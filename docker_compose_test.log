2025-05-11 14:22:54,277 - docker_compose_test - INFO - Starting Docker Compose test...
2025-05-11 14:22:54,277 - docker_compose_test - INFO - Starting services using Docker Compose...
2025-05-11 14:22:55,152 - docker_compose_test - INFO - Services started successfully
2025-05-11 14:22:55,152 - docker_compose_test - INFO - Waiting for services to be healthy...
2025-05-11 14:22:55,152 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:22:55,667 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=starting
2025-05-11 14:22:55,667 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=running, Health=starting
2025-05-11 14:22:55,667 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=running, Health=
2025-05-11 14:22:55,667 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=running, Health=starting
2025-05-11 14:22:55,667 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:22:55,668 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 14:22:55,668 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:22:55,668 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 14:22:55,668 - docker_compose_test - INFO - Checking services health...
2025-05-11 14:22:55,683 - docker_compose_test - ERROR - Error checking Orchestrator service health: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x106b828d0>: Failed to establish a new connection: [Errno 61] Connection refused'))
2025-05-11 14:22:55,685 - docker_compose_test - ERROR - Error checking Repository Mapper service health: HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x106bb19d0>: Failed to establish a new connection: [Errno 61] Connection refused'))
2025-05-11 14:22:55,688 - docker_compose_test - ERROR - Error checking Domain Analyzer service health: HTTPConnectionPool(host='localhost', port=8002): Max retries exceeded with url: /health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x106bb3d50>: Failed to establish a new connection: [Errno 61] Connection refused'))
2025-05-11 14:22:55,690 - docker_compose_test - ERROR - Error checking File-Domain Mapper service health: HTTPConnectionPool(host='localhost', port=8003): Max retries exceeded with url: /health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x106bc1f50>: Failed to establish a new connection: [Errno 61] Connection refused'))
2025-05-11 14:22:55,693 - docker_compose_test - ERROR - Error checking Domain-File Repomap service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:22:55,697 - docker_compose_test - ERROR - Error checking Diagram Generator service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:22:55,701 - docker_compose_test - INFO - Prometheus service is healthy
2025-05-11 14:22:55,704 - docker_compose_test - ERROR - Error checking Grafana service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:22:55,704 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 1s)
2025-05-11 14:23:00,709 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:23:00,916 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=
2025-05-11 14:23:00,917 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=starting
2025-05-11 14:23:00,917 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=running, Health=
2025-05-11 14:23:00,917 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=starting
2025-05-11 14:23:00,917 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:23:00,917 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=
2025-05-11 14:23:00,917 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:23:00,917 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=
2025-05-11 14:23:00,917 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 6s)
2025-05-11 14:23:05,922 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:23:06,022 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:23:06,022 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:23:06,022 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:23:06,022 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:23:06,022 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:23:06,022 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:23:06,022 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:23:06,022 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:23:06,022 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 11s)
2025-05-11 14:23:11,028 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:23:11,206 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=
2025-05-11 14:23:11,207 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:23:11,207 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:23:11,207 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:23:11,207 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:23:11,207 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=
2025-05-11 14:23:11,207 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:23:11,207 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=
2025-05-11 14:23:11,207 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 16s)
2025-05-11 14:23:16,212 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:23:16,313 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:23:16,313 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:23:16,313 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:23:16,313 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:23:16,313 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:23:16,313 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:23:16,313 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:23:16,313 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:23:16,313 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 21s)
2025-05-11 14:23:21,318 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:23:21,414 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:23:21,414 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:23:21,414 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:23:21,414 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:23:21,414 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:23:21,414 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:23:21,414 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:23:21,414 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:23:21,414 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 26s)
2025-05-11 14:23:26,419 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:23:26,520 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:23:26,520 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:23:26,520 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:23:26,520 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:23:26,520 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:23:26,520 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:23:26,520 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:23:26,520 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:23:26,520 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 31s)
2025-05-11 14:23:31,525 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:23:31,624 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:23:31,625 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:23:31,625 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:23:31,625 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:23:31,625 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:23:31,625 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:23:31,625 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:23:31,625 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:23:31,625 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 36s)
2025-05-11 14:23:36,625 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:23:36,706 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:23:36,706 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:23:36,706 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:23:36,706 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:23:36,706 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:23:36,706 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:23:36,706 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:23:36,706 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:23:36,706 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 42s)
2025-05-11 14:23:41,712 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:23:41,797 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:23:41,797 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:23:41,797 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:23:41,797 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:23:41,797 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:23:41,797 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:23:41,797 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:23:41,797 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:23:41,797 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 47s)
2025-05-11 14:23:46,800 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:23:46,894 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:23:46,894 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:23:46,894 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:23:46,894 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:23:46,894 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:23:46,894 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:23:46,894 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:23:46,894 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:23:46,894 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 52s)
2025-05-11 14:23:51,900 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:23:51,979 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:23:51,979 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:23:51,979 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:23:51,979 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:23:51,979 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:23:51,979 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:23:51,979 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:23:51,979 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:23:51,979 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 57s)
2025-05-11 14:23:56,980 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:23:57,061 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:23:57,061 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:23:57,061 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:23:57,061 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:23:57,061 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:23:57,061 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:23:57,061 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:23:57,061 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:23:57,061 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 62s)
2025-05-11 14:24:02,066 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:24:02,147 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:24:02,147 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:24:02,147 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:24:02,147 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:24:02,147 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:24:02,147 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:24:02,147 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:24:02,147 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:24:02,147 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 67s)
2025-05-11 14:24:07,150 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:24:07,231 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:24:07,231 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:24:07,231 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:24:07,231 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:24:07,231 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:24:07,231 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:24:07,231 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:24:07,231 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:24:07,231 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 72s)
2025-05-11 14:24:08,177 - docker_compose_test - INFO - Stopping services using Docker Compose...
2025-05-11 14:24:08,852 - docker_compose_test - INFO - Services stopped successfully
