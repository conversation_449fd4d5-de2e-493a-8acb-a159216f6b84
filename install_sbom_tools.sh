#!/bin/bash
# Script to install SBOM generation tools

set -e

# ANSI colors for terminal output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
BOLD='\033[1m'
NC='\033[0m' # No Color

echo -e "${BLUE}${BOLD}=== Installing SBOM Generation Tools ===${NC}"

# Check if pip is installed
if ! command -v pip &> /dev/null; then
    echo -e "${RED}Error: pip is not installed. Please install Python and pip first.${NC}"
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo -e "${RED}Error: npm is not installed. Please install Node.js and npm first.${NC}"
    exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo -e "${YELLOW}Warning: Docker is not installed. Docker image analysis will not be available.${NC}"
    echo -e "${YELLOW}Please install Docker if you want to analyze container images.${NC}"
fi

# Install CycloneDX Python module
echo -e "${BLUE}Installing CycloneDX Python module...${NC}"
pip install cyclonedx-bom

# Install CycloneDX npm module
echo -e "${BLUE}Installing CycloneDX npm module...${NC}"
npm install -g @cyclonedx/cyclonedx-npm

# Install Syft
echo -e "${BLUE}Installing Syft...${NC}"
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    brew tap anchore/syft
    brew install syft
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    curl -sSfL https://raw.githubusercontent.com/anchore/syft/main/install.sh | sh -s -- -b /usr/local/bin
else
    echo -e "${YELLOW}Unsupported OS. Please install Syft manually: https://github.com/anchore/syft#installation${NC}"
fi

# Install license identification tools
echo -e "${BLUE}Installing license identification tools...${NC}"

# Install pip-licenses for Python license identification
echo -e "${BLUE}Installing pip-licenses...${NC}"
pip install pip-licenses

# Install license-checker for JavaScript license identification
echo -e "${BLUE}Installing license-checker...${NC}"
# Try to install globally first
npm install -g license-checker || {
    echo -e "${YELLOW}Global installation failed. Installing license-checker locally...${NC}"
    # If global installation fails, install locally
    npm install license-checker --no-save || {
        echo -e "${YELLOW}Local installation also failed. Will try to use npx when needed.${NC}"
        echo -e "${YELLOW}This is not critical as the script has fallback mechanisms.${NC}"
    }
}

# Install requests for API calls
echo -e "${BLUE}Installing Python requests library...${NC}"
pip install requests

# Check if Ruby is installed for license_finder
if command -v gem &> /dev/null; then
    echo -e "${BLUE}Installing license_finder...${NC}"
    gem install license_finder || echo -e "${YELLOW}Warning: Failed to install license_finder. This is optional and not critical.${NC}"
else
    echo -e "${YELLOW}Warning: Ruby is not installed. license_finder will not be available.${NC}"
    echo -e "${YELLOW}This is optional and not critical for basic license identification.${NC}"
fi

echo -e "${GREEN}${BOLD}All SBOM tools have been installed successfully!${NC}"
echo -e "${GREEN}You can now run ./generate_sbom.py to create your SBOM${NC}"
echo -e "${GREEN}After generating the SBOM, run ./update_licenses.py to enhance license information${NC}"
