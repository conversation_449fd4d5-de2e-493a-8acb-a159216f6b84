#!/usr/bin/env python3
"""
Update license information in a CycloneDX SBOM.

This script enhances license information in an existing SBOM by:
1. Using external license data sources (pip-licenses, license-checker)
2. Querying package repositories (PyPI, npm) for license information
3. Using a database of common packages and their known licenses
4. Applying heuristics to identify licenses from package metadata

Usage:
    python update_licenses.py --sbom sbom_output/combined-sbom.json --output sbom_output/enhanced-sbom.json
"""

import argparse
import json
import os
import sys
import requests
import re
import subprocess
from typing import Dict, List, Any, Optional
import datetime

# ANSI colors for terminal output
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'

def print_header(message: str) -> None:
    """Print a formatted header message."""
    print(f"\n{Colors.HEADER}{Colors.BOLD}=== {message} ==={Colors.ENDC}\n")

def print_step(message: str) -> None:
    """Print a formatted step message."""
    print(f"{Colors.BLUE}→ {message}{Colors.ENDC}")

def print_success(message: str) -> None:
    """Print a formatted success message."""
    print(f"{Colors.GREEN}✓ {message}{Colors.ENDC}")

def print_warning(message: str) -> None:
    """Print a formatted warning message."""
    print(f"{Colors.YELLOW}⚠ {message}{Colors.ENDC}")

def print_error(message: str) -> None:
    """Print a formatted error message."""
    print(f"{Colors.RED}✗ {message}{Colors.ENDC}")

# Database of common packages and their licenses
COMMON_LICENSES = {
    # Python packages
    "numpy": "BSD-3-Clause",
    "pandas": "BSD-3-Clause",
    "matplotlib": "BSD-3-Clause",
    "scipy": "BSD-3-Clause",
    "requests": "Apache-2.0",
    "flask": "BSD-3-Clause",
    "django": "BSD-3-Clause",
    "fastapi": "MIT",
    "sqlalchemy": "MIT",
    "pytest": "MIT",
    "black": "MIT",
    "isort": "MIT",
    "mypy": "MIT",
    "flake8": "MIT",
    "pylint": "GPL-2.0",
    "pydantic": "MIT",
    "uvicorn": "BSD-3-Clause",
    "starlette": "BSD-3-Clause",
    "aiohttp": "Apache-2.0",
    "asyncio": "Apache-2.0",
    "tornado": "Apache-2.0",
    "jinja2": "BSD-3-Clause",
    "pillow": "HPND",
    "cryptography": "Apache-2.0 OR BSD-3-Clause",
    "pytz": "MIT",
    "six": "MIT",
    "click": "BSD-3-Clause",
    "pyyaml": "MIT",
    "tqdm": "MIT",
    "boto3": "Apache-2.0",
    "botocore": "Apache-2.0",
    "psycopg2": "LGPL-3.0-or-later",
    "redis": "MIT",
    "celery": "BSD-3-Clause",
    "gunicorn": "MIT",
    "werkzeug": "BSD-3-Clause",
    "setuptools": "MIT",
    "wheel": "MIT",
    "pip": "MIT",
    "twine": "Apache-2.0",
    "virtualenv": "MIT",
    "coverage": "Apache-2.0",
    "sphinx": "BSD-2-Clause",
    "jupyter": "BSD-3-Clause",
    "ipython": "BSD-3-Clause",
    "notebook": "BSD-3-Clause",
    "scikit-learn": "BSD-3-Clause",
    "tensorflow": "Apache-2.0",
    "pytorch": "BSD-3-Clause",
    "keras": "Apache-2.0",
    "seaborn": "BSD-3-Clause",
    "plotly": "MIT",
    "dash": "MIT",
    "streamlit": "Apache-2.0",
    "beautifulsoup4": "MIT",
    "lxml": "BSD-3-Clause",
    "scrapy": "BSD-3-Clause",
    "selenium": "Apache-2.0",
    "httpx": "BSD-3-Clause",
    "grpcio": "Apache-2.0",
    "protobuf": "BSD-3-Clause",
    "sqlmodel": "MIT",
    "alembic": "MIT",
    "marshmallow": "MIT",
    "typer": "MIT",
    "rich": "MIT",
    "pytest-cov": "MIT",
    "pytest-mock": "MIT",
    "pytest-asyncio": "Apache-2.0",
    "pytest-xdist": "MIT",
    "pytest-django": "BSD-3-Clause",
    "pytest-flask": "MIT",
    "pytest-benchmark": "BSD-2-Clause",
    "pytest-timeout": "MIT",
    "pytest-sugar": "BSD-3-Clause",
    "pytest-html": "MPL-2.0",
    "pytest-selenium": "MIT",
    "pytest-randomly": "MIT",
    "pytest-repeat": "MIT",
    "pytest-rerunfailures": "MIT",
    "pytest-cov": "MIT",
    "pytest-mock": "MIT",
    "pytest-asyncio": "Apache-2.0",
    "pytest-xdist": "MIT",
    "pytest-django": "BSD-3-Clause",
    "pytest-flask": "MIT",
    "pytest-benchmark": "BSD-2-Clause",
    "pytest-timeout": "MIT",
    "pytest-sugar": "BSD-3-Clause",
    "pytest-html": "MPL-2.0",
    "pytest-selenium": "MIT",
    "pytest-randomly": "MIT",
    "pytest-repeat": "MIT",
    "pytest-rerunfailures": "MIT",

    # JavaScript packages
    "react": "MIT",
    "react-dom": "MIT",
    "react-router": "MIT",
    "react-router-dom": "MIT",
    "redux": "MIT",
    "react-redux": "MIT",
    "redux-thunk": "MIT",
    "redux-saga": "MIT",
    "axios": "MIT",
    "lodash": "MIT",
    "moment": "MIT",
    "date-fns": "MIT",
    "formik": "MIT",
    "yup": "MIT",
    "styled-components": "MIT",
    "emotion": "MIT",
    "material-ui": "MIT",
    "antd": "MIT",
    "bootstrap": "MIT",
    "tailwindcss": "MIT",
    "webpack": "MIT",
    "babel": "MIT",
    "eslint": "MIT",
    "prettier": "MIT",
    "typescript": "Apache-2.0",
    "jest": "MIT",
    "enzyme": "MIT",
    "testing-library": "MIT",
    "cypress": "MIT",
    "storybook": "MIT",
    "next": "MIT",
    "gatsby": "MIT",
    "express": "MIT",
    "koa": "MIT",
    "fastify": "MIT",
    "nest": "MIT",
    "mongoose": "MIT",
    "sequelize": "MIT",
    "typeorm": "MIT",
    "prisma": "Apache-2.0",
    "graphql": "MIT",
    "apollo-server": "MIT",
    "apollo-client": "MIT",
    "socket.io": "MIT",
    "d3": "BSD-3-Clause",
    "chart.js": "MIT",
    "three.js": "MIT",
    "leaflet": "BSD-2-Clause",
    "mapbox-gl": "BSD-3-Clause"
}

def run_pip_licenses() -> Dict[str, str]:
    """Run pip-licenses to get license information for Python packages."""
    print_step("Running pip-licenses to get Python package license information...")

    try:
        # Check if pip-licenses is installed
        subprocess.run(["pip", "install", "pip-licenses"], check=True, capture_output=True)

        # Run pip-licenses
        result = subprocess.run(
            ["pip-licenses", "--format=json", "--with-urls"],
            check=True,
            text=True,
            capture_output=True
        )

        # Parse the output
        licenses = {}
        data = json.loads(result.stdout)
        for package in data:
            name = package.get("Name", "").lower()
            license_name = package.get("License", "")
            if name and license_name and license_name != "UNKNOWN":
                licenses[name] = license_name

        print_success(f"Found license information for {len(licenses)} Python packages")
        return licenses
    except Exception as e:
        print_warning(f"Failed to run pip-licenses: {str(e)}")
        return {}

def run_license_checker() -> Dict[str, str]:
    """Run license-checker to get license information for JavaScript packages."""
    print_step("Running license-checker to get JavaScript package license information...")

    try:
        # Try multiple ways to run license-checker
        license_checker_commands = [
            ["license-checker", "--json"],                  # Global installation
            ["npx", "license-checker", "--json"],           # Using npx
            ["./node_modules/.bin/license-checker", "--json"]  # Local installation
        ]

        # First, try to install license-checker locally if it's not already installed
        try:
            print_step("Installing license-checker locally...")
            subprocess.run(
                ["npm", "install", "license-checker", "--no-save"],
                check=True,
                capture_output=True,
                timeout=60
            )
            print_success("license-checker installed locally")
        except Exception as install_error:
            print_warning(f"Could not install license-checker locally: {str(install_error)}")
            print_step("Will try to use existing installation if available")

        # Try each command until one works
        result = None
        for cmd in license_checker_commands:
            try:
                print_step(f"Trying to run {' '.join(cmd)}...")
                result = subprocess.run(
                    cmd,
                    check=True,
                    text=True,
                    capture_output=True,
                    timeout=60
                )
                if result.returncode == 0:
                    print_success(f"Successfully ran {' '.join(cmd)}")
                    break
            except Exception as cmd_error:
                print_warning(f"Command {' '.join(cmd)} failed: {str(cmd_error)}")
                continue

        if not result or result.returncode != 0:
            print_warning("All license-checker commands failed. Using fallback license database.")
            return {}

        # Parse the output
        licenses = {}
        data = json.loads(result.stdout)
        for package_key, package_info in data.items():
            # Extract package name from the key (format: name@version)
            name = package_key.split("@")[0].lower()
            license_name = package_info.get("licenses", "")
            if name and license_name and license_name != "UNKNOWN":
                licenses[name] = license_name

        print_success(f"Found license information for {len(licenses)} JavaScript packages")
        return licenses
    except Exception as e:
        print_warning(f"Failed to run license-checker: {str(e)}")

        # Fallback: Try to find package.json files and extract license info
        print_step("Attempting to extract license information from package.json files...")
        licenses = {}
        try:
            # Find all package.json files
            package_json_files = []
            for root, dirs, files in os.walk('.'):
                if 'node_modules' in root:
                    continue  # Skip node_modules directories
                if 'package.json' in files:
                    package_json_files.append(os.path.join(root, 'package.json'))

            # Extract license information from each package.json
            for package_file in package_json_files:
                try:
                    with open(package_file, 'r') as f:
                        package_data = json.load(f)
                        name = package_data.get('name', '').lower()
                        license_info = package_data.get('license', '')
                        if name and license_info and license_info != "UNKNOWN":
                            licenses[name] = license_info
                except Exception:
                    continue

            if licenses:
                print_success(f"Found license information for {len(licenses)} JavaScript packages from package.json files")
            else:
                print_warning("Could not extract license information from package.json files")
        except Exception as fallback_error:
            print_warning(f"Fallback license extraction failed: {str(fallback_error)}")

        return licenses

def query_pypi(package_name: str) -> Optional[str]:
    """Query PyPI for license information."""
    try:
        response = requests.get(f"https://pypi.org/pypi/{package_name}/json", timeout=5)
        if response.status_code == 200:
            data = response.json()
            license_info = data.get("info", {}).get("license", "")
            classifiers = data.get("info", {}).get("classifiers", [])

            # If license is specified directly
            if license_info and license_info not in ["UNKNOWN", ""]:
                return license_info

            # Look for license in classifiers
            for classifier in classifiers:
                if classifier.startswith("License ::"):
                    # Extract license from classifier
                    license_match = re.search(r"License :: .*:: (.*)", classifier)
                    if license_match:
                        return license_match.group(1)

        return None
    except Exception:
        return None

def query_npm(package_name: str) -> Optional[str]:
    """Query npm registry for license information."""
    try:
        response = requests.get(f"https://registry.npmjs.org/{package_name}", timeout=5)
        if response.status_code == 200:
            data = response.json()

            # Get the latest version
            latest_version = data.get("dist-tags", {}).get("latest")
            if latest_version and latest_version in data.get("versions", {}):
                license_info = data["versions"][latest_version].get("license")
                if license_info and not isinstance(license_info, dict):
                    return license_info

            # Try to get license from the root object
            license_info = data.get("license")
            if license_info and not isinstance(license_info, dict):
                return license_info

        return None
    except Exception:
        return None

def update_sbom_licenses(sbom_file: str, output_file: str) -> None:
    """Update license information in the SBOM."""
    print_header("Updating License Information in SBOM")

    try:
        # Load SBOM
        with open(sbom_file, 'r') as f:
            sbom_data = json.load(f)

        # Get license information from various sources
        pip_licenses = run_pip_licenses()
        npm_licenses = run_license_checker()

        # Track statistics
        total_components = len(sbom_data.get("components", []))
        updated_components = 0
        still_unknown = 0

        # Update license information for each component
        for component in sbom_data.get("components", []):
            name = component.get("name", "").lower()

            # Skip if already has license information
            has_license = False
            if "licenses" in component:
                for license_data in component["licenses"]:
                    if "license" in license_data and "id" in license_data["license"]:
                        license_id = license_data["license"]["id"]
                        if license_id and license_id != "Unknown":
                            has_license = True
                            break

            if has_license:
                continue

            # Try to find license information
            license_id = None

            # 1. Check in our database of common packages
            if name in COMMON_LICENSES:
                license_id = COMMON_LICENSES[name]

            # 2. Check in pip-licenses results
            elif name in pip_licenses:
                license_id = pip_licenses[name]

            # 3. Check in license-checker results
            elif name in npm_licenses:
                license_id = npm_licenses[name]

            # 4. Query package repositories
            elif "purl" in component:
                purl = component["purl"]
                if purl.startswith("pkg:pypi/"):
                    package_name = purl.split("/")[1].split("@")[0]
                    license_id = query_pypi(package_name)
                elif purl.startswith("pkg:npm/"):
                    package_name = purl.split("/")[1].split("@")[0]
                    license_id = query_npm(package_name)

            # Update component with license information
            if license_id:
                # Remove existing unknown licenses
                if "licenses" in component:
                    component["licenses"] = [
                        lic for lic in component["licenses"]
                        if not (
                            "license" in lic and
                            "id" in lic["license"] and
                            lic["license"]["id"] == "Unknown"
                        )
                    ]
                else:
                    component["licenses"] = []

                # Add the new license
                component["licenses"].append({
                    "license": {
                        "id": license_id
                    }
                })

                updated_components += 1
            else:
                still_unknown += 1

        # Write updated SBOM
        with open(output_file, 'w') as f:
            json.dump(sbom_data, f, indent=2)

        print_success(f"Updated license information for {updated_components} components")
        print_warning(f"{still_unknown} components still have unknown licenses")
        print_success(f"Updated SBOM written to {output_file}")

        # Generate updated license report
        license_report_file = os.path.join(os.path.dirname(output_file), "enhanced-license-report.md")
        generate_license_report(sbom_data, license_report_file)

    except Exception as e:
        print_error(f"Failed to update SBOM licenses: {str(e)}")

def generate_license_report(sbom_data: Dict[str, Any], output_file: str) -> None:
    """Generate a license report from the updated SBOM."""
    print_step("Generating enhanced license report...")

    try:
        # Extract license information
        license_counts = {}
        license_components = {}
        total_components = len(sbom_data.get("components", []))
        unknown_count = 0

        for component in sbom_data.get("components", []):
            name = component.get("name", "Unknown")
            version = component.get("version", "Unknown")
            component_id = f"{name}@{version}"

            has_license = False
            if "licenses" in component:
                for license_data in component["licenses"]:
                    if "license" in license_data and "id" in license_data["license"]:
                        license_id = license_data["license"]["id"]
                        if license_id and license_id != "Unknown":
                            has_license = True

                            if license_id not in license_counts:
                                license_counts[license_id] = 0
                                license_components[license_id] = []

                            license_counts[license_id] += 1
                            license_components[license_id].append(component_id)

            if not has_license:
                unknown_count += 1

        # Generate report
        with open(output_file, 'w') as f:
            f.write("# Enhanced License Report for Bracket Codebase\n\n")
            f.write(f"**Generated:** {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            f.write("## License Summary\n\n")
            f.write("| License | Count | Percentage |\n")
            f.write("|---------|-------|------------|\n")

            for license_id, count in sorted(license_counts.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / total_components) * 100
                f.write(f"| {license_id} | {count} | {percentage:.1f}% |\n")

            if unknown_count > 0:
                percentage = (unknown_count / total_components) * 100
                f.write(f"| Unknown | {unknown_count} | {percentage:.1f}% |\n")

            f.write(f"\n**Total Components:** {total_components}\n\n")

            # Write license details by category
            license_categories = {
                "Permissive": ["MIT", "BSD", "Apache", "ISC", "0BSD", "Unlicense", "CC0"],
                "Copyleft": ["GPL", "LGPL", "AGPL", "MPL", "EPL", "CDDL"],
                "Commercial": ["Proprietary", "Commercial"],
                "Unknown": ["Unknown"]
            }

            for category, patterns in license_categories.items():
                f.write(f"## {category} Licenses\n\n")

                category_found = False
                for license_id, components in license_components.items():
                    if any(pattern in license_id for pattern in patterns):
                        category_found = True
                        f.write(f"### {license_id} ({len(components)} components)\n\n")

                        # List first 10 components
                        for i, component in enumerate(sorted(components)):
                            if i < 10:
                                f.write(f"- {component}\n")
                            elif i == 10:
                                f.write(f"- ... and {len(components) - 10} more\n")

                        f.write("\n")

                if not category_found and category != "Unknown":
                    f.write(f"No {category.lower()} licenses found.\n\n")

                if category == "Unknown" and unknown_count > 0:
                    f.write(f"### Unknown ({unknown_count} components)\n\n")
                    f.write("These components still have unknown license information. Further investigation is needed.\n\n")

        print_success(f"Enhanced license report generated at {output_file}")

    except Exception as e:
        print_error(f"Failed to generate license report: {str(e)}")

def main():
    parser = argparse.ArgumentParser(description="Update license information in a CycloneDX SBOM")
    parser.add_argument("--sbom", required=True, help="Path to the CycloneDX SBOM JSON file")
    parser.add_argument("--output", help="Path to the output SBOM file (default: enhanced-sbom.json in the same directory)")
    args = parser.parse_args()

    # Set default output file if not specified
    if not args.output:
        output_dir = os.path.dirname(args.sbom)
        args.output = os.path.join(output_dir, "enhanced-sbom.json")

    # Update SBOM licenses
    update_sbom_licenses(args.sbom, args.output)

if __name__ == "__main__":
    main()
