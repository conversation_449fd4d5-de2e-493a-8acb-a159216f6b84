#!/bin/bash
# Apply path handling fixes to the microservices

set -e  # Exit on error

# Get the current directory
CURRENT_DIR=$(pwd)

# Go to the microservices directory
cd ../../

echo "Applying path handling fixes to microservices..."

# Create backup of original files
echo "Creating backups of original files..."
cp bracket_irl_common/bracket_irl_common/storage.py bracket_irl_common/bracket_irl_common/storage.py.orig
cp domain-analyzer-service/src/services/analyzer.py domain-analyzer-service/src/services/analyzer.py.orig
cp orchestrator-service/src/services/orchestrator.py orchestrator-service/src/services/orchestrator.py.orig

# Apply the storage client fix
echo "Applying storage client fix..."
patch -p1 < tests/e2e/storage_client_path_fix.patch

# Apply the domain analyzer fix
echo "Applying domain analyzer fix..."
patch -p1 < tests/e2e/domain_analyzer_path_fix.patch

# Apply the orchestrator fix
echo "Applying orchestrator fix..."
patch -p1 < tests/e2e/orchestrator_path_fix.patch

echo "All fixes applied successfully!"
echo "Rebuilding and restarting services..."

# Rebuild and restart the services
docker compose down
docker compose build
docker compose up -d

echo "Services restarted. Wait for them to become healthy before running the E2E test."
echo "To run the orchestrator E2E test, use: cd tests/e2e && ./run_orchestrator_e2e_test.sh"

# Return to the original directory
cd $CURRENT_DIR
