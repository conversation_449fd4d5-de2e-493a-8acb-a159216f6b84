"""
PR Broadcast Service

This module provides the main service class that orchestrates the PR/MR diff analysis,
domain impact mapping, and diagram generation.
"""

import os
import json
import logging
import asyncio
from typing import Dict, List, Any, Optional, Tuple, Set, Union

from pr_broadcast.diff.gitlab import GitlabDiffFetcher
from pr_broadcast.diff.analyzer import EnhancedFunctionDiffA<PERSON>yzer
from pr_broadcast.diff.models import FunctionChange
from pr_broadcast.domain.mapper import DomainMapper
from pr_broadcast.domain.taxonomy import DomainTaxonomy
from pr_broadcast.impact.analyzer import ImpactAnalyzer
from pr_broadcast.impact.propagator import ImpactPropagator
from pr_broadcast.diagram.generator import DiagramGenerator
from pr_broadcast.diagram.llm import LLMDiagramGenerator
from pr_broadcast.output.manager import OutputManager

logger = logging.getLogger(__name__)


class PRBroadcastService:
    """
    Main service for analyzing PR/MR diffs, mapping changes to domains, and generating
    specialized Mermaid diagrams that visualize the impact of changes.

    This class orchestrates the entire PR Broadcast workflow, from fetching diffs
    to generating impact diagrams and reports.
    """

    def __init__(
        self,
        gitlab_url: str,
        gitlab_token: str,
        taxonomy_json_path: str,
        domain_traces_yaml_path: str,
        output_dir: str,
        llm_api_key: Optional[str] = None,
        llm_model: str = "gpt-4"
    ):
        """
        Initialize the PR Broadcast service.

        Args:
            gitlab_url: URL of the GitLab instance
            gitlab_token: GitLab API token
            taxonomy_json_path: Path to the domain taxonomy JSON file
            domain_traces_yaml_path: Path to the domain traces YAML file
            output_dir: Directory to save output artifacts
            llm_api_key: API key for the LLM service (OpenAI or OpenRouter)
            llm_model: LLM model to use
        """
        self.gitlab_url = gitlab_url
        self.gitlab_token = gitlab_token
        self.taxonomy_json_path = taxonomy_json_path
        self.domain_traces_yaml_path = domain_traces_yaml_path
        self.output_dir = output_dir
        self.llm_api_key = llm_api_key
        self.llm_model = llm_model

        # Initialize components
        self.diff_fetcher = GitlabDiffFetcher(gitlab_url, gitlab_token)
        self.diff_analyzer = EnhancedFunctionDiffAnalyzer()
        self.domain_mapper = DomainMapper(taxonomy_json_path, domain_traces_yaml_path)
        self.impact_analyzer = ImpactAnalyzer(taxonomy_json_path, domain_traces_yaml_path)
        self.impact_propagator = ImpactPropagator(taxonomy_json_path)
        self.diagram_generator = DiagramGenerator(output_dir)
        self.llm_diagram_generator = LLMDiagramGenerator(taxonomy_json_path, llm_api_key, llm_model)
        self.output_manager = OutputManager(output_dir)
        self.domain_taxonomy = DomainTaxonomy(taxonomy_json_path)

        logger.info("Initialized PR Broadcast service")

    async def analyze_merge_request(
        self,
        project_id: str,
        mr_iid: int,
        include_indirect_impact: bool = True,
        use_llm_diagrams: bool = True
    ) -> Dict[str, Any]:
        """
        Analyze a GitLab merge request and generate impact diagrams.

        Args:
            project_id: GitLab project ID
            mr_iid: Merge request IID (internal ID)
            include_indirect_impact: Whether to include indirect impact analysis
            use_llm_diagrams: Whether to use LLM to generate diagrams

        Returns:
            Dictionary containing analysis results and paths to generated diagrams
        """
        logger.info(f"Analyzing merge request {mr_iid} in project {project_id}")

        # Step 1: Fetch MR details and diffs
        mr_details, diffs = await self.diff_fetcher.fetch_merge_request(project_id, mr_iid)
        logger.info(f"Fetched {len(diffs)} diffs for merge request {mr_iid}")

        # Step 2: Analyze diffs to identify changed functions
        function_changes = []
        for diff in diffs:
            changes = self.diff_analyzer.analyze_diff(diff)
            function_changes.extend(changes)

        logger.info(f"Identified {len(function_changes)} function changes")

        # Step 3: Analyze the impact of changes on domains
        domain_impact = self.impact_analyzer.analyze_impact(function_changes)
        logger.info(f"Analyzed impact for {len(domain_impact)} domains")

        # Step 4: Propagate impact through the domain hierarchy
        if include_indirect_impact:
            domain_impact = self.impact_propagator.propagate_impact(domain_impact)
            logger.info(f"Propagated impact to {len(domain_impact)} domains")

        # Step 5: Generate impact diagrams
        if use_llm_diagrams:
            # Generate LLM-based diagrams
            diagrams = await self._generate_llm_diagrams(domain_impact, mr_iid)
        else:
            # Generate standard diagrams
            diagrams = self.diagram_generator.generate_diagrams(
                mr_details,
                function_changes,
                domain_impact,
                self.taxonomy_json_path
            )

        logger.info(f"Generated {len(diagrams)} diagrams")

        # Step 6: Save artifacts and generate summary
        artifacts = self.output_manager.save_diagrams(diagrams, mr_iid)

        # Generate summary
        summary = self._generate_summary(mr_details, function_changes, domain_impact)
        summary_path = self.output_manager.save_summary(summary, mr_iid)

        logger.info(f"Saved summary to {summary_path}")

        # Return results
        return {
            "mr_details": mr_details,
            "function_changes": [fc.to_dict() for fc in function_changes],
            "domain_impact": domain_impact,
            "diagrams": diagrams,
            "artifacts": artifacts,
            "summary_path": summary_path
        }

    async def analyze_local_diffs(
        self,
        diffs: Union[List[Dict[str, Any]], Dict[str, Any]],
        mr_details: Dict[str, Any],
        include_indirect_impact: bool = True,
        use_llm_diagrams: bool = True
    ) -> Dict[str, Any]:
        """
        Analyze local diffs and generate impact diagrams.

        Args:
            diffs: List of dictionaries containing diff information or a dictionary with a 'diffs' key
            mr_details: Dictionary containing merge request details
            include_indirect_impact: Whether to include indirect impact analysis
            use_llm_diagrams: Whether to use LLM to generate diagrams

        Returns:
            Dictionary containing analysis results and paths to generated diagrams
        """
        # Handle case where diffs is a dictionary with a 'diffs' key
        if isinstance(diffs, dict) and 'diffs' in diffs:
            diffs_list = diffs['diffs']
            # If mr_details is empty and the dict has mr_details, use that
            if 'mr_details' in diffs and not mr_details:
                mr_details = diffs['mr_details']
        else:
            diffs_list = diffs

        logger.info(f"Analyzing {len(diffs_list)} local diffs")

        # Step 1: Analyze diffs to identify changed functions
        function_changes = []
        for diff in diffs_list:
            changes = self.diff_analyzer.analyze_diff(diff)
            function_changes.extend(changes)

        logger.info(f"Identified {len(function_changes)} function changes")

        # Step 2: Analyze the impact of changes on domains
        domain_impact = self.impact_analyzer.analyze_impact(function_changes)
        logger.info(f"Analyzed impact for {len(domain_impact)} domains")

        # Step 3: Propagate impact through the domain hierarchy
        if include_indirect_impact:
            domain_impact = self.impact_propagator.propagate_impact(domain_impact)
            logger.info(f"Propagated impact to {len(domain_impact)} domains")

        # Step 4: Generate impact diagrams
        if use_llm_diagrams:
            # Generate LLM-based diagrams
            diagrams = await self._generate_llm_diagrams(domain_impact, mr_details["iid"])
        else:
            # Generate standard diagrams
            diagrams = self.diagram_generator.generate_diagrams(
                mr_details,
                function_changes,
                domain_impact,
                self.taxonomy_json_path
            )

        logger.info(f"Generated {len(diagrams)} diagrams")

        # Step 5: Save artifacts and generate summary
        artifacts = self.output_manager.save_diagrams(diagrams, mr_details["iid"])

        # Generate summary
        summary = self._generate_summary(mr_details, function_changes, domain_impact)
        summary_path = self.output_manager.save_summary(summary, mr_details["iid"])

        logger.info(f"Saved summary to {summary_path}")

        # Return results
        return {
            "mr_details": mr_details,
            "function_changes": [fc.to_dict() for fc in function_changes],
            "domain_impact": domain_impact,
            "diagrams": diagrams,
            "artifacts": artifacts,
            "summary_path": summary_path
        }

    async def _generate_llm_diagrams(
        self,
        domain_impact: Dict[str, Dict[str, Any]],
        mr_iid: int
    ) -> Dict[str, Dict[str, Any]]:
        """
        Generate LLM-based diagrams for impacted domains.

        Args:
            domain_impact: Dictionary mapping domains to impact information
            mr_iid: Merge request IID (internal ID)

        Returns:
            Dictionary mapping domains to diagram information
        """
        # Initialize diagrams dictionary
        diagrams = {}

        # Generate diagrams for leaf domains
        leaf_diagrams = {}

        for domain, impact in domain_impact.items():
            if impact["direct_impact"] and self.domain_taxonomy.is_leaf_domain(domain):
                logger.info(f"Generating diagram for leaf domain: {domain}")

                # Get the domain node
                domain_node = self.domain_taxonomy.get_domain_node(domain)

                if domain_node and "diagram" in domain_node:
                    original_diagram = domain_node["diagram"]

                    # Generate a new diagram showing the impact of changes
                    impact_diagram = await self.llm_diagram_generator.generate_leaf_domain_diagram(
                        domain,
                        original_diagram,
                        impact["changes"]
                    )

                    leaf_diagrams[domain] = {
                        "original_diagram": original_diagram,
                        "impact_diagram": impact_diagram,
                        "changes": impact
                    }

                    logger.info(f"Generated impact diagram for leaf domain: {domain}")

        # Add leaf diagrams to the diagrams dictionary
        diagrams.update(leaf_diagrams)

        # Generate diagrams for parent domains
        parent_diagrams = {}

        # Get all domains with changes
        affected_domains = set(domain_impact.keys())

        # Process each domain with changes
        for domain in affected_domains:
            # Skip if not a parent domain
            if self.domain_taxonomy.is_leaf_domain(domain):
                continue

            # Skip if already processed
            if domain in parent_diagrams:
                continue

            # Get the domain's diagram
            domain_node = self.domain_taxonomy.get_domain_node(domain)

            if domain_node and "diagram" in domain_node:
                parent_diagram = domain_node["diagram"]

                # Get all child domains with changes
                child_domains = [d for d in affected_domains if self.domain_taxonomy.is_child_of(d, domain)]

                # Collect all child diagrams
                child_diagrams = {}
                for child in child_domains:
                    if child in leaf_diagrams:
                        child_diagrams[child] = leaf_diagrams[child]

                if child_diagrams:
                    logger.info(f"Generating diagram for parent domain: {domain}")

                    # Generate a new diagram showing the impact of changes
                    impact_diagram = await self.llm_diagram_generator.generate_parent_domain_diagram(
                        domain,
                        parent_diagram,
                        child_diagrams
                    )

                    parent_diagrams[domain] = {
                        "original_diagram": parent_diagram,
                        "impact_diagram": impact_diagram,
                        "child_domains": child_domains
                    }

                    logger.info(f"Generated impact diagram for parent domain: {domain}")

        # Add parent diagrams to the diagrams dictionary
        diagrams.update(parent_diagrams)

        return diagrams

    def _generate_summary(
        self,
        mr_details: Dict[str, Any],
        function_changes: List[FunctionChange],
        domain_impact: Dict[str, Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Generate a summary of the analysis.

        Args:
            mr_details: Merge request details
            function_changes: List of function changes
            domain_impact: Dictionary mapping domains to impact information

        Returns:
            Dictionary containing summary information
        """
        # Count changes by type
        change_counts = {
            "added": len([fc for fc in function_changes if fc.change_type == "ADDED"]),
            "modified": len([fc for fc in function_changes if fc.change_type == "MODIFIED"]),
            "deleted": len([fc for fc in function_changes if fc.change_type == "DELETED"])
        }

        # Count domains by level
        domain_levels = {}
        for domain in domain_impact.keys():
            # Count the number of "->" to determine the level
            level = domain.count("->") if "->" in domain else 0

            if level not in domain_levels:
                domain_levels[level] = 0

            domain_levels[level] += 1

        # Get most impacted domains
        sorted_domains = sorted(
            domain_impact.items(),
            key=lambda x: x[1]["impact_score"],
            reverse=True
        )

        # Take top 5 domains
        most_impacted_domains = []
        for domain, impact in sorted_domains[:5]:
            most_impacted_domains.append({
                "name": domain,
                "changes": impact
            })

        # Create summary
        summary = {
            "title": mr_details["title"],
            "author": mr_details["author"]["name"],
            "total_changes": len(function_changes),
            "change_counts": change_counts,
            "total_impacted_domains": len(domain_impact),
            "domain_levels": domain_levels,
            "most_impacted_domains": most_impacted_domains
        }

        return summary
