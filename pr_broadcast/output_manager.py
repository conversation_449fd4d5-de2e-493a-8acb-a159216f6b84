"""
Output Manager

This module provides functionality to manage the output artifacts from PR Broadcast.
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional, Set, Tuple

logger = logging.getLogger(__name__)

class OutputManager:
    """
    Manages the output artifacts from PR Broadcast.
    
    Key features:
    1. Creates appropriate directory structure
    2. Saves generated diagrams
    3. Creates summary reports
    4. Generates HTML reports
    """

    def __init__(self, output_dir: str):
        """
        Initialize the output manager.

        Args:
            output_dir: Directory to save output artifacts
        """
        self.output_dir = output_dir
        
        # Create output directory if it doesn't exist
        self._create_directory_structure()
        
        logger.info(f"Initialized output manager with output directory {output_dir}")

    def save_diagrams(
        self,
        diagrams: Dict[str, Dict[str, Any]],
        mr_iid: int
    ) -> Dict[str, Dict[str, str]]:
        """
        Save generated diagrams to files.

        Args:
            diagrams: Dictionary mapping domains to diagram information
            mr_iid: Merge request IID (internal ID)

        Returns:
            Dictionary mapping diagram types to file paths
        """
        artifacts = {
            "leaf_domains": {},
            "parent_domains": {}
        }
        
        # Save each diagram
        for domain, diagram_info in diagrams.items():
            # Determine if this is a leaf or parent domain
            is_leaf = "changes" in diagram_info
            
            # Create a sanitized domain name for the file name
            domain_file_name = self._sanitize_filename(domain)
            
            # Determine the output directory and file name
            if is_leaf:
                output_dir = os.path.join(self.output_dir, "leaf_domains")
                file_name = f"leaf_domain_impact_{domain_file_name}_{mr_iid}.md"
                diagram_type = "leaf_domains"
            else:
                output_dir = os.path.join(self.output_dir, "parent_domains")
                file_name = f"parent_domain_impact_{domain_file_name}_{mr_iid}.md"
                diagram_type = "parent_domains"
                
            # Create the output directory if it doesn't exist
            os.makedirs(output_dir, exist_ok=True)
                
            # Save the diagram
            file_path = os.path.join(output_dir, file_name)
            
            try:
                with open(file_path, 'w') as f:
                    # Add a header
                    f.write(f"# Impact Diagram for Domain: {domain}\n\n")
                    
                    # Add the original diagram
                    f.write("## Original Diagram\n\n")
                    f.write("```mermaid\n")
                    f.write(diagram_info["original_diagram"])
                    f.write("\n```\n\n")
                    
                    # Add the impact diagram
                    f.write("## Impact Diagram\n\n")
                    f.write("```mermaid\n")
                    f.write(diagram_info["impact_diagram"])
                    f.write("\n```\n\n")
                    
                    # Add additional information for leaf domains
                    if is_leaf:
                        f.write("## Function Changes\n\n")
                        for fc in diagram_info["changes"]["functions"]:
                            f.write(f"### {fc['function_name']} ({fc['change_type']})\n\n")
                            f.write("```diff\n")
                            f.write(fc["diff_content"])
                            f.write("\n```\n\n")
                
                # Add the file path to the artifacts
                artifacts[diagram_type][domain] = file_path
                logger.info(f"Saved diagram for domain {domain} to {file_path}")
            except Exception as e:
                logger.error(f"Failed to save diagram for domain {domain}: {e}")
        
        return artifacts

    def save_summary(
        self,
        summary: Dict[str, Any],
        mr_iid: int
    ) -> str:
        """
        Save a summary report.

        Args:
            summary: Dictionary containing summary information
            mr_iid: Merge request IID (internal ID)

        Returns:
            Path to the summary file
        """
        # Create the summary file path
        file_path = os.path.join(self.output_dir, f"summary_{mr_iid}.md")
        
        try:
            with open(file_path, 'w') as f:
                # Add a header
                f.write(f"# PR Impact Summary for MR !{mr_iid}\n\n")
                
                # Add MR details
                f.write("## MR Details\n\n")
                f.write(f"- **Title**: {summary['title']}\n")
                f.write(f"- **Author**: {summary['author']}\n\n")
                
                # Add change summary
                f.write("## Change Summary\n\n")
                f.write(f"- **Total Changed Functions**: {summary['total_changes']}\n")
                f.write(f"  - Added: {summary['change_counts']['added']}\n")
                f.write(f"  - Modified: {summary['change_counts']['modified']}\n")
                f.write(f"  - Deleted: {summary['change_counts']['deleted']}\n\n")
                
                # Add domain impact summary
                f.write("## Domain Impact Summary\n\n")
                f.write(f"- **Total Impacted Domains**: {summary['total_impacted_domains']}\n")
                
                # Add domain levels
                if summary.get("domain_levels"):
                    f.write("- **Domain Levels**:\n")
                    for level, count in summary["domain_levels"].items():
                        f.write(f"  - Level {level}: {count} domains\n")
                f.write("\n")
                
                # Add most impacted domains
                f.write("## Most Impacted Domains\n\n")
                for domain in summary.get("most_impacted_domains", []):
                    f.write(f"### {domain['name']}\n\n")
                    f.write(f"- **Changed Functions**: {len(domain['changes']['functions'])}\n")
                    f.write("- **Functions**:\n")
                    for fc in domain['changes']['functions']:
                        f.write(f"  - {fc['function_name']} ({fc['change_type']})\n")
                    f.write("\n")
            
            logger.info(f"Saved summary to {file_path}")
            
            # Generate an HTML report
            html_path = self._generate_html_report(summary, mr_iid)
            
            return file_path
        except Exception as e:
            logger.error(f"Failed to save summary: {e}")
            return ""

    def _create_directory_structure(self) -> None:
        """
        Create the directory structure for output artifacts.
        """
        # Create main output directory
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Create subdirectories
        os.makedirs(os.path.join(self.output_dir, "leaf_domains"), exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, "parent_domains"), exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, "html"), exist_ok=True)
        
        logger.info(f"Created directory structure in {self.output_dir}")

    def _generate_html_report(
        self,
        summary: Dict[str, Any],
        mr_iid: int
    ) -> str:
        """
        Generate an HTML report.

        Args:
            summary: Dictionary containing summary information
            mr_iid: Merge request IID (internal ID)

        Returns:
            Path to the HTML report
        """
        # Create the HTML file path
        file_path = os.path.join(self.output_dir, "html", f"pr_impact_report_{mr_iid}.html")
        
        try:
            with open(file_path, 'w') as f:
                # Write HTML header
                f.write(f"""<!DOCTYPE html>
<html>
<head>
    <title>PR Impact Report for MR !{mr_iid}</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {{
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }}
        h1, h2, h3 {{
            color: #2c3e50;
        }}
        .summary-section {{
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }}
        .domain-section {{
            margin-bottom: 20px;
            padding: 15px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
        }}
        .function-list {{
            margin-left: 20px;
        }}
        .added {{
            color: #28a745;
        }}
        .modified {{
            color: #ffc107;
        }}
        .deleted {{
            color: #dc3545;
        }}
        .stats {{
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
        }}
        .stat-box {{
            flex: 1;
            min-width: 200px;
            margin: 10px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 5px;
            text-align: center;
        }}
        .stat-value {{
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
        }}
    </style>
</head>
<body>
    <h1>PR Impact Report for MR !{mr_iid}</h1>
    
    <div class="summary-section">
        <h2>MR Details</h2>
        <p><strong>Title:</strong> {summary['title']}</p>
        <p><strong>Author:</strong> {summary['author']}</p>
        
        <h2>Change Summary</h2>
        <div class="stats">
            <div class="stat-box">
                <h3>Total Changes</h3>
                <div class="stat-value">{summary['total_changes']}</div>
            </div>
            <div class="stat-box">
                <h3>Added</h3>
                <div class="stat-value added">{summary['change_counts']['added']}</div>
            </div>
            <div class="stat-box">
                <h3>Modified</h3>
                <div class="stat-value modified">{summary['change_counts']['modified']}</div>
            </div>
            <div class="stat-box">
                <h3>Deleted</h3>
                <div class="stat-value deleted">{summary['change_counts']['deleted']}</div>
            </div>
        </div>
        
        <h2>Domain Impact</h2>
        <div class="stats">
            <div class="stat-box">
                <h3>Impacted Domains</h3>
                <div class="stat-value">{summary['total_impacted_domains']}</div>
            </div>
""")

                # Add domain levels
                if summary.get("domain_levels"):
                    for level, count in summary["domain_levels"].items():
                        f.write(f"""
            <div class="stat-box">
                <h3>Level {level}</h3>
                <div class="stat-value">{count}</div>
            </div>
""")

                f.write("""
        </div>
    </div>
    
    <h2>Most Impacted Domains</h2>
""")

                # Add most impacted domains
                for domain in summary.get("most_impacted_domains", []):
                    f.write(f"""
    <div class="domain-section">
        <h3>{domain['name']}</h3>
        <p><strong>Changed Functions:</strong> {len(domain['changes']['functions'])}</p>
        <div class="function-list">
""")

                    # Add functions
                    for fc in domain['changes']['functions']:
                        change_type = fc['change_type']
                        class_name = "added" if change_type == "ADDED" else "modified" if change_type == "MODIFIED" else "deleted"
                        f.write(f"""
            <p class="{class_name}"><strong>{fc['function_name']}</strong> ({change_type})</p>
""")

                    f.write("""
        </div>
    </div>
""")

                # Write HTML footer
                f.write("""
</body>
</html>
""")
            
            logger.info(f"Generated HTML report at {file_path}")
            return file_path
        except Exception as e:
            logger.error(f"Failed to generate HTML report: {e}")
            return ""

    def _sanitize_filename(self, name: str) -> str:
        """
        Sanitize a string for use in a filename.

        Args:
            name: String to sanitize

        Returns:
            Sanitized string
        """
        # Replace invalid characters with underscores
        invalid_chars = ['<', '>', ':', '"', '/', '\\', '|', '?', '*', ' ']
        for char in invalid_chars:
            name = name.replace(char, '_')
        
        # Limit the length
        if len(name) > 50:
            name = name[:47] + '...'
        
        return name
