"""
Enhanced PR Broadcast Service

This module provides an enhanced service for analyzing PR/MR diffs, mapping changes to domains,
and generating specialized Mermaid diagrams that visualize the impact of changes.

Key enhancements:
1. Maps function changes to domains in the domain taxonomy
2. Uses LLM to generate impact diagrams for leaf domains
3. Propagates changes upward through the domain hierarchy
4. Combines changes in the same domain
"""

import os
import json
import yaml
import logging
import asyncio
from typing import Dict, List, Any, Optional, Tuple, Set

from pr_broadcast.diff_analysis.gitlab_diff_fetcher import GitlabDiffFetcher
from pr_broadcast.diff_analysis.function_diff_analyzer import FunctionChange, ChangeType
from pr_broadcast.enhanced_function_diff_analyzer import EnhancedFunctionDiffAnalyzer
from pr_broadcast.domain_mapper import DomainMapper
from pr_broadcast.llm_diagram_generator import LLMDiagramGenerator
from pr_broadcast.output_manager import OutputManager

logger = logging.getLogger(__name__)

class EnhancedPRBroadcastService:
    """
    Enhanced service for analyzing PR/MR diffs, mapping changes to domains, and generating
    specialized Mermaid diagrams that visualize the impact of changes.
    """

    def __init__(
        self,
        gitlab_url: str,
        gitlab_token: str,
        taxonomy_json_path: str,
        domain_traces_yaml_path: str,
        output_dir: str,
        llm_api_key: Optional[str] = None,
        llm_model: str = "gpt-4o-mini",
    ):
        """
        Initialize the Enhanced PR Broadcast Service.

        Args:
            gitlab_url: URL of the GitLab instance
            gitlab_token: GitLab API token
            taxonomy_json_path: Path to the domain taxonomy JSON file
            domain_traces_yaml_path: Path to the domain traces YAML file
            output_dir: Directory to save generated diagrams and reports
            llm_api_key: API key for the LLM service (optional)
            llm_model: LLM model to use for diagram generation
        """
        self.gitlab_url = gitlab_url
        self.gitlab_token = gitlab_token
        self.taxonomy_json_path = taxonomy_json_path
        self.domain_traces_yaml_path = domain_traces_yaml_path
        self.output_dir = output_dir
        self.llm_api_key = llm_api_key
        self.llm_model = llm_model

        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Initialize components
        self.diff_fetcher = GitlabDiffFetcher(gitlab_url, gitlab_token)
        self.diff_analyzer = EnhancedFunctionDiffAnalyzer()
        self.domain_mapper = DomainMapper(taxonomy_json_path, domain_traces_yaml_path)
        self.diagram_generator = LLMDiagramGenerator(taxonomy_json_path, llm_api_key, llm_model)
        self.output_manager = OutputManager(output_dir)

        # Load domain taxonomy
        self.taxonomy = self._load_taxonomy()

    async def analyze_merge_request(
        self,
        project_id: str,
        mr_iid: int,
        include_indirect_impact: bool = True,
    ) -> Dict[str, Any]:
        """
        Analyze a GitLab merge request and generate impact diagrams.

        Args:
            project_id: GitLab project ID
            mr_iid: Merge request IID (internal ID)
            include_indirect_impact: Whether to include indirect impact analysis

        Returns:
            Dictionary containing analysis results and paths to generated diagrams
        """
        logger.info(f"Analyzing merge request {mr_iid} in project {project_id}")

        # Step 1: Fetch MR details and diffs, analyze to identify changed functions
        mr_details, function_changes = await self._fetch_and_analyze_diffs(project_id, mr_iid)
        
        # Step 2: Map changed functions to domains in the taxonomy
        domain_changes = self.domain_mapper.map_functions_to_domains(function_changes)
        logger.info(f"Mapped changes to {len(domain_changes)} domains")
        
        # Step 3: Generate impact diagrams for leaf domains
        leaf_diagrams = await self._generate_domain_diagrams(domain_changes, mr_iid)
        logger.info(f"Generated {len(leaf_diagrams)} leaf domain diagrams")
        
        # Step 4: Propagate changes upward through the domain hierarchy
        if include_indirect_impact:
            parent_diagrams = await self._propagate_changes_upward(domain_changes, leaf_diagrams, mr_iid)
            logger.info(f"Generated {len(parent_diagrams)} parent domain diagrams")
            all_diagrams = {**leaf_diagrams, **parent_diagrams}
        else:
            all_diagrams = leaf_diagrams
        
        # Step 5: Save artifacts and generate summary
        artifacts = self.output_manager.save_diagrams(all_diagrams, mr_iid)
        summary = self._generate_summary(mr_details, function_changes, domain_changes)
        summary_path = self.output_manager.save_summary(summary, mr_iid)
        
        # Step 6: Prepare and return results
        results = {
            "mr_details": mr_details,
            "function_changes": [fc.to_dict() for fc in function_changes],
            "domain_changes": domain_changes,
            "diagrams": artifacts,
            "summary": summary,
            "summary_path": summary_path
        }
        
        return results

    async def _fetch_and_analyze_diffs(
        self,
        project_id: str,
        mr_iid: int
    ) -> Tuple[Dict[str, Any], List[FunctionChange]]:
        """
        Fetch MR details and diffs, and analyze them to identify changed functions.

        Args:
            project_id: GitLab project ID
            mr_iid: Merge request IID (internal ID)

        Returns:
            Tuple containing MR details and list of function changes
        """
        # Fetch MR details and diffs
        mr_details, diffs = await self.diff_fetcher.fetch_merge_request(project_id, mr_iid)
        logger.info(f"Fetched {len(diffs)} file diffs from merge request {mr_iid}")

        # Analyze diffs to identify changed functions
        function_changes = []
        for diff in diffs:
            file_changes = self.diff_analyzer.analyze_diff(diff)
            function_changes.extend(file_changes)
        
        logger.info(f"Identified {len(function_changes)} function changes")
        return mr_details, function_changes

    async def _generate_domain_diagrams(
        self,
        domain_changes: Dict[str, Dict[str, Any]],
        mr_iid: int
    ) -> Dict[str, Dict[str, Any]]:
        """
        Generate impact diagrams for leaf domains.

        Args:
            domain_changes: Dictionary mapping domains to changes
            mr_iid: Merge request IID (internal ID)

        Returns:
            Dictionary mapping domains to diagram information
        """
        leaf_diagrams = {}
        
        # Process each leaf domain with changes
        for domain, changes in domain_changes.items():
            # Skip if not a leaf domain
            if not self._is_leaf_domain(domain):
                continue
                
            # Extract the original domain diagram
            domain_diagram = self._get_domain_diagram(domain)
            
            if domain_diagram:
                # Generate a new diagram showing the impact of changes
                new_diagram = await self.diagram_generator.generate_leaf_domain_diagram(
                    domain, 
                    domain_diagram, 
                    changes["functions"]
                )
                
                leaf_diagrams[domain] = {
                    "original_diagram": domain_diagram,
                    "impact_diagram": new_diagram,
                    "changes": changes
                }
        
        return leaf_diagrams

    async def _propagate_changes_upward(
        self,
        domain_changes: Dict[str, Dict[str, Any]],
        leaf_diagrams: Dict[str, Dict[str, Any]],
        mr_iid: int
    ) -> Dict[str, Dict[str, Any]]:
        """
        Propagate changes upward through the domain hierarchy.

        Args:
            domain_changes: Dictionary mapping domains to changes
            leaf_diagrams: Dictionary mapping leaf domains to diagram information
            mr_iid: Merge request IID (internal ID)

        Returns:
            Dictionary mapping parent domains to diagram information
        """
        parent_diagrams = {}
        
        # Get all domains with changes
        affected_domains = set(domain_changes.keys())
        
        # Process each domain with changes
        for domain in affected_domains:
            # Get the parent domain
            parent = self._get_parent_domain(domain)
            
            # Skip if no parent or parent is root
            if not parent or parent == "Root":
                continue
                
            # Skip if already processed
            if parent in parent_diagrams:
                continue
                
            # Get the parent's diagram
            parent_diagram = self._get_domain_diagram(parent)
            
            if parent_diagram:
                # Get all child domains with changes
                child_domains = [d for d in affected_domains if self._is_child_of(d, parent)]
                
                # Collect all child diagrams
                child_diagrams = {}
                for child in child_domains:
                    if child in leaf_diagrams:
                        child_diagrams[child] = leaf_diagrams[child]
                
                # Generate a new diagram showing the impact of changes
                new_diagram = await self.diagram_generator.generate_parent_domain_diagram(
                    parent,
                    parent_diagram,
                    child_diagrams
                )
                
                parent_diagrams[parent] = {
                    "original_diagram": parent_diagram,
                    "impact_diagram": new_diagram,
                    "child_domains": child_domains
                }
        
        return parent_diagrams

    def _generate_summary(
        self,
        mr_details: Dict[str, Any],
        function_changes: List[FunctionChange],
        domain_changes: Dict[str, Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Generate a summary of the analysis results.

        Args:
            mr_details: Merge request details
            function_changes: List of function changes
            domain_changes: Dictionary mapping domains to changes

        Returns:
            Dictionary containing summary information
        """
        # Count changes by type
        change_counts = {
            "added": 0,
            "modified": 0,
            "deleted": 0
        }
        
        for fc in function_changes:
            change_counts[fc.change_type] += 1

        # Count domains by level
        domain_levels = {}
        for domain in domain_changes.keys():
            # Count the number of "->" to determine the level
            level = domain.count("->") if "->" in domain else 0
            
            if level not in domain_levels:
                domain_levels[level] = 0
            
            domain_levels[level] += 1

        # Generate summary
        summary = {
            "title": mr_details["title"],
            "author": mr_details["author"]["name"],
            "total_changes": len(function_changes),
            "change_counts": change_counts,
            "total_impacted_domains": len(domain_changes),
            "domain_levels": domain_levels,
            "most_impacted_domains": self._get_most_impacted_domains(domain_changes, limit=5)
        }

        return summary

    def _get_most_impacted_domains(
        self,
        domain_changes: Dict[str, Dict[str, Any]],
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Get the most impacted domains.

        Args:
            domain_changes: Dictionary mapping domains to changes
            limit: Maximum number of domains to return

        Returns:
            List of dictionaries containing domain name and impact information
        """
        # Sort domains by number of function changes (descending)
        sorted_domains = sorted(
            domain_changes.items(),
            key=lambda x: len(x[1]["functions"]),
            reverse=True
        )

        # Return top N domains
        return [
            {"name": domain, "changes": changes}
            for domain, changes in sorted_domains[:limit]
        ]

    def _load_taxonomy(self) -> Dict[str, Any]:
        """
        Load the domain taxonomy from JSON.

        Returns:
            Dictionary containing domain taxonomy
        """
        try:
            with open(self.taxonomy_json_path, 'r') as f:
                taxonomy = json.load(f)
            
            logger.info(f"Loaded domain taxonomy from {self.taxonomy_json_path}")
            return taxonomy
        except Exception as e:
            logger.error(f"Failed to load domain taxonomy: {e}")
            return {}

    def _is_leaf_domain(self, domain: str) -> bool:
        """
        Check if a domain is a leaf domain (has no children).

        Args:
            domain: Domain name

        Returns:
            True if the domain is a leaf domain, False otherwise
        """
        # Find the domain node in the taxonomy
        domain_node = self._find_domain_node(domain)
        
        # If domain not found or has children, it's not a leaf
        if not domain_node or "children" in domain_node and domain_node["children"]:
            return False
            
        return True

    def _get_parent_domain(self, domain: str) -> Optional[str]:
        """
        Get the parent domain of a domain.

        Args:
            domain: Domain name

        Returns:
            Parent domain name, or None if no parent
        """
        if "->" not in domain:
            return None
            
        parts = domain.split(" -> ")
        return " -> ".join(parts[:-1])

    def _is_child_of(self, child: str, parent: str) -> bool:
        """
        Check if a domain is a child of another domain.

        Args:
            child: Child domain name
            parent: Parent domain name

        Returns:
            True if child is a child of parent, False otherwise
        """
        return child.startswith(parent + " -> ")

    def _find_domain_node(self, domain: str) -> Optional[Dict[str, Any]]:
        """
        Find a domain node in the taxonomy.

        Args:
            domain: Domain name

        Returns:
            Domain node, or None if not found
        """
        # Start with the root node
        node = self.taxonomy
        
        # If looking for the root, return it
        if domain == "Root" or not domain:
            return node
            
        # Split the domain path
        parts = domain.split(" -> ")
        
        # Traverse the taxonomy
        for part in parts:
            if "children" not in node:
                return None
                
            # Find the child with matching name
            found = False
            for child in node["children"]:
                if child["name"] == part:
                    node = child
                    found = True
                    break
                    
            if not found:
                return None
                
        return node

    def _get_domain_diagram(self, domain: str) -> Optional[str]:
        """
        Get the Mermaid diagram for a domain.

        Args:
            domain: Domain name

        Returns:
            Mermaid diagram as a string, or None if not found
        """
        # Find the domain node in the taxonomy
        domain_node = self._find_domain_node(domain)
        
        # If domain not found or has no diagram, return None
        if not domain_node or "diagram" not in domain_node:
            return None
            
        return domain_node["diagram"]
