"""
Impact Diagram Generator

This module provides functionality to generate specialized Mermaid diagrams that
visualize the impact of changes on the domain hierarchy.
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional, Set, Tuple

from pr_broadcast.diff_analysis.function_diff_analyzer import FunctionChange, ChangeType

logger = logging.getLogger(__name__)

class ImpactDiagramGenerator:
    """
    Generates specialized Mermaid diagrams that visualize the impact of changes.
    """

    def __init__(self, output_dir: str):
        """
        Initialize the impact diagram generator.

        Args:
            output_dir: Directory to save generated diagrams
        """
        self.output_dir = output_dir
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Create subdirectories for different diagram types
        self.hierarchy_dir = os.path.join(output_dir, "hierarchy")
        self.domain_dir = os.path.join(output_dir, "domains")
        self.function_dir = os.path.join(output_dir, "functions")
        
        os.makedirs(self.hierarchy_dir, exist_ok=True)
        os.makedirs(self.domain_dir, exist_ok=True)
        os.makedirs(self.function_dir, exist_ok=True)

    def generate_diagrams(
        self,
        mr_details: Dict[str, Any],
        function_changes: List[FunctionChange],
        domain_impact: Dict[str, Any]
    ) -> Dict[str, str]:
        """
        Generate impact diagrams.

        Args:
            mr_details: Merge request details
            function_changes: List of function changes
            domain_impact: Dictionary mapping domains to impact information

        Returns:
            Dictionary mapping diagram types to file paths
        """
        diagrams = {}
        
        # Generate hierarchy impact diagram
        hierarchy_diagram = self._generate_hierarchy_impact_diagram(mr_details, domain_impact)
        hierarchy_path = os.path.join(self.hierarchy_dir, f"hierarchy_impact_{mr_details['iid']}.md")
        self._save_diagram(hierarchy_diagram, hierarchy_path)
        diagrams["hierarchy"] = hierarchy_path
        
        # Generate domain impact diagrams for each affected domain
        domain_diagrams = {}
        for domain, impact in domain_impact.items():
            if impact["direct_impact"]:
                domain_diagram = self._generate_domain_impact_diagram(domain, impact, function_changes)
                domain_path = os.path.join(self.domain_dir, f"domain_impact_{self._sanitize_filename(domain)}_{mr_details['iid']}.md")
                self._save_diagram(domain_diagram, domain_path)
                domain_diagrams[domain] = domain_path
        
        diagrams["domains"] = domain_diagrams
        
        # Generate function change diagrams for each changed function
        function_diagrams = {}
        for change in function_changes:
            function_diagram = self._generate_function_change_diagram(change)
            function_path = os.path.join(self.function_dir, f"function_change_{self._sanitize_filename(change.function_name)}_{mr_details['iid']}.md")
            self._save_diagram(function_diagram, function_path)
            function_diagrams[change.function_name] = function_path
        
        diagrams["functions"] = function_diagrams
        
        # Generate summary diagram
        summary_diagram = self._generate_summary_diagram(mr_details, function_changes, domain_impact)
        summary_path = os.path.join(self.output_dir, f"summary_impact_{mr_details['iid']}.md")
        self._save_diagram(summary_diagram, summary_path)
        diagrams["summary"] = summary_path
        
        logger.info(f"Generated {len(diagrams)} impact diagrams")
        return diagrams

    def _generate_hierarchy_impact_diagram(
        self,
        mr_details: Dict[str, Any],
        domain_impact: Dict[str, Any]
    ) -> str:
        """
        Generate a hierarchy impact diagram.

        Args:
            mr_details: Merge request details
            domain_impact: Dictionary mapping domains to impact information

        Returns:
            Mermaid diagram as a string
        """
        # Extract domains and their impact
        domains = list(domain_impact.keys())
        
        # Group domains by their hierarchy level
        domain_levels = {}
        for domain in domains:
            # Count the number of "->" to determine the level
            level = domain.count("->") if "->" in domain else 0
            
            if level not in domain_levels:
                domain_levels[level] = []
            
            domain_levels[level].append(domain)
        
        # Start building the diagram
        diagram = f"# Hierarchy Impact Diagram for MR !{mr_details['iid']}: {mr_details['title']}\n\n"
        diagram += "```mermaid\nflowchart TD\n"
        
        # Add nodes for each domain
        node_ids = {}
        for level in sorted(domain_levels.keys()):
            for domain in domain_levels[level]:
                # Create a unique node ID
                node_id = f"D{len(node_ids)}"
                node_ids[domain] = node_id
                
                # Get the domain name (last part of the path)
                domain_name = domain.split(" -> ")[-1] if " -> " in domain else domain
                
                # Get the impact information
                impact = domain_impact[domain]
                severity = impact["severity"]
                direct = impact["direct_impact"]
                
                # Determine the node style based on severity and direct impact
                style_class = f"{severity}{'_direct' if direct else '_indirect'}"
                
                # Add the node
                diagram += f"  {node_id}[\"{domain_name}\"]:::{style_class}\n"
        
        # Add edges for domain hierarchy
        for domain, node_id in node_ids.items():
            if " -> " in domain:
                # Get the parent domain
                parent_parts = domain.split(" -> ")[:-1]
                parent = " -> ".join(parent_parts)
                
                # Check if the parent is in our diagram
                if parent in node_ids:
                    parent_id = node_ids[parent]
                    diagram += f"  {parent_id} --> {node_id}\n"
        
        # Add style classes
        diagram += "\n  %% Style classes\n"
        diagram += "  classDef high_direct fill:#FF5252,stroke:#FF0000,color:white,stroke-width:2px,font-weight:bold\n"
        diagram += "  classDef high_indirect fill:#FF7F7F,stroke:#FF5252,color:white,stroke-width:1px\n"
        diagram += "  classDef medium_direct fill:#FFD700,stroke:#FFA500,stroke-width:2px,font-weight:bold\n"
        diagram += "  classDef medium_indirect fill:#FFEC8B,stroke:#FFD700,stroke-width:1px\n"
        diagram += "  classDef low_direct fill:#90EE90,stroke:#32CD32,stroke-width:2px,font-weight:bold\n"
        diagram += "  classDef low_indirect fill:#E0FFE0,stroke:#90EE90,stroke-width:1px\n"
        
        diagram += "```\n\n"
        
        # Add a legend
        diagram += "## Legend\n\n"
        diagram += "- **Red**: High impact\n"
        diagram += "- **Yellow**: Medium impact\n"
        diagram += "- **Green**: Low impact\n"
        diagram += "- **Bold border**: Direct impact\n"
        diagram += "- **Thin border**: Indirect impact\n"
        
        return diagram

    def _generate_domain_impact_diagram(
        self,
        domain: str,
        impact: Dict[str, Any],
        function_changes: List[FunctionChange]
    ) -> str:
        """
        Generate a domain impact diagram.

        Args:
            domain: Domain name
            impact: Impact information for the domain
            function_changes: List of function changes

        Returns:
            Mermaid diagram as a string
        """
        # Get the domain name (last part of the path)
        domain_name = domain.split(" -> ")[-1] if " -> " in domain else domain
        
        # Get the changes for this domain
        domain_changes = impact["changes"]
        
        # Group changes by file
        file_changes = {}
        for change in domain_changes:
            file_path = change["file_path"]
            
            if file_path not in file_changes:
                file_changes[file_path] = []
            
            file_changes[file_path].append(change)
        
        # Start building the diagram
        diagram = f"# Domain Impact Diagram for {domain}\n\n"
        diagram += f"## Impact Summary\n\n"
        diagram += f"- **Severity**: {impact['severity']}\n"
        diagram += f"- **Impact Score**: {impact['impact_score']:.2f}\n"
        diagram += f"- **Direct Impact**: {'Yes' if impact['direct_impact'] else 'No'}\n"
        diagram += f"- **Indirect Impact**: {'Yes' if impact['indirect_impact'] else 'No'}\n"
        diagram += f"- **Changed Functions**: {len(domain_changes)}\n\n"
        
        diagram += "```mermaid\nflowchart TD\n"
        
        # Add a node for the domain
        diagram += f"  D[\"{domain_name}\"]:::domain\n\n"
        
        # Add nodes for each file
        file_ids = {}
        for file_path in file_changes.keys():
            # Create a unique node ID
            file_id = f"F{len(file_ids)}"
            file_ids[file_path] = file_id
            
            # Get the file name
            file_name = os.path.basename(file_path)
            
            # Add the node
            diagram += f"  {file_id}[\"File: {file_name}\"]:::file\n"
            
            # Connect to the domain
            diagram += f"  D --> {file_id}\n"
        
        diagram += "\n"
        
        # Add nodes for each function
        for file_path, changes in file_changes.items():
            file_id = file_ids[file_path]
            
            for i, change in enumerate(changes):
                # Create a unique node ID
                func_id = f"{file_id}_F{i}"
                
                # Get the function name and change type
                func_name = change["function_name"]
                change_type = change["change_type"]
                
                # Add the node
                diagram += f"  {func_id}[\"{func_name}\"]:::{change_type}\n"
                
                # Connect to the file
                diagram += f"  {file_id} --> {func_id}\n"
        
        # Add style classes
        diagram += "\n  %% Style classes\n"
        diagram += "  classDef domain fill:#D4F1F9,stroke:#1E90FF,stroke-width:2px,font-weight:bold\n"
        diagram += "  classDef file fill:#F0F8FF,stroke:#87CEFA,stroke-width:1px\n"
        diagram += "  classDef added fill:#90EE90,stroke:#32CD32,stroke-width:2px\n"
        diagram += "  classDef modified fill:#FFD700,stroke:#FFA500,stroke-width:2px\n"
        diagram += "  classDef deleted fill:#FF5252,stroke:#FF0000,stroke-width:2px\n"
        
        diagram += "```\n\n"
        
        # Add a legend
        diagram += "## Legend\n\n"
        diagram += "- **Green**: Added functions\n"
        diagram += "- **Yellow**: Modified functions\n"
        diagram += "- **Red**: Deleted functions\n"
        
        # Add function details
        diagram += "\n## Changed Functions\n\n"
        
        for file_path, changes in file_changes.items():
            file_name = os.path.basename(file_path)
            diagram += f"### File: {file_name}\n\n"
            
            for change in changes:
                func_name = change["function_name"]
                change_type = change["change_type"]
                
                # Format the change type
                if change_type == ChangeType.ADDED:
                    change_type_str = "Added"
                elif change_type == ChangeType.MODIFIED:
                    change_type_str = "Modified"
                else:
                    change_type_str = "Deleted"
                
                diagram += f"#### {func_name} ({change_type_str})\n\n"
                
                # Add the diff content if available
                if change.get("diff_content"):
                    diagram += "```diff\n"
                    diagram += change["diff_content"]
                    diagram += "\n```\n\n"
        
        return diagram

    def _generate_function_change_diagram(self, change: FunctionChange) -> str:
        """
        Generate a function change diagram.

        Args:
            change: Function change

        Returns:
            Mermaid diagram as a string
        """
        # Get the function name and change type
        func_name = change.function_name
        change_type = change.change_type
        
        # Format the change type
        if change_type == ChangeType.ADDED:
            change_type_str = "Added"
        elif change_type == ChangeType.MODIFIED:
            change_type_str = "Modified"
        else:
            change_type_str = "Deleted"
        
        # Start building the diagram
        diagram = f"# Function Change Diagram for {func_name}\n\n"
        diagram += f"## Change Details\n\n"
        diagram += f"- **File**: {change.file_path}\n"
        diagram += f"- **Function**: {func_name}\n"
        diagram += f"- **Change Type**: {change_type_str}\n"
        diagram += f"- **Impact Score**: {change.impact_score:.2f}\n\n"
        
        # Add the diff content if available
        if change.diff_content:
            diagram += "## Diff\n\n"
            diagram += "```diff\n"
            diagram += change.diff_content
            diagram += "\n```\n\n"
        
        # Add a simple mermaid diagram
        diagram += "## Visual Representation\n\n"
        diagram += "```mermaid\nflowchart LR\n"
        
        # Add nodes for the file and function
        diagram += f"  F[\"File: {os.path.basename(change.file_path)}\"]:::file\n"
        diagram += f"  FN[\"{func_name}\"]:::{change_type}\n"
        
        # Connect the file to the function
        diagram += f"  F --> FN\n"
        
        # Add style classes
        diagram += "\n  %% Style classes\n"
        diagram += "  classDef file fill:#F0F8FF,stroke:#87CEFA,stroke-width:1px\n"
        diagram += "  classDef added fill:#90EE90,stroke:#32CD32,stroke-width:2px\n"
        diagram += "  classDef modified fill:#FFD700,stroke:#FFA500,stroke-width:2px\n"
        diagram += "  classDef deleted fill:#FF5252,stroke:#FF0000,stroke-width:2px\n"
        
        diagram += "```\n"
        
        return diagram

    def _generate_summary_diagram(
        self,
        mr_details: Dict[str, Any],
        function_changes: List[FunctionChange],
        domain_impact: Dict[str, Any]
    ) -> str:
        """
        Generate a summary diagram.

        Args:
            mr_details: Merge request details
            function_changes: List of function changes
            domain_impact: Dictionary mapping domains to impact information

        Returns:
            Mermaid diagram as a string
        """
        # Count changes by type
        change_counts = {
            ChangeType.ADDED: 0,
            ChangeType.MODIFIED: 0,
            ChangeType.DELETED: 0
        }
        
        for change in function_changes:
            change_counts[change.change_type] += 1
        
        # Count domains by impact severity
        impact_counts = {
            "high": 0,
            "medium": 0,
            "low": 0
        }
        
        for domain, impact in domain_impact.items():
            impact_counts[impact["severity"]] += 1
        
        # Start building the diagram
        diagram = f"# Impact Summary for MR !{mr_details['iid']}: {mr_details['title']}\n\n"
        diagram += f"## MR Details\n\n"
        diagram += f"- **Title**: {mr_details['title']}\n"
        diagram += f"- **Author**: {mr_details['author']['name']}\n"
        diagram += f"- **Source Branch**: {mr_details['source_branch']}\n"
        diagram += f"- **Target Branch**: {mr_details['target_branch']}\n\n"
        
        diagram += f"## Impact Summary\n\n"
        diagram += f"- **Total Changed Functions**: {len(function_changes)}\n"
        diagram += f"  - Added: {change_counts[ChangeType.ADDED]}\n"
        diagram += f"  - Modified: {change_counts[ChangeType.MODIFIED]}\n"
        diagram += f"  - Deleted: {change_counts[ChangeType.DELETED]}\n"
        diagram += f"- **Total Impacted Domains**: {len(domain_impact)}\n"
        diagram += f"  - High Impact: {impact_counts['high']}\n"
        diagram += f"  - Medium Impact: {impact_counts['medium']}\n"
        diagram += f"  - Low Impact: {impact_counts['low']}\n\n"
        
        # Add a mermaid diagram
        diagram += "```mermaid\npie title Function Changes\n"
        diagram += f"  \"Added\" : {change_counts[ChangeType.ADDED]}\n"
        diagram += f"  \"Modified\" : {change_counts[ChangeType.MODIFIED]}\n"
        diagram += f"  \"Deleted\" : {change_counts[ChangeType.DELETED]}\n"
        diagram += "```\n\n"
        
        diagram += "```mermaid\npie title Domain Impact\n"
        diagram += f"  \"High\" : {impact_counts['high']}\n"
        diagram += f"  \"Medium\" : {impact_counts['medium']}\n"
        diagram += f"  \"Low\" : {impact_counts['low']}\n"
        diagram += "```\n\n"
        
        # Add most impacted domains
        diagram += "## Most Impacted Domains\n\n"
        
        # Sort domains by impact score
        sorted_domains = sorted(
            domain_impact.items(),
            key=lambda x: x[1]["impact_score"],
            reverse=True
        )
        
        # Take the top 5
        top_domains = sorted_domains[:5]
        
        diagram += "```mermaid\nflowchart TD\n"
        diagram += "  MR[\"MR !{mr_details['iid']}\"]:::mr\n"
        
        # Add nodes for each domain
        for i, (domain, impact) in enumerate(top_domains):
            # Create a unique node ID
            node_id = f"D{i}"
            
            # Get the domain name (last part of the path)
            domain_name = domain.split(" -> ")[-1] if " -> " in domain else domain
            
            # Get the severity
            severity = impact["severity"]
            
            # Add the node
            diagram += f"  {node_id}[\"{domain_name}\"]:::{severity}\n"
            
            # Connect to the MR
            diagram += f"  MR --> {node_id}\n"
        
        # Add style classes
        diagram += "\n  %% Style classes\n"
        diagram += "  classDef mr fill:#D4F1F9,stroke:#1E90FF,stroke-width:2px,font-weight:bold\n"
        diagram += "  classDef high fill:#FF5252,stroke:#FF0000,color:white,stroke-width:2px\n"
        diagram += "  classDef medium fill:#FFD700,stroke:#FFA500,stroke-width:2px\n"
        diagram += "  classDef low fill:#90EE90,stroke:#32CD32,stroke-width:2px\n"
        
        diagram += "```\n\n"
        
        # Add a table of the most impacted domains
        diagram += "| Domain | Impact Score | Severity | Direct Impact | Changed Functions |\n"
        diagram += "|--------|--------------|----------|---------------|-------------------|\n"
        
        for domain, impact in top_domains:
            diagram += f"| {domain} | {impact['impact_score']:.2f} | {impact['severity']} | {'Yes' if impact['direct_impact'] else 'No'} | {len(impact['changes'])} |\n"
        
        return diagram

    def _save_diagram(self, diagram: str, file_path: str) -> None:
        """
        Save a diagram to a file.

        Args:
            diagram: Diagram content
            file_path: Path to save the diagram
        """
        try:
            with open(file_path, 'w') as f:
                f.write(diagram)
            
            logger.info(f"Saved diagram to {file_path}")
        except Exception as e:
            logger.error(f"Failed to save diagram to {file_path}: {e}")

    def _sanitize_filename(self, name: str) -> str:
        """
        Sanitize a string for use in a filename.

        Args:
            name: String to sanitize

        Returns:
            Sanitized string
        """
        # Replace invalid characters with underscores
        invalid_chars = ['<', '>', ':', '"', '/', '\\', '|', '?', '*']
        for char in invalid_chars:
            name = name.replace(char, '_')
        
        # Replace spaces with underscores
        name = name.replace(' ', '_')
        
        # Limit the length
        if len(name) > 50:
            name = name[:47] + '...'
        
        return name
