#!/usr/bin/env python3
"""
PR Broadcast CLI

This module provides a command-line interface for the PR Broadcast service.
"""

import os
import sys
import json
import logging
import asyncio
import argparse
from typing import Dict, List, Any, Optional

from pr_broadcast.service import PRBroadcastService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def main():
    """Main entry point for the PR Broadcast CLI."""
    parser = argparse.ArgumentParser(description="PR Broadcast - Analyze PR/MR impact on domains")

    # Required arguments
    # parser.add_argument("--taxonomy-json", required=True, help="Path to domain taxonomy JSON file")
    # parser.add_argument("--domain-traces-yaml", required=True, help="Path to domain traces YAML file")
    # parser.add_argument("--output-dir", required=True, help="Directory to save generated diagrams and reports")

    # GitLab integration arguments
    gitlab_group = parser.add_argument_group("GitLab Integration")
    gitlab_group.add_argument("--gitlab-url", help="URL of the GitLab instance")
    gitlab_group.add_argument("--gitlab-token", help="GitLab API token")
    gitlab_group.add_argument("--project-id", help="GitLab project ID")
    gitlab_group.add_argument("--mr-iid", type=int, help="Merge request IID (internal ID)")

    # Local analysis arguments
    local_group = parser.add_argument_group("Local Analysis")
    local_group.add_argument("--local-diffs", help="Path to JSON file containing local diffs")
    local_group.add_argument("--mr-details", help="Path to JSON file containing merge request details")

    # LLM arguments
    llm_group = parser.add_argument_group("LLM Integration")
    llm_group.add_argument("--llm-api-key", help="API key for the LLM service (OpenAI or OpenRouter)")
    llm_group.add_argument("--llm-model", default="gpt-40-mini", help="LLM model to use")

    # Optional arguments
    parser.add_argument("--include-indirect-impact", action="store_true", default=True,help="Include indirect impact analysis")
    parser.add_argument("--use-llm-diagrams", action="store_true", default=True,help="Use LLM to generate diagrams")
    parser.add_argument("--open-browser", action="store_true", default=True, help="Open HTML report in browser")
    parser.add_argument("--save-results", action="store_true", default=True, help="Save analysis results to JSON file")

    args = parser.parse_args()

    args.local_diffs =  "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/pr_broadcast/data/formatted_logger_diff.json"
    args.mr_details = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/pr_broadcast/data/logger_mr_details.json"
    args.taxonomy_json =  "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/domain_taxonomy.json"
    args.domain_traces_yaml =  "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/domain_traces.yaml"
    args.output_dir =  "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/pr_broadcast/data/bracket/"

    # Validate arguments
    # if (args.gitlab_url and args.gitlab_token and args.project_id and args.mr_iid) and (args.local_diffs or args.mr_details):
    #     logger.error("Cannot specify both GitLab integration and local analysis arguments")
    #     return 1

    # if not ((args.gitlab_url and args.gitlab_token and args.project_id and args.mr_iid) or (args.local_diffs and args.mr_details)):
    #     logger.error("Must specify either GitLab integration or local analysis arguments")
    #     return 1

    try:
        # Initialize the PR Broadcast service
        service = PRBroadcastService(
            gitlab_url=args.gitlab_url or "",
            gitlab_token=args.gitlab_token or "",
            taxonomy_json_path=args.taxonomy_json,
            domain_traces_yaml_path=args.domain_traces_yaml,
            output_dir=args.output_dir,
            llm_api_key=args.llm_api_key,
            llm_model=args.llm_model
        )

        # Analyze merge request or local diffs
        if args.gitlab_url and args.gitlab_token and args.project_id and args.mr_iid:
            # Analyze GitLab merge request
            results = await service.analyze_merge_request(
                project_id=args.project_id,
                mr_iid=args.mr_iid,
                include_indirect_impact=args.include_indirect_impact,
                use_llm_diagrams=args.use_llm_diagrams
            )
        else:
            # Analyze local diffs
            with open(args.local_diffs, 'r') as f:
                diffs_data = json.load(f)

            # Check if the loaded JSON has a specific structure
            if isinstance(diffs_data, dict) and 'diffs' in diffs_data and 'mr_details' in diffs_data:
                # This is a combined file with both diffs and mr_details
                diffs = diffs_data
                mr_details = diffs_data['mr_details']
                logger.info(f"Loaded combined diffs and MR details from {args.local_diffs}")
            else:
                # Separate files for diffs and mr_details
                diffs = diffs_data

                with open(args.mr_details, 'r') as f:
                    mr_details = json.load(f)
                logger.info(f"Loaded diffs from {args.local_diffs} and MR details from {args.mr_details}")

            results = await service.analyze_local_diffs(
                diffs=diffs,
                mr_details=mr_details,
                include_indirect_impact=args.include_indirect_impact,
                use_llm_diagrams=args.use_llm_diagrams
            )

        # Print summary
        print("\n" + "=" * 80)
        print(f"PR BROADCAST SUMMARY FOR MR !{results['mr_details']['iid']}")
        print("=" * 80)

        print(f"\nTitle: {results['mr_details']['title']}")
        print(f"Author: {results['mr_details']['author']['name']}")

        print("\nCHANGE SUMMARY:")
        print(f"- Total Changed Functions: {len(results['function_changes'])}")

        change_counts = {
            "ADDED": 0,
            "MODIFIED": 0,
            "DELETED": 0
        }

        for fc in results['function_changes']:
            change_counts[fc['change_type']] += 1

        print(f"  - Added: {change_counts['ADDED']}")
        print(f"  - Modified: {change_counts['MODIFIED']}")
        print(f"  - Deleted: {change_counts['DELETED']}")

        print("\nDOMAIN IMPACT SUMMARY:")
        print(f"- Total Impacted Domains: {len(results['domain_impact'])}")

        # Count domains by level
        domain_levels = {}
        for domain in results['domain_impact'].keys():
            # Count the number of "->" to determine the level
            level = domain.count("->")

            if level not in domain_levels:
                domain_levels[level] = 0

            domain_levels[level] += 1

        print("- Domain Levels:")
        for level, count in domain_levels.items():
            print(f"  - Level {level}: {count} domains")

        print("\nMOST IMPACTED DOMAINS:")
        sorted_domains = sorted(
            results['domain_impact'].items(),
            key=lambda x: x[1]["impact_score"],
            reverse=True
        )

        for domain, impact in sorted_domains[:5]:
            print(f"- {domain}")
            print(f"  - Impact Score: {impact['impact_score']:.2f}")
            print(f"  - Severity: {impact['severity']}")
            print(f"  - Direct Impact: {'Yes' if impact['direct_impact'] else 'No'}")
            print(f"  - Changed Functions: {len(impact['changes'])}")

        print("\nGENERATED ARTIFACTS:")
        if "artifacts" in results:
            artifacts = results["artifacts"]
            if "leaf_domains" in artifacts:
                print(f"- Leaf Domain Diagrams: {len(artifacts['leaf_domains'])}")
            if "parent_domains" in artifacts:
                print(f"- Parent Domain Diagrams: {len(artifacts['parent_domains'])}")

        print(f"\nSummary Report: {results['summary_path']}")

        # Save results if requested
        if args.save_results:
            save_results(results, args.output_dir, results['mr_details']['iid'])

        # Open HTML report in browser if requested
        if args.open_browser:
            html_dir = os.path.join(args.output_dir, "html")
            html_file = f"pr_impact_report_{results['mr_details']['iid']}.html"
            html_path = os.path.join(html_dir, html_file)

            if os.path.exists(html_path):
                import webbrowser
                webbrowser.open(f"file://{os.path.abspath(html_path)}")
                print(f"Opened HTML report in browser: {html_path}")
            else:
                print(f"HTML report not found: {html_path}")

        print("\n" + "=" * 80)
        print("PR Broadcast analysis complete!")
        print("=" * 80 + "\n")

        return 0

    except Exception as e:
        logger.error(f"Error in PR Broadcast CLI: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

def save_results(results: Dict[str, Any], output_dir: str, mr_iid: int) -> None:
    """
    Save analysis results to a JSON file.

    Args:
        results: Analysis results
        output_dir: Output directory
        mr_iid: Merge request IID
    """
    results_path = os.path.join(output_dir, f"results_{mr_iid}.json")

    try:
        with open(results_path, 'w') as f:
            json.dump(results, f, indent=2)

        logger.info(f"Saved analysis results to {results_path}")
    except Exception as e:
        logger.error(f"Failed to save analysis results: {e}")

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
