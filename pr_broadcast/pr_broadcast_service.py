"""
PR Broadcast Service

This module provides the main service class that orchestrates the PR/MR diff analysis,
domain impact mapping, and diagram generation.
"""

import os
import logging
from typing import Dict, List, Any, Optional, Tuple

from pr_broadcast.diff_analysis.gitlab_diff_fetcher import GitlabDiffFetcher
from pr_broadcast.diff_analysis.function_diff_analyzer import Fun<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FunctionChange
from pr_broadcast.domain_impact.impact_analyzer import DomainImpactAnalyzer
from pr_broadcast.domain_impact.impact_propagator import ImpactPropagator
from pr_broadcast.diagram_generation.impact_diagram_generator import ImpactDiagramGenerator

logger = logging.getLogger(__name__)

class PRBroadcastService:
    """
    Service for analyzing PR/MR diffs, mapping changes to domains, and generating
    specialized Mermaid diagrams that visualize the impact of changes.
    """

    def __init__(
        self,
        gitlab_url: str,
        gitlab_token: str,
        taxonomy_json_path: str,
        domain_traces_yaml_path: str,
        output_dir: str,
    ):
        """
        Initialize the PR Broadcast Service.

        Args:
            gitlab_url: URL of the GitLab instance
            gitlab_token: GitLab API token
            taxonomy_json_path: Path to the domain taxonomy JSON file
            domain_traces_yaml_path: Path to the domain traces YAML file
            output_dir: Directory to save generated diagrams and reports
        """
        self.gitlab_url = gitlab_url
        self.gitlab_token = gitlab_token
        self.taxonomy_json_path = taxonomy_json_path
        self.domain_traces_yaml_path = domain_traces_yaml_path
        self.output_dir = output_dir

        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Initialize components
        self.diff_fetcher = GitlabDiffFetcher(gitlab_url, gitlab_token)
        self.diff_analyzer = FunctionDiffAnalyzer()
        self.impact_analyzer = DomainImpactAnalyzer(taxonomy_json_path, domain_traces_yaml_path)
        self.impact_propagator = ImpactPropagator(taxonomy_json_path)
        self.diagram_generator = ImpactDiagramGenerator(output_dir)

    async def analyze_merge_request(
        self,
        project_id: str,
        mr_iid: int,
        include_indirect_impact: bool = True,
    ) -> Dict[str, Any]:
        """
        Analyze a GitLab merge request and generate impact diagrams.

        Args:
            project_id: GitLab project ID
            mr_iid: Merge request IID (internal ID)
            include_indirect_impact: Whether to include indirect impact analysis

        Returns:
            Dictionary containing analysis results and paths to generated diagrams
        """
        logger.info(f"Analyzing merge request {mr_iid} in project {project_id}")

        # Step 1: Fetch MR details and diffs
        mr_details, diffs = await self.diff_fetcher.fetch_merge_request(project_id, mr_iid)
        logger.info(f"Fetched {len(diffs)} file diffs from merge request {mr_iid}")

        # Step 2: Analyze diffs to identify changed functions
        function_changes = []
        for diff in diffs:
            file_changes = self.diff_analyzer.analyze_diff(diff)
            function_changes.extend(file_changes)
        
        logger.info(f"Identified {len(function_changes)} function changes")

        # TODO: Not sure if the changed function to domain mapping follows the correct path
        # Step 3: Map changed functions to domains and analyze direct impact
        direct_impact = self.impact_analyzer.analyze_impact(function_changes)
        logger.info(f"Identified direct impact on {len(direct_impact)} domains")

        # Step 4: Propagate impact through domain hierarchy and call graph
        if include_indirect_impact:
            all_impact = self.impact_propagator.propagate_impact(direct_impact, function_changes)
            logger.info(f"Propagated impact to {len(all_impact)} domains")
        else:
            all_impact = direct_impact

        # Step 5: Generate impact diagrams
        diagrams = self.diagram_generator.generate_diagrams(
            mr_details,
            function_changes,
            all_impact
        )
        logger.info(f"Generated {len(diagrams)} impact diagrams")

        # Step 6: Prepare and return results
        results = {
            "mr_details": mr_details,
            "function_changes": [fc.to_dict() for fc in function_changes],
            "domain_impact": all_impact,
            "diagrams": diagrams,
            "summary": self._generate_summary(mr_details, function_changes, all_impact)
        }

        return results

    def _generate_summary(
        self,
        mr_details: Dict[str, Any],
        function_changes: List[FunctionChange],
        domain_impact: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Generate a summary of the analysis results.

        Args:
            mr_details: Merge request details
            function_changes: List of function changes
            domain_impact: Dictionary mapping domains to impact information

        Returns:
            Dictionary containing summary information
        """
        # Count changes by type
        change_counts = {
            "added": 0,
            "modified": 0,
            "deleted": 0
        }
        
        for fc in function_changes:
            change_counts[fc.change_type] += 1

        # Count domains by impact severity
        impact_counts = {
            "high": 0,
            "medium": 0,
            "low": 0
        }
        
        for domain, impact in domain_impact.items():
            impact_counts[impact["severity"]] += 1

        # Generate summary
        summary = {
            "title": mr_details["title"],
            "author": mr_details["author"]["name"],
            "total_changes": len(function_changes),
            "change_counts": change_counts,
            "total_impacted_domains": len(domain_impact),
            "impact_counts": impact_counts,
            "most_impacted_domains": self._get_most_impacted_domains(domain_impact, limit=5)
        }

        return summary

    def _get_most_impacted_domains(
        self,
        domain_impact: Dict[str, Any],
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Get the most impacted domains.

        Args:
            domain_impact: Dictionary mapping domains to impact information
            limit: Maximum number of domains to return

        Returns:
            List of dictionaries containing domain name and impact information
        """
        # Sort domains by impact score (descending)
        sorted_domains = sorted(
            domain_impact.items(),
            key=lambda x: x[1]["impact_score"],
            reverse=True
        )

        # Return top N domains
        return [
            {"name": domain, "impact": impact}
            for domain, impact in sorted_domains[:limit]
        ]
