"""
Enhanced Function Diff Analyzer

This module provides an enhanced analyzer for extracting function changes from diffs,
with improved function signature formatting and matching.
"""

import os
import re
import logging
from typing import Dict, List, Any, Optional, Tuple

from pr_broadcast.diff_analysis.function_diff_analyzer import FunctionDiffAnalyzer, FunctionChange, ChangeType

logger = logging.getLogger(__name__)

class EnhancedFunctionDiffAnalyzer(FunctionDiffAnalyzer):
    """
    Enhanced analyzer for extracting function changes from diffs.
    
    Key enhancements:
    1. Improved function signature extraction
    2. Proper formatting of function signatures (file_path:Class.method)
    3. Better handling of class methods and nested functions
    """

    def analyze_diff(self, diff: Dict[str, Any]) -> List[FunctionChange]:
        """
        Analyze a diff to extract function changes.

        Args:
            diff: Dictionary containing diff information

        Returns:
            List of function changes
        """
        # Skip if diff is empty or for a binary file
        if not diff.get("diff") or diff.get("binary_file", False):
            return []

        # Get file paths
        old_path = diff.get("old_path", "")
        new_path = diff.get("new_path", "")
        
        # Use new path if available, otherwise use old path
        file_path = new_path if new_path and new_path != "/dev/null" else old_path
        
        # Skip if no file path
        if not file_path or file_path == "/dev/null":
            return []
            
        # Skip non-Python files for now (can be extended to support other languages)
        if not file_path.endswith((".py", ".rb", ".js", ".ts", ".java", ".c", ".cpp", ".h", ".hpp")):
            return []

        # Extract function changes from the diff
        function_changes = self._extract_function_changes(diff["diff"], file_path)
        
        logger.debug(f"Extracted {len(function_changes)} function changes from {file_path}")
        return function_changes

    def _extract_function_changes(self, diff_content: str, file_path: str) -> List[FunctionChange]:
        """
        Extract function changes from a diff.

        Args:
            diff_content: Diff content
            file_path: File path

        Returns:
            List of function changes
        """
        function_changes = []
        current_function = None
        current_diff_lines = []
        in_function_diff = False
        
        # Split the diff into lines
        lines = diff_content.split("\n")
        
        for line in lines:
            # Skip diff header lines
            if line.startswith("---") or line.startswith("+++") or line.startswith("@@"):
                # If we were processing a function diff, save it
                if in_function_diff and current_function:
                    function_change = self._create_function_change(
                        current_function, 
                        file_path, 
                        "\n".join(current_diff_lines)
                    )
                    function_changes.append(function_change)
                    
                # Reset for next function
                current_function = None
                current_diff_lines = []
                in_function_diff = False
                continue
                
            # Check if this line defines or modifies a function
            if line.startswith("+") or line.startswith("-"):
                content = line[1:].strip()
                
                # Check if this is a function definition
                function_match = self._is_function_definition(content)
                if function_match:
                    # If we were processing a function diff, save it
                    if in_function_diff and current_function:
                        function_change = self._create_function_change(
                            current_function, 
                            file_path, 
                            "\n".join(current_diff_lines)
                        )
                        function_changes.append(function_change)
                        
                    # Start tracking a new function
                    current_function = function_match
                    current_diff_lines = [line]
                    in_function_diff = True
                elif in_function_diff:
                    # Add this line to the current function diff
                    current_diff_lines.append(line)
            elif in_function_diff:
                # Add context lines to the current function diff
                current_diff_lines.append(line)
        
        # Save the last function diff if any
        if in_function_diff and current_function:
            function_change = self._create_function_change(
                current_function, 
                file_path, 
                "\n".join(current_diff_lines)
            )
            function_changes.append(function_change)
        
        return function_changes

    def _is_function_definition(self, line: str) -> Optional[str]:
        """
        Check if a line defines a function or method.

        Args:
            line: Line to check

        Returns:
            Function signature if the line defines a function, None otherwise
        """
        # Python function or method definition
        python_match = re.match(r'^(\s*)(def|async\s+def)\s+([a-zA-Z0-9_]+)\s*\(', line)
        if python_match:
            indentation = python_match.group(1)
            func_name = python_match.group(3)
            
            # Check if this is a class method (indented and not __init__)
            if indentation and func_name != "__init__":
                # Try to find the class name by looking at previous lines
                class_name = self._extract_class_name(line)
                if class_name:
                    return f"{class_name}.{func_name}"
            
            return func_name
            
        # Python class definition
        class_match = re.match(r'^(\s*)class\s+([a-zA-Z0-9_]+)', line)
        if class_match:
            class_name = class_match.group(2)
            return f"{class_name}"
            
        # Ruby method definition
        ruby_match = re.match(r'^(\s*)def\s+([a-zA-Z0-9_]+)', line)
        if ruby_match:
            return ruby_match.group(2)
            
        # JavaScript/TypeScript function definition
        js_match = re.match(r'^(\s*)function\s+([a-zA-Z0-9_]+)', line)
        if js_match:
            return js_match.group(2)
            
        # JavaScript/TypeScript method definition
        js_method_match = re.match(r'^(\s*)([a-zA-Z0-9_]+)\s*\([^)]*\)\s*{', line)
        if js_method_match:
            return js_method_match.group(2)
            
        # Java/C++/C method definition
        c_match = re.match(r'^(\s*)([a-zA-Z0-9_]+)\s+([a-zA-Z0-9_]+)\s*\(', line)
        if c_match:
            return c_match.group(3)
            
        return None

    def _extract_class_name(self, line: str) -> Optional[str]:
        """
        Extract the class name from a method definition line.

        Args:
            line: Line containing a method definition

        Returns:
            Class name if found, None otherwise
        """
        # This is a simplified implementation
        # In a real implementation, we would need to analyze the context
        # For now, we'll just assume the method belongs to a class with a generic name
        indentation = len(line) - len(line.lstrip())
        if indentation > 0:
            return "Class"  # Placeholder
        return None

    def _create_function_change(
        self,
        function_name: str,
        file_path: str,
        diff_content: str
    ) -> FunctionChange:
        """
        Create a FunctionChange object.

        Args:
            function_name: Function name
            file_path: File path
            diff_content: Diff content for the function

        Returns:
            FunctionChange object
        """
        # Determine the change type
        if all(line.startswith("+") for line in diff_content.split("\n") if line.startswith(("+", "-"))):
            change_type = ChangeType.ADDED
        elif all(line.startswith("-") for line in diff_content.split("\n") if line.startswith(("+", "-"))):
            change_type = ChangeType.DELETED
        else:
            change_type = ChangeType.MODIFIED
            
        # Format the function signature
        function_signature = self._format_function_signature(function_name, file_path)
        
        # Calculate impact score
        impact_score = self._calculate_impact_score(change_type, diff_content)
        
        return FunctionChange(
            function_name=function_name,
            function_signature=function_signature,
            file_path=file_path,
            change_type=change_type,
            diff_content=diff_content,
            impact_score=impact_score
        )

    def _format_function_signature(self, function_name: str, file_path: str) -> str:
        """
        Format a function signature in the format "file_path:Class.method".

        Args:
            function_name: Function name
            file_path: File path

        Returns:
            Formatted function signature
        """
        # Extract the file name from the path
        file_name = os.path.basename(file_path)
        
        # If function_name already includes a class name, use it as is
        if "." in function_name:
            return f"{file_name}:{function_name}"
            
        # Otherwise, assume it's a standalone function
        return f"{file_name}:{function_name}"

    def _calculate_impact_score(self, change_type: str, diff_content: str) -> float:
        """
        Calculate an impact score for a function change.

        Args:
            change_type: Change type
            diff_content: Diff content for the function

        Returns:
            Impact score
        """
        # Count the number of added and removed lines
        added_lines = len([line for line in diff_content.split("\n") if line.startswith("+")])
        removed_lines = len([line for line in diff_content.split("\n") if line.startswith("-")])
        
        # Calculate the impact score based on the change type and the number of lines changed
        if change_type == ChangeType.ADDED:
            return 0.5 + min(added_lines / 10.0, 1.0)
        elif change_type == ChangeType.DELETED:
            return 0.7 + min(removed_lines / 10.0, 1.0)
        else:  # MODIFIED
            return 0.3 + min((added_lines + removed_lines) / 20.0, 1.0)
