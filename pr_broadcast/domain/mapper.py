"""
Domain Mapper

This module provides functionality to map functions to domains in the domain taxonomy.
"""

import os
import json
import yaml
import logging
import difflib
from typing import Dict, List, Any, Optional, Set, Tuple

from pr_broadcast.diff.models import FunctionChange

logger = logging.getLogger(__name__)


class DomainMapper:
    """
    Maps functions to domains in the domain taxonomy.
    
    This class provides methods to map function changes to domains in the domain
    taxonomy, using domain traces to establish the mapping.
    """

    def __init__(self, taxonomy_json_path: str, domain_traces_yaml_path: str):
        """
        Initialize the domain mapper.

        Args:
            taxonomy_json_path: Path to the domain taxonomy JSON file
            domain_traces_yaml_path: Path to the domain traces YAML file
        """
        self.taxonomy_json_path = taxonomy_json_path
        self.domain_traces_yaml_path = domain_traces_yaml_path
        
        # Load domain taxonomy and traces
        self.taxonomy = self._load_taxonomy()
        self.domain_traces = self._load_domain_traces()
        
        # Extract function-to-domain mappings from the taxonomy
        self.function_domains = self._extract_function_domains()
        
        logger.info(f"Loaded {len(self.function_domains)} function-to-domain mappings")

    def map_functions_to_domains(self, function_changes: List[FunctionChange]) -> Dict[str, Dict[str, Any]]:
        """
        Map function changes to domains in the taxonomy.

        Args:
            function_changes: List of function changes

        Returns:
            Dictionary mapping domains to changes
        """
        # Initialize domain changes dictionary
        domain_changes = {}
        
        # Process each function change
        for fc in function_changes:
            # Find the domain for this function
            domain = self._find_domain_for_function(fc.function_signature)
            
            if domain:
                logger.debug(f"Mapped function {fc.function_signature} to domain {domain}")
                
                # Initialize domain entry if not exists
                if domain not in domain_changes:
                    domain_changes[domain] = {
                        "functions": []
                    }
                
                # Add function change to domain
                domain_changes[domain]["functions"].append({
                    "function_name": fc.function_name,
                    "function_signature": fc.function_signature,
                    "file_path": fc.file_path,
                    "change_type": fc.change_type,
                    "diff_content": fc.diff_content,
                    "impact_score": fc.impact_score
                })
            else:
                logger.debug(f"Could not map function {fc.function_signature} to any domain")
        
        return domain_changes

    def _load_taxonomy(self) -> Dict[str, Any]:
        """
        Load the domain taxonomy from JSON.

        Returns:
            Dictionary containing domain taxonomy
        """
        try:
            with open(self.taxonomy_json_path, 'r') as f:
                taxonomy = json.load(f)
            
            logger.info(f"Loaded domain taxonomy from {self.taxonomy_json_path}")
            return taxonomy
        except Exception as e:
            logger.error(f"Failed to load domain taxonomy: {e}")
            return {}

    def _load_domain_traces(self) -> Dict[str, Any]:
        """
        Load domain traces from YAML.

        Returns:
            Dictionary containing domain traces
        """
        try:
            with open(self.domain_traces_yaml_path, 'r') as f:
                traces = yaml.safe_load(f)
            
            logger.info(f"Loaded domain traces from {self.domain_traces_yaml_path}")
            return traces
        except Exception as e:
            logger.error(f"Failed to load domain traces: {e}")
            return {}

    def _extract_function_domains(self) -> Dict[str, str]:
        """
        Extract function-to-domain mappings from the taxonomy.

        Returns:
            Dictionary mapping function signatures to domains
        """
        function_domains = {}
        
        # Function to recursively process the taxonomy
        def process_node(node, domain_path=""):
            # Get the domain name
            domain_name = node.get("name", "")
            
            # Update the domain path
            if domain_path:
                current_path = f"{domain_path} -> {domain_name}"
            else:
                current_path = domain_name
            
            # Process functions in this domain
            functions = node.get("functions", [])
            for func in functions:
                function_domains[func] = current_path
            
            # Process child domains
            children = node.get("children", [])
            for child in children:
                process_node(child, current_path)
        
        # Process the taxonomy tree
        process_node(self.taxonomy)
        
        return function_domains

    def _find_domain_for_function(self, function_signature: str) -> Optional[str]:
        """
        Find the domain for a function.

        Args:
            function_signature: Function signature (file_path:function_name)

        Returns:
            Domain path, or None if not found
        """
        # Check if the function signature is directly in the mapping
        if function_signature in self.function_domains:
            return self.function_domains[function_signature]
        
        # Try to find a close match
        return self._find_closest_match(function_signature)

    def _find_closest_match(self, function_signature: str) -> Optional[str]:
        """
        Find the closest matching function signature in the mapping.

        Args:
            function_signature: Function signature (file_path:function_name)

        Returns:
            Domain path, or None if no close match found
        """
        # Split the function signature into file path and function name
        parts = function_signature.split(":")
        if len(parts) != 2:
            return None
            
        file_name, func_name = parts
        
        # Try to match by function name
        best_match = None
        best_score = 0
        
        for sig, domain in self.function_domains.items():
            sig_parts = sig.split(":")
            if len(sig_parts) != 2:
                continue
                
            sig_file, sig_func = sig_parts
            
            # Calculate similarity scores
            file_similarity = difflib.SequenceMatcher(None, file_name, sig_file).ratio()
            func_similarity = difflib.SequenceMatcher(None, func_name, sig_func).ratio()
            
            # Weight function name similarity more heavily
            combined_score = (func_similarity * 0.7) + (file_similarity * 0.3)
            
            # Update best match if this is better
            if combined_score > best_score and combined_score > 0.6:  # Threshold
                best_score = combined_score
                best_match = domain
        
        return best_match
