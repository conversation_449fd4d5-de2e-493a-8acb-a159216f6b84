"""
Domain Taxonomy Utilities

This module provides utilities for working with domain taxonomies.
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional, Set, Tuple

logger = logging.getLogger(__name__)


class DomainTaxonomy:
    """
    Utilities for working with domain taxonomies.
    
    This class provides methods to load, query, and manipulate domain taxonomies.
    """

    def __init__(self, taxonomy_json_path: str):
        """
        Initialize the domain taxonomy utilities.

        Args:
            taxonomy_json_path: Path to the domain taxonomy JSON file
        """
        self.taxonomy_json_path = taxonomy_json_path
        
        # Load domain taxonomy
        self.taxonomy = self._load_taxonomy()
        
        # Build domain hierarchy
        self.domain_hierarchy = self._build_domain_hierarchy()
        
        # Build domain parent mapping
        self.domain_parents = self._build_domain_parent_mapping()
        
        logger.info(f"Initialized domain taxonomy from {taxonomy_json_path}")

    def get_domain_node(self, domain_path: str) -> Optional[Dict[str, Any]]:
        """
        Get a domain node from the taxonomy.

        Args:
            domain_path: Domain path (e.g., "Root -> Domain -> Subdomain")

        Returns:
            Domain node, or None if not found
        """
        if not domain_path:
            return self.taxonomy
            
        parts = domain_path.split(" -> ")
        node = self.taxonomy
        
        for part in parts:
            if "children" not in node:
                return None
                
            found = False
            for child in node["children"]:
                if child["name"] == part:
                    node = child
                    found = True
                    break
                    
            if not found:
                return None
                
        return node

    def is_leaf_domain(self, domain_path: str) -> bool:
        """
        Check if a domain is a leaf domain.

        Args:
            domain_path: Domain path (e.g., "Root -> Domain -> Subdomain")

        Returns:
            True if the domain is a leaf domain, False otherwise
        """
        node = self.get_domain_node(domain_path)
        return node and ("children" not in node or not node["children"])

    def get_parent_domain(self, domain_path: str) -> Optional[str]:
        """
        Get the parent domain of a domain.

        Args:
            domain_path: Domain path (e.g., "Root -> Domain -> Subdomain")

        Returns:
            Parent domain path, or None if the domain has no parent
        """
        if " -> " not in domain_path:
            return None
            
        parts = domain_path.split(" -> ")
        return " -> ".join(parts[:-1])

    def is_child_of(self, child_domain: str, parent_domain: str) -> bool:
        """
        Check if a domain is a child of another domain.

        Args:
            child_domain: Child domain path
            parent_domain: Parent domain path

        Returns:
            True if the child domain is a child of the parent domain, False otherwise
        """
        return child_domain.startswith(parent_domain + " -> ")

    def get_child_domains(self, parent_domain: str) -> List[str]:
        """
        Get the child domains of a domain.

        Args:
            parent_domain: Parent domain path

        Returns:
            List of child domain paths
        """
        return self.domain_hierarchy.get(parent_domain, [])

    def _load_taxonomy(self) -> Dict[str, Any]:
        """
        Load the domain taxonomy from JSON.

        Returns:
            Dictionary containing domain taxonomy
        """
        try:
            with open(self.taxonomy_json_path, 'r') as f:
                taxonomy = json.load(f)
            
            logger.info(f"Loaded domain taxonomy from {self.taxonomy_json_path}")
            return taxonomy
        except Exception as e:
            logger.error(f"Failed to load domain taxonomy: {e}")
            return {}

    def _build_domain_hierarchy(self) -> Dict[str, List[str]]:
        """
        Build a mapping from parent domains to child domains.

        Returns:
            Dictionary mapping parent domains to lists of child domains
        """
        domain_hierarchy = {}
        
        # Function to recursively process the taxonomy
        def process_node(node, domain_path=""):
            # Get the domain name
            domain_name = node.get("name", "")
            
            # Update the domain path
            if domain_path:
                current_path = f"{domain_path} -> {domain_name}"
            else:
                current_path = domain_name
            
            # Process child domains
            children = node.get("children", [])
            
            # Add child domains to the hierarchy
            if children:
                domain_hierarchy[current_path] = []
                
                for child in children:
                    child_name = child.get("name", "")
                    child_path = f"{current_path} -> {child_name}"
                    domain_hierarchy[current_path].append(child_path)
                    
                    # Process the child node
                    process_node(child, current_path)
        
        # Process the taxonomy tree
        process_node(self.taxonomy)
        
        logger.info(f"Built domain hierarchy with {len(domain_hierarchy)} parent domains")
        return domain_hierarchy

    def _build_domain_parent_mapping(self) -> Dict[str, str]:
        """
        Build a mapping from domains to their parent domains.

        Returns:
            Dictionary mapping domains to their parent domains
        """
        domain_parents = {}
        
        # Invert the hierarchy mapping
        for parent, children in self.domain_hierarchy.items():
            for child in children:
                domain_parents[child] = parent
        
        logger.info(f"Built domain parent mapping with {len(domain_parents)} child domains")
        return domain_parents
