{"id": 12345, "iid": 789, "project_id": 54321, "title": "Enhance Logger System with Better Progress Tracking and Error Handling", "description": "This MR improves the logger system by:\n\n1. Refactoring the logger factory to use if/elif instead of match/case for better compatibility\n2. Adding prefix customization via kwargs\n3. Improving progress tracking with percent calculation\n4. Enhancing error, warning, and success messages with bold formatting\n5. Using monotonic time for more reliable debounced refreshes\n6. Adding cursor hiding for better terminal UX\n7. Clarifying documentation for log methods", "state": "opened", "created_at": "2023-10-15T14:30:00.000Z", "updated_at": "2023-10-15T16:45:00.000Z", "target_branch": "main", "source_branch": "feature/enhanced-logger", "author": {"id": 101, "name": "<PERSON>", "username": "jane_dev", "email": "<EMAIL>"}, "assignee": {"id": 102, "name": "<PERSON>", "username": "john_rev", "email": "<EMAIL>"}, "labels": ["enhancement", "logger", "core"], "web_url": "https://gitlab.example.com/bracket/bracket_prod/-/merge_requests/789", "changes_count": 4, "user_notes_count": 3, "merge_status": "can_be_merged"}