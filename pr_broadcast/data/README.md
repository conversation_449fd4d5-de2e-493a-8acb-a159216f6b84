# PR Broadcast Test Data

This directory contains test data for the PR Broadcast system.

## Files

- `logger_diff.json` - Contains diffs for logger system changes
- `logger_mr_details.json` - Contains MR details for the logger changes
- `formatted_logger_diff.json` - Combined file with both diffs and MR details for logger changes
- `dummy_data.json` - Simple test data for PR Broadcast

## Running Tests

### Using the Combined File

```bash
python test_pr_broadcast.py \
  --taxonomy-json /path/to/domain_taxonomy.json \
  --domain-traces-yaml /path/to/domain_traces.yaml \
  --dummy-data pr_broadcast/data/formatted_logger_diff.json \
  --output-dir pr_broadcast_output \
  --include-indirect-impact
```

### Using Separate Files

```bash
python -m pr_broadcast.cli \
  --taxonomy-json /path/to/domain_taxonomy.json \
  --domain-traces-yaml /path/to/domain_traces.yaml \
  --output-dir pr_broadcast_output \
  --mr-details pr_broadcast/data/logger_mr_details.json \
  --local-diffs pr_broadcast/data/logger_diff.json \
  --include-indirect-impact
```

### Using LLM for Diagram Generation

Add the `--use-llm-diagrams` and `--llm-api-key` options to use LLM for diagram generation:

```bash
python test_pr_broadcast.py \
  --taxonomy-json /path/to/domain_taxonomy.json \
  --domain-traces-yaml /path/to/domain_traces.yaml \
  --dummy-data pr_broadcast/data/formatted_logger_diff.json \
  --output-dir pr_broadcast_output \
  --include-indirect-impact \
  --use-llm-diagrams \
  --llm-api-key YOUR_LLM_API_KEY
```

### Opening the HTML Report

Add the `--open-browser` option to automatically open the HTML report in your browser:

```bash
python test_pr_broadcast.py \
  --taxonomy-json /path/to/domain_taxonomy.json \
  --domain-traces-yaml /path/to/domain_traces.yaml \
  --dummy-data pr_broadcast/data/formatted_logger_diff.json \
  --output-dir pr_broadcast_output \
  --include-indirect-impact \
  --open-browser
```
