"""
PR Broadcast Module

This module provides functionality to analyze PR/MR diffs, map changes to domains,
and generate specialized Mermaid diagrams that visualize the impact of changes.

Components:
1. Diff Analysis - Fetches and analyzes PR/MR diffs to identify changed functions
2. Domain Impact Mapping - Maps changed functions to domains and analyzes impact
3. Diagram Generation - Creates specialized Mermaid diagrams to visualize changes
"""

from pr_broadcast.service import PRBroadcastService
from pr_broadcast.diff.models import FunctionChange, ChangeType
from pr_broadcast.diff.analyzer import FunctionDiff<PERSON>nalyzer, EnhancedFunctionDiffAnalyzer
from pr_broadcast.diff.gitlab import GitlabDiffFetcher
from pr_broadcast.domain.mapper import DomainMapper
from pr_broadcast.domain.taxonomy import DomainTaxonomy
from pr_broadcast.impact.analyzer import ImpactAnalyzer
from pr_broadcast.impact.propagator import ImpactPropagator
from pr_broadcast.diagram.generator import DiagramGenerator
from pr_broadcast.diagram.llm import LLMDiagramGenerator
from pr_broadcast.output.manager import OutputManager

__all__ = [
    'PRBroadcastService',
    'FunctionChange',
    'ChangeType',
    'FunctionDiffAnalyzer',
    'EnhancedFunctionDiffAnalyzer',
    'GitlabDiffFetcher',
    'DomainMapper',
    'DomainTaxonomy',
    'ImpactAnalyzer',
    'ImpactPropagator',
    'DiagramGenerator',
    'LLMDiagramGenerator',
    'OutputManager'
]
