"""
Domain Mapper

This module provides functionality to map function changes to domains in the domain taxonomy.
"""

import os
import json
import yaml
import logging
import difflib
from typing import Dict, List, Any, Optional, Set, Tuple

from pr_broadcast.diff_analysis.function_diff_analyzer import FunctionChange

logger = logging.getLogger(__name__)

class DomainMapper:
    """
    Maps function changes to domains in the domain taxonomy.
    
    Key features:
    1. Maps function signatures to domains
    2. Supports partial matching for function names
    3. Groups function changes by domain
    """

    def __init__(self, taxonomy_json_path: str, domain_traces_yaml_path: str):
        """
        Initialize the domain mapper.

        Args:
            taxonomy_json_path: Path to the domain taxonomy JSON file
            domain_traces_yaml_path: Path to the domain traces YAML file
        """
        self.taxonomy_json_path = taxonomy_json_path
        self.domain_traces_yaml_path = domain_traces_yaml_path
        
        # Load domain taxonomy and traces
        self.taxonomy = self._load_taxonomy()
        self.domain_traces = self._load_domain_traces()
        
        # Extract function-to-domain mappings from the taxonomy
        self.function_domains = self._extract_function_domains()
        
        logger.info(f"Loaded {len(self.function_domains)} function-to-domain mappings")

    def map_functions_to_domains(self, function_changes: List[FunctionChange]) -> Dict[str, Dict[str, Any]]:
        """
        Map function changes to domains in the taxonomy.

        Args:
            function_changes: List of function changes

        Returns:
            Dictionary mapping domains to changes
        """
        domain_changes = {}
        
        # Process each function change
        for fc in function_changes:
            # Find the domain for this function
            domain = self._find_domain_for_function(fc.function_signature)
            
            if domain:
                logger.debug(f"Mapped function {fc.function_signature} to domain {domain}")
                
                # Initialize domain entry if not exists
                if domain not in domain_changes:
                    domain_changes[domain] = {
                        "functions": []
                    }
                
                # Add function change to domain
                domain_changes[domain]["functions"].append({
                    "function_name": fc.function_name,
                    "function_signature": fc.function_signature,
                    "file_path": fc.file_path,
                    "change_type": fc.change_type,
                    "diff_content": fc.diff_content,
                    "impact_score": fc.impact_score
                })
            else:
                logger.debug(f"Could not map function {fc.function_signature} to any domain")
        
        return domain_changes

    def _find_domain_for_function(self, function_signature: str) -> Optional[str]:
        """
        Find the domain for a function signature.

        Args:
            function_signature: Function signature in the format "file_path:Class.method"

        Returns:
            Domain name if found, None otherwise
        """
        # Check for exact match
        if function_signature in self.function_domains:
            return self.function_domains[function_signature]
            
        # Try partial matching
        return self._partial_match_function(function_signature)

    def _partial_match_function(self, function_signature: str) -> Optional[str]:
        """
        Find the domain for a function signature using partial matching.

        Args:
            function_signature: Function signature in the format "file_path:Class.method"

        Returns:
            Domain name if found, None otherwise
        """
        # Extract parts from the function signature
        parts = function_signature.split(":")
        if len(parts) != 2:
            return None
            
        file_name, func_name = parts
        
        # Try to match by function name
        best_match = None
        best_score = 0
        
        for sig, domain in self.function_domains.items():
            sig_parts = sig.split(":")
            if len(sig_parts) != 2:
                continue
                
            sig_file, sig_func = sig_parts
            
            # Calculate similarity scores
            file_similarity = difflib.SequenceMatcher(None, file_name, sig_file).ratio()
            func_similarity = difflib.SequenceMatcher(None, func_name, sig_func).ratio()
            
            # Weight function name similarity more heavily
            combined_score = (func_similarity * 0.7) + (file_similarity * 0.3)
            
            # Update best match if this is better
            if combined_score > best_score and combined_score > 0.6:  # Threshold
                best_score = combined_score
                best_match = domain
        
        return best_match

    def _extract_function_domains(self) -> Dict[str, str]:
        """
        Extract function-to-domain mappings from the taxonomy.

        Returns:
            Dictionary mapping function signatures to domain names
        """
        function_domains = {}
        
        # Process each leaf domain in the taxonomy
        self._process_domain_node(self.taxonomy, "", function_domains)
        
        return function_domains

    def _process_domain_node(
        self,
        node: Dict[str, Any],
        path: str,
        function_domains: Dict[str, str]
    ) -> None:
        """
        Process a domain node to extract function-to-domain mappings.

        Args:
            node: Domain node
            path: Path to the domain
            function_domains: Dictionary to populate with function-to-domain mappings
        """
        # Get the domain name
        domain_name = node.get("name", "")
        
        # Update the path
        if path:
            full_path = f"{path} -> {domain_name}"
        else:
            full_path = domain_name
            
        # If this is a leaf node (has functions), add mappings
        if "functions" in node:
            for func in node["functions"]:
                function_domains[func] = full_path
                
        # Process child nodes
        if "children" in node:
            for child in node["children"]:
                self._process_domain_node(child, full_path, function_domains)

    def _load_taxonomy(self) -> Dict[str, Any]:
        """
        Load the domain taxonomy from JSON.

        Returns:
            Dictionary containing domain taxonomy
        """
        try:
            with open(self.taxonomy_json_path, 'r') as f:
                taxonomy = json.load(f)
            
            logger.info(f"Loaded domain taxonomy from {self.taxonomy_json_path}")
            return taxonomy
        except Exception as e:
            logger.error(f"Failed to load domain taxonomy: {e}")
            return {}

    def _load_domain_traces(self) -> Dict[str, Any]:
        """
        Load the domain traces from YAML.

        Returns:
            Dictionary containing domain traces
        """
        try:
            with open(self.domain_traces_yaml_path, 'r') as f:
                traces = yaml.safe_load(f)
            
            logger.info(f"Loaded domain traces from {self.domain_traces_yaml_path}")
            return traces
        except Exception as e:
            logger.error(f"Failed to load domain traces: {e}")
            return {}
