"""
GitLab Diff Fetcher

This module provides functionality to fetch PR/MR diffs from GitLab.
"""

import logging
import aiohttp
from typing import Dict, List, Any, Tuple, Optional

logger = logging.getLogger(__name__)

class GitlabDiffFetcher:
    """
    Fetches PR/MR diffs from GitLab.
    """

    def __init__(self, gitlab_url: str, gitlab_token: str):
        """
        Initialize the GitLab diff fetcher.

        Args:
            gitlab_url: URL of the GitLab instance
            gitlab_token: GitLab API token
        """
        self.gitlab_url = gitlab_url.rstrip('/')
        self.gitlab_token = gitlab_token
        self.headers = {
            'Private-Token': gitlab_token,
            'Content-Type': 'application/json'
        }

    async def fetch_merge_request(
        self,
        project_id: str,
        mr_iid: int
    ) -> Tuple[Dict[str, Any], List[Dict[str, Any]]]:
        """
        Fetch a merge request and its diffs from GitLab.

        Args:
            project_id: GitLab project ID
            mr_iid: Merge request IID (internal ID)

        Returns:
            Tuple containing merge request details and diffs
        """
        # Fetch merge request details
        mr_details = await self._fetch_mr_details(project_id, mr_iid)
        
        # Fetch merge request diffs
        diffs = await self._fetch_mr_diffs(project_id, mr_iid)
        
        return mr_details, diffs

    async def _fetch_mr_details(self, project_id: str, mr_iid: int) -> Dict[str, Any]:
        """
        Fetch merge request details from GitLab.

        Args:
            project_id: GitLab project ID
            mr_iid: Merge request IID (internal ID)

        Returns:
            Dictionary containing merge request details
        """
        url = f"{self.gitlab_url}/api/v4/projects/{project_id}/merge_requests/{mr_iid}"
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=self.headers) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"Failed to fetch merge request details: {error_text}")
                
                mr_details = await response.json()
                
                logger.info(f"Fetched details for merge request {mr_iid}")
                return mr_details

    async def _fetch_mr_diffs(self, project_id: str, mr_iid: int) -> List[Dict[str, Any]]:
        """
        Fetch merge request diffs from GitLab.

        Args:
            project_id: GitLab project ID
            mr_iid: Merge request IID (internal ID)

        Returns:
            List of dictionaries containing diff information
        """
        url = f"{self.gitlab_url}/api/v4/projects/{project_id}/merge_requests/{mr_iid}/changes"
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=self.headers) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"Failed to fetch merge request diffs: {error_text}")
                
                changes = await response.json()
                
                logger.info(f"Fetched {len(changes.get('changes', []))} diffs for merge request {mr_iid}")
                return changes.get('changes', [])

    async def fetch_file_content(
        self,
        project_id: str,
        file_path: str,
        ref: str
    ) -> Optional[str]:
        """
        Fetch file content from GitLab.

        Args:
            project_id: GitLab project ID
            file_path: Path to the file
            ref: Git reference (branch, tag, or commit SHA)

        Returns:
            File content as string, or None if the file doesn't exist
        """
        url = f"{self.gitlab_url}/api/v4/projects/{project_id}/repository/files/{file_path}/raw"
        params = {'ref': ref}
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=self.headers, params=params) as response:
                if response.status == 404:
                    logger.warning(f"File {file_path} not found at ref {ref}")
                    return None
                
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"Failed to fetch file content: {error_text}")
                
                content = await response.text()
                
                logger.info(f"Fetched content for file {file_path} at ref {ref}")
                return content
