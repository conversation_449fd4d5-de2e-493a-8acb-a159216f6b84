"""
Function Diff Analyzer

This module provides functionality to analyze diffs and identify changed functions.
"""

import re
import logging
import difflib
from enum import Enum
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Set, Tuple

logger = logging.getLogger(__name__)

class ChangeType(str, Enum):
    """Enum representing types of function changes."""
    ADDED = "added"
    MODIFIED = "modified"
    DELETED = "deleted"

@dataclass
class FunctionChange:
    """Represents a change to a function."""
    file_path: str
    function_name: str
    function_signature: str
    change_type: str
    old_content: Optional[str] = None
    new_content: Optional[str] = None
    diff_content: Optional[str] = None
    impact_score: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "file_path": self.file_path,
            "function_name": self.function_name,
            "function_signature": self.function_signature,
            "change_type": self.change_type,
            "old_content": self.old_content,
            "new_content": self.new_content,
            "diff_content": self.diff_content,
            "impact_score": self.impact_score
        }

class FunctionDiffAnalyzer:
    """
    Analyzes diffs to identify changed functions.
    """

    # Regular expressions for different languages
    FUNCTION_PATTERNS = {
        'py': r'def\s+([a-zA-Z0-9_]+)\s*\([^)]*\)(?:\s*->.*?)?:',
        'rb': r'def\s+([a-zA-Z0-9_?!]+)(?:\s*\([^)]*\))?',
        'js': r'(?:function\s+([a-zA-Z0-9_$]+)|(?:const|let|var)\s+([a-zA-Z0-9_$]+)\s*=\s*(?:async\s*)?(?:function|\([^)]*\)\s*=>))',
        'ts': r'(?:function\s+([a-zA-Z0-9_$]+)|(?:const|let|var)\s+([a-zA-Z0-9_$]+)\s*=\s*(?:async\s*)?(?:function|\([^)]*\)\s*=>)|(?:(?:public|private|protected)?\s*(?:async\s*)?([a-zA-Z0-9_$]+)\s*\([^)]*\)))',
        'java': r'(?:public|private|protected|static|\s)+[\w\<\>\[\]]+\s+([a-zA-Z0-9_]+)\s*\([^)]*\)\s*(?:throws\s+[\w\s,]+)?\s*\{',
        'cpp': r'(?:[\w\<\>\[\]]+\s+)+([a-zA-Z0-9_]+)\s*\([^)]*\)\s*(?:const)?\s*(?:noexcept)?\s*(?:override)?\s*(?:final)?\s*(?:=\s*0)?\s*(?:=\s*default)?\s*(?:=\s*delete)?\s*\{',
        'go': r'func\s+(?:\([^)]*\)\s*)?([a-zA-Z0-9_]+)\s*\([^)]*\)\s*(?:\([^)]*\))?\s*\{',
    }

    def __init__(self):
        """Initialize the function diff analyzer."""
        pass

    def analyze_diff(self, diff: Dict[str, Any]) -> List[FunctionChange]:
        """
        Analyze a diff to identify changed functions.

        Args:
            diff: Dictionary containing diff information

        Returns:
            List of FunctionChange objects
        """
        file_path = diff.get('new_path', diff.get('old_path', ''))
        
        # Skip non-code files
        if not self._is_code_file(file_path):
            logger.debug(f"Skipping non-code file: {file_path}")
            return []
        
        # Get file extension to determine language
        file_ext = self._get_file_extension(file_path)
        if file_ext not in self.FUNCTION_PATTERNS:
            logger.debug(f"Unsupported file type: {file_path}")
            return []
        
        # Extract function changes
        if diff.get('new_file', False):
            # New file - all functions are added
            return self._extract_added_functions(diff, file_ext)
        elif diff.get('deleted_file', False):
            # Deleted file - all functions are deleted
            return self._extract_deleted_functions(diff, file_ext)
        else:
            # Modified file - need to identify added, modified, and deleted functions
            return self._extract_modified_functions(diff, file_ext)

    def _is_code_file(self, file_path: str) -> bool:
        """
        Check if a file is a code file.

        Args:
            file_path: Path to the file

        Returns:
            True if the file is a code file, False otherwise
        """
        code_extensions = ['.py', '.rb', '.js', '.ts', '.java', '.cpp', '.go']
        _, ext = os.path.splitext(file_path)
        return ext.lower() in code_extensions

    def _get_file_extension(self, file_path: str) -> str:
        """
        Get the file extension.

        Args:
            file_path: Path to the file

        Returns:
            File extension without the dot
        """
        _, ext = os.path.splitext(file_path)
        return ext.lower()[1:]  # Remove the dot

    def _extract_added_functions(
        self,
        diff: Dict[str, Any],
        file_ext: str
    ) -> List[FunctionChange]:
        """
        Extract added functions from a new file.

        Args:
            diff: Dictionary containing diff information
            file_ext: File extension

        Returns:
            List of FunctionChange objects
        """
        file_path = diff.get('new_path', '')
        content = diff.get('diff', '')
        
        # Extract only added lines (starting with +)
        added_lines = []
        for line in content.split('\n'):
            if line.startswith('+') and not line.startswith('+++'):
                added_lines.append(line[1:])  # Remove the + prefix
        
        new_content = '\n'.join(added_lines)
        
        # Extract functions from the new content
        functions = self._extract_functions(new_content, file_ext)
        
        # Create FunctionChange objects
        changes = []
        for func_name, func_signature, func_content in functions:
            changes.append(FunctionChange(
                file_path=file_path,
                function_name=func_name,
                function_signature=func_signature,
                change_type=ChangeType.ADDED,
                old_content=None,
                new_content=func_content,
                diff_content=func_content,
                impact_score=self._calculate_impact_score(func_content, ChangeType.ADDED)
            ))
        
        return changes

    def _extract_deleted_functions(
        self,
        diff: Dict[str, Any],
        file_ext: str
    ) -> List[FunctionChange]:
        """
        Extract deleted functions from a deleted file.

        Args:
            diff: Dictionary containing diff information
            file_ext: File extension

        Returns:
            List of FunctionChange objects
        """
        file_path = diff.get('old_path', '')
        content = diff.get('diff', '')
        
        # Extract only deleted lines (starting with -)
        deleted_lines = []
        for line in content.split('\n'):
            if line.startswith('-') and not line.startswith('---'):
                deleted_lines.append(line[1:])  # Remove the - prefix
        
        old_content = '\n'.join(deleted_lines)
        
        # Extract functions from the old content
        functions = self._extract_functions(old_content, file_ext)
        
        # Create FunctionChange objects
        changes = []
        for func_name, func_signature, func_content in functions:
            changes.append(FunctionChange(
                file_path=file_path,
                function_name=func_name,
                function_signature=func_signature,
                change_type=ChangeType.DELETED,
                old_content=func_content,
                new_content=None,
                diff_content=func_content,
                impact_score=self._calculate_impact_score(func_content, ChangeType.DELETED)
            ))
        
        return changes

    def _extract_modified_functions(
        self,
        diff: Dict[str, Any],
        file_ext: str
    ) -> List[FunctionChange]:
        """
        Extract modified functions from a modified file.

        Args:
            diff: Dictionary containing diff information
            file_ext: File extension

        Returns:
            List of FunctionChange objects
        """
        file_path = diff.get('new_path', '')
        content = diff.get('diff', '')
        
        # Split the diff into chunks
        chunks = self._split_diff_into_chunks(content)
        
        # Process each chunk to identify function changes
        changes = []
        for chunk in chunks:
            # Extract old and new content from the chunk
            old_content, new_content = self._extract_old_and_new_content(chunk)
            
            # Extract functions from old and new content
            old_functions = self._extract_functions(old_content, file_ext)
            new_functions = self._extract_functions(new_content, file_ext)
            
            # Map functions by name
            old_funcs_map = {name: (signature, content) for name, signature, content in old_functions}
            new_funcs_map = {name: (signature, content) for name, signature, content in new_functions}
            
            # Identify added, modified, and deleted functions
            added_funcs = set(new_funcs_map.keys()) - set(old_funcs_map.keys())
            deleted_funcs = set(old_funcs_map.keys()) - set(new_funcs_map.keys())
            common_funcs = set(old_funcs_map.keys()) & set(new_funcs_map.keys())
            
            # Create FunctionChange objects for added functions
            for func_name in added_funcs:
                signature, content = new_funcs_map[func_name]
                changes.append(FunctionChange(
                    file_path=file_path,
                    function_name=func_name,
                    function_signature=signature,
                    change_type=ChangeType.ADDED,
                    old_content=None,
                    new_content=content,
                    diff_content=content,
                    impact_score=self._calculate_impact_score(content, ChangeType.ADDED)
                ))
            
            # Create FunctionChange objects for deleted functions
            for func_name in deleted_funcs:
                signature, content = old_funcs_map[func_name]
                changes.append(FunctionChange(
                    file_path=file_path,
                    function_name=func_name,
                    function_signature=signature,
                    change_type=ChangeType.DELETED,
                    old_content=content,
                    new_content=None,
                    diff_content=content,
                    impact_score=self._calculate_impact_score(content, ChangeType.DELETED)
                ))
            
            # Create FunctionChange objects for modified functions
            for func_name in common_funcs:
                old_signature, old_content = old_funcs_map[func_name]
                new_signature, new_content = new_funcs_map[func_name]
                
                # Check if the function was actually modified
                if old_content != new_content or old_signature != new_signature:
                    # Generate a unified diff
                    diff_content = self._generate_unified_diff(old_content, new_content, func_name)
                    
                    changes.append(FunctionChange(
                        file_path=file_path,
                        function_name=func_name,
                        function_signature=new_signature,
                        change_type=ChangeType.MODIFIED,
                        old_content=old_content,
                        new_content=new_content,
                        diff_content=diff_content,
                        impact_score=self._calculate_impact_score(diff_content, ChangeType.MODIFIED)
                    ))
        
        return changes

    def _split_diff_into_chunks(self, diff_content: str) -> List[str]:
        """
        Split a diff into chunks.

        Args:
            diff_content: Diff content

        Returns:
            List of diff chunks
        """
        chunks = []
        current_chunk = []
        
        for line in diff_content.split('\n'):
            if line.startswith('@@'):
                # Start of a new chunk
                if current_chunk:
                    chunks.append('\n'.join(current_chunk))
                current_chunk = [line]
            elif current_chunk:
                current_chunk.append(line)
        
        # Add the last chunk
        if current_chunk:
            chunks.append('\n'.join(current_chunk))
        
        return chunks

    def _extract_old_and_new_content(self, chunk: str) -> Tuple[str, str]:
        """
        Extract old and new content from a diff chunk.

        Args:
            chunk: Diff chunk

        Returns:
            Tuple containing old and new content
        """
        old_lines = []
        new_lines = []
        
        for line in chunk.split('\n'):
            if line.startswith('-') and not line.startswith('---'):
                old_lines.append(line[1:])  # Remove the - prefix
            elif line.startswith('+') and not line.startswith('+++'):
                new_lines.append(line[1:])  # Remove the + prefix
            elif not line.startswith('@@'):
                # Context line - add to both old and new content
                old_lines.append(line)
                new_lines.append(line)
        
        return '\n'.join(old_lines), '\n'.join(new_lines)

    def _extract_functions(
        self,
        content: str,
        file_ext: str
    ) -> List[Tuple[str, str, str]]:
        """
        Extract functions from file content.

        Args:
            content: File content
            file_ext: File extension

        Returns:
            List of tuples containing function name, signature, and content
        """
        if file_ext not in self.FUNCTION_PATTERNS:
            return []
        
        pattern = self.FUNCTION_PATTERNS[file_ext]
        functions = []
        
        # Find all function definitions
        matches = re.finditer(pattern, content, re.MULTILINE)
        
        for match in matches:
            # Get the function name
            func_name = None
            for group in match.groups():
                if group:
                    func_name = group
                    break
            
            if not func_name:
                continue
            
            # Get the function signature (the line containing the function definition)
            start_pos = match.start()
            end_pos = content.find('\n', start_pos)
            if end_pos == -1:
                end_pos = len(content)
            
            signature = content[start_pos:end_pos].strip()
            
            # Get the function content (including the signature)
            # This is a simplified approach - a more robust solution would parse the code
            # to correctly identify the function body with proper indentation handling
            func_content = self._extract_function_content(content, start_pos, file_ext)
            
            functions.append((func_name, signature, func_content))
        
        return functions

    def _extract_function_content(
        self,
        content: str,
        start_pos: int,
        file_ext: str
    ) -> str:
        """
        Extract function content from file content.

        Args:
            content: File content
            start_pos: Start position of the function
            file_ext: File extension

        Returns:
            Function content
        """
        # This is a simplified approach - a more robust solution would parse the code
        # to correctly identify the function body with proper indentation handling
        
        # Find the end of the line containing the function definition
        end_of_line = content.find('\n', start_pos)
        if end_of_line == -1:
            return content[start_pos:]
        
        # Get the indentation of the first line after the function definition
        next_line_start = end_of_line + 1
        if next_line_start >= len(content):
            return content[start_pos:end_of_line]
        
        # Find the indentation level of the first line of the function body
        next_line = content[next_line_start:content.find('\n', next_line_start) if content.find('\n', next_line_start) != -1 else len(content)]
        indentation_match = re.match(r'^(\s+)', next_line)
        
        if not indentation_match:
            # No indentation found - return just the function signature
            return content[start_pos:end_of_line]
        
        base_indentation = indentation_match.group(1)
        
        # Find the end of the function by looking for a line with the same or less indentation
        current_pos = next_line_start
        while current_pos < len(content):
            line_end = content.find('\n', current_pos)
            if line_end == -1:
                line_end = len(content)
            
            line = content[current_pos:line_end]
            
            # Skip empty lines
            if not line.strip():
                current_pos = line_end + 1
                continue
            
            # Check if this line has less indentation than the function body
            if not line.startswith(base_indentation):
                break
            
            current_pos = line_end + 1
        
        return content[start_pos:current_pos]

    def _generate_unified_diff(
        self,
        old_content: str,
        new_content: str,
        func_name: str
    ) -> str:
        """
        Generate a unified diff between old and new function content.

        Args:
            old_content: Old function content
            new_content: New function content
            func_name: Function name

        Returns:
            Unified diff
        """
        old_lines = old_content.split('\n')
        new_lines = new_content.split('\n')
        
        diff = difflib.unified_diff(
            old_lines,
            new_lines,
            fromfile=f'a/{func_name}',
            tofile=f'b/{func_name}',
            lineterm=''
        )
        
        return '\n'.join(diff)

    def _calculate_impact_score(self, content: str, change_type: str) -> float:
        """
        Calculate an impact score for a function change.

        Args:
            content: Function content or diff
            change_type: Type of change

        Returns:
            Impact score between 0.0 and 1.0
        """
        # This is a simplified approach - a more sophisticated approach would
        # analyze the nature of the changes, the function's complexity, etc.
        
        # Count the number of lines
        lines = content.split('\n')
        num_lines = len(lines)
        
        # Base score based on change type
        base_score = {
            ChangeType.ADDED: 0.5,
            ChangeType.MODIFIED: 0.3,
            ChangeType.DELETED: 0.7
        }.get(change_type, 0.3)
        
        # Adjust score based on function size
        size_factor = min(1.0, num_lines / 50.0)  # Cap at 50 lines
        
        # Count the number of changed lines in a diff
        if change_type == ChangeType.MODIFIED:
            changed_lines = sum(1 for line in lines if line.startswith('+') or line.startswith('-'))
            change_ratio = changed_lines / max(1, num_lines)
            
            # Adjust score based on the ratio of changed lines
            return base_score + (change_ratio * 0.5)
        
        return base_score + (size_factor * 0.5)

import os  # Added import for _is_code_file method
