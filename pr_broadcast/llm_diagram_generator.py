"""
LLM Diagram Generator

This module provides functionality to generate Mermaid diagrams using LLM to visualize
the impact of changes on domains.
"""

import os
import json
import logging
import asyncio
import aiohttp
from typing import Dict, List, Any, Optional, Set, Tuple

logger = logging.getLogger(__name__)

class LLMDiagramGenerator:
    """
    Generates Mermaid diagrams using LLM to visualize the impact of changes on domains.
    
    Key features:
    1. Extracts domain diagrams from the taxonomy
    2. Combines with function changes
    3. Uses LLM to generate updated diagrams
    4. Propagates changes upward through the hierarchy
    """

    def __init__(
        self,
        taxonomy_json_path: str,
        llm_api_key: Optional[str] = None,
        llm_model: str = "gpt-4o-mini"
    ):
        """
        Initialize the LLM diagram generator.

        Args:
            taxonomy_json_path: Path to the domain taxonomy JSON file
            llm_api_key: API key for the LLM service (optional)
            llm_model: LLM model to use for diagram generation
        """
        self.taxonomy_json_path = taxonomy_json_path
        self.llm_api_key = llm_api_key or os.environ.get("OPENAI_API_KEY")
        self.llm_model = llm_model
        
        # Load domain taxonomy
        self.taxonomy = self._load_taxonomy()
        
        logger.info(f"Initialized LLM diagram generator with model {llm_model}")

    async def generate_leaf_domain_diagram(
        self,
        domain: str,
        original_diagram: str,
        function_changes: List[Dict[str, Any]]
    ) -> str:
        """
        Generate a Mermaid diagram for a leaf domain showing the impact of changes.

        Args:
            domain: Domain name
            original_diagram: Original Mermaid diagram for the domain
            function_changes: List of function changes in the domain

        Returns:
            Updated Mermaid diagram as a string
        """
        # Prepare the prompt for the LLM
        prompt = self._prepare_leaf_domain_prompt(domain, original_diagram, function_changes)
        
        # Call the LLM to generate the diagram
        response = await self._call_llm(prompt)
        
        # Extract the Mermaid diagram from the response
        diagram = self._extract_mermaid_from_response(response)
        
        return diagram

    async def generate_parent_domain_diagram(
        self,
        domain: str,
        original_diagram: str,
        child_diagrams: Dict[str, Dict[str, Any]]
    ) -> str:
        """
        Generate a Mermaid diagram for a parent domain showing the impact of changes.

        Args:
            domain: Domain name
            original_diagram: Original Mermaid diagram for the domain
            child_diagrams: Dictionary mapping child domains to diagram information

        Returns:
            Updated Mermaid diagram as a string
        """
        # Prepare the prompt for the LLM
        prompt = self._prepare_parent_domain_prompt(domain, original_diagram, child_diagrams)
        
        # Call the LLM to generate the diagram
        response = await self._call_llm(prompt)
        
        # Extract the Mermaid diagram from the response
        diagram = self._extract_mermaid_from_response(response)
        
        return diagram

    def _prepare_leaf_domain_prompt(
        self,
        domain: str,
        original_diagram: str,
        function_changes: List[Dict[str, Any]]
    ) -> str:
        """
        Prepare a prompt for generating a leaf domain diagram.

        Args:
            domain: Domain name
            original_diagram: Original Mermaid diagram for the domain
            function_changes: List of function changes in the domain

        Returns:
            Prompt for the LLM
        """
        # Format function changes for the prompt
        formatted_changes = []
        for fc in function_changes:
            change_type = fc["change_type"]
            function_name = fc["function_name"]
            diff_content = fc["diff_content"]
            
            formatted_changes.append(f"Function: {function_name}")
            formatted_changes.append(f"Change Type: {change_type}")
            formatted_changes.append(f"Diff Content:\n```diff\n{diff_content}\n```")
            formatted_changes.append("")
        
        # Build the prompt
        prompt = f"""
You are an expert in software architecture and visualization. Your task is to update a Mermaid diagram
for a domain to show the impact of code changes.

Domain: {domain}

Original Mermaid Diagram:
```mermaid
{original_diagram}
```

Function Changes:
{formatted_changes}

Please generate an updated Mermaid diagram that:
1. Shows the impact of these changes on the domain
2. Uses color coding to highlight added, modified, and deleted functions
3. Maintains the overall structure of the original diagram
4. Adds new nodes or edges as needed to represent the changes
5. Uses the following color scheme:
   - Added functions: Green (#90EE90)
   - Modified functions: Yellow (#FFD700)
   - Deleted functions: Red (#FF5252)
   - Unchanged elements: Keep original colors

Return ONLY the updated Mermaid diagram code, without any additional explanation.
The diagram should be enclosed in ```mermaid and ``` tags.
"""
        
        return prompt

    def _prepare_parent_domain_prompt(
        self,
        domain: str,
        original_diagram: str,
        child_diagrams: Dict[str, Dict[str, Any]]
    ) -> str:
        """
        Prepare a prompt for generating a parent domain diagram.

        Args:
            domain: Domain name
            original_diagram: Original Mermaid diagram for the domain
            child_diagrams: Dictionary mapping child domains to diagram information

        Returns:
            Prompt for the LLM
        """
        # Format child diagrams for the prompt
        formatted_children = []
        for child_domain, child_info in child_diagrams.items():
            child_name = child_domain.split(" -> ")[-1]
            impact_diagram = child_info["impact_diagram"]
            changes = child_info["changes"]
            
            formatted_children.append(f"Child Domain: {child_name}")
            formatted_children.append(f"Number of Changed Functions: {len(changes['functions'])}")
            formatted_children.append(f"Impact Diagram:\n```mermaid\n{impact_diagram}\n```")
            formatted_children.append("")
        
        # Build the prompt
        prompt = f"""
You are an expert in software architecture and visualization. Your task is to update a Mermaid diagram
for a parent domain to show the propagated impact of code changes from its child domains.

Parent Domain: {domain}

Original Mermaid Diagram:
```mermaid
{original_diagram}
```

Child Domain Changes:
{formatted_children}

Please generate an updated Mermaid diagram that:
1. Shows the propagated impact of changes from child domains
2. Uses color coding to highlight affected components
3. Maintains the overall structure of the original diagram
4. Uses the following color scheme:
   - High impact: Red (#FF5252)
   - Medium impact: Yellow (#FFD700)
   - Low impact: Green (#90EE90)
   - Unchanged elements: Keep original colors

Return ONLY the updated Mermaid diagram code, without any additional explanation.
The diagram should be enclosed in ```mermaid and ``` tags.
"""
        
        return prompt

    async def _call_llm(self, prompt: str) -> str:
        """
        Call the LLM to generate a response.

        Args:
            prompt: Prompt for the LLM

        Returns:
            LLM response as a string
        """
        # Check if API key is available
        if not self.llm_api_key:
            logger.warning("No LLM API key provided, using mock response")
            return self._mock_llm_response(prompt)
            
        try:
            # Call OpenAI API
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {self.llm_api_key}"
                }
                
                payload = {
                    "model": self.llm_model,
                    "messages": [
                        {"role": "system", "content": "You are a helpful assistant that generates Mermaid diagrams."},
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": 0.7,
                    "max_tokens": 2000
                }
                
                async with session.post(
                    "https://api.openai.com/v1/chat/completions",
                    headers=headers,
                    json=payload
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"LLM API error: {error_text}")
                        return self._mock_llm_response(prompt)
                        
                    result = await response.json()
                    return result["choices"][0]["message"]["content"]
        except Exception as e:
            logger.error(f"Error calling LLM API: {e}")
            return self._mock_llm_response(prompt)

    def _mock_llm_response(self, prompt: str) -> str:
        """
        Generate a mock LLM response for testing.

        Args:
            prompt: Prompt for the LLM

        Returns:
            Mock response as a string
        """
        # Extract the original diagram from the prompt
        start_idx = prompt.find("```mermaid")
        end_idx = prompt.find("```", start_idx + 10)
        
        if start_idx != -1 and end_idx != -1:
            original_diagram = prompt[start_idx + 10:end_idx].strip()
            
            # Add a comment to indicate this is a mock response
            modified_diagram = original_diagram + "\n  %% This is a mock diagram generated without LLM"
            
            return f"```mermaid\n{modified_diagram}\n```"
        
        return "```mermaid\ngraph TD\n  A[Mock Diagram] --> B[No LLM API Key]\n```"

    def _extract_mermaid_from_response(self, response: str) -> str:
        """
        Extract the Mermaid diagram from the LLM response.

        Args:
            response: LLM response

        Returns:
            Mermaid diagram as a string
        """
        # Find the Mermaid code block
        start_idx = response.find("```mermaid")
        end_idx = response.find("```", start_idx + 10) if start_idx != -1 else -1
        
        if start_idx != -1 and end_idx != -1:
            # Extract the diagram without the ```mermaid and ``` tags
            return response[start_idx + 10:end_idx].strip()
        
        # If no Mermaid block found, return the whole response
        return response

    def _load_taxonomy(self) -> Dict[str, Any]:
        """
        Load the domain taxonomy from JSON.

        Returns:
            Dictionary containing domain taxonomy
        """
        try:
            with open(self.taxonomy_json_path, 'r') as f:
                taxonomy = json.load(f)
            
            logger.info(f"Loaded domain taxonomy from {self.taxonomy_json_path}")
            return taxonomy
        except Exception as e:
            logger.error(f"Failed to load domain taxonomy: {e}")
            return {}
