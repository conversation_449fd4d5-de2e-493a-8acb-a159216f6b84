"""
Impact Propagator

This module provides functionality to propagate the impact of changes through
the domain hierarchy.
"""

import logging
from typing import Dict, List, Any, Optional, Set, Tuple

from pr_broadcast.domain.taxonomy import DomainTaxonomy

logger = logging.getLogger(__name__)


class ImpactPropagator:
    """
    Propagates the impact of changes through the domain hierarchy.
    
    This class provides methods to propagate the impact of changes from directly
    affected domains to their parent domains in the hierarchy.
    """

    def __init__(self, taxonomy_json_path: str):
        """
        Initialize the impact propagator.

        Args:
            taxonomy_json_path: Path to the domain taxonomy JSON file
        """
        self.taxonomy_json_path = taxonomy_json_path
        
        # Initialize domain taxonomy
        self.domain_taxonomy = DomainTaxonomy(taxonomy_json_path)
        
        logger.info("Initialized impact propagator")

    def propagate_impact(
        self,
        direct_impact: Dict[str, Dict[str, Any]]
    ) -> Dict[str, Dict[str, Any]]:
        """
        Propagate the impact of changes through the domain hierarchy.

        Args:
            direct_impact: Dictionary mapping domains to direct impact information

        Returns:
            Dictionary mapping domains to combined direct and indirect impact information
        """
        # Start with the direct impact
        all_impact = direct_impact.copy()
        
        # Propagate impact up the domain hierarchy
        hierarchy_impact = self._propagate_up_hierarchy(direct_impact)
        
        # Merge hierarchy impact with direct impact
        for domain, impact in hierarchy_impact.items():
            if domain in all_impact:
                # Domain already has direct impact - update with indirect impact
                all_impact[domain]["indirect_impact"] = True
                
                # Only update impact score if it's higher
                if impact["impact_score"] > all_impact[domain]["impact_score"]:
                    all_impact[domain]["impact_score"] = impact["impact_score"]
                    all_impact[domain]["severity"] = impact["severity"]
            else:
                # Domain only has indirect impact
                all_impact[domain] = impact
        
        logger.info(f"Propagated impact to {len(all_impact)} domains")
        return all_impact

    def _propagate_up_hierarchy(self, direct_impact: Dict[str, Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """
        Propagate impact up the domain hierarchy.

        Args:
            direct_impact: Dictionary mapping domains to direct impact information

        Returns:
            Dictionary mapping domains to indirect impact information
        """
        indirect_impact = {}
        
        # Process each directly impacted domain
        for domain, impact in direct_impact.items():
            # Get the impact score
            impact_score = impact["impact_score"]
            
            # Propagate up the hierarchy with decreasing impact
            current_domain = domain
            propagation_factor = 0.7  # Impact decreases as we go up the hierarchy
            
            while True:
                # Get the parent domain
                parent_domain = self.domain_taxonomy.get_parent_domain(current_domain)
                
                # Stop if no parent domain
                if not parent_domain:
                    break
                
                # Reduce the impact score
                impact_score *= propagation_factor
                
                # Add to indirect impact
                if parent_domain not in indirect_impact:
                    indirect_impact[parent_domain] = {
                        "changes": [],
                        "impact_score": 0.0,
                        "severity": "low",
                        "direct_impact": False,
                        "indirect_impact": True
                    }
                
                # Update impact score
                indirect_impact[parent_domain]["impact_score"] += impact_score
                
                # Move up to the parent
                current_domain = parent_domain
        
        # Calculate severity for each domain
        for domain, impact in indirect_impact.items():
            impact["severity"] = self._calculate_severity(impact["impact_score"])
        
        return indirect_impact

    def _calculate_severity(self, impact_score: float) -> str:
        """
        Calculate the severity level based on the impact score.

        Args:
            impact_score: Impact score

        Returns:
            Severity level (low, medium, high)
        """
        if impact_score < 0.5:
            return "low"
        elif impact_score < 1.0:
            return "medium"
        else:
            return "high"
