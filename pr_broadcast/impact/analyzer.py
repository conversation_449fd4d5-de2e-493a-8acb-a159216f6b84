"""
Impact Analyzer

This module provides functionality to analyze the impact of changes on domains.
"""

import logging
from typing import Dict, List, Any, Optional, Set, Tuple

from pr_broadcast.diff.models import FunctionChange
from pr_broadcast.domain.mapper import DomainMapper
from pr_broadcast.domain.taxonomy import DomainTaxonomy

logger = logging.getLogger(__name__)


class ImpactAnalyzer:
    """
    Analyzes the impact of changes on domains.
    
    This class provides methods to analyze the direct impact of function changes
    on domains and calculate impact scores and severity levels.
    """

    def __init__(self, taxonomy_json_path: str, domain_traces_yaml_path: str):
        """
        Initialize the impact analyzer.

        Args:
            taxonomy_json_path: Path to the domain taxonomy JSON file
            domain_traces_yaml_path: Path to the domain traces YAML file
        """
        self.taxonomy_json_path = taxonomy_json_path
        self.domain_traces_yaml_path = domain_traces_yaml_path
        
        # Initialize domain mapper and taxonomy
        self.domain_mapper = DomainMapper(taxonomy_json_path, domain_traces_yaml_path)
        self.domain_taxonomy = DomainTaxonomy(taxonomy_json_path)
        
        logger.info("Initialized impact analyzer")

    def analyze_impact(self, function_changes: List[FunctionChange]) -> Dict[str, Dict[str, Any]]:
        """
        Analyze the direct impact of function changes on domains.

        Args:
            function_changes: List of function changes

        Returns:
            Dictionary mapping domains to impact information
        """
        # Map function changes to domains
        domain_changes = self.domain_mapper.map_functions_to_domains(function_changes)
        
        # Calculate impact scores and severity levels
        domain_impact = {}
        
        for domain, changes in domain_changes.items():
            # Calculate impact score
            impact_score = sum(fc["impact_score"] for fc in changes["functions"])
            
            # Calculate severity level
            severity = self._calculate_severity(impact_score)
            
            # Create impact entry
            domain_impact[domain] = {
                "changes": changes["functions"],
                "impact_score": impact_score,
                "severity": severity,
                "direct_impact": True,
                "indirect_impact": False
            }
        
        logger.info(f"Analyzed impact for {len(domain_impact)} domains")
        return domain_impact

    def _calculate_severity(self, impact_score: float) -> str:
        """
        Calculate the severity level based on the impact score.

        Args:
            impact_score: Impact score

        Returns:
            Severity level (low, medium, high)
        """
        if impact_score < 1.0:
            return "low"
        elif impact_score < 2.0:
            return "medium"
        else:
            return "high"
