"""
Output Manager

This module provides functionality to manage the output artifacts from PR Broadcast.
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional, Set, Tuple

logger = logging.getLogger(__name__)


class OutputManager:
    """
    Manages the output artifacts from PR Broadcast.

    Key features:
    1. Creates appropriate directory structure
    2. Saves generated diagrams
    3. Creates summary reports
    4. Generates HTML reports
    """

    def __init__(self, output_dir: str):
        """
        Initialize the output manager.

        Args:
            output_dir: Directory to save output artifacts
        """
        self.output_dir = output_dir

        # Create output directory if it doesn't exist
        self._create_directory_structure()

        logger.info(f"Initialized output manager with output directory {output_dir}")

    def save_diagrams(
        self,
        diagrams: Dict[str, Any],
        mr_iid: int
    ) -> Dict[str, Dict[str, str]]:
        """
        Save generated diagrams to files.

        Args:
            diagrams: Dictionary mapping domains to diagram information or a dictionary with domain types
            mr_iid: Merge request IID (internal ID)

        Returns:
            Dictionary mapping diagram types to file paths
        """
        artifacts = {
            "leaf_domains": {},
            "parent_domains": {}
        }

        # Check if diagrams is a dictionary with domain types
        if "leaf_domains" in diagrams or "parent_domains" in diagrams:
            # Handle structured diagrams
            for diagram_type, domains in diagrams.items():
                if diagram_type not in ["leaf_domains", "parent_domains"]:
                    continue

                for domain, diagram_info in domains.items():
                    self._save_domain_diagram(domain, diagram_info, diagram_type, mr_iid, artifacts)
        else:
            # Handle flat diagrams dictionary
            for domain, diagram_info in diagrams.items():
                # Determine if this is a leaf or parent domain
                is_leaf = "changes" in diagram_info
                diagram_type = "leaf_domains" if is_leaf else "parent_domains"

                self._save_domain_diagram(domain, diagram_info, diagram_type, mr_iid, artifacts)

        # Generate HTML report
        self._generate_html_report(artifacts, mr_iid)

        return artifacts

    def _save_domain_diagram(
        self,
        domain: str,
        diagram_info: Dict[str, Any],
        diagram_type: str,
        mr_iid: int,
        artifacts: Dict[str, Dict[str, str]]
    ) -> None:
        """
        Save a domain diagram to a file.

        Args:
            domain: Domain name
            diagram_info: Diagram information
            diagram_type: Type of diagram (leaf_domains or parent_domains)
            mr_iid: Merge request IID (internal ID)
            artifacts: Dictionary to update with file paths
        """
        # Create a sanitized domain name for the file name
        domain_file_name = self._sanitize_filename(domain)

        # Determine the output directory and file name
        if diagram_type == "leaf_domains":
            output_dir = os.path.join(self.output_dir, "leaf_domains")
            file_name = f"leaf_domain_impact_{domain_file_name}_{mr_iid}.md"
        else:
            output_dir = os.path.join(self.output_dir, "parent_domains")
            file_name = f"parent_domain_impact_{domain_file_name}_{mr_iid}.md"

        # Create the full file path
        file_path = os.path.join(output_dir, file_name)

        # Ensure the output directory exists
        os.makedirs(output_dir, exist_ok=True)

        # Save the diagram
        try:
            with open(file_path, 'w') as f:
                # Add a header
                f.write(f"# Impact Diagram for Domain: {domain}\n\n")

                # Check if diagram_info has the expected structure
                if "original_diagram" in diagram_info and "impact_diagram" in diagram_info:
                    # Add the original diagram
                    f.write("## Original Diagram\n\n")
                    f.write("```mermaid\n")
                    f.write(diagram_info["original_diagram"])
                    f.write("\n```\n\n")

                    # Add the impact diagram
                    f.write("## Impact Diagram\n\n")
                    f.write("```mermaid\n")
                    f.write(diagram_info["impact_diagram"])
                    f.write("\n```\n\n")

                    # Add additional information for leaf domains
                    if diagram_type == "leaf_domains" and "changes" in diagram_info:
                        f.write("## Function Changes\n\n")
                        for fc in diagram_info["changes"].get("functions", []):
                            f.write(f"### {fc.get('function_name', 'Unknown')} ({fc.get('change_type', 'Unknown')})\n\n")
                            f.write("```diff\n")
                            f.write(fc.get("diff_content", ""))
                            f.write("\n```\n\n")
                else:
                    # Just write the diagram info as JSON
                    f.write("## Diagram Information\n\n")
                    f.write("```json\n")
                    f.write(json.dumps(diagram_info, indent=2))
                    f.write("\n```\n\n")

            # Add to artifacts
            artifacts[diagram_type][domain] = file_path

            logger.info(f"Saved diagram for domain {domain} to {file_path}")
        except Exception as e:
            logger.error(f"Failed to save diagram for domain {domain}: {e}")

    def save_summary(
        self,
        summary: Dict[str, Any],
        mr_iid: int
    ) -> str:
        """
        Save a summary report.

        Args:
            summary: Dictionary containing summary information
            mr_iid: Merge request IID (internal ID)

        Returns:
            Path to the saved summary file
        """
        # Create the summary directory if it doesn't exist
        summary_dir = os.path.join(self.output_dir, "summary")
        os.makedirs(summary_dir, exist_ok=True)

        # Create the summary file path
        summary_path = os.path.join(summary_dir, f"pr_impact_summary_{mr_iid}.json")

        # Save the summary
        try:
            with open(summary_path, 'w') as f:
                json.dump(summary, f, indent=2)

            logger.info(f"Saved summary to {summary_path}")

            # Generate a markdown summary
            self._generate_markdown_summary(summary, mr_iid)

            return summary_path
        except Exception as e:
            logger.error(f"Failed to save summary: {e}")
            return ""

    def _create_directory_structure(self) -> None:
        """
        Create the directory structure for output artifacts.
        """
        # Create the main output directory
        os.makedirs(self.output_dir, exist_ok=True)

        # Create subdirectories
        os.makedirs(os.path.join(self.output_dir, "leaf_domains"), exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, "parent_domains"), exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, "summary"), exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, "html"), exist_ok=True)

        logger.info(f"Created directory structure in {self.output_dir}")

    def _generate_markdown_summary(
        self,
        summary: Dict[str, Any],
        mr_iid: int
    ) -> None:
        """
        Generate a markdown summary report.

        Args:
            summary: Dictionary containing summary information
            mr_iid: Merge request IID (internal ID)
        """
        # Create the summary file path
        summary_path = os.path.join(self.output_dir, "summary", f"pr_impact_summary_{mr_iid}.md")

        # Generate the markdown summary
        try:
            with open(summary_path, 'w') as f:
                f.write(f"# PR Impact Summary for MR !{mr_iid}\n\n")

                f.write(f"## {summary['title']}\n\n")
                f.write(f"**Author**: {summary['author']}\n\n")

                f.write("## Change Summary\n\n")
                f.write(f"- **Total Changed Functions**: {summary['total_changes']}\n")
                f.write(f"  - Added: {summary['change_counts']['added']}\n")
                f.write(f"  - Modified: {summary['change_counts']['modified']}\n")
                f.write(f"  - Deleted: {summary['change_counts']['deleted']}\n\n")

                f.write("## Domain Impact Summary\n\n")
                f.write(f"- **Total Impacted Domains**: {summary['total_impacted_domains']}\n\n")

                if summary.get("domain_levels"):
                    f.write("### Domain Levels\n\n")
                    for level, count in summary["domain_levels"].items():
                        f.write(f"- **Level {level}**: {count} domains\n")
                    f.write("\n")

                f.write("## Most Impacted Domains\n\n")
                for domain in summary["most_impacted_domains"]:
                    name = domain["name"]
                    changes = domain["changes"]
                    f.write(f"### {name}\n\n")
                    f.write(f"- **Changed Functions**: {len(changes['functions'])}\n")
                    f.write("- **Functions**:\n")
                    for fc in changes["functions"][:3]:  # Show only first 3 functions
                        f.write(f"  - {fc['function_name']} ({fc['change_type']})\n")
                    if len(changes["functions"]) > 3:
                        f.write(f"  - ... and {len(changes['functions']) - 3} more\n")
                    f.write("\n")

            logger.info(f"Generated markdown summary at {summary_path}")
        except Exception as e:
            logger.error(f"Failed to generate markdown summary: {e}")

    def _generate_html_report(
        self,
        artifacts: Dict[str, Dict[str, str]],
        mr_iid: int
    ) -> None:
        """
        Generate an HTML report.

        Args:
            artifacts: Dictionary mapping diagram types to file paths
            mr_iid: Merge request IID (internal ID)
        """
        # Create the HTML file path
        html_path = os.path.join(self.output_dir, "html", f"pr_impact_report_{mr_iid}.html")

        # Generate the HTML report
        try:
            with open(html_path, 'w') as f:
                f.write("<!DOCTYPE html>\n")
                f.write("<html lang=\"en\">\n")
                f.write("<head>\n")
                f.write("  <meta charset=\"UTF-8\">\n")
                f.write("  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n")
                f.write(f"  <title>PR Impact Report for MR !{mr_iid}</title>\n")
                f.write("  <script src=\"https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js\"></script>\n")
                f.write("  <style>\n")
                f.write("    body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }\n")
                f.write("    h1, h2, h3 { color: #333; }\n")
                f.write("    .container { max-width: 1200px; margin: 0 auto; }\n")
                f.write("    .diagram { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }\n")
                f.write("    .tabs { display: flex; margin-bottom: 20px; }\n")
                f.write("    .tab { padding: 10px 20px; cursor: pointer; border: 1px solid #ddd; border-radius: 5px 5px 0 0; margin-right: 5px; }\n")
                f.write("    .tab.active { background-color: #f0f0f0; }\n")
                f.write("    .tab-content { display: none; }\n")
                f.write("    .tab-content.active { display: block; }\n")
                f.write("  </style>\n")
                f.write("</head>\n")
                f.write("<body>\n")
                f.write("  <div class=\"container\">\n")
                f.write(f"    <h1>PR Impact Report for MR !{mr_iid}</h1>\n")

                # Add tabs
                f.write("    <div class=\"tabs\">\n")
                f.write("      <div class=\"tab active\" onclick=\"showTab('summary')\">Summary</div>\n")
                f.write("      <div class=\"tab\" onclick=\"showTab('leaf-domains')\">Leaf Domains</div>\n")
                f.write("      <div class=\"tab\" onclick=\"showTab('parent-domains')\">Parent Domains</div>\n")
                f.write("    </div>\n")

                # Add summary tab content
                f.write("    <div id=\"summary\" class=\"tab-content active\">\n")
                f.write("      <h2>Summary</h2>\n")
                f.write("      <p>This report shows the impact of changes on domains in the codebase.</p>\n")
                f.write("    </div>\n")

                # Add leaf domains tab content
                f.write("    <div id=\"leaf-domains\" class=\"tab-content\">\n")
                f.write("      <h2>Leaf Domains</h2>\n")

                for domain, path in artifacts.get("leaf_domains", {}).items():
                    f.write(f"      <div class=\"diagram\">\n")
                    f.write(f"        <h3>{domain}</h3>\n")
                    f.write(f"        <div class=\"mermaid\">\n")

                    # Read the impact diagram from the file
                    try:
                        with open(path, 'r') as diagram_file:
                            content = diagram_file.read()

                            # Extract the impact diagram
                            start_index = content.find("```mermaid", content.find("## Impact Diagram"))
                            end_index = content.find("```", start_index + 10)

                            if start_index > 0 and end_index > start_index:
                                diagram = content[start_index + 10:end_index].strip()
                                f.write(f"          {diagram}\n")
                    except Exception as e:
                        logger.error(f"Failed to read diagram from {path}: {e}")

                    f.write("        </div>\n")
                    f.write("      </div>\n")

                f.write("    </div>\n")

                # Add parent domains tab content
                f.write("    <div id=\"parent-domains\" class=\"tab-content\">\n")
                f.write("      <h2>Parent Domains</h2>\n")

                for domain, path in artifacts.get("parent_domains", {}).items():
                    f.write(f"      <div class=\"diagram\">\n")
                    f.write(f"        <h3>{domain}</h3>\n")
                    f.write(f"        <div class=\"mermaid\">\n")

                    # Read the impact diagram from the file
                    try:
                        with open(path, 'r') as diagram_file:
                            content = diagram_file.read()

                            # Extract the impact diagram
                            start_index = content.find("```mermaid", content.find("## Impact Diagram"))
                            end_index = content.find("```", start_index + 10)

                            if start_index > 0 and end_index > start_index:
                                diagram = content[start_index + 10:end_index].strip()
                                f.write(f"          {diagram}\n")
                    except Exception as e:
                        logger.error(f"Failed to read diagram from {path}: {e}")

                    f.write("        </div>\n")
                    f.write("      </div>\n")

                f.write("    </div>\n")

                # Add JavaScript
                f.write("    <script>\n")
                f.write("      mermaid.initialize({ startOnLoad: true });\n")
                f.write("\n")
                f.write("      function showTab(tabId) {\n")
                f.write("        // Hide all tab contents\n")
                f.write("        document.querySelectorAll('.tab-content').forEach(content => {\n")
                f.write("          content.classList.remove('active');\n")
                f.write("        });\n")
                f.write("\n")
                f.write("        // Remove active class from all tabs\n")
                f.write("        document.querySelectorAll('.tab').forEach(tab => {\n")
                f.write("          tab.classList.remove('active');\n")
                f.write("        });\n")
                f.write("\n")
                f.write("        // Show the selected tab content\n")
                f.write("        document.getElementById(tabId).classList.add('active');\n")
                f.write("\n")
                f.write("        // Add active class to the clicked tab\n")
                f.write("        document.querySelector(`.tab[onclick=\"showTab('${tabId}')\"]`).classList.add('active');\n")
                f.write("      }\n")
                f.write("    </script>\n")

                f.write("  </div>\n")
                f.write("</body>\n")
                f.write("</html>\n")

            logger.info(f"Generated HTML report at {html_path}")
        except Exception as e:
            logger.error(f"Failed to generate HTML report: {e}")

    def _sanitize_filename(self, name: str) -> str:
        """
        Sanitize a name for use in a filename.

        Args:
            name: Name to sanitize

        Returns:
            Sanitized name
        """
        # Replace invalid characters with underscores
        return name.replace(" ", "_").replace("/", "_").replace("\\", "_").replace(":", "_")
