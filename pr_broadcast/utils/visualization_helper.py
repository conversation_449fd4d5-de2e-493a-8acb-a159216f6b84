"""
Visualization Helper

This module provides helper functions for visualizing PR impact diagrams.
"""

import os
import logging
from typing import Dict, List, Any

logger = logging.getLogger(__name__)

def enhance_diagram_colors(diagram_content: str) -> str:
    """
    Enhance the colors in a Mermaid diagram for better visualization.
    
    Args:
        diagram_content: Mermaid diagram content
        
    Returns:
        Enhanced diagram content
    """
    # Add custom styling for different impact levels
    style_definitions = """
  %% Custom styling for impact levels
  classDef high_direct fill:#FF5252,stroke:#FF0000,stroke-width:2px,color:white
  classDef high_indirect fill:#FF9E80,stroke:#FF6E40,stroke-width:1px
  classDef medium_direct fill:#FFD740,stroke:#FFAB00,stroke-width:2px
  classDef medium_indirect fill:#FFE57F,stroke:#FFD740,stroke-width:1px
  classDef low_direct fill:#B2FF59,stroke:#76FF03,stroke-width:2px
  classDef low_indirect fill:#CCFF90,stroke:#B2FF59,stroke-width:1px
  classDef domain fill:#E1F5FE,stroke:#4FC3F7,stroke-width:1px
  classDef function fill:#F3E5F5,stroke:#CE93D8,stroke-width:1px
  classDef file fill:#E8F5E9,stroke:#81C784,stroke-width:1px
"""
    
    # Add the style definitions before the closing tag
    if "```" in diagram_content:
        return diagram_content.replace("```", style_definitions + "\n```", 1)
    
    return diagram_content + style_definitions

def generate_html_report(diagrams: Dict[str, Any], output_path: str) -> str:
    """
    Generate an HTML report from the generated diagrams.
    
    Args:
        diagrams: Dictionary mapping diagram types to file paths
        output_path: Path to save the HTML report
        
    Returns:
        Path to the generated HTML report
    """
    html_content = f"""<!DOCTYPE html>
<html>
<head>
    <title>PR Impact Analysis Report</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {{
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        h1, h2, h3 {{
            color: #2c3e50;
        }}
        .diagram-container {{
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .tabs {{
            display: flex;
            margin-bottom: 10px;
        }}
        .tab {{
            padding: 10px 15px;
            cursor: pointer;
            border: 1px solid #ddd;
            border-bottom: none;
            border-radius: 5px 5px 0 0;
            background-color: #f1f1f1;
            margin-right: 5px;
            transition: background-color 0.3s;
        }}
        .tab:hover {{
            background-color: #e0e0e0;
        }}
        .tab.active {{
            background-color: #fff;
            border-bottom: 1px solid #fff;
            font-weight: bold;
        }}
        .tab-content {{
            display: none;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 0 5px 5px 5px;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .tab-content.active {{
            display: block;
        }}
        .header {{
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }}
        .header h1 {{
            color: white;
            margin: 0;
        }}
        .footer {{
            text-align: center;
            margin-top: 30px;
            padding: 10px;
            color: #777;
            font-size: 0.9em;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>PR Impact Analysis Report</h1>
    </div>
    
    <div class="tabs">
        <div class="tab active" onclick="openTab(event, 'summary')">Summary</div>
        <div class="tab" onclick="openTab(event, 'hierarchy')">Hierarchy Impact</div>
        <div class="tab" onclick="openTab(event, 'domains')">Domain Impact</div>
        <div class="tab" onclick="openTab(event, 'functions')">Function Changes</div>
    </div>
    
    <div id="summary" class="tab-content active">
        <h2>Summary Impact</h2>
        <div class="diagram-container">
            <div class="mermaid">
                {{summary_content}}
            </div>
        </div>
    </div>
    
    <div id="hierarchy" class="tab-content">
        <h2>Hierarchy Impact</h2>
        <div class="diagram-container">
            <div class="mermaid">
                {{hierarchy_content}}
            </div>
        </div>
    </div>
    
    <div id="domains" class="tab-content">
        <h2>Domain Impact</h2>
        <div class="diagram-container">
            <div class="mermaid">
                {{domains_content}}
            </div>
        </div>
    </div>
    
    <div id="functions" class="tab-content">
        <h2>Function Changes</h2>
        <div class="diagram-container">
            <div class="mermaid">
                {{functions_content}}
            </div>
        </div>
    </div>
    
    <div class="footer">
        Generated by PR Broadcast - Visualizing the impact of code changes on domain architecture
    </div>
    
    <script>
        mermaid.initialize({{ 
            startOnLoad: true,
            theme: 'dark',
            securityLevel: 'loose',
            flowchart: {{ 
                curve: 'basis',
                diagramPadding: 8
            }}
        }});
        
        function openTab(evt, tabName) {{
            var i, tabcontent, tablinks;
            
            tabcontent = document.getElementsByClassName("tab-content");
            for (i = 0; i < tabcontent.length; i++) {{
                tabcontent[i].className = tabcontent[i].className.replace(" active", "");
            }}
            
            tablinks = document.getElementsByClassName("tab");
            for (i = 0; i < tablinks.length; i++) {{
                tablinks[i].className = tablinks[i].className.replace(" active", "");
            }}
            
            document.getElementById(tabName).className += " active";
            evt.currentTarget.className += " active";
        }}
    </script>
</body>
</html>
"""
    
    # Replace placeholders with actual diagram content
    try:
        with open(diagrams.get("summary", ""), 'r') as f:
            summary_content = f.read()
        
        with open(diagrams.get("hierarchy", ""), 'r') as f:
            hierarchy_content = f.read()
        
        # For domain diagrams, just use the first one as an example
        domain_files = list(diagrams.get("domains", {}).values())
        if domain_files:
            with open(domain_files[0], 'r') as f:
                domains_content = f.read()
        else:
            domains_content = "graph TD\\n  A[No domain diagrams]"
        
        # For function diagrams, just use the first one as an example
        function_files = list(diagrams.get("functions", {}).values())
        if function_files:
            with open(function_files[0], 'r') as f:
                functions_content = f.read()
        else:
            functions_content = "graph TD\\n  A[No function diagrams]"
        
        # Extract the Mermaid code from the markdown files
        def extract_mermaid(content):
            if "```mermaid" in content:
                start = content.find("```mermaid") + 10
                end = content.find("```", start)
                return content[start:end].strip()
            return content
        
        html_content = html_content.replace("{{summary_content}}", extract_mermaid(summary_content))
        html_content = html_content.replace("{{hierarchy_content}}", extract_mermaid(hierarchy_content))
        html_content = html_content.replace("{{domains_content}}", extract_mermaid(domains_content))
        html_content = html_content.replace("{{functions_content}}", extract_mermaid(functions_content))
        
        # Save the HTML report
        with open(output_path, 'w') as f:
            f.write(html_content)
        
        logger.info(f"Generated HTML report at {output_path}")
        return output_path
    
    except Exception as e:
        logger.error(f"Error generating HTML report: {e}")
        return ""
