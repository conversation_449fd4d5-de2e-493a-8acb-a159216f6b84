"""
Function Diff Analyzer

This module provides functionality to analyze diffs and extract function changes.
"""

import os
import re
import logging
import difflib
from typing import Dict, List, Any, Optional, Tuple, Union

from pr_broadcast.diff.models import FunctionChange, ChangeType

logger = logging.getLogger(__name__)


class FunctionDiffAnalyzer:
    """
    Analyzes diffs to extract function changes.

    This class identifies functions that have been added, modified, or deleted
    in a diff and extracts relevant information about those changes.
    """

    # Patterns for identifying function definitions in different languages
    FUNCTION_PATTERNS = {
        '.py': r'^\s*(def|class)\s+([a-zA-Z0-9_]+)',
        '.rb': r'^\s*(def|class)\s+([a-zA-Z0-9_]+)',
        '.js': r'^\s*(function\s+([a-zA-Z0-9_]+)|([a-zA-Z0-9_]+)\s*=\s*function)',
        '.ts': r'^\s*(function\s+([a-zA-Z0-9_]+)|([a-zA-Z0-9_]+)\s*=\s*function|\s*([a-zA-Z0-9_]+)\s*\(.*\)\s*[:{])',
        '.java': r'^\s*(public|private|protected)?\s*(static)?\s*[a-zA-Z0-9_<>]+\s+([a-zA-Z0-9_]+)\s*\(',
        '.cs': r'^\s*(public|private|protected)?\s*(static)?\s*[a-zA-Z0-9_<>]+\s+([a-zA-Z0-9_]+)\s*\(',
        '.go': r'^\s*func\s+([a-zA-Z0-9_]+)',
        '.php': r'^\s*(function|class)\s+([a-zA-Z0-9_]+)',
        '.c': r'^\s*[a-zA-Z0-9_]+\s+([a-zA-Z0-9_]+)\s*\(',
        '.cpp': r'^\s*[a-zA-Z0-9_:]+\s+([a-zA-Z0-9_]+)\s*\(',
        '.h': r'^\s*[a-zA-Z0-9_]+\s+([a-zA-Z0-9_]+)\s*\(',
        '.hpp': r'^\s*[a-zA-Z0-9_:]+\s+([a-zA-Z0-9_]+)\s*\(',
        '.rs': r'^\s*fn\s+([a-zA-Z0-9_]+)',
        '.swift': r'^\s*(func|class|struct|enum)\s+([a-zA-Z0-9_]+)',
        '.kt': r'^\s*(fun|class|interface)\s+([a-zA-Z0-9_]+)',
    }

    def __init__(self):
        """Initialize the function diff analyzer."""
        logger.info("Initialized function diff analyzer")

    def analyze_diff(self, diff: Union[Dict[str, Any], str]) -> List[FunctionChange]:
        """
        Analyze a diff to extract function changes.

        Args:
            diff: Dictionary containing diff information or a string containing the diff

        Returns:
            List of function changes
        """
        # Handle string input (convert to dict)
        if isinstance(diff, str):
            # Assume this is a raw diff string
            diff_dict = {
                "diff": diff,
                "old_path": "",  # We'll need to extract this from the diff
                "new_path": "",  # We'll need to extract this from the diff
                "binary_file": False
            }

            # Try to extract file paths from the diff
            lines = diff.split("\n")
            for line in lines:
                if line.startswith("--- a/"):
                    diff_dict["old_path"] = line[6:]
                elif line.startswith("+++ b/"):
                    diff_dict["new_path"] = line[6:]
                if diff_dict["old_path"] and diff_dict["new_path"]:
                    break

            diff = diff_dict

        # Skip if diff is empty or for a binary file
        if not diff.get("diff") or diff.get("binary_file", False):
            return []

        # Get file paths
        old_path = diff.get("old_path", "")
        new_path = diff.get("new_path", "")

        # Use new path if available, otherwise use old path
        file_path = new_path if new_path and new_path != "/dev/null" else old_path

        # Skip if no file path
        if not file_path or file_path == "/dev/null":
            return []

        # Skip non-code files
        if not self._is_code_file(file_path):
            logger.debug(f"Skipping non-code file: {file_path}")
            return []

        # Get file extension to determine language
        file_ext = self._get_file_extension(file_path)
        if file_ext not in self.FUNCTION_PATTERNS:
            logger.debug(f"Unsupported file type: {file_path}")
            return []

        # Parse the diff to extract function changes
        function_changes = self._parse_diff(diff, file_path, file_ext)

        logger.debug(f"Found {len(function_changes)} function changes in {file_path}")
        return function_changes

    def _is_code_file(self, file_path: str) -> bool:
        """
        Check if a file is a code file based on its extension.

        Args:
            file_path: Path to the file

        Returns:
            True if the file is a code file, False otherwise
        """
        # Get file extension
        _, ext = os.path.splitext(file_path)

        # Check if extension is in the list of supported extensions
        return ext in self.FUNCTION_PATTERNS

    def _get_file_extension(self, file_path: str) -> str:
        """
        Get the file extension from a file path.

        Args:
            file_path: Path to the file

        Returns:
            File extension (including the dot)
        """
        _, ext = os.path.splitext(file_path)
        return ext

    def _parse_diff(self, diff: Dict[str, Any], file_path: str, file_ext: str) -> List[FunctionChange]:
        """
        Parse a diff to extract function changes.

        Args:
            diff: Dictionary containing diff information
            file_path: Path to the file
            file_ext: File extension

        Returns:
            List of function changes
        """
        # Get the diff content
        diff_content = diff.get("diff", "")

        # Split the diff into lines
        lines = diff_content.split("\n")

        # Track function changes
        function_changes = []

        # Track the current function being processed
        current_function = None
        current_diff_lines = []
        in_function_diff = False

        # Process each line in the diff
        for line in lines:
            # Skip empty lines
            if not line:
                continue

            # Check if this is a diff line (added, removed, or context)
            if line.startswith("+") or line.startswith("-") or line.startswith(" "):
                # Get the content of the line (without the diff marker)
                content = line[1:]

                # Check if this is a function definition
                function_match = self._is_function_definition(content, file_ext)
                if function_match:
                    # If we were processing a function diff, save it
                    if in_function_diff and current_function:
                        function_change = self._create_function_change(
                            current_function,
                            file_path,
                            "\n".join(current_diff_lines)
                        )
                        function_changes.append(function_change)

                    # Start tracking a new function
                    current_function = function_match
                    current_diff_lines = [line]
                    in_function_diff = True
                elif in_function_diff:
                    # Add this line to the current function diff
                    current_diff_lines.append(line)
            elif in_function_diff:
                # Add context lines to the current function diff
                current_diff_lines.append(line)

        # If we were processing a function diff, save it
        if in_function_diff and current_function:
            function_change = self._create_function_change(
                current_function,
                file_path,
                "\n".join(current_diff_lines)
            )
            function_changes.append(function_change)

        return function_changes

    def _is_function_definition(self, line: str, file_ext: str) -> Optional[str]:
        """
        Check if a line is a function definition.

        Args:
            line: Line to check
            file_ext: File extension

        Returns:
            Function name if the line is a function definition, None otherwise
        """
        # Get the pattern for this file type
        pattern = self.FUNCTION_PATTERNS.get(file_ext)
        if not pattern:
            return None

        # Check if the line matches the pattern
        match = re.match(pattern, line)
        if match:
            # Extract the function name
            function_name = match.group(2) if len(match.groups()) >= 2 else match.group(1)
            return function_name

        return None

    def _create_function_change(self, function_name: str, file_path: str, diff_content: str) -> FunctionChange:
        """
        Create a function change object.

        Args:
            function_name: Name of the function
            file_path: Path to the file
            diff_content: Diff content for the function

        Returns:
            FunctionChange object
        """
        # Determine the change type
        change_type = self._determine_change_type(diff_content)

        # Create a function signature
        function_signature = f"{file_path}:{function_name}"

        # Calculate the impact score
        impact_score = self._calculate_impact_score(diff_content, change_type)

        # Create the function change
        return FunctionChange(
            file_path=file_path,
            function_name=function_name,
            function_signature=function_signature,
            change_type=change_type,
            diff_content=diff_content,
            impact_score=impact_score
        )

    def _determine_change_type(self, diff_content: str) -> ChangeType:
        """
        Determine the type of change from the diff content.

        Args:
            diff_content: Diff content for the function

        Returns:
            ChangeType enum value
        """
        # Count the number of added and removed lines
        added_lines = len([line for line in diff_content.split("\n") if line.startswith("+")])
        removed_lines = len([line for line in diff_content.split("\n") if line.startswith("-")])

        # Determine the change type
        if added_lines > 0 and removed_lines == 0:
            return ChangeType.ADDED
        elif added_lines == 0 and removed_lines > 0:
            return ChangeType.DELETED
        else:
            return ChangeType.MODIFIED

    def _calculate_impact_score(self, diff_content: str, change_type: ChangeType) -> float:
        """
        Calculate an impact score for the change.

        Args:
            diff_content: Diff content for the function
            change_type: Type of change

        Returns:
            Impact score
        """
        # Count the number of lines in the diff
        lines = diff_content.split("\n")
        num_lines = len(lines)

        # Set base score based on change type
        if change_type == ChangeType.ADDED:
            base_score = 0.5
        elif change_type == ChangeType.DELETED:
            base_score = 0.7
        else:  # MODIFIED
            base_score = 0.3

        # Adjust score based on function size
        size_factor = min(1.0, num_lines / 50.0)  # Cap at 50 lines

        # Count the number of changed lines in a diff
        if change_type == ChangeType.MODIFIED:
            changed_lines = sum(1 for line in lines if line.startswith('+') or line.startswith('-'))
            change_ratio = changed_lines / max(1, num_lines)

            # Adjust score based on the ratio of changed lines
            return base_score + (change_ratio * 0.5)

        return base_score + (size_factor * 0.5)


class EnhancedFunctionDiffAnalyzer(FunctionDiffAnalyzer):
    """
    Enhanced analyzer for extracting function changes from diffs.

    Key enhancements:
    1. Improved function signature extraction
    2. Proper formatting of function signatures (file_path:Class.method)
    3. Better handling of class methods and nested functions
    """

    def _create_function_change(self, function_name: str, file_path: str, diff_content: str) -> FunctionChange:
        """
        Create a function change object with enhanced function signature.

        Args:
            function_name: Name of the function
            file_path: Path to the file
            diff_content: Diff content for the function

        Returns:
            FunctionChange object
        """
        # Determine the change type
        change_type = self._determine_change_type(diff_content)

        # Create a function signature with proper formatting
        # Extract class name if this is a method
        class_name = self._extract_class_name(diff_content)

        if class_name:
            function_signature = f"{file_path}:{class_name}.{function_name}"
        else:
            function_signature = f"{file_path}:{function_name}"

        # Calculate the impact score
        impact_score = self._calculate_impact_score(diff_content, change_type)

        # Create the function change
        return FunctionChange(
            file_path=file_path,
            function_name=function_name,
            function_signature=function_signature,
            change_type=change_type,
            diff_content=diff_content,
            impact_score=impact_score
        )

    def _extract_class_name(self, diff_content: str) -> Optional[str]:
        """
        Extract the class name from the diff content if this is a method.

        Args:
            diff_content: Diff content for the function

        Returns:
            Class name if this is a method, None otherwise
        """
        # This is a simplified implementation
        # In a real implementation, you would need to parse the code to extract the class name

        # For now, just check if "class" appears in the diff content
        lines = diff_content.split("\n")
        for line in lines:
            if "class" in line:
                # Extract the class name
                match = re.search(r'class\s+([a-zA-Z0-9_]+)', line)
                if match:
                    return match.group(1)

        return None
