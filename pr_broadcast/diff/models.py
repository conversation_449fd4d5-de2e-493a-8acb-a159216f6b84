"""
Function Change Models

This module defines data models for representing function changes in diffs.
"""

import enum
from dataclasses import dataclass
from typing import Dict, List, Any, Optional


class ChangeType(str, enum.Enum):
    """Type of change to a function."""
    ADDED = "ADDED"
    MODIFIED = "MODIFIED"
    DELETED = "DELETED"


@dataclass
class FunctionChange:
    """Represents a change to a function in a diff."""
    file_path: str
    function_name: str
    function_signature: str
    change_type: ChangeType
    diff_content: str
    impact_score: float
    old_content: Optional[str] = None
    new_content: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "file_path": self.file_path,
            "function_name": self.function_name,
            "function_signature": self.function_signature,
            "change_type": self.change_type,
            "diff_content": self.diff_content,
            "impact_score": self.impact_score,
            "old_content": self.old_content,
            "new_content": self.new_content
        }
