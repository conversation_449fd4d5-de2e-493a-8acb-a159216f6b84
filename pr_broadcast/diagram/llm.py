"""
LLM Diagram Generator

This module provides functionality to generate Mermaid diagrams using LLM to visualize
the impact of changes on domains.
"""

import os
import json
import logging
import asyncio
import aiohttp
from typing import Dict, List, Any, Optional, Set, Tuple

from pr_broadcast.domain.taxonomy import DomainTaxonomy

logger = logging.getLogger(__name__)


class LLMDiagramGenerator:
    """
    Generates Mermaid diagrams using LLM to visualize the impact of changes on domains.
    
    Key features:
    1. Extracts domain diagrams from the taxonomy
    2. Combines with function changes
    3. Uses LLM to generate updated diagrams
    4. Propagates changes upward through the hierarchy
    """

    def __init__(
        self,
        taxonomy_json_path: str,
        llm_api_key: Optional[str] = None,
        llm_model: str = "gpt-4"
    ):
        """
        Initialize the LLM diagram generator.

        Args:
            taxonomy_json_path: Path to the domain taxonomy JSON file
            llm_api_key: API key for the LLM service (OpenAI or OpenRouter)
            llm_model: LLM model to use
        """
        self.taxonomy_json_path = taxonomy_json_path
        self.llm_api_key = llm_api_key
        self.llm_model = llm_model
        
        # Initialize domain taxonomy
        self.domain_taxonomy = DomainTaxonomy(taxonomy_json_path)
        
        logger.info(f"Initialized LLM diagram generator with model {llm_model}")

    async def generate_leaf_domain_diagram(
        self,
        domain: str,
        original_diagram: str,
        function_changes: List[Dict[str, Any]]
    ) -> str:
        """
        Generate a Mermaid diagram for a leaf domain showing the impact of changes.

        Args:
            domain: Domain name
            original_diagram: Original Mermaid diagram for the domain
            function_changes: List of function changes in the domain

        Returns:
            Updated Mermaid diagram as a string
        """
        # Prepare the prompt for the LLM
        prompt = self._prepare_leaf_domain_prompt(domain, original_diagram, function_changes)
        
        # Call the LLM to generate the diagram
        response = await self._call_llm(prompt)
        
        # Extract the Mermaid diagram from the response
        diagram = self._extract_mermaid_from_response(response)
        
        return diagram

    async def generate_parent_domain_diagram(
        self,
        domain: str,
        original_diagram: str,
        child_diagrams: Dict[str, Dict[str, Any]]
    ) -> str:
        """
        Generate a Mermaid diagram for a parent domain showing the impact of changes.

        Args:
            domain: Domain name
            original_diagram: Original Mermaid diagram for the domain
            child_diagrams: Dictionary mapping child domains to diagram information

        Returns:
            Updated Mermaid diagram as a string
        """
        # Prepare the prompt for the LLM
        prompt = self._prepare_parent_domain_prompt(domain, original_diagram, child_diagrams)
        
        # Call the LLM to generate the diagram
        response = await self._call_llm(prompt)
        
        # Extract the Mermaid diagram from the response
        diagram = self._extract_mermaid_from_response(response)
        
        return diagram

    def _prepare_leaf_domain_prompt(
        self,
        domain: str,
        original_diagram: str,
        function_changes: List[Dict[str, Any]]
    ) -> str:
        """
        Prepare a prompt for generating a leaf domain diagram.

        Args:
            domain: Domain name
            original_diagram: Original Mermaid diagram for the domain
            function_changes: List of function changes in the domain

        Returns:
            Prompt for the LLM
        """
        # Format function changes for the prompt
        formatted_changes = []
        for fc in function_changes:
            formatted_changes.append(f"Function: {fc['function_name']}")
            formatted_changes.append(f"Change Type: {fc['change_type']}")
            formatted_changes.append(f"File Path: {fc['file_path']}")
            formatted_changes.append(f"Diff Content:\n```diff\n{fc['diff_content']}\n```")
            formatted_changes.append("")
        
        # Build the prompt
        prompt = f"""
You are an expert in software architecture and visualization. Your task is to update a Mermaid diagram
for a domain to show the impact of code changes.

Domain: {domain}

Original Diagram:
```mermaid
{original_diagram}
```

Function Changes:
{formatted_changes}

Instructions:
1. Analyze the original diagram and the function changes.
2. Create an updated Mermaid diagram that shows the impact of the changes.
3. Use color coding to highlight the affected components:
   - Added functions/components: Green
   - Modified functions/components: Yellow
   - Deleted functions/components: Red
4. Maintain the overall structure of the original diagram.
5. Add new components or connections if necessary.
6. Include a legend explaining the color coding.

Please provide ONLY the updated Mermaid diagram without any additional text.
"""
        
        return prompt

    def _prepare_parent_domain_prompt(
        self,
        domain: str,
        original_diagram: str,
        child_diagrams: Dict[str, Dict[str, Any]]
    ) -> str:
        """
        Prepare a prompt for generating a parent domain diagram.

        Args:
            domain: Domain name
            original_diagram: Original Mermaid diagram for the domain
            child_diagrams: Dictionary mapping child domains to diagram information

        Returns:
            Prompt for the LLM
        """
        # Format child diagrams for the prompt
        formatted_children = []
        for child_domain, child_info in child_diagrams.items():
            child_name = child_domain.split(" -> ")[-1]
            impact_diagram = child_info["impact_diagram"]
            changes = child_info["changes"]
            
            formatted_children.append(f"Child Domain: {child_name}")
            formatted_children.append(f"Number of Changed Functions: {len(changes['functions'])}")
            formatted_children.append(f"Impact Diagram:\n```mermaid\n{impact_diagram}\n```")
            formatted_children.append("")
        
        # Build the prompt
        prompt = f"""
You are an expert in software architecture and visualization. Your task is to update a Mermaid diagram
for a parent domain to show the propagated impact of code changes from its child domains.

Parent Domain: {domain}

Original Diagram:
```mermaid
{original_diagram}
```

Child Domain Changes:
{formatted_children}

Instructions:
1. Analyze the original diagram and the child domain changes.
2. Create an updated Mermaid diagram that shows the propagated impact of the changes.
3. Use color coding to highlight the affected components:
   - Components with direct changes: Bright colors (Green, Yellow, Red)
   - Components with propagated changes: Lighter colors
4. Maintain the overall structure of the original diagram.
5. Add new components or connections if necessary.
6. Include a legend explaining the color coding.

Please provide ONLY the updated Mermaid diagram without any additional text.
"""
        
        return prompt

    async def _call_llm(self, prompt: str) -> str:
        """
        Call the LLM to generate a response.

        Args:
            prompt: Prompt for the LLM

        Returns:
            LLM response as a string
        """
        # Check if we're using OpenAI or OpenRouter
        if "openrouter.ai" in self.llm_model:
            return await self._call_openrouter(prompt)
        else:
            return await self._call_openai(prompt)

    async def _call_openai(self, prompt: str) -> str:
        """
        Call the OpenAI API to generate a response.

        Args:
            prompt: Prompt for the LLM

        Returns:
            LLM response as a string
        """
        if not self.llm_api_key:
            raise ValueError("OpenAI API key is required")
        
        url = "https://api.openai.com/v1/chat/completions"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.llm_api_key}"
        }
        
        data = {
            "model": self.llm_model,
            "messages": [
                {"role": "system", "content": "You are an expert in software architecture and visualization."},
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.7,
            "max_tokens": 2000
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=data) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"Failed to call OpenAI API: {error_text}")
                
                result = await response.json()
                
                # Extract the response content
                response_content = result["choices"][0]["message"]["content"]
                
                return response_content

    async def _call_openrouter(self, prompt: str) -> str:
        """
        Call the OpenRouter API to generate a response.

        Args:
            prompt: Prompt for the LLM

        Returns:
            LLM response as a string
        """
        if not self.llm_api_key:
            raise ValueError("OpenRouter API key is required")
        
        url = "https://openrouter.ai/api/v1/chat/completions"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.llm_api_key}",
            "HTTP-Referer": "https://bracket.com",
            "X-Title": "PR Broadcast"
        }
        
        data = {
            "model": self.llm_model,
            "messages": [
                {"role": "system", "content": "You are an expert in software architecture and visualization."},
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.7,
            "max_tokens": 2000
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=data) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"Failed to call OpenRouter API: {error_text}")
                
                result = await response.json()
                
                # Extract the response content
                response_content = result["choices"][0]["message"]["content"]
                
                return response_content

    def _extract_mermaid_from_response(self, response: str) -> str:
        """
        Extract the Mermaid diagram from the LLM response.

        Args:
            response: LLM response as a string

        Returns:
            Mermaid diagram as a string
        """
        # Check if the response contains a Mermaid diagram
        if "```mermaid" in response:
            # Extract the diagram
            start_index = response.find("```mermaid")
            end_index = response.find("```", start_index + 10)
            
            if end_index > start_index:
                # Extract the diagram content
                diagram = response[start_index + 10:end_index].strip()
                return diagram
        
        # If no Mermaid diagram found, return the response as is
        return response
