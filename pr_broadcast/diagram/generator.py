"""
Diagram Generator

This module provides functionality to generate Mermaid diagrams that visualize
the impact of changes on domains.
"""

import os
import logging
from typing import Dict, List, Any, Optional, Set, Tuple

from pr_broadcast.diff.models import FunctionChange, ChangeType
from pr_broadcast.domain.taxonomy import DomainTaxonomy

logger = logging.getLogger(__name__)


class DiagramGenerator:
    """
    Generates Mermaid diagrams that visualize the impact of changes on domains.
    
    This class provides methods to generate various types of diagrams showing
    the impact of changes on domains and functions.
    """

    def __init__(self, output_dir: str):
        """
        Initialize the diagram generator.

        Args:
            output_dir: Directory to save generated diagrams
        """
        self.output_dir = output_dir
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Create subdirectories for different diagram types
        self.hierarchy_dir = os.path.join(output_dir, "hierarchy")
        self.domain_dir = os.path.join(output_dir, "domains")
        self.function_dir = os.path.join(output_dir, "functions")
        
        os.makedirs(self.hierarchy_dir, exist_ok=True)
        os.makedirs(self.domain_dir, exist_ok=True)
        os.makedirs(self.function_dir, exist_ok=True)
        
        logger.info(f"Initialized diagram generator with output directory {output_dir}")

    def generate_diagrams(
        self,
        mr_details: Dict[str, Any],
        function_changes: List[FunctionChange],
        domain_impact: Dict[str, Dict[str, Any]],
        taxonomy_json_path: str
    ) -> Dict[str, str]:
        """
        Generate impact diagrams.

        Args:
            mr_details: Merge request details
            function_changes: List of function changes
            domain_impact: Dictionary mapping domains to impact information
            taxonomy_json_path: Path to the domain taxonomy JSON file

        Returns:
            Dictionary mapping diagram types to file paths
        """
        # Initialize domain taxonomy
        domain_taxonomy = DomainTaxonomy(taxonomy_json_path)
        
        # Generate diagrams
        diagrams = {}
        
        # Generate hierarchy impact diagram
        hierarchy_diagram = self._generate_hierarchy_impact_diagram(
            mr_details,
            domain_impact,
            domain_taxonomy
        )
        hierarchy_path = os.path.join(self.hierarchy_dir, f"hierarchy_impact_{mr_details['iid']}.md")
        self._save_diagram(hierarchy_diagram, hierarchy_path)
        diagrams["hierarchy"] = hierarchy_path
        
        # Generate domain impact diagrams for each affected domain
        domain_diagrams = {}
        for domain, impact in domain_impact.items():
            if impact["direct_impact"]:
                domain_diagram = self._generate_domain_impact_diagram(
                    domain,
                    impact,
                    domain_taxonomy
                )
                domain_path = os.path.join(
                    self.domain_dir,
                    f"domain_impact_{self._sanitize_filename(domain)}_{mr_details['iid']}.md"
                )
                self._save_diagram(domain_diagram, domain_path)
                domain_diagrams[domain] = domain_path
        
        diagrams["domains"] = domain_diagrams
        
        # Generate function impact diagrams for each changed function
        function_diagrams = {}
        for change in function_changes:
            function_diagram = self._generate_function_impact_diagram(change)
            function_path = os.path.join(
                self.function_dir,
                f"function_impact_{self._sanitize_filename(change.function_name)}_{mr_details['iid']}.md"
            )
            self._save_diagram(function_diagram, function_path)
            function_diagrams[change.function_signature] = function_path
        
        diagrams["functions"] = function_diagrams
        
        # Generate summary diagram
        summary_diagram = self._generate_summary_diagram(
            mr_details,
            function_changes,
            domain_impact
        )
        summary_path = os.path.join(self.output_dir, f"summary_impact_{mr_details['iid']}.md")
        self._save_diagram(summary_diagram, summary_path)
        diagrams["summary"] = summary_path
        
        logger.info(f"Generated {len(diagrams)} impact diagrams")
        return diagrams

    def _generate_hierarchy_impact_diagram(
        self,
        mr_details: Dict[str, Any],
        domain_impact: Dict[str, Dict[str, Any]],
        domain_taxonomy: DomainTaxonomy
    ) -> str:
        """
        Generate a hierarchy impact diagram.

        Args:
            mr_details: Merge request details
            domain_impact: Dictionary mapping domains to impact information
            domain_taxonomy: Domain taxonomy utilities

        Returns:
            Mermaid diagram as a string
        """
        # Group domains by level
        domain_levels = {}
        
        for domain in domain_impact.keys():
            # Count the number of "->" to determine the level
            level = domain.count("->") if "->" in domain else 0
            
            if level not in domain_levels:
                domain_levels[level] = []
            
            domain_levels[level].append(domain)
        
        # Start building the diagram
        diagram = f"# Hierarchy Impact Diagram for MR !{mr_details['iid']}\n\n"
        diagram += f"## {mr_details['title']}\n\n"
        diagram += "```mermaid\nflowchart TD\n"
        
        # Add a node for the MR
        diagram += f"  MR[\"MR !{mr_details['iid']}\"]:::mr\n\n"
        
        # Add nodes for each domain
        node_ids = {}
        for level in sorted(domain_levels.keys()):
            for domain in domain_levels[level]:
                # Create a unique node ID
                node_id = f"D{len(node_ids)}"
                node_ids[domain] = node_id
                
                # Get the domain name (last part of the path)
                domain_name = domain.split(" -> ")[-1] if " -> " in domain else domain
                
                # Get the impact information
                impact = domain_impact[domain]
                severity = impact["severity"]
                direct = impact["direct_impact"]
                
                # Determine the node style based on severity and direct impact
                style_class = f"{severity}{'_direct' if direct else '_indirect'}"
                
                # Add the node
                diagram += f"  {node_id}[\"{domain_name}\"]:::{style_class}\n"
        
        # Add edges between domains
        for domain, node_id in node_ids.items():
            # Get the parent domain
            parent_domain = domain_taxonomy.get_parent_domain(domain)
            
            # Skip if no parent or parent is not in the diagram
            if not parent_domain or parent_domain not in node_ids:
                # Connect to the MR instead
                diagram += f"  MR --> {node_id}\n"
                continue
            
            # Get the parent node ID
            parent_node_id = node_ids[parent_domain]
            
            # Add the edge
            diagram += f"  {parent_node_id} --> {node_id}\n"
        
        # Add style classes
        diagram += "\n  %% Style classes\n"
        diagram += "  classDef mr fill:#F0F8FF,stroke:#87CEFA,stroke-width:2px\n"
        diagram += "  classDef high_direct fill:#FF5252,stroke:#FF0000,stroke-width:2px\n"
        diagram += "  classDef medium_direct fill:#FFD700,stroke:#FFA500,stroke-width:2px\n"
        diagram += "  classDef low_direct fill:#90EE90,stroke:#32CD32,stroke-width:2px\n"
        diagram += "  classDef high_indirect fill:#FFCDD2,stroke:#FF5252,stroke-width:1px\n"
        diagram += "  classDef medium_indirect fill:#FFF9C4,stroke:#FFD700,stroke-width:1px\n"
        diagram += "  classDef low_indirect fill:#C8E6C9,stroke:#90EE90,stroke-width:1px\n"
        
        diagram += "```\n"
        
        return diagram

    def _generate_domain_impact_diagram(
        self,
        domain: str,
        impact: Dict[str, Any],
        domain_taxonomy: DomainTaxonomy
    ) -> str:
        """
        Generate a domain impact diagram.

        Args:
            domain: Domain name
            impact: Impact information for the domain
            domain_taxonomy: Domain taxonomy utilities

        Returns:
            Mermaid diagram as a string
        """
        # Get the domain name (last part of the path)
        domain_name = domain.split(" -> ")[-1] if " -> " in domain else domain
        
        # Get the changes for this domain
        domain_changes = impact["changes"]
        
        # Group changes by file
        file_changes = {}
        for change in domain_changes:
            file_path = change["file_path"]
            
            if file_path not in file_changes:
                file_changes[file_path] = []
            
            file_changes[file_path].append(change)
        
        # Start building the diagram
        diagram = f"# Domain Impact Diagram for {domain}\n\n"
        diagram += f"## Impact Summary\n\n"
        diagram += f"- **Severity**: {impact['severity']}\n"
        diagram += f"- **Impact Score**: {impact['impact_score']:.2f}\n"
        diagram += f"- **Direct Impact**: {'Yes' if impact['direct_impact'] else 'No'}\n"
        diagram += f"- **Indirect Impact**: {'Yes' if impact['indirect_impact'] else 'No'}\n"
        diagram += f"- **Changed Functions**: {len(domain_changes)}\n\n"
        
        diagram += "```mermaid\nflowchart TD\n"
        
        # Add a node for the domain
        diagram += f"  D[\"{domain_name}\"]:::domain\n\n"
        
        # Add nodes for each file
        file_ids = {}
        for file_path in file_changes.keys():
            # Create a unique node ID
            file_id = f"F{len(file_ids)}"
            file_ids[file_path] = file_id
            
            # Get the file name (last part of the path)
            file_name = os.path.basename(file_path)
            
            # Add the node
            diagram += f"  {file_id}[\"{file_name}\"]:::file\n"
            
            # Connect to the domain
            diagram += f"  D --> {file_id}\n"
        
        # Add nodes for each function
        for file_path, changes in file_changes.items():
            file_id = file_ids[file_path]
            
            for i, change in enumerate(changes):
                # Create a unique node ID
                func_id = f"FN{len(file_ids) + i}"
                
                # Get the function name
                func_name = change["function_name"]
                
                # Get the change type
                change_type = change["change_type"].lower()
                
                # Add the node
                diagram += f"  {func_id}[\"{func_name}\"]:::{change_type}\n"
                
                # Connect to the file
                diagram += f"  {file_id} --> {func_id}\n"
        
        # Add style classes
        diagram += "\n  %% Style classes\n"
        diagram += "  classDef domain fill:#F0F8FF,stroke:#87CEFA,stroke-width:2px\n"
        diagram += "  classDef file fill:#E1F5FE,stroke:#4FC3F7,stroke-width:1px\n"
        diagram += "  classDef added fill:#90EE90,stroke:#32CD32,stroke-width:2px\n"
        diagram += "  classDef modified fill:#FFD700,stroke:#FFA500,stroke-width:2px\n"
        diagram += "  classDef deleted fill:#FF5252,stroke:#FF0000,stroke-width:2px\n"
        
        diagram += "```\n"
        
        # Add function changes
        diagram += "\n## Function Changes\n\n"
        
        for change in domain_changes:
            func_name = change["function_name"]
            change_type = change["change_type"]
            
            diagram += f"### {func_name} ({change_type})\n\n"
            diagram += "```diff\n"
            diagram += change["diff_content"]
            diagram += "\n```\n\n"
        
        return diagram

    def _generate_function_impact_diagram(self, change: FunctionChange) -> str:
        """
        Generate a function impact diagram.

        Args:
            change: Function change

        Returns:
            Mermaid diagram as a string
        """
        # Get the function name and file path
        func_name = change.function_name
        file_path = change.file_path
        change_type = change.change_type
        
        # Start building the diagram
        diagram = f"# Function Impact Diagram for {func_name}\n\n"
        diagram += f"## Impact Summary\n\n"
        diagram += f"- **File Path**: {file_path}\n"
        diagram += f"- **Change Type**: {change_type}\n"
        diagram += f"- **Impact Score**: {change.impact_score:.2f}\n\n"
        
        # Add a simple mermaid diagram
        diagram += "## Visual Representation\n\n"
        diagram += "```mermaid\nflowchart LR\n"
        
        # Add nodes for the file and function
        diagram += f"  F[\"File: {os.path.basename(file_path)}\"]:::file\n"
        diagram += f"  FN[\"{func_name}\"]:::{change_type.lower()}\n"
        
        # Connect the file to the function
        diagram += f"  F --> FN\n"
        
        # Add style classes
        diagram += "\n  %% Style classes\n"
        diagram += "  classDef file fill:#F0F8FF,stroke:#87CEFA,stroke-width:1px\n"
        diagram += "  classDef added fill:#90EE90,stroke:#32CD32,stroke-width:2px\n"
        diagram += "  classDef modified fill:#FFD700,stroke:#FFA500,stroke-width:2px\n"
        diagram += "  classDef deleted fill:#FF5252,stroke:#FF0000,stroke-width:2px\n"
        
        diagram += "```\n"
        
        # Add the diff content
        diagram += "\n## Diff Content\n\n"
        diagram += "```diff\n"
        diagram += change.diff_content
        diagram += "\n```\n"
        
        return diagram

    def _generate_summary_diagram(
        self,
        mr_details: Dict[str, Any],
        function_changes: List[FunctionChange],
        domain_impact: Dict[str, Dict[str, Any]]
    ) -> str:
        """
        Generate a summary diagram.

        Args:
            mr_details: Merge request details
            function_changes: List of function changes
            domain_impact: Dictionary mapping domains to impact information

        Returns:
            Mermaid diagram as a string
        """
        # Count changes by type
        change_counts = {
            ChangeType.ADDED: 0,
            ChangeType.MODIFIED: 0,
            ChangeType.DELETED: 0
        }
        
        for change in function_changes:
            change_counts[change.change_type] += 1
        
        # Count domains by impact severity
        impact_counts = {
            "high": 0,
            "medium": 0,
            "low": 0
        }
        
        for domain, impact in domain_impact.items():
            impact_counts[impact["severity"]] += 1
        
        # Start building the diagram
        diagram = f"# Summary Impact Diagram for MR !{mr_details['iid']}\n\n"
        diagram += f"## {mr_details['title']}\n\n"
        diagram += f"- **Author**: {mr_details['author']['name']}\n"
        diagram += f"- **Total Changed Functions**: {len(function_changes)}\n"
        diagram += f"- **Total Impacted Domains**: {len(domain_impact)}\n\n"
        
        # Add a mermaid diagram
        diagram += "## Function Changes\n\n"
        diagram += "```mermaid\npie title Function Changes\n"
        diagram += f"  \"Added\" : {change_counts[ChangeType.ADDED]}\n"
        diagram += f"  \"Modified\" : {change_counts[ChangeType.MODIFIED]}\n"
        diagram += f"  \"Deleted\" : {change_counts[ChangeType.DELETED]}\n"
        diagram += "```\n\n"
        
        diagram += "## Domain Impact\n\n"
        diagram += "```mermaid\npie title Domain Impact\n"
        diagram += f"  \"High\" : {impact_counts['high']}\n"
        diagram += f"  \"Medium\" : {impact_counts['medium']}\n"
        diagram += f"  \"Low\" : {impact_counts['low']}\n"
        diagram += "```\n\n"
        
        # Add most impacted domains
        diagram += "## Most Impacted Domains\n\n"
        
        # Sort domains by impact score
        sorted_domains = sorted(
            domain_impact.items(),
            key=lambda x: x[1]["impact_score"],
            reverse=True
        )
        
        # Take the top 5
        top_domains = sorted_domains[:5]
        
        diagram += "```mermaid\nflowchart TD\n"
        diagram += "  MR[\"MR !{mr_details['iid']}\"]:::mr\n"
        
        # Add nodes for each domain
        for i, (domain, impact) in enumerate(top_domains):
            # Create a unique node ID
            node_id = f"D{i}"
            
            # Get the domain name (last part of the path)
            domain_name = domain.split(" -> ")[-1] if " -> " in domain else domain
            
            # Get the severity
            severity = impact["severity"]
            
            # Add the node
            diagram += f"  {node_id}[\"{domain_name}\"]:::{severity}\n"
            
            # Connect to the MR
            diagram += f"  MR --> {node_id}\n"
        
        # Add style classes
        diagram += "\n  %% Style classes\n"
        diagram += "  classDef mr fill:#F0F8FF,stroke:#87CEFA,stroke-width:2px\n"
        diagram += "  classDef high fill:#FF5252,stroke:#FF0000,stroke-width:2px\n"
        diagram += "  classDef medium fill:#FFD700,stroke:#FFA500,stroke-width:2px\n"
        diagram += "  classDef low fill:#90EE90,stroke:#32CD32,stroke-width:2px\n"
        
        diagram += "```\n"
        
        return diagram

    def _save_diagram(self, diagram: str, file_path: str) -> None:
        """
        Save a diagram to a file.

        Args:
            diagram: Diagram content
            file_path: Path to save the diagram
        """
        # Create the directory if it doesn't exist
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # Save the diagram
        with open(file_path, 'w') as f:
            f.write(diagram)
        
        logger.info(f"Saved diagram to {file_path}")

    def _sanitize_filename(self, name: str) -> str:
        """
        Sanitize a name for use in a filename.

        Args:
            name: Name to sanitize

        Returns:
            Sanitized name
        """
        # Replace invalid characters with underscores
        return name.replace(" ", "_").replace("/", "_").replace("\\", "_").replace(":", "_")
