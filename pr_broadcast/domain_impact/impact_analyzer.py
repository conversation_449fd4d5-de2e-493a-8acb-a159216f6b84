"""
Domain Impact Analyzer

This module provides functionality to map changed functions to domains and analyze
the direct impact of changes on the domain hierarchy.
"""

import os
import json
import yaml
import logging
from typing import Dict, List, Any, Optional, Set, Tuple

from pr_broadcast.diff_analysis.function_diff_analyzer import FunctionChange, ChangeType

logger = logging.getLogger(__name__)

class DomainImpactAnalyzer:
    """
    Maps changed functions to domains and analyzes the direct impact of changes.
    """

    def __init__(self, taxonomy_json_path: str, domain_traces_yaml_path: str):
        """
        Initialize the domain impact analyzer.

        Args:
            taxonomy_json_path: Path to the domain taxonomy JSON file
            domain_traces_yaml_path: Path to the domain traces YAML file
        """
        self.taxonomy_json_path = taxonomy_json_path
        self.domain_traces_yaml_path = domain_traces_yaml_path
        
        # Load domain taxonomy and traces
        self.taxonomy = self._load_taxonomy()
        self.domain_traces = self._load_domain_traces()
        
        # Build function-to-domain mapping
        self.function_to_domains = self._build_function_to_domain_mapping()

    def analyze_impact(self, function_changes: List[FunctionChange]) -> Dict[str, Any]:
        """
        Analyze the direct impact of function changes on domains.

        Args:
            function_changes: List of function changes

        Returns:
            Dictionary mapping domains to impact information
        """
        # Initialize impact dictionary
        domain_impact = {}
        
        # Process each function change
        for change in function_changes:
            # Get the function signature
            function_signature = self._normalize_function_signature(change.function_signature, change.file_path)
            
            # Find domains containing this function
            domains = self._find_domains_for_function(function_signature)
            
            # If no domains found, try with just the function name
            if not domains:
                function_name = change.function_name
                domains = self._find_domains_for_function_name(function_name, change.file_path)
            
            print(f"Domains: For fn: {function_signature} ; {domains} domains found")
            
            # Update impact for each domain
            for domain in domains:
                if domain not in domain_impact:
                    domain_impact[domain] = {
                        "changes": [],
                        "impact_score": 0.0,
                        "severity": "low",
                        "direct_impact": True,
                        "indirect_impact": False
                    }
                
                # Add this change to the domain's changes
                domain_impact[domain]["changes"].append(change.to_dict())
                
                # Update impact score
                domain_impact[domain]["impact_score"] += change.impact_score
            
            # If no domains found, log a warning
            if not domains:
                logger.warning(f"No domains found for function: {function_signature}")
        
        # Calculate severity for each domain
        for domain, impact in domain_impact.items():
            impact["severity"] = self._calculate_severity(impact["impact_score"])
        
        logger.info(f"Analyzed direct impact on {len(domain_impact)} domains")
        return domain_impact

    def _load_taxonomy(self) -> Dict[str, Any]:
        """
        Load the domain taxonomy from JSON.

        Returns:
            Dictionary containing domain taxonomy
        """
        try:
            with open(self.taxonomy_json_path, 'r') as f:
                taxonomy = json.load(f)
            
            logger.info(f"Loaded domain taxonomy from {self.taxonomy_json_path}")
            return taxonomy
        except Exception as e:
            logger.error(f"Failed to load domain taxonomy: {e}")
            return {}

    def _load_domain_traces(self) -> Dict[str, List[str]]:
        """
        Load domain traces from YAML.

        Returns:
            Dictionary mapping domain traces to lists of function signatures
        """
        try:
            with open(self.domain_traces_yaml_path, 'r') as f:
                traces_data = yaml.safe_load(f)
            
            domain_traces = traces_data.get('domain_traces', {})
            
            logger.info(f"Loaded domain traces from {self.domain_traces_yaml_path}")
            return domain_traces
        except Exception as e:
            logger.error(f"Failed to load domain traces: {e}")
            return {}

    def _build_function_to_domain_mapping(self) -> Dict[str, List[str]]:
        """
        Build a mapping from function signatures to domains.

        Returns:
            Dictionary mapping function signatures to lists of domains
        """
        function_to_domains = {}
        
        # Process each domain trace
        for domain, functions in self.domain_traces.items():
            for function in functions:
                # Normalize function signature
                normalized_function = self._normalize_function_signature(function)
                
                if normalized_function not in function_to_domains:
                    function_to_domains[normalized_function] = []
                
                function_to_domains[normalized_function].append(domain)
        
        logger.info(f"Built function-to-domain mapping with {len(function_to_domains)} functions")
        return function_to_domains

    def _normalize_function_signature(
        self,
        function_signature: str,
        file_path: Optional[str] = None
    ) -> str:
        """
        Normalize a function signature for consistent matching.

        Args:
            function_signature: Function signature
            file_path: Optional file path to prepend

        Returns:
            Normalized function signature
        """
        # Remove whitespace
        normalized = function_signature.strip()
        
        # If the signature already includes a file path, use it as is
        if ':' in normalized:
            return normalized
        
        # If a file path is provided, prepend it
        if file_path:
            # Extract the file name from the path
            file_name = os.path.basename(file_path)
            return f"{file_name}:{normalized}"
        
        return normalized

    def _find_domains_for_function(self, function_signature: str) -> List[str]:
        """
        Find domains containing a function.

        Args:
            function_signature: Function signature

        Returns:
            List of domains containing the function
        """
        return self.function_to_domains.get(function_signature, [])

    def _find_domains_for_function_name(
        self,
        function_name: str,
        file_path: Optional[str] = None
    ) -> List[str]:
        """
        Find domains containing a function by name.

        Args:
            function_name: Function name
            file_path: Optional file path to narrow the search

        Returns:
            List of domains containing the function
        """
        domains = []
        
        # Extract file name from path
        file_name = os.path.basename(file_path) if file_path else None
        
        # Search for functions with matching name
        for signature, signature_domains in self.function_to_domains.items():
            # Check if the signature contains the function name
            if f":{function_name}" in signature or f".{function_name}" in signature:
                # If file path is provided, check if the signature contains the file name
                if file_name and file_name not in signature:
                    continue
                
                domains.extend(signature_domains)
        
        return list(set(domains))  # Remove duplicates

    def _calculate_severity(self, impact_score: float) -> str:
        """
        Calculate severity based on impact score.

        Args:
            impact_score: Impact score

        Returns:
            Severity level (low, medium, high)
        """
        if impact_score >= 2.0:
            return "high"
        elif impact_score >= 1.0:
            return "medium"
        else:
            return "low"
