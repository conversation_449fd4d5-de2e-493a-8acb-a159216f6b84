"""
Impact Propagator

This module provides functionality to propagate the impact of changes through
the domain hierarchy and call graph.
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional, Set, Tuple

from pr_broadcast.diff_analysis.function_diff_analyzer import FunctionChange, ChangeType

logger = logging.getLogger(__name__)

class ImpactPropagator:
    """
    Propagates the impact of changes through the domain hierarchy and call graph.
    """

    def __init__(self, taxonomy_json_path: str):
        """
        Initialize the impact propagator.

        Args:
            taxonomy_json_path: Path to the domain taxonomy JSON file
        """
        self.taxonomy_json_path = taxonomy_json_path
        
        # Load domain taxonomy
        self.taxonomy = self._load_taxonomy()
        
        # Build domain hierarchy
        self.domain_hierarchy = self._build_domain_hierarchy()
        
        # Build domain parent mapping
        self.domain_parents = self._build_domain_parent_mapping()

    def propagate_impact(
        self,
        direct_impact: Dict[str, Any],
        function_changes: List[FunctionChange]
    ) -> Dict[str, Any]:
        """
        Propagate the impact of changes through the domain hierarchy and call graph.

        Args:
            direct_impact: Dictionary mapping domains to direct impact information
            function_changes: List of function changes

        Returns:
            Dictionary mapping domains to combined direct and indirect impact information
        """
        # Start with the direct impact
        all_impact = direct_impact.copy()
        
        # Propagate impact up the domain hierarchy
        hierarchy_impact = self._propagate_up_hierarchy(direct_impact)
        
        # Merge hierarchy impact with direct impact
        for domain, impact in hierarchy_impact.items():
            if domain in all_impact:
                # Domain already has direct impact - update with indirect impact
                all_impact[domain]["indirect_impact"] = True
                
                # Only update impact score if it's higher
                if impact["impact_score"] > all_impact[domain]["impact_score"]:
                    all_impact[domain]["impact_score"] = impact["impact_score"]
                    all_impact[domain]["severity"] = impact["severity"]
            else:
                # Domain only has indirect impact
                all_impact[domain] = impact
        
        # Propagate impact through call graph (if available)
        # This would require additional information about function calls
        # For now, we'll just use the hierarchy propagation
        
        logger.info(f"Propagated impact to {len(all_impact)} domains")
        return all_impact

    def _load_taxonomy(self) -> Dict[str, Any]:
        """
        Load the domain taxonomy from JSON.

        Returns:
            Dictionary containing domain taxonomy
        """
        try:
            with open(self.taxonomy_json_path, 'r') as f:
                taxonomy = json.load(f)
            
            logger.info(f"Loaded domain taxonomy from {self.taxonomy_json_path}")
            return taxonomy
        except Exception as e:
            logger.error(f"Failed to load domain taxonomy: {e}")
            return {}

    def _build_domain_hierarchy(self) -> Dict[str, List[str]]:
        """
        Build a mapping from domains to their child domains.

        Returns:
            Dictionary mapping domains to lists of child domains
        """
        domain_hierarchy = {}
        
        def process_node(node, parent_path=None):
            # Get the full path of this node
            node_path = node.get('full_path')
            
            if not node_path:
                # Root node - process children
                for child in node.get('children', []):
                    process_node(child)
                return
            
            # Add this node to its parent's children
            if parent_path:
                if parent_path not in domain_hierarchy:
                    domain_hierarchy[parent_path] = []
                
                domain_hierarchy[parent_path].append(node_path)
            
            # Process children
            for child in node.get('children', []):
                process_node(child, node_path)
        
        # Process the taxonomy tree
        process_node(self.taxonomy)
        
        logger.info(f"Built domain hierarchy with {len(domain_hierarchy)} parent domains")
        return domain_hierarchy

    def _build_domain_parent_mapping(self) -> Dict[str, str]:
        """
        Build a mapping from domains to their parent domains.

        Returns:
            Dictionary mapping domains to their parent domains
        """
        domain_parents = {}
        
        # Invert the hierarchy mapping
        for parent, children in self.domain_hierarchy.items():
            for child in children:
                domain_parents[child] = parent
        
        logger.info(f"Built domain parent mapping with {len(domain_parents)} child domains")
        return domain_parents

    def _propagate_up_hierarchy(self, direct_impact: Dict[str, Any]) -> Dict[str, Any]:
        """
        Propagate impact up the domain hierarchy.

        Args:
            direct_impact: Dictionary mapping domains to direct impact information

        Returns:
            Dictionary mapping domains to indirect impact information
        """
        indirect_impact = {}
        
        # Process each directly impacted domain
        for domain, impact in direct_impact.items():
            # Get the impact score
            impact_score = impact["impact_score"]
            
            # Propagate up the hierarchy with decreasing impact
            current_domain = domain
            propagation_factor = 0.7  # Impact decreases as we go up the hierarchy
            
            while current_domain in self.domain_parents:
                # Get the parent domain
                parent_domain = self.domain_parents[current_domain]
                
                # Reduce the impact score
                impact_score *= propagation_factor
                
                # Add to indirect impact
                if parent_domain not in indirect_impact:
                    indirect_impact[parent_domain] = {
                        "changes": [],
                        "impact_score": 0.0,
                        "severity": "low",
                        "direct_impact": False,
                        "indirect_impact": True
                    }
                
                # Update impact score
                indirect_impact[parent_domain]["impact_score"] += impact_score
                
                # Move up to the parent
                current_domain = parent_domain
        
        # Calculate severity for each domain
        for domain, impact in indirect_impact.items():
            impact["severity"] = self._calculate_severity(impact["impact_score"])
        
        logger.info(f"Propagated impact up hierarchy to {len(indirect_impact)} domains")
        return indirect_impact

    def _calculate_severity(self, impact_score: float) -> str:
        """
        Calculate severity based on impact score.

        Args:
            impact_score: Impact score

        Returns:
            Severity level (low, medium, high)
        """
        if impact_score >= 2.0:
            return "high"
        elif impact_score >= 1.0:
            return "medium"
        else:
            return "low"
