# PR Broadcast

PR Broadcast is a powerful feature that visualizes the impact of code changes in merge requests/pull requests on your codebase's domain architecture. It helps reviewers understand the scope and implications of changes by generating specialized Mermaid diagrams that highlight affected domains and functions.

## Features

- **MR/PR Diff Visualization**: Fetches GitLab merge request diffs, identifies changed functions, and generates specialized Mermaid diagrams
- **Domain Impact Highlighting**: Shows which domains are directly altered and which might be indirectly affected
- **Function-Level Analysis**: Analyzes changes at the function level to provide detailed insights
- **Hierarchical Impact Propagation**: Propagates impact through the domain hierarchy to identify potential ripple effects
- **Rich Visualization**: Generates multiple diagram types to visualize changes from different perspectives
- **LLM Integration**: Uses LLM to generate enhanced diagrams showing the impact of changes

## Components

1. **Diff Analysis**
   - `GitlabDiffFetcher`: Fetches PR/MR diffs from GitLab
   - `FunctionDiffAnalyzer`: Analyzes diffs to identify changed functions
   - `EnhancedFunctionDiffAnalyzer`: Enhanced version with improved function signature extraction

2. **Domain Mapping**
   - `DomainMapper`: Maps functions to domains in the domain taxonomy
   - `DomainTaxonomy`: Utilities for working with domain taxonomies

3. **Impact Analysis**
   - `ImpactAnalyzer`: Analyzes the impact of changes on domains
   - `ImpactPropagator`: Propagates impact through the domain hierarchy

4. **Diagram Generation**
   - `DiagramGenerator`: Generates standard Mermaid diagrams
   - `LLMDiagramGenerator`: Uses LLM to generate enhanced diagrams

5. **Output Management**
   - `OutputManager`: Manages output artifacts (diagrams, reports, etc.)

## Usage

### Command Line Interface

```bash
python -m pr_broadcast.cli \
  --gitlab-url https://gitlab.example.com \
  --gitlab-token YOUR_GITLAB_TOKEN \
  --project-id 12345 \
  --mr-iid 678 \
  --taxonomy-json /path/to/domain_taxonomy.json \
  --domain-traces-yaml /path/to/domain_traces.yaml \
  --output-dir /path/to/output \
  --include-indirect-impact \
  --use-llm-diagrams \
  --llm-api-key YOUR_LLM_API_KEY \
  --llm-model gpt-4 \
  --save-results
```

### Python API

```python
import asyncio
from pr_broadcast import PRBroadcastService

async def analyze_mr():
    service = PRBroadcastService(
        gitlab_url="https://gitlab.example.com",
        gitlab_token="YOUR_GITLAB_TOKEN",
        taxonomy_json_path="/path/to/domain_taxonomy.json",
        domain_traces_yaml_path="/path/to/domain_traces.yaml",
        output_dir="/path/to/output",
        llm_api_key="YOUR_LLM_API_KEY",
        llm_model="gpt-4"
    )

    results = await service.analyze_merge_request(
        project_id="12345",
        mr_iid=678,
        include_indirect_impact=True,
        use_llm_diagrams=True
    )

    print(f"Generated diagrams for {len(results['domain_impact'])} domains")

asyncio.run(analyze_mr())
```

## Generated Diagrams

PR Broadcast generates several types of diagrams:

1. **Leaf Domain Diagrams**: Detailed diagrams for leaf domains showing the impact of changes
2. **Parent Domain Diagrams**: Diagrams for parent domains showing the propagated impact from child domains
3. **Summary Reports**: JSON and Markdown reports summarizing the impact of changes
4. **HTML Reports**: Interactive HTML reports with Mermaid diagrams

## Requirements

- Python 3.7+
- Access to a GitLab instance with API token (for GitLab integration)
- Domain taxonomy JSON file
- Domain traces YAML file
- LLM API key (for LLM-based diagram generation)

## Installation

```bash
# Clone the repository
git clone https://github.com/your-org/bracket.git
cd bracket

# Install dependencies
pip install -r requirements.txt
```

## Testing

To test PR Broadcast with dummy data:

```bash
python test_pr_broadcast.py \
  --taxonomy-json /path/to/domain_taxonomy.json \
  --domain-traces-yaml /path/to/domain_traces.yaml \
  --dummy-data /path/to/dummy_data.json \
  --output-dir /path/to/output \
  --include-indirect-impact \
  --use-llm-diagrams \
  --llm-api-key YOUR_LLM_API_KEY \
  --open-browser
```

## Integration with GitLab CI/CD

You can integrate PR Broadcast with GitLab CI/CD to automatically generate impact diagrams for each merge request:

```yaml
pr_broadcast:
  stage: analyze
  script:
    - python -m pr_broadcast.cli --gitlab-url $CI_SERVER_URL --gitlab-token $GITLAB_TOKEN --project-id $CI_PROJECT_ID --mr-iid $CI_MERGE_REQUEST_IID --taxonomy-json domain_taxonomy.json --domain-traces-yaml domain_traces.yaml --output-dir pr_broadcast_output --include-indirect-impact --use-llm-diagrams --llm-api-key $LLM_API_KEY --save-results
  artifacts:
    paths:
      - pr_broadcast_output/
  only:
    - merge_requests
```

## Benefits

- **Improved Code Reviews**: Helps reviewers understand the impact of changes
- **Architectural Awareness**: Highlights how changes affect the domain architecture
- **Risk Assessment**: Identifies potential ripple effects of changes
- **Knowledge Sharing**: Helps team members understand the codebase architecture
- **Documentation**: Generates visual documentation of changes for future reference
