#!/usr/bin/env python3
"""
Test Codebase Explainer

This script tests the codebase explainer by generating an explanation from a sample taxonomy JSON file.
"""

import os
import sys
import asyncio
import logging

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from bracket_core.global_codebase_explainer import generate_codebase_explanation

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def main():
    """Main entry point for the script."""
    # Find a sample taxonomy JSON file
    sample_taxonomy_path = "./experiments/taxonomy-exp/data/mem0-test5/domain_taxonomy.json"
    output_md_path = "./codebase_explanation_test.md"
    
    if not os.path.exists(sample_taxonomy_path):
        logger.error(f"Sample taxonomy JSON file not found: {sample_taxonomy_path}")
        return 1
    
    try:
        # Generate the codebase explanation
        result = await generate_codebase_explanation(
            taxonomy_json_path=sample_taxonomy_path,
            output_md_path=output_md_path,
            openai_model="gpt-4o-mini",
            max_tokens=4000,
            temperature=0.7,
        )
        
        if result.success:
            logger.info(f"Codebase explanation generated successfully: {result.output_path}")
            return 0
        else:
            logger.error(f"Codebase explanation generation failed: {result.error_message}")
            return 1
        
    except Exception as e:
        logger.error(f"Error generating codebase explanation: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
