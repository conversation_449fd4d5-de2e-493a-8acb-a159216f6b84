#!/bin/bash

# Create a virtual environment for Bracket
VENV_PATH="/tmp/bracket_venv"

# Check if the virtual environment already exists
if [ ! -d "$VENV_PATH" ]; then
    echo "Creating virtual environment at $VENV_PATH"
    python3 -m venv "$VENV_PATH"
    
    # Activate the virtual environment and install dependencies
    source "$VENV_PATH/bin/activate"
    pip install tenacity openai python-dotenv
    
    echo "Virtual environment created and dependencies installed"
else
    echo "Virtual environment already exists at $VENV_PATH"
fi

echo "Setup complete"
