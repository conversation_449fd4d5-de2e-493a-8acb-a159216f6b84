#!/usr/bin/env python3
"""
Generate Codebase Explanation

This script generates a comprehensive explanation of a codebase based on a domain taxonomy JSON file.

Usage:
    python scripts/generate_codebase_explanation.py --taxonomy <path> --output <path>
"""

import os
import sys
import argparse
import asyncio
import logging
from typing import Optional

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from bracket_core.global_codebase_explainer import generate_codebase_explanation

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Define a callback function for streaming output
def stream_callback(chunk: str):
    """Callback function for streaming output.

    This function outputs chunks directly to stdout for immediate display.
    """
    # Print the chunk immediately without buffering
    print(chunk, end="", flush=True)

async def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Generate a codebase explanation from a domain taxonomy JSON file")

    parser.add_argument(
        "--taxonomy",
        required=True,
        help="Path to the domain taxonomy JSON file"
    )

    parser.add_argument(
        "--output",
        required=True,
        help="Path to save the output markdown file"
    )

    parser.add_argument(
        "--api-key",
        help="OpenAI API key (if not provided, will try to use environment variable)"
    )

    parser.add_argument(
        "--model",
        # default="gpt-4o-mini",
        default="o3-mini",
        help="OpenAI model to use (default: gpt-4o-mini)"
    )

    parser.add_argument(
        "--max-tokens",
        type=int,
        default=7000,
        help="Maximum tokens to generate (default: 4000)"
    )

    parser.add_argument(
        "--temperature",
        type=float,
        default=0.7,
        help="Sampling temperature (default: 0.7)"
    )

    parser.add_argument(
        "--stream",
        default=True,
        action="store_true",
        help="Enable streaming output"
    )

    parser.add_argument(
        "--use-openrouter",
        action="store_true",
        default=True,
        help="Use OpenRouter instead of OpenAI (default: True)"
    )

    args = parser.parse_args()

    try:
        # Generate the codebase explanation
        output_dir = args.output

        # Set up streaming callback if streaming is enabled
        callback = None
        if args.stream:
            callback = stream_callback
            logger.info("Streaming mode enabled")

        result = await generate_codebase_explanation(
            taxonomy_json_path=args.taxonomy,
            global_code_explain_md_path=os.path.join(output_dir, "global_overview_short2.md"),
            suggested_questions_md_path=os.path.join(output_dir, "suggested_questions_short2.md"),
            openai_api_key=args.api_key,
            openai_model=args.model,
            max_tokens=args.max_tokens,
            temperature=args.temperature,
            use_streaming=args.stream,
            stream_callback=callback,
            use_openrouter=args.use_openrouter,
        )

        # No buffer to flush with the new implementation

        if result.success:
            logger.info(f"Codebase explanation generated successfully: {result.output_path}")
            return 0
        else:
            logger.error(f"Codebase explanation generation failed: {result.error_message}")
            return 1

    except Exception as e:
        logger.error(f"Error generating codebase explanation: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

# Example usage:
# python scripts/generate_codebase_explanation.py --taxonomy /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/outputs/datadog_agent/taxonomy/taxonomy.json --output /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/outputs/datadog_agent/global_artifacts
