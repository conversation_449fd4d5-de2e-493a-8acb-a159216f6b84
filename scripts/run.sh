#!/bin/bash

# Run script for Bracket project
# This script provides options to run either the Python (bracket_core) or TypeScript (bracket_ext) project

set -e

function show_help {
    echo "Usage: ./scripts/run.sh [options]"
    echo ""
    echo "Options:"
    echo "  --core       Run the Python project (bracket_core)"
    echo "  --ext        Run the TypeScript project (bracket_ext)"
    echo "  --build-ext  Build the TypeScript project (bracket_ext)"
    echo "  --help       Show this help message"
    echo ""
    echo "Examples:"
    echo "  ./scripts/run.sh --core"
    echo "  ./scripts/run.sh --ext"
    echo "  ./scripts/run.sh --build-ext"
}

if [ $# -eq 0 ]; then
    show_help
    exit 1
fi

case "$1" in
    --core)
        echo "Running bracket_core..."
        poetry run bracket
        ;;
    --ext)
        echo "Running bracket_ext..."
        cd bracket_ext
        npm run dev
        ;;
    --build-ext)
        echo "Building bracket_ext..."
        cd bracket_ext
        npm run build
        ;;
    --help)
        show_help
        ;;
    *)
        echo "Unknown option: $1"
        show_help
        exit 1
        ;;
esac
