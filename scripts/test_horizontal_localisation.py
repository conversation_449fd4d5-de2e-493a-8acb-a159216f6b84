#!/usr/bin/env python3
# chmod +x scripts/test_horizontal_localisation.py  # Run this to make the script executable
"""
Test script for the horizontal layered approach for code localization.

This script demonstrates how to use the horizontal_localisation.py module
to find relevant functions in a codebase based on a user query.
"""

import os
import sys
import asyncio
import argparse
import logging

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the horizontal localisation module
from bracket_core.localisation.horizontal_localisation import find_relevant_functions

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def main():
    """Main entry point for the script."""
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description='Test the horizontal layered approach for code localization')
    parser.add_argument('--query', type=str, help='User query')
    parser.add_argument('--taxonomy', type=str, help='Path to the domain taxonomy JSON file')
    parser.add_argument('--output_dir', type=str, help='Directory to save LLM outputs and analysis')
    parser.add_argument('--semantic_fns', type=str, help='Path to the semantic documented functions parquet file')
    parser.add_argument('--model', type=str, default='gpt-4o-mini', help='OpenAI model for first pass')
    parser.add_argument('--second_pass_model', type=str, help='OpenAI model for second pass')
    parser.add_argument('--use_openrouter', action='store_true', help='Use OpenRouter API for first pass')
    parser.add_argument('--openrouter_model', type=str, default='google/gemini-2.0-flash-001', help='OpenRouter model for first pass')
    parser.add_argument('--use_third_pass', action='store_true', help='Use third pass to filter functions')
    parser.add_argument('--third_pass_model', type=str, default='gpt-4o', help='OpenAI model for third pass')
    parser.add_argument('--relevance_threshold', type=float, default=7.0, help='Minimum score to consider a domain relevant')
    parser.add_argument('--max_branches', type=int, default=5, help='Maximum number of branches to explore at each level')

    args = parser.parse_args()

    # Default values if not provided
    if not args.query:
        args.query = "How does the system implement adaptive rate limiting based on system load and resource availability?"
    if not args.taxonomy:
        args.taxonomy = "./experiments/gitlab/domain_taxonomy.json"
    if not args.output_dir:
        args.output_dir = "./bracket_core/localisation/qa_data"
    if not args.semantic_fns:
        args.semantic_fns = "./experiments/gitlab/semantic_documented_functions.parquet"

    # Print configuration
    print(f"Running horizontal localisation with:")
    print(f"  Query: {args.query}")
    print(f"  Taxonomy: {args.taxonomy}")
    print(f"  Output directory: {args.output_dir}")
    print(f"  Semantic functions: {args.semantic_fns}")
    print(f"  Model (first pass): {args.model}")
    print(f"  Model (second pass): {args.second_pass_model or args.model}")
    print(f"  Use OpenRouter: {args.use_openrouter}")
    if args.use_openrouter:
        print(f"  OpenRouter model: {args.openrouter_model}")
    print(f"  Use third pass: {args.use_third_pass}")
    if args.use_third_pass:
        print(f"  Third pass model: {args.third_pass_model}")
    print(f"  Relevance threshold: {args.relevance_threshold}")
    print(f"  Max branches per level: {args.max_branches}")
    print()

    # Run the horizontal localisation
    start_time = asyncio.get_event_loop().time()
    result = await find_relevant_functions(
        query=args.query,
        domain_taxonomy_json_path=args.taxonomy,
        model=args.model,
        second_pass_model=args.second_pass_model,
        output_dir=args.output_dir,
        semantic_documented_fns_path=args.semantic_fns,
        # Horizontal approach specific parameters
        relevance_threshold=args.relevance_threshold,
        max_branches_per_level=args.max_branches,
        # OpenRouter parameters
        use_openrouter=args.use_openrouter,
        openrouter_model=args.openrouter_model,
        # Third pass parameters
        use_third_pass=args.use_third_pass,
        third_pass_model=args.third_pass_model
    )
    end_time = asyncio.get_event_loop().time()

    # Print results
    if result.success:
        print(f"\nFound {len(result.relevant_functions)} relevant functions in {end_time - start_time:.2f} seconds")

        # Print top 5 functions
        if result.relevant_functions:
            print("\nTop relevant functions:")
            for i, func in enumerate(result.relevant_functions[:5]):
                print(f"  {i+1}. {func['function_name']} (Score: {func['relevance_score']})")
                print(f"     Domain: {func['domain_trace']}")

        # Print third pass reasoning if available
        if result.third_pass_result and result.third_pass_result.success:
            print("\nThird pass reasoning:")
            print(result.third_pass_result.reasoning)
    else:
        print(f"\nError: {result.error_message}")

    return 0 if result.success else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
