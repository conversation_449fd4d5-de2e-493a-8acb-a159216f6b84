#!/usr/bin/env python3
"""
Generate Domain Taxonomy JSON

This script generates a hierarchical JSON structure that combines domain analysis,
domain traces, and mermaid diagrams into a single taxonomy representation.

Usage:
    python scripts/generate_domain_taxonomy.py --domain-analysis <path> --domain-traces <path> --domain-diagrams <dir> --output <path>
"""

import os
import sys
import argparse
import logging

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from bracket_core.domain_taxonomy_mapper import DomainTaxonomyMapper

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """Main entry point for the domain taxonomy generator."""
    parser = argparse.ArgumentParser(description="Generate domain taxonomy JSON")
    parser.add_argument("--domain-analysis", required=True, help="Path to the domain analysis YAML file")
    parser.add_argument("--domain-traces", required=True, help="Path to the domain traces YAML file")
    parser.add_argument("--domain-diagrams", required=True, help="Directory containing the generated mermaid diagrams")
    parser.add_argument("--output", required=True, help="Path to save the output JSON file")
    
    args = parser.parse_args()
    
    try:
        # Create domain taxonomy mapper
        mapper = DomainTaxonomyMapper(
            domain_analysis_yaml_path=args.domain_analysis,
            domain_traces_yaml_path=args.domain_traces,
            domain_diagrams_dir=args.domain_diagrams,
            output_json_path=args.output
        )
        
        # Map domain taxonomy
        result = mapper.map_taxonomy()
        
        if result.success:
            logger.info(f"Domain taxonomy JSON generated successfully: {result.output_path}")
            return 0
        else:
            logger.error(f"Domain taxonomy JSON generation failed: {result.error_message}")
            return 1
        
    except Exception as e:
        logger.error(f"Error generating domain taxonomy JSON: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    sys.exit(main())
