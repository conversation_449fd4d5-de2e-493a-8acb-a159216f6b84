#!/usr/bin/env python3
"""
Generate suggested questions for the codebase based on the domain taxonomy.

This script generates high-quality, codebase-specific questions that help onboard
staff engineers by demonstrating deep understanding of the codebase architecture.

Features:
- Uses domain taxonomy data including diagrams to generate contextual questions
- Supports both OpenAI and OpenRouter models for generation
- Produces questions organized by relevant categories
- Generates questions that are specific to the codebase rather than generic
- Focuses on complex architectural questions that demonstrate codebase understanding

Usage:
    python scripts/generate_suggested_questions.py --taxonomy <path> --output <dir> [--use-openrouter]
"""

import argparse
import json
import os
import sys

import asyncio
import logging
from typing import Dict, List, Any

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import from bracket_core
from bracket_core.llm.api_keys import get_openai_api_key
from bracket_core.llm.oai.chat_openai import ChatOpenAI
from bracket_core.llm.get_client import get_openrouter_client

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Generate suggested questions for the codebase.")
    parser.add_argument("--taxonomy", required=True, help="Path to the domain taxonomy JSON file")
    parser.add_argument("--output", required=True, help="Directory to save the output files")
    parser.add_argument("--model", default="gpt-4o-mini", help="Model to use for generation")
    parser.add_argument("--max-tokens", type=int, default=2000, help="Maximum number of tokens to generate")
    parser.add_argument("--use-openrouter", action="store_true", default=False,
                      help="Use OpenRouter instead of OpenAI (for larger context windows)")
    parser.add_argument("--openrouter-model", default="google/gemini-2.5-pro-preview",
                      help="OpenRouter model to use if --use-openrouter is specified")
    parser.add_argument("--temperature", type=float, default=0.7,
                      help="Temperature for generation (higher = more creative)")
    parser.add_argument("--debug", action="store_true", default=False,
                      help="Enable debug mode with additional logging and fallbacks")
    parser.add_argument("--skip-api", action="store_true", default=False,
                      help="Skip API calls and use default questions (for testing)")

    args = parser.parse_args()

    # Check for environment variables that might override arguments
    if os.environ.get("BRACKET_DEBUG") == "1":
        args.debug = True
    if os.environ.get("BRACKET_SKIP_API") == "1":
        args.skip_api = True

    return args

def generate_default_questions() -> Dict[str, List[str]]:
    """Generate default questions as a fallback."""
    logger.info("Generating default questions")
    return {
        "Architecture": [
            "How is the codebase structured to separate concerns between different components?",
            "What design patterns are used to manage dependencies between different parts of the system?",
            "How does the architecture support scalability and maintainability?"
        ],
        "Integration": [
            "How do different components of the system communicate with each other?",
            "What mechanisms are in place for error handling during component interactions?",
            "How is data transformed as it flows between different parts of the system?"
        ],
        "Testing": [
            "What testing frameworks and methodologies are used in the codebase?",
            "How is test coverage measured and maintained across the codebase?",
            "What strategies are in place for integration testing between components?"
        ],
        "Performance": [
            "What performance optimization techniques are implemented in the codebase?",
            "How is the system designed to handle increased load and traffic?",
            "What monitoring and profiling tools are used to identify performance bottlenecks?"
        ],
        "Security": [
            "How is authentication and authorization handled across the system?",
            "What measures are in place to protect sensitive data and prevent common vulnerabilities?",
            "How are security considerations integrated into the development workflow?"
        ],
        "Deployment": [
            "What is the deployment strategy for the application?",
            "How is configuration management handled across different environments?",
            "What CI/CD pipelines are in place for automated testing and deployment?"
        ]
    }

def load_taxonomy(taxonomy_path: str) -> Dict[str, Any]:
    """Load the domain taxonomy from a JSON file."""
    logger.info(f"Loading taxonomy from {taxonomy_path}")
    try:
        with open(taxonomy_path, "r") as f:
            data = json.load(f)

        # Validate the structure
        if not isinstance(data, dict):
            logger.warning(f"Taxonomy is not a dictionary: {type(data)}")
            return {"Default": {"description": "Default domain"}}

        # Check if the taxonomy has children
        if "children" in data:
            logger.info("Found 'children' key in taxonomy, extracting child domains")
            # Extract domains from children
            domains = {}
            for child in data.get("children", []):
                if isinstance(child, dict) and "name" in child and "description" in child:
                    domains[child["name"]] = {"description": child["description"]}
            return domains

        return data
    except json.JSONDecodeError as e:
        logger.error(f"Error parsing taxonomy JSON: {e}")
        return {"Default": {"description": "Default domain"}}
    except Exception as e:
        logger.error(f"Error loading taxonomy: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return {"Default": {"description": "Default domain"}}

async def generate_questions(taxonomy: Dict[str, Any], model: str, max_tokens: int, use_openrouter: bool = False, openrouter_model: str = "google/gemini-2.5-pro-preview", temperature: float = 0.7) -> Dict[str, List[str]]:
    """Generate suggested questions for each domain in the taxonomy."""
    questions = {}

    # Log the taxonomy structure for debugging
    logger.info(f"Taxonomy structure: {type(taxonomy)}")
    logger.info(f"Taxonomy keys: {list(taxonomy.keys()) if isinstance(taxonomy, dict) else 'Not a dict'}")

    # Extract domain names, descriptions, and diagrams
    domains = {}
    domain_diagrams = {}

    # Ensure taxonomy is a dictionary
    if not isinstance(taxonomy, dict):
        logger.warning(f"Taxonomy is not a dictionary: {type(taxonomy)}")
        domains = {"Default": "Default domain"}
        domain_diagrams = {}
        return domains, domain_diagrams

    # Check if this is a hierarchical structure with children
    if "children" in taxonomy:
        logger.info("Processing hierarchical taxonomy structure with children")
        # Extract top-level domains and their diagrams
        for child in taxonomy.get("children", []):
            if isinstance(child, dict) and "name" in child:
                domain_name = child.get("name")
                domain_desc = child.get("description", "No description available")
                domains[domain_name] = domain_desc

                # Extract diagram if available
                if "diagram" in child:
                    domain_diagrams[domain_name] = child["diagram"]
    else:
        # Process flat structure
        logger.info("Processing flat taxonomy structure")
        for domain_name, domain_data in taxonomy.items():
            if domain_data is None:
                logger.warning(f"Domain data is None for {domain_name}")
                continue
            if not isinstance(domain_data, dict):
                logger.warning(f"Domain data is not a dictionary for {domain_name}: {type(domain_data)}")
                continue
            if "description" in domain_data:
                domains[domain_name] = domain_data["description"]
            # Extract diagram if available
            if "diagram" in domain_data:
                domain_diagrams[domain_name] = domain_data["diagram"]

    # First, generate categories based on the domain taxonomy
    try:
        logger.info("Generating categories based on domain taxonomy...")

        # Create a more detailed prompt that includes diagrams for key domains
        category_prompt = """You are an expert software architect tasked with creating insightful questions about a complex codebase.
        Your goal is to generate deep, architecture-level questions that will help a Staff Software Engineer understand this codebase.

        # Codebase Structure

        The codebase has the following domains:

        """

        # Add domain information
        category_prompt += json.dumps(domains, indent=2)

        # Add a sample of diagrams (limit to 2-3 to avoid token limits)
        if domain_diagrams:
            category_prompt += "\n\n# Sample Domain Diagrams\n\n"
            # Take up to 3 diagrams
            sample_domains = list(domain_diagrams.keys())[:3]
            for domain in sample_domains:
                category_prompt += f"## {domain}\n\n{domain_diagrams[domain]}\n\n"

        category_prompt += """
        Based on this codebase structure, identify 5-6 meaningful categories for organizing questions about this codebase.
        Each category should represent a distinct aspect of the codebase that developers might want to understand.

        The categories should include:
        - Architecture (required)
        - Integration (required)
        - And 3-4 other categories that are most relevant to this specific codebase

        Format your response as a JSON object where keys are category names and values are brief descriptions of what each category covers.
        Example: {
            "Architecture": "Questions about the overall architecture, design patterns, and system organization",
            "Integration": "Questions about how different components interact and integrate with each other"
        }
        """


        # Prepare messages for the API call
        messages = [
            {"role": "system", "content": "You are a helpful assistant that analyzes codebases and identifies meaningful categories for questions."},
            {"role": "user", "content": category_prompt}
        ]

        # Extract system and user prompts from messages
        system_prompt_text = messages[0]["content"]
        user_prompt_text = messages[1]["content"]

        # Call the appropriate API
        if use_openrouter:
            logger.info(f"Using OpenRouter with model: {openrouter_model}")
            # Create OpenRouter client
            openrouter_client = get_openrouter_client(
                model=openrouter_model,
                max_tokens=max_tokens,
                temperature=temperature
            )

            # Call OpenRouter client
            category_content = await openrouter_client.generate(
                prompt=user_prompt_text,
                system_prompt=system_prompt_text,
                max_tokens=max_tokens // 2,  # Use half the tokens for category generation
                temperature=temperature
            )
        else:
            logger.info(f"Using OpenAI with model: {model}")
            # Create OpenAI client
            openai_client = ChatOpenAI(
                api_key=get_openai_api_key(),
                model=model,
                max_retries=3
            )

            # Call OpenAI client
            category_content = await openai_client.agenerate(
                messages=messages,
                max_tokens=max_tokens // 2,  # Use half the tokens for category generation
                temperature=temperature
            )

        if not category_content:
            logger.error("Error: Received empty content for category generation")
            # Fallback to default categories
            categories = {
                "Architecture": "Questions about the overall architecture, design patterns, and system organization",
                "Integration": "Questions about how different components interact and integrate with each other",
                "Other": "General questions about the codebase, implementation details, and best practices"
            }
        else:
            category_content = category_content.strip()
            try:
                # Try to extract JSON from the response
                # Sometimes the model returns markdown-formatted JSON
                json_content = category_content
                if '```json' in category_content:
                    # Extract JSON from markdown code block
                    start = category_content.find('```json') + 7
                    end = category_content.find('```', start)
                    if end > start:
                        json_content = category_content[start:end].strip()
                        logger.info("Extracted JSON from markdown code block")

                # Try to parse the JSON
                categories = json.loads(json_content)
                logger.info(f"Generated {len(categories)} categories: {', '.join(categories.keys())}")
            except json.JSONDecodeError as e:
                logger.error(f"Error parsing category JSON: {e}")
                logger.error(f"Raw content: {category_content}")
                # Fallback to default categories
                categories = {
                    "Architecture": "Questions about the overall architecture, design patterns, and system organization",
                    "Integration": "Questions about how different components interact and integrate with each other",
                    "Other": "General questions about the codebase, implementation details, and best practices"
                }
    except Exception as e:
        logger.error(f"Error generating categories: {e}")
        import traceback
        logger.error(traceback.format_exc())
        # Fallback to default categories
        categories = {
            "Architecture": "Questions about the overall architecture, design patterns, and system organization",
            "Integration": "Questions about how different components interact and integrate with each other",
            "Other": "General questions about the codebase, implementation details, and best practices"
        }

    # Now generate questions for each category
    for category, description in categories.items():
        # Create a more detailed prompt for generating questions
        prompt = f"""
You are an expert software architect tasked with creating insightful questions about a complex codebase.
Your goal is to generate 2-3 deep, architecture-level questions for the category: {category} ({description}).

# Codebase Structure

The codebase has the following domains:

{json.dumps(domains, indent=2)}

"""

        # Add a sample of diagrams for context (limit to 1-2 to avoid token limits)
        if domain_diagrams:
            prompt += "# Sample Domain Diagrams\n\n"
            # Take up to 2 diagrams
            sample_domains = list(domain_diagrams.keys())[:2]
            for domain in sample_domains:
                prompt += f"## {domain}\n\n{domain_diagrams[domain]}\n\n"

        prompt += f"""
IMPORTANT GUIDELINES:
1. DO NOT use domain-specific abbreviations or shorthand in your questions
2. DO NOT refer to internal domain terminology that wouldn't be familiar to someone new to the codebase
3. Use full, descriptive names for components and systems instead of abbreviations
4. Focus on concrete technical concepts rather than abstract domain concepts
5. Include both high-level architectural questions and low-level implementation/debugging questions

Generate 2-3 insightful questions about the codebase related to the category: {category}.
The questions should:
1. Be specific to this particular codebase (not generic software questions)
2. Focus on relationships between components and design decisions
3. Be complex but concise questions that will introduce a Staff Engineer to a new codebase
4. Be 15-20 tokens, short, intense, concise and deeply about the codebase
5. Refer to actual components, patterns, or architectural elements visible in the diagrams when possible

Format your response as a JSON array of strings containing only the questions.
"""

        try:
            # Prepare messages for the API call
            messages = [
                {"role": "system", "content": "You are a helpful assistant that generates insightful questions about codebases."},
                {"role": "user", "content": prompt}
            ]

            # Extract system and user prompts from messages
            system_prompt_text = messages[0]["content"]
            user_prompt_text = messages[1]["content"]

            # Call the appropriate API
            if use_openrouter:
                logger.info(f"Using OpenRouter with model: {openrouter_model} for category: {category}")
                # Create OpenRouter client
                openrouter_client = get_openrouter_client(
                    model=openrouter_model,
                    max_tokens=max_tokens,
                    temperature=temperature
                )

                # Call OpenRouter client
                content = await openrouter_client.generate(
                    prompt=user_prompt_text,
                    system_prompt=system_prompt_text,
                    max_tokens=max_tokens,
                    temperature=temperature
                )
            else:
                logger.info(f"Using OpenAI with model: {model} for category: {category}")
                # Create OpenAI client
                openai_client = ChatOpenAI(
                    api_key=get_openai_api_key(),
                    model=model,
                    max_retries=3
                )

                # Call OpenAI client
                content = await openai_client.agenerate(
                    messages=messages,
                    max_tokens=max_tokens,
                    temperature=temperature
                )

            if not content:
                logger.error(f"Error: Received empty content for {category}")
                questions[category] = [f"Error generating questions for {category}"]
                continue

            content = content.strip()

            # Try to parse the content as JSON
            try:
                # Try to extract JSON from the response
                # Sometimes the model returns markdown-formatted JSON
                json_content = content
                if '```json' in content:
                    # Extract JSON from markdown code block
                    start = content.find('```json') + 7
                    end = content.find('```', start)
                    if end > start:
                        json_content = content[start:end].strip()
                        logger.info(f"Extracted JSON from markdown code block for {category}")
                elif '```' in content:
                    # Try to extract from a generic code block
                    start = content.find('```') + 3
                    # Skip the language identifier if present
                    if content[start:].strip() and '\n' in content[start:]:
                        start = content.find('\n', start) + 1
                    end = content.find('```', start)
                    if end > start:
                        json_content = content[start:end].strip()
                        logger.info(f"Extracted content from generic code block for {category}")

                # Try to parse as a JSON array
                category_questions = json.loads(json_content)
                if isinstance(category_questions, list):
                    questions[category] = category_questions
                else:
                    # If it's not a list, extract the values
                    logger.warning(f"Expected a JSON array for {category}, but got {type(category_questions)}")
                    if isinstance(category_questions, dict):
                        # If it's a dictionary, use the values
                        questions[category] = list(category_questions.values())
                    else:
                        # Otherwise, convert to string and use as a single question
                        questions[category] = [str(category_questions)]
            except json.JSONDecodeError as e:
                logger.warning(f"Error parsing JSON for {category}: {e}")
                logger.warning(f"Raw content: {content[:100]}...")
                # If the response is not valid JSON, extract questions manually
                lines = content.split("\n")
                category_questions = []
                for line in lines:
                    line = line.strip()
                    if line and line.startswith('"') and line.endswith('"'):
                        category_questions.append(line.strip('"'))
                    elif line and not line.startswith("[") and not line.startswith("]") and not line.startswith("{") and not line.startswith("}"):
                        category_questions.append(line)

                questions[category] = category_questions

            # Add a small delay to avoid rate limiting
            await asyncio.sleep(1)

        except Exception as e:
            logger.error(f"Error generating questions for {category}: {e}")
            import traceback
            logger.error(traceback.format_exc())
            questions[category] = [f"Error generating questions for {category}"]

    return questions

async def main() -> int:
    """Main function."""
    try:
        args = parse_args()

        # Print arguments for debugging
        logger.info(f"Running with arguments: {args}")

        # Create the output directory if it doesn't exist
        try:
            os.makedirs(args.output, exist_ok=True)
            # Test if we can write to the directory
            test_file = os.path.join(args.output, "test_write.txt")
            with open(test_file, "w") as f:
                f.write("test")
            os.remove(test_file)
            logger.info(f"Created output directory: {args.output}")
        except Exception as e:
            logger.error(f"Error creating or writing to output directory: {e}")
            logger.error(f"Will try to use a temporary directory instead")
            import tempfile
            args.output = tempfile.mkdtemp(prefix="bracket_questions_")
            logger.info(f"Using temporary directory instead: {args.output}")

        # Load the domain taxonomy
        try:
            logger.info(f"Loading taxonomy from: {args.taxonomy}")
            # Check if the taxonomy file exists
            if not os.path.exists(args.taxonomy):
                logger.error(f"Taxonomy file does not exist: {args.taxonomy}")
                return 1

            # Check if the taxonomy file is readable
            if not os.access(args.taxonomy, os.R_OK):
                logger.error(f"Taxonomy file is not readable: {args.taxonomy}")
                return 1

            # Check if the taxonomy file is a valid JSON file
            try:
                with open(args.taxonomy, 'r') as f:
                    json.load(f)
            except json.JSONDecodeError as e:
                logger.error(f"Taxonomy file is not a valid JSON file: {e}")
                return 1

            taxonomy = load_taxonomy(args.taxonomy)
            logger.info(f"Successfully loaded taxonomy with {len(taxonomy.keys()) if isinstance(taxonomy, dict) else 0} keys")

            # Check if the taxonomy is empty or has no domains
            if not taxonomy or (isinstance(taxonomy, dict) and len(taxonomy.keys()) == 0):
                logger.warning("Taxonomy is empty or has no domains, using default domains")
                # Create a default taxonomy with some generic domains
                taxonomy = {
                    "Backend": {"description": "Server-side code and business logic"},
                    "Frontend": {"description": "User interface and client-side code"},
                    "Database": {"description": "Data storage and retrieval"},
                    "Infrastructure": {"description": "Deployment, CI/CD, and cloud resources"},
                    "Testing": {"description": "Unit tests, integration tests, and test frameworks"}
                }
        except Exception as e:
            logger.error(f"Error loading taxonomy file: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return 1

        # Generate questions
        try:
            logger.info(f"Generating questions using model: {args.model if not args.use_openrouter else args.openrouter_model}")

            # Check for API keys before making API calls
            if not args.use_openrouter:
                # Check for OpenAI API key
                api_key = get_openai_api_key()
                if not api_key:
                    logger.error("OpenAI API key not found. Please set the OPENAI_API_KEY environment variable.")
                    # Use default questions instead
                    logger.warning("Using default questions instead")
                    questions = generate_default_questions()
                else:
                    logger.info("OpenAI API key found")
                    questions = await generate_questions(
                        taxonomy=taxonomy,
                        model=args.model,
                        max_tokens=args.max_tokens,
                        use_openrouter=args.use_openrouter,
                        openrouter_model=args.openrouter_model,
                        temperature=args.temperature
                    )
            else:
                # Using OpenRouter
                questions = await generate_questions(
                    taxonomy=taxonomy,
                    model=args.model,
                    max_tokens=args.max_tokens,
                    use_openrouter=args.use_openrouter,
                    openrouter_model=args.openrouter_model,
                    temperature=args.temperature
                )

            logger.info(f"Successfully generated questions for {len(questions)} categories")
        except Exception as e:
            logger.error(f"Error generating questions: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # Generate default questions as a fallback
            logger.warning("Using default questions as fallback due to error")
            questions = generate_default_questions()

        # Save the questions to a JSON file
        try:
            output_path = os.path.join(args.output, "suggested_questions.json")
            logger.info(f"Saving questions to {output_path}")

            # Ensure the output directory exists
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # Check if we can write to the output file
            with open(output_path, "w") as f:
                json.dump(questions, f, indent=2)

            # Verify that the file was written correctly
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                logger.info(f"Successfully wrote {file_size} bytes to {output_path}")
            else:
                logger.error(f"Failed to write to {output_path}")
                return 1

            logger.info(f"Generated suggested questions and saved to {output_path}")
            return 0
        except Exception as e:
            logger.error(f"Error saving questions to file: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # Try to save to a temporary file as a last resort
            try:
                import tempfile
                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".json")
                temp_path = temp_file.name
                with open(temp_path, "w") as f:
                    json.dump(questions, f, indent=2)
                logger.info(f"Saved questions to temporary file: {temp_path}")
                return 0
            except Exception as e2:
                logger.error(f"Error saving to temporary file: {e2}")
                return 1
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    try:
        # Configure more detailed logging for debugging
        logging.basicConfig(
            level=logging.DEBUG,  # Set to DEBUG for more detailed logs
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),  # Log to console
                logging.FileHandler(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'generate_questions.log'))  # Log to file
            ]
        )
        logger.info("Starting generate_suggested_questions.py")
        logger.info(f"Python version: {sys.version}")
        logger.info(f"Current working directory: {os.getcwd()}")
        logger.info(f"Script directory: {os.path.dirname(os.path.abspath(__file__))}")

        # Run the main function with proper exception handling
        exit_code = asyncio.run(main())
        logger.info(f"Script completed with exit code: {exit_code}")
        sys.exit(exit_code)
    except Exception as e:
        logger.critical(f"Unhandled exception in main script: {e}")
        import traceback
        logger.critical(traceback.format_exc())
        sys.exit(1)
