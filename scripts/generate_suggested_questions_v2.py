#!/usr/bin/env python3
"""
Generate Suggested Questions V2

This script generates high-quality, codebase-specific questions based on a domain taxonomy JSON file.
It is a simplified version that focuses only on question generation without streaming.

Usage:
    python scripts/generate_suggested_questions_v2.py --taxonomy <path> --output <path> [--use-openrouter]
"""

import os
import sys
import json
import argparse
import asyncio
import logging
import re
from typing import Dict, List, Any, Optional

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from bracket_core.llm.api_keys import get_openai_api_key, get_openrouter_api_key
# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import OpenRouter client if available
OPENROUTER_AVAILABLE = False
get_openrouter_client = None

try:
    from bracket_core.llm.get_client import get_openrouter_client
    OPENROUTER_AVAILABLE = True
except ImportError:
    logger.warning("OpenRouter client not available. Will use OpenAI only.")


async def generate_suggested_questions(
    taxonomy_json_path: str,
    output_dir: str,
    openai_api_key: Optional[str] = None,
    openai_model: str = "gpt-4o-mini",
    temperature: float = 0.7,
    use_openrouter: bool = False,
    openrouter_model: str = "google/gemini-2.5-pro-preview",
) -> Dict[str, Any]:
    """
    Generate suggested questions for a codebase based on domain taxonomy.

    Args:
        taxonomy_json_path: Path to the domain taxonomy JSON file
        output_dir: Directory to save the output files
        openai_api_key: OpenAI API key (if None, will try to get from environment)
        openai_model: OpenAI model to use
        temperature: Sampling temperature
        use_openrouter: Whether to use OpenRouter instead of OpenAI
        openrouter_model: OpenRouter model to use

    Returns:
        Dictionary with success status and output paths
    """
    result = {
        "success": True,
        "error_message": "",
        "questions": {},
        "output_paths": {
            "json": os.path.join(output_dir, "suggested_questions.json"),
            "markdown": os.path.join(output_dir, "suggested_questions.md")
        }
    }

    try:
        # Read the taxonomy JSON
        logger.info(f"Reading taxonomy JSON from {taxonomy_json_path}")
        with open(taxonomy_json_path, 'r') as f:
            taxonomy_data = json.load(f)

        # Extract codebase structure for the prompt
        codebase_structure = extract_codebase_structure(taxonomy_data)

        # Create the prompt for question generation
        system_prompt, user_prompt = create_questions_prompt(codebase_structure)

        # Generate questions using the appropriate API
        if use_openrouter and OPENROUTER_AVAILABLE:
            logger.info(f"Using OpenRouter with model {openrouter_model}")
            api_key = get_openrouter_api_key(provided_key=openai_api_key)
            questions = await generate_questions_openrouter(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                api_key=api_key,
                model=openrouter_model,
                temperature=temperature
            )
        else:
            logger.info(f"Using OpenAI with model {openai_model}")
            api_key = get_openai_api_key(provided_key=openai_api_key)
            questions = await generate_questions_openai(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                api_key=api_key,
                model=openai_model,
                temperature=temperature
            )

        # Save the questions
        if questions:
            # Save as JSON
            with open(result["output_paths"]["json"], 'w') as f:
                json.dump(questions, f, indent=2)

            # Format and save as markdown
            formatted_questions = format_questions_markdown(questions)
            with open(result["output_paths"]["markdown"], 'w') as f:
                f.write(formatted_questions)

            result["questions"] = questions
            logger.info(f"Successfully generated and saved questions to {output_dir}")
        else:
            result["success"] = False
            result["error_message"] = "Failed to generate questions"
            logger.error("Failed to generate questions")

    except Exception as e:
        logger.error(f"Error generating suggested questions: {e}")
        import traceback
        logger.error(traceback.format_exc())
        result["success"] = False
        result["error_message"] = str(e)

    return result


def extract_codebase_structure(taxonomy_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract the codebase structure from the taxonomy data for use in prompts.

    Args:
        taxonomy_data: Dictionary containing the domain taxonomy data

    Returns:
        Dictionary with codebase overview and domain information
    """
    structure = {
        "codebase_overview": None,
        "top_level_domains": [],
        "domain_diagrams": {}
    }

    # Extract codebase overview diagram
    if "codebase_overview" in taxonomy_data and "diagram" in taxonomy_data["codebase_overview"]:
        structure["codebase_overview"] = taxonomy_data["codebase_overview"]["diagram"]

    # Extract top-level domains
    if "children" in taxonomy_data:
        structure["top_level_domains"] = [
            {"name": domain.get("name", ""), "full_path": domain.get("full_path", domain.get("name", ""))}
            for domain in taxonomy_data["children"]
        ]

    # Extract domain diagrams
    structure["domain_diagrams"] = extract_domain_diagrams(taxonomy_data.get("children", []))

    return structure


def extract_domain_diagrams(domains: List[Dict[str, Any]], prefix: str = "") -> Dict[str, str]:
    """
    Extract diagrams for all domains recursively.

    Args:
        domains: List of domain dictionaries
        prefix: Prefix for the domain path (for recursive calls)

    Returns:
        Dictionary mapping domain paths to diagrams
    """
    diagrams = {}

    for domain in domains:
        domain_name = domain.get("name", "")
        domain_path = domain.get("full_path", prefix + domain_name if prefix else domain_name)

        # Extract diagram for this domain
        if "diagram" in domain:
            diagrams[domain_path] = domain["diagram"]

        # Extract combined diagram if available
        if "combined_diagram" in domain:
            diagrams[f"{domain_path} (Combined)"] = domain["combined_diagram"]

        # Recursively extract diagrams for children
        if "children" in domain and domain["children"]:
            child_diagrams = extract_domain_diagrams(
                domain["children"],
                prefix=domain_path + " -> " if domain_path else ""
            )
            diagrams.update(child_diagrams)

    return diagrams


def create_questions_prompt(codebase_structure: Dict[str, Any]) -> tuple[str, str]:
    """
    Create a prompt for generating suggested questions about the codebase.

    Args:
        codebase_structure: Dictionary with codebase overview and domain information

    Returns:
        Tuple of (system_prompt, user_prompt) for the LLM API
    """
    # System prompt
    system_prompt = """
You are an expert software architect tasked with creating insightful questions about a complex codebase.
Your goal is to generate 2-3 deep, architecture-level questions for each main domain or category in the codebase.

The questions should be organized by these categories:
- Architecture
- Monitoring
- Infrastructure
- Security
- Performance
- Data Flow
- Integration
- Testing
- Deployment

For each category that's relevant to this codebase, generate 2-3 questions. If a category isn't relevant, skip it.

IMPORTANT GUIDELINES:
1. DO NOT use domain-specific abbreviations or shorthand (like AA, AR, ADM, DP, PR, etc.) in your questions
2. DO NOT refer to internal domain terminology that wouldn't be familiar to someone new to the codebase
3. Use full, descriptive names for components and systems instead of abbreviations
4. Focus on concrete technical concepts rather than abstract domain concepts
5. Include both high-level architectural questions and low-level implementation/debugging questions

The questions should:
1. Cover different aspects of the codebase architecture
2. Range from high-level architectural questions to specific implementation patterns
3. Focus on relationships between components and design decisions
4. Be specific to this particular codebase (not generic software questions)
5. Encourage exploration of the codebase's most important or complex parts
6. Be phrased clearly and concisely using standard industry terminology
7. Refer to actual files, functions, and components by their full names when appropriate
8. Be complex but concise questions that will introduce a Staff Engineer to a new codebase
9. Be 15-20 tokens, short, intense, concise and deeply about the codebase

Format your response as a JSON object where keys are categories and values are arrays of questions.
Example: {
  "Architecture": ["How does the authentication system interact with the database layer?", "What patterns are used in the core module?"],
  "Security": ["How are user permissions enforced across microservices?", "What encryption methods protect sensitive data?"]
}
"""

    # User prompt
    user_prompt = "# Codebase Structure\n\n"

    # Add codebase overview diagram if available
    if codebase_structure["codebase_overview"]:
        user_prompt += "## Codebase Overview Diagram\n\n"
        user_prompt += codebase_structure["codebase_overview"] + "\n\n"

    # Add information about top-level domains
    user_prompt += "## Top-Level Domains\n\n"
    for domain in codebase_structure["top_level_domains"]:
        domain_name = domain.get("name", "")
        user_prompt += f"- {domain_name}\n"

    # Add domain diagrams
    user_prompt += "\n## Domain Diagrams\n\n"

    # Prioritize combined diagrams and top-level domains
    important_diagrams = {}

    # First add combined diagrams for top-level domains
    for domain in codebase_structure["top_level_domains"]:
        domain_name = domain.get("name", "")
        domain_path = domain.get("full_path", domain_name)
        combined_key = f"{domain_path} (Combined)"

        if combined_key in codebase_structure["domain_diagrams"]:
            important_diagrams[f"Combined {domain_name}"] = codebase_structure["domain_diagrams"][combined_key]

    # Then add regular diagrams for top-level domains
    for domain in codebase_structure["top_level_domains"]:
        domain_name = domain.get("name", "")
        domain_path = domain.get("full_path", domain_name)

        if domain_path in codebase_structure["domain_diagrams"] and domain_name not in important_diagrams:
            important_diagrams[domain_name] = codebase_structure["domain_diagrams"][domain_path]

    # Add the important diagrams to the prompt
    for domain_name, diagram in important_diagrams.items():
        user_prompt += f"### {domain_name}\n\n"
        user_prompt += diagram + "\n\n"

    # Add instructions for generating questions
    user_prompt += """
# Task
Based on the domain taxonomy and diagrams provided above, generate 2-3 insightful, architecture-level questions for each relevant category (Architecture, Monitoring, Infrastructure, Security, etc.).

IMPORTANT GUIDELINES:
1. DO NOT use domain-specific abbreviations or shorthand (like AA, AR, ADM, DP, PR, etc.) in your questions
2. DO NOT refer to internal domain terminology that wouldn't be familiar to someone new to the codebase
3. Use full, descriptive names for components and systems instead of abbreviations
4. Focus on concrete technical concepts rather than abstract domain concepts
5. Include both high-level architectural questions and low-level implementation/debugging questions

Your questions should:
1. Cover different aspects of the codebase architecture
2. Range from high-level architectural questions to specific implementation patterns
3. Focus on relationships between components and design decisions
4. Be specific to this particular codebase (not generic software questions)
5. Encourage exploration of the codebase's most important or complex parts
6. Be under 20 tokens, short, intense, concise and deeply about the codebase
7. Be organized by category based on the main domains and aspects of the system
8. Use standard industry terminology that would be familiar to any experienced developer

Format your response as a JSON object where keys are categories and values are arrays of questions.
Example: {
  "Architecture": ["How does the authentication system interact with the database layer?", "What patterns are used in the core module?"],
  "Security": ["How are user permissions enforced across microservices?", "What encryption methods protect sensitive data?"]
}
"""

    return system_prompt, user_prompt


async def generate_questions_openai(
    system_prompt: str,
    user_prompt: str,
    api_key: str,
    model: str = "gpt-4o-mini",
    temperature: float = 0.7  # Not used directly but kept for API consistency
) -> Dict[str, List[str]]:
    """
    Generate questions using OpenAI API.

    Args:
        system_prompt: System prompt for the OpenAI API
        user_prompt: User prompt for the OpenAI API
        api_key: OpenAI API key
        model: OpenAI model to use
        temperature: Sampling temperature (not used directly in ChatOpenAI but kept for API consistency)

    Returns:
        Dictionary mapping categories to lists of questions
    """
    try:
        # Import here to avoid circular imports
        from bracket_core.llm.oai.chat_openai import ChatOpenAI

        # Create the OpenAI client
        client = ChatOpenAI(
            api_key=api_key,
            model=model,
            max_retries=3
        )

        # Generate the questions
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        response = await client.agenerate(messages=messages)

        # Parse the response as JSON
        return parse_questions_response(response)

    except Exception as e:
        logger.error(f"Error generating questions with OpenAI: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return {}


async def generate_questions_openrouter(
    system_prompt: str,
    user_prompt: str,
    api_key: str,
    model: str = "google/gemini-2.5-pro-preview",
    temperature: float = 0.7
) -> Dict[str, List[str]]:
    """
    Generate questions using OpenRouter API.

    Args:
        system_prompt: System prompt for the OpenRouter API
        user_prompt: User prompt for the OpenRouter API
        api_key: OpenRouter API key
        model: OpenRouter model to use
        temperature: Sampling temperature

    Returns:
        Dictionary mapping categories to lists of questions
    """
    try:
        # Check if OpenRouter is available
        if not OPENROUTER_AVAILABLE or get_openrouter_client is None:
            logger.error("OpenRouter client not available")
            return {}

        # Import here to avoid circular imports
        from bracket_core.llm.get_client import get_openrouter_client as get_client

        # Get the OpenRouter client
        client = get_client(
            api_key=api_key,
            model=model,
            temperature=temperature
        )

        # Generate the questions
        response = await client.generate(
            prompt=user_prompt,
            system_prompt=system_prompt
        )

        # Parse the response as JSON
        return parse_questions_response(response)

    except Exception as e:
        logger.error(f"Error generating questions with OpenRouter: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return {}


def parse_questions_response(response: str) -> Dict[str, List[str]]:
    """
    Parse the LLM response to extract questions.

    Args:
        response: Response from the LLM

    Returns:
        Dictionary mapping categories to lists of questions
    """
    # Try to extract JSON from the response
    try:
        # Find JSON in the response (it might be surrounded by markdown or other text)
        json_match = re.search(r'```json\s*(.*?)\s*```', response, re.DOTALL)
        if json_match:
            clean_response = json_match.group(1)
        else:
            # Try to find JSON without markdown code blocks
            json_match = re.search(r'(\{.*\})', response, re.DOTALL)
            if json_match:
                clean_response = json_match.group(1)
            else:
                clean_response = response

        # Parse the JSON
        questions_by_domain = json.loads(clean_response)

        if isinstance(questions_by_domain, dict):
            total_questions = sum(len(qs) for qs in questions_by_domain.values())
            logger.info(f"Successfully parsed {total_questions} suggested questions across {len(questions_by_domain)} domains")
            return questions_by_domain
        elif isinstance(questions_by_domain, list):
            # Handle case where the model returns a list instead of a dict
            logger.warning("Response was a list of questions instead of a domain-categorized dict. Converting to categorized format.")
            return {"General": questions_by_domain}
        else:
            logger.warning("Response was not a dict or list of questions")
            return {}
    except json.JSONDecodeError as e:
        logger.error(f"Error parsing questions response as JSON: {e}")
        # Try to extract questions using regex as a fallback
        questions = re.findall(r'"([^"]+)"', response)
        if questions:
            logger.info(f"Extracted {len(questions)} questions using regex")
            return {"General": questions}
        return {}


def format_questions_markdown(questions: Dict[str, List[str]]) -> str:
    """
    Format questions as markdown for better readability.

    Args:
        questions: Dictionary mapping categories to lists of questions

    Returns:
        Formatted markdown string
    """
    formatted = "# Suggested Questions by Domain\n\n"

    for domain, domain_questions in questions.items():
        formatted += f"## {domain}\n\n"
        for i, question in enumerate(domain_questions, 1):
            formatted += f"{i}. {question}\n"
        formatted += "\n"

    return formatted


def generate_default_questions() -> Dict[str, List[str]]:
    """
    Generate default questions as a fallback.

    Returns:
        Dictionary mapping categories to lists of questions
    """
    logger.info("Generating default questions")
    return {
        "Architecture": [
            "How is the codebase structured to separate concerns between different components?",
            "What design patterns are used to manage dependencies between different parts of the system?",
            "How does the architecture support scalability and maintainability?"
        ],
        "Integration": [
            "How do different components of the system communicate with each other?",
            "What mechanisms are in place for error handling during component interactions?",
            "How is data transformed as it flows between different parts of the system?"
        ],
        "Performance": [
            "What strategies are used to optimize performance in critical paths?",
            "How does the system handle high load situations?",
            "What caching mechanisms are implemented and where?"
        ],
        "Security": [
            "How is authentication and authorization implemented across the system?",
            "What measures are in place to prevent common security vulnerabilities?",
            "How is sensitive data protected both at rest and in transit?"
        ]
    }


async def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Generate suggested questions from a domain taxonomy JSON file")

    parser.add_argument(
        "--taxonomy",
        required=True,
        help="Path to the domain taxonomy JSON file"
    )

    parser.add_argument(
        "--output",
        required=True,
        help="Path to save the output files"
    )

    parser.add_argument(
        "--api-key",
        help="API key (if not provided, will try to use environment variable)"
    )

    parser.add_argument(
        "--model",
        default="gpt-4o-mini",
        help="Model to use (default: gpt-4o-mini)"
    )

    parser.add_argument(
        "--max-tokens",
        type=int,
        default=4000,
        help="Maximum tokens to generate (default: 4000)"
    )

    parser.add_argument(
        "--temperature",
        type=float,
        default=0.7,
        help="Sampling temperature (default: 0.7)"
    )

    parser.add_argument(
        "--use-openrouter",
        default=True,
        action="store_true",
        help="Use OpenRouter instead of OpenAI"
    )

    parser.add_argument(
        "--openrouter-model",
        default="google/gemini-2.5-pro-preview",
        help="OpenRouter model to use (default: google/gemini-2.5-pro-preview)"
    )

    args = parser.parse_args()

    try:
        # Create output directory if it doesn't exist
        os.makedirs(args.output, exist_ok=True)

        # Generate the suggested questions
        result = await generate_suggested_questions(
            taxonomy_json_path=args.taxonomy,
            output_dir=args.output,
            openai_api_key=args.api_key,
            openai_model=args.model,
            temperature=args.temperature,
            use_openrouter=args.use_openrouter,
            openrouter_model=args.openrouter_model
        )

        if result["success"]:
            logger.info(f"Suggested questions generated successfully")
            logger.info(f"JSON output: {result['output_paths']['json']}")
            logger.info(f"Markdown output: {result['output_paths']['markdown']}")
            return 0
        else:
            logger.error(f"Suggested questions generation failed: {result['error_message']}")
            return 1

    except Exception as e:
        logger.error(f"Error generating suggested questions: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
