#!/bin/bash

# Setup script for Bracket project
# This script sets up both the Python (bracket_core) and TypeScript (bracket_ext) projects

set -e

echo "Setting up Bracket project..."

# Create scripts directory if it doesn't exist
mkdir -p scripts

# Setup Python environment
echo "Setting up Python environment (bracket_core)..."
poetry install

# Setup TypeScript environment
echo "Setting up TypeScript environment (bracket_ext)..."
cd bracket_ext
npm install
cd ..

echo "Setup complete!"
echo "To run bracket_core: poetry run bracket"
echo "To run bracket_ext: cd bracket_ext && npm run dev"
