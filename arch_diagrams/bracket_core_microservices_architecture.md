# Bracket Core Microservices Architecture

```mermaid
flowchart TB
    classDef service fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef storage fill:#e8f5e9,stroke:#2e7d32,stroke-width:2px
    classDef infrastructure fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef external fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px
    classDef client fill:#ffebee,stroke:#b71c1c,stroke-width:2px
    classDef subgraph_style fill:#f5f5f5,stroke:#9e9e9e,stroke-width:1px
    
    %% External Clients
    Client[VSCode Extension]:::client
    API[External API Clients]:::client
    
    %% Infrastructure Components
    subgraph Infrastructure["GKE Infrastructure"]
        direction TB
        K8s[Kubernetes Cluster]:::infrastructure
        Prometheus[Prometheus\nMetrics Collection]:::infrastructure
        Grafana[Grafana\nDashboards]:::infrastructure
        CloudStorage[Google Cloud Storage]:::storage
        
        K8s --- Prometheus
        Prometheus --- Grafana
    end
    
    %% Common Infrastructure Layer
    subgraph Common["Common Infrastructure Layer (bracket-irl-common)"]
        direction TB
        Config[Configuration Management]:::infrastructure
        Logging[Structured Logging]:::infrastructure
        Storage[Storage Abstraction]:::infrastructure
        Metrics[Metrics Collection]:::infrastructure
        Health[Health Checks]:::infrastructure
        ErrorHandling[Error Handling]:::infrastructure
        LLMClients[LLM Clients]:::infrastructure
        TokenCounting[Token Counting]:::infrastructure
        RateLimiting[Rate Limiting]:::infrastructure
        DataModels[Shared Data Models]:::infrastructure
    end
    
    %% Core Microservices
    subgraph CoreServices["Core Microservices"]
        direction TB
        
        %% Orchestrator Service
        subgraph Orchestrator["Orchestrator Service (Port 8000)"]
            direction TB
            OrchAPI[REST API]:::service
            JobManager[Job Manager]:::service
            PipelineCoordinator[Pipeline Coordinator]:::service
            ServiceRegistry[Service Registry]:::service
            
            OrchAPI --- JobManager
            JobManager --- PipelineCoordinator
            PipelineCoordinator --- ServiceRegistry
        end
        
        %% Repository Mapper Service
        subgraph RepoMapper["Repository Mapper Service (Port 8001)"]
            direction TB
            RepoMapperAPI[REST API]:::service
            CodeExtractor[Code Extractor]:::service
            AST[AST Generator]:::service
            SymbolExtractor[Symbol Extractor]:::service
            ImportAnalyzer[Import Analyzer]:::service
            
            RepoMapperAPI --- CodeExtractor
            CodeExtractor --- AST
            AST --- SymbolExtractor
            SymbolExtractor --- ImportAnalyzer
        end
        
        %% Domain Analyzer Service
        subgraph DomainAnalyzer["Domain Analyzer Service (Port 8002)"]
            direction TB
            DomainAPI[REST API]:::service
            FunctionClustering[Function Clustering]:::service
            DomainIdentification[Domain Identification]:::service
            HierarchyBuilder[Hierarchy Builder]:::service
            LLMProcessor1[LLM Processor]:::service
            
            DomainAPI --- FunctionClustering
            FunctionClustering --- DomainIdentification
            DomainIdentification --- HierarchyBuilder
            HierarchyBuilder --- LLMProcessor1
        end
        
        %% File Domain Mapper Service
        subgraph FileDomainMapper["File Domain Mapper Service (Port 8003)"]
            direction TB
            FileMapperAPI[REST API]:::service
            FileAnalyzer[File Analyzer]:::service
            DomainAssigner[Domain Assigner]:::service
            BatchProcessor[Batch Processor]:::service
            
            FileMapperAPI --- FileAnalyzer
            FileAnalyzer --- DomainAssigner
            DomainAssigner --- BatchProcessor
        end
        
        %% Domain File Repomap Service
        subgraph DomainFileRepomap["Domain File Repomap Service (Port 8004)"]
            direction TB
            RepomapAPI[REST API]:::service
            MapIntegrator[Map Integrator]:::service
            RelationshipEnricher[Relationship Enricher]:::service
            ArtifactGenerator[Artifact Generator]:::service
            
            RepomapAPI --- MapIntegrator
            MapIntegrator --- RelationshipEnricher
            RelationshipEnricher --- ArtifactGenerator
        end
        
        %% Diagram Generator Service
        subgraph DiagramGenerator["Diagram Generator Service (Port 8005)"]
            direction TB
            DiagramAPI[REST API]:::service
            TemplateEngine[Template Engine]:::service
            MermaidGenerator[Mermaid Generator]:::service
            LLMProcessor2[LLM Processor]:::service
            
            DiagramAPI --- TemplateEngine
            TemplateEngine --- MermaidGenerator
            MermaidGenerator --- LLMProcessor2
        end
    end
    
    %% Storage Components
    subgraph Storage["Persistent Storage"]
        direction TB
        JobDB[(Job Database)]:::storage
        ArtifactStorage[(Artifact Storage)]:::storage
        ConfigStorage[(Configuration Storage)]:::storage
    end
    
    %% External Services
    subgraph ExternalServices["External Services"]
        direction TB
        OpenAI[OpenAI API]:::external
        Claude[Anthropic Claude API]:::external
        SourceControl[Source Control Systems]:::external
    end
    
    %% Connections between components
    Client --> Orchestrator
    API --> Orchestrator
    
    Orchestrator --> RepoMapper
    Orchestrator --> DomainAnalyzer
    Orchestrator --> FileDomainMapper
    Orchestrator --> DomainFileRepomap
    Orchestrator --> DiagramGenerator
    
    Orchestrator --> JobDB
    Orchestrator --> ArtifactStorage
    
    RepoMapper --> ArtifactStorage
    DomainAnalyzer --> ArtifactStorage
    FileDomainMapper --> ArtifactStorage
    DomainFileRepomap --> ArtifactStorage
    DiagramGenerator --> ArtifactStorage
    
    DomainAnalyzer --> OpenAI
    DomainAnalyzer --> Claude
    DiagramGenerator --> OpenAI
    DiagramGenerator --> Claude
    
    RepoMapper --> SourceControl
    
    %% Common infrastructure connections
    Common -.-> Orchestrator
    Common -.-> RepoMapper
    Common -.-> DomainAnalyzer
    Common -.-> FileDomainMapper
    Common -.-> DomainFileRepomap
    Common -.-> DiagramGenerator
    
    %% Infrastructure connections
    CoreServices -.-> K8s
    CoreServices -.-> Prometheus
    CoreServices -.-> CloudStorage
```

## Bracket Core Microservices Architecture: Detailed Explanation

The Bracket Core IRL system has been transformed from a monolithic pipeline into a scalable, cloud-ready microservices architecture deployed on Google Kubernetes Engine (GKE). This architecture follows modern microservices principles, with each component focusing on a specific responsibility within the overall pipeline.

### Core Microservices

#### 1. Orchestrator Service (Port 8000)

**Purpose**: Coordinates the entire pipeline workflow and provides the main API endpoints.

**Components**:
- **REST API**: Exposes endpoints for job submission, status checking, and result retrieval
- **Job Manager**: Handles job creation, tracking, and status updates
- **Pipeline Coordinator**: Orchestrates the flow between services
- **Service Registry**: Maintains information about available services

**Responsibilities**:
- Accepting and validating job requests
- Coordinating the execution of the pipeline
- Tracking job status and progress
- Providing a unified API for clients
- Managing error handling and recovery

#### 2. Repository Mapper Service (Port 8001)

**Purpose**: Extracts code structure from repositories.

**Components**:
- **REST API**: Exposes endpoints for repository mapping
- **Code Extractor**: Extracts code from files
- **AST Generator**: Generates Abstract Syntax Trees
- **Symbol Extractor**: Extracts functions, classes, and other symbols
- **Import Analyzer**: Analyzes import relationships

**Responsibilities**:
- Parsing code in multiple languages
- Extracting structural information
- Building a comprehensive repository map
- Filtering and prioritizing important code elements

#### 3. Domain Analyzer Service (Port 8002)

**Purpose**: Analyzes code to identify logical domains.

**Components**:
- **REST API**: Exposes endpoints for domain analysis
- **Function Clustering**: Clusters related functions
- **Domain Identification**: Identifies logical domains
- **Hierarchy Builder**: Builds domain hierarchies
- **LLM Processor**: Uses LLMs for domain analysis

**Responsibilities**:
- Analyzing function relationships
- Identifying logical domains
- Creating hierarchical domain structures
- Using LLMs to enhance domain understanding

#### 4. File Domain Mapper Service (Port 8003)

**Purpose**: Maps files to identified domains.

**Components**:
- **REST API**: Exposes endpoints for file-domain mapping
- **File Analyzer**: Analyzes file content
- **Domain Assigner**: Assigns files to domains
- **Batch Processor**: Processes files in batches

**Responsibilities**:
- Analyzing file content and structure
- Mapping files to appropriate domains
- Processing files in efficient batches
- Reducing the search space for subsequent processing

#### 5. Domain File Repomap Service (Port 8004)

**Purpose**: Generates domain-file relationship maps.

**Components**:
- **REST API**: Exposes endpoints for domain-file repomap generation
- **Map Integrator**: Integrates domain and file maps
- **Relationship Enricher**: Enriches relationships
- **Artifact Generator**: Generates the combined artifact

**Responsibilities**:
- Integrating domain and file maps
- Enriching relationships between domains and files
- Creating a unified representation
- Generating the combined artifact

#### 6. Diagram Generator Service (Port 8005)

**Purpose**: Creates visual diagrams of the codebase.

**Components**:
- **REST API**: Exposes endpoints for diagram generation
- **Template Engine**: Creates diagram templates
- **Mermaid Generator**: Generates Mermaid diagrams
- **LLM Processor**: Uses LLMs to enhance diagrams

**Responsibilities**:
- Creating diagram templates
- Generating Mermaid diagram code
- Using LLMs to enhance diagrams
- Creating visual representations of domains

### Common Infrastructure Layer (bracket-irl-common)

**Purpose**: Provides shared utilities and standardized interfaces.

**Components**:
- **Configuration Management**: Centralized configuration
- **Structured Logging**: Consistent logging format
- **Storage Abstraction**: Unified storage interface
- **Metrics Collection**: Prometheus metrics
- **Health Checks**: Standardized health checks
- **Error Handling**: Consistent error handling
- **LLM Clients**: Unified LLM interface
- **Token Counting**: Token counting utilities
- **Rate Limiting**: API rate limiting
- **Shared Data Models**: Common data structures

**Responsibilities**:
- Providing shared utilities
- Ensuring consistent interfaces
- Reducing code duplication
- Standardizing common functionality

### Infrastructure Components

**GKE Infrastructure**:
- **Kubernetes Cluster**: Manages container orchestration
- **Prometheus**: Collects metrics
- **Grafana**: Visualizes metrics
- **Google Cloud Storage**: Stores artifacts

**Storage Components**:
- **Job Database**: Stores job information
- **Artifact Storage**: Stores generated artifacts
- **Configuration Storage**: Stores configuration

**External Services**:
- **OpenAI API**: Provides LLM capabilities
- **Anthropic Claude API**: Provides LLM capabilities
- **Source Control Systems**: Provides access to code repositories

## Key Benefits of the Microservices Architecture

1. **Scalability**: Each service can be scaled independently based on demand.
2. **Resilience**: Failures in one service don't affect others.
3. **Maintainability**: Smaller, focused codebases are easier to maintain.
4. **Deployability**: Services can be deployed independently.
5. **Observability**: Comprehensive metrics and health checks.
6. **Performance**: Optimized resource usage and parallel processing.

## Data Flow

1. Clients submit jobs to the Orchestrator Service.
2. The Orchestrator coordinates the execution of the pipeline.
3. The Repository Mapper Service extracts code structure.
4. The Domain Analyzer Service identifies logical domains.
5. The File Domain Mapper Service maps files to domains.
6. The Domain File Repomap Service creates a unified representation.
7. The Diagram Generator Service creates visual diagrams.
8. Results are stored in Artifact Storage.
9. Clients retrieve results from the Orchestrator Service.

This microservices architecture transforms the monolithic IRL pipeline into a scalable, cloud-ready system that can handle large codebases efficiently while maintaining the innovative approach to codebase analysis that sets Bracket apart.
