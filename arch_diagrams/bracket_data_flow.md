# Bracket Data Flow Architecture

```mermaid
flowchart TB
    classDef inputData fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef processedData fill:#e8f5e9,stroke:#2e7d32,stroke-width:2px
    classDef outputData fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef processor fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px
    classDef storage fill:#ffebee,stroke:#b71c1c,stroke-width:2px
    classDef subgraph_style fill:#f5f5f5,stroke:#9e9e9e,stroke-width:1px
    
    %% Input Data
    RawCode[Raw Codebase\nMultiple Languages & Files]:::inputData
    UserQuery[User Query/Task]:::inputData
    
    %% Bracket Core Data Flow
    subgraph BracketCoreFlow["Bracket Core Data Flow"]
        direction TB
        
        %% Repository Mapping Data Flow
        subgraph RepoMappingFlow["Repository Mapping"]
            direction TB
            RepoMapProcessor[Repository Mapper]:::processor
            ParsedCode[Parsed Code Structure]:::processedData
            SymbolTable[Symbol Table]:::processedData
            ImportGraph[Import Graph]:::processedData
            RepoMapOutput[Repository Map]:::outputData
            
            RepoMapProcessor --> ParsedCode
            RepoMapProcessor --> SymbolTable
            RepoMapProcessor --> ImportGraph
            ParsedCode & SymbolTable & ImportGraph --> RepoMapOutput
        end
        
        %% Knowledge Graph Data Flow
        subgraph KGFlow["Knowledge Graph Generation"]
            direction TB
            KGProcessor[Knowledge Graph Generator]:::processor
            FunctionTable[Function Table]:::processedData
            CallGraph[Call Graph]:::processedData
            RelationshipGraph[Relationship Graph]:::processedData
            KGOutput[Knowledge Graph]:::outputData
            
            KGProcessor --> FunctionTable
            KGProcessor --> CallGraph
            KGProcessor --> RelationshipGraph
            FunctionTable & CallGraph & RelationshipGraph --> KGOutput
        end
        
        %% Semantic Documentation Data Flow
        subgraph SemanticDocFlow["Semantic Documentation"]
            direction TB
            DocProcessor[Documentation Processor]:::processor
            FunctionDocs[Function Documentation]:::processedData
            SignificanceMarkers[Significance Markers]:::processedData
            PurposeDescriptions[Purpose Descriptions]:::processedData
            SemanticLayerOutput[Semantic Layer]:::outputData
            
            DocProcessor --> FunctionDocs
            DocProcessor --> SignificanceMarkers
            DocProcessor --> PurposeDescriptions
            FunctionDocs & SignificanceMarkers & PurposeDescriptions --> SemanticLayerOutput
        end
        
        %% Domain Analysis Data Flow
        subgraph DomainAnalysisFlow["Domain Analysis"]
            direction TB
            DomainProcessor[Domain Analyzer]:::processor
            FunctionClusters[Function Clusters]:::processedData
            DomainIdentifiers[Domain Identifiers]:::processedData
            HierarchyStructure[Hierarchy Structure]:::processedData
            DomainHierarchyOutput[Domain Hierarchy]:::outputData
            
            DomainProcessor --> FunctionClusters
            DomainProcessor --> DomainIdentifiers
            DomainProcessor --> HierarchyStructure
            FunctionClusters & DomainIdentifiers & HierarchyStructure --> DomainHierarchyOutput
        end
        
        %% File-Domain Mapping Data Flow
        subgraph FileDomainFlow["File-Domain Mapping"]
            direction TB
            FileDomainProcessor[File-Domain Mapper]:::processor
            FileAnalysisData[File Analysis Data]:::processedData
            DomainAssignments[Domain Assignments]:::processedData
            FileDomainMapOutput[File-Domain Map]:::outputData
            
            FileDomainProcessor --> FileAnalysisData
            FileDomainProcessor --> DomainAssignments
            FileAnalysisData & DomainAssignments --> FileDomainMapOutput
        end
        
        %% Domain-File Repomap Data Flow
        subgraph DomainFileRepomapFlow["Domain-File Repomap"]
            direction TB
            RepomapProcessor[Repomap Generator]:::processor
            IntegratedMaps[Integrated Maps]:::processedData
            EnrichedRelationships[Enriched Relationships]:::processedData
            CombinedArtifactOutput[Combined Artifact]:::outputData
            
            RepomapProcessor --> IntegratedMaps
            RepomapProcessor --> EnrichedRelationships
            IntegratedMaps & EnrichedRelationships --> CombinedArtifactOutput
        end
        
        %% Diagram Generation Data Flow
        subgraph DiagramGenFlow["Diagram Generation"]
            direction TB
            DiagramProcessor[Diagram Generator]:::processor
            DiagramTemplates[Diagram Templates]:::processedData
            MermaidCode[Mermaid Code]:::processedData
            DiagramsOutput[Domain Diagrams]:::outputData
            
            DiagramProcessor --> DiagramTemplates
            DiagramProcessor --> MermaidCode
            DiagramTemplates & MermaidCode --> DiagramsOutput
        end
        
        %% Taxonomy Generation Data Flow
        subgraph TaxonomyGenFlow["Taxonomy Generation"]
            direction TB
            TaxonomyProcessor[Taxonomy Generator]:::processor
            TaxonomyStructure[Taxonomy Structure]:::processedData
            JSONData[JSON Data]:::processedData
            DomainTaxonomyOutput[Domain Taxonomy JSON]:::outputData
            
            TaxonomyProcessor --> TaxonomyStructure
            TaxonomyProcessor --> JSONData
            TaxonomyStructure & JSONData --> DomainTaxonomyOutput
        end
        
        %% Codebase Explanation Data Flow
        subgraph ExplanationFlow["Codebase Explanation"]
            direction TB
            ExplanationProcessor[Explanation Generator]:::processor
            ArchitectureDescription[Architecture Description]:::processedData
            ComponentExplanations[Component Explanations]:::processedData
            CodebaseExplanationOutput[Codebase Explanation]:::outputData
            
            ExplanationProcessor --> ArchitectureDescription
            ExplanationProcessor --> ComponentExplanations
            ArchitectureDescription & ComponentExplanations --> CodebaseExplanationOutput
        end
    end
    
    %% Bracket Extension Data Flow
    subgraph BracketExtFlow["Bracket Extension Data Flow"]
        direction TB
        
        %% Context Engine Data Flow
        subgraph ContextEngineFlow["Context Engine"]
            direction TB
            ContextProcessor[Context Processor]:::processor
            RelevantCode[Relevant Code]:::processedData
            FunctionContext[Function Context]:::processedData
            DomainContext[Domain Context]:::processedData
            ContextOutput[Context Information]:::outputData
            
            ContextProcessor --> RelevantCode
            ContextProcessor --> FunctionContext
            ContextProcessor --> DomainContext
            RelevantCode & FunctionContext & DomainContext --> ContextOutput
        end
        
        %% Cline System Data Flow
        subgraph ClineFlow["Cline System"]
            direction TB
            ClineProcessor[Cline Processor]:::processor
            UserInput[User Input]:::processedData
            ContextData[Context Data]:::processedData
            SystemPrompt[System Prompt]:::processedData
            AIResponse[AI Response]:::outputData
            
            ClineProcessor --> UserInput
            ClineProcessor --> ContextData
            ClineProcessor --> SystemPrompt
            UserInput & ContextData & SystemPrompt --> AIResponse
        end
        
        %% Mermaid Service Data Flow
        subgraph MermaidServiceFlow["Mermaid Service"]
            direction TB
            MermaidProcessor[Mermaid Processor]:::processor
            FunctionPath[Function Path]:::processedData
            DomainMapping[Domain Mapping]:::processedData
            DiagramData[Diagram Data]:::processedData
            MermaidOutput[Mermaid Diagram]:::outputData
            
            MermaidProcessor --> FunctionPath
            MermaidProcessor --> DomainMapping
            MermaidProcessor --> DiagramData
            FunctionPath & DomainMapping & DiagramData --> MermaidOutput
        end
    end
    
    %% Storage Components
    ArtifactStorage[(Artifact Storage)]:::storage
    
    %% External Processors
    LLMProcessor[LLM Processor\nClaude/GPT]:::processor
    
    %% Flow between components
    RawCode --> RepoMappingFlow
    RepoMapOutput --> KGFlow
    KGOutput --> SemanticDocFlow
    SemanticLayerOutput --> DomainAnalysisFlow
    DomainHierarchyOutput --> FileDomainFlow
    FileDomainMapOutput --> DomainFileRepomapFlow
    CombinedArtifactOutput --> DiagramGenFlow
    CombinedArtifactOutput --> TaxonomyGenFlow
    DomainTaxonomyOutput --> ExplanationFlow
    
    %% LLM Integration
    SemanticDocFlow -.-> LLMProcessor
    DomainAnalysisFlow -.-> LLMProcessor
    DiagramGenFlow -.-> LLMProcessor
    ExplanationFlow -.-> LLMProcessor
    ClineFlow -.-> LLMProcessor
    
    %% Storage Integration
    RepoMapOutput --> ArtifactStorage
    KGOutput --> ArtifactStorage
    SemanticLayerOutput --> ArtifactStorage
    DomainHierarchyOutput --> ArtifactStorage
    FileDomainMapOutput --> ArtifactStorage
    CombinedArtifactOutput --> ArtifactStorage
    DiagramsOutput --> ArtifactStorage
    DomainTaxonomyOutput --> ArtifactStorage
    CodebaseExplanationOutput --> ArtifactStorage
    
    %% Extension Integration
    DomainTaxonomyOutput --> ContextEngineFlow
    DiagramsOutput --> MermaidServiceFlow
    CodebaseExplanationOutput --> ContextEngineFlow
    
    %% User Interaction
    UserQuery --> ClineFlow
    ContextOutput --> ClineFlow
    MermaidOutput --> UserInterface[User Interface]:::outputData
    AIResponse --> UserInterface
```

## Bracket Data Flow Architecture: Detailed Explanation

The Bracket system processes data through a sophisticated pipeline that transforms raw code into a structured, semantic representation. This data flow diagram illustrates how information moves through the system, from raw code to user-facing outputs.

### Input Data

1. **Raw Codebase**: The input codebase consisting of multiple files in various programming languages.
2. **User Query/Task**: User requests and tasks submitted through the VSCode extension.

### Bracket Core Data Flow

#### 1. Repository Mapping

**Data Transformation**:
- **Raw Code** → **Repository Mapper** → **Parsed Code Structure**, **Symbol Table**, **Import Graph** → **Repository Map**

**Data Description**:
- **Parsed Code Structure**: Structured representation of the code's syntax
- **Symbol Table**: Table of functions, classes, and variables
- **Import Graph**: Graph of import relationships between files
- **Repository Map**: Comprehensive structural representation of the codebase

#### 2. Knowledge Graph Generation

**Data Transformation**:
- **Repository Map** → **Knowledge Graph Generator** → **Function Table**, **Call Graph**, **Relationship Graph** → **Knowledge Graph**

**Data Description**:
- **Function Table**: Table of functions with their signatures and locations
- **Call Graph**: Graph of function calls
- **Relationship Graph**: Graph of relationships between code elements
- **Knowledge Graph**: Graph representation of code elements and their relationships

#### 3. Semantic Documentation

**Data Transformation**:
- **Knowledge Graph** → **Documentation Processor** → **Function Documentation**, **Significance Markers**, **Purpose Descriptions** → **Semantic Layer**

**Data Description**:
- **Function Documentation**: Extracted and generated documentation for functions
- **Significance Markers**: Indicators of function significance
- **Purpose Descriptions**: Descriptions of function purposes
- **Semantic Layer**: Semantic meaning added to the structural representation

#### 4. Domain Analysis

**Data Transformation**:
- **Semantic Layer** → **Domain Analyzer** → **Function Clusters**, **Domain Identifiers**, **Hierarchy Structure** → **Domain Hierarchy**

**Data Description**:
- **Function Clusters**: Groups of related functions
- **Domain Identifiers**: Names and identifiers for domains
- **Hierarchy Structure**: Hierarchical structure of domains
- **Domain Hierarchy**: Hierarchical organization of the codebase into logical domains

#### 5. File-Domain Mapping

**Data Transformation**:
- **Domain Hierarchy** → **File-Domain Mapper** → **File Analysis Data**, **Domain Assignments** → **File-Domain Map**

**Data Description**:
- **File Analysis Data**: Analysis of file content and structure
- **Domain Assignments**: Assignments of files to domains
- **File-Domain Map**: Mapping between files and domains

#### 6. Domain-File Repomap

**Data Transformation**:
- **File-Domain Map** → **Repomap Generator** → **Integrated Maps**, **Enriched Relationships** → **Combined Artifact**

**Data Description**:
- **Integrated Maps**: Integration of domain hierarchy and file mapping
- **Enriched Relationships**: Enhanced relationships between domains and files
- **Combined Artifact**: Unified representation of domains, files, and functions

#### 7. Diagram Generation

**Data Transformation**:
- **Combined Artifact** → **Diagram Generator** → **Diagram Templates**, **Mermaid Code** → **Domain Diagrams**

**Data Description**:
- **Diagram Templates**: Templates for different diagram types
- **Mermaid Code**: Generated Mermaid diagram code
- **Domain Diagrams**: Visual representations of domains and their relationships

#### 8. Taxonomy Generation

**Data Transformation**:
- **Combined Artifact** → **Taxonomy Generator** → **Taxonomy Structure**, **JSON Data** → **Domain Taxonomy JSON**

**Data Description**:
- **Taxonomy Structure**: Structure of the domain taxonomy
- **JSON Data**: Formatted JSON data
- **Domain Taxonomy JSON**: JSON representation of the domain taxonomy

#### 9. Codebase Explanation

**Data Transformation**:
- **Domain Taxonomy JSON** → **Explanation Generator** → **Architecture Description**, **Component Explanations** → **Codebase Explanation**

**Data Description**:
- **Architecture Description**: Description of the codebase architecture
- **Component Explanations**: Explanations of codebase components
- **Codebase Explanation**: Comprehensive explanation of the codebase

### Bracket Extension Data Flow

#### 1. Context Engine

**Data Transformation**:
- **Domain Taxonomy JSON**, **Codebase Explanation** → **Context Processor** → **Relevant Code**, **Function Context**, **Domain Context** → **Context Information**

**Data Description**:
- **Relevant Code**: Code relevant to the current context
- **Function Context**: Context about functions
- **Domain Context**: Context about domains
- **Context Information**: Comprehensive context information for the AI assistant

#### 2. Cline System

**Data Transformation**:
- **User Query**, **Context Information** → **Cline Processor** → **User Input**, **Context Data**, **System Prompt** → **AI Response**

**Data Description**:
- **User Input**: Processed user input
- **Context Data**: Context data for the AI
- **System Prompt**: System prompt for the AI
- **AI Response**: Response from the AI assistant

#### 3. Mermaid Service

**Data Transformation**:
- **Domain Diagrams** → **Mermaid Processor** → **Function Path**, **Domain Mapping**, **Diagram Data** → **Mermaid Diagram**

**Data Description**:
- **Function Path**: Path to the current function
- **Domain Mapping**: Mapping between functions and domains
- **Diagram Data**: Data for the Mermaid diagram
- **Mermaid Diagram**: Visual diagram displayed in the Mermaid Companion view

### Storage Integration

All artifacts generated by the Bracket Core pipeline are stored in **Artifact Storage**, which can be local or cloud-based. This storage serves as a bridge between Bracket Core and Bracket Extension, allowing the extension to access and use the artifacts generated by the core.

### LLM Integration

Several components in the pipeline use Large Language Models (LLMs) for processing:

1. **Semantic Documentation**: Uses LLMs to generate function descriptions
2. **Domain Analysis**: Uses LLMs to identify and name domains
3. **Diagram Generation**: Uses LLMs to enhance diagrams
4. **Codebase Explanation**: Uses LLMs to generate explanations
5. **Cline System**: Uses LLMs to generate AI responses

### User Interaction

The final outputs of the system are presented to the user through the VSCode extension:

1. **AI Response**: Displayed in the Chat Interface
2. **Mermaid Diagram**: Displayed in the Mermaid Companion view

## Key Data Transformations

1. **Structural Transformation**: Raw code → Structured representation
2. **Semantic Transformation**: Structured representation → Semantic understanding
3. **Domain Transformation**: Semantic understanding → Domain organization
4. **Visual Transformation**: Domain organization → Visual representation
5. **Explanatory Transformation**: Visual representation → Natural language explanation

This data flow architecture enables Bracket to transform raw code into a comprehensive cognitive mental model that can be used for various purposes, from onboarding new developers to planning refactoring efforts.
