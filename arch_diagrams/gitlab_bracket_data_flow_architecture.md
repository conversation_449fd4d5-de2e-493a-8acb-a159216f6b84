# GitLab-Bracket Data Flow Architecture

This architecture diagram illustrates the data flow between GitLab and Bracket, focusing on how code changes are processed and transformed into valuable artifacts.

```mermaid
flowchart TB
    classDef gitlabComponent fill:#e8f5e9,stroke:#2e7d32,stroke-width:2px
    classDef bracketComponent fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef dataComponent fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px
    classDef cloudComponent fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px
    classDef processComponent fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef userComponent fill:#ffebee,stroke:#b71c1c,stroke-width:2px
    
    %% Data Sources
    GitLabRepo[GitLab Repository]:::gitlabComponent
    GitLabMR[GitLab Merge Request]:::gitlabComponent
    GitLabCommit[GitLab Commit]:::gitlabComponent
    
    %% Data Ingestion
    subgraph DataIngestion["Data Ingestion"]
        direction TB
        RepoClone[Repository Clone]:::processComponent
        DiffExtraction[Diff Extraction]:::processComponent
        CodeFiltering[Code Filtering]:::processComponent
    end
    
    %% Bracket Processing (Black Box)
    subgraph BracketProcessing["Bracket Processing"]
        direction TB
        
        %% Initial Indexing
        subgraph InitialIndexing["Initial Indexing"]
            direction TB
            FullRepoAnalysis[Full Repository Analysis]:::bracketComponent
            StructureExtraction[Structure Extraction]:::bracketComponent
            SymbolIdentification[Symbol Identification]:::bracketComponent
        end
        
        %% Delta Indexing
        subgraph DeltaIndexing["Delta Indexing"]
            direction TB
            ChangedFilesAnalysis[Changed Files Analysis]:::bracketComponent
            DependencyImpactAnalysis[Dependency Impact Analysis]:::bracketComponent
            IncrementalUpdate[Incremental Update]:::bracketComponent
        end
        
        %% Artifact Generation
        subgraph ArtifactGeneration["Artifact Generation"]
            direction TB
            DomainIdentification[Domain Identification]:::bracketComponent
            DiagramCreation[Diagram Creation]:::bracketComponent
            DocumentationGeneration[Documentation Generation]:::bracketComponent
        end
    end
    
    %% Data Storage
    subgraph DataStorage["Data Storage"]
        direction TB
        
        %% Cloud Storage
        subgraph CloudStorage["Cloud Storage (GCS)"]
            direction TB
            RepositoryData[Repository Data]:::dataComponent
            IndexedData[Indexed Data]:::dataComponent
            ArtifactData[Artifact Data]:::dataComponent
        end
        
        %% Cache
        subgraph CacheLayer["Cache Layer"]
            direction TB
            LocalCache[Local Cache]:::dataComponent
            RedisCache[Redis Cache]:::dataComponent
        end
    end
    
    %% Artifact Types
    subgraph Artifacts["Bracket Artifacts"]
        direction TB
        
        %% Code Understanding
        subgraph CodeUnderstanding["Code Understanding"]
            direction TB
            DomainTaxonomy[Domain Taxonomy]:::dataComponent
            CodebaseMap[Codebase Map]:::dataComponent
            DependencyGraph[Dependency Graph]:::dataComponent
        end
        
        %% Visualizations
        subgraph Visualizations["Visualizations"]
            direction TB
            ArchitectureDiagrams[Architecture Diagrams]:::dataComponent
            DomainDiagrams[Domain Diagrams]:::dataComponent
            ImpactDiagrams[Impact Diagrams]:::dataComponent
        end
        
        %% Documentation
        subgraph Documentation["Documentation"]
            direction TB
            CodeExplanations[Code Explanations]:::dataComponent
            ArchitectureOverview[Architecture Overview]:::dataComponent
            OnboardingGuides[Onboarding Guides]:::dataComponent
        end
    end
    
    %% Consumption
    subgraph Consumption["Consumption"]
        direction TB
        
        %% GitLab Integration
        subgraph GitLabIntegration["GitLab Integration"]
            direction TB
            MRComments[MR Comments]:::gitlabComponent
            WikiPages[Wiki Pages]:::gitlabComponent
            CIQualityGates[CI Quality Gates]:::gitlabComponent
        end
        
        %% Bracket Extension
        subgraph BracketExtension["Bracket Extension"]
            direction TB
            VSCodeExtension[VSCode Extension]:::bracketComponent
            MermaidCompanion[Mermaid Companion]:::bracketComponent
            CodeQueryInterface[Code Query Interface]:::bracketComponent
        end
        
        %% API Access
        APIAccess[API Access]:::bracketComponent
    end
    
    %% Cloud Infrastructure
    subgraph CloudInfrastructure["Cloud Infrastructure"]
        direction TB
        GKECluster[GKE Cluster]:::cloudComponent
        CloudFunctions[Cloud Functions]:::cloudComponent
        PubSubTopics[Pub/Sub Topics]:::cloudComponent
    end
    
    %% Data Flow Connections
    
    %% Source to Ingestion
    GitLabRepo --> RepoClone
    GitLabMR --> DiffExtraction
    GitLabCommit --> DiffExtraction
    
    %% Ingestion to Processing
    RepoClone --> FullRepoAnalysis
    DiffExtraction --> ChangedFilesAnalysis
    CodeFiltering --> FullRepoAnalysis
    CodeFiltering --> ChangedFilesAnalysis
    
    %% Processing Flow
    FullRepoAnalysis --> StructureExtraction
    StructureExtraction --> SymbolIdentification
    SymbolIdentification --> DomainIdentification
    
    ChangedFilesAnalysis --> DependencyImpactAnalysis
    DependencyImpactAnalysis --> IncrementalUpdate
    IncrementalUpdate --> DomainIdentification
    
    DomainIdentification --> DiagramCreation
    DomainIdentification --> DocumentationGeneration
    
    %% Processing to Storage
    FullRepoAnalysis --> RepositoryData
    StructureExtraction --> IndexedData
    SymbolIdentification --> IndexedData
    
    ChangedFilesAnalysis --> RepositoryData
    DependencyImpactAnalysis --> IndexedData
    IncrementalUpdate --> IndexedData
    
    DomainIdentification --> ArtifactData
    DiagramCreation --> ArtifactData
    DocumentationGeneration --> ArtifactData
    
    %% Cache Connections
    IndexedData <--> LocalCache
    ArtifactData <--> RedisCache
    
    %% Storage to Artifacts
    RepositoryData --> CodebaseMap
    IndexedData --> DependencyGraph
    ArtifactData --> DomainTaxonomy
    
    DomainTaxonomy --> ArchitectureDiagrams
    DomainTaxonomy --> DomainDiagrams
    DependencyGraph --> ImpactDiagrams
    
    CodebaseMap --> CodeExplanations
    DomainTaxonomy --> ArchitectureOverview
    CodeExplanations --> OnboardingGuides
    
    %% Artifacts to Consumption
    ArchitectureDiagrams --> WikiPages
    ImpactDiagrams --> MRComments
    ArchitectureOverview --> CIQualityGates
    
    DomainDiagrams --> MermaidCompanion
    CodeExplanations --> VSCodeExtension
    DependencyGraph --> CodeQueryInterface
    
    %% All Artifacts to API
    CodeUnderstanding --> APIAccess
    Visualizations --> APIAccess
    Documentation --> APIAccess
    
    %% Cloud Infrastructure
    GKECluster --> BracketProcessing
    CloudFunctions --> DataIngestion
    PubSubTopics --> DeltaIndexing
    CloudStorage --> GKECluster
```

## GitLab-Bracket Data Flow Architecture: Detailed Explanation

This architecture diagram illustrates the data flow between GitLab and Bracket, focusing on how code is processed and transformed into valuable artifacts without revealing the proprietary implementation details of Bracket's IRL technology.

### Data Sources

- **GitLab Repository**: The primary source of code that will be analyzed
- **GitLab Merge Request**: Contains code changes that need to be analyzed for impact
- **GitLab Commit**: Individual code changes that trigger incremental analysis

### Data Ingestion

- **Repository Clone**: Clones the repository for full analysis
- **Diff Extraction**: Extracts changes from commits or merge requests
- **Code Filtering**: Filters code to focus on relevant files and changes

### Bracket Processing

#### Initial Indexing
- **Full Repository Analysis**: Analyzes the entire repository structure
- **Structure Extraction**: Extracts the code structure and relationships
- **Symbol Identification**: Identifies functions, classes, and other symbols

#### Delta Indexing
- **Changed Files Analysis**: Analyzes only the files that have changed
- **Dependency Impact Analysis**: Determines the impact of changes on dependencies
- **Incremental Update**: Updates the existing index with new information

#### Artifact Generation
- **Domain Identification**: Identifies logical domains in the codebase
- **Diagram Creation**: Creates visual representations of the codebase
- **Documentation Generation**: Generates documentation from the analysis

### Data Storage

#### Cloud Storage (GCS)
- **Repository Data**: Raw repository data and metadata
- **Indexed Data**: Processed and indexed code information
- **Artifact Data**: Generated artifacts like diagrams and documentation

#### Cache Layer
- **Local Cache**: Caches frequently accessed data locally
- **Redis Cache**: Distributed cache for sharing data between services

### Bracket Artifacts

#### Code Understanding
- **Domain Taxonomy**: Hierarchical representation of code domains
- **Codebase Map**: Map of the codebase structure and relationships
- **Dependency Graph**: Graph of dependencies between components

#### Visualizations
- **Architecture Diagrams**: High-level architecture visualizations
- **Domain Diagrams**: Visualizations of domain relationships
- **Impact Diagrams**: Visualizations of change impact

#### Documentation
- **Code Explanations**: Explanations of code functionality
- **Architecture Overview**: Overview of the codebase architecture
- **Onboarding Guides**: Guides for new developers

### Consumption

#### GitLab Integration
- **MR Comments**: Comments on merge requests with analysis insights
- **Wiki Pages**: Wiki pages with generated documentation
- **CI Quality Gates**: Quality gates in CI/CD pipelines

#### Bracket Extension
- **VSCode Extension**: Extension for accessing Bracket features
- **Mermaid Companion**: Tool for visualizing diagrams
- **Code Query Interface**: Interface for querying code information

#### API Access
- **API Access**: Programmatic access to Bracket artifacts

### Cloud Infrastructure

- **GKE Cluster**: Kubernetes cluster for running Bracket services
- **Cloud Functions**: Serverless functions for event-driven processing
- **Pub/Sub Topics**: Messaging for event coordination

### Key Data Flows

#### Initial Repository Indexing
1. GitLab Repository is cloned via Repository Clone
2. Full Repository Analysis processes the entire codebase
3. Structure Extraction and Symbol Identification create a detailed code model
4. Domain Identification generates a domain taxonomy
5. Artifacts are stored in Cloud Storage
6. Artifacts are made available through GitLab Integration and Bracket Extension

#### Delta Code Change Processing
1. GitLab Commit or Merge Request triggers Diff Extraction
2. Changed Files Analysis processes only the modified files
3. Dependency Impact Analysis determines the impact of changes
4. Incremental Update updates the existing index
5. Updated artifacts are generated and stored
6. Impact Diagrams show the effect of changes in MR Comments

#### Artifact Consumption
1. Developers access artifacts through VSCode Extension or GitLab
2. MR Comments provide insights during code review
3. Wiki Pages offer documentation for the team
4. CI Quality Gates ensure code quality based on analysis
5. API Access enables integration with other tools and systems
