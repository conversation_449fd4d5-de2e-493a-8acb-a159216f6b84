```mermaid
flowchart TB

%% ==============================================================
%%  TOP-LEVEL DOMAIN AND SUBDOMAIN PURPOSES (VERTICAL / HIERARCHICAL)
%% ==============================================================

subgraph TQA_TopDomain["Testing, QA, Linting & Developer Tooling" ]
direction TB
style TQA_TopDomain fill:#F8F8F8,stroke:#D4F1F9,stroke-width:6px,rx:28,ry:28

%% Core Domain Nodes PASTEL BLUE
TQA_Core["Unified Approach to Code Reliability,\nQuality, Safety, and Developer Effectiveness"]:::coreDomain

%% SUBDOMAIN PURPOSES - PASTEL BLUE / GROUPED
subgraph S1_SharedHelpers["Shared Examples & Helpers" ]
direction TB
style S1_SharedHelpers fill:#F8F8F8,stroke:#7ad0ee,stroke-width:3,rx:18,ry:18
S1_Title["Reusable Test Logic, Data Patterns,\nHelper Modules, and Test Patterns"]:::core
end

subgraph S2_StaticAnalysis["Static Analysis, Linting & Danger" ]
direction TB
style S2_StaticAnalysis fill:#F8F8F8,stroke:#7ad0ee,stroke-width:3,rx:18,ry:18
S2_Title["Automation, Linting, Danger Plugins,\nCustom Cops, and Suggestion Infrastructure"]:::core
end

subgraph S3_ToolingScripting["Tooling Utilities & Scripting" ]
direction TB
style S3_ToolingScripting fill:#F8F8F8,stroke:#8ddc8d,stroke-width:3,rx:18,ry:18
S3_Title["Migration Management, Housekeeping,\nPipeline & QA Scripting, Utility Libraries"]:::core
end

subgraph S4_RSpec["RSpec & Automated Testing" ]
direction TB
style S4_RSpec fill:#F8F8F8,stroke:#7ECD7E,stroke-width:3,rx:18,ry:18
S4_Title["Automated End-to-End and Feature Testing,\nDomain Data Factories, Init & Error Handling"]:::core
end

%% Map everything top-down
TQA_Core --> S1_SharedHelpers
TQA_Core --> S2_StaticAnalysis
TQA_Core --> S3_ToolingScripting
TQA_Core --> S4_RSpec

%% ==============================================================
%%  CROSS-SUBDOMAIN: DOMAIN-WIDE DATA STRUCTURES & ABSTRACTIONS
%% ==============================================================

subgraph Domain_DataStructs["Domain-wide Data Structures & Abstractions"]
direction TB
style Domain_DataStructs fill:#F8F8F8,stroke:#9bdd93,stroke-width:3,rx:20,ry:20

%% Domain data sets - PASTEL GREEN
DS_TestData["Test Data/Example Data Structures"]:::dataStruct
DS_StaticTodos["Static Analysis TODO/YAML Registry"]:::dataStruct
DS_LintMetrics["Linting & Quality Metrics/Reports"]:::dataStruct
DS_MigrationState["Migration States & Files"]:::dataStruct
DS_FeatureFlags["Feature Flag Registry & Audit State"]:::dataStruct
DS_SecurityResults["Scan/Security Scan Configs & Results"]:::dataStruct
DS_RepoArtifacts["Repository, File, and Artifact Sets"]:::dataStruct
DS_MemberEntities["User/Group/Test Entities"]:::dataStruct

%% ABSTRACTIONS / PATTERNS - PASTEL RED for patterns, blue for core
PAT_WaitRepeat["Waiter/Repeater/Environment Masking Pattern"]:::pattern
PAT_Suggestion["Suggestion Engine Pattern"]:::pattern
PAT_Formatter["Formatting/Reporting Abstraction"]:::pattern
PAT_Error["Cross-Domain Error Handling Pattern"]:::pattern
PAT_Init["Initialization & Setup Pattern"]:::init
end

%% Key: classdefs above
classDef coreDomain fill:#D4F1F9,stroke:#40b4e5,stroke-width:4px,rx:24,ry:24,color:#191b44
classDef core fill:#D4F1F9,stroke:#7ad0ee,stroke-width:2px,rx:14,ry:14,color:#2e3a4b
classDef group fill:#F8F8F8,stroke:#d4f1f9,stroke-width:3px,color:#222,rx:18,ry:18
classDef util fill:#FFF8DC,stroke:#FFD580,stroke-width:2px,rx:12,ry:12,color:#665300
classDef dataStruct fill:#E0F8E0,stroke:#9bdd93,stroke-width:2.5px,rx:26,ry:26,color:#144d26
classDef pattern fill:#FFE4E1,stroke:#dc9090,stroke-width:2.0px,rx:14,ry:14,color:#852020
classDef error fill:#FFE4E1,stroke:#f8b2b1,stroke-width:2.0px,rx:14,ry:14,color:#c33232
classDef init fill:#E6E6FA,stroke:#b5afe7,stroke-width:2.0px,rx:14,ry:14,color:#3c2d4f

%% Map domain-wide data-structs/patterns to subdomains
TQA_Core --- Domain_DataStructs

%% ==============================================================
%%  SUBDOMAINS - ABSTRACTED CORE NODES & MAJOR FLOWS
%% ==============================================================

%% === SHARED EXAMPLES & HELPERS ===
subgraph S1_Logic["Abstractions for Fixtures, Helpers, and Environment Masking"]
direction TB
style S1_Logic fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rx:16,ry:16

S1_Helpers["Test Helper Modules"]:::core
S1_Shared["Reusable & Parametric Shared Example Sets"]:::core
S1_Utils["Cross-Suite Utility Routines"]:::util
S1_Patterns["Embedded Wait/Repeat & Secret Masking Patterns"]:::pattern
S1_Reporting["Test Reporting & Cross-Suite Metadata"]:::util

S1_Helpers --> S1_Shared
S1_Helpers --> S1_Utils
S1_Helpers --> S1_Patterns
S1_Shared --> S1_Helpers
S1_Shared --> S1_Reporting
S1_Utils -- uses/generates --> S1_Reporting
S1_SharedHelpers --> S1_Logic

%% Integration with Domain Data & Patterns
S1_Logic -- uses --> DS_TestData
S1_Logic -- emits --> S1_Reporting
S1_Logic -- patterns --> PAT_WaitRepeat
S1_Logic -- patterns --> PAT_Error
S1_Logic -- uses --> DS_RepoArtifacts
S1_Logic -- uses --> DS_MemberEntities
S1_Logic -- patterns --> PAT_Init

%% === STATIC ANALYSIS, LINTING & DANGER ===
subgraph S2_Logic["Danger Automation, Linting Rules, Suggestion & Static Analysis Pipeline"]
direction TB
style S2_Logic fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rx:16,ry:16

S2_Suggestor["Suggestion Engine & Template/Comment Logic"]:::core
S2_Plugins["Danger Plugins/Automation"]:::core
S2_Cops["Custom RuboCop Cops/Extensions"]:::core
S2_LintRules["Lint Enforcement Rules"]:::error
S2_Validation["Validators & Domain Guards"]:::error
S2_Utils["Automation Utilities"]:::util
S2_Formatter["Reporting/Formatter Abstractions"]:::pattern

S2_Plugins -- triggers --> S2_Cops
S2_Cops -- analyzes/reports-on --> S2_LintRules
S2_Suggestor -- powers --> S2_Plugins
S2_Plugins -- writes/annotates via --> S2_Formatter
S2_Formatter -- formats --> S2_Plugins
S2_Utils -- batch-fixes --> S2_LintRules
S2_Cops -- uses --> S2_Validation

S2_StaticAnalysis --> S2_Logic

%% Integration with Data-structs & Patterns
S2_Logic -- interacts-with --> DS_StaticTodos
S2_Logic -- uses --> DS_LintMetrics
S2_Logic -- uses --> DS_RepoArtifacts
S2_Logic -- emits/feeds --> DS_TestData
S2_Logic -- patterns --> PAT_Suggestion
S2_Logic -- patterns --> PAT_Formatter
S2_Logic -- patterns --> PAT_WaitRepeat
S2_Logic -- patterns --> PAT_Error

%% === TOOLING UTILITIES & SCRIPTING ===
subgraph S3_Logic["Migration, Housekeeping, Pipeline QA, Utility Infrastructure"]
direction TB
style S3_Logic fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2,rx:16,ry:16

S3_Migrate["Migration Orchestration & Tracking"]:::core
S3_Housekeep["Schema Health/Consistency & Audit Utilities"]:::core
S3_Pipeline["Pipeline/CI Scripting & Coverage Processing"]:::util
S3_Runtime["Runtime QA/Execution Safety"]:::util
S3_Utils["Core Utility Libraries/Resetters"]:::util
S3_Error["Error/Obsolete Resource Handling"]:::error

S3_Migrate -- coordinates --> S3_Housekeep
S3_Housekeep -- updates-for --> S3_Runtime
S3_Pipeline -- quality gate --> S3_Migrate
S3_Runtime -- feedback-to --> S3_Error
S3_Utils -- library/support-for --> S3_Migrate
S3_Utils -- support-for --> S3_Housekeep
S3_Utils -- runtime-support --> S3_Runtime
S3_ToolingScripting --> S3_Logic

%% Integration with Data-structs & Patterns
S3_Logic -- uses --> DS_MigrationState
S3_Logic -- uses --> DS_FeatureFlags
S3_Logic -- uses --> DS_LintMetrics
S3_Logic -- uses --> DS_RepoArtifacts
S3_Logic -- uses --> DS_MemberEntities
S3_Logic -- patterns --> PAT_Init
S3_Logic -- patterns --> PAT_Error
S3_Logic -- supports --> DS_TestData

%% === RSPEC & AUTOMATED TESTING ===
subgraph S4_Logic["End-to-End, Feature/Component, Shared Data, Setup/Errors"]
direction TB
style S4_Logic fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2,rx:16,ry:16

S4_FeatureSpecs["Feature/Component Specs Suites"]:::core
S4_Data["Shared Test Data Factories/Seeders"]:::util
S4_Init["Suite Initialization/Env Setup"]:::init
S4_Error["Error Reporting & Failure Safety"]:::error
S4_Support["2FA/Welcome Helpers, Shared Utilities"]:::util

S4_FeatureSpecs --- S4_Data
S4_FeatureSpecs -- cross-suite error--> S4_Error
S4_FeatureSpecs -- needs/init --> S4_Init
S4_Support -- supports --> S4_FeatureSpecs

S4_RSpec --> S4_Logic

%% Integration with Data-structs & Patterns
S4_Logic -- uses/produces --> DS_TestData
S4_Logic -- uses --> DS_SecurityResults
S4_Logic -- uses --> DS_RepoArtifacts
S4_Logic -- uses --> DS_MemberEntities
S4_Logic -- uses --> DS_LintMetrics
S4_Logic -- error-pattern --> PAT_Error
S4_Logic -- setup-pattern --> PAT_Init

%% ==============================================================
%%  HIGHER-LEVEL COLLABORATION & DATA FLOW BETWEEN SUBDOMAINS
%% ==============================================================

%% SHARED DATA STRUCTURES
DS_TestData -- "common test data and factories" --> S1_Logic
DS_TestData -- "populated by test factories" --> S4_Logic
DS_TestData -- "referenced in static analysis" --> S2_Logic
DS_TestData -- "used for migrations, scripts" --> S3_Logic

DS_LintMetrics -- "reports/metrics span" --> S2_Logic
DS_LintMetrics -- "quality gates/pipeline" --> S3_Logic
DS_LintMetrics -- "used for test reporting" --> S1_Logic
DS_LintMetrics -- "used for test result analytics" --> S4_Logic

DS_MemberEntities -- "user/group context for tests/migrations" --> S4_Logic
DS_MemberEntities -- "fixture/entity in shared examples" --> S1_Logic
DS_MemberEntities -- "migration and schema validation" --> S3_Logic

DS_RepoArtifacts -- "repo/file artifacts for test + lint" --> S1_Logic
DS_RepoArtifacts -- "static scanning targets" --> S2_Logic
DS_RepoArtifacts -- "migration/file info" --> S3_Logic
DS_RepoArtifacts -- "end-to-end verification" --> S4_Logic

DS_StaticTodos -- "bidirectional with suggestion engine" --> S2_Logic
DS_StaticTodos -- "exposes registry to shared helpers" --> S1_Logic

DS_MigrationState -- "migration states/files" --> S3_Logic
DS_MigrationState -- "cross-checks for feature specs" --> S4_Logic

DS_FeatureFlags -- "enables/flags for tests/migration/checks" --> S3_Logic
DS_FeatureFlags -- "referenced for test parameterization" --> S1_Logic

DS_SecurityResults -- "feeds security testing" --> S4_Logic
DS_SecurityResults -- "consumed by lint checks" --> S2_Logic

%% SHARED ABSTRACTIONS AND PATTERNS
PAT_WaitRepeat -- "shared wait/masking pattern in test/lint" --> S1_Logic
PAT_WaitRepeat -- "used for pipeline/test stability in QA" --> S4_Logic
PAT_WaitRepeat -- "used for CI/pipeline orchestration" --> S3_Logic

PAT_Suggestion -- "engine for static analysis" --> S2_Logic
PAT_Suggestion -- "provides suggestion infra to s1 shared helpers" --> S1_Logic

PAT_Formatter -- "cross-domain reporting & result abstraction" --> S2_Logic
PAT_Formatter -- "feeds test metrics for reporting" --> S1_Logic

PAT_Init -- "setup/init for test, migration and QA" --> S1_Logic
PAT_Init -- "startup for migration and pipeline" --> S3_Logic
PAT_Init -- "suite/test env setup" --> S4_Logic

PAT_Error -- "error handling/decorators" --> S1_Logic
PAT_Error -- "propagates to reporting utils" --> S2_Logic
PAT_Error -- "handles safety/obsolete in migrations" --> S3_Logic
PAT_Error -- "logs/report errors from tests" --> S4_Logic

%% ==============================================================
%%  HIGH-LEVEL COLLABORATION BETWEEN SUBDOMAINS
%% ==============================================================

%% S1 Shared Examples & S4 RSpec: Helpers/factories/fixtures
S1_Logic -- "provides factory modules" --> S4_Logic
S1_Logic -- "parameterizes test flows" --> S4_Logic

%% S2 Static Analysis, S3 Tooling: Automation of migrations/checks
S2_Logic -- "validates/test-enforces schema" --> S3_Logic
S3_Logic -- "automates static/danger checks" --> S2_Logic

%% S2 Static Analysis, S4 RSpec: Lint/coverage/test feedback
S2_Logic -- "informs lint rules" --> S4_Logic
S4_Logic -- "exposes test cases" --> S2_Logic

%% S3 Tooling Scripting, S4 RSpec: Pipelines, migration, envs
S3_Logic -- "sets up/tears down db/test envs" --> S4_Logic
S4_Logic -- "provides scenario data & seeders" --> S3_Logic

%% S1 SharedHelpers, S3 Tooling: Test data and environment helpers
S1_Logic -- "provides test utilities to scripts" --> S3_Logic
S3_Logic -- "calls shared test helpers for QA tasks" --> S1_Logic

%% S2 Static Analysis, S1 Shared: Feed suggestion/test feedback loop
S2_Logic -- "integrates with shared metadata/examples" --> S1_Logic
S1_Logic -- "returns test example data to lint suggestion" --> S2_Logic

%% S4 RSpec, S1 Shared: Error/init patterns
S1_Logic -- "setup/init for test runs" --> S4_Logic
S4_Logic -- "uses error handling helpers" --> S1_Logic

%% S3 Tooling, S2 Static: Migration/quality feedback for lint
S3_Logic -- "outputs migration quality/status" --> S2_Logic
S2_Logic -- "feeds back migration rule warnings" --> S3_Logic

%% SUBDOMAIN -> DOMAIN-DATA IDENTITY
S1_SharedHelpers === S1_Logic
S2_StaticAnalysis === S2_Logic
S3_ToolingScripting === S3_Logic
S4_RSpec === S4_Logic

%% Domain data structs connected bottom up to main node for verticality
Domain_DataStructs --> S1_Logic
Domain_DataStructs --> S2_Logic
Domain_DataStructs --> S3_Logic
Domain_DataStructs --> S4_Logic

%% Patterns' visibility: Connect patterns to corresponding subdomain logics for domain-level visual pattern flow
PAT_WaitRepeat -. used in .-> S1_Logic
PAT_WaitRepeat -. used in .-> S4_Logic
PAT_WaitRepeat -. used in .-> S3_Logic
PAT_Suggestion -. powers .-> S2_Logic
PAT_Suggestion -. supports .-> S1_Logic
PAT_Formatter -. outputs .-> S2_Logic
PAT_Formatter -. enables reporting .-> S1_Logic
PAT_Formatter -. formats QA data .-> S3_Logic
PAT_Error -. safety/handling .-> S3_Logic
PAT_Error -. logs .-> S4_Logic
PAT_Error -. logs .-> S2_Logic
PAT_Error -. error/fixture .-> S1_Logic
PAT_Init -. setup .-> S1_Logic
PAT_Init -. startup .-> S3_Logic
PAT_Init -. initialization .-> S4_Logic

%% Data structures - vertical edges for clarity, align with pastel green color class
DS_TestData --- S1_Logic
DS_TestData --- S4_Logic
DS_TestData --- S3_Logic
DS_TestData --- S2_Logic
DS_LintMetrics --- S2_Logic
DS_LintMetrics --- S3_Logic
DS_LintMetrics --- S1_Logic
DS_LintMetrics --- S4_Logic
DS_RepoArtifacts --- S1_Logic
DS_RepoArtifacts --- S2_Logic
DS_RepoArtifacts --- S3_Logic
DS_RepoArtifacts --- S4_Logic
DS_MemberEntities --- S1_Logic
DS_MemberEntities --- S3_Logic
DS_MemberEntities --- S4_Logic

%% Final vertical arrangement
S1_Logic
S2_Logic
S3_Logic
S4_Logic

%% =============== SPACING/STYLING ===============
linkStyle default stroke-width:1.8px,stroke:#7ad0ee

%% Node vertical ordering anchor points for clarity
TQA_Core
S1_SharedHelpers
S2_StaticAnalysis
S3_ToolingScripting
S4_RSpec

%% SHARED COLOR CONTINUITY for all subdomain main nodes and core abstractions
class TQA_Core,S1_Title,S2_Title,S3_Title,S4_Title,S1_Helpers,S1_Shared,S2_Suggestor,S2_Plugins,S2_Cops,S3_Migrate,S3_Housekeep,S4_FeatureSpecs core
class S1_Utils,S1_Reporting,S2_Utils,S3_Pipeline,S3_Runtime,S3_Utils,S4_Data,S4_Support util
class S1_Patterns,S2_Formatter,DS_TestData,DS_StaticTodos,DS_LintMetrics,DS_MigrationState,DS_FeatureFlags,DS_SecurityResults,DS_RepoArtifacts,DS_MemberEntities,DS_SecurityResults,S3_Error,S4_Error,S2_LintRules,S2_Validation pattern,dataStruct,error
class PAT_WaitRepeat,PAT_Suggestion,PAT_Formatter,PAT_Error,PAT_Init pattern,error,init
class S4_Init,S3_Error,S4_Error,S2_LintRules,S2_Validation init,error
```