```mermaid
flowchart TB
  %% =========================
  %% STYLE DEFINITIONS
  %% =========================
  classDef pastelBlue fill:#D4F1F9,stroke:#5D8AA8,stroke-width:2.5px,color:#243547,rx:10,ry:10
  classDef pastelYellow fill:#FFF8DC,stroke:#E1D9AC,stroke-width:2.5px,color:#6D5A09,rx:10,ry:10
  classDef pastelGreen fill:#E0F8E0,stroke:#7FC97F,stroke-width:2.5px,color:#226522,rx:10,ry:10
  classDef pastelRed fill:#FFE4E1,stroke:#D37F7F,stroke-width:2.5px,color:#A63B2F,rx:10,ry:10
  classDef pastelPurple fill:#E6E6FA,stroke:#B9AEDC,stroke-width:2.5px,color:#4B417F,rx:10,ry:10
  classDef lightGrayGroup fill:#F8F8F8,stroke:#B3BEC0,stroke-width:3px,color:#333,rx:15,ry:15

  %% =========================
  %% DOMAIN ROOT
  %% =========================
  subgraph ROOT_DOMAIN["Authentication, Authorization & User Management Domain Overview"]
    direction TB
    style ROOT_DOMAIN fill:#F8F8F8,stroke:#243547,stroke-width:4px

    CoreConcepts[
      Domain Core Concepts & Behaviors
      - Secure access control
      - User authentication & identity
      - Authorization rules
      - License/compliance constraints
      - User onboarding & self-service flows
      - Membership & group linkage
      - Preferences, notification & status management
    ]:::pastelBlue

    %% KEY ABSTRACTIONS & PATTERNS (used across subdomains)
    subgraph DOMAIN_PATTERNS["Cross-domain Patterns & Abstractions"]
      direction TB
      style DOMAIN_PATTERNS fill:#F8F8F8,stroke:#91C99C,stroke-width:2.4px
      ServiceLayerPattern[Service Layer Pattern]:::pastelPurple
      PolicyAuthPattern[Policy/Authorization Layer]:::pastelPurple
      NotificationObsPattern[Notification/Observer Pattern]:::pastelPurple
      DataAbstraction[Data Structure Abstractions]:::pastelPurple
      DomainErrorHandling[Error, Block/Abuse Handling]:::pastelRed
    end
  end

  %% =========================
  %% SUBDOMAIN: AUTHENTICATION ABSTRACTED
  %% =========================
  subgraph AUTHN["Authentication\nCore Identity Verification, SSO, MFA, PATs, Block State"]
    direction TB
    style AUTHN fill:#F8F8F8,stroke:#61A4BC,stroke-width:3px

    AuthEntry[Authentication Entry Points\nController Callback/Routes]:::pastelBlue
    UserIdentity[User Identity & Auth State\nUser / Identity, Group Links, 2FA Enrolled]:::pastelGreen
    AuthVerifierCore[Authentication Verifier\nCore Decision Engine]:::pastelBlue
    MFAService[2FA/MFA Service\n2FA Policies & Providers]:::pastelPurple
    IDPSSOCore[Identity Provider / SSO Engine\nOAuth, LDAP, SAML, OmniAuth]:::pastelBlue
    PATManager[Personal Access Token Manager]:::pastelPurple
    BlockListEngine[Blocked User & IP Management]:::pastelRed
    AuthEventLog[Authentication Events & Audit Trail]:::pastelGreen

    class AuthEntry,AuthVerifierCore,IDPSSOCore pastelBlue
    class MFAService,PATManager pastelPurple
    class UserIdentity,AuthEventLog pastelGreen
    class BlockListEngine pastelRed
  end

  %% =========================
  %% SUBDOMAIN: USER & MEMBERSHIP MANAGEMENT (ABSTRACTED)
  %% =========================
  subgraph USER_MGMT["User & Membership Management\nProfile, Preferences, Onboarding, Invitations"]
    direction TB
    style USER_MGMT fill:#F8F8F8,stroke:#A5D8F3,stroke-width:3px

    UserProfile[User Profile & Status\nPreferences/Settings, Status Flags]:::pastelBlue
    PreferencesEngine[Preferences & Notification Engine]:::pastelYellow
    OnboardingFlow[User Onboarding & Registration\nInitial Setup, Terms, Confirms]:::pastelBlue
    InviteMgr[Invitations & Group Onboarding]:::pastelBlue
    MembershipGroup[Membership & Group Links]:::pastelGreen
    NotificationSrv[User Notification Service]:::pastelYellow
    UserDataDS[User Data Structures\nCore User, Extended Details]:::pastelGreen

    class UserProfile,OnboardingFlow,InviteMgr pastelBlue
    class PreferencesEngine,NotificationSrv pastelYellow
    class MembershipGroup,UserDataDS pastelGreen
  end

  %% =========================
  %% SUBDOMAIN: ENTERPRISE USER & SEAT LICENSING (ABSTRACTED)
  %% =========================
  subgraph ENTERPRISE_SEAT["Enterprise User & Seat Licensing\nLicense Enforcement, Provisioning, Compliance"]
    direction TB
    style ENTERPRISE_SEAT fill:#F8F8F8,stroke:#5D8AA8,stroke-width:2.7px

    SeatEnforcementCore[Seat Allocation & Enforcement\nCompliance, Overage Handling]:::pastelBlue
    UserProvisioning[Enterprise User Provisioning\nLink/Unlink, Async Deprovisioning]:::pastelBlue
    SeatDataDS[Seat & Membership Data\nBillable, Available, Roles, overage]:::pastelGreen
    BulkImporter[Bulk User Import & Group Sync]:::pastelYellow
    LicensingErrs[Licensing/Provision Errors]:::pastelRed
    AccessControlDS[Access Levels & Entitlements]:::pastelGreen

    class SeatEnforcementCore,UserProvisioning pastelBlue
    class SeatDataDS,AccessControlDS pastelGreen
    class BulkImporter pastelYellow
    class LicensingErrs pastelRed
  end

  %% =========================
  %% CROSS-SUBDOMAIN SHARED DATA STRUCTURES
  %% =========================
  subgraph SHARED_DATA["Key Domain Data Structures"]
    direction TB
    style SHARED_DATA fill:#F8F8F8,stroke:#61C48E,stroke-width:2.8px
    CanonicalUser[User\nCore Entity, ID, Credentials]:::pastelGreen
    MembershipRecord[Membership\nGroup/Org, Role]:::pastelGreen
    SeatCountsDS[Seat Counts\nBillable, Available, Overage]:::pastelGreen
    AccessLevelsDS[Access Levels/Entitlements]:::pastelGreen
    NotificationDS[Notification Preferences/Events]:::pastelGreen
  end

  %% =========================
  %% HIGH-LEVEL DOMAIN FLOW / RELATIONSHIPS
  %% =========================

  %% ---- CORE DOMAIN CONCEPTS -> SUBDOMAINS
  CoreConcepts --> AUTHN
  CoreConcepts --> USER_MGMT
  CoreConcepts --> ENTERPRISE_SEAT
  CoreConcepts --> SHARED_DATA

  %% ---- CROSS-SUBDOMAIN SHARED DATA (BI-DIRECTIONAL)
  AUTHN <--> CanonicalUser
  AUTHN <--> AccessLevelsDS
  AUTHN <--> MembershipRecord
  AUTHN --reads/feeds--> SeatCountsDS
  AUTHN --uses/feeds--> NotificationDS

  USER_MGMT <--> CanonicalUser
  USER_MGMT <--> MembershipRecord
  USER_MGMT <--> NotificationDS
  USER_MGMT --collects/updates--> AccessLevelsDS

  ENTERPRISE_SEAT <--> CanonicalUser
  ENTERPRISE_SEAT <--> MembershipRecord
  ENTERPRISE_SEAT --allocates/enforces--> SeatCountsDS
  ENTERPRISE_SEAT <--> AccessLevelsDS

  %% ==== COLLABORATIVE FLOWS AMONG SUBDOMAINS ====

  %% Authentication triggers provisioning actions
  AUTHN --[on SSO/SCIM]--> ENTERPRISE_SEAT

  %% Provisioning flows update authentication/authorization state
  ENTERPRISE_SEAT --[on Provision/Deprovision]--> AUTHN
  ENTERPRISE_SEAT --[sync]--> USER_MGMT

  %% Onboarding (USER_MGMT) updates CanonicalUser and triggers notifications
  USER_MGMT --[on Onboarding]--> AUTHN
  USER_MGMT --[inits/updates]--> SHARED_DATA

  %% Preferences/notification changes impact authentication and onboarding behaviors
  USER_MGMT --[preference, status, notif events]--> AUTHN

  %% Licensing/seat limits impact onboarding and invitations
  ENTERPRISE_SEAT --[enforces/limits]--> USER_MGMT

  %% Notifications for licensing, onboarding, auth events all use shared notification DS
  AUTHN --[security/email events]--> NotificationDS
  USER_MGMT --[preferences, onboarding, callouts]--> NotificationDS
  ENTERPRISE_SEAT --[seat warning/overage/compliance]--> NotificationDS

  %% Error propagation/abuse/lockout
  AUTHN --[error, lock, abuse]--> DomainErrorHandling
  USER_MGMT --[invite, verification, callout error]--> DomainErrorHandling
  ENTERPRISE_SEAT --[seat block, overage, compliance error]--> LicensingErrs
  ENTERPRISE_SEAT --[error]--> DomainErrorHandling

  %% Service/abstraction layers feeding all subdomains
  AUTHN --[service layer]--> ServiceLayerPattern
  USER_MGMT --[service, policy]--> ServiceLayerPattern
  ENTERPRISE_SEAT --[service, policy]--> ServiceLayerPattern

  AUTHN --[policy, enforcement]--> PolicyAuthPattern
  USER_MGMT --[onboarding, callouts, pref policies]--> PolicyAuthPattern
  ENTERPRISE_SEAT --[license/seat policies]--> PolicyAuthPattern

  AUTHN --[audit, notification events]--> NotificationObsPattern
  USER_MGMT --[email, UI, onboarding events]--> NotificationObsPattern
  ENTERPRISE_SEAT --[license/usage alert]--> NotificationObsPattern

  %% Data abstraction, shared across
  AUTHN --[uses]--> DataAbstraction
  USER_MGMT --[uses]--> DataAbstraction
  ENTERPRISE_SEAT --[uses]--> DataAbstraction

  %% Error patterns
  DomainErrorHandling --[reports to]--> CoreConcepts
  LicensingErrs --[escalates to]--> DomainErrorHandling

  %% =========================
  %% LOGICAL RELATIONSHIPS & FLOW BETWEEN CORE ASPECTS
  %% =========================

  %% AUTHN flow
  AuthEntry --> AuthVerifierCore
  AuthVerifierCore --[may enforce MFA]--> MFAService
  AuthVerifierCore --[may use SSO]--> IDPSSOCore
  AuthVerifierCore --[may verify PAT]--> PATManager
  AuthVerifierCore --[may block user]--> BlockListEngine
  AuthVerifierCore --> UserIdentity
  AuthVerifierCore --[logs]--> AuthEventLog

  %% USER MGMT flow
  OnboardingFlow --> UserProfile
  OnboardingFlow --> InviteMgr
  OnboardingFlow --> PreferencesEngine
  OnboardingFlow --[adds member]--> MembershipGroup
  InviteMgr --> OnboardingFlow
  PreferencesEngine --[applies notification]--> NotificationSrv
  PreferencesEngine --[updates]--> UserProfile
  NotificationSrv --> NotificationDS

  %% ENTERPRISE SEAT flow
  UserProvisioning --[assigns user]--> SeatEnforcementCore
  UserProvisioning --[feeds membership]--> MembershipGroup
  SeatEnforcementCore --[counts/seats]--> SeatDataDS
  SeatEnforcementCore --[checks entitlements]--> AccessControlDS
  SeatEnforcementCore --[enforces/compliance]--> LicensingErrs
  BulkImporter --[group sync/provisioning]--> UserProvisioning

  %% Linking subdomain data with shared DS
  UserIdentity --[primary]--> CanonicalUser
  MembershipGroup --> MembershipRecord
  SeatDataDS --> SeatCountsDS
  PreferencesEngine --> NotificationDS
  AccessControlDS --> AccessLevelsDS

  %% =========================
  %% CLASS ASSIGNMENTS AT END FOR OVERRIDES
  %% =========================
  class CoreConcepts,AuthEntry,AuthVerifierCore,IDPSSOCore,MFAService,PATManager,OnboardingFlow,InviteMgr,UserProfile,PreferencesEngine,SeatEnforcementCore,UserProvisioning,BulkImporter pastelBlue
  class AuthEventLog,UserIdentity,UserDataDS,MembershipGroup,MembershipRecord,CanonicalUser,SeatCountsDS,SeatDataDS,AccessLevelsDS,AccessControlDS,NotificationDS pastelGreen
  class NotificationSrv pastelYellow
  class BlockListEngine,LicensingErrs,DomainErrorHandling pastelRed
  class ServiceLayerPattern,PolicyAuthPattern,NotificationObsPattern,DataAbstraction pastelPurple
  class lightGrayGroup lightGrayGroup
```