```mermaid
flowchart TB
%% COMBINED DOMAIN: AI, Machine Learning & Code Assistance -> AI & LLM Features
%% Vertical layout for top-level domain/subdomain structure

%% --- COLOR/STYLE DEFINTIONS ---
%% Core domain: pastel blue (#D4F1F9)
%% Supporting/utility: pastel yellow (#FFF8DC)
%% Data structures: pastel green (#E0F8E0)
%% Error: pastel red (#FFE4E1)
%% Initialization/setup: pastel purple (#E6E6FA)
%% Groupings: #F8F8F8, with pastel borders/consistent subdomain color
%% Node shape: all are rounded rectangles

%% --- DOMAIN AND SUBDOMAINS ---

subgraph AI_LLM_FEATURES_DOMAIN["AI & LLM Features":::domainGroup]
  direction TB

  %% ===== High-level Subdomain Nodes =====

  subgraph AI_LLM_CHAT_GROUP["AI Chat, Duo Workflows & Gateways":::coreGroup]
    direction TB
    aiChatCore["Conversational Threading, Duo Workflows, AI Agents":::core]
    aiGateway["LLM Gateway Integration & API":::core]
    chatDataStruct["Chat & Workflow Entities":::dataStruct]
    chatGraphQl["GraphQL & API Layer":::support]
    aiAgentMgmt["Agent Lifecycle/Management":::support]
  end

  subgraph PROMPT_PROVIDERS_GROUP["Prompt Providers & Tools":::coreGroup]
    direction TB
    promptBase["Prompt Construction & Utilities":::core]
    promptTemplates["Prompt/Action Templates":::core]
    promptExec["Tool Executors & Providers":::core]
    promptModelsDs["Model Versions, Params, Supported Models":::dataStruct]
  end

  subgraph CODE_SUGGESTIONS_GROUP["AI Code Suggestions & Completion":::coreGroup]
    direction TB
    codeSuggestLogic["Suggestion Tasks & Code Completion Flows":::core]
    codePrompting["LLM/Provider Integration & Inference":::core]
    codeDataPersistence["Suggestion Events, Agent Versioning":::dataStruct]
    codeAccessAuth["Access Control, Usage Metrics, Authorization":::support]
    aiResourceAdapters["Resource Wrappers Issues, Epics, Builds":::core]
    codeTemplates["Suggestion/Review Templates":::support]
  end

  subgraph EXP_FLAGS_GROUP["Experimentation & Feature Flags":::coreGroup]
    direction TB
    expFramework["Experimentation Core":::core]
    featFlagsOps["Feature Flag Operations":::support]
    aiSettings["AI Feature Setting Models":::dataStruct]
    featCascade["Enablement Cascade/Policy Logic":::support]
    featAdmin["Feature Setting Admin/Controllers":::support]
    expGraphqlApi["Feature/Experiment GraphQL API":::support]
    expLlminfra["LLM/AI Feature Authorizer & Utilities":::support]
  end
end

%% ===== GLOBAL DOMAIN DATA & PATTERNS drawn as spanning layers =====

subgraph DOMAIN_DATAS["Domain Data Structures & Patterns":::datasGroup]
  direction TB
  aiThreadEntity["Thread/Conversation/Data":::dataStruct]
  workflowEntity["Workflow/Checkpoint/Event Data":::dataStruct]
  agentVersion["Agent & Versioning Data":::dataStruct]
  featureSetting["Feature Setting Entity":::dataStruct]
  suggestionEvent["Suggestion/Usage Event":::dataStruct]
  promptVersioning["Prompt Versioning Structures":::dataStruct]
  contextStruct["AI/LLM Context Carrier":::dataStruct]
end

%% ====== EDGE RELATIONSHIPS, INTERACTIONS & FLOW ======

%% --- AI Chat <-> Prompt Providers ---
aiChatCore -- prompts/templates/executes --> promptTemplates
aiChatCore -- interacts with --> promptBase
aiGateway -- uses providers --> promptExec
aiAgentMgmt -- instantiates/updates agents --> promptModelsDs
aiGateway -- routes to --> promptModelsDs

%% --- Prompt Providers to Suggestion Domain ---
promptExec -- delivers executable prompts --> codePrompting
promptTemplates -- supply templates --> codeTemplates
promptBase -- prompt/model/params mapping --> promptModelsDs
promptBase -- assigns context structures --> contextStruct
promptModelsDs -- shared model/version definitions --> codePrompting
promptBase -- prompt entity versioning --> promptVersioning

%% --- Suggestion Domain Core Flows ---
codeSuggestLogic -- uses/extracts context/entities --> contextStruct
codePrompting -- orchestrates LLM/calls gateway --> aiGateway
codePrompting -- uses models/templates --> promptTemplates
codePrompting -- uses versioned prompt structure --> promptVersioning
codeSuggestLogic -- records --> suggestionEvent
codeSuggestLogic -- uses/updates agent/version --> agentVersion
codeSuggestLogic -- accesses resources --> aiResourceAdapters
codeSuggestLogic -- applies policy/auth --> codeAccessAuth

%% --- Experimentation & Feature Flags connections ---
expFramework -- governs rollout --> featFlagsOps
expFramework -- enables features/settings --> aiSettings
featCascade -- applies feature changes --> aiSettings
featCascade -- triggers enable for workflows --> aiChatCore
aiSettings -- tags data/entities --> featureSetting
featAdmin -- controls mutation/settings --> aiSettings
expGraphqlApi -- exposes setting/experiment types --> aiSettings
expLlminfra -- authorizes/validates features --> aiSettings
expLlminfra -- supplies flags to --> codeAccessAuth

%% --- GraphQL, API, Gateway Collaboration ---
aiGateway -- exposes workflows --> chatGraphQl
chatGraphQl -- exposes conversation/workflow data --> aiChatCore
chatGraphQl -- exposes suggestion/code actions --> codeSuggestLogic
expGraphqlApi -- exposes feature/setting queries --> aiSettings
codePrompting -- used by API/gateway for inference --> aiGateway
promptModelsDs -- used for API schema/enums --> chatGraphQl

%% --- DATA STRUCTURE FLOWS AND SHARED PATTERNS ---

aiChatCore -- stores/reads --> aiThreadEntity
aiChatCore -- stores/reads --> workflowEntity
promptExec -- uses model/version info --> agentVersion
promptBase -- accesses --> promptVersioning
codeSuggestLogic -- stores usage events --> suggestionEvent
aiSettings -- holds setting objects --> featureSetting
codeSuggestLogic -- uses messages/context --> contextStruct
aiResourceAdapters -- maps to external resources --> aiThreadEntity
aiGateway -- serializes context --> contextStruct
expLlminfra -- uses feature/entity data --> featureSetting

%% --- KEY ABSTRACTION FLOW ---
promptBase -- supplies prompt logic --> promptExec
codePrompting -- resolves prompt models --> promptModelsDs
featCascade -- orchestrates enablement for all subdomains --> aiSettings

%% --- DOMAIN COLLABORATION FLOW ---
expFramework -- enables/experiments with --> AI_LLM_CHAT_GROUP
expFramework -- enables/experiments with --> CODE_SUGGESTIONS_GROUP
expFramework -- enables/experiments with --> PROMPT_PROVIDERS_GROUP
featCascade -- enables/cascades to --> CODE_SUGGESTIONS_GROUP
featCascade -- enables/cascades to --> PROMPT_PROVIDERS_GROUP

%% --- ERRORS AND INITIALIZATION, ABSTRACT ---
classDef domainGroup fill:#F8F8F8,stroke:#2890AF,stroke-width:4px,color:#1F355D,rx:16,ry:16;
classDef coreGroup fill:#D4F1F9,stroke:#86C1E1,stroke-width:2px,color:#1A2B3C,rx:10,ry:10;
classDef datasGroup fill:#F8F8F8,stroke:#A4E7A4,stroke-width:2px,color:#195D23,rx:10,ry:10;
classDef support fill:#FFF8DC,stroke:#C3B697,stroke-width:2px,color:#895A00,rx:8,ry:8;
classDef dataStruct fill:#E0F8E0,stroke:#89BFA4,stroke-width:2px,color:#194333,rx:8,ry:8;
classDef errorNode fill:#FFE4E1,stroke:#EFADA9,stroke-width:2px,color:#8C4442,rx:8,ry:8;
classDef initNode fill:#E6E6FA,stroke:#7A6BAF,stroke-width:2px,color:#212036,rx:8,ry:8;

%% Node shapes rounded rectangles - default; others if needed

%% --- SUBGROUP STYLES ---
class AI_LLM_FEATURES_DOMAIN,AI_LLM_CHAT_GROUP,PROMPT_PROVIDERS_GROUP,CODE_SUGGESTIONS_GROUP,EXP_FLAGS_GROUP,DOMAIN_DATAS fill:#F8F8F8,stroke-width:2px;

%% --- KEY DATA STRUCTURE GROUP ---
class aiThreadEntity,workflowEntity,agentVersion,featureSetting,suggestionEvent,promptVersioning,contextStruct dataStruct;

%% --- GROUP INTERNAL CLASSES ---
class aiChatCore,aiGateway,aiAgentMgmt,chatGraphQl core;
class chatDataStruct dataStruct;
class promptBase,promptTemplates,promptExec,codePrompting,aiResourceAdapters core;
class promptModelsDs,codeDataPersistence codeTemplates,codeAccessAuth support;
class expFramework,expLlminfra core;
class featFlagsOps,featCascade,featAdmin,expGraphqlApi,support;
class aiSettings dataStruct;
class codeSuggestLogic,codeTemplates,aiResourceAdapters core;

%% --- LAYOUT SPACING ---
AI_LLM_CHAT_GROUP --> PROMPT_PROVIDERS_GROUP
PROMPT_PROVIDERS_GROUP --> CODE_SUGGESTIONS_GROUP
CODE_SUGGESTIONS_GROUP --> EXP_FLAGS_GROUP

%% --- DATA STRUCTURE GROUP ASSOCIATIONS (spanning) ---
AI_LLM_CHAT_GROUP -- accesses --> DOMAIN_DATAS
PROMPT_PROVIDERS_GROUP -- shares/uses --> DOMAIN_DATAS
CODE_SUGGESTIONS_GROUP -- stores/reads --> DOMAIN_DATAS
EXP_FLAGS_GROUP -- tags/enables --> DOMAIN_DATAS

%% --- DOMAIN LEGEND (as subgraph at bottom for pattern reference) ---
subgraph DOMAIN_LEGEND["Legend & Patterns Abbreviated":::datasGroup]
  direction TB
  style DOMAIN_LEGEND fill:#F8F8F8,stroke:#CECECE,stroke-width:1px
  legend1["Pastel blue: Core/primary domain logic":::core]
  legend2["Pastel green: Data structures/entities":::dataStruct]
  legend3["Pastel yellow: Supporting/utility, auth, API":::support]
  legend4["Pastel red: Error, fallback nodes":::errorNode]
  legend5["Pastel purple: Initialization/setup":::initNode]
end

%% --- END OF DIAGRAM ---
```