```mermaid
flowchart TD
%% ----------------------------------------------------------
%% COLOR & SHAPE DEFINITIONS
%% ----------------------------------------------------------
%% Core domain: #D4F1F9
%% Supporting/utility: #FFF8DC
%% Data structure: #E0F8E0
%% Error handling: #FFE4E1
%% Initialization/setup: #E6E6FA
%% Subgraph: #F8F8F8
%% Borders (AwardEmoji): #A8D8EA, (Todos): #D4F1F9, (Supporting): #FFD580, (Batch): #A7D7A1, (EE): #CBAACB

%% ----------------------------------------------------------
%% ROOT DOMAIN
%% ----------------------------------------------------------
subgraph sgDomain["Source Code, Repository & Collaboration → Collaboration & Discussion" ]
  direction TB
  style sgDomain fill:#F8F8F8,stroke-width:3,stroke:#D4F1F9,rounded

  %% ==== CORE ENTITIES/SHARED DATA STRUCTURES spanning subdomains ======================
  subgraph sgCoreEntities["Core Collaboration Entities"]
    direction TB
    style sgCoreEntities fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded

    AE[shape="rounded-rectangle" "AwardEmoji\n- user reactions storage\n- event broadcast"]
    style AE fill:#D4F1F9,stroke:#A8D8EA,stroke-width:2

    TODO[shape="rounded-rectangle" "Todo\n- personal user reminders\n- action tracking"]
    style TODO fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2

    SNOTES[shape="rounded-rectangle" "System Notes\n- audit trail for activity\ndiscussion system events"]
    style SNOTES fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2

    NSVC[shape="rounded-rectangle" "NotificationService\n- orchestrates notifications\nfor discussion, todos, emoji"]
    style NSVC fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2

    NOTIFREC[shape="rounded-rectangle" "NotificationRecipient\n- associates notifications and users"]
    style NOTIFREC fill:#D4F1F9,stroke:#A8D8EA,stroke-width:2

    USERMENTION[shape="rounded-rectangle" "CommitUserMention\n- user mentions in commits & issues"]
    style USERMENTION fill:#D4F1F9,stroke:#A8D8EA,stroke-width:2

    APIEntitiesTodo[shape="rounded-rectangle" "API Entities\n- API surfaces for collaboration objects"]
    style APIEntitiesTodo fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2

  end

  %% ===================== SUBDOMAINS ========================================
  %% -------------------- TODOS & SYSTEM NOTES -------------------------------
  subgraph sgTodosSD["Todos & System Notes\nPersonal reminders, quick actions, audit events" ]
    direction TB
    style sgTodosSD fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded

    TodosFinder["TodosFinder\n- finds/filter personal todos"]
    style TodosFinder fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

    TodoPresenter["TodoPresenter\n- API/view formatting"]
    style TodoPresenter fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

    PendingTodosFinder["PendingTodosFinder\n- finds only pending todos"]
    style PendingTodosFinder fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

    QuickActionsInterp["QuickActionsInterpretService\n- parses/executes quick commands"]
    style QuickActionsInterp fill:#FFF8DC,stroke:#FFD580,stroke-width:2,rounded-rectangle

    AllowedTargetFilter["AllowedTargetFilterService\n- cross-resource access checks"]
    style AllowedTargetFilter fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

    TodoPolicy["TodoPolicy\n- governs todo actions"]
    style TodoPolicy fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

    DashboardTodosController["Dashboard::TodosController\n- user dashboard todos UI"]
    style DashboardTodosController fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

    TodosAsyncWorker["Todos Async Workers\n- batch/bulk, clean up, destroy"]
    style TodosAsyncWorker fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rounded-rectangle

    TodosServices["Todos Domain Services\n- snoozing, filters, destroy"]
    style TodosServices fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

    SystemNotes["SystemNoteMetadata\n- tracks system-level note events"]
    style SystemNotes fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rounded-rectangle

    QuickActionsStatus["QuickActionsStatus\n- feedback for quick action usage"]
    style QuickActionsStatus fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rounded-rectangle

    TodosGQLResolver["Todos GraphQL API\n- API resolvers & mutations"]
    style TodosGQLResolver fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

  end

  %% -------------------- AWARD EMOJIS & NOTIFICATIONS -------------------------
  subgraph sgAwardEmojiSD["Award Emojis & Notifications\nFlexible reactions, notification management" ]
    direction TB
    style sgAwardEmojiSD fill:#F8F8F8,stroke:#A8D8EA,stroke-width:2,rounded

    AwardEmojisFinder["AwardEmojisFinder\n- flexible retrieval by user, context"]
    style AwardEmojisFinder fill:#FFF8DC,stroke:#FFD580,stroke-width:2,rounded-rectangle

    AwardEmojiSvcLayer["AwardEmoji Services\n- base, add, destroy, toggle, collect user emoji"]
    style AwardEmojiSvcLayer fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

    AwardEmojiPresenter["AwardEmojiPresenter\n- decorates for UI"]
    style AwardEmojiPresenter fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

    AwardEmojiHelper["AwardEmojiHelper\n- UI support, URLs, tags"]
    style AwardEmojiHelper fill:#FFF8DC,stroke:#FFD580,stroke-width:2,rounded-rectangle

    ToggleAwardEmoji["ToggleAwardEmoji\n- controller concern"]
    style ToggleAwardEmoji fill:#FFF8DC,stroke:#FFD580,stroke-width:2,rounded-rectangle

    AwardEmojiPolicy["AwardEmojiPolicy\n- action permissions"]
    style AwardEmojiPolicy fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

    EmojiBatchLoader["BatchLoader\n- efficiently aggregates emoji reactions"]
    style EmojiBatchLoader fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rounded-rectangle

    NotificationSetting["NotificationSetting\n- user notification preferences"]
    style NotificationSetting fill:#D4F1F9,stroke:#A8D8EA,stroke-width:2,rounded-rectangle

    EEEmojiHelpers["EE Extension Widgets\n- epics, batch loaders, overrides"]
    style EEEmojiHelpers fill:#CBAACB,stroke:#CBAACB,stroke-width:2,rounded-rectangle

    EmojiNameValidator["EmojiNameValidator\n- canonicalizes/validates custom emoji"]
    style EmojiNameValidator fill:#FFF8DC,stroke:#FFD580,stroke-width:2,rounded-rectangle

    CustomEmoji["CustomEmoji\n- user-defined/uploaded emoji"]
    style CustomEmoji fill:#D4F1F9,stroke:#A8D8EA,stroke-width:2,rounded-rectangle

    NotifyHelper["NotifyHelper\n- merge req notification utils"]
    style NotifyHelper fill:#FFF8DC,stroke:#FFD580,stroke-width:2,rounded-rectangle
  end

end

%% ========================= DOMAIN COLLABORATION FLOWS ===============================

%% Shared entities are used by both subdomains
AE -.cross domain-.- sgTodosSD
AE -.domain data.-> sgAwardEmojiSD
TODO -.collaborative data.-> sgTodosSD
SNOTES -.history records.-> sgTodosSD
SNOTES -.history context.-> sgAwardEmojiSD
NSVC -.notifies.-> sgTodosSD
NSVC -.notifies.-> sgAwardEmojiSD
NOTIFREC -.delivers notifications.-> sgAwardEmojiSD

%% Todos & System Notes data flow
DashboardTodosController -- "queries & renders" --> TodosFinder
DashboardTodosController -- "executes commands" --> QuickActionsInterp
TodosFinder -- "filters & permission checks" --> TodoPolicy
TodosFinder -- "formats result" --> TodoPresenter
TodosFinder -- "finds pending" --> PendingTodosFinder
TodosFinder -- "sends reminders, state" --> TodosServices
TodoPresenter -- "outputs data" --> APIEntitiesTodo
TodosFinder -- "system note event audit" --> SystemNotes
QuickActionsInterp -- "updates status" --> QuickActionsStatus
QuickActionsInterp -- "triggers event" --> SystemNotes
TodosFinder -- "dispatches batch jobs" --> TodosAsyncWorker
TodosServices -- "triggered by workers" --> TodosAsyncWorker
TodosGQLResolver -- "queries domain" --> TodosFinder
TodosGQLResolver -- "performs actions" --> TodosServices
TodosGQLResolver -- "formats output" --> TodoPresenter

%% Award Emoji domain data flow
AwardEmojisFinder -- "used by services & APIs" --> AE
AwardEmojiSvcLayer -- "acts on" --> AE
AwardEmojiSvcLayer -- "presented by" --> AwardEmojiPresenter
AwardEmojiSvcLayer -- "queries helper" --> AwardEmojiHelper
AwardEmojiSvcLayer -- "policy checks" --> AwardEmojiPolicy
AwardEmojiSvcLayer -- "batch actions" --> EmojiBatchLoader
AwardEmojiSvcLayer -- "updates notification settings" --> NotificationSetting
ToggleAwardEmoji -- "triggers service" --> AwardEmojiSvcLayer
AwardEmojiHelper -- "ui/urls for emojis" --> AE
AwardEmojiHelper -- "verifies" --> EmojiNameValidator
EmojiNameValidator -- "validates" --> CustomEmoji
NotifyHelper -- "links notifications" --> NotificationSetting
AwardEmojiPresenter -- "exposes model" --> APIEntitiesTodo
EEEmojiHelpers -- "extend helpers/batch/loaders" --> AwardEmojiSvcLayer

%% Bidirectional notification collaboration for both subdomains
DashboardTodosController -- "notifies via" --> NSVC
TodosServices -- "triggers notifications" --> NSVC
AwardEmojiSvcLayer -- "triggers notifications" --> NSVC
AwardEmojiSvcLayer -- "records event" --> SNOTES
NSVC -- "delivers to recipients" --> NOTIFREC
NSVC -- "delivers to user mentions" --> USERMENTION

%% Domain integration: quick actions, discussion, emoji reactions, todos
QuickActionsInterp -- "awards emoji via commands" --> AwardEmojiSvcLayer
QuickActionsInterp -- "creates/removes todos" --> TodosFinder
QuickActionsInterp -- "records system note" --> SNOTES
ToggleAwardEmoji -- "used as entry from comments" --> DashboardTodosController
DashboardTodosController -- "shows emoji state" --> AwardEmojiPresenter

%% Data structure span: API entities cover multiple concepts
APIEntitiesTodo -.exposes todos, emoji and system  in APIs.-.> sgTodosSD
APIEntitiesTodo -.exposes todos, emoji and system  in APIs.-.> sgAwardEmojiSD

%% EE extension & batch loading relationships
EEEmojiHelpers -- "provide EE-specific batch load, widget logic" --> EmojiBatchLoader
EEEmojiHelpers -- "override base reactions" --> AwardEmojiSvcLayer

%% Subdomain-specific to domain integration (vertical, logical)
sgCoreEntities --> sgTodosSD
sgCoreEntities --> sgAwardEmojiSD
sgTodosSD --> sgAwardEmojiSD

%% ---- HIGH LEVEL COLLABORATION LOGIC: VERTICAL FLOW ----
sgDomain --> sgCoreEntities
sgCoreEntities --> sgTodosSD
sgCoreEntities --> sgAwardEmojiSD
sgTodosSD --> sgAwardEmojiSD
sgAwardEmojiSD --> NSVC

%% ================= STYLED LAYOUT GLUE/ORDERING ========================
classDef core fill:#D4F1F9,stroke:#A8D8EA,stroke-width:2,rounded-rectangle
classDef utility fill:#FFF8DC,stroke:#FFD580,stroke-width:2,rounded-rectangle
classDef data fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rounded-rectangle
classDef error fill:#FFE4E1,stroke:#FFE4E1,stroke-width:2,rounded-rectangle
classDef setup fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rounded-rectangle
classDef ee fill:#CBAACB,stroke:#CBAACB,stroke-width:2,rounded-rectangle

class AE,TODO,APIEntitiesTodo,NOTIFREC,USERMENTION core
class SystemNotes,QuickActionsStatus,AwardEmojiPresenter,EmojiBatchLoader data
class QuickActionsInterp,AwardEmojiHelper,ToggleAwardEmoji,NotifyHelper utility
class TodoPolicy,AllowedTargetFilter,AwardEmojiPolicy core
class TodosAsyncWorker,NSVC setup
class EEEmojiHelpers ee

%% ----------------------------------------------------------------------
```