```mermaid
flowchart TB
%% LAYOUT & STYLE
%% --- Global Color/Style Definitions --- %%
classDef core fill:#D4F1F9,stroke:#8AC6D1,stroke-width:2,rx:14,ry:14
classDef data fill:#E0F8E0,stroke:#98DB9A,stroke-width:2,rx:14,ry:14
classDef util fill:#FFF8DC,stroke:#FFD700,stroke-width:2,rx:14,ry:14
classDef error fill:#FFE4E1,stroke:#FFAAAA,stroke-width:2,rx:14,ry:14
classDef init fill:#E6E6FA,stroke:#CBAACB,stroke-width:2,rx:14,ry:14
classDef groupbox fill:#F8F8F8,stroke:#C9D6E3,stroke-width:4,rx:18,ry:18
classDef border_vuln fill:#F8F8F8,stroke:#3656A8,stroke-width:2
classDef border_exports fill:#F8F8F8,stroke:#A279E8,stroke-width:2
classDef border_incident fill:#F8F8F8,stroke:#97C3E6,stroke-width:2

%% =========== DOMAIN ROOT CLUSTER =========== %%
subgraph root["Security, Compliance & Policies :: Vulnerability & Incident Management"]
direction TB
class root groupbox

%% --- SUBDOMAINS LOGICAL GROUPINGS VERTICAL --- %%
subgraph S1["Findings & Scanners Attack Surface Discovery & Ingestion"]
direction TB
class S1 border_vuln

VULN_CORE["Vulnerability\ncore models, relationships":::core]
FINDINGS_SCANNERS["Findings, Identifiers,\nEvidence, Scanners":::core]
SCAN_PIPELINE["Scan Pipeline & Ingestion":::core]
SCAN_SETUP["Scan Setup/\nCI Config":::init]
VULN_PRESENTATION["Presentation, Search, Serializers":::util]
VULN_POLICIES["Policies, Permissions, Enum Data":::util]
VULN_REMOVAL["Bulk Ops,\nAudit/Bulk Services":::core]
VULN_WORKERS["Async Workers":::init]
VULN_GRAPHQL["GraphQL API Layer":::core]
DAST_PROFILES["DAST\nProfiles & Schedules":::core]

VULN_CORE --> FINDINGS_SCANNERS
FINDINGS_SCANNERS --> SCAN_PIPELINE
SCAN_PIPELINE --> VULN_CORE
SCAN_SETUP -->|configure| SCAN_PIPELINE
VULN_PRESENTATION --> VULN_CORE
VULN_POLICIES --> VULN_CORE
VULN_REMOVAL --> VULN_CORE
VULN_WORKERS --> VULN_CORE
VULN_GRAPHQL --> VULN_CORE
DAST_PROFILES --> SCAN_PIPELINE

class VULN_CORE,FINDINGS_SCANNERS,SCAN_PIPELINE,SCAN_SETUP,DAST_PROFILES,VULN_REMOVAL,VULN_GRAPHQL core
class VULN_PRESENTATION,VULN_POLICIES util
class VULN_WORKERS,SCAN_SETUP init

end

subgraph S2["Vulnerability Exports & Reports Visibility, Sharing, Analytics"]
direction TB
class S2 border_exports

VULN_EXPORTS["Vulnerability Exports\ncore orchestration, part/archival state":::core]
EXPORT_SERVICES["Export & CSV/Batch Services":::util]
EXPORT_WORKERS["Background Processing Workers":::init]
EXPORT_API["API / Controller\nIntegration":::util]
VULN_STATS["Vulnerability Statistics,\nAnalytics, Historical Data":::data]
SEC_GRAPHQL_REPORT["GraphQL Interfaces,\nResolvers, Mutations":::util]
VULN_INGESTION["Ingestion: Findings, ETL,\nMapping, UUIDs":::util]
SEC_CONFIG["Security Config\nbehavioral integration":::util]
ISSUE_LINKS["Bulk Issue Linking,\nRemovals, Event Decorations":::util]
VULN_SERIALIZERS["Serializers & Entities":::util]
EVENT_WORKERS["Event-Driven Workers":::init]
ENUMS_DS["Domain Enums & Shared Data":::data]
LLM_BG_MIG["Background\nMigrations, LLM Template":::init]

VULN_EXPORTS --> EXPORT_SERVICES
EXPORT_SERVICES --> EXPORT_WORKERS
EXPORT_API --> VULN_EXPORTS
VULN_EXPORTS --> VULN_STATS
VULN_EXPORTS --> SEC_GRAPHQL_REPORT
VULN_EXPORTS --> VULN_INGESTION
VULN_EXPORTS --> VULN_SERIALIZERS
VULN_EXPORTS --> EVENT_WORKERS
VULN_EXPORTS --> ISSUE_LINKS
VULN_EXPORTS --> ENUMS_DS
VULN_STATS --> SEC_GRAPHQL_REPORT
VULN_STATS --> LLMMIG
EXPORT_API --> SEC_CONFIG

class VULN_EXPORTS core
class EXPORT_SERVICES,EXPORT_API,ISSUE_LINKS,VULN_SERIALIZERS,SEC_CONFIG,VULN_INGESTION,SEC_GRAPHQL_REPORT util
class VULN_STATS,ENUMS_DS data
class EXPORT_WORKERS,EVENT_WORKERS,LLM_BG_MIG init

end

subgraph S3["Incident & Audit Events Incident Response, Policy Enforcement"]
direction TB
class S3 border_incident

INCIDENT_MODELS["Incident Core Models,\nOncall, Escalation":::core]
INCIDENT_TIMELINE["Timeline: Events, Tagging,\nEvent Finder":::core]
INCIDENT_ESCALATION["Escalation: Policies,\nPending, SLA, Rules":::core]
INCIDENT_ALERT_LINK["Alert/Incident Linking\n& External Integrations":::core]
ONCALL_MGMT["Oncall Schedules & Rotations":::data]
AUDIT_EVENT_DATA["Audit Events,\nPresentation, API":::data]
INCIDENT_WORKERS["Async & Background\nWorkers":::init]
INCIDENT_CONTROLLERS["Controllers,\nREST APIs":::util]
INCIDENT_POLICIES["Incident Policies\n& Permissions":::core]
ALERT_MGMT["Alert Mgmt, Extractors,\nTODOs, Webhook":::core]

INCIDENT_MODELS --> INCIDENT_TIMELINE
INCIDENT_MODELS --> INCIDENT_ESCALATION
INCIDENT_ESCALATION --> INCIDENT_WORKERS
INCIDENT_MODELS --> ONCALL_MGMT
INCIDENT_MODELS --> INCIDENT_ALERT_LINK
INCIDENT_ALERT_LINK --> ALERT_MGMT
INCIDENT_MODELS --> AUDIT_EVENT_DATA
INCIDENT_MODELS --> INCIDENT_CONTROLLERS
INCIDENT_MODELS --> INCIDENT_POLICIES
INCIDENT_POLICIES -->|enforce| INCIDENT_MODELS
AUDIT_EVENT_DATA --> INCIDENT_CONTROLLERS

class INCIDENT_MODELS,INCIDENT_TIMELINE,INCIDENT_ESCALATION,INCIDENT_ALERT_LINK,INCIDENT_POLICIES,ALERT_MGMT core
class INCIDENT_WORKERS init
class ONCALL_MGMT,AUDIT_EVENT_DATA data
class INCIDENT_CONTROLLERS util

end

%% =========== INTER-SUBDOMAIN RELATIONSHIPS ============== %%

%% ----- CORE DATA FLOWS BETWEEN SUBDOMAINS VERTICAL CONNECTIONS ----- %%
VULN_CORE -- "incident triggers or associated with" --> INCIDENT_MODELS
FINDINGS_SCANNERS -- "issues/alerts create findings" --> ALERT_MGMT
VULN_EXPORTS -- "exports consumed by compliance/audit" --> AUDIT_EVENT_DATA
VULN_STATS -- "feeds incident and audit reporting" --> AUDIT_EVENT_DATA
INCIDENT_MODELS -- "incident escalations update vuln state" --> VULN_CORE
INCIDENT_WORKERS -- "trigger bulk dismissal / resolve in vuln" --> VULN_REMOVAL
VULN_POLICIES -- "enforced at incident level" --> INCIDENT_POLICIES
EXPORT_WORKERS -- "purge/trigger on incident closure" --> INCIDENT_WORKERS
VULN_GRAPHQL -- "surface incident state for vuln" --> SEC_GRAPHQL_REPORT
LLM_BG_MIG -- "migrates or enhances compliance & incident analytics" --> VULN_STATS
SEC_CONFIG -- "project scanning config applies to incidents" --> INCIDENT_MODELS

%% ----- DOMAIN-SPANNING DATA STRUCTURES ------ %%

DATA_VULN["Vulnerability Domain Model\nacross all subdomains":::core]
DATA_PROJECT["Project/Group/Namespace Context":::data]
DATA_FINDING["Finding atomic evidence, for vuln/incident/export/audit":::data]
DATA_STATISTIC["Vulnerability & Incident Statistics\nAggregation, history, export":::data]
DATA_ISSUE_LINK["Issue/Incident Links Findings/Exports/Incidents":::data]

DATA_VULN -.-> VULN_CORE
DATA_VULN -.-> VULN_EXPORTS
DATA_VULN -.-> INCIDENT_MODELS
DATA_PROJECT -.-> VULN_CORE
DATA_PROJECT -.-> VULN_EXPORTS
DATA_PROJECT -.-> INCIDENT_MODELS
DATA_PROJECT -.-> VULN_STATS
DATA_PROJECT -.-> INCIDENT_ALERT_LINK
DATA_FINDING -.-> FINDINGS_SCANNERS
DATA_FINDING -.-> VULN_EXPORTS
DATA_FINDING -.-> AUDIT_EVENT_DATA
DATA_FINDING -.-> INCIDENT_MODELS
DATA_STATISTIC -.-> VULN_STATS
DATA_STATISTIC -.-> AUDIT_EVENT_DATA
DATA_STATISTIC -.-> INCIDENT_ESCALATION
DATA_ISSUE_LINK -.-> VULN_CORE
DATA_ISSUE_LINK -.-> VULN_EXPORTS
DATA_ISSUE_LINK -.-> INCIDENT_ALERT_LINK
DATA_ISSUE_LINK -.-> AUDIT_EVENT_DATA

class DATA_VULN core
class DATA_PROJECT,DATA_FINDING,DATA_STATISTIC,DATA_ISSUE_LINK data

%% ------------- STRATEGIC PATTERNS/ABSTRACTIONS ------------ %%
EVENT_SOURCING["Event Sourcing/Streaming for Bulk Ops\nworkers, state changes, historical audit":::util]
RBAC["RBAC & Policy Enforcement\nacross Vulnerability & Incident models":::util]
ASYNCHRONOUS["Async/Background Processing\nworkers, ETL tasks, SLA, escalation":::init]
AGGREGATION["Aggregation Patterns\nstatistics, analytics, comparison":::util]
PRESENTATION["Unified Presentation/Serializers\nacross findings, incidents & exports":::util]

AGGREGATION -.-> DATA_STATISTIC
AGGREGATION -.-> VULN_STATS
AGGREGATION -.-> INCIDENT_ESCALATION
PRESENTATION -.-> VULN_PRESENTATION
PRESENTATION -.-> SEC_GRAPHQL_REPORT
PRESENTATION -.-> VULN_SERIALIZERS
PRESENTATION -.-> AUDIT_EVENT_DATA
ASYNCHRONOUS -.-> EXPORT_WORKERS
ASYNCHRONOUS -.-> INCIDENT_WORKERS
ASYNCHRONOUS -.-> EVENT_WORKERS
EVENT_SOURCING -.-> EVENT_WORKERS
EVENT_SOURCING -.-> INCIDENT_WORKERS
EVENT_SOURCING -.-> AUDIT_EVENT_DATA
RBAC -.-> VULN_POLICIES
RBAC -.-> INCIDENT_POLICIES
RBAC -.-> EXPORT_API


class AGGREGATION,PRESENTATION,EVENT_SOURCING,RBAC util
class ASYNCHRONOUS init

%% --- HIGH-LEVEL DATA FLOW CONCEPTUAL --- %%
VULN_CORE -- "vuln state changes, findings created" --> FINDINGS_SCANNERS
FINDINGS_SCANNERS -- "findings ingested/exported" --> VULN_EXPORTS
VULN_EXPORTS -- "export data feeds" --> VULN_STATS
VULN_STATS -- "feeds analytics to" --> INCIDENT_ESCALATION
INCIDENT_MODELS -- "audit event triggered" --> AUDIT_EVENT_DATA
INCIDENT_MODELS -- "issue/incident status updates vuln" --> VULN_CORE
VULN_EXPORTS -- "exported for compliance & reporting via" --> EXPORT_API
VULN_EXPORTS -- "bulk/batch updates" --> INCIDENT_WORKERS

%% =============== SUBDOMAIN PURPOSES EXPLICIT =================== %%
FSD["Findings & Scanners\n> Asset/attack surface analysis, ingest findings, attach evidence, integrate scan results":::core]
VRED["Vulnerability Exports & Reports\n> Creating/composing exports, batch ops, advanced analytics, compliance data supply":::core]
IAED["Incident & Audit Events\n> Incident response, timeline, escalation, audit logging, oncall workflow":::core]

FSD -.-|feeds data| VRED
VRED -.-|triggers action| IAED
IAED -.-|updates state| FSD

FSD -.-> DATA_FINDING
VRED -.-> DATA_STATISTIC
IAED -.-> DATA_PROJECT

class FSD,VRED,IAED core

end

%% ========== SPACING ========== %%
%% Dummy for vertical spacing & visual separation
style root fill:#F8F8F8,stroke:#CBAACB,stroke-width:0,rx:20,ry:20
```