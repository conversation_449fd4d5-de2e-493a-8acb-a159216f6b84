```mermaid
flowchart TD
  %% DOMAIN LAYER: Infrastructure, Performance & Operations -> Cluster & Cloud Integration
  subgraph Domain["Infrastructure, Performance & Operations<br>Cluster & Cloud Integration"]
    direction TB
    style Domain fill:#F8F8F8,stroke:#37BFF5,stroke-width:3,cornerRadius:22

    %% CLUSTER MANAGEMENT SUBDOMAIN
    subgraph ClusterDomain["Kubernetes & Cluster Management<br>Subdomain"]
      direction TB
      style ClusterDomain fill:#F8F8F8,stroke:#4CB6E7,stroke-width:2,cornerRadius:18

      %% Abstracted core models
      ClusterCore["Cluster Domain Model & Policy":::core]
      ClusterProject["Project/Group/Instance Clusterables":::core]
      ClusterProvider["Providers: AWS / GCP":::core]
      KubeNamespace["KubernetesNamespace":::data]
      AgentToken["Agent Token":::data]
      ManagedResource["Managed Resource":::data]
      ActivityEvent["Activity Event":::data]
      ClusterPolicy["Cluster Policy":::utility]
      ClusterServiceLayer["Cluster & Kubernetes<br>Service Layer":::utility]
      KubeResourceLib["Kubernetes Resource Abstractions":::utility]
      ClusterController["Cluster API Controllers":::core]
      ClusterWorker["Cluster Lifecycle Workers":::init]
      ClusterFinder["Finders & Helpers":::utility]
      KubeLogger["Kubernetes Logger":::utility]
      ClusterEntity["Cluster Entity<br>Serializer":::utility]
      ClusterDeploymentEntity["Deployment Entity<br>Serializer":::data]
      ClusterPolicyAppStatus["ApplicationStatus/Version Concern":::utility]
    end

    %% AGENT & CONFIGURATION SUBDOMAIN
    subgraph AgentDomain["Agents & Configurations<br>Subdomain"]
      direction TB
      style AgentDomain fill:#F8F8F8,stroke:#80D0D7,stroke-width:2,cornerRadius:18

      AgentCore["Agent<br>Main Cluster Agent":::core]
      AgentHierarchy["Clusters Hierarchy":::core]
      AgentTokenFinder["Agent Token Finders":::utility]
      AgentAuthorizations["Agent Authorizations":::utility]
      AgentURLConfig["Agent URL Configurations":::data]
      AgentConfigPolicy["Agent Config Policy":::utility]
      AgentCreateService["Agent Creation & Audit Service":::utility]
      AgentTokenService["Token Management Service":::utility]
      AgentWorker["Agent Operations & Event Workers":::init]
      AgentDashboard["Agent Dashboard Controller":::init]
      AgentCIConfigScope["CI Config Scopes Concerns":::utility]
      RemoteDevWorkspace["Remote Dev: Workspace Configs/Policies":::data]
      AgentGraphQLAPI["Agent & URL Config GraphQL Integration":::utility]
      AgentQAUtil["QA/Orchestration Utilities":::utility]
    end

    %% CROSS-CUTTING DOMAIN STRUCTURES & INTERFACES
    subgraph CrossCutting["Cross-Subdomain Domain Structures"]
      direction TB
      style CrossCutting fill:#F8F8F8,stroke:#66CC99,stroke-width:2,cornerRadius:18

      ClustersData["Shared Data Structures:<br>Cluster, Agent, Token,<br>ManagedResource, Namespace":::data]
      ClusterToAgent["Cluster-Agent Logical Link":::core]
      PolicyAbstraction["Access & Authorization Policies":::core]
      RemoteDevAbstraction["Remote Development Integration":::utility]
      CloudIntegrationAbstraction["Cloud Providers/Connectors AWS/GCP":::core]
      KubeAbstractions["Kubernetes Resource Wrappers":::utility]
    end
  end

  %% COLOR/SHAPE DEFINITIONS
  classDef core fill:#D4F1F9,stroke:#88C7E8,stroke-width:2,color:#2B4558,rx:10,ry:10
  classDef data fill:#E0F8E0,stroke:#78D88E,stroke-width:2,color:#234828,rx:10,ry:10
  classDef utility fill:#FFF8DC,stroke:#E8C989,stroke-width:2,color:#4F3E07,rx:10,ry:10
  classDef error fill:#FFE4E1,stroke:#F39797,stroke-width:2,color:#600F0F,rx:10,ry:10
  classDef init fill:#E6E6FA,stroke:#BDB7E6,stroke-width:2,color:#534E65,rx:10,ry:10

  %% RELATIONSHIPS BETWEEN DOMAINS

  %% Cluster Entity/Model Relationships
  ClusterProject -- isClusterable --> ClusterCore
  ClusterCore -- usesProvider --> ClusterProvider
  ClusterCore -- hasNamespace --> KubeNamespace
  ClusterCore -- policyControlledBy --> ClusterPolicy
  ClusterCore -- controlledThrough --> ClusterController
  ClusterCore -- usesServicesVia --> ClusterServiceLayer
  ClusterCore -- permissionsCheckedBy --> PolicyAbstraction

  %% Agent Subdomain Relationships
  AgentCore -- managesTokensVia --> AgentTokenFinder
  AgentCore -- participatesInHierarchy --> AgentHierarchy
  AgentCore -- accessesURLConfig --> AgentURLConfig
  AgentCore -- governedBy --> AgentConfigPolicy
  AgentCore -- authorizationManagedBy --> AgentAuthorizations
  AgentCore -- policiesAppliedBy --> PolicyAbstraction
  AgentCore -- handlesCIConfigScopes --> AgentCIConfigScope
  AgentCore -- createdVia --> AgentCreateService
  AgentCore -- tokensManagedBy --> AgentTokenService
  AgentCore -- operatedBy --> AgentWorker
  AgentCore -- isRepresentedIn --> AgentDashboard
  AgentCore -- lifecycleTrackedBy --> ActivityEvent

  %% Data Structures & Domain-Spanning Connections
  ClusterCore -- sharesDataWith --> AgentCore
  ClustersData -- spans --> ClusterCore
  ClustersData -- spans --> AgentCore
  ManagedResource -- isManagedBy --> AgentCore
  KubeNamespace -- isOwnedBy --> ClusterCore
  AgentToken -- securedBy --> AgentCore

  %% Agent & Cluster Collaborations
  ClusterCore -- authorizes --> AgentCore
  ClusterCore -- grantsAccessTo --> AgentAuthorizations
  ClusterCore -- integratesWith --> KubeAbstractions
  ClusterCore -- usesCloudIntegration --> CloudIntegrationAbstraction
  AgentCore -- isClusterAgentFor --> ClusterCore
  AgentCore -- receivesEventsFrom --> ClusterWorker
  AgentCore -- participatesInRemoteDev --> RemoteDevAbstraction

  %% Abstractions, Patterns & Integration Points
  ClusterServiceLayer -- orchestrates --> KubeAbstractions
  KubeAbstractions -- manages --> KubeNamespace
  KubeAbstractions -- dataBoundTo --> ManagedResource
  AgentURLConfig -- exposedVia --> AgentGraphQLAPI
  AgentDashboard -- authorizationVia --> PolicyAbstraction
  AgentDashboard -- presents --> AgentCore
  ClusterController -- presents --> ClusterCore
  RemoteDevWorkspace -- usesPolicy --> RemoteDevAbstraction
  RemoteDevWorkspace -- boundTo --> AgentCore

  %% Cross-Subdomain Data Flow
  AgentWorker -- triggers --> ManagedResource
  AgentWorker -- triggers --> ActivityEvent
  ClusterWorker -- triggers --> ClusterCore

  %% Cloud & Remote Development Integration
  ClusterProvider -- providesConnectionFor --> CloudIntegrationAbstraction
  CloudIntegrationAbstraction -- enhances --> ClusterCore
  RemoteDevAbstraction -- coordinates --> RemoteDevWorkspace
  RemoteDevAbstraction -- configures --> AgentCore

  %% QA & Tooling Support
  ClusterCore -- testedVia --> AgentQAUtil
  AgentCore -- testedVia --> AgentQAUtil
  KubeAbstractions -- testedVia --> AgentQAUtil

  %% Kubernetes Patterns & Policies
  ClusterPolicyAppStatus -- statusReplicatedTo --> ClusterPolicy
  ClusterPolicyAppStatus -- combinesStatusWith --> KubeAbstractions

  %% Legend: Retain for color map context
  subgraph Legend["Legend"]
    direction TB
    style Legend fill:#F8F8F8,stroke:#E1E1E1,stroke-width:1
    LegendCore["Core domain components":::core]
    LegendData["Domain data structures":::data]
    LegendUtility["Supporting/utility components":::utility]
    LegendInit["Init/setup/worker components":::init]
    LegendError["Error handling":::error]
    LegendBorder["Logical group/subgraph" style "fill:#F8F8F8,stroke:#B2D4EE"]
  end
```