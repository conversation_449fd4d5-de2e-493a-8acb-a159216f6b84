```mermaid
flowchart TB
%% ================== COLOR & DEFINITIONS ===================
classDef core fill:#D4F1F9,stroke:#80c9e4,stroke-width:2,rx:12,shape:rounded-rectangle
classDef utility fill:#FFF8DC,stroke:#FFD580,stroke-width:2,rx:12,shape:rounded-rectangle
classDef data fill:#E0F8E0,stroke:#98d44e,stroke-width:2,rx:12,shape:rounded-rectangle
classDef error fill:#FFE4E1,stroke:#eeaaa9,stroke-width:2,rx:12,shape:rounded-rectangle
classDef setup fill:#E6E6FA,stroke:#c6beef,stroke-width:2,rx:12,shape:rounded-rectangle
classDef groupDomain fill:#F8F8F8,stroke:#D4F1F9,stroke-width:4,rx:24
classDef groupA fill:#F8F8F8,stroke:#D4F1F9,stroke-width:3,rx:18
classDef groupB fill:#F8F8F8,stroke:#E5D8AD,stroke-width:3,rx:18
classDef groupC fill:#F8F8F8,stroke:#B7E6ED,stroke-width:3,rx:18

%% =============== DOMAIN ROOT + LOGICAL FLOW ===============
subgraph SourceCodeDomain["Source Code, Repository & Collaboration"]
direction TB
class SourceCodeDomain groupDomain

DC["DOMAIN CONTEXT\nCollaborative development & management of code, repositories, and supporting artifacts"]:::core

%% --- DOMAIN-WIDE SHARED CONCEPTS ---
  subgraph sgCoreConcepts["Core Domain Concepts"]
    direction TB
    class sgCoreConcepts groupDomain

    Project["Project"]:::core
    Repository["Repository"]:::core
    Branch["Branch"]:::core
    Tag["Tag"]:::core
    Commit["Commit"]:::core
    MergeRequest["MergeRequest"]:::core
    Diff["Diff"]:::core
    User["User"]:::core
    FileBlob["File/Blob\ncore file object"]:::data
    UploadRef["Upload Reference\nassociation from domain objects to files"]:::data
  end

%% ====== SUBDOMAIN 1: REPOSITORY MANAGEMENT (Simplified) =======
  subgraph sgRepoMgmt["Repository Management"]
    direction TB
    class sgRepoMgmt groupA

    REPO_PURPOSE["Purpose:\nVersioned code & project structuring,\n collaboration via branches, tags, MRs"]:::core

    RepoModel["Repository Model & Services"]:::core
    BranchTag["Branch/Tag Management"]:::core
    ForkNetwork["Fork Network"]:::core
    MRSystem["Merge Request System"]:::core
    DiffSys["Diff & Review Engine"]:::core
    RepoProtection["Protection Policy & Security"]:::core
    RepoAPI["Repository APIs"]:::utility
    RepoError["Repo & MR Error Handling"]:::error

    RMData1["Domain Data Structures:\nCommitRange, DiffLineEntity, MRWidgetEntity,\nRepositoryLanguage, DiffStats"]:::data
  end

%% ====== SUBDOMAIN 2: SNIPPETS & FILES Simplified =======
  subgraph sgSnippets["Snippets & Files"]
    direction TB
    class sgSnippets groupB

    SF_PURPOSE["Purpose:\nCode artifacts, user-level files,\n\ndomain-level file operations"]:::core

    SnippetLogic["Snippet/File Logic"]:::core
    FileHierarchy["File Structure Abstraction"]:::data
    FileAccess["File Access Control"]:::core
    FileService["File Persistence Adapter Pattern"]:::utility
    SnippetAPI["Snippets & File APIs"]:::utility
    SFData1["Domain Data Structures:\nFile/Blob"]:::data
  end

%% ======= SUBDOMAIN 3: COLLABORATION & DISCUSSION Simplified =======
  subgraph sgCollab["Collaboration & Discussion"]
    direction TB
    class sgCollab groupC

    COL_PURPOSE["Purpose:\nUser collaboration, work tracking,\ndiscussions & notifications"]:::core

    TodoCore["Todos & Actions System"]:::core
    AwardEmoji["Award Emoji/Reactions"]:::core
    SystemNote["System Notes/Audit Events"]:::data
    NotifyHub["Notifications\nOrchestration"]:::setup
    UserMention["User Mentions"]:::core
    DiscussionAPI["Collaboration APIs"]:::utility
    CollabData1["Core Collaboration Data:\nTodos, AwardEmojis, SystemNotes"]:::data
  end

%% ===== DOMAIN-SCOPED PATTERNS / ABSTRACTIONS =====
subgraph sgPatterns["Cross-domain Abstractions & Patterns"]
direction TB
class sgPatterns groupDomain

UploadServicePattern["Upload Service Pattern"]:::core
PolicyAbstraction["Access Policy Abstraction"]:::core
PersistencePattern["Persistence Adapter Pattern"]:::utility
NotificationPattern["Notification Orchestration"]:::core
ReviewPattern["Code Review Pattern"]:::core
end


%% =============== DOMAIN FLOW/RELATIONSHIPS ===============

%% -- DOMAIN CONTEXT to SUBDOMAINS --
DC --> sgRepoMgmt
DC --> sgSnippets
DC --> sgCollab

%% -- SHARED CORE CONCEPTS --
sgCoreConcepts --> sgRepoMgmt
sgCoreConcepts --> sgSnippets
sgCoreConcepts --> sgCollab

%% -- Core inter-entity topology --
Project --"Owns"--> Repository
Repository --"Organizes"--> Branch
Repository --"Categorizes"--> Tag
Branch --"Contains Commits"--> Commit
Repository --"Tracks"--> MergeRequest
MergeRequest --"Describes"--> Diff
Repository --"Files stored as"--> FileBlob
Repository --"Linked Users"--> User
Project --"Associated Users"--> User

%% -- Repository Management subdomain logic --
REPO_PURPOSE --> RepoModel
RepoModel --"Has"--> BranchTag
BranchTag --"Includes"--> ForkNetwork
RepoModel --"Feeds"--> MRSystem
MRSystem --"Reviewed via"--> DiffSys
DiffSys --"Anchors"--> RepoProtection
RepoModel --"APIs exposed via"--> RepoAPI
RepoModel --"Error protected by"--> RepoError
RepoModel --"Contains"--> RMData1

%% -- Snippets & Files subdomain logic --
SF_PURPOSE --> SnippetLogic
SnippetLogic --"Structures in"--> FileHierarchy
SnippetLogic --"Secured by"--> FileAccess
SnippetLogic --"Persists Files via"--> FileService
SnippetLogic --"API endpoint"--> SnippetAPI
SnippetLogic --"Artifacts in"--> SFData1

%% -- Collaboration & Discussion subdomain logic --
COL_PURPOSE --> TodoCore
TodoCore --"User Actions"--> AwardEmoji
TodoCore --"Audit Events"--> SystemNote
TodoCore --"Notifications Managed by"--> NotifyHub
NotifyHub --"Mentions Link"--> UserMention
TodoCore --"APIs via"--> DiscussionAPI
TodoCore --"Tracks"--> CollabData1

%% -- Collaboration of Data Across Domain --
%% -- File references and upload data relationships --
FileBlob --"Referenced by"--> UploadRef
UploadRef --"Becomes artifact for"--> SnippetLogic
UploadRef --"Attaches to"--> Project
UploadRef --"Linked from"--> MergeRequest
UploadRef --"Linked from"--> DiscussionAPI
UploadRef --"Awarded on"--> AwardEmoji
UploadRef --"Notified by"--> NotifyHub

%% -- Collaboration bridges between subdomains --
%% MergeRequest triggers review/collab
MRSystem --"Collaborations via"--> NotifyHub
MRSystem --"Todo/Action generated for"--> TodoCore
DiffSys --"Discussion threads in"--> SystemNote
RepoModel --"Artifacts managed for"--> SnippetLogic
FileBlob --"Shared reference for"--> CollabData1
TodoCore --"Actionable comments on"--> MergeRequest
AwardEmoji --"User reactions on"--> MergeRequest
SystemNote --"History of"--> RepoModel

%% -- API SURFACES connect domain boundaries
RepoAPI --"Exposes"--> BranchTag
RepoAPI --"Publishes"--> MRSystem
SnippetAPI --"Exposes"--> SnippetLogic
DiscussionAPI --"Surfaces collab data"--> TodoCore

%% -- Data structure sharing --
RMData1 --"MR Widget serializes"--> MRSystem
SFData1 --"Blob structure shared with"--> FileBlob
CollabData1 --"Todos/AwardEmojis referenced by"--> MRSystem

%% ---------------- CROSS-DOMAIN PATTERNS -----------------
%% UploadServicePattern, PolicyAbstraction, etc.
UploadServicePattern --"Pervasive in"--> SnippetLogic
UploadServicePattern --"Used in"--> RepoModel
UploadServicePattern --"Used in"--> TodoCore
PolicyAbstraction --"Enforces in"--> FileAccess
PolicyAbstraction --"Enforces in"--> RepoProtection
PersistencePattern --"Persisting files for"--> FileService
PersistencePattern --"Underlying for"--> RepoModel
NotificationPattern --"Orients notifications for"--> NotifyHub
NotificationPattern --"Linked in"--> MRSystem
ReviewPattern --"Enables"--> DiffSys
ReviewPattern --"Process flow for"--> MRSystem

%% ---- KEY COLLABORATION FLOWS / ARROWS VERTICAL -----

% 1. Creation & Collaboration
User --"Creates"--> Project
User --"Forks, Branches, MRs"--> RepoModel
User --"Uploads files to"--> SnippetLogic
User --"Adds reactions on"--> AwardEmoji

% 2. Review & Merge
MRSystem --"Reviewed via"--> DiffSys
DiffSys --"User notified via"--> NotifyHub

% 3. Discussion & Notification
TodoCore --"Mention in draft/merge event"--> MergeRequest
NotifyHub --"Sends notification to"--> User
UserMention --"Links users in"--> MergeRequest

% 4. File/Artifact Flow
SnippetLogic --"Links uploaded file"--> FileBlob
MRSystem --"Annotates/diffs files"--> FileBlob
AwardEmoji --"Attached to"--> FileBlob
SystemNote --"Audit refs for file/merge"--> FileBlob

% 5. Snippet/Attachment API
SnippetAPI --"Expose/download files"--> FileService
DiscussionAPI --"User actionable endpoint"--> TodoCore

%% ---- BACKGROUND LOGIC (batchments, workers abstracted away) ----

% Style subgraph backgrounds for clarity
style SourceCodeDomain fill:#F8F8F8,stroke:#D4F1F9,stroke-width:4,rx:24
style sgCoreConcepts fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rx:16
style sgRepoMgmt fill:#F8F8F8,stroke:#D4F1F9,stroke-width:3,rx:18
style sgSnippets fill:#F8F8F8,stroke:#E5D8AD,stroke-width:3,rx:18
style sgCollab fill:#F8F8F8,stroke:#B7E6ED,stroke-width:3,rx:18
style sgPatterns fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2,rx:16

%% Apply class to all major nodes for consistent pastel coloring
class DC,Project,Repository,Branch,Tag,Commit,MergeRequest,Diff,User,FileBlob,UploadRef core
class REPO_PURPOSE,RepoModel,BranchTag,ForkNetwork,MRSystem,DiffSys,RepoProtection,RepoAPI RepoError core
class SF_PURPOSE,SnippetLogic,FileHierarchy,FileAccess,FileService,SnippetAPI core
class COL_PURPOSE,TodoCore,AwardEmoji,SystemNote,NotifyHub,UserMention,DiscussionAPI core
class RMData1,SFData1,CollabData1 data
class UploadServicePattern,PolicyAbstraction,PersistencePattern,NotificationPattern,ReviewPattern core
```