```mermaid
flowchart TB
  %% ========= COLOR & SHAPE DEFINITIONS ===========
  classDef core fill:#D4F1F9,stroke:#90badb,stroke-width:2px,color:#222,stroke-dasharray:0,rx:8,ry:8
  classDef support fill:#FFF8DC,stroke:#f0db99,stroke-width:2px,color:#222,stroke-dasharray:0,rx:8,ry:8
  classDef data fill:#E0F8E0,stroke:#7ecfa1,stroke-width:2px,color:#222,stroke-dasharray:0,rx:8,ry:8
  classDef error fill:#FFE4E1,stroke:#fac0be,stroke-width:2px,color:#222,stroke-dasharray:0,rx:8,ry:8
  classDef init fill:#E6E6FA,stroke:#b6a9d3,stroke-width:2px,color:#222,stroke-dasharray:0,rx:8,ry:8
  classDef group fill:#F8F8F8,stroke:#90badb,stroke-width:2px,rx:18,ry:18

  %% ========= TOP-LEVEL DOMAIN ==========
  subgraph domainGroup["Integrations & Extensibility - Import/Export Integrations" ]
    direction TB

    %% ---------- DOMAIN ENTRYPOINTS ----------
    subgraph sgEntrypoint["Import Entrypoints"]
      direction TB
      controller["Controllers & API Endpoints"]:::core
      serviceCore["Import Service Layer"]:::core
    end

    %% ---------- SHARED DOMAIN DATA STRUCTURES & CONCEPTS ----------
    subgraph sgDomainData["Domain Data Structures"]
      direction TB
      dsIssues["Issue Structures"]:::data
      dsPullRequest["Pull Request Structures"]:::data
      dsNotes["Notes/Comments Structures"]:::data
      dsUsers["User Identity Map"]:::data
      dsLabels["Labels/Tags"]:::data
      dsLFS["LFS Objects"]:::data
      dsMeta["Meta Data and Mappings"]:::data
    end

    %% ---------- PATTERNS, ABSTRACTIONS & SHARED SUPPORT ----------
    subgraph sgPatterns["Common Patterns & Abstractions"]
      direction TB
      patternStages["Staged Import Pipeline"]:::support
      patternWorkers["Specialized Import Workers"]:::support
      patternSerializers["Data Serialization/Representation"]:::support
      patternErrorHandling["Import Error Handling"]:::error
      patternStat["Import Object Counting/Stats"]:::support
      patternAdvance["Stage Advancement Logic"]:::support
      patternMixins["Shared Mixins/Concerns"]:::support
    end

    %% ---------- SUBDOMAIN ABSTRACTIONS ----------
    subgraph sgSubDomains["Import Source Subdomains"]
      direction TB

      %% ---- GitHub Importers ABSTRACTION ---
      subgraph sgGithub["GitHub Import"]
        direction TB
        githubController["GitHub Import Controller"]:::core
        githubStages["GitHub Import Stages"]:::core
        githubImporters["GitHub Entity Importers"]:::core
        githubUtils["GitHub Utilities"]:::support

        githubController -- Initiates --> githubStages
        githubStages -- Delegates-to --> githubImporters
        githubImporters -- Uses --> githubUtils
        githubImporters -- Handles-data --> dsIssues
        githubImporters -- Handles-data --> dsPullRequest
        githubImporters -- Handles-data --> dsNotes
        githubImporters -- Handles-data --> dsUsers
        githubImporters -- Handles-data --> dsLabels
        githubImporters -- Handles-data --> dsLFS
        githubImporters -- Aggregates-Stats --> patternStat
        githubImporters -- Serializes --> patternSerializers
        githubImporters -. Tracks-Errors .-> patternErrorHandling
        githubStages -- Advances-via --> patternAdvance
        githubStages -- Uses-mixins --> patternMixins
      end

      %% ---- Bitbucket Importers ABSTRACTION ---
      subgraph sgBitbucket["Bitbucket Import"]
        direction TB
        bitbucketController["Bitbucket Import Controller"]:::core
        bitbucketStages["Bitbucket Import Stages"]:::core
        bitbucketImporters["Bitbucket Entity Importers"]:::core
        bitbucketUtils["Bitbucket Utilities"]:::support

        bitbucketController -- Initiates --> bitbucketStages
        bitbucketStages -- Delegates-to --> bitbucketImporters
        bitbucketImporters -- Uses --> bitbucketUtils
        bitbucketImporters -- Handles-data --> dsIssues
        bitbucketImporters -- Handles-data --> dsPullRequest
        bitbucketImporters -- Handles-data --> dsNotes
        bitbucketImporters -- Handles-data --> dsUsers
        bitbucketImporters -- Handles-data --> dsLFS
        bitbucketImporters -- Aggregates-Stats --> patternStat
        bitbucketImporters -- Serializes --> patternSerializers
        bitbucketImporters -. Tracks-Errors .-> patternErrorHandling
        bitbucketStages -- Advances-via --> patternAdvance
        bitbucketStages -- Uses-mixins --> patternMixins
      end

      %% ---- Bitbucket Server Importers ABSTRACTION ---
      subgraph sgBitbucketServer["Bitbucket Server Import"]
        direction TB
        bbServerController["Bitbucket Server Controller"]:::core
        bbServerStages["Bitbucket Server Stages"]:::core
        bbServerImporters["BBServer Entity Importers"]:::core
        bbServerUtils["BBServer Utilities"]:::support

        bbServerController -- Initiates --> bbServerStages
        bbServerStages -- Delegates-to --> bbServerImporters
        bbServerImporters -- Uses --> bbServerUtils
        bbServerImporters -- Handles-data --> dsIssues
        bbServerImporters -- Handles-data --> dsPullRequest
        bbServerImporters -- Handles-data --> dsNotes
        bbServerImporters -- Handles-data --> dsUsers
        bbServerImporters -- Handles-data --> dsLFS
        bbServerImporters -- Aggregates-Stats --> patternStat
        bbServerImporters -- Serializes --> patternSerializers
        bbServerImporters -. Tracks-Errors .-> patternErrorHandling
        bbServerStages -- Advances-via --> patternAdvance
        bbServerStages -- Uses-mixins --> patternMixins
      end

      %% ---- Jira Importers ABSTRACTION ---
      subgraph sgJira["Jira Import"]
        direction TB
        jiraController["Jira Import Controller"]:::core
        jiraStages["Jira Import Stages"]:::core
        jiraImporters["Jira Entity Importers"]:::core
        jiraUtils["Jira Utilities"]:::support

        jiraController -- Initiates --> jiraStages
        jiraStages -- Delegates-to --> jiraImporters
        jiraImporters -- Uses --> jiraUtils
        jiraImporters -- Handles-data --> dsIssues
        jiraImporters -- Handles-data --> dsNotes
        jiraImporters -- Handles-data --> dsLabels
        jiraImporters -- Handles-data --> dsUsers
        jiraImporters -- Handles-data --> dsMeta
        jiraImporters -- Serializes --> patternSerializers
        jiraImporters -- Aggregates-Stats --> patternStat
        jiraImporters -. Tracks-Errors .-> patternErrorHandling
        jiraStages -- Advances-via --> patternAdvance
        jiraStages -- Uses-mixins --> patternMixins
      end
    end

    %% ---------- COLLABORATION & FLOW BETWEEN SUBDOMAINS AND DOMAIN CONCEPTS ----------
    controller -- Schedules Import Jobs --> serviceCore
    serviceCore -- Dispatches Jobs --> sgSubDomains

    sgSubDomains -- Populates --> sgDomainData
    sgDomainData -- Used-by --> sgSubDomains

    sgSubDomains -- Implements-with --> sgPatterns
    sgPatterns -- Supports --> sgSubDomains

    sgPatterns -- Relies-on-data --> sgDomainData

    %% ---------- INIT & SUPPORT ----------
    subgraph sgDomainInit["Initialization & Bulk Import"]
      direction TB
      bulkAttrWorker["Bulk User Attribute Import"]:::init
      configPurgeWorker["Import Config Purge"]:::init
      placeholderReassign["Placeholder Reassignment"]:::init

      placeholderReassign -- Uses --> bulkAttrWorker
      bulkAttrWorker -- Schedules --> configPurgeWorker
    end
    sgDomainInit -- Assists --> serviceCore

  end %% End domainGroup

  %% ========= GROUP STYLES =========
  class domainGroup group
  class sgEntrypoint group
  class sgDomainData group
  class sgPatterns group
  class sgSubDomains group
  class sgDomainInit group

  %% ========= KEY ARROW STYLES ==========
  linkStyle 44,45,46,47 stroke:#b6a9d3,stroke-width:2px
  %% Core subdomain-to-pattern dependencies
  linkStyle 28,37,46,64 stroke:#90badb,stroke-width:2px
  %% Error handling edge
  class patternErrorHandling error
```