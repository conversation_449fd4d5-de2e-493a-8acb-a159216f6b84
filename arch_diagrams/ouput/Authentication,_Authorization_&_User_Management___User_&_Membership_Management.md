```mermaid
flowchart TB

%% High-level Combined Diagram for
%% Authentication, Authorization & User Management > User & Membership Management

%%--------------------------------------
%% DOMAIN CONTEXT SUBGRAPH
subgraph D_AAUMM["Authentication, Authorization & User Management" ]
  direction TB
  style D_AAUMM fill:#F8F8F8,stroke:#A5D8F3,stroke-width:3
  %% DOMAIN ENTRYPOINT NODE
  CoreConcepts["User & Membership Management\nCore Domain Concepts":::core]
end

%%--------------------------------------
%% SUBDOMAIN 1: User Preferences & Notifications Group
subgraph S_UserPrefsNotifs["User Preferences & Notifications"]
  direction TB
  style S_UserPrefsNotifs fill:#F8F8F8,stroke:#A5D8F3,stroke-width:2

  Prefs["User Preferences & Profile\nPreferences/Settings Model,\nProfile Data, Access Policies":::core]
  Notifs["Notifications & Security\nNotification Settings,\nKey Expiry, Rate Limiting, Security Events":::core]
  Callouts["Callouts & Dismissals\nBanners, Group Announcements":::core]
  Status["User Status\nPresence & Availability, EE/Custom attrs":::core]
  
  PreferencesDS["User Preference DS":::datastruct]
  UserAttrDS["User Custom Attr DS":::datastruct]
  StatusDS["User Status DS":::datastruct]
  CalloutDS["User Callout DS":::datastruct]

  UtilityPrefs["Preference Service Layer\nMutation, Update, Serialization":::utility]
  UtilityCallouts["Callout Service Layer\nDismissal, Group Callout, TodoCache":::utility]
  UtilityNotifs["Notification Services\nNotification Controller, Rate Limiter":::utility]

  %% Key Relationships
  Prefs -- "stores & updates" --> PreferencesDS
  UtilityPrefs -- "updates/serializes" --> PreferencesDS
  Prefs -- "policy/authorization" --> UtilityPrefs
  UtilityPrefs -- "applies changes via" --> Prefs

  Status -- "stores & updates" --> StatusDS
  Status -- "extensions for EE" --> UserAttrDS
  Status -- "supports profile & presence features" --> Prefs

  Callouts -- "dismissal & banner mgmt" --> CalloutDS
  UtilityCallouts -- "service for callouts dismiss, update, cache" --> CalloutDS

  Prefs -- "loads user status" --> Status
  Notifs -- "sends notifications based on preferences" --> Prefs
  Notifs -- "security-related notifications sent with rate limiting via" --> UtilityNotifs

  UtilityNotifs -- "applies rate limits" --> Notifs

  Callouts -- "user can dismiss/group banners" --> Prefs

  %% Data Structures internal flow
  Status -- "custom attr extends" --> UserAttrDS

end

%%--------------------------------------
%% SUBDOMAIN 2: Invitations & Onboarding Group
subgraph S_InvitesOnboarding["Invitations & Onboarding"]
  direction TB
  style S_InvitesOnboarding fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2

  RegOnboard["Registration & Onboarding Flow\nUser Signup, Terms, Verification, Enterprise Flows":::core]
  InvitationProc["Invitation Flow\nOnboarded by Invite, Import, Reassignment":::core]
  EmailVerify["Email Verification & Token Services\nConfirmation, Abuse Prevention, Token Gen":::core]
  NotificationProc["Onboard & Invitation Notification\nSystem Events, New User & Email Notifs":::core]
  UserDetailsExt["User Data & Details DS\nExtended User/EE Details,\nNamespace Email, Support PIN":::datastruct]
  VerificationProc["Identity Verification & Helpers\nIdentity Checks, Error Handling, Helpers":::utility]
  RegistrationHelpers["Registration & UI Helpers\nForm UX, Automation, Specs":::utility]
  BroadcastsNode["Broadcast Messages & Dismissal\nNotifications Related to Onboarding State":::core]
  ErrorNode["Domain Errors\nVerification/Email/Invite Validation":::error]

  %% Relationships
  RegOnboard -- "orchestrates onboarding" --> UserDetailsExt
  InvitationProc -- "invites users, triggers onboarding" --> UserDetailsExt
  InvitationProc -- "invoke import/reassignment" --> RegOnboard
  EmailVerify -- "validates & verifies" --> UserDetailsExt
  RegOnboard -- "delegates email/identity check to" --> EmailVerify
  EmailVerify -- "calls error handling" --> ErrorNode

  NotificationProc -- "notifies new users" --> RegOnboard
  NotificationProc -- "email confirmation after onboarding" --> EmailVerify
  NotificationProc -- "system hooks/logging" --> RegOnboard

  VerificationProc -- "assists identity/CI verification" --> RegOnboard
  VerificationProc -- "helpers for controllers" --> EmailVerify
  VerificationProc -- "error surfaced to" --> ErrorNode

  RegistrationHelpers -- "Renders/assists onboarding UI" --> RegOnboard
  RegistrationHelpers -- "Reads EE/onboarding user details" --> UserDetailsExt

  BroadcastsNode -- "dismissal updates onboarding state" --> UserDetailsExt

end

%%--------------------------------------
%% DATA STRUCTURE HUB shared/central DS abstractions
subgraph SharedDataStructures["Domain Data Structures Key Abstractions"]
  direction TB
  style SharedDataStructures fill:#F8F8F8,stroke:#61C48E,stroke-width:2
  UserDS["User\nCore Entity":::datastruct]
  MembershipDS["Membership\nUser Memberships, Groups":::datastruct]
end

%%--------------------------------------
%% KEY ABSTRACTIONS / PATTERNS (Cross-domain)
subgraph KeyAbstractions["Key Domain Patterns & Abstractions"]
  direction TB
  style KeyAbstractions fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2
  ServiceLayer["Service Layer Pattern":::init]
  PolicyLayer["Policy/Authorization Layer":::init]
  DataAccess["Data Structure Abstractions":::init]
  NotificationPattern["Notification/Observer Pattern":::init]
  ErrorHandling["Error/Abuse Prevention & Handling":::error]
end

%%--------------------------------------
%% HIGH-LEVEL DOMAIN FLOW / RELATIONSHIPS

CoreConcepts --> S_UserPrefsNotifs
CoreConcepts --> S_InvitesOnboarding
CoreConcepts --> SharedDataStructures

%% User Entity Bridges Shared Data Structures
UserDS -- "core entity for all flows" --> S_UserPrefsNotifs
UserDS -- "central to invite/onboard flows" --> S_InvitesOnboarding

MembershipDS -- "links users to groups, callouts, onboarding" --> S_UserPrefsNotifs
MembershipDS -- "used for invitations/membership confirmation" --> S_InvitesOnboarding

%% Inter-Subdomain Relationships

%% From Invite/Onboarding to Preferences:
S_InvitesOnboarding -- "after onboarding, initialize preferences & notifications for user" --> S_UserPrefsNotifs
%% Onboarding affects initial status and callouts:
S_InvitesOnboarding -- "sets initial status, notification state, broadcasts dismissal for onboarding" --> S_UserPrefsNotifs

%% Preferences impact onboarding Email Notifications and Verification:
S_UserPrefsNotifs -- "preference (eg, notification/email settings) affect onboarding flow" --> S_InvitesOnboarding

%% Shared Data flows:
S_InvitesOnboarding -- "populates/reads" --> SharedDataStructures
S_UserPrefsNotifs -- "updates/reads" --> SharedDataStructures

%% Core Patterns Used by Both Subdomains:
S_UserPrefsNotifs -- "service pattern used in domain logic" --> KeyAbstractions
S_InvitesOnboarding -- "service/access/error/policy patterns across domain" --> KeyAbstractions

%% Key Cross-domain Data & Patterns
KeyAbstractions -- "patterns shape flows, error & authorization" --> SharedDataStructures
SharedDataStructures --> KeyAbstractions

%% Error/Abuse Handling is shared:
S_InvitesOnboarding -- "surfaced in error node" --> ErrorHandling
S_UserPrefsNotifs -- "surfaced in error node" --> ErrorHandling

%% Notification, Policy, and Service Patterns
S_UserPrefsNotifs -- "observer, notification pattern for user events" --> NotificationPattern
S_InvitesOnboarding -- "system notifies users/invited/verified/EE onboarding" --> NotificationPattern
S_UserPrefsNotifs -- "policy/enforcement in preferences and callouts" --> PolicyLayer
S_InvitesOnboarding -- "registration policies, onboarding authorization" --> PolicyLayer
S_UserPrefsNotifs -- "service-oriented operations for status, preferences, callouts" --> ServiceLayer
S_InvitesOnboarding -- "invitation/onboarding/service layer" --> ServiceLayer
S_UserPrefsNotifs -- "rich DS for preferences/status/callout" --> DataAccess
S_InvitesOnboarding -- "user DS, details/namespace email/support pin" --> DataAccess

%%--------------------------------------
%% CLASS DEFINITIONS
classDef core fill:#D4F1F9,stroke:#A5D8F3,stroke-width:2,color:#222,rx:8,ry:8
classDef datastruct fill:#E0F8E0,stroke:#61C48E,stroke-width:2,color:#222,rx:8,ry:8
classDef utility fill:#FFF8DC,stroke:#FFD700,stroke-width:2,color:#222,rx:8,ry:8
classDef error fill:#FFE4E1,stroke:#FFB3AB,stroke-width:2,color:#222,rx:8,ry:8
classDef init fill:#E6E6FA,stroke:#B4A1DE,stroke-width:2,color:#222,rx:8,ry:8

%%--------------------------------------
%% Subdomain Legend (for visual aid/structure only; not a legend node)
%% D_AAUMM = Overall Domain Context
%% S_UserPrefsNotifs = User Preferences & Notifications Subdomain
%% S_InvitesOnboarding = Invitations & Onboarding Subdomain
%% SharedDataStructures = Domain-wide Canonical Data Structures
%% KeyAbstractions = Essential Patterns Used Across Domain
```