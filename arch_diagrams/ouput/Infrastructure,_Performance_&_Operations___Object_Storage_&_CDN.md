```mermaid
flowchart TD
  %% ======================================================= 
  %% HIGH-LEVEL DOMAIN GROUPING
  %% ======================================================= 
  subgraph L1_ObjectStorageCDN["Infrastructure, Performance & Operations / Object Storage & CDN"]
    direction TB
    style L1_ObjectStorageCDN fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded=true

    %% ---------------------------
    %% SUBDOMAIN: Direct Uploads & File Management ABSTRACTED
    %% ---------------------------
    subgraph L2_DirectUploadsFileManagement["Direct Uploads & File Management"]
      direction TB
      style L2_DirectUploadsFileManagement fill:#F8F8F8,stroke:#A9DCF1,stroke-width:1.8px,rounded=true

      %% CORE SERVICE ABSTRACTION
      DUFM_DirectUploadManager[[Direct Upload Manager<br/>Coordinates file uploads to storage & CDN]]
      style DUFM_DirectUploadManager fill:#D4F1F9,stroke:#A9DCF1,stroke-width:1.7px,rounded=true

      %% DATA FLOW & DU TRACKING
      DUFM_PendingUploadsStore[[Pending Uploads Store<br/>Tracks in-progress direct uploads]]
      style DUFM_PendingUploadsStore fill:#E0F8E0,stroke:#77d2a6,stroke-width:1.5px,rounded=true
      DUFM_UploadedFile[[Direct Upload Instance<br/>Represents a single user/bot upload]]
      style DUFM_UploadedFile fill:#E0F8E0,stroke:#77d2a6,stroke-width:1.5px,rounded=true

      %% OBJECT STORAGE & CDN ABSTRACTIONS
      DUFM_ObjectStorageAPI[[Object Storage / CDN API<br/>Abstraction layer for S3/GCS/CDN]]
      style DUFM_ObjectStorageAPI fill:#D4F1F9,stroke:#A9DCF1,stroke-width:1.5px,rounded=true

      %% MIDDLEWARE SECURITY
      DUFM_UploadSecurity[[Upload Security Middleware<br/>Guards and validates uploads]]
      style DUFM_UploadSecurity fill:#FFF8DC,stroke:#ffe292,stroke-width:1.4px,rounded=true

      %% FILE UTILITIES
      DUFM_FileTools[[File Utility Functions<br/>Helpers for compression, URL, and Redis integration]]
      style DUFM_FileTools fill:#FFF8DC,stroke:#ffe292,stroke-width:1.4px,rounded=true

      %% CERTIFICATE MGMT & CDN DOMAIN DATA
      DUFM_CDNDomain[[CDN Domain Handler<br/>Handles domain+SSL for CDN uploads]]
      style DUFM_CDNDomain fill:#E0F8E0,stroke:#77d2a6,stroke-width:1.5px,rounded=true

      %% ERROR HANDLING Core
      DUFM_StorageCDNErrors[[Storage/CDN Errors]]
      style DUFM_StorageCDNErrors fill:#FFE4E1,stroke:#FFB6B9,stroke-width:1.2px,rounded=true
    end

    %% --------------------------
    %% DOMAIN DATA STRUCTURES (SHARED)
    %% --------------------------
    subgraph DS_SharedStructures["Shared Domain Data Structures"]
      direction TB
      style DS_SharedStructures fill:#F8F8F8,stroke:#77d2a6,stroke-width:1.6px,rounded=true

      DS_PendingUploads[[Pending Uploads Record<br/>Cross-subdomain Redis/meta store]]
      style DS_PendingUploads fill:#E0F8E0,stroke:#77d2a6,stroke-width:1.3px,rounded=true

      DS_CDNDomainObj[[CDN Domain Object<br/>Cert, Hostname, etc.]]
      style DS_CDNDomainObj fill:#E0F8E0,stroke:#77d2a6,stroke-width:1.3px,rounded=true

      DS_FileReference[[File Reference<br/>Upload IDs, paths, metadata]]
      style DS_FileReference fill:#E0F8E0,stroke:#77d2a6,stroke-width:1.3px,rounded=true
    end

    %% --------------------------
    %% CROSS-DOMAIN ABSTRACTIONS / UTILS
    %% --------------------------
    subgraph L1_InfraAbstractions["Infra Layer Abstractions"]
      direction TB
      style L1_InfraAbstractions fill:#F8F8F8,stroke:#ffe292,stroke-width:1.2px,rounded=true

      L1_StorageAdapter[[StorageAdapter<br/>Unified upload & storage interface]]
      style L1_StorageAdapter fill:#D4F1F9,stroke:#A9DCF1,stroke-width:1.2px,rounded=true

      L1_UploadMiddleware[[Upload Middleware<br/>Multi-part & security processing]]
      style L1_UploadMiddleware fill:#FFF8DC,stroke:#ffe292,stroke-width:1.2px,rounded=true

      L1_UtilityHelpers[[Utility Helpers<br/>Compression, asset proxies, Redis, URL building]]
      style L1_UtilityHelpers fill:#FFF8DC,stroke:#ffe292,stroke-width:1.2px,rounded=true
    end

    %% --------------------------
    %% INITIALIZATION & EXTENSIONS
    %% --------------------------
    subgraph L1_Initialization["Initialization & Extensibility"]
      direction TB
      style L1_Initialization fill:#F8F8F8,stroke:#B790E8,stroke-width:1.2px,rounded=true

      L1_ConfigInit[[Domain Configurators & Initializers]]
      style L1_ConfigInit fill:#E6E6FA,stroke:#B790E8,stroke-width:1.2px,rounded=true
    end

    %% --------------------------
    %% CERT MGMT & CDN DOMAIN INTEGRATION FLOW
    %% --------------------------
    subgraph L1_CertMgmt["CDN Domain & Certificate Management"]
      direction TB
      style L1_CertMgmt fill:#F8F8F8,stroke:#A9DCF1,stroke-width:1.2px,rounded=true

      L1_LetsEncryptOrder[[SSL & ACME Order Management]]
      style L1_LetsEncryptOrder fill:#FFF8DC,stroke:#ffe292,stroke-width:1.1px,rounded=true

      L1_LetsEncryptChallenge[[ACME Challenge Handler]]
      style L1_LetsEncryptChallenge fill:#FFF8DC,stroke:#ffe292,stroke-width:1.1px,rounded=true
    end

    %% --------------------------
    %% ERROR HANDLING ROOT
    %% --------------------------
    subgraph L1_Errors["Error Handling Core"]
      direction TB
      style L1_Errors fill:#F8F8F8,stroke:#FFB6B9,stroke-width:1.2px,rounded=true

      L1_StorageError[[Storage/CDN/Upload Error]]
      style L1_StorageError fill:#FFE4E1,stroke:#FFB6B9,stroke-width:1.2px,rounded=true
    end

    %% --------------------------
    %% INTER-SUBDOMAIN/DOMAIN COLLABORATION
    %% --------------------------

    %% Direct Uploads <-> StorageAdapter & Abstractions
    DUFM_DirectUploadManager -- Handles/uses --> L1_StorageAdapter
    L1_StorageAdapter -- Persists metadata in --> DS_PendingUploads

    %% Pending Direct Uploads is the store for new uploads
    DUFM_DirectUploadManager -- Persists/Retrieves --> DS_PendingUploads
    DUFM_PendingUploadsStore -- Linked-file-meta --> DS_FileReference

    %% Upload instance connects to reference and CDN
    DUFM_UploadedFile -- Associated-meta --> DS_FileReference

    %% CDN domain links to object storage and certificate
    DUFM_CDNDomain -- Refers to --> DS_CDNDomainObj

    DUFM_CDNDomain -- Requests Certificate via --> L1_LetsEncryptOrder
    L1_LetsEncryptOrder -- Validates challenge via --> L1_LetsEncryptChallenge
    L1_LetsEncryptOrder -- Issues Domain/SSL for --> DS_CDNDomainObj

    %% Object storage API uses infra abstractions
    DUFM_ObjectStorageAPI -- Depends on --> L1_StorageAdapter

    %% Upload Security is fulfilled by UploadMiddleware
    DUFM_UploadSecurity -- Implemented by --> L1_UploadMiddleware
    L1_UploadMiddleware -- Integrates with --> L1_UtilityHelpers

    %% File tools and infra helpers cross-link
    DUFM_FileTools -- Built on --> L1_UtilityHelpers

    %% Initialization influences storage and uploads
    L1_ConfigInit -- Configures --> DUFM_ObjectStorageAPI
    L1_ConfigInit -- Enables --> L1_StorageAdapter

    %% Errors are routed up from subdomain to root error handling
    DUFM_StorageCDNErrors -- Routed to --> L1_StorageError
    L1_StorageAdapter -- Surfaces errors to --> L1_StorageError

    %% All uploads and storage ops have error association
    DUFM_DirectUploadManager -- Raises on error --> L1_StorageError

    %% Data flow: Pending uploads are sources for new upload instances
    DS_PendingUploads -- Instantiates --> DUFM_UploadedFile

    %% Direct file instance is uploaded to object storage
    DUFM_UploadedFile -- Uploads via --> DUFM_ObjectStorageAPI

    %% Object Storage API interacts with CDN domain for public access control
    DUFM_ObjectStorageAPI -- Exposes through --> DUFM_CDNDomain

    %% Utilities and middleware sit in supportive role
    DUFM_FileTools -- Support --> DUFM_DirectUploadManager
    DUFM_UploadSecurity -- Validates --> DUFM_UploadedFile

    %% CDN domain is primary public artifact published
    DUFM_CDNDomain -- CDN endpoint for --> DUFM_UploadedFile

    %% Share Certification endpoint with DS_CDNDomainObj
    DUFM_CDNDomain -- SSL cert/meta --> DS_CDNDomainObj

    %% Error propagation to domain error layer
    DUFM_StorageCDNErrors -- Detected by --> DUFM_ObjectStorageAPI

    %% Initialization sets up security and glob/extension features
    L1_ConfigInit -- Configures --> DUFM_UploadSecurity
    L1_ConfigInit -- Enables filters/matchers on --> DUFM_ObjectStorageAPI

  end
```