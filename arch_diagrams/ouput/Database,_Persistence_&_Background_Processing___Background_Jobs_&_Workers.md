```mermaid
flowchart TB
  %% Combined Domain: Database, Persistence & Background Processing -> Background Jobs & Workers
  %% STYLE DEFINITIONS
  classDef core fill:#D4F1F9,stroke:#7FB8D3,stroke-width:2px,rx:8,ry:8
  classDef support fill:#FFF8DC,stroke:#E5D9AA,stroke-width:2px,rx:8,ry:8
  classDef data fill:#E0F8E0,stroke:#80C280,stroke-width:2px,rx:8,ry:8
  classDef error fill:#FFE4E1,stroke:#D29191,stroke-width:2px,rx:8,ry:8
  classDef init fill:#E6E6FA,stroke:#C1B6DD,stroke-width:2px,rx:8,ry:8
  classDef group fill:#F8F8F8,stroke:#BBC6C8,stroke-width:2px,rx:16,ry:16

  %% DOMAIN TOP
  subgraph DBPBJP["Database, Persistence & Background Processing\nBackground Jobs & Workers"]
    class DBPBJP group
    direction TB

    %% SUBDOMAIN 1 - Worker Management & Scheduling
    subgraph WMS["Worker Management & Scheduling"]
      class WMS group
      direction TB

      WMS_CORE[ApplicationWorker\nBase for all background jobs]:::core
      WMS_COORD[JobCoordinator\nScheduling & Coordination]:::core
      WMS_MIG[BackgroundMigrationWorker\nMigration Job Orchestration]:::core
      WMS_QUEUE[Worker Queues & Routing\nCronjobQueue, PipelineQueue, etc]:::support
      WMS_MIXINS[Worker Mixins/Concerns]:::support
      WMS_DATA[Background Job Records & Logs\nJob Status, Batch, Metrics]:::data
      WMS_SCHED[SidekiqConfig & Sharding\nQueue Assignment, CronInit]:::support
      WMS_LOCKS[ExclusiveLease\nDistributed Locking]:::support
      WMS_ERROR[Worker Error Handlers]:::error
      WMS_MON[QueueDaemon/Monitor\nConcurrency, Health]:::core
      
      %% Key worker implementation group (abstracted, not detailed)
      WMS_WORKERS[Domain Worker Groups\nProject, CI, DB, Integration, Pages, Webhook, Export, Email, etc]:::core
      
      %% Logical grouping boundary
      class WMS_CORE,WMS_COORD,WMS_MIG,WMS_MON,WMS_WORKERS core
      class WMS_QUEUE,WMS_SCHED,WMS_MIXINS,WMS_LOCKS support
      class WMS_DATA data
      class WMS_ERROR error
    end

    %% SUBDOMAIN 2 - Job Utilities, Consistency & Idempotence
    subgraph UCID["Job Utilities, Consistency & Idempotence"]
      class UCID group
      direction TB

      UCID_APPWORKER[Worker Base Abstractions\nApplicationWorker, Shared Concerns]:::core
      UCID_ATTR[WorkerAttributes\nFeature Cat, Consistency]:::core
      UCID_IDEMPOTENCY[Idempotency & Deduplication\nJob-level IdempotencyCache, Retry Util]:::core
      UCID_LIMIT[LimitedCapacity/Job Limit & Throttle]:::core
      UCID_AFTERCOMMIT[AfterCommitQueue\nTransaction Callback]:::core
      UCID_BATCH[Batch Processing Abstractions\nEachBatch, FastDestroyAll, etc]:::core
      UCID_BGMIG["Background Migration Strategies\nBatching, Logger"]:::core
      UCID_DB_CONS["Database Consistency\nRead-after-write, LeaseGuard"]:::core
      UCID_SIDEKIQ[Sidekiq Job Utils\nNormalizers, Retriers]:::support
      UCID_SYNCH[Job Synchronization & Waiters\nBatch Waiter, BackgroundTask]:::support
      UCID_JOBDATA[Batch/Work State Trackers\nJobTracker, Batch Logs, Metrics]:::data
      UCID_QUEUEERR[Queue Error Handling]:::error
      UCID_RESCH[Reloop/Rescheduling Helper\nLoopWithRuntimeLimit, ReschedulingMethods]:::support

      %% Logical grouping
      class UCID_APPWORKER,UCID_ATTR,UCID_IDEMPOTENCY,UCID_LIMIT,UCID_AFTERCOMMIT,UCID_BATCH,UCID_BGMIG,UCID_DB_CONS core
      class UCID_SIDEKIQ,UCID_SYNCH,UCID_RESCH support
      class UCID_JOBDATA data
      class UCID_QUEUEERR error
    end

    %% SHARED DOMAIN DATA STRUCTURES & ABSTRACTIONS
    subgraph DOMAIN_DS["Domain-Shared Data Structures, Patterns, Contracts"]
      class DOMAIN_DS group
      direction TB

      DS_JOBREC[Job Record/State\nBatch, Status, Transition Log]:::data
      DS_BATCHMETRIC[Batch Metrics & Samplers]:::data
      DS_CRONCFG[CronJob Config/Queue Contracts]:::data
      DS_LOCK[Distributed Lock/Lease\nExclusiveLease, LeaseGuard]:::support
      DS_CONSISTENCY[Consistency Idempotence Contracts]:::core
      DS_PIPELINEQ[Pipeline Queue Interface]:::data
      DS_CAPACITY[Job Capacity Tracker]:::data
    end

    %% CROSS-CUTTING DOMAIN BEHAVIORS / PATTERNS
    subgraph DOMAIN_PATTERNS["Cross-Domain Patterns & Key Flows"]
      class DOMAIN_PATTERNS group
      direction TB

      DP_SCHEDULING[Job Scheduling & Queue Routing]:::core
      DP_CRON[Recurring Jobs/Cron Schedules]:::core
      DP_CONCURRENCY[Workers Concurrency Control & Throttling]:::core
      DP_BATCHING[Batch Execution & Partitioned Operations]:::core
      DP_CONSISTENCY[Consistency, Error Handling, Deduplication]:::core
      DP_MONITOR[Health, State Report, Self-healing]:::core
    end

  end

  %% TOP-LEVEL DOMAIN FLOW
  DBPBJP --> WMS
  DBPBJP --> UCID
  DBPBJP --> DOMAIN_DS
  DBPBJP --> DOMAIN_PATTERNS

  %% WORKER MANAGEMENT & SCHEDULING
  WMS_CORE -->|schedules, runs| WMS_WORKERS
  WMS_MIG -->|delegates jobs| WMS_WORKERS
  WMS_COORD -->|coordinates| WMS_WORKERS
  WMS_MIXINS -->|adds common behaviors to| WMS_WORKERS
  WMS_QUEUE -->|controls queues for| WMS_WORKERS
  WMS_SCHED -->|assigns and initializes| WMS_QUEUE
  WMS_LOCKS -->|locks/leases used by| WMS_WORKERS
  WMS_ERROR -->|catches| WMS_WORKERS
  WMS_MON -->|monitors| WMS_WORKERS
  
  %% WMS -> DOMAIN DATA STRUCTURES
  WMS_CORE --uses--> DS_JOBREC
  WMS_MIG --tracks via--> DS_JOBREC
  WMS_COORD --updates--> DS_JOBREC
  WMS_QUEUE --configures via--> DS_CRONCFG
  WMS_MON --monitors via--> DS_BATCHMETRIC
  WMS_LOCKS --integrates--> DS_LOCK

  %% JOB UTILITIES, CONSISTENCY & IDEMPOTENCE
  UCID_APPWORKER --base for--> UCID_IDEMPOTENCY
  UCID_APPWORKER --applies attributes--> UCID_ATTR
  UCID_IDEMPOTENCY --uses cache/data--> UCID_JOBDATA
  UCID_LIMIT --tracks via--> UCID_JOBDATA
  UCID_AFTERCOMMIT --queues for--> UCID_APPWORKER
  UCID_BATCH --invokes to process via batching--> UCID_BGMIG
  UCID_DB_CONS --used by--> UCID_APPWORKER
  UCID_SIDEKIQ --normalizes, retries and delegates to--> UCID_APPWORKER
  UCID_SYNCH --waits/completes batches on--> UCID_APPWORKER
  UCID_QUEUEERR --handles--> UCID_APPWORKER
  UCID_RESCH --is utility for--> UCID_APPWORKER

  %% UCID <-> DOMAIN DATA STRUCTURES
  UCID_APPWORKER --logs via--> DS_JOBREC
  UCID_IDEMPOTENCY --relies on--> DS_CONSISTENCY
  UCID_LIMIT --tracks limits via--> DS_CAPACITY
  UCID_BATCH --uses queue/config--> DS_CRONCFG
  UCID_DB_CONS --applies via--> DS_CONSISTENCY
  UCID_QUEUEERR --writes/truncates in--> DS_JOBREC

  %% SHARED STRUCTURES/PATTERNS: WMS <-> UCID
  WMS_WORKERS --implement via shared base--> UCID_APPWORKER
  WMS_CORE --delegates idempotence, limits, consistency to--> UCID_IDEMPOTENCY
  WMS_MON --observes state/event log from--> UCID_JOBDATA
  WMS_ERROR --escalates error handling to--> UCID_QUEUEERR
  WMS_MIXINS --include batch/iteration utility from--> UCID_BATCH
  WMS_LOCKS --integrates leaseguard/idempotency from--> UCID_DB_CONS

  UCID_APPWORKER --extends with WMS scheduling/routing concerns--> WMS_QUEUE
  UCID_BATCH --provides partition/batching contracts to--> WMS_WORKERS
  UCID_LIMIT --is referenced in concurrency control for--> WMS_MON
  UCID_AFTERCOMMIT --ensures transaction atomicity for--> WMS_WORKERS

  %% DATA STRUCTURE FLOWS ACROSS SUBDOMAINS
  DS_JOBREC --status & transitions recorded for all jobs, migrations, workers--> WMS_WORKERS
  DS_JOBREC --tracked by--> UCID_APPWORKER
  DS_BATCHMETRIC --referenced in job/batch monitoring--> WMS_MON & UCID_JOBDATA
  DS_LOCK --synchronization enforced for job uniqueness, concurrency--> WMS_LOCKS & UCID_DB_CONS
  DS_CRONCFG --cron schedules/intervals for batch & queue jobs--> WMS_QUEUE & UCID_BATCH
  DS_PIPELINEQ --interface for pipelined jobs, reused in batch processing & queues--> WMS_QUEUE & UCID_BATCH
  DS_CAPACITY --tracks global/worker level concurrency, used by limiters--> WMS_MON & UCID_LIMIT
  DS_CONSISTENCY --idempotence, read-after-write policies enforced across all background domain

  %% DOMAIN PATTERNS AND FLOWS
  DP_SCHEDULING --directs queues, applies config to--> WMS_QUEUE & UCID_SIDEKIQ
  DP_SCHEDULING --input for job orchestration on--> WMS_CORE & UCID_APPWORKER
  DP_CRON --schedules batch jobs/cron workers via contract--> WMS_QUEUE & DS_CRONCFG
  DP_CRON --applies pattern for periodic/recurring execution of--> WMS_WORKERS & UCID_BATCH
  DP_CONCURRENCY --throttling & job slot allocation through--> WMS_MON & UCID_LIMIT
  DP_CONCURRENCY --influences worker behavior at base via--> UCID_APPWORKER
  DP_BATCHING --batch partition logic, ranges, each_batch for huge data/async jobs--> UCID_BATCH, WMS_MIG & WMS_WORKERS
  DP_CONSISTENCY --idempotence/consistency guarantee flows into base/job/worker logic--> UCID_DB_CONS, UCID_IDEMPOTENCY, WMS_LOCKS, DS_CONSISTENCY
  DP_MONITOR --system health/stale job recovery on--> WMS_MON, DS_JOBREC, UCID_JOBDATA

  %% KEY DOMAIN COLLABORATIONS vertical linking
  WMS_CORE --relies on utility/consistency contracts from--> UCID_APPWORKER
  WMS_WORKERS --execute and mutate state using domain data structures--> DOMAIN_DS
  WMS_MIG --schedules batched migration jobs using batch processing utils from--> UCID_BATCH
  WMS_QUEUE --initialized/configured with help of--> UCID_SIDEKIQ, UCID_AFTERCOMMIT

  UCID_APPWORKER --provides base, contracts, shared logic for all job types, bridging UCID & WMS
  UCID_BATCH --key abstraction for all batched/partitioned/iterative operations in background jobs domain
  UCID_DB_CONS --ensures jobs/worker operations are consistent, non-duplicated, with correct result across replays

  %% LOGICAL GROUPINGS
  class DBPBJP group
  class WMS,UCID,DOMAIN_DS,DOMAIN_PATTERNS group

  %% LEGEND -- light, pastel
  subgraph LEGEND["Legend"]
    class LEGEND group
    direction TB
    LEG_CORE[Core domain logic]:::core
    LEG_SUPPORT[Supporting/utility logic]:::support
    LEG_DATA[Data structure/record]:::data
    LEG_ERROR[Error handling/concern]:::error
    LEG_INIT[Initialization/setup]:::init
    LEG_GROUP[Logical grouping/subgraph]:::group
  end

  %% Ensure vertical layout with enough spacing
  WMS --> DOMAIN_DS
  UCID --> DOMAIN_DS
  DOMAIN_DS --> DOMAIN_PATTERNS
```