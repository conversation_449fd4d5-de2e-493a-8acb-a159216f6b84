```mermaid
flowchart TB
%% ==================== COLOR STYLES ======================================
classDef core fill:#D4F1F9,stroke:#A2CFEF,stroke-width:2px,color:#363A3F,rx:8,ry:8
classDef data fill:#E0F8E0,stroke:#A6DEB6,stroke-width:2px,color:#25664B,rx:8,ry:8
classDef util fill:#FFF8DC,stroke:#F7DF8C,stroke-width:2px,color:#927E12,rx:8,ry:8
classDef error fill:#FFE4E1,stroke:#EDB6B6,stroke-width:2px,color:#914040,rx:8,ry:8
classDef init fill:#E6E6FA,stroke:#C1B6E0,stroke-width:2px,color:#6B5580,rx:8,ry:8
classDef group fill:#F8F8F8,stroke:#BED8EB,stroke-width:3px,rx:16,ry:16,stroke-dasharray:5 3
classDef newpastel fill:#F6F0FF,stroke:#E0CFFF,stroke-width:2px,color:#68548C,rx:8,ry:8

%% ================== DOMAIN HIERARCHY ==================================

subgraph CMgmtRoot["Security, Compliance & Policies / Compliance Management"]
class CMgmtRoot group

    %% ----- SUBDOMAIN LOGICAL PURPOSES -----
    subgraph AuditReporting["Audit Events & Reporting"]
    direction TB
        ar_core["Audit Event Models
  - Event definition & structure
  - Domain hooks" ]:::core
        ar_dest["Destinations & Integrations
  - Streaming/log sinks
  - Filters & headers" ]:::core
        ar_service["Audit Services
  - Capture, export & stream
  - Specialized event services" ]:::core
        ar_ctrl["REST & GraphQL Controllers
  - UI & API
  - Admin access" ]:::core
        ar_policy["Policies & Permissions
  - Access enforcement 
  - Destination policy files" ]:::core
        ar_data["Audit Event Data Structures
  - Serializers
  - Event queue
  - Details objects" ]:::data
        ar_helpers["Domain Helpers & Validators
  - Streaming sync helpers
  - Validators" ]:::util
        ar_proc["Audit Workflow Process
  - Auditable concerns
  - Domain workflow base" ]:::util
        ar_compliance_int["Compliance Integration
  - PIPL and requirements
  - Compliance workers" ]:::init
    end

    subgraph LicenseScanPol["License Scanning & Policies"]
    direction TB
        lsp_core["License Scanning Core
  - License scan orchestration
  - SBOM/CI scanner abstraction" ]:::core
        lsp_data["License Scan Data & Reports
  - License reports
  - Comparison entities" ]:::data
        lsp_policies["License & Compliance Policies
  - Policy objects
  - Approval rules
  - Policy serialization" ]:::core
        lsp_enforcement["Policy Enforcement & Compliance Events
  - License violation checkers
  - Approval rule builder
  - Background compliance workers" ]:::core
        lsp_graphql["Policy Application & GraphQL
  - Policy resolvers & mutations" ]:::util
        lsp_helpers["Support & Integration Services
  - Mailers
  - Helpers
  - Parsers" ]:::util
        lsp_custom["Custom License Policy Management
  - Custom policy finders/creators" ]:::util
    end

    %% ------------ DOMAIN-SPECIFIC DATA STRUCTURES SHARED/IMPORTANT --------------------
    ds_audit_event["AuditEvent
  Domain event, source for auditing & compliance reporting"]:::data
    ds_license_report["LicenseScanReport
  Data structure for detected licenses, referenced in compliance"]:::data
    ds_policy["CompliancePolicy
  Abstraction for approvals/policies, spans events and scan results"]:::data
    ds_violation["ComplianceViolation
  Unified for audit or license type violation"]:::data

    %% ------------ KEY ABSTRACTIONS & PATTERNS (SPANNING DOMAIN) -------------------------
    ab_auditable["Auditable Concern
  Models/Services include this for event emission"]:::util
    ab_stream_dest["Streaming/Integration Destinations
  Pattern for logging/export of compliance data"]:::util
    ab_policy_enforce["Policy Enforcement Service
  Pattern for enforcing policies on domain events"]:::util
    ab_report_compare["Report Comparison & Serialization
  Pattern for diffing/comparing reports for compliance"]:::util

end

%% ====================== LOGICAL SUBDOMAIN INTERFACE HIGHLIGHTS =========================

%% -- Edge: Core data flows and integration between subdomains --
ar_core --produces--> ds_audit_event
ar_service --logs--> ds_audit_event
ar_dest --streams--> ds_audit_event
ar_ctrl --queries--> ds_audit_event
ar_data --serializes--> ds_audit_event
ar_policy --controls access to--> ar_service
ar_helpers --supports--> ar_service
ar_proc --injects events into--> ds_audit_event
ar_compliance_int --analyses--> ds_audit_event

lsp_core --populates--> ds_license_report
lsp_data --compares/serializes--> ds_license_report
lsp_policies --applies to--> ds_license_report
lsp_enforcement --enforces--> lsp_policies
lsp_enforcement --triggers--> ds_violation
lsp_graphql --controls access to--> lsp_policies
lsp_helpers --supports--> lsp_core
lsp_custom --manages--> lsp_policies

%% -- Edges: Domain-wide abstractions/patterns connection --
ds_audit_event --subject to--> ds_policy
ds_license_report --subject to--> ds_policy
ds_policy --defines/actions on--> ds_violation
ds_violation --materialized in--> ar_compliance_int
ds_violation --materialized in--> lsp_enforcement

ab_auditable --included by--> ar_core
ab_auditable --included by--> ar_proc
ab_auditable --used for plugin pattern in--> lsp_enforcement
ab_stream_dest --implemented by--> ar_dest
ab_stream_dest --used by--> lsp_core
ab_policy_enforce --pattern for--> ar_policy
ab_policy_enforce --pattern for--> lsp_policies
ab_policy_enforce --pattern for--> lsp_enforcement
ab_report_compare --implemented by--> ar_data
ab_report_compare --implemented by--> lsp_data

%% -- Cross-subdomain collaboration flows --
ar_compliance_int --feeds violation info to--> lsp_enforcement
ar_service --exports compliance info to--> lsp_policies
ar_data --feeds data to--> lsp_core
lsp_policies --utilizes audit events from--> ds_audit_event

%% ======================== LAYOUT AND GROUPING RELATIONSHIPS ============================

%% -- Shared data structure relationships --
ds_audit_event -.provides source events.-> ds_violation
ds_license_report -.provides detected licenses.-> ds_violation
ds_policy -.classifies.-> ds_violation

%% -- Important overall flows --
ar_core -->|Defines & emits| ds_audit_event
ar_service --Validates & manages--> ds_audit_event
ar_dest --Distributes--> ds_audit_event
ar_compliance_int --Spawns/updates compliance report jobs--> ds_audit_event
ar_proc --Hooks to model actions--> ar_core

lsp_core -->|Orchestrates| ds_license_report
lsp_policies --Defines rules for--> ds_license_report
lsp_enforcement --Monitors--> ds_license_report
lsp_enforcement --Notifies/records--> ds_violation
lsp_data --Compares/serializes--> ds_license_report

%% -- Abstraction flows across the domain --
ab_auditable -.integrated in models/services.-> ar_core
ab_policy_enforce -.shared logic.-> lsp_enforcement
ab_stream_dest -.shared pattern.-> ar_dest
ab_report_compare -.diffing/compare logic.-> lsp_data

%% -- Core subdomain group layouts --
class CMgmtRoot group
class AuditReporting,ar_core,ar_dest,ar_service,ar_ctrl,ar_policy,ar_data,ar_helpers,ar_proc,ar_compliance_int group
class LicenseScanPol,lsp_core,lsp_data,lsp_policies,lsp_enforcement,lsp_graphql,lsp_helpers,lsp_custom group

%% -- Domain-specific data and patterns --
class ds_audit_event,ds_license_report,ds_policy,ds_violation data
class ab_auditable,ab_stream_dest,ab_policy_enforce,ab_report_compare util

%% =========================== DOMAIN-GLOBAL LAYOUT ================================

%% Visual vertical layout: main domain at top, two subdomains (with grouping), 
%% shared data/abstractions in between with inter-subdomain flows below

CMgmtRoot
    subgraph S1["   "]
    direction TB
        AuditReporting
        ds_audit_event
        ds_policy
        ds_license_report
        ds_violation
        ab_auditable
        ab_stream_dest
        ab_policy_enforce
        ab_report_compare
        LicenseScanPol
    end
end
```