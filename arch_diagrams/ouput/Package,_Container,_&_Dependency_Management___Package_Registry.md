```mermaid
flowchart TB
%% ------------------- GLOBAL STYLE CLASSDEFS -------------------
classDef core fill:#D4F1F9,stroke:#64b7db,stroke-width:2,rx:10,ry:10
classDef util fill:#FFF8DC,stroke:#e7c678,stroke-width:2,rx:10,ry:10
classDef data fill:#E0F8E0,stroke:#30d179,stroke-width:2,rx:10,ry:10
classDef error fill:#FFE4E1,stroke:#ECA6A6,stroke-width:2,rx:10,ry:10
classDef init fill:#E6E6FA,stroke:#998eca,stroke-width:2,rx:10,ry:10
classDef subgraph fill:#F8F8F8,stroke:#A7A7A7,stroke-width:3,rx:24,ry:24
classDef group fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rx:18,ry:18
classDef group_green fill:#F8F8F8,stroke:#b5dab8,stroke-width:2,rx:18,ry:18
classDef group_yellow fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,rx:18,ry:18

%% ========================= DOMAIN ROOT =========================
subgraph PKG_REGISTRY_DOMAIN["Package Registry Domain"]
direction TB
class PKG_REGISTRY_DOMAIN subgraph

  CORE_DATACONCEPT: Package Domain Models
  class CORE_DATA core

  subgraph SUBDOMAINS_GROUP["Subdomains Overview"]
    direction TB
    class SUBDOMAINS_GROUP group

    PKG_METADATA_SDPackage Metadata & Security
    class PKG_METADATA_SD core

    MAVEN_SD(Maven Artifacts Registry)
    class MAVEN_SD core

    NPM_SD(NPM Packages Registry)
    class NPM_SD core

    CONAN_SD(Conan Package Registry)
    class CONAN_SD core
  end

  POLICIES(Package Access & Policy)
  class POLICIES util

end

%% ================ CROSS-CUTTING DATA STRUCTURES =================
subgraph COMMON_DATA_STRUCTS["Domain-Spanning Data Structures"]
  direction TB
  class COMMON_DATA_STRUCTS group_green
  PKG_PACKAGEPackage
  PKG_DEPENDENCY(Package Dependency)
  PKG_FILE(Package File/Artifact)
  PKG_TAG(Tag)
  PKG_METADATA(Metadata Entity)
  PKG_BUILDINFO(Build Information)
  PKG_NAMESPACE_SETTING(Package Registry Setting/Policy)
end
class PKG_PACKAGE,PKG_DEPENDENCY,PKG_FILE,PKG_TAG,PKG_METADATA,PKG_BUILDINFO,PKG_NAMESPACE_SETTING data

%% ======== DOMAIN BEHAVIORS & HIGH-LEVEL PATTERNS ================
subgraph DOMAIN_BEHAVIORS["Key Behaviors & Orchestrators"]
  direction TB
  class DOMAIN_BEHAVIORS group_yellow
  PKG_CREATECreate/Find Package Process
  PKG_PUBLISH(Publish & Upload Flow)
  PKG_VERSION(Version/Revision Management)
  PKG_METADATA_MGMT(Metadata Ingestion & Sync)
  PKG_ACCESS_CTRL(Access & Authorization)
  PKG_VIRTUAL(Virtual/Upstream Registries)
  PKG_SEARCH_SEARCH(Search/Query)
  PKG_LIFECYCLE(Lifecycle & Cleanup)
end
class PKG_CREATE,PKG_PUBLISH,PKG_VERSION,PKG_METADATA_MGMT,PKG_ACCESS_CTRL,PKG_VIRTUAL,PKG_SEARCH_SEARCH,PKG_LIFECYCLE util

%% ==================== SUBDOMAIN: PACKAGE METADATA CORE ==========================
subgraph PKG_METADATA["Subdomain: Package Metadata & Security"]
  direction TB
  class PKG_METADATA subgraph

  PKGMDL_PACKAGECore Package Model
  PKGMDL_DEP(Package Dependency)
  PKGMDL_FILE(File Model)
  PKGMDL_TAG(Tag Model)
  PKGMDL_META(Metadata Record)
  PKGMDL_POLICIES(Authorization & Policy Layer)
  PKGMDL_SERVICES(Core Services)
  PKGMDL_FINDERS(Generic Finders)
  PKGMDL_WORKERS(Background Processing)
  PKGMDL_ENUMS(Enums/Constants)
  PKGMDL_SYNC(Security/Advisory Sync)
  PKGMDL_ERROR(Error Handling)
end
class PKGMDL_PACKAGE,PKGMDL_DEP,PKGMDL_FILE,PKGMDL_TAG,PKGMDL_META core
class PKGMDL_POLICIES util
class PKGMDL_SERVICES,PKGMDL_FINDERS,PKGMDL_WORKERS util
class PKGMDL_ENUMS,PKGMDL_SYNC data
class PKGMDL_ERROR error

%% =================== SUBDOMAIN: MAVEN ==========================
subgraph PKG_MAVEN["Subdomain: Maven Registry"]
direction TB
class PKG_MAVEN subgraph

  MVN_PKGMaven Package Model
  MVN_META(Maven Metadata)
  MVN_MDL_POLICY(Maven Policy)
  MVN_SERVICES(Package Lifecycle Services)
  MVN_META_SRV(Metadata Sync/Xml Services)
  MVN_VIRTUAL(Virtual Registry/Upstreams)
  MVN_API(API Helpers)
  MVN_CACHE(Cache & Workers)
  MVN_ERROR(Error/E2E QA)
end
class MVN_PKG,MVN_META,MVN_MDL_POLICY core
class MVN_SERVICES, MVN_VIRTUAL, MVN_API, MVN_CACHE util
class MVN_META_SRV data
class MVN_ERROR error

%% =================== SUBDOMAIN: NPM ============================
subgraph PKG_NPM["Subdomain: NPM Registry"]
direction TB
class PKG_NPM subgraph

  NPM_PKGNPM Package Model
  NPM_META(NPM Metadata)
  NPM_CACHE(Cache/Storage)
  NPM_FILE(Package File)
  NPM_SERVICES(Services: Creation/Deprecation)
  NPM_WORKERS(Workers/Background)
  NPM_FINDERS(Finders)
  NPM_API(API/Endpoints)
  NPM_IMPORT(Migrations/Source User)
end
class NPM_PKG,NPM_META,NPM_FILE core
class NPM_CACHE,NPM_SERVICES,NPM_WORKERS,NPM_FINDERS,NPM_API,NPM_IMPORT util

%% =================== SUBDOMAIN: CONAN ==========================
subgraph PKG_CONAN["Subdomain: Conan Registry"]
direction TB
class PKG_CONAN subgraph

  CONAN_ROOTDomain Aggregate Root
  CONAN_PKG(Conan Package)
  CONAN_META(Conan Metadata)
  CONAN_REF(Package Reference)
  CONAN_REV(Package Revision)
  CONAN_SERVICES(Creation/Upsert/Files)
  CONAN_SEARCH(Search/Finders)
  CONAN_API(API Entities/Endpoints)
  CONAN_AUTH(Tokens)
  CONAN_WORKERS(Worker Processing)
end
class CONAN_ROOT,CONAN_PKG,CONAN_META,CONAN_REF,CONAN_REV core
class CONAN_SERVICES,CONAN_SEARCH,CONAN_API,CONAN_AUTH,CONAN_WORKERS util

%% ========= COLLABORATIONS & RELATIONSHIPS AT DOMAIN LEVEL ===========

%% ---- Subdomain purposes
PKG_METADATA_SD ---|Security, core models,\n dependency & metadata flows| PKG_METADATA
MAVEN_SD ---|Java ecosystem, Maven-specific artifacts,\n virtual upstreams, xml metadata, caching| PKG_MAVEN
NPM_SD ---|JavaScript ecosystem, npm packages,\n tagging and metadata cache, user migration| PKG_NPM
CONAN_SD ---|C/C++/binary, Conan recipes and references,\n revisions and metadata workflows| PKG_CONAN

%% ---- Shared concepts
CORE_DATA -.->|Model base for all packages| COMMON_DATA_STRUCTS
COMMON_DATA_STRUCTS -->|Base entities referenced by all registries| PKG_METADATA
COMMON_DATA_STRUCTS --> PKG_MAVEN
COMMON_DATA_STRUCTS --> PKG_NPM
COMMON_DATA_STRUCTS --> PKG_CONAN

%% ---- Cross-subdomain data flow
PKG_PUBLISH --> PKG_CREATE
PKG_CREATE ---|Triggers storage, lifecycle, metadata flows| PKG_METADATA
PKG_CREATE --> PKG_MAVEN
PKG_CREATE --> PKG_NPM
PKG_CREATE --> PKG_CONAN

PKG_METADATA_MGMT ---|Central metadata flows: Enrichment, sync\n advisories, cache generation, versioning| PKG_METADATA
PKG_METADATA_MGMT --> PKG_MAVEN
PKG_METADATA_MGMT --> PKG_NPM
PKG_METADATA_MGMT --> PKG_CONAN

PKG_VERSION ---|Domain-wide: package revisions, versions,\n manifest, cache, semantic validation| PKG_METADATA
PKG_VERSION --> PKG_MAVEN
PKG_VERSION --> PKG_NPM
PKG_VERSION --> PKG_CONAN

PKG_ACCESS_CTRL ---|Policies, authorization, project/group gating| POLICIES
POLICIES --> PKG_METADATA
POLICIES --> PKG_MAVEN
POLICIES --> PKG_NPM
POLICIES --> PKG_CONAN

PKG_SEARCH_SEARCH ---|Unified search: domain, language, cross-repo| PKG_METADATA
PKG_SEARCH_SEARCH --> PKG_MAVEN
PKG_SEARCH_SEARCH --> PKG_NPM
PKG_SEARCH_SEARCH --> PKG_CONAN

PKG_LIFECYCLE ---|Cleanup, background processing,\n E2E flows, QA, error resilience| PKG_METADATA
PKG_LIFECYCLE --> PKG_MAVEN
PKG_LIFECYCLE --> PKG_NPM
PKG_LIFECYCLE --> PKG_CONAN

PKG_VIRTUAL ---|Virtual/Upstream registry federation,\n dependency mirroring/cache| PKG_MAVEN
PKG_VIRTUAL --> PKG_METADATA

%% ---- Subdomain-to-subdomain direct relationships
PKG_MAVEN ---|References generic package & metadata flows| PKG_METADATA
PKG_MAVEN ---|Implements virtual registry via shared policies/data| PKG_METADATA

PKG_NPM ---|Uses core domain entities/models/routes\n via web/api concerns, shared services| PKG_METADATA

PKG_CONAN ---|Integration, storage, shared policies| PKG_METADATA

PKG_MAVEN ---|Cache, upstream, and virtual registry\n pattern reused by NPM and Conan| PKG_NPM
PKG_MAVEN ---|Metadata ingestion patterns\n cross-applies to Conan| PKG_CONAN

PKG_CONAN ---|API route and service/worker\n conventions reused from NPM & Maven| PKG_MAVEN
PKG_CONAN ---|Shared dependency/metadata linkage| PKG_NPM

%% ---- Below: Data structures & abstractions spanning all subdomains

PKG_PACKAGE ---|Specialized/model-typed in| MVN_PKG
PKG_PACKAGE ---|Specialized/model-typed in| NPM_PKG
PKG_PACKAGE ---|Specialized/model-typed in| CONAN_PKG
PKG_PACKAGE ---|Base for all artifact storage| PKGMDL_PACKAGE

PKG_METADATA ---|Specialized for ecosystem| MVN_META
PKG_METADATA ---|Specialized for ecosystem| NPM_META
PKG_METADATA ---|Specialized for ecosystem| CONAN_META
PKG_METADATA ---|Base for domain security/scan| PKGMDL_META

PKG_FILE ---|Artifact storage link| PKGMDL_FILE
PKG_FILE ---|File caches| NPM_FILE
PKG_FILE ---|File metadata| CONAN_META

PKG_DEPENDENCY ---|Features in dependency fields, manifests| PKGMDL_DEP
PKG_DEPENDENCY ---|Core for security/advisory| PKG_METADATA

PKG_TAG ---|Tagging/version| PKGMDL_TAG
PKG_TAG ---|Tag handling in npm| NPM_SERVICES

PKG_NAMESPACE_SETTING ---|Policies, quotas,\n project/group gates| POLICIES

%% ---- Core patterns connecting subdomains
MVN_SERVICES ---|Implements package lifecycle flows| PKG_CREATE
NPM_SERVICES ---|Implements package creation,\n tag/deprecation| PKG_CREATE
CONAN_SERVICES ---|Implements package, revision creation| PKG_CREATE
PKGMDL_SERVICES ---|Shared orchestration logic for all subdomains| PKG_CREATE

MVN_META_SRV ---|XML/metadata handling and cache| PKG_METADATA_MGMT
NPM_CACHE ---|Metadata JSON, file cache| PKG_METADATA_MGMT
CONAN_META ---|Metadata files per revision| PKG_METADATA_MGMT
PKGMDL_SYNC ---|Advisory/cve metadata enrichment| PKG_METADATA_MGMT

%% ---- Workers & background jobs
MVN_CACHE ---|Metadata sync, file append, cache update| PKG_LIFECYCLE
NPM_WORKERS ---|Metadata, cleanup, deprecation/BG flows| PKG_LIFECYCLE
CONAN_WORKERS ---|File background processing| PKG_LIFECYCLE
PKGMDL_WORKERS ---|Domain-wide cleanup/scan| PKG_LIFECYCLE

%% ---- Authorization, policies, and errors propagated
MVN_MDL_POLICY ---|API/virtual registry gating| PKG_ACCESS_CTRL
NPM_SERVICES ---|API-bound, quota, user controls| PKG_ACCESS_CTRL
CONAN_AUTH ---|Token/endpoint control| PKG_ACCESS_CTRL
PKGMDL_POLICIES ---|Domain-wide policies| PKG_ACCESS_CTRL

MVN_ERROR ---|Surface error flows, e2e| PKG_LIFECYCLE
NPM_SERVICES ---|Deprecation, error propagation| PKG_LIFECYCLE
CONAN_WORKERS ---|Error handling for processing| PKG_LIFECYCLE
PKGMDL_ERROR ---|Domain error model| PKG_LIFECYCLE

%% ---- APIs, endpoints, ecosystem connectors
MVN_API ---|REST, download, external format\n for Maven clients| PKG_PUBLISH
NPM_API ---|NPM endpoints, upload & tag flows| PKG_PUBLISH
CONAN_API ---|Conan v1/v2 endpoints| PKG_PUBLISH
PKGMDL_SERVICES ---|Web controllers, API endpoints| PKG_PUBLISH

%% ---- Search and discoverability
MVN_SERVICES ---|Finder/Selector, virtual registry\n supports search| PKG_SEARCH_SEARCH
NPM_FINDERS ---|Package/user level finder logic| PKG_SEARCH_SEARCH
CONAN_SEARCH ---|Finders for revision/files| PKG_SEARCH_SEARCH
PKGMDL_FINDERS ---|Unified finders, DB queries| PKG_SEARCH_SEARCH

%% ---- Lifecycles, seeds, and migration
NPM_IMPORT ---|DB migration, source-user flows| PKG_LIFECYCLE
PKGMDL_WORKERS ---|Global scan/migration| PKG_LIFECYCLE

%% ---- Virtual registry patterns
MVN_VIRTUAL ---|Upstream proxy/cache through\n virtual registry concept| PKG_VIRTUAL

%% ---- Data/metadata flows
PKG_METADATA --> PKG_CREATE
PKG_METADATA --> PKG_PUBLISH
PKG_METADATA --> PKG_METADATA_MGMT

%% ================ LEGEND GROUP (SYNTHETIC, PASTEL SHADES) ===================
subgraph LEGEND["Legend & Abstraction Map"]
  direction TB
  style LEGEND fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2,rx:14,ry:14
  l1_core(Core domain concept):::core
  l2_data(Basic/shared data structure):::data
  l3_util(Utility/service/lifecycle/process):::util
  l4_error(Error/QA/handling):::error
  l5_init(Initialization/worker/bg):::init
end

%% ========== HORIZONTAL SPACING DUMMIES ==========
CORE_DATA -.-> LEGEND

%% ========== CLASS ASSIGNMENTS ON NEW COMPONENTS ==========
class PKG_PACKAGE,PKG_DEPENDENCY,PKG_FILE,PKG_TAG,PKG_METADATA,PKG_BUILDINFO,PKG_NAMESPACE_SETTING data
class PKG_CREATE,PKG_PUBLISH,PKG_VERSION,PKG_METADATA_MGMT,PKG_ACCESS_CTRL,PKG_VIRTUAL,PKG_SEARCH_SEARCH,PKG_LIFECYCLE util

%% END DIAGRAM
```