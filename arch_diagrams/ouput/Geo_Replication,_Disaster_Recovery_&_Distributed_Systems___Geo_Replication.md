```mermaid
flowchart TB
  %% =====================================
  %% Top-level: Geo Replication Domain
  %% =====================================
  subgraph GeoReplicationDomain["Geo Replication Domain"]
    direction TB
    style GeoReplicationDomain fill:#F8F8F8,stroke:#8EC3EB,stroke-width:4,stroke-dasharray: 4 2

    %% -----------------------------------------------------------------
    %% SUBDOMAIN GROUPS
    %% -----------------------------------------------------------------

    %% 1. Geo Nodes & Registries Core data model & system configuration
    subgraph GeoNodesRegistries["Geo Nodes & Registries"]
      direction TB
      style GeoNodesRegistries fill:#F8F8F8,stroke:#8AC6D1,stroke-width:3,rx:18,ry:18

      GeoNodeCore[Geo Node\nManages physical and logical site information]:::core
      GeoNodeStatus[Geo Node Status\nTracks health and sync state]:::core
      Registries[Replicable Resource Registries\nTracks project/wiki/artifact state per node]:::core
      RegistryStates[Registry States\nData snapshot per registry]:::data
      Replicator[Replicator Abstraction\nUnified interface for replication operations]:::core
      RegistryFinders[Registry Finders\nFilter and access registry state]:::support
      NodeServices[Node & Registry Services\nSynchronize, update, remove state]:::core
      RegistryWorkers[Registry Workers\nBackg. jobs for sync, verification, cleanup]:::support
      Migrations[Geo Migrations & DB\nSchema evolution for node/registry]:::setup
    end

    %% 2. Event Logs & Finders Change tracking, propagation, event processing
    subgraph EventLogsFinders["Event Logs & Processing"]
      direction TB
      style EventLogsFinders fill:#F8F8F8,stroke:#85C4E4,stroke-width:3,rx:18,ry:18

      EventLogCore[Event Log Model & State\nCentralizes change events, cursor & gap tracking]:::core
      EventProcessing[Event Processing Cursor\nProcesses & advances event state]:::core
      EventServices[Event Services\nEvent creation/consumption, transform/persist]:::core
      EventUtilities[Event Util/Finders\nSupport batch ops, metrics, infra checks]:::support
      EventDataStructs[Event Structures & Migrations\nState cols, event schema]:::data
    end

    %% 3. Repository Syncing & Verification Sync orchestration, audits, QA
    subgraph RepoSyncVerification["Repository Syncing & Verification"]
      direction TB
      style RepoSyncVerification fill:#F8F8F8,stroke:#7ac3e8,stroke-width:3,rx:18,ry:18

      Scheduler[Sync Job Scheduler\nOrchestrates, dispatches sync/verify tasks]:::core
      SyncWorkers[Sync & Migration Workers\nPerforms repo, attachment, storage sync]:::core
      RepoServices[Repo Sync Services\nImplements sync/lease/cache logic for repos]:::core
      LoggingHelpers[Logging/Audit Helpers\nAdds context to sync/verify ops]:::support
      SyncDataStructs[Sync Data & Migration\nSync state indices/structs in DB]:::data
      QAEndToEnd[QA: Sync/Verify QA Flows\nEnd-to-end replication test specs]:::support
      QASupport[QA UI Support\nWait-for-replication, UI state wrapper]:::support
    end


    %% ===================================================
    %% SHARED/DOMAIN-SPANNING DATA STRUCTURES & CONCEPTS
    %% ===================================================

    subgraph CoreDomainData["Core Domain Data Structures & Concepts"]
      direction TB
      style CoreDomainData fill:#F8F8F8,stroke:#A1DBA1,stroke-width:2,rx:18,ry:18
      RepoRegistry[Repository Registry Entry\nTracks per-repo sync/verify status]:::data
      EventEntry[Replication Event Entry\nNormalized change/event record]:::data
      NodeStatusStruct[Node Status Record\nSync/health/state with timestamps]:::data
    end

    %% ==========================================
    %% RELATIONSHIPS / INTERACTIONS
    %% ==========================================

    %% -- Geo Nodes & Registries <-> Event Logs & Finders
    GeoNodeCore -- Contains --> Registries
    Registries -- Maps per-resource --> RepoRegistry
    Registries -- Emits change events --> EventLogCore
    EventLogCore -- Applies to --> Registries
    RegistryStates -- Statuses for --> Registries
    RegistryFinders -- Queries --> Registries
    NodeServices -- Orchestrates sync/calls on --> Registries
    RegistryWorkers -- Invokes NodeServices/batches --> Registries
    Replicator -- Manages resource-level --> Registries

    %% -- Event Logs & Finders <-> Repository Syncing & Verification
    RepoServices -- Triggers events on --> EventLogCore
    EventServices -- Consumes/dispatches events for --> RepoServices
    EventProcessing -- Advances sync cursor for --> RepoSyncVerification
    EventUtilities -- Provides infra helpers for --> Scheduler
    EventLogCore -- Drives state for --> SyncWorkers

    %% -- Repository Syncing & Verification <-> Geo Nodes & Registries
    Scheduler -- Delegates jobs for --> SyncWorkers
    SyncWorkers -- Update state in --> RepoRegistry
    SyncWorkers -- Integrate with --> LoggingHelpers
    RepoServices -- Wraps sync process for --> SyncWorkers
    SyncDataStructs -- Provides migration/state for --> RepoRegistry
    QAEndToEnd -- Exercises sync across --> GeoNodeCore & Registries
    QASupport -- Enables wait-state checks via --> NodeStatusStruct
    QASupport -- UI-wrapped confirmation on --> RepoRegistry

    %% -- Core Data Structures & Cross-domain
    Registries -- Aggregates to --> RepoRegistry
    SyncWorkers -- Operate on --> RepoRegistry
    Replicator -- Orchestrates copy/verify for --> RepoRegistry
    EventLogCore -- Stores as --> EventEntry
    EventProcessing -- Advances position for --> EventEntry
    Scheduler -- Schedules based on --> NodeStatusStruct
    GeoNodeStatus -- Updates --> NodeStatusStruct
    NodeServices -- Health/status via --> NodeStatusStruct

    %% -- Shared Abstractions & Patterns
    Replicator -- Central abstraction bridging subdomains --> RepoServices
    RegistryFinders -- Aggregates finders for operational views --> Registries

    %% -- Edge Connections between groups for clarity
    GeoNodesRegistries --- CoreDomainData
    RepoSyncVerification --- CoreDomainData
    EventLogsFinders --- CoreDomainData

    %% --------------------------------------------------------
    %% LEGEND for visual clarity (stub nodes to show colors)
    %% --------------------------------------------------------
    subgraph Legend["Legend"]
      direction TB
      style Legend fill:#F8F8F8,stroke:#C0C0C0,stroke-width:1,rx:12,ry:12
      LCore["Core Domain":::core]
      LSupport["Supporting/Utility":::support]
      LData["Data Structure":::data]
      LSetup["Initialization/DB":::setup]
    end
  end

  %% ==================================
  %% CLASS DEFINITIONS FOR COLOR/STYLE
  %% ==================================
  classDef core fill:#D4F1F9,stroke:#8AC6D1,stroke-width:2,rx:12,ry:12,color:#222;
  classDef support fill:#FFF8DC,stroke:#FFE69A,stroke-width:2,rx:12,ry:12,color:#444;
  classDef data fill:#E0F8E0,stroke:#93D65E,stroke-width:2,rx:12,ry:12,color:#222;
  classDef setup fill:#E6E6FA,stroke:#B7B7E2,stroke-width:2,rx:12,ry:12,color:#222;

  %% Node to class mapping
  class GeoNodeCore,GeoNodeStatus,Registries,Replicator,NodeServices,RepoServices,Scheduler,SyncWorkers,EventLogCore,EventProcessing,EventServices core
  class Migrations,SyncDataStructs,EventDataStructs setup
  class RegistryStates,RepoRegistry,EventEntry,NodeStatusStruct data
  class RegistryFinders,RegistryWorkers,LoggingHelpers,QAEndToEnd,QASupport,EventUtilities,support
```