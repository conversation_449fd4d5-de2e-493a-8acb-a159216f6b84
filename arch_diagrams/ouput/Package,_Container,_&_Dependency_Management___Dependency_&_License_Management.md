```mermaid
flowchart TD
  %% =========================
  %% Domain: Package, Container, & Dependency Management -> Dependency & License Management (Level 1)
  %% Combined Architecture Overview
  %% Vertical Layout
  %% COLOR LEGEND: 
  %%  - Core domain:        #D4F1F9
  %%  - Supporting/utility: #FFF8DC
  %%  - Data structure:     #E0F8E0
  %%  - Error handling:     #FFE4E1
  %%  - Initialization:     #E6E6FA
  %%  - Groupings:          #F8F8F8 (pastel border)
  %%  - New components:     pastel palette
  %%  - All nodes: rounded-rectangle

  %% ====== DOMAIN LAYER ======
  subgraph DOMAIN["Dependency & License Management" ]
    direction TB
    style DOMAIN fill:#F8F8F8,stroke:#9BC9E2,stroke-width:4,rounded-corners

    %% CORE ABSTRACTIONS

    LicenseComplianceCore["License Compliance"]:::core
    SBOMCore["SBOM: Components, Occurrences, DepGraph"]:::core
    SBOMExportCore["SBOM Reports & Exporters"]:::core

    %% HIGH-LEVEL LOGICAL FLOW vertical
    LicenseComplianceCore 
    SBOMCore
    SBOMExportCore
  end

  classDef core fill:#D4F1F9,stroke:#94b6db,stroke-width:2,rx:12,ry:12

  %% ===== CORE DOMAIN SPANNING STRUCTURES/PATTERNS =====
  subgraph DATAMODELS["Domain-Spanning Data Structures & Models"]
    direction TB
    style DATAMODELS fill:#F8F8F8,stroke:#B1E8D5,stroke-width:2.5,rounded-corners

    Package["Package"]:::data
    PackageVersion["PackageVersion"]:::data
    LicenseModel["License"]:::data
    SBOMComponent["SBOM Component"]:::data
    SBOMOccurrence["SBOM Occurrence"]:::data
    DependencyEdge["Dependency Graph Edge"]:::data
    SBOMReport["SBOM Report"]:::data
    DependencyListExport["DependencyListExport"]:::data
  end

  classDef data fill:#E0F8E0,stroke:#7DD694,stroke-width:1.7,rx:12,ry:12

  %% ===== ERROR & POLICY INFRA =====
  subgraph POLICIES["Policy & Error Infrastructure"]
    direction TB
    style POLICIES fill:#F8F8F8,stroke:#FFB8A1,stroke-width:1.8,rounded-corners
    LicensePolicy["License Policy"]:::error
    DependencyPolicy["Dependency Policy"]:::error
    ExportError["Export/Parser Error"]:::error
    ComplianceError["Compliance Error"]:::error
  end

  classDef error fill:#FFE4E1,stroke:#FFA7A7,stroke-width:1.6,rx:12,ry:12

  %% ==== INGESTION/PARSING/WORKFLOWS ====
  subgraph INGESTION["Ingestion, Parsing & Synchronization"]
    direction TB
    style INGESTION fill:#F8F8F8,stroke:#AAF4E6,stroke-width:2,rounded-corners

    MetadataIngestion["Package Metadata Ingestion Service"]:::init
    LicenseSync["License Synchronization Worker"]:::init
    SBOMIngestWorkflow["SBOM Ingestion Workflow"]:::init
    ExportIngestWorkflow["Export/Report Ingestion"]:::init
  end

  classDef init fill:#E6E6FA,stroke:#A999D9,stroke-width:1.8,rx:12,ry:12

  %% ==== COMPLIANCE/ANALYSIS/SCANNING ====
  subgraph ANALYSIS["Compliance, Scanning & Analysis"]
    direction TB
    style ANALYSIS fill:#F8F8F8,stroke:#AAD9E6,stroke-width:2.1,rounded-corners

    ComplianceCheck["License Compliance Checker"]:::core
    LicenseScanService["License Scanning & Comparison"]:::core
    SBOMScanService["SBOM Parsing/Validation"]:::core
    DepGraphAnalyzer["Dependency Graph Analyzer"]:::support
  end

  classDef support fill:#FFF8DC,stroke:#EEDD82,stroke-width:1.5,rx:12,ry:12

  %% ==== SERIALIZATION/EXPORT/LISTING ====
  subgraph EXPORT["Report Serialization, Exports & SBOM Generation"]
    direction TB
    style EXPORT fill:#F8F8F8,stroke:#8CC7E6,stroke-width:2.1,rounded-corners

    SBOMReportExport["SBOM Export Services\nJSON, CycloneDX, CSV, Array"]:::core
    DependencyExportService["Dependency List Export Service"]:::core
    ExportMailer["Export Completion Emailer"]:::support
  end

  %% ==== UTILITIES & INTEGRATION ====
  subgraph UTILS["Support Utilities & Integration Points"]
    direction TB
    style UTILS fill:#F8F8F8,stroke:#E8DCB1,stroke-width:2,rounded-corners

    SPDXUtils["SPDX License Utilities"]:::support
    PurlUtils["PURL Utilities"]:::support
    UpgradeUtils["Upgrade Planning Utils"]:::support
    LocationEntity["Dependency Location Entity"]:::data
  end

  %% ========= LOGICAL RELATIONSHIPS & COLLABORATION =============

  %% == High-level vertical flow ==
  DOMAIN --> DATAMODELS
  DOMAIN --> INGESTION
  DOMAIN --> ANALYSIS
  DOMAIN --> EXPORT
  DOMAIN --> UTILS
  DOMAIN --> POLICIES

  %% == Ingestion routes package & license data into core structures ==
  MetadataIngestion --"extracts/updates"--> Package
  MetadataIngestion -->|license info| LicenseModel
  MetadataIngestion -->|syncs| LicenseSync
  MetadataIngestion -->|feeds| SBOMComponent

  LicenseSync --"ensures up-to-date"--> LicenseModel

  SBOMIngestWorkflow --"populates"--> SBOMComponent
  SBOMIngestWorkflow --"populates"--> SBOMOccurrence
  SBOMIngestWorkflow --"populates"--> DependencyEdge
  SBOMIngestWorkflow --"uses"--> PurlUtils
  SBOMIngestWorkflow --"uses"--> SPDXUtils

  ExportIngestWorkflow --"integrates report data"--> SBOMReport
  ExportIngestWorkflow --"links to"--> Package

  %% == From ingestion to analysis/scanning ==
  PackageVersion --"scanned by"--> ComplianceCheck
  LicenseModel --"checked by"--> ComplianceCheck
  SBOMOccurrence --"analyzed by"--> SBOMScanService
  SBOMComponent --"looked up by"--> LicenseScanService
  DependencyEdge --"traversed by"--> DepGraphAnalyzer

  %% == Analysis generates or influences exports ==
  ComplianceCheck --"feeds results to"--> SBOMReportExport
  LicenseScanService --"generates"--> SBOMReport
  SBOMScanService --"validates"--> SBOMReport
  DepGraphAnalyzer --"describes dependencies in"--> SBOMReport

  %% == Exporting & Reporting ==
  SBOMReportExport --"outputs"--> SBOMReport
  SBOMReportExport --"generates"--> DependencyListExport
  SBOMReportExport --"notifies via"--> ExportMailer
  DependencyExportService --"wraps"--> DependencyListExport
  DependencyExportService --"interacts with"--> ExportMailer

  %% == Policy & Error usage ==
  ComplianceCheck --"references"--> LicensePolicy
  DepGraphAnalyzer --"references"--> DependencyPolicy
  SBOMReportExport --"handles errors"--> ExportError
  ComplianceCheck --"handles"--> ComplianceError

  %% == Utilities integration ==
  SPDXUtils --"normalizes"--> LicenseModel
  PurlUtils --"parses"--> SBOMComponent
  LocationEntity --"maps"--> DependencyEdge
  UpgradeUtils --"guides"--> MetadataIngestion

  %% == Data sharing between subdomains ==
  LicenseModel --"referenced by"--> SBOMComponent
  SBOMComponent --"embedded in"--> SBOMOccurrence
  SBOMOccurrence --"maps to"--> DependencyEdge
  SBOMReport --"summarizes"--> {SBOMComponent, LicenseModel, DependencyEdge}
  SBOMReport --"input to"--> DependencyListExport

  %% == Exporters consume from SBOM subdomain ==
  SBOMCore --"underlies"--> SBOMReportExport
  SBOMCore --"feeds"--> DependencyExportService

  %% == SBOM Exporters utilize occurrence/component/location aggregation ==
  LocationEntity --"attached to"--> SBOMReportExport

  %% == Policies influence ingestion and reporting ==
  LicensePolicy --"configures"--> ComplianceCheck
  DependencyPolicy --"affects"--> DepGraphAnalyzer
  ExportError --"surfaced by"--> ExportIngestWorkflow

  %% == Cross-subdomain edge examples ==
  LicenseModel --"reconciled with"--> SPDXUtils
  SBOMComponent --"specifies PURL with"--> PurlUtils
  SBOMReport --"enables downstream"--> ExportMailer
  
  %% == Vertical logical flow aligning subdomain roles ==
  LicenseComplianceCore -->|tracks & enforces| LicensePolicy
  LicenseComplianceCore --> ComplianceCheck

  SBOMCore -->|structures| SBOMComponent
  SBOMCore --> SBOMOccurrence
  SBOMCore --> DependencyEdge

  SBOMExportCore -->|outputs| SBOMReport
  SBOMExportCore --> DependencyListExport
  SBOMExportCore --> ExportMailer

  %% == Relationships between subdomains vertical collaboration ==
  LicenseComplianceCore --"enforces license rules and maps to"--> SBOMCore
  SBOMCore --"provides dependency graph and components to"--> SBOMExportCore
  SBOMExportCore --"serializes, exports, and notifies on"--> {SBOMReport, DependencyListExport, ExportMailer}
  UTILS --"support and enable"--> {LicenseComplianceCore, SBOMCore, SBOMExportCore}

  %% == Styling for grouped subgraph borders ==
  style DATAMODELS stroke:#B1E8D5,stroke-width:1.4
  style POLICIES stroke:#FFB8A1,stroke-width:1.2
  style INGESTION stroke:#AAF4E6,stroke-width:1.4
  style ANALYSIS stroke:#AAD9E6,stroke-width:1.3
  style EXPORT stroke:#8CC7E6,stroke-width:1.4
  style UTILS stroke:#E8DCB1,stroke-width:1.2

  %% Additional abstraction classes for clean coloring/spaces
  classDef highlight fill:#FAFAFC,stroke:#AEEDF3,stroke-width:1.5,rx:12,ry:12
```