```mermaid
flowchart TD
  %% COLOR AND STYLE DEFINITIONS
  classDef core fill:#D4F1F9,stroke:#85C7D4,stroke-width:2px,rx:15,ry:15,color:#2A4255
  classDef support fill:#FFF8DC,stroke:#FFD700,stroke-width:2px,rx:15,ry:15,color:#5E573C
  classDef data fill:#E0F8E0,stroke:#70C48D,stroke-width:2px,rx:15,ry:15,color:#314D3F
  classDef error fill:#FFE4E1,stroke:#EE9999,stroke-width:2px,rx:15,ry:15,color:#944E56
  classDef init fill:#E6E6FA,stroke:#B0A8E6,stroke-width:2px,rx:15,ry:15,color:#433D57
  classDef group fill:#F8F8F8,stroke:#C3CEDA,stroke-width:3px,rx:22,ry:22,color:#526475

  %% --- TOP LEVEL DOMAIN ---
  subgraph gr0[Geo Replication, Disaster Recovery & Distributed Systems]
    direction TB
    geo_repl_core[Geo Replication, DR & Distributed\nDomain Core Logic]:::core
  end

  %% --- 1. BACKUP & RESTORE SUBDOMAIN ---
  subgraph gr1[Backup & Restore]
    direction TB
    backup_restore_core[Backup & Restore Core Engine]:::core
    backup_options_struct[Backup Options Structure]:::data
    backup_metadata_struct[Backup Metadata Structure]:::data
    restore_logic[Restore Orchestration & Logic]:::core

    %% CLUSTER METADATA/STATE
    global_metadata[Distributed System Metadata\nState, Topology, Checks]:::data

    %% Interoperation
    geo_repl_core -->|uses backup & restore| backup_restore_core

    %% Core relationships
    backup_restore_core --> backup_options_struct
    backup_restore_core --> backup_metadata_struct
    backup_restore_core --> restore_logic
    backup_metadata_struct -.-> global_metadata

  end

  %% --- 1a. BACKUP HELPERS & CLI SUBDOMAIN ---
  subgraph gr1a[Backup Helpers & CLI Subdomain]
    direction TB

    subgraph gr1a1[Backup Orchestration]
      backup_manager_node[Backup Manager]:::core
      backup_logger_node[Backup Logger]:::support
    end

    subgraph gr1a2[Abstractions & Extension Points]
      task_abstraction[Backup Task Abstraction]:::core
      target_abstraction[Backup Target Abstraction]:::core
    end

    subgraph gr1a3[CLI Interface & Execution]
      cli_entry[CLI Entrypoint & Executor]:::core
      cli_command_interface[CLI Command Layer]:::support
      cli_context_loader[Context Loader]:::init
      cli_services[CLI Services]:::support
    end

    subgraph gr1a4[Domain Data Structures]
      task_options[Backup Options]:::data
      task_metadata[Backup Metadata]:::data
      db_config[DB Configuration]:::data
    end

    subgraph gr1a5[Backup Tasks/Targets Examples]
      db_task[Database Task]:::core
      repos_task[Repository Task]:::core
      files_target[Files Target]:::core
      db_target[Database Target]:::core
    end

    subgraph gr1a6[Restore Coordination]
      unpacker[Restore Unpacker]:::core
      restore_pre[Restore Preconditions]:::core
    end

    subgraph gr1a7[Helpers & Utilities]
      compression_util[Compression Utilities]:::support
      remote_storage_util[Remote Storage]:::support
      shell_util[Shell Abstractions]:::support
    end

    subgraph gr1a8[Error Handling]
      domain_errors[Domain Error Abstractions]:::error
    end

    backup_manager_node --> backup_logger_node
    backup_manager_node -->|orchestrates| task_abstraction
    backup_manager_node -->|orchestrates| target_abstraction
    backup_manager_node -->|flows options| task_options
    backup_manager_node -->|records| task_metadata

    cli_entry --> cli_command_interface
    cli_entry --> cli_context_loader
    cli_entry --> cli_services

    task_abstraction -->|implemented by| db_task
    task_abstraction -->|implemented by| repos_task

    target_abstraction -->|implemented by| files_target
    target_abstraction -->|implemented by| db_target

    db_task --> db_target
    repos_task --> files_target

    task_options --> db_task
    task_options --> repos_task
    db_config --> db_target

    restore_pre --> unpacker
    restore_pre --> task_metadata

    remote_storage_util --> backup_manager_node
    compression_util --> db_task
    shell_util --> compression_util

    domain_errors -.-> db_task
    domain_errors -.-> db_target
    domain_errors -.-> cli_entry
    domain_errors -.-> cli_services

    backup_metadata_struct -.-> task_metadata
    backup_options_struct -.-> task_options

    backup_restore_core --> backup_manager_node
    backup_restore_core --> cli_entry
    backup_restore_core --> task_abstraction
    backup_restore_core --> target_abstraction
    backup_restore_core --> domain_errors
    backup_restore_core --> unpacker
    backup_restore_core --> restore_pre

  end

  %% --- INTERCONNECTIONS BETWEEN GROUPS ---
  geo_repl_core --> backup_restore_core
  backup_restore_core --> gr1a
  backup_options_struct -->|shared config pattern| gr1a4
  backup_metadata_struct -->|maps to| gr1a4
  restore_logic -->|delegates| gr1a6

  %% --- HIGH-LEVEL PATTERNS AND ABSTRACT FLOWS ---
  %% Pattern: Orchestration, Abstraction, Data Serialization

  %% Orchestration flow
  geo_repl_core -->|triggers| backup_manager_node
  backup_manager_node -->|delegates tasks| task_abstraction
  cli_entry -->|executes| backup_manager_node
  restore_logic -->|executes| unpacker
  restore_logic -->|runs| restore_pre
  unpacker -->|instantiates tasks| task_abstraction

  %% Data flows
  backup_options_struct --> task_options
  backup_metadata_struct --> task_metadata
  db_config --> db_target
  global_metadata --> backup_restore_core
  global_metadata --> gr1a4

  %% Error Propagation
  domain_errors --> backup_manager_node
  domain_errors --> restore_logic
  domain_errors --> cli_entry

  %% Data Serialization/Deserialization
  backup_metadata_struct -->|serializes to| task_metadata
  gr1a4 -->|serializes/deserializes| task_metadata

  %% CLUSTER CONCEPT
  geo_repl_core -->|syncs cluster state| global_metadata

  %% Key Legend Nodes for concept clarity (grouped, lightly rendered)
  subgraph grK[Key Domain Patterns]
    direction TB
    orchestration_pat[Orchestration Pattern]:::support
    abstraction_pat[Abstraction Pattern]:::support
    data_serialization_pat[Data Serialization/Deserialization]:::data
  end
  orchestration_pat -.-> backup_manager_node
  orchestration_pat -.-> restore_logic
  abstraction_pat -.-> task_abstraction
  abstraction_pat -.-> target_abstraction
  data_serialization_pat -.-> backup_metadata_struct
  data_serialization_pat -.-> gr1a4

  %% CLASSES
  class geo_repl_core,backup_restore_core,backup_manager_node,task_abstraction,target_abstraction,db_task,repos_task,files_target,db_target,restore_logic,unpacker,restore_pre core;
  class backup_logger_node,cli_command_interface,cli_services,compression_util,remote_storage_util,shell_util,orchestration_pat,abstraction_pat support;
  class backup_options_struct,backup_metadata_struct,task_options,task_metadata,db_config,global_metadata,data_serialization_pat data;
  class domain_errors error;
  class cli_entry,cli_context_loader init;
  class gr0,gr1,gr1a,gr1a1,gr1a2,gr1a3,gr1a4,gr1a5,gr1a6,gr1a7,gr1a8,grK group;
```