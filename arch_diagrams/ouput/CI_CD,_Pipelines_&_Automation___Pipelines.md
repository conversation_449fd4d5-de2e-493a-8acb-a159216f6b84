```mermaid
flowchart TB
%% ====== DOMAIN ROOT ======
subgraph D1["CI/CD, Pipelines & Automation → Pipelines" ]
direction TB
style D1 fill:#F8F8F8,stroke:#7EC8E3,stroke-width:4,rx:24

%% ====================== CORE DOMAIN ABSTRACTIONS ======================
subgraph CORE_ABSTRACTIONS["Pipeline Domain - Core Concepts"]
direction TB
style CORE_ABSTRACTIONS fill:#F8F8F8,stroke:#7EC8E3,stroke-width:2,rx:16
PIPELINE[/"Pipeline"/]:::Core
STAGES["Stages"]:::Core
JOBS["Jobs"]:::Core
BUILDS["Builds"]:::Core
VARIABLES["Variables & Secrets"]:::Core
CONFIG["Config & Definitions"]:::Core
TRIGGERS["Schedules & Triggers"]:::Core
ANALYTICS["Pipeline Analytics & Metrics"]:::Core
SECURITY["Security & Compliance"]:::Core

PIPELINE --> STAGES
PIPELINE --> VARIABLES
PIPELINE --> CONFIG
PIPELINE --> TRIGGERS
PIPELINE --> ANALYTICS
PIPELINE --> SECURITY
STAGES --> JOBS
JOBS --> BUILDS
end

%% ===========================================================
%% ========== SUBDOMAIN: PIPELINE CONFIGURATION ===============
%% ===========================================================
subgraph S1["Pipeline Configuration & Definitions"]
direction TB
style S1 fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rx:17
S1_CONFIG_CORE["Config DSL & Loader\nyaml, entries, normalizer"]:::Core
S1_CONFIG_INPUTS["Pipeline Inputs,\nInterpolation, Functions"]:::DataStructure
S1_CONFIG_NORMALIZATION["Config Normalization & Strategy"]:::DataStructure
S1_EXPR_RULES["Rules & Expression Eval"]:::Core
S1_STAGES_STRUCTURE["Stages Structure Mgmt"]:::Core
S1_VALIDATION["Linting & Validation"]:::Init
S1_TAGS["Tags & Tag Processing"]:::DataStructure
S1_EXT_INCLUDES["External Includes/Mapping"]:::Utility

S1_CONFIG_CORE -->|composes| S1_CONFIG_INPUTS
S1_CONFIG_CORE -->|normalizes with| S1_CONFIG_NORMALIZATION
S1_CONFIG_CORE -->|uses| S1_TAGS
S1_CONFIG_CORE -->|merges includes| S1_EXT_INCLUDES
S1_CONFIG_CORE -->|validates rules| S1_EXPR_RULES 
S1_CONFIG_CORE -->|produces| S1_STAGES_STRUCTURE
S1_CONFIG_CORE -->|is linted by| S1_VALIDATION
end

%% ===========================================================
%% ========= SUBDOMAIN: PIPELINE VARIABLES & SECRETS =========
%% ===========================================================
subgraph S2["Pipeline Variables & Secrets"]
direction TB
style S2 fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rx:17

S2_VARIABLE_MODELS["Variable Models\nProject/Group/Instance/Pipeline"]:::Core
S2_BUILDERS["Variable Builder Pattern"]:::Utility
S2_COLLECTIONS["Variable Collections"]:::DataStructure
S2_SERVICE_MGMT["Variable Management Services"]:::Core
S2_DOWNSTREAM["Downstream Variable Generation"]:::Utility
S2_PRESENTATION["GraphQL & Presenters"]:::Utility
S2_INIT_POLICY["Init, Policy, Setting"]:::Init

S2_VARIABLE_MODELS -->|composed via| S2_BUILDERS
S2_BUILDERS -->|outputs| S2_COLLECTIONS
S2_COLLECTIONS -->|consumed by| S2_SERVICE_MGMT
S2_DOWNSTREAM -->|generates for| S2_COLLECTIONS
S2_VARIABLE_MODELS -->|exposed via| S2_PRESENTATION
S2_SERVICE_MGMT -->|constrained by| S2_INIT_POLICY
end

%% ===========================================================
%% ========= SUBDOMAIN: PIPELINE SCHEDULES & TRIGGERS ========
%% ===========================================================
subgraph S3["Pipeline Schedules & Triggers"]
direction TB
style S3 fill:#F8F8F8,stroke:#7EC8E3,stroke-width:2,rx:17
S3_SCHEDULE_MODELS["Schedule & Trigger Models"]:::Core
S3_SCHED_SERVICES["Schedule Services"]:::Utility
S3_TRIGGER_SERVICES["Trigger Services"]:::Utility
S3_CRON_SUPPORT["Cron Schedulable\nSupport"]:::Utility
S3_REST_API["REST API & GraphQL"]:::Core
S3_POLICIES["Freeze Periods & Policies"]:::Error
S3_DATA_TYPES["Schedule Types/Enums"]:::DataStructure

S3_SCHEDULE_MODELS -->|uses| S3_DATA_TYPES
S3_SCHEDULE_MODELS -->|managed by| S3_SCHED_SERVICES
S3_SCHEDULE_MODELS -->|triggered by| S3_TRIGGER_SERVICES
S3_SCHEDULE_MODELS -->|scheduled via| S3_CRON_SUPPORT
S3_SCHEDULE_MODELS -->|exposed via| S3_REST_API
S3_SCHEDULE_MODELS -->|restricted by| S3_POLICIES
end

%% ===========================================================
%% ========== SUBDOMAIN: PIPELINE CREATION & EXECUTION =======
%% ===========================================================
subgraph S4["Pipeline Creation & Execution"]
direction TB
style S4 fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rx:17
S4_PIPELINES_CORE["Pipeline, Build, Stage Models"]:::Core
S4_PIPELINE_CREATION["Pipeline Creation Chain"]:::Core
S4_PIPELINE_EXEC["Pipeline Processing & Execution"]:::Core
S4_SERVICES["Pipeline Action Services\ncancel, retry, destroy, etc."]:::Core
S4_WORKERS["Execution Workers"]:::Init
S4_PRESENTERS["Presenters / Serializers"]:::Utility
S4_API_ENTRY["API Controllers / GraphQL"]:::Init

S4_PIPELINE_CREATION -->|builds| S4_PIPELINES_CORE
S4_PIPELINES_CORE -->|processed by| S4_PIPELINE_EXEC
S4_PIPELINE_EXEC -->|driven by| S4_SERVICES
S4_SERVICES -->|enqueue| S4_WORKERS
S4_PIPELINES_CORE -->|presented by| S4_PRESENTERS
S4_PIPELINES_CORE -->|accessed via| S4_API_ENTRY
end

%% ===========================================================
%% =============== SUBDOMAIN: PIPELINE ANALYTICS =============
%% ===========================================================
subgraph S5["Pipeline Analytics & Metrics"]
direction TB
style S5 fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rx:17
S5_ANALYTICS_MODELS["Analytics Models\nDaily Reports etc."]:::Core
S5_ANALYTICS_SERVICES["Aggregation & Comparison Services"]:::Core
S5_ARTIFACTS["Coverage & Artifact Reporting"]:::Utility
S5_GRAPHQL["GraphQL/Finders"]:::Utility
S5_BACKGROUND["Workers, Scheduling"]:::Init
S5_METRICS["Charts & Metrics\nhistograms, stats"]:::DataStructure

S5_ANALYTICS_SERVICES -->|aggregates| S5_ANALYTICS_MODELS
S5_ANALYTICS_SERVICES -->|analyzes| S5_ARTIFACTS
S5_ANALYTICS_MODELS -->|exposed via| S5_GRAPHQL
S5_ARTIFACTS -->|processed via| S5_BACKGROUND
S5_ANALYTICS_MODELS -->|feeds| S5_METRICS
end

%% ===========================================================
%% =============== SUBDOMAIN: SECURITY & COMPLIANCE ==========
%% ===========================================================
subgraph S6["Pipeline Security & Compliance"]
direction TB
style S6 fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rx:17
S6_JOB_TOKEN["Job Token Scope & Policy"]:::Core
S6_SECURITY_SCAN["Security Scanning & Results"]:::Core
S6_FINDINGS_DS["Scan Result Data Structures"]:::DataStructure
S6_SCAN_POLICY["Pipeline Policy & Compliance"]:::Core
S6_QUOTA_ENV["Quota & Env Controls"]:::Utility
S6_SECURE_FILES["Secure File Management"]:::Core

S6_JOB_TOKEN -->|enforces| S6_SCAN_POLICY
S6_SCAN_POLICY -->|controls| S6_SECURITY_SCAN
S6_SECURITY_SCAN -->|produces| S6_FINDINGS_DS
S6_SECURITY_SCAN -->|restricted by| S6_QUOTA_ENV
S6_SECURITY_SCAN -->|cross-links| S6_SECURE_FILES
end

%% =============== LOGICAL RELATIONSHIPS ACROSS SUBDOMAINS =============

%% Core pipeline flows depend on config, variables, and triggers
CONFIG -.->|defines structure| PIPELINE
CONFIG -->|resolved by| S1_CONFIG_CORE
VARIABLES -.->|used by| PIPELINE
VARIABLES -->|provided & resolved by| S2_BUILDERS
TRIGGERS -.->|initiates| PIPELINE
TRIGGERS -->|scheduled by| S3_SCHED_SERVICES
ANALYTICS -.->|analyzes| PIPELINE
SECURITY -.->|enforces compliance| PIPELINE

%% Creation & execution orchestrates the pipeline based on config, vars, triggers
S4_PIPELINE_CREATION -->|evaluates| S1_CONFIG_CORE
S4_PIPELINE_CREATION -->|propagates| S2_COLLECTIONS
S4_PIPELINE_CREATION -->|responds to| S3_SCHED_SERVICES
S4_PIPELINE_CREATION -->|writes results to| S5_ANALYTICS_MODELS
S4_PIPELINE_CREATION -->|enforces| S6_SCAN_POLICY
S4_PIPELINE_CREATION -->|executes scan jobs| S6_SECURITY_SCAN

%% Variables & Secrets loaded at config expansion
S1_CONFIG_CORE -->|injects vars from| S2_COLLECTIONS
S2_COLLECTIONS -->|feeds| S1_CONFIG_INPUTS

%% Schedules and triggers interact with pipeline creation
S3_SCHED_SERVICES -->|schedules| S4_PIPELINE_CREATION
S3_TRIGGER_SERVICES -->|invokes| S4_PIPELINE_CREATION

%% Analytics & Metrics depend on execution results
S4_PIPELINES_CORE -->|reports to| S5_ANALYTICS_MODELS
S4_PIPELINES_CORE -->|emits coverage/artifacts| S5_ARTIFACTS
S5_ANALYTICS_MODELS -->|exposed to| S5_METRICS

%% Security flows through all stages
S6_SECURITY_SCAN -.->|verifies| S4_PIPELINE_CREATION
S6_JOB_TOKEN -.->|restricts| S4_PIPELINE_CREATION
S6_SCAN_POLICY -.->|governs| S1_CONFIG_CORE
S6_QUOTA_ENV -.->|restricts| S4_PIPELINE_EXEC
S6_SECURE_FILES -.->|binds to| S1_CONFIG_CORE

%% Shared domain data structures
SHARED_PIPELINE_DS["Pipeline Domain Data Structures"]:::DataStructure
SHARED_RESULTS["Pipeline Results/Reports Structures"]:::DataStructure
SHARED_VARIABLES_DS["Pipeline Variables DS"]:::DataStructure

PIPELINE -->|backed by| SHARED_PIPELINE_DS
S4_PIPELINES_CORE -->|uses| SHARED_PIPELINE_DS
S1_CONFIG_CORE -->|outputs structure to| SHARED_PIPELINE_DS
S2_COLLECTIONS -->|feeds| SHARED_VARIABLES_DS
S4_PIPELINE_CREATION -->|feeds| SHARED_RESULTS
S5_METRICS -->|reads| SHARED_RESULTS
S6_FINDINGS_DS -->|shared with| SHARED_RESULTS

%% ====== KEY PATTERN & ABSTRACTION NODES =======
PATTERN_CHAIN["Chain of Responsibility Pipeline Creation"]:::Utility
PATTERN_BUILDER["Builder Pattern Variable & Config"]:::Utility
PATTERN_POLICY["Policy/Strategy Schedule, Security, Compliance"]:::Utility
PATTERN_DS_AGG["Aggregations & Comparisons"]:::Utility

S1_CONFIG_CORE -.->|uses| PATTERN_BUILDER
S2_BUILDERS -.->|is| PATTERN_BUILDER
S3_SCHED_SERVICES -.->|uses| PATTERN_POLICY
S6_JOB_TOKEN -.->|uses| PATTERN_POLICY
S4_PIPELINE_CREATION -.->|follows| PATTERN_CHAIN
S5_ANALYTICS_SERVICES -.->|uses| PATTERN_DS_AGG

%% =================== CLASS DEFINITIONS ===================
classDef Core fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:14
classDef DataStructure fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rx:14
classDef Utility fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rx:14
classDef Error fill:#FFE4E1,stroke:#FFE4E1,stroke-width:2,rx:14
classDef Init fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rx:14

class PIPELINE,STAGES,JOBS,BUILDS,VARIABLES,CONFIG,TRIGGERS,ANALYTICS,SECURITY Core
class S1_CONFIG_CORE,S1_EXPR_RULES,S1_STAGES_STRUCTURE Core
class S4_PIPELINES_CORE,S4_PIPELINE_CREATION,S4_PIPELINE_EXEC,S4_SERVICES Core
class S2_VARIABLE_MODELS,S2_SERVICE_MGMT Core
class S3_SCHEDULE_MODELS Core
class S5_ANALYTICS_MODELS,S5_ANALYTICS_SERVICES Core
class S6_JOB_TOKEN,S6_SECURITY_SCAN,S6_SCAN_POLICY,S6_SECURE_FILES Core

class S1_CONFIG_INPUTS,S1_CONFIG_NORMALIZATION,S1_TAGS DataStructure
class S2_COLLECTIONS,S2_BUILDERS,S2_DOWNSTREAM DataStructure
class SHARED_PIPELINE_DS,SHARED_RESULTS,SHARED_VARIABLES_DS DataStructure
class S5_METRICS,S6_FINDINGS_DS DataStructure

class S1_EXT_INCLUDES Utility
class S3_SCHED_SERVICES,S3_TRIGGER_SERVICES,S3_CRON_SUPPORT Utility
class S4_PRESENTERS,S4_SERVICES,S4_PIPELINE_EXEC Utility
class S2_PRESENTATION,S2_BUILDERS,S2_DOWNSTREAM Utility
class S5_ARTIFACTS,S5_GRAPHQL Utility
class S6_QUOTA_ENV Utility

class S1_VALIDATION,S4_WORKERS,S4_API_ENTRY,S5_BACKGROUND,S2_INIT_POLICY Init

class S3_POLICIES Error

class PATTERN_CHAIN,PATTERN_BUILDER,PATTERN_POLICY,PATTERN_DS_AGG Utility

end
```