```mermaid
flowchart TB
  %% --- STYLING CLASSES ---
  %% Core domain: pastel blue
  classDef core fill:#D4F1F9,stroke:#A0D6E5,stroke-width:2px,color:#144E6C,rx:12,ry:12;
  %% Supporting/utility: pastel yellow
  classDef util fill:#FFF8DC,stroke:#FFD700,stroke-width:2px,color:#75691D,rx:12,ry:12;
  %% Data structure: pastel green
  classDef datastruct fill:#E0F8E0,stroke:#95D7B2,stroke-width:2px,color:#2B4C3F,rx:12,ry:12;
  %% Error handling: pastel red
  classDef error fill:#FFE4E1,stroke:#FFB6B2,stroke-width:2px,color:#9B3B35,rx:12,ry:12;
  %% Initialization/setup: pastel purple
  classDef init fill:#E6E6FA,stroke:#C6C9E7,stroke-width:2px,color:#444468,rx:12,ry:12;
  %% Logical groupings: very light gray
  classDef group fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,color:#333,border-radius:16px;
  %% --- DOMAIN ROOT ---
  subgraph SG_DOMAIN["Integrations & Extensibility: Third-Party Integrations" ]
    direction TB

    %% --- CORE DOMAIN ABSTRACTIONS ---
    subgraph SG_CORE_ABST["Core Abstractions & Integration Models"]
      direction TB
      SlackIntegration["Slack Integration\nNotification Handler" ]
      SlashCommandsIntegration["Slack Slash Commands\nChatOps/Automation" ]
      SlackAppConfig["Slack App Config & Settings" ]
      InstanceSlackIntegration["Instance Slack Integration" ]
      class SlackIntegration,SlashCommandsIntegration,SlackAppConfig,InstanceSlackIntegration core
    end
    class SG_CORE_ABST group

    %% --- DOMAIN DATA STRUCTURES ---
    subgraph SG_DATA["Domain Events & Chat Message Data"]
      direction TB
      ChatEvent["Chat Event Message\nBase Structure" ]
      IssueEvent["Issue Event Message" ]
      AlertEvent["Alert Event Message" ]
      DeploymentEvent["Deployment Event Message" ]
      PipelineEvent["Pipeline Event Message" ]
      class ChatEvent,IssueEvent,AlertEvent,DeploymentEvent,PipelineEvent datastruct
    end
    class SG_DATA group

    %% --- INTEGRATION PIPELINES ---
    subgraph SG_PIPELINE["Event Routing & Notification Pipelines"]
      direction TB
      EventRouter["Event Router Service" ]
      BgWorker["Background Worker" ]
      InteractionDispatcher["Slack Interaction Dispatcher" ]
      BlockKitBuilder["Block Kit Builder" ]
      class EventRouter,BgWorker,InteractionDispatcher,BlockKitBuilder core
    end
    class SG_PIPELINE group

    %% --- DOMAIN UTILITIES & EXTENSIBILITY SUPPORT ---
    subgraph SG_UTILS["Extensibility Utilities & Option Handlers"]
      direction TB
      OptionRouter["Dynamic Option Router\nAssignees/Labels" ]
      UserSearch["User Search Handler" ]
      LabelSearch["Label Search Handler" ]
      ScopePropagation["Scope Propagation Service" ]
      IntegrationRecord["Integration Record\nAPI Scope/Workspace" ]
      class OptionRouter,UserSearch,LabelSearch,ScopePropagation,IntegrationRecord util
    end
    class SG_UTILS group

    %% --- CONTROLLERS / CONFIG / SETUP ---
    subgraph SG_SETUP["Setup & Configuration Flow"]
      direction TB
      AdminSetup["Admin OAuth Setup" ]
      ProjectBind["Project/Group App Bindings" ]
      SlashCmdController["Slash Command Controller" ]
      OAuthHandler["OAuth & Error Handling" ]
      class AdminSetup,ProjectBind,SlashCmdController,OAuthHandler init
    end
    class SG_SETUP group

    %% --- INSTALLATION & AUTHORIZATIONS ---
    subgraph SG_INSTALL["Installation & Authorization Services"]
      direction TB
      AppInstaller["App Installer Instance/Project/Group" ]
      UserAuthorizer["User Chat Account Linker" ]
      ExclusionService["Integration Exclusion Filter" ]
      class AppInstaller,UserAuthorizer,ExclusionService util
    end
    class SG_INSTALL group

    %% --- SUPPORTING LIBRARIES / API / MANIFEST ---
    subgraph SG_LIBS["API Wrappers & Supporting Libraries"]
      direction TB
      SlackAPI["Slack API Wrapper" ]
      MarkdownSanitize["Markdown Sanitizer" ]
      AppManifest["Slack App Manifest" ]
      SlashCmdRunner["Slash Command Runner" ]
      PresenterError["Error Presenter" ]
      PresenterDeploy["Deployment Presenter" ]
      PresenterIncident["Incident Presenter" ]
      class SlackAPI,MarkdownSanitize,AppManifest,SlashCmdRunner,PresenterError,PresenterDeploy,PresenterIncident util
    end
    class SG_LIBS group

    %% --- ERROR HANDLING COMPONENTS ---
    ErrorHandler["Error Handler" ]
    class ErrorHandler error

    %% ==== LOGICAL FLOWS AND RELATIONSHIPS ====

    %% General Integration Data Flow
    SlackIntegration -->|defines events| ChatEvent
    SlackIntegration -.->|delivers events| EventRouter
    SlackAppConfig -->|configures| SlackIntegration
    SlackAppConfig -->|binds via| AppInstaller
    AppInstaller -->|installs| SlackAppConfig
    AppInstaller -->|installs| InstanceSlackIntegration

    %% Command Collaboration
    SlashCommandsIntegration -->|executes| SlashCmdRunner
    SlashCommandsIntegration -->|presents errors| PresenterError
    SlashCommandsIntegration -->|presents deployments| PresenterDeploy
    SlashCommandsIntegration -->|presents incidents| PresenterIncident
    SlashCommandsIntegration --> SlashCmdController
    SlashCmdController --> SlashCommandsIntegration

    %% SlashCommandsIntegration connects with ProjectBind and AdminSetup
    SlashCommandsIntegration <-->|enabled for| ProjectBind
    AdminSetup -->|redirects OAuth| OAuthHandler
    OAuthHandler -->|handles errors| ErrorHandler
    ProjectBind -->|configures| SlackIntegration

    %% Event Routing
    BgWorker -->|triggers| EventRouter
    EventRouter -->|routes| InteractionDispatcher
    EventRouter -->|formats messages| BlockKitBuilder

    %% Domain Data Event Types
    ChatEvent -.-> IssueEvent
    ChatEvent -.-> AlertEvent
    ChatEvent -.-> DeploymentEvent
    ChatEvent -.-> PipelineEvent

    %% Notification Pipeline
    BgWorker -->|delivers| ChatEvent
    EventRouter -->|uses| SlackAPI
    BlockKitBuilder -->|utilizes| SlackAPI

    %% Interactions & Block Kit Flows
    InteractionDispatcher --> BlockKitBuilder
    BlockKitBuilder -->|presents| ChatEvent

    %% Utilities & Option Routing
    OptionRouter -->|delegates to| UserSearch
    OptionRouter -->|delegates to| LabelSearch

    %% Scope & Integration Collaboration
    ScopePropagation -->|populates| IntegrationRecord
    IntegrationRecord -->|provides scope| SlackIntegration

    %% Installation & Exclusion
    AppInstaller -->|instantiates| SlackAppConfig
    ExclusionService -->|restricts| SlackAppConfig
    UserAuthorizer -->|links user| SlackAppConfig

    %% Data Structure Formatting
    ChatEvent -->|sanitized by| MarkdownSanitize

    %% API & Manifest
    SlackAppConfig -->|declares app| AppManifest
    SlackAPI -->|used by| EventRouter
    SlackAPI -->|used by| BlockKitBuilder

    %% Presenters & SlashCmdRunner flow
    SlashCmdRunner --> PresenterError
    SlashCmdRunner --> PresenterDeploy
    SlashCmdRunner --> PresenterIncident

    %% Setup connects to Integration
    AdminSetup -->|enables| SlackAppConfig

    %% Error Handling links
    OAuthHandler -->|on error| ErrorHandler
    SlashCommandsIntegration -->|on error| ErrorHandler
    EventRouter -->|on error| ErrorHandler

  end
  class SG_DOMAIN group

  %% ==== END OF DIAGRAM ====
```