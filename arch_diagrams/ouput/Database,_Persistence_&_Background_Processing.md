```mermaid
flowchart TD
%% ============== CLASSES AND COLORS ================
classDef core fill:#D4F1F9,stroke:#7FB8D3,stroke-width:2px,rx:10,ry:10,color:#224761
classDef support fill:#FFF8DC,stroke:#E5D9AA,stroke-width:2px,rx:10,ry:10,color:#75661B
classDef data fill:#E0F8E0,stroke:#80C280,stroke-width:2px,rx:10,ry:10,color:#175028
classDef error fill:#FFE4E1,stroke:#D29191,stroke-width:2px,rx:10,ry:10,color:#822B26
classDef init fill:#E6E6FA,stroke:#C1B6DD,stroke-width:2px,rx:10,ry:10,color:#553473
classDef group fill:#F8F8F8,stroke:#BBC6C8,stroke-width:3px,rx:22,ry:22

%% ================= DOMAIN ROOT =====================
subgraph DOMAIN["Database, Persistence & Background Processing"]
  class DOMAIN group
  direction TB

  DPBP_PURPOSE[Domain Purpose:\n"Reliable persistence, transactional integrity, scalable background processing,\nmigrations and consistency for all product data."]:::core

  %% ------------ SUBDOMAIN ABSTRACTS WITH VERTICAL FLOW ----------
  subgraph SUB1["Background Jobs & Workers"]
    class SUB1 group
    direction TB
    SUB1_PURPOSE[Purpose:\n"Asynchronous and scheduled job execution & coordination.\nPlatform for all background/batch operations."]:::core

    WORKER_BASE[Worker Base Abstractions]:::core
    JOB_SCHEDULING[Queueing, Routing, Scheduling]:::core
    BATCH_EXEC[Batch & Partitioned Processing]:::core
    JOB_CONSISTENCY[Idempotence & Consistency Contracts]:::core
    JOBDATA[Job State, Batch Metrics & Logs]:::data
    ERROR_HANDLER[Error & Failure Handling]:::error
    WORKER_UTILITY[Supporting Utilities & Mixins]:::support
    BG_MONITOR[Queue/Job Monitoring & Health]:::core
    BG_INIT[Background Config, Setup, Sharding]:::init
  end

  subgraph SUB2["Data Integrity & DB Utilities"]
    class SUB2 group
    direction TB
    SUB2_PURPOSE[Purpose:\n"Ensures data correctness, reusable model layers, domain schema tools, bulk operations,\ninit/seed helpers, event storage, cache, import."]:::core

    DB_MODELS[Domain Models & Integrity Concerns]:::core
    DB_BATCHHELPERS[Batch DB Helpers & Structure Utilities]:::support
    DB_CACHE[Cache & In-Memory Utility]:::support
    SCHEMA_DICT[DB Schema Dictionary, EnumDefs]:::data
    SEEDER[Seeder & DevData Bootstrapping]:::init
    EVENT_STORE[Event Store & Change Tracking]:::core
    CLICKHOUSE_MODEL[ClickHouse & Analytics Model Layer]:::core
    BG_MIGRATION_MODEL[Background Migration/Isolated Models]:::core
    FIXTURES_DEV[Fixtures, Integration, QA/Test Utilities]:::init
    SUPPORT_UTILS[Utility Helpers]:::support
    DATA_IMPORT[DB Importers, Transformers]:::support
    DB_INIT[System Bootstrap & Init`ilization]:::init
  end

  subgraph SUB3["Migrations & Partitioning"]
    class SUB3 group
    direction TB
    SUB3_PURPOSE[Purpose:\n"Orchestrate schema/data evolution:\nMigrations, partitioning, sharding, index, with consistency & safety."]:::core
    MIG_CORE[Migration Orchestrator]:::core
    MIG_STEP[Migration Steps, Handlers]:::init
    PARTITION_LOGIC[Partitioning Engine]:::core
    MIG_SCHEMAINFO[Partition/Schema Info]:::data
    SEARCH_MIGRATION[Elastic/ClickHouse Migration Flows]:::core
    MIG_CLIENTS[Migration Clients, Infra Layer]:::support
    QA_VALIDATION[Migration Integration/QA/Test]:::init
    ERROR_MIG[Migration Error Handling]:::error
  end

  %% --------------- SHARED STRUCTURES AND LOGICS ----------------
  subgraph SHARED["Domain-Shared Data, Contracts, Abstractions"]
    class SHARED group
    direction TB
    JOBSTATE_DS[Job/Batch State & Metrics]:::data
    PARTITION_STATE[Partition & Schema State]:::data
    MIGRATION_STATUS[Migration State & Task Tracking]:::data
    CONSISTENCY_CONTRACT[Consistency, Idempotence Contracts]:::core
    LOCKING[Distributed Lock/Lease Abstractions]:::support
    INDEXING_ADAPT[Indexing/Record Abstractions]:::core
    EVENTS[Domain Event Flow, Change Log]:::core
    ERROR_FLOW[Domain Error Flow & Reporting]:::error
  end
end

%% ================= HIGH-LEVEL VERTICAL LAYOUT =================

DPBP_PURPOSE --> SUB1
DPBP_PURPOSE --> SUB2
DPBP_PURPOSE --> SUB3

SUB1 --> SHARED
SUB2 --> SHARED
SUB3 --> SHARED

%% ========= LOGICAL FLOWS BETWEEN SUBDOMAINS =========

%% -- 1. Background Jobs <-> Data Integrity & Utilities --
WORKER_BASE --applies on--> DB_MODELS
JOB_CONSISTENCY --validates constraints--> DB_MODELS
BATCH_EXEC --uses batch helpers--> DB_BATCHHELPERS
JOBDATA --records state in--> JOBSTATE_DS
BG_MONITOR --reads health/state from--> JOBSTATE_DS
BG_INIT --setup for background jobs uses--> DB_INIT

WORKER_UTILITY --mixins/extensions for--> DB_MODELS
JOB_SCHEDULING --routes jobs for--> SEEDER
ERROR_HANDLER --escalates to--> ERROR_FLOW
JOB_CONSISTENCY --enforces via--> CONSISTENCY_CONTRACT

%% -- 1a. Background Jobs <-> Migration & Partitioning --
BATCH_EXEC --schedules batch migrations via--> MIG_CORE
BG_INIT --initializes job migration handlers in--> MIG_STEP
BATCH_EXEC --references partition info from--> PARTITION_STATE
JOB_CONSISTENCY --aligns guarantees with--> CONSISTENCY_CONTRACT
BG_MONITOR --escalates migration issues to--> ERROR_MIG

%% -- 2. Data Integrity & DB Utilities <-> Migrations & Partitioning --
DB_MODELS --structure inspected by--> MIG_CORE
SCHEMA_DICT --informs schema change of--> MIG_SCHEMAINFO
DB_BATCHHELPERS --supports migration partitions through--> PARTITION_LOGIC
DB_CACHE --temporary storage/validation for--> MIG_CORE
BG_MIGRATION_MODEL --used as migration-bound models in--> MIG_CORE
CLICKHOUSE_MODEL --used as migration boundary for reporting in--> SEARCH_MIGRATION

FIXTURES_DEV --provisions integration scenarios for--> QA_VALIDATION
EVENT_STORE --provides events for--> EVENTS
SEEDER --prepares fixture/seed jobs for migration flows in--> MIG_CORE

%% -- 3. Migrations & Partitioning Internal Collaborations --
MIG_CORE --orchestrates steps--> MIG_STEP
MIG_STEP --executes partitioning/batching via--> PARTITION_LOGIC
PARTITION_LOGIC --manipulates partition/schema state via--> MIG_SCHEMAINFO
SEARCH_MIGRATION --migration analytics/data move via--> PARTITION_STATE
MIG_SCHEMAINFO --tracks state feeds--> MIGRATION_STATUS
MIG_CLIENTS --support migration execution for--> MIG_CORE
QA_VALIDATION --tests and rollback via--> ERROR_MIG

%% -- 4. Shared Domain Data & Flows --
DB_MODELS --mutations trigger events into--> EVENTS
WORKER_BASE --job state logged as--> JOBSTATE_DS
MIG_CORE --state transitions updated in--> MIGRATION_STATUS
BATCH_EXEC --batch state/metrics logged in--> JOBSTATE_DS
MIG_CORE --uses global state/partition info from--> PARTITION_STATE

LOCKING --provides uniqueness and lease for--> WORKER_BASE & MIG_CORE
CONSISTENCY_CONTRACT --used by all subdomains for: consistency, deduplication, atomicity
INDEXING_ADAPT --shared querying/indexing for models and migration records in--> DB_MODELS & SEARCH_MIGRATION
ERROR_FLOW --escalates up from error handlers across subdomains
EVENTS --log mutation/hook for background jobs, migrations, seeding

PARTITION_STATE --feeds partitioning/migration for--> BATCH_EXEC, PARTITION_LOGIC, SEARCH_MIGRATION
JOBSTATE_DS --cross-domain job state for history/consistency in--> BG_MONITOR & QA_VALIDATION

%% -- 5. Data Seeding & Init Flows --
SEEDER --spawns jobs/procs in--> WORKER_BASE
SEEDER --provisions dev/test data for--> DB_MODELS & EVENT_STORE
DB_INIT --ensures proper system domain config for all subdomains

%% -- 6. Caching & Eventing Cross-Domain --
DB_CACHE --accelerates lookups for--> WORKER_BASE & MIG_CORE
EVENT_STORE --hooks into mutation handlers for audit/trigger in all subdomains

%% -- 7. Analytics, Reporting, Observability --
CLICKHOUSE_MODEL --analyzes migration/batch status from--> MIGRATION_STATUS
BG_MONITOR --feeds health/metrics into--> EVENTS

%% ================= SUBGRAPH / GROUPBOX BOUNDARIES =================

class DOMAIN,SHARED,group
class SUB1,SUB2,SUB3,group
class DPBP_PURPOSE,core
class SUB1_PURPOSE,SUB2_PURPOSE,SUB3_PURPOSE,core

class WORKER_BASE,JOB_SCHEDULING,BATCH_EXEC,JOB_CONSISTENCY,BG_MONITOR,core
class JOBDATA,data
class ERROR_HANDLER,error
class BG_INIT,init
class WORKER_UTILITY,support

class DB_MODELS,EVENT_STORE,CLICKHOUSE_MODEL,BG_MIGRATION_MODEL,core
class DB_BATCHHELPERS,DB_CACHE,DATA_IMPORT,SUPPORT_UTILS,support
class SCHEMA_DICT,data
class SEEDER,DB_INIT,FIXTURES_DEV,init

class MIG_CORE,PARTITION_LOGIC,SEARCH_MIGRATION,core
class MIG_STEP,QA_VALIDATION,init
class MIG_SCHEMAINFO,PARTITION_STATE,data
class MIG_CLIENTS,support
class ERROR_MIG,error

class JOBSTATE_DS,PARTITION_STATE,MIGRATION_STATUS,data
class CONSISTENCY_CONTRACT,INDEXING_ADAPT,EVENTS,core
class LOCKING,support
class ERROR_FLOW,error

%% ============ LEGEND ABSTRACTED FOR SPACE =============
subgraph LEGEND["Legend"]
 class LEGEND group
 direction TB
 L_CORE[Core domain logic]:::core
 L_SUPPORT[Supporting/utility component]:::support
 L_DATA[Domain data structure]:::data
 L_ERROR[Error handling]:::error
 L_INIT[System/boot/initiation]:::init
 L_GROUP[Logical grouping]:::group
end
```