```mermaid
flowchart TB
  %% === STYLING DEFINITIONS ===
  classDef coreDomain fill:#D4F1F9,stroke:#A8D3E6,stroke-width:2px,color:#222,rx:10,ry:10
  classDef utility fill:#FFF8DC,stroke:#FFE4A0,stroke-width:2px,color:#444,rx:10,ry:10
  classDef dataStruct fill:#E0F8E0,stroke:#C2EABD,stroke-width:2px,color:#333,rx:10,ry:10
  classDef errorHandling fill:#FFE4E1,stroke:#FFB6B0,stroke-width:2px,color:#333,rx:10,ry:10
  classDef init fill:#E6E6FA,stroke:#B8A8F0,stroke-width:2px,color:#333,rx:10,ry:10
  classDef subgraphGray fill:#F8F8F8,stroke:#D4F1F9,stroke-width:3px,color:#111,rx:16,ry:16
  classDef subgraphGreen fill:#F8F8F8,stroke:#E0F8E0,stroke-width:3px,color:#222,rx:16,ry:16
  classDef subgraphYellow fill:#F8F8F8,stroke:#FFF8DC,stroke-width:3px,color:#222,rx:16,ry:16
  classDef subgraphRed fill:#F8F8F8,stroke:#FFE4E1,stroke-width:3px,color:#222,rx:16,ry:16

%% === DOMAIN ROOT CLUSTER ===
subgraph markdown_content_domain["Frontend, UI & Presentation · Markdown & Content Rendering":::subgraphGray]
  direction TB

  %% ==================== 1. CORE ARCHITECTURE ABSTRACTIONS ====================
  subgraph banzai_core["Core: Banzai Rendering Framework":::subgraphGray]
    direction TB
    banzaiEntry["Banzai: Markdown Rendering API\nFacade/Entry Point":::coreDomain]
    banzaiPipeline["Banzai Pipeline Architecture\nPipeline Resolver/Loader":::coreDomain]
    coreRenderer["Renderer\nPipeline Execution & HTML Output":::coreDomain]
    filterArray["Filter Array\nFilter Chain Core":::coreDomain]
    referenceParsers["Reference Parsers\nDomain Reference Logic":::coreDomain]
    basePipeline["BasePipeline Pattern\nBanzai, SingleLine, GFM, etc.":::coreDomain]
    class banzaiEntry,banzaiPipeline,coreRenderer,filterArray,referenceParsers,basePipeline coreDomain
  end

  %% ==================== 2. SUBDOMAIN CLUSTERS ===============================

  subgraph markdown_rendering["Subdomain: Markdown Rendering & Parsers":::subgraphGray]
    direction TB
    pipelineTypes["Registered Pipeline Types\nGFM, Wiki, AsciiDoc, Email, Atom, ...":::coreDomain]
    renderingEngines["Markdown Engines/Parsers\nCMark, GLFM, Kramdown":::coreDomain]
    cacheMechanisms["Markdown Cache Structures\nActiveRecord/Redis/Frontmatter":::dataStruct]
    appSrv["Application/Service\nLayer":::coreDomain]
    referenceNodes["Domain Reference Nodes\nCommit, Issue, User,... Parsed":::coreDomain]
    markupDetection["Markup Helper\nMarkup Type Detection & Routing":::utility]

    class pipelineTypes,renderingEngines,referenceNodes appSrv,markupDetection coreDomain
    class cacheMechanisms dataStruct
    class markupDetection utility

    %% Logical relationships in renderer
    banzaiEntry --loads/executes--> banzaiPipeline
    banzaiPipeline --uses--> pipelineTypes
    banzaiPipeline --runs pipelines on--> coreRenderer
    coreRenderer --invokes filters in--> filterArray

    coreRenderer --uses cache--> cacheMechanisms
    coreRenderer --queries refs via--> referenceParsers
    coreRenderer --delegates to render engine--> renderingEngines

    markupDetection --used by--> coreRenderer
    markupDetection --used by--> appSrv
    pipelineTypes --uses--> renderingEngines
    banzaiEntry --exposed via--> appSrv
  end

  subgraph banzai_filters["Subdomain: Banzai Filters":::subgraphGray]
    direction TB
    filterChains["Registered Filter Chains\nGFM, EE, Jira, Duo, ...":::coreDomain]
    filterGroups["Content Filter Groups\nMarkdown, Reference, Sanitization, Media, ToC, ...":::coreDomain]
    refFilterAbstraction["Reference Filter Pattern\nAbstract/Concrete Filters":::coreDomain]
    contentEnrichment["Enrichment Filters\nBroadcast, Attributes, Emoji, KaTeX, ...":::coreDomain]
    sanitization["Sanitization Filters\nSecurity/HTML Safety":::dataStruct]
    mediaHandling["Image/Media Filters":::dataStruct]
    filterConcerns["Filter Concerns\nPipeline Timing, Output Safety,\nTimeout":::utility]
    filterError["Redaction/Error Filters":::errorHandling]
    filterDataCache["Markdown/Reference Cache\nField/Reference Caching":::dataStruct]

    %% relationships (within)
    filterChains --composed of--> filterGroups
    filterGroups --composed of--> contentEnrichment
    filterGroups --includes-> sanitization
    filterGroups --includes-> refFilterAbstraction
    filterGroups --includes-> mediaHandling
    filterGroups --includes-> filterError

    filterGroups --applies filter concerns via--> filterConcerns

    refFilterAbstraction --queries via--> referenceParsers
    refFilterAbstraction --caches via--> filterDataCache

    sanitization --shared with--> filterDataCache
    mediaHandling --used by--> filterGroups

    class filterChains,filterGroups,refFilterAbstraction,contentEnrichment coreDomain
    class sanitization,mediaHandling,filterDataCache dataStruct
    class filterConcerns utility
    class filterError errorHandling
  end

  %% ==================== 3. DOMAIN-WIDE SHARED DATA STRUCTURES & PATTERNS ======
  subgraph shared_structures["Shared Data Structures & Cross-subdomain Patterns":::subgraphGreen]
    direction TB
    markdownCache["Markdown Field Cache\nApp/AR, Redis Store, Versioning":::dataStruct]
    refCache["Reference Cache\nCache Expansion/Redaction":::dataStruct]
    sanitizable["Sanitizable Model Concern":::dataStruct]
    renderContext["Render Context\nPipeline Execution/State":::utility]
    baseFilter["Base Filter Abstraction\nFilterArray, AbstractReference":::coreDomain]
    pipelinePattern["Pipeline/Filter Patterns\nInheritance, Registry":::coreDomain]
    class markdownCache,refCache,sanitizable dataStruct
    class renderContext utility
    class baseFilter,pipelinePattern coreDomain
  end

  %% ==================== 4. COLLABORATION & FLOW BETWEEN SUBDOMAINS ===========
  subgraph domain_collab["Domain Collaboration: Data & Control Flow":::subgraphYellow]
    direction TB

    %% Abstract flow: Markdown is rendered via Banzai pipelines, which are built from registered filter chains, and whose execution invokes pipelines and filter groups; reference/resolver parsers support reference detection and expansion; filtering and parsing chain uses caching and sanitization as shared cross-domain data structure features.

    rendererEntry["Render Markdown Content\nBanzai API/Controller Service":::coreDomain]
    banzaiPipelineExec["Run Banzai Pipeline":::coreDomain]
    filterChainExec["Process Filter Chain":::coreDomain]
    filterExec["Apply Markdown/Ref/Media/Sanitation Filters":::coreDomain]
    refExpansion["Resolve & Expand References":::coreDomain]
    cachePersist["Persist/Invalidate Cache\nMarkdown, Reference":::dataStruct]
    sanitizeHTML["Sanitize HTML & Ensure Safety":::dataStruct]

    class rendererEntry,banzaiPipelineExec,filterChainExec,filterExec,refExpansion coreDomain
    class cachePersist,sanitizeHTML dataStruct

    %% Control & data flow
    rendererEntry --invokes pipeline via--> banzaiPipelineExec
    banzaiPipelineExec --composed from--> filterChainExec
    filterChainExec --executes--> filterExec
    filterExec --uses for references--> refExpansion
    filterExec --uses cache/sanitization--> cachePersist
    filterExec --ensures safety via--> sanitizeHTML
    cachePersist --shared with--> sanitizeHTML
  end

  %% ==================== 5. APP REUSE AND UTILITY SURFACES ====================
  subgraph ui_app_services["UI/App Integration & Utility Services":::subgraphYellow]
    direction TB
    previewService["Preview Markdown API/Service":::coreDomain]
    markupHelperUI["Markup Detection for UI":::utility]
    labelFilter["Quick Action & Label Syntax":::coreDomain]
    tOC["Table of Contents Generation":::coreDomain]
    searchHelper["Search Helper w/ Syntax Highlighting":::utility]
    emojiFilter["Custom/Standard Emoji Filter":::coreDomain]
    remoteReadme["Remote Dev/Readme Helper":::utility]

    class previewService coreDomain
    class markupHelperUI,searchHelper,remoteReadme utility
    class labelFilter,emojiFilter,tOC coreDomain

    previewService --calls--> rendererEntry
    markupHelperUI --used by--> previewService
    tOC --implemented as--> filterGroups
    searchHelper --augments--> rendererEntry
  end

  %% ==================== 6. ERROR & VALIDATION HANDLING =======================
  subgraph error_handling["Error Handling & Redaction":::subgraphRed]
    direction TB
    redaction["Reference Redactor Filter":::errorHandling]
    filterTimeout["Timeout/Filter Rescues":::errorHandling]
    docLint["Documentation Link Lint":::errorHandling]
    class redaction,filterTimeout,docLint errorHandling
    filterExec --rescues on error via--> filterTimeout
    filterExec --redacts as needed--> redaction
    docLint --validates--> previewService
  end

  %% ==================== 7. SUBDOMAIN RELATIONSHIPS & INTEGRATION LINES =======
  %% Integration: pipelines and filters

  %% Core Banzai <-> Filter Chain
  banzaiPipeline --builds filter chains from--> filterChains
  filterChains --run by--> banzaiPipeline

  filterArray --composes filters--> filterGroups
  filterGroups --included in pipelines--> pipelineTypes

  pipelineTypes --include via pattern--> pipelinePattern
  basePipeline --basis for--> pipelinePattern

  %% Both subdomains interact with referenceParsers, data caching and sanitization
  referenceParsers --used by filters for--> refExpansion
  referenceNodes --parsed via--> referenceParsers
  refFilterAbstraction --implemented using--> referenceParsers
  refFilterAbstraction --persist references in--> refCache

  %% Shared cache and sanitization
  filterDataCache --relies on--> markdownCache
  sanitization --integrates for models--> sanitizable
  filterError --reports via--> docLint

  cacheMechanisms --wraps--> markdownCache
  filterDataCache --wraps--> refCache

  renderContext --injected into--> coreRenderer
  filterConcerns --mixed into filters--> filterGroups
  filterConcerns --used by--> filterExec

  %% App service integration points
  appSrv --calls--> coreRenderer
  coreRenderer --uses pipeline registry--> pipelineTypes
  appSrv --surfaces to UI via--> previewService
  previewService --calls--> rendererEntry

  %% Cross-subdomain: Filter chains, cache, reference, and sanitization are shared abstractions
  filterChains --pattern implemented in--> pipelinePattern
  filterExec --pattern implemented using--> baseFilter

  %% Data flows
  coreRenderer --caching via--> cacheMechanisms
  cacheMechanisms --persisted/cached by--> cachePersist
  filterGroups --filtering ensures--> sanitizeHTML

  %% Table of contents is a shared filter and parser concern
  tOC --filter within--> filterGroups
  tOC --output used by--> appSrv

  %% EE/extension: Specialized filter chains
  filterChains --include EE/Jira extensions via--> pipelineTypes

  %% Utility/Helper usage
  markupDetection --helper for--> previewService
  searchHelper --provides UI search context--> appSrv

  %% Reuse of reference abstraction
  refFilterAbstraction --abstracts over--> referenceParsers
  referenceParsers --parse into--> referenceNodes

end

%% =================== GLOBAL RELATIONSHIPS / ARROWS ===========================
%% Abstracted overall flow

appSrv --renders via--> rendererEntry
rendererEntry --core rendering entry--> banzaiEntry

banzaiEntry --loads pipelines--> banzaiPipeline
banzaiPipeline --organizes--> pipelineTypes
pipelineTypes --compose via filter chains--> filterChains
filterChains --composed of--> filterGroups

filterGroups --delegates filtering logic to--> filterExec

filterExec --applies filter concerns--> filterConcerns
filterExec --expands references via--> refExpansion
filterExec --persists/reads through cache--> cachePersist

cachePersist --depends on--> markdownCache
cachePersist --interacts with--> refCache
sanitizeHTML --uses models via--> sanitizable

refExpansion --parsers from--> referenceParsers

%% Error/Redaction reporting in filters
filterExec --rescues/error logging--> filterTimeout
filterExec --applies redaction--> redaction
redaction --reports lint/errors--> docLint

%% UI/utilities
markupHelperUI --used in UI via--> appSrv
remoteReadme --used in--> appSrv
searchHelper --extends UI search in--> appSrv
tOC --provides navigation for--> appSrv

%% Patterns/Abstractions
basePipeline --used as--> pipelinePattern
filterArray --used as--> baseFilter

%% Application surface
appSrv --outputs UI to--> markdown_content_domain

%% CONCLUSION: Diagram end
```