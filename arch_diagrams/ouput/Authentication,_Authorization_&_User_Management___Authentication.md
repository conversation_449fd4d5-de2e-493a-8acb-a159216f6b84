```mermaid
flowchart TB
  %% ======================================================
  %% DOMAIN: Authentication, Authorization & User Management -> Authentication
  %% COMBINED HIGH-LEVEL DOMAIN DIAGRAM (VERTICAL LAYOUT)
  %% ======================================================

  %% === NODE STYLE CLASSES (per guidelines and subdomain colors) ===
  classDef core fill:#D4F1F9,stroke:#61a4bc,stroke-width:2px,rx:12,ry:12;
  classDef support fill:#FFF8DC,stroke:#e8d68b,stroke-width:2px,rx:12,ry:12;
  classDef data fill:#E0F8E0,stroke:#91c99c,stroke-width:2px,rx:12,ry:12;
  classDef error fill:#FFE4E1,stroke:#e7b0ad,stroke-width:2px,rx:12,ry:12;
  classDef setup fill:#E6E6FA,stroke:#b7aeea,stroke-width:2px,rx:12,ry:12;
  classDef grouping fill:#F8F8F8,stroke:#dedede,stroke-width:3px,rx:20,ry:20;
  classDef borderO flowchart;
  
  %% =========== HIERARCHY GROUPINGS (SUBDOMAINS) ===========
  subgraph AUTH_DOMAIN["Authentication Domain: Core Concepts & Subdomains"]
    direction TB
    style AUTH_DOMAIN fill:#F8F8F8,stroke:#D4F1F9,stroke-width:3px

      %% -- SHARED CORE DATA STRUCTURES --
      UserIdentity["User / Identity":::core]
      AuthEvent["AuthenticationEvent":::data]
      PATEntity["PersonalAccessToken":::core]
      PATApiEntity["PersonalAccessToken API Entity":::data]
      GroupIdentityLink["Group SAML/LDAP Identity Link":::data]
      AuthResult["Auth::Result & Blocked State":::data]
      TwoFASettings["2FA Config & State enrolled, settings":::data]

      %% -- AUTH ENTRY / ROUTING --
      AuthEntry["Auth Entry Points
Controllers / Callbacks":::core]
      class AuthEntry core

      %% -- Key abstractions/patterns --
      AuthVerifier["Auth Verifier & Result Pattern":::core]
      IdentityLinker["Identity Abstractions & Linking":::support]
      AuthPolicy["Provider & Block Policy":::support]
      AuthServiceBase["Auth Base Service Pattern":::support]

    end

  %% ------------ SUBDOMAIN: Identity Providers & Social Auth OAuth, SAML, OIDC, LDAP, OmniAuth, SCIM, SSO ------------
  subgraph IDP_AUTH["Identity Provider & SSO Integration"]
    direction TB
    style IDP_AUTH fill:#F8F8F8,stroke:#61a4bc,stroke-width:2px

      IDPControllers["OAuth/SAML/LDAP/OIDC Strategy Controllers":::core]
      OmniAuthInit["OmniAuth Initializers & Strategies":::setup]
      IDPIdentity["External Identity Mapping
OAuth, SAML, LDAP, SCIM":::data]
      SCIMProvision["SCIM Provisioning & Group Sync":::core]
      SAMLGroupLink["Group SAML / LDAP Link & Lifecycle":::core]
      IDPPolicy["IDP Policy, GroupSaml Policy":::support]

      IDPError["IDP Connection/Error Handling":::error]
  end

  %% ------------ SUBDOMAIN: Blocked User Handling ------------
  subgraph BLOCKED["Blocked User Handling"]
    direction TB
    style BLOCKED fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px

      BlockedService["Block/Unblock Services
Manual & AutoBan":::core]
      BlockedTracker["BlockedUserTracker
& Custom Attribute Log":::support]
      UniqueIpLimiter["Unique IP Login Limiter":::support]
      BlockedUserPolicy["Blocked Policy & Enforcement Rule":::support]
      BlockedError["IpBlocked Error & States":::error]
  end

  %% ------------ SUBDOMAIN: PATs Personal Access Tokens ------------
  subgraph PATS["Personal Access Tokens"]
    direction TB
    style PATS fill:#F8F8F8,stroke:#96B6C5,stroke-width:2px

      PATController["PAT Controllers / API Endpoint":::core]
      PATService["PAT Lifecycle Services
Create, Rotate, Revoke, Expiry":::core]
      PATEntityHigh["PAT Domain Entity":::core]
      PATSerialization["PAT Serializers & Resource Entity":::data]
      PATValidation["PAT Expiry Validation / EE Extensions":::support]
      PATNotif["PAT Notification Workers / Expiry":::support]
      PATPolicy["PAT Policy / Helper":::support]
      PATError["PAT Expiry/Error":::error]
  end

  %% ------------ SUBDOMAIN: 2FA & Multi-Factor MFA ------------
  subgraph MFA["2FA & Multi-Factor Authentication"]
    direction TB
    style MFA fill:#F8F8F8,stroke:#6CB3CF,stroke-width:2px

      TwoFAController["2FA Controllers / Concerns\nEnforcement":::core]
      TwoFAService["2FA Service Layer\nRegistration & Validation":::core]
      WebAuthnProvider["WebAuthn/FIDO Provider\nDevice Reg & Verification":::core]
      OTPProvider["OTP, TOTP, Push, Duo, Fortinet\nProviders & Adapters":::core]
      PhoneMFA["Phone & TeleSign Verification Services":::core]
      TwoFAHolder["2FA State / Enrolled Model\nbackup codes, phone, devices":::data]
      TwoFAPolicy["2FA Enforcement/Policy":::support]
      TwoFAError["2FA Error/Lockout":::error]
  end

  %% =========== RELATIONSHIPS & FLOW ACROSS SUBDOMAINS ===========
  %% Identity Providers <-> Core Domain
  IDP_AUTH -- "Authenticates & Link External Identities" --> UserIdentity
  IDP_AUTH -- "SSO/SCIM Provisioning" --> SCIMProvision
  IDPIdentity -. "Represents user for" .-> UserIdentity
  SAMLGroupLink -. "Syncs group identity with" .-> UserIdentity
  SCIMProvision -- "Automated Identity + Group Provision" --> UserIdentity
  IDPControllers -. "Feeds Auth Events" .-> AuthEvent

  %% Blocked User Handling
  BlockedService -- "Updates Blocked State" --> UserIdentity
  UniqueIpLimiter -. "Automatically blocks via" .-> BlockedService
  BlockedService -- "Logs Actions" --> BlockedTracker
  BlockedTracker -- "Records changes/Events" --> AuthEvent
  BlockedService -- "Enforces Policy" --> BlockedUserPolicy
  BlockedService -. "Triggers" .-> BlockedError
  BlockedUserPolicy -- "Consulted by" --> AuthVerifier
  
  %% PATs usage
  PATController -- "User interacts via API/Web" --> PATService
  PATService -- "Operates on" --> UserIdentity
  PATService -- "Issues/Revokes Tokens" --> PATEntityHigh
  PATService -- "Notifies expiration" --> PATNotif
  PATPolicy -- "Interprets permissions for" --> PATService
  PATSerialization -- "Serializes Entity for" --> PATController
  PATEntityHigh -. "Maps to user" .-> UserIdentity
  PATValidation -- "Validates expiry" --> PATService
  PATService -. "Handles Error" .-> PATError
  PATNotif -. "Signals Expiry Event" .-> AuthEvent
  
  %% MFA
  TwoFAController -- "Triggers 2FA workflow" --> TwoFAService
  TwoFAService -- "Accesses/enforces" --> TwoFAHolder
  TwoFAService -- "Consults policy/feature gate" --> TwoFAPolicy
  TwoFAService -- "Calls by Provider" --> WebAuthnProvider
  TwoFAService -- "Calls by Provider" --> OTPProvider
  TwoFAService -- "Integrates" --> PhoneMFA
  TwoFAService -. "Handles error" .-> TwoFAError
  TwoFAHolder -- "Belongs to" --> UserIdentity
  WebAuthnProvider -- "Device tied to" --> TwoFAHolder
  OTPProvider -. "Push/Manual OTP" .-> TwoFAHolder
  PhoneMFA -. "Phone verified for" .-> TwoFAHolder
  TwoFAController -. "Admin flow may enforce" .-> BlockedUserPolicy
  TwoFAService -- "Verifies for" --> AuthVerifier

  %% Core Domain glue
  AuthEntry -- "Entry for all login/flows" --> AuthVerifier
  AuthVerifier -- "Evaluates policy for" --> AuthPolicy
  AuthVerifier -- "Yields" --> AuthResult
  AuthResult -- "Reflects" --> UserIdentity
  AuthVerifier -- "Consumes 2FA" --> TwoFAService
  AuthVerifier -- "Consumes IDP SSO" --> IDP_AUTH
  AuthVerifier -- "Uses PAT for API" --> PATEntityHigh
  AuthServiceBase -- "Implements core logic for" --> AuthVerifier

  %% Abstractions & Patterns
  IdentityLinker -- "Links external identities" --> UserIdentity
  AuthPolicy -- "Drives enforcement IDP, Blocked, PAT, 2FA" --> AuthVerifier

  %% Key Data Flows
  UserIdentity -. "Attribute: Blocked State" .-> AuthResult
  UserIdentity -. "Attribute: 2FA/Enrolled Methods" .-> TwoFAHolder
  UserIdentity -. "Tokens" .-> PATEntityHigh
  UserIdentity -. "Group/External Identities" .-> IDPIdentity

  %% Subdomain cross-linkage
  PATEntityHigh -- "PAT/token mapping for user API flows" --> IDP_AUTH
  TwoFAHolder -- "Required for SSO/IDP enforcement" --> IDP_AUTH
  PATEntityHigh -- "Blocked/expired tokens handled by" --> BlockedService
  PATEntityHigh -- "Can trigger block when misused" --> BlockedService
  AuthEvent -- "Audit trail for all critical auth state changes" --> AuthVerifier
  SCIMProvision -- "Can update block/enrollment for user" --> BlockedService

  %% Initialization/setup span all
  OmniAuthInit -- "System strategy/IDP setup" --> IDPControllers
  OmniAuthInit -- "Drives available providers" --> AuthEntry
  OmniAuthInit -- "Controls login/route availability" --> TwoFAController

  %% Errors
  IDPError -- "Surfaces in SSO flow" --> IDP_AUTH
  BlockedError -- "Surfaces in API/web flows" --> BlockedService
  PATError -- "Raised for expiry/invalid usage" --> PATService
  TwoFAError -- "Returned for lockout/failure" --> TwoFAService

  %% Abstract / Shared structure classes
  class UserIdentity,PATEntityHigh,PATEntity,GroupIdentityLink,AuthEvent,AuthResult,TwoFAHolder data;
  class PATService,BlockedService,TwoFAService,SCIMProvision,SAMLGroupLink,WebAuthnProvider,OTPProvider,PATController,TwoFAController,IDPControllers,PATNotif,PhoneMFA,PATValidation,PATEntityHigh,PATSerialization,PATEntity core;
  class BlockedUserPolicy,AuthPolicy,IdentityLinker,AuthVerifier,AuthServiceBase,PATPolicy,PATSerialization,TwoFAPolicy,BlockedTracker,UniqueIpLimiter,IDPPolicy,support,OmniAuthInit support;
  class IDPError,BlockedError,PATError,TwoFAError error;
  class setup,OmniAuthInit setup;

  %% Logical grouping border spacing for readability
  linkStyle default stroke-width:2px;

  %% Abstracted vertical layout tuning (by using additional invisible nodes/edges)
  UserIdentity -->| |PATEntityHigh
  UserIdentity -->| |TwoFAHolder
  UserIdentity -->| |IDPIdentity
```