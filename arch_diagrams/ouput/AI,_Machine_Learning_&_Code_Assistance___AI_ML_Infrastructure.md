```mermaid
flowchart TB
  %% VERTICAL LAYOUT COMBINED DIAGRAM FOR: AI, Machine Learning & Code Assistance -> AI/ML Infrastructure (Hierarchy Level: 1)

  %% COLOR SCHEME:
  %% Core domain: #D4F1F9
  %% Supporting/utility: #FFF8DC
  %% Data structures: #E0F8E0
  %% Error handling: #FFE4E1
  %% Initialization/setup: #E6E6FA
  %% Subgraph/Logical grouping: #F8F8F8 pastel borders

  %% ========= DOMAIN LEVEL CONCEPTS & GROUPINGS ===========
  subgraph Domain_Context["AI/ML Infrastructure" ]
    direction TB
    style Domain_Context fill:#F8F8F8,stroke:#6EC6E9,stroke-width:3,rounded

    CoreConcepts["Core Concepts:\n- Self-hosted Model Management\n- Gateway Integration\n- Model Validation\n- Infra Status Probing"]:::domcoreconcept
    S_LI["Subdomain:\nSelf-hosted & Gateway Integrations"]:::subdom
  end

  %% ========= DATA STRUCTURES ABSTRACTED ===========
  subgraph SharedData["Domain Data Structures"]
    direction TB
    style SharedData fill:#F8F8F8,stroke:#94CFAA,stroke-width:2,rounded
    SelfHostedModelAbs["SelfHostedModel"]:::coremodel
    ModelEnums["Model Enums\nAcceptedModels / ReleaseStates"]:::datastruct
    ModelMetadataAbs["ModelMetadata"]:::datastruct
  end

  %% ========= API / INTERFACES ===========
  subgraph APILayer["API and Interface Layer"]
    direction TB
    style APILayer fill:#F8F8F8,stroke:#97C6E6,stroke-width:2,rounded
    GraphQLAPI["GraphQL API Layer"]:::core
    AdminHelpers["Admin & UI Helpers"]:::utility
  end

  %% ========= DOMAIN SERVICES ===========
  subgraph ServiceLayer["Domain Services"]
    direction TB
    style ServiceLayer fill:#F8F8F8,stroke:#4EB987,stroke-width:2,rounded
    SHMServices["Self-hosted Model Services\nCreate / Update / Destroy"]:::core
    AuditingPattern["Auditing Pattern"]:::utility
  end

  %% ========= INTEGRATION/GATEWAY/PROBES ===========
  subgraph InfraGateway["Integration & Infra Gateway"]
    direction TB
    style InfraGateway fill:#F8F8F8,stroke:#B5D8EE,stroke-width:2,rounded

    GatewayAbstraction["Gateway Abstractions"]:::core
    Probes["Connectivity & Status Probes"]:::utility
    RemoteDevIntegration["Remote Dev Integration"]:::utility
    CloudConnectors["Cloud Connector Pattern"]:::utility
  end

  %% ========= EXTERNAL INTEGRATIONS ===========
  subgraph ExtIntegrations["External Integrations"]
    direction TB
    style ExtIntegrations fill:#F8F8F8,stroke:#E7B6AC,stroke-width:2,rounded

    Langsmith["Langsmith Integration"]:::utility
  end

  %% =========== RELATIONSHIPS ABSTRACTED LOGICAL FLOW ===========

  %% Subdomain purpose to core concepts
  S_LI --"Implements & Connects"--> CoreConcepts

  %% Core concepts to major abstraction groupings
  CoreConcepts --"Realized via"--> SelfHostedModelAbs
  CoreConcepts --"Validated by"--> Probes
  CoreConcepts --"Controlled with"--> GraphQLAPI
  CoreConcepts --"Managed using"--> ServiceLayer

  %% Data structures support core infra and model logic
  SelfHostedModelAbs --"Defines config for"--> ModelMetadataAbs
  SelfHostedModelAbs --"Status/Types via"--> ModelEnums
  ModelMetadataAbs --"Controls serialization for"--> GatewayAbstraction

  %% API Layer to Services and Data Structures
  GraphQLAPI --"CRUD/Validate"--> SHMServices
  GraphQLAPI --"Mutation uses"--> ModelEnums
  GraphQLAPI --"Query/Mutation"--> SelfHostedModelAbs
  GraphQLAPI --"Maps"--> ModelMetadataAbs
  GraphQLAPI --"Maps"--> AdminHelpers

  %% Admin helpers supply model status/types
  AdminHelpers --"Presents options using"--> ModelEnums
  AdminHelpers --"Displays/Manages"--> SelfHostedModelAbs

  %% Services operate mainly on core model
  SHMServices --"Persists"--> SelfHostedModelAbs

  %% Service/Auditing Coverage
  SHMServices --"Audited by"--> AuditingPattern

  %% Gateway and Probes Logical dependencies
  GatewayAbstraction --"Calls/registers"--> SelfHostedModelAbs
  GatewayAbstraction --"Invokes"--> ModelMetadataAbs
  GatewayAbstraction --"Integrates with"--> Probes

  Probes --"Checks/Monitors"--> SelfHostedModelAbs
  Probes --"Connects via"--> CloudConnectors

  %% Remote Dev & External
  RemoteDevIntegration --"Refers to"--> ModelEnums
  RemoteDevIntegration --"Uses"--> SelfHostedModelAbs

  CloudConnectors --"Probes status"--> Probes

  Langsmith --"Syncs experiments with"--> SelfHostedModelAbs

  %% Data-flow summary (from top to bottom)
  S_LI -.-> GraphQLAPI
  GraphQLAPI -.-> SHMServices
  SHMServices -.-> SelfHostedModelAbs
  SelfHostedModelAbs -.-> GatewayAbstraction
  GatewayAbstraction -.-> Probes
  Probes -.-> CloudConnectors
  RemoteDevIntegration -.-> SelfHostedModelAbs
  ExtIntegrations -.-> SelfHostedModelAbs
  AdminHelpers -.-> SelfHostedModelAbs

  %% =========== CLASS DEFINITIONS ===========
  classDef domcoreconcept fill:#D4F1F9,stroke:#72BFD3,stroke-width:2,rx:16,ry:16
  classDef subdom fill:#E6E6FA,stroke:#AFA2CF,stroke-width:2,rx:16,ry:16
  classDef core fill:#D4F1F9,stroke:#97C6E6,stroke-width:1,rx:14,ry:14
  classDef coremodel fill:#D4F1F9,stroke:#5BACD7,stroke-width:2,rx:14,ry:14
  classDef datastruct fill:#E0F8E0,stroke:#94CFAA,stroke-width:1,rx:14,ry:14
  classDef utility fill:#FFF8DC,stroke:#E9C96E,stroke-width:1,rx:14,ry:14
  classDef error fill:#FFE4E1,stroke:#ED9696,stroke-width:1,rx:14,ry:14
  classDef init fill:#E6E6FA,stroke:#AFA2CF,stroke-width:1,rx:14,ry:14

  %% =========== END OF COMBINED DIAGRAM ===========
```