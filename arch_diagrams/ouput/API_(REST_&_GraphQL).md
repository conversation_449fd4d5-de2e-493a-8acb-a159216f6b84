```mermaid
flowchart TB
%% ---- STYLES ---- %%
classDef coreDomain fill:#D4F1F9,stroke:#6fc3df,stroke-width:2,stroke-linecap:round,rx:14,ry:14
classDef supporting fill:#FFF8DC,stroke:#f7d670,stroke-width:2,stroke-linecap:round,rx:14,ry:14
classDef dataStructure fill:#E0F8E0,stroke:#7acf88,stroke-width:2,stroke-linecap:round,rx:14,ry:14
classDef errorHandling fill:#FFE4E1,stroke:#f8a5a5,stroke-width:2,stroke-linecap:round,rx:14,ry:14
classDef initialization fill:#E6E6FA,stroke:#c0aaff,stroke-width:2,stroke-linecap:round,rx:14,ry:14
classDef groupBG fill:#F8F8F8,stroke:#B6B6F7,stroke-width:3,stroke-dasharray: 4 3
classDef groupREST fill:#F8F8F8,stroke:#A9D4EF,stroke-width:3,stroke-dasharray:6 4
classDef groupGraphQL fill:#F8F8F8,stroke:#D4F1F9,stroke-width:3,stroke-dasharray:8 4
classDef crossDomain fill:#F8F8F8,stroke:#C9DCEB,stroke-width:3,stroke-dasharray:4 3
classDef borderDS fill:#E0F8E0,stroke:#4dbb73,stroke-width:2
classDef borderAbstr fill:#D4F1F9,stroke:#6fc3df,stroke-width:2

%% ======================= DOMAIN ROOT ===================== %%
subgraph Domain_API_Root["API REST & GraphQL Domain"]
    class Domain_API_Root groupBG

    direction TB

    %% ========== REST API SUBDOMAIN ========== %%
    subgraph REST_API["REST API Subdomain"]
        class REST_API groupREST
        direction TB

        REST_EntryPoint["REST API Request Flow"]
        class REST_EntryPoint coreDomain

        REST_Endpoints["Endpoints & Controllers"]
        class REST_Endpoints coreDomain

        REST_Entities["Entities & Serialization"]
        class REST_Entities coreDomain

        REST_Middleware["Middleware & Initialization"]
        class REST_Middleware initialization

        REST_Validation["Parameter Validation"]
        class REST_Validation dataStructure

        REST_Policy["Authorization & Policy"]
        class REST_Policy coreDomain

        REST_Helper["Supporting Utilities & Helpers"]
        class REST_Helper supporting

        REST_DataContract["REST Entity / Data Contract"]
        class REST_DataContract dataStructure
    end

    %% ========== GRAPHQL API SUBDOMAIN ========== %%
    subgraph GQL_API["GraphQL API Subdomain"]
        class GQL_API groupGraphQL
        direction TB

        GQL_Controller["GraphQL API Controller"]
        class GQL_Controller initialization

        GQL_Schema["Schema Engine & API Root Types"]
        class GQL_Schema coreDomain

        GQL_Types["GraphQL Type System & Mapping"]
        class GQL_Types coreDomain

        GQL_Pagination["Connections, Pagination, Redaction"]
        class GQL_Pagination dataStructure

        GQL_Mutation["Mutation & Concerns"]
        class GQL_Mutation supporting

        GQL_Audit["Audit Stream/Event Extensions"]
        class GQL_Audit coreDomain

        GQL_Introspection["Introspection & Internal Queries"]
        class GQL_Introspection supporting

        GQL_DataContract["GraphQL Object Data Shape"]
        class GQL_DataContract dataStructure
    end

    %% ========== CROSS SUBDOMAIN SHARED ABSTRACTIONS ========== %%
    subgraph CrossLayer["Shared Concepts & Core Patterns"]
        class CrossLayer crossDomain
        direction TB

        API_DataFlow["API Domain Model & Request-Response Pattern"]
        class API_DataFlow coreDomain

        API_Contract["Inter-API Entity/Data Contracts"]
        class API_Contract dataStructure

        API_Permissions["Permissions Abstraction / Policy Contracts"]
        class API_Permissions coreDomain

        API_Redaction["Redaction / Privacy Pattern"]
        class API_Redaction supporting

        API_Versioning["API Versioning, Contracts"]
        class API_Versioning supporting
    end

end

%% ======================= RELATIONSHIPS ===================== %%

%% ------ REST API FLOW (abstracted) ------ %%
REST_EntryPoint --> REST_Middleware
REST_EntryPoint --> REST_Endpoints
REST_Endpoints --> REST_Policy
REST_Endpoints --> REST_Validation
REST_Endpoints --> REST_Helper
REST_Endpoints --> REST_Entities
REST_Entities --> REST_DataContract
REST_Entities --> API_DataFlow
REST_DataContract --> API_Contract
REST_Middleware -.-> REST_Helper
REST_Policy --> API_Permissions

%% ------ GraphQL API FLOW (abstracted) ------ %%
GQL_Controller --> GQL_Schema
GQL_Controller --> GQL_Pagination
GQL_Controller --> GQL_Mutation
GQL_Controller --> GQL_Introspection
GQL_Schema --> GQL_Types
GQL_Types --> GQL_Pagination
GQL_Types --> GQL_DataContract
GQL_Pagination --> GQL_DataContract
GQL_Schema --> GQL_Mutation
GQL_Types --> GQL_Audit
GQL_Introspection --> GQL_Schema
GQL_Mutation --> GQL_Types
GQL_Audit --> API_DataFlow
GQL_DataContract --> API_Contract

%% ------ CROSS SUBDOMAIN INTEGRATION ------ %%
REST_DataContract -- Shares Structure --> API_Contract
GQL_DataContract -- Shares Structure --> API_Contract

REST_Policy -- Conceptual Contract --> API_Permissions
GQL_Schema -- Permissions Layer --> API_Permissions
GQL_Mutation -- Redaction Logic --> API_Redaction
GQL_Pagination -- Uses Redaction --> API_Redaction
REST_Helper -- Privacy Filtering --> API_Redaction

REST_DataContract -- Data Output --> API_DataFlow
GQL_DataContract -- Data Output --> API_DataFlow
REST_EntryPoint -- Implements Pattern --> API_DataFlow
GQL_Controller -- Implements Pattern --> API_DataFlow

REST_DataContract -- Versioned As --> API_Versioning
GQL_DataContract -- Versioned As --> API_Versioning

API_Contract -- Contract Boundary --> API_DataFlow

%% ========== CROSS-SUBDOMAIN INFORMATION FLOW ========== %%

API_DataFlow -. Cross-API Compositional Pattern .-> API_Contract

API_Permissions -. Authorization Logic across APIs .-> API_DataFlow
API_Redaction -. Privacy/Redaction shared .-> API_DataFlow
API_Versioning -. API Output .-> API_Contract

%% ========= SYNCHRONIZATION OF PARALLEL FLOWS ========= %%

REST_EntryPoint -. Orchestrates .-> REST_Endpoints
GQL_Controller -. Orchestrates .-> GQL_Schema

REST_Endpoints -. Triggers .-> REST_Entities
GQL_Schema -. Triggers .-> GQL_Types

REST_Policy -. Contract Applies .-> REST_Entities
GQL_Types -. Authorization Applies .-> GQL_Pagination

%% =========== API DOMAIN COHERENCE =========== %%

API_DataFlow ==> Domain_API_Root

%% ======================= LABELS / GROUP FLOW ===================== %%

REST_API -. Implements .-> CrossLayer
GQL_API -. Implements .-> CrossLayer

%% ======================= STYLES FOR CONSISTENCY ===================== %%
style Domain_API_Root fill:#F8F8F8,stroke:#B6B6F7,stroke-width:4,rx:30,ry:30
style REST_API margin:45px
style GQL_API margin:45px
style CrossLayer margin:20px
style REST_API fill:#F8F8F8,stroke:#A9D4EF,stroke-width:3,stroke-dasharray:8 4
style GQL_API fill:#F8F8F8,stroke:#D4F1F9,stroke-width:3,stroke-dasharray:8 4

linkStyle default stroke-width:2,stroke:#ABB,stroke-dasharray:1 6
linkStyle 0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20 stroke-width:2,stroke:#6fc3df
linkStyle 21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39 stroke-width:2,stroke:#7acf88
linkStyle 40,41,42,43,44,45,46,47,48,49,50,51,52,53,54 stroke-width:2,stroke:#f7d670

%% ------ ALIGN TOP TO BOTTOM FOR BOTH SUBDOMAINS ------ %%
REST_API --> GQL_API
GQL_API --> CrossLayer

%% ======================== CLASSES ======================== %%
class REST_EntryPoint,REST_Endpoints,REST_Entities,REST_Policy,REST_DataContract,REST_Helper coreDomain
class REST_Validation dataStructure
class REST_Middleware initialization

class GQL_Controller initialization
class GQL_Schema,GQL_Types,GQL_Audit coreDomain
class GQL_Pagination,GQL_DataContract dataStructure
class GQL_Mutation,GQL_Introspection supporting

class API_DataFlow,API_Permissions coreDomain
class API_Contract dataStructure
class API_Redaction,API_Versioning supporting
```