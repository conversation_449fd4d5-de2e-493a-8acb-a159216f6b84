```mermaid
flowchart TB
%% === COMBINED DOMAIN DIAGRAM: TESTING, QA, LINTING & DEVELOPER TOOLING / TOOLING UTILITIES & SCRIPTING ===
%% Palette:
%% - pastel blue: #D4F1F9 (core/domain logic)
%% - pastel yellow: #FFF8DC (support/utility)
%% - pastel green: #E0F8E0 (domain-wide data structures)
%% - pastel red: #FFE4E1 (error handling)
%% - pastel purple: #E6E6FA (initialization/setup)
%% - light gray for groups: #F8F8F8

%% === GROUPING: MIGRATION & QUALITY TOOLING (CORE SUBDOMAIN) ===
subgraph MGQTL_Zone["Migration & Quality Tooling" [Subdomain: Ensures code, schema, and data quality through analysis, migration support, and checks]]
direction TB
style MGQTL_Zone fill:#F8F8F8,stroke:#D4F1F9,stroke-width:3,rounded

 %% Core Lint & Quality
 QLinting["Code Quality & Linting Engines
    - Code metrics
    - Dynamic coverage
    - Static analysis integration
 "]
 style QLinting fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:10

 %% Migration Control
 MigrationOrch["Migration Orchestration
    - Schema migration runners
    - Migration integrity checks
    - Pre/post schema health validators
 "]
 style MigrationOrch fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rx:10

 %% Housekeeping & Consistency
 SchemaHousekeep["Schema Health & Consistency Utilities
    - Index optimizers
    - DB table/partition cleanup
    - Feature flag expiry & audits
 "
 ]
 style SchemaHousekeep fill:#D4F1F9,stroke:#E6E6FA,stroke-width:2,rx:10

 %% Pipeline Utilities
 PipelineQA["Pipeline, API & Quality Scripting
    - Pipeline status enforcement
    - Query fingerprinting
    - Semgrep result processors
 "]
 style PipelineQA fill:#FFF8DC,stroke:#D4F1F9,stroke-width:2,rx:10

 %% Runtime & QA Support
 QARuntime["Testing & QA Runtimes
    - Execution safety/checks
    - Cross-DB boundaries support
    - Automated runtime tracking
 "]
 style QARuntime fill:#FFF8DC,stroke:#D4F1F9,stroke-width:2,rx:10

 %% Support Libraries
 CoreUtils["Core Utilities & Resetters
    - Token and state resetters
    - Library support for migrations & checks
 "]
 style CoreUtils fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rx:10

 %% Error/Obsolete Handling
 ErrorObsolete["Error/Obsolete Resource Handlers
    - Feature flag/obsolete resource deletion with error control
 "]
 style ErrorObsolete fill:#FFE4E1,stroke:#D4F1F9,stroke-width:2,rx:10

end

%% === DOMAIN-SPANNING DATA STRUCTURES & PATTERNS ===
subgraph DDataStructs["Domain-wide Data Structures" [Domain Entities Shaping Tooling Interactions]]
direction TB
style DDataStructs fill:#F8F8F8,stroke:#E0F8E0,stroke-width:3,rounded

 MigrationDS["Migration States/Files
    - Schema version sets
    - Migration file checksums
    - Partitioning info
 "]
 style MigrationDS fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rx:16

 TableSizeDS["DB Table Sizes & Classifications
    - Table classifications for migration scheduling
    - Used in migration decisions and audits
 "]
 style TableSizeDS fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rx:16

 FeatureFlagDS["Feature Flag Sets & Checksums
    - Tracks enabled/discontinued flags
    - Obsolete lists for auditing
 "]
 style FeatureFlagDS fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rx:16

 CodeMetricsDS["Quality Metrics/Reports
    - Lint/coverage results
    - ABC/Complexity reports
 "]
 style CodeMetricsDS fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rx:16

end

%% === MAJOR FLOW/LOGICAL STRUCTURE OF THE DOMAIN ===
%% Vertical arrangement of domain flow

%% Linting, QA and Scripting feed core data into migration/QA processes
QLinting -- "Provides coverage/metrics" --> CodeMetricsDS
PipelineQA -- "Aggregates pipeline quality data" --> CodeMetricsDS

%% Migration orchestration consumes migration data, table stats, and feature flags
MigrationOrch -- "Reads/Writes migration state" --> MigrationDS
MigrationOrch -- "Utilizes table sizes" --> TableSizeDS
MigrationOrch -- "References feature flag sets" --> FeatureFlagDS

%% Schema Housekeeping processes migration and table size info for health/consistency
SchemaHousekeep -- "Audits and updates" --> MigrationDS
SchemaHousekeep -- "Classifies/updates" --> TableSizeDS
SchemaHousekeep -- "Audits/removes flags" --> FeatureFlagDS

%% Runtime QA uses DB/table structures, interacts with migrations
QARuntime -- "Ensures runtime/DB safety" --> MigrationDS
QARuntime -- "Verifies cross-DB actions" --> TableSizeDS

%% Core utilities support migrations & schema health
CoreUtils -- "Supports operations" --> MigrationOrch
CoreUtils -- "Aids utilities" --> SchemaHousekeep
CoreUtils -- "Supports QA runtime" --> QARuntime

%% Error/Obsolete handling ties with migration and feature flag audits
ErrorObsolete -- "Removes obsolete sets" --> FeatureFlagDS
ErrorObsolete -- "Audits obsolete migrations" --> MigrationDS
ErrorObsolete -- "Reports errors for dev visibility" --> CodeMetricsDS

%% Inter-subdomain, high-level behaviors
QLinting -- "Triggers quality gates for merge/checks" --> PipelineQA
PipelineQA -- "Reports/feeds artifacts" --> MigrationOrch
PipelineQA -- "Invokes runtime checks" --> QARuntime

MigrationOrch -- "Orchestrates migration flow; coordinates audit/utilities" --> SchemaHousekeep
SchemaHousekeep -- "Provides post-migration health; updates data for runtime safety" --> QARuntime

QARuntime -- "Detects safety violations; feedbacks to" --> ErrorObsolete
ErrorObsolete -- "Reports/remediates issues. Notifies" --> QLinting

%% ========== GROUPED RELATIONSHIP LOGICAL FLOW ==========
%% Placing logical group structures to group role and flow

subgraph DomainFlow["Logical Domain Flow"]
direction TB
style DomainFlow fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded
    QLinting -->|ensures code quality| PipelineQA -->|validates build quality| MigrationOrch -->|drives safe migrations| SchemaHousekeep -->|maintains runtime health| QARuntime -->|guards against failures| ErrorObsolete
end

%% Connect data structure use in the overall domain flow
MigrationOrch ---|migration state tables| MigrationDS
SchemaHousekeep ---|audit/modify| MigrationDS
MigrationOrch ---|decision stats| TableSizeDS
SchemaHousekeep ---|classification/updates| TableSizeDS
SchemaHousekeep ---|obsolete checks/flag audits| FeatureFlagDS
ErrorObsolete ---|removes unused flags| FeatureFlagDS
QLinting ---|benchmark/report| CodeMetricsDS
PipelineQA ---|merges with pipeline result sets| CodeMetricsDS
QARuntime ---|QA results/stats| CodeMetricsDS

%% Cross-link Core Utilities as horizontal layer
CoreUtils -.->|setup/cleanup| MigrationOrch
CoreUtils -.->|library/support| SchemaHousekeep
CoreUtils -.->|reset tokens/data| QARuntime

%% Highlight connections between domain data structures and subdomains (feedback and orchestration)
MigrationDS -- feeds state/metadata to --> MigrationOrch
MigrationDS -- updated/validated by --> SchemaHousekeep
FeatureFlagDS -- purged/validated by --> ErrorObsolete
FeatureFlagDS -- referenced in --> SchemaHousekeep
TableSizeDS -- classified/updated by --> SchemaHousekeep
TableSizeDS -- used by --> MigrationOrch
CodeMetricsDS -- used in gates by --> QLinting
CodeMetricsDS -- referenced by --> PipelineQA
CodeMetricsDS -- error/safety logs from --> ErrorObsolete
CodeMetricsDS -- runtime stats from --> QARuntime

%% Final: vertical glue to align high-level grouping with data-structure concepts
MGQTL_Zone === DomainFlow
DomainFlow === DDataStructs

%% RESTYLING summary groupings for higher-level abstraction
style MGQTL_Zone fill:#F8F8F8,stroke:#D4F1F9,stroke-width:3,rounded
style DDataStructs fill:#F8F8F8,stroke:#E0F8E0,stroke-width:3,rounded
style DomainFlow fill:#F8F8F8,stroke:#E6E6FA,stroke-width:3,rounded
```