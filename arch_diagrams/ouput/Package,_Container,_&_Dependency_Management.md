```mermaid
flowchart TB
%% ========= COLOR AND CLASS DEFINITIONS =========
classDef core fill:#D4F1F9,stroke:#68B3C8,stroke-width:2,rx:12,ry:12
classDef coreModel fill:#D4F1F9,stroke:#86B1D8,stroke-width:2,stroke-dasharray: 4 4,rx:12,ry:12
classDef utility fill:#FFF8DC,stroke:#FFD580,stroke-width:2,rx:12,ry:12
classDef datastructure fill:#E0F8E0,stroke:#9CCB9C,stroke-width:2,rx:12,ry:12
classDef error fill:#FFE4E1,stroke:#FFA7A7,stroke-width:2,rx:12,ry:12
classDef init fill:#E6E6FA,stroke:#A999D9,stroke-width:2,rx:12,ry:12
classDef support fill:#FFF8DC,stroke:#EEDD82,stroke-width:2,rx:12,ry:12
classDef group fill:#F8F8F8,stroke:#B6D0E2,stroke-width:2,rx:22,ry:22
classDef group_blue fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rx:20,ry:20
classDef group_green fill:#F8F8F8,stroke:#B1E8D5,stroke-width:2,rx:20,ry:20
classDef group_yellow fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,rx:20,ry:20
classDef group_red fill:#F8F8F8,stroke:#FFE4E1,stroke-width:2,rx:20,ry:20
classDef group_purple fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2,rx:20,ry:20
classDef legend fill:#F8F8F8,stroke:#8CC7E6,stroke-width:2,rx:12,ry:12

%%======== ROOT DOMAIN OVERVIEW ===========
subgraph DOMAIN_ROOT["Package, Container, & Dependency Management" ]
direction TB
class DOMAIN_ROOT group

  DOMAIN_CORE["Core Domain Concepts & Data":::group_blue]
  class DOMAIN_CORE group_blue

  %%==== SUBDOMAIN ABSTRACTS =====
  PKG_REGISTRY["Package Registry":::core]
  CONTAINER_REGISTRY["Container Registry":::core]
  ARTIFACT_CLEANUP["Artifact Storage & Cleanup":::core]
  DEP_LICENSE["Dependency & License Management":::core]
end

%%============= CORE SHARED STRUCTURES ===================
subgraph ABSTRACTIONS["Domain-spanning Data Structures & Key Patterns"]
direction TB
class ABSTRACTIONS group_green
  PKG_ENTITY["Package":::datastructure]
  PKG_FILE_ENTITY["Package/Artifact File":::datastructure]
  CONTAINER_IMAGE_ENTITY["Container Image":::datastructure]
  DEP_GRAPH["Dependency Graph Edges, Occurrences":::datastructure]
  LICENSE_ENTITY["License":::datastructure]
  SBOM_ENTITY["SBOM Component & Occurrence":::datastructure]
  TAG_ENTITY["Tag":::datastructure]
  POLICY_ABS["Access/Retention/Expiration Policy":::datastructure]
end

%% ==== HIGH LEVEL DOMAIN COORDINATION & LIFECYCLE ==========
subgraph DOMAIN_BEHAVIORS["Key Domain Behaviors & Orchestration"]
direction TB
class DOMAIN_BEHAVIORS group_yellow
  ARTIFACT_LIFECYCLE["Artifact/Package Lifecycle":::utility]
  STORAGE_CONTROL["Storage/Upload/Distribution":::utility]
  ACCESS_CONTROL["Access/Authorization Policy":::utility]
  VIRTUAL_REPOS["Federation/Upstream Mirroring":::utility]
  METADATA_MANAGEMENT["Metadata & Metadata Sync":::utility]
  SBOM_FLOW["SBOM or License/Dependency Workflow":::utility]
  CLEANUP_FLOW["Cleanup/Expiration/Destruction":::utility]
  SEARCH_DISCOVERY["Search, Query & Reporting":::utility]
end

%% ========= SUBDOMAIN :: PACKAGE REGISTRY ==============
subgraph PKG_REGISTRY_GROUP["Package Registry" ]
direction TB
class PKG_REGISTRY_GROUP group

  PKG_REGISTRY_CORE["Core Package Registry\ndomain models, files, tags, metadata":::core]
  PKG_REGISTRY_API["Registry APIs/Endpoints":::utility]
  PKG_REGISTRY_WORKERS["Services & Workers\nbackground, lifecycle":::utility]
  PKG_REGISTRY_POLICIES["Registry Policy":::datastructure]
  PKG_REGISTRY_VIRTUAL["Virtual/Upstream Registries":::utility]
end

%% ========= SUBDOMAIN :: CONTAINER REGISTRY ==============
subgraph CONTAINER_GROUP["Container Registry" ]
direction TB
class CONTAINER_GROUP group

  CONTAINER_REPO_CORE["Container Repository\nmodels, tags, blobs":::core]
  CONTAINER_REG_API["Registry API, Adapters/Bridge":::utility]
  CONTAINER_POLICY["Access & Expiration Policy":::datastructure]
  CONTAINER_EVENT["Domain Events & Event Flow":::datastructure]
  CONTAINER_BG["Background Services & Replication":::utility]
end

%% ========= SUBDOMAIN :: ARTIFACT STORAGE & CLEANUP ============
subgraph ARTIFACT_CLEANUP_GROUP["Artifact Storage & Cleanup" ]
direction TB
class ARTIFACT_CLEANUP_GROUP group

  CLEANUP_POLICY["Cleanup Policy & Enforcement":::core]
  CLEANUP_SCHEDULER["Policy Execution & Worker Orchestrators":::utility]
  CLEANUP_UPLOADER["File/Artifact Uploader":::datastructure]
  CLEANUP_STORAGE["Storage/Upload, Routing & Metrics":::datastructure]
end

%% ========= SUBDOMAIN :: DEPENDENCY & LICENSE MGMT ============
subgraph DEP_LICENSE_GROUP["Dependency & License Management" ]
direction TB
class DEP_LICENSE_GROUP group

  LICENSE_CORE["License Compliance Audit/Policy":::core]
  SBOM_CORE["SBOM: Dependency/Component Occurrences":::datastructure]
  LICENSE_EXPORT["Export/Reporting/Serialization":::utility]
  LICENSE_SCAN_ANALYZE["Compliance & Dependency Analysis":::utility]
  LICENSE_POLICY_INFRA["Policy & Error Infrastructure":::error]
end

%%============= RELATIONSHIPS AND FLOW VERTICAL ================

DOMAIN_ROOT --> DOMAIN_CORE
DOMAIN_CORE --> ABSTRACTIONS
DOMAIN_CORE --> DOMAIN_BEHAVIORS

%%--- Connect subdomains to core
DOMAIN_ROOT --> PKG_REGISTRY
DOMAIN_ROOT --> CONTAINER_REGISTRY
DOMAIN_ROOT --> ARTIFACT_CLEANUP
DOMAIN_ROOT --> DEP_LICENSE

%% ====== SUBDOMAIN PURPOSES & DATA SPANS ========
PKG_REGISTRY -- "Repository for software packages (ecosystem-focused: Maven, NPM, Conan, etc).\nManages package lifecycle, metadata, files, policy, versioning." --> PKG_REGISTRY_GROUP

PKG_REGISTRY_GROUP --> PKG_REGISTRY_CORE
PKG_REGISTRY_GROUP --> PKG_REGISTRY_API
PKG_REGISTRY_GROUP --> PKG_REGISTRY_WORKERS
PKG_REGISTRY_GROUP --> PKG_REGISTRY_POLICIES
PKG_REGISTRY_GROUP --> PKG_REGISTRY_VIRTUAL

CONTAINER_REGISTRY -- "Stores and manages container images, repositories, tags, blobs; integrates with CI/CD & geo-replication." --> CONTAINER_GROUP

CONTAINER_GROUP --> CONTAINER_REPO_CORE
CONTAINER_GROUP --> CONTAINER_REG_API
CONTAINER_GROUP --> CONTAINER_POLICY
CONTAINER_GROUP --> CONTAINER_EVENT
CONTAINER_GROUP --> CONTAINER_BG

ARTIFACT_CLEANUP -- "Defines and enforces cleanup/retention for package and artifact storage, orchestrates destruction workflow, implements storage logic." --> ARTIFACT_CLEANUP_GROUP

ARTIFACT_CLEANUP_GROUP --> CLEANUP_POLICY
ARTIFACT_CLEANUP_GROUP --> CLEANUP_SCHEDULER
ARTIFACT_CLEANUP_GROUP --> CLEANUP_UPLOADER
ARTIFACT_CLEANUP_GROUP --> CLEANUP_STORAGE

DEP_LICENSE -- "Tracks dependencies, license compliance, SBOM, export/report flows, integrates with package & container metadata." --> DEP_LICENSE_GROUP

DEP_LICENSE_GROUP --> LICENSE_CORE
DEP_LICENSE_GROUP --> SBOM_CORE
DEP_LICENSE_GROUP --> LICENSE_EXPORT
DEP_LICENSE_GROUP --> LICENSE_SCAN_ANALYZE
DEP_LICENSE_GROUP --> LICENSE_POLICY_INFRA

%% ------- DOMAIN BEHAVIORS TO SUBDOMAINS --------

ARTIFACT_LIFECYCLE --> PKG_REGISTRY_CORE
ARTIFACT_LIFECYCLE --> CONTAINER_REPO_CORE
ARTIFACT_LIFECYCLE --> CLEANUP_POLICY

STORAGE_CONTROL --> PKG_REGISTRY_API
STORAGE_CONTROL --> CLEANUP_UPLOADER
STORAGE_CONTROL --> CONTAINER_REG_API

ACCESS_CONTROL --> PKG_REGISTRY_POLICIES
ACCESS_CONTROL --> CONTAINER_POLICY
ACCESS_CONTROL --> CLEANUP_POLICY
ACCESS_CONTROL --> LICENSE_POLICY_INFRA

VIRTUAL_REPOS --> PKG_REGISTRY_VIRTUAL
VIRTUAL_REPOS --> CONTAINER_REG_API

METADATA_MANAGEMENT --> PKG_REGISTRY_CORE
METADATA_MANAGEMENT --> CONTAINER_REPO_CORE
METADATA_MANAGEMENT --> LICENSE_SCAN_ANALYZE
METADATA_MANAGEMENT --> SBOM_CORE

SBOM_FLOW --> LICENSE_CORE
SBOM_FLOW --> SBOM_CORE
SBOM_FLOW --> LICENSE_EXPORT
SBOM_FLOW --> LICENSE_SCAN_ANALYZE

CLEANUP_FLOW --> ARTIFACT_CLEANUP_GROUP
CLEANUP_FLOW --> CONTAINER_BG
CLEANUP_FLOW --> PKG_REGISTRY_WORKERS

SEARCH_DISCOVERY --> PKG_REGISTRY_API
SEARCH_DISCOVERY --> LICENSE_EXPORT
SEARCH_DISCOVERY --> LICENSE_SCAN_ANALYZE
SEARCH_DISCOVERY --> CONTAINER_REG_API

%% ======================= SHARED DATA STRUCTURES ==========================
%%--- Packages/Container Images/Files/Tags flow across registries ---
PKG_ENTITY -->|Foundation for| PKG_REGISTRY_CORE
PKG_ENTITY -->|Referential| SBOM_CORE
PKG_ENTITY -->|Deletion/Retention Policies| CLEANUP_POLICY

PKG_FILE_ENTITY -->|Artifacts in| PKG_REGISTRY_CORE
PKG_FILE_ENTITY -->|Physical file info| CONTAINER_REPO_CORE
PKG_FILE_ENTITY -->|Upload/cleanup target| CLEANUP_UPLOADER

CONTAINER_IMAGE_ENTITY -->|Model/entities for| CONTAINER_REPO_CORE
CONTAINER_IMAGE_ENTITY --> CONTAINER_REG_API

TAG_ENTITY -->|Versioning in| PKG_REGISTRY_CORE
TAG_ENTITY --> CONTAINER_REPO_CORE
TAG_ENTITY --> LICENSE_SCAN_ANALYZE

LICENSE_ENTITY -->|Compliance for| LICENSE_CORE
LICENSE_ENTITY --> SBOM_CORE
LICENSE_ENTITY --> LICENSE_SCAN_ANALYZE
LICENSE_ENTITY --> LICENSE_POLICY_INFRA

DEP_GRAPH -->|Shared by| PKG_REGISTRY_CORE
DEP_GRAPH -->|Core in| SBOM_CORE
DEP_GRAPH --> LICENSE_SCAN_ANALYZE

SBOM_ENTITY --> SBOM_CORE
SBOM_ENTITY --> LICENSE_EXPORT
SBOM_ENTITY --> LICENSE_SCAN_ANALYZE

POLICY_ABS --> PKG_REGISTRY_POLICIES
POLICY_ABS --> CONTAINER_POLICY
POLICY_ABS --> CLEANUP_POLICY
POLICY_ABS --> LICENSE_POLICY_INFRA

%% ------------ CROSS-SUBDOMAIN DATA & CONTROL FLOW --------------
PKG_REGISTRY_CORE -- "Creates & publishes" --> CLEANUP_UPLOADER
PKG_REGISTRY_CORE -- "Metadata/Dependency info" --> LICENSE_CORE
PKG_REGISTRY_WORKERS -- "Triggers storage cleanups on" --> CLEANUP_SCHEDULER
PKG_REGISTRY_POLICIES -- "Access/media gating" --> CLEANUP_POLICY
PKG_REGISTRY_VIRTUAL -- "Upstream/federation" --> CONTAINER_REG_API

CONTAINER_REPO_CORE -- "Stores image/files" --> CLEANUP_UPLOADER
CONTAINER_REPO_CORE -- "Provides tag/file info to" --> LICENSE_CORE
CONTAINER_REG_API -- "R/W operations use" --> PKG_FILE_ENTITY
CONTAINER_BG -- "Triggers retention/replication via" --> CLEANUP_SCHEDULER
CONTAINER_POLICY -- "Retention/expiration" --> CLEANUP_POLICY
CONTAINER_EVENT -- "Feeds system event/audit to" --> CLEANUP_SCHEDULER

CLEANUP_POLICY -- "Defines rules for" --> CLEANUP_SCHEDULER
CLEANUP_SCHEDULER -- "Orchestrates jobs using" --> CLEANUP_UPLOADER
CLEANUP_UPLOADER -- "Operates on - file, image, artifact" --> PKG_FILE_ENTITY
CLEANUP_STORAGE -- "Metrics/Tracking on" --> PKG_ENTITY

LICENSE_CORE -- "Receives dep info from" --> PKG_REGISTRY_CORE
LICENSE_CORE -- "Correlates with" --> CONTAINER_REPO_CORE
LICENSE_CORE -- "Enforces policy via" --> LICENSE_POLICY_INFRA
LICENSE_CORE -- "Analyzes graph from" --> DEP_GRAPH
LICENSE_CORE -- "Attaches license findings to" --> SBOM_CORE
LICENSE_CORE -- "Feeds SBOM to" --> LICENSE_EXPORT

SBOM_CORE -- "Links package/image/component to" --> PKG_ENTITY
SBOM_CORE -- "Refer dependency chain" --> DEP_GRAPH
SBOM_CORE -- "Used by" --> LICENSE_EXPORT
SBOM_CORE -- "Scanned by" --> LICENSE_SCAN_ANALYZE
SBOM_CORE -- "Governed by policy" --> LICENSE_POLICY_INFRA

LICENSE_EXPORT -- "Pulls SBOM/License/Dep" --> SBOM_CORE
LICENSE_EXPORT -- "Generates reports for" --> PKG_REGISTRY_CORE
LICENSE_EXPORT -- "Enforces restrictions from" --> LICENSE_POLICY_INFRA

LICENSE_SCAN_ANALYZE -- "Validates package/image" --> PKG_ENTITY
LICENSE_SCAN_ANALYZE -- "Parses SBOM graph" --> SBOM_CORE
LICENSE_SCAN_ANALYZE -- "Surfaces findings for" --> LICENSE_EXPORT
LICENSE_SCAN_ANALYZE -- "Policy checks" --> LICENSE_POLICY_INFRA

%% =====// COLLABORATION HIGHLIGHT//====
PKG_REGISTRY_GROUP -- "Shared package models and policies connect to" --> CONTAINER_GROUP
CONTAINER_GROUP -- "Blob/Tag/Retention/Replica patterns reused by" --> ARTIFACT_CLEANUP_GROUP
ARTIFACT_CLEANUP_GROUP -- "Lifecycle, removal flow and storage logic central for" --> PKG_REGISTRY_GROUP
DEP_LICENSE_GROUP -- "Consumes metadata, SBOM, dep info from" --> PKG_REGISTRY_GROUP
DEP_LICENSE_GROUP -- "Validates, reports on packages/images from" --> CONTAINER_GROUP
DEP_LICENSE_GROUP -- "Enforces cleanup/rules with" --> ARTIFACT_CLEANUP_GROUP

%% ====== KEY ABSTRACTIONS AND FLOW ======
ARTIFACT_LIFECYCLE -- "Drives orchestration across all subdomains" --> {PKG_REGISTRY_GROUP, CONTAINER_GROUP, ARTIFACT_CLEANUP_GROUP, DEP_LICENSE_GROUP}
STORAGE_CONTROL -- "Coordinates artifact/image uploads, references PKG_FILE_ENTITY and CONTAINER_IMAGE_ENTITY" --> {PKG_REGISTRY_GROUP, CONTAINER_GROUP}
ACCESS_CONTROL -- "Governed by policies and enforced at registry, storage, and compliance layers" --> {PKG_REGISTRY_GROUP, CONTAINER_GROUP, ARTIFACT_CLEANUP_GROUP, DEP_LICENSE_GROUP}
CLEANUP_FLOW -- "Unifies worker/job orchestration and destruction for artifact/package/image data" --> {ARTIFACT_CLEANUP_GROUP, PKG_REGISTRY_WORKERS, CONTAINER_BG}

%%========= VIRTUAL LAYER: LIFECYCLE EVENTS/INTEGRATION =============
CONTAINER_EVENT -- "Surface registry and storage changes to compliance and reporting" --> DEP_LICENSE_GROUP
CLEANUP_SCHEDULER -- "Processes events and job queues triggered by registry actions" --> {PKG_REGISTRY_GROUP, CONTAINER_GROUP}

%%======== LEGEND ==========
subgraph LEGEND["Legend & Abstraction Map"]
direction TB
style LEGEND fill:#F8F8F8,stroke:#8CC7E6,stroke-width:1.5,rx:12,ry:12
l1_core["Core domain/model":::core]
l2_data["Data structure":::datastructure]
l3_util["Domain utility":::utility]
l4_error["Error/policy infra":::error]
l5_init["Process/init":::init]
end

%%=== NODE STYLINGS (MAIN GROUPS) ===
class DOMAIN_ROOT group
class PKG_REGISTRY_GROUP,CONTAINER_GROUP,ARTIFACT_CLEANUP_GROUP,DEP_LICENSE_GROUP group

class PKG_REGISTRY,CONTAINER_REGISTRY,ARTIFACT_CLEANUP,DEP_LICENSE core
class PKG_REGISTRY_CORE,CONTAINER_REPO_CORE,CLEANUP_POLICY,LICENSE_CORE core
class PKG_REGISTRY_API,PKG_REGISTRY_WORKERS,PKG_REGISTRY_VIRTUAL,CONTAINER_REG_API,CONTAINER_BG,CLEANUP_SCHEDULER,LICENSE_EXPORT,LICENSE_SCAN_ANALYZE utility
class PKG_REGISTRY_POLICIES,CONTAINER_POLICY,CLEANUP_UPLOADER,CLEANUP_STORAGE,CONTAINER_EVENT datastructure
class LICENSE_POLICY_INFRA error
class SBOM_CORE datastructure

class DOMAIN_BEHAVIORS group_yellow
class ABSTRACTIONS group_green

class l1_core core
class l2_data datastructure
class l3_util utility
class l4_error error
class l5_init init
```