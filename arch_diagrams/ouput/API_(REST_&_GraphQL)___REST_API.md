```mermaid
flowchart TB
  %% MAIN DOMAIN OVERVIEW
  subgraph API_REST_GraphQL["API REST & GraphQL → REST API"]
    direction TB
    style API_REST_GraphQL fill:#F8F8F8,stroke:#AED7E0,stroke-width:4,stroke-dasharray: 8 4,rx:18,ry:18

    %% SUBDOMAIN 1: CONTROLLERS, ENDPOINTS & VALIDATIONS (Main Entry point)
    subgraph Subdomain_Controllers["Controllers, Endpoints & Validations"]
      direction TB
      style Subdomain_Controllers fill:#F8F8F8,stroke:#A9D4EF,stroke-width:2,stroke-dasharray:6 3,rx:14,ry:14
      
      RESTAPIEntry[REST API Request Entry<br>& Routing\nBase API Classes, Routing, Request Lifecycle]:::core
      EndpointsAbstr[Endpoints & Actions\nRESTful endpoints, Core Action Controllers]:::core
      PolicyAuth[Authorization & Policy\nPolicy Abstractions]:::core
      ParameterValid[Parameter Validations\nDomain-specific API\nParameter Validators]:::data
      MiddlewareStack[API Middleware Layer\nSecurity, Initialization, Context, Static]:::init
      EndpointHelpers[Endpoint Helper Abstractions]:::support
      APIUtilities[Supporting Utilities\nRate Limiting, Add-ons, EE Integrations]:::support
      style RESTAPIEntry fill:#D4F1F9,stroke:#7EB6DA,stroke-width:2
      style EndpointsAbstr fill:#D4F1F9,stroke:#7EB6DA,stroke-width:2
      style PolicyAuth fill:#D4F1F9,stroke:#7EB6DA,stroke-width:2
      style ParameterValid fill:#E0F8E0,stroke:#8BC195,stroke-width:2
      style MiddlewareStack fill:#E6E6FA,stroke:#C0B7DD,stroke-width:2
      style EndpointHelpers fill:#FFF8DC,stroke:#FCE5AD,stroke-width:2
      style APIUtilities fill:#FFF8DC,stroke:#FCE5AD,stroke-width:2
    end

    %% SUBDOMAIN 2: ENDPOINTS & HELPERS (API Specification)
    subgraph Subdomain_Endpoints["REST API Endpoints & Helpers"]
      direction TB
      style Subdomain_Endpoints fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,stroke-dasharray:6 3,rx:14,ry:14
      
      EndpointSpecs[REST API Endpoint Classes\nProject/User/CI/Container/Etc]:::core
      HelperModules[API Helper Modules<br/>Auth, Packages, Pagination, Search, Internal, Caching]:::support
      EntityLayerRef[Entity Reference Layer]:::data
      ConcernsLayer[API Concerns Shared Logic]:::support
      UtilityRateLimit[API Utilities<br/>Rate Limiting, Endpoint Discovery]:::support
      InitSupport[API Initialization<br/>Initializer Patch/Middleware]:::init
      MailHandlers[Mail Notification Handlers<br/>API-related Emails]:::core
      style EndpointSpecs fill:#D4F1F9,stroke:#7EB6DA,stroke-width:2
      style HelperModules fill:#FFF8DC,stroke:#FCE5AD,stroke-width:2
      style ConcernsLayer fill:#FFF8DC,stroke:#FCE5AD,stroke-width:2
      style UtilityRateLimit fill:#FFF8DC,stroke:#FCE5AD,stroke-width:2
      style InitSupport fill:#E6E6FA,stroke:#C0B7DD,stroke-width:2
      style MailHandlers fill:#D4F1F9,stroke:#7EB6DA,stroke-width:2
      style EntityLayerRef fill:#E0F8E0,stroke:#8BC195,stroke-width:2
    end

    %% SUBDOMAIN 3: ENTITIES, SERIALIZERS & DOMAIN DATA
    subgraph Subdomain_Entities["API Entities & Serializers"]
      direction TB
      style Subdomain_Entities fill:#F8F8F8,stroke:#AEEAF5,stroke-width:2,stroke-dasharray:6 3,rx:14,ry:14

      SerializerCore[Serialization Abstractions<br/>Serializer Base Classes, Grape::Entity]:::core
      RichEntities[Entity Object Graph\nAPI Entities, Domain Representation]:::data
      SerializerGroups[Serializer Clusters<br/>MergeRequests, CI, Harbor, Etc]:::support
      APIDataShapes[Data Structures for API Output]:::data
      EntityHelpers[Entity Utility/Request Helpers]:::support
      ConceptClusters[Conceptual Clusters\nMergeRequests, CI Builds, ErrorTracking, Analytics]:::core
      style SerializerCore fill:#D4F1F9,stroke:#AEEAF5,stroke-width:2
      style RichEntities fill:#E0F8E0,stroke:#A8CFA8,stroke-width:2
      style SerializerGroups fill:#FFF8DC,stroke:#F7E5AA,stroke-width:2
      style EntityHelpers fill:#FFF8DC,stroke:#F7E5AA,stroke-width:2
      style ConceptClusters fill:#D4F1F9,stroke:#AEEAF5,stroke-width:2
      style APIDataShapes fill:#E0F8E0,stroke:#A8CFA8,stroke-width:2
    end

    %% SHARED CONCEPTS / CROSS-SUBDOMAIN ABSTRACTIONS
    subgraph CrossDomain["Core Concepts & Data Flow"]
      direction TB
      style CrossDomain fill:#F8F8F8,stroke:#C9DCEB,stroke-width:2,rx:12,ry:12

      APIDataModel[API Domain Model:\nRequest → Endpoint → Policy + Validation → Entity → Serializer → Output]:::core
      DomainContracts[API Contracts & Versioning]:::support
      SharedDataShape[API Entity/Data Contract\nShape shared across all layers]:::data
      style APIDataModel fill:#D4F1F9,stroke:#79B7DD,stroke-width:2
      style DomainContracts fill:#FFF8DC,stroke:#E6CC72,stroke-width:2
      style SharedDataShape fill:#E0F8E0,stroke:#56C569,stroke-width:2
    end
  end

  %% NODE CLASSES
  classDef core fill:#D4F1F9,stroke:#7EB6DA,stroke-width:2,rx:12,ry:12
  classDef data fill:#E0F8E0,stroke:#8BC195,stroke-width:2,rx:12,ry:12
  classDef support fill:#FFF8DC,stroke:#FCE5AD,stroke-width:2,rx:12,ry:12
  classDef error fill:#FFE4E1,stroke:#FF9E99,stroke-width:2,rx:12,ry:12
  classDef init fill:#E6E6FA,stroke:#C0B7DD,stroke-width:2,rx:12,ry:12

  %% ------ RELATIONSHIPS AND FLOW BETWEEN SUBDOMAINS ------

  %% CONTROLLERS/Subdomain_Controllers orchestrates REQUEST FLOW:
  RESTAPIEntry --> MiddlewareStack
  RESTAPIEntry --> EndpointsAbstr
  EndpointsAbstr --> PolicyAuth
  EndpointsAbstr --> ParameterValid
  EndpointsAbstr -.-> EndpointHelpers
  EndpointsAbstr -.-> APIUtilities
  MiddlewareStack -.-> APIUtilities

  %% ENDPOINTS: Define the API surface, reference EndpointSpecs/Helpers:
  RESTAPIEntry --> Subdomain_Endpoints
  EndpointsAbstr --> EndpointSpecs
  EndpointSpecs --> HelperModules
  EndpointSpecs --> ConcernsLayer
  EndpointSpecs --> UtilityRateLimit
  EndpointSpecs --> MailHandlers
  EndpointSpecs --> EntityLayerRef

  %% POLICIES/VALIDATIONS LAYER to ENDPOINTS
  PolicyAuth --> EndpointSpecs
  ParameterValid --> EndpointSpecs

  %% HELPERS cross-reference to entities for output:
  HelperModules --> EntityLayerRef
  EndpointHelpers --> HelperModules

  %% Utilities, Middleware, Initialization are linked to REST API Entry
  APIUtilities -.-> UtilityRateLimit
  APIUtilities -.-> InitSupport

  %% ENTITIES LAYER: maps between domain data and API:
  EndpointSpecs --> RichEntities
  EntityLayerRef --> RichEntities
  RichEntities -.-> APIDataShapes
  APIDataShapes -.-> SerializerGroups
  RichEntities -->|Domain data| ConceptClusters

  %% SERIALIZER ABSTRACTIONS bridge entities → output shapes:
  RichEntities --> SerializerCore
  SerializerCore --> SerializerGroups
  SerializerGroups --> ConceptClusters

  %% ENTITIES: Use EntityHelpers for data composition
  EntityHelpers -.-> RichEntities
  EntityHelpers -.-> SerializerGroups

  %% Serialization pipeline links back to output and contract
  SerializerGroups --> SharedDataShape
  ConceptClusters -->|Aggregated Output| SharedDataShape

  %% SHARED CONCEPT: APIDataModel flows through all:
  RESTAPIEntry --> APIDataModel
  EndpointSpecs --> APIDataModel
  EndpointSpecs --> DomainContracts
  APIDataModel --> SharedDataShape
  SharedDataShape --> SerializerCore

  %% DATA MODEL is the contract layer
  DomainContracts --> SharedDataShape

  %% MailHandlers can reference Entities and cross-domain output
  MailHandlers -.-> SerializerGroups
  MailHandlers -.-> SharedDataShape

  %% InitSupport connected to Middleware and API entry
  InitSupport -.-> MiddlewareStack

  %% Concept Clusters illustrate orchestrated output
  ConceptClusters-->SerializerCore
  ConceptClusters-->APIDataShapes
  ConceptClusters-->SharedDataShape

  %% Cross-Cluster collaboration (MergeRequests, CI, ErrorTracking, Analytics, Harbor, etc.)
  ConceptClusters -.->|Cross-domain Data| APIDataModel

  %% ++++++++ CLUSTER/COLLABORATION ANNOTATIONS ++++++++
  %% Highlight top-to-bottom vertical flow
  API_REST_GraphQL -->|Entry Point| RESTAPIEntry
  RESTAPIEntry -->|Orchestrates endpoints| EndpointSpecs
  EndpointSpecs -->|Composes Entities| RichEntities
  RichEntities -->|Render| SerializerCore
  SerializerCore -->|Produces\ndata contract| SharedDataShape

  %% LATERAL COLLABORATION
  MiddlewareStack -.-> HelperModules
  PolicyAuth -.-> HelperModules
  ParameterValid -.-> HelperModules
  HelperModules -.-> ConcernsLayer

  %% FINAL OUTPUT
  SharedDataShape ==> API_REST_GraphQL

  %% Ensure appropriate spacing and vertical layout
  style Subdomain_Controllers margin:40px
  style Subdomain_Endpoints margin:40px
  style Subdomain_Entities margin:35px
  style CrossDomain margin:35px
```