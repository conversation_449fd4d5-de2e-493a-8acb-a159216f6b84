```mermaid
flowchart TB

%% ================================================================
%% COLOR CLASSES (CONSISTENT BY ROLE)
%% ================================================================
classDef core fill:#D4F1F9,stroke:#6CC3D5,stroke-width:2.5px,rx:14,ry:14
classDef support fill:#FFF8DC,stroke:#FFD981,stroke-width:2.5px,rx:14,ry:14
classDef data fill:#E0F8E0,stroke:#78D678,stroke-width:2.5px,rx:14,ry:14
classDef error fill:#FFE4E1,stroke:#F4BDBD,stroke-width:2.5px,rx:14,ry:14
classDef init fill:#E6E6FA,stroke:#BFA3FA,stroke-width:2.5px,rx:14,ry:14
classDef groupbox fill:#F8F8F8,stroke:#BFD1DF,stroke-width:3px,rx:22,ry:22,stroke-dasharray:0
classDef pastelblue fill:#D4F1F9,stroke:#6CC3D5,stroke-width:2,rx:14,ry:14
classDef pastelgreen fill:#E0F8E0,stroke:#A8E7A0,stroke-width:2,rx:14,ry:14
classDef pastelred fill:#FFE4E1,stroke:#FFA6A6,stroke-width:2,rx:14,ry:14
classDef pastelyellow fill:#FFF8DC,stroke:#FFD981,stroke-width:2,rx:14,ry:14
classDef pastelpurple fill:#E6E6FA,stroke:#BFA3FA,stroke-width:2,rx:14,ry:14

%% ================================================================
%% 0. TOP-LEVEL DOMAIN CONTEXT
%% ================================================================
subgraph DOMAIN["Projects, Groups & Organization Management"]
direction TB
style DOMAIN fill:#F8F8F8,stroke:#4178A5,stroke-width:5px,rx:32,ry:32

DOMAIN_CORE["Core Domain Concepts
- Orgs, Groups, Namespaces, Projects, Membership, Seats":::core]
class DOMAIN_CORE core

%% ================================================================
%% 1. SUBDOMAIN GROUPS
%% ================================================================

%% 1A. ORGANIZATION STRUCTURE, GROUP & NAMESPACE MGMT
subgraph ORG_GROUP["Organization, Group & Namespace Management"]
direction TB
style ORG_GROUP fill:#F8F8F8,stroke:#BFD1DF,stroke-width:3px,rx:20,ry:20
  ORG_ENT["Organization Core Entities, Policies, Members, Details":::core]
  GROUP_ENT["Group/Namespace Core Settings, Quotas, Hierarchy, Analytics":::core]
  NAMESPACE_DS["Key Data Structures
- Organization, Group, Namespace, Membership, Settings":::data]
class ORG_ENT,GROUP_ENT core
class NAMESPACE_DS data
end
class ORG_GROUP groupbox

%% 1B. PROJECT MANAGEMENT
subgraph PROJ_MGMT["Project Management"]
direction TB
style PROJ_MGMT fill:#F8F8F8,stroke:#399FDB,stroke-width:3px,rx:20,ry:20
  PROJECTS_CORE["Project Lifecycle
- Creation, Import, Fork
- Membership, Access, Compliance":::core]
  PROJECT_MEMBERS["Project Membership, Permissions, Access Control":::core]
  PROJECT_STATS["Project Analytics & Insights":::core]
  PROJECT_DATA["Project Data Structures
- Project Entity, Membership, Stats, Import Task":::data]
class PROJECTS_CORE,PROJECT_MEMBERS,PROJECT_STATS core
class PROJECT_DATA data
end
class PROJ_MGMT groupbox

%% 1C. MEMBERSHIP & INVITATIONS
subgraph MEMBER_INV["Membership & Invitations"]
direction TB
style MEMBER_INV fill:#F8F8F8,stroke:#C7CED1,stroke-width:3px,rx:20,ry:20
  MEMBER_MGMT["Membership Mgmt Invitations, Requests, Lifecycle":::core]
  MEMBER_NOTIF["Membership Notifications, Mailers, Reminders":::support]
  MEMBER_POLICIES["Access Policies & Permissions":::core]
  MEMBER_DATA["Membership Data Structures
- Membership, Agreements":::data]
class MEMBER_MGMT,MEMBER_POLICIES core
class MEMBER_NOTIF support
class MEMBER_DATA data
end
class MEMBER_INV groupbox

%% 1D. SEATS & LICENSING MGMT
subgraph SEATS_LIC["Seats & Licensing mgmt"]
direction TB
style SEATS_LIC fill:#F8F8F8,stroke:#B9E7FF,stroke-width:3px,rx:20,ry:20
  SEATS_CORE["Seat & Add-On Assignment, Usage Tracking":::core]
  LICENSE_CORE["Licensing, Promotions, Versioning":::core]
  SEATS_API["API/Worker Layer Seat Actions, Exports, Reconciliation":::init]
  SEATS_DATA["Seat/Licensing Data Structures
- Seat, AddOn, Versions, Status Enums":::data]
  SEATS_ALERTS["Overages, Eligibility, Usage Notifications":::error]
class SEATS_CORE,LICENSE_CORE core
class SEATS_API init
class SEATS_DATA data
class SEATS_ALERTS error
end
class SEATS_LIC groupbox

%% 1E. GROUP/FEDERATION MEMBERSHIP & ANALYTICS
subgraph GROUP_FED["Group Membership, Federation & Analytics"]
direction TB
style GROUP_FED fill:#F8F8F8,stroke:#A9D6EA,stroke-width:3px,rx:20,ry:20
  FED_MEMBERSHIP["Group Membership & Federated Identity SAML/SCIM":::core]
  GROUP_ANALYTICS["Group/Namespace Analytics & Stats":::core]
  GROUP_POLICIES["Group Access Policies & Permission Enforcement":::core]
  GROUP_SHARED_DATA["Key Group Data Structures
- Group, Namespace, Membership, Stats, Settings":::data]
class FED_MEMBERSHIP,GROUP_ANALYTICS,GROUP_POLICIES core
class GROUP_SHARED_DATA data
end
class GROUP_FED groupbox

%% ================================================================
%% 2. SHARED DOMAIN DATA STRUCTURES
%% ================================================================
subgraph SHARED_DS["Shared Data Structures"]
direction TB
style SHARED_DS fill:#F8F8F8,stroke:#78D678,stroke-width:2px,rx:18,ry:18
  ORGANIZATION["Organization":::data]
  GROUP["Group":::data]
  NAMESPACE["Namespace":::data]
  PROJECT["Project":::data]
  MEMBERSHIP["Membership":::data]
  SETTINGS["Setting/FeatureSetting":::data]
  SEAT["Seat":::data]
  ADDON["Add-On":::data]
  LICENSE_ASSIGN["LicenseAssignment":::data]
  ANALYTICS_STAT["Stats/Aggregates":::data]
  SCIM_OBJ["SCIM/SAML/Federated Identity":::data]
  AGREEMENT["Terms/Agreement":::data]
class ORGANIZATION,GROUP,NAMESPACE,PROJECT,MEMBERSHIP,SETTINGS,SEAT,ADDON,LICENSE_ASSIGN,ANALYTICS_STAT,SCIM_OBJ,AGREEMENT data
end

%% ================================================================
%% 3. CROSS-DOMAIN ABSTRACTIONS / PATTERNS Nodes in pastel green
%% ================================================================
subgraph PATTERNS["Domain-wide Key Abstractions & Patterns"]
direction TB
style PATTERNS fill:#F8F8F8,stroke:#A8E7A0,stroke-width:2,rx:18,ry:18
  HIERARCHY_AGG["Hierarchical Aggregates":::pastelgreen]
  POLICY_ENF["Policy/Permission Enforcement":::pastelgreen]
  FED_IDENTITY["Federated Identity Pattern":::pastelgreen]
  ASYNC_WORKERS["Background Processing/Workers":::pastelgreen]
end

%% ================================================================
%% 4. LOGICAL FLOW / RELATIONSHIPS BETWEEN SUBDOMAINS
%% ================================================================

%% Org mgmt is root for groups, groups manage namespaces, groups/namespaces provide context for projects
DOMAIN_CORE --> ORG_GROUP
ORG_GROUP --> GROUP_FED

ORG_GROUP -- "provides group/org context for" --> PROJ_MGMT
ORG_GROUP -- "provides membership, policy to" --> MEMBER_INV
ORG_GROUP -- "provides group membership for" --> SEATS_LIC

PROJ_MGMT -- "uses group, org membership for" --> MEMBER_INV
PROJ_MGMT -- "relates to seat licensing by" --> SEATS_LIC
PROJ_MGMT -- "uses groups/namespaces for scoping" --> GROUP_FED

MEMBER_INV -- "membership entries tracked by" --> SEATS_LIC
MEMBER_INV -- "triggers join/leave for" --> GROUP_FED

SEATS_LIC -- "relies on org/group/project for assignment context" --> SHARED_DS

GROUP_FED -- "influences seat/license assignment" --> SEATS_LIC
GROUP_FED -- "provides analytics for orgs/groups" --> PROJ_MGMT

%% Policies and membership connect everywhere
MEMBER_INV -- "policy boundaries inform" --> PROJ_MGMT
MEMBER_INV -- "policies/notifications for" --> GROUP_FED

%% ================================================================
%% 5. DOMAIN-LEVEL RELATIONSHIPS TO SHARED DATA STRUCTURES & PATTERNS
%% ================================================================

ORG_GROUP -- "owns and updates" --> ORGANIZATION
ORG_GROUP -- "manages/contains" --> GROUP
ORG_GROUP -- "manages/contains" --> NAMESPACE

PROJ_MGMT -- "projects exist in namespace/group/org" --> PROJECT
PROJ_MGMT -- "project membership via" --> MEMBERSHIP
PROJ_MGMT -- "project access controlled by" --> SETTINGS
PROJ_MGMT -- "gathers/returns analytics" --> ANALYTICS_STAT

MEMBER_INV -- "acts on" --> MEMBERSHIP
MEMBER_INV -- "uses org/group/project context for invites" --> ORGANIZATION

SEATS_LIC -- "assigns seats to members in" --> MEMBERSHIP
SEATS_LIC -- "assigns seats/licenses per" --> PROJECT
SEATS_LIC -- "seat/add-on references" --> SEAT
SEATS_LIC -- "license tracks" --> LICENSE_ASSIGN
SEATS_LIC -- "trigger promotions based on" --> GROUP

GROUP_FED -- "federates membership over" --> SCIM_OBJ
GROUP_FED -- "analyzes/aggregates over" --> ANALYTICS_STAT

MEMBER_INV -- "checks agreements for" --> AGREEMENT

%% Key Settings/Features
GROUP_FED -- "applies feature/settings on" --> SETTINGS
MEMBER_INV -- "accepts policy/terms via" --> AGREEMENT

%% Patterns
ORG_GROUP -- "uses" --> HIERARCHY_AGG
GROUP_FED -- "aggregates, queries stats via" --> HIERARCHY_AGG
PROJ_MGMT -- "segments/rolls up data via" --> HIERARCHY_AGG

MEMBER_INV -- "policy checks and enforcement via" --> POLICY_ENF
SEATS_LIC -- "permission checks via" --> POLICY_ENF
PROJ_MGMT -- "policy gates/roles via" --> POLICY_ENF
GROUP_FED -- "permissions, federation config via" --> POLICY_ENF

GROUP_FED -- "federated user identity through" --> FED_IDENTITY
MEMBER_INV -- "external SSO, invitation via" --> FED_IDENTITY
SEATS_LIC -- "honors federation status via" --> FED_IDENTITY

PROJ_MGMT -- "import, event, analytics jobs via" --> ASYNC_WORKERS
SEATS_LIC -- "reconciliation, overages, alerts via" --> ASYNC_WORKERS
GROUP_FED -- "saml/scim sync, member events via" --> ASYNC_WORKERS
MEMBER_INV -- "reminders, mailers, bulk ops via" --> ASYNC_WORKERS
ORG_GROUP -- "default org setup, fallback jobs via" --> ASYNC_WORKERS

%% ================================================================
%% 6. KEY SUBDOMAIN TOUCHPOINTS FINAL COLLABORATIVE LAYER
%% ================================================================

%% Org/Group Namespace touchpoints
ORG_ENT -.-> ORGANIZATION
ORG_ENT -.-> NAMESPACE
ORG_ENT -.-> GROUP
ORG_ENT -.-> SETTINGS
GROUP_ENT -.-> GROUP
GROUP_ENT -.-> NAMESPACE
GROUP_ENT -.-> SETTINGS

%% Project touchpoints
PROJECTS_CORE -. "uses group namespace" .-> GROUP
PROJECTS_CORE -. "project entity" .-> PROJECT
PROJECTS_CORE -. "scoped by org/group" .-> ORGANIZATION
PROJECTS_CORE -. "membership in project" .-> MEMBERSHIP
PROJECT_MEMBERS -. "uses org/group policies" .-> MEMBER_POLICIES
PROJECT_MEMBERS -.-> SETTINGS
PROJECT_STATS -.-> ANALYTICS_STAT

%% Membership & Invites
MEMBER_MGMT -.-> MEMBERSHIP
MEMBER_MGMT -.-> PROJECT
MEMBER_MGMT -.-> GROUP
MEMBER_MGMT -.-> ORGANIZATION
MEMBER_MGMT -.-> SETTINGS
MEMBER_NOTIF -.-> ASYNC_WORKERS

%% Licensing
SEATS_CORE -.-> SEAT
SEATS_CORE -.-> MEMBERSHIP
LICENSE_CORE -.-> LICENSE_ASSIGN
SEATS_API -.-> ASYNC_WORKERS
SEATS_ALERTS -.-> MEMBER_NOTIF

%% Federation
FED_MEMBERSHIP -.-> MEMBERSHIP
FED_MEMBERSHIP -.-> GROUP
FED_MEMBERSHIP -.-> NAMESPACE
FED_MEMBERSHIP -.-> SCIM_OBJ
FED_MEMBERSHIP -.-> SETTINGS

GROUP_ANALYTICS -.-> ANALYTICS_STAT
GROUP_ANALYTICS -.-> ASYNC_WORKERS

GROUP_POLICIES -.-> POLICY_ENF

%% Common shared data between subdomains
MEMBERSHIP -- shared_by --> ORG_ENT
MEMBERSHIP -- shared_by --> PROJECTS_CORE
MEMBERSHIP -- shared_by --> FED_MEMBERSHIP
MEMBERSHIP -- shared_by --> SEATS_CORE
SETTINGS -- shared_by --> PROJECTS_CORE
SETTINGS -- shared_by --> GROUP_ENT
SETTINGS -- shared_by --> FED_MEMBERSHIP

%% Seat Licensing & Promotions
LICENSE_ASSIGN -.-> SEATS_CORE
LICENSE_ASSIGN -.-> SEATS_ALERTS

%% Group/Federation Analytics/Stats
ANALYTICS_STAT -- aggregates --> GROUP
ANALYTICS_STAT -- aggregates --> PROJECT
ANALYTICS_STAT -- aggregates --> ORGANIZATION
ANALYTICS_STAT -- aggregates --> NAMESPACE

%% Data flow: invitations or org/federation can trigger seat assignment changes
MEMBER_INV -- "can cause seat usage/promotion in" --> SEATS_LIC
GROUP_FED -- "federation events can alter seat/membership" --> SEATS_LIC

%% Initialization/setup & error handling touching all subdomains
ASYNC_WORKERS -- "init/maintenance for all subdomains" --> ORG_GROUP
ASYNC_WORKERS -- "init/maintenance for all subdomains" --> PROJ_MGMT
ASYNC_WORKERS -- "init/maintenance for all subdomains" --> MEMBER_INV
ASYNC_WORKERS -- "init/maintenance for all subdomains" --> SEATS_LIC
ASYNC_WORKERS -- "init/maintenance for all subdomains" --> GROUP_FED

%% Error region crosslinks
SEATS_ALERTS -- "usage/eligibility errors reported to" --> MEMBER_NOTIF
SEATS_ALERTS -- "alerts to membership" --> MEMBER_MGMT

%% Policy/Permission cross-branch
POLICY_ENF -- "enforced in" --> MEMBER_POLICIES
POLICY_ENF -- "enforced in" --> GROUP_POLICIES
POLICY_ENF -- "enforced in" --> PROJECT_MEMBERS
POLICY_ENF -- "enforced in" --> SEATS_LIC

%% Abstractions to subdomains cross
HIERARCHY_AGG -- "used by" --> ORG_GROUP
HIERARCHY_AGG -- "used by" --> GROUP_FED
HIERARCHY_AGG -- "used by" --> PROJ_MGMT
HIERARCHY_AGG -- "used by" --> SEATS_LIC

FED_IDENTITY -- "applied in" --> GROUP_FED
FED_IDENTITY -- "applied in" --> MEMBER_INV
FED_IDENTITY -- "applied in" --> SEATS_LIC

%% ================================================================
%% LAYOUT ORGANIZATION
%% ================================================================

DOMAIN_CORE --- ORG_GROUP
ORG_GROUP --- GROUP_FED
ORG_GROUP --- PROJ_MGMT
ORG_GROUP --- MEMBER_INV
ORG_GROUP --- SEATS_LIC

PROJ_MGMT --- MEMBER_INV
PROJ_MGMT --- SEATS_LIC
PROJ_MGMT --- GROUP_FED

MEMBER_INV --- SEATS_LIC
MEMBER_INV --- GROUP_FED

SEATS_LIC --- GROUP_FED

ORG_GROUP --- SHARED_DS
PROJ_MGMT --- SHARED_DS
MEMBER_INV --- SHARED_DS
SEATS_LIC --- SHARED_DS
GROUP_FED --- SHARED_DS

ORG_GROUP --- PATTERNS
GROUP_FED --- PATTERNS
PROJ_MGMT --- PATTERNS
MEMBER_INV --- PATTERNS
SEATS_LIC --- PATTERNS

%% END
```