```mermaid
flowchart TB
%% ===================== COMBINED DOMAIN: SECURITY, COMPLIANCE & POLICIES =====================
%% ========================== HIERARCHY LEVEL: 0 (VERTICAL LAYOUT) ============================

%% ---------------------- LOGICAL GROUPINGS (SUBDOMAINS) ---------------------------------------
subgraph D_CORE["Security, Compliance & Policies Domain"]
direction TB
style D_CORE fill:#F8F8F8,stroke:#355C7D,stroke-width:4,rounded=true

  %% ------ SUBDOMAIN: SECURITY INTEGRATION & ORCHESTRATION ------
  subgraph SIO["Security Integration & Orchestration"]
    direction TB
    style SIO fill:#F8F8F8,stroke:#355C7D,stroke-width:3,rounded=true

    SI_Policies[ "Policy Mgmt & Synchronization" ]
    style SI_Policies fill:#D4F1F9,stroke:#355C7D,stroke-width:2,shape:rounded-rectangle

    SI_Scheduling[ "Policy Scheduling & Execution Layer" ]
    style SI_Scheduling fill:#FFF8DC,stroke:#B39DDB,stroke-width:2,shape:rounded-rectangle

    SI_Enforce[ "Policy Enforcement/Evaluation Engine" ]
    style SI_Enforce fill:#D4F1F9,stroke:#355C7D,stroke-width:2,shape:rounded-rectangle

    SI_Integration[ "CI/CD Action Integration & GraphQL" ]
    style SI_Integration fill:#FFF8DC,stroke:#355C7D,stroke-width:2,shape:rounded-rectangle

    SI_Obs[ "Observability & Analytics" ]
    style SI_Obs fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,shape:rounded-rectangle

    SI_Err[ "Policy Violation/Unenforceable Notification" ]
    style SI_Err fill:#FFE4E1,stroke:#FFE4E1,stroke-width:2,shape:rounded-rectangle

    SI_Data[ "Policy Schedule/Links" ]
    style SI_Data fill:#E0F8E0,stroke:#355C7D,stroke-width:2,shape:rounded-rectangle

    %% Relationships internal summary
    SI_Policies -->|configures| SI_Scheduling
    SI_Scheduling -->|invokes| SI_Enforce
    SI_Policies -->|feeds| SI_Enforce
    SI_Enforce -->|notifies/analytics| SI_Obs
    SI_Scheduling -->|triggers| SI_Integration
    SI_Enforce -->|reports| SI_Err
    SI_Policies -->|manages| SI_Data
  end

  %% ------ SUBDOMAIN: VULNERABILITY & INCIDENT MANAGEMENT ------
  subgraph VIM["Vulnerability & Incident Management"]
    direction TB
    style VIM fill:#F8F8F8,stroke:#3656A8,stroke-width:3,rounded=true

    VIM_Findings[ "Attack Surface & Scan Findings" ]
    style VIM_Findings fill:#D4F1F9,stroke:#8AC6D1,stroke-width:2,shape:rounded-rectangle

    VIM_VulnCore[ "Vulnerability State & Policy" ]
    style VIM_VulnCore fill:#D4F1F9,stroke:#8AC6D1,stroke-width:2,shape:rounded-rectangle

    VIM_Exports[ "Exports, Reports & Analytics" ]
    style VIM_Exports fill:#E0F8E0,stroke:#98DB9A,stroke-width:2,shape:rounded-rectangle

    VIM_Incident[ "Incident Response & Audit Event" ]
    style VIM_Incident fill:#D4F1F9,stroke:#97C3E6,stroke-width:2,shape:rounded-rectangle

    VIM_Workers[ "Async/Background Orchestration" ]
    style VIM_Workers fill:#E6E6FA,stroke:#CBAACB,stroke-width:2,shape:rounded-rectangle

    VIM_GQL[ "Unified API/GraphQL Layer" ]
    style VIM_GQL fill:#FFF8DC,stroke:#B39DDB,stroke-width:2,shape:rounded-rectangle

    %% Key conceptual flows
    VIM_Findings -->|generates state in| VIM_VulnCore
    VIM_VulnCore -->|provides for| VIM_Exports
    VIM_Exports -->|feeds analytics| VIM_Incident
    VIM_VulnCore --> VIM_Incident
    VIM_Workers -->|enforces/bulk ops| VIM_VulnCore
    VIM_Exports --> VIM_GQL
    VIM_Incident --> VIM_Workers
    VIM_Incident --> VIM_GQL
    VIM_Findings --> VIM_Workers
  end

  %% ------ SUBDOMAIN: COMPLIANCE MANAGEMENT ------
  subgraph CM["Compliance Management"]
    direction TB
    style CM fill:#F8F8F8,stroke:#A2CFEF,stroke-width:3,rounded=true

    CM_Audit[ "Audit Event & Compliance Reporting" ]
    style CM_Audit fill:#D4F1F9,stroke:#A2CFEF,stroke-width:2,shape:rounded-rectangle

    CM_License[ "License Scan & Policy Enforcement" ]
    style CM_License fill:#D4F1F9,stroke:#A2CFEF,stroke-width:2,shape:rounded-rectangle

    CM_Workflows[ "Compliance Workflow/Integration" ]
    style CM_Workflows fill:#E6E6FA,stroke:#C1B6E0,stroke-width:2,shape:rounded-rectangle

    CM_CompPolicy[ "Compliance/Approval Policy Abstraction" ]
    style CM_CompPolicy fill:#E0F8E0,stroke:#A6DEB6,stroke-width:2,shape:rounded-rectangle

    CM_Event[ "AuditEvent/Violation Data" ]
    style CM_Event fill:#E0F8E0,stroke:#A6DEB6,stroke-width:2,shape:rounded-rectangle

    %% Core relationships
    CM_Audit -->|logs outcomes| CM_CompPolicy
    CM_License -->|feeds results to| CM_CompPolicy
    CM_Workflows -->|drives/complies with| CM_CompPolicy
    CM_CompPolicy -->|records| CM_Event
  end

  %% ------ SUBDOMAIN: SECRETS & SENSITIVE DATA ------
  subgraph SECRETSDATA["Secrets & Sensitive Data"]
    direction TB
    style SECRETSDATA fill:#F8F8F8,stroke:#a7dbd8,stroke-width:3,rounded=true

    SSD_OTP[ "OTP, 2FA, AuthN & Strategy" ]
    style SSD_OTP fill:#D4F1F9,stroke:#a8c4cb,stroke-width:2,shape:rounded-rectangle

    SSD_Smartcard[ "Smartcard AuthN & Identity" ]
    style SSD_Smartcard fill:#D4F1F9,stroke:#98bdda,stroke-width:2,shape:rounded-rectangle

    SSD_Saml[ "SAML, SSL & Secure Setup" ]
    style SSD_Saml fill:#E6E6FA,stroke:#bbb9e3,stroke-width:2,shape:rounded-rectangle

    SSD_Policy[ "Compliance & Policy Reporting" ]
    style SSD_Policy fill:#D4F1F9,stroke:#355C7D,stroke-width:2,shape:rounded-rectangle

    SSD_Error[ "Invalid Credentials/Error Events" ]
    style SSD_Error fill:#FFE4E1,stroke:#e7b3b7,stroke-width:2,shape:rounded-rectangle

    SSD_Data[ "User/Key/Secure Data Structures" ]
    style SSD_Data fill:#E0F8E0,stroke:#98cca4,stroke-width:2,shape:rounded-rectangle

    %% Flows
    SSD_OTP -->|drives| SSD_Smartcard
    SSD_OTP --> SSD_Data
    SSD_Smartcard --> SSD_Data
    SSD_OTP -->|policy applied| SSD_Policy
    SSD_OTP --> SSD_Error
    SSD_Smartcard --> SSD_Error
    SSD_Policy --> SSD_Error
    SSD_Policy --> SSD_Saml
    SSD_Policy --> SSD_Data
    SSD_Saml --> SSD_Data
    SSD_Data --> SSD_OTP
  end
end


%% ================== DOMAIN-SPANNING CORE CONCEPTS VERTICAL BETWEEN GROUPS ================

%% DOMAIN-SPECIFIC DATA STRUCTURES (central/shared across subdomains)
DS_Vulnerability[ "Vulnerability/Incident Domain Model" ]
style DS_Vulnerability fill:#E0F8E0,stroke:#67c4de,stroke-width:2,shape:rounded-rectangle

DS_AuditEvent[ "AuditEvent / Compliance Log" ]
style DS_AuditEvent fill:#E0F8E0,stroke:#A6DEB6,stroke-width:2,shape:rounded-rectangle

DS_Policy[ "Unified Policy/Rule Approval/Compliance/Enforcement" ]
style DS_Policy fill:#E0F8E0,stroke:#B39DDB,stroke-width:2,shape:rounded-rectangle

DS_UserIdentity[ "User Identity/Sensitive Data" ]
style DS_UserIdentity fill:#E0F8E0,stroke:#98cca4,stroke-width:2,shape:rounded-rectangle

DS_Statistic[ "Cross-domain Statistics/Analytics" ]
style DS_Statistic fill:#E0F8E0,stroke:#E6E6FA,stroke-width:2,shape:rounded-rectangle

%% KEY ABSTRACTIONS & SHARED PATTERNS
PATTERN_PolicySync[ "Policy Synchronization & Orchestration" ]
style PATTERN_PolicySync fill:#FFF8DC,stroke:#A2CFEF,stroke-width:2,shape:rounded-rectangle

PATTERN_RBAC[ "RBAC & Enforcement Patterns" ]
style PATTERN_RBAC fill:#FFF8DC,stroke:#FFD700,stroke-width:2,shape:rounded-rectangle

PATTERN_Analytics[ "Aggregated Analytics/Presentation" ]
style PATTERN_Analytics fill:#FFF8DC,stroke:#E6E6FA,stroke-width:2,shape:rounded-rectangle

PATTERN_EventSourcing[ "Audit Event Sourcing/Streaming" ]
style PATTERN_EventSourcing fill:#FFF8DC,stroke:#97C3E6,stroke-width:2,shape:rounded-rectangle

PATTERN_Async[ "Async & Background Processing" ]
style PATTERN_Async fill:#E6E6FA,stroke:#B39DDB,stroke-width:2,shape:rounded-rectangle

PATTERN_AuthNStrategy[ "Authentication Strategy Pattern" ]
style PATTERN_AuthNStrategy fill:#FFF8DC,stroke:#E3C47E,stroke-width:2,shape:rounded-rectangle

%% =============== RELATIONSHIPS BETWEEN DATA STRUCTURES, ABSTRACTIONS, AND SUBDOMAINS ===============

%% Security Integration & Orchestration
SIO -->|uses| DS_Policy
SIO -->|sync/feeds| PATTERN_PolicySync
SIO -->|metrics| DS_Statistic
SIO -->|notifies on| DS_AuditEvent
SIO --> PATTERN_Async
SIO -->|enforces| PATTERN_RBAC

%% Vulnerability & Incident Mgmt
VIM -->|manages| DS_Vulnerability
VIM -->|creates| DS_AuditEvent
VIM -->|feeds| DS_Statistic
VIM -->|enforces| DS_Policy
VIM --> PATTERN_RBAC
VIM --> PATTERN_Analytics
VIM -->|event sourced| PATTERN_EventSourcing
VIM -->|bulk ops| PATTERN_Async

%% Compliance Management
CM -->|logs| DS_AuditEvent
CM -->|establishes| DS_Policy
CM -->|classifies/compares| DS_Statistic
CM -->|feeds license/report results| DS_Vulnerability
CM -->|enforces policy via| PATTERN_PolicySync
CM --> PATTERN_EventSourcing
CM --> PATTERN_Analytics

%% Secrets & Sensitive Data
SECRETSDATA -->|protects/annotates| DS_UserIdentity
SECRETSDATA -->|feeds audit/violations| DS_AuditEvent
SECRETSDATA -->|detects/feeds policy outcome| DS_Policy
SECRETSDATA --> PATTERN_AuthNStrategy
SECRETSDATA -->|signals failures| PATTERN_Async

%% ----- DATA STRUCTURE CROSS-FLOWS (VERTICAL LOGIC) -----
DS_Policy -->|enforced by| SIO
DS_Policy -->|referenced/triggered in| VIM
DS_Policy -->|defined by| CM
DS_Policy -->|applied in reporting| SECRETSDATA

DS_AuditEvent -->|feeds analytics| CM
DS_AuditEvent -->|emitted by| VIM
DS_AuditEvent -->|reported by| SIO
DS_AuditEvent -->|populates| PATTERN_EventSourcing
DS_AuditEvent -->|audits| SECRETSDATA

DS_Vulnerability -->|state transitions audited| DS_AuditEvent
DS_Vulnerability -->|subject to| DS_Policy
DS_Vulnerability -->|feeds incident & compliance analytics| DS_Statistic
DS_Vulnerability -->|subject to workflow| PATTERN_Async

DS_UserIdentity -->|governs secrets & 2FA| SECRETSDATA
DS_UserIdentity -->|referenced in compliance events| CM

DS_Statistic -->|aggregates data from| SIO
DS_Statistic -->|feeds| PATTERN_Analytics
DS_Statistic -->|visualized in| CM
DS_Statistic -->|alerts/incidents in| VIM

%% ----- ABSTRACTION SHARED FLOWS -----
PATTERN_PolicySync -->|drives| SIO
PATTERN_PolicySync -->|supports| CM
PATTERN_RBAC -->|applied across| SIO
PATTERN_RBAC -->|applied across| VIM
PATTERN_RBAC -->|applied across| CM
PATTERN_Analytics -->|consumes| DS_Statistic
PATTERN_Analytics -->|unifies reporting| VIM
PATTERN_Analytics -->|unifies reporting| CM
PATTERN_EventSourcing -->|gathers| DS_AuditEvent
PATTERN_EventSourcing -->|tracks states| VIM
PATTERN_EventSourcing -->|enables compliance logs| CM
PATTERN_Async -->|executes background| VIM
PATTERN_Async -->|executes background| SIO
PATTERN_Async -->|propagates events| SECRETSDATA
PATTERN_AuthNStrategy -->|organizes logic| SECRETSDATA
PATTERN_AuthNStrategy -->|can enforce policy from| CM

%% ================ VERTICAL DOMAIN FLOW (TOP-LEVEL) ===================
SIO -. orchestrates policy rules and feeds enforcements .-> VIM
VIM -. triggers compliance/audit and analytics .-> CM
SECRETSDATA -. emits secure auth events and triggers policy audits .-> CM
SECRETSDATA -. enforces/enhances policy on auth .-> SIO
CM -. updates policy and violation lists .-> SIO
VIM -. state transitions triggered by secrets/auth events .-> SECRETSDATA

%% ====== GROUPING & LAYOUT =========
%% Anchor for clean vertical spacing separating logical layers
Z_SPACING1[ ]:::core
Z_SPACING2[ ]:::core

%% =============== LEGEND FOR COLOR (NOT RENDERED, FOR MAINTAINING CONSISTENCY) =====================
%% Core domain: #D4F1F9, Supporting: #FFF8DC, Data: #E0F8E0, Error: #FFE4E1, Init: #E6E6FA, Grouping: #F8F8F8
```