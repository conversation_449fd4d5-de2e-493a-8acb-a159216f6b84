```mermaid
flowchart TD
%% ============== STYLE DEFINTIONS ===============
classDef domain fill:#F8F8F8,stroke:#5bc0eb,stroke-width:4,rx:50,ry:50
classDef subdomain fill:#F8F8F8,stroke:#89D2EB,stroke-width:3,rx:30,ry:30
classDef core fill:#D4F1F9,stroke:#B3E5FC,stroke-width:2,color:#223,rx:18,ry:18
classDef util fill:#FFF8DC,stroke:#F4E6A2,stroke-width:2,color:#223,rx:18,ry:18
classDef datastruct fill:#E0F8E0,stroke:#B2DFDB,stroke-width:2,color:#223,rx:18,ry:18
classDef error fill:#FFE4E1,stroke:#FFB6B6,stroke-width:2,color:#400,rx:18,ry:18
classDef init fill:#E6E6FA,stroke:#C3B1E1,stroke-width:2,color:#223,rx:18,ry:18
classDef abstract fill:#D6F4E3,stroke:#57B794,stroke-width:2,color:#223,rx:18,ry:18

%% =========== DOMAIN ROOT =============
subgraph InfraDomainMain["Infrastructure, Performance & Operations" ]
direction TB
class InfraDomainMain domain

%% =========================================================================
%% SUBDOMAINS TOP LEVEL LOGICAL GROUPINGS
%% =========================================================================

  subgraph SubdomainCaching["Caching & Redis"]
    direction TB
    class SubdomainCaching subdomain

    CachingFoundation["Redis & Cache Core Abstractions"]:::core
    RedisPatternsKey["Cluster/Multistore/Composed Patterns"]:::core
    InfrastructurePerfControls["Performance Controls & Rate Limiting"]:::core
    RedisSharedData["Unified Redis Domain Data Structures"]:::datastruct
    CachingPublicAPI["Domain Caching APIs"]:::core
    RedisRequestState["Request-local Caching & Memoization"]:::util
    RedisInit["Instrumentation & Connection Setup"]:::init
    RedisFallback["Cache/Redis Error Handling"]:::error
  end

  subgraph SubdomainClusters["Cluster & Cloud Integration"]
    direction TB
    class SubdomainClusters subdomain

    ClusterDomainCore["Cluster/Agent/Provider Abstractions"]:::core
    ClusterPolicyAccess["Authentication & Policy Models"]:::core
    KubeInfraAbstractions["K8s Resource/Service Abstractions"]:::util
    CloudAdapters["Cloud Provider Connectors"]:::core
    ClusterInit["Cluster/Agent Lifecycle, Init & Workers"]:::init
    SharedClusterData["Cluster-Agent Managed Domain Data"]:::datastruct
    RemoteDevIntegration["Remote Dev/Workspace Integration"]:::util
    ClusterErrorHandling["Cluster/Cloud Error Handling"]:::error
  end

  subgraph SubdomainStorageCDN["Object Storage & CDN"]
    direction TB
    class SubdomainStorageCDN subdomain

    StorageAPICore["Object Storage & CDN API Layer"]:::core
    UploadMgt["Direct Upload & File Management"]:::core
    StorageAdapterPattern["StorageAdapter/Upload Middleware"]:::core
    StorageDomainStructs["Object/Upload/CDN Domain Structures"]:::datastruct
    CDNDomainMgmt["CDN/Certificate/SSL Handler"]:::util
    StorageInit["Storage/CDN Initialization & Config"]:::init
    StorageUtil["File & Asset Utility Helpers"]:::util
    StorageError["Storage/CDN Error Routing"]:::error
  end

%% =========================================================================
%% CROSS-DOMAIN & SHARED STRUCTURES
%% =========================================================================

  subgraph CrossDomainStructures["Shared Data Structures & Abstractions"]
    direction TB
    class CrossDomainStructures subdomain
    
    UnifiedPendingUploads["Pending Uploads/Meta Stores Redis/Cluster"]:::datastruct
    CrossSubdomainTokens["Agent/Cluster/Upload Token & Session"]:::datastruct
    CDNDomainStruct["CDN Domain + SSL Cert"]:::datastruct
    InfraPolicy["Access/Authorization Policy Abstraction"]:::core
    CrossDomainError["Infra Error Propagation"]:::error
  end

%% =========================================================================
%% CORE LOGIC & DATA FLOW: CONNECTIONS, ABSTRACTIONS, FLOW
%% =========================================================================

  %% Caching & Redis Core Structure
  CachingFoundation --> RedisPatternsKey
  CachingFoundation -.uses shared.-> RedisSharedData
  RedisPatternsKey ---> CachingPublicAPI
  CachingPublicAPI -- "abstracts" --> RedisSharedData
  CachingPublicAPI -- "uses" --> RedisRequestState
  InfrastructurePerfControls --> CachingPublicAPI
  InfrastructurePerfControls --"enforces /rate-limiting/"-->RedisSharedData
  CachingFoundation --> RedisInit
  CachingPublicAPI --> RedisFallback
  RedisFallback -.fallbacks for.-> RedisSharedData

  %% Cluster & Cloud Integration Core Structure
  ClusterDomainCore --> CloudAdapters
  ClusterDomainCore --> ClusterPolicyAccess
  ClusterDomainCore --> KubeInfraAbstractions
  ClusterDomainCore --> SharedClusterData
  ClusterDomainCore --> ClusterInit
  ClusterDomainCore --> ClusterErrorHandling
  CloudAdapters --> ClusterDomainCore
  KubeInfraAbstractions --manages--> SharedClusterData
  ClusterDomainCore --> RemoteDevIntegration
  RemoteDevIntegration -.binds configs.-> SharedClusterData
  ClusterDomainCore --> ClusterPolicyAccess
  ClusterPolicyAccess --uses policy on--> ClusterDomainCore
  ClusterInit --operates/agrees/with--> ClusterDomainCore

  %% Object Storage & CDN Core Structure
  StorageAPICore --> StorageAdapterPattern
  StorageAdapterPattern -.unified interface.-> StorageDomainStructs
  UploadMgt ---> StorageAPICore
  UploadMgt --"tracks/persists"--> StorageDomainStructs
  StorageAPICore -- "CDN via" --> CDNDomainMgmt
  CDNDomainMgmt --> StorageDomainStructs
  StorageUtil -.helpers for.-> StorageAPICore
  StorageUtil -.helpers.-> UploadMgt
  StorageAdapterPattern --raises--> StorageError
  StorageAPICore --> StorageError
  StorageInit --> StorageAPICore
  UploadMgt --fails on error--> StorageError

  %% Cross-subdomain: Collaboration & Shared Domain Structures
  SharedClusterData --"links tokens/sessions to uploads"--> UnifiedPendingUploads
  RedisSharedData --"backs upload meta"--> UnifiedPendingUploads
  UnifiedPendingUploads --"index for uploads"--> StorageDomainStructs
  StorageDomainStructs -- "store upload meta/tokens"--> CrossSubdomainTokens
  SharedClusterData --"reference cluster agent tokens"--> CrossSubdomainTokens
  ClusterDomainCore --authorizes--> InfraPolicy
  CachingPublicAPI -- "policy checked by" --> InfraPolicy
  StorageAPICore -- "policy checked" --> InfraPolicy
  InfraPolicy --"applies to"--> [ClusterDomainCore, CachingFoundation, StorageAPICore]
  CDNDomainMgmt -- "provides public endpoints" --> StorageDomainStructs
  CDNDomainMgmt --"shared cert domain"--> CDNDomainStruct
  StorageDomainStructs --"CDN meta"--> CDNDomainStruct
  ClusterDomainCore -- "contributes domain" --> CDNDomainStruct

  %% Domain-wide Initialization/Config
  StorageInit -- "sync" ---> ClusterInit
  ClusterInit --"init context"-->RedisInit
  StorageInit -- "boot sync" --.-> RedisInit
  RedisInit --"setup instrumentation"--> CachingFoundation
  ClusterInit --"enables agents"--> ClusterDomainCore

  %% Error Handling, Propagation, & Fallback
  CrossDomainError -- "bubbles up from" ---> [RedisFallback, ClusterErrorHandling, StorageError]
  RedisFallback -- "provides fallback for" --> CachingPublicAPI
  ClusterErrorHandling -- "handles" --> ClusterDomainCore
  StorageError -- "routes to domain error" --> CrossDomainError
  
%% =========================================================================
%% COLLABORATION & FLOW BETWEEN SUBDOMAINS: KEY INTERACTIONS
%% =========================================================================

  %% Cluster agents use redis-powered state for rate limiting/caching/policy
  ClusterDomainCore -.uses session/cache via.-> CachingFoundation
  ClusterInit -.requires caches.-> RedisInit
  ClusterDomainCore -- "stores deployment state in" --> RedisSharedData
  ClusterDomainCore --"publishes events via"--> RedisSharedData
  SharedClusterData --"cross-subdomain meta store"--> RedisSharedData

  %% Object Storage leverages caching for upload flows, uses tokens/refs from cluster/agent
  UploadMgt --"verifies cluster/agent token"--> CrossSubdomainTokens
  StorageAdapterPattern -.cache upload meta.-> RedisSharedData
  StorageAPICore -- "validates upload capability via" --> InfraPolicy
  StorageDomainStructs -- "may sync with cluster for CDN domain namespace" --> SharedClusterData
  StorageDomainStructs -- "publish CDN for cluster deployments" --> CDNDomainStruct

  %% High-level: Shared Error, Initialization, Policy abstractions tie all
  InfraPolicy -- "crosses all subdomains" --> [CachingFoundation, ClusterDomainCore, StorageAPICore]
  CrossDomainError -- "collects errors" --> [RedisFallback, ClusterErrorHandling, StorageError]

%% VISUAL SPACERS FOR READABILITY
  style InfraDomainMain fill:#F8F8F8,stroke:#5bc0eb,stroke-width:4
  style SubdomainCaching fill:#F8F8F8,stroke:#89D2EB,stroke-width:3
  style SubdomainClusters fill:#F8F8F8,stroke:#37BFF5,stroke-width:3
  style SubdomainStorageCDN fill:#F8F8F8,stroke:#D4F1F9,stroke-width:3
  style CrossDomainStructures fill:#F8F8F8,stroke:#77d2a6,stroke-width:3

%% =========================================================================
%% MINI-LEGEND: Color/Role key
%% =========================================================================
subgraph Legend ["Legend"]
direction TB
L_Core["Core domain concept"]:::core
L_Util["Utility/support"]:::util
L_Data["Domain data structure"]:::datastruct
L_Error["Error/fallback path"]:::error
L_Init["Initialization/setup"]:::init
L_Abstract["Domain abstraction/pattern"]:::abstract
end
```