```mermaid
flowchart TB
  %%---- CLASSES ----%%
  classDef coreDomain fill:#D4F1F9,stroke:#6fc3df,stroke-width:2,stroke-dasharray: 2 2,color:#222,stroke-linecap:round
  classDef utility fill:#FFF8DC,stroke:#f7d670,stroke-width:2,stroke-dasharray: 2 2,color:#222,stroke-linecap:round
  classDef dataStructure fill:#E0F8E0,stroke:#7acf88,stroke-width:2,stroke-dasharray: 4 2,color:#222,stroke-linecap:round
  classDef errorHandling fill:#FFE4E1,stroke:#f8a5a5,stroke-width:2,stroke-dasharray: 2 2,color:#222,stroke-linecap:round
  classDef initialization fill:#E6E6FA,stroke:#c0aaff,stroke-width:2,stroke-dasharray: 2 2,color:#222,stroke-linecap:round
  classDef groupBG fill:#F8F8F8,stroke:#B6B6F7,stroke-width:3,stroke-dasharray: 3 3
  classDef groupEE fill:#F8F8F8,stroke:#c0aaff,stroke-width:3,stroke-dasharray: 8 4
  classDef groupUtility fill:#F8F8F8,stroke:#f7d670,stroke-width:3,stroke-dasharray: 2 4

  %% --- GRAPHQL API DOMAIN --- %%
  subgraph DOMAIN["GraphQL API Domain" ]
    class DOMAIN groupBG

    %% Core Schema Engine & Definition
    subgraph SCHEMA["Schema Engine & Definition" ]
      class SCHEMA groupBG
      SchemaCore["GraphQL Schema Core" ]
      class SchemaCore coreDomain
      DeprecationHandling["Deprecations & Type Name Deprecations" ]
      class DeprecationHandling utility
      RequestTimeouts["Query Execution Timeout" ]
      class RequestTimeouts errorHandling
      OperationRegistry["Known Operation Registry" ]
      class OperationRegistry utility
      SchemaCore --- DeprecationHandling
      SchemaCore --- RequestTimeouts
      SchemaCore --- OperationRegistry
    end

    %% Top-level Root Types & Extensions
    subgraph ROOTTYPES["API Root Types & Extensions" ]
      class ROOTTYPES groupBG
      Query["Query Type" ]
      class Query coreDomain
      Mutation["Mutation Type" ]
      class Mutation coreDomain
      Subscription["Subscription Type" ]
      class Subscription coreDomain
      EERootTypes["EE Root Types Extensions" ]
      class EERootTypes coreDomain
      Query --- EERootTypes
      Mutation --- EERootTypes
      Subscription --- EERootTypes
      ROOTTYPES -- uses --> SchemaCore
      ROOTTYPES --- Query
      ROOTTYPES --- Mutation
      ROOTTYPES --- Subscription
    end

    %% Type System + Domain Object Mapping
    subgraph TYPES["Type System, Interfaces & Domain Mapping"]
      class TYPES groupBG
      DomainTypes["Domain-Specific Types & Interfaces" ]
      class DomainTypes coreDomain
      PermissionsTypes["Permission Abstractions" ]
      class PermissionsTypes coreDomain
      EETypeExt["EE-Specific Type Extensions" ]
      class EETypeExt coreDomain
      DomainTypes --- PermissionsTypes
      EETypeExt --- DomainTypes
      EETypeExt --- PermissionsTypes
      TYPES -- depends --> ROOTTYPES
    end

    %% Pagination, Connections, Data Structure Abstractions
    subgraph PAGINATION["Data Structures, Pagination & Connection Extensions"]
      class PAGINATION groupBG
      PaginationArray["ActiveRecord Connection Array" ]
      class PaginationArray dataStructure
      OffsetPagination["Offset-based Pagination" ]
      class OffsetPagination dataStructure
      RedactionModule["Connection Redaction Utility" ]
      class RedactionModule utility
      PresentFieldExt["Field/Node Presentation Extensions" ]
      class PresentFieldExt utility
      GitalyFieldExt["Gitaly Field Extensions" ]
      class GitalyFieldExt utility
      DomainConnectionExt["Domain-specific Connection Extensions" ]
      class DomainConnectionExt utility
      ExternallyPaginated["External Pagination Extension" ]
      class ExternallyPaginated utility
      PaginationArray --- OffsetPagination
      PaginationArray --- RedactionModule
      OffsetPagination --- RedactionModule
      PresentFieldExt --- DOMAIN
      DomainConnectionExt --- PresentFieldExt
      GitalyFieldExt --- DOMAIN
      ExternallyPaginated --- PaginationArray
      PAGINATION -- supports --> TYPES
    end

    %% API Controllers / Entry Points
    subgraph CONTROLLERS["API Controllers & Entry Points"]
      class CONTROLLERS groupBG
      APIGraphQLController["GraphQL API Controller" ]
      class APIGraphQLController initialization
      ExplorerController["GraphQL Explorer Controller" ]
      class ExplorerController initialization
      APIGraphQLController --- SchemaCore
      APIGraphQLController --- PAGINATION
      ExplorerController --- APIGraphQLController
    end

    %% Concerns/Resolvers/Mutations
    subgraph MUTATIONS["Mutation Support & Concerns"]
      class MUTATIONS groupBG
      MutationConcerns["Mutations: Resolves/Concerns" ]
      class MutationConcerns utility
      MutationConcerns --- TYPES
      MutationConcerns --- SCHEMA
      MUTATIONS -- enables --> ROOTTYPES
    end

    %% Audit Events, Streaming and EE
    subgraph AUDIT["Audit Events & Streaming Interfaces EE"]
      class AUDIT groupEE
      AuditStreaming["Audit Event Streaming Interfaces" ]
      class AuditStreaming coreDomain
      AuditDestinations["External Audit Destinations" ]
      class AuditDestinations coreDomain
      AuditCloudProviders["Cloud Integration S3/GCP" ]
      class AuditCloudProviders coreDomain
      AuditStreaming --- AuditDestinations
      AuditStreaming --- AuditCloudProviders
      AUDIT -- extends --> TYPES
    end

    %% Introspection and Query Support
    subgraph INTROSPECTION["Schema Introspection & Internal Queries"]
      class INTROSPECTION groupUtility
      CachedIntrospection["Cached Introspection Query" ]
      class CachedIntrospection coreDomain
      InternalQueries["Predefined Internal Queries" ]
      class InternalQueries utility
      CachedIntrospection --- SchemaCore
      InternalQueries --- SchemaCore
      INTROSPECTION -- supports --> CONTROLLERS
    end

    %% EE Extensions/Triggers
    subgraph EEEXT["Enterprise Event Triggers EE"]
      class EEEXT groupEE
      SubscriptionTriggers["Subscription Triggers EE" ]
      class SubscriptionTriggers coreDomain
      SubscriptionTriggers --- Subscription
      SubscriptionTriggers --- EERootTypes
      SubscriptionTriggers --- OperationRegistry
      EEEXT -- enhances --> ROOTTYPES
    end
  end

  %% ------------------- RELATIONSHIPS BETWEEN GROUPS ------------------- %%

  SCHEMA --> ROOTTYPES
  ROOTTYPES --> TYPES
  ROOTTYPES --> CONTROLLERS
  ROOTTYPES --> MUTATIONS
  TYPES --> PAGINATION
  TYPES --> AUDIT
  TYPES --> EEEXT
  CONTROLLERS --> INTROSPECTION
  CONTROLLERS --> PAGINATION
  CONTROLLERS --> SCHEMA
  PAGINATION --> INTROSPECTION
  MUTATIONS --> TYPES
  AUDIT --> EEEXT
  EEEXT --> ROOTTYPES
  INTROSPECTION --> SCHEMA

  %% ---- BORDER NODES: DOMAIN-SPANNING DATA STRUCTURES & ABSTRACTIONS ---- %%
  DataStructuresDS["Core GraphQL Data Structures" ]
  class DataStructuresDS dataStructure
  SchemaAbstractionDS["Schema & Type Abstractions" ]
  class SchemaAbstractionDS coreDomain
  PermissionAbstractionDS["Permissions Abstraction" ]
  class PermissionAbstractionDS coreDomain
  RedactionPatternDS["Redaction/Privacy Patterns" ]
  class RedactionPatternDS utility

  SchemaCore --- SchemaAbstractionDS
  DomainTypes --- DataStructuresDS
  PermissionsTypes --- PermissionAbstractionDS
  RedactionModule --- RedactionPatternDS
  PaginationArray --- DataStructuresDS
  AuditStreaming --- DataStructuresDS
  SubscriptionTriggers --- DataStructuresDS
  MutationConcerns --- PermissionAbstractionDS

  %% ------ Flow/Control ----- %%
  APIGraphQLController ==> SchemaCore
  APIGraphQLController ==> ROOTTYPES
  APIGraphQLController ==> PAGINATION
  APIGraphQLController ==> Types
  ExplorerController ==> APIGraphQLController
  ROOTTYPES ==> TYPES
  TYPES ==> PAGINATION
  CONTROLLERS ==> INTROSPECTION
  CONTROLLERS ==> EEEXT

  %% ---- STYLES ---- %%
  linkStyle default stroke-width:2,stroke:#AAA,stroke-dasharray:1 4
```