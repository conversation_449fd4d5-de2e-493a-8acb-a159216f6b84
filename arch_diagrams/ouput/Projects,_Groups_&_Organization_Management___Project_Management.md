```mermaid
flowchart TB
%% ================================================
%% MERMAID DIAGRAM: Projects, Groups & Org Mgmt → Project Management (Domain-Composed)
%% ================================================
%% Color & Shape Classes
classDef core fill:#D4F1F9,stroke:#4178A5,stroke-width:2,corner-radius:14px
classDef support fill:#FFF8DC,stroke:#FFD981,stroke-width:2,corner-radius:14px
classDef structure fill:#E0F8E0,stroke:#399FDB,stroke-width:2,corner-radius:14px
classDef error fill:#FFE4E1,stroke:#C88787,stroke-width:2,corner-radius:14px
classDef init fill:#E6E6FA,stroke:#BFA3FA,stroke-width:2,corner-radius:14px
classDef boundary fill:#F8F8F8,stroke:#B7CEE3,stroke-width:2,corner-radius:18px
classDef domainLabel fill:#F8F8F8,stroke-width:0

%% ------- TOP-LEVEL DOMAIN CONTEXT -------
subgraph DOMAIN["PROJECT MANAGEMENT  "]
direction TB
style DOMAIN fill:#F8F8F8,stroke:#A7B9CB,stroke-width:4,corner-radius:36px

    CoreProject["CoreProject \npastel blue\n Core concept: A managed project with membership, access, creation, analytics" ]:::core
    class CoreProject domainLabel

    %% Abstracted Core Data Structures (shared)
    Project["Project Entity" ]:::structure
    Membership["Membership / Access" ]:::structure
    ProjectStats["Project Statistics & Insights" ]:::structure
    ProjectEvent["Project Events" ]:::structure
    ProjectAuth["Project Authorization" ]:::structure
    ImportTask["Import / Pipeline Task" ]:::structure

    %% ========== SUBDOMAIN BOUNDARIES ==========

    subgraph D1["Project Creation & Import" ]
    direction TB
    style D1 fill:#F8F8F8,stroke:#D4F1F9,stroke-width:3,corner-radius:18
      PCICore["Project Creation Services" ]:::core
      PCIImport["Project Import Pipeline" ]:::core
      PCIEvents["Creation Events" ]:::structure
      PCIForking["Forking & Targeting" ]:::core
      PCIComplyOnboard["Compliance, Onboarding, Templates" ]:::support
      PCIAbstractions["Shared Abstractions" ]:::init
    end

    subgraph D2["Membership & Access" ]
    direction TB
    style D2 fill:#F8F8F8,stroke:#98CCE7,stroke-width:3,corner-radius:18
      MAData["Membership & Authorization Models" ]:::structure
      MAFinders["Access & Membership Queries" ]:::support
      MAServices["Membership Management Services" ]:::core
      MAPolicy["Policy & Permissions" ]:::init
      MATeams["Project Teams & Roles" ]:::core
      MAEventing["Membership Events" ]:::error
      MAUtilities["Utilities & Mixins" ]:::support
      MAIntegration["EE Extensions/Integrations" ]:::support
    end

    subgraph D3["Project Analytics & Insights" ]
    direction TB
    style D3 fill:#F8F8F8,stroke:#399FDB,stroke-width:3,corner-radius:18
      PAIDomain["Analytics Data Models" ]:::structure
      PAIServices["Analytics Services" ]:::core
      PAIEventBus["Analytics Eventing" ]:::support
      PAIQuery["Query/Presentation/Resolvers" ]:::support
      PAIControllers["Web/API Controllers" ]:::core
      PAIHelpers["Views & Helpers" ]:::support
      PAIWorkers["Processing/Workers" ]:::init
    end

    %% ========== DOMAIN-SPECIFIC DATA STRUCTURE/CORE FLOW LAYER ===========
    ProjectStats -.-> Project
    Project -. membership .-> Membership
    Project -- "has events" --> ProjectEvent
    ProjectEvent -.-> "results in" --> ProjectStats
    Membership -. "authorizes" .-> ProjectAuth
    Project -.-> ImportTask
    Project -.-> ProjectStats
    Membership -.-> ProjectStats
    ImportTask -.-> ProjectEvent

    %% ======== CROSS-SUBDOMAIN, COLLABORATING DATA & LOGIC FLOW ========

    %% Project Creation & Import → Membership & Access
    PCICore -- "on creation" --> PCIEvents
    PCIEvents -- "triggers" -.-> MAData
    PCICore -. "establishes" .-> MAData
    PCIForking -. "generates" .-> MAData
    PCIImport -- "import results in" -.-> PCIEvents
    PCIImport -.-> ImportTask
    PCIEvents -- "fires" --> MAEventing
    PCIEvents -- "triggers authorization" --> ProjectAuth

    PCIComplyOnboard -.-> PCICore
    PCIAbstractions -.-> PCICore
    PCIComplyOnboard -.-> MAData
    PCIComplyOnboard -.-> MAServices

    PCICore --> Project
    PCIImport --> Project
    PCIImport -.-> ImportTask

    %% Project Creation & Import → Analytics & Insights
    PCIEvents -- "emits" --> PAIEventBus
    PCIEvents -.-> ProjectEvent
    PCIEvents -- "fires update" --> PAIDomain

    %% Analytics & Insights -> Membership & Access
    PAIDomain -. "segment/aggregate by" .-> Membership
    PAIDomain -.-> ProjectAuth
    PAIServices -- "uses authorization" --> MAServices
    PAIServices -.-> ProjectStats
    PAIServices --> Project
    PAIServices -- "presents stats to" --> PAIQuery
    PAIQuery -.-> ProjectStats
    PAIServices -- "renders via controllers" ---> PAIControllers

    PAIEventBus -- "domain events" --> PCIEvents
    PAIEventBus --> MAEventing

    %% Membership & Access → Analytics & Insights
    MAData -- "aggregates stats for" --> PAIDomain
    MAServices -- "modifies project member info" --> ProjectStats
    MAServices --> Project
    MAPolicy -- "policy-checks in analytics" --> PAIServices
    MAServices --> PAIServices
    MATeams -- "collaborators to" --> PAIServices
    MAEventing --> PAIEventBus
    MAFinders -.-> PAIQuery

    MATeams -- "team data affects" --> ProjectStats
    MAFinders -- "feed into statistics" --> ProjectStats
    MAIntegration -- "augments membership logic" --> MAServices

    %% Analytics & Insights: Internal flows
    PAIDomain -- "source of" --> ProjectStats
    PAIQuery -- "read/project info" --> ProjectStats
    PAIHelpers --> PAIControllers
    PAIWorkers -- "refreshes" --> ProjectStats

    %% Project Creation & Import: Internal flows
    PCICore -- "calls" --> PCIImport
    PCICore -- "can fork via" --> PCIForking
    PCICore --> PCIComplyOnboard
    PCIImport --> PCIComplyOnboard

    %% Membership & Access: Internal flows
    MAServices --> MAFinders
    MAServices -- "applies policies" --> MAPolicy
    MATeams --> MAData

    %% Common Patterns & Abstractions Shared
    PCIAbstractions -. "shared with" .-> MAPolicy
    PCIAbstractions -. "used for" .-> PAIServices
    PCIAbstractions -.-> MAUtilities

    %% EE Extensions
    MAIntegration -- "extensible/override" -> PCICore
    MAIntegration -- "EE data flows" --> PCIComplyOnboard

    %% Key: Exposure touchpoints
    PAIControllers -- "expose analytics" --> Project
    MAServices -- "expose member mgmt" --> Project

end

%% LAYOUT TWEAKING: Main concept vertical links
CoreProject --- Project
Project --- Membership
Project --- ImportTask
Project --- ProjectStats
Project --- ProjectEvent
Membership --- ProjectAuth
ProjectStats === ProjectEvent

%% --------- STYLES ---------
class DOMAIN boundary
class D1,D2,D3 boundary
class Project, Membership, ProjectStats, ProjectEvent, ProjectAuth, ImportTask structure

class CoreProject core
class PCICore, PCIImport, PCIForking, PCIComplyOnboard, PCICore, PCIEvents, PCIAbstractions core
class MAData, MAFinders, MAPolicy, MATeams, MAServices, MAEventing, MAUtilities, MAIntegration, ProjectAuth structure
class PAIDomain, PAIServices, PAIEventBus, PAIQuery, PAIControllers, PAIHelpers, PAIWorkers structure

class PCIAbstractions, MAPolicy, PAIServices, MAUtilities, MATeams, PAIWorkers, PCIComplyOnboard, MAIntegration, PAIHelpers, PAIEventBus init
class PCIComplyOnboard, MAIntegration, PCIAbstractions, PAIHelpers, PAIServices, MAFinders support
class MAEventing, PCIEvents, ProjectEvent error

%% ================================================
```