```mermaid
flowchart TD

%%============ ROOT DOMAIN ============%%
subgraph DOMAIN["Source Code, Repository & Collaboration: Repository Management"]
  direction TB
  style DOMAIN fill:#F8F8F8,stroke:#D4F1F9,stroke-width:3,rx:18

  %%----------------- SHARED ENTITIES, DATA, AND ABSTRACTIONS ------------%%
  subgraph DOMAIN_CONCEPTS["Core Domain Concepts"]
    direction TB
    style DOMAIN_CONCEPTS fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rx:14

    Project["Project"]:::core
    Repository["Repository"]:::core
    Branch["Branch"]:::core
    Tag["Tag"]:::core
    Commit["Commit"]:::core
    MergeRequest["MergeRequest"]:::core
    ForkNetwork["ForkNetwork"]:::core
    Diff["Diff"]:::core
    DiffRefs["DiffRefs"]:::data
    Suggestion["Suggestion"]:::core
  end

  %%------ Subdomain A: Repositories, Branches & Tags ------%%
  subgraph REPOS_BRANCHES_TAGS["Repositories, Branches & Tags"]
    direction TB
    style REPOS_BRANCHES_TAGS fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rx:12

    RB_Purpose[/"Manages core repositories & logical structure"/]:::support

    RepoModel["ProjectRepository"]:::core
    BranchModel["Branch model"]:::core
    TagModel["Tag model"]:::core
    BranchService["Branch Management Services"]:::core
    TagService["Tag Management Services"]:::core
    ForksService["Forks & PoolRepo Services"]:::core
    RepoApi["Repository Branches/Tags API"]:::support
    GitAbstractions["GitLab::Git Abstractions"]:::support
    LangDetection["Language Detection"]:::support
    RepoWorkers["Repository Background Workers"]:::support
    RepoData["Repository Data Structures"]:::data
    BranchProtection["Branch & Tag Protection"]:::core

    RepoModel -->|owned by| Project
    BranchModel --> RepoModel
    TagModel --> RepoModel
    ForksService --> RepoModel
    BranchService --> BranchModel
    TagService --> TagModel
    BranchService --uses--> BranchProtection
    RepoWorkers --> RepoModel
    RepoApi --> BranchService
    RepoApi --> TagService
    GitAbstractions --low-level ops--> RepoModel
    BranchProtection --protects--> BranchModel
    BranchProtection --protects--> TagModel
    LangDetection --> RepoData
    RepoData --> RepoModel
    ForkNetwork --logical link--> Project
    RepoModel --shares storage with--> ForkNetwork
  end

  %%------ Subdomain B: Commits, Diffs & Merge Requests ------%%
  subgraph COMMITS_DIFFS_MRS["Commits, Diffs & Merge Requests"]
    direction TB
    style COMMITS_DIFFS_MRS fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rx:12

    CDMR_Purpose[/"Enables code collaboration:\ncommits, code review, merge orchestrations"/]:::support

    MRModel["MergeRequest"]:::core
    MRCommit["MR Commits Collection"]:::core
    DiffService["Diff & Viewer System"]:::core
    Mergeability["Mergeability Services"]:::core
    ReviewSystem["Reviews & Discussion System"]:::core
    DiffDiscussion["Diff Discussions"]:::support
    Collaboration["Assignees & Reviewers"]:::core
    MRErrorHandling["MR Error Handling"]:::error
    MRHelpers["Utilities & Finders"]:::support
    MRWidgets["Merge Request Widget/Data"]:::data

    MRModel -->|links code| Branch
    MRModel -->|references| Commit
    MRModel --> MRCommit
    MRCommit --part of--> Diff
    DiffService --> Diff
    DiffService --> DiffRefs
    DiffService --uses--> Suggestion
    Mergeability --> MRModel
    Mergeability --performs checks on--> DiffService
    ReviewSystem --attached to--> MRModel
    ReviewSystem --collaborates via--> Collaboration
    DiffDiscussion --comments on--> Diff
    MRWidgets --serializes--> MRModel
    MRErrorHandling --> Mergeability
    MRHelpers --finders/helpers--> MRModel
  end

  %%---------------------- SHARED DATA STRUCTURES --------------------------%%
  subgraph DOMAIN_DATA["Domain-Specific Data Structures"]
    direction TB
    style DOMAIN_DATA fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2,rx:10

    CommitRange["CommitRange"]:::data
    DiffLineEntity["DiffLineEntity"]:::data
    MRWidgetEntity["MRWidgetEntity"]:::data
    RepoLang["RepositoryLanguage"]:::data
    DiffStats["DiffStatsSummaryType"]:::data
  end

end

%%============ RELATIONSHIPS AND COLLABORATION ===========%%

%% Subdomain Purposes
REPOS_BRANCHES_TAGS -.Lays repository & branch/tag foundation for.-> COMMITS_DIFFS_MRS
COMMITS_DIFFS_MRS -.Orchestrates collaboration atop.-> REPOS_BRANCHES_TAGS

%% KEY LOGICAL RELATIONSHIPS
Project --> Repository
Repository --> Branch
Repository --> Tag
Branch --contains--> Commit
Repository --> ForkNetwork

%% SUBDOMAIN INTERACTIONS
RepoModel --provides codebase for--> MRModel
BranchModel --source/target for--> MRModel
TagModel --used for baseline tagging in--> MRModel
BranchModel --enables history for--> Commit
MRModel --open against--> BranchModel
Commit --inspected by--> DiffService
CommitRange --spans range for--> Diff
Diff --created by--> DiffService
DiffService --consults--> RepoData
MRModel --provides changes for--> ReviewSystem

%% DOMAIN DATA BINDINGS
MRWidgetEntity --serializes--> MRModel
DiffLineEntity --serializes--> Diff
RepoLang --classifies--> Repository
DiffStats --summarizes--> Diff
CommitRange --shared in--> RepoModel & MRModel

%% PATTERN: LOW-LEVEL COLLABORATION - GIT & CORE ABSTRACTIONS
GitAbstractions -.low-level ops/services for.-> BranchModel
GitAbstractions -.low-level ops/services for.-> TagModel
GitAbstractions -.low-level ops/services for.-> RepoModel
GitAbstractions -.low-level diff/blob for.-> DiffService

%% PROTECTION/PERMISSIONS FLOW
BranchProtection --enforces protection--> BranchModel
BranchProtection --verifies permissions for--> MRModel
BranchProtection --gates actions in--> BranchService
BranchProtection --influences--> Mergeability

%% REVIEW AND MERGE FLOW
MRModel --requires review by--> ReviewSystem
ReviewSystem --anchors discussions in--> DiffDiscussion
ReviewSystem --assigns roles in--> Collaboration
Collaboration --assigns participants to--> MRModel
Mergeability --checks status for--> MRModel

%% ERROR HANDLING LINK
MRErrorHandling --handles faults in--> Mergeability
MRErrorHandling --validates in--> MRModel

%% WORKERS AND BACKGROUND INTEGRATION
RepoWorkers -.triggers repo state changes for.-> RepoModel
RepoWorkers -.triggers repo state changes for.-> BranchModel
RepoWorkers -.notify MR system via webhooks/events.-> MRModel

%% INFRASTRUCTURE AND API BOUNDARIES
RepoApi --exposes-->|branches/tags CRUD| BranchService
RepoApi --exposes-->|tags CRUD| TagService
RepoApi --publishes structure/metadata for--> DiffService
RepoApi --surface branches for--> MRModel

%% DOMAIN-SCOPED ABSTRACTIONS (PASTEL)
classDef core fill:#D4F1F9,stroke:#80c9e4,stroke-width:1,rx:6
classDef support fill:#FFF8DC,stroke:#E4CD80,stroke-width:1,rx:6
classDef data fill:#E0F8E0,stroke:#98d44e,stroke-width:1,rx:6
classDef error fill:#FFE4E1,stroke:#eeaaa9,stroke-width:1,rx:6
classDef init fill:#E6E6FA,stroke:#b7aadf,stroke-width:1,rx:6

%% DOMAIN-LEVEL LOGICAL GROUPINGS
class DOMAIN,DOMAIN_CONCEPTS,DOMAIN_DATA fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rx:16
class REPOS_BRANCHES_TAGS,COMMITS_DIFFS_MRS fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rx:12

%% LOGICAL NODE SHAPES
class RB_Purpose,CDMR_Purpose support

%% (spacing)
DOMAIN_DATA -.-> DOMAIN_CONCEPTS
COMMITS_DIFFS_MRS -.-> DOMAIN_DATA
REPOS_BRANCHES_TAGS -.-> DOMAIN_DATA
```