```mermaid
flowchart TD

%% ============================================================
%%      CI/CD, Pipelines & Automation - Domain Overview
%% ============================================================

subgraph DOMAIN["CI/CD, Pipelines & Automation Domain" ]
direction TB
style DOMAIN fill:#F8F8F8,stroke:#3090C7,stroke-width:5,rx:28

  NODE_OVERVIEW["Purpose: Automate build, test, and deployment via configurable, scalable, and extensible pipelines"]:::core

  %% === KEY CROSS-CUTTING ABSTRACTIONS & DATA STRUCTURES ===
  subgraph CORE_CONCEPTS["Shared Core Concepts & Data Structures"]
  direction TB
  style CORE_CONCEPTS fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rx:16

    PIPELINE_DSL[/"Pipeline Definition & DSL"/]:::core
    PIPELINE_ABSTRACTION["Pipeline/Config Abstractions\n- EntryNode, EntryStrategy, Composability"]:::data
    TEMPLATE_CONCEPT["Template/Extension Abstractions\n- Extensible Templates, Catalog, File Loaders"]:::core
    POLICY_ABSTRACTION["Policy/Hook Orchestration\n- Compliance, Security, Execution Hooks"]:::core
    VARIABLE_DS["Variables & Secret Handling\n- Builders, Scopes, DA Structures"]:::data
    ARTIFACT_DS["Artifacts & Trace Data Structures\n- Artifacts, Metadata, Trace Chunks"]:::data
    PIPELINE_EVENT_DS["Pipeline Event/Result Structures\n- Domain Events, Policy changes, Results"]:::data

    PATTERN_BUILDER["Builder/Factory Pattern"]:::utility
    PATTERN_STRATEGY["Policy/Strategy Pattern"]:::utility
    PATTERN_CHAIN["Chain of Responsibility for pipeline processing"]:::utility

    PIPELINE_DSL --> PIPELINE_ABSTRACTION
    PIPELINE_DSL --> VARIABLE_DS
    PIPELINE_DSL --> ARTIFACT_DS
    PIPELINE_DSL --> TEMPLATE_CONCEPT
    PIPELINE_DSL --> POLICY_ABSTRACTION
    PIPELINE_DSL -->|produces/uses| PIPELINE_EVENT_DS

    PIPELINE_ABSTRACTION -.-> PATTERN_BUILDER
    PIPELINE_ABSTRACTION -.-> PATTERN_STRATEGY
    POLICY_ABSTRACTION -.-> PATTERN_STRATEGY
    PIPELINE_ABSTRACTION -.-> PATTERN_CHAIN
  end

  %% ===== SUBDOMAIN OVERVIEW NODES =====
  SUBDOMAIN_PIPELINES["Pipelines\n- Defines, configures, and orchestrates full pipeline workflows\n- Config, Execution, Analytics, Security" ]:::core
  SUBDOMAIN_TEMPLATES["Templates & Extensibility\n- Provides reusable CI configs & extension points\n- Compliance, security, flexible onboarding" ]:::core
  SUBDOMAIN_JOBS["Jobs, Runners & Artifacts\n- Runs individual builds, job logic, artifact retention\n- Handles execution, artifacts, cleanup" ]:::core

  %% Subdomains LOGIC FLOW
  DOMAIN --> NODE_OVERVIEW
  DOMAIN --> SUBDOMAIN_PIPELINES
  DOMAIN --> SUBDOMAIN_TEMPLATES
  DOMAIN --> SUBDOMAIN_JOBS

end

%% ============================================================
%%       SUBDOMAIN: PIPELINES Orchestration & Config
%% ============================================================
subgraph SUBPIPE["Pipelines: Definition, Orchestration, Security & Analytics"]
direction TB
style SUBPIPE fill:#F8F8F8,stroke:#7EC8E3,stroke-width:2,rx:19

  PIPE_CONFIG["Pipeline Config & DSL\n- YAML loading, normalization, entry strategy"]:::core
  PIPE_EXEC["Pipeline Execution\n- Creation chain, services, workers"]:::core
  PIPE_SECURITY["Security & Compliance Layer\n- Scan policy enforcement, token scope"]:::core
  PIPE_ANALYTICS["Analytics & Metrics\n- Aggregation, reporting, artifact stats"]:::core

  VAR_HANDLING["Variables & Secret Handling\n- Collection, builder, presentation"]:::utility
  SCHEDULER["Schedules & Triggers\n- Cron, event, REST/GraphQL entry"]:::utility

  PIPE_CONFIG --> PIPE_EXEC
  PIPE_EXEC --> PIPE_SECURITY
  PIPE_EXEC --> PIPE_ANALYTICS
  PIPE_CONFIG --> VAR_HANDLING
  PIPE_CONFIG --> SCHEDULER
  PIPE_EXEC --> SCHEDULER

  %% Shared data flow
  PIPE_EXEC -->|produces| ARTIFACT_DS
  PIPE_EXEC -->|emits| PIPELINE_EVENT_DS
  PIPE_SECURITY -->|governs| PIPE_EXEC
  PIPE_ANALYTICS -->|reads| ARTIFACT_DS
  PIPE_ANALYTICS -->|reports| PIPELINE_EVENT_DS

  %% Abstractions/patterns
  PIPE_CONFIG -.-> PATTERN_BUILDER
  PIPE_EXEC -.-> PATTERN_CHAIN
  PIPE_SECURITY -.-> PATTERN_STRATEGY
end

%% ============================================================
%%  SUBDOMAIN: CI/CD TEMPLATES & EXTENSIBILITY Config Ext, Policy
%% ============================================================
subgraph SUBTEMPL["Templates & Extensibility: Config Patterns, Hooks, Compliance"]
direction TB
style SUBTEMPL fill:#F8F8F8,stroke:#3090C7,stroke-width:2,rx:19

  TEMPLATE_MGMT["Template Definition & Catalog\n- Catalog API, reusable configs, file loaders"]:::core
  POLICY_HOOK["Execution Policy & Orchestration\n- Hooks, onboarding, compliance"]:::core
  CONFIG_ENTRY["Config Entry Abstraction\n- EntryNode, EntryStrategy for extensibility"]:::core
  ENTERPRISE_POLICY["Enterprise & Compliance Policy Logic\n- Required includes, enforcement"]:::core

  TEMPLATE_MGMT --> CONFIG_ENTRY
  TEMPLATE_MGMT --> ENTERPRISE_POLICY
  POLICY_HOOK --> ENTERPRISE_POLICY
  POLICY_HOOK --> CONFIG_ENTRY

  CONFIG_ENTRY --> PIPELINE_ABSTRACTION
  TEMPLATE_MGMT -->|feeds| TEMPLATE_CONCEPT

  %% Connection to shared concepts
  CONFIG_ENTRY --> PIPELINE_DSL
  POLICY_HOOK --> POLICY_ABSTRACTION
  ENTERPRISE_POLICY --> POLICY_ABSTRACTION

  %% Patterns/strategy
  TEMPLATE_MGMT -.-> PATTERN_BUILDER
  POLICY_HOOK -.-> PATTERN_STRATEGY
end

%% ============================================================
%%  SUBDOMAIN: JOBS, RUNNERS & ARTIFACTS Execution, Storage
%% ============================================================
subgraph SUBJOBS["Jobs, Runners & Artifacts: Execution, Storage, Lifecycle"]
direction TB
style SUBJOBS fill:#F8F8F8,stroke:#B3D8EB,stroke-width:2,rx:19

  JOB_EXEC["Job Runner & Execution\n- Job/Build model, runner orchestration"]:::core
  ARTIFACT_MGMT["Artifact Management\n- Store, access, lifecycle, cleanup"]:::core
  TRACE_MGMT["Trace & Log Handling\n- Trace chunking, streaming"]:::utility
  ARTIFACT_API["Artifact Access & API\n- REST, GraphQL, security"]:::core
  ARTIFACT_WORKERS["Artifact & Cleanup Workers\n- Cron, expiry, backup"]:::init

  ARTIFACT_DS_OVERVIEW["Artifact Data Structures\n- JobArtifact, PipelineArtifact, Metadata"]:::data

  JOB_EXEC --> ARTIFACT_MGMT
  ARTIFACT_MGMT --> TRACE_MGMT
  ARTIFACT_MGMT --> ARTIFACT_API
  ARTIFACT_MGMT --> ARTIFACT_WORKERS
  ARTIFACT_MGMT --> ARTIFACT_DS_OVERVIEW

  %% Data flow: artifacts connect to analytics, compliance, events
  ARTIFACT_MGMT -->|produces/feeds| ARTIFACT_DS
  ARTIFACT_API -->|offers| ARTIFACT_DS
  TRACE_MGMT -->|feeds| ARTIFACT_DS

  %% Worker flows
  ARTIFACT_WORKERS -->|cleanup/retention| ARTIFACT_MGMT
  ARTIFACT_WORKERS -->|expire/backup| ARTIFACT_DS

  %% Cross-link to templates/pipeline config
  ARTIFACT_MGMT -->|bound by pipeline DSL| PIPELINE_DSL
  ARTIFACT_MGMT -->|references config/entry| PIPELINE_ABSTRACTION
  ARTIFACT_WORKERS -->|triggered via policy/events| PIPELINE_EVENT_DS
end

%% ============================================================
%%    KEY COLLABORATION & FLOW -- ACROSS SUBDOMAINS
%% ============================================================

%% ======================== DOMAIN ORCHESTRATION FLOW =======================
NODE_OVERVIEW --> SUBDOMAIN_PIPELINES
NODE_OVERVIEW --> SUBDOMAIN_TEMPLATES
NODE_OVERVIEW --> SUBDOMAIN_JOBS

%% == SUBDOMAINS COLLABORATION ==
SUBDOMAIN_PIPELINES -- "Generates and orchestrates pipeline flow" --> SUBDOMAIN_TEMPLATES
SUBDOMAIN_PIPELINES -- "Schedules and manages execution via jobs/runners" --> SUBDOMAIN_JOBS
SUBDOMAIN_TEMPLATES -- "Provides reusable config/extensions" --> SUBDOMAIN_PIPELINES
SUBDOMAIN_TEMPLATES -- "Injects compliance, security, onboarding policies" --> SUBDOMAIN_PIPELINES
SUBDOMAIN_JOBS -- "Executes composed jobs/builds, produces artifacts" --> SUBDOMAIN_PIPELINES
SUBDOMAIN_JOBS -- "Lifecycle and validation logic influenced by policies/templates" --> SUBDOMAIN_TEMPLATES
SUBDOMAIN_PIPELINES -- "Feeds artifact/metrics data for analytics" --> SUBDOMAIN_JOBS

%% == KEY SHARED DATA STRUCTURES/PATTERNS ==
SUBDOMAIN_PIPELINES ---|Uses| PIPELINE_ABSTRACTION
SUBDOMAIN_PIPELINES ---|Uses| VARIABLE_DS
SUBDOMAIN_PIPELINES ---|Uses| ARTIFACT_DS
SUBDOMAIN_PIPELINES ---|Emits| PIPELINE_EVENT_DS

SUBDOMAIN_TEMPLATES ---|Builds on| PIPELINE_ABSTRACTION
SUBDOMAIN_TEMPLATES ---|Defines| TEMPLATE_CONCEPT
SUBDOMAIN_TEMPLATES ---|Orchestrates| POLICY_ABSTRACTION
SUBDOMAIN_TEMPLATES ---|Injects| PIPELINE_EVENT_DS

SUBDOMAIN_JOBS ---|Implements via| PIPELINE_ABSTRACTION
SUBDOMAIN_JOBS ---|Produces| ARTIFACT_DS
SUBDOMAIN_JOBS ---|Emits| PIPELINE_EVENT_DS
SUBDOMAIN_JOBS ---|Triggered by| POLICY_ABSTRACTION

%% == CROSS-DOMAIN DATA FLOW ==
PIPELINE_ABSTRACTION -->|Configures| ARTIFACT_DS
TEMPLATE_CONCEPT -->|Extensible config for| PIPELINE_ABSTRACTION
POLICY_ABSTRACTION -->|Enforces on| PIPELINE_ABSTRACTION
VARIABLE_DS -->|Expand in| PIPELINE_DSL
PIPELINE_EVENT_DS -->|Observed by| POLICY_ABSTRACTION
ARTIFACT_DS -->|Consumed in| PIPE_ANALYTICS

%% Patterns applied across the domain
PATTERN_BUILDER -.-> PIPELINE_ABSTRACTION
PATTERN_BUILDER -.-> TEMPLATE_CONCEPT
PATTERN_STRATEGY -.-> POLICY_ABSTRACTION
PATTERN_CHAIN -.-> PIPE_EXEC

%% =================== CLASS DEFINITIONS (CONSOLIDATED) ====================
classDef core fill:#D4F1F9,stroke:#3090C7,stroke-width:2,rx:14
classDef data fill:#E0F8E0,stroke:#84D69E,stroke-width:2,rx:14
classDef utility fill:#FFF8DC,stroke:#FFD580,stroke-width:2,rx:14
classDef error fill:#FFE4E1,stroke:#C38181,stroke-width:2,rx:14
classDef init fill:#E6E6FA,stroke:#A991D4,stroke-width:2,rx:14

%% Assign classes for node styling
class NODE_OVERVIEW,SUBDOMAIN_PIPELINES,SUBDOMAIN_TEMPLATES,SUBDOMAIN_JOBS,TEMPLATE_CONCEPT,TEMPLATE_MGMT,POLICY_HOOK,CONFIG_ENTRY,ENTERPRISE_POLICY,PIPE_CONFIG,PIPE_EXEC,PIPE_SECURITY,PIPE_ANALYTICS,PIPELINE_DSL core
class VARIABLE_DS,ARTIFACT_DS,PIPELINE_EVENT_DS,PIPELINE_ABSTRACTION,ARTIFACT_DS_OVERVIEW data
class VAR_HANDLING,SCHEDULER,TRACE_MGMT,PATTERN_BUILDER,PATTERN_STRATEGY,PATTERN_CHAIN utility
class ARTIFACT_WORKERS init

%% Subgraph backgrounds for clarity and structure
classDef subgraphbg fill:#F8F8F8,stroke:#EBEBEB,stroke-width:1,rx:18
class DOMAIN,CORE_CONCEPTS,SUBPIPE,SUBTEMPL,SUBJOBS subgraphbg
```