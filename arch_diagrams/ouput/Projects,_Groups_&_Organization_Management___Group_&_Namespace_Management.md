```mermaid
flowchart TB
%% Combined Architecture: Projects, Groups & Organization Management -> Group & Namespace Management (Hierarchy Level: 1)
%% HIGH-LEVEL vertical layout, color-preserved, key flows only

%% COLOR CLASSES
classDef core fill:#D4F1F9,stroke:#8CC7E6,stroke-width:2,rounded-rectangle
classDef support fill:#FFF8DC,stroke:#FFE08C,stroke-width:2,rounded-rectangle
classDef data fill:#E0F8E0,stroke:#78D678,stroke-width:2,rounded-rectangle
classDef error fill:#FFE4E1,stroke:#EB8D8D,stroke-width:2,rounded-rectangle
classDef init fill:#E6E6FA,stroke:#E6C2F6,stroke-width:2,rounded-rectangle
classDef groupbox fill:#F8F8F8,stroke:#BFD1DF,stroke-width:2,rounded-rectangle
classDef pastelblue fill:#D4F1F9,stroke:#6CC3D5,stroke-width:2,rounded-rectangle
classDef pastelyellow fill:#FFF8DC,stroke:#F6D96B,stroke-width:2,rounded-rectangle
classDef pastelgreen fill:#E0F8E0,stroke:#A8E7A0,stroke-width:2,rounded-rectangle
classDef pastelred fill:#FFE4E1,stroke:#F4BDBD,stroke-width:2,rounded-rectangle
classDef pastelpurple fill:#E6E6FA,stroke:#B8B9DD,stroke-width:2,rounded-rectangle

%% ||||||||||||||||||||||||||||||||||||| HIGHEST-LEVEL CONTEXTS ||||||||||||||||||||||||||||||||||||||||
subgraph DOMAIN["Projects, Groups & Organization Management / Group & Namespace Management"]
direction TB
style DOMAIN fill:#F8F8F8,stroke:#BFD1DF,stroke-width:3,rounded-rectangle

%% ----------------------------------------------------------------------------------------------------
%% ---- SUBDOMAIN 1: GROUP & NAMESPACE SETTINGS & PERMISSIONS ==== <<abstracted>>
subgraph S1["Group Settings & Permissions" ]
direction TB
style S1 fill:#F8F8F8,stroke:#99BFE3,stroke-width:2,rounded-rectangle

S1C1["Controllers / Entry Points":::core]
S1S1["Core Domain Services":::core]
S1D1["NamespaceSetting":::data]
S1D2["Groups::FeatureSetting":::data]
S1P1["Access Policies":::core]
S1H1["Helpers & Utilities":::support]
S1E1["Error Handling":::error]
S1W1["Async Workers":::init]
end

%% ----------------------------------------------------------------------------------------------------
%% ---- SUBDOMAIN 2: NAMESPACE HIERARCHY, MANAGEMENT & QUOTAS ==== <<abstracted>>
subgraph S2["Namespace Management & Quotas" ]
direction TB
style S2 fill:#F8F8F8,stroke:#5EC2E6,stroke-width:2,rounded-rectangle

S2D1["Namespace":::data]
S2D2["Group":::data]
S2D3["ProjectNamespace":::data]
S2Q["Quota & Storage Logic":::core]
S2T["Hierarchy & Traversal":::core]
S2S["Namespace Services":::support]
S2C["Controllers & Init":::init]
S2P["Quota/Policy Enforcement":::support]
end

%% ----------------------------------------------------------------------------------------------------
%% ---- SUBDOMAIN 3: GROUP ANALYTICS ABSTRACTED==== 
subgraph S3["Group Analytics"]
direction TB
style S3 fill:#F8F8F8,stroke:#A9D6EA,stroke-width:2,rounded-rectangle

S3C1["Analytics Controllers & Entry":::core]
S3S1["Analytics / Aggregation Services":::core]
S3F1["Analytics Finders & Preloaders":::support]
S3D1["Analytics Data Structures":::data]
S3W1["Analytics Workers":::init]
end

%% ----------------------------------------------------------------------------------------------------
%% ---- SUBDOMAIN 4: GROUP MEMBERSHIP & FEDERATION ==== Membership, SAML, SCIM, Group Links
subgraph S4["Group Membership & Federation"]
direction TB
style S4 fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

S4C1["Membership Controllers":::core]
S4S1["Membership & SAML Services":::core]
S4D1["Group Member / Identity Models":::data]
S4SAML["SAML/SCIM/Federation Logic":::core]
S4W1["Membership Workers":::init]
S4H1["Membership Helpers":::support]
S4GQ["Membership GraphQL Interfaces":::core]
end

%% ----------------------------------------------------------------------------------------------------
%% ---- SHARED DOMAIN DATA STRUCTURES -----
subgraph SHARED["Key Domain Data Structures"]
direction TB
style SHARED fill:#F8F8F8,stroke:#78D678,stroke-width:2,rounded-rectangle

NS["Namespace":::data]
GRP["Group":::data]
MEM["GroupMember/Member":::data]
NSS["NamespaceSetting":::data]
FS["FeatureSetting":::data]
STAT["Namespace/Group Statistics":::data]
SCIM["SCIMIdentity/GroupSCIMIdentity":::data]
SAMLPRV["SAMLProvider":::data]
end

%% ----------------------------------------------------------------------------------------------------
%% |||||||||||||||| DOMAIN-WIDE COLLABORATION/RELATIONSHIP FLOWS |||||||||||||||||||||||||||||||||||||||

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% SHARED OBJECTS: Show which subdomains own/interact with which core data structures

S1D1 -- spans --> NS
S1D1 -- setting for --> GRP
S1D2 -- feature toggle for --> GRP

S2D1 -- base type --> NS
S2D2 -- is kind of --> NS
S2D2 -- member of --> GRP
S2D3 -- belongs to --> NS
S2Q -- quota, stats for --> NS
S2Q -- aggregates for --> GRP

S3D1 -- statistics/analytics over --> NS
S3D1 -- analytics for --> GRP
S3S1 -- aggregates stats from --> STAT
S3C1 -- exposes analytics for --> GRP

S4D1 -- membership for --> GRP
S4D1 -- membership in --> NS
S4SAML -- federates/identifies --> GRP
S4SAML -- sso/trust for --> SAMLPRV
S4SAML -- manages identity --> SCIM
S4H1 -- surface group/identity data --> GRP

%% Cross-subdomain: Membership & Settings
S1S1 -- authorizes/mutates --> S4D1
S4C1 -- changes membership on --> S1D1
S4S1 -- updates settings on --> S1D1

%% Cross-subdomain: Settings & Namespace/Quotas
S1S1 -- configures limits via --> S2Q
S2P -- applies policy using --> S1P1

%% Cross-subdomain: Namespace/Quotas & Analytics
S2Q -- provides stats for --> S3S1
S3S1 -- calculates analytics on --> S2D2

%% Cross-subdomain: Membership & Analytics
S4D1 -- influences group stats in --> S3D1
S3F1 -- preloads membership context from --> S4D1

%% Cross-subdomain: Membership & Federation/Settings
S4SAML -- uses settings from --> S1D1

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% TOP-LEVEL DOMAIN USAGE FLOW

%% USER ENTRY POINTS controllers -> Services -> Data Models (conceptualized)
S1C1 --> S1S1
S1S1 --> S1D1
S1S1 -- applies policies --> S1P1
S1C1 -- renders via --> S1H1
S1S1 -- triggers event --> S1W1
S1S1 -- error flow --> S1E1

S2C --> S2S
S2S --> S2D1
S2S --> S2Q
S2Q --> S2D2
S2S --> S2P
S2C -- schedule jobs via --> S2S

S3C1 --> S3S1
S3S1 --> S3D1
S3S1 -- triggers async --> S3W1
S3S1 --> S3F1

S4C1 --> S4S1
S4C1 -- exposes via GraphQL --> S4GQ
S4S1 --> S4D1
S4S1 -- federates via --> S4SAML
S4S1 -- triggers async --> S4W1
S4C1 -- uses helpers --> S4H1

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% DOMAIN-WIDE LOGICAL GROUPS & KEY FLOWS

%% Settings <-> Membership: Settings/Permissions changes directly impact group member access & authorization
S1S1 -- governs access --> S4D1
S4S1 -- queries settings --> S1D1

%% Namespace Management <-> Group: The group entity is at the intersection of both settings and quotas
GRP -- managed by --> S1S1
GRP -- quota enforced in --> S2Q

%% Membership & SAML: SAML/SCIM provides federated/group membership/identity for groups, working over membership models
S4SAML -- assigns/provisions group identity --> S4D1
S4SAML -- enforces membership in --> S4D1
S1P1 -- policy controls over --> S4D1

%% Analytics <-> Quota/Stats: Group analytics rely on up-to-date statistics and quotas across the namespace tree
S3S1 -- queries stats from --> STAT
S2Q -- updates --> STAT

%% Analytics <-> Membership: Analytical services aggregate data about group membership and activity
S3S1 -- aggregates on --> S4D1

%% Settings <-> Namespace Logic: Group and namespace settings often stored in shared, hierarchical namespace
S1D1 -- persisted in --> NS
NSS -- belongs to --> NS

%% All flows revolve logically around "Group" and "Namespace" as pivots for settings, stats/quotas, analytics, membership/federation

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% KEY DOMAIN ABSTRACTIONS/PATTERNS shown as pastel green data/aggregate boxes

subgraph ABSTRACTIONS["Domain Abstractions & Patterns"]
direction TB
style ABSTRACTIONS fill:#F8F8F8,stroke:#A8E7A0,stroke-width:2,rounded-rectangle

AGG["Denormalized Hierarchical Aggregates":::pastelgreen]
POL["Policy Enforcement Strategies":::pastelgreen]
SYNC["Background Job/Async Pipeline":::pastelgreen]
FED["Federated/External Identity Pattern":::pastelgreen]
end

%% MAP Subdomains to Patterns/Abstractions

S1S1 -- triggers sync --> SYNC
S2Q -- maintains aggregates --> AGG
S2S -- manages hierarchy via --> AGG

S1P1 -- applies --> POL
S2P -- applies --> POL
S4S1 -- enforces via --> POL
S4SAML -- externalizes/group membership --> FED

S3W1 -- runs stats jobs --> SYNC
S2C -- schedules cleanup/aggregation --> SYNC
S4W1 -- queues member/federation tasks --> SYNC

%% Shared objects are used between abstractions (e.g. AGG and POL both act on STAT and GRP, etc)
AGG -- aggregates for --> STAT
POL -- checks on --> MEM
FED -- transforms --> MEM
FED -- provisions --> SCIM
SYNC -- maintains --> AGG

%% Link KEY DATA objects to patterns
NS -- root object for --> AGG
GRP -- subject of --> AGG
MEM -- subject of --> POL
SCIM -- identity for --> FED

end %%=======
end %%====================================================== END DOMAIN GROUPING

%% COMPACT VERTICAL LAYERS

%% KEY: This diagram is a domain architecture summary:
%% - Each subdomain is a box summarizing main components (not all class/files shown)
%% - Shared domain models and objects are surfaced and interconnect the subdomains
%% - Logical relationships and flow arrows show major collaborations for group and namespace management
```