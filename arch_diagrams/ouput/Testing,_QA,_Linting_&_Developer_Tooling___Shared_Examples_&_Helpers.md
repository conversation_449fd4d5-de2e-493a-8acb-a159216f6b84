```mermaid
flowchart TB
%% Styles
classDef domain fill:#D4F1F9,stroke:#92d8ea,stroke-width:4px,color:#222,stroke-dasharray: 0,rx:24,ry:24
classDef subdomain fill:#F8F8F8,stroke:#b0b0b0,stroke-width:3px,color:#222,rx:14,ry:14
classDef core fill:#D4F1F9,stroke:#92d8ea,stroke-width:2px,color:#222,rx:8,ry:8
classDef util fill:#FFF8DC,stroke:#f7e3aa,stroke-width:2px,color:#222,rx:8,ry:8
classDef data fill:#E0F8E0,stroke:#9bdd93,stroke-width:2px,color:#222,rx:8,ry:8
classDef error fill:#FFE4E1,stroke:#f8b2b1,stroke-width:2px,color:#222,rx:8,ry:8
classDef init fill:#E6E6FA,stroke:#b5afe7,stroke-width:2px,color:#222,rx:8,ry:8
classDef group fill:#F8F8F8,stroke:#b5afe7,stroke-width:3px,color:#222,rx:16,ry:16
classDef dataStruct fill:#E0F8E0,stroke:#97e297,stroke-width:2px,color:#222,rx:30,ry:30
classDef pattern fill:#FFE4E1,stroke:#dc9090,stroke-width:2px,color:#222,rx:14,ry:14

%% VERTICAL LAYOUT
direction TB

%% DOMAIN BOUNDARY
subgraph sgDomain["Testing, QA, Linting & Developer Tooling - Shared Examples & Helpers" ]
  class sgDomain domain

  %% Core Concepts - Abstracted Nodes
  cd_test_helpers["Test Helper Abstractions"]:::core
  cd_shared_examples["Reusable Shared Examples"]:::core
  cd_project_utils["Project & File Utilities"]:::util
  cd_data_structures["Structured Example/Test Data"]:::dataStruct
  cd_env_masking["Environment & Secret Masking"]:::pattern
  cd_wait_repeat["Wait/Repeat Patterns"]:::pattern
  cd_reporting["Unified Test Reporting/Metadata"]:::util
  cd_api_helpers["API/Fabrication Helpers"]:::util

  %% DOMAIN-SPECIFIC DATA STRUCTURES abstract node
  ds_example_data["Example Data Structures"]:::data
  ds_json_report["Structured JSON Reports"]:::data
  ds_metrics["Test Metrics Data"]:::data
  ds_test_metadata["Test Metadata"]:::data

  %% KEY PATTERNS/ABSTRACTIONS
  pat_waiter["Waiter/Repeater Pattern"]:::pattern
  pat_env_stub["Environment Stub Pattern"]:::pattern
  pat_log_link["Log Correlation Pattern"]:::pattern
  pat_secret_mask["CI/CD Variable Masking"]:::pattern

  %% SUBDOMAIN SINGLE: Shared Examples & Helpers
  subgraph sgHelpers["Subdomain: Shared Examples & Helpers"]
    class sgHelpers subdomain

    %% Logical Groupings, Abstracted
    sgHelpers_core[Core Helper Modules]:::group
    sgHelpers_util[Test Utility Modules]:::group
    sgHelpers_patterns[Test Patterns/Infrastructure]:::group
    sgHelpers_shared[Shared Example Sets]:::group
    sgHelpers_data[Helper-Driven Data Structures]:::group

    %% Abstracted mapping to high-level concepts
    sgHelpers_core--provides-->cd_test_helpers
    sgHelpers_core--supports-->cd_project_utils
    sgHelpers_core--drives-->cd_reporting

    sgHelpers_shared--enables-->cd_shared_examples
    sgHelpers_shared--consumes-->cd_data_structures

    sgHelpers_util--facilitates-->cd_project_utils
    sgHelpers_util--enables-->cd_api_helpers

    sgHelpers_patterns--implements-->cd_wait_repeat
    sgHelpers_patterns--embeds-->cd_env_masking

    sgHelpers_data--produces-->cd_data_structures
    sgHelpers_data--generates-->cd_reporting
  end

  %% Abstractions and Core Relationships
  cd_test_helpers--are leveraged by-->cd_shared_examples
  cd_test_helpers--facilitate-->cd_reporting
  cd_shared_examples--parameterize/tests-->cd_project_utils
  cd_project_utils--manage-->ds_example_data

  %% Patterns in use
  cd_wait_repeat--is based on-->pat_waiter
  cd_env_masking--is based on-->pat_env_stub
  cd_env_masking--applies-->pat_secret_mask
  cd_reporting--relies on-->pat_log_link
  cd_reporting--interpolates/results-->ds_json_report
  cd_reporting--decorates-->ds_test_metadata

  %% Data Flow
  cd_project_utils--generate/consume-->ds_example_data
  cd_test_helpers--enrich-->ds_test_metadata
  cd_reporting--produces-->ds_json_report
  cd_reporting--emits-->ds_metrics

  %% Sharing and Collaboration
  cd_test_helpers--used by-->cd_api_helpers
  cd_api_helpers--create-->ds_example_data
  cd_api_helpers--support-->cd_shared_examples
  cd_shared_examples--drive/combine-->cd_reporting

  %% Patterns and Data: Where used
  pat_waiter--enables-->cd_wait_repeat
  pat_env_stub--enables-->cd_env_masking
  pat_log_link--enables-->cd_reporting
  pat_secret_mask--applied within-->cd_env_masking

end

%% Class assignment
class cd_test_helpers,cd_shared_examples,cd_project_utils,cd_api_helpers,cd_reporting core
class ds_example_data,ds_json_report,ds_metrics,ds_test_metadata dataStruct
class pat_waiter,pat_env_stub,pat_log_link,pat_secret_mask,cd_env_masking,cd_wait_repeat pattern
class sgHelpers,sgHelpers_core,sgHelpers_shared,sgHelpers_util,sgHelpers_patterns,sgHelpers_data group

%% Attach domain core to subdomain
sgHelpers--collaborates with-->cd_test_helpers
sgHelpers--centralizes-->cd_shared_examples
sgHelpers--organizes/utilizes-->cd_project_utils
sgHelpers--enables data flow to-->cd_reporting
sgHelpers--adopts/implements-->cd_api_helpers
sgHelpers--follows/embeds patterns-->cd_wait_repeat
sgHelpers--applies-->cd_env_masking

%% Space out the data structures at the bottom
cd_test_helpers-.produces.->ds_test_metadata
cd_reporting-.outputs.->ds_json_report
cd_reporting-.outputs.->ds_metrics
cd_project_utils-.feeds.->ds_example_data
cd_api_helpers-.generates.->ds_example_data

%% Higher-level conceptual flows
sgDomain--uses/relies on-->pat_waiter
sgDomain--uses/relies on-->pat_env_stub
sgDomain--uses/relies on-->pat_secret_mask
sgDomain--uses/relies on-->pat_log_link

ds_example_data-.feeds.->cd_reporting
ds_json_report-.summarizes.->ds_metrics
cd_shared_examples-.consume.->ds_example_data
cd_test_helpers-.parameterize.->cd_shared_examples
sgHelpers_patterns-.organize.->pat_waiter
sgHelpers_patterns-.organize.->pat_env_stub

%% Structure for vertical reading and hierarchy
sgDomain --> sgHelpers
```