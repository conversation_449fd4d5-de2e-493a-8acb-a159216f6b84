```mermaid
flowchart TB

%% ==== DEFINITIONS AND COLORS ====

classDef core fill:#D4F1F9,stroke:#82bae6,stroke-width:2px,rx:10,ry:10,color:#2e3a4b;
classDef support fill:#FFF8DC,stroke:#FFD580,stroke-width:2px,rx:10,ry:10,color:#4d4840;
classDef util fill:#FFF8DC,stroke:#FFD580,stroke-width:2px,rx:10,ry:10,color:#4d4840;
classDef data fill:#E0F8E0,stroke:#8ddc8d,stroke-width:2px,rx:10,ry:10,color:#225c2c;
classDef error fill:#FFE4E1,stroke:#f3b4bb,stroke-width:2px,rx:10,ry:10,color:#802126;
classDef init fill:#E6E6FA,stroke:#cabbe9,stroke-width:2px,rx:10,ry:10,color:#3c2d4f;
classDef group fill:#F8F8F8,stroke:#B3CDE8,stroke-width:3px,color:#333;
classDef sg fill:#F8F8F8,stroke:#D4F1F9,stroke-width:3px,color:#333;
classDef sg-yellow fill:#F8F8F8,stroke:#FFD580,stroke-width:3px,color:#333;
classDef sg-green fill:#F8F8F8,stroke:#8ddc8d,stroke-width:3px,color:#333;
classDef sg-purple fill:#F8F8F8,stroke:#cabbe9,stroke-width:3px,color:#333;

%% ==== ROOT DOMAIN CONTEXT ====

subgraph DOMAIN["Testing, QA, Linting & Developer Tooling: Static Analysis, Linting & Danger"]
  direction TB
  class DOMAIN group

  %% --- SUBDOMAIN 1: DANGER AUTOMATION & SUGGESTION ENGINE ---
  subgraph SG_DangerCore["Danger Plugins & Automation"]
    direction TB
    class SG_DangerCore sg

    D1_SuggestionEngine["Suggestion Engine\n- Abstract suggestion base\n- Provides engine for all Danger-based static analysis"]:::core
    D1_Suggestor["Suggestor Module\n- Logic for text templating/commenting\n- Applies to all suggestion/automation plugins"]:::core
    D1_DangerPlugins["Danger Plugin Layer\n- Plugins for DB, Schema, CI, FeatureFlags, Sidekiq, AI Logging,\n  Customer Success, Experiments, etc."]:::core
    D1_ProjectHelper["Danger Project Helper\n- Shared helper methods for Danger plugins"]:::util
    D1_StaticAnalysisDS["Static Analysis Data Structures\n- RuboCop TODO registry\n- Cop-specific TODO state"]:::data
  end

  %% --- SUBDOMAIN 2: RUBOCOP CUSTOM COPS & EXTENSIONS ---
  subgraph SG_Rubocop["RuboCop Custom Cops & Extensions"]
    direction TB
    class SG_Rubocop sg

    R2_CustomCops["Custom Cops & Extensions\n- Project-specific RuboCop cops for DB, API, QA, RSpec, etc."]:::core
    R2_LintRules["Lint Enforcement Rules\n- Common patterns, safety, style, migration, performance"]:::error
    R2_Helpers["Helper Modules\n- Code reuse/migration helpers for custom cops"]:::util
    R2_Validators["Model & Domain Validators\n- FeatureFlags, URLs, paths, HTML safety, cron, durations"]:::error
    R2_RspecCops["RSpec Cops\n- Ensures best practices in tests/spec infrastructure"]:::core
    R2_Utils["RuboCop Automation Utilities\n- TODO generation, housekeeper fixers, shell helpers"]:::util
    R2_Tooling["Inline RuboCop Tooling\n- Danger inline disables, gettext extraction, static translation guards"]:::support
    R2_RspecSpecs["RSpec Support & Cop Tests\n- Ensure custom cops/validators function and are enforced"]:::support
  end

  %% --- SHARED DATA STRUCTURES & CROSS-CUTTING CONCEPTS ---
  subgraph SG_DataInfra["Domain Data Structures & Patterns"]
    direction TB
    class SG_DataInfra sg-green
    DS_SharedTodos["RuboCop TODO Registry/YAMLs\n- Shared data structure for TODO state tracking and suggestions"]:::data
    DS_CopTodo["Cop TODO State Object\n- Models per-cop TODO management across suggestions and automation"]:::data
    DS_Formatter["Formatter Abstractions\n- Graceful/TODO formatters for TODO persistence & MR feedback"]:::core
  end

  %% --- INITIALIZATION / ORCHESTRATION ---
  subgraph SG_Init["Initialization & Orchestration"]
    direction TB
    class SG_Init sg-purple
    INIT_CheckTasks["Linter Task Runner\n- Orchestrates static analysis, formatter output, and result management"]:::init
    INIT_Engine["Danger Orchestrator\n- Triggers Danger plugins & RuboCop static analysis pipeline"]:::init
  end

end

%% ==== HIGH-LEVEL RELATIONSHIPS & INTERACTIONS ====


%% --- KEY COLLABORATION BETWEEN SUBDOMAINS ---
D1_DangerPlugins --"Triggers enforcement via"--> R2_CustomCops
D1_DangerPlugins --"Applies on"--> R2_LintRules
D1_SuggestionEngine --"Powers"--> D1_DangerPlugins
R2_CustomCops --"Analyze codebase, create issues/warnings for"--> DS_SharedTodos
R2_CustomCops --"Reference"--> R2_Helpers
R2_CustomCops --"Enforce domain practices via"--> R2_Validators
R2_RspecCops --"Drive best practices in"--> R2_RspecSpecs
R2_Utils --"Automates"--> DS_SharedTodos
D1_ProjectHelper --"Shared utilities for"--> D1_DangerPlugins
D1_ProjectHelper --"Shared with"--> R2_Tooling
D1_ProjectHelper --"Shared with"--> R2_Utils
D1_Suggestor --"Template & comment logic for"--> D1_DangerPlugins
D1_SuggestionEngine --"Abstraction for"--> R2_Tooling
D1_SuggestionEngine --"Integrated into"--> R2_Utils

%% DATA PIPELINES / SHARED INFRASTRUCTURE
DS_SharedTodos --"Bidirectional sync for MR state"--> D1_DangerPlugins
DS_SharedTodos --"Populated & consumed by"--> R2_Utils
DS_SharedTodos --"Managed by"--> DS_CopTodo
DS_SharedTodos --"Updated by formatter"--> DS_Formatter
DS_CopTodo --"Per-cop scan state"--> D1_StaticAnalysisDS
DS_Formatter --"Orchestrated via"--> INIT_CheckTasks

%% CROSS-DOMAIN: SUGGESTION ENGINE => FORMATTER => DANGER
D1_DangerPlugins --"Integrates results via"--> DS_Formatter
D1_SuggestionEngine --"Feeds"--> DS_Formatter
D1_DangerPlugins --"Writes MR comments for findings from"--> DS_SharedTodos

%% RUBOCOP COPS: WHICH ARE DOMAIN-SPECIFIC, WHICH ARE SHARED LOGIC?
R2_CustomCops --"Extend/enforce rules with"--> R2_LintRules
R2_CustomCops --"Specialize with"--> R2_Helpers
R2_CustomCops --"Informs static analysis for"--> D1_DangerPlugins
R2_CustomCops --"Integrates QA/enforcement with"--> D1_DangerPlugins
R2_CustomCops --"Invoke Suggestion Engine"--> D1_SuggestionEngine
R2_RspecCops --"Powers test linting for"--> D1_DangerPlugins

%% RUBOCOP UTILS AUTOMATE FEEDBACK CYCLE
R2_Utils --"Triggers batch fixes, orchestrates via"--> DS_Formatter
R2_Utils --"Consistent state across MRs by updating"--> DS_SharedTodos

%% SUGGESTORS FEED DATA TO DANGER PLUGINS, LINT UTILITIES, AND COMMENT GENERATORS
D1_Suggestor --"Engine for"--> D1_DangerPlugins
D1_Suggestor --"Template logic for"--> DS_Formatter

%% INITIALIZERS
INIT_Engine --"Orchestrates execution of"--> D1_DangerPlugins
INIT_CheckTasks --"Runs static analysis using"--> DS_Formatter
INIT_CheckTasks --"Updates"--> DS_SharedTodos

%% RSpec tests guard correctness for custom automation
R2_RspecSpecs --"Test cop/validator correctness for"--> R2_CustomCops
R2_RspecSpecs --"Inform linting validation for"--> R2_LintRules

%% DANGER PROJECT HELPER: SHARED ABSTRACTION FOR DOMAIN LOGIC
D1_ProjectHelper --"Shared utility base for"--> SG_Rubocop
D1_ProjectHelper --"Utility for"--> SG_DangerCore

%% VALIDATORS enforce domain constraints invoked both by custom cops and automation plugins
R2_Validators --"Guard rules for"--> R2_CustomCops
R2_Validators --"Inform check logic for"--> D1_DangerPlugins

%% LINT RULE INTEGRATION: Bidirectional enforcement cycle
R2_LintRules --"Rule enforcement feedback via"--> D1_SuggestionEngine

%% --- ABSTRACTION AND PATTERNS ---
D1_SuggestionEngine --"Abstract pattern for code suggestions\nacross automation and custom cops"--> R2_CustomCops
DS_Formatter --"Pattern for result reporting and\natomic updates across Danger & RuboCop"--> D1_DangerPlugins
D1_Suggestor --"Encapsulates reusable MR comment\nlogic shared across plugins and linting"--> SG_Rubocop

%% --- EXPLICIT DATA FLOWS / CONTROL ---
R2_Utils --"Generates/upgrades"--> DS_SharedTodos
DS_Formatter --"Formats findings for\nDanger feedback as well as updates"--> DS_SharedTodos

%% --- LOGICAL GROUPS AND PURPOSES FOR VISUAL GROUPING ---
%% Just for visual clarity; optional grouping rectangles with color

subgraph COLLAB["Domain Collaboration & Data Flow"]
  direction TB
  class COLLAB sg

  SG_DangerCore
  SG_Rubocop
  SG_DataInfra
  SG_Init

end

%% STYLES: Spacing for vertical layout
linkStyle default stroke-width:1.6px,stroke:#aaa
```