# Bracket Extension Architecture

```mermaid
flowchart TB
    classDef uiComponent fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef coreComponent fill:#e8f5e9,stroke:#2e7d32,stroke-width:2px
    classDef integrationComponent fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef serviceComponent fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px
    classDef externalComponent fill:#ffebee,stroke:#b71c1c,stroke-width:2px
    classDef dataComponent fill:#f5f5f5,stroke:#9e9e9e,stroke-width:2px
    classDef subgraph_style fill:#f5f5f5,stroke:#9e9e9e,stroke-width:1px
    
    %% VSCode Extension Host
    VSCodeExtensionHost[VSCode Extension Host]:::externalComponent
    
    %% Main Extension Entry Point
    ExtensionEntry[Extension Entry Point\nextension.ts]:::coreComponent
    
    %% UI Components
    subgraph UI["UI Components"]
        direction TB
        
        %% Webview UI
        subgraph WebviewUI["Webview UI (React)"]
            direction TB
            App[App.tsx]:::uiComponent
            ChatView[ChatView.tsx]:::uiComponent
            SettingsView[SettingsView.tsx]:::uiComponent
            TaskHeader[TaskHeader.tsx]:::uiComponent
            ChatRow[ChatRow.tsx]:::uiComponent
            ChatTextArea[ChatTextArea.tsx]:::uiComponent
            
            App --> ChatView
            App --> SettingsView
            ChatView --> TaskHeader
            ChatView --> ChatRow
            ChatView --> ChatTextArea
        end
        
        %% Webview Providers
        subgraph WebviewProviders["Webview Providers"]
            direction TB
            BracketProvider[BracketProvider.ts]:::uiComponent
            MermaidCompanionProvider[MermaidCompanionProvider.ts]:::uiComponent
            
            BracketProvider -.- WebviewUI
        end
    end
    
    %% Core Components
    subgraph Core["Core Components"]
        direction TB
        
        %% Bracket System
        subgraph BracketSystem["Bracket System"]
            direction TB
            Bracket[Bracket.ts]:::coreComponent
            PromptSystem[Prompt System]:::coreComponent
            ModeValidator[Mode Validator]:::coreComponent
            SlidingWindow[Sliding Window]:::coreComponent
            
            Bracket --> PromptSystem
            Bracket --> ModeValidator
            Bracket --> SlidingWindow
        end
        
        %% API System
        subgraph APISystem["API System"]
            direction TB
            ApiHandler[ApiHandler Interface]:::coreComponent
            AnthropicProvider[Anthropic Provider]:::coreComponent
            OpenAIProvider[OpenAI Provider]:::coreComponent
            BedrockProvider[Bedrock Provider]:::coreComponent
            
            ApiHandler --> AnthropicProvider
            ApiHandler --> OpenAIProvider
            ApiHandler --> BedrockProvider
        end
    end
    
    %% Integration Components
    subgraph Integrations["Integration Components"]
        direction TB
        
        %% Editor Integration
        subgraph EditorIntegration["Editor Integration"]
            direction TB
            CodeActionProvider[CodeActionProvider.ts]:::integrationComponent
            DiffViewProvider[DiffViewProvider.ts]:::integrationComponent
            
            CodeActionProvider --> DiffViewProvider
        end
        
        %% Terminal Integration
        subgraph TerminalIntegration["Terminal Integration"]
            direction TB
            TerminalRegistry[TerminalRegistry.ts]:::integrationComponent
            TerminalProcess[TerminalProcess.ts]:::integrationComponent
            
            TerminalRegistry --> TerminalProcess
        end
        
        %% Context Engine Integration
        subgraph ContextEngineIntegration["Context Engine Integration"]
            direction TB
            ContextEngine[Context Engine]:::integrationComponent
            ContextMentions[Context Mentions]:::integrationComponent
            
            ContextEngine --> ContextMentions
        end
    end
    
    %% Services
    subgraph Services["Services"]
        direction TB
        
        %% Telemetry Service
        TelemetryService[Telemetry Service]:::serviceComponent
        
        %% MCP Service
        subgraph MCPService["MCP Service"]
            direction TB
            McpServerManager[MCP Server Manager]:::serviceComponent
            McpHub[MCP Hub]:::serviceComponent
            
            McpServerManager --> McpHub
        end
        
        %% Mermaid Service
        subgraph MermaidService["Mermaid Service"]
            direction TB
            MermaidCompanionService[Mermaid Companion Service]:::serviceComponent
            MermaidMappingService[Mermaid Mapping Service]:::serviceComponent
            
            MermaidCompanionService --> MermaidMappingService
        end
        
        %% Browser Service
        BrowserSession[Browser Session]:::serviceComponent
    end
    
    %% External APIs
    subgraph ExternalAPIs["External APIs"]
        direction TB
        AnthropicAPI[Anthropic API]:::externalComponent
        OpenAIAPI[OpenAI API]:::externalComponent
        BedrockAPI[AWS Bedrock API]:::externalComponent
        
        AnthropicProvider --> AnthropicAPI
        OpenAIProvider --> OpenAIAPI
        BedrockProvider --> BedrockAPI
    end
    
    %% Data Components
    subgraph Data["Data Components"]
        direction TB
        ExtensionState[Extension State]:::dataComponent
        HistoryItem[History Item]:::dataComponent
        Settings[Settings]:::dataComponent
        
        ExtensionState --> HistoryItem
        ExtensionState --> Settings
    end
    
    %% Bracket Core Integration
    subgraph BracketCoreIntegration["Bracket Core Integration"]
        direction TB
        DomainTaxonomy[Domain Taxonomy JSON]:::dataComponent
        MermaidDiagrams[Mermaid Diagrams]:::dataComponent
        CodebaseExplanation[Codebase Explanation]:::dataComponent
        
        DomainTaxonomy --> MermaidDiagrams
    end
    
    %% Connections between main components
    ExtensionEntry --> UI
    ExtensionEntry --> Core
    ExtensionEntry --> Integrations
    ExtensionEntry --> Services
    
    VSCodeExtensionHost --> ExtensionEntry
    
    BracketProvider --> Bracket
    Bracket --> ApiHandler
    
    MermaidCompanionProvider --> MermaidCompanionService
    MermaidMappingService --> BracketCoreIntegration
    
    Bracket --> TerminalIntegration
    Bracket --> EditorIntegration
    Bracket --> ContextEngineIntegration
    
    BracketProvider --> ExtensionState
    
    %% Export API
    ExportAPI[Export API]:::coreComponent
    ExtensionEntry --> ExportAPI
```

## Bracket Extension Architecture: Detailed Explanation

The Bracket Extension (bracket_ext) is a TypeScript-based VSCode extension that serves as the product layer of the Bracket system. It provides a rich user interface for interacting with the AI assistant and visualizing the codebase structure through integration with the Bracket Core IRL system.

### Main Components

#### 1. Extension Entry Point (extension.ts)

**Purpose**: Serves as the main entry point for the VSCode extension.

**Responsibilities**:
- Initializing the extension
- Registering commands, code actions, and webview providers
- Setting up event listeners
- Exporting the API for other extensions

#### 2. UI Components

##### Webview UI (React)

**Purpose**: Provides the user interface for interacting with the AI assistant.

**Components**:
- **App.tsx**: Main React component
- **ChatView.tsx**: Chat interface
- **SettingsView.tsx**: Settings interface
- **TaskHeader.tsx**: Header for tasks
- **ChatRow.tsx**: Individual chat messages
- **ChatTextArea.tsx**: Input area for user messages

**Responsibilities**:
- Rendering the chat interface
- Handling user input
- Displaying AI responses
- Managing settings

##### Webview Providers

**Purpose**: Bridge between VSCode and the React UI.

**Components**:
- **BracketProvider.ts**: Provides the main chat interface
- **MermaidCompanionProvider.ts**: Provides the Mermaid diagram viewer

**Responsibilities**:
- Creating and managing webviews
- Handling messages between extension and webview
- Managing state

#### 3. Core Components

##### Bracket System

**Purpose**: Core logic for handling AI interactions.

**Components**:
- **Bracket.ts**: Main class for handling AI interactions
- **Prompt System**: Manages prompts for the AI
- **Mode Validator**: Validates operations based on mode
- **Sliding Window**: Manages conversation context

**Responsibilities**:
- Processing user input
- Generating AI responses
- Managing conversation context
- Handling tools and commands

##### API System

**Purpose**: Interfaces with AI providers.

**Components**:
- **ApiHandler Interface**: Common interface for all providers
- **Anthropic Provider**: Interfaces with Anthropic Claude
- **OpenAI Provider**: Interfaces with OpenAI
- **Bedrock Provider**: Interfaces with AWS Bedrock

**Responsibilities**:
- Sending requests to AI providers
- Processing responses
- Handling streaming
- Managing rate limits and tokens

#### 4. Integration Components

##### Editor Integration

**Purpose**: Integrates with the VSCode editor.

**Components**:
- **CodeActionProvider.ts**: Provides code actions
- **DiffViewProvider.ts**: Handles code diffs

**Responsibilities**:
- Providing code actions
- Handling code diffs
- Managing editor interactions

##### Terminal Integration

**Purpose**: Integrates with the VSCode terminal.

**Components**:
- **TerminalRegistry.ts**: Manages terminal instances
- **TerminalProcess.ts**: Handles terminal processes

**Responsibilities**:
- Executing commands in the terminal
- Capturing terminal output
- Managing terminal processes

##### Context Engine Integration

**Purpose**: Provides context about the codebase.

**Components**:
- **Context Engine**: Analyzes the codebase
- **Context Mentions**: Handles context mentions in messages

**Responsibilities**:
- Analyzing the codebase
- Providing context for AI
- Handling context mentions

#### 5. Services

##### Telemetry Service

**Purpose**: Collects telemetry data.

**Responsibilities**:
- Collecting usage data
- Sending telemetry events
- Respecting user privacy settings

##### MCP Service

**Purpose**: Manages Multi-Context Processing.

**Components**:
- **MCP Server Manager**: Manages MCP servers
- **MCP Hub**: Coordinates MCP operations

**Responsibilities**:
- Managing MCP servers
- Coordinating MCP operations
- Handling MCP requests

##### Mermaid Service

**Purpose**: Manages Mermaid diagrams.

**Components**:
- **Mermaid Companion Service**: Main service for Mermaid companion
- **Mermaid Mapping Service**: Maps functions to diagrams

**Responsibilities**:
- Loading domain taxonomy
- Mapping functions to diagrams
- Displaying diagrams

##### Browser Service

**Purpose**: Provides browser functionality.

**Responsibilities**:
- Opening web pages
- Capturing screenshots
- Managing browser sessions

#### 6. External APIs

**Purpose**: Interfaces with external AI providers.

**Components**:
- **Anthropic API**: Claude API
- **OpenAI API**: OpenAI API
- **AWS Bedrock API**: AWS Bedrock API

**Responsibilities**:
- Providing AI capabilities
- Handling API requests and responses

#### 7. Data Components

**Purpose**: Manages data for the extension.

**Components**:
- **Extension State**: Manages state for the extension
- **History Item**: Represents a history item
- **Settings**: Manages settings

**Responsibilities**:
- Managing state
- Storing history
- Managing settings

#### 8. Bracket Core Integration

**Purpose**: Integrates with Bracket Core.

**Components**:
- **Domain Taxonomy JSON**: Represents the domain taxonomy
- **Mermaid Diagrams**: Visual representations of domains
- **Codebase Explanation**: Explanation of the codebase

**Responsibilities**:
- Loading domain taxonomy
- Displaying Mermaid diagrams
- Providing codebase explanations

### Integration with Bracket Core

The Bracket Extension integrates with Bracket Core through the following mechanisms:

1. **Domain Taxonomy**: The extension loads the domain taxonomy JSON file generated by Bracket Core's IRL system.

2. **Mermaid Diagrams**: The extension displays Mermaid diagrams generated by Bracket Core's IRL system.

3. **Codebase Explanation**: The extension uses the codebase explanation generated by Bracket Core's IRL system.

4. **Context Engine**: The extension uses the context engine to provide context about the codebase to the AI assistant.

### Key Features

1. **AI Assistant**: Provides an AI assistant for code-related tasks.

2. **Mermaid Companion**: Displays Mermaid diagrams as users navigate through code.

3. **Terminal Integration**: Executes commands in the terminal and captures output.

4. **Editor Integration**: Provides code actions and handles code diffs.

5. **Context Engine**: Provides context about the codebase to the AI assistant.

6. **Multi-Context Processing**: Enables processing of multiple contexts.

7. **Browser Integration**: Provides browser functionality for web-related tasks.

### Data Flow

1. User interacts with the UI (ChatView).
2. BracketProvider processes the interaction and passes it to Bracket.
3. Bracket uses the API System to generate a response from the AI.
4. Bracket processes the response and executes any tools or commands.
5. Results are passed back to BracketProvider and displayed in the UI.
6. For Mermaid diagrams, MermaidCompanionProvider loads diagrams from the domain taxonomy and displays them.

This architecture enables a seamless integration between the VSCode extension and the Bracket Core IRL system, providing users with a powerful tool for understanding and working with codebases.
