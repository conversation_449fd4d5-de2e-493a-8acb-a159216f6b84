# Bracket Core IRL Pipeline Architecture

```mermaid
flowchart TB
    classDef inputComponent fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef processingComponent fill:#e8f5e9,stroke:#2e7d32,stroke-width:2px
    classDef outputComponent fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef storageComponent fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px
    classDef cloudComponent fill:#ffebee,stroke:#b71c1c,stroke-width:2px
    classDef dataComponent fill:#f5f5f5,stroke:#9e9e9e,stroke-width:2px
    classDef subgraph_style fill:#f5f5f5,stroke:#9e9e9e,stroke-width:1px
    
    %% Input Components
    RawCode[Raw Codebase]:::inputComponent
    DeltaChanges[Delta Code Changes]:::inputComponent
    
    %% Orchestration Layer
    subgraph Orchestration["Orchestration Layer"]
        direction TB
        Orchestrator[Orchestrator Service]:::processingComponent
        JobService[Job Service]:::processingComponent
        StorageClient[Storage Client]:::processingComponent
        RestAPI[REST API Endpoints]:::processingComponent
        
        Orchestrator --> JobService
        Orchestrator --> StorageClient
        RestAPI --> Orchestrator
    end
    
    %% Indexing Pipeline
    subgraph IndexingPipeline["Indexing Pipeline"]
        direction TB
        
        %% Repository Mapping
        subgraph RepoMapping["Repository Mapping"]
            direction TB
            RepoMapperService[Repository Mapper Service]:::processingComponent
            CodeExtraction[Code Extraction & Parsing]:::processingComponent
            SymbolExtraction[Symbol Extraction]:::processingComponent
            DependencyAnalysis[Dependency Analysis]:::processingComponent
            
            RepoMapperService --> CodeExtraction
            CodeExtraction --> SymbolExtraction
            SymbolExtraction --> DependencyAnalysis
        end
        
        %% Knowledge Graph Generation
        subgraph KnowledgeGraph["Knowledge Graph Generation"]
            direction TB
            KGService[Knowledge Graph Service]:::processingComponent
            RelationshipAnalysis[Relationship Analysis]:::processingComponent
            GraphConstruction[Graph Construction]:::processingComponent
            ImportanceRanking[Importance Ranking]:::processingComponent
            
            KGService --> RelationshipAnalysis
            RelationshipAnalysis --> GraphConstruction
            GraphConstruction --> ImportanceRanking
        end
        
        %% Semantic Documentation
        subgraph SemanticDoc["Semantic Documentation"]
            direction TB
            DocService[Documentation Service]:::processingComponent
            FunctionAnalysis[Function Analysis]:::processingComponent
            SignificanceMarking[Significance Marking]:::processingComponent
            
            DocService --> FunctionAnalysis
            FunctionAnalysis --> SignificanceMarking
        end
        
        %% Domain Analysis
        subgraph DomainAnalysis["Domain Analysis"]
            direction TB
            DomainService[Domain Analyzer Service]:::processingComponent
            FunctionClustering[Function Clustering]:::processingComponent
            HierarchyConstruction[Hierarchy Construction]:::processingComponent
            DomainNaming[Domain Naming]:::processingComponent
            
            DomainService --> FunctionClustering
            FunctionClustering --> HierarchyConstruction
            HierarchyConstruction --> DomainNaming
        end
        
        %% File-Domain Mapping
        subgraph FileDomainMapping["File-Domain Mapping"]
            direction TB
            FileDomainService[File-Domain Mapper Service]:::processingComponent
            FileAnalysis[File Analysis]:::processingComponent
            DomainAssignment[Domain Assignment]:::processingComponent
            
            FileDomainService --> FileAnalysis
            FileAnalysis --> DomainAssignment
        end
        
        %% Domain-File Repomap
        subgraph DomainFileRepomap["Domain-File Repomap"]
            direction TB
            RepoMapService[Domain-File Repomap Service]:::processingComponent
            MapIntegration[Map Integration]:::processingComponent
            StatisticsGeneration[Statistics Generation]:::processingComponent
            
            RepoMapService --> MapIntegration
            MapIntegration --> StatisticsGeneration
        end
        
        %% Diagram Generation
        subgraph DiagramGen["Diagram Generation"]
            direction TB
            DiagramService[Diagram Generator Service]:::processingComponent
            MermaidGeneration[Mermaid Generation]:::processingComponent
            DiagramOptimization[Diagram Optimization]:::processingComponent
            
            DiagramService --> MermaidGeneration
            MermaidGeneration --> DiagramOptimization
        end
        
        %% Taxonomy Generation
        subgraph TaxonomyGen["Taxonomy Generation"]
            direction TB
            TaxonomyService[Taxonomy Generator Service]:::processingComponent
            ArtifactIntegration[Artifact Integration]:::processingComponent
            JSONGeneration[JSON Generation]:::processingComponent
            
            TaxonomyService --> ArtifactIntegration
            ArtifactIntegration --> JSONGeneration
        end
        
        %% Codebase Explanation
        subgraph CodebaseExplanation["Codebase Explanation"]
            direction TB
            ExplanationService[Explanation Generator Service]:::processingComponent
            ArchitectureAnalysis[Architecture Analysis]:::processingComponent
            ExplanationGeneration[Explanation Generation]:::processingComponent
            
            ExplanationService --> ArchitectureAnalysis
            ArchitectureAnalysis --> ExplanationGeneration
        end
    end
    
    %% Delta Codebase Ingestion
    subgraph DeltaIngestion["Delta Codebase Ingestion"]
        direction TB
        DiffDetection[Diff Detection]:::processingComponent
        ChangeAnalysis[Change Analysis]:::processingComponent
        ImpactAssessment[Impact Assessment]:::processingComponent
        SelectiveUpdate[Selective Update]:::processingComponent
        
        DiffDetection --> ChangeAnalysis
        ChangeAnalysis --> ImpactAssessment
        ImpactAssessment --> SelectiveUpdate
    end
    
    %% Artifact Storage
    subgraph ArtifactStorage["Artifact Storage"]
        direction TB
        LocalStorage[Local Storage]:::storageComponent
        CloudStorage[Cloud Storage]:::cloudComponent
        ArtifactManager[Artifact Manager]:::processingComponent
        
        ArtifactManager --> LocalStorage
        ArtifactManager --> CloudStorage
    end
    
    %% Output Artifacts
    subgraph OutputArtifacts["Output Artifacts"]
        direction TB
        RepoMap[Repository Map]:::outputComponent
        KG[Knowledge Graph]:::outputComponent
        SemanticLayer[Semantic Layer]:::outputComponent
        DomainHierarchy[Domain Hierarchy]:::outputComponent
        FileDomainMap[File-Domain Map]:::outputComponent
        CombinedArtifact[Combined Artifact]:::outputComponent
        Diagrams[Mermaid Diagrams]:::outputComponent
        DomainTaxonomy[Domain Taxonomy JSON]:::outputComponent
        CodebaseExplanationDoc[Codebase Explanation]:::outputComponent
    end
    
    %% Flow between components
    RawCode --> Orchestration
    DeltaChanges --> DeltaIngestion
    DeltaIngestion --> Orchestration
    
    Orchestration --> IndexingPipeline
    
    RepoMapping --> KnowledgeGraph
    KnowledgeGraph --> SemanticDoc
    SemanticDoc --> DomainAnalysis
    DomainAnalysis --> FileDomainMapping
    FileDomainMapping --> DomainFileRepomap
    DomainFileRepomap --> DiagramGen
    DomainFileRepomap --> TaxonomyGen
    TaxonomyGen --> CodebaseExplanation
    
    %% Output flows
    RepoMapping --> RepoMap
    KnowledgeGraph --> KG
    SemanticDoc --> SemanticLayer
    DomainAnalysis --> DomainHierarchy
    FileDomainMapping --> FileDomainMap
    DomainFileRepomap --> CombinedArtifact
    DiagramGen --> Diagrams
    TaxonomyGen --> DomainTaxonomy
    CodebaseExplanation --> CodebaseExplanationDoc
    
    %% Storage flows
    RepoMap --> ArtifactStorage
    KG --> ArtifactStorage
    SemanticLayer --> ArtifactStorage
    DomainHierarchy --> ArtifactStorage
    FileDomainMap --> ArtifactStorage
    CombinedArtifact --> ArtifactStorage
    Diagrams --> ArtifactStorage
    DomainTaxonomy --> ArtifactStorage
    CodebaseExplanationDoc --> ArtifactStorage
```

## Bracket Core IRL Pipeline: Comprehensive Architecture

The Bracket Core Intermediate Representation Layer (IRL) pipeline transforms raw code into a structured, hierarchical representation that captures the essence of a codebase's architecture, functionality, and design patterns. This diagram illustrates the high-level architecture of the pipeline without revealing the proprietary implementation details.

### Input Sources

**Raw Codebase**: The complete source code of a repository during initial setup or when analyzing a new repository.

**Delta Code Changes**: Incremental changes to the codebase, such as commits or pull requests, which trigger selective re-indexing.

### Orchestration Layer

The Orchestration Layer coordinates the entire IRL pipeline and manages job execution:

- **Orchestrator Service**: Central coordinator that manages the flow between pipeline stages
- **Job Service**: Handles job creation, status tracking, and artifact management
- **Storage Client**: Manages artifact storage and retrieval
- **REST API Endpoints**: Provides external access to the pipeline functionality

### Indexing Pipeline

The Indexing Pipeline processes the codebase through several sequential stages:

#### Repository Mapping

Creates a comprehensive structural representation of the codebase:

- **Repository Mapper Service**: Microservice responsible for analyzing repositories
- **Code Extraction & Parsing**: Extracts code from files across multiple languages
- **Symbol Extraction**: Identifies functions, classes, methods, and variables
- **Dependency Analysis**: Maps import relationships between files

#### Knowledge Graph Generation

Builds a graph representation of the codebase's components and their relationships:

- **Knowledge Graph Service**: Microservice responsible for graph generation
- **Relationship Analysis**: Determines how components relate to each other
- **Graph Construction**: Creates a graph structure representing the codebase
- **Importance Ranking**: Ranks components by their significance in the codebase

#### Semantic Documentation

Adds semantic meaning to the codebase components:

- **Documentation Service**: Microservice responsible for semantic documentation
- **Function Analysis**: Analyzes functions to understand their purpose
- **Significance Marking**: Identifies architecturally significant components

#### Domain Analysis

Organizes code into logical domains:

- **Domain Analyzer Service**: Microservice responsible for domain analysis
- **Function Clustering**: Groups related functions
- **Hierarchy Construction**: Builds a hierarchical structure of domains
- **Domain Naming**: Assigns meaningful names to domains

#### File-Domain Mapping

Maps files to domains:

- **File-Domain Mapper Service**: Microservice responsible for mapping files to domains
- **File Analysis**: Analyzes files to understand their purpose
- **Domain Assignment**: Assigns files to appropriate domains

#### Domain-File Repomap

Creates a unified representation that links domains, files, and functions:

- **Domain-File Repomap Service**: Microservice responsible for creating domain-file repomaps
- **Map Integration**: Combines domain hierarchy with file mapping
- **Statistics Generation**: Generates statistics for each domain

#### Diagram Generation

Creates visual representations of domains:

- **Diagram Generator Service**: Microservice responsible for diagram generation
- **Mermaid Generation**: Generates Mermaid diagrams for domains
- **Diagram Optimization**: Optimizes diagrams for readability

#### Taxonomy Generation

Combines all outputs into a unified JSON representation:

- **Taxonomy Generator Service**: Microservice responsible for taxonomy generation
- **Artifact Integration**: Integrates all artifacts into a unified representation
- **JSON Generation**: Generates the final JSON taxonomy

#### Codebase Explanation

Generates a comprehensive explanation of the codebase:

- **Explanation Generator Service**: Microservice responsible for explanation generation
- **Architecture Analysis**: Analyzes the overall architecture
- **Explanation Generation**: Generates natural language explanations

### Delta Codebase Ingestion

The Delta Codebase Ingestion system processes incremental changes to the codebase:

- **Diff Detection**: Identifies changes between versions
- **Change Analysis**: Analyzes the nature and scope of changes
- **Impact Assessment**: Determines which domains and artifacts are affected
- **Selective Update**: Updates only the affected parts of the IRL representation

### Artifact Storage

The Artifact Storage system manages the storage and retrieval of artifacts:

- **Local Storage**: Stores artifacts locally for immediate access
- **Cloud Storage**: Stores artifacts in the cloud for persistence and sharing
- **Artifact Manager**: Manages artifact lifecycle and versioning

### Output Artifacts

The IRL pipeline produces several artifacts:

- **Repository Map**: Structural representation of the codebase
- **Knowledge Graph**: Graph representation of components and relationships
- **Semantic Layer**: Semantic documentation of components
- **Domain Hierarchy**: Hierarchical organization of domains
- **File-Domain Map**: Mapping between files and domains
- **Combined Artifact**: Unified representation linking domains, files, and functions
- **Mermaid Diagrams**: Visual representations of domains
- **Domain Taxonomy JSON**: Comprehensive JSON representation of the codebase
- **Codebase Explanation**: Natural language explanation of the codebase
