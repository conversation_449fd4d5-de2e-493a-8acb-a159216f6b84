```mermaid
flowchart TD
%%-------------------------
%% COLOR DEFINTIONS
%%-------------------------
%% Core domain files: pastel blue #D4F1F9
%% Supporting/utility files: pastel yellow #FFF8DC
%% Data structure files: pastel green #E0F8E0
%% Error handling files: pastel red #FFE4E1
%% Initialization/setup files: pastel purple #E6E6FA
%% Groupings: very light gray #F8F8F8, pastel borders
%% Node shape: all rounded rectangles unless otherwise specified

%%-------------------------
%% SUBGRAPH: TODOS CORE LOGIC
%%-------------------------
subgraph sgTodos["Todos Domain: Core Logic"]
  direction TB
  style sgTodos fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded

  TodosFinder["TodosFinder
  finder, cross-project access"]
  style TodosFinder fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

  PendingTodosFinder["PendingTodosFinder
  specialized finder for pending todos"]
  style PendingTodosFinder fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

  ResourceStateEventFinder["ResourceStateEventFinder
  finds state events for resources"]
  style ResourceStateEventFinder fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

  TodoPresenter["TodoPresenter
  presents Todo for views/API"]
  style TodoPresenter fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

  TodoPolicy["TodoPolicy
  authorization for Todo actions"]
  style TodoPolicy fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

  TodosActions["TodosActions Controller concern"]
  style TodosActions fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rounded-rectangle

  SystemNoteMetadata["SystemNoteMetadata
  tracks system  metadata"]
  style SystemNoteMetadata fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rounded-rectangle

  QuickActionsStatus["QuickActionsStatus
  tracks status/messages for quick actions in "]
  style QuickActionsStatus fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rounded-rectangle

end

%%-------------------------
%% SUBGRAPH: TODO DATA STRUCTURES
%%-------------------------
subgraph sgTodoDS["Todos Domain: Data Structures"]
  direction TB
  style sgTodoDS fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2,rounded

  ApiEntitiesTodo["API::Entities::Todo
  exposes Todo fields for API"]
  style ApiEntitiesTodo fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rounded-rectangle

  TypeTodoSortEnum["Types::TodoSortEnum
  enum for todo sorting in GraphQL"]
  style TypeTodoSortEnum fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rounded-rectangle

  TypeCurrentUserTodos["Types::CurrentUserTodos
  GraphQL interface for current user's todos"]
  style TypeCurrentUserTodos fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rounded-rectangle

end

%%-------------------------
%% SUBGRAPH: GRAPHQL API
%%-------------------------
subgraph sgGQL["Todos Domain: GraphQL API"]
  direction TB
  style sgGQL fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded

  GQLTodosResolver["TodosResolver
  GraphQL resolver for todos"]
  style GQLTodosResolver fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

  GQLMutCreate["Mutations::Todos::Create"]
  style GQLMutCreate fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

  GQLMutSnooze["Mutations::Todos::Snooze"]
  style GQLMutSnooze fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

  GQLMutUnSnooze["Mutations::Todos::UnSnooze"]
  style GQLMutUnSnooze fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

  GQLMutRestore["Mutations::Todos::Restore"]
  style GQLMutRestore fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

  GQLMutMarkDone["Mutations::Todos::MarkDone"]
  style GQLMutMarkDone fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

  GQLMutDeleteMany["Mutations::Todos::DeleteMany"]
  style GQLMutDeleteMany fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

  GQLMutDeleteAllDone["Mutations::Todos::DeleteAllDone"]
  style GQLMutDeleteAllDone fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

  GQLMutBaseMany["Mutations::Todos::BaseMany
  base for bulk mutations"]
  style GQLMutBaseMany fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

end

%%-------------------------
%% SUBGRAPH: ASYNC PROCESSING / WORKERS
%%-------------------------
subgraph sgWorkers["Todos Domain: Asynchronous Processing"]
  direction TB
  style sgWorkers fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2,rounded

  TodosDestroyerQueue["TodosDestroyerQueue
  concern, enqueue/shared logic"]
  style TodosDestroyerQueue fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rounded-rectangle

  ProjectPrivateWorker["TodosDestroyer::ProjectPrivateWorker"]
  style ProjectPrivateWorker fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rounded-rectangle

  EntityLeaveWorker["TodosDestroyer::EntityLeaveWorker"]
  style EntityLeaveWorker fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rounded-rectangle

  ConfidentialIssueWorker["TodosDestroyer::ConfidentialIssueWorker"]
  style ConfidentialIssueWorker fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rounded-rectangle

  PrivateFeaturesWorker["TodosDestroyer::PrivateFeaturesWorker"]
  style PrivateFeaturesWorker fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rounded-rectangle

  GroupPrivateWorker["TodosDestroyer::GroupPrivateWorker"]
  style GroupPrivateWorker fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rounded-rectangle

  DestroyedIssuableWorker["TodosDestroyer::DestroyedIssuableWorker"]
  style DestroyedIssuableWorker fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rounded-rectangle

  DestroyedDesignsWorker["TodosDestroyer::DestroyedDesignsWorker"]
  style DestroyedDesignsWorker fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rounded-rectangle

  DeleteAllDoneWorker["Todos::DeleteAllDoneWorker"]
  style DeleteAllDoneWorker fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rounded-rectangle

end

%%-------------------------
%% SUBGRAPH: TODO SERVICES AND BUSINESS LOGIC
%%-------------------------
subgraph sgServices["Todos Domain: Services and Business Logic"]
  direction TB
  style sgServices fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded

  AllowedTargetFilterService["Todos::AllowedTargetFilterService
  filters todos for visibility/permissions"]
  style AllowedTargetFilterService fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

  SnoozingService["Todos::SnoozingService
  handles snoozing/un-snoozing logic"]
  style SnoozingService fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

  DestroyConfidentialIssueService["Todos::Destroy::ConfidentialIssueService"]
  style DestroyConfidentialIssueService fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

  DestroyProjectPrivateService["Todos::Destroy::ProjectPrivateService"]
  style DestroyProjectPrivateService fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

  DestroyDesignService["Todos::Destroy::DesignService"]
  style DestroyDesignService fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

  DestroyGroupPrivateService["Todos::Destroy::GroupPrivateService"]
  style DestroyGroupPrivateService fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

  DestroyDestroyedIssuableService["Todos::Destroy::DestroyedIssuableService"]
  style DestroyDestroyedIssuableService fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

  DestroyEntityLeaveService["Todos::Destroy::EntityLeaveService"]
  style DestroyEntityLeaveService fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

  DeleteDoneTodosService["Todos::Delete::DoneTodosService"]
  style DeleteDoneTodosService fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

  DestroyBaseService["Todos::Destroy::BaseService
  base service for destruction logic"]
  style DestroyBaseService fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle
end

%%-------------------------
%% SUBGRAPH: CONTROLLERS
%%-------------------------
subgraph sgControllers["Todos Domain: Controllers"]
  direction TB
  style sgControllers fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded

  DashboardTodosController["Dashboard::TodosController
  dashboard, per-user todos"]
  style DashboardTodosController fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

  ProjectsTodosController["Projects::TodosController
  project-scoped todos"]
  style ProjectsTodosController fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

end

%%-------------------------
%% SUBGRAPH: UI/HELPERS
%%-------------------------
subgraph sgUI["Todos Domain: Helpers & Presentation"]
  direction TB
  style sgUI fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,rounded

  IssuablesHelper["IssuablesHelper
  serialization, sidebar, label/issuable helpers"]
  style IssuablesHelper fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rounded-rectangle

  NotificationRecipientsRequestReview["NotificationRecipients::Builder::RequestReview
  sends notifications on review requests"]
  style NotificationRecipientsRequestReview fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rounded-rectangle

end

%%-------------------------
%% SUBGRAPH: QUICK ACTIONS/COMMANDS
%%-------------------------
subgraph sgQuickActions["Todos Domain: Quick Actions & Integration"]
  direction TB
  style sgQuickActions fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,rounded

  QuickActionsInterpretService["QuickActions::InterpretService
  parses and interprets quick action commands"]
  style QuickActionsInterpretService fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rounded-rectangle

end

%%-------------------------
%% CROSS-SUBGRAPH RELATIONSHIPS
%%-------------------------

%% Core finders and presenters
TodosFinder -->|refines data| TodoPresenter
TodosFinder -->|finds| PendingTodosFinder
TodosFinder -->|uses| ResourceStateEventFinder

%% Finders to GraphQL
TodosFinder -->|used by| GQLTodosResolver
PendingTodosFinder -->|pending queries| GQLTodosResolver

%% Presenters and API entities
TodoPresenter -->|format for API| ApiEntitiesTodo
ApiEntitiesTodo -->|exposes| TypeCurrentUserTodos

%% Policies in queries and mutations
TodosFinder -->|authorization| TodoPolicy
TodoPolicy -->|checked by| GQLTodosResolver

%% GraphQL mutations bulk uses base
GQLMutDeleteMany -- extends --> GQLMutBaseMany
GQLMutDeleteMany -- invokes --> DeleteDoneTodosService
GQLMutDeleteAllDone -- invokes --> DeleteAllDoneWorker

GQLMutSnooze -- uses --> SnoozingService
GQLMutUnSnooze -- uses --> SnoozingService
GQLMutRestore -- uses --> AllowedTargetFilterService
GQLMutCreate -- uses --> AllowedTargetFilterService
GQLMutMarkDone -- uses --> DeleteDoneTodosService

%% GraphQL Sort Enum
TypeTodoSortEnum -- used by --> GQLTodosResolver

%% Resolvers
GQLTodosResolver -->|resolves for| TypeCurrentUserTodos

%% UI helpers and controllers
IssuablesHelper -- support --> DashboardTodosController
IssuablesHelper -- support --> ProjectsTodosController

%% Controller concern
TodosActions -- included in --> DashboardTodosController
TodosActions -- included in --> ProjectsTodosController

%% Dashboard/Projects controllers act as entryways
DashboardTodosController -- queries --> TodosFinder
ProjectsTodosController -- queries --> TodosFinder

%% Async worker queues
ProjectPrivateWorker -- includes --> TodosDestroyerQueue
EntityLeaveWorker -- includes --> TodosDestroyerQueue
ConfidentialIssueWorker -- includes --> TodosDestroyerQueue
PrivateFeaturesWorker -- includes --> TodosDestroyerQueue
GroupPrivateWorker -- includes --> TodosDestroyerQueue
DestroyedIssuableWorker -- includes --> TodosDestroyerQueue
DestroyedDesignsWorker -- includes --> TodosDestroyerQueue
DeleteAllDoneWorker -- includes --> TodosDestroyerQueue

%% Async workers invoke domain services
ProjectPrivateWorker -- invokes --> DestroyProjectPrivateService
EntityLeaveWorker -- invokes --> DestroyEntityLeaveService
ConfidentialIssueWorker -- invokes --> DestroyConfidentialIssueService
PrivateFeaturesWorker -- invokes --> DestroyGroupPrivateService
GroupPrivateWorker -- invokes --> DestroyGroupPrivateService
DestroyedIssuableWorker -- invokes --> DestroyDestroyedIssuableService
DestroyedDesignsWorker -- invokes --> DestroyDesignService
DeleteAllDoneWorker -- invokes --> DeleteDoneTodosService

%% Service layer logic inheritance/delegation
DestroyConfidentialIssueService -- extends --> DestroyBaseService
DestroyProjectPrivateService -- extends --> DestroyBaseService
DestroyDesignService -- uses --> DestroyBaseService
DestroyGroupPrivateService -- extends --> DestroyBaseService
DestroyDestroyedIssuableService -- uses --> DestroyBaseService
DestroyEntityLeaveService -- extends --> DestroyBaseService
DeleteDoneTodosService -- extends --> DestroyBaseService

%% Feature logic
AllowedTargetFilterService -- filters --> TodosFinder
AllowedTargetFilterService -- filters --> PendingTodosFinder
SnoozingService -- acts on --> TodosFinder

%% System Note support/metadata for todos, links to quick actions and UI
SystemNoteMetadata -- referenced by --> QuickActionsStatus
SystemNoteMetadata -- tracked by --> QuickActionsInterpretService
QuickActionsInterpretService -- used by --> QuickActionsStatus

%% Notification/Review collaboration support
NotificationRecipientsRequestReview -- processes --> SystemNoteMetadata

%% Highlight core data flow from APIs and controllers
DashboardTodosController -- renders --> TodoPresenter
ProjectsTodosController -- renders --> TodoPresenter

%% Helpers for Dashboard UI
IssuablesHelper -- supports --> TodoPresenter

%% UI triggers QuickActions
QuickActionsInterpretService -- applied in --> DashboardTodosController
QuickActionsInterpretService -- applied in --> ProjectsTodosController

%%-------------------------
%% LAYOUT ADJUSTMENTS FOR VERTICAL ALIGNMENT
%%-------------------------
sgControllers --> sgTodos
sgTodos --> sgServices
sgTodos --> sgGQL
sgGQL --> sgServices
sgServices --> sgWorkers
sgServices --> sgTodoDS
sgServices --> sgUI
sgTodos --> sgUI
sgQuickActions --> sgTodos
sgQuickActions --> sgControllers
sgUI --> sgQuickActions

%%-------------------------
%% DOMAIN INTERACTIONS SUMMARY
%%-------------------------
%% User/API/GraphQL --> Controllers/GraphQL Resolver --> Finders/Policy --> Services (filter, snoozing, destroying)
%% --> Workers (delete, destroy) --> Data structures/Presenters/Entities --> UI Helpers/Notification/Command Systems
```