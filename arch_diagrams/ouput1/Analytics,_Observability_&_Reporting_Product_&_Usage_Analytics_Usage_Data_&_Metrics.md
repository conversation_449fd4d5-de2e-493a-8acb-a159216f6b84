```mermaid
flowchart TD
  %% STYLE DEFINITIONS
  %% Node shapes and color coding
  classDef core fill:#D4F1F9,stroke:#91c2e2,stroke-width:2px,stroke-dasharray:0,rx:10,ry:10;
  classDef util fill:#FFF8DC,stroke:#e8d593,stroke-width:2px,stroke-dasharray:0,rx:10,ry:10;
  classDef ds fill:#E0F8E0,stroke:#88cbaa,stroke-width:2px,stroke-dasharray:0,rx:10,ry:10;
  classDef error fill:#FFE4E1,stroke:#ffb6b0,stroke-width:2px,stroke-dasharray:0,rx:10,ry:10;
  classDef init fill:#E6E6FA,stroke:#b7b7e4,stroke-width:2px,stroke-dasharray:0,rx:10,ry:10;
  classDef subgraph fill:#F8F8F8,stroke:#B2DFEE,stroke-width:2px,color:#444,rx:15,ry:15;

  %% 1. CORE DOMAIN: USAGE DATA METRICS AND USAGE DATA (REPORTING CADENCE, PING ETC)
  subgraph USAGE DATA & METRICS["Usage Data & Metrics":::subgraph]
    direction TB
    usage_data[lib/gitlab/usage_data.rb\n"Aggregates usage data for analytics":::core]
    usage_data_metrics[lib/gitlab/usage_data_metrics.rb\n"Builds metrics-based Usage Ping payload":::core]
    usage_data_non_sql_metrics[lib/gitlab/usage_data_non_sql_metrics.rb\n"Non-SQL UsageData metrics":::core]
    ee_usage_data[ee/lib/ee/gitlab/usage_data.rb\n"EE extension for UsageData":::core]
    metric_definition[lib/gitlab/usage/metric.rb\n"Defines/aggregates metric definitions":::ds]
    metric_query[lib/gitlab/usage/metrics/query.rb\n"Executes/retrieves DB metrics":::util]
    metric_key_path_proc[lib/gitlab/usage/metrics/key_path_processor.rb\n"Builds metric payload key paths":::util]
    service_ping_report[spec/lib/gitlab/usage/service_ping_report_spec.rb\n"Spec: Service Ping report logic"]
    metric_metadata_decorator[lib/gitlab/usage/service_ping/legacy_metric_metadata_decorator.rb\n"Legacy metadata wrapper":::util]
    instrumented_payload[lib/gitlab/usage/service_ping/instrumented_payload.rb\n"Builds instrumented metrics payload":::util]
    payload_keys_proc[lib/gitlab/usage/service_ping/payload_keys_processor.rb\n"Processes keys in reports":::util]
    topology[lib/gitlab/usage_data/topology.rb\n"Node & service topology metrics":::core]
    topology_spec[spec/lib/gitlab/usage_data/topology_spec.rb\n"Spec: topology data and queries"]

    usage_data_metrics --> usage_data
    usage_data_non_sql_metrics --> usage_data
    usage_data_metrics --> metric_definition
    usage_data_metrics --> metric_query
    usage_data_metrics --> metric_metadata_decorator
    usage_data_metrics --> metric_key_path_proc
    usage_data_metrics --> service_ping_report
    usage_data_metrics --> instrumented_payload
    instrumented_payload --> metric_definition
    usage_data_metrics --> payload_keys_proc
    payload_keys_proc --> metric_key_path_proc
    ee_usage_data --> usage_data
    usage_data_metrics --> topology
    topology_spec --> topology

    class usage_data,usage_data_metrics,usage_data_non_sql_metrics,topology,ee_usage_data core;
    class metric_definition ds;
    class metric_query,metric_key_path_proc,metric_metadata_decorator,instrumented_payload,payload_keys_proc util;
  end

  %% 2. METRIC INSTRUMENTATION / TYPES OF METRICS
  subgraph INSTRUMENTATIONS["Metric Instrumentations":::subgraph]
    direction TB
    base_metric[lib/gitlab/usage/metrics/instrumentations/base_metric.rb\n"Base class for all metrics":::core]
    base_integrations_metric[lib/gitlab/usage/metrics/instrumentations/base_integrations_metric.rb\n"Base for integrations metrics":::core]
    database_metric[lib/gitlab/usage/metrics/instrumentations/database_metric.rb\n"DB-based metric count/sum/..."]
    generic_metric[lib/gitlab/usage/metrics/instrumentations/generic_metric.rb\n"Generic single-value metric"]
    redis_metric[lib/gitlab/usage/metrics/instrumentations/redis_metric.rb\n"Redis key-based metric":::core]
    redis_hll_metric[lib/gitlab/usage/metrics/instrumentations/redis_hll_metric.rb\n"HyperLogLog event metrics":::core]
    unique_count_metric[lib/gitlab/usage/metrics/instrumentations/unique_count_metric.rb\n"Unique users count HLL"]
    numbers_metric[lib/gitlab/usage/metrics/instrumentations/numbers_metric.rb\n"Numeric aggregation metric":::util]
    prometheus_metric[lib/gitlab/usage/metrics/instrumentations/prometheus_metric.rb\n"Prometheus-based metric":::core]

    database_metric --> base_metric
    base_integrations_metric --> database_metric
    generic_metric --> base_metric
    redis_metric --> base_metric
    numbers_metric --> base_metric
    redis_hll_metric --> base_metric
    unique_count_metric --> base_metric
    prometheus_metric --> generic_metric

    class base_metric,base_integrations_metric,redis_metric,redis_hll_metric,prometheus_metric core;
    class database_metric generic_metric numbers_metric base_integrations_metric unique_count_metric;
  end

  %% 3. METRIC IMPLEMENTATIONS INSTRUMENTATIONS
  subgraph METRIC_IMPL["Metric Definitions & Implementations":::subgraph]
    direction TB
    impl1[lib/gitlab/usage/metrics/instrumentations/count_issues_metric.rb\n"Counts issues":::core]
    impl2[lib/gitlab/usage/metrics/instrumentations/count_projects_metric.rb\n"Counts projects"]
    impl3[lib/gitlab/usage/metrics/instrumentations/count_users_creating_issues_metric.rb\n"Counts users creating issues"]
    impl4[lib/gitlab/usage/metrics/instrumentations/uuid_metric.rb\n"Instance UUID":::core]
    impl5[lib/gitlab/usage/metrics/instrumentations/version_metric.rb\n"GitLab version"]
    impl6[lib/gitlab/usage/metrics/instrumentations/edition_metric.rb\n"GitLab edition"]
    impl7[lib/gitlab/usage/metrics/instrumentations/active_user_count_metric.rb\n"Active user count"]
    impl8[ee/lib/gitlab/usage/metrics/instrumentations/count_users_creating_ci_builds_metric.rb\n"EE: Users creating CI builds"]
    impl9[lib/gitlab/usage/metrics/instrumentations/click_house_configured_metric.rb\n"ClickHouse configured metric"]
    impl10[lib/gitlab/usage/metrics/instrumentations/collected_data_categories_metric.rb\n"Collected data categories"]
    ee_impl1[ee/lib/gitlab/usage/metrics/instrumentations/zoekt_search_enabled_metric.rb\n"EE: Zoekt search enabled"]

    impl1 --> database_metric
    impl2 --> database_metric
    impl3 --> database_metric
    impl4 --> generic_metric
    impl5 --> generic_metric
    impl6 --> generic_metric
    impl7 --> database_metric
    impl8 --> database_metric
    impl9 --> generic_metric
    impl10 --> generic_metric
    ee_impl1 --> generic_metric

    %% Major groups of instrumented metrics all derive from base and generic types
    class impl1,impl2,impl3,impl4,impl5,impl6,impl7,impl8,impl9,impl10,ee_impl1 core;
  end
  METRIC_IMPL --> INSTRUMENTATIONS

  %% 4. USAGE DATA COUNTERS & UNIQUE EVENTS
  subgraph USAGE_COUNTERS["Unique Event Tracking & Counters":::subgraph]
    direction TB
    hll_redis_counter[lib/gitlab/usage_data_counters/hll_redis_counter.rb\n"HLL event counter":::core]
    redis_counter[lib/gitlab/usage_data_counters/redis_counter.rb\n"Basic redis-based counter"]
    base_counter[lib/gitlab/usage_data_counters/base_counter.rb\n"Base for ID counters"]
    usage_data_counters[lib/gitlab/usage_data_counters.rb\n"Main registry for all counters"]
    issue_activity_counter[lib/gitlab/usage_data_counters/issue_activity_unique_counter.rb\n"Tracks unique Issue events"]
    merge_request_widget_counter[lib/gitlab/usage_data_counters/merge_request_widget_extension_counter.rb\n"MR Widget extension events"]
    work_item_counter[lib/gitlab/usage_data_counters/work_item_activity_unique_counter.rb]
    ipynb_diff_counter[lib/gitlab/usage_data_counters/ipynb_diff_activity_counter.rb]
    jetbrains_bundle_counter[lib/gitlab/usage_data_counters/jetbrains_bundled_plugin_activity_unique_counter.rb]
    jetbrains_plugin_counter[lib/gitlab/usage_data_counters/jetbrains_plugin_activity_unique_counter.rb]
    vscode_extension_counter[lib/gitlab/usage_data_counters/vscode_extension_activity_unique_counter.rb]
    visual_studio_extension_counter[lib/gitlab/usage_data_counters/visual_studio_extension_activity_unique_counter.rb]
    neovim_plugin_counter[lib/gitlab/usage_data_counters/neovim_plugin_activity_unique_counter.rb]
    gitlab_cli_counter[lib/gitlab/usage_data_counters/gitlab_cli_activity_unique_counter.rb]

    hll_redis_counter --> redis_counter
    usage_data_counters --> base_counter
    merge_request_widget_counter --> base_counter
    issue_activity_counter --> hll_redis_counter
    merge_request_widget_counter --> hll_redis_counter
    work_item_counter --> hll_redis_counter
    ipynb_diff_counter --> hll_redis_counter
    jetbrains_bundle_counter --> hll_redis_counter
    jetbrains_plugin_counter --> hll_redis_counter
    vscode_extension_counter --> hll_redis_counter
    visual_studio_extension_counter --> hll_redis_counter
    neovim_plugin_counter --> hll_redis_counter
    gitlab_cli_counter --> hll_redis_counter

    class hll_redis_counter,usage_data_counters,base_counter core;
    class redis_counter,merge_request_widget_counter,work_item_counter,ipynb_diff_counter,jetbrains_bundle_counter,jetbrains_plugin_counter,vscode_extension_counter,visual_studio_extension_counter,neovim_plugin_counter,gitlab_cli_counter util;
    class issue_activity_counter core;
  end

  USAGE_COUNTERS --> USAGE_DATA & METRICS

  %% 5. PRODUCT & USAGE ANALYTICS API AND GRAPHQL
  subgraph API_LAYER["Analytics APIs & GraphQL":::subgraph]
    direction TB
    api_usage_track[lib/api/usage_data_track.rb\n"REST API: Track internal usage events":::core]
    api_usage_queries[lib/api/usage_data_queries.rb\n"REST API Usage data queries"]
    api_usage_non_sql[lib/api/usage_data_non_sql_metrics.rb\n"REST API Non-SQL Usage data"]
    gql_measurements_resolver[app/graphql/resolvers/admin/analytics/usage_trends/measurements_resolver.rb\n"GraphQL Admin: Usage trend Measurements resolver"]
    gql_project_usage_data_resolver[ee/app/graphql/resolvers/product_analytics/project_usage_data_resolver.rb\n"GraphQL: Project analytics usage data resolver"]
    gql_state_enum[ee/app/graphql/types/product_analytics/state_enum.rb\n"GraphQL: Analytics state enum":::ds]
    gql_category_enum[ee/app/graphql/types/product_analytics/category_enum.rb\n"GraphQL: Dashboard category enum":::ds]
    gql_month_selection[ee/app/graphql/types/product_analytics/month_selection_input_type.rb\n"GraphQL: Usage data input type":::ds]

    api_usage_track --> usage_data
    api_usage_queries --> usage_data
    api_usage_non_sql --> usage_data_non_sql_metrics
    gql_measurements_resolver --> usage_data
    gql_measurements_resolver --> metric_definition
    gql_project_usage_data_resolver --> usage_data
    gql_project_usage_data_resolver --> gql_month_selection
    gql_measurements_resolver --> gql_state_enum
    gql_project_usage_data_resolver --> gql_category_enum

    class api_usage_track,api_usage_queries,api_usage_non_sql,gql_measurements_resolver,gql_project_usage_data_resolver core;
    class gql_state_enum,gql_category_enum,gql_month_selection ds;
  end

  API_LAYER --> USAGE_DATA & METRICS

  %% 6. USAGE DATA - SERVICE PING & SERVICE ARCHITECTURE
  subgraph SERVICE_PING["Service Ping Collection & Submission":::subgraph]
    direction TB
    build_payload[lib/service_ping/build_payload.rb\n"Builds Service Ping payload":::core]
    devops_report[lib/service_ping/devops_report.rb\n"DevOps analytics report":::core]
    service_ping_settings[lib/service_ping/service_ping_settings.rb\n"Service Ping feature flags":::util]
    submit_service_ping[app/services/service_ping/submit_service.rb\n"Handles submission of Service Ping":::core]

    build_payload --> usage_data_metrics
    build_payload --> usage_data
    devops_report --> build_payload
    submit_service_ping --> build_payload
    submit_service_ping --> error_handler
    service_ping_settings --> build_payload

    class build_payload,devops_report,submit_service_ping core;
    class service_ping_settings util;
  end

  SERVICE_PING --> USAGE_DATA & METRICS

  %% 7. SUPPORT FOR DATA STRUCTURE & TRANSFORMATION
  subgraph SUPPORT["Support, Data Structure, & Transformation":::subgraph]
    direction TB
    page_layout_helper[app/helpers/page_layout_helper.rb\n"Layout for usage/analytics UIs":::util]
    faster_cache_keys[app/models/concerns/faster_cache_keys.rb\n"Optimizes cache key gen":::util]
    gitlab_tracking[lib/gitlab/tracking.rb\n"Core tracking for events/metrics":::core]
    gitlab_counters[lib/gitlab/counters.rb\n"Helper for various data counters":::util]
    usage_utils[lib/gitlab/utils/usage_data.rb\n"Aggregation helpers for usage data":::util]
    gitlab_analytics_date_filler[lib/gitlab/analytics/date_filler.rb\n"Fills data for time series":::util]
    pipeline_scope_counts[lib/gitlab/pipeline_scope_counts.rb\n"Support for pipeline stats":::ds]
    workers_argument_builder[lib/gitlab/analytics/usage_trends/workers_argument_builder.rb\n"Builds args for trend workers":::util]

    usage_utils --> usage_data
    usage_utils --> database_metric
    usage_utils --> hll_redis_counter
    gitlab_tracking --> usage_data
    workers_argument_builder --> metric_definition

    class page_layout_helper,faster_cache_keys,gitlab_counters,usage_utils,gitlab_analytics_date_filler,workers_argument_builder util;
    class gitlab_tracking core;
    class pipeline_scope_counts ds;
  end

  SUPPORT --> USAGE_DATA & METRICS

  %% 8. WORKERS: JOBS COLLECTING & FLUSHING USAGE METRICS
  subgraph WORKERS["Background Jobs for Analytics":::subgraph]
    direction TB
    service_ping_worker[app/workers/gitlab_service_ping_worker.rb\n"Worker: Collects Service Ping":::core]
    version_check_worker[app/workers/gitlab/version/version_check_cron_worker.rb\n"Worker: Version check schedule":::init]
    usage_data_ai_cron[ee/app/workers/analytics/dump_ai_user_metrics_write_buffer_cron_worker.rb\n"Cron: Dumps AI user metrics"]
    product_analytics_sync[ee/app/workers/product_analytics/sync_funnels_worker.rb\n"Worker: Product analytics funnel"]
    usage_buffer_worker[ee/app/workers/usage_events/dump_write_buffer_cron_worker.rb\n"Cron: Dump/write usage events"]
    write_buffer_processor[ee/app/workers/concerns/analytics/write_buffer_processor_worker.rb\n"Worker concern: buffer processor":::util]
    base_usage_backfill[ee/app/workers/analytics/base_usage_backfill_worker.rb\n"Base for backfill workers"]

    service_ping_worker --> usage_data
    service_ping_worker --> build_payload
    service_ping_worker --> submit_service_ping
    version_check_worker --> usage_data_metrics
    usage_data_ai_cron --> write_buffer_processor
    product_analytics_sync --> write_buffer_processor
    usage_buffer_worker --> write_buffer_processor

    base_usage_backfill --> metric_definition

    class service_ping_worker core;
    class version_check_worker init;
    class usage_data_ai_cron,product_analytics_sync,usage_buffer_worker base_usage_backfill core;
    class write_buffer_processor util;
  end

  WORKERS --> USAGE_DATA & METRICS

  %% 9. DOMAIN DATA MODELS: USAGE / ANALYTICS
  subgraph MODELS["Usage Analytics Domain Models":::subgraph]
    direction TB
    queries_service_ping[app/models/service_ping/queries_service_ping.rb\n"ServicePing SQL-based metrics":::ds]
    non_sql_service_ping[app/models/service_ping/non_sql_service_ping.rb\n"ServicePing non-SQL data":::ds]
    ai_user_metrics[ee/app/models/ai/user_metrics.rb\n"AI user analytics metrics":::ds]
    value_stream_dashboard_count[ee/app/models/analytics/value_stream_dashboard/count.rb\n"Value Stream dashboard metrics":::ds]
    snapshot[ee/app/models/analytics/devops_adoption/snapshot.rb\n"Devops analytics snapshot":::ds]
    queries_service_ping --> usage_data
    non_sql_service_ping --> usage_data
    ai_user_metrics --> usage_data_metrics
    value_stream_dashboard_count --> usage_data_metrics
    snapshot --> usage_data_metrics

    class queries_service_ping,non_sql_service_ping,ai_user_metrics,value_stream_dashboard_count,snapshot ds;
  end

  MODELS --> USAGE_DATA & METRICS

  %% 10. POLICY & CONTROLLERS ADMIN VIEWS
  subgraph UI_CONTROLLERS["Admin UI Controllers & Policies":::subgraph]
    direction TB
    usage_trends_ctrl[app/controllers/admin/usage_trends_controller.rb\n"Admin: Usage trends UI":::core]
    cohorts_ctrl[app/controllers/admin/cohorts_controller.rb\n"Admin: Cohorts report UI":::core]
    mr_analytics_ctrl[ee/app/controllers/projects/analytics/merge_request_analytics_controller.rb\n"EE: MR Analytics/Product":::core]
    measurement_policy[app/policies/analytics/usage_trends/measurement_policy.rb\n"Access control: UsageTrends":::util]

    usage_trends_ctrl --> usage_data
    cohorts_ctrl --> usage_data
    usage_trends_ctrl --> measurement_policy
    mr_analytics_ctrl --> usage_data

    class usage_trends_ctrl,cohorts_ctrl,mr_analytics_ctrl core;
    class measurement_policy util;
  end

  UI_CONTROLLERS --> USAGE_DATA & METRICS

  %% 11. DATA TEAM & DANGER/TOOLING GOVERNANCE
  subgraph GOVERNANCE["Data Warehouse Governance / Tooling":::subgraph]
    direction TB
    data_team_tooling[tooling/danger/datateam.rb\n"Danger file: Data team governance":::util]
    data_team_tooling --> usage_data_metrics
    class data_team_tooling util;
  end

  GOVERNANCE --> USAGE_DATA & METRICS

  %% 12. EE-SPECIFICS/EXTENSIONS
  subgraph EE_FEATURES["EE-Specific Metrics, Mixins, Analytics":::subgraph]
    direction TB
    ee_measurement[ee/app/models/ee/analytics/usage_trends/measurement.rb\n"EE: Analytics UsageTrends Measurement mixin":::core]
    ee_push_event_payload[ee/app/models/ee/push_event_payload.rb\n"EE: Push event analytics":::core]
    ee_analytics_entity[ee/app/serializers/user_analytics_entity.rb\n"EE: Analytics user serializer":::util]

    ee_measurement --> usage_data_metrics
    ee_push_event_payload --> usage_data_metrics
    ee_analytics_entity --> usage_data_metrics

    class ee_measurement,ee_push_event_payload core;
    class ee_analytics_entity util;
  end

  EE_FEATURES --> USAGE_DATA & METRICS

  %% 13. TESTS & SPEC COVERAGE
  subgraph TESTS["Specs & Test Coverage":::subgraph]
    direction TB
    usage_data_spec[spec/lib/gitlab/usage_data_spec.rb\n"Unit: UsageData logic"]
    usage_metrics_spec[ee/spec/lib/gitlab/usage_data_metrics_spec.rb\n"EE: Spec, UsageDataMetrics"]
    metrics_def_spec[spec/lib/gitlab/usage/metric_definition_spec.rb\n"Spec: Metric definition logic"]
    usage_project_analytics_spec[ee/spec/lib/analytics/product_analytics/project_usage_data_spec.rb\n"EE: Spec ProjectUsageData"]
    usage_utils_spec[spec/lib/gitlab/utils/usage_data_spec.rb\n"Spec: UsageUtils"]
    code_review_events_spec[spec/lib/gitlab/usage_data_counters/code_review_events_spec.rb\n"Spec: Code Review metrics"]

    usage_data_spec --> usage_data
    usage_metrics_spec --> usage_data_metrics
    metrics_def_spec --> metric_definition
    usage_project_analytics_spec --> MODELS
    usage_utils_spec --> usage_utils
    code_review_events_spec --> hll_redis_counter
  end

  %% SUBGRAPH RELATION LINES JOINING THE MAJOR BUILDING BLOCKS
  INSTRUMENTATIONS --> USAGE_DATA & METRICS
  USAGE_COUNTERS --> METRIC_IMPL
  SERVICE_PING --> USAGE_DATA & METRICS
  API_LAYER --> SERVICE_PING
  WORKERS --> MODELS
  SUPPORT --> METRIC_IMPL
  SUPPORT --> WORKERS
  MODELS --> METRIC_IMPL
  EE_FEATURES --> MODELS
  EE_FEATURES --> INSTRUMENTATIONS
  TESTS --> USAGE_DATA & METRICS
  TESTS --> METRIC_IMPL
  TESTS --> SUPPORT

  %% OVERARCHING DOMAIN THEME SUBGRAPH
  subgraph ANALYTICS_OBSERVABILITY_REPORTING["Analytics, Observability, & Reporting - Product & Usage Analytics":::subgraph]
    direction TB
    USAGE_DATA & METRICS
    INSTRUMENTATIONS
    METRIC_IMPL
    USAGE_COUNTERS
    SERVICE_PING
    API_LAYER
    WORKERS
    SUPPORT
    MODELS
    UI_CONTROLLERS
    GOVERNANCE
    EE_FEATURES
    TESTS
  end
```