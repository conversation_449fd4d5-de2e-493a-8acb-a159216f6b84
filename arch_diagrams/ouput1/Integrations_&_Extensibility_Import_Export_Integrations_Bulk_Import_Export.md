```mermaid
flowchart TB
  %% VERTICAL LAYOUT, PAST<PERSON> COLORS, LOGICAL GROUPING

  %% SUBGRAPH: CORE ENTITIES AND DATA STRUCTURES
  subgraph Core Entities & Data Structures
    direction TB
    style Core Entities & Data Structures fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded
    BI_Entity["BulkImports::Entity":::core] 
    BI_EntityType["Entity Type/Validation":::core]
    BI_Configuration["BulkImports::Configuration":::data]
    BI_ExportStatus["ExportStatus":::data]
    BI_ExportBatch["ExportBatch":::data]
    BI_ExportUpload["ExportUpload":::data]
    BI_BatchTracker["BatchTracker":::data]
    BI_Tracker["Tracker":::data]
    BI_FileTransfer["FileTransfer":::core]
    BI_GroupConfig["GroupConfig":::data]
    BI_ProjectConfig["ProjectConfig":::data]
  end

  %% SUBGRAPH: FINDERS QUERY/RETRIEVAL LAYER
  subgraph Entity & Import Finders
    direction TB
    style Entity & Import Finders fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded
    EntitiesFinder["EntitiesFinder":::core]
    ImportsFinder["ImportsFinder":::core]
  end

  %% SUBGRAPH: CONTROLLER
  subgraph Web & API Layer
    direction TB
    style Web & API Layer fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded
    BulkImportsController["BulkImportsController":::core]
  end

  %% SUBGRAPH: DOMAIN SERVICES
  subgraph Bulk Import Services
    direction TB
    style Bulk Import Services fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded
    CreateService["CreateService":::core]
    RelationExportService["RelationExportService":::core]
    RelationBatchExportService["RelationBatchExportService":::core]
    BatchedRelationExportService["BatchedRelationExportService":::core]
    TreeExportService["TreeExportService":::core]
    ExportService["ExportService":::core]
    FileExportService["FileExportService":::core]
    ArchiveExtractionService["ArchiveExtractionService":::core]
    FileDecompressionService["FileDecompressionService":::core]
    FileDownloadService["FileDownloadService":::core]
    UserContributionsExportService["UserContributionsExportService":::core]
    UpdateSourceUsersService["UpdateSourceUsersService":::core]
  end

  %% SUBGRAPH: USER REASSIGNMENT & SOURCE USERS
  subgraph Source Users Services
    direction TB
    style Source Users Services fill:#F8F8F8,stroke:#F8BC7C,stroke-width:2,rounded
    AcceptReassignmentService["AcceptReassignmentService":::support]
    RejectReassignmentService["RejectReassignmentService":::support]
    ReassignService["ReassignService":::support]
    ReassignPlaceholderUserRecordsService["ReassignPHUserRecordsService":::support]
  end

  %% SUBGRAPH: PIPELINE WORKERS BACKGROUND PROCESSORS
  subgraph Import & Export Worker Processors
    direction TB
    style Import & Export Worker Processors fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded
    UserContributionsExportWorker["UserContributionsExportWorker":::core]
    RelationExportWorker["RelationExportWorker":::core]
    RelationBatchExportWorker["RelationBatchExportWorker":::core]
    FinishProjectImportWorker["FinishProjectImportWorker":::core]
    StaleImportWorker["StaleImportWorker":::core]
    EntityWorker["EntityWorker":::core]
    PipelineWorker["PipelineWorker":::core]
    BulkImportWorker["BulkImportWorker":::core]
    TransformReferencesWorker["TransformReferencesWorker":::core]
  end

  %% UTILITY, SUPPORTING, AND ERROR HANDLING
  subgraph Supporting & Utility Layer
    direction TB
    style Supporting & Utility Layer fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,rounded
    AfterCommitQueue["AfterCommitQueue":::support]
    WithUploads["WithUploads":::support]
  end

  %% DATA NODE STYLES
  classDef core fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1,stroke-dasharray:0,rx:10,ry:10
  classDef data fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rx:10,ry:10
  classDef support fill:#FFF8DC,stroke:#FFF8DC,stroke-width:1,rx:10,ry:10
  classDef error fill:#FFE4E1,stroke:#FFE4E1,stroke-width:1,rx:10,ry:10
  classDef init fill:#E6E6FA,stroke:#E6E6FA,stroke-width:1,rx:10,ry:10

  %% INITIALIZATION/SERVICE ENTRYPOINTS
  class CreateService,RelationExportService,RelationBatchExportService,BatchedRelationExportService,ExportService,FileExportService init

  %% DOMAIN-CONCEPT FLOWS AND RELATIONSHIPS

  %% Core Entities
  BI_EntityType -- "Defines/Validates type for" --> BI_Entity
  BI_Entity -- "Has" --> BI_Tracker
  BI_Entity -- "Tracks batches via" --> BI_BatchTracker
  BI_Entity -- "Relies on" --> BI_ExportStatus
  BI_Entity -- "References" --> BI_Configuration
  BI_Entity -- "Uses" --> AfterCommitQueue
  BI_Entity -- "References file transfer via" --> BI_FileTransfer
  BI_Entity -- "Is used by" --> EntitiesFinder
  BI_Entity -- "Is used by" --> CreateService

  BI_ExportStatus -- "References" --> BI_Configuration
  BI_BatchTracker -- "Belongs to" --> BI_Tracker
  BI_ExportUpload -- "Belongs to" --> BI_ExportBatch
  BI_BatchTracker -- "Scoped by" --> BI_Entity

  BI_FileTransfer -- "Chooses config for portable" --> BI_GroupConfig
  BI_FileTransfer -- "Chooses config for portable" --> BI_ProjectConfig

  %% Finders
  EntitiesFinder -- "Performs queries on" --> BI_Entity
  EntitiesFinder -- "Filters/sorts" --> BI_Entity
  ImportsFinder -- "Queries via" --> BI_Entity
  ImportsFinder -- "Works with configuration via" --> BI_Configuration
  ImportsFinder -- "Is used by controller" --> BulkImportsController

  %% Controller
  BulkImportsController -- "Invokes" --> CreateService
  BulkImportsController -- "Uses" --> EntitiesFinder
  BulkImportsController -- "Uses" --> ImportsFinder

  %% Domain Services
  CreateService -- "Validates entity type via" --> BI_EntityType
  CreateService -- "Persists new entities" --> BI_Entity
  CreateService -- "Relies on" --> BI_Configuration
  RelationExportService -- "Works with" --> BI_ExportStatus
  RelationExportService -- "Coordinates workers" --> RelationExportWorker
  RelationBatchExportService -- "Coordinates workers" --> RelationBatchExportWorker
  RelationBatchExportService -- "Uses batch trackers" --> BI_BatchTracker
  BatchedRelationExportService -- "Splits relations/batches" --> RelationBatchExportService
  BatchedRelationExportService -- "Uses caching for" --> BI_BatchTracker
  TreeExportService -- "Uses file transfer config" --> BI_FileTransfer
  TreeExportService -- "Exports entities" --> BI_Entity
  ExportService -- "Performs user validation" --> BI_Entity
  ExportService -- "Outputs/export state" --> BI_ExportStatus
  FileExportService -- "Exports via config" --> BI_FileTransfer
  FileDownloadService -- "Downloads files for" --> ExportService
  FileDownloadService -- "Uses validations" --> Supporting & Utility Layer
  ArchiveExtractionService -- "Extracts files/archives" --> FileDecompressionService
  UpdateSourceUsersService -- "Fetches and updates source users for" --> BI_Entity

  %% Source Users / User Reassignment
  AcceptReassignmentService -- "Handles acceptance for" --> ReassignService
  RejectReassignmentService -- "Handles rejection for" --> ReassignService
  ReassignService -- "Validates and executes reassignment for" --> UpdateSourceUsersService
  ReassignPlaceholderUserRecordsService -- "Moves placeholder records during" --> UpdateSourceUsersService

  %% User Contribution Export
  UserContributionsExportService -- "Maps/user contributions to" --> BI_Entity
  UserContributionsExportWorker -- "Processes user contribution exports" --> UserContributionsExportService
  UserContributionsExportWorker -- "Enqueues/export events" --> ExportService

  %% Worker Relationships
  ExportService -- "Enqueues" --> RelationExportWorker
  ExportService -- "Enqueues batched jobs via" --> RelationBatchExportService
  RelationExportWorker -- "Performs export using" --> RelationExportService
  RelationBatchExportWorker -- "Coordinates batches using" --> RelationBatchExportService
  PipelineWorker -- "Runs pipelines" --> BI_Entity
  PipelineWorker -- "Runs pipelines" --> BI_Tracker
  PipelineWorker -- "Performs batch operations via" --> BatchedRelationExportService
  EntityWorker -- "Drives entity pipeline execution" --> PipelineWorker
  TransformReferencesWorker -- "Performs reference transformations on data" --> BI_Entity
  TransformReferencesWorker -- "Updates relationships for" --> UpdateSourceUsersService
  FinishProjectImportWorker -- "Finalizes after import" --> BI_Entity

  BulkImportWorker -- "Kicks off orchestration of" --> BI_Entity
  StaleImportWorker -- "Cleans up unfinished imports" --> BI_Entity

  %% File Transfer, Batch, and Upload Relationship
  BI_FileTransfer -- "Coordinates with" --> FileDownloadService
  FileDownloadService -- "Performs file validation via" --> Supporting & Utility Layer
  FileExportService -- "Archives and compresses with" --> ArchiveExtractionService

  %% Utility Layer
  BI_ExportUpload -- "Includes upload helpers via" --> WithUploads

  %% Core Data Interactions
  BI_Entity -- "Tracks export jobs via" --> BI_ExportBatch
  BI_ExportBatch -- "Has" --> BI_ExportUpload
  BI_Tracker -- "Links entity and process state" --> BatchTracker

  %% Styles for Subgroup Borders (for visual clarity)
  style Core Entities & Data Structures stroke:#9ED4EA,stroke-width:2
  style Entity & Import Finders stroke:#9ED4EA,stroke-width:2
  style Bulk Import Services stroke:#A393EB,stroke-width:2
  style Source Users Services stroke:#F8BC7C,stroke-width:2
  style Web & API Layer stroke:#9ED4EA,stroke-width:2
  style Import & Export Worker Processors stroke:#9ED4EA,stroke-width:2
  style Supporting & Utility Layer stroke:#FFF8DC,stroke-width:2
```