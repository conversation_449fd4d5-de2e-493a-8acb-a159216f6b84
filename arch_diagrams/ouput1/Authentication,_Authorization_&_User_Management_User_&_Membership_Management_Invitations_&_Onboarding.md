```mermaid
flowchart TB
  %% VERTICAL layout enforced
  %% Color/Style definitions based on provided pastel scheme

  %% --- CORE DOMAIN: USER MANAGEMENT ---
  subgraph User_Management[User Management]
    direction TB
    style User_Management fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded

    U1[app/models/users/namespace_commit_email.rb\nNamespace-Scoped User Email\nGroup-to-User-Email mapping]
    style U1 fill:#E0F8E0,stroke:#D4F1F9,stroke-width:2,stroke-dasharray:2 2,rounded

    U2[ee/app/models/ee/user_detail.rb\nExtended User Details\nOnboarding, Enterprise Assoc.]
    style U2 fill:#E0F8E0,stroke:#D4F1F9,stroke-width:2,stroke-dasharray:2 2,rounded

    U3[app/models/concerns/require_email_verification.rb\nRequire Email Verification\nConcern for ActiveSupport]
    style U3 fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rounded

    U4[app/validators/anti_abuse/unique_detumbled_email_validator.rb\nUnique Detumbled Email Validator\nPrevents email abuse]
    style U4 fill:#FFE4E1,stroke:#FFE4E1,stroke-width:2,rounded

    U1 --provides namespace email mapping for--> U2
    U1 --validates against--> U4
    U2 --used for onboarding/enterprise logic in--> Onboarding_Invitations
    U2 --read by--> RegHelpers
    U3 --required by user registration controllers--> Registration_Controllers
  end


  %% --- CORE DOMAIN: ONBOARDING & INVITATION ---
  subgraph Onboarding_Invitations[Onboarding & Invitation]
    direction TB
    style Onboarding_Invitations fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded

    O1[ee/app/services/onboarding/status_convert_to_invite_service.rb\nOnboarding Status to Invitation Service]
    style O1 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded

    O2[app/controllers/import/source_users_controller.rb\nImport Source Users Controller]
    style O2 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded

    O3[spec/features/invites_spec.rb\nInvitation Feature Spec & User Flow]
    style O3 fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rounded

    O2 --delegates reassignment to--> O1
    O2 --used by--> O3
    O1 --updates user status in--> U2
  end

  %% --- CORE DOMAIN: REGISTRATION & VERIFICATION ---
  subgraph Registration_Controllers[Registration & Verification Controllers]
    direction TB
    style Registration_Controllers fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded

    R1[ee/app/controllers/ee/registrations_controller.rb\nEnterprise Registration Controller]
    style R1 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded

    R2[app/controllers/users/terms_controller.rb\nTerms Acceptance Controller]
    style R2 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded
    R3[ee/app/controllers/ee/users/terms_controller.rb\nEe: Extended Terms Controller]
    style R3 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded

    R4[ee/app/controllers/users/base_identity_verification_controller.rb\nBase Identity Verification Controller]
    style R4 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded

    R1 --enforces onboarding/identity verification via--> U3
    R1 --controls registration flow, uses helpers--> RegHelpers
    R1 --calls--> Email_Verification_Services
    R1 --is extended by--> R3

    R2 --manages user agreement to terms\nworks with--> U2
    R3 --prepends additional behaviors to--> R2

    R4 --provides base logic for user identity verification--> Email_Verification_Services
    R4 --uses identity verification helpers--> VerificationHelpers
    R4 --coordinates with enterprise onboarding policies in--> U2
    R4 --uses error/event tracking services--> Error_Handling
  end

  %% --- CORE DOMAIN: EMAIL VERIFICATION & NOTIFICATION SERVICES ---
  subgraph Email_Verification_Services[Email Verification & Notification Services]
    direction TB
    style Email_Verification_Services fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded

    EV1[app/services/users/email_verification/base_service.rb\nBase Service for Email Verification]
    style EV1 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded

    EV2[app/services/users/email_verification/generate_token_service.rb\nService: Generate Email Verification Token]
    style EV2 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded

    EV3[ee/app/services/users/email_verification/send_custom_confirmation_instructions_service.rb\nService: Send Custom Confirmation Instructions]
    style EV3 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded

    EV4[app/services/users/support_pin/update_service.rb\nSupport PIN Update Service]
    style EV4 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded

    EV5[app/services/users/migrate_records_to_ghost_user_in_batches_service.rb\nMigrate Records to Ghost User in Batches]
    style EV5 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded

    EV6[app/services/concerns/users/new_user_notifier.rb\nNew User Notification\nSystem Hooks, Logging]
    style EV6 fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rounded

    EV7[ee/app/services/users/join_early_access_program_service.rb\nJoin Early Access Program Service]
    style EV7 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded

    EV8[ee/app/services/ee/emails/create_service.rb\nAudit Logging Email Creation Service]
    style EV8 fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rounded

    EV9[app/helpers/admin/identities_helper.rb\nProvider/Identity UI Helper]
    style EV9 fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rounded

    EV1 --base for verification--> EV2
    EV1 --extended by--> EV3
    EV2 --generates tokens for--> EV3

    EV3 --sends confirmations after registration via--> R1
    EV3 --inspects user onboarding progress from--> U2

    EV4 --generates support pins for users\nrelies on user info--> U2
    EV6 --notifies new users after successful onboarding/registration flows--> R1
    EV6 --notifies after PIN/update events--> EV4

    EV5 --migrates user records to ghosts for offboarding\nrequires user records from--> U2

    EV8 --logs audit on email creation\nused by onboarding and registration flows--> R1
    EV9 --renders identity info in admin views\nused when managing user sign-ins, onboarding, and emails
  end

  %% --- CORE DOMAIN: VERIFICATION & HELPER CONCERNS ---
  subgraph VerificationHelpers[Verification & Helper Concerns]
    direction TB
    style VerificationHelpers fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded

    V1[ee/app/helpers/users/identity_verification_helper.rb\nUI/JSON Helper for Identity Verification]
    style V1 fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rounded

    V2[ee/lib/users/identity_verification/authorize_ci.rb\nAuthorize CI for User after Verification]
    style V2 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded

    V1 --provides helper methods to--> R4
    V1 --used for JSON/HTML rendering in onboarding flows--> R1
    V2 --protects CI features with identity status\ncalled from controllers and flows--> R1
  end


  %% --- UI & PRESENTATION LAYER ---
  subgraph RegHelpers[Registration & Onboarding UI Helpers]
    direction TB
    style RegHelpers fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded

    H1[ee/app/helpers/ee/registrations_helper.rb\nRegistration Form/UX Helper]
    style H1 fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rounded

    H2[qa/qa/flow/sign_up.rb\nAcceptance Test Signup Automation]
    style H2 fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rounded

    H1 --reads onboarding/enterprise data from--> U2
    H1 --renders options for registration flows--> R1
    H2 --test framework using form flows and invitation flows--> O3
  end


  %% --- DOMAIN: BROADCASTS, MESSAGES and NOTIFICATIONS ---
  subgraph Broadcast_Notifications[Broadcasts & Notifications]
    direction TB
    style Broadcast_Notifications fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded

    BN1[app/controllers/users/broadcast_message_dismissals_controller.rb\nBroadcast Message Dismissals\nUser dismisses/consumes notifications]
    style BN1 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded

    BN1 --dismissal events update user onboarding/notification state--> U2
  end

  %% --- DOMAIN: LLM/TOOLS (Edge Case) ---
  subgraph Tools_LLM[LLM & Tool Integration Layer]
    direction TB
    style Tools_LLM fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded

    T1[ee/lib/gitlab/llm/chain/tools/tool.rb\nLLM Tool Interface User Context]
    style T1 fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rounded

    T1 --interfaces with user context--> U2
  end


  %% --- DOMAIN: SUPPORT TESTS (QA/UI) ---
  subgraph QA_Flows[QA Support UI Flows]
    direction TB
    style QA_Flows fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded

    Q1[qa/qa/page/import/review_reassignment.rb\nQA: Import Review & Reassignment Page]
    style Q1 fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rounded

    Q1 --QA UI overlay for invite reassignment--> O2
  end


  %% --- CORE RELATIONSHIPS BETWEEN DOMAIN GROUPS ---


  %% User Management <-> Registration/Controllers
  U2 --details used in--> R1
  U2 --read for onboarding checks by--> O1

  %% Onboarding & Invitations <-> Registration
  O2 --after reassignment initiate or complete onboarding in--> R1
  O1 --converts onboarding status to invitations in--> R1

  %% Registration Controllers <-> Email Verification
  R1 --delegates verification to--> Email_Verification_Services
  R4 --calls verification data from--> EV1

  %% User Management <-> Email Verification
  U1 --email-record for users (scope) validated via--> EV1
  U3 --email verification behavior for users--> EV1

  %% Email Verification <-> Validator
  EV1 --calls detumbled/unique validator--> U4

  %% Registration & Onboarding Helpers <-> Registration Controllers
  H1 --renders registration fields/options for--> R1
  H2 --automates onboarding/invite flows that pass into--> O3

  %% Verification Helpers <-> Registration Controllers
  V1 --render/serialize identity verification for onboarding UI--> R1
  V1 --helper logic called by--> R4

  %% QA Flows <-> Invitations
  Q1 --QA flow for import reassignment feeds into--> O2

  %% Broadcast/Dismissals <-> User Management & Onboarding
  BN1 --updates notification dismissals associated to user onboarding status--> U2

  %% LLM Tools <-> User Management
  T1 --reads user/group context for LLM enrichment from--> U2

  %% Email/Identity Provider Helpers <-> Registration
  EV9 --identity provider info for admins feeds into onboarding flows--> R1

  %% Email Notification <-> Registration
  EV6 --new user notification for onboarding triggers on registration logic in--> R1

  %% Early Access Programs
  EV7 --sets onboarding feature flips for users updating user details via--> U2
  EV7 --called from onboarding/registration flows--> R1

  %% EE Email Audit <-> Registration
  EV8 --audit-logs for email creation as part of registration process--> R1

  %% Spec Coverage and QA
  O3 --ensures full behavior spec of onboarding/invitations-> O2

  %% Error Handling Subgraph
  subgraph Error_Handling[Error Handling]
    direction TB
    style Error_Handling fill:#F8F8F8,stroke:#FFE4E1,stroke-width:2,rounded

    ER1[ee/lib/users/identity_verification/authorize_ci.rb\nCI Identity Verification Error]
    style ER1 fill:#FFE4E1,stroke:#FFE4E1,stroke-width:2,rounded

    ER2[app/validators/anti_abuse/unique_detumbled_email_validator.rb\nAntiAbuse Email Error]
    style ER2 fill:#FFE4E1,stroke:#FFE4E1,stroke-width:2,rounded

    ER1 --used by--> V2
    ER2 --email uniqueness error for registration/invite flows\nlinks to--> O1
  end

  %% Top Level Core Relationships
  U2 ---> R1
  R1 ---> Email_Verification_Services
  O1 ---> R1
  R1 --- VerificationHelpers
  RegHelpers ---> R1
  Email_Verification_Services ---> Error_Handling
  O2 ---> O1
  BN1 ---> U2
  QA_Flows ---> O2
  R4 ---> EV1
  V1 ---> R4
  V1 ---> R1
```