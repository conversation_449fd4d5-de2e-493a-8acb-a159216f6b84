```mermaid
flowchart TD
%% Styling

%% Colors
%% Core domain files: pastel blue (#D4F1F9)
%% Supporting/utility files: pastel yellow (#FFF8DC)
%% Data structure files: pastel green (#E0F8E0)
%% Error handling files: pastel red (#FFE4E1)
%% Initialization/setup files: pastel purple (#E6E6FA)
%% Groupings: very light gray (#F8F8F8) with pastel borders

%% Node styles
classDef core fill:#D4F1F9,stroke:#87CEEB,stroke-width:2px,rx:8,ry:8,color:#222
classDef support fill:#FFF8DC,stroke:#F5DEB3,stroke-width:2px,rx:8,ry:8,color:#222
classDef data fill:#E0F8E0,stroke:#9ACD32,stroke-width:2px,rx:8,ry:8,color:#222
classDef error fill:#FFE4E1,stroke:#FFB6B6,stroke-width:2px,rx:8,ry:8,color:#222
classDef init fill:#E6E6FA,stroke:#B9AEDC,stroke-width:2px,rx:8,ry:8,color:#222
classDef group fill:#F8F8F8,stroke:#B9D6E6,stroke-width:2.5px,rx:15,ry:15,color:#222

%% Domain Overall Group
subgraph G[Compliance Management / License Scanning & Policies]
class G group

%% License Scanning Core
  subgraph LicenseScanning[License Scanning Core]
  class LicenseScanning group

    LICENSE_SCANNING[Gitlab::LicenseScanning Core Dispatch]:::core
    SBOM_SCANNER[Gitlab::LicenseScanning::SbomScanner]:::core
    BASE_SCANNER[Gitlab::LicenseScanning::BaseScanner]:::core
    PIPELINE_COMPONENTS[Gitlab::LicenseScanning::PipelineComponents]:::core
    BRANCH_COMPONENTS[Gitlab::LicenseScanning::BranchComponents]:::core

    LICENSE_SCANNING --> SBOM_SCANNER
    LICENSE_SCANNING --> BASE_SCANNER
    SBOM_SCANNER --> BASE_SCANNER
    SBOM_SCANNER --> PIPELINE_COMPONENTS
    BRANCH_COMPONENTS --> PIPELINE_COMPONENTS
    LICENSE_SCANNING --> BRANCH_COMPONENTS
  end

%% Data Structures & Reporting
  subgraph DataStructures[Domain Data Structures & Reports]
  class DataStructures group

    LS_REPORT[Gitlab::Ci::Reports::LicenseScanning::Report]:::data
    LS_REPORTS_COMPARER[Gitlab::Ci::Reports::LicenseScanning::ReportsComparer]:::data
    PIPELINE_COMPONENTS -. ProvidesDependencies .-> LS_REPORT
    LS_REPORT --> LS_REPORTS_COMPARER

    LICENSE_POLICY[ee/app/models/sca/license_policy.rb]:::data
    LICENSE_TEMPLATE[app/models/license_template.rb]:::data

    LS_REPORTS_COMPARER --> LS_REPORT
    LICENSE_POLICY -. PolicyClassification .-> LS_REPORT
    LICENSE_TEMPLATE -. UsedForLicenseContent .-> LS_REPORT

    LICENSE_SCANNING -.uses.-> LS_REPORT
    SBOM_SCANNER -.populates.-> LS_REPORT
  end

%% License Scan Results Interpretation
  subgraph ScanInterpretation[Scan Results, Comparison & Serialization]
  class ScanInterpretation group

    LC_COMPARER_ENTITY[license_compliance/collapsed_comparer_entity.rb]:::core
    LC_COMPARER_SERIALIZER[license_compliance/comparer_serializer.rb]:::support
    LC_COLLAPSED_COMPARER_SERIALIZER[license_compliance/collapsed_comparer_serializer.rb]:::support
    POLICY_ENTITY[security/license_policy_entity.rb]:::support
    LC_COMPLIANCE_FINDER[security/license_compliance_jobs_finder.rb]:::core
    LICENSE_VIOLATION_CHECKER[security/scan_result_policies/license_violation_checker.rb]:::core

    LS_REPORTS_COMPARER --> LC_COMPARER_ENTITY
    LC_COMPARER_ENTITY --> LC_COMPARER_SERIALIZER
    LC_COMPARER_ENTITY --> LC_COLLAPSED_COMPARER_SERIALIZER
    LICENSE_POLICY --> POLICY_ENTITY
    LC_COMPLIANCE_FINDER -.finds-> LS_REPORT

    LICENSE_VIOLATION_CHECKER --> LS_REPORT
    LICENSE_VIOLATION_CHECKER --> LICENSE_POLICY
  end

%% Policy Management & Enforcement
  subgraph PolicyManagement[Policy Management & Enforcement]
  class PolicyManagement group

    APPROVAL_RULE_BUILDER[security/scan_result_policies/approval_rule_params_builder.rb]:::core
    UPDATE_LICENSE_APPROVALS[security/scan_result_policies/update_license_approvals_service.rb]:::core

    LICENSE_VIOLATION_CHECKER --> APPROVAL_RULE_BUILDER
    UPDATE_LICENSE_APPROVALS --> APPROVAL_RULE_BUILDER
    UPDATE_LICENSE_APPROVALS --> LICENSE_VIOLATION_CHECKER
    UPDATE_LICENSE_APPROVALS --> LS_REPORT
    UPDATE_LICENSE_APPROVALS --> LICENSE_POLICY
  end

%% Compliance Events & Violations
  subgraph ComplianceViolations[Compliance Events & Violations]
  class ComplianceViolations group

    VIOLATION_AUTHOR[gitlab/compliance_management/violations/approved_by_merge_request_author.rb]:::core
    VIOLATION_INSUFFICIENT[gitlab/compliance_management/violations/approved_by_insufficient_users.rb]:::core
    COMPLIANCE_VIOLATION_ENUM[ee/app/models/concerns/enums/merge_requests/compliance_violation.rb]:::data

    COMPLIANCE_VIOLATION_ENUM --> VIOLATION_AUTHOR
    COMPLIANCE_VIOLATION_ENUM --> VIOLATION_INSUFFICIENT
    VIOLATION_AUTHOR --> UPDATE_LICENSE_APPROVALS
    VIOLATION_INSUFFICIENT --> UPDATE_LICENSE_APPROVALS
  end

%% Policy Application (Workers)
  subgraph PolicyWorkers[Policy & Compliance Background Workers]
  class PolicyWorkers group

    PREVENT_AUTH_WORKER[compliance_management/standards/gitlab/prevent_approval_by_author_group_worker.rb]:::support
    PREVENT_COMMITTER_WORKER[compliance_management/standards/gitlab/prevent_approval_by_committer_group_worker.rb]:::support
    BASE_WORKER[compliance_management/standards/base_worker.rb]:::support
    SAST_GROUP_WORKER[compliance_management/standards/gitlab/sast_group_worker.rb]:::support
    DAST_GROUP_WORKER[compliance_management/standards/gitlab/dast_group_worker.rb]:::support
    ONE_NON_AUTHOR_WORKER[compliance_management/standards/soc2/at_least_one_non_author_approval_group_worker.rb]:::support
    REFRESH_WORKER[compliance_management/standards/refresh_worker.rb]:::support

    PREVENT_AUTH_WORKER --> BASE_WORKER
    PREVENT_COMMITTER_WORKER --> BASE_WORKER
    DAST_GROUP_WORKER --> BASE_WORKER
    SAST_GROUP_WORKER --> BASE_WORKER
    ONE_NON_AUTHOR_WORKER --> BASE_WORKER
    REFRESH_WORKER --> BASE_WORKER

    UPDATE_LICENSE_APPROVALS --> REFRESH_WORKER
    VIOLATION_AUTHOR --> PREVENT_AUTH_WORKER
    VIOLATION_INSUFFICIENT --> ONE_NON_AUTHOR_WORKER
    POLICY_ENTITY -.serialized-by.-> LC_COMPARER_SERIALIZER
  end

%% Policy Definition & Access Control (GraphQL & Policy Files)
  subgraph GraphQLandPolicies[Access Control & Policy Representation]
  class GraphQLandPolicies group
  
    VULN_POLICY_RESOLVER[graphql/resolvers/compliance_management/security_policies/vulnerability_management_policy_resolver.rb]:::support
    SCAN_POLICY_RESOLVER[graphql/resolvers/compliance_management/security_policies/scan_result_policy_resolver.rb]:::support
    PIPE_POLICY_RESOLVER[graphql/resolvers/compliance_management/security_policies/pipeline_execution_policy_resolver.rb]:::support
    STATE_POLICY[policies/terraform/state_policy.rb]:::support
    DEP_LIST_POLICY[policies/dependencies/dependency_list_export_policy.rb]:::support
    PKG_PROJECT_POLICY[policies/ee/packages/policies/project_policy.rb]:::support
    PKG_GROUP_POLICY[policies/ee/packages/policies/group_policy.rb]:::support

    VULN_POLICY_RESOLVER --> UPDATE_LICENSE_APPROVALS
    SCAN_POLICY_RESOLVER --> UPDATE_LICENSE_APPROVALS
    PIPE_POLICY_RESOLVER --> UPDATE_LICENSE_APPROVALS
    DEP_LIST_POLICY -.exports.-> LS_REPORT
    PKG_PROJECT_POLICY -.applies-to.-> LICENSE_POLICY
    PKG_GROUP_POLICY -.applies-to.-> LICENSE_POLICY
    STATE_POLICY -.related-state.-> LICENSE_POLICY
  end

%% Utility & Supporting Services
  subgraph SupportServices[Supporting & Integration Services]
  class SupportServices group

    LICENSE_MAILER[mailers/license_mailer.rb]:::support
    LICENSE_HELPER[helpers/license_helper.rb]:::support
    FEATURE_FLAGS_HELPER[helpers/ee/feature_flags_helper.rb]:::support
    BILLING_PLANS_HELPER[helpers/billing_plans_helper.rb]:::support
    SUBSCRIBABLE_BANNER_HELPER[helpers/ee/subscribable_banner_helper.rb]:::support

    GITLAB_SUBS_FETCH[services/********************/fetch_purchase_eligible_namespaces_service.rb]:::support
    MEMBER_MGMT_QUEUE[services/********************/member_management/queue_non_billable_to_billable_service.rb]:::support
    LICENSE_ADD_ON[services/********************/add_on_purchases/self_managed/license_add_ons/duo_amazon_q.rb]:::support

    ADMIN_LICENSE_REQ[controllers/concerns/admin/license_request.rb]:::support
    CC_STATUS_PROBE[services/cloud_connector/status_checks/probes/license_probe.rb]:::support

    LICENSE_HELPER --> LICENSE_POLICY
    LICENSE_HELPER --> LICENSE_MAILER
    BILLING_PLANS_HELPER --> LICENSE_HELPER
    SUBSCRIBABLE_BANNER_HELPER --> LICENSE_HELPER
    GITLAB_SUBS_FETCH --> BILLING_PLANS_HELPER
    MEMBER_MGMT_QUEUE --> GITLAB_SUBS_FETCH
    LICENSE_ADD_ON --> GITLAB_SUBS_FETCH
    ADMIN_LICENSE_REQ --> LICENSE_HELPER
    CC_STATUS_PROBE --> LICENSE_HELPER
    CC_STATUS_PROBE --> LICENSE_POLICY
  end

%% Custom License Policies
  subgraph CustomLicensePolicies[Custom Software License Management]
  class CustomLicensePolicies group

    FIND_OR_CREATE_CL[services/security/custom_software_licenses/find_or_create_service.rb]:::support

    LICENSE_POLICY -.manages.-> FIND_OR_CREATE_CL
    LICENSE_SCANNING -.uses.-> FIND_OR_CREATE_CL
  end

%% Compliance Framework & Comparison Operators
  subgraph ComplianceFramework[Compliance Framework & Comparison]
  class ComplianceFramework group

    TRIGGER_EXT_CTRL[services/compliance_management/compliance_framework/compliance_requirements/trigger_external_control_service.rb]:::support
    COMP_REQ_COMPARISON[lib/compliance_management/compliance_requirements/comparison_operator.rb]:::support
    MR_APPROVAL_SETTING[lib/compliance_management/merge_request_approval_settings/setting.rb]:::support

    TRIGGER_EXT_CTRL --> LICENSE_POLICY
    COMP_REQ_COMPARISON --> LICENSE_POLICY
    MR_APPROVAL_SETTING --> LICENSE_POLICY
  end

%% Parsers: License Scanning & Compliance
  subgraph Parsers[License Scanning & Compliance Parsers]
  class Parsers group

    PARSER_LICENSE_SCANNING[ci/parsers/license_compliance/license_scanning.rb]:::support

    PARSER_LICENSE_SCANNING --> LS_REPORT
    LICENSE_SCANNING --> PARSER_LICENSE_SCANNING
  end

%% Testing & Test Support
  subgraph TestSupport[Test Support & Specs]
  class TestSupport group

    LS_REPORT_SPEC[spec/lib/gitlab/ci/reports/license_scanning/report_spec.rb]:::support
    LS_REPORTS_COMPARER_SPEC[spec/lib/gitlab/ci/reports/license_scanning/reports_comparer_spec.rb]:::support
    LS_REPORT_HELPERS[spec/support/helpers/license_scanning_report_helpers.rb]:::support
    VULN_HELPERS[spec/support/helpers/vulnerable_helpers.rb]:::support
    QA_LS_SPEC[qa/specs/features/ee/browser_ui/13_secure/license_scanning_spec.rb]:::support

    LS_REPORT_SPEC --> LS_REPORT
    LS_REPORTS_COMPARER_SPEC --> LS_REPORTS_COMPARER
    LS_REPORTS_COMPARER_SPEC --> LS_REPORT
    LS_REPORT_HELPERS --> LS_REPORT_SPEC
    QA_LS_SPEC --> LS_REPORT
    VULN_HELPERS --> LS_REPORT_SPEC
    QA_LS_SPEC --> LS_REPORTS_COMPARER_SPEC
  end

%% Fundamental Shared Concerns & Models
  subgraph SharedConcerns[Models & Shared Concerns]
  class SharedConcerns group

    PROT_DEPLOY_KEY_ACCESS[models/concerns/protected_ref_deploy_key_access.rb]:::support
    STATE_VERSION[models/terraform/state_version.rb]:::support
    EE_NAMESPACE_SETTINGS[models/ee/ci/namespace_settings.rb]:::support
    PROJECT_SEC_EXCLUSION[models/security/project_security_exclusion.rb]:::support

    PROT_DEPLOY_KEY_ACCESS -.access-control.-> LICENSE_POLICY
    STATE_VERSION -.tf-state.-> LICENSE_POLICY
    EE_NAMESPACE_SETTINGS --> LICENSE_POLICY
    PROJECT_SEC_EXCLUSION -.exclusion.-> LICENSE_POLICY
  end
end

%% Inter-Group Connections (Key Flows)
LS_REPORTS_COMPARER -->|diffs/compares| LC_COMPARER_ENTITY
LC_COMPARER_ENTITY -->|presents comparison| LC_COMPARER_SERIALIZER
LS_REPORT -->|data| LICENSE_VIOLATION_CHECKER
LICENSE_VIOLATION_CHECKER -->|validates policies| UPDATE_LICENSE_APPROVALS
UPDATE_LICENSE_APPROVALS -->|enforces| PolicyWorkers
LICENSE_POLICY -->|policy info| POLICY_ENTITY
PARSER_LICENSE_SCANNING -->|populates| LS_REPORT
SCAN_POLICY_RESOLVER -->|GraphQL policies| UPDATE_LICENSE_APPROVALS
COMPLIANCE_VIOLATION_ENUM -->|reasons/severity| VIOLATION_AUTHOR
COMPLIANCE_VIOLATION_ENUM -->|reasons/severity| VIOLATION_INSUFFICIENT
LICENSE_TEMPLATE -->|content/templates| LS_REPORT
FIND_OR_CREATE_CL -->|creates/finds| LICENSE_POLICY

%% Other Key Relationships
LICENSE_HELPER -->|supports| LICENSE_MAILER
LICENSE_HELPER -->|supports| LICENSE_POLICY
SUBSCRIBABLE_BANNER_HELPER -->|visual support| LICENSE_POLICY
GITLAB_SUBS_FETCH -->|namespace eligibility| LICENSE_POLICY

%% Node Classes
class LICENSE_SCANNING,SBOM_SCANNER,BASE_SCANNER,LICENSE_VIOLATION_CHECKER,UPDATE_LICENSE_APPROVALS,LC_COMPLIANCE_FINDER,APPROVAL_RULE_BUILDER,VIOLATION_AUTHOR,VIOLATION_INSUFFICIENT,LS_REPORT,LS_REPORTS_COMPARER,LICENSE_TEMPLATE,LICENSE_POLICY,COMPLIANCE_VIOLATION_ENUM core
class PARSER_LICENSE_SCANNING,LC_COMPARER_ENTITY,LC_COMPARER_SERIALIZER,LC_COLLAPSED_COMPARER_SERIALIZER,POLICY_ENTITY support
class LICENSE_MAILER,LICENSE_HELPER,FEATURE_FLAGS_HELPER,BILLING_PLANS_HELPER,SUBSCRIBABLE_BANNER_HELPER,ADMIN_LICENSE_REQ,GITLAB_SUBS_FETCH,MEMBER_MGMT_QUEUE,LICENSE_ADD_ON,CC_STATUS_PROBE SUPPORT
class FIND_OR_CREATE_CL,TRIGGER_EXT_CTRL,COMP_REQ_COMPARISON,MR_APPROVAL_SETTING support
class PREVENT_AUTH_WORKER,PREVENT_COMMITTER_WORKER,BASE_WORKER,SAST_GROUP_WORKER,DAST_GROUP_WORKER,ONE_NON_AUTHOR_WORKER,REFRESH_WORKER support
class VULN_POLICY_RESOLVER,SCAN_POLICY_RESOLVER,PIPE_POLICY_RESOLVER,STATE_POLICY,DEP_LIST_POLICY,PKG_PROJECT_POLICY,PKG_GROUP_POLICY support
class LS_REPORT_SPEC,LS_REPORTS_COMPARER_SPEC,LS_REPORT_HELPERS,VULN_HELPERS,QA_LS_SPEC support
class PROT_DEPLOY_KEY_ACCESS,STATE_VERSION,EE_NAMESPACE_SETTINGS,PROJECT_SEC_EXCLUSION support
class LS_REPORT,LICENSE_POLICY,LICENSE_TEMPLATE,COMPLIANCE_VIOLATION_ENUM data
```