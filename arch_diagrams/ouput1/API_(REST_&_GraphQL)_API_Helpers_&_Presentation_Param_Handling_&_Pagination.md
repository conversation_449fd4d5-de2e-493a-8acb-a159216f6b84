```mermaid
flowchart TD
  %% Styling
  classDef core fill:#D4F1F9,stroke:#8ecae6,stroke-width:2px,rx:12,ry:12
  classDef utility fill:#FFF8DC,stroke:#ffe082,stroke-width:2px,rx:12,ry:12
  classDef data fill:#E0F8E0,stroke:#a1dab4,stroke-width:2px,rx:12,ry:12
  classDef error fill:#FFE4E1,stroke:#ffb4b4,stroke-width:2px,rx:12,ry:12
  classDef init fill:#E6E6FA,stroke:#b5aedc,stroke-width:2px,rx:12,ry:12
  classDef group fill:#F8F8F8,stroke:#a7c7e7,stroke-width:2px,rx:18,ry:18
  classDef purplestroke stroke:#b5aedc
  classDef yellowstroke stroke:#ffe082
  classDef greenstroke stroke:#a1dab4
  classDef redstroke stroke:#ffb4b4
  classDef bluestroke stroke:#8ecae6

  %% Root grouping for domain - vertical layout
  subgraph Domain_API_Helpers_and_Presentation["API REST & GraphQL / API Helpers & Presentation / Param Handling & Pagination"]
    direction TB

    %% 1. API Entry Points and Main Helpers
    subgraph MAIN_API_HELPERS["API Core Helpers"]
      class MAIN_API_HELPERS group,bluestroke
      api_helpers[API::Helpers]:::core
      api_pagination_params[API::PaginationParams]:::core
      api_helpers_pagination[API::Helpers::Pagination]:::core
      api_helpers_merge_requests[API::Helpers::MergeRequestsHelpers]:::core
      api_helpers_common[API::Helpers::CommonHelpers]:::utility
      api_helpers_headers[API::Helpers::HeadersHelpers]:::utility
      api_helpers_rate_limiter[API::Helpers::RateLimiter]:::utility
      api_helpers_merge_requests2[API::Helpers::MergeRequestsHelpers feature]:::core
      api_helpers_packages_basic_auth[API::Helpers::Packages::BasicAuthHelpers]:::utility
      api_helpers_packages_dep_proxy[API::Helpers::Packages::DependencyProxyHelpers]:::utility
    end

    %% 2. Pagination Core Engine and Patterns
    subgraph PAGINATION_CORE["Pagination Engine & Patterns"]
      class PAGINATION_CORE group,bluestroke
      gitlab_pagination_base[Gitlab::Pagination::Base]:::core
      gitlab_pagination_delegate[Gitlab::PaginationDelegate]:::core
      gitlab_multi_collection_paginator[Gitlab::MultiCollectionPaginator]:::core
      gitlab_offset_pagination[Gitlab::Pagination::OffsetPagination]:::core
      gitlab_offset_header_builder[Gitlab::Pagination::OffsetHeaderBuilder]:::utility
      gitlab_pagination_cursor_keyset[Gitlab::Pagination::CursorBasedKeyset]:::core
      gitlab_keyset[Gitlab::Pagination::Keyset]:::core
      gitlab_keyset_pager[Gitlab::Pagination::Keyset::Pager]:::core
      gitlab_keyset_page[Gitlab::Pagination::Keyset::Page]:::data
      gitlab_keyset_paginator[Gitlab::Pagination::Keyset::Paginator]:::core
      gitlab_keyset_cursor_pager[Gitlab::Pagination::Keyset::CursorPager]:::core
      gitlab_keyset_cursor_req_ctx[Gitlab::Pagination::Keyset::CursorBasedRequestContext]:::utility
      gitlab_keyset_header_builder[Gitlab::Pagination::Keyset::HeaderBuilder]:::utility
    end

    %% 3. Pagination Optimizations
    subgraph PAGINATION_OPTIM["Pagination Optimizations"]
      class PAGINATION_OPTIM group,bluestroke
      gitlab_keyset_iterator[Gitlab::Pagination::Keyset::Iterator]:::utility
      gitlab_keyset_order_obj_data[Gitlab::Pagination::Keyset::InOperatorOptimization::OrderByColumnData]:::data
      gitlab_keyset_coldata[Gitlab::Pagination::Keyset::InOperatorOptimization::ColumnData]:::data
      gitlab_keyset_query_builder[Gitlab::Pagination::Keyset::InOperatorOptimization::QueryBuilder]:::utility
      gitlab_keyset_record_loader[Gitlab::Pagination::Keyset::InOperatorOptimization::Strategies::RecordLoaderStrategy]:::utility
      gitlab_keyset_sql_type_missing[Gitlab::Pagination::Keyset::SqlTypeMissingError]:::error
      gitlab_keyset_unsupported_scope_order[Gitlab::Pagination::Keyset::UnsupportedScopeOrder]:::error
      gitlab_offset_index_scan[Gitlab::Pagination::Offset::PaginationWithIndexOnlyScan]:::utility
    end

    %% 4. Parameter Handling & Transformation
    subgraph PARAMS_HANDLING["Parameter Handling & Coercion"]
      class PARAMS_HANDLING group,greenstroke
      api_validations_comma_array[API::Validations::Types::CommaSeparatedToArray]:::data
      api_validations_hash_int[API::Validations::Types::HashOfIntegerValues]:::data
      api_validations_workhorse_file[API::Validations::Types::WorkhorseFile]:::data
    end

    %% 5. API Utilities, Data Transformation/Presentation
    subgraph API_UTILITIES["API Utilities & Data Structures"]
      class API_UTILITIES group,yellowstroke
      union_finder[UnionFinder]:::utility
      event_filter[EventFilter]:::utility
      etag_caching_middleware[Gitlab::EtagCaching::Middleware]:::utility
      gitlab_utils_link_header[Gitlab::Utils::LinkHeaderParser]:::utility
      gitlab_utils_lazy_attributes[Gitlab::Utils::LazyAttributes]:::utility
      gitlab_utils_gsub[Gitlab::Utils::Gsub]:::utility
      mash_permitted_patch[config/initializers/hashie_mash_permitted_patch]:::init
      action_view_patch[config/initializers/action_view_patch]:::init
    end

    %% 6. GraphQL & Externally Paginated Data
    subgraph GRAPHQL["GraphQL Support & Pagination"]
      class GRAPHQL group,bluestroke
      graphql_external_array[Gitlab::Graphql::ExternallyPaginatedArray]:::data
      bulk_imports_graphql[BulkImports::Clients::Graphql]:::utility
    end

    %% 7. API Authentication
    subgraph API_AUTH["API Authentication"]
      class API_AUTH group,bluestroke
      gitlab_api_auth_builder[Gitlab::APIAuthentication::Builder]:::utility
      gitlab_api_auth_token_type[Gitlab::APIAuthentication::TokenTypeBuilder]:::utility
      gitlab_api_auth_sent_through[Gitlab::APIAuthentication::SentThroughBuilder]:::utility
    end

    %% 8. Parameter Handling for Controllers Rails Integration
    subgraph CONTROLLER_PARAM_HELPERS["Controllers: Param & Pagination Helpers"]
      class CONTROLLER_PARAM_HELPERS group,purplestroke
      strong_pagination_params[StrongPaginationParams]:::utility
      params_backward_compatibility[ParamsBackwardCompatibility]:::utility
      find_snippet[FindSnippet]:::utility
      issuable_collections[IssuableCollections]:::utility
      page_limiter[PageLimiter]:::utility
    end

    %% 9. API Extensions - EE features/helpers
    subgraph EE_API_HELPERS["API EE Enterprise Extensions"]
      class EE_API_HELPERS group,bluestroke
      ee_api_helpers[EE::API::Helpers]:::core
      ee_api_helpers_groups[EE::API::Helpers::GroupsHelpers]:::utility
      ee_api_helpers_merge_requests[EE::API::Helpers::MergeRequestsHelpers]:::utility
      ee_api_helpers_protected_branches[EE::API::Helpers::ProtectedBranchesHelpers]:::utility
      ee_api_helpers_protected_tags[EE::API::Helpers::ProtectedTagsHelpers]:::utility
      ee_api_helpers_search[EE::API::Helpers::SearchHelpers]:::utility
      ee_json_schema_validator[EE::JsonSchemaValidator]:::utility
      ee_api_helpers_scim_pagination[EE::API::Helpers::ScimPagination]:::utility
      ee_grape_request_proxy[API::GrapeRequestProxy]:::utility
    end

    %% 10. Service Layer Helpers (for API params, refs, etc)
    subgraph SERVICE_LAYER["Service Layer Concerns/Helpers"]
      class SERVICE_LAYER group,bluestroke
      base_service[BaseService]:::utility
      projects_remove_refs[Projects::RemoveRefs]:::utility
      ci_downstream_pipeline_helpers[Ci::DownstreamPipelineHelpers]:::utility
    end

    %% Interconnections

    %% MAIN_API_HELPERS
    api_helpers --> api_helpers_pagination
    api_helpers --> api_helpers_common
    api_helpers --> api_helpers_merge_requests
    api_helpers --> api_helpers_headers
    api_helpers --> api_pagination_params
    api_helpers --> api_helpers_rate_limiter
    api_helpers -->|includes authentication for packages| api_helpers_packages_basic_auth
    api_helpers -->|includes dependency registry support| api_helpers_packages_dep_proxy
    api_helpers --> gitlab_pagination_base
    api_helpers --> gitlab_offset_pagination
    api_helpers --> gitlab_keyset
    api_helpers --> api_helpers_merge_requests2

    api_helpers_pagination -->|delegates| gitlab_offset_pagination
    api_pagination_params --> api_helpers_pagination
    api_pagination_params --> gitlab_pagination_delegate

    api_helpers_merge_requests -.-> api_helpers_common
    api_helpers_merge_requests -.-> gitlab_keyset

    %% PAGINATION_CORE
    gitlab_keyset --> gitlab_keyset_pager
    gitlab_keyset --> gitlab_keyset_paginator
    gitlab_keyset_pager --> gitlab_keyset_page
    gitlab_keyset_paginator --> gitlab_keyset_page
    gitlab_keyset_paginator --> gitlab_keyset_cursor_req_ctx
    gitlab_keyset_cursor_pager --> gitlab_keyset_cursor_req_ctx
    gitlab_keyset_cursor_req_ctx --> gitlab_keyset_header_builder

    gitlab_pagination_cursor_keyset --> gitlab_keyset
    gitlab_pagination_cursor_keyset --> gitlab_offset_pagination

    gitlab_pagination_base --> gitlab_offset_pagination
    gitlab_offset_pagination --> gitlab_offset_header_builder
    gitlab_offset_pagination -->|for optimized queries| gitlab_offset_index_scan
    gitlab_offset_pagination --> gitlab_pagination_cursor_keyset

    gitlab_pagination_base --> gitlab_keyset_pager
    gitlab_offset_pagination --> gitlab_pagination_delegate
    gitlab_offset_pagination --> gitlab_multi_collection_paginator

    %% PAGINATION_OPTIM
    gitlab_keyset --> gitlab_keyset_iterator
    gitlab_keyset_iterator --> gitlab_keyset_order_obj_data
    gitlab_keyset_order_obj_data --> gitlab_keyset_coldata
    gitlab_keyset_query_builder --> gitlab_keyset_order_obj_data
    gitlab_keyset_query_builder --> gitlab_keyset_coldata
    gitlab_keyset_query_builder --> gitlab_keyset_record_loader
    gitlab_keyset_record_loader --> gitlab_keyset_sql_type_missing
    gitlab_keyset_iterator --> gitlab_keyset_unsupported_scope_order

    gitlab_pagination_base --> gitlab_offset_header_builder

    %% PARAMS_HANDLING
    api_validations_comma_array -->|used for custom param coercion| api_helpers_common
    api_validations_hash_int -->|used in param normalization| api_helpers_common
    api_validations_workhorse_file -->|API upload param validation| api_helpers_common
    api_helpers_common --> api_helpers

    %% API_UTILITIES
    union_finder -->|used for union queries| gitlab_multi_collection_paginator
    event_filter -->|supports param set filtering| gitlab_keyset_query_builder
    etag_caching_middleware -->|middleware for cache headers| api_helpers_headers
    gitlab_utils_link_header -->|parses pagination http links| api_helpers_headers
    mash_permitted_patch -->|initializes allowed param hash methods| api_helpers
    action_view_patch -->|preload header helper| api_helpers_headers
    gitlab_utils_lazy_attributes -->|used for deferred param hydration| api_helpers_common
    gitlab_utils_gsub -->|utility for string param processing| api_helpers_common

    %% GRAPHQL
    graphql_external_array --> gitlab_keyset_paginator
    graphql_external_array --> gitlab_keyset_page
    bulk_imports_graphql -.->|fetches paginated data| graphql_external_array

    %% API_AUTH
    gitlab_api_auth_builder --> gitlab_api_auth_token_type
    gitlab_api_auth_token_type --> gitlab_api_auth_sent_through
    gitlab_api_auth_sent_through --> api_helpers

    %% CONTROLLER_PARAM_HELPERS
    strong_pagination_params --> api_helpers
    params_backward_compatibility --> api_helpers
    find_snippet --> api_helpers
    issuable_collections --> api_helpers
    page_limiter --> api_helpers
    strong_pagination_params -->|provides param pattern| api_pagination_params

    %% EE_API_HELPERS
    ee_api_helpers --> api_helpers
    ee_api_helpers --> ee_api_helpers_groups
    ee_api_helpers --> ee_api_helpers_merge_requests
    ee_api_helpers --> ee_api_helpers_protected_branches
    ee_api_helpers --> ee_api_helpers_protected_tags
    ee_api_helpers --> ee_api_helpers_scim_pagination
    ee_api_helpers_groups -->|group param extensions| api_helpers_common
    ee_api_helpers_merge_requests --> api_helpers_merge_requests
    ee_api_helpers_protected_branches --> api_helpers_common
    ee_api_helpers_protected_tags --> api_helpers_common
    ee_api_helpers_scim_pagination --> gitlab_keyset_paginator
    ee_api_helpers_scim_pagination --> gitlab_keyset_header_builder
    ee_grape_request_proxy -.->|wraps request for API context| api_helpers
    ee_json_schema_validator --> api_pagination_params
    ee_api_helpers_search --> api_helpers_common

    %% SERVICE_LAYER
    base_service --> api_helpers
    projects_remove_refs --> base_service
    ci_downstream_pipeline_helpers --> base_service

    %% Domain Data Structures & Transformations
    gitlab_keyset_page -->|models single page of data| gitlab_offset_pagination
    gitlab_multi_collection_paginator -->|multi-resource pagination| gitlab_keyset
    gitlab_keyset_cursor_req_ctx --> gitlab_keyset_paginator
    gitlab_keyset_header_builder --> api_helpers_headers
    gitlab_keyset_header_builder -->|constructs next/prev URIs| gitlab_utils_link_header

    gitlab_keyset_coldata --> gitlab_keyset_query_builder
    gitlab_keyset_order_obj_data --> gitlab_keyset_query_builder
    gitlab_keyset_order_obj_data --> gitlab_keyset_paginator

    gitlab_keyset_sql_type_missing --> gitlab_keyset_paginator
    gitlab_keyset_unsupported_scope_order --> gitlab_keyset_iterator

    gitlab_offset_index_scan --> gitlab_offset_pagination

    %% Presentational Context
    api_helpers_headers --> etag_caching_middleware
    api_helpers_headers --> gitlab_utils_link_header

    %% Parameter Interface / Backwards Compatibility
    params_backward_compatibility --> api_helpers
    params_backward_compatibility --> strong_pagination_params

  end
```