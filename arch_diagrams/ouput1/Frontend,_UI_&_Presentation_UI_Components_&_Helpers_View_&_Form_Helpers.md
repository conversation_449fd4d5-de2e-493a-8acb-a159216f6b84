```mermaid
flowchart TD
  %% Domain: Frontend, UI & Presentation / UI Components & Helpers / View & Form Helpers
  %% Layout and Style Definitions
  %% Color Codes per system prompt:
  %% Core domain files: pastel blue #D4F1F9
  %% Supporting/utility files: pastel yellow #FFF8DC
  %% Data structure files: pastel green #E0F8E0
  %% Error handling files: pastel red #FFE4E1
  %% Initialization/setup files: pastel purple #E6E6FA
  %% Logical groupings/subgraphs: very light gray #F8F8F8, pastel border for visual structure
  
  %% MAIN SUBGRAPH
  subgraph UI_Domain["Frontend, UI & Presentation / UI Components & Helpers / View & Form Helpers"]
    direction TB
    %% 1. UI COMPONENTS Core domain feature
    subgraph UI_Components["UI Components" ]
      direction TB
      style UI_Components fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded
      SCC1[Layouts::SettingsSectionComponent\nPure structural component\nfile: layouts/settings_section_component.rb]
      style SCC1 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2px,rounded
      SBC1[Layouts::SettingsBlockComponent\nCollapsible settings block\nfile: layouts/settings_block_component.rb]
      style SBC1 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2px,rounded
      CRUDC1[Layouts::CrudComponent\nCRUD section UI builder\nfile: layouts/crud_component.rb]
      style CRUDC1 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2px,rounded
      PHC1[Layouts::PageHeadingComponent\nPage heading presentation\nfile: layouts/page_heading_component.rb]
      style PHC1 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2px,rounded
      OBC1[Onboarding::Component\nUser onboarding presenter\nfile: onboarding/component.rb]
      style OBC1 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2px,rounded
    end

    %% 2. MODEL PRESENTATION AND VIEW HELPERS (Core logic)
    subgraph Model_Presentation["Model Presentation & Link Helpers"]
      direction TB
      style Model_Presentation fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded
      BADGE1[Badge\nDomain: Badge rendering, formatting, model-specific\nfile: badge.rb]
      style BADGE1 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2px,rounded
      BH1[BadgesHelper\nGenerates badge HTML/shapes, composes with Badge model\nfile: badges_helper.rb]
      style BH1 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2px,rounded
    end

    %% 3. FORM HELPERS (UI state/data entry)
    subgraph Form_Helpers["Form & ViewComponent Form Helpers"]
      direction TB
      style Form_Helpers fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded
      GFB[GitlabUiFormBuilder\nAbstraction for Pajamas-style form inputs\nfile: gitlab/form_builders/gitlab_ui_form_builder.rb]
      style GFB fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2px,rounded
      VHL[ViteHelper\nVite.js asset helpers for forms & UI loads\nfile: vite_helper.rb]
      style VHL fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      APH[ApplicationHelper\nShared view-level helpers vital for forms/views\nfile: application_helper.rb]
      style APH fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2px,rounded
    end

    %% 4. DOMAIN HELPERS (Shared UI / Reusable presentation)
    subgraph UI_Helpers["UI Helper Modules"]
      direction TB
      style UI_Helpers fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded
      
      %% Group: Visual & Interactive
      BH2[ButtonHelper\nReusable button UI, clipboard, dropdown link\nfile: button_helper.rb]
      style BH2 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2px,rounded
      IH[IconsHelper\nStandard icons generation & mapping\nfile: icons_helper.rb]
      style IH fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2px,rounded
      LH[ListboxHelper\nDropdown/listbox rendering utility\nfile: listbox_helper.rb]
      style LH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      TAH[TabHelper\nTab navigation rendering/logic\nfile: tab_helper.rb]
      style TAH fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2px,rounded
      DH[DropdownsHelper\nGeneral dropdown widget support\nfile: dropdowns_helper.rb]
      style DH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      
      %% Group: Data & Statistics
      PH[PaginationHelper\nPagination controls; supports collection APIs\nfile: pagination_helper.rb]
      style PH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      KSH[KeysetHelper\nKeyset pagination for high-performance UIs\nfile: keyset_helper.rb]
      style KSH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      NH[NumbersHelper\nCounter formatting, number helpers\nfile: numbers_helper.rb]
      style NH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      SH[StatAnchorsHelper\nAnchor link+stat UI attributes\nfile: stat_anchors_helper.rb]
      style SH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      STH[SortingTitlesValuesHelper\nSorting field labels and values\nfile: sorting_titles_values_helper.rb]
      style STH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      PLH[PlanLimitsHelper\nPlan usage/limits UI data\nfile: plan_limits_helper.rb]
      style PLH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded

      %% Group: UI Experience
      FAVH[FaviconHelper\nFavicon selection/presentation\nfile: favicon_helper.rb]
      style FAVH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      AHS[ActiveSessionsHelper\nDevice/session icons\nfile: active_sessions_helper.rb]
      style AHS fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      GH[GitpodHelper\nGitpod call-to-action\nfile: gitpod_helper.rb]
      style GH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      GTH[GitHelper\nGit display utilities: formatting, signatures\nfile: git_helper.rb]
      style GTH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      CH[CalendarHelper\nFeeds/calendar data injection\nfile: calendar_helper.rb]
      style CH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      OTTH[OneTrustHelper\nConsent management embed\nfile: one_trust_helper.rb]
      style OTTH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      BH3[BizibleHelper\nBizible analytics script/conditions\nfile: bizible_helper.rb]
      style BH3 fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      PFH[PerformanceBarHelper\nAdmin/dev bar widgets\nfile: performance_bar_helper.rb]
      style PFH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      RHH[ReadmeHelper\nREADME display utilities\nfile: readme_helper.rb]
      style RHH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      RH[RegistrationsHelper\nRegistration events/forms logic\nfile: registrations_helper.rb]
      style RH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      SFSH[SafeFormatHelper\nHTML-safe string formatting/tag helpers\nfile: safe_format_helper.rb]
      style SFSH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      ColorsH[ColorsHelper\nColor conversion/rendering for UI\nfile: colors_helper.rb]
      style ColorsH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      FeedTH[FeedTokenHelper\nFeed token UI/links\nfile: feed_token_helper.rb]
      style FeedTH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      SubBannerH[SubscribableBannerHelper\nBanner injection for subscriptions\nfile: subscribable_banner_helper.rb]
      style SubBannerH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      EnableSSH[EnableSearchSettingsHelper\nSearch UX helpers\nfile: enable_search_settings_helper.rb]
      style EnableSSH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      NotificationsH[NotificationsHelper\nNotification icons UI\nfile: notifications_helper.rb]
      style NotificationsH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      EmailsH[EmailsHelper\nEmails formatting & brand look\nfile: emails_helper.rb]
      style EmailsH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      AppearancesH[AppearancesHelper\nAppearance banners, branding, logo\nfile: appearances_helper.rb]
      style AppearancesH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      SafeParamsH[SafeParamsHelper\nParams sanitation for UIs\nfile: safe_params_helper.rb]
      style SafeParamsH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      GitlabSTH[*********************\nJS loading, CSP nonce helpers\nfile: gitlab_script_tag_helper.rb]
      style GitlabSTH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      UHL[UrlHelper\nURL generation & presentation\nfile: url_helper.rb]
      style UHL fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded

      InstConfigH[InstanceConfigurationHelper\nDisplay for admin instance settings\nfile: instance_configuration_helper.rb]
      style InstConfigH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      MirrH[MirrorHelper\nMirror repo link utilities\nfile: mirror_helper.rb]
      style MirrH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      SubmoduleH[SubmoduleHelper\nSubmodule ref display\nfile: submodule_helper.rb]
      style SubmoduleH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      TabH[TabHelper\nTabbed navigation reuse\nfile: tab_helper.rb]
      style TabH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      TagsH[TagsHelper\nTag link helpers\nfile: tags_helper.rb]
      style TagsH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      TimeH[TimeHelper\nRelative time/duration for UI\nfile: time_helper.rb]
      style TimeH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      WorkItemsH[WorkItemsHelper\nWork items presentation\nfile: work_items_helper.rb]
      style WorkItemsH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      VisLvlH[VisibilityLevelHelper\nVisibility display/colors for UI\nfile: visibility_level_helper.rb]
      style VisLvlH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      ProgrammingLH[ProgrammingLanguagesHelper\nLanguage dropdown search\nfile: programming_languages_helper.rb]
      style ProgrammingLH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      ContainerRegistryH[ContainerRegistryHelper\nContainer registry UI attributes\nfile: container_registry/container_registry_helper.rb]
      style ContainerRegistryH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      IntegrationsH[IntegrationsHelper\nThird-party integration form+display\nfile: integrations_helper.rb]
      style IntegrationsH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      ProtectedBranchesH[ProtectedBranchesHelper\nPermission logic for protected branches UI\nfile: protected_branches_helper.rb]
      style ProtectedBranchesH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      EnvironmentsH[EnvironmentsHelper\nEnvironments list/folder display\nfile: environments_helper.rb]
      style EnvironmentsH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      ProjAlertMgmtH[ProjectsAlertManagementHelper\nAlert management UI data\nfile: projects/alert_management_helper.rb]
      style ProjAlertMgmtH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      CompH[ComponentsHelper\nWorkhorse version UI display\nfile: components_helper.rb]
      style CompH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      AdminCompH[Admin::ComponentsHelper\nAdmin-only components UI for DB info\nfile: admin/components_helper.rb]
      style AdminCompH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      AutoDevopsH[AutoDevopsHelper\nAutoDevOps onboarding UI logic\nfile: auto_devops_helper.rb]
      style AutoDevopsH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      EmailsH[EmailsHelper\nBranding and structure for emails\nfile: emails_helper.rb]
      style EmailsH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      RecaptchaH[RecaptchaHelper\nRecaptcha JS and presentation\nfile: recaptcha_helper.rb]
      style RecaptchaH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded

      %% CI Helpers (subgroup)
      subgraph CiHelpers["CI Pipeline UI Helpers"]
        direction TB
        style CiHelpers fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2px,rounded
        CipipH[Ci::PipelinesHelper\nPipelines list display/data\nfile: ci/pipelines_helper.rb]
        style CipipH fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2px,rounded
        CiStatH[Ci::StatusHelper\nStatus icons, CI badge coloring\nfile: ci/status_helper.rb]
        style CiStatH fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2px,rounded
        CiVarsH[Ci::VariablesHelper\nUI helpers for CI variables/editors\nfile: ci/variables_helper.rb]
        style CiVarsH fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2px,rounded
        CiSFH[Ci::SecureFilesHelper\nSecure files visibility controls\nfile: ci/secure_files_helper.rb]
        style CiSFH fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2px,rounded
      end
    end

    %% 5. DOMAIN DATA STRUCTURE FILES (validators/models-for-data integrity)
    subgraph Data_Structures["UI Data/Structure Files"]
      direction TB
      style Data_Structures fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2px,rounded
      PUV[PublicUrlValidator\nValidates safe URLs for forms/UI\nfile: public_url_validator.rb]
      style PUV fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2px,rounded
    end

    %% 6. UTILITY/SHARED/helpers for controller/view (imported for initialization/state/context)
    subgraph Supporting_Helpers["Supporting & Shared Helpers"]
      direction TB
      style Supporting_Helpers fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2px,rounded
      CPPH[ContinueParams\nControls redirect/continue param passing\nfile: continue_params.rb]
      style CPPH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      PLangSwitchH[PreferredLanguageSwitcher\nLanguage switch parameter resolver\nfile: preferred_language_switcher.rb]
      style PLangSwitchH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      ICOSH[InvisibleCaptchaOnSignup\nInvisible captcha for anti-spam\nfile: invisible_captcha_on_signup.rb]
      style ICOSH fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,rounded
      APPSETH[ApplicationSettingsHelper\nApplication-wide setup, used in many forms\nfile: application_settings_helper.rb]
      style APPSETH fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2px,rounded
    end

    %% 7. NESTED: DOMAIN-SPECIFIC and DATA TRANSFORMATION relationships
    %% Main Relationships - UI Composition & Usage

    %% --- UI Components depend on form/view helpers and utility helpers for presentation logic
    SCC1 --> APH
    SCC1 --> VHL
    SCC1 --> BH2
    SCC1 --> SFSH
    SCC1 --> TAH
    SCC1 --> PH
    SCC1 --> STH
    SCC1 --> ColorsH
    SCC1 --> NotificationsH
    SCC1 --> RHH
    SCC1 --> PLH

    SBC1 --> SCC1
    SBC1 --> BH2
    SBC1 --> APH

    CRUDC1 --> BH2
    CRUDC1 --> APH
    CRUDC1 --> PH
    CRUDC1 --> SFSH
    CRUDC1 --> NotificationsH
    CRUDC1 --> LH
    CRUDC1 --> VHL

    PHC1 --> APH
    PHC1 --> NotificationsH
    PHC1 --> RHH

    OBC1 --> APH
    OBC1 --> TAH
    OBC1 --> PH
    OBC1 --> SFSH
    OBC1 --> VHL

    %% --- Model presentation links badges_helper <-> badge.rb
    BH1 -- renders, calls --> BADGE1
    BADGE1 -- encapsulates, builds URLs for --> BH1

    %% --- Domain Helper modules composition - they call each other for utility
    ApplicationHelper -- provides shared context to --> UI_Components
    ApplicationHelper -- mixes methods from --> ViteHelper
    ApplicationHelper -- context-delegates to --> SafeFormatHelper
    ApplicationHelper -- calls methods from --> IconsHelper
    ApplicationHelper -- provides glue logic for --> NotificationsHelper
    ApplicationHelper -- uses methods from --> TabHelper
    ApplicationHelper -- uses --> TimeHelper
    ApplicationHelper -- supports --> EmailsHelper

    EmailsHelper -- formats --> AppearancesHelper
    NotificationsHelper -- uses --> IconsHelper
    NotificationsHelper -- uses --> BadgesHelper

    GitlabUiFormBuilder -- used by --> UI_Components
    GitlabUiFormBuilder -- used by --> APH
    GitlabUiFormBuilder -- provides abstraction for Pajamas components

    LH -- provides reusable dropdown for --> CRUDC1
    LH -- used by --> ApplicationHelper

    PH -- used across UI for pagination
    KSH -- used for performance pagination in tables/lists

    SFSH -- provides html_safe-format for nearly all helpers

    VisLvlH -- converts domain model data for coloring UI components

    RH -- registration UI
    SafeParamsH -- param cleaning for forms

    %% --- Data Transformation & Validation
    PUV -- validates URLs for --> BADGE1
    PUV -- indirectly supports form helpers via model integrations

    %% --- Supporting Helper Context Use
    CPPH -- included in controllers for redirect handling, populates params for UI actions
    PLangSwitchH -- included in controllers/forms for localization switching and presented via helpers

    APPSETH -- global settings, called from UI helpers like ApplicationHelper, TabHelper
    ICOSH -- anti-spam, included in forms

    %% --- CI pipeline helpers (subgraph lines)
    CiHelpers --> ApplicationHelper
    CiHelpers --> TabHelper
    CiHelpers --> NotificationsHelper
    CiHelpers --> BadgesHelper

    CiStatH -- formats badge color/shape for CI status in UI --> BH1
    CiVarsH -- provides CI variable form paths for UI --> GitlabUiFormBuilder
    CiSFH -- SecureFilesHelper restricts UI/control display in form components

    CipipH -- pipelines list data, returned to view templates composed with tab+pagination helpers

    PH -- orchestrates data structure for Paginated UI lists, used in Form_Helpers & UI_Components

    %% --- Relationships between main helper clusters (high-level)
    UI_Components --> Form_Helpers
    Form_Helpers --> UI_Helpers
    UI_Helpers --> Data_Structures
    Data_Structures --> UI_Components

    Supporting_Helpers --> UI_Components
    Supporting_Helpers --> Form_Helpers
    Supporting_Helpers --> UI_Helpers
    Supporting_Helpers --> Data_Structures

    Model_Presentation --> UI_Components
    UI_Components --> Model_Presentation
    UI_Helpers --> Model_Presentation

  end

  %% Vertical alignment is default; ensure good reading with subgraph spacing

  %% Group Styles
  style UI_Domain fill:#F8F8F8,stroke:#D4F1F9,stroke-width:4px,rounded
```