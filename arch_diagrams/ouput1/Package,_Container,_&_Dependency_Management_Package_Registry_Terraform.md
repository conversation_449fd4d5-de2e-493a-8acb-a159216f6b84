```mermaid
flowchart TD
  %% Styles
  %% Pastel color definitions
  classDef core fill:#D4F1F9,stroke:#85C1E9,stroke-width:2px,color:#222,stroke-dasharray: 0,stroke-linecap:round,rx:8,ry:8
  classDef supporter fill:#FFF8DC,stroke:#EFCB68,stroke-width:2px,color:#222,rx:8,ry:8
  classDef data fill:#E0F8E0,stroke:#85E185,stroke-width:2px,color:#222,rx:8,ry:8
  classDef error fill:#FFE4E1,stroke:#FF8F8F,stroke-width:2px,color:#222,rx:8,ry:8
  classDef init fill:#E6E6FA,stroke:#B299EA,stroke-width:2px,color:#222,rx:8,ry:8
  classDef group fill:#F8F8F8,stroke-width:2px,color:#222
  classDef borderBlue stroke:#85C1E9,stroke-width:2px
  classDef borderYellow stroke:#EFCB68,stroke-width:2px
  classDef borderGreen stroke:#85E185,stroke-width:2px
  classDef borderRed stroke:#FF8F8F,stroke-width:2px
  classDef borderPurple stroke:#B299EA,stroke-width:2px

  %% STRUCTURE

  %% Core Data Structures
  subgraph sgCore["Core Terraform Models & Data Structures"]
    direction TB
    STATE["State\napp/models/terraform/state.rb"]:::core
    TF_MODEL["Terraform\napp/models/terraform.rb"]:::core
    STVER["StateVersion\nee/app/models/ee/terraform/state_version.rb"]:::core
    STVER2["State\nee/app/models/ee/terraform/state.rb"]:::core
    STATEUP["StateUploader\napp/uploaders/terraform/state_uploader.rb"]:::supporter
  end
  class sgCore group,borderBlue

  %% Registry: Terraform Module Packages
  subgraph sgModule["Terraform Module Package Registry"]
    direction TB
    TFM_PKG["Package Record\napp/models/packages/terraform_module.rb"]:::core
    TFM_META["Metadatum Record\napp/models/packages/terraform_module/metadatum.rb"]:::core
    TFM_META_POLICY["Metadatum Policy\napp/policies/packages/terraform_module/metadatum_policy.rb"]:::supporter
    TFM_CREATE["CreatePackageService\napp/services/packages/terraform_module/create_package_service.rb"]:::core
    TFM_FILEWORKER["ProcessPackageFileWorker\napp/workers/packages/terraform_module/process_package_file_worker.rb"]:::supporter
    TFM_META_PROC["ProcessFileService\napp/services/packages/terraform_module/metadata/process_file_service.rb"]:::supporter
    TFM_META_EXTR["ExtractFilesService\napp/services/packages/terraform_module/metadata/extract_files_service.rb"]:::supporter
    TFM_META_PARSEHCL["ParseHclFileService\napp/services/packages/terraform_module/metadata/parse_hcl_file_service.rb"]:::supporter
    TFM_META_UPDATE["UpdateService\napp/services/packages/terraform_module/metadata/update_service.rb"]:::supporter
    TFM_PKGFINDER["PackagesFinder\napp/finders/packages/terraform_module/packages_finder.rb"]:::supporter
  end
  class sgModule group,borderBlue

  %% API Exposure/Interfaces
  subgraph sgAPI["API, Entities, and External Interfaces"]
    direction TB
    API_PROJ_PKG["ProjectPackages API\nlib/api/terraform/modules/v1/project_packages.rb"]:::core
    API_NS_PKG["NamespacePackages API\nlib/api/terraform/modules/v1/namespace_packages.rb"]:::core
    API_ENT_VER["ModuleVersion Entity\nlib/api/entities/terraform/module_version.rb"]:::data
    API_ENT_VERS["ModuleVersions Entity\nlib/api/entities/terraform/module_versions.rb"]:::data
    API_STVER["StateVersion API\nlib/api/terraform/state_version.rb"]:::core
    API_TF_TOKEN["Terraform Registry Token\nlib/gitlab/terraform_registry_token.rb"]:::supporter
    API_PARSER_TFPLAN["CI Tfplan Parser\nlib/gitlab/ci/parsers/terraform/tfplan.rb"]:::supporter
    API_CI_REPORTS["TerraformReports\nlib/gitlab/ci/reports/terraform_reports.rb"]:::data
    API_BACKUP["Backup Task\nlib/backup/tasks/terraform_state.rb"]:::supporter
  end
  class sgAPI group,borderBlue

  %% GraphQL Types for Metadata / Module Registry
  subgraph sgGQL["GraphQL Module Registry Types"]
    direction TB
    TYPE_META_ROOT["RootType\napp/graphql/types/packages/terraform_module/metadatum/root_type.rb"]:::data
    TYPE_META_INPUT["InputType\napp/graphql/types/packages/terraform_module/metadatum/input_type.rb"]:::data
    TYPE_META_DEP["DependencyType\napp/graphql/types/packages/terraform_module/metadatum/dependency_type.rb"]:::data
  end
  class sgGQL group,borderGreen

  %% Services for Terraform State Management
  subgraph sgSVCS["Terraform State Management Services"]
    direction TB
    REMOTE_STATE["RemoteStateHandler\napp/services/terraform/remote_state_handler.rb"]:::core
    DESTROY_S["DestroyService\napp/services/terraform/states/destroy_service.rb"]:::supporter
    DESTROY_TRIGGER_S["TriggerDestroyService\napp/services/terraform/states/trigger_destroy_service.rb"]:::supporter
    TF_STATES_F["StatesFinder\napp/finders/terraform/states_finder.rb"]:::supporter
  end
  class sgSVCS group,borderBlue

  %% Controllers (HTTP/REST interface)
  subgraph sgCTRL["Controllers REST Endpoints"]
    direction TB
    PROJECTS_TF_CTRL["TerraformController\napp/controllers/projects/terraform_controller.rb"]:::core
    TF_SVCS_CTRL["ServicesController\napp/controllers/terraform/services_controller.rb"]:::core
  end
  class sgCTRL group,borderPurple

  %% QA & Spec
  subgraph sgQA["QA/Spec Features"]
    QA_SPEC["Browser UI Spec\nqa/qa/specs/features/browser_ui/5_package/infrastructure_registry/terraform_module_registry_spec.rb"]:::supporter
  end
  class sgQA group,borderYellow

  %% RELATIONSHIPS

  %% Core Data relationships
  TF_MODEL --> STATE
  STATE --> STATEUP
  STATE --> STVER
  STATE -->|versioning/replication| STVER2
  STATEUP --> STATE

  %% State Management Services
  REMOTE_STATE --> STATE
  REMOTE_STATE --> STVER
  DESTROY_S --> STATE
  DESTROY_S --> STVER
  DESTROY_TRIGGER_S --> STATE
  DESTROY_TRIGGER_S --> DESTROY_S

  TF_STATES_F --> STATE

  %% Module Registry: Model/Metadatum
  TFM_PKG --> TFM_META
  TFM_META --> TFM_META_POLICY
  TFM_CREATE --> TFM_PKG
  TFM_CREATE --> TFM_META
  TFM_PKGFINDER --> TFM_PKG

  %% Module File Processing
  TFM_FILEWORKER --> TFM_CREATE
  TFM_FILEWORKER --> TFM_META_PROC
  TFM_FILEWORKER --> TFM_META_EXTR
  TFM_META_PROC --> TFM_META_PARSEHCL
  TFM_META_PROC --> TFM_META_EXTR
  TFM_META_EXTR --> TFM_META_PARSEHCL
  TFM_META_UPDATE --> TFM_META

  %% GraphQL Types relate to Metadatum & Registry
  TYPE_META_ROOT --> TYPE_META_INPUT
  TYPE_META_ROOT --> TYPE_META_DEP
  TYPE_META_ROOT --> TFM_META

  %% API exposure for module registry
  API_PROJ_PKG --> TFM_PKGFINDER
  API_PROJ_PKG --> TFM_CREATE
  API_PROJ_PKG --> API_ENT_VERS
  API_PROJ_PKG --> API_ENT_VER
  API_PROJ_PKG --> API_TF_TOKEN
  API_NS_PKG --> TFM_PKGFINDER
  API_NS_PKG --> API_ENT_VERS
  API_NS_PKG --> API_ENT_VER

  %% API exposure for state management
  API_STVER --> STATE
  API_STVER --> REMOTE_STATE
  API_STVER --> STVER

  API_TF_TOKEN --> API_PROJ_PKG
  API_TF_TOKEN --> API_NS_PKG

  %% CI/CD integration and reports
  API_PARSER_TFPLAN --> API_CI_REPORTS
  API_CI_REPORTS --> STATE

  %% Backup
  API_BACKUP --> STATE

  %% Controllers to Services/APIs
  PROJECTS_TF_CTRL --> STATE
  PROJECTS_TF_CTRL --> REMOTE_STATE
  PROJECTS_TF_CTRL --> DESTROY_S
  PROJECTS_TF_CTRL --> DESTROY_TRIGGER_S
  PROJECTS_TF_CTRL --> TFM_PKG
  PROJECTS_TF_CTRL --> TFM_CREATE
  PROJECTS_TF_CTRL --> TFM_META
  PROJECTS_TF_CTRL --> TF_STATES_F

  TF_SVCS_CTRL --> REMOTE_STATE
  TF_SVCS_CTRL --> STATE

  %% QA Spec covers APIs/controllers
  QA_SPEC --> API_PROJ_PKG
  QA_SPEC --> API_NS_PKG
  QA_SPEC --> PROJECTS_TF_CTRL
  QA_SPEC --> TF_SVCS_CTRL

  %% Cross-API relationship for entities (data exposure)
  API_ENT_VER --> TFM_META
  API_ENT_VER --> TFM_PKG
  API_ENT_VERS --> API_ENT_VER

  %% GraphQL <-> Module Registry
  TYPE_META_ROOT --> API_ENT_VER
  TYPE_META_INPUT --> TFM_META
  TYPE_META_DEP --> TFM_META

  %% Package model prefixing
  TF_MODEL --"module namespace prefixing"--> TFM_PKG

  %% Metadatum strong memoize/shared behaviors
  TFM_META --"uses strong memoize\nSemanticVersionable"--> TFM_CREATE

  %% Color legend node shapes & color block
  style sgCore fill:#F8F8F8,stroke:#85C1E9,stroke-width:2px,rx:20,ry:20
  style sgModule fill:#F8F8F8,stroke:#85C1E9,stroke-width:2px,rx:20,ry:20
  style sgAPI fill:#F8F8F8,stroke:#85C1E9,stroke-width:2px,rx:20,ry:20
  style sgCTRL fill:#F8F8F8,stroke:#B299EA,stroke-width:2px,rx:20,ry:20
  style sgGQL fill:#F8F8F8,stroke:#85E185,stroke-width:2px,rx:20,ry:20
  style sgSVCS fill:#F8F8F8,stroke:#85C1E9,stroke-width:2px,rx:20,ry:20
  style sgQA fill:#F8F8F8,stroke:#EFCB68,stroke-width:2px,rx:20,ry:20
```