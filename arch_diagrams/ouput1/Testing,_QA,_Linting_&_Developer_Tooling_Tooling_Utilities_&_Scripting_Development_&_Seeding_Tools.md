```mermaid
flowchart TD

%%===============================
%% Styles for color consistency
%%===============================
classDef core fill:#D4F1F9,stroke:#A3CADF,stroke-width:2px,stroke-dasharray:0,rx:8,ry:8,color:#333,shape:rect;
classDef support fill:#FFF8DC,stroke:#FFE4A1,stroke-width:2px,stroke-dasharray:0,rx:8,ry:8,color:#333,shape:rect;
classDef data fill:#E0F8E0,stroke:#B6EFC6,stroke-width:2px,stroke-dasharray:0,rx:8,ry:8,color:#333,shape:rect;
classDef error fill:#FFE4E1,stroke:#FFB6B1,stroke-width:2px,stroke-dasharray:0,rx:8,ry:8,color:#333,shape:rect;
classDef init fill:#E6E6FA,stroke:#C3C3E5,stroke-width:2px,stroke-dasharray:0,rx:8,ry:8,color:#333,shape:rect;
classDef subgraph fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rx:12,ry:12

%%===============================
%% Linting & Syntax Utilities
%%===============================
subgraph linting_subgraph["Linting, Syntax, and Ruby Tooling" ]
    class linting_subgraph subgraph
    TCheckRubySyntax["tooling/lib/tooling/check_ruby_syntax.rb
    - Ruby syntax validator for files
    - Defines allowed filetypes/extensions"]:::core

    TLintTemplatesBash["scripts/lint_templates_bash.rb
    - Bash template linter using shellcheck
    - Provides content processing for jobs"]:::support

    MergeRequestRSPECFail["tooling/merge_request_rspec_failure_rake_task.rb
    - Rake task for summarizing MR RSPEC failures"]:::core
end

%%===============================
%% Predictive Test Mapping Layer
%%===============================
subgraph predictive_subgraph["Predictive Test Mapping & Changes" ]
    class predictive_subgraph subgraph
    TFindChanges["tooling/lib/tooling/find_changes.rb
    - Predictive mapping of changed files to required tests
    - Integrates ALLOWED_FILE_TYPES and git diff"]:::core

    MViewToSystemSpec["tooling/lib/tooling/mappings/view_to_system_specs_mappings.rb
    - Maps view changes to system spec files"]:::core

    MPartialToViews["tooling/lib/tooling/mappings/partial_to_views_mappings.rb
    - Maps partials to view files"]:::core

    CrystalballDescClassExecDet["tooling/lib/tooling/crystalball/described_class_execution_detector.rb
    - Crystalball: Detects classes under test, excluding folders"]:::support

    CrystalballCoverageLinesExecDet["tooling/lib/tooling/crystalball/coverage_lines_execution_detector.rb
    - Crystalball: Change-based code execution detector"]:::support
end

%%===============================
%% Parallel Rspec & Test Runners
%%===============================
subgraph runner_subgraph["Parallel Test Runners & Quarantine Analysis"]
    class runner_subgraph subgraph
    ParallelRSpecRunner["tooling/lib/tooling/parallel_rspec_runner.rb
    - Enhanced parallel playback for RSPEC via Knapsack
    - Filters/predicts tests to run"]:::core
    FastQuarantine["tooling/lib/tooling/fast_quarantine.rb
    - Fast quarantine of flaky or unstable tests"]:::core
    TJMetrics["tooling/lib/tooling/job_metrics.rb
    - Stores and pushes CI job metrics and tags"]:::data
end

%%===============================
%% Tooling Utilities, Scripts & Developer Tools
%%===============================
subgraph tooling_scripts_subgraph["Tooling Utilities & Developer/QA Scripts"]
    class tooling_scripts_subgraph subgraph

    InternalEventsCli["scripts/internal_events/cli.rb
    - Main CLI entrypoint for Internal Events"]:::core
    InternalEventsCliHelpersFmt["scripts/internal_events/cli/helpers/formatting.rb
    - Formatting helpers for CLI user output"]:::support
    InternalEventsCliHelpersInputs["scripts/internal_events/cli/helpers/cli_inputs.rb
    - CLI input helpers for multiple options & prompts"]:::support
    InternalEventsCliEvent["scripts/internal_events/cli/event.rb
    - Event representation and YAML writer"]:::data
    InternalEventsCliMetric["scripts/internal_events/cli/metric.rb
    - Metric structure definition and manipulation"]:::data

    GenerateMessageE2E["scripts/generate-message-to-run-e2e-pipeline.rb
    - Generates MR note to run E2E pipeline"]:::support

    GenerateRspecPipeline["scripts/generate_rspec_pipeline.rb
    - Generates optimal RSPEC pipeline/partitioning"]:::core

    DocsScreenshots["scripts/docs_screenshots.rb
    - Screenshot management for docs artifacts"]:::support

    SetupFossEnv["scripts/setup/generate-as-if-foss-env.rb
    - Generates environment variables for FOSS mode"]:::support

    MinifySimplecovResultsets["scripts/minify-simplecov-resultsets.rb
    - Reduces SimpleCov result size for performance"]:::support

end

%%===============================
%% API Support Layer
%%===============================
subgraph api_subgraph["Tooling API Infrastructure"]
    class api_subgraph subgraph
    TAPIRequest["tooling/lib/tooling/api/request.rb
    - Tooling API Request Handler REST, paginated responses"]:::core
end

%%===============================
%% DAST Documentation Tools
%%===============================
subgraph dast_docs_subgraph["DAST Variables Documentation Tooling"]
    class dast_docs_subgraph subgraph
    DastDocsRenderer["tooling/dast_variables/docs/renderer.rb
    - Renders DAST variable docs from templates"]:::support
    DastDocsHelper["tooling/dast_variables/docs/helper.rb
    - Provides rendering methods, table output, descriptions"]:::support
end

%%===============================
%% Debugging & Dev Logging
%%===============================
subgraph debug_subgraph["Tooling Debugging & Diagnostics"]
    class debug_subgraph subgraph
    TDebug["tooling/lib/tooling/debug.rb
    - Developer debug toggles & printing"]:::support
end

%%===============================
%% Danger Automation
%%===============================
subgraph danger_subgraph["Automation: Danger Rules QA and Sidekiq"]
    class danger_subgraph subgraph
    DangerSidekiqQueues["tooling/danger/sidekiq_queues.rb
    - Danger rules for detecting Sidekiq queue changes"]:::support
end

%%===============================
%% Orchestration: QA, Seeding & Specialized Tools
%%===============================

subgraph qa_subgraph["E2E/QA Tooling Utilities & Cleanup Scripts"]
    class qa_subgraph subgraph

    subgraph qa_resource_management["QA Resource Management - Project, Group, User"]
        class qa_resource_management subgraph
        QADeleteResourceBase["qa/qa/tools/delete_resource_base.rb
        - Abstract base for delete helpers, API, Group/Project/Wait"]:::core

        QADelProjects["qa/qa/tools/delete_projects.rb
        - Deletes projects via API"]:::core
        QADelSubgroups["qa/qa/tools/delete_subgroups.rb
        - Deletes subgroups via API"]:::core
        QADelUserProjects["qa/qa/tools/delete_user_projects.rb
        - User-specific project deletion"]:::core
        QARevokePats["qa/qa/tools/revoke_user_personal_access_tokens.rb
        - Revokes Personal Access Tokens"]:::core

        QALibGroup["qa/qa/tools/lib/group.rb
        - Group resource helpers"]:::support
        QALibProject["qa/qa/tools/lib/project.rb
        - Project resource helpers"]:::support
    end

    subgraph qa_migration["QA Migration Helpers"]
      class qa_migration subgraph
      QAMigInfluxToGcs["qa/qa/tools/migrate_influx_data_to_gcs.rb
      - InfluxDB to GCS migrator base"]:::support
      QAMigInfluxToGcsCsv["qa/qa/tools/migrate_influx_data_to_gcs_csv.rb
      - InfluxDB to GCS as CSV"]:::support
      QAMigInfluxToGcsJson["qa/qa/tools/migrate_influx_data_to_gcs_json.rb
      - InfluxDB to GCS as JSON"]:::support
    end

    QAInitializeAuth["qa/qa/tools/initialize_gitlab_auth.rb
    - Sets up GitLab authentication, seeds Personal Access Token if needed"]:::core

    QAKnapsackRU["qa/qa/tools/knapsack_report_updater.rb
    - Updates Knapsack master report via API"]:::support

    subgraph qa_ci_utils["QA CI Utilities & Mapping"]
      class qa_ci_utils subgraph
      QACiFfChanges["qa/qa/tools/ci/ff_changes.rb
      - Detects FF changes in MRs"]:::support
      QACiPipelineCreator["qa/qa/tools/ci/pipeline_creator.rb
      - Manages definitions for various pipeline types"]:::core
      QACiScenarioExamples["qa/qa/tools/ci/scenario_examples.rb
      - Introspects RSpec examples for pipeline scenarios"]:::support
      QACiQaChanges["qa/qa/tools/ci/qa_changes.rb
      - Identifies QA specs or paths impacted by code changes"]:::support
      QACiCodePathsMapping["qa/qa/tools/ci/code_paths_mapping.rb
      - Exports code path mapping files to GCS"]:::support
    end

end

%%===============================
%% Seeding & Quality Data Generators
%%===============================
subgraph seeding_subgraph["Seeding, Quality Fixtures & Data Utilities"]
    class seeding_subgraph subgraph

    SeederDependencies["ee/lib/quality/seeders/dependencies.rb
    - EE quality seeder for dependency data"]:::support
    SeederIssues["lib/quality/seeders/issues.rb
    - Seeds issues for backfill and tests"]:::support

    DataSeederRakeSpec["ee/spec/tasks/gitlab/seed/data_seeder_rake_spec.rb
    - RSpec suite for seeder tasks"]:::support
end

%%===============================
%% TEST SUPPORT: Specs, Deprecation, RSpec Order, Helpers & Fixtures
%%===============================
subgraph test_support_subgraph["Test, QA, RSpec Support & Deprecations"]
    class test_support_subgraph subgraph
    DeprecationToolkitEnv["spec/deprecation_toolkit_env.rb
    - Custom deprecation toolkit for tests, raises on disallowed usages"]:::support

    QASpecsQaDeprecationToolkitEnv["qa/qa/specs/qa_deprecation_toolkit_env.rb
    - QA-specific deprecation toolkit configuration"]:::support

    RSpecOrder["spec/support/rspec_order.rb
    - Custom RSpec order and formatter"]:::support

    RSpecReqMigration["spec/support/helpers/require_migration.rb
    - Automatically locate & require DB migrations in specs"]:::support

    SupportFips["spec/support/fips.rb
    - Helpers for FIPS-mode related specs"]:::support

    SupportForgeryProtection["spec/support/forgery_protection.rb
    - Helpers for forgery protection tests"]:::support

    ExportFileHelper["spec/support/import_export/export_file_helper.rb
    - File helpers for import/export spec validations"]:::support

    BenchmarksBanzai["spec/benchmarks/banzai_benchmark.rb
    - RSpec suite: Markdown benchmark, uses MarkupHelper, etc."]:::support

    ToolingFixturesSampleMetric["spec/tooling/fixtures/metrics/sample_instrumentation_metric.rb
    - Sample metric instrumentation fixture"]:::data

    InternalEventsCliFlowsMetricSpec["spec/scripts/internal_events/cli/flows/metric_definer_spec.rb
    - Specs for Internal Events CLI metric flow"]:::support

    QAHelpersFastQuarantine["qa/qa/specs/helpers/fast_quarantine.rb
    - QA spec helper for Fast-Quarantine"]:::support
end

%%===============================
%% RELATIONSHIPS AND CONNECTIONS
%%===============================

%% Predictive Test Mapping
TFindChanges -- maps changed files --> MViewToSystemSpec
TFindChanges -- also maps changed files --> MPartialToViews
TFindChanges -- uses exclusion & class detection --> CrystalballDescClassExecDet
TFindChanges -- uses coverage-based path detection --> CrystalballCoverageLinesExecDet
MViewToSystemSpec -- includes --> TFindChanges
MPartialToViews -- includes --> TFindChanges

%% Parallel RSpec, Test Runners
ParallelRSpecRunner -- manages test selection --> TFindChanges
ParallelRSpecRunner -- reads quarantine info --> FastQuarantine
ParallelRSpecRunner -- collects --> TJMetrics

%% QA Resource Management
QADeleteResourceBase -- includes --> QALibGroup
QADeleteResourceBase -- includes --> QALibProject
QADelProjects -- extends --> QADeleteResourceBase
QADelSubgroups -- extends --> QADeleteResourceBase
QADelUserProjects -- extends --> QADeleteResourceBase
QARevokePats -- extends --> QADeleteResourceBase

%% QA Migration
QAMigInfluxToGcsCsv -- extends --> QAMigInfluxToGcs
QAMigInfluxToGcsJson -- extends --> QAMigInfluxToGcs

%% QA CI Utilities
QACiScenarioExamples -- uses --> QACiPipelineCreator
QACiQaChanges -- calls --> QACiScenarioExamples
QACiCodePathsMapping -- generates mapping for --> QACiFfChanges
QAKnapsackRU -- updates master report used in --> ParallelRSpecRunner

%% DAST Docs Connections
DastDocsRenderer -- includes --> DastDocsHelper

%% Event CLI Data Flow
InternalEventsCli -- uses --> InternalEventsCliEvent
InternalEventsCli -- uses --> InternalEventsCliMetric
InternalEventsCli -- uses --> InternalEventsCliHelpersFmt
InternalEventsCli -- uses --> InternalEventsCliHelpersInputs
InternalEventsCliHelpersFmt -- used by --> InternalEventsCliHelpersInputs

%% RSpec Order
RSpecOrder -- configures ordering for --> RSpecReqMigration
RSpecOrder -- provides formatter for --> BenchmarksBanzai

%% FastQuarantine Spec Helper
FastQuarantine -- is configured by --> QAHelpersFastQuarantine

%% Tooling API Request
TAPIRequest -- utility for --> QADeleteResourceBase
TAPIRequest -- utility for --> QADelProjects
TAPIRequest -- utility for --> QADelSubgroups

%% Seeder Rake Spec
SeederDependencies -- used by --> DataSeederRakeSpec

%% Docs Screenshots
DocsScreenshots -- used in doc generation pipelines --> DastDocsRenderer

%% Misc: Scripting utilities
MinifySimplecovResultsets -- processes --> TJMetrics

%% Linting
TCheckRubySyntax -- used by --> MergeRequestRSPECFail
TLintTemplatesBash -- invoked by developer scripts --> SetupFossEnv

%% Debugging
TDebug -- enabled by --> GenerateRspecPipeline

%% Danger Bot
DangerSidekiqQueues -- monitors changes for --> MergeRequestRSPECFail

%% Grouped test dependencies (support/fixtures)
ToolingFixturesSampleMetric -- fixture for --> DeprecationToolkitEnv
ToolingFixturesSampleMetric -- used in --> InternalEventsCliFlowsMetricSpec

%% FINAL GROUPING FOR REFERENCE
class TCheckRubySyntax,MergeRequestRSPECFail,TFindChanges,MViewToSystemSpec,MPartialToViews,ParallelRSpecRunner,FastQuarantine,QADeleteResourceBase,QADelProjects,QADelSubgroups,QADelUserProjects,QARevokePats,QACiPipelineCreator,QAInitializeAuth,GenerateRspecPipeline,InternalEventsCli,TAPIRequest core;

class CrystalballDescClassExecDet,CrystalballCoverageLinesExecDet,TLintTemplatesBash,TDebug,TJMetrics,SeederDependencies,SeederIssues,DocsScreenshots,QALibGroup,QALibProject,QAMigInfluxToGcs,QAMigInfluxToGcsCsv,QAMigInfluxToGcsJson,QAKnapsackRU,QACiQaChanges,QACiScenarioExamples,QACiFfChanges,QACiCodePathsMapping,MinifySimplecovResultsets,DastDocsRenderer,DastDocsHelper,DeprecationToolkitEnv,QASpecsQaDeprecationToolkitEnv,RSpecOrder,RSpecReqMigration,SupportFips,SupportForgeryProtection,ExportFileHelper,BenchmarksBanzai,InternalEventsCliHelpersFmt,InternalEventsCliHelpersInputs,QAHelpersFastQuarantine,DataSeederRakeSpec,DangerSidekiqQueues support;

class ToolingFixturesSampleMetric,InternalEventsCliEvent,InternalEventsCliMetric data;
class DocsScreenshots error;
class SetupFossEnv,QAInitializeAuth init;

%%===============================
%% END OF DIAGRAM
%%===============================
```