```mermaid
flowchart TD
%% --- COLORS & SHAPES ---
%% Core domain files: pastel blue #D4F1F9
%% Supporting/utility files: pastel yellow #FFF8DC
%% Data structure files: pastel green #E0F8E0
%% Error handling files: pastel red #FFE4E1
%% Initialization/setup files: pastel purple #E6E6FA
%% Logical groups: #F8F8F8 bg, pastel border

%% --- DOMAIN ENTRYPOINTS & CONTROLLERS ---
subgraph MainEntrypoints["Entry Points and Orchestration"]
direction TB
style MainEntrypoints fill:#F8F8F8,stroke:#B2EBF2,stroke-width:2px
TRIALS_CONTROLLER["Trials Controller\nee/app/controllers/gitlab_subscriptions/trials_controller.rb"]
style TRIALS_CONTROLLER fill:#D4F1F9,stroke:#90CAF9,stroke-width:1.5px,rx:15,ry:15
DUO_COMMON_CONCERN["Duo Common Concern Controller mixin\nee/app/controllers/concerns/gitlab_subscriptions/trials/duo_common.rb"]
style DUO_COMMON_CONCERN fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
END

%% --- CORE SUBSCRIPTION/TRIAL MODELS ---
subgraph SubscriptionModels["Core Domain: Plans, Limits, Subscriptions"]
direction TB
style SubscriptionModels fill:#F8F8F8,stroke:#64B5F6,stroke-width:2px
PLAN["Plan\napp/models/plan.rb"]
style PLAN fill:#D4F1F9,stroke:#90CAF9,stroke-width:1.5px,rx:15,ry:15
PLAN_LIMITS["PlanLimits\napp/models/plan_limits.rb"]
style PLAN_LIMITS fill:#D4F1F9,stroke:#90CAF9,stroke-width:1.5px,rx:15,ry:15
EE_PLAN["EE Extension: Plan\nee/app/models/ee/plan.rb"]
style EE_PLAN fill:#D4F1F9,stroke:#90CAF9,stroke-width:1.5px,rx:15,ry:15
TRIAL_STATUS["TrialStatus Entity\nee/app/models/gitlab_subscriptions/trial_status.rb"]
style TRIAL_STATUS fill:#E0F8E0,stroke:#A5D6A7,stroke-width:1.5px,rx:15,ry:15
ADD_ON_STATUS["AddOnStatus Entity\nee/app/models/gitlab_subscriptions/trials/add_on_status.rb"]
style ADD_ON_STATUS fill:#E0F8E0,stroke:#A5D6A7,stroke-width:1.5px,rx:15,ry:15
DUO["Duo Add-On Domain\nee/app/models/gitlab_subscriptions/duo.rb"]
style DUO fill:#D4F1F9,stroke:#90CAF9,stroke-width:1.5px,rx:15,ry:15
DUOPRO["DuoPro Add-On Domain\nee/app/models/gitlab_subscriptions/duo_pro.rb"]
style DUOPRO fill:#D4F1F9,stroke:#90CAF9,stroke-width:1.5px,rx:15,ry:15
DUOENT["DuoEnterprise Add-On Domain\nee/app/models/gitlab_subscriptions/duo_enterprise.rb"]
style DUOENT fill:#D4F1F9,stroke:#90CAF9,stroke-width:1.5px,rx:15,ry:15
TRIALS["Trials Logic Types/Eligibility/Util\nee/app/models/gitlab_subscriptions/trials.rb"]
style TRIALS fill:#D4F1F9,stroke:#90CAF9,stroke-width:1.5px,rx:15,ry:15
TRIALSDUOPRO["Trial: DuoPro\nee/app/models/gitlab_subscriptions/trials/duo_pro.rb"]
style TRIALSDUOPRO fill:#D4F1F9,stroke:#90CAF9,stroke-width:1.5px,rx:15,ry:15
TRIALSDUOENT["Trial: DuoEnterprise\nee/app/models/gitlab_subscriptions/trials/duo_enterprise.rb"]
style TRIALSDUOENT fill:#D4F1F9,stroke:#90CAF9,stroke-width:1.5px,rx:15,ry:15
TRIALSDUOADDON["Trial: DuoAddOn\nee/app/models/gitlab_subscriptions/trials/duo_add_on.rb"]
style TRIALSDUOADDON fill:#D4F1F9,stroke:#90CAF9,stroke-width:1.5px,rx:15,ry:15
HAS_USER_TYPE["User Type Concern\napp/models/concerns/has_user_type.rb"]
style HAS_USER_TYPE fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
END

%% --- SERVICES: BUSINESS LOGIC ---
subgraph TrialsServices["Business Logic: Services"]
direction TB
style TrialsServices fill:#F8F8F8,stroke:#FFD54F,stroke-width:2px
CREATE_SERVICE["Create Trial Service\nee/app/services/gitlab_subscriptions/trials/create_service.rb"]
style CREATE_SERVICE fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
BASE_APPLY_TRIAL["Base Apply Trial Service\nee/app/services/gitlab_subscriptions/trials/base_apply_trial_service.rb"]
style BASE_APPLY_TRIAL fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
APPLY_TRIAL["ApplyTrial Service\nee/app/services/gitlab_subscriptions/trials/apply_trial_service.rb"]
style APPLY_TRIAL fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
APPLY_DUOPRO["ApplyDuoPro Service\nee/app/services/gitlab_subscriptions/trials/*********************.rb"]
style APPLY_DUOPRO fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
APPLY_DUOENT["ApplyDuoEnterprise Service\nee/app/services/gitlab_subscriptions/trials/apply_duo_enterprise_service.rb"]
style APPLY_DUOENT fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
BASE_CREATE_ADDON["BaseCreateAddOn Service\nee/app/services/gitlab_subscriptions/trials/base_create_add_on_service.rb"]
style BASE_CREATE_ADDON fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
CREATE_ADDON_LEAD["Create AddOn Lead Service\nee/app/services/gitlab_subscriptions/trials/create_add_on_lead_service.rb"]
style CREATE_ADDON_LEAD fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
CREATE_DUOPRO["CreateDuoPro Service\nee/app/services/gitlab_subscriptions/trials/create_duo_pro_service.rb"]
style CREATE_DUOPRO fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
CREATE_DUOENT["CreateDuoEnterprise Service\nee/app/services/gitlab_subscriptions/trials/create_duo_enterprise_service.rb"]
style CREATE_DUOENT fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
CREATE_LEAD["CreateLead Service\nee/app/services/gitlab_subscriptions/create_lead_service.rb"]
style CREATE_LEAD fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
APPLY_TRIAL_WORKER["ApplyTrial Worker\nee/app/workers/gitlab_subscriptions/trials/apply_trial_worker.rb"]
style APPLY_TRIAL_WORKER fill:#E6E6FA,stroke:#CE93D8,stroke-width:1.5px,rx:15,ry:15
CHECK_FUTURE_RENEWAL["CheckFutureRenewal Service\nee/app/services/gitlab_subscriptions/check_future_renewal_service.rb"]
style CHECK_FUTURE_RENEWAL fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
FETCH_PLANS["FetchSubscriptionPlans Service\nee/app/services/gitlab_subscriptions/fetch_subscription_plans_service.rb"]
style FETCH_PLANS fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
USER_ADDON_ASSIGN_SAAS["Create Saas User Add-On Assignment\nee/app/services/gitlab_subscriptions/user_add_on_assignments/saas/create_without_notification_service.rb"]
style USER_ADDON_ASSIGN_SAAS fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
END

%% --- PRESENTERS AND WIDGETS ---
subgraph Presenters["Presenters and UI Adapters"]
direction TB
style Presenters fill:#F8F8F8,stroke:#AED581,stroke-width:2px
SUB_PRESENTER["Subscription Presenter\nee/app/presenters/subscription_presenter.rb"]
style SUB_PRESENTER fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
TRIAL_STATUS_WIDGET["Trial StatusWidget Presenter\nee/app/presenters/gitlab_subscriptions/trials/status_widget_presenter.rb"]
style TRIAL_STATUS_WIDGET fill:#D4F1F9,stroke:#90CAF9,stroke-width:1.5px,rx:15,ry:15
DUOENT_WIDGET_PRES["DuoEnterprise StatusWidgetPresenter\nee/app/presenters/gitlab_subscriptions/trials/duo_enterprise_status_widget_presenter.rb"]
style DUOENT_WIDGET_PRES fill:#D4F1F9,stroke:#90CAF9,stroke-width:1.5px,rx:15,ry:15
END

%% --- COMPONENTS: UI ---
subgraph UIComponents["UI Components: Forms, Cards, Widgets"]
direction TB
style UIComponents fill:#F8F8F8,stroke:#FFB74D,stroke-width:2px
TRIALFORM["TrialForm Component\nee/app/components/gitlab_subscriptions/trials/trial_form_component.rb"]
style TRIALFORM fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
TRIALFORM_WITH_ERRORS["TrialFormWithErrors Component\nee/app/components/gitlab_subscriptions/trials/trial_form_with_errors_component.rb"]
style TRIALFORM_WITH_ERRORS fill:#FFE4E1,stroke:#E57373,stroke-width:1.5px,rx:15,ry:15
LEAD_FORM["LeadForm Component\nee/app/components/gitlab_subscriptions/trials/lead_form_component.rb"]
style LEAD_FORM fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
LEAD_FORM_WITH_ERRORS["LeadFormWithErrors Component\nee/app/components/gitlab_subscriptions/trials/lead_form_with_errors_component.rb"]
style LEAD_FORM_WITH_ERRORS fill:#FFE4E1,stroke:#E57373,stroke-width:1.5px,rx:15,ry:15
FORM_ERRORS["FormErrorsComponent\nee/app/components/gitlab_subscriptions/trials/form_errors_component.rb"]
style FORM_ERRORS fill:#FFE4E1,stroke:#E57373,stroke-width:1.5px,rx:15,ry:15
DUOENT_FORM_ERRORS["DuoEnterprise FormErrorsComponent\nee/app/components/gitlab_subscriptions/trials/duo_enterprise/form_errors_component.rb"]
style DUOENT_FORM_ERRORS fill:#FFE4E1,stroke:#E57373,stroke-width:1.5px,rx:15,ry:15

DUOPRO_LEAD_FORM["DuoPro LeadFormComponent\nee/app/components/gitlab_subscriptions/trials/duo_pro/lead_form_component.rb"]
style DUOPRO_LEAD_FORM fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
DUOPRO_LEAD_FORM_ERRORS["DuoPro LeadFormWithErrors\nee/app/components/gitlab_subscriptions/trials/duo_pro/lead_form_with_errors_component.rb"]
style DUOPRO_LEAD_FORM_ERRORS fill:#FFE4E1,stroke:#E57373,stroke-width:1.5px,rx:15,ry:15
DUOENT_LEAD_FORM["DuoEnterprise LeadFormComponent\nee/app/components/gitlab_subscriptions/trials/duo_enterprise/lead_form_component.rb"]
style DUOENT_LEAD_FORM fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
DUOENT_LEAD_FORM_ERRORS["DuoEnterprise LeadFormWithErrors\nee/app/components/gitlab_subscriptions/trials/duo_enterprise/lead_form_with_errors_component.rb"]
style DUOENT_LEAD_FORM_ERRORS fill:#FFE4E1,stroke:#E57373,stroke-width:1.5px,rx:15,ry:15

BASE_DISCOVER["BaseDiscoverComponent\nee/app/components/gitlab_subscriptions/base_discover_component.rb"]
style BASE_DISCOVER fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
DISCOVER_TRIAL["DiscoverTrialComponent\nee/app/components/gitlab_subscriptions/discover_trial_component.rb"]
style DISCOVER_TRIAL fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
DISCOVER_DUOPRO["DiscoverDuoProComponent\nee/app/components/gitlab_subscriptions/discover_duo_pro_component.rb"]
style DISCOVER_DUOPRO fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
DISCOVER_DUOENT["DiscoverDuoEnterpriseComponent\nee/app/components/gitlab_subscriptions/discover_duo_enterprise_component.rb"]
style DISCOVER_DUOENT fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15

TRIAL_ADVANTAGES["TrialAdvantagesComponent\nee/app/components/gitlab_subscriptions/trial_advantages_component.rb"]
style TRIAL_ADVANTAGES fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
TRIAL_ADVANTAGE["TrialAdvantageComponent\nee/app/components/gitlab_subscriptions/trial_advantage_component.rb"]
style TRIAL_ADVANTAGE fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15

DUOENT_ADVANTAGE_LIST["DuoEnterprise AdvantagesListComponent\nee/app/components/gitlab_subscriptions/trials/duo_enterprise/advantages_list_component.rb"]
style DUOENT_ADVANTAGE_LIST fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
DUOPRO_ADVANTAGE_LIST["DuoPro AdvantagesListComponent\nee/app/components/gitlab_subscriptions/trials/duo_pro/advantages_list_component.rb"]
style DUOPRO_ADVANTAGE_LIST fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15

DUOENT_ALERT_BASE["DuoEnterprise Alert Base\nee/app/components/gitlab_subscriptions/duo_enterprise_alert/base_component.rb"]
style DUOENT_ALERT_BASE fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
DUOENT_ALERT_FREE["DuoEnterprise Alert Free\nee/app/components/gitlab_subscriptions/duo_enterprise_alert/free_component.rb"]
style DUOENT_ALERT_FREE fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
DUOENT_ALERT_PREMIUM["DuoEnterprise Alert Premium\nee/app/components/gitlab_subscriptions/duo_enterprise_alert/premium_component.rb"]
style DUOENT_ALERT_PREMIUM fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
DUOENT_ALERT_ULT["DuoEnterprise Alert Ultimate\nee/app/components/gitlab_subscriptions/duo_enterprise_alert/ultimate_component.rb"]
style DUOENT_ALERT_ULT fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15

BILLING_PLAN["Billing/Plan Component\nee/app/components/billing/plan_component.rb"]
style BILLING_PLAN fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
END

%% --- POLICIES: AUTHZ ---
subgraph Policies["Authorization Policies"]
direction TB
style Policies fill:#F8F8F8,stroke:#AED581,stroke-width:2px
ADDON_POL["AddOnPurchasePolicy\nee/app/policies/gitlab_subscriptions/add_on_purchase_policy.rb"]
style ADDON_POL fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
USER_ADDON_ASSIGN_POL["UserAddOnAssignmentPolicy\nee/app/policies/gitlab_subscriptions/user_add_on_assignment_policy.rb"]
style USER_ADDON_ASSIGN_POL fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
END

%% --- HELPERS & SUPPORT LOGIC ---
subgraph Helpers["Helpers, Query Finders, Utilities"]
direction TB
style Helpers fill:#F8F8F8,stroke:#FFD54F,stroke-width:2px
TRIALS_HELPER["Trials Helper\nee/app/helpers/gitlab_subscriptions/trials_helper.rb"]
style TRIALS_HELPER fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
SELECTS_HELPER["Selects Helper\nee/app/helpers/selects_helper.rb"]
style SELECTS_HELPER fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
PROMO_MANAGEMENT["PromotionManagement Helper\nee/app/helpers/gitlab_subscriptions/promotion_management_helper.rb"]
style PROMO_MANAGEMENT fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
RECONCILE_HELPER["UpcomingReconciliation Helper\nee/app/helpers/gitlab_subscriptions/upcoming_reconciliation_helper.rb"]
style RECONCILE_HELPER fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
TRIAL_ELIGIBLE_FINDER["Namespaces::TrialEligibleFinder\nee/app/finders/namespaces/trial_eligible_finder.rb"]
style TRIAL_ELIGIBLE_FINDER fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
END

%% --- PORTALS: EXTERNAL CONNECTIONS ---
subgraph Portals["External Integrations: Subscription Portal"]
direction TB
style Portals fill:#F8F8F8,stroke:#64B5F6,stroke-width:2px
PORTAL_CLIENT["Subscription Portal Client REST\nee/lib/gitlab/subscription_portal/clients/rest.rb"]
style PORTAL_CLIENT fill:#FFF8DC,stroke:#FFE082,stroke-width:1.5px,rx:15,ry:15
END

%% --- DATA STRUCTURES: SUPPORT ---
subgraph DataStructures["Data Structures and API/DB Utilities"]
direction TB
style DataStructures fill:#F8F8F8,stroke:#A5D6A7,stroke-width:2px
PLAN_LIMITS_API["API: Admin::PlanLimits\nlib/api/admin/plan_limits.rb"]
style PLAN_LIMITS_API fill:#E0F8E0,stroke:#A5D6A7,stroke-width:1.5px,rx:15,ry:15
PLAN_LIMITS_UPDATE["PlanLimits UpdateService\nee/app/services/ee/admin/plan_limits/update_service.rb"]
style PLAN_LIMITS_UPDATE fill:#E0F8E0,stroke:#A5D6A7,stroke-width:1.5px,rx:15,ry:15
END

%% --- RELATIONSHIPS LOGIC ---

%% Controller Uses Business Logic
TRIALS_CONTROLLER --> |uses| DUO_COMMON_CONCERN
TRIALS_CONTROLLER --> |invokes| CREATE_SERVICE
TRIALS_CONTROLLER --> |uses| APPLY_TRIAL_WORKER
TRIALS_CONTROLLER --> |presents| TRIAL_STATUS_WIDGET
TRIALS_CONTROLLER --> |invokes| TRIALS_HELPER
TRIALS_CONTROLLER --> |renders| TRIALFORM
TRIALS_CONTROLLER --> |renders| LEAD_FORM

%% Controller/Helpers => Models
TRIALS_CONTROLLER --> |loads| TRIALS
TRIALS_CONTROLLER --> |loads| PLAN
TRIALS_CONTROLLER --> |checks eligibility| TRIAL_ELIGIBLE_FINDER
TRIALS_CONTROLLER --> |handles| DUOPRO, DUOENT

%% Models inherit/extend/compose
EE_PLAN --> PLAN
PLAN_LIMITS_API --> PLAN_LIMITS
PLAN_LIMITS_UPDATE --> PLAN_LIMITS
DUO --> |calls| DUOPRO
DUO --> |calls| DUOENT

DUOPRO --> |queries| PLAN
DUOENT --> |queries| PLAN

TRIALS --> |utilizes| DUOPRO
TRIALS --> |utilizes| DUOENT
TRIALS --> |queries| PLAN

TRIALSDUOPRO --> |uses| DUOPRO
TRIALSDUOENT --> |uses| DUOENT
TRIALSDUOADDON --> |uses| DUO

%% Services - Execution Flow
CREATE_SERVICE --> |calls| TRIALS
CREATE_SERVICE --> |calls| TRIAL_ELIGIBLE_FINDER
CREATE_SERVICE --> |delegates| CREATE_LEAD
CREATE_SERVICE --> |delegates| APPLY_TRIAL
CREATE_SERVICE --> |delegates| BASE_APPLY_TRIAL

CREATE_SERVICE --> |delegates| BASE_CREATE_ADDON
BASE_CREATE_ADDON --> |calls| CREATE_ADDON_LEAD
CREATE_ADDON_LEAD --> |executes| PORTAL_CLIENT

CREATE_SERVICE --> |invokes| CREATE_DUOPRO
CREATE_SERVICE --> |invokes| CREATE_DUOENT
CREATE_DUOPRO --> |calls| APPLY_DUOPRO
CREATE_DUOENT --> |calls| APPLY_DUOENT

APPLY_TRIAL_WORKER --> |invokes| APPLY_TRIAL

APPLY_TRIAL --> |delegates| BASE_APPLY_TRIAL
APPLY_DUOPRO --> |delegates| BASE_APPLY_TRIAL
APPLY_DUOENT --> |delegates| BASE_APPLY_TRIAL

APPLY_TRIAL --> |uses eligibility| TRIALS
APPLY_DUOENT --> |uses eligibility| DUOENT
APPLY_DUOPRO --> |uses eligibility| DUOPRO

CREATE_LEAD --> |executes| PORTAL_CLIENT

USER_ADDON_ASSIGN_SAAS --> |checks| DUOPRO

CHECK_FUTURE_RENEWAL --> |queries| PLAN

FETCH_PLANS --> |sends| PORTAL_CLIENT

%% UI components composition
TRIALFORM --> |uses| TRIALFORM_WITH_ERRORS
TRIALFORM --> |uses| LEAD_FORM
LEAD_FORM --> |uses| LEAD_FORM_WITH_ERRORS
LEAD_FORM_WITH_ERRORS --> |shows| FORM_ERRORS
LEAD_FORM_WITH_ERRORS --> |shows| DUOENT_FORM_ERRORS

TRIALFORM_WITH_ERRORS --> |shows errors| FORM_ERRORS

DUOPRO_LEAD_FORM --> |uses| DUOPRO_LEAD_FORM_ERRORS
DUOENT_LEAD_FORM --> |uses| DUOENT_LEAD_FORM_ERRORS

TRIALFORM --> |presents| TRIAL_ADVANTAGES
TRIAL_ADVANTAGES --> |composes| TRIAL_ADVANTAGE
DUOENT_ADVANTAGE_LIST --> |composes| TRIAL_ADVANTAGES
DUOPRO_ADVANTAGE_LIST --> |composes| TRIAL_ADVANTAGES

BASE_DISCOVER --> |inherited by| DISCOVER_TRIAL
BASE_DISCOVER --> |inherited by| DISCOVER_DUOPRO
DISCOVER_DUOPRO --> |inherited by| DISCOVER_DUOENT

DUOENT_ALERT_BASE --> |inherited by| DUOENT_ALERT_FREE
DUOENT_ALERT_BASE --> |inherited by| DUOENT_ALERT_PREMIUM
DUOENT_ALERT_BASE --> |inherited by| DUOENT_ALERT_ULT

BILLING_PLAN --> |queries| PLAN

%% Presenters surface trial status logic
TRIAL_STATUS_WIDGET --> |uses| TRIAL_STATUS
TRIAL_STATUS_WIDGET --> |presents| DUOENT_WIDGET_PRES
SUB_PRESENTER --> |uses| PLAN

%% Policies reference domain logic
ADDON_POL --> |depends on| DUO
USER_ADDON_ASSIGN_POL --> |depends on| ADDON_POL

%% Helper utilities
TRIALS_HELPER --> |generates form data for| TRIALFORM
TRIALS_HELPER --> |provides namespace selector| TRIAL_ELIGIBLE_FINDER
TRIALS_HELPER --> |outputs trial error messages| FORM_ERRORS

RECONCILE_HELPER --> |outputs warning| PLAN

PROMO_MANAGEMENT --> |applies promotion logic for| PLAN

SELECTS_HELPER --> |supports| TRIALFORM

%% Eligibility Finder ties domain concepts
TRIAL_ELIGIBLE_FINDER --> |queries| PLAN
TRIAL_ELIGIBLE_FINDER --> |queries| PLAN_LIMITS
TRIAL_ELIGIBLE_FINDER --> |supports| TRIALS

%% Data structure transformation
PLAN_LIMITS_UPDATE --> |mutates| PLAN_LIMITS
PLAN_LIMITS_API --> |exposes| PLAN_LIMITS

%% Portal/External
PORTAL_CLIENT --> |requested by| CREATE_ADDON_LEAD
PORTAL_CLIENT --> |requested by| CREATE_LEAD
PORTAL_CLIENT --> |requested by| FETCH_PLANS

%% Worker + Async
APPLY_TRIAL_WORKER --> |processes| BASE_APPLY_TRIAL

%% UserType Concern
HAS_USER_TYPE --> |included in| PLAN

%% Grouping logical relationships via subgraphs
%% Logical clusters are shown using subgraphs already above

%% --------------- END of GRAPH ---------------
```