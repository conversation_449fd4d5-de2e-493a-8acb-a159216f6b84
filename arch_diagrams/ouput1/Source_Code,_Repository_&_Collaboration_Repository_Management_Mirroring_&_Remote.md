```mermaid
flowchart TD
  %% Domain: Source Code, Repository & Collaboration / Repository Management / Mirroring & Remote
  %% Vertical Layout
  %% Color legend:
  %% - Core domain files: pastel blue #D4F1F9
  %% - Supporting/utility files: pastel yellow #FFF8DC
  %% - Data structure files: pastel green #E0F8E0
  %% - Error handling files: pastel red #FFE4E1
  %% - Initialization/setup files: pastel purple #E6E6FA
  %% - Logical groupings/subgraphs: very light gray #F8F8F8 with pastel borders

  %% ---- GROUP: Remote Mirror Models, Data, and Configuration (Core) ---- %%
  subgraph "Remote Mirrors: Models, Data Structures, and Configuration" [" "]
    direction TB
    style "Remote Mirrors: Models, Data Structures, and Configuration" fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,corner-radius:8

    A1[["app/models/remote_mirror.rb\\nCore model: RemoteMirror domain object"]]
    style A1 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:16
    A2[["app/models/concerns/mirror_authentication.rb\\nConcern: Credentials, SSH, token logic for mirrors"]]
    style A2 fill:#FFF8DC,stroke:#D4F1F9,stroke-width:1,rx:16
    A3[["app/models/remote_mirrors/attributes.rb\\nAttribute whitelisting for RemoteMirror CE"]]
    style A3 fill:#E0F8E0,stroke:#D4F1F9,stroke-width:1,rx:16
    A4[["ee/app/models/ee/remote_mirror.rb\\nEE Extension: Enhanced validations, branch regex logic"]]
    style A4 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:16
    A5[["ee/app/models/ee/remote_mirrors/attributes.rb\\nEE Attribute extension for Remote Mirrors"]]
    style A5 fill:#E0F8E0,stroke:#D4F1F9,stroke-width:1,rx:16
    A6[["ee/app/models/concerns/mirror_configuration.rb\\nEE Concern: Mirror branch settings logic"]]
    style A6 fill:#FFF8DC,stroke:#D4F1F9,stroke-width:1,rx:16

    %% Relationships
    A1 --- A2
    A1 --- A3
    A1 --- A4
    A4 --- A5
    A4 --- A6
    A4 -- extends --> A1
    A5 -- extends --> A3
    A6 -- included in --> A4
  end

  %% ---- GROUP: Remote Mirror Services Business Logic ---- %%
  subgraph "Remote Mirrors: Services" [" "]
    direction TB
    style "Remote Mirrors: Services" fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,corner-radius:8

    B1[["app/services/remote_mirrors/create_service.rb\\nCreates RemoteMirror, attribute filtering, access check"]]
    style B1 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:16
    B2[["app/services/remote_mirrors/destroy_service.rb\\nDestroys RemoteMirror, validations, error handling"]]
    style B2 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:16
    B3[["app/services/remote_mirrors/sync_service.rb\\nOrchestrates repository synchronization"]]
    style B3 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:16

    %% Service interrelations
    B1 -. uses .-> A3
    B1 --> A1
    B2 --> A1
    B3 --> A1
    B2 -. uses .-> A4
    B3 -. may use .-> A4

    %% EE attribute extensions used for filtering in EE
    B1 -. uses .-> A5
    B2 -. uses .-> A5
  end

  %% ---- GROUP: API, Serialization, and Entities (Presentation Interfaces) ---- %%
  subgraph "Remote Mirrors: API & Serialization" [" "]
    direction TB
    style "Remote Mirrors: API & Serialization" fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,corner-radius:8

    C1[["lib/api/remote_mirrors.rb\\nREST API: surface create/destroy/sync"]]
    style C1 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:16
    C2[["lib/api/entities/remote_mirror.rb\\nAPI entity: CE representation of remote mirror"]]
    style C2 fill:#E0F8E0,stroke:#D4F1F9,stroke-width:1,rx:16
    C3[["app/serializers/remote_mirror_entity.rb\\nSerialization: Exposes attributes to web/API"]]
    style C3 fill:#E0F8E0,stroke:#D4F1F9,stroke-width:1,rx:16
    C4[["lib/api/entities/pull_mirror.rb\\nAPI entity: EE pull mirror representation"]]
    style C4 fill:#E0F8E0,stroke:#D4F1F9,stroke-width:1,rx:16
    C5[["ee/app/serializers/ee/project_mirror_entity.rb\\nEE Extension: extra fields for serialization"]]
    style C5 fill:#E0F8E0,stroke:#D4F1F9,stroke-width:1,rx:16
    C6[["lib/api/helpers/remote_mirrors_helpers.rb\\nParam validation for mirror endpoints"]]
    style C6 fill:#FFF8DC,stroke:#D4F1F9,stroke-width:1,rx:16

    %% API-Entity relationships
    C1 --> C2
    C1 --> C3
    C2 --> A1
    C3 --> A1
    C4 --> A1
    C1 -- uses params helpers --> C6

    C3 -. extends .-> C5
    C4 -. EE Variant .-> C5
    C1 -. EE Variant .-> C4
  end

  %% ---- GROUP: Mirroring Actions Scheduling & Background Processing ---- %%
  subgraph "Remote Mirrors: Background Jobs & Workers" [" "]
    direction TB
    style "Remote Mirrors: Background Jobs & Workers" fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,corner-radius:8

    D1[["app/workers/remote_mirror_notification_worker.rb\\nSends notifications about mirror failures"]]
    style D1 fill:#E6E6FA,stroke:#D4F1F9,stroke-width:2,rx:16
    D2[["app/workers/repository_update_remote_mirror_worker.rb\\nSchedules mirror updates, retry handling"]]
    style D2 fill:#E6E6FA,stroke:#D4F1F9,stroke-width:2,rx:16
    D3[["ee/app/workers/repository_update_mirror_worker.rb\\nEE: Handles updating project mirror and metrics"]]
    style D3 fill:#E6E6FA,stroke:#D4F1F9,stroke-width:2,rx:16
    D4[["ee/app/workers/pull_mirrors/reenable_configuration_worker.rb\\nEE: Re-enables pull mirror config on event"]]
    style D4 fill:#E6E6FA,stroke:#D4F1F9,stroke-width:2,rx:16

    %% Worker relationships
    D2 --> B3
    D2 --> A1
    D1 --> A1
    D3 --> A4
    D4 --> A4
    D1 -. notification .-> F1

  end

  %% ---- GROUP: Mailers / Notification / Error & Status Reporting ---- %%
  subgraph "Remote Mirrors: Notification & Mailers" [" "]
    direction TB
    style "Remote Mirrors: Notification & Mailers" fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,corner-radius:8

    F1[["app/mailers/emails/remote_mirrors.rb\\nSends email notifications on mirror update issues"]]
    style F1 fill:#E6E6FA,stroke:#D4F1F9,stroke-width:2,rx:16
    F2[["ee/app/mailers/ee/emails/projects.rb\\nEE: Specialized mirror status/project notifications"]]
    style F2 fill:#E6E6FA,stroke:#D4F1F9,stroke-width:2,rx:16
    F3[["ee/app/services/ee/notification_service.rb\\nEE: Notification hooks for mirror events"]]
    style F3 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:16

    %% Mailer relationships
    F1 --> A1
    F2 --> A4
    F3 --> F2
    F3 -- uses --> F1
    F3 -- triggers jobs --> D1
  end

  %% ---- GROUP: Mirroring UI and Controllers Interaction/Presentation ---- %%
  subgraph "Remote Mirrors: Controllers & Helpers" [" "]
    direction TB
    style "Remote Mirrors: Controllers & Helpers" fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,corner-radius:8

    E1[["app/controllers/projects/mirrors_controller.rb\\nWeb controller: manages mirror configuration/update actions"]]
    style E1 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:16
    E2[["ee/app/controllers/ee/projects/mirrors_controller.rb\\nEE controller extension: logic for pull mirrors, EE params"]]
    style E2 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:16
    E3[["ee/app/helpers/ee/mirror_helper.rb\\nEE helpers for UI, state messages, mirror form"]]
    style E3 fill:#FFF8DC,stroke:#D4F1F9,stroke-width:1,rx:16

    %% Controller/helper relationships
    E1 --> B1
    E1 --> B2
    E1 --> B3
    E1 --> A1
    E2 -- extends --> E1
    E2 --> A4
    E2 --> A5
    E2 --> E3
    E3 --> E1
    E3 --> A4
    E1 -. uses .-> C3
  end

  %% ---- GROUP: Automated Git Interactions Abstractions over Git ---- %%
  subgraph "Mirroring & Remote: Git and Storage Integration" [" "]
    direction TB
    style "Mirroring & Remote: Git and Storage Integration" fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,corner-radius:8

    G1[["lib/gitlab/git/remote_mirror.rb\\nGit abstraction: Handles Git operations with mirrors"]]
    style G1 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:16
    G2[["lib/gitlab/gitaly_client/remote_service.rb\\nIntegration with Gitaly: fetch, update remote mirrors"]]
    style G2 fill:#FFF8DC,stroke:#D4F1F9,stroke-width:1,rx:16
    G3[["lib/gitlab/git/wraps_gitaly_errors.rb\\nError wrapping of gRPC/Gitaly operations"]]
    style G3 fill:#FFE4E1,stroke:#D4F1F9,stroke-width:1,rx:16
    G4[["lib/gitlab/git/cross_repo.rb\\nSupports mirroring between multiple repositories"]]
    style G4 fill:#FFF8DC,stroke:#D4F1F9,stroke-width:1,rx:16

    G1 --> G2
    G2 --> G3
    G1 --> G3
    A1 --> G1
    A4 --> G1
    B3 --> G2
    B1 -. uses .-> G4
    G1 -- used for data transfer --> A1
    G1 -- used for data transfer --> A4
  end

  %% ---- GROUP: Supporting Infrastructure/Utilities for Mirroring ---- %%
  subgraph "Supporting & Utility Files for Mirroring" [" "]
    direction TB
    style "Supporting & Utility Files for Mirroring" fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,corner-radius:8

    U1[["app/services/batched_git_ref_updates/cleanup_scheduler_service.rb\\nBatched update job scheduling for mirrors"]]
    style U1 fill:#FFF8DC,stroke:#FFF8DC,stroke-width:1,rx:16
    U2[["app/services/batched_git_ref_updates/project_cleanup_service.rb\\nCleanup for batched Git ref updates"]]
    style U2 fill:#FFF8DC,stroke:#FFF8DC,stroke-width:1,rx:16

    %% Link to core
    U1 --> A1
    U2 --> A1
    D2 -- may trigger cleanup --> U2
  end

  %% ---- GROUP: Domain-Specific Errors and Policies ---- %%
  subgraph "Error Handling & Policies" [" "]
    direction TB
    style "Error Handling & Policies" fill:#F8F8F8,stroke:#FFE4E1,stroke-width:2,corner-radius:8

    X1[["app/controllers/concerns/registry/connection_errors_handler.rb\\nCatches Faraday/registry connection errors"]]
    style X1 fill:#FFE4E1,stroke:#FFE4E1,stroke-width:2,rx:16
    X2[["app/policies/integration_policy.rb\\nDomain policy for integrations"]]
    style X2 fill:#FFE4E1,stroke:#FFE4E1,stroke-width:2,rx:16

    X1 -. used for .-> E1
    X1 -. used for .-> E2
    X2 -. used for .-> E1
    X2 -. used for .-> C1
  end

  %% ---- GROUP: Pull Mirror EE Data & Services ---- %%
  subgraph "Pull Mirror (EE): Data & Core Services" [" "]
    direction TB
    style "Pull Mirror EE: Data & Core Services" fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2,corner-radius:8

    PM1[["ee/app/models/repositories/pull_mirrors/attributes.rb\\nAllowed EE pull mirror attributes"]]
    style PM1 fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rx:16
    PM2[["ee/app/services/repositories/pull_mirrors/update_service.rb\\nEE: Logic for updating pull mirrors"]]
    style PM2 fill:#D4F1F9,stroke:#E0F8E0,stroke-width:2,rx:16
    PM3[["ee/app/services/start_pull_mirroring_service.rb\\nEE: Service orchestrating scheduled pull mirror"]]
    style PM3 fill:#D4F1F9,stroke:#E0F8E0,stroke-width:2,rx:16

    PM2 --> PM1
    PM3 --> PM2
    PM1 --> A4
  end

  %% ---- GROUP: Test and QA Utilities ---- %%
  subgraph "QA and Test Support" [" "]
    direction TB
    style "QA and Test Support" fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2,corner-radius:8

    Q1[["spec/support/helpers/features/mirroring_helpers.rb\\nHelpers for UI tests on mirroring"]]
    style Q1 fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rx:16
    Q2[["spec/features/projects/remote_mirror_spec.rb\\nFeature specs for remote mirror behavior"]]
    style Q2 fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rx:16
    Q3[["spec/workers/repository_update_remote_mirror_worker_spec.rb\\nSpec for worker scheduling mirror updates"]]
    style Q3 fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rx:16

    Q2 -- uses .-> Q1
    Q3 -- tests .-> D2
    Q2 -- tests .-> E1
    Q2 -- tests .-> A1
  end

  %% ---- GROUP: Mirroring-Related UI QA page object QA/EE ---- %%
  subgraph "QA: Mirroring UI Page Object (QA/EE)" [" "]
    direction TB
    style "QA: Mirroring UI Page Object QA/EE" fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2,corner-radius:8

    QA1[["qa/qa/page/project/settings/mirroring_repositories.rb\\nPage object: projects > settings > mirroring repos"]]
    style QA1 fill:#E6E6FA,stroke:#E6E6FA,stroke-width:1,rx:16
    QA1_ee[["qa/qa/ee/page/project/settings/mirroring_repositories.rb\\nEE Page object extension"]]
    style QA1_ee fill:#E6E6FA,stroke:#E6E6FA,stroke-width:1,rx:16

    QA1_ee -- extends --> QA1
    Q2 -- uses UI page --> QA1
    Q2 -- uses UI page --> QA1_ee
  end

  %% ---- GROUP: Cross-cutting: Repository Replication & Mirroring Orchestration ---- %%
  subgraph "Repository Replication & Orchestration" [" "]
    direction TB
    style "Repository Replication & Orchestration" fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,corner-radius:8

    R1[["app/services/repositories/replicate_service.rb\\nOrchestrates repository replication, checksum, consistency"]]
    style R1 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:16
    R2[["ee/app/services/projects/update_mirror_service.rb\\nEE: Handles updating project mirror and resolving divergence"]]
    style R2 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:16

    R1 --> G1
    R2 --> A4
    R2 --> G1
    R2 -- used by --> D3
  end

  %% ---- DEPENDENCY LINES BETWEEN GROUPS (INTER-GROUP) ---- %%

  %% Services depend on models and attributes
  B1 --> A1
  B2 --> A1
  B3 --> A1

  %% Controllers connect to services
  E1 --> B1
  E1 --> B2
  E1 --> B3
  E2 -- extends --> E1

  %% API & serialization entities depend on models
  C2 --> A1
  C3 --> A1
  C5 --> A4

  %% Workers depend on services and models
  D2 --> B3
  D2 --> A1
  D3 --> R2

  %% UI helpers depend on EE components
  E3 --> A4
  E3 --> C5

  %% Main process: UI/Controllers -> Services -> Models/Git/Jobs/Workers
  E1 --> D2
  E2 --> D3

  %% Pull mirror EE services and data
  PM2 --> PM1
  PM3 --> PM2
  PM2 --> A4

  %% Replication orchestrates with core models and git abstraction
  R1 --> A1
  R1 --> G1

  %% Mailers are triggered by workers, services
  D1 --> F1
  D3 --> F2
  F3 --> F1

  %% TEST/QA LINES
  Q2 -- validates --> E1
  Q3 -- validates --> D2
  Q2 -- validates --> A1
  QA1 -- supports --> Q2

  %% Error Handling Policies connected to Controllers and API
  X1 -. handles errors for .-> E1
  X2 -. policy for .-> C1

  %% Batched git ref update services support cleanup and update process
  U1 --> U2
  U1 --> A1
  U2 --> A1

  %% END OF GRAPH %%
```