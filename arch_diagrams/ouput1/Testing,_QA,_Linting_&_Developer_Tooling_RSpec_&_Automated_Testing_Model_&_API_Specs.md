```mermaid
flowchart TD
  %% Subgraphs for logical groupings - pastel gray with pastel borders
  %% Core areas: Model Specs, API Specs, GraphQL API, Mutations, Data Structures, Test Helpers, Migrations
  %% Colors:
  %% - Core: pastel blue #D4F1F9
  %% - Utility/support: pastel yellow #FFF8DC
  %% - Data structure/spec helpers: pastel green #E0F8E0
  %% - Error handling: pastel red #FFE4E1
  %% - Initialization/setup: pastel purple #E6E6FA
  %% - Subgraph bg: #F8F8F8
  %% - Subgraph border: use group color

  %% --------------------------------------------------------------------------
  %% MODEL SPECIFICATIONS (Testing model logic and domain rules)
  subgraph Model_Specs["Model Specifications" ]
    direction TB
    style Model_Specs fill:#F8F8F8,stroke:#ADD8E6,stroke-width:2

    mm1["scoped_label_set_spec.rb":::core]
    mm2["click_house_model_spec.rb":::core]
    mm3["remote_mirror_spec.rb":::core]
    mm4["burndown_spec.rb":::core]
    mm5["repository_spec.rb":::core]
    mm6["approval_wrapped_rule_spec.rb":::core]
    mm7["application_setting_spec.rb":::core]
    mm8["concerns/elastic/repositories_search_spec.rb":::core]
    mm9["concerns/elastic/projects_search_spec.rb":::core]
    mm10["concerns/elastic/application_versioned_search_spec.rb":::core]
    mm11["geo/every_geo_event_spec.rb":::core]
    mm12["concerns/approver_migrate_hook_spec.rb":::core]
    mm13["gitlab_subscriptions/trial_status_spec.rb":::core]
    mm14["virtual_registries/packages/maven/cache/entry_spec.rb":::core]
    mm15["hooks/group_hook_spec.rb":::core]
    mm16["ee/concerns/vulnerability_finding_helpers_spec.rb":::core]
    mm17["concerns/deprecated_approvals_before_merge_spec.rb":::core]
    mm18["saml_group_link_spec.rb":::core]
    mm19["incident_management/oncall_shift_spec.rb":::core]
    mm20["ee/preloaders/group_policy_preloader_spec.rb":::core]
    mm21["approval_state_spec.rb":::core]
    mm22["elasticsearch_indexed_namespace_spec.rb":::core]
    mm23["ee/groups/feature_setting_spec.rb":::core]
    mm24["concerns/identity_verifiable_spec.rb":::core]
    mm25["merge_trains/car_spec.rb":::core]
    mm26["namespaces/free_user_cap/enforcement_spec.rb":::core]
    mm27["license_spec.rb":::core]
    mm28["security/vulnerability_management_policy_rule_spec.rb":::core]
    mm29["virtual_registries/packages/maven/registry_upstream_spec.rb":::core]
    mm30["epic/related_epic_link_spec.rb":::core]
    mm31["ci/instance_runner_failed_jobs_spec.rb":::core]
    mm32["ci/minutes/notification_spec.rb":::core]
    mm33["vulnerabilities/read_spec.rb":::core]
    mm34["ee/namespace_spec.rb":::core]
    mm35["namespaces/storage/root_size_spec.rb":::core]
    mm36["ee/preloaders/project_policy_preloader_spec.rb":::core]
    mm37["concerns/epic_tree_sorting_spec.rb":::core]
    mm38["ee/description_version_spec.rb":::core]
  end

  %% --------------------------------------------------------------------------
  %% MIGRATION TESTS
  subgraph Migration_Specs["Migration/Registry Management" ]
    direction TB
    style Migration_Specs fill:#F8F8F8,stroke:#ADD8E6,stroke-width:2
    m1["migrations/geo/migrate_ci_job_artifacts_to_separate_registry_spec.rb":::core]
  end

  %% --------------------------------------------------------------------------
  %% API REQUEST SPECS
  subgraph API_Specs["API Request Specs" ]
    direction TB
    style API_Specs fill:#F8F8F8,stroke:#ADD8E6,stroke-width:2
    a1["requests/api/graphql/issues_spec.rb":::core]
    a2["requests/api/graphql/project/issues_spec.rb":::core]
    a3["requests/api/files_spec.rb":::core]
    a4["requests/api/graphql/group/epic/epic_children_spec.rb":::core]
    a5["requests/api/graphql/boards/board_lists_query_spec.rb":::core]
    a6["requests/api/graphql/boards/epic_board_list_epics_query_spec.rb":::core]
    a7["requests/api/project_mirror_spec.rb":::core]
    a8["requests/api/gitlab_subscriptions/api/internal/namespaces_spec.rb":::core]
    a9["requests/api/graphql/project/merge_requests_spec.rb":::core]
    a10["requests/api/merge_request_approvals_spec.rb":::core]
    a11["requests/api/resource_iteration_events_spec.rb":::core]
    a12["requests/api/project_import_spec.rb":::core]
    a13["requests/api/license_spec.rb":::core]
    a14["requests/api/compliance_external_controls_spec.rb":::core]
    a15["requests/api/boards_spec.rb":::core]
    a16["requests/api/internal/kubernetes_spec.rb":::core]
    a17["requests/api/usage_data_spec.rb":::core]
    a18["requests/api/group_hooks_spec.rb":::core]
    a19["requests/api/epic_issues_spec.rb":::core]
    a20["requests/api/ci/runner/jobs_put_spec.rb":::core]
    a21["requests/api/ci/runner/jobs_trace_spec.rb":::core]
    a22["requests/api/merge_requests_spec.rb":::core]
  end

  %% --------------------------------------------------------------------------
  %% GRAPHQL API: DOMAIN QUERY CLUSTERS logical groupings by feature
  subgraph GraphQL_Aggregate_Queries["GraphQL Aggregate Queries/Features"]
    direction TB
    style GraphQL_Aggregate_Queries fill:#F8F8F8,stroke:#ADD8E6,stroke-width:2

    gq1["requests/api/graphql/issues_spec.rb":::core]
    gq2["requests/api/graphql/project/issues_spec.rb":::core]
    gq3["requests/api/graphql/group/epic/epic_children_spec.rb":::core]
    gq4["requests/api/graphql/project/merge_requests_spec.rb":::core]
    gq5["requests/api/graphql/group/epic/epic_ancestors_spec.rb":::core]
    gq6["requests/api/graphql/group/epics_spec.rb":::core]
    gq7["requests/api/graphql/boards/epic_boards_query_spec.rb":::core]
    gq8["requests/api/graphql/boards/epic_lists_query_spec.rb":::core]
    gq9["requests/api/graphql/board/epic_board_list_epics_query_spec.rb":::core]
    gq10["requests/api/graphql/boards/boards_query_spec.rb":::core]
    gq11["requests/api/graphql/iterations/iterations_spec.rb":::core]
    gq12["requests/api/graphql/project/work_items_spec.rb":::core]
    gq13["requests/api/graphql/group/work_items_spec.rb":::core]
    gq14["requests/api/graphql/group/epic/epic_issues_spec.rb":::core]
    gq15["requests/api/graphql/epics/epics_spec.rb":::core]
    gq16["requests/api/graphql/namespace/projects_spec.rb":::core]
    gq17["requests/api/graphql/projects/ai_xray_reports_spec.rb":::core]
    gq18["requests/api/graphql/groups/saved_reply_spec.rb":::core]
    gq19["requests/api/graphql/group_query_spec.rb":::core]
    gq20["requests/api/graphql/member_role/instance_member_roles_spec.rb":::core]
    gq21["requests/api/graphql/member_role/dependent_security_policies_spec.rb":::core]
    gq22["requests/api/graphql/member_role/single_member_role_spec.rb":::core]
    gq23["requests/api/graphql/member_role/group_member_role_spec.rb":::core]
    gq24["requests/api/graphql/members/instance_standard_roles_spec.rb":::core]
    gq25["requests/api/graphql/members/group_standard_roles_spec.rb":::core]
    gq26["requests/api/graphql/group/standard_roles_spec.rb":::core]
    gq27["requests/api/graphql/group/labels_query_spec.rb":::core]
    gq28["requests/api/graphql/issuables/custom_fields_spec.rb":::core]
    gq29["requests/api/graphql/project/sbom/dependencies_spec.rb":::core]
    gq30["requests/api/graphql/group/sbom/dependency_aggregation_spec.rb":::core]
    gq31["requests/api/graphql/project/value_stream_dashboard_usage_overview_spec.rb":::core]
    gq32["requests/api/graphql/group/value_stream_dashboard_usage_overview_spec.rb":::core]
    gq33["requests/api/graphql/milestone_spec.rb":::core]
  end

  %% --------------------------------------------------------------------------
  %% MUTATION: DOMAIN API MUTATIONS CRUD, workflow
  subgraph GraphQL_Mutations["GraphQL Mutations CRUD/Workflow"]
    direction TB
    style GraphQL_Mutations fill:#F8F8F8,stroke:#ADD8E6,stroke-width:2

    mu1["requests/api/graphql/mutations/epics/create_spec.rb":::core]
    mu2["requests/api/graphql/mutations/epics/update_spec.rb":::core]
    mu3["requests/api/graphql/mutations/epics/set_subscription_spec.rb":::core]
    mu4["requests/api/graphql/mutations/epic_tree/reorder_spec.rb":::core]
    mu5["requests/api/graphql/mutations/issues/set_weight_spec.rb":::core]
    mu6["requests/api/graphql/mutations/issues/set_epic_spec.rb":::core]
    mu7["requests/api/graphql/mutations/issues/promote_to_epic_spec.rb":::core]
    mu8["requests/api/graphql/mutations/incidents/oncall_rotation/update_spec.rb":::core]
    mu9["requests/api/graphql/mutations/boards/epic_boards/create_spec.rb":::core]
    mu10["requests/api/graphql/mutations/boards/epic_boards/update_spec.rb":::core]
    mu11["requests/api/graphql/mutations/boards/epic_boards/destroy_spec.rb":::core]
    mu12["requests/api/graphql/mutations/boards/epic_boards/epic_move_list_spec.rb":::core]
    mu13["requests/api/graphql/mutations/boards/epics/create_spec.rb":::core]
    mu14["requests/api/graphql/mutations/boards/issues/issue_move_list_spec.rb":::core]
    mu15["requests/api/graphql/mutations/boards/lists/update_limit_metrics_spec.rb":::core]
    mu16["requests/api/graphql/mutations/boards/create_spec.rb":::core]
    mu17["requests/api/graphql/mutations/analytics/devops_adoption/enabled_namespaces/enable_spec.rb":::core]
    mu18["requests/api/graphql/mutations/analytics/devops_adoption/enabled_namespaces/bulk_enable_spec.rb":::core]
    mu19["requests/api/graphql/mutations/analytics/devops_adoption/enabled_namespaces/disable_spec.rb":::core]
    mu20["requests/api/graphql/mutations/compliance_management/frameworks/create_spec.rb":::core]
    mu21["requests/api/graphql/mutations/compliance_management/frameworks/update_spec.rb":::core]
    mu22["requests/api/graphql/mutations/compliance_management/frameworks/destroy_spec.rb":::core]
    mu23["requests/api/graphql/mutations/compliance_management/compliance_framework/compliance_requirements/create_spec.rb":::core]
    mu24["requests/api/graphql/mutations/compliance_management/compliance_framework/compliance_requirements/update_spec.rb":::core]
    mu25["requests/api/graphql/mutations/compliance_management/compliance_framework/compliance_requirements/destroy_spec.rb":::core]
    mu26["requests/api/graphql/mutations/compliance_management/compliance_framework/compliance_requirements_controls/create_spec.rb":::core]
    mu27["requests/api/graphql/mutations/compliance_management/compliance_framework/compliance_requirements_controls/update_spec.rb":::core]
    mu28["requests/api/graphql/mutations/compliance_management/compliance_framework/compliance_requirements_controls/destroy_spec.rb":::core]
    mu29["requests/api/graphql/mutations/projects/set_compliance_framework_spec.rb":::core]
    mu30["requests/api/graphql/mutations/projects/update_compliance_frameworks_spec.rb":::core]
    mu31["requests/api/graphql/mutations/vulnerabilities/bulk_dismiss_spec.rb":::core]
    mu32["requests/api/graphql/mutations/vulnerabilities/create_issue_link_spec.rb":::core]
    mu33["requests/api/graphql/mutations/vulnerabilities/destroy_external_issue_link_spec.rb":::core]
    mu34["requests/api/graphql/mutations/vulnerabilities/create_issue_spec.rb":::core]
    mu35["requests/api/graphql/mutations/requirements_management/update_requirement_spec.rb":::core]
    mu36["requests/api/graphql/mutations/requirements_management/create_requirement_spec.rb":::core]
    mu37["requests/api/graphql/mutations/remote_development/workspace_operations/create_spec.rb":::core]
    mu38["requests/api/graphql/mutations/remote_development/workspace_operations/update_spec.rb":::core]
    mu39["requests/api/graphql/mutations/remote_development/organization_cluster_agent_mapping_operations/create_spec.rb":::core]
    mu40["requests/api/graphql/mutations/quality_management/test_cases/create_spec.rb":::core]
    mu41["requests/api/graphql/mutations/incident_management/escalation_policy/create_spec.rb":::core]
    mu42["requests/api/graphql/mutations/projects/initialize_product_analytics_spec.rb":::core]
    mu43["requests/api/graphql/mutations/merge_requests/destroy_requested_changes_spec.rb":::core]
    mu44["requests/api/graphql/mutations/iterations/delete_spec.rb":::core]
    mu45["requests/api/graphql/mutations/iterations/create_spec.rb":::core]
    mu46["requests/api/graphql/mutations/iterations/update_spec.rb":::core]
    mu47["requests/api/graphql/mutations/iterations/cadences/create_spec.rb":::core]
    mu48["requests/api/graphql/mutations/iterations/cadences/update_spec.rb":::core]
    mu49["requests/api/graphql/mutations/iterations/cadences/destroy_spec.rb":::core]
    mu50["requests/api/graphql/mutations//create/note_spec.rb":::core]
    mu51["requests/api/graphql/mutations/ci/runner/create_spec.rb":::core]
    mu52["requests/api/graphql/mutations/ci/runner/update_spec.rb":::core]
    mu53["requests/api/graphql/mutations/geo/registries/update_spec.rb":::core]
    mu54["requests/api/graphql/mutations/geo/registries/bulk_update_spec.rb":::core]
    mu55["requests/api/graphql/mutations/dast_site_profiles/update_spec.rb":::core]
    mu56["requests/api/graphql/mutations/dast_scanner_profiles/update_spec.rb":::core]
    mu57["requests/api/graphql/mutations/projects/lock_path_spec.rb":::core]
    mu58["requests/api/graphql/mutations/products/export_requirements_spec.rb":::core]
    mu59["requests/api/graphql/mutations/security/finding/create_issue_spec.rb":::core]
    mu60["requests/api/graphql/mutations/security_policy/create_security_policy_project_spec.rb":::core]
    mu61["requests/api/graphql/mutations/security_policy/assign_security_policy_project_spec.rb":::core]
    mu62["requests/api/graphql/mutations/security_policy/commit_scan_execution_policy_spec.rb":::core]
    mu63["requests/api/graphql/mutations/security_policy/unassign_security_policy_project_spec.rb":::core]
    mu64["requests/api/graphql/mutations/merge_requests/set_assignees_spec.rb":::core]
    mu65["requests/api/graphql/mutations/merge_requests/update_approval_rule_spec.rb":::core]
    mu66["requests/api/graphql/mutations/work_items/update_spec.rb":::core]
    mu67["requests/api/graphql/mutations/users/member_roles/assign_spec.rb":::core]
    mu68["requests/api/graphql/mutations/users/abuse/namespace_bans/destroy_spec.rb":::core]
  end

  %% --------------------------------------------------------------------------
  %% DATA STRUCTURE & TEST UTILITY SPECS
  subgraph Spec_Helpers["Test Utilities and Shared Data Structures"]
    direction TB
    style Spec_Helpers fill:#F8F8F8,stroke:#90EE90,stroke-width:2

    th1["spec/support/shared_examples/epic_linkable_spec.rb":::data]
    th2["spec/support/shared_examples/graphql_issue_list_request_spec.rb":::data]
    th3["spec/support/shared_examples/value_stream_analytics_query_spec.rb":::data]
    th4["spec/support/shared_contexts/group_and_project_boards_query_context.rb":::data]
    th5["spec/support/shared_examples/sorted_paginated_query_spec.rb":::data]
    th6["spec/support/test_helpers/graphql_helpers.rb":::data]
  end

  %% --------------------------------------------------------------------------
  %% LOGICAL RELATIONSHIPS (edges)
  %% 1. Model specs underpin many API specs via expectation on DB structures and behaviors
  %% 2. API specs test both direct API and GraphQL through Model/Domain behaviors
  %% 3. GraphQL aggregates core behaviors through model and interacts with mutation specs
  %% 4. Mutation specs interoperate with the domain via model behaviors to test write
  %% 5. Spec helpers provide composable shared logic and domain data transform support

  %% MODEL <==> API
  Model_Specs -->|verifies domain logic| API_Specs

  %% MODEL <==> GRAPHQL
  Model_Specs -->|model/relationship assertions| GraphQL_Aggregate_Queries

  %% MODEL <==> MUTATION
  Model_Specs -->|writable flows| GraphQL_Mutations

  %% API <==> GRAPHQL
  API_Specs -->|calls via GraphQL endpoint| GraphQL_Aggregate_Queries
  API_Specs -->|mutation interaction| GraphQL_Mutations

  %% GRAPHQL <==> MUTATIONS
  GraphQL_Aggregate_Queries -->|uses for mutations| GraphQL_Mutations
  GraphQL_Mutations -->|persist/write/read| Model_Specs
  GraphQL_Mutations -->|shared data shapes| Spec_Helpers

  %% ALL Specs <-- Spec Helper
  Model_Specs -->|test support| Spec_Helpers
  API_Specs -->|test support| Spec_Helpers
  GraphQL_Aggregate_Queries -->|test support| Spec_Helpers
  GraphQL_Mutations -->|test support| Spec_Helpers

  %% Migrations relate to model and API specs (registry/data)
  Migration_Specs --> Model_Specs
  Migration_Specs --> API_Specs

  %% --------------------------------------------------------------------------
  %% Styling for node classes
  classDef core fill:#D4F1F9,stroke:#B0E0E6,stroke-width:2,color:#000,stroke-dasharray:0,stroke-linecap:round;
  classDef data fill:#E0F8E0,stroke:#90EE90,stroke-width:2,color:#000,stroke-dasharray:0,stroke-linecap:round;
```