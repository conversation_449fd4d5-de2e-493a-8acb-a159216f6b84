```mermaid
flowchart TD
  %% STYLES
  classDef core fill:#D4F1F9,color:#222,stroke:#B9EAF8,stroke-width:2px,stroke-dasharray:0,rx:10,ry:10;
  classDef utility fill:#FFF8DC,color:#222,stroke:#FFE9AD,stroke-width:2px,stroke-dasharray:0,rx:10,ry:10;
  classDef datastruct fill:#E0F8E0,color:#222,stroke:#AEE3AD,stroke-width:2px,stroke-dasharray:0,rx:10,ry:10;
  classDef error fill:#FFE4E1,color:#222,stroke:#F6D2CF,stroke-width:2px,stroke-dasharray:0,rx:10,ry:10;
  classDef init fill:#E6E6FA,stroke:#CFCFFD,color:#222,stroke-width:2px,stroke-dasharray:0,rx:10,ry:10;
  classDef group fill:#F8F8F8,stroke-width:2px,stroke:#E0E9F3,rx:12,ry:12;

%% DOMAIN ROOT
  subgraph LeafDomain["Source Code, Repository & Collaboration / Repository Management / Repository Import / Export"]
    direction TB

%% IMPORT/EXPORT SUBSYSTEM
    subgraph ImportExportSubsystem["Import/Export Core" ]
      direction TB
      IEGitlabModule[Gitlab::ImportExport<br/>Domain Facade]:::core
      IEError[Error Handling<br/>Error]:::error
      IEAttributeCleaner[Attribute Cleaning<br/>AttributeCleaner]:::utility
      IEHashUtil[Hash Utility<br/>HashUtil]:::utility
      IEAfterExportStrategy[After Export Strategy<br/>AfterExportStrategyBuilder]:::utility
      IEDomainUtil[Domain API<br/>Gitlab::ImportExport]:::core
    end
    class ImportExportSubsystem group

%% PROJECT IMPORT/EXPORT
    subgraph ProjectImportExport["Project Import/Export Logic"]
      direction TB
      PIERestorer[Project Tree Restorer<br/>TreeRestorer]:::core
      PIEObjBuilder[Project Object Builder<br/>ObjectBuilder]:::core
      PIEExporter[Export Task<br/>ExportTask]:::core
      PIEExportedMerger[Exported Relations Merger]:::core
      PIERepoSaver[Repo Saver]:::core
      PIEWikiRepoSaver[Wiki Repo Saver]:::core
      PIESnippetsRepoSaver[Snippets Repo Saver]:::core
      PIEStatisticsRestorer[Statistics Restorer]:::utility
      PIEAvatarRestorer[Avatar Restorer]:::utility
      PIEVersionSaver[Version Saver]:::utility
      PIEVersionChecker[Version Checker]:::utility
      PIEReader[Config Reader]:::utility
      PIEUploader[Uploads Saver]:::utility
    end
    class ProjectImportExport group

%% GROUP IMPORT/EXPORT
    subgraph GroupImportExport["Group Import/Export Logic"]
      direction TB
      GIEObjBuilder[Group Object Builder<br/>ObjectBuilder]:::core
      GIERelationFactory[Group Relation Factory]:::utility
    end
    class GroupImportExport group

%% SAMPLE IMPORT/EXPORT FACTORIES
    subgraph SampleImportExport["Sample Import/Export Extensions"]
      direction TB
      SampleTreeRestorer[Sample Tree Restorer]:::core
      SampleRelationTreeRestorer[Sample RelationTreeRestorer]:::utility
      SampleRelationFactory[Sample Relation Factory]:::utility
    end
    class SampleImportExport group

%% DATA STRUCTURE COMPONENTS
    subgraph DataStructures["Data Structures / Utilities"]
      direction TB
      DSInlineHash[Inline Hash]:::datastruct
      DSCommonUtil[ImportExport Test Helpers]:::utility
    end
    class DataStructures group

%% FILE UPLOAD/EXPORT
    subgraph FileUploadExport["File Upload/Export Support"]
      direction TB
      FEUploader[ImportExportUploader]:::core
      FEEUploader[ExportUploader]:::core
      FEFileUploader[FileUploader]:::core
    end
    class FileUploadExport group

%% WORKERS AND PROCESSING
    subgraph ProcessingAndWorkers["Domain Workers / Processing"]
      direction TB
      ProjectExportWorkerNode[ProjectExportWorker]:::init
      ProjectsImportExportRelationExportWorker[RelationExportWorker]:::init
      ProjectsImportExportAfterImportMRsWorker[AfterImportMergeRequestsWorker]:::init
    end
    class ProcessingAndWorkers group

%% BULK IMPORTS/EXPORTS
    subgraph BulkImportExports["Bulk Imports/Exports Services"]
      direction TB
      BulkNdjsonPipeline[Ndjson Pipeline]:::core
      BulkProcessService[BulkImports::ProcessService]:::core
      BulkRepoBundleExportSvc[Repo Bundle Export Service]:::core
      BulkLfsExportSvc[LFS Objects Export Service]:::core
      BulkUploadsExportSvc[Uploads Export Service]:::core
    end
    class BulkImportExports group

%% CSV EXPORT
    subgraph ExportCsv["CSV Export"]
      direction TB
      CsvMapExportFieldsService[Map Export Fields Service]:::core
    end
    class ExportCsv group

%% FINDERS & HELPERS
    subgraph FindersHelpers["Finders & Helpers"]
      direction TB
      ReleasesFinderNode[ReleasesFinder]:::utility
      GroupReleasesFinderNode[GroupReleasesFinder]:::utility
      SelectForProjectAuth[SelectForProjectAuthorization]:::utility
      ProjectMirrorEntityNode[ProjectMirrorEntity]:::utility
      ProjectsTerraformHelper[TerraformHelper]:::utility
    end
    class FindersHelpers group

%% REPOSITORY MANAGEMENT SERVICES
    subgraph RepoManagement["Repository Management/State"]
      direction TB
      RepoModTracker[Modification Tracker]:::utility
      RepoStorageMovable[RepositoryStorageMovable]:::utility
      RepoArchiveCleanUpSvc[Archive Cleanup Service]:::utility
      EEProjectImportState[ProjectImportState EE]:::init
    end
    class RepoManagement group

%% SPECIALIZED IMPORTERS/EXPORTERS
    subgraph SpecializedImportersExporters["Specialized Importers/Exporters"]
      direction TB
      BitbucketPullRequestWorker[ImportPullRequestWorker]:::init
      BitbucketLfsObjectWorker[ImportLfsObjectWorker]:::init
      GithubGistsFinishImportWorker[FinishImportWorker]:::init
    end
    class SpecializedImportersExporters group

%% EE/ENTERPRISE LOGIC
    subgraph EnterpriseLogic["Enterprise Import/Export Logic"]
      direction TB
      EERepoImportWorker[RepositoryImportWorker EE]:::init
      EESegmentedFinalizer[Segmented Export Finalizer]:::utility
      EEPostReceiveService[EE PostReceiveService]:::utility
      EEGitlabProjectsImportService[Gitlab Projects ImportService EE]:::utility
    end
    class EnterpriseLogic group

%% SEARCH/ELASTIC SUPPORT
    subgraph SearchElastic["Search/Elastic & Cleanup"]
      direction TB
      ElasticDeleteProjectWorker[ElasticDeleteProjectWorker]:::init
      SearchProjectsFinder[Search::ProjectsFinder]:::utility
      ElasticMigrationCleanup[Elastic MigrationCleanupService]:::utility
      ElasticDeleteProjectAssociations[Elastic Delete Project AssociationsService]:::utility
      ElasticSnippetClassProxy[Elastic SnippetClassProxy]:::utility
      ElasticAsJSON[Elastic AsJSON]:::utility
      ElasticStateFilter[Elastic StateFilter]:::utility
      ElasticRouting[Elastic Routing]:::utility
      ElasticConfig[Elastic Config]:::utility
      ElasticCommitConfig[Elastic Commit Config]:::utility
      ElasticLegacyReference[Elastic Legacy Reference]:::utility
    end
    class SearchElastic group

%% SPEC & SUPPORT
    subgraph SpecsSupport["Tests & Spec Helpers"]
      direction TB
      ElasticDeleteProjectWorkerSpec[ElasticDeleteProjectWorker spec]:::datastruct
      ImportGitlabProjectsCtlSpec[Import::GitlabProjectsController spec]:::datastruct
      ImportGitlabGroupsCtlSpec[Import::GitlabGroupsController spec]:::datastruct
      ProjectTreeRestorerSpec[Project/TreeRestorer spec]:::datastruct
      BulkNdjsonPipelineSpec[BulkImports::NdjsonPipeline spec]:::datastruct
      ImportExportCommonUtil[Spec: ImportExport::CommonUtil]:::datastruct
    end
    class SpecsSupport group

%% LINKAGES AND INTERACTIONS -- CORE NODES

  IEGitlabModule --> PIEExplorer
  IEGitlabModule --> PIEObjBuilder
  IEGitlabModule --> PIEReader
  IEGitlabModule --> IEAfterExportStrategy
  IEGitlabModule --> IEDomainUtil
  IEGitlabModule --> PIERepoSaver
  IEGitlabModule --> PIEExporter
  IEGitlabModule --> PIEExportedMerger
  IEGitlabModule --> PIEUploader

  PIEUploader --> FEUploader
  PIEUploader --> FEEUploader
  PIERepoSaver --> PIEWikiRepoSaver
  PIERepoSaver --> PIESnippetsRepoSaver
  PIERepoSaver --> PIEObjBuilder

  PIEObjBuilder --> PIEExporter
  PIEObjBuilder --> PIEExportedMerger

  PIEExporter --> PIEExportedMerger
  PIEExporter -- Uses --> PIERepoSaver
  PIEExporter -- Uses --> PIEObjBuilder

  PIEExportedMerger --> PIEReader
  PIEExportedMerger --> PIERepoSaver

  PIEReader --> PIEObjBuilder

  PIEStatisticsRestorer --> PIEReader
  PIEAvatarRestorer --> PIEReader

  PIEVersionSaver --> PIEReader
  PIEVersionChecker --> PIEVersionSaver

  PIEObjBuilder -- Cleans Attributes --> IEAttributeCleaner
  PIEObjBuilder --> DSInlineHash

  PIESnippetsRepoSaver --> PIERepoSaver

  PIEWikiRepoSaver --> PIERepoSaver

  PIEObjBuilder -- Uses --> PIEError
  PIEExporter -- Handles Errors --> PIEError

  PIEReader --> PIEObjBuilder

  PIEObjBuilder -- Cleans Attributes --> IEAttributeCleaner
  PIEObjBuilder -- Utilities --> DSInlineHash

  %% Group Relationships
  GIERelationFactory --> GIEObjBuilder

  %% Sample Import/Export Relationships
  SampleTreeRestorer --> SampleRelationTreeRestorer
  SampleRelationTreeRestorer --> SampleRelationFactory

  SampleTreeRestorer --> PIERestorer
  SampleRelationTreeRestorer --> PIEObjBuilder

  SampleRelationFactory --> PIEObjBuilder

  %% Bulk Import/Export Services
  BulkNdjsonPipeline --> BulkProcessService
  BulkProcessService --> BulkNdjsonPipeline
  BulkProcessService --> BulkRepoBundleExportSvc
  BulkProcessService --> BulkLfsExportSvc
  BulkProcessService --> BulkUploadsExportSvc

  BulkNdjsonPipeline --- PIEObjBuilder

  BulkRepoBundleExportSvc --> BulkNdjsonPipeline
  BulkLfsExportSvc --> BulkNdjsonPipeline
  BulkUploadsExportSvc --> BulkNdjsonPipeline

  %% File Upload/Export
  FEUploader --> FEEUploader
  FEEUploader --> FEFileUploader

  FEUploader --> PIEUploader

  %% Processing / Workers
  ProjectExportWorkerNode --> PIEExporter
  ProjectsImportExportRelationExportWorker --> PIEExporter
  ProjectsImportExportAfterImportMRsWorker --> PIEExporter

  ProjectExportWorkerNode --> IEAfterExportStrategy

  %% CSV Export
  CsvMapExportFieldsService -- Data Mapping --> PIEObjBuilder

  %% Finders & Helpers
  ReleasesFinderNode --> GroupReleasesFinderNode
  ReleasesFinderNode --> SelectForProjectAuth
  GroupReleasesFinderNode --> SelectForProjectAuth
  ProjectMirrorEntityNode --> PIEObjBuilder

  ProjectsTerraformHelper --> PIEObjBuilder

  %% Repo management
  RepoModTracker --> PIEObjBuilder
  RepoStorageMovable --> PIERepoSaver
  RepoArchiveCleanUpSvc --> PIERepoSaver
  EEProjectImportState --> PIEObjBuilder

  %% Specialized Importers
  BitbucketPullRequestWorker --> PIEExporter
  BitbucketLfsObjectWorker --> PIEExporter
  GithubGistsFinishImportWorker --> PIEExporter

  %% EE Logic
  EERepoImportWorker --> PIEExporter
  EESegmentedFinalizer --> PIEExporter
  EEPostReceiveService --> PIEExporter
  EEGitlabProjectsImportService --> PIEExporter

  %% Search/Elastic relationships
  ElasticDeleteProjectWorker --> PIEExporter
  ElasticDeleteProjectWorker --> SearchProjectsFinder
  ElasticDeleteProjectWorker --> ElasticDeleteProjectAssociations
  ElasticDeleteProjectWorker --> ElasticMigrationCleanup

  ElasticMigrationCleanup --> ElasticDeleteProjectAssociations

  ElasticDeleteProjectWorker --> ElasticStateFilter
  ElasticDeleteProjectWorker --> ElasticSnippetClassProxy
  ElasticSnippetClassProxy --> ElasticAsJSON
  ElasticSnippetClassProxy --> ElasticConfig

  ElasticConfig --> ElasticCommitConfig
  ElasticRouting --> ElasticConfig
  ElasticStateFilter --> ElasticConfig
  ElasticLegacyReference --> ElasticConfig

  %% Specs relationships
  ElasticDeleteProjectWorkerSpec --> ElasticDeleteProjectWorker
  ImportGitlabProjectsCtlSpec --> PIEExporter
  ImportGitlabGroupsCtlSpec --> PIEExporter
  ProjectTreeRestorerSpec --> PIERestorer
  BulkNdjsonPipelineSpec --> BulkNdjsonPipeline
  ImportExportCommonUtil --> PIEObjBuilder
  ImportExportCommonUtil --> PIEExporter

  %% Data structure utilities
  DSCommonUtil --> PIEObjBuilder
  DSCommonUtil --> PIEExporter

  %% Utility error
  IEError --> PIEObjBuilder
  IEError --> PIEExporter
  IEError --> BulkNdjsonPipeline

  %% Miscellaneous utilities
  DSInlineHash --> PIEObjBuilder
  DSInlineHash --> BulkNdjsonPipeline

  %% FINAL CONNECTIVE SYMBOLIC LINES FOR DOMAIN SCHEMATICS
  IEGitlabModule -- delegates core domain logic --> PIEObjBuilder
  IEGitlabModule -- delegates data path logic --> PIERepoSaver
  IEGitlabModule -- handles errors via --> IEError

  PIEObjBuilder -- core attribute flow --> PIERepoSaver
  PIEObjBuilder -- builds/rebuilds project domain objects --> PIEExporter
  PIEObjBuilder -- passes config to --> PIEReader
  PIEReader -- enables config discovery for --> PIEExporter

  PIERepoSaver -- handles repository structure for import/export --> PIEExporter
  PIERepoSaver -- can delegate to --> PIEWikiRepoSaver
  PIERepoSaver -- can delegate to --> PIESnippetsRepoSaver

  PIEExporter -- centralizes project import/export actions --> PIEReader
  PIEExporter -- utilizes after_export_strategy from --> IEAfterExportStrategy

  PIEUploader -- handles file upload lifecycle for import/export --> PIEExporter
  PIEUploader -- underpins --> FEUploader

  bulkIMPEdge{{"Bulk Import Data"}}:::datastruct
  bulkIMPEdge -.-> BulkNdjsonPipeline
  bulkIMPEdge -.-> BulkRepoBundleExportSvc
  bulkIMPEdge -.-> BulkLfsExportSvc
  bulkIMPEdge -.-> BulkUploadsExportSvc

  CSVDataEdge{{"CSV Export Mapping"}}:::datastruct
  CSVDataEdge -.-> CsvMapExportFieldsService

  classDef node shape:rounded-rectangle
  class IEGitlabModule,IEAttributeCleaner,IEHashUtil,IEAfterExportStrategy,IEDomainUtil,PIERestorer,PIEObjBuilder,PIEExporter,PIEExportedMerger,PIERepoSaver,PIEWikiRepoSaver,PIESnippetsRepoSaver,PIEStatisticsRestorer,PIEAvatarRestorer,PIEVersionSaver,PIEVersionChecker,PIEReader,PIEUploader,GIEObjBuilder,GIERelationFactory,SampleTreeRestorer,SampleRelationTreeRestorer,SampleRelationFactory,DSInlineHash,DSCommonUtil,FEUploader,FEEUploader,FEFileUploader,ProjectExportWorkerNode,ProjectsImportExportRelationExportWorker,ProjectsImportExportAfterImportMRsWorker,BulkNdjsonPipeline,BulkProcessService,BulkRepoBundleExportSvc,BulkLfsExportSvc,BulkUploadsExportSvc,CsvMapExportFieldsService,ReleasesFinderNode,GroupReleasesFinderNode,SelectForProjectAuth,ProjectMirrorEntityNode,ProjectsTerraformHelper,RepoModTracker,RepoStorageMovable,RepoArchiveCleanUpSvc,EEProjectImportState,BitbucketPullRequestWorker,BitbucketLfsObjectWorker,GithubGistsFinishImportWorker,EERepoImportWorker,EESegmentedFinalizer,EEPostReceiveService,EEGitlabProjectsImportService,ElasticDeleteProjectWorker,SearchProjectsFinder,ElasticMigrationCleanup,ElasticDeleteProjectAssociations,ElasticSnippetClassProxy,ElasticAsJSON,ElasticStateFilter,ElasticRouting,ElasticConfig,ElasticCommitConfig,ElasticLegacyReference,ElasticDeleteProjectWorkerSpec,ImportGitlabProjectsCtlSpec,ImportGitlabGroupsCtlSpec,ProjectTreeRestorerSpec,BulkNdjsonPipelineSpec,ImportExportCommonUtil,IEError node
```