```mermaid
flowchart TD
  %% COLOR DEFINITIONS
  %% Core domain files: #D4F1F9 (pastel blue)
  %% Supporting/utility files: #FFF8DC (pastel yellow)
  %% Data structure files: #E0F8E0 (pastel green)
  %% Error handling files: #FFE4E1 (pastel red)
  %% Initialization/setup files: #E6E6FA (pastel purple)
  %% Groupings: #F8F8F8 with pastel blue borders

  %% SUBGRAPH: Domain Core Concepts
  subgraph core["Work Item Core Concepts" ]
    direction TB
    style core fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2
    workitem["WorkItem":::core_domain]
    workitemtype["Type":::core_domain]
    widgetdef["WidgetDefinition":::data_structure]
    typespolicy["TypePolicy":::supporting]
    workitempolicy["WorkItemPolicy":::supporting]
    workitempresenter["WorkItemPresenter":::supporting]
  end

  %% SUBGRAPH: Work Item Widgets
  subgraph widgets["Work Item Widgets"]
    direction TB
    style widgets fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2
    widgetsbase["Widgets::Base":::core_domain]
    labels["Widgets::Labels":::core_domain]
    milestone["Widgets::Milestone":::core_domain]
    startdue["Widgets::StartAndDueDate":::core_domain]
    linkedresources["Widgets::LinkedResources":::core_domain]
    awardemoji["Widgets::AwardEmoji":::core_domain]
    crmcontacts["Widgets::CrmContacts":::core_domain]
    linkeditems["Widgets::LinkedItems":::core_domain]
    hierarchy["Widgets::Hierarchy":::core_domain]
    description["Widgets::Description":::core_domain]
    currenttodos["Widgets::CurrentUserTodos":::core_domain]
    ["Widgets::Notes":::core_domain]
    participants["Widgets::Participants":::core_domain]
    development["Widgets::Development":::core_domain]
    designs["Widgets::Designs":::core_domain]
    emailpart["Widgets::EmailParticipants":::core_domain]
    timetracking["Widgets::TimeTracking":::core_domain]
    notifications["Widgets::Notifications":::core_domain]
    errtracking["Widgets::ErrorTracking":::core_domain]
    rollupabledates["RollupableDates":::data_structure]
    ## EE widgets
    weight["Widgets::Weight EE":::core_domain]
    healthstatus["Widgets::HealthStatus EE":::core_domain]
    verification["Widgets::VerificationStatus EE":::core_domain]
    iterations["Widgets::Iteration EE":::core_domain]
    vulnerabilities["Widgets::Vulnerabilities EE":::core_domain]
    requirementlegacy["Widgets::RequirementLegacy EE":::core_domain]
  end

  %% SUBGRAPH: Widget Callbacks/Behaviors
  subgraph callbacks["Widget Callbacks and Behavioral Hooks"]
    direction TB
    style callbacks fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2
    callbackbase["Callbacks::Base":::supporting]
    cbassignees["Callbacks::Assignees":::supporting]
    cbnotes["Callbacks::Notes":::supporting]
    cbcrmcontacts["Callbacks::CrmContacts":::supporting]
    cbnotifications["Callbacks::Notifications":::supporting]
    cbawardemoji["Callbacks::AwardEmoji":::supporting]
    cbstartdue["Callbacks::StartAndDueDate":::supporting]
    cbcurrenttodos["Callbacks::CurrentUserTodos":::supporting]
    cbmilestone["DataSync::Widgets::Milestone":::supporting]
    cbparticipants["DataSync::Widgets::Participants":::supporting]
    cbdevelopment["DataSync::Widgets::Development":::supporting]
    cbdesigns["DataSync::Widgets::Designs":::supporting]
    cbstatus["DataSync::Widgets::Status EE":::supporting]
  end

  %% SUBGRAPH: Widgetable Service and Concerns
  subgraph widgetable["Widgetable Service/Concerns"]
    direction TB
    style widgetable fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2
    widgetableservice["WidgetableService":::supporting]
    widgetableserconcern["WidgetableService Concern":::supporting]
  end

  %% SUBGRAPH: Domain Services
  subgraph services["Services & Processes"]
    direction TB
    style services fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2
    create_srv["CreateService":::supporting]
    build_srv["BuildService":::supporting]
    closing_mr_srv["ClosingMergeRequests::CreateService":::supporting]
    quickactions_srv["QuickActions::TargetService":::supporting]
    create_ee_srv["EE::WorkItems::CreateService":::supporting]
    update_ee_srv["EE::WorkItems::UpdateService":::supporting]
  end

  %% SUBGRAPH: Sorting & Utility
  subgraph sortingutils["Sorting/Utility"]
    direction TB
    style sortingutils fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2
    sortingkeys["SortingKeys":::supporting]
  end

  %% SUBGRAPH: Events
  subgraph events["Domain Events"]
    direction TB
    style events fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2
    event_created["WorkItemCreatedEvent":::initialization]
    event_updated["WorkItemUpdatedEvent":::initialization]
    event_deleted["WorkItemDeletedEvent":::initialization]
    event_closed["WorkItemClosedEvent EE":::initialization]
    event_reopened["WorkItemReopenedEvent EE":::initialization]
  end

  %% SUBGRAPH: GraphQL: Mutations & Types for Work Items
  subgraph graphql_mut["GraphQL Mutations/Concerns"]
    direction TB
    style graphql_mut fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2
    gql_create["Mutations::WorkItems::Create":::core_domain]
    gql_create_task["Mutations::WorkItems::CreateFromTask":::core_domain]
    gql_update["Mutations::WorkItems::Update":::core_domain]
    gql_delete["Mutations::WorkItems::Delete":::core_domain]
    gql_convert["Mutations::WorkItems::Convert":::core_domain]
    gql_add_mr["Mutations::WorkItems::AddClosingMergeRequest":::core_domain]
    gql_csv_export["Mutations::WorkItems::CSV::Export":::core_domain]
    gql_user_pref_update["Mutations::WorkItems::UserPreference::Update":::core_domain]
    gql_subscribe["Mutations::WorkItems::Subscribe":::core_domain]
    gql_linked["Mutations::WorkItems::LinkedItems::Base":::core_domain]
    gql_widgetable["Mutations::WorkItems::Widgetable":::supporting]
    ## EE GraphQL
    gql_ee_create["EE::Mutations::WorkItems::Create":::core_domain]
  end

  subgraph graphql_types["GraphQL Types & Widget Types"]
    direction TB
    style graphql_types fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2
    gql_witype["Types::WorkItemType":::data_structure]
    gql_widget_interface["Types::WorkItems::WidgetInterface":::data_structure]
    gql_widget_weight["Types::WorkItems::Widgets::WeightType EE":::data_structure]
    gql_widget_labels["Types::WorkItems::Widgets::LabelsType EE":::data_structure]
    gql_widget_color["Types::WorkItems::Widgets::ColorType EE":::data_structure]
    gql_widget_health["Types::WorkItems::Widgets::HealthStatusType EE":::data_structure]
    gql_widget_status["Types::WorkItems::StatusType EE":::data_structure]
    gql_widget_iteration["Types::WorkItems::Widgets::IterationType EE":::data_structure]
    gql_widget_vuln["Types::WorkItems::Widgets::VulnerabilitiesType EE":::data_structure]
    gql_widget_rolledup["Types::WorkItems::Widgets::RolledupDatesType EE":::data_structure]
    gql_widget_verif["Types::WorkItems::Widgets::VerificationStatusInputType EE":::data_structure]
    gql_widget_verif_filter["Types::WorkItems::Widgets::VerificationStatusFilterInputType EE":::data_structure]
    gql_widget_reqleg_filter["Types::WorkItems::Widgets::RequirementLegacyFilterInputType EE":::data_structure]
    gql_widget_health_in["Types::WorkItems::Widgets::HealthStatusInputType EE":::data_structure]
    gql_widget_weight_in["Types::WorkItems::Widgets::WeightInputType EE":::data_structure]
    gql_widget_color_in["Types::WorkItems::Widgets::ColorInputType EE":::data_structure]
    gql_widget_progress_in["Types::WorkItems::Widgets::ProgressInputType EE":::data_structure]
    gql_widget_customfield["Types::WorkItems::Widgets::CustomFieldFilterInputType EE":::data_structure]
  end

  %% SUBGRAPH: GraphQL: Query/Finder Layer
  subgraph gql_query["GraphQL Queries, Finders and Resolvers"]
    direction TB
    style gql_query fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2
    gql_workitems_finder["WorkItemsFinder":::supporting]
    gql_glql_finder["Glql::WorkItemsFinder":::supporting]
    gql_ee_resolver["EE::Resolvers::WorkItemsResolver":::supporting]
    gql_desc_templates["EE::Resolvers::WorkItems::DescriptionTemplatesResolver":::supporting]
  end

  %% SUBGRAPH: Controllers/Presentation Layer
  subgraph presentation["Controllers & Presentation"]
    direction TB
    style presentation fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2
    grp_wi_controller["Groups::WorkItemsController":::supporting]
    prj_wi_controller["Projects::WorkItemsController":::supporting]
    ee_prj_wi_controller["EE::Projects::WorkItemsController":::supporting]
  end

  %% SUBGRAPH: Board/Related Features
  subgraph boards["Boards/Related Features"]
    direction TB
    style boards fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2
    sortingkeys
    quickactions_srv
  end

  %% SUBGRAPH: EE Models/Concerns/Support
  subgraph ee_support["EE Models, Concerns, and Advanced Support"]
    direction TB
    style ee_support fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2
    ee_type["EE::WorkItems::Type":::supporting]
    ee_label_concern["UnifiedAssociations::Labels EE":::supporting]
    ee_award_concern["UnifiedAssociations::AwardEmoji EE":::supporting]
    ee_widgets_assignees["Widgets::Assignees EE":::supporting]
    ee_widgets_linkeditems["Widgets::LinkedItems EE":::supporting]
    ee_widgets_dev["Widgets::Development EE":::supporting]
    ee_widgets_startdue["Widgets::StartAndDueDate EE":::supporting]
  end

  %% EXPLICIT COLORS
  classDef core_domain fill:#D4F1F9,stroke:#A8C6D8,stroke-width:2,rx:10,ry:10
  classDef supporting fill:#FFF8DC,stroke:#F2E6B9,stroke-width:2,rx:10,ry:10
  classDef data_structure fill:#E0F8E0,stroke:#B4D8B4,stroke-width:2,rx:10,ry:10
  classDef error_handling fill:#FFE4E1,stroke:#FFA5A0,stroke-width:2,rx:10,ry:10
  classDef initialization fill:#E6E6FA,stroke:#C6B4F2,stroke-width:2,rx:10,ry:10

  %% LOGICAL RELATIONSHIPS

  %% Core Model Structure
  workitemtype -- defines --> workitem
  widgetdef -- defines widgets for --> workitemtype

  workitem -- hasType --> workitemtype
  workitem -- hasWidgets --> widgetsbase

  workitem -- uses --> rollupabledates
  workitem -- uses --> sortingkeys

  %% Policy Layer
  typespolicy -- policyFor --> workitemtype
  workitempolicy -- policyFor --> workitem
  workitempresenter -- presents --> workitem

  %% Widgets System: Base & Hierarchy
  widgetsbase -- inheritedBy --> labels
  widgetsbase -- inheritedBy --> milestone
  widgetsbase -- inheritedBy --> startdue
  widgetsbase -- inheritedBy --> linkedresources
  widgetsbase -- inheritedBy --> awardemoji
  widgetsbase -- inheritedBy --> crmcontacts
  widgetsbase -- inheritedBy --> linkeditems
  widgetsbase -- inheritedBy --> hierarchy
  widgetsbase -- inheritedBy --> description
  widgetsbase -- inheritedBy --> currenttodos
  widgetsbase -- inheritedBy --> 
  widgetsbase -- inheritedBy --> participants
  widgetsbase -- inheritedBy --> development
  widgetsbase -- inheritedBy --> designs
  widgetsbase -- inheritedBy --> emailpart
  widgetsbase -- inheritedBy --> timetracking
  widgetsbase -- inheritedBy --> notifications
  widgetsbase -- inheritedBy --> errtracking
  widgetsbase -- inheritedBy --> requirementlegacy
  widgetsbase -- inheritedBy --> weight
  widgetsbase -- inheritedBy --> healthstatus
  widgetsbase -- inheritedBy --> verification
  widgetsbase -- inheritedBy --> iterations
  widgetsbase -- inheritedBy --> vulnerabilities

  %% Widget Data Flows
  labels -- delegatesTo --> workitem
  milestone -- delegatesTo --> workitem
  startdue -- delegatesTo --> workitem
  linkedresources -- delegatesTo --> workitem
  awardemoji -- delegatesTo --> workitem
  crmcontacts -- delegatesTo --> workitem
  linkeditems -- delegatesTo --> workitem
  hierarchy -- delegatesTo --> workitem
  description -- delegatesTo --> workitem
  currenttodos -- delegatesTo --> workitem
   -- delegatesTo --> workitem
  participants -- delegatesTo --> workitem
  development -- delegatesTo --> workitem
  designs -- delegatesTo --> workitem
  emailpart -- delegatesTo --> workitem
  timetracking -- delegatesTo --> workitem
  notifications -- delegatesTo --> workitem
  errtracking -- delegatesTo --> workitem
  rollupabledates -- usedBy --> startdue
  requirementlegacy -- delegatesTo --> workitem
  weight -- delegatesTo --> workitem
  healthstatus -- delegatesTo --> workitem
  verification -- delegatesTo --> workitem
  iterations -- delegatesTo --> workitem
  vulnerabilities -- delegatesTo --> workitem

  %% EE Model Extensions and Concerns
  ee_type -- enhances --> workitemtype
  ee_label_concern -- includedIn --> labels
  ee_award_concern -- includedIn --> awardemoji
  ee_widgets_assignees -- extends --> widgetsbase
  ee_widgets_linkeditems -- extends --> linkeditems
  ee_widgets_dev -- extends --> development
  ee_widgets_startdue -- extends --> startdue

  %% Callback and Lifecycle Flow
  callbackbase -- extendedBy --> cbassignees
  callbackbase -- extendedBy --> cbnotes
  callbackbase -- extendedBy --> cbcrmcontacts
  callbackbase -- extendedBy --> cbnotifications
  callbackbase -- extendedBy --> cbawardemoji
  callbackbase -- extendedBy --> cbstartdue
  callbackbase -- extendedBy --> cbcurrenttodos

  workitem -- invokes --> callbackbase

  %% DataSync Callbacks for Widgets
  cbmilestone -- managesLifecycle --> milestone
  cbparticipants -- managesLifecycle --> participants
  cbdevelopment -- managesLifecycle --> development
  cbdesigns -- managesLifecycle --> designs
  cbstatus -- managesLifecycle --> healthstatus

  %% Widgetable Service Structure
  widgetableservice -- providesTo --> create_srv
  widgetableservice -- providesTo --> widgetableserconcern
  create_srv -- executesFor --> workitem
  create_srv -- coordinates --> callbackbase
  create_ee_srv -- enhances --> create_srv
  update_ee_srv -- updatesFor --> workitem

  build_srv -- builds --> workitem

  closing_mr_srv -- linksMRFor --> workitem

  %% Sorting and Quick Actions
  sortingkeys -- providesOrderFor --> workitem
  quickactions_srv -- invokedFor --> workitem

  %% Events and DDD
  event_created -- signals --> workitem
  event_updated -- signals --> workitem
  event_deleted -- signals --> workitem
  event_closed -- signals --> workitem
  event_reopened -- signals --> workitem

  %% GraphQL API: Mutations/Types
  gql_create -- creates --> workitem
  gql_create -- uses --> gql_widgetable
  gql_create_task -- creates --> workitem
  gql_update -- updates --> workitem
  gql_update -- uses --> gql_widgetable
  gql_delete -- deletes --> workitem
  gql_convert -- convertsTypeFor --> workitem
  gql_add_mr -- addsMRFor --> workitem
  gql_csv_export -- exportsFor --> workitem
  gql_user_pref_update -- updatesPrefFor --> workitemtype
  gql_subscribe -- subscribesTo --> workitem
  gql_linked -- linksItems --> workitem
  gql_widgetable -- extractsParamsFor --> widgetsbase
  gql_ee_create -- extends --> gql_create

  %% GraphQL Widget Types/Interfaces
  gql_witype -- isTypeOf --> workitem
  gql_widget_interface -- implementedBy --> gql_widget_weight
  gql_widget_interface -- implementedBy --> gql_widget_labels
  gql_widget_interface -- implementedBy --> gql_widget_color
  gql_widget_interface -- implementedBy --> gql_widget_health
  gql_widget_interface -- implementedBy --> gql_widget_status
  gql_widget_interface -- implementedBy --> gql_widget_iteration
  gql_widget_interface -- implementedBy --> gql_widget_vuln
  gql_widget_interface -- implementedBy --> gql_widget_rolledup
  gql_widget_interface -- implementedBy --> gql_widget_verif

  %% GraphQL Widget Input/Filter Types
  gql_widget_weight_in -- inputFor --> weight
  gql_widget_health_in -- inputFor --> healthstatus
  gql_widget_color_in -- inputFor --> labels
  gql_widget_progress_in -- inputFor --> workitem
  gql_widget_verif_filter -- filterFor --> verification
  gql_widget_reqleg_filter -- filterFor --> requirementlegacy
  gql_widget_customfield -- filterFor --> workitem

  %% GraphQL Finders/Resolvers
  gql_workitems_finder -- finds --> workitem
  gql_glql_finder -- finds --> workitem
  gql_ee_resolver -- resolves --> workitem
  gql_desc_templates -- resolves --> description

  %% Boards/Related Features
  sortingkeys -- sorts --> boards
  quickactions_srv -- supports --> boards

  %% Presentation Layer
  grp_wi_controller -- manages --> workitem
  prj_wi_controller -- manages --> workitem
  ee_prj_wi_controller -- manages --> workitem
  grp_wi_controller -- invokes --> create_srv
  prj_wi_controller -- invokes --> create_srv
  ee_prj_wi_controller -- invokes --> create_ee_srv

  %% Domain structure and data transformation summary  
  workitem -- hasWidgets --> widgetsbase
  widgetsbase -- delegatesTo --> workitem
  workitemtype -- hasDefinition --> widgetdef
  workitem -- policiesDefinedBy --> workitempolicy
  workitemtype -- policiesDefinedBy --> typespolicy
  workitem -- presentedBy --> workitempresenter

  %% Quick command summary
  widgetsbase -- QuickActions --> quickactions_srv
  quickactions_srv -- resolves --> widgetsbase

  %% Widget definition and instantiation
  widgetdef -- instantiates --> widgetsbase

  %% Widgetable Service and Callbacks
  create_srv -- managesCallbacks --> callbackbase
  callbackbase -- hooksFor --> widgetsbase

  %% EE-specific widget feature extension
  ee_widgets_assignees -- extends --> labels
  ee_widgets_linkeditems -- extends --> linkeditems
  ee_widgets_dev -- extends --> development
  ee_widgets_startdue -- extends --> startdue

  %% Relationships to Data Structures
  workitem -- uses --> widgetdef
  widgetdef -- references --> widgetsbase
  widgetdef -- associatesWith --> workitemtype

  %% Supporting/support files across the domain
  sortingkeys -- referredBy --> widgetsbase
  rollupabledates -- usedBy --> startdue

  %% DataSync widgets for entity copy/lifecycle
  cbmilestone -- postMoveCleanup --> milestone
  cbparticipants -- postMoveCleanup --> participants
  cbdevelopment -- postMoveCleanup --> development
  cbdesigns -- postMoveCleanup --> designs

  %% Error handling (not many explicit error files, but errors raised in service/callbacks)
  callbackbase -- raisesError --> workitem
  cbcrmcontacts -- raisesError --> crmcontacts
  cbawardemoji -- raisesError --> awardemoji
```