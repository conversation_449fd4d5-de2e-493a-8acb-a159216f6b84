```mermaid
flowchart TB
  %% ------------------------
  %% SUBGRAPH: DOMAIN CONTEXT
  %% ------------------------
  subgraph GeoReplicationDomain["Geo Replication: Repository Syncing & Verification" ]
  direction TB
  style GeoReplicationDomain fill:#F8F8F8,stroke:#8EC3EB,stroke-width:3,stroke-dasharray: 5 5

   %% -----------------------------------------------------------
   %% CORE SCHEDULING & JOB DISPATCH: Sync orchestration BLUE
   %% -----------------------------------------------------------

   subgraph SchedulerCluster["Scheduling & Job Dispatch" ]
   direction TB
   style SchedulerCluster fill:#F8F8F8,stroke:#5BB5E7,stroke-width:2

     geo_scheduler[/"geo/scheduler/scheduler_worker.rb\n\n* Core: Generic scheduler for geo replication syncing jobs\n* Batches & schedules synchronization tasks\n* Provides exclusive lease & strong memoization for idempotency\n* Ensures consistent, conflict-free job dispatch"/]
     style geo_scheduler fill:#D4F1F9,stroke:#7ac3e8,stroke-width:2,stroke-dasharray: 3 2,rx:12,ry:12

     geo_secondary_scheduler[/"geo/scheduler/secondary/scheduler_worker.rb\n\n* Secondary-Node-only scheduler logic for geo replication\n* Extends base scheduler to filter on 'secondary' nodes\n* Coordinates jobs for verification and sync\n* Logs and checks geo database state"/]
     style geo_secondary_scheduler fill:#D4F1F9,stroke:#7ac3e8,stroke-width:2,rx:12,ry:12

   end

   %% -----------------------------------------------------------
   %% REPLICATION & SYNC WORKERS: File/Data movement BLUE
   %% -----------------------------------------------------------

   subgraph ReplicationAndSync["Replication & Sync Workers" ]
   direction TB
   style ReplicationAndSync fill:#F8F8F8,stroke:#5BB5E7,stroke-width:2

      geo_registry_sync[/"geo/repository_registry_sync_worker.rb\n\n* Syncs the main repository registry on secondary nodes\n* Uses batch capacity derived from node config\n* Responsible for pulling changes to secondary"/]
      style geo_registry_sync fill:#D4F1F9,stroke:#7ac3e8,stroke-width:2,rx:12,ry:12

      geo_hashed_storage_attach_mig[/"geo/hashed_storage_attachments_migration_worker.rb\n\n* Migrates project attachments between storage layouts\n* Ensures correct physical attachment placement during geo sync\n* Facilitates data integrity during migration"/]
      style geo_hashed_storage_attach_mig fill:#D4F1F9,stroke:#7ac3e8,stroke-width:2,rx:12,ry:12

      geo_framework_repo_sync_service[/"geo/framework_repository_sync_service.rb\n\n* Service: Implements sync execution, lease management, and caching logic\n* Wraps repository and replicator abstractions\n* Controls: clone/fetch repo, storage selection, cache expiry\n* Encapsulates domain-specific 'replicator', forwarding to registry"/]
      style geo_framework_repo_sync_service fill:#D4F1F9,stroke:#7ac3e8,stroke-width:2,rx:12,ry:12

   end

   %% -----------------------------------------------------------
   %% LOGGING HELPERS: Contextual Audit YELLOW
   %% -----------------------------------------------------------

   subgraph LoggingHelpers["Audit Logging & Helpers" ]
   direction TB
   style LoggingHelpers fill:#F8F8F8,stroke:#EDD792,stroke-width:2

      geo_container_repo_log_helpers[/"gitlab/geo/container_repository_log_helpers.rb\n\n* Adds contextual logging for container repo events\n* Enriches logs with project and repository data\n* Leverages base LogHelpers pattern"/]
      style geo_container_repo_log_helpers fill:#FFF8DC,stroke:#EDD792,stroke-width:2,rx:12,ry:12

   end

   %% -----------------------------------------------------------
   %% DATA STRUCTURES & DB MIGRATION GREEN
   %% -----------------------------------------------------------

   subgraph DataStructuresAndMigration["Data Persistence & Structure" ]
   direction TB
   style DataStructuresAndMigration fill:#F8F8F8,stroke:#A1DBA1,stroke-width:2

      geo_db_migration[/"db/geo/migrate/20180806011909_add_failed_synchronizations_partial_index.rb\n\n* Adds partial DB index for failed syncs\n* Enables performant error/failure reporting\n* Used to optimize registry tracking"\]
      style geo_db_migration fill:#E0F8E0,stroke:#A1DBA1,stroke-width:2,rx:12,ry:12

   end

   %% -----------------------------------------------------------
   %% QA / TEST AUTOMATION: End-to-End Verification YELLOW
   %% -----------------------------------------------------------

   subgraph QASpecs["QA: Repository Sync/Verification" ]
   direction TB
   style QASpecs fill:#F8F8F8,stroke:#EDD792,stroke-width:2

      qa_wiki_http_push_sec[/"qa/specs/features/ee/browser_ui/12_systems/geo/wiki_http_push_to_secondary_spec.rb\n\n* Verifies HTTP push to secondary Geo nodes for wikis\n* Ensures syncing/verification propagation"/]
      style qa_wiki_http_push_sec fill:#FFF8DC,stroke:#EDD792,stroke-width:2,rx:12,ry:12

      qa_wiki_http_push[/"qa/specs/features/ee/browser_ui/12_systems/geo/wiki_http_push_spec.rb\n\n* QA spec for wiki HTTP pushes on primary node\n* Checks push propagation before secondary sync"/]
      style qa_wiki_http_push fill:#FFF8DC,stroke:#EDD792,stroke-width:2,rx:12,ry:12

      qa_ssh_push_sec[/"qa/specs/features/ee/browser_ui/12_systems/geo/ssh_push_to_secondary_spec.rb\n\n* Verifies SSH push to secondary Geo\n* End-to-end physical repo verification"/]
      style qa_ssh_push_sec fill:#FFF8DC,stroke:#EDD792,stroke-width:2,rx:12,ry:12

      qa_ssh_push[/"qa/specs/features/ee/browser_ui/12_systems/geo/ssh_push_spec.rb\n\n* QA: SSH push spec for primary nodes\n* Ensures pushes reach registry"/]
      style qa_ssh_push fill:#FFF8DC,stroke:#EDD792,stroke-width:2,rx:12,ry:12

      qa_http_push_sec[/"qa/specs/features/ee/browser_ui/12_systems/geo/http_push_to_secondary_spec.rb\n\n* Tests HTTP push to secondary\n* Validates HTTP repo sync workflow"/]
      style qa_http_push_sec fill:#FFF8DC,stroke:#EDD792,stroke-width:2,rx:12,ry:12

      qa_http_push[/"qa/specs/features/ee/browser_ui/12_systems/geo/http_push_spec.rb\n\n* HTTP push - primary node, initiator of geo synchronization"/]
      style qa_http_push fill:#FFF8DC,stroke:#EDD792,stroke-width:2,rx:12,ry:12

   end

   %% -----------------------------------------------------------
   %% QA / UI ABSRACTION SUPPORT YELLOW
   %% -----------------------------------------------------------

   subgraph QAPageSupport["QA: UI Support/Replication Wait" ]
   direction TB
   style QAPageSupport fill:#F8F8F8,stroke:#EDD792,stroke-width:2

      qa_page_project_show[/"qa/ee/page/project/show.rb\n\n* Supports waiting for repository replication in QA scenarios\n* Wraps access to UI elements to confirm repo state on both nodes"/]
      style qa_page_project_show fill:#FFF8DC,stroke:#EDD792,stroke-width:2,rx:12,ry:12

   end

  %% ---------------
  %% RELATIONSHIPS
  %% ---------------

  %% SCHEDULING DEPENDENCIES (CONTROL FLOW/HIERARCHY)
  geo_scheduler --> geo_secondary_scheduler
  geo_secondary_scheduler --> geo_registry_sync
  geo_registry_sync --> geo_framework_repo_sync_service
  geo_scheduler --> geo_hashed_storage_attach_mig

  %% LOGGING INTEGRATION WITH WORKERS & SERVICES
  geo_registry_sync -.-> geo_container_repo_log_helpers
  geo_framework_repo_sync_service -.-> geo_container_repo_log_helpers

  %% DB STRUCTURE SUPPORTS REGISTRY
  geo_db_migration ==> geo_registry_sync

  %% QA: ACTION SEQUENCES (verifies, triggers, or inspects via UI)
  qa_wiki_http_push --> geo_scheduler
  qa_wiki_http_push_sec --> geo_secondary_scheduler
  qa_wiki_http_push_sec --> qa_page_project_show
  qa_ssh_push --> geo_scheduler
  qa_ssh_push_sec --> geo_secondary_scheduler
  qa_ssh_push_sec --> qa_page_project_show
  qa_http_push --> geo_scheduler
  qa_http_push_sec --> geo_secondary_scheduler
  qa_http_push_sec --> qa_page_project_show

  %% UI SUPPORT PARTNERS WITH QA SPECS
  qa_page_project_show --> geo_framework_repo_sync_service
  qa_page_project_show -.-> geo_registry_sync

  %% DOMAIN DATA FLOW (ABSTRACTIONS)
  geo_framework_repo_sync_service --> geo_registry_sync
  geo_registry_sync --> geo_db_migration
  geo_hashed_storage_attach_mig -.-> geo_framework_repo_sync_service

  %% ------------ GROUP RELATIONSHIPS
    SchedulerCluster --- ReplicationAndSync
    ReplicationAndSync --- LoggingHelpers
    ReplicationAndSync --- DataStructuresAndMigration
    QASpecs --- QAPageSupport

  %% ------------- DOMAIN BORDERS
    SchedulerCluster --- GeoReplicationDomain
    ReplicationAndSync --- GeoReplicationDomain
    LoggingHelpers --- GeoReplicationDomain
    DataStructuresAndMigration --- GeoReplicationDomain
    QASpecs --- GeoReplicationDomain
    QAPageSupport --- GeoReplicationDomain

  %% ------------- LAYOUT SPACING
    style geo_scheduler margin:20
    style geo_secondary_scheduler margin:20
    style geo_registry_sync margin:20
    style geo_hashed_storage_attach_mig margin:20
    style geo_framework_repo_sync_service margin:20
    style geo_container_repo_log_helpers margin:20
    style geo_db_migration margin:20
    style qa_page_project_show margin:20

end
```