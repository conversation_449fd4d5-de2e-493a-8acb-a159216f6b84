```mermaid
flowchart TB
    %% Global styling
    %% Core domain nodes
    classDef core fill:#D4F1F9,stroke:#61a4bc,stroke-width:2px,stroke-dasharray: 5,rx:8,ry:8;
    %% Supporting / utility nodes
    classDef support fill:#FFF8DC,stroke:#e8d68b,stroke-width:2px,rx:8,ry:8;
    %% Data structures
    classDef data fill:#E0F8E0,stroke:#91c99c,stroke-width:2px,rx:8,ry:8;
    %% Error handling
    classDef error fill:#FFE4E1,stroke:#e7b0ad,stroke-width:2px,rx:8,ry:8;
    %% Initialization / setup
    classDef setup fill:#E6E6FA,stroke:#b7aeea,stroke-width:2px,rx:8,ry:8;
    %% Subgraph border
    classDef grouping fill:#F8F8F8,stroke:#dedede,stroke-width:2px,rx:12,ry:12;

    %% Subgraphs organized by logical purpose

    %% Authentication MODELS
    subgraph AuthModels["Authentication Models & Domain Data Structures"]
        direction TB
        style AuthModels fill:#F8F8F8,stroke:#D4F1F9

        m_identity[Identity model\napp/models/identity.rb]:::core
        m_identity_uniq[Identity::UniquenessScopes\napp/models/identity/uniqueness_scopes.rb]:::core
        eem_ident[EE::Identity\nee/app/models/ee/identity.rb]:::core
        eem_ident_us[EE::Identity::UniquenessScopes\nee/app/models/ee/identity/uniqueness_scopes.rb]:::core

        m_dk_req[Doorkeeper::OpenidConnect::Request\napp/models/doorkeeper/openid_connect/request.rb]:::core
        m_dk_token[Doorkeeper::AccessToken\napp/models/doorkeeper/access_token.rb]:::core
        m_dk_grant[Doorkeeper::AccessGrant\napp/models/doorkeeper/access_grant.rb]:::core
        m_dk_devicetoken[Doorkeeper::DeviceAuthorizationGrant::DeviceGrant\napp/models/doorkeeper/device_authorization_grant/device_grant.rb]:::core

        m_oauth_grant[OAuthAccessGrant\napp/models/oauth_access_grant.rb]:::core
        m_oauth_token[OAuthAccessToken\napp/models/oauth_access_token.rb]:::core

        m_saml_group_link[SamlGroupLink \nee/app/models/saml_group_link.rb]:::data
        m_ldap_key[LDAPKey \nee/app/models/ldap_key.rb]:::data

        m_auth_event[AuthenticationEvent\napp/models/authentication_event.rb]:::data
    end

    %% IDENTITY PROVIDER AND POLICY
    subgraph Policies["Provider Policies & Administration"]
        direction TB
        style Policies fill:#F8F8F8,stroke:#D4F1F9

        p_idp_policy[IdentityProviderPolicy\napp/policies/identity_provider_policy.rb]:::support

        c_admin_identities[Admin::IdentitiesController\napp/controllers/admin/identities_controller.rb]:::core
    end

    %% OAUTH & OPENID CONNECT
    subgraph OAuth["OAuth / OpenID Connect Flows & Controllers"]
        direction TB
        style OAuth fill:#F8F8F8,stroke:#D4F1F9

        c_oauth_apps[Oauth::ApplicationsController\napp/controllers/oauth/applications_controller.rb]:::core
        c_oauth_apps_concern[OauthApplications concern\n(app/controllers/concerns/oauth_applications.rb)]:::support

        c_oauth_tokens[Oauth::TokensController\napp/controllers/oauth/tokens_controller.rb]:::core
        c_oauth_authed_apps[Oauth::AuthorizedApplicationsController\napp/controllers/oauth/authorized_applications_controller.rb]:::core

        c_oauth_device_auths[Oauth::DeviceAuthorizationsController\napp/controllers/oauth/device_authorizations_controller.rb]:::core

        c_jwks[JwksController\napp/controllers/jwks_controller.rb]:::support

        s_keys_create[Keys::CreateService\napp/services/keys/create_service.rb]:::support

        dko_req[Doorkeeper::OpenidConnect::Request]:::data
        dko_token[Doorkeeper::AccessToken]:::data
        dko_grant[Doorkeeper::AccessGrant]:::data

        oit_user[Gitlab::Auth::Oidc::User\nee/lib/gitlab/auth/oidc/user.rb]:::core
        oit_auth_hash[Gitlab::Auth::Oidc::AuthHash\nee/lib/gitlab/auth/oidc/auth_hash.rb]:::data
        oit_config[Gitlab::Auth::Oidc::Config\nee/lib/gitlab/auth/oidc/config.rb]:::support
        oit_stepup[Gitlab::Auth::Oidc::StepUpAuthentication\nlib/gitlab/auth/oidc/step_up_authentication.rb]:::support
        oit_stepup_before[Gitlab::Auth::Oidc::StepUpAuthBeforeRequestPhase\nlib/gitlab/auth/oidc/step_up_auth_before_request_phase.rb]:::support
        oit_linker[Gitlab::Auth::Oidc::IdentityLinker\nee/lib/gitlab/auth/oidc/identity_linker.rb]:::core

        m_dk_device_token[Doorkeeper::DeviceAuthorizationGrant::DeviceGrant]:::data
    end

    %% AUTHENTICATION SERVICES, BASE & HELPERS
    subgraph AuthnBase["Core Authentication Logic & Utilities"]
        direction TB
        style AuthnBase fill:#F8F8F8,stroke:#D4F1F9

        auth_base[Gitlab::Auth\nlib/gitlab/auth.rb]:::core
        res_base[Gitlab::Auth::Result\nlib/gitlab/auth/result.rb]:::support

        c_login[QA::Page::Main::Login\nqa/qa/page/main/login.rb]:::support
        c_oauth[QA::Page::Main::OAuth\nqa/qa/page/main/oauth.rb]:::support
        resource_oauth_app[QA::Resource::InstanceOauthApplication\nqa/qa/resource/instance_oauth_application.rb]:::support
        support_oauth_app[WebIde::DefaultOauthApplication\nlib/web_ide/default_oauth_application.rb]:::support

        auth_scope_validator[Gitlab::Auth::ScopeValidator\nlib/gitlab/auth/scope_validator.rb]:::support
        dpop_token[Gitlab::Auth::DpopToken\nlib/gitlab/auth/dpop_token.rb]:::support
    end

    %% SAML DOMAIN
    subgraph Saml["SAML Authentication & Group SAML"]
        direction TB
        style Saml fill:#F8F8F8,stroke:#61a4bc

        sm_user[Gitlab::Auth::Saml::User\nlib/gitlab/auth/saml/user.rb]:::core
        sm_config[Gitlab::Auth::Saml::Config\nlib/gitlab/auth/saml/config.rb]:::support
        ee_sm_config[EE::Gitlab::Auth::Saml::Config\nee/lib/ee/gitlab/auth/saml/config.rb]:::support
        sm_auth_hash[Gitlab::Auth::Saml::AuthHash\nlib/gitlab/auth/saml/auth_hash.rb]:::data
        sm_id_linker[Gitlab::Auth::Saml::IdentityLinker\nlib/gitlab/auth/saml/identity_linker.rb]:::core
        ee_sm_id_linker[EE::Gitlab::Auth::Saml::IdentityLinker\nee/lib/ee/gitlab/auth/saml/identity_linker.rb]:::core
        sm_membership_updater[Gitlab::Auth::Saml::MembershipUpdater\nee/lib/gitlab/auth/saml/membership_updater.rb]:::core
        sm_sso_enforcer[Gitlab::Auth::Saml::SsoEnforcer\nee/lib/gitlab/auth/saml/sso_enforcer.rb]:::support
        sm_sso_state[Gitlab::Auth::Saml::SsoState\nee/lib/gitlab/auth/saml/sso_state.rb]:::support
        sm_origin_validator[Gitlab::Auth::Saml::OriginValidator\nlib/gitlab/auth/saml/origin_validator.rb]:::support
        sm_sso_filterable[EE::Gitlab::Auth::Saml::SsoSessionFilterable\nee/lib/ee/gitlab/auth/saml/sso_session_filterable.rb]:::support
        sm_duo_updater[Gitlab::Auth::Saml::DuoAddOnAssignmentUpdater\nee/lib/gitlab/auth/saml/duo_add_on_assignment_updater.rb]:::support

        pf_saml_filter[ParameterFilters::SamlResponse\nlib/parameter_filters/saml_response.rb]:::support

        group_saml_resp[Gitlab::Auth::GroupSaml::ResponseCheck\nee/lib/gitlab/auth/group_saml/response_check.rb]:::support
        omniauth_group_saml[OmniAuth::Strategies::GroupSaml\nee/lib/omni_auth/strategies/group_saml.rb]:::core
    end

    %% LDAP DOMAIN
    subgraph Ldap["LDAP Authentication, Sync & Access Control"]
        direction TB
        style Ldap fill:#F8F8F8,stroke:#61a4bc

        ldap_user[Gitlab::Auth::Ldap::User\nlib/gitlab/auth/ldap/user.rb]:::core
        ldap_person[Gitlab::Auth::Ldap::Person\nlib/gitlab/auth/ldap/person.rb]:::data
        ldap_adapter[Gitlab::Auth::Ldap::Adapter\nlib/gitlab/auth/ldap/adapter.rb]:::support
        ldap_config[Gitlab::Auth::Ldap::Config\nlib/gitlab/auth/ldap/config.rb]:::support
        ldap_auth_hash[Gitlab::Auth::Ldap::AuthHash\nlib/gitlab/auth/ldap/auth_hash.rb]:::data
        ldap_access[Gitlab::Auth::Ldap::Access\nlib/gitlab/auth/ldap/access.rb]:::core
        ldap_authen[Gitlab::Auth::Ldap::Authentication\nlib/gitlab/auth/ldap/authentication.rb]:::core
        ldap_dn[Gitlab::Auth::Ldap::DN\nlib/gitlab/auth/ldap/dn.rb]:::support
        ldap_error[Gitlab::Auth::Ldap::LdapConnectionError\nlib/gitlab/auth/ldap/ldap_connection_error.rb]:::error

        ee_ldap_user[EE::Gitlab::Auth::Ldap::User\nee/lib/ee/gitlab/auth/ldap/user.rb]:::core
        ee_ldap_sync_users[EE::Gitlab::Auth::Ldap::Sync::Users\nee/lib/ee/gitlab/auth/ldap/sync/users.rb]:::core
        ee_ldap_sync_proxy[EE::Gitlab::Auth::Ldap::Sync::Proxy\nee/lib/ee/gitlab/auth/ldap/sync/proxy.rb]:::core
        ee_ldap_sync_groups[EE::Gitlab::Auth::Ldap::Sync::Groups\nee/lib/ee/gitlab/auth/ldap/sync/groups.rb]:::core
        ee_ldap_sync_ext_users[EE::Gitlab::Auth::Ldap::Sync::ExternalUsers\nee/lib/ee/gitlab/auth/ldap/sync/external_users.rb]:::core
        ee_ldap_sync_admin_users[EE::Gitlab::Auth::Ldap::Sync::AdminUsers\nee/lib/ee/gitlab/auth/ldap/sync/admin_users.rb]:::core
        ee_ldap_sync_group[EE::Gitlab::Auth::Ldap::Sync::Group\nee/lib/ee/gitlab/auth/ldap/sync/group.rb]:::core
        ee_ldap_access[EE::Gitlab::Auth::Ldap::Access\nee/lib/ee/gitlab/auth/ldap/access.rb]:::core
        ee_ldap_config[EE::Gitlab::Auth::Ldap::Config\nee/lib/ee/gitlab/auth/ldap/config.rb]:::support
        ee_ldap_group[EE::Gitlab::Auth::Ldap::Group\nee/lib/ee/gitlab/auth/ldap/group.rb]:::data
        ee_ldap_person[EE::Gitlab::Auth::Ldap::Person\nee/lib/ee/gitlab/auth/ldap/person.rb]:::data
        ee_ldap_access_lvls[EE::Gitlab::Auth::Ldap::AccessLevels\nee/lib/ee/gitlab/auth/ldap/access_levels.rb]:::support
        ee_ldap_adapter[EE::Gitlab::Auth::Ldap::Adapter\nee/lib/ee/gitlab/auth/ldap/adapter.rb]:::support
    end

    %% OMNIAUTH PROVIDERS & STRATEGIES
    subgraph OmniAuthStrategies["OmniAuth Providers, SAML/LDAP Strategies, Initializers"]
        direction TB
        style OmniAuthStrategies fill:#F8F8F8,stroke:#61a4bc

        init_doorkeeper[Doorkeeper Initializer\nconfig/initializers/doorkeeper.rb]:::setup
        init_omniauth_saml[PatchOmniAuth SAML\nconfig/initializers_before_autoloader/100_patch_omniauth_saml.rb]:::setup
        strat_group_saml[OmniAuth::Strategies::GroupSaml\nee/lib/omni_auth/strategies/group_saml.rb]:::core
    end

    %% SCIM & IDENTITY PROVISIONING
    subgraph Scim["SCIM & Automated Provisioning"]
        direction TB
        style Scim fill:#F8F8F8,stroke:#91c99c

        scim_base[EE::Gitlab::Scim::BaseProvisioningService\nee/lib/ee/gitlab/scim/base_provisioning_service.rb]:::support
        scim_user[EE::Gitlab::Scim::ProvisioningService\nee/lib/ee/gitlab/scim/provisioning_service.rb]:::core
        scim_group[EE::Gitlab::Scim::Group::ProvisioningService\nee/lib/ee/gitlab/scim/group/provisioning_service.rb]:::core
        scim_group_deprov[EE::Gitlab::Scim::Group::DeprovisioningService\nee/lib/ee/gitlab/scim/group/deprovisioning_service.rb]:::core
    end

    %% ACCESS TOKENS & AUTHN EVENTS
    subgraph AccessTokens["Access Tokens & Grants"]
        direction TB
        style AccessTokens fill:#F8F8F8,stroke:#61a4bc

        t_oauth_token[OAuthAccessToken Model]:::core
        t_oauth_grant[OAuthAccessGrant Model]:::core
        t_dk_token[Doorkeeper::AccessToken Model]:::core
        t_dk_grant[Doorkeeper::AccessGrant Model]:::core
        t_authn_event[AuthenticationEvent Model]:::data
    end

    %% ERROR HANDLING
    subgraph ErrorHandling["Error Handling Modules"]
        direction TB
        style ErrorHandling fill:#F8F8F8,stroke:#FFE4E1

        err_ldap_connect[Gitlab::Auth::Ldap::LdapConnectionError]:::error
    end

    %% RELATIONSHIPS

    %% IDENTITY
    m_identity --inclusion--> m_identity_uniq
    m_identity --extension--> eem_ident
    m_identity_uniq --extension--> eem_ident_us
    m_identity --associates--> m_oauth_token
    m_identity --associates--> ldap_user
    m_identity --associates--> sm_user
    m_identity --associates--> oit_user

    eem_ident --associates--> eem_ident_us
    eem_ident --references--> sm_config

    %% OAUTH
    c_oauth_apps --uses--> m_oauth_token
    c_oauth_apps --includes--> c_oauth_apps_concern
    c_oauth_apps_concern --prepares_scopes--> m_oauth_token
    c_oauth_tokens --manages--> m_oauth_token
    c_oauth_authed_apps --reads--> m_oauth_token
    c_oauth_device_auths --uses--> m_dk_device_token
    c_jwks --manages--> dko_req
    s_keys_create --creates--> m_oauth_token

    t_oauth_token --based_on--> m_oauth_token
    t_oauth_grant --based_on--> m_oauth_grant

    %% OPENID CONNECT
    oit_user --has_config--> oit_config
    oit_user --parses--> oit_auth_hash
    oit_auth_hash --references--> oit_config
    oit_user --links_identities--> oit_linker
    oit_linker --baseclass--> m_identity

    %% SAML
    sm_user --finds--> m_identity
    sm_user --links_with--> sm_id_linker
    sm_id_linker --overrides--> ee_sm_id_linker
    sm_user --uses_groups--> sm_config
    sm_user --parses_hash--> sm_auth_hash
    sm_auth_hash --wraps_groups--> sm_config
    sm_user --invokes--> sm_membership_updater
    sm_membership_updater --updates--> m_identity
    sm_user --uses--> ldap_user
    sm_membership_updater --invokes--> group_saml_resp
    sm_user --configured_by--> sm_config
    sm_config --is_extended_by--> ee_sm_config
    sm_sso_enforcer --enforces--> sm_sso_state
    sm_sso_state --stores--> sm_origin_validator
    sm_sso_filterable --filters--> m_identity
    sm_duo_updater --updates--> m_identity

    group_saml_resp --validates--> sm_auth_hash
    group_saml_resp --used_by--> omniauth_group_saml
    omniauth_group_saml --callback--> sm_user

    pf_saml_filter --filters--> sm_auth_hash

    %% LDAP
    ldap_user --finds--> m_identity
    ldap_user --inherits--> ee_ldap_user
    ldap_user --uses--> ldap_adapter
    ldap_user --gets_dn_from--> ldap_dn
    ldap_user --uses_access--> ldap_access
    ldap_user --composes--> ldap_person
    ldap_user --parses_hash--> ldap_auth_hash
    ldap_user --links--> m_identity

    ldap_access --authorizes--> m_identity
    ldap_access --uses--> ldap_adapter
    ldap_access --delegates_to--> ee_ldap_access

    ldap_adapter --configured_by--> ldap_config
    ldap_config --extended_by--> ee_ldap_config
    ldap_adapter --used_by--> ee_ldap_adapter

    ldap_authen --calls--> ldap_user
    ldap_authen --calls--> ldap_adapter

    ldap_person --extended_by--> ee_ldap_person
    ldap_person --normalizes--> ldap_dn

    ee_ldap_sync_proxy --wraps--> ldap_adapter
    ee_ldap_sync_users --uses--> ee_ldap_sync_proxy
    ee_ldap_sync_groups --uses--> ee_ldap_sync_proxy
    ee_ldap_sync_ext_users --inherits--> ee_ldap_sync_users
    ee_ldap_sync_admin_users --inherits--> ee_ldap_sync_users
    ee_ldap_sync_group --extends--> ee_ldap_sync_proxy
    ee_ldap_group --adapts--> ldap_adapter

    ee_ldap_access_lvls --integrates--> ee_ldap_group

    m_ldap_key --owned_by--> m_identity

    %% POLICIES / ADMIN
    p_idp_policy --governs--> m_identity
    c_admin_identities --manages--> m_identity

    %% BASE AUTH LOGIC
    auth_base --provides-->|find_user| m_identity
    auth_base --provides-->|find_with_user_password| ldap_user
    auth_base --provides-->|find_with_user_password| sm_user
    auth_base --provides-->|find_with_user_password| oit_user
    auth_base --initializes--> res_base
    auth_scope_validator --validates--> m_oauth_token
    dpop_token --verifies--> m_oauth_token

    c_login --uses--> auth_base
    c_login --tests--> m_oauth_token
    c_oauth --interacts--> c_oauth_apps
    resource_oauth_app --provisions--> m_oauth_token
    support_oauth_app --registers--> m_oauth_token

    %% TOKEN/GRANT RELATIONS
    m_dk_req --references--> m_dk_grant
    m_dk_grant --grants--> m_dk_token
    m_dk_token --used_by--> m_oauth_token
    m_oauth_token --grants_access_to--> m_identity

    %% SCIM
    scim_base --extended_by--> scim_user
    scim_base --extended_by--> scim_group
    scim_group --calls--> scim_group_deprov
    scim_user --provisions--> m_identity
    scim_group --provisions--> m_identity

    %% ERROR HANDLING
    err_ldap_connect --handles--> ldap_adapter

    %% OMNIAUTH / PROVIDERS
    init_doorkeeper --configures--> c_oauth_apps
    init_doorkeeper --configures--> c_oauth_device_auths
    init_omniauth_saml --patches--> sm_user
    strat_group_saml --extends--> omniauth_group_saml
    strat_group_saml --handles--> group_saml_resp

    %% EDGES SHARED/INTERCONNECTED

    m_identity_uniq --scopes--> m_identity
    eem_ident_us --adds_saml_scope--> eem_ident
    m_saml_group_link --links_groups--> m_identity

    ldap_user --can_find_by_email--> ldap_person
    ldap_person --locates_user--> ldap_adapter

    sm_user --can_auto_link--> ldap_user
    sm_user --can_auto_link--> m_identity

    ldap_authen --uses--> ldap_adapter

    oit_user --inherits--> m_identity
    oit_user --initializes--> oit_config
    oit_user --obtains_groups--> oit_auth_hash

    c_oauth_device_auths --delegates_to--> m_dk_device_token

    eem_ident --belongs_to--> m_identity
    eem_ident --validates--> sm_config

    scim_base --returns_identity--> m_identity
    scim_group_deprov --removes--> m_identity

    sm_duo_updater --assigns--> m_identity

    sm_membership_updater --invokes--> m_saml_group_link

    %% Kerberos / X509 helpers
    subgraph ExtHelpers["Additional AuthN Mechanisms"]
        direction TB
        style ExtHelpers fill:#F8F8F8,stroke:#FFF8DC

        kerberos_helper[KerberosHelper\napp/helpers/kerberos_helper.rb]:::support
        ee_kerberos_helper[EE::KerberosHelper\nee/app/helpers/ee/kerberos_helper.rb]:::support
        x509_helper[X509Helper\napp/helpers/x509_helper.rb]:::support

        kerberos_helper --supports--> ldap_user
        kerberos_helper --augments--> ee_kerberos_helper
        x509_helper --supports--> ldap_adapter
    end

    %% Setup file
    config_init_doorkeeper[Doorkeeper Initializer\nconfig/initializers/doorkeeper.rb]:::setup
    config_init_doorkeeper --registers--> m_dk_token
    config_init_doorkeeper --registers--> m_dk_grant

    %% Intergroup connections (nesting edges for domain behaviors)
    AuthModels --provides_users_for--> OAuth
    AuthModels --provides_users_for--> Saml
    AuthModels --provides_users_for--> Ldap
    AuthModels --provides_users_for--> Scim
    OAuth --grants_tokens_to--> AuthModels
    Saml --creates_identities_in--> AuthModels
    Ldap --syncs_users_in--> AuthModels
    Scim --provisions_and_syncs_identities_in--> AuthModels

    OAuth --depends_on_setup--> OmniAuthStrategies
    Saml --depends_on_setup--> OmniAuthStrategies
    Ldap --depends_on_setup--> OmniAuthStrategies

    ErrorHandling --protects--> Ldap
    ErrorHandling --protects--> OAuth

    Policies --policy_on--> AuthModels

    %% Classes
    class m_identity,m_identity_uniq,eem_ident,eem_ident_us,eem_ident core;
    class m_dk_req,m_dk_token,m_dk_grant,m_dk_devicetoken,m_oauth_grant,m_oauth_token,oit_user,oit_linker,sm_user,sm_id_linker,ee_sm_id_linker,ldap_user,ldap_access,ldap_authen,ee_ldap_user,ee_ldap_access,ee_ldap_sync_users,ee_ldap_sync_proxy,ee_ldap_sync_groups,ee_ldap_sync_ext_users,ee_ldap_sync_admin_users,ee_ldap_sync_group,scim_user,scim_group,scim_group_deprov,omniauth_group_saml,strat_group_saml core;

    class kerberos_helper,ee_kerberos_helper,c_login,c_oauth,support_oauth_app,c_oauth_apps_concern,s_keys_create,auth_scope_validator,dpop_token,x509_helper,ee_ldap_access_lvls,ldap_adapter,ldap_dn,ldap_config,init_doorkeeper,init_omniauth_saml,oit_config,oit_auth_hash,oit_stepup,oit_stepup_before,sm_config,ee_sm_config,sm_auth_hash,sm_sso_state,sm_origin_validator,sm_sso_filterable,sm_sso_enforcer,sm_duo_updater,pf_saml_filter,group_saml_resp,c_jwks,resource_oauth_app,res_base support;

    class m_auth_event,m_saml_group_link,ldap_person,ldap_auth_hash,ee_ldap_group,ee_ldap_person,data;

    class err_ldap_connect,error;

    class init_doorkeeper,config_init_doorkeeper,init_omniauth_saml,setup;
```