```mermaid
flowchart TD
  %% GLOBAL STYLES
  classDef core fill:#D4F1F9,stroke:#99D9EA,stroke-width:2px,color:#144A68,stroke-dasharray:0,rx:10,ry:10;
  classDef util fill:#FFF8DC,stroke:#FFECB3,stroke-width:2px,color:#A07E32,rx:10,ry:10;
  classDef data fill:#E0F8E0,stroke:#A8E6A8,stroke-width:2px,color:#357A46,rx:10,ry:10;
  classDef error fill:#FFE4E1,stroke:#ECA6A6,stroke-width:2px,color:#A65C5C,rx:10,ry:10;
  classDef init fill:#E6E6FA,stroke:#C3C3E6,stroke-width:2px,color:#635D82,rx:10,ry:10;
  classDef gsubgraph fill:#F8F8F8,stroke:#D0D0D0,stroke-width:2px;
  classDef entity fill:#E0F8E0,stroke:#A8E6A8,stroke-width:2px,color:#357A46,stroke-dasharray:5,rx:10,ry:10;
  classDef api fill:#D4F1F9,stroke:#B5DEF4,stroke-width:2px,color:#205873,stroke-dasharray:1,rx:10,ry:10;

  %% ---------- DOMAIN ROOT & CORE AGGREGATES ----------
  subgraph sgDomain["Conan Package Registry Domain"]
    direction TB
    class sgDomain gsubgraph

    subgraph sgCore["Core Domain Types & Aggregates"]
      direction TB
      class sgCore gsubgraph

      pkgConan[/"packages/conan.rb"/\nConan Root Module]:::core
      pkgConanPkg["package.rb\nConan Package Aggregate"]:::core
      pkgConanRef["package_reference.rb\nRepresents package references"]:::core
      pkgConanRev["package_revision.rb\nTracks revisions of packages"]:::core
      pkgRecipeRev["recipe_revision.rb\nRecipe revision for Conan package"]:::core
      pkgMeta["metadatum.rb\nConan metadata per package"]:::core
      pkgFileMeta["file_metadatum.rb\nMetadata about individual Conan package files"]:::core

      %% associations within aggregates
      pkgConan --> pkgConanPkg
      pkgConanPkg --> pkgMeta
      pkgConanPkg --> pkgRecipeRev
      pkgConanPkg --> pkgConanRef
      pkgConanPkg --> pkgConanRev
      pkgConanRef --> pkgConanPkg
      pkgConanRev --> pkgConanPkg
      pkgConanRev --> pkgConanRef
      pkgRecipeRev --> pkgConanPkg
      pkgRecipeRev --> pkgConanRef
      pkgMeta --> pkgConanPkg
      pkgFileMeta --> pkgConanRev
      pkgFileMeta --> dataPkgFile
    end

    %% ---------- SUPPORTING DATA STRUCTURES ----------
    subgraph sgDataStructs["Domain Data Structures"]
      direction TB
      class sgDataStructs gsubgraph

      dataPkgFile["package_file\nRepresents stored files"]:::data
      dataProject["project\nProject owner context"]:::data
      dataShaAttr["ShaAttribute\nShared SHA logic"]:::util

      pkgConanRef --> dataShaAttr
      pkgConanRev --> dataShaAttr
      pkgRecipeRev --> dataShaAttr
    end
    pkgConanPkg --> dataProject
    pkgConanRef --> pkgRecipeRev
    pkgConanRev --> dataProject
    pkgRecipeRev --> dataProject

    %% ---------- FINDERS ----------
    subgraph sgFinders["Finders"]
      direction TB
      class sgFinders gsubgraph

      finderFiles[/"package_files_finder.rb"/\nFind package files]:::util
      finderFile[/"package_file_finder.rb"/\nFind specific package file]:::util
      finderPkg[/"package_finder.rb"/\nFind Conan packages by params]:::util

      finderFile --> finderFiles
      finderFiles --> pkgConanPkg
      finderFiles --> pkgConanRef
      finderFiles --> pkgRecipeRev
      finderFiles --> pkgConanRev
      finderPkg --> pkgConanPkg
    end

    %% ---------- SERVICES ----------
    subgraph sgServices["Services"]
      direction TB
      class sgServices gsubgraph

      serviceCreatePkg[/"create_package_service.rb"/\nCreate package aggregate]:::core
      serviceCreateFile[/"create_package_file_service.rb"/\nUpload and link package file]:::core
      serviceUpsertRecipeRev[/"upsert_recipe_revision_service.rb"/\nCreate or update recipe revision]:::core
      serviceUpsertPkgRev[/"upsert_package_revision_service.rb"/\nCreate or update package revision]:::core
      serviceUpsertPkgRef[/"upsert_package_reference_service.rb"/\nCreate or update package reference]:::core
      serviceExtractMeta[/"metadata_extraction_service.rb"/\nExtract metadata from Conan files]:::util
      serviceSearch[/"search_service.rb"/\nImplement Conan search logic]:::core

      serviceCreatePkg --> pkgConanPkg
      serviceCreatePkg --> pkgMeta
      serviceCreateFile --> pkgConanPkg
      serviceCreateFile --> pkgFileMeta
      serviceCreateFile --> pkgConanRef
      serviceCreateFile --> pkgRecipeRev
      serviceCreateFile --> pkgConanRev
      serviceUpsertRecipeRev --> pkgRecipeRev
      serviceUpsertRecipeRev --> pkgConanPkg
      serviceUpsertPkgRev --> pkgConanRev
      serviceUpsertPkgRev --> pkgConanPkg
      serviceUpsertPkgRev --> pkgConanRef
      serviceUpsertPkgRef --> pkgConanRef
      serviceUpsertPkgRef --> pkgConanPkg
      serviceUpsertPkgRef --> pkgRecipeRev
      serviceExtractMeta --> pkgFileMeta
      serviceExtractMeta --> dataPkgFile
      serviceSearch --> pkgConanPkg
      serviceSearch --> pkgMeta
    end

    %% ---------- PRESENTERS ----------
    subgraph sgPresenters["Presenters"]
      direction TB
      class sgPresenters gsubgraph

      presenterPkg[/"package_presenter.rb"/\nExpose Conan package data to API]:::util
      presenterPkg --> pkgConanPkg
      presenterPkg --> pkgMeta
      presenterPkg --> pkgConanRev
      presenterPkg --> dataPkgFile
      presenterPkg --> entityUploadUrls
      presenterPkg --> entityRecipeSnap
      presenterPkg --> entityPkgSnap
    end

    %% ---------- POLICIES ----------
    subgraph sgPolicies["Policies"]
      direction TB
      class sgPolicies gsubgraph
      policyMeta[/"metadatum_policy.rb"/\nControl access to metadata]:::util
      policyFileMeta[/"file_metadatum_policy.rb"/\nControl access to file metadata]:::util

      policyMeta --> pkgMeta
      policyFileMeta --> pkgFileMeta
    end

    %% ---------- WORKERS ----------
    subgraph sgWorkers["Workers"]
      direction TB
      class sgWorkers gsubgraph
      workerProcPkgFile[/"process_package_file_worker.rb"/\nBackground file processing]:::init
      workerProcPkgFile --> pkgFileMeta
      workerProcPkgFile --> serviceExtractMeta
      workerProcPkgFile --> pkgConanRev
      workerProcPkgFile --> pkgConanRef
    end

    %% ---------- API ENDPOINTS ----------
    subgraph sgAPI["API Interfaces"]
      direction TB
      class sgAPI gsubgraph

      apiInstanceV1[/"instance_packages.rb"/\nAPI endpoint for packages]:::api
      concernV1Endpoints[/"v1_endpoints.rb"/\nConan V1 API implementation]:::api
      concernShared[/"shared_endpoints.rb"/\nCommon Conan API endpoint logic]:::api

      apiInstanceV1 --> concernV1Endpoints
      concernV1Endpoints --> concernShared
      concernV1Endpoints --> serviceSearch
      concernV1Endpoints --> serviceCreatePkg
      concernV1Endpoints --> finderPkg
      concernV1Endpoints --> serviceCreateFile
      concernV1Endpoints --> presenterPkg

      subgraph sgEntities["API Entities"]
        direction TB
        class sgEntities gsubgraph

        entityUploadUrls[/"upload_urls.rb"/\nProvides upload URLs]:::entity
        entityRecipeRev[/"recipe_revisions.rb"/\nExposes recipe revisions]:::entity
        entityRecipeSnap[/"recipe_snapshot.rb"/\nDescribes file snapshot]:::entity
        entityRecipeManifest[/"recipe_manifest.rb"/\nDescribes manifest URLs]:::entity
        entityPkgSnap[/"package_snapshot.rb"/\nExposes package snapshot]:::entity
        entityPkgManifest[/"package_manifest.rb"/\nDescribes package manifest URLs]:::entity
        entityRevision[/"revision.rb"/\nExposes revision properties]:::entity

        entityRecipeRev --> entityRevision
        entityRecipeSnap --> entityRevision
        entityPkgSnap --> entityRevision
      end

      apiInstanceV1 --> entityRecipeRev
      apiInstanceV1 --> entityRecipeSnap
      apiInstanceV1 --> entityUploadUrls
      apiInstanceV1 --> entityRecipeManifest
      apiInstanceV1 --> entityPkgSnap
      apiInstanceV1 --> entityPkgManifest
    end

    %% ---------- TOKENS & AUTH ----------
    subgraph sgAuth["Domain Auth / Tokens"]
      direction TB
      class sgAuth gsubgraph

      conanToken[/"conan_token.rb"/\nConan token generation and validation]:::util
      conanToken --> apiInstanceV1
    end


    %% ---------- FIXTURES, EXAMPLES, QA ----------
    subgraph sgFixtures["Fixtures & QA"]
      direction TB
      class sgFixtures gsubgraph
      fixtureConanfilePy[/"conanfile.py"/\nExample Conan recipe file]:::data
      fixtureQARepoSpec[/"conan_repository_spec.rb"/\nQA spec for Conan repository]:::init
    end

    fixtureConanfilePy --> serviceExtractMeta

  end

  %% ---------- COLLABORATIVE INTERACTIONS (EDGES ONLY, NO NODES) ----------
  %% Data flow from file upload through service and worker to aggregates:
  dataPkgFile --> serviceCreateFile
  serviceCreateFile --> workerProcPkgFile
  workerProcPkgFile --> serviceUpsertPkgRev
  workerProcPkgFile --> serviceUpsertPkgRef
  workerProcPkgFile --> pkgConanPkg

  %% Services update domain data structures and aggregates
  serviceCreatePkg --> serviceUpsertPkgRef
  serviceCreatePkg --> serviceUpsertRecipeRev

  %% Presenters use API entities to serialize aggregates to API response
  presenterPkg --> entityRecipeManifest
  presenterPkg --> entityPkgManifest

  %% API entity relationships
  entityUploadUrls --> entityPkgManifest
  entityRecipeSnap --> entityRecipeManifest
  entityPkgSnap --> entityPkgManifest

  %% API endpoints call presenters and services
  apiInstanceV1 --> serviceCreatePkg
  apiInstanceV1 --> serviceSearch

  %% Finders hand off to services to perform operations
  finderPkg --> serviceSearch
  finderFiles --> finderPkg

  %% QA spec references API & domain aggregates
  fixtureQARepoSpec --> apiInstanceV1
  fixtureQARepoSpec --> pkgConanPkg
  fixtureQARepoSpec --> serviceCreatePkg

  %% Conan tokens used in auth layers for API and service actions
  conanToken --> serviceCreatePkg
  conanToken --> serviceSearch

  %% Entities expose core aggregates
  entityRevision --> pkgConanRev
  entityRevision --> pkgRecipeRev
  entityRevision --> pkgConanRef

  %% Policies check permissions of domain data
  policyMeta --> policyFileMeta

  %% Metadata extraction transforms input files to domain data
  serviceExtractMeta --> pkgMeta

  %% Recipe files feed into metadata extraction for package creation
  fixtureConanfilePy --> serviceExtractMeta

  %% Draw subtle grouping for setup & initialization
  pkgConanPkg -->|Initializes associations| pkgMeta
  pkgConanPkg -->|Initializes associations| pkgRecipeRev
  pkgConanPkg -->|Initializes associations| pkgConanRef

  %% Additional associations for search/service boundaries
  serviceSearch --> finderPkg
  serviceSearch --> pkgMeta
  serviceSearch --> pkgConanPkg
  serviceSearch --> presenterPkg

  %% Conan referential integrity
  pkgConanRef -->|References| pkgRecipeRev
  pkgConanRev -->|References| pkgConanRef

%% Styles for subgraphs and spacing
  class sgCore,sgDataStructs,sgFinders,sgServices,sgPresenters,sgPolicies,sgWorkers,sgAPI,sgEntities,sgAuth,sgFixtures gsubgraph;
```