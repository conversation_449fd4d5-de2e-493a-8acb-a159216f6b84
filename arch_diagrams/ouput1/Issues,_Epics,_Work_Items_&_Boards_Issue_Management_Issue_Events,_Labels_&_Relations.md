```mermaid
%% VERTICAL LOGICAL ARCHITECTURE DIAGRAM FOR ISSUES, EPICS, WORK ITEMS, BOARDS, ISSUE EVENTS, LABELS & RELATIONS
flowchart TD
    %% ==== BASE STYLES ====
    classDef core fill:#D4F1F9,color:#1D2436,stroke:#A3D8EF,stroke-width:2px,stroke-dasharray:0,rx:12,ry:12
    classDef support fill:#FFF8DC,color:#7E6000,stroke:#FFE7A4,stroke-width:2px,stroke-dasharray:0,rx:12,ry:12
    classDef datastruct fill:#E0F8E0,color:#3B583B,stroke:#C4ECCC,stroke-width:2px,stroke-dasharray:0,rx:12,ry:12
    classDef error fill:#FFE4E1,color:#91494A,stroke:#FFC6C2,stroke-width:2px,stroke-dasharray:0,rx:12,ry:12
    classDef init fill:#E6E6FA,color:#4B4752,stroke:#CABFF7,stroke-width:2px,stroke-dasharray:0,rx:12,ry:12
    classDef logicalgroup fill:#F8F8F8,color:#2B2B2B,stroke:#D4F1F9,stroke-width:2px,stroke-dasharray:0,rx:18,ry:18

    %% ==== DOMAIN CONCEPTS / LOGICAL GROUPS ====
    subgraph DomainCore["Core Concepts" ]
    direction TB
        SUB_CORRES[ "Issue/Epic/Work Item Core Models" ]
        SUB_LABELCORE["Label Models"]
        SUB_EVENTS["Resource & State Events"]
        SUB_RELATIONS["Issue/Work Item Relations"]
        SUB_LABELREL["Label ↔ Issue Relations"]
        SUB_SERVICES["Core Services"]
    end
    class DomainCore logicalgroup

    subgraph LabelLogic["Label/Board/Label-Related Logic"]
    direction TB
        SUB_LABELSERVICES["Label CRUD/Filter Services"]
        SUB_LABELUTILS["Label Utilities"]
        SUB_LABELPOLICIES["Label Policies"]
        SUB_LABELQA["Label QA/Automation"]
    end
    class LabelLogic logicalgroup

    subgraph EventLogic["Issue, Label, Resource Event Logic"]
    direction TB
        SUB_EVENTMODELS["Event Models"]
        SUB_EVENTAPI["Resource Event API"]
        SUB_EVENTSERVICES["Event Processing Services"]
        SUB_EVENTEE["EE/Enterprise Event Extensions"]
    end
    class EventLogic logicalgroup

    subgraph IssueRelationsLogic["Issue Link/Relation Logic"]
    direction TB
        SUB_ISSUELINKCONTROLLERS["Issue Link Controllers"]
        SUB_ISSUELINKSERVICES["Issue Link Services"]
        SUB_ISSUELINKAPI["Issue Link API"]
        SUB_ISSUELINKEE["EE/Enterprise Issue Relations"]
    end
    class IssueRelationsLogic logicalgroup

    subgraph UtilitySupport["Supporting Concerns, Finders, Helpers"]
    direction TB
        SUB_CONCERNS["Model Concerns"]
        SUB_FINDERS["Finder/Query Concerns"]
        SUB_HELPERS["Helpers"]
    end
    class UtilitySupport logicalgroup

    subgraph Initialization["Initialization, Policies, Serializers"]
    direction TB
        SUB_INIT["Initialization Sequence"]
        SUB_POLICIES["Policies"]
        SUB_SERIALIZERS["Serializers"]
        SUB_GRAPHQL["GraphQL Types/Resolvers"]
    end
    class Initialization logicalgroup

    subgraph QAandTests["QA/Test Support"]
    direction TB
        SUB_QA["QA: Label/Board/Issue"]
        SUB_TESTS["RSpec & shared test files"]
    end
    class QAandTests logicalgroup

    subgraph DataMigration["Bulk Import, Data Pipelines"]
    direction TB
        SUB_PIPELINES["Bulk Import Pipelines"]
    end
    class DataMigration logicalgroup

    %% ==== CORE MODELS & CONCEPTS ====
    SUB_CORRES-->|Implements core domain entities|res_event[ "resource_event.rb" ]
    SUB_CORRES-->|Model for Issue/Epic state event|res_state_event[ "resource_state_event.rb" ]
    SUB_CORRES-->|Polymorphic position for diff |diff_note_position[ "diff_note_position.rb" ]
    SUB_CORRES-->|Epics, work items|label_priority[ "label_priority.rb" ]
    SUB_CORRES-->|Core labels|label[ "label.rb" ]
    SUB_CORRES-->|Project labels|project_label[ "project_label.rb" ]
    SUB_CORRES-->|Label Note|label_note[ "label_note.rb" ]

    class res_event, res_state_event, diff_note_position, label_priority, label, project_label, label_note core

    %% ==== LABEL RELATIONSHIPS & DATA STRUCTURES ====
    SUB_LABELREL-->|Label/issuable join|label_link[ "label_link.rb" ]
    SUB_LABELREL-->|Domain anti-abuse|ab_label[ "anti_abuse/reports/label.rb" ]
    SUB_LABELREL-->|Abuse user mentions|ab_user_mention[ "anti_abuse/reports/user_mention.rb" ]
    SUB_LABELREL-->|Abuse event|ab_event[ "anti_abuse/event.rb" ]

    class label_link, ab_label, ab_user_mention, ab_event datastruct

    %% ==== RESOURCE EVENTS/STATE EVENTS ====
    SUB_EVENTS-->|Resource -> State|resource_timebox_event[ "resource_timebox_event.rb" ]
    SUB_EVENTS-->|EE Weight Events|resource_weight_event[ "resource_weight_event.rb" ]
    SUB_EVENTS-->|Resource Label Event|resource_label_event[ "resource_label_event.rb" ]
    SUB_EVENTS-->|EE Resource Label Event extension|ee_resource_label_event[ "ee/resource_label_event.rb" ]

    class resource_timebox_event, resource_weight_event, resource_label_event, ee_resource_label_event core

    %% ==== RELATIONS ISSUE/WORK ITEM LINKS ====
    SUB_RELATIONS-->|Assignment Events|mr_assignment_event[ "resource_events/merge_request_assignment_event.rb" ]
    SUB_RELATIONS-->|Assignment Events|issue_assignment_event[ "resource_events/issue_assignment_event.rb" ]
    SUB_RELATIONS-->|Abuse Report Event|abuse_report_event[ "resource_events/abuse_report_event.rb" ]

    class mr_assignment_event, issue_assignment_event, abuse_report_event datastruct

    %% ==== SERVICES CORE ====
    SUB_SERVICES-->|Destroy label links|destroy_label_links_service[ "issuable/destroy_label_links_service.rb" ]
    SUB_SERVICES-->|Assignment Processing|process_assignees[ "issuable/process_assignees.rb" ]
    SUB_SERVICES-->|Issuable deletion|destroy_service[ "issuable/destroy_service.rb" ]

    class destroy_label_links_service, process_assignees, destroy_service core

    %% ==== LABEL-RELATED LOGIC/SERVICES ====
    SUB_LABELSERVICES-->|Label create|label_create_service[ "labels/create_service.rb" ]
    SUB_LABELSERVICES-->|Available labels finder|available_labels_service[ "labels/available_labels_service.rb" ]
    SUB_LABELSERVICES-->|Find or create label|find_or_create_service[ "labels/find_or_create_service.rb" ]
    SUB_LABELSERVICES-->|Promote label|ee_promote_service[ "ee/labels/promote_service.rb" ]
    SUB_LABELSERVICES-->|Label bulk assign/board logic|labels_pipeline[ "bulk_imports/common/pipelines/labels_pipeline.rb" ]

    class label_create_service, available_labels_service, find_or_create_service, ee_promote_service, labels_pipeline support

    SUB_LABELUTILS-->|Helper rendering|labels_helper[ "helpers/labels_helper.rb" ]
    SUB_LABELUTILS-->|EE helper rendering|ee_labels_helper[ "ee/helpers/ee/labels_helper.rb" ]
    SUB_LABELUTILS-->|Label entity serializer|label_entity[ "serializers/label_entity.rb" ]

    class labels_helper, ee_labels_helper support
    class label_entity datastruct

    SUB_LABELPOLICIES-->|Label Authorization|project_label_policy[ "policies/project_label_policy.rb" ]
    SUB_LABELPOLICIES-->|Label Authorization|group_label_policy[ "policies/group_label_policy.rb" ]
    SUB_LABELPOLICIES-->|Label Event Policy|resource_label_event_policy[ "policies/resource_label_event_policy.rb" ]

    class project_label_policy, group_label_policy, resource_label_event_policy init

    %% ==== EVENT LOGIC, API, SERVICES ====
    SUB_EVENTMODELS-->|EE ScopedLabelSet|scoped_label_set[ "ee/models/scoped_label_set.rb" ]

    class scoped_label_set core

    SUB_EVENTAPI-->|Resource Events API|api_resource_events[ "api/resource_label_events.rb" ]  
    SUB_EVENTAPI-->|Resource State Events API|api_resource_state_events[ "api/resource_state_events.rb" ]

    class api_resource_events, api_resource_state_events support

    SUB_EVENTSERVICES-->|Change State|change_state_service[ "resource_events/change_state_service.rb" ]
    SUB_EVENTSERVICES-->|Change Labels|change_labels_service[ "resource_events/change_labels_service.rb" ]
    SUB_EVENTSERVICES-->|Merge into Notes|merge_into_notes_service[ "resource_events/merge_into_notes_service.rb" ]
    SUB_EVENTSERVICES-->|Synthetic Notes Base|base_change_timebox_service[ "resource_events/base_change_timebox_service.rb" ]

    class change_state_service, change_labels_service, merge_into_notes_service, base_change_timebox_service support

    SUB_EVENTEE-->|EE Notes Builder|ee_synthetic_weight_notes_builder[ "ee/resource_events/synthetic_weight_notes_builder_service.rb" ]
    SUB_EVENTEE-->|EE Notes Builder|ee_synthetic_iteration_notes_builder[ "ee/resource_events/synthetic_iteration_notes_builder_service.rb" ]
    SUB_EVENTEE-->|EE Change Labels Service|ee_change_labels_service[ "ee/resource_events/change_labels_service.rb" ]
    SUB_EVENTEE-->|Synthetic Notes Service|synthetic_notes_service[ "resource_events/synthetic_notes_service.rb" ]  

    class ee_synthetic_weight_notes_builder, ee_synthetic_iteration_notes_builder, ee_change_labels_service, synthetic_notes_service support

    %% ==== ISSUE RELATIONS LINKS, API, CONTROLLERS ====
    SUB_ISSUELINKCONTROLLERS-->|Controller concerns|issuable_links[ "controllers/concerns/issuable_links.rb" ]
    SUB_ISSUELINKCONTROLLERS-->|Projects IssueLinks|projects_issue_links[ "controllers/projects/issue_links_controller.rb" ]

    class issuable_links, projects_issue_links support

    SUB_ISSUELINKSERVICES-->|Create issue links|issue_links_create_service[ "issue_links/create_service.rb" ]
    SUB_ISSUELINKSERVICES-->|Destroy issue links|issue_links_destroy_service[ "issue_links/destroy_service.rb" ]
    SUB_ISSUELINKSERVICES-->|Base destroy service|issuable_links_destroy_service[ "issuable_links/destroy_service.rb" ]
    SUB_ISSUELINKSERVICES-->|Base create service|issuable_links_create_service[ "issuable_links/create_service.rb" ]
    SUB_ISSUELINKSERVICES-->|List Service|issuable_links_list_service[ "issuable_links/list_service.rb" ]

    class issue_links_create_service, issue_links_destroy_service, issuable_links_destroy_service, issuable_links_create_service, issuable_links_list_service core

    SUB_ISSUELINKAPI-->|Issue Links API|api_issue_links[ "api/issue_links.rb" ]  

    class api_issue_links support

    SUB_ISSUELINKEE-->|EE IssueLinks Service|ee_issue_links_create_service[ "ee/issue_links/create_service.rb" ]
    SUB_ISSUELINKEE-->|EE Issuable Links Service|ee_issuable_links_create_service[ "ee/issuable_links/create_service.rb" ]
    SUB_ISSUELINKEE-->|FeatureFlag Issue Links|feature_flag_issues_destroy[ "feature_flag_issues/destroy_service.rb" ]
    SUB_ISSUELINKEE-->|Issue FeatureFlags List|issue_feature_flags_list[ "issue_feature_flags/list_service.rb" ]

    class ee_issue_links_create_service, ee_issuable_links_create_service, feature_flag_issues_destroy, issue_feature_flags_list support

    %% ==== UTILITY SUPPORT LAYER ====
    SUB_CONCERNS-->|Issuable Concern|issuable_concern[ "models/concerns/issuable.rb" ]
    SUB_CONCERNS-->|Taggable Queries|taggable_queries[ "models/concerns/taggable_queries.rb" ]
    SUB_CONCERNS-->|IssueResourceEvent|issue_resource_event[ "models/concerns/issue_resource_event.rb" ]

    class issuable_concern, taggable_queries, issue_resource_event support

    SUB_FINDERS-->|MergedAt Filter|merged_at_filter[ "finders/concerns/merged_at_filter.rb" ]
    SUB_FINDERS-->|EE Label Filter|ee_label_filter[ "ee/finders/ee/issuables/label_filter.rb" ]

    class merged_at_filter, ee_label_filter support

    SUB_HELPERS-->|Labels Helper|labels_helper[ "helpers/labels_helper.rb" ]
    SUB_HELPERS-->|EE Labels Helper|ee_labels_helper[ "ee/helpers/ee/labels_helper.rb" ]

    class labels_helper, ee_labels_helper support

    %% ==== INITIALIZATION, POLICIES, SERIALIZERS, GRAPHQL ====
    SUB_INIT-->|Label as Hash|labels_as_hash[ "controllers/concerns/labels_as_hash.rb" ]
    SUB_INIT-->|Issues Calendar|issues_calendar[ "controllers/concerns/issues_calendar.rb" ]

    class labels_as_hash, issues_calendar init

    SUB_POLICIES-->|ResourceLabelEvent Policy|resource_label_event_policy[ "policies/resource_label_event_policy.rb" ]
    class resource_label_event_policy init

    SUB_SERIALIZERS-->|Dependency List|dependency_list_serializer[ "ee/serializers/dependency_list_serializer.rb" ]

    class dependency_list_serializer support

    SUB_GRAPHQL-->|LabelsResolver|labels_resolver[ "graphql/resolvers/labels_resolver.rb" ]
    SUB_GRAPHQL-->|Health Status Enum|health_status_filter_enum[ "graphql/types/health_status_filter_enum.rb" ]


    class labels_resolver, health_status_filter_enum support

    %% ==== QA, TEST, SHARED EXAMPLES ====
    SUB_QA-->|QA Label New|qa_label_new[ "qa/page/label/new.rb" ]
    SUB_QA-->|QA Label Index|qa_label_index[ "qa/page/label/index.rb" ]
    SUB_QA-->|QA LabelResource|qa_label_base[ "qa/resource/label_base.rb" ]
    SUB_QA-->|QA EE Board|ee_board_list[ "qa/ee/resource/board/board_list/project/base_board_list.rb" ]

    class qa_label_new, qa_label_index, qa_label_base, ee_board_list support

    SUB_TESTS-->|Label Creation|user_creates_labels_spec[ "spec/features/projects/labels/user_creates_labels_spec.rb" ]
    SUB_TESTS-->|Labels Subscription - Project|labels_subscription_spec[ "spec/features/projects/labels/subscription_spec.rb" ]
    SUB_TESTS-->|Labels Subscription - Group|group_labels_subscription_spec[ "spec/features/groups/labels/subscription_spec.rb" ]
    SUB_TESTS-->|Sidebar Shared|sidebar_labels_shared_examples[ "spec/support/shared_examples/features/sidebar/sidebar_labels_shared_examples.rb" ]
    SUB_TESTS-->|Controller Issue Links|issue_links_controller_spec[ "spec/controllers/projects/issue_links_controller_spec.rb" ]
    SUB_TESTS-->|Controller Labels|labels_controller_spec[ "spec/controllers/projects/labels_controller_spec.rb" ]

    class user_creates_labels_spec, labels_subscription_spec, group_labels_subscription_spec, sidebar_labels_shared_examples, issue_links_controller_spec, labels_controller_spec support

    %% ==== DATA PIPELINES & MIGRATION ====
    SUB_PIPELINES-->|Bulk Import Label|labels_pipeline[ "bulk_imports/common/pipelines/labels_pipeline.rb" ]
    class labels_pipeline support

    %% ==== RELATIONSHIP ARROWS ====
    %% Domain Models and Relationships
    label-->|has|project_label
    label-->|has_many|label_link
    label_link-->|belongs_to|label
    label_link-->|links to|res_event
    project_label-->|inherits|label
    label_note-->|references|label
    resource_label_event-->|references|label
    resource_label_event-->|EE extension|ee_resource_label_event
    resource_timebox_event-->|inherited by|res_event
    resource_state_event-->|inherited by|res_event
    resource_weight_event-->|inherits|res_event

    %% Resource Event Model Relationships
    resource_label_event-->|triggers|change_labels_service
    resource_label_event-->|processed by|change_state_service
    resource_timebox_event-->|processed by|base_change_timebox_service
    resource_weight_event-->|processed by|ee_synthetic_weight_notes_builder

    %% Label/Issue Services
    destroy_label_links_service-->|used_by|destroy_service
    process_assignees-->|used_by|destroy_service
    available_labels_service-->|used_by|label_create_service
    find_or_create_service-->|used_by|label_create_service
    label_create_service-->|creates|label

    %% Label APIs and Serialization
    api_resource_events-->|serves|resource_label_event
    api_resource_state_events-->|serves|resource_state_event
    label_entity-->|serializes|label
    labels_resolver-->|resolves|label

    %% Helpers and Utilities
    labels_helper-->|used_by|project_label_policy
    ee_labels_helper-->|extends|labels_helper

    %% Policies and Authorization
    project_label_policy-->|authorizes|project_label
    group_label_policy-->|authorizes|project_label
    resource_label_event_policy-->|authorizes|resource_label_event

    %% Issue Link Relationships
    issue_links_create_service-->|inherits|issuable_links_create_service
    issue_links_destroy_service-->|inherits|issuable_links_destroy_service
    issue_links_create_service-->|used_by|projects_issue_links
    issue_links_destroy_service-->|used_by|projects_issue_links
    issuable_links_list_service-->|used_by|projects_issue_links
    projects_issue_links-->|includes|issuable_links

    %% EE/Enterprise Linkage
    ee_issuable_links_create_service-->|overrides|issuable_links_create_service
    ee_issue_links_create_service-->|overrides|issue_links_create_service
    feature_flag_issues_destroy-->|inherits|issuable_links_destroy_service
    issue_feature_flags_list-->|inherits|issuable_links_list_service

    %% Model Concerns and Data Flows
    issuable_concern-->|included in|res_event
    taggable_queries-->|extended by|label
    issue_resource_event-->|concern for|resource_event

    %% Synthetic Notes and Builder Services
    merge_into_notes_service-->|uses|synthetic_notes_service
    synthetic_notes_service-->|base class for|ee_synthetic_weight_notes_builder
    synthetic_notes_service-->|base class for|ee_synthetic_iteration_notes_builder

    %% Finder, Filters, Pipelines
    merged_at_filter-->|used by|issue_links_create_service
    ee_label_filter-->|overrides|merged_at_filter
    labels_pipeline-->|uses|label

    %% QA Automation and Testing Connectivity
    qa_label_new-->|tests|label_create_service
    qa_label_index-->|tests|labels_helper
    qa_label_base-->|base for|qa_label_new
    ee_board_list-->|ee QA for|label

    user_creates_labels_spec-->|tests|label_create_service
    labels_subscription_spec-->|tests|labels_helper
    group_labels_subscription_spec-->|tests|labels_helper
    sidebar_labels_shared_examples-->|shared_example for|labels_helper
    labels_controller_spec-->|tests|projects_issue_links
    issue_links_controller_spec-->|tests|projects_issue_links

    %% GraphQL, Usage Counter
    labels_resolver-->|used by|label
    health_status_filter_enum-->|used by|res_event

    %% Misc Connections
    ab_label-->|anti-abuse|label_link
    ab_user_mention-->|anti-abuse|label
    ab_event-->|anti-abuse|label

    %% Specialized Domain Entities
    dependency_list_serializer-->|serializes|label
    scoped_label_set-->|groups|label

    %% Bulk Import
    labels_pipeline-->|imports/exports|label

    %% End of Diagram
```