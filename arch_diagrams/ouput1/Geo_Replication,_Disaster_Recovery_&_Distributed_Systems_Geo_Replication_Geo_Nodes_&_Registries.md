```mermaid
flowchart TD
  %% Styling
  %% Core domain files - pastel blue
  classDef core fill:#D4F1F9,stroke:#8AC6D1,stroke-width:2px,color:#222,stroke-dasharray: 0,rx:16px,ry:16px
  %% Supporting/utility files - pastel yellow
  classDef support fill:#FFF8DC,stroke:#FFE69A,stroke-width:2px,color:#444,rx:16px,ry:16px
  %% Data Structures - pastel green
  classDef data fill:#E0F8E0,stroke:#93D65E,stroke-width:2px,color:#222,rx:16px,ry:16px
  %% Error handling - pastel red
  classDef error fill:#FFE4E1,stroke:#FFB4A2,stroke-width:2px,color:#744,rx:16px,ry:16px
  %% Initialization/setup - pastel purple
  classDef setup fill:#E6E6FA,stroke:#B7B7E2,stroke-width:2px,color:#222,rx:16px,ry:16px
  %% Logical Grouping/Subgraphs
  classDef grouping fill:#F8F8F8,color:#222,stroke:#DDD,stroke-width:2px

  %% Top-level group: Geo Replication/Geo Nodes & Registries
  subgraph Geo_Replication_Geo_Nodes_and_Registries["Geo Replication - Geo Nodes & Registries"]
    direction TB

    %% 1. Core Domain Models: Geo Nodes & Status
    subgraph Geo_Nodes_And_Status["Geo Nodes & Status"]
      direction TB
      GN1[GeoNode\nee/app/models/geo_node.rb]:::core
      GN2[GeoNodeStatus\nee/app/models/geo_node_status.rb]:::core
      GN3[GeoNodeNamespaceLink\nee/app/models/geo_node_namespace_link.rb]:::core
    end
    class Geo_Nodes_And_Status grouping

    %% 2. Registries for Replicable Resources
    subgraph Registries["Registries Track State & Sync"]
      direction TB
      R1[ProjectRepositoryRegistry\nee/app/models/geo/project_repository_registry.rb]:::core
      R2[ProjectWikiRepositoryRegistry\nee/app/models/geo/project_wiki_repository_registry.rb]:::core
      R3[SnippetRepositoryRegistry\nee/app/models/geo/snippet_repository_registry.rb]:::core
      R4[PagesDeploymentRegistry\nee/app/models/geo/pages_deployment_registry.rb]:::core
      R5[MergeRequestDiffRegistry\nee/app/models/geo/merge_request_diff_registry.rb]:::core
      R6[GroupWikiRepositoryRegistry\nee/app/models/geo/group_wiki_repository_registry.rb]:::core
      R7[LfsObjectRegistry\nee/app/models/geo/lfs_object_registry.rb]:::core
      R8[JobArtifactRegistry\nee/app/models/geo/job_artifact_registry.rb]:::core
      R9[PackageFileRegistry\nee/app/models/geo/package_file_registry.rb]:::core
      R10[TerraformStateVersionRegistry\nee/app/models/geo/terraform_state_version_registry.rb]:::core
      R11[CiSecureFileRegistry\nee/app/models/geo/ci_secure_file_registry.rb]:::core
      R12[ContainerRepositoryRegistry\nee/app/models/geo/container_repository_registry.rb]:::core
      R13[DependencyProxyBlobRegistry\nee/app/models/geo/dependency_proxy_blob_registry.rb]:::core
      R14[DependencyProxyManifestRegistry\nee/app/models/geo/dependency_proxy_manifest_registry.rb]:::core
      R15[UploadRegistry\nee/app/models/geo/upload_registry.rb]:::core
      R16[DesignManagementRepositoryRegistry\nee/app/models/geo/design_management_repository_registry.rb]:::core
    end
    class Registries grouping

    %% 3. Registry State Tracking
    subgraph Registry_States["Registry State Tracking"]
      direction TB
      S1[ProjectState\nee/app/models/geo/project_state.rb]:::data
      S2[WikiRepositoryState\nee/app/models/geo/wiki_repository_state.rb]:::data
      S3[GroupWikiRepositoryState\nee/app/models/geo/group_wiki_repository_state.rb]:::data
      S4[DesignManagementRepositoryState\nee/app/models/geo/design_management_repository_state.rb]:::data
      S5[PagesDeploymentState\nee/app/models/geo/pages_deployment_state.rb]:::data
      S6[DependencyProxyBlobState\nee/app/models/geo/dependency_proxy_blob_state.rb]:::data
      S7[UploadState\nee/app/models/geo/upload_state.rb]:::data
      S8[ContainerRepositoryState\nee/app/models/geo/container_repository_state.rb]:::data
      S9[LfsObjectState\nee/app/models/geo/lfs_object_state.rb]:::data
      S10[SnippetRepositoryState\nee/app/models/geo/snippet_repository_state.rb]:::data
      S11[CiSecureFileState\nee/app/models/geo/ci_secure_file_state.rb]:::data
    end
    class Registry_States grouping

    %% 4. Registry Management/Finders
    subgraph Finders_and_Frameworks["Finder Classes Queries/Filters"]
      direction TB
      F1[FrameworkRegistryFinder\nee/app/finders/geo/framework_registry_finder.rb]:::support
      F2[ProjectRepositoryRegistryFinder\nee/app/finders/geo/project_repository_registry_finder.rb]:::support
      F3[ProjectWikiRepositoryRegistryFinder\nee/app/finders/geo/project_wiki_repository_registry_finder.rb]:::support
      F4[SnippetRepositoryRegistryFinder\nee/app/finders/geo/snippet_repository_registry_finder.rb]:::support
      F5[PagesDeploymentRegistryFinder\nee/app/finders/geo/pages_deployment_registry_finder.rb]:::support
      F6[MergeRequestDiffRegistryFinder\nee/app/finders/geo/merge_request_diff_registry_finder.rb]:::support
      F7[GroupWikiRepositoryRegistryFinder\nee/app/finders/geo/group_wiki_repository_registry_finder.rb]:::support
      F8[LfsObjectRegistryFinder\nee/app/finders/geo/lfs_object_registry_finder.rb]:::support
      F9[JobArtifactRegistryFinder\nee/app/finders/geo/job_artifact_registry_finder.rb]:::support
      F10[PackageFileRegistryFinder\nee/app/finders/geo/package_file_registry_finder.rb]:::support
      F11[TerraformStateVersionRegistryFinder\nee/app/finders/geo/terraform_state_version_registry_finder.rb]:::support
      F12[CiSecureFileRegistryFinder\nee/app/finders/geo/ci_secure_file_registry_finder.rb]:::support
      F13[DependencyProxyBlobRegistryFinder\nee/app/finders/geo/dependency_proxy_blob_registry_finder.rb]:::support
      F14[DependencyProxyManifestRegistryFinder\nee/app/finders/geo/dependency_proxy_manifest_registry_finder.rb]:::support
      F15[UploadRegistryFinder\nee/app/finders/geo/upload_registry_finder.rb]:::support
      F16[ContainerRepositoryRegistryFinder\nee/app/finders/geo/container_repository_registry_finder.rb]:::support
      F17[DesignManagementRepositoryRegistryFinder\nee/app/finders/geo/design_management_repository_registry_finder.rb]:::support
      F18[PipelineArtifactRegistryFinder\nee/app/finders/geo/pipeline_artifact_registry_finder.rb]:::support
      F19[RegistryFinder\nee/app/finders/geo/registry_finder.rb]:::support
    end
    class Finders_and_Frameworks grouping

    %% 5. GraphQL API Types & Enums
    subgraph GraphQL_Types["GraphQL Types/Enums"]
      direction TB
      GT1[GeoNodeType\nee/app/graphql/types/geo/geo_node_type.rb]:::support
      GT2[RegistrableType\nee/app/graphql/types/geo/registrable_type.rb]:::support
      GT3[RegistryStateEnum\nee/app/graphql/types/geo/registry_state_enum.rb]:::support
      GT4[RegistrySortEnum\nee/app/graphql/types/geo/registry_sort_enum.rb]:::support
      GT5[RegistryClassEnum\nee/app/graphql/types/geo/registry_class_enum.rb]:::support
      %% RegistryType objects
      GT6[ProjectRepositoryRegistryType]:::support
      GT7[ProjectWikiRepositoryRegistryType]:::support
      GT8[SnippetRepositoryRegistryType]:::support
      GT9[PagesDeploymentRegistryType]:::support
      GT10[MergeRequestDiffRegistryType]:::support
      GT11[GroupWikiRepositoryRegistryType]:::support
      GT12[LfsObjectRegistryType]:::support
      GT13[JobArtifactRegistryType]:::support
      GT14[PackageFileRegistryType]:::support
      GT15[TerraformStateVersionRegistryType]:::support
      GT16[CiSecureFileRegistryType]:::support
      GT17[ContainerRepositoryRegistryType]:::support
      GT18[DependencyProxyBlobRegistryType]:::support
      GT19[DependencyProxyManifestRegistryType]:::support
      GT20[DesignManagementRepositoryRegistryType]:::support
      GT21[PipelineArtifactRegistryType]:::support
    end
    class GraphQL_Types grouping

    %% 6. Core Geo Services
    subgraph Geo_Services["Core Geo Services"]
      direction TB
      GS1[SyncService\nee/app/services/geo/sync_service.rb]:::core
      GS2[RegistryUpdateService\nee/app/services/geo/registry_update_service.rb]:::core
      GS3[RepositoryRegistryRemovalService\nee/app/services/geo/repository_registry_removal_service.rb]:::core
      GS4[NodeStatusRequestService\nee/app/services/geo/node_status_request_service.rb]:::core
      GS5[NodeUpdateService\nee/app/services/geo/node_update_service.rb]:::core
      GS6[BlobDownloadService\nee/app/services/geo/blob_download_service.rb]:::core
      GS7[FileRegistryRemovalService\nee/app/services/geo/file_registry_removal_service.rb]:::core
      GS8[RegistryConsistencyService\nee/app/services/geo/registry_consistency_service.rb]:::core
      GS9[ContainerRepositorySync\nee/app/services/geo/container_repository_sync.rb]:::core
      GS10[ContainerRepositorySyncService\nee/app/services/geo/container_repository_sync_service.rb]:::core
    end
    class Geo_Services grouping

    %% 7. Worker/Job System
    subgraph Workers["Worker/Job Orchestration"]
      direction TB
      W1[SyncWorker\nee/app/workers/geo/sync_worker.rb]:::support
      W2[VerificationBatchWorker\nee/app/workers/geo/verification_batch_worker.rb]:::support
      W3[VerificationStateBackfillWorker\nee/app/workers/geo/verification_state_backfill_worker.rb]:::support
      W4[VerificationTimeoutWorker\nee/app/workers/geo/verification_timeout_worker.rb]:::support
      W5[DestroyWorker\nee/app/workers/geo/destroy_worker.rb]:::support
      W6[SidekiqCronConfigWorker\nee/app/workers/geo/sidekiq_cron_config_worker.rb]:::support
      W7[SecondaryUsageDataCronWorker\nee/app/workers/geo/secondary_usage_data_cron_worker.rb]:::support
      W8[VerificationCronWorker\nee/app/workers/geo/verification_cron_worker.rb]:::support
      W9[MetricsUpdateWorker\nee/app/workers/geo/metrics_update_worker.rb]:::support
      W10[ResyncDirectUploadJobArtifactRegistryWorker\nee/app/workers/geo/resync_direct_upload_job_artifact_registry_worker.rb]:::support
      W11[SyncTimeoutCronWorker\nee/app/workers/geo/sync_timeout_cron_worker.rb]:::support
      W12[ContainerRepositorySyncWorker\nee/app/workers/geo/container_repository_sync_worker.rb]:::support
    end
    class Workers grouping

    %% 8. Replication/Registry Abstractions
    subgraph Abstractions["Replication & Registry Abstractions"]
      direction TB
      A1[Replicator\nee/lib/gitlab/geo/replicator.rb]:::core
      A2[RegistryBatcher\nee/lib/gitlab/geo/registry_batcher.rb]:::support
      A3[Replication Base\nee/lib/gitlab/geo/replication.rb]:::support
      A4[TransferRequest\nee/lib/gitlab/geo/transfer_request.rb]:::support
      A5[RepoSyncRequest\nee/lib/gitlab/geo/repo_sync_request.rb]:::support
      A6[Replication::BlobRetriever\nee/lib/gitlab/geo/replication/blob_retriever.rb]:::support
      A7[Replication::BaseRetriever\nee/lib/gitlab/geo/replication/base_retriever.rb]:::support
      A8[SignedData\nee/lib/gitlab/geo/signed_data.rb]:::support
      A9[GitSSHProxy\nee/lib/gitlab/geo/git_ssh_proxy.rb]:::support
    end
    class Abstractions grouping

    %% 9. API & Controller Integration
    subgraph API_Controllers["API and Admin Controllers"]
      direction TB
      C1[Geo\nlib/api/geo.rb]:::core
      C2[Admin::Geo::NodesController\nee/app/controllers/admin/geo/nodes_controller.rb]:::core
      C3[Admin::Geo::ApplicationController\nee/app/controllers/admin/geo/application_controller.rb]:::core
      C4[Oauth::GeoAuthController\nee/app/controllers/oauth/geo_auth_controller.rb]:::core
    end
    class API_Controllers grouping

    %% 10. Policy & Permissions Layer
    subgraph Policies["Policy & Permissions"]
      direction TB
      P1[GeoNodePolicy\nee/app/policies/geo_node_policy.rb]:::support
      P2[RegistryPolicy\nee/app/policies/geo/registry_policy.rb]:::support
    end
    class Policies grouping
    
    %% 11. Special Models
    subgraph Special_Models["Special/Other Models"]
      direction TB
      DM1[DeletedProject\nee/app/models/geo/deleted_project.rb]:::core
      DM2[SecondaryUsageData\nee/app/models/geo/secondary_usage_data.rb]:::core
    end
    class Special_Models grouping

    %% 12. Serialization Layer
    subgraph Serializers["Serialization"]
      direction TB
      S14[GeoNodeStatusSerializer\nee/app/serializers/geo_node_status_serializer.rb]:::support
      S15[GeoNodeSerializer\nee/app/serializers/geo_node_serializer.rb]:::support
    end
    class Serializers grouping

    %% 13. Migrations & Database Structure
    subgraph Migrations["DB Migrations/Core Schema"]
      direction TB
      M1[Migration classes under\nee/db/geo/migrate & ee/db/geo/post_migrate]:::setup
    end
    class Migrations grouping

    %% ==== LOGICAL RELATIONSHIPS ====

    %% Geo Node oversees Registries and Statuses
    GN1 --> R1
    GN1 --> R2
    GN1 --> R3
    GN1 --> R4
    GN1 --> R5
    GN1 --> R6
    GN1 --> R7
    GN1 --> R8
    GN1 --> R9
    GN1 --> R10
    GN1 --> R11
    GN1 --> R12
    GN1 --> R13
    GN1 --> R14
    GN1 --> R15
    GN1 --> R16
    GN1 --> GN2
    GN1 --> GN3

    %% Registries translate to RegistryStates
    R1 -- Tracks State --> S1
    R2 -- Tracks State --> S2
    R3 -- Tracks State --> S10
    R4 -- Tracks State --> S5
    R5 -- Sync & Verify Relationship --> M1
    R6 -- Tracks State --> S3
    R7 -- Tracks State --> S9
    R8 -- Registry-State Link --> S9
    R9 -- Registry-State Link --> S7
    R10 -- Sync Link --> M1
    R11 -- Registry-State Link --> S11
    R12 -- Tracks State --> S8
    R13 -- Tracks State --> S6
    R14 -- Registry-State Link --> M1
    R16 -- Tracks State --> S4

    %% Registry finders relate to registries
    F2 -- For querying --> R1
    F3 -- For querying --> R2
    F4 -- For querying --> R3
    F5 -- For querying --> R4
    F6 -- For querying --> R5
    F7 -- For querying --> R6
    F8 -- For querying --> R7
    F9 -- For querying --> R8
    F10 -- For querying --> R9
    F11 -- For querying --> R10
    F12 -- For querying --> R11
    F13 -- For querying --> R13
    F14 -- For querying --> R14
    F15 -- For querying --> R15
    F16 -- For querying --> R12
    F17 -- For querying --> R16
    F18 -- For querying --> M1

    %% FrameworkRegistryFinder is included in all specific finders (aggregate)
    F1 --> F2
    F1 --> F3
    F1 --> F4
    F1 --> F5
    F1 --> F6
    F1 --> F7
    F1 --> F8
    F1 --> F9
    F1 --> F10
    F1 --> F11
    F1 --> F12
    F1 --> F13
    F1 --> F14
    F1 --> F15
    F1 --> F16
    F1 --> F17
    F1 --> F18

    %% Abstractions connect to Replicator, which is the main orchestrator
    A1 -- Abstracts replication of --> R1
    A1 -- Abstracts replication of --> R2
    A1 -- Abstracts replication of --> R3
    A1 -- Abstracts replication of --> R4
    A1 -- Abstracts replication of --> R5
    A1 -- Abstracts replication of --> R6
    A1 -- Abstracts replication of --> R7
    A1 -- Abstracts replication of --> R8
    A1 -- Abstracts replication of --> R9
    A1 -- Abstracts replication of --> R10
    A1 -- Abstracts replication of --> R11
    A1 -- Abstracts replication of --> R12
    A1 -- Abstracts replication of --> R13
    A1 -- Abstracts replication of --> R14
    A1 -- Abstracts replication of --> R16

    %% Abstractions: Batcher/Requests support Replicator/Registries
    A2 -- Batches registry operations --> R1
    A2 -- Batches registry operations --> R2
    A2 -- Batches registry operations --> R3
    A2 -- Batches registry operations --> R4
    A2 -- Batches registry operations --> R5
    A2 -- Batches registry operations --> R6
    A2 -- Batches registry operations --> R7
    A2 -- Batches registry operations --> R9
    A2 -- Batches registry operations --> R10

    %% Replication::BlobRetriever, BaseRetriever support Replicator
    A1 -- Uses BlobRetriever --> A6
    A6 -- Inherits from --> A7

    %% SignedData used by Replicator and NodeStatusRequest
    A8 -- Provides signing for --> A1
    A8 -- Provides signing for --> GS4

    %% GraphQL Types map onto registry models
    GT6 -- Exposes data of --> R1
    GT7 -- Exposes data of --> R2
    GT8 -- Exposes data of --> R3
    GT9 -- Exposes data of --> R4
    GT10 -- Exposes data of --> R5
    GT11 -- Exposes data of --> R6
    GT12 -- Exposes data of --> R7
    GT13 -- Exposes data of --> R8
    GT14 -- Exposes data of --> R9
    GT15 -- Exposes data of --> R10
    GT16 -- Exposes data of --> R11
    GT17 -- Exposes data of --> R12
    GT18 -- Exposes data of --> R13
    GT19 -- Exposes data of --> R14
    GT20 -- Exposes data of --> R16
    GT21 -- Exposes data of --> M1

    %% Union & enum types map to specialized types and registries
    GT2 -- Union includes --> GT6
    GT2 -- Union includes --> GT16
    GT2 -- Union includes --> GT17
    GT2 -- Union includes --> GT18
    GT2 -- Union includes --> GT19

    %% GraphQL Type exposes GeoNode
    GT1 -- GraphQL API exposed for node --> GN1

    %% Geo services orchestrate operations/web sync on Registries
    GS1 -- Replicates --> R1
    GS1 -- Replicates --> R2
    GS1 -- Replicates --> R3
    GS1 -- Replicates --> R4
    GS1 -- Replicates --> R5
    GS1 -- Replicates --> R6
    GS1 -- Replicates --> R7
    GS1 -- Replicates --> R8
    GS1 -- Replicates --> R9
    GS1 -- Replicates --> R10
    GS1 -- Replicates --> R11
    GS1 -- Replicates --> R12
    GS1 -- Replicates --> R13
    GS1 -- Replicates --> R14
    GS1 -- Replicates --> R16

    GS2 -- Updates --> R1
    GS2 -- Updates --> R2
    GS2 -- Updates --> R3
    GS2 -- Updates --> R4
    GS2 -- Updates --> R5
    GS2 -- Updates --> R6
    GS2 -- Updates --> R7
    GS2 -- Updates --> R8
    GS2 -- Updates --> R9
    GS2 -- Updates --> R10
    GS2 -- Updates --> R11
    GS2 -- Updates --> R12
    GS2 -- Updates --> R13
    GS2 -- Updates --> R14
    GS2 -- Updates --> R16

    GS3 -- Removes registry entries for --> R1
    GS3 -- Removes registry entries for --> R2
    GS3 -- Removes registry entries for --> R3
    GS3 -- Removes registry entries for --> R4
    GS3 -- Removes registry entries for --> R5
    GS3 -- Removes registry entries for --> R6
    GS3 -- Removes registry entries for --> R7
    GS3 -- Removes registry entries for --> R8
    GS3 -- Removes registry entries for --> R9
    GS3 -- Removes registry entries for --> R10
    GS3 -- Removes registry entries for --> R11
    GS3 -- Removes registry entries for --> R12
    GS3 -- Removes registry entries for --> R13
    GS3 -- Removes registry entries for --> R14
    GS3 -- Removes registry entries for --> R16

    GS8 -- Ensures consistency of --> R1
    GS8 -- Ensures consistency of --> R2
    GS8 -- Ensures consistency of --> R3
    GS8 -- Ensures consistency of --> R4
    GS8 -- Ensures consistency of --> R5
    GS8 -- Ensures consistency of --> R6
    GS8 -- Ensures consistency of --> R7
    GS8 -- Ensures consistency of --> R8
    GS8 -- Ensures consistency of --> R9
    GS8 -- Ensures consistency of --> R10
    GS8 -- Ensures consistency of --> R11
    GS8 -- Ensures consistency of --> R12
    GS8 -- Ensures consistency of --> R13
    GS8 -- Ensures consistency of --> R14
    GS8 -- Ensures consistency of --> R16

    %% Geo workers orchestrate services
    W1 -- Invokes --> GS1
    W2 -- Invokes verification across --> GS1
    W3 -- Fills missing verification state for --> R1
    W4 -- Times out long verifications for --> R1
    W5 -- Invokes destroy/remove for --> GS3
    W6 -- Sets up cron jobs as per node config
    W7 -- Collects usage data from --> DM2
    W8 -- Schedules verifications for --> GS1
    W9 -- Updates metrics for --> R1
    W10 -- Resyncs registries for --> R8
    W11 -- Handles timeout for syncs on --> R1
    W12 -- Syncs container repositories via --> GS9

    %% Policies secure registry and node access
    P1 -- Applies permissions to --> GN1
    P2 -- Applies permissions to --> R1
    P2 -- Applies permissions to --> R2
    P2 -- Applies permissions to --> R3
    P2 -- Applies permissions to --> R4
    P2 -- Applies permissions to --> R5
    P2 -- Applies permissions to --> R6
    P2 -- Applies permissions to --> R7
    P2 -- Applies permissions to --> R8
    P2 -- Applies permissions to --> R9
    P2 -- Applies permissions to --> R10
    P2 -- Applies permissions to --> R11
    P2 -- Applies permissions to --> R12
    P2 -- Applies permissions to --> R13
    P2 -- Applies permissions to --> R14
    P2 -- Applies permissions to --> R16

    %% API/Controllers integrate with node and registries
    C1 -- Exposes API for --> R1
    C1 -- Exposes API for --> GN1
    C2 -- Manages CRUD for --> GN1
    C3 -- Sets context for --> C2
    C4 -- Authenticates cross-site access for --> GN1

    %% Serialization
    S14 -- Serializes --> GN2
    S15 -- Serializes --> GN1

    %% Special Models
    DM1 -- Tracks deleted project info for --> R1
    DM2 -- Aggregates data on secondary usage for reporting

    %% Migrations/Setup
    M1 -- Defines database for all registry & state tables

  end

  %% Classes for design
  class GN1,GN2,GN3,R1,R2,R3,R4,R5,R6,R7,R8,R9,R10,R11,R12,R13,R14,R15,R16,DM1,DM2,GS1,GS2,GS3,GS4,GS5,GS6,GS7,GS8,GS9,GS10,A1,C1,C2,C3,C4 core
  class F1,F2,F3,F4,F5,F6,F7,F8,F9,F10,F11,F12,F13,F14,F15,F16,F17,F18,F19,A2,A3,A4,A5,A6,A7,A8,A9,S14,S15,W1,W2,W3,W4,W5,W6,W7,W8,W9,W10,W11,W12,P1,P2 support
  class S1,S2,S3,S4,S5,S6,S7,S8,S9,S10,S11 data
  class M1 setup
  class GT1,GT2,GT3,GT4,GT5,GT6,GT7,GT8,GT9,GT10,GT11,GT12,GT13,GT14,GT15,GT16,GT17,GT18,GT19,GT20,GT21 support
```