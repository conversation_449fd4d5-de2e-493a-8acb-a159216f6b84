```mermaid
flowchart TD
  %% Layout direction
  %% Vertical layout for readability
  %% Styles
  classDef coreDomain fill:#D4F1F9,stroke:#6fc3df,stroke-width:2,stroke-dasharray: 2 2,color:#222,stroke-linecap:round
  classDef dataStructure fill:#E0F8E0,stroke:#7acf88,stroke-width:2,stroke-dasharray: 4 2,color:#222,stroke-linecap:round
  classDef utility fill:#FFF8DC,stroke:#f7d670,stroke-width:2,stroke-dasharray: 2 2,color:#222,stroke-linecap:round
  classDef errorHandling fill:#FFE4E1,stroke:#f8a5a5,stroke-width:2,stroke-dasharray: 2 2,color:#222,stroke-linecap:round
  classDef initialization fill:#E6E6FA,stroke:#c0aaff,stroke-width:2,stroke-dasharray: 2 2,color:#222,stroke-linecap:round
  classDef groupBG fill:#F8F8F8,stroke:#B6B6F7,stroke-width:3,stroke-dasharray: 3 3
  classDef groupBGBorderCE fill:#F8F8F8,stroke:#B6B6F7,stroke-width:3,stroke-dasharray: 8 2
  classDef groupBGBorderEE fill:#F8F8F8,stroke:#c0aaff,stroke-width:3,stroke-dasharray: 8 4
  classDef test fill:#fbf7fa,stroke:#d1d6e0,stroke-width:1,stroke-dasharray:2 2

  %% GraphQL Schema Definition and Extensions (Core Domain)
  subgraph S0["GraphQL Schema Core" ]
    class S0 groupBG
    gitlab_schema["gitlab_schema.rb" ]
    class gitlab_schema coreDomain
    ee_gitlab_schema["ee/gitlab_schema.rb" ]
    class ee_gitlab_schema coreDomain
    types_base_field["types/base_field.rb" ]
    class types_base_field coreDomain
    gitlab_type_deprecations["lib/gitlab/graphql/type_name_deprecations.rb" ]
    class gitlab_type_deprecations utility
    gitlab_deprecations["lib/gitlab/graphql/deprecations.rb" ]
    class gitlab_deprecations utility
    gitlab_timeout["lib/gitlab/graphql/timeout.rb" ]
    class gitlab_timeout errorHandling
    gitlab_known_ops["lib/gitlab/graphql/known_operations.rb" ]
    class gitlab_known_ops utility
    gitlab_schema --- ee_gitlab_schema
    gitlab_schema --- types_base_field
    gitlab_schema --- gitlab_timeout
    gitlab_schema --- gitlab_known_ops
    gitlab_schema --- gitlab_deprecations
    gitlab_schema --- gitlab_type_deprecations
    ee_gitlab_schema --- gitlab_schema
    ee_gitlab_schema --- gitlab_known_ops
    ee_gitlab_schema --- gitlab_type_deprecations
    ee_gitlab_schema --- gitlab_timeout
  end

  %% GraphQL Root Types Query, Mutation, Subscription + EE Extensions
  subgraph S1["Root Types Query, Mutation, Subscription" ]
    class S1 groupBG
    mutation_type["ee/types/mutation_type.rb" ]
    class mutation_type coreDomain
    query_type["ee/types/query_type.rb" ]
    class query_type coreDomain
    subscription_type["ee/types/subscription_type.rb" ]
    class subscription_type coreDomain
    mutation_type_ce["types/mutation_type.rb" ]
    class mutation_type_ce coreDomain
    query_type_ce["types/query_type.rb" ]
    class query_type_ce coreDomain
    subscription_type_ce["types/subscription_type.rb" ]
    class subscription_type_ce coreDomain
    mutation_type --- ee_gitlab_schema
    query_type --- ee_gitlab_schema
    subscription_type --- ee_gitlab_schema
    mutation_type --- gitlab_schema
    query_type --- gitlab_schema
    subscription_type --- gitlab_schema
    mutation_type --- mutation_type_ce
    mutation_type_ce --- gitlab_schema
    query_type --- query_type_ce
    query_type_ce --- gitlab_schema
    subscription_type --- subscription_type_ce
    subscription_type_ce --- gitlab_schema
  end

  %% Built-in and Domain-Specific Type System CE + EE
  subgraph S2["Type Definitions & Interfaces" ]
    class S2 groupBG
    types_alert_integration["types/alert_management/integration_type.rb"]
    class types_alert_integration coreDomain
    types_invitation_if["types/invitation_interface.rb"]
    class types_invitation_if coreDomain
    ee_types_namespace["ee/types/namespace_type.rb"]
    class ee_types_namespace coreDomain
    ee_types_issue["ee/types/issue_type.rb"]
    class ee_types_issue coreDomain
    ee_types_noteable_if["ee/types//noteable_interface.rb"]
    class ee_types_noteable_if coreDomain
    ee_types_permission_project["ee/types/permission_types/project.rb"]
    class ee_types_permission_project coreDomain
    ee_types_permission_group["ee/types/permission_types/group.rb"]
    class ee_types_permission_group coreDomain
    ee_types_permission_work_item["ee/types/permission_types/work_item.rb"]
    class ee_types_permission_work_item coreDomain
    ee_types_permission_namespaces_base["ee/types/permission_types/namespaces/base.rb"]
    class ee_types_permission_namespaces_base coreDomain
    types_alert_integration --- types_base_field
    types_invitation_if --- types_base_field
    types_invitation_if --- ee_types_permission_project
    ee_types_namespace --- ee_types_permission_namespaces_base
    ee_types_issue --- types_base_field
    ee_types_noteable_if --- types_base_field
    ee_types_permission_group --- ee_types_permission_namespaces_base
    ee_types_permission_project --- ee_types_permission_group
    ee_types_permission_work_item --- ee_types_permission_project
  end

  %% Audit Event and External Interfaces EE
  subgraph S3["Audit Events & Streaming Interfaces"]
    class S3 groupBGBorderEE
    audit_if_base_header["ee/types/audit_events/streaming/base_header_interface.rb"]
    class audit_if_base_header coreDomain
    audit_if_google_cloud["ee/types/audit_events/google_cloud_logging_configuration_interface.rb"]
    class audit_if_google_cloud coreDomain
    audit_if_external_dest["ee/types/audit_events/external_audit_event_destination_interface.rb"]
    class audit_if_external_dest coreDomain
    audit_if_amazon_s3["ee/types/audit_events/amazon_s3_configuration_interface.rb"]
    class audit_if_amazon_s3 coreDomain
    audit_if_base_header --- audit_if_external_dest
    audit_if_google_cloud --- audit_if_external_dest
    audit_if_amazon_s3 --- audit_if_external_dest
  end

  %% GraphQL Controller & Web UI
  subgraph S4["API Endpoints and Controllers"]
    class S4 groupBG
    graphql_controller["app/controllers/graphql_controller.rb"]
    class graphql_controller initialization
    explorer_controller["app/controllers/api/graphql/graphql_explorer_controller.rb"]
    class explorer_controller initialization
    graphql_controller --- gitlab_schema
    graphql_controller --- gitlab_timeout
    explorer_controller --- graphql_controller
    explorer_controller --- types_base_field
  end

  %% Mutations & Concerns
  subgraph S5["Mutation Support Concerns"]
    class S5 groupBG
    resolve_namespace["mutations/concerns/mutations/resolves_namespace.rb"]
    class resolve_namespace utility
    resolve_group["mutations/concerns/mutations/resolves_group.rb"]
    class resolve_group utility
    resolve_namespace --- gitlab_schema
    resolve_group --- gitlab_schema
    resolve_namespace --- resolve_group
  end

  %% Data Structure and Connection Extensions
  subgraph S6["Connection Extensions & Pagination"]
    class S6 groupBG
    pagination_array["lib/gitlab/graphql/pagination/active_record_array_connection.rb"]
    class pagination_array dataStructure
    pagination_offset["lib/gitlab/graphql/pagination/offset_active_record_relation_connection.rb"]
    class pagination_offset dataStructure
    conn_redaction["lib/gitlab/graphql/connection_redaction.rb"]
    class conn_redaction utility
    present_ext["lib/gitlab/graphql/present/field_extension.rb"]
    class present_ext utility
    calls_gitaly_ext["lib/gitlab/graphql/calls_gitaly/field_extension.rb"]
    class calls_gitaly_ext utility
    dast_profile_ext["lib/gitlab/graphql/project/dast_profile_connection_extension.rb"]
    class dast_profile_ext utility
    board_issues_ext["lib/gitlab/graphql/board/issues_connection_extension.rb"]
    class board_issues_ext utility
    external_paginated_ext["lib/gitlab/graphql/extensions/externally_paginated_array_extension.rb"]
    class external_paginated_ext utility

    pagination_array --- pagination_offset
    pagination_array --- conn_redaction
    pagination_offset --- conn_redaction
    present_ext --- types_base_field
    calls_gitaly_ext --- types_base_field
    dast_profile_ext --- types_base_field
    board_issues_ext --- types_base_field
    external_paginated_ext --- pagination_array
  end

  %% Cached Introspection Query and Queries Structure
  subgraph S7["Introspection & Query Support"]
    class S7 groupBG
    introspection_query["cached_introspection_query.rb"]
    class introspection_query coreDomain
    queries_lib["lib/gitlab/graphql/queries.rb"]
    class queries_lib utility
    introspection_query --- gitlab_schema
    queries_lib --- gitlab_schema
  end

  %% Spec/Testing Files
  subgraph S8["Specs & Query Validation Testing"]
    class S8 groupBG
    queries_spec["spec/lib/gitlab/graphql/queries_spec.rb"]
    class queries_spec test
    gitlab_schema_spec["spec/requests/api/graphql/gitlab_schema_spec.rb"]
    class gitlab_schema_spec test
    mutation_type_spec["ee/spec/graphql/ee/types/mutation_type_spec.rb"]
    class mutation_type_spec test
    glql_base_controller_spec["spec/controllers/glql/base_controller_spec.rb"]
    class glql_base_controller_spec test
    queries_spec --- queries_lib
    gitlab_schema_spec --- gitlab_schema
    gitlab_schema_spec --- gitlab_timeout
    gitlab_schema_spec --- gitlab_deprecations
    mutation_type_spec --- mutation_type
    glql_base_controller_spec --- graphql_controller
  end

  %% EE Extensions and Triggers
  subgraph S9["Enterprise Extensions & Subscription Triggers"]
    class S9 groupBGBorderEE
    graphql_triggers["ee/graphql_triggers.rb"]
    class graphql_triggers coreDomain
    graphql_triggers --- subscription_type
    graphql_triggers --- ee_gitlab_schema
    graphql_triggers --- gitlab_known_ops
  end

  %% Key Relationships between Groups
  gitlab_schema --> S1
  S1 --> S2
  S1 --> S3
  S1 --> S4
  S1 --> S5
  S1 --> S9
  S2 --> S6
  S1 --> S6
  S4 --> S7
  S5 --> S2
  S6 --> S7
  introspection_query --> queries_lib
  queries_lib --> S8
  S8 --> gitlab_schema
  gitlab_schema --> gitlab_type_deprecations
  S3 --> types_base_field
  S2 --> types_base_field
  S3 --> S9
  S5 --> types_base_field
  S5 --> types_invitation_if
```