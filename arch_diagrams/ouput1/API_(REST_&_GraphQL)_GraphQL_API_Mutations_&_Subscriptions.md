```mermaid
flowchart TD
  %%------STYLE DEFINITIONS-----
  %% Node background colors and shapes
  classDef core fill:#D4F1F9,stroke:#75b5ce,stroke-width:2px,rx:12,ry:12,color:#2a4266,stroke-dasharray:0;
  classDef support fill:#FFF8DC,stroke:#e4c871,stroke-width:2px,rx:12,ry:12,color:#765d26,stroke-dasharray:0;
  classDef datastructure fill:#E0F8E0,stroke:#91be91,stroke-width:2px,rx:12,ry:12,color:#28562a,stroke-dasharray:0;
  classDef error fill:#FFE4E1,stroke:#ff9690,stroke-width:2px,rx:12,ry:12,color:#952c23,stroke-dasharray:0;
  classDef init fill:#E6E6FA,stroke:#b3a6ea,stroke-width:2px,rx:12,ry:12,color:#4d3d6a,stroke-dasharray:0;
  classDef grouping fill:#F8F8F8,stroke-width:2px,stroke-dasharray:4 4,color:#333;

  %%------GRAPH DIRECTION----
  direction TB

  %% === SUBGRAPH: BASES AND SHARED ABSTRACTIONS ===
  subgraph sg_base["Base Mutations & Shared Abstractions"]
    direction TB
    base_mut[BaseMutation<br>App-level GraphQL mutation base]:::core
    sg_base_mut[Mutations::Base<br>Module base for specific domain]:::core
    base_iss[Mutations::Issues::Base<br>Issues mutation base, issues concern]:::core
    base_merge[Mutations::MergeRequests::Base<br>Merge requests mutation base]:::core
    base_rel[Mutations::Releases::Base<br>Release mutation base, project scoping]:::core
    base_note[Mutations::Notes::Base<br>Notes mutation base]:::core
    base_snip[Mutations::Snippets::Base<br>Snippets mutation base]:::core
    base_savedr[Mutations::SavedReplies::Base<br>Saved replies mutation base]:::core
    base_page[Mutations::Pages::Base<br>Page mutation base, with FindsProject]:::core
    base_dsgn[Mutations::DesignManagement::Base<br>Design management mutation base, with FindsProject and ResolvesIssuable]:::core
    base_alert[Mutations::AlertManagement::Base<br>alert management mutation base]:::core
    subgraph sg_concerns["Mutation Concerns & Utilities"]
      validates[ValidateTimeEstimate<br>Reusable concern, time tracking and validation]:::support
      resolves_iss[ResolvesIssuable<br>Finds and authorizes issuables]:::support
      resolves_res[ResolvesResourceParent<br>Generic resource parent resolver]:::support
      resolves_subs[ResolvesSubscription<br>Sets subscription state]:::support
      assignable[Assignable<br>Bulk assign/unassign utility]:::support
    end
  end

  %% === SUBGRAPH: DOMAIN CONCEPTS: MAJOR FEATURE GROUPS ===
  subgraph sg_issues["Issues"]
    direction TB
    iss_create[Create<br>Issue creation, arguments and spam protection]:::core
    iss_bulk[BulkUpdate<br>Bulk update actions on issues]:::core
    iss_update[Update<br>Issue update with time and label parsing]:::core
    iss_set_sub[SetSubscription<br>Set subscription state on issues]:::core
    iss_confid[SetConfidential<br>Set confidential state, spam protected]:::core
    iss_lock[SetLocked<br>Lock discussion]:::core
    iss_due[SetDueDate<br>Set/revoke issue due date]:::core
    iss_link_alerts[LinkAlerts<br>Associate alerts to incident issues]:::core
    iss_unlink_alerts[UnlinkAlert<br>Remove alert link from incident]:::core
    iss_set_severity[SetSeverity<br>Set incident severity]:::core
    iss_set_escalation[SetEscalationStatus<br>Set escalation status]:::core
    iss_move[Move<br>Move issue between projects]:::core
    iss_set_crm[SetCrmContacts<br>Set CRM contacts for issue]:::core
    iss_com_args[CommonMutationArguments<br>Issue argument inclusion]:::support
  end

  subgraph sg_merge_requests["Merge Requests"]
    direction TB
    mr_create[Create<br>Create MRs with FindsProject]:::core
    mr_update[Update<br>Update MRs, includes Description & Time validation]:::core
    mr_accept[Accept<br>Accept/merge MRs, authorize access, async merge]:::core
    mr_set_locked[SetLocked<br>Lock MR discussion]:::core
    mr_set_reviewers[SetReviewers<br>Manage reviewers]:::core
    mr_set_draft[SetDraft<br>Draft state]:::core
    mr_set_labels[SetLabels<br>Manage labels]:::core
    mr_set_assignees[SetAssignees<br>Managing assignees]:::core
    mr_set_milestone[SetMilestone<br>Link milestone to MR]:::core
    mr_rereview[ReviewerRereview<br>Trigger new review]:::core
    mr_destroy_requested[DestroyRequestedChanges<br>Remove requested changes]:::core
    mr_update_approval[UpdateApprovalRule<br>Approval rule configuration]:::core
  end

  subgraph sg_notes["Notes & Discussions"]
    direction TB
    notes_create_base[Create::Base<br>Note creation base class]:::core
    notes_create_note[Create::Note<br>Create simple note]:::core
    notes_create_disc[Create::Discussion<br>Create discussion note]:::core
    notes_create_diff[Create::DiffNote<br>Create diff note with position]:::core
    notes_create_img_diff[Create::ImageDiffNote<br>Create image diff note]:::core
    notes_update_base[Update::Base<br>Update base for ]:::core
    notes_update_note[Update::Note<br>Update basic note]:::core
    notes_update_img_diff[Update::ImageDiffNote<br>Update image diff note]:::core
    notes_destroy[Destroy<br>Destroy note mutation]:::core
    notes_convert_thread[ConvertToThread<br>Convert note to thread]:::core
    notes_reposition_img_diff[RepositionImageDiffNote<br>Move image diff note]:::core
    notes_abusereport_create[AbuseReport::Create<br>Create abuse report note]:::core
    notes_abusereport_update[AbuseReport::Update<br>Update abuse report note]:::core
    notes_service_compat[ServiceCompatibility<br>Params mapping utility]:::support
  end

  subgraph sg_snippets["Snippets"]
    direction TB
    snip_create[Create<br>Create snippet, spam-protected]:::core
    snip_update[Update<br>Update snippet]:::core
    snip_destroy[Destroy<br>Destroy snippet]:::core
    snip_mark_spam[MarkAsSpam<br>Flag snippet as spam]:::core
    snip_base[Base<br>Shared snippet mutation logic]:::core
  end

  subgraph sg_labels["Labels"]
    direction TB
    labels_create[Create<br>Label creation]:::core
  end

  subgraph sg_projects["Projects/Branches/Tags"]
    direction TB
    proj_blobs_remove[BlobsRemove<br>Bulk blobs remove]:::core
    proj_star[Star<br>Project starring logic]:::core
    proj_sync_fork[SyncFork<br>Sync forked repos]:::core
    proj_update_compliance[UpdateComplianceFrameworks<br>Compliance frameworks]:::core
    proj_branchrules_squash[BranchRules::SquashOptions::Update]:::core
    br_create[BranchRules::Create<br>Create branch rule]:::core
    repo_br_create[Repositories::Branches::Create<br>Branch create]:::core
    repo_br_delete[Repositories::Branches::Delete<br>Branch delete]:::core
    repo_tag_create[Repositories::Tags::Create<br>Tag create]:::core
    repo_tag_delete[Repositories::Tags::Delete<br>Tag delete]:::core
  end

  subgraph sg_environments["Environments"]
    direction TB
    env_create[Create<br>Create environment]:::core
    env_update[Update<br>Update environment]:::core
    env_delete[Delete<br>Delete environment]:::core
    env_stop[Stop<br>Stop environment]:::core
    env_canary_update[CanaryIngress::Update<br>Update canary ingress]:::core
  end

  subgraph sg_clusters["Clusters/Agents"]
    direction TB
    clust_agent_create[Agents::Create<br>Create cluster agent]:::core
    clust_agenttokens_create[AgentTokens::Create<br>Create agent token]:::core
  end

  subgraph sg_terraform["Terraform State"]
    direction TB
    terra_unlock[State::Unlock<br>Unlock TF state]:::core
    terra_lock[State::Lock<br>Lock TF state]:::core
    terra_delete[State::Delete<br>Delete TF state]:::core
  end

  subgraph sg_ci["CI/CD and Runner"]
    direction TB
    ci_job_cancel[Ci::Job::Cancel<br>Cancel CI job]:::core
    ci_jobtoken_add[Ci::JobTokenScope::AddProject<br>Add job token project]:::core
    ci_jobtoken_remove[Ci::JobTokenScope::RemoveProject<br>Remove job token project]:::core
    ci_jobtoken_update[Ci::JobTokenScope::UpdateJobTokenPolicies<br>Update job token policies]:::core
    ci_jobtoken_allowlist[Ci::JobTokenScope::AutopopulateAllowlist<br>Auto-populate allowlist]:::core
    ci_jobtoken_clear[Ci::JobTokenScope::ClearAllowlistAutopopulations<br>Clear autopopulation]:::core
    ci_pipeline_retry[Ci::Pipeline::Retry<br>Retry pipelines]:::core
    ci_pipeline_destroy[Ci::Pipeline::Destroy<br>Destroy pipeline]:::core
    ci_pipelinesched_varin[Ci::PipelineSchedule::VariableInputType<br>Pipeline schedule var input]:::datastructure
    ci_runner_bulkpause[Ci::Runner::BulkPause<br>Bulk pause runners]:::core
    ci_runner_bulkdel[Ci::Runner::BulkDelete<br>Bulk delete runners]:::core
    ci_runner_update[Ci::Runner::Update<br>Update runner]:::core
    ci_pipeline_trigger_create[Ci::PipelineTrigger::Create<br>Pipeline trigger creation]:::core
    ci_catalog_destroy[Ci::Catalog::Resources::Destroy<br>Destroy CI catalog resource]:::core
    ci_catalog_inputargs[Ci::Runner::CommonMutationArguments<br>Runner mutation arguments]:::support
  end

  subgraph sg_achievements["Achievements"]
    direction TB
    ach_create[Create<br>Achievements creation]:::core
    ach_update[Update<br>Achievements update]:::core
    ach_delete[Delete<br>Achievements delete]:::core
    ach_revoke[Revoke<br>Revoke achievements]:::core
    ach_update_user[UpdateUserAchievement<br>Update user achievement]:::core
    ach_update_user_priorities[UpdateUserAchievementPriorities<br>Prioritize user achievements]:::core
    ach_delete_user[DeleteUserAchievement<br>Delete user achievement]:::core
  end

  subgraph sg_alert_management["Alert Management"]
    direction TB
    alert_set_assignees[Alerts::SetAssignees<br>Alert assignee operations]:::core
    alert_update_status[UpdateAlertStatus<br>Update alert status]:::core
    alert_create_issue[CreateAlertIssue<br>Link alert to incident]:::core
    alerts_todo_create[Alerts::Todo::Create<br>Todo from alert]:::core
    am_http_create[HttpIntegration::Create<br>Create HTTP integration]:::core
    am_http_update[HttpIntegration::Update<br>Update HTTP integration]:::core
    am_http_destroy[HttpIntegration::Destroy<br>Delete HTTP integration]:::core
    am_http_reset[HttpIntegration::ResetToken<br>Reset HTTP integration token]:::core
    am_prom_create[PrometheusIntegration::Create<br>Prometheus integration create]:::core
    am_prom_update[PrometheusIntegration::Update<br>Prometheus integration update]:::core
    am_prom_reset[PrometheusIntegration::ResetToken<br>Prometheus token reset]:::core
    am_prom_base[PrometheusIntegration::PrometheusIntegrationBase<br>Prometheus mutation base]:::core
    am_http_base[HttpIntegration::HttpIntegrationBase<br>HTTP integration mutation base]:::core
  end

  subgraph sg_todos["Todos"]
    direction TB
    todos_mark_all[MarkAllDone<br>Mark all todos done]:::core
    todos_snooze[SnoozeMany<br>Snooze bulk todos]:::core
    todos_resolve[ResolveMany<br>Resolve bulk todos]:::core
    todos_restore[RestoreMany<br>Restore bulk todos]:::core
    todos_unsnooze[UnsnoozeMany<br>Unsnooze bulk todos]:::core
  end

  subgraph sg_supp_util["Supporting & Utility"]
    direction TB
    spam_protect[Mutations::SpamProtection<br>Spam protection mixin]:::support
    finds_project[FindsProject<br>Find and authorize project]:::support
    finds_namespace[FindsNamespace<br>Find and authorize namespace]:::support
    finds_group[Mutations::ResolvesGroup<br>Group resolution utility]:::support
    resolves_ids[ResolvesIds<br>ID parsing utility for integrations]:::support
  end

  subgraph sg_data_types["Data Structures"]
    direction TB
    user_achievement_type[Types::Achievements::UserAchievementType]:::datastructure
    achievement_type[Types::Achievements::AchievementType]:::datastructure
    timeline_event_type[Types::IncidentManagement::TimelineEventType]:::datastructure
    timeline_event_tag_type[Types::IncidentManagement::TimelineEventTagType]:::datastructure
    pipeline_type[Types::Ci::PipelineType]:::datastructure
    job_type[Types::Ci::JobType]:::datastructure
    label_type[Types::LabelType]:::datastructure
  end

  subgraph sg_incident_mgmt["Incident Management"]
    direction TB
    im_tag_base[TimelineEventTag::Base<br>Timeline tag mutation base]:::core
    im_tag_create[TimelineEventTag::Create<br>Create event tag]:::core
    im_event_base[TimelineEvent::Base<br>Base for event mutations]:::core
    im_event_create[TimelineEvent::Create<br>Create event]:::core
    im_event_update[TimelineEvent::Update<br>Update event]:::core
    im_event_promote[TimelineEvent::PromoteFromNote<br>Promote note to event]:::core
  end

  subgraph sg_integrations["Integrations"]
    direction TB
    integrations_exclusions_create[Exclusions::Create<br>Create exclusions]:::core
    integrations_exclusions_delete[Exclusions::Delete<br>Delete exclusions]:::core
  end

  subgraph sg_subscriptions["Subscriptions"]
    direction TB
    subs_notes_deleted[Subscriptions::Notes::Deleted<br>Subscription for note deleted]:::core
    subs_base[Subscriptions::BaseSubscription<br>Base for GraphQL subscriptions]:::core
    subs_user_mr_updated[Subscriptions::User::MergeRequestUpdated<br>User MR updated subscription]:::core
    subs_ci_pipeline_status[Subscriptions::Ci::Pipelines::StatusUpdated<br>Pipeline status update sub]:::core
    subs_issuable_updated[Subscriptions::IssuableUpdated<br>Issuable updated sub]:::core
    subs_workitem_updated[Subscriptions::WorkItemUpdated<br>Work item updated sub]:::core
  end

  subgraph sg_ml["ML Model Registry"]
    direction TB
    ml_models_create[Ml::Models::Create<br>Model creation]:::core
    ml_models_edit[Ml::Models::Edit<br>Edit model]:::core
    ml_models_destroy[Ml::Models::Destroy<br>Destroy model]:::core
    ml_models_delete[Ml::Models::Delete<br>Delete model]:::core
    ml_model_versions_create[Ml::ModelVersions::Create<br>Create model ver]:::core
    ml_model_versions_edit[Ml::ModelVersions::Edit<br>Edit model ver]:::core
    ml_model_versions_delete[Ml::ModelVersions::Delete<br>Delete model ver]:::core
  end

  subgraph sg_release["Releases & Assets"]
    direction TB
    rel_create[Create<br>Create release]:::core
    rel_update[Update<br>Update release]:::core
    rel_delete[Delete<br>Delete release]:::core
    rel_asset_create[ReleaseAssetLinks::Create<br>Create link asset]:::core
    rel_asset_update[ReleaseAssetLinks::Update<br>Update link asset]:::core
    rel_asset_delete[ReleaseAssetLinks::Delete<br>Delete link asset]:::core
  end

  subgraph sg_containers["Container Registry & Packages"]
    direction TB
    container_repo_destroy[ContainerRepositories::Destroy<br>Destroy repo]:::core
    container_repo_destroy_tags[ContainerRepositories::DestroyTags<br>Destroy tags]:::core
    container_repo_base[ContainerRepositories::DestroyBase<br>Repo destruction base]:::core
    container_expir_policy_update[ContainerExpirationPolicies::Update<br>Expiration policy]:::core
    pkg_destroy[Packages::Destroy<br>Delete package]:::core
    pkg_bulk_destroy[Packages::BulkDestroy<br>Bulk delete packages]:::core
    pkg_destroy_file[Packages::DestroyFile<br>Delete package file]:::core
    pkg_cleanup_policy_update[Packages::Cleanup::Policy::Update<br>Update cleanup]:::core
  end

  subgraph sg_award_emoji["Award Emojis"]
    direction TB
    award_add[Add<br>Add award emoji]:::core
    award_remove[Remove<br>Remove award emoji]:::core
    award_toggle[Toggle<br>Toggle award emoji]:::core
    award_base[Base<br>Award emoji mutation base]:::core
  end

  subgraph sg_savedreplies["Saved Replies"]
    direction TB
    savedreplies_update[Update<br>Update saved reply]:::core
    savedreplies_destroy[Destroy<br>Destroy saved reply]:::core
    savedreplies_base[Base<br>Saved reply base]:::core
  end

  subgraph sg_misc["Other Mutations"]
    direction TB
    discussions_toggle[Discussions::ToggleResolve<br>Toggle resolved state, discussions]:::core
    custom_emoji_create[CustomEmoji::Create<br>Create custom emoji]:::core
    custom_emoji_destroy[CustomEmoji::Destroy<br>Destroy custom emoji]:::core
    uploads_delete[Uploads::Delete<br>Delete upload]:::core
    timelogs_delete[Timelogs::Delete<br>Delete timelog]:::core
    timelogs_create[Timelogs::Create<br>Create timelog]:::core
    timelogs_base[Timelogs::Base<br>Timelogs base]:::core
    members_bulk[Members::BulkUpdateBase<br>Bulk member base for groups/projects]:::core
    members_projects_bulk[Members::Projects::BulkUpdate<br>Project member update]:::core
    members_groups_bulk[Members::Groups::BulkUpdate<br>Group member update]:::core
  end

  %% === LOGICAL RELATIONSHIPS & DEPENDENCY LINKS ===

  %% --- Abstraction Inheritance ---
  %% BaseMutation is central
  base_mut --> sg_base_mut
  sg_base_mut -->|inherits/includes| base_iss
  sg_base_mut -->|inherits/includes| base_merge
  sg_base_mut -->|inherits/includes| base_rel
  sg_base_mut -->|inherits/includes| base_note
  sg_base_mut -->|inherits/includes| base_snip
  sg_base_mut -->|inherits/includes| base_savedr
  sg_base_mut -->|inherits/includes| base_page
  sg_base_mut -->|inherits/includes| base_dsgn
  sg_base_mut -->|inherits/includes| base_alert

  %% Mutations include/extend concerns for logic reuse
  base_iss --> resolves_iss
  base_iss --> validates
  base_merge --> resolves_iss
  labels_create -->|inherits| base_mut
  sg_todos -->|inherits| base_mut
  sg_snippets -->|inherits| base_snip
  sg_award_emoji -->|inherits| award_base
  award_base -->|inherits| base_mut
  base_rel --> finds_project
  base_page --> finds_project
  base_dsgn --> resolves_iss

  %% Integrations base inclusion
  integrations_exclusions_create --> resolves_ids
  integrations_exclusions_delete --> resolves_ids

  %% Notes creation & updating relationship
  notes_create_note --> notes_create_base
  notes_create_disc --> notes_create_base
  notes_create_diff --> notes_create_base
  notes_create_img_diff --> notes_create_base
  notes_update_note --> notes_update_base
  notes_update_img_diff --> notes_update_base

  %% Notes features reuse service compatibility
  snip_create --> notes_service_compat
  snip_update --> notes_service_compat
  issues_create --> spam_protect

  %% Alerts, incidents, timeline events
  im_tag_create --> im_tag_base
  im_tag_base --> base_mut
  im_event_create --> im_event_base
  im_event_update --> im_event_base
  im_event_promote --> im_event_base
  im_event_base --> base_mut

  %% Releases & assets logical layering
  rel_create --> base_rel
  rel_update --> base_rel
  rel_delete --> base_rel
  rel_asset_create --> finds_project
  rel_asset_update --> finds_project
  rel_asset_delete --> finds_project

  %% ML Model registry
  ml_models_create --> finds_project
  ml_models_edit --> finds_project
  ml_models_destroy --> finds_project
  ml_models_delete --> finds_project
  ml_model_versions_create --> finds_project
  ml_model_versions_edit --> finds_project
  ml_model_versions_delete --> finds_project

  %% Members/Groups/Projects bulk update
  members_projects_bulk --> members_bulk
  members_groups_bulk --> members_bulk

  %% Award Emojis logical inheritance
  award_add --> award_base
  award_remove --> award_base
  award_toggle --> award_base

  %% Package registry/cleanup
  pkg_cleanup_policy_update --> finds_project

  %% CI/CD relations
  ci_jobtoken_add --> finds_project
  ci_jobtoken_remove --> finds_project
  ci_jobtoken_update --> finds_project
  ci_jobtoken_allowlist --> finds_project
  ci_jobtoken_clear --> finds_project
  ci_runner_update --> ci_catalog_inputargs

  %% Supporting mutation utilities are shared
  finds_project -->|shared| iss_create
  finds_project -->|shared| proj_blobs_remove
  finds_project -->|shared| proj_sync_fork
  finds_project -->|shared| clust_agent_create
  finds_project -->|shared| clust_agenttokens_create
  finds_project -->|shared| env_create

  %% Data structure flow: Mutations produce/transform types
  ach_create --> achievement_type
  ach_update --> achievement_type
  ach_delete --> achievement_type
  ach_update_user --> user_achievement_type
  ach_update_user_priorities --> user_achievement_type
  ach_delete_user --> user_achievement_type
  rel_create -->|outputs| rel_asset_create
  rel_update -->|outputs| rel_asset_update
  rel_delete -->|outputs| rel_asset_delete
  labels_create --> label_type
  ci_pipeline_retry --> pipeline_type
  ci_job_cancel --> job_type

  %% Incident management: tags/events linked via types, outputs
  im_tag_create --> timeline_event_tag_type
  im_event_create --> timeline_event_type
  im_event_update --> timeline_event_type
  im_event_promote --> timeline_event_type

  %% Notes & snippets: share base, and affect note/snippet types
  notes_create_note --> notes_create_disc
  notes_create_note --> notes_create_diff
  notes_create_note --> notes_create_img_diff
  snip_create --> snip_base
  snip_update --> snip_base
  snip_destroy --> snip_base
  snip_mark_spam --> snip_base

  %% Alert management logical dependencies
  alert_set_assignees --> alert_update_status
  alert_update_status --> alert_create_issue
  alerts_todo_create --> alert_update_status
  am_http_create --> am_http_base
  am_http_update --> am_http_base
  am_http_destroy --> am_http_base
  am_http_reset --> am_http_base
  am_prom_create --> am_prom_base
  am_prom_update --> am_prom_base
  am_prom_reset --> am_prom_base

  %% Snippets/link to spam protect
  snip_create --> spam_protect
  snip_update --> spam_protect
  snip_mark_spam --> spam_protect

  %% ----Saved Replies logical flow----
  savedreplies_update --> savedreplies_base
  savedreplies_destroy --> savedreplies_base

  %% Custom emoji
  custom_emoji_create --> finds_group
  custom_emoji_destroy --> finds_group

  %% Containers/Registry: destroy tags use destroy base pattern
  container_repo_destroy_tags --> container_repo_base
  container_repo_destroy --> container_repo_base

  %% Integration: exclusions link
  integrations_exclusions_create --> integrations_exclusions_delete

  %% Timelogs: consistent base and output
  timelogs_create --> timelogs_base
  timelogs_delete --> timelogs_base

  %% Boards/Lanes/Issues flow
  boards_destroy[Boards::Destroy]:::core
  boards_update[Boards::Update]:::core
  boards_create[Boards::Create]:::core
  boards_lists_update[Boards::Lists::Update]:::core
  boards_lists_destroy[Boards::Lists::Destroy]:::core
  boards_lists_create[Boards::Lists::Create]:::core
  boards_lists_basecreate[Boards::Lists::BaseCreate]:::core
  boards_lists_baseupdate[Boards::Lists::BaseUpdate]:::core
  boards_common_args[Boards::CommonMutationArguments]:::support
  boards_issues_move[Boards::Issues::IssueMoveList]:::core
  boards_create --> boards_common_args
  boards_update --> boards_common_args
  boards_lists_create --> boards_lists_basecreate
  boards_lists_update --> boards_lists_baseupdate

  %% Organization Mutations
  org_create[Organizations::Create]:::core
  org_update[Organizations::Update]:::core
  org_users_update[Organizations::OrganizationUsers::Update]:::core
  org_create --> org_update
  org_update --> org_users_update

  %% Timelog relationships: base unifies
  timelogs_create --> timelogs_base
  timelogs_delete --> timelogs_base

  %% === GROUPING SUBGRAPHS ===

  class sg_base grouping
  class sg_issues grouping
  class sg_merge_requests grouping
  class sg_notes grouping
  class sg_snippets grouping
  class sg_labels grouping
  class sg_projects grouping
  class sg_environments grouping
  class sg_clusters grouping
  class sg_terraform grouping
  class sg_ci grouping
  class sg_achievements grouping
  class sg_alert_management grouping
  class sg_todos grouping
  class sg_supp_util grouping
  class sg_data_types grouping
  class sg_incident_mgmt grouping
  class sg_integrations grouping
  class sg_subscriptions grouping
  class sg_ml grouping
  class sg_release grouping
  class sg_containers grouping
  class sg_award_emoji grouping
  class sg_savedreplies grouping
  class sg_misc grouping

  %% === INITIALIZATION/SPECIALIZATION SEQUENCES ===
  %% Core entry: BaseMutation
  base_mut --> sg_issues
  base_mut --> sg_merge_requests
  base_mut --> sg_notes
  base_mut --> sg_snippets
  base_mut --> sg_labels
  base_mut --> sg_projects
  base_mut --> sg_environments
  base_mut --> sg_clusters
  base_mut --> sg_terraform
  base_mut --> sg_ci
  base_mut --> sg_achievements
  base_mut --> sg_alert_management
  base_mut --> sg_todos
  base_mut --> sg_ml
  base_mut --> sg_release
  base_mut --> sg_containers
  base_mut --> sg_award_emoji
  base_mut --> sg_incident_mgmt
  base_mut --> sg_integrations
  base_mut --> sg_misc
  base_mut --> sg_subscriptions

  %% Subscription depends on subscription base
  subs_notes_deleted --> subs_base
  subs_user_mr_updated --> subs_base
  subs_ci_pipeline_status --> subs_base
  subs_issuable_updated --> subs_base
  subs_workitem_updated --> subs_base

  %% Shared data structure usage
  iss_create --> user_achievement_type
  iss_update --> user_achievement_type
  ach_create --> achievement_type
  mr_create --> label_type
  mr_update --> label_type
  notes_create_note --> label_type

  %% ML registry subgraph usage
  ml_models_create --> ml_model_versions_create
  ml_models_edit --> ml_model_versions_edit
  ml_models_destroy --> ml_model_versions_delete

  %% Highlighted data transformation flow
  ach_update_user_priorities --> user_achievement_type

  %% GraphQL Input types & supporting types
  ci_pipelinesched_varin --> ci_runner_bulkpause
  ci_pipelinesched_varin --> ci_runner_bulkdel

  %% Exclusions create/delete logical coupling
  integrations_exclusions_create --> integrations_exclusions_delete

  %% Timeline event tags use tag type
  im_tag_create --> timeline_event_tag_type

  %% --- END ---
```