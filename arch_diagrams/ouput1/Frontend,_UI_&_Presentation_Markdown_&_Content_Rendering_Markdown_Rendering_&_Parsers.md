```mermaid
flowchart TD
  %% --- Mermaid-Styling for Pastel Domain ---
  %% Node Styles
  classDef coreDomain fill:#D4F1F9,stroke:#A8D3E6,stroke-width:2px,color:#222,stroke-dasharray:0,rx:10,ry:10
  classDef utility fill:#FFF8DC,stroke:#FFE4A0,stroke-width:2px,color:#444,rx:10,ry:10
  classDef dataStruct fill:#E0F8E0,stroke:#C2EABD,stroke-width:2px,color:#333,rx:10,ry:10
  classDef errorHandling fill:#FFE4E1,stroke:#FFB6B0,stroke-width:2px,color:#333,rx:10,ry:10
  classDef init fill:#E6E6FA,stroke:#B8A8F0,stroke-width:2px,color:#333,rx:10,ry:10
  classDef subgraphGray fill:#F8F8F8,stroke:#DFDFFF,stroke-width:3px,color:#111

  %% --- Main Domain Cluster ---
  subgraph Markdown_Rendering_Parsers["Markdown Rendering & Parsers":::subgraphGray]
    direction TB

    %% ----------------- Core Concepts: Markdown Rendering -----------------
    subgraph Markup_Pipelines["Banzai Pipeline Architecture":::subgraphGray]
      direction TB
      banzaiRb(["lib/banzai.rb\nBanzai: Markdown Rendering Facade"]):::coreDomain
      banzaiPipeline(["lib/banzai/pipeline.rb\nPipeline Loader and Resolver"]):::coreDomain
      renderer(["lib/banzai/renderer.rb\nBanzai: Render/Process Core"]):::coreDomain
      commitRenderer(["lib/banzai/commit_renderer.rb\nRender Commit Info"]):::coreDomain
      querying(["lib/banzai/querying.rb\nReference Node Querying"]):::utility

      %% Base and Combined Pipelines
      basePipeline(["lib/banzai/pipeline/base_pipeline.rb\nBasePipeline Pattern"]):::coreDomain
      fullPipeline(["lib/banzai/pipeline/full_pipeline.rb\nCombined GFM + Plain"]):::coreDomain
      gfmPipeline(["lib/banzai/pipeline/gfm_pipeline.rb\nGitLab Flavored Markdown"]):::coreDomain
      plainMarkdownPipeline(["lib/banzai/pipeline/plain_markdown_pipeline.rb\nPlain Markdown"]):::coreDomain
      preProcessPipeline(["lib/banzai/pipeline/pre_process_pipeline.rb\nPreprocessing"]):::coreDomain
      postProcessPipeline(["lib/banzai/pipeline/post_process_pipeline.rb\nPostprocessing"]):::coreDomain
      singleLinePipeline(["lib/banzai/pipeline/single_line_pipeline.rb\nSingle Line"]):::coreDomain
      singleLineMarkdownPipeline(["lib/banzai/pipeline/single_line_markdown_pipeline.rb\nSingle Line Markdown"]):::coreDomain
      descriptionPipeline(["lib/banzai/pipeline/description_pipeline.rb\nMarkdown Description\n- Limited Markdown"]):::coreDomain
      commitDescriptionPipeline(["lib/banzai/pipeline/commit_description_pipeline.rb\nCommit Description\nextends Single Line"]):::coreDomain
      atomPipeline(["lib/banzai/pipeline/atom_pipeline.rb\nAtom RSS/Syndication"]):::coreDomain
      emailPipeline(["lib/banzai/pipeline/email_pipeline.rb\nMarkdown-to-Email"]):::coreDomain
      notePipeline(["lib/banzai/pipeline/note_pipeline.rb\nNotes Markdown"]):::coreDomain
      labelPipeline(["lib/banzai/pipeline/label_pipeline.rb\nLabel Syntax"]):::coreDomain
      quickActionPipeline(["lib/banzai/pipeline/quick_action_pipeline.rb\nQuick Action DSL"]):::coreDomain
      emojiPipeline(["lib/banzai/pipeline/emoji_pipeline.rb\nEmoji Only"]):::coreDomain
      markupPipeline(["lib/banzai/pipeline/markup_pipeline.rb\nOther Markup Syntax"]):::coreDomain
      wikiPipeline(["lib/banzai/pipeline/wiki_pipeline.rb\nWiki Markdown"]):::coreDomain
      asciiDocPipeline(["lib/banzai/pipeline/ascii_doc_pipeline.rb\nAsciiDoc Block"]):::coreDomain

      %% Specialized/EE pipelines
      incidentTimelinePipeline(["lib/banzai/pipeline/incident_management/timeline_event_pipeline.rb\nIncident Management"]):::coreDomain
      jiraAdfPipeline(["lib/banzai/pipeline/jira_import/adf_commonmark_pipeline.rb\nJira ADF Importer"]):::coreDomain
      serviceDeskEmailPipeline(["lib/banzai/pipeline/service_desk_email_pipeline.rb\nService Desk Email"]):::coreDomain
      broadcastMessagePipeline(["lib/banzai/pipeline/broadcast_message_pipeline.rb\nBroadcast Message"]):::coreDomain

      %% Interconnections pipelines extend/integrate
      fullPipeline --extends--> plainMarkdownPipeline
      fullPipeline --uses--> gfmPipeline
      notePipeline --extends--> fullPipeline
      wikiPipeline --extends--> fullPipeline
      atomPipeline --extends--> fullPipeline
      descriptionPipeline --extends--> fullPipeline
      plainMarkdownPipeline --extends--> basePipeline
      singleLinePipeline --extends--> basePipeline
      singleLineMarkdownPipeline --extends--> singleLinePipeline
      commitDescriptionPipeline --extends--> singleLinePipeline
      commitDescriptionPipeline --uses--> singleLinePipeline
      markupPipeline --extends--> basePipeline
      asciiDocPipeline --extends--> basePipeline
      preProcessPipeline --extends--> basePipeline
      postProcessPipeline --extends--> basePipeline
      emojiPipeline --extends--> basePipeline
      labelPipeline --extends--> basePipeline
      jiraAdfPipeline --extends--> basePipeline
      quickActionPipeline --extends--> basePipeline
      broadcastMessagePipeline --extends--> descriptionPipeline
      incidentTimelinePipeline --extends--> plainMarkdownPipeline
      serviceDeskEmailPipeline --extends--> emailPipeline
    end

    %% ----------------- Reference Parsers -----------------
    subgraph Reference_Parsers["Reference Parsers\nResolve Markdown References":::subgraphGray]
      direction TB
      refBaseParser(["lib/banzai/reference_parser/base_parser.rb\nCore Ref Parser"]):::coreDomain
      refIssuable(["lib/banzai/reference_parser/issuable_parser.rb\nIssuable Ref"]):::coreDomain
      refUser(["lib/banzai/reference_parser/user_parser.rb\nUser Ref"]):::coreDomain
      refDesign(["lib/banzai/reference_parser/design_parser.rb\nDesign Ref"]):::coreDomain
      refProject(["lib/banzai/reference_parser/project_parser.rb\nProject Ref"]):::coreDomain
      refMergeRequest(["lib/banzai/reference_parser/merge_request_parser.rb\nMR Ref"]):::coreDomain
      refMilestone(["lib/banzai/reference_parser/milestone_parser.rb\nMilestone Ref"]):::coreDomain
      refCommit(["lib/banzai/reference_parser/commit_parser.rb\nCommit Ref"]):::coreDomain
      refEpic(["lib/banzai/reference_parser/epic_parser.rb\nEpic Ref"]):::coreDomain
      refWikiPage(["lib/banzai/reference_parser/wiki_page_parser.rb\nWikiPage Ref"]):::coreDomain
      refMentionedProject(["lib/banzai/reference_parser/mentioned_project_parser.rb\nMent. Project"]):::coreDomain
      refMentionedGroup(["lib/banzai/reference_parser/mentioned_group_parser.rb\nMent. Group"]):::coreDomain
      refWorkItem(["lib/banzai/reference_parser/work_item_parser.rb\nWork Item"]):::coreDomain
      refLabel(["lib/banzai/reference_parser/label_parser.rb\nLabel Ref"]):::coreDomain
      refMentionedUser(["lib/banzai/reference_parser/mentioned_user_parser.rb\nMentioned User"]):::coreDomain
      refSnippet(["lib/banzai/reference_parser/snippet_parser.rb\nSnippet Ref"]):::coreDomain
      refExternalIssue(["lib/banzai/reference_parser/external_issue_parser.rb\nExt. Issue"]):::coreDomain
      refVulnerability(["lib/banzai/reference_parser/vulnerability_parser.rb\nVulnerability"]):::coreDomain
      refDirectUser(["lib/banzai/reference_parser/directly_addressed_user_parser.rb\nDirectly Addressed"]):::coreDomain
      refFeatureFlag(["lib/banzai/reference_parser/feature_flag_parser.rb\nFeature Flag"]):::coreDomain
      refCommitRange(["lib/banzai/reference_parser/commit_range_parser.rb\nCommit Range"]):::coreDomain
      refIssue(["lib/banzai/reference_parser/issue_parser.rb\nIssue Ref"]):::coreDomain

      %% Inheritance relations
      refIssuable --extends--> refBaseParser
      refUser --extends--> refBaseParser
      refDesign --extends--> refBaseParser
      refProject --extends--> refBaseParser
      refMilestone --extends--> refBaseParser
      refCommit --extends--> refBaseParser
      refWikiPage --extends--> refBaseParser
      refLabel --extends--> refBaseParser
      refMentionedGroup --extends--> refBaseParser
      refMentionedUser --extends--> refBaseParser
      refSnippet --extends--> refBaseParser
      refExternalIssue --extends--> refBaseParser
      refFeatureFlag --extends--> refBaseParser
      refCommitRange --extends--> refBaseParser
      refMergeRequest --extends--> refIssuable
      refIssue --extends--> refIssuable
      refWorkItem --extends--> refIssue
      refEpic --extends--> refIssuable
      refMentionedProject --extends--> refProject
      refDirectUser --extends--> refUser
      refVulnerability --extends--> refIssuable
    end

    %% ----------------- Markdown Cache & Data Structures -----------------
    subgraph Cache_and_DataStructures["Markdown Cache & Data Structures":::subgraphGray]
      direction TB
      markdownCache(["lib/gitlab/markdown_cache.rb\nCache Versioning"]):::dataStruct
      cacheRedisStore(["lib/gitlab/markdown_cache/redis/store.rb\nRedis Store"]):::dataStruct
      cacheRedisExtension(["lib/gitlab/markdown_cache/redis/extension.rb\nActiveRecord/Redis Ext"]):::dataStruct
      cacheActiveRecordExtension(["lib/gitlab/markdown_cache/active_record/extension.rb\nAR Markdown Cache"]):::dataStruct
    end

    %% ----------------- Markup Engines & Parsers -----------------
    subgraph Markup_Engines["Markdown Engines/Parsers":::subgraphGray]
      direction TB
      glfmMarkdown(["lib/banzai/filter/markdown_engines/glfm_markdown.rb\nGLFM Engine"]):::coreDomain
      cmark(["lib/banzai/filter/markdown_engines/cmark.rb\nCMark Engine"]):::coreDomain
      kramdownCommonmark(["lib/kramdown/converter/commonmark.rb\nKramdown to CM Converter"]):::coreDomain
      kramdownAtlassian(["lib/kramdown/parser/atlassian_document_format.rb\nADF Parser"]):::coreDomain
      htmlToMarkdownParser(["lib/gitlab/email/html_to_markdown_parser.rb\nHTML -> Markdown"]):::utility
      markupHelper(["lib/gitlab/markup_helper.rb\nDetect Markup Types"]):::utility
    end

    %% ----------------- AsciiDoc/Media/Converters -----------------
    subgraph Other_Parsers_Converters["AsciiDoc, Media & Converters":::subgraphGray]
      direction TB
      asciidocInclude(["lib/gitlab/asciidoc/include_processor.rb\nAsciiDoc Include"]):::utility
      asciidocHtml5(["lib/gitlab/asciidoc/html5_converter.rb\nAsciiDoc HTML5 Conv"]):::utility
      rougeHtmlGitlab(["lib/rouge/formatters/html_gitlab.rb\nRouge Syntax HL"]):::utility
      rougeCommonmarkPlugin(["lib/rouge/plugins/common_mark.rb\nRouge CommonMark\nPlugin"]):::utility
      stringRegexMarker(["lib/gitlab/string_regex_marker.rb\nString Regex Marking"]):::utility
      frontMatter(["lib/gitlab/front_matter.rb\nFront Matter Parser"]):::utility
      utilsNokogiri(["lib/gitlab/utils/nokogiri.rb\nCSS->XPath"]):::utility
      utilsMarkdown(["lib/gitlab/utils/markdown.rb\nString to Anchor"]):::utility
      utilsTomlParser(["lib/gitlab/utils/toml_parser.rb\nParse TOML"]):::utility
    end

    %% ----------------- Changelogs & Template Parser -----------------
    subgraph Changelog_and_Template["Changelog / Template Parsing":::subgraphGray]
      direction TB
      changelogRelease(["lib/gitlab/changelog/release.rb\nChangelog Release Struct"]):::dataStruct
      changelogCommitter(["lib/gitlab/changelog/committer.rb\nCommitter"]):::utility
      changelogError(["lib/gitlab/changelog/error.rb\nChangelog Error"]):::errorHandling
      templateEvalState(["lib/gitlab/template_parser/eval_state.rb\nTemplate Eval State"]):::dataStruct
      templateParser(["lib/gitlab/template_parser/parser.rb\nTemplate Parser"]):::utility
      templateAST(["lib/gitlab/template_parser/ast.rb\nTemplate AST"]):::dataStruct
      templateError(["lib/gitlab/template_parser/error.rb\nTemplate Error"]):::errorHandling
    end

    %% ----------------- Initialization/Setup -----------------
    subgraph Init_and_Patches["Initialization & Engine Patches":::subgraphGray]
      direction TB
      wikiClothPatch(["config/initializers/wikicloth_ruby_3_patch.rb\nWikiCloth Math/Funcs"]):::init
      wikiClothLuaPatch(["config/initializers/wikicloth_disable_lua_patch.rb\nWikiCloth Lua Off"]):::init
      contentSecurityPolicyPatch(["app/controllers/concerns/content_security_policy_patch.rb\nContent Security Policy"]):::init
    end

    %% ----------------- Application/Service Layer -----------------
    subgraph Application_Services["Application/Service Layer":::subgraphGray]
      direction TB
      helpController(["app/controllers/help_controller.rb\nMarkdown Help UI"]):::coreDomain
      searchHelper(["app/helpers/search_helper.rb\nSearch & Params Helper"]):::utility
      importHelper(["app/helpers/import_helper.rb\nImport Helper"]):::utility
      previewMarkdown(["app/controllers/concerns/preview_markdown.rb\nController Concern\nfor Markdown Service Params"]):::coreDomain
      previewMarkdownService(["app/services/preview_markdown_service.rb\nPreview Markdown"]):::coreDomain
      markdownRewriterService(["app/services/markdown_content_rewriter_service.rb\nMarkdown Rewriter"]):::coreDomain
      markupRenderingService(["app/services/markup/rendering_service.rb\nGeneric Markup Renderer"]):::coreDomain
      statusPageRenderer(["ee/app/serializers/status_page/renderer.rb\nStatusPage Renderer"]):::coreDomain
      statusPostProcessPipeline(["ee/lib/gitlab/status_page/pipeline/post_process_pipeline.rb\nStatusPage PostProcess"]):::coreDomain
      statusImageFilter(["ee/lib/gitlab/status_page/filter/image_filter.rb\nStatusPage Img Filter"]):::coreDomain
      remoteDevReadmeHelper(["ee/app/helpers/remote_development/readme_helper.rb\nRemote Dev Readme"]):::utility
    end

    %% ----------------- Error Handling -----------------
    subgraph Error_Handling["Error Handling":::subgraphGray]
      direction TB
      rubocopDocLinkCop(["rubocop/cop/gitlab/documentation_links/link.rb\nRuboCop Doc Link\nStatic Analysis"]):::errorHandling
    end

    %% ----------------- Testing/Spec Files group as utility -----------------
    subgraph Spec_Files["Testing/Spec Files":::subgraphGray]
      direction TB
      banzaiDescSpec(["spec/lib/banzai/pipeline/description_pipeline_spec.rb"]):::utility
      banzaiEmojiSpec(["spec/lib/banzai/pipeline/emoji_pipeline_spec.rb"]):::utility
      banzaiPlainMdSpec(["spec/lib/banzai/pipeline/plain_markdown_pipeline_spec.rb"]):::utility
      banzaiSingleLineMdSpec(["spec/lib/banzai/pipeline/single_line_markdown_pipeline_spec.rb"]):::utility
      banzaiSingleLineSpec(["spec/lib/banzai/pipeline/single_line_pipeline_spec.rb"]):::utility
      featureMdSpec(["spec/features/markdown/markdown_spec.rb"]):::utility
      featureMathSpec(["spec/features/markdown/math_spec.rb"]):::utility
      fileTypeDetectionSpec(["spec/lib/gitlab/file_type_detection_spec.rb"]):::utility
      prependableSpec(["spec/lib/gitlab/patch/prependable_spec.rb"]):::utility
      cacheMarkdownFieldSpec(["spec/models/concerns/cache_markdown_field_spec.rb"]):::utility
    end

    %% ---------------------------------------------------------------------------
    %% ==================== LOGICAL/ARCHITECTURAL RELATIONSHIPS ===================
    %% Core rendering entry points
    banzaiRb --calls/uses--> banzaiPipeline
    banzaiRb --calls--> renderer
    banzaiRb --calls--> commitRenderer

    %% Pipeline loader relationships
    banzaiPipeline --resolves--> fullPipeline
    banzaiPipeline --resolves--> gfmPipeline
    banzaiPipeline --resolves--> plainMarkdownPipeline
    banzaiPipeline --resolves--> notePipeline
    banzaiPipeline --resolves--> singleLinePipeline
    banzaiPipeline --resolves--> atomPipeline
    banzaiPipeline --resolves--> emailPipeline
    banzaiPipeline --resolves--> markupPipeline
    banzaiPipeline --resolves--> descriptionPipeline

    %% Renderer <-> Pipeline (entry point)
    renderer --evaluates--> basePipeline
    renderer --calls--> postProcessPipeline

    %% Commit renderer uses renderer
    commitRenderer --calls--> renderer

    %% Query/Filters
    querying --used by--> refBaseParser
    querying --used by--> renderer

    %% Markup Engines Used By Pipelines
    gfmPipeline --uses--> glfmMarkdown
    gfmPipeline --uses--> cmark
    plainMarkdownPipeline --uses--> kramdownCommonmark
    plainMarkdownPipeline --uses--> cmark
    jiraAdfPipeline --uses--> kramdownAtlassian
    asciiDocPipeline --uses--> asciidocInclude
    asciiDocPipeline --uses--> asciidocHtml5

    %% Utilities aiding filter/engine
    cmark --uses--> utilsMarkdown
    glfmMarkdown --uses--> utilsMarkdown
    kramdownCommonmark --uses--> utilsMarkdown

    %% Rouge, HTML, and syntax highlighting
    glfmMarkdown --syntax--> rougeHtmlGitlab
    asciiDocPipeline --syntax--> rougeHtmlGitlab
    kramdownCommonmark --syntax--> rougeHtmlGitlab
    kramdownAtlassian --syntax--> rougeHtmlGitlab
    rougeHtmlGitlab --plugin--> rougeCommonmarkPlugin

    %% MarkupHelper detects what rendering is needed
    markupHelper --used by--> renderer
    markupHelper --used by--> helpController
    markupHelper --used by--> statusPageRenderer
    helpController --uses--> markupHelper
    statusPageRenderer --calls--> markupHelper

    %% Markdown Cache Interactions
    markdownCache --versioning_for--> cacheRedisStore
    markdownCache --included_in--> cacheActiveRecordExtension
    markdownCache --included_in--> cacheRedisExtension
    cacheActiveRecordExtension --extends--> cacheRedisExtension
    cacheRedisStore --provides cache for--> cacheActiveRecordExtension
    cacheRedisStore --provides cache for--> cacheRedisExtension

    cacheActiveRecordExtension --included_in--> previewMarkdownService
    cacheActiveRecordExtension --included_in--> previewMarkdown

    %% Markdown Services use pipelines
    previewMarkdownService --calls--> renderer
    previewMarkdownService --uses--> markdownRewriterService
    previewMarkdownService --contributes_to--> previewMarkdown

    %% Application services to rendering
    helpController --calls--> previewMarkdownService
    helpController --calls--> markupRenderingService
    previewMarkdown --used by--> helpController

    %% StatusPage Customization (EE)
    statusPageRenderer --uses--> postProcessPipeline
    statusPageRenderer --customizes--> statusPostProcessPipeline
    statusPostProcessPipeline --uses--> statusImageFilter

    %% Import, Search, and RemoteDev: Utilities integrate with rendering
    importHelper --uses--> markupHelper
    searchHelper --searches--> helpController
    remoteDevReadmeHelper --extends--> markupHelper

    %% Front matter, string utilities, etc
    frontMatter --preprocesses--> plainMarkdownPipeline
    stringRegexMarker --used by--> kramdownAtlassian
    utilsNokogiri --used by--> statusImageFilter
    utilsNokogiri --used by--> asciidocInclude

    %% Error handling and validation
    rubocopDocLinkCop --validates--> helpController
    changelogError --error_for--> changelogRelease
    changelogCommitter --uses--> changelogRelease
    changelogCommitter --rescues--> changelogError
    templateError --handles errors in--> templateParser
    templateError --handles errors in--> templateEvalState
    templateParser --creates--> templateAST
    templateAST --evaluated_by--> templateEvalState

    %% Controller Concerns/Content Security
    contentSecurityPolicyPatch --applies_on--> helpController

    %% EE StatusPage pipelines link
    postProcessPipeline --extends--> statusPostProcessPipeline

    %% Markdown Content Rewriter usage
    markdownRewriterService --rewrites--> helpController
    markdownRewriterService --rewrites--> previewMarkdownService

    %% -------- Core Abstractions and Structural Patterns --------
    %% BasePipeline: All Banzai pipelines are subclasses of BasePipeline.
    %% Reference parsing: all reference_parser subclasses inherit base ref logic.
    refBaseParser --used by--> gfmPipeline
    refIssuable --used by--> refMergeRequest
    refIssuable --used by--> refIssue
    refIssuable --used by--> refEpic
    refUser --used by--> refDirectUser
    refProject --used by--> refMentionedProject

    %% Testing/specs group, connect to main implementations
    banzaiDescSpec --tests--> descriptionPipeline
    banzaiEmojiSpec --tests--> emojiPipeline
    banzaiPlainMdSpec --tests--> plainMarkdownPipeline
    banzaiSingleLineMdSpec --tests--> singleLineMarkdownPipeline
    banzaiSingleLineSpec --tests--> singleLinePipeline
    cacheMarkdownFieldSpec --tests--> cacheActiveRecordExtension
    featureMdSpec --tests--> banzaiPipeline
    featureMathSpec --tests--> gfmPipeline
    fileTypeDetectionSpec --tests--> markupHelper
    prependableSpec --tests--> cacheRedisExtension

    %% Initialization/patching files
    wikiClothPatch --patches--> asciidocInclude
    wikiClothLuaPatch --patches--> asciidocInclude

    %% App controller/services interactions
    markupRenderingService --calls--> renderer
    markupRenderingService --used_by--> helpController

    %% Email-to-Markdown parser
    htmlToMarkdownParser --used by--> emailPipeline
    emailPipeline --parses--> htmlToMarkdownParser

    %% Changelog/template parsing connections
    changelogRelease --to_markdown--> templateParser
    changelogCommitter --writes--> changelogRelease

    %% Remote Dev Readme helper reuse
    remoteDevReadmeHelper --used by--> helpController

    %% AsciiDoc and Media
    asciidocInclude --uses--> frontMatter
    asciidocHtml5 --used by--> asciiDocPipeline

  end
```