```mermaid
flowchart TD
  %% === Domain: Projects, Groups & Organization Management / Project Management / Project Settings & Configuration ===

  %% Main grouping for all files in this domain
  subgraph Domain["Project Settings & Configuration" ]
    direction TB
    style Domain fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded

    %% ========== CORE DOMAIN CONCEPTS ==========
    subgraph SettingsModels["Settings Models" ]
      direction TB
      style SettingsModels fill:#F8F8F8,stroke:#D4F1F9,stroke-width:1,rounded

      PS["project_setting.rb":::coreDomain]:::coreDomain
      ECS["ee/app/models/ee/project_setting.rb":::coreDomain]:::coreDomain
      CFS["ee/app/models/compliance_management/compliance_framework/project_settings.rb":::coreDomain]:::coreDomain
      SPJ["ee/app/models/status_page/project_setting.rb":::coreDomain]:::coreDomain
      PDP["ee/app/models/projects/deletion_schedule.rb":::coreDomain]:::coreDomain
      PCF["app/models/projects/topic.rb":::coreDomain]:::coreDomain
      PCA["app/models/project_custom_attribute.rb":::coreDomain]:::coreDomain
      NLCCS["app/models/namespace_ci_cd_setting.rb":::coreDomain]:::coreDomain
      SSHK["app/models/ssh_host_key.rb":::coreDomain]:::coreDomain
      PD["app/models/pages_domain.rb":::coreDomain]:::coreDomain
      APP["app/models/appearance.rb":::coreDomain]:::coreDomain
      BF_SQ["app/models/projects/branch_rules/squash_option.rb":::coreDomain]:::coreDomain
      OFC["app/models/operations/feature_flags_client.rb":::coreDomain]:::coreDomain
    end

    subgraph Finders["Project Settings Finders" ]
      direction TB
      style Finders fill:#F8F8F8,stroke:#D4F1F9,stroke-width:1,rounded

      PROJ_FD["app/finders/projects_finder.rb":::coreDomain]:::coreDomain
      ADMIN_PROJ_FD["app/finders/admin/projects_finder.rb":::coreDomain]:::coreDomain
      NS_PROJ_FD["app/finders/namespaces/projects_finder.rb":::coreDomain]:::coreDomain
      AC_PROJ_FD["app/finders/autocomplete/project_finder.rb":::supportFile]:::supportFile
      VSCS_FINDER["app/finders/vs_code/settings/settings_finder.rb":::supportFile]:::supportFile
    end

    subgraph ProjectControllers["Project Settings Controllers" ]
      direction TB
      style ProjectControllers fill:#F8F8F8,stroke:#D4F1F9,stroke-width:1,rounded

      ORG_PROJ_CTRL["app/controllers/organizations/projects_controller.rb":::coreDomain]:::coreDomain
      ADMIN_LB_CTRL["app/controllers/admin/labels_controller.rb":::coreDomain]:::coreDomain
      PRJ_APP_CTRL["app/controllers/projects/application_controller.rb":::coreDomain]:::coreDomain
      PRJ_PAGES_CTRL["app/controllers/projects/pages_controller.rb":::coreDomain]:::coreDomain
      PRJ_VARS_CTRL["app/controllers/projects/variables_controller.rb":::coreDomain]:::coreDomain
      PRJ_USAGE_CTRL["app/controllers/projects/usage_quotas_controller.rb":::coreDomain]:::coreDomain
      PRJ_SD_CTRL["app/controllers/projects/service_desk_controller.rb":::coreDomain]:::coreDomain
      PRJ_SETTINGS_MR["app/controllers/projects/settings/merge_requests_controller.rb":::coreDomain]:::coreDomain
      PRJ_SETTINGS_REPO["app/controllers/projects/settings/repository_controller.rb":::coreDomain]:::coreDomain
      PRJ_SETTINGS_OP["app/controllers/projects/settings/operations_controller.rb":::coreDomain]:::coreDomain
      PRJ_SETTINGS_CICD["app/controllers/projects/settings/ci_cd_controller.rb":::coreDomain]:::coreDomain
      PRJ_SETTINGS_INTLOG["app/controllers/projects/settings/integration_hook_logs_controller.rb":::coreDomain]:::coreDomain
      PRJ_PIPELINE_SETTINGS["app/controllers/projects/pipelines_settings_controller.rb":::coreDomain]:::coreDomain
    end

    subgraph EEProjectControllers["EE Project Settings Controllers" ]
      direction TB
      style EEProjectControllers fill:#F8F8F8,stroke:#D4F1F9,stroke-width:1,rounded

      EE_PCTRL_MR["ee/app/controllers/ee/projects/settings/merge_requests_controller.rb":::coreDomain]:::coreDomain
      EE_PCTRL_CICD["ee/app/controllers/ee/projects/settings/ci_cd_controller.rb":::coreDomain]:::coreDomain
      EE_PCTRL_PUSH_RULES["ee/app/controllers/projects/push_rules_controller.rb":::coreDomain]:::coreDomain
      EE_PCTRL_LOGS["ee/app/controllers/projects/logs_controller.rb":::coreDomain]:::coreDomain
      EE_PCTRL_SD["ee/app/controllers/ee/projects/service_desk_controller.rb":::coreDomain]:::coreDomain
      EE_PCTRL_SECRETS["ee/app/controllers/projects/secrets_controller.rb":::coreDomain]:::coreDomain
    end

    subgraph Services["Project Settings Services" ]
      direction TB
      style Services fill:#F8F8F8,stroke:#D4F1F9,stroke-width:1,rounded

      RESET_CACHE_SVC["app/services/reset_project_cache_service.rb":::supportFile]:::supportFile
      PAGES_UPDATE_SVC["app/services/pages/update_service.rb":::coreDomain]:::coreDomain
      PROJ_MARK_DEL_SVC["app/services/projects/mark_for_deletion_service.rb":::coreDomain]:::coreDomain
      PROJ_ADJ_DEL_SVC["app/services/projects/adjourned_deletion_service.rb":::coreDomain]:::coreDomain
      PROJ_DESTROY_SVC["app/services/projects/destroy_service.rb":::coreDomain]:::coreDomain
      PROJ_DISABLE_LEGACY_SVC["ee/app/services/projects/disable_legacy_inactive_projects_service.rb":::coreDomain]:::coreDomain
      PROJ_UPDATE_SVC["app/services/projects/update_service.rb":::coreDomain]:::coreDomain
      PROJ_TRANSFER_SVC["app/services/projects/transfer_service.rb":::coreDomain]:::coreDomain
      PROJ_IMPORT_ERR_FILT["app/services/projects/import_error_filter.rb":::supportFile]:::supportFile
      PROJ_OP_UPDATE_SVC["app/services/projects/operations/update_service.rb":::coreDomain]:::coreDomain
      ENV_UPDATE_SVC["app/services/environments/update_service.rb":::supportFile]:::supportFile
      PROJ_OPS_UPDATE_SVC["app/services/projects/operations/update_service.rb":::coreDomain]:::coreDomain
    end

    subgraph EEProjectServices["EE Project Settings Services" ]
      direction TB
      style EEProjectServices fill:#F8F8F8,stroke:#D4F1F9,stroke-width:1,rounded

      EE_PROJ_UPDATE_SVC["ee/app/services/ee/projects/update_service.rb":::coreDomain]:::coreDomain
      EE_PROJ_OPS_UPDATE_SVC["ee/app/services/ee/projects/operations/update_service.rb":::coreDomain]:::coreDomain
      EE_PROJ_UNLINK_FORK["ee/app/services/ee/projects/unlink_fork_service.rb":::coreDomain]:::coreDomain
      EE_PROJ_MARK_DEL_SVC["ee/app/services/ee/projects/mark_for_deletion_service.rb":::coreDomain]:::coreDomain
      EE_PROJ_DESTROY_SVC["ee/app/services/ee/projects/destroy_service.rb":::coreDomain]:::coreDomain
      EE_PROJ_RESTORE_SVC["ee/app/services/ee/projects/restore_service.rb":::coreDomain]:::coreDomain
      EE_PROJ_EN_DEPLOY_KEY_SVC["ee/app/services/ee/projects/enable_deploy_key_service.rb":::coreDomain]:::coreDomain
      EE_PROJ_DIS_DEPLOY_KEY_SVC["ee/app/services/ee/projects/disable_deploy_key_service.rb":::coreDomain]:::coreDomain
    end

    subgraph Policies["Settings & Project Policies" ]
      direction TB
      style Policies fill:#F8F8F8,stroke:#D4F1F9,stroke-width:1,rounded

      APP_SETTING_POLICY["app/policies/application_setting_policy.rb":::coreDomain]:::coreDomain
      APP_SETTING_TERM_POLICY["app/policies/application_setting/term_policy.rb":::coreDomain]:::coreDomain
      PRJ_CICD_POLICY["app/policies/project_ci_cd_setting_policy.rb":::coreDomain]:::coreDomain
      PRJ_STATS_POLICY["app/policies/project_statistics_policy.rb":::coreDomain]:::coreDomain
    end

    subgraph Serializers["Settings & Project Serializers" ]
      direction TB
      style Serializers fill:#F8F8F8,stroke:#D4F1F9,stroke-width:1,rounded

      PRJ_ENTITY["app/serializers/project_entity.rb":::supportFile]:::supportFile
      EE_GRP_ENTITY["ee/app/serializers/autocomplete/group_entity.rb":::supportFile]:::supportFile
      EE_ENV_SERIAL["ee/app/serializers/ee/environment_serializer.rb":::supportFile]:::supportFile
      EE_CLUSTER_ENV_SERIAL["ee/app/serializers/clusters/environment_serializer.rb":::supportFile]:::supportFile
      EE_SEC_PRJ_SERIAL["ee/app/serializers/security/project_serializer.rb":::supportFile]:::supportFile
    end

    subgraph Helpers["Project Settings Helpers" ]
      direction TB
      style Helpers fill:#F8F8F8,stroke:#D4F1F9,stroke-width:1,rounded

      PRJ_HELPER["app/helpers/projects_helper.rb":::supportFile]:::supportFile
      EE_PRJ_HELPER["ee/app/helpers/ee/projects_helper.rb":::supportFile]:::supportFile
      EE_SEC_CONFIG_HELPER["ee/app/helpers/ee/projects/security/configuration_helper.rb":::supportFile]:::supportFile
      FORM_HELPER["app/helpers/form_helper.rb":::supportFile]:::supportFile
      ADM_DEPLOY_KEY_HELPER["app/helpers/admin/deploy_key_helper.rb":::supportFile]:::supportFile
    end

    subgraph GraphQLTypesAndMutations["GraphQL Types / Mutations" ]
      direction TB
      style GraphQLTypesAndMutations fill:#F8F8F8,stroke:#D4F1F9,stroke-width:1,rounded

      PRJ_SORT_ENUM["app/graphql/types/projects/project_sort_enum.rb":::dataStruct]:::dataStruct
      EE_MUT_CICD_NS_UPDATE["ee/app/graphql/mutations/ci/namespace_ci_cd_settings_update.rb":::coreDomain]:::coreDomain
      EE_MUT_CICD_PRJ_UPDATE["ee/app/graphql/ee/mutations/ci/project_ci_cd_settings_update.rb":::coreDomain]:::coreDomain
      EE_MUT_PRJ_SETTINGS_UPDATE["ee/app/graphql/mutations/projects/project_settings_update.rb":::coreDomain]:::coreDomain
    end

    subgraph Workers["Workers & Background Jobs" ]
      direction TB
      style Workers fill:#F8F8F8,stroke:#D4F1F9,stroke-width:1,rounded

      INACTIVE_DEL_NOTIFY["app/workers/projects/inactive_projects_deletion_notification_worker.rb":::supportFile]:::supportFile
      PRJ_CACHE_WORKER["app/workers/project_cache_worker.rb":::supportFile]:::supportFile
      POST_CREATION_WORKER["app/workers/projects/post_creation_worker.rb":::supportFile]:::supportFile
      INACTIVE_DEL_CRON["app/workers/projects/inactive_projects_deletion_cron_worker.rb":::supportFile]:::supportFile
    end

    subgraph Validations["Validators" ]
      direction TB
      style Validations fill:#F8F8F8,stroke:#FFE4E1,stroke-width:1,rounded

      PRJ_PATH_VALIDATOR["app/validators/project_path_validator.rb":::errorHandling]:::errorHandling
      VALIDATIONS_INITIALIZER["config/initializers/6_validations.rb":::errorHandling]:::errorHandling
    end

    subgraph Eventing["Events & Auditing" ]
      direction TB
      style Eventing fill:#F8F8F8,stroke:#D4F1F9,stroke-width:1,rounded

      PRJ_ATTR_CHANGED_EVT["app/events/projects/project_attributes_changed_event.rb":::supportFile]:::supportFile
      PRJ_SETTING_AUDITOR["ee/lib/projects/project_setting_changes_auditor.rb":::supportFile]:::supportFile
    end

    subgraph ApplicationSettings["Application Settings Services" ]
      direction TB
      style ApplicationSettings fill:#F8F8F8,stroke:#FFF8DC,stroke-width:1,rounded

      APP_SETTINGS_BASE_SVC["app/services/application_settings/base_service.rb":::supportFile]:::supportFile
      APP_SETTINGS_UPD_SVC["app/services/application_settings/update_service.rb":::supportFile]:::supportFile
    end

    subgraph DomainConcerns["Domain Concerns" ]
      direction TB
      style DomainConcerns fill:#F8F8F8,stroke:#FFF8DC,stroke-width:1,rounded

      THROTTLED_TOUCH["app/models/concerns/throttled_touch.rb":::supportFile]:::supportFile
      REPORTABLE_CHANGES["app/models/concerns/reportable_changes.rb":::supportFile]:::supportFile
      PARTITIONED_TABLE["app/models/concerns/partitioned_table.rb":::supportFile]:::supportFile
      DEPRECATED_ASSIGNEE["app/models/concerns/deprecated_assignee.rb":::supportFile]:::supportFile
      REFERABLE["app/models/concerns/referable.rb":::supportFile]:::supportFile
      WHERE_COMPOSITE["app/models/concerns/where_composite.rb":::supportFile]:::supportFile
      NOTIFICATION_BRANCH_SEL["app/models/concerns/notification_branch_selection.rb":::supportFile]:::supportFile
      UPDATE_PROJECT_STATS["app/models/concerns/update_project_statistics.rb":::supportFile]:::supportFile
      PROJ_API_COMPAT["app/models/concerns/project_api_compatibility.rb":::supportFile]:::supportFile
      CASCADING_PRJ_ATTR["app/models/concerns/cascading_project_setting_attribute.rb":::supportFile]:::supportFile
      PACKAGES_DESTRUCTIBLE["app/models/concerns/packages/destructible.rb":::supportFile]:::supportFile
      PAGINATED_COLLECTION["app/controllers/concerns/paginated_collection.rb":::supportFile]:::supportFile
      REPO_SETTINGS_REDIRECT["app/controllers/concerns/repository_settings_redirect.rb":::supportFile]:::supportFile
      VALID_OR_DEFAULT["app/finders/concerns/valid_or_default.rb":::supportFile]:::supportFile
    end

    subgraph DataStructures["Data Structure Definitions" ]
      direction TB
      style DataStructures fill:#F8F8F8,stroke:#E0F8E0,stroke-width:1,rounded

      PRJ_SORT_ENUM_DS["app/graphql/types/projects/project_sort_enum.rb":::dataStruct]:::dataStruct
    end

  end

  %% =============== RELATIONSHIPS & INTERACTIONS ===============

  %% --- Models & Settings compositions ---
  PS -- "extends via Concern" --> ECS
  PS -- "compliance framework settings" --> CFS
  PS -- "status page setting" --> SPJ
  PS -- "deletion schedules" --> PDP
  PS -- "links with custom attribs" --> PCA
  PS -- "links with branch squash option" --> BF_SQ
  PS -- "links with pages domain" --> PD
  PS -- "has feature flags client" --> OFC
  ECS -- "audited by" --> PRJ_SETTING_AUDITOR

  PCF -- "sets topics for" --> PS

  CFS -- "belongs_to" --> PS

  %% --- Finders and Data Flow ---
  PROJ_FD -- "finds projects for" --> PS
  PROJ_FD -- "uses" --> AC_PROJ_FD
  ADMIN_PROJ_FD -- "admin context finder" --> PS
  NS_PROJ_FD -- "namespace-scoped finder" --> PS
  PROJ_FD -- "applies sort enum" --> PRJ_SORT_ENUM
  VSCS_FINDER -- "fetches project settings" --> PS

  %% --- Controllers orchestrate interactions ---
  ORG_PROJ_CTRL -- "uses" --> PROJ_FD
  ORG_PROJ_CTRL -- "controls" --> PS

  PRJ_APP_CTRL -- "superclass for" --> PRJ_PAGES_CTRL
  PRJ_APP_CTRL -- "superclass for" --> PRJ_SD_CTRL
  PRJ_APP_CTRL -- "superclass for" --> PRJ_VARS_CTRL
  PRJ_APP_CTRL -- "superclass for" --> PRJ_USAGE_CTRL
  PRJ_APP_CTRL -- "superclass for" --> PRJ_SETTINGS_MR
  PRJ_APP_CTRL -- "superclass for" --> PRJ_SETTINGS_REPO
  PRJ_APP_CTRL -- "superclass for" --> PRJ_SETTINGS_OP
  PRJ_APP_CTRL -- "superclass for" --> PRJ_SETTINGS_CICD
  PRJ_APP_CTRL -- "superclass for" --> PRJ_SETTINGS_INTLOG
  PRJ_APP_CTRL -- "superclass for" --> PRJ_PIPELINE_SETTINGS

  PRJ_PAGES_CTRL -- "manages" --> PD
  PRJ_VARS_CTRL -- "manages variable settings" --> PS
  PRJ_SD_CTRL -- "manages service desk" --> PS
  PRJ_USAGE_CTRL -- "displays quotas" --> PS
  PRJ_SETTINGS_MR -- "manages merge request settings" --> PS
  PRJ_SETTINGS_REPO -- "manages repository settings" --> PS
  PRJ_SETTINGS_OP -- "manages operations integrations" --> PS
  PRJ_SETTINGS_CICD -- "manages pipeline and CI/CD" --> PS
  PRJ_SETTINGS_INTLOG -- "manages integration hook logs" --> PS

  %% EE Controllers extend/override standard controllers
  EE_PCTRL_MR -- "extends" --> PRJ_SETTINGS_MR
  EE_PCTRL_CICD -- "extends" --> PRJ_SETTINGS_CICD
  EE_PCTRL_LOGS -- "adds observability logging" --> PS
  EE_PCTRL_PUSH_RULES -- "manages push rules integration" --> PS
  EE_PCTRL_SD -- "enhances service desk features" --> PS
  EE_PCTRL_SECRETS -- "manages project secrets" --> PS

  %% --- Services orchestrate flows ---
  PROJ_UPDATE_SVC -- "updates settings" --> PS
  PROJ_UPDATE_SVC -- "interacts with" --> PRJ_ATTR_CHANGED_EVT
  PROJ_UPDATE_SVC -- "calls" --> PROJ_TRANSFER_SVC
  PROJ_UPDATE_SVC -- "calls" --> PROJ_MARK_DEL_SVC
  PROJ_UPDATE_SVC -- "uses concerns" --> CASCADING_PRJ_ATTR
  PROJ_UPDATE_SVC -- "uses" --> PROJ_IMPORT_ERR_FILT

  PROJ_MARK_DEL_SVC -- "calls" --> PROJ_UPDATE_SVC
  PROJ_MARK_DEL_SVC -- "schedules deletion" --> PDP

  PROJ_ADJ_DEL_SVC -- "removes or restores" --> PDP

  PROJ_DESTROY_SVC -- "destroys" --> PS
  PROJ_DESTROY_SVC -- "invokes background jobs" --> INACTIVE_DEL_NOTIFY

  RESET_CACHE_SVC -- "interacts with" --> PRJ_CACHE_WORKER

  PAGES_UPDATE_SVC -- "updates" --> PD

  PROJ_OP_UPDATE_SVC -- "updates operations config" --> PS

  PROJ_IMPORT_ERR_FILT -- "filters errors on" --> PS

  ENV_UPDATE_SVC -- "updates environments for" --> PS

  PROJ_DISABLE_LEGACY_SVC -- "sets legacy inactive projects" --> PS

  %% EE Services extend/enhance above
  EE_PROJ_UPDATE_SVC -- "extends" --> PROJ_UPDATE_SVC
  EE_PROJ_OPS_UPDATE_SVC -- "extends" --> PROJ_OP_UPDATE_SVC
  EE_PROJ_UNLINK_FORK -- "extends fork logic" --> PROJ_UPDATE_SVC
  EE_PROJ_MARK_DEL_SVC -- "extends" --> PROJ_MARK_DEL_SVC
  EE_PROJ_DESTROY_SVC -- "extends" --> PROJ_DESTROY_SVC
  EE_PROJ_RESTORE_SVC -- "restores deleted projects" --> PDP
  EE_PROJ_EN_DEPLOY_KEY_SVC -- "handles EE deploy key" --> PS
  EE_PROJ_DIS_DEPLOY_KEY_SVC -- "handles EE deploy key" --> PS

  %% --- Policies orchestrate permissions ---
  APP_SETTING_POLICY -- "applies to" --> PS
  APP_SETTING_TERM_POLICY -- "applies to" --> PS
  PRJ_CICD_POLICY -- "applies to" --> NLCCS
  PRJ_STATS_POLICY -- "applies to" --> PS

  %% --- Serializers transform objects for API/UI ---
  PRJ_ENTITY -- "serializes" --> PS
  EE_GRP_ENTITY -- "serializes group assignment" --> PCA
  EE_ENV_SERIAL -- "serializes env with compliance" --> CFS
  EE_CLUSTER_ENV_SERIAL -- "serializes cluster env" --> OFC
  EE_SEC_PRJ_SERIAL -- "serializes project for sec" --> PS

  %% --- GraphQL types/mutations for settings ---
  PRJ_SORT_ENUM -- "enumerates sort in" --> PROJ_FD
  EE_MUT_CICD_NS_UPDATE -- "mutates NamespaceCiCdSetting" --> NLCCS
  EE_MUT_CICD_PRJ_UPDATE -- "mutates CI/CD settings" --> NLCCS
  EE_MUT_PRJ_SETTINGS_UPDATE -- "mutates project settings" --> PS

  %% --- Helpers ---
  PRJ_HELPER -- "renders project settings views" --> PS
  EE_PRJ_HELPER -- "extends helper logic" --> PRJ_HELPER
  EE_SEC_CONFIG_HELPER -- "extends security config" --> PS
  FORM_HELPER -- "shared form logic" --> ProjectControllers
  ADM_DEPLOY_KEY_HELPER -- "helper for deploy keys" --> PRJ_SETTINGS_REPO

  %% --- Workers ---
  INACTIVE_DEL_NOTIFY -- "notifies on inactive projects" --> PROJ_DISABLE_LEGACY_SVC
  POST_CREATION_WORKER -- "triggers after creation" --> PROJ_UPDATE_SVC
  INACTIVE_DEL_CRON -- "scans on cron" --> PROJ_DISABLE_LEGACY_SVC
  PRJ_CACHE_WORKER -- "maintains project stats cache" --> PS

  %% --- Validations ---
  PRJ_PATH_VALIDATOR -- "validates project paths" --> PS
  VALIDATIONS_INITIALIZER -- "initializes validations" --> PS

  %% --- Events/Auditing ---
  PRJ_ATTR_CHANGED_EVT -- "emits change events for" --> PS
  PRJ_SETTING_AUDITOR -- "audits project setting changes" --> PS

  %% --- Application Settings Services support app settings that link to PS ---
  APP_SETTINGS_BASE_SVC -- "base for update service" --> APP_SETTINGS_UPD_SVC
  APP_SETTINGS_UPD_SVC -- "updates application settings" --> PS

  %% --- Domain Concerns Usage ---
  PS -- "uses throttling/touch concern" --> THROTTLED_TOUCH
  PS -- "uses reportable changes concern" --> REPORTABLE_CHANGES
  PS -- "may be partitioned table concern" --> PARTITIONED_TABLE
  PS -- "compat for legacy API concern" --> PROJ_API_COMPAT
  PS -- "use cascaded attr logic" --> CASCADING_PRJ_ATTR
  PS -- "notification branch selection" --> NOTIFICATION_BRANCH_SEL
  PS -- "where composite helper concern" --> WHERE_COMPOSITE
  PS -- "referencing concern" --> REFERABLE
  PS -- "update stats concern" --> UPDATE_PROJECT_STATS

  %% --- Data Transformation ---
  PRJ_SORT_ENUM_DS -- "used in" --> PROJ_FD

  %% ----------- STYLING -----------
  classDef coreDomain fill:#D4F1F9,stroke:#8ecae6,stroke-width:2,rounded
  classDef supportFile fill:#FFF8DC,stroke:#f9d976,stroke-width:1,rounded
  classDef dataStruct fill:#E0F8E0,stroke:#70ba77,stroke-width:1,rounded
  classDef errorHandling fill:#FFE4E1,stroke:#feb2b2,stroke-width:1,rounded
  classDef initialization fill:#E6E6FA,stroke:#bdb2ff,stroke-width:1,rounded
```