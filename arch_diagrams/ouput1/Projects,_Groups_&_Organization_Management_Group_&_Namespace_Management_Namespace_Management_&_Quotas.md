```mermaid
flowchart TD
  %% Layout: vertical, nodes top-down
  %% Colors
  %% Core domain files: pastel blue (#D4F1F9)
  %% Supporting/utility files: pastel yellow (#FFF8DC)
  %% Data structure files: pastel green (#E0F8E0)
  %% Error handling files: pastel red (#FFE4E1)
  %% Initialization/setup files: pastel purple (#E6E6FA)
  %% Groupings: light gray (#F8F8F8) border pastel
  
  %% Namespace Model Core
  subgraph core_namespace["Core Namespace Entities"]
    direction TB
    style core_namespace fill:#F8F8F8,stroke:#719ECE,stroke-width:2,rx:16,ry:16

    namespace[/"Namespace.rb"/]:::coreblue
    group[/"Group.rb"/]:::coreblue
    projectns[/"ProjectNamespace.rb"/]:::coreblue
    desc[/"Descendants.rb"/]:::coreblue
    nsdetail[/"Namespace::Detail.rb"/]:::coreblue
    nsstats[/"NamespaceStatistics.rb"/]:::datagreen
    nsagg[/"Namespace::AggregationSchedule.rb"/]:::datagreen
    nsrootstat[/"Namespace::RootStorageStatistics.rb"/]:::datagreen
    nslimit[/"NamespaceLimit.rb"/]:::datagreen
    sync_ev[/"Namespaces::SyncEvent.rb"/]:::coreblue
  end

  %% Namespace Traversal / Hierarchy
  subgraph traversal["Namespace Hierarchy & Traversal"]
    direction TB
    style traversal fill:#F8F8F8,stroke:#5EC2E6,stroke-width:2,rx:16,ry:16

    linear[/"Traversal/Linear.rb"/]:::coreblue
    recursive[/"Traversal/Recursive.rb"/]:::coreblue
    cached[/"Traversal/Cached.rb"/]:::coreblue
    linearsc[/"Traversal/LinearScopes.rb"/]:::supportyellow
    trie[/"TraversalTrieNode.rb"/]:::datagreen
    trhier[/"TraversalHierarchy.rb"/]:::coreblue
  end

  %% Quota & Storage
  subgraph quotas_storage["Namespace Quotas & Storage"]
    direction TB
    style quotas_storage fill:#F8F8F8,stroke:#FBDAA7,stroke-width:2,rx:16,ry:16
    nsrootstat_ee[/"EE/Namespace/RootStorageStatistics.rb"/]:::datagreen
    nsstats_ee[/"EE/NamespaceStatistics.rb"/]:::datagreen
    ns_st_root[/"EE/Namespaces/Storage/RootSize.rb"/]:::datagreen
    ns_st_cost_factor[/"EE/Namespaces/Storage/CostFactor.rb"/]:::supportyellow
    ns_st_limit_enf[/"EE/Namespaces/Storage/NamespaceLimit/Enforcement.rb"/]:::coreblue
    ns_st_repo_limit_enf[/"EE/Namespaces/Storage/RepositoryLimit/Enforcement.rb"/]:::coreblue
    ns_st_limit_exc[/"EE/Namespaces/Storage/LimitExclusion.rb"/]:::coreblue
    ns_st_limit_mailer[/"EE/Namespaces/Storage/NamespaceLimitMailer.rb"/]:::supportyellow
    ns_st_repo_limit_mailer[/"EE/Namespaces/Storage/RepositoryLimitMailer.rb"/]:::supportyellow
    ns_st_ns_limit_email_service[/"EE/Namespaces/Storage/NamespaceLimit/EmailNotificationService.rb"/]:::supportyellow
    ns_st_usg_export[/"EE/Namespaces/Storage/UsageExportService.rb"/]:::supportyellow
    ns_st_usg_export_worker[/"EE/Namespaces/StorageUsageExportWorker.rb"/]:::initpurple
    ns_st_repo_limit_email_service[/"EE/Namespaces/Storage/RepositoryLimit/EmailNotificationService.rb"/]:::supportyellow
  end

  %% Free User Cap & Enforcement
  subgraph free_user_cap["Free User Cap & Enforcement"]
    direction TB
    style free_user_cap fill:#F8F8F8,stroke:#B2E2B1,stroke-width:2,rx:16,ry:16

    free_cap_enf[/"EE/Namespaces/FreeUserCap/Enforcement.rb"/]:::coreblue
    free_cap_root[/"EE/Namespaces/FreeUserCap/RootSize.rb"/]:::datagreen
    free_cap_notify[/"EE/Namespaces/FreeUserCap/NotifyOverLimitService.rb"/]:::supportyellow
    free_cap_usage_alert[/"EE/Components/Namespaces/FreeUserCap/UsageQuotaAlertComponent.rb"/]:::supportyellow
    free_cap_helper[/"EE/Helpers/Namespaces/FreeUserCapHelper.rb"/]:::supportyellow
    free_cap_worker[/"EE/Workers/Namespaces/FreeUserCap/GroupOverLimitNotificationWorker.rb"/]:::initpurple
    ns_combined_preenf[/"EE/Namespaces/CombinedStorageUsers/PreEnforcement.rb"/]:::coreblue
  end

  %% Services
  subgraph services["Core Services"]
    direction TB
    style services fill:#F8F8F8,stroke:#BEB8E6,stroke-width:2,rx:16,ry:16

    update_desc[/"UpdateDenormalizedDescendantsService.rb"/]:::supportyellow
    stats_refresher[/"StatisticsRefresherService.rb"/]:::supportyellow
    ns_pkg_update[/"Namespaces/PackageSettings/UpdateService.rb"/]:::supportyellow
    ns_pkg_update_ee[/"EE/Namespaces/PackageSettings/UpdateService.rb"/]:::supportyellow
    ns_export_runner[/"EE/Namespaces/Export/ExportRunner.rb"/]:::supportyellow
    ns_export_base[/"EE/Namespaces/Export/BaseService.rb"/]:::supportyellow
    ns_export_detailed[/"EE/Namespaces/Export/DetailedDataService.rb"/]:::supportyellow
    ns_export_member[/"EE/Namespaces/Export/Member.rb"/]:::datagreen
    ns_export_limited[/"EE/Namespaces/Export/LimitedDataService.rb"/]:::supportyellow
  end

  %% Policies
  subgraph policies["Authorization & Policies"]
    direction TB
    style policies fill:#F8F8F8,stroke:#D7B1D6,stroke-width:2,rx:16,ry:16

    group_policy[/"GroupPolicy.rb"/]:::supportyellow
    ns_rs_policy[/"Namespace/RootStorageStatisticsPolicy.rb"/]:::supportyellow
    ns_ci_cd_policy[/"NamespaceCiCdSettingPolicy.rb"/]:::supportyellow
    userns_policy[/"EE/Namespaces/UserNamespacePolicy.rb"/]:::supportyellow
  end

  %% Controllers/Init
  subgraph interfaces["Controllers & Initialization"]
    direction TB
    style interfaces fill:#F8F8F8,stroke:#B6ADEB,stroke-width:2,rx:16,ry:16

    group_usage_quotas_ctrl[/"Groups/UsageQuotasController.rb"/]:::initpurple
    prof_usage_quota_ctrl[/"Profiles/UsageQuotasController.rb"/]:::initpurple
    admin_ns_limits_ctrl[/"EE/Controllers/Admin/NamespaceLimitsController.rb"/]:::initpurple
    user_ns_visits_ctrl[/"Users/NamespaceVisitsController.rb"/]:::initpurple
    enable_desc_cache_worker[/"Workers/Namespaces/EnableDescendantsCacheCronWorker.rb"/]:::initpurple
    prune_agg_sched_worker[/"Workers/Namespaces/PruneAggregationSchedulesWorker.rb"/]:::initpurple
    ns_import_user[/"Import/NamespaceImportUser.rb"/]:::datagreen
  end

  %% Helpers
  subgraph helpers["Helpers & Utilities"]
    direction TB
    style helpers fill:#F8F8F8,stroke:#F7ECAB,stroke-width:2,rx:16,ry:16

    ns_helpers[/"NamespacesHelper.rb"/]:::supportyellow
    ee_ns_helpers[/"EE/Helpers/NamespacesHelper.rb"/]:::supportyellow
    nsadjdel[/"Namespaces/AdjournedDeletable.rb"/]:::supportyellow
    cascade_ns_attr[/"CascadingNamespaceSettingAttribute.rb"/]:::supportyellow
    update_ns_stats[/"UpdateNamespaceStatistics.rb"/]:::supportyellow
    del_ns[/"DeletableNamespace.rb"/]:::supportyellow
    case_sens[/"CaseSensitivity.rb"/]:::supportyellow
  end

  %% EE Enhancements
  subgraph ee_enhancements["EE Enhancements & Integrations"]
    direction TB
    style ee_enhancements fill:#F8F8F8,stroke:#D4B1D1,stroke-width:2,rx:16,ry:16

    ee_namespace[/"EE/Namespace.rb"/]:::coreblue
    ee_ns_package[/"EE/Namespace/PackageSetting.rb"/]:::supportyellow
    ns_st_comb_alert[/"EE/Components/Namespaces/CombinedStorageUsers/OwnerAlertComponent.rb"/]:::supportyellow
    ns_comb_alert_base[/"EE/Components/Namespaces/CombinedStorageUsers/BaseAlertComponent.rb"/]:::supportyellow
  end

  %% Relationships

  %% Core Model Relationships
  namespace-->|"STI base"<br>"Provides root behaviors"|group
  namespace-->|"STI base"<br>"Provides root behaviors"|projectns
  namespace-->|"Child/parent linkage"|desc
  group-->|"Inheritance / Features"|projectns
  group-->|"Quota statistics"|nsrootstat
  group-->|"Storage quotas"|nsagg
  group-->|"Namespace statistics"|nsstats
  projectns-->|"Quota statistics"|nsrootstat
  projectns-->|"Namespace statistics"|nsstats
  namespace-->|"Associated details"|nsdetail
  namespace-->|"Aggregation linked"|nsagg
  namespace-->|"Tracks denormalized descendants"|desc
  desc-->|"Expires/refreshes via"|update_desc
  nsagg-->|"Schedules stats aggregation of"|nsstats
  nslimit-->|"Limit enforced on"|nsrootstat
  nslimit-->|"Policy check used in"|ns_st_limit_enf
  
  %% Traversal Relationships
  namespace-->|"Traversal includes"<br>"linear/recursive/cached"|linear
  namespace-->|"Traversal includes"|recursive
  namespace-->|"Traversal includes"|cached
  linear-->|"Scopes/from traits"|linearsc
  linear-->|"Hierarchy management"|trhier
  recursive-->|"Hierarchy management"|trhier
  cached-->|"Hierarchy management"|trhier
  trhier-->|"Works with trie node"|trie
  desc-->|"Refreshed using traversal"<br>"scope information"|cached
  
  %% Quota & Storage Relationships
  nsrootstat-->|"Reference EE enhancements"|nsrootstat_ee
  nsstats-->|"Reference EE enhancements"|nsstats_ee
  nsrootstat_ee-->|"Enforces limits through"|ns_st_limit_enf
  nsrootstat_ee-->|"Supports forks policy"|ns_st_cost_factor
  nsrootstat-->|"Updated by"|stats_refresher
  nsrootstat-->|"Limit management via"|nslimit
  nsrootstat-->|"Support for alerts/mailers"|ns_st_limit_mailer
  ns_st_limit_mailer-->|"Sends emails for limits"<br>"to owners"|ns_st_limit_enf
  ns_st_limit_enf-->|"Checks settings"<br>"and statistics"|nsrootstat
  ns_st_repo_limit_enf-->|"Checks repository limits"|nsrootstat
  ns_st_limit_exc-->|"Exclusions applied to"|nsrootstat
  ns_st_usg_export-->|"Exports via"|ns_st_usg_export_worker
  ns_st_repo_limit_mailer-->|"Alerts for repository quota"|ns_st_repo_limit_enf
  ns_st_ns_limit_email_service-->|"Alerts for namespace quota"|ns_st_limit_enf
  ns_st_repo_limit_email_service-->|"Alerts for repo quota"|ns_st_repo_limit_enf

  %% Free User Cap Relationships
  nsrootstat_ee-->|"Free User Cap check"|free_cap_enf
  nsrootstat_ee-->|"User cap alert hygiene"|free_cap_notify
  free_cap_enf-->|"Send alerts using"|free_cap_notify
  free_cap_notify-->|"Worker notification"|free_cap_worker
  group-->|"User cap limit logic"|free_cap_enf
  free_cap_enf-->|"UI banners/components"|free_cap_usage_alert
  free_cap_enf-->|"Helpers display cap status"|free_cap_helper

  %% Traversal Update/Descendants Service
  update_desc-->|"Relies on traversal scopes"|linear
  update_desc-->|"Refreshes descendants table"|desc
  update_desc-->|"Updates traversal ids mapping"|trhier

  %% Export/Import/Advanced Data
  ns_export_runner-->|"Initiates export flow"|ns_export_base
  ns_export_base-->|"Detailed/limited export"|ns_export_detailed
  ns_export_base-->|"Detailed/limited export"|ns_export_limited
  ns_export_detailed-->|"Transforms member data"|ns_export_member
  ns_export_limited-->|"Finds members with access"|ns_export_member
  ns_export_base-->|"Uses statistics data"|nsstats

  %% Package Settings Services
  ns_pkg_update_ee-->|"Enhances OSS pkg update"|ns_pkg_update
  ns_pkg_update-->|"Updates namespace package settings"|namespace

  %% Controllers/Init relationships
  group_usage_quotas_ctrl-->|"Reads quota, statistics"|nsstats
  group_usage_quotas_ctrl-->|"Reads storage, root stats"|nsrootstat
  group_usage_quotas_ctrl-->|"Reads free user cap"|free_cap_enf
  group_usage_quotas_ctrl-->|"Helpers/alert components"|free_cap_usage_alert
  prof_usage_quota_ctrl-->|"Reads stats/root stats"|nsrootstat
  admin_ns_limits_ctrl-->|"Administers root stats/agg"|nsagg
  user_ns_visits_ctrl-->|"Triggers visit tracking"|enable_desc_cache_worker
  enable_desc_cache_worker-->|"Updates cache on descendants"|desc
  prune_agg_sched_worker-->|"Scheduled cleanup/aggregation"|nsagg

  %% Policies relationships
  group_policy-->|"Group-managed quotas"|group
  ns_rs_policy-->|"Authorize access to"/><br>nsrootstat
  userns_policy-->|"User namespace rules"/><br>namespace

  %% Helpers / Utilities links
  ns_helpers-->|"Format/parse app data"|group
  ns_helpers-->|"Format/parse app data"|namespace
  ns_helpers-->|"Format/parse app data"|projectns
  ee_ns_helpers-->|"Extends core helpers"|ns_helpers
  cascade_ns_attr-->|"Cascading attr for settings"|namespace
  update_ns_stats-->|"Used for refreshing stats"|nsstats
  del_ns-->|"Deletion logic"|group
  nsadjdel-->|"Adjourned deletion logic"|group

  %% EE Enhancements
  ee_namespace-->|"Extends/overrides core behaviors"|namespace
  ee_ns_package-->|"Extends package settings"|namespace
  ns_st_comb_alert-->|"Combines owner/user alerts"|ns_comb_alert_base
  ns_comb_alert_base-->|"Mixed quota/user alerts"|free_cap_enf
  ns_combined_preenf-->|"Shared pre-enforcement logic"|ns_st_comb_alert
  ns_combined_preenf-->|"Shared pre-enforcement logic"|free_cap_enf

  %% Other key relationships, data structures, flows
  namespace-->|"Data flows to statistics/quotas"|nsstats
  group-->|"Linked directly to quotas/aggregation"|nsagg
  projectns-->|"Inherits stats/quota from namespace"|nsstats
  recursive-->|"Implements custom ancestor queries"|trhier
  trie-->|"Used to optimize hierarchy traversal"|trhier

  %% Legend: Node styles
  classDef coreblue fill:#D4F1F9,stroke:#6CC3D5,stroke-width:2,rx:12,ry:12
  classDef supportyellow fill:#FFF8DC,stroke:#F6D96B,stroke-width:2,rx:12,ry:12
  classDef datagreen fill:#E0F8E0,stroke:#A8E7A0,stroke-width:2,rx:12,ry:12
  classDef errorred fill:#FFE4E1,stroke:#F4BDBD,stroke-width:2,rx:12,ry:12
  classDef initpurple fill:#E6E6FA,stroke:#B8B9DD,stroke-width:2,rx:12,ry:12
```