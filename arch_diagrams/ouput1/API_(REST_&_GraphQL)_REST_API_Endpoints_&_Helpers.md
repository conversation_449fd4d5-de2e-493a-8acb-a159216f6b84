```mermaid
flowchart TD
    %% COLORS AND STYLES
    %% Core domain files: pastel blue (#D4F1F9)
    %% Supporting/utility files: pastel yellow (#FFF8DC)
    %% Data structure files: pastel green (#E0F8E0)
    %% Error handling files: pastel red (#FFE4E1)
    %% Initialization/setup files: pastel purple (#E6E6FA)
    %% Logical groupings: very light gray (#F8F8F8) with pastel border

    %% Top-level API Structure
    subgraph api_core[REST API Endpoints]
      direction TB
      style api_core fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded=true

      api_base["lib/api/base.rb<br/>API Base Class":::core] 
      api_api["lib/api/api.rb<br/>API Mounting & Base":::core]
      endpoints_subgraph[Endpoints]
      helpers_subgraph([Helpers])
      concerns_subgraph([Concerns])
      entities_subgraph([Entities])
      api_core_files([Core Files])
      style api_core_files fill:#D4F1F9,stroke:#79B7DD,stroke-width:2,stroke-dasharray:5,rounded=true;
    end

    classDef core fill:#D4F1F9,stroke:#79B7DD,stroke-width:2,rounded=true;
    classDef supporting fill:#FFF8DC,stroke:#E6CC72,stroke-width:2,rounded=true;
    classDef datastruct fill:#E0F8E0,stroke:#56C569,stroke-width:2,rounded=true;
    classDef error fill:#FFE4E1,stroke:#F3AFAF,stroke-width:2,rounded=true;
    classDef init fill:#E6E6FA,stroke:#A393E6,stroke-width:2,rounded=true;
    classDef logical fill:#F8F8F8,stroke:#B9B9B9,stroke-width:2,rounded=true;

    %% -- ENDPOINT SUBGRAPH --
    subgraph endpoints_subgraph[REST API Endpoint Classes]
      direction TB
      style endpoints_subgraph fill:#F8F8F8,stroke:#B9DDFA,stroke-width:2,rounded=true

      api_projects["lib/api/projects.rb<br/>Projects Endpoint":::core]
      api_users["lib/api/users.rb<br/>Users Endpoint":::core]
      api_merge_requests["lib/api/merge_requests.rb<br/>MergeRequests Endpoint":::core]
      api_commits["lib/api/commits.rb<br/>Commits Endpoint":::core]
      api_ci_pipelines["lib/api/ci/pipelines.rb<br/>CI Pipelines":::core]
      api_ci_jobs["lib/api/ci/jobs.rb<br/>CI Jobs":::core]
      api_ci_triggers["lib/api/ci/triggers.rb<br/>CI Triggers":::core]
      api_cont_repos["lib/api/container_repositories.rb<br/>Container Repositories":::core]
      api_proj_cont_repos["lib/api/project_container_repositories.rb<br/>Project Container Repositories":::core]
      api_group_cont_repos["lib/api/group_container_repositories.rb<br/>Group Container Repositories":::core]
      api_proj_packs["lib/api/project_packages.rb<br/>Project Packages":::core]
      api_group_packs["lib/api/group_packages.rb<br/>Group Packages":::core]
      api_packages["lib/api/package_files.rb<br/>Package Files":::core]
      api_helm_packs["lib/api/helm_packages.rb<br/>Helm Packages":::core]
      api_pypi["lib/api/pypi_packages.rb<br/>PyPi Packages":::core]
      api_nuget_gp["lib/api/nuget_group_packages.rb<br/>Nuget Group Packages":::core]
      api_proj_snippets["lib/api/project_snippets.rb<br/>Project Snippets":::core]
      api_snippets["lib/api/snippets.rb<br/>Snippets":::core]
      api_notes["lib/api/.rb<br/>Notes Endpoint":::core]
      api_events["lib/api/events.rb<br/>Events Endpoint":::core]
      api_wikis["lib/api/wikis.rb<br/>Wikis Endpoint":::core]
      api_todos["lib/api/todos.rb<br/>Todos":::core]
      api_hooks["lib/api/project_hooks.rb<br/>Project Hooks":::core]
      api_sys_hooks["lib/api/system_hooks.rb<br/>System Hooks":::core]
      api_group_labels["lib/api/group_labels.rb<br/>Group Labels":::core]
      api_group_milestones["lib/api/group_milestones.rb<br/>Group Milestones":::core]
      api_group_vars["lib/api/group_variables.rb<br/>Group Variables":::core]
      api_resource_at["lib/api/resource_access_tokens.rb<br/>Resource Access Tokens":::core]
      api_appearance["lib/api/appearance.rb<br/>Appearance":::core]
      api_templates["lib/api/templates.rb<br/>Templates":::core]
      api_custom_attr["lib/api/custom_attributes_endpoints.rb<br/>Custom Attrs Endpoints":::core]
    end

    %% -- HELPERS SUBGRAPH --
    subgraph helpers_subgraph[REST API Helpers]
      direction TB
      style helpers_subgraph fill:#F8F8F8,stroke:#FAE0B9,stroke-width:2,rounded=true

      helpers_packages["lib/api/helpers/packages_helpers.rb<br/>Packages Helpers":::supporting]
      helpers_packages_npm["lib/api/helpers/packages/npm.rb<br/>NPM Helpers":::supporting]
      helpers_packages_nuget["lib/api/helpers/packages/nuget.rb<br/>Nuget Helpers":::supporting]
      helpers_conreg["lib/api/helpers/container_registry_helpers.rb<br/>Container Registry Helpers":::supporting]
      helpers_auth["lib/api/helpers/authentication.rb<br/>Authentication Helpers":::supporting]
      helpers_openapi["lib/api/helpers/open_api.rb<br/>Open API Helpers":::supporting]
      helpers_kas["lib/api/helpers/kas_helpers.rb<br/>KAS Helpers":::supporting]
      helpers_importgithub["lib/api/helpers/import_github_helpers.rb<br/>ImportGithub Helpers":::supporting]
      helpers_notes["lib/api/helpers/notes_helpers.rb<br/>Notes Helpers":::supporting]
      helpers_issues["lib/api/helpers/issues_helpers.rb<br/>Issues Helpers":::supporting]
      helpers_labels["lib/api/helpers/label_helpers.rb<br/>Label Helpers":::supporting]
      helpers_users["lib/api/helpers/users_helpers.rb<br/>Users Helpers":::supporting]
      helpers_pagination["lib/api/helpers/pagination_strategies.rb<br/>Pagination Strategies":::supporting]
      helpers_settings["lib/api/helpers/settings_helpers.rb<br/>Settings Helpers":::supporting]
      helpers_award_emoji["lib/api/helpers/award_emoji.rb<br/>Award Emoji Helpers":::supporting]
      helpers_project_snap["lib/api/helpers/project_snapshots_helpers.rb<br/>Project Snapshots":::supporting]
      helpers_variables["lib/api/helpers/variables_helpers.rb<br/>Variables Helpers":::supporting]
      helpers_caching["lib/api/helpers/caching.rb<br/>Caching Helpers":::supporting]
      helpers_search["lib/api/helpers/search_helpers.rb<br/>Search Helpers":::supporting]
      helpers_internal["lib/api/helpers/internal_helpers.rb<br/>Internal Helpers":::supporting]
      helpers_events["lib/api/helpers/resource_events_helpers.rb<br/>Res. Events Helpers":::supporting]
      helpers_integrations["lib/api/helpers/integrations_helpers.rb<br/>Integrations Helpers":::supporting]
    end

    %% -- CONCERNS SUBGRAPH --
    subgraph concerns_subgraph[API Concerns]
      direction TB
      style concerns_subgraph fill:#F8F8F8,stroke:#B9E7DD,stroke-width:2,rounded=true

      concerns_debian["lib/api/concerns/packages/debian_distribution_endpoints.rb<br/>Debian Package Dist. Endpoints":::supporting]
      concerns_milestones["lib/api/concerns/milestones/group_project_params.rb<br/>Milestones Group Project Params":::supporting]
    end

    %% -- ENTITIES SUBGRAPH --
    subgraph entities_subgraph[API Entities/Data Structures]
      direction TB
      style entities_subgraph fill:#F8F8F8,stroke:#B9FAEF,stroke-width:2,rounded=true

      entities_entity_helpers["lib/api/entities/entity_helpers.rb<br/>Entity Helpers":::datastruct]
    end

    %% -- RATE LIMIT & UTILITY SUBGRAPH --
    subgraph utility_core[Support & Rate Limiting]
      direction TB
      style utility_core fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,rounded=true

      check_rate_limit["app/controllers/concerns/check_rate_limit.rb<br/>General Rate Limiting":::supporting]
      search_rate_limitable["app/controllers/concerns/search_rate_limitable.rb<br/>Search Rate Limiting":::supporting]
      gitlab_request_endpoints["lib/gitlab/request_endpoints.rb<br/>API Endpoint Discovery":::supporting]
    end

    %% -- INITIALIZATION/SUPPORTING FILES SUBGRAPH --
    subgraph init_support[Initialization & Configuration]
      direction TB
      style init_support fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2,rounded=true

      fog_core_patch["config/initializers/fog_core_patch.rb<br/>Fog Core Patch":::init]
    end

    %% -- EMAIL NOTIFICATIONS SUBGRAPH --
    subgraph mailers_area[Email Notifications]
      direction TB
      style mailers_area fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded=true

      mailer_profile["app/mailers/emails/profile.rb<br/>Profile Email Notifications":::core]
      mailer_identity["app/mailers/emails/identity_verification.rb<br/>Identity Verification Emails":::core]
      mailer_pages_domains["app/mailers/emails/pages_domains.rb<br/>Pages Domains Email Notifications":::core]
      mailer_shared["app/mailers/emails/shared.rb<br/>Shared Email Helpers":::core]
    end

    %% ----- LOGICAL RELATIONSHIPS AND DATA TRANSFORMATIONS -----
    %% Core API Mounting & Routing
    api_api --> api_base
    api_base -->|Mounts & Configures| api_projects
    api_base -->|Mounts & Configures| api_users
    api_base -->|Mounts & Configures| api_merge_requests
    api_base -->|Mounts & Configures| api_commits
    api_base -->|Mounts & Configures| api_ci_pipelines
    api_base -->|Mounts & Configures| api_ci_jobs
    api_base -->|Mounts & Configures| api_ci_triggers
    api_base -->|Mounts & Configures| api_cont_repos
    api_base -->|Mounts & Configures| api_proj_cont_repos
    api_base -->|Mounts & Configures| api_group_cont_repos
    api_base -->|Mounts & Configures| api_proj_packs
    api_base -->|Mounts & Configures| api_group_packs
    api_base -->|Mounts & Configures| api_packages
    api_base -->|Mounts & Configures| api_helm_packs
    api_base -->|Mounts & Configures| api_pypi
    api_base -->|Mounts & Configures| api_nuget_gp
    api_base -->|Mounts & Configures| api_notes
    api_base -->|Mounts & Configures| api_events
    api_base -->|Mounts & Configures| api_wikis
    api_base -->|Mounts & Configures| api_todos
    api_base -->|Mounts & Configures| api_hooks
    api_base -->|Mounts & Configures| api_sys_hooks
    api_base -->|Mounts & Configures| api_group_labels
    api_base -->|Mounts & Configures| api_group_milestones
    api_base -->|Mounts & Configures| api_group_vars
    api_base -->|Mounts & Configures| api_resource_at
    api_base -->|Mounts & Configures| api_appearance
    api_base -->|Mounts & Configures| api_custom_attr
    api_base -->|Mounts & Configures| api_templates
    api_base -->|Mounts & Configures| api_proj_snippets
    api_base -->|Mounts & Configures| api_snippets

    %% Endpoint - Helpers relationships
    api_ci_pipelines --> helpers_packages
    api_ci_pipelines --> helpers_packages_npm
    api_ci_pipelines -->|Auth| helpers_auth
    api_ci_pipelines -->|Pagination| helpers_pagination
    api_ci_jobs --> helpers_packages
    api_ci_jobs -->|Pagination| helpers_pagination

    api_projects --> helpers_pagination
    api_projects --> helpers_users
    api_projects --> helpers_labels
    api_projects --> helpers_project_snap

    api_users --> helpers_users
    api_users --> helpers_pagination

    api_merge_requests --> helpers_notes
    api_merge_requests --> helpers_issues

    api_commits --> helpers_notes

    api_notes --> helpers_notes
    api_events --> helpers_events

    api_templates --> helpers_settings

    api_pypi --> helpers_packages

    api_proj_packs --> helpers_packages
    api_group_packs --> helpers_packages
    api_packages --> helpers_packages

    api_helm_packs --> helpers_packages

    api_pypi --> helpers_packages

    api_nuget_gp --> helpers_packages

    api_proj_snippets --> helpers_notes
    api_proj_snippets --> helpers_users

    %% Container Registry endpoints use Container Registry Helpers
    api_cont_repos --> helpers_conreg
    api_proj_cont_repos --> helpers_conreg
    api_group_cont_repos --> helpers_conreg

    %% Authentication helpers
    api_hooks --> helpers_auth
    api_sys_hooks --> helpers_auth

    %% Label support
    api_group_labels --> helpers_labels
    api_todos --> helpers_labels

    api_group_milestones --> helpers_events
    api_group_vars --> helpers_variables

    %% Custom Entity Exposures
    api_projects --> entities_entity_helpers
    api_users --> entities_entity_helpers
    api_merge_requests --> entities_entity_helpers

    %% Concerns and Shared Logic
    api_proj_packs --> concerns_debian
    api_proj_packs --> concerns_milestones

    api_group_milestones --> concerns_milestones

    %% Utility support and rate limiting
    api_projects --> check_rate_limit
    api_users --> check_rate_limit
    api_todos --> check_rate_limit
    api_todos --> search_rate_limitable
    api_merge_requests --> search_rate_limitable

    %% Request endpoint discovery for tests/support
    api_api --> gitlab_request_endpoints

    %% Initialization and Setup
    api_api --> fog_core_patch

    %% EMAIL NOTIFICATIONS LOGIC
    mailer_profile -.-> api_users
    mailer_profile -.-> api_projects
    mailer_identity -.-> api_users
    mailer_pages_domains -.-> api_projects
    mailer_shared -.->|Shared Data & Layouts| mailer_profile
    mailer_shared -.-> mailer_identity
    mailer_shared -.-> mailer_pages_domains

    %% Cross-Helpers Relationships
    helpers_packages_npm --> helpers_packages
    helpers_packages_nuget --> helpers_packages

    helpers_award_emoji --> helpers_issues
    helpers_award_emoji --> helpers_notes
    helpers_integration_helpers --> helpers_award_emoji

    helpers_openapi --> api_api

    %% Data Transformation: Helpers <-> Entities
    helpers_notes --> entities_entity_helpers
    helpers_projects_snap --> entities_entity_helpers

    %% Data Structure Reference
    api_core_files -.-> endpoints_subgraph
    api_core_files -.-> helpers_subgraph
    api_core_files -.-> concerns_subgraph
    api_core_files -.-> entities_subgraph

    %% Legend for ease
    class api_base,api_api,api_projects,api_users,api_merge_requests,api_commits,api_ci_pipelines,api_ci_jobs,api_ci_triggers,api_cont_repos,api_proj_cont_repos,api_group_cont_repos,api_proj_packs,api_group_packs,api_packages,api_helm_packs,api_pypi,api_nuget_gp,api_proj_snippets,api_snippets,api_notes,api_events,api_wikis,api_todos,api_hooks,api_sys_hooks,api_group_labels,api_group_milestones,api_group_vars,api_resource_at,api_appearance,api_templates,api_custom_attr core;
    class check_rate_limit,search_rate_limitable,gitlab_request_endpoints supporting;
    class fog_core_patch init;
    class helpers_packages,helpers_packages_npm,helpers_packages_nuget,helpers_conreg,helpers_auth,helpers_openapi,helpers_kas,helpers_importgithub,helpers_notes,helpers_issues,helpers_labels,helpers_users,helpers_pagination,helpers_settings,helpers_award_emoji,helpers_project_snap,helpers_variables,helpers_caching,helpers_search,helpers_internal,helpers_events,helpers_integrations supporting;
    class concerns_debian,concerns_milestones supporting;
    class entities_entity_helpers datastruct;
    class mailer_profile,mailer_identity,mailer_pages_domains,mailer_shared core;

    %% Spacing hack (invisible dummy node)
    dummy(("")):::logical

    %% Vertical layouts preference sequence for top-to-bottom
    style endpoints_subgraph fill:#F8F8F8,stroke:#B9DDFA,stroke-width:2,rounded=true
    style helpers_subgraph fill:#F8F8F8,stroke:#FAE0B9,stroke-width:2,rounded=true
    style concerns_subgraph fill:#F8F8F8,stroke:#B9E7DD,stroke-width:2,rounded=true
    style entities_subgraph fill:#F8F8F8,stroke:#B9FAEF,stroke-width:2,rounded=true
    style api_core fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded=true
    style mailers_area fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded=true
    style utility_core fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,rounded=true
    style init_support fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2,rounded=true

    %% Hide node labels for subgraph "headers"
    class endpoints_subgraph,helpers_subgraph,concerns_subgraph,entities_subgraph,api_core,mailers_area,utility_core,init_support logical
```