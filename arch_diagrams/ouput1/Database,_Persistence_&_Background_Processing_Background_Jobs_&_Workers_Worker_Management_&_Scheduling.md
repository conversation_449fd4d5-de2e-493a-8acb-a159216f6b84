```mermaid
flowchart TD
  %% Styles
  classDef core fill:#D4F1F9,stroke:#A4D6EB,stroke-width:2px,stroke-dasharray: 2 2,stroke-linecap:round,rx:8,ry:8
  classDef support fill:#FFF8DC,stroke:#E7D59B,stroke-width:2px,stroke-dasharray: 2 2,stroke-linecap:round,rx:8,ry:8
  classDef data fill:#E0F8E0,stroke:#A7D8A7,stroke-width:2px,stroke-dasharray: 2 2,stroke-linecap:round,rx:8,ry:8
  classDef error fill:#FFE4E1,stroke:#F1B1AD,stroke-width:2px,stroke-dasharray: 2 2,stroke-linecap:round,rx:8,ry:8
  classDef init fill:#E6E6FA,stroke:#B9AFC7,stroke-width:2px,stroke-dasharray: 2 2,stroke-linecap:round,rx:8,ry:8
  classDef group fill:#F8F8F8,stroke-width:2px,stroke:#C3C3C3,rx:10,ry:10

  %% CORE DB: Persistence, Async Background, Worker Scheduling
  subgraph DB["DATABASE, PERSISTENCE & BACKGROUND PROCESSING" ]
    class DB group
    direction TB
    subgraph CoreDomainFeatures["Core Database & Background Concepts"]
      class CoreDomainFeatures group
      A1[Database Workers]:::core
      A2[BackgroundMigrationWorker]:::core
      A3[BackgroundMigration::CiDatabaseWorker]:::core
      B1[lib/gitlab/background_migration.rb \nBackgroundMigration domain orchestrator]:::core
      B2[lib/gitlab/database/background_migration_job.rb \nMigration job record]:::data
      B3[lib/gitlab/database/background_migration/batched_migration_wrapper.rb \nBatch migration executor]:::support
      B4[lib/gitlab/database/background_migration/batch_metrics.rb \nBatch metrics]:::data
      B5[lib/gitlab/database/background_migration/batched_job_transition_log.rb \nBatch job transition log]:::data
      B6[lib/gitlab/database/background_migration/sub_batch_timeout_error.rb]:::error
      B7[lib/gitlab/database/load_balancing/session_map.rb \nSession sticking map]:::data
      B8[lib/gitlab/database/load_balancing/sticking.rb \nSession stick strategies]:::core
      B9[lib/gitlab/database/load_balancing/srv_resolver.rb]:::support
      B10[lib/gitlab/database/stat_activity_sampler.rb]:::core
      B11[lib/gitlab/database/transaction/context.rb]:::support
    end

    subgraph BackgroundJobWorkers["Background Job Workers & Management"]
      class BackgroundJobWorkers group
      C1[ApplicationWorker\nShared worker base]:::core
      C2[JobCoordinator\nBackgroundMigration::JobCoordinator]:::core
      C3[lib/gitlab/sidekiq_versioning/worker.rb \nworker versioning]:::support
      C4[lib/gitlab/sidekiq_versioning/middleware.rb]:::support
      C5[lib/gitlab/sidekiq_config/worker.rb \nSidekiq config adapter]:::support
      C6[lib/gitlab/sidekiq_config/worker_router.rb \nQueue routing]:::support
      C7[lib/gitlab/sidekiq_config/worker_matcher.rb]:::support
      C8[lib/gitlab/sidekiq_config/cron_job_initializer.rb \nCron init]:::init
      C9[lib/gitlab/sidekiq_config/cli_methods.rb]:::support
      C10[lib/gitlab/sidekiq_config/dummy_worker.rb \nDummy worker metadata]:::support
      C11[lib/gitlab/sidekiq_status_spec.rb \njob status test]:::support
      C12[lib/gitlab/sidekiq_sharding/validator.rb]:::support
      C13[lib/gitlab/sidekiq_sharding/router.rb]:::support
      C14[lib/gitlab/sidekiq_sharding/scheduled_enq.rb]:::support
      C15[lib/gitlab/sidekiq_daemon/monitor.rb \nQueue daemon]:::core
      C16[lib/gitlab/daemon.rb \nLegacy process control]:::support
      C17[lib/gitlab/exclusive_lease.rb \nApp-level locking for workers]:::support
      C18[lib/gitlab/action_cable/config.rb]:::support
      C19[lib/gitlab/utils/job.rb \nJob grouping]:::support
      C20[lib/gitlab/loop_helpers.rb]:::support
    end

    subgraph WorkerConcerns["Shared Worker Concerns & Worker Mixins"]
      class WorkerConcerns group
      D1[CronjobQueue]:::support
      D2[PipelineQueue]:::support
      D3[BackgroundMigration::SingleDatabaseWorker]:::core
      D4[ClusterQueue]:::support
      D5[PipelineBackgroundQueue]:::support
      D6[AutoDevopsQueue]:::support
      D7[ChaosQueue]:::support
      D8[SecurityScansQueue]:::support
      D9[ObjectPoolQueue]:::support
      D10[CronjobChildWorker]:::support
      D11[Reenqueuer]:::support
      D12[EachShardWorker]:::support
      D13[GitGarbageCollectMethods]:::support
      D14[ClusterApplications]:::support
      D15[Search::Worker \nmixin concern]:::support
    end

    subgraph WorkerImpls["Worker Implementations & Feature Groups"]
      class WorkerImpls group
      E1[AuthorizedProjectUpdate::ProjectRecalculateWorker]:::core
      E2[AuthorizedProjectUpdate::ProjectRecalculatePerUserWorker]:::core
      E3[AuthorizedProjectUpdate::UserRefreshFromReplicaWorker]:::core
      E4[AuthorizedProjectUpdate::UserRefreshOverUserRangeWorker]:::core
      E5[AuthorizedProjectUpdate::EnqueueGroupMembersRefreshAuthorizedProjectsWorker]:::core
      E6[AuthorizedProjectUpdate::PeriodicRecalculateWorker]:::core
      E7[Projects::AfterImportWorker]:::core
      
      F1[Ci::CancelPipelineWorker]:::core
      F2[Ci::PipelineCleanupRefWorker]:::core
      F3[Ci::CreateDownstreamPipelineWorker]:::core
      F4[Ci::InitializePipelinesIidSequenceWorker]:::core

      G1[Database::MonitorLockedTablesWorker]:::core
      G2[Database::LockTablesWorker]:::core
      G3[Database::CiProjectMirrorsConsistencyCheckWorker]:::core
      G4[Database::CiNamespaceMirrorsConsistencyCheckWorker]:::core

      H1[BackgroundMigrationWorker]:::core
      H2[BackgroundMigration::CiDatabaseWorker]:::core

      I1[MergeRequestCleanupRefsWorker]:::core
      I2[ScheduleMergeRequestCleanupRefsWorker]:::core
      
      J1[PauseControl::ResumeWorker]:::core
      J2[ConcurrencyLimit::ResumeWorker]:::core

      K1[ContainerExpirationPolicyWorker]:::core
      K2[ContainerRegistry::DeleteContainerRepositoryWorker]:::core
      
      L1[Integrations::ExecuteWorker]:::core
      L2[Integrations::PropagateIntegrationDescendantWorker]:::core
      L3[Integrations::GroupMentionWorker]:::core
      L4[Integrations::JiraConnect::RemoveBranchWorker]:::core

      M1[Gitlab::Scheduling::ScheduleWithinWorker]:::core
      M2[Gitlab::Export::PruneProjectExportJobsWorker]:::core
      M3[Gitlab::BitbucketServerImport::AdvanceStageWorker]:::core
      M4[Gitlab::GithubImport::AdvanceStageWorker]:::core

      N1[PagesWorker]:::core
      N2[PagesDomainSslRenewalWorker]:::core
      N3[PagesDomainSslRenewalCronWorker]:::core
      N4[PagesDomainVerificationCronWorker]:::core
      N5[PagesDomainRemovalCronWorker]:::core
      N6[Pages::ResetPagesDefaultDomainRedirectWorker]:::core

      O1[WebHooks::LogDestroyWorker]:::core
      O2[WebHooks::LogExecutionWorker]:::core
      O3[WebHookWorker]:::core

      P1[StuckExportJobsWorker]:::core
      P2[DeleteMergedBranchesWorker]:::core

      Q1[ServiceDeskEmailReceiverWorker]:::core
      Q2[EmailReceiverWorker]:::core

      R1[Deployments::UpdateEnvironmentWorker]:::core

      S1[Environments::AutoDeleteCronWorker]:::core
      S2[Environments::RecalculateAutoStopWorker]:::core

      T1[FlushCounterIncrementsWorker]:::core

      U1[UploadsChecksumWorker]:::core

      V1[RemoveExpiredGroupLinksWorker]:::core
      V2[GroupDestroyWorker]:::core

      W1[Analytics::UsageTrends::CounterJobWorker]:::core
      W2[Analytics::UsageTrends::CountJobTriggerWorker]:::core

      X1[Release::ManageEvidenceWorker]:::core
      X2[Release::CreateEvidenceWorker]:::core
      
      Y1[LooseForeignKeys::CleanupWorker]:::core
      Y2[Snippets::ScheduleBulkRepositoryShardMovesWorker]:::core
      Y3[Namespaces::RootStatisticsWorker]:::core
      Y4[Namespaces::ScheduleAggregationWorker]:::core
      Y5[Namespaces::ProcessSyncEventsWorker]:::core
      Y6[Namespaces::ProcessOutdatedNamespaceDescendantsCronWorker]:::core
      Y7[Namespaces::UpdateRootStatisticsWorker]:::core

      Z1[AppSec::Dast::ProfileScheduleWorker]:::core
    end

    subgraph DataStructures["Domain-specific Data Structures"]
      class DataStructures group
      D21[BackgroundMigrationJob\nstatus, batch, metrics]:::data
      D22[BatchedJobTransitionLog]:::data
      D23[SessionMap]:::data
      D24[BatchMetrics]:::data
      D25[StatActivitySampler]:::data
      D26[Cronjob Configs \nQueue Schedules, Initializers]:::data
      D27[Pipeline Queue Interface]:::data
      D28[ExclusiveLease Distributed Locking]:::data
    end

    subgraph ErrorHandling["Worker/DB Error Handlers"]
      class ErrorHandling group
      E21[BackgroundMigration SubBatchTimeoutError]:::error
      E22[TableShouldNotBeLocked\nLockTablesWorker]:::error
      E23[DestroyError \nWebHooks::LogDestroyWorker]:::error
      E24[BatchMigration::TimeoutExceptions]:::error
    end
  end

  %% Worker relationships (logical/data/control-flow, not invocation steps)

  %% Core <-> Job/Worker Coordination
  A2 -- manages background jobs --> B1
  B1 -- stores job state --> B2
  B1 -- metrics --> B4
  B1 -- transition logs --> B5
  B1 -- job execution wrapper --> B3
  B3 -- usage/records --> B4
  B3 -- transitions --> B5
  B1 -- error handling --> B6

  %% Session Sticking and Load Balancing (multi-DB workers)
  B7 -- used by stick strategies --> B8
  B8 -- used for db connection policies --> CoreDomainFeatures
  B9 -- provides DNS/SRV resolution --> B8
  B8 -- influences jobs and workers --> C2

  %% Sidekiq/Worker infra
  C1 -- base for --> WorkerImpls
  C1 -- contains common logic for -> WorkerConcerns
  WorkerConcerns -- mixins for jobs --> WorkerImpls
  D1 -- schedules/queues crons --> WorkerImpls
  D2 -- pipeline job ordering --> WorkerImpls
  D3 -- background migration concern --> H1

  %% Worker/Job Scheduling
  C3 -- versioning controls --> C1
  C4 -- injects version on job execution --> C1
  C5 -- Sidekiq options access --> C1
  C6 -- logical to physical queue router --> WorkerImpls
  C7 -- worker assignment to queue --> C6
  C8 -- cronjob init --> WorkerImpls
  C9 -- CLI/ops queue config --> C6
  C10 -- fallback queue configs --> C6
  C20 -- loop helpers used by environments/auto-delete and others --> S1

  %% Worker Daemonization, Monitoring, Lease
  C15 -- manages running job concurrency --> C1
  C16 -- process/daemon infra --> C15
  C17 -- distributed locks (leases) --> WorkerImpls
  C18 -- pool size for action cable jobs --> C1
  C19 -- job naming/grouping for stats --> WorkerImpls

  %% Data Structures
  D21 -- tracks background job progress --> H1
  D22 -- logs state transitions for batch background jobs --> H1
  D23 -- track session stickiness --> B8
  D24 -- metrics aggregation, referenced in batch/job wrappers --> B3
  D25 -- records live db activity for diagnostics --> CoreDomainFeatures
  D26 -- used by cronjob initializers, cronjob queue mixins --> WorkerImpls
  D27 -- base interface for pipeline group queue concerns --> D2,D5
  D28 -- distributed locking helper --> WorkerImpls

  %% Error Handling
  E21 -- thrown on sub-batch worker timeout --> B1
  E22 -- lock error in LockTablesWorker --> G2
  E23 -- error management for webhooks --> O1
  E24 -- raised on release evidence worker timeout --> X1

  %% Major Worker Logical Groupings / Patterns
  %% - Many workers support cron scheduling, job deduplication, leasing etc.
  WorkerImpls -- includes concern--> WorkerConcerns
  WorkerImpls -- uses data structures--> DataStructures
  WorkerImpls -- error delegation --> ErrorHandling
  
  %% Relations between core worker feature groups (some key cross-usage)
  E1 -- recalculates --> E2
  E1 -- manages authorizations for users --> E3
  E3 -- conditionally enqueues jobs --> E4
  E4 -- enqueues per-user update jobs --> E1
  E2 -- extends --> E1
  E5 -- enqueues group member jobs --> E1
  E6 -- periodic recalculation --> E1

  F1 -- cancels pipelines --> F2
  F2 -- cleans up ci refs --> F1
  F3 -- schedules downstream pipelines --> F1
  F4 -- initializes iid sequence on event --> F1

  G1 -- monitors table locks --> G2
  G2 -- applies table locks --> G1
  G3 -- consistency for project mirrors --> G1
  G4 -- consistency for namespace mirrors --> G1

  J1 -- resumes paused background jobs --> WorkerImpls
  J2 -- manages worker concurrency limits --> WorkerImpls

  K2 -- uses limited capacity concern and lock via cronjob child worker --> D10
  K1 -- triggers registry cleanups --> K2

  L1 -- processes integration events --> L2
  L2 -- propagates descendant integration configs --> L1
  L3 -- manages group mention notifications --> L1
  L4 -- handles JIRA connect branch removals --> L1

  N1 -- manages pages deployments --> WorkerImpls
  N2 -- scheduled SSL renewal --> N1
  N3 -- cron for domain SSL renewals --> N2
  N4 -- scheduled domain verification --> N1,N2
  N5 -- domain removal tasks --> N1,N2
  N6 -- domain redirect logic --> N1,N2

  O1 -- destroys webhook logs --> O2
  O2 -- executes webhook logs --> O1
  O3 -- main web hook job --> O1,O2

  P1 -- handles stuck export jobs --> WorkerImpls
  P2 -- removes deleted branches --> WorkerImpls

  Q1 -- receives email via service desk, extends Q2 --> Q2
  Q2 -- main email receiver job --> Q1

  W2 -- triggers counter job workers via cron --> W1

  X2 -- creates evidence for releases --> X1

  %% Namespace/aggregation/cleanup jobs
  Y1 -- cleans up loose foreign keys --> G4
  Y3 -- computes root statistics --> Y5
  Y4 -- schedules aggregates --> Y3
  Y5 -- processes sync events --> Y3
  Y6 -- cron job for descendants --> Y4,Y5
  Y7 -- event sourced update of root statistics --> Y3

  %% AppSec/other jobs
  Z1 -- schedules DAST profiles --> WorkerImpls

  %% Supportive/utility modules are included where necessary by feature/implementation jobs.

  %% Data transformation and flow
  B1 -- coordinates data transformations through batch jobs --> B2,B3,B4
  G1,G2 -- influence table locks/statuses --> D21
  WorkerImpls -- read/update stats/lock states via --> DataStructures

  %% Worker Concerns used by various workers
  D1 -- used in workers that execute at intervals, e.g., background migrations, cron jobs
  D2 -- used in pipeline queue jobs for partitioning, e.g., F2, F3
  D3 -- included by background migration worker and ci database worker
  D10 -- included by child jobs in cron/bulk operations
  D11 -- provides utility for jobs able to reenqueue themselves after failure or throttling
  D12 -- supports sharded execution for partitioned tables
  D13 -- shared concern for Git garbage collection workers

  %% Initialization/Setup structure
  C8 -- initializes cron jobs at startup --> D1, WorkerImpls
  C17 -- exclusive lease for critical background tasks --> D28

  %% Major cross-group interaction: Migration job triggers background workers
  B1 -- triggers and coordinates --> H1,H2
  H1,H2 -- execute background migrations against tracked jobs --> B2

  %% Sidekiq/Worker scheduling config flows
  C5,C6,C8,C9,C10 -- coordinate to define and apply Sidekiq job config and queue mapping --> WorkerImpls

  %% Logical boundaries for Database, Worker Management, Data Structures, Error Handling
  class DB, CoreDomainFeatures, BackgroundJobWorkers, WorkerConcerns, WorkerImpls, DataStructures, ErrorHandling group
  class A1,A2,A3,B1,B2,B3,B4,B5,B6,B7,B10,B11,B8 core
  class C1,C2,C15 core
  class D3,H1,H2 core
  class G1,G2,G3,G4,E1,E2,E3,E4,E5,E6,F1,F2,F3,F4,J1,J2,K1,K2,L1,L2,L3,L4,M1,M2,M3,M4,N1,N2,N3,N4,N5,N6,O1,O2,O3,P1,P2,Q1,Q2,R1,S1,S2,T1,U1,V1,V2,W1,W2,X1,X2,Y1,Y3,Y4,Y5,Y6,Y7,Z1 core

  class C3,C4,C5,C6,C7,C8,C9,C10,C11,C12,C13,C14,C16,C17,C18,C19,C20 support
  class D1,D2,D4,D5,D6,D7,D8,D9,D10,D11,D12,D13,D14,D15 support

  class B2,B4,B5,D21,D22,D23,D24,D25,D26,D27,D28 data

  class B6,E21,E22,E23,E24 error

  class C8 init
```