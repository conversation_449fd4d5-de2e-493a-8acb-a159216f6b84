```mermaid
flowchart TD
  %% GLOBAL STYLES
  linkStyle default stroke:#bbb,stroke-width:2px
  classDef coreDomain fill:#D4F1F9,stroke:#B7DFF8,stroke-width:2px,stroke-dasharray: 2 2,rx:14,ry:14
  classDef utility fill:#FFF8DC,stroke:#FFEEB2,stroke-width:2px,stroke-dasharray: 2 2,rx:14,ry:14
  classDef dataStruct fill:#E0F8E0,stroke:#BDF7C4,stroke-width:2px,stroke-dasharray: 2 2,rx:14,ry:14
  classDef errorHandling fill:#FFE4E1,stroke:#FFB6AC,stroke-width:2px,stroke-dasharray: 2 2,rx:14,ry:14
  classDef init fill:#E6E6FA,stroke:#B8B8FF,stroke-width:2px,stroke-dasharray: 2 2,rx:14,ry:14
  classDef groupbox fill:#F8F8F8,stroke:#D4F1F9,stroke-width:3px,rx:18,ry:18
  classDef groupboxYellow fill:#F8F8F8,stroke:#FFEEB2,stroke-width:3px,rx:18,ry:18
  classDef groupboxGreen fill:#F8F8F8,stroke:#BDF7C4,stroke-width:3px,rx:18,ry:18
  classDef groupboxPurple fill:#F8F8F8,stroke:#B8B8FF,stroke-width:3px,rx:18,ry:18

  %% DOMAIN BOUNDARY & GROUPING

  %% ---- API Integrations for ML Experiment & Artifact Management ----
  subgraph ML API & Entrypoints
    direction TB
    class ML API & Entrypoints groupbox

    subgraph MLflow API [MLflow REST API Handler]
      direction TB
      class MLflow API groupbox

      mlflowEntrypoint["lib/api/ml/mlflow/entrypoint.rb<br/>MLflow Experiment API Entrypoint"]:::coreDomain
      mlflowApiHelpers["API::Ml::Mlflow::ApiHelpers<br/>*implied, provides API-specific helpers"]:::utility
      relatedResourcesHelpers["API::Helpers::RelatedResourcesHelpers<br/>*implied, common helpers for related resources"]:::utility
    end

    subgraph MLflow Artifacts API [MLflow Artifacts API Handler]
      direction TB
      class MLflow Artifacts API groupbox

      mlflowArtifactsEntrypoint["lib/api/ml/mlflow_artifacts/entrypoint.rb<br/>MLflow Artifacts API Entrypoint"]:::coreDomain
      mlflowArtifactsApiHelpers["API::Ml::Mlflow::ApiHelpers<br/>*implied, helpers reused for artifact handling"]:::utility
      relatedResourcesHelpers2["API::Helpers::RelatedResourcesHelpers<br/>*implied, common helpers reused"]:::utility
    end
  end

  %% ---- MLops Controller Surface ----
  subgraph Project AI/MLOps Controller
    direction TB
    class Project AI/MLOps Controller groupbox
    agentsController["ee/app/controllers/projects/ml/agents_controller.rb<br/>Project-scoped AI Agents Controller"]:::coreDomain
  end

  %% ---- Vendor-Specific Model & Configuration Management ----
  subgraph LLM Integrations & Configuration
    direction TB
    class LLM Integrations & Configuration groupbox
    vertexAIConf["ee/lib/gitlab/llm/vertex_ai/configuration.rb<br/>Vertex AI Model Configuration/Abstraction"]:::coreDomain
  end

  %% ---- Data Preprocessing Pipeline ----
  subgraph Data Preprocessing Utilities
    direction TB
    class Data Preprocessing Utilities groupboxGreen
    chunking["gems/gitlab-active-context/lib/active_context/preprocessors/chunking.rb<br/>Document Sequence Chunking"]:::dataStruct
  end

  %% ---- Jupyter/IPython Notebook Utilities ----
  subgraph Notebook Utility
    direction TB
    class Notebook Utility groupboxYellow
    ipynbDiffVersion["gems/ipynbdiff/lib/ipynb_diff/version.rb<br/>Notebook Diff Utility Versioning"]:::utility
  end

  %% ----- LOGICAL RELATIONSHIPS & INTERACTIONS -----

  %% MLOPS CONTROLLER to ML experiment and artifact API Entrypoints
  agentsController --> mlflowEntrypoint
  agentsController --> mlflowArtifactsEntrypoint

  %% Entrypoints use helpers shared abstractions
  mlflowEntrypoint -- uses --> mlflowApiHelpers
  mlflowEntrypoint -- uses --> relatedResourcesHelpers
  mlflowArtifactsEntrypoint -- uses --> mlflowArtifactsApiHelpers
  mlflowArtifactsEntrypoint -- uses --> relatedResourcesHelpers2
  mlflowApiHelpers --- mlflowArtifactsApiHelpers

  %% Controller interfaces with LLM Configuration (Vertex AI)
  agentsController -- "invokes/queries model config for agent features" --> vertexAIConf

  %% VertexAI config delegates to model abstractions domain-specific data manipulation
  vertexAIConf -- "delegates serialization & host connection to model_config objects"--> chunking

  %% Data Preprocessing is invoked by controller and by ML API layers for pipeline integration
  agentsController -- "calls for agent ref chunking" --> chunking
  mlflowEntrypoint -- "applies chunking for experiment artifact transfer" --> chunking
  mlflowArtifactsEntrypoint -- "applies chunking to artifacts" --> chunking

  %% Notebook diffing primarily supports experiment management & reproducibility
  mlflowEntrypoint -- "uses for experiment version diffing" --> ipynbDiffVersion
  mlflowArtifactsEntrypoint -- "uses for artifact diffing" --> ipynbDiffVersion

  %% --- CORE CONCEPTUAL FLOWS & DATA STRUCTURE TRANSFORMATIONS ---

  %% Chunking transforms document references data structure
  chunking -.->|"chunks document sequences<br/>yields {field: chunk} mappings"| agentsController

  %% VertexAIConf applies model config and delegates transformation to data structure utils chunking
  vertexAIConf -.->|"normalizes model config,<br/>provides as_json, payload<br/>uses data preprocessors"| chunking

  %% Experiment & artifact management routes feed into project MLOps dashboard/controller
  mlflowEntrypoint -.->|"creates, tracks, and records<br/>ML experiments"| agentsController
  mlflowArtifactsEntrypoint -.->|"manages model artifacts<br/>and storage records"| agentsController

  %% NotebookDiff is utilized as a supporting utility for artifact/experiment versioning
  ipynbDiffVersion -.->|"enables version diff for<br/>notebooks in experiments"| mlflowEntrypoint
  ipynbDiffVersion -.->|"enables artifact version diff for<br/>notebook-based results"| mlflowArtifactsEntrypoint

  %% LINK NODE CLASSES FOR LEGEND CLARITY
  class mlflowEntrypoint,mlflowArtifactsEntrypoint,agentsController,vertexAIConf coreDomain
  class mlflowApiHelpers,mlflowArtifactsApiHelpers,relatedResourcesHelpers,relatedResourcesHelpers2,ipynbDiffVersion utility
  class chunking dataStruct

  %% ---- GROUPING BOX BORDERS ----
  style ML API & Entrypoints stroke:#D4F1F9,stroke-width:3px,fill:#F8F8F8,rx:16,ry:16
  style Project AI/MLOps Controller stroke:#D4F1F9,stroke-width:3px,fill:#F8F8F8,rx:16,ry:16
  style LLM Integrations & Configuration stroke:#D4F1F9,stroke-width:3px,fill:#F8F8F8,rx:16,ry:16
  style Data Preprocessing Utilities stroke:#BDF7C4,stroke-width:3px,fill:#F8F8F8,rx:16,ry:16
  style Notebook Utility stroke:#FFEEB2,stroke-width:3px,fill:#F8F8F8,rx:16,ry:16

  %% --- LEGEND ---
  subgraph Legend[ ]
    direction TB
    class Legend groupbox
    lcore["Core Domain<br/>#D4F1F9"]:::coreDomain
    lutil["Supporting/Utility<br/>#FFF8DC"]:::utility
    ldata["Data Structure<br/>#E0F8E0"]:::dataStruct
    linit["Initialization/Setup<br/>#E6E6FA"]:::init
  end
  Legend -.-> lcore
  Legend -.-> lutil
  Legend -.-> ldata
  Legend -.-> linit
```