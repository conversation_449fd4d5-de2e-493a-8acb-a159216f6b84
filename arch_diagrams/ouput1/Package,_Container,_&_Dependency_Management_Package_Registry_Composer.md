```mermaid
flowchart TD
  %% Theme colors and styles
  classDef core fill:#D4F1F9,stroke:#9ad1e3,stroke-width:2px,color:#222,stroke-dasharray:0,rounded
  classDef support fill:#FFF8DC,stroke:#ffd97a,stroke-width:2px,color:#222,stroke-dasharray:0,rounded
  classDef data fill:#E0F8E0,stroke:#abedc6,stroke-width:2px,color:#222,stroke-dasharray:0,rounded
  classDef error fill:#FFE4E1,stroke:#ffacac,stroke-width:2px,color:#222,stroke-dasharray:0,rounded
  classDef init fill:#E6E6FA,stroke:#bdbdf9,stroke-width:2px,color:#222,stroke-dasharray:0,rounded
  classDef grouping fill:#F8F8F8,stroke:#cfd7dc,stroke-width:2px,rounded

  %% Subgraph: Domain Layer - Core Concepts
  subgraph Domain_Package_Registry["Domain Layer: Composer Package Registry"]
    direction TB
    style Domain_Package_Registry fill:#F8F8F8,stroke:#a3c7e7

    subgraph CoreDomainLogic["Core Domain Models" ]
      direction TB
      style CoreDomainLogic fill:#F8F8F8,stroke:#88c3eb

      packages[Packages Module<br>Abstraction layer for all package-related domains]:::core
      composer_package[Composer::Package<br>Composer-specific Package model<br>STI on ::Packages::Package<br>Links to Composer Metadatum]:::core
      composer_metadatum[Composer::Metadatum<br>Metadata record for Composer packages<br>Belongs to Composer::Package<br>Holds composer_json, target_sha]:::data
    end

    subgraph DataPolicyValidation["Authorization & Validation" ]
      direction TB
      style DataPolicyValidation fill:#F8F8F8,stroke:#88c3eb

      metadatum_policy[Composer::MetadatumPolicy<br>Delegates policy checks to Package]:::support
      metadatum_validation[Model Validations & Delegates<br>Validations for metadata, semver & presence]:::support
    end

    subgraph DataStructures_API["Data Structures & API Integration" ]
      direction TB
      style DataStructures_API fill:#F8F8F8,stroke:#7eb99a

      graphql_metadatum_type[GraphQL Type: MetadatumType<br>Exposes fields for query API<br>composer_json, target_sha, etc.]:::data
      version_index[Composer::VersionIndex<br>Data structure builder for Composer package versions<br>as_json, dist & source attributes]:::data
      composer_json_linker[ComposerJsonLinker<br>Enriches composer.json with clickable links<br>Dependency linking]:::support
    end
  end

  %% Subgraph: Services & Business Logic
  subgraph ServicesLogic["Services: Business Logic & Processing" ]
    direction TB
    style ServicesLogic fill:#F8F8F8,stroke:#9369d2

    version_parser_service[VersionParserService<br>Parses and normalizes version names from tags/branches]:::support
    create_package_service[CreatePackageService<br>Handles transactional creation of Composer package & metadata]:::core
    composer_json_service[ComposerJsonService<br>Loads, parses, and validates composer.json content<br>Custom JSON error handling]:::support
  end

  %% Subgraph: Finders & Query Layer
  subgraph FindersLayer["Query Layer: Finders & Filtering" ]
    direction TB
    style FindersLayer fill:#F8F8F8,stroke:#ffe699

    packages_finder[Composer::PackagesFinder<br>Finder abstraction<br>Queries accessible Composer packages for user/group]:::support
  end

  %% Subgraph: Interfaces
  subgraph PresentationAPI["API & External Access" ]
    direction TB
    style PresentationAPI fill:#F8F8F8,stroke:#b9cde2

    composer_packages_api[API: ComposerPackages<br>Handles Composer HTTP API for package consumers<br>Authentication, params, queries via finder]:::core
    rubygem_packages_api[API: RubygemPackages<br>Analogous for completeness, not primary here]:::support
    graphql_type[GraphQL Type<br>Composer::MetadatumType for API responses]:::data
  end

  %% Subgraph: Tests
  subgraph TestSupport["Specs & QA" ]
    direction TB
    style TestSupport fill:#F8F8F8,stroke:#9ad1e3

    version_index_spec[Spec: VersionIndexSpec<br>Verifies version index data structure/output<br>Fixture for composer packages, projects]:::support
    qa_composer_registry_spec[QA: ComposerRegistry Spec<br>Feature test for Composer registry API scenarios]:::support
  end

  %% Subgraph: Initialization & Setup
  subgraph Initialization["Initialization & Setup" ]
    direction TB
    style Initialization fill:#F8F8F8,stroke:#bdbdf9

  end

  %% Edges: Logical Relationships vertical layout
  %% Core model relationships
  packages --> composer_package
  composer_package --> composer_metadatum
  composer_metadatum --> metadatum_validation

  %% Authorization/validation
  composer_metadatum --> metadatum_policy
  metadatum_policy --> composer_package

  %% Service-to-model relationships
  create_package_service --> composer_package
  create_package_service --> composer_metadatum
  create_package_service --> composer_json_service
  create_package_service --> version_parser_service
  composer_json_service --> composer_metadatum
  composer_json_service --> error

  version_parser_service --> create_package_service
  composer_package --semantic version validation--> version_parser_service

  %% API/GraphQL Exposition
  composer_metadatum --> graphql_metadatum_type
  graphql_metadatum_type --> composer_metadatum

  version_index --> composer_package
  version_index --> composer_metadatum
  version_index --> composer_json_service

  composer_json_linker --> composer_json_service

  %% Finders
  packages_finder --> composer_package
  packages_finder --> composer_metadatum

  %% API layer
  composer_packages_api --> packages_finder
  composer_packages_api --> composer_package
  composer_packages_api --> version_index
  composer_packages_api --> composer_metadatum

  rubygem_packages_api ---|analogous| composer_packages_api

  %% Tests & QA
  version_index_spec --> version_index
  version_index_spec --> composer_package
  version_index_spec --> composer_metadatum

  qa_composer_registry_spec --> composer_packages_api

  %% Data structures & linking
  composer_json_linker --> composer_json_service
  composer_json_service -.-> error

  %% Styling (apply node classes)
  class packages,composer_package,create_package_service,composer_packages_api core
  class composer_metadatum,version_index,graphql_metadatum_type data
  class metadatum_policy,metadatum_validation,version_parser_service,packages_finder,support composer_json_service,composer_json_linker,version_index_spec,qa_composer_registry_spec support
  class rubygem_packages_api support
  class error error
  class Initialization init
  class Domain_Package_Registry,CoreDomainLogic,ServicesLogic,FindersLayer,PresentationAPI,TestSupport,Initialization grouping

  %% Domain-specific error handling node
  error[InvalidJson Error Handling<br>Custom error for invalid composer.json<br>Raised by ComposerJsonService]:::error

  %% Group: Dataflows between API, Models, Aggregation
  subgraph Dataflows["Data Transformations & Aggregation" ]
    direction TB
    style Dataflows fill:#F8F8F8,stroke:#b7ddb7

    df1[Package instance<br>Aggregates Metadatum and validation status]:::data
    df2[VersionIndex as_json<br>Maps Composer package & metadata to API format<br>Provides dist/source for Composer clients]:::data
    df3[ComposerJson transformation<br>From uploaded file to validated domain model]:::data
  end

  composer_packages_api --> df1
  composer_package --> df1
  composer_metadatum --> df1
  composer_package --> df2
  composer_metadatum --> df2
  version_index --> df2
  composer_json_service --> df3
  composer_metadatum --> df3
  composer_json_linker --> df3
```