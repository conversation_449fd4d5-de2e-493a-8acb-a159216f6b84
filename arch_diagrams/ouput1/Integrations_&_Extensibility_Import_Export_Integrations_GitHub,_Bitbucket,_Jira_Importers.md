```mermaid
flowchart TD
  %% COLOR & SHAPE DEFINITIONS
  classDef core fill:#D4F1F9,stroke:#90badb,stroke-width:2px,color:#222,stroke-dasharray:0,rx:8,ry:8
  classDef support fill:#FFF8DC,stroke:#f0db99,stroke-width:2px,color:#222,stroke-dasharray:0,rx:8,ry:8
  classDef data fill:#E0F8E0,stroke:#7ecfa1,stroke-width:2px,color:#222,stroke-dasharray:0,rx:8,ry:8
  classDef error fill:#FFE4E1,stroke:#fac0be,stroke-width:2px,color:#222,stroke-dasharray:0,rx:8,ry:8
  classDef init fill:#E6E6FA,stroke:#b6a9d3,stroke-width:2px,color:#222,stroke-dasharray:0,rx:8,ry:8
  classDef group fill:#F8F8F8,stroke:#90badb,stroke-width:2px,rx:20,ry:20

  %% --- MAIN DOMAIN SUBGRAPHS ---
  %% GITHUB IMPORT LOGIC
  subgraph sgGithubImport["GitHub Import - Core Logic"]
    direction TB
    ghStage[[app/workers/gitlab/github_import/stage]]:::core
    ghWorkers[[app/workers/gitlab/github_import/]]:::core
    ghImporters[[lib/gitlab/github_import/importer/]]:::core
    ghRepresent[[lib/gitlab/github_import/representation/]]:::data
    ghDataStructs[
      "GitHub Data Structures":::data
      ghIssue["Issue"]:::data
      ghPullReq["PullRequest"]:::data
      ghCollab["Collaborator"]:::data
      ghPRReview["PullRequestReview"]:::data
      ghDiffNote["DiffNote"]:::data
      ghNoteText["NoteText"]:::data
      ghReplayEv["ReplayEvent"]:::data
      ghProtectedBranch["ProtectedBranch"]:::data
      ghLfsObj["LfsObject"]:::data
      ghUser["User"]:::data
      ghIssueEvent["IssueEvent"]:::data
      ghPRReviewReq["PullRequests::ReviewRequests"]:::data
    ]
    ghUtils[[lib/gitlab/github_import/]]:::support

    ghStageG1[[Stages:]]
    ghStageBaseData["ImportBaseDataWorker"]:::core
    ghStageIssuesAndNotes["ImportIssuesAndDiffNotesWorker"]:::core
    ghStagePR["ImportPullRequestsWorker"]:::core
    ghStageNotes["ImportNotesWorker"]:::core
    ghStagePRReviews["ImportPullRequestsReviewsWorker"]:::core
    ghStagePRReviewReq["ImportPullRequestsReviewRequestsWorker"]:::core
    ghStagePRMerged["ImportPullRequestsMergedByWorker"]:::core
    ghStageCollab["ImportCollaboratorsWorker"]:::core
    ghStageProtBranch["ImportProtectedBranchesWorker"]:::core
    ghStageLFS["ImportLfsObjectsWorker"]:::core
    ghStageAtt["ImportAttachmentsWorker"]:::core
    ghStageRepo["ImportRepositoryWorker"]:::core
    ghStageFinish["FinishImportWorker"]:::core
    ghStageG1 --> ghStageBaseData
    ghStageG1 --> ghStageIssuesAndNotes
    ghStageG1 --> ghStagePR
    ghStageG1 --> ghStageNotes
    ghStageG1 --> ghStagePRReviews
    ghStageG1 --> ghStagePRReviewReq
    ghStageG1 --> ghStagePRMerged
    ghStageG1 --> ghStageCollab
    ghStageG1 --> ghStageProtBranch
    ghStageG1 --> ghStageLFS
    ghStageG1 --> ghStageAtt
    ghStageG1 --> ghStageRepo
    ghStageG1 --> ghStageFinish

    ghStage -- Uses --> ghWorkers
    ghStage -- Drives --> ghImporters
    ghWorkers -- Converts/Imports --> ghImporters
    ghImporters -- Transform --> ghRepresent
    ghImporters -- Relies-on --> ghUtils
    ghStage -- Progress --> ghStageG1
    ghStagePR -- Allocates MR IDs --> ghImporters
    ghStageIssuesAndNotes -- Allocates Issues IDs --> ghImporters

    subgraph ghObjWorkers["GitHub Object Import Workers"]
      direction TB
      ghWkImportIssue["ImportIssueWorker"]:::core
      ghWkImportPR["ImportPullRequestWorker"]:::core
      ghWkImportNote["ImportNoteWorker"]:::core
      ghWkImportDiffNote["ImportDiffNoteWorker"]:::core
      ghWkImportCollab["ImportCollaboratorWorker"]:::core
      ghWkImportLFS["ImportLfsObjectWorker"]:::core
      ghWkImportPRReview["pull_requests/import_review_worker"]:::core
      ghWkImportPRRevReq["pull_requests/import_review_request_worker"]:::core
      ghWkImportReplayEv["ReplayEventsWorker"]:::core
      ghWkImportPRMergedBy["pull_requests/import_merged_by_worker"]:::core
      ghWkImportProtBranch["ImportProtectedBranchWorker"]:::core
    end

    subgraph ghAttachmentsImport["GitHub Attachments Import Workers"]
      direction TB
      ghAttNote["attachments/import_note_worker"]:::core
      ghAttMR["attachments/import_merge_request_worker"]:::core
      ghAttIssue["attachments/import_issue_worker"]:::core
      ghAttRelease["attachments/import_release_worker"]:::core
    end

    ghImporters -- Schedules --> ghObjWorkers
    ghImporters -- Schedules --> ghAttachmentsImport

    ghWkImportIssue -- Deserializes --> ghIssue
    ghWkImportPR -- Deserializes --> ghPullReq
    ghWkImportNote -- Deserializes --> ghRepresent
    ghWkImportDiffNote -- Deserializes --> ghDiffNote
    ghWkImportCollab -- Deserializes --> ghCollab
    ghWkImportPRReview -- Deserializes --> ghPRReview
    ghWkImportPRRevReq -- Deserializes --> ghPRReviewReq
    ghWkImportLFS -- Deserializes --> ghLfsObj
    ghWkImportReplayEv -- Deserializes --> ghReplayEv
    ghWkImportProtBranch -- Deserializes --> ghProtectedBranch
    ghAttNote -- Deserializes --> ghNoteText
    ghAttMR -- Deserializes --> ghNoteText
    ghAttIssue -- Deserializes --> ghNoteText
    ghAttRelease -- Deserializes --> ghNoteText

    ghWkImportIssue -- Imports/Transforms --> ghImporters
    ghWkImportPR -- Imports/Transforms --> ghImporters
    ghWkImportNote -- Imports/Transforms --> ghImporters
    ghWkImportDiffNote -- Imports/Transforms --> ghImporters
    ghWkImportCollab -- Imports/Transforms --> ghImporters
    ghWkImportPRReview -- Imports/Transforms --> ghImporters
    ghWkImportPRRevReq -- Imports/Transforms --> ghImporters
    ghWkImportLFS -- Imports/Transforms --> ghImporters
    ghWkImportReplayEv -- Imports/Transforms --> ghImporters
    ghWkImportProtBranch -- Imports/Transforms --> ghImporters
    ghAttNote -- Imports/Transforms --> ghImporters
    ghAttMR -- Imports/Transforms --> ghImporters
    ghAttIssue -- Imports/Transforms --> ghImporters
    ghAttRelease -- Imports/Transforms --> ghImporters

    ghUtils -- Provides --> ghObjectCounter["ObjectCounter stats"]:::support
    ghObjectCounter -- Used-for-stats-by --> ghImporters
    ghObjectCounter -- Used-by --> ghStageRepo["ImportRepositoryWorker"]
    ghObjectCounter -- Used-by --> ghStageIssuesAndNotes

    ghUtils -- Provides --> ghUserFinder["UserFinder"]:::support
    ghUtils -- Provides --> ghLabelFinder["LabelFinder"]:::support
    ghUtils -- Provides --> ghMilestoneFinder["MilestoneFinder"]:::support
    ghUtils -- Provides --> ghIssuableFinder["IssuableFinder"]:::support

    ghImporters -- Relies-on --> ghUserFinder
    ghImporters -- Relies-on --> ghLabelFinder
    ghImporters -- Relies-on --> ghMilestoneFinder
    ghImporters -- Relies-on --> ghIssuableFinder

    ghRepresent --> ghIssue
    ghRepresent --> ghPullReq
    ghRepresent --> ghCollab
    ghRepresent --> ghPRReview
    ghRepresent --> ghDiffNote
    ghRepresent --> ghNoteText
    ghRepresent --> ghReplayEv
    ghRepresent --> ghProtectedBranch
    ghRepresent --> ghLfsObj
    ghRepresent --> ghUser
    ghRepresent --> ghIssueEvent
    ghRepresent --> ghPRReviewReq

    ghImporters -- Transforms --> ghDataStructs

    ghWorkers -- Utilizes --> ghUtils
    ghObjWorkers -- Utilizes --> ghUtils
    ghStage -- Utilizes --> ghUtils

    ghUtils -.-> ghLogger["Logger"]:::support
    ghUtils -.-> ghSettings["Settings"]:::support
  end

  %% BITBUCKET IMPORT LOGIC
  subgraph sgBitbucketImport["Bitbucket Import - Core Logic"]
    direction TB
    bbStage[[app/workers/gitlab/bitbucket_import/stage]]:::core
    bbWorkers[[app/workers/gitlab/bitbucket_import/]]:::core
    bbImporters[[lib/gitlab/bitbucket_import/importers/]]:::core
    bbDataStructs[
      "Bitbucket Data Structures":::data
      bbIssue["Issue"]:::data
      bbPullReq["PullRequest"]:::data
      bbNotes["Notes"]:::data
      bbLfsObj["LfsObject"]:::data
      bbUser["User"]:::data
    ]
    bbUtils[[lib/gitlab/bitbucket_import/]]:::support

    bbStageG1[[Stages:]]

    bbStageRepo["ImportRepositoryWorker"]:::core
    bbStageIssues["ImportIssuesWorker"]:::core
    bbStagePR["ImportPullRequestsWorker"]:::core
    bbStageNotes["ImportIssuesNotesWorker"]:::core
    bbStagePRNotes["ImportPullRequestsNotesWorker"]:::core
    bbStageLFS["ImportLfsObjectsWorker"]:::core
    bbStageUsers["ImportUsersWorker"]:::core
    bbStageFinish["FinishImportWorker"]:::core

    bbStageG1 --> bbStageRepo
    bbStageG1 --> bbStageIssues
    bbStageG1 --> bbStagePR
    bbStageG1 --> bbStageNotes
    bbStageG1 --> bbStagePRNotes
    bbStageG1 --> bbStageLFS
    bbStageG1 --> bbStageUsers
    bbStageG1 --> bbStageFinish

    bbStage -- Drives --> bbStageG1
    bbStage -- Spawns --> bbWorkers
    bbWorkers -- Orchestrates --> bbImporters
    bbImporters -- Uses --> bbDataStructs
    bbImporters -- Relies-on --> bbUtils

    bbObjWorkers[
      "Bitbucket Object Import Workers":::core
      bbWkImportIssue["ImportIssueWorker"]:::core
      bbWkImportPR["ImportPullRequestWorker"]:::core
      bbWkImportIssueNotes["ImportIssueNotesWorker"]:::core
      bbWkImportPRNotes["ImportPullRequestNotesWorker"]:::core
      bbWkImportLFS["ImportLfsObjectWorker"]:::core
    ]

    bbWkImportIssue -- Deserializes --> bbIssue
    bbWkImportIssueNotes -- Deserializes --> bbNotes
    bbWkImportPR -- Deserializes --> bbPullReq
    bbWkImportPRNotes -- Deserializes --> bbNotes
    bbWkImportLFS -- Deserializes --> bbLfsObj

    bbStage -- Utilizes --> bbObjWorkers
    bbStage -- Advances -> bbStageFinish
    bbImporters -- Spawns --> bbObjWorkers
    bbImporters -- Transforms --> bbDataStructs

    bbUtils -- Provides --> bbUserFinder["UserFinder"]:::support
    bbUtils -- Provides --> bbRefConverter["RefConverter"]:::support

    bbImporters -- Relies-on --> bbUserFinder
    bbImporters -- Relies-on --> bbRefConverter

    bbStage -- Interacts-with --> bbAdvance["AdvanceStageWorker"]:::support
    bbWorkers -- Interacts-with --> bbAdvance

    bbStage -- Uses --> bbStageMethods["StageMethods concern"]:::support
    bbWorkers -- Uses --> bbStageMethods

    bbImporters -- Uses --> bbParallelScheduling["ParallelScheduling"]:::support
    bbImporters -- Uses --> bbErrorTracking["ErrorTracking"]:::error
    bbErrorTracking -- Used-by --> bbImporters
  end

  %% BITBUCKET SERVER IMPORT LOGIC
  subgraph sgBitbucketServerImport["Bitbucket Server Import - Core Logic"]
    direction TB
    bbsStage[[app/workers/gitlab/bitbucket_server_import/stage]]:::core
    bbsWorkers[[app/workers/gitlab/bitbucket_server_import/]]:::core
    bbsImporters[[lib/gitlab/bitbucket_server_import/importers/]]:::core
    bbsUtils[[lib/gitlab/bitbucket_server_import/]]:::support

    bbsStageG1[[Stages:]]
    bbsStageRepo["ImportRepositoryWorker"]:::core
    bbsStageNotes["ImportNotesWorker"]:::core
    bbsStagePR["ImportPullRequestsWorker"]:::core
    bbsStageLFS["ImportLfsObjectsWorker"]:::core
    bbsStageUsers["ImportUsersWorker"]:::core
    bbsStageFinish["FinishImportWorker"]:::core
    bbsStageG1 --> bbsStageRepo
    bbsStageG1 --> bbsStageNotes
    bbsStageG1 --> bbsStagePR
    bbsStageG1 --> bbsStageLFS
    bbsStageG1 --> bbsStageUsers
    bbsStageG1 --> bbsStageFinish

    bbsStage -- Drives --> bbsStageG1
    bbsStage -- Spawns --> bbsWorkers
    bbsWorkers -- Orchestrates --> bbsImporters
    bbsImporters -- Relies-on --> bbsUtils

    bbsUtils -- Provides --> bbsLogger["Logger"]:::support
    bbsUtils -- Provides --> bbsLoggable["Loggable"]:::support

    bbsImporters -- Uses --> bbsLogger
    bbsImporters -- Uses --> bbsLoggable

    bbsStage -- Uses --> bbsStageMethods["StageMethods concern"]:::support
    bbsWorkers -- Uses --> bbsStageMethods

    bbsImporters -- Uses --> bbsParallelScheduling["ParallelScheduling BitbucketImport"]:::support
    bbsImporters -- Uses --> bbsErrorTracking["ErrorTracking BitbucketImport"]:::error
  end

  %% JIRA IMPORT LOGIC
  subgraph sgJiraImport["Jira Import - Core Logic"]
    direction TB
    jiraStage[[app/workers/gitlab/jira_import/stage]]:::core
    jiraWorkers[[app/workers/gitlab/jira_import/]]:::core
    jiraImporters[[lib/gitlab/jira_import/]]:::core
    jiraDataStructs[
      "Jira Data Structures":::data
      jiraIssue["Issue"]:::data
      jiraMeta["Metadata"]:::data
      jiraLabels["Labels"]:::data
      jiraUsers["Users"]:::data
      jiraIssueMapping["IssueMappings"]:::data
    ]
    jiraUtils[[lib/gitlab/jira_import/]]:::support

    jiraStageG1[[Stages:]]
    jiraStageStart["StartImportWorker"]:::core
    jiraStageAttachments["ImportAttachmentsWorker"]:::core
    jiraStageIssues["ImportIssuesWorker"]:::core
    jiraStageNotes["ImportNotesWorker"]:::core
    jiraStageLabels["ImportLabelsWorker"]:::core
    jiraStageFinish["FinishImportWorker"]:::core
    jiraStageG1 --> jiraStageStart
    jiraStageG1 --> jiraStageAttachments
    jiraStageG1 --> jiraStageIssues
    jiraStageG1 --> jiraStageNotes
    jiraStageG1 --> jiraStageLabels
    jiraStageG1 --> jiraStageFinish

    jiraStage -- Drives --> jiraStageG1
    jiraStage -- Spawns --> jiraWorkers
    jiraWorkers -- Orchestrates --> jiraImporters
    jiraImporters -- Uses --> jiraDataStructs
    jiraImporters -- Relies-on --> jiraUtils

    jiraObjWorkers[
      "Jira Object Import Workers":::core
      jiraWkImportIssue["ImportIssueWorker"]:::core
    ]

    jiraWkImportIssue -- Deserializes --> jiraIssue

    jiraStage -- Uses --> jiraObjWorkers
    jiraImporters -- Relies-on --> jiraObjWorkers
    jiraImporters -- Transforms --> jiraDataStructs

    jiraUtils -- Provides --> jiraHandleLabels["HandleLabelsService"]:::support
    jiraUtils -- Provides --> jiraIssueSerializer["IssueSerializer"]:::support
    jiraUtils -- Provides --> jiraBaseImporter["BaseImporter"]:::support

    jiraImporters -- Uses --> jiraHandleLabels
    jiraImporters -- Uses --> jiraIssueSerializer
    jiraImporters -- Uses --> jiraBaseImporter

    jiraStage -- Uses --> jiraAdvance["AdvanceStageWorker"]:::support
    jiraWorkers -- Uses --> jiraAdvance
    jiraStage -- Uses --> jiraImportWorker["ImportWorker concern"]:::support
    jiraWorkers -- Uses --> jiraImportWorker
    jiraWorkers -- Uses --> jiraQueueOptions["QueueOptions"]:::support
    jiraImporters -- Relies-on --> jiraQueueOptions
  end

  %% DOMAIN INITIALIZATION & SUPPORTING FILES
  subgraph sgInitSupport["Domain Initialization, Serializers & Support"]
    direction TB
    subgraph sgSerializers["Serializers & Entities"]
      direction TB
      ghEntity["github_realtime_repo_entity.rb"]:::data
      ghSerializer["github_realtime_repo_serializer.rb"]:::data
      ghSerializer -- Wraps --> ghEntity
    end
    reassignPlaceholder["reassign_placeholder_user_records_worker.rb"]:::init
    bulkImportAttr["bulk_imports/source_users_attributes_worker.rb"]:::init
    bulkImportPurge["bulk_imports/configuration_purge_worker.rb"]:::init
    reassignPlaceholder -- Coordinates --> bulkImportAttr
    bulkImportAttr -- Purges --> bulkImportPurge
  end

  %% CONTROLLER GROUP ENTRYPOINT FOR IMPORTS
  subgraph sgControllerGroup["Controllers/Endpoints"]
    direction TB
    ctrlGithub["github_controller.rb\nGitHub Import UI/API"]:::core
    ctrlGithubGroups["github_groups_controller.rb"]:::core
    ctrlGitea["gitea_controller.rb"]:::core
    ctrlBitbucket["bitbucket_controller.rb"]:::core
    ctrlBitbucketServer["bitbucket_server_controller.rb"]:::core
    ctrlGitlabGroup["gitlab_groups_controller.rb"]:::core
    ctrlGithub -- Manages --> ctrlGithubGroups
    ctrlGithubGroups -- Manages --> ctrlGitlabGroup
    ctrlGitea -- Extends --> ctrlGithub
    ctrlBitbucket -- Drives --> bbStage
    ctrlBitbucketServer -- Drives --> bbsStage
    ctrlGithub -- Drives --> ghStage
  end

  %% SERVICES FOR EXTENSIBILITY LOGIC
  subgraph sgExtServices["Import Core Services"]
    direction TB
    ghSvc["import/github_service.rb"]:::core
    ghSvcCancel["import/github/cancel_project_import_service.rb"]:::core
    ghGistsImport["import/github/gists_import_service.rb"]:::core
    bbServerSvc["import/bitbucket_server_service.rb"]:::core
    gitlabProjCreate["import/gitlab_projects/create_project_service.rb"]:::core
    gitlabProjFile["import/gitlab_projects/file_acquisition_strategies/file_upload.rb"]:::support
    ghSvc -- Cancels --> ghSvcCancel
    ghSvc -- Schedules Gists Import --> ghGistsImport
    bbServerSvc -- Creates/Manages Projects --> gitlabProjCreate
    gitlabProjCreate -- Handles File Upload --> gitlabProjFile
  end

  %% CROSS-SYSTEM DOMAIN PATTERNS - MIXINS/CONCERNS
  subgraph sgWorkersConcerns["Worker Concerns & Mixins"]
    direction TB
    bitbucketStageMethods["bitbucket_import/stage_methods.rb"]:::support
    bitbucketServerStageMethods["bitbucket_server_import/stage_methods.rb"]:::support
    githubStageMethods["github_import/stage_methods.rb"]:::support
    githubQueue["github_import/queue.rb"]:::support
    jiraImportWorker["jira_import/import_worker.rb"]:::support
    bitbucketStageMethods -- Used-by --> bbStage
    bitbucketServerStageMethods -- Used-by --> bbsStage
    githubStageMethods -- Used-by --> ghStage
    githubQueue -- Used-by --> ghWorkers
    jiraImportWorker -- Used-by --> jiraWorkers
  end
  
  %% COLLABORATIONS & FLOW BETWEEN MAIN GROUPINGS
  sgControllerGroup -- Schedules Import via Service --> sgExtServices
  sgExtServices -- Schedules Jobs for Importers --> sgGithubImport
  sgExtServices -- Schedules Jobs for Importers --> sgBitbucketImport
  sgExtServices -- Schedules Jobs for Importers --> sgJiraImport
  sgExtServices -- Schedules Jobs for Importers --> sgBitbucketServerImport

  %% GITHUB AND BITBUCKET IMPORTERS INTERCONNECTED FOR ADVANCE STAGE
  ghStage -- Advances-via --> ghAdvanceWorker["AdvanceStageWorker"]:::support
  bbStage -- Advances-via --> bbAdvanceWorker["AdvanceStageWorker"]:::support
  jiraStage -- Advances-via --> jiraAdvanceWorker["AdvanceStageWorker"]:::support
  bbsStage -- Advances-via --> bbsAdvanceWorker["AdvanceStageWorker"]:::support

  %% DATA STRUCTURE AGGREGATION
  ghRepresent -- Provides --> ghDataStructs
  bbImporters -- Provides --> bbDataStructs
  jiraImporters -- Provides --> jiraDataStructs
  bbsImporters -- Provides --> bbDataStructs

  %% ERROR HANDLING (SHOW LOGICAL FLOW, RED EDGES)
  classDef errorLink stroke:#E57373,stroke-width:3px
  ghUtils -- ErrorStats --> ghObjectCounter:::error
  bbImporters -- TracksFailVia --> bbErrorTracking:::error
  bbsImporters -- TracksFailVia --> bbsErrorTracking:::error

  %% GROUP STYLES
  class sgGithubImport group
  class sgBitbucketImport group
  class sgJiraImport group
  class sgBitbucketServerImport group
  class sgInitSupport group
  class sgControllerGroup group
  class sgExtServices group
  class sgWorkersConcerns group

  %% LABELS FOR KEY LOGICAL GROUPINGS
  linkStyle 18 stroke:#b6a9d3,stroke-width:2px
  linkStyle 23 stroke:#b6a9d3,stroke-width:2px
  linkStyle 28 stroke:#b6a9d3,stroke-width:2px
  linkStyle 33 stroke:#b6a9d3,stroke-width:2px
```