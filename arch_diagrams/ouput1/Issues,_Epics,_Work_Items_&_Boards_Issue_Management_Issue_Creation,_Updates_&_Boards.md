```mermaid
flowchart TD
  %% Layout Direction
  direction TB

  %% Color Palette
  classDef core fill:#D4F1F9,stroke:#AEE4F9,stroke-width:2px,color:#222,stroke-dasharray:5 5,rx:18,ry:18;
  classDef utility fill:#FFF8DC,stroke:#F8E7A2,stroke-width:2px,color:#444,rx:18,ry:18;
  classDef structure fill:#E0F8E0,stroke:#AFF7B8,stroke-width:2px,color:#333,rx:18,ry:18;
  classDef error fill:#FFE4E1,stroke:#FFBCB3,stroke-width:2px,color:#A05151,rx:18,ry:18;
  classDef init fill:#E6E6FA,stroke:#B8AEE0,stroke-width:2px,color:#3a3269,rx:18,ry:18;
  classDef group fill:#F8F8F8,stroke:#AEE4F9,stroke-width:2px,rx:22,ry:18;

  %% --- DOMAIN ROOT: ISSUES, EPICS, WORK ITEMS & BOARDS ---
  subgraph Domain_Issues_Epics_WorkItems_Boards["Issues, Epics, Work Items & Boards/Issue Management/Issue Creation, Updates & Boards"]
  class Domain_Issues_Epics_WorkItems_Boards group

    %% --- DATA STRUCTURES ---
    subgraph Data_Structures["Domain Data Structures"]
      class Data_Structures group

      structure_issue["Issue\napp/models/issue.rb"]
      structure_issue_metrics["Issue::Metrics\napp/models/issue/metrics.rb"]
      structure_timelog["Timelog\napp/models/timelog.rb"]
      structure_issue_email["Issue::Email\napp/models/issue/email.rb"]
      structure_issues_csv_import["Issues::CsvImport\napp/models/issues/csv_import.rb"]
    end
    class structure_issue,structure_issue_metrics,structure_timelog,structure_issue_email,structure_issues_csv_import structure

    %% Issue relations
    structure_issue_metrics --> structure_issue
    structure_timelog --> structure_issue
    structure_issue_email --> structure_issue
    structure_issues_csv_import --> structure_issue

    %% --- DOMAIN POLICIES & AUTHORIZATION ---
    subgraph Policies_Auth["Policies & Authorization"]
      class Policies_Auth group

      core_issue_policy["IssuePolicy\napp/policies/issue_policy.rb"]
      core_issuable_policy["IssuablePolicy\napp/policies/issuable_policy.rb"]
    end
    class core_issue_policy,core_issuable_policy core

    structure_issue --> core_issue_policy
    core_issue_policy --> core_issuable_policy

    %% --- FINDERS & FILTERS ---
    subgraph Finders_Filters["Finders, Filters & Query Abstractions"]
      class Finders_Filters group

      core_issuable_finder["IssuableFinder\napp/finders/issuable_finder.rb"]
      core_issues_finder_params["IssuesFinder::Params\napp/finders/issues_finder/params.rb"]
      core_issuables_base_filter["Issuables::BaseFilter\napp/finders/issuables/base_filter.rb"]
      utility_author_filter["AuthorFilter\napp/finders/issuables/author_filter.rb"]
      utility_assignee_filter["AssigneeFilter\napp/finders/issuables/assignee_filter.rb"]
      utility_crm_org_filter["CrmOrganizationFilter\napp/finders/issuables/crm_organization_filter.rb"]
      utility_crm_contact_filter["CrmContactFilter\napp/finders/issuables/crm_contact_filter.rb"]
      core_issues_confidentiality_filter["Issues::ConfidentialityFilter\napp/finders/issues/confidentiality_filter.rb"]
    end
    class core_issuable_finder,core_issues_finder_params,core_issuables_base_filter,core_issues_confidentiality_filter core
    class utility_author_filter,utility_assignee_filter,utility_crm_org_filter,utility_crm_contact_filter utility

    core_issuable_finder --> core_issues_finder_params
    core_issues_finder_params --> structure_issue
    core_issuable_finder --> core_issuables_base_filter
    core_issuables_base_filter -->|is parent class of| utility_author_filter
    core_issuables_base_filter --> utility_assignee_filter
    core_issuables_base_filter --> utility_crm_org_filter
    core_issuables_base_filter --> utility_crm_contact_filter
    core_isuable_finder --> core_issues_confidentiality_filter
    core_issues_confidentiality_filter --> core_issuables_base_filter

    %% --- DOMAIN SERVICES (CRUD, BULK, ETC.) ---
    subgraph Issue_Domain_Services["Domain Services"]
      class Issue_Domain_Services group

      core_issues_base_service["Issues::BaseService\napp/services/issues/base_service.rb"]
      core_issues_create_service["Issues::CreateService\napp/services/issues/create_service.rb"]
      core_issues_update_service["Issues::UpdateService\napp/services/issues/update_service.rb"]
      core_issues_build_service["Issues::BuildService\napp/services/issues/build_service.rb"]
      core_issues_export_csv_service["Issues::ExportCsvService\napp/services/issues/export_csv_service.rb"]
      core_issues_prepare_import_csv_service["Issues::PrepareImportCsvService\napp/services/issues/prepare_import_csv_service.rb"]
      core_issues_import_csv_service["Issues::ImportCsvService\napp/services/issues/import_csv_service.rb"]
      core_issues_after_create_service["Issues::AfterCreateService\napp/services/issues/after_create_service.rb"]
      core_issues_reopen_service["Issues::ReopenService\napp/services/issues/reopen_service.rb"]
      core_issues_move_service["Issues::MoveService\napp/services/issues/move_service.rb"]
      core_issues_clone_service["Issues::CloneService\napp/services/issues/clone_service.rb"]
      core_issues_relative_position_rebalancing_service["Issues::RelativePositionRebalancingService\napp/services/issues/relative_position_rebalancing_service.rb"]

      core_task_list_toggle_service["TaskListToggleService\napp/services/task_list_toggle_service.rb"]
      utility_bulk_update_service["Issuable::BulkUpdateService\napp/services/issuable/bulk_update_service.rb"]
      utility_clone_base_service["Issuable::Clone::BaseService\napp/services/issuable/clone/base_service.rb"]
      utility_import_csv_base_service["Issuable::ImportCsv::BaseService\napp/services/issuable/import_csv/base_service.rb"]

      utility_issue_email_participants_destroy_service["IssueEmailParticipants::DestroyService\napp/services/issue_email_participants/destroy_service.rb"]
      core_issues_create_from_vulnerability_service["Issues::CreateFromVulnerabilityService\nee/app/services/issues/create_from_vulnerability_service.rb"]
    end

    class core_issues_base_service,core_issues_create_service,core_issues_update_service,core_issues_build_service,core_issues_export_csv_service,core_issues_prepare_import_csv_service,core_issues_import_csv_service,core_issues_after_create_service,core_issues_reopen_service,core_issues_move_service,core_issues_clone_service,core_issues_relative_position_rebalancing_service,core_issues_create_from_vulnerability_service core
    class utility_bulk_update_service,utility_clone_base_service,utility_import_csv_base_service,utility_issue_email_participants_destroy_service utility
    class core_task_list_toggle_service utility

    %% Service relates to models
    core_issues_base_service --> structure_issue
    core_issues_create_service --> core_issues_base_service
    core_issues_update_service --> core_issues_base_service
    core_issues_build_service --> core_issues_base_service
    core_issues_export_csv_service --> structure_issue
    core_issues_import_csv_service --> utility_import_csv_base_service
    core_issues_prepare_import_csv_service --> core_issues_import_csv_service
    core_issues_move_service --> utility_clone_base_service
    core_issues_clone_service --> utility_clone_base_service
    utility_clone_base_service --> structure_issue
    core_issues_after_create_service --> core_issues_base_service
    core_issues_reopen_service --> core_issues_base_service
    utility_bulk_update_service --> structure_issue
    core_issues_relative_position_rebalancing_service --> structure_issue
    utility_issue_email_participants_destroy_service --> structure_issue
    core_task_list_toggle_service --> core_issues_update_service

    % Specialized service (from EE)
    core_issues_create_from_vulnerability_service --> structure_issue

    %% --- SERIALIZERS & PRESENTERS (API Data Representations) ---
    subgraph Serializers_Presenters["Serializers & Presenters"]
      class Serializers_Presenters group

      core_analytics_issue_serializer["AnalyticsIssueSerializer\napp/serializers/analytics_issue_serializer.rb"]
      core_issue_entity["IssueEntity\napp/serializers/issue_entity.rb"]
      core_issue_sidebar_entity["IssueSidebarBasicEntity\napp/serializers/issue_sidebar_basic_entity.rb"]
      utility_issue_email_presenter["IssueEmailParticipantPresenter\napp/presenters/issue_email_participant_presenter.rb"]
    end
    class core_analytics_issue_serializer,core_issue_entity,core_issue_sidebar_entity core
    class utility_issue_email_presenter utility

    structure_issue -->|Serializes| core_issue_entity
    structure_issue -->|Serializes| core_issue_sidebar_entity
    structure_issue -->|Analytics| core_analytics_issue_serializer
    structure_issue_email --> utility_issue_email_presenter

    %% --- CONTROLLERS, HELPERS, UI ENTRYPOINTS ---
    subgraph Controller_UI["Controllers, Helpers & UI Entrypoints"]
      class Controller_UI group

      core_projects_issues_controller["Projects::IssuesController\napp/controllers/projects/issues_controller.rb"]
      utility_issuable_collections_action["IssuableCollectionsAction\napp/controllers/concerns/issuable_collections_action.rb"]
      utility_projects_issues_helper["Projects::IssuesHelper\napp/helpers/projects/issues_helper.rb"]
    end
    class core_projects_issues_controller core
    class utility_issuable_collections_action,utility_projects_issues_helper utility

    core_projects_issues_controller -- renders/manages --> structure_issue
    core_projects_issues_controller -.uses.-> core_issues_create_service
    core_projects_issues_controller -.uses.-> core_issues_update_service
    core_projects_issues_controller -.lists.-> core_issuable_finder
    core_projects_issues_controller --> utility_projects_issues_helper
    core_projects_issues_controller --> utility_issuable_collections_action
    utility_issuable_collections_action --> structure_issue

    %% --- INITIALIZATION / WORKERS / ASYNC PROCESSES ---
    subgraph Workers_Initialization["Workers, Initialization, Async Processing"]
      class Workers_Initialization group

      init_new_issue_worker["NewIssueWorker\napp/workers/new_issue_worker.rb"]
      init_issues_placement_worker["Issues::PlacementWorker\napp/workers/issues/placement_worker.rb"]
      init_issues_close_worker["Issues::CloseWorker\napp/workers/issues/close_worker.rb"]
      init_issue_due_scheduler_worker["IssueDueSchedulerWorker\napp/workers/issue_due_scheduler_worker.rb"]
    end
    class init_new_issue_worker,init_issues_placement_worker,init_issues_close_worker,init_issue_due_scheduler_worker init

    init_new_issue_worker --> core_issues_create_service
    init_issues_placement_worker --> structure_issue
    init_issues_close_worker --> core_issues_update_service
    init_issue_due_scheduler_worker --> structure_issue

    %% --- GRAPHQL, MUTATIONS & API EXTENSIONS ---
    subgraph API_Mutations_GraphQL["GraphQL Mutations & API Extensions"]
      class API_Mutations_GraphQL group

      core_set_assignees["Mutations::Issues::SetAssignees\napp/graphql/mutations/issues/set_assignees.rb"]
    end
    class core_set_assignees core

    core_set_assignees --> structure_issue
    core_set_assignees --> core_issues_update_service

    %% --- DOMAIN CONCERNS/MODULES/UTILITIES ---
    subgraph Utilities_Concerns["Utilities, Concerns & Domain Logic"]
      class Utilities_Concerns group

      utility_from_intersect["FromIntersect\napp/models/concerns/from_intersect.rb"]
      utility_from_except["FromExcept\napp/models/concerns/from_except.rb"]
      utility_quick_actions["SpendTimeAndDateSeparator\nlib/gitlab/quick_actions/spend_time_and_date_separator.rb"]
      utility_recent_issues["RecentIssues\nlib/gitlab/search/recent_issues.rb"]
      utility_clone_attributes_rewriter["Clone::AttributesRewriter\nlib/gitlab/issuable/clone/attributes_rewriter.rb"]
    end
    class utility_from_intersect,utility_from_except,utility_quick_actions,utility_recent_issues,utility_clone_attributes_rewriter utility

    core_issues_base_service -.uses.-> utility_from_intersect
    core_issues_base_service -.uses.-> utility_from_except
    utility_bulk_update_service -.uses.-> utility_from_intersect
    core_issues_build_service -.transforms.-> utility_clone_attributes_rewriter
    structure_timelog --> utility_quick_actions
    structure_issue --> utility_recent_issues

    %% --- DOMAIN EXTENSIONS: EE (Epics, Boards, Weights, Health, etc.) ---
    subgraph EE_Extensions["EE Extensions: Epics, Boards, Health, Iterations"]
      class EE_Extensions group

      structure_ee_issue["EE::Issue\nee/app/models/ee/issue.rb"]
      core_ee_issues_finder["EE::IssuesFinder\nee/app/finders/ee/issues_finder.rb"]
      core_ee_issues_finder_params["EE::IssuesFinder::Params\nee/app/finders/ee/issues_finder/params.rb"]
      core_ee_issues_update_service["EE::Issues::UpdateService\nee/app/services/ee/issues/update_service.rb"]
      core_ee_issues_after_create_service["EE::Issues::AfterCreateService\nee/app/services/ee/issues/after_create_service.rb"]
      core_ee_issues_reopen_service["EE::Issues::ReopenService\nee/app/services/ee/issues/reopen_service.rb"]
      core_ee_issues_close_service["EE::Issues::CloseService\nee/app/services/ee/issues/close_service.rb"]
      core_ee_issues_build_service["EE::Issues::BuildService\nee/app/services/ee/issues/build_service.rb"]
      core_ee_issues_move_service["EE::Issues::MoveService\nee/app/services/ee/issues/move_service.rb"]
      core_ee_issues_clone_service["EE::Issues::CloneService\nee/app/services/ee/issues/clone_service.rb"]
      core_ee_issues_base_service["EE::Issues::BaseService\nee/app/services/ee/issues/base_service.rb"]
    end
    class structure_ee_issue,core_ee_issues_finder,core_ee_issues_finder_params,core_ee_issues_update_service,core_ee_issues_after_create_service,core_ee_issues_reopen_service,core_ee_issues_close_service,core_ee_issues_build_service,core_ee_issues_move_service,core_ee_issues_clone_service,core_ee_issues_base_service core

    structure_ee_issue --> structure_issue

    core_ee_issues_finder --> core_issuable_finder
    core_ee_issues_finder_params --> core_issues_finder_params
    core_ee_issues_update_service --> core_issues_update_service
    core_ee_issues_after_create_service --> core_issues_after_create_service
    core_ee_issues_reopen_service --> core_issues_reopen_service
    core_ee_issues_close_service --> core_issues_update_service
    core_ee_issues_build_service --> core_issues_build_service
    core_ee_issues_move_service --> core_issues_move_service
    core_ee_issues_clone_service --> core_issues_clone_service
    core_ee_issues_base_service --> core_issues_base_service

    %% --- EE GraphQL Mutations & Arguments ---
    subgraph EE_GraphQL_Mutations["EE GraphQL & Mutations"]
      class EE_GraphQL_Mutations group

      core_ee_mutations_create["EE::Mutations::Issues::Create\nee/app/graphql/ee/mutations/issues/create.rb"]
      core_ee_mutations_update["EE::Mutations::Issues::Update\nee/app/graphql/ee/mutations/issues/update.rb"]
      core_ee_mutations_bulk_update["EE::Mutations::Issues::BulkUpdate\nee/app/graphql/ee/mutations/issues/bulk_update.rb"]
      utility_common_ee_mutation_args["Mutations::Issues::CommonEEMutationArguments\nee/app/graphql/mutations/issues/common_ee_mutation_arguments.rb"]
      core_ee_mutations_set_weight["Mutations::Issues::SetWeight\nee/app/graphql/mutations/issues/set_weight.rb"]
    end
    class core_ee_mutations_create,core_ee_mutations_update,core_ee_mutations_bulk_update,core_ee_mutations_set_weight core
    class utility_common_ee_mutation_args utility

    core_ee_mutations_create --extends--> core_issues_create_service
    core_ee_mutations_update --> core_issues_update_service
    core_ee_mutations_bulk_update --> utility_bulk_update_service
    core_ee_mutations_create --> utility_common_ee_mutation_args
    core_ee_mutations_update --> utility_common_ee_mutation_args
    core_ee_mutations_bulk_update --> utility_common_ee_mutation_args
    core_ee_mutations_set_weight --> structure_ee_issue

    %% --- EE Controllers & Helpers ---
    subgraph EE_Controllers_Helpers["EE Controllers & Helpers"]
      class EE_Controllers_Helpers group

      utility_ee_issuable_actions["EE::IssuableActions\nee/app/controllers/concerns/ee/issuable_actions.rb"]
      utility_ee_issuable_collections["EE::IssuableCollections\nee/app/controllers/concerns/ee/issuable_collections.rb"]
      core_ee_projects_issues_controller["EE::Projects::IssuesController\nee/app/controllers/ee/projects/issues_controller.rb"]
      utility_ee_issuables_helper["EE::IssuablesHelper\nee/app/helpers/ee/issuables_helper.rb"]
      utility_ee_issues_helper["EE::IssuesHelper\nee/app/helpers/ee/issues_helper.rb"]
    end
    class utility_ee_issuable_actions,utility_ee_issuable_collections,core_ee_projects_issues_controller utility
    class utility_ee_issuables_helper,utility_ee_issues_helper utility

    core_ee_projects_issues_controller --> core_projects_issues_controller
    utility_ee_issuable_actions --> core_projects_issues_controller
    utility_ee_issuable_collections --> utility_issuable_collections_action
    utility_ee_issuables_helper --> utility_projects_issues_helper
    utility_ee_issues_helper --> utility_projects_issues_helper
    utility_ee_issues_helper --> structure_ee_issue

    %% --- EE SERIALIZERS / API / SEARCH ---
    subgraph EE_APIs_Serializers["EE API, Serializers & Search"]
      class EE_APIs_Serializers group

      core_gitlab_insights_finders_issuable_finder["Gitlab::Insights::Finders::IssuableFinder\nee/lib/gitlab/insights/finders/issuable_finder.rb"]
      core_elastic_issue_class_proxy["Elastic::Latest::IssueClassProxy\nee/lib/elastic/latest/issue_class_proxy.rb"]
      core_ee_api_entities_issue_basic["EE::API::Entities::IssueBasic\nee/lib/ee/api/entities/issue_basic.rb"]
    end
    class core_gitlab_insights_finders_issuable_finder, core_elastic_issue_class_proxy, core_ee_api_entities_issue_basic core

    core_gitlab_insights_finders_issuable_finder --> structure_ee_issue
    core_elastic_issue_class_proxy --> structure_ee_issue
    core_ee_api_entities_issue_basic --> structure_ee_issue

    %% --- EMAIL HANDLING CREATION ---
    subgraph Email_Handlers["Email Handlers"]
      class Email_Handlers group
      utility_create_issue_handler["Email::CreateIssueHandler\nlib/gitlab/email/handler/create_issue_handler.rb"]
    end
    class utility_create_issue_handler utility

    utility_create_issue_handler --> core_issues_create_service
    utility_create_issue_handler --> structure_issue

    %% --- IMPORTS / EXPORTS / BULK ---
    subgraph Import_Export["Bulk Imports, Export Pipelines"]
      class Import_Export group

      utility_issues_pipeline["BulkImports::Projects::Pipelines::IssuesPipeline\nlib/bulk_imports/projects/pipelines/issues_pipeline.rb"]
    end
    class utility_issues_pipeline utility

    utility_issues_pipeline --> structure_issue
    utility_issues_pipeline --> core_issues_import_csv_service

    %% --- TESTING / QA / AUTOMATION ---
    subgraph QA_Features["QA / QA-Mobile Automation"]
      class QA_Features group

      utility_qa_issue_new["QA::Page::Issuable::New\nqa/qa/page/issuable/new.rb"]
      utility_qa_mobile_issue_show["QA::Mobile::Page::Project::Issue::Show\nqa/qa/mobile/page/project/issue/show.rb"]
      utility_qa_issue_index["QA::Page::Project::Issue::Index\nqa/qa/page/project/issue/index.rb"]
      utility_qa_issue_new_project["QA::Page::Project::Issue::New\nqa/qa/page/project/issue/new.rb"]
    end
    class utility_qa_issue_new,utility_qa_mobile_issue_show,utility_qa_issue_index,utility_qa_issue_new_project utility

    utility_qa_issue_new --> structure_issue
    utility_qa_issue_new_project --> core_projects_issues_controller
    utility_qa_issue_index --> structure_issue
    utility_qa_issue_index --> core_projects_issues_controller
    utility_qa_mobile_issue_show --> structure_issue

    %% --- VIEW / RENDERING / TEST HELPERS ---
    subgraph Rendering_TestHelpers["View Rendering & Test Helpers"]
      class Rendering_TestHelpers group

      utility_view_issue_partial_spec["Issue HAML Spec\nspec/views/projects/_issue.html.haml_spec.rb"]
    end
    class utility_view_issue_partial_spec utility

    utility_view_issue_partial_spec --> structure_issue
    utility_view_issue_partial_spec --> utility_projects_issues_helper

  end

  %% Key Domain Flows
  structure_issue -.is queried via.-> core_issuable_finder
  core_issuable_finder -.drives.-> core_projects_issues_controller
  structure_issue -->|Created/Mutated by| core_issues_create_service
  structure_issue -->|Updated by| core_issues_update_service
  structure_issue -->|Cloned or Moved by| core_issues_clone_service
  structure_issue -->|Bulk updated by| utility_bulk_update_service
  structure_issue -->|Displayed by| core_issue_entity
  structure_issue -->|Exported by| core_issues_export_csv_service
  structure_issue -->|Imported by| core_issues_import_csv_service
  core_issues_import_csv_service --> utility_import_csv_base_service
  structure_issue -->|Authorization| core_issue_policy

  %% Domain Abstractions, Patterns
  core_issues_base_service -.inherits from.-> utility_clone_base_service
  utility_clone_base_service -.uses.-> utility_clone_attributes_rewriter
  core_issues_create_service -.uses.-> core_issues_build_service
  core_issues_update_service -.uses.-> core_issues_build_service

  %% Important Data Transformations
  core_issues_update_service -.updates.-> structure_issue_metrics
  core_issues_build_service -.initializes.-> structure_issue
  core_issues_prepare_import_csv_service -.schedules.-> core_issues_import_csv_service
```