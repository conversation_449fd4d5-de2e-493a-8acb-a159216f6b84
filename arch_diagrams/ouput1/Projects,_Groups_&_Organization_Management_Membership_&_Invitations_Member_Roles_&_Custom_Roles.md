```mermaid
flowchart TD
%% ================= CONFIGURE COLORS AND STYLES ==================
%% Pastel Blue: Core domain files      #D4F1F9
%% Pastel Yellow: Supporting Utilities #FFF8DC
%% Pastel Green: Data Structures       #E0F8E0
%% Pastel Red: Error Handling          #FFE4E1
%% Pastel Purple: Initialization/Setup #E6E6FA
%% Subgraphs: Very Light Gray          #F8F8F8

%% Core domain files (rounded rectangles, pastel blue)
classDef core fill:#D4F1F9,stroke:#BEE4EF,stroke-width:2px,color:#283546,rx:10,ry:10
%% Supporting/utility files (rounded rectangles, pastel yellow)
classDef util fill:#FFF8DC,stroke:#F7ECAB,stroke-width:2px,color:#4B4A29,rx:10,ry:10
%% Data structure files (rounded rectangles, pastel green)
classDef data fill:#E0F8E0,stroke:#B6EABA,stroke-width:2px,color:#255528,rx:10,ry:10
%% Error handling files (rounded rectangles, pastel red)
classDef error fill:#FFE4E1,stroke:#FFC6BC,stroke-width:2px,color:#621820,rx:10,ry:10
%% Initialization files (rounded rectangles, pastel purple)
classDef init fill:#E6E6FA,stroke:#CBCBE1,stroke-width:2px,color:#363353,rx:10,ry:10
%% Subgraph group
classDef subgraph fill:#F8F8F8,stroke-width:3px,stroke-dasharray:0 0

%% ==== SUBGRAPH: CORE DOMAIN AGGREGATES & DATA ENTITIES ====
subgraph Core Entities & Roles
class CoreEntities subgraph
direction TB

    member[Member Model\napp/models/member.rb]
    memberps[Members Preloader\napp/models/members_preloader.rb]
    mrrole[Custom Member Role\nee/app/models/members/member_role.rb]
    arrole[Admin Role Assignment\nee/app/models/authz/admin_role.rb]
    baserole[Base Role\nee/app/models/authz/base_role.rb]
    adm[Admin Role\nee/app/models/authz/admin.rb]
    approver[Approver Approval Rules\n(ee/app/models/approver.rb)]
    amrrule[Approval Merge Request Rule\nee/app/models/approval_merge_request_rule.rb]

    memberroleability[Member Role Ability Loader\nee/app/models/auth/member_role_ability_loader.rb]

    memberswithparents[Members With Parents\nee/app/models/ee/members/members_with_parents.rb]

    arrole --> baserole
    mrrole --> baserole
    adm --> baserole

    member --> memberps
    member --> mrrole
    member --> approver
    member --> amrrule
    member --> memberswithparents
    member --> memberroleability

    mrrole -->|Defines/Associates| arrole
    mrrole -->|Subclasses| baserole

class member,memberps,mrrole,arrole,baserole,adm,approver,amrrule,memberroleability,memberswithparents core
end

%% ==== SUBGRAPH: MEMBERSHIP MANAGEMENT LOGIC ====
subgraph Membership Logic & Actions
class MembershipLogic subgraph
direction TB

    membershipact[Membership Actions\napp/controllers/concerns/membership_actions.rb]
    eemembershipact[EE Membership Actions\nee/app/controllers/concerns/ee/membership_actions.rb]
    memberscreator[Member Creator Service\napp/services/members/creator_service.rb]
    membersimport[Import Project Team Service\nee/app/services/ee/members/import_project_team_service.rb]
    removeexpired[Remove Expired Members Worker\napp/workers/remove_expired_members_worker.rb]
    userassignt[User Assignment to Member Roles Service\nee/app/services/users/member_roles/assign_service.rb]
    dataBuilderMA[Member Approval Data Builder\nee/lib/gitlab/data_builder/member_approval_builder.rb]
    promoUtils[Promotion Management Utils\nee/lib/gitlab_subscriptions/member_management/promotion_management_utils.rb]

    membershipact --> memberscreator
    membershipact --|Customized by|--> eemembershipact

    memberscreator --> member
    membersimport --> member
    removeexpired --> member
    userassignt --> mrrole
    userassignt -->|Assigns admin roles| arrole
    dataBuilderMA --> member
    promoUtils --> mrrole
    promoUtils --> member

class membershipact,eemembershipact,memberscreator,membersimport,removeexpired,userassignt,dataBuilderMA,promoUtils core
end

%% ==== SUBGRAPH: DOMAIN SERVICES: MEMBER ROLE LIFECYCLE ====
subgraph Member Role Services & Business Rules
class MemberServices subgraph
direction TB

    mrcsvc[Create Member Role Service\nee/app/services/member_roles/create_service.rb]
    mruSvc[Update Member Role Service\nee/app/services/member_roles/update_service.rb]
    mrdSvc[Delete Member Role Service\nee/app/services/member_roles/delete_service.rb]
    crsvc[Custom Roles CreateServiceable\nee/app/services/concerns/authz/custom_roles/create_serviceable.rb]
    mrolehlp[Member Roles Helper\nee/app/helpers/member_roles_helper.rb]

    authzrAdmC[Admin Role Create Service\nauthz/admin_roles/create_service.rb]
    ladapAdmC[LDAP Admin Role Links Create Service\nauthz/ldap_admin_role_links/create_service.rb]

    mrcsvc --> crsvc
    mruSvc --> crsvc
    mrcsvc --> mrrole
    mruSvc --> mrrole
    mrdSvc --> mrrole
    mrcsvc --> authzrAdmC
    mrcsvc --> ladapAdmC
    mrdSvc -->|Handles dependency checks| mruSvc
    mrolehlp --> mrrole

class mrcsvc,mruSvc,mrdSvc,crsvc,authzrAdmC,ladapAdmC,mrolehlp core
end

%% ==== SUBGRAPH: PRESENTATION LAYER ====
subgraph Presenters & Serializers
class Presentation subgraph
direction TB

    mempresenter[Member Presenter\napp/presenters/member_presenter.rb]
    grpmempresenter[Group Member Presenter\napp/presenters/group_member_presenter.rb]
    memberApprPresenter[Member Approval Presenter\nee/app/presenters/gitlab_subscriptions/member_management/member_approval_presenter.rb]
    memberEntity[Member Serializer\napp/serializers/member_entity.rb]
    memberUserEnt[Member User Serializer EE\nee/app/serializers/ee/member_user_entity.rb]

    mempresenter --> member
    grpmempresenter --> mempresenter
    memberApprPresenter --> mrcsvc

    memberEntity --> member
    memberUserEnt --> member

class mempresenter,grpmempresenter,memberApprPresenter,memberEntity,memberUserEnt core
end

%% ==== SUBGRAPH: POLICY & PERMISSiONS ====
subgraph Permission & Policy Layer
class Policy subgraph
direction TB

    mrpol[Member Role Policy\nee/app/policies/members/member_role_policy.rb]
    usermrpol[User Member Role Policy\nee/app/policies/users/user_member_role_policy.rb]

    mrrole --> mrpol
    mrrole --> usermrpol
    userassignt --> usermrpol

class mrpol,usermrpol core
end

%% ==== SUBGRAPH: API, GRAPHQL, & FINDERS ====
subgraph API & GraphQL Access Layer
class APIAccess subgraph
direction TB

    apiMemberRoles[API: Member Roles\nee/lib/api/member_roles.rb]
    apiMembersHelpers[API: MembersHelpers\nlib/api/helpers/members_helpers.rb]
    usersfinder[Users Finder\napp/finders/users_finder.rb]
    adminrolesF[Admin Roles Finder\nee/app/finders/members/admin_roles_finder.rb]
    allrolesF[All Roles Finder\nee/app/finders/members/all_roles_finder.rb]
    dkeysf[Deploy Keys Finder\napp/finders/deploy_keys/deploy_keys_finder.rb]

    graphql_mrCreate[GraphQL Mutation: MemberRole Create\nee/app/graphql/mutations/member_roles/create.rb]
    graphql_mrBase[GraphQL Mutation: MemberRole Base\nee/app/graphql/mutations/member_roles/base.rb]
    graphql_mrDelete[GraphQL Mutation: MemberRole Delete\nee/app/graphql/mutations/member_roles/delete.rb]
    graphql_mrAdminCreate[GraphQL Mutation: Admin Role Create\nee/app/graphql/mutations/member_roles/admin/create.rb]
    graphql_mrAdminUpdate[GraphQL Mutation: Admin Role Update\nee/app/graphql/mutations/member_roles/admin/update.rb]
    graphql_mrAdminBase[GraphQL Mutation: Admin Base\nee/app/graphql/mutations/member_roles/admin/base.rb]

    graphql_mrResolver[GraphQL Resolver: Member Roles\nee/app/graphql/resolvers/member_roles/roles_resolver.rb]
    graphql_srResolver[GraphQL Resolver: Standard Roles\nee/app/graphql/resolvers/members/standard_roles_resolver.rb]
    graphql_arResolver[GraphQL Resolver: Admin Roles\nee/app/graphql/resolvers/members/admin_roles_resolver.rb]

    graphql_types_mrOrderBy[GraphQL: MemberRoles OrderByEnum\nee/app/graphql/types/member_roles/order_by_enum.rb]
    graphql_types_accessLevel[GraphQL: MemberAccess Level Name\nee/app/graphql/types/member_access_level_name_enum.rb]
    graphql_types_stdRole[GraphQL: Standard Role Type\nee/app/graphql/types/members/standard_role_type.rb]
    graphql_types_custPerm[GraphQL: Members Customizable Permission\nee/app/graphql/types/members/customizable_permission.rb]
    graphql_types_userMemberR[GraphQL: User MemberRole Type\nee/app/graphql/types/users/user_member_role_type.rb]
    graphql_types_userType[GraphQL: EE UserType\nee/app/graphql/ee/types/user_type.rb]
    graphql_types_mrAccessLvlEnum[GraphQL: EE MemberAccess Level Enum\nee/app/graphql/ee/types/member_access_level_enum.rb]

    apiMemberRoles --> mrrole
    apiMemberRoles --> member
    apiMemberRoles --> apiMembersHelpers
    apiMembersHelpers --> member

    graphql_mrCreate --> graphql_mrBase
    graphql_mrDelete --> graphql_mrBase
    graphql_mrAdminCreate --> graphql_mrAdminBase
    graphql_mrAdminUpdate --> graphql_mrAdminBase

    graphql_mrBase --> mrcsvc
    graphql_mrBase --> mruSvc
    graphql_mrBase --> mrdSvc

    graphql_mrCreate --> mrcsvc
    graphql_mrDelete --> mrdSvc
    graphql_mrAdminCreate --> mrcsvc
    graphql_mrAdminUpdate --> mruSvc

    graphql_mrResolver --> adminrolesF
    graphql_mrResolver --> allrolesF
    graphql_srResolver --> mrrole
    graphql_arResolver --> adminrolesF
    graphql_types_stdRole --> graphql_types_accessLevel
    graphql_types_stdRole --> mrolehlp
    graphql_types_userMemberR --> mrrole

class apiMemberRoles,apiMembersHelpers,graphql_mrCreate,graphql_mrDelete,graphql_mrResolver,graphql_srResolver,graphql_arResolver,usersfinder,adminrolesF,allrolesF,dkeysf,graphql_mrBase,graphql_mrAdminBase,graphql_types_mrOrderBy,graphql_types_accessLevel,graphql_types_stdRole,graphql_types_custPerm,graphql_types_userMemberR,graphql_types_userType,graphql_types_mrAccessLvlEnum,graphql_mrAdminCreate,graphql_mrAdminUpdate util
end

%% ==== SUBGRAPH: DOMAIN EVENTS & AUDIT ====
subgraph Domain Events
class Events subgraph
direction TB

    destroyedEv[Member Destroyed Event\napp/events/members/destroyed_event.rb]
    modifiedByAdmin[Membership Modified By Admin Event\nee/app/events/members/membership_modified_by_admin_event.rb]

    destroyedEv --> member
    modifiedByAdmin --> userassignt

class destroyedEv,modifiedByAdmin core
end

%% ==== SUBGRAPH: USERS MANAGEMENT SERVICES (EE Extensions) ====
subgraph User Account Lifecycle
class UserMgmt subgraph
direction TB

    banSvc[Ban Service\nee/app/services/ee/users/ban_service.rb]
    unbanSvc[Unban Service\nee/app/services/ee/users/unban_service.rb]
    autoBanSvc[AutoBan Service\nee/app/services/ee/users/auto_ban_service.rb]
    deactivateSvc[Deactivate Service\nee/app/services/ee/users/deactivate_service.rb]

    banSvc --> userassignt
    unbanSvc --> userassignt
    autoBanSvc --> banSvc
    deactivateSvc --> userassignt

class banSvc,unbanSvc,autoBanSvc,deactivateSvc core
end

%% ==== SUBGRAPH: TEST SUPPORT / GENERATORS ====
subgraph Test Support & Code Generation
class TestAssets subgraph
direction TB

    featureApprHlp[Feature Approval Helper\nee/spec/support/helpers/feature_approval_helper.rb]
    memberRoleHlp[Member Role Helpers\nee/spec/support/helpers/member_role_helpers.rb]
    codegen[Custom Role Code Generator\nee/lib/generators/gitlab/custom_roles/code_generator.rb]

    featureApprHlp --> amrrule
    memberRoleHlp --> mrrole
    codegen --> mrrole
    codegen --> graphql_types_custPerm

class featureApprHlp,memberRoleHlp,codegen util
end

%% ==== SUBGRAPH: QA / UI COMPONENTS ====
subgraph QA & UI
class QaUi subgraph
direction TB

    qaEventsProj[QA Project Event Resource\nqa/qa/resource/events/project.rb]
    qaUserGPG[QA User GPG Resource\nqa/qa/resource/user_gpg.rb]
    qaMembersTable[QA Page Component: MembersTable\nqa/qa/page/component/members/members_table.rb]

    qaMembersTable --> membershipact
    qaMembersTable --> memberps

class qaEventsProj,qaUserGPG,qaMembersTable util
end

%% ==== SUBGRAPH: DATA STRUCTURES ====
subgraph Domain Data Structures
class DomainData subgraph
direction TB

    members_data[Members Associations\napp/models/member.rb]
    mrrole_data[Custom Member Role Data\nee/app/models/members/member_role.rb]

    members_data -.-> member
    mrrole_data -.-> mrrole

class members_data,mrrole_data data
end

%% ==== INTERFEATURE INTEGRATION AND DEPENDENCIES ====
member -- Accessed by --> membershipact
member -- Rendered by --> mempresenter
member -- Serialized as --> memberEntity
memberps -- Property preloading for --> member
mrrole -- Serialized by --> memberApprPresenter
mrrole -- Used by --> userassignt
mrrole -- Edited via --> mrcsvc
mrrole -- Updated via --> mruSvc
mrrole -- Deleted via --> mrdSvc
mrrole -- Managed by --> mrolehlp
mrrole -- Policy enforced by --> mrpol
arrole -- Used by --> userassignt
apiMemberRoles --> memberscreator
allrolesF -.-> mrrole
adminrolesF -.-> arrole

%% Permission checks
memberroleability --> mrrole

%% Policy checks to Presenters
mrpol --> mempresenter
usermrpol --> mempresenter

%% Highlighting relations of data
graphql_types_stdRole -->|Implements| graphql_types_custPerm
graphql_types_stdRole --> mrrole
graphql_types_userMemberR -->|References| mrrole

%% Test and feature helpers
featureApprHlp --> qaMembersTable
memberRoleHlp --> featureApprHlp

%% Processor workflows
removeexpired --> destroyedEv

%% Custom role codegen
codegen --> mrcsvc
codegen --> graphql_mrBase

%% UI to Action
qaMembersTable --> membershipact

%% Integration points
banSvc --> modifiedByAdmin
promoUtils --> modifiedByAdmin

%% Membership inheritance/aggregation
memberswithparents --> member

%% Connect Core Entity with Events
destroyedEv --> member
modifiedByAdmin --> mrrole

%% Extend GraphQL schema with EE types
graphql_types_userType --> graphql_types_userMemberR

%% Connect Finders back to Data
usersfinder --> member
dkeysf --> member

%% Connect Preloader and Presenters
memberps --> grpmempresenter

%% Visualize special data transformations
dataBuilderMA --> memberApprPresenter

%% Visualize link from API to assignment logic
apiMembersHelpers --> userassignt

%% End of diagram
```