```mermaid
flowchart TD
  %% Domain: Security, Compliance & Policies / Vulnerability & Incident Management / Policies & Rules
  %% VERTICAL LAYOUT
  %% Color Legend:
  %%   Core domain:      #D4F1F9
  %%   Data structures:  #E0F8E0
  %%   Supporting:       #FFF8DC
  %%   Error handling:   #FFE4E1
  %%   Initialization:   #E6E6FA
  %%   Grouping:         #F8F8F8 / pastel borders

  %% DOMAIN ROOT
  subgraph S1["Security, Compliance & Policies / Vulnerability & Incident Management / Policies & Rules" ]
    direction TB

    %% POLICY MANAGEMENT
    subgraph CORE_POLICIES["Core Policy Logic" ]
      direction TB
      style CORE_POLICIES fill:#F8F8F8,stroke:#5cacee,stroke-width:2px,rounded
      P1["security/policy.rb\\nDefines Policy Model & Main Abstractions"]
      style P1 fill:#D4F1F9,stroke:#3399cc,stroke-width:2px,rounded

      P2["security/approval_policy_rule.rb\\nDefines Approval Policy Rule"]
      style P2 fill:#D4F1F9,stroke:#3399cc,stroke-width:2px,rounded
      
      P3["security/scan_execution_policy_rule.rb\\nExec Policy Rule Model, Links Scan and Policy"]
      style P3 fill:#D4F1F9,stroke:#3399cc,stroke-width:2px,rounded

      P4["security/scan_result_policy_read.rb\\nManages Rule Reads for Scan Results"]
      style P4 fill:#D4F1F9,stroke:#3399cc,stroke-width:2px,rounded

      P5["security/scan_result_policy_violation.rb\\nStores Violations Against Scan Result Policies"]
      style P5 fill:#E0F8E0,stroke:#8fc69d,stroke-width:2px,rounded

      P6["security/approval_policy_rule_project_link.rb\\nMaps Projects to Approval Policy Rules"]
      style P6 fill:#E0F8E0,stroke:#8fc69d,stroke-width:2px,rounded

      P7["security/pipeline_execution_policy/pipeline.rb\\nExecutes Policies in Pipelines"]
      style P7 fill:#D4F1F9,stroke:#3399cc,stroke-width:2px,rounded

      P8["security/policy_ci_skippable.rb\\nMixin: Skip-CI Policy"]
      style P8 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded

      P9["security/scan_execution_policy/config.rb\\nScan Exec Policy Config"]
      style P9 fill:#E0F8E0,stroke:#8fc69d,stroke-width:2px,rounded
    end

    %% DOMAIN CONCERNS, ABSTRACT RULES, & POLICY EXTENSIONS
    subgraph CONCERNS_POLICIES["Policy Rule Abstractions & Concerns"]
      direction TB
      style CONCERNS_POLICIES fill:#F8F8F8,stroke:#a1bdf9,stroke-width:2px,rounded
      C1["security/policy_rule.rb\\nAbstract Rule Concern: Polymorphism & Attributes"]
      style C1 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded

      C2["security/scan_result_policy.rb\\nScan Result Policies Mixin"]
      style C2 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded

      C3["security/scan_execution_policy.rb\\nScan Exec Policy Concern"]
      style C3 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded

      C4["security/vulnerability_management_policy.rb\\nVuln Mgmt Policy Concern"]
      style C4 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded

      C5["security/pipeline_execution_schedule_policy.rb\\nExec Schedule Policy Concern"]
      style C5 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded

      C6["security/policies/vulnerability_management.rb\\nMixin for Vuln Mgmt Scope"]
      style C6 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded

      C7["approval_rule_like.rb\\nSimulates Shared Rule API"]
      style C7 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded
    end

    %% POLICY FINDERS
    subgraph POLICY_FINDERS["Policy Finders"]
      direction TB
      style POLICY_FINDERS fill:#F8F8F8,stroke:#aacfd0,stroke-width:2px,rounded
      F1["security/security_policies_finder.rb"]
      style F1 fill:#D4F1F9,stroke:#3399cc,stroke-width:2px,rounded

      F2["security/scan_result_policies_finder.rb"]
      style F2 fill:#D4F1F9,stroke:#3399cc,stroke-width:2px,rounded

      F3["security/vulnerability_management_policies_finder.rb"]
      style F3 fill:#D4F1F9,stroke:#3399cc,stroke-width:2px,rounded

      F4["security/pipeline_execution_policies_finder.rb"]
      style F4 fill:#D4F1F9,stroke:#3399cc,stroke-width:2px,rounded

      F5["security/pipeline_execution_schedule_policies_finder.rb"]
      style F5 fill:#D4F1F9,stroke:#3399cc,stroke-width:2px,rounded

      F6["security/security_policy_projects_finder.rb"]
      style F6 fill:#D4F1F9,stroke:#3399cc,stroke-width:2px,rounded

      F7["security/approval_groups_finder.rb"]
      style F7 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded

      F8["security/scan_result_policy_read_params_builder.rb"]
      style F8 fill:#E0F8E0,stroke:#8fc69d,stroke-width:2px,rounded
    end

    %% RULE EVALUATION & ENFORCEMENT LOGIC
    subgraph POLICY_RULES_SERVICES["Policy Evaluation & Enforcement"]
      direction TB
      style POLICY_RULES_SERVICES fill:#F8F8F8,stroke:#a1cca5,stroke-width:2px,rounded
      S3["security/security_orchestration_policies/policy_rule_evaluation_service.rb\\nEvaluates Policy Rules"]
      style S3 fill:#D4F1F9,stroke:#3399cc,stroke-width:2px,rounded

      S4["security/scan_result_policies/update_approvals_service.rb"]
      style S4 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded

      S5["security/scan_result_policies/approval_rules/delete_service.rb"]
      style S5 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded

      S6["security/scan_result_policies/approval_rules/update_service.rb"]
      style S6 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded

      S7["security/scan_result_policies/approval_rules/create_service.rb"]
      style S7 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded

      S8["security/scan_result_policies/add_approvers_to_rules_service.rb"]
      style S8 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded

      S9["security/scan_result_policies/sync_preexisting_states_approval_rules_service.rb"]
      style S9 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded

      S10["security/scan_result_policies/update_service.rb"]
      style S10 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded

      S11["security/scan_result_policies/vulnerabilities_count_service.rb"]
      style S11 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded

      S12["security/scan_result_policies/unblock_fail_open_approval_rules_service.rb"]
      style S12 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded

      S13["security/scan_result_policies/fallback_behavior_tracking_service.rb"]
      style S13 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded
    end

    %% SUPPORTING CONCERNS FOR EVALUATION/ENFORCEMENT
    subgraph ENFORCEMENT_SUPPORT["Enforcement Helpers & Logging"]
      direction TB
      style ENFORCEMENT_SUPPORT fill:#F8F8F8,stroke:#fbc08d,stroke-width:2px,rounded
      E4["security/scan_result_policies/policy_violation_comment_generator.rb"]
      style E4 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded

      E5["security/scan_result_policies/vulnerability_states_helper.rb"]
      style E5 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded

      E6["security/scan_result_policies/policy_logger.rb"]
      style E6 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded

      E7["security/scan_result_policies/related_pipelines.rb"]
      style E7 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded
    end

    %% DOMAIN DATA STRUCTURES
    subgraph POLICY_STRUCTS["Policy-related Data Structures"]
      direction TB
      style POLICY_STRUCTS fill:#F8F8F8,stroke:#8fc69d,stroke-width:2px,rounded
      DS1["scan_result_policy_rules\\nVarious Rule Structs"]
      style DS1 fill:#E0F8E0,stroke:#8fc69d,stroke-width:2px,rounded

      DS2["policy_violation_details.rb\\nViolation & Finding Structs"]
      style DS2 fill:#E0F8E0,stroke:#8fc69d,stroke-width:2px,rounded

      DS3["policy_violation_comment.rb\\nViolatedTypes/Pattern Structs"]
      style DS3 fill:#E0F8E0,stroke:#8fc69d,stroke-width:2px,rounded
    end

    %% POLICY VALIDATION, DIFF & SCOPE UTILITIES
    subgraph POLICY_UTILS["Policy Validation, Diff & Scope"]
      direction TB
      style POLICY_UTILS fill:#F8F8F8,stroke:#72c8b0,stroke-width:2px,rounded
      U1["security_orchestration_policies/policy_configuration_validation_service.rb\\nPolicy Validation"]
      style U1 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded

      U2["security_orchestration_policies/policy_diff/field_diff.rb"]
      style U2 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded

      U3["security_orchestration_policies/policy_diff/rule_diff.rb"]
      style U3 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded

      U4["security_orchestration_policies/policy_diff/rules_diff.rb"]
      style U4 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded

      U5["security_orchestration_policies/policy_diff/diff.rb"]
      style U5 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded

      U6["security_orchestration_policies/policy_scope_checker.rb"]
      style U6 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded

      U7["security_orchestration_policies/deprecated_properties_checker.rb"]
      style U7 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded

      U8["security_orchestration_policies/cadence_checker.rb"]
      style U8 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded
    end

    %% POLICY SYNCHRONIZATION & WORKERS
    subgraph POLICY_SYNC["Policy Synchronization & Background Processing"]
      direction TB
      style POLICY_SYNC fill:#F8F8F8,stroke:#a287d5,stroke-width:2px,rounded
      W1["security/sync_scan_policies_worker.rb"]
      style W1 fill:#E6E6FA,stroke:#a287d5,stroke-width:2px,rounded

      W2["security/persist_security_policies_worker.rb"]
      style W2 fill:#E6E6FA,stroke:#a287d5,stroke-width:2px,rounded

      W3["security/sync_project_policies_worker.rb"]
      style W3 fill:#E6E6FA,stroke:#a287d5,stroke-width:2px,rounded

      W4["security/refresh_project_policies_worker.rb"]
      style W4 fill:#E6E6FA,stroke:#a287d5,stroke-width:2px,rounded

      W5["security/delete_approval_policy_rules_worker.rb"]
      style W5 fill:#E6E6FA,stroke:#a287d5,stroke-width:2px,rounded

      W6["security/delete_security_policy_worker.rb"]
      style W6 fill:#E6E6FA,stroke:#a287d5,stroke-width:2px,rounded

      W7["security/scan_result_policies/delete_scan_result_policy_reads_worker.rb"]
      style W7 fill:#E6E6FA,stroke:#a287d5,stroke-width:2px,rounded

      W8["security/scan_result_policies/unblock_fail_open_approval_rules_worker.rb"]
      style W8 fill:#E6E6FA,stroke:#a287d5,stroke-width:2px,rounded

      W9["security/sync_policy_worker.rb\\nSyncs and Updates Policy Metadata"]
      style W9 fill:#E6E6FA,stroke:#a287d5,stroke-width:2px,rounded

      W10["security/scan_result_policies/add_approvers_to_rules_worker.rb"]
      style W10 fill:#E6E6FA,stroke:#a287d5,stroke-width:2px,rounded

      W11["security/scan_result_policies/cleanup_merge_request_violations_worker.rb"]
      style W11 fill:#E6E6FA,stroke:#a287d5,stroke-width:2px,rounded

      W12["security/scan_result_policies/sync_findings_to_approval_rules_worker.rb"]
      style W12 fill:#E6E6FA,stroke:#a287d5,stroke-width:2px,rounded

      W13["security/generate_policy_violation_comment_worker.rb"]
      style W13 fill:#E6E6FA,stroke:#a287d5,stroke-width:2px,rounded

      W14["security/sync_policy_violation_comment_worker.rb"]
      style W14 fill:#E6E6FA,stroke:#a287d5,stroke-width:2px,rounded

      W15["security/orchestration_configuration_remove_bot_worker.rb"]
      style W15 fill:#E6E6FA,stroke:#a287d5,stroke-width:2px,rounded
    end

    %% POLICY PRESENTATION & INTERFACES
    subgraph POLICY_PRESENTERS["Presenters, Helpers, GraphQL, API & Controllers"]
      direction TB
      style POLICY_PRESENTERS fill:#F8F8F8,stroke:#8bb4fc,stroke-width:2px,rounded
      H1["projects/security/configuration_presenter.rb"]
      style H1 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded

      H2["projects/security/configuration_helper.rb"]
      style H2 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded

      C1a["projects/security/policies_controller.rb"]
      style C1a fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded

      C2a["groups/security/policies_controller.rb"]
      style C2a fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded

      GQL1["types/security/vulnerability_management_policy_type.rb"]
      style GQL1 fill:#E0F8E0,stroke:#8fc69d,stroke-width:2px,rounded

      GQL2["resolvers/security/vulnerability_management_policy_resolver.rb"]
      style GQL2 fill:#FFF8DC,stroke:#daa520,stroke-width:2px,rounded

      GQL3["types/security_policy_validation_error.rb"]
      style GQL3 fill:#FFE4E1,stroke:#dd8888,stroke-width:2px,rounded

      GQL4["types/security_orchestration/orchestration_policy_type.rb"]
      style GQL4 fill:#E0F8E0,stroke:#8fc69d,stroke-width:2px,rounded

      GQL5["types/security_orchestration/security_policy_source_type.rb"]
      style GQL5 fill:#E0F8E0,stroke:#8fc69d,stroke-width:2px,rounded
    end

    %% DOMAIN INTERACTIONS
    %% High-level relationships between groupings
    CORE_POLICIES -- "Composed With" --> CONCERNS_POLICIES
    CORE_POLICIES -- "Uses" --> POLICY_STRUCTS
    CORE_POLICIES -- "Data Access" --> POLICY_FINDERS
    CORE_POLICIES -- "Enforced By" --> POLICY_RULES_SERVICES
    CORE_POLICIES -- "Served To" --> POLICY_PRESENTERS
    CORE_POLICIES -- "Validated By" --> POLICY_UTILS

    POLICY_FINDERS -- "Supply Policies To" --> POLICY_RULES_SERVICES
    POLICY_FINDERS -- "Enriched By" --> CONCERNS_POLICIES

    POLICY_RULES_SERVICES -- "Log/Evaluate/Update" --> ENFORCEMENT_SUPPORT
    POLICY_RULES_SERVICES -- "Assess/Write" --> POLICY_STRUCTS
    POLICY_RULES_SERVICES -- "Triggers" --> POLICY_SYNC

    POLICY_UTILS -- "Validate/Compare" --> CORE_POLICIES

    POLICY_SYNC -- "Syncs/Updates" --> CORE_POLICIES

    POLICY_PRESENTERS -- "Expose/Render" --> CORE_POLICIES
    POLICY_PRESENTERS -- "Provide" --> POLICY_STRUCTS
    POLICY_PRESENTERS -- "Surface Errors" --> POLICY_UTILS

    %% Details/group-internal relationships not exhaustive but covering most logical collaborations

    %% Core Policy Models <-> Concerns
    P2 -- "includes" --> C1
    P2 -- "includes" --> C2
    P3 -- "includes" --> C1
    P3 -- "includes" --> C3

    %% PolicyRule concern selects rule classes for policy types
    C1 -- "Maps Policy Type" --> P2
    C1 -- "Maps Policy Type" --> P3

    %% ScanResultPolicy concern manages policy model's scan results
    C2 -- "Provides Mixin For" --> P4
    C2 -- "Has Many" --> P4

    %% ApprovalRuleLike concern standardizes rule interface for various rule models
    C7 -- "Implemented By" --> P2

    %% Policy Finders
    F1 -- "Returns Policy Sets" --> P1
    F2 -- "Finds" --> P4
    F3 -- "Finds" --> P1
    F4 -- "Finds" --> P7
    F5 -- "Finds" --> P7
    F6 -- "Returns Projects" --> P1
    F7 -- "Group Selection" --> P2
    F8 -- "Builds Params" --> P4

    %% Rule services
    S3 -- "Evaluates Rule Against MRs" --> P2
    S3 -- "Accesses" --> P4
    S3 -- "Coordinates" --> S4
    S4 -- "Updates Approvals Based on" --> P2
    S5 -- "Deletes" --> P2
    S6 -- "Modifies" --> P2
    S7 -- "Creates" --> P2
    S8 -- "Adds Approvers to" --> P2
    S9 -- "Synchronizes Approval States For MR" --> P2
    S11 -- "Memoization for Approval Rule Evaluations" --> P2
    S12 -- "Unblocks MR on Failure" --> S4
    S13 -- "Tracks Fallback Events" --> S11

    %% Enforcement support
    E4 -- "Creates Bot Comments" --> W13
    E4 -- "Triggers" --> W14
    E5 -- "Determines Vulnerability States" --> S9
    E6 -- "Logs To" --> S4
    E6 -- "Logs" --> S13
    E7 -- "Finds Related Pipelines for Rule Evaluations" --> S4

    %% Data Structure utilization
    DS2 -- "Used By" --> E4
    DS2 -- "Provides Violation Info" --> P5
    DS3 -- "Parses/Builds Policy Violation Comments" --> W13

    %% Policy Validation / Diff / Scope
    U1 -- "Validates Configs for/with" --> CORE_POLICIES
    U2 -- "Used By" --> U5
    U3 -- "Used By" --> U4
    U4 -- "Rules Diff for" --> U5
    U5 -- "Diff all Policy Changes" --> CORE_POLICIES
    U6 -- "Determines Applicability" --> CORE_POLICIES
    U7 -- "Checks Deprecated Policy Properties" --> U1
    U8 -- "Validates Schedules" --> CORE_POLICIES

    %% Policy Synchronization
    W1 -- "Triggers" --> W2
    W2 -- "Persists Results To" --> P1
    W3 -- "Refreshes Policies" --> P1
    W4 -- "Refreshes For Project" --> P1
    W5 -- "Deletes" --> P2
    W6 -- "Deletes Policies" --> P1
    W7 -- "Removes Reads" --> P4
    W8 -- "Unblocks Fail Open Rules" --> S12
    W9 -- "Handles Policy Update Events" --> W1
    W10 -- "Adds Approvers" --> P2
    W11 -- "Cleans MR Violations" --> P5
    W14 -- "Syncs Bot Comments" --> E4

    %% Presenters & Externals
    H1 -- "Prepares Policy Configs UI" --> P1
    H2 -- "Assists UI/Presentation" --> H1

    C1a -- "Controls Policy Edit/Show For Project" --> P1
    C2a -- "Controls Policy Edit/Show For Group" --> P1

    GQL1 -- "GraphQL: Policy Interface" --> P1
    GQL2 -- "GraphQL: Resolver for Vuln Mgmt" --> P1
    GQL3 -- "GraphQL: Error Type" --> U1
    GQL4 -- "GraphQL: Policy Interface" --> P1
    GQL5 -- "GraphQL: Policy Source" --> P1
  end

  %% Cosmetic spacing for clarity in vertical flow
  S1 -->|""| CORE_POLICIES
  S1 -->|""| POLICY_FINDERS
  S1 -->|""| POLICY_RULES_SERVICES
  S1 -->|""| ENFORCEMENT_SUPPORT
  S1 -->|""| POLICY_STRUCTS
  S1 -->|""| POLICY_UTILS
  S1 -->|""| POLICY_SYNC
  S1 -->|""| POLICY_PRESENTERS
```