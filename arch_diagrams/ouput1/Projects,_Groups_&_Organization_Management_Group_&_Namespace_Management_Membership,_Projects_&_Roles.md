```mermaid
flowchart TD
    %% ================================================
    %% STYLING DEFINITIONS (Keep at top for clarity)
    %% ================================================
    classDef core fill:#D4F1F9,color:#222,stroke:#B0D6E4,stroke-width:2px,stroke-dasharray:0;
    classDef support fill:#FFF8DC,color:#222,stroke:#E4D6B8,stroke-width:2px,stroke-dasharray:0;
    classDef datastructure fill:#E0F8E0,color:#222,stroke:#AEDBAD,stroke-width:2px,stroke-dasharray:0;
    classDef error fill:#FFE4E1,color:#222,stroke:#FFC0CB,stroke-width:2px,stroke-dasharray:0;
    classDef setup fill:#E6E6FA,color:#222,stroke:#C9C9F1,stroke-width:2px,stroke-dasharray:0;
    classDef graySubgraph fill:#F8F8F8,stroke:#D4F1F9,stroke-width:3px,rx:14,ry:14;


    %% ================================================
    %% MAIN DOMAIN SUBGRAPHS (GROUPED BY LOGICAL PURPOSE)
    %% ================================================

    %% -- Membership & Member Roles Core Models --
    subgraph sgMembershipModels[Membership & Member Roles]
        direction TB
        groupMember[GroupMember\nCore group membership logic]:::core
        projectNamespaceMember[ProjectNamespaceMember\nMembership in projects namespace]:::core
        membersWithParents[MembersWithParents\nAggregate group + ancestors memberships]:::core
    end
    class sgMembershipModels graySubgraph

    %% -- Namespace, Group, and User Contexts --
    subgraph sgNamespaces[Namespaces & User Context]
        direction TB
        userNamespace[UserNamespace\nUser-level namespaces and membership checks]:::core
        groupDeployKeysGroup[GroupDeployKeysGroup\nLink group with deploy keys]:::support
        directlyAddressedUser[DirectlyAddressedUser\nTagging users by direct mention]:::support
    end
    class sgNamespaces graySubgraph

    %% -- Data Structures, Preloaders & Access Precalculations --
    subgraph sgMemberPreloaders[Membership Preloaders, Access Calculators]
        direction TB
        userMaxAccessPreloader[UserMaxAccessLevelInGroupsPreloader\nPreloads maximal group access]:::datastructure
        userMemberRolesGroupsPreloader[UserMemberRolesInGroupsPreloader\nPreloads user roles per group]:::datastructure
        userMemberRolesProjectsPreloader[UserMemberRolesInProjectsPreloader\nPreloads user roles per project]:::datastructure
        objectHierarchy[ObjectHierarchy\nHierarchical relations for projects/groups]:::datastructure
    end
    class sgMemberPreloaders graySubgraph

    %% -- Finder Services for Membership, Groups, Projects, Users --
    subgraph sgFinders[Finders & Query Objects]
        direction TB
        groupMembersFinder[GroupMembersFinder\nFinds group members by criteria]:::core
        groupProjectsFinder[GroupProjectsFinder\nFinds all projects in a group]:::core
        contributedProjectsFinder[ContributedProjectsFinder\nFinds all user-contributed projects]:::core
        invitedGroupsFinder[InvitedGroupsFinder\nFinds groups invited to projects]:::support
        userGroupsCounter[UserGroupsCounter\nCounts groups for selected users]:::support
        acceptingProjectImportsFinder[AcceptingProjectImportsFinder\nFind groups accepting project imports]:::support
    end
    class sgFinders graySubgraph

    %% -- Controllers: Web Entry Points --
    subgraph sgControllers[Controllers]
        direction TB
        groupsSharedProjectsController[Groups::SharedProjectsController\nAPI for shared projects]:::support
        groupsGroupLinksController[Groups::GroupLinksController\nManages group↔group relationships]:::support
        projectsGroupLinksController[Projects::GroupLinksController\nManages project↔group links]:::support
        dashboardGroupsController[Dashboard::GroupsController\nUser dashboard for groups]:::support
        exploreGroupsController[Explore::GroupsController\nExplore available groups]:::support
    end
    class sgControllers graySubgraph

    %% -- Policies & Permissions --
    subgraph sgPolicies[Policy & Authorization]
        direction TB
        memberPolicyHelpers[MemberPolicyHelpers\nShared policy logic for members]:::support
        findGroupProjects[FindGroupProjects\nPolicy helper for accessible group projects]:::support
        abilityPrepend[AbilityPrepend\nEnterprise-specific permission extension]:::support
        groupPolicy[GroupPolicy EE\nRemote development group policy]:::support
    end
    class sgPolicies graySubgraph

    %% -- Services: Domain Behaviors & Business Logic --
    subgraph sgServices[Services & Business Logic]
        direction TB
        baseGroupService[BaseGroupService\nBase service for group actions]:::setup
        groupParticipantsService[Groups::ParticipantsService\nFinds participants in group scope]:::core
        groupsAutocompleteService[Groups::AutocompleteService\nGroup-level autocompletion service]:::support
        groupLinksUpdateService[Projects::GroupLinks::UpdateService\nManages updates to group links]:::core
        unassignIssuablesService[Members::UnassignIssuablesService\nRemoves members from issues & MRs]:::core
        importProjectTeamService[Members::ImportProjectTeamService\nImports project teams as members]:::core
        searchGroupService[Search::GroupService\nSearches within groups]:::support
    end
    class sgServices graySubgraph

    %% -- Helpers: View Logic --
    subgraph sgHelpers[Helpers]
        direction TB
        membersHelper[MembersHelper\nGeneral member view helpers]:::support
        routingMembersHelper[Routing::MembersHelper\nURL helpers for member resources]:::support
    end
    class sgHelpers graySubgraph

    %% -- Mailers: Asynchronous Communication --
    subgraph sgMailers[Mailers & Notifications]
        direction TB
        emailsGroupsMailer[Emails::Groups\nEmails for group actions export, export error, etc.]:::support
    end
    class sgMailers graySubgraph

    %% -- Partitioned/Analytical Models (Visits etc) --
    subgraph sgVisits[Analytics: User Visits]
        direction TB
        usersGroupVisit[GroupVisit\nTracks group visitation for analytics]:::datastructure
        usersProjectVisit[ProjectVisit\nTracks project visitation for analytics]:::datastructure
        usersVisitable[Users::Visitable\nShared concern for visit tracking]:::support
    end
    class sgVisits graySubgraph

    %% -- Enterprise Extensions --
    subgraph sgEE[Enterprise Enhancements EE]
        direction TB
        memberApproval[MemberApproval\nEnterprise member approval workflow]:::core
        eeHelpersMembersHelper[EE::MembersHelper\nEE member management helpers]:::support
    end
    class sgEE graySubgraph

    %% -- Data Structure: Project/Group Badges --
    subgraph sgBadges[Badges & Awards]
        direction TB
        groupBadge[GroupBadge\nBadge data for groups]:::datastructure
    end
    class sgBadges graySubgraph

    %% ================================================
    %% CORE CONCEPT RELATIONSHIPS & COLLABORATIONS
    %% ================================================

    %%--- Membership Model Interactions
    groupMember -- "Parent class for project group memberships" --> projectNamespaceMember
    membersWithParents -- "Uses" --> groupMember
    membersWithParents -- "Uses" --> objectHierarchy
    membersWithParents -- "Aggregates memberships via" --> userMaxAccessPreloader
    userNamespace -- "Checks membership via" --> groupMember

    %%--- Preloaders & Access Relationships
    userMaxAccessPreloader -- "Relies on" --> groupMember
    userMaxAccessPreloader -- "Used by" --> membersWithParents
    userMemberRolesGroupsPreloader -.-> groupMember
    userMemberRolesProjectsPreloader -.-> projectNamespaceMember
    objectHierarchy -- "Encapsulates hierarchy for" --> groupMember
    objectHierarchy -- "Encapsulates hierarchy for" --> userNamespace

    %%--- Data Access Collaboration
    groupMembersFinder -- "Uses" --> groupMember
    groupMembersFinder -- "Supports filters for" --> userNamespace
    groupProjectsFinder -- "Finds projects for" --> groupMember
    contributedProjectsFinder -- "Finds user contributions via" --> groupMember
    invitedGroupsFinder -- "Finds group links for" --> groupMember

    %%--- Controllers & Web Layer
    groupsSharedProjectsController -- "Returns data using" --> groupProjectsFinder
    groupsGroupLinksController -- "Manages via" --> groupMember
    projectsGroupLinksController -- "Manages via" --> projectNamespaceMember
    dashboardGroupsController -- "Presents user view for" --> groupMember
    exploreGroupsController -- "Lists groups via" --> groupMember

    %%--- Policies, Permissions, Workflows
    groupMember -- "Checks policies using" --> memberPolicyHelpers
    groupMember -- "Works with" --> findGroupProjects
    abilityPrepend -- "Extends abilities for" --> groupMember
    groupPolicy -- "Adds policy for" --> groupMember

    %%--- Services & Use Cases
    baseGroupService -- "Base for" --> groupParticipantsService
    groupParticipantsService -- "Finds users with" --> groupMember
    groupsAutocompleteService -- "Queries" --> groupMember
    groupLinksUpdateService -- "Modifies" --> groupMember
    groupLinksUpdateService -- "Updates" --> projectNamespaceMember
    unassignIssuablesService -- "Handles removals in" --> groupMember
    importProjectTeamService -- "Imports to" --> projectNamespaceMember
    importProjectTeamService -- "Imports to" --> groupMember
    searchGroupService -- "Searches membership content via" --> groupMember

    %%--- Helpers
    membersHelper -- "Used in views with" --> groupMember
    routingMembersHelper -- "Builds URLs for resources" --> groupMember

    %%--- Email / Mailers
    emailsGroupsMailer -- "Notifies about actions on" --> groupMember
    emailsGroupsMailer -- "Sends notifications to" --> userNamespace

    %%--- Visits / Analytics
    usersGroupVisit -- "Tracks usage for" --> groupMember
    usersProjectVisit -- "Tracks usage for" --> projectNamespaceMember
    usersGroupVisit -- "Uses concern" --> usersVisitable
    usersProjectVisit -- "Uses concern" --> usersVisitable

    %%--- EE Extensions/Approvals
    memberApproval -- "Manages approval for" --> groupMember
    memberApproval -- "EE approval workflow uses" --> userNamespace
    eeHelpersMembersHelper -- "EE-specific helpers for" --> groupMember

    %%--- Badges
    groupBadge -- "Applies to" --> groupMember

    %%--- Peripheral data structures
    groupDeployKeysGroup -- "Associates with" --> groupMember
    directlyAddressedUser -- "Integrates with" --> groupMember

    %% ================================================
    %% OTHER HIGH-LEVEL DOMAIN RELATIONSHIPS
    %% ================================================
    %% --- Analytical models influence data structures
    usersVisitable -- "Embedded in" --> usersGroupVisit
    usersVisitable -- "Embedded in" --> usersProjectVisit

    %% === User's context in membership logic
    userGroupsCounter -- "Counts for stats on" --> groupMember

    %% --- Data structure flows
    groupMember -.-> objectHierarchy
    groupMember -.-> membersWithParents
    groupMember -- "Has roles managed by" --> userMemberRolesGroupsPreloader
    projectNamespaceMember -- "Has roles managed by" --> userMemberRolesProjectsPreloader

    %% --- Data structure transformation
    userMaxAccessPreloader -- "Transforms" --> groupMember
    membersWithParents -- "Returns unions of" --> groupMember

    %% --- Group, project, and namespace collaboration
    groupMember -- "May reference" --> projectNamespaceMember
    groupMember -- "Has associated" --> groupBadge

    %% --- Interactions to utility and supporting files
    groupMembersFinder -- "Filters using" --> groupBadge
    projectNamespaceMember -- "Inherits behaviours from" --> groupMember

    %% Core abstractions edges interfaces, patterns
    groupMember -- "Implements" --> usersVisitable

    %% ================================================
    %% DOMAIN BOOTSTRAP / SETUP SEQUENCE
    %% ================================================
    %% Setup/initialization sequence
    baseGroupService -- "Used as base for" --> groupParticipantsService
    baseGroupService -- "Used as base for" --> groupsAutocompleteService

    %% ================================================
    %% CLASS & SHAPE DEFINITIONS
    %% ================================================
    class groupMember,projectNamespaceMember,membersWithParents,groupProjectsFinder,contributedProjectsFinder,groupMembersFinder,groupParticipantsService,groupLinksUpdateService,unassignIssuablesService,importProjectTeamService,memberApproval,baseGroupService core;
    class userNamespace,groupDeployKeysGroup,directlyAddressedUser,memberPolicyHelpers,findGroupProjects,emailsGroupsMailer,usersVisitable,invitedGroupsFinder,groupsSharedProjectsController,groupsGroupLinksController,projectsGroupLinksController,dashboardGroupsController,exploreGroupsController,abilityPrepend,groupPolicy,groupsAutocompleteService,searchGroupService,membersHelper,routingMembersHelper,eeHelpersMembersHelper support;
    class userMaxAccessPreloader,userMemberRolesGroupsPreloader,userMemberRolesProjectsPreloader,objectHierarchy,usersGroupVisit,usersProjectVisit,groupBadge datastructure;
    class baseGroupService setup;

    %% ================================================
    %% SPACING FOR READABILITY (BLANK NODES)
    %% ================================================
    %% invisible spacing to keep groups open visually
    gap1[ ]:::datastructure
    gap2[ ]:::datastructure
    gap3[ ]:::datastructure
    gap1 --> gap2
    gap2 --> gap3
```