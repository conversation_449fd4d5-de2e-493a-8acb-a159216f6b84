```mermaid
flowchart TD
%% Styling
%% Color Palette
%% Core Domain Files: #D4F1F9 (pastel blue)
%% Supporting/Utility: #FFF8DC (pastel yellow)
%% Data Structures: #E0F8E0 (pastel green)
%% Error Handling: #FFE4E1 (pastel red)
%% Initialization/Setup: #E6E6FA (pastel purple)
%% Group Background: #F8F8F8 with pastel border

%% Core Domain: Batched Background Migrations, Bulk Jobs, Bulk Processing
subgraph BATCHED_BACKGROUND_MIGRATIONS[ "Batched Background Migrations" ]
direction TB
style BATCHED_BACKGROUND_MIGRATIONS fill:#F8F8F8,stroke:#D4F1F9,stroke-width:3,rounded corners

A1[/"Batched Migrations Finder\napp/finders/database/batched_background_migrations_finder.rb"/]:::core
A2["BatchedBackgroundMigrationWorker\napp/workers/database/batched_background_migration_worker.rb"]:::core
A3["ExecutionWorkers Main, CI\napp/workers/database/batched_background_migration/main_execution_worker.rb\napp/workers/database/batched_background_migration/ci_execution_worker.rb"]:::core
A4["SingleDatabaseWorker Concern\napp/workers/database/batched_background_migration/single_database_worker.rb"]:::utility
A5["CI Database Worker\napp/workers/database/batched_background_migration/ci_database_worker.rb"]:::core
A6["Batch Migration ExecutionWorker\napp/workers/database/batched_background_migration/execution_worker.rb"]:::core
A7["Batched Background Migration Model\nlib/gitlab/database/background_migration/batched_migration.rb"]:::datastruct
A8["Batched Job Model\nlib/gitlab/database/background_migration/batched_job.rb"]:::datastruct
A9["Batch Optimizer\nlib/gitlab/database/background_migration/batch_optimizer.rb"]:::utility
A10["Batching Strategy PK\nlib/gitlab/background_migration/batching_strategies/primary_key_batching_strategy.rb"]:::utility
A11["Batched Migrations API Admin\nlib/api/admin/batched_background_migrations.rb"]:::core
A12["Dictionary Utility\nlib/gitlab/utils/batched_background_migrations_dictionary.rb"]:::utility
A13["Batched Migration Generator\nlib/generators/batched_background_migration/batched_background_migration_generator.rb"]:::init
A14["Admin Controller for Batched Jobs\napp/controllers/admin/batched_jobs_controller.rb"]:::core
A15["Batch Worker Context Utility\nlib/gitlab/batch_worker_context.rb"]:::utility

A1 -->|Finder returns Models|A7
A1 -->|Filters migrations|A7
A7 -->|Has Many|A8
A2 -.->|Includes|A4
A2 -.->|Creates Migration Jobs|A6
A4 -.->|Used in|A5
A2 -.->|Tracks in|A7
A2 -.->|Schedules|A3
A2 -.->|Execution Workers|A6
A6 -.->|Works on|A8
A5 -.->|Specialized Worker for CI|A3
A3 -.->|Executes for DB types|A7
A7 -.->|Optimizes via|A9
A9 -.->|Applies on|A8
A7 -.->|Uses Batching|A10
A7 -.->|Exposed via API|A11
A11 -.->|Uses|A7
A11 -.->|Uses|A14
A11 -.->|References|A1
A11 -.->|Presents|A7
A12 -.->|Describes migration meta|A7
A13 -.->|Generates migrations|A7
A13 -.->|Creates dictionary files|A12
A14 -.->|Exposes admin management|A7
A15 -.->|Provides context for bulk jobs|A2
A10 -.->|Strategy for model batching|A7
 
end

%% Subgraph: Bulk Processing & Bulk Jobs
subgraph BULK_PROCESSING[ "Bulk Processing & Jobs" ]
direction TB
style BULK_PROCESSING fill:#F8F8F8,stroke:#D4F1F9,stroke-width:3,rounded corners

B1["Bulk Processor\nActiveContext BulkProcessor, BulkProcessQueue\ngems/gitlab-active-context/lib/active_context/bulk_processor.rb\ngems/gitlab-active-context/lib/active_context/bulk_process_queue.rb"]:::core
B2["BulkInsertableTask Utility\nlib/gitlab/ingestion/bulk_insertable_task.rb"]:::utility
B3["Bulk Event Payload Service\napp/services/bulk_push_event_payload_service.rb"]:::utility
B4["Schedule Bulk Shard Moves\napp/services/concerns/schedule_bulk_repository_shard_moves_methods.rb"]:::utility
B5["Bulk Policy Execution Worker\napp/workers/packages/cleanup/execute_policy_worker.rb"]:::core
B6["Bulk Export Pipeline Workers\napp/workers/bulk_imports/finish_batched_relation_export_worker.rb\napp/workers/bulk_imports/pipeline_batch_worker.rb"]:::core

B1-->|Manages queues of bulk tasks|B2
B2-->|Bulk Insert Tasks|B3
B4-->|Schedules workers|B1
B5-->|Executes bulk policies|B3
B6-->|Processes batched exports/imports|B5

end

%% Subgraph: Data Structures & Persistence Models
subgraph DATA_STRUCTURES[ "Persistence Models & Batch Data Structures" ]
direction TB
style DATA_STRUCTURES fill:#F8F8F8,stroke:#E0F8E0,stroke-width:3,rounded corners

C1["BatchedMigration Model\nlib/gitlab/database/background_migration/batched_migration.rb"]:::datastruct
C2["BatchedJob Model\nlib/gitlab/database/background_migration/batched_job.rb"]:::datastruct
C3["Reindexing Action\nlib/gitlab/database/reindexing/reindex_action.rb"]:::datastruct
C4["Bulk Update Helper\nlib/gitlab/database/bulk_update.rb"]:::utility

C1 -->|Has Many|C2
C3-->|Links to database tables|C1
C4-->|Executes bulk SQL operations|C1

end

%% Subgraph: Database Cleanup & Orphaned Data
subgraph CLEANUP_ORPHANED[ "Database Cleanup & Orphaned Data Handlers" ]
direction TB
style CLEANUP_ORPHANED fill:#F8F8F8,stroke:#D4F1F9,stroke-width:3,rounded corners

D1["Orphan Job Artifact Final Objects\nlib/gitlab/cleanup/orphan_job_artifact_final_objects/generate_list.rb\nlib/gitlab/cleanup/orphan_job_artifact_final_objects/process_list.rb\nlib/gitlab/cleanup/orphan_job_artifact_final_objects/batch_from_list.rb"]:::core
D2["Batch Paginator for Artifact Cleanup\nlib/gitlab/cleanup/orphan_job_artifact_final_objects/paginators/base_paginator.rb"]:::utility
D3["Orphan Job Artifact EE Extensions\nee/lib/ee/gitlab/cleanup/orphan_job_artifact_files.rb\nee/lib/ee/gitlab/cleanup/orphan_job_artifact_files_batch.rb"]:::core

D1-->|Processes orphaned artifacts in batches|D2
D3-->|Extends artifact cleanup in EE|D1

end

%% Subgraph: Background Migration Implementations
subgraph BATCH_JOBS_DOMAIN[ "Background/Batched Migration Jobs" ]
direction TB
style BATCH_JOBS_DOMAIN fill:#F8F8F8,stroke:#D4F1F9,stroke-width:3,rounded corners

E001["BatchedMigrationJob Implementations\nlib/gitlab/background_migration/\n*_job.rb, *_service.rb, etc"]:::core
E002["Batching Strategies\nlib/gitlab/background_migration/batching_strategies/primary_key_batching_strategy.rb"]:::utility
E003["Support Structures: EachBatch, FromUnion etc.\nlib/gitlab/database/background_migration/"]:::utility

E001-->|Use batching strategies|E002
E001-->|Extend Persistence Models|C1
E001-->|Utilize Batch Optimizer|A9
E003-->|Shared mixins for migration jobs|E001

%% Key examples
E101["Migrate Scim Identities\nlib/gitlab/background_migration/migrate_scim_identities.rb"]:::core
E201["Copy Column Backfill\nlib/gitlab/background_migration/copy_column_using_background_migration_job.rb"]:::core
E301["Remove Old Job Tokens\nlib/gitlab/background_migration/remove_old_job_tokens.rb"]:::core
E401["Update Jira Tracker Data Deployment Type\nlib/gitlab/background_migration/update_jira_tracker_data_deployment_type_based_on_url.rb"]:::core

E001-->|Includes various domain jobs|E101
E001-->|Includes various domain jobs|E201
E001-->|Includes various domain jobs|E301
E001-->|Includes various domain jobs|E401

end

%% Subgraph: Utility & Support Concerns
subgraph UTILITIES_SUPPORT[ "Support Concerns & Utility Modules" ]
direction TB
style UTILITIES_SUPPORT fill:#F8F8F8,stroke:#FFF8DC,stroke-width:3,rounded corners

F1["ExclusiveLeaseGuard, StrongMemoize, etc\napp/workers/database/batched_background_migration/execution_worker.rb\napp/workers/database/batched_background_migration/single_database_worker.rb\napp/workers/database/batched_background_migration/ci_database_worker.rb\nlib/gitlab/utils/strong_memoize.rb"]:::utility
F2["ClickHouse Worker Concern\napp/workers/concerns/click_house_worker.rb"]:::utility
F3["ProjectStartImport Concern\napp/workers/concerns/project_start_import.rb"]:::utility

F1-->|Provide lease and concurrency logic|A6
F2-->|ClickHouse migration support|F1

end

%% Subgraph: Error Handling & Fault Tolerance
subgraph ERROR_HANDLING[ "Error Handling & Job Reliability" ]
direction TB
style ERROR_HANDLING fill:#F8F8F8,stroke:#FFE4E1,stroke-width:3,rounded corners

G1["Error Classes & Split/Retry Errors\nlib/gitlab/database/background_migration/batched_job.rb"]:::error
G2["Sidekiq Reliable Fetch\nvendor/gems/sidekiq-reliable-fetch/lib/sidekiq/base_reliable_fetch.rb\nvendor/gems/sidekiq-reliable-fetch/lib/sidekiq/semi_reliable_fetch.rb\nvendor/gems/sidekiq-reliable-fetch/lib/sidekiq/interruptions_exhausted.rb\nvendor/gems/sidekiq-reliable-fetch/lib/sidekiq/interrupted_set.rb"]:::error
G3["Bulk Job Fault Tolerance\nActiveContext bulk processor fault handling"]:::error

G2-->|Job reliability layer|A2
G1-->|Error propagation / retry control|A6
G3-->|Bulk job retry after bulk-error|B1

end

%% Subgraph: Loose Foreign Keys Processing
subgraph LOOSE_FOREIGN_KEYS[ "Loose Foreign Keys & Deletion Batching" ]
direction TB
style LOOSE_FOREIGN_KEYS fill:#F8F8F8,stroke:#D4F1F9,stroke-width:3,rounded corners

H1["ProcessDeletedRecordsService\napp/services/loose_foreign_keys/process_deleted_records_service.rb"]:::core
H2["PartitionCleanerService\napp/services/loose_foreign_keys/partition_cleaner_service.rb"]:::core

H1-->|Cleans up deleted records in batches|H2
H2-->|Applies on partitioned tables|C1

end

%% Subgraph: Indexing & Infra Bulk Operations
subgraph INDEXING_BULK[ "Infra: Bulk Indexing & Side Operations" ]
direction TB
style INDEXING_BULK fill:#F8F8F8,stroke:#E6E6FA,stroke-width:3,rounded corners

I1["Elastic BulkCronWorker\nEE Concern\nee/app/workers/concerns/elastic/bulk_cron_worker.rb"]:::core
I2["Elastic Namespace Update Worker\nee/app/workers/elastic/namespace_update_worker.rb"]:::core
I3["Elastic Migration Cleanup Cron\nEE Worker\nee/app/workers/search/elastic/migration_cleanup_cron_worker.rb"]:::core
I4["Geo SkipSecondary Concern\nee/app/workers/concerns/geo/skip_secondary.rb"]:::utility
I5["Gitlab ElasticsearchEnabledCache\nee/lib/gitlab/elastic/elasticsearch_enabled_cache.rb"]:::utility
I6["ActiveContext ElasticIndexer\ngems/gitlab-active-context/lib/active_context/databases/concerns/elastic_indexer.rb"]:::utility

I1-->|Prepend|I4
I2-->|Include|I1
I3-->|Prepend|I4
I4-->|Used for geo secondary skip|I2
I6-->|Provides indexing bulk logic|I2
I5-->|Caching support for jobs|I2

end

%% Subgraph: ClickHouse Write Buffer
subgraph CLICKHOUSE[ "ClickHouse Write Buffer" ]
direction TB
style CLICKHOUSE fill:#F8F8F8,stroke:#E0F8E0,stroke-width:3,rounded corners

J1["ClickHouse WriteBuffer\nlib/click_house/write_buffer.rb"]:::datastruct
J2["ClickHouseWorker Concern\napp/workers/concerns/click_house_worker.rb"]:::utility

J2-->|Uses buffer for events|J1

end

%% Subgraph: Cron/Clean Workers & Repository Checks
subgraph CRON_REPO_BACKGROUND[ "Repo Check & Cron Job Batching" ]
direction TB
style CRON_REPO_BACKGROUND fill:#F8F8F8,stroke:#D4F1F9,stroke-width:3,rounded corners

K1["RepositoryCheck BatchWorker\napp/workers/repository_check/batch_worker.rb"]:::core
K2["CronjobQueue, RepositoryCheckQueue concerns"]:::utility
K1-->|Runs scheduled repo checks|C1
K1-->|Uses cron queues|K2

end

%% Cross-domain Connections
%% Batched Background Migrations <-> Data Structures
A7-->|Persistent batch state|C1
A8-->|Individual job state|C2

%% BatchedBackgroundMigrationWorker uses Error Handling split/retry
A2-->|Handles batch errors|G1

%% Utility Layer Integration
A4-->|Shares lease/concurrency patterns|F1
A6-->|Relies on utility/lease support|F1
B5-->|Schedules bulk cleanup|D1

%% Bulk Jobs <-> Repo/Cleanup/Database
B6-->|Runs as bulk import jobs|A2
B1-->|Coordinates large queue batches|A7

%% Indexing Bulk <-> BatchedBackgroundMigrations
I1-->|May schedule/track jobs|A2

%% Decorate/Style
classDef core fill:#D4F1F9,stroke:#93C4DB,stroke-width:2,stroke-dasharray:0,rx:8,ry:8
classDef utility fill:#FFF8DC,stroke:#E8D499,stroke-width:2,stroke-dasharray:0,rx:8,ry:8
classDef datastruct fill:#E0F8E0,stroke:#8ED089,stroke-width:2,stroke-dasharray:0,rx:8,ry:8
classDef error fill:#FFE4E1,stroke:#E09EA0,stroke-width:2,stroke-dasharray:0,rx:8,ry:8
classDef init fill:#E6E6FA,stroke:#B8B8DC,stroke-width:2,stroke-dasharray:0,rx:8,ry:8
```