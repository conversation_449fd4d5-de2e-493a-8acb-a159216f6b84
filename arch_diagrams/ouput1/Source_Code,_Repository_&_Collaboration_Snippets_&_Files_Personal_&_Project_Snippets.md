```mermaid
flowchart TD
  %% Domain: Source Code / Repository & Collaboration / Snippets & Files / Personal & Project Snippets
  %% Layout and styling
  %% Colors: 
  %% - Core domain files: pastel blue (#D4F1F9)
  %% - Supporting/utility files: pastel yellow (#FFF8DC)
  %% - Data structure files: pastel green (#E0F8E0)
  %% - Error handling files: pastel red (#FFE4E1)
  %% - Initialization/setup files: pastel purple (#E6E6FA)
  %% - Grouping: light gray (#F8F8F8)

  %% === TOP LEVEL ORGANIZATION ===
  subgraph CLUSTER_SNIPPET_MODELS["Snippets: Core Models" ]
    direction TB
    style CLUSTER_SNIPPET_MODELS fill:#F8F8F8,stroke:#6ECFF6,stroke-width:1,stroke-dasharray: 7 7

    SNIPPET["Snippet.rb":::coreM]:::coreM
    PERSONAL_SNIPPET["PersonalSnippet.rb":::coreM]:::coreM
    PROJECT_SNIPPET["ProjectSnippet.rb":::coreM]:::coreM
    SNIPPET_STATS["SnippetStatistics.rb":::coreM]:::coreM
    SNIPPET_USER_MENTION["SnippetUserMention.rb":::coreM]:::coreM
  end

  subgraph CLUSTER_SNIPPET_PRESENTATION["Snippets: Presentation" ]
    direction TB
    style CLUSTER_SNIPPET_PRESENTATION fill:#F8F8F8,stroke:#6ECFF6,stroke-width:1,stroke-dasharray: 7 7

    SNIPPET_PRESENTER["SnippetPresenter.rb":::coreM]:::coreM
    SNIPPETS_HELPER["SnippetsHelper.rb":::supportU]:::supportU
    ROUTING_SNIPPET_HELPER["Routing::SnippetsHelper.rb":::supportU]:::supportU
  end

  subgraph CLUSTER_POLICIES["Snippets: Authorization Policies" ]
    direction TB
    style CLUSTER_POLICIES fill:#F8F8F8,stroke:#6ECFF6

    PERSONAL_SNIPPET_POLICY["PersonalSnippetPolicy.rb":::supportU]
    PROJECT_SNIPPET_POLICY["ProjectSnippetPolicy.rb":::supportU]
  end

  subgraph CLUSTER_FINDERS["Snippets: Querying and Search" ]
    direction TB
    style CLUSTER_FINDERS fill:#F8F8F8,stroke:#6ECFF6

    SNIPPETS_FINDER["SnippetsFinder.rb":::supportU]
    EE_SNIPPETS_FINDER["EE::SnippetsFinder.rb":::supportU]
    LABEL_FILTER["Issuables/LabelFilter.rb":::supportU]
    SNIPPET_SEARCH_SERVICE["Search::SnippetService.rb":::supportU]
    GITLAB_SNIPPET_SEARCH_RESULTS["Gitlab::SnippetSearchResults.rb":::supportU]
    EE_GITLAB_SNIPPET_SEARCH_RESULTS["EE::Gitlab::SnippetSearchResults.rb":::supportU]
  end

  subgraph CLUSTER_SERVICES["Snippets: Business Logic Services"]
    direction TB
    style CLUSTER_SERVICES fill:#F8F8F8,stroke:#6ECFF6

    SNIPPET_BASE_SERVICE["Snippets/BaseService.rb":::coreM]
    SNIPPET_CREATE_SERVICE["Snippets/CreateService.rb":::coreM]
    SNIPPET_UPDATE_SERVICE["Snippets/UpdateService.rb":::coreM]
    SNIPPET_DESTROY_SERVICE["Snippets/DestroyService.rb":::coreM]
    EE_SNIPPET_DESTROY_SERVICE["EE::Snippets::DestroyService.rb":::coreM]
    SNIPPET_BULK_DESTROY_SERVICE["Snippets/BulkDestroyService.rb":::coreM]
    SNIPPET_REPO_VALIDATION_SERVICE["Snippets/RepositoryValidationService.rb":::coreM]
    SNIPPET_UPDATE_STATISTICS_SERVICE["Snippets/UpdateStatisticsService.rb":::coreM]
    SNIPPET_UPDATE_REPO_STORAGE_SERVICE["Snippets/UpdateRepositoryStorageService.rb":::coreM]
    SNIPPET_COUNT_SERVICE["Snippets/CountService.rb":::coreM]
    SNIPPET_SCHEDULE_SHARD_MOVES_SERVICE["Snippets/ScheduleBulkRepositoryShardMovesService.rb":::coreM]
  end

  subgraph CLUSTER_CONTROLLERS["Snippets: Web/API Controllers"]
    direction TB
    style CLUSTER_CONTROLLERS fill:#F8F8F8,stroke:#6ECFF6

    SNIPPETS_APP_CONTROLLER["Snippets/ApplicationController.rb":::coreM]
    SNIPPETS_CONTROLLER["SnippetsController.rb":::coreM]
    PROJECTS_SNIPPETS_CONTROLLER["Projects/SnippetsController.rb":::coreM]
    PROJECTS_SNIPPETS_APP_CONTROLLER["Projects/Snippets/ApplicationController.rb":::coreM]
    DASHBOARD_SNIPPETS_CONTROLLER["Dashboard/SnippetsController.rb":::coreM]
    EXPLORE_SNIPPETS_CONTROLLER["Explore/SnippetsController.rb":::coreM]
    SNIPPETS_NOTES_CONTROLLER["Snippets/NotesController.rb":::coreM]
    SNIPPETS_BLOBS_ACTIONS["Concerns/Snippets/BlobsActions.rb":::supportU]
    SNIPPETS_ACTIONS["SnippetsActions concern":::supportU]
    SNIPPET_AUTHORIZATIONS["SnippetAuthorizations.rb":::supportU]
  end

  subgraph CLUSTER_GRAPHQL["Snippets: API / GraphQL / Resolvers"]
    direction TB
    style CLUSTER_GRAPHQL fill:#F8F8F8,stroke:#6ECFF6

    RESOLVES_SNIPPETS["GraphQL/Resolvers/Concerns/ResolvesSnippets.rb":::supportU]
  end

  subgraph CLUSTER_GIT_ACCESS["Snippets: Repository & Access" ]
    direction TB
    style CLUSTER_GIT_ACCESS fill:#F8F8F8,stroke:#6ECFF6

    GITLAB_GIT_ACCESS_SNIPPET["GitAccessSnippet.rb":::coreM]
    GITLAB_USER_ACCESS_SNIPPET["UserAccessSnippet.rb":::supportU]
    GITLAB_PATH_REGEX["PathRegex.rb":::supportU]
    SNIPPET_REPOSITORY["Gitlab::Repositories::SnippetRepository.rb":::coreM]
    EE_SNIPPET_REPOSITORY["EE::SnippetRepository.rb":::coreM]
    EE_SNIPPET["EE::Snippet.rb":::coreM]
    ELASTIC_SNIPPETS_SEARCH["Elastic/SnippetsSearch.rb":::supportU]
    EE_PROJECT_SNIPPET_POLICY["EE::ProjectSnippetPolicy.rb":::supportU]
  end

  subgraph CLUSTER_IMPORT_EXPORT["Snippets: Import/Export & Bulk Ops"]
    direction TB
    style CLUSTER_IMPORT_EXPORT fill:#F8F8F8,stroke:#6ECFF6

    BULK_IMPORT_PIPELINE_SNIPPETS["BulkImports::Projects::Pipelines::SnippetsPipeline.rb":::supportU]
    BULK_IMPORT_PIPELINE_REPO["BulkImports::Projects::Pipelines::SnippetsRepositoryPipeline.rb":::supportU]
    GITHUB_GISTS_IMPORTER["GithubGistsImport/Importer/GistImporter.rb":::supportU]
    GITHUB_GISTS_IMPORTERS["GithubGistsImport/Importer/GistsImporter.rb":::supportU]
    DB_DEV_SNIPPET_FIXTURE["db/fixtures/development/12_snippets.rb":::initS]
  end

  subgraph CLUSTER_API_SUPPORT["Snippets: API Entities & Responses"]
    direction TB
    style CLUSTER_API_SUPPORT fill:#F8F8F8,stroke:#6ECFF6

    API_SNIPPETS_HTTP_MAP["Api/Helpers/Snippets/HttpResponseMap.rb":::errorH]
    EE_API_SNIPPETS_HELPERS["EE/Api/Helpers/SnippetsHelpers.rb":::supportU]
    API_USER_PUBLIC["Api/Entities/UserPublic.rb":::supportU]
  end

  subgraph CLUSTER_TEST_SUPPORT["Snippets: Test, Spec, QA"]
    direction TB
    style CLUSTER_TEST_SUPPORT fill:#F8F8F8,stroke:#6ECFF6

    SPEC_SNIPPET_REPOSITORY["spec/models/snippet_repository_spec.rb":::supportU]
    SPEC_IMPORT_EXPORT_SNIPPET["spec/lib/gitlab/import_export/snippets_repo_restorer_spec.rb":::supportU]
    SPEC_SNIPPET_SUPPORT["spec/support/helpers/features/snippet_spec_helpers.rb":::supportU]
    SPEC_USER_VIEWS_SNIPPETS["spec/features/projects/snippets/user_views_snippets_spec.rb":::supportU]
    QA_RESOURCE_SNIPPET["qa/resource/snippet.rb":::supportU]
    QA_RESOURCE_PROJECT_SNIPPET["qa/resource/project_snippet.rb":::supportU]
    QA_RESOURCE_VISIBILITY["qa/resource/visibility.rb":::supportU]
    QA_PAGE_SNIPPET_INDEX["qa/page/dashboard/snippet/index.rb":::supportU]
    QA_PAGE_SNIPPET_SHOW["qa/page/dashboard/snippet/show.rb":::supportU]
    QA_PAGE_SNIPPET_NEW["qa/page/dashboard/snippet/new.rb":::supportU]
    QA_PAGE_SNIPPET_EDIT["qa/page/dashboard/snippet/edit.rb":::supportU]
    QA_PAGE_PROJECT_SNIPPET_NEW["qa/page/project/snippet/new.rb":::supportU]
    QA_PAGE_COMPONENT_SNIPPET["qa/page/component/snippet.rb":::supportU]
    QA_PAGE_COMPONENT_WORKITEM_WIDGETS["qa/page/component/work_item/widgets.rb":::supportU]
    QA_EE_PAGE_PROJECT_SNIPPET_INDEX["qa/ee/page/project/snippet/index.rb":::supportU]
    QA_SPEC_FEATURE_EE_GEO["qa/specs/features/ee/browser_ui/12_systems/geo/project_snippet_spec.rb":::supportU]
  end

  %% === STYLE MAPS ===
  classDef coreM fill:#D4F1F9,stroke:#7BB8E2,stroke-width:2,rx:12,ry:12;
  classDef dataS fill:#E0F8E0,stroke:#A2E6A2,stroke-width:2,rx:12,ry:12;
  classDef supportU fill:#FFF8DC,stroke:#EEDFA4,stroke-width:2,rx:12,ry:12;
  classDef errorH fill:#FFE4E1,stroke:#FFC9C9,stroke-width:2,rx:12,ry:12;
  classDef initS fill:#E6E6FA,stroke:#B6B6D6,stroke-width:2,rx:12,ry:12;

  %% ========== LOGICAL RELATIONSHIPS ==========

  %% --- Models Hierarchy (STI) ---
  SNIPPET --> PERSONAL_SNIPPET
  SNIPPET --> PROJECT_SNIPPET
  PROJECT_SNIPPET -->|has_one| SNIPPET_STATS
  PERSONAL_SNIPPET -->|inherits| SNIPPET
  PROJECT_SNIPPET -->|inherits| SNIPPET
  SNIPPET --> SNIPPET_STATS
  SNIPPET --> SNIPPET_USER_MENTION

  %% --- Policy Collaboration ---
  PERSONAL_SNIPPET_POLICY --> PERSONAL_SNIPPET
  PROJECT_SNIPPET_POLICY --> PROJECT_SNIPPET
  EE_PROJECT_SNIPPET_POLICY --> PROJECT_SNIPPET_POLICY

  %% --- Presentation ---
  SNIPPET_PRESENTER --> SNIPPET
  SNIPPET_PRESENTER --> PERSONAL_SNIPPET
  SNIPPET_PRESENTER --> PROJECT_SNIPPET
  SNIPPET_PRESENTER --> SNIPPET_STATS
  SNIPPETS_HELPER --> SNIPPET
  ROUTING_SNIPPET_HELPER --> SNIPPET

  %% --- Finders & Search ---
  SNIPPETS_FINDER --> SNIPPET
  SNIPPETS_FINDER --> PERSONAL_SNIPPET
  SNIPPETS_FINDER --> PROJECT_SNIPPET
  EE_SNIPPETS_FINDER --> SNIPPETS_FINDER
  LABEL_FILTER --> SNIPPETS_FINDER
  GITLAB_SNIPPET_SEARCH_RESULTS --> SNIPPETS_FINDER
  SNIPPET_SEARCH_SERVICE --> GITLAB_SNIPPET_SEARCH_RESULTS
  EE_GITLAB_SNIPPET_SEARCH_RESULTS -.-> GITLAB_SNIPPET_SEARCH_RESULTS
  EE_GITLAB_SNIPPET_SEARCH_RESULTS -.-> SNIPPETS_FINDER

  %% --- Services and Interactions ---
  SNIPPET_BASE_SERVICE --> SNIPPET
  SNIPPET_BASE_SERVICE --> SNIPPET_REPO_VALIDATION_SERVICE
  SNIPPET_BASE_SERVICE --> SNIPPET_UPDATE_STATISTICS_SERVICE
  SNIPPET_CREATE_SERVICE --> SNIPPET_BASE_SERVICE
  SNIPPET_UPDATE_SERVICE --> SNIPPET_BASE_SERVICE
  SNIPPET_UPDATE_SERVICE --> SNIPPET_REPO_VALIDATION_SERVICE
  SNIPPET_UPDATE_SERVICE --> SNIPPET_UPDATE_STATISTICS_SERVICE
  SNIPPET_DESTROY_SERVICE --> SNIPPET_BASE_SERVICE
  SNIPPET_DESTROY_SERVICE --> SNIPPET_UPDATE_STATISTICS_SERVICE
  SNIPPET_BULK_DESTROY_SERVICE --> SNIPPET_DESTROY_SERVICE
  EE_SNIPPET_DESTROY_SERVICE -.-> SNIPPET_DESTROY_SERVICE
  SNIPPET_SCHEDULE_SHARD_MOVES_SERVICE --> SNIPPET_UPDATE_REPO_STORAGE_SERVICE
  SNIPPET_UPDATE_REPO_STORAGE_SERVICE --> SNIPPET
  SNIPPET_COUNT_SERVICE --> SNIPPETS_FINDER

  %% --- Controller Orchestration ---
  SNIPPETS_APP_CONTROLLER --> SNIPPET
  SNIPPETS_CONTROLLER --> SNIPPETS_APP_CONTROLLER
  SNIPPETS_CONTROLLER --> SNIPPETS_ACTIONS
  SNIPPETS_CONTROLLER --> SNIPPET_AUTHORIZATIONS
  SNIPPETS_NOTES_CONTROLLER --> SNIPPETS_APP_CONTROLLER
  PROJECTS_SNIPPETS_CONTROLLER --> PROJECTS_SNIPPETS_APP_CONTROLLER
  PROJECTS_SNIPPETS_APP_CONTROLLER -->|finds| PROJECT_SNIPPET
  PROJECTS_SNIPPETS_APP_CONTROLLER --> SNIPPET_AUTHORIZATIONS
  PROJECTS_SNIPPETS_CONTROLLER --> SNIPPETS_ACTIONS
  SNIPPETS_BLOBS_ACTIONS --> SNIPPET
  EXPLORE_SNIPPETS_CONTROLLER --> SNIPPET
  DASHBOARD_SNIPPETS_CONTROLLER --> SNIPPET

  %% --- GraphQL ---
  RESOLVES_SNIPPETS --> SNIPPETS_FINDER

  %% --- Repo & Access Control ---
  GITLAB_GIT_ACCESS_SNIPPET --> SNIPPET
  GITLAB_GIT_ACCESS_SNIPPET --> PROJECT_SNIPPET
  GITLAB_GIT_ACCESS_SNIPPET --> PERSONAL_SNIPPET
  GITLAB_GIT_ACCESS_SNIPPET --> SNIPPET_REPOSITORY
  GITLAB_USER_ACCESS_SNIPPET --> SNIPPET
  GITLAB_PATH_REGEX --> SNIPPET
  SNIPPET_REPOSITORY --> GITLAB_GIT_ACCESS_SNIPPET
  EE_SNIPPET_REPOSITORY --> SNIPPET_REPOSITORY
  EE_SNIPPET --> SNIPPET
  ELASTIC_SNIPPETS_SEARCH --> SNIPPET
  EE_SNIPPET --> ELASTIC_SNIPPETS_SEARCH

  %% --- Import/Export / Bulk---
  BULK_IMPORT_PIPELINE_SNIPPETS --> SNIPPET
  BULK_IMPORT_PIPELINE_REPO --> SNIPPET_REPOSITORY
  GITHUB_GISTS_IMPORTER --> PERSONAL_SNIPPET
  GITHUB_GISTS_IMPORTERS --> GITHUB_GISTS_IMPORTER
  DB_DEV_SNIPPET_FIXTURE --> SNIPPET

  %% --- API Support and HTTP Mapping / Error Handling ---
  API_SNIPPETS_HTTP_MAP --> SNIPPET_BASE_SERVICE
  API_SNIPPETS_HTTP_MAP --> SNIPPET_CREATE_SERVICE
  API_SNIPPETS_HTTP_MAP --> SNIPPET_UPDATE_SERVICE
  API_SNIPPETS_HTTP_MAP --> SNIPPET_DESTROY_SERVICE
  API_SNIPPETS_HTTP_MAP --> SNIPPET_BULK_DESTROY_SERVICE
  EE_API_SNIPPETS_HELPERS --> API_SNIPPETS_HTTP_MAP
  API_USER_PUBLIC --> SNIPPET

  %% --- Test/QA/SUPPORT: Implement/verify the domain experience ---
  SPEC_SNIPPET_REPOSITORY --> SNIPPET
  SPEC_SNIPPET_REPOSITORY --> SNIPPET_REPOSITORY
  SPEC_IMPORT_EXPORT_SNIPPET --> SNIPPET
  SPEC_IMPORT_EXPORT_SNIPPET --> SNIPPET_REPOSITORY
  SPEC_SNIPPET_SUPPORT --> SNIPPET
  SPEC_USER_VIEWS_SNIPPETS --> SNIPPET
  QA_RESOURCE_SNIPPET --> SNIPPET
  QA_RESOURCE_PROJECT_SNIPPET --> PROJECT_SNIPPET
  QA_RESOURCE_PROJECT_SNIPPET --> QA_RESOURCE_SNIPPET
  QA_RESOURCE_VISIBILITY --> QA_RESOURCE_SNIPPET
  QA_PAGE_SNIPPET_INDEX --> SNIPPET
  QA_PAGE_SNIPPET_SHOW --> SNIPPET
  QA_PAGE_SNIPPET_NEW --> SNIPPET
  QA_PAGE_SNIPPET_EDIT --> SNIPPET
  QA_PAGE_PROJECT_SNIPPET_NEW --> PROJECT_SNIPPET
  QA_PAGE_COMPONENT_SNIPPET --> QA_PAGE_SNIPPET_SHOW
  QA_PAGE_COMPONENT_WORKITEM_WIDGETS -.-> SNIPPET
  QA_EE_PAGE_PROJECT_SNIPPET_INDEX --> PROJECT_SNIPPET
  QA_SPEC_FEATURE_EE_GEO -.-> EE_SNIPPET_REPOSITORY

  %% === DATA STRUCTURE NODES ===

  class SNIPPET coreM;
  class PERSONAL_SNIPPET coreM;
  class PROJECT_SNIPPET coreM;
  class SNIPPET_STATS coreM;
  class SNIPPET_USER_MENTION coreM;
  class SNIPPET_PRESENTER coreM;
  class SNIPPETS_HELPER supportU;
  class ROUTING_SNIPPET_HELPER supportU;
  class PERSONAL_SNIPPET_POLICY supportU;
  class PROJECT_SNIPPET_POLICY supportU;
  class EE_PROJECT_SNIPPET_POLICY supportU;
  class SNIPPETS_FINDER supportU;
  class EE_SNIPPETS_FINDER supportU;
  class LABEL_FILTER supportU;
  class SNIPPET_SEARCH_SERVICE supportU;
  class GITLAB_SNIPPET_SEARCH_RESULTS supportU;
  class EE_GITLAB_SNIPPET_SEARCH_RESULTS supportU;
  class SNIPPET_BASE_SERVICE coreM;
  class SNIPPET_CREATE_SERVICE coreM;
  class SNIPPET_UPDATE_SERVICE coreM;
  class SNIPPET_DESTROY_SERVICE coreM;
  class EE_SNIPPET_DESTROY_SERVICE coreM;
  class SNIPPET_BULK_DESTROY_SERVICE coreM;
  class SNIPPET_REPO_VALIDATION_SERVICE coreM;
  class SNIPPET_UPDATE_STATISTICS_SERVICE coreM;
  class SNIPPET_UPDATE_REPO_STORAGE_SERVICE coreM;
  class SNIPPET_COUNT_SERVICE coreM;
  class SNIPPET_SCHEDULE_SHARD_MOVES_SERVICE coreM;
  class SNIPPETS_APP_CONTROLLER coreM;
  class SNIPPETS_CONTROLLER coreM;
  class PROJECTS_SNIPPETS_CONTROLLER coreM;
  class PROJECTS_SNIPPETS_APP_CONTROLLER coreM;
  class DASHBOARD_SNIPPETS_CONTROLLER coreM;
  class EXPLORE_SNIPPETS_CONTROLLER coreM;
  class SNIPPETS_NOTES_CONTROLLER coreM;
  class SNIPPETS_BLOBS_ACTIONS supportU;
  class SNIPPETS_ACTIONS supportU;
  class SNIPPET_AUTHORIZATIONS supportU;
  class RESOLVES_SNIPPETS supportU;
  class GITLAB_GIT_ACCESS_SNIPPET coreM;
  class GITLAB_USER_ACCESS_SNIPPET supportU;
  class GITLAB_PATH_REGEX supportU;
  class SNIPPET_REPOSITORY coreM;
  class EE_SNIPPET_REPOSITORY coreM;
  class EE_SNIPPET coreM;
  class ELASTIC_SNIPPETS_SEARCH supportU;
  class BULK_IMPORT_PIPELINE_SNIPPETS supportU;
  class BULK_IMPORT_PIPELINE_REPO supportU;
  class GITHUB_GISTS_IMPORTER supportU;
  class GITHUB_GISTS_IMPORTERS supportU;
  class DB_DEV_SNIPPET_FIXTURE initS;
  class API_SNIPPETS_HTTP_MAP errorH;
  class EE_API_SNIPPETS_HELPERS supportU;
  class API_USER_PUBLIC supportU;
  class SPEC_SNIPPET_REPOSITORY supportU;
  class SPEC_IMPORT_EXPORT_SNIPPET supportU;
  class SPEC_SNIPPET_SUPPORT supportU;
  class SPEC_USER_VIEWS_SNIPPETS supportU;
  class QA_RESOURCE_SNIPPET supportU;
  class QA_RESOURCE_PROJECT_SNIPPET supportU;
  class QA_RESOURCE_VISIBILITY supportU;
  class QA_PAGE_SNIPPET_INDEX supportU;
  class QA_PAGE_SNIPPET_SHOW supportU;
  class QA_PAGE_SNIPPET_NEW supportU;
  class QA_PAGE_SNIPPET_EDIT supportU;
  class QA_PAGE_PROJECT_SNIPPET_NEW supportU;
  class QA_PAGE_COMPONENT_SNIPPET supportU;
  class QA_PAGE_COMPONENT_WORKITEM_WIDGETS supportU;
  class QA_EE_PAGE_PROJECT_SNIPPET_INDEX supportU;
  class QA_SPEC_FEATURE_EE_GEO supportU;
```