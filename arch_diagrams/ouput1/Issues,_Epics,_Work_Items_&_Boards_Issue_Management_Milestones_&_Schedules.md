```mermaid
flowchart TD
%% VERTICAL LAYOUT for Issues, Epics, Work Items & Boards/Issue Management/Milestones & Schedules Domain
%% Color scheme:
%%   Core domain files: pastel blue (#D4F1F9)
%%   Supporting/utility files: pastel yellow (#FFF8DC)
%%   Data structure files: pastel green (#E0F8E0)
%%   Error handling: pastel red (#FFE4E1)
%%   Initialization/setup: pastel purple (#E6E6FA)
%%   Subgraphs: very light gray (#F8F8F8) with matching pastel borders
%%   Use rounded rectangles for primary nodes

%% DOMAIN CONCEPTS & DATA LAYER
subgraph S1["Milestone Models & Core Data Structures" ]
direction TB
style S1 fill:#F8F8F8,stroke:#3CA6E3,stroke-width:2,color:#212121

milestone[["Milestone"]]
style milestone fill:#D4F1F9,stroke:#67AFCE,stroke-width:2,color:#212121,stroke-dasharray:5 5,stroke-linejoin:round,rx:10,ry:10

milestoneish[["Milestoneish concern"]]
style milestoneish fill:#E0F8E0,stroke:#B3E6B3,stroke-width:2,color:#212121,rx:10,ry:10

milestone_release[["MilestoneRelease"]]
style milestone_release fill:#D4F1F9,stroke:#67AFCE,stroke-width:2,color:#212121,rx:10,ry:10

resource_milestone_event[["ResourceMilestoneEvent"]]
style resource_milestone_event fill:#D4F1F9,stroke:#67AFCE,stroke-width:2,color:#212121,rx:10,ry:10

iteration[["Iteration EE"]]
style iteration fill:#D4F1F9,stroke:#67AFCE,stroke-width:2,color:#212121,rx:10,ry:10

milestoneable[["Milestoneable concern"]]
style milestoneable fill:#E0F8E0,stroke:#B3E6B3,stroke-width:2,color:#212121,rx:10,ry:10

ee_milestone[["EE::Milestone concern"]]
style ee_milestone fill:#D4F1F9,stroke:#67AFCE,stroke-width:2,color:#212121,rx:10,ry:10

ee_resource_milestone_event[["EE::ResourceMilestoneEvent"]]
style ee_resource_milestone_event fill:#D4F1F9,stroke:#67AFCE,stroke-width:2,color:#212121,rx:10,ry:10

ee_resource_timebox_event[["EE::ResourceTimeboxEvent"]]
style ee_resource_timebox_event fill:#D4F1F9,stroke:#67AFCE,stroke-width:2,color:#212121,rx:10,ry:10

milestone -->|has many| milestone_release
milestone --o milestoneish
milestone -.->|Extended by| ee_milestone
milestoneable -.-> milestone
resource_milestone_event --o milestone
resource_milestone_event -.->|EE Extends| ee_resource_milestone_event
resource_milestone_event --o ee_resource_timebox_event
iteration -.-> milestone
end

%% QUERIES & FINDERS
subgraph S2["Finders & Query Services"]
direction TB
style S2 fill:#F8F8F8,stroke:#EEBA75,stroke-width:2,color:#212121

milestones_finder[["MilestonesFinder"]]
style milestones_finder fill:#FFF8DC,stroke:#EEBA75,stroke-width:2,color:#212121,rx:10,ry:10

resource_milestone_event_finder[["ResourceMilestoneEventFinder"]]
style resource_milestone_event_finder fill:#FFF8DC,stroke:#EEBA75,stroke-width:2,color:#212121,rx:10,ry:10

ee_iterations_cadences_finder[["Iterations::CadencesFinder EE"]]
style ee_iterations_cadences_finder fill:#FFF8DC,stroke:#EEBA75,stroke-width:2,color:#212121,rx:10,ry:10

milestones_finder --o milestone
resource_milestone_event_finder --o resource_milestone_event
ee_iterations_cadences_finder --o iteration
end

%% SERVICES & DOMAIN BEHAVIOR
subgraph S3["Milestone Domain Services"]
direction TB
style S3 fill:#F8F8F8,stroke:#67AFCE,stroke-width:2

milestones_base_service[["Milestones::BaseService"]]
style milestones_base_service fill:#D4F1F9,stroke:#67AFCE,stroke-width:2

create_service[["Milestones::CreateService"]]
style create_service fill:#D4F1F9,stroke:#67AFCE,stroke-width:2

destroy_service[["Milestones::DestroyService"]]
style destroy_service fill:#D4F1F9,stroke:#67AFCE,stroke-width:2

find_or_create_service[["Milestones::FindOrCreateService"]]
style find_or_create_service fill:#D4F1F9,stroke:#67AFCE,stroke-width:2

update_service[["Milestones::UpdateService"]]
style update_service fill:#D4F1F9,stroke:#67AFCE,stroke-width:2

ee_update_service[["EE::Milestones::UpdateService"]]
style ee_update_service fill:#D4F1F9,stroke:#67AFCE,stroke-width:2

close_service[["Milestones::CloseService"]]
style close_service fill:#D4F1F9,stroke:#67AFCE,stroke-width:2

reopen_service[["Milestones::ReopenService"]]
style reopen_service fill:#D4F1F9,stroke:#67AFCE,stroke-width:2

promote_service[["Milestones::PromoteService"]]
style promote_service fill:#D4F1F9,stroke:#67AFCE,stroke-width:2

transfer_service[["Milestones::TransferService"]]
style transfer_service fill:#D4F1F9,stroke:#67AFCE,stroke-width:2

issues_count_service[["Milestones::IssuesCountService"]]
style issues_count_service fill:#D4F1F9,stroke:#67AFCE,stroke-width:2

merge_requests_count_service[["Milestones::MergeRequestsCountService"]]
style merge_requests_count_service fill:#D4F1F9,stroke:#67AFCE,stroke-width:2

closed_issues_count_service[["Milestones::ClosedIssuesCountService"]]
style closed_issues_count_service fill:#D4F1F9,stroke:#67AFCE,stroke-width:2

import_csv_preprocess_service[["ImportCsv::PreprocessMilestonesService"]]
style import_csv_preprocess_service fill:#FFF8DC,stroke:#EEBA75,stroke-width:2

change_milestone_service[["ResourceEvents::ChangeMilestoneService"]]
style change_milestone_service fill:#D4F1F9,stroke:#67AFCE,stroke-width:2

ee_iterations_update_service[["Iterations::UpdateService EE"]]
style ee_iterations_update_service fill:#D4F1F9,stroke:#67AFCE,stroke-width:2

ee_iterations_cadences_update_service[["Iterations::Cadences::UpdateService EE"]]
style ee_iterations_cadences_update_service fill:#D4F1F9,stroke:#67AFCE,stroke-width:2

ee_iterations_cadences_destroy_service[["Iterations::Cadences::DestroyService EE"]]
style ee_iterations_cadences_destroy_service fill:#D4F1F9,stroke:#67AFCE,stroke-width:2

bulk_import_milestones_pipeline[["BulkImports::Common::Pipelines::MilestonesPipeline"]]
style bulk_import_milestones_pipeline fill:#FFF8DC,stroke:#EEBA75,stroke-width:2

create_service --> milestones_base_service
destroy_service --> milestones_base_service
update_service --> milestones_base_service
ee_update_service -.-> update_service
ee_update_service -.->|Calls| epics_update_dates_service(Epics::UpdateDatesService)
update_service --> reopen_service
update_service --> close_service
promote_service --> milestones_base_service
transfer_service --> milestones_base_service
find_or_create_service --> milestones_base_service
change_milestone_service --o milestone
issues_count_service --o milestone
merge_requests_count_service --o milestone
closed_issues_count_service --o milestone
import_csv_preprocess_service --o milestone
bulk_import_milestones_pipeline --o milestone
ee_iterations_update_service --o iteration
ee_iterations_cadences_update_service --o iteration
ee_iterations_cadences_destroy_service --o iteration
end

%% CONTROLLERS LAYER
subgraph S4["Controllers & UI Integration"]
direction TB
style S4 fill:#F8F8F8,stroke:#967EB7,stroke-width:2

groups_milestones_controller[["Groups::MilestonesController"]]
style groups_milestones_controller fill:#E6E6FA,stroke:#967EB7,stroke-width:2

projects_milestones_controller[["Projects::MilestonesController"]]
style projects_milestones_controller fill:#E6E6FA,stroke:#967EB7,stroke-width:2

dashboard_milestones_controller[["Dashboard::MilestonesController"]]
style dashboard_milestones_controller fill:#E6E6FA,stroke:#967EB7,stroke-width:2

milestone_actions[["MilestoneActions concern"]]
style milestone_actions fill:#FFF8DC,stroke:#EEBA75,stroke-width:2

milestones_helper[["MilestonesHelper"]]
style milestones_helper fill:#FFF8DC,stroke:#EEBA75,stroke-width:2

groups_milestones_controller --o milestone_actions
projects_milestones_controller --o milestone_actions
dashboard_milestones_controller --o milestone_actions
milestone_actions --o milestone
milestones_helper --o milestone

end

%% API LAYER
subgraph S5["API Endpoints"]
direction TB
style S5 fill:#F8F8F8,stroke:#35A89F,stroke-width:2

api_milestone_responses[["API::MilestoneResponses"]]
style api_milestone_responses fill:#FFF8DC,stroke:#EEBA75,stroke-width:2

api_project_milestones[["API::ProjectMilestones"]]
style api_project_milestones fill:#D4F1F9,stroke:#35A89F,stroke-width:2

api_resource_milestone_events[["API::ResourceMilestoneEvents"]]
style api_resource_milestone_events fill:#D4F1F9,stroke:#35A89F,stroke-width:2

api_milestone_responses --o milestone
api_project_milestones --o api_milestone_responses
api_resource_milestone_events --o resource_milestone_event

end

%% POLICIES LAYER
subgraph S6["Authorization & Policies"]
direction TB
style S6 fill:#F8F8F8,stroke:#FFE4E1,stroke-width:2

resource_milestone_event_policy[["ResourceMilestoneEventPolicy"]]
style resource_milestone_event_policy fill:#FFE4E1,stroke:#DE8E8E,stroke-width:2

ee_iteration_policy[["IterationPolicy EE"]]
style ee_iteration_policy fill:#FFE4E1,stroke:#DE8E8E,stroke-width:2

ee_iterations_cadence_policy[["Iterations::CadencePolicy EE"]]
style ee_iterations_cadence_policy fill:#FFE4E1,stroke:#DE8E8E,stroke-width:2

resource_milestone_event_policy --o resource_milestone_event
ee_iteration_policy --o iteration
ee_iterations_cadence_policy --o iteration

end

%% GRAPHQL, SERIALIZERS, MAILERS (SUPPORT)
subgraph S7["GraphQL, Serializers, Mailers, Other Supporting Logic"]
direction TB
style S7 fill:#F8F8F8,stroke:#A0C2D1,stroke-width:2

graphql_types_milestone_sort_enum[["Types::MilestoneSortEnum"]]
style graphql_types_milestone_sort_enum fill:#FFF8DC,stroke:#A0C2D1,stroke-width:2

ee_graphql_types_milestone_type[["EE::Types::MilestoneType"]]
style ee_graphql_types_milestone_type fill:#FFF8DC,stroke:#A0C2D1,stroke-width:2

ee_graphql_issues_base_resolver[["EE::Resolvers::Issues::BaseResolver"]]
style ee_graphql_issues_base_resolver fill:#FFF8DC,stroke:#A0C2D1,stroke-width:2

ee_graphql_mutations_set_iteration[["Mutations::Issues::SetIteration"]]
style ee_graphql_mutations_set_iteration fill:#FFF8DC,stroke:#A0C2D1,stroke-width:2

ee_milestone_serializer[["MilestoneSerializer EE"]]
style ee_milestone_serializer fill:#FFF8DC,stroke:#A0C2D1,stroke-width:2

ee_emails_issues[["EE::Emails::Issues"]]
style ee_emails_issues fill:#FFF8DC,stroke:#A0C2D1,stroke-width:2

graphql_types_milestone_sort_enum --o milestone
ee_graphql_types_milestone_type --o milestone
ee_graphql_issues_base_resolver --o milestone
ee_graphql_mutations_set_iteration --o iteration
ee_milestone_serializer --o milestone
ee_emails_issues --o iteration

end

%% DOMAIN TRANSFORMATION & CALLBACKS
subgraph S8["Domain-Specific Data Transformations & Callbacks"]
direction TB
style S8 fill:#F8F8F8,stroke:#67AFCE,stroke-width:2

issuable_callbacks_milestone[["Issuable::Callbacks::Milestone"]]
style issuable_callbacks_milestone fill:#D4F1F9,stroke:#67AFCE,stroke-width:2

issuable_callbacks_milestone --o milestone
issuable_callbacks_milestone --o update_service

end

%% QA, KEEPS, FIXTURES, SPEC INFRASTRUCTURE
subgraph S9["Test Infrastructure, QA Automation, Maintainer Scripts"]
direction TB
style S9 fill:#F8F8F8,stroke:#B3E6B3,stroke-width:2

qa_project_milestone[["QA::Resource::ProjectMilestone"]]
style qa_project_milestone fill:#E0F8E0,stroke:#B3E6B3,stroke-width:2

qa_page_milestone_show[["QA::Page::Milestone::Show"]]
style qa_page_milestone_show fill:#E0F8E0,stroke:#B3E6B3,stroke-width:2

qa_page_milestone_new[["QA::Page::Milestone::New"]]
style qa_page_milestone_new fill:#E0F8E0,stroke:#B3E6B3,stroke-width:2

qa_page_milestone_index[["QA::Page::Milestone::Index"]]
style qa_page_milestone_index fill:#E0F8E0,stroke:#B3E6B3,stroke-width:2

qa_page_group_milestone_new[["QA::Page::Group::Milestone::New"]]
style qa_page_group_milestone_new fill:#E0F8E0,stroke:#B3E6B3,stroke-width:2

qa_page_group_milestone_index[["QA::Page::Group::Milestone::Index"]]
style qa_page_group_milestone_index fill:#E0F8E0,stroke:#B3E6B3,stroke-width:2

spec_gfm_autocomplete[["spec/features/groups/milestones/gfm_autocomplete_spec"]]
style spec_gfm_autocomplete fill:#E0F8E0,stroke:#B3E6B3,stroke-width:2

qa_browser_ui_create_project_milestone[["qa/specs/features/browser_ui/2_plan/milestone/create_project_milestone_spec"]]
style qa_browser_ui_create_project_milestone fill:#E0F8E0,stroke:#B3E6B3,stroke-width:2

qa_browser_ui_create_group_milestone[["qa/specs/features/browser_ui/2_plan/milestone/create_group_milestone_spec"]]
style qa_browser_ui_create_group_milestone fill:#E0F8E0,stroke:#B3E6B3,stroke-width:2

qa_browser_ui_assign_milestone[["qa/specs/features/browser_ui/2_plan/milestone/assign_milestone_spec"]]
style qa_browser_ui_assign_milestone fill:#E0F8E0,stroke:#B3E6B3,stroke-width:2

ee_fixtures_burndown[["ee/db/fixtures/development/20_burndown"]]
style ee_fixtures_burndown fill:#E0F8E0,stroke:#B3E6B3,stroke-width:2

keeps_helpers_milestones[["keeps/helpers/milestones"]]
style keeps_helpers_milestones fill:#E0F8E0,stroke:#B3E6B3,stroke-width:2

qa_project_milestone --o milestone
qa_page_milestone_show --o milestone
qa_page_milestone_new --o milestone
qa_page_milestone_index --o milestone
qa_page_group_milestone_new --o milestone
qa_page_group_milestone_index --o milestone
spec_gfm_autocomplete --o milestone
qa_browser_ui_create_project_milestone --o milestone
qa_browser_ui_create_group_milestone --o milestone
qa_browser_ui_assign_milestone --o milestone
ee_fixtures_burndown --o milestone
keeps_helpers_milestones --o milestone

end

%% RELATIONSHIP LINES
S1 -->|Data Layer feeds| S2
S1 -->|Data Layer feeds| S3
S1 -->|Data Layer feeds| S4
S1 -->|Domain data surfaces to| S5
S1 -->|Domain data checked by| S6
S1 -->|Structures supported by| S7
S1 -->|Data used by| S8
S1 -->|Loaded/Inspected by| S9
S2 --> S3
S3 --> S4
S4 --> S5
S5 --> S6
S4 --> S6
S2 --> S5
S3 --> S5
S6 --> S7
S7 --> S5
S8 --> S3
S9 --> S1
S9 --> S4

%% EXTRA: Key Domain Flows/Transformations
milestone -->|Can be updated/closed| update_service
milestone -->|Has issues| issues_count_service
milestone -->|Can be imported/exported| bulk_import_milestones_pipeline
milestone -->|Given to QA/Specs| qa_project_milestone

update_service -.->|Extended by| ee_update_service
ee_update_service -.->|Triggers Epics| epics_update_dates_service
epics_update_dates_service -.-> milestone

resource_milestone_event -.-> change_milestone_service
qa_project_milestone -.-> qa_page_milestone_show
qa_project_milestone -.-> qa_page_milestone_index
qa_project_milestone -.-> qa_page_milestone_new
qa_project_milestone -.-> qa_page_group_milestone_new

%% Cluster border styling
classDef core fill:#D4F1F9,stroke:#67AFCE,stroke-width:2;
classDef support fill:#FFF8DC,stroke:#EEBA75,stroke-width:2;
classDef data fill:#E0F8E0,stroke:#B3E6B3,stroke-width:2;
classDef error fill:#FFE4E1,stroke:#DE8E8E,stroke-width:2;
classDef init fill:#E6E6FA,stroke:#967EB7,stroke-width:2;
classDef sub fill:#F8F8F8,stroke:#B0BEC5,stroke-width:2;
```