```mermaid
flowchart TD
  %% COLORS
  %% Domain Core: pastel blue #D4F1F9, Utilities: pastel yellow #FFF8DC
  %% Data: pastel green #E0F8E0, Error: pastel red #FFE4E1, Init: pastel purple #E6E6FA, Grouping: #F8F8F8

  %% CORE CLUSTERS
  subgraph S1[Clusters Core Models & Domain Logic]
    direction TB
    style S1 fill:#F8F8F8,stroke:#B2D4EE,stroke-width:2,cornerRadius:12
    CLUSTER["Cluster
app/models/clusters/cluster.rb"]:::core
    INSTANCE["Instance
app/models/clusters/instance.rb"]:::core
    GROUP["Group
app/models/clusters/group.rb"]:::core
    PROJECT["Project
app/models/clusters/project.rb"]:::core
    CLUSTER_ENABLED_GRANT["ClusterEnabledGrant
app/models/clusters/cluster_enabled_grant.rb"]:::core
    PROVIDER_GCP["Providers::Gcp
app/models/clusters/providers/gcp.rb"]:::core
    PROVIDER_AWS["Providers::Aws
app/models/clusters/providers/aws.rb"]:::core
    KUBERNETES_NAMESPACE["KubernetesNamespace
app/models/clusters/kubernetes_namespace.rb"]:::data
    AGENT_TOKEN["AgentToken
app/models/clusters/agent_token.rb"]:::data
    DEPLOYMENT_CLUSTER["DeploymentCluster
app/models/deployment_cluster.rb"]:::core
    CLUSTERS_POLICY["ClusterPolicy
app/policies/clusters/cluster_policy.rb"]:::utility
  end

  %% CONCERNS
  subgraph S2[Behavioral Abstractions / Concerns]
    direction TB
    style S2 fill:#F8F8F8,stroke:#B2D4EE,stroke-width:2,cornerRadius:12
    CONC_KUBERNETES_LOGGER["KubernetesLogger
app/models/clusters/concerns/kubernetes_logger.rb"]:::utility
    CONC_PROVIDER_STATUS["ProviderStatus
app/models/clusters/concerns/provider_status.rb"]:::utility
    CONC_APPLICATION_STATUS["ApplicationStatus
app/models/clusters/concerns/application_status.rb"]:::utility
    CONC_PROM_CLIENT["PrometheusClient
app/models/clusters/concerns/prometheus_client.rb"]:::utility
    CONC_APPLICATION_VERSION["ApplicationVersion
app/models/clusters/concerns/application_version.rb"]:::utility
    CONC_APPLICATION_CORE["ApplicationCore
app/models/clusters/concerns/application_core.rb"]:::utility
  end

  %% CLUSTER AGENTS, AUTH, EVENT ACTIVITY, MANAGED RESOURCES
  subgraph S3[Agents, Authorizations & Activity]
    direction TB
    style S3 fill:#F8F8F8,stroke:#B2D4EE,stroke-width:2,cornerRadius:12
    AGENT_ACTIVITY_EVENT["ActivityEvent
app/models/clusters/agents/activity_event.rb"]:::data
    AGENT_MANAGED_RESOURCE["ManagedResource
app/models/clusters/agents/managed_resource.rb"]:::data
    AUTH_UA_GROUP["UserAccess::GroupAuthorization
app/models/clusters/agents/authorizations/user_access/group_authorization.rb"]:::core
    AUTH_UA_PROJECT["UserAccess::ProjectAuthorization
app/models/clusters/agents/authorizations/user_access/project_authorization.rb"]:::core
    AUTH_CI_IMPLICIT["CiAccess::ImplicitAuthorization
app/models/clusters/agents/authorizations/ci_access/implicit_authorization.rb"]:::core
    AUTH_CI_PROJECT["CiAccess::ProjectAuthorization
app/models/clusters/agents/authorizations/ci_access/project_authorization.rb"]:::core
    AUTH_CI_ORG["CiAccess::OrganizationAuthorization
app/models/clusters/agents/authorizations/ci_access/organization_authorization.rb"]:::core
  end

  %% KUBERNETES CLUSTER RESOURCE ABSTRACTIONS
  subgraph S4[Kubernetes Resource Abstractions]
    direction TB
    style S4 fill:#F8F8F8,stroke:#B2D4EE,stroke-width:2,cornerRadius:12
    KUBE_NAMESPACE_LIB["Namespace
lib/gitlab/kubernetes/namespace.rb"]:::data
    KUBE_POD["Pod
lib/gitlab/kubernetes/pod.rb"]:::data
    KUBE_DEPLOYMENT["Deployment
lib/gitlab/kubernetes/deployment.rb"]:::data
    KUBE_INGRESS["Ingress
lib/gitlab/kubernetes/ingress.rb"]:::data
    **********************["RolloutInstances
lib/gitlab/kubernetes/rollout_instances.rb"]:::utility
    KUBE_ROLLOUT_STATUS["RolloutStatus
lib/gitlab/kubernetes/rollout_status.rb"]:::utility
    KUBE_KUBECLIENT["KubeClient
lib/gitlab/kubernetes/kube_client.rb"]:::utility
    KUBE_SERVICE_ACCOUNT["ServiceAccount
lib/gitlab/kubernetes/service_account.rb"]:::data
    KUBE_SERVICE_ACCOUNT_TOKEN["ServiceAccountToken
lib/gitlab/kubernetes/service_account_token.rb"]:::data
    KUBE_CONFIG_MAP["ConfigMap
lib/gitlab/kubernetes/config_map.rb"]:::data
    KUBE_CLUSTER_ROLE_BIND["ClusterRoleBinding
lib/gitlab/kubernetes/cluster_role_binding.rb"]:::data
    KUBE_ROLE_BINDING["RoleBinding
lib/gitlab/kubernetes/role_binding.rb"]:::data
    KUBE_ROLE["Role
lib/gitlab/kubernetes/role.rb"]:::data
    KUBE_TLS_SECRET["TlsSecret
lib/gitlab/kubernetes/tls_secret.rb"]:::data
    KUBE_GENERIC_SECRET["GenericSecret
lib/gitlab/kubernetes/generic_secret.rb"]:::data
    KUBE_LOGGER["Logger
lib/gitlab/kubernetes/logger.rb"]:::utility
    KUBE_NODE["Node
lib/gitlab/kubernetes/node.rb"]:::data
    KUBE_DEFAULT_NAMESPACE["DefaultNamespace
lib/gitlab/kubernetes/default_namespace.rb"]:::data
    KUBE_HELM["Helm
lib/gitlab/kubernetes/helm.rb"]:::utility
    KUBE_POD_CMD["PodCmd
lib/gitlab/kubernetes/pod_cmd.rb"]:::utility
    KUBE_KUBECTL_CMD["KubectlCmd
lib/gitlab/kubernetes/kubectl_cmd.rb"]:::utility
    KUBERNETES["Kubernetes Module
lib/gitlab/kubernetes.rb"]:::utility
    KUBE_ERRORS["Errors
lib/gitlab/kubernetes/errors.rb"]:::error
    KUBE_CM_AWS_NODE_AUTH["ConfigMaps::AwsNodeAuth
lib/gitlab/kubernetes/config_maps/aws_node_auth.rb"]:::data
  end

  %% CONTROLLERS & HELPERS
  subgraph S5[Cluster Controllers, API, and UI]
    direction TB
    style S5 fill:#F8F8F8,stroke:#B2D4EE,stroke-width:2,cornerRadius:12
    CONTROLLERS_CLUSTERS["ClustersController
app/controllers/clusters/clusters_controller.rb"]:::core
    CONTROLLERS_PROJECTS["Projects::ClustersController
app/controllers/projects/clusters_controller.rb"]:::core
    CONTROLLERS_BASE["BaseController
app/controllers/clusters/base_controller.rb"]:::utility
    CLUSTERS_HELPER["ClustersHelper
app/helpers/clusters_helper.rb"]:::utility
    CLUSTERS_FINDER["ClustersFinder
app/finders/clusters_finder.rb"]:::utility
    CLUSTER_ENTITY["ClusterEntity
app/serializers/cluster_entity.rb"]:::utility
    KNATIVE_SERVICES_FINDER["KnativeServicesFinder
app/finders/clusters/knative_services_finder.rb"]:::utility
    CLUSTER_INTEGRATION_PRESENTER["IntegrationPresenter
app/presenters/clusters/integration_presenter.rb"]:::utility
  end

  %% CLUSTER SERVICES, MANAGEMENT, LIFECYCLE
  subgraph S6[Cluster & Kubernetes Service Layer]
    direction TB
    style S6 fill:#F8F8F8,stroke:#B2D4EE,stroke-width:2,cornerRadius:12
    BUILD_KUBERN_NAMESPACE["BuildKubernetesNamespaceService
app/services/clusters/build_kubernetes_namespace_service.rb"]:::core
    BUILD_SERVICE["BuildService
app/services/clusters/build_service.rb"]:::core
    CREATE_SERVICE["CreateService
app/services/clusters/create_service.rb"]:::core
    DESTROY_SERVICE["DestroyService
app/services/clusters/destroy_service.rb"]:::core
    UPDATE_SERVICE["UpdateService
app/services/clusters/update_service.rb"]:::core
    CLUSTER_KUBERNETES["Kubernetes namespace
app/services/clusters/kubernetes.rb"]:::core
    K8S_CREATE_NAMESPACE["Kubernetes::CreateOrUpdateNamespaceService
app/services/clusters/kubernetes/create_or_update_namespace_service.rb"]:::core
    K8S_CREATE_SA["Kubernetes::CreateOrUpdateServiceAccountService
app/services/clusters/kubernetes/create_or_update_service_account_service.rb"]:::core
    K8S_FETCH_TOKEN["Kubernetes::FetchKubernetesTokenService
app/services/clusters/kubernetes/fetch_kubernetes_token_service.rb"]:::core
    CLEANUP_PROJECT_NAMESPACE_SERVICE["Cleanup::ProjectNamespaceService
app/services/clusters/cleanup/project_namespace_service.rb"]:::utility
    CLEANUP_SERVICE_ACCOUNT_SERVICE["Cleanup::ServiceAccountService
app/services/clusters/cleanup/service_account_service.rb"]:::utility
    MANAGEMENT_VALIDATE_MGPROJECT["Management::ValidateManagementProjectPermissionsService
app/services/clusters/management/validate_management_project_permissions_service.rb"]:::utility
    %% MIGRATION & AGENT SERVICES
    MIGRATION_CREATE["Migration::CreateService
app/services/clusters/migration/create_service.rb"]:::core
    MIGRATION_UPDATE["Migration::UpdateService
app/services/clusters/migration/update_service.rb"]:::core
    MIGRATION_INSTALL_AGENT["Migration::InstallAgentService
app/services/clusters/migration/install_agent_service.rb"]:::core
    %% AGENTS SERVICES
    AGENTS_CREATE["Agents::CreateService
app/services/clusters/agents/create_service.rb"]:::core
    AGENTS_DELETE["Agents::DeleteService
app/services/clusters/agents/delete_service.rb"]:::core
    AGENTS_DELETE_EVENTS["Agents::DeleteExpiredEventsService
app/services/clusters/agents/delete_expired_events_service.rb"]:::utility
    AGENTS_CREATE_EVENT["Agents::CreateActivityEventService
app/services/clusters/agents/create_activity_event_service.rb"]:::utility
    AGENTS_MANAGED_RES_DELETE["Agents::ManagedResources::DeleteService
app/services/clusters/agents/managed_resources/delete_service.rb"]:::utility
    %% AGENT TOKENS
    AGENTTOKENS_CREATE["AgentTokens::CreateService
app/services/clusters/agent_tokens/create_service.rb"]:::utility
    AGENTTOKENS_TRACKUSAGE["AgentTokens::TrackUsageService
app/services/clusters/agent_tokens/track_usage_service.rb"]:::utility
  end

  %% WORKERS: CLUSTER / KUBE ASYNC
  subgraph S7[Cluster Infrastructure Workers]
    direction TB
    style S7 fill:#F8F8F8,stroke:#B2B0EA,stroke-width:2,cornerRadius:12

    WORKER_CLEANUP_SA["Cleanup::ServiceAccountWorker
app/workers/clusters/cleanup/service_account_worker.rb"]:::init
    WORKER_CLEANUP_NS["Cleanup::ProjectNamespaceWorker
app/workers/clusters/cleanup/project_namespace_worker.rb"]:::init
    WORKER_MIGRATION_INSTALL["Migration::InstallAgentWorker
app/workers/clusters/migration/install_agent_worker.rb"]:::init
    WORKER_APPS_DEACTIVATE["Applications::DeactivateIntegrationWorker
app/workers/clusters/applications/deactivate_integration_worker.rb"]:::init
    WORKER_APPS_ACTIVATE["Applications::ActivateIntegrationWorker
app/workers/clusters/applications/activate_integration_worker.rb"]:::init
    WORKER_APPS_WAIT_UNINSTALL["Applications::WaitForUninstallAppWorker
app/workers/clusters/applications/wait_for_uninstall_app_worker.rb"]:::init
    WORKER_APPS_UNINSTALL["Applications::UninstallWorker
app/workers/clusters/applications/uninstall_worker.rb"]:::init
  end

  %% QA, TOOLING, TEST, SUPPORT
  subgraph S8[Tooling & Cluster Environment Support]
    direction TB
    style S8 fill:#F8F8F8,stroke:#F3D36E,stroke-width:2,cornerRadius:12
    TOOLING_K8S_CLIENT["KubernetesClient
tooling/lib/tooling/kubernetes_client.rb"]:::utility
    CI_KUBERNETES_NS["Ci::Build::Prerequisite::KubernetesNamespace
lib/gitlab/ci/build/prerequisite/kubernetes_namespace.rb"]:::utility
    SCRIPTS_AUTOMATED_CLEANUP["ReviewApps::AutomatedCleanup
scripts/review_apps/automated_cleanup.rb"]:::utility
  end

  %% DATA STRUCTURES & SERIALIZERS
  subgraph S9[Cluster Data Structures, Serialization & Visualization]
    direction TB
    style S9 fill:#F8F8F8,stroke:#74E6A6,stroke-width:2,cornerRadius:12
    CLUSTER_DEPLOY_ENTITY["DeploymentEntity
ee/app/serializers/clusters/deployment_entity.rb"]:::data
  end

  %% Nodes classes
  classDef core fill:#D4F1F9,stroke:#88C7E8,stroke-width:2,color:#2B4558,stroke-dasharray:1 6,stroke-linecap:round
  classDef data fill:#E0F8E0,stroke:#78D88E,stroke-width:2,color:#234828
  classDef utility fill:#FFF8DC,stroke:#E8C989,stroke-width:2,color:#4F3E07
  classDef error fill:#FFE4E1,stroke:#F39797,stroke-width:2,color:#600F0F
  classDef init fill:#E6E6FA,stroke:#BDB7E6,stroke-width:2,color:#534E65

  %% MAIN CLUSTERS RELATIONS / DOMAIN KERNEL  
  CLUSTER -.->|uses| PROVIDER_GCP
  CLUSTER -.->|uses| PROVIDER_AWS
  CLUSTER -- calls --> KUBERNETES_NAMESPACE
  CLUSTER -- references --> AGENT_TOKEN
  PROJECT -- isClusterable --> CLUSTER
  GROUP -- isClusterable --> CLUSTER
  INSTANCE -- isClusterable --> CLUSTER
  CLUSTER_ENABLED_GRANT --forbids--> CLUSTER
  KUBERNETES_NAMESPACE --belongsTo--> PROJECT
  KUBERNETES_NAMESPACE --belongsTo--> CLUSTER
  KUBERNETES_NAMESPACE --uses-> KUBE_NAMESPACE_LIB
  PROVIDER_GCP --state--> CONC_PROVIDER_STATUS
  PROVIDER_AWS --state--> CONC_PROVIDER_STATUS

  %% CONCERNS LINKAGE
  CLUSTER --includes--> CONC_PROVIDER_STATUS
  CLUSTER --includes--> CONC_APPLICATION_STATUS
  CLUSTER --includes--> CONC_APPLICATION_CORE
  CONC_APPLICATION_CORE --uses--> CONC_KUBERNETES_LOGGER
  CONC_APPLICATION_STATUS --reliesOn--> CONC_APPLICATION_VERSION
  CONC_PROM_CLIENT --wraps->|Prometheus Adapter| KUBE_NODE
  PROVIDER_AWS --includes--> CONC_PROVIDER_STATUS
  PROVIDER_GCP --includes--> CONC_PROVIDER_STATUS

  %% AGENTS, AUTHORISATIONS, ACTIVITIES
  AGENT_ACTIVITY_EVENT --records--> AGENT_MANAGED_RESOURCE
  AGENT_ACTIVITY_EVENT --belongsTo--> AGENT_TOKEN
  AGENT_ACTIVITY_EVENT --relatesTo--> AGENTTOKENS_CREATE
  AGENT_ACTIVITY_EVENT --triggers--> AGENTS_DELETE_EVENTS

  AUTH_UA_PROJECT --references--> AGENT_MANAGED_RESOURCE
  AUTH_UA_PROJECT --references--> AGENT_ACTIVITY_EVENT
  AUTH_UA_GROUP --references--> AGENT_ACTIVITY_EVENT
  AUTH_CI_IMPLICIT --for--> AGENT_MANAGED_RESOURCE
  AUTH_CI_PROJECT --for--> AGENT_MANAGED_RESOURCE
  AUTH_CI_ORG --for--> AGENT_MANAGED_RESOURCE

  %% KUBERNETES RESOURCE ABSTRACTIONS
  KUBERNETES_NAMESPACE --uses--> KUBE_NAMESPACE_LIB
  KUBERNETES_NAMESPACE --defines--> KUBE_DEFAULT_NAMESPACE
  KUBERNETES_NAMESPACE --uses--> KUBE_SERVICE_ACCOUNT
  KUBE_NAMESPACE_LIB --wraps--> KUBE_NODE
  KUBE_DEPLOYMENT --uses--> KUBE_POD
  KUBE_DEPLOYMENT --uses--> **********************
  ********************** --computes--> KUBE_ROLLOUT_STATUS
  KUBE_ROLLOUT_STATUS --aggregates--> KUBE_DEPLOYMENT
  KUBE_INGRESS --connects--> KUBE_DEPLOYMENT
  KUBE_DEFAULT_NAMESPACE --yields--> KUBERNETES_NAMESPACE

  %% CONTROLLERS, HELPERS, API FLOW
  CONTROLLERS_BASE --baseFor--> CONTROLLERS_CLUSTERS
  CONTROLLERS_CLUSTERS --subclassedBy--> CONTROLLERS_PROJECTS
  CONTROLLERS_CLUSTERS --uses--> CLUSTERS_FINDER
  CONTROLLERS_CLUSTERS --uses--> CLUSTERS_HELPER
  CONTROLLERS_CLUSTERS --uses--> CLUSTER_ENTITY
  CONTROLLERS_PROJECTS --uses--> CLUSTERS_FINDER
  CONTROLLERS_PROJECTS --uses--> CLUSTERS_HELPER
  CONTROLLERS_PROJECTS --uses--> CLUSTER_ENTITY

  %% SERVICE LAYER: CRUD, LIFECYCLE, MANAGEMENT
  BUILD_SERVICE --builds--> CLUSTER
  CREATE_SERVICE --creates--> CLUSTER
  UPDATE_SERVICE --modifies--> CLUSTER
  DESTROY_SERVICE --removes--> CLUSTER
  BUILD_KUBERN_NAMESPACE --populates--> KUBERNETES_NAMESPACE

  CLEANUP_PROJECT_NAMESPACE_SERVICE --executesOn--> CLEANUP_PROJECT_NAMESPACE_SERVICE
  CLEANUP_SERVICE_ACCOUNT_SERVICE --destroys--> CLUSTER
  MANAGEMENT_VALIDATE_MGPROJECT --checks--> CLUSTER

  %% MIGRATION & AGENT LIFECYCLE
  MIGRATION_CREATE --creates--> CLUSTER
  MIGRATION_UPDATE --modifies--> CLUSTER
  MIGRATION_INSTALL_AGENT --wraps--> KUBE_KUBECLIENT
  MIGRATION_INSTALL_AGENT --installsOn--> CLUSTER

  AGENTS_CREATE --creates--> AGENT_MANAGED_RESOURCE
  AGENTS_DELETE --removes--> AGENT_MANAGED_RESOURCE
  AGENTS_MANAGED_RES_DELETE --removes--> AGENT_MANAGED_RESOURCE
  AGENTS_CREATE_EVENT --creates--> AGENT_ACTIVITY_EVENT
  AGENTS_DELETE_EVENTS --cleans--> AGENT_ACTIVITY_EVENT

  AGENTTOKENS_CREATE --creates--> AGENT_TOKEN
  AGENTTOKENS_CREATE --logs--> AGENT_ACTIVITY_EVENT
  AGENTTOKENS_TRACKUSAGE --tracks--> AGENT_TOKEN

  %% KUBERNETES SERVICE OPERATIONS
  K8S_CREATE_NAMESPACE --creates--> KUBERNETES_NAMESPACE
  K8S_CREATE_NAMESPACE --calls--> K8S_CREATE_SA
  K8S_CREATE_SA --provisions--> KUBE_SERVICE_ACCOUNT
  K8S_CREATE_SA --sets--> KUBE_SERVICE_ACCOUNT_TOKEN
  K8S_FETCH_TOKEN --retrieves--> KUBE_SERVICE_ACCOUNT_TOKEN

  %% WORKER RELATIONSHIPS
  WORKER_CLEANUP_SA --executes--> CLEANUP_SERVICE_ACCOUNT_SERVICE
  WORKER_CLEANUP_NS --executes--> CLEANUP_PROJECT_NAMESPACE_SERVICE
  WORKER_MIGRATION_INSTALL --executes--> MIGRATION_INSTALL_AGENT
  WORKER_APPS_DEACTIVATE --calls--> UPDATE_SERVICE
  WORKER_APPS_ACTIVATE --calls--> UPDATE_SERVICE
  WORKER_APPS_WAIT_UNINSTALL --polls--> KUBE_DEPLOYMENT
  WORKER_APPS_UNINSTALL --executes--> DESTROY_SERVICE

  %% QA/TOOLING/ENVIRONMENT SUPPORT
  TOOLING_K8S_CLIENT --tests--> KUBE_KUBECLIENT
  CI_KUBERNETES_NS --checks--> KUBERNETES_NAMESPACE
  SCRIPTS_AUTOMATED_CLEANUP --deletes--> KUBERNETES_NAMESPACE

  %% SERIALIZER PIPELINE
  CLUSTER --representedBy--> CLUSTER_ENTITY
  AGENT_MANAGED_RESOURCE --representedBy--> CLUSTER_DEPLOY_ENTITY

  %% COMMON DOMAIN FLOWS
  CONTROLLERS_CLUSTERS --calls--> CREATE_SERVICE
  CONTROLLERS_CLUSTERS --calls--> UPDATE_SERVICE
  CONTROLLERS_CLUSTERS --calls--> DESTROY_SERVICE
  CONTROLLERS_PROJECTS --calls--> CREATE_SERVICE
  CONTROLLERS_PROJECTS --calls--> UPDATE_SERVICE
  CONTROLLERS_PROJECTS --calls--> DESTROY_SERVICE

  CLUSTER --has--> AGENT_MANAGED_RESOURCE
  CLUSTER --grants--> AUTH_UA_PROJECT
  CLUSTER --grants--> AUTH_UA_GROUP
  CLUSTER --supports--> AUTH_CI_IMPLICIT
  CLUSTER --supports--> AUTH_CI_PROJECT
  CLUSTER --supports--> AUTH_CI_ORG
  CLUSTER --references--> PROVIDER_AWS
  CLUSTER --references--> PROVIDER_GCP

  %% MERGE/AGGREGATE APPLICATION STATUS
  CLUSTER --usesStatusFrom--> CONC_APPLICATION_STATUS
  CONC_APPLICATION_STATUS --combines--> KUBE_DEPLOYMENT

  %% KUBERNETES OBJECT TRANSFORMATION
  BUILD_KUBERN_NAMESPACE --invokes--> KUBE_NAMESPACE_LIB
  KUBERNETES_NAMESPACE --uses--> KUBE_LOGGER
  KUBE_NAMESPACE_LIB --logsTo--> KUBE_LOGGER

  %% Style subgraph titles
  linkStyle default stroke-width:1.5,stroke:#D1D1D1
```