```mermaid
flowchart TB
  %% LAYOUT AND STYLES
  %% Vertical layout, node colors, shapes/styles as per guidelines
  %% Color palette
  %% Core Domain: #D4F1F9 (pastel blue)
  %% Supporting/utility: #FFF8DC (pastel yellow)
  %% Data Structures: #E0F8E0 (pastel green)
  %% Error/EH: #FFE4E1 (pastel red)
  %% Initialization/Setup: #E6E6FA (pastel purple)
  %% Group/Cluster: border pastel, bg #F8F8F8
  %% Main Nodes: rounded rectangles

  %% Definitions for node style
  classDef coreDomain fill:#D4F1F9,stroke:#96B6C5,stroke-width:2px,color:#222,rx:10,ry:10
  classDef utility fill:#FFF8DC,stroke:#E6DFA4,stroke-width:2px,color:#222,rx:10,ry:10
  classDef data fill:#E0F8E0,stroke:#A6DFB9,stroke-width:2px,color:#222,rx:10,ry:10
  classDef error fill:#FFE4E1,stroke:#FFC1CC,stroke-width:2px,color:#222,rx:10,ry:10
  classDef init fill:#E6E6FA,stroke:#C6B8E8,stroke-width:2px,color:#222,rx:10,ry:10
  classDef eeBorder stroke:#A6BFFF,stroke-width:2px

  %% --- DATA/ENTITY LAYER ---
  subgraph S1["Domain Models & Data Structures" ]
    direction TB
    style S1 fill:#F8F8F8,stroke:#96B6C5,stroke-width:2px

    pat_model["PersonalAccessToken.rb - PAT ActiveRecord Entity" ]
    pat_model_last_ip["authn/personal_access_token_last_used_ip.rb - Last Used IP Model" ]
    class pat_model,pat_model_last_ip coreDomain

    ee_pat_model["ee/personal_access_token.rb - PAT Enterprise Extension" ]
    class ee_pat_model coreDomain eeBorder

    pat_entity["personal_access_token_entity.rb - PAT API Serializer" ]
    impersonation_pat_entity["impersonation_access_token_entity.rb - Impersonation PAT Serializer" ]
    class pat_entity,impersonation_pat_entity data

    pat_token_struct["authn/tokens/personal_access_token.rb - PAT Token Prefix & Lookup" ]
    class pat_token_struct data

    attr_encrypted["vendor/gems/attr_encrypted.rb - Encryption Support" ]
    attr_encrypted_ar["vendor/gems/attr_encrypted/adapters/active_record.rb - ActiveRecord Adapter" ]
    attr_encrypted_sequel["vendor/gems/attr_encrypted/adapters/sequel.rb - Sequel Adapter" ]
    class attr_encrypted,attr_encrypted_ar,attr_encrypted_sequel utility

    %%"PAT Data relationships"
    pat_model --|Has many| pat_model_last_ip
    pat_model --|Serialized to API| pat_entity
    pat_model --|Extension| ee_pat_model
    pat_model --|Token Structure| pat_token_struct

    attr_encrypted -.-> pat_model
    attr_encrypted_ar -.-> pat_model
    attr_encrypted_sequel -.-> pat_model

    impersonation_pat_entity -. Uses .-> pat_entity

    ee_pat_model --|Extends| pat_model

  end

  %% --- AUTH LOGIC: CREATION, REVOCATION, LIFECYCLE ---
  subgraph S2["PAT Lifecycle & Service Layer" ]
    direction TB
    style S2 fill:#F8F8F8,stroke:#96B6C5,stroke-width:2px

    pat_create_svc["personal_access_tokens/create_service.rb<br/>- PAT Creator" ]
    pat_revoke_svc["personal_access_tokens/revoke_service.rb<br/>- PAT Revoker" ]
    pat_rotate_svc["personal_access_tokens/rotate_service.rb<br/>- PAT Rotator" ]
    pat_revoke_family_svc["personal_access_tokens/revoke_token_family_service.rb<br/>- Revoke PAT Family" ]
    pat_last_used_svc["personal_access_tokens/last_used_service.rb<br/>- Track Last Used" ]
    pat_expiring_worker["personal_access_tokens/expiring_worker.rb<br/>- Expiring PAT Notification Worker" ]
    pat_expired_notif_worker["personal_access_tokens/expired_notification_worker.rb<br/>- Expired PAT Notify Worker" ]

    class pat_create_svc,pat_revoke_svc,pat_rotate_svc,pat_revoke_family_svc,pat_last_used_svc,pat_expiring_worker,pat_expired_notif_worker coreDomain

    %% Resource/project/group tokens
    resource_create_svc["resource_access_tokens/create_service.rb<br/>- Resource Token Creator" ]
    resource_revoke_svc["resource_access_tokens/revoke_service.rb<br/>- Resource Token Revoker" ]
    proj_rotate_svc["project_access_tokens/rotate_service.rb<br/>- Project PAT Rotator" ]
    group_rotate_svc["group_access_tokens/rotate_service.rb<br/>- Group PAT Rotator" ]
    class resource_create_svc,resource_revoke_svc,proj_rotate_svc,group_rotate_svc coreDomain

    %% Enterprise/EE services
    ee_pat_revoke_svc["ee/personal_access_tokens/revoke_service.rb<br/>- EE PAT Revoker Audit" ]
    ee_pat_invalid_revoke["ee/personal_access_tokens/revoke_invalid_tokens.rb<br/>- EE Revoke Invalid Tokens" ]
    ee_pat_update_lifetime_grp["personal_access_tokens/groups/update_lifetime_service.rb<br/>- EE PAT Group Lifetime" ]
    ee_pat_update_lifetime_inst["personal_access_tokens/instance/update_lifetime_service.rb<br/>- EE PAT Instance Lifetime" ]
    class ee_pat_revoke_svc,ee_pat_invalid_revoke,ee_pat_update_lifetime_grp,ee_pat_update_lifetime_inst coreDomain eeBorder

    %% PAT Services Relationships & Flows
    pat_create_svc --|Creates| pat_model
    resource_create_svc --|Creates| pat_model
    resource_revoke_svc --|Revokes| pat_model
    pat_revoke_svc --|Revokes| pat_model
    pat_rotate_svc --|Rotates| pat_model
    pat_revoke_family_svc --|Revokes Family| pat_model
    pat_last_used_svc --|Tracks last used| pat_model
    pat_expiring_worker --|Notifies about expiring| pat_model
    pat_expired_notif_worker --|Notifies expired| pat_model
    pat_expiring_worker --|Uses| pat_last_used_svc

    %% Resource token services extend core
    resource_create_svc --|Subclass of| pat_create_svc
    resource_revoke_svc --|Subclass of| pat_revoke_svc
    proj_rotate_svc --|Extends| pat_rotate_svc
    group_rotate_svc --|Extends| pat_rotate_svc

    %% EE Service Connections
    ee_pat_revoke_svc --|Augments| pat_revoke_svc
    ee_pat_invalid_revoke --|Revokes invalid| pat_model
    ee_pat_update_lifetime_grp --|Batch update| pat_model
    ee_pat_update_lifetime_inst --|Batch update| pat_model

    ee_pat_update_lifetime_grp --|Extends| ee_pat_update_lifetime_inst

  end

  %% --- POLICIES, PERMISSIONS, HELPERS, ERROR HANDLING ---
  subgraph S3["Policies, Helpers, Error Handling"]
    direction TB
    style S3 fill:#F8F8F8,stroke:#96B6C5,stroke-width:2px

    crud_policy_helpers["policies/concerns/crud_policy_helpers.rb<br/>- CRUD Policy Helpers" ]
    pat_policy["ee/personal_access_token_policy.rb<br/>- EE PAT Policy Extension" ]
    access_token_helpers["ee/access_tokens_helper.rb<br/>- EE Access Tokens View Helper" ]
    pat_helpers["ee/personal_access_tokens_helper.rb<br/>- EE PAT Helpers" ]
    class crud_policy_helpers utility
    class pat_policy coreDomain eeBorder
    class access_token_helpers utility eeBorder
    class pat_helpers utility eeBorder

    %% Relationships
    pat_policy --|Augments| crud_policy_helpers
    access_token_helpers --|Uses| pat_helpers
    pat_helpers --|Checks policy| pat_policy
    access_token_helpers --|Helper for| pat_entity

  end

  %% --- CONTROLLERS: WEB/MVC INTERFACE & API GATEWAYS ---
  subgraph S4["Web/API Entry Points"]
    direction TB
    style S4 fill:#F8F8F8,stroke:#96B6C5,stroke-width:2px

    pat_controller["user_settings/personal_access_tokens_controller.rb<br/>- User PAT Controller"]
    ee_pat_controller["ee/user_settings/personal_access_tokens_controller.rb<br/>- EE PAT Controller Extension"]
    admin_applications_ctrl["admin/applications_controller.rb<br/>- Admin OAuth Apps Controller"]
    pat_ctrl_concern["controllers/concerns/access_tokens_actions.rb<br/>- PAT Action Concern"]

    pat_api["lib/api/personal_access_tokens.rb<br/>- PAT API Endpoint"]
    pat_api_self_rotation["lib/api/personal_access_tokens/self_rotation.rb<br/>- PAT Self-Rotation API"]
    pat_api_self_info["lib/api/personal_access_tokens/self_information.rb<br/>- PAT Self-Info API"]

    resource_api_self_rotation["lib/api/resource_access_tokens/self_rotation.rb<br/>- Resource PAT Rotation API"]

    class pat_controller,ee_pat_controller, admin_applications_ctrl coreDomain
    class pat_ctrl_concern utility
    class pat_api,pat_api_self_rotation,pat_api_self_info,resource_api_self_rotation coreDomain

    %% Relationships
    pat_controller --|Extends| pat_ctrl_concern
    pat_controller --|Handles| pat_create_svc
    pat_controller --|Handles| pat_revoke_svc
    ee_pat_controller --|Extends| pat_controller
    ee_pat_controller --|Augments| pat_helpers

    pat_api --|API to Services| pat_create_svc
    pat_api --|API to Services| pat_revoke_svc
    pat_api --|Serializes| pat_entity
    pat_api_self_rotation --|Extends| pat_api
    pat_api_self_info --|Extends| pat_api

    resource_api_self_rotation --|Uses| resource_create_svc

  end

  %% --- NOTIFICATIONS/MAILERS ---
  subgraph S5["Notifications & Workers"]
    direction TB
    style S5 fill:#F8F8F8,stroke:#96B6C5,stroke-width:2px

    pat_expiring_worker
    pat_expired_notif_worker
    pat_policy_worker["ee/app/workers/personal_access_tokens/instance/policy_worker.rb<br/>- Policy Enforcement Worker" ]
    credentials_mailer["ee/app/mailers/credentials_inventory_mailer.rb<br/>- Credentials Inventory Mailer" ]
    pat_profile_mailer["ee/app/mailers/ee/emails/profile.rb<br/>- PAT Profile Notification Mailer"]

    class pat_expiring_worker,pat_expired_notif_worker,pat_policy_worker,credentials_mailer,pat_profile_mailer coreDomain eeBorder

    pat_policy_worker --|Enforces| ee_pat_update_lifetime_inst
    pat_expiring_worker --|Triggers| credentials_mailer
    pat_expiring_worker --|Triggers| pat_profile_mailer
    pat_expired_notif_worker --|Triggers| credentials_mailer
    ee_pat_invalid_revoke --|Notifies| pat_profile_mailer
  end

  %% --- EE/ENTERPRISE LOGIC & SPECIAL CASES ---
  subgraph S6["EE-Specific Expiration, Limits, Validation & Utility"]
    direction TB
    style S6 fill:#F8F8F8,stroke:#A6BFFF,stroke-width:2px

    pat_expiry_validator["ee/lib/ee/gitlab/personal_access_tokens/service_account_token_validator.rb<br/>- PAT Expiry Validator"]
    pat_expiry_calc["ee/lib/ee/gitlab/personal_access_tokens/expiry_date_calculator.rb<br/>- Expiry Date Calculator"]
    cloud_connector_tokens["ee/lib/cloud_connector/tokens.rb<br/>- CloudConnector: PAT Issuance/Claims"]

    class pat_expiry_validator,pat_expiry_calc,cloud_connector_tokens utility eeBorder

    pat_expiry_validator --|Validates| pat_model
    pat_expiry_validator --|Checks| ee_pat_model
    pat_expiry_calc --|Calculates expiry for| pat_model
    cloud_connector_tokens --|Issues PAT for| pat_model

  end

  %% --- LOWER-LEVEL AUTH SUPPORT ---
  subgraph S7["Auth/Key/DPOP Lower Layers"]
    direction TB
    style S7 fill:#F8F8F8,stroke:#96B6C5,stroke-width:2px

    key_status_checker["lib/gitlab/auth/key_status_checker.rb<br/>- Key Status Checker"]
    dpop_token_user["lib/gitlab/auth/dpop_token_user.rb<br/>- DPoP Token User" ]
    pat_kas_user_access["lib/gitlab/kas/user_access.rb<br/>- KAS Cookie Auth" ]
    keys_last_used["services/keys/last_used_service.rb<br/>- Keys Last Used Service"]
    smartcard["ee/lib/gitlab/auth/smartcard.rb<br/>- Smartcard Integration"]

    class key_status_checker, dpop_token_user, pat_kas_user_access, keys_last_used, smartcard utility

    keys_last_used --|Updates| pat_model_last_ip
    key_status_checker --|Checks| pat_model
    dpop_token_user --|Validates| pat_model
    pat_kas_user_access --|Secures| pat_controller

    smartcard --|Enforces| pat_controller

  end

  %% --- TESTS and QA AUTOMATION ---
  subgraph S8["Test & Quality Automation"]
    direction TB
    style S8 fill:#F8F8F8,stroke:#AACFE7,stroke-width:2px

    pat_controller_spec["spec/controllers/user_settings/personal_access_tokens_controller_spec.rb<br/>- PAT Controller Spec"]
    qa_pat_spec["qa/specs/features/browser_ui/10_govern/user/impersonation_token_spec.rb<br/>- QA Impersonation Token UI Spec"]
    qa_project_token_spec["qa/specs/features/browser_ui/10_govern/project/project_access_token_spec.rb<br/>- QA Project Token UI Spec"]
    qa_pat_page["qa/page/profile/personal_access_tokens.rb<br/>- QA PAT Feature Page"]
    qa_pat_resource["qa/resource/personal_access_token.rb<br/>- QA PAT Resource"]
    qa_group_pat_resource["qa/resource/group_access_token.rb<br/>- QA Group PAT Resource"]
    qa_project_pat_resource["qa/resource/project_access_token.rb<br/>- QA Project PAT Resource"]

    class pat_controller_spec,qa_pat_spec,qa_project_token_spec,qa_pat_page,qa_pat_resource,qa_group_pat_resource,qa_project_pat_resource utility

    pat_controller_spec --|Specs| pat_controller
    qa_pat_spec --|Tests| pat_controller
    qa_project_token_spec --|Tests| proj_rotate_svc
    qa_pat_page --|Uses| qa_pat_resource
    qa_pat_page --|Tests| pat_controller
    qa_pat_resource --|Calls API| pat_api
    qa_group_pat_resource --|Calls API| resource_api_self_rotation
    qa_project_pat_resource --|Calls API| resource_api_self_rotation

  end

  %% --- REMOTE DEV / CROSS-BOUNDARY ---
  subgraph S9["Remote Dev & Integration Hooks"]
    direction TB
    style S9 fill:#F8F8F8,stroke:#A6BFFF,stroke-width:2px

    remote_pat_creator["ee/lib/remote_development/workspace_operations/create/personal_access_token_creator.rb<br/>- PAT RemoteDev Creator"]

    class remote_pat_creator utility eeBorder

    remote_pat_creator --|Uses| pat_create_svc
  end

  %% --- KEY RELATIONSHIP LINES (between subdomains) ---
  %% Entity <-> Service
  S1 --|Uses/returns| S2
  S2 --|Checks| S3
  S3 -. Used by .-> S4
  S4 --|API Gateways| S2
  S2 --|Triggers/Notifies| S5
  S2 --|Validates Expiry| S6
  S2 --|Uses encryption| S1
  S1 --|Data Used by| S8
  S2 --|PAT creation for remote dev| S9

  %% EE/Extension cross-cuts
  S2 --|Overrides/extends| S6
  S6 --|Enterprise controls| S5

  %% Lower layer utilities cross-cut domain
  S7 --|Support| S2
  S7 --|Support| S1
  S7 --|Support| S4
  S7 --|Security| S3

  %% Test & QA validate all flows
  S8 --|E2E/feature tests| S4
  S8 --|E2E/feature tests| S2
  S8 --|Uses domain data| S1
```