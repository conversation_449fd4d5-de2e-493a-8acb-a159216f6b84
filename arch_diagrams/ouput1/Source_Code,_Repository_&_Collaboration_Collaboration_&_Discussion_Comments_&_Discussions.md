```mermaid
flowchart TD
  %% DOMAIN: Source Code, Repository & Collaboration / Collaboration & Discussion / Comments & Discussions
  %% Mermaid vertical layout and custom pastel styling
  %% Color legend:
  %% - Core domain files: #D4F1F9 (blue)
  %% - Supporting/utility files: #FFF8DC (yellow)
  %% - Data structure files: #E0F8E0 (green)
  %% - Error handling files: #FFE4E1 (red)
  %% - Initialization/setup files: #E6E6FA (purple)
  %% - Group/subgraph background: #F8F8F8
  %% - Rounded rectangles unless otherwise noted

  %% CORE DOMAIN CONCEPTS
  subgraph Noteable Entities [ "Domain: Comments & Discussions – Core Concepts" ]
    style Noteable Entities fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px
    classDef core fill:#D4F1F9,stroke:#90C5E1,stroke-width:2px,color:#222,stroke-dasharray:0;
    classDef datastruct fill:#E0F8E0,stroke:#6ECE83,stroke-width:2px,stroke-dasharray:0;
    classDef support fill:#FFF8DC,stroke:#E2C696,stroke-width:2px,stroke-dasharray:0;
    classDef error fill:#FFE4E1,stroke:#E17777,stroke-width:2px,stroke-dasharray:0;
    classDef init fill:#E6E6FA,stroke:#BEBEE6,stroke-width:2px,stroke-dasharray:0;

    %% Models and Core Data
    E_Discussion["app/models/discussion.rb\nDiscussion base abstraction"]:::core
    E_Noteable["app/models/concerns/noteable.rb\nNoteable concern: provides discussions and noteable logic to domain objects"]:::core
    E_IndividualNoteDiscussion["app/models/individual_note_discussion.rb\nIndividual discussion"]:::core
    E_OutOfContextDiscussion["app/models/out_of_context_discussion.rb\nHandles out-of-context discussions"]:::core
    E_ResolvableDiscussion["app/models/concerns/resolvable_discussion.rb\nResolvability support for discussions"]:::core

    %% Notes and Variants
    E_UserMention["app/models/user_mention.rb\nTracks mentions in /discussions"]:::core
    E_MergeRequestUserMention["app/models/merge_request_user_mention.rb\nMentions in merge requests"]:::core
    E_IssueUserMention["app/models/issue_user_mention.rb\nMentions in issues"]:::core
    E_DesignUserMention["app/models/design_user_mention.rb\nMentions in designs"]:::core
    E_IssueEmailParticipant["app/models/issue_email_participant.rb\nNote email participants for issues"]:::core
    E_Event["app/models/event.rb\nUser activities in discussions/"]:::core
    E_AntiAbuseDiscussion["app/models/anti_abuse/reports/discussion.rb\nAbuse discussion report tracking"]:::core
    
    %% Data Structures
    E_NoteDiffFile["app/models/note_diff_file.rb\nCaptures file diffs commented on"]:::datastruct
    E_NotesNoteMetadata["app/models//note_metadata.rb\nExtra note metadata, email participants, etc"]:::datastruct
  end

  %% DOMAIN DATA TRANSFORMATION & PROCESSING
  subgraph Discussions & Notes Services [ "Services: Domain Behaviors & Workflow" ]
    style Discussions & Notes Services fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px

    S_DiscussionsBaseService["app/services/discussions/base_service.rb\nBase class for discussion services"]:::core
    S_DiscussionsResolveService["app/services/discussions/resolve_service.rb\nResolves discussions"]:::core
    S_DiscussionsUnresolveService["app/services/discussions/unresolve_service.rb\nUnresolves discussions"]:::core
    S_DiscussionsUpdateDiffPositionService["app/services/discussions/update_diff_position_service.rb\nUpdates diff position in discussions"]:::core

    S_DraftNotesBaseService["app/services/draft_notes/base_service.rb\nDraft : base class"]:::core
    S_DraftNotesCreateService["app/services/draft_notes/create_service.rb\nCreates draft /reviews"]:::core
    S_DraftNotesDestroyService["app/services/draft_notes/destroy_service.rb\nDestroys/cancels draft "]:::core
    S_DraftNotesPublishService["app/services/draft_notes/publish_service.rb\nPublishes draft  to actual "]:::core

    S_NotesBaseService["app/services//base_service.rb\nBase class for  services"]:::core
    S_NotesBuildService["app/services//build_service.rb\nBuilds  for rendering or saving"]:::core
    S_NotesCreateService["app/services//create_service.rb\nCreates a note, handles quick actions, validation"]:::core
    S_NotesDestroyService["app/services//destroy_service.rb\nDestroys a note, triggers hooks, cleans up"]:::core
    S_NotesPostProcessService["app/services//post_process_service.rb\nPost-processing after note creation"]:::core
    S_NotesRenderService["app/services//render_service.rb\nRenders  for display"]:::core
    S_NotesResolveService["app/services//resolve_service.rb\nResolves a note inline/on action"]:::core
    S_NotesQuickActionsService["app/services//quick_actions_service.rb\nProcess quick actions in "]:::core

    S_NotesAbuseReportBuildService["app/services//abuse_report/build_service.rb\nBuilds abuse report "]:::core
    S_NotesAbuseReportCreateService["app/services//abuse_report/create_service.rb\nCreates abuse report "]:::core
    S_NotesAbuseReportUpdateService["app/services//abuse_report/update_service.rb\nUpdates abuse report "]:::core

    S_NoteSummary["app/services/note_summary.rb\nSummary of a note for audit/analytics purposes"]:::datastruct
  end

  %% DOMAIN POLICIES & ACCESS CONTROL
  subgraph Policies & Access Control
    style Policies & Access Control fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px

    P_NotesAntiAbusePolicy["app/policies/anti_abuse/reports/note_policy.rb\nAbuse report policy for note access"]:::core
    P_DraftNotePolicy["app/policies/draft_note_policy.rb\nDraft note access control"]:::core
    P_EventPolicy["app/policies/event_policy.rb\nEvent access policy"]:::core
  end

  %% PRESENTATION: HELPERS, PRESENTERS & SERIALIZERS
  subgraph Presentation & Serialization
    style Presentation & Serialization fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px

    H_NotesHelper["app/helpers/notes_helper.rb\nView helpers for  and discussions"]:::support
    H_EE_NotesHelper["ee/app/helpers/ee/notes_helper.rb\nExtended helpers for EE-specific discussion behaviors"]:::support

    PR_NotePresenter["app/presenters/note_presenter.rb\nPresenter for displaying "]:::support

    S_DiscussionEntity["app/serializers/discussion_entity.rb\nSerializes discussion for API/UI views"]:::support
    S_DiscussionSerializer["app/serializers/discussion_serializer.rb\nSerializer for discussions collection"]:::support
    S_ProjectNoteEntity["app/serializers/project_note_entity.rb\nProject note serialization for API/UI"]:::support
  end

  %% CONTROLLERS & API ENDPOINTS
  subgraph Presentation Controllers [ "Controllers / API Endpoints" ]
    style Presentation Controllers fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px

    C_ProjectsNotesController["app/controllers/projects/notes_controller.rb\nCRUD/effects for "]:::core
    C_ProjectsDiscussionsController["app/controllers/projects/discussions_controller.rb\nHandles discussion presentation"]:::core
    C_RendersNotes["app/controllers/concerns/renders_notes.rb\nPreload and prepare  for rendering"]:::support
    C_SentNotificationsController["app/controllers/sent_notifications_controller.rb\nHandles unsubscribe/notification actions"]:::core
  end

  %% SEARCH & FINDER UTILITIES
  subgraph Finders & Query Layer
    style Finders & Query Layer fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px

    F_NotesFinder["app/finders/notes_finder.rb\nFind/filter  and discussions"]:::support
    F_UserRecentEventsFinder["app/finders/user_recent_events_finder.rb\nRecent activity incl. /discussions"]:::support
    F_FinderWithGroupHierarchy["app/finders/concerns/finder_with_group_hierarchy.rb\nFinder concern for scoping/group filtering"]:::support
  end

  %% NOTIFICATION, MENTION & RECIPIENT SERVICES
  subgraph Notification & Participant System
    style Notification & Participant System fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px

    N_NotificationRecipientsDefault["app/services/notification_recipients/builder/default.rb\nDefault notification recipients builder"]:::support
    N_NotificationRecipientsMergeRequestUnmergeable["app/services/notification_recipients/builder/merge_request_unmergeable.rb\nNotification builder for MR unmergeable"]:::support
    N_NotificationRecipientsNewReview["app/services/notification_recipients/builder/new_review.rb\nNotification builder for new review"]:::support
    N_NotificationRecipientsNewNote["app/services/notification_recipients/builder/new_note.rb\nNotification builder for new "]:::support

    M_Participable["app/models/concerns/participable.rb\nConcern for extracting participants in /discussions"]:::core
  end

  %% EMAILS / MAILERS FOR COMMENTS & DISCUSSIONS
  subgraph Email Notification Layer [ "Emails & Mailers" ]
    style Email Notification Layer fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px

    E_NotifyMailer["app/mailers/notify.rb\nGeneral notifications, hooks for /discussions"]:::support
    E_EmailsNotes["app/mailers/emails/.rb\nSends notification emails for note events"]:::support
    E_EmailsMergeRequests["app/mailers/emails/merge_requests.rb\nEmails specific to merge request discussions/comments"]:::support
    E_EmailsReviews["app/mailers/emails/reviews.rb\nReview email notifications, thread visibility"]:::support
    E_EmailRejectionMailer["app/mailers/email_rejection_mailer.rb\nInforms users on rejected comment/discussion emails"]:::error
    E_EE_EmailsNotes["ee/app/mailers/ee/emails/.rb\nEE-specific  mailer: epics, vulnerabilities"]:::support
  end

  %% DOMAIN INTERACTIONS & LOGICAL RELATIONSHIPS
  %% - Core entity linkages
  E_Discussion -. uses .-> E_Noteable
  E_Discussion --- E_ResolvableDiscussion
  E_Discussion -- subclass --> E_IndividualNoteDiscussion
  E_Discussion -- subclass --> E_OutOfContextDiscussion
  E_Discussion -- subclass --> E_AntiAbuseDiscussion

  %% Mentions specialization/inheritance
  E_MergeRequestUserMention -- extends --> E_UserMention
  E_IssueUserMention -- extends --> E_UserMention
  E_DesignUserMention -- extends --> E_UserMention

  %% Note e-mail participant
  E_NotesNoteMetadata -- belongs_to --> E_IndividualNoteDiscussion

  %% Diff files and metadata
  E_IndividualNoteDiscussion -. references .-> E_NoteDiffFile
  E_IndividualNoteDiscussion -. uses .-> E_NotesNoteMetadata

  %% Service class inheritance/usage
  S_DiscussionsResolveService -- extends --> S_DiscussionsBaseService
  S_DiscussionsUnresolveService -- extends --> S_DiscussionsBaseService
  S_DiscussionsUpdateDiffPositionService -- extends --> S_DiscussionsBaseService

  S_DraftNotesCreateService -- extends --> S_DraftNotesBaseService
  S_DraftNotesDestroyService -- extends --> S_DraftNotesBaseService
  S_DraftNotesPublishService -- extends --> S_DraftNotesBaseService

  S_NotesBaseService -- used_by --> S_NotesBuildService
  S_NotesBaseService -- used_by --> S_NotesCreateService
  S_NotesBaseService -- used_by --> S_NotesDestroyService
  S_NotesBaseService -- used_by --> S_NotesPostProcessService
  S_NotesBaseService -- used_by --> S_NotesRenderService
  S_NotesBaseService -- used_by --> S_NotesQuickActionsService
  S_NotesBaseService -- used_by --> S_NotesResolveService

  S_NotesQuickActionsService -. processes .-> S_NotesCreateService

  S_NotesPostProcessService -. called_after .-> S_NotesCreateService
  S_NotesAbuseReportBuildService -- extends --> S_NotesBuildService
  S_NotesAbuseReportCreateService -- extends --> S_NotesCreateService
  S_NotesAbuseReportUpdateService -- extends --> S_NotesBaseService

  S_NotesCreateService -. creates .-> E_IndividualNoteDiscussion
  S_NotesDestroyService -. destroys .-> E_IndividualNoteDiscussion
  S_NotesResolveService -. resolves .-> E_IndividualNoteDiscussion

  S_NotesBuildService -. builds .-> E_IndividualNoteDiscussion
  S_NotesRenderService -. renders .-> E_IndividualNoteDiscussion

  S_DraftNotesPublishService -. publishes .-> S_DraftNotesCreateService

  %% Policies and access
  P_NotesAntiAbusePolicy -. governs .-> E_AntiAbuseDiscussion
  P_DraftNotePolicy -. governs .-> S_DraftNotesCreateService
  P_EventPolicy -. governs .-> E_Event
  H_NotesHelper -. view_helpers_for .-> E_IndividualNoteDiscussion
  PR_NotePresenter -. presents .-> E_IndividualNoteDiscussion

  %% Presenters and serializers wiring
  S_DiscussionEntity -- serializes --> E_Discussion
  S_DiscussionSerializer -- serializes_collections_of --> E_Discussion
  S_ProjectNoteEntity -- serializes --> E_IndividualNoteDiscussion

  %% Controller relationships
  C_ProjectsNotesController -- CRUD_on --> E_IndividualNoteDiscussion
  C_ProjectsDiscussionsController -- manages --> E_Discussion
  C_ProjectsDiscussionsController -- renders .-> S_DiscussionSerializer
  C_ProjectsDiscussionsController -- view_helpers .-> H_NotesHelper
  C_RendersNotes -- concern_for .-> C_ProjectsNotesController
  C_RendersNotes -- concern_for .-> C_ProjectsDiscussionsController
  C_RendersNotes -- preloads_data_for .-> S_NotesRenderService

  %% Finders usage
  F_NotesFinder -- queries_notes_for --> E_IndividualNoteDiscussion
  F_NotesFinder -- used_by .-> C_ProjectsNotesController
  F_FinderWithGroupHierarchy -- used_by .-> F_NotesFinder

  %% Notification/mention services
  N_NotificationRecipientsDefault -- selects_notifications_for --> E_IndividualNoteDiscussion
  N_NotificationRecipientsNewNote -- selects_notifications_for --> E_IndividualNoteDiscussion
  N_NotificationRecipientsMergeRequestUnmergeable -- selects_notifications_for --> E_MergeRequestUserMention
  N_NotificationRecipientsNewReview -- selects_notifications_for --> E_MergeRequestUserMention
  M_Participable -- used_by .-> E_IndividualNoteDiscussion
  M_Participable -- used_by .-> S_NotesCreateService

  %% Email notification mailers
  E_EmailsNotes -- sends_emails_for --> E_IndividualNoteDiscussion
  E_EmailsNotes -- invoked_by .-> S_NotesCreateService
  E_EmailRejectionMailer -- handles_rejection_of .-> E_IndividualNoteDiscussion
  E_EE_EmailsNotes -- extends .-> E_EmailsNotes
  E_EmailsMergeRequests -- sends_emails_for --> E_MergeRequestUserMention
  E_EmailsReviews -- sends_emails_for --> E_IndividualNoteDiscussion
  E_NotifyMailer -- base_for .-> E_EmailsNotes
  E_NotifyMailer -- base_for .-> E_EmailsMergeRequests
  E_NotifyMailer -- base_for .-> E_EmailsReviews

  %% Data structure flows and key patterns
  E_IndividualNoteDiscussion -. stores_diff_files .-> E_NoteDiffFile
  E_NotesNoteMetadata -. stores_metadata_for .-> E_IndividualNoteDiscussion
  E_Noteable -- provides_interface_for .-> S_NotesCreateService
  S_NotesCreateService -- triggers_post_process --> S_NotesPostProcessService
  S_NotesDestroyService -- cleans_up .-> E_NotesNoteMetadata

  %% Cross-component behavioral flows
  E_DesignUserMention -. used_by .-> S_NotesCreateService
  E_MergeRequestUserMention -. used_by .-> S_DraftNotesPublishService
  E_IssueUserMention -. referenced_in .-> E_NotesNoteMetadata

  %% Sibling/related entity flows
  E_Event -. references .-> E_IndividualNoteDiscussion
  E_AntiAbuseDiscussion -. tracks .-> E_IndividualNoteDiscussion
  E_IssueEmailParticipant -- links .-> E_IndividualNoteDiscussion

  %% Main entrypoints
  C_ProjectsNotesController -- orchestrates_via .-> S_NotesCreateService
  C_ProjectsNotesController -- orchestrates_destroy .-> S_NotesDestroyService
  C_ProjectsDiscussionsController -- orchestrates_resolve .-> S_DiscussionsResolveService
  C_ProjectsDiscussionsController -- orchestrates_unresolve .-> S_DiscussionsUnresolveService

  %% Subgraph links for collaboration across boundaries 
  Discussions & Notes Services -. interacts_with .-> Noteable Entities
  Presentation Controllers -. uses .-> Presentation & Serialization
  Noteable Entities -. referenced_by .-> Policies & Access Control
  Presentation Controllers -. authorizes .-> Policies & Access Control
  Discussions & Notes Services -. authorizes .-> Policies & Access Control
  Finders & Query Layer -. feeds_into .-> Presentation Controllers
  Notification & Participant System -. delivers_to .-> Email Notification Layer

  %% Class definitions (shapes)
  class E_Discussion,E_Noteable,E_IndividualNoteDiscussion,E_OutOfContextDiscussion,E_ResolvableDiscussion,E_UserMention,E_MergeRequestUserMention,E_IssueUserMention,E_DesignUserMention,E_IssueEmailParticipant,E_Event,E_AntiAbuseDiscussion,M_Participable core;
  class E_NoteDiffFile,E_NotesNoteMetadata,S_NoteSummary datastruct;
  class S_DiscussionsBaseService,S_DiscussionsResolveService,S_DiscussionsUnresolveService,S_DiscussionsUpdateDiffPositionService,S_DraftNotesBaseService,S_DraftNotesCreateService,S_DraftNotesDestroyService,S_DraftNotesPublishService,S_NotesBaseService,S_NotesBuildService,S_NotesCreateService,S_NotesDestroyService,S_NotesPostProcessService,S_NotesRenderService,S_NotesResolveService,S_NotesQuickActionsService,S_NotesAbuseReportBuildService,S_NotesAbuseReportCreateService,S_NotesAbuseReportUpdateService core;
  class P_NotesAntiAbusePolicy,P_DraftNotePolicy,P_EventPolicy core;
  class H_NotesHelper,H_EE_NotesHelper,PR_NotePresenter,S_DiscussionEntity,S_DiscussionSerializer,S_ProjectNoteEntity,F_NotesFinder,F_UserRecentEventsFinder,F_FinderWithGroupHierarchy,support,N_NotificationRecipientsDefault,N_NotificationRecipientsMergeRequestUnmergeable,N_NotificationRecipientsNewReview,N_NotificationRecipientsNewNote support;
  class E_NotifyMailer,E_EmailsNotes,E_EmailsMergeRequests,E_EmailsReviews,E_EE_EmailsNotes support;
  class E_EmailRejectionMailer error;
```