```mermaid
flowchart TB
  %% Core Subgraph: Webhook Event Processing (Domain Core)
  subgraph Webhook Event Processing["Webhook Event Processing" ]
    direction TB
    style Webhook Event Processing fill:#F8F8F8,stroke:#59BEE2,stroke-width:2px,stroke-dasharray: 8,rx:10
    
    subgraph Core_Functionality["Domain Core" ]
      direction TB
      style Core_Functionality fill:#F8F8F8,stroke:#85D4EB,stroke-width:1.5px

      PackagistModel["app/models/integrations/packagist.rb\nPackagist Integration":::core]
      LoggableConcern["app/models/concerns/integrations/loggable.rb\nLogging Concern":::support]
      WebHookLogFinder["app/finders/web_hooks/web_hook_logs_finder.rb\nWebhook Log Query\n& Filtering":::support]
      ProjectsHookLogsController["app/controllers/projects/hook_logs_controller.rb\nProject Webhook Log API":::core]
      GroupsHookLogsController["ee/app/controllers/groups/hook_logs_controller.rb\nGroup Webhook Log API":::core]
      WebHookLogPresenter["app/presenters/web_hook_log_presenter.rb\nWebhook Log Presentation":::support]

      PackagistModel -->|Includes| LoggableConcern
      ProjectsHookLogsController -->|Uses| WebHookLogFinder
      ProjectsHookLogsController -->|Presents| WebHookLogPresenter
      GroupsHookLogsController -->|Uses| WebHookLogFinder
      GroupsHookLogsController -->|Presents| WebHookLogPresenter
      WebHookLogFinder -->|Returns logs for| WebHookLogPresenter
        
    end

    subgraph Log_Management["Webhook Log Lifecycle" ]
      direction TB
      style Log_Management fill:#F8F8F8,stroke:#edc87b,stroke-width:1.5px
      LogDestroyService["app/services/web_hooks/log_destroy_service.rb\nDelete Webhook Logs":::support]
      LogExecutionService["app/services/web_hooks/log_execution_service.rb\nLog Webhook Executions":::support]
      ResendService["app/services/web_hooks/events/resend_service.rb\nWebhook Log Replay":::core]

      WebHookLogFinder --> LogDestroyService
      LogExecutionService -->|Can update| LogDestroyService
      LogExecutionService -->|Logs execution| WebHookLogFinder
      ResendService -->|Fetches logs with| WebHookLogFinder
      ResendService -->|Triggers log replay, checks state with| LogExecutionService
    end
    
    subgraph Event_Abstractions["Event System Abstractions" ]
      direction TB
      style Event_Abstractions fill:#F8F8F8,stroke:#59D48A,stroke-width:1.5px
      EventStoreSubscriber["lib/gitlab/event_store/subscriber.rb\nEvent Handler Worker":::support]
      EventStoreEvent["lib/gitlab/event_store/event.rb\nEvent Data Structure":::data]
      EventStoreSubscriber --> EventStoreEvent
      LogExecutionService -->|Records event| EventStoreEvent
      EventStoreSubscriber -->|Triggers handlers| LogExecutionService

    end
    
    subgraph Webhook_Payload_Parsing["Domain-specific Webhook Processors" ]
      direction TB
      style Webhook_Payload_Parsing fill:#F8F8F8,stroke:#7CD5B3,stroke-width:1.5px

      MailgunBaseProcessor["lib/gitlab/mailgun/webhook_processors/base.rb\nMailgun Webhook Processor Base":::core]
      PagerDutyPayloadParser["lib/pager_duty/webhook_payload_parser.rb\nPagerDuty Webhook Parser":::core]
      HttpIntegrationBase["ee/app/graphql/ee/mutations/alert_management/http_integration/http_integration_base.rb\nAlert HTTP Integration Base GraphQL":::core]

      MailgunBaseProcessor -->|Handles payload| LogExecutionService
      PagerDutyPayloadParser -->|Normalizes payload| LogExecutionService
      HttpIntegrationBase -->|Validates and maps payload| LogExecutionService
      HttpIntegrationBase -->|Supplies mapping to| EventStoreEvent
    end

    subgraph Recursion_Protection["Recursion Detection/Protection" ]
      direction TB
      style Recursion_Protection fill:#F8F8F8,stroke:#9a9ccd,stroke-width:1.5px

      RecursionDetection["lib/gitlab/web_hooks/recursion_detection.rb\nRecursion Protection Module":::core]
      RecursionUUID["lib/gitlab/web_hooks/recursion_detection/uuid.rb\nRecursion UUID Assignment":::support]
      RecursionDetection -->|Uses| RecursionUUID
      LogExecutionService -->|Enforces via| RecursionDetection
      ResendService -->|Checks with| RecursionDetection
    end

    subgraph External_Support["Ancillary Supporting/Utility Components" ]
      direction TB
      style External_Support fill:#F8F8F8,stroke:#f6e097,stroke-width:1.5px
      ExternalRedirectController["app/controllers/external_redirect/external_redirect_controller.rb\nRedirect Utilities":::support]
      PushOptions["lib/gitlab/push_options.rb\nGit Push Options Parser":::support]
      QuickActionFilter["lib/banzai/filter/quick_action_filter.rb\nQuick Action Extraction":::support]
      LoggableConcern -.->|Shared logging| LogExecutionService
      LoggableConcern -.->|Shared logging| ResendService
    end
    
    subgraph Error_Handling["Error Handling and Validation" ]
      direction TB
      style Error_Handling fill:#F8F8F8,stroke:#F6AEB2,stroke-width:1.5px
      FilterArgumentValidator["ee/app/finders/remote_development/filter_argument_validator.rb\nGlobal Argument Validator":::support]
      VerifyResponse["qa/qa/vendor/smocker/verify_response.rb\nQA/Mocking Validation":::support]
      RateLimiter["ee/lib/ee/gitlab/web_hooks/rate_limiter.rb\nEE Rate Limiting":::error]
      FilterArgumentValidator -->|Validates arguments for| WebHookLogFinder
      RateLimiter -->|Applied during| LogExecutionService
    end

  end

  %% Data Structures Section
  subgraph Data_Transformations["Domain Data Structures & Transformations" ]
    direction TB
    style Data_Transformations fill:#F8F8F8,stroke:#9edeb8,stroke-width:2px,rx:10

    WebHookLog["Webhook Log DB record":::data]
    EventStoreEventDS["EventStoreEvent serialized event":::data]

    LogExecutionService -->|Writes/updates| WebHookLog
    WebHookLogFinder -->|Fetches| WebHookLog
    LogDestroyService -->|Deletes| WebHookLog
    ResendService -->|Reads/resends| WebHookLog

    EventStoreEvent -->|Persists event data| EventStoreEventDS
    LogExecutionService -->|Generates| EventStoreEventDS

    PagerDutyPayloadParser -->|Parses into| WebHookLog
    MailgunBaseProcessor -->|Parses into| WebHookLog
    HttpIntegrationBase -->|Validates/transforms into| WebHookLog
  end

  %% Styles
  classDef core fill:#D4F1F9,color:#222,stroke:#59BEE2,stroke-width:1px,rx:15
  classDef support fill:#FFF8DC,color:#222,stroke:#edc87b,stroke-width:1px,rx:13
  classDef data fill:#E0F8E0,color:#222,stroke:#7CD5B3,stroke-width:1.1px,rx:13
  classDef error fill:#FFE4E1,color:#222,stroke:#F6AEB2,stroke-width:1.2px,rx:13

  %% Node Shapes
  class PackagistModel,MailgunBaseProcessor,PagerDutyPayloadParser,HttpIntegrationBase,RecursionDetection,ProjectsHookLogsController,GroupsHookLogsController,ResendService,EventStoreSubscriber rounded-rectangle
  class LoggableConcern,WebHookLogFinder,LogExecutionService,LogDestroyService,ExternalRedirectController,PushOptions,QuickActionFilter,WebHookLogPresenter,RecursionUUID,FilterArgumentValidator,VerifyResponse,RateLimiter rounded-rectangle

  class WebHookLog,EventStoreEventDS data
  class EventStoreEvent data

  %% Spacing & Relationships
  Webhook Event Processing --> Data_Transformations
  LogExecutionService --> Data_Transformations
  ResendService --> Data_Transformations
```