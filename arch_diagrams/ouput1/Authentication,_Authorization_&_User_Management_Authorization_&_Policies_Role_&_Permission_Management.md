```mermaid
flowchart TD
%% COLOR & SHAPE DEFINITIONS
classDef coreDomain fill:#D4F1F9,stroke:#8fd3ee,stroke-width:2px,color:#333,rx:8,ry:8;
classDef supportUtil fill:#FFF8DC,stroke:#eedd82,stroke-width:2px,color:#333,rx:8,ry:8;
classDef dataStruct fill:#E0F8E0,stroke:#8fe28f,stroke-width:2px,color:#333,rx:8,ry:8;
classDef errorHandling fill:#FFE4E1,stroke:#e39990,stroke-width:2px,color:#333,rx:8,ry:8;
classDef initSetup fill:#E6E6FA,stroke:#b8aee8,stroke-width:2px,color:#333,rx:8,ry:8;
classDef groupBorder fill:#F8F8F8,stroke-dasharray: 2 6,stroke-width:2px,rx:16,ry:16;
%% 1. DOMAIN ROOT GROUPING

subgraph G1[Authorization & Policies / Role & Permission Management]
direction TB
class G1 groupBorder

%% Policies group
subgraph G2[Policies & Policy Behaviors]
direction TB
class G2 groupBorder

groupPolicy["app/policies/group_policy.rb\nGroup Policy: Group membership & access logic"]:::coreDomain
eeGroupPolicy["ee/app/policies/ee/group_policy.rb\nEE Ext: Group Policy - advanced logic"]:::coreDomain
eeProjectPolicy["ee/app/policies/ee/project_policy.rb\nEE Ext: Project Policy"]:::coreDomain
eeMergeRequestPolicy["ee/app/policies/ee/merge_request_policy.rb\nEE Ext: MR Policy"]:::coreDomain
eeIssuePolicy["ee/app/policies/ee/issue_policy.rb\nEE Ext: Issue Policy"]:::coreDomain
eeUserPolicy["ee/app/policies/ee/user_policy.rb\nEE Ext: User Policy: user feature flag conditions"]:::coreDomain
eePolicyActor["ee/app/policies/ee/policy_actor.rb\nEE Policy Actor: role determination"]:::coreDomain

readonlyAbilities["ee/app/policies/ee/readonly_abilities.rb\nReadonly Abilities ext"]:::supportUtil
archivedAbilities["ee/app/policies/ee/archived_abilities.rb\nArchived Abilities ext"]:::supportUtil

groupProjectNamespaceSharedPolicy["app/policies/namespaces/group_project_namespace_shared_policy.rb\nShared Group/Project Policy, assumed"]:::supportUtil
findGroupProjects["app/policies/concerns/find_group_projects.rb\nFind Group Projects concern, assumed"]:::supportUtil

achievementsPolicy["app/policies/achievements/achievement_policy.rb\nNamespace-based Policy"]:::coreDomain
resourceStateEventPolicy["app/policies/resource_state_event_policy.rb\nExtends ResourceEventPolicy"]:::coreDomain
timeboxPolicy["app/policies/timebox_policy.rb\nBasic Policy"]:::coreDomain
timelineEventTagPolicy["app/policies/incident_management/timeline_event_tag_policy.rb\nEvent Tag Policy: Project-based"]:::coreDomain
timelogCategoryPolicy["app/policies/time_tracking/timelog_category_policy.rb\nTimelog Category Policy: Namespace-based"]:::coreDomain

eeRequirementsPolicy["ee/app/policies/requirements_management/requirement_policy.rb\nRequirements Policy: Delegates to parent & issue"]:::coreDomain
vulnStateTransitionPolicy["ee/app/policies/vulnerabilities/state_transition_policy.rb\nVulnerability Transition Policy - Project-based"]:::coreDomain

dastScannerProfilePolicy["ee/app/policies/dast_scanner_profile_policy.rb\nDAST Scanner Profile Policy"]:::coreDomain

containerRegistryTagPolicy["app/policies/container_registry/tag_policy.rb\nContainer Registry Tag Policy: Enforces deletion protection"]:::coreDomain
containerRegistryReferrerPolicy["app/policies/container_registry/referrer_policy.rb\nPolicy Delegates to Tag"]:::coreDomain

gitTagPolicy["app/policies/gitlab/git/tag_policy.rb\nGit Tag Policy: Protect Delete"]:::coreDomain
packagesTagPolicy["app/policies/packages/tag_policy.rb\nPackages Tag Policy"]:::coreDomain

usersSavedReplyPolicy["app/policies/users/saved_reply_policy.rb\nSaved Reply Policy - User based"]:::coreDomain
usersNamespaceCommitEmailPolicy["app/policies/users/namespace_commit_email_policy.rb\nNamespace Commit Email Policy - User delegated"]:::coreDomain

%% Relational lines: EE group/project/project policy extensions
groupPolicy -- Extends, Prepending --> eeGroupPolicy
eeGroupPolicy -- Uses --> findGroupProjects
groupPolicy -- Inherits --> groupProjectNamespaceSharedPolicy
eeProjectPolicy -- Includes --> readonlyAbilities
eeProjectPolicy -- Uses --> vulnStateTransitionPolicy
eeProjectPolicy -- Uses --> readonlyAbilities

eeGroupPolicy -- Includes --> readonlyAbilities
eeGroupPolicy -- Includes --> archivedAbilities
eeMergeRequestPolicy -- Uses --> eeProjectPolicy
eeIssuePolicy -- Uses --> eeProjectPolicy

containerRegistryTagPolicy -- Delegates --> containerRegistryReferrerPolicy
containerRegistryTagPolicy -- Prevents-action --> gitTagPolicy
packagesTagPolicy -- Delegates --> gitTagPolicy
usersSavedReplyPolicy -- Delegates-user --> usersNamespaceCommitEmailPolicy

achievementsPolicy -- Delegates --> groupPolicy
timelineEventTagPolicy -- Delegates --> eeProjectPolicy
timelogCategoryPolicy -- Delegates --> groupPolicy

eeUserPolicy -- Extends --> groupPolicy

resourceStateEventPolicy -- Extends --> groupPolicy

dastScannerProfilePolicy -- Inherits --> groupPolicy

eeRequirementsPolicy -- Delegates --> eeIssuePolicy
vulnStateTransitionPolicy -- Delegates --> eeProjectPolicy

class groupPolicy,eeGroupPolicy,eeProjectPolicy,eeMergeRequestPolicy,eeIssuePolicy,eeUserPolicy,eePolicyActor,readonlyAbilities,archivedAbilities,groupProjectNamespaceSharedPolicy,findGroupProjects,achievementsPolicy, resourceStateEventPolicy,timeboxPolicy,timelineEventTagPolicy,timelogCategoryPolicy,eeRequirementsPolicy,vulnStateTransitionPolicy,dastScannerProfilePolicy,containerRegistryTagPolicy,containerRegistryReferrerPolicy,gitTagPolicy,packagesTagPolicy,usersSavedReplyPolicy,usersNamespaceCommitEmailPolicy coreDomain

end

%% Roles & Permissions Models
subgraph G3[Role & Permission Structures]
direction TB
class G3 groupBorder

ability["app/models/ability.rb\nAbility: Permission engine, queries, filtering, user-to-abilities binding"]:::coreDomain
userHighestRole["ee/app/models/ee/user_highest_role.rb\nUser Highest Role: ext ActiveSupport::Concern for roles"]:::dataStruct
customAbility["ee/app/models/authz/custom_ability.rb\nCustom Ability: User+Resource abilities management"]:::coreDomain
adminChangedPasswordNotifier["app/models/concerns/admin_changed_password_notifier.rb\nNotifier: Admin password change notifications"]:::supportUtil

class ability,userHighestRole,customAbility,adminChangedPasswordNotifier dataStruct

ability -- Uses --> customAbility
ability -- Determines permissions for --> groupPolicy
userHighestRole -- Provides role values --> customAbility
customAbility -- Uses roles from --> userHighestRole

end

%% External Authorization Integrations
subgraph G4[External Authorization & Access Controls]
direction TB
class G4 groupBorder
allowable["lib/gitlab/allowable.rb\nModule: can? logic, uses Ability"]:::supportUtil
authzClient["lib/gitlab/external_authorization/client.rb\nClient: Invokes external auth service"]:::supportUtil
authzAccess["lib/gitlab/external_authorization/access.rb\nAccess: Authorizes user/label, caches/loaders"]:::coreDomain
authzLogger["lib/gitlab/external_authorization/logger.rb\nLogger: Log access results"]:::supportUtil
authzResponse["lib/gitlab/external_authorization/response.rb\nResponse: Wraps and interprets service response"]:::dataStruct

authzCacheSpec["spec/lib/gitlab/external_authorization/cache_spec.rb\nSpec: External Authorization cache"]:::supportUtil

authUserAccessDeniedReason["lib/gitlab/auth/user_access_denied_reason.rb\nUserAccessDeniedReason: why user denied"]:::errorHandling

allowable -- Wraps & delegates to --> ability
authzAccess -- Initiates via --> authzClient
authzAccess -- Interprets result via --> authzResponse
authzAccess -- Logs via --> authzLogger
authzLogger -- Operates on -> authzAccess
authzCacheSpec -- Tests --> authzAccess
ability -- Used by --> allowable
authUserAccessDeniedReason -- Used by --> authzAccess

end

%% Custom Roles & Advanced Features
subgraph G5[Custom Roles / Auditing / Policy Adapters]
direction TB
class G5 groupBorder

customRolesShared["ee/lib/gitlab/custom_roles/shared.rb\nShared params/constants for Custom Roles"]:::supportUtil
authorityAnalyzer["ee/lib/gitlab/authority_analyzer.rb\nAnalyzer: User authority from MR commits"]:::coreDomain
gitAccess["ee/lib/ee/gitlab/git_access.rb\nGit Access: EE extension for Git commands access, OTP, group"]:::coreDomain

ldapSyncAdminRole["ee/lib/gitlab/authz/ldap/sync/admin_role.rb\nLDAP Admin Role Synchronizer"]:::supportUtil

class customRolesShared,authorityAnalyzer,gitAccess,ldapSyncAdminRole supportUtil

customAbility -- Uses constants from --> customRolesShared
ldapSyncAdminRole -- Applies roles to --> customAbility

authorityAnalyzer -- Used by --> ability
gitAccess -- Uses policies from --> eeProjectPolicy
gitAccess -- Relies on --> groupPolicy

end

%% EE/GraphQL Layer: LDAP Admin Role Management
subgraph G6[GraphQL: Admin Role Management LDAP]
direction TB
class G6 groupBorder

ldapAdminRoleLinkCreate["ee/app/graphql/mutations/authz/ldap_admin_role_links/create.rb\nAdminRoleLink Create Mutation"]:::coreDomain
ldapAdminRoleLinkDestroy["ee/app/graphql/mutations/authz/ldap_admin_role_links/destroy.rb\nAdminRoleLink Destroy Mutation"]:::coreDomain
adminRolesLdapSync["ee/app/graphql/mutations/authz/admin_roles/ldap_sync.rb\nLDAP Sync Mutation for Admin Roles"]:::coreDomain

ldapAdminRoleLinkCreate -- Calls --> ldapSyncAdminRole
ldapAdminRoleLinkDestroy -- Uses --> ldapSyncAdminRole
adminRolesLdapSync -- Triggers --> ldapSyncAdminRole

end

%% EE Controllers & Services: User Permissions & Token Management
subgraph G7[Controllers & Services: EE Roles/Permissions, Tokens]
direction TB
class G7 groupBorder

adminRolesAndPermsController["ee/app/controllers/admin/application_settings/roles_and_permissions_controller.rb\nAdmin: RolesAndPermissions, EE controller"]:::coreDomain
groupMembersController["ee/app/controllers/ee/groups/group_members_controller.rb\nGroups: GroupMembersController, EE"]:::coreDomain

resourceAccessTokensCreateService["ee/app/services/ee/resource_access_tokens/create_service.rb\nCreate Resource Access Token, audits"]:::coreDomain
resourceAccessTokensRevokeService["ee/app/services/ee/resource_access_tokens/revoke_service.rb\nRevoke Resource Access Token, audits"]:::coreDomain
userPermissionsExportService["ee/app/services/user_permissions/export_service.rb\nExport User Permissions as CSV"]:::supportUtil

resourceAccessTokensCreateService -- Audits via --> resourceAccessTokensRevokeService
adminRolesAndPermsController -- Invokes --> resourceAccessTokensCreateService
adminRolesAndPermsController -- Uses policies in --> ability
groupMembersController -- Authorizes via --> groupPolicy
userPermissionsExportService -- Uses --> ability

end

%% EE Policy Helpers (CI, Clusters, Global)
subgraph G8[EE Policy Helpers & Adapters]
direction TB
class G8 groupBorder

eeCiTroubleshootJobHelper["ee/app/policies/ee/ci/troubleshoot_job_policy_helper.rb\nCI Troubleshoot Job Policy Helper"]:::supportUtil
eeClustersAgentPolicy["ee/app/policies/ee/clusters/agent_policy.rb\nCluster Agent Policy, includes remote development"]:::supportUtil
eeGlobalPolicy["ee/app/policies/ee/global_policy.rb\nGlobal EE Policy: Misc features, scoping, conditions"]:::supportUtil

eeGlobalPolicy -- Uses scoped abilities from --> readonlyAbilities
eeCiTroubleshootJobHelper -- Checks via --> ability
eeClustersAgentPolicy -- Includes --> groupPolicy

end

%% DATA STRUCTURE & TRANSFORMERS: Domain-Specific
subgraph G9[Domain Data Structures & Transformers]
direction TB
class G9 groupBorder

semVer["app/models/packages/sem_ver.rb\nPackages SemVer: semantic version structure"]:::dataStruct

semVer -- Used-in policies for --> packagesTagPolicy

end

end

%% --- LOGICAL RELATIONSHIP CONNECTIONS ACROSS GROUPS ---

%% Central connectors: Ability is at the core (permissions) for most logic
ability -.-> G2
ability -.-> G3
ability -.-> G4
ability -.-> G5
ability -.-> G7

G3 -- "Permission queries/data" --> G2
G2 -- "Enforced in Controllers" --> G7
G7 -- "Drives GraphQL Mutations" --> G6

%% All policy group helpers/extensions connect back to Policies
G8 -- "Policy helpers extend/enhance" --> G2

G4 -- "External AuthZ enforced at policy layer" --> G2

G5 -- "Advanced roles & custom authorities" --> G3
G5 -- "Authority data influences permissions" --> G2

G9 -- "Versioning/structure for packages" --> G2

%% Data Structures in userHighestRole propagate to policies
userHighestRole -. "Provides role values" .-> ability

%% Error Handling plugs into policy results
authUserAccessDeniedReason --| "Denial reason feeds errors" |--> groupPolicy
authUserAccessDeniedReason --| "Denial reason used in Api/Access" |--> G7

%% End of Diagram
```