```mermaid
flowchart TD
  %% Domain: Source Code, Repository & Collaboration / Snippets & Files / Uploads & Attachments

  %% Define color classes
  classDef core fill:#D4F1F9,stroke:#B4CFE1,stroke-width:2px,color:#233E57,shape:rounded-rectangle;
  classDef utility fill:#FFF8DC,stroke:#FFEFD5,stroke-width:2px,color:#775500,shape:rounded-rectangle;
  classDef data fill:#E0F8E0,stroke:#BDEABB,stroke-width:2px,color:#415F36,shape:rounded-rectangle;
  classDef error fill:#FFE4E1,stroke:#FFB6B0,stroke-width:2px,color:#8B4040,shape:rounded-rectangle;
  classDef init fill:#E6E6FA,stroke:#C6BEEF,stroke-width:2px,color:#4A418B,shape:rounded-rectangle;
  classDef group fill:#F8F8F8,stroke:#E6E6EA,stroke-width:3px;
  
  %% === SUBGRAPHS ===

  %% --- 1. Uploads Models, Data Structures, Persistence Layer ---
  subgraph "Models & Data Structures" ["Models & Data Structures"]
    direction TB
    m_upload["Upload Model\ndomain persistent entity"]:::data
    m_ee_upload["EE Upload Model\nGeo, Verifications"]:::data
    m_alert_metric_image["AlertManagement::MetricImage\nmetric image upload"]:::data
    m_ml_model_version["Ml::ModelVersion\nML Model artifact"]:::data
    m_uploads_fog["Uploads::Fog\nObject Storage adapter"]:::core
    m_avatarable["Avatarable Concern\nattach avatar to models"]:::data
    m_x509_serial["X509SerialNumberAttribute\nX509 on uploads"]:::data
  end
  class "Models & Data Structures" group

  %% --- 2. Uploaders Core Abstractions & Specializations ---
  subgraph "Uploaders & Uploader Abstractions" ["Uploaders & Uploader Abstractions"]
    direction TB
    up_gitlab_uploader["GitlabUploader\nbase uploader"]:::core
    up_file_uploader["FileUploader\ngeneric file upload"]:::core
    up_namespace_file["NamespaceFileUploader\nnamespace files"]:::core
    up_personal_file["PersonalFileUploader\npersonal snippet files"]:::core
    up_job_artifact["JobArtifactUploader\njob artifacts"]:::core
    up_attachment["AttachmentUploader\ngeneral file attachments"]:::core
    up_placeholder_ra["Import::PlaceholderReassignmentsUploader\nCSV reassignments"]:::core
    up_records_uploads["RecordsUploads::Concern\nrecord upload attach"]:::core
    up_workhorse["Workhorse UploadPath\ntemp paths"]:::utility
    up_uploads_actions["UploadsActions controller concern"]:::utility
  end
  class "Uploaders & Uploader Abstractions" group

  %% --- 3. Controllers: HTTP endpoints, REST ---
  subgraph "Controllers & Upload APIs" ["Controllers & Upload APIs"]
    direction TB
    c_uploads["UploadsController\ngeneric uploads API"]:::core
    c_projects_uploads["Projects::UploadsController"]:::core
    c_groups_uploads["Groups::UploadsController"]:::core
    c_banzai_uploads["Banzai::UploadsController\nmarkdown/CIA upload API"]:::core
    c_profiles_avatars["Profiles::AvatarsController"]:::core
    c_projects_avatars["Projects::AvatarsController"]:::core
    c_groups_avatars["Groups::AvatarsController"]:::core
    c_admin_topics_avatars["Admin::Topics::AvatarsController"]:::core
    c_ee_uploads["EE::UploadsController EE extend"]:::core
    c_conc_uploads_actions["Concerns::UploadsActions"]:::utility
    c_conc_workhorse_auth["Concerns::WorkhorseAuthorization"]:::utility
    c_conc_send_file["SendFileUpload\nfile sending util"]:::utility
    c_autocomplete_exp["AutocompleteSources::ExpiresIn"]:::utility
  end
  class "Controllers & Upload APIs" group

  %% --- 4. Services, Business Logic ---
  subgraph "Service Layer" ["Services Upload Business Logic"]
    direction TB
    s_upload_service["UploadService\ngeneric upload orchestrator"]:::core
    s_uploads_destroy["Uploads::DestroyService\ndelete objects/files"]:::core
    s_packages_ml_file["Packages::MlModel::CreatePackageFileService"]:::core
    s_packages_nuget_metadata["Packages::Nuget::ExtractMetadataFileService"]:::core
  end
  class "Service Layer" group

  %% --- 5. Uploader Infrastructure & Object Storage Integrations ---
  subgraph "Uploader Infrastructure & Storage Adapters" ["Uploader Infrastructure & Storage"]
    direction TB
    infra_uploaded_file["UploadedFile\ndomain upload file"]:::data
    infra_carrier_wave_s3_patch["CarrierWave S3 Encryption Headers Patch"]:::init
    infra_carrier_wave_perf_patch["CarrierWave Performance Patch"]:::init
    infra_carrier_wave_string_file["CarrierWaveStringFile"]:::utility
    infra_backup_uploads["Backup::Tasks::Uploads"]:::core
    infra_uploads_fog["app/models/uploads/fog.rb\nFog Adapter"]:::core
    infra_object_storage_spec["ObjectStorageSpec\nstorage adapter tests"]:::utility
    infra_object_storage_cdn_spec["ObjectStorage::CDN Spec"]:::utility
  end
  class "Uploader Infrastructure & Storage Adapters" group

  %% --- 6. API Finders, Markdown Integration, Banzai, and Rewrites ---
  subgraph "Finders & Markdown/Link Integrations" ["Finders, Markdown Rewrites, Integrations"]
    direction TB
    f_banzai_uploads_finder["Banzai::UploadsFinder\nlookup uploads for context"]:::core
    f_banzai_md_uploads["API::MarkdownUploads\nAPI Find uploads in markdown"]:::core
    f_banzai_filter_service["Banzai::Filter::ServiceDeskUploadLinkFilter"]:::core
    f_gfm_uploads_rewriter["Gitlab::Gfm::UploadsRewriter\nrewrite md links on move"]:::core
  end
  class "Finders & Markdown/Link Integrations" group

  %% --- 7. Policies, Authorization ---
  subgraph "Policies & Authorization" ["Policies & Authorization"]
    direction TB
    pol_upload_policy["UploadPolicy\naccess control"]:::core
    pol_tf_state_policy["Terraform::StateVersionPolicy"]:::core
  end
  class "Policies & Authorization" group

  %% --- 8. Error Handling ---
  subgraph "Error Handling" ["Error Handling"]
    direction TB
    err_failure_handler["Gitlab::Email::FailureHandler"]:::error
  end
  class "Error Handling" group

  %% --- 9. Test, Spec, QA Files support/coverage/validation ---
  subgraph "Specs & QA (Test Coverage)" ["Test Coverage & Specs"]
    direction TB
    spec_every_uploader["ee/spec/uploaders/every_gitlab_uploader_spec.rb"]:::utility
    spec_content_type["spec/uploaders/content_type_whitelist_spec.rb"]:::utility
    spec_records_uploads["spec/uploaders/records_uploads_spec.rb"]:::utility
    spec_object_storage["spec/uploaders/object_storage_spec.rb"]:::utility
    spec_object_storage_cdn["spec/uploaders/object_storage/cdn_spec.rb"]:::utility
    spec_uploads_controller["spec/controllers/uploads_controller_spec.rb"]:::utility
    spec_projects_uploads_controller["spec/controllers/projects/uploads_controller_spec.rb"]:::utility
    spec_groups_uploads_controller["spec/controllers/groups/uploads_controller_spec.rb"]:::utility
    spec_email_attachment["spec/lib/gitlab/email/attachment_uploader_spec.rb"]:::utility
    spec_decompressed_validator["spec/lib/gitlab/import_export/decompressed_archive_size_validator_spec.rb"]:::utility
  end
  class "Specs & QA Test Coverage" group

  %% === RELATIONSHIPS ===

  %% 1. Model + Storage adapters
  m_upload -- "EE concerns, Geo, Verifiability" --> m_ee_upload
  m_upload -- "Stored by" --> m_uploads_fog
  m_alert_metric_image -- "Attaches" --> m_upload
  m_ml_model_version -- "Versioned files" --> m_upload

  %% 2. Uploaders <-> Models, Data
  m_upload -- "Handled by" --> up_gitlab_uploader
  up_gitlab_uploader -- "Base for" --> up_file_uploader
  up_file_uploader -- "Specialized by" --> up_namespace_file
  up_file_uploader -- "Specialized by" --> up_personal_file
  up_file_uploader -- "Specialized by" --> up_job_artifact
  up_file_uploader -- "Specialized by" --> up_attachment
  up_file_uploader -- "Specialized by" --> up_placeholder_ra
  up_attachment -- "Includes" --> up_records_uploads
  up_namespace_file -- "Includes" --> up_records_uploads
  up_personal_file -- "Includes" --> up_records_uploads

  up_gitlab_uploader -- "Upload logic via" --> up_workhorse
  up_job_artifact -- "Extends" --> up_workhorse
  up_records_uploads -- "Stores Upload reference" --> m_upload

  m_avatarable -- "Provides Avatar uploading to" --> m_upload
  m_alert_metric_image -- "Uploads Image attaches upload" --> up_attachment

  m_x509_serial -- "X509 on uploads" --> m_upload

  %% 3. Controller-Uploaders interaction
  c_uploads -- "Uses" --> up_file_uploader
  c_projects_uploads -- "Uses" --> up_file_uploader
  c_groups_uploads -- "Uses" --> up_file_uploader
  c_banzai_uploads -- "Uses" --> up_file_uploader
  c_profiles_avatars -- "Avatar uses" --> up_file_uploader
  c_projects_avatars -- "Avatar uses" --> up_file_uploader
  c_groups_avatars -- "Avatar uses" --> up_file_uploader
  c_admin_topics_avatars -- "Avatar uses" --> up_file_uploader

  %% Controller-UploadsActions concern
  c_uploads -- "Includes" --> c_conc_uploads_actions
  c_projects_uploads -- "Includes" --> c_conc_uploads_actions
  c_groups_uploads -- "Includes" --> c_conc_uploads_actions
  c_banzai_uploads -- "Includes" --> c_conc_uploads_actions
  c_ee_uploads -- "Extends" --> c_uploads

  c_conc_uploads_actions -- "Finds model using" --> m_upload

  %% Utility / Workhorse Authorization
  c_uploads -- "Authorize with" --> c_conc_workhorse_auth
  c_projects_uploads -- "Authorize with" --> c_conc_workhorse_auth
  c_groups_uploads -- "Authorize with" --> c_conc_workhorse_auth
  c_banzai_uploads -- "Authorize with" --> c_conc_workhorse_auth

  %% File sending, download utilities
  c_uploads -- "Send file using" --> c_conc_send_file
  c_projects_uploads -- "Send file using" --> c_conc_send_file
  c_groups_uploads -- "Send file using" --> c_conc_send_file
  c_banzai_uploads -- "Send file using" --> c_conc_send_file

  %% 4. Services: business logic
  s_upload_service -- "Persists uploads on" --> up_file_uploader
  s_upload_service -- "Creates" --> m_upload
  s_uploads_destroy -- "Removes" --> m_upload
  s_uploads_destroy -- "Authorizes via" --> pol_upload_policy

  s_packages_ml_file -- "Handles ML package files with" --> m_ml_model_version
  s_packages_ml_file -- "Create file" --> m_upload

  s_packages_nuget_metadata -- "Extracts" --> m_upload

  %% 5. Policies
  pol_upload_policy -- "Protects" --> c_uploads
  pol_upload_policy -- "Protects" --> s_uploads_destroy
  pol_tf_state_policy -- "Restricts terraform state" --> m_upload

  %% 6. Uploader infrastructure
  infra_uploaded_file -- "Presented to" --> up_file_uploader
  infra_uploaded_file -- "Validated by" --> s_upload_service

  infra_carrier_wave_s3_patch -- "Patches" --> m_uploads_fog
  infra_carrier_wave_perf_patch -- "Patches" --> up_file_uploader

  infra_carrier_wave_string_file -- "Wraps string as file for" --> up_file_uploader

  infra_object_storage_spec -- "Covers" --> infra_uploaded_file
  infra_object_storage_cdn_spec -- "Covers" --> m_uploads_fog

  %% 7. Backup
  infra_backup_uploads -- "Backs up" --> m_upload
  infra_backup_uploads -- "Accesses" --> up_file_uploader

  %% 8. Finders, Markdown integration, Banzai pipeline
  f_banzai_uploads_finder -- "Finds uploads for" --> m_upload
  f_banzai_uploads_finder -- "Called by" --> f_banzai_md_uploads
  f_banzai_md_uploads -- "API endpoint in" --> c_banzai_uploads
  f_banzai_md_uploads -- "Returns" --> m_upload

  f_gfm_uploads_rewriter -- "Migrates upload links" --> m_upload
  f_gfm_uploads_rewriter -- "Uses patterns from" --> up_file_uploader

  f_banzai_filter_service -- "Rewrites links in service desk markdown" --> m_upload

  %% 9. Auxiliary helpers, expiry, QA
  c_autocomplete_exp -- "Handles HTTP cache expiry for" --> c_uploads

  %% 10. Error Handling
  err_failure_handler -- "Handles mail upload errors for" --> m_upload

  %% 11. Spec files for coverage & safety
  spec_every_uploader -- "Ensures Geo support for" --> up_file_uploader
  spec_content_type -- "Validates" --> up_file_uploader
  spec_records_uploads -- "Validates" --> up_records_uploads
  spec_object_storage -- "Validates" --> m_uploads_fog
  spec_object_storage_cdn -- "Validates CDN on" --> m_uploads_fog

  spec_uploads_controller -- "Test coverage for" --> c_uploads
  spec_projects_uploads_controller -- "Test coverage for" --> c_projects_uploads
  spec_groups_uploads_controller -- "Test coverage for" --> c_groups_uploads

  spec_email_attachment -- "Covers attachment upload" --> up_attachment
  spec_decompressed_validator -- "Validates decompression for" --> s_upload_service

  %% 12. EE Extensions
  c_ee_uploads -- "Adds model extensions to" --> m_ee_upload

  %% 13. Link up all core node roots for diagram neatness
  up_file_uploader -- "Manipulates files for" --> m_upload
  up_attachment -- "Stores attachments for" --> m_upload

  %% Styling consistency for gap between groups and clean flow
  style "Models & Data Structures" fill:#F8F8F8,stroke:#B7E6ED,stroke-width:3px,stroke-dasharray: 4
  style "Uploaders & Uploader Abstractions" fill:#F8F8F8,stroke:#B7E6ED,stroke-width:3px,stroke-dasharray: 4
  style "Controllers & Upload APIs" fill:#F8F8F8,stroke:#B7E6ED,stroke-width:3px,stroke-dasharray: 4
  style "Service Layer" fill:#F8F8F8,stroke:#B7E6ED,stroke-width:3px,stroke-dasharray: 4
  style "Uploader Infrastructure & Storage Adapters" fill:#F8F8F8,stroke:#B7E6ED,stroke-width:3px,stroke-dasharray: 4
  style "Finders & Markdown/Link Integrations" fill:#F8F8F8,stroke:#B7E6ED,stroke-width:3px,stroke-dasharray: 4
  style "Policies & Authorization" fill:#F8F8F8,stroke:#F9E6C0,stroke-width:3px,stroke-dasharray: 4
  style "Error Handling" fill:#F8F8F8,stroke:#FDCFCF,stroke-width:3px,stroke-dasharray: 4
  style "Specs & QA Test Coverage" fill:#F8F8F8,stroke:#BDEABB,stroke-width:3px,stroke-dasharray: 4
```