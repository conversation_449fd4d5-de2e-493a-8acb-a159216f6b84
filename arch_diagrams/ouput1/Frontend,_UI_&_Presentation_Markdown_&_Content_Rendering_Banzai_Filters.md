```mermaid
flowchart TD
  %% === COLORS ===
  %% Core domain files: #D4F1F9 (Pastel Blue)
  %% Supporting/utility files: #FFF8DC (Pastel Yellow)
  %% Data structure files: #E0F8E0 (Pastel Green)
  %% Error handling files: #FFE4E1 (Pastel Red)
  %% Initialization/setup files: #E6E6FA (Pastel Purple)
  %% Groupings/subgraphs: #F8F8F8 with pastel borders

  %% === BANZAI PIPELINES ===
  subgraph banzai_pipelines["Banzai Pipelines" ]
    direction TB
    style banzai_pipelines fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded-rectangle

    pipeline_gfm["lib/banzai/pipeline/gfm_pipeline.rb" ]
    style pipeline_gfm fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1.5px,rounded-rectangle

    pipeline_duo["ee/lib/banzai/pipeline/duo_chat_documentation_pipeline.rb" ]
    style pipeline_duo fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1.5px,rounded-rectangle

    pipeline_jira["ee/lib/banzai/pipeline/jira_gfm_pipeline.rb" ]
    style pipeline_jira fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1.5px,rounded-rectangle

    pipeline_single["ee/lib/ee/banzai/pipeline/single_line_pipeline.rb"]
    style pipeline_single fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1.5px,rounded-rectangle

    pipeline_postprocess["ee/lib/ee/banzai/pipeline/post_process_pipeline.rb"]
    style pipeline_postprocess fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1.5px,rounded-rectangle

    pipeline_gfm_ext["ee/lib/ee/banzai/pipeline/gfm_pipeline.rb"]
    style pipeline_gfm_ext fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1.5px,rounded-rectangle
  end

  %% === FILTER GROUPS ===
  subgraph markdown_filter_chain["Markdown Content Filter Chain"]
    direction TB
    style markdown_filter_chain fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded-rectangle

    markdown_filter_base["lib/banzai/filter_array.rb"]
    style markdown_filter_base fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    filter_markdown["lib/banzai/filter/markdown_pre_escape_legacy_filter.rb"]
    style filter_markdown fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    filter_postescape["lib/banzai/filter/markdown_post_escape_legacy_filter.rb"]
    style filter_postescape fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    filter_autolink["lib/banzai/filter/autolink_filter.rb"]
    style filter_autolink fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    filter_spaced["lib/banzai/filter/spaced_link_filter.rb"]
    style filter_spaced fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    filter_absolute["lib/banzai/filter/absolute_link_filter.rb"]
    style filter_absolute fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    filter_external["lib/banzai/filter/external_link_filter.rb"]
    style filter_external fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    filter_inline_diff["lib/banzai/filter/inline_diff_filter.rb"]
    style filter_inline_diff fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    filter_task_list["lib/banzai/filter/task_list_filter.rb"]
    style filter_task_list fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    filter_math["lib/banzai/filter/math_filter.rb"]
    style filter_math fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    filter_mermaid["lib/banzai/filter/mermaid_filter.rb"]
    style filter_mermaid fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    filter_syntax["lib/banzai/filter/syntax_highlight_filter.rb"]
    style filter_syntax fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    filter_custom_emoji["lib/banzai/filter/custom_emoji_filter.rb"]
    style filter_custom_emoji fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    filter_footnote["lib/banzai/filter/footnote_filter.rb"]
    style filter_footnote fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    filter_blockquote["lib/banzai/filter/blockquote_fence_legacy_filter.rb"]
    style filter_blockquote fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    filter_suggestion["lib/banzai/filter/suggestion_filter.rb"]
    style filter_suggestion fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    filter_truncate["lib/banzai/filter/truncate_visible_filter.rb"]
    style filter_truncate fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    filter_convert["lib/banzai/filter/convert_text_to_doc_filter.rb"]
    style filter_convert fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    filter_set_dir["lib/banzai/filter/set_direction_filter.rb"]
    style filter_set_dir fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    filter_asciidoc_post["lib/banzai/filter/ascii_doc_post_processing_filter.rb"]
    style filter_asciidoc_post fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    filter_gollum_tags["lib/banzai/filter/gollum_tags_filter.rb"]
    style filter_gollum_tags fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle
  end

  subgraph rendering_filters["Rendering & Content Enrichment Filters"]
    direction TB
    style rendering_filters fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded-rectangle

    filter_include["lib/banzai/filter/include_filter.rb"]
    style filter_include fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    filter_front_matter["lib/banzai/filter/front_matter_filter.rb"]
    style filter_front_matter fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    filter_broadcast["lib/banzai/filter/broadcast_message_placeholders_filter.rb"]
    style filter_broadcast fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    filter_attributes["lib/banzai/filter/attributes_filter.rb"]
    style filter_attributes fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    filter_escape["lib/banzai/filter/escaped_char_filter.rb"]
    style filter_escape fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle
  end

  %% === IMAGE AND MEDIA HANDLING ===
  subgraph image_media_filters["Image & Media Filters"]
    direction TB
    style image_media_filters fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2px,rounded-rectangle

    filter_image_link["lib/banzai/filter/image_link_filter.rb"]
    style filter_image_link fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1px,rounded-rectangle

    filter_image_lazy["lib/banzai/filter/image_lazy_load_filter.rb"]
    style filter_image_lazy fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1px,rounded-rectangle

    filter_asset_proxy["lib/banzai/filter/asset_proxy_filter.rb"]
    style filter_asset_proxy fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1px,rounded-rectangle

    filter_upload["lib/banzai/filter/upload_link_filter.rb"]
    style filter_upload fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1px,rounded-rectangle

    filter_repository["lib/banzai/filter/repository_link_filter.rb"]
    style filter_repository fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1px,rounded-rectangle

    filter_audio["lib/banzai/filter/audio_link_filter.rb"]
    style filter_audio fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1px,rounded-rectangle

    filter_video["lib/banzai/filter/video_link_filter.rb"]
    style filter_video fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1px,rounded-rectangle

    filter_wiki_link["lib/banzai/filter/wiki_link_filter.rb"]
    style filter_wiki_link fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1px,rounded-rectangle

    filter_wiki_gollum["lib/banzai/filter/wiki_link_gollum_filter.rb"]
    style filter_wiki_gollum fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1px,rounded-rectangle

    filter_jira_img["ee/lib/banzai/filter/jira_private_image_link_filter.rb"]
    style filter_jira_img fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1px,rounded-rectangle
  end

  %% === SANITIZATION FILTERS ===
  subgraph sanitization_filters["Sanitization Filters"]
    direction TB
    style sanitization_filters fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2px,rounded-rectangle

    filter_sanitize_base["lib/banzai/filter/base_sanitization_filter.rb"]
    style filter_sanitize_base fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1.5px,rounded-rectangle

    filter_sanitize["lib/banzai/filter/sanitization_filter.rb"]
    style filter_sanitize fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1.5px,rounded-rectangle

    filter_asciidoc_sanitize["lib/banzai/filter/ascii_doc_sanitization_filter.rb"]
    style filter_asciidoc_sanitize fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1.5px,rounded-rectangle

    filter_minimum_sanitize["lib/banzai/filter/minimum_markdown_sanitization_filter.rb"]
    style filter_minimum_sanitize fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1.5px,rounded-rectangle

    filter_sanitize_link["lib/banzai/filter/sanitize_link_filter.rb"]
    style filter_sanitize_link fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1.5px,rounded-rectangle

    filter_html_entity["lib/banzai/filter/html_entity_filter.rb"]
    style filter_html_entity fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1.5px,rounded-rectangle

    filter_normalize["lib/banzai/filter/normalize_source_filter.rb"]
    style filter_normalize fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1.5px,rounded-rectangle

    filter_crossproject_iss["ee/lib/banzai/filter/cross_project_issuable_information_filter.rb"]
    style filter_crossproject_iss fill:#FFE4E1,stroke:#FFE4E1,stroke-width:1.5px,rounded-rectangle
  end

  %% === REFERENCE FILTERS ===
  subgraph reference_filters["Reference & Object-Link Expansion Filters"]
    direction TB
    style reference_filters fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded-rectangle

    filter_ref["lib/banzai/filter/references/reference_filter.rb"]
    style filter_ref fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1.5px,rounded-rectangle

    filter_ref_abstract["lib/banzai/filter/references/abstract_reference_filter.rb"]
    style filter_ref_abstract fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1.5px,rounded-rectangle

    filter_ref_cache["lib/banzai/filter/references/reference_cache.rb"]
    style filter_ref_cache fill:#D4F1F9,stroke:#E0F8E0,stroke-width:1.5px,rounded-rectangle

    filter_ref_issue["lib/banzai/filter/references/issue_reference_filter.rb"]
    style filter_ref_issue fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1.5px,rounded-rectangle

    filter_ref_mr["lib/banzai/filter/references/merge_request_reference_filter.rb"]
    style filter_ref_mr fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1.5px,rounded-rectangle

    filter_ref_commit["lib/banzai/filter/references/commit_reference_filter.rb"]
    style filter_ref_commit fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1.5px,rounded-rectangle

    filter_ref_commitrange["lib/banzai/filter/references/commit_range_reference_filter.rb"]
    style filter_ref_commitrange fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1.5px,rounded-rectangle

    filter_ref_design["lib/banzai/filter/references/design_reference_filter.rb"]
    style filter_ref_design fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1.5px,rounded-rectangle

    filter_ref_project["lib/banzai/filter/references/project_reference_filter.rb"]
    style filter_ref_project fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1.5px,rounded-rectangle

    filter_ref_external["lib/banzai/filter/references/external_issue_reference_filter.rb"]
    style filter_ref_external fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1.5px,rounded-rectangle

    filter_ref_label["lib/banzai/filter/references/label_reference_filter.rb"]
    style filter_ref_label fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1.5px,rounded-rectangle

    filter_ref_milestone["lib/banzai/filter/references/milestone_reference_filter.rb"]
    style filter_ref_milestone fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1.5px,rounded-rectangle

    filter_ref_snippet["lib/banzai/filter/references/snippet_reference_filter.rb"]
    style filter_ref_snippet fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1.5px,rounded-rectangle

    filter_ref_user["lib/banzai/filter/references/user_reference_filter.rb"]
    style filter_ref_user fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1.5px,rounded-rectangle

    filter_ref_alert["lib/banzai/filter/references/alert_reference_filter.rb"]
    style filter_ref_alert fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1.5px,rounded-rectangle

    filter_ref_vuln["lib/banzai/filter/references/vulnerability_reference_filter.rb"]
    style filter_ref_vuln fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1.5px,rounded-rectangle

    filter_ref_flag["lib/banzai/filter/references/feature_flag_reference_filter.rb"]
    style filter_ref_flag fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1.5px,rounded-rectangle

    filter_ref_iteration["ee/lib/banzai/filter/references/iteration_reference_filter.rb"]
    style filter_ref_iteration fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1.5px,rounded-rectangle

    filter_ref_epic["ee/lib/ee/banzai/filter/references/epic_reference_filter.rb"]
    style filter_ref_epic fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1.5px,rounded-rectangle

    filter_ref_cross_project["ee/lib/banzai/filter/cross_project_issuable_information_filter.rb"]
    style filter_ref_cross_project fill:#FFE4E1,stroke:#FFE4E1,stroke-width:1.5px,rounded-rectangle
  end

  %% === TABLE OF CONTENTS ===
  subgraph toc_filters["Table of Contents Filters"]
    direction TB
    style toc_filters fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2px,rounded-rectangle

    filter_toc_legacy["lib/banzai/filter/table_of_contents_legacy_filter.rb"]
    style filter_toc_legacy fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1px,rounded-rectangle

    filter_toc_tag_legacy["lib/banzai/filter/table_of_contents_tag_legacy_filter.rb"]
    style filter_toc_tag_legacy fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1px,rounded-rectangle

    filter_toc_tag["lib/banzai/filter/table_of_contents_tag_filter.rb"]
    style filter_toc_tag fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1px,rounded-rectangle
  end

  %% === RENDER CONTEXT AND UTILITIES ===
  subgraph reference_parsers["Reference Parsers & Context"]
    direction TB
    style reference_parsers fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2px,rounded-rectangle

    parser_alert["lib/banzai/reference_parser/alert_parser.rb"]
    style parser_alert fill:#FFF8DC,stroke:#FFF8DC,stroke-width:1px,rounded-rectangle

    parser_iteration["ee/lib/banzai/reference_parser/iteration_parser.rb"]
    style parser_iteration fill:#FFF8DC,stroke:#FFF8DC,stroke-width:1px,rounded-rectangle

    parser_iterations_cad["ee/lib/banzai/reference_parser/iterations_cadence_parser.rb"]
    style parser_iterations_cad fill:#FFF8DC,stroke:#FFF8DC,stroke-width:1px,rounded-rectangle

    object_renderer["lib/banzai/object_renderer.rb"]
    style object_renderer fill:#FFF8DC,stroke:#FFF8DC,stroke-width:1px,rounded-rectangle

    render_context["lib/banzai/render_context.rb"]
    style render_context fill:#FFF8DC,stroke:#FFF8DC,stroke-width:1px,rounded-rectangle

    color_parser["lib/banzai/color_parser.rb"]
    style color_parser fill:#FFF8DC,stroke:#FFF8DC,stroke-width:1px,rounded-rectangle
  end

  %% === CONCERNS / ASPECTS ===
  subgraph filter_concerns["Filter Concerns & Output Safety"]
    direction TB
    style filter_concerns fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2px,rounded-rectangle

    concerns_timeout["lib/banzai/filter/concerns/timeout_filter_handler.rb"]
    style concerns_timeout fill:#FFF8DC,stroke:#FFF8DC,stroke-width:1px,rounded-rectangle

    concerns_pipeline_timing["lib/banzai/filter/concerns/pipeline_timing_check.rb"]
    style concerns_pipeline_timing fill:#FFF8DC,stroke:#FFF8DC,stroke-width:1px,rounded-rectangle

    concerns_output_safety["lib/banzai/filter/concerns/output_safety.rb"]
    style concerns_output_safety fill:#FFF8DC,stroke:#FFF8DC,stroke-width:1px,rounded-rectangle
  end

  %% === DOMAIN INTEGRATION / DATA STRUCTURES ===
  subgraph domain_structures["Markdown Field Caching & Sanitization"]
    direction TB
    style domain_structures fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2px,rounded-rectangle

    cache_markdown_field["app/models/concerns/cache_markdown_field.rb"]
    style cache_markdown_field fill:#E6E6FA,stroke:#E6E6FA,stroke-width:1px,rounded-rectangle

    sanitizable["app/models/concerns/sanitizable.rb"]
    style sanitizable fill:#E6E6FA,stroke:#E6E6FA,stroke-width:1px,rounded-rectangle
  end

  %% === EE EXTENSIONS AND ENHANCEMENTS ===
  subgraph ee_extensions["EE: Advanced and Third-party Search & Filters"]
    direction TB
    style ee_extensions fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2px,rounded-rectangle

    search_elastic["ee/app/services/concerns/search/elasticsearchable.rb"]
    style search_elastic fill:#E6E6FA,stroke:#E6E6FA,stroke-width:1px,rounded-rectangle

    search_advanced_zoekt["ee/app/services/concerns/search/advanced_and_zoekt_searchable.rb"]
    style search_advanced_zoekt fill:#E6E6FA,stroke:#E6E6FA,stroke-width:1px,rounded-rectangle

    search_zoekt["ee/app/services/concerns/search/zoekt_searchable.rb"]
    style search_zoekt fill:#E6E6FA,stroke:#E6E6FA,stroke-width:1px,rounded-rectangle

    absolute_doc_link["ee/lib/banzai/filter/absolute_documentation_link_filter.rb"]
    style absolute_doc_link fill:#E6E6FA,stroke:#E6E6FA,stroke-width:1px,rounded-rectangle
  end

  %% === SUPPORTING UTILITIES ===
  subgraph supporting_utilities["Supporting Utilities and Helpers"]
    direction TB
    style supporting_utilities fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2px,rounded-rectangle

    string_range_marker["lib/gitlab/string_range_marker.rb"]
    style string_range_marker fill:#FFF8DC,stroke:#FFF8DC,stroke-width:1px,rounded-rectangle

    haml_no_plain["haml_lint/linter/no_plain_nodes.rb"]
    style haml_no_plain fill:#FFF8DC,stroke:#FFF8DC,stroke-width:1px,rounded-rectangle

    haml_inline_js["haml_lint/linter/inline_javascript.rb"]
    style haml_inline_js fill:#FFF8DC,stroke:#FFF8DC,stroke-width:1px,rounded-rectangle

    tracking_helper["app/helpers/tracking_helper.rb"]
    style tracking_helper fill:#FFF8DC,stroke:#FFF8DC,stroke-width:1px,rounded-rectangle
  end

  %% === ERROR HANDLING ===
  subgraph error_handling["Error and Redaction Filters"]
    direction TB
    style error_handling fill:#F8F8F8,stroke:#FFE4E1,stroke-width:2px,rounded-rectangle

    filter_reference_redactor["lib/banzai/filter/reference_redactor_filter.rb"]
    style filter_reference_redactor fill:#FFE4E1,stroke:#FFE4E1,stroke-width:1px,rounded-rectangle
  end

  %% === SPEC COVERAGE / VALIDATION ===
  subgraph spec_coverage["Filter/Reference Spec Coverage"]
    direction TB
    style spec_coverage fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded-rectangle

    %% Markdown, link, and upload filter specs
    spec_repository["spec/lib/banzai/filter/repository_link_filter_spec.rb"]
    style spec_repository fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    spec_absolute["spec/lib/banzai/filter/absolute_link_filter_spec.rb"]
    style spec_absolute fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    spec_audio["spec/lib/banzai/filter/audio_link_filter_spec.rb"]
    style spec_audio fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    spec_image["spec/lib/banzai/filter/image_link_filter_spec.rb"]
    style spec_image fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    spec_upload["spec/lib/banzai/filter/upload_link_filter_spec.rb"]
    style spec_upload fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    spec_video["spec/lib/banzai/filter/video_link_filter_spec.rb"]
    style spec_video fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    spec_autolink["spec/lib/banzai/filter/autolink_filter_spec.rb"]
    style spec_autolink fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    spec_truncate["spec/lib/banzai/filter/truncate_visible_filter_spec.rb"]
    style spec_truncate fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    spec_issue["spec/lib/banzai/filter/references/issue_reference_filter_spec.rb"]
    style spec_issue fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    spec_toc["spec/lib/banzai/filter/table_of_contents_legacy_filter_spec.rb"]
    style spec_toc fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    spec_toc_tag["spec/lib/banzai/filter/table_of_contents_tag_legacy_filter_spec.rb"]
    style spec_toc_tag fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    spec_service_desk["spec/lib/banzai/filter/service_desk_upload_link_filter_spec.rb"]
    style spec_service_desk fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    spec_mention_anon["ee/spec/lib/gitlab/status_page/filter/mention_anonymization_filter_spec.rb"]
    style spec_mention_anon fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle
  end

  %% === SPECIALIZED FILTERS ===
  subgraph special_filters["Special Content Filters"]
    direction TB
    style special_filters fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded-rectangle

    filter_kroki["lib/banzai/filter/kroki_filter.rb"]
    style filter_kroki fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    filter_plantuml["lib/banzai/filter/plantuml_filter.rb"]
    style filter_plantuml fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    filter_dollar_math_post["lib/banzai/filter/dollar_math_post_legacy_filter.rb"]
    style filter_dollar_math_post fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle

    filter_issuable_expand["lib/banzai/filter/issuable_reference_expansion_filter.rb"]
    style filter_issuable_expand fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1px,rounded-rectangle
  end

  %% === NODE RELATIONSHIPS ===

  %% === Pipeline chains ===
  pipeline_gfm -->|uses| markdown_filter_chain
  pipeline_duo -->|extends| pipeline_gfm
  pipeline_duo --> filter_absolute
  pipeline_jira -->|extends| pipeline_gfm
  pipeline_jira --> filter_jira_img

  pipeline_single --> filter_ref_epic
  pipeline_single --> filter_ref_iteration
  pipeline_single --> filter_ref_vuln

  pipeline_gfm_ext --> pipeline_gfm
  pipeline_postprocess --> filter_ref_cross_project
  pipeline_postprocess --> pipeline_gfm

  %% === Filter chain ===
  markdown_filter_chain --> rendering_filters
  markdown_filter_chain --> image_media_filters
  markdown_filter_chain --> sanitization_filters
  markdown_filter_chain --> reference_filters
  markdown_filter_chain --> toc_filters
  markdown_filter_chain --> special_filters
  markdown_filter_chain --> filter_concerns

  %% === References dependency chain ===
  filter_ref_issue --> filter_ref
  filter_ref_issue --> filter_ref_abstract
  filter_ref_issue --> filter_ref_cache

  filter_ref_mr --> filter_ref_abstract
  filter_ref_mr --> filter_ref

  filter_ref_commit --> filter_ref_abstract
  filter_ref_commit --> filter_ref

  filter_ref_commitrange --> filter_ref_abstract
  filter_ref_commitrange --> filter_ref

  filter_ref_design --> filter_ref_abstract

  filter_ref_project --> filter_ref

  filter_ref_external --> filter_ref

  filter_ref_label --> filter_ref_abstract

  filter_ref_milestone --> filter_ref_abstract

  filter_ref_snippet --> filter_ref_abstract

  filter_ref_user --> filter_ref

  filter_ref_alert --> filter_ref_abstract

  filter_ref_vuln --> filter_ref_alert
  filter_ref_flag --> filter_ref_abstract

  filter_ref_iteration --> filter_ref_abstract

  filter_ref_epic --> filter_ref_abstract

  filter_ref_cross_project --> filter_ref_issue

  filter_ref_cache --> concerns_output_safety

  %% === Reference Parsers relationships ===
  parser_alert --> filter_ref_alert
  parser_iteration --> filter_ref_iteration
  parser_iterations_cad --> parser_iteration

  %% === Render Context / Utilities relationships ===
  object_renderer --> render_context
  object_renderer --> reference_filters
  object_renderer --> filter_reference_redactor

  %% === Reference expansion ===
  filter_issuable_expand --> rendering_filters
  filter_issuable_expand --> filter_ref_issue
  filter_issuable_expand --> filter_ref_mr

  %% === Image/media dependencies ===
  filter_image_link --> image_media_filters
  filter_image_link --> filter_concerns
  filter_image_link --> filter_attributes

  filter_asset_proxy --> filter_concerns
  filter_upload --> image_media_filters
  filter_upload --> filter_repository

  filter_repository --> filter_concerns

  filter_image_lazy --> filter_concerns

  filter_jira_img --> filter_image_link

  filter_audio --> image_media_filters
  filter_video --> image_media_filters

  filter_wiki_link --> filter_wiki_gollum

  %% === Sanitization Filter Structure ===
  filter_sanitize --> filter_sanitize_base
  filter_asciidoc_sanitize --> filter_sanitize_base
  filter_minimum_sanitize --> filter_sanitize_base
  filter_sanitize_link --> filter_concerns
  filter_sanitize --> filter_sanitize_link

  filter_crossproject_iss --> filter_ref_issue

  %% === Concerns shared across filters ===
  filter_markdown --> concerns_timeout
  filter_markdown --> concerns_pipeline_timing
  filter_postescape --> concerns_timeout
  filter_postescape --> concerns_pipeline_timing
  filter_autolink --> concerns_output_safety
  filter_autolink --> concerns_pipeline_timing
  filter_spaced --> concerns_timeout
  filter_task_list --> concerns_pipeline_timing
  filter_inline_diff --> concerns_pipeline_timing
  filter_custom_emoji --> concerns_pipeline_timing
  filter_custom_emoji --> concerns_timeout
  filter_custom_emoji --> concerns_output_safety
  filter_footnote --> concerns_pipeline_timing
  filter_suggestion --> concerns_pipeline_timing
  filter_blockquote --> concerns_pipeline_timing
  filter_dollar_math_post --> concerns_pipeline_timing
  filter_math --> concerns_pipeline_timing
  filter_mermaid --> concerns_pipeline_timing
  filter_kroki --> concerns_pipeline_timing
  filter_plantuml --> concerns_pipeline_timing
  filter_syntax --> concerns_timeout
  filter_syntax --> concerns_pipeline_timing
  filter_syntax --> concerns_output_safety

  %% === Table of Contents Chains ===
  filter_toc_tag_legacy --> filter_toc_tag
  filter_toc_tag_legacy --> filter_toc_legacy
  filter_toc_tag --> filter_toc_legacy

  %% === Domain integration and field cache ===
  cache_markdown_field --> markdown_filter_chain
  cache_markdown_field --> rendering_filters
  cache_markdown_field --> sanitizable

  sanitizable --> filter_sanitize

  %% === EE SEARCH EXTENSIONS ===
  search_advanced_zoekt --> search_elastic
  search_advanced_zoekt --> search_zoekt
  search_elastic --> search_zoekt

  %% === Absolute Documentation Filter usage ===
  pipeline_duo --> absolute_doc_link
  absolute_doc_link --> filter_absolute

  %% === Error/Redactor relationship ===
  filter_reference_redactor --> filter_ref_issue
  filter_reference_redactor --> filter_ref_mr
  filter_reference_redactor --> filter_ref_abstract

  %% === Specialized Filter hookups ===
  filter_dollar_math_post --> filter_math
  filter_kroki --> filter_math
  filter_plantuml --> filter_math

  %% === Support/utilities to filters ===
  string_range_marker --> filter_inline_diff

  tracking_helper --> filter_broadcast

  haml_no_plain --> markdown_filter_chain
  haml_inline_js --> markdown_filter_chain

  color_parser --> rendering_filters

  %% === Integration with other domains csp, workhorse ===
  one_trust_csp["app/controllers/concerns/one_trust_csp.rb"]
  style one_trust_csp fill:#FFF8DC,stroke:#FFF8DC,stroke-width:1px,rounded-rectangle

  workhorse_request["app/controllers/concerns/workhorse_request.rb"]
  style workhorse_request fill:#FFF8DC,stroke:#FFF8DC,stroke-width:1px,rounded-rectangle

  one_trust_csp --> pipeline_gfm
  workhorse_request --> filter_upload

  %% === Filter Spec Coverage relationships ===
  spec_repository --> filter_repository
  spec_absolute --> filter_absolute
  spec_audio --> filter_audio
  spec_image --> filter_image_link
  spec_upload --> filter_upload
  spec_video --> filter_video
  spec_autolink --> filter_autolink
  spec_truncate --> filter_truncate
  spec_issue --> filter_ref_issue
  spec_toc --> filter_toc_legacy
  spec_toc_tag --> filter_toc_tag_legacy
  spec_service_desk --> filter_upload
  spec_mention_anon --> filter_ref_issue

  %% === Interrelations across reference filters ===
  filter_ref_abstract --> filter_ref

  %% === Special: EE enhancements connect to pipelines ===
  pipeline_gfm_ext --> pipeline_gfm
  pipeline_gfm_ext --> filter_ref_epic
  pipeline_gfm_ext --> filter_ref_iteration
  pipeline_gfm_ext --> filter_ref_vuln

  %% === Compose pipelines to filters ===
  pipeline_gfm --> filter_ref_issue
  pipeline_gfm --> filter_ref_mr
  pipeline_gfm --> filter_ref_commit
  pipeline_gfm --> filter_ref_commitrange
  pipeline_gfm --> filter_ref_user
  pipeline_gfm --> filter_ref_label
  pipeline_gfm --> filter_ref_snippet
  pipeline_gfm --> filter_ref_alert
  pipeline_gfm --> filter_ref_project
  pipeline_gfm --> filter_ref_external
  pipeline_gfm --> filter_ref_milestone
  pipeline_gfm --> filter_ref_vuln

  %% === Cross project issuable filter wiring ===
  filter_ref_cross_project --> filter_ref_issue
  filter_ref_cross_project --> filter_ref_mr

  %% === Table of Contents wiring to markdown chain ===
  markdown_filter_chain --> toc_filters

  %% === Absolute doc link relates to jira and duo pipelines
  pipeline_jira --> absolute_doc_link
  pipeline_duo --> absolute_doc_link

  %% === End ===
```