```mermaid
flowchart TD
  %% Define node styles
  classDef coreDomain fill:#D4F1F9,stroke:#A1CDE6,stroke-width:2px,color:#333,stroke-dasharray:1,rx:12,ry:12
  classDef support fill:#FFF8DC,stroke:#F5DEB3,stroke-width:2px,color:#333,rx:12,ry:12
  classDef dataStruct fill:#E0F8E0,stroke:#AFD8B0,stroke-width:2px,color:#333,rx:12,ry:12
  classDef errorHandling fill:#FFE4E1,stroke:#EAA2A2,stroke-width:2px,color:#333,rx:12,ry:12
  classDef init fill:#E6E6FA,stroke:#CBC3E3,stroke-width:2px,color:#333,rx:12,ry:12
  classDef group fill:#F8F8F8,stroke-width:3px,stroke-dasharray:4,color:#333

  %% Invitations & Membership Core Flow
  subgraph sg_invite_core["Invitations & Memberships Core" ]
    direction TB
    InviteMailer["invite_mailer.rb\nInvite emails, tokens, descriptions":::coreDomain]
    InviteAcceptedMailer["invite_accepted_mailer.rb\nAcceptance notifications, email rendering":::coreDomain]
    InviteDeclinedMailer["invite_declined_mailer.rb\nDecline notifications, email rendering":::coreDomain]
    InviteReminderMailer["invite_reminder_mailer.rb\nInvite reminder logic, reminders sequence":::coreDomain]
    InviteService["invite_service.rb\nProcess invite inputs, email & user validation":::coreDomain]
    CreateService["create_service.rb\nBase member creation workflow, validation, error classes":::coreDomain]
    StandardMemberBuilder["standard_member_builder.rb\nMember instantiation, base builder":::coreDomain]
    InviteMemberBuilder["invite_member_builder.rb\nInvitee-specific member creation, email matching":::coreDomain]
    InvitationReminderEmailService["invitation_reminder_email_service.rb\nSchedules reminder emails, calculates timings":::coreDomain]
    RemoveUnacceptedMemberInvitesWorker["remove_unaccepted_member_invites_worker.rb\nCleans up stale invites":::init]
    MemberInvitationReminderEmailsWorker["member_invitation_reminder_emails_worker.rb\nSends reminders for pending invitations":::init]
    InviteMembersHelper["invite_members_helper.rb\nInvite UI helpers: notices, modal, select data":::support]

    InviteService -- extends --> CreateService
    InviteMemberBuilder -- extends --> StandardMemberBuilder
    InviteMailer -- uses --> InviteService
    InviteMailer -- uses --> InviteMemberBuilder
    InviteAcceptedMailer -- uses --> CreateService
    InviteDeclinedMailer -- uses --> CreateService
    InviteReminderMailer -- uses --> InvitationReminderEmailService
    InvitationReminderEmailService -- uses --> InviteMailer
    MemberInvitationReminderEmailsWorker -- uses --> InvitationReminderEmailService
    MemberInvitationReminderEmailsWorker -- uses --> InviteReminderMailer
    RemoveUnacceptedMemberInvitesWorker -- monitors --> InviteService
    InviteMembersHelper -- supports --> InviteService
    InviteMembersHelper -- supports --> InviteMemberBuilder
    InviteReminderMailer -- receives --> InviteService
    InviteReminderMailer -- uses --> InviteMembersHelper
  end
  class sg_invite_core group

  %% Membership/Access Requests & Approval
  subgraph sg_membership_access["Membership & Access Requests" ]
    direction TB
    AccessRequestsFinder["access_requests_finder.rb\nFinder: access requests by source":::coreDomain]
    RequestAccessService["request_access_service.rb\nHandles request submission, access checks":::coreDomain]
    ApproveAccessRequestService["approve_access_request_service.rb\nApproves pending access requests":::coreDomain]
    AccessRequestedMailer["access_requested_mailer.rb\nEmail to admins on access request":::coreDomain]
    AccessDeniedMailer["access_denied_mailer.rb\nEmail to user when request denied":::coreDomain]
    AccessDeniedMailerPreview["access_denied_mailer_preview.rb\nDev previews for denial emails":::support]
    AccessRequestedMailerPreview["access_requested_mailer_preview.rb\nDev previews for request emails":::support]
    ApproveAccessRequestServiceEE["ee/members/approve_access_request_service.rb\nEE: Extended access checks, seat policies":::coreDomain]
  end
  class sg_membership_access group

  AccessRequestsFinder -- provides --> RequestAccessService
  RequestAccessService -- uses --> AccessRequestedMailer
  ApproveAccessRequestService -- uses --> AccessDeniedMailer
  ApproveAccessRequestService -- extends --> RequestAccessService
  ApproveAccessRequestServiceEE -- extends --> ApproveAccessRequestService
  AccessDeniedMailerPreview -- previews --> AccessDeniedMailer
  AccessRequestedMailerPreview -- previews --> AccessRequestedMailer
  ApproveAccessRequestService -- can trigger --> AccessDeniedMailer

  %% Mailers & Notification Previews
  subgraph sg_mailers["Mailers & Previews"]
    direction TB
    AccessDeniedMailer
    InviteAcceptedMailerPreview["invite_accepted_mailer_preview.rb\nPreview for invite accepted email":::support]
    InviteDeclinedMailerPreview["invite_declined_mailer_preview.rb\nPreview for invite declined email":::support]
    InviteReminderMailerPreview["invite_reminder_mailer_preview.rb\nPreviews all invite reminder variants":::support]
    InviteAcceptedMailerPreview -- previews --> InviteAcceptedMailer
    InviteDeclinedMailerPreview -- previews --> InviteDeclinedMailer
    InviteReminderMailerPreview -- previews --> InviteReminderMailer
  end
  class sg_mailers group

  %% Email/Notification Concern and Extensions
  subgraph sg_email_core["Email Composition & Shared Logic"]
    direction TB
    EmailsMembers["emails/members.rb\nConcern: notification logic, member emails":::coreDomain]
  end
  class sg_email_core group

  InviteMailer -- includes --> EmailsMembers
  InviteAcceptedMailer -- includes --> EmailsMembers
  InviteDeclinedMailer -- includes --> EmailsMembers
  InviteReminderMailer -- includes --> EmailsMembers
  AccessDeniedMailer -- includes --> EmailsMembers
  AccessRequestedMailer -- includes --> EmailsMembers

  %% Member Event / Life-cycle Handling
  subgraph sg_member_events["Member Lifecycle & Events"]
    direction TB
    MembersAddedEvent["members_added_event.rb\nEvent: member added schema":::coreDomain]
    MembersActivateServiceEE["ee/members/activate_service.rb\nEE: Bulk/conditional activate memberships":::coreDomain]
    RemoveUnacceptedMemberInvitesWorker
    MemberInvitationReminderEmailsWorker
    MembersAddedEvent -- triggers --> MembersActivateServiceEE
    MembersAddedEvent -- model --> InviteMailer
    MemberInvitationReminderEmailsWorker -- triggers --> InviteReminderMailer
    RemoveUnacceptedMemberInvitesWorker -- actsOn --> InviteService
  end
  class sg_member_events group

  %% Data Model Abstractions
  subgraph sg_data_models["Domain Data Models"]
    direction TB
    TermAgreement["term_agreement.rb\nMember terms version acceptance":::dataStruct]
    MembershipPlaceholders["import/placeholders/membership.rb\nImported member state tracking":::dataStruct]
  end
  class sg_data_models group

  InviteService -- references --> TermAgreement
  InviteMemberBuilder -- can map--> MembershipPlaceholders
  ApproveAccessRequestService -- updates --> TermAgreement

  %% Invite Flow Integration: UI, Controllers, Initializers
  subgraph sg_invite_flow["Invitation & Access Request Flow Integration"]
    direction TB
    InvitesController["invites_controller.rb\nMain invites flow, authentication step, details rendering":::init]
    VerifiesWithEmail["concerns/verifies_with_email.rb\nHandle email verification for invite flows":::init]
    DashboardHelper["dashboard_helper.rb\nProvides user-role data for dashboard, overview":::support]
    InviteMembersHelper
    InviteMembersHelper -- supports --> InvitesController
    InvitesController -- includes --> VerifiesWithEmail
    InvitesController -- uses --> InviteService
    InvitesController -- shows --> DashboardHelper
  end
  class sg_invite_flow group

  %% Workers (Domain Coordination)
  subgraph sg_workers["Workers Reminders & Cleanup"]
    direction TB
    RemoveUnacceptedMemberInvitesWorker
    MemberInvitationReminderEmailsWorker
  end
  class sg_workers group

  %% QA & Spec Feature Orchestration
  subgraph sg_qa_spec["QA/Spec Feature Orchestration"]
    direction TB
    FeatureInviteFlowSpec["ee/spec/features/registrations/saas/invite_flow_spec.rb\nSaaS invite flows, registration scenarios":::support]
    SignUpHelpers["sign_up_helpers.rb\nFills in sign up form with invites":::support]
    InviteMembersModalHelpers["features/invite_members_modal_helpers.rb\nAutomates interaction with invite members UI":::support]
    FeatureInviteFlowSpec -- uses --> SignUpHelpers
    FeatureInviteFlowSpec -- automates --> InviteMembersModalHelpers
  end
  class sg_qa_spec group

  %% Supporting Domain Finders, Policies, Presenters
  subgraph sg_auxiliary_support["Policy, Finder & Presentation Layer"]
    direction TB
    PersonalAccessTokensFinder["personal_access_tokens_finder.rb\nPAT filtering in member context":::support]
    ClosedAtFilterable["concerns/closed_at_filterable.rb\nScoped filtering for expired/inactive":::support]
    OnboardingStatusPresenter["onboarding/status_presenter.rb\nPresents onboarding and invite state for UI":::support]
    UserPolicy["user_policy.rb\nGatekeeping: what users may do in invite/access context":::support]
    OnboardingStatusPresenter -- provides --> InvitesController
    UserPolicy -- restricts --> InviteService
  end
  class sg_auxiliary_support group

  %% Domain Event Integration
  subgraph sg_events["Event Store & Authorizations"]
    direction TB
    ProjectAuthorizationsEvent["project_authorizations/authorizations_added_event.rb\nTrack granted project authorizations":::coreDomain]
    MembersAddedEvent
    MembersAddedEvent -- signals --> ProjectAuthorizationsEvent
  end
  class sg_events group

  %% Connect key flows across groups
  InviteMailer -- isTriggeredBy --> InvitesController
  InviteReminderMailer -- isTriggeredBy --> MemberInvitationReminderEmailsWorker
  InviteAcceptedMailer -- isTriggeredBy --> InviteService
  InviteDeclinedMailer -- isTriggeredBy --> InviteService
  InviteMailer -- canTrigger --> InviteAcceptedMailer
  InviteMailer -- canTrigger --> InviteDeclinedMailer
  InviteMailer -- can trigger --> InviteReminderMailer
  InviteAcceptedMailer -- updates --> ProjectAuthorizationsEvent
  AccessRequestedMailer -- isTriggeredBy --> RequestAccessService
  AccessDeniedMailer -- isTriggeredBy --> ApproveAccessRequestService
  MembersAddedEvent -- can trigger --> MemberInvitationReminderEmailsWorker
  AccessDeniedMailer -- notifies --> UserPolicy

  %% Add preview mailer tests for coverage
  InviteAcceptedMailerPreview -- confirmsRendering --> InviteAcceptedMailer
  InviteDeclinedMailerPreview -- confirmsRendering --> InviteDeclinedMailer
  InviteReminderMailerPreview -- confirmsRendering --> InviteReminderMailer
  AccessDeniedMailerPreview -- confirmsRendering --> AccessDeniedMailer

  %% Connect data models to workflow
  InviteMemberBuilder -- persists --> MembershipPlaceholders
  InviteMemberBuilder -- checks --> TermAgreement

  %% Entry/Exit points (Controllers)
  InvitesController -- orchestrates --> InviteService
  InvitesController -- orchestrates --> InviteMembersHelper
  InvitesController -- feeds --> InviteMailer
  InvitesController -- returnsStateTo --> OnboardingStatusPresenter

  %% Error handling across domain
  CreateService -- raises --> errorHandling["Internal Errors\ne.g., BlankInvitesError,\nTooManyInvitesError":::errorHandling]
  InviteService -- raises --> errorHandling
  ApproveAccessRequestService -- raises --> errorHandling

  %% Group edges for overall context
  sg_invite_core --- sg_membership_access
  sg_invite_core --- sg_mailers
  sg_invite_core --- sg_email_core
  sg_invite_core --- sg_member_events
  sg_invite_core --- sg_data_models
  sg_invite_core --- sg_invite_flow
  sg_invite_core --- sg_workers
  sg_invite_core --- sg_qa_spec
  sg_invite_core --- sg_auxiliary_support
  sg_invite_core --- sg_events

  %% Subgraph border order for clarity
  sg_membership_access:::group
  sg_invite_core:::group
  sg_invite_flow:::group
  sg_mailers:::group
  sg_email_core:::group
  sg_auxiliary_support:::group
  sg_member_events:::group
  sg_data_models:::group
  sg_qa_spec:::group
  sg_events:::group
  sg_workers:::group
```