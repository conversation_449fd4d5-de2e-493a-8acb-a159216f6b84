```mermaid
graph TD
  %% VERTICAL LAYOUT AND COLORS
  %% Core domain files: #D4F1F9
  %% Supporting/utility files: #FFF8DC
  %% Data structure files: #E0F8E0
  %% Error handling files: #FFE4E1
  %% Initialization/setup files: #E6E6FA
  %% Logical groupings: #F8F8F8 with pastel borders

  %% CORE CONCEPT: PIPELINE AND DOMAIN DATA STRUCTURES
  subgraph "CI/CD Pipeline Core Data Structures"["CI/CD Pipeline Core Data Structures"]
    style "CI/CD Pipeline Core Data Structures" fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rx:20
    ci_pipeline[[Ci::Pipeline]]
    style ci_pipeline fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15
    ci_bridge[[Ci::Bridge]]
    style ci_bridge fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15
    ci_stage[[Ci::Stage]]
    style ci_stage fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15
    ci_build[[Ci::Build]]
    style ci_build fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15
    ci_sources_pipeline[[Ci::Sources::Pipeline]]
    style ci_sources_pipeline fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rx:15
    ci_pipeline_config[[Ci::PipelineConfig]]
    style ci_pipeline_config fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rx:15
    ci_pipeline_chat_data[[Ci::PipelineChatData]]
    style ci_pipeline_chat_data fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rx:15
    ci_resource[[Ci::Resource]]
    style ci_resource fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rx:15
    ci_build_metadata[[Ci::BuildMetadata]]
    style ci_build_metadata fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rx:15
    ci_build_tag[[Ci::BuildTag]]
    style ci_build_tag fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rx:15
    ci_commit_with_pipeline[[Ci::CommitWithPipeline]]
    style ci_commit_with_pipeline fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rx:15
    ci_build_report_result[[Ci::BuildReportResult]]
    style ci_build_report_result fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rx:15
    ci_running_build[[Ci::RunningBuild]]
    style ci_running_build fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rx:15
    ci_build_trace_metadata[[Ci::BuildTraceMetadata]]
    style ci_build_trace_metadata fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rx:15
    ci_build_execution_config[[Ci::BuildExecutionConfig]]
    style ci_build_execution_config fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rx:15
    ci_pipeline_message[[Ci::PipelineMessage]]
    style ci_pipeline_message fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rx:15
    ci_project_with_pipeline_var[[Ci::ProjectWithPipelineVariable]]
    style ci_project_with_pipeline_var fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rx:15
    ci_trigger_request[[Ci::TriggerRequest]]
    style ci_trigger_request fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rx:15
    commit_status[[CommitStatus]]
    style commit_status fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rx:15
    generic_commit_status[[GenericCommitStatus]]
    style generic_commit_status fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rx:15

    ci_pipeline-->|has many|ci_stage
    ci_pipeline-->|has many|ci_build
    ci_pipeline-->|has chat data|ci_pipeline_chat_data
    ci_pipeline-->|has pipeline config|ci_pipeline_config
    ci_pipeline-->|has sources|ci_sources_pipeline
    ci_build-->|has metadata|ci_build_metadata
    ci_build-->|has tag|ci_build_tag
    ci_build-->|build report|ci_build_report_result
    ci_build-->|pending state|ci_running_build
    ci_build-->|trace metadata|ci_build_trace_metadata
    ci_build-->|execution config|ci_build_execution_config
    ci_pipeline-->|has resource group|ci_resource
    ci_pipeline-->|has message|ci_pipeline_message
    ci_pipeline-->|project pipeline var|ci_project_with_pipeline_var
    ci_pipeline-->|triggered by|ci_trigger_request
    commit_status-->|generalizes|generic_commit_status
    ci_pipeline-->|can have bridge|ci_bridge
  end

  %% PIPELINE CREATION: ABSTRACT/CHAIN/PROCESSING STRUCTURE
  subgraph "Pipeline Creation & Execution Logic"["Pipeline Creation & Execution Logic"]
    style "Pipeline Creation & Execution Logic" fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rx:20
    b_create_pipeline_service[[CreatePipelineService]]
    style b_create_pipeline_service fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15
    b_create_downstream_pipeline_service[[CreateDownstreamPipelineService]]
    style b_create_downstream_pipeline_service fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15
    b_trigger_downstream_pipeline_service[[TriggerDownstreamPipelineService]]
    style b_trigger_downstream_pipeline_service fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15
    b_pipeline_processing_service[[ProcessPipelineService]]
    style b_pipeline_processing_service fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15
    b_pipeline_processing_atomic_service[[PipelineProcessing::AtomicProcessingService]]
    style b_pipeline_processing_atomic_service fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15
    b_pipeline_creation_start_service[[PipelineCreation::StartPipelineService]]
    style b_pipeline_creation_start_service fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15
    b_pipeline_creation_requests[[PipelineCreation::Requests]]
    style b_pipeline_creation_requests fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rx:15
    b_pipeline_creation_cancel_redundant[[PipelineCreation::CancelRedundantPipelinesService]]
    style b_pipeline_creation_cancel_redundant fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15
    b_pipeline_triggers_create[[PipelineTriggers::CreateService]]
    style b_pipeline_triggers_create fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15
    b_pipeline_triggers_destroy[[PipelineTriggers::DestroyService]]
    style b_pipeline_triggers_destroy fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15

    b_create_pipeline_service-->|calls/initiates|b_pipeline_processing_service
    b_create_pipeline_service-->|uses|b_pipeline_creation_requests
    b_create_downstream_pipeline_service-->|triggers|b_create_pipeline_service
    b_pipeline_processing_service-->|uses|b_pipeline_processing_atomic_service
    b_pipeline_processing_atomic_service-->|progresses|b_pipeline_creation_start_service
    b_pipeline_creation_start_service-->|modifies status|ci_pipeline
    b_pipeline_creation_requests-->|tracks|ci_pipeline
    b_pipeline_creation_cancel_redundant-->|cancels|ci_pipeline
    b_pipeline_triggers_create-->|creates|ci_trigger_request
    b_pipeline_triggers_destroy-->|removes|ci_trigger_request
    b_create_downstream_pipeline_service-->|manages downstream via|b_trigger_downstream_pipeline_service
  end

  %% PIPELINE EXECUTION: WORKERS / BACKGROUND PROCESSING
  subgraph "Pipeline Execution Background Workers"["Pipeline Execution Background Workers"]
    style "Pipeline Execution Background Workers" fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rx:20
    w_initial_pipeline_process_worker[[InitialPipelineProcessWorker]]
    style w_initial_pipeline_process_worker fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rx:15
    w_pipeline_bridge_status_worker[[PipelineBridgeStatusWorker]]
    style w_pipeline_bridge_status_worker fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rx:15
    w_drop_pipeline_worker[[DropPipelineWorker]]
    style w_drop_pipeline_worker fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rx:15
    w_pending_build_update_project[[PendingBuilds::UpdateProjectWorker]]
    style w_pending_build_update_project fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rx:15
    w_pending_build_update_group[[PendingBuilds::UpdateGroupWorker]]
    style w_pending_build_update_group fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rx:15
    w_update_build_names_worker[[UpdateBuildNamesWorker]]
    style w_update_build_names_worker fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rx:15
    w_partitioning_worker[[PartitioningWorker]]
    style w_partitioning_worker fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rx:15
    w_refs_unlock_prev_pipelines[[Refs::UnlockPreviousPipelinesWorker]]
    style w_refs_unlock_prev_pipelines fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rx:15
    w_resource_group_assign_resource[[ResourceGroups::AssignResourceFromResourceGroupWorker]]
    style w_resource_group_assign_resource fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rx:15
    w_resource_group_assign_resource_v2[[ResourceGroups::AssignResourceFromResourceGroupWorkerV2]]
    style w_resource_group_assign_resource_v2 fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rx:15
    w_pipeline_finished_worker[[PipelineFinishedWorker]]
    style w_pipeline_finished_worker fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rx:15
    w_cancel_redundant_pipelines[[CancelRedundantPipelinesWorker]]
    style w_cancel_redundant_pipelines fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rx:15
    w_low_urgency_cancel_redundant[[LowUrgencyCancelRedundantPipelinesWorker]]
    style w_low_urgency_cancel_redundant fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rx:15
    w_schedule_unlock_pipelines_cron[[ScheduleUnlockPipelinesInQueueCronWorker]]
    style w_schedule_unlock_pipelines_cron fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rx:15
    w_daily_build_group_report[[DailyBuildGroupReportResultsWorker]]
    style w_daily_build_group_report fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rx:15
    w_parse_secure_file_metadata[[ParseSecureFileMetadataWorker]]
    style w_parse_secure_file_metadata fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rx:15
    w_pipeline_finished_worker-->|updates|ci_pipeline
    w_pipeline_bridge_status_worker-->|inherits status|ci_bridge
    w_initial_pipeline_process_worker-->|runs after|b_create_pipeline_service
    w_update_build_names_worker-->|affects|ci_build
    w_partitioning_worker-->|allocates|ci_pipeline
    w_drop_pipeline_worker-->|removes|ci_pipeline

    %% EE Enterprise Edition Specific Background Workers
    w_upstream_projects_subscriptions_cleanup_worker[[UpstreamProjectsSubscriptionsCleanupWorker]]
    style w_upstream_projects_subscriptions_cleanup_worker fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rx:15
    w_trigger_downstream_subscriptions_worker[[TriggerDownstreamSubscriptionsWorker]]
    style w_trigger_downstream_subscriptions_worker fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rx:15

    w_upstream_projects_subscriptions_cleanup_worker-->|cleans EE cross-project connections|ci_pipeline
    w_trigger_downstream_subscriptions_worker-->|triggers downstream in EE|ci_pipeline
  end

  %% PIPELINE SERVICES: CONTROL, INTEGRATIONS, STATE CHANGES
  subgraph "Pipeline Service Layer"["Pipeline Service Layer"]
    style "Pipeline Service Layer" fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rx:20
    s_cancel_pipeline_service[[CancelPipelineService]]
    style s_cancel_pipeline_service fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15
    s_drop_pipeline_service[[DropPipelineService]]
    style s_drop_pipeline_service fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15
    s_retry_pipeline_service[[RetryPipelineService]]
    style s_retry_pipeline_service fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15
    s_destroy_pipeline_service[[DestroyPipelineService]]
    style s_destroy_pipeline_service fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15
    s_enqueue_job_service[[EnqueueJobService]]
    style s_enqueue_job_service fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15
    s_process_build_service[[ProcessBuildService]]
    style s_process_build_service fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15
    s_prepare_build_service[[PrepareBuildService]]
    style s_prepare_build_service fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15
    s_build_unschedule_service[[BuildUnscheduleService]]
    style s_build_unschedule_service fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15
    s_expire_pipeline_cache_service[[ExpirePipelineCacheService]]
    style s_expire_pipeline_cache_service fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15
    s_run_scheduled_build_service[[RunScheduledBuildService]]
    style s_run_scheduled_build_service fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15
    s_pipeline_trigger_service[[PipelineTriggerService]]
    style s_pipeline_trigger_service fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15
    s_play_bridge_service[[PlayBridgeService]]
    style s_play_bridge_service fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15
    s_play_manual_stage_service[[PlayManualStageService]]
    style s_play_manual_stage_service fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15
    s_queue_build_queue_service[[Queue::BuildQueueService]]
    style s_queue_build_queue_service fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rx:15
    s_pipeline_bridge_status_service[[PipelineBridgeStatusService]]
    style s_pipeline_bridge_status_service fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15
    s_copy_cross_db_associations[[CopyCrossDatabaseAssociationsService]]
    style s_copy_cross_db_associations fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rx:15

    s_cancel_pipeline_service-->|cancels|ci_pipeline
    s_drop_pipeline_service-->|deletes|ci_pipeline
    s_destroy_pipeline_service-->|removes|ci_pipeline
    s_retry_pipeline_service-->|restarts|ci_pipeline
    s_enqueue_job_service-->|puts jobs into queue|ci_build
    s_process_build_service-->|executes action|ci_build
    s_prepare_build_service-->|sets up|ci_build
    s_expire_pipeline_cache_service-->|refreshes|ci_pipeline
    s_run_scheduled_build_service-->|triggers scheduled|ci_build
    s_pipeline_trigger_service-->|creates pipeline from external trigger|ci_pipeline
    s_play_bridge_service-->|initiates|ci_bridge
    s_play_manual_stage_service-->|automates manual|ci_stage
    s_queue_build_queue_service-->|finds builds for runner|ci_build
    s_pipeline_bridge_status_service-->|sync status for bridge|ci_bridge

    %% EE SERVICES
    s_ee_pipeline_bridge_status_service[[EE::PipelineBridgeStatusService]]
    style s_ee_pipeline_bridge_status_service fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15
    s_ee_destroy_pipeline_service[[EE::DestroyPipelineService]]
    style s_ee_destroy_pipeline_service fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15
    s_ee_retry_pipeline_service[[EE::RetryPipelineService]]
    style s_ee_retry_pipeline_service fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15
    s_ee_create_downstream_pipeline_service[[EE::CreateDownstreamPipelineService]]
    style s_ee_create_downstream_pipeline_service fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15
    s_ee_create_pipeline_service[[EE::CreatePipelineService]]
    style s_ee_create_pipeline_service fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15
    s_ee_pipeline_creation_start_service[[EE::PipelineCreation::StartPipelineService]]
    style s_ee_pipeline_creation_start_service fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15
    s_ee_pipeline_processing_atomic_service[[EE::PipelineProcessing::AtomicProcessingService]]
    style s_ee_pipeline_processing_atomic_service fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:15

    s_ee_pipeline_bridge_status_service-->|extends|s_pipeline_bridge_status_service
    s_ee_destroy_pipeline_service-->|extends|s_destroy_pipeline_service
    s_ee_retry_pipeline_service-->|extends|s_retry_pipeline_service
    s_ee_create_downstream_pipeline_service-->|extends|b_create_downstream_pipeline_service
    s_ee_create_pipeline_service-->|extends|b_create_pipeline_service
    s_ee_pipeline_creation_start_service-->|extends|b_pipeline_creation_start_service
    s_ee_pipeline_processing_atomic_service-->|extends|b_pipeline_processing_atomic_service
  end

  %% MAILERS & EMAIL NOTIFICATIONS
  subgraph "Pipeline Notification Mailers"["Pipeline Notification Mailers"]
    style "Pipeline Notification Mailers" fill:#F8F8F8,stroke:#F8F8F8,stroke-width:2,rx:20
    m_emails_pipelines[[Emails::Pipelines]]
    style m_emails_pipelines fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rx:15
    m_emails_auto_devops[[Emails::AutoDevops]]
    style m_emails_auto_devops fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rx:15
    m_emails_pipelines-->|sends pipeline status|ci_pipeline
    m_emails_auto_devops-->|notifies auto devops status|ci_pipeline
  end

  %% PRESENTATION LAYER
  subgraph "Pipeline Presentation Layer"["Pipeline Presentation Layer"]
    style "Pipeline Presentation Layer" fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,rx:20
    pr_pipeline_presenter[[PipelinePresenter]]
    style pr_pipeline_presenter fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rx:15
    pr_pipeline_serialization[[PipelineSerializer]]
    style pr_pipeline_serialization fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rx:15
    pr_pipeline_presenter-->|wraps|ci_pipeline
    pr_pipeline_serialization-->|serializes|ci_pipeline
  end

  %% FINDERS
  subgraph "Pipeline Finders"["Pipeline Finders"]
    style "Pipeline Finders" fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,rx:20
    f_pipelines_finder[[PipelinesFinder]]
    style f_pipelines_finder fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rx:15
    f_build_source_finder[[BuildSourceFinder]]
    style f_build_source_finder fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rx:15
    f_build_name_finder[[BuildNameFinder]]
    style f_build_name_finder fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rx:15
    f_pipelines_for_mr_finder[[PipelinesForMergeRequestFinder]]
    style f_pipelines_for_mr_finder fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rx:15
    f_pipelines_finder-->|queries|ci_pipeline
    f_build_source_finder-->|queries|ci_build
    f_build_name_finder-->|queries|ci_build
    f_pipelines_for_mr_finder-->|queries|ci_pipeline
  end

  %% CONTROLLERS & GRAPHQL MUTATIONS
  subgraph "Pipeline API & User Entry Points"["Pipeline API & User Entry Points"]
    style "Pipeline API & User Entry Points" fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2,rx:20
    c_pipelines_controller[[Projects::PipelinesController]]
    style c_pipelines_controller fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rx:15
    gql_create_pipeline[[Mutations::Ci::Pipeline::Create]]
    style gql_create_pipeline fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rx:15
    gql_cancel_pipeline[[Mutations::Ci::Pipeline::Cancel]]
    style gql_cancel_pipeline fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rx:15
    c_pipelines_controller-->|triggers creation|b_create_pipeline_service
    gql_create_pipeline-->|invokes|b_create_pipeline_service
    gql_cancel_pipeline-->|invokes|s_cancel_pipeline_service
  end

  %% LOGICAL RELATIONSHIPS BETWEEN GROUPS
  b_create_pipeline_service-->|builds|ci_pipeline
  b_create_pipeline_service-->|uses config|ci_pipeline_config
  b_create_pipeline_service-->|invokes chain|b_pipeline_processing_atomic_service
  b_pipeline_processing_service-->|triggers background|w_initial_pipeline_process_worker
  w_initial_pipeline_process_worker-->|calls pipeline process service|b_pipeline_processing_service

  %% Domain-specific data flow
  ci_pipeline-->|has downstream|ci_bridge
  ci_bridge-->|triggers downstream|b_trigger_downstream_pipeline_service
  b_trigger_downstream_pipeline_service-->|creates downstream|ci_pipeline
  ci_pipeline-->|parent/child link|ci_sources_pipeline

  %% WORKER TO SERVICE INTERACTIONS (EXECUTION)
  w_pipeline_bridge_status_worker-->|invokes|s_pipeline_bridge_status_service
  w_drop_pipeline_worker-->|calls|s_drop_pipeline_service
  w_refs_unlock_prev_pipelines-->|unlock pipelines|ci_pipeline

  %% PIPELINE STATE + SCHEDULING
  run_pipeline_schedule_worker[[RunPipelineScheduleWorker]]
  style run_pipeline_schedule_worker fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rx:15
  pipeline_schedule_worker[[PipelineScheduleWorker]]
  style pipeline_schedule_worker fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rx:15

  pipeline_schedule_worker-->|queues run|run_pipeline_schedule_worker
  run_pipeline_schedule_worker-->|triggers creation|b_create_pipeline_service

  %% SUPPLEMENTARY UTILITIES
  tooling_api_pipeline[[Tooling::API::Pipeline]]
  style tooling_api_pipeline fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rx:15
  tooling_api_pipeline-->|fetches status & jobs|ci_pipeline

  %% HIERARCHY LINKAGE (FAMILY, UPSTREAM/DOWNSTREAM)
  ci_pipeline-->|tracks family via same_family_ids|ci_sources_pipeline
  ci_pipeline-->|references|ci_commit_with_pipeline

  %% BOUNDARY: EXTERNAL NOTIFICATION
  pipeline_notification_worker[[PipelineNotificationWorker]]
  style pipeline_notification_worker fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rx:15
  pipeline_hooks_worker[[PipelineHooksWorker]]
  style pipeline_hooks_worker fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rx:15
  
  ci_pipeline-->|sends status update|pipeline_notification_worker
  ci_pipeline-->|sends execution hooks|pipeline_hooks_worker

  %% RESOURCE GROUPS
  ci_resource-->|assignment|w_resource_group_assign_resource
  ci_resource-->|assignment v2|w_resource_group_assign_resource_v2

  %% EE Extensions (Overriding/Enhancing Core Logic)
  s_ee_pipeline_bridge_status_service-->|overrides pipeline bridge status|s_pipeline_bridge_status_service
  s_ee_destroy_pipeline_service-->|augments destroy pipeline|s_destroy_pipeline_service
  s_ee_create_pipeline_service-->|extends create pipeline|b_create_pipeline_service
  s_ee_create_downstream_pipeline_service-->|extends downstream creation|b_create_downstream_pipeline_service
  s_ee_pipeline_processing_atomic_service-->|extends processing atomic|b_pipeline_processing_atomic_service

  %% PIPELINE MESSAGES AND STATUS FEEDBACK
  ci_pipeline_message-->|communicates status/errors|ci_pipeline

  %% PRESENTERS/SERIALIZERS to CONTROLLER/API
  pr_pipeline_presenter-->|used by|c_pipelines_controller
  pr_pipeline_serialization-->|used by|c_pipelines_controller

  %% FINAL: INTERACTIONS WITHIN CORE
  ci_pipeline-->|sources, status, members|ci_stage
  ci_stage-->|status for jobs|ci_build
  ci_build-->|tracked by status|commit_status
  commit_status-->|specializes|generic_commit_status
  ci_pipeline-->|results reported|ci_build_report_result

  %% CONNECTION OF FINDERS/SERVICES TO API/CONTROLLER
  f_pipelines_finder-->|used by|c_pipelines_controller
  f_pipelines_finder-->|used by|gql_create_pipeline
  f_pipelines_for_mr_finder-->|used by|c_pipelines_controller
```