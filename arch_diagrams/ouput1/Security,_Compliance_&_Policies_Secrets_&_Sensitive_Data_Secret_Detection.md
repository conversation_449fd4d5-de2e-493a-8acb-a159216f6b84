```mermaid
flowchart TB
  %% Styling for node colors and shapes
  classDef core fill:#D4F1F9,stroke:#7ed6df,stroke-width:2px,color:#222,stroke-dasharray:0,rx:8,ry:8
  classDef ds fill:#E0F8E0,stroke:#b6e0b6,stroke-width:2px,color:#222,stroke-dasharray:0,rx:8,ry:8
  classDef util fill:#FFF8DC,stroke:#faeb9a,stroke-width:2px,color:#222,stroke-dasharray:0,rx:8,ry:8
  classDef error fill:#FFE4E1,stroke:#ffb3ab,stroke-width:2px,color:#222,stroke-dasharray:0,rx:8,ry:8
  classDef init fill:#E6E6FA,stroke:#b2b2d9,stroke-width:2px,color:#222,stroke-dasharray:0,rx:8,ry:8
  classDef groupbox fill:#F8F8F8,stroke:#D4F1F9,stroke-width:3px,rx:12,ry:12,color:#222

  %% --- CORE CONCEPT: Secret Detection ---
  subgraph S1["Secret Detection" ]
    direction TB
    S1F1["SecretsCheck\nNot only runs bulk secret detection, but initializes orchestration, applies rulesets, emits logs"]:::core
    S1F2["SecretPushProtection\nEligibilityChecker\nDetermines if code push needs secret scan, enforces project config/auditing"]:::core
    S1F3["SecretPushProtection\nResponseHandler\nInterprets secret scan responses,\nformats findings,\nprepares error messages"]:::core
    S1F4["secret_push_protection Logger\nSpecialized JSON logger for secret push events"]:::util
    S1F6["secret_detection_logger.rb JsonLogger"]:::util
    S1F5["SecretDetectionBuildAction\nInjects secret detection configuration\nto CI pipelines"]:::core
    S1F7["secret_detection.token_lookup Service\nFinds tokens/credentials by type in DB"]:::core
    S1F11["scan_security_report_secrets_worker.rb\nScans security reports for detected secrets"]:::core
    S1F12["update_token_status_worker.rb\nUpdates token status after detection/scan"]:::core
    S1F8["TokenRevocationService\nRevokes compromised tokens found by detection"]:::core
    S1F9["cloudflare_exposed_credential_checker.rb\nChecks Cloudflare-flagged credentials in requests"]:::util
    S1F10["encryption_keys.rb\nGathers and manages encryption key metadata"]:::ds
    S1F13["WeakPasswords\ndomain-specific password policies/forbidden substrings"]:::ds
    S1F14["password/complexity_validator.rb\nCustom validator enforcing password rules"]:::core
    S1F15["dast/site_profile_secret_variable.rb\nstructure for storing secrets for DAST site profiles"]:::ds
  end
  class S1 groupbox

  %% --- CORE CONCEPT: Secret Push Protection Policy Configuration and Enforcement ---
  subgraph S2["Secret Push Protection Policy Configuration"]
    direction TB
    S2F1["set_secret_push_protection Service\nTop-level entry to policy set/enable/disable, delegates to project/group"]:::core
    S2F2["set_project_secret_push_protection Service\nProject-level secret push protection logic,\naudit context"]:::core
    S2F3["set_group_secret_push_protection Service\nGroup-level policy logic, handles projects list"]:::core
    S2F4["set_group_secret_push_protection_worker.rb\nPerforms async background policy enablement"]:::core
    S2F5["set_pre_receive_secret_detection GraphQL mutation\nEnables secret protection for project, entry for policy change"]:::core
    S2F7["dast_site_validation_resolver.rb\nGraphQL resolver, used for DAST site validation,\nconnects policy and site profile logic"]:::core
    S2F8["RemoteDevelopment\nimage_pull_secrets_validator.rb\nValidates image pull secrets for valid names and duplicates"]:::util
  end
  class S2 groupbox

  %% --- DATA STRUCTURES & DOMAIN OBJECTS ---
  subgraph S3["Data Structures & Domain Models"]
    direction TB
    S3F1["dast/site_profile_secret_variable.rb\nSecret variable with masking, decoding, key mapping,\nused for API/DAST site scanning"]:::ds
    S3F2["encryption_keys.rb\nRecords and exposes all system encryption keys,\nand key types"]:::ds
    S3F3["TransactionCallbackPayload Telesign\nData structure for telephony callback with secret info"]:::ds
  end
  class S3 groupbox

  %% --- INTEGRATIONS: Arkose (Bot protection), DAST, Fuzzing, Training Providers ---
  subgraph S4["Security Integrations Arkose, DAST, Fuzzing, Training"]
    direction TB

    subgraph S4AArkose["Arkose Bot & Token Verification"]
      direction TB
      S4A1["token_verifiable concern Controller\nIncludes strong memoize; verifies Arkose Labs token,\nlogs challenges, results, or skipped"]:::core
      S4A2["Arkose::TruthDataAuthorizationService\nService: retrieves/caches access tokens from Arkose API"]:::core
      S4A3["Arkose::TruthDataService\nSends Arkose truth data, logs result"]:::core
      S4A4["arkose_helper.rb\nPrepares Arkose data exchange payloads for use-case"]:::util
    end
    class S4AArkose groupbox

    subgraph S4BDast["DAST Site & API Fuzzing"]
      direction TB
      S4B1["fuzzing/api/scan_profile.rb\nDescribes pre-baked profiles for API fuzzing"]:::ds
      S4B2["fuzzing/api/ci_configuration.rb\nHandles fetching/updating scan profiles, CI config"]:::core
      S4B3["dast_site_validation_resolver.rb GraphQL\nValidates target for DAST scan, controls scan profiles"]:::core
    end
    class S4BDast groupbox

    subgraph S4CTrain["Security Training Providers"]
      direction TB
      S4C1["training_providers/base_url_service.rb\nBase service for training URL generation/reactive cache"]:::core
      S4C2["training_providers/kontra_url_service.rb\nKontra training integration, custom params"]:::core
      S4C3["training_providers/secure_flag_url_service.rb\nSecureFlag training, HTTP requests"]:::core
      S4C4["training_providers/secure_code_warrior_url_service.rb\nIntegrates with SecureCodeWarrior, OWASP/CWE mapping"]:::core
    end
    class S4CTrain groupbox
  end
  class S4 groupbox

  %% --- SUPPORTING: Tests, Validators and Misc Utilities ---
  subgraph S5["Supporting: Validators, Utilities, Tests"]
    direction TB
    S5F1["spec/lib/arkose/verify_response_spec.rb\nTest coverage for Arkose response"]:::util
    S5F2["spec/lib/arkose/data_exchange_payload_spec.rb\nTest Arkose data payload"]:::util
    S5F3["spec/support/api/graphql/mutations/audit_events/legacy_destination_helper_shared_examples.rb\nHelpers for shared secret token retrieval in tests"]:::util
    S5F4["captcha/captcha_verification_service.rb\nCaptcha-based service integrating secrets as part of challenge"]:::util
  end
  class S5 groupbox

  %% --- INIT: Initialization/Workers/Process flows ---
  subgraph S6["Initialization, Scheduling and Workers"]
    direction TB
    S6F1["scan_security_report_secrets_worker.rb\nScans security findings, revokes if required, triggers other workers"]:::init
    S6F2["security/secret_detection/update_token_status_worker.rb\nProcesses finding batches, status update"]:::init
    S6F3["vulnerabilities/archival/schedule_worker.rb\nSchedules vulnerabilities archival jobs, maintains cursor state in Redis"]:::init
  end
  class S6 groupbox

  %% --- ERRORS / HANDLING ---
  subgraph S7["Error Handling Secret Detection"]
    direction TB
    S7F1["secret_push_protection/response_handler.rb\nFormats/funnels all errors from secret detection scanning to the clients"]:::error
    S7F2["secrets_check.rb\nHandles, logs and raises errors for secret scanning failures"]:::error
  end
  class S7 groupbox

  %% ---- LOGICAL RELATIONSHIPS BETWEEN FILES: Secret Detection ---

  %% SecretScan entry/eligibility and response handling
  S1F1 --> S1F2
  S1F1 --> S1F3
  S1F1 --"Uses/Logs via"--> S1F6
  S1F1 --"Enforces via ruleset"--> S1F13
  S1F1 --"Validates via"--> S1F14
  S1F1 --"Interprets errors from"--> S7F1
  S1F2 --"Delegates response to"--> S1F3
  S1F3 --"Uses error format from"--> S7F1
  S1F3 --"Returns results to"--> S2F1
  S1F6 --"Extends"--> S1F4
  S1F7 --"Finds tokens for"--> S1F8
  S1F11 --"Schedules and processes via"--> S6F1
  S1F12 --"Processes scan results for"--> S1F8
  S1F12 --"Runs as batch by"--> S6F2
  S1F8 --"Revokes tokens after detection"--> S1F12
  S1F8 --"Triggered by findings from"--> S1F11
  S1F5 --"Installs config for"--> S2F1
  S1F15 --"Used in"--> S3F1

  %% --- Secret Push Protection Policy Config Relationships ---
  S2F1 --"Calls"--> S2F2
  S2F1 --"Calls"--> S2F3
  S2F1 --"Called by GraphQL mutation"--> S2F5
  S2F1 --"Calls"--> S2F4
  
  S2F2 --"Handles audits for"--> S2F5
  S2F3 --"Handles group policy for"--> S2F5
  S2F4 --"Performs async on"--> S2F3
  S2F5 --"Triggers policy via"--> S2F1
  S2F5 --"Relates to"--> S2F7
  S2F7 --"Validates DAST site with policy"--> S4BDast

  %% --- Integration: Arkose flows ---
  S4A1 --"Verifies tokens with"--> S4A2
  S4A1 --"Logs challenge via"--> S4A4
  S4A2 --"Authorizes token for"--> S4A3
  S4A3 --"Reports verification to"--> S4A4

  S5F1 --"Tests for"--> S4A1
  S5F2 --"Tests payload for"--> S4A4

  %% --- DAST/Fuzzing integration relationships ---
  S4BDast --> S1F15
  S4B2 --"Loads scan profiles from"--> S4B1
  S4B3 --"Manipulates/validates site profile using"--> S4BDast
  S4B2 --"Fetched/updates scan profiles into"--> S4B3

  %% --- Training provider extension hierarchy ---
  S4C2 --"Extends"--> S4C1
  S4C3 --"Extends"--> S4C1
  S4C4 --"Extends"--> S4C1

  %% --- Data structure sharing / utility style linking ---
  S1F10 --"Provides encryption key data for"--> S3F2
  S1F15 --"Used by"--> S3F1
  S1F13 --"Enforced by"--> S1F1
  S1F14 --"Validates with"--> S1F13

  %% --- Workers and Scheduling flows ---
  S1F11 --"Schedules update with"--> S6F2
  S6F2 --"Schedules/updates findings"--> S1F12
  S6F1 --"Scans findings for"--> S1F8
  S6F3 --"Coordinates with"--> S6F1

  %% --- Misc utility/support dependency edges ---
  S3F3 --"Used for"--> S1F15
  S4BDast --"Uses site secrets via"--> S1F15
  S5F3 --"Used for retrieving secret tokens in test cases for"--> S2F5
  S5F4 --"Challenges/validates secrets with Captcha in"--> S1F1

  %% --- Password/complexity enforcement relationships ---
  S1F13 --"FORBIDDEN_WORDS, etc. for"--> S1F14
  S1F14 --"Invoked by"--> S1F1
  
  %% --- Secret detection error handling ---
  S1F1 --"Delegates error formatting to"--> S7F1
  S1F2 --"Dispatches error states to"--> S7F2

  %% --- Core/DS/Util connections for major control flows ---
  S2F1 --"Enables/disables policy for"--> S1F1
  S2F2 --"Sends audit log for"--> S1F4
  S3F1 --"References/Masks secrets for scans in"--> S1F1
  S1F5 --"Adds detection to CI via"--> S2F1
```