```mermaid
flowchart TB
  %% SUBGRAPH: Repository Size & Limits
  subgraph "Repository Size & Limits"["Repository Size & Limits"]
    direction TB
    style "Repository Size & Limits" fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,corner-radius:6px

    subgraph "Limit Checking & Enforcement"
      direction TB
      style "Limit Checking & Enforcement" fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,corner-radius:6px
      RSChecker["lib/gitlab/repository_size_checker.rb":::coreFile]:::rounded
      RSCheckerMsg["lib/gitlab/repository_size_error_message.rb":::errorFile]:::rounded
      RExcessMsg["ee/lib/gitlab/root_excess_size_error_message.rb":::errorFile]:::rounded

      RSChecker-->|"Delegates error messages" | RSCheckerMsg
      RSChecker-->|"Delegates root excess warnings" | RExcessMsg
      RSCheckerMsg-->|"Error detailing" | RExcessMsg
    end

    subgraph "Data Structures"
      direction TB
      style "Data Structures" fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2,corner-radius:6px
      RefCounter["lib/gitlab/reference_counter.rb":::dataStructureFile]:::rounded
      JSONSizeEst["lib/gitlab/utils/json_size_estimator.rb":::dataStructureFile]:::rounded
    end

    subgraph "Size Related Errors"
      direction TB
      style "Size Related Errors" fill:#F8F8F8,stroke:#FFE4E1,stroke-width:2,corner-radius:6px
      DocDelErr["ee/lib/elastic/latest/document_should_be_deleted_from_index_error.rb":::errorFile]:::rounded
    end

    subgraph "Plan & Instance Limits"
      direction TB
      style "Plan & Instance Limits" fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2,corner-radius:6px
      InstConfig["app/models/instance_configuration.rb":::coreFile]:::rounded
      PlanLimitsCtrl["app/controllers/admin/plan_limits_controller.rb":::coreFile]:::rounded

      PlanLimitsCtrl-->|"Fetches/sets limits" | InstConfig
      InstConfig-->|"Provides limits to checkers" | RSChecker
    end
  end

  %% SUBGRAPH: Repository Storage Movement & Management
  subgraph "Repository Storage Move/Management"
    direction TB
    style "Repository Storage Move/Management" fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,corner-radius:6px

    subgraph "Resource Abstractions"
      direction TB
      style "Resource Abstractions" fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,corner-radius:6px
      ProjRepoMove["app/models/projects/repository_storage_move.rb":::coreFile]:::rounded
      SnipRepoMove["app/models/snippets/repository_storage_move.rb":::coreFile]:::rounded
      RepoMovable["app/models/concerns/repositorystoragemovable.rb":::coreFile]:::rounded

      ProjRepoMove-->|"Includes" | RepoMovable
      SnipRepoMove-->|"Includes" | RepoMovable
    end

    subgraph "Storage Update & Coordination"
      direction TB
      style "Storage Update & Coordination" fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,corner-radius:6px
      UpdateStorWrk["app/workers/projects/update_repository_storage_worker.rb":::supportFile]:::rounded
      GarbageCollWrk["app/workers/projects/git_garbage_collect_worker.rb":::supportFile]:::rounded
      ArchiveCacheWrk["app/workers/repository_archive_cache_worker.rb":::supportFile]:::rounded

      ProjRepoMove-->|"Schedules" | UpdateStorWrk
      UpdateStorWrk-->|"Triggers" | GarbageCollWrk
      UpdateStorWrk-->|"Updates cache" | ArchiveCacheWrk
    end

    subgraph "Abstractions & Patterns"
      direction TB
      style "Abstractions & Patterns" fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,corner-radius:6px
      StaticModel["lib/static_model.rb":::supportFile]:::rounded
      SafeUrl["app/models/concerns/safe_url.rb":::supportFile]:::rounded
      Routable["app/models/concerns/routable.rb":::supportFile]:::rounded
      HasEnvScope["app/models/concerns/has_environment_scope.rb":::supportFile]:::rounded

      ProjRepoMove-->|"Uses" | StaticModel
      SnipRepoMove-->|"Uses" | StaticModel
      ProjRepoMove-->|"Uses route" | Routable
      SnipRepoMove-->|"Possible route context" | Routable
    end
  end

  %% SUBGRAPH: Search / Zoekt Coordination
  subgraph "Search Index, Routing, Replication"
    direction TB
    style "Search Index, Routing, Replication" fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,corner-radius:6px

    subgraph "Zoekt Indexing Domain"
      direction TB
      style "Zoekt Indexing Domain" fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,corner-radius:6px
      ZoektIndex["ee/app/models/search/zoekt/index.rb":::coreFile]:::rounded
      ZoektTask["ee/app/models/search/zoekt/task.rb":::coreFile]:::rounded
      ZoektRouter["ee/app/models/search/zoekt/router.rb":::coreFile]:::rounded
      ZoektRep["ee/app/models/search/zoekt/replica.rb":::coreFile]:::rounded
      ZoektEnabledNs["ee/app/models/search/zoekt/enabled_namespace.rb":::coreFile]:::rounded
      ZoektMod["ee/app/models/search/zoekt.rb":::coreFile]:::rounded

      ZoektRouter-->|"Coordinates" | ZoektIndex
      ZoektRouter-->|"Uses" | ZoektRep
      ZoektRep-->|"Belongs to" | ZoektEnabledNs
      ZoektEnabledNs-->|"Has many" | ZoektIndex
      ZoektMod-->|"Queries, triggers" | ZoektRouter
      ZoektMod-->|"Triggers" | ZoektTask
      ZoektTask-->|"Works on" | ZoektIndex
    end
  end

  %% SUBGRAPH: Data Operations, Cleanup, Analysis
  subgraph "Repository Data Operations"
    direction TB
    style "Repository Data Operations" fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,corner-radius:6px

    subgraph "Gitaly Client Services"
      direction TB
      style "Gitaly Client Services" fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,corner-radius:6px
      GitalyAttribs["lib/gitlab/gitaly_client/attributes_bag.rb":::supportFile]:::rounded
      GitalyAnalysis["lib/gitlab/gitaly_client/analysis_service.rb":::supportFile]:::rounded
      GitalyCleanup["lib/gitlab/gitaly_client/cleanup_service.rb":::supportFile]:::rounded

      GitalyAnalysis-->|"Reads repo data" | GitalyAttribs
      GitalyCleanup-->|"Cleans & rewrites based on" | GitalyAttribs
    end

    subgraph "Quotas & Data Transfer Finders"
      direction TB
      style "Quotas & Data Transfer Finders" fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,corner-radius:6px
      ProjDtTransFind["app/finders/data_transfer/project_data_transfer_finder.rb":::supportFile]:::rounded
      EnvFinder["app/finders/environments/environments_finder.rb":::supportFile]:::rounded

      ProjDtTransFind-->|"Reads" | RSChecker
      ProjDtTransFind-->|"Checks limits" | InstConfig
      EnvFinder-->|"Uses scope/context" | HasEnvScope
    end

    CleanupRemUploads["lib/gitlab/cleanup/remote_uploads.rb":::supportFile]:::rounded
  end

  %% SUBGRAPH: Collaboration & Coordination Services
  subgraph "Collaboration & Coordination Services"
    direction TB
    style "Collaboration & Coordination Services" fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2,corner-radius:6px

    ContainersDeleteTags["app/services/projects/container_repository/delete_tags_service.rb":::supportFile]:::rounded
    BuildArtifactsCtrl["app/controllers/projects/build_artifacts_controller.rb":::coreFile]:::rounded
    MetricImgUploader["app/uploaders/metric_image_uploader.rb":::supportFile]:::rounded
    APIStatsHelper["lib/api/helpers/project_stats_refresh_conflicts_helpers.rb":::supportFile]:::rounded

    BuildArtifactsCtrl-->|"Validates, extracts, triggers" | MetricImgUploader
    BuildArtifactsCtrl-->|"Checks project stats refresh" | APIStatsHelper
    ContainersDeleteTags-->|"Updates artifacts/repos" | ProjRepoMove
    ContainersDeleteTags-->|"Coordinates with" | RSChecker
    ContainersDeleteTags-->|"Handles errors" | RSCheckerMsg
  end

  %% SUBGRAPH: Initialization, Setup, EE Integration
  subgraph "Initialization & EE Integration"
    direction TB
    style "Initialization & EE Integration" fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2,corner-radius:6px

    EEProjCacheWrk["ee/app/workers/ee/project_cache_worker.rb":::supportFile]:::rounded
    EERepoCheckWrk["ee/app/workers/ee/repository_check/single_repository_worker.rb":::supportFile]:::rounded
    EEClustersCtrl["ee/app/controllers/ee/clusters/clusters_controller.rb":::coreFile]:::rounded
    SystemAccess["app/models/system_access.rb":::coreFile]:::rounded

    EEProjCacheWrk-->|"Caches project stats" | RSChecker
    EERepoCheckWrk-->|"Verifies consistency" | ProjRepoMove
    EEClustersCtrl-->|"Coordinates environments" | EnvFinder
    EEClustersCtrl-->|"Requires system access context" | SystemAccess
  end

  %% SUBGRAPH: Testing, Spec & QA Helpers
  subgraph "Testing & QA"
    direction TB
    style "Testing & QA" fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,corner-radius:6px

    RepArchiveRTLimitSpec["spec/lib/gitlab/repository_archive_rate_limiter_spec.rb":::supportFile]:::rounded
    RepCacheAdapterSpec["spec/lib/gitlab/repository_cache_adapter_spec.rb":::supportFile]:::rounded
    QAUserStore["qa/qa/runtime/user/store.rb":::supportFile]:::rounded
  end

  %% CORE LOGICAL RELATIONSHIPS ACROSS DOMAINS

  %% Connecting core repository size enforcement implied flows
  RSChecker-->|"Used by" | ProjRepoMove
  RSChecker-->|"Used by" | SnipRepoMove
  RSChecker-->|"Called from" | ContainersDeleteTags
  RSChecker-->|"Referenced in" | EEProjCacheWrk
  RExcessMsg-->|"Passed in error context" | BuildArtifactsCtrl

  %% Data structure supporting error messages
  RefCounter-->|"Basis for counting references in size limit checks" | RSChecker
  JSONSizeEst-->|"Used for estimating object size in limits" | RSChecker

  %% Interaction of storage movement and indexing
  ProjRepoMove-->|"Triggers reindexing" | ZoektMod
  ZoektIndex-->|"Indexes repository states" | ProjRepoMove
  ZoektMod-->|"Fetches from" | ZoektRouter

  %% Container repository and data transfer finder relationships
  ContainersDeleteTags-->|"Cleans up data post-tag delete" | CleanupRemUploads
  ProjDtTransFind-->|"Consults limits" | RSChecker

  %% Error handling flows
  DocDelErr-->|"Handles search related deletion errors" | ZoektMod

  %% Init and setup
  SystemAccess-->|"Ensures correct context for migration and execution" | EEProjCacheWrk

  %% CI/CD & uploaders
  MetricImgUploader-->|"Supports artifact management" | BuildArtifactsCtrl

  %% Route abstraction
  Routable-->|"Provides route lookup for containers" | ProjRepoMove
  Routable-->|"Provides route lookup for containers" | SnipRepoMove

  %% Supporting files (shape/color classes)
  classDef coreFile fill:#D4F1F9,stroke:#8fcfe7,color:#222,stroke-width:2,stroke-dasharray:2 2
  classDef supportFile fill:#FFF8DC,stroke:#FCDC85,color:#222,stroke-width:2,stroke-dasharray:2 2
  classDef dataStructureFile fill:#E0F8E0,stroke:#83e9a1,color:#222,stroke-width:2,stroke-dasharray:2 2
  classDef errorFile fill:#FFE4E1,stroke:#E19CA5,color:#222,stroke-width:2,stroke-dasharray:2 2
  classDef initFile fill:#E6E6FA,stroke:#B99EFF,color:#222,stroke-width:2,stroke-dasharray:2 2

  class RSChecker,ProjRepoMove,SnipRepoMove,ZoektIndex,ZoektTask,ZoektRouter,ZoektRep,ZoektEnabledNs,ZoektMod,InstConfig,PlanLimitsCtrl,BuildArtifactsCtrl,EEClustersCtrl,SystemAccess coreFile;
  class RSCheckerMsg,RExcessMsg,DocDelErr errorFile;
  class RefCounter,JSONSizeEst dataStructureFile;
  class StaticModel,SafeUrl,Routable,HasEnvScope,UpdateStorWrk,GarbageCollWrk,ArchiveCacheWrk,ProjDtTransFind,EnvFinder,GitalyAttribs,GitalyAnalysis,GitalyCleanup,ContainersDeleteTags,MetricImgUploader,APIStatsHelper,RepArchiveRTLimitSpec,RepCacheAdapterSpec,QAUserStore,CleanupRemUploads supportFile;
  class EEProjCacheWrk,EERepoCheckWrk,EEClustersCtrl initFile;

  %% Node style
  class RSChecker,ProjRepoMove,SnipRepoMove,ZoektIndex,ZoektTask,ZoektRouter,ZoektRep,ZoektEnabledNs,ZoektMod,InstConfig,PlanLimitsCtrl,BuildArtifactsCtrl,EEClustersCtrl,SystemAccess rounded-rectangle;
  class RSCheckerMsg,RExcessMsg,DocDelErr rounded-rectangle;
  class RefCounter,JSONSizeEst rounded-rectangle;
  class StaticModel,SafeUrl,Routable,HasEnvScope,UpdateStorWrk,GarbageCollWrk,ArchiveCacheWrk,ProjDtTransFind,EnvFinder,GitalyAttribs,GitalyAnalysis,GitalyCleanup,ContainersDeleteTags,MetricImgUploader,APIStatsHelper,RepArchiveRTLimitSpec,RepCacheAdapterSpec,QAUserStore,CleanupRemUploads rounded-rectangle;
  class EEProjCacheWrk,EERepoCheckWrk rounded-rectangle;
```