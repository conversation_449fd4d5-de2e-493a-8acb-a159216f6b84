```mermaid
%% VERTICAL Mermaid Diagram: CI/CD, Pipelines & Automation/Jobs, Runners & Builds/Artifacts Handling & Cleanup Domain
flowchart TD
  %% Styling
  classDef core fill:#D4F1F9,stroke:#B3D8EB,stroke-width:2px,color:#222,rx:10,ry:10
  classDef data fill:#E0F8E0,stroke:#B8E9B6,stroke-width:2px,color:#222,rx:10,ry:10
  classDef util fill:#FFF8DC,stroke:#EDE5C2,stroke-width:2px,color:#222,rx:10,ry:10
  classDef error fill:#FFE4E1,stroke:#FFC0CB,stroke-width:2px,color:#222,rx:10,ry:10
  classDef init fill:#E6E6FA,stroke:#C8C8F5,stroke-width:2px,color:#222,rx:10,ry:10
  classDef group fill:#F8F8F8,stroke:#B3D8EB,stroke-width:2px

  %% === Artifact Model Layer ===
  subgraph S1["Artifact Models":::group]
    direction TB
    B1["ci/job_artifact.rb":::core] 
    B2["ci/pipeline_artifact.rb":::core]
    B3["ci/secure_file.rb":::core]
    B4["ci/deleted_object.rb":::core]
    B5["ci/job_artifact_report.rb":::core]
    CD1["concerns/ci/artifactable.rb":::util]
    CD2["concerns/object_storable.rb":::util]
    CD3["enums/ci/job_artifact.rb":::data]
    CD4["ee/enums/ci/job_artifact.rb":::data]
  end

  %% === Data Storage & Trace Chunking ===
  subgraph S2["Build Trace & Artifact Storage":::group]
    direction TB
    D1["ci/build_trace_chunks/database.rb":::data]
    D2["ci/build_trace_chunks/fog.rb":::data]
    D3["uploaders/ci/secure_file_uploader.rb":::util]
    D4["uploaders/ci/pipeline_artifact_uploader.rb":::util]
  end

  %% === Artifact Data Processing ===
  subgraph S3["Artifact Data Processing":::group]
    direction TB
    DA1["lib/gitlab/ci/artifact_file_reader.rb":::util]
    DA2["lib/gitlab/ci/build/artifacts/metadata.rb":::data]
    DA3["lib/gitlab/ci/build/artifacts/metadata/entry.rb":::data]
    DA4["lib/gitlab/ci/artifacts/decompressed_artifact_size_validator.rb":::util]
    DA5["lib/gitlab/ci/artifacts/logger.rb":::util]
    DA6["lib/gitlab/ci/trace/chunked_io.rb":::util]
    DA7["lib/gitlab/ci/trace/stream.rb":::util]
    DA8["lib/gitlab/ci/trace/metrics.rb":::util]
  end

  %% === Artifact API, GraphQL, and Controller ===
  subgraph S4["Artifact Access, API & GraphQL":::group]
    direction TB
    A1["controllers/projects/artifacts_controller.rb":::core]
    A2["graphql/mutations/ci/job/artifacts_destroy.rb":::core]
    A3["graphql/mutations/ci/job_artifact/destroy.rb":::core]
    A4["graphql/mutations/ci/job_artifact/bulk_destroy.rb":::core]
    A5["api/ci/job_artifacts.rb":::core]
    A6["ee/api/ci/job_artifacts.rb":::core]
    EF1["events/ci/job_artifacts_deleted_event.rb":::core]
    F1["finders/ci/job_artifacts_finder.rb":::util]
    QA1["qa/page/project/artifact/show.rb":::util]
  end

  %% === Artifact Lifecycle Services ===
  subgraph S5["Artifact Lifecycle Services":::group]
    direction TB
    S1A["services/ci/job_artifacts/create_service.rb":::core]
    S1B["services/ci/job_artifacts/delete_service.rb":::core]
    S1C["services/ci/job_artifacts/bulk_delete_by_project_service.rb":::core]
    S1D["services/ci/job_artifacts/expire_project_build_artifacts_service.rb":::core]
    S1E["services/ci/job_artifacts/track_artifact_report_service.rb":::core]
    S1F["services/ci/job_artifacts/destroy_all_expired_service.rb":::core]
    S1G["services/ci/job_artifacts/delete_project_artifacts_service.rb":::core]
    S1H["services/ci/job_artifacts/destroy_associations_service.rb":::core]
    S1I["services/ci/job_artifacts/update_unknown_locked_status_service.rb":::core]
    S1J["services/ci/unlock_artifacts_service.rb":::core]
    S1K["services/ci/archive_trace_service.rb":::core]
    S1L["services/ci/find_exposed_artifacts_service.rb":::core]
    S1M["services/ci/destroy_secure_file_service.rb":::core]
    S1N["services/projects/container_repository/destroy_service.rb":::core]
  end

  %% === Pipeline Artifact Services Backward-compatible ===
  subgraph S6["Pipeline Artifact Services":::group]
    direction TB
    PS1["services/ci/pipeline_artifacts/destroy_all_expired_service.rb":::core]
    PS2["services/ci/pipeline_artifacts/create_code_quality_mr_diff_report_service.rb":::core]
    PS3["services/ci/pipeline_artifacts/create_quality_report_worker.rb":::init]
    PS4["workers/ci/pipeline_artifacts/expire_artifacts_worker.rb":::init]
  end

  %% === Artifact Cron, Expiry & Maintenance Workers ===
  subgraph S7["Workers & Cron Jobs":::group]
    direction TB
    W1["workers/ci/archive_traces_cron_worker.rb":::init]
    W2["workers/ci/job_artifacts/track_artifact_report_worker.rb":::init]
    W3["workers/ci/job_artifacts/expire_project_build_artifacts_worker.rb":::init]
    W4["workers/ci/pipeline_artifacts/expire_artifacts_worker.rb":::init]
    W5["workers/ci/update_locked_unknown_artifacts_worker.rb":::init]
    W6["workers/ci/ref_delete_unlock_artifacts_worker.rb":::init]
    W7["workers/expire_build_artifacts_worker.rb":::init]
    W8["workers/deployments/hooks_worker.rb":::init]
  end

  %% === Artifact Verification, Cleanup & Backup ===
  subgraph S8["Verification, Cleanup & Backup":::group]
    direction TB
    V1["lib/gitlab/verify/job_artifacts.rb":::util]
    CL1["lib/gitlab/cleanup/orphan_job_artifact_files.rb":::util]
    CL2["spec/lib/gitlab/cleanup/orphan_job_artifact_files_spec.rb":::util]
    CL3["spec/lib/gitlab/cleanup/orphan_job_artifact_files_batch_spec.rb":::util]
    B1B["lib/backup/tasks/artifacts.rb":::util]
    B2B["lib/backup/tasks/builds.rb":::util]
    B3B["lib/backup/tasks/ci_secure_files.rb":::util]
  end

  %% === EE Extensions ===
  subgraph S9["EE Extensions":::group]
    direction TB
    EE1["ee/serializers/evidences/build_artifact_entity.rb":::util]
    EE2["ee/services/ee/packages/mark_package_for_destruction_service.rb":::util]
  end

  %% === QA & Spec Artifacts ===
  subgraph S10["QA & Spec: Artifacts End-to-End":::group]
    direction TB
    QAS1["qa/specs/features/browser_ui/4_verify/ci_job_artifacts/unlocking_job_artifacts_across_parent_child_pipelines_spec.rb":::util]
    QAS2["qa/service/docker_run/mixins/third_party_docker.rb":::util]
    QS1["spec/requests/api/graphql/mutations/ci/job_artifact/bulk_destroy_spec.rb":::util]
    SQ1["spec/lib/gitlab/ci/build/artifacts/metadata/entry_spec.rb":::util]
  end

  %% === Misc ===
  subgraph S11["Utility Scripts & Misc":::group]
    direction TB
    SC1["scripts/download-downstream-artifact.rb":::util]
    T1["tooling/lib/tooling/glci/failure_categories/download_job_trace.rb":::util]
  end

  %% ======================
  %%      Relationships
  %% ======================
  %% --- Core Model Layer ---
  B1 -- implements, stores --> CD1
  B1 -- uses file storage --> CD2
  B1 -- uses enums for types --> CD3
  B1 -- uses ee-specific enums --> CD4
  B2 -- includes, stores --> CD1
  B3 -- uses uploader --> D3
  B4 -- manages expired/removed artifacts --> B1
  CD1 -- includes logging --> DA5
  B1 -- has-one --> B5

  %% --- Build Trace Chunks ---
  D1 -- stores trace data --> B1
  D2 -- object storage support --> D1
  D1 -- data used in --> DA6
  DA6 -- IO access --> B1

  %% --- Artifact Access ---
  A1 -- returns artifact data from --> B1
  F1 -- queries artifact with --> B1
  A2 -- mutation deletes --> B1
  A3 -- destroys singular --> B1
  A4 -- bulk destroys --> B1
  EF1 -- event emitted on-delete --> B1
  A5 -- API endpoints on --> B1
  A5 -- download + auth helpers --> A1
  A6 -- EE layer extends API --> A5
  QA1 -- tests UI for --> B1

  %% --- Data Processing ---
  DA1 -- reads from jobs/artifacts --> B1
  DA2 -- parses artifact structure --> DA1
  DA3 -- represents artifact files/dirs --> DA2
  DA4 -- validates decompressed artifacts --> DA2
  DA5 -- logs to artifact actions --> B1
  DA7 -- streams log output --> D1
  DA8 -- metrics on trace operations --> D1

  %% --- Artifact Services Lifecycle ---
  S1A -- stores new artifact --> B1
  S1A -- uses type/size --> CD3
  S1B -- removes artifact --> B1
  S1C -- destroys all for project --> B1
  S1D -- expires old build artifacts --> B1
  S1E -- tracks uploaded reports --> B1
  S1F -- sweeps+removes expired --> B1
  S1G -- deletes all project artifacts via worker --> W7
  S1H -- removes associations for fast destroy --> B1
  S1I -- updates locking states --> B1
  S1J -- unlocks old artifacts --> B1
  S1K -- archives traces --> D1
  S1L -- exposes artifacts for download --> B1
  S1M -- destroys secure file --> B3
  S1N -- handles related container artifacts --> B4

  %% --- Workers & Cron ---
  W1 -- archives traces --> S1K
  W2 -- tracks reports --> S1E
  W3 -- expires project build artifacts --> S1D
  W4 -- expires pipeline artifacts --> PS1
  W5 -- updates unknown lock status by invoking --> S1I
  W6 -- unlocks artifacts ref --> S1J
  W7 -- batch artifact expiry --> S1F
  W8 -- handles container deployment hooks --> S1N

  %% --- Pipeline Artifact Services ---
  PS1 -- destroys expired pipeline artifacts --> B2
  PS2 -- code quality report ops --> B2
  PS3 -- background pipeline artifact creation --> B2
  PS4 -- triggers PS1

  %% --- Verification, Cleanup, Backup ---
  V1 -- verifies artifact integrity --> B1
  CL1 -- finds/removes orphaned artifacts (filesystem) --> B1
  CL2 -- spec for CL1
  CL3 -- batch spec for CL1
  B1B -- backs up artifacts --> B1
  B2B -- builds backup --> B1
  B3B -- backup of secure files --> B3

  %% --- EE Extensions ---
  EE1 -- exposes artifact URLs --> B1
  EE2 -- manages destruction in EE --> B4

  %% --- QA & Spec Connections ---
  QAS1 -- tests unlocking logic across pipelines --> B1
  QAS2 -- service mixin for third-party Docker used in artifacts jobs
  QS1 -- tests GraphQL bulk destroy on artifacts --> A4
  SQ1 -- tests artifact metadata entry --> DA3

  %% --- Misc ---
  SC1 -- downloads downstream artifacts via API --> A5
  T1 -- downloads job traces for validation --> D1

  %% --- Cross-group relationships (dashed) ---
  CD1 -.-> DA5
  CD1 -.-> DA2
  D3 -.-> B3
  D4 -.-> B2
  DA6 -.-> DA7
  DA6 -.-> DA8
  PS2 -.-> S1A

  %% --- Legend (not displayed as nodes) ---
  %% class core/core files
  %% class data/data structure files
  %% class util/supporting/utility files
  %% class error/error handling files
  %% class init/initialization/setup files
  %% class group for logical grouping/subgraph

  %% Assign classes
  class B1,B2,B3,B4,B5,A1,A2,A3,A4,A5,A6,EF1,S1A,S1B,S1C,S1D,S1E,S1F,S1G,S1H,S1I,S1J,S1K,S1L,S1M,S1N,PS1,PS2,EE1,EE2 core
  class CD1,CD2,F1,DA1,DA4,DA5,DA6,DA7,DA8,V1,CL1,CL2,CL3,B1B,B2B,B3B,QA1,QAS1,QAS2,QS1,SQ1,SC1,T1 util
  class D1,D2,DA2,DA3,CD3,CD4 data
  class W1,W2,W3,W4,W5,W6,W7,W8,PS3,PS4 init
```