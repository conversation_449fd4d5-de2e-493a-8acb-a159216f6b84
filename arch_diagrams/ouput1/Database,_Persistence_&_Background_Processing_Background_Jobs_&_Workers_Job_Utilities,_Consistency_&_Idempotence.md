```mermaid
flowchart TD
  %% Style Definitions %%%
  classDef coreDomain fill:#D4F1F9,stroke:#7FB8D3,stroke-width:2px,color:#212529,rx:10,ry:10
  classDef supporting fill:#FFF8DC,stroke:#E5D9AA,stroke-width:2px,color:#212529,rx:10,ry:10
  classDef dataStructure fill:#E0F8E0,stroke:#80C280,stroke-width:2px,color:#212529,rx:10,ry:10
  classDef errorHandling fill:#FFE4E1,stroke:#D29191,stroke-width:2px,color:#212529,rx:10,ry:10
  classDef initialization fill:#E6E6FA,stroke:#C1B6DD,stroke-width:2px,color:#212529,rx:10,ry:10
  classDef logicalGrouping fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,color:#212529,rx:10,ry:10
  classDef eeCoreDomain fill:#D4F1F9,stroke:#6FC1E9,stroke-width:2px,color:#212529,rx:10,ry:10

  %% DOMAIN GROUPINGS %%%
  subgraph Database and Persistence [Database and Persistence]
    direction TB
    class Database and Persistence logicalGrouping
    subgraph BatchProcessing [Batch Processing Abstractions]
      direction TB
      EBRB[EachBatch module\nEfficient batch iteration]:::coreDomain
      FPB[FastDestroyAll module\nEfficient destruction of records]:::coreDomain
      TRRB[Transitionable module\nTransitioning record states]:::supporting
      RPRB[RelativePositioning module\nPosition calculation in record sets]:::coreDomain
      SARB[ShaAttribute module\nSHA-typed attribute support]:::supporting
      BCMRB[BatchVerifier Verify]:::coreDomain
    end

    subgraph FinderSupport [Finder Utilities]
      direction TB
      FMRB[FinderMethods\nAuthorization plus finders]:::supporting
      UAFB[UpdatedAtFilter module\nUpdated at filtering]:::supporting
    end

    subgraph DataMutation [Transactional and Callback Utilities]
      direction TB
      ACRB[AfterCommitQueue module\nQueue callbacks after commit]:::coreDomain
      BSB[BaseServiceUtility module\nService utilities]:::supporting
    end

    subgraph PaginationSupport [Pagination/Keyset Utilities]
      direction TB
      PKT[Pagination Keyset RequestContext]:::coreDomain
      GKP[GitalyKeysetPager\nKeyset pagination for repositories]:::coreDomain
    end

    subgraph IdBatchingSupport [ID Batching]
      direction TB
      NPEB[NamespaceProjectIdsEachBatch\nBatching project IDs in namespaces]:::dataStructure
    end
  end

  subgraph Background Job & Workers [Background Processing, Jobs, and Workers]
    direction TB
    class Background Job & Workers logicalGrouping
    subgraph WorkerCore [Worker Base and Shared Concerns]
      direction TB
      AWB[ApplicationWorker base concern]:::coreDomain
      WATTR[WorkerAttributes concern\nFeature category, consistency]:::coreDomain
      ECEX[ExceptionBacktrace\nHandles error backtraces]:::errorHandling
      LCW[LimitedCapacity::Worker\nMulti-job processing/limits]:::coreDomain
      LCJT[LimitedCapacity::JobTracker\nTracks jobs and capacity slots]:::dataStructure
      REAC[ReactiveCacheableWorker\nCache-based jobs]:::coreDomain
    end

    subgraph Cluster and Storage Workers [Cluster/Repository-specific Workers]
      direction TB
      CLQ[ClusterAgentQueue]:::coreDomain
      CCMM[ClusterCleanupMethods]:::coreDomain
      RRQ[RepositoryCheckQueue]:::coreDomain
      URSW[UpdateRepositoryStorageWorker]:::coreDomain
      PIO[ProjectImportOptions]:::coreDomain
      NIS[NewIssuable]:::coreDomain
    end

    subgraph Worker Utilities [Background Worker Utilities]
      direction TB
      WSSS[WorkerSessionStateSetter EE]:::coreDomain
      RSM[GitHubImport::ReschedulingMethods]:::coreDomain
      LWRL[LoopWithRuntimeLimit\nTime-limited loops for jobs]:::supporting
      CBWC[ClickHouse::ConsistencyWorker\nCross-DB batch processing & consistency]:::coreDomain
    end
  end

  subgraph Job Utilities [Job Utilities, Third-party Integrations]
    direction TB
    class Job Utilities logicalGrouping
    subgraph SidekiqInternal [Sidekiq Core Job Utils]
      direction TB
      SKJU[Sidekiq::JobUtil\nValidation/normalization]:::supporting
      SKJR[Sidekiq::JobRetry\nRetry handling, morgue]:::errorHandling
      SKITER[Sidekiq::Job::Iterable and IterableJob\nIterative job execution]:::coreDomain
    end

    subgraph GiLabJobInfra [Pipeline/Job logic]
      direction TB
      SGML[Gitlab::SidekiqLimits\nQueue limits logic]:::coreDomain
      JW[Gitlab::JobWaiter\nSynchronize batch job completion]:::coreDomain
      BGT[Gitlab::BackgroundTask\nEncapsulated reusable background tasks]:::coreDomain
    end
  end

  subgraph Background Migration Framework [Background Migration Framework]
    direction TB
    class Background Migration Framework logicalGrouping
    subgraph CoreMigration [Batching Strategies]
      direction TB
      BMLOG[BgMigration::Logger\nStructured migration logs]:::supporting
      BMBAS[BgMigration::BatchingStrategies::BaseStrategy\nAbstract base]:::coreDomain
      BMLOOSE[BgMigration::BatchingStrategies::LooseIndexScanBatchingStrategy\nNon-distinct column batching]:::coreDomain
      BMDISM[BgMigration::BatchingStrategies::DismissedVulnerabilitiesStrategy\nDismissed vulnerabilities batching]:::coreDomain
      BMBACKVG[BgMigration::BatchingStrategies::BackfillVulnerabilityReadsClusterAgentBatchingStrategy]:::coreDomain
      BMBACKPS[BgMigration::BatchingStrategies::BackfillProjectStatisticsWithContainerRegistrySizeBatchingStrategy]:::coreDomain
      BMBACKPN[BgMigration::BatchingStrategies::BackfillProjectNamespacePerGroupBatchingStrategy]:::coreDomain
    end
  end

  subgraph Consistency & Idempotence [Consistency & Idempotence]
    direction TB
    class Consistency & Idempotence logicalGrouping
    CONDB[Database::Consistency\nRead-after-write consistency]:::coreDomain
    EXLG[ExclusiveLeaseGuard\nEnsure exclusive resource use]:::supporting
    IDPC[IdempotencyCache\nIdempotency for critical sections]:::coreDomain
  end

  subgraph Error Handling & Retry [Error Handling, Queue Error Handling]
    direction TB
    class Error Handling & Retry logicalGrouping
    QERR[Database::QueueErrorHandlingConcern\nLength validations for errors]:::errorHandling
  end

  subgraph Data Structures & EE Logic [Domain Data Structures, EE Extensions]
    direction TB
    class Data Structures & EE Logic logicalGrouping
    EEBD[EE::Ci::BuildDependencies\nEE cross-project dependency limit]:::eeCoreDomain
    SCIMP[ScimPaginatable EE\nEE SCIM paging]:::eeCoreDomain
    APPRH[ApproverMigrateHook EE\nEE approval migration hook]:::eeCoreDomain
    FGH[EE::FinderWithGroupHierarchy\nEE group hierarchy preload]:::eeCoreDomain
    EE_BUILDW[EE::Ci::BuildFinishedWorker\nEE-specific build finish logic]:::eeCoreDomain
  end

  subgraph Search/Elastic Utilities [Search/ElasticSearch EE Utilities]
    direction TB
    class Search/Elastic Utilities logicalGrouping
    SEDT[DocumentType EE]:::eeCoreDomain
    SEBKH[MigrationBackfillHelper EE]:::eeCoreDomain
    SECIH[MigrationCreateIndexHelper EE]:::eeCoreDomain
    SERIH[MigrationReindexTaskHelper EE]:::eeCoreDomain
    SEDBSV[MigrationDeleteBasedOnSchemaVersion EE]:::eeCoreDomain
    SEHELP[MigrationHelper EE]:::eeCoreDomain
    SEOBS[MigrationObsolete EE]:::eeCoreDomain
    SEUMH[MigrationUpdateMappingsHelper EE]:::eeCoreDomain
  end

  %% RELATIONSHIPS AND INTERACTIONS %%
  %% BatchProcessing
  EBRB --provides batched iteration to--> BMLOOSE
  EBRB --used for record movement in--> RPRB
  EBRB --used for batch verification in--> BCMRB
  EBRB --efficient batch destroy in--> FPB
  FPB --called by model-specific logic in--> BatchProcessing
  RPRB --supports relative order updates in-->|record sets| BatchProcessing
  SARB --SHA columns for records in--> BatchProcessing
  EBRB --batch iteration for group NS batching in--> NPEB

  %% FinderSupport
  FMRB -.uses.-> BatchProcessing
  UAFB -.used in.-> FinderSupport

  %% DataMutation
  ACRB --runs after commit callbacks for migrations/workers in--> WorkerCore
  BSB -.facilitates service orchestration for background jobs in.-> WorkerCore

  %% PaginationSupport
  PKT --provides pagination context to--> GKP

  %% IdBatchingSupport
  NPEB --used in-->|namespace/project iterators| BatchProcessing

  %% WorkerCore
  AWB --included in all app workers (eg, ReactiveCacheableWorker, LimitedCapacity, jobs)--> WorkerCore
  AWB --framework for worker attributes--> WATTR
  WATTR --specifies consistency/idempotency categories for jobs--> WorkerCore
  LCW --invokes LCJT to manage job slot allocation and capacity---> LCJT
  REAC --inherits from--> AWB

  %% Cluster and Storage Workers
  URSW --calls into storage move logic, may enqueue jobs via AWB--> AWB
  CCMM --used for cleanup and capacity checks in worker flows-->|to prevent over-processing| WorkerCore

  %% Worker Utilities
  CBWC --batches over ClickHouse tables, resumes processing-->|provides idempotence/consistency| Consistency & Idempotence
  RSM --rescheduling jobs in GitHub imports|--> WorkerCore
  LWRL --time-limited loops for job batches-->|used in high-throughput jobs| WorkerCore

  %% Job Utilities
  SKJU --validates and normalizes any job for Sidekiq-->|consumed by worker launching| Background Job & Workers
  SKJR --retries and morgue on error for Sidekiq jobs-->|manages job lifecycle| Error Handling & Retry
  SGML --computes limits for workers, enforces throttling-->|applies at job enqueue time| WorkerCore
  JW --used as synchronization primitive for batch jobs--> WorkerCore
  BGT --manages independent background tasks, can start/stop-->|utilized by data maintenance jobs| WorkerCore

  %% Background Migration Framework
  BMBAS --abstract base for all batch strategies-->|extended by| BMLOOSE
  BMBAS --abstract base for all batch strategies-->|extended by| BMDISM
  BMBAS --abstract base for all batch strategies-->|extended by| BMBACKVG
  BMBAS --abstract base for all batch strategies-->|extended by| BMBACKPS
  BMBAS --abstract base for all batch strategies-->|extended by| BMBACKPN
  BMLOOSE --leverages EachBatch for iteration|--> EBRB

  %% Consistency & Idempotence
  CONDB --sets up read consistency block for worker/controller calls in--> WorkerCore
  EXLG --ensures only one concurrent job/worker in batch, interacts with-->|critical sections| WorkerCore
  IDPC --ensures idempotent job execution|--> Background Job & Workers
  IDPC --used for critical batch/joined jobs|--> Background Migration Framework

  %% Error Handling & Retry
  QERR --included in batched job logic for error truncation, used by-->|job record models| Error Handling & Retry

  %% Data Structures & EE Logic
  EEBD --extends job dependency limits, used in EE only|--> Background Job & Workers
  SCIMP --EE paging concern for models|--> Data Structures & EE Logic
  APPRH --EE approval hook logic, triggers jobs-->|interacts with services/jobs| WorkerCore
  FGH --preloading associations optimizes batch loads-->|support for EE group queries| FinderSupport
  EE_BUILDW --after build jobs, triggers report worker-->|chained job flows| WorkerCore

  %% Search/Elastic Utilities
  SEDT --provides document type base for all elastic migrations|--> SECIH
  SEDT --provides document type base for all elastic migrations|--> SEHELP
  SEBKH --uses index naming from DocumentType module-->|helper mixin| SEDT
  SEHELP --provides shard/slice logic to elastic migrations-->|mixin to helpers| SEBKH
  SEHELP --provides shard/slice logic to elastic migrations-->|mixin to helpers| SECIH
  SEHELP --provides shard/slice logic to elastic migrations-->|mixin to helpers| SERIH
  SEHELP --provides shard/slice logic to elastic migrations-->|mixin to helpers| SEDBSV
  SEUMH --applies migrations, triggers mappings update-->|uses index helper| SEDT
  SEOBS --logs migration halts|--> Error Handling & Retry

  %% Key cross-group links
  ACRB --queues after-commit actions for batch jobs-->|ensures atomicity| Background Migration Framework
  SKJU --consumed by ApplicationWorker for job normalization-->|initiation| AWB
  LCJT --tracks running job IDs for retry/limited workers-->|supports| LCW
  IDPC --provides deduplication/idempotence for Sidekiq jobs-->|is invoked by| AWB

  %% Testing and QA Entry Points (not styled but shown as relationships)
  subgraph TestingSupport [Test Coverage & QA]
    direction TB
    class TestingSupport logicalGrouping
    QARM[QA::RepositoryStorageMoves]:::supporting
    BATCH_OPTIM[BatchOptimizer spec]:::supporting
    LWMS[LockWritesManager spec]:::supporting
    QLMW[QueryLimiting SidekiqMiddleware spec]:::supporting
    RENEQUEUER[Reenqueuer spec]:::supporting
    ENVCREATE[Create environment for job shared examples]:::supporting
    ORPHAN_CLEANUP[OrphanFinalArtifactsCleanupHelpers]:::supporting
  end

  BatchProcessing --unit testing coverage for batch behaviors--> BATCH_OPTIM
  WorkerCore --unit testing for query limiting and worker context--> QLMW
  WorkerCore --unit testing for re-enqueue/lease edge cases--> RENEQUEUER
  DataMutation --service helpers/unit testing--> ENVCREATE
  Cluster and Storage Workers --QA moves, cluster storage moves--> QARM
  Error Handling & Retry --unit testing for error handling in migrations--> LWMS
  DataMutation --file cleanup in batch process testing--> ORPHAN_CLEANUP

  %% Styling for all nodes
  class EBRB,FPB,TRRB,RPRB,SARB,BCMRB,PKT,GKP,NPEB,AWB,WATTR,LCW,LCJT,REAC,CLQ,CCMM,RRQ,URSW,PIO,NIS,WSSS,RSM,LWRL,CBWC,SKITER,SGML,SKJU,SKJR,JW,BGT,BMBAS,BMLOOSE,BMDISM,BMBACKVG,BMBACKPS,BMBACKPN,CONDB,IDPC,EXLG coreDomain
  class QERR,ECEX,SKJR errorHandling
  class NPEB,LCJT dataStructure
  class BMLOG,FGH,BSB supporting
  class BMLOG,BMBAS supporting
  class SEDT,SEBKH,SECIH,SERIH,SEDBSV,SEHELP,SEOBS,SEUMH eeCoreDomain
  class EEBD,SCIMP,APPRH,EE_BUILDW eeCoreDomain
  classdef logicalGrouping fill:#F8F8F8,stroke:#A3A3A3,stroke-width:2px,color:#212529,rx:24,ry:24
```