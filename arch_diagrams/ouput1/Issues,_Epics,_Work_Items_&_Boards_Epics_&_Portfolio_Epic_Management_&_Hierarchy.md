```mermaid
flowchart TD
  %% CORE DOMAIN ENTITIES & ABSTRACTIONS
  subgraph Epic Management & Hierarchy[#F8F8F8]
    direction TB
    style Epic Management & Hierarchy fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded-rectangle
    E1[Epic Model\napp/models/epic.rb]:::core
    EE_E1[EE::Epic Model Extension\nee/app/models/ee/epic.rb]:::core
    EpicTreeSorting[Epics Tree Sorting\nConcern\nee/app/models/concerns/epic_tree_sorting.rb]:::core
    EpicIssue[Epics-Issues Join\nAssociation\nee/app/models/epic_issue.rb]:::core
    RelatedEpicLink[Related Epics Linkage\nee/app/models/epic/related_epic_link.rb]:::core
    EpicUserMention[Epic User Mentions\nee/app/models/epic_user_mention.rb]:::core
    LabelExt[EE::Label Extension\nEpics Board Tasks\nee/app/models/ee/label.rb]:::core
    FeatureFlagIssue[Feature Flag Issue\nee/app/models/feature_flag_issue.rb]:::core

    EpicTreeSorting --> E1
    EE_E1 --> E1
    EpicIssue --> E1
    RelatedEpicLink --> E1
    EpicUserMention --> E1
    LabelExt --> E1

    E1 -.->|Associates| EpicIssue
    E1 -.->|Has| RelatedEpicLink
    E1 -.->|Mentions through| EpicUserMention
    E1 -.->|Labels via| LabelExt
    EpicIssue -.->|Concerned| EpicTreeSorting
    EpicIssue -.->|Joins| FeatureFlagIssue
    RelatedEpicLink -.->|EE Extension| EE_E1
  end

  %% POLICIES & ACCESS CONTROL
  subgraph Policies & Access[#F8F8F8]
    direction TB
    style Policies & Access fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded-rectangle
    EpicPolicy[Epic Policy\nee/app/policies/epic_policy.rb]:::support
    EpicBoardPolicy[Boards::EpicBoardPolicy\nee/app/policies/boards/epic_board_policy.rb]:::support
    EpicPolicy --> E1
    EpicBoardPolicy --> LabelExt
  end

  %% CONTROLLERS
  subgraph Epic Controllers[#F8F8F8]
    direction TB
    style Epic Controllers fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded-rectangle
    EpicsCtrl[Groups::EpicsController\nee/app/controllers/groups/epics_controller.rb]:::core
    EpicLinksCtrl[Groups::Epics::EpicLinksController\nee/app/controllers/groups/epics/epic_links_controller.rb]:::core
    EpicIssuesCtrl[Groups::EpicIssuesController\nee/app/controllers/groups/epic_issues_controller.rb]:::core
    RelatedEpicLinksCtrl[Groups::Epics::RelatedEpicLinksController\nee/app/controllers/groups/epics/related_epic_links_controller.rb]:::core
    EpicsActions[EpicsActions Controller Concern\nee/app/controllers/concerns/epics_actions.rb]:::support

    EpicsCtrl --> E1
    EpicLinksCtrl --> E1
    RelatedEpicLinksCtrl --> E1
    EpicIssuesCtrl --> EpicIssue
    EpicsCtrl -.Uses.->|Concern| EpicsActions
    EpicLinksCtrl -.Uses.->|Concern| EpicsActions
    EpicLinksCtrl -.->|Relates via| RelatedEpicLink
    EpicLinksCtrl -.->|Relates Epic Issue| EpicIssue
    RelatedEpicLinksCtrl -.->|Associates| RelatedEpicLink
    EpicIssuesCtrl -.->|Join| EpicIssue
  end

  %% FINDERS & QUERY OBJECTS
  subgraph Finders & Querying[#F8F8F8]
    direction TB
    style Finders & Querying fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2px,rounded-rectangle
    EpicsFinder[EpicsFinder\nee/app/finders/epics_finder.rb]:::support
    FinderFindable[Epics::Findable Concern\nee/app/finders/concerns/epics/findable.rb]:::support
    EpicBoardsFinder[Boards::EpicBoardsFinder\nee/app/finders/boards/epic_boards_finder.rb]:::support
    XAncestorsFinder[Epics::CrossHierarchyAncestorsFinder\nee/app/finders/epics/cross_hierarchy_ancestors_finder.rb]:::support
    XChildrenFinder[Epics::CrossHierarchyChildrenFinder\nee/app/finders/epics/cross_hierarchy_children_finder.rb]:::support
    WithIssuesFinder[Epics::WithIssuesFinder\nee/app/finders/epics/with_issues_finder.rb]:::support

    EpicsFinder --> FinderFindable
    EpicBoardsFinder --> E1
    XAncestorsFinder --> E1
    XAncestorsFinder --> FinderFindable
    XChildrenFinder --> E1
    XChildrenFinder --> FinderFindable
    WithIssuesFinder --> E1
    FinderFindable --> E1
  end

  %% GRAPHQL TYPES, RESOLVERS, & MUTATIONS
  subgraph GraphQL API Layer[#F8F8F8]
    direction TB
    style GraphQL API Layer fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded-rectangle

    %% Enums and Types
    EpicType[GQL Types::EpicType Implied]:::data
    EpicIssueType[GQL Types::EpicIssueType\nee/app/graphql/types/epic_issue_type.rb]:::data
    EpicDescendantWeightSum[GQL Types::EpicDescendantWeightSumType\nee/app/graphql/types/epic_descendant_weight_sum_type.rb]:::data
    EpicSortEnum[GQL Types::EpicSortEnum\nee/app/graphql/types/epic_sort_enum.rb]:::data
    EpicWildcardIdEnum[GQL Types::EpicWildcardIdEnum\nee/app/graphql/types/epic_wildcard_id_enum.rb]:::data
    EpicStateEventEnum[GQL Types::EpicStateEventEnum\nee/app/graphql/types/epic_state_event_enum.rb]:::data
    EpicStateEnum[GQL Types::EpicStateEnum\nee/app/graphql/types/epic_state_enum.rb]:::data
    EpicTreeNodeInput[GQL Types::EpicTree::EpicTreeNodeInputType\nee/app/graphql/types/epic_tree/epic_tree_node_input_type.rb]:::data
    UnionedEpicFilterInput[GQL Types::Epics::UnionedEpicFilterInputType\nee/app/graphql/types/epics/unioned_epic_filter_input_type.rb]:::data
    NegatedEpicFilterInput[GQL Types::Epics::NegatedEpicFilterInputType\nee/app/graphql/types/epics/negated_epic_filter_input_type.rb]:::data

    %% Mutations
    MutBase[Mutations::Epics::Base\nee/app/graphql/mutations/epics/base.rb]:::core
    MutUpdate[Mutations::Epics::Update\nee/app/graphql/mutations/epics/update.rb]:::core
    MutCreate[Mutations::Epics::Create\nee/app/graphql/mutations/epics/create.rb]:::core
    MutAddIssue[Mutations::Epics::AddIssue\nee/app/graphql/mutations/epics/add_issue.rb]:::core
    MutSetSubscription[Mutations::Epics::SetSubscription\nee/app/graphql/mutations/epics/set_subscription.rb]:::core
    MutReorderTree[Mutations::EpicTree::Reorder\nee/app/graphql/mutations/epic_tree/reorder.rb]:::core
    SharedEpicArgs[Mutations::SharedEpicArguments Concern\nee/app/graphql/mutations/concerns/mutations/shared_epic_arguments.rb]:::support
    MutBoardsEpicBase[Mutations::Boards::EpicBoards::Base\nee/app/graphql/mutations/boards/epic_boards/base.rb]:::core
    MutBoardsEpicMoveList[Mutations::Boards::EpicBoards::EpicMoveList\nee/app/graphql/mutations/boards/epic_boards/epic_move_list.rb]:::core
    MutBoardsEpicListUpdate[Mutations::Boards::EpicLists::Update\nee/app/graphql/mutations/boards/epic_lists/update.rb]:::core
    MutBoardsEpicListCreate[Mutations::Boards::EpicLists::Create\nee/app/graphql/mutations/boards/epic_lists/create.rb]:::core
    MutIssuesPromoteToEpic[Mutations::Issues::PromoteToEpic\nee/app/graphql/mutations/issues/promote_to_epic.rb]:::core

    %% Resolvers
    EpicsResolver[GQL::EpicsResolver\nee/app/graphql/resolvers/epics_resolver.rb]:::core
    EpicIssuesResolver[GQL::EpicIssuesResolver\nee/app/graphql/resolvers/epic_issues_resolver.rb]:::core
    BoardEpicsRes[Resolvers::BoardGroupings::EpicsResolver\nee/app/graphql/resolvers/board_groupings/epics_resolver.rb]:::core
    BoardEpicListRes[Resolvers::Boards::EpicListResolver\nee/app/graphql/resolvers/boards/epic_list_resolver.rb]:::core
    EpicChildrenRes[Resolvers::Epics::ChildrenResolver\nee/app/graphql/resolvers/epics/children_resolver.rb]:::core
    EpicAncestorsRes[Resolvers::EpicAncestorsResolver\nee/app/graphql/resolvers/epic_ancestors_resolver.rb]:::core

    %% Relationships (Types)
    EpicIssueType --> EpicType
    EpicDescendantWeightSum --> EpicType
    EpicType -.Uses.->|Fields| EpicSortEnum
    EpicType -.Uses.->|State| EpicStateEnum
    EpicType -.Uses.->|Event| EpicStateEventEnum
    EpicType -.Uses.->|Tree Node| EpicTreeNodeInput
    EpicType -.Filter.-> UnionedEpicFilterInput
    EpicType -.Filter.-> NegatedEpicFilterInput

    %% Relationships (Mutations)
    MutBase --> EpicType
    MutUpdate --> MutBase
    MutUpdate --> SharedEpicArgs
    MutCreate --> MutBase
    MutCreate --> SharedEpicArgs
    MutAddIssue --> MutBase
    MutSetSubscription --> MutBase
    MutReorderTree --> EpicType
    SharedEpicArgs --> EpicType
    MutBoardsEpicBase --> EpicType
    MutBoardsEpicMoveList --> MutBoardsEpicBase
    MutBoardsEpicListUpdate --> MutBoardsEpicBase
    MutBoardsEpicListCreate --> MutBoardsEpicBase
    MutIssuesPromoteToEpic --> EpicType

    %% Relationships (Resolvers)
    EpicsResolver --> EpicsFinder
    EpicsResolver --> EpicType
    EpicIssuesResolver --> EpicIssueType
    BoardEpicsRes --> EpicType
    BoardEpicListRes --> EpicType
    EpicChildrenRes --> EpicType
    EpicAncestorsRes --> EpicType
    EpicChildrenRes --> EpicsResolver
    EpicAncestorsRes --> EpicsResolver
  end

  %% SERIALIZERS & ENTITY REPRESENTATION
  subgraph Serializers & Entities[#F8F8F8]
    direction TB
    style Serializers & Entities fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded-rectangle
    EpicEntity[EpicEntity\nee/app/serializers/epic_entity.rb]:::core
    EpicBaseEntity[EpicBaseEntity\nee/app/serializers/epic_base_entity.rb]:::core
    EpicAiEntity[EpicAiEntity\nee/app/serializers/epic_ai_entity.rb]:::core
    EpicNoteEntity[EpicNoteEntity\nee/app/serializers/epic_note_entity.rb]:::core
    LinkedEpicEntity[LinkedEpicEntity\nee/app/serializers/linked_epic_entity.rb]:::core
    LinkedEpicIssueEntity[LinkedEpicIssueEntity\nee/app/serializers/linked_epic_issue_entity.rb]:::core
    LinkedEpicSerializer[LinkedEpicSerializer\nee/app/serializers/linked_epic_serializer.rb]:::core
    LinkedEpicIssueSerializer[LinkedEpicIssueSerializer\nee/app/serializers/linked_epic_issue_serializer.rb]:::core
    EpicNoteSerializer[EpicNoteSerializer\nee/app/serializers/epic_note_serializer.rb]:::core
    EpicSerializer[EpicSerializer\nee/app/serializers/epic_serializer.rb]:::core
    EpicDiscussionSerializer[Epics::DiscussionSerializer\nee/app/serializers/epics/discussion_serializer.rb]:::core
    EpicsRelatedEpicEntity[Epics::RelatedEpicEntity\nee/app/serializers/epics/related_epic_entity.rb]:::core
    EpicsRelatedEpicSerializer[Epics::RelatedEpicSerializer\nee/app/serializers/epics/related_epic_serializer.rb]:::core

    EpicEntity --> EpicBaseEntity
    EpicAiEntity --> EpicEntity
    EpicNoteEntity --> EpicNoteSerializer
    EpicNoteSerializer --> EpicNoteEntity
    LinkedEpicEntity --> EpicEntity
    LinkedEpicIssueEntity --> LinkedEpicEntity
    LinkedEpicSerializer --> LinkedEpicEntity
    LinkedEpicIssueSerializer --> LinkedEpicIssueEntity
    EpicSerializer --> EpicEntity
    EpicDiscussionSerializer --> EpicEntity
    EpicsRelatedEpicSerializer --> EpicsRelatedEpicEntity
  end

  %% PRESENTERS, HELPERS & VIEW LOGIC
  subgraph Presentation & View Logic[#F8F8F8]
    direction TB
    style Presentation & View Logic fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2px,rounded-rectangle
    EpicPresenter[EpicPresenter\nee/app/presenters/epic_presenter.rb]:::support
    EpicIssuePresenter[EpicIssuePresenter\nee/app/presenters/epic_issue_presenter.rb]:::support
    WorkItemPresenter[EE::WorkItemPresenter\nee/app/presenters/ee/work_item_presenter.rb]:::support
    EpicsHelper[EpicsHelper\nee/app/helpers/epics_helper.rb]:::support
    NewDropdownHelper[EE::NewDropdownHelper\nee/app/helpers/ee/nav/new_dropdown_helper.rb]:::support

    EpicPresenter --> EpicEntity
    EpicIssuePresenter --> EpicIssue
    WorkItemPresenter --> E1
    EpicsHelper --> E1
    NewDropdownHelper --> E1
    NewDropdownHelper --> EpicsHelper
  end

  %% EVENTING & NOTIFICATIONS
  subgraph Event Notifications & Emails[#F8F8F8]
    direction TB
    style Event Notifications & Emails fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded-rectangle
    EpicCreatedEvent[Epics::EpicCreatedEvent\nee/app/events/epics/epic_created_event.rb]:::support
    EpicUpdatedEvent[Epics::EpicUpdatedEvent\nee/app/events/epics/epic_updated_event.rb]:::support
    EmailsEpics[Emails::Epics\nee/app/mailers/emails/epics.rb]:::support

    EpicCreatedEvent --> E1
    EpicUpdatedEvent --> E1
    EmailsEpics --> E1
    EmailsEpics --> EpicNoteEntity
  end

  %% SERVICES: EPICS
  subgraph Epics Services[#F8F8F8]
    direction TB
    style Epics Services fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded-rectangle

    EpicsBaseService[Epics::BaseService\nee/app/services/epics/base_service.rb]:::core
    EpicsCreateService[Epics::CreateService\nee/app/services/epics/create_service.rb]:::core
    EpicsUpdateService[Epics::UpdateService\nee/app/services/epics/update_service.rb]:::core
    EpicsCloseService[Epics::CloseService\nee/app/services/epics/close_service.rb]:::core
    EpicsReopenService[Epics::ReopenService\nee/app/services/epics/reopen_service.rb]:::core
    EpicsUpdateDatesService[Epics::UpdateDatesService\nee/app/services/epics/update_dates_service.rb]:::core
    EpicsDescendantCountService[Epics::DescendantCountService\nee/app/services/epics/descendant_count_service.rb]:::core
    EpicsTransferService[Epics::TransferService\nee/app/services/epics/transfer_service.rb]:::core
    EpicsTreeReorderService[Epics::TreeReorderService\nee/app/services/epics/tree_reorder_service.rb]:::core
    EpicsIssuePromoteService[Epics::IssuePromoteService\nee/app/services/epics/issue_promote_service.rb]:::core
    EpicLinksListService[Epics::EpicLinks::ListService\nee/app/services/epics/epic_links/list_service.rb]:::core
    EpicLinksDestroyService[Epics::EpicLinks::DestroyService\nee/app/services/epics/epic_links/destroy_service.rb]:::core
    EpicIssuesUpdateService[EpicIssues::UpdateService\nee/app/services/epic_issues/update_service.rb]:::core
    EpicIssuesDestroyService[EpicIssues::DestroyService\nee/app/services/epic_issues/destroy_service.rb]:::core
    EpicsRelatedEpicLinksDestroy[Epics::RelatedEpicLinks::DestroyService\nee/app/services/epics/related_epic_links/destroy_service.rb]:::core
    EpicsStrategiesBase[Epics::Strategies::BaseDatesStrategy\nee/app/services/epics/strategies/base_dates_strategy.rb]:::core
    EpicsStrategiesStart[Epics::Strategies::StartDateInheritedStrategy\nee/app/services/epics/strategies/start_date_inherited_strategy.rb]:::core
    EpicsStrategiesDue[Epics::Strategies::DueDateInheritedStrategy\nee/app/services/epics/strategies/due_date_inherited_strategy.rb]:::core
    EpicsSyncAsWorkItem[Epics::SyncAsWorkItem Concern\nee/app/services/concerns/epics/sync_as_work_item.rb]:::support
    TodosConfidentialEpicService[Todos::Destroy::ConfidentialEpicService\nee/app/services/todos/destroy/confidential_epic_service.rb]:::core

    EpicsCreateService --> EpicsBaseService
    EpicsUpdateService --> EpicsBaseService
    EpicsCloseService --> EpicsBaseService
    EpicsReopenService --> EpicsBaseService
    EpicsIssuePromoteService --> E1
    EpicsTransferService --> E1
    EpicsUpdateDatesService --> E1
    EpicsUpdateDatesService --> EpicsStrategiesStart
    EpicsUpdateDatesService --> EpicsStrategiesDue
    EpicsDescendantCountService --> E1
    EpicsTreeReorderService --> E1
    EpicsSyncAsWorkItem --> E1
    EpicsRelatedEpicLinksDestroy --> RelatedEpicLink
    EpicsBaseService --> EpicsSyncAsWorkItem
    EpicsCreateService --> EpicsSyncAsWorkItem
    EpicsUpdateService --> EpicsSyncAsWorkItem
    EpicsStrategiesStart --> EpicsStrategiesBase
    EpicsStrategiesDue --> EpicsStrategiesBase
    EpicLinksListService --> RelatedEpicLink
    EpicLinksDestroyService --> RelatedEpicLink
    EpicIssuesUpdateService --> EpicIssue
    EpicIssuesDestroyService --> EpicIssue
    TodosConfidentialEpicService --> E1
  end

  %% SERVICES: WORK ITEMS, LEGACY EPIC LINKS
  subgraph Work Items Legacy Epic Links[#F8F8F8]
    direction TB
    style Work Items Legacy Epic Links fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2px,rounded-rectangle

    WI_LE_RelatedList[WorkItems::LegacyEpics::RelatedEpicLinks::ListService\nee/app/services/work_items/legacy_epics/related_epic_links/list_service.rb]:::data
    WI_LE_RelatedDestroy[WorkItems::LegacyEpics::RelatedEpicLinks::DestroyService\nee/app/services/work_items/legacy_epics/related_epic_links/destroy_service.rb]:::data

    WI_LE_RelatedList --> EpicsRelatedEpicLinksDestroy
    WI_LE_RelatedDestroy --> EpicsRelatedEpicLinksDestroy
    WI_LE_RelatedList --> RelatedEpicLink
    WI_LE_RelatedDestroy --> RelatedEpicLink
  end

  %% SYSTEM NOTES
  subgraph System Notes[#F8F8F8]
    direction TB
    style System Notes fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2px,rounded-rectangle
    EpicsService[SystemNotes::EpicsService\nee/app/services/system_notes/epics_service.rb]:::support
    EpicsService --> E1
    EpicsService --> EpicIssue
  end

  %% WORKERS & BACKGROUND TASKS
  subgraph Workers, Jobs, & Scheduled Tasks[#F8F8F8]
    direction TB
    style Workers, Jobs, & Scheduled Tasks fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2px,rounded-rectangle
    NewEpicWorker[NewEpicWorker\nee/app/workers/new_epic_worker.rb]:::support
    UpdateEpicsDatesWorker[Epics::UpdateEpicsDatesWorker\nee/app/workers/epics/update_epics_dates_worker.rb]:::support
    NewEpicIssueWorker[Epics::NewEpicIssueWorker\nee/app/workers/epics/new_epic_issue_worker.rb]:::support
    UpdateCachedMetaWorker[Epics::UpdateCachedMetadataWorker\nee/app/workers/epics/update_cached_metadata_worker.rb]:::support

    NewEpicWorker --> EpicsCreateService
    UpdateEpicsDatesWorker --> EpicsUpdateDatesService
    NewEpicIssueWorker --> EpicIssue
    UpdateCachedMetaWorker --> E1
  end

  %% API ENTITIES & HELPERS
  subgraph API Entities & Elastic[#F8F8F8]
    direction TB
    style API Entities & Elastic fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2px,rounded-rectangle
    EE_API_EpicEntity[EE::API::Entities::Epic\nee/lib/ee/api/entities/epic.rb]:::data
    API_EpicBoardEntity[API::Entities::EpicBoard\nee/lib/api/entities/epic_board.rb]:::data
    API_EpicLinks[API::EpicLinks\nee/lib/api/epic_links.rb]:::support
    API_EpicsHelper[API::Helpers::EpicsHelpers\nee/lib/api/helpers/epics_helpers.rb]:::support
    BanzaiEpicParser[EE::Banzai::ReferenceParser::EpicParser\nee/lib/ee/banzai/reference_parser/epic_parser.rb]:::support
    EE_QuickActionsEpic[EE::Gitlab::QuickActions::EpicActions\nee/lib/ee/gitlab/quick_actions/epic_actions.rb]:::support

    EE_API_EpicEntity --> E1
    API_EpicBoardEntity --> LabelExt
    API_EpicLinks --> E1
    API_EpicLinks --> RelatedEpicLink
    API_EpicsHelper --> E1
    BanzaiEpicParser --> E1
    EE_QuickActionsEpic --> E1
    EE_QuickActionsEpic --> RelatedEpicLink
  end

  %% LIBRARIES: SYNC, TRANSFORM, METRICS, & SEARCH
  subgraph Supporting Domain Libraries[#F8F8F8]
    direction TB
    style Supporting Domain Libraries fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2px,rounded-rectangle
    EpicWorkItemSyncDiff[Gitlab::EpicWorkItemSync::Diff\nee/lib/gitlab/epic_work_item_sync/diff.rb]:::support
    WorkItemsTransformService[Gitlab::WorkItems::LegacyEpics::TransformServiceResponse\nee/lib/gitlab/work_items/legacy_epics/transform_service_response.rb]:::support
    EpicActivityCounter[Gitlab::UsageDataCounters::EpicActivityUniqueCounter\nee/lib/gitlab/usage_data_counters/epic_activity_unique_counter.rb]:::support
    GQLAggEpicsEpicNode[Gitlab::Graphql::Aggregations::Epics::EpicNode\nee/lib/gitlab/graphql/aggregations/epics/epic_node.rb]:::support
    ElasticEpicClassProxy[Elastic::Latest::EpicClassProxy\nee/lib/elastic/latest/epic_class_proxy.rb]:::support
    ElasticEpicInstanceProxy[Elastic::Latest::EpicInstanceProxy\nee/lib/elastic/latest/epic_instance_proxy.rb]:::support
    BulkImportsEpicObjectCreator[BulkImports::EpicObjectCreator\nee/lib/bulk_imports/epic_object_creator.rb]:::support

    EpicWorkItemSyncDiff --> E1
    EpicWorkItemSyncDiff --> RelatedEpicLink
    WorkItemsTransformService --> WorkItemsLegacyEpicsRelatedDestroy
    GQLAggEpicsEpicNode --> E1
    EpicActivityCounter --> E1
    ElasticEpicClassProxy --> E1
    ElasticEpicInstanceProxy --> E1
    BulkImportsEpicObjectCreator --> E1
    BulkImportsEpicObjectCreator --> EpicIssue
  end

  %% DOMAIN DATA STRUCTURES & TRANSFORMATIONS (Epics & Work Items)
  subgraph Data Structures & Transformations[#F8F8F8]
    direction TB
    style Data Structures & Transformations fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2px,rounded-rectangle
    EpicDS[Epics Data Structure]:::data
    IssueDS[Issues Data Structure]:::data
    EpicIssueDS[Epic-Issue Link EpicIssue]:::data
    RelatedEpicLinkDS[Related Epic Link]:::data
    WorkItemDS[Work Items]:::data

    EpicDS -.-> EpicIssueDS
    EpicDS -.-> RelatedEpicLinkDS
    IssueDS -.-> EpicIssueDS
    EpicDS -.-> WorkItemDS
    EpicIssueDS -.->|Sync| WorkItemDS
    RelatedEpicLinkDS -.->|Sync| WorkItemDS

    %% How model files provide implementations:
    E1 --> EpicDS
    EpicIssue --> EpicIssueDS
    RelatedEpicLink --> RelatedEpicLinkDS
    WorkItemPresenter --> WorkItemDS

    %% Transformations
    EpicsSyncAsWorkItem --> WorkItemDS
    EpicWorkItemSyncDiff --> WorkItemDS
    WorkItemsTransformService --> WorkItemDS
    EpicsStrategiesBase --> EpicDS
    EpicsStrategiesStart --> EpicDS
    EpicsStrategiesDue --> EpicDS
  end

  %% BOARDS, LISTS, LABELS
  subgraph Boards & Lists[#F8F8F8]
    direction TB
    style Boards & Lists fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2px,rounded-rectangle
    BoardsEpicsListService[Boards::Epics::ListService\nee/app/services/boards/epics/list_service.rb]:::support
    BoardsEpicBoardsFinder[Boards::EpicBoardsFinder\nee/app/finders/boards/epic_boards_finder.rb]:::support
    LabelEE[EE::Label\nee/app/models/ee/label.rb]:::core

    BoardsEpicsListService --> BoardsEpicBoardsFinder
    BoardsEpicsListService --> EpicsFinder
    BoardsEpicsListService --> LabelEE
    LabelEE --> E1
  end

  %% TESTS & SPECS (shown as last group)
  subgraph Test Layer & Specs[#F8F8F8]
    direction TB
    style Test Layer & Specs fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2px,rounded-rectangle
    EpicModelSpec[epic_spec.rb Model\nee/spec/models/epic_spec.rb]:::support
    EpicGFMAutocompleteSpec[gfm_autocomplete_spec.rb\nee/spec/features/epics/gfm_autocomplete_spec.rb]:::support
    MarkdownFeature[MarkdownFeature Spec Helper\nee/spec/support/helpers/ee/markdown_feature.rb]:::support
    QAEpicResource[QA::EE::Resource::Epic\nqa/qa/ee/resource/epic.rb]:::support
    QAEpicsMgmtSpec[QA Epic Management Spec\nqa/qa/specs/features/ee/browser_ui/2_plan/epic/epics_management_spec.rb]:::support
    QAEpicsWorkItemsSync[QA Epics to Work Items Sync\nqa/qa/specs/features/ee/api/2_plan/epics_to_work_items_sync_spec.rb]:::support

    EpicModelSpec --> E1
    EpicGFMAutocompleteSpec --> E1
    MarkdownFeature --> E1
    QAEpicResource --> E1
    QAEpicsMgmtSpec --> E1
    QAEpicsWorkItemsSync --> WorkItemDS
  end

  %% INTER-SUBGRAPH DEPENDENCIES
  EpicModelSpec -.TestedModel.-> E1
  EpicModelSpec -.TestedModel.-> EE_E1

  %% STYLE CLASSES
  classDef core fill:#D4F1F9,stroke:#8ED3E8,stroke-width:2px,rounded-rectangle;
  classDef support fill:#FFF8DC,stroke:#E5D9A6,stroke-width:2px,rounded-rectangle;
  classDef data fill:#E0F8E0,stroke:#7DC57C,stroke-width:2px,rounded-rectangle;

  %% LEGEND (NOT rendered, here for reference)

  %% Core domain files: pastel blue (#D4F1F9)
  %% Supporting/utility files: pastel yellow (#FFF8DC)
  %% Data structure files: pastel green (#E0F8E0)
  %% Error handling files: pastel red (#FFE4E1) -- not present in above
  %% Initialization/setup files: pastel purple (#E6E6FA)
  %% Logical groupings/subgraphs: very light gray (#F8F8F8) with pastel borders
```