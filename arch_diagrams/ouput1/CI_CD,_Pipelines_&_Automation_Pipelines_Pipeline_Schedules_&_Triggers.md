```mermaid
%% VERTICAL layout
flowchart TB

%% ==== SUBGRAPH: Core Domain Models (Pastel Blue) ====
subgraph CORE_DOMAIN_MODELS["Core Domain Models"]
  direction TB
  style CORE_DOMAIN_MODELS fill:#F8F8F8,stroke:#7EC8E3,stroke-width:2,round=10

  ci_trigger["ci/trigger.rb":::coreFile]:::rounded
  ci_pipeline_schedule["ci/pipeline_schedule.rb":::coreFile]:::rounded
  ci_pipeline_schedule_input["ci/pipeline_schedule_input.rb":::coreFile]:::rounded
  ci_pipeline_schedule_variable["ci/pipeline_schedule_variable.rb":::coreFile]:::rounded
  ci_freeze_period["ci/freeze_period.rb":::coreFile]:::rounded
end

%% ==== SUBGRAPH: Domain Data Structures Pastel Green ====
subgraph DOMAIN_DATA_STRUCTURES["Domain Data Structures"]
  direction TB
  style DOMAIN_DATA_STRUCTURES fill:#F8F8F8,stroke:#98DE9A,stroke-width:2,round=10

  t_pipeline_schedule_type["types/ci/pipeline_schedule_type.rb":::dataFile]:::rounded
  t_pipeline_schedule_status_enum["types/ci/pipeline_schedule_status_enum.rb":::dataFile]:::rounded
end

%% ==== SUBGRAPH: GraphQL Mutations Pastel Blue ====
subgraph GRAPHQL_MUTATIONS["GraphQL API Mutations"]
  direction TB
  style GRAPHQL_MUTATIONS fill:#F8F8F8,stroke:#7EC8E3,stroke-width:2,round=10

  gql_ps_base["mutations/ci/pipeline_schedule/base.rb":::coreFile]:::rounded
  gql_ps_create["mutations/ci/pipeline_schedule/create.rb":::coreFile]:::rounded
  gql_ps_update["mutations/ci/pipeline_schedule/update.rb":::coreFile]:::rounded
  gql_ps_delete["mutations/ci/pipeline_schedule/delete.rb":::coreFile]:::rounded
  gql_ps_play["mutations/ci/pipeline_schedule/play.rb":::coreFile]:::rounded
  gql_ps_take_ownership["mutations/ci/pipeline_schedule/take_ownership.rb":::coreFile]:::rounded

  gql_tr_update["mutations/ci/pipeline_trigger/update.rb":::coreFile]:::rounded
  gql_tr_delete["mutations/ci/pipeline_trigger/delete.rb":::coreFile]:::rounded
end

%% ==== SUBGRAPH: Services - Pipeline Schedules Pastel Yellow ====
subgraph PIPELINE_SCHEDULE_SERVICES["Pipeline Schedule Services"]
  direction TB
  style PIPELINE_SCHEDULE_SERVICES fill:#F8F8F8,stroke:#FFE5A2,stroke-width:2,round=10

  svc_ps_create["pipeline_schedules/create_service.rb":::supportFile]:::rounded
  svc_ps_play["pipeline_schedules/play_service.rb":::supportFile]:::rounded
  svc_ps_take_ownership["pipeline_schedules/take_ownership_service.rb":::supportFile]:::rounded
  svc_ps_update["pipeline_schedules/update_service.rb":::supportFile]:::rounded
  svc_ps_base_save["pipeline_schedules/base_save_service.rb":::supportFile]:::rounded
  svc_ps_vars_create["pipeline_schedules/variables_create_service.rb":::supportFile]:::rounded
  svc_ps_vars_update["pipeline_schedules/variables_update_service.rb":::supportFile]:::rounded
  svc_ps_vars_base["pipeline_schedules/variables_base_save_service.rb":::supportFile]:::rounded
  svc_ps_calc_next_run["pipeline_schedules/calculate_next_run_service.rb":::supportFile]:::rounded
end

%% ==== SUBGRAPH: Services - Pipeline Triggers Pastel Yellow ====
subgraph PIPELINE_TRIGGER_SERVICES["Pipeline Trigger Services"]
  direction TB
  style PIPELINE_TRIGGER_SERVICES fill:#F8F8F8,stroke:#FFE5A2,stroke-width=2,round=10

  svc_tr_expire["pipeline_triggers/expire_service.rb":::supportFile]:::rounded
  svc_tr_update["pipeline_triggers/update_service.rb":::supportFile]:::rounded
end

%% ==== SUBGRAPH: Finders / Query Objects Pastel Yellow ====
subgraph DOMAIN_FINDERS["Finder/Query Objects"]
  direction TB
  style DOMAIN_FINDERS fill:#F8F8F8,stroke:#FFE5A2,stroke-width:2,round=10

  finder_ps["pipeline_schedules_finder.rb":::supportFile]:::rounded
end

%% ==== SUBGRAPH: Helpers Pastel Yellow ====
subgraph DOMAIN_HELPERS["Helpers"]
  direction TB
  style DOMAIN_HELPERS fill:#F8F8F8,stroke:#FFE5A2,stroke-width=2,round=10

  h_routing_ps["routing/pipeline_schedules_helper.rb":::supportFile]:::rounded
  h_ci_ps["ci/pipeline_schedules_helper.rb":::supportFile]:::rounded
end

%% ==== SUBGRAPH: Support & Utility Modules Pastel Yellow ====
subgraph SUPPORT_UTILITY["Support & Utility Modules"]
  direction TB
  style SUPPORT_UTILITY fill:#F8F8F8,stroke:#FFE5A2,stroke-width=2,round=10

  cron_schedulable["concerns/cron_schedulable.rb":::supportFile]:::rounded
end

%% ==== SUBGRAPH: API Interfaces - REST Pastel Blue ====
subgraph DOMAIN_API["REST API Interfaces"]
  direction TB
  style DOMAIN_API fill:#F8F8F8,stroke:#7EC8E3,stroke-width:2,round=10

  api_ps["lib/api/ci/pipeline_schedules.rb":::coreFile]:::rounded
  api_fp["lib/api/freeze_periods.rb":::coreFile]:::rounded
end

%% ==== SUBGRAPH: Integration & Query Layer Pastel Blue ====
subgraph INTEGRATION_QUERY["Integration & Query"]
  direction TB
  style INTEGRATION_QUERY fill:#F8F8F8,stroke:#7EC8E3,stroke-width:2,round=10

  qa_ps["qa/resource/pipeline_schedule.rb":::coreFile]:::rounded
  gql_spec_ps["spec/requests/api/graphql/ci/pipeline_schedules_spec.rb":::coreFile]:::rounded
end

%% ==== SUBGRAPH: Data Migration Pastel Green ====
subgraph DATA_MIGRATION["Data Migration"]
  direction TB
  style DATA_MIGRATION fill:#F8F8F8,stroke:#98DE9A,stroke-width:2,round=10

  migration_ps_var_project_id["lib/gitlab/background_migration/backfill_ci_pipeline_schedule_variables_project_id.rb":::dataFile]:::rounded
end

%% ==== SUBGRAPH: EE Premium Extensions (Pastel Blue) ====
subgraph EE_EXTENSIONS["EE/Premium Extensions"]
  direction TB
  style EE_EXTENSIONS fill:#F8F8F8,stroke:#7EC8E3,stroke-width:2,round=10

  ee_ps["ee/app/models/ee/ci/pipeline_schedule.rb":::coreFile]:::rounded
end

%% ==== SUBGRAPH: Policies Pastel Red for error handling/authorization ====
subgraph DOMAIN_POLICIES["Policies & Authorization"]
  direction TB
  style DOMAIN_POLICIES fill:#F8F8F8,stroke:#FFC9C7,stroke-width:2,round=10

  policy_fp["ci/freeze_period_policy.rb":::errorFile]:::rounded
end

%% ==== Connections and Relations %%

%% MODELS: how core data is composed
ci_pipeline_schedule -->|has-many| ci_pipeline_schedule_input
ci_pipeline_schedule -->|has-many| ci_pipeline_schedule_variable
ci_pipeline_schedule_input -->|belongs-to| ci_pipeline_schedule
ci_pipeline_schedule_variable -->|belongs-to| ci_pipeline_schedule
ci_pipeline_schedule_variable -->|belongs-to| ci_pipeline_schedule_input
ci_pipeline_schedule -->|has-many| ci_trigger
ci_freeze_period -->|belongs-to| ci_pipeline_schedule

%% Model uses cron logic for schedule
ci_pipeline_schedule -- uses --> cron_schedulable

%% DOMAIN DATA STRUCTURES
t_pipeline_schedule_type -- represents --> ci_pipeline_schedule
t_pipeline_schedule_status_enum -- used-by --> ci_pipeline_schedule

%% FINDERS
finder_ps -- queries --> ci_pipeline_schedule
finder_ps -- can query --> ci_pipeline_schedule_input
finder_ps -- can query --> ci_pipeline_schedule_variable

%% HELPERS
h_routing_ps -- generates-routes-for --> ci_pipeline_schedule
h_ci_ps -- builds-ui-data-for --> ci_pipeline_schedule
h_ci_ps -- uses --> h_routing_ps

%% SERVICES > PIPELINE SCHEDULES
svc_ps_create -- persists --> ci_pipeline_schedule
svc_ps_create -- uses --> svc_ps_base_save
svc_ps_play -- uses --> ci_pipeline_schedule
svc_ps_play -- checks/updates --> ci_pipeline_schedule.status
svc_ps_play -- triggers --> svc_ps_calc_next_run
svc_ps_update -- updates --> ci_pipeline_schedule
svc_ps_update -- uses --> svc_ps_base_save

svc_ps_take_ownership -- updates-owner --> ci_pipeline_schedule

svc_ps_vars_create -- creates --> ci_pipeline_schedule_variable
svc_ps_vars_create -- uses --> svc_ps_vars_base
svc_ps_vars_update -- updates --> ci_pipeline_schedule_variable
svc_ps_vars_update -- uses --> svc_ps_vars_base

svc_ps_vars_base -- used-by --> svc_ps_vars_create
svc_ps_vars_base -- used-by --> svc_ps_vars_update

svc_ps_calc_next_run -- calculates --> ci_pipeline_schedule.next_run_at

%% SERVICES > PIPELINE TRIGGERS
svc_tr_expire -- expires --> ci_trigger
svc_tr_update -- updates --> ci_trigger

%% API INTERFACES
api_ps -- exposes-rest-for --> ci_pipeline_schedule
api_ps -- exposes-rest-for --> ci_pipeline_schedule_input
api_ps -- exposes-rest-for --> ci_pipeline_schedule_variable

api_fp -- exposes-rest-for --> ci_freeze_period

%% GRAPHQL MUTATIONS - SCHEDULES
gql_ps_base -- base-for --> gql_ps_create
gql_ps_base -- base-for --> gql_ps_update
gql_ps_base -- base-for --> gql_ps_delete
gql_ps_base -- base-for --> gql_ps_play
gql_ps_base -- base-for --> gql_ps_take_ownership

gql_ps_create -- creates --> ci_pipeline_schedule
gql_ps_update -- updates --> ci_pipeline_schedule
gql_ps_delete -- deletes --> ci_pipeline_schedule
gql_ps_play -- plays --> ci_pipeline_schedule
gql_ps_take_ownership -- updates --> ci_pipeline_schedule

gql_ps_create -- uses --> t_pipeline_schedule_type
gql_ps_update -- uses --> t_pipeline_schedule_type
gql_ps_play -- uses --> t_pipeline_schedule_type
gql_ps_delete -- uses --> t_pipeline_schedule_type
gql_ps_take_ownership -- uses --> t_pipeline_schedule_type

%% GRAPHQL MUTATIONS - TRIGGERS
gql_tr_update -- updates --> ci_trigger
gql_tr_delete -- deletes --> ci_trigger

%% QA AND TEST
qa_ps -- tests --> api_ps
gql_spec_ps -- tests --> gql_ps_create
gql_spec_ps -- tests --> gql_ps_update
gql_spec_ps -- tests --> gql_ps_delete
gql_spec_ps -- tests --> gql_ps_play
gql_spec_ps -- tests --> ci_pipeline_schedule

%% DATA MIGRATION
migration_ps_var_project_id -- migrates --> ci_pipeline_schedule_variable

%% POLICIES / AUTHORIZATION
policy_fp -- governs-access-to --> ci_freeze_period

%% EE EXTENSIONS
ee_ps -- prepends --> ci_pipeline_schedule

%% STYLES
classDef coreFile fill:#D4F1F9,stroke:#D4F1F9,color:#393A54,stroke-width:2,stroke-dasharray: 0 0,rx:8,ry:8
classDef supportFile fill:#FFF8DC,stroke:#FFF8DC,color:#393A54,stroke-width:2,stroke-dasharray: 0 0,rx:8,ry:8
classDef dataFile fill:#E0F8E0,stroke:#E0F8E0,color:#37662e,stroke-width:2,rx:8,ry:8
classDef errorFile fill:#FFE4E1,stroke:#FFE4E1,color:#78333a,stroke-width:2,rx:8,ry:8
classDef rounded stroke-dasharray: 0 0,rx:8,ry:8
```