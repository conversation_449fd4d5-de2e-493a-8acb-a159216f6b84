```mermaid
flowchart TD
%% COLOR DEFS
%% Core domain: #D4F1F9
%% Utility: #FFF8DC
%% Data: #E0F8E0
%% Error: #FFE4E1
%% Setup: #E6E6FA
%% Subgraph: #F8F8F8

%% CORE: CONTROLLING SIDEBAR NAVIGATION/TOC + MENU GROUPINGS

subgraph sgSidebarPanel["Panel & Context Abstractions" ]
direction TB
style sgSidebarPanel fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded=true

panel[/"Sidebars::Panel"/]
context[/"Sidebars::Context"/]

panel -- provides base for --> ywork_panel[/"YourWork::Panel"/]
panel -- provides base for --> projects_panel[/"Projects::Panel"/]
panel -- provides base for --> groups_panel[/"Groups::Panel"/]
panel -- provides base for --> explore_panel[/"Explore::Panel"/]
panel -- provides base for --> admin_panel[/"Admin::Panel"/]
panel -- provides base for --> userProfile_panel[/"UserProfile::Panel"/]
panel -- provides base for --> userSettings_panel[/"UserSettings::Panel"/]
panel -- provides base for --> org_panel[/"Organizations::Panel"/]

context -- base for contextual --> ywork_ctx[/"YourWork::Context"/]
context -- base for contextual --> projects_ctx[/"Projects::Context"/]
context -- base for contextual --> groups_ctx[/"Groups::Context"/]
context -- base for contextual --> org_ctx[/"Organizations::Context"/]

end
style panel fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded=true
style context fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded=true
style ywork_panel fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded=true
style projects_panel fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded=true
style groups_panel fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded=true
style explore_panel fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded=true
style admin_panel fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded=true
style userProfile_panel fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded=true
style userSettings_panel fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded=true
style org_panel fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded=true
style ywork_ctx fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1,rounded=true
style projects_ctx fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1,rounded=true
style groups_ctx fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1,rounded=true
style org_ctx fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1,rounded=true

%% CORE: MENU COMPOSITION

subgraph sgSidebarMenu["Sidebar Menu/Item Abstractions" ]
direction TB
style sgSidebarMenu fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded=true

menu[/"Sidebars::Menu"/]
menuItem[/"Sidebars::MenuItem"/]
staticMenu[/"Sidebars::StaticMenu"/]
nilMenuItem[/"Sidebars::NilMenuItem"/]

menu -- contains/composes --> menuItem
staticMenu -- is a type of --> menu
nilMenuItem -- special case of --> menuItem

menuItem -- serialization --> serialize_menu[|"serialize_for_super_sidebar"|]
menu -- serialization --> serialize_menu
end
style menu fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded=true
style menuItem fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rounded=true
style serialize_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style staticMenu fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1,rounded=true
style nilMenuItem fill:#FFE4E1,stroke:#FFE4E1,stroke-width:1,rounded=true

%% ABSTRACTIONS: MENU CONCERNS/MIXINS

subgraph sgSidebarConcerns["Sidebar Menu Concerns / Features"]
direction TB
style sgSidebarConcerns fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,rounded=true

hasIcon[/"HasIcon"/]
hasPill[/"HasPill"/]
hasPartial[/"HasPartial"/]
hasAvatar[/"HasAvatar"/]
hasActiveRoutes[/"HasActiveRoutes"/]
positionableList[/"PositionableList"/]
renderable[/"Renderable"/]
renderIfLoggedIn[/"RenderIfLoggedIn"/]
containerWithHtml[/"ContainerWithHtmlOptions"/]
linkWithHtml[/"LinkWithHtmlOptions"/]
superSidebarPanel[/"SuperSidebarPanel"/]

menu -- includes --> hasIcon
menu -- includes --> hasPill
menu -- includes --> hasAvatar
menu -- includes --> positionableList
menu -- includes --> renderable

userSettings_menu_class[/"UserSettings::Menus::*"/]
userSettings_menu_class -- includes --> renderIfLoggedIn

hasPartial -- is used by --> menuItem
containerWithHtml -- is used by --> menu
linkWithHtml -- is used by --> menuItem
panel -- includes --> positionableList
superSidebarPanel -- is used by --> superSidebar_panels[/"SuperSidebarPanel Classes"/]
end
style hasIcon fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rounded=true
style hasPill fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rounded=true
style hasPartial fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rounded=true
style hasAvatar fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rounded=true
style hasActiveRoutes fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rounded=true
style positionableList fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rounded=true
style renderable fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rounded=true
style renderIfLoggedIn fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rounded=true
style containerWithHtml fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rounded=true
style linkWithHtml fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rounded=true
style superSidebarPanel fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rounded=true
style userSettings_menu_class fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1,rounded=true
style superSidebar_panels fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1,rounded=true

%% PANEL => MENUS RELATIONS LOGICAL, not procedural

%% YOUR WORK
ywork_panel -- constructs --> yw_menus[/"YourWork::Menus::*"/]
ywork_panel -- uses context --> ywork_ctx
yw_menus -- organize menu --> menu
yw_menus -- includes groupings for --> ywork_proj_menu[/"ProjectsMenu"/]
yw_menus -- includes groupings for --> ywork_groups_menu[/"GroupsMenu"/]
yw_menus -- includes groupings for --> ywork_issues_menu[/"IssuesMenu"/]
yw_menus -- includes groupings for --> ywork_merge_menu[/"MergeRequestsMenu"/]
yw_menus -- includes groupings for --> ywork_todos_menu[/"TodosMenu"/]
yw_menus -- includes groupings for --> ywork_activity_menu[/"ActivityMenu"/]
yw_menus -- includes groupings for --> ywork_snippets_menu[/"SnippetsMenu"/]
yw_menus -- includes groupings for --> yw_org_menu[/"OrganizationsMenu"/]
yw_menus -- includes groupings for --> yw_import_menu[/"ImportHistoryMenu"/]
yw_menus -- includes groupings for --> yw_milestones_menu[/"MilestonesMenu"/]
yw_menus -- includes --> yw_oper_dashboard_menu[/"OperationsDashboardMenu"/]
yw_menus -- includes --> yw_workspaces_menu[/"WorkspacesMenu"/]
yw_menus -- includes --> yw_env_dashboard_menu[/"EnvironmentsDashboardMenu"/]
yw_menus -- includes --> yw_security_dashboard_menu[/"SecurityDashboardMenu"/]

style ywork_panel fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded=true
style yw_menus fill:#D4F1F9,stroke:#E0F8E0,stroke-width:1,rounded=true
style ywork_proj_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style ywork_groups_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style ywork_issues_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style ywork_merge_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style ywork_todos_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style ywork_activity_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style ywork_snippets_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style yw_org_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style yw_import_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style yw_milestones_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style yw_oper_dashboard_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style yw_workspaces_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style yw_env_dashboard_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style yw_security_dashboard_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true

%% PROJECTS
projects_panel -- uses context --> projects_ctx
projects_panel -- constructs --> proj_menus[/"Projects::Menus::*"/]
proj_menus -- organize menu --> menu
proj_menus -- includes many menus --> pr_scope_menu[/"ScopeMenu"/]
proj_menus -- includes --> pr_repo_menu[/"RepositoryMenu"/]
proj_menus -- includes --> pr_merge_menu[/"MergeRequestsMenu"/]
proj_menus -- includes --> pr_issues_menu[/"IssuesMenu"/]
proj_menus -- includes --> pr_workitems_menu[/"WorkItemsMenu"/]
proj_menus -- includes --> pr_deployments_menu[/"DeploymentsMenu"/]
proj_menus -- includes --> pr_monitor_menu[/"MonitorMenu"/]
proj_menus -- includes --> pr_analytics_menu[/"AnalyticsMenu"/]
proj_menus -- includes --> pr_security_compliance_menu[/"SecurityComplianceMenu"/]
proj_menus -- includes --> pr_packages_menu[/"PackagesRegistriesMenu"/]
proj_menus -- includes --> pr_ci_cd_menu[/"CiCdMenu"/]
proj_menus -- includes --> pr_wiki_menu[/"WikiMenu"/]
proj_menus -- includes --> pr_extwiki_menu[/"ExternalWikiMenu"/]
proj_menus -- includes --> pr_confluence_menu[/"ConfluenceMenu"/]
proj_menus -- includes --> pr_hidden_menu[/"HiddenMenu"/]
proj_menus -- includes --> pr_project_info_menu[/"ProjectInformationMenu"/]
proj_menus -- includes --> pr_snippets_menu[/"SnippetsMenu"/]
proj_menus -- includes --> pr_extissue_menu[/"ExternalIssueTrackerMenu"/]
proj_menus -- includes --> pr_infra_menu[/"InfrastructureMenu"/]
proj_menus -- includes --> pr_zentao_menu[/"ZentaoMenu"/]
proj_menus -- includes --> pr_getstarted_menu[/"GetStartedMenu"/]
proj_menus -- includes --> pr_learn_gitlab_menu[/"LearnGitlabMenu"/]

style projects_panel fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded=true
style proj_menus fill:#D4F1F9,stroke:#E0F8E0,stroke-width:1,rounded=true
style pr_scope_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style pr_repo_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style pr_merge_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style pr_issues_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style pr_workitems_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style pr_deployments_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style pr_monitor_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style pr_analytics_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style pr_security_compliance_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style pr_packages_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style pr_ci_cd_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style pr_wiki_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style pr_extwiki_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style pr_confluence_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style pr_hidden_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style pr_project_info_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style pr_snippets_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style pr_extissue_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style pr_infra_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style pr_zentao_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style pr_getstarted_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style pr_learn_gitlab_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true

%% GROUPS
groups_panel -- uses context --> groups_ctx
groups_panel -- constructs --> gp_menus[/"Groups::Menus::*"/]
gp_menus -- organize menu --> menu
gp_menus -- includes --> gp_scope_menu[/"ScopeMenu"/]
gp_menus -- includes --> gp_groupinfo_menu[/"GroupInformationMenu"/]
gp_menus -- includes --> gp_workitems_menu[/"WorkItemsMenu"/]
gp_menus -- includes --> gp_issues_menu[/"IssuesMenu"/]
gp_menus -- includes --> gp_mr_menu[/"MergeRequestsMenu"/]
gp_menus -- includes --> gp_settings_menu[/"SettingsMenu"/]
gp_menus -- includes --> gp_packages_menu[/"PackagesRegistriesMenu"/]
gp_menus -- includes --> gp_customer_rels_menu[/"CustomerRelationsMenu"/]
gp_menus -- includes --> gp_ci_cd_menu[/"CiCdMenu"/]
gp_menus -- includes --> gp_kubernetes_menu[/"KubernetesMenu"/]
gp_menus -- includes --> gp_security_compliance_menu[/"SecurityComplianceMenu"/]

style groups_panel fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rounded=true
style gp_menus fill:#D4F1F9,stroke:#E0F8E0,stroke-width:1,rounded=true
style gp_scope_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style gp_groupinfo_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style gp_workitems_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style gp_issues_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style gp_mr_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style gp_settings_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style gp_packages_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style gp_customer_rels_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style gp_ci_cd_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style gp_kubernetes_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true
style gp_security_compliance_menu fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded=true

%% ADMIN
admin_panel -- constructs --> admin_menus[/"Admin::Menus::*"/]
admin_menus -- organize menu --> menu
admin_menus -- includes --> adm_analytics_menu[/"AnalyticsMenu"/]
admin_menus -- includes --> adm_overview_menu[/"AdminOverviewMenu"/]
admin_menus -- includes --> adm_settings_menu[/"AdminSettingsMenu"/]
admin_menus -- includes --> adm_monitoring_menu[/"MonitoringMenu"/]
admin_menus -- includes --> adm_ci_cd_menu[/"CiCdMenu"/]
admin_menus -- includes --> adm_applications_menu[/"ApplicationsMenu"/]
admin_menus -- includes --> adm_deploy_keys_menu[/"DeployKeysMenu"/]
admin_menus -- includes --> adm_subscription_menu[/"SubscriptionMenu"/]
admin_menus -- includes --> adm_system_hooks_menu[/"SystemHooksMenu"/]
admin_menus -- includes --> adm_labels_menu[/"LabelsMenu"/]
admin_menus -- includes --> adm_abuse_menu[/"AbuseReportsMenu"/]
admin_menus -- includes --> adm_spam_menu[/"SpamLogsMenu"/]
admin_menus -- includes --> adm_messages_menu[/"MessagesMenu"/]
admin_menus -- includes --> adm_push_rules_menu[/"PushRulesMenu"/]
admin_menus -- includes --> adm_geo_menu[/"GeoMenu"/]
admin_menus -- includes --> adm_targeted_menu[/"TargetedMessagesMenu"/]
admin_panel -- uses context --> context

%% ADMIN EE EXTENSIONS
adm_overview_menu -- can be enhanced by --> ee_admin_overview[/"EE::AdminOverviewMenu EE"/]
adm_monitoring_menu -- can be enhanced by --> ee_monitoring_menu[/"EE::MonitoringMenu EE"/]

%% USER PROFILE
userProfile_panel -- constructs --> up_menus[/"UserProfile::Menus::*"/]
up_menus -- organize menu --> menu
up_menus -- includes --> up_overview_menu[/"OverviewMenu"/]
up_menus -- includes --> up_activity_menu[/"ActivityMenu"/]
up_menus -- includes --> up_groups_menu[/"GroupsMenu"/]
up_menus -- includes --> up_followers_menu[/"FollowersMenu"/]
up_menus -- includes --> up_following_menu[/"FollowingMenu"/]
up_menus -- includes --> up_contributed_menu[/"ContributedProjectsMenu"/]
up_menus -- includes --> up_starred_menu[/"StarredProjectsMenu"/]
up_menus -- includes --> up_personal_menu[/"PersonalProjectsMenu"/]
up_menus -- includes --> up_snippets_menu[/"SnippetsMenu"/]

%% USER SETTINGS
userSettings_panel -- constructs --> us_menus[/"UserSettings::Menus::*"/]
us_menus -- organize menu --> menu
us_menus -- includes --> us_profile_menu[/"ProfileMenu"/]
us_menus -- includes --> us_account_menu[/"AccountMenu"/]
us_menus -- includes --> us_password_menu[/"PasswordMenu"/]
us_menus -- includes --> us_sshkeys_menu[/"SshKeysMenu"/]
us_menus -- includes --> us_gpgkeys_menu[/"GpgKeysMenu"/]
us_menus -- includes --> us_applications_menu[/"ApplicationsMenu"/]
us_menus -- includes --> us_active_sessions_menu[/"ActiveSessionsMenu"/]
us_menus -- includes --> us_notifications_menu[/"NotificationsMenu"/]
us_menus -- includes --> us_emails_menu[/"EmailsMenu"/]
us_menus -- includes --> us_usage_quotas_menu[/"UsageQuotasMenu"/]
us_menus -- includes --> us_auth_log_menu[/"AuthenticationLogMenu"/]
us_menus -- includes --> us_preferences_menu[/"PreferencesMenu"/]
us_menus -- includes --> us_chat_menu[/"ChatMenu"/]
us_menus -- includes --> us_comment_templates_menu[/"CommentTemplatesMenu"/]
us_menus -- includes --> us_access_tokens_menu[/"AccessTokensMenu"/]

%% ORGANIZATIONS
org_panel -- uses context --> org_ctx
org_panel -- constructs --> org_menus[/"Organizations::Menus::*"/]
org_menus -- organize menu --> menu
org_menus -- includes --> org_scope_menu[/"ScopeMenu"/]
org_menus -- includes --> org_manage_menu[/"ManageMenu"/]
org_menus -- includes --> org_settings_menu[/"SettingsMenu"/]

%% EXPLORE
explore_panel -- constructs --> explore_menus[/"Explore::Menus::*"/]
explore_menus -- organize menu --> menu
explore_menus -- includes --> ex_proj_menu[/"ProjectsMenu"/]
explore_menus -- includes --> ex_groups_menu[/"GroupsMenu"/]
explore_menus -- includes --> ex_catalog_menu[/"CatalogMenu"/]
explore_menus -- includes --> ex_snippets_menu[/"SnippetsMenu"/]
explore_menus -- includes --> ex_topics_menu[/"TopicsMenu"/]

%% SEARCH NAVIGATION
search_nav[/"Search::Navigation"/]
search_nav -- provides Tab Rules to --> explore_panel
style search_nav fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1,rounded=true

%% SUPER SIDEBAR PANELS
superSidebarPanel -- extends --> super_projects_panel[/"Projects::SuperSidebarPanel"/]
superSidebarPanel -- extends --> super_groups_panel[/"Groups::SuperSidebarPanel"/]
superSidebarPanel -- extends --> super_org_panel[/"Organizations::SuperSidebarPanel"/]

super_projects_panel -- enhances --> projects_panel
super_groups_panel -- enhances --> groups_panel
super_org_panel -- enhances --> org_panel

%% SUPER SIDEBAR MENU GROUPS
subgraph sgSuperMenus["Super Sidebar Menus" ]
direction TB
style sgSuperMenus fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2,rounded=true

ppm_code[/"Projects::SuperSidebarMenus::CodeMenu"/]
ppm_plan[/"Projects::SuperSidebarMenus::PlanMenu"/]
ppm_manage[/"Projects::SuperSidebarMenus::ManageMenu"/]
ppm_build[/"Projects::SuperSidebarMenus::BuildMenu"/]
ppm_deploy[/"Projects::SuperSidebarMenus::DeployMenu"/]
ppm_secure[/"Projects::SuperSidebarMenus::SecureMenu"/]
ppm_analyze[/"Projects::SuperSidebarMenus::AnalyzeMenu"/]
ppm_operations[/"Projects::SuperSidebarMenus::OperationsMenu"/]
ppm_monitor[/"Projects::SuperSidebarMenus::MonitorMenu"/]

gpm_code[/"Groups::SuperSidebarMenus::CodeMenu"/]
gpm_plan[/"Groups::SuperSidebarMenus::PlanMenu"/]
gpm_manage[/"Groups::SuperSidebarMenus::ManageMenu"/]
gpm_build[/"Groups::SuperSidebarMenus::BuildMenu"/]
gpm_deploy[/"Groups::SuperSidebarMenus::DeployMenu"/]
gpm_secure[/"Groups::SuperSidebarMenus::SecureMenu"/]
gpm_analyze[/"Groups::SuperSidebarMenus::AnalyzeMenu"/]
gpm_operations[/"Groups::SuperSidebarMenus::OperationsMenu"/]

ppm_code -- grouped by --> super_projects_panel
ppm_plan -- grouped by --> super_projects_panel
ppm_manage -- grouped by --> super_projects_panel
ppm_build -- grouped by --> super_projects_panel
ppm_deploy -- grouped by --> super_projects_panel
ppm_secure -- grouped by --> super_projects_panel
ppm_analyze -- grouped by --> super_projects_panel
ppm_operations -- grouped by --> super_projects_panel
ppm_monitor -- grouped by --> super_projects_panel

gpm_code -- grouped by --> super_groups_panel
gpm_plan -- grouped by --> super_groups_panel
gpm_manage -- grouped by --> super_groups_panel
gpm_build -- grouped by --> super_groups_panel
gpm_deploy -- grouped by --> super_groups_panel
gpm_secure -- grouped by --> super_groups_panel
gpm_analyze -- grouped by --> super_groups_panel
gpm_operations -- grouped by --> super_groups_panel

end
style ppm_code fill:#E6E6FA,stroke:#D4F1F9,stroke-width:1,rounded=true
style ppm_plan fill:#E6E6FA,stroke:#D4F1F9,stroke-width:1,rounded=true
style ppm_manage fill:#E6E6FA,stroke:#D4F1F9,stroke-width:1,rounded=true
style ppm_build fill:#E6E6FA,stroke:#D4F1F9,stroke-width:1,rounded=true
style ppm_deploy fill:#E6E6FA,stroke:#D4F1F9,stroke-width:1,rounded=true
style ppm_secure fill:#E6E6FA,stroke:#D4F1F9,stroke-width:1,rounded=true
style ppm_analyze fill:#E6E6FA,stroke:#D4F1F9,stroke-width:1,rounded=true
style ppm_operations fill:#E6E6FA,stroke:#D4F1F9,stroke-width:1,rounded=true
style ppm_monitor fill:#E6E6FA,stroke:#D4F1F9,stroke-width:1,rounded=true

style gpm_code fill:#E6E6FA,stroke:#D4F1F9,stroke-width:1,rounded=true
style gpm_plan fill:#E6E6FA,stroke:#D4F1F9,stroke-width:1,rounded=true
style gpm_manage fill:#E6E6FA,stroke:#D4F1F9,stroke-width:1,rounded=true
style gpm_build fill:#E6E6FA,stroke:#D4F1F9,stroke-width:1,rounded=true
style gpm_deploy fill:#E6E6FA,stroke:#D4F1F9,stroke-width:1,rounded=true
style gpm_secure fill:#E6E6FA,stroke:#D4F1F9,stroke-width:1,rounded=true
style gpm_analyze fill:#E6E6FA,stroke:#D4F1F9,stroke-width:1,rounded=true
style gpm_operations fill:#E6E6FA,stroke:#D4F1F9,stroke-width:1,rounded=true

%% SUPPORT: BREADCRUMBS & NAVHELPER

breadcrumbs_helper[/"BreadcrumbsHelper"/]
sidebars_helper[/"SidebarsHelper"/]
nav_helper[/"NavHelper"/]
nav_newdropdown_helper[/"Nav::NewDropdownHelper"/]
ee_nav_helper[/"EE::NavHelper"/]

ywork_panel -- uses/helped by --> sidebars_helper
projects_panel -- uses/helped by --> sidebars_helper
groups_panel -- uses/helped by --> sidebars_helper
breadcrumbs_helper -- provides navigation to --> ywork_panel
breadcrumbs_helper -- provides navigation to --> projects_panel
breadcrumbs_helper -- provides navigation to --> groups_panel
nav_helper -- provides css & layout for --> ywork_panel
nav_helper -- provides css & layout for --> projects_panel
nav_helper -- provides css & layout for --> groups_panel
ee_nav_helper -- extends --> nav_helper
nav_newdropdown_helper -- creates menu items for --> ywork_panel
nav_newdropdown_helper -- creates menu items for --> projects_panel

style breadcrumbs_helper fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rounded=true
style sidebars_helper fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rounded=true
style nav_helper fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rounded=true
style nav_newdropdown_helper fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rounded=true
style ee_nav_helper fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rounded=true

%% DATA COMPONENTS / STRUCTURES

sidebars_menu_data[/"serialize_for_super_sidebar Menu/MenuItem Structure"/]
menuItem -- serialization produces --> sidebars_menu_data
menu -- serialization produces --> sidebars_menu_data
staticMenu -- serialization produces --> sidebars_menu_data

style sidebars_menu_data fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rounded=true

%% QA/MANUAL-TEST: SIDEBAR NAV

qa_side_nav[/"QA::Page::*::Menu"/]
qa_side_nav -- tests/integrates --> ywork_panel
qa_side_nav -- tests/integrates --> projects_panel
qa_side_nav -- tests/integrates --> groups_panel
qa_side_nav -- tests/integrates --> userProfile_panel
qa_side_nav -- tests/integrates --> userSettings_panel

style qa_side_nav fill:#FFF8DC,stroke:#FFF8DC,stroke-width:1,rounded=true

%% E2E / SPEC FEATURES for NAVIGATION

rs_dashboard_shortcuts[/"Dashboard Shortcuts Feature Spec"/]
rs_dashboard_shortcuts -- asserts nav behaviors --> ywork_panel
rs_dashboard_shortcuts -- asserts nav behaviors --> projects_panel
rs_dashboard_shortcuts -- asserts nav behaviors --> groups_panel

style rs_dashboard_shortcuts fill:#FFF8DC,stroke:#FFF8DC,stroke-width:1,rounded=true

%% EE EXTENSIONS TO MENUS & PANELS

ee_yourwork_panel[/"EE::Sidebars::YourWork::Panel"/]
ee_projects_panel[/"EE::Sidebars::Projects::Panel"/]
ee_groups_panel[/"EE::Sidebars::Groups::Panel"/]
ee_admin_panel[/"EE::Sidebars::Admin::Panel"/]
ee_usersettings_panel[/"EE::Sidebars::UserSettings::Panel"/]

ee_yourwork_panel -- extends --> ywork_panel
ee_projects_panel -- extends --> projects_panel
ee_groups_panel -- extends --> groups_panel
ee_admin_panel -- extends --> admin_panel
ee_usersettings_panel -- extends --> userSettings_panel

style ee_yourwork_panel fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1,rounded=true
style ee_projects_panel fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1,rounded=true
style ee_groups_panel fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1,rounded=true
style ee_admin_panel fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1,rounded=true
style ee_usersettings_panel fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1,rounded=true

%% DOMAIN GOALS AND FLOW

graph_root{{"Sidebar Navigation & Menus Domain"}}
style graph_root fill:#D4F1F9,stroke:#D4F1F9,stroke-width:4,rounded=true

graph_root -- provides navigation context for --> sgSidebarPanel
graph_root -- provides navigation context for --> sgSidebarMenu
graph_root -- provides navigation context for --> sgSidebarConcerns
graph_root -- provides navigation context for --> sgSuperMenus
graph_root -- enables --> sidebars_menu_data
graph_root -- provides extension points to --> ee_yourwork_panel
graph_root -- provides extension points to --> ee_projects_panel
graph_root -- provides extension points to --> ee_groups_panel
graph_root -- provides extension points to --> ee_admin_panel
graph_root -- provides extension points to --> ee_usersettings_panel
graph_root -- interacts via view helpers with --> sidebars_helper
graph_root -- supports --> qa_side_nav
graph_root -- is tested by --> rs_dashboard_shortcuts
graph_root -- collaborates with --> breadcrumbs_helper
graph_root -- integrates layout/UX with --> nav_helper
graph_root -- structures navigation for --> search_nav
graph_root -- configures menu data --> sidebars_menu_data

%% LEGACY/STRUCTURAL - UNCATEGORIZED/SEARCH/MISC

uncategorized_menu[/"UncategorizedMenu"/]
staticMenu -- can represent --> uncategorized_menu

search_panel[/"Search::Panel"/]
search_panel -- specializes --> panel
search_panel -- supports --> search_nav

style uncategorized_menu fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1,rounded=true
style search_panel fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1,rounded=true
```