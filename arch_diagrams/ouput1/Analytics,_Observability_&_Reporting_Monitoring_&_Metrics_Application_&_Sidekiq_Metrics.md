```mermaid
%% VERTICAL LAYOUT: Application & Sidekiq Metrics - Analytics, Observability & Reporting/Monitoring & Metrics Domain

flowchart TD
  %% --- SUBGRAPHS (LOGICAL GROUPS) ---
  %% 1. CONTROLLERS & API
  subgraph CONTROLLERS_API["Controllers & API" ]
    direction TB
    style CONTROLLERS_API fill:#F8F8F8,stroke:#80BFFF,stroke-width:2,border-radius:10px

    MC[/"metrics_controller.rb\nServes monitoring/metrics-related HTTP actions"/]
    style MC fill:#D4F1F9,stroke:#7FC6E3,stroke-width:2

    DC[/"deployments_controller.rb\nProvides deployment and metrics endpoints"/]
    style DC fill:#D4F1F9,stroke:#7FC6E3,stroke-width:2

    SQA["sidekiq_metrics.rb\nAPI for Sidekiq metrics, exposes job/queue/process metrics" ]
    style SQA fill:#D4F1F9,stroke:#7FC6E3,stroke-width:2
  end

  %% 2. GRAPHQL & POLICIES
  subgraph GRAPHQL_POLICY["GraphQL & Policies"]
    direction TB
    style GRAPHQL_POLICY fill:#F8F8F8,stroke:#80BFFF,stroke-width:2,border-radius:10px

    TRRES["traces_resolver.rb\nGraphQL resolver for observability tracing"]
    style TRRES fill:#D4F1F9,stroke:#7FC6E3,stroke-width:2

    LICP["logs_issues_connection_policy.rb\nPolicy: Access to logs-issues for observability"]
    style LICP fill:#D4F1F9,stroke:#7FC6E3,stroke-width:2
  end

  %% 3. HELPERS ISSUES/OBSERVABILITY
  subgraph HELPERS["Observability Issue Helpers"]
    direction TB
    style HELPERS fill:#F8F8F8,stroke:#FCEABE,stroke-width:2,border-radius:10px

    MIH["metrics_issues_helper.rb\nBuilds params for metrics-based issue creation"]
    style MIH fill:#FFF8DC,stroke:#FFE699,stroke-width:2

    TIH["tracing_issues_helper.rb\nBuilds params for trace-based issue creation"]
    style TIH fill:#FFF8DC,stroke:#FFE699,stroke-width:2

    LIH["logs_issues_helper.rb\nBuilds params for log-based issue creation"]
    style LIH fill:#FFF8DC,stroke:#FFE699,stroke-width:2
  end

  %% 4. METRICS CORE & SLIS
  subgraph METRICS_CORE["Metrics Core & SLI modules"]
    direction TB
    style METRICS_CORE fill:#F8F8F8,stroke:#80BFFF,stroke-width:2,border-radius:10px

    GMC["metrics.rb\nMain entry for application metrics & config"]
    style GMC fill:#D4F1F9,stroke:#7FC6E3,stroke-width:2

    RLS["rails_slis.rb\nService Level Indicators for Rails & GraphQL"]
    style RLS fill:#D4F1F9,stroke:#7FC6E3,stroke-width:2

    GSSLIS["global_search_slis.rb\nSLIs for search performance tracking"]
    style GSSLIS fill:#D4F1F9,stroke:#7FC6E3,stroke-width:2

    GSI["global_search_indexing_slis.rb\nSLIs for global indexing"]
    style GSI fill:#D4F1F9,stroke:#7FC6E3,stroke-width:2

    LLMSLI["llm_chat_first_token.rb\nSLIs for LLM response latency"]
    style LLMSLI fill:#D4F1F9,stroke:#7FC6E3,stroke-width:2

    SKS["sidekiq_slis.rb\nSidekiq job SLIs exec, queue, errors"]
    style SKS fill:#D4F1F9,stroke:#7FC6E3,stroke-width:2

    DBSLI["database_transaction_slis.rb\nDB transaction latency SLIs"]
    style DBSLI fill:#D4F1F9,stroke:#7FC6E3,stroke-width:2

    SEC_SC_SLI["security_scan_slis.rb\nSLIs for security scanning"]
    style SEC_SC_SLI fill:#D4F1F9,stroke:#7FC6E3,stroke-width:2

    GLQL["glql_slis.rb\nSLIs for GitLab GraphQL"]
    style GLQL fill:#D4F1F9,stroke:#7FC6E3,stroke-width:2

    LFS["lfs.rb\nSLIs for Large File Storage"]
    style LFS fill:#D4F1F9,stroke:#7FC6E3,stroke-width:2
  end

  %% 5. METRICS EXPORTERS & MIDDLEWARE
  subgraph EXPORTERS_MW["Exporters & Middleware"]
    direction TB
    style EXPORTERS_MW fill:#F8F8F8,stroke:#C19CEB,stroke-width:2,border-radius:10px

    WEBEXP["web_exporter.rb\nExposes app metrics for Prometheus web"]
    style WEBEXP fill:#E6E6FA,stroke:#C19CEB,stroke-width:2

    SKEXP["sidekiq_exporter.rb\nExposes Sidekiq metrics for Prometheus"]
    style SKEXP fill:#E6E6FA,stroke:#C19CEB,stroke-width:2

    GCEXP["gc_request_middleware.rb\nTriggers GC on certain metric endpoints"]
    style GCEXP fill:#E6E6FA,stroke:#C19CEB,stroke-width:2

    RMID["rack_middleware.rb\nMain metrics Rack middleware requests"]
    style RMID fill:#E6E6FA,stroke:#C19CEB,stroke-width:2

    ERMID["elasticsearch_rack_middleware.rb\nMiddleware for Elasticsearch request metrics"]
    style ERMID fill:#E6E6FA,stroke:#C19CEB,stroke-width:2

    PTCK["path_traversal_check.rb\nMeasures requests for path-traversal SLIs"]
    style PTCK fill:#E6E6FA,stroke:#C19CEB,stroke-width:2
  end

  %% 6. METRICS SUBSCRIBERS LOW-LEVEL EVENTS
  subgraph SUBSCRIBERS["ActiveSupport Instrumented Subscribers"]
    direction TB
    style SUBSCRIBERS fill:#F8F8F8,stroke:#80BFFF,stroke-width:2,border-radius:10px

    AVSUB["action_view.rb\nTracks timings for view rendering"]
    style AVSUB fill:#D4F1F9,stroke:#7FC6E3,stroke-width:2

    ACSUB["active_record.rb\nDB query timings & counts"]
    style ACSUB fill:#D4F1F9,stroke:#7FC6E3,stroke-width:2

    ECSUB["external_http.rb\nExternal HTTP request timings & errors"]
    style ECSUB fill:#D4F1F9,stroke:#7FC6E3,stroke-width:2

    ACCSUB["action_cable.rb\nTracks ActionCable events channels"]
    style ACCSUB fill:#D4F1F9,stroke:#7FC6E3,stroke-width:2

    RCSUB["rails_cache.rb\nTracks cache events read/write/miss"]
    style RCSUB fill:#D4F1F9,stroke:#7FC6E3,stroke-width:2
  end

  %% 7. METRICS SAMPLERS
  subgraph SAMPLERS["Metrics Samplers Daemon Background"]
    direction TB
    style SAMPLERS fill:#F8F8F8,stroke:#A3EDBA,stroke-width:2,border-radius:10px

    BSMPL["base_sampler.rb\nSampler abstraction - schedule/sample loop"]
    style BSMPL fill:#E0F8E0,stroke:#A3EDBA,stroke-width:2

    ACTSMPL["action_cable_sampler.rb\nSamples ActionCable metrics periodically"]
    style ACTSMPL fill:#E0F8E0,stroke:#A3EDBA,stroke-width:2

    CCOSMPL["concurrency_limit_sampler.rb\nSamples worker concurrency/queueing"]
    style CCOSMPL fill:#E0F8E0,stroke:#A3EDBA,stroke-width:2

    DBSMPL["database_sampler.rb\nSamples DB connection pool stats"]
    style DBSMPL fill:#E0F8E0,stroke:#A3EDBA,stroke-width:2

    PSAMPL["puma_sampler.rb\nSamples Puma process/thread stats"]
    style PSAMPL fill:#E0F8E0,stroke:#A3EDBA,stroke-width:2

    STACTSMPL["stat_activity_sampler.rb\nSamples PostgreSQL activity stats"]
    style STACTSMPL fill:#E0F8E0,stroke:#A3EDBA,stroke-width:2

    THSMPL["threads_sampler.rb\nSamples thread metrics Puma/Sidekiq"]
    style THSMPL fill:#E0F8E0,stroke:#A3EDBA,stroke-width:2
  end

  %% 8. METRICS CORE INFRASTRUCTURE
  subgraph INFRA["Metrics Core Infrastructure"]
    direction TB
    style INFRA fill:#F8F8F8,stroke:#D9E7CA,stroke-width:2,border-radius:10px

    TRAN["transaction.rb\nMetrics for generic transactional units"]
    style TRAN fill:#E0F8E0,stroke:#A3EDBA,stroke-width:2

    SYS["system.rb\nSystem/OS-level metric helpers"]
    style SYS fill:#E0F8E0,stroke:#A3EDBA,stroke-width:2

    ENVIRON["environment.rb\nDetect metrics context web/api/git"]
    style ENVIRON fill:#E0F8E0,stroke:#A3EDBA,stroke-width:2

    MMEM["memory.rb\nMeasures Ruby memory usage heap, GC"]
    style MMEM fill:#E0F8E0,stroke:#A3EDBA,stroke-width:2

    DLT["delta.rb\nTracks change/delta of metric values"]
    style DLT fill:#E0F8E0,stroke:#A3EDBA,stroke-width:2

    BTT["boot_time_tracker.rb\nCaptures process boot/startup time"]
    style BTT fill:#E0F8E0,stroke:#A3EDBA,stroke-width:2

    RUNTIME["runtime_limiter.rb\nEnforces max runtime constraints"]
    style RUNTIME fill:#E0F8E0,stroke:#A3EDBA,stroke-width:2

    NMET["null_metric.rb\nNull metric object for disabled metrics"]
    style NMET fill:#E0F8E0,stroke:#A3EDBA,stroke-width:2
  end

  %% 9. ELASTICSEARCH INDEXING/METRICS
  subgraph ELASTIC["Elasticsearch Monitoring/Support"]
    direction TB
    style ELASTIC fill:#F8F8F8,stroke:#FCEABE,stroke-width:2,border-radius:10px

    ENIN["index_name.rb\nCalculates index names used for metrics"]
    style ENIN fill:#FFF8DC,stroke:#FFE699,stroke-width:2

    GBULK["bulk_indexer.rb\nBulk operation metrics for ES"]
    style GBULK fill:#FFF8DC,stroke:#FFE699,stroke-width:2

    NOTE_CONF["note_config.rb\nNote-specific ES index config"]
    style NOTE_CONF fill:#FFF8DC,stroke:#FFE699,stroke-width:2
  end

  %% 10. PRELOADERS/OPTIMIZERS & SUPPORT
  subgraph CORE_SUPPORT["Core Support/Preloaders"]
    direction TB
    style CORE_SUPPORT fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,border-radius:10px

    PROTEN["protected_environment_preloader.rb\nOptimizes env loading for metrics"]
    style PROTEN fill:#FFF8DC,stroke:#FFE699,stroke-width:2
  end

  %% 11. PERFORMANCE BAR/PEEK DEV OBSERVABILITY
  subgraph PBAR["Performance Bar - Peek Instrumentation"]
    direction TB
    style PBAR fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2,border-radius:10px

    PBRDISK["redis_adapter_when_peek_enabled.rb\nPeek-aware, queues stats job for Redis"]
    style PBRDISK fill:#E0F8E0,stroke:#A3EDBA,stroke-width:2

    PBSTAT["stats.rb\nFetches/logs Peek stats from Redis"]
    style PBSTAT fill:#E0F8E0,stroke:#A3EDBA,stroke-width:2

    PEAKAR["active_record.rb\nPeek: AR query timings for dev insights"]
    style PEAKAR fill:#E0F8E0,stroke:#A3EDBA,stroke-width:2

    PEAKHT["external_http.rb\nPeek: external HTTP timings for dev"]
    style PEAKHT fill:#E0F8E0,stroke:#A3EDBA,stroke-width:2

    PEAKRD["redis_detailed.rb\nPeek: detailed Redis access for dev"]
    style PEAKRD fill:#E0F8E0,stroke:#A3EDBA,stroke-width:2

    PKTRC["tracing.rb\nPeek: tracing context for dev"]
    style PKTRC fill:#E0F8E0,stroke:#A3EDBA,stroke-width:2
  end

  %% 12. HEALTH CHECK LAYER
  subgraph HEALTH["Health Checks"]
    direction TB
    style HEALTH fill:#F8F8F8,stroke:#FFE4E1,stroke-width:2,border-radius:10px

    DBCHK["db_check.rb\nChecks DB for liveness/metrics"]
    style DBCHK fill:#FFE4E1,stroke:#FFC2B3,stroke-width:2

    REDCHK["redis.rb\nChecks Redis for liveness/metrics"]
    style REDCHK fill:#FFE4E1,stroke:#FFC2B3,stroke-width:2

    MCHK["metric.rb\nHealthcheck base/metric integration"]
    style MCHK fill:#FFE4E1,stroke:#FFC2B3,stroke-width:2

    BASECHK["base_abstract_check.rb\nBase interface for all health checks"]
    style BASECHK fill:#FFE4E1,stroke:#FFC2B3,stroke-width:2

    SACHK["simple_abstract_check.rb\nShared for simple derived checks"]
    style SACHK fill:#FFE4E1,stroke:#FFC2B3,stroke-width:2

    SRVCHK["server.rb\nHealth check runner daemon/server"]
    style SRVCHK fill:#FFE4E1,stroke:#FFC2B3,stroke-width:2

    RABCHK["redis_abstract_check.rb\nBase for Redis health checks"]
    style RABCHK fill:#FFE4E1,stroke:#FFC2B3,stroke-width:2

    PMCHK["puma_check.rb\nPuma-specific health check"]
    style PMCHK fill:#FFE4E1,stroke:#FFC2B3,stroke-width:2

    PRBSTS["probes/status.rb\nStruct: health probe status representation"]
    style PRBSTS fill:#FFE4E1,stroke:#FFC2B3,stroke-width:2

    MSTRCHK["master_check.rb\nCheck for clustered Puma master"]
    style MSTRCHK fill:#FFE4E1,stroke:#FFC2B3,stroke-width:2

    RSLT["result.rb\nStruct: health check results"]
    style RSLT fill:#FFE4E1,stroke:#FFC2B3,stroke-width:2

    GITCHK["gitaly_check.rb\nGitaly git-storage health check"]
    style GITCHK fill:#FFE4E1,stroke:#FFC2B3,stroke-width:2
  end

  %% --- DOMAIN-PATTERN/FILE RELATIONSHIPS ---
  %% 1. Controllers <-> Metrics Core
  MC --> GMC
  MC --> MIH
  MC --> LIH
  
  DC --> GMC
  DC --> MIH

  SQA --> SKS

  %% 2. Policy encforces access for observability features
  LICP ---> LIH

  %% 3. GraphQL Resolves Observability concepts
  TRRES --> GMC
  TRRES --> TIH

  %% 4. Observability Issue Helpers: Compose params -> consumed by controllers
  MIH -.-> MC
  MIH -.-> DC
  MIH -.-> GMC

  TIH -.-> MC
  TIH -.-> TRRES

  LIH -.-> MC
  LIH -.-> LICP

  %% 5. Metrics Core <-> SLIs/Config Layer
  GMC -- configures --> RLS
  GMC -- loads --> SKS
  GMC -- configures --> GSI
  GMC -- configures --> GSSLIS
  GMC -- configures --> GLQL
  GMC -- configures --> LFS
  GMC -- configures --> LLMSLI
  GMC -- configures --> DBSLI
  GMC -- configures --> SEC_SC_SLI

  %% 6. Exporters and Middleware
  WEBEXP --> GMC
  SKEXP --> SKS
  GCEXP --> RMID
  RMID --> GMC
  ERMID --> GMC
  PTCK --> GMC

  %% 7. Metrics Subscribers <-> Metrics Core
  AVSUB --> GMC
  ACSUB --> GMC
  ECSUB --> GMC
  ACCSUB --> GMC
  RCSUB --> GMC

  %% 8. Metrics Samplers extend BaseSampler, push data -> Metrics Core
  ACTSMPL --> BSMPL
  CCOSMPL --> BSMPL
  DBSMPL --> BSMPL
  PSAMPL --> BSMPL
  STACTSMPL --> BSMPL
  THSMPL --> BSMPL

  BSMPL --> GMC
  ACTSMPL --> GMC
  CCOSMPL --> GMC
  DBSMPL --> GMC
  PSAMPL --> GMC
  STACTSMPL --> GMC
  THSMPL --> GMC

  %% 9. Core Infrastructure is used by SLIs, Samplers, Middleware
  TRAN --> GMC
  SYS --> GMC
  RUNTIME --> GMC
  NMET --> GMC
  ENVIRON --> GMC
  MMEM --> GMC
  DLT --> GMC
  BTT --> GMC

  %% 10. ElasticSearch Support
  ENIN --> GSI
  GBULK --> GSI
  NOTE_CONF --> GSI

  %% 11. Preloader
  PROTEN --> DC

  %% 12. Performance Bar/Peek - dev observability tooling
  PBRDISK --> PBSTAT
  PBSTAT --> GMC
  PEAKAR --> PBSTAT
  PEAKHT --> PBSTAT
  PEAKRD --> PBSTAT
  PKTRC --> PBSTAT

  PEAKAR --> ACSUB
  PEAKHT --> ECSUB
  PEAKRD --> ACCSUB
  PKTRC --> TRRES

  %% 13. Health Checks
  DBCHK --> SACHK
  DBCHK --> MCHK
  REDCHK --> RABCHK
  REDCHK --> SACHK
  MCHK --> GMC
  BASECHK --> SACHK
  BASECHK --> DBCHK
  SACHK --> DBCHK
  SACHK --> PMCHK
  SACHK --> MSTRCHK
  RABCHK --> REDCHK
  SRVCHK --> GMC
  PMCHK --> SACHK
  PRBSTS --> SRVCHK
  MSTRCHK --> SACHK
  GITCHK --> BASECHK
  RSLT --> SACHK

  %% --- Cross-layer or Data Transformation Flows (Dashed) ---
  SQA -.-> SKEXP
  MC -.-> WEBEXP

  %% Highlight important SLI/metrics data transformations
  SKS --> GMC
  RLS --> GMC
  GSSLIS --> GMC
  GSI --> GMC

  %% Data from Samplers/Subscribers feeds both Exporters and Metrics Core
  BSMPL -.-> WEBEXP
  ECSUB -.-> WEBEXP
  AVSUB -.-> WEBEXP
  ACSUB -.-> WEBEXP

  %% Elastic indexing passes through Metrics/SLI layers
  GBULK -.-> GSI
  NOTE_CONF -.-> ENIN

  %% Preloader optimizes data structure input for Controllers
  PROTEN -.-> MC

  %% Performance Bar (dev logging) consumes/augments data from Metrics Core
  PBSTAT --structured logs--> GMC

  %% Subgraph connectors
  CONTROLLERS_API --> METRICS_CORE
  GRAPHQL_POLICY --> METRICS_CORE
  HELPERS -.-> METRICS_CORE
  METRICS_CORE --> SUBSCRIBERS
  METRICS_CORE --> SAMPLERS
  SUBSCRIBERS -.-> EXPORTERS_MW
  SAMPLERS -.-> EXPORTERS_MW
  METRICS_CORE --> INFRA
  INFRA -.-> EXPORTERS_MW
  METRICS_CORE --> HEALTH
  METRICS_CORE --> PBAR
  METRICS_CORE --> ELASTIC
  METRICS_CORE --> CORE_SUPPORT
```