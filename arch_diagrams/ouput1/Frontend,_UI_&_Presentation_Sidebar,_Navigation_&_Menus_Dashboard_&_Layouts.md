```mermaid
flowchart TD
  %% DOMAIN: Frontend, UI & Presentation / Sidebar, Navigation & Menus / Dashboard & Layouts
  %% STYLES
  %% Core domain files: pastel blue (#D4F1F9)
  %% Supporting/utility files: pastel yellow (#FFF8DC)
  %% Data structure files: pastel green (#E0F8E0)
  %% Error handling files: pastel red (#FFE4E1)
  %% Initialization/setup files: pastel purple (#E6E6FA)
  %% Subgraph backgrounds: very light gray (#F8F8F8)

  %% ===============================
  %% SIDEBAR & NAVIGATION COMPONENTS
  %% ===============================
  subgraph Sidebar_Navigation_Components[Sidebar & Navigation Components]
    direction TB
    style Sidebar_Navigation_Components fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rx:18,ry:18
    ActionCardC["ActionCardComponent<br>onboarding/action_card_component.rb"]:::corefile
    HorizontalSectionC["HorizontalSectionComponent<br>layouts/horizontal_section_component.rb"]:::corefile
  end
  classDef corefile fill:#D4F1F9,stroke:#2196F3,stroke-width:1.5px,color:#0B294B,stroke-dasharray: "0"
  class ActionCardC,HorizontalSectionC corefile

  %% =======================
  %% DASHBOARD CONTROLLERS
  %% =======================
  subgraph Dashboard_Controllers[Dashboard & Layout Controllers]
    direction TB
    style Dashboard_Controllers fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rx:18,ry:18
    RootController["RootController<br>controllers/root_controller.rb"]:::corefile
    IdeController["IdeController<br>controllers/ide_controller.rb"]:::corefile
    SandboxController["SandboxController<br>controllers/sandbox_controller.rb"]:::corefile
    PwaController["PwaController<br>controllers/pwa_controller.rb"]:::corefile
    DashboardApplicationController["Dashboard::ApplicationController<br>controllers/dashboard/application_controller.rb"]:::corefile
  end

  classDef supportfile fill:#FFF8DC,stroke:#D3B95E,stroke-width:1.5px,color:#786215,stroke-dasharray: "0"
  classDef errorfile fill:#FFE4E1,stroke:#DE7362,stroke-width:1.5px,color:#A0160A,stroke-dasharray: "0"
  classDef setupfile fill:#E6E6FA,stroke:#A691D3,stroke-width:1.5px,color:#392F5A,stroke-dasharray: "0"
  classDef datafile fill:#E0F8E0,stroke:#45C478,stroke-width:1.5px,color:#086A31,stroke-dasharray: "0"

  %% ===============================
  %% CONTROLLER CONCERNS & DECORATORS
  %% ===============================
  subgraph Concerns_and_Decorators[Controller Concerns & Decorators]
    direction TB
    style Concerns_and_Decorators fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rx:18,ry:18
    InternalRedirect["InternalRedirect<br>controllers/concerns/internal_redirect.rb"]:::supportfile
    WithPerformanceBar["WithPerformanceBar<br>controllers/concerns/with_performance_bar.rb"]:::supportfile
    SourcegraphDecorator["SourcegraphDecorator<br>controllers/concerns/sourcegraph_decorator.rb"]:::supportfile
    EEWebIdeCSP["WebIdeCSP<br>ee/controllers/concerns/ee/web_ide_csp.rb"]:::supportfile
  end

  %% ==============================
  %% UI PRESENTATION HELPERS
  %% ==============================
  subgraph UI_Presentation_Helpers[UI & Dashboard Helpers]
    direction TB
    style UI_Presentation_Helpers fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rx:18,ry:18
    IdeHelper["IdeHelper<br>helpers/ide_helper.rb"]:::supportfile
    JobsHelper["Admin::JobsHelper<br>helpers/admin/jobs_helper.rb"]:::supportfile
    EEDashboardHelper["EE::DashboardHelper<br>ee/helpers/ee/dashboard_helper.rb"]:::supportfile
    EEApsHelper["EE::AppearancesHelper<br>ee/helpers/ee/appearances_helper.rb"]:::supportfile
  end

  %% ===================
  %% DOMAIN DATA STRUCTURES
  %% ===================
  subgraph Domain_Data_Structures[Domain Data Structures]
    direction TB
    style Domain_Data_Structures fill:#F8F8F8,stroke:#45C478,stroke-width:2px,rx:18,ry:18
    WebIdeTerminal["WebIdeTerminal<br>app/models/web_ide_terminal.rb"]:::datafile
  end

  %% =====================
  %% SETTINGS & WEB IDE LOGIC
  %% =====================
  subgraph WebIde_Settings_Engine[Web IDE Settings Engine]
    direction TB
    style WebIde_Settings_Engine fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rx:18,ry:18
    WIS_MainRB["WebIde::Settings::Main<br>lib/web_ide/settings/main.rb"]:::corefile
    WIS_SettingsRB["WebIde::Settings<br>lib/web_ide/settings.rb"]:::corefile
    WIS_Initializer["SettingsInitializer<br>lib/web_ide/settings/settings_initializer.rb"]:::setupfile
    WIS_Msgs["WebIde::Settings::Messages<br>lib/web_ide/settings/messages.rb"]:::errorfile
    WIS_ExtMarketplaceGen["ExtensionMarketplaceGenerator<br>lib/web_ide/settings/extension_marketplace_generator.rb"]:::supportfile
    WIS_ExtMarketplaceMetaGen["ExtensionMarketplaceMetadataGenerator<br>lib/web_ide/settings/extension_marketplace_metadata_generator.rb"]:::supportfile
    WIS_ExtMarketplaceHomeGen["ExtensionMarketplaceHomeUrlGenerator<br>lib/web_ide/settings/extension_marketplace_home_url_generator.rb"]:::supportfile
    WIS_ExtMarketplaceVMGen["ExtensionMarketplaceViewModelGenerator<br>lib/web_ide/settings/extension_marketplace_view_model_generator.rb"]:::supportfile
    WIS_SettingsSync["SettingsSync<br>lib/web_ide/settings_sync.rb"]:::supportfile
  end

  %% ================
  %% WEB IDE CONFIGURATION
  %% ================
  subgraph WebIde_Config[Web IDE & Terminal Config]
    direction TB
    style WebIde_Config fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rx:18,ry:18
    WI_Config_Entry_Global["Config::Entry::Global<br>lib/web_ide/config/entry/global.rb"]:::datafile
    WI_Config_Entry_Terminal["Config::Entry::Terminal<br>lib/web_ide/config/entry/terminal.rb"]:::datafile
  end

  %% =================================
  %% PRESENTERS & SUPPORT LAYERS EE
  %% =================================
  subgraph Enterprise_Presenters[Enterprise Edition Presenters & Helpers]
    direction TB
    style Enterprise_Presenters fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rx:18,ry:18
    EEClustersClusterPresenter["EE::Clusters::ClusterPresenter<br>ee/presenters/ee/clusters/cluster_presenter.rb"]:::supportfile
  end

  %% ==============
  %% SPEC & QA UI
  %% ==============
  subgraph Test_UI[Specs & QA Automated UI Support]
    direction TB
    style Test_UI fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rx:18,ry:18
    LayoutsHeadSpec["layouts/_head.html.haml_spec.rb<br>spec/views/layouts/_head.html.haml_spec.rb"]:::supportfile
    QALayoutPerformanceBar["QA::Page::Layout::PerformanceBar<br>qa/qa/page/layout/performance_bar.rb"]:::supportfile
    QALayoutBanner["QA::Page::Layout::Banner<br>qa/qa/page/layout/banner.rb"]:::supportfile
    QAProjectWebIdeSettingsSync["QA::Page::Project::WebIDE::SettingsSync<br>qa/qa/page/project/web_ide/settings_sync.rb"]:::supportfile
  end

  %% ==========================
  %% VENDOR / EXTERNAL DASH SUPPORT
  %% ==========================
  subgraph VendorDash[Vendor/External Dashboard Components]
    direction TB
    style VendorDash fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rx:18,ry:18
    SidekiqWeb["Sidekiq::Web<br>vendor/gems/sidekiq/lib/sidekiq/web.rb"]:::supportfile
    SidekiqWebAction["Sidekiq::WebAction<br>vendor/gems/sidekiq/lib/sidekiq/web/action.rb"]:::supportfile
    SidekiqWebHelpers["Sidekiq::WebHelpers<br>vendor/gems/sidekiq/lib/sidekiq/web/helpers.rb"]:::supportfile
  end

  %% ================================
  %% WEBPACK / CONSOLE / NOCACHE UTILS
  %% ================================
  subgraph Supporting_Infra_Services[Supporting Infra / Utilities]
    direction TB
    style Supporting_Infra_Services fill:#F8F8F8,stroke:#FFDEAD,stroke-width:2px,rx:18,ry:18
    NoCacheHeaders["NoCacheHeaders<br>lib/gitlab/no_cache_headers.rb"]:::supportfile
    Console["Console<br>lib/gitlab/console.rb"]:::supportfile
    WebpackKnownOps["Webpack::GraphqlKnownOperations<br>lib/gitlab/webpack/graphql_known_operations.rb"]:::supportfile
    WebpackManifest["Webpack::Manifest<br>lib/gitlab/webpack/manifest.rb"]:::supportfile
  end

  %% ==========================
  %% LOGICAL RELATIONSHIPS & INTERACTIONS
  %% ==========================

  %% --- COMPONENT USES IN LAYOUTS ---
  RootController -->|Provides project dashboard context| ActionCardC
  RootController -->|Layout sections via| HorizontalSectionC
  PwaController -->|Renders inside layout| HorizontalSectionC
  DashboardApplicationController -->|Bases for| RootController
  IdeController -->|Web IDE session/redirect| WIS_SettingsRB
  IdeController -->|Project terminal routing| WebIdeTerminal
  SandboxController -.->|Fallback layout/dev tools| HorizontalSectionC

  %% --- CONTROLLER CONCERNS AUGMENTATION ---
  RootController --> WithPerformanceBar
  RootController --> InternalRedirect
  IdeController --> InternalRedirect
  DashboardApplicationController --> SourcegraphDecorator
  PwaController --> WithPerformanceBar
  SandboxController --> WithPerformanceBar
  IdeController --> EEWebIdeCSP

  %% --- HELPERS SUPPORTING UI LOGIC ---
  IdeController --> IdeHelper
  RootController --> JobsHelper
  DashboardApplicationController --> EEDashboardHelper
  DashboardApplicationController --> EEApsHelper

  %% --- DOMAIN DATA STRUCTURE DEPENDENCY ---
  IdeController --> WebIdeTerminal
  PwaController --> WebIdeTerminal

  %% --- WEB IDE SETTINGS LAYER (SETTINGS MAIN & HELPERS) ---
  IdeController --> WIS_MainRB
  IdeHelper --> WIS_SettingsRB
  WIS_SettingsRB --> WIS_MainRB
  WIS_SettingsRB --> WIS_Initializer
  WIS_SettingsRB --> WIS_Msgs
  WIS_MainRB --> WIS_Msgs

  %% --- SETTINGS GENERATORS/TRANSFORMERS ---
  WIS_Initializer --> WIS_ExtMarketplaceGen
  WIS_Initializer --> WIS_ExtMarketplaceMetaGen
  WIS_Initializer --> WIS_ExtMarketplaceHomeGen
  WIS_Initializer --> WIS_ExtMarketplaceVMGen

  %% --- EXTENSION MARKETPLACE GENERATORS (assist SettingsInitializer) ---
  WIS_ExtMarketplaceGen --> WIS_ExtMarketplaceMetaGen
  WIS_ExtMarketplaceGen --> WIS_ExtMarketplaceHomeGen
  WIS_ExtMarketplaceVMGen --> WIS_ExtMarketplaceGen

  %% --- SETTINGS CONFIG SYNCHRONIZATION ---
  WIS_SettingsRB --> WIS_SettingsSync
  QAProjectWebIdeSettingsSync --> WIS_SettingsSync

  %% --- CONFIG DATA STRUCTURE RELATIONSHIPS ---
  WebIdeTerminal --> WI_Config_Entry_Terminal
  WI_Config_Entry_Global --> WI_Config_Entry_Terminal

  %% --- ENTERPRISE LOGIC INTEGRATION ---
  DashboardApplicationController --> EEClustersClusterPresenter
  EEDashboardHelper --> ActionCardC
  EEApsHelper --> ActionCardC

  %% --- TEST/QA UI SUPPORT INTEGRATION ---
  LayoutsHeadSpec --> HorizontalSectionC
  QALayoutPerformanceBar --> WithPerformanceBar
  QALayoutBanner --> RootController
  QAProjectWebIdeSettingsSync --> WIS_SettingsSync

  %% --- VENDOR SIDEKIQ WEB SUPPORT ---
  SidekiqWeb --> SidekiqWebAction
  SidekiqWeb --> SidekiqWebHelpers

  %% --- INFRASTRUCTURE/UTILITY SUPPORT ---
  IdeController --> NoCacheHeaders
  IdeController --> Console
  IdeController --> WebpackManifest
  IdeController --> WebpackKnownOps

  %% --- INTER-FILE RELATIONSHIPS WITH ABBREVIATED LINES (WITHOUT DUPLICATE ARROWS) ---

  %% ======= GROUPINGS VISIBILITY ========
  Sidebar_Navigation_Components --> Dashboard_Controllers
  Dashboard_Controllers --> Concerns_and_Decorators
  Dashboard_Controllers --> UI_Presentation_Helpers
  Dashboard_Controllers --> Domain_Data_Structures
  Dashboard_Controllers --> WebIde_Settings_Engine
  Dashboard_Controllers --> WebIde_Config
  Dashboard_Controllers --> Enterprise_Presenters
  Dashboard_Controllers --> Test_UI
  Dashboard_Controllers --> VendorDash
  Dashboard_Controllers --> Supporting_Infra_Services

  %% ========== STYLES ============
  class Sidebar_Navigation_Components,Dashboard_Controllers,Concerns_and_Decorators,UI_Presentation_Helpers,Domain_Data_Structures,WebIde_Settings_Engine,Enterprise_Presenters,WebIde_Config,VendorDash,Test_UI,Supporting_Infra_Services subgraph
  class WI_Config_Entry_Global,WI_Config_Entry_Terminal,WebIdeTerminal datafile
  class WIS_Initializer setupfile
  class WIS_Msgs errorfile
```