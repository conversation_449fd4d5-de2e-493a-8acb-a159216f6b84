```mermaid
flowchart TB
%% === DOMAIN: TESTING, QA, LINTING & DEVELOPER TOOLING / TOOLING UTILITIES & SCRIPTING / MIGRATION & QUALITY TOOLING ===  
%% Palette:  
%%  - pastel blue: #D4F1F9 (core domain logic)
%%  - pastel yellow: #FFF8DC (support/utility) 
%%  - pastel green: #E0F8E0 (data structures)
%%  - pastel red: #FFE4E1 (error handling) 
%%  - pastel purple: #E6E6FA (initialization/setup)
%%  - light gray for groups: #F8F8F8

%% === LINTING, COVERAGE & CODE QUALITY TOOLING ===
subgraph "Linting & Coverage Tools" [Linting & Coverage Tools]
direction TB
style "Linting & Coverage Tools" fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded

    TCS["tooling/lib/tooling/crystalball/coverage_lines_strategy.rb
    CoverageLinesStrategy
    Core coverage strategy, enhances test coverage tracking
    "]
    style TCS fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:8

    RMABC["scripts/lint/ruby-metrics-abc.rb
    MetricsABC
    Ruby ABC metric calculation using AST
    "]
    style RMABC fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:8

    DFCS["spec/tooling/lib/tooling/find_codeowners_spec.rb
    Test: Validates Codeowners file detection logic
    "]
    style DFCS fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:8

    DCGS["tooling/danger/remote_development/desired_config_generator_suggestor.rb
    Danger rule for Remote Dev config changes
    "]
    style DCGS fill:#FFF8DC,stroke:#D4F1F9,stroke-width:2,rx:8

    TCS -- provides coverage data to --> RMABC
    DFCS -- validates --> RMABC
    DCGS -- uses logic from --> TCS
end

%% === DATABASE MIGRATION & SCHEMA HEALTH ===
subgraph "Database Migration & Schema Quality" [Database Migration & Schema Quality]
direction TB
style "Database Migration & Schema Quality" fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2,rounded

    DSMR["scripts/database/migrate.rb
    Migration CLI. Orchestrates schema migrations
    "]
    style DSMR fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rx:8

    DSV["scripts/database/schema_validator.rb
    SchemaValidator
    Validates schema consistency pre/post migration
    "]
    style DSV fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:8

    MCC["scripts/database/migration_checksum_checker.rb
    MigrationChecksumChecker
    Checks checksum integrity of migration files
    "]
    style MCC fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:8

    DTSZ["scripts/database/table_sizes.rb
    Utility: Computes database table sizes, classifies for migration decisions
    "]
    style DTSZ fill:#FFF8DC,stroke:#D4F1F9,stroke-width:2,rx:8

    SCRC["spec/tasks/migrate/schema_check_rake_spec.rb
    Test: Ensures schema version checks in Rake tasks
    "]
    style SCRC fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:8

    DSMR -- invokes --> DSV
    DSMR -- performs migrations checked by --> MCC
    DSMR -- uses size/classification from --> DTSZ
    SCRC -- validates --> DSMR
    SCRC -- validates --> DSV
    SCRC -- validates --> MCC
end

%% === MIGRATION & DATABASE HEALTH HOUSEKEEPING ===
subgraph "Migration & DB Housekeeping" [Migration & DB Housekeeping]
direction TB
style "Migration & DB Housekeeping" fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2,rounded

    KIOC["keeps/initialize_big_int_conversion.rb
    InitializeBigIntConversion
    Automates migration for bigint conversions
    "]
    style KIOC fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:8

    RDI["keeps/remove_duplicated_indexes.rb
    RemoveDuplicatedIndexes
    Detects and removes superfluous DB indexes
    "]
    style RDI fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:8

    UFCS["keeps/update_table_sizes.rb
    UpdateTableSizes
    Updates classified table size dictionaries post-migration
    "]
    style UFCS fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:8

    OFBM["keeps/overdue_finalize_background_migration.rb
    OverdueFinalizeBackgroundMigration
    Checks and finalizes outdated batched migrations
    "]
    style OFBM fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:8

    UWD["keeps/update_workers_data_consistency.rb
    UpdateWorkersDataConsistency
    Audits/updates Sidekiq workers for consistency
    "]
    style UWD fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:8

    MAF["keeps/mark_old_advanced_search_migrations_as_obsolete.rb
    MarkOldAdvancedSearchMigrationsAsObsolete
    Marks outdated search migrations obsolete
    "]
    style MAF fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:8

    DOAF["keeps/delete_obsolete_advanced_search_migrations.rb
    DeleteObsoleteAdvancedSearchMigrations
    Removes obsolete advanced search migrations
    "]
    style DOAF fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:8

    DOFF["keeps/delete_old_feature_flags.rb
    DeleteOldFeatureFlags
    Removes unused & stale feature flags, with error handling
    "]
    style DOFF fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:8

    KIOC -- relies on DB state from --> UFCS
    RDI -- references indexes listed by --> UFCS
    UFCS -- updates post-migration dictionaries for --> KIOC
    OFBM -- audits results of --> DSMR
    UWD -- validates consistency after --> OFBM
    MAF -- audits/flags obsolete after --> DSMR
    DOAF -- relies on audit results from --> MAF
    DOFF -.-> DSMR
    KIOC -- relies on table size info from --> DTSZ

end

%% === PIPELINE, QA, API & QUALITY SCRIPTS ===
subgraph "Pipeline, QA & Script Utilities" [Pipeline, QA & Script Utilities]
direction TB
style "Pipeline, QA & Script Utilities" fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded

    SPMSC["scripts/pipeline/pre_merge_checks.rb
    PreMergeChecks
    Checks MR pipeline status before merging
    "]
    style SPMSC fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:8

    PMSCSP["spec/scripts/pipeline/pre_merge_checks_spec.rb
    Test: Verifies PreMergeChecks logic and edge cases
    "]
    style PMSCSP fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:8

    SIEC["spec/scripts/internal_events/cli/global_state_spec.rb
    Test: Internal Events CLI, tests state/defn loading
    "]
    style SIEC fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:8

    SQFE["scripts/sql_fingerprint_extractor.rb
    SQLFingerprintExtractor
    Extracts and processes SQL query fingerprints
    "]
    style SQFE fill:#FFF8DC,stroke:#D4F1F9,stroke-width:2,rx:8

    SAPI["scripts/api/get_job_id.rb
    JobFinder
    Finds CI jobs with artifacts via API queries
    "]
    style SAPI fill:#FFF8DC,stroke:#D4F1F9,stroke-width:2,rx:8

    SSCRP["scripts/semgrep_result_processor.rb
    SemgrepResultProcessor
    Processes, filters Semgrep static analysis results
    "]
    style SSCRP fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:8

    SCCEA["scripts/cells/ci-ensure-application-settings-have-definition-file.rb
    CiEnsureApplicationSettingsHaveDefinitionFile
    Ensures every CI setting has a definition file
    "]
    style SCCEA fill:#FFF8DC,stroke:#D4F1F9,stroke-width:2,rx:8

    SPMSC --> PMSCSP
    PMSCSP -- validates --> SPMSC
    SPMSC -- may use fingerprint data from --> SQFE
    SSCRP -- is validated by --> PMSCSP
    SAPI -- feeds MR results to --> SPMSC
    SCCEA -- validates definition files with results from --> SAPI
    SIEC -- tests state logic for --> SAPI
end

%% === QA & TESTING RUNTIME UTILITIES ===
subgraph "Testing, QA & Safety" [Testing, QA & Safety]
direction TB
style "Testing, QA & Safety" fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded

    QCRT["qa/qa/support/code_runtime_tracker.rb
    CodeRuntimeTracker
    QA utility: tracks and logs code execution stats during automated test runs
    "]
    style QCRT fill:#FFF8DC,stroke:#D4F1F9,stroke-width:2,rx:8

    DBPCM["spec/support/database/prevent_cross_database_modification.rb
    Helpers for preventing cross-database access in specs, ensures DB safety
    "]
    style DBPCM fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:8

    DBVS["spec/support/database/gitlab_schemas_validate_connection.rb
    RSpec helper for DB schema validation & suppression
    "]
    style DBVS fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,rx:8

    DBPCM -- is used with/invokes --> DBVS
    QCRT -- tracks data for --> DBPCM
end

%% === DOMAIN SUPPORT / DATA / UTILITY LIBS ===
subgraph "Core & Utility Support" [Core & Utility Support]
direction TB
style "Core & Utility Support" fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,rounded

    GLDRT["lib/gitlab/doctor/reset_tokens.rb
    Gitlab::Doctor::ResetTokens
    Utility: Resets API-related tokens with data-logging
    "]
    style GLDRT fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rx:8

    GLDRT -- used by --> DSMR
    GLDRT -- referenced for data reset in --> UWD
end

%% === DOMAIN-SPANNING MAJOR DATA STRUCTURES ===
subgraph "Domain Data Structures" [Domain Data Structures]
direction TB
style "Domain Data Structures" fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2,rounded

    MGSTRUCT["Migration Files, DB Partition Tables, Table Size Dictionaries
    Pastel green: Data structures representing migration state, db table metadata, index lists, controlled and updated by various keeps/scripts
    "]
    style MGSTRUCT fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rx:12

    FFLIST["Feature Flag Lists/Checksums/Obsolete Sets
    Pastel green: Feature flag data, subject to audits and deletions
    "]
    style FFLIST fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rx:12

    MGSTRUCT -- referenced/modifed by --> DSMR
    MGSTRUCT -- updated by --> UFCS
    MGSTRUCT -- source for changes in --> RDI
    MGSTRUCT -- read/obsoleted by --> DOAF
    FFLIST -- updated by --> DOFF
end

%% === LOGICAL RELATIONSHIPS BETWEEN GROUPS ===

"Linting & Coverage Tools" -- provides metrics to --> "Migration & DB Housekeeping"
"Linting & Coverage Tools" -- code quality integration with --> "Pipeline, QA & Script Utilities"
"Database Migration & Schema Quality" -- performs structure checks for --> "Migration & DB Housekeeping"
"Migration & DB Housekeeping" -- ensures consistency post-deploy for --> "Testing, QA & Safety"
"Pipeline, QA & Script Utilities" -- links with runtime checks of --> "Testing, QA & Safety"
"Testing, QA & Safety" -- provides safety guards for --> "Database Migration & Schema Quality"

"Core & Utility Support" -- core utility usage for --> "Database Migration & Schema Quality"
"Core & Utility Support" -- supports routines in --> "Migration & DB Housekeeping"

"Domain Data Structures" --- feeds core and derived data to ---> "Migration & DB Housekeeping"
"Domain Data Structures" --- is indirectly validated by ---> "Linting & Coverage Tools"
"Domain Data Structures" --- is checked by ---> "Database Migration & Schema Quality"

%% Highlight error-handling file
style DOFF fill:#FFE4E1,stroke:#D4F1F9,stroke-width:2,rx:8

%% Special coloring for initialization/process flow
style DSMR fill:#E6E6FA,stroke:#D4F1F9,stroke-width:2,rx:8
```