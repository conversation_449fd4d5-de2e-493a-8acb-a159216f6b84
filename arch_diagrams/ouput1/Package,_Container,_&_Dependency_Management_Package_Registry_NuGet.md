```mermaid
flowchart TD
    %% Diagram Title
    %% LOGICAL GROUPS

    %%=========================== CORE DOMAIN MODELS ===========================
    subgraph core_models["Core Domain Models"]
        direction TB
        style core_models fill:#F8F8F8,stroke:#80C4DE,stroke-width:2px,rounded,corner-radius:10px
        nuget_main["nuget.rb\nNuGet namespace root"]
        style nuget_main fill:#D4F1F9,stroke:#A7C9DD,stroke-width:2px,rounded

        nuget_package["package.rb\nNuGetPackage entity"]
        style nuget_package fill:#D4F1F9,stroke:#A7C9DD,stroke-width:2px,rounded

        nuget_symbol["symbol.rb\nSymbol file Entity"]
        style nuget_symbol fill:#D4F1F9,stroke:#A7C9DD,stroke-width:2px,rounded

        nuget_metadatum["metadatum.rb\nNuGet metadata properties"]
        style nuget_metadatum fill:#E0F8E0,stroke:#78C883,stroke-width:2px,rounded

        nuget_dependency_link_meta["dependency_link_metadatum.rb\nDependency link metadata"]
        style nuget_dependency_link_meta fill:#E0F8E0,stroke:#78C883,stroke-width:2px,rounded

        nuget_version_normalizable["version_normalizable.rb\nVersion normalization\nActiveSupport::Concern"]
        style nuget_version_normalizable fill:#E0F8E0,stroke:#78C883,stroke-width:2px,rounded
    end

    %%=========================== PRESENTERS ===================================
    subgraph presenters["Presenters, Output Serialization & Helpers"]
        direction TB
        style presenters fill:#F8F8F8,stroke:#E1BEE7,stroke-width:2px,rounded,corner-radius:10px
        search_results_presenter["search_results_presenter.rb\nAggregates package search results"]
        style search_results_presenter fill:#FFF8DC,stroke:#DEC866,stroke-width:2px,rounded

        package_versions_presenter["packages_versions_presenter.rb\nSerializes package version lists"]
        style package_versions_presenter fill:#FFF8DC,stroke:#DEC866,stroke-width:2px,rounded

        package_metadata_presenter["package_metadata_presenter.rb\nSerializes package metadata"]
        style package_metadata_presenter fill:#FFF8DC,stroke:#DEC866,stroke-width:2px,rounded

        v2_metadata_index_presenter["v2/metadata_index_presenter.rb\nOData v2 XML Index"]
        style v2_metadata_index_presenter fill:#FFF8DC,stroke:#DEC866,stroke-width:2px,rounded

        v2_service_index_presenter["v2/service_index_presenter.rb\nOData v2 Service Index"]
        style v2_service_index_presenter fill:#FFF8DC,stroke:#DEC866,stroke-width:2px,rounded

        presenter_helpers["presenter_helpers.rb\nShared serialization helpers"]
        style presenter_helpers fill:#FFF8DC,stroke:#DEC866,stroke-width:2px,rounded

        version_helpers["version_helpers.rb\nNuGet version sorting utilities"]
        style version_helpers fill:#FFF8DC,stroke:#DEC866,stroke-width:2px,rounded
    end

    %%========================= FINDERS ========================================
    subgraph finders["Domain Finders"]
        direction TB
        style finders fill:#F8F8F8,stroke:#B5EAD7,stroke-width:2px,rounded,corner-radius:10px
        package_finder["package_finder.rb\nFind/filter NuGet packages"]
        style package_finder fill:#D4F1F9,stroke:#A7C9DD,stroke-width:2px,rounded
    end

    %%======================= POLICIES =========================================
    subgraph policies["Authorization Policies"]
        direction TB
        style policies fill:#F8F8F8,stroke:#C9D6E3,stroke-width:2px,rounded,corner-radius:10px
        metadatum_policy["metadatum_policy.rb\nAccess to NuGet metadata"]
        style metadatum_policy fill:#FFF8DC,stroke:#DEC866,stroke-width:2px,rounded
        dependency_link_metadatum_policy["dependency_link_metadatum_policy.rb\nAccess to dependency link metadata"]
        style dependency_link_metadatum_policy fill:#FFF8DC,stroke:#DEC866,stroke-width:2px,rounded
    end

    %%======================= WORKERS ==========================================
    subgraph workers["Async Workers & Cleanup"]
        direction TB
        style workers fill:#F8F8F8,stroke:#D6A4A4,stroke-width:2px,rounded,corner-radius:10px
        create_symbols_worker["create_symbols_worker.rb\nCreate symbol files async"]
        style create_symbols_worker fill:#E6E6FA,stroke:#b0a8ea,stroke-width:2px,rounded

        extraction_worker["extraction_worker.rb\nExtract package content async"]
        style extraction_worker fill:#E6E6FA,stroke:#b0a8ea,stroke-width:2px,rounded

        cleanup_stale_symbols_worker["cleanup_stale_symbols_worker.rb\nAsync cleanup\nof orphaned symbols"]
        style cleanup_stale_symbols_worker fill:#E6E6FA,stroke:#b0a8ea,stroke-width:2px,rounded
    end

    %%========================= FILE UPLOADERS =================================
    subgraph uploaders["File Uploaders"]
        direction TB
        style uploaders fill:#F8F8F8,stroke:#A7C9DD,stroke-width:2px,rounded,corner-radius:10px
        symbol_uploader["symbol_uploader.rb\nHandles symbol file uploads"]
        style symbol_uploader fill:#FFF8DC,stroke:#DEC866,stroke-width:2px,rounded
    end

    %%============================= SERVICES ===================================
    subgraph services["Domain Services"]
        direction TB
        style services fill:#F8F8F8,stroke:#FFE082,stroke-width:2px,rounded,corner-radius:10px

        sync_metadatum_service["sync_metadatum_service.rb\nSync NuGet package metadata"]
        style sync_metadatum_service fill:#FFF8DC,stroke:#DEC866,stroke-width:2px,rounded

        create_temp_package_service["create_temporary_package_service.rb\nCreate temp package for upload"]
        style create_temp_package_service fill:#FFF8DC,stroke:#DEC866,stroke-width:2px,rounded

        symbols_extract_sig_checksum["symbols/extract_signature_and_checksum_service.rb\nExtract PDB symbol signature and checksum"]
        style symbols_extract_sig_checksum fill:#FFF8DC,stroke:#DEC866,stroke-width:2px,rounded

        update_pkg_from_metadata_service["update_package_from_metadata_service.rb\nUpdate package from metadata"]
        style update_pkg_from_metadata_service fill:#FFF8DC,stroke:#DEC866,stroke-width:2px,rounded

        extract_remote_metadata_file_service["extract_remote_metadata_file_service.rb\nExtract nuspec metadata from remote files"]
        style extract_remote_metadata_file_service fill:#FFF8DC,stroke:#DEC866,stroke-width:2px,rounded

        create_or_update_package_service["create_or_update_package_service.rb\nIdempotent create or update\nfor NuGet package"]
        style create_or_update_package_service fill:#FFF8DC,stroke:#DEC866,stroke-width:2px,rounded

        extract_metadata_file_service["extract_metadata_file_service.rb\nExtract nuspec metadata from uploads"]
        style extract_metadata_file_service fill:#FFF8DC,stroke:#DEC866,stroke-width:2px,rounded

        metadata_extraction_service["metadata_extraction_service.rb\nUnified metadata extraction\nfrom .nupkg"]
        style metadata_extraction_service fill:#FFF8DC,stroke:#DEC866,stroke-width:2px,rounded

        extract_metadata_content_service["extract_metadata_content_service.rb\nParse nuspec XML metadata"]
        style extract_metadata_content_service fill:#FFF8DC,stroke:#DEC866,stroke-width:2px,rounded

        process_package_file_service["process_package_file_service.rb\nValidate, extract and process .nupkg files"]
        style process_package_file_service fill:#FFF8DC,stroke:#DEC866,stroke-width:2px,rounded

        odata_package_entry_service["odata_package_entry_service.rb\nGenerate OData-compliant responses"]
        style odata_package_entry_service fill:#FFF8DC,stroke:#DEC866,stroke-width:2px,rounded

        create_dependency_service["create_dependency_service.rb\nPersist dependency graph"]
        style create_dependency_service fill:#FFF8DC,stroke:#DEC866,stroke-width:2px,rounded

        symbols_create_symbol_files_service["symbols/create_symbol_files_service.rb\nExtract and store symbol entries"]
        style symbols_create_symbol_files_service fill:#FFF8DC,stroke:#DEC866,stroke-width:2px,rounded
    end

    %%======================== API ENDPOINTS & INFRA ===========================
    subgraph api["API Endpoint Layer"]
        direction TB
        style api fill:#F8F8F8,stroke:#b2dfed,stroke-width:2px,rounded,corner-radius:10px
        nuget_project_packages_api["nuget_project_packages.rb\nHTTP NuGet endpoints"]
        style nuget_project_packages_api fill:#D4F1F9,stroke:#A7C9DD,stroke-width:2px,rounded

        api_entities_service_index["entities/nuget/service_index.rb"]
        style api_entities_service_index fill:#E0F8E0,stroke:#78C883,stroke-width:2px,rounded
        api_entities_search_results["entities/nuget/search_results.rb"]
        style api_entities_search_results fill:#E0F8E0,stroke:#78C883,stroke-width:2px,rounded
        api_entities_search_result_version["entities/nuget/search_result_version.rb"]
        style api_entities_search_result_version fill:#E0F8E0,stroke:#78C883,stroke-width:2px,rounded
        api_entities_packages_versions["entities/nuget/packages_versions.rb"]
        style api_entities_packages_versions fill:#E0F8E0,stroke:#78C883,stroke-width:2px,rounded
        api_entities_packages_metadata_item["entities/nuget/packages_metadata_item.rb"]
        style api_entities_packages_metadata_item fill:#E0F8E0,stroke:#78C883,stroke-width:2px,rounded
        api_entities_packages_metadata["entities/nuget/packages_metadata.rb"]
        style api_entities_packages_metadata fill:#E0F8E0,stroke:#78C883,stroke-width:2px,rounded

        api_concerns_public_endpoints["concerns/packages/nuget/public_endpoints.rb\nPublic-facing NuGet API"]
        style api_concerns_public_endpoints fill:#FFF8DC,stroke:#DEC866,stroke-width:2px,rounded
        api_concerns_private_endpoints["concerns/packages/nuget/private_endpoints.rb\nAuthenticated NuGet API"]
        style api_concerns_private_endpoints fill:#FFF8DC,stroke:#DEC866,stroke-width:2px,rounded
    end

    %%============================= INITIALIZATION & GRAPHQL ===========================
    subgraph initialization["Initialization & GraphQL"]
        direction TB
        style initialization fill:#F8F8F8,stroke:#b0a8ea,stroke-width:2px,rounded,corner-radius:10px
        graphql_metadatum_type["metadatum_type.rb\nGraphQL type for NuGet metadata"]
        style graphql_metadatum_type fill:#E6E6FA,stroke:#b0a8ea,stroke-width:2px,rounded
    end

    %%=========================== BIG GROUPING INTERACTIONS =======================
    %% MODELS AND RELATIONS
    nuget_main --> nuget_package
    nuget_package -->|has_one| nuget_metadatum
    nuget_package -->|has_many| nuget_symbol
    nuget_package -->|has_many| nuget_dependency_link_meta

    nuget_metadatum -- includes --> nuget_version_normalizable
    nuget_metadatum_policy -- delegated_to --> nuget_package
    nuget_version_normalizable -.-> nuget_package

    nuget_symbol -->|belongs_to| nuget_package
    nuget_symbol --> symbol_uploader
    nuget_symbol -- uses --> symbols_extract_sig_checksum
    nuget_symbol -- uses --> symbols_create_symbol_files_service
    create_symbols_worker --> symbols_create_symbol_files_service
    create_symbols_worker --> nuget_symbol

    cleanup_stale_symbols_worker --> nuget_symbol

    nuget_dependency_link_meta --> nuget_package
    dependency_link_metadatum_policy -- delegated_to --> nuget_dependency_link_meta

    %% PRESENTERS LOGICAL RELATIONS
    search_results_presenter --> presenter_helpers
    search_results_presenter --> package_finder
    search_results_presenter --> nuget_package

    presenter_helpers --> version_helpers
    package_metadata_presenter --> presenter_helpers
    package_metadata_presenter --> nuget_package

    package_versions_presenter --> nuget_package

    v2_metadata_index_presenter --> nuget_package
    v2_metadata_index_presenter --> nuget_metadatum
    v2_metadata_index_presenter --> nuget_dependency_link_meta

    v2_service_index_presenter --> nuget_package

    %% FINDERS
    package_finder --> nuget_package

    %% POLICIES
    metadatum_policy --> nuget_metadatum
    dependency_link_metadatum_policy --> nuget_dependency_link_meta

    %% WORKERS
    create_symbols_worker --> nuget_package
    extraction_worker --> process_package_file_service
    extraction_worker -- processes --> nuget_package
    cleanup_stale_symbols_worker --> nuget_symbol

    %% UPLOADERS
    symbol_uploader --> nuget_symbol

    %% SERVICES INTERACTIONS
    sync_metadatum_service --> nuget_metadatum
    sync_metadatum_service --> nuget_package

    create_temp_package_service --> nuget_package

    update_pkg_from_metadata_service -->|reads+updates| nuget_package
    update_pkg_from_metadata_service -->|metadata| nuget_metadatum
    update_pkg_from_metadata_service -- uses --> sync_metadatum_service

    extract_remote_metadata_file_service -- yields_nuspec_content --> extract_metadata_content_service
    extract_metadata_file_service -- yields_nuspec_content --> extract_metadata_content_service
    extract_metadata_file_service --> nuget_package

    metadata_extraction_service --> extract_metadata_file_service
    metadata_extraction_service --> extract_metadata_content_service

    extract_metadata_content_service --> nuget_metadatum
    extract_metadata_content_service -- produces --> nuget_dependency_link_meta

    process_package_file_service --> extract_metadata_file_service
    process_package_file_service --> create_dependency_service
    process_package_file_service --> nuget_package

    odata_package_entry_service --> nuget_package
    odata_package_entry_service --> nuget_metadatum

    create_dependency_service --> nuget_dependency_link_meta
    create_dependency_service --> nuget_package

    create_or_update_package_service --> nuget_package
    create_or_update_package_service --> sync_metadatum_service
    create_or_update_package_service --> create_dependency_service

    symbols_create_symbol_files_service --> nuget_package
    symbols_create_symbol_files_service --> nuget_symbol
    symbols_create_symbol_files_service --> symbols_extract_sig_checksum

    %% API AND INFRA LAYER
    nuget_project_packages_api -- exposes --> api_concerns_public_endpoints
    nuget_project_packages_api -- exposes --> api_concerns_private_endpoints
    nuget_project_packages_api --> create_or_update_package_service
    nuget_project_packages_api --> extract_remote_metadata_file_service
    nuget_project_packages_api --> package_finder
    nuget_project_packages_api --> search_results_presenter
    nuget_project_packages_api --> v2_metadata_index_presenter
    nuget_project_packages_api --> v2_service_index_presenter
    nuget_project_packages_api --> package_versions_presenter
    nuget_project_packages_api --> package_metadata_presenter

    api_entities_service_index --> v2_service_index_presenter
    api_entities_search_results --> search_results_presenter
    api_entities_search_result_version --> package_versions_presenter
    api_entities_packages_versions --> package_versions_presenter
    api_entities_packages_metadata --> package_metadata_presenter
    api_entities_packages_metadata_item --> package_metadata_presenter

    api_concerns_public_endpoints --> api_entities_service_index
    api_concerns_public_endpoints --> v2_service_index_presenter

    api_concerns_private_endpoints --> api_entities_packages_metadata
    api_concerns_private_endpoints --> v2_metadata_index_presenter

    %% GRAPHQL INITIALIZATION
    graphql_metadatum_type --> nuget_metadatum

    %% STYLES FOR SUBGRAPH LABELS
    classDef coreModelBorder fill:#F8F8F8,stroke:#80C4DE,stroke-width:2px;
    classDef infraBorder fill:#F8F8F8,stroke:#b2dfed,stroke-width:2px;
    classDef serviceBorder fill:#F8F8F8,stroke:#FFE082,stroke-width:2px;
    classDef presentBorder fill:#F8F8F8,stroke:#E1BEE7,stroke-width:2px;
    classDef policyBorder fill:#F8F8F8,stroke:#C9D6E3,stroke-width:2px;
    classDef workerBorder fill:#F8F8F8,stroke:#D6A4A4,stroke-width:2px;
    classDef uploaderBorder fill:#F8F8F8,stroke:#A7C9DD,stroke-width:2px;
    classDef graphqlBorder fill:#F8F8F8,stroke:#b0a8ea,stroke-width:2px;

    %% CONNECTION SPACING HINTS
    %% redraw (invis) edges to enforce vertical layout and spacing between major zones
    nuget_main --- nuget_project_packages_api
    nuget_main --- sync_metadatum_service

    %% Abstractions
    nuget_version_normalizable -. includes .-> version_helpers
    nuget_version_normalizable -. used_in .-> extract_metadata_content_service
    presenter_helpers -. utilizes .-> version_helpers
    metadata_extraction_service -. uses .-> extract_metadata_content_service

    %% DATA FLOW
    process_package_file_service --. produces .--> nuget_package
    process_package_file_service --. extracts .--> nuget_metadatum
    process_package_file_service --. extracts .--> nuget_dependency_link_meta
    extract_metadata_content_service -. creates .-> nuget_metadatum
    extract_metadata_content_service -. creates .-> nuget_dependency_link_meta
    symbols_extract_sig_checksum -. yields .-> nuget_symbol

    %% Initialization flow
    nuget_package --> graphql_metadatum_type

    %% Connecting core abstractions
    create_temp_package_service --> create_or_update_package_service
    create_temp_package_service --> process_package_file_service

    %% End-of-graph
```