```mermaid
flowchart TB
  %% Subgraph: Domain Root
  subgraph Domain["Package, Container, & Dependency Management / Container Registry / Protection Rules & Policies"]
    direction TB
    style Domain fill:#F8F8F8,stroke:#8ecae6,stroke-width:2,corner-radius:10

    %% --- Core Domain Models ---
    subgraph Models["Core Domain Models"]
      direction TB
      style Models fill:#F8F8F8,stroke:#90c8f8,stroke-width:2,corner-radius:10

      PKGRule["packages/protection/rule.rb":::coreModel]
      PKGRule[shape="rounded-rectangle"]

      CRRule["container_registry/protection/rule.rb":::coreModel]
      CRRule[shape="rounded-rectangle"]

      CRTgRule["container_registry/protection/tag_rule.rb":::coreModel]
      CRTgRule[shape="rounded-rectangle"]

      %% Empty model root for container_registry/protection.rb
      CRProt["container_registry/protection.rb":::coreModel]
      CRProt[shape="rounded-rectangle"]

      %% Relationships core models to each other
      CRRule -- Implements container repository protection rules --> CRProt
      CRTgRule -- Implements tag rule protection for container images --> CRProt
      PKGRule -- Implements package-level protection rules --> Domain
      CRRule -- Structure used by --> Domain
      CRTgRule -- Structure used by --> Domain
      CRProt -- Provides container registry protection namespace --> Domain
    end

    %% --- Data Structures (Entities) ---
    subgraph Entities["Data Structure Definitions"]
      direction TB
      style Entities fill:#F8F8F8,stroke:#80d4a4,stroke-width:2,corner-radius:10
      
      APIEntRule["lib/api/entities/projects/container_registry/protection/rule.rb":::dataStruct]
      APIEntRule[shape="rounded-rectangle"]
    end

    %% --- Authorization & Policies ---
    subgraph Policies["Authorization & Policies"]
      direction TB
      style Policies fill:#F8F8F8,stroke:#dbcaf4,stroke-width:2,corner-radius:10

      CRTgRulePol["container_registry/protection/tag_rule_policy.rb":::support]
      CRTgRulePol[shape="rounded-rectangle"]
    end

    %% --- Package Protection Services ---
    subgraph PkgServices["Package Protection Services"]
      direction TB
      style PkgServices fill:#F8F8F8,stroke:#6db6e6,stroke-width:2,corner-radius:10

      PKGUpdRuleSvc["packages/protection/update_rule_service.rb":::coreService]
      PKGUpdRuleSvc[shape="rounded-rectangle"]
      PKGDelRuleSvc["packages/protection/delete_rule_service.rb":::coreService]
      PKGDelRuleSvc[shape="rounded-rectangle"]
      PKGCheckExistSvc["packages/protection/check_rule_existence_service.rb":::coreService]
      PKGCheckExistSvc[shape="rounded-rectangle"]
    end

    %% --- Container Registry Protection Services ---
    subgraph CRProtServices["Container Registry Protection Services"]
      direction TB
      style CRProtServices fill:#F8F8F8,stroke:#90c8f8,stroke-width:2,corner-radius:10

      CRUpdRuleSvc["container_registry/protection/update_rule_service.rb":::coreService]
      CRUpdRuleSvc[shape="rounded-rectangle"]

      CRCrtRuleSvc["container_registry/protection/create_rule_service.rb":::coreService]
      CRCrtRuleSvc[shape="rounded-rectangle"]

      CRDelRuleSvc["container_registry/protection/delete_rule_service.rb":::coreService]
      CRDelRuleSvc[shape="rounded-rectangle"]

      CRTgUpdSvc["container_registry/protection/update_tag_rule_service.rb":::coreService]
      CRTgUpdSvc[shape="rounded-rectangle"]

      CRTgCrtSvc["container_registry/protection/create_tag_rule_service.rb":::coreService]
      CRTgCrtSvc[shape="rounded-rectangle"]

      CRTgDelSvc["container_registry/protection/delete_tag_rule_service.rb":::coreService]
      CRTgDelSvc[shape="rounded-rectangle"]
    end

    %% --- Container Expiration Policies Services ---
    subgraph ExpPolicyServices["Container Expiration Policy Services"]
      direction TB
      style ExpPolicyServices fill:#F8F8F8,stroke:#80d4a4,stroke-width:2,corner-radius:10

      ExpUpdSvc["container_expiration_policies/update_service.rb":::coreService]
      ExpUpdSvc[shape="rounded-rectangle"]
    end

    %% --- Authentication Services ---
    subgraph Auth["Authentication Services"]
      direction TB
      style Auth fill:#F8F8F8,stroke:#fff3a8,stroke-width:2,corner-radius:10

      CRAuthSvc["auth/container_registry_authentication_service.rb":::support]
      CRAuthSvc[shape="rounded-rectangle"]
    end

    %% --- GraphQL Mutations ---
    subgraph GraphQLMutations["GraphQL Mutations"]
      direction TB
      style GraphQLMutations fill:#F8F8F8,stroke:#80c8a6,stroke-width:2,corner-radius:10

      GQLTgUpd["mutations/container_registry/protection/tag_rule/update.rb":::dataStruct]
      GQLTgUpd[shape="rounded-rectangle"]
      GQLTgDel["mutations/container_registry/protection/tag_rule/delete.rb":::dataStruct]
      GQLTgDel[shape="rounded-rectangle"]
      GQLRuleDel["mutations/container_registry/protection/rule/delete.rb":::dataStruct]
      GQLRuleDel[shape="rounded-rectangle"]
    end

    %% --- Controller: Web Interface ---
    subgraph WebController["Web Interface"]
      direction TB
      style WebController fill:#F8F8F8,stroke:#ecd8d7,stroke-width:2,corner-radius:10

      PkgAndRegCtrl["controllers/projects/settings/packages_and_registries_controller.rb":::init]
      PkgAndRegCtrl[shape="rounded-rectangle"]
    end

  end

  %%---------------- Node Styles -----------------%%
  classDef coreModel fill:#D4F1F9,stroke:#90c8f8,stroke-width:2,color:#333,corner-radius:8;
  classDef coreService fill:#D4F1F9,stroke:#90c8f8,stroke-width:2,color:#333,corner-radius:8;
  classDef support fill:#FFF8DC,stroke:#ffd86b,stroke-width:2,color:#333,corner-radius:8;
  classDef dataStruct fill:#E0F8E0,stroke:#80c8a6,stroke-width:2,color:#333,corner-radius:8;
  classDef error fill:#FFE4E1,stroke:#ffadad,stroke-width:2,color:#333,corner-radius:8;
  classDef init fill:#E6E6FA,stroke:#dbcaf4,stroke-width:2,color:#333,corner-radius:8;

  %% ---------------- LOGICAL INTERACTIONS -----------------

  %% Core Models Relationships
  PKGRule -- "Defines rules for protected packages and package types" --> PKGUpdRuleSvc
  PKGRule -- "Defines rules for protected packages" --> PKGDelRuleSvc
  PKGRule -- "Predicate for policy checks" --> PKGCheckExistSvc

  CRRule -- "Defines repository protection rules" --> CRUpdRuleSvc
  CRRule -- "Defines protection rules" --> CRDelRuleSvc
  CRRule -- "Basis for GraphQL mutations" --> GQLRuleDel

  CRTgRule -- "Defines tag pattern-based protection rules" --> CRTgUpdSvc
  CRTgRule -- "Defines tag protection rules" --> CRTgCrtSvc
  CRTgRule -- "Basis for delete mutation" --> CRTgDelSvc
  CRTgRule -- "Policy rules for deletion" --> CRTgRulePol
  CRTgRule -- "Exposed via API entities" --> APIEntRule

  CRProt -- "Namespace for core container registry protection logic" --> CRRule
  CRProt -- "Namespace for tag-based protection" --> CRTgRule

  %% Data Structure Interactions
  APIEntRule -- "Presents protection rules in API" --> Domain

  %% Policy Relations
  CRTgRulePol -- "Authorizes actions on tag rules" --> CRTgDelSvc
  CRTgRulePol -- "Checked during GraphQL mutation" --> GQLTgDel

  %% Package Protection Services
  PKGUpdRuleSvc -- "Updates package protection rules" --> PKGRule
  PKGDelRuleSvc -- "Deletes package protection rule" --> PKGRule
  PKGCheckExistSvc -- "Checks if protection rule matching access/package exists" --> PKGRule
  PKGCheckExistSvc -- "Error handling for invalid/unauthorized":::error --> PKGRule

  %% Container Registry Protection Services
  CRUpdRuleSvc -- "Updates repository protection rules" --> CRRule
  CRCrtRuleSvc -- "Creates new repository protection rules" --> CRRule
  CRDelRuleSvc -- "Deletes repository protection rules" --> CRRule
  CRTgUpdSvc -- "Updates tag protection" --> CRTgRule
  CRTgCrtSvc -- "Creates tag-based protection rule" --> CRTgRule
  CRTgDelSvc -- "Deletes tag protection rule" --> CRTgRule

  %% Container Expiration Policy Services
  ExpUpdSvc -- "Updates expiration policies for containers" --> Domain

  %% Authentication Services
  CRAuthSvc -- "Provides token/authorization" --> CRRule
  CRAuthSvc -- "Determines push protection" --> CRTgRule

  %% GraphQL Interactions Mutations
  GQLTgUpd -- "Updates tag rule via mutation" --> CRTgUpdSvc
  GQLTgUpd -- "Authorized and validated via policy" --> CRTgRulePol
  GQLTgUpd -- "Mutates tag protection rule" --> CRTgRule

  GQLTgDel -- "Triggers tag rule deletion" --> CRTgDelSvc
  GQLTgDel -- "Authorized and validated via policy" --> CRTgRulePol
  GQLTgDel -- "Mutates tag protection rule" --> CRTgRule

  GQLRuleDel -- "Triggers repo rule deletion" --> CRDelRuleSvc
  GQLRuleDel -- "Mutates repository protection rule" --> CRRule

  %% Controller: Web Entry
  PkgAndRegCtrl -- "Web/HTML interface for managing protection rules" --> PKGRule
  PkgAndRegCtrl -- "Web entry for container registry protection settings" --> CRRule
  PkgAndRegCtrl -- "Web entry for tag protection settings" --> CRTgRule
  PkgAndRegCtrl -- "Enables feature flag protection flows" --> Domain

  %% Service and Entity Coordination
  PKGUpdRuleSvc -- "Returns updated rule for presentation" --> APIEntRule
  CRUpdRuleSvc -- "Returns updated rule for presentation" --> APIEntRule
  CRCrtRuleSvc -- "Returns created rule entity" --> APIEntRule
  CRDelRuleSvc -- "Handles deletion, error propagates to clients":::error --> APIEntRule
  CRTgCrtSvc -- "Returns created tag rule entity" --> APIEntRule
  CRTgUpdSvc -- "Modifies existing entity" --> APIEntRule
  CRTgDelSvc -- "Removes entity, confirms via API" --> APIEntRule

  %% Package & Container Interdisciplinary Flows
  PKGCheckExistSvc -- "Used by auth for existence/permission checks" --> CRAuthSvc

  %% Domain-wide Error Handling
  PKGDelRuleSvc -- "Raises ArgumentError if rule/user missing":::error --> Domain
  CRDelRuleSvc -- "Raises ArgumentError if rule/user missing":::error --> Domain
  CRTgDelSvc -- "Raises ArgumentError if tag rule/user missing":::error --> Domain

  %% Legend
  subgraph Legend["Legend"]
    direction TB
    l1["Core domain file":::coreModel]
    l2["Supporting/Utility file":::support]
    l3["Data structure/entity file":::dataStruct]
    l4["Initialization/Setup file":::init]
    l5["Error handling/raise errors":::error]
  end
  style Legend fill:#F8F8F8,stroke:#cccccc,stroke-width:1,corner-radius:10
```