```mermaid
flowchart TD
%% COLOR DEFINITIONS
%% Core domain files: pastel blue #D4F1F9
%% Supporting/utility files: pastel yellow #FFF8DC
%% Data structure files: pastel green #E0F8E0
%% Error handling files: pastel red #FFE4E1
%% Initialization/setup files: pastel purple #E6E6FA
%% Logical groupings/subgraphs: very light gray #F8F8F8 with pastel borders

%% OBJECT STORAGE/COLLABORATION-LAYERED GROUPS

subgraph A["Object Storage CDN" ]
direction TB
style A fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded=true

    A0[CDN Concern\napp/uploaders/object_storage/cdn.rb]
    A0:::core

    A1[File URL Abstraction\napp/uploaders/object_storage/cdn/file_url.rb]
    A1:::core

    A2[Google CDN Adapter\napp/uploaders/object_storage/cdn/google_cdn.rb]
    A2:::core

    A3[Google CDN IP Cache\napp/uploaders/object_storage/cdn/google_ip_cache.rb]
    A3:::support

    A0 -- "Provides\ncdn_enabled_url, cdn logic" --> A1
    A1 -- "Delegates to CDN-enabled files"--> A0

    A2 -- "Implements CDN operations\ndepends on IP Cache" --> A3
    A0 -- "Abstracts CDN provider extends with concrete CDN\n(e.g. Google)" --> A2

    A2 -- "Adapts for Google CDN\nSigned URL generation" --> A0
    A3 -- "Caches Google CDN IPs" --> A2

end

%% PERSISTENCE/MIGRATION GROUP MIGRATION SERVICES

subgraph B["Repository Storage & Attachments Migration" ]
direction TB
style B fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded=true

    B0[MigrationService\napp/services/projects/hashed_storage/migration_service.rb]
    B0:::core

    B1[Migrate Attachments Service\napp/services/projects/hashed_storage/migrate_attachments_service.rb]
    B1:::core

    B2[Update Repository Storage Methods\napp/services/concerns/update_repository_storage_methods.rb]
    B2:::support

    B3[UpdateRepoStorage Worker EE\n(ee/app/workers/groups/update_repository_storage_worker.rb)]
    B3:::support

    B0 -- "Handles overall storage migration for project" --> B1
    B1 -- "Handles attachments migration,\nextends base attachment service" --> B0

    B2 -- "Provides helper methods for storage operations\ntracking, mirroring, checks" --> B0
    B3 -- "Background job driving repository storage updates\nuses base methods and overrides" --> B2
    B3 -- "Background migration orchestration" --> B0

end

%% MIGRATION: OBJECT AND FILE STORAGE MIGRATERS

subgraph C["Local/Remote/Object Storage Migration Engines" ]
direction TB
style C fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded=true

    C0[Base Migrater\nlib/gitlab/local_and_remote_storage_migration/base_migrater.rb]
    C0:::core

    C1[Artifact Migrater\nlib/gitlab/local_and_remote_storage_migration/artifact_migrater.rb]
    C1:::core

    C2[Pages Deployment Migrater\nlib/gitlab/local_and_remote_storage_migration/pages_deployment_migrater.rb]
    C2:::core

    C3[Uploads Migration Helper\nlib/gitlab/uploads/migration_helper.rb]
    C3:::support

    C0 -- "Base migration abstraction" --> C1
    C0 -- "Base migration abstraction" --> C2
    C1 -- "Migrates artifacts from local<->remote/object storage\nfor CI job artifacts" --> C0
    C2 -- "Migrates 'PagesDeployment' items local<->remote/object storage" --> C0

    C3 -- "Migration helper utility initialization,\nbulk migration for uploads" --> C0
    C3 -- "May use artifact/pages migraters for specific structures" --> C1
    C3 -- "May use artifact/pages migraters for specific structures" --> C2

end

%% DATABASE/REINDEX/LOW-LEVEL MIGRATION

subgraph D["Database & Indexing Operations" ]
direction TB
style D fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded=true

    D0[Reindex Concurrently\nlib/gitlab/database/reindexing/reindex_concurrently.rb]
    D0:::core
end

%% DATA ABSTRACTIONS/TRANSFORMERS/CONCERNS

subgraph E["Model & Data Structure Sharding" ]
direction TB
style E fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2,rounded=true

    E0[Populates Sharding Key Concern\napp/models/concerns/populates_sharding_key.rb]
    E0:::data
end

%% SPEC COVERAGE (for domain completeness, no arrows out)
classDef data fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rounded=true;
classDef core fill:#D4F1F9,stroke:#A7D8EA,stroke-width:2,rounded=true;
classDef support fill:#FFF8DC,stroke:#F9E79F,stroke-width:2,rounded=true;
classDef error fill:#FFE4E1,stroke:#FFB6B6,stroke-width:2,rounded=true;
classDef init fill:#E6E6FA,stroke:#D6C2FE,stroke-width:2,rounded=true;

F0[Group Wiki Repo Storage Move Spec\nqa/qa/specs/features/ee/api/3_create/wiki/group_wiki_repository_storage_move_spec.rb]
class F0 support
style F0 fill:#FFF8DC,stroke:#F9E79F,stroke-width:2,rounded=true

%% LOGICAL RELATIONSHIPS BETWEEN GROUPS

%% Object Storage CDN <-> Migration Helper
A1 -- "Links to object/blob storage for file URLs" --> C3
C3 -- "Migration helper may invoke CDN URL logic for uploads" --> A1

%% Migration Service <-> LocalAndRemoteStorageMigration Engines
B0 -- "Uses storage-type migration\ndrives artifact/pages/file storage migration" --> C0
B1 -- "May invoke artifact migration routines\nfor attachment references" --> C1

%% UpdateRepositoryStorageWorker/Methods integration
B3 -- "Schedules/calls MigrationService for project repo migration" --> B0

%% Reindexing and migrations for DB-level procedures
D0 -- "Supports backend migration scenarios\n(maintenance, performance during background migrations)" --> B0

%% Model concern is used where sharding keys are needed for storage
E0 -- "Concern included in models to provide sharding keys during migration/persistence" --> B0
E0 -- "Concern included in migration engines for sharding-awareness" --> C0
E0 -- "Concern included in attachment services for sharding" --> B1

%% Artifact/Pages migrater both use base migrater
C1 -- "Specializes BaseMigrater to CI artifacts" --> C0
C2 -- "Specializes BaseMigrater to Pages Deployments" --> C0

%% Uploads Migration Helper orchestrates all low-level migrations
C3 -- "Initializes migraters, coordinates batch upload migrations" --> C1
C3 -- "Initializes migraters, coordinates batch upload migrations" --> C2

%% SPEC connected for completeness
F0 -- "Covers integration for repo storage moves EE feature test" --> B3

%% CDN Concern interacts with external stores, which affects all uploads
A0 -- "CDN helpers, consulted by\nobject-attached services migration, uploaders" --> B1
A0 -- "CDN helpers, consulted by\nmigration helpers" --> C3

%% LEGEND

subgraph Z["Legend" ]
direction TB
style Z fill:#F8F8F8,stroke:#DDDDDD,rounded=true
    Z1[Core Domain Logic]
    class Z1 core
    Z2[Supporting/Utility]
    class Z2 support
    Z3[Data Structure/Concern]
    class Z3 data
    Z4[Error/Exception Handling]
    class Z4 error
    Z5[Setup/Initialization]
    class Z5 init
end

%% Proper node spacing for verticality
linkStyle default stroke-width:2,stroke:#BFD7EA
```