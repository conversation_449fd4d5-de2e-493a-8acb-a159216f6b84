```mermaid
flowchart TD
  %% Domain: Authentication, Authorization & User Management / User & Membership Management / Session & Account Management
  %% LAYOUT & COLORS
  %% All subgraphs and nodes are color coded according to domain role and styled per instructions
  %% Use VERTICAL layout
  %% Style nodes
  classDef core fill:#D4F1F9,stroke:#86b8cc,stroke-width:2px,rx:12,ry:12
  classDef support fill:#FFF8DC,stroke:#ffe4a1,stroke-width:2px,rx:12,ry:12
  classDef data fill:#E0F8E0,stroke:#90cfa0,stroke-width:2px,rx:12,ry:12
  classDef error fill:#FFE4E1,stroke:#e89c8c,stroke-width:2px,rx:12,ry:12
  classDef init fill:#E6E6FA,stroke:#a6a6ce,stroke-width:2px,rx:12,ry:12
  classDef groupfill fill:#F8F8F8,stroke:#dcdcdc,stroke-width:2px,rx:16,ry:16

  %% ====================================================================================
  %% 1. CORE USER ENTITY AND PASSWORD MGMT
  subgraph User_Core_Abstractions["User and Authentication Core" ]
    direction TB
    active_session[ActiveSession<br>Session management logic|app/models/active_session.rb]:::core
    user_password[EncryptedUserPassword<br>Password encryption concern|app/models/concerns/encrypted_user_password.rb]:::core
    restricted_signup[RestrictedSignup<br>Restrict sign-ups by domain|app/models/concerns/restricted_signup.rb]:::core
    recoverable_email[RecoverableByAnyEmail<br>Password recovery by any confirmed email|app/models/concerns/recoverable_by_any_email.rb]:::core
  end
  class User_Core_Abstractions groupfill

  %% 2. SESSION MGMT / STORES / STRUCTURES
  subgraph Session_Data_Structures["Session Data Structures & Stores" ]
    direction TB
    anon_session[AnonymousSession<br>Anonymous IP session tracking|lib/gitlab/anonymous_session.rb]:::data
    namespaced_store[NamespacedSessionStore<br>Namespaced session store abstraction|lib/gitlab/namespaced_session_store.rb]:::data
    session_cache[Sessions::CacheStore<br>Session storage in cache|lib/gitlab/sessions/cache_store.rb]:::data
    cache_store_coder[Sessions::CacheStoreCoder<br>Session coder/decoder|lib/gitlab/sessions/cache_store_coder.rb]:::data
  end
  class Session_Data_Structures groupfill

  %% 3. CONTROLLERS: USER MANAGEMENT, SESSIONS, PASSWORD, KEYS, ACCOUNT
  subgraph User_Mgmt_Controllers["User, Session and Account Controllers"]
    direction TB
    passwords_ctrl[PasswordsController<br>Password reset/edit|app/controllers/passwords_controller.rb]:::core
    unsubscribes_ctrl[UnsubscribesController<br>Email unsubscribe management|app/controllers/users/unsubscribes_controller.rb]:::core
    accounts_ctrl[Profiles::AccountsController<br>User account profile actions|app/controllers/profiles/accounts_controller.rb]:::core
    active_sessions_ctrl[ActiveSessionsController<br>User's active sessions UI|app/controllers/user_settings/active_sessions_controller.rb]:::core
    ssh_keys_ctrl[SshKeysController<br>User SSH key management|app/controllers/user_settings/ssh_keys_controller.rb]:::core
    gpg_keys_ctrl[GpgKeysController<br>User GPG key management|app/controllers/user_settings/gpg_keys_controller.rb]:::core
  end
  class User_Mgmt_Controllers groupfill

  %% 4. CONTROLLERS: EE Enterprise Edition
  subgraph EE_Controllers["Enterprise Edition Extensions"]
    direction TB
    ee_app_ctrl[EE::ApplicationController<br>EE application logic/extensions|ee/app/controllers/ee/application_controller.rb]:::support
    ee_sessions_ctrl[EE::ActiveSessionsController<br>SAML sessions, browser session info|ee/app/controllers/ee/user_settings/active_sessions_controller.rb]:::support
    credentials_inventory_ctrl[CredentialsInventoryActions<br>Credential revoke/filter inventory|ee/app/controllers/concerns/credentials_inventory_actions.rb]:::support
    identity_verif_ctrl[IdentityVerificationUser<br>EE identity verification concern|ee/app/controllers/concerns/identity_verification_user.rb]:::support
  end
  class EE_Controllers groupfill

  %% 5. SERVICES: USER-RELATED DOMAIN LOGIC
  subgraph User_Services["User & Membership Domain Services"]
    direction TB
    user_destroy_service[Users::DestroyService<br>Destructive user operations|app/services/users/destroy_service.rb]:::core
    ban_service[Users::BanService<br>Ban users|app/services/users/ban_service.rb]:::core
    unban_service[Users::UnbanService<br>Unban users|app/services/users/unban_service.rb]:::core
    unfollow_service[Users::UnfollowService<br>User unfollow logic|app/services/users/unfollow_service.rb]:::core
    migrate_to_ghost_service[Users::MigrateRecordsToGhostUserService<br>Ghost user migration|app/services/users/migrate_records_to_ghost_user_service.rb]:::core
    batch_status_cleaner[Users::BatchStatusCleanerService<br>Clean user status|app/services/users/batch_status_cleaner_service.rb]:::core
    destroy_session_service[Users::DestroySessionService<br>Remove user sessions|app/services/users/destroy_session_service.rb]:::core
    pin_revoke_service[Users::SupportPin::RevokeService<br>Revoke support PIN|app/services/users/support_pin/revoke_service.rb]:::core
    upsert_cc_validation[Users::UpsertCreditCardValidationService<br>Credit card validation for user|app/services/users/upsert_credit_card_validation_service.rb]:::core
    reset_feed_token[Users::ResetFeedTokenService<br>Reset user feed token|app/services/users/reset_feed_token_service.rb]:::core
    ee_deactivate_enterprise[EE::Users::DeactivateEnterpriseService<br>EE: deactivate enterprise accounts|ee/app/services/users/deactivate_enterprise_service.rb]:::core
  end
  class User_Services groupfill

  %% 6. SERVICES: KEYS & EMAILS MGMT
  subgraph Key_Email_Services["Key and Email Services"]
    direction TB
    gpg_destroy_service[GpgKeys::DestroyService<br>Destroy GPG key & invalidate signatures|app/services/gpg_keys/destroy_service.rb]:::core
    key_destroy_service[Keys::DestroyService<br>Destroy SSH key|app/services/keys/destroy_service.rb]:::core
    email_destroy_service[Emails::DestroyService<br>Destroy/remove emails|app/services/emails/destroy_service.rb]:::core
  end
  class Key_Email_Services groupfill

  %% 7. EE: ABUSE PREVENTION SERVICES
  subgraph EE_Abuse_Prev_Services["EE Abuse Prevention & Throttling"]
    direction TB
    ee_git_abuse_app_throttle[EE::Users::GitAbuse::ApplicationThrottleService<br>Throttling & user banning app-level|ee/app/services/users/abuse/git_abuse/application_throttle_service.rb]:::support
    ee_git_abuse_base_throttle[EE::Users::GitAbuse::BaseThrottleService<br>Base for throttling/banning|ee/app/services/users/abuse/git_abuse/base_throttle_service.rb]:::support
    ee_git_abuse_namespace_throttle[EE::Users::GitAbuse::NamespaceThrottleService<br>Namespace-level throttling|ee/app/services/users/abuse/git_abuse/namespace_throttle_service.rb]:::support
  end
  class EE_Abuse_Prev_Services groupfill

  %% 8. WORKERS: ASYNC/JOB QUEUE SUPPORT
  subgraph Workers["Background Workers"]
    direction TB
    delete_user_worker[DeleteUserWorker<br>Enqueue user deletion|app/workers/delete_user_worker.rb]:::support
    ssh_expired_worker[SshKeys::ExpiredNotificationWorker<br>Cron SSH expiry notification|app/workers/ssh_keys/expired_notification_worker.rb]:::support
  end
  class Workers groupfill

  %% 9. HELPERS & VIEW SUPPORT
  subgraph Helpers_and_View["Helpers & View Utilities"]
    direction TB
    sessions_helper[SessionsHelper<br>Session-related presentation and utilities|app/helpers/sessions_helper.rb]:::support
  end
  class Helpers_and_View groupfill

  %% 10. EE MODELS EXTENSIONS
  subgraph EE_Models["Enterprise Edition Model Extensions"]
    direction TB
    ee_active_session[EE::ActiveSession<br>EE extension of session model|ee/app/models/ee/active_session.rb]:::support
  end
  class EE_Models groupfill

  %% 11. CLIENT/MANUAL TESTING SUPPORT
  subgraph QA_and_Specs["QA & Specs: Acceptance, Integration, Feature"]
    direction TB
    spec_two_factor[Specs: TwoFactorAuthsController<br>2FA integration/feature specs|spec/controllers/profiles/two_factor_auths_controller_spec.rb]:::support
    spec_admin_users_ctrl[Specs: Admin::UsersController<br>Admin/user CRUD and password specs|spec/controllers/admin/users_controller_spec.rb]:::support
    spec_acc_recovery[Specs: Account Recovery Callout<br>Feature test: 2FA/account recovery|ee/spec/features/account_recovery_regular_check_spec.rb]:::support
    spec_password[Specs: UserSettings Password<br>Password feature spec|ee/spec/features/user_settings/password_spec.rb]:::support
    qa_password[QA: Profile Password Page<br>Password UI & manual QA|qa/qa/page/profile/password.rb]:::support
    qa_accounts_show[QA: Profile Accounts Show<br>Account page manual QA view|qa/qa/page/profile/accounts/show.rb]:::support
  end
  class QA_and_Specs groupfill

  %% ====================================================================================
  %% LOGICAL RELATIONSHIPS / INTERACTIONS

  %% USER ACCOUNT & SESSION MANAGEMENT
  accounts_ctrl -->|manage support PIN|pin_revoke_service
  accounts_ctrl -->|calls|user_destroy_service
  accounts_ctrl -->|uses|active_session
  active_sessions_ctrl --> active_session
  active_sessions_ctrl --> destroy_session_service
  active_session -->|wraps session storage|namespaced_store
  active_session -->|session id ops|session_cache
  active_session -->|session decode/encode|cache_store_coder
  destroy_session_service --> active_session

  ee_active_session --> active_session

  ## Password & Recovery
  passwords_ctrl --> user_password
  passwords_ctrl --> recoverable_email
  recoverable_email --> user_password
  qa_password --> passwords_ctrl
  spec_password --> passwords_ctrl

  ## Session Structures
  namespaced_store --> session_cache
  session_cache --> cache_store_coder
  active_session --> anon_session
  sessions_helper --> active_session

  ## Signup Restrictions
  accounts_ctrl --> restricted_signup

  ## Account Removal / Destructive Operations
  user_destroy_service --> migrate_to_ghost_service
  user_destroy_service --> batch_status_cleaner
  user_destroy_service --> destroy_session_service
  user_destroy_service --> delete_user_worker
  delete_user_worker --> user_destroy_service

  migrate_to_ghost_service --> batch_status_cleaner

  ## Key & Email Destruction
  ssh_keys_ctrl -->|manages|key_destroy_service
  ssh_keys_ctrl -->|notifies on expiry|ssh_expired_worker
  gpg_keys_ctrl -->|manages|gpg_destroy_service
  key_destroy_service -->|calls|user_destroy_service
  gpg_destroy_service -->|calls|user_destroy_service

  gpg_destroy_service -->|invalidates via|key_destroy_service
  emails_destroy_service --> user_destroy_service

  ## Feed token reset & credit card validation
  accounts_ctrl --> reset_feed_token
  accounts_ctrl --> upsert_cc_validation

  ## User states (ban, unban, unfollow)
  accounts_ctrl --> ban_service
  accounts_ctrl --> unban_service
  accounts_ctrl --> unfollow_service

  ban_service -->|calls|unfollow_service
  unban_service -->|calls|ban_service

  ## Support PIN
  pin_revoke_service --> accounts_ctrl

  ## Email Unsubscribe
  unsubscribes_ctrl --> user_destroy_service

  ## QA/spec coverage (logical, high-level)
  qa_accounts_show --> accounts_ctrl
  qa_password --> passwords_ctrl
  spec_two_factor --> accounts_ctrl
  spec_two_factor --> passwords_ctrl
  spec_admin_users_ctrl --> accounts_ctrl
  spec_acc_recovery --> passwords_ctrl

  ## Enterprise Edition (EE) Extensions & Interactions
  ee_app_ctrl --> accounts_ctrl
  ee_sessions_ctrl --> active_sessions_ctrl
  ee_sessions_ctrl --> active_session
  credentials_inventory_ctrl --> ssh_keys_ctrl
  credentials_inventory_ctrl --> gpg_keys_ctrl
  identity_verif_ctrl --> accounts_ctrl

  ## EE: Abuse throttling services
  ee_git_abuse_app_throttle --> ban_service
  ee_git_abuse_base_throttle --> ee_git_abuse_app_throttle
  ee_git_abuse_namespace_throttle --> ee_git_abuse_base_throttle
  ee_deactivate_enterprise --> user_destroy_service

  ## Workers interactions
  ssh_expired_worker --> ssh_keys_ctrl

  ## Helpers utilized in controllers/views
  sessions_helper --> active_sessions_ctrl
  sessions_helper --> accounts_ctrl

  %% GPG/SSH KEY mgmt across controllers
  ssh_keys_ctrl --> key_destroy_service
  gpg_keys_ctrl --> gpg_destroy_service
  key_destroy_service --> batch_status_cleaner

  ## Account/Password management UI & test coverage
  qa_accounts_show --> accounts_ctrl
  qa_password --> passwords_ctrl
  spec_password --> passwords_ctrl
  spec_admin_users_ctrl --> accounts_ctrl

  %% ====================================================================================

  %% CORE CONCEPTS GROUP LABELS
  User_Core_Abstractions:::groupfill
  Session_Data_Structures:::groupfill
  User_Mgmt_Controllers:::groupfill
  EE_Controllers:::groupfill
  User_Services:::groupfill
  Key_Email_Services:::groupfill
  EE_Abuse_Prev_Services:::groupfill
  Workers:::groupfill
  Helpers_and_View:::groupfill
  EE_Models:::groupfill
  QA_and_Specs:::groupfill
```