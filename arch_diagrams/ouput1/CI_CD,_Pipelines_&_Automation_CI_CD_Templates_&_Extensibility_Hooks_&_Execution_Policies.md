```mermaid
flowchart TD
  %%--- Subgraph: Core Domain Concepts -------------------------------------------
  subgraph CI_CD_Core_Logic["CI/CD Templates & Extensibility — Core Domain" ]
    direction TB
    style CI_CD_Core_Logic fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded

    eeGitlabCiConfigEE[ "ConfigEE Extended Pipeline Processing
      - Extends pipeline config processing for EE features
      - Handles required includes, security orchestration, enforcement 
      - Adds Policy Execution Stage logic and custom error handling" ]
    style eeGitlabCiConfigEE fill:#D4F1F9,stroke:#3090C7,stroke-width:2,stroke-dasharray: 4 2,rx:15

    gitlabConfigEntrySimplifiable["Simplifiable Entry Abstraction
      - Flexible data structure for dynamic config entries
      - Implements EntryStrategy for transformation
      - Enables conditional and composable config parsing" ]
    style gitlabConfigEntrySimplifiable fill:#E0F8E0,stroke:#60BC60,stroke-width:2,rx:15

    ciJobTokenInternalEventsTracking["Job Token Internal Events Tracking
      - Monitors changes to CI/CD job token scope settings
      - Leverages InternalEventsTracking concern
      - Emits domain events for pipeline security policy changes" ]
    style ciJobTokenInternalEventsTracking fill:#D4F1F9,stroke:#3090C7,stroke-width:2,rx:15

  end

  %%--- Subgraph: Supporting & Utility Files -------------------------------------
  subgraph Utility_Files["Utility, Integration, and Support Files" ]
    direction TB
    style Utility_Files fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,rounded

    batchLoaderInitializer["Batch Loader Initializer
      - Optimizes batch data loading without method replacement
      - Sets performance and loading strategies for config structures" ]
    style batchLoaderInitializer fill:#FFF8DC,stroke:#FFD580,stroke-width:2,rx:15

  end

  %%--- Subgraph: Automation and Onboarding --------------------------------------
  subgraph Automation_Onboarding["Automation / Onboarding Integration" ]
    direction TB
    style Automation_Onboarding fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded

    onboardingCreateIterableTriggerWorker["Create Iterable Trigger Worker
      - Initiates onboarding and external automation triggers
      - Delegates orchestration to CreateIterableTriggerService
      - Integrates CI/CD pipeline with onboarding hooks" ]
    style onboardingCreateIterableTriggerWorker fill:#D4F1F9,stroke:#3090C7,stroke-width:2,rx:15

    onboardingTriggerErrorHandling["CreateIterableTriggerError
      - Worker-level domain exception for onboarding pipeline hooks" ]
    style onboardingTriggerErrorHandling fill:#FFE4E1,stroke:#C38181,stroke-width:2,rx:15

  end

  %%--- LOGICAL RELATIONSHIPS ----------------------------------------------------

  %% - CI config processing extended across codebase using structure abstractions
  eeGitlabCiConfigEE -->|uses entry strategies| gitlabConfigEntrySimplifiable
  eeGitlabCiConfigEE -->|enforces| ciJobTokenInternalEventsTracking
  eeGitlabCiConfigEE -->|initializes with| batchLoaderInitializer

  %% - Internal event tracking enriches pipeline policy handling
  ciJobTokenInternalEventsTracking -->|monitors event| eeGitlabCiConfigEE

  %% - Simplifiable Entry pattern enables extensibility for config
  gitlabConfigEntrySimplifiable -->|parses and transforms| batchLoaderInitializer

  %% - Onboarding worker uses CI/CD policy config for pipeline triggers
  onboardingCreateIterableTriggerWorker -->|triggers pipeline with| eeGitlabCiConfigEE
  onboardingCreateIterableTriggerWorker -->|handles config via| gitlabConfigEntrySimplifiable
  onboardingCreateIterableTriggerWorker --> onboardingTriggerErrorHandling

  %% - Loader optimizes all config structure initializations
  batchLoaderInitializer -.-> eeGitlabCiConfigEE
  batchLoaderInitializer -.-> gitlabConfigEntrySimplifiable

  %%--- CONCEPTUAL DATA FLOW & ABSTRACTIONS --------------------------------------

  subgraph Domain_Data_Structures["Core Data Structures & Abstractions" ]
    direction TB
    style Domain_Data_Structures fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2,rounded

    entryStrategy["EntryStrategy name, klass, condition
      - Strategy pattern for dynamic config entry parsing" ]
    style entryStrategy fill:#E0F8E0,stroke:#60BC60,stroke-width:2,rv:15

    requiredIncludesData["Required Includes & Policy Configs
      - Data specifying security, compliance, and custom policies 
      - Includes pipeline execution and external orchestration details" ]
    style requiredIncludesData fill:#E0F8E0,stroke:#60BC60,stroke-width:2,rv:15

    jobTokenScopeEvent["JobTokenScopeChangeEvent
      - Domain event for tracking job token policy scope changes" ]
    style jobTokenScopeEvent fill:#E0F8E0,stroke:#60BC60,stroke-width:2,rv:15

  end

  %% Structural/Abstraction Relationships
  gitlabConfigEntrySimplifiable --> entryStrategy
  eeGitlabCiConfigEE --> requiredIncludesData
  ciJobTokenInternalEventsTracking --> jobTokenScopeEvent
  onboardingCreateIterableTriggerWorker --> requiredIncludesData
  onboardingCreateIterableTriggerWorker --> jobTokenScopeEvent

  %%--- Policy and Hook Execution Relationships ----------------------------------

  subgraph Policy_Hook_Execution["Policy / Hook Execution Collaboration" ]
    direction TB
    style Policy_Hook_Execution fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2,rounded

    executionPolicyEnforcement["Pipeline Execution Policy Enforcement
      - Injects custom stages into CI pipeline for compliance
      - Validates stage conditions and triggers error handling" ]
    style executionPolicyEnforcement fill:#E6E6FA,stroke:#A991D4,stroke-width:2,rx:15

    onboardingOrchestration["Onboarding & External Hook Orchestration
      - Raises errors for onboarding/extensibility failures
      - Coordinates with Pipeline config & events" ]
    style onboardingOrchestration fill:#E6E6FA,stroke:#A991D4,stroke-width:2,rx:15

  end

  eeGitlabCiConfigEE --> executionPolicyEnforcement
  executionPolicyEnforcement --> onboardingOrchestration
  onboardingCreateIterableTriggerWorker --> onboardingOrchestration
  executionPolicyEnforcement -->|raises| onboardingTriggerErrorHandling

  %%--- INITIALIZATION SEQUENCE --------------------------------------------------
  batchLoaderInitializer -->|enables| executionPolicyEnforcement

  %%--- GROUP-TO-GROUP RELATIONSHIPS --------------------------------------------

  CI_CD_Core_Logic --> Utility_Files
  CI_CD_Core_Logic --> Automation_Onboarding
  Automation_Onboarding --> Policy_Hook_Execution
  CI_CD_Core_Logic --> Domain_Data_Structures
  Policy_Hook_Execution --> Domain_Data_Structures

  %%--- Node Classes for THEMING -------------------------------------------------
  classDef blue fill:#D4F1F9,stroke:#3090C7,stroke-width:2,rx:15
  classDef yellow fill:#FFF8DC,stroke:#FFD580,stroke-width:2,rx:15
  classDef green fill:#E0F8E0,stroke:#60BC60,stroke-width:2,rx:15
  classDef red fill:#FFE4E1,stroke:#C38181,stroke-width:2,rx:15
  classDef purple fill:#E6E6FA,stroke:#A991D4,stroke-width:2,rx:15
  classDef subgraphBg fill:#F8F8F8,stroke:#EBEBEB,stroke-width:2
```