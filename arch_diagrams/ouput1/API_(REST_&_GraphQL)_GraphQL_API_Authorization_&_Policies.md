```mermaid
flowchart TB
  %% Layout direction
  direction TB

  %% COLOR DEFINITIONS
  %% Core domain: pastel blue (#D4F1F9)
  %% Supporting/utility: pastel yellow (#FFF8DC)
  %% Data structures: pastel green (#E0F8E0)
  %% Error handling: pastel red (#FFE4E1)
  %% Initialization/setup: pastel purple (#E6E6FA)
  %% Grouping: very light gray (#F8F8F8) with pastel borders

  %% LAYERS / CORE GROUPS

  subgraph LEGEND [GraphQL API Authorization & Policies Domain]
    style LEGEND fill:#F8F8F8,stroke:#D4F1F9,stroke-width:3,stroke-dasharray: 5 5
    direction TB
  end

  subgraph Core_Policy_Auth["Core Policies and Authorization"]
    style Core_Policy_Auth fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2
    NP[NamespacePolicy<br/>app/policies/namespace_policy.rb]:::corefile
    BA[BasePolicy]:::corefile
    OA[ObjectAuthorization<br/>lib/gitlab/graphql/authorize/object_authorization.rb]:::corefile
    AR[AuthorizeResource<br/>lib/gitlab/graphql/authorize/authorize_resource.rb]:::corefile
    FE[FieldExtension<br/>lib/gitlab/graphql/authorize/field_extension.rb]:::corefile
    TBI[BaseInterface<br/>app/graphql/types/base_interface.rb]:::corefile
    NP --> BA
  end

  subgraph Auth_Errors["Error Handling"]
    style Auth_Errors fill:#F8F8F8,stroke:#FFE4E1,stroke-width:2
    SGE[StandardGraphqlError<br/>lib/gitlab/graphql/standard_graphql_error.rb]:::errorfile
  end

  subgraph Mutations_Spam["Spam Protection for Mutations"]
    style Mutations_Spam fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2
    MSP[SpamProtection<br/>app/graphql/mutations/concerns/mutations/spam_protection.rb]:::supportfile
  end

  subgraph Policy_Construct["Enterprise Security Policy Construction"]
    style Policy_Construct fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2
    CSP[ConstructSecurityPolicies<br/>ee/app/graphql/resolvers/concerns/construct_security_policies.rb]:::corefile
  end

  subgraph GraphQL_Channel["GraphQL Channel Extensions EE"]
    style GraphQL_Channel fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2
    GCH[GraphqlChannel<br/>ee/app/channels/ee/graphql_channel.rb]:::initfile
  end

  subgraph Call_Limits["Field Query Limits"]
    style Call_Limits fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2
    FCC[FieldCallCount<br/>lib/gitlab/graphql/limit/field_call_count.rb]:::supportfile
  end

  subgraph Testing["Authorization/Policy Testing"]
    style Testing fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2
    AUTHRES[AuthorizeResource Spec<br/>spec/lib/gitlab/graphql/authorize/authorize_resource_spec.rb]:::datafile
  end

  %% NODE STYLES
  classDef corefile fill:#D4F1F9,stroke:#3896b5,stroke-width:2,rx:12,ry:12
  classDef supportfile fill:#FFF8DC,stroke:#cfc798,stroke-width:2,rx:12,ry:12
  classDef datafile fill:#E0F8E0,stroke:#98cfa4,stroke-width:2,rx:12,ry:12
  classDef errorfile fill:#FFE4E1,stroke:#e8a6a0,stroke-width:2,rx:12,ry:12
  classDef initfile fill:#E6E6FA,stroke:#a7a7cf,stroke-width:2,rx:12,ry:12

  %% --- LOGICAL RELATIONSHIPS AND INTERACTIONS ---

  %% POLICY INTEGRATION
  NP -- Uses policy rules, extends --> BA
  TBI -- Defines field authorization with --> AR
  AR -- Relies on --> OA
  OA -- Provides permission checking for --> AR
  AR -- Error handling via --> SGE
  FE -- Wraps field resolver, uses --> OA
  FE -- Wraps field resolver, uses --> AR
  FE -- Data visibility for --> TBI

  %% FIELD/LIST/CONNECTION REDACTION AND AUTHORIZATION
  FE -- Handles unauthorized data, signals errors to --> SGE
  TBI -- Field-level authorization interacts with --> AR
  TBI -- Data masking via --> FE

  %% MUTATION SPAM PROTECTION
  MSP -- For mutation security, uses error types --> SGE
  MSP -- Designed for use in mutation resolvers (not directly coupled, but part of GraphQL API architecture)
  MSP -- Mitigates abuse for authorization entrypoints

  %% ENTERPRISE POLICY CONSTRUCTION
  CSP -- Constructs/derives policies for --> NP
  CSP -- Used by advanced resolvers, communicates with --> FE
  CSP -- Generates data for policy evaluation consumed by --> OA

  %% CHANNEL LEVEL AUTHORIZATION (EE)
  GCH -- Modifies authorization scopes for --> TBI
  GCH -- Integrates with channel-level GraphQL authorization, allowing features like AI awareness
  GCH -- Extends authorization layers for runtime context

  %% FIELD CALL LIMITS AS DEFENSE-IN-DEPTH
  FCC -- Guards field execution, raises errors via --> SGE
  FCC -- Applied as GraphQL field extension on queries tied to --> TBI
  FCC -- Integrated with other field extensions like --> FE

  %% TESTING/MODEL VALIDATION
  AUTHRES -- Specification/validation for --> AR
  AUTHRES -- Simulates user access and scope rules managed by --> OA
  AUTHRES -- Ensures error scenarios via --> SGE

  %% ----- GROUP/ABSTRACTION LABELS -----

  %% POLICIES and AUTHORIZATION ABSTRACTION
  BA -- Foundation for --> NP
  NP -- Defines project/group permissions for --> AR

  %% DOMAIN DATA FLOW

  %% 1. Query/mutation resolves...
  %% 2. FieldExtensions (FE, FCC) enforce limits and authorization
  %% 3. Authorization flows through TBI, AR, OA and NP/BA, leveraging CSP in EE
  %% 4. Mutations may engage MSP for spam/captcha verification
  %% 5. Errors unified through SGE

  %% DATA/ERROR FLOW
  TBI -- Request hits --> FE
  FE -- On unauthorized, throws --> SGE
  FCC -- On limit exceeded, throws --> SGE
  MSP -- On spam, throws --> SGE
  AR -- Fails authorization, throws --> SGE

  %% EE/ENTERPRISE CONNECTIONS
  GCH -- Adds context for --> TBI

  %% ConstructSecurityPolicies can inform NamespacePolicy logic
  CSP -- Policy building feeds --> NP

  %% DATA STRUCTURES & TRANSFORMATIONS
  OA -- Translates user+object+action into permission check result

  %% Abstraction Notes
  BA -- Policy abstraction for --> AR
  FE -- Extension pattern, plugs into --> TBI and GraphQL fields

  %% RIGIDLY GROUPED FILES BY TYPE
  Core_Policy_Auth
  Auth_Errors
  Mutations_Spam
  Policy_Construct
  GraphQL_Channel
  Call_Limits
  Testing

  %% INTER-SUBGRAPH CONNECTORS (using invisible links for layout clarity)
  Core_Policy_Auth -.-> Mutations_Spam
  Core_Policy_Auth -.-> Policy_Construct
  Core_Policy_Auth -.-> GraphQL_Channel
  Core_Policy_Auth -.-> Call_Limits
  Core_Policy_Auth -.-> Testing
```