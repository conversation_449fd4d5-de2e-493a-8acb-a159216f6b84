```mermaid
flowchart TD
  %% VERTICAL LAYOUT
  direction TB

  %% DOMAIN: Authentication, Authorization & User Management / Authentication / Token & Session Management

  %% CORE CONCEPTS & GROUPED FEATURES %%%%%%%%%%%%%%%%%%%%%%%%%
  %% COLORS %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
  %% Core domain files:       #D4F1F9 (Pastel Blue)
  %% Supporting/utility:      #FFF8DC (Pastel Yellow)
  %% Data structure files:    #E0F8E0 (Pastel Green)
  %% Error handling:          #FFE4E1 (Pastel Red)
  %% Initialization/setup:    #E6E6FA (Pastel Purple)
  %% Logical Groupings:       #F8F8F8 background, pastel borders.

  %% TOKEN DATA STRUCTURES AND MANIPULATION
  subgraph TOKEN_DATA_STRUCTURES_AND_FIELD_MANAGEMENT["Token Data Structures & Field Management"]
    style TOKEN_DATA_STRUCTURES_AND_FIELD_MANAGEMENT fill:#F8F8F8,stroke:#30AFDB,stroke-width:2,rounded=true

    tfb["TokenField::Base":::datastruct]
    tfd["TokenField::Digest":::datastruct]
    tfi["TokenField::Insecure":::datastruct]
    tfe["TokenField::Encrypted":::datastruct]
    tfph["TokenField::PrefixHelper":::utility]
    tfen["TokenField::EncryptionHelper":::utility]
    tfgrt["TokenField::Generator::RoutableToken":::datastruct]

    tfb --fabrication, base abstraction--> tfd
    tfb --fabrication, base abstraction--> tfi
    tfb --fabrication, base abstraction--> tfe
    tfb --token prefix utility--> tfph
    tfe --token encryption/decryption--> tfen
    tfgrt --generates routable tokens--> tfb
    tfd --finds digest tokens--> tfb
    tfe --finds encrypted tokens--> tfb
    tfi --manages plaintext tokens--> tfb
    tfe --fallback strategies--> tfi
  end

  classDef datastruct fill:#E0F8E0,stroke:#79D489,stroke-width:1,rounded=true;
  classDef utility fill:#FFF8DC,stroke:#FFD699,stroke-width:1,rounded=true;

  %% TOKEN TYPES: ABSTRACTIONS & REVOCATION
  subgraph TOKEN_TYPES_AND_REVOCATION["Token Types & Revocation Abstractions"]
    style TOKEN_TYPES_AND_REVOCATION fill:#F8F8F8,stroke:#30AFDB,stroke-width:2,rounded=true

    t_deploy["Tokens::DeployToken":::domainclass]
    t_personal["Tokens::PersonalAccessToken":::domainclass]
    t_feed["Tokens::FeedToken":::domainclass]
    t_runner["Tokens::RunnerAuthenticationToken":::domainclass]
    t_ci_job["Tokens::CiJobToken":::domainclass]
    t_ci_trigger["Tokens::CiTriggerToken":::domainclass]
    t_ff_client["Tokens::FeatureFlagsClientToken":::domainclass]
    t_incoming_email["Tokens::IncomingEmailToken":::domainclass]
    t_oauth_app_secret["Tokens::OauthApplicationSecret":::domainclass]
    t_cluster_agent["Tokens::ClusterAgentToken":::domainclass]
    t_gitlab_session["Tokens::GitlabSession":::domainclass]
    t_jwt["Tokens::Jwt":::domainclass]

    agnostid["AgnosticTokenIdentifier":::utility]

    t_deploy --token_field::prefix_helper--> tfph
    t_feed --token_field::prefix_helper--> tfph
    t_oauth_app_secret --doorkeeper-secret-format--> doorkeeperunique
    t_cluster_agent --prefix helper--> tfph
    t_runner --prefix helper--> tfph
    t_ci_job --prefix helper--> tfph
    t_ci_trigger --prefix helper--> tfph
    t_ff_client --prefix helper--> tfph
    t_incoming_email --prefix helper--> tfph
    t_jwt --JWT signature/claims--> jwt_token
    agnostid --delegates detection--> t_deploy
    agnostid --delegates detection--> t_feed
    agnostid --delegates detection--> t_personal
    agnostid --delegates detection--> t_ci_job
    agnostid --delegates detection--> t_ci_trigger
    agnostid --delegates detection--> t_oauth_app_secret
    agnostid --delegates detection--> t_cluster_agent
    agnostid --delegates detection--> t_runner
  end

  classDef domainclass fill:#D4F1F9,stroke:#7AC5DD,stroke-width:1,rounded=true;

  %% JWT & TOKEN ENCODING/DECODING %%%%%%%%%%%%%%%%%%%%%%%%%%%%
  subgraph JWT_AND_TOKEN_SERIALIZATION["JWT & Token Serialization"]
    style JWT_AND_TOKEN_SERIALIZATION fill:#F8F8F8,stroke:#30AFDB,stroke-width:2,rounded=true
    jwt_token["Gitlab::JWTToken":::core]
    jwt_auth["Gitlab::JwtAuthenticatable":::utility]
    jsonweb_token_token["JSONWebToken::Token":::datastruct]
    jsonweb_token_rsa["JSONWebToken::RSAToken":::datastruct]
    authn_jwt["Tokens::Jwt":::domainclass]
    ee_ci_jwt["EE::Gitlab::Ci::Jwt":::core]

    authn_jwt --encodes/decodes via--> jsonweb_token_token
    authn_jwt --validates w/ public keys--> jsonweb_token_rsa
    jwt_token --base class--> jsonweb_token_token
    jwt_token --utility for HMAC decoding--> jwt_auth
    ee_ci_jwt --extends and overrides--> jwt_token
    jsonweb_token_rsa --inherits--> jsonweb_token_token
  end

  classDef core fill:#D4F1F9,stroke:#7AC5DD,stroke-width:1,rounded=true;

  %% AUTHN: API TOKEN RESOLUTION & LOCATION %%%%%%%%%%%%%%%%%%%
  subgraph API_TOKEN_LOCATION_AND_RESOLUTION["API Token Location & Resolution"]
    style API_TOKEN_LOCATION_AND_RESOLUTION fill:#F8F8F8,stroke:#30AFDB,stroke-width:2,rounded=true
    api_token_locator["APIAuthentication::TokenLocator":::utility]
    api_token_resolver["APIAuthentication::TokenResolver":::utility]

    api_token_locator --extract credentials--> api_token_resolver
    api_token_resolver --resolves tokens for--> agnostid
  end

  %% DOORKEEPER/OAUTH2 TOKEN SECRET MANAGEMENT %%%%%%%%%%%%%%%%
  subgraph OAUTH_TOKEN_MANAGEMENT["OAuth Token Management"]
    style OAUTH_TOKEN_MANAGEMENT fill:#F8F8F8,stroke:#30AFDB,stroke-width:2,rounded=true
    doorkeeperunique["DoorkeeperSecretStoring::Token::UniqueApplicationToken":::utility]
    authz_reset_secret["Authz::Applications::ResetSecretService":::core]
    ee_app_create["EE::Applications::CreateService":::core]

    t_oauth_app_secret --uses prefix from--> doorkeeperunique
    authz_reset_secret --resets secrets for--> doorkeeperunique
    ee_app_create --creates and audits app tokens--> doorkeeperunique
  end

  %% TOKEN AUTHENTICATION, EXTRACTION & VALIDATION %%%%%%%%%%%%
  subgraph TOKEN_AUTHENTICATION_AND_VALIDATION["Token Authentication & Validation"]
    style TOKEN_AUTHENTICATION_AND_VALIDATION fill:#F8F8F8,stroke:#30AFDB,stroke-width:2,rounded=true
    auth_finders["Auth::AuthFinders":::core]
    request_auth["Auth::RequestAuthenticator":::core]
    access_token_validator["AccessTokenValidationService":::core]
    dpop_service["Auth::DpopAuthenticationService":::core]

    request_auth --uses finders--> auth_finders
    request_auth --delegates validation--> access_token_validator
    request_auth --calls sessionless user extraction--> auth_finders
    dpop_service --Proof of Possession implementation--> request_auth
    api_token_resolver --token input for--> request_auth
  end

  %% SESSION & STATEFUL AUTHN MANAGEMENT %%%%%%%%%%%%%%%%%%%%%%
  subgraph SESSION_MANAGEMENT["Session Management"]
    style SESSION_MANAGEMENT fill:#F8F8F8,stroke:#A494C1,stroke-width:2,rounded=true
    ns_session["EE::Gitlab::Session":::core]
    session_expiry["SessionExpireFromInitEnforcer":::core]
    session_store_builder["Sessions::StoreBuilder":::utility]
    gitlab_session_token["Tokens::GitlabSession":::domainclass]
    sessionless_auth["Concerns::SessionlessAuthentication":::utility]
    smartcard_session["Gitlab::Auth::Smartcard::Session":::utility]

    ns_session --session id for worker--> SESSION_MANAGEMENT
    session_store_builder --builds session storage--> ns_session
    gitlab_session_token --extract session id from token--> ns_session
    session_expiry --enforces session expiration--> ns_session
    smartcard_session --manages active smartcard sessions--> ns_session
    sessionless_auth --handles stateless token auth--> request_auth
    sessionless_auth --sign-in logic--> request_auth
  end

  %% USER, GROUP, AND KEY MODELS & HELPERS %%%%%%%%%%%%%%%%%%%
  subgraph DOMAIN_MODELS_AND_HELPERS["Models & Helpers"]
    style DOMAIN_MODELS_AND_HELPERS fill:#F8F8F8,stroke:#30AFDB,stroke-width:2,rounded=true
    gdeploy_model["GroupDeployToken model":::datastruct]
    key_ee["EE::Key model/ext":::datastruct]
    access_tokens_helper["AccessTokensHelper":::utility]
    authorized_keys["Gitlab::AuthorizedKeys":::datastruct]

    gdeploy_model --relationship to group tokens--> t_deploy
    key_ee --auth key integration--> tokens_ci
    authorized_keys --validates/updates keys file--> key_ee
    access_tokens_helper --UI support for token mgmt--> t_personal
    authorized_keys --facilitates SSH/Deploy token integration--> t_deploy
  end

  %% ERROR HANDLING DEFINITIONS %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
  subgraph ERROR_HANDLING["Errors & Exception Classes"]
    style ERROR_HANDLING fill:#F8F8F8,stroke:#EA97A1,stroke-width:2,rounded=true
    auth_errors["AuthFinders: AuthenticationError, TokenNotFoundError, ExpiredError, RevokedError, UnauthorizedError":::errorclass]
    too_many_ips["Auth::TooManyIps":::errorclass]
    devise_failure["Gitlab::DeviseFailure":::errorclass]

    auth_finders --raises--> auth_errors
    request_auth --may raise--> auth_errors
    request_auth --may raise--> too_many_ips
    devise_failure --handles warden+devise failures--> SESSIONS_CONTROLLERS
  end

  classDef errorclass fill:#FFE4E1,stroke:#EA97A1,stroke-width:1,rounded=true;

  %% CONTROLLERS & REQUEST ENTRYPOINTS %%%%%%%%%%%%%%%%%%%%%%%%
  subgraph SESSIONS_CONTROLLERS["Session & Token Authentication Controllers"]
    style SESSIONS_CONTROLLERS fill:#F8F8F8,stroke:#30AFDB,stroke-width:2,rounded=true

    sessions["SessionsController":::core]
    admin_sessions["Admin::SessionsController":::core]
    ee_sessions["EE::SessionsController":::core]
    jwt_ctrl["JwtController":::core]
    admin_creds["Admin::CredentialsController":::core]
    confirmations_ee["EE::ConfirmationsController":::core]
    well_known_ctrl["WellKnownController":::core]
    jwks_ctrl["EE::JwksController":::core]
    skips_signedin["SkipsAlreadySignedInMessage concern":::utility]
    known_signin["KnownSignIn concern":::utility]
  end

  %% CONTROLLER-CODE CONCERNS & MAILERS %%%%%%%%%%%%%%%%%%%%%%%
  subgraph AUTHN_CONTROLLER_SUPPORT["Controller Concerns & Mailers"]
    style AUTHN_CONTROLLER_SUPPORT fill:#F8F8F8,stroke:#FFD699,stroke-width:2,rounded=true
    devise_mailer["DeviseMailer":::utility]
  end

  %% SUPPORTING LIBRARIES & MISC %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
  subgraph SUPPORT_LIBRARIES_AND_VENDOR["Crypto, PBKDF2, Subscriptions, Smartcard"]
    style SUPPORT_LIBRARIES_AND_VENDOR fill:#F8F8F8,stroke:#30AFDB,stroke-width:2,rounded=true
    pbkdf2mod["devise/pbkdf2_encryptable/model":::utility]
    pbkdf2enc["devise/pbkdf2_encryptable/encryptable":::utility]
    pbkdf2sha["devise/pbkdf2_encryptable/encryptors/pbkdf2_sha512":::utility]
    smartcard_cert["Gitlab::Auth::Smartcard::Certificate":::core]
    smartcard_session2["Gitlab::Auth::Smartcard::Session":::utility]
    smartcard_san["Gitlab::Auth::Smartcard::SANExtension":::utility]
    gitlab_subs_int_auth["GitlabSubscriptions::API::Internal::Auth":::utility]
    gitlab_exclusive_lease["EE::Gitlab::ExclusiveLease":::utility]
  end

  %% TEST FILES OPTIONAL, for completeness
  subgraph TEST_SUPPORT["Test Specs"]
    style TEST_SUPPORT fill:#F8F8F8,stroke:#D0D0D0,stroke-width:1,rounded=true
    authorized_keys_spec["authorized_keys_spec":::utility]
    auth_spec["auth_spec":::utility]
    smartcard_san_spec["smartcard_san_extension_spec":::utility]
  end


  %% LOGICAL RELATIONSHIPS / INTERACTIONS %%%%%%%%%%%%%%%%%%%%%

  %% CONTROLLERS interact with authentication/validation
  sessions --delegates token validation to--> request_auth
  admin_sessions --delegates token validation to--> request_auth
  ee_sessions --extends---> sessions
  jwt_ctrl --performs jwt auth using--> jwt_token
  jwt_ctrl --delegates additional token validation to--> access_token_validator
  confirmations_ee --sign in flow--> sessions
  admin_creds --filters and reviews credentials--> sessions

  well_known_ctrl --discovery and jwks serving--> jwks_ctrl
  jwks_ctrl --loads keys from--> jwt_token

  sessions --includes concern--> skips_signedin
  sessions --includes concern--> known_signin
  ee_sessions --includes GoogleAnalytics concerns

  %% SESSION/CREDENTIALS enrichment
  admin_sessions --includes--> known_signin

  %% TOKEN AUTHN & FIELD MGMT interact with token types
  t_deploy --token field ops--> tfd
  t_personal --token field ops--> tfd
  t_feed --token field ops--> tfd
  t_runner --token field ops--> tfd
  t_ci_job --token field ops--> tfd
  t_ci_trigger --token field ops--> tfd
  t_ff_client --token field ops--> tfd
  t_incoming_email --token field ops--> tfd
  t_oauth_app_secret --token field ops--> tfe
  t_cluster_agent --token field ops--> tfd

  t_deploy --deployed-to group via model--> gdeploy_model

  %% JWT interaction
  authn_jwt --decodes tokens for auth--> request_auth

  %% API TOKEN LOCATION interacts with token finder
  api_token_locator --extracts/locates credentials for--> api_token_resolver
  api_token_resolver --calls agnostic type identifier--> agnostid

  %% Helper relations
  access_tokens_helper --view helpers for tokens--> t_personal

  %% Deploy tokens API endpoint management
  api_deploy_tokens["API::DeployTokens":::core]
  api_deploy_tokens --token param-> field helpers--> tfb
  api_deploy_tokens --model access--> gdeploy_model

  %% OAUTH
  doorkeeperunique --generates prefixes for--> t_oauth_app_secret
  doorkeeperunique --validates/generated secrets--> authz_reset_secret
  ee_app_create --manages app token creation--> doorkeeperunique
  t_oauth_app_secret --handles secrets in prefix--> doorkeeperunique

  %% Smartcard relations
  smartcard_cert --finds user by certificate--> smartcard_session
  smartcard_cert --used by finder modules in token validation--> request_auth
  smartcard_session --tracks sign-ins--> ns_session

  %% Expiry Enforcement
  session_expiry --called by--> sessions

  %% Error Handling
  sessions --delegates error handling--> devise_failure
  sessions --uses auth errors--> auth_errors
  admin_sessions --delegates error handling--> devise_failure
  request_auth --raises errors--> auth_errors
  request_auth --may raise--> too_many_ips

  %% Crypto
  pbkdf2mod --extends--> pbkdf2enc
  pbkdf2sha --used by--> pbkdf2enc

  %% Subscription API token validation
  gitlab_subs_int_auth --performs internal validation--> jwt_token

  %% Key/AuthorizedKeys validation
  key_ee --delegates key presence to--> authorized_keys

  %% Tests (show connection for documentation)
  authorized_keys_spec --tests--> authorized_keys
  auth_spec --tests--> request_auth
  smartcard_san_spec --tests--> smartcard_san

  %% Vendor gem for token field strategies
  pbkdf2mod --provides password comparison---> sessions

  %% Classes
  class tfb,tfd,tfi,tfe,tfgrt datastruct;
  class tfph,tfen,api_token_locator,api_token_resolver,agnostid,doorkeeperunique,session_store_builder,skips_signedin,known_signin,devise_mailer,pbkdf2mod,pbkdf2enc,pbkdf2sha,smartcard_session,smartcard_san,gitlab_subs_int_auth,gitlab_exclusive_lease utility;
  class authn_jwt,t_deploy,t_personal,t_feed,t_runner,t_ci_job,t_ci_trigger,t_oauth_app_secret,t_ff_client,t_incoming_email,t_cluster_agent,t_gitlab_session,ee_ci_jwt core;
  class gdeploy_model,key_ee,authorized_keys,jsonweb_token_token,jsonweb_token_rsa datastruct;
  class auth_errors,too_many_ips,devise_failure errorclass;

  %% Initialization/Main entry: purple
  class session_expiry,session_store_builder initialization;

  %% Main controllers, models
  class sessions,admin_sessions,ee_sessions,jwt_ctrl,admin_creds,confirmations_ee,well_known_ctrl,jwks_ctrl api_deploy_tokens core;

  %% Special domains
  class smartcard_cert,smartcard_session2 core;

  %% Test
  class authorized_keys_spec,auth_spec,smartcard_san_spec utility;
```