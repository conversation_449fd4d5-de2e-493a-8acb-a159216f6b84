```mermaid
flowchart TD
  %% LAYOUT & GROUP STYLES
  %% Color palette: pastel blue (#D4F1F9), pastel green (#E0F8E0), pastel yellow (#FFF8DC), pastel red (#FFE4E1), pastel purple (#E6E6FA), light gray for groupings (#F8F8F8)
  %% NODE SHAPES: Most are rounded-rectangle, Data structures: hexagon, Policies: stadium, Helpers/Concerns: parallelogram

  %% CORE CONCEPTS GROUP
  subgraph core[Core Concepts and Domain Models]
    direction TB
    core_style fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded
    pb[ProtectedBranch<br>Domain Model]:::coref
    pt[ProtectedTag<br>Domain Model]:::coref
    prb[Projects::BranchRule<br>Abstract Branch Rule]:::coref
    prb_cache[ProtectedBranch::CacheKey<br>Cache Identity]:::support
    pb_access[ProtectedBranchAccess<br>Access Policy]:::support
    pt_access[ProtectedTagAccess<br>Access Policy]:::support
    pr[ProtectedRef<br>Module]:::coref
    pr_concern[ProtectedRef Concern<br>Access Logic]:::support
    prb_push_access[ProtectedBranch::PushAccessLevel]:::support
    prb_access_level[ProtectedRef::AccessLevel<br>Module]:::support
    pt_create[ProtectedTag::CreateAccessLevel]:::support
    prb_cache --> pb
    pb -.->|includes| pr
    pt -.->|includes| pr
    pr -.->|provides<br>domain API| pr_concern
    pb -.-> pb_access
    pt -.-> pt_access
    pb -.-> prb_push_access
    pt -.-> pt_create
    pr_concern -.-> prb_access_level
    prb -.->|composed-of| pb
    prb -.->|uses| pr_concern
    pb -.-> prb_push_access
    prb -.->|inherits-from| prb
    pb -.->|used-by| prb
  end

  %% BRANCH RULES—ABSTRACTIONS AND RULE SYSTEM
  subgraph rules[Branch Rules System]
    direction TB
    rules_style fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded
    brr[Projects::BranchRule<br>Abstract Rule]:::coref
    all_brr[Projects::AllBranchesRule<br>All Branches Rule]:::coref
    all_pbrr[Projects::AllProtectedBranchesRule<br>All Protected Branches Rule]:::coref
    brs[Projects::BranchRules<br>Namespace/Prefix]:::support
    custom_br[Projects::CustomBranchRule<br>Concern]:::support
    ee_custom_br[EE::Projects::CustomBranchRule<br>EE API Concern]:::support
    br_merge_approval[EE::Projects::BranchRules::MergeRequestApprovalSetting]:::support
    all_br_merge_approval[EE::Projects::AllBranchesRules::MergeRequestApprovalSetting]:::support
    brr -.->|inherits| brr
    all_brr -.->|inherits| brr
    all_pbrr -.->|inherits| brr
    brr -.->|includes| custom_br
    all_brr -.->|includes| custom_br
    all_pbrr -.->|includes| custom_br
    custom_br -.-> ee_custom_br
    all_brr -.->|uses| all_br_merge_approval
    brr -.->|uses| br_merge_approval
    rules_style2 fill:#D4F1F9,stroke:#D4F1F9,stroke-width:1,rounded
  end

  %% DATA STRUCTURES & MATCHING
  subgraph datastruct[Data Structures & Matchers]
    direction TB
    datastruct_style fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2,rounded
    ref_matcher[RefMatcher<br>Ref Pattern Matching]:::datas
    push_event[PushEvent<br>Tracks Push Actions]:::datas
    group_model[Ci::Group<br>CI Group Struct]:::datas
    exported_pb[ExportedProtectedBranch<br>Exported State]:::datas
    datastruct_style2 fill:#E0F8E0,stroke:#E0F8E0,stroke-width:1,rounded
  end

  %% SERVICES: CREATION, UPDATE, DELETE, CACHE
  subgraph svc[Services: Branches/Tags/Rules]
    direction TB
    svc_style fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2,rounded
    pb_basesvc[ProtectedBranches::BaseService]:::init
    pb_create[ProtectedBranches::CreateService]:::init
    pb_update[ProtectedBranches::UpdateService]:::init
    pb_destroy[ProtectedBranches::DestroyService]:::init
    pb_api[ProtectedBranches::ApiService]:::init
    pb_cache[ProtectedBranches::CacheService]:::init
    pb_legacy_create[ProtectedBranches::LegacyApiCreateService]:::init
    pb_legacy_update[ProtectedBranches::LegacyApiUpdateService]:::init
    pb_finder[ProtectedBranchesFinder]:::support
    pt_create_svc[ProtectedTags::CreateService]:::init
    pt_update_svc[ProtectedTags::UpdateService]:::init
    pt_destroy_svc[ProtectedTags::DestroyService]:::init
    br_base_svc[BranchRules::BaseService]:::init
    br_destroy[BranchRules::DestroyService]:::init
    branch_squash_update[Projects::BranchRules::SquashOptions::UpdateService]:::init
    branch_squash_destroy[Projects::BranchRules::SquashOptions::DestroyService]:::init
    svc_style2 fill:#E6E6FA,stroke:#E6E6FA,stroke-width:1,rounded
  end

  %% CONTROLLERS & GRAPHQL MUTATIONS
  subgraph api_clients[API Interfaces: Controllers, GraphQL]
    direction TB
    api_clients_style fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2,rounded
    pb_ctlr[Projects::ProtectedBranchesController<br>REST Controller]:::init
    pt_ctlr[Projects::ProtectedTagsController<br>REST Controller]:::init
    pr_ctlr[Projects::ProtectedRefsController<br>REST Controller]:::init
    br_graphql_upd[Mutations::BranchRules::Update<br>GraphQL Update]:::init
    br_graphql_del[Mutations::BranchRules::Delete<br>GraphQL Delete]:::init
    api_clients_style2 fill:#E6E6FA,stroke:#E6E6FA,stroke-width:1,rounded
  end

  %% POLICIES
  subgraph policies[Security Policies]
    direction TB
    policies_style fill:#F8F8F8,stroke:#FFE4E1,stroke-width:2,rounded
    pb_policy[ProtectedBranchPolicy<br>Authorization]:::plcy
    pb_access_policy[ProtectedBranchAccessPolicy<br>Authorization]:::plcy
    repo_policy[RepositoryPolicy<br>Authorization]:::plcy
    br_policy[Projects::BranchRulePolicy<br>Branch Rule Access]:::plcy
    all_br_policy[Projects::AllProtectedBranchesRulePolicy]:::plcy
    policies_style2 fill:#FFE4E1,stroke:#FFE4E1,stroke-width:1,rounded
  end

  %% HELPERS & CONCERNS
  subgraph helpers[Helpers & Concerns]
    direction TB
    helpers_style fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,rounded
    brh[BranchesHelper]:::support
    proj_br_rh[Projects::Settings::BranchRulesHelper]:::support
    users_callouts[Users::CalloutsHelper]:::support
    checks_collab[ChecksCollaboration]:::support
    def_br_prot[DefaultBranchProtection]:::support
    reset_col_err[ResetOnColumnErrors]:::support
    id_in_ordered[IdInOrdered]:::support
    helpers_style2 fill:#FFF8DC,stroke:#FFF8DC,stroke-width:1,rounded
  end

  %% EVENTS (EVENT-DRIVEN LOGIC)
  subgraph events[Domain Events]
    direction TB
    events_style fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded
    pb_created_event[Repositories::ProtectedBranchCreatedEvent]:::coref
    pb_destroyed_event[Repositories::ProtectedBranchDestroyedEvent]:::coref
  end

  %% DATA TRANSFORMATION & PARAMS
  subgraph params[Data Transformation & Params]
    direction TB
    params_style fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2,rounded
    pr_access_level_params[ProtectedRefs::AccessLevelParams]:::datas
  end

  %% CORE CONCEPTS CONNECTIONS
  prb --> brr
  pb --> prb
  pt --> pt_create
  pr -.->|shared logic| pb
  pr -.->|shared logic| pt

  %% RULE SYSTEM CONNECTIONS
  rules -- rules-subclasses --> rules
  brr -.->|uses| pb
  brr -.->|uses| pr_concern
  brr -.->|has-many| br_merge_approval
  all_brr -.->|inherits| brr
  all_pbrr -.->|inherits| brr

  %% SERVICES LOGICAL COLLABORATION
  pb_basesvc -->|foundation| pb_create
  pb_basesvc -->|foundation| pb_update
  pb_basesvc -->|foundation| pb_destroy
  pb_basesvc --> pb_api
  pb_basesvc --> pb_cache
  pb_create --> pb
  pb_update --> pb
  pb_update --> pr_access_level_params
  pb_destroy --> pb
  pb_cache --> pb
  pb_legacy_create -.-> pb_create
  pb_legacy_update -.-> pb_update
  pt_create_svc --> pt
  pt_update_svc --> pt
  pt_destroy_svc --> pt
  br_base_svc -.->|foundation| br_destroy
  br_destroy -.-> pb_destroy
  br_destroy -.-> brr
  branch_squash_update -.-> brr
  branch_squash_destroy -.-> brr

  %% API LAYER TO SERVICE COLLABORATION
  pr_ctlr --> pb_ctlr
  pr_ctlr --> pt_ctlr
  pb_ctlr --> pb_create
  pb_ctlr --> pb_update
  pb_ctlr --> pb_destroy
  pt_ctlr --> pt_create_svc
  pt_ctlr --> pt_update_svc
  pt_ctlr --> pt_destroy_svc
  br_graphql_upd -.-> pb_update
  br_graphql_del -.-> pb_destroy

  %% POLICIES ENFORCE ACCESS ON DOMAIN OBJECTS
  pb_policy -.-> pb
  pb_access_policy -.-> pb_access
  br_policy -.-> brr
  all_br_policy -.-> all_pbrr
  repo_policy -.->|governs| pb

  %% HELPERS & CONCERNS ARE USED IN VIEWS & CONTROLLERS
  proj_br_rh -.-> pb_ctlr
  proj_br_rh -.-> brr
  users_callouts -.-> pb_ctlr
  checks_collab -.-> pb_ctlr
  brh -.-> pb_ctlr
  def_br_prot -.-> pb_ctlr
  reset_col_err -.-> pb
  id_in_ordered -.-> pb

  %% EVENTS: EMITTED BY SERVICES
  pb_create -.-> pb_created_event
  pb_destroy -.-> pb_destroyed_event
  pb_update -- possibly emits --> pb_created_event
  pb_destroy -- audit/event --> pb_destroyed_event

  %% DATA STRUCTURES
  ref_matcher -.-> pb
  ref_matcher -.-> pt
  group_model -.-> pb
  exported_pb -.-> pb

  %% PARAMS
  pr_access_level_params -.-> pb_update
  pr_access_level_params -.-> pb_create
  pr_access_level_params -.-> pb_api

  %% FINDERS
  pb_finder -.-> pb
  pb_finder -.-> brr

  %% ADDITIONAL RELATIONS: EE COMPONENTS (represented as dashed lines or cross-links if needed)

  %% STYLES
  classDef coref fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,stroke-dasharray:0,rx:12,ry:12
  classDef support fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rx:12,ry:12
  classDef datas fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rx:12,ry:12
  classDef plcy fill:#FFE4E1,stroke:#FFE4E1,stroke-width:2,rx:12,ry:12
  classDef init fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2,rx:12,ry:12

  %% Final grouping for clarity
  %% Subgraph ordering
  core --> rules
  rules --> svc
  svc --> api_clients
  api_clients --> policies
  svc --> events
  datastruct --> core
  helpers --> api_clients
  svc --> datastruct
  params --> svc
  datastruct --> svc
```