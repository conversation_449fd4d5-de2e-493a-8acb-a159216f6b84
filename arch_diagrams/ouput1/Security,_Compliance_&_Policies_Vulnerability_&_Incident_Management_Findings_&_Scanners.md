```mermaid
flowchart TD
%% COLORS
%% Core domain:    #D4F1F9 (pastel blue)
%% Data structure: #E0F8E0 (pastel green)
%% Utility:        #FFF8DC (pastel yellow)
%% Error:          #FFE4E1 (pastel red)
%% Init/setup:     #E6E6FA (pastel purple)
%% Subgraphs:      #F8F8F8 (very light gray, pastel borders)
%% Shapes: Default roundedRectangle, database=cylinder, doc=parallelogram

%% TOP LEVEL DOMAIN STRUCTURE
subgraph MAIN[Security, Compliance & Policies :: Vulnerability & Incident Management :: Findings & Scanners]
  direction TB

  %% CORE DOMAIN MODELS (VULNERABILITIES, FINDINGS, SCANNERS)
  subgraph S1["Vulnerability Management" ]
    style S1 fill:#F8F8F8,stroke:#8AC6D1,stroke-width:2,rounded
    VULN["Vulnerability.rb":::core]:::core
    VULN_EE["ee/vulnerability.rb":::core]:::core
    VULN_UMN["VulnerabilityUserMention.rb":::core]:::core
    VULN_FEEDBACK["vulnerabilities/feedback.rb":::core]:::core
    VULN_ISSUE_LINK["vulnerabilities/issue_link.rb":::core]:::core
    VULN_EXTERNAL_ISSUE["vulnerabilities/external_issue_link.rb":::core]:::core
    VULN_MR_LINK["vulnerabilities/merge_request_link.rb":::core]:::core
    VULN_FINDING["vulnerabilities/finding.rb":::core]:::core
    VULN_FINDINGID["vulnerabilities/finding_identifier.rb":::data]
    VULN_REPINFO["vulnerabilities/representation_information.rb":::data]
    VULN_FINDINGSIG["vulnerabilities/finding_signature.rb":::data]
    VULN_SEV_OVERRIDE["vulnerabilities/severity_override.rb":::core]
    VULN_READ["vulnerabilities/read.rb":::data]
    VULN_SCANNER["vulnerabilities/scanner.rb":::core]
    VULN_PROJECTSGRADE["vulnerabilities/projects_grade.rb":::core]
    VULN_FINDINGEVIDENCE["vulnerabilities/finding/evidence.rb":::data]
    VULN_FINDINGTOKEN["vulnerabilities/finding_token_status.rb":::data]
    VULN_STATDIFF["vulnerabilities/stat_diff.rb":::data]
  end

  %% FINDING AGGREGATION & TRANSFORMATION
  subgraph S2["Finding Composition && Scanning"]
    style S2 fill:#F8F8F8,stroke:#3656A8,stroke-width:2,rounded
    SEC_FINDING["security/finding.rb":::core]
    SEC_SCAN["security/scan.rb":::core]
    SEC_SCANNERS["security_scanners.rb":::core]
    SEC_JOBFINDER["security_jobs_finder.rb":::util]
    SCANR_FINDER["scan_result_policies/findings_finder.rb":::util]
    PIPE_FINDERS["pipeline_vulnerabilities_finder.rb":::util]
    SEC_FINDINGSFINDER["findings_finder.rb":::util]
    SEC_ANLYZR_PROJSTAT["analyzer_project_status.rb":::core]
    SEC_ANLYZR_NSSTAT["analyzer_namespace_status.rb":::core]
    SEC_PIPEFINDER["related_pipelines_finder.rb":::util]
  end

  %% SCAN CONFIGURATION && INIT/SETUP
  subgraph S3["CI Configuration / Scan Setup"]
    style S3 fill:#F8F8F8,stroke:#CBAACB,stroke-width:2,rounded
    CI_DEP_SCN_CREATE["ci_configuration/dependency_scanning_create_service.rb":::init]
    CI_CONT_SCN_CREATE["ci_configuration/container_scanning_create_service.rb":::init]
    CI_SECRET_CREATE["ci_configuration/secret_detection_create_service.rb":::init]
    CI_SASTIAC_CREATE["ci_configuration/sast_iac_create_service.rb":::init]
    CI_CONF_DEP["configure_dependency_scanning.rb":::init]
    CI_CONF_CONT["configure_container_scanning.rb":::init]
    SAST_CONFIGURE["sast_configuration_controller.rb":::init]
  end

  %% DATA STRUCTURES & ENUMS
  subgraph S4["Supporting Data Structures & Enums"]
    style S4 fill:#F8F8F8,stroke:#98DB9A,stroke-width:2,rounded
    ENUM_VULN["concerns/enums/vulnerability.rb":::data]
    ENUM_VULN_EE["ee/enums/vulnerability.rb":::data]
    VULN_FINDHELP["vulnerability_finding_helpers.rb":::util]
    EE_FLAG_HELPERS["ee/vulnerability_flag_helpers.rb":::data]
    VULN_SCOPES["vulnerability_scopes.rb":::util]
    REDACT["redactable.rb":::util]
  end

  %% INGESTION/PIPELINE TASKS & DATA FLOW
  subgraph S5["Ingestion & Processing Pipeline"]
    style S5 fill:#F8F8F8,stroke:#A0C4FF,stroke-width:2,rounded
    INGEST_SERVICE["ingestion/ingest_report_service.rb":::core]
    INGEST_SLICE["ingestion/ingest_report_slice_service.rb":::core]
    INGEST_CVS_SLICE["ingestion/ingest_cvs_slice_service.rb":::core]
    MAP_COLLECTION["ingestion/finding_map_collection.rb":::data]
    FINDING_MAP["ingestion/finding_map.rb":::data]
    ABS_TASK["ingestion/abstract_task.rb":::util]
    CONTEXT["ingestion/context.rb":::util]
    ING_TSKS1["ingest_vulnerabilities/create.rb":::core]
    ING_TSKS2["ingest_finding_signatures.rb":::core]
    ING_TSKS3["ingest_finding_links.rb":::core]
    ING_TSKS4["ingest_finding_evidence.rb":::core]
    ING_TSKS5["ingest_cvs_security_scanners.rb":::core]
    ING_TSKS6["ingest_vulnerabilities.rb":::core]
    ING_TSKS7["ingest_vulnerability_reads.rb":::core]
    ING_TSKS8["ingest_remediations.rb":::core]
    ING_TSKS9["tasks/update_vulnerability_uuids.rb":::core]
    ING_TSKS10["tasks/update_vulnerability_uuids/vulnerability_feedback.rb":::core]
    ING_TSKS11["tasks/ingest_vulnerabilities/set_present_on_default_branch.rb":::core]
    ING_TSKS12["tasks/ingest_vulnerabilities/update.rb":::core]
    ING_TSKS13["tasks/ingest_vulnerabilities/apply_severity_overrides.rb":::core]
    ING_TSKS14["tasks/ingest_vulnerability_reads/update.rb":::core]
    CREATE_REP_INFO["ingestion/create_vulnerability_representation_information.rb":::core]
    MARK_RESOLVED["ingestion/mark_as_resolved_service.rb":::core]
  end

  %% GRAPHQL LAYER / API DEFINITIONS
  subgraph S6["GraphQL Types & Resolvers"]
    style S6 fill:#F8F8F8,stroke:#78A1BB,stroke-width:2,rounded
    GQL_VULN_TYPE["types/vulnerability_type.rb":::core]
    GQL_FINDING_TYPE["types/pipeline_security_report_finding_type.rb":::core]
    GQL_SORTENUM["types/vulnerability_sort_enum.rb":::data]
    GQL_ENUM_SEVERITY["types/vulnerability_severity_enum.rb":::data]
    GQL_FINDREPORTS_TYPE["types/security/finding_reports_comparer_type.rb":::core]
    GQL_FINDREPORTS_FINDING["types/security/finding_reports_comparer/finding_type.rb":::core]
    GQL_FINDREPORTS_SCANNER["types/security/finding_reports_comparer/scanner_type.rb":::core]
    GQL_FINDREPORTS_REPORT["types/security/finding_reports_comparer/report_type.rb":::core]
    GQL_FINDREPORTS_STATUS["types/security/finding_reports_comparer/status_enum.rb":::data]
    GQL_DETAILS_RESOLVER["resolvers/vulnerabilities/details_resolver.rb":::core]
    GQL_FINDREPORTS_RESOLVER["resolvers/security_report/finding_reports_comparer_resolver.rb":::core]
    GQL_FINDING_RESOLVER["resolvers/security_report/finding_resolver.rb":::core]
    GQL_SCANNER_RESOLVER["resolvers/vulnerabilities/scanners_resolver.rb":::core]
    GQL_VULN_BASE_RESOLVER["resolvers/vulnerabilities_base_resolver.rb":::util]
    GQL_VULN_FILTER["resolvers/vulnerability_filterable.rb":::util]
    GQL_IDENTIFIER_SEARCH["resolvers/vulnerabilities/identifier_search_resolver.rb":::util]
    GQL_PIPE_FINDINGS_RESOLVER["resolvers/pipeline_security_report_findings_resolver.rb":::core]
    GQL_ARCHIVES_RESOLVER["resolvers/vulnerabilities/archives_resolver.rb":::core]
  end

  %% REMOVAL, CLEANUP, AND BULK/AUDIT OPERATIONS
  subgraph S7["Removal, Audit & Bulk Services"]
    style S7 fill:#F8F8F8,stroke:#FFD6A5,stroke-width:2,rounded
    REMOVAL_BASE["removal/abstract_task_scoped_to_finding.rb":::util]
    REMOVAL_BASE_VULN["removal/abstract_task_scoped_to_vulnerability.rb":::util]
    REMOVAL_DELETE_EVIDENCE["removal/tasks/delete_finding_evidences.rb":::core]
    REMOVAL_DELETE_FLAGS["removal/tasks/delete_finding_flags.rb":::core]
    REMOVAL_DELETE_LINKS["removal/tasks/delete_finding_links.rb":::core]
    REMOVAL_DELETE_REMEDIATION["removal/tasks/delete_finding_remediations.rb":::core]
    REMOVE_VULN_ALL["removal/remove_from_project_service.rb":::core]
    REMOVE_ISSUE_LINKS["removal/tasks/delete_vulnerability_issue_links.rb":::core]
    REMOVE_EXT_ISSUE_LINKS["removal/tasks/delete_vulnerability_external_issue_links.rb":::core]
    REMOVE_USER_MENTIONS["removal/tasks/delete_vulnerability_user_mentions.rb":::core]
    REMOVE_STATE_TRANS["removal/tasks/delete_vulnerability_state_transitions.rb":::core]
    REM_HISTORY_DEL["historical_statistics/deletion_service.rb":::core]
    BULK_DISMISS["bulk_dismiss_service.rb":::core]
    BULK_SEV_OVERRIDE["bulk_severity_override_service.rb":::core]
    SEV_OVERRIDE_AUDIT["severity_override_audit_service.rb":::core]
    BULK_CREATE_REDET["bulk_create_redetected_notes_service.rb":::core]
    EXPORT_PURGE["archival/export/purge_service.rb":::core]
    EXPORT_EXPORT["archival/export/export_service.rb":::core]
    EXPORT_CSV["archival/export/exporters/csv_service.rb":::core]
    ARCHIVE_BATCH["archival/archive_batch_service.rb":::core]
  end

  %% PRESENTERS, SERIALIZERS, HELPERS
  subgraph S8["Presentation, Serialization, and View-Helpers"]
    style S8 fill:#F8F8F8,stroke:#D7E3FC,stroke-width:2,rounded
    VULN_FINDING_PRES["presenters/vulnerabilities/finding_presenter.rb":::util]
    VULN_SCANNER_PRES["presenters/vulnerabilities/scanner_presenter.rb":::util]
    VULN_PRESENT["presenters/vulnerability_presenter.rb":::util]
    SEC_FINDING_PRES["presenters/security/finding_presenter.rb":::util]
    SERIALIZE_VULN["serializers/vulnerability_serializer.rb":::util]
    SERIALIZE_VULN_ENTITY["serializers/vulnerability_entity.rb":::util]
    SERIALIZE_VULN_EXT_ISSUE["serializers/vulnerabilities/external_issue_link_entity.rb":::util]
    SERIALIZE_VULN_ISSUE["serializers/vulnerabilities/issue_link_entity.rb":::util]
    SERIALIZE_VULN_MR_LINK["serializers/vulnerabilities/merge_request_link_entity.rb":::util]
    SERIALIZE_STATE_TRANS["serializers/vulnerabilities/state_transition_entity.rb":::util]
    SERIALIZE_FINDING_ENTITY["serializers/vulnerabilities/finding_entity.rb":::util]
    SERIALIZE_SCANNER_ENTITY["serializers/vulnerabilities/scanner_entity.rb":::util]
    SERIALIZE_RESPONSE_ENTITY["serializers/vulnerabilities/response_entity.rb":::util]
    SERIALIZE_IDENTITY["serializers/vulnerabilities/identifier_entity.rb":::util]
    VULN_HELPER["helpers/vulnerabilities_helper.rb":::util]
    SEC_HELPER["helpers/security_helper.rb":::util]
  end

  %% POLICIES & PERMISSIONS
  subgraph S9["Policies & Permissions"]
    style S9 fill:#F8F8F8,stroke:#FFB3AB,stroke-width:2,rounded
    POLICY_FINDING["policies/vulnerabilities/finding_policy.rb":::util]
    POLICY_FEEDBACK["policies/vulnerabilities/feedback_policy.rb":::util]
    POLICY_MR_LINK["policies/vulnerabilities/merge_request_link_policy.rb":::util]
    POLICY_STATISTIC["policies/vulnerabilities/statistic_policy.rb":::util]
    POLICY_SCANNER["policies/vulnerabilities/scanner_policy.rb":::util]
    POLICY_REPINFO["policies/vulnerabilities/representation_information_policy.rb":::util]
    POLICY_ADV_MGMT["policies/vulnerabilities/advanced_vulnerability_management_policy.rb":::util]
    POLICY_PROJECTS_GRADE["policies/vulnerabilities/projects_grade_policy.rb":::util]
    POLICY_ARCHIVE_EXPORT["policies/vulnerabilities/archive_export_policy.rb":::util]
  end

  %% WORKERS ASYNC OPERATIONS
  subgraph S10["Asynchronous Workers"]
    style S10 fill:#F8F8F8,stroke:#B6E3C6,stroke-width:2,rounded
    WORKER_UPDATE_READS["workers/vulnerabilities/update_namespace_ids_of_vulnerability_reads_worker.rb":::init]
    WORKER_TRAVERSAL_IDS_STAT["workers/vulnerabilities/update_traversal_ids_of_vulnerability_statistic_worker.rb":::init]
    WORKER_REMOVE_ALL["workers/vulnerabilities/remove_all_vulnerabilities_worker.rb":::init]
    WORKER_MARK_RESOLVED["workers/vulnerabilities/mark_dropped_as_resolved_worker.rb":::init]
  end

  %% CONTROLLERS WEB/API
  subgraph S11["Controllers: Web/API Entry Points"]
    style S11 fill:#F8F8F8,stroke:#F7D1BA,stroke-width:2,rounded
    SEC_VULN_CTRL["controllers/projects/security/vulnerabilities_controller.rb":::core]
    SEC_VULN_REPORT_CTRL["controllers/projects/security/vulnerability_report_controller.rb":::core]
    SEC_VULN_FEEDBACK_CTRL["controllers/projects/vulnerability_feedback_controller.rb":::core]
    GROUP_VULN_CTRL["controllers/groups/security/vulnerabilities_controller.rb":::core]
    SEC_SCANNED_RES_CTRL["controllers/projects/security/scanned_resources_controller.rb":::core]
    SEC_DAST_SITE_PROFILE_CTRL["controllers/projects/security/dast_site_profiles_controller.rb":::core]
    SEC_DAST_SCANNER_PROFILE_CTRL["controllers/projects/security/dast_scanner_profiles_controller.rb":::core]
    SEC_DISCOVER_CTRL["controllers/projects/security/discover_controller.rb":::core]
    SEC_DAST_CONFIG_CTRL["controllers/projects/security/dast_configuration_controller.rb":::core]
    SEC_SECRET_CONFIG_CTRL["controllers/projects/security/secret_detection_configuration_controller.rb":::core]
  end

  %% HIGH LEVEL APPSEC DAST PROFILES/CONFIGS
  subgraph S12["DAST Profiles & Pipeline Models"]
    style S12 fill:#F8F8F8,stroke:#FFC3A0,stroke-width:2,rounded
    DAST_SITE_PROFILE["dast_site_profile.rb":::core]
    DAST_SCANNER_PROFILE["dast_scanner_profile.rb":::core]
    DAST_PROFILE["dast/profile.rb":::core]
    DAST_PROFILES_PIPELINE["dast/profiles_pipeline.rb":::core]
    DAST_PROFILES_TAG["dast/profile_tag.rb":::core]
    DAST_PRE_SCAN["dast/pre_scan_verification.rb":::core]
    DAST_PRE_SCAN_STEP["dast/pre_scan_verification_step.rb":::data]
    DAST_SCANNER_BUILD["dast/scanner_profiles_build.rb":::data]
    DAST_SITE_BUILD["dast/site_profiles_build.rb":::data]
    DAST_BRANCH["dast/branch.rb":::data]
    DAST_SITE_VALIDATION["dast_site_validation.rb":::core]
  end

  %% FINDERS QUERY LOGIC
  subgraph S13["Finders: Query and Search Abstractions"]
    style S13 fill:#F8F8F8,stroke:#BABECC,stroke-width:2,rounded
    SEC_FINDS_FINDER["finders/security/findings_finder.rb":::util]
    SEC_PIPELINE_FINDER["finders/security/pipeline_vulnerabilities_finder.rb":::util]
    SEC_FEEDBACKS_FINDER["finders/security/vulnerability_feedbacks_finder.rb":::util]
    SEC_READS_FINDER["finders/security/vulnerability_reads_finder.rb":::util]
    SEC_READS_ELASTIC["finders/security/vulnerability_reads_elastic_finder.rb":::util]
    SEC_READS_BASEFINDER["finders/security/vulnerability_reads_base_finder.rb":::util]
    SEC_SCAN_RES_POLICIES_VULN_FINDER["finders/security/scan_result_policies/vulnerabilities_finder.rb":::util]
    SEC_SCAN_RES_POLICIES_FINDINGS_FINDER["finders/security/scan_result_policies/findings_finder.rb":::util]
    SEC_PROJECT_EXCLUS_FINDER["finders/security/project_security_exclusions_finder.rb":::util]
    SEC_AUTOCOMPLETE_VULN["finders/autocomplete/vulnerabilities_autocomplete_finder.rb":::util]
    APPSEC_FUZZ_COVER_CORP["finders/app_sec/fuzzing/coverage/corpuses_finder.rb":::util]
    VULN_SCANNERS_LIST["services/vulnerability_scanners/list_service.rb":::util]
  end

end

%% --- LOGICAL INTERACTIONS BETWEEN COMPONENTS ---

%% Core Models / Aggregations
VULN -->|uses| VULN_FINDING
VULN -->|uses| VULN_SCANNER
VULN -->|has| VULN_ISSUE_LINK
VULN -->|has| VULN_MR_LINK
VULN -->|has| VULN_FEEDBACK
VULN -->|has| VULN_EXTERNAL_ISSUE
VULN_FINDING -->|has| VULN_FINDINGID
VULN_FINDING -->|has| VULN_FINDINGSIG
VULN_FINDING -->|has| VULN_FINDINGEVIDENCE
VULN_FINDING -->|has| VULN_FINDINGTOKEN
VULN_FINDING -->|used by| SEC_FINDING
VULN_FINDING -->|scanned_by| VULN_SCANNER
VULN_FINDING -->|aggregates| VULN_REPINFO
VULN -->|modified_by| VULN_SEV_OVERRIDE
VULN_SCANNER -->|scans| VULN_FINDING
VULN_FINDING -->|reports_on| SEC_SCAN
VULN_FINDING -->|stat diff| VULN_STATDIFF
VULN -->|has user mention| VULN_UMN

%% DAST Profile models
DAST_PROFILE -->|links to| DAST_SITE_PROFILE
DAST_PROFILE -->|links to| DAST_SCANNER_PROFILE
DAST_PROFILES_PIPELINE -->|links| DAST_PROFILE
DAST_PROFILES_TAG -->|tags| DAST_PROFILE
DAST_SCANNER_BUILD -->|build for| DAST_SCANNER_PROFILE
DAST_SITE_BUILD -->|build for| DAST_SITE_PROFILE
DAST_PRE_SCAN -->|step| DAST_PRE_SCAN_STEP
DAST_BRANCH -->|branch| DAST_PROFILE
DAST_SITE_VALIDATION -->|validates| DAST_SITE_PROFILE

%% Scan config/setup
CI_CONF_DEP -->|calls| CI_DEP_SCN_CREATE
CI_CONF_CONT -->|calls| CI_CONT_SCN_CREATE
SAST_CONFIGURE -.->|setup| VULN
SEC_HELPER -->|view data| SEC_VULN_CTRL

%% Enum, DataHelpers, Concerns
ENUM_VULN_EE -->|extends| ENUM_VULN
VULN -->|uses enums| ENUM_VULN
VULN_FINDING -->|uses| VULN_FINDHELP
VULN_FINDING -->|uses| EE_FLAG_HELPERS
VULN_READ -->|scopes| VULN_SCOPES

%% Aggregation & Pipeline 
SEC_FINDING -->|refers| SEC_SCAN
SEC_FINDING -->|maps to| VULN_FINDING
SEC_SCAN -->|collects| SEC_FINDING
SEC_JOBFINDER -->|restricts jobs| SEC_SCAN
PIPE_FINDERS -->|restricts| VULN_FINDING

%% Ingestion
INGEST_SERVICE -->|splits_to| INGEST_SLICE
INGEST_SERVICE -->|collects| MAP_COLLECTION
INGEST_SLICE -->|uses_map| FINDING_MAP
INGEST_CVS_SLICE -->|executes tasks| ING_TSKS5
INGEST_SLICE -->|executes tasks| ING_TSKS1 & ING_TSKS2 & ING_TSKS3 & ING_TSKS4 & ING_TSKS8
INGEST_SERVICE -->|abstract| ABS_TASK
INGEST_SERVICE -->|context| CONTEXT
ING_TSKS9 -->|updates| VULN_FINDING
ING_TSKS11 -->|set_present_on_default| VULN
ING_TSKS12 -->|update_vuln| VULN
ING_TSKS13 -->|apply_severity_override| VULN
CREATE_REP_INFO -->|writes| VULN_REPINFO

%% GraphQL layer
GQL_VULN_TYPE -->|exposes| VULN
GQL_FINDING_TYPE -->|exposes| VULN_FINDING
GQL_SORTENUM -->|for| GQL_VULN_TYPE
GQL_ENUM_SEVERITY -->|for| GQL_VULN_TYPE
GQL_FINDREPORTS_TYPE -->|aggregate| GQL_FINDREPORTS_FINDING
GQL_FINDREPORTS_FINDING -->|links| VULN_FINDING
GQL_FINDREPORTS_SCANNER -->|links| VULN_SCANNER
GQL_DETAILS_RESOLVER -.->|pipeline| GQL_FINDING_TYPE
GQL_FINDREPORTS_RESOLVER -->|resolver| GQL_FINDREPORTS_TYPE
GQL_FINDING_RESOLVER -->|resolves| GQL_FINDING_TYPE

GQL_SCANNER_RESOLVER -->|loads| VULN_SCANNER
GQL_PIPE_FINDINGS_RESOLVER -->|resolves| GQL_FINDING_TYPE
GQL_ARCHIVES_RESOLVER -->|archive| GQL_VULN_TYPE
GQL_VULN_BASE_RESOLVER -->|parent| GQL_VULN_TYPE
GQL_VULN_FILTER -->|filters| VULN
GQL_IDENTIFIER_SEARCH -->|search on| VULN_FINDINGID

%% Serializers and Presenters
VULN_FINDING_PRES -->|presents| VULN_FINDING
VULN_SCANNER_PRES -->|presents| VULN_SCANNER
VULN_PRESENT -->|presents| VULN
SEC_FINDING_PRES -->|extends| VULN_FINDING_PRES
SERIALIZE_VULN -->|serializes| VULN
SERIALIZE_VULN_ENTITY -->|serializes| VULN
SERIALIZE_VULN_EXT_ISSUE -->|serializes| VULN_EXTERNAL_ISSUE
SERIALIZE_VULN_ISSUE -->|serializes| VULN_ISSUE_LINK
SERIALIZE_VULN_MR_LINK -->|serializes| VULN_MR_LINK
SERIALIZE_STATE_TRANS -->|serializes| VULN
SERIALIZE_FINDING_ENTITY -->|serializes| VULN_FINDING
SERIALIZE_SCANNER_ENTITY -->|serializes| VULN_SCANNER

%% Removal and Audit
REMOVAL_BASE -->|base for| REMOVAL_DELETE_EVIDENCE & REMOVAL_DELETE_FLAGS & REMOVAL_DELETE_LINKS & REMOVAL_DELETE_REMEDIATION
REMOVAL_BASE_VULN -->|base for| REMOVE_ISSUE_LINKS & REMOVE_EXT_ISSUE_LINKS & REMOVE_USER_MENTIONS & REMOVE_STATE_TRANS
REMOVE_VULN_ALL -->|uses| REMOVAL_BASE & REMOVAL_BASE_VULN

BULK_DISMISS -->|bulk| VULN
BULK_SEV_OVERRIDE -->|bulk| VULN
SEV_OVERRIDE_AUDIT -->|audits| VULN
BULK_CREATE_REDET -->| for| VULN

%% Policies
POLICY_FINDING -->|governs| VULN_FINDING
POLICY_FEEDBACK -->|governs| VULN_FEEDBACK
POLICY_MR_LINK -->|governs| VULN_MR_LINK
POLICY_STATISTIC -->|governs| VULN_PROJECTSGRADE
POLICY_SCANNER -->|governs| VULN_SCANNER
POLICY_REPINFO -->|governs| VULN_REPINFO
POLICY_ADV_MGMT -->|feature| VULN
POLICY_PROJECTS_GRADE -->|policy| VULN_PROJECTSGRADE
POLICY_ARCHIVE_EXPORT -->|policy| ARCHIVE_BATCH

%% Finder relationships
SEC_PIPELINE_FINDER -->|queries| VULN
SEC_FINDS_FINDER -->|queries| VULN_FINDING
SEC_FEEDBACKS_FINDER -->|queries| VULN_FEEDBACK
SEC_READS_FINDER -->|queries| VULN_READ
SEC_READS_ELASTIC -->|queries| VULN_READ
SEC_SCAN_RES_POLICIES_VULN_FINDER -->|queries| VULN
SEC_SCAN_RES_POLICIES_FINDINGS_FINDER -->|queries| VULN_FINDING
SEC_PROJECT_EXCLUS_FINDER -->|queries| VULN
APPSEC_FUZZ_COVER_CORP -->|queries| VULN_FINDING

%% Controllers
SEC_VULN_CTRL -->|load| VULN
SEC_VULN_CTRL -->|load findings| VULN_FINDING
SEC_VULN_REPORT_CTRL -->|shows| VULN
SEC_SCANNED_RES_CTRL -->|shows| SEC_SCAN
SEC_DAST_SITE_PROFILE_CTRL -->|manages| DAST_SITE_PROFILE
SEC_DAST_SCANNER_PROFILE_CTRL -->|manages| DAST_SCANNER_PROFILE
SEC_DISCOVER_CTRL -->|shows| VULN
GROUP_VULN_CTRL -->|loads group| VULN
SEC_DAST_CONFIG_CTRL -->|setup| DAST_PROFILE
SEC_SECRET_CONFIG_CTRL -->|setup| VULN

%% Worker interactions
WORKER_UPDATE_READS -->|updates| VULN_READ
WORKER_TRAVERSAL_IDS_STAT -->|updates| VULN_STATDIFF
WORKER_MARK_RESOLVED -->|resolves| VULN
WORKER_REMOVE_ALL -->|removes| VULN
ARCHIVE_BATCH -->|archives| VULN

%% Presentation/Helpers
VULN_HELPER -->|helpers| VULN
VULN_HELPER -->|helpers| VULN_FINDING
SEC_HELPER -->|dashboard| SEC_FINDING

%% Other Signaling/Supports
SEC_FINDINGSFINDER -->|for presentation| VULN_FINDING_PRES
SEC_PIPELINE_FINDER -->|for reporting| SERIALIZE_VULN
SERIALIZE_VULN -->|wrapped by| SERIALIZE_FINDING_ENTITY
SERIALIZE_STATE_TRANS -->|used in| VULN

%% Subgraph clusters and Node Styles
classDef core fill:#D4F1F9,stroke:#8AC6D1,stroke-width:2,rounded
classDef data fill:#E0F8E0,stroke:#98DB9A,stroke-width:2,rounded
classDef util fill:#FFF8DC,stroke:#FFD700,stroke-width:2,rounded
classDef error fill:#FFE4E1,stroke:#FFAAAA,stroke-width:2,rounded
classDef init fill:#E6E6FA,stroke:#CBAACB,stroke-width:2,rounded
```