```mermaid
flowchart TD
  %% LAYOUT & COLORS
  %% Vertical layout
  %% COLORS:
  %%   Core domain files: pastel blue (#D4F1F9)
  %%   Supporting/utility files: pastel yellow (#FFF8DC)
  %%   Data structure files: pastel green (#E0F8E0)
  %%   Error handling files: pastel red (#FFE4E1)
  %%   Initialization/setup files: pastel purple (#E6E6FA)
  %%   Subgraph: very light gray (#F8F8F8)

  %% -------------------------------------------------
  %% DOMAIN CORE CONCEPTS AND MAIN GROUPINGS

  subgraph DB_CORE["Database, Persistence & Object Storage" ]
    direction TB
    style DB_CORE fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded-rect

    %% ------------------- STORAGE ABSTRACTIONS --------------------------
    subgraph STORAGE_ARCH["Storage Abstractions" ]
      direction TB
      style STORAGE_ARCH fill:#F8F8F8,stroke:#D4F1F9,stroke-width:1.5px,rounded-rect

      storage_legacy_proj["app/models/storage/legacy_project.rb":::core "LegacyProject Project’s legacy disk storage logic"]:::core
      storage_hashed["app/models/storage/hashed.rb":::core "Hashed Project’s hashed storage logic"]:::core
      can_move_repo["app/models/concerns/can_move_repository_storage.rb":::support "CanMoveRepositoryStorage\nMovable storage concern"]:::support
      fh_legacy_repo["app/models/concerns/storage/legacy_repository.rb":::support "LegacyRepository support for legacy storage"]:::support

      storage_legacy_proj -->|provides base directory, disk_path|storage_hashed
      storage_legacy_proj --> can_move_repo
      storage_hashed --> can_move_repo
      storage_legacy_proj -.-> fh_legacy_repo
    end

    %% ------------------- UPLOADS CORE & FILESTORE ----------------------
    subgraph UPLOADS_CORE["File Store & Upload Models" ]
      direction TB
      style UPLOADS_CORE fill:#F8F8F8,stroke:#D4F1F9,stroke-width:1.5px,rounded-rect

      uploads_base["app/models/uploads/base.rb":::core "Uploads::Base\nAbstract upload model"]:::core
      uploads_local["app/models/uploads/local.rb":::core "Uploads::Local\nLocal disk uploads"]:::core
      file_store_mounter["app/models/concerns/file_store_mounter.rb":::support "FileStoreMounter\nMounts uploaders for file-based fields"]:::support
      with_uploads["app/models/concerns/with_uploads.rb":::support "WithUploads\nAttachable concern"]:::support

      uploads_base --> uploads_local
      uploads_local --> file_store_mounter
      uploads_base --> with_uploads

      metric_image_uploading["app/models/concerns/metric_image_uploading.rb":::support "MetricImageUploading\nValidates if uploaded files are images"]:::support
      uploads_local --> metric_image_uploading
    end

    %% ----------------- OBJECT STORAGE LAYERS --------------------------
    subgraph OBJECT_STORAGE["Object Storage\nAPI, S3, Azure, Helpers, Config" ]
      direction TB
      style OBJECT_STORAGE fill:#F8F8F8,stroke:#D4F1F9,stroke-width:1.5px,rounded-rect

      s3_api["app/uploaders/object_storage/s3.rb":::core "object_storage/s3.rb\nS3 signed HEAD/URL helpers"]:::core
      fog_helpers["lib/object_storage/fog_helpers.rb":::support "FogHelpers\nCommon actions for object stores"]:::support
      objst_config["lib/object_storage/config.rb":::support "ObjectStorage::Config"]:::support

      %% object storage helpers for CLI/Backup
      backup_task["gems/gitlab-backup-cli/lib/gitlab/backup/cli/tasks/task.rb":::core "Backup::Cli::Task\nmain backup task abstraction"]:::core
      backup_google["gems/gitlab-backup-cli/lib/gitlab/backup/cli/targets/object_storage/google.rb":::core "Backup Target: Google\nGoogle cloud object store target"]:::core
      backup_files["gems/gitlab-backup-cli/lib/gitlab/backup/cli/targets/files.rb":::core "Backup Target: Files\nFile system backup target"]:::core

      s3_api --> fog_helpers
      s3_api --> objst_config
      fog_helpers --> objst_config
      backup_task --> backup_google
      backup_task --> backup_files
    end

    %% ------------------- UPLOADERS (Carrierwave) ----------------------
    subgraph UPLOADERS["Uploaders Abstractions, Decorators, File/Blob" ]
      direction TB
      style UPLOADERS fill:#F8F8F8,stroke:#D4F1F9,stroke-width:1.5px,rounded-rect

      avatar_uploader["app/uploaders/avatar_uploader.rb":::core "AvatarUploader"]:::core
      favicon_uploader["app/uploaders/favicon_uploader.rb":::core "FaviconUploader"]:::core
      deleted_object_uploader["app/uploaders/deleted_object_uploader.rb":::core "DeletedObjectUploader"]:::core
      content_type_whitelist["app/uploaders/content_type_whitelist.rb":::support "ContentTypeWhitelist Uploader Content-Type rules"]:::support

      avatar_uploader --> content_type_whitelist
      favicon_uploader --> content_type_whitelist

      %% Uploader utility: carrierwave/concerns
      uploader_helper["app/uploaders/uploader_helper.rb":::support "UploaderHelper uploader shared logic"]:::support

      avatar_uploader --> uploader_helper
      favicon_uploader --> uploader_helper

      %% Virtual registries, cache
      ee_entry_uploader["ee/app/uploaders/virtual_registries/cache/entry_uploader.rb":::core "VirtualRegistries::EntryUploader"]:::core

      ee_entry_uploader --> s3_api
      ee_entry_uploader --> content_type_whitelist
      ee_entry_uploader --> uploader_helper

    end

    %% ------------------- BLOB-LIKE, FILE, & DATA STRUCTURES -----------
    subgraph BLOBS_STRUCTS["Blob/File Models & Data Structures" ]
      direction TB
      style BLOBS_STRUCTS fill:#F8F8F8,stroke:#E0F8E0,stroke-width:1.5px,rounded-rect

      blob_like["app/models/concerns/blob_like.rb":::structure "BlobLike\nBlob/shared binary model concern"]:::structure
      dep_proxy_blob["app/models/dependency_proxy/blob.rb":::structure "DependencyProxy::Blob\nBlob in proxy registry"]:::structure

      blob_like --> with_uploads
      dep_proxy_blob --> file_store_mounter
    end

    %% ------------------- BACKGROUND WORKERS (Processing) --------------
    subgraph BG_WORKERS["Background Processing & Workers" ]
      direction TB
      style BG_WORKERS fill:#F8F8F8,stroke:#E6E6FA,stroke-width:1.5px,rounded-rect

      migrate_uploads_worker["app/workers/object_storage/migrate_uploads_worker.rb":::init "MigrateUploadsWorker"]:::init
      delete_stale_direct_uploads_worker["app/workers/object_storage/delete_stale_direct_uploads_worker.rb":::init "DeleteStaleDirectUploadsWorker"]:::init
      obj_storage_queue["app/workers/concerns/object_storage_queue.rb":::support "ObjectStorageQueue\nWorker concern: shared processing logic"]:::support

      migrate_uploads_worker --> obj_storage_queue
      migrate_uploads_worker -->|operates on|uploads_base
      delete_stale_direct_uploads_worker --> obj_storage_queue
      delete_stale_direct_uploads_worker --> uploads_base
    end

    %% --------------- CLEANUP & MAINTENANCE UTILITIES ------------------
    subgraph CLEANUP["Cleanup, Sanitation, Orphaned Files" ]
      direction TB
      style CLEANUP fill:#F8F8F8,stroke:#FFF8DC,stroke-width:1.5px,rounded-rect

      orphan_artifact_helpers["lib/gitlab/cleanup/orphan_job_artifact_final_objects/storage_helpers.rb":::support "OrphanJobArtifact StorageHelpers"]:::support
      orphan_lfs_refs["lib/gitlab/cleanup/orphan_lfs_file_references.rb":::support "OrphanLfsFileReferences"]:::support
      orphan_job_artifact_batch["lib/gitlab/cleanup/orphan_job_artifact_files_batch.rb":::support "OrphanJobArtifactFilesBatch"]:::support
      orphan_job_artifact_from_storage["lib/gitlab/cleanup/orphan_job_artifact_final_objects/batch_from_storage.rb":::support "OrphanJobArtifactFinalObjects::BatchFromStorage"]:::support
      project_uploads["lib/gitlab/cleanup/project_uploads.rb":::support "Cleanup::ProjectUploads"]:::support

      exif_sanitizer["lib/gitlab/sanitizers/exif.rb":::support "Sanitizers::Exif\nEXIF cleanup"]:::support
      file_info_util["lib/gitlab/utils/file_info.rb":::support "Utils::FileInfo\nSymlinks/hardlinks helper"]:::support

      orphan_artifact_helpers --> orphan_job_artifact_from_storage
      orphan_job_artifact_from_storage --> orphan_job_artifact_batch
      orphan_artifact_helpers --> project_uploads
      orphan_lfs_refs --> project_uploads
      exif_sanitizer --> uploads_base
      exif_sanitizer --> file_info_util

      %% REST: cloud paginators for different providers
      paginator_google["lib/gitlab/cleanup/orphan_job_artifact_final_objects/paginators/google.rb":::support "Paginators::Google"]:::support
      paginator_azure["lib/gitlab/cleanup/orphan_job_artifact_final_objects/paginators/azure.rb":::support "Paginators::Azure"]:::support
      paginator_aws["lib/gitlab/cleanup/orphan_job_artifact_final_objects/paginators/aws.rb":::support "Paginators::Aws"]:::support

      orphan_job_artifact_from_storage --> paginator_google
      orphan_job_artifact_from_storage --> paginator_azure
      orphan_job_artifact_from_storage --> paginator_aws
    end

    %% ------------------- SYSTEM CHECKS & INIT -------------------------
    subgraph SYS_CHECK["Initialization & System Checks" ]
      direction TB
      style SYS_CHECK fill:#F8F8F8,stroke:#E6E6FA,stroke-width:1.5px,rounded-rect

      uploads_dir_exists["lib/system_check/app/uploads_directory_exists_check.rb":::init "UploadsDirectoryExistsCheck"]:::init
      uploads_path_perm["lib/system_check/app/uploads_path_permission_check.rb":::init "UploadsPathPermissionCheck"]:::init
      uploads_tmp_perm["lib/system_check/app/uploads_path_tmp_permission_check.rb":::init "UploadsPathTmpPermissionCheck"]:::init
      objst_config_init["config/initializers/fog_google_https_private_urls.rb":::init "fog_google_https_private_urls.rb\nGoogle S3 monkeypatch"]:::init

      uploads_dir_exists --> uploads_path_perm
      uploads_dir_exists --> uploads_tmp_perm
      objst_config_init --> objst_config
    end

    %% ------------------ S3, OTHER CLOUD ADAPTERS ----------------------
    subgraph CLOUD_ADAPTERS["S3 & Cloud Storage Clients" ]
      direction TB
      style CLOUD_ADAPTERS fill:#F8F8F8,stroke:#D4F1F9,stroke-width:1.5px,rounded-rect

      s3_client["ee/lib/gitlab/status_page/storage/s3_client.rb":::support "S3Client\nMinimal S3 API helper"]:::support
      s3_multipart_upload["ee/lib/gitlab/status_page/storage/s3_multipart_upload.rb":::support "S3MultipartUpload\nMultipart file upload"]:::support

      s3_client --> s3_multipart_upload
      s3_multipart_upload --> fog_helpers
    end

    %% ------------------ SHARDING, SERIALIZATION, & INDEX -------------
    subgraph STORAGE_META["Sharding, Serializers, Search Index" ]
      direction TB
      style STORAGE_META fill:#F8F8F8,stroke:#E0F8E0,stroke-width:1.5px,rounded-rect

      storage_shard["ee/app/models/storage_shard.rb":::structure "StorageShard\nStorage location config"]:::structure
      storage_shard_serializer["ee/app/serializers/storage_shard_serializer.rb":::support "StorageShardSerializer"]:::support

      storage_shard --> storage_shard_serializer
    end

    %% ------------------ DATA/DB PG TYPE HELPERS -----------------------
    subgraph DB_HELPERS["Database Type Helpers & Adapters" ]
      direction TB
      style DB_HELPERS fill:#F8F8F8,stroke:#E0F8E0,stroke-width:1.5px,rounded-rect

      schema_cache_with_renamed["lib/gitlab/database/schema_cache_with_renamed_table.rb":::support "SchemaCacheWithRenamedTable"]:::support
      json_pg_safe["lib/gitlab/database/type/json_pg_safe.rb":::support "JsonPgSafe"]:::support
      pg_type_map_cache["lib/gitlab/database/postgresql_adapter/type_map_cache.rb":::support "PGAdapter::TypeMapCache"]:::support
      clickhouse_con["lib/click_house/connection.rb":::support "ClickHouse::Connection"]:::support

      schema_cache_with_renamed --> json_pg_safe
      pg_type_map_cache --> json_pg_safe
      schema_cache_with_renamed --> pg_type_map_cache
    end

    %% ------------------- SUPPORTING UTILITIES -------------------------
    subgraph SUPPORT_UTILS["Supporting Utilities & Libs" ]
      direction TB
      style SUPPORT_UTILS fill:#F8F8F8,stroke:#FFF8DC,stroke-width:1.5px,rounded-rect

      xml_converter["lib/gitlab/xml_converter.rb":::support "XmlConverter\nXML to Ruby parser/helper"]:::support
      relative_positioning_item["lib/gitlab/relative_positioning/item_context.rb":::support "RelativePositioning::ItemContext"]:::support
      redis_cache_patch["lib/gitlab/patch/redis_cache_store.rb":::support "Patch::RedisCacheStore"]:::support
      memory_upload["lib/gitlab/memory/upload_and_cleanup_reports.rb":::support "Memory::UploadAndCleanupReports"]:::support
    end

  end

  %% ------------------------------------------------------------
  %% DOMAIN COLLABORATION AND FLOW

  %% --- Storage and File Models <-> Uploaders
  storage_legacy_proj --> avatar_uploader
  storage_legacy_proj --> favicon_uploader
  storage_hashed --> avatar_uploader
  storage_hashed --> favicon_uploader
  with_uploads --> avatar_uploader
  with_uploads --> favicon_uploader
  uploads_local --> avatar_uploader
  uploads_local --> favicon_uploader

  %% --- Uploaders <-> Object Storage
  avatar_uploader --> s3_api
  avatar_uploader --> objst_config
  favicon_uploader --> s3_api
  favicon_uploader --> objst_config
  deleted_object_uploader --> s3_api
  deleted_object_uploader --> objst_config

  %% --- Object Storage <-> Cleanup Utilities via fog_helpers/etc
  s3_api --> orphan_artifact_helpers
  fog_helpers --> orphan_artifact_helpers
  objst_config --> orphan_artifact_helpers
  objst_config --> migrate_uploads_worker
  objst_config --> backup_google
  backup_google --> s3_api
  backup_files --> storage_legacy_proj

  %% --- Background Workers
  migrate_uploads_worker --> uploads_local
  migrate_uploads_worker --> s3_api
  migrate_uploads_worker --> fog_helpers

  %% --- Data Structure Linkages
  file_store_mounter --> dep_proxy_blob
  file_store_mounter --> storage_legacy_proj
  file_store_mounter --> storage_hashed

  %% --- Cleaners and Sanitizers
  exif_sanitizer --> avatar_uploader
  exif_sanitizer --> favicon_uploader

  %% --- System Checks
  uploads_path_perm --> uploads_dir_exists
  uploads_tmp_perm --> uploads_dir_exists

  %% --- Testing Helpers (part of logical interactions)
  subgraph TEST_HELPERS["Test/Spec Helpers & Stubs" ]
    direction TB
    style TEST_HELPERS fill:#F8F8F8,stroke:#FFF8DC,stroke-width:1.5px,rounded-rect

    stub_object_storage["spec/support/helpers/stub_object_storage.rb":::support "StubObjectStorage\nStubs config/uploader for tests"]:::support
    file_uploader_spec["spec/uploaders/file_uploader_spec.rb":::support "FileUploaderSpec"]:::support
    migrate_uploads_worker_spec["spec/uploaders/workers/object_storage/migrate_uploads_worker_spec.rb":::support "MigrateUploadsWorkerSpec"]:::support
    multipart_helpers["spec/support/helpers/multipart_helpers.rb":::support "MultipartHelpers\nSupport multipart uploads in tests"]:::support
    orphan_final_artifacts_cleanup_helpers["spec/support/helpers/orphan_final_artifacts_cleanup_helpers.rb":::support "OrphanFinalArtifactsCleanupHelpers"]:::support

    stub_object_storage --> avatar_uploader
    stub_object_storage --> favicon_uploader
    stub_object_storage --> s3_api
    stub_object_storage --> migrate_uploads_worker
    stub_object_storage --> dep_proxy_blob

    file_uploader_spec --> avatar_uploader
    file_uploader_spec --> favicon_uploader
    migrate_uploads_worker_spec --> migrate_uploads_worker
    orphan_final_artifacts_cleanup_helpers --> orphan_job_artifact_from_storage
    multipart_helpers --> uploads_local
  end

  %% ------------------------------------------------------------
  %% STYLE CLASSES

  classDef core fill:#D4F1F9,stroke:#A0C4D4,stroke-width:1.5px,stroke-dasharray: ''
  classDef support fill:#FFF8DC,stroke:#E6DCAC,stroke-width:1.5px,stroke-dasharray: ''
  classDef structure fill:#E0F8E0,stroke:#A9CCA3,stroke-width:1.5px,stroke-dasharray: ''
  classDef init fill:#E6E6FA,stroke:#C8C6E8,stroke-width:1.5px,stroke-dasharray: ''
  
  %% Node Shapes
  %% Use rounded rectangles for main logical/structural nodes
  linkStyle default stroke-width:1.2px,stroke:#B3B3B3
```