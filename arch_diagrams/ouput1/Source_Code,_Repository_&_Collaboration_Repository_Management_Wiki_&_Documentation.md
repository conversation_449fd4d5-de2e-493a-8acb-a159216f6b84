```mermaid
flowchart TD
  %% STYLING CONSTANTS
  %% Core domain node: pastel blue (#D4F1F9)
  %% Supporting/utility node: pastel yellow (#FFF8DC)
  %% Data structure node: pastel green (#E0F8E0)
  %% Error handling node: pastel red (#FFE4E1)
  %% Initialization/setup node: pastel purple (#E6E6FA)
  %% Subgraph background: very light gray (#F8F8F8)

  %% DOMAIN SUBGRAPHS & GROUPINGS

  %% 1. WIKI CORE DOMAIN
  subgraph wiki_core_domain[WIKI CORE | Models, Policies, Data]
    direction TB
    style wiki_core_domain fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded

    WIKI_MODEL["Wiki<br/>Manages core Git-based wiki logic":::core]
    PROJECT_WIKI["ProjectWiki<br/>Project-specific wiki":::core]
    GROUP_WIKI["GroupWiki<br/>Group-specific wiki":::core]
    WIKI_PAGE["WikiPage<br/>Represents individual wiki pages":::core]
    WIKI_PAGE_META["WikiPage::Meta<br/>Metadata/state for each wiki page":::datastruct]
    WIKI_PAGE_SLUG["WikiPage::Slug<br/>Manages slugs/URLs":::datastruct]
    WIKI_DIRECTORY["WikiDirectory<br/>Logical structure of wiki directories":::datastruct]
    USER_MENTION["Wikis::UserMention<br/>Tracks user mentions in wiki metadata":::datastruct]

    %% Relationships
    WIKI_MODEL -->|inherits| PROJECT_WIKI
    WIKI_MODEL -->|inherits| GROUP_WIKI
    PROJECT_WIKI -.-> WIKI_PAGE
    GROUP_WIKI -.-> WIKI_PAGE
    WIKI_PAGE -->|has metadata| WIKI_PAGE_META
    WIKI_PAGE -->|has slugs| WIKI_PAGE_SLUG
    WIKI_PAGE_META --> WIKI_PAGE_SLUG
    WIKI_PAGE_META --> USER_MENTION
    WIKI_PAGE --> WIKI_DIRECTORY

    %% Policies
    WIKI_POLICY["WikiPolicy":::core]
    WIKI_PAGE_POLICY["WikiPagePolicy":::core]
    WIKI_MODEL --> WIKI_POLICY
    WIKI_PAGE --> WIKI_PAGE_POLICY
  end

  %% 2. WIKI REPOSITORY STRUCTURES
  subgraph wiki_repo_domain[WIKI REPOSITORY STRUCTURES]
    direction TB
    style wiki_repo_domain fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded

    PROJECT_WIKI_REPO["Projects::WikiRepository<br/>Persisted wiki repo project":::core]
    GROUP_WIKI_REPO["GroupWikiRepository<br/>Persisted wiki repo group":::core]
    EE_PROJECT_WIKI_REPO["EE::Projects::WikiRepository<br/>EE extensions":::core]

    PROJECT_WIKI_REPO -->|used by| PROJECT_WIKI
    GROUP_WIKI_REPO -->|used by| GROUP_WIKI
    EE_PROJECT_WIKI_REPO --> PROJECT_WIKI_REPO
    EE_PROJECT_WIKI_REPO --> GROUP_WIKI_REPO
  end

  %% 3. DATA STRUCTURES Git & Wiki-specific
  subgraph datastructs_git[DATA STRUCTURES | Git and Wiki]
    direction TB
    style datastructs_git fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2,rounded

    GL_GIT_WIKIPAGE["Gitlab::Git::WikiPage":::datastruct]
    GL_GIT_WIKIPAGEVERSION["Gitlab::Git::WikiPageVersion":::datastruct]
    GL_GIT_WIKIFILE["Gitlab::Git::WikiFile":::datastruct]
    GL_SEARCH_FOUNDWIKIPAGE["Gitlab::Search::FoundWikiPage":::datastruct]
    EE_SEARCH_FOUNDWIKIPAGE["EE::Gitlab::Search::FoundWikiPage":::datastruct]
    GL_WIKI_PAGES_FRONT_MATTER["Gitlab::WikiPages::FrontMatterParser":::datastruct]
    GL_WIKI_PAGES_UTIL["Gitlab::WikiPages<br/>Constants & utilities":::supporting]

    GL_GIT_WIKIPAGE --> GL_GIT_WIKIPAGEVERSION
    WIKI_PAGE --> GL_GIT_WIKIPAGE
    WIKI_PAGE --> GL_GIT_WIKIPAGEVERSION
    WIKI_PAGE --> GL_GIT_WIKIFILE
    WIKI_PAGE --> GL_WIKI_PAGES_FRONT_MATTER
    GL_GIT_WIKIPAGE -.->|used in search| GL_SEARCH_FOUNDWIKIPAGE
    GL_SEARCH_FOUNDWIKIPAGE --> EE_SEARCH_FOUNDWIKIPAGE

    GL_WIKI_PAGES_FRONT_MATTER --> GL_WIKI_PAGES_UTIL
  end

  %% 4. WIKI DOMAIN SERVICES
  subgraph wiki_services[WIKI SERVICES | Page Mutations & Event Hooks]
    direction TB
    style wiki_services fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded

    WIKI_SERVICE_BASE["WikiPages::BaseService<br/>Shared logic for page services":::core]
    WIKI_SERVICE_CREATE["WikiPages::CreateService<br/>Page creation":::core]
    WIKI_SERVICE_UPDATE["WikiPages::UpdateService<br/>Page updates":::core]
    WIKI_SERVICE_DESTROY["WikiPages::DestroyService<br/>Page deletion":::core]
    EE_WIKI_SERVICE_CREATE["EE::WikiPages::CreateService<br/>EE override":::core]
    EE_WIKI_SERVICE_UPDATE["EE::WikiPages::UpdateService<br/>EE override":::core]
    EE_WIKI_SERVICE_DESTROY["EE::WikiPages::DestroyService<br/>EE override":::core]
    WIKI_SERVICE_EVENT_CREATE["WikiPages::EventCreateService<br/>Auditing & event hooks for page changes":::core]
    WIKI_ATTACHMENT_SERVICE["Wikis::CreateAttachmentService<br/>Upload page/file attachments":::core]

    WIKI_SERVICE_CREATE --> WIKI_SERVICE_BASE
    WIKI_SERVICE_UPDATE --> WIKI_SERVICE_BASE
    WIKI_SERVICE_DESTROY --> WIKI_SERVICE_BASE
    EE_WIKI_SERVICE_CREATE --> WIKI_SERVICE_CREATE
    EE_WIKI_SERVICE_UPDATE --> WIKI_SERVICE_UPDATE
    EE_WIKI_SERVICE_DESTROY --> WIKI_SERVICE_DESTROY
    WIKI_SERVICE_CREATE --> WIKI_SERVICE_EVENT_CREATE
    WIKI_SERVICE_UPDATE --> WIKI_SERVICE_EVENT_CREATE
    WIKI_SERVICE_DESTROY --> WIKI_SERVICE_EVENT_CREATE
    WIKI_SERVICE_CREATE --> WIKI_ATTACHMENT_SERVICE
    WIKI_SERVICE_UPDATE --> WIKI_ATTACHMENT_SERVICE
  end

  %% 5. GIT OPERATIONS & EVENT SERVICES
  subgraph git_wiki_services[GIT REPOSITORY OPERATIONS WITH WIKI]
    direction TB
    style git_wiki_services fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded

    GIT_WIKI_PUSH_SVC["Git::WikiPushService<br/>Processes Git push to wikis":::core]
    EE_GIT_WIKI_PUSH_SVC["EE::Git::WikiPushService<br/>EE-specific logic":::core]
    GIT_WIKI_PUSH_CHANGE["Git::WikiPushService::Change<br/>Tracks file changes":::datastruct]
    WIKI_GC_WORKER["Wikis::GitGarbageCollectWorker<br/>Wiki Git GC":::supporting]

    GIT_WIKI_PUSH_SVC --> GIT_WIKI_PUSH_CHANGE
    EE_GIT_WIKI_PUSH_SVC --> GIT_WIKI_PUSH_SVC
    GIT_WIKI_PUSH_SVC --> WIKI_MODEL
    GIT_WIKI_PUSH_SVC --> WIKI_SERVICE_EVENT_CREATE
    GIT_WIKI_PUSH_SVC -->|updates| WIKI_PAGE
    WIKI_GC_WORKER --> WIKI_MODEL
  end

  %% 6. PRESENTATION & GRAPHQL API
  subgraph wiki_presentation[PRESENTATION & API INTEGRATION]
    direction TB
    style wiki_presentation fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded

    WIKI_HELPER["WikiHelper<br/>View helpers for wikis":::supporting]
    WIKI_PAGE_VERSION_HELPER["WikiPageVersionHelper":::supporting]
    ROUTING_WIKI_HELPER["Routing::WikiHelper<br/>URL/path helpers":::supporting]
    WIKI_GRAPHQL_TYPE["Types::Wikis::WikiPageType<br/>GraphQL API Type":::core]

    WIKI_HELPER --> WIKI_PAGE_VERSION_HELPER
    WIKI_HELPER --> ROUTING_WIKI_HELPER
    WIKI_PAGE --> WIKI_HELPER
    WIKI_PAGE --> WIKI_GRAPHQL_TYPE
    WIKI_MODEL --> WIKI_GRAPHQL_TYPE
  end

  %% 7. SEARCH & INDEXING
  subgraph search_indexing[SEARCH & FULL TEXT INDEXING]
    direction TB
    style search_indexing fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded

    GL_PROJECT_SEARCH_RESULTS["Gitlab::ProjectSearchResults<br/>Wiki search in project":::supporting]
    GL_ELASTIC_PROJECT_SEARCH["Gitlab::Elastic::ProjectSearchResults<br/>Elastic index wiki search":::supporting]
    ELASTIC_WIKI_CLASS_PROXY["Elastic::Latest::WikiClassProxy<br/>Elastic search abstraction":::datastruct]
    ELASTIC_WIKI_INSTANCE_PROXY["Elastic::Latest::WikiInstanceProxy":::datastruct]
    ELASTIC_WIKI_CONFIG["Elastic::Latest::WikiConfig<br/>Index settings/mappings":::datastruct]
    EE_ELASTIC_WIKI_INDEXER["ElasticWikiIndexerWorker<br/>Indexes wiki in ES":::supporting]
    GL_SEARCH_FOUNDWIKIPAGE
    EE_SEARCH_FOUNDWIKIPAGE

    GL_PROJECT_SEARCH_RESULTS -->|returns| GL_SEARCH_FOUNDWIKIPAGE
    GL_ELASTIC_PROJECT_SEARCH --> GL_PROJECT_SEARCH_RESULTS
    ELASTIC_WIKI_CLASS_PROXY --> GL_ELASTIC_PROJECT_SEARCH
    ELASTIC_WIKI_INSTANCE_PROXY --> ELASTIC_WIKI_CLASS_PROXY
    ELASTIC_WIKI_INSTANCE_PROXY -->|uses| ELASTIC_WIKI_CONFIG
    EE_ELASTIC_WIKI_INDEXER --> ELASTIC_WIKI_INSTANCE_PROXY
    EE_SEARCH_FOUNDWIKIPAGE --> GL_SEARCH_FOUNDWIKIPAGE
  end

  %% 8. API INTEGRATION & BULK IMPORT
  subgraph api_bulkimport[API & BULK IMPORT]
    direction TB
    style api_bulkimport fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,rounded

    API_WIKIS_HELPERS["API::Helpers::WikisHelpers<br/>API helpers for managing wikis":::supporting]
    BULK_IMPORT_WIKI_PIPELINE["BulkImports::Common::Pipelines::WikiPipeline":::supporting]

    API_WIKIS_HELPERS --> WIKI_MODEL
    API_WIKIS_HELPERS -->|crud| WIKI_PAGE
    BULK_IMPORT_WIKI_PIPELINE --> WIKI_MODEL
  end

  %% 9. QA AUTOMATION
  subgraph qa_automation[QA AUTOMATION | Wiki Feature Coverage]
    direction TB
    style qa_automation fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded

    QA_WIKI_PROJECT_PAGE["QA::Resource::Wiki::ProjectPage":::supporting]
    QA_WIKI_GROUP_PAGE["QA::Resource::Wiki::GroupPage":::supporting]
    QA_REPO_WIKI_PUSH["QA::Resource::Repository::WikiPush":::supporting]
    QA_PAGE_PROJECT_WIKI_SHOW["QA::Page::Project::Wiki::Show":::supporting]
    QA_PAGE_PROJECT_WIKI_EDIT["QA::Page::Project::Wiki::Edit":::supporting]
    QA_PAGE_PROJECT_WIKI_LIST["QA::Page::Project::Wiki::List":::supporting]
    QA_PAGE_PROJECT_WIKI_GIT_ACCESS["QA::Page::Project::Wiki::GitAccess":::supporting]
    QA_COMPONENT_WIKI_PAGE_FORM["QA::Page::Component::WikiPageForm":::supporting]
    QA_COMPONENT_WIKI_SIDEBAR["QA::Page::Component::WikiSidebar":::supporting]
    QA_COMPONENT_WIKI["QA::Page::Component::Wiki":::supporting]
    QA_DELETE_WIKI_MODAL["QA::Page::Modal::DeleteWiki":::supporting]
    QA_WIKI_GROUP_WIKI_SHOW["QA::EE::Page::Group::Wiki::Show":::supporting]
    QA_WIKI_GROUP_WIKI_EDIT["QA::EE::Page::Group::Wiki::Edit":::supporting]

    QA_WIKI_PROJECT_PAGE --> QA_PAGE_PROJECT_WIKI_SHOW
    QA_WIKI_PROJECT_PAGE --> QA_PAGE_PROJECT_WIKI_EDIT
    QA_REPO_WIKI_PUSH --> QA_WIKI_PROJECT_PAGE
    QA_WIKI_GROUP_PAGE --> QA_WIKI_GROUP_WIKI_SHOW
    QA_WIKI_GROUP_PAGE --> QA_WIKI_GROUP_WIKI_EDIT
    QA_PAGE_PROJECT_WIKI_LIST --> QA_COMPONENT_WIKI_SIDEBAR
    QA_PAGE_PROJECT_WIKI_SHOW --> QA_COMPONENT_WIKI
    QA_PAGE_PROJECT_WIKI_EDIT --> QA_COMPONENT_WIKI_PAGE_FORM
    QA_COMPONENT_WIKI --> QA_COMPONENT_WIKI_SIDEBAR
  end

  %% 10. SUPPORTING UTILITIES, HELPERS & DATA BUILDERS
  subgraph supporting_helpers[SUPPORTING UTILITIES, HELPERS & DATA BUILDERS]
    direction TB
    style supporting_helpers fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,rounded

    GL_WIKI_DATA_BUILDER["Gitlab::DataBuilder::WikiPage<br/>Builds event payloads for hooks":::supporting]
    GL_WIKI_FILE_FINDER["Gitlab::WikiFileFinder<br/>Search/locate wiki files":::supporting]
    GL_WIKI_PAGES_UTIL
    GL_BITBUCKET_WIKI_FORMATTER["Gitlab::BitbucketImport::WikiFormatter<br/>Handles Bitbucket wiki import":::supporting]

    GL_WIKI_DATA_BUILDER --> WIKI_PAGE
    GL_WIKI_FILE_FINDER --> WIKI_MODEL
    GL_BITBUCKET_WIKI_FORMATTER --> WIKI_MODEL
  end

  %% 11. INTEGRATIONS, CONCERNS & EXTENSIONS
  subgraph concerns_integrations[INTEGRATIONS, CONCERNS, EXTENSIONS]
    direction TB
    style concerns_integrations fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded

    INTEGRATIONS_EXTERNAL_WIKI["Integrations::Base::ExternalWiki<br/>External wiki integrations":::supporting]
    EE_WIKI_CORE_EXT["EE::Wiki<br/>EE extension to Wiki core logic":::core]

    EE_WIKI_CORE_EXT --> WIKI_MODEL
    INTEGRATIONS_EXTERNAL_WIKI -.-> WIKI_MODEL
  end

  %% 12. MARKUP / CONTENT RENDERING & Banzai
  subgraph rendering_banzai[MARKUP, CONTENT RENDERING & BANZAI]
    direction TB
    style rendering_banzai fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,rounded

    BANZAI_MD_FILTER["Banzai::Filter::MarkdownFilter":::supporting]
    BANZAI_RENDER_CONTEXT["Banzai::RenderContext<br/>Rendering context for pipelines":::supporting]
    BANZAI_MARKDOWN_ENGINE["Banzai::Filter::MarkdownEngines::Base":::supporting]
    BANZAI_BASE_REL_LINK["Banzai::Filter::BaseRelativeLinkFilter<br/>Adjust wiki links in HTML":::supporting]
    BANZAI_WIKI_LINK_REWRITER["Banzai::Filter::WikiLinkFilter::Rewriter<br/>Handles internal/external wiki links":::supporting]
    BANZAI_REFERENCE_EXTRACTOR["Banzai::ReferenceExtractor<br/>Finds references in wiki content":::supporting]

    WIKI_PAGE --> BANZAI_MD_FILTER
    WIKI_PAGE --> BANZAI_RENDER_CONTEXT
    WIKI_PAGE --> BANZAI_REFERENCE_EXTRACTOR
    BANZAI_MD_FILTER --> BANZAI_MARKDOWN_ENGINE
    BANZAI_MD_FILTER --> BANZAI_BASE_REL_LINK
    BANZAI_BASE_REL_LINK --> BANZAI_WIKI_LINK_REWRITER
  end

  %% STYLES FOR NODES
  classDef core fill:#D4F1F9,stroke:#568EA6,stroke-width:1,stroke-dasharray:0,rx:8,ry:8;
  classDef supporting fill:#FFF8DC,stroke:#BDB76B,stroke-width:1,rx:8,ry:8;
  classDef datastruct fill:#E0F8E0,stroke:#73C673,stroke-width:1,rx:8,ry:8;
  classDef error fill:#FFE4E1,stroke:#CD5C5C,stroke-width:1,rx:8,ry:8;
  classDef init fill:#E6E6FA,stroke:#B0C4DE,stroke-width:1,rx:8,ry:8;

  %% LOGICAL FLOW / HIGH LEVEL RELATIONSHIPS

  %% Core relationships: core models <-> repo, data structures, services
  wiki_core_domain --> wiki_repo_domain
  wiki_core_domain --> datastructs_git
  wiki_core_domain --> wiki_services
  wiki_core_domain --> wiki_presentation
  wiki_core_domain --> search_indexing
  wiki_core_domain --> api_bulkimport
  wiki_core_domain --> qa_automation
  wiki_core_domain --> supporting_helpers
  wiki_core_domain --> concerns_integrations
  wiki_core_domain --> rendering_banzai

  %% From repo structures to git datastructs and GC worker
  wiki_repo_domain --> datastructs_git
  wiki_repo_domain --> git_wiki_services
  wiki_repo_domain --> search_indexing

  %% From wiki services to event auditing and to GIT services
  wiki_services --> git_wiki_services
  wiki_services --> api_bulkimport
  wiki_services --> supporting_helpers
  wiki_services --> search_indexing

  %% Data flow from wiki pages to QA, helpers, and rendering
  wiki_core_domain --> qa_automation
  wiki_core_domain --> rendering_banzai

  %% Presentation layer feeds into QA and helpers
  wiki_presentation --> qa_automation
  wiki_presentation --> supporting_helpers

  %% QA automation validates both presentation and mutation paths
  qa_automation --> api_bulkimport
  qa_automation --> wiki_services

  %% API integrates to QA for coverage
  api_bulkimport --> qa_automation
  api_bulkimport --> supporting_helpers

  %% Rendering depends on core WikiPage and helpers/util
  rendering_banzai --> supporting_helpers

  %% Error handling flows (not explicit in code, but policies and base services always raise StandardError on failure)
  class WIKI_SERVICE_CREATE,WIKI_SERVICE_UPDATE,WIKI_SERVICE_DESTROY,WIKI_SERVICE_EVENT_CREATE,EE_WIKI_SERVICE_CREATE,EE_WIKI_SERVICE_UPDATE,EE_WIKI_SERVICE_DESTROY,WIKI_ATTACHMENT_SERVICE error

  %% Initialization not explicit, but GitGarbageCollectWorker/workers/services do init patterns
  class WIKI_GC_WORKER init

  %% Data structures/layers in pastel green
  class GL_GIT_WIKIPAGE,GL_GIT_WIKIPAGEVERSION,GL_GIT_WIKIFILE,GL_SEARCH_FOUNDWIKIPAGE,EE_SEARCH_FOUNDWIKIPAGE,WIKI_PAGE_META,WIKI_PAGE_SLUG,GL_WIKI_PAGES_FRONT_MATTER,ELASTIC_WIKI_CLASS_PROXY,ELASTIC_WIKI_INSTANCE_PROXY,ELASTIC_WIKI_CONFIG,WIKI_DIRECTORY,USER_MENTION datastruct
```