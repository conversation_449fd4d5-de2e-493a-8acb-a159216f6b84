```mermaid
flowchart TD
  %% Domain: Analytics, Observability & Reporting / Cycle Analytics & Value Streams / Stage Analytics & Aggregation
  %% Color Definitions
  
  %% Node Styles
  classDef domainBlue fill:#D4F1F9,stroke:#70B7D1,stroke-width:2px,color:#222,rx:12,ry:12,font-weight:bold
  classDef utilityYellow fill:#FFF8DC,stroke:#EEDF91,stroke-width:2px,color:#333,rx:12,ry:12
  classDef dataGreen fill:#E0F8E0,stroke:#97D497,stroke-width:2px,color:#224422,rx:12,ry:12
  classDef errorRed fill:#FFE4E1,stroke:#FFB6B2,stroke-width:2px,color:#BF1F25,rx:12,ry:12
  classDef initPurple fill:#E6E6FA,stroke:#C8BAF6,stroke-width:2px,color:#332244,rx:12,ry:12
  classDef groupSubgraph fill:#F8F8F8,stroke:#DFE6EC,stroke-width:3px,border-radius:8px
  classDef title fill:#F8F8F8,stroke:none,color:#192147,font-size:22px
  
  %% Core Concepts
  subgraph CORE_DOMAIN["Cycle Analytics Domain Layer"]
    direction TB
    CA_STAGE["Stage\napp/models/analytics/cycle_analytics/stage.rb"]:::domainBlue
    CA_ISSUE_EVENT["IssueStageEvent\napp/models/analytics/cycle_analytics/issue_stage_event.rb"]:::domainBlue
    CA_MR_EVENT["MergeRequestStageEvent\napp/models/analytics/cycle_analytics/merge_request_stage_event.rb"]:::domainBlue
    CA_STAGE_AGG["StageAggregation\napp/models/analytics/cycle_analytics/stage_aggregation.rb"]:::domainBlue
    CA_STAGE_EVENT_HASH["StageEventHash\napp/models/analytics/cycle_analytics/stage_event_hash.rb"]:::domainBlue
    CA_MODULE["CycleAnalytics Module\napp/models/analytics/cycle_analytics.rb"]:::utilityYellow
  end
  class CORE_DOMAIN groupSubgraph
  
  %% Domain Behaviors/Services
  subgraph DOMAIN_SERVICES["Analytics Domain Services"]
    direction TB
    CA_SVC_LIST["ListService\napp/services/analytics/cycle_analytics/stages/list_service.rb"]:::utilityYellow
    EE_CA_SVC_LIST["EE ListService\nee/app/services/ee/analytics/cycle_analytics/stages/list_service.rb"]:::utilityYellow
    NAMESPACE_AGG["NamespaceAggregatorService\nee/app/services/analytics/cycle_analytics/namespace_aggregator_service.rb"]:::utilityYellow
  end
  class DOMAIN_SERVICES groupSubgraph
  
  %% Domain Data Structures
  subgraph DOMAIN_DS["Data Structure & Aggregation"]
    direction TB
    AGG_DATA_COLLECTOR["Aggregated::DataCollector\nlib/gitlab/analytics/cycle_analytics/aggregated/data_collector.rb"]:::dataGreen
    AGG_RECORDS_FETCHER["Aggregated::RecordsFetcher\nlib/gitlab/analytics/cycle_analytics/aggregated/records_fetcher.rb"]:::dataGreen
    DEFAULT_STAGES["DefaultStages\nlib/gitlab/analytics/cycle_analytics/default_stages.rb"]:::dataGreen
  end
  class DOMAIN_DS groupSubgraph
  
  %% Domain Utilities & Concerns
  subgraph DOMAIN_UTIL["Utilities, Concerns, Presenters"]
    direction TB
    STAGEABLE_CONCERN["Stageable\napp/models/concerns/analytics/cycle_analytics/stageable.rb"]:::utilityYellow
    PARENTABLE_CONCERN["Parentable\napp/models/analytics/cycle_analytics/parentable.rb"]:::utilityYellow
    STAGE_PRESENTER["StagePresenter\napp/presenters/analytics/cycle_analytics/stage_presenter.rb"]:::utilityYellow
    PG_FULL_TEXT_SEARCH["PgFullTextSearchable\napp/models/concerns/pg_full_text_searchable.rb"]:::utilityYellow
  end
  class DOMAIN_UTIL groupSubgraph

  %% GraphQL API Integration
  subgraph API_GRAPHQL["API Exposure & GraphQL Integration"]
    direction TB
    EE_STAGE_TYPE["StageType\nee/app/graphql/ee/types/analytics/cycle_analytics/value_streams/stage_type.rb"]:::utilityYellow
    EE_STAGE_METRICS_TYPE["StageMetricsType\nee/app/graphql/ee/types/analytics/cycle_analytics/value_streams/stage_metrics_type.rb"]:::utilityYellow
  end
  class API_GRAPHQL groupSubgraph

  %% Controller Behavior
  subgraph CONTROLLER_BEHAVIOR["Controller & Actions Charts/Endpoints"]
    direction TB
    EE_STAGE_ACTIONS["StageActions Concern\nee/app/controllers/concerns/ee/analytics/cycle_analytics/stage_actions.rb"]:::utilityYellow
  end
  class CONTROLLER_BEHAVIOR groupSubgraph

  %% Value Stream Reporting & Insights
  subgraph VALUE_STREAM_INSIGHT["Reporting, Summary & Insights"]
    direction TB
    VA_SUMMARY_GROUP_BASE["Summary::Group::Base\nee/lib/gitlab/analytics/cycle_analytics/summary/group/base.rb"]:::utilityYellow
    VA_SUMMARY_DEPLOY["Summary::Group::DeploymentFrequency\nee/lib/gitlab/analytics/cycle_analytics/summary/group/deployment_frequency.rb"]:::utilityYellow
    VA_SUMMARY_CYCLETIME["Summary::CycleTime\nee/lib/gitlab/analytics/cycle_analytics/summary/cycle_time.rb"]:::utilityYellow
    INSIGHTS_MOD["Insights Module\nee/lib/gitlab/insights.rb"]:::utilityYellow
  end
  class VALUE_STREAM_INSIGHT groupSubgraph
  
  %% Relationships Across Layers

  %% Stages Core Relationships
  CA_STAGE --> STAGEABLE_CONCERN
  CA_STAGE --> PARENTABLE_CONCERN
  CA_STAGE --> CA_STAGE_EVENT_HASH
  CA_STAGE --> CA_STAGE_AGG
  CA_STAGE -.-> CA_ISSUE_EVENT
  CA_STAGE -.-> CA_MR_EVENT

  CA_ISSUE_EVENT -->|uses enum states| CA_STAGE
  CA_ISSUE_EVENT -->|concerns| STAGEABLE_CONCERN
  CA_ISSUE_EVENT -.->|parent/association| CA_STAGE_EVENT_HASH

  CA_MR_EVENT -->|uses enum states| CA_STAGE
  CA_MR_EVENT -->|concerns| STAGEABLE_CONCERN
  CA_MR_EVENT -.->|parent/association| CA_STAGE_EVENT_HASH

  CA_STAGE_AGG --> CA_STAGE
  CA_STAGE_AGG --> PARENTABLE_CONCERN

  CA_STAGE_EVENT_HASH --> CA_STAGE

  CA_STAGE --> CA_MODULE
  CA_ISSUE_EVENT --> CA_MODULE
  CA_MR_EVENT --> CA_MODULE
  CA_STAGE_AGG --> CA_MODULE
  CA_STAGE_EVENT_HASH --> CA_MODULE

  %% Service Relationships
  CA_SVC_LIST --> CA_STAGE
  CA_SVC_LIST --> DEFAULT_STAGES
  CA_SVC_LIST --> STAGEABLE_CONCERN

  EE_CA_SVC_LIST --> CA_SVC_LIST
  EE_CA_SVC_LIST -->|persists/extends| CA_STAGE

  NAMESPACE_AGG --> VA_SUMMARY_GROUP_BASE
  NAMESPACE_AGG --> AGG_DATA_COLLECTOR

  %% Aggregation and Data Flow
  AGG_DATA_COLLECTOR --> CA_STAGE
  AGG_DATA_COLLECTOR --> AGG_RECORDS_FETCHER
  AGG_RECORDS_FETCHER -->|preloads, serializes| CA_ISSUE_EVENT
  AGG_RECORDS_FETCHER -->|preloads, serializes| CA_MR_EVENT

  AGG_DATA_COLLECTOR --> DEFAULT_STAGES
  DEFAULT_STAGES -->|provides config to| CA_STAGE

  %% Presenters & GraphQL
  STAGE_PRESENTER --> CA_STAGE

  EE_STAGE_TYPE --> CA_STAGE
  EE_STAGE_TYPE --> STAGE_PRESENTER
  EE_STAGE_METRICS_TYPE --> EE_STAGE_TYPE
  EE_STAGE_METRICS_TYPE --> AGG_DATA_COLLECTOR

  %% Controller/Actions
  EE_STAGE_ACTIONS --> AGG_DATA_COLLECTOR
  EE_STAGE_ACTIONS --> CA_STAGE
  EE_STAGE_ACTIONS --> VA_SUMMARY_CYCLETIME

  %% Value Streams/Insights
  VA_SUMMARY_GROUP_BASE -->|base for| VA_SUMMARY_DEPLOY
  VA_SUMMARY_GROUP_BASE -->|base for| VA_SUMMARY_CYCLETIME
  VA_SUMMARY_DEPLOY -->|metrics source| NAMESPACE_AGG
  VA_SUMMARY_CYCLETIME -->|metric source| AGG_DATA_COLLECTOR

  INSIGHTS_MOD --> VA_SUMMARY_GROUP_BASE
  INSIGHTS_MOD --> AGG_DATA_COLLECTOR

  %% Utility Relationships
  STAGEABLE_CONCERN --> CA_STAGE_EVENT_HASH
  STAGEABLE_CONCERN -->|label support| CA_STAGE

  PARENTABLE_CONCERN --> CA_STAGE

  PG_FULL_TEXT_SEARCH --> CA_STAGE
  PG_FULL_TEXT_SEARCH --> CA_ISSUE_EVENT
  PG_FULL_TEXT_SEARCH --> CA_MR_EVENT

  %% Shared Data Structure Usage
  AGG_DATA_COLLECTOR -->|fetches/aggregates| CA_ISSUE_EVENT
  AGG_DATA_COLLECTOR -->|fetches/aggregates| CA_MR_EVENT
  AGG_DATA_COLLECTOR -->|fetches/aggregates| CA_STAGE

  AGG_RECORDS_FETCHER -->|serializes data for| EE_STAGE_TYPE
  AGG_RECORDS_FETCHER -->|serializes data for| EE_STAGE_METRICS_TYPE

  %% Data flow to API
  EE_STAGE_TYPE --> EE_STAGE_ACTIONS
  EE_STAGE_METRICS_TYPE --> EE_STAGE_TYPE

  %% Legend (no need for clickable legend, but describing groupings)
  classDef legend fill:#F8F8F8,stroke:#E6E6FA,stroke-width:0.5px,color:#888
```