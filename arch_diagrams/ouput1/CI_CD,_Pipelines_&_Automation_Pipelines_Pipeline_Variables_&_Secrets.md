```mermaid
flowchart TD
  %% Color and shape definitions
  classDef core fill:#D4F1F9,stroke:#7ecce7,stroke-width:2px,stroke-dasharray:0,rx:20,ry:20
  classDef utility fill:#FFF8DC,stroke:#FFDEAD,stroke-width:2px,stroke-dasharray:0,rx:20,ry:20
  classDef datastruct fill:#E0F8E0,stroke:#79c779,stroke-width:2px,stroke-dasharray:0,rx:20,ry:20
  classDef error fill:#FFE4E1,stroke:#FFB6B6,stroke-width:2px,stroke-dasharray:0,rx:20,ry:20
  classDef init fill:#E6E6FA,stroke:#b6b6ff,stroke-width:2px,stroke-dasharray:0,rx:20,ry:20
  classDef group fill:#F8F8F8,stroke:#b6cbe2,stroke-width:2.5px,stroke-dasharray:6 2,rx:18,ry:18

  %%---------------------------
  %% DOMAIN: Pipeline Variables & Secrets LOGICAL SKELETON

  %% GROUP: Domain Core Models
  subgraph A1 [Project, Group, Pipeline Variable Models]
    direction TB
    A1_Variable[app/models/ci/variable.rb\n"Project-level variable abstraction"]:::core
    A1_GroupVariable[app/models/ci/group_variable.rb\n"Group-level variable abstraction"]:::core
    A1_InstanceVariable[app/models/ci/instance_variable.rb\n"Instance-level variable abstraction"]:::core
    A1_PipelineVariable[app/models/ci/pipeline_variable.rb\n"Pipeline execution-scoped variable"]:::core
    A1_VariableValue[app/models/ci/variable_value.rb\n"Evaluated variable value"]:::datastruct
    A1_PersistentRef[app/models/ci/persistent_ref.rb\n"Persistent Git ref to CI pipeline"]:::utility
  end
  class A1 group

  %% GROUP: Abstract Variable Behaviors Concerns & Behaviors
  subgraph B1 [Shared Variable Logic & Behaviors]
    direction TB
    B1_HasVariable[app/models/concerns/ci/has_variable.rb\n"Abstract variable behavior"]:::utility
    B1_NewHasVariable[app/models/concerns/ci/new_has_variable.rb\n"Encrypted value support"]:::utility
    B1_HidableVariable[app/models/concerns/ci/hidable_variable.rb\n"Hiding/Masking variable state"]:::utility
    B1_Contextable[app/models/concerns/ci/contextable.rb\n"Contextual/environment-scoped variable selection"]:::utility
  end
  class B1 group

  %% GROUP: Data Transformations & Structures
  subgraph C1 [Variable Data Structures]
    direction TB
    C1_Collection[lib/gitlab/ci/variables/collection.rb\n"Variable collection/aggregation"]:::datastruct
    C1_CollectionItem[lib/gitlab/ci/variables/collection/item.rb\n"Single variable in collection"]:::datastruct
    C1_Sort[lib/gitlab/ci/variables/collection/sort.rb\n"Variable dependency sorting"]:::datastruct
    C1_Helpers[lib/gitlab/ci/variables/helpers.rb\n"Set operations merge, inherit, transform, apply inheritance"]:::utility
  end
  class C1 group

  %% GROUP: Variable Builder Pattern
  subgraph D1 [Variable Builder Pattern]
    direction TB
    D1_Builder[lib/gitlab/ci/variables/builder.rb\n"Central builder for all variables for pipeline context"]:::core
    D1_BuilderPipeline[lib/gitlab/ci/variables/builder/pipeline.rb\n"Pipeline-scoped variable builder"]:::utility
    D1_BuilderProject[lib/gitlab/ci/variables/builder/project.rb\n"Project-scoped variable builder"]:::utility
    D1_BuilderGroup[lib/gitlab/ci/variables/builder/group.rb\n"Group-scoped variable builder"]:::utility
    D1_BuilderInstance[lib/gitlab/ci/variables/builder/instance.rb\n"Instance-scoped variable builder"]:::utility
    D1_BuilderRelease[lib/gitlab/ci/variables/builder/release.rb\n"Release-scoped variable builder"]:::utility
  end
  class D1 group

  %% GROUP: Downstream Pipeline Variables Child pipelines, bridges
  subgraph E1 [Downstream Pipeline Variable Generation]
    direction TB
    E1_DownstreamBase[lib/gitlab/ci/variables/downstream/base.rb\n"Abstract downstream context"]:::utility
    E1_DownGen[lib/gitlab/ci/variables/downstream/generator.rb\n"Downstream variable generator bridges, child pipeline"]:::utility
    E1_DownRawGen[lib/gitlab/ci/variables/downstream/raw_variable_generator.rb\n"Generator raw downstream variables"]:::datastruct
    E1_DownExpandableGen[lib/gitlab/ci/variables/downstream/expandable_variable_generator.rb\n"Generator for expandable templated variables"]:::datastruct
  end
  class E1 group

  %% GROUP: Domain Services (Business Logic)
  subgraph F1 [Services: Variable & Secret Management]
    direction TB
    F1_ChangeVariableService[app/services/ci/change_variable_service.rb\n"Create/Update/Destroy variable"]:::core
    F1_ChangeVariablesService[app/services/ci/change_variables_service.rb\n"Batch variable change project/group"]:::core
    F1_UpdateInstanceVariablesService[app/services/ci/update_instance_variables_service.rb\n"Instance-level variable update"]:::core
    F1_ListConfigVariablesService[app/services/ci/list_config_variables_service.rb\n"Resolve config YAML-defined variables"]:::utility
    F1_ParseDotenvArtifactService[app/services/ci/parse_dotenv_artifact_service.rb\n"Parse dotenv artifacts into variables"]:::utility
    F1_JobTokenScopeAddGroupService[app/services/ci/job_token_scope/add_group_service.rb\n"Add group for job token sharing"]:::core
    F1_JobTokenScopeRemoveProjectService[app/services/ci/job_token_scope/remove_project_service.rb\n"Remove project from job token scope"]:::core
    F1_JobTokenScopeAddGroupOrProjectService[app/services/ci/job_token_scope/add_group_or_project_service.rb\n"Conditionally add group/project to scope"]:::core
    F1_JobTokenAutopopulateAllowlistService[app/services/ci/job_token/autopopulate_allowlist_service.rb\n"Auto-populate allowlist"]:::utility
    F1_JobTokenClearAutopopulatedAllowlistService[app/services/ci/job_token/clear_autopopulated_allowlist_service.rb\n"Clear allowlist"]:::utility
    F1_JobTokenExportAuthorizationsService[app/services/ci/job_token/export_authorizations_service.rb\n"Export allowed job authorizations"]:::utility
  end
  class F1 group

  %% GROUP: Job Token Scope Validation
  subgraph F2 [Supporting: Job Token Scope Validations]
    direction TB
    F2_EditScopeValidations[app/services/concerns/ci/job_token_scope/edit_scope_validations.rb\n"Validations for editing job token scope"]:::utility
  end
  class F2 group

  %% GROUP: Presentation/GraphQL Layer
  subgraph G1 [Presentation: GraphQL & Presenters]
    direction TB
    G1_GroupVariablePresenter[app/presenters/ci/group_variable_presenter.rb\n"Presenter: group variable"]:::utility
    G1_VariablePresenter[app/presenters/ci/variable_presenter.rb\n"Presenter: project variable"]:::utility
    G1_GraphqlVariablesResolver[app/graphql/resolvers/ci/variables_resolver.rb\n"GraphQL: variable resolver"]:::core
    G1_GraphqlGroupSortEnum[app/graphql/types/ci/group_variables_sort_enum.rb\n"GraphQL: group variable sort enums"]:::utility
    G1_GraphqlGroupVarType[app/graphql/types/ci/group_variables_sort_enum.rb\n"GraphQL: group variable type"]:::utility
    G1_GraphqlProjectVarType[app/graphql/types/ci/project_variable_type.rb\n"GraphQL: project variable type"]:::utility
    G1_GraphqlInstanceVarType[app/graphql/types/ci/instance_variable_type.rb\n"GraphQL: instance variable type"]:::utility
  end
  class G1 group

  %% GROUP: Initialization/Settings/Policies
  subgraph H1 [Initialization and Settings/Policies]
    direction TB
    H1_ProjectCiCdSetting[app/models/project_ci_cd_setting.rb\n"Project-level settings minimum variable override role etc."]:::init
  end
  class H1 group

  %% GROUP: Variable Source Discovery (Finders)
  subgraph I1 [Variable Source Finders]
    direction TB
    I1_GroupVariablesFinder[app/finders/ci/group_variables_finder.rb\n"Find and sort group variables"]:::utility
  end
  class I1 group

  %%---------------------------
  %% LOGICAL RELATIONSHIPS
  %%---------------------------

  %% Model -> Concerns
  A1_Variable -- uses --> B1_HasVariable
  A1_GroupVariable -- uses --> B1_HasVariable
  A1_GroupVariable -- uses --> B1_HidableVariable
  A1_InstanceVariable -- uses --> B1_NewHasVariable
  A1_PipelineVariable -- uses --> B1_HasVariable

  %% Concerns
  B1_NewHasVariable -- extends --> B1_HasVariable

  %% Model <-> Data structures
  A1_Variable -- included in --> D1_BuilderProject
  A1_GroupVariable -- included in --> D1_BuilderGroup
  A1_InstanceVariable -- included in --> D1_BuilderInstance
  A1_PipelineVariable -- included in --> D1_BuilderPipeline

  %% Variable Builder hierarchy
  D1_Builder -- composes --> D1_BuilderPipeline
  D1_Builder -- composes --> D1_BuilderProject
  D1_Builder -- composes --> D1_BuilderGroup
  D1_Builder -- composes --> D1_BuilderInstance
  D1_Builder -- composes --> D1_BuilderRelease

  %% VariableValue evaluates Variable
  A1_VariableValue -- evaluates --> A1_Variable
  A1_VariableValue -- evaluates --> A1_GroupVariable

  %% Data structure composition
  C1_CollectionItem -- member of --> C1_Collection
  C1_Sort -- sorts --> C1_Collection
  C1_Helpers -- operates on --> C1_Collection
  D1_BuilderPipeline -- produces --> C1_Collection
  D1_BuilderProject -- produces --> C1_Collection
  D1_BuilderGroup -- produces --> C1_Collection
  D1_BuilderInstance -- produces --> C1_Collection
  D1_BuilderRelease -- produces --> C1_Collection

  %% Variable Collection (central data flow)
  D1_Builder -- outputs --> C1_Collection
  C1_Collection -- contains --> C1_CollectionItem

  %% Downstream pipeline variable composition
  E1_DownstreamBase -- parent of --> E1_DownGen
  E1_DownGen -- composes --> E1_DownRawGen
  E1_DownGen -- composes --> E1_DownExpandableGen
  E1_DownGen -- returns --> C1_Collection

  %% Service use of data structures/models
  F1_ChangeVariableService -- mutates --> A1_Variable
  F1_ChangeVariablesService -- mutates --> A1_Variable
  F1_ChangeVariablesService -- uses --> B1_HasVariable
  F1_UpdateInstanceVariablesService -- mutates --> A1_InstanceVariable
  F1_ListConfigVariablesService -- merges --> C1_Collection
  F1_ParseDotenvArtifactService -- parses --> C1_Collection
  F1_JobTokenScopeAddGroupService -- updates --> H1_ProjectCiCdSetting
  F1_JobTokenScopeRemoveProjectService -- updates --> H1_ProjectCiCdSetting
  F1_JobTokenScopeAddGroupOrProjectService -- calls --> F1_JobTokenScopeAddGroupService

  %% Job Token Scope validations
  F1_JobTokenScopeAddGroupService -- uses --> F2_EditScopeValidations
  F1_JobTokenScopeRemoveProjectService -- uses --> F2_EditScopeValidations
  F1_JobTokenScopeAddGroupOrProjectService -- uses --> F2_EditScopeValidations

  %% Presentation layer
  G1_GroupVariablePresenter -- presents --> A1_GroupVariable
  G1_VariablePresenter -- presents --> A1_Variable
  G1_GraphqlVariablesResolver -- queries --> A1_InstanceVariable
  G1_GraphqlVariablesResolver -- queries --> A1_GroupVariable
  G1_GraphqlVariablesResolver -- queries --> A1_Variable

  %% Finders - data sourcing for variables
  I1_GroupVariablesFinder -- fetches --> A1_GroupVariable
  I1_GroupVariablesFinder -- sorts --> A1_GroupVariable

  %% Settings controls model
  H1_ProjectCiCdSetting -- restricts --> A1_Variable
  H1_ProjectCiCdSetting -- restricts --> F1_ChangeVariableService

  %% Contextable concern - scoping
  B1_Contextable -- resolves --> D1_Builder

  %% Misc Model: PersistentRef on Pipeline
  A1_PersistentRef -- references --> A1_Variable

  %%------------------------------
  %% Group Logical Relationships
  %%------------------------------

  %% Core Variable Model Family
  linkStyle 0 stroke:#399bdb,stroke-width:2.5px
  linkStyle 1 stroke:#399bdb,stroke-width:2.5px
  linkStyle 2 stroke:#399bdb,stroke-width:2.5px
  linkStyle 3 stroke:#399bdb,stroke-width:2.5px

  %% Concerns/Behaviors
  linkStyle 4 stroke:#f2bc5e,stroke-dasharray:3 2
  linkStyle 5 stroke:#f2bc5e,stroke-dasharray:3 2
  linkStyle 6 stroke:#f2bc5e,stroke-dasharray:3 2
  linkStyle 7 stroke:#f2bc5e,stroke-dasharray:3 2

  %% Builder pattern
  linkStyle 8 stroke:#20afd2,stroke-width:2
  linkStyle 9 stroke:#20afd2,stroke-width:2
  linkStyle 10 stroke:#20afd2,stroke-width:2
  linkStyle 11 stroke:#20afd2,stroke-width:2
  linkStyle 12 stroke:#20afd2,stroke-width:2

  %% Variable composition and processing
  linkStyle 13 stroke:#bad68b,stroke-width:2.5px
  linkStyle 14 stroke:#bad68b,stroke-width:2.5px

  %% Data structure pipeline
  linkStyle 15 stroke:#65b264,stroke-width:2
  linkStyle 16 stroke:#65b264,stroke-width:2
  linkStyle 17 stroke:#65b264,stroke-width:2
  linkStyle 18 stroke:#65b264,stroke-width:2
  linkStyle 19 stroke:#65b264,stroke-width:2

  %% Builder produces collections
  linkStyle 20 stroke:#72b1d0,stroke-width:2.5px,stroke-dasharray:3 1
  linkStyle 21 stroke:#72b1d0,stroke-width:2.5px,stroke-dasharray:3 1

  %% Downstream variable generation
  linkStyle 22 stroke:#dbe530,stroke-dasharray:3 2
  linkStyle 23 stroke:#dbe530,stroke-dasharray:3 2
  linkStyle 24 stroke:#dbe530,stroke-dasharray:3 2

  %% Service-model/data interactions
  linkStyle 25 stroke:#2550ce,stroke-width:2.5px
  linkStyle 26 stroke:#2550ce,stroke-width:2.5px
  linkStyle 27 stroke:#2550ce,stroke-width:2.5px
  linkStyle 28 stroke:#2550ce,stroke-width:2.5px
  linkStyle 29 stroke:#2550ce,stroke-width:2.5px
  linkStyle 30 stroke:#2550ce,stroke-width:2.5px
  linkStyle 31 stroke:#2550ce,stroke-width:2.5px
  linkStyle 32 stroke:#2550ce,stroke-width:2.5px
  linkStyle 33 stroke:#2550ce,stroke-width:2.5px
  linkStyle 34 stroke:#2550ce,stroke-width:2.5px

  %% Job token scope validations
  linkStyle 35 stroke:#fac9c9,stroke-dasharray:4 4
  linkStyle 36 stroke:#fac9c9,stroke-dasharray:4 4
  linkStyle 37 stroke:#fac9c9,stroke-dasharray:4 4

  %% Presentation / GraphQL
  linkStyle 38 stroke:#b5aeed,stroke-width:2
  linkStyle 39 stroke:#b5aeed,stroke-width:2
  linkStyle 40 stroke:#b5aeed,stroke-width:2
  linkStyle 41 stroke:#b5aeed,stroke-width:2

  %% Finders
  linkStyle 42 stroke:#bbd98e,stroke-dasharray:2 4
  linkStyle 43 stroke:#bbd98e,stroke-dasharray:2 4

  %% Settings and policy constraints
  linkStyle 44 stroke:#DD98DA,stroke-dasharray:3 2
  linkStyle 45 stroke:#DD98DA,stroke-dasharray:3 2

  %% Contextable use
  linkStyle 46 stroke:#75d3da,stroke-dasharray:2 7

  %% PersistentRef
  linkStyle 47 stroke:#fadfbf,stroke-dasharray:2 7
```