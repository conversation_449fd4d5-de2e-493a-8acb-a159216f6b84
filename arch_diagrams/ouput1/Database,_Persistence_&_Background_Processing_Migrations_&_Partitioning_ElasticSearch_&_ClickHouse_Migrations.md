```mermaid
flowchart TD
    %% Color classes as per prompt
    classDef core fill:#D4F1F9,stroke:#A6DCEF,stroke-width:2px,stroke-dasharray: 0,stroke-linejoin:round,color:#444,rx:12,ry:12
    classDef data fill:#E0F8E0,stroke:#94D7A2,stroke-width:2px,stroke-dasharray: 0,stroke-linejoin:round,color:#444,rx:12,ry:12
    classDef util fill:#FFF8DC,stroke:#FFE4A6,stroke-width:2px,stroke-dasharray:0,stroke-linejoin:round,color:#444,rx:12,ry:12
    classDef error fill:#FFE4E1,stroke:#F5C6CB,stroke-width:2px,stroke-dasharray:0,stroke-linejoin:round,color:#444,rx:12,ry:12
    classDef init fill:#E6E6FA,stroke:#C3B1E1,stroke-width:2px,stroke-dasharray:0,stroke-linejoin:round,color:#444,rx:12,ry:12
    classDef logicalGroup fill:#F8F8F8,stroke:#C8D6E6,stroke-width:4px,rx:24,ry:24

    %% SUBGRAPH: ElasticSearch Migrations Framework
    subgraph ES_Migrations["Elasticsearch Migration Framework"]
        direction TB
        es_mig[Elastic::Migration\nee/lib/elastic/migration.rb]:::core
        es_mig_opt[Migration Options\nee/lib/elastic/migration.rb]:::util
        es_mig_state[Migration State\nee/app/workers/concerns/elastic/migration_state.rb]:::core
        es_mig_record[MigrationRecord\nee/app/models/elastic/migration_record.rb]:::data
        es_mig_worker[MigrationWorker\nee/app/workers/elastic/migration_worker.rb]:::core
        es_mig_timeout[TimeoutError\nee/lib/elastic/timeout_error.rb]:::error
        es_mig_helpers_update[MigrationUpdateMappingsHelper]:::util
        es_mig_helpers_remove[MigrationRemoveFieldsHelper\nee/app/workers/concerns/search/elastic/migration_remove_fields_helper.rb]:::util
        es_mig_helpers_backfill[MigrationDatabaseBackfillHelper\nee/app/workers/concerns/search/elastic/migration_database_backfill_helper.rb]:::util
        es_mig_helpers_schema[MigrationReindexBasedOnSchemaVersion\nee/app/workers/concerns/search/elastic/migration_reindex_based_on_schema_version.rb]:::util
        es_mig_helpers_helper[MigrationHelper]:::util
        es_mig_helpers_delete[MigrationDeleteBasedOnSchemaVersion]:::util
        es_mig_core_abstraction[Migration Core Abstraction]:::core

        class ES_Migrations,logicalGroup
        es_mig_core_abstraction --> es_mig
        es_mig --options/migration state--> es_mig_opt
        es_mig --tracks/loads state--> es_mig_state
        es_mig_state --persists state--> es_mig_record
        es_mig_worker --runs--> es_mig
        es_mig_worker --uses--> es_mig_state
        es_mig_worker --handles errors--> es_mig_timeout
        es_mig_helpers_update -.includes.-> es_mig
        es_mig_helpers_remove -.includes.-> es_mig
        es_mig_helpers_backfill -.includes.-> es_mig
        es_mig_helpers_helper -.includes.-> es_mig
        es_mig_helpers_schema -.includes.-> es_mig
        es_mig_helpers_delete -.includes.-> es_mig
    end

    %% SUBGRAPH: ElasticSearch Migration Step Files
    subgraph ES_Steps["Elasticsearch Migration Step Files"]
        direction TB
        es_step1[Migration Steps\nee/elastic/migrate/*.rb]:::init
        es_step_update[Add/Update Field Migrations]:::init
        es_step_remove[Remove Field Migrations]:::init
        es_step_reindex[Reindex Based on Schema/Backfill]:::init
        es_step_special[Special Case Steps]:::init

        es_step1 --> es_step_update
        es_step1 --> es_step_remove
        es_step1 --> es_step_reindex
        es_step1 --> es_step_special
    end
    class ES_Steps,logicalGroup

    %% Group step files
    es_step_update -.includes.-> es_mig_helpers_update
    es_step_remove -.includes.-> es_mig_helpers_remove
    es_step_reindex -.includes.-> es_mig_helpers_backfill
    es_step_reindex -.includes.-> es_mig_helpers_schema
    es_step_special -.includes.-> es_mig_helpers_helper
    es_step_remove -.optional.-> es_mig_helpers_delete

    %% SUBGRAPH: Model Concerns for ElasticSearch
    subgraph ModelConcerns["Model Concerns: ElasticSearch Persistence/Update"]
        direction TB
        application_vs[ApplicationVersionedSearch\nee/app/models/concerns/elastic/application_versioned_search.rb]:::core
        repo_search[RepositoriesSearch\nee/app/models/concerns/elastic/repositories_search.rb]:::core
        wiki_search[WikiRepositoriesSearch\nee/app/models/concerns/elastic/wiki_repositories_search.rb]:::core
        group_update[MaintainElasticsearchOnGroupUpdate\nee/app/models/concerns/elastic/maintain_elasticsearch_on_group_update.rb]:::core
        ns_update[NamespaceUpdate\nee/app/models/concerns/elastic/namespace_update.rb]:::core
        issues_search[IssuesSearch\nee/app/models/concerns/search/elastic/issues_search.rb]:::core

        class ModelConcerns,logicalGroup
        application_vs --> repo_search
        application_vs --> wiki_search
        issues_search --includes--> application_vs
        group_update --updates--> application_vs
        ns_update --triggers--> application_vs
        ns_update --schedules job--> ns_update_worker[NamespaceUpdateWorker]:::core
    end

    %% SUBGRAPH: ElasticSearch Finders, Mappers and Queries
    subgraph ElasticInfra["Elasticsearch Finders / Mappers"]
        direction TB
        ep_finder[ElasticProjectsNotIndexedFinder\nee/app/finders/search/elastic_projects_not_indexed_finder.rb]:::util
        relation[Relation\nee/lib/search/elastic/relation.rb]:::util
        mapper[ResponseMapper\nee/lib/search/elastic/response_mapper.rb]:::util
        bool_expr[BoolExpr\nee/lib/search/elastic/bool_expr.rb]:::data
        response[TaskStatus\nee/lib/search/elastic/task_status.rb]:::data
        sorts[Sorts\nee/lib/search/elastic/sorts.rb]:::util
        formats[Formats\nee/lib/search/elastic/formats.rb]:::util
        indexer_spec[Indexer Spec\nee/spec/lib/gitlab/elastic/indexer_spec.rb]:::init

        class ElasticInfra,logicalGroup
        ep_finder --finds Projects--> repo_search
        relation --drives---> mapper
        mapper --maps to--> bool_expr
        mapper --maps to--> response
        relation --uses--> formats
        relation --uses--> sorts
    end

    %% SUBGRAPH: Gem Extensions for Elastic Model/Adapter/Client
    subgraph GemsElastic["GemExtensions: Elasticsearch Integrations"]
        direction TB
        gem_idx[InstanceMethods Indexing\nee/lib/gem_extensions/elasticsearch/model/indexing/instance_methods.rb]:::util
        gem_imp[Adapter::ActiveRecord::Importing\nee/lib/gem_extensions/elasticsearch/model/adapter/active_record/importing.rb]:::util
        gem_rec[Adapter::ActiveRecord::Records\nee/lib/gem_extensions/elasticsearch/model/adapter/active_record/records.rb]:::util
        gem_multi_rec[Adapter::Multiple::Records\nee/lib/gem_extensions/elasticsearch/model/adapter/multiple/records.rb]:::util
        gem_resp_rec[Response::Records\nee/lib/gem_extensions/elasticsearch/model/response/records.rb]:::util
        gem_utils[API::Utils\nee/lib/gem_extensions/elasticsearch/api/utils.rb]:::util
        gem_client[Model::Client\nee/lib/gem_extensions/elasticsearch/model/client.rb]:::util

        class GemsElastic,logicalGroup
        gem_idx --called by--> application_vs
        gem_imp --called by--> gem_idx
        gem_rec --used by--> gem_idx
        gem_multi_rec --used by--> gem_idx
        gem_resp_rec --used by--> mapper
        gem_utils --used by--> gem_client
        gem_client --used for connections--> relation
    end

    %% SUBGRAPH: ElasticSearch Latest / Custom Analysis
    subgraph ES_Custom["Elasticsearch Customization and References"]
        direction TB
        custom_ana[CustomLanguageAnalyzers\nee/lib/elastic/latest/custom_language_analyzers.rb]:::util
        emb_ref[References::Embedding\nee/lib/search/elastic/references/embedding.rb]:::data

        class ES_Custom,logicalGroup
        custom_ana --enhances mappings--> es_step_update
        emb_ref --used by--> repo_search
        emb_ref --supports--> issues_search
    end

    %% SUBGRAPH: ClickHouse Migration Engine/Core Abstractions
    subgraph CH_MigrationsCore["ClickHouse Migration Core"]
        direction TB
        ch_migration[Migration\nlib/click_house/migration.rb]:::core
        ch_iterator[Iterator\nlib/click_house/iterator.rb]:::util
        ch_schema[MigrationSupport::SchemaMigration\nlib/click_house/migration_support/schema_migration.rb]:::data
        ch_errors[MigrationSupport::Errors\nlib/click_house/migration_support/errors.rb]:::error
        ch_migrator[MigrationSupport::Migrator\nlib/click_house/migration_support/migrator.rb]:::core
        ch_context[MigrationSupport::MigrationContext\nlib/click_house/migration_support/migration_context.rb]:::util

        class CH_MigrationsCore,logicalGroup
        ch_migrator --executes--> ch_migration
        ch_context --wraps migration--> ch_migrator
        ch_migrator --tracks schema--> ch_schema
        ch_migrator --errors--> ch_errors
        ch_iterator --batching helper--> ch_migrator
    end

    %% SUBGRAPH: ClickHouse Migration Spec/Fixtures
    subgraph CH_Specs["ClickHouse Migration Specifications"]
        direction TB
        sync_cursor_spec[SyncCursor Spec\nspec/lib/click_house/sync_cursor_spec.rb]:::init
        excl_lock_spec[MigrationSupport::ExclusiveLock Spec]:::init
        base_model_spec[Models::BaseModel Spec]:::init
        client_spec[ClickHouse::Client Spec]:::init

        fixtures1[Migration Fixtures: Plain/Create/Drop\nspec/fixtures/click_house/migrations/*]:::init
        fixtures2[Migration Error Cases/Down Methods\nspec/fixtures/click_house/migrations/*]:::init

        class CH_Specs,logicalGroup
        sync_cursor_spec --validates--> ch_migration
        excl_lock_spec --tests--> ch_migrator
        base_model_spec --tests helper/model--> ch_iterator
        client_spec --tests connections--> ch_migration
        fixtures1 --test migration API--> ch_migration
        fixtures2 --test migration robustness--> ch_migration
    end

    %% SUBGRAPH: ClickHouse Migrations Files (Primary)
    subgraph CH_Migrations["ClickHouse Migration Steps"]
        direction TB

        dbmig1[ClickHouse Table & View Steps\ndb/click_house/migrate/main/*]:::init
        dbmig2[Insert/Backfill Data Migration Steps\ndb/click_house/migrate/main/*]:::init
        dbmig3[Drop/Modify Table/View Steps\ndb/click_house/migrate/main/*]:::init
        
        dbmig1 --define structure--> ch_migration
        dbmig2 --define data flow--> ch_migration
        dbmig3 --define structure change--> ch_migration
    end
    class CH_Migrations,logicalGroup

    %% SUBGRAPH: ClickHouse Client/Configuration/Core
    subgraph CH_Client["ClickHouse Client/Config & Query Engine"]
        direction TB
        ch_client_cfg[Client::Configuration\ngems/click_house-client/lib/click_house/client/configuration.rb]:::util
        ch_client_query[Client::Query\ngems/click_house-client/lib/click_house/client/query.rb]:::util

        ch_client_cfg --provides config--> client_spec
        ch_client_query --used in--> ch_migration
    end
    class CH_Client,logicalGroup

    %% SUBGRAPH: Core GitLab Database Utilities & Partitioning
    subgraph CoreDB["Core Database Utilities & Partitioning"]
        direction TB
        dyn_model[DynamicModelHelpers\nlib/gitlab/database/dynamic_model_helpers.rb]:::util
        schema_cache[SchemaCacheWithRenamedTableLegacy\nlib/gitlab/database/schema_cache_with_renamed_table_legacy.rb]:::util
        ro_relation[ReadOnlyRelation\nlib/gitlab/database/read_only_relation.rb]:::util
        conv_feat[ConvertFeatureCategoryToGroupLabel\nlib/gitlab/database/convert_feature_category_to_group_label.rb]:::util
        schema_info[GitlabSchemaInfo\nlib/gitlab/database/gitlab_schema_info.rb]:::data
        reindexing[Reindexing Engine\nlib/gitlab/database/reindexing.rb]:::core
        reindex_coord[Reindexing::Coordinator Spec\nspec/lib/gitlab/database/reindexing/coordinator_spec.rb]:::init
        reindex_notif[Reindexing::GrafanaNotifier Spec\nspec/lib/gitlab/database/reindexing/grafana_notifier_spec.rb]:::init
        reindex_conc[Reindexing::ReindexConcurrently Spec\nspec/lib/gitlab/database/reindexing/reindex_concurrently_spec.rb]:::init

        class CoreDB,logicalGroup
        reindexing --uses helpers--> dyn_model
        reindexing --partition info--> schema_info
        reindex_coord --tests--> reindexing
        reindex_notif --notifies--> reindexing
        reindex_conc --tests--> reindexing
        schema_cache --used by--> reindexing
        ro_relation --safety for batches--> dyn_model
        conv_feat --supports labeling--> schema_info
    end

    %% SUBGRAPH: Integration: Elastic & ClickHouse, QA
    subgraph IntegrationQA["Integration & Integration Testing ElasticSearch & ClickHouse"]
        direction TB
        elastic_helpers[ElasticsearchHelpers\nee/spec/support/helpers/elasticsearch_helpers.rb]:::util
        qa_user[QA Elasticsearch UserIndex Spec]:::init
        qa_issue[QA Elasticsearch IssueIndex Spec]:::init
        qa_note[QA Elasticsearch NoteIndex Spec]:::init
        qa_merge[QA Elasticsearch MRIndex Spec]:::init
        qa_commit[QA Elasticsearch CommitIndex Spec]:::init
        
        elastic_helpers --assertions--> repo_search
        elastic_helpers --assertions--> issues_search
        qa_user --runs against--> es_step1
        qa_issue --runs against--> es_step1
        qa_note --runs against--> es_step1
        qa_merge --runs against--> es_step1
        qa_commit --runs against--> es_step1
    end
    class IntegrationQA,logicalGroup

    %% SUBGRAPH: Partitioning, System Checks & Generators
    subgraph PartitioningAndSystem["Partitioning, Initialization, Generation"]
        direction TB
        sys_check[ProjectsHaveNamespaceCheck\nlib/system_check/app/projects_have_namespace_check.rb]:::util
        generator[ClickHouse Migration Generator\nlib/generators/gitlab/click_house/migration_generator.rb]:::init

        sys_check --validates db state--> reindexing
        generator --generates migration--> ch_migration
    end
    class PartitioningAndSystem,logicalGroup

    %% SUBGRAPH: ActiveContext/ElasticSearch/Opensearch Integration (Vector DB, Query, Processor)
    subgraph ActiveCtxElastic["ActiveContext Elasticsearch/Opensearch Integration"]
        direction TB
        ac_client[ElasticSearch::Client\ngems/gitlab-active-context/lib/active_context/databases/elasticsearch/client.rb]:::util
        ac_query_result[QueryResult ES\n(gems/gitlab-active-context/lib/active_context/databases/elasticsearch/query_result.rb)]:::util
        ac_adapter[Adapter\ngems/gitlab-active-context/lib/active_context/databases/elasticsearch/adapter.rb]:::core
        ac_processor[Processor ES\n(gems/gitlab-active-context/lib/active_context/databases/elasticsearch/processor.rb)]:::util
        ac_proc_osearch[Processor OpenSearch\n(gems/gitlab-active-context/lib/active_context/databases/opensearch/processor.rb)]:::util
        ac_concerns[Concerns::ElasticProcessor\ngems/gitlab-active-context/lib/active_context/databases/concerns/elastic_processor.rb]:::util
        ac_executor[Executor ES\n(gems/gitlab-active-context/lib/active_context/databases/elasticsearch/executor.rb)]:::util

        class ActiveCtxElastic,logicalGroup
        ac_adapter --provides-> ac_client
        ac_adapter --provides-> ac_query_result
        ac_processor --transforms query--> ac_concerns
        ac_proc_osearch --transforms query--> ac_concerns
        ac_client --supports querying--> es_step1
        ac_executor --executes mappings--> es_step_update
    end

    %% LOGICAL RELATIONSHIPS across major clusters

    %% Elastic Migrations <-> Core Model Concerns
    es_mig --forwards update--> application_vs

    %% ES Migration Steps <-> Infra + Model Concerns
    es_step1 --runs via worker--> es_mig_worker
    es_step1 --operates on indices--> es_mig
    es_step1 --triggers--> repo_search
    es_step1 --triggers--> issues_search
    es_step1 --triggers--> wiki_search

    %% Elastic Infra <-> GemExtensions
    relation --adapts AR model--> gem_rec
    relation --adapts multiple records--> gem_multi_rec
    relation --uses client--> gem_client

    %% ClickHouse Migrations <-> Core & Step Files
    dbmig1 --initialize tables/views--> ch_migration
    dbmig2 --backfill/insert data--> ch_migration
    dbmig3 --apply structure modifications--> ch_migration
    ch_migration --supports---> ch_migrator
    ch_migrator --uses batching helper--> ch_iterator
    ch_migrator --tracks state in--> ch_schema

    %% ClickHouse Specs/Fixtures <-> Migrations Core
    sync_cursor_spec --verifies cursor logic--> dbmig1
    base_model_spec --verifies models/batches--> dbmig1
    excl_lock_spec --tests locking--> ch_migrator

    %% ClickHouse Client <-> Core
    ch_client_cfg --supplies to--> ch_migration
    ch_client_query --used in execution--> ch_migration

    %% CoreDB/Partitioning <-> ClickHouse/ElasticSearch
    reindexing --facilitates db reindexing--> ch_migration
    reindexing --facilitates db reindexing--> es_mig
    sys_check --system validation--> reindexing

    %% ActiveContext Elasticsearch Integration <-> ES_Migrations/Steps
    ac_client --connects to--> es_step1
    ac_processor --map/query objects--> es_step1
    ac_executor --setup mappings--> es_mig_helpers_update
    ac_query_result --wraps search results--> repo_search

    %% ElasticSearch/ClickHouse QA Integration
    qa_user --integration test--> es_step1
    qa_issue --integration test--> es_step1
    qa_note --integration test--> es_step1
    qa_merge --integration test--> es_step1
    qa_commit --integration test--> es_step1

    %% Partitioning, Init and Generators
    generator --creates migration templates--> dbmig1

    %% Data structuring links
    repo_search --indexes data via--> bool_expr
    repo_search --uses struct---> response
    wiki_search --indexes data via--> emb_ref
    issues_search --tracks embeddings--> emb_ref

    %% Example: Data transformation flows
    dbmig1 --produce tables for---> ch_schema
    ch_schema --enables selection/upgrade--> ch_migration

    %% Backward links for testing and validation
    indexer_spec --tests indexing logic--> repo_search
    elastic_helpers --query/integration test--> relation
    elastic_helpers --validates mappings--> es_step_update

    %% Common Error flows
    es_mig_timeout --error for--> es_mig
    ch_errors --error for--> ch_migrator

    %% Explicit links among abstract helpers and concrete steps
    es_mig_helpers_update --facilitates field mapping--> es_step_update
    es_mig_helpers_remove --facilitates field deletion--> es_step_remove
    es_mig_helpers_schema --batch schema validation--> es_step_reindex

    %% Optional QA to Core
    qa_user -.validates global index migrations.-> es_mig
    qa_issue -.validates global index migrations.-> es_mig

    %% End of diagram
```