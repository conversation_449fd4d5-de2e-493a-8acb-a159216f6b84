```mermaid
flowchart TD
  %% =====================  COLORS & SHAPES ======================
  %% Core domain files - pastel blue
  classDef coreDomain fill:#D4F1F9,stroke:#8fc7de,stroke-width:2px,color:#222,stroke-dasharray:0,stroke-linecap:round,stroke-linejoin:round,rx:12,ry:12
  %% Supporting/utility files - pastel yellow
  classDef supporting fill:#FFF8DC,stroke:#e6dca6,stroke-width:2px,color:#222,rx:12,ry:12
  %% Data structure files - pastel green
  classDef datastruct fill:#E0F8E0,stroke:#a6e6a6,stroke-width:2px,color:#222,rx:12,ry:12
  %% Error handling files - pastel red
  classDef error fill:#FFE4E1,stroke:#ffc1c1,stroke-width:2px,color:#b91212,rx:12,ry:12
  %% Initialization files - pastel purple
  classDef init fill:#E6E6FA,stroke:#bdbdf6,stroke-width:2px,color:#4e4e6a,rx:12,ry:12
  %% Groupings - very light gray
  classDef group fill:#F8F8F8,stroke:#d8d8fd,stroke-width:2px,color:#222,rx:16,ry:16

  %% =================== ROOT SUBGRAPH: Third-Party Integrations: Cloud Providers ====================
  subgraph root[Third-Party Integrations: Cloud Providers GCP, AWS, Azure, etc.]
    direction TB
    class root group

    %% =================== SUBGRAPH: GCP Integrations Core ====================
    subgraph gcp_core[GCP Integrations Core Domain Files]
      direction TB
      class gcp_core group

      igcp_model[Google Play Integration\napp/models/integrations/google_play.rb]
      igcp_gcp_artifact_registry[Artifact Registry Integration\n ee/app/models/integrations/google_cloud_platform/artifact_registry.rb]
      igcp_wlif[Workload Identity Federation Integration\n ee/app/models/integrations/google_cloud_platform/workload_identity_federation.rb]
      igcp_controller_config[Configuration UI Controller\napp/controllers/projects/google_cloud/configuration_controller.rb]
      igcp_controller_db[Databases UI Controller\napp/controllers/projects/google_cloud/databases_controller.rb]
      igcp_controller_deploy[Deployments UI Controller\napp/controllers/projects/google_cloud/deployments_controller.rb]
      igcp_controller_serviceacc[Service Accounts UI Controller\napp/controllers/projects/google_cloud/service_accounts_controller.rb]
      igcp_controller_regions[GCP Regions UI Controller\napp/controllers/projects/google_cloud/gcp_regions_controller.rb]
      igcp_controller_revoke_oauth[Revoke GCP OAuth Controller\napp/controllers/projects/google_cloud/revoke_oauth_controller.rb]
      igcp_controller_base[GoogleCloud Base Controller\napp/controllers/projects/google_cloud/base_controller.rb]

      class igcp_model,igcp_gcp_artifact_registry,igcp_wlif coreDomain
      class igcp_controller_config,igcp_controller_db,igcp_controller_deploy,igcp_controller_serviceacc,igcp_controller_regions,igcp_controller_revoke_oauth,igcp_controller_base coreDomain

      %% UI controllers depend on base controller
      igcp_controller_config --> igcp_controller_base
      igcp_controller_db --> igcp_controller_base
      igcp_controller_deploy --> igcp_controller_base
      igcp_controller_serviceacc --> igcp_controller_base
      igcp_controller_regions --> igcp_controller_base
      igcp_controller_revoke_oauth --> igcp_controller_base

      %% Configuration UI uses ServiceAccountsService for data population
      igcp_controller_config --> gcpa_service_acc_service

      %% Artifacts and Workload Identity Fdn integration are used in deeper service layers
    end

    %% =================== SUBGRAPH: GCP Domain Services ====================
    subgraph gcp_services[GCP Domain Services]
      direction TB
      class gcp_services group

      gcpa_base_service[BaseService Abstraction\napp/services/cloud_seed/google_cloud/base_service.rb]
      gcpa_find_cloudsql[Get CloudSQL Instances\napp/services/cloud_seed/google_cloud/get_cloudsql_instances_service.rb]
      gcpa_enable_cloudsql[Enable CloudSQL\napp/services/cloud_seed/google_cloud/enable_cloudsql_service.rb]
      gcpa_create_cloudsql[Create CloudSQL Instance\napp/services/cloud_seed/google_cloud/create_cloudsql_instance_service.rb]
      gcpa_fetch_ip[Fetch Google IP List\napp/services/cloud_seed/google_cloud/fetch_google_ip_list_service.rb]
      gcpa_service_acc_service[Service Accounts Service\napp/services/cloud_seed/google_cloud/service_accounts_service.rb]
      gcpa_create_service_acc[Create Service Account\napp/services/cloud_seed/google_cloud/create_service_accounts_service.rb]

      class gcpa_base_service,gcpa_find_cloudsql,gcpa_enable_cloudsql,gcpa_create_cloudsql,gcpa_fetch_ip,gcpa_service_acc_service,gcpa_create_service_acc coreDomain

      %% Service inheritance/composition
      gcpa_find_cloudsql --> gcpa_base_service
      gcpa_enable_cloudsql --> gcpa_base_service
      gcpa_create_cloudsql --> gcpa_base_service
      gcpa_service_acc_service --> gcpa_base_service
      gcpa_create_service_acc --> gcpa_base_service

      %% Services connect to data structure and utility layers & are orchestrated by workers/controllers

      %% Service Account creation wraps utility and data
      gcpa_create_service_acc --> gcpa_service_acc_service
      gcpa_create_service_acc --> google_api_client

      gcpa_create_cloudsql --> gcpa_enable_cloudsql
      gcpa_create_cloudsql --> gcpa_base_service
      gcpa_create_cloudsql --> gcpa_find_cloudsql

      %% ServiceAccountService is foundational for finding and storing GCP service account data

    end

    %% =================== SUBGRAPH: GCP Worker Orchestration ===================
    subgraph gcp_workers[GCP Background Workers]
      direction TB
      class gcp_workers group

      gcp_worker_create_cloudsql[Create CloudSQL Instance Worker\napp/workers/google_cloud/create_cloudsql_instance_worker.rb]
      gcp_worker_fetch_iplist[Fetch Google IP List Worker\napp/workers/google_cloud/fetch_google_ip_list_worker.rb]

      class gcp_worker_create_cloudsql,gcp_worker_fetch_iplist coreDomain

      %% Workers trigger domain services
      gcp_worker_create_cloudsql --> gcpa_create_cloudsql
      gcp_worker_fetch_iplist --> gcpa_fetch_ip
    end

    %% =================== SUBGRAPH: Core Domain Utilities & Cloud Clients ===================
    subgraph gcp_utilities[GCP APIs, Libraries, Data Structures]
      direction TB
      class gcp_utilities group

      google_api_auth[Google API Auth\nlib/google_api/auth.rb]
      google_api_cp_client[Cloud Platform Client\nlib/google_api/cloud_platform/client.rb]
      google_cloud_lib[GoogleCloud Library\n ee/lib/google_cloud.rb]
      google_cloud_base_client[GoogleCloud BaseClient\n ee/lib/google_cloud/base_client.rb]
      google_cloud_artifact_client[Artifact Registry API Client\n ee/lib/google_cloud/artifact_registry/client.rb]
      google_cloud_compute_client[Compute API Client\n ee/lib/google_cloud/compute/client.rb]
      google_cloud_artifact_repo_service[Artifact Registry GetRepository Service\nee/app/services/google_cloud/artifact_registry/get_repository_service.rb]
      google_cloud_compute_regions_service[List GCP Regions Service\nee/app/services/google_cloud/compute/list_regions_service.rb]
      google_cloud_compute_zones_service[List GCP Zones Service\nee/app/services/google_cloud/compute/list_zones_service.rb]

      class google_api_auth,google_api_cp_client,google_cloud_lib supporting
      class google_cloud_base_client,google_cloud_artifact_client,google_cloud_compute_client supporting
      class google_cloud_artifact_repo_service,google_cloud_compute_regions_service,google_cloud_compute_zones_service coreDomain

      %% Cloud client library inheritance & use
      google_cloud_artifact_client --> google_cloud_base_client
      google_cloud_compute_client --> google_cloud_base_client

      google_cloud_artifact_repo_service --> google_cloud_artifact_client
      google_cloud_compute_regions_service --> google_cloud_compute_client
      google_cloud_compute_zones_service --> google_cloud_compute_client

      %% Base service and controllers rely on these for cloud actions
      gcpa_base_service --> google_api_cp_client
      gcpa_base_service --> google_api_auth
      gcpa_base_service --> google_cloud_lib

      gcpa_create_service_acc --> google_api_cp_client
      gcpa_enable_cloudsql --> google_api_cp_client

      %% Data flows: service objects call API clients/libraries under-the-hood

      %% Overlay: Artifact Registry and Compute stacks used by ArtifactRegistry and Compute domain objects, used for interacting with GCP.
      igcp_gcp_artifact_registry --> google_cloud_artifact_repo_service
      igcp_wlif --> google_cloud_lib
      igcp_wlif --> google_cloud_base_client
    end

    %% =================== SUBGRAPH: Domain Data Structures & Storage ===================
    subgraph gcp_data[GCP Domain Data Structures]
      direction TB
      class gcp_data group

      cloud_service_token[ServiceAccessToken\napp/models/cloud_connector/service_access_token.rb]
      class cloud_service_token datastruct

      %% Service uses token to access external systems
      gcpa_base_service --> cloud_service_token
      google_cloud_base_client --> cloud_service_token
      google_cloud_artifact_client --> cloud_service_token
      google_cloud_compute_client --> cloud_service_token
    end

    %% =================== SUBGRAPH: Helper Modules ===================
    subgraph gcp_helpers[Helper Modules]
      direction TB
      class gcp_helpers group

      gcp_cloudsql_helper[CloudSQL Tier/Settings Helper\napp/helpers/projects/google_cloud/cloudsql_helper.rb]
      class gcp_cloudsql_helper supporting

      igcp_controller_db --> gcp_cloudsql_helper
    end

    %% =================== SUBGRAPH: AWS Integrations ===================
    subgraph aws_core[AWS Integrations]
      direction TB
      class aws_core group

      aws_role[AWS IAM Role Model\napp/models/aws/role.rb]
      aws_s3_client[AWS S3 Client\nlib/aws/s3_client.rb]
      controller_aws_base[AWS Base Controller\napp/controllers/projects/aws/base_controller.rb]
      class aws_role,aws_s3_client,controller_aws_base coreDomain

      controller_aws_base --> aws_role
      aws_role --> aws_s3_client
    end

    %% =================== SUBGRAPH: Azure Integrations ===================
    subgraph azure_core[Azure Integrations]
      direction TB
      class azure_core group

      omni_azure_oauth[Azure OAuth2 OmniAuth\nlib/omni_auth/strategies/azure_oauth2.rb]
      class omni_azure_oauth coreDomain
    end

    %% =============== SUBGRAPH: Cross-Cloud Policy and Audit ======================
    subgraph audit_policy[Audit & Policy]
      direction TB
      class audit_policy group

      audit_gcp_logging_policy[GoogleCloud Logging Policy\nee/app/policies/audit_events/google_cloud_logging_configuration_policy.rb]
      audit_aws_policy[Amazon S3 Policy\nee/app/policies/audit_events/amazon_s3_configuration_policy.rb]
      audit_gcp_auth[GoogleCloud Auth Token Gen\nlib/audit_events/google_cloud/authentication.rb]

      class audit_gcp_logging_policy,audit_aws_policy coreDomain
      class audit_gcp_auth supporting

      gcpa_base_service --> audit_gcp_auth
      controller_aws_base --> audit_aws_policy
      igcp_controller_base --> audit_gcp_logging_policy
    end

    %% ============ SUBGRAPH: Cloud Connector Framework & Utilities ================
    subgraph cloud_connector[CloudConnector Core & Utilities]
      direction TB
      class cloud_connector group

      cc_connected_service[ConnectedService Model\nlib/cloud_connector/connected_service.rb]
      cc_base_avail_service[BaseAvailableServiceData\n ee/lib/cloud_connector/base_available_service_data.rb]
      cc_selfsigned_avail_service[SelfSigned::AvailableServiceData\nee/lib/cloud_connector/self_signed/available_service_data.rb]
      cc_missing_service[MissingServiceData\n ee/lib/cloud_connector/missing_service_data.rb]
      cc_available_services[AvailableServices\n ee/lib/cloud_connector/available_services.rb]
      cc_module[CloudConnector API Root\n ee/lib/cloud_connector.rb]

      class cc_connected_service,cc_base_avail_service,cc_selfsigned_avail_service,cc_missing_service,cc_available_services,cc_module supporting

      cc_selfsigned_avail_service --> cc_base_avail_service
      cc_available_services --> cc_base_avail_service
      cc_available_services --> cc_selfsigned_avail_service
      cc_available_services --> cc_missing_service
      cc_connected_service --> cc_base_avail_service
      cc_module --> cc_available_services
    end

    %% =========== SUBGRAPH: Status Checks & Probes (Token/Liveness Checking) ============
    subgraph status_checks[Status Checks & Probes]
      direction TB
      class status_checks group

      sc_base_probe[StatusChecks::BaseProbe\nee/app/services/cloud_connector/status_checks/probes/base_probe.rb]
      sc_token_probe[Token Probe\nee/app/services/cloud_connector/status_checks/probes/token_probe.rb]
      sc_ai_gateway_probe[AIGatewayURL Presence Probe\nee/app/services/cloud_connector/status_checks/probes/self_hosted/ai_gateway_url_presence_probe.rb]
      class sc_base_probe,sc_token_probe,sc_ai_gateway_probe supporting

      sc_token_probe --> sc_base_probe
      sc_ai_gateway_probe --> sc_base_probe

      cc_connected_service --> sc_base_probe
      cc_module --> sc_base_probe
      cloud_service_token --> sc_token_probe
    end

    %% =========== SUBGRAPH: GraphQL Layer for GCP Artifact Registry and Runners ===========
    subgraph gcp_graphql[GCP GraphQL Types & Resolvers]
      direction TB
      class gcp_graphql group

      gql_artifact_details_type[ArtifactDetailsType\n ee/app/graphql/types/google_cloud/artifact_registry/artifact_details_type.rb]
      gql_artifact_resolver[ArtifactResolver\n ee/app/graphql/resolvers/google_cloud/artifact_registry/artifact_resolver.rb]
      gql_runner_provision[RunnerGoogleCloud ProvisioningStepsResolver\nee/app/graphql/resolvers/ci/runner_google_cloud_provisioning_steps_resolver.rb]

      class gql_artifact_details_type,gql_artifact_resolver,gql_runner_provision coreDomain

      gql_artifact_resolver --> gql_artifact_details_type
      gql_artifact_resolver --> google_cloud_artifact_repo_service
    end

    %% =========== SUBGRAPH: Kubernetes Clusters (Cloud/Hybrid) ===========
    subgraph clusters_k8s[Kubernetes Cluster Integrations]
      direction TB
      class clusters_k8s group

      clusters_kubernetes[Cluster Platform: Kubernetes\napp/models/clusters/platforms/kubernetes.rb]
      class clusters_kubernetes coreDomain

      clusters_kubernetes --> gcpa_base_service
      clusters_kubernetes --> gcpa_find_cloudsql
    end

    %% ========== LINKAGE & LOGICAL INTERACTIONS ==========
    %% Domain UI Controllers (GCP) use GCP Services
    igcp_controller_config --> gcpa_base_service
    igcp_controller_db --> gcpa_find_cloudsql
    igcp_controller_deploy --> gcpa_create_cloudsql
    igcp_controller_serviceacc --> gcpa_service_acc_service

    %% Common flows: service -> API client -> token/data structure
    gcpa_find_cloudsql --> google_cloud_base_client
    gcpa_enable_cloudsql --> google_cloud_base_client
    gcpa_create_cloudsql --> google_cloud_base_client
    gcpa_service_acc_service --> google_cloud_base_client
    gcpa_create_service_acc --> google_cloud_base_client

    google_cloud_base_client --> cloud_service_token

    %% GCP integrations use CloudConnector for accessible/external service reflection
    gcpa_base_service --> cc_connected_service
    gcpa_base_service --> cc_available_services
    gcpa_base_service --> cc_base_avail_service

    %% Artifact Registry flows
    gql_artifact_resolver --> igcp_gcp_artifact_registry
    gql_artifact_resolver --> google_cloud_artifact_client
    igcp_gcp_artifact_registry --> google_cloud_artifact_client

    %% Workload Identity Federation and ArtifactRegistry flows to clients/libraries
    igcp_wlif --> google_cloud_base_client
    igcp_wlif --> google_cloud_lib

    %% Compute domain API usage (regions/zones)
    igcp_controller_regions --> google_cloud_compute_regions_service
    igcp_controller_regions --> google_cloud_compute_zones_service

    google_cloud_compute_regions_service --> google_cloud_compute_client
    google_cloud_compute_zones_service --> google_cloud_compute_client

    %% Logging / error handling / auth
    gcpa_base_service --> audit_gcp_auth
    gcpa_base_service --> google_api_auth

    %% AWS & Azure integration is symmetric: controllers reference domain models and clients

    %% Token & ServiceAccessToken propagation
    gcpa_base_service --> cloud_service_token
    google_cloud_base_client --> cloud_service_token
    google_cloud_artifact_client --> cloud_service_token
    sc_token_probe --> cloud_service_token

    %% Helper
    gcp_cloudsql_helper --> gcpa_find_cloudsql

  end
```