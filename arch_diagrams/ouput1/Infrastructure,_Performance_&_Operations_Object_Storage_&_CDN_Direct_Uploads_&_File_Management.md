```mermaid
flowchart TD
  %% DOMAIN GROUPING
  subgraph F8_Infra_Perf_Operations_ObjectStorageCDN["Infrastructure, Performance & Operations / Object Storage & CDN / Direct Uploads & File Management" ]
    direction TB
    style F8_Infra_Perf_Operations_ObjectStorageCDN fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded=true

    %% CORE DOMAIN SERVICES

    subgraph CoreDomainServices["Core Services"]
      direction TB
      style CoreDomainServices fill:#F8F8F8,stroke:#99d3ea,stroke-width:2px,rounded=true
      OS_DeleteStaleDirectUploadsService[[DeleteStaleDirectUploadsService<br/>Cleans up incomplete stale uploads]]
      style OS_DeleteStaleDirectUploadsService fill:#D4F1F9,stroke:#A9DCF1,stroke-width:1.7px,stroke-dasharray:10 4,rounded=true

      OS_PendingDirectUpload[[PendingDirectUpload<br/>Tracks pending direct uploads in Redis]]
      style OS_PendingDirectUpload fill:#E0F8E0,stroke:#89c592,stroke-width:1.4px,rounded=true

      OS_DirectUpload[[DirectUpload<br/>Represents a single direct upload instance]]
      style OS_DirectUpload fill:#E0F8E0,stroke:#89c592,stroke-width:1.4px,rounded=true

      Pages_Domain_CreateAcmeOrderService[[CreateAcmeOrderService<br/>Starts Let's Encrypt order for CDN domain]]
      style Pages_Domain_CreateAcmeOrderService fill:#D4F1F9,stroke:#A9DCF1,stroke-width:1.5px,rounded=true
    end

    %% DATA STRUCTURES

    subgraph DomainDataStructures["Data Structures"]
      direction TB
      style DomainDataStructures fill:#F8F8F8,stroke:#77d2a6,stroke-width:2px,rounded=true

      OS_PendingDirectUpload_DB[PendingDirectUpload Redis Store]
      style OS_PendingDirectUpload_DB fill:#E0F8E0,stroke:#77d2a6,stroke-width:1.3px,rounded=true

      CDN_Domain[CDN Domain Object]
      style CDN_Domain fill:#E0F8E0,stroke:#77d2a6,stroke-width:1.3px,rounded=true
    end

    %% OBJECT STORAGE / CDN ABSTRACTIONS

    subgraph ObjectStorageAbstractions["Object Storage / CDN Layer"]
      direction TB
      style ObjectStorageAbstractions fill:#F8F8F8,stroke:#A9DCF1,stroke-width:2px,rounded=true
      Pages_DeploymentUploader[[Pages DeploymentUploader<br/>Handles upload of static site deployments]]
      style Pages_DeploymentUploader fill:#D4F1F9,stroke:#A9DCF1,stroke-width:1.3px,rounded=true

      Gitlab_StatusPage_Storage[[StatusPage Storage<br/>Handles object storage for status pages, CDN upload, and errors]]
      style Gitlab_StatusPage_Storage fill:#D4F1F9,stroke:#A9DCF1,stroke-width:1.3px,rounded=true

      OS_FogGoogleGlobSupport[[fog_google_list_objects_match_glob_support.rb<br/>Enables advanced bucket listing glob patterns]]
      style OS_FogGoogleGlobSupport fill:#FFF8DC,stroke:#ffe292,stroke-width:1.2px,rounded=true

      OS_DirectUploadSupport[[direct_upload_support.rb<br/>Validates direct upload providers/config]]
      style OS_DirectUploadSupport fill:#FFF8DC,stroke:#ffe292,stroke-width:1.2px,rounded=true
    end

    %% MIDDLEWARE

    subgraph Middleware["Middleware & Interception"]
      direction TB
      style Middleware fill:#F8F8F8,stroke:#A8C1F4,stroke-width:2px,rounded=true
      GL_Middleware_Multipart[[Multipart Middleware<br/>Processes multipart direct uploads from Workhorse]]
      style GL_Middleware_Multipart fill:#FFF8DC,stroke:#ffe292,stroke-width:1.2px,rounded=true

      GL_Middleware_PathTraversal[[PathTraversalCheck Middleware<br/>Guards file uploads from traversal attacks]]
      style GL_Middleware_PathTraversal fill:#FFF8DC,stroke:#ffe292,stroke-width:1.2px,rounded=true
    end

    %% SUPPORTING UTILITIES

    subgraph SupportingUtilities["Supporting/Utility Layer"]
      direction TB
      style SupportingUtilities fill:#F8F8F8,stroke:#ffe292,stroke-width:2px,rounded=true

      GL_Gzip[gzip.rb<br/>Compression Utility]
      style GL_Gzip fill:#FFF8DC,stroke:#ffe292,stroke-width:1.1px,rounded=true

      GL_GonHelper[gon_helper.rb<br/>Expose file size/bucket attrs to frontend]
      style GL_GonHelper fill:#FFF8DC,stroke:#ffe292,stroke-width:1.1px,rounded=true

      GL_AssetProxy[asset_proxy.rb<br/>CDN proxy helper]
      style GL_AssetProxy fill:#FFF8DC,stroke:#ffe292,stroke-width:1.1px,rounded=true

      GL_UrlHelpers[url_helpers.rb<br/>Generate normalized URLs]
      style GL_UrlHelpers fill:#FFF8DC,stroke:#ffe292,stroke-width:1.1px,rounded=true

      ********************[redis_store_factory.rb<br/>Patches Redis store for compatibility]
      style ******************** fill:#FFF8DC,stroke:#ffe292,stroke-width:1.1px,rounded=true

      GL_WebpackDevServer[webpack/dev_server_middleware.rb]
      style GL_WebpackDevServer fill:#FFF8DC,stroke:#ffe292,stroke-width:1.1px,rounded=true
    end

    %% THIRD-PARTY/WIDER INFRA ASTRACTIONS

    subgraph InfraExternalAbstractions["Infra/Ext. Abstractions"]
      direction TB
      style InfraExternalAbstractions fill:#F8F8F8,stroke:#B1AFE4,stroke-width:2px,rounded=true

      GL_LetsEncryptOrder[[lets_encrypt/order.rb<br/>Wrap ACME order for SSL]]
      style GL_LetsEncryptOrder fill:#FFF8DC,stroke:#ffe292,stroke-width:1.1px,rounded=true

      GL_LetsEncryptChallenge[[lets_encrypt/challenge.rb<br/>Handles Let's Encrypt ACME challenge]]
      style GL_LetsEncryptChallenge fill:#FFF8DC,stroke:#ffe292,stroke-width:1.1px,rounded=true
    end

    %% ERROR HANDLING

    subgraph ErrorHandling["Error Handling"]
      direction TB
      style ErrorHandling fill:#F8F8F8,stroke:#FFB6B9,stroke-width:2px,rounded=true
      Gitlab_StatusPage_Storage_Error[[StatusPage::Storage::Error<br/>Represents storage/CDN errors]]
      style Gitlab_StatusPage_Storage_Error fill:#FFE4E1,stroke:#FFB6B9,stroke-width:1.1px,rounded=true
    end

    %% INITIALIZATION

    subgraph Initialization["Initialization & Integration"]
      direction TB
      style Initialization fill:#F8F8F8,stroke:#D1B3E1,stroke-width:2px,rounded=true
      FogGoogleGlobInitializer[[fog_google_list_objects_match_glob_support.rb<br/>initializer]]
      style FogGoogleGlobInitializer fill:#E6E6FA,stroke:#B790E8,stroke-width:1.1px,rounded=true

      DirectUploadSupportInitializer[[direct_upload_support.rb<br/>initializer]]
      style DirectUploadSupportInitializer fill:#E6E6FA,stroke:#B790E8,stroke-width:1.1px,rounded=true
    end

    %% SPEC/TEST LAYER

    subgraph SpecificationTesting["Specification & Testing"]
      direction TB
      style SpecificationTesting fill:#F8F8F8,stroke:#d2d2d2,stroke-width:2px,rounded=true
      Spec_ObjectStorageCDNGoogleCDN[[google_cdn_spec.rb<br/>Validates CDN integration]]
      style Spec_ObjectStorageCDNGoogleCDN fill:#FFF8DC,stroke:#ffe292,stroke-width:1.1px,rounded=true

      Spec_Middleware_PathTraversal[[path_traversal_check_spec.rb<br/>Tests security around path uploads]]
      style Spec_Middleware_PathTraversal fill:#FFF8DC,stroke:#ffe292,stroke-width:1.1px,rounded=true
    end
  end

  %% ---- RELATIONSHIPS ---

  %% -- Service uses and collaboration
  OS_DeleteStaleDirectUploadsService -- Reads/Writes --> OS_PendingDirectUpload
  OS_PendingDirectUpload -- "Uses Redis store" --- OS_PendingDirectUpload_DB

  OS_DeleteStaleDirectUploadsService -- "Cleans up" --> OS_PendingDirectUpload_DB

  %% -- PendingDirectUpload and DirectUpload structural
  OS_PendingDirectUpload -- "Prepares/Persists data for" --> OS_DirectUpload

  OS_DirectUpload -- "Used by" --> GL_Middleware_Multipart

  %% --- Middleware Connections
  GL_Middleware_Multipart -- "Processes uploads via" --> OS_DirectUpload
  GL_Middleware_Multipart -- "Validates params with" --> OS_DirectUploadSupport

  %% --- CDN Context Connections
  Pages_DeploymentUploader -- "Uploads to" --> ObjectStorageAbstractions
  Pages_DeploymentUploader -- "Uses" --> OS_DirectUpload
  Pages_DeploymentUploader -- "Handles" --> CDN_Domain

  Gitlab_StatusPage_Storage -- "Uploads to CDN / Handles" --> CDN_Domain
  Gitlab_StatusPage_Storage -- "Handles storage for" --> OS_DirectUpload
  Gitlab_StatusPage_Storage -- "Signals errors to" --> Gitlab_StatusPage_Storage_Error

  %% --- External integration for certificates ACME/Let's Encrypt
  Pages_Domain_CreateAcmeOrderService -- "Creates order via" --> GL_LetsEncryptOrder
  GL_LetsEncryptOrder -- "Challenges domain via" --> GL_LetsEncryptChallenge
  Pages_Domain_CreateAcmeOrderService -- "Works with" --> CDN_Domain

  %% --- Initializer Influence
  DirectUploadSupportInitializer -- "Initializes / patches config for" --> OS_DirectUploadSupport

  FogGoogleGlobInitializer -- "Extends API for" --> OS_FogGoogleGlobSupport
  OS_FogGoogleGlobSupport -- "Enhances" --> ObjectStorageAbstractions

  %% --- Utility Use
  GL_Gzip -- "Compresses/decompresses data for" --> Gitlab_StatusPage_Storage

  GL_GonHelper -- "Pushes file/bucket info to" --> Pages_DeploymentUploader

  GL_AssetProxy -- "Resolves/proxies" --> Gitlab_StatusPage_Storage

  GL_UrlHelpers -- "Provides URL normalization for" --> Pages_DeploymentUploader
  GL_UrlHelpers -- "Supports URL for" --> Gitlab_StatusPage_Storage

  ******************** -- "Supports/patches Redis for" --> OS_PendingDirectUpload_DB

  %% --- Test yield
  Spec_ObjectStorageCDNGoogleCDN -- "Tests features of" --> Gitlab_StatusPage_Storage

  Spec_Middleware_PathTraversal -- "Tests for" --> GL_Middleware_PathTraversal

  GL_Middleware_PathTraversal -- "Middleware for upload path security" --> GL_Middleware_Multipart

  %% --- Initialization to Core Services
  DirectUploadSupportInitializer -- "Enables features in" --> OS_DirectUpload

  FogGoogleGlobInitializer -- "Affects" --> OS_FogGoogleGlobSupport

  %% -- Extra: StatusPage::Storage <-> Error

  Gitlab_StatusPage_Storage -- "Raises" --> Gitlab_StatusPage_Storage_Error

  %% -- Extra: Domain Structure Mapping

  OS_DirectUpload -- "References/idempotency via" --> OS_PendingDirectUpload_DB

  %% -- End connections
```