```mermaid
flowchart TD
  %% COLOR DEFINITIONS (PASTEL)
  %% Core domain files: #D4F1F9
  %% Supporting/utility files: #FFF8DC
  %% Data structure files: #E0F8E0
  %% Error handling files: #FFE4E1
  %% Initialization/setup files: #E6E6FA
  %% Logical groupings/subgraphs: #F8F8F8

  %% GROUP: CONTROLLER SPECS (Request/Acceptance and Behavior tests)
  subgraph Controllers & Behavior Specs ["Controller/Test Specs"]
    direction TB
    style Controllers & Behavior Specs fill:#F8F8F8,stroke:#94c3e7,stroke-width:2px,rounded
    c1["ee/spec/requests/projects/merge_requests_controller_spec.rb
    <br>Tests HTTP behaviors, access, and MR UI flows":::core]
    c2["ee/spec/requests/projects/issues_controller_spec.rb
    <br>Validates Issue controller behavior and domain flows":::core]
    c3["ee/spec/requests/groups/epics/epic_links_controller_spec.rb
    <br>Tests API & controller behavior for Epics linking":::core]
    c4["ee/spec/requests/groups/epics/related_epic_links_controller_spec.rb
    <br>Validates managing related epics via controller":::core]
    c5["ee/spec/requests/projects/commit_controller_spec.rb
    <br>Validates HTTP commit controller for verification":::core]
    c6["ee/spec/requests/groups/analytics/cycle_analytics/value_streams_controller_spec.rb
    <br>Acceptance specs for VSM analytics access & listing":::core]
    c7["ee/spec/requests/admin/users_controller_spec.rb
    <br>Checks admin management endpoints":::core]
    c8["ee/spec/requests/admin/subscriptions_controller_spec.rb
    <br>Validates subscriptions management endpoints":::core]
    c9["ee/spec/controllers/concerns/routable_actions_spec.rb
    <br>Shared concern tests for routable controller logic":::support]
    c10["ee/spec/controllers/projects/merge_requests_controller_spec.rb
    <br>Extends MR controller coverage":::core]
    c11["ee/spec/requests/groups/insights_controller_spec.rb
    <br>Ensures controller test coverage for group insights":::core]
    c12["ee/spec/requests/projects/insights_controller_spec.rb
    <br>Project-level insights controller specs":::core]
    c13["ee/spec/requests/api/related_epic_links_spec.rb
    <br>API endpoint coverage for relationships between epics":::core]
    c14["ee/spec/requests/omniauth_kerberos_spec.rb
    <br>Validates authentication controller for Kerberos":::core]
    c15["ee/spec/requests/projects/merge_requests_controller_spec.rb
    <br>Project MR controller extended specs":::core]
    c16["ee/spec/mailers/notify_spec.rb
    <br>Ensures correct notification/communication flows":::support]
    c17["ee/spec/requests/api/submodules_spec.rb
    <br>API coverage for submodule feature endpoints":::core]
    c18["ee/spec/requests/groups/security/compliance_dashboard/frameworks_controller_spec.rb
    <br>Controller tests for compliance framework endpoints":::core]
    c19["ee/spec/requests/admin/users_controller_spec.rb
    <br>Ensures user provisioning, permissions tested":::core]
    c20["ee/spec/requests/api/graphql_spec.rb
    <br>GraphQL API endpoint coverage and behavior specs":::core]
    c21["ee/spec/requests/projects/issue_feature_flags_controller_spec.rb
    <br>Feature flag to issues controller request specs":::core]
    c22["ee/spec/requests/projects/environments_controller_spec.rb
    <br>Environments controller request specs for EE":::core]
    c23["ee/spec/requests/groups/analytics/dashboards_controller_spec.rb
    <br>Validates group analytics dashboard access":::core]
    c24["ee/spec/requests/groups/clusters_controller_spec.rb
    <br>Tests group-level cluster controller endpoints":::core]
    c25["ee/spec/requests/groups/labels_spec.rb
    <br>Group label controller coverage":::core]
    c26["ee/spec/requests/smartcard_controller_spec.rb
    <br>Validates smartcard auth endpoints":::core]
    c27["ee/spec/requests/groups/two_factor_auths_controller_spec.rb
    <br>Coverage for group-level 2FA endpoints":::core]
    c28["ee/spec/requests/admin/application_settings/scim_oauth_controller_spec.rb
    <br>SCIM OAuth controller for admin settings":::core]
    c29["ee/spec/requests/git_http_geo_spec.rb
    <br>Extend HTTP/Git test with geo":::core]
    c30["ee/spec/requests/api/internal/ai/x_ray/scan_spec.rb
    <br>AI XRay internal scan endpoints":::core]
    c31["ee/spec/requests/api/code_suggestions_spec.rb
    <br>API code suggestions endpoint controller tests":::core]

    %% More controllers clustered by "Admin," "Groups," "Projects," "API"
    subgraph Controllers_Admin ["Admin-related specs"]
      direction TB
      c7
      c8
      c19
      c28
    end
    subgraph Controllers_Groups ["Group-level controller specs"]
      direction TB
      c3
      c4
      c6
      c11
      c23
      c24
      c25
      c27
      c18
    end
    subgraph Controllers_Projects ["Project-level controller specs"]
      direction TB
      c1
      c2
      c5
      c10
      c12
      c15
      c21
      c22
      c29
      c26
    end
    subgraph Controllers_API ["API & GraphQL endpoint specs"]
      direction TB
      c13
      c17
      c20
      c30
      c31
    end
    subgraph Controllers_Auth ["Authentication & Security"]
      direction TB
      c14
      c16
    end
    c9
  end

  %% GROUP: SERVICE & WORKER SPECS Internal/Background Behavior
  subgraph Worker & Service Specs ["Service/Worker Spec Layer"]
    direction TB
    style Worker & Service Specs fill:#F8F8F8,stroke:#94d7af,stroke-width:2px,rounded
    w1["ee/spec/workers/group_saml_group_sync_worker_spec.rb
    <br>Worker for group SAML sync background logic":::core]
    w2["ee/spec/workers/merge_request_reset_approvals_worker_spec.rb
    <br>Worker test for resetting approvals on MRs":::core]
    w3["ee/spec/workers/auth/saml_group_sync_worker_spec.rb
    <br>Background worker specs for SAML group sync":::core]
  end

  %% GROUP: CONTRACT/SUPPORT Contract/Pact, Shared Examples, Utilities
  subgraph Support & Contracts ["Support/Contracts & Shared Examples"]
    direction TB
    style Support & Contracts fill:#F8F8F8,stroke:#d7d494,stroke-width:2px,rounded
    s1["ee/spec/contracts/provider/pact_helpers/project/merge_requests/show/get_suggested_reviewers_helper.rb
    <br>Pact contract for MR suggested reviewers API":::support]
    s2["ee/spec/support/shared_examples/controllers/analytics/cycle_analytics/value_stream_shared_examples.rb
    <br>Shared examples for value stream analytics controllers":::support]
  end

  %% GROUP: DATA STRUCTURES
  subgraph Domain Data Concepts ["Domain Data Structures"]
    direction TB
    style Domain Data Concepts fill:#F8F8F8,stroke:#a1eaa1,stroke-width:2px,rounded
    d1["MR Approval State
    <br>Created/Consumed by: MergeRequestResetApprovalsWorker,
    Projects::MergeRequestsController/Spec,
    MergeRequest domain":::data]
    d2["Epic Links Structure
    <br>Validated via: groups/epics/epic_links_controller_spec & related_epic_links_controller_spec":::data]
    d3["ComplianceFramework Data
    <br>Tested by: compliance_dashboard/frameworks_controller_spec":::data]
    d4["Cluster Sync State
    <br>Used in: groups/clusters_controller_spec, group_saml_group_sync_worker_spec":::data]
    d5["Group Analytics Value Streams
    <br>Shared by: value_stream_shared_examples.rb, analytics/cycle_analytics specs":::data]
    d6["Suggested MR Reviewers Payload
    <br>Defined in: get_suggested_reviewers_helper.rb":::data]
    d7["Code Suggestions Response Structure
    <br>Validated by: api/code_suggestions_spec.rb":::data]
    d8["User Authentication State
    <br>Tested throughout: omniauth_kerberos_spec, smartcard_controller_spec, two_factor_auths_controller_spec":::data]
  end

  %% GROUP: DOMAIN PATTERNS/ABSTRACTIONS
  subgraph Domain Patterns & Abstractions ["Patterns, Reusability & Testing Infrastructure"]
    direction TB
    style Domain Patterns & Abstractions fill:#F8F8F8,stroke:#b7aee7,stroke-width:2px,rounded
    p1["Request Spec Pattern
    <br>Verifies controller endpoints and API/public UX":::support]
    p2["Shared Examples
    <br>Reusable test behaviors for multiple controller/specs":::support]
    p3["Background Worker Pattern
    <br>Decouples long-running tasks, validated via worker specs":::support]
    p4["Contract/Pact Testing
    <br>Enforces request-response agreement on consumers/providers":::support]
  end

  %% NODE STYLE ASSIGNMENTS
  classDef core fill:#D4F1F9,stroke:#b3dbe8,stroke-width:2px,color:#333,stroke-dasharray:0 0,rounded
  classDef support fill:#FFF8DC,stroke:#ede5b3,stroke-width:2px,color:#333,stroke-dasharray:0 0,rounded
  classDef data fill:#E0F8E0,stroke:#94d7af,stroke-width:2px,color:#333,stroke-dasharray:0 0,rounded

  %% LOGICAL RELATIONSHIPS/EDGES

  %% Controllers request/acceptance specs rely on shared examples and utility layers
  c6 -- "Uses shared_examples for analytics" --> s2
  c6 -- "Tests processing of" --> d5
  c11 -- "Uses shared_examples for analytics" --> s2
  c11 -- "Validates" --> d5
  c23 -- "Value Stream" --> d5
  c23 -- "Coverage complements" --> c6
  c25 -- "Validates group label data" --> d3
  c2 -- "Creates/uses Issues structure" --> d1
  c1 -- "Drives MR domain logic" --> d1 
  c1 -- "Shares MR-related data structures" --> d1
  c3 -- "Validates Epic Links" --> d2
  c4 -- "Related Epic Links API" --> d2
  c5 -- "Commit verification test" --> d1

  c16 -- "Sends domain email notifications for events in controllers" --> c1
  c16 -- "Verifies MR/Issue notification flows" --> c2
  c16 -- "Covers user/account events" --> c7

  %% API/contract specs use pact helpers
  c13 -- "Enforces contract for" --> s1
  c1 -- "Receives contract coverage" --> s1
  s1 -- "Defines contract for suggested reviewers" --> d6
  c31 -- "Validates suggestions endpoint" --> d7
  c31 -- "API results used in MR UI" --> c1

  %% Auth specs test session and authentication state
  c14 -- "Validates OMNI authentication" --> d8
  c26 -- "Smartcard: ensures strong authentication" --> d8
  c27 -- "Covers group-level 2FA" --> d8

  %% Compliance/Framework relationships
  c18 -- "Checks compliance framework CRUD" --> d3
  c18 -- "Is accessed/covered by admin" --> c7
  c18 -- "Interplays with group security" --> c24

  %% Cluster/Sync/Background workers
  c24 -- "States create or sync clusters" --> d4
  w1 -- "Sync logic for SAML to groups" --> d4
  w3 -- "Additional SAML sync background job" --> d4
  c24 -- "May enqueue" --> w1
  c24 -- "Delegates periodic syncs to" --> w3

  %% Value Stream Analytics
  c6 -- "Reports on analytics value streams" --> d5
  s2 -- "Reusable scenarios for all controllers on value streams" --> c6
  s2 -- "Shared Across" --> c11

  %% MR Approval Resets
  c1 -- "Can trigger approval reset in background worker" --> w2
  w2 -- "Resets approvals in MR state" --> d1

  %% Contracts support
  s1 -- "Defines contract for suggested reviewers" --> c1

  %% Abstractions/Pattern Usage
  Controllers & Behavior Specs -- "Implements" --> p1
  Worker & Service Specs -- "Implements" --> p3
  Support & Contracts -- "Framework for" --> p2
  d7 -- "Enforced by contract" --> p4

  %% Cross-cutting: Test pattern/abstraction relationships for DRYness and maintainability
  c9 -- "Is a concern tested for reusability" --> p2
  s2 -- "Provides shared test logic" --> p2

  %% Request specs interact with data structures and workers
  c13 -- "Covers epic relationships" --> d2
  c6 -- "Consumes/covers value stream analytics" --> d5
  c21 -- "Covers issue-feature-flag links at project level" --> d1
  c22 -- "Environments spec covers deployment/CI environments" --> d4

  %% More relationships: coverage, shared expectations, utility
  c18 -- "Compliance dashboard interacts with" --> c24
  c31 -- "Code suggestions depend on project data, tested via" --> c1
  w2 -- "Background worker updating MR state, enqueued by controller specs" --> c1

  %% Explicitly show utility relation
  s2 -- "Covers VSA behaviors for controllers" --> c6
  s2 -- "Shared with analytics controller specs" --> c11

  %% Pattern reuse
  p2 -- "Reusable across controller specs, shared_examples & support" --> c9
  p2 -- "Backed by shared_examples files" --> s2

  %% Service/worker specs implement worker pattern
  w1 -- "Implements regular SAML sync" --> p3
  w2 -- "Implements MR approval logic in background" --> p3
  w3 -- "Background group sync by SAML" --> p3

  %% Cross-pact/contract pattern
  s1 -- "Pact contract enforces MR data" --> p4

  %% Cross-cutting: test and data structure patterns
  Controllers & Behavior Specs -- "Validates domain structure & flows using" --> Domain Data Concepts
  Controllers & Behavior Specs -- "Reuses" --> Support & Contracts
  Worker & Service Specs -- "Coordinates background domain behaviors for" --> Domain Data Concepts
  Support & Contracts -- "Is patternized via" --> Domain Patterns & Abstractions

  %% CLASS ASSIGNMENT
  class c1,c2,c3,c4,c5,c6,c7,c8,c10,c11,c12,c13,c14,c15,c17,c18,c19,c20,c21,c22,c23,c24,c25,c26,c27,c28,c29,c30,c31 core
  class w1,w2,w3 core
  class s1,s2 support
  class d1,d2,d3,d4,d5,d6,d7,d8 data
  class p1,p2,p3,p4 support
  class c9 support
```