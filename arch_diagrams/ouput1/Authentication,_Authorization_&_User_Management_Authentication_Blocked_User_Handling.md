```mermaid
flowchart TD
  %% VERTICAL LAYOUT
  %% GROUP 1: CORE BLOCKED USER LOGIC
  subgraph S1["Blocked User Management Services" ]
    direction TB
    style S1 fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded=true

    BSERV["BlockService
Handles explicit user block operations
app/services/users/block_service.rb"]
      style BSERV fill:#D4F1F9,stroke:#9AD7E2,stroke-width:2,stroke-dasharray:5 5,rounded=true

    AUTOBAN["AutoBanService
Performs automated user banning based on policy
app/services/users/auto_ban_service.rb"]
      style AUTOBAN fill:#D4F1F9,stroke:#9AD7E2,stroke-width:2,stroke-dasharray:5 5,rounded=true

    UNTRUST["UntrustService
Removes user's trusted status, support for spam context
app/services/users/untrust_service.rb"]
      style UNTRUST fill:#D4F1F9,stroke:#9AD7E2,stroke-width:2,stroke-dasharray:5 5,rounded=true

    LDAPREP["RepairLdapBlockedService
Synchronizes LDAP block state for users
app/services/users/repair_ldap_blocked_service.rb"]
      style LDAPREP fill:#D4F1F9,stroke:#9AD7E2,stroke-width:2,stroke-dasharray:5 5,rounded=true
  end

  %% GROUP 2: DATA STRUCTURES
  subgraph S2["Blocked User Domain Data Structures" ]
    direction TB
    style S2 fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2,rounded=true

    BUSER["User Blocked State
Core entity holding 'blocked?' and block status/metadata
"]
      style BUSER fill:#E0F8E0,stroke:#A8D9A8,stroke-width:2,rounded=true

    AUTHRES["Auth::Result
Authentication outcome with blocked/geo states
ee/lib/ee/gitlab/auth/result.rb"]
      style AUTHRES fill:#E0F8E0,stroke:#A8D9A8,stroke-width:2,rounded=true

    CAL["Custom Attributes Log
Tracks reasons, triggers, timestamps for ban/block events
"]
      style CAL fill:#E0F8E0,stroke:#A8D9A8,stroke-width:2,stroke-dasharray:3 2,rounded=true
  end

  %% GROUP 3: SUPPORTING/UTILITY LOGIC
  subgraph S3["Supporting & Utility Files" ]
    direction TB
    style S3 fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,rounded=true

    IBLIM["UniqueIpsLimiter
Limits and counts user sign-ins from unique IPs to trigger block
spec/lib/gitlab/auth/unique_ips_limiter_spec.rb, underlying logic"]
      style IBLIM fill:#FFF8DC,stroke:#D7CFA7,stroke-width:2,rounded=true

    BUTRACK["BlockedUserTracker
Logs blocked activity, reacts to state change of user
lib/gitlab/auth/blocked_user_tracker.rb"]
      style BUTRACK fill:#FFF8DC,stroke:#D7CFA7,stroke-width:2,rounded=true
  end

  %% GROUP 4: ERROR HANDLING
  subgraph S4["Error Handling & Exceptions" ]
    direction TB
    style S4 fill:#F8F8F8,stroke:#FFE4E1,stroke-width:2,rounded=true

    IPBLOCKED["IpBlocked Error
Exception for block due to excessive failed attempts
lib/gitlab/auth/ip_blocked.rb"]
      style IPBLOCKED fill:#FFE4E1,stroke:#DF8D85,stroke-width:2,rounded=true
  end

  %% GROUP 5: INITIALIZATION/INFRASTRUCTURE
  subgraph S5["Initialization & Infrastructure" ]
    direction TB
    style S5 fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2,rounded=true

    BASESERV["BaseService Inherited
Abstract for consistent service logic and error/success states
"]
      style BASESERV fill:#E6E6FA,stroke:#D6C3F7,stroke-width:2,rounded=true
  end

  %% INTERACTIONS & LOGICAL CONNECTIVITY

  %% BlockService and related core services depend on BaseService
  BSERV -.-> BASESERV
  UNTRUST -.-> BASESERV

  %% Core services operate on User data
  BSERV -- modifies/block state --> BUSER
  AUTOBAN -- bans & updates attributes --> BUSER
  LDAPREP -- checks/repairs blocked status --> BUSER
  UNTRUST -- removes trust/spam attrs --> BUSER

  %% Custom attributes log for auditing auto-bans/blocks
  AUTOBAN -- logs reason/event --> CAL
  BSERV -- may update logs --> CAL

  %% RepairLdapBlockedService may read/update Auth::Result state
  LDAPREP -- triggers update on --> AUTHRES

  %% AutoBanService uses error pattern for exceptional flows
  AUTOBAN -.-> IPBLOCKED

  %% Unique IPs Limiter may trigger block using BlockService and raise IpBlocked
  IBLIM -- may trigger --> BSERV
  IBLIM -- raises error on threshold exceeded --> IPBLOCKED

  %% BlockedUserTracker logs state changes & activity
  BSERV -- notifies --> BUTRACK
  AUTOBAN -- notifies --> BUTRACK
  LDAPREP -- notifies --> BUTRACK

  %% Auth::Result interacts with user for authentication and block feedback
  AUTHRES -- queries user 'blocked?' state --> BUSER

  %% Bidirectional pointer: AUTOBAN/BSERV set blocked, tracker logs after change
  AUTOBAN -- updates status/logs --> BUTRACK
  BSERV -- updates status/logs --> BUTRACK

  %% Utility: Untrust service affects only user's trust (e.g. related to spam blocks)
  UNTRUST -- adjusts trust attribute --> BUSER

  %% LDAP block / untrust may have info symmetry with Auth::Result
  LDAPREP -- syncs state <--> AUTHRES

  %% Auth::Result is leveraged in broader authentication flows
  AUTHRES -- feeds result to --> IBLIM

  %% Spec and implementation linkage (test file to infra)
  IBLIM -. test coverage .-> BSERV
  IBLIM -. test coverage .-> AUTOBAN

  %% Error handling is applied by multiple services
  BSERV -. error/exception .-> IPBLOCKED
  LDAPREP -. error reporting .-> IPBLOCKED

  %% Diagnostic tracing (BlockedUserTracker to custom logging)
  BUTRACK -- audit log entry --> CAL

  %% Cross-Group link styling for readability
  linkStyle 0,1,19,20,21,22,23,27,28 stroke:#CACFD2,stroke-width:2
  linkStyle 2,3,4,5,6,7,8,9,10,11 stroke:#9AD7E2,stroke-width:2,stroke-dasharray:7 2
  linkStyle 12,13,14 stroke:#D7CFA7,stroke-width:2,stroke-dasharray:2 4
  linkStyle 15,16,24,25,26 stroke:#A8D9A8,stroke-width:2,stroke-dasharray:3 2
  linkStyle 17,18 stroke:#DF8D85,stroke-width:2,stroke-dasharray:1 1
```