```mermaid
flowchart TB
  %% Styles
  %% Core Domain Files
  classDef core fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2px,color:#222,rx:8,ry:8;
  %% Supporting/Utility Files
  classDef util fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2px,color:#333,rx:8,ry:8;
  %% Data Structure Files
  classDef data fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2px,color:#222,rx:8,ry:8;
  %% Error Handling Files
  classDef error fill:#FFE4E1,stroke:#FFE4E1,stroke-width:2px,color:#222,rx:8,ry:8;
  %% Initialization/Setup Files
  classDef init fill:#E6E6FA,stroke:#E6E6FA,stroke-width:2px,color:#222,rx:8,ry:8;
  %% Logical Groupings
  classDef domain fill:#F8F8F8,stroke:#D4F1F9,stroke-width:3px,rx:10,ry:10;
  classDef subdomain fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2px,rx:8,ry:8;

  %% GROUP: Group Membership & Identity Management
  subgraph GROUP_IDENTITY["Group Membership & Identity Management"]
    direction TB
    mgm1["members/last_group_owner_assigner.rb":::core]
    mgm2["members/members/invited_private_group_accessibility_assigner.rb":::core]
    finder1["finders/namespaces/groups/invited_groups_finder.rb":::core]
    finder2["finders/joined_groups_finder.rb":::core]
    finder3["finders/namespaces/groups/shared_groups_finder.rb":::core]
    rolep["finders/concerns/members/role_parser.rb":::util]
    membermodel["ee/app/models/ee/member.rb":::core]
    groupmembermodel["ee/app/models/ee/group_member.rb":::core]
    helper1["helpers/groups/group_members_helper.rb":::util]
    helper2["ee/app/helpers/ee/groups/group_members_helper.rb":::util]
    controller1["controllers/groups/group_members_controller.rb":::core]
    policy["policies/group_member_policy.rb":::core]
    worker1["workers/members/prune_deletions_worker.rb":::core]
    workergexport["ee/app/workers/members/groups/base_memberships_export_worker.rb":::core]
    workergdexport["ee/app/workers/members/groups/export_detailed_memberships_worker.rb":::core]
    graphql1["ee/app/graphql/mutations/members/groups/export.rb":::core]
    graphql2["ee/app/graphql/ee/resolvers/pending_group_members_resolver.rb":::core]

    mgm1 --> groupmembermodel
    mgm2 --> membermodel
    mgm2 --> finder1
    finder1 --> helper1
    finder1 --> helper2
    finder1 --> mgm2
    finder2 --> groupmembermodel
    finder3 --> groupmembermodel
    controller1 --> helper1
    helper1 --> rolep
    helper2 --> helper1
    helper2 --> rolep
    controller1 --> policy
    controller1 --> mgm1
    controller1 --> mgm2
    groupmembermodel --> membermodel
    workergexport --> controller1
    workergdexport --> workergexport
    graphql1 --> workergexport
    graphql1 --> controller1
    graphql2 --> groupmembermodel
    graphql2 --> controller1
    helper1 --> groupmembermodel
    helper1 --> membermodel
  end
  class GROUP_IDENTITY domain;

  %% GROUP: SAML, SCIM & Group Links
  subgraph SAML_SCIM["SAML, SCIM & Group Federation"]
    direction TB
    samlprov["ee/app/models/saml_provider.rb":::core]
    samlglink["ee/app/models/ldap_group_link.rb":::core]
    scimidentity["ee/app/models/scim_identity.rb":::data]
    gscimident["ee/app/models/group_scim_identity.rb":::data]
    scimoauthtok["ee/app/models/scim_oauth_access_token.rb":::data]
    gscimauth["ee/app/models/group_scim_auth_access_token.rb":::data]
    scimfinder["ee/app/finders/authn/scim_group_finder.rb":::core]
    gscimfinder["ee/app/finders/authn/group_scim_finder.rb":::core]
    gsamlidfinder["ee/app/finders/group_saml_identity_finder.rb":::core]
    authfinder["ee/app/finders/auth/group_saml_identity_finder.rb":::core]
    gsamlgrpworker["ee/app/workers/group_saml_group_sync_worker.rb":::core]
    ldapgrpsync["ee/app/workers/ldap_group_sync_worker.rb":::core]
    ldapallgrpsync["ee/app/workers/ldap_all_groups_sync_worker.rb":::core]
    scimprovfinder["ee/app/finders/authn/enterprise_users_finder.rb":::core]
    scimprovusersfinder["ee/app/finders/auth/provisioned_users_finder.rb":::core]
    samlgrouphelper["ee/app/helpers/saml_group_links_helper.rb":::util]
    samlworkers["ee/app/workers/auth/saml_group_sync_worker.rb":::core]
    samlmssync["ee/app/workers/system_access/saml_microsoft_group_sync_worker.rb":::core]
    grpsamlmssync["ee/app/workers/system_access/group_saml_microsoft_group_sync_worker.rb":::core]
    groupgsync["ee/app/services/groups/sync_service.rb":::core]
    ssoctrl["ee/app/controllers/groups/sso_controller.rb":::core]
    samlprovctrl["ee/app/controllers/groups/saml_providers_controller.rb":::core]
    scimoauthctrl["ee/app/controllers/groups/scim_oauth_controller.rb":::core]
    samlauth["ee/app/controllers/concerns/saml_authorization.rb":::core]
    ldapgrplinkctrl["ee/app/controllers/groups/ldap_group_links_controller.rb":::core]
    ldapctrl["ee/app/controllers/groups/ldaps_controller.rb":::core]
    ldapgrouplinkshlp["ee/app/helpers/ldap_group_links_helper.rb":::util]
    gldaphelper["ee/app/helpers/groups/ldap_sync_helper.rb":::util]
    samlprovhlp["ee/app/helpers/ee/saml_providers_helper.rb":::util]
    ssohlp["ee/app/helpers/groups/sso_helper.rb":::util]
    gsamlbase["ee/app/services/group_saml/saml_provider/base_service.rb":::core]
    gsamlcreatesrv["ee/app/services/group_saml/saml_group_links/create_service.rb":::core]
    gsamlupdatesrv["ee/app/services/group_saml/saml_provider/update_service.rb":::core]
    provresp["ee/lib/ee/gitlab/scim/provisioning_response.rb":::error]
    provisioningsrv["ee/lib/ee/gitlab/scim/group_sync_provisioning_service.rb":::core]
    sglexport["ee/app/workers/members/groups/export_detailed_memberships_worker.rb":::core]
    entity["app/serializers/group_access_token_entity.rb":::data]

    samlprov <--> samlglink
    samlprov --> gsamlbase
    gsamlcreatesrv --> gsamlbase
    gsamlupdatesrv --> gsamlbase
    gsamlbase --> samlprov
    gsamlbase --> samlglink
    samlprovctrl --> samlprov
    samlprovctrl --> samlauth
    gsamlcreatesrv --> samlprovctrl
    gsamlcreatesrv --> samlglink
    gsamlcreatesrv --> gsamlbase
    gsamlcreatesrv --> gsamlupdatesrv
    samlgrouphelper --> samlprov
    samlgrouphelper --> samlglink
    samlgrouphelper --> gscimident
    samlgrouphelper --> gsamlcreatesrv
    samlprovhlp --> samlprov
    samlprovhlp --> gsamlbase
    ssoctrl --> samlprov
    ssoctrl --> gsamlbase
    ssoctrl --> samlauth
    groupgsync --> samlprov
    groupgsync --> gsamlbase
    gsamlbase --> provresp
    provisioningsrv --> provresp
    provisioningsrv --> scimidentity
    provisioningsrv --> gscimident
    provisioningsrv --> scimoauthtok
    provisioningsrv --> gscimauth
    scimfinder --> samlprov
    gscimfinder --> samlprov
    gscimfinder --> gscimident
    gscimfinder --> scimidentity
    sglexport --> samlprov
    sglexport --> samlglink
    scimoauthctrl --> scimoauthtok
    gsamlgrpworker --> samlprov
    gsamlgrpworker --> samlglink
    gsamlgrpworker --> gscimident
    gsamlgrpworker --> gscimauth
    ldapgrpsync --> samlglink
    ldapgrpsync --> ldapallgrpsync
    samlworkers --> samlprov
    samlworkers --> samlglink
    samlworkers --> gscimident
    samlmssync --> samlprov
    samlmssync --> samlglink
    grpsamlmssync --> samlprov
    grpsamlmssync --> samlglink
    ldapgrplinkctrl --> samlglink
    ldapgrplinkctrl --> ldapgrpsync
    ldapctrl --> samlglink
    ldapctrl --> ldapgrpsync
    ldapgrouplinkshlp --> samlglink
    gldaphelper --> ldapgrpsync
    helper1 --> samlprov
    helper2 --> samlprov
    ssohlp --> samlprov
    entity --> samlprov
  end
  class SAML_SCIM domain;

  %% GROUP: Group SAML Domain Logic
  subgraph GROUP_SAML_DOMAIN["Group SAML Domain Logic"]
    direction TB
    gsamlcfg["ee/lib/gitlab/auth/group_saml/config.rb":::util]
    gsamllookup["ee/lib/gitlab/auth/group_saml/group_lookup.rb":::core]
    gsamlidentlink["ee/lib/gitlab/auth/group_saml/identity_linker.rb":::core]
    gsamluser["ee/lib/gitlab/auth/group_saml/user.rb":::core]
    gsamlsessionenforcer["ee/lib/gitlab/auth/group_saml/session_enforcer.rb":::core]
    gsamlssoenforcer["ee/lib/gitlab/auth/group_saml/sso_enforcer.rb":::core]
    gsmemupdater["ee/lib/gitlab/auth/group_saml/membership_updater.rb":::core]
    gsmemenforcer["ee/lib/gitlab/auth/group_saml/membership_enforcer.rb":::core]
    gsduoaddon["ee/lib/gitlab/auth/group_saml/duo_add_on_assignment_updater.rb":::core]
    gsstate["ee/lib/gitlab/auth/group_saml/sso_state.rb":::core]
    gsxmlresp["ee/lib/gitlab/auth/group_saml/xml_response.rb":::util]
    gsresponse["ee/lib/gitlab/auth/group_saml/response_store.rb":::data]
    gmagmenforcer["ee/lib/gitlab/auth/group_saml/gma_membership_enforcer.rb":::core]
    helper4["ee/app/helpers/groups/sso_helper.rb":::util]

    gsamlcfg --> gsamllookup
    gsamllookup --> gsamlidentlink
    gsamlidentlink --> gsamluser
    gsamlidentlink --> gsmemupdater
    gsamluser --> gsmemupdater
    gsamluser --> helper4
    gsamlidentlink --> gsamluser
    gsamlidentlink --> gsamlcfg
    gsamlidentlink --> gsamlssoenforcer
    gsamlidentlink --> gsamlcfg
    gsamlssoenforcer -- uses state --> gsstate
    gsamlssoenforcer --> gsamlsessionenforcer
    gsamlssoenforcer --> gsamluser
    gsamlssoenforcer --> gsmemenforcer
    gsamlssoenforcer --> gsresponse
    gsmemupdater --> gsamluser
    gsmemupdater --> gsamlidentlink
    gsmemupdater --> gsstate
    gsmemenforcer --> gsamluser
    gsmemenforcer --> gsmemupdater
    gsmemenforcer --> gsamlcfg
    gsduoaddon --> gsamluser
    gsxmlresp --> gsamlcfg
    gsxmlresp --> gsresponse
    gmagmenforcer --> gsmemupdater
    gsresponse --> gsamlcfg
    helper4 --> gsamluser
  end
  class GROUP_SAML_DOMAIN domain;

  %% GROUP: GraphQL API Resolvers for Membership
  subgraph GRAPHQL["GraphQL Exposure: Membership, Export, SAML"]
    direction TB
    graphql1
    graphql2
    saveresp1["ee/app/graphql/resolvers/groups/saved_reply_resolver.rb":::core]
    saveresp2["ee/app/graphql/resolvers/groups/saved_replies_resolver.rb":::core]
    resolvesg["ee/app/graphql/resolvers/concerns/ee/resolves_groups.rb":::util]

    graphql1 --> workergdexport
    graphql1 --> sglexport
    graphql2 --> groupmembermodel
    graphql2 --> membermodel
    saveresp1 --> membermodel
    saveresp2 --> membermodel
    resolvesg --> gsamlcfg
    resolvesg --> samlprov
    resolvesg --> gsamlbase
  end
  class GRAPHQL domain;

  %% GROUP: Domain Events, Policies, Misc Core
  subgraph OTHER["Domain Events, Policies & Utils"]
    direction TB
    gdel_evt["app/events/groups/group_deleted_event.rb":::core]
    accessent["app/services/members/groups/creator_service.rb":::core]
    gmroleparser["finders/concerns/members/role_parser.rb":::util]
    groupacc["app/serializers/group_access_token_entity.rb":::data]
    groupacc --> samlprov
    accessent --> membermodel
    accessent --> groupmembermodel
    gdel_evt --> groupmembermodel
    gdel_evt --> samlprov
    gmroleparser --> membermodel
    gmroleparser --> groupmembermodel
  end
  class OTHER domain;

  %% GROUP: Data Structures for Membership SCIM, SAML
  subgraph DATASTRUCTS["Data Structures: SCIM, SAML, Membership"]
    direction TB
    scimidentity
    gscimident
    scimoauthtok
    gscimauth
    gsresponse
    entity
    entity --> samlprov
    entity --> gscimident
    gsresponse --> scimidentity
    gsresponse --> gscimident
  end
  class DATASTRUCTS domain;

  %% Testing/QA Layer light, for relationships, not mechanics
  subgraph QA["Integration & Feature Tests"]
    direction TB
    featmembers["spec/features/groups/members/filter_members_spec.rb":::util]
    featleave["spec/features/groups/members/leave_group_spec.rb":::util]
    qasamlmr["qa/qa/specs/features/ee/browser_ui/10_govern/group/saml_sso_merge_request_approve_spec.rb":::util]
    qasamlssonew["qa/qa/specs/features/ee/browser_ui/10_govern/group/group_saml_enforced_sso_new_account_spec.rb":::util]
    qasamlgit["qa/qa/specs/features/ee/browser_ui/10_govern/group/group_saml_enforced_sso_git_access_spec.rb":::util]
    qasamlssonew --> samlprov
    qasamlmr --> samlprov
    qasamlgit --> samlprov
    featmembers --> controller1
    featleave --> controller1
  end
  class QA domain;

  %% CROSS-GROUP RELATIONSHIPS AND COLLABORATIONS
  gsamlidentlink --"links to SAML Provider/Group"--> samlprov
  gsamluser --"is associated with SAML Provider"--> samlprov
  gsamlcfg --"drives SAML Provider config"--> samlprov
  gsamllookup --"finds group for SAML context"--> samlprov
  gsamlbase --"manages SAML Provider for group"--> samlprov
  gsamlbase --"controls group SAML links"--> samlglink
  provisioningsrv --"provisions SCIM identities"--> gscimident
  provisioningsrv --"provisions SCIM tokens"--> gscimauth
  gscimident --"belongs to group"--> samlprov
  scimidentity --"identity for user & group"--> gscimident
  sglexport --"exports extended membership"--> groupmembermodel
  gmroleparser --"parses role strings into access/data"--> membermodel
  samlprovhlp --"helps with SAML sign-in"--> samlprov
  samlgrouphelper --"helps with SAML group links"--> samlglink
  scimprovusersfinder --"finds SCIM-provisioned users"--> scimidentity
  scimprovfinder --"finds enterprise users"--> membermodel
  ssohlp --"surface SSO utility for group"--> samlprov
  gdel_evt --"fires on group delete, affecting members/SAML"--> samlprov
  gdel_evt --> groupmembermodel

  %% POLICY AND ACCESS ENFORCEMENT
  controller1 --"access/authorization"--> policy
  controller1 --"delegates to creator service"--> accessent
  policy --> membermodel
  policy --> groupmembermodel

  %% WORKERS AND BACKGROUND SYNC
  gsamlgrpworker --"sync group SAML"--> samlprov
  gsamlgrpworker --"sync SAML group links"--> samlglink
  samlworkers --"sync global SAML group links"--> samlglink
  ldapgrpsync --"sync LDAP group links"--> samlglink

  %% GROUP MEMBERSHIP / DATA TRANSFORMATIONS
  mgm1 --"assigns last group owner"--> groupmembermodel
  mgm2 --"assigns accessibility to invited members"--> groupmembermodel
  mgm2 --"fetches private group invited members"--> finder1

  %% FILTERING/QUERIES
  finder1 --"finds groups user is invited to"--> groupmembermodel
  finder2 --"finds groups user has joined"--> groupmembermodel
  finder3 --"finds groups shared with the user"--> groupmembermodel

  %% UTILITIES / DOMAIN INTEGRATION
  helper1 --"serializes group members for API/UI"--> groupmembermodel
  helper2 --"EE extensions for group members helper"--> helper1

  %% GRAPHQL INTEGRATION
  graphql1 --"exposes group member export in API"--> workergdexport
  graphql2 --"resolves pending group members"--> groupmembermodel

  %% TEST COVERAGE - INTEGRATION LAYER
  featmembers --"integration on group membership"--> controller1
  featleave --"integration on member leave"--> controller1

  %% SAML/SCIM DOMAIN ABSTRACTIONS
  gsamlidentlink --"links SAML identity for user"--> gscimident
  gsamlidentlink --"updates membership"--> gsmemupdater
  gsmemupdater --"logs audit event for membership change"--> samlprov
  gsamluser --"per-user SAML-based logic"--> samlprov
  gsamlssoenforcer --"enforces session state"--> gsstate
  gsamlssoenforcer --"enforces SSO for group"--> samlprov
  gsxmlresp --"parses SAML XML responses"--> gsamlcfg
  gsxmlresp --"validates SAML responses"--> samlprov

  %% SCIM/SAML LOGIC AND PROCESSES
  provisioningsrv --"provisions identities/tokens"--> gscimident
  provisioningsrv --"audits provisioning actions"--> provresp
  gscimfinder --"queries by group for SCIM/SAML identities"--> gscimident
  scimfinder --"finds SAML groups for SCIM filter"--> samlprov
  gsamlbase --"serves SAML group links API"--> samlprov
  gsamlcreatesrv --"creates SAML group links"--> gsamlbase

  %% Specialized SAML/SCIM syncing workers
  samlmssync --"sync with Microsoft SAML"--> samlprov
  grpsamlmssync --"sync with Microsoft SAML by group"--> samlprov

  %% Miscellaneous
  rolep --> membermodel
  rolep --> groupmembermodel
  helper2 --> rolep

  %% Domains' Cross References (for clarity)
  GROUP_IDENTITY -.-> SAML_SCIM
  SAML_SCIM -.-> GROUP_SAML_DOMAIN
  SAML_SCIM -.-> GRAPHQL
  GROUP_SAML_DOMAIN -.-> DATASTRUCTS
  GROUP_SAML_DOMAIN -.-> QA
  DATASTRUCTS -.-> GRAPHQL
  DATASTRUCTS -.-> QA
```