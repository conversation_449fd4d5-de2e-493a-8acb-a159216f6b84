```mermaid
flowchart TD
  %% THEMING AND LAYOUT %%
  %% Set layout direction and node colors
  %% VERTICAL LAYOUT
  %% Main GraphQL Resolvers Domain: API/GraphQL API/Resolvers

  %% Core subgraph for GraphQL Resolvers
  subgraph CORE_RESOLVERS["GraphQL API Resolvers"]
    direction TB
    style CORE_RESOLVERS fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rx:15,ry:15

    %% Core abstractions for resolver hierarchy
    BaseResolver["BaseResolver
- Foundation for GraphQL resolver pattern
- Common resolve, authorization, argument logic"
    shape="rounded-rectangle"]
    style BaseResolver fill:#D4F1F9,stroke:#A3D8F5,stroke-width:2,rx:13,ry:13

    LooksAhead["LooksAhead
- Concern: selective data preloading
- Efficient DB query resolution"
    shape="rounded-rectangle"]
    style LooksAhead fill:#FFF8DC,stroke:#FFE8A3,stroke-width:2,rx:13,ry:13

    CachingArrayResolver["CachingArrayResolver
- Reusable concern for batched Array results"
    shape="rounded-rectangle"]
    style CachingArrayResolver fill:#FFF8DC,stroke:#FFE8A3,stroke-width:2,rx:13,ry:13

    %% Common concerns cross-resolver
    ConcernsWorkItemsLookAhead["WorkItems::LookAheadPreloads
- Automatic preloading for work item widgets
- GraphQL performance"
    shape="rounded-rectangle"]
    style ConcernsWorkItemsLookAhead fill:#FFF8DC,stroke:#A3D8F5,stroke-width:2,rx:13,ry:13

    ConcernsMergeRequestsLookAhead["MergeRequests::LookAheadPreloads
- MR-specific preloading
- For high performance MR queries"
    shape="rounded-rectangle"]
    style ConcernsMergeRequestsLookAhead fill:#FFF8DC,stroke:#A3D8F5,stroke-width:2,rx:13,ry:13

    ConcernsProjectsLookAhead["Projects::LookAheadPreloads
- Project preloading strategies"
    shape="rounded-rectangle"]
    style ConcernsProjectsLookAhead fill:#FFF8DC,stroke:#A3D8F5,stroke-width:2,rx:13,ry:13

    ResolvesMergeRequests["ResolvesMergeRequests
- Shared routines for MR resolvers"
    shape="rounded-rectangle"]
    style ResolvesMergeRequests fill:#FFF8DC,stroke:#A3D8F5,stroke-width:2,rx:13,ry:13

    ResolvesGroups["ResolvesGroups
- Shared routines for group resolvers"
    shape="rounded-rectangle"]
    style ResolvesGroups fill:#FFF8DC,stroke:#A3D8F5,stroke-width:2,rx:13,ry:13

    %% Design Management Resolvers
    subgraph DESIGN_MGMT["Design Management"]
      direction TB
      style DESIGN_MGMT fill:#F8F8F8,stroke:#A3D8F5,stroke-width:2,rx:12,ry:12

      DsgnVer["VersionsResolver
- All design versions for a collection"
      shape="rounded-rectangle"]
      style DsgnVer fill:#D4F1F9,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10

      DsgnRslv["DesignResolver
- Find a Design by ID/filename"
      shape="rounded-rectangle"]
      style DsgnRslv fill:#D4F1F9,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10

      DsgnAtVer["DesignAtVersionResolver
- Find Design@version; glue type for GQL"
      shape="rounded-rectangle"]
      style DsgnAtVer fill:#D4F1F9,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10

      DVrnsAtVer["DesignsAtVersionResolver
- All designs at a specific version"
      shape="rounded-rectangle"]
      style DVrnsAtVer fill:#D4F1F9,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10

      DVerRslv["VersionResolver
- Find a specific design version"
      shape="rounded-rectangle"]
      style DVerRslv fill:#D4F1F9,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10
    end

    %% Work Items Resolvers
    subgraph WORK_ITEMS_RESOLVERS["Work Item Resolvers"]
      direction TB
      style WORK_ITEMS_RESOLVERS fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2,rx:13,ry:13

      WkItemsResolver["WorkItemsResolver
- Query/filter work items"
      shape="rounded-rectangle"]
      style WkItemsResolver fill:#D4F1F9,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10

      WkItemResolver["WorkItemResolver
- Find specific work item"
      shape="rounded-rectangle"]
      style WkItemResolver fill:#D4F1F9,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10

      WkLinked["LinkedItemsResolver
- Related work items links/types"
      shape="rounded-rectangle"]
      style WkLinked fill:#D4F1F9,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10

      WkChildren["ChildrenResolver
- Query work item hierarchy"
      shape="rounded-rectangle"]
      style WkChildren fill:#D4F1F9,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10

      WkAncestors["AncestorsResolver
- Ancestor tree for a work item"
      shape="rounded-rectangle"]
      style WkAncestors fill:#D4F1F9,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10

      WkStateCounts["WorkItemStateCountsResolver
- Aggregate work item state counts"
      shape="rounded-rectangle"]
      style WkStateCounts fill:#D4F1F9,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10

      WkTypesResolver["TypesResolver
- Work item types per context"
      shape="rounded-rectangle"]
      style WkTypesResolver fill:#D4F1F9,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10

      WkUserWorkItems["UserWorkItemsResolver
- Work items by user"
      shape="rounded-rectangle"]
      style WkUserWorkItems fill:#D4F1F9,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10
      
      WkDescTemplates["DescriptionTemplatesResolver
- Fetch work item description templates"
      shape="rounded-rectangle"]
      style WkDescTemplates fill:#E6E6FA,stroke:#D4F1F9,stroke-width:2,rx:10,ry:10

      WkDescTemplateContent["DescriptionTemplateContentResolver
- Resolve content of templates"
      shape="rounded-rectangle"]
      style WkDescTemplateContent fill:#E6E6FA,stroke:#D4F1F9,stroke-width:2,rx:10,ry:10

      WkUserPref["UserPreferenceResolver
- Work item user-specific preferences"
      shape="rounded-rectangle"]
      style WkUserPref fill:#FFF8DC,stroke:#D4F1F9,stroke-width:2,rx:10,ry:10

      WkDiscussions["WorkItemDiscussionsResolver
- Discussions for work items"
      shape="rounded-rectangle"]
      style WkDiscussions fill:#D4F1F9,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10

      WkReferences["WorkItemReferencesResolver
- Reference lookup for work items"
      shape="rounded-rectangle"]
      style WkReferences fill:#FFF8DC,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10
    end


    %% Issue and MergeRequest Resolvers
    subgraph ISSUABLES["Issues & Merge Requests"]
      direction TB
      style ISSUABLES fill:#F8F8F8,stroke:#F8F8F8,stroke-width:2,rx:12,ry:12

      IssuesResolver["IssuesResolver
- Main entry for issues GQL API"
      shape="rounded-rectangle"]
      style IssuesResolver fill:#D4F1F9,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10

      IssuesBaseResolver["Issues::BaseResolver
- Issue field/argument logic"
      shape="rounded-rectangle"]
      style IssuesBaseResolver fill:#D4F1F9,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10

      MergeRequestsResolver["MergeRequestsResolver
- Main entry for MRs GQL queries"
      shape="rounded-rectangle"]
      style MergeRequestsResolver fill:#D4F1F9,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10

      UserMergeRequestsResolverBase["UserMergeRequestsResolverBase
- Abstraction for user-based MR queries"
      shape="rounded-rectangle"]
      style UserMergeRequestsResolverBase fill:#D4F1F9,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10

      MRCountResolver["MergeRequestsCountResolver
- Computes MR counts batch loader"
      shape="rounded-rectangle"]
      style MRCountResolver fill:#E6E6FA,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10

      MergedRequestsResolvers["ReviewRequestedMergeRequestsResolver
AssignedMergeRequestsResolver
AuthoredMergeRequestsResolver"
      shape="rounded-rectangle"]
      style MergedRequestsResolvers fill:#E0F8E0,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10
    end

    %% Error Tracking Resolvers
    subgraph ERR_TRACK["Error Tracking / Sentry"]
      direction TB
      style ERR_TRACK fill:#F8F8F8,stroke:#FFE4E1,stroke-width:2,rx:12,ry:12

      SentryStackTrace["ErrorTracking::SentryErrorStackTraceResolver"
      shape="rounded-rectangle"]
      style SentryStackTrace fill:#FFE4E1,stroke:#FFCCC9,stroke-width:2,rx:10,ry:10

      SentryDetailedError["ErrorTracking::SentryDetailedErrorResolver"
      shape="rounded-rectangle"]
      style SentryDetailedError fill:#FFE4E1,stroke:#FFCCC9,stroke-width:2,rx:10,ry:10

      SentryErrorsResolver["ErrorTracking::SentryErrorsResolver"
      shape="rounded-rectangle"]
      style SentryErrorsResolver fill:#FFE4E1,stroke:#FFCCC9,stroke-width:2,rx:10,ry:10
    end

    %% Authorization & Utility concerns
    subgraph UTILS["Supporting/Utility Components"]
      direction TB
      style UTILS fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,rx:12,ry:12

      AuthorizeResource["Gitlab::Graphql::Authorize::AuthorizeResource
- Shared auth concern"
      shape="rounded-rectangle"]
      style AuthorizeResource fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rx:10,ry:10

      FullPathResolver["FullPathResolver
- Resolves models by full_path field"
      shape="rounded-rectangle"]
      style FullPathResolver fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rx:10,ry:10
    end

    %% Board resolvers
    subgraph BOARDS["Boards & Lists"]
      direction TB
      style BOARDS fill:#F8F8F8,stroke:#A3D8F5,stroke-width:2,rx:12,ry:12

      BoardsResolver["BoardsResolver
- Query boards for project or group"
      shape="rounded-rectangle"]
      style BoardsResolver fill:#D4F1F9,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10

      BoardResolver["BoardResolver
- Retrieve single board"
      shape="rounded-rectangle"]
      style BoardResolver fill:#D4F1F9,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10

      BoardListsResolver["BoardListsResolver
- Lists on a board via lookahead"
      shape="rounded-rectangle"]
      style BoardListsResolver fill:#D4F1F9,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10

      BoardListIssuesResolver["BoardListIssuesResolver
- Issues filtered for a list"
      shape="rounded-rectangle"]
      style BoardListIssuesResolver fill:#D4F1F9,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10
    end

    %% Container, Packages, Releases
    subgraph CI_CD_RESOLVERS["CI/CD, Containers & Packages"]
      direction TB
      style CI_CD_RESOLVERS fill:#F8F8F8,stroke:#A3D8F5,stroke-width:2,rx:12,ry:12

      ContainerRepoTagsResolver["ContainerRepositoryTagsResolver
- Tags in container registry"
      shape="rounded-rectangle"]
      style ContainerRepoTagsResolver fill:#E0F8E0,stroke:#78A48A,stroke-width:2,rx:10,ry:10

      PackagePipelinesResolver["PackagePipelinesResolver
- Pipelines for packages"
      shape="rounded-rectangle"]
      style PackagePipelinesResolver fill:#E0F8E0,stroke:#78A48A,stroke-width:2,rx:10,ry:10
    end

    %% Analytics/Cycle Analytics group
    subgraph ANALYTICS["Analytics Resolvers"]
      direction TB
      style ANALYTICS fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2,rx:12,ry:12

      AnlDeploymentCount["Analytics::CycleAnalytics::DeploymentCountResolver
- Deployed counts metric"
      shape="rounded-rectangle"]
      style AnlDeploymentCount fill:#E0F8E0,stroke:#98D9A6,stroke-width:2,rx:10,ry:10

      AnlStagesResolver["Analytics::CycleAnalytics::StagesResolver
- Stages in Value Stream"
      shape="rounded-rectangle"]
      style AnlStagesResolver fill:#E0F8E0,stroke:#98D9A6,stroke-width:2,rx:10,ry:10
    end

    %% GraphQL event triggers
    subgraph TRIGGERS["GraphQL Event Triggers"]
      direction TB
      style TRIGGERS fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,rx:12,ry:12
      GraphqlTriggers["GraphqlTriggers
- Mutation/subscription event triggers
- Pipeline status, MRs, etc"
      shape="rounded-rectangle"]
      style GraphqlTriggers fill:#FFF8DC,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10
    end

    %% User related resolvers
    subgraph USERS["User-based Resolvers"]
      direction TB
      style USERS fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rx:12,ry:12

      UsersResolver["UsersResolver
- Query/filter users"
      shape="rounded-rectangle"]
      style UsersResolver fill:#D4F1F9,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10

      AutocompleteUsersResolver["AutocompleteUsersResolver
- User autocomplete source"
      shape="rounded-rectangle"]
      style AutocompleteUsersResolver fill:#D4F1F9,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10

      GroupCountResolver["Users::GroupCountResolver
- Group number per user"
      shape="rounded-rectangle"]
      style GroupCountResolver fill:#E0F8E0,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10

      RecentlyViewedIssuesResolver["Users::RecentlyViewedIssuesResolver"
       shape="rounded-rectangle"]
      style RecentlyViewedIssuesResolver fill:#D4F1F9,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10

      RecentlyViewedMergeRequestsResolver["Users::RecentlyViewedMergeRequestsResolver"
       shape="rounded-rectangle"]
      style RecentlyViewedMergeRequestsResolver fill:#D4F1F9,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10
    end

    %% Miscellaneous specialized resolvers
    subgraph SPECIALIZED["Specialized/Utility Resolvers"]
      direction TB
      style SPECIALIZED fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,rx:12,ry:12

      BulkLabelsResolver["BulkLabelsResolver
- Batch label lookup by context"
      shape="rounded-rectangle"]
      style BulkLabelsResolver fill:#FFF8DC,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10

      PaginatedTreeResolver["PaginatedTreeResolver
- Tree entry resolution w/ paging"
      shape="rounded-rectangle"]
      style PaginatedTreeResolver fill:#E0F8E0,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10

      BlobsResolver["BlobsResolver
- Retrieve blobs from repo"
      shape="rounded-rectangle"]
      style BlobsResolver fill:#E0F8E0,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10
    end
  end

  %% --- Logical/Conceptual Relationships and Collaborations ---

  %% Foundation and concern relationships
  ConcernsWorkItemsLookAhead --> LooksAhead
  ConcernsMergeRequestsLookAhead --> LooksAhead
  ConcernsProjectsLookAhead --> LooksAhead

  %% Core inheritance/composition
  DsgnRslv --> BaseResolver
  DsgnAtVer --> BaseResolver
  DVerRslv --> BaseResolver
  DsgnVer --> BaseResolver
  DVrnsAtVer --> BaseResolver

  IssuesResolver --> IssuesBaseResolver
  IssuesBaseResolver --> BaseResolver

  MergeRequestsResolver --> BaseResolver
  UserMergeRequestsResolverBase --> MergeRequestsResolver
  MRCountResolver --> BaseResolver

  MergedRequestsResolvers --> UserMergeRequestsResolverBase

  SentryStackTrace --> BaseResolver
  SentryDetailedError --> BaseResolver
  SentryErrorsResolver --> BaseResolver

  %% Work Items Resolvers Structure
  WkItemsResolver --> BaseResolver
  WkItemResolver --> BaseResolver
  WkLinked --> BaseResolver
  WkChildren --> BaseResolver
  WkAncestors --> BaseResolver
  WkStateCounts --> BaseResolver
  WkTypesResolver --> BaseResolver
  WkUserWorkItems --> BaseResolver
  WkDescTemplates --> BaseResolver
  WkDescTemplateContent --> BaseResolver
  WkUserPref --> BaseResolver
  WkDiscussions --> BaseResolver
  WkReferences --> BaseResolver

  %% Boards and Lists Structure
  BoardsResolver --> BaseResolver
  BoardResolver --> BaseResolver
  BoardListsResolver --> BaseResolver
  BoardListIssuesResolver --> BaseResolver

  %% Analytics
  AnlDeploymentCount --> BaseResolver
  AnlStagesResolver --> BaseResolver

  %% Specialized/Utility dependencies
  BulkLabelsResolver --> BaseResolver
  PaginatedTreeResolver --> BaseResolver
  BlobsResolver --> BaseResolver

  %% Utility relationships
  AuthorizeResource --> BaseResolver
  FullPathResolver --> BaseResolver

  %% Container/CI/CD
  ContainerRepoTagsResolver --> BaseResolver
  PackagePipelinesResolver --> BaseResolver

  %% GraphQL Event Triggers
  GraphqlTriggers ---|publishes events|> DsgnVer
  GraphqlTriggers ---|publishes events|> MergeRequestsResolver
  GraphqlTriggers ---|publishes events|> WkItemsResolver

  %% Advanced pattern: LookAhead concern used in multiple resolvers for efficient batch loading
  DsgnVer --> ConcernsWorkItemsLookAhead
  WkItemsResolver --> ConcernsWorkItemsLookAhead
  MergeRequestsResolver --> ConcernsMergeRequestsLookAhead
  BoardsResolver --> ConcernsProjectsLookAhead

  %% User resolver structure
  UsersResolver --> BaseResolver
  AutocompleteUsersResolver --> BaseResolver
  GroupCountResolver --> BaseResolver
  RecentlyViewedIssuesResolver --> BaseResolver
  RecentlyViewedMergeRequestsResolver --> BaseResolver

  %% Collaboration between work item resolvers
  WkUserWorkItems -- provides IDs --> WkItemsResolver
  WkTypesResolver -- type metadata --> WkItemsResolver
  WkDescTemplateContent -- content provider --> WkDescTemplates

  %% Issues/MRs <--> Work Item Collaboration
  IssuesResolver -- relates to --> WkItemsResolver
  MergeRequestsResolver -- linked to --> WkItemsResolver

  %% Boards use lists, lists resolve issues/work items
  BoardsResolver -- has --> BoardListsResolver
  BoardListsResolver -- has --> BoardListIssuesResolver
  BoardListIssuesResolver -- filters with --> IssuesResolver

  %% Analytics uses work item, user, and pipeline data
  AnlDeploymentCount -- aggregates from --> WkItemsResolver
  AnlStagesResolver -- sequence from --> MergeRequestsResolver

  %% Error tracking: Sentry error batch relationships
  SentryDetailedError -- fetches --> SentryStackTrace
  SentryErrorsResolver -- links to --> SentryDetailedError

  %% BlobsResolver and repo trees
  PaginatedTreeResolver -- lists entries --> BlobsResolver

  %% Containerfinder for packages/CI/CD
  PackagePipelinesResolver -- triggers --> ContainerRepoTagsResolver

  %% foundational concern sharing
  IssuesBaseResolver --- ResolvesMergeRequests
  BoardsResolver --- ResolvesGroups
  GroupCountResolver --- ResolvesGroups

  %% AuthorizeResource used throughout for authz
  DsgnRslv --- AuthorizeResource
  MergeRequestsResolver --- AuthorizeResource
  WkItemsResolver --- AuthorizeResource
  BoardsResolver --- AuthorizeResource
  BlobsResolver --- AuthorizeResource
  SentryStackTrace --- AuthorizeResource

  %% Data Types group as green pastel nodes
  subgraph DATA_TYPES["Domain-Specific Data Structures"]
    direction TB
    style DATA_TYPES fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2,rx:12,ry:12

    TypesDesign["Types::DesignManagement::DesignType etc.
- GraphQL types for design data"
    shape="rounded-rectangle"]
    style TypesDesign fill:#E0F8E0,stroke:#98D9A6,stroke-width:2,rx:10,ry:10

    TypesWorkItem["Types::WorkItemType
Types::WorkItems::UserPreference etc."
    shape="rounded-rectangle"]
    style TypesWorkItem fill:#E0F8E0,stroke:#98D9A6,stroke-width:2,rx:10,ry:10

    TypesMR["Types::MergeRequestType and related"
    shape="rounded-rectangle"]
    style TypesMR fill:#E0F8E0,stroke:#98D9A6,stroke-width:2,rx:10,ry:10

    TypesUser["Types::UserType, Types::GroupType, etc."
    shape="rounded-rectangle"]
    style TypesUser fill:#E0F8E0,stroke:#98D9A6,stroke-width:2,rx:10,ry:10
  end

  %% Data type transformations and use
  DsgnVer -- "returns" --> TypesDesign
  DsgnRslv -- "returns" --> TypesDesign
  DVrnsAtVer -- "returns" --> TypesDesign
  DVerRslv -- "returns" --> TypesDesign
  DsgnAtVer -- "returns" --> TypesDesign

  WkItemsResolver -- "returns" --> TypesWorkItem
  WkItemResolver -- "returns" --> TypesWorkItem
  WkLinked -- "returns" --> TypesWorkItem
  WkChildren -- "returns" --> TypesWorkItem
  WkAncestors -- "returns" --> TypesWorkItem
  WkTypesResolver -- "returns" --> TypesWorkItem
  WkStateCounts -- "returns" --> TypesWorkItem
  WkDescTemplates -- "returns" --> TypesWorkItem
  WkDescTemplateContent -- "returns" --> TypesWorkItem

  MergeRequestsResolver -- "returns" --> TypesMR
  UserMergeRequestsResolverBase -- "returns" --> TypesMR
  MRCountResolver -- "returns" --> TypesMR

  UsersResolver -- "returns" --> TypesUser
  AutocompleteUsersResolver -- "returns" --> TypesUser
  GroupCountResolver -- "returns" --> TypesUser

  %% Utility to types
  BoardListsResolver -- "returns" --> TypesWorkItem
  BlobsResolver -- "returns" --> TypesDesign

  %% Error tracking returns error types
  SentryErrorsResolver -- "returns" --> TypesDesign

  %% Connections to setup/initialization purple pastel
  subgraph SETUP["Initialization/Setup"]
    direction TB
    style SETUP fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2,rx:12,ry:12

    WkDescTemplates
    WkDescTemplateContent
    MRCountResolver
  end

  %% Connections: patterns and abstractions supporting the domain
  subgraph PATTERNS["Key Abstractions & Patterns"]
    direction TB
    style PATTERNS fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rx:12,ry:12

    PATTERN_LOOKAHEAD["LookAhead Pattern
- Efficient N+1 management"
    shape="rounded-rectangle"]
    style PATTERN_LOOKAHEAD fill:#D4F1F9,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10

    PATTERN_BATCHLOADER["BatchLoader Pattern
- Batched/async DB resolving"
    shape="rounded-rectangle"]
    style PATTERN_BATCHLOADER fill:#D4F1F9,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10

    PATTERN_AUTHORIZE["Authorization concern chain
- Fine-grained GraphQL security"
    shape="rounded-rectangle"]
    style PATTERN_AUTHORIZE fill:#D4F1F9,stroke:#A3D8F5,stroke-width:2,rx:10,ry:10

    BaseResolver -- "implements" --> PATTERN_LOOKAHEAD
    BaseResolver -- "includes" --> PATTERN_AUTHORIZE
    IssuesResolver -- "uses" --> PATTERN_BATCHLOADER
    ConcernsWorkItemsLookAhead -- "applies to" --> PATTERN_LOOKAHEAD
    ConcernsMergeRequestsLookAhead -- "applies to" --> PATTERN_LOOKAHEAD
  end
```