```mermaid
flowchart TD
    %% GLOBAL STYLES
    %% Core Domain Files: pastel blue (#D4F1F9)
    %% Supporting/Utility Files: pastel yellow (#FFF8DC)
    %% Data Structure Files: pastel green (#E0F8E0)
    %% Error Handling Files: pastel red (#FFE4E1)
    %% Initialization/Setup Files: pastel purple (#E6E6FA)
    %% Logical Groupings/Subgraphs: very light gray (#F8F8F8), pastel borders
    %% All nodes are rounded rectangles unless otherwise needed

    %% GROUPING: GROUP AND NAMESPACE DATA STRUCTURES AND HIERARCHY
    subgraph L1["Group & Namespace Data Structures" ]
      direction TB
      style L1 fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,corner-radius:12px

      N1["app/models/namespaces/traversal/recursive_scopes.rb
      RecursiveScopes for nested group traversal and hierarchy queries"]
      style N1 fill:#E0F8E0,stroke:#36A8D6,stroke-width:1px,stroke-dasharray:3 2,corner-radius:8px

      N2["app/models/concerns/group_descendant.rb
      Encapsulates logic for group hierarchy & descendants"]
      style N2 fill:#E0F8E0,stroke:#36A8D6,stroke-width:1px,corner-radius:8px

      N3["app/models/group_deletion_schedule.rb
      Schedules and manages group deletion requests"]
      style N3 fill:#E0F8E0,stroke:#36A8D6,stroke-width:1px,corner-radius:8px

      N4["ee/app/models/ee/group.rb
      EE extensions: advanced attributes, hierarchy logic, token auth."]
      style N4 fill:#D4F1F9,stroke:#5ACEDE,stroke-width:1px,corner-radius:8px
    end

    %% GROUPING: FINDERS - GROUP RETRIEVAL, FILTERING & SEARCH
    subgraph L2["Finders: Group Query, Search, and Filtering"]
      direction TB
      style L2 fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,corner-radius:12px

      F1["app/finders/group_finder.rb
      Direct lookup & retrieve group by attributes"]
      style F1 fill:#D4F1F9,stroke:#68BCD6,stroke-width:1px,corner-radius:8px

      F2["app/finders/groups_finder.rb
      Flexible filtering: authorized, all, custom attributes"]
      style F2 fill:#D4F1F9,stroke:#68BCD6,stroke-width:1px,corner-radius:8px

      F2ee["ee/app/finders/ee/groups_finder.rb
        EE logic: SAML filtering, repository storage"]
      style F2ee fill:#D4F1F9,stroke:#68BCD6,stroke-width:1px,stroke-dasharray:5 2,corner-radius:8px

      F3["app/finders/group_descendants_finder.rb
      Finds all subgroups/projects within a group"]  
      style F3 fill:#D4F1F9,stroke:#68BCD6,stroke-width:1px,corner-radius:8px

      F4["app/finders/groups/user_groups_finder.rb
      Finds groups for given user, with permission & search filtering"]
      style F4 fill:#D4F1F9,stroke:#68BCD6,stroke-width:1px,corner-radius:8px

      F5["app/finders/groups/accepting_project_transfers_finder.rb
      Finds groups accepting project transfers"]
      style F5 fill:#D4F1F9,stroke:#68BCD6,stroke-width:1px,corner-radius:8px

      F6["app/finders/concerns/namespaces/groups_filter.rb
      Shared filtering: by_id, search, sorting"]
      style F6 fill:#FFF8DC,stroke:#B9B06F,stroke-width:1px,corner-radius:8px

      F7["app/finders/autocomplete/group_finder.rb
      Autocomplete: for UI/group selector"]
      style F7 fill:#FFF8DC,stroke:#B9B06F,stroke-width:1px,corner-radius:8px

      F8["app/finders/autocomplete/routes_finder.rb
      Autocomplete: route search by path for namespace/group selection"]
      style F8 fill:#FFF8DC,stroke:#B9B06F,stroke-width:1px,corner-radius:8px

      F9["ee/app/finders/users/contributed_groups_finder.rb
      Finds groups users contributed to EE"]
      style F9 fill:#FFF8DC,stroke:#B9B06F,stroke-width:1px,stroke-dasharray:5 2,corner-radius:8px

      F10["ee/app/finders/autocomplete/group_subgroups_finder.rb
      Autocomplete: subgroup search EE"]
      style F10 fill:#FFF8DC,stroke:#B9B06F,stroke-width:1px,stroke-dasharray:5 2,corner-radius:8px

      F11["ee/app/finders/groups_with_templates_finder.rb
      Finds groups usable as templates EE"]
      style F11 fill:#FFF8DC,stroke:#B9B06F,stroke-width:1px,stroke-dasharray:5 2,corner-radius:8px
    end

    %% GROUPING: CONTROLLERS (GROUP MANAGEMENT & HIERARCHY)
    subgraph L3["Controllers: Group Management and Hierarchies"]
      direction TB
      style L3 fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,corner-radius:12px

      G1["app/controllers/groups_controller.rb
      Main group CRUD actions creation, show, destroy, etc."]
      style G1 fill:#D4F1F9,stroke:#5ACEDE,stroke-width:1px,corner-radius:8px

      G2["app/controllers/groups/children_controller.rb
      Lists children of group for hierarchy navigation"]
      style G2 fill:#D4F1F9,stroke:#5ACEDE,stroke-width:1px,corner-radius:8px

      G3["app/controllers/groups/imports_controller.rb
      Group import workflow"]
      style G3 fill:#D4F1F9,stroke:#5ACEDE,stroke-width:1px,corner-radius:8px

      G4["app/controllers/groups/crm/organizations_controller.rb
      Organization management within groups"]
      style G4 fill:#D4F1F9,stroke:#5ACEDE,stroke-width:1px,corner-radius:8px

      G5ee["ee/app/controllers/ee/groups/runners_controller.rb
        EE extension: group runners management"]
      style G5ee fill:#D4F1F9,stroke:#5ACEDE,stroke-width:1px,stroke-dasharray:5 2,corner-radius:8px

      G6ee["ee/app/controllers/groups/virtual_registries_controller.rb
        EE extension: group virtual registries UI"]
      style G6ee fill:#D4F1F9,stroke:#5ACEDE,stroke-width:1px,stroke-dasharray:5 2,corner-radius:8px

      G7ee["ee/app/controllers/groups/virtual_registries/maven/registries_controller.rb
        EE extension: Maven virtual registries in groups"]
      style G7ee fill:#D4F1F9,stroke:#5ACEDE,stroke-width:1px,stroke-dasharray:5 2,corner-radius:8px

      G8profile["app/controllers/profiles/groups_controller.rb
      Profile view/controller for user's groups"]
      style G8profile fill:#D4F1F9,stroke:#5ACEDE,stroke-width:1px,corner-radius:8px

      G9reg["ee/app/controllers/registrations/groups_controller.rb
      EE: Group creation/registration with onboarding/analytics"]
      style G9reg fill:#D4F1F9,stroke:#5ACEDE,stroke-width:1px,stroke-dasharray:5 2,corner-radius:8px
    end

    %% GROUPING: CONTROLLER CONCERNS / UTILITY MODULES
    subgraph L4["Controller Concerns & Utilities"]
      direction TB
      style L4 fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2px,corner-radius:12px

      C1["app/controllers/concerns/groups/params.rb
      Strong params for group creation/update"]
      style C1 fill:#FFF8DC,stroke:#B9B06F,stroke-width:1px,corner-radius:8px

      C2["app/controllers/concerns/group_tree.rb
      Rendering and filtering of group hierarchies in controllers"]
      style C2 fill:#FFF8DC,stroke:#B9B06F,stroke-width:1px,corner-radius:8px
    end

    %% GROUPING: SERVICES - GROUP CREATION, TRANSFER, NESTED CREATE/HIERARCHY
    subgraph L5["Group Services: Creation, Nested Creation, Transfer, Import/Export"]
      direction TB
      style L5 fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,corner-radius:12px

      S1["app/services/groups/create_service.rb
      Group creation/validation, params filtering, after-create hooks"]
      style S1 fill:#D4F1F9,stroke:#5ACEDE,stroke-width:1px,corner-radius:8px

      S1ee["ee/app/services/ee/groups/create_service.rb
      EE: Audit, custom validation, after-build hooks"]
      style S1ee fill:#D4F1F9,stroke:#5ACEDE,stroke-width:1px,stroke-dasharray:5 2,corner-radius:8px

      S2["app/services/groups/nested_create_service.rb
      Nested group creation & path/visibility propagation"]
      style S2 fill:#D4F1F9,stroke:#5ACEDE,stroke-width:1px,corner-radius:8px

      S3["app/services/groups/transfer_service.rb
      Handles group transfer between namespaces including validation"]
      style S3 fill:#D4F1F9,stroke:#5ACEDE,stroke-width:1px,corner-radius:8px

      S3ee["ee/app/services/ee/groups/transfer_service.rb
      EE: extended group transfer SAML, wikis, associations"]
      style S3ee fill:#D4F1F9,stroke:#5ACEDE,stroke-width:1px,stroke-dasharray:5 2,corner-radius:8px

      S4["app/services/groups/import_export/import_service.rb
      Imports group tree, structure, permissions from import"]
      style S4 fill:#D4F1F9,stroke:#5ACEDE,stroke-width:1px,corner-radius:8px

      S5["app/services/customer_relations/group_migration_service.rb
      Moves customer relations/org data with group transfers"]
      style S5 fill:#D4F1F9,stroke:#5ACEDE,stroke-width:1px,corner-radius:8px

      S6["app/services/routes/rename_descendants_service.rb
      Renames group route descendants post-transfer/rename"]
      style S6 fill:#FFF8DC,stroke:#B9B06F,stroke-width:1px,corner-radius:8px

      S7["ee/app/services/registrations/base_namespace_create_service.rb
      EE: base for group/namespace creation with experiments"]
      style S7 fill:#E6E6FA,stroke:#A48ED6,stroke-width:1px,stroke-dasharray:5 2,corner-radius:8px

      S8["ee/app/services/registrations/standard_namespace_create_service.rb
      EE: Standard group/namespace creation with project"]
      style S8 fill:#E6E6FA,stroke:#A48ED6,stroke-width:1px,stroke-dasharray:5 2,corner-radius:8px

      S9["ee/app/services/registrations/import_namespace_create_service.rb
      EE: Import flow for group/namespace creation"]
      style S9 fill:#E6E6FA,stroke:#A48ED6,stroke-width:1px,stroke-dasharray:5 2,corner-radius:8px
    end

    %% GROUPING: GRAPHQL/RESOLVERS
    subgraph L6["GraphQL: Resolvers, Types, Schema"]
      direction TB
      style L6 fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,corner-radius:12px

      Q1["app/graphql/resolvers/groups_resolver.rb
      GraphQL: group resolution, search, auth logic"]
      style Q1 fill:#D4F1F9,stroke:#5ACEDE,stroke-width:1px,corner-radius:8px

      Q2ee["ee/app/graphql/ee/types/group_type.rb
      EE: GraphQL GroupType fields/logic epics, provisioning, etc."]
      style Q2ee fill:#D4F1F9,stroke:#5ACEDE,stroke-width:1px,stroke-dasharray:5 2,corner-radius:8px
    end

    %% GROUPING: EVENTS (GROUP PATH/TRANSFER)
    subgraph L7["Domain Events: Group Path & Transfer"]
      direction TB
      style L7 fill:#F8F8F8,stroke:#FFE4E1,stroke-width:2px,corner-radius:12px

      E1["app/events/groups/group_path_changed_event.rb
      Event: Group path changes for sync, notify, auditing"]
      style E1 fill:#FFE4E1,stroke:#DE678A,stroke-width:1px,corner-radius:8px

      E2["app/events/groups/group_transfered_event.rb
      Event: Group transfer completed"]
      style E2 fill:#FFE4E1,stroke:#DE678A,stroke-width:1px,corner-radius:8px
    end

    %% GROUPING: API HELPERS & INTERFACE
    subgraph L8["API Help/Import: REST & Grape Interface"]
      direction TB
      style L8 fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2px,corner-radius:12px

      A1["lib/api/group_import.rb
      REST/Grape: Group import endpoint"]
      style A1 fill:#E0F8E0,stroke:#69BE8E,stroke-width:1px,corner-radius:8px

      A2["lib/api/helpers/groups_helpers.rb
      Shared param and field validation for API"]
      style A2 fill:#FFF8DC,stroke:#B9B06F,stroke-width:1px,corner-radius:8px

      A3["lib/gitlab/group_search_results.rb
      Aggregates group search results for API/UI"]
      style A3 fill:#D4F1F9,stroke:#4DCFC0,stroke-width:1px,corner-radius:8px
    end

    %% GROUPING: BULK IMPORTS PIPELINES ETC
    subgraph L9["Bulk Imports: Group Pipelines & Data Flows"]
      direction TB
      style L9 fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2px,corner-radius:12px

      B1["lib/bulk_imports/groups/pipelines/group_pipeline.rb
      Bulk import: main group pipeline, loads group data"]
      style B1 fill:#E0F8E0,stroke:#69BE8E,stroke-width:1px,corner-radius:8px

      B2["ee/lib/bulk_imports/groups/pipelines/epics_pipeline.rb
      EE: pipeline for importing group epics"]
      style B2 fill:#E0F8E0,stroke:#69BE8E,stroke-width:1px,stroke-dasharray:5 2,corner-radius:8px

      B3["ee/lib/bulk_imports/groups/pipelines/iterations_pipeline.rb
      EE: import group iterations"]
      style B3 fill:#E0F8E0,stroke:#69BE8E,stroke-width:1px,stroke-dasharray:5 2,corner-radius:8px

      B4["ee/lib/bulk_imports/groups/pipelines/iterations_cadences_pipeline.rb
      EE: import iterations cadences"]
      style B4 fill:#E0F8E0,stroke:#69BE8E,stroke-width:1px,stroke-dasharray:5 2,corner-radius:8px
    end

    %% GROUPING: SERIALIZERS & API ENTITIES EE Exposures
    subgraph L10["Serializers & API Entities"]
      direction TB
      style L10 fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2px,corner-radius:12px

      Srz1["ee/app/serializers/ee/group_child_entity.rb
      EE: Exposes compliance for group children"]
      style Srz1 fill:#FFF8DC,stroke:#B9B06F,stroke-width:1px,stroke-dasharray:5 2,corner-radius:8px

      Srz2["ee/lib/ee/api/entities/group.rb
      EE: API entity, adds LDAP, group_links"]
      style Srz2 fill:#FFF8DC,stroke:#B9B06F,stroke-width:1px,stroke-dasharray:5 2,corner-radius:8px

      Srz3["ee/lib/ee/api/entities/group_detail.rb
      EE: API entity, exposes advanced settings"]
      style Srz3 fill:#FFF8DC,stroke:#B9B06F,stroke-width:1px,stroke-dasharray:5 2,corner-radius:8px
    end

    %% RELATIONSHIPS: DATA & LOGIC FLOW

    %% DATA STRUCTURES <-> FINDERS
    N1 -->|Hierarchy queries| F2
    N1 -->|Hierarchy queries| F3
    N1 -->|Hierarchy filters| F4

    N2 -->|Hierarchical expansion| F3

    N4 -->|Extended group model features| F2ee
    N4 -->|EE hierarchy features| F3
    N4 -->|Auth/storage features| F2ee
    N4 -->|Inheritance| N2

    N3 -->|Deletion scheduling for groups| S3

    %% FINDERS <-> CONTROLLERS
    F1 -->|Find group| G1
    F2 -->|Flexible filters| G1
    F2ee -->|EE filters| G1
    F4 -->|User groups| G1
    F4 -->|User's group filters| G8profile
    F3 --> |Subgroup/project lookup| G2
    F5 -->|Project transfer eligibility| S3
    F6 -->|Filter logic shared| F2
    F6 -->|Filter logic shared| F4
    F7 -->|Autocomplete group| G1
    F8 -->|Autocomplete routes| G1
    F9 -->|User contributed groups| F2ee
    F10 -->|Autocomplete subgroups| F7
    F11 -->|Find template groups| S1

    %% CONTROLLER CONCERNS <-> CONTROLLERS
    C1 -->|Strong param helper| G1
    C1 -->|Strong param helper| G2
    C1 -->|Strong param helper| G3
    C1 -->|Strong param helper| G8profile
    C1 -->|Params support| S1
    C2 -->|Hierarchy tree rendering| G1
    C2 -->|Hierarchy tree handling| G2

    %% SERVICES <-> CONTROLLERS
    S1 -->|Creation backing| G1
    S1ee -->|EE audit/logic| S1
    S1 -->|Used for nested create| S2
    S2 -->|Nested creation| G1
    S2 -->|Path data propagates| N1
    S1ee -->|After create| E1

    S3 -->|Transfer service| G1
    S3ee -->|EE transfer logic| S3
    S3ee -->|Advanced transfer| E2
    S3 ee -->|Hooks and sync| S5

    S4 -->|Handles import input| G3

    S5 -->|Migrates org data on group move| S3ee

    S6 -->|Renames descendants| S3

    S7 -->|EE creation base|S8
    S7 -->|EE import base|S9
    S8 -->|Standard group creation|G9reg
    S9 -->|Import create flow|G9reg
    S9 -->|Delegates to|S1

    %% EVENTS
    E1 -->|Notify on group path update|G1
    E2 -->|Notify on group transfer|S3

    %% GRAPHQL
    Q1 -->|Data resolve|F2
    Q1 -->|Resolve to GroupType|Q2ee
    Q2ee -->|Expose EE fields|N4

    %% API
    A1 -->|Import endpoint|S4
    A1 -->|Uses params from|A2
    A2 -->|Params used by|S1

    %% BULK IMPORTS
    B1 -->|Creates groups from bulk import|S1
    B2 -->|Imports epics (EE)|N4
    B3 -->|Imports iterations (EE)|N4
    B4 -->|Cadence import|B3

    %% SERIALIZERS
    Srz1 -->|Expose child info|N4
    Srz2 -->|EE group entity|N4
    Srz3 -->|EE group detail|N4

    %% SEARCH UTILITIES
    A3 -->|Aggregates search|F2

    %% CONNECTED EDGES FOR GROUP DATA FLOW
    F2 -->|Group filtering|N1
    F2ee -->|EE filtering|N4
    F3 -->|Finds descendants|N2

    S3 -->|Transfer can cause descendant rename|S6

    S1 -->|Uses params|C1
    G1 -->|Show/hierarchy tree|C2
    G2 -->|Child group listing|F3

    S1ee -->|Audit events|E1
    S3ee -->|Transfer event|E2

    G9reg -->|Uses creation services|S8
    G9reg -->|Uses import services|S9
    S9 -->|Uses create_service|S1

    %% EE FINDERS EXTEND BASE BEHAVIORS
    F2ee -->|Overrides and extends|F2
    F2ee -->|Uses EE::Group|N4

    %% NEW CONNECTIONS FOR DOMAIN SPECIFIC SCENARIOS
    S3 -->|After transfer triggers customer relation migration|S5

    B1 -->|Triggers pipelines for group-wide data|B2
    B1 -->|Triggers pipelines for group-wide data|B3
    B3 -->|Uses iterations cadence|B4

    %% STYLED CONNECTIONS
    linkStyle default stroke-width:1px,stroke:#82B5E2

    %% Add extra spacing in vertical layout
    L2 --> L3
    L3 --> L5
    L5 --> L6
    L6 --> L7
    L7 --> L8
    L8 --> L9
    L9 --> L10
```