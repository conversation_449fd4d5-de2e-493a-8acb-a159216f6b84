```mermaid
flowchart TD
%% Color scheme definitions
classDef core fill:#D4F1F9,stroke:#80BFD6,stroke-width:2px,color:#222,rx:10,ry:10
classDef support fill:#FFF8DC,stroke:#F7E7B3,stroke-width:2px,color:#222,rx:10,ry:10
classDef datastruct fill:#E0F8E0,stroke:#B8EAC6,stroke-width:2px,color:#222,rx:10,ry:10
classDef error fill:#FFE4E1,stroke:#F4BEBE,stroke-width:2px,color:#222,rx:10,ry:10
classDef init fill:#E6E6FA,stroke:#ADADD6,stroke-width:2px,color:#222,rx:10,ry:10
classDef group fill:#F8F8F8,stroke:#B5CFFD,stroke-width:2px

%% ===========================================================================
%% GROUP: SHARED EXAMPLES, CONTEXTS AND HELPERS
subgraph SHARED EXAMPLES, CONTEXTS & HELPERS ["SHARED EXAMPLES, CONTEXTS & HELPERS"]
direction TB
style SHARED EXAMPLES, CONTEXTS & HELPERS fill:#F8F8F8,stroke:#B5CFFD,stroke-width:2px

SE_AccessControl[access_control_shared_examples.rb<br/>Protected tags access control shared examples]
SE_Analytics[analytics_shared_context.rb<br/>Analytics fixtures shared context]
SE_Secrets[secrets_check_shared_contexts.rb<br/>Secrets checking shared contexts]
SE_RemoteDev[remote_development_shared_contexts.rb<br/>Remote development environment shared contexts]
SE_SecPolicies[policies_shared_contexts.rb<br/>Security orchestration policies shared context]
SE_Presenters[nuget_shared_context.rb<br/>NuGet presenters shared context]
SE_FeatureFlags[stub_feature_flags_spec.rb<br/>Stub feature flags for specs]
SE_MemberAccess[stub_member_access_level_spec.rb<br/>Stub member access levels for testing]
SE_BulkImpMemberAttrs[member_attributes_transformer_spec.rb<br/>Bulk import member attributes shared examples]
SE_WorkItemEpics[work_item_epics_shared_context.rb<br/>Work item epics migration shared context]
SE_WebIDE[web_ide_shared_context.rb<br/>Web IDE test preparation context]
SE_PackagesRegistry[packages_registry_shared_context.rb<br/>Packages registry common setup]
SE_GitlabProjMigration[gitlab_project_migration_common.rb<br/>GitLab Project migration shared helpers]

class SE_AccessControl core
class SE_Analytics core
class SE_Secrets support
class SE_RemoteDev support
class SE_SecPolicies core
class SE_Presenters core
class SE_FeatureFlags support
class SE_MemberAccess support
class SE_BulkImpMemberAttrs support
class SE_WorkItemEpics core
class SE_WebIDE support
class SE_PackagesRegistry support
class SE_GitlabProjMigration support
end

%% ===========================================================================
%% GROUP: MATCHERS AND SPECIFIC MATCHING UTILS
subgraph MATCHERS & VALIDATORS ["MATCHERS & VALIDATORS"]
direction TB
style MATCHERS & VALIDATORS fill:#F8F8F8,stroke:#88C9AE,stroke-width:2px

M_GraphQL[graphql_matchers.rb<br/>Validate GraphQL authorizations & types]
M_EventStore[event_store.rb<br/>Event store matcher]
M_BeSorted[be_sorted.rb<br/>Sort order matcher]
M_ExceedQueryLimit[exceed_query_limit.rb<br/>SQL query limit matcher]
M_BeNPlus1[be_n_plus_1_query.rb<br/>Detect N+1 SQL queries]
M_BeValidJSON[be_valid_json.rb<br/>Validate JSON responses]
M_Result[result_matchers.rb<br/>Result type matchers ok, err]
M_PushFrontendFlags[pushed_frontend_feature_flags_matcher.rb<br/>Frontend feature flags matcher]
M_PushLicensedFeatures[pushed_licensed_features_matcher.rb<br/>Licensed features being pushed matcher]
M_HaveText[have_plain_text_content.rb<br/>Plain text email content matcher]
M_SidekiqJSON[sidekiq_json_matcher.rb<br/>Sidekiq job arguments JSON validation]
M_DBEnums[db_schema_matchers.rb<br/>Match DB enum column type]
M_AccessGeneric[access_matchers_generic.rb<br/>Access control matcher generic]
M_Access[access_matchers.rb<br/>Access matcher for users]
M_AccessForController[access_matchers_for_controller.rb<br/>Access matcher for controller]
M_AccessForRequest[access_matchers_for_request.rb<br/>Access matcher for requests]
M_InternalEventsCLI[internal_events_cli_matchers.rb<br/>Internal events CLI output matcher]
M_TokenAuth[token_authenticatable_matchers.rb<br/>Token authenticatable matcher]
M_Markdown[markdown_matchers.rb<br/>Markdown matchers autolinks etc]
M_Query[query_matcher.rb<br/>SQL query pattern matcher]
M_MatchIDs[match_ids.rb<br/>Element ID matcher]
M_ReqUrgency[request_urgency_matcher.rb<br/>Request urgency matcher]
M_InternalEvents[internal_events_matchers.rb<br/>Internal internal-events/metrics matchers]
M_BackgroundMig[background_migrations_matchers.rb<br/>Scheduled background migration matcher]
M_LockRows[lock_rows.rb<br/>Row locking matcher]
M_ExceedRedisCall[exceed_redis_call_limit.rb<br/>Redis calls limit matcher]
M_InvokeRopSteps[invoke_rop_steps.rb<br/>ROP step validation]
end

%% ===========================================================================
%% EE: ENTERPRISE SPECIFIC MATCHERS
subgraph EE SPECIFIC MATCHERS ["EE SPECIFIC MATCHERS"]
direction TB
style EE SPECIFIC MATCHERS fill:#F8F8F8,stroke:#7CCAF3,stroke-width:2px

EE_MD_Matchers[ee/markdown_matchers.rb<br/>EE Markdown specific matchers]
EE_EpicAggregate[epic_aggregate_matchers.rb<br/>Epic nodes aggregate matcher]
EE_Snowplow[snowplow/tracking_event_verifier.rb<br/>Snowplow event verifiers for EE]
end

class EE_MD_Matchers core
class EE_EpicAggregate core
class EE_Snowplow core

%% ===========================================================================
%% QA: QA Factories & Matchers for Integration/E2E
subgraph QA FACTORIES & MATCHERS ["QA FACTORIES & MATCHERS"]
direction TB
style QA FACTORIES & MATCHERS fill:#F8F8F8,stroke:#D4B5FD,stroke-width:2px

QA_Factories_Badges[badges.rb<br/>QA badge factories]
QA_Factories_Agents[agents.rb<br/>QA agent factories]
QA_Factories_AccessTokens[access_tokens.rb<br/>QA access token factories]
QA_Factories_CiVars[ci_variables.rb<br/>QA ci variable factories]
QA_Factories_Commits[commits.rb<br/>QA commit factories]
QA_Factories_DeployTokens[deploy_tokens.rb<br/>QA deploy token factories]
QA_Factories_Designs[designs.rb<br/>QA design factories]
QA_Factories_Files[files.rb<br/>QA file factories]
QA_Factories_Groups[groups.rb<br/>QA group factories]
QA_Factories_Issues[issues.rb<br/>QA issue factories]
QA_Factories_Jobs[jobs.rb<br/>QA job factories]
QA_Factories_Labels[labels.rb<br/>QA label factories]
QA_Factories_Milestones[milestones.rb<br/>QA milestone factories]
QA_Factories_MRs[merge_requests.rb<br/>QA merge request factories]
QA_Factories_Notes[.rb<br/>QA note factories]
QA_Factories_Packages[packages.rb<br/>QA package factories]
QA_Factories_Pipelines[pipelines.rb<br/>QA pipeline factories]
*********************[projects.rb<br/>QA project factories]
QA_Factories_Runners[runners.rb<br/>QA runner factories]
QA_Factories_Snippets[snippets.rb<br/>QA snippet factories]
QA_Factories_Shared[_shared.rb<br/>QA shared factory utilities]

QA_Matchers_HaveQA[have_matcher.rb<br/>QA page element presence matcher]
QA_Matchers_Eventually[eventually_matcher.rb<br/>Ensures condition met eventually]
QA_Matchers_HaveText[have_text.rb<br/>QA page content matcher]
QA_SupportData_Image[image.rb<br/>Data helpers for images in QA specs]
end

class QA_Factories_Badges support
class QA_Factories_Agents support
class QA_Factories_AccessTokens support
class QA_Factories_CiVars support
class QA_Factories_Commits support
class QA_Factories_DeployTokens support
class QA_Factories_Designs support
class QA_Factories_Files support
class QA_Factories_Groups support
class QA_Factories_Issues support
class QA_Factories_Jobs support
class QA_Factories_Labels support
class QA_Factories_Milestones support
class QA_Factories_MRs support
class QA_Factories_Notes support
class QA_Factories_Packages support
class QA_Factories_Pipelines support
class ********************* support
class QA_Factories_Runners support
class QA_Factories_Snippets support
class QA_Factories_Shared support

class QA_Matchers_HaveQA core
class QA_Matchers_Eventually core
class QA_Matchers_HaveText core
class QA_SupportData_Image datastruct

%% ===========================================================================
%% FACTORY BOT SUPPORT AND PATCHES
subgraph FACTORY BOT & PATCHES ["FACTORY BOT SUPPORT & PATCHES"]
direction TB
style FACTORY BOT & PATCHES fill:#F8F8F8,stroke:#D9C7FE,stroke-width:2px

FB_Support[factory_bot.rb<br/>FactoryBot helpers and patches]
FB_RSpec_MocksPatch[patches/rspec_mocks_doubles_fast_spec_helper_patch.rb<br/>FastSpec Mock patches]
FB_GitlabFactoryBotPatch[support/factory_bot.rb<br/>Gitlab logic for FactoryBot settable_id?]

end

class FB_Support support
class FB_RSpec_MocksPatch support
class FB_GitlabFactoryBotPatch support

%% ===========================================================================
%% PROVIDER CONTRACTS & CONTRACT TEST SUPPORT
subgraph PROVIDER CONTRACTS & HELPERS ["PROVIDER CONTRACTS & HELPERS"]
direction TB
style PROVIDER CONTRACTS & HELPERS fill:#F8F8F8,stroke:#FFEBCD,stroke-width:2px

PC_ContractSource[contract_source_helper.rb<br/>Determine contract source locations]
PC_UsersHelper[users_helper.rb<br/>Provider-specific user helpers]
end

class PC_ContractSource support
class PC_UsersHelper support

%% ===========================================================================
%% QA SUPPORT: FORMATTERS & PAGE CONCERNS
subgraph QA SUPPORT TOOLS ["QA REPORTING, FORMATTERS & SUPPORT"]
direction TB
style QA SUPPORT TOOLS fill:#F8F8F8,stroke:#F8F7F0,stroke-width:2px

QA_Formatters_Coverband[coverband_formatter.rb<br/>RSpec E2E-to-diff-file formatter]
QA_Formatters_Quarantine[quarantine_formatter.rb<br/>Tagging & tracking quarantined specs]
QA_PageConcern[page_concern.rb<br/>Module for page class correctness]
end

class QA_Formatters_Coverband support
class QA_Formatters_Quarantine support
class QA_PageConcern support

%% ===========================================================================
%% DATA STRUCTURE / UTILITY FILES
subgraph DATA STRUCTS & UTILITIES ["DATA STRUCTURES & UTILITIES"]
direction TB
style DATA STRUCTS & UTILITIES fill:#F8F8F8,stroke:#B8EAC6,stroke-width:2px

DS_ARModels[models.rb<br/>ActiveRecord models PartitionedRecord, etc]
DS_QueryRecorder[query_recorder.rb<br/>SQL query recorder for tests]
DS_MockActionCable[mock_action_cable.rb<br/>Mock ActionCable for GraphQL subscription testing]
DS_OrphanCleanup[orphan_final_artifacts_cleanup_helpers.rb<br/>Helpers for orphan artifact cleanup]
end

class DS_ARModels datastruct
class DS_QueryRecorder datastruct
class DS_MockActionCable datastruct
class DS_OrphanCleanup datastruct

%% ===========================================================================
%% RELATIONSHIPS BETWEEN GROUPS AND FILES

%% Shared Examples supply common state and data for matchers/validators and test specs
SE_AccessControl -.-> M_Access
SE_SecPolicies -.-> M_Access
SE_SecPolicies -.-> M_Result
SE_Presenters -.-> M_GraphQL
SE_WorkItemEpics -.-> M_Result
SE_BulkImpMemberAttrs -.-> M_Result
SE_Analytics -.-> M_DBEnums

SE_RemoteDev -.-> M_BeValidJSON
SE_Secrets -.-> M_BeValidJSON
SE_PackagesRegistry -.-> QA_Factories_Packages
SE_GitlabProjMigration -.-> *********************
SE_WebIDE -.-> QA_Factories_Files
SE_FeatureFlags -.-> QA_Factories_Shared
SE_MemberAccess -.-> QA_Factories_Shared

%% Matchers & Validators—many depend on utility/data structures
M_ExceedQueryLimit -- uses --> DS_QueryRecorder
M_BeNPlus1 -- uses --> DS_QueryRecorder
M_EventStore -- uses --> DS_ARModels
M_Query -- uses --> DS_QueryRecorder
M_InternalEvents -- uses --> M_EventStore
M_DBEnums -- uses --> DS_ARModels
M_SidekiqJSON -- uses --> M_BeValidJSON
M_PushFrontendFlags -- uses --> QA_Matchers_HaveText
M_PushLicensedFeatures -- uses --> QA_Matchers_HaveText
M_InternalEventsCLI -- uses --> QA_Factories_Shared
M_InvokeRopSteps -- uses --> M_Result
M_Result -- uses --> M_BeValidJSON

%% All access matchers share a general helpers relationship
M_AccessGeneric -.-> M_Access
M_Access -- extends --> M_AccessForController
M_Access -- extends --> M_AccessForRequest

%% Markdown matchers logic extended for EE
M_Markdown -- composed_by --> EE_MD_Matchers
EE_MD_Matchers -- composable_with --> M_Markdown

%% Snowplow event verifiers compose other data and event store
EE_Snowplow -- uses --> M_EventStore
EE_Snowplow -- uses --> M_Result

%% Epic aggregate matcher uses EE domain logic and general matchers
EE_EpicAggregate -- uses --> M_Result

%% FactoryBot helpers support most factorizations and QA matchers
FB_Support -- extend --> QA_Factories_Shared
FB_RSpec_MocksPatch -- extend --> FB_Support
FB_GitlabFactoryBotPatch -- extend --> QA_Factories_Shared
FB_Support -- supports --> QA_Factories_Agents
FB_Support -- supports --> QA_Factories_Badges
FB_Support -- supports --> QA_Factories_MRs
FB_Support -- supports --> QA_Factories_Jobs
FB_Support -- supports --> *********************
FB_Support -- supports --> QA_Factories_Runners
FB_Support -- supports --> QA_Factories_Files

%% Provider contract helpers for contract-based integration tests
PC_ContractSource -- supports --> PC_UsersHelper
PC_ContractSource -- used_by --> SE_AccessControl

%% QA: Factories used throughout shared contexts and QA matchers
QA_Factories_Shared -- support --> QA_Factories_Agents
QA_Factories_Shared -- support --> QA_Factories_MRs
QA_Factories_Shared -- support --> *********************
QA_Factories_Shared -- support --> QA_Factories_Issues
QA_Factories_Shared -- support --> QA_Factories_Groups
QA_Factories_Shared -- support --> QA_Factories_Snippets

%% QA matchers use factories for integration context
QA_Matchers_HaveQA -- uses --> QA_Factories_Shared
QA_Matchers_Eventually -- uses --> QA_Factories_Shared
QA_Matchers_HaveText -- uses --> QA_Factories_Shared

%% QA formatters utilize test state & page helpers
QA_Formatters_Coverband -- uses --> *********************
QA_Formatters_Quarantine -- uses --> QA_Factories_Shared
QA_PageConcern -- enforces --> QA_Factories_Shared

%% Data utilities used to setup test helpers or formatters
DS_MockActionCable -- used_by --> M_GraphQL
DS_OrphanCleanup -- used_by --> M_LockRows

%% Data helpers for image QA tests
QA_SupportData_Image -- used_by --> QA_Factories_Shared

%% Bulk import and cleanup helpers tap into utility/data definitions
SE_BulkImpMemberAttrs -- uses --> M_Result
SE_BulkImpMemberAttrs -- uses --> QA_Factories_Shared
SE_BulkImpMemberAttrs -- uses --> DS_ARModels

%% Test spec examples rely on the matcher ecosystem
SE_MemberAccess -- uses --> M_Access
SE_MemberAccess -- uses --> M_InternalEvents
SE_FeatureFlags -- uses --> M_PushFrontendFlags

%% End, establish overall domain abstraction boundary
```