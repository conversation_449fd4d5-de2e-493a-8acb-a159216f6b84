```mermaid
flowchart TD
  %% Subgraphs for logical groupings with pastel colors and pastel borders
  subgraph S1["Geo Replication, Disaster Recovery & Distributed Systems / Backup & Restore / Snapshot & Restore Management" ]
    direction TB
    S1style[""]:::sg_domain

    %% CORE COMPONENTS: RESTORE & BACKUP MANAGEMENT
    subgraph S2["Core Restore and Backup Commands" ]
      direction TB
      S2style[""]:::sg_core

      CLI_RestoreSub[/"gems/gitlab-backup-cli/lib/gitlab/backup/cli/commands/restore_subcommand.rb"/]:::core
      CLI_Database[/"gems/gitlab-backup-cli/lib/gitlab/backup/cli/targets/database.rb"/]:::core
    end

    %% SNAPSHOT/REPOSITORY MANAGEMENT
    subgraph S3["Snapshot & PoolRepository Management" ]
      direction TB
      S3style[""]:::sg_core

      PoolRepos[/"lib/backup/restore/pool_repositories.rb"/]:::core
      PoolRepoResult[ "PoolRepositories::Result" ]:::ds
    end

    %% GROUP UTILITY / SHARED
    subgraph S4["Result Data Structure" ]
      direction TB
      S4style[""]:::sg_data

      RestoreResult[ "Restore Result Struct" ]:::ds
    end

  end

  %% LOGICAL RELATIONSHIPS AND INTERACTIONS

  %% 1. Restore commands control the overall restore process
  CLI_RestoreSub -- Initiates full environment restore --> CLI_Database
  CLI_RestoreSub -- Coordinates repository/pool snapshot restores --> PoolRepos

  %% 2. PoolRepositories manages reinitialization/restore of pool repositories
  PoolRepos -- Returns reinit state/result as --> PoolRepoResult
  PoolRepos -- Used as part of snapshot/pool restore process in --> CLI_RestoreSub

  %% 3. Database restore logic implements backup and restore flows for data layer
  CLI_Database -- Implements DB backup/restore for --> CLI_RestoreSub

  %% 4. Data Structure dependency: PoolRepositories yields its Result struct
  PoolRepoResult -- Represents pool state to/for --> CLI_RestoreSub

  %% 5. Data/output structures (example for logical grouping)
  RestoreResult -- Used to aggregate/communicate restoration outcomes in --> CLI_RestoreSub

  %% 6. Patterns and reusable abstractions
  CLI_RestoreSub -- Uses subcommand pattern via --> CLI_Database

  %% 7. Group/class/struct associations
  PoolRepos -- Defines --> PoolRepoResult

  %% 8. Domain concept flow
  CLI_RestoreSub -- Triggers sequence: Repositories, then DB Restore, then Local Files via --> CLI_Database

  %% 9. Expose that PoolRepos interacts with data layer (pools/repositories backed by db/filesystem)
  PoolRepos -- Interacts with pool storage --.-> CLI_Database

  %% 10. Restoration outcome is consolidated and logged by restore subcommand
  PoolRepoResult -- Communicated as part of reporting in --> CLI_RestoreSub

  %% Styling blocks
  classDef core fill:#D4F1F9,stroke:#B5DEEF,stroke-width:2px,color:#333,stroke-dasharray: 0 0,rx:12,ry:12
  classDef ds fill:#E0F8E0,stroke:#A9DDAB,stroke-width:2px,color:#333,rx:12,ry:12
  classDef sg_domain fill:#F8F8F8,stroke:#E6E6FA,stroke-width:3px,rx:20,ry:20
  classDef sg_core fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rx:16,ry:16
  classDef sg_data fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2px,rx:16,ry:16

  %% Ensure all nodes are nicely spaced and vertical
  linkStyle default stroke-width:2px,stroke:#bfd1e6
```