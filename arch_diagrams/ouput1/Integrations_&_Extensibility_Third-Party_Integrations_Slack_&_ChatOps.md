```mermaid
flowchart TD
  %% Styling definitions
  %% Core domain files: pastel blue
  classDef core fill:#D4F1F9,stroke:#A0D6E5,stroke-width:2px,color:#144E6C,stroke-dasharray: 2 2,rx:12,ry:12;
  %% Supporting/utility files: pastel yellow
  classDef util fill:#FFF8DC,stroke:#FFD700,stroke-width:2px,color:#75691D,rx:12,ry:12;
  %% Data structure files: pastel green
  classDef datastruct fill:#E0F8E0,stroke:#95D7B2,stroke-width:2px,color:#2B4C3F,rx:12,ry:12;
  %% Error handling: pastel red
  classDef error fill:#FFE4E1,stroke:#FFB6B2,stroke-width:2px,color:#9B3B35,rx:12,ry:12;
  %% Initialization/setup files: pastel purple
  classDef init fill:#E6E6FA,stroke:#C6C9E7,stroke-width:2px,color:#444468,rx:12,ry:12;
  %% Logical groupings/subgraphs
  classDef group fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,color:#333,border-radius:16px;

  %% MAIN CORE INTEGRATION ABSTRACTIONS
  subgraph sgCoreIntegrations["Core Integration Models & Abstractions"]
    direction TB
    INTEGRATION_SLACK["integrations/slack.rb"  
      "\nSlack Integration\nNotification logic, event triggers, config" ]
    INTEGRATION_MATTERMOST["integrations/mattermost.rb\nMattermost Integration\nParallels Slack, similar notification flows" ]
    INTEGRATION_SLACK_APP["integrations/gitlab_slack_application.rb\nGitLab Slack App\nProject/Group/Instance configuration, event settings" ]
    INTEGRATION_SLACK_SLASH_CMDS["integrations/slack_slash_commands.rb\nSlack Slash Commands Integration\nTriggers, configuration, permissions" ]
    INTEGRATION_INSTANCE_SLACK["integrations/instance/slack.rb\nInstance-level Slack Integration\nScalable configuration" ]
    INTEGRATION_INSTANCE_MATTERMOST["integrations/instance/mattermost.rb" ]
    INTEGRATION_INSTANCE_SLACK_CMDS["integrations/instance/slack_slash_commands.rb" ]
    class INTEGRATION_SLACK,INTEGRATION_MATTERMOST,INTEGRATION_SLACK_APP,INTEGRATION_SLACK_SLASH_CMDS,INTEGRATION_INSTANCE_SLACK,INTEGRATION_INSTANCE_MATTERMOST,INTEGRATION_INSTANCE_SLACK_CMDS core
  end
  class sgCoreIntegrations group

  %% DOMAIN DATA STRUCTURES
  subgraph sgDataStruct["Domain Events & Chat Message Data Structures"]
    direction TB
    CHAT_BASE["chat_message/base_message.rb\nBase Chat Message\nCommon event fields and formatting" ]
    CHAT_ALERT["chat_message/alert_message.rb\nAlert Message\nFields: title, severity, events, status" ]
    CHAT_ISSUE["chat_message/issue_message.rb\nIssue Chat Message\nDescribes issue events, state, description" ]
    CHAT_NOTE["chat_message/note_message.rb\nNote Message\nHandles note events and merge " ]
    CHAT_WIKI["chat_message/wiki_page_message.rb\nWiki Page Message\nWiki update and activity message fields" ]
    CHAT_GROUP_MENTION["chat_message/group_mention_message.rb\nGroup Mention Msg\nIssue/note merge mention tracking" ]
    CHAT_DEPLOYMENT["chat_message/deployment_message.rb\nDeployment Message\nDeployment event fields & coloring" ]
    CHAT_PUSH["chat_message/push_message.rb\nPush Event Message" ]
    CHAT_PIPELINE["chat_message/pipeline_message.rb\nPipeline Message\nPipeline event details" ]
    class CHAT_BASE,CHAT_ALERT,CHAT_ISSUE,CHAT_NOTE,CHAT_WIKI,CHAT_GROUP_MENTION,CHAT_DEPLOYMENT,CHAT_PUSH,CHAT_PIPELINE datastruct
  end
  class sgDataStruct group

  %% SLACK APP EVENT PIPELINE
  subgraph sgSlackEventPipeline["Slack App: Event Routing & Processing"]
    direction TB
    SLACK_EVENT_WORKER["workers/integrations/slack_event_worker.rb\nBackground Event Worker" ]
    SLACK_EVENT_SERVICE["services/integrations/slack_event_service.rb\nSlack Event Router\nInspects event type, dispatches to handler" ]
    SLACK_EVENTS_URL_VERIFY["services/integrations/slack_events/url_verification_service.rb\nResponder for Slack URL verification event" ]
    SLACK_EVENTS_HOME_OPENED["services/integrations/slack_events/app_home_opened_service.rb\nHandles 'app_home_opened' & block kit payload" ]
    SLACK_INTERACTION_SERVICE["services/integrations/slack_interaction_service.rb\nDispatches user interactions from Slack" ]
    INTERACTIONS_BLOCK_ACTION_SERVICE["services/integrations/slack_interactions/block_action_service.rb\nProcesses action blocks from Slack UI flows" ]
    INTERACTIONS_IM_MODAL_OPENED["services/integrations/slack_interactions/incident_management/incident_modal_opened_service.rb\nIncident modal open handler, posting Slack Block Kit UI" ]
    INTERACTIONS_IM_MODAL_CLOSED["services/integrations/slack_interactions/incident_management/incident_modal_closed_service.rb\nCloses/handles modal close" ]
    INTERACTIONS_IM_MODAL_SUBMIT["services/integrations/slack_interactions/incident_management/incident_modal_submit_service.rb\nHandles incident submissions, issue creation" ]
    class SLACK_EVENT_WORKER,SLACK_EVENT_SERVICE,SLACK_EVENTS_URL_VERIFY,SLACK_EVENTS_HOME_OPENED,SLACK_INTERACTION_SERVICE,INTERACTIONS_BLOCK_ACTION_SERVICE,INTERACTIONS_IM_MODAL_OPENED,INTERACTIONS_IM_MODAL_CLOSED,INTERACTIONS_IM_MODAL_SUBMIT core
  end
  class sgSlackEventPipeline group

  %% EXTENSIBILITY/UTILITY & SCOPES
  subgraph sgExtUtils["Slack App Utilities, Options, Scope Propagation"]
    direction TB
    SLACK_OPTION_SERVICE["services/integrations/slack_option_service.rb\nDynamic Option Handler\nRoute assignee/labels to handlers" ]
    SLACK_USER_SEARCH["services/integrations/slack_options/user_search_handler.rb\nSlack User Search" ]
    SLACK_LABEL_SEARCH["services/integrations/slack_options/label_search_handler.rb\nSlack Label Search" ]
    PROP_BULK_CREATE["services/integrations/propagation/bulk_create_service.rb\nIntegration scope propagation bulk" ]
    SLACK_WS_API_SCOPE["models/integrations/slack_workspace/api_scope.rb\nAPI Scope Record" ]
    SLACK_WS_INTEGRATION_API_SCOPE["models/integrations/slack_workspace/integration_api_scope.rb\nScope-linking record" ]
    SLACK_INTEGRATION_MODEL["models/slack_integration.rb\nDB Record for imported Slack workspaces/scopes" ]
    class SLACK_OPTION_SERVICE,SLACK_USER_SEARCH,SLACK_LABEL_SEARCH,PROP_BULK_CREATE,SLACK_WS_API_SCOPE,SLACK_WS_INTEGRATION_API_SCOPE,SLACK_INTEGRATION_MODEL util
  end
  class sgExtUtils group

  %% CONCERNS / REUSABLE SHARED LOGIC
  subgraph sgConcerns["Integration Shared Concerns & Notifiers"]
    direction TB
    BASE_SLACK_NOTIF["concerns/integrations/base/slack_notification.rb\nShared Slack notification logic events, logging" ]
    SLACK_MATTERMOST_NOTIFIER["concerns/integrations/slack_mattermost_notifier.rb\nSends outgoing messages Slack/Mattermost" ]
    SLACK_MATTERMOST_FIELDS["concerns/integrations/slack_mattermost_fields.rb\nConfigures webhook and channel fields" ]
    BASE_CHAT_NOTIFICATION["concerns/integrations/base/chat_notification.rb\nGeneral chat notification concern" ]
    class BASE_SLACK_NOTIF,SLACK_MATTERMOST_NOTIFIER,SLACK_MATTERMOST_FIELDS,BASE_CHAT_NOTIFICATION util
  end
  class sgConcerns group

  %% CONTROLLERS and ROUTING
  subgraph sgControllers["Controllers, Settings, and OAuth Flow"]
    direction TB
    ADM_SLACK_CTRL["controllers/admin/slacks_controller.rb\nAdmin setup & OAuth redirects" ]
    PRJ_SLACK_CMDS_CTRL["controllers/projects/integrations/slash_commands_controller.rb\nRoute project-level slash commands" ]
    PRJ_SLACK_SETTINGS_CTRL["controllers/projects/settings/slacks_controller.rb\nConfigures Project->Slack bindings" ]
    SLACK_CTRL_SETTINGS["controllers/concerns/integrations/slack_controller_settings.rb\nOAuth and error-handling" ]
    class ADM_SLACK_CTRL,PRJ_SLACK_CMDS_CTRL,PRJ_SLACK_SETTINGS_CTRL,SLACK_CTRL_SETTINGS init
  end
  class sgControllers group

  %% SERVICES: INSTALLATION, AUTHZ, EXCLUSIONS
  subgraph sgInstallServices["Installation & Authorization Services"]
    direction TB
    INST_SVC_INSTANCE["services/integrations/slack_installation/instance_service.rb\nInstance-level Slack app installer" ]
    INST_SVC_PROJECT["services/integrations/slack_installation/project_service.rb\nProject-level installer or binding" ]
    INST_SVC_GROUP["services/integrations/slack_installation/group_service.rb\nGroup-level installer or binding" ]
    EXCLUSION_BASE["services/integrations/exclusions/base_service.rb\nExclusions and filtering for integrations" ]
    CHATS_AUTH["services/chat_names/authorize_user_service.rb\nLinking chat identity with user accounts" ]
    class INST_SVC_INSTANCE,INST_SVC_PROJECT,INST_SVC_GROUP,EXCLUSION_BASE,CHATS_AUTH util
  end
  class sgInstallServices group

  %% WORKERS
  subgraph sgWorkers["Chat Notification Background Workers"]
    direction TB
    CHAT_NOTIFICATION_WORKER["workers/chat_notification_worker.rb\nDelivers chat messages after commit etc" ]
    class CHAT_NOTIFICATION_WORKER core
  end
  class sgWorkers group

  %% KEY UTILS & LIBS
  subgraph sgLibs["Supporting Libraries & Domain Enablers"]
    direction TB
    SLACK_API_LIB["lib/slack/api.rb\nSlack API Wrapper\nPosts events to Slack endpoints" ]
    SLACK_BLOCKKIT_INCIDENT("lib/slack/block_kit/incident_management/incident_modal_opened.rb\nIncident Modal Block Kit" )
    SLACK_BLOCKKIT_HOME("lib/slack/block_kit/app_home_opened.rb\nApp Home Block Kit Builder" )
    SLACK_MARKDOWN_SANIT("lib/slack_markdown_sanitizer.rb\nCleans markdown for Slack compat" )
    GITLAB_SLASH_VERIFY("lib/gitlab/slash_commands/verify_request.rb\nSlash command request verifier" )
    GITLAB_SLASH_RUN("lib/gitlab/slash_commands/run.rb\nCore logic for ChatOps /run" )
    GITLAB_SLASH_PRESENTERS_ERROR("lib/gitlab/slash_commands/presenters/error.rb\nPresent error responses" )
    GITLAB_SLASH_PRESENTERS_DEPLOY("lib/gitlab/slash_commands/presenters/deploy.rb\nPresent deployment states" )
    GITLAB_SLASH_PRESENTERS_INCIDENT("lib/gitlab/slash_commands/presenters/incident_management/incident_new.rb\nIncident response presentation" )
    GITLAB_SLASH_PRESENTERS_ISSUE_SHOW("lib/gitlab/slash_commands/presenters/issue_show.rb\nShows Issue info" )
    SLACK_MANIFEST("lib/slack/manifest.rb\nDefines Slack App manifest for sharing/config" )
    class SLACK_API_LIB,SLACK_BLOCKKIT_INCIDENT,SLACK_BLOCKKIT_HOME,SLACK_MARKDOWN_SANIT,GITLAB_SLASH_VERIFY,GITLAB_SLASH_RUN,GITLAB_SLASH_PRESENTERS_ERROR,GITLAB_SLASH_PRESENTERS_DEPLOY,GITLAB_SLASH_PRESENTERS_INCIDENT,GITLAB_SLASH_PRESENTERS_ISSUE_SHOW,SLACK_MANIFEST util
  end
  class sgLibs group

  %% DOMAIN RELATIONSHIPS & FLOWS

  %% Integration model hierarchies and concern inclusion
  INTEGRATION_SLACK --"includes"--> BASE_SLACK_NOTIF
  INTEGRATION_SLACK --"includes"--> SLACK_MATTERMOST_NOTIFIER
  INTEGRATION_SLACK --"includes"--> SLACK_MATTERMOST_FIELDS
  INTEGRATION_MATTERMOST --"includes"--> BASE_CHAT_NOTIFICATION
  INTEGRATION_MATTERMOST --"includes"--> SLACK_MATTERMOST_NOTIFIER
  INTEGRATION_MATTERMOST --"includes"--> SLACK_MATTERMOST_FIELDS
  INTEGRATION_SLACK_APP --"utilizes"--> SLACK_API_LIB
  INTEGRATION_SLACK_SLASH_CMDS --"utilizes"--> GITLAB_SLASH_RUN

  %% Instance-level models for scale
  INTEGRATION_INSTANCE_SLACK --"inherits from"--> INTEGRATION_SLACK
  INTEGRATION_INSTANCE_MATTERMOST --"inherits from"--> INTEGRATION_MATTERMOST

  %% Chat message data structure inheritance
  CHAT_ALERT --"inherits from"--> CHAT_BASE
  CHAT_ISSUE --"inherits from"--> CHAT_BASE
  CHAT_NOTE --"inherits from"--> CHAT_BASE
  CHAT_WIKI --"inherits from"--> CHAT_BASE
  CHAT_GROUP_MENTION --"inherits from"--> CHAT_BASE
  CHAT_DEPLOYMENT --"inherits from"--> CHAT_BASE
  CHAT_PUSH --"inherits from"--> CHAT_BASE
  CHAT_PIPELINE --"inherits from"--> CHAT_BASE

  %% Notification pipeline: worker to event router/services
  SLACK_EVENT_WORKER --"invokes"--> SLACK_EVENT_SERVICE
  SLACK_EVENT_SERVICE --"routes to"--> SLACK_EVENTS_URL_VERIFY
  SLACK_EVENT_SERVICE --"routes to"--> SLACK_EVENTS_HOME_OPENED
  SLACK_EVENT_SERVICE --"routes to"--> SLACK_INTERACTION_SERVICE

  %% Interaction routing chain
  SLACK_INTERACTION_SERVICE --"dispatches"--> INTERACTIONS_BLOCK_ACTION_SERVICE
  SLACK_INTERACTION_SERVICE --"dispatches"--> INTERACTIONS_IM_MODAL_OPENED
  SLACK_INTERACTION_SERVICE --"dispatches"--> INTERACTIONS_IM_MODAL_CLOSED
  SLACK_INTERACTION_SERVICE --"dispatches"--> INTERACTIONS_IM_MODAL_SUBMIT

  INTERACTIONS_IM_MODAL_OPENED --"constructs Block Kit with"--> SLACK_BLOCKKIT_INCIDENT
  SLACK_EVENTS_HOME_OPENED --"constructs Block Kit with"--> SLACK_BLOCKKIT_HOME
  INTERACTIONS_IM_MODAL_SUBMIT --"may present"--> GITLAB_SLASH_PRESENTERS_INCIDENT

  %% Slash command invocation
  INTEGRATION_SLACK_SLASH_CMDS --"uses"--> GITLAB_SLASH_VERIFY
  INTEGRATION_SLACK_SLASH_CMDS --"uses"--> GITLAB_SLASH_PRESENTERS_ERROR
  INTEGRATION_SLACK_SLASH_CMDS --"presents deployment status via"--> GITLAB_SLASH_PRESENTERS_DEPLOY
  INTEGRATION_SLACK_SLASH_CMDS --"presents incidents via"--> GITLAB_SLASH_PRESENTERS_INCIDENT

  %% Option service delegation
  SLACK_OPTION_SERVICE --"routes :assignee"--> SLACK_USER_SEARCH
  SLACK_OPTION_SERVICE --"routes :labels"--> SLACK_LABEL_SEARCH

  %% Scope propagation and domain data
  PROP_BULK_CREATE --"links to"--> SLACK_WS_INTEGRATION_API_SCOPE
  SLACK_WS_INTEGRATION_API_SCOPE --"references"--> SLACK_WS_API_SCOPE
  SLACK_WS_INTEGRATION_API_SCOPE --"references"--> SLACK_INTEGRATION_MODEL

  %% Controller setup and OAuth
  PRJ_SLACK_SETTINGS_CTRL --"includes"--> SLACK_CTRL_SETTINGS
  ADM_SLACK_CTRL --"includes"--> SLACK_CTRL_SETTINGS
  ADM_SLACK_CTRL --"instantiates"--> INTEGRATION_SLACK_APP
  PRJ_SLACK_SETTINGS_CTRL --"references"--> INTEGRATION_SLACK_APP
  PRJ_SLACK_CMDS_CTRL --"references"--> INTEGRATION_SLACK_SLASH_CMDS

  %% Worker relationship: Chat events delivery
  CHAT_NOTIFICATION_WORKER --"delivers"--> CHAT_BASE

  %% Install/authorization flow
  INST_SVC_INSTANCE --"installs on"--> INTEGRATION_INSTANCE_SLACK
  INST_SVC_PROJECT --"installs on"--> INTEGRATION_SLACK_APP
  INST_SVC_GROUP --"installs on"--> INTEGRATION_SLACK_APP

  EXCLUSION_BASE --"restricts"--> INTEGRATION_SLACK_APP
  
  CHATS_AUTH --"authorizes user for"--> INTEGRATION_SLACK_APP

  %% Slack API usage across pipeline
  SLACK_EVENT_SERVICE --"uses"--> SLACK_API_LIB
  SLACK_EVENTS_HOME_OPENED --"uses"--> SLACK_API_LIB
  INTERACTIONS_IM_MODAL_OPENED --"uses"--> SLACK_API_LIB
  INTERACTIONS_IM_MODAL_SUBMIT --"uses"--> SLACK_API_LIB

  %% Slack markdown utility
  CHAT_BASE --"formats via"--> SLACK_MARKDOWN_SANIT

  %% Manifest/App Home
  INTEGRATION_SLACK_APP --"defines app via"--> SLACK_MANIFEST
  SLACK_BLOCKKIT_HOME --"display for"--> INTEGRATION_SLACK_APP

  %% Extra: Presenters link for slash commands
  GITLAB_SLASH_RUN --"returns"--> GITLAB_SLASH_PRESENTERS_ERROR
  GITLAB_SLASH_RUN --"returns"--> GITLAB_SLASH_PRESENTERS_DEPLOY
  GITLAB_SLASH_RUN --"returns"--> GITLAB_SLASH_PRESENTERS_INCIDENT
  GITLAB_SLASH_RUN --"returns"--> GITLAB_SLASH_PRESENTERS_ISSUE_SHOW

  %% ENFORCE PASTEL COLORS AND SPACING
```