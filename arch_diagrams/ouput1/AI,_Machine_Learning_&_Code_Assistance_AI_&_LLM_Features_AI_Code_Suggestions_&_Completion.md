```mermaid
flowchart TB
%% Domain: AI Code Suggestions & Completion - Logical Architecture

%% Colors
%% Core domain files: pastel blue (#D4F1F9)
%% Supporting/utility: pastel yellow (#FFF8DC)
%% Data structure files: pastel green (#E0F8E0)
%% Error: pastel red (#FFE4E1)
%% Initialization/setup: pastel purple (#E6E6FA)
%% Groupings border: #B1C7E3 fill: #F8F8F8

%% MAIN SUBGRAPH: AI Code Suggestions & Completion
subgraph Domain["AI Code Suggestions & Completion"]
  direction TB
  %% 1. TASKS AND EXECUTION
  subgraph sgTaskExecution[ "Task & Execution Flows" ]
    direction TB
    style sgTaskExecution fill:#F8F8F8,stroke:#B1C7E3,stroke-width:2,rx:20
    CS_TaskFactory["TaskFactory
Task selector and dispatcher":::support]
    CS_TasksBase["Tasks::Base
Abstract Task":::core]
    CS_TasksCodeCompletion["Tasks::CodeCompletion
Handles code completion task":::core]
    CS_TasksCodeGeneration["Tasks::CodeGeneration
Handles code generation task":::core]
    CS_Client["Client
External API client for suggestions":::support]
    CS_Context["Context
Request context assembler":::support]
    CS_ModelDetailsBase["ModelDetails::Base
Abstract model info/provider":::support]
    CS_ModelDetailsCodeCompletion["ModelDetails::CodeCompletion
Model info for completion":::support]
    CS_EventsFinder["CodeSuggestionEventsFinder
Query/filter suggestion events":::supportbg]
    CS_StoreDeps["Xray::StoreDependenciesService
Saves dependency info for code analysis":::support]
    CS_ScanDeps["RepositoryXray::ScanDependenciesService
Scans repo for dependency files":::support]
    CS_Instruction["Instruction
Prompt/instruction builder":::support]
    CS_InstructionsExtractor["InstructionsExtractor
Extracts code instructions/comments":::support]
    CS_ProgrammingLanguage["ProgrammingLanguage
Maps/detects languages":::support]
    CS_FileContent["FileContent
Extracts code context":::support]
  end

  %% 2. PROMPTS AND PROVIDERS
  subgraph sgPromptProvider[ "Prompt Builders & Model Providers" ]
    direction TB
    style sgPromptProvider fill:#F8F8F8,stroke:#B1C7E3,stroke-width:2,rx:20
    CS_PromptsBase["Prompts::Base
Shared prompt base":::support]
    CS_PromptsDefault["Prompts::CodeCompletion::Default
Default completion prompt":::support]
    CS_PromptsAnthropicBase["Prompts::CodeCompletion::Anthropic::Base
Anthropic model base":::support]
    CS_PromptsAnthropicHaiku["Prompts::CodeCompletion::Anthropic::ClaudeHaiku
Claude Haiku prompt":::support]
    CS_PromptsAnthropicSonnet["Prompts::CodeCompletion::Anthropic::ClaudeSonnet
Claude Sonnet prompt":::support]
    CS_PromptsAiGateway["Prompts::CodeCompletion::AiGatewayCodeCompletionMessage
Gateway-style prompt":::support]
    CS_PromptsFireworks["Prompts::CodeCompletion::FireworksCodestral
Fireworks Codestral prompt":::support]
    CS_PromptsDefaultGen["Prompts::CodeCompletion::Default
Default prompt class":::support]
  end

  %% 3. DOMAIN DATA & PERSISTENCE
  subgraph sgDataInfra[ "Domain Data & Persistence" ]
    direction TB
    style sgDataInfra fill:#F8F8F8,stroke:#5ED6B0,stroke-width:2,rx:20
    CS_CodeSuggestionEvent["CodeSuggestionEvent
Suggestion event record":::data]
    CS_AgentVersion["AgentVersion
Tracks AI models":::data]
    CS_EventsFinder
  end

  %% 4. GRAPHQL LAYER Mutations & Types
  subgraph sgGraphQL[ "GraphQL API Entry Points" ]
    direction TB
    style sgGraphQL fill:#F8F8F8,stroke:#B999F3,stroke-width:2,rx:20
    GQL_MutationAction["Mutations::AI::Action
Generic AI action mutation":::core]
    GQL_MutationAgentCreate["Mutations::AI::Agents::Create
Create AI agent":::support]
    GQL_MutationAgentUpdate["Mutations::AI::Agents::Update
Update AI agent":::support]
    GQL_MutationAgentDestroy["Mutations::AI::Agents::Destroy
Destroy AI agent":::support]
    GQL_CodeSuggestionsAccessResolver["Resolvers::AI::CodeSuggestionsAccessResolver
Checks code suggestions access":::support]
    GQL_UserContextsResolver["Resolvers::AI::UserCodeSuggestionsContextsResolver
Finds per-user suggestion contexts":::support]
    GQL_CodeSuggestionEventType["Types::Analytics::AIUsage::CodeSuggestionEventType
Event result type":::data]
    GQL_AdditionalContextType["Types::AI::AdditionalContextType
Additional context type":::data]
    GQL_SummarizeReviewInput["Types::AI::SummarizeReviewInputType
Input for review summary":::support]
    GQL_GenerateDescriptionInput["Types::AI::GenerateDescriptionInputType
Input for description gen":::support]
    GQL_GenerateCubeQueryInput["Types::AI::GenerateCubeQueryInputType
Input for cube analytics":::support]
    GQL_EEPipelineType["EE::Types::Ci::PipelineType
GraphQL type, pipeline, hooks AI":::support]
    GQL_AnalyticsMetrics["Resolvers::Analytics::AiMetrics::CodeSuggestionMetricsResolver
Suggest metrics resolver":::support]
    GQL_AnalyticsNSMetrics["Resolvers::Analytics::AiMetrics::NamespaceMetricsResolver
NS-level metrics":::support]
  end

  %% 5. LLM SERVICES: Completion, Generation, Summarization, etc.
  subgraph sgLlmServices[ "LLM Services & Adapters" ]
    direction TB
    style sgLlmServices fill:#F8F8F8,stroke:#B1C7E3,stroke-width:2,rx:20
    LLM_ServiceCompletion["Llm::CompletionWorker
Async completion worker":::core]
    LLM_CompletionService["Llm::Internal::CompletionService
LLM completion orchestrator":::core]
    LLM_ExecMethodService["Llm::ExecuteMethodService
Executes LLM tasks":::core]
    LLM_DescriptionService["Llm::GenerateDescriptionService
Describe resource":::core]
    LLM_ReviewService["Llm::ReviewMergeRequestService
Review MR via LLM":::core]
    LLM_GenerateCommitMsgService["Llm::GenerateCommitMessageService
Commit message via LLM":::core]
    LLM_ExplainCodeService["Llm::ExplainCodeService
Code explanation":::core]
    LLM_SummarizeNewMRService["Llm::SummarizeNewMergeRequestService
Summary for new MR":::core]
    LLM_GenerateSummaryService["Llm::GenerateSummaryService
Summarize comments":::core]
    LLM_DescriptionComposer["Llm::DescriptionComposerService
Interactive descriptions":::core]
    LLM_GitCommandService["Llm::GitCommandService
Handle git-related queries":::core]
  end

  %% 6. LLM MODELS, PROMPTS, FORMATTERS
  subgraph sgLlmInfra[ "LLM Model Integration & Prompting" ]
    direction TB
    style sgLlmInfra fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rx:20
    LLM_VAICodeCompletion["VertexAI::ModelConfigurations::CodeCompletion
Google Vertex code completion":::support]
    LLM_VAICode["VertexAI::ModelConfigurations::Code
Vertex generic code model":::support]
    LLM_VAIText["VertexAI::ModelConfigurations::Text
Vertex text completion":::support]
    LLM_VAIChat["VertexAI::ModelConfigurations::Chat
Vertex chat":::support]
    LLM_VAICodeChat["VertexAI::ModelConfigurations::CodeChat
Vertex code chat":::support]
    LLM_VAIEmbText["VertexAI::Embeddings::Text
Vertex embeddings":::support]
    LLM_ChainResponseMod["Chain::ResponseModifier
LLM pipeline response mod":::support]
    LLM_ChainStreamedAnswer["Chain::StreamedAnswer
Streaming support":::support]
    LLM_ChainStreamedDocAnswer["Chain::StreamedDocumentationAnswer
Stream doc answers":::support]
    LLM_ChainPlainResp["Chain::PlainResponseModifier
Response adapter":::support]
    LLM_ChainOutputParser["Chain::Parsers::OutputParser
LLM output parsers":::support]
    LLM_ChainGitlabCtx["Chain::GitlabContext
Stores job/user/resource context":::support]
    LLM_ChainXrayContext["Chain::Concerns::XrayContext
Integration for Xray context":::support]
    LLM_ChainJobLog["Chain::Concerns::JobLoggable
CI log access for LLM":::support]
    LLM_ResponseModTool["ResponseModifiers::ToolAnswer
Parse tool outputs":::support]
    LLM_ResponseModResVuln["ResponseModifiers::ResolveVulnerability
Handles vuln response":::support]
    LLM_AFmt["Utils::CodeSuggestionFormatter
LLM-friendly code formatting":::support]
    LLM_UtilsAIFeats["Utils::AiFeaturesCatalogue
AI feature introspection":::support]
    LLM_CompletionsFactory["CompletionsFactory
Selects completion logic":::core]
  end

  %% 7. CODE SUGGESTION DOMAIN - USER ACCESS & AUTH
  subgraph sgAuth[ "User Authorization & Usage" ]
    direction TB
    style sgAuth fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,rx:20
    AI_UserAuth["Concerns::AI::UserAuthorizable
Authorizes AI feature use":::support]
    CS_CodeSuggestionsHelper["GitlabSubscriptions::CodeSuggestionsHelper
Subscription-aware helpers":::support]
    CS_IdeHelper["EE::IdeHelper
Adds code suggestions to IDE":::support]
    LLM_TanukiBot["Llm::TanukiBot
Feature flag and chat integration":::core]
  end

  %% 8. EXTERNAL ADAPTERS / CLOUD CONNECTOR
  subgraph sgCloud[ "Cloud Connectors / Self-Hosted/3rd-party Probes" ]
    direction TB
    style sgCloud fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2,rx:20
    Cloud_ModelConfigProbe["CloudConnector::SelfHosted::ModelConfigurationProbe
Probe self-hosted model config":::init]
    Cloud_CodeSuggestionsLicenseProbe["CloudConnector::SelfHosted::CodeSuggestionsLicenseProbe
License probe for self-hosted":::init]
    Cloud_AmazonQProbe["CloudConnector::AmazonQ::EndToEndProbe
Amazon Q connectivity":::init]
    Cloud_EndToEndProbe["CloudConnector::StatusChecks::EndToEndProbe
Probes if code completion works":::init]
    Ai_AmazonQ_Commands["AI::AmazonQ::Commands
Amazon Q integration":::init]
    Ai_AmazonQ_Identity["AI::AmazonQ::IdentityProviderPayloadFactory
Builds payload for Amazon Q":::init]
  end

  %% 9. AI RESOURCE WRAPPERS
  subgraph sgResources[ "AI Wrappers: Issues, Epics, Builds" ]
    direction TB
    style sgResources fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rx:20
    AIR_CiBuild["AIResource::Ci::Build
CI build resource with AI":::core]
    AIR_Issue["AIResource::Issue
Issue resource":::core]
    AIR_Epic["AIResource::Epic
Epic resource":::core]
    AIR_Wrapper["AIResource::Wrapper
Resource wrapper/adapter":::core]
    AIR_Noteable["AIResource::Concerns::Noteable
Note interface concern":::support]
  end

  %% 10. DOMAIN DATA STRUCTURES
  subgraph sgDataStructs[ "Domain Data Structures" ]
    direction TB
    style sgDataStructs fill:#F8F8F8,stroke:#A4E7A4,stroke-width:2,rx:20
    LLM_AiMsgContext["AiMessageContext
Holds message context":::data]
    LLM_AiMsgAdditionalContext["AiMessageAdditionalContext
Additional context wrapper":::data]
    LLM_AiMsgAdditionalContextItem["AiMessageAdditionalContextItem
Additional context item":::data]
    CS_FileContent
    CS_ProgrammingLanguage
    CS_Context
  end

  %% 11. TEMPLATES & PROMPTS
  subgraph sgTemplates[ "Prompt Templates" ]
    direction TB
    style sgTemplates fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rx:20
    TMPL_SummarizeReview["Templates::SummarizeReview
Review summary prompt":::support]
    TMPL_GenDescription["Templates::GenerateDescription
Description prompt":::support]
    TMPL_GenCommitMessage["Templates::GenerateCommitMessage
Commit message prompt":::support]
    TMPL_CategorizeQ["Templates::CategorizeQuestion
Question categorization":::support]
  end

  %% 12. TESTS / SPECS test impact on logical flow, not all relationships drawn
  subgraph sgSpecs[ "Domain Unit Tests" ]
    direction TB
    style sgSpecs fill:#F8F8F8,stroke:#FFD9D9,stroke-width:2,rx:20
    Sp_CodeGenGatewayMsgSpec["spec/prompts/code_generation/ai_gateway_messages_spec
Tests codegen prompt params":::support]
    Sp_TasksBaseSpec["spec/tasks/base_spec
Tests abstract base logic":::support]
    Sp_SlashCommandToolSpec["spec/chain/tools/slash_command_tool_spec
Tests command tool logic":::support]
    Sp_ResolveVulnSpec["spec/completions/resolve_vulnerability_spec
Vuln completion test":::support]
  end

  %% LOGICAL RELATIONSHIPS / CONNECTIVITY

  %% 1. TaskFactory picks, builds, and executes the relevant task
  CS_TaskFactory-->|"creates" |CS_TasksCodeCompletion
  CS_TaskFactory-->|"creates" |CS_TasksCodeGeneration
  CS_TaskFactory-->|"uses"|CS_TasksBase
  CS_TaskFactory-->|"delegates to client"|CS_Client

  %% 2. Tasks use ModelDetails to pick model, inform prompt builders
  CS_TasksCodeCompletion-->|"gets model info"|CS_ModelDetailsCodeCompletion
  CS_TasksCodeGeneration-->|"gets model info"|CS_ModelDetailsBase
  CS_TasksBase-->|"uses"|CS_ModelDetailsBase

  %% 3. Tasks instantiate Prompts/Providers
  CS_TasksCodeCompletion-->|"instantiates"|CS_PromptsBase
  CS_TasksCodeCompletion-->|"uses"|CS_PromptsAnthropicBase
  CS_TasksCodeCompletion-->|"uses"|CS_PromptsAnthropicHaiku
  CS_TasksCodeCompletion-->|"uses"|CS_PromptsAnthropicSonnet
  CS_TasksCodeCompletion-->|"uses/dispatches to"|CS_PromptsFireworks
  CS_TasksCodeCompletion-->|"uses/dispatches to"|CS_PromptsAiGateway
  CS_TasksCodeGeneration-->|"uses prompt"|CS_PromptsDefault
  CS_TasksCodeGeneration-->|"uses prompt"|CS_PromptsDefaultGen

  %% 4. ModelDetails select LLM/Cloud/Vertex AI providers
  CS_ModelDetailsBase-->|"selects provider"|LLM_VAICodeCompletion
  CS_ModelDetailsBase-->|"selects provider"|LLM_VAICode
  CS_ModelDetailsBase-->|"selects provider"|LLM_VAIChat

  %% 5. Prompts construct parameters for LLM usage
  CS_PromptsAnthropicBase-->|"builds"|CS_PromptsAnthropicHaiku
  CS_PromptsAnthropicBase-->|"builds"|CS_PromptsAnthropicSonnet

  %% 6. FileContent, ProgrammingLanguage, Instruction, Context
  CS_TasksBase-->|"extracts code"|CS_FileContent
  CS_FileContent-->|"gets lang info"|CS_ProgrammingLanguage
  CS_TasksBase-->|"parses context"|CS_Context
  CS_TasksCodeCompletion-->|"builds instructions"|CS_Instruction
  CS_Instruction-->|"extracts from code"|CS_InstructionsExtractor

  %% 7. Data flows from Task/Prompt to Client for API calls
  CS_TasksCodeCompletion-->|"calls"|CS_Client
  CS_TasksCodeGeneration-->|"calls"|CS_Client
  CS_Client-->|"uses context"|CS_Context

  %% 8. Events and Persistence
  CS_TasksCodeCompletion-->|"records to"|CS_CodeSuggestionEvent
  CS_EventsFinder-->|"queries from"|CS_CodeSuggestionEvent

  %% 9. Scan Dependencies: code context for suggestions
  CS_StoreDeps-->|"called from"|CS_ScanDeps

  %% 10. GraphQL Layer: Entry points to domain behaviors
  GQL_MutationAction-->|"calls"|CS_TaskFactory
  GQL_MutationAgentCreate-->|"creates"|CS_AgentVersion
  GQL_MutationAgentUpdate-->|"updates"|CS_AgentVersion
  GQL_MutationAgentDestroy-->|"removes"|CS_AgentVersion
  GQL_CodeSuggestionsAccessResolver-->|"uses"|AI_UserAuth
  GQL_UserContextsResolver-->|"uses"|AI_UserAuth
  GQL_CodeSuggestionEventType-->|"exposes"|CS_CodeSuggestionEvent

  %% 11. LLM Services
  CS_Client-->|"invokes"|LLM_ServiceCompletion
  LLM_ServiceCompletion-->|"triggers"|LLM_CompletionService
  LLM_CompletionService-->|"invokes"|LLM_ExecMethodService
  LLM_ExecMethodService-->|"initiates"|LLM_CompletionsFactory

  LLM_CompletionsFactory-->|"selects service"|LLM_ServiceCompletion
  LLM_CompletionsFactory-->|"selects service"|LLM_DescriptionService
  LLM_CompletionsFactory-->|"selects service"|LLM_ReviewService
  LLM_CompletionsFactory-->|"selects service"|LLM_GenerateCommitMsgService
  LLM_CompletionsFactory-->|"selects service"|LLM_ExplainCodeService
  LLM_CompletionsFactory-->|"selects service"|LLM_GitCommandService
  LLM_CompletionsFactory-->|"uses strategies"|LLM_UtilsAIFeats

  %% 12. LLM Infra Response modification, context, parsing
  LLM_CompletionService-->|"uses"|LLM_ChainResponseMod
  LLM_CompletionService-->|"uses"|LLM_ChainPlainResp
  LLM_CompletionService-->|"streams via"|LLM_ChainStreamedAnswer
  LLM_CompletionService-->|"streams doc via"|LLM_ChainStreamedDocAnswer
  LLM_CompletionService-->|"parses via"|LLM_ChainOutputParser
  LLM_CompletionService-->|"formats via"|LLM_AFmt
  LLM_CompletionService-->|"holds context"|LLM_ChainGitlabCtx
  LLM_ChainGitlabCtx-->|"extends"|LLM_ChainXrayContext
  LLM_ChainGitlabCtx-->|"uses job logs"|LLM_ChainJobLog
  LLM_CompletionService-->|"modifies response"|LLM_ResponseModTool

  %% 13. Data Structures: Messaging and context structures underpin data flows
  LLM_ChainGitlabCtx-->|"serializes"|LLM_AiMsgContext
  LLM_AiMsgContext-->|"holds extra"|LLM_AiMsgAdditionalContext
  LLM_AiMsgAdditionalContext-->|"items as"|LLM_AiMsgAdditionalContextItem

  %% 14. Code Suggestion Helper Utilities
  CS_CodeSuggestionsHelper-->|"provides url/util"|AI_UserAuth
  CS_IdeHelper-->|"checks feature"|AI_UserAuth

  %% 15. AIResource Wrappers Epics, Issues, Builds
  AIR_Wrapper-->|"wraps"|AIR_Issue
  AIR_Wrapper-->|"wraps"|AIR_Epic
  AIR_Wrapper-->|"wraps"|AIR_CiBuild
  AIR_Issue-->|"includes"|AIR_Noteable
  AIR_Epic-->|"includes"|AIR_Noteable
  AIR_CiBuild-->|"includes"|AIR_Noteable

  %% 16. External Cloud: Model/License/End-to-End checks
  Cloud_ModelConfigProbe-->|"verifies config"|Cloud_CodeSuggestionsLicenseProbe
  Cloud_AmazonQProbe-->|"uses id payload"|Ai_AmazonQ_Identity
  Cloud_EndToEndProbe-->|"verifies license/use"|Cloud_CodeSuggestionsLicenseProbe

  %% 17. Amazon Q and Integration points
  Ai_AmazonQ_Commands-->|"provides subcmds"|Ai_AmazonQ_Identity

  %% 18. User Authorization & LLM feature flagging
  LLM_TanukiBot-->|"authorizes"|AI_UserAuth

  %% 19. Prompt Templates: used as building blocks by LLM services and prompts
  TMPL_SummarizeReview-->|"used by"|LLM_ReviewService
  TMPL_GenDescription-->|"used by"|LLM_DescriptionService
  TMPL_GenCommitMessage-->|"used by"|LLM_GenerateCommitMsgService
  TMPL_CategorizeQ-->|"used by"|LLM_ExecMethodService

  %% 20. Tests validate prompt/task/suggestion logic
  Sp_CodeGenGatewayMsgSpec-->|"tests"|CS_PromptsAiGateway
  Sp_TasksBaseSpec-->|"tests"|CS_TasksBase
  Sp_SlashCommandToolSpec-->|"tests"|LLM_ChainGitlabCtx
  Sp_ResolveVulnSpec-->|"tests"|LLM_ResponseModResVuln

%% Styling - shapes, color classes
classDef core fill:#D4F1F9,color:#222,stroke:#919ea7,stroke-width:2,rx:14,stroke-dasharray:2 2
classDef support fill:#FFF8DC,color:#444,stroke:#B8B892,stroke-width:1.5,rx:13
classDef supportbg fill:#FFF8DC,stroke:#FFEDAA,stroke-width:2,rx:12
classDef data fill:#E0F8E0,color:#155322,stroke:#7CCF8E,stroke-width:1.5,rx:13
classDef error fill:#FFE4E1,color:#B35854,stroke:#DCA2A0,stroke-width:2,rx:12
classDef init fill:#E6E6FA,color:#4A4498,stroke:#B1ADF3,stroke-width:1.5,rx:13

%% Decorate main nodes with core/data/support classes
class CS_TaskFactory,CS_TasksBase,CS_TasksCodeCompletion,CS_TasksCodeGeneration,LLM_ServiceCompletion,LLM_CompletionService,LLM_ExecMethodService,LLM_DescriptionService,LLM_ReviewService,LLM_DescriptionComposer,LLM_SummarizeNewMRService,LLM_GenerateCommitMsgService,LLM_ExplainCodeService,LLM_GenerateSummaryService,LLM_GitCommandService,LLM_CompletionsFactory,AIR_Issue,AIR_Epic,AIR_CiBuild,AIR_Wrapper,AIR_Noteable,GQL_MutationAction,LLM_TanukiBot core
class CS_Client,CS_ProgrammingLanguage,CS_FileContent,CS_Context,CS_ModelDetailsBase,CS_ModelDetailsCodeCompletion,CS_Instruction,CS_InstructionsExtractor,CS_CodeSuggestionsHelper,CS_IdeHelper,CS_PromptsBase,CS_PromptsAnthropicBase,CS_PromptsAnthropicHaiku,CS_PromptsAnthropicSonnet,CS_PromptsAiGateway,CS_PromptsFireworks,CS_PromptsDefault,CS_PromptsDefaultGen,CS_StoreDeps,CS_ScanDeps,support
class LLM_VAICodeCompletion,LLM_VAIChat,LLM_VAICode,LLM_VAIText,LLM_VAICodeChat,LLM_VAIEmbText,LLM_ChainResponseMod,LLM_ChainStreamedAnswer,LLM_ChainStreamedDocAnswer,LLM_ChainPlainResp,LLM_ChainOutputParser,LLM_ChainGitlabCtx,LLM_ChainXrayContext,LLM_ChainJobLog,LLM_ResponseModTool,LLM_AFmt,LLM_UtilsAIFeats support
class LLM_CompletionService support
class LLM_AiMsgContext,LLM_AiMsgAdditionalContext,LLM_AiMsgAdditionalContextItem data
class CS_CodeSuggestionEvent,CS_AgentVersion,CS_EventsFinder data
class AI_UserAuth data
class Cloud_ModelConfigProbe,Cloud_CodeSuggestionsLicenseProbe,Cloud_AmazonQProbe,Cloud_EndToEndProbe,Ai_AmazonQ_Commands,Ai_AmazonQ_Identity init
class GQL_MutationAgentCreate,GQL_MutationAgentUpdate,GQL_MutationAgentDestroy,GQL_CodeSuggestionsAccessResolver,GQL_UserContextsResolver,GQL_CodeSuggestionEventType,GQL_AdditionalContextType,GQL_SummarizeReviewInput,GQL_GenerateDescriptionInput,GQL_GenerateCubeQueryInput,GQL_EEPipelineType,GQL_AnalyticsMetrics,GQL_AnalyticsNSMetrics support
class TMPL_SummarizeReview,TMPL_GenDescription,TMPL_GenCommitMessage,TMPL_CategorizeQ support
class Sp_CodeGenGatewayMsgSpec,Sp_TasksBaseSpec,Sp_SlashCommandToolSpec,Sp_ResolveVulnSpec error

end
```