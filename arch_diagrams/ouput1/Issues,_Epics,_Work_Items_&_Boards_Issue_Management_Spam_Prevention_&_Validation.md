```mermaid
flowchart TB
  %% Node Styles
  classDef core fill:#D4F1F9,stroke:#84B6F4,stroke-width:2px,stroke-dasharray:1 3,rx:12,ry:12
  classDef support fill:#FFF8DC,stroke:#F7E7AE,stroke-width:2px,stroke-dasharray:1 3,rx:12,ry:12
  classDef data fill:#E0F8E0,stroke:#82C48B,stroke-width:2px,stroke-dasharray:1 3,rx:12,ry:12
  classDef error fill:#FFE4E1,stroke:#F4A29E,stroke-width:2px,stroke-dasharray:1 3,rx:12,ry:12
  classDef init fill:#E6E6FA,stroke:#B39DDB,stroke-width:2px,stroke-dasharray:1 3,rx:12,ry:12
  classDef group fill:#F8F8F8,stroke:#84B6F4,stroke-width:3px,rx:20,ry:20

  %% Main Subgraph: Issue Management Core Concepts
  subgraph Issue Management Core["Issue, Epic, Work Item & Board Management"]
    direction TB
    issFinderParams["issuable_finder/params.rb\nParameter Logic & Filtering"]
    issEscalationAfterUpdate["incident_management/issuable_escalation_statuses/after_update_service.rb\nStatus Update & Escalation Logic"]
    issCallbacksBase["issuable/callbacks/base.rb\nIssuable Callback Base"]
    issCallbacksDesc["issuable/callbacks/description.rb\nIssuable Description Callback"]
    timelogsDelete["timelogs/delete_service.rb\nRemove Timelogs Linked to Issues"]
    issSeverity["issuable_severity.rb\nIssue Severity Model"]
    eeIssImportCsv["ee/issues/import_csv_service.rb\nEnterprise CSV Import Spam-Aware"]
    eeIssDestroy["ee/issuable/destroy_service.rb\nEnterprise Issuable Destroy"]
    class issFinderParams,issEscalationAfterUpdate,issCallbacksBase,issCallbacksDesc,timelogsDelete,issSeverity,eeIssImportCsv,eeIssDestroy core
  end
  class Issue Management Core group

  %% Subgraph: Data Models & Domain Data Structures
  subgraph Domain Data Structures & Models
    direction TB
    abuseReport["abuse_report.rb\nAbuse Report Model"]
    spamLog["spam_log.rb\nSpam Log Model"]
    issSeverity
    class abuseReport,spamLog,issSeverity data
  end
  class Domain Data Structures & Models group

  %% Subgraph: Spam/Abuse Prevention & Validation
  subgraph Spam and Abuse Prevention & Validation
    direction TB
    spamLog
    abusiveTermLength["search/abuse_validators/no_abusive_term_length_validator.rb\nAbusive Search Validator"]
    abuseDetection["search/abuse_detection.rb\nAbusive Search Detection"]
    spammableConcern["concerns/spammable.rb\nSpammable Model Concern"]
    limitableConcern["concerns/limitable.rb\nLimitation Checking"]
    spamLogVerifier["spam_log.rb\nCAPTCHA & Recaptcha Verification"]
    akismetMethods["concerns/akismet_methods.rb\nAkismet Service Integration"]
    spammableActionAkismet["controllers/concerns/spammable_actions/akismet_mark_as_spam_action.rb\nSpam Action Controller"]
    spamActionService["spam/spam_action_service.rb\nPerform Spam Action"]
    spamVerdictService["spam/spam_verdict_service.rb\nVerdict Calculation"]
    eeSpamVerdict["ee/spam/spam_verdict_service.rb\nEE Verdict Logic"]
    spamHamService["spam/ham_service.rb\nMark as Not Spam"]
    spamAkismetService["spam/akismet_service.rb\nAkismet Detection"]
    spamAkismetMarkAsSpam["spam/akismet_mark_as_spam_service.rb\nAkismet MarkAsSpam"]
    spamSpamParams["spam/spam_params.rb\nParameters from Request"]
    spamConcernsResponse["../lib/spam/concerns/has_spam_action_response_fields.rb\nSpam Response Fields"]
    spamCheckResult["../lib/gitlab/spamcheck/result.rb\nSpamCheck Result Representation"]
    spamCheckClient["../lib/gitlab/spamcheck/client.rb\nSpamCheck API Client"]
    class spamLog,abusiveTermLength,abuseDetection,spammableConcern,limitableConcern,spamLogVerifier,akismetMethods,spammableActionAkismet,spamActionService,spamVerdictService,eeSpamVerdict,spamHamService,spamAkismetService,spamAkismetMarkAsSpam,spamSpamParams,spamConcernsResponse,spamCheckResult,spamCheckClient core
  end
  class Spam and Abuse Prevention & Validation group

  %% Subgraph: Abuse Reporting, Event Responses & Helpers
  subgraph Abuse Reporting & Helpers
    direction TB
    abuseReport
    abuseReportEventsHelper["resource_events/abuse_report_events_helper.rb\nAbuse Event Messaging"]
    class abuseReport,abuseReportEventsHelper core
  end
  class Abuse Reporting & Helpers group

  %% Subgraph: CODEOWNERS Validation
  subgraph CODEOWNERS Validation & Owner Checks
    direction TB
    codeOwnersProcess["ee/lib/gitlab/code_owners/owner_validation/process.rb\nCODEOWNERS Reference Validator"]
    codeOwnersEligibleApprovers["ee/lib/gitlab/code_owners/owner_validation/eligible_approvers_filter.rb\nEligible Approvers Filter"]
    codeOwnersAccessibleOwners["ee/lib/gitlab/code_owners/owner_validation/accessible_owners_filter.rb\nAccessible Owners Filter"]
    class codeOwnersProcess,codeOwnersEligibleApprovers,codeOwnersAccessibleOwners core
  end
  class CODEOWNERS Validation & Owner Checks group

  %% Subgraph: Supporting Test Files
  subgraph Test Artifacts & Spec Files
    direction TB
    spammableCaptchaHtmlSpec["controllers/concerns/spammable_actions/captcha_check/html_format_actions_support_spec.rb"]
    spammableCaptchaJsonSpec["controllers/concerns/spammable_actions/captcha_check/json_format_actions_support_spec.rb"]
    dbFixturesSpamLogs["db/fixtures/development/23_spam_logs.rb\nSeed Data for SpamLog"]
    class spammableCaptchaHtmlSpec,spammableCaptchaJsonSpec,dbFixturesSpamLogs support
  end
  class Test Artifacts & Spec Files group

  %% Subgraph: Other Supporting and Utility Files
  subgraph Utilities & Other Support
    direction TB
    gitlabQuickActions["quick_actions/commit_actions.rb\nCommit Quick Actions"]
    eeVulnerabilityPolicy["ee/app/policies/vulnerability_policy.rb"]
    eeEscalationPolicyDestroy["incident_management/escalation_policies/destroy_service.rb\nEscalation Policy Destroy"]
    commitPushCheck["ee/lib/ee/gitlab/checks/push_rules/commit_check.rb\nEE Push Commit Check"]
    emailNotificationSpec["qa/specs/features/browser_ui/2_plan/email/trigger_email_notification_spec.rb\nQA Email Notification Spec"]
    class gitlabQuickActions,eeVulnerabilityPolicy,eeEscalationPolicyDestroy,commitPushCheck,emailNotificationSpec support
  end
  class Utilities & Other Support group

  %% Relationships Within Issue Management
  issFinderParams --> issEscalationAfterUpdate
  issFinderParams --> issCallbacksBase
  issCallbacksBase --> issCallbacksDesc
  issEscalationAfterUpdate --> issSeverity
  issCallbacksDesc --> issSeverity
  issCallbacksBase --> issSeverity
  issFinderParams --> abuseReport
  issFinderParams --> spamLog

  %% Relationships: Data Models & Domain Data Structures
  abuseReport -->|references| spamLog
  abuseReport -->|references| issSeverity
  abuseReport -->|invokes| abuseReportEventsHelper
  spamLog -->|is processed by| spamActionService
  spamLog -->|validated via| spamLogVerifier

  %% Spam and Abuse Prevention Logical Relationships
  spammableConcern -- includes --> limitableConcern
  spammableConcern -->|included by| abuseReport
  spammableConcern -->|included by| spamLog
  spamActionService --> spamVerdictService
  spamActionService --> spamHamService
  spamActionService --> spamAkismetMarkAsSpam
  spamHamService --> spamLog
  spamHamService --> akismetMethods
  spamVerdictService --> akismetMethods
  spamVerdictService --> spamAkismetService
  spamVerdictService --> spamCheckClient
  spamVerdictService --> spamCheckResult
  eeSpamVerdict --> spamVerdictService
  spamAkismetService --> akismetMethods
  spamConcernsResponse -->|used by| spammableActionAkismet
  spammableActionAkismet -->|used by| spamActionService
  spamSpamParams --> spamActionService
  spamCheckClient --> spamCheckResult
  spamCheckClient --> spamVerdictService
  abusiveTermLength --> abuseDetection
  abuseDetection --> issFinderParams
  abuseDetection --> spamLog

  %% Relationships: Integration Between Domains
  issEscalationAfterUpdate --> issCallbacksBase

  %% CODEOWNERS Logical Flow
  codeOwnersProcess --> codeOwnersEligibleApprovers
  codeOwnersProcess --> codeOwnersAccessibleOwners

  %% Test Files (Support Integration)
  spammableCaptchaHtmlSpec --> spammableActionAkismet
  spammableCaptchaJsonSpec --> spammableActionAkismet
  dbFixturesSpamLogs --> spamLog

  %% Utilities & Support
  eeIssDestroy --> issCallbacksBase
  eeEscalationPolicyDestroy --> issEscalationAfterUpdate
  gitlabQuickActions --> issFinderParams
  commitPushCheck --> abuseDetection
  emailNotificationSpec --> issFinderParams
  eeVulnerabilityPolicy --> issSeverity

  %% Extra Cross-Domain Relationships
  eeIssImportCsv --> spamActionService
  eeIssImportCsv --> spamVerdictService

  %% Highlighting Error, Initialization, and Data Structure Nodes
  class abuseReport error
  class spamLog data
  class issSeverity data
  class spamCheckResult data
  class spamSpamParams data
  class dbFixturesSpamLogs init
```