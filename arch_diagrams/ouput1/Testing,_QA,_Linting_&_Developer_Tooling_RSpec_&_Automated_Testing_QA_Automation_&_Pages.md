```mermaid
flowchart TB
  %% Color definitions
  classDef core fill:#D4F1F9,stroke:#A2CFE3,stroke-width:2px,color:#222,stroke-dasharray:0 0,stroke-linejoin:round,rx:10,ry:10
  classDef supporting fill:#FFF8DC,stroke:#FFE9A8,stroke-width:2px,color:#222,stroke-linejoin:round,rx:10,ry:10
  classDef data fill:#E0F8E0,stroke:#B9EABB,stroke-width:2px,color:#222,stroke-linejoin:round,rx:10,ry:10
  classDef error fill:#FFE4E1,stroke:#FFBAB2,stroke-width:2px,color:#222,stroke-linejoin:round,rx:10,ry:10
  classDef init fill:#E6E6FA,stroke:#CBC3E3,stroke-width:2px,color:#222,stroke-linejoin:round,rx:10,ry:10
  classDef grouping fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2.5px,color:#333

  %% ---- QA Automated Testing Domain ----
  subgraph "Testing, QA, Linting & Developer Tooling / RSpec & Automated Testing / QA Automation & Pages"["QA Automated Testing Domain"]
    direction TB
    class "Testing, QA, Linting & Developer Tooling / RSpec & Automated Testing / QA Automation & Pages" grouping

    %% 1. Core RSpec Integration and QA Automation
    subgraph "RSpec Integration"["RSpec Core Test Integration"]
      direction TB
      ee_spec_workers_concerns_search_elastic_index_name["search/elastic/index_name_spec.rb\nTests Elastic search index naming behavior"]:::core
      ee_spec_workers_concerns_worker_session_state_setter["worker_session_state_setter_spec.rb\nVerifies session state setting in worker"]:::core
      ee_spec_workers_concerns_update_orchestration_policy_configuration["update_orchestration_policy_configuration_spec.rb\nTests policy config update workflows"]:::core
      ee_spec_workers_concerns_elastic_bulk_cron_worker["elastic/bulk_cron_worker_spec.rb\nTimed Elastic bulk indexing test"]:::core
      ee_spec_workers_concerns_geo_skip_secondary["geo/skip_secondary_spec.rb\nGeo replication and skipping logic"]:::core
      ee_spec_workers_ai_active_context_bulk_process_worker["ai/active_context/bulk_process_worker_spec.rb\nTests AI context bulk processing queues"]:::core
      ee_spec_workers_system_access_saml_microsoft_group_sync_worker["system_access/saml_microsoft_group_sync_worker_spec.rb\nMicrosoft group sync test"]:::core
      ee_spec_workers_system_access_group_saml_microsoft_group_sync_worker["system_access/group_saml_microsoft_group_sync_worker_spec.rb\nTest for group-level SAML group sync"]:::core
      ee_spec_workers_compliance_management_pipl_send_recurring_notifications_worker["compliance_management/pipl/send_recurring_notifications_worker_spec.rb\nPIPL user notifications test"]:::core
      ee_spec_workers_compliance_management_pipl_block_pipl_users_worker["compliance_management/pipl/block_pipl_users_worker_spec.rb\nPIPL user block logic test"]:::core
      ee_spec_workers_compliance_management_pipl_delete_pipl_users_worker["compliance_management/pipl/delete_pipl_users_worker_spec.rb\nPIPL user delete test"]:::core
      spec_workers_bulk_imports_pipeline_batch_worker["bulk_imports/pipeline_batch_worker_spec.rb\nBulk import: batch pipeline test"]:::core
      spec_workers_bulk_imports_pipeline_worker["bulk_imports/pipeline_worker_spec.rb\nBulk import: single pipeline logic"]:::core
      spec_workers_concerns_cronjob_queue["concerns/cronjob_queue_spec.rb\nCronjob worker context maintenance test"]:::core
      spec_workers_redis_migration_worker["redis_migration_worker_spec.rb\nRedis migration pattern enforcement test"]:::core
      spec_workers_integrations_create_external_cross_reference_worker["integrations/create_external_cross_reference_worker_spec.rb\nTests integration reference sync"]:::core
      spec_lib_gitlab_cluster_mixins_puma_cluster["lib/gitlab/cluster/mixins/puma_cluster_spec.rb\nTest Puma clustering orchestration"]:::core
      spec_workers_bulk_imports_pipeline_batch_worker --> spec_workers_bulk_imports_pipeline_worker
      ee_spec_workers_concerns_worker_session_state_setter --> ee_spec_workers_system_access_saml_microsoft_group_sync_worker
      ee_spec_workers_concerns_worker_session_state_setter --> ee_spec_workers_system_access_group_saml_microsoft_group_sync_worker
      ee_spec_workers_concerns_elastic_bulk_cron_worker --> ee_spec_workers_concerns_search_elastic_index_name
      ee_spec_workers_concerns_search_elastic_index_name --"validates usage of Search::Elastic::IndexName"--> ee_spec_workers_concerns_elastic_bulk_cron_worker
      spec_workers_concerns_cronjob_queue --"Enforces context among jobs"--> spec_workers_bulk_imports_pipeline_batch_worker
      spec_workers_bulk_imports_pipeline_worker --"Handles pipeline relation"--> spec_workers_bulk_imports_pipeline_batch_worker
      ee_spec_workers_compliance_management_pipl_send_recurring_notifications_worker -.-> ee_spec_workers_compliance_management_pipl_block_pipl_users_worker
      ee_spec_workers_compliance_management_pipl_send_recurring_notifications_worker -.-> ee_spec_workers_compliance_management_pipl_delete_pipl_users_worker

    end

    %% 2. QA Feature Pages: Abstractions of Product Areas for UI Automation READABLE/Fine-grained Pages Layer
    subgraph "QA Page Classes"["QA Feature Page Object Model"]
      direction TB

      qa_page_base["qa/page/base.rb\nCore page abstraction: element interaction, navigation, generic UI logic"]:::core
      qa_page_validatable["qa/page/validatable.rb\nBase for page element validation"]:::core

      subgraph "QA::Page::Admin"["Admin Area Pages"]
        direction TB
        qa_page_admin_sidebar_overview["admin/sidebar/overview.rb\nNav. logic for users/groups overview"]:::core
        qa_page_admin_sidebar_settings["admin/sidebar/settings.rb\nNavigation picker for admin settings"]:::core
        qa_page_admin_settings_general["admin/settings/general.rb\nInteract with admin general settings page"]:::core
        qa_page_admin_settings_network["admin/settings/network.rb\nNetwork settings expanders etc"]:::core
        qa_page_admin_settings_metrics["admin/settings/metrics_and_profiling.rb\nMetrics & Profiling setting expanders"]:::core
        qa_page_admin_new_session["admin/new_session.rb\nAdmin sign-in screen interactions"]:::core
        qa_page_admin_overview_users_show["admin/overview/users/show.rb\nNavigate/impersonate user as admin"]:::core
        qa_page_admin_overview_users_components_impersonation_tokens["admin/overview/users/components/impersonation_tokens.rb\nManages user impersonation tokens"]:::core
        qa_page_admin_overview_groups_show["admin/overview/groups/show.rb\nAdmin group dashboard, edit nav"]:::core
        qa_page_admin_overview_groups_edit["admin/overview/groups/edit.rb\nAdmin group edit interface"]:::core
        qa_page_admin_applications["admin/applications.rb\nAdmin application creation screen"]:::core

        qa_page_admin_sidebar_overview --> qa_page_admin_overview_users_show
        qa_page_admin_overview_users_show --> qa_page_admin_overview_users_components_impersonation_tokens
        qa_page_admin_sidebar_overview --> qa_page_admin_overview_groups_show
        qa_page_admin_overview_groups_show --> qa_page_admin_overview_groups_edit
        qa_page_admin_sidebar_settings --> qa_page_admin_settings_general
        qa_page_admin_sidebar_settings --> qa_page_admin_settings_network
        qa_page_admin_sidebar_settings --> qa_page_admin_settings_metrics
        qa_page_admin_sidebar_settings --> qa_page_admin_applications

      end

      subgraph "QA::Page::Project::Settings"["Project Settings Pages"]
        direction TB
        qa_page_project_settings_main["project/settings/main.rb\nMain project settings"]:::core
        qa_page_project_settings_advanced["project/settings/advanced.rb\nAdv./danger settings: transfer, delete, unarchive"]:::core
        qa_page_project_settings_auto_devops["project/settings/auto_devops.rb\nToggle Auto DevOps options"]:::core
        qa_page_project_settings_branch_rules["project/settings/branch_rules.rb\nBranch protection navigation"]:::core
        qa_page_project_settings_branch_rules_details["project/settings/branch_rules_details.rb\nView details of branch rules"]:::core
        qa_page_project_settings_ci_cd["project/settings/ci_cd.rb\nExpand runners, CI/CD variables"]:::core
        qa_page_project_settings_monitor["project/settings/monitor.rb\nIncident/Alert settings & expansion"]:::core
        qa_page_project_settings_alerts["project/settings/alerts.rb\nConfigure project alerting"]:::core
        qa_page_project_settings_services_pipeline_status_emails["project/settings/services/pipeline_status_emails.rb"]:::core
        qa_page_project_settings_services_jira["project/settings/services/jira.rb"]:::core

        qa_page_project_settings_main --> qa_page_project_settings_advanced
        qa_page_project_settings_main --> qa_page_project_settings_auto_devops
        qa_page_project_settings_main --> qa_page_project_settings_branch_rules
        qa_page_project_settings_branch_rules --> qa_page_project_settings_branch_rules_details
        qa_page_project_settings_main --> qa_page_project_settings_ci_cd
        qa_page_project_settings_main --> qa_page_project_settings_monitor
        qa_page_project_settings_monitor --> qa_page_project_settings_alerts
        qa_page_project_settings_services_pipeline_status_emails --> qa_page_project_settings_services_jira

      end

      subgraph "QA::Page::Group::Settings"["Group Settings Pages"]
        direction TB
        qa_page_group_settings_repository["group/settings/repository.rb\nExpand deploy tokens for group"]:::core

      end

      subgraph "QA::Page::Project::WorkItem"["Project Work Item Pages"]
        direction TB
        qa_page_project_work_item_new["project/work_item/new.rb\nCreate work item UI"]:::core
        qa_page_project_work_item_show["project/work_item/show.rb\nShow/edit work item UI"]:::core

      end

      subgraph "QA::Page::Project::SubMenus"["Project Navigation Submenus"]
        direction TB
        qa_page_project_sub_menus_settings["project/sub_menus/settings.rb\nAttach settings submenu methods"]:::core
        qa_page_project_sub_menus_create_new_menu["project/sub_menus/create_new_menu.rb\nNavigation to 'create new' items"]:::core

      end

      subgraph "QA::Page::SubMenus"["Generic Navigation Submenus"]
        direction TB
        qa_page_sub_menus_settings["sub_menus/settings.rb\nGeneric settings navigation"]:::core
        qa_page_sub_menus_common["sub_menus/common.rb\nCommon submenu, sidebar helpers"]:::core
        qa_page_sub_menus_main["sub_menus/main.rb\nMain nav logic: issues/MRs etc."]:::core

      end

      subgraph "QA::Page::Settings"["Settings Component"]
        direction TB
        qa_page_settings_common["settings/common.rb\nButton expand/collapse logic shared throughout pages"]:::core

      end

      qa_page_base --> qa_page_settings_common
      qa_page_validatable --> qa_page_base
      qa_page_project_settings_main --> qa_page_settings_common
      qa_page_group_settings_repository --> qa_page_settings_common
      qa_page_admin_settings_general --> qa_page_settings_common
      qa_page_admin_settings_network --> qa_page_settings_common
      qa_page_admin_settings_metrics --> qa_page_settings_common
      qa_page_project_settings_auto_devops --> qa_page_settings_common
      qa_page_project_settings_ci_cd --> qa_page_settings_common
      qa_page_project_settings_monitor --> qa_page_settings_common
      qa_page_sub_menus_settings --> qa_page_settings_common
      qa_page_project_sub_menus_settings --> qa_page_sub_menus_settings
      qa_page_project_sub_menus_create_new_menu --> qa_page_project_sub_menus_settings

    end

    %% 3. QA::EE Page Extensions (Enterprise/Feature overlays)
    subgraph "QA EE Page Extensions"["QA::EE Feature Extension Pages"]
      direction TB
      
      qa_ee_page_workspace_new["ee/page/workspace/new.rb\nEE workspace creation page"]:::core
      qa_ee_page_trials_select["ee/page/trials/select.rb\nEE trials welcome/select page"]:::core
      qa_ee_page_registration_welcome["ee/page/registration/welcome.rb\nEE registration intro pages"]:::core
      qa_ee_page_project_work_item_index["ee/page/project/work_item/index.rb\nEE: Work Item list enhancements"]:::core
      qa_ee_page_project_value_stream_analytics["ee/page/project/value_stream_analytics.rb\nEE: Value Stream Analytics"]:::core
      qa_ee_page_project_policies_policy_list["ee/page/project/policies/policy_list.rb\nEE: Security/Policy listing"]:::core
      qa_ee_page_project_path_locks_index["ee/page/project/path_locks/index.rb\nEE: Manage file path locks"]:::core
      qa_ee_page_project_menu["ee/page/project/menu.rb\nEE: Adds secure/code/analyze nav"]:::core
      qa_ee_page_project_job_show["ee/page/project/job/show.rb\nEE: Job Root Cause Analysis"]:::core
      qa_ee_page_project_analyze_visualization_setup["ee/page/project/analyze/visualization_setup.rb\nEE: Analytics visualizations"]:::core
      qa_ee_page_project_analyze_dashboard_setup["ee/page/project/analyze/dashboard_setup.rb\nEE: Analytics dashboard editor"]:::core
      qa_ee_page_project_secure_vulnerability_security_training["ee/page/project/secure/vulnerability_security_training.rb\nEE: Security training toggles"]:::core
      qa_ee_page_project_secure_show["ee/page/project/secure/show.rb\nEE: Security/Vuln. reports"]:::core
      qa_ee_page_project_secure_security_dashboard["ee/page/project/secure/security_dashboard.rb\nEE: Security dashboard UI"]:::core
      qa_ee_page_project_secure_dependency_list["ee/page/project/secure/dependency_list.rb\nEE: Dependency list UI"]:::core
      qa_ee_page_project_settings_pipeline_subscriptions["ee/page/project/settings/pipeline_subscriptions.rb\nEE: Pipeline subscriptions page"]:::core
      qa_ee_page_project_settings_merge_request_approvals["ee/page/project/settings/merge_request_approvals.rb\nEE: MR approval settings"]:::core
      qa_ee_page_project_settings_main["ee/page/project/settings/main.rb\nEE: Main settings, overlays FOSS"]:::core
      qa_ee_page_project_settings_integrations["ee/page/project/settings/integrations.rb\nEE: integration settings"]:::core
      qa_ee_page_project_settings_analytics["ee/page/project/settings/analytics.rb\nEE: Enhanced analytics, dashboards"]:::core
      qa_ee_page_project_settings_issue_template_default["ee/page/project/settings/issue_template_default.rb\nEE: Set default issue template"]:::core

      qa_ee_page_project_menu --> qa_ee_page_project_secure_show
      qa_ee_page_project_menu --> qa_ee_page_project_analyze_dashboard_setup
      qa_ee_page_project_secure_show --> qa_ee_page_project_secure_security_dashboard
      qa_ee_page_project_secure_show --> qa_ee_page_project_secure_vulnerability_security_training
      qa_ee_page_project_secure_show --> qa_ee_page_project_secure_dependency_list
      qa_ee_page_project_settings_main --> qa_ee_page_project_settings_pipeline_subscriptions
      qa_ee_page_project_settings_main --> qa_ee_page_project_settings_merge_request_approvals
      qa_ee_page_project_settings_main --> qa_ee_page_project_settings_integrations
      qa_ee_page_project_settings_main --> qa_ee_page_project_settings_analytics
      qa_ee_page_project_value_stream_analytics --> qa_ee_page_project_settings_analytics

    end

    %% 4. Flaky Test Detection, QA Parallelization, and Knapsack Reporting
    subgraph "Advanced Tooling, Flaky Test & Parallelization"["Flaky Test Detection, Parallel Execution, Reporting"]
      direction TB

      %% Flaky Test Data Structures and Transformations
      subgraph "Flaky Test Suite Core"["Flaky Test Detection"]
        direction TB
        rspec_flaky_main["lib/gitlab/rspec_flaky.rb\nRSpec flaky test lib entrypoint"]:::core
        rspec_flaky_report["lib/gitlab/rspec_flaky/report.rb\nHandles JSON report: load, write, prune"]:::data
        rspec_flaky_flaky_example["lib/gitlab/rspec_flaky/flaky_example.rb\nData for single flaky spec"]:::data
        rspec_flaky_flaky_examples_collection["lib/gitlab/rspec_flaky/flaky_examples_collection.rb\nData for collection of flaky specs"]:::data
        rspec_flaky_config["lib/gitlab/rspec_flaky/config.rb\nLoads paths and settings for reporting"]:::supporting
        rspec_flaky_version["lib/gitlab/rspec_flaky/version.rb\nLibrary version"]:::supporting

        rspec_flaky_main --> rspec_flaky_report
        rspec_flaky_report --> rspec_flaky_flaky_examples_collection
        rspec_flaky_flaky_examples_collection --> rspec_flaky_flaky_example
        rspec_flaky_report --> rspec_flaky_config
        rspec_flaky_main --> rspec_flaky_version
      end

      subgraph "QA::Specs Parallelization & Knapsack Integration"["Parallelization & Reporting"]
        direction TB
        qa_specs_parallel_runner["specs/parallel_runner.rb\nParallel running strategy logic"]:::core
        qa_specs_parallel_runner_spec["specs/parallel_runner_spec.rb\nTests parallel runner logic"]:::core
        qa_support_knapsack_report["spec/support/knapsack_report_spec.rb\nTests for knapsack report logic"]:::core

        qa_page_base -.-> qa_specs_parallel_runner
        qa_specs_parallel_runner_spec --"validates"--> qa_specs_parallel_runner
        qa_support_knapsack_report --"verifies reporting"--> qa_specs_parallel_runner

      end

    end

    %% 5. Contract/Provider Test Integration
    subgraph "Pact Contracts/Provider Verification"["Pact Contract Testing Integration"]
      direction TB
      pact_helpers_edit_pipeline_schedule["contracts/provider/pact_helpers/project/pipeline_schedules/edit/put_edit_a_pipeline_schedule_helper.rb\nPUT edit pipeline schedule"]:::core
      pact_helpers_create_new_pipeline["contracts/provider/pact_helpers/project/pipelines/new/post_create_a_new_pipeline_helper.rb\nPOST create a new pipeline"]:::core
      pact_helpers_get_diffs_batch["contracts/provider/pact_helpers/project/merge_requests/show/get_diffs_batch_helper.rb\nGET batch diffs for MR"]:::core
      pact_helpers_get_discussions["contracts/provider/pact_helpers/project/merge_requests/show/get_discussions_helper.rb\nGET discussions for MR"]:::core
      pact_helpers_get_diffs_metadata["contracts/provider/pact_helpers/project/merge_requests/show/get_diffs_metadata_helper.rb\nGET diffs metadata for MR"]:::core
      pact_helpers_delete_pipeline["contracts/provider/pact_helpers/project/pipelines/show/delete_pipeline_helper.rb\nDELETE pipeline"]:::core
      pact_helpers_get_pipeline_header_data["contracts/provider/pact_helpers/project/pipelines/show/get_pipeline_header_data_helper.rb\nGET pipeline header"]:::core
      pact_helpers_get_list_project_pipelines["contracts/provider/pact_helpers/project/pipelines/index/get_list_project_pipelines_helper.rb\nGET list project pipelines"]:::core

      pact_helpers_edit_pipeline_schedule --> pact_helpers_get_list_project_pipelines
      pact_helpers_get_diffs_batch --> pact_helpers_get_diffs_metadata
      pact_helpers_get_discussions --> pact_helpers_get_diffs_metadata
      pact_helpers_delete_pipeline --> pact_helpers_get_pipeline_header_data

    end

    %% 6. Miscellaneous/Support/Domain-Driven Specs
    subgraph "Test Support, Docs, Keeps, Quarantine & Screenshots"["Cross-domain Support & Specialized QA Modules"]
      direction TB
      qa_specs_helpers_quarantine["specs/helpers/quarantine.rb\nSupport for marking/running quarantined tests"]:::supporting
      qa_docs_screenshots_container_registry["docs_screenshots/container_registry_docs.rb\nDoc screenshot generation for Container Registry"]:::supporting
      qa_keeps_prompts_remove_feature_flags["keeps/prompts/remove_feature_flags_spec.rb\nEnsures feature flag removals"]:::supporting

      qa_specs_helpers_quarantine --> qa_docs_screenshots_container_registry
    end

    %% 7. Example QA Feature Specs
    subgraph "QA Feature Specs"["UI Flow Feature Test Specs"]
      direction TB
      qa_specs_features_create_project_snippet_multiple["specs/features/browser_ui/3_create/snippet/create_project_snippet_with_multiple_files_spec.rb\nMulti-file snippet creation test"]:::core
      qa_specs_features_ee_create_repository_file_locking["specs/features/ee/browser_ui/3_create/repository/file_locking_spec.rb\nFile locking feature test"]:::core
      qa_specs_features_ee_create_repository_push_rules["specs/features/ee/browser_ui/3_create/repository/push_rules_spec.rb\nPush rules/branch control test"]:::core
      qa_specs_features_verify_ci_project_artifacts_bulk_delete["specs/features/browser_ui/4_verify/ci_project_artifacts/user_can_bulk_delete_artifacts_spec.rb\nArtifacts bulk delete test"]:::core

      qa_specs_features_create_project_snippet_multiple --> qa_page_project_work_item_new
      qa_specs_features_ee_create_repository_file_locking --> qa_ee_page_project_path_locks_index
      qa_specs_features_ee_create_repository_push_rules --> qa_page_project_settings_branch_rules
      qa_specs_features_verify_ci_project_artifacts_bulk_delete --> qa_page_project_settings_ci_cd
    end

    %% 8. Browser/Script Extension Tooling
    subgraph "Script Extension Support"["Script/Browser Extension Tooling"]
      direction TB
      qa_runtime_script_extensions_interceptor["runtime/script_extensions/interceptor_spec.rb\nTest browser JS interception"]:::supporting
      qa_runtime_script_extensions_interceptor --> qa_page_base
    end

    %% 9. Gems/GitLab Orchestrator & Helpers
    subgraph "Orchestration Helpers"["Orchestration: Progress/Output"]
      direction TB
      gitlab_orchestrator_helpers_spinner["gems/gitlab-orchestrator/lib/gitlab/orchestrator/lib/helpers/spinner.rb\nProgress spinner helper"]:::supporting
      gitlab_orchestrator_helpers_spinner -.-> qa_page_base
    end

  end
```