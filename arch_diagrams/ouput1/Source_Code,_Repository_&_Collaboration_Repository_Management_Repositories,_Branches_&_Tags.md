```mermaid
flowchart TD
  %%================== GROUPS (SUBGRAPHS) DEFINITIONS ===========================

  %% Subgraph: Core Domain Models & Behaviors
  subgraph CORE_DOMAIN_MODELS["Core Domain: Repositories, Branches & Tags"]
    direction TB
    style CORE_DOMAIN_MODELS fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded-rect

    %% Repositories and branches/tags core models pastel blue
    PRJRB["app/models/project.rb"]:::core_file
    PROJREPO["app/models/project_repository.rb"]:::core_file
    HASREPO["app/models/concerns/has_repository.rb"]:::core_file
    POOLREPO["app/models/pool_repository.rb"]:::core_file
    FRKNET["app/models/fork_network.rb"]:::core_file
    FRKNETMEM["app/models/fork_network_member.rb"]:::core_file
    PAGEDEP["app/models/pages_deployment.rb"]:::core_file
    REPO_LANG["app/models/repository_language.rb"]:::core_file
    PROGLANG["app/models/programming_language.rb"]:::core_file

    %% Branch/Tag/Dropdown Protection
    PROTDD["app/models/protectable_dropdown.rb"]:::core_file

    %% Key/DeployKey/Authorization
    KEY["app/models/key.rb"]:::core_file
    DEPLOYKEY["app/models/deploy_key.rb"]:::core_file

    %% Repository Data Structures
    REDRTR["app/models/redirect_route.rb"]:::core_file
    PUSH_EVT["app/models/push_event_payload.rb"]:::core_file
    RELHLT["app/models/release_highlight.rb"]:::core_file

  end

  %% Subgraph: Finders
  subgraph FINDERS["Finders: Querying Branches, Tags, Repositories"]
    direction TB
    style FINDERS fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded-rect

    TREEFND["app/finders/repositories/tree_finder.rb"]:::support_file
    BRANCHFND["app/finders/repositories/branch_names_finder.rb"]:::support_file
    TAGFND["app/finders/ci/tags_finder.rb"]:::support_file
    SRPRJBRCH["app/finders/starred_projects_finder.rb"]:::support_file
    USRSTRFND["app/finders/users_star_projects_finder.rb"]:::support_file
    CHANGELOGTAG["app/finders/repositories/changelog_tag_finder.rb"]:::support_file
    TAGSFND["app/finders/tags_finder.rb"]:::support_file
    BRNSFND["app/finders/branches_finder.rb"]:::support_file
    GITREFSFND["app/finders/git_refs_finder.rb"]:::support_file
    CHANGELOGCOMMITS["app/finders/repositories/changelog_commits_finder.rb"]:::support_file
    FORKFND["app/finders/fork_projects_finder.rb"]:::support_file
  end

  %% Subgraph: Services - Branches/Tags/Repositories Management
  subgraph SERVICES["Domain Services"]
    direction TB
    style SERVICES fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded-rect

    %% Branch management
    BRNCH_CREATE["app/services/branches/create_service.rb"]:::core_file
    BRNCH_DELETE["app/services/branches/delete_service.rb"]:::core_file
    BRNCH_DELETEMRG["app/services/branches/delete_merged_service.rb"]:::core_file
    BRNCH_VALID["app/services/branches/validate_new_service.rb"]:::core_file
    BRNCH_DIVCOMM["app/services/branches/diverging_commit_counts_service.rb"]:::core_file

    %% Tag/Release management
    TAGS_CREATE["app/services/tags/create_service.rb"]:::core_file

    %% Repository services
    REPOS_BASE["app/services/repositories/base_service.rb"]:::core_file
    REPOS_DESTROY["app/services/repositories/destroy_service.rb"]:::core_file
    REPOS_HK["app/services/repositories/housekeeping_service.rb"]:::core_file
    REPOS_REWRITE["app/services/repositories/rewrite_history_service.rb"]:::core_file

    %% Project/Repo language
    DETECT_LANG["app/services/projects/detect_repository_languages_service.rb"]:::core_file
    REPO_LANG_SVC["app/services/projects/repository_languages_service.rb"]:::core_file

    %% Forks/syncs
    FRKSYNC["app/services/projects/forks/sync_service.rb"]:::core_file

    %% Git operations
    GIT_TAG_PUSH["app/services/git/tag_push_service.rb"]:::core_file
    GIT_BRANCH_PUSH["app/services/git/branch_push_service.rb"]:::core_file
    GIT_BRANCH_HOOKS["app/services/git/branch_hooks_service.rb"]:::core_file

    %% Misc
    GIT_DEDUP["app/services/projects/git_deduplication_service.rb"]:::core_file
    READMERNDER["app/services/projects/readme_renderer_service.rb"]:::core_file
    UPD_REM_MIR["app/services/projects/update_remote_mirror_service.rb"]:::core_file
    UPD_REPO_STOR["app/services/projects/update_repository_storage_service.rb"]:::core_file
    AFTER_RENAME["app/services/projects/after_rename_service.rb"]:::core_file
    BRANCHES_BY_MODE["app/services/projects/branches_by_mode_service.rb"]:::core_file
  end

  %% Subgraph: API Interfaces
  subgraph API["API: REST Endpoints"]
    direction TB
    style API fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded-rect

    API_BRANCHES["lib/api/branches.rb"]:::support_file
    API_TAGS["lib/api/tags.rb"]:::support_file
    API_REPOS["lib/api/repositories.rb"]:::support_file
    API_DEPLOYKEYS["lib/api/deploy_keys.rb"]:::support_file

    API_ENT_TAG["lib/api/entities/tag.rb"]:::data_file
    API_ENT_DIFFREFS["lib/api/entities/diff_refs.rb"]:::data_file
    API_ENT_REPOSTOR["lib/api/entities/project_repository_storage.rb"]:::data_file
    API_ENT_BASICSTOR["lib/api/entities/basic_repository_storage_move.rb"]:::data_file
  end

  %% Subgraph: GitLab Git Abstractions
  subgraph GITLAB_LIB["GitLab Git Abstractions & Internals"]
    direction TB
    style GITLAB_LIB fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded-rect

    GITLABGIT["lib/gitlab/git.rb"]:::core_file
    GITLABGITREPO["lib/gitlab/git/repository.rb"]:::core_file
    GITLABGITBRANCH["lib/gitlab/git/branch.rb"]:::core_file
    GITLABGITTAG["lib/gitlab/git/tag.rb"]:::core_file
    GITLABGITREF["lib/gitlab/git/ref.rb"]:::core_file
    GITLABGITTREE["lib/gitlab/git/tree.rb"]:::core_file
    GITLABGITRPOTYPE["lib/gitlab/repositories/repo_type.rb"]:::core_file
    GITLABREPOS["lib/gitlab/repositories.rb"]:::core_file
    GITLABREPOSPROJ["lib/gitlab/repositories/project_repository.rb"]:::core_file
    GITLABREPOSWIKI["lib/gitlab/repositories/wiki_repository.rb"]:::core_file
    GITLABGITFINDREFS["lib/gitlab/git/finders/refs_finder.rb"]:::support_file
    GITLABREPOSIDENT["lib/gitlab/repositories/identifier.rb"]:::support_file
    GITLABGITPUSH["lib/gitlab/git/push.rb"]:::support_file
    GITLABGITCHANGE["lib/gitlab/git/changes.rb"]:::support_file
    GITLABGITPATH["lib/gitlab/git/path_helper.rb"]:::support_file
    GITLABGITOBJECTPOOL["lib/gitlab/git/object_pool.rb"]:::support_file
    GITLABGITSIGNEDTAG["lib/gitlab/signed_tag.rb"]:::support_file
    GITLABLANGDET["lib/gitlab/language_detection.rb"]:::support_file
    GITLABCHECKCHANGES["lib/gitlab/checks/changes_access.rb"]:::support_file
    GITLABSHELLADAPT["lib/gitlab/shell_adapter.rb"]:::support_file
    GITLABGLREPO["lib/gitlab/gl_repository.rb"]:::core_file
    GITLABGLID["lib/gitlab/gl_id.rb"]:::support_file
    GITLABREPOCACHE["lib/gitlab/repository_cache.rb"]:::support_file
    GITLABREPOCACHEPLR["lib/gitlab/repository_cache/preloader.rb"]:::support_file
    GITLABREPOPATH["lib/gitlab/repo_path.rb"]:::support_file
    GITLABDEFAULTBR["lib/gitlab/default_branch.rb"]:::support_file
  end

  %% Subgraph: Controller Layer
  subgraph CONTROLLERS["Controllers: HTTP, Web, Git Access"]
    direction TB
    style CONTROLLERS fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded-rect

    REPO_APPCTRL["app/controllers/repositories/application_controller.rb"]:::core_file
    REPO_HTTPCL["app/controllers/repositories/git_http_client_controller.rb"]:::core_file
    REPO_HTTP["app/controllers/repositories/git_http_controller.rb"]:::core_file
    PRJ_BRANCHCTRL["app/controllers/projects/branches_controller.rb"]:::core_file
    PRJ_TAGCTRL["app/controllers/projects/tags_controller.rb"]:::core_file
    PRJ_REPOCTRL["app/controllers/projects/repositories_controller.rb"]:::core_file
    PRJ_TREECTRL["app/controllers/projects/tree_controller.rb"]:::core_file
    PRJ_REFSCTRL["app/controllers/projects/refs_controller.rb"]:::core_file
    PRJ_CHNGCTRL["app/controllers/explore/projects_controller.rb"]:::support_file
    PRJ_DBRDPRJ["app/controllers/dashboard/projects_controller.rb"]:::support_file
    PRJ_FORKCTRL["app/controllers/projects/forks_controller.rb"]:::core_file

  end

  %% Subgraph: Workers and Task Scheduling
  subgraph WORKERS["Workers: Background Jobs & Syncs"]
    direction TB
    style WORKERS fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded-rect
    REPO_FORK_WORKER["app/workers/repository_fork_worker.rb"]:::support_file
    REPO_CLEANUP["app/workers/repository_cleanup_worker.rb"]:::support_file
    REPO_CHECK_DISP["app/workers/repository_check/dispatch_worker.rb"]:::support_file
    REPO_CHECK_SINGLE["app/workers/repository_check/single_repository_worker.rb"]:::support_file
    PROJ_FORKS_SYNC["app/workers/projects/forks/sync_worker.rb"]:::support_file
    REPOS_REWRITE_WORKER["app/workers/repositories/rewrite_history_worker.rb"]:::support_file
    BATCHED_CLEANUP_WKR["app/workers/batched_git_ref_updates/project_cleanup_worker.rb"]:::support_file
    BATCHED_CLEANUP_SCHED["app/workers/batched_git_ref_updates/cleanup_scheduler_worker.rb"]:::support_file
  end

  %% Subgraph: Utilities & Helpers
  subgraph UTILS["Utilities & Helpers"]
    direction TB
    style UTILS fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,rounded-rect

    PROTREFSHELPER["app/helpers/protected_refs_helper.rb"]:::support_file
    REPO_LANG_HELP["app/helpers/repository_languages_helper.rb"]:::support_file
    ROUTING_HELP["app/helpers/routing/projects_helper.rb"]:::support_file
  end

  %% Subgraph: Events & Domain Events
  subgraph EVENTS["Domain Events"]
    direction TB
    style EVENTS fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2,rounded-rect
    DEFBRCH_EVT["app/events/repositories/default_branch_changed_event.rb"]:::support_file
  end

  %%================== STYLES =================================

  classDef core_file fill:#D4F1F9,stroke:#B7DDE9,stroke-width:2,stroke-dasharray:0;
  classDef support_file fill:#FFF8DC,stroke:#e7d799,stroke-width:2,stroke-dasharray:0;
  classDef data_file fill:#E0F8E0,stroke:#bcdcb6,stroke-width:2,stroke-dasharray:0;
  classDef error_file fill:#FFE4E1,stroke:#ffb3ab,stroke-width:2,stroke-dasharray:0;
  classDef init_file fill:#E6E6FA,stroke:#bcbddc,stroke-width:2,stroke-dasharray:0;


  %%================== LOGICAL RELATIONSHIPS, COLLABORATION, AND FLOW ===========

  %% Core domain structure, project/repo -> repository -> underlying data
  PRJRB -- has many --> PROJREPO
  PRJRB -- uses --> HASREPO
  PRJRB -- fork networks --> FRKNET
  FRKNET -- contains --> FRKNETMEM
  PROJREPO -- uses --> POOLREPO
  PRJRB -- manages --> REPO_LANG
  PRJRB -- relates --> PROGLANG
  PRJRB -- uses --> PAGEDEP
  PRJRB -- has --> REDRTR
  PRJRB -- triggers --> RELHLT

  PRJRB -- authorizes with --> KEY
  KEY -- includes --> DEPLOYKEY

  %% Protectable dropdown relates to branch and tag protection
  PROTDD -- targets --> BRNCH_CREATE
  PROTDD -- targets --> TAGS_CREATE
  PROTDD -- initialized with --> PRJRB

  %% Finders interact with project/repository/branch/tag abstractions
  TREEFND -- queries --> PRJRB
  TREEFND -- queries --> HASREPO
  TREEFND -- queries --> PROJREPO

  BRANCHFND -- queries --> HASREPO
  BRANCHFND -- queries --> PROJREPO

  CHANGELOGTAG -- queries --> PRJRB
  CHANGELOGTAG -- uses --> TAGSFND

  TAGSFND -- inherits from --> GITREFSFND
  BRNSFND -- inherits from --> GITREFSFND

  TAGFND -- filters --> TAGSFND
  TAGFND -- queries --> TAGSFND

  GITREFSFND -- queries --> HASREPO
  GITREFSFND -- uses --> GITLABGITREF

  CHANGELOGCOMMITS -- queries --> PRJRB

  SRPRJBRCH -- queries --> PRJRB
  USRSTRFND -- queries --> PRJRB

  FORKFND -- inherits from --> SRPRJBRCH

  %% Services implement and enforce domain logic using models and finders
  BRNCH_CREATE -- validates with --> BRNCH_VALID
  BRNCH_CREATE -- persists new branches on --> HASREPO
  BRNCH_CREATE -- uses --> GITLABGITBRANCH
  BRNCH_CREATE -- relies on --> BRNCH_DELETE
  BRNCH_DELETE -- checks access --> PRJRB
  BRNCH_DELETEMRG -- checks merged branches --> PRJRB
  BRNCH_DELETEMRG -- deletes with --> BRNCH_DELETE

  BRNCH_VALID -- uses --> HASREPO
  BRNCH_VALID -- validates with --> PROTDD

  BRNCH_DIVCOMM -- uses --> HASREPO
  BRNCH_DIVCOMM -- uses --> GITLABREPOCACHE

  TAGS_CREATE -- uses --> HASREPO
  TAGS_CREATE -- validates --> GITLABGITTAG
  TAGS_CREATE -- uses --> PROTDD

  REPOS_BASE -- uses --> REPOS_HK
  REPOS_DESTROY -- deletes --> PROJREPO
  REPOS_HK -- manages --> HASREPO
  REPOS_REWRITE -- rewrites history for --> HASREPO

  DETECT_LANG -- detects using --> GITLABLANGDET
  DETECT_LANG -- persists to --> REPO_LANG
  REPO_LANG_SVC -- delegates to --> DETECT_LANG
  REPO_LANG_SVC -- updates --> PRJRB

  FRKSYNC -- syncs --> PRJRB
  FRKSYNC -- for fork --> FRKNET
  FRKSYNC -- for fork member --> FRKNETMEM

  GIT_TAG_PUSH -- triggers --> TAGSFND
  GIT_TAG_PUSH -- archives --> PRJRB
  GIT_BRANCH_PUSH -- updates --> BRNCH_CREATE
  GIT_BRANCH_PUSH -- triggers --> GIT_BRANCH_HOOKS

  GIT_DEDUP -- operates on --> PRJRB
  READMERNDER -- renders files from --> HASREPO

  UPD_REM_MIR -- syncs with --> PROJREPO
  UPD_REPO_STOR -- tracks --> PROJREPO
  AFTER_RENAME -- renames --> PROJREPO
  BRANCHES_BY_MODE -- finds branches using --> BRNSFND

  %% API endpoints interface for clients, use services/models
  API_BRANCHES -- exposes branches --> BRNCH_CREATE
  API_BRANCHES -- exposes branches --> BRNCH_DELETE
  API_BRANCHES -- fetches --> BRNSFND

  API_TAGS -- exposes tags --> TAGS_CREATE
  API_TAGS -- fetches --> TAGSFND

  API_REPOS -- fetches --> PROJREPO

  API_ENT_TAG -- represents --> GITLABGITTAG
  API_ENT_DIFFREFS -- represents --> GITLABGITREF

  API_DEPLOYKEYS -- uses --> DEPLOYKEY

  %% GitLab Git abstractions define low-level data transformations and repo access
  GITLABGIT -- entrypoint for low-level repo ops --> GITLABGITREPO
  GITLABGITREPO -- exposes --> GITLABGITBRANCH
  GITLABGITREPO -- exposes --> GITLABGITTAG
  GITLABGITREPO -- exposes --> GITLABGITREF
  GITLABGITREPO -- exposes --> GITLABGITTREE
  GITLABGITREPO -- is used by --> HASREPO

  GITLABGITBRANCH -- modeled as --> GITLABGITREF
  GITLABGITTAG -- modeled as --> GITLABGITREF

  GITLABGITFINDREFS -- used by --> TREEFND
  GITLABGITFINDREFS -- used by --> BRANCHFND
  GITLABGITFINDREFS -- used by --> TAGFND

  GITLABREPOSPROJ -- core for --> PROJREPO
  GITLABREPOSWIKI -- used for wiki repos
  GITLABREPOSIDENT -- used by --> GITLABGLREPO

  GITLABLANGDET -- used by --> DETECT_LANG

  GITLABSHELLADAPT -- used by --> REPOS_BASE
  GITLABGLREPO -- singleton for repo ID resolution
  GITLABREPOCACHE -- used by --> HASREPO

  %% Controllers orchestrate HTTP requests; interact with finders/services/models
  REPO_APPCTRL -- base class for --> REPO_HTTPCL
  REPO_HTTPCL -- base class for --> REPO_HTTP
  REPO_HTTP -- serves git/HTTP protocol requests
  PRJ_BRANCHCTRL -- manages API/UI for branches
  PRJ_BRANCHCTRL -- uses --> BRNCH_CREATE
  PRJ_BRANCHCTRL -- uses --> BRNCH_DELETE
  PRJ_BRANCHCTRL -- uses --> BRNCH_DELETEMRG
  PRJ_BRANCHCTRL -- queries --> BRNSFND
  PRJ_BRANCHCTRL -- queries --> BRANCHES_BY_MODE

  PRJ_TAGCTRL -- manages API/UI for tags
  PRJ_TAGCTRL -- uses --> TAGS_CREATE
  PRJ_TAGCTRL -- queries --> TAGSFND

  PRJ_REPOCTRL -- serves UI/API for project repositories
  PRJ_REPOCTRL -- queries --> PROJREPO

  PRJ_TREECTRL -- explores repo tree, pulls from --> TREEFND

  PRJ_FORKCTRL -- uses --> FRKNET

  %% Workers perform async tasks, invoke services/models
  REPO_FORK_WORKER -- invokes --> FRKSYNC
  PROJ_FORKS_SYNC -- invokes --> FRKSYNC
  REPO_CLEANUP -- runs --> REPOS_HK
  REPO_CHECK_DISP -- schedules --> REPO_CHECK_SINGLE
  REPO_CHECK_SINGLE -- checks --> HASREPO
  REPOS_REWRITE_WORKER -- invokes --> REPOS_REWRITE

  BATCHED_CLEANUP_WKR -- invokes --> REPOS_HK
  BATCHED_CLEANUP_SCHED -- schedules --> BATCHED_CLEANUP_WKR

  %% Helpers/utilities facilitate shared logic/data
  PROTREFSHELPER -- used by --> PRJ_BRANCHCTRL
  REPO_LANG_HELP -- used by --> PRJ_REPOCTRL
  ROUTING_HELP -- used by API & services

  %% Events track changes in repo state
  DEFBRCH_EVT -- tracks changes to --> PRJRB
  DEFBRCH_EVT -- is triggered by --> BRNCH_CREATE

  %%======== DATA STRUCTURES & ABSTRACTIONS ========

  GITLABGITBRANCH -- creates --> API_ENT_TAG
  GITLABGITTAG -- creates --> API_ENT_TAG
  PROJREPO -- relates to --> API_ENT_REPOSTOR

  %% Relationship with domain models and repo/data
  PROJREPO -- uses --> GITLABGITREPO
  HASREPO -- delegates to --> GITLABGITREPO

  %% Pool repository sharing/optimization with forks
  POOLREPO -- is used by --> PROJREPO
  POOLREPO -- links --> PRJRB

  %% Lang detection and data model
  DETECT_LANG -- builds --> REPO_LANG
  REPO_LANG -- relates to --> PROGLANG

  %% ProtectableDropdown encapsulates branch/tag protection domain logic
  PROTDD -- relates to --> GITLABGITBRANCH
  PROTDD -- relates to --> GITLABGITTAG

  %% Git Abstraction and Promotion
  GITLABGITREPO -- provides --> GITLABGITBRANCH
  GITLABGITREPO -- provides --> GITLABGITTAG
  GITLABGITREPO -- provides --> GITLABGITREF

  %%===========================================
```