```mermaid
flowchart TB
  %% Domain: Infrastructure, Performance & Operations / Caching & Redis / Redis Caching & State
  %% Styling
  %% --- Node style definitions
  classDef core fill:#D4F1F9,stroke:#B3E5FC,stroke-width:2px,color:#333,rx:10,ry:10
  classDef util fill:#FFF8DC,stroke:#F6D365,stroke-width:2px,color:#333,rx:10,ry:10
  classDef datastruct fill:#E0F8E0,stroke:#B2DFDB,stroke-width:2px,color:#333,rx:10,ry:10
  classDef error fill:#FFE4E1,stroke:#FFB6B6,stroke-width:2px,color:#333,rx:10,ry:10
  classDef init fill:#E6E6FA,stroke:#C3B1E1,stroke-width:2px,color:#333,rx:10,ry:10
  classDef groupbox fill:#F8F8F8,stroke-width:2px,stroke-dasharray:2 2,stroke:#B3B3B3

  %% --- Groupings

  subgraph G1_Core_Abstractions["Core Redis Abstractions & Connection Management"]
    direction TB
    redis[Redis Root lib/gitlab/redis.rb]:::core
    wrapper[Redis Wrapper lib/gitlab/redis/wrapper.rb]:::core
    cluster_store[Cluster Store lib/gitlab/redis/cluster_store.rb]:::core
    multi_store[MultiStore lib/gitlab/redis/multi_store.rb]:::core
    multi_store_wrapper[MultiStoreWrapper lib/gitlab/redis/multi_store_wrapper.rb]:::core
    multi_store_cp[MultiStoreConnectionPool lib/gitlab/redis/multi_store_connection_pool.rb]:::core
    cluster_util[ClusterUtil lib/gitlab/redis/cluster_util.rb]:::core
    command_builder[CommandBuilder lib/gitlab/redis/command_builder.rb]:::util
    config_generator[ConfigGenerator lib/gitlab/redis/config_generator.rb]:::util
    backwards_compat[BackwardsCompatibility lib/gitlab/redis/backwards_compatibility.rb]:::util
  end
  class G1_Core_Abstractions groupbox

  subgraph G2_Specialized_Stores["Specialized Redis Stores Logical Caches"]
    direction TB
    cache_store[Cache lib/gitlab/redis/cache.rb]:::core
    shared_state[SharedState lib/gitlab/redis/shared_state.rb]:::core
    repository_cache[RepositoryCache lib/gitlab/redis/repository_cache.rb]:::core
    sessions[Sessions lib/gitlab/redis/sessions.rb]:::core
    trace_chunks[TraceChunks lib/gitlab/redis/trace_chunks.rb]:::core
    feature_flag[FeatureFlag lib/gitlab/redis/feature_flag.rb]:::core
    hll[HLL lib/gitlab/redis/hll.rb]:::core
    queues[Queues lib/gitlab/redis/queues.rb]:::core
    queues_metadata[QueuesMetadata lib/gitlab/redis/queues_metadata.rb]:::core
    db_load_balancing[DbLoadBalancing lib/gitlab/redis/db_load_balancing.rb]:::core
    chat[Chat lib/gitlab/redis/chat.rb]:::core
    rate_limiting[RateLimiting lib/gitlab/redis/rate_limiting.rb]:::core
    boolean[Boolean lib/gitlab/redis/boolean.rb]:::util
    cursor_store[CursorStore lib/gitlab/redis/cursor_store.rb]:::core
  end
  class G2_Specialized_Stores groupbox

  subgraph G3_Caching_APIs["Cache APIs & Domain Behaviors"]
    direction TB
    cache_api[Cache lib/gitlab/cache.rb]:::core
    request_cache[RequestCache lib/gitlab/cache/request_cache.rb]:::core
    json_caches[JSONKeyed Cache lib/gitlab/cache/json_caches/json_keyed.rb]:::core
    import_caching[Import/Caching lib/gitlab/cache/import/caching.rb]:::core
    etag_store[Etag Caching Store lib/gitlab/etag_caching/store.rb]:::core
    process_mem_cache[ProcessMemoryCache lib/gitlab/process_memory_cache.rb]:::core
    ip_state[IP Address State lib/gitlab/ip_address_state.rb]:::core
    shard_health_cache[ShardHealthCache lib/gitlab/shard_health_cache.rb]:::core
    excl_lease_helpers[ExclusiveLeaseHelpers lib/gitlab/exclusive_lease_helpers.rb]:::core
    buffered_counter[BufferedCounter lib/gitlab/counters/buffered_counter_spec.rb]:::core
  end
  class G3_Caching_APIs groupbox

  subgraph G4_Perf_Enforcement["Performance Controls & Rate Limiting"]
    direction TB
    rack_attack[Core lib/gitlab/rack_attack.rb]:::core
    rack_attack_store[RackAttack::Store lib/gitlab/rack_attack/store.rb]:::core
    optim_lock[Optimistic Locking lib/gitlab/optimistic_locking.rb]:::core
  end
  class G4_Perf_Enforcement groupbox

  subgraph G5_Instrumentation["Redis Instrumentation & Validation"]
    direction TB
    redis_client_mw[RedisClientMiddleware lib/gitlab/instrumentation/redis_client_middleware.rb]:::init
    redis_cluster_validator[RedisClusterValidator lib/gitlab/instrumentation/redis_cluster_validator.rb]:::init
    redis_base[RedisBase lib/gitlab/instrumentation/redis_base.rb]:::init
  end
  class G5_Instrumentation groupbox

  subgraph G6_Patching["Redis Client Patching"]
    direction TB
    patch_redis_client[Patch::RedisClient lib/gitlab/patch/redis_client.rb]:::init
  end
  class G6_Patching groupbox

  subgraph G7_RequestState["Request State and Helpers"]
    direction TB
    safe_request_store[SafeRequestStore gems/gitlab-safe_request_store/lib/gitlab/safe_request_store.rb]:::core
    safe_request_store_null[SafeRequestStore::NullStore gems/gitlab-safe_request_store/lib/gitlab/safe_request_store/null_store.rb]:::core
    safe_request_store_version[SafeRequestStore::Version gems/gitlab-safe_request_store/lib/gitlab/safe_request_store/version.rb]:::core
    strong_memoize[StrongMemoize gems/gitlab-utils/lib/gitlab/utils/strong_memoize.rb]:::util
  end
  class G7_RequestState groupbox

  subgraph G8_ErrorHandling["Error & Exception Handling"]
    direction TB
    error_backwards[BackwardsCompatibility Error lib/gitlab/redis/backwards_compatibility.rb]:::error
    error_command_builder[CommandBuilder Errors lib/gitlab/redis/command_builder.rb]:::error
    error_boolean[Boolean Errors lib/gitlab/redis/boolean.rb]:::error
    error_excl_lease[ExclusiveLeaseHelpers Errors lib/gitlab/exclusive_lease_helpers.rb]:::error
    error_etag_store[Etag Store Errors lib/gitlab/etag_caching/store.rb]:::error
    error_rackattack[RackAttack Store Errors lib/gitlab/rack_attack/store.rb]:::error
    error_configgen[ConfigGenerator Errors lib/gitlab/redis/config_generator.rb]:::error
  end
  class G8_ErrorHandling groupbox

  subgraph G9_SystemChecks["Redis System & Config Checks"]
    direction TB
    external_db_checker[ExternalDatabaseChecker lib/gitlab/config_checker/external_database_checker.rb]:::init
    redis_version_check[RedisVersionCheck lib/system_check/app/redis_version_check.rb]:::init
  end
  class G9_SystemChecks groupbox

  subgraph G10_Support_SpecHelpers["Support & Spec Helpers"]
    direction TB
    redis_helpers[RedisHelpers spec/support/helpers/redis_helpers.rb]:::util
    cursor_store_spec[CursorStore Spec spec/lib/gitlab/redis/cursor_store_spec.rb]:::util
    buffered_counter_spec[BufferedCounter Spec spec/lib/gitlab/counters/buffered_counter_spec.rb]:::util
  end
  class G10_Support_SpecHelpers groupbox

  %% ------------ RELATIONSHIPS AND INTERACTIONS ------------

  %% --- Core relationships in Redis abstractions
  redis --> wrapper
  wrapper -->|provides core| multi_store
  wrapper -->|base class| cache_store & shared_state & repository_cache & sessions & trace_chunks & queues & feature_flag & hll & chat & rate_limiting & db_load_balancing & queues_metadata
  wrapper -->|utilized by| cluster_store
  multi_store -->|uses| multi_store_wrapper
  multi_store_wrapper --> multi_store_cp
  multi_store --> cluster_util
  multi_store --> wrapper
  cluster_store --> wrapper
  cluster_util --> cluster_store
  backwards_compat --> shared_state
  command_builder -->|used by| cluster_store

  config_generator --> wrapper
  cluster_util -->|used by| multi_store_wrapper

  %% --- Specialized stores - configuration, fallback, cross-store relationships
  cache_store -- fallback provider --> repository_cache & rate_limiting & feature_flag & chat
  shared_state -- fallback provider --> trace_chunks & db_load_balancing & sessions
  queues -- fallback provider --> queues_metadata

  %% --- Core logical cache API dependencies
  cache_api -- hooks/extends --> cache_store
  json_caches --> cache_api
  request_cache --> cache_api
  import_caching -->|logic over| cache_api & cache_store
  etag_store --> shared_state
  process_mem_cache --> cache_api

  shard_health_cache -->|uses| shared_state
  excl_lease_helpers --> cache_store
  ip_state --> request_cache
  buffered_counter --> cache_store

  %% --- Performance/rate limiters' core relationships
  rack_attack --> rack_attack_store
  rack_attack_store --> rate_limiting
  rack_attack --> cache_store
  rack_attack --> shared_state
  optim_lock --> cache_store

  %% --- Instrumentation relationships
  redis_client_mw --> wrapper & redis_base
  redis_cluster_validator --> redis_client_mw & redis_base & cluster_store
  redis_base --> wrapper

  %% --- Patching
  patch_redis_client --> wrapper

  %% --- Request store & memoize helpers' relationships
  safe_request_store --> safe_request_store_null & strong_memoize
  safe_request_store_version --> safe_request_store
  request_cache --> safe_request_store
  request_cache --> strong_memoize

  %% --- Error handling
  error_backwards --> backwards_compat
  error_command_builder --> command_builder
  error_boolean --> boolean
  error_excl_lease --> excl_lease_helpers
  error_etag_store --> etag_store
  error_rackattack --> rack_attack_store
  error_configgen --> config_generator

  %% --- System checks
  external_db_checker --> wrapper
  redis_version_check --> queues

  %% --- Support/helpers/specs
  redis_helpers --> wrapper & cache_store & sessions & queues & repository_cache & shared_state & chat & feature_flag & rate_limiting & trace_chunks & db_load_balancing
  cursor_store_spec --> cursor_store
  buffered_counter_spec --> buffered_counter

  %% --- Logical collaboration:
  import_caching -. utilizes .-> cache_store
  import_caching -. transforms .-> cache_api
  cursor_store --> shared_state
  request_cache --> process_mem_cache

  hll -- for metrics/counters --> cache_store

  %% --- Highlight key transformation/data structure flows:
  json_caches -- transforms JSON keys --> cache_store
  etag_store -- transforms etag metadata --> shared_state
  process_mem_cache -- handles process-local caching for --> cache_api
  shared_state -- stores ephemeral/general keys for --> various domain caches

  %% --- Inter-integration flows
  cache_api -->|layer atop| cache_store & process_mem_cache
  cache_api -->|used by| json_caches & import_caching & request_cache & etag_store

  %% --- Other meaningful logical links
  strong_memoize -->|memoization for| request_cache & multi_store & safe_request_store

  %% --- Testing and utility flows
  redis_helpers -. cleans up .-> cache_store & sessions & queues & repository_cache & shared_state & trace_chunks & feature_flag & chat & rate_limiting & db_load_balancing

  %% --- Vertical flow enforcement (semantic)
  G1_Core_Abstractions --> G2_Specialized_Stores
  G2_Specialized_Stores --> G3_Caching_APIs
  G3_Caching_APIs --> G4_Perf_Enforcement
  G4_Perf_Enforcement --> G5_Instrumentation
  G5_Instrumentation --> G6_Patching
  G1_Core_Abstractions --> G7_RequestState
  G7_RequestState --> G3_Caching_APIs
  G5_Instrumentation --> G8_ErrorHandling
  G1_Core_Abstractions --> G8_ErrorHandling
  G1_Core_Abstractions --> G9_SystemChecks
  G2_Specialized_Stores --> G9_SystemChecks
  G2_Specialized_Stores --> G10_Support_SpecHelpers
  G3_Caching_APIs --> G10_Support_SpecHelpers

  %% ------------ END OF DIAGRAM ---------------
```