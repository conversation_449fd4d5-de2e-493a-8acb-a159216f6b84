```mermaid
flowchart TB
%% VERTICAL layout

%% Subgraph: <PERSON>GANI<PERSON><PERSON><PERSON> SETTINGS DOMAIN - GROUPING
subgraph OrganizationSettingsDomain["Organization Settings Domain"]
  direction TB
  style OrganizationSettingsDomain fill:#F8F8F8,stroke:#ADD8E6,stroke-width:2,corner-radius:15px

  %% --- CORE DOMAIN FILES GROUP ---
  subgraph CoreDomainModels["Core Domain Models & Structures"]
    direction TB
    style CoreDomainModels fill:#F8F8F8,stroke:#98D8F1,stroke-width:2,corner-radius:12px

    org_settings_model["organizations/organization_setting.rb
    <br/>Role: Main organization settings persistence & schema validation"
      style org_settings_model fill:#D4F1F9,stroke:#B0D0E6,stroke-width:2,corner-radius:8px,stroke-dasharray:0

    application_setting_model["application_setting.rb
    <br/>Role: Global application settings storage"
      style application_setting_model fill:#D4F1F9,stroke:#B0D0E6,stroke-width:2,corner-radius:8px

    application_setting_term["application_setting/term.rb
    <br/>Role: Terms of Service for organizations; agreement state"
      style application_setting_term fill:#E0F8E0,stroke:#90D890,stroke-width:2,corner-radius:8px

    application_setting_impl["application_setting_implementation.rb
    <br/>Role: Logic for settings allowlist/denylist & normalization"
      style application_setting_impl fill:#FFF8DC,stroke:#FFE9A6,stroke-width:2,corner-radius:8px

    org_push_rule_model["organization_push_rule.rb
    <br/>Role: Rules for force & standards at organization level"
      style org_push_rule_model fill:#D4F1F9,stroke:#B0D0E6,stroke-width:2,corner-radius:8px
  end

  %% --- CONTROLLERS GROUP ---
  subgraph SettingsControllers["Organization Settings Controllers"]
    direction TB
    style SettingsControllers fill:#F8F8F8,stroke:#C3B4E3,stroke-width:2,corner-radius:12px

    org_settings_controller["organizations/settings_controller.rb
    <br/>Role: Endpoint for settings management & access control"
      style org_settings_controller fill:#D4F1F9,stroke:#B0D0E6,stroke-width:2,corner-radius:8px

    admin_app_settings_controller["admin/application_settings_controller.rb
    <br/>Role: Admin interface for adjusting global app settings"
      style admin_app_settings_controller fill:#D4F1F9,stroke:#B0D0E6,stroke-width:2,corner-radius:8px

    admin_app_settings_appearance["admin/application_settings/appearances_controller.rb
    <br/>Role: Themes & look-and-feel for organization"
      style admin_app_settings_appearance fill:#FFF8DC,stroke:#FFE9A6,stroke-width:2,corner-radius:8px

    admin_version_check["admin/version_check_controller.rb
    <br/>Role: Application version & support checks"
      style admin_version_check fill:#FFF8DC,stroke:#FFE9A6,stroke-width:2,corner-radius:8px

    users_pins_controller["users/pins_controller.rb
    <br/>Role: Manages navigation pinning cross-links with settings updates"
      style users_pins_controller fill:#FFF8DC,stroke:#FFE9A6,stroke-width:2,corner-radius:8px

    admin_geo_settings_controller["ee/app/controllers/admin/geo/settings_controller.rb
    <br/>Role: Geo-replication settings at organizational scope"
      style admin_geo_settings_controller fill:#D4F1F9,stroke:#B0D0E6,stroke-width:2,corner-radius:8px

    ee_admin_app_settings_controller["ee/app/controllers/ee/admin/application_settings_controller.rb
    <br/>Role: EE-specific settings concern & extension"
      style ee_admin_app_settings_controller fill:#D4F1F9,stroke:#B0D0E6,stroke-width:2,corner-radius:8px

    admin_app_settings_scim_oauth["ee/app/controllers/admin/application_settings/scim_oauth_controller.rb
    <br/>Role: SCIM OAuth config for settings & user provisioning"
      style admin_app_settings_scim_oauth fill:#D4F1F9,stroke:#B0D0E6,stroke-width:2,corner-radius:8px

    admin_gitlab_duo_config["ee/app/controllers/admin/gitlab_duo/configuration_controller.rb
    <br/>Role: Admin config for AI settings in org context"
      style admin_gitlab_duo_config fill:#D4F1F9,stroke:#B0D0E6,stroke-width:2,corner-radius:8px

    ee_admin_users_controller["ee/app/controllers/ee/admin/users_controller.rb
    <br/>Role: Additional user management controls with org-specific logic"
      style ee_admin_users_controller fill:#D4F1F9,stroke:#B0D0E6,stroke-width:2,corner-radius:8px

    remote_dev_workspaces_controller["ee/app/controllers/remote_development/workspaces_controller.rb
    <br/>Role: Controls remote dev workspace settings"
      style remote_dev_workspaces_controller fill:#FFF8DC,stroke:#FFE9A6,stroke-width:2,corner-radius:8px
  end

  %% --- DOMAIN SERVICES ---
  subgraph DomainServices["Settings Domain Services"]
    direction TB
    style DomainServices fill:#F8F8F8,stroke:#BBCBFA,stroke-width:2,corner-radius:12px

    org_update_service["organizations/update_service.rb
    <br/>Role: Orchestrates update to org settings, permissions, audit"
      style org_update_service fill:#D4F1F9,stroke:#B0D0E6,stroke-width:2,corner-radius:8px

    ee_app_settings_update_service["ee/app/services/ee/application_settings/update_service.rb
    <br/>Role: Enhanced settings updates, EE implications"
      style ee_app_settings_update_service fill:#D4F1F9,stroke:#B0D0E6,stroke-width:2,corner-radius:8px

    user_deactivate_service["app/services/users/deactivate_service.rb
    <br/>Role: User deactivation, may alter org membership/settings"
      style user_deactivate_service fill:#FFF8DC,stroke:#FFE9A6,stroke-width:2,corner-radius:8px

    ns_setting_changes_auditor["ee/lib/namespaces/namespace_setting_changes_auditor.rb
    <br/>Role: Audits and logs org/group settings changes"
      style ns_setting_changes_auditor fill:#E6E6FA,stroke:#C3B4E3,stroke-width:2,corner-radius:8px

    plans_finder["app/finders/admin/plans_finder.rb
    <br/>Role: Finds plans relevant for organization settings"
      style plans_finder fill:#FFF8DC,stroke:#FFE9A6,stroke-width:2,corner-radius:8px
  end

  %% --- SUPPORTING & DOMAIN-SPECIFIC HELPERS ---
  subgraph SupportingHelpers["Supporting & Domain-specific Helpers"]
    direction TB
    style SupportingHelpers fill:#F8F8F8,stroke:#A4D4AE,stroke-width:2,corner-radius:12px

    ee_readme_helper["ee/app/helpers/ee/readme_helper.rb
    <br/>Role: Integrate documentation, may reference org settings"
      style ee_readme_helper fill:#FFF8DC,stroke:#FFE9A6,stroke-width:2,corner-radius:8px

    ee_terms_helper["ee/app/helpers/ee/terms_helper.rb
    <br/>Role: Terms acceptance/help for settings UIs"
      style ee_terms_helper fill:#FFF8DC,stroke:#FFE9A6,stroke-width:2,corner-radius:8px
  end

  %% --- DATA STRUCTURES & TRANSFORMATION ALGORITHMS ---
  subgraph DataStructures["Persistent Data Structures & Access Models"]
    direction TB
    style DataStructures fill:#F8F8F8,stroke:#90EE90,stroke-width:2,corner-radius:12px

    sys_broadcast_message["system/broadcast_message.rb
    <br/>Role: Messaging at org/app level, settings-driven"
      style sys_broadcast_message fill:#E0F8E0,stroke:#90D890,stroke-width:2,corner-radius:8px

    ee_security_pipeline_config["ee/app/models/security/pipeline_execution_policy/config.rb
    <br/>Role: Security pipeline policy that may use org settings"
      style ee_security_pipeline_config fill:#E0F8E0,stroke:#90D890,stroke-width:2,corner-radius:8px
  end

  %% --- ABSTRACTIONS & AUTHORIZATION POLICIES ---
  subgraph PoliciesAbstractions["Access Controls & Policy Abstractions"]
    direction TB
    style PoliciesAbstractions fill:#F8F8F8,stroke:#C3C5E7,stroke-width:2,corner-radius:12px

    releases_evidence_policy["releases/evidence_policy.rb
    <br/>Role: Evidence rules, org-level policies"
      style releases_evidence_policy fill:#FFF8DC,stroke:#FFE9A6,stroke-width:2,corner-radius:8px

    ee_ci_pipeline_policy["ee/app/policies/ee/ci/pipeline_policy.rb
    <br/>Role: EE-specific CI pipeline org-wide policies"
      style ee_ci_pipeline_policy fill:#FFF8DC,stroke:#FFE9A6,stroke-width:2,corner-radius:8px

    ee_ci_deployable_policy["ee/app/policies/ee/ci/deployable_policy.rb
    <br/>Role: EE deploy controls, environment settings"
      style ee_ci_deployable_policy fill:#FFF8DC,stroke:#FFE9A6,stroke-width:2,corner-radius:8px
  end

  %% --- EXTERNAL INTEGRATIONS & IMPORTS ---
  subgraph ExtIntegrations["Integration, Import & Remote Features"]
    direction TB
    style ExtIntegrations fill:#F8F8F8,stroke:#C6E2F7,stroke-width:2,corner-radius:12px

    ee_gitlab_import_sources["ee/lib/ee/gitlab/import_sources.rb
    <br/>Role: Controls available import sources at org/app level"
      style ee_gitlab_import_sources fill:#FFF8DC,stroke:#FFE9A6,stroke-width:2,corner-radius:8px

    remote_dev_settings_reader["ee/lib/remote_development/settings/current_settings_reader.rb
    <br/>Role: Reads settings for remote dev context"
      style remote_dev_settings_reader fill:#FFF8DC,stroke:#FFE9A6,stroke-width:2,corner-radius:8px

    gitlab_email["lib/gitlab/email.rb
    <br/>Role: Used for organization notification; references settings"
      style gitlab_email fill:#FFF8DC,stroke:#FFE9A6,stroke-width:2,corner-radius:8px
  end

  %% --- QA & INT/UNIT TEST REPRESENTATION ---
  subgraph TestingAndQA["Testing & QA Representation"]
    direction TB
    style TestingAndQA fill:#F8F8F8,stroke:#E6CBA8,stroke-width:2,corner-radius:12px

    qa_knapsack_report["qa/qa/support/knapsack_report.rb
    <br/>Role: Test distribution reporting across organization changes"
      style qa_knapsack_report fill:#FFF8DC,stroke:#FFE9A6,stroke-width:2,corner-radius:8px

    qa_admin_settings_account_limit["qa/qa/page/admin/settings/component/account_and_limit.rb
    <br/>Role: UI automation for admin org settings"
      style qa_admin_settings_account_limit fill:#FFF8DC,stroke:#FFE9A6,stroke-width:2,corner-radius:8px

    ee_profiles_account_spec["ee/spec/features/profiles/account_spec.rb
    <br/>Role: Feature spec for org settings side-effect user account"
      style ee_profiles_account_spec fill:#FFF8DC,stroke:#FFE9A6,stroke-width:2,corner-radius:8px
  end

  %% --- EDGE/LINK LOGIC: RELATIONSHIPS/INTERACTIONS ---
  %% Controllers <-> Core Models/Structs
  org_settings_controller -->|uses, updates, queries| org_settings_model
  org_settings_controller -->|authorization| ns_setting_changes_auditor
  org_settings_controller -->|calls update| org_update_service

  org_update_service -- updates --> org_settings_model
  org_update_service -- triggers audit --> ns_setting_changes_auditor

  admin_app_settings_controller -->|queries/updates| application_setting_model
  admin_app_settings_controller -->|applies| application_setting_impl
  admin_app_settings_controller -->|uses| application_setting_term
  admin_app_settings_controller -->|calls update service| ee_app_settings_update_service

  admin_geo_settings_controller -->|inherits/uses| admin_app_settings_controller

  admin_app_settings_appearance -->|assigns themes| application_setting_model

  admin_app_settings_scim_oauth -->|SCIM tokens, settings| application_setting_model

  admin_gitlab_duo_config -->|AI settings| application_setting_model

  ee_admin_app_settings_controller -->|extends| admin_app_settings_controller
  ee_admin_app_settings_controller -->|calls| ee_app_settings_update_service

  admin_version_check -->|checks upgrade constraints| application_setting_model

  users_pins_controller -->|updates user prefs impacting| org_settings_model

  ee_admin_users_controller -->|reads/writes| org_settings_model
  ee_admin_users_controller -->|may reference| ns_setting_changes_auditor

  remote_dev_workspaces_controller -->|contexts| org_settings_model

  %% Data pipeline: Settings repositories
  org_settings_model -- belongs_to --> application_setting_model
  org_settings_model -- validates_schema --> application_setting_impl

  org_settings_model -- may reference --> org_push_rule_model

  org_settings_model -- stores array --> sys_broadcast_message
  org_push_rule_model -- restricts pushes --> org_settings_model

  ee_security_pipeline_config -- reads --> application_setting_model

  %% Helper relationships
  ee_readme_helper -- fetches/links to --> org_settings_model
  ee_terms_helper -- resolves links from --> application_setting_term

  %% Policies
  releases_evidence_policy -- rules depend on --> org_settings_model
  ee_ci_pipeline_policy -- control builds for --> org_settings_model
  ee_ci_deployable_policy -- restricts based on --> org_settings_model

  %% Domain & Supporting Services
  ee_app_settings_update_service -- updates --> application_setting_model
  ee_app_settings_update_service -- triggers audit via --> ns_setting_changes_auditor

  user_deactivate_service -- affects access to --> org_settings_model

  plans_finder -- limits plans according to --> application_setting_model

  %% External Integration & Import
  ee_gitlab_import_sources -- references available sources from --> application_setting_model

  remote_dev_settings_reader -- pulls values from --> application_setting_model

  gitlab_email -- reads notification settings from --> org_settings_model

  %% Testing/QA
  qa_knapsack_report -- distributes/validates flows re: --> org_settings_model
  qa_admin_settings_account_limit -- configures UI for --> org_settings_model
  ee_profiles_account_spec -- simulates user/org settings interactions in --> org_settings_model

  %% Cross-link: Key Type Implementations
  application_setting_term -- "has_many" --> application_setting_model

end

%% Legend (not functional, for color reference only):
%% pastel blue: #D4F1F9 (core domain), pastel yellow: #FFF8DC (support/util), pastel green: #E0F8E0 (data struct), pastel red: #FFE4E1 (error), pastel purple: #E6E6FA (init/setup), very light gray: #F8F8F8 (subgraph)
```