```mermaid
flowchart TD
  %% COLORS
  %% Core domain files: #D4F1F9 (pastel blue)
  %% Supporting/utility files: #FFF8DC (pastel yellow)
  %% Data structure files: #E0F8E0 (pastel green)
  %% Error handling files: #FFE4E1 (pastel red)
  %% Initialization/setup files: #E6E6FA (pastel purple)
  %% Logical groupings/subgraphs: #F8F8F8 (with pastel borders)

  %% DOMAIN: Integrations & Extensibility/Third-Party Integrations/External CI/CD & SCM Integrations

  %% MAIN DOMAIN SUBGRAPH
  subgraph EXT[Integrations & Extensibility / 3rd-Party Integrations / External CI-CD & SCM]
    direction TB
    style EXT fill:#F8F8F8,stroke:#D4A5A5,stroke-width:2px,rounded=true

    %% INTEGRATION MODELS GROUP
    subgraph A[Integrations Models]
      direction TB
      style A fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded=true

      intJenkins["Jenkins Integration\napp/models/integrations/jenkins.rb"]:::core
      intBamboo["Bamboo Integration\napp/models/integrations/bamboo.rb"]:::core
      intYouTrack["YouTrack Integration\napp/models/integrations/youtrack.rb"]:::core
      intMockCi["Mock CI Integration\napp/models/integrations/mock_ci.rb"]:::core
      intAssembla["Assembla Integration\napp/models/integrations/assembla.rb"]:::core
      intCustomIssueTracker["Custom Issue Tracker\napp/models/integrations/custom_issue_tracker.rb"]:::core
      intZentaoData["Zentao Tracker Data\napp/models/integrations/zentao_tracker_data.rb"]:::core

      intInstZentao["Instance Zentao Integration\napp/models/integrations/instance/zentao.rb"]:::core
      intInstJenkins["Instance Jenkins Integration\nnot defined in tree\nlogical member"]:::core
      intInstPipelinesEmail["Instance PipelinesEmail Integration\napp/models/integrations/instance/pipelines_email.rb"]:::core
      intInstUnifyCircuit["Instance UnifyCircuit Integration\napp/models/integrations/instance/unify_circuit.rb"]:::core
      intInstRedmine["Instance Redmine Integration\napp/models/integrations/instance/redmine.rb"]:::core
      intInstPivotal["Instance Pivotaltracker Integration\napp/models/integrations/instance/pivotaltracker.rb"]:::core
      intInstMockCi["Instance Mock CI Integration\napp/models/integrations/instance/mock_ci.rb"]:::core
      intInstWiki["Instance External Wiki Integration\napp/models/integrations/instance/external_wiki.rb"]:::core
    end

    %% BASE INTEGRATION CONCERNS GROUP (Abstractions & Shared Behaviors)
    subgraph B[Integration Base Concerns]
      direction TB
      style B fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded=true

      baseCI["Base CI\napp/models/concerns/integrations/base/ci.rb"]:::support
      baseMockCi["Base Mock CI\napp/models/concerns/integrations/base/mock_ci.rb"]:::support
      baseDiffblueCover["Base Diffblue Cover\napp/models/concerns/integrations/base/diffblue_cover.rb"]:::support
      baseClickUp["Base Clickup\napp/models/concerns/integrations/base/clickup.rb"]:::support
      basePipelinesEmail["Base Pipelines Email\napp/models/concerns/integrations/base/pipelines_email.rb"]:::support
      baseUnifyCircuit["Base Unify Circuit\napp/models/concerns/integrations/base/unify_circuit.rb"]:::support
      basePushover["Base Pushover\napp/models/concerns/integrations/base/pushover.rb"]:::support
      baseEmailsPush["Base Emails On Push\napp/models/concerns/integrations/base/emails_on_push.rb"]:::support
      baseIrker["Base Irker\napp/models/concerns/integrations/base/irker.rb"]:::support
      baseThirdPartyWiki["Base ThirdPartyWiki\napp/models/concerns/integrations/base/third_party_wiki.rb"]:::support
    end

    %% WORKERS (ASYNC PROCESSING, CROSS-REFERENCE HANDLING, PROPAGATION)
    subgraph Workers["Workers: Async Processing"]
      direction TB
      style Workers fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2px,rounded=true

      propagateIntegration["Propagate Integration\napp/workers/propagate_integration_worker.rb"]:::init
      propagateIntegrationProject["Propagate Integration Project\napp/workers/propagate_integration_project_worker.rb"]:::init
      propagateIntegrationInherit["Propagate Integration Inherit\napp/workers/propagate_integration_inherit_worker.rb"]:::init
      propagateIntegrationInheritDesc["Propagate Integration Inherit Descendant\napp/workers/propagate_integration_inherit_descendant_worker.rb"]:::init
      createExternalCrossRef["Create External CrossRef\napp/workers/integrations/create_external_cross_reference_worker.rb"]:::init
    end

    %% CONTROLLER CONCERN (PARAMETERS MANAGEMENT)
    subgraph Ctrl[Controllers: Params]
      direction TB
      style Ctrl fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2px,rounded=true

      ctrlParams["Integration Params\napp/controllers/concerns/integrations/params.rb"]:::support
    end

    %% DATA STRUCTURES
    subgraph DS["Supporting Data Structures"]
      direction TB
      style DS fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2px,rounded=true

      grafanaInt["Grafana Integration\napp/models/grafana_integration.rb"]:::data
    end

    %% TESTS
    subgraph TS["System & Feature Tests"]
      direction TB
      style TS fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2px,rounded=true

      featProjInt["Project Integrations Feature Spec\nspec/features/projects/integrations/project_integrations_spec.rb"]:::support
      featGroupInt["Group Integrations Feature Spec\nspec/features/groups/integrations/group_integrations_spec.rb"]:::support
      featInstInt["Instance Integrations Feature Spec\nspec/features/admin/integrations/instance_integrations_spec.rb"]:::support
    end

    %% QA & CI SUPPORT INFRA
    subgraph QACI["QA: Jenkins, GitLab Orchestrator, CI Helpers"]
      direction TB
      style QACI fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2px,rounded=true

      qaJenkinsHelpers["Jenkins Helpers\nqa/qa/vendor/jenkins/helpers.rb"]:::support
      qaJenkinsClient["Jenkins Client\nqa/qa/vendor/jenkins/client.rb"]:::support
      qaJenkinsJob["Jenkins Job\nqa/qa/vendor/jenkins/job.rb"]:::support

      qaSvcJenkinsPage["QA Service Jenkins Page\nqa/qa/page/project/settings/services/jenkins.rb"]:::support

      orchestratorCI["Orchestrator CI Helpers\nqa/gems/gitlab-orchestrator/lib/gitlab/orchestrator/lib/helpers/ci.rb"]:::init
      orchestratorInst["Orchestrator Installation\nqa/gems/gitlab-orchestrator/lib/gitlab/orchestrator/lib/instance/installation.rb"]:::init
      orchestratorCmdDestroy["Orchestrator Command Destroy\nqa/gems/gitlab-orchestrator/lib/gitlab/orchestrator/commands/destroy.rb"]:::init
      orchestratorCmdDeployment["Orchestrator Cmd Deployment\nqa/gems/gitlab-orchestrator/lib/gitlab/orchestrator/commands/subcommands/deployment.rb"]:::init
      orchestratorPresets["Orchestrator ResourcePresets\nqa/gems/gitlab-orchestrator/lib/gitlab/orchestrator/lib/deployment/resource_presets.rb"]:::init
    end

    %% MATTERMOST SUPPORT (Logical Inclusion for SCM/CI Extensibility)
    subgraph MAT["Mattermost SCM Extensibility"]
      direction TB
      style MAT fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2px,rounded=true

      matterSession["Session\nlib/mattermost/session.rb"]:::support
      matterTeam["Team\nlib/mattermost/team.rb"]:::support
    end

    %% OMNIAUTH STRATEGY (Auth for Third-Party Integrations)
    subgraph OAUTH["Third-Party Authentication"]
      direction TB
      style OAUTH fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2px,rounded=true

      omniauthSalesforce["Omniauth Salesforce\nvendor/gems/omniauth-salesforce/lib/omniauth/strategies/salesforce.rb"]:::support
    end

    %% ZENTAO-RELATED LOGIC AND DATA
    subgraph ZEN["Zentao Integration Logic"]
      direction TB
      style ZEN fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded=true

      zentaoQuery["Zentao Query\nlib/gitlab/zentao/query.rb"]:::support
      zentaoClientSpec["Zentao Client Spec\nspec/lib/gitlab/zentao/client_spec.rb"]:::support
    end
  end

  %% === LOGICAL RELATIONSHIPS & INTERACTIONS ===

  %% CORE DOMAIN ARCH
  intJenkins -- uses --> baseCI
  intJenkins -- uses --> baseMockCi
  intJenkins -- uses --> ctrlParams

  intMockCi -- uses --> baseMockCi
  intMockCi -- shares_behavior_with --> intInstMockCi

  intInstMockCi -- includes --> baseMockCi
  intInstMockCi -- is_instance_of --> intMockCi

  intInstZentao -- logical_data --> intZentaoData

  intInstRedmine -- includes --> baseThirdPartyWiki
  intInstRedmine -- includes --> baseIrker

  intInstPipelinesEmail -- includes --> basePipelinesEmail

  intInstUnifyCircuit -- includes --> baseUnifyCircuit

  intInstWiki -- includes --> baseThirdPartyWiki
  intInstWiki -- instance_of --> baseThirdPartyWiki

  %% BASE CONCERN INHERITANCE & REUSE
  baseMockCi -- extends --> baseCI
  baseClickUp -- includes --> baseEmailsPush

  %% WORKER USAGE
  propagateIntegration -- syncs --> intJenkins
  propagateIntegration -- syncs --> intMockCi
  propagateIntegration -- syncs --> intInstMockCi
  propagateIntegrationInherit -- propagates --> propagateIntegration
  propagateIntegrationInheritDesc -- propagates --> propagateIntegrationInherit
  propagateIntegrationProject -- updates --> propagateIntegration

  createExternalCrossRef -- links_crossref --> intJenkins
  createExternalCrossRef -- links_crossref --> intMockCi

  %% CONTROLLER PARAMS
  ctrlParams -- transforms_params_for --> intJenkins
  ctrlParams -- transforms_params_for --> intMockCi
  ctrlParams -- used_by --> intInstMockCi
  ctrlParams -- used_by --> intInstJenkins

  %% DATA STRUCTURE INTERACTION
  grafanaInt -- stores_tokens_for --> intJenkins

  %% QA / CI / SYSTEM TEST INTEGRATIONS
  featProjInt -- tests --> intJenkins
  featProjInt -- tests --> intMockCi
  featGroupInt -- tests_group --> intJenkins
  featGroupInt -- tests_group --> intMockCi
  featInstInt -- tests_instance --> intInstJenkins
  featInstInt -- tests_instance --> intInstMockCi

  %% QA JENKINS INFRASTRUCTURE
  qaJenkinsHelpers -- supports --> qaJenkinsClient
  qaJenkinsClient -- uses --> qaJenkinsHelpers
  qaJenkinsJob -- uses --> qaJenkinsHelpers
  qaSvcJenkinsPage -- automates_ui_for --> intJenkins
  qaJenkinsClient -- interacts_with --> intJenkins

  orchestratorCI -- monitors_ci_for --> intJenkins
  orchestratorInst -- manages_env_for --> intJenkins
  orchestratorCmdDestroy -- command_for --> orchestratorInst
  orchestratorCmdDeployment -- deploys --> orchestratorInst
  orchestratorPresets -- presets_for --> orchestratorInst

  %% MATTERMOST LOGIC
  matterTeam -- belongs_to --> matterSession

  %% OMNIAUTH (EXTERNAL AUTH)
  omniauthSalesforce -- oauth_for --> intJenkins

  %% ZENTAO DOMAIN LOGIC
  intInstZentao -- queries --> zentaoQuery
  zentaoQuery -- used_in_tests --> zentaoClientSpec
  intZentaoData -- supports --> intInstZentao

  %% STYLE CLASSES
  classDef core fill:#D4F1F9,stroke:#43B0F1,stroke-width:2px,stroke-dasharray:1 0,rx:10,ry:10;
  classDef support fill:#FFF8DC,stroke:#EDC162,stroke-width:2px,rx:10,ry:10;
  classDef data fill:#E0F8E0,stroke:#6FCF97,stroke-width:2px,rx:10,ry:10;
  classDef error fill:#FFE4E1,stroke:#F986A9,stroke-width:2px,rx:10,ry:10;
  classDef init fill:#E6E6FA,stroke:#B7B7F7,stroke-width:2px,rx:10,ry:10;
```