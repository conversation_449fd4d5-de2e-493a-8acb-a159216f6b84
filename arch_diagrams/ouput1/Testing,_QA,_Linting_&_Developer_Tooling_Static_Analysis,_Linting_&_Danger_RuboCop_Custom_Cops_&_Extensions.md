```mermaid
flowchart TD
  %% COLORS & STYLES
  %% Node & subgraph background colors
  %% - Core Domain: pastel blue (#D4F1F9)
  %% - Supporting Utility: pastel yellow (#FFF8DC)
  %% - Data Structures: pastel green (#E0F8E0)
  %% - Errors, Linting, Validation: pastel red (#FFE4E1)
  %% - Initialization/Setup: pastel purple (#E6E6FA)
  %% - Subgraph background: very light gray (#F8F8F8)
  %% - Subgraph border: appropriate pastel
  
  %% Define reusable classes for node styles
  classDef core fill:#D4F1F9,stroke:#82bae6,stroke-width:2px,color:#2e3a4b,rx:10,ry:10
  classDef support fill:#FFF8DC,stroke:#e7c983,stroke-width:2px,color:#4d4840,rx:10,ry:10
  classDef data fill:#E0F8E0,stroke:#8ddc8d,stroke-width:2px,color:#225c2c,rx:10,ry:10
  classDef error fill:#FFE4E1,stroke:#f3b4bb,stroke-width:2px,color:#802126,rx:10,ry:10
  classDef init fill:#E6E6FA,stroke:#cabbe9,stroke-width:2px,color:#3c2d4f,rx:10,ry:10
  classDef group fill:#F8F8F8,stroke:#B3CDE8,stroke-width:2px,color:#2e3a4b

  %% LAYOUT - VERTICAL
  direction TB

  %% === ROOT: TESTING, QA, LINTING & DEVELOPER TOOLING ===

  subgraph LINT["Testing, QA, Linting & Static Analysis" ]
    class LINT group

    %% --------- Dictionaries and Data Structures -----------
    subgraph DATA["Domain-specific Data Structures"]
      class DATA group
      rubocop_code_reuse_helpers[code_reuse_helpers.rb\nHelper functions for code reuse patterns]
      rubocop_migration_helpers[rubocop/migration_helpers.rb\nHelpers for migration-related RuboCop cops]
      class rubocop_code_reuse_helpers,support
      class rubocop_migration_helpers,data
    end

    %% --------- Custom Validators ----------
    subgraph VALIDATORS["Model Validators"]
      class VALIDATORS group
      feature_flag_validator[feature_flag_strategies_validator.rb\nValidates structure of feature flag strategies]
      addressable_url_validator[addressable_url_validator.rb\nEnsures URL values are well-formed]
      abstract_path_validator[abstract_path_validator.rb\nValidates safe path structures]
      html_safety_validator[html_safety_validator.rb\nChecks for HTML safety in model attributes]
      cron_validator[cron_validator.rb\nValidates cron schedule expressions]
      duration_validator[duration_validator.rb\nChecks for text durations with error handling]
      class feature_flag_validator,addressable_url_validator,abstract_path_validator,html_safety_validator,cron_validator,duration_validator,error
    end

    %% --------- Core: RuboCop Custom Cops & Extensions ----------
    subgraph RUBOCOP_LIB["RuboCop Custom Cops & Code Reuse Extensions"]
      class RUBOCOP_LIB group
      rubocop_gitlab_cops[rubocop/cop/gitlab/*\nOrganization-specific cop logics]
      rubocop_code_reuse_cops[rubocop/cop/code_reuse/*\nCode organization enforcers]
      rubocop_rspec_cops[rubocop/cop/rspec/*\nRSpec style & structure linting]
      **********************[rubocop/cop/migration/*\nMigration safety constraints]
      rubocop_qa_cops[rubocop/cop/qa/*\nQA conventions Selectors, Page Object, Feature Flags]
      rubocop_performance_cops[rubocop/cop/performance/*\nRails/AR query optimizations]
      rubocop_api_cops[rubocop/cop/api/*\nAPI safety conventions]
      rubocop_usage_data_cops[rubocop/cop/usage_data/*\nUsageData metrics analyses]
      rubocop_graphql_cops[rubocop/cop/graphql/*\nGraphQL schema and contract enforcers]
      rubocop_database_cops[rubocop/cop/database/*\nDB access & connection safety]
      rubocop_sidekiq_cops[rubocop/cop/sidekiq_* & sidekiq_api_usage.rb\nSidekiq worker constraints]
      rubocop_gitlab_rails_cops[rubocop/cop/gitlab/rails/*\nGitLab-specific Rails conventions]
      rubocop_background_migration_cops[rubocop/cop/background_migration/*\nBackground migration rules]
      rubocop_style_cops[rubocop/cop/style/*\nStyle and format enforcers]
      rubocop_static_translation[rubocop/cop/static_translation_definition.rb\nGuards for static translation]
      class rubocop_gitlab_cops,core
      class rubocop_code_reuse_cops,core
      class rubocop_rspec_cops,core
      class **********************,core
      class rubocop_qa_cops,core
      class rubocop_performance_cops,core
      class rubocop_api_cops,core
      class rubocop_usage_data_cops,core
      class rubocop_graphql_cops,core
      class rubocop_database_cops,core
      class rubocop_sidekiq_cops,core
      class rubocop_gitlab_rails_cops,core
      class rubocop_background_migration_cops,core
      class rubocop_style_cops,core
      class rubocop_static_translation,core
    end

    %% --------- Lint Error Handling, Special Rules ----------
    subgraph LINTING["Global/Shared Linting Rules"]
      class LINTING group
      rubocop_ignored_columns[rubocop/cop/ignored_columns.rb\nDatabase columns ignore check]
      rubocop_destroy_all[rubocop/cop/destroy_all.rb\nBan destroy_all in favor of delete_all]
      rubocop_active_model_errors[rubocop/cop/active_model_errors_direct_manipulation.rb\nDiscourage direct ActiveModel errors manipulation]
      rubocop_default_scope[rubocop/cop/default_scope.rb\nDenylists use of default_scope]
      rubocop_user_admin[rubocop/cop/user_admin.rb\nPrevents User#admin? logic]
      rubocop_file_decompression[rubocop/cop/file_decompression.rb\nValidate secure file decompression]
      rubocop_avoid_becomes[rubocop/cop/avoid_becomes.rb\nAvoid becomes]
      rubocop_sidekiq_api_usage[rubocop/cop/sidekiq_api_usage.rb\nGuard Sidekiq API usage]
      rubocop_static_translation_definition[rubocop/cop/static_translation_definition.rb\nFlag static-scoped translations]
      rubocop_safe_params[rubocop/cop/safe_params.rb\nEnforce use of safe_params]
      rubocop_sidekiq_options_queue[rubocop/cop/sidekiq_options_queue.rb\nDisallow manual Sidekiq queue assignment]
      rubocop_filename_length[rubocop/cop/filename_length.rb\nRestrict filename/path length]
      rubocop_avoid_break_from_strong_memoize[rubocop/cop/avoid_break_from_strong_memoize.rb\nBan break from strong_memoize block]
      rubocop_prefer_class_methods[rubocop/cop/prefer_class_methods_over_module.rb\nCompel use of class_methods instead of module ClassMethods]
      rubocop_active_record_reload[rubocop/cop/active_record_association_reload.rb\nBan reload; prefer reset]
      class rubocop_ignored_columns,rubocop_destroy_all,rubocop_active_model_errors,rubocop_default_scope,rubocop_user_admin,rubocop_file_decompression,rubocop_avoid_becomes,rubocop_sidekiq_api_usage,rubocop_static_translation_definition,rubocop_safe_params,rubocop_sidekiq_options_queue,rubocop_filename_length,rubocop_avoid_break_from_strong_memoize,rubocop_prefer_class_methods,rubocop_active_record_reload,error
    end

    %% --------- RuboCop Config Generators & Utility -------
    subgraph RUBOCOP_UTILS["RuboCop Utilities & Automation"]
      class RUBOCOP_UTILS group
      keeps_generate_rubocop_todos[keeps/generate_rubocop_todos.rb\nAutomated RuboCop TODO generator]
      rubocop_gitlab_housekeeper_fixer[gems/gitlab-housekeeper/lib/gitlab/housekeeper/keeps/rubocop_fixer.rb\nHousekeeper RuboCop autocorrect & cleanup]
      gitlab_housekeeper_shell[gems/gitlab-housekeeper/lib/gitlab/housekeeper/shell.rb\nShell helper for RuboCop tasks]
      class keeps_generate_rubocop_todos,rubocop_gitlab_housekeeper_fixer,gitlab_housekeeper_shell,support
    end

    %% --------- Danger & Tooling Extensions -----------
    subgraph TOOLING["Danger & Inline RuboCop Support"]
      class TOOLING group
      tooling_rubocop_inline_disable[tooling/danger/rubocop_inline_disable_suggestion.rb\nSuggests reasons for inline disables]
      tooling_gettext_extractor[tooling/lib/tooling/gettext_extractor.rb\nExtracts translatable strings from sources]
      class tooling_rubocop_inline_disable,tooling_gettext_extractor,support
    end

    %% --------- RSpec Support, Validators --------
    subgraph RSPEC_VALIDATORS["RSpec Validator Specs"]
      class RSPEC_VALIDATORS group
      rsa_key_validator_spec[spec/validators/rsa_key_validator_spec.rb\nSpec for RSA key model validator]
      rspec_rubocop_sidekiq_queue_migrate_spec[spec/rubocop/cop/migration/sidekiq_queue_migrate_spec.rb\nTest for Sidekiq queue-migrate cop]
      rspec_rubocop_ignored_columns_spec[spec/rubocop/cop/ignored_columns_spec.rb\nTest for ignored columns rubocop cop]
      rspec_rubocop_remove_column_spec[spec/rubocop/cop/migration/remove_column_spec.rb\nTest for migration RemoveColumn cop]
      rspec_rubocop_duplicate_spec_location_spec[spec/rubocop/cop/rspec/duplicate_spec_location_spec.rb\nTest for duplicate spec file checker cop]
      rspec_rubocop_inline_association_spec[spec/rubocop/cop/rspec/factory_bot/inline_association_spec.rb\nTests FactoryBot inline association cop]
      rspec_rubocop_feature_category_spec[spec/rubocop/cop/rspec/feature_category_spec.rb\nTests RSpec feature category cop]
      rspec_rubocop_namespaced_class_spec[spec/rubocop/cop/gitlab/namespaced_class_spec.rb\nTests namespaced class cop]
      rspec_rubocop_mark_used_feature_flags_spec[spec/rubocop/cop/gitlab/mark_used_feature_flags_spec.rb\nTests feature-flag usage tracking cop]
      rspec_rubocop_update_column_in_batches_spec[spec/rubocop/cop/migration/update_column_in_batches_spec.rb\nTest for update_column_in_batches cop]
      class rsa_key_validator_spec,rspec_rubocop_sidekiq_queue_migrate_spec,rspec_rubocop_ignored_columns_spec,rspec_rubocop_remove_column_spec,rspec_rubocop_duplicate_spec_location_spec,rspec_rubocop_inline_association_spec,rspec_rubocop_feature_category_spec,rspec_rubocop_namespaced_class_spec,rspec_rubocop_mark_used_feature_flags_spec,rspec_rubocop_update_column_in_batches_spec,error
    end
  end

%% ============= RELATIONSHIPS & DEPENDENCIES =============

%% DATA STRUCTURES / HELPERS
rubocop_code_reuse_helpers --> rubocop_code_reuse_cops
rubocop_migration_helpers --> **********************
rubocop_migration_helpers --> rubocop_background_migration_cops
rubocop_migration_helpers --> rubocop_usage_data_cops
rubocop_migration_helpers --> rubocop_database_cops
rubocop_code_reuse_helpers --> rubocop_gitlab_cops
rubocop_code_reuse_helpers --> rubocop_api_cops

%% CUSTOM COPS CENTRAL NODE
rubocop_gitlab_cops --> rubocop_gitlab_rails_cops
rubocop_gitlab_cops --> rubocop_qa_cops
rubocop_gitlab_cops --> rubocop_gitlab_housekeeper_fixer
rubocop_code_reuse_cops --> rubocop_gitlab_cops
rubocop_code_reuse_cops --> rubocop_code_reuse_helpers

rubocop_code_reuse_cops --> rubocop_rspec_cops
rubocop_code_reuse_helpers --> rubocop_rspec_cops
rubocop_code_reuse_cops --> rubocop_usage_data_cops

rubocop_gitlab_cops --> rubocop_style_cops
rubocop_gitlab_cops --> rubocop_graphql_cops

rubocop_style_cops --> tooling_rubocop_inline_disable

%% RSpec-specific custom cops
rubocop_rspec_cops --> rspec_rubocop_sidekiq_queue_migrate_spec
rubocop_rspec_cops --> rspec_rubocop_remove_column_spec
rubocop_rspec_cops --> rspec_rubocop_duplicate_spec_location_spec
rubocop_rspec_cops --> rspec_rubocop_inline_association_spec
rubocop_rspec_cops --> rspec_rubocop_feature_category_spec

********************** --> rspec_rubocop_sidekiq_queue_migrate_spec
********************** --> rspec_rubocop_remove_column_spec
********************** --> rspec_rubocop_update_column_in_batches_spec

rubocop_ignored_columns --> rspec_rubocop_ignored_columns_spec
rubocop_gitlab_cops --> rubocop_ignored_columns

rubocop_gitlab_cops --> rubocop_user_admin

%% VALIDATORS: domain-driven validation tie-in
feature_flag_validator --> rubocop_gitlab_cops
feature_flag_validator --> rubocop_rspec_cops
addressable_url_validator --> rubocop_gitlab_cops
abstract_path_validator --> rubocop_gitlab_cops
html_safety_validator --> rubocop_gitlab_cops
cron_validator --> rubocop_gitlab_cops
duration_validator --> rubocop_gitlab_cops

rsa_key_validator_spec --> feature_flag_validator
rsa_key_validator_spec --> rubocop_gitlab_cops

%% RUBOCOP UTILITIES / TOOLING
rubocop_gitlab_housekeeper_fixer --> keeps_generate_rubocop_todos
rubocop_gitlab_housekeeper_fixer --> gitlab_housekeeper_shell

keeps_generate_rubocop_todos --> rubocop_gitlab_housekeeper_fixer
keeps_generate_rubocop_todos --> rubocop_rspec_cops
keeps_generate_rubocop_todos --> **********************

tooling_rubocop_inline_disable --> rubocop_style_cops

tooling_gettext_extractor --> rubocop_static_translation
tooling_gettext_extractor --> rubocop_gitlab_cops

%% QA & DANGER INTEGRATION
rubocop_qa_cops --> rubocop_gitlab_cops

%% DATA STRUCTURE TRANSFORMATIONS (example: gettext extractor uses helpers)
tooling_gettext_extractor --> rubocop_code_reuse_helpers

%% SPECIAL: Sibling & Collaboration dependencies
rubocop_background_migration_cops --> rubocop_usage_data_cops
rubocop_background_migration_cops --> rubocop_migration_helpers

rubocop_gitlab_rails_cops --> rubocop_gitlab_cops

rubocop_database_cops --> rubocop_code_reuse_helpers
rubocop_database_cops --> rubocop_migration_helpers

rubocop_rspec_cops --> rubocop_gitlab_cops
rubocop_rspec_cops --> **********************

rubocop_usage_data_cops --> rubocop_migration_helpers
rubocop_usage_data_cops --> rubocop_code_reuse_helpers

rubocop_static_translation_definition --> rubocop_gitlab_cops
rubocop_static_translation_definition --> rubocop_style_cops

rubocop_graphql_cops --> rubocop_style_cops

rubocop_api_cops --> rubocop_code_reuse_helpers

rubocop_performance_cops --> rubocop_gitlab_cops

rubocop_qa_cops --> rubocop_code_reuse_helpers

********************** --> rubocop_code_reuse_helpers

rubocop_active_model_errors --> rubocop_gitlab_cops
rubocop_destroy_all --> rubocop_code_reuse_helpers

rubocop_safe_params --> rubocop_gitlab_rails_cops

%% NESTED: Many validation and custom cops depend on general rubocop organization
rubocop_file_decompression --> rubocop_code_reuse_cops
rubocop_default_scope --> rubocop_code_reuse_helpers
rubocop_user_admin --> rubocop_code_reuse_helpers
rubocop_avoid_becomes --> rubocop_code_reuse_cops

%% Linting rules inform testing, QA, and spec setup
rubocop_rspec_cops --> rubocop_qa_cops
********************** --> rubocop_qa_cops
rubocop_code_reuse_cops --> rubocop_qa_cops

%% SPECIALIST TEST CONNECTIONS FOR DOMAIN SAFETY
rubocop_migration_helpers --> rubocop_sidekiq_cops
rubocop_migration_helpers --> **********************

rubocop_gitlab_cops --> rubocop_sidekiq_cops
********************** --> rubocop_sidekiq_cops

%% Feature flag safety is central
feature_flag_validator --> rubocop_usage_data_cops
feature_flag_validator --> rubocop_qa_cops

%% End of diagram
```