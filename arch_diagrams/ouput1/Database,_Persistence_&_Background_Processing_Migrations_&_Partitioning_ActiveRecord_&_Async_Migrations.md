```mermaid
flowchart TD
  %% LAYOUT & STYLE SETUP
  %% Vertical layout, pastel colors, strict node grouping
  %% Color legend:
  %% - Core domain files: #D4F1F9
  %% - Utilities/supporting: #FFF8DC
  %% - Data structures: #E0F8E0
  %% - Error handling: #FFE4E1
  %% - Initialization/setup/boot: #E6E6FA
  %% - Subgraphs: #F8F8F8
  %% Node shape default: rounded rectangle

  %% --------- SUBGRAPH: ActiveRecord Models AND Abstractions ---------
  subgraph ARMODELS [ActiveRecord Core: Inheritance, Partitioning]
    direction TB
    style ARMODELS fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded
    AR_AppRec["ApplicationRecord":::core]
    AR_CiAppRec["Ci::ApplicationRecord":::core]
  end

  classDef core fill:#D4F1F9,stroke:#76b8e4,stroke-width:2,stroke-dasharray:2 4,rx:10,ry:10
  class AR_AppRec,AR_CiAppRec core

  %% --------- SUBGRAPH: ActiveRecord Initialization & Customization ---------
  subgraph ARINIT [ActiveRecord Initialization & Patches]
    direction TB
    style ARINIT fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2,rounded
    AR_Enum["00_active_record_enum.rb":::init]
    AR_DisableXDB["00_active_record_disable_cross_database.rb":::init]
    AR_DataTypes["1_active_record_data_types.rb":::init]
    AR_PostgreTypes["0_postgresql_types.rb":::init]
    AR_Relation["active_record_relation.rb":::init]
    AR_TableDef["active_record_table_definition.rb":::init]
    AR_Preloader["active_record_preloader.rb":::init]
    AR_SkipTX["skip_transaction_checks.rb":::init]
    AR_Pagination["active_record_keyset_pagination.rb":::init]
    AR_Cte["postgresql_cte.rb":::init]
    AR_CteMat["postgres_cte_as_materialized.rb":::init]
    AR_SpeedUpCheck["ar_speed_up_migration_checking.rb":::init]
  end

  classDef init fill:#E6E6FA,stroke:#baa5ed,stroke-width:2,rx:20,ry:20

  class AR_Enum,AR_DisableXDB,AR_DataTypes,AR_PostgreTypes,AR_Relation,AR_TableDef,AR_Preloader,AR_SkipTX,AR_Pagination,AR_Cte,AR_CteMat,AR_SpeedUpCheck init

  %% --------- SUBGRAPH: Partitioning & Migration Helpers ---------
  subgraph PART [Partitioning, Migration & Database Utilities]
    direction TB
    style PART fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded
    Part_Helpers["partitioning_migration_helpers.rb":::core]
    Part_BulkCopy["partitioning_migration_helpers/bulk_copy.rb":::utils]
    Part_Backfill["partitioning_migration_helpers/backfill_partitioned_table.rb":::core]
    DB_RenameHelper["rename_table_helpers.rb":::utils]
    DB_MarkMigrationSvc["mark_migration_service.rb":::utils]
    DB_InitializerConn["initializer_connections.rb":::init]
    DB_TablesSorted["tables_sorted_by_foreign_keys.rb":::utils]
  end

  classDef utils fill:#FFF8DC,stroke:#e5e399,stroke-width:2,rx:10,ry:10

  class Part_BulkCopy,DB_RenameHelper,DB_MarkMigrationSvc,DB_InitializerConn,DB_TablesSorted utils

  %% --------- SUBGRAPH: Background Migration Architecture ---------
  subgraph BGMIG [Background Migration Core]
    direction TB
    style BGMIG fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded
    BGM_BaseJob["background_migration/base_job.rb":::core]
    BGM_BatchJob["background_migration/batched_migration_job.rb":::core]
    BGM_Runner["background_migration/batched_migration_runner.rb":::core]
    BGM_ProjNS_BF["background_migration/project_namespaces/backfill_project_namespaces.rb":::core]
  end

  class BGM_BaseJob,BGM_BatchJob,BGM_Runner,BGM_ProjNS_BF core

  %% --------- SUBGRAPH: Async Index Management ---------
  subgraph ASYNCIDX [Async Index Creation & Destruction]
    direction TB
    style ASYNCIDX fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded
    AI_Main["async_indexes.rb":::core]
    AI_Creator["async_indexes/index_creator.rb":::core]
    AI_Destructor["async_indexes/index_destructor.rb":::core]
  end
  
  class AI_Main,AI_Creator,AI_Destructor core

  %% --------- SUBGRAPH: Schema and Migration Process ---------
  subgraph DBSCHEMA [Schema Migration & Validation]
    direction TB
    style DBSCHEMA fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded
    SM_Main["schema_migrations.rb":::core]
    SM_Context["schema_migrations/context.rb":::core]
    SM_Migrations["schema_migrations/migrations.rb":::core]
    SV_Filter["schema_validation/inconsistency_filter.rb":::utils]
    DB_Cleaner["schema_cleaner.rb":::utils]
  end

  class SM_Main,SM_Context,SM_Migrations core
  class SV_Filter,DB_Cleaner utils

  %% --------- SUBGRAPH: Background Migration Types ---------
  subgraph BGM_TYPES [Background Migration Strategies]
    direction TB
    style BGM_TYPES fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded
    BGM_DesiredSKJob["background_migration/backfill_desired_sharding_key_job.rb":::core]
    BGM_DesiredSKPartJob["background_migration/backfill_desired_sharding_key_partition_job.rb":::core]
    BGM_BatchMigJob["background_migration/batched_migration_job.rb":::core]
    BGM_BackfillPt["background_migration/backfill_partitioned_table.rb":::core]
  end

  class BGM_DesiredSKJob,BGM_DesiredSKPartJob,BGM_BatchMigJob,BGM_BackfillPt core

  %% --------- SUBGRAPH: Background Migration Job Implementations ---------
  subgraph BGM_IMP [Specific Background Migration Jobs]
    direction TB
    style BGM_IMP fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded
    %% Showcase a representative sample; links are for relationship structure
    BGM_BackfillNSID["backfill_namespaces_redirect_routes_namespace_id.rb":::core]
    BGM_BackfillProjNSID["backfill_projects_redirect_routes_namespace_id.rb":::core]
    BGM_BackfillProjRepo["backfill_project_repositories.rb":::core]
    BGM_BackfillIssuesWIDS["backfill_issues_dates_with_work_item_dates_sources.rb":::core]
    BGM_BackfillEpicsWI["backfill_epic_basic_fields_to_work_item_record.rb":::core]
    BGM_BackfillRelatedEpic["backfill_related_epic_links_to_issue_links.rb":::core]
    BGM_BackfillPartitionID["backfill_partition_id_ci_pipeline_message.rb":::core]
    BGM_BackfillResWgtEV["backfill_resource_weight_events_namespace_id.rb":::core]
    BGM_BackfillSBOM["backfill_sbom_occurrences_vulnerabilities_project_id.rb":::core]
    BGM_BackfillStories["backfill_lists_sharding_key.rb":::core]
    BGM_BackfillUsersCM["backfill_users_color_mode_id.rb":::core]
    BGM_BackfillCIRes["backfill_ci_resources_project_id.rb":::core]
    BGM_BackfillJobVars["backfill_ci_job_variables_project_id.rb":::core]
    BGM_BackfillSecFind["backfill_security_findings_project_id.rb":::core]
    BGM_BackfillProjFeat["backfill_project_feature_package_registry_access_level.rb":::core]
    %% ... more would go here as rectangles; focus is on representing variety and their relation
  end

  class BGM_BackfillNSID,BGM_BackfillProjNSID,BGM_BackfillProjRepo,BGM_BackfillIssuesWIDS,BGM_BackfillEpicsWI,BGM_BackfillRelatedEpic,BGM_BackfillPartitionID,BGM_BackfillResWgtEV,BGM_BackfillSBOM,BGM_BackfillStories,BGM_BackfillUsersCM,BGM_BackfillCIRes,BGM_BackfillJobVars,BGM_BackfillSecFind,BGM_BackfillProjFeat core

  %% --------- SUBGRAPH: Migration Runner, Process & Orchestration ---------
  subgraph RUNNERS [Migration Process Orchestration]
    direction TB
    style RUNNERS fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded
    Mig_Runner["migrations/runner.rb":::core]
    Mig_Timeout["migrations/timeout_helpers.rb":::utils]
    Mig_SwapCols["migrations/swap_columns.rb":::utils]
    Mig_SwapColsDef["migrations/swap_columns_default.rb":::utils]
    Mig_Squasher["migrations/squasher.rb":::utils]
    Mig_PGBackPID["migrations/pg_backend_pid.rb":::utils]
    Mig_RedisHelpers["migrations/redis_helpers.rb":::utils]
    Mig_ReestConnStack["migrations/reestablished_connection_stack.rb":::utils]
    Mig_RunnerBackComm["migrations/runner_backoff/communicator.rb":::utils]
    Mig_RunnerBackMixin["migrations/runner_backoff/active_record_mixin.rb":::utils]
  end

  class Mig_Runner core
  class Mig_Timeout,Mig_SwapCols,Mig_SwapColsDef,Mig_Squasher,Mig_PGBackPID,Mig_RedisHelpers,Mig_ReestConnStack,Mig_RunnerBackComm,Mig_RunnerBackMixin utils

  %% --------- SUBGRAPH: Load Balancing ---------
  subgraph LB [Database Load Balancing]
    direction TB
    style LB fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded
    LB_Main["load_balancing.rb":::core]
    LB_Session["load_balancing/session.rb":::core]
    LB_PrimaryHost["load_balancing/primary_host.rb":::core]
    LB_HostList["load_balancing/host_list.rb":::core]
    LB_Resolver["load_balancing/resolver.rb":::utils]
    LB_Sampler["load_balancing/service_discovery/sampler.rb":::utils]
    LB_SidekiqMW["load_balancing/sidekiq_server_middleware.rb":::utils]
    LB_ActionCable["load_balancing/action_cable_callbacks.rb":::utils]
    LB_Logger["load_balancing/logger.rb":::utils]
  end

  class LB_Main,LB_Session,LB_PrimaryHost,LB_HostList core
  class LB_Resolver,LB_Sampler,LB_SidekiqMW,LB_ActionCable,LB_Logger utils

  %% --------- SUBGRAPH: Encryption Helpers ---------
  subgraph ENCRYPT [Encryption Key Providers]
    direction TB
    style ENCRYPT fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,rounded
    Encr_KeyProvider["encryption/key_provider.rb":::utils]
    Encr_KeyProviderWrap["encryption/key_provider_wrapper.rb":::utils]
    Encr_NonDerived["encryption/non_derived_key_provider.rb":::utils]
  end

  class Encr_KeyProvider,Encr_KeyProviderWrap,Encr_NonDerived utils

  %% --------- SUBGRAPH: Data Structure Definitions ---------
  subgraph DS [Data Structure Layers]
    direction TB
    style DS fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2,rounded
    DS_SecAppRecord["app/models/application_record.rb":::ds]
    DS_CiSecAppRecord["app/models/ci/application_record.rb":::ds]
    DS_PostgresVac["postgres_autovacuum_activity.rb":::ds]
  end

  classDef ds fill:#E0F8E0,stroke:#a5dbb1,stroke-width:2,rx:10,ry:10

  class DS_SecAppRecord,DS_CiSecAppRecord,DS_PostgresVac ds

  %% Relationships - Connecting the logical layers

  %% ActiveRecord Models to Initializer, Partitioning, and Data Structures
  AR_AppRec -->|is base for| AR_CiAppRec
  AR_AppRec -- Configures --> ARINIT
  AR_CiAppRec -- Partitioning/DB Routing --> PART

  %% Partitioning helpers reference data structure files for direct DB access
  PART -- Uses AR base --> AR_AppRec
  Part_BulkCopy -- Relies on helpers --> Part_Helpers
  Part_Backfill -- Extends base job --> BGM_BatchJob
  Part_Backfill -- EachBatch pattern --> BGM_BatchJob

  %% ActiveRecord customizations used across the codebase
  ARINIT -- Modifies AR behaviors --> AR_AppRec
  ARINIT -- Extended by --> BGMIG
  ARINIT -- Supports --> DBSCHEMA

  %% AsyncIndex system orchestrates index management for DB changes
  AI_Main -- Orchestrates --> AI_Creator
  AI_Main -- Orchestrates --> AI_Destructor
  AI_Creator -- Called by --> AI_Main
  AI_Destructor -- Called by --> AI_Main
  AI_Main -- Utilizes --> AR_AppRec

  %% Schema Migration orchestrates & validates database schemas
  SM_Main -- Uses context --> SM_Context
  SM_Main -- Manages migrations --> SM_Migrations
  SM_Migrations -- Loads context --> SM_Context
  SM_Main -- Used by --> DBSCHEMA
  DBSCHEMA -- Cleans up --> DB_Cleaner
  DBSCHEMA -- Filters inconsistencies --> SV_Filter

  %% Background Migration Job Architecture
  BGM_Runner -- Uses --> BGM_BaseJob
  BGM_BatchJob -- Inherits --> BGM_BaseJob
  BGM_BatchJob -- Applied by --> BGM_TYPES
  BGM_ProjNS_BF -- Helper for projects --> BGM_BatchJob

  %% Migration runners orchestrate job strategies
  RUNNERS -->|runs| BGM_BatchJob
  BGM_Runner -->|used by| RUNNERS
  Mig_Timeout -- Used in --> RUNNERS
  Mig_SwapCols -- Used for column migration in --> RUNNERS
  Mig_Squasher -- Squashes migrations in --> RUNNERS
  Mig_RunnerBackComm -- Coordinates backoff in --> RUNNERS

  %% BackgroundJob types are extended by specific implementations
  BGM_DesiredSKPartJob -- Extends --> BGM_DesiredSKJob
  BGM_DesiredSKJob -- Extends --> BGM_BatchJob
  BGM_BackfillPt -- Extends --> BGM_BatchJob

  %% Concrete background migration jobs are based on job types
  BGM_BackfillNSID -- Extends --> BGM_DesiredSKJob
  BGM_BackfillProjNSID -- Extends --> BGM_DesiredSKJob
  BGM_BackfillProjRepo -- Extends --> BGM_BatchJob
  BGM_BackfillEpicsWI -- Extends --> BGM_BatchJob
  BGM_BackfillRelatedEpic -- Extends --> BGM_BatchJob
  BGM_BackfillPartitionID -- Extends --> BGM_BatchMigJob
  BGM_BackfillSBOM -- Extends --> BGM_DesiredSKJob
  BGM_BackfillResWgtEV -- Extends --> BGM_DesiredSKJob
  BGM_BackfillStories -- Extends --> BGM_BatchMigJob
  BGM_BackfillUsersCM -- Extends --> BGM_BatchMigJob
  BGM_BackfillCIRes -- Extends --> BGM_DesiredSKJob

  %% Link representative concrete jobs to their base job patterns for clarity
  BGM_IMP -- "Inherits from" --> BGM_TYPES

  %% Partitioning helpers rely on batched migration jobs for data movement
  Part_Backfill -- Schedules --> Part_BulkCopy

  %% Load balancing applies to all DB access patterns models/utilities
  AR_AppRec -- "Uses DB load balancing" --> LB
  AR_CiAppRec -- "Uses DB load balancing" --> LB
  PART -- "Uses Load Balancer" --> LB
  RUNNERS -- "May use" --> LB

  %% Encryption helpers injected into AR for security enhancements
  ENCRYPT -- "Used by models needing encryption" --> AR_AppRec
  ENCRYPT -- "Applied by" --> BGM_BaseJob

  %% Data Structures and models
  AR_AppRec -- "Model base: AR ORM" --> DS_SecAppRecord
  AR_CiAppRec -- "Model base: AR ORM" --> DS_CiSecAppRecord

  %% Utility helpers used broadly
  DB_RenameHelper -- Used by --> SM_Migrations
  DB_MarkMigrationSvc -- Used by --> RUNNERS
  DB_TablesSorted -- Used by --> DBSCHEMA
  AR_SkipTX -- Used by --> BGM_BaseJob

  %% Demo cross-link: Partitioned tables are backfilled via BackfillPt jobs
  Part_Backfill -- "Implements partitioned batch in" --> BGM_BackfillPt

  %% Bold subgraph connections for "vertical" reading order
  ARMODELS -.-> PART
  PART -.-> BGMIG
  BGMIG -.-> BGM_TYPES
  BGM_TYPES -.-> BGM_IMP
  BGMIG -.-> DBSCHEMA
  DBSCHEMA -.-> PART
  RUNNERS -.-> LB
  LB -.-> ENCRYPT
  ENCRYPT -.-> ARMODELS

  %% Legend (hidden)
  %% style LegendBox fill:#E0F8E0,stroke:#76b8e4,stroke-width:2
```