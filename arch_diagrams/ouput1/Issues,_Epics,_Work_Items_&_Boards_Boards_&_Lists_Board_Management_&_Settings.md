```mermaid
flowchart TD
  %% Styling
  %% Subgraph backgrounds
  classDef lightgray fill:#F8F8F8,color:#444,stroke-width:2px,stroke:#CCE3F6
  classDef borderblue stroke:#84D3ED
  classDef borderpurple stroke:#C9B4E8
  classDef borderyellow stroke:#F9E19B
  classDef borderred stroke:#F6B2A2
  classDef bordergreen stroke:#A1D89F
  %% File backgrounds
  classDef core fill:#D4F1F9,stroke:#84D3ED,stroke-width:2px,stroke-dasharray: 0
  classDef support fill:#FFF8DC,stroke:#F9E19B,stroke-width:2px,stroke-dasharray: 0
  classDef data fill:#E0F8E0,stroke:#A1D89F,stroke-width:2px,stroke-dasharray: 0
  classDef error fill:#FFE4E1,stroke:#F6B2A2,stroke-width:2px,stroke-dasharray: 0
  classDef init fill:#E6E6FA,stroke:#C9B4E8,stroke-width:2px,stroke-dasharray: 0

  %% DOMAIN ENTRYPOINTS (Controllers & API)
  subgraph Domain_EntryPoints["Domain Entrypoints"]
    direction TB
    PB[Projects::BoardsController]:::core
    BA[BoardsActions Controller Concern]:::support
    EEBA[EE::BoardsActions Controller Concern]:::support
    BPC(BoardPolicy):::support
    APIBoards(lib/api/boards.rb):::core
    APIGroupBoards(lib/api/group_boards.rb):::core
    APIBoardsRes(lib/api/boards_responses.rb):::support
    EEAPIBoardsRes(ee/lib/ee/api/boards_responses.rb):::support
    API_Topics(lib/api/topics.rb):::support
  end
  class Domain_EntryPoints lightgray borderblue

  %% MODELS & DOMAIN DATA
  subgraph Boards_Core["Boards, Epics, Lists, and Work Items"]
    direction TB
    Boardapp/models/board.rb:::core
    Todo(app/models/todo.rb):::core
    BUserPref(ee/app/models/board_user_preference.rb):::data
    BAssignee(ee/app/models/board_assignee.rb):::data
    BoardRecentVisit(app/models/concerns/board_recent_visit.rb):::support
    Sortable(app/models/concerns/sortable.rb):::support
    SortableTitle(app/models/concerns/sortable_title.rb):::support
    EEBoard(ee/app/models/ee/board.rb):::core
    EpicBoard(ee/app/models/boards/epic_board.rb):::core
    EpicListUserPref(ee/app/models/boards/epic_list_user_preference.rb):::data
    EpicUserPref(ee/app/models/boards/epic_user_preference.rb):::data
    EpicBoardLabel(ee/app/models/boards/epic_board_label.rb):::data
    EpicBoardPosition(ee/app/models/boards/epic_board_position.rb):::data
  end
  class Boards_Core lightgray borderblue

  %% LISTS & POSITIONS, PREFERENCES
  subgraph List_Management["Lists, List Metadata, Positions, Preferences"]
    direction TB
    ILMetadataee/app/graphql/types/boards/epic_list_metadata_type.rb:::data
    EpicListPolicy(ee/app/policies/boards/epic_list_policy.rb):::support
    EpicBoardsFinder(ee/app/finders/boards/milestones_finder.rb):::support
  end
  class List_Management lightgray borderblue

  %% PRESENTERS & SERIALIZERS
  subgraph Presentation["Presenters and Serializers"]
    direction TB
    BoardPresenterapp/presenters/board_presenter.rb:::support
    IssueBoardEntity(app/serializers/issue_board_entity.rb):::support
    EE_IssueBoardEntity(ee/app/serializers/ee/issue_board_entity.rb):::support
    IterationEntity(ee/app/serializers/autocomplete/iteration_entity.rb):::support
  end
  class Presentation lightgray borderyellow

  %% FINDERS
  subgraph Finders["Finders"]
    direction TB
    BoardsFinderapp/finders/boards/boards_finder.rb:::support
    BVFinder(app/finders/boards/visits_finder.rb):::support
    TimelogsFinder(app/finders/timelogs/timelogs_finder.rb):::support
  end
  class Finders lightgray borderyellow

  %% DOMAIN SERVICES
  subgraph Services["Services, Domain Actions, & Mutations"]
    direction TB
    BaseServiceapp/services/boards/base_service.rb:::core
    CreateService(app/services/boards/create_service.rb):::core
    UpdateService(app/services/boards/update_service.rb):::core
    BoardListsUpdate(app/services/boards/lists/update_service.rb):::core
    BoardIssuesList(app/services/boards/issues/list_service.rb):::core
    BoardIssuesMove(app/services/boards/issues/move_service.rb):::core
    BaseItemMove(app/services/boards/base_item_move_service.rb):::core

    EpicListsUpdate(ee/app/services/boards/epic_lists/update_service.rb):::core
    EpicBoardsDestroy(ee/app/services/boards/epic_boards/destroy_service.rb):::core
    EpicMove(ee/app/services/boards/epics/move_service.rb):::core
    EpicPosCreate(ee/app/services/boards/epics/position_create_service.rb):::core
    EpicUserPrefUpdate(ee/app/services/boards/epic_user_preferences/update_service.rb):::core
    UserPrefUpdate(ee/app/services/boards/user_preferences/update_service.rb):::core

    BVisitsCreate(app/services/boards/visits/create_service.rb):::support

    EEBoardCreate(ee/app/services/ee/boards/create_service.rb):::core
    EEBoardUpdate(ee/app/services/ee/boards/update_service.rb):::core
    EEListUpdate(ee/app/services/ee/boards/lists/update_service.rb):::core
    EEIssueMove(ee/app/services/ee/boards/issues/move_service.rb):::core
  end
  class Services lightgray borderpurple

  %% GRAPHQL
  subgraph GraphQL["GraphQL: Mutations and Resolvers"]
    direction TB
    GQ_Mut_BoardCreateee/app/graphql/ee/mutations/boards/create.rb:::core
    GQ_Mut_IssueMove(ee/app/graphql/ee/mutations/boards/issues/issue_move_list.rb):::core
    GQ_Mut_ScopedMgmt(ee/app/graphql/mutations/boards/scoped_board_mutation.rb):::support
    GQ_Mut_ScopedArgs(ee/app/graphql/mutations/boards/scoped_issue_board_arguments.rb):::support
    GQ_Mut_UserPref(ee/app/graphql/mutations/boards/update_epic_user_preferences.rb):::support
    GQ_Res_EpicBoards(ee/app/graphql/resolvers/boards/epic_boards_resolver.rb):::support
  end
  class GraphQL lightgray borderpurple

  %% HELPERS
  subgraph Helpers["Helpers"]
    direction TB
    BoardsHelperee/app/helpers/ee/boards_helper.rb:::support
  end
  class Helpers lightgray borderyellow

  %% BULK IMPORT
  subgraph Bulk_Import["Bulk Import Pipelines"]
    direction TB
    BoardsPipeline(lib/bulk_imports/common/pipelines/boards_pipeline.rb):::support
  end
  class Bulk_Import lightgray bordergreen

  %% CONNECTIONS

  %% ENTRYPOINTS to CONTROLLER CONCERNS
  PB -- uses --> BA
  PB -- uses (EE features) --> EEBA

  APIBoards -- include --> APIBoardsRes
  APIGroupBoards -- include --> APIBoardsRes
  APIBoardsRes -- EE overrides --> EEAPIBoardsRes

  %% CONTROLLER to SERVICES
  PB -- calls --> CreateService
  PB -- calls --> UpdateService
  PB -- calls --> BoardListsUpdate
  PB -- calls --> BoardIssuesList
  PB -- calls --> BoardIssuesMove

  %% API to SERVICES
  APIBoardsRes -- uses --> CreateService
  APIBoardsRes -- uses --> UpdateService
  APIBoardsRes -- uses --> BoardListsUpdate
  APIBoardsRes -- uses --> BoardIssuesList

  %% GRAPHQL Mutations/Resolvers to SERVICES
  GQ_Mut_BoardCreate -- uses --> CreateService
  GQ_Mut_IssueMove -- uses --> BoardIssuesMove
  GQ_Res_EpicBoards -- fetches --> EpicBoard

  %% CONTROLLER & SERVICES: Policy
  PB -- policy-checks --> BPC

  %% DOMAIN MODELS to CONCERNS
  Board -- include --> BoardRecentVisit
  Board -- uses --> Sortable
  Todo -- include --> Sortable
  Todo -- uses --> SortableTitle

  EpicBoard -- uses --> EpicBoardLabel
  EpicBoard -- uses --> EpicBoardPosition
  EpicBoard -- uses --> EpicListUserPref
  EpicBoard -- uses --> EpicUserPref

  EpicBoardPosition -- include --> Sortable

  %% Service Hierarchy / Inheritance / Use
  CreateService -- inherits --> BaseService
  UpdateService -- inherits --> BaseService
  BoardListsUpdate -- inherits --> BaseService
  BoardIssuesList -- inherits --> BaseService
  BoardIssuesMove -- inherits --> BaseItemMove
  BaseItemMove -- inherits --> BaseService

  EpicListsUpdate -- inherits --> BoardListsUpdate
  EpicBoardsDestroy -- inherits --> CreateService
  EpicMove -- inherits --> BaseItemMove
  EpicPosCreate -- inherits --> BaseService

  %% EE Service Extensions
  EEBoardCreate -- overrides --> CreateService
  EEBoardUpdate -- overrides --> UpdateService
  EEListUpdate -- overrides --> BoardListsUpdate
  EEIssueMove -- overrides --> BoardIssuesMove

  EpicUserPrefUpdate -- manages --> EpicUserPref
  UserPrefUpdate -- manages --> BUserPref

  BVisitsCreate -- manages --> BoardRecentVisit

  %% PRESENTATION & SERIALIZATION
  PB -- render --> BoardPresenter
  BoardIssuesList -- renders via --> IssueBoardEntity
  IssueBoardEntity -- extended_by --> EE_IssueBoardEntity
  Board -- serialized via --> BoardPresenter
  Board -- serialized via --> IssueBoardEntity
  EpicBoard -- serialized via --> BoardPresenter

  IterationEntity -- used_in --> ILMetadata

  %% FINDERS - used in services
  BoardsFinder -- used_by --> CreateService
  BVFinder -- used_by --> BVisitsCreate
  TimelogsFinder -- used_by --> BoardIssuesList

  EpicBoardsFinder -- used_by --> EpicBoard

  %% POLICY links
  EpicListPolicy -- enforces --> EpicBoard

  %% Helpers
  BoardsHelper -- used_by --> PB
  BoardsHelper -- used_by --> BoardPresenter

  %% Bulk Import
  BoardsPipeline -- imports --> Board
  BoardsPipeline -- imports --> EpicBoard

  %% Data Structures -- usage & flow
  Board -- has_many --> Todo
  Board -- has_many --> EpicBoardPosition
  Board -- has_many --> EpicBoardLabel
  EpicBoard -- has_many --> EpicBoardPosition
  EpicBoard -- has_many --> EpicBoardLabel

  EpicBoard -- has_many --> EpicUserPref
  EpicBoard -- has_many --> EpicListUserPref

  %% Lists and List Metadata
  EpicBoard -- list_metadata --> ILMetadata

  %% GraphQL Arguments
  GQ_Mut_BoardCreate -- extends --> GQ_Mut_ScopedMgmt
  GQ_Mut_BoardCreate -- extends --> GQ_Mut_ScopedArgs

  GQ_Mut_UserPref -- updates --> EpicUserPref

  %% Testing (minimal links - demo relationships only; ignore in render)
  %% Ignore or comment out QA and spec/test files for focus

  %% Inter-EE and OSS Connections
  EEBoard -- prepends --> Board
  EEBoardCreate -- adds_features_to --> CreateService
  EEIssueMove -- adds_enterprise_logic_to --> BoardIssuesMove

  %% Final groupings for logical areas

  %% Style major concepts
  class PB,APIBoards,APIGroupBoards,APIBoardsRes,Board,Todo,EEBoard,EpicBoard,CreateService,UpdateService,BoardListsUpdate,BoardIssuesList,BoardIssuesMove,BaseItemMove,EEBoardCreate,EEBoardUpdate,EEListUpdate,EEIssueMove,EpicListsUpdate,EpicBoardsDestroy,EpicMove,EpicPosCreate,GQ_Mut_BoardCreate,GQ_Mut_IssueMove core
  class BoardRecentVisit,Sortable,SortableTitle,BVFinder,TimelogsFinder,BoardsFinder,EpicBoardsFinder,BPC,EEBA,BA,BoardPresenter,IssueBoardEntity,EE_IssueBoardEntity,IterationEntity,BoardsHelper,GQ_Mut_ScopedMgmt,GQ_Mut_ScopedArgs,GQ_Mut_UserPref,GQ_Res_EpicBoards support
  class BUserPref,BAssignee,EpicListUserPref,EpicUserPref,EpicBoardLabel,EpicBoardPosition,ILMetadata data
  class EEAPIBoardsRes error
  class BoardsPipeline init
```