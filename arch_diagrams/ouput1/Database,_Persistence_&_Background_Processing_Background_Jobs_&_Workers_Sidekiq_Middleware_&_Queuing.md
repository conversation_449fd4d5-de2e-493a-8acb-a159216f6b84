```mermaid
%% VERTICAL Mermaid Diagram for: Database, Persistence & Background Jobs Domain
%% Pastel color styling and logical grouping per instructions
flowchart TD
  %% Core Domain Subgraph: Sidekiq Middleware Pipeline
  subgraph SIDEKIQ_MW["Sidekiq Middleware & Execution Pipeline"]
    direction TB
    style SIDEKIQ_MW fill:#F8F8F8,stroke:#78B6E1,stroke-width:2,rounded=true

    %% Core Domain Nodes Blue #D4F1F9
    SIDEKIQ_MW_ENTRY["Middleware Chain Entry Point":::core]
    GITLAB_SIDEKIQ_MW["lib/gitlab/sidekiq_middleware.rb\nMiddleware Pipeline Registry":::core]
    GITLAB_SIDEKIQ_QUEUE["lib/gitlab/sidekiq_queue.rb\nSidekiq Queue Operations":::core]
    GITLAB_SIDEKIQ_MIGRATE["lib/gitlab/********************.rb\nJob Migration Utilities":::core]

    %% Middleware Classes
    M_CLIENT_METRICS["lib/gitlab/sidekiq_middleware/client_metrics.rb\nClient Metrics":::core]
    M_SERVER_METRICS["lib/gitlab/sidekiq_middleware/server_metrics.rb\nServer Metrics":::core]
    M_MONITOR["lib/gitlab/sidekiq_middleware/monitor.rb\nJob Monitor":::core]
    M_REQUESTSTORE["lib/gitlab/sidekiq_middleware/request_store_middleware.rb\nRequestStore Context":::core]
    M_SKIPJOBS["lib/gitlab/sidekiq_middleware/skip_jobs.rb\nJob Skipping":::core]
    M_ARGUMENTS_LOGGER["lib/gitlab/sidekiq_middleware/arguments_logger.rb\nArguments Logging":::core]
    M_INSTRUMENT_LOGGER["lib/gitlab/sidekiq_middleware/instrumentation_logger.rb\nInstrumentation":::core]
    M_QUERY_ANALYZER["lib/gitlab/sidekiq_middleware/query_analyzer.rb\nDB Query Analyzing":::core]
    M_ADMINMODE_CLIENT["lib/gitlab/sidekiq_middleware/admin_mode/client.rb\nAdminMode Client":::core]
    M_ADMINMODE_SERVER["lib/gitlab/sidekiq_middleware/admin_mode/server.rb\nAdminMode Server":::core]
    M_IDENTITY_PASSTHROUGH["lib/gitlab/sidekiq_middleware/identity/passthrough.rb\nIdentity Carrier":::core]
    M_IDENTITY_RESTORE["lib/gitlab/sidekiq_middleware/identity/restore.rb\nIdentity Restore":::core]
    M_EXTRA_DONE_LOG["lib/gitlab/sidekiq_middleware/extra_done_log_metadata.rb\nDone Log Metadata":::core]
    M_SHARD_AWARENESS["lib/gitlab/sidekiq_middleware/shard_awareness_validator.rb\nShard Awareness":::core]
    M_SET_IP["lib/gitlab/sidekiq_middleware/set_ip_address.rb\nIP Address State":::core]

    %% Status Middleware
    M_STATUS_SERVER["lib/gitlab/sidekiq_status/server_middleware.rb\nStatus Tracking Server MW":::core]
    M_STATUS_CLIENT["lib/gitlab/sidekiq_status/client_middleware.rb\nStatus Tracking Client MW":::core]

    %% Concurrency Limit Middleware (Group)
    subgraph MW_CONCURRENCY["ConcurrencyLimit Middleware":::group]
      direction TB
      style MW_CONCURRENCY fill:#F8F8F8,stroke:#7FCBD8,stroke-width:2,rounded=true
      M_CONC_SLEEP["lib/gitlab/sidekiq_middleware/concurrency_limit/server.rb":::core]
      M_CONC_CLIENT["lib/gitlab/sidekiq_middleware/concurrency_limit/client.rb":::core]
      M_CONC_RESUME["lib/gitlab/sidekiq_middleware/concurrency_limit/resume.rb":::core]
    end

    %% Resource Usage Limit Middleware
    M_RESOURCE_LIMIT_SERVER["lib/gitlab/sidekiq_middleware/resource_usage_limit/server.rb\nResource Usage Limit Server MW":::core]
    M_RESOURCE_LIMIT_MW["lib/gitlab/sidekiq_middleware/resource_usage_limit/middleware.rb\nResource Usage Tracking MW":::core]

    %% Worker Context Middleware
    M_WORKER_CONTEXT_SERVER["lib/gitlab/sidekiq_middleware/worker_context/server.rb\nWorkerContext Server MW":::core]
    M_WORKER_CONTEXT_CLIENT["lib/gitlab/sidekiq_middleware/worker_context/client.rb\nWorkerContext Client MW":::core]
    M_WORKER_CONTEXT["lib/gitlab/sidekiq_middleware/worker_context.rb\nContext Utilities":::core]

    %% Duplicate Jobs Middleware
    M_DUPLICATE_SERVER["lib/gitlab/sidekiq_middleware/duplicate_jobs/server.rb\nDuplicate Jobs Server MW":::core]
    M_DUPLICATE_CLIENT["lib/gitlab/sidekiq_middleware/duplicate_jobs/client.rb\nDuplicate Jobs Client MW":::core]
    M_DUPLICATE_JOB["lib/gitlab/sidekiq_middleware/duplicate_jobs/duplicate_job.rb\nDuplicate Job Controller":::core]
    M_DUPLICATE_COOKIE["lib/gitlab/sidekiq_middleware/duplicate_jobs/cookie.rb\nDeduplication Cookie Logic":::core]
    M_DUPLICATE_STRATEGIES["lib/gitlab/sidekiq_middleware/duplicate_jobs/strategies.rb\nDeduplication Strategies":::core]
    M_DUPLICATE_BASE["lib/gitlab/sidekiq_middleware/duplicate_jobs/strategies/base.rb\nDedup Base":::core]
    M_DUPLICATE_WHEN_SCHED["lib/gitlab/sidekiq_middleware/duplicate_jobs/strategies/deduplicates_when_scheduling.rb":::core]
    M_DUPLICATE_UNTIL_EXECUTING["lib/gitlab/sidekiq_middleware/duplicate_jobs/strategies/until_executing.rb":::core]
    M_DUPLICATE_UNTIL_EXECUTED["lib/gitlab/sidekiq_middleware/duplicate_jobs/strategies/until_executed.rb":::core]
    M_DUPLICATE_NONE["lib/gitlab/sidekiq_middleware/duplicate_jobs/strategies/none.rb":::core]

    %% Size Limiter Middleware Server, Client, Validation
    M_SIZELIM_SERVER["lib/gitlab/sidekiq_middleware/size_limiter/server.rb\nSize Limit Server MW":::core]
    M_SIZELIM_CLIENT["lib/gitlab/sidekiq_middleware/size_limiter/client.rb\nSize Limit Client MW":::core]
    M_SIZELIM_COMPRESSOR["lib/gitlab/sidekiq_middleware/size_limiter/compressor.rb\nJob Payload Compressor":::core]
    M_SIZELIM_VALIDATOR["lib/gitlab/sidekiq_middleware/size_limiter/validator.rb\nPayload Limit Validator":::core]
    M_SIZELIM_EXCEED["lib/gitlab/sidekiq_middleware/size_limiter/exceed_limit_error.rb\nSize Limit Error":::error]

    %% Batch Loader Middleware
    M_BATCHLOADER["lib/gitlab/sidekiq_middleware/batch_loader.rb\nBatch Loader Middleware":::core]

    %% Pause Control Middleware
    subgraph MW_PAUSECONTROL["PauseControl Middleware":::group]
      direction TB
      style MW_PAUSECONTROL fill:#F8F8F8,stroke:#A39FC9,stroke-width:2,rounded=true
      M_PAUSE_SERVER["lib/gitlab/sidekiq_middleware/pause_control/server.rb\nPauseControl Server MW":::core]
      M_PAUSE_CLIENT["lib/gitlab/sidekiq_middleware/pause_control/client.rb\nPauseControl Client MW":::core]
      M_PAUSE_HANDLER["lib/gitlab/sidekiq_middleware/pause_control/strategy_handler.rb\nStrategy Handler":::core]
      M_PAUSE_SERVICE["lib/gitlab/sidekiq_middleware/pause_control/pause_control_service.rb\nPauseControl Service":::core]
      M_PAUSE_BASE["lib/gitlab/sidekiq_middleware/pause_control/strategies/base.rb\nBase Strategy":::core]
      M_PAUSE_NONE["lib/gitlab/sidekiq_middleware/pause_control/strategies/none.rb\nNone Strategy":::core]
      M_PAUSE_ADVSEARCH["lib/gitlab/sidekiq_middleware/pause_control/strategies/advanced_search.rb\nAdvancedSearch Strategy":::core]
      M_PAUSE_ZOEKT["lib/gitlab/sidekiq_middleware/pause_control/strategies/zoekt.rb\nZoekt Strategy":::core]
      M_PAUSE_CLICKHOUSE["lib/gitlab/sidekiq_middleware/pause_control/strategies/click_house_migration.rb\nClickHouseMigration Strategy":::core]
      M_PAUSE_DEPRECATED["lib/gitlab/sidekiq_middleware/pause_control/strategies/deprecated.rb\nDeprecated Strategy":::core]
      M_PAUSE_WORKERSMAP["lib/gitlab/sidekiq_middleware/pause_control/workers_map.rb\nPaused Workers Registry":::core]
      M_PAUSE_MAIN["lib/gitlab/sidekiq_middleware/pause_control.rb\nPauseControl Strategy Loader":::core]
    end

    %% Concurrency Limit Data Structure Logic
    subgraph MW_CONC_DATA["ConcurrencyLimit Data & Coordination":::group]
      direction TB
      style MW_CONC_DATA fill:#F8F8F8,stroke:#88C7AD,stroke-width:2,rounded=true
      M_CONC_SERVICE["lib/gitlab/sidekiq_middleware/concurrency_limit/concurrency_limit_service.rb\nConcurrency Limit Data Service":::data]
      M_CONC_WORKERSMAP["lib/gitlab/sidekiq_middleware/concurrency_limit/workers_map.rb\nWorkers Concurrency Registry":::data]
      M_CONC_QUEUE["lib/gitlab/sidekiq_middleware/concurrency_limit/queue_manager.rb\nJob Queue Manager":::data]
      M_CONC_MW["lib/gitlab/sidekiq_middleware/concurrency_limit/middleware.rb\nConcurrency MW Inner Logic":::core]
      M_CONC_TRACKER["lib/gitlab/sidekiq_middleware/concurrency_limit/worker_execution_tracker.rb\nJob Execution Tracker":::data]
    end

    %% Other Important Middleware
    M_STATUS["lib/gitlab/sidekiq_status.rb\nJob Status API":::core]
    M_RETRY_ERROR["lib/gitlab/sidekiq_middleware/retry_error.rb\nMiddleware RetryError Class":::error]

    %% Sidekiq Cluster Management
    SIDEKIQ_CLUSTER["sidekiq_cluster/sidekiq_cluster.rb\nSidekiq Cluster Mgmt":::core]

    %% Domain Specific Data/Utility Modules (green)
    D_EVENT_STORE["lib/gitlab/event_store/subscription.rb\nEvent Store Subscriptions":::data]
    D_SET_CACHE["lib/gitlab/set_cache.rb\nRedis-Backed SetCache":::data]

    %% Persistence and Background Job Utilities
    DB_WITH_LOCK_RETRIES["lib/gitlab/database/with_lock_retries.rb\nDB Lock Retries":::data]
    DB_WITH_LOCK_OUT_TX["lib/gitlab/database/with_lock_retries_outside_transaction.rb\nDB Lock Retries OutTx":::data]
    DB_LB_SIDEKIQ_CLIENT_MW["lib/gitlab/database/load_balancing/sidekiq_client_middleware.rb\nLoadBalancing / Shard-aware Jobs":::data]
    GITLAB_RED_WORKHORSE["lib/gitlab/redis/workhorse.rb\nRedis Connection: Workhorse":::data]
    GITLAB_TASK_HELPERS["lib/gitlab/task_helpers.rb\nGeneric Task Utils":::support]

    %% ClickHouse Migration Support
    CLICKHOUSE_MW["lib/click_house/migration_support/sidekiq_middleware.rb\nClickHouse Migration MW":::core]

    %% Logging/Deduplication Utilities
    LOG_DEDUP["lib/gitlab/sidekiq_logging/deduplication_logger.rb\nDeduplication Job Logging":::support]

    %% Middleware Helper
    METRICS_HELPER["lib/gitlab/sidekiq_middleware/metrics_helper.rb\nMetrics Helper Utilities":::support]

    %% Error File
    PUMA_ERROR_HANDLER["lib/gitlab/puma/error_handler.rb\nPuma Error Handler":::error]

    %% Sidekiq Signal/Process Helpers
    GITLAB_SIDEKIQ_SIGNALS["lib/gitlab/sidekiq_signals.rb\nSidekiq Process/Signal Handler":::core]

    %% Group styles
    classDef core fill:#D4F1F9,stroke:#79BDD4,stroke-width:1,rounded=true
    classDef support fill:#FFF8DC,stroke:#E4CF88,stroke-width:1,rounded=true
    classDef data fill:#E0F8E0,stroke:#A2CBA2,stroke-width:1,rounded=true
    classDef error fill:#FFE4E1,stroke:#F2B4B2,stroke-width:1,rounded=true
    classDef group fill:#F8F8F8,stroke:#BDBBD3,stroke-width:1,rounded=true

    %% Main dependency flow

    SIDEKIQ_MW_ENTRY -->|registers| GITLAB_SIDEKIQ_MW

    GITLAB_SIDEKIQ_MW --> M_CLIENT_METRICS
    GITLAB_SIDEKIQ_MW --> M_SERVER_METRICS
    GITLAB_SIDEKIQ_MW --> M_MONITOR
    GITLAB_SIDEKIQ_MW --> M_REQUESTSTORE
    GITLAB_SIDEKIQ_MW --> M_SKIPJOBS
    GITLAB_SIDEKIQ_MW --> M_ARGUMENTS_LOGGER
    GITLAB_SIDEKIQ_MW --> M_INSTRUMENT_LOGGER
    GITLAB_SIDEKIQ_MW --> M_QUERY_ANALYZER
    GITLAB_SIDEKIQ_MW --> M_ADMINMODE_CLIENT
    GITLAB_SIDEKIQ_MW --> M_ADMINMODE_SERVER
    GITLAB_SIDEKIQ_MW --> M_IDENTITY_PASSTHROUGH
    GITLAB_SIDEKIQ_MW --> M_IDENTITY_RESTORE
    GITLAB_SIDEKIQ_MW --> M_EXTRA_DONE_LOG
    GITLAB_SIDEKIQ_MW --> M_SHARD_AWARENESS
    GITLAB_SIDEKIQ_MW --> M_SET_IP
    GITLAB_SIDEKIQ_MW --> M_BATCHLOADER

    GITLAB_SIDEKIQ_MW --> MW_CONCURRENCY
    GITLAB_SIDEKIQ_MW --> MW_PAUSECONTROL
    GITLAB_SIDEKIQ_MW --> M_WORKER_CONTEXT_SERVER
    GITLAB_SIDEKIQ_MW --> M_WORKER_CONTEXT_CLIENT
    GITLAB_SIDEKIQ_MW --> M_DUPLICATE_SERVER
    GITLAB_SIDEKIQ_MW --> M_DUPLICATE_CLIENT
    GITLAB_SIDEKIQ_MW --> M_SIZELIM_SERVER
    GITLAB_SIDEKIQ_MW --> M_SIZELIM_CLIENT

    M_CLIENT_METRICS --> METRICS_HELPER
    M_SERVER_METRICS --> METRICS_HELPER

    M_WORKER_CONTEXT_SERVER --> M_WORKER_CONTEXT
    M_WORKER_CONTEXT_CLIENT --> M_WORKER_CONTEXT

    %% PauseControl Interactions
    M_PAUSE_CLIENT --> M_PAUSE_HANDLER
    M_PAUSE_SERVER --> M_PAUSE_HANDLER
    M_PAUSE_HANDLER --> M_PAUSE_MAIN
    M_PAUSE_HANDLER --> M_PAUSE_WORKERSMAP
    M_PAUSE_HANDLER --> M_PAUSE_BASE
    M_PAUSE_MAIN --> M_PAUSE_NONE
    M_PAUSE_MAIN --> M_PAUSE_ADVSEARCH
    M_PAUSE_MAIN --> M_PAUSE_ZOEKT
    M_PAUSE_MAIN --> M_PAUSE_CLICKHOUSE
    M_PAUSE_MAIN --> M_PAUSE_DEPRECATED
    M_PAUSE_BASE --> M_PAUSE_SERVICE

    %% PauseControl and ClickHouse
    M_PAUSE_CLICKHOUSE --> CLICKHOUSE_MW

    %% ConcurrencyLimit Group
    M_CONC_CLIENT --> M_CONC_MW
    M_CONC_RESUME --> M_CONC_MW
    M_CONC_SLEEP --> M_CONC_MW
    M_CONC_MW --> M_CONC_SERVICE
    M_CONC_SERVICE --> M_CONC_WORKERSMAP
    M_CONC_SERVICE --> M_CONC_QUEUE
    M_CONC_SERVICE --> M_CONC_TRACKER

    %% Duplicate Jobs Group
    M_DUPLICATE_CLIENT --> M_DUPLICATE_JOB
    M_DUPLICATE_SERVER --> M_DUPLICATE_JOB
    M_DUPLICATE_JOB --> M_DUPLICATE_STRATEGIES
    M_DUPLICATE_JOB --> M_DUPLICATE_COOKIE
    M_DUPLICATE_STRATEGIES --> M_DUPLICATE_BASE
    M_DUPLICATE_STRATEGIES --> M_DUPLICATE_WHEN_SCHED
    M_DUPLICATE_STRATEGIES --> M_DUPLICATE_UNTIL_EXECUTING
    M_DUPLICATE_STRATEGIES --> M_DUPLICATE_UNTIL_EXECUTED
    M_DUPLICATE_STRATEGIES --> M_DUPLICATE_NONE

    %% Size Limiter MW interconnection
    M_SIZELIM_CLIENT --> M_SIZELIM_VALIDATOR
    M_SIZELIM_SERVER --> M_SIZELIM_COMPRESSOR
    M_SIZELIM_VALIDATOR --> M_SIZELIM_COMPRESSOR
    M_SIZELIM_VALIDATOR --> M_SIZELIM_EXCEED

    %% Status Middleware
    M_STATUS_SERVER --> M_STATUS
    M_STATUS_CLIENT --> M_STATUS

    %% Concurrency & Pause Control cross-ref with WorkerContext
    M_CONC_MW --> M_WORKER_CONTEXT
    M_PAUSE_SERVICE --> M_WORKER_CONTEXT
    M_RESOURCE_LIMIT_MW --> M_WORKER_CONTEXT

    %% Event store use
    M_BATCHLOADER --> D_EVENT_STORE

    %% Utilities and Persistence
    SIDEKIQ_CLUSTER -->|manages| GITLAB_SIDEKIQ_QUEUE
    SIDEKIQ_CLUSTER --> DB_WITH_LOCK_RETRIES
    SIDEKIQ_CLUSTER --> DB_WITH_LOCK_OUT_TX

    GITLAB_SIDEKIQ_QUEUE --> GITLAB_SET_CACHE
    GITLAB_SIDEKIQ_QUEUE --> GITLAB_RED_WORKHORSE

    DB_LB_SIDEKIQ_CLIENT_MW --> GITLAB_SIDEKIQ_QUEUE

    %% ClickHouse linkages
    CLICKHOUSE_MW --> DB_LB_SIDEKIQ_CLIENT_MW

    %% Logging/deduplication utility use
    M_DUPLICATE_JOB --> LOG_DEDUP

    %% Task/service interdep
    GITLAB_TASK_HELPERS --> DB_WITH_LOCK_RETRIES

    %% Signal handling
    GITLAB_SIDEKIQ_MW --> GITLAB_SIDEKIQ_SIGNALS

    %% Error handling links
    GITLAB_SIDEKIQ_MW --> PUMA_ERROR_HANDLER
    M_SIZELIM_VALIDATOR --> M_SIZELIM_EXCEED
    M_DUPLICATE_CLIENT --> M_DUPLICATE_NONE
    M_DUPLICATE_SERVER --> M_DUPLICATE_NONE
    M_DUPLICATE_UNTIL_EXECUTED --> M_DUPLICATE_WHEN_SCHED
    M_DUPLICATE_UNTIL_EXECUTING --> M_DUPLICATE_WHEN_SCHED
    M_DUPLICATE_NONE --> M_DUPLICATE_BASE

    %% Job Migrate interacts with Queue
    GITLAB_SIDEKIQ_MIGRATE --> GITLAB_SIDEKIQ_QUEUE

    %% RBAC/IP/mode features
    M_ADMINMODE_SERVER --> M_IDENTITY_RESTORE
    M_ADMINMODE_CLIENT --> M_IDENTITY_PASSTHROUGH

    %% Logical Purpose: Data and Abstractions
    D_EVENT_STORE -->|emits jobs to| GITLAB_SIDEKIQ_QUEUE

    %% SetCache used for various atomic set operations in the job domain
    GITLAB_SET_CACHE -.-> GITLAB_SIDEKIQ_QUEUE

    %% Resource Usage Limit dependency
    M_RESOURCE_LIMIT_SERVER --> M_RESOURCE_LIMIT_MW

    %% Stylings and certain cross-links

    class SIDEKIQ_MW_ENTRY,GITLAB_SIDEKIQ_MW,GITLAB_SIDEKIQ_QUEUE,GITLAB_SIDEKIQ_MIGRATE core
    class M_CLIENT_METRICS,M_SERVER_METRICS,M_MONITOR,M_REQUESTSTORE,M_SKIPJOBS,M_ARGUMENTS_LOGGER,M_INSTRUMENT_LOGGER,M_QUERY_ANALYZER,M_ADMINMODE_CLIENT,M_ADMINMODE_SERVER,M_IDENTITY_PASSTHROUGH,M_IDENTITY_RESTORE,M_EXTRA_DONE_LOG,M_SHARD_AWARENESS,M_SET_IP,M_BATCHLOADER core
    class M_STATUS_SERVER,M_STATUS_CLIENT core
    class D_EVENT_STORE,GITLAB_RED_WORKHORSE,GITLAB_SET_CACHE,DB_WITH_LOCK_RETRIES,DB_WITH_LOCK_OUT_TX,DB_LB_SIDEKIQ_CLIENT_MW data
    class M_RESOURCE_LIMIT_SERVER,M_RESOURCE_LIMIT_MW core
    class METRICS_HELPER,LOG_DEDUP,GITLAB_TASK_HELPERS support
    class M_SIZELIM_COMPRESSOR,M_SIZELIM_CLIENT,M_SIZELIM_SERVER,M_SIZELIM_VALIDATOR core
    class M_SIZELIM_EXCEED error
    class M_STATUS error
    class M_RETRY_ERROR error
    class PUMA_ERROR_HANDLER error
    class GITLAB_SIDEKIQ_SIGNALS core
    class SIDEKIQ_CLUSTER core

    %% Dummy nodes for color legend (not shown)
    classDef dummy fill:#ffffff,stroke-width:0
  end

  %% Partnerships: Persistence, Utilities, Error
  subgraph PERSIST_UTILS["Persistence & Job Utilities"]
    direction TB
    style PERSIST_UTILS fill:#F8F8F8,stroke:#AEDCCE,stroke-width:2,rounded=true
    D_SET_CACHE
    DB_WITH_LOCK_RETRIES
    DB_WITH_LOCK_OUT_TX
    DB_LB_SIDEKIQ_CLIENT_MW
    GITLAB_RED_WORKHORSE
    LOG_DEDUP
    GITLAB_TASK_HELPERS
    PUMA_ERROR_HANDLER
  end

  %% Ownership mapping domain cross-deps
  SIDEKIQ_MW --> PERSIST_UTILS

  %% Styles for groupings
  classDef group fill:#F8F8F8,stroke:#BDBBD3,stroke-width:1,rounded=true

  %% ClickHouse support
  subgraph CLICKHOUSE_SUP["ClickHouse Job Migration & MW"]
    direction TB
    style CLICKHOUSE_SUP fill:#F8F8F8,stroke:#7FCBD8,stroke-width:2,rounded=true
    CLICKHOUSE_MW
  end

  %% Domain-specific data structures
  subgraph DOMAIN_DT["Domain Data Structures & States"]
    direction TB
    style DOMAIN_DT fill:#F8F8F8,stroke:#A2CBA2,stroke-width:2,rounded=true
    M_CONC_SERVICE
    M_CONC_WORKERSMAP
    M_CONC_QUEUE
    M_CONC_TRACKER
    M_PAUSE_WORKERSMAP
    GITLAB_SET_CACHE
  end

  style SIDEKIQ_MW fill:#F8F8F8,stroke:#78B6E1,stroke-width:2,rounded=true
  style MW_CONCURRENCY fill:#F8F8F8,stroke:#7FCBD8,stroke-width:2,rounded=true
  style MW_PAUSECONTROL fill:#F8F8F8,stroke:#A39FC9,stroke-width:2,rounded=true
  style MW_CONC_DATA fill:#F8F8F8,stroke:#88C7AD,stroke-width:2,rounded=true
  style PERSIST_UTILS fill:#F8F8F8,stroke:#AEDCCE,stroke-width:2,rounded=true
  style DOMAIN_DT fill:#F8F8F8,stroke:#A2CBA2,stroke-width:2,rounded=true
  style CLICKHOUSE_SUP fill:#F8F8F8,stroke:#7FCBD8,stroke-width:2,rounded=true
```