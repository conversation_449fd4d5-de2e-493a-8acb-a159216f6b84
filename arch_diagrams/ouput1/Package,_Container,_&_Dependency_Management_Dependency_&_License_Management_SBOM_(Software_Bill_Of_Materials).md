```mermaid
flowchart TD
  %% Domain: Package, Container, & Dependency Management/Dependency & License Management/SBOM
  
  %% Core Data Models & Structures %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
  subgraph sbomModels["SBoM Core Data Structures and Relationships"]
    direction TB
    style sbomModels fill:#F8F8F8,stroke:#AAC2D2,stroke-width:2px

    sbomOccurrence["Sbom::Occurrence\nees/app/models/sbom/occurrence.rb":::coreDomain]
    sbomComponent["Sbom::Component\nees/app/models/sbom/component.rb":::coreDomain]
    sbomComponentVersion["Sbom::ComponentVersion\nees/app/models/sbom/component_version.rb":::coreDomain]
    sbomSource["Sbom::Source\nees/app/models/sbom/source.rb":::coreDomain]
    sbomSourcePackage["Sbom::SourcePackage\nees/app/models/sbom/source_package.rb":::coreDomain]
    sbomOccurrenceVuln["Sbom::OccurrencesVulnerability\nees/app/models/sbom/occurrences_vulnerability.rb":::coreDomain]
    sbomGraphPath["Sbom::GraphPath\nees/app/models/sbom/graph_path.rb":::coreDomain]
    sbomDependencyPath["Sbom::DependencyPath\nees/app/models/sbom/dependency_path.rb":::coreDomain]
    enumsSbom["Enums::Sbom\napp/models/concerns/enums/sbom.rb":::supporting]
    sbomBase["Sbom Base Module\nees/app/models/sbom.rb":::supporting]
  end

  classDef coreDomain fill:#D4F1F9,stroke:#AABED8,stroke-width:2px,stroke-dasharray:5 5,style=rounded-rectangle
  classDef supporting fill:#FFF8DC,stroke:#E4D8A8,stroke-width:2px,style=rounded-rectangle

  %% Data Model Internal Relationships
  sbomOccurrence -- "has many" --> sbomOccurrenceVuln
  sbomOccurrence -- "belongs to" --> sbomComponent
  sbomOccurrence -- "belongs to" --> sbomComponentVersion
  sbomOccurrence -- "belongs to" --> sbomSource
  sbomOccurrence -- "belongs to" --> sbomSourcePackage
  sbomOccurrence -- "graph paths" --> sbomGraphPath
  sbomOccurrence -- "dependency paths" --> sbomDependencyPath
  sbomComponent -- "has many" --> sbomOccurrence
  sbomComponentVersion -- "has many" --> sbomOccurrence
  sbomSourcePackage -- "has many" --> sbomOccurrence
  sbomOccurrenceVuln -- "references" --> sbomOccurrence
  sbomOccurrenceVuln -- "references" --> sbomComponent
  sbomDependencyPath -- "references occurrences" --> sbomOccurrence

  sbomComponentVersion -- "belongs to" --> sbomComponent
  sbomSourcePackage -- "belongs to" --> sbomComponent

  sbomComponent -- "component_type enum" --> enumsSbom
  sbomComponent -- "purl_type enum" --> enumsSbom
  sbomSourcePackage -- "purl_type enum" --> enumsSbom
  sbomBase -.-> sbomComponent
  sbomBase -.-> sbomOccurrence
  sbomBase -.-> sbomComponentVersion

  %% Query & Finder Layer %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
  subgraph sbomFinders["SBoM Data Access & Query Layer"]
    direction TB
    style sbomFinders fill:#F8F8F8,stroke:#D8CC9B,stroke-width:2px

    findComponents["Sbom::ComponentsFinder\nee/app/finders/sbom/components_finder.rb":::supporting]
    findComponentVersions["Sbom::ComponentVersionsFinder\nee/app/finders/sbom/component_versions_finder.rb":::supporting]
    findDependencyPaths["Sbom::DependencyPathsFinder\nee/app/finders/sbom/dependency_paths_finder.rb":::supporting]
    findDependencyLocations["Sbom::DependencyLocationsFinder\nee/app/finders/sbom/dependency_locations_finder.rb":::supporting]
    findAggregations["Sbom::AggregationsFinder\nee/app/finders/sbom/aggregations_finder.rb":::supporting]
    findPossiblyAffected["Sbom::PossiblyAffectedOccurrencesFinder\nee/app/finders/sbom/possibly_affected_occurrences_finder.rb":::supporting]
  end

  findComponents -- "query" --> sbomComponent
  findComponentVersions -- "query" --> sbomComponentVersion
  findDependencyPaths -- "query" --> sbomDependencyPath
  findDependencyLocations -- "query" --> sbomOccurrence
  findAggregations -- "aggregate" --> sbomOccurrence & sbomComponent & sbomComponentVersion
  findPossiblyAffected -- "scan" --> sbomOccurrence

  %% SBOM Ingestion / Processing Layer %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
  subgraph sbomIngestion["SBoM Ingestion Workflows"]
    direction TB
    style sbomIngestion fill:#F8F8F8,stroke:#9474C0,stroke-width:2px

    ingestReportService["IngestReportService\nees/app/services/sbom/ingestion/ingest_report_service.rb":::init]
    occMapCollection["OccurrenceMapCollection\nees/app/services/sbom/ingestion/occurrence_map_collection.rb":::init]
    ingestionTasksBase["Tasks::Base\nees/app/services/sbom/ingestion/tasks/base.rb":::init]
    ingestComponents["Tasks::IngestComponents\nees/app/services/sbom/ingestion/tasks/ingest_components.rb":::init]
    ingestComponentVersions["Tasks::IngestComponentVersions\nees/app/services/sbom/ingestion/tasks/ingest_component_versions.rb":::init]
    ingestOccurrences["Tasks::IngestOccurrences\nees/app/services/sbom/ingestion/tasks/ingest_occurrences.rb":::init]
    ingestSources["Tasks::IngestSources\nees/app/services/sbom/ingestion/tasks/ingest_sources.rb":::init]
    ingestSourcePackages["Tasks::IngestSourcePackages\nees/app/services/sbom/ingestion/tasks/ingest_source_packages.rb":::init]
    ingestOccurrencesVulns["Tasks::IngestOccurrencesVulnerabilities\nees/app/services/sbom/ingestion/tasks/ingest_occurrences_vulnerabilities.rb":::init]
    ingestDeleteNotPresent["DeleteNotPresentOccurrencesService\nees/app/services/sbom/ingestion/delete_not_present_occurrences_service.rb":::init]
    ingestDeleteForRegistry["ContainerScanningForRegistry::DeleteNotPresentOccurrencesService\nees/app/services/sbom/ingestion/container_scanning_for_registry/delete_not_present_occurrences_service.rb":::init]
    ingestionStrategyDefault["ExecutionStrategy::Default\nees/app/services/sbom/ingestion/execution_strategy/default.rb":::init]
    ingestionStrategyRegistry["ExecutionStrategy::ContainerScanningForRegistry\nees/app/services/sbom/ingestion/execution_strategy/container_scanning_for_registry.rb":::init]
    ingestionVulnData["Ingestion::VulnerabilityData\nees/app/services/sbom/ingestion/vulnerability_data.rb":::init]
    scheduleIngestReports["Sbom::ScheduleIngestReportsService\nees/app/services/sbom/schedule_ingest_reports_service.rb":::init]
  end

  classDef init fill:#E6E6FA,stroke:#BAA0E6,stroke-width:2px,style=rounded-rectangle

  scheduleIngestReports -- "schedules" --> ingestReportService
  ingestReportService -- "processes" --> occMapCollection
  occMapCollection -- "provides occurrence maps" --> ingestionTasksBase
  ingestionTasksBase -- "base for" --> ingestComponents & ingestComponentVersions & ingestOccurrences & ingestSources & ingestSourcePackages & ingestOccurrencesVulns
  ingestComponents -- "ingest into" --> sbomComponent
  ingestComponentVersions -- "ingest into" --> sbomComponentVersion
  ingestOccurrences -- "ingest into" --> sbomOccurrence
  ingestSources -- "ingest into" --> sbomSource
  ingestSourcePackages -- "ingest into" --> sbomSourcePackage
  ingestOccurrencesVulns -- "ingest into" --> sbomOccurrenceVuln
  ingestDeleteNotPresent -- "removes missing" --> sbomOccurrence
  ingestDeleteForRegistry -- "specialized removal" --> ingestDeleteNotPresent
  ingestionStrategyDefault -- "uses" --> ingestReportService
  ingestionStrategyRegistry -- "inherits" --> ingestionStrategyDefault
  ingestionStrategyRegistry -- "uses" --> ingestDeleteForRegistry
  ingestionVulnData -- "enriches" --> occMapCollection
  ingestionTasksBase -- "uses" --> ingestionVulnData

  %% Data Serialization/Exposure Layer %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
  subgraph sbomSerialization["SBoM Serialization / API Entities"]
    direction TB
    style sbomSerialization fill:#F8F8F8,stroke:#86B977,stroke-width:2px

    depLicenseListEntity["DependencyLicenseListEntity\nee/app/serializers/sbom/dependency_license_list_entity.rb":::supporting]
    sbomEntity["SbomEntity Hierarchy\nSbomEntity, SbomMetadataEntity, SbomComponentsEntity, SbomLicenseEntity\nees/app/serializers/sbom/sbom_entity.rb":::supporting]
    depPathEntity["DependencyPathEntity\nees/app/serializers/sbom/dependency_path_entity.rb":::supporting]
  end

  sbomEntity -- "exposes" --> sbomOccurrence
  depPathEntity -- "exposes" --> sbomDependencyPath
  depLicenseListEntity -- "serializes" --> sbomEntity

  %% Event/Job Processing Layer %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
  subgraph sbomWorkers["SBoM Event and Worker Processing"]
    direction TB
    style sbomWorkers fill:#F8F8F8,stroke:#E6AAC1,stroke-width:2px

    ingestReportsWorker["IngestReportsWorker\nees/app/workers/sbom/ingest_reports_worker.rb":::init]
    createOccVulnsWorker["CreateOccurrencesVulnerabilitiesWorker\nees/app/workers/sbom/create_occurrences_vulnerabilities_worker.rb":::init]
    procVulnsWorker["ProcessVulnerabilitiesWorker\nees/app/workers/sbom/process_vulnerabilities_worker.rb":::init]
    syncTraversalIdsWorker["SyncProjectTraversalIdsWorker\nees/app/workers/sbom/sync_project_traversal_ids_worker.rb":::init]
    syncArchivedWorker["SyncArchivedStatusWorker\nees/app/workers/sbom/sync_archived_status_worker.rb":::init]
  end

  ingestReportsWorker -- "invokes" --> ingestReportService
  createOccVulnsWorker -- "invokes" --> sbomOccurrenceVuln
  createOccVulnsWorker -- "interacts" --> sbomOccurrence
  procVulnsWorker -- "updates" --> sbomOccurrenceVuln
  syncTraversalIdsWorker -- "updates" --> sbomOccurrence
  syncArchivedWorker -- "invokes" --> sbomOccurrence

  %% Services for Traversal & Status %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
  subgraph sbomServices["SBoM Traversal & Status Services"]
    direction TB
    style sbomServices fill:#F8F8F8,stroke:#C9B8CB,stroke-width:2px

    syncTraversalIdsService["SyncTraversalIdsService\nees/app/services/sbom/sync_traversal_ids_service.rb":::init]
    createOccVulnsService["CreateOccurrencesVulnerabilitiesService\nees/app/services/sbom/create_occurrences_vulnerabilities_service.rb":::init]
  end

  createOccVulnsService -- "writes" --> sbomOccurrenceVuln
  syncTraversalIdsService -- "syncs" --> sbomOccurrence
  syncTraversalIdsWorker -- "calls" --> syncTraversalIdsService

  %% API Endpoint Layer %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
  subgraph sbomAPI["SBoM API Entry Points"]
    direction TB
    style sbomAPI fill:#F8F8F8,stroke:#E4B7A4,stroke-width:2px

    apiOccurrences["API::Sbom::Occurrences\nee/lib/api/sbom/occurrences.rb":::supporting]
  end

  apiOccurrences -- "exposes" --> sbomOccurrence

  %% GraphQL Types and Resolvers %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
  subgraph sbomGraphQL["SBoM GraphQL Types & Resolvers"]
    direction TB
    style sbomGraphQL fill:#F8F8F8,stroke:#9ACBC3,stroke-width:2px

    gqlTypeSourceType["Types::Sbom::SourceTypeEnum\nee/app/graphql/types/sbom/source_type_enum.rb":::supporting]
    gqlTypeDepSort["Types::Sbom::DependencySortEnum\nee/app/graphql/types/sbom/dependency_sort_enum.rb":::supporting]
    gqlTypeDepPath["Types::Sbom::DependencyPathType\nee/app/graphql/types/sbom/dependency_path_type.rb":::supporting]
    gqlTypeComponentVer["Types::Sbom::ComponentVersionType\nee/app/graphql/types/sbom/component_version_type.rb":::supporting]

    gqlResComponent["Resolvers::Sbom::ComponentResolver\nee/app/graphql/resolvers/sbom/component_resolver.rb":::supporting]
    gqlResComponentVer["Resolvers::Sbom::ComponentVersionResolver\nee/app/graphql/resolvers/sbom/component_version_resolver.rb":::supporting]
    gqlResDependencyIntf["Resolvers::Sbom::DependencyInterfaceResolver\nee/app/graphql/resolvers/sbom/dependency_interface_resolver.rb":::supporting]
    gqlResDependencyAgg["Resolvers::Sbom::DependencyAggregationResolver\nee/app/graphql/resolvers/sbom/dependency_aggregation_resolver.rb":::supporting]
    gqlResDependencies["Resolvers::Sbom::DependenciesResolver\nee/app/graphql/resolvers/sbom/dependencies_resolver.rb":::supporting]
  end

  gqlResComponent -- "returns" --> sbomComponent
  gqlResComponentVer -- "returns" --> sbomComponentVersion
  gqlResDependencyIntf -- "uses" --> sbomFinders
  gqlResDependencyAgg -- "extends" --> gqlResDependencyIntf
  gqlResDependencies -- "extends" --> gqlResDependencyIntf

  %% License, Source, PURL, Component Utilities %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
  subgraph sbomUtils["SBOM License, PURL, Structure & Decoding Utilities"]
    direction TB
    style sbomUtils fill:#F8F8F8,stroke:#83C9A8,stroke-width:2px

    sbomPurlEncoder["PackageUrl Encoder\nlib/sbom/package_url/encoder.rb":::utility]
    sbomPurlDecoder["PackageUrl Decoder\nlib/sbom/package_url/decoder.rb":::utility]
    sbomPurlNormalizer["PackageUrl Normalizer\nlib/sbom/package_url/normalizer.rb":::utility]
    sbomPurlArgValidator["PackageUrl Argument Validator\nlib/sbom/package_url/argument_validator.rb":::utility]
    sbomPurlStringUtils["PackageUrl StringUtils\nlib/sbom/package_url/string_utils.rb":::utility]
    sbomPurlTypeConv["PurlType::Converter\nlib/sbom/purl_type/converter.rb":::utility]
    sbomSourceHelper["SourceHelper\nlib/sbom/source_helper.rb":::utility]
  end

  classDef utility fill:#FFF8DC,stroke:#E4D8A8,stroke-width:2px,style=rounded-rectangle

  sbomPurlEncoder -- "encodes" --> sbomComponent
  sbomPurlDecoder -- "decodes" --> sbomComponent
  sbomPurlNormalizer -- "normalizes" --> sbomComponent
  sbomPurlArgValidator -- "validates" --> sbomComponent
  sbomPurlStringUtils -- "used by" --> sbomPurlEncoder & sbomPurlDecoder
  sbomSourceHelper -- "used by" --> sbomSource

  %% CI/CD SBOM Parsing, Reporting, and Data Structures %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
  subgraph sbomCI["CI/CD: SBOM Parsing, Reporting, and CycloneDX Data"]
    direction TB
    style sbomCI fill:#F8F8F8,stroke:#8EB9E6,stroke-width:2px

    ciSbomSource["Gitlab::Ci::Reports::Sbom::Source\nlib/gitlab/ci/reports/sbom/source.rb":::dataStruct]
    ciSbomReport["Gitlab::Ci::Reports::Sbom::Report\nlib/gitlab/ci/reports/sbom/report.rb":::dataStruct]
    ciSbomReports["Gitlab::Ci::Reports::Sbom::Reports\nlib/gitlab/ci/reports/sbom/reports.rb":::dataStruct]
    ciSbomComponent["Gitlab::Ci::Reports::Sbom::Component\nlib/gitlab/ci/reports/sbom/component.rb":::dataStruct]
    ciSbomMetadata["Gitlab::Ci::Reports::Sbom::Metadata\nlib/gitlab/ci/reports/sbom/metadata.rb":::dataStruct]
    ciSbomLicense["Gitlab::Ci::Reports::Sbom::License\nlib/gitlab/ci/reports/sbom/license.rb":::dataStruct]
    ciSbomDepAdjList["Gitlab::Ci::Reports::Sbom::DependencyAdjacencyList\nlib/gitlab/ci/reports/sbom/dependency_adjacency_list.rb":::dataStruct]

    ciParsersStart["Parsers::Sbom::Source\nlib/gitlab/ci/parsers/sbom/source/trivy.rb\nlib/gitlab/ci/parsers/sbom/source/container_scanning.rb\nlib/gitlab/ci/parsers/sbom/source/container_scanning_for_registry.rb\nlib/gitlab/ci/parsers/sbom/source/dependency_scanning.rb\nlib/gitlab/ci/parsers/sbom/source/dependency_scanning_component.rb":::utility]
    ciParsersComponent["Parser::Component\nlib/gitlab/ci/parsers/sbom/component.rb":::utility]
    ciParsersLicense["Parser::License\nlib/gitlab/ci/parsers/sbom/license.rb":::utility]
    ciParsersCyclonedxMeta["CyclonedxMetadataComponent\nlib/gitlab/ci/parsers/sbom/cyclonedx_metadata_component.rb":::utility]
    ciSchemaValidator["Validators::CyclonedxSchemaValidator\nlib/gitlab/ci/parsers/sbom/validators/cyclonedx_schema_validator.rb":::utility]
  end

  classDef dataStruct fill:#E0F8E0,stroke:#95C795,stroke-width:2px,style=rounded-rectangle

  ciSbomReport -- "contains" --> ciSbomSource & ciSbomMetadata & ciSbomComponent & ciSbomLicense & ciSbomDepAdjList
  ciSbomReports -- "aggregates" --> ciSbomReport
  ciParsersStart -- "parse" --> ciSbomReport
  ciParsersComponent -- "parse" --> ciSbomComponent
  ciParsersLicense -- "parse" --> ciSbomLicense
  ciSbomDepAdjList -- "models" --> ciSbomComponent
  ciSbomMetadata -- "attached to" --> ciSbomReport
  ciParsersCyclonedxMeta -- "extracts" --> ciSbomMetadata
  ciSchemaValidator -- "validates" --> ciSbomReport
  ciSbomSource -- "uses" --> sbomSourceHelper
  ciSbomComponent -- "uses" --> sbomPurlTypeConv

  %% Licensing, Enum, and Policy %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
  subgraph sbomDomainInfra["Domain-Level Policy, Error & Enum Infrastructure"]
    direction TB
    style sbomDomainInfra fill:#F8F8F8,stroke:#EBC5C1,stroke-width:2px

    sbomOccurrencePolicy["Sbom::OccurrencePolicy\nee/app/policies/sbom/occurrence_policy.rb":::supporting]
    vulnerabilityMRService["VulnerabilityMergeRequestLinks::CreateService\nees/app/services/vulnerability_merge_request_links/create_service.rb":::supporting]
  end

  sbomOccurrencePolicy -- "governs" --> sbomOccurrence

  %% Event Models %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
  subgraph sbomEvents["SBoM Domain Events"]
    direction TB
    style sbomEvents fill:#F8F8F8,stroke:#F1BDB5,stroke-width:2px

    sbomIngestedEvent["SbomIngestedEvent\nees/app/events/sbom/sbom_ingested_event.rb":::supporting]
    vulnerabilitiesCreatedEvent["VulnerabilitiesCreatedEvent\nees/app/events/sbom/vulnerabilities_created_event.rb":::supporting]
  end

  sbomIngestedEvent -- "triggers" --> ingestReportsWorker
  vulnerabilitiesCreatedEvent -- "triggers" --> createOccVulnsWorker

  %% CI Job Artifact Extension for SBOM %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
  subgraph ciIntegration["CI Integration & Artifact Enrichment"]
    direction TB
    style ciIntegration fill:#F8F8F8,stroke:#C1D9E5,stroke-width:2px

    ciJobArtifact["EE::Ci::JobArtifact SBOM extension\nee/app/models/ee/ci/job_artifact.rb":::supporting]
  end

  ciJobArtifact -- "provides SBOM report interfaces" --> ciSbomReport

  %% Relationships: Ingested Reports and Finders Linking Core Data %%%%%%%%%%%%%%%%%%%%%
  ingestReportService -- "ingests" --> sbomOccurrence
  ingestReportService -- "ingests" --> sbomComponent
  ingestReportService -- "ingests" --> sbomComponentVersion
  ingestReportService -- "attaches" --> sbomSource
  ingestReportService -- "attaches" --> sbomSourcePackage

  findComponents -- "for UI / GraphQL" --> gqlResComponent
  findComponentVersions -- "for UI / GraphQL" --> gqlResComponentVer
  findDependencyPaths -- "for serialization" --> depPathEntity

  %% Domain Data Flow %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
  ciSbomReports -- "input" --> ingestReportService
  ingestReportService -- "creates/updates" --> sbomModels
  
  sbomModels -- "queried by" --> sbomFinders
  sbomModels -- "serialized by" --> sbomSerialization
  sbomFinders -- "used by" --> sbomGraphQL
  sbomSerialization -- "delivered to" --> sbomAPI & sbomGraphQL
  sbomAPI -- "serves data" --> external[EXTERNAL CONSUMERS]
  sbomGraphQL -- "serves data" --> external

  %% Other Logical Links
  sbomIngestion -- "depends on" --> sbomCI
  sbomWorkers -- "trigger" --> sbomIngestion
  sbomEvents -- "trigger" --> sbomWorkers
  sbomServices -- "support" --> sbomWorkers

  class sbomOccurrence,sbomComponent,sbomComponentVersion,sbomSource,sbomSourcePackage,sbomOccurrenceVuln,sbomGraphPath,sbomDependencyPath,sbomBase,findComponents,findComponentVersions,findDependencyPaths,findDependencyLocations,findAggregations,findPossiblyAffected,syncTraversalIdsService,createOccVulnsService,depLicenseListEntity,sbomEntity,depPathEntity,syncTraversalIdsWorker,createOccVulnsWorker,procVulnsWorker,ingestReportsWorker,syncArchivedWorker,ingestReportService,occMapCollection,ingestionTasksBase,ingestComponents,ingestComponentVersions,ingestOccurrences,ingestSources,ingestSourcePackages,ingestOccurrencesVulns,ingestDeleteNotPresent,ingestDeleteForRegistry,ingestionStrategyDefault,ingestionStrategyRegistry,ingestionVulnData,scheduleIngestReports,gqlTypeSourceType,gqlTypeDepSort,gqlTypeDepPath,gqlTypeComponentVer,gqlResComponent,gqlResComponentVer,gqlResDependencyIntf,gqlResDependencyAgg,gqlResDependencies,sbomOccurrencePolicy,vulnerabilityMRService,ciJobArtifact,sbomPurlEncoder,sbomPurlDecoder,sbomPurlNormalizer,sbomPurlArgValidator,sbomPurlStringUtils,sbomPurlTypeConv,sbomSourceHelper,sbomIngestedEvent,vulnerabilitiesCreatedEvent,apiOccurrences,enumsSbom supporting
  class ciSbomSource,ciSbomReport,ciSbomReports,ciSbomComponent,ciSbomMetadata,ciSbomLicense,ciSbomDepAdjList,dataStruct
  class ciParsersStart,ciParsersComponent,ciParsersLicense,ciParsersCyclonedxMeta,ciSchemaValidator,utility
  class ingestReportService,occMapCollection,ingestionTasksBase,ingestComponents,ingestComponentVersions,ingestOccurrences,ingestSources,ingestSourcePackages,ingestOccurrencesVulns,ingestDeleteNotPresent,ingestDeleteForRegistry,ingestionStrategyDefault,ingestionStrategyRegistry,ingestionVulnData,syncTraversalIdsService,createOccVulnsService,scheduleIngestReports,ingestReportsWorker,createOccVulnsWorker,procVulnsWorker,syncTraversalIdsWorker,syncArchivedWorker,init

  %% Data structure color for .e.g. CI::Reports::Sbom::...
  class ciSbomSource,ciSbomReport,ciSbomReports,ciSbomComponent,ciSbomMetadata,ciSbomLicense,ciSbomDepAdjList dataStruct
```