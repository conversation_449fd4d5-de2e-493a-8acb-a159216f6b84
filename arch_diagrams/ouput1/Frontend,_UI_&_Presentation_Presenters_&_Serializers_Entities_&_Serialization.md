```mermaid
flowchart TD
  %% GLOBAL STYLE DEFINITIONS
  classDef core fill:#D4F1F9,stroke:#A9D6E5,stroke-width:2px,color:#222,stroke-dasharray: 0 0,stroke-linecap:round,rx:12,ry:12
  classDef utility fill:#FFF8DC,stroke:#FAE5A1,stroke-width:2px,color:#222,stroke-dasharray: 0 0,stroke-linecap:round,rx:12,ry:12
  classDef data fill:#E0F8E0,stroke:#A7D7A7,stroke-width:2px,color:#222,stroke-dasharray: 0 0,stroke-linecap:round,rx:12,ry:12
  classDef error fill:#FFE4E1,stroke:#EDC1C1,stroke-width:2px,color:#222,stroke-dasharray: 0 0,stroke-linecap:round,rx:12,ry:12
  classDef init fill:#E6E6FA,stroke:#C6C6EC,stroke-width:2px,color:#222,stroke-dasharray: 0 0,stroke-linecap:round,rx:12,ry:12
  classDef region fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rx:16,ry:16

  %% DOMAIN GROUPING - Presenters & Serializers
  subgraph "Presenters & Serializers Layer"
  direction TB
    subgraph "Presenters"[Presenters Layer]
    direction TB
      subgraph "GitLab Presenters"[GitLab Presenters]
      direction TB
        gitlab_gksp["kas/server_info_presenter.rb"]:::core
        gitlab_blamep["blame_presenter.rb"]:::core
      end
      subgraph "ML Presenters"[ML Presenters]
      direction TB
        ml_model_version["ml/model_version_presenter.rb"]:::core
        ml_candidate["ml/candidate_presenter.rb"]:::core
      end
      subgraph "Packages/Composer & Nuget Presenters"[Packages: Composer & Nuget]
      direction TB
        composer_packages_presenter["packages/composer/packages_presenter.rb"]:::core
        nuget_service_index_presenter["packages/nuget/service_index_presenter.rb"]:::core
        nuget_packages_metadata_presenter["packages/nuget/packages_metadata_presenter.rb"]:::core
      end
      terraform_modules_presenter["terraform/modules_presenter.rb"]:::core
      terraform_module_version_presenter["terraform/module_version_presenter.rb"]:::core
      merge_request_presenter["merge_request_presenter.rb"]:::core
      members_presenter["members_presenter.rb"]:::core
      ci_trigger_presenter["ci/trigger_presenter.rb"]:::core
      blobs_unfold_presenter["blobs/unfold_presenter.rb"]:::core
      ci_build_runner_presenter["ci/build_runner_presenter.rb"]:::core
    end

    subgraph "Serializers"[Serializers Layer]
    direction TB
      base_serializer["BaseSerializer"]:::core
      grape_entity["Grape::Entity"]:::core
      subgraph "Entity Files"[Entities]
        direction TB
          cohort_activity_month_entity["cohort_activity_month_entity.rb"]:::data
          codequality_degradation_entity["codequality_degradation_entity.rb"]:::data
          environment_entity["environment_entity.rb"]:::data
          runner_entity["runner_entity.rb"]:::data
          environment_status_entity["environment_status_entity.rb"]:::data
          move_to_project_entity["move_to_project_entity.rb"]:::data
          detailed_status_entity["detailed_status_entity.rb"]:::data
          build_action_entity["build_action_entity.rb"]:::data
          group_issuable_autocomplete_entity["group_issuable_autocomplete_entity.rb"]:::data
          job_group_entity["job_group_entity.rb"]:::data
          pipeline_details_entity["pipeline_details_entity.rb"]:::data
          triggered_pipeline_entity["triggered_pipeline_entity.rb"]:::data
          feature_flag_summary_entity["feature_flag_summary_entity.rb"]:::data
          context_commits_diff_entity["context_commits_diff_entity.rb"]:::data
          blob_entity["blob_entity.rb"]:::data
          build_artifact_entity["build_artifact_entity.rb"]:::data
          group_deploy_key_entity["group_deploy_key_entity.rb"]:::data
          member_user_entity["member_user_entity.rb"]:::data
          linked_project_issue_entity["linked_project_issue_entity.rb"]:::data
          trigger_variable_entity["trigger_variable_entity.rb"]:::data
      end
      subgraph "Grape Entities - Specialized"[Grape Specialized Entities]
        direction TB
          award_emoji_entity["award_emoji_entity.rb"]:::data
          analytics_stage_entity["analytics_stage_entity.rb"]:::data
          commit_entity["commit_entity.rb"]:::data
          build_coverage_entity["build_coverage_entity.rb"]:::data
          time_trackable_entity["time_trackable_entity.rb"]:::data
          deploy_keys_project_entity["deploy_keys_project_entity.rb"]:::data
      end
      subgraph "Entity Concerns"[Entity Concerns]
        direction TB
          request_aware_entity["request_aware_entity.rb"]:::utility
          diff_file_conflict_type["concerns/diff_file_conflict_type.rb"]:::utility
      end

      subgraph "Serializer Files"[Serializers]
        direction TB
          environment_serializer["environment_serializer.rb"]:::core
          container_repositories_serializer["container_repositories_serializer.rb"]:::core
          group_serializer["group_serializer.rb"]:::core
          group_access_token_serializer["group_access_token_serializer.rb"]:::core
          project_access_token_serializer["project_access_token_serializer.rb"]:::core
          personal_access_token_serializer["personal_access_token_serializer.rb"]:::core
          impersonation_access_token_serializer["impersonation_access_token_serializer.rb"]:::core
          namespace_serializer["namespace_serializer.rb"]:::core
          project_note_serializer["project_note_serializer.rb"]:::core
          project_mirror_serializer["project_mirror_serializer.rb"]:::core
          cohort_activity_month_entity["cohort_activity_month_entity.rb"]:::core
          codequality_reports_comparer_serializer["codequality_reports_comparer_serializer.rb"]:::core
          accessibility_reports_comparer_serializer["accessibility_reports_comparer_serializer.rb"]:::core
          label_serializer["label_serializer.rb"]:::core
          diffs_serializer["diffs_serializer.rb"]:::core
          diff_line_serializer["diff_line_serializer.rb"]:::core
          route_serializer["route_serializer.rb"]:::core
          review_app_setup_serializer["review_app_setup_serializer.rb"]:::core
          release_serializer["release_serializer.rb"]:::core
          merge_request_create_serializer["merge_request_create_serializer.rb"]:::core
          lfs_file_lock_serializer["lfs_file_lock_serializer.rb"]:::core
          draft_note_serializer["draft_note_serializer.rb"]:::core
          paginated_diff_serializer["paginated_diff_serializer.rb"]:::core
          fork_namespace_serializer["fork_namespace_serializer.rb"]:::core
          build_trace_serializer["build_trace_serializer.rb"]:::core
          analytics_summary_serializer["analytics_summary_serializer.rb"]:::core
          stage_serializer["stage_serializer.rb"]:::core
          diff_line_serializer["diff_line_serializer.rb"]:::core
          group_link_group_group_link_serializer["group_link/group_group_link_serializer.rb"]:::core
          group_link_project_group_link_serializer["group_link/project_group_link_serializer.rb"]:::core
          group_link_group_group_link_entity["group_link/group_group_link_entity.rb"]:::data
          group_link_project_group_link_entity["group_link/project_group_link_entity.rb"]:::data
          group_link_group_link_entity["group_link/group_link_entity.rb"]:::data
          import_provider_repo_serializer["import/provider_repo_serializer.rb"]:::core
          import_github_org_serializer["import/github_org_serializer.rb"]:::core
          import_github_failure_serializer["import/github_failure_serializer.rb"]:::core
          import_bulk_import_entity["import/bulk_import_entity.rb"]:::data
          import_base_provider_repo_entity["import/base_provider_repo_entity.rb"]:::data
          import_bitbucket_provider_repo_entity["import/bitbucket_provider_repo_entity.rb"]:::data
          import_gitlab_provider_repo_entity["import/gitlab_provider_repo_entity.rb"]:::data
          import_fogbugz_provider_repo_entity["import/fogbugz_provider_repo_entity.rb"]:::data
          import_manifest_provider_repo_entity["import/manifest_provider_repo_entity.rb"]:::data
          import_github_failure_entity["import/github_failure_entity.rb"]:::data
          import_github_org_entity["import/github_org_entity.rb"]:::data
          review_app_setup_serializer["review_app_setup_serializer.rb"]:::core
          web_ide_terminal_serializer["web_ide_terminal_serializer.rb"]:::core
          issue_serializer["issue_serializer.rb"]:::core
          linked_project_issue_serializer["linked_project_issue_serializer.rb"]:::core
          members_presenter["members_presenter.rb"]:::core
          move_to_project_entity["move_to_project_entity.rb"]:::data
          project_linked_issue_entity["project_linked_issue_entity.rb"]:::data
          user_serializer["user_serializer.rb"]:::core
          user_entity["user_entity.rb"]:::data
          member_user_entity["member_user_entity.rb"]:::data
          group_deploy_key_entity["group_deploy_key_entity.rb"]:::data
          build_artifact_entity["build_artifact_entity.rb"]:::data
          draft_note_serializer["draft_note_serializer.rb"]:::core
      end
    end

    subgraph "Specialized Domains"[Specialized Domains]
    direction TB
      subgraph "Admin Serializers"[Admin]
      direction TB
        admin_abuse_report_serializer["admin/abuse_report_serializer.rb"]:::core
        admin_abuse_report_details_serializer["admin/abuse_report_details_serializer.rb"]:::core
        admin_user_serializer["admin/user_serializer.rb"]:::core
        admin_user_entity["admin/user_entity.rb"]:::data
        admin_reported_content_entity["admin/reported_content_entity.rb"]:::data
      end
      subgraph "ActivityPub"[ActivityPub]
      direction TB
        activity_pub_collection_serializer["activity_pub/collection_serializer.rb"]:::core
        activity_pub_object_serializer["activity_pub/object_serializer.rb"]:::core
        activity_pub_actor_serializer["activity_pub/actor_serializer.rb"]:::core
        activity_pub_publish_release_activity_serializer["activity_pub/publish_release_activity_serializer.rb"]:::core
        activity_pub_releases_outbox_serializer["activity_pub/releases_outbox_serializer.rb"]:::core
        activity_pub_releases_actor_serializer["activity_pub/releases_actor_serializer.rb"]:::core
        activity_pub_user_entity["activity_pub/user_entity.rb"]:::data
        activity_pub_release_entity["activity_pub/release_entity.rb"]:::data
        activity_pub_project_entity["activity_pub/project_entity.rb"]:::data
      end
      subgraph "CI/CD Serializers"[CI/CD]
      direction TB
        subgraph "CI Entities"
          ci_job_entity["ci/job_entity.rb"]:::data
          ci_pipeline_entity["ci/pipeline_entity.rb"]:::data
          ci_basic_variable_entity["ci/basic_variable_entity.rb"]:::data
          ci_variable_entity["ci/variable_entity.rb"]:::data
          ci_group_variable_entity["ci/group_variable_entity.rb"]:::data
          ci_trigger_entity["ci/trigger_entity.rb"]:::data
          ci_codequality_mr_diff_entity["ci/codequality_mr_diff_entity.rb"]:::data
          ci_lint_job_entity["ci/lint/job_entity.rb"]:::data
          ci_daily_build_group_report_result_entity["ci/daily_build_group_report_result_entity.rb"]:::data
        end
        subgraph "CI Serializers"
          ci_downloadable_artifact_serializer["ci/downloadable_artifact_serializer.rb"]:::core
          ci_codequality_mr_diff_report_serializer["ci/codequality_mr_diff_report_serializer.rb"]:::core
          ci_variable_serializer["ci/variable_serializer.rb"]:::core
          ci_group_variable_serializer["ci/group_variable_serializer.rb"]:::core
          ci_trigger_serializer["ci/trigger_serializer.rb"]:::core
          ci_pipeline_schedule_entity["ci/pipeline_schedule_entity.rb"]:::data
          ci_instance_variable_serializer["ci/instance_variable_serializer.rb"]:::core
          ci_tag_serializer["ci/tag_serializer.rb"]:::core
          ci_lint_result_serializer["ci/lint/result_serializer.rb"]:::core
        end
      end

      subgraph "Feature Flags"[FeatureFlags]
      direction TB
        feature_flags_scope_entity["feature_flags/scope_entity.rb"]:::data
        feature_flags_user_list_entity["feature_flags/user_list_entity.rb"]:::data
        feature_flags_client_configuration_entity["feature_flags/client_configuration_entity.rb"]:::data
        feature_flags_client_serializer["feature_flags_client_serializer.rb"]:::core
      end

      subgraph "Integrations"[Integrations]
      direction TB
        integrations_project_serializer["integrations/project_serializer.rb"]:::core
        integrations_project_entity["integrations/project_entity.rb"]:::data
        integrations_field_entity["integrations/field_entity.rb"]:::data
        integrations_field_serializer["integrations/field_serializer.rb"]:::core
        integrations_event_entity["integrations/event_entity.rb"]:::data
        integrations_event_serializer["integrations/event_serializer.rb"]:::core
        integrations_harbor_repository_entity["integrations/harbor_serializers/repository_entity.rb"]:::data
      end

      subgraph "Packages"[Packages]
      direction TB
        packages_composer_packages_presenter["packages/composer/packages_presenter.rb"]:::core
        packages_nuget_service_index_presenter["packages/nuget/service_index_presenter.rb"]:::core
        packages_nuget_packages_metadata_presenter["packages/nuget/packages_metadata_presenter.rb"]:::core
      end
    end
    
    subgraph "Other Serialization Concerns & Entities"[Auxiliary]
    direction TB
      entity_date_helper["entity_date_helper.rb"]:::utility
      time_trackable_entity["time_trackable_entity.rb"]:::utility
      base_discussion_entity["base_discussion_entity.rb"]:::data
      detailed_status_entity["detailed_status_entity.rb"]:::data
    end
  end

  %% DOMAIN GROUPING - Entities, Data Structures
  subgraph "Entities & Data Structures"
  direction TB
    subgraph "Label/Issuable Entities"
      base_label["models/concerns/base_label.rb"]:::data
      issuables_base_label["serializers/issuables/base_label_entity.rb"]:::data
      anti_abuse_reports_label["serializers/anti_abuse/reports/label_entity.rb"]:::data
    end
    subgraph "Evidences Subgroup"
      evidences_project_entity["serializers/evidences/project_entity.rb"]:::data
      evidences_release_entity["serializers/evidences/release_entity.rb"]:::data
      evidences_evidence_entity["serializers/evidences/evidence_entity.rb"]:::data
      evidences_evidence_serializer["serializers/evidences/evidence_serializer.rb"]:::core
      evidences_issue_entity["serializers/evidences/issue_entity.rb"]:::data
      evidences_milestone_entity["serializers/evidences/milestone_entity.rb"]:::data
    end
    subgraph "Groups, Users, Members Entities"
      group_entity["serializers/group_entity.rb"]:::data
      user_entity["serializers/user_entity.rb"]:::data
      member_user_entity["serializers/member_user_entity.rb"]:::data
      members_presenter["presenters/members_presenter.rb"]:::core
    end
  end

  %% DOMAIN GROUPING - Utility & Support Files
  subgraph "Utilities, Setup & Error Handling"
  direction TB
    subgraph "Utilities"
      request_aware_entity["serializers/request_aware_entity.rb"]:::utility
      entity_date_helper["serializers/entity_date_helper.rb"]:::utility
      diff_file_conflict_type["serializers/concerns/diff_file_conflict_type.rb"]:::utility
      base_label["models/concerns/base_label.rb"]:::utility
    end
    subgraph "Initialization & Setup"
      application_mailer["mailers/application_mailer.rb"]:::init
      admin_notification_mailer["mailers/emails/admin_notification.rb"]:::init
      mailer_preview["mailers/previews/email_rejection_mailer_preview.rb"]:::init
    end
    subgraph "Error Handling"
      error_tracking_project_serializer["serializers/error_tracking/project_serializer.rb"]:::error
      error_tracking_error_serializer["serializers/error_tracking/error_serializer.rb"]:::error
      error_tracking_error_event_serializer["serializers/error_tracking/error_event_serializer.rb"]:::error
      error_tracking_detailed_error_serializer["serializers/error_tracking/detailed_error_serializer.rb"]:::error
    end
  end

  %% DOMAIN RELATIONSHIPS
  
  %% --- Core Presenter/Serializer relationships
  gitlab_gksp --|presents|--> grape_entity
  gitlab_blamep --|presents|--> grape_entity
  ml_model_version --|presents|--> grape_entity
  ml_candidate --|presents|--> grape_entity
  terraform_modules_presenter --|presents modules|--> grape_entity
  terraform_module_version_presenter --|presents version|--> grape_entity
  composer_packages_presenter --|prepares data|--> grape_entity
  nuget_service_index_presenter --|prepares data|--> grape_entity
  nuget_packages_metadata_presenter --|prepares metadata|--> grape_entity
  merge_request_presenter --|presents MR|--> grape_entity
  members_presenter --|presents members|--> grape_entity
  ci_trigger_presenter --|presents triggers|--> ci_trigger_entity
  blobs_unfold_presenter --|presents blobs|--> blob_entity
 
  %% Serializers - data entity relationships
  environment_serializer -. transforms .-> environment_entity
  project_note_serializer -. transforms .-> grape_entity
  container_repositories_serializer -. transforms .-> grape_entity
  group_serializer -. transforms .-> group_entity
  group_access_token_serializer -. transforms .-> grape_entity
  project_access_token_serializer -. transforms .-> grape_entity
  personal_access_token_serializer -. transforms .-> grape_entity
  impersonation_access_token_serializer -. transforms .-> grape_entity
  namespace_serializer -. transforms .-> grape_entity
  label_serializer -. transforms .-> issuables_base_label
  diffs_serializer -. transforms .-> detailed_status_entity
  diff_line_serializer -. transforms .-> grape_entity
  route_serializer -. transforms .-> grape_entity
  review_app_setup_serializer -. transforms .-> grape_entity
  release_serializer -. transforms .-> grape_entity
  merge_request_create_serializer -. transforms .-> grape_entity
  lfs_file_lock_serializer -. transforms .-> grape_entity
  draft_note_serializer -. transforms .-> grape_entity
  paginated_diff_serializer -. transforms .-> grape_entity
  fork_namespace_serializer -. transforms .-> grape_entity
  build_trace_serializer -. transforms .-> grape_entity

  %% CI/CD serializers/entities
  ci_pipeline_entity -. includes .-> ci_job_entity
  ci_pipeline_entity -. includes .-> ci_trigger_entity
  ci_trigger_entity -. exposes .-> user_entity
  ci_variable_entity --|inherits|--> ci_basic_variable_entity
  ci_group_variable_entity --|inherits|--> ci_basic_variable_entity
  ci_variable_serializer -. transforms .-> ci_variable_entity
  ci_group_variable_serializer -. transforms .-> ci_group_variable_entity
  ci_trigger_serializer -. transforms .-> ci_trigger_entity
 
  %% Integrations
  integrations_project_serializer -. transforms .-> integrations_project_entity
  integrations_event_serializer -. transforms .-> integrations_event_entity
  integrations_field_serializer -. transforms .-> integrations_field_entity
 
  %% Feature Flags
  feature_flags_client_serializer -. transforms .-> feature_flags_client_configuration_entity
  
  %% Evidences
  evidences_release_entity --|exposes|--> evidences_project_entity
  evidences_release_entity --|exposes|--> evidences_milestone_entity
  evidences_milestone_entity --|exposes issues|--> evidences_issue_entity
  evidences_evidence_entity --|exposes|--> evidences_release_entity
  evidences_evidence_serializer -. transforms .-> evidences_evidence_entity

  %% Admin
  admin_abuse_report_serializer -. transforms .-> admin_abuse_report_entity
  admin_abuse_report_details_serializer -. transforms .-> admin_abuse_report_details_entity
  admin_user_serializer -. transforms .-> admin_user_entity

  %% ActivityPub
  activity_pub_actor_serializer -. transforms .-> activity_pub_user_entity
  activity_pub_object_serializer -. transforms .-> activity_pub_release_entity
  activity_pub_publish_release_activity_serializer -. transforms .-> activity_pub_release_entity
  activity_pub_releases_outbox_serializer -. transforms .-> activity_pub_release_entity
  activity_pub_releases_actor_serializer -. transforms .-> activity_pub_activitypub_releases_actor_entity

  %% Utilities
  base_label --|concern for|--> issuables_base_label
  anti_abuse_reports_label --|inherits|--> issuables_base_label
  request_aware_entity --|included by|--> grape_entity

  %% Mailers
  application_mailer --|base class for|--> admin_notification_mailer
  admin_notification_mailer --|used in|--> mailer_preview

  %% Error handling
  error_tracking_project_serializer -. transforms .-> error_tracking_project_entity
  error_tracking_error_serializer -. transforms .-> error_entity
  error_tracking_error_event_serializer -. transforms .-> error_event_entity
  error_tracking_detailed_error_serializer -. transforms .-> detailed_error_entity

  %% Cross-cutting Concerns
  entity_date_helper --|utility for|--> grape_entity
  time_trackable_entity --|utility for|--> grape_entity

  %% EE extension examples pseudonodes for composition
  subgraph "EE Extensions"
    direction TB
    ee_issue_entity["ee/issue_entity.rb"]:::core
    ee_user_entity["ee/user_entity.rb"]:::core
    ee_member_entity["ee/member_entity.rb"]:::core
    ee_note_entity["ee/note_entity.rb"]:::core
    ee_environment_entity["ee/environment_entity.rb"]:::core
    ee_deployment_entity["ee/deployment_entity.rb"]:::core
    ee_base_discussion_entity["ee/base_discussion_entity.rb"]:::core
    ee_issue_sidebar_extras_entity["ee/issue_sidebar_extras_entity.rb"]:::core
    ee_issue_sidebar_basic_entity["ee/issue_sidebar_basic_entity.rb"]:::core
  end

  grape_entity --- ee_issue_entity
  user_entity --- ee_user_entity
  member_user_entity --- ee_member_entity
  base_discussion_entity --- ee_base_discussion_entity
  detailed_status_entity --- ee_environment_entity
  group_entity --- ee_issue_sidebar_extras_entity
  group_entity --- ee_issue_sidebar_basic_entity
  

  %% Node classes
  class gitlab_gksp,gitlab_blamep,ml_model_version,ml_candidate,composer_packages_presenter,nuget_service_index_presenter,nuget_packages_metadata_presenter,terraform_modules_presenter,terraform_module_version_presenter,merge_request_presenter,members_presenter,ci_trigger_presenter,blobs_unfold_presenter,ci_build_runner_presenter,base_serializer,environment_serializer,container_repositories_serializer,group_serializer,group_access_token_serializer,project_access_token_serializer,personal_access_token_serializer,impersonation_access_token_serializer,namespace_serializer,project_note_serializer,project_mirror_serializer,cohort_activity_month_entity,codequality_reports_comparer_serializer,accessibility_reports_comparer_serializer,label_serializer,diffs_serializer,diff_line_serializer,route_serializer,review_app_setup_serializer,release_serializer,merge_request_create_serializer,lfs_file_lock_serializer,draft_note_serializer,paginated_diff_serializer,fork_namespace_serializer,build_trace_serializer,analytics_summary_serializer,stage_serializer,diff_line_serializer,group_link_group_group_link_serializer,group_link_project_group_link_serializer,import_provider_repo_serializer,import_github_org_serializer,import_github_failure_serializer,web_ide_terminal_serializer,issue_serializer,linked_project_issue_serializer,members_presenter,project_linked_issue_entity,user_serializer,admin_abuse_report_serializer,admin_abuse_report_details_serializer,admin_user_serializer,activity_pub_collection_serializer,activity_pub_object_serializer,activity_pub_actor_serializer,activity_pub_publish_release_activity_serializer,activity_pub_releases_outbox_serializer,activity_pub_releases_actor_serializer,ci_downloadable_artifact_serializer,ci_codequality_mr_diff_report_serializer,ci_variable_serializer,ci_group_variable_serializer,ci_trigger_serializer,ci_instance_variable_serializer,ci_tag_serializer,ci_lint_result_serializer,feature_flags_client_serializer,evidences_evidence_serializer,integrations_project_serializer,integrations_event_serializer,integrations_field_serializer,integrations_field_serializer,admin_user_serializer,admin_abuse_report_serializer,admin_abuse_report_details_serializer,activity_pub_actor_serializer,activity_pub_object_serializer,activity_pub_publish_release_activity_serializer,activity_pub_releases_outbox_serializer,activity_pub_releases_actor_serializer,ci_downloadable_artifact_serializer,ci_variable_serializer,ci_group_variable_serializer,ci_trigger_serializer,ci_pipeline_entity,ci_basic_variable_entity,ci_variable_entity,ci_group_variable_entity,ci_trigger_entity,ci_job_entity,ci_codequality_mr_diff_entity,ci_lint_job_entity,ci_daily_build_group_report_result_entity,feature_flags_scope_entity,feature_flags_user_list_entity,feature_flags_client_configuration_entity,evidences_release_entity,evidences_project_entity,evidences_milestone_entity,evidences_issue_entity,evidences_evidence_entity,group_entity,user_entity,member_user_entity,anti_abuse_reports_label,issuables_base_label,base_label,error_tracking_project_serializer,error_tracking_error_serializer,error_tracking_error_event_serializer,error_tracking_detailed_error_serializer,gitlab_gksp,gitlab_blamep,ml_model_version,ml_candidate,class core;


  class cohort_activity_month_entity,codequality_degradation_entity,environment_entity,runner_entity,environment_status_entity,move_to_project_entity,detailed_status_entity,build_action_entity,group_issuable_autocomplete_entity,job_group_entity,pipeline_details_entity,triggered_pipeline_entity,feature_flag_summary_entity,context_commits_diff_entity,blob_entity,build_artifact_entity,group_deploy_key_entity,member_user_entity,linked_project_issue_entity,trigger_variable_entity,award_emoji_entity,analytics_stage_entity,commit_entity,build_coverage_entity,time_trackable_entity,deploy_keys_project_entity,class data;
  class request_aware_entity,entity_date_helper,diff_file_conflict_type,base_discussion_entity class utility;
  class application_mailer,admin_notification_mailer,mailer_preview class init;
  class error_tracking_project_serializer,error_tracking_error_serializer,error_tracking_error_event_serializer,error_tracking_detailed_error_serializer class error;

  class region;

%% END OF DIAGRAM
```