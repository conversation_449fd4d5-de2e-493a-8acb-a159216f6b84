```mermaid
flowchart TD
%% Group: Debian Packages - Core Domain Concepts and Entry Points
subgraph debian_core["Debian Package Domain Core" ]
  direction TB
  style debian_core fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded=true

  debian_api_entry[DebianProjectPackages
    vertical API for project-scoped package registry
    Handles upload, download, validation of Debian packages and metadata]:::api_blue

  debian_group_api_entry[DebianGroupPackages
    vertical API for group-scoped package registry
    Handles group-level package endpoints]:::api_blue

  debian_group_dist_api[GroupDebianDistributions
    vertical API endpoints for group-level distributions management]:::api_blue

  debian_api_conc[DebianPackageEndpoints
    vertical API shared concerns: validation rules, param handling, helpers etc.]:::support_yellow

  debian_entity[Entities::Packages::Debian::Distribution
    vertical Grape entity for exposing Debian distribution data]:::support_yellow

end

%% Group: Core Domain Data Models
subgraph debian_models["Debian Package Models" ]
  direction TB
  style debian_models fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded=true

  debian_package_const[debian.rb
    Domain constants and regex rules]:::core_blue

  debian_package[Debian::Package
    Core model - represents a Debian package entity]:::core_blue

  debian_file_entry[Debian::FileEntry
    ActiveModel for abstracting package files for validation and digests]:::data_green

  debian_file_metadatum[Debian::FileMetadatum
    Persistence of package file metadata]:::data_green

  debian_project_dist[ProjectDistribution
    Project-level distribution, tracks codename/suite/metadata]:::core_blue

  debian_group_dist[GroupDistribution
    Group-level distribution, for group-wide packages]:::core_blue

  debian_publication[Debian::Publication
    Package publication join between packages and distributions]:::data_green

  debian_group_dist_key[GroupDistributionKey
    Manages group-level distribution cryptographic keys]:::data_green

  debian_project_dist_key[ProjectDistributionKey
    Manages project-level distribution cryptographic keys]:::data_green

  debian_group_arch[GroupArchitecture
    Architecture list for group-level distributions]:::core_blue

  debian_project_arch[ProjectArchitecture
    Architecture list for project-level distributions]:::core_blue

  debian_group_comp[GroupComponent
    Components for group distributions]:::core_blue

  debian_project_comp[ProjectComponent
    Components for project distributions]:::core_blue

  debian_group_comp_file[GroupComponentFile
    Files in a group component]:::data_green

  debian_project_comp_file[ProjectComponentFile
    Files in a project component]:::data_green

end

%% Group: Shared Domain Concerns Abstractions/Patterns for Model Extension
subgraph debian_concerns["Debian Model Concerns & Abstractions" ]
  direction TB
  style debian_concerns fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded=true

  dist_concern[concerns/debian/distribution.rb
    Abstracts distribution-level features:
    container type keys, file mounter, codename/suite logic]:::support_yellow

  arch_concern[concerns/debian/architecture.rb
    Abstracts architecture-to-distribution relationship]:::support_yellow

  comp_concern[concerns/debian/component.rb
    Abstract component logic for distributions]:::support_yellow

  comp_file_concern[concerns/debian/component_file.rb
    Abstract file storage and sortable/project/group logic for component files]:::support_yellow

  dist_key_concern[concerns/debian/distribution_key.rb
    Key encryption, key management, associations with distros]:::support_yellow
end

%% Group: File and Object Uploaders
subgraph debian_uploaders["Debian File/Component Uploaders" ]
  direction TB
  style debian_uploaders fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded=true

  dist_release_uploader[DistributionReleaseFileUploader
    Uploader for RELEASE index files from project/group distros]:::support_yellow

  comp_file_uploader[ComponentFileUploader
    Handles uploading of component files by component/type]:::support_yellow
end

%% Group: Finders and Query Objects
subgraph debian_finders["Debian Finders & Query Objects" ]
  direction TB
  style debian_finders fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2px,rounded=true

  debian_dist_finder[Debian::DistributionsFinder
    Finds distributions by container project/group, codename, suite]:::support_yellow
end

%% Group: Services - Debian
subgraph debian_services["Debian Services" ]
  direction TB
  style debian_services fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded=true

  sign_dist_service[SignDistributionService
    GPG-signs Release files for distribution]:::core_blue

  gen_dist_key[GenerateDistributionKeyService
    Generates cryptographic signing key for distribution]:::core_blue

  find_or_create_incoming[FindOrCreateIncomingService
    Ensures the incoming package exists for an upload process]:::core_blue

  extract_changes_meta[ExtractChangesMetadataService
    Extracts metadata from .changes files for validation/indexing]:::core_blue

  extract_deb_meta[ExtractDebMetadataService
    Extracts metadata from .deb packages]:::core_blue

  extract_meta_service[ExtractMetadataService
    Extracts and validates package-level metadata]:::core_blue

  parse822_service[ParseDebian822Service
    Parses metadata/layout in Debian RFC822 control data format]:::core_blue

  create_pkg_file_service[CreatePackageFileService
    Handles creation/association of package file and metadata]:::core_blue

  process_pkg_file_service[ProcessPackageFileService
    Coordinates processing+validation of uploaded packages]:::core_blue

  gen_dist_service[GenerateDistributionService
    Generates publishing artifacts: Release, Packages, Component files for a distribution]:::core_blue

  create_dist_service[CreateDistributionService
    Logic to create new distributions, their components and architectures]:::core_blue

  update_dist_service[UpdateDistributionService
    Logic for updating distribution, adding/removing architectures/components safely]:::core_blue
end

%% Group: Debian File/Package Processing Workers
subgraph debian_workers["Debian Background Workers" ]
  direction TB
  style debian_workers fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2px,rounded=true

  clean_dangling_worker[CleanupDanglingPackageFilesWorker
    Scheduled worker to clean unused file uploads]:::init_purple

  process_pkg_worker[ProcessPackageFileWorker
    Validates and processes new incoming package files]:::init_purple

  gen_dist_worker[GenerateDistributionWorker
    Generates/rebuilds distribution indices & metadata]:::init_purple

end


%% Group: RPM Packages - Core Domain Models
subgraph rpm_models["RPM Package Models" ]
  direction TB
  style rpm_models fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded=true

  rpm_module[rpm.rb
    Table name prefix and RPM domain constants]
    :::core_blue

  rpm_package[Rpm::Package
    Core model - represents an RPM package record]
    :::core_blue

  rpm_metadatum[Rpm::Metadatum
    Stores RPM metadata for package: epoch, summary, release etc.]
    :::data_green

  rpm_repo_file[Rpm::RepositoryFile
    Models files metadata/filelists within a repository]
    :::data_green
end

%% Group: RPM Services - Metadata Generation and Parsing
subgraph rpm_services["RPM & Repository Metadata Services" ]
  direction TB
  style rpm_services fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded=true

  update_xml_service[RepositoryMetadata::UpdateXmlService
    Orchestrates update/build for repository metadata XML]:::core_blue

  build_repomd_xml[RepositoryMetadata::BuildRepomdXmlService
    Builds repomd.xml repository metadata file]:::core_blue

  build_primary_xml[RepositoryMetadata::BuildPrimaryXmlService
    Builds primary.xml file package summary + details]:::core_blue

  build_other_xml[RepositoryMetadata::BuildOtherXmlService
    Builds other.xml change/requires/provides data]:::core_blue

  build_filelist_xml[RepositoryMetadata::BuildFilelistXmlService
    Builds filelists.xml file contents for each package]:::core_blue

  parse_rpm_pkg[ParsePackageService
    Parses .rpm files, extracts attributes, requirements, changelogs]:::core_blue
end

%% Group: RPM Repository/Package File Uploaders
subgraph rpm_uploaders["RPM File/Repository Uploaders" ]
  direction TB
  style rpm_uploaders fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded=true

  rpm_repo_uploader[RepositoryFileUploader
    Handles object storage & upload for RPM repository files]:::support_yellow
end

%% Group: API - RPM
subgraph rpm_api["RPM Package APIs" ]
  direction TB
  style rpm_api fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded=true

  rpm_api_entry[RpmProjectPackages
    Project-scoped REST API for RPM package registry]:::api_blue
end

%% Group: Registry API Integration
subgraph registry_api["Shared Registry API & Entities" ]
  direction TB
  style registry_api fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2px,rounded=true

  debian_api_entity[Entities::Packages::Debian::Distribution
    Exposes distribution fields in JSON for APIs]:::support_yellow

end


%% INTER-GROUP RELATIONSHIPS AND COLLABORATIONS

%% Debian API dependencies
debian_api_entry -- uses constants, validation --> debian_package_const
debian_api_entry -- uses for endpoint param validation --> debian_api_conc
debian_api_entry -- references, exposes --> debian_project_dist
debian_api_entry -- presents --> debian_entity

debian_group_api_entry -- uses constants --> debian_package_const
debian_group_api_entry -- uses concerns --> debian_api_conc
debian_group_api_entry -- references, exposes --> debian_group_dist

debian_group_dist_api -- references API constraints --> debian_api_conc
debian_group_dist_api -- exposes, manages --> debian_group_dist

debian_api_conc -- uses regex from --> debian_package_const
debian_api_conc -- presents --> debian_api_entity

debian_entity -- field mapping --> debian_project_dist
debian_entity -- field mapping --> debian_group_dist

%% Core Model relationships
debian_package -- has_one --> debian_publication
debian_package -- has_one (distribution via publication) --> debian_project_dist
debian_publication -- belongs_to --> debian_package

%% Model <-> Concern Abstractions
debian_group_dist -- includes --> dist_concern
debian_project_dist -- includes --> dist_concern

debian_group_comp -- includes --> comp_concern
debian_project_comp -- includes --> comp_concern

debian_group_arch -- includes --> arch_concern
debian_project_arch -- includes --> arch_concern

debian_group_dist_key -- includes --> dist_key_concern
debian_project_dist_key -- includes --> dist_key_concern

debian_group_comp_file -- includes --> comp_file_concern
debian_project_comp_file -- includes --> comp_file_concern

%% Component and Archive relationships
debian_group_dist -- has_many --> debian_group_arch
debian_group_dist -- has_many --> debian_group_comp
debian_group_comp -- has_many --> debian_group_comp_file
debian_group_arch -- has_many --> debian_group_comp_file

debian_project_dist -- has_many --> debian_project_arch
debian_project_dist -- has_many --> debian_project_comp
debian_project_comp -- has_many --> debian_project_comp_file
debian_project_arch -- has_many --> debian_project_comp_file

%% Metadatum relationships
debian_file_metadatum -- belongs_to --> debian_file_entry

%% Uploader integration with models
dist_release_uploader -- used by --> debian_project_dist
dist_release_uploader -- used by --> debian_group_dist
comp_file_uploader -- used by --> debian_project_comp_file
comp_file_uploader -- used by --> debian_group_comp_file

%% Domain finders
debian_dist_finder -- queries --> debian_group_dist
debian_dist_finder -- queries --> debian_project_dist

%% Service-Model Interactions (Debian)
sign_dist_service -- signs/output --> debian_project_dist
sign_dist_service -- signs/output --> debian_group_dist
gen_dist_key -- generates keys for --> debian_project_dist_key
gen_dist_key -- generates keys for --> debian_group_dist_key

create_dist_service -- creates --> debian_group_dist
create_dist_service -- creates --> debian_project_dist
create_dist_service -- creates --> debian_group_comp
create_dist_service -- creates --> debian_project_comp
create_dist_service -- creates --> debian_group_arch
create_dist_service -- creates --> debian_project_arch

update_dist_service -- updates --> debian_group_dist
update_dist_service -- updates --> debian_project_dist

find_or_create_incoming -- resolves --> debian_package

extract_changes_meta -- reads --> debian_file_entry
extract_changeS_meta -- error handling --> debian_file_entry

extract_deb_meta -- reads from --> debian_file_entry
extract_meta_service -- processes --> debian_file_metadatum

gen_dist_service -- produces --> debian_group_dist
gen_dist_service -- produces --> debian_project_dist
gen_dist_service -- makes --> debian_group_comp_file
gen_dist_service -- makes --> debian_project_comp_file

process_pkg_file_service -- coordinates --> extract_meta_service
process_pkg_file_service -- coordinates --> extract_changes_meta
process_pkg_file_service -- validates --> extract_deb_meta
process_pkg_file_service -- operates on --> debian_file_entry
process_pkg_file_service -- outputs --> debian_file_metadatum
process_pkg_file_service -- interacts with --> create_pkg_file_service

create_pkg_file_service -- inserts/associates --> debian_file_entry
create_pkg_file_service -- produces --> debian_file_metadatum

parse822_service -- used by --> extract_meta_service
parse822_service -- used by --> extract_changes_meta

%% Worker-service relationships
process_pkg_worker -- invokes --> process_pkg_file_service
process_pkg_worker -- feeds data to --> debian_package

clean_dangling_worker -- cleans --> debian_file_entry

gen_dist_worker -- invokes --> gen_dist_service

%% RPM Domain: Model relationships
rpm_package -- has_one (rpm_metadatum) --> rpm_metadatum
rpm_metadatum -- belongs_to --> rpm_package
rpm_repo_file -- handles files for --> rpm_package

%% RPM API and Model
rpm_api_entry -- invokes --> rpm_package
rpm_api_entry -- exposes --> rpm_repo_file

%% RPM Data Uploaders
rpm_repo_uploader -- used by --> rpm_repo_file

%% RPM Services and Metadata Generation
update_xml_service -- delegates (builders) --> build_primary_xml
update_xml_service -- delegates --> build_other_xml
update_xml_service -- delegates --> build_filelist_xml

build_repomd_xml -- builds --> rpm_repo_file
build_primary_xml -- builds XML from --> rpm_package
build_other_xml -- builds 'other' xml from --> rpm_package
build_filelist_xml -- builds filelists xml from --> rpm_package

parse_rpm_pkg -- parses, extracts --> rpm_metadatum

%% Registry API entities
debian_api_entry -- presents --> debian_api_entity
debian_group_api_entry -- presents --> debian_api_entity
debian_group_dist_api -- presents --> debian_api_entity

%% Cross-link core concepts
debian_package -- is foundation for --> rpm_package

%% Node Styles
classDef core_blue fill:#D4F1F9,stroke:#A3D5E0,stroke-width:2px,color:#222,stroke-dasharray:0 0,rx:10,ry:10
classDef support_yellow fill:#FFF8DC,stroke:#FFECB3,stroke-width:2px,color:#333,stroke-dasharray:0 0,rx:10,ry:10
classDef data_green fill:#E0F8E0,stroke:#AEDFA3,stroke-width:2px,color:#2a2,stroke-dasharray:0 0,rx:10,ry:10
classDef init_purple fill:#E6E6FA,stroke:#C7B4F5,stroke-width:2px,color:#444,stroke-dasharray:0 0,rx:10,ry:10
classDef api_blue fill:#D4F1F9,stroke:#7AD7F0,stroke-width:2px,color:#222,stroke-dasharray:0 0,rx:10,ry:10

%% Node shapes
class debian_package_const,debian_package,debian_project_dist,debian_group_dist,debian_group_arch,debian_project_arch,debian_group_comp,debian_project_comp,sign_dist_service,gen_dist_key,find_or_create_incoming,extract_changes_meta,extract_deb_meta,extract_meta_service,process_pkg_file_service,gen_dist_service,create_dist_service,update_dist_service,debian_file_entry,rpm_package,rpm_metadatum,rpm_repo_file,rpm_module,update_xml_service,build_repomd_xml,build_primary_xml,build_other_xml,build_filelist_xml,parse_rpm_pkg core_blue
class debian_api_entry,debian_group_api_entry,debian_group_dist_api rpm_api_entry,api_blue
class dist_concern,arch_concern,comp_concern,dist_key_concern,comp_file_concern,dist_release_uploader,comp_file_uploader,rpm_repo_uploader,debian_api_conc,debian_entity debian_api_entity support_yellow
class debian_file_metadatum,debian_publication,debian_group_dist_key,debian_project_dist_key,debian_group_comp_file,debian_project_comp_file data_green
class clean_dangling_worker,process_pkg_worker,gen_dist_worker init_purple
```