```mermaid
flowchart TD
  %% Global Styles
  linkStyle default stroke-width:2px,stroke:#C3CBD6
  classDef pastelBlue  fill:#D4F1F9,stroke:#A9D3DF,color:#243547,stroke-width:2px,rounded-corners,stroke-dasharray: 4 2
  classDef pastelYellow  fill:#FFF8DC,stroke:#E1D9AC,color:#6D5A09,stroke-width:2px,rounded-corners,stroke-dasharray: 4 2
  classDef pastelGreen  fill:#E0F8E0,stroke:#A7CFAC,color:#226522,stroke-width:2px,rounded-corners,stroke-dasharray: 4 2
  classDef pastelRed  fill:#FFE4E1,stroke:#FFC9C2,color:#A63B2F,stroke-width:2px,rounded-corners,stroke-dasharray: 4 2
  classDef pastelPurple  fill:#E6E6FA,stroke:#C4BFE7,color:#4B417F,stroke-width:2px,rounded-corners,stroke-dasharray: 4 2
  classDef groupSubgraph fill:#F8F8F8,stroke:#B3BEC0,stroke-width:2px,rounded-corners


  %% Subgraph: Enterprise User & Seat Licensing Domain
  subgraph SEAT_MGMT_DOMAIN["Enterprise User & Seat Licensing: Seat Management" ]
    class SEAT_MGMT_DOMAIN groupSubgraph

      %% Seat Management Core Logic
      subgraph CORE["Core Seat Management Logic"]
        class CORE groupSubgraph

        BlockSeatOveragesModule["BlockSeatOverages\nGitlabSubscriptions/MemberManagement/BlockSeatOverages.rb"]:::pastelBlue
        SyncSeatLinkWorker["SyncSeatLinkWorker\napp/workers/sync_seat_link_worker.rb"]:::pastelBlue
      end

      %% Supporting Structure (utilities/value transformation helpers)
      subgraph UTILITIES["Supporting Structures & Utilities"]
        class UTILITIES groupSubgraph

        ProcessInvites["process_invites \ninvite parsing and normalization"]:::pastelYellow
        ParseInputList["parse_input_list \nlist value handling"]:::pastelYellow
        ProcessEmails["process_emails \nemail value extraction and n-to-1 transformation"]:::pastelYellow
      end

      %% Data Structure Subgraph: Seat Counts & License Models
      subgraph DATA_STRUCTURES["Domain Data Structures"]
        class DATA_STRUCTURES groupSubgraph

        SeatCounts["Seat Counts \nbillable, available,\noverage, blocked"]:::pastelGreen
        AccessLevel["Access Level & Role ID\naccess constants, role assignment"]:::pastelGreen
        InvitesList["Invites List\nemail or user format, member representations"]:::pastelGreen
        RootNamespace["Root Namespace\norganizational root structure"]:::pastelGreen
      end

      %% Initialization/Configuration
      subgraph INIT["Initialization Workers"]
        class INIT groupSubgraph
        SeatSyncInit["Scheduled Seat Licensing Sync\nSyncSeatLinkWorker triggers"]:::pastelPurple
      end

      %% Error Handling
      subgraph ERRORS["Error & Limit Handling"]
        class ERRORS groupSubgraph

        BlockOveragesError["Block Seat Overages\nEnforcement/Error Forwarding"]:::pastelRed
      end

  end

  %% LOGICAL RELATIONSHIPS & ABSTRACTIONS

  %% Sync worker executes domain sync and invokes seat-link logic with BlockSeatOverages
  SyncSeatLinkWorker ==> BlockSeatOveragesModule
  class SyncSeatLinkWorker,BlockSeatOveragesModule pastelBlue

  %% BlockSeatOveragesModule depends on helpers/utilities for determining seat availability/processing
  BlockSeatOveragesModule --> ProcessInvites
  BlockSeatOveragesModule --> ParseInputList
  BlockSeatOveragesModule --> ProcessEmails

  %% Data Structures usage
  BlockSeatOveragesModule -.-> SeatCounts
  BlockSeatOveragesModule -.-> RootNamespace
  BlockSeatOveragesModule -.-> InvitesList
  BlockSeatOveragesModule -.-> AccessLevel

  ProcessInvites --> InvitesList
  ParseInputList --> InvitesList
  ProcessEmails --> InvitesList

  BlockSeatOveragesModule -.-> BlockOveragesError
  BlockSeatOveragesModule -.-> SeatSyncInit

  %% SeatSyncInit triggers the sync worker
  SeatSyncInit -- schedules/enqueues --> SyncSeatLinkWorker

  %% BlockSeatOveragesModule relies on root_namespace, seat counts and access level info
  BlockSeatOveragesModule --> RootNamespace
  BlockSeatOveragesModule --> SeatCounts
  BlockSeatOveragesModule --> AccessLevel

  %% Supporting utility functions build key domain structures
  ProcessInvites ==> InvitesList
  ParseInputList ==> InvitesList
  ProcessEmails ==> InvitesList

  %% Data Structures interrelations
  RootNamespace -- owns/manages --> SeatCounts
  RootNamespace -- scope for --> AccessLevel

  %% Error handling is invoked on block overages enforcement
  BlockSeatOveragesModule -- triggers --> BlockOveragesError

  %% Sync workflow summary path
  SeatSyncInit ==> SyncSeatLinkWorker
  SyncSeatLinkWorker ==> BlockSeatOveragesModule
  BlockSeatOveragesModule -- checks --> SeatCounts
  BlockSeatOveragesModule -- checks --> RootNamespace
  BlockSeatOveragesModule -- checks --> AccessLevel
  BlockSeatOveragesModule -- parses --> InvitesList
  BlockSeatOveragesModule -- applies --> BlockOveragesError

  %% Logical groups annotation
  class CORE,BlockSeatOveragesModule,SyncSeatLinkWorker pastelBlue
  class UTILITIES,ProcessInvites,ParseInputList,ProcessEmails pastelYellow
  class DATA_STRUCTURES,SeatCounts,AccessLevel,InvitesList,RootNamespace pastelGreen
  class ERRORS,BlockOveragesError pastelRed
  class INIT,SeatSyncInit pastelPurple
```