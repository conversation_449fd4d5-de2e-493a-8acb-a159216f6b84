```mermaid
flowchart TD
  %% Color Definitions
  %% Core domain files: #D4F1F9 pastel blue
  %% Supporting/utility files: #FFF8DC pastel yellow
  %% Data structure files: #E0F8E0 pastel green
  %% Error handling files: #FFE4E1 pastel red
  %% Initialization/setup files: #E6E6FA pastel purple
  %% Group: #F8F8F8 background, pastel borders

  %% COLUMN LAYOUT
  %% VERTICAL: Core Loggers -> Supporting/Integration Loggers -> Specialized Context/Data Loggers -> Error/Reporting/Profiler -> Utilities, Formatters, Data Structure Handlers -> External/QA/System Integration

  %% CORE LOGGERS
  subgraph Core Application Loggers
    direction TB
    style Core Application Loggers fill:#F8F8F8,stroke:#A4D8EF,stroke-width:2,rounded=true
    AppJsonLogger["AppJsonLogger<br/>lib/gitlab/app_json_logger.rb"]:::core_logger
    AppTextLogger["AppTextLogger<br/>lib/gitlab/app_text_logger.rb"]:::core_logger
    AppLogger["AppLogger<br/>lib/gitlab/app_logger.rb"]:::core_logger
    JsonLogger["JsonLogger<br/>superclass<br/>implicit"]:::core_logger
    MultiDestinationLogger["MultiDestinationLogger<br/>superclass, implicit"]:::core_logger
  end

  %% SUPPORTING/INTEGRATION LOGGERS
  subgraph Supporting & Event-Based Loggers
    direction TB
    style Supporting & Event-Based Loggers fill:#F8F8F8,stroke:#F7E7C4,stroke-width:2,rounded=true

    AuthLogger["AuthLogger<br/>lib/gitlab/auth_logger.rb"]:::core_logger
    DeprecationJsonLogger["DeprecationJsonLogger<br/>lib/gitlab/deprecation_json_logger.rb"]:::core_logger
    IntegrationsLogger["IntegrationsLogger<br/>lib/gitlab/integrations_logger.rb"]:::core_logger
    RedactedSearchResultsLogger["RedactedSearchResultsLogger<br/>lib/gitlab/redacted_search_results_logger.rb"]:::core_logger
    FeatureLogger["FeatureLogger<br/>lib/feature/logger.rb"]:::core_logger
    ServicesLogger["ServicesLogger<br/>lib/gitlab/services/logger.rb"]:::core_logger
    GraphqlLogger["GraphqlLogger<br/>lib/gitlab/graphql_logger.rb"]:::core_logger
    FileHookLogger["FileHookLogger<br/>lib/gitlab/file_hook_logger.rb"]:::core_logger
    RepositoryCheckLogger["RepositoryCheckLogger<br/>lib/gitlab/repository_check_logger.rb"]:::core_logger
    EnvironmentLogger["EnvironmentLogger<br/>lib/gitlab/environment_logger.rb"]:::core_logger
    ProjectStatsRefreshConflictsLogger["ProjectStatsRefreshConflictsLogger<br/>lib/gitlab/project_stats_refresh_conflicts_logger.rb"]:::core_logger
  end

  %% SPECIALIZED/CONTEXT LOGGERS GEO, MEMORY, GRAPELOGGING, ARKOSE, etc
  subgraph Specialized & Contextual Loggers
    direction TB
    style Specialized & Contextual Loggers fill:#F8F8F8,stroke:#B1F6C7,stroke-width:2,rounded=true

    subgraph Geo Logging
      style Geo Logging fill:#F8F8F8,stroke:#A4D8EF,stroke-width:1.5,rounded=true
      GeoLogger["Geo::Logger<br/>ee/lib/gitlab/geo/logger.rb"]:::core_logger
      GeoLogCursorLogger["Geo::LogCursor::Logger<br/>ee/lib/gitlab/geo/log_cursor/logger.rb"]:::core_logger
      GeoProjectLogHelpers["Geo::ProjectLogHelpers<br/>ee/lib/gitlab/geo/project_log_helpers.rb"]:::supporting_logger
    end

    ArkoseLogger["Arkose::Logger<br/>ee/lib/arkose/logger.rb"]:::core_logger

    subgraph Memory Reporting
      style Memory Reporting fill:#F8F8F8,stroke:#B1F6C7,stroke-width:1.5,rounded=true
      DiagnosticReportsLogger["Memory::DiagnosticReportsLogger<br/>lib/gitlab/memory/diagnostic_reports_logger.rb"]:::core_logger
    end

    subgraph Grape Logging Framework
      style Grape Logging Framework fill:#F8F8F8,stroke:#A4D8EF,stroke-width:1,rounded=true
      subgraph Grape Logging Formatters
        style Grape Logging Formatters fill:#F8F8F8,stroke:#A4D8EF,stroke-width:1,rounded=true
        LogrageWithTimestamp["LogrageWithTimestamp<br/>lib/gitlab/grape_logging/formatters/lograge_with_timestamp.rb"]:::supporting_logger
      end

      subgraph Grape Loggers
        style Grape Loggers fill:#F8F8F8,stroke:#A4D8EF,stroke-width:1,rounded=true
        QueueDurationLogger["QueueDurationLogger<br/>lib/gitlab/grape_logging/loggers/queue_duration_logger.rb"]:::supporting_logger
        ContentLogger["ContentLogger<br/>lib/gitlab/grape_logging/loggers/content_logger.rb"]:::supporting_logger
        UrgencyLogger["UrgencyLogger<br/>lib/gitlab/grape_logging/loggers/urgency_logger.rb"]:::supporting_logger
        RouteLogger["RouteLogger<br/>lib/gitlab/grape_logging/loggers/route_logger.rb"]:::supporting_logger
        ContextLogger["ContextLogger<br/>lib/gitlab/grape_logging/loggers/context_logger.rb"]:::supporting_logger
        CloudflareLogger["CloudflareLogger<br/>lib/gitlab/grape_logging/loggers/cloudflare_logger.rb"]:::supporting_logger
        ExceptionLogger["ExceptionLogger<br/>lib/gitlab/grape_logging/loggers/exception_logger.rb"]:::supporting_logger
        FilterParameters["FilterParameters<br/>lib/gitlab/grape_logging/loggers/filter_parameters.rb"]:::supporting_logger
      end
    end
  end

  %% ERROR HANDLING/FORMATTERS/PROFILER RELATED
  subgraph Error Tracking & Exception Formatting
    direction TB
    style Error Tracking & Exception Formatting fill:#F8F8F8,stroke:#F2B4AF,stroke-width:2,rounded=true

    ErrorTrackingLogFormatter["ErrorTracking::LogFormatter<br/>lib/gitlab/error_tracking/log_formatter.rb"]:::error_logger
    ExceptionLogFormatter["ExceptionLogFormatter<br/>lib/gitlab/exception_log_formatter.rb"]:::error_logger
    ActiveContextErrorHandler["ActiveContext::ErrorHandler<br/>gems/gitlab-active-context/lib/active_context/error_handler.rb"]:::error_logger
  end

  subgraph Application Profiling & Backtrace Utilities
    direction TB
    style Application Profiling & Backtrace Utilities fill:#F8F8F8,stroke:#C2B8EE,stroke-width:1.5,rounded=true

    Profiler["Profiler<br/>lib/gitlab/profiler.rb"]:::supporting_logger
    BacktraceCleaner["BacktraceCleaner<br/>lib/gitlab/backtrace_cleaner.rb"]:::supporting_logger
  end

  %% UTILITY, DATA STRUCTURE, PROCESS MANAGEMENT, CACHING
  subgraph Infrastructure Utilities & Data Handlers
    direction TB
    style Infrastructure Utilities & Data Handlers fill:#F8F8F8,stroke:#E0F8E0,stroke-width:1.5,rounded=true

    ProcessManagement["ProcessManagement<br/>lib/gitlab/process_management.rb"]:::supporting_logger
    PatchSprocketsBaseFileDigestKey["Patch::SprocketsBaseFileDigestKey<br/>lib/gitlab/patch/sprockets_base_file_digest_key.rb"]:::supporting_logger
    CacheJsonRedisKeyed["JsonCaches::RedisKeyed<br/>lib/gitlab/cache/json_caches/redis_keyed.rb"]:::datastruct_logger
    EventStoreStore["EventStore::Store<br/>lib/gitlab/event_store/store.rb"]:::datastruct_logger
    SidekiqLogsJobs["SidekiqLogging::LogsJobs<br/>lib/gitlab/sidekiq_logging/logs_jobs.rb"]:::supporting_logger
  end

  %% TESTING/INTEGRATION/QA
  subgraph Observability Testing & System Integration
    direction TB
    style Observability Testing & System Integration fill:#F8F8F8,stroke:#DCD3EE,stroke-width:2,rounded=true

    Kibana["QA SystemLogs::Kibana<br/>qa/qa/support/system_logs/kibana.rb"]:::init_logger
    LoggableSpec["LoggableSpec<br/>spec/lib/gitlab/loggable_spec.rb"]:::init_logger
  end

  %% --------------------------
  %% LOGICAL RELATIONSHIPS/DEPENDECIES

  %% Inheritance/Composition Arrows

  AppJsonLogger -- extends --> JsonLogger
  AppTextLogger -- extends --> AppLogger
  AppLogger -- extends --> MultiDestinationLogger
  AuthLogger -- extends --> JsonLogger
  DeprecationJsonLogger -- extends --> JsonLogger
  IntegrationsLogger -- extends --> JsonLogger
  RedactedSearchResultsLogger -- extends --> JsonLogger
  FeatureLogger -- extends --> JsonLogger
  ServicesLogger -- extends --> JsonLogger
  GraphqlLogger -- extends --> JsonLogger
  FileHookLogger -- extends --> AppLogger
  RepositoryCheckLogger -- extends --> AppLogger
  ProjectStatsRefreshConflictsLogger --|writes to|--> AppLogger
  
  GeoLogger -- extends --> JsonLogger
  GeoLogCursorLogger -- uses --> GeoLogger
  GeoProjectLogHelpers -- includes --> GeoLogger
  GeoProjectLogHelpers -- includes --> LogHelpers["LogHelpers implicit"]:::supporting_logger

  ArkoseLogger -- uses --> JsonLogger
  
  DiagnosticReportsLogger -- extends --> AppLogger

  LogrageWithTimestamp -- uses --> EncodingHelper["EncodingHelper implicit"]:::supporting_logger

  QueueDurationLogger -- extends --> GrapeLoggingBase["GrapeLogging::Loggers::Base implicit"]:::supporting_logger
  ContentLogger -- extends --> GrapeLoggingBase
  UrgencyLogger -- extends --> GrapeLoggingBase
  RouteLogger -- extends --> GrapeLoggingBase
  ContextLogger -- extends --> GrapeLoggingBase
  CloudflareLogger -- extends --> GrapeLoggingBase
  ExceptionLogger -- extends --> GrapeLoggingBase
  FilterParameters -- extends --> GrapeFilterBase["GrapeLogging::Loggers::FilterParameters implicit"]:::supporting_logger

  ErrorTrackingLogFormatter -- uses --> ExceptionLogFormatter
  ExceptionLogFormatter --|formats exceptions for|--> (all loggers in Core Application Loggers & Supporting & Event-Based Loggers)

  ActiveContextErrorHandler -- uses --> ActiveContextLogger["ActiveContext::Logger implicit"]:::supporting_logger

  Profiler --|uses cleaned data from|--> BacktraceCleaner
  Profiler --|uses|--> AppLogger
  
  AppLogger --|writes logs to|--> AppJsonLogger

  RepositoryCheckLogger --|writes logs to|--> AppLogger
  FileHookLogger --|writes logs to|--> AppLogger

  ProjectStatsRefreshConflictsLogger --|warns via|--> AppLogger

  %% Grapelogging Formatters/Loggers
  LogrageWithTimestamp -- processes data for --> AppJsonLogger
  GrapeLoggingBase -- used by --> AppJsonLogger

  %% Specialized Context
  DiagnosticReportsLogger --|outputs reports to|--> AppLogger

  %% Data Structure Handling
  EventStoreStore --|publishes events for|--> ServicesLogger

  CacheJsonRedisKeyed --|provides cache for|--> AppLogger

  SidekiqLogsJobs --|formats Sidekiq logs for|--> AppJsonLogger

  ProcessManagement --|signal handling for|--> (all logger processes)

  PatchSprocketsBaseFileDigestKey --|used at|--> AppLogger

  Kibana --|system log analysis|--> AppJsonLogger
  Kibana --|tests log outputs from|--> AppLogger
  LoggableSpec --|tests|--> AppLogger

  %% Cross-domain
  ProjectStatsRefreshConflictsLogger --|detects conflicts for|--> RepositoryCheckLogger

  %% Graph Style Definitions
  classDef core_logger fill:#D4F1F9,stroke:#A4D8EF,stroke-width:1,stroke-dasharray: 2,rx:10,ry:10;
  classDef supporting_logger fill:#FFF8DC,stroke:#F7E7C4,stroke-width:1,stroke-dasharray: 2,rx:10,ry:10;
  classDef datastruct_logger fill:#E0F8E0,stroke:#B1F6C7,stroke-width:1,stroke-dasharray: 1,rx:10,ry:10;
  classDef error_logger fill:#FFE4E1,stroke:#F2B4AF,stroke-width:1,stroke-dasharray: 2,rx:10,ry:10;
  classDef init_logger fill:#E6E6FA,stroke:#DCD3EE,stroke-width:1,stroke-dasharray: 2,rx:10,ry:10;
```