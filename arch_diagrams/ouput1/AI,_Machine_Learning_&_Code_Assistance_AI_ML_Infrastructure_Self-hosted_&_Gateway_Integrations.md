```mermaid
flowchart TB
  %% VERTICAL LAYOUT
  %% STYLING PALETTE
  %% Core domain files: #D4F1F9
  %% Supporting/utility files: #FFF8DC
  %% Data structures: #E0F8E0
  %% Error handling: #FFE4E1
  %% Initialization/setup: #E6E6FA
  %% Subgraph: #F8F8F8 with colored border

  %% === DATA STRUCTURES & MODELS ===
  subgraph Data_Structures_and_Models["Data Structures & Core Model" ]
    direction TB
    style Data_Structures_and_Models fill:#F8F8F8,stroke:#6EC6E9,stroke-width:2,rounded
    SelfHostedModel["SelfHostedModel\nee/app/models/ai/self_hosted_model.rb"]:::coremodel
    ReleaseStateEnum["ReleaseStateEnum\nee/app/graphql/types/ai/self_hosted_models/release_state_enum.rb"]:::datastruct
    AcceptedModelsEnum["AcceptedModelsEnum\nee/app/graphql/types/ai/self_hosted_models/accepted_models_enum.rb"]:::datastruct
    ModelMetadata["ModelMetadata\nee/lib/gitlab/llm/ai_gateway/model_metadata.rb"]:::datastruct
  end

  %% === GRAPHQL API LAYER (Mutations & Resolvers) ===
  subgraph GraphQL_API["GraphQL API Layer" ]
    direction TB
    style GraphQL_API fill:#F8F8F8,stroke:#6EC6E9,stroke-width:2,rounded
    subgraph GraphQL_Mutations["Mutations: Model CRUD & Connection"]
      direction TB
      style GraphQL_Mutations fill:#F8F8F8,stroke:#97C867,stroke-width:1
      SHMCreate["Create\nee/app/graphql/mutations/ai/self_hosted_models/create.rb"]:::core
      SHMUpdate["Update\nee/app/graphql/mutations/ai/self_hosted_models/update.rb"]:::core
      SHMDelete["Delete\nee/app/graphql/mutations/ai/self_hosted_models/delete.rb"]:::core
      SHMConnCheck["ConnectionCheck\nee/app/graphql/mutations/ai/self_hosted_models/connection_check.rb"]:::core
      SHMBase["Base Abstraction\nee/app/graphql/mutations/ai/self_hosted_models/base.rb"]:::utility
    end
    subgraph GraphQL_Resolvers["Resolvers"]
      direction TB
      style GraphQL_Resolvers fill:#F8F8F8,stroke:#97C867,stroke-width:1
      SHMResolver["SelfHostedModelsResolver\nee/app/graphql/resolvers/ai/self_hosted_models/self_hosted_models_resolver.rb"]:::core
      AgentFindResolver["FindAgentResolver\nee/app/graphql/resolvers/ai/agents/find_agent_resolver.rb"]:::core
      AgentDetailResolver["AgentDetailResolver\nee/app/graphql/resolvers/ai/agents/agent_detail_resolver.rb"]:::core
    end
  end

  %% === SERVICE LAYER (CRUD, Auditing) ===
  subgraph Service_Layer["Domain Services & Creation/Destruction"]
    direction TB
    style Service_Layer fill:#F8F8F8,stroke:#4EB987,stroke-width:2,rounded
    SHMCreateService["CreateService\nee/app/services/ai/self_hosted_models/create_service.rb"]:::core
    SHMUpdateService["UpdateService\nee/app/services/ai/self_hosted_models/update_service.rb"]:::core
    SHMDestroyService["DestroyService\nee/app/services/ai/self_hosted_models/destroy_service.rb"]:::core
  end

  %% === INTEGRATIONS & INFRASTRUCTURE: GATEWAY, PROBES, CONNECTORS  ===
  subgraph Integrations_Infra["AI/ML Infrastructure, Gateway & Probing"]
    direction TB
    style Integrations_Infra fill:#F8F8F8,stroke:#A68EC7,stroke-width:2,rounded
    subgraph Gateway_Core["Gateway Abstractions & Tasks"]
      direction TB
      style Gateway_Core fill:#F8F8F8,stroke:#B5D8EE,stroke-width:1
      AiGatewayApi["AiGateway API\nee/lib/gitlab/ai_gateway.rb"]:::core
      ChainReqAiGateway["ChainReq::AiGateway\nee/lib/gitlab/llm/chain/requests/ai_gateway.rb"]:::core
      GatewayModelConfigCheck["ModelConfigCheck\nee/lib/gitlab/llm/ai_gateway/self_hosted_models/tasks/model_config_check.rb"]:::utility
      SelfHostedAiGateway["SelfHosted::AiGateway\nee/lib/gitlab/ai/self_hosted/ai_gateway.rb"]:::core
    end

    subgraph Probes_and_Connectors["Connection Probes & Cloud Integration"]
      direction TB
      style Probes_and_Connectors fill:#F8F8F8,stroke:#B5D8EE,stroke-width:1
      ProbeHost["HostProbe\nee/app/services/cloud_connector/status_checks/probes/host_probe.rb"]:::utility
      ProbeAccess["AccessProbe\nee/app/services/cloud_connector/status_checks/probes/access_probe.rb"]:::utility
    end
  end

  %% === ADMIN & UI LAYER (Helpers) ===
  subgraph AdminUI["Admin & Helpers"]
    direction TB
    style AdminUI fill:#F8F8F8,stroke:#DBE295,stroke-width:2,rounded
    AdminSHMHelper["SelfHostedModelsHelper\nee/app/helpers/admin/ai/self_hosted_models_helper.rb"]:::utility
  end

  %% === LANGSMITH & REMOTE DEV ===
  subgraph Ext_Integrations["External Integrations & Remote Dev"]
    direction TB
    style Ext_Integrations fill:#F8F8F8,stroke:#E7B6AC,stroke-width:2,rounded
    LangsmithClient["Langsmith::Client\nee/lib/langsmith/client.rb"]:::utility
    subgraph RemoteDev["Remote Development"]
      direction TB
      style RemoteDev fill:#F8F8F8,stroke:#E6B6E9,stroke-width:1
      RemoteDevMain["AgentConfigMain\nee/lib/remote_development/agent_config_operations/main.rb"]:::utility
      RemoteDevFiles["Files\nee/lib/remote_development/files.rb"]:::utility
      RemoteDevConsts["Constants\nee/lib/remote_development/remote_development_constants.rb"]:::utility
    end
  end

  %% ==== NODE STYLES ====
  classDef core fill:#D4F1F9,stroke:#97C6E6,stroke-width:1,rx:10,ry:10
  classDef coremodel fill:#D4F1F9,stroke:#5BACD7,stroke-width:2,rx:10,ry:10
  classDef datastruct fill:#E0F8E0,stroke:#94CFAA,stroke-width:1,rx:10,ry:10
  classDef utility fill:#FFF8DC,stroke:#E9C96E,stroke-width:1,rx:10,ry:10
  classDef error fill:#FFE4E1,stroke:#ED9696,stroke-width:1,rx:10,ry:10
  classDef init fill:#E6E6FA,stroke:#AFA2CF,stroke-width:1,rx:10,ry:10

  %% === LOGICAL RELATIONSHIPS & DEPENDENCIES ===

  %% GraphQL mutations use base for abstraction
  SHMCreate -- extends/uses --> SHMBase
  SHMUpdate -- extends/uses --> SHMBase
  SHMDelete -- extends/uses --> SHMBase
  SHMConnCheck -- extends/uses --> SHMBase

  %% CRUD mutations & connection check coordinate with services
  SHMCreate -- invokes --> SHMCreateService
  SHMUpdate -- invokes --> SHMUpdateService
  SHMDelete -- invokes --> SHMDestroyService
  SHMConnCheck -- delegates --> GatewayModelConfigCheck

  %% Services operate on the core model
  SHMCreateService -- persists --> SelfHostedModel
  SHMUpdateService -- persists --> SelfHostedModel
  SHMDestroyService -- destroys --> SelfHostedModel

  %% CRUD resolvers operate over model, via access control
  SHMResolver -- queries --> SelfHostedModel
  SHMResolver -- maps_enum --> ReleaseStateEnum
  SHMResolver -- maps_enum --> AcceptedModelsEnum

  %% Model supports release states & accepted model enums
  SelfHostedModel -- has --> ReleaseStateEnum
  SelfHostedModel -- supports --> AcceptedModelsEnum

  %% ModelMetadata enables serialization of model configs for gateway APIs
  ModelMetadata -- serializes --> SelfHostedModel

  %% Admin helpers utilize enums and model for UI logic/option population
  AdminSHMHelper -- symbols --> AcceptedModelsEnum
  AdminSHMHelper -- symbols --> ReleaseStateEnum

  %% AI Gateway: central for remote inference calls and connection checks
  AiGatewayApi -- uses --> ModelMetadata
  AiGatewayApi -- calls --> SelfHostedModel

  ChainReqAiGateway -- leverages --> AiGatewayApi

  GatewayModelConfigCheck -- reads --> SelfHostedModel
  GatewayModelConfigCheck -- builds_metadata_params --> ModelMetadata

  %% SelfHosted::AiGateway composes multiple probes and uses AiGatewayApi
  SelfHostedAiGateway -- composes --> ProbeHost
  SelfHostedAiGateway -- composes --> AiGatewayApi

  %% Probes test service URLs and access for models
  ProbeHost -- checks --> SelfHostedModel
  ProbeAccess -- checks --> SelfHostedModel

  %% GraphQL connection check mutation builds probe to validate models
  SHMConnCheck -- executes --> ProbeHost
  SHMConnCheck -- executes --> ProbeAccess

  %% Agent resolvers for deeper integrations
  AgentFindResolver -- uses --> SelfHostedModel
  AgentDetailResolver -- uses --> SelfHostedModel

  %% Langsmith allows for experiment/test integration; shares model context
  LangsmithClient -- syncs_runs_with --> SelfHostedModel

  %% Remote development: agent config references model types, for edge deployments
  RemoteDevMain -- references --> SelfHostedModel
  RemoteDevMain -- references --> AdminSHMHelper
  RemoteDevMain -- constant --> RemoteDevConsts
  RemoteDevMain -- uses --> RemoteDevFiles

  %% ==== HIERARCHY: DATA FLOW / CONTROL SUMMARY ==== 
  %% CLIENT/ADMIN/UI -> GraphQL Mutations/Resolvers/Helpers -> Services -> Core Model -> Gateway & Integration
  %% DATA STRUCTURES link to Model, ModelMetadata, Enums

  %% APPLICATION LOGICAL FLOWS
  SHMCreate -- returns --> SHMResolver
  SHMUpdate -- returns --> SHMResolver
  SHMDelete -- invalidates --> SHMResolver
  SHMConnCheck -- returns_status_to --> SHMResolver

  SHMResolver -- provides_model_list --> AdminSHMHelper

  SHMCreateService -- audits --> SHMUpdateService
  SHMUpdateService -- audits --> SHMDestroyService

  AiGatewayApi -- invokes_remote --> ChainReqAiGateway

  GatewayModelConfigCheck -- reports_status_to --> SHMConnCheck

  %% === END DIAGRAM ===
```