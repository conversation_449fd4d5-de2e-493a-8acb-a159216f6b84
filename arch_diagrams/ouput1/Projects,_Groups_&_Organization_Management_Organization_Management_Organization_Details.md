```mermaid
flowchart TD
  %% STYLES
  classDef core fill:#D4F1F9,stroke:#BDE8F5,stroke-width:2px,color:#222,rx:10,ry:10
  classDef utility fill:#FFF8DC,stroke:#F8EAB8,stroke-width:2px,color:#222,rx:10,ry:10
  classDef data fill:#E0F8E0,stroke:#B7DFB7,stroke-width:2px,color:#222,rx:10,ry:10
  classDef error fill:#FFE4E1,stroke:#FFCCCC,stroke-width:2px,color:#222,rx:10,ry:10
  classDef init fill:#E6E6FA,stroke:#B3B3D6,stroke-width:2px,color:#222,rx:10,ry:10
  classDef group fill:#F8F8F8,stroke:#DCDCDC,stroke-width:2px,color:#222

  %% DOMAIN: ORGANIZATION MANAGEMENT - ORGANIZATION DETAILS

  %% 1. ORGANIZATION CORE DATA STRUCTURES
  subgraph A["Organization Core Data Structures"]
    direction TB
    ORG_MODEL["organizations/organization.rb\n- Organization entity\n- Core business logic\n- Visibility rules, scopes\n- Integrates concerns: SQL Patterns, VisibilityLevel\n- Domain constants e.g. default org id"]:::core
    ORG_DETAIL_MODEL["organizations/organization_detail.rb\n- OrganizationDetail entity\n- Markdown for description, avatars, uploads\n- 'Belongs to' relationship with Organization\n- Data validation"]:::data
    DESC_VERSION["description_version.rb\n- Version control for markdown descriptions\n- Track parent namespace\n- Linked to OrgDetail for auditing changes"]:::data
    INTERNAL_ID["internal_id.rb\n- InternalId generator & tracker\n- Uniqueness + atomic increment\n- Used to assign unique ids to Organizations\n- Supports 'track_greatest'"]:::utility
    EN_ORG_EE["ee/organizations/organization.rb\n- EE-specific Organization extensions\n- Extra associations projects, exports, sbom\n- Prepends Organization model"]:::core
  end
  class A group

  %% 2. CONTROLLERS: EXPOSE DOMAIN BEHAVIORS
  subgraph B["Organization Domain Controllers"]
    direction TB
    ORG_APP_CONTROLLER["organizations/application_controller.rb\n- Organization-wide filters\n- Loads organization context from params\n- Authorizations"]:::core
    ORGS_CONTROLLER["organizations/organizations_controller.rb\n- Handles org show/activity/index\n- Activities, markdown preview\n- Event filtering, resource limits"]:::core
    GROUPS_CONTROLLER["organizations/groups_controller.rb\n- Managing org groups CRUD\n- Authorizations: create/view/edit/remove group\n- Integrates Params Module"]:::core
    ADMIN_ORG_CONTROLLER["admin/organizations_controller.rb\n- Admin area for organizations\n- Feature flag checks allow creation, index\n- Sets up admin-view context"]:::core
  end
  class B group

  %% 3. DOMAIN SERVICES: BUSINESS LOGIC & DATA MUTATIONS
  subgraph C["Domain Services & Business Logic"]
    direction TB
    ORG_SVC_BASE["organizations/base_service.rb\n- Service object utility base\n- Shared organizational logic\n- Initializes org data attributes"]:::core
    ORG_SVC_CREATE["organizations/create_service.rb\n- Inherits BaseService\n- Implements org creation with permissions/feature flag checks\n- Handles cross-db safety, owner setup\n- Error handling for permissions"]:::core
    ORG_SVC_UPDATE["customer_relations/organizations/update_service.rb\n- Updates org details as service\n- Handles update/active param\n- Encapsulates error handling\n- Uses ServiceResponse wrapper"]:::core
    FALLBACK_TRACKER["lib/gitlab/organizations/fallback_organization_tracker.rb\n- Tracks fallback org globally\n- Used in Current/Organization context\n- Enable/disable tracking blocks"]:::utility
    DEFAULT_ORG_IMPORTER["lib/gitlab/database_importers/default_organization_importer.rb\n- Data initialization for default org\n- Ensures ID/attributes for system org\n- Used at database setup/init"]:::init
  end
  class C group

  %% 4. POLICIES: AUTHORIZATION/ACCESS CONTROL
  subgraph D["Policies & Access Control"]
    direction TB
    ORG_POLICY["organizations/organization_policy.rb\n- Main policy: read, create, ownership, visibility\n- Integration with public? check\n- Used by controllers/services"]:::core
    ORG_USER_POLICY["organizations/organization_user_policy.rb\n- Policy for users inside org context\n- Handles last owner logic\n- Rules for admin org"]:::core
    ORG_POLICY_EE["ee/organizations/organization_policy.rb\n- EE: extra access rules\n- Integrates remote development policies, features\n- Prepends/extends OSS org policy"]:::core
    REMOTE_DEV_POLICY["ee/policies/remote_development/organization_policy.rb\n- Cluster agent mapping policy EE\n- Rule extensions for org resources\n- Used via prepending in EE"]:::core
    ORG_PERMISSIONS_TYPE["graphql/types/permission_types/organization_user.rb\n- GraphQL PermissionType for org user\n- Defines abilities for org membership"]:::data
  end
  class D group

  %% 5. PRESENTATION & HELPERS: VIEW LAYER BRIDGE
  subgraph E["Helpers & Presentation"]
    direction TB
    ORG_HELPER["helpers/organizations/organization_helper.rb\n- Navigation helpers, org context \n- App data serialization for JS apps\n- Provides paths/helpers for organization UIs"]:::utility
    EE_ORG_HELPER["ee/helpers/ee/organizations/organization_helper.rb\n- EE: augments org helper with advanced activity event types\n- Extends/overrides OSS helpers"]:::utility
  end
  class E group

  %% 6. VALIDATORS: DOMAIN-SPECIFIC RULES
  subgraph F["Validation & Data Quality"]
    direction TB
    ORG_PATH_VALIDATOR["validators/organizations/path_validator.rb\n- Extends AbstractPathValidator\n- Path/format validation using regex\n- Guards against reserved names"]:::utility
  end
  class F group

  %% 7. FINDERS, RESOLVERS: DATA QUERY/GRAPHQL EDGE
  subgraph G["Data Finders & GraphQL Resolvers"]
    direction TB
    ORG_USERS_FINDER["finders/organizations/organization_users_finder.rb\n- Finds users within org context\n- Handles authorization checks\n- Returns filtered user sets"]:::core
    ORGS_GRAPHQL_RESOLVER["graphql/resolvers/crm/organizations_resolver.rb\n- BaseResolver subclass\n- Handles sorting, authorization\n- Integrates org type for GraphQL API"]:::core
  end
  class G group

  %% 8. CONTEXT/ATTRIBUTES: REQUEST CONTEXT SUPPORT
  subgraph H["Contextual & Session State"]
    direction TB
    CURRENT_ORG_ATTR["models/current.rb\n- Thread-local: Current.organization\n- Ensures org is set for current request\n- Handles missing assignment error"]:::utility
    CURRENT_ORG_CONTEXT["lib/gitlab/current/organization.rb\n- Extracts org from params/user/session\n- Resolves proper org context via various sources\n- Supports fallback behavior"]:::utility
  end
  class H group

  %% 9. SUPPORT STRUCTURES & UTILITIES
  subgraph I["Supporting Structures"]
    direction TB
    PATH_ENUMS["models/concerns/enums/abuse/category.rb\n- Defines abuse categories\n- Used by organizations for abuse checks/reporting"]:::utility
    ACHIEVEMENTS_MODEL["models/achievements/user_achievement.rb\n- Associations: users' achievements link\n- Award/Revocation tracking\n- Indirectly associated with organization context"]:::data
    GHOST_USER_MIG["models/users/ghost_user_migration.rb\n- Handles user migration to 'ghost user' state\n- Org related if owner leaves or is removed"]:::data
  end
  class I group

  %% === RELATIONSHIPS & INTERACTIONS ===

  %% DATA & ENTITY RELATIONSHIPS
  ORG_MODEL -- "has one" --- ORG_DETAIL_MODEL
  ORG_MODEL -- "has many desc" --- DESC_VERSION
  ORG_MODEL -- "uses ID allocation" --- INTERNAL_ID
  EN_ORG_EE ---|preprends/extends| ORG_MODEL

  ORG_DETAIL_MODEL -- "links" --- DESC_VERSION

  %% CONTROLLER-DATA LINKAGE
  ORG_APP_CONTROLLER -- "loads context for" --- ORGS_CONTROLLER
  ORG_APP_CONTROLLER -- "loads context for" --- GROUPS_CONTROLLER
  ORG_APP_CONTROLLER -- "shared base for" --- ADMIN_ORG_CONTROLLER
  ORGS_CONTROLLER -- "queries, persists" --> ORG_MODEL
  ORGS_CONTROLLER -- "accesses details" --- ORG_DETAIL_MODEL
  ORGS_CONTROLLER -- "changes, fetches versions" --- DESC_VERSION

  ORGS_CONTROLLER -- "uses" --- ORG_USERS_FINDER
  ORGS_CONTROLLER -- "fetches permissions" --- ORG_POLICY
  GROUPS_CONTROLLER -- "validates group paths" --- ORG_PATH_VALIDATOR
  GROUPS_CONTROLLER -- "uses policies" --- ORG_POLICY

  ADMIN_ORG_CONTROLLER -- "uses" --- ORG_SVC_CREATE
  ADMIN_ORG_CONTROLLER -- "checks feature flags" --- ORG_SVC_BASE

  %% SERVICE LAYER/DOMAIN ACTIONS
  ORG_SVC_CREATE -- "creates" --- ORG_MODEL
  ORG_SVC_CREATE -- "persists details" --- ORG_DETAIL_MODEL
  ORG_SVC_CREATE -- "raises errors" --- ORG_SVC_BASE
  ORG_SVC_CREATE -- "uses" --- ORG_POLICY
  ORG_SVC_CREATE -- "assigns unique ids" --- INTERNAL_ID

  ORG_SVC_BASE -- "shared by" --- ORG_SVC_CREATE
  ORG_SVC_UPDATE -- "updates" --- ORG_MODEL
  ORG_SVC_UPDATE -- "runs authorization" --- ORG_POLICY

  DEFAULT_ORG_IMPORTER -- "uses" --- ORG_MODEL
  DEFAULT_ORG_IMPORTER -- "initializes default details" --- ORG_DETAIL_MODEL

  FALLBACK_TRACKER -- "used by" --- CURRENT_ORG_CONTEXT

  %% POLICY/ACCESS RELATIONSHIPS
  ORG_POLICY -- "applies to" --- ORG_MODEL
  ORG_USER_POLICY -- "applies to" --- ORG_MODEL
  ORG_USER_POLICY -- "rules for" --- ORG_USERS_FINDER
  ORG_POLICY_EE -- "preprends" --- ORG_POLICY
  REMOTE_DEV_POLICY -- "preprends" --- ORG_POLICY_EE
  ORG_PERMISSIONS_TYPE -- "maps to abilities on" --- ORG_USER_POLICY

  %% CONTEXTUAL ATTRIBUTES RELATIONSHIPS
  CURRENT_ORG_ATTR -- "provides Current.organization for" --- ORG_APP_CONTROLLER
  CURRENT_ORG_ATTR -- "raises" --- ORG_SVC_CREATE
  CURRENT_ORG_CONTEXT -- "feeds into" --- CURRENT_ORG_ATTR
  CURRENT_ORG_CONTEXT -- "uses fallback via" --- FALLBACK_TRACKER

  %% DATA FINDER/RESOLVER LOGIC
  ORG_USERS_FINDER -- "filters uses of" --- ORG_POLICY
  ORG_USERS_FINDER -- "returns" --- ORG_PERMISSIONS_TYPE
  ORGS_GRAPHQL_RESOLVER -- "wraps org query for" --- ORG_MODEL
  ORGS_GRAPHQL_RESOLVER -- "maps permissions using" --- ORG_PERMISSIONS_TYPE

  %% HELPERS & PRESENTATION
  ORG_HELPER -- "serializes org for frontend" --- ORG_MODEL
  ORG_HELPER -- "serializes details for frontend" --- ORG_DETAIL_MODEL
  EE_ORG_HELPER -- "extends" --- ORG_HELPER

  %% VALIDATION, UTILITY, SUPPORT
  ORG_PATH_VALIDATOR -- "used by" --- GROUPS_CONTROLLER
  ORG_PATH_VALIDATOR -- "validates path for" --- ORG_MODEL
  PATH_ENUMS -- "referenced by" --- ORG_POLICY
  ACHIEVEMENTS_MODEL -- "records for users in" --- ORG_MODEL
  GHOST_USER_MIG -- "used if user leaves" --- ORG_MODEL

  %% EE/OSS AUGMENTATIONS
  EN_ORG_EE -- "adds associations to" --- ORG_MODEL
  ORG_POLICY_EE -- "wraps/adds rules to" --- ORG_POLICY
  REMOTE_DEV_POLICY -- "augments policies for" --- ORG_MODEL
  EE_ORG_HELPER -- "extends" --- ORG_HELPER

  %% DATA INITIALIZATION
  DEFAULT_ORG_IMPORTER -- "called at db/init" --- ORG_MODEL
  DEFAULT_ORG_IMPORTER -- "sets up default details" --- ORG_DETAIL_MODEL

  %% FORMATTING/MODELLING DETAILS
  classDef controller fill:#D4F1F9,stroke:#BDE8F5,stroke-width:2px,color:#222,rx:10,ry:10
  class ORG_APP_CONTROLLER,ORGS_CONTROLLER,GROUPS_CONTROLLER,ADMIN_ORG_CONTROLLER controller
  class ORG_POLICY,ORG_USER_POLICY,ORG_POLICY_EE,REMOTE_DEV_POLICY,ORG_PERMISSIONS_TYPE core
  class ORG_MODEL,EN_ORG_EE,ORG_DETAIL_MODEL core
  class DESC_VERSION,ACHIEVEMENTS_MODEL,GHOST_USER_MIG,data
  class INTERNAL_ID,ORG_PATH_VALIDATOR,ORG_HELPER,EE_ORG_HELPER,FALLBACK_TRACKER,PATH_ENUMS utility
  class CURRENT_ORG_ATTR,CURRENT_ORG_CONTEXT utility
  class ORG_USERS_FINDER,ORGS_GRAPHQL_RESOLVER core
  class ORG_SVC_CREATE,ORG_SVC_BASE,ORG_SVC_UPDATE core
  class DEFAULT_ORG_IMPORTER init

  %% CROSS-GROUP RELATIONSHIPS
  ORG_HELPER -- "used in" --- ORGS_CONTROLLER
  ORG_HELPER -- "used in" --- GROUPS_CONTROLLER

  %% BORDERS/GROUPING: SUBGRAPH BORDERS
  linkStyle default stroke-width:2px,stroke:#BBB,opacity:0.85
```