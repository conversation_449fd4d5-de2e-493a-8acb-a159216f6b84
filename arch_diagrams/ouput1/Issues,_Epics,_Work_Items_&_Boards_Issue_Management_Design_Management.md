```mermaid
flowchart TD
  %% Global Styles
  %% Color definitions from instructions
  %% Core Domain: pastel blue #D4F1F9
  %% Supporting/utility: pastel yellow #FFF8DC
  %% Data structure: pastel green #E0F8E0
  %% Error handling: pastel red #FFE4E1
  %% Initialization/setup: pastel purple #E6E6FA
  %% Subgraph background: very light gray #F8F8F8

  %% MAIN DOMAIN LAYER
  subgraph DOMAIN_ISSUES_EPICS_WORK_ITEMS_BOARD["Issues, Epics, Work Items & Boards / Issue & Design Management"]
    direction TB
    style DOMAIN_ISSUES_EPICS_WORK_ITEMS_BOARD fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,corner-radius:8px

    %% CORE CONCEPTS: DESIGN MANAGEMENT MODELS
    subgraph DG_MODELS["Design Management Core Models"]
      direction TB
      style DG_MODELS fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,corner-radius:8px

      dmDesign["Design design.rb"]
      style dmDesign fill:#D4F1F9,stroke:#738FA7,stroke-width:1,corner-radius:8px

      dmDesignAtVersion["DesignAtVersion design_at_version.rb"]
      style dmDesignAtVersion fill:#D4F1F9,stroke:#738FA7,stroke-width:1,corner-radius:8px

      dmRepository["Repository repository.rb"]
      style dmRepository fill:#D4F1F9,stroke:#738FA7,stroke-width:1,corner-radius:8px

      dmDesignCollection["DesignCollection design_collection.rb"]
      style dmDesignCollection fill:#D4F1F9,stroke:#738FA7,stroke-width:1,corner-radius:8px

      dmDesignAction["DesignAction design_action.rb"]
      style dmDesignAction fill:#D4F1F9,stroke:#738FA7,stroke-width:1,corner-radius:8px

      dmVersion["Version version.rb"]
      style dmVersion fill:#D4F1F9,stroke:#738FA7,stroke-width:1,corner-radius:8px

      %% Logical/model dependencies
      dmDesignCollection -->|handles collection of| dmDesign
      dmDesignCollection -->|tracks versions via| dmVersion
      dmDesignCollection -->|connects to| dmRepository
      dmDesign -->|belongs to| dmRepository
      dmDesign -->|has many| dmDesignAction
      dmDesign -->|has many| dmVersion
      dmVersion -->|has many| dmDesign
      dmDesignAtVersion -->|references| dmDesign
      dmDesignAtVersion -->|references| dmVersion
      dmDesignAction -->|operates on| dmDesign
      dmVersion -->|created via| dmDesignAction
      dmRepository -- Project integration --> dmDesign
      dmRepository -- Project integration --> dmDesignCollection
      dmRepository -- Project integration --> dmVersion
    end

    %% DATA STRUCTURE FILES
    subgraph DATA_STRUCTURES["Domain Data Structures"]
      direction TB
      style DATA_STRUCTURES fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2,corner-radius:8px

      dmAction["Action action.rb"]
      style dmAction fill:#E0F8E0,stroke:#5B8A72,stroke-width:1,corner-radius:8px

      designFinder["DesignsFinder designs_finder.rb"]
      style designFinder fill:#E0F8E0,stroke:#5B8A72,stroke-width:1,corner-radius:8px
    end

    %% DOMAIN POLICIES FOR SECURITY/ACCESS
    subgraph DM_POLICIES["Design Management Policies"]
      direction TB
      style DM_POLICIES fill:#F8F8F8,stroke:#FFE4E1,stroke-width:2,corner-radius:8px

      dmDesignPolicy["DesignPolicy design_policy.rb"]
      style dmDesignPolicy fill:#FFE4E1,stroke:#AA7070,stroke-width:1,corner-radius:8px
      dmDesignCollectionPolicy["DesignCollectionPolicy design_collection_policy.rb"]
      style dmDesignCollectionPolicy fill:#FFE4E1,stroke:#AA7070,stroke-width:1,corner-radius:8px
      dmRepositoryPolicy["RepositoryPolicy repository_policy.rb"]
      style dmRepositoryPolicy fill:#FFE4E1,stroke:#AA7070,stroke-width:1,corner-radius:8px
      dmVersionPolicy["VersionPolicy version_policy.rb"]
      style dmVersionPolicy fill:#FFE4E1,stroke:#AA7070,stroke-width:1,corner-radius:8px

      %% Delegation relationships
      dmDesignPolicy --delegates to Issue--> dmDesign
      dmDesignCollectionPolicy --inherits from--> dmDesignPolicy
      dmRepositoryPolicy --delegates to Project--> dmRepository
      dmVersionPolicy --delegates to Issue--> dmVersion
    end

    %% SERVICES FOR DOMAIN BEHAVIORS
    subgraph DESIGN_SERVICES["Design Management Services"]
      direction TB
      style DESIGN_SERVICES fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,corner-radius:8px

      baseDesignService["Base Service design_service.rb"]
      style baseDesignService fill:#FFF8DC,stroke:#D2B886,stroke-width:1,corner-radius:8px

      saveDesignsService["SaveDesignsService save_designs_service.rb"]
      style saveDesignsService fill:#D4F1F9,stroke:#738FA7,stroke-width:1,corner-radius:8px

      deleteDesignsService["DeleteDesignsService delete_designs_service.rb"]
      style deleteDesignsService fill:#D4F1F9,stroke:#738FA7,stroke-width:1,corner-radius:8px

      moveDesignsService["MoveDesignsService move_designs_service.rb"]
      style moveDesignsService fill:#D4F1F9,stroke:#738FA7,stroke-width:1,corner-radius:8px

      designUserNotesCountService["DesignUserNotesCountService design_user_notes_count_service.rb"]
      style designUserNotesCountService fill:#FFF8DC,stroke:#D2B886,stroke-width:1,corner-radius:8px

      generateImageVersionsService["GenerateImageVersionsService generate_image_versions_service.rb"]
      style generateImageVersionsService fill:#D4F1F9,stroke:#738FA7,stroke-width:1,corner-radius:8px

      runsDesignActions["RunsDesignActions runs_design_actions.rb"]
      style runsDesignActions fill:#FFF8DC,stroke:#D2B886,stroke-width:1,corner-radius:8px

      copyDesignCollectionQService["CopyDesignCollection::QueueService copy_design_collection/queue_service.rb"]
      style copyDesignCollectionQService fill:#FFF8DC,stroke:#D2B886,stroke-width:1,corner-radius:8px

      copyDesignCollectionCopyService["CopyDesignCollection::CopyService copy_design_collection/copy_service.rb"]
      style copyDesignCollectionCopyService fill:#D4F1F9,stroke:#738FA7,stroke-width:1,corner-radius:8px

      %% Service relationships
      saveDesignsService --uses--> runsDesignActions
      saveDesignsService --uses--> baseDesignService
      deleteDesignsService --uses--> runsDesignActions
      deleteDesignsService --uses--> baseDesignService
      moveDesignsService --uses--> baseDesignService
      copyDesignCollectionQService --uses--> baseDesignService
      copyDesignCollectionCopyService --uses--> baseDesignService
      copyDesignCollectionCopyService --used by--> copyDesignCollectionQService
      generateImageVersionsService --uses--> baseDesignService
      deleteDesignsService --commit actions--> dmDesignAction
      saveDesignsService --saves--> dmDesign
      saveDesignsService --creates--> dmVersion
      deleteDesignsService --deletes from--> dmDesign
      moveDesignsService --moves--> dmDesign
      generateImageVersionsService --updates--> dmDesign
      designUserNotesCountService --counts  on--> dmDesign

    end

    %% GRAPHQL RESOLVERS
    subgraph GRAPHQL_RESOLVERS["GraphQL Resolvers"]
      direction TB
      style GRAPHQL_RESOLVERS fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2,corner-radius:8px

      versionInCollectionResolver["VersionInCollectionResolver version_in_collection_resolver.rb"]
      style versionInCollectionResolver fill:#E6E6FA,stroke:#8E8CB7,stroke-width:1,corner-radius:8px

      designsResolver["DesignsResolver designs_resolver.rb"]
      style designsResolver fill:#E6E6FA,stroke:#8E8CB7,stroke-width:1,corner-radius:8px

      %% Resolvers to models/services
      designsResolver --queries--> dmDesign
      designsResolver --filters by version--> dmVersion
      versionInCollectionResolver --resolves via collection--> dmVersion
      versionInCollectionResolver --resolves via collection--> dmDesignCollection
    end

    %% CONTROLLERS AND ROUTING
    subgraph CONTROLLERS["Controllers"]
      direction TB
      style CONTROLLERS fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,corner-radius:8px

      resizedImageController["ResizedImageController resized_image_controller.rb"]
      style resizedImageController fill:#D4F1F9,stroke:#738FA7,stroke-width:1,corner-radius:8px

      rawImagesController["RawImagesController raw_images_controller.rb"]
      style rawImagesController fill:#D4F1F9,stroke:#738FA7,stroke-width:1,corner-radius:8px

      mainDesignsController["DesignsController designs_controller.rb"]
      style mainDesignsController fill:#D4F1F9,stroke:#738FA7,stroke-width:1,corner-radius:8px

      %% Controller uses models
      mainDesignsController --manages--> dmDesign
      mainDesignsController --authorizes via policy--> dmDesignPolicy
      resizedImageController --renders resized design from--> dmDesignAction
      resizedImageController --accesses--> dmVersion
      rawImagesController --serves blob from--> dmRepository
      rawImagesController --accesses design--> dmDesign
      rawImagesController --authorizes via--> dmDesignPolicy
    end

    %% BACKGROUND PROCESSING
    subgraph WORKERS["Background Workers"]
      direction TB
      style WORKERS fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2,corner-radius:8px

      copyDesignCollectionWorker["CopyDesignCollectionWorker copy_design_collection_worker.rb"]
      style copyDesignCollectionWorker fill:#E6E6FA,stroke:#8E8CB7,stroke-width:1,corner-radius:8px

      newVersionWorker["NewVersionWorker new_version_worker.rb"]
      style newVersionWorker fill:#E6E6FA,stroke:#8E8CB7,stroke-width:1,corner-radius:8px

      %% Workers call services
      copyDesignCollectionWorker --enqueues jobs for--> copyDesignCollectionQService
      newVersionWorker --generates image variants--> generateImageVersionsService
      newVersionWorker --creates--> dmVersion
      copyDesignCollectionWorker --duplicates collection--> copyDesignCollectionCopyService
    end

    %% SERIALIZERS & PRESENTATION
    subgraph PRESENTATION["Presentation & Serialization"]
      direction TB
      style PRESENTATION fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,corner-radius:8px

      issueSidebarExtrasEntity["IssueSidebarExtrasEntity issue_sidebar_extras_entity.rb"]
      style issueSidebarExtrasEntity fill:#FFF8DC,stroke:#D2B886,stroke-width:1,corner-radius:8px

      systemNotesDesignService["SystemNotes::DesignManagementService design_management_service.rb"]
      style systemNotesDesignService fill:#FFF8DC,stroke:#D2B886,stroke-width:1,corner-radius:8px

      %% Serializes  about version changes
      systemNotesDesignService --records  for--> dmVersion
      issueSidebarExtrasEntity --presents sidebar extras for--> dmDesign
    end

    %% EE EXTENSIONS (ENTERPRISE EDITION)
    subgraph EE_EXTENSIONS["Enterprise Extensions"]
      direction TB
      style EE_EXTENSIONS fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2,corner-radius:8px

      eeDmRepository["EE::DesignManagement::Repository ee/design_management/repository.rb"]
      style eeDmRepository fill:#E6E6FA,stroke:#8E8CB7,stroke-width:1,corner-radius:8px

      eeSaveDesignsService["EE::DesignManagement::SaveDesignsService ee/save_designs_service.rb"]
      style eeSaveDesignsService fill:#E6E6FA,stroke:#8E8CB7,stroke-width:1,corner-radius:8px

      eeDeleteDesignsService["EE::DesignManagement::DeleteDesignsService ee/delete_designs_service.rb"]
      style eeDeleteDesignsService fill:#E6E6FA,stroke:#8E8CB7,stroke-width:1,corner-radius:8px

      %% EE modules prepend/override core
      eeDmRepository --extends and enhances--> dmRepository
      eeSaveDesignsService --overrides--> saveDesignsService
      eeDeleteDesignsService --overrides--> deleteDesignsService
    end

    %% IMPORT/EXPORT & REPOSITORY
    subgraph INFRASTRUCTURE["Infrastructure & Repo Integration"]
      direction TB
      style INFRASTRUCTURE fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,corner-radius:8px

      gitlabDesignRepoSaver["ImportExport::DesignRepoSaver design_repo_saver.rb"]
      style gitlabDesignRepoSaver fill:#FFF8DC,stroke:#D2B886,stroke-width:1,corner-radius:8px

      gitlabDesignMgmtRepo["Repositories::DesignManagementRepository design_management_repository.rb"]
      style gitlabDesignMgmtRepo fill:#FFF8DC,stroke:#D2B886,stroke-width:1,corner-radius:8px

      gitlabDesignRepoSaver --persists repo for--> dmRepository
      gitlabDesignMgmtRepo --describes repo type for--> dmRepository
      gitlabDesignMgmtRepo --used by--> dmDesign

    end
  
    %% -- INTER-SUBGRAPH/DOMAIN-LEVEL RELATIONSHIPS --

    %% Cross-group relations
    designFinder --used by--> designsResolver
    designFinder --used by--> saveDesignsService
    designFinder --used by--> deleteDesignsService

    %% Notes/service to controller
    systemNotesDesignService --called by--> mainDesignsController
    systemNotesDesignService -- about--> dmVersion

    %% Workers/Services-to-Models
    copyDesignCollectionWorker --acts on collection--> dmDesignCollection
    newVersionWorker --invokes on version--> dmVersion

    %% Services-to-Policies
    saveDesignsService --checks access via--> dmDesignPolicy
    deleteDesignsService --checks access via--> dmDesignPolicy
    baseDesignService --checks access via--> dmDesignPolicy

    %% Presentation-to-Resolvers-Controllers
    issueSidebarExtrasEntity --used in--> mainDesignsController

    %% Controllers leverage Services
    mainDesignsController --calls create/delete via--> saveDesignsService
    mainDesignsController --calls delete via--> deleteDesignsService
    mainDesignsController --move via--> moveDesignsService

    %% Data flows between components
    dmDesign --data passed via--> saveDesignsService
    dmDesignAction --transformation for--> generateImageVersionsService
    dmDesignAtVersion --used for point-in-time queries in--> designsResolver
    dmDesign --mutation event flows through--> dmVersion

  end

  %% SECTION: EXTERNAL FILES / INTERFACES (invisible in main diagram, can be referenced if necessary)
  %% Exclude files with no direct relationship taking part in domain logic, unless they are directly referenced above

  %% Not explicitly drawn: QA specs, badges, global serializers, unrelated services outside design management

  %% Legend (as pastels and rounded rectangles)
  classDef pastel_blue fill:#D4F1F9,stroke:#738FA7,stroke-width:1,corner-radius:8px
  classDef pastel_yellow fill:#FFF8DC,stroke:#D2B886,stroke-width:1,corner-radius:8px
  classDef pastel_green fill:#E0F8E0,stroke:#5B8A72,stroke-width:1,corner-radius:8px
  classDef pastel_red fill:#FFE4E1,stroke:#AA7070,stroke-width:1,corner-radius:8px
  classDef pastel_purple fill:#E6E6FA,stroke:#8E8CB7,stroke-width:1,corner-radius:8px
```