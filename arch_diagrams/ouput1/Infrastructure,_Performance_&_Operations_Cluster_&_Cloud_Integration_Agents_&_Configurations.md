```mermaid
flowchart TD
  %% Subgraph: Cluster Agents Core Domain
  subgraph coreDomain["Cluster Agents - Core" ]
    direction TB
    style coreDomain fill:#F8F8F8,stroke:#ADD8E6,stroke-width:2
    AGENT_MODEL["clusters/agent.rb<br>Agent<br>Main cluster agent record":::core_model]
    AGENT_MIGRATION["clusters/agent_migration.rb<br>AgentMigration<br>Tracks agent migration events":::core_model]
    AGENTS_HIERARCHY["clusters/clusters_hierarchy.rb<br>ClustersHierarchy<br>Manages hierarchical clusters":::core_model]
    AGENT_TOKEN_FINDER["clusters/agent_tokens_finder.rb<br>AgentTokensFinder<br>Finds and filters agent tokens":::utility_model]
    AGENTS_FINDER["clusters/agents_finder.rb<br>AgentsFinder<br>Finder pattern for agents":::utility_model]
    CI_ACCESS_FINDER["agents/authorizations/ci_access/finder.rb<br>CI Access Finder":::utility_model]
    USER_ACCESS_FINDER["agents/authorizations/user_access/finder.rb<br>User Access Finder":::utility_model]
    
    AGENT_MODEL -- manages/owned by --> AGENT_MIGRATION
    AGENT_MODEL -- has tokens --> AGENT_TOKEN_FINDER
    AGENT_MODEL -- queried by --> AGENTS_FINDER
    AGENT_MODEL -- ci-access --> CI_ACCESS_FINDER
    AGENT_MODEL -- user-access --> USER_ACCESS_FINDER
    AGENT_MODEL -- hierarchical queries --> AGENTS_HIERARCHY
  end
  classDef core_model fill:#D4F1F9,stroke:#7098B0,stroke-width:2,stroke-dasharray: 5,rx:8,ry:8
  classDef utility_model fill:#FFF8DC,stroke:#B6AA7C,stroke-width:2,stroke-dasharray: 5,rx:8,ry:8

  %% Subgraph: Cluster Agent Authorizations & Access
  subgraph authorizations["Agent Authorizations & Config Access" ]
    direction TB
    style authorizations fill:#F8F8F8,stroke:#4682B4,stroke-width:2
    CI_ACCESS_REFRESH["ci_access/refresh_service.rb<br>RefreshService<br>Manages CI access lists":::support_service]
    USER_ACCESS_REFRESH["user_access/refresh_service.rb<br>RefreshService<br>Maintains user access list":::support_service]
    CI_ACCESS_FILTER["ci_access/filter_service.rb<br>FilterService<br>Filters CI access authorization set":::support_service]
    PROXY_USER_SERVICE["authorize_proxy_user_service.rb<br>Authorize proxy user":::support_service]
    EE_PROXY_USER_SERVICE["ee/agents/authorize_proxy_user_service.rb<br>EE Proxy User Extension":::support_service]
    EE_CI_CONFIG_SCOPES["concerns/ee/clusters/agents/authorizations/ci_access/config_scopes.rb<br>EE CI ConfigScoping":::support_service]
    CI_CONFIG_SCOPES["concerns/clusters/agents/authorizations/ci_access/config_scopes.rb<br>ConfigScopes<br>Shared config scopes":::support_service]
    
    CI_ACCESS_FINDER -- uses/queries --> CI_ACCESS_REFRESH
    USER_ACCESS_FINDER -- uses/queries --> USER_ACCESS_REFRESH
    CI_ACCESS_REFRESH -- filters with --> CI_ACCESS_FILTER
    CI_ACCESS_FINDER -- scope helpers --> CI_CONFIG_SCOPES
    CI_ACCESS_FINDER -- EE scope/helper --> EE_CI_CONFIG_SCOPES
    PROXY_USER_SERVICE -- extended by --> EE_PROXY_USER_SERVICE
  end
  classDef support_service fill:#FFF8DC,stroke:#EEDD82,stroke-width:2,rx:8,ry:8

  %% Subgraph: Data Structures & Agent URL Configurations
  subgraph dataStructures["Agent URL Configuration & Secure Data" ]
    direction TB
    style dataStructures fill:#F8F8F8,stroke:#50C878,stroke-width:2
    URL_CONFIG_MODEL["Agents/UrlConfiguration<br>agent_url_configuration.rb<br>Stores agent URL config":::ds_model]
    URL_CONFIG_TYPE["graphql/types/clusters/agent_url_configuration_type.rb<br>Exposes config in API":::ds_model]
    AGENT_URL_CONFIG_POLICY["policies/clusters/agents/url_configuration_policy.rb":::ds_model]
    AGENT_URL_CONFIG_DELETE["graphql/mutations/clusters/agent_url_configurations/delete.rb":::ds_model]
    AGENT_URL_CONFIG_CREATE["graphql/mutations/clusters/agent_url_configurations/create.rb":::ds_model]
    AGENT_URL_CONFIG_CREATE_SERVICE["agents/create_url_configuration_service.rb<br>Creates agent url configs":::ds_service]
    AGENT_URL_CONFIG_DELETE_SERVICE["agents/delete_url_configuration_service.rb<br>Deletes config service":::ds_service]
    CREATE_CLUSTER_AGENT_URL_MIGRATION["db/migrate/20240725223931_create_cluster_agent_url_configurations.rb<br>Migration":::ds_model]
    CLUSTER_AGENT_TOKENS_REVOKE_SERVICE_EE["ee/clusters/agent_tokens/revoke_service.rb<br>EE extended service":::ds_service]
    CLUSTER_AGENT_TOKENS_REVOKE_SERVICE["clusters/agent_tokens/revoke_service.rb<br>Revokes agent tokens":::ds_service]

    URL_CONFIG_MODEL -- API-exposed as --> URL_CONFIG_TYPE
    URL_CONFIG_MODEL -- mutation create uses --> AGENT_URL_CONFIG_CREATE_SERVICE
    URL_CONFIG_MODEL -- mutation delete uses --> AGENT_URL_CONFIG_DELETE_SERVICE
    URL_CONFIG_MODEL -- policy access --> AGENT_URL_CONFIG_POLICY
    URL_CONFIG_TYPE -- mutation create --> AGENT_URL_CONFIG_CREATE
    URL_CONFIG_TYPE -- mutation delete --> AGENT_URL_CONFIG_DELETE
    AGENT_URL_CONFIG_CREATE -- calls --> AGENT_URL_CONFIG_CREATE_SERVICE
    AGENT_URL_CONFIG_DELETE -- calls --> AGENT_URL_CONFIG_DELETE_SERVICE
    AGENT_URL_CONFIG_CREATE_SERVICE -- persists to --> URL_CONFIG_MODEL
    AGENT_URL_CONFIG_DELETE_SERVICE -- removes from --> URL_CONFIG_MODEL
    CREATE_CLUSTER_AGENT_URL_MIGRATION -- initializes --> URL_CONFIG_MODEL

    AGENT_MODEL -- has --> URL_CONFIG_MODEL
    CLUSTER_AGENT_TOKENS_REVOKE_SERVICE -- used by --> AGENT_MODEL
    CLUSTER_AGENT_TOKENS_REVOKE_SERVICE_EE -- extends --> CLUSTER_AGENT_TOKENS_REVOKE_SERVICE
  end
  classDef ds_model fill:#E0F8E0,stroke:#6DA06F,stroke-width:2,rx:8,ry:8
  classDef ds_service fill:#FFF8DC,stroke:#EEDD82,stroke-width:2,rx:8,ry:8

  %% Subgraph: GraphQL Integration & API Layer
  subgraph graphqlAPI["GraphQL & API Integration" ]
    direction TB
    style graphqlAPI fill:#F8F8F8,stroke:#5472AE,stroke-width:2
    AGENT_DELETE_MUTATION["mutations/clusters/agents/delete.rb<br>Delete Cluster Agent":::support_service]
    AGENT_CONNECTIONS_RESOLVER["graphql/resolvers/kas/agent_connections_resolver.rb<br>Resolves agent connections":::support_service]
    CLUSTER_AGENT_POLICY["policies/clusters/agent_policy.rb<br>Authorizes access":::support_service]
    URL_CONFIG_TYPE -.-> AGENT_MODEL
    AGENT_CONNECTIONS_RESOLVER -- reads via --> AGENT_MODEL
    AGENT_DELETE_MUTATION -- removes --> AGENT_MODEL
    AGENT_URL_CONFIG_CREATE -- creates/links --> URL_CONFIG_MODEL
    AGENT_URL_CONFIG_DELETE -- deletes --> URL_CONFIG_MODEL
    CLUSTER_AGENT_POLICY -- policies for --> AGENT_MODEL
  end

  %% Subgraph: Cluster Agent Operations & Workers
  subgraph operations["Agent Operations & Workers" ]
    direction TB
    style operations fill:#F8F8F8,stroke:#DA70D6,stroke-width:2
    NOTIFY_PUSH_WORKER["agents/notify_git_push_worker.rb<br>Notify on git push":::init_service]
    DELETE_EXPIRED_EVENTS["agents/delete_expired_events_worker.rb<br>Cleanup expired events":::init_service]
    DELETE_MANAGED_RESOURCES["agents/managed_resources/delete_worker.rb<br>Delete managed resources":::init_service]
    AGENT_AUTOFLOW_EVENT_WORKER["agents/auto_flow/work_items/reopened_event_worker.rb<br>Emit autoflow events on issue":::init_service]
    AUTOFLOW_MR_EVENT_WORKER["agents/auto_flow/merge_requests/updated_event_worker.rb<br>MR autoflow event":::init_service]
    AGENT_AUTO_FLOW["agents/auto_flow.rb<br>Domain logic for autoflow events":::support_service]
    AUTOFLOW_MR_EVENT_WORKER -- uses --> AGENT_AUTO_FLOW
    AGENT_AUTOFLOW_EVENT_WORKER -- uses --> AGENT_AUTO_FLOW
    NOTIFY_PUSH_WORKER -- schedules updates for --> AGENT_MODEL
    DELETE_EXPIRED_EVENTS -- cleans related --> AGENT_MODEL
    DELETE_MANAGED_RESOURCES -- cleans up --> AGENT_MODEL
  end
  classDef init_service fill:#E6E6FA,stroke:#BFA3E6,stroke-width:2,rx:8,ry:8

  %% Subgraph: Cluster Agent Configuration - Service Layer
  subgraph agentService["Agent Service Layer" ]
    direction TB
    style agentService fill:#F8F8F8,stroke:#A9A9E6,stroke-width:2
    INTEGRATION_CREATE_SERVICE["integrations/create_service.rb<br>Extend cluster with integrations":::support_service]
    AGENT_TOKEN_REVOKE_SERVICE["clusters/agent_tokens/revoke_service.rb<br>Revoke tokens":::support_service]
    AGENT_TOKEN_CREATE_SERVICE["ee/clusters/agent_tokens/create_service.rb<br>EE create audit events":::support_service]
    AGENTS_CREATE_SERVICE["ee/clusters/agents/create_service.rb<br>EE create/audit events":::support_service]
    AGENTS_AUTHORIZE_PROXY["agents/authorize_proxy_user_service.rb<br>Authorize proxy user via agent config":::support_service]
    AGENT_MODEL -- integration/creation --> INTEGRATION_CREATE_SERVICE
    AGENT_MODEL -- has-tokens --> AGENT_TOKEN_CREATE_SERVICE
    AGENT_MODEL -- has-tokens --> AGENT_TOKEN_REVOKE_SERVICE
    AGENT_MODEL -- authorizes-proxy via --> AGENTS_AUTHORIZE_PROXY
    AGENTS_CREATE_SERVICE -- audit logging for --> AGENT_MODEL
    AGENT_TOKEN_CREATE_SERVICE -- audit logging for --> AGENT_MODEL
    AGENT_TOKEN_REVOKE_SERVICE -- audit logging for --> AGENT_MODEL
  end

  %% Subgraph: Controllers and Entry Points
  subgraph controlLayer["Controllers & Entry Points"]
    direction TB
    style controlLayer fill:#F8F8F8,stroke:#AFEEEE,stroke-width:2
    AGENT_DASHBOARD_CTRL["controllers/agents/dashboard_controller.rb<br>User dashboard/entry":::init_service]
    KAS_COOKIE_CONCERN["controllers/concerns/kas_cookie.rb<br>Manages Guest KAS session":::support_service]
    AGENT_DASHBOARD_CTRL -- session/cookie helped by --> KAS_COOKIE_CONCERN
    AGENT_DASHBOARD_CTRL -- presents/queries --> AGENT_MODEL
    AGENT_DASHBOARD_CTRL -- permissions --> CLUSTER_AGENT_POLICY
  end

  %% Subgraph: Remote Development/Workspaces EE
  subgraph remoteDev["Remote Development & Workspace Operations EE"]
    direction TB
    style remoteDev fill:#F8F8F8,stroke:#48D1CC,stroke-width:2
    WORKSPACES_AGENT_CONFIG_VERSION["workspaces_agent_config_version.rb<br>Tracks config versions for agent workspaces":::ds_model]
    WORKSPACES_FINDER["workspaces_finder.rb<br>Finder for agent workspaces":::support_service]
    WORKSPACES_AGENT_CONFIG_POLICY["workspaces_agent_config_policy.rb<br>Policy for workspace configs":::support_service]
    AGENT_CONFIG_UPDATER["lib/agent_config_operations/updater.rb<br>Updates agent config models":::ds_service]
    REMOTE_DEV_COMMON_SERVICE["services/remote_development/common_service.rb<br>Common service utilities":::support_service]
    DEFAULT_RUNTIME_CLASS_VALIDATOR["validators/remote_development/default_runtime_class_validator.rb":::support_service]
    WORKSPACES_FINDER -- fetches --> WORKSPACES_AGENT_CONFIG_VERSION
    AGENT_CONFIG_UPDATER -- updates --> WORKSPACES_AGENT_CONFIG_VERSION
    WORKSPACES_AGENT_CONFIG_POLICY -- secures --> WORKSPACES_AGENT_CONFIG_VERSION

    %% Remote Dev: Workspace Operations - Create Phase
    subgraph wsCreate["Workspace Operations: Create"]
      style wsCreate fill:#F8F8F8,stroke:#B1A8C4,stroke-width:1
      WS_CREATE_MAIN["workspace_operations/create/main.rb<br>Main orchestration for workspace create":::support_service]
      WS_CREATE_CREATOR["workspace_operations/create/workspace_creator.rb<br>Creates workspace records":::support_service]
      WS_CREATE_BOOTSTRAP["workspace_operations/create/creator_bootstrapper.rb<br>Prepare for creation":::support_service]
      WS_CREATE_DEVFILE_FETCHER["workspace_operations/create/devfile_fetcher.rb<br>Fetches and parses Devfile":::support_service]
      WS_CREATE_VOLUME_DEFINER["workspace_operations/create/volume_definer.rb<br>Defines container volumes":::support_service]
      WS_CREATE_CONSTANTS["workspace_operations/create/create_constants.rb<br>Domain constants":::support_service]
      WS_CREATE_MAIN -- uses --> WS_CREATE_CREATOR
      WS_CREATE_MAIN -- uses --> WS_CREATE_DEVFILE_FETCHER
      WS_CREATE_MAIN -- uses --> WS_CREATE_BOOTSTRAP
      WS_CREATE_CREATOR -- constants from --> WS_CREATE_CONSTANTS
      WS_CREATE_VOLUME_DEFINER -- constants from --> WS_CREATE_CONSTANTS
    end
    WS_CREATE_MAIN --- remoteDev

    %% Remote Dev: Workspace Operations - Update Phase
    WS_UPDATE_MAIN["workspace_operations/update/main.rb<br>Main orchestration for workspace update":::support_service]
    WS_UPDATE_UPDATER["workspace_operations/update/updater.rb<br>Updates existing workspace models":::support_service]
    WS_UPDATE_MAIN -- uses --> WS_UPDATE_UPDATER
    WS_UPDATE_UPDATER -- updates --> WORKSPACES_AGENT_CONFIG_VERSION

    %% Remote Dev: Workspace Operations - Reconcile Phase
    subgraph wsReconcile["Workspace Operations: Reconcile"]
      style wsReconcile fill:#F8F8F8,stroke:#B1A8C4,stroke-width:1
      WS_RECONCILE_MAIN["workspace_operations/reconcile/main.rb<br>Main orchestrator for reconcile":::support_service]
      WS_RECONCILE_IN_PARAMS_EXTRACTOR["workspace_operations/reconcile/input/params_extractor.rb<br>Extracts input params":::support_service]
      WS_RECONCILE_IN_PARAMS_VALIDATOR["workspace_operations/reconcile/input/params_validator.rb<br>Validates params":::support_service]
      WS_RECONCILE_IN_ACTUAL_STATE_CALC["workspace_operations/reconcile/input/actual_state_calculator.rb<br>Calculates actual state of workspace":::support_service]
      WS_RECONCILE_IN_FACTORY["workspace_operations/reconcile/input/factory.rb<br>Creates AgentInfo objects":::support_service]
      WS_RECONCILE_IN_AGENTINFO["workspace_operations/reconcile/input/agent_info.rb<br>AgentInfo data structure":::ds_model]
      WS_RECONCILE_IN_PARAMS_TO_INFOS["workspace_operations/reconcile/input/params_to_infos_converter.rb<br>Converts params to agent infos":::support_service]
      WS_RECONCILE_IN_AGENTINFOS_OBSERVER["workspace_operations/reconcile/input/agent_infos_observer.rb<br>Observes agent infos":::support_service]

      WS_RECONCILE_OUT_CONFIG_EXTRACTOR["workspace_operations/reconcile/output/config_values_extractor.rb<br>Extracts config values for response":::support_service]
      WS_RECONCILE_OUT_RESPONSE_BUILDER["workspace_operations/reconcile/output/response_payload_builder.rb<br>Builds response payload":::support_service]
      WS_RECONCILE_OUT_RESP_OBSERVER["workspace_operations/reconcile/output/response_payload_observer.rb<br>Observes response payloads":::support_service]
      WS_RECONCILE_OUT_DEVFILE_PARSER["workspace_operations/reconcile/output/devfile_parser.rb<br>Parses devfiles":::support_service]
      WS_RECONCILE_OUT_DESIRED_CONFIG_GEN["workspace_operations/reconcile/output/desired_config_generator.rb<br>Generates desired workspace configuration":::support_service]

      WS_RECONCILE_PERSIST_WORKSPACES_FINDER["workspace_operations/reconcile/persistence/workspaces_to_be_returned_finder.rb":::support_service]
      WS_RECONCILE_PERSIST_WORKSPACES_UPDATER["workspace_operations/reconcile/persistence/workspaces_to_be_returned_updater.rb":::support_service]
      WS_RECONCILE_PERSIST_FROM_AGENT_INFOS["workspace_operations/reconcile/persistence/workspaces_from_agent_infos_updater.rb":::support_service]
      WS_RECONCILE_PERSIST_LIFECYCLE["workspace_operations/reconcile/persistence/workspaces_lifecycle_manager.rb":::support_service]
      WS_RECONCILE_PERSIST_ORPHANED_OBSERVER["workspace_operations/reconcile/persistence/orphaned_workspaces_observer.rb":::support_service]
      WS_RECONCILE_ERROR_TYPE["workspace_operations/reconcile/error_type.rb<br>Error types":::err_type]
      WS_RECONCILE_TERMINATION_PROGRESS["workspace_operations/reconcile/termination_progress.rb<br>Termination states":::support_service]
      WS_RECONCILE_UPDATE_TYPES["workspace_operations/reconcile/update_types.rb<br>Partial/full reconcile types":::support_service]
      WS_RECONCILE_CONSTANTS["workspace_operations/reconcile/reconcile_constants.rb<br>Domain constants":::support_service]

      WS_RECONCILE_MAIN -- validates params --> WS_RECONCILE_IN_PARAMS_VALIDATOR
      WS_RECONCILE_MAIN -- extracts params --> WS_RECONCILE_IN_PARAMS_EXTRACTOR
      WS_RECONCILE_MAIN -- calculates state --> WS_RECONCILE_IN_ACTUAL_STATE_CALC
      WS_RECONCILE_MAIN -- produces agent_info --> WS_RECONCILE_IN_FACTORY
      WS_RECONCILE_MAIN -- builds inputs --> WS_RECONCILE_IN_PARAMS_TO_INFOS
      WS_RECONCILE_MAIN -- completes via --> WS_RECONCILE_PERSIST_WORKSPACES_FINDER
      WS_RECONCILE_MAIN -- builds response --> WS_RECONCILE_OUT_RESPONSE_BUILDER
      WS_RECONCILE_MAIN -- hands to --> WS_RECONCILE_PERSIST_FROM_AGENT_INFOS

      WS_RECONCILE_OUT_RESPONSE_BUILDER -- builds config from --> WS_RECONCILE_OUT_DEVFILE_PARSER
      WS_RECONCILE_OUT_DEVFILE_PARSER -- reads constants from --> WS_RECONCILE_CONSTANTS
      WS_RECONCILE_PERSIST_ORPHANED_OBSERVER -- manages --> WS_RECONCILE_PERSIST_WORKSPACES_FINDER
      WS_RECONCILE_PERSIST_LIFECYCLE -- handles --> WS_RECONCILE_PERSIST_FROM_AGENT_INFOS
      WS_RECONCILE_PERSIST_FROM_AGENT_INFOS -- updates --> WS_RECONCILE_PERSIST_WORKSPACES_UPDATER
    end
    WS_RECONCILE_MAIN --- remoteDev

    %% Remote Dev: Organization/Namespace Cluster Agent Mapping
    ORG_CLUSTER_AGENT_MAPPING_CREATE_MAIN["organization_cluster_agent_mapping_operations/create/main.rb":::support_service]
    ORG_CLUSTER_AGENT_MAPPING_CREATOR["organization_cluster_agent_mapping_operations/create/mapping_creator.rb":::support_service]
    ORG_CLUSTER_AGENT_MAPPING_DELETE_MAIN["organization_cluster_agent_mapping_operations/delete/main.rb":::support_service]
    NAMESPACE_CLUSTER_AGENT_MAPPING_CREATE["namespace_cluster_agent_mapping_operations/create/main.rb":::support_service]

    ORG_CLUSTER_AGENT_MAPPING_CREATE_MAIN -- calls --> ORG_CLUSTER_AGENT_MAPPING_CREATOR
    NAMESPACE_CLUSTER_AGENT_MAPPING_CREATE -- maps namespace to agents

    %% Remote Dev: Settings & Validation utils
    REMOTEDEV_SETTINGS_INIT["settings/settings_initializer.rb<br>Initialize settings":::support_service]
    REMOTEDEV_SETTINGS_MESSAGES["settings/messages.rb<br>Domain messages/errors":::support_service]
    REMOTEDEV_SETTINGS_POLICY_EGRESS_VALIDATOR["settings/network_policy_egress_validator.rb<br>Validate egress settings":::support_service]
    REMOTEDEV_SETTINGS_INIT -- uses --> REMOTEDEV_SETTINGS_MESSAGES
    REMOTEDEV_SETTINGS_INIT -- validates --> REMOTEDEV_SETTINGS_POLICY_EGRESS_VALIDATOR

    REMOTE_DEV_COMMON_SERVICE -- used throughout Remote Dev operations
  end

  classDef err_type fill:#FFE4E1,stroke:#FFB6B4,stroke-width:2,rx:8,ry:8

  %% Subgraph: Cloud Connector and Cloud Integrations EE
  subgraph cloudConnector["Cloud Connector & Cloud Integrations EE"]
    direction TB
    style cloudConnector fill:#F8F8F8,stroke:#ADD8E6,stroke-width:2
    CLOUD_CONNECTOR_KEY_MODEL["cloud_connector/keys.rb<br>Key material for cloud connection":::ds_model]
    CLOUD_CONNECTOR_PROBE_TYPE["graphql/types/cloud_connector/probe_result_type.rb<br>GraphQL type for probe result":::ds_model]
    GCP_MACHINE_TYPE_SERVICE["google_cloud/compute/list_machine_types_service.rb<br>List types in GCP":::ds_service]
    CLOUD_CONNECTOR_KEY_MODEL -- status to --> CLOUD_CONNECTOR_PROBE_TYPE
    GCP_MACHINE_TYPE_SERVICE -- provides compute types
  end

  %% Subgraph: Quality Assurance and Environment
  subgraph qaEnv["QA/Orchestration Utilities"]
    direction TB
    style qaEnv fill:#F8F8F8,stroke:#D4B6F8,stroke-width:2
    QA_AGENT_RESOURCE["qa/resource/clusters/agent.rb<br>QA Agent Resource":::utility_model]
    QA_AGENT_TOKEN_RESOURCE["qa/resource/clusters/agent_token.rb<br>QA Agent Token":::utility_model]
    QA_GITLAB_INSTANCES["qa/service/gitlab/instances.rb<br>GitLab Instances":::utility_model]
    QA_CONFIG_BASE["orchestrator/lib/instance/configurations/_base.rb<br>Base instance config":::utility_model]
    QA_CLEANUP_CONFIG_BASE["orchestrator/lib/deployment/configurations/cleanup/_base.rb<br>Base deployment cleanup":::utility_model]
    QA_INSTANCE_SUBCOMMANDS["orchestrator/commands/subcommands/instance.rb<br>Instance subcommands":::utility_model]
    QA_KIND_CONFIG["orchestrator/lib/deployment/configurations/kind.rb<br>Kind setup":::utility_model]
    
    QA_AGENT_TOKEN_RESOURCE -- fabricates via --> QA_AGENT_RESOURCE
  end

  %% Subgraph: Kubernetes Kubeconfig Building Utilities
  subgraph kubeconf["Kubernetes Config Utilities"]
    direction TB
    style kubeconf fill:#F8F8F8,stroke:#B5E3E0,stroke-width:2
    KUBECONFIG_TEMPLATE["kubernetes/kubeconfig/template.rb<br>Kubeconfig Template":::utility_model]
    KUBECONFIG_ENTRY_USER["kubernetes/kubeconfig/entry/user.rb<br>User info for cluster":::utility_model]
    KUBECONFIG_ENTRY_CONTEXT["kubernetes/kubeconfig/entry/context.rb<br>Kube Context":::utility_model]
    KUBECONFIG_ENTRY_CLUSTER["kubernetes/kubeconfig/entry/cluster.rb<br>Cluster connection info":::utility_model]
    KUBECONFIG_TEMPLATE -- includes --> KUBECONFIG_ENTRY_CLUSTER
    KUBECONFIG_TEMPLATE -- includes --> KUBECONFIG_ENTRY_USER
    KUBECONFIG_TEMPLATE -- includes --> KUBECONFIG_ENTRY_CONTEXT
  end

  %% Subgraph: Consul Integration & Topology
  subgraph infraService["Infra & Topology Service Integration"]
    direction TB
    style infraService fill:#F8F8F8,stroke:#C7E7F2,stroke-width:2
    CONSUL_INTERNAL["gitlab/consul/internal.rb<br>Consul HTTP utility":::utility_model]
    CONSUL_SPEC["spec/lib/gitlab/consul/internal_spec.rb<br>Consul integration test":::utility_model]
    TOPOLOGY_BASE_SERVICE["topology_service_client/base_service.rb<br>Base infra service":::utility_model]
    TOPOLOGY_CELL_SERVICE["topology_service_client/cell_service.rb<br>Cell-level interface":::utility_model]
    INFRA_GO_MIDDLEWARE["middleware/go.rb<br>Go proxy for repo fetches":::utility_model]

    CONSUL_INTERNAL -- tested by --> CONSUL_SPEC
    TOPOLOGY_CELL_SERVICE -- extends --> TOPOLOGY_BASE_SERVICE
    INFRA_GO_MIDDLEWARE -- uses project path format
  end

  %% Subgraph: Other Supporting/Initializer Files
  subgraph miscUtil["Miscellaneous & Initializer Files"]
    direction TB
    style miscUtil fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2
    SYSTEM_CHECK_GITLAB_SHELL["system_check/gitlab_shell_check.rb<br>Check for shell setup":::support_service]
    GITLAB_FP_SETTINGS_DEP_RESOLVER["fp/settings/settings_dependency_resolver.rb<br>Domain deps resolver":::utility_model]
    GITLAB_FP_SETTINGS_ENV_OVERRIDE["fp/settings/env_var_override_processor.rb<br>Env-var override processor":::utility_model]
    GITLAB_KAS_CLIENT["kas/client.rb<br>KAS api client":::support_service]
  end

  %% Inter-domain Connections & Key Collaborations
  AGENT_MODEL -. transforms .-> URL_CONFIG_MODEL
  AGENT_MODEL -. syncs .-> WORKSPACES_AGENT_CONFIG_VERSION
  AGENT_MODEL -. is referenced in .-> NAMESPACE_CLUSTER_AGENT_MAPPING_CREATE
  AGENT_MODEL -. is referenced in .-> ORG_CLUSTER_AGENT_MAPPING_CREATOR
  AGENT_MODEL -. provides identity for .-> QA_AGENT_RESOURCE
  AGENT_MODEL -. provides config for .-> KUBECONFIG_TEMPLATE
  AGENT_MODEL -. used for .-> CONSUL_INTERNAL
  AGENT_MODEL -. is base for .-> AUTOFLOW_MR_EVENT_WORKER
  AGENT_MODEL -. policies enforced by .-> CLUSTER_AGENT_POLICY
  AGENT_MODEL -. access protected by .-> AGENT_URL_CONFIG_POLICY
  AGENT_MODEL -. invoked by .-> AGENT_DASHBOARD_CTRL

  %% Legend Domain Color Map
  subgraph legend [Domain Legend]
    direction TB
    style legend fill:#F8F8F8,stroke:#CCCCCC,stroke-width:1
    coreLegend["Core domain files":::core_model]
    dsLegend["Data & Config Structure Files":::ds_model]
    supportLegend["Supporting/Utility/Service files":::support_service]
    initLegend["Initialization/Worker/Process files":::init_service]
    errLegend["Error Handling/Domain Errors":::err_type]
  end
```