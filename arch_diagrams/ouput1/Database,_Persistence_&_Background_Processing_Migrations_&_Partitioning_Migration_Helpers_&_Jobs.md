```mermaid
flowchart TD
%% STRUCTURE: Partition: Core Concepts -> Migration Helpers/Jobs -> Partitioning -> Async Constraints / Observers / Instrumentation -> Validation & Setup -> Utilities

%%================ CORE DOMAIN PRIMITIVES ================%%
subgraph CORE_DOMAIN_PRIMITIVES[Core Domain Concepts & Abstractions]
  style CORE_DOMAIN_PRIMITIVES fill:#F8F8F8,stroke:#AEC6CF,stroke-width:1px,rounded

  MIGRATION["Migration Base
lib/gitlab/database/migration.rb"]:::core
  MIGRATION_HELPERS["Migration Helpers
lib/gitlab/database/migration_helpers.rb"]:::core
  SCHEMA_HELPERS["Schema Helpers
lib/gitlab/database/schema_helpers.rb"]:::core
  BATCH_COUNTER["BatchCounter for batched operations
lib/gitlab/database/batch_counter.rb"]:::core
  TABLES_LOCKER["TablesLocker - write locking coordination
lib/gitlab/database/tables_locker.rb"]:::core
  TABLES_TRUNCATE["TablesTruncate - batched truncation
lib/gitlab/database/tables_truncate.rb"]:::core
  LOCK_WRITES_MANAGER["LockWritesManager - trigger-based write blocks
lib/gitlab/database/lock_writes_manager.rb"]:::core
  UNIDIRECTIONAL_COPY_TRIGGER["UnidirectionalCopyTrigger - column copy
lib/gitlab/database/unidirectional_copy_trigger.rb"]:::core

  MIGRATION_HELPERS --> SCHEMA_HELPERS
  MIGRATION --> MIGRATION_HELPERS
  MIGRATION_HELPERS -->|Provides| BATCH_COUNTER
  MIGRATION_HELPERS -->|Provides| TABLES_LOCKER
  MIGRATION_HELPERS -->|Provides| LOCK_WRITES_MANAGER
  MIGRATION_HELPERS -->|Provides| UNIDIRECTIONAL_COPY_TRIGGER
  MIGRATION_HELPERS -->|Provides| TABLES_TRUNCATE
end

%%================ MIGRATION HELPERS & JOBS ================%%
subgraph MIGRATION_HELPERS_JOBS[Migration Behavior & Job Helpers]
  style MIGRATION_HELPERS_JOBS fill:#F8F8F8,stroke:#A7C7E7,stroke-width:1px,rounded

  BKG_MIGRATION_HELPERS["BackgroundMigrationHelpers
lib/gitlab/database/migrations/background_migration_helpers.rb"]:::core
  BATCHED_BKG_MIGRATION_HELPERS["BatchedBackgroundMigrationHelpers
lib/gitlab/database/migrations/batched_background_migration_helpers.rb"]:::core
  SWAPPING["Swapping columns, indexes, FKs
lib/gitlab/database/migration_helpers/swapping.rb"]:::core
  CONVERT_TO_BIGINT["ConvertToBigint
lib/gitlab/database/migration_helpers/convert_to_bigint.rb"]:::core
  EXTENSION_HELPERS["ExtensionHelpers
lib/gitlab/database/migrations/extension_helpers.rb"]:::supporting
  RESTRICT_SCHEMA["RestrictGitlabSchema
lib/gitlab/database/migration_helpers/restrict_gitlab_schema.rb"]:::supporting
  WRAPAROUND_VACUUM_HELPERS["WraparoundVacuumHelpers
lib/gitlab/database/migration_helpers/wraparound_vacuum_helpers.rb"]:::supporting
  ANNOUNCE_DATABASE["AnnounceDatabase
lib/gitlab/database/migration_helpers/announce_database.rb"]:::supporting
  LOOSE_INDEX_SCAN["LooseIndexScan
app/models/concerns/loose_index_scan.rb"]:::supporting
  WRAPAROUND_AUTOVACUUM["WraparoundAutovacuum
lib/gitlab/database/migration_helpers/wraparound_autovacuum.rb"]:::supporting
  CONSTRAINTS_HELPERS["ConstraintsHelpers
lib/gitlab/database/migrations/constraints_helpers.rb"]:::supporting
  REQUIRE_DDL_MULTIPLE_LOCKS["RequireDisableDdlTransactionForMultipleLocks
lib/gitlab/database/migration_helpers/require_disable_ddl_transaction_for_multiple_locks.rb"]:::supporting
  CASCADING_NS_SETTINGS["CascadingNamespaceSettings
lib/gitlab/database/migration_helpers/cascading_namespace_settings.rb"]:::supporting
  SIDEEKIQ_HELPERS["SidekiqHelpers
lib/gitlab/database/migrations/sidekiq_helpers.rb"]:::supporting
  MIGRATION_HELPERS_V2["V2
lib/gitlab/database/migration_helpers/v2.rb"]:::supporting
  WORK_ITEMS_WIDGETS["WorkItems::Widgets
lib/gitlab/database/migration_helpers/work_items/widgets.rb"]:::supporting
  LOOSE_FK_HELPERS["LooseForeignKeyHelpers
lib/gitlab/database/migration_helpers/loose_foreign_key_helpers.rb"]:::supporting

  BKG_MIGRATION_HELPERS -- uses --> CONSTRAINTS_HELPERS
  BKG_MIGRATION_HELPERS -- schedules --> SIDEEKIQ_HELPERS

  BATCHED_BKG_MIGRATION_HELPERS -- uses --> MIGRATION_HELPERS
  BKG_MIGRATION_HELPERS -- extends --> MIGRATION_HELPERS

  WRAPAROUND_AUTOVACUUM -- uses --> MIGRATION_HELPERS

  SWAPPING -- uses --> SCHEMA_HELPERS
  SWAPPING -- uses --> MIGRATION_HELPERS_V2
  MIGRATION_HELPERS_V2 -- extends --> MIGRATION_HELPERS

  CONVERT_TO_BIGINT -- uses --> MIGRATION_HELPERS
  CASCADING_NS_SETTINGS -- uses --> MIGRATION_HELPERS

  LOOSE_FK_HELPERS -- uses --> SCHEMA_HELPERS

  RESTRICT_SCHEMA -- depends on --> MIGRATION_HELPERS
  REQUIRE_DDL_MULTIPLE_LOCKS -- depends on --> MIGRATION_HELPERS

end

%%================ PARTITIONING & DATA CHANGES ================%%
subgraph PARTITIONING["Partitioning Migration Helpers"]
  style PARTITIONING fill:#F8F8F8,stroke:#99C794,stroke-width:1px,rounded

  PARTITION_INDEX_HELPERS["IndexHelpers
lib/gitlab/database/partitioning_migration_helpers/index_helpers.rb"]:::core
  PARTITION_FK_HELPERS["ForeignKeyHelpers
lib/gitlab/database/partitioning_migration_helpers/foreign_key_helpers.rb"]:::core
  PARTITION_UNIQUENESS_HELPERS["UniquenessHelpers
lib/gitlab/database/partitioning_migration_helpers/uniqueness_helpers.rb"]:::core
  PARTITION_TABLE_MANAGEMENT["TableManagementHelpers
lib/gitlab/database/partitioning_migration_helpers/table_management_helpers.rb"]:::core
  ALTER_CELL_SEQ_RANGE["AlterCellSequencesRange
lib/gitlab/database/alter_cell_sequences_range.rb"]:::core
  DEDUPLICATE_CI_TAGS["DeduplicateCiTags
lib/gitlab/database/deduplicate_ci_tags.rb"]:::core

  PARTITION_INDEX_HELPERS -- uses --> SWAPPING
  PARTITION_INDEX_HELPERS -- uses --> SCHEMA_HELPERS
  PARTITION_INDEX_HELPERS -- uses --> MIGRATION_HELPERS

  PARTITION_FK_HELPERS -- uses --> MIGRATION_HELPERS
  PARTITION_FK_HELPERS -- uses --> PARTITION_INDEX_HELPERS

  PARTITION_UNIQUENESS_HELPERS -- uses --> MIGRATION_HELPERS
  PARTITION_UNIQUENESS_HELPERS -- uses --> SCHEMA_HELPERS
  PARTITION_TABLE_MANAGEMENT -- uses --> SCHEMA_HELPERS
  PARTITION_TABLE_MANAGEMENT -- uses --> MIGRATION_HELPERS
  PARTITION_TABLE_MANAGEMENT -- uses --> LOOSE_FK_HELPERS

  ALTER_CELL_SEQ_RANGE -- used in --> PARTITION_TABLE_MANAGEMENT
  DEDUPLICATE_CI_TAGS -- uses --> MIGRATION_HELPERS

end

%%=============== ASYNC CONSTRAINTS & OBSERVERS ==============%%
subgraph ASYNC_AND_OBSERVERS["Async Constraints, Observers & Instrumentation"]
  style ASYNC_AND_OBSERVERS fill:#F8F8F8,stroke:#b9e4c2,stroke-width:1px,rounded

  ASYNC_MIGRATION_HELPERS["AsyncConstraints::MigrationHelpers
lib/gitlab/database/async_constraints/migration_helpers.rb"]:::core
  ASYNC_VALIDATORS["AsyncConstraints::Validators
lib/gitlab/database/async_constraints/validators.rb"]:::core
  ASYNC_VALIDATOR_BASE["Validators::Base
lib/gitlab/database/async_constraints/validators/base.rb"]:::supporting
  ASYNC_VALIDATOR_CHECK["Validators::CheckConstraint
lib/gitlab/database/async_constraints/validators/check_constraint.rb"]:::supporting
  POSTGRES_ASYNC_VALIDATION["PostgresAsyncConstraintValidation
lib/gitlab/database/async_constraints/postgres_async_constraint_validation.rb"]:::supporting

  OBSERVATION["Observation Struct
lib/gitlab/database/migrations/observation.rb"]:::data
  OBSERVERS_BASE["MigrationObserver & Observers
lib/gitlab/database/migrations/observers/migration_observer.rb"]:::core
  OBSERVER_TOTAL_SIZE["Observers::TotalDatabaseSizeChange
lib/gitlab/database/migrations/observers/total_database_size_change.rb"]:::core
  OBSERVER_TX_DURATION["Observers::TransactionDuration
lib/gitlab/database/migrations/observers/transaction_duration.rb"]:::core
  OBSERVER_QUERY_DETAILS["Observers::QueryDetails
lib/gitlab/database/migrations/observers/query_details.rb"]:::core
  OBSERVER_QUERY_LOG["Observers::QueryLog
lib/gitlab/database/migrations/observers/query_log.rb"]:::core
  OBSERVER_BATCH_DETAILS["Observers::BatchDetails
lib/gitlab/database/migrations/observers/batch_details.rb"]:::core
  INSTRUMENTATION["Instrumentation
lib/gitlab/database/migrations/instrumentation.rb"]:::core

  ASYNC_MIGRATION_HELPERS -- manages --> POSTGRES_ASYNC_VALIDATION
  ASYNC_VALIDATORS -- uses --> ASYNC_VALIDATOR_BASE
  ASYNC_VALIDATORS -- uses --> ASYNC_VALIDATOR_CHECK
  ASYNC_VALIDATORS -- instantiates --> POSTGRES_ASYNC_VALIDATION
  ASYNC_VALIDATOR_CHECK -- checks via --> CONSTRAINTS_HELPERS

  INSTRUMENTATION -- creates --> OBSERVATION
  INSTRUMENTATION -- notifies --> OBSERVERS_BASE
  OBSERVERS_BASE -- implements --> OBSERVER_TOTAL_SIZE
  OBSERVERS_BASE -- implements --> OBSERVER_TX_DURATION
  OBSERVERS_BASE -- implements --> OBSERVER_QUERY_DETAILS
  OBSERVERS_BASE -- implements --> OBSERVER_QUERY_LOG
  OBSERVERS_BASE -- implements --> OBSERVER_BATCH_DETAILS
end

%%================ LOCK/TRANSACTION RELIABILITY ================%%
subgraph LOCK_TRANSACTION_SUPPORT["Lock Retry & DDL Transaction Patterns"]
  style LOCK_TRANSACTION_SUPPORT fill:#F8F8F8,stroke:#FFF8DC,stroke-width:1px,rounded

  LOCK_RETRY_MIXIN["LockRetryMixin
lib/gitlab/database/migrations/lock_retry_mixin.rb"]:::supporting
  LOCK_RETRIES_HELPERS["LockRetriesHelpers
lib/gitlab/database/migrations/lock_retries_helpers.rb"]:::supporting
  RUNNER_BACKOFF_MIGRATION_HELPERS["RunnerBackoff::MigrationHelpers
lib/gitlab/database/migrations/runner_backoff/migration_helpers.rb"]:::supporting

  LOCK_RETRY_MIXIN -- uses --> LOCK_RETRIES_HELPERS
  LOCK_RETRIES_HELPERS -- used in --> MIGRATION_HELPERS

  RUNNER_BACKOFF_MIGRATION_HELPERS -- extends --> MIGRATION_HELPERS

end

%%================ MIGRATION METADATA & VERSIONING ==========%%
subgraph METADATA_SETUP["Migration Metadata, Milestones & Versioning"]
  style METADATA_SETUP fill:#F8F8F8,stroke:#E6E6FA,stroke-width:1px,rounded

  MIGRATION_MILESTONE_MIXIN["MilestoneMixin
lib/gitlab/database/migrations/milestone_mixin.rb"]:::init
  DATABASE_DECOMPOSITION["Database Decomposition::Migrate
lib/gitlab/database/decomposition/migrate.rb"]:::init
  POSTGRES_LOAD_SCHEMA_VERSIONS_MIXIN["LoadSchemaVersionsMixin
lib/gitlab/database/postgresql_database_tasks/load_schema_versions_mixin.rb"]:::init

  MIGRATION_MILESTONE_MIXIN -- included by --> MIGRATION
  POSTGRES_LOAD_SCHEMA_VERSIONS_MIXIN -- loads to --> MIGRATION_HELPERS

  DATABASE_DECOMPOSITION -- interacts with --> MIGRATION_HELPERS
end

%%================ SUPPORTING & INFRASTRUCTURE ===========%%
subgraph SUPPORTING_INFRA["Supporting Data Structures, Scripts & Initializers"]
  style SUPPORTING_INFRA fill:#F8F8F8,stroke:#F9E79F,stroke-width:1px,rounded

  HAS_CHECK_CONSTRAINTS["HasCheckConstraints
app/models/concerns/has_check_constraints.rb"]:::supporting
  IGNORABLE_COLUMNS["IgnorableColumns
app/models/concerns/ignorable_columns.rb"]:::supporting
  FROM_SET_OPERATOR["FromSetOperator
app/models/concerns/from_set_operator.rb"]:::supporting
  CROSS_DATABASE_IGNORED_TABLES["CrossDatabaseIgnoredTables
app/models/concerns/cross_database_ignored_tables.rb"]:::supporting
  ADMIN_BKG_MIGRATIONS_HELPER["Admin::BackgroundMigrationsHelper
app/helpers/admin/background_migrations_helper.rb"]:::supporting

  MIGRATION_TIMESTAMP_CHECKER["MigrationTimestampChecker
scripts/database/migration_timestamp_checker.rb"]:::supporting
  MIGRATION_COLLISION_CHECKER["MigrationCollisionChecker
scripts/database/migration_collision_checker.rb"]:::supporting
  MIGRATION_SCHEMA_VALIDATOR["MigrationSchemaValidator
scripts/migration_schema_validator.rb"]:::supporting

  REMOVE_ACTIVE_JOB_CALLBACK["remove_active_job_execute_callback.rb
config/initializers/remove_active_job_execute_callback.rb"]:::init

  HAS_CHECK_CONSTRAINTS -- uses --> CONSTRAINTS_HELPERS
  IGNORABLE_COLUMNS -- used by --> MIGRATION_SCHEMA_VALIDATOR
  MIGRATION_SCHEMA_VALIDATOR -- checks --> SCHEMA_HELPERS
  ADMIN_BKG_MIGRATIONS_HELPER -- summarizes --> BATCHED_BKG_MIGRATION_HELPERS

end

%%============= DATA VALIDATION & SYSTEM CHECKS =============%%
subgraph VALIDATION_CHECKS["Schema Validation, Constraints & System Checks"]
  style VALIDATION_CHECKS fill:#F8F8F8,stroke:#98FB98,stroke-width:1px,rounded

  SYSTEM_CHECK_DB_CONFIG["DatabaseConfigExistsCheck
lib/system_check/app/database_config_exists_check.rb"]:::supporting
  DB_VALIDATION_SOURCES_STRUCTURE["Schema::Validation::Sources::StructureSql
gems/gitlab-schema-validation/lib/gitlab/schema/validation/sources/structure_sql.rb"]:::data
  DB_VALIDATION_SOURCES_DATABASE["Schema::Validation::Sources::Database
gems/gitlab-schema-validation/lib/gitlab/schema/validation/sources/database.rb"]:::data
  DB_VALIDATION_ADAPTERS_COLUMN["Adapters::ColumnStructureSqlAdapter
gems/gitlab-schema-validation/lib/gitlab/schema/validation/adapters/column_structure_sql_adapter.rb"]:::data
  DB_VALIDATION_ADAPTERS_FOREIGN_KEY["Adapters::ForeignKeyStructureSqlAdapter
gems/gitlab-schema-validation/lib/gitlab/schema/validation/adapters/foreign_key_structure_sql_adapter.rb"]:::data
  DB_VALIDATION_VALIDATORS_MISSING_COLUMNS["Validators::MissingTableColumns
gems/gitlab-schema-validation/lib/gitlab/schema/validation/validators/missing_table_columns.rb"]:::error

  DB_VALIDATION_SOURCES_STRUCTURE -- parses with --> DB_VALIDATION_ADAPTERS_COLUMN
  DB_VALIDATION_SOURCES_STRUCTURE -- checks with --> DB_VALIDATION_ADAPTERS_FOREIGN_KEY
  DB_VALIDATION_SOURCES_DATABASE -- matches with --> DB_VALIDATION_SOURCES_STRUCTURE
  DB_VALIDATION_VALIDATORS_MISSING_COLUMNS -- reports with --> DB_VALIDATION_SOURCES_DATABASE

  SYSTEM_CHECK_DB_CONFIG -- validates --> MIGRATION
end

%%============ MIGRATIONS: JOBS, FINALIZATIONS, QUEUES ===========%%
subgraph DB_MIGRATIONS["Database Migrations Jobs, Finalization & Partitioning"]
  style DB_MIGRATIONS fill:#F8F8F8,stroke:#AEC6CF,stroke-width:1px,rounded

  classDef migrationClass fill:#D4F1F9,stroke:#5DADE2,stroke-width:1px,stroke-dasharray: 2,rounded
  POSTMIG_SET_A["db/post_migrate/*"]:::migrationClass

  POSTMIG_1[".../finalize_terraform_module_metadata_semver_patch_bigint_conversion.rb"]:::migrationClass
  POSTMIG_2[".../add_packages_debian_file_metadata_project_id_trigger.rb"]:::migrationClass
  POSTMIG_3[".../add_unique_index_for_lfs_objects_projects_with_repo_type.rb"]:::migrationClass
  POSTMIG_4[".../requeue_delete_orphaned_groups.rb"]:::migrationClass
  POSTMIG_5[".../finalize_hk_backfill_error_tracking_error_events_project_id.rb"]:::migrationClass
  POSTMIG_6[".../remove_geo_event_log_repositories_changed_event_id_column.rb"]:::migrationClass
  POSTMIG_7[".../queue_update_closed_merged_mrs.rb"]:::migrationClass
  POSTMIG_8[".../queue_backfill_software_license_policies.rb"]:::migrationClass
  POSTMIG_9[".../queue_backfill_bulk_import_failures_namespace_id.rb"]:::migrationClass
  POSTMIG_10[".../queue_backfill_bulk_import_failures_project_id.rb"]:::migrationClass
  POSTMIG_11[".../add_bulk_import_failures_project_id_trigger.rb"]:::migrationClass
  POSTMIG_12[".../finalize_requeue_backfill_work_item_hierarchy_for_epics.rb"]:::migrationClass
  POSTMIG_13[".../add_issue_emails_namespace_id_trigger.rb"]:::migrationClass
  POSTMIG_14[".../add_issue_user_mentions_namespace_id_fk.rb"]:::migrationClass
  POSTMIG_15[".../finalize_backfill_packages_debian_group_distribution_keys_group_id.rb"]:::migrationClass
  POSTMIG_16[".../requeue_backfill_epic_issues_into_work_item_parent_links.rb"]:::migrationClass
  POSTMIG_17[".../finalize_backfill_project_id_to_dependency_list_exports.rb"]:::migrationClass
  POSTMIG_18[".../finalize_backfill_dast_scanner_profiles_builds_project_id.rb"]:::migrationClass
  POSTMIG_19[".../drop_search_indices.rb"]:::migrationClass
  POSTMIG_20[".../remove_issues_vulnerability_feedback_issue_id_fk.rb"]:::migrationClass
  POSTMIG_21[".../backfill_null_project_on_ci_stages_records.rb"]:::migrationClass
  POSTMIG_22[".../queue_set_total_number_of_vulnerabilities_for_existing_projects.rb"]:::migrationClass
  POSTMIG_23[".../drop_prometheus_alert_events.rb"]:::migrationClass
  POSTMIG_24[".../drop_issues_prometheus_alert_events.rb"]:::migrationClass
  POSTMIG_25[".../add_index_on_user_id_and_notification_email_to_notification_settings.rb"]:::migrationClass
  POSTMIG_26[".../queue_delete_orphaned_deploy_tokens.rb"]:::migrationClass
  POSTMIG_27[".../create_partitions_for_audit_event_tables.rb"]:::migrationClass

  POSTMIG_SET_A --> POSTMIG_1
  POSTMIG_SET_A --> POSTMIG_2
  POSTMIG_SET_A --> POSTMIG_3
  POSTMIG_SET_A --> POSTMIG_4
  POSTMIG_SET_A --> POSTMIG_5
  POSTMIG_SET_A --> POSTMIG_6
  POSTMIG_SET_A --> POSTMIG_7
  POSTMIG_SET_A --> POSTMIG_8
  POSTMIG_SET_A --> POSTMIG_9
  POSTMIG_SET_A --> POSTMIG_10
  POSTMIG_SET_A --> POSTMIG_11
  POSTMIG_SET_A --> POSTMIG_12
  POSTMIG_SET_A --> POSTMIG_13
  POSTMIG_SET_A --> POSTMIG_14
  POSTMIG_SET_A --> POSTMIG_15
  POSTMIG_SET_A --> POSTMIG_16
  POSTMIG_SET_A --> POSTMIG_17
  POSTMIG_SET_A --> POSTMIG_18
  POSTMIG_SET_A --> POSTMIG_19
  POSTMIG_SET_A --> POSTMIG_20
  POSTMIG_SET_A --> POSTMIG_21
  POSTMIG_SET_A --> POSTMIG_22
  POSTMIG_SET_A --> POSTMIG_23
  POSTMIG_SET_A --> POSTMIG_24
  POSTMIG_SET_A --> POSTMIG_25
  POSTMIG_SET_A --> POSTMIG_26
  POSTMIG_SET_A --> POSTMIG_27

  %% Key relationships from migrations to helpers
  POSTMIG_SET_A -- include --> MIGRATION
  POSTMIG_SET_A -- use --> MIGRATION_HELPERS
  POSTMIG_SET_A -- often schedule --> BKG_MIGRATION_HELPERS
  POSTMIG_SET_A -- coordinate partitioning --> PARTITION_TABLE_MANAGEMENT
  POSTMIG_SET_A -- use batched jobs --> BATCHED_BKG_MIGRATION_HELPERS
  POSTMIG_SET_A -- use observer logic --> INSTRUMENTATION
end

%%====================== LEGEND (for clarity only in code) ======================%%
classDef core fill:#D4F1F9,stroke:#5DADE2,stroke-width:1px,rounded
classDef supporting fill:#FFF8DC,stroke:#F7DC6F,stroke-width:1px,rounded
classDef error fill:#FFE4E1,stroke:#FFB6B6,stroke-width:1px,rounded
classDef data fill:#E0F8E0,stroke:#82E0AA,stroke-width:1px,rounded
classDef init fill:#E6E6FA,stroke:#B39DDC,stroke-width:1px,rounded

%%============== FINAL RELATIONSHIPS: DOMAIN-WIDE ==============%%
%% Core concept helpers extended everywhere
MIGRATION_HELPERS_JOBS --> PARTITIONING
MIGRATION_HELPERS_JOBS -->|use constraint helpers| ASYNC_AND_OBSERVERS
MIGRATION_HELPERS_JOBS -->|uses lock/transaction| LOCK_TRANSACTION_SUPPORT
PARTITIONING --> ASYNC_AND_OBSERVERS
PARTITIONING -->|partitioning| DB_MIGRATIONS
MIGRATION_HELPERS_JOBS -->|core helpers| DB_MIGRATIONS
ASYNC_AND_OBSERVERS -->|background/observation| DB_MIGRATIONS
CORE_DOMAIN_PRIMITIVES --> MIGRATION_HELPERS_JOBS
CORE_DOMAIN_PRIMITIVES --> PARTITIONING
CORE_DOMAIN_PRIMITIVES --> ASYNC_AND_OBSERVERS
CORE_DOMAIN_PRIMITIVES --> VALIDATION_CHECKS
DB_MIGRATIONS --> VALIDATION_CHECKS

SUPPORTING_INFRA --> CORE_DOMAIN_PRIMITIVES
SUPPORTING_INFRA --> MIGRATION_HELPERS_JOBS
SUPPORTING_INFRA --> DB_MIGRATIONS

VALIDATION_CHECKS --> METADATA_SETUP
METADATA_SETUP --> DB_MIGRATIONS

%% Key data structure connections
CONSTRAINTS_HELPERS --> OBSERVERS_BASE
CONSTRAINTS_HELPERS --> ASYNC_VALIDATOR_CHECK
SCHEMA_HELPERS --> PARTITION_FK_HELPERS
SCHEMA_HELPERS --> PARTITION_UNIQUENESS_HELPERS
SCHEMA_HELPERS --> PARTITION_INDEX_HELPERS
SCHEMA_HELPERS --> PARTITION_TABLE_MANAGEMENT
MIGRATION_HELPERS --> PARTITION_TABLE_MANAGEMENT

%% End Diagram
```