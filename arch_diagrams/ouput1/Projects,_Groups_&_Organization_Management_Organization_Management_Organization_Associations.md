```mermaid
flowchart TD
  %% Colors
  classDef core fill:#D4F1F9,stroke:#A9D7EA,stroke-width:2px,color:#222,stroke-dasharray:0,rx:10,ry:10
  classDef support fill:#FFF8DC,stroke:#FFE9A7,stroke-width:2px,color:#333,stroke-dasharray:0,rx:10,ry:10
  classDef data fill:#E0F8E0,stroke:#B5EAD7,stroke-width:2px,color:#222,stroke-dasharray:0,rx:10,ry:10
  classDef error fill:#FFE4E1,stroke:#F3B2B2,stroke-width:2px,color:#8B0000,stroke-dasharray:0,rx:10,ry:10
  classDef init fill:#E6E6FA,stroke:#C3BFE6,stroke-width:2px,color:#222,stroke-dasharray:0,rx:10,ry:10
  classDef group fill:#F8F8F8,stroke:#A9D7EA,stroke-width:2px,color:#444

  %% Domain Grouping ---------------------------------------------------------

  subgraph G1["ORGANIZATION ASSOCIATIONS" ]
    direction TB
    class G1 group

    subgraph SG1["Core Domain Logic" ]
      direction TB
      class SG1 group

      OU["organizations/organization_user.rb
      Represents membership and access level of a user in an organization
      Enforces uniqueness and manages owner logic"]
      class OU core

      OUF["finders/organizations/user_organizations_finder.rb
      Finds organizations a user belongs to, access allows filtering, search"]
      class OUF core

      OG["finders/organizations/groups_finder.rb
      Finds groups associated with an organization, supports CTE/sorting"]
      class OG core

      OAC["finders/organizations/organization_association_counter.rb
      Counts resource associations within an organization, enforces authorization"]
      class OAC core

      OUUS["services/organizations/organization_users/update_service.rb
      Handles updating organization user records and enforcing update policies"]
      class OUUS core

      MOU["graphql/mutations/organizations/organization_users/base.rb
      Base mutation for manipulating organization user records via GraphQL"]
      class MOU core

      %% Core logical flows
      OUUS -- Updates --> OU
      OUUS -- May trigger error/status --> MOU
      OUF -- Returns --> OU
      OG -- Finds for --> OU
      OAC -- Counts for --> OU
      MOU -- Mutates --> OU
      MOU -- Invokes --> OUUS

      OU -- Models [membership, ownership, access level, validation]

    end

    subgraph SG2["Associated Domain Data Structures" ]
      direction TB
      class SG2 group

      CRO["customer_relations/organization.rb
      CRM representation of organizations, exposes search/filter/validation"]
      class CRO data

      CRC["customer_relations/contact.rb
      Domain model for organization-related contacts, supports search/sorting"]
      class CRC data

      %% Data dependencies
      OUF -- Organization list may use --> CRO
      OG -- Can return --> CRO
      OAC -- May count for --> CRO
      CRO -- Can relate --> CRC

    end

    subgraph SG3["Supporting Finders & Utilities" ]
      direction TB
      class SG3 group

      CRMOF["finders/crm/organizations_finder.rb
      CRM-scoped organization filter/search, with state counts"]
      class CRMOF support

      CRMCOF["finders/crm/contacts_finder.rb
      CRM contacts finder: filters/sorts/searches contacts"]
      class CRMCOF support

      EF["finders/events_finder.rb
      Finder for events user/organization events with sorting and pagination"]
      class EF support

      %% Support relationships
      CRMOF -- Finds --> CRO
      CRMCOF -- Finds --> CRC
      OG -- May apply event/CRM scoping via --> CRMOF
      OUF -- May utilize --> CRMOF

    end

    subgraph SG4["Organization Data Initialization and Transformations" ]
      direction TB
      class SG4 group

      OUD["organization_user.rb#create_default_organization_record_for
      Sets up default user-organization records"]
      class OUD init

      OU -- Exposes --> OUD
      OUUS -- May initialize via --> OUD

    end

  end

  %% Error Handling
  OUE["services/organizations/organization_users/update_service.rb#error_updating
  Error reporting and handling for user update logic"]
  class OUE error

  OUUS -- Reports errors to --> OUE
  MOU -- Can return errors from --> OUE

  %% CRM Initialization/Services
  subgraph CRMInit["CRM-Related Creation/Initialization" ]
    direction TB
    class CRMInit group

    CROCS["services/customer_relations/organizations/create_service.rb
    Service for creating CRM organizations, with permissions and validation"]
    class CROCS init

    CRCCS["services/customer_relations/contacts/create_service.rb
    Service for creating CRM contacts, with permission checks"]
    class CRCCS init

    CROCS -- Creates --> CRO
    CRCCS -- Creates --> CRC

    OUF -- May filter on newly created --> CRO
  end

  %% Logical Relationships to Other Domains (minimal illustration for context, not detailed out)
  subgraph ExtREL["External Relationships conceptual, No arrows inward-out"]
    direction TB
    class ExtREL group

    User["User model identity, permissions, externally defined"]
    class User data
    Group["Group model collaborative unit, externally defined"]
    class Group data
    Organization["Organizations::Organization entity, externally defined"]
    class Organization data

    OU -- Belongs to --> User
    OU -- Belongs to --> Organization
    OG -- Finds for --> Group

    CROCS -- Requires --> Organization
    CRCCS -- Requires --> CRO

  end

  %% Final Relationships Outside Subgraphs
  OU -- "Models org-user relationship\nwith unique access level and owner semantics" --> Organization

  %% Spacing for readability
  style OUUS stroke-width:3px
```