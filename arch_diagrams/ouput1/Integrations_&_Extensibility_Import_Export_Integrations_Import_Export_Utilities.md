```mermaid
flowchart TD
  %%== SUBGRAPH: Domain Root ==%%
  subgraph DOMAIN_IMPORT_EXPORT_UTILITIES["Integrations & Extensibility / Import / Export Integrations / Import / Export Utilities"]
    direction TB

    %%== SUBGRAPH: Core Domain Models Pastel Blue ==%%
    subgraph CORE_MODELS["Core Domain Models"]
      style CORE_MODELS fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,stroke-dasharray:4 10
      IMPORT_EXPORT_UPLOAD["ImportExportUpload
      - Upload management
      - Tracks import/export files"]:::coremodel
    end

    %%== SUBGRAPH: Concerns & Abstractions Pastel Yellow ==%%
    subgraph CONCERNS["Concerns, Patterns & Utilities"]
      style CONCERNS fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,stroke-dasharray:4 10
      BASE_DATA_FIELDS["BaseDataFields
      - Data field abstraction"]:::util
      HAS_DATA_FIELDS["HasDataFields
      - Data field helper
      - Dynamic accessors"]:::util
      HAS_AVATAR["HasAvatar
      - Avatar helper"]:::util
      PUSH_DATA_VALIDATIONS["PushDataValidations
      - Validations for push/ml events"]:::util
      REACTIVELY_CACHED["ReactivelyCached
      - Reactive caching logic"]:::util
      RESET_SECRET_FIELDS["ResetSecretFields
      - Helper for secret management"]:::util
      PROP_BULK_HASHES["Propagation
      BulkOperationHashes
      - Prepares data/field hashes"]:::util
    end

    %%== SUBGRAPH: Controllers Pastel Blue ==%%
    subgraph CONTROLLERS["Import/Export Controllers"]
      style CONTROLLERS fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,stroke-dasharray:4 10
      MANIFEST_CTRL["ManifestController
      - Handles manifest upload
      - Validates files"]:::coremodel
      BASE_IMPORT_CTRL["BaseController
      - Import controller base
      - Filter/serialize helpers"]:::coremodel
      FOGBUGZ_CTRL["FogbugzController
      - Fogbugz import logic"]:::coremodel
      HISTORY_CTRL["HistoryController
      - Import/export history UI"]:::coremodel
      URL_CTRL["UrlController
      - Remote import URL validation"]:::coremodel
    end

    %%== SUBGRAPH: Workers / Job Scheduling Pastel Blue ==%%
    subgraph WORKERS["Import/Export Worker Jobs"]
      style WORKERS fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,stroke-dasharray:4 10
      STUCK_IMPORT_JOB["StuckImportJob concern
      - Core stuck job logic"]:::coremodel
      STUCK_IMPORT_JOBS_WORKER["StuckProjectImportJobsWorker
      - Fails stuck jobs"]:::coremodel
      IMPORT_FILE_CLN_WORKER["ImportFileCleanupWorker
      - Cleans up old import files"]:::coremodel
      REMOVE_IMPORT_FILE_WORKER["RemoveImportFileWorker
      - Removes specific file"]:::coremodel
      REFRESH_IMPORT_JID_WORKER["RefreshImportJidWorker
      - Ensures JID freshness"]:::coremodel
      LOAD_PLACEHOLDER_WORKER["LoadPlaceholderReferencesWorker
      - Loads placeholder refs"]:::coremodel
      DEL_PLACEHOLDER_USER_WORKER["DeletePlaceholderUserWorker
      - Removes placeholder user"]:::coremodel
      ASSIGN_CSV_WORKER["AssignmentFromCsvWorker
      - User CSV mapping logic"]:::coremodel
      NOTIFY_UPON_DEATH["NotifyUponDeath concern
      - Notifies if worker dies"]:::coremodel
      IMPORT_EXPORT_PROJECT_CLEANUP["ImportExportProjectCleanupWorker
      - Cleans up export state"]:::coremodel
    end

    %%== SUBGRAPH: External Importers Pastel Blue ==%%
    subgraph EXT_IMPORTERS["External Importers Concerns"]
      style EXT_IMPORTERS fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,stroke-dasharray:4 10
      GITHUB_OBJECT_IMPORTER["Github ObjectImporter
      - Imports single resources Github"]:::coremodel
      BITBUCKET_OBJECT_IMPORTER["Bitbucket ObjectImporter
      - Imports single resources Bitbucket"]:::coremodel
      BITBUCKET_SERVER_OBJECT_IMPORTER["BitbucketServer ObjectImporter
      - Single object import, server"]:::coremodel
    end

    %%== SUBGRAPH: Presentation and Policy Pastel Blue ==%%
    subgraph PRESENTERS_POLICIES["Presentation & Policy"]
      style PRESENTERS_POLICIES fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,stroke-dasharray:4 10
      PENDING_REASSIGN_ALERT["PendingReassignmentAlertPresenter
      - Presents bulk import alerts"]:::coremodel
      IMPORT_SOURCE_USER_POLICY["SourceUserPolicy
      - Source user authorization"]:::coremodel
    end

    %%== SUBGRAPH: Services - Import/Export Pastel Blue ==%%
    subgraph SERVICES["Import/Export Services"]
      style SERVICES fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,stroke-dasharray:4 10
      CSV_BASE_SERVICE["Csv::BaseService
      - CSV import base logic"]:::coremodel
      PLACEHOLDER_CREATE_SERVICE["PlaceholderMemberships::CreateService
      - Creates import placeholders"]:::coremodel
      PREPARE_SERVICE["PrepareService
      - Prepares import upload, queue"]:::coremodel
      FOGBUGZ_SERVICE["FogbugzService
      - Coordinates import from Fogbugz"]:::coremodel
      SOURCE_USERS_BASE_SERVICE["SourceUsers::BaseService
      - Base for user source mapping"]:::coremodel
      GENERATE_CSV_SERVICE["SourceUsers::GenerateCsvService
      - Generates mapping CSV"]:::coremodel
      CANCEL_REASSIGN_SERVICE["SourceUsers::CancelReassignmentService
      - Cancel reassignment logic"]:::coremodel
      KEEP_PLACEHOLDER_SERVICE["SourceUsers::KeepAsPlaceholderService
      - Keep as placeholder logic"]:::coremodel
      KEEP_ALL_PLACEHOLDERS_SERVICE["SourceUsers::KeepAllAsPlaceholderService
      - Bulk keep-all logic"]:::coremodel
      RESEND_NOTIFICATION_SERVICE["SourceUsers::ResendNotificationService
      - Sends reminders for user mapping"]:::coremodel
      BULK_REASSIGN_FROM_CSV_SERVICE["SourceUsers::BulkReassignFromCsvService
      - Bulk user reassignment from CSV"]:::coremodel
      PLACEHOLDER_REFERENCES_BASE_SERVICE["PlaceholderReferences::BaseService
      - Base for ref services"]:::coremodel
      PLACEHOLDER_REFERENCES_LOAD["PlaceholderReferences::LoadService
      - Loads placeholder refs"]:::coremodel
      PLACEHOLDER_REFERENCES_PUSH["PlaceholderReferences::PushService
      - Pushes ref mapping"]:::coremodel
      VALIDATE_REMOTE_GIT_ENDPOINT["ValidateRemoteGitEndpointService
      - Validates external git endpoint"]:::coremodel
      IMPORT_FILE_CLEANUP_SERVICE["ImportFileCleanupService
      - Delete expired import files"]:::coremodel
      IMPORT_EXPORT_CLEANUP_SERVICE["ImportExportCleanUpService
      - Deletes export files/dirs"]:::coremodel
      BULKIMPORT_GET_IMPORTABLE["BulkImports::GetImportableDataService
      - Gets data ready for import"]:::coremodel
    end

    %%== SUBGRAPH: Finders, Validators, Templates Pastel Yellow/Green ==%%
    subgraph FINDERS_UTILS["Finders, Validators, and Templates"]
      style FINDERS_UTILS fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2,stroke-dasharray:4 10
      TEMPLATE_FINDER["TemplateFinder
      - Loads import/export templates"]:::util
      LICENSE_TEMPLATE_FINDER["LicenseTemplateFinder
      - Finds license templates"]:::util
      REMOTE_FILE_VALIDATOR["GitlabProjects::RemoteFileValidator
      - Validator for remote project imports"]:::util
    end

    %%== SUBGRAPH: Data Structures & Placeholders Pastel Green/Red ==%%
    subgraph DATA_STRUCTS["Domain Data Structures & Storage"]
      style DATA_STRUCTS fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2,stroke-dasharray:4 10
      PLACEHOLDER_STORE["PlaceholderReferences::Store
      - Stores placeholder references
      in-memory/redis/set"]:::datastruct
      PLACEHOLDER_ALIAS_RESOLVER["PlaceholderReferences::AliasResolver
      - Resolves model/column aliases"]:::datastruct
      PLACEHOLDER_USER_LIMIT["PlaceholderUserLimit
      - Limit tracking/validation"]:::datastruct
      USER_MAPPING_VALIDATOR["UserMapping::ReassignmentCsvValidator
      - Validates mapping CSV"]:::datastruct
      IMPORT_EXCEPTIONS["Exceptions Import
      - Domain error classes"]:::errorhandler
    end

    %%== STYLES FOR NODES ==%%
    classDef coremodel fill:#D4F1F9,stroke:#88BFD6,stroke-width:2,stroke-dasharray:7 7,rx:10,ry:10
    classDef util fill:#FFF8DC,stroke:#FFE394,stroke-width:2,stroke-dasharray:8 8,rx:10,ry:10
    classDef datastruct fill:#E0F8E0,stroke:#A4CCA4,stroke-width:2,stroke-dasharray:6 6,rx:10,ry:10
    classDef errorhandler fill:#FFE4E1,stroke:#ECBAB5,stroke-width:2,stroke-dasharray:8 8,rx:10,ry:10

    %%== LOGICAL RELATIONSHIPS: Connections ==%%

    %% [MODEL] Core Upload connects to import/export file cleanup services and workers
    IMPORT_EXPORT_UPLOAD --> IMPORT_FILE_CLEANUP_SERVICE
    IMPORT_EXPORT_UPLOAD --> IMPORT_FILE_CLN_WORKER
    IMPORT_EXPORT_UPLOAD --> REMOVE_IMPORT_FILE_WORKER

    %% [SERVICE] Import services collaborate with jobs and models
    CSV_BASE_SERVICE --"uses"--> IMPORT_EXPORT_UPLOAD
    PREPARE_SERVICE --"queues jobs"--> WORKERS
    PREPARE_SERVICE --"uses"--> CSV_BASE_SERVICE
    FOGBUGZ_SERVICE --"validates/filters"--> REMOTE_FILE_VALIDATOR
    FOGBUGZ_SERVICE --"calls"--> BASE_IMPORT_CTRL
    SOURCE_USERS_BASE_SERVICE --"used by"--> GENERATE_CSV_SERVICE
    SOURCE_USERS_BASE_SERVICE --"used by"--> CANCEL_REASSIGN_SERVICE
    SOURCE_USERS_BASE_SERVICE --"used by"--> KEEP_PLACEHOLDER_SERVICE
    SOURCE_USERS_BASE_SERVICE --"used by"--> KEEP_ALL_PLACEHOLDERS_SERVICE
    SOURCE_USERS_BASE_SERVICE --"used by"--> RESEND_NOTIFICATION_SERVICE
    GENERATE_CSV_SERVICE --"produces"--> USER_MAPPING_VALIDATOR
    BULK_REASSIGN_FROM_CSV_SERVICE --"uses"--> USER_MAPPING_VALIDATOR
    PLACEHOLDER_CREATE_SERVICE --"uses"--> PLACEHOLDER_STORE
    PLACEHOLDER_REFERENCES_BASE_SERVICE --"base for"--> PLACEHOLDER_REFERENCES_LOAD
    PLACEHOLDER_REFERENCES_BASE_SERVICE --"base for"--> PLACEHOLDER_REFERENCES_PUSH
    PLACEHOLDER_REFERENCES_PUSH --"resolves"--> PLACEHOLDER_ALIAS_RESOLVER
    PLACEHOLDER_REFERENCES_PUSH --"stores"--> PLACEHOLDER_STORE
    PLACEHOLDER_REFERENCES_LOAD --"reads from"--> PLACEHOLDER_STORE
    IMPORT_FILE_CLEANUP_SERVICE --"removes"--> IMPORT_EXPORT_UPLOAD

    %% [MODELS/UTILS] Concerns support composition, encryption, validations
    CORE_MODELS --"include"--> BASE_DATA_FIELDS
    CORE_MODELS --"include"--> HAS_DATA_FIELDS
    CORE_MODELS --"include"--> HAS_AVATAR
    CORE_MODELS --"include"--> PUSH_DATA_VALIDATIONS
    CORE_MODELS --"include"--> RESET_SECRET_FIELDS
    CORE_MODELS --"include"--> REACTIVELY_CACHED
    BASE_DATA_FIELDS --"provides"--> PROP_BULK_HASHES

    %% [CONTROLLERS] Controllers orchestrate import/export, invoke services
    MANIFEST_CTRL --"calls"--> PREPARE_SERVICE
    MANIFEST_CTRL --"validates"--> REMOTE_FILE_VALIDATOR
    MANIFEST_CTRL --"base"--> BASE_IMPORT_CTRL
    BASE_IMPORT_CTRL --"serializes to"--> TEMPLATE_FINDER
    FOGBUGZ_CTRL --"triggers"--> FOGBUGZ_SERVICE
    URL_CTRL --"calls"--> VALIDATE_REMOTE_GIT_ENDPOINT

    %% [WORKERS] Import jobs invoke service logic
    STUCK_IMPORT_JOB --"included in"--> STUCK_IMPORT_JOBS_WORKER
    IMPORT_FILE_CLN_WORKER --"calls"--> IMPORT_FILE_CLEANUP_SERVICE
    REMOVE_IMPORT_FILE_WORKER --"calls"--> IMPORT_EXPORT_UPLOAD
    REFRESH_IMPORT_JID_WORKER --"refreshes"--> IMPORT_EXPORT_UPLOAD
    LOAD_PLACEHOLDER_WORKER --"reads"--> PLACEHOLDER_REFERENCES_LOAD
    LOAD_PLACEHOLDER_WORKER --"checks"--> PLACEHOLDER_STORE
    DEL_PLACEHOLDER_USER_WORKER --"removes"--> PLACEHOLDER_STORE
    ASSIGN_CSV_WORKER --"runs on"--> USER_MAPPING_VALIDATOR
    ASSIGN_CSV_WORKER --"calls"--> BULK_REASSIGN_FROM_CSV_SERVICE

    %% [EXTERNAL IMPORTERS] Specialized importers for VCS platforms
    GITHUB_OBJECT_IMPORTER --"used by"--> WORKERS
    BITBUCKET_OBJECT_IMPORTER --"used by"--> WORKERS
    BITBUCKET_SERVER_OBJECT_IMPORTER --"used by"--> WORKERS

    %% [POLICY & PRESENTERS] Used in user mapping/permissions flows
    PENDING_REASSIGN_ALERT --"presents alerts for"--> GENERATE_CSV_SERVICE
    IMPORT_SOURCE_USER_POLICY --"authorizes"--> SOURCE_USERS_BASE_SERVICE

    %% [FINDERS/VALIDATORS] Utility classes for discovery and validation
    TEMPLATE_FINDER --"provides to"--> MANIFEST_CTRL
    TEMPLATE_FINDER --"uses"--> LICENSE_TEMPLATE_FINDER

    %% [DATA STRUCT] Placeholders and limits
    PLACEHOLDER_ALIAS_RESOLVER --"resolves alias for"--> PLACEHOLDER_STORE
    PLACEHOLDER_USER_LIMIT --"applies to"--> PLACEHOLDER_STORE
    USER_MAPPING_VALIDATOR --"validates for"--> BULK_REASSIGN_FROM_CSV_SERVICE
    PLACEHOLDER_STORE --"used in"--> LOAD_PLACEHOLDER_WORKER
    IMPORT_EXCEPTIONS --"support errors for"--> ALL

    %% [BROAD RELATION: All entities rely on domain data structures and exceptions]
    ALL[All domain processes]:::coremodel
    ALL --"raise"--> IMPORT_EXCEPTIONS
    ALL --"utilize"--> PLACEHOLDER_STORE

    %% [SERVICES] - Import/Export Project cleanup
    IMPORT_EXPORT_CLEANUP_SERVICE --"removes"--> IMPORT_EXPORT_UPLOAD
    IMPORT_EXPORT_PROJECT_CLEANUP --"uses"--> IMPORT_EXPORT_CLEANUP_SERVICE

    %% [Bulk import data loader]
    BULKIMPORT_GET_IMPORTABLE --"flows to"--> PREPARE_SERVICE

    %%== END SUBGRAPH DOMAIN ==%%
  end
```