```mermaid
flowchart TD
  %% COLORS
  %% Core domain:       #D4F1F9   (Pastel Blue)
  %% Supporting/utils:  #FFF8DC   (Pastel Yellow)
  %% Data Structures:   #E0F8E0   (Pastel Green)
  %% Error Handling:    #FFE4E1   (Pastel Red)
  %% Initialization:    #E6E6FA   (Pastel Purple)
  %% Group bg:          #F8F8F8   (Very Light Gray, pastel border)

  %% == CORE DOMAIN CONCEPTS ==
  subgraph Types["GraphQL Type System for Work Items" ]
    direction TB
    style Types fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded=true

    Types_BaseObject["types/base_object.rb":::core] 
    Types_BaseField["types/base_field.rb":::core]
    Types_BaseInputObject["types/base_input_object.rb":::core]
    Types_BaseEnum["types/base_enum.rb":::core]
    Types_BaseInterface["types/base_interface.rb":::core]
    Types_BaseUnion["types/base_union.rb":::core]

    Types_BaseObject -->|extends| Types_BaseField
    Types_BaseObject -->|base for| Types_BaseInputObject
    Types_BaseInputObject --> Types_BaseArgument
    Types_BaseField --> Types_BaseInterface
    Types_BaseField --> Types_BaseEnum

    Types_BaseObject --> Types_BaseInterface
  end

  classDef core fill:#D4F1F9,stroke:#D4F1F9,stroke-width:2,stroke-dasharray:2 2,rx:8,ry:8

  %% == GENERAL TYPES: Work Item and Widget Core Types ==
  subgraph WorkItemsDomain["Work Items Types and Core" ]
    direction TB
    style WorkItemsDomain fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded=true

    WI_BaseObject["types/work_items/base_object.rb":::core]
    WI_WorkItemType["types/work_item_type.rb":::core]
    WI_WidgetTypeEnum["types/work_items/widget_type_enum.rb":::data]
    WI_WidgetInterface["types/work_items/widget_interface.rb":::core]
    WI_WidgetDefinitionInterface["types/work_items/widget_definition_interface.rb":::core]
    WI_TypeType["types/work_items/type_type.rb":::core]
    WI_WorkItemTypeCountsByStateType["types/work_items/work_item_type_counts_by_state_type.rb":::data]

    WI_BaseObject --> WI_WorkItemType
    WI_WorkItemType --> WI_TypeType
    WI_WorkItemType --> WI_WidgetTypeEnum
    WI_WorkItemType --> WI_WidgetInterface
    WI_TypeType --> WI_WorkItemTypeCountsByStateType

    WI_WidgetInterface --> WI_WidgetTypeEnum
    WI_WidgetDefinitionInterface --> WI_WidgetTypeEnum

    WI_BaseObject -->|extends| Types_BaseObject
    WI_WorkItemType -->|extends| WI_BaseObject
    WI_TypeType -->|extends| WI_BaseObject
    WI_WorkItemTypeCountsByStateType -->|extends| WI_BaseObject

    WI_WidgetInterface -->|include| Types_BaseInterface
    WI_WidgetDefinitionInterface -->|include| Types_BaseInterface
  end

  classDef data fill:#E0F8E0,stroke:#E0F8E0,stroke-width:2,rx:8,ry:8

  %% == WIDGET DEFINITIONS ==
  subgraph WidgetDefs["Widget Definitions feature-level abstractions"]
    direction TB
    style WidgetDefs fill:#F8F8F8,stroke:#FFD1EC,stroke-width:2,rounded=true

    WidgetDef_Generic["types/work_items/widget_definitions/generic_type.rb":::core]
    WidgetDef_Assignees["types/work_items/widget_definitions/assignees_type.rb":::core]
    WidgetDef_Hierarchy["types/work_items/widget_definitions/hierarchy_type.rb":::core]

    WidgetDef_Generic --> WI_WidgetDefinitionInterface
    WidgetDef_Assignees --> WI_WidgetDefinitionInterface
    WidgetDef_Hierarchy --> WI_WidgetDefinitionInterface
    WidgetDef_Hierarchy --> WI_TypeType
  end

  %% == WIDGET INTERFACES AND WIDGET TYPES ==
  subgraph WidgetFeatures["Widget Features Widget Types, Inputs, and Enums"]
    direction TB
    style WidgetFeatures fill:#F8F8F8,stroke:#6ED7B3,stroke-width:2,rounded=true

    %% Widget Interfaces
    WidgetIntf["types/work_items/widget_interface.rb":::core]
    WidgetIntf --> WI_WidgetTypeEnum

    %% Widget Enums
    WidgetTypeEnum["types/work_items/widget_type_enum.rb":::data]
    WidgetTypeEnum --> WI_WorkItemType

    %% Widgets - Feature-Specific Types instantiated as fields in main WorkItemType
    subgraph Widgets["Widget Types"]
      direction TB
      style Widgets fill:#F8F8F8,stroke:#94D3F7,stroke-width:2

      Widget_Assignees["widgets/assignees_type.rb":::core]
      Widget_AwardEmoji["widgets/award_emoji_type.rb":::core]
      Widget_Comments["widgets/notes_type.rb":::core]
      Widget_Designs["widgets/designs_type.rb":::core]
      Widget_Development["widgets/development_type.rb":::core]
      Widget_EmailParticipants["widgets/email_participants_type.rb":::core]
      Widget_ErrorTracking["widgets/error_tracking_type.rb":::core]
      Widget_Labels["widgets/labels_type.rb":::core]
      Widget_LinkedItems["widgets/linked_items_type.rb":::core]
      Widget_LinkedResources["widgets/linked_resources_type.rb":::core]
      Widget_Milestone["widgets/milestone_type.rb":::core]
      Widget_Notes["widgets/notes_type.rb":::core]
      Widget_Notifications["widgets/notifications_type.rb":::core]
      Widget_Participants["widgets/participants_type.rb":::core]
      Widget_StartAndDueDate["widgets/start_and_due_date_type.rb":::core]
      Widget_TimeTracking["widgets/time_tracking/time_tracking_type.rb":::core]
      Widget_CurrentUserTodos["widgets/current_user_todos_type.rb":::core]
      Widget_Hierarchy["widgets/hierarchy_type.rb":::core]
      Widget_CrmContacts["widgets/crm_contacts_type.rb":::core]
      Widget_Description["widgets/description_type.rb":::core]
      Widget_LinkedResources --> Widget_LinkedItems

      %% All widgets implement the WidgetInterface:
      Widget_Assignees --> WidgetIntf
      Widget_AwardEmoji --> WidgetIntf
      Widget_Comments --> WidgetIntf
      Widget_Designs --> WidgetIntf
      Widget_Development --> WidgetIntf
      Widget_EmailParticipants --> WidgetIntf
      Widget_ErrorTracking --> WidgetIntf
      Widget_Labels --> WidgetIntf
      Widget_LinkedItems --> WidgetIntf
      Widget_LinkedResources --> WidgetIntf
      Widget_Milestone --> WidgetIntf
      Widget_Notes --> WidgetIntf
      Widget_Notifications --> WidgetIntf
      Widget_Participants --> WidgetIntf
      Widget_StartAndDueDate --> WidgetIntf
      Widget_TimeTracking --> WidgetIntf
      Widget_CurrentUserTodos --> WidgetIntf
      Widget_Hierarchy --> WidgetIntf
      Widget_CrmContacts --> WidgetIntf
      Widget_Description --> WidgetIntf
    end

    %% Widget-Related Enums/Inputs
    subgraph WidgetEnums["Widget Enums and Input Types"]
      direction TB
      style WidgetEnums fill:#F8F8F8,stroke:#D4F1F9,stroke-width:1,rx:5,ry:5

      Enum_ErrorTrackingStatus["widgets/error_tracking_status_enum.rb":::data]
      Enum_ErrorTrackingStatus --> Widget_ErrorTracking

      Enum_AwardEmojiUpdateAction["work_items/award_emoji_update_action_enum.rb":::data]
      Input_AwardEmojiUpdate["widgets/award_emoji_update_input_type.rb":::data]
      Input_AwardEmojiUpdate --> Enum_AwardEmojiUpdateAction

      Enum_NotesFilterType["work_items/notes_filter_type_enum.rb":::data]
      Enum_NotesFilterType --> Widget_Notes

      Input_CurrentUserTodos["widgets/current_user_todos_input_type.rb":::data]
      Input_CurrentUserTodos --> Widget_CurrentUserTodos

      Enum_TodoUpdateAction["work_items/todo_update_action_enum.rb":::data]
      Input_CurrentUserTodos --> Enum_TodoUpdateAction

      Enum_StateEvent["work_items/state_event_enum.rb":::data]
      Enum_StateEvent --> WI_WorkItemType
    end
  end

  %% == WIDGET DATA STRUCTURES: Widget Inputs/Subtypes feature configuration per widget ==
  subgraph WidgetInputs["Widget Input Types per widget feature"]
    direction TB
    style WidgetInputs fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2

    Input_Assignees["widgets/assignees_input_type.rb":::data]
    Input_CrmContactsCreate["widgets/crm_contacts_create_input_type.rb":::data]
    Input_CrmContactsUpdate["widgets/crm_contacts_update_input_type.rb":::data]
    Input_Description["widgets/description_input_type.rb":::data]
    Input_Designs["widgets/designs_type.rb":::data]
    Input_LabelsCreate["widgets/labels_create_input_type.rb":::data]
    Input_LabelsUpdate["widgets/labels_update_input_type.rb":::data]
    Input_LinkedItemsCreate["widgets/linked_items_create_input_type.rb":::data]
    Input_Notes["widgets/notes_input_type.rb":::data]
    Input_NotificationsUpdate["widgets/notifications_update_input_type.rb":::data]
    Input_Milestone["widgets/milestone_input_type.rb":::data]
    Input_HierarchyCreate["widgets/hierarchy_create_input_type.rb":::data]
    Input_HierarchyUpdate["widgets/hierarchy_update_input_type.rb":::data]
    Input_RolledupDates["widgets/rolledup_dates_input_type.rb":::data]
    %%%% Time Tracking Inputs
    Input_TimeTracking["widgets/time_tracking/time_tracking_input_type.rb":::data]
    Input_Timelog["widgets/time_tracking/timelog_input_type.rb":::data]

    Input_Assignees -->|input for| Widget_Assignees
    Input_CrmContactsCreate -->|input for| Widget_CrmContacts
    Input_CrmContactsUpdate -->|input for| Widget_CrmContacts
    Input_Description -->|input for| Widget_Description
    Input_LabelsCreate -->|input for| Widget_Labels
    Input_LabelsUpdate -->|input for| Widget_Labels
    Input_LinkedItemsCreate -->|input for| Widget_LinkedItems
    Input_Notes -->|input for| Widget_Notes
    Input_NotificationsUpdate -->|input for| Widget_Notifications
    Input_Milestone -->|input for| Widget_Milestone
    Input_HierarchyCreate -->|input for| Widget_Hierarchy
    Input_HierarchyUpdate -->|input for| Widget_Hierarchy
    Input_RolledupDates -->|input for| Widget_Hierarchy
    Input_TimeTracking -->|input for| Widget_TimeTracking
    Input_Timelog -->|input for| Widget_TimeTracking
  end

  %% Time Tracking substructure
  subgraph TimeTracking["Time Tracking Nested Types"]
    direction TB
    style TimeTracking fill:#F8F8F8,stroke:#B1E5D6,stroke-width:1,rx:7,ry:7

    TT_TimeTrackingType["widgets/time_tracking/time_tracking_type.rb":::core]
    TT_HumanReadableAttributes["widgets/time_tracking/human_readable_attributes_type.rb":::data]
    TT_TimelogType["widgets/time_tracking/timelog_type.rb":::core]
    TT_TimelogInputType["widgets/time_tracking/timelog_input_type.rb":::data]

    Widget_TimeTracking --> TT_TimeTrackingType
    TT_TimeTrackingType --> TT_HumanReadableAttributes
    TT_TimelogType --> TT_TimeTrackingType
    TT_TimelogInputType --> TT_TimelogType
    Input_TimeTracking --> TT_TimeTrackingType
  end

  %% == ERRORS and HANDLING for WIDGETS ==
  subgraph ErrorWidgets["Widget-specific Error Tracking"]
    direction TB
    style ErrorWidgets fill:#F8F8F8,stroke:#FFE4E1,stroke-width:1,rx:5,ry:5

    Widget_ErrorTracking --> Enum_ErrorTrackingStatus
    Widget_ErrorTracking --> ErrorTracking_StackTrace["widgets/error_tracking/stack_trace_type.rb":::data]
    Widget_ErrorTracking --> ErrorTracking_StackTraceContext["widgets/error_tracking/stack_trace_context_type.rb":::data]
  end

  classDef error fill:#FFE4E1,stroke:#FFE4E1,stroke-width:2,rx:8,ry:8

  %% == WORK ITEMS ENUMS AND FILTERS ==
  subgraph Filters["Work Item Enums and Filters"]
    direction TB
    style Filters fill:#F8F8F8,stroke:#E0F8E0,stroke-width:1

    Enum_Sort["work_items/sort_enum.rb":::data]
    Enum_NegatedWorkItemFilter["work_items/negated_work_item_filter_input_type.rb":::data]
    Enum_UnionedWorkItemFilter["work_items/unioned_work_item_filter_input_type.rb":::data]
    Enum_NotesFilterType --> Widget_Notes
    Enum_Sort --> WI_WorkItemType
    Enum_NegatedWorkItemFilter --> WI_WorkItemType
    Enum_UnionedWorkItemFilter --> WI_WorkItemType
  end

  %% == USER PREFERENCE & CUSTOMIZATION ==
  subgraph UserPrefs["Work Item User Preferences"]
    direction TB
    style UserPrefs fill:#F8F8F8,stroke:#FFECB3,stroke-width:2

    WI_UserPreference["work_items/user_preference.rb":::support]
    WI_UserPreference -->|relates to| WI_WorkItemType
    WI_UserPreference -->|preference for| WI_TypeType
  end
  classDef support fill:#FFF8DC,stroke:#FFF8DC,stroke-width:2,rx:8,ry:8

  %% == DATA STRUCTURES AND DOMAIN FLOWS ==
  subgraph DataFlow["Data Structures and Domain Data Flows"]
    direction TB
    style DataFlow fill:#F8F8F8,stroke:#B2E7B2,stroke-width:1,rx:7,ry:7

    WI_DescriptionTemplateType["work_items/description_template_type.rb":::data]
    WI_DescriptionTemplateContentInput["work_items/description_template_content_input_type.rb":::data]
    WI_DescriptionTemplateType --> WI_DescriptionTemplateContentInput

    WI_LinkedResourceType["work_items/linked_resource_type.rb":::data]
    WI_ConvertTaskInputType["work_items/convert_task_input_type.rb":::data]
    WI_EmailParticipantType["work_items/email_participant_type.rb":::data]
    WI_ClosingMergeRequestType["work_items/closing_merge_request_type.rb":::data]
    WI_StateCountsType["work_item_state_counts_type.rb":::data]
    WI_AvailableExportFieldsEnum["work_items/available_export_fields_enum.rb":::data]
    WI_DescriptionTemplateContentInput --> WI_TypeType
    WI_ConvertTaskInputType --> WI_WorkItemType
    WI_EmailParticipantType --> Widget_EmailParticipants
    WI_ClosingMergeRequestType --> Widget_Development
    WI_StateCountsType --> WI_WorkItemType
    WI_AvailableExportFieldsEnum --> WI_WorkItemType
  end

  %% == INTERFACES for Cross-cutting Features ==
  subgraph Interfaces["Cross-cutting GraphQL Interfaces"]
    direction TB
    style Interfaces fill:#F8F8F8,stroke:#E6E6FA,stroke-width:1

    Types_TodoableInterface["types/todoable_interface.rb":::core]
    UserInterface["types/user_interface.rb":::core]
    Types_TodoableInterface --> UserInterface
    WI_WorkItemType --> Types_TodoableInterface
    Types_TodoableInterface --> WI_WidgetInterface

    Types_TodoableInterface --> Types_BaseInterface
    UserInterface -->|implements| Types_TodoableInterface
  end

  %% == WIDGET DEF/FEATURE-TO-WIDGET LINKS ==
  %% WidgetDefinitions not only link to WidgetDefinitionInterface, but also through TypeType to WidgetDefs
  WI_TypeType --> WidgetDef_Generic
  WI_TypeType --> WidgetDef_Assignees
  WI_TypeType --> WidgetDef_Hierarchy

  %% == EDGE RELATIONSHIPS: DATA TRANSFORM/INITIALIZATION ==
  Input_HierarchyCreate --> Widget_Hierarchy
  Input_HierarchyUpdate --> Widget_Hierarchy
  Input_LabelsCreate --> Widget_Labels
  Input_LabelsUpdate --> Widget_Labels
  Input_Milestone --> Widget_Milestone
  Input_Notes --> Widget_Notes
  Input_TimeTracking --> Widget_TimeTracking
  Input_Timelog --> Widget_TimeTracking
  Input_AwardEmojiUpdate --> Widget_AwardEmoji
  Input_CurrentUserTodos --> Widget_CurrentUserTodos
  Input_CrmContactsCreate --> Widget_CrmContacts
  Input_CrmContactsUpdate --> Widget_CrmContacts
  Input_LinkedItemsCreate --> Widget_LinkedItems

  %% == STYLING ASSIGNMENTS ==
  class Types_BaseObject,WI_BaseObject,Types_BaseField,Types_BaseInputObject,Types_BaseEnum,Types_BaseInterface,Types_BaseUnion,WI_WorkItemType,WI_TypeType,WI_WorkItemTypeCountsByStateType,WI_WidgetInterface,WI_WidgetDefinitionInterface,WidgetIntf,WidgetDef_Generic,WidgetDef_Assignees,WidgetDef_Hierarchy,Widget_Assignees,Widget_AwardEmoji,Widget_Comments,Widget_Designs,Widget_Development,Widget_EmailParticipants,Widget_ErrorTracking,Widget_Labels,Widget_LinkedItems,Widget_LinkedResources,Widget_Milestone,Widget_Notes,Widget_Notifications,Widget_Participants,Widget_StartAndDueDate,Widget_TimeTracking,Widget_CurrentUserTodos,Widget_Hierarchy,Widget_CrmContacts,Widget_Description,TT_TimeTrackingType,TT_TimelogType,TT_HumanReadableAttributes,TT_TimelogInputType,UserInterface,Types_TodoableInterface core
  class Input_Assignees,Input_CrmContactsCreate,Input_CrmContactsUpdate,Input_Description,Input_Designs,Input_LabelsCreate,Input_LabelsUpdate,Input_LinkedItemsCreate,Input_Notes,Input_NotificationsUpdate,Input_Milestone,Input_HierarchyCreate,Input_HierarchyUpdate,Input_RolledupDates,Input_TimeTracking,Input_Timelog data
  class Enum_ErrorTrackingStatus,Enum_AwardEmojiUpdateAction,Enum_NotesFilterType,Enum_TodoUpdateAction,Enum_StateEvent,Enum_Sort,Enum_NegatedWorkItemFilter,Enum_UnionedWorkItemFilter,WI_DescriptionTemplateType,WI_DescriptionTemplateContentInput,WI_LinkedResourceType,WI_ConvertTaskInputType,WI_EmailParticipantType,WI_ClosingMergeRequestType,WI_StateCountsType,WI_AvailableExportFieldsEnum,WidgetTypeEnum data
  class WI_UserPreference support
  class WidgetIntf,Types_TodoableInterface,Types_BaseInterface,UserInterface,WI_WidgetInterface,WI_WidgetDefinitionInterface core
  class Widget_ErrorTracking,ErrorTracking_StackTrace,ErrorTracking_StackTraceContext error
```