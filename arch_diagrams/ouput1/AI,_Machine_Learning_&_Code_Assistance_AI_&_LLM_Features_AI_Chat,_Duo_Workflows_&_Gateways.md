```mermaid
flowchart TB
  %% Vertical layout
  %% Color constants
  %% - pastel blue (#D4F1F9) core domain
  %% - pastel yellow (#FFF8DC) supporting/utility
  %% - pastel green (#E0F8E0) data structure/model files
  %% - pastel red (#FFE4E1) error handling
  %% - pastel purple (#E6E6FA) initialization/setup
  %% - group bg: very light gray (#F8F8F8), border color = themed color
  %% All nodes: shape="rounded-rectangle"

  %% =============================
  %%    CORE DOMAIN CONCEPTS
  %% =============================

  subgraph CORE_AI_LLM_CHAT["AI Chat & Duo Workflows Core":::coreGroup]
    direction TB

    subgraph AI_CONVERSATION["AI Conversation":::coreGroup]
      direction TB
      aiThrd["Thread thread.rb":::dataStruct]
      aiMsg["Message message.rb":::dataStruct]
      aiThrdPolicy["ThreadPolicy thread_policy.rb":::support]
      aiThreadFinder["ThreadFinder thread_finder.rb":::support]
      aiNotesForFinder["NotesForAiFinder notes_for_ai_finder.rb":::support]
    end

    subgraph DUO_WORKFLOWS["Duo Workflows":::coreGroup]
      direction TB
      duoWf["Workflow workflow.rb":::dataStruct]
      duoCkpt["Checkpoint checkpoint.rb":::dataStruct]
      duoCkptWrite["CheckpointWrite checkpoint_write.rb":::dataStruct]
      duoEv["Event event.rb":::dataStruct]
      duoChatEv["DuoChatEvent duo_chat_event.rb":::dataStruct]
      duoWfPolicy["WorkflowPolicy workflow_policy.rb":::support]
      duoCkptPolicy["CheckpointPolicy checkpoint_policy.rb":::support]
      duoWfWorkload["Workload workload.rb":::support]
    end

    subgraph AGENT_MANAGEMENT["AI Agent Management":::coreGroup]
      direction TB
      crAgSrv["CreateAgentService create_agent_service.rb":::support]
      delAgSrv["DestroyAgentService destroy_agent_service.rb":::support]
      agBaseMutation["Agents Base mutations/ai/agents/base.rb":::support]
    end

    subgraph DUO_SETTINGS["Duo Settings":::coreGroup]
      direction TB
      duoSetType["DuoSettingsType duo_settings_type.rb":::dataStruct]
      duoSetUpdate["UpdateService duo_settings/update_service.rb":::support]
      duoSetRes["DuoSettingsResolver duo_settings_resolver.rb":::support]
    end
  end

  %% ===============================
  %%       GRAPHQL INTERFACE
  %% ===============================

  subgraph GRAPHQL_API["GraphQL API":::graphqlGrp]
    direction TB
    gql_chatInput["ChatInputType chat_input_type.rb":::dataStruct]
    gql_chatRes["ChatMessagesResolver chat_messages_resolver.rb":::support]
    gql_threadsRes["ThreadsResolver conversations/threads_resolver.rb":::support]
    gql_titleRes["TitleResolver conversations/title_resolver.rb":::support]
    gql_delThread["DeleteConversationThread mutations/ai/delete_conversation_thread.rb":::support]
    gql_duoWfEvRes["WorkflowEventsResolver duo_workflows/workflow_events_resolver.rb":::support]
    gql_usrChatAccRes["UserChatAccessResolver user_chat_access_resolver.rb":::support]
    gql_sub_aiComp["AiCompletionResponse subscriptions/ai_completion_response.rb":::support]
    gql_xrayType["XrayReportType xray_report_type.rb":::support]
    gql_threadType["ThreadType conversations/thread_type.rb":::dataStruct]
    gql_duoSetType["DuoSettingsType duo_settings_type.rb":::dataStruct]
    gql_duoWfType["WorkflowType duo_workflows/workflow_type.rb":::dataStruct]
    gql_duoWfEvType["WorkflowEventType duo_workflows/workflow_event_type.rb":::dataStruct]
    gql_duoEnableCheckType["EnablementCheckType duo_workflows/enablement_check_type.rb":::dataStruct]
    gql_slashCmdType["SlashCommandType slash_command_type.rb":::dataStruct]
  end

  %% ===============================
  %%         PRESENTATION
  %% ===============================

  subgraph PRESENTERS["Presenters":::presentersGrp]
    direction TB
    wfPres["WorkflowPresenter workflow_presenter.rb":::support]
    wfCkptEvPres["WorkflowCheckpointEventPresenter workflow_checkpoint_event_presenter.rb":::support]
  end

  %% ===============================
  %%    SERVICES & WORKERS
  %% ===============================

  subgraph SERVICES_AND_WORKERS["Services & Workers":::coreGroup]
    direction TB
    delConvSrv["DeleteConversationThreadService delete_conversation_thread_service.rb":::support]
    duoWfOnboard["OnboardingService onboarding_service.rb":::support]
    duoWfCKSrv["CreateCheckpointService create_checkpoint_service.rb":::support]
    duoWfCrEvSrv["CreateEventService create_event_service.rb":::support]
    duoWfEvtUpd["UpdateEventService update_event_service.rb":::support]
    duoWfEnableSrv["EnablementCheckService enablement_check_service.rb":::support]
    duoWfUpdStatus["UpdateWorkflowStatusService update_workflow_status_service.rb":::support]
    duoWfCleanStuck["CleanStuckWorkflowsService clean_stuck_workflows_service.rb":::support]
    duoWfStartSrv["StartWorkflowService start_workflow_service.rb":::support]
    duoWfOATSrv["CreateOauthAccessTokenService create_oauth_access_token_service.rb":::support]
    duoWfFailStuck["FailStuckWorkflowsWorker fail_stuck_workflows_worker.rb":::support]
    acMigWorker["ActiveContext::MigrationWorker active_context/migration_worker.rb":::support]
    acBulkProc["ActiveContext::BulkProcessWorker active_context/bulk_process_worker.rb":::support]
    llmChSrv["ChatService llm/chat_service.rb":::support]
    llmCatSrv["CategorizeChatQuestionService internal/categorize_chat_question_service.rb":::support]
    amazonQCreSrv["AmazonQ::CreateService amazon_q/create_service.rb":::support]
    amazonQTrigSrv["AmazonQTriggerService amazon_q/amazon_q_trigger_service.rb":::support]
    duoSetUpdateSrv["DuoSettings::UpdateService duo_settings/update_service.rb":::support]
    acptFolSrv["AcceptFollowService activity_pub/accept_follow_service.rb":::support]
    convCleanupSrv["Ai::Conversation::CleanupService cleanup_service.rb":::support]
    convCleanupWorker["Ai::Conversation::CleanupCronWorker cleanup_cron_worker.rb":::support]
  end

  %% ===============================
  %%             API
  %% ===============================

  subgraph REST_GRPC_IMPL["API & Gateway Interfaces":::apiGrp]
    direction TB
    apiDuoWfHelpers["API::Helpers::DuoWorkflowHelpers duo_workflow_helpers.rb":::support]
    apiDuoWf["API::DuoWorkflows workflows.rb":::core]
    apiDuoWfInternal["API::DuoWorkflowsInternal workflows_internal.rb":::core]
    duoWfServiceClient["DuoWorkflowService gRPC client client.rb":::support]
    duoWfProto["duo_workflow_service_client contract_pb contract_pb.rb":::support]
    duoWfProtoS["duo_workflow_service contract_services_pb contract_services_pb":::support]
  end

  %% ===============================
  %%      DOMAIN LOGIC & UTILS
  %% ===============================
  subgraph DOMAIN_LOGIC_UTILS["Domain Logic & Utilities":::utilsGrp]
    direction TB
    duoChatRSrcHelper["DuoChatResourceHelper duo_chat_resource_helper.rb":::support]
    duoChatComp["Completions completions.rb":::support]
    duoChatDefQ["DefaultQuestions default_questions.rb":::support]
    duoChatReq["Request request.rb":::support]
    duoChatStepExec["StepExecutor step_executor.rb":::support]
    duoChatReactExec["ReactExecutor react_executor.rb":::support]
    duoChatEventP["AgentEventParser agent_event_parser.rb":::support]
    duoChatDataW["DatasetWriter dataset_writer.rb":::support]
    duoChatDataR["DatasetReader dataset_reader.rb":::support]
    duoChatParseEsc["EscapePatternHandler parsers/escape_pattern_handler.rb":::support]
    duoChatParseFA["FinalAnswerParser parsers/final_answer_parser.rb":::support]
    duoChatAgentEvtBase["AgentEvents::BaseEvent agent_events/base_event.rb":::support]
    duoChatAgentEvtErr["AgentEvents::Error agent_events/error.rb":::errorNode]
    duoChatAgentEvtUnknown["AgentEvents::Unknown agent_events/unknown.rb":::errorNode]
    duoChatAgentEvtAct["AgentEvents::Action agent_events/action.rb":::support]
    duoChatAgentEvtFinalDelta["AgentEvents::FinalAnswerDelta agent_events/final_answer_delta.rb":::support]
    llmChatStorage["ChatStorage chat_storage.rb":::support]
    llmAIMessage["AiMessage ai_message.rb":::dataStruct]
    llmAnswerUtil["Chain::Answer chain/answer.rb":::support]
    llmChatAnalyzer["ChatMessageAnalyzer chat_message_analyzer.rb":::support]
    llmChainConvUtil["Chain::Utils::ChatConversation chat_conversation.rb":::support]
    llmChainAuth["Chain::Utils::ChatAuthorizer chat_authorizer.rb":::support]
    addContext["AdditionalContext additional_context.rb":::support]
    duoWorkflowUtils["duo_workflow.rb":::core]
    duoWorkflowExec["Executor executor.rb":::support]
  end

  %% ===============================
  %%    LLM GATEWAY INTEGRATION
  %% ===============================
  subgraph LLM_GATEWAY["AI/LLM Gateway & Integration":::gatewayGrp]
    direction TB
    llmGWClient["AiGateway::Client ai_gateway/client.rb":::support]
    llmGWDocsClient["AiGateway::DocsClient ai_gateway/docs_client.rb":::support]
    llmGWCompleteBase["AiGateway::Completions::Base completions/base.rb":::support]
    llmGWCompleteSumMR["Completions::SummarizeNewMergeRequest completions/summarize_new_merge_request.rb":::support]
    llmGWRespGitCmd["ResponseModifiers::GitCommand response_modifiers/git_command.rb":::support]
    llmAnthropicTanuki["Anthropic::ResponseModifiers::TanukiBot anthropic/response_modifiers/tanuki_bot.rb":::support]
    llmAnthropicMRRev["Anthropic::Completions::ReviewMergeRequest anthropic/completions/review_merge_request.rb":::support]
    llmVertexAIClient["VertexAi::Client vertex_ai/client.rb":::support]
    llmQAIClient["QAi::Client q_ai/client.rb":::support]
    llmGWCompleteChat["Completions::Chat completions/chat.rb":::support]
  end

  %% ===============================
  %%    ADMIN & CONTROLLER LOGIC
  %% ===============================
  subgraph ADMIN_CONTROLLER["Admin & Controllers":::adminGrp]
    direction TB
    duoWfSettingsCtrl["DuoWorkflowSettingsController duo_workflow_settings_controller.rb":::support]
  end

  %% ===============================
  %%    QA / TEST / SPEC HELPERS
  %% ===============================
  subgraph QA_TEST["QA / Test Integration":::testGrp]
    direction TB
    qaDuoChatSpec["Root Cause Duo Chat Spec root_cause_analysis_with_duo_chat_spec.rb":::support]
    qaDuoChatPg["DuoChat page duo_chat.rb":::support]
    qaDuoChatCallout["DuoChatCallout duo_chat_callout.rb":::support]
  end

  %% ===============================
  %%        DOMAIN RELATIONSHIPS
  %% ===============================

  %% Conversation thread/messages structure
  aiThrd --> aiMsg
  aiThrdPolicy --> aiThrd
  aiThreadFinder --> aiThrd
  aiNotesForFinder --> aiThrd
  gql_threadType --> aiThrd
  gql_threadType --> aiMsg

  %% Duo Workflows structure
  duoWf --> duoCkpt
  duoWf --> duoCkptWrite
  duoWf --> duoEv
  duoCkpt --> duoCkptWrite
  duoEv --> duoWf

  duoWfPolicy --> duoWf
  duoCkptPolicy --> duoCkpt
  duoWfWorkload --> duoWf

  gql_duoWfType --> duoWf
  gql_duoWfEvType --> duoEv
  gql_duoEnableCheckType --> duoWf

  %% Presenters usage in GraphQL/API
  wfPres --> duoWf
  wfCkptEvPres --> duoCkpt
  gql_duoWfType --> wfPres
  gql_duoWfEvType --> wfCkptEvPres

  %% Services/workers acting on models
  delConvSrv --> aiThrd
  duoWfOnboard --> duoWf
  duoWfCKSrv --> duoCkpt
  duoWfCKSrv --> duoWf
  duoWfCrEvSrv --> duoEv
  duoWfCrEvSrv --> duoWf
  duoWfEvtUpd --> duoEv
  duoWfEnableSrv --> duoWf
  duoWfUpdStatus --> duoWf
  duoWfCleanStuck --> duoWf
  duoWfStartSrv --> duoWf
  duoWfOATSrv --> duoWf

  duoWfFailStuck --> duoWf

  %% GraphQL API interaction
  gql_chatInput --> gql_chatRes
  gql_chatRes --> aiMsg
  gql_chatRes --> aiThrd
  gql_threadsRes --> aiThrd
  gql_titleRes --> aiThrd
  gql_titleRes --> aiMsg
  gql_delThread --> delConvSrv
  gql_duoWfEvRes --> duoEv
  gql_usrChatAccRes --> llmChainAuth
  gql_sub_aiComp --> aiMsg

  %% Resolution flows and transformations
  duoSetUpdate --> duoSetType
  duoSetRes --> duoSetType

  duoSetUpdateSrv --> duoSetType
  duoSetRes --> duoSetType

  duoWf --> duoChatEv
  duoEv --> duoChatEv

  %% Authorization / access logic
  gql_usrChatAccRes --> llmChainAuth
  llmChainAuth --> aiThrd
  llmChainAuth --> aiMsg

  %% Duo workflow API structure
  apiDuoWfHelpers --> apiDuoWf
  apiDuoWfHelpers --> apiDuoWfInternal
  apiDuoWf --> duoWf
  apiDuoWfInternal --> duoWf
  apiDuoWf --> duoWfWorkload
  duoWfServiceClient --> duoWfProto
  duoWfServiceClient --> duoWfProtoS

  %% LLM gateway & completion structure
  llmGWClient --> llmGWCompleteBase
  llmGWCompleteSumMR --> llmGWCompleteBase
  llmGWRespGitCmd --> llmGWCompleteBase
  llmGWDocsClient --> llmGWClient
  llmGWCompleteBase --> llmGWClient

  llmGWCompleteBase --> llmGWRespGitCmd
  llmGWCompleteBase --> llmGWCompleteSumMR
  llmGWClient --> llmVertexAIClient
  llmGWClient --> llmQAIClient

  llmGWCompleteChat --> llmGWCompleteBase
  llmGWCompleteChat --> llmChainConvUtil

  llmAnthropicTanuki --> llmGWCompleteBase
  llmAnthropicMRRev --> llmGWCompleteBase

  %% AI Message, storage and analysis
  aiMsg --> llmAIMessage
  llmChatStorage --> llmAIMessage
  llmChatAnalyzer --> llmAIMessage
  llmChatAnalyzer --> llmChatStorage
  llmChainConvUtil --> llmAIMessage
  llmChainConvUtil --> llmChatStorage

  %% Support/chat tools
  duoChatRSrcHelper --> duoChatReq
  duoChatComp --> duoChatReq
  duoChatReq --> aiThrd
  duoChatReq --> aiMsg
  duoChatStepExec --> duoChatReq
  duoChatReactExec --> duoChatReq
  duoChatEventP --> duoChatAgentEvtBase
  duoChatAgentEvtErr --> duoChatAgentEvtBase
  duoChatAgentEvtUnknown --> duoChatAgentEvtBase
  duoChatAgentEvtAct --> duoChatAgentEvtBase
  duoChatAgentEvtFinalDelta --> duoChatAgentEvtBase

  duoChatParseFA --> duoChatAgentEvtFinalDelta
  duoChatParseEsc --> duoChatAgentEvtFinalDelta

  duoChatReq --> duoChatEventP
  duoChatDataW --> duoChatReq
  duoChatDataR --> duoChatReq

  %% QA & Admin controls surface
  qaDuoChatSpec --> aiThrd
  qaDuoChatPg --> aiThrd
  qaDuoChatCallout --> duoWfSettingsCtrl

  duoWfSettingsCtrl --> duoSetUpdate

  %% LLM ChatService and Question Categorization
  llmChSrv --> aiThrd
  llmChSrv --> aiMsg
  llmCatSrv --> aiMsg

  %% Policy checks
  duoWfPolicy --> duoWf
  duoCkptPolicy --> duoCkpt

  %% Amazon Q Integration
  amazonQCreSrv --> duoSetType
  amazonQTrigSrv --> amazonQCreSrv

  %% Add context to support code and workflow related AI context
  addContext --> aiMsg
  addContext --> aiThrd

  %% Data structure flows (Checkpoints, Events, Writes)
  duoWf --> duoCkpt
  duoCkpt --> duoCkptWrite
  duoWf --> duoEv

  %% Integrate service/utility abstractions
  duoWorkflowExec --> duoWf
  duoWorkflowUtils --> duoWorkflowExec
  duoWorkflowUtils --> duoWf

  %% GraphQL event flows
  gql_duoWfEvType --> duoEv

  %% ===============================
  %%        STYLES & CLASSES
  %% ===============================

  classDef coreGroup fill:#D4F1F9,stroke:#86C1E1,stroke-width:2px,color:#1A2B3C,rx:8,ry:8;
  classDef presentersGrp fill:#FFF8DC,stroke:#F1E1A6,stroke-width:2px,color:#594400,rx:8,ry:8;
  classDef dataStruct fill:#E0F8E0,stroke:#89BFA4,stroke-width:2px,color:#194333,rx:8,ry:8;
  classDef support fill:#FFF8DC,stroke:#C3B697,stroke-width:2px,color:#895A00,rx:8,ry:8;
  classDef errorNode fill:#FFE4E1,stroke:#EFADA9,stroke-width:2px,color:#8C4442,rx:8,ry:8;
  classDef adminGrp fill:#E6E6FA,stroke:#7A6BAF,stroke-width:2px,color:#212036,rx:8,ry:8;
  classDef testGrp fill:#FFF8DC,stroke:#C3B697,stroke-width:2px,color:#895A00,rx:8,ry:8;
  classDef core fill:#D4F1F9,stroke:#86C1E1,stroke-width:2px,color:#1A2B3C,rx:8,ry:8;
  classDef apiGrp fill:#D4F1F9,stroke:#99B3C6,stroke-width:2px,color:#274D66,rx:8,ry:8;
  classDef graphqlGrp fill:#E6E6FA,stroke:#B3A6DB,stroke-width:2px,color:#493973,rx:8,ry:8;
  classDef utilsGrp fill:#FFF8DC,stroke:#D5C59D,stroke-width:2px,color:#7B6320,rx:8,ry:8;
  classDef gatewayGrp fill:#D4F1F9,stroke:#8CB3C6,stroke-width:2px,color:#165485,rx:8,ry:8;

  %% Group bg
  class CORE_AI_LLM_CHAT,AI_CONVERSATION,DUO_WORKFLOWS,DUO_SETTINGS,AGENT_MANAGEMENT,PRESENTERS,SERVICES_AND_WORKERS,ADMIN_CONTROLLER,QA_TEST,REST_GRPC_IMPL,DOMAIN_LOGIC_UTILS,LLM_GATEWAY fill:#F8F8F8,stroke-width:2px;
```