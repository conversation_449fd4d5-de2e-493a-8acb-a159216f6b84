```mermaid
flowchart TD
%% Section: Core Domain Concepts
  subgraph core_domain["Core Domain Entities & Concepts"]
    direction TB
    style core_domain fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rx:10
    MR[["MergeRequest"]]:::core
    MRDF[["MergeRequestDiffFile"]]:::core
    MRDC[["MergeRequestDiffCommit"]]:::core
    MRCDF[["MergeRequestContextCommitDiffFile"]]:::core
    MRCC[["MergeRequestContextCommit"]]:::core
    MRD[["MergeRequestDiff"]]:::core
    MergeRequestBlock[["MergeRequestBlock"]]:::core
    MergeRequestsClosingIssues[["MergeRequestsClosingIssues"]]:::core
    Repository[["Repository"]]:::core
    Commit[["Commit"]]:::core
    CommitCollection[["CommitCollection"]]:::core
    ContextCommitsDiff[["ContextCommitsDiff"]]:::core
    Compare[["Compare"]]:::core
    CommitRange[["CommitRange"]]:::core
    Suggestion[["Suggestion"]]:::core
  end

  classDef core fill:#D4F1F9,stroke:#80c9e4,stroke-width:1,rx:6

%% Section: Diffs and Diff Viewers
  subgraph diffs_and_viewers["Diffs & Diff Viewers"]
    direction TB
    style diffs_and_viewers fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rx:10
    DVBase[["DiffViewer::Base"]]:::support
    DVText[["DiffViewer::Text\nDiffViewer::Simple\nDiffViewer::Rich\nDiffViewer::Static"]]:::support
    DVImage[["DiffViewer::Image"]]:::support
    DVClientSide[["DiffViewer::ClientSide"]]:::support
    DVServerSide[["DiffViewer::ServerSide"]]:::support
    DiffFile[["Gitlab::Diff::File"]]:::support
    DiffCollection[["Gitlab::Diff::FileCollection::MergeRequestDiff\nGitlab::Diff::FileCollection::Compare\nGitlab::Diff::FileCollection::PaginatedMergeRequestDiff\nGitlab::Diff::FileCollection::MergeRequestDiffBatch"]]:::support
    DiffLine[["Gitlab::Diff::Line"]]:::support
    DiffStatsCache[["Gitlab::Diff::StatsCache"]]:::support
    DiffHighlight[["Gitlab::Diff::Highlight"]]:::support
    DiffParser[["Gitlab::Diff::Parser"]]:::support    
    RapidDiffExpandLines[["RapidDiffs::Viewers::Text::ExpandLinesComponent"]]:::support
    RapidDiffLineNumber[["RapidDiffs::Viewers::Text::LineNumberComponent"]]:::support
    RapidDiffInlineHunk[["RapidDiffs::Viewers::Text::InlineHunkComponent"]]:::support
    RapidDiffParallelHunk[["RapidDiffs::Viewers::Text::ParallelHunkComponent"]]:::support
    RapidDiffInlineView[["RapidDiffs::Viewers::Text::InlineViewComponent"]]:::support
    RapidDiffViewerBase[["RapidDiffs::Viewers::ViewerComponent"]]:::support
  end

  classDef support fill:#FFF8DC,stroke:#E4CD80,stroke-width:1,rx:6

%% Section: Domain Data Structures
  subgraph data_structures["Domain-Specific Data Structures"]
    direction TB
    style data_structures fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2,rx:10
    DiffRefs[["Gitlab::Diff::DiffRefs"]]:::data
    DiffLineEntity[["DiffLineEntity\nDiffLineParallelEntity"]]:::data
    DiffFileEntity[["DiffFileEntity\nDiffFileBaseEntity\nDiffFileMetadataEntity"]]:::data
    PaginatedDiffEntity[["PaginatedDiffEntity"]]:::data
    MergeRequestWidgetEntity[["MergeRequestWidgetEntity\nMergeRequestWidgetCommitEntity\nMergeRequestNoteableEntity\nMergeRequestPollCachedWidgetEntity\nMergeRequestForPipelineEntity"]]:::data
    MergeRequestMetricsEntity[["MergeRequestMetricsEntity"]]:::data
    AnalyticsMergeRequestEntity[["AnalyticsMergeRequestEntity"]]:::data
    MergeRequestCreateEntity[["MergeRequestCreateEntity"]]:::data
    MergeRequestUserEntity[["MergeRequestUserEntity"]]:::data
    DiffSuggestion[["Gitlab::Diff::Suggestion"]]:::data
    DiffSuggestionParser[["Gitlab::Diff::SuggestionsParser"]]:::data
    DiffStatsSummaryType[["Types::DiffStatsSummaryType"]]:::data
    MergeRequestType[["Types::MergeRequestType"]]:::data
    MergeRequestSortEnum[["Types::MergeRequestSortEnum"]]:::data
  end

  classDef data fill:#E0F8E0,stroke:#98d44e,stroke-width:1,rx:6

%% Section: Mergeability and Merge Strategies
  subgraph mergeability["Mergeability Checks & Strategies"]
    direction TB
    style mergeability fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rx:10
    MergeabilityCheckService[["MergeRequests::MergeabilityCheckService\nMergeabilityCheckBatchService"]]:::core
    MergeabilityLogger[["MergeRequests::Mergeability::Logger"]]:::support
    MergeabilityResultsStore[["Gitlab::MergeRequests::Mergeability::ResultsStore"]]:::support
    MergeabilityRedisInterface[["Gitlab::MergeRequests::Mergeability::RedisInterface"]]:::support
    MergeabilityCheckBaseService[["MergeRequests::Mergeability::CheckBaseService\nCheckCiStatusService\nCheckCommitsStatusService\nCheckConflictStatusService\nCheckDiscussionsStatusService\nCheckDraftStatusService\nCheckMergeTimeService\nCheckMergeRequestTitleRegexService\nCheckOpenStatusService\nCheckPathLocksService\nCheckRebaseStatusService\nCheckBlockedByOtherMrsService\nCheckRequestedChangesService\nCheckExternalStatusChecksPassedService"]]:::core
    DetailedMergeStatusService[["MergeRequests::Mergeability::DetailedMergeStatusService"]]:::support
    MergeService[["MergeRequests::MergeService\nMergeBaseService\nMergeOrchestrationService\nMergeToRefService\nMergeStrategies::FromSourceBranch"]]:::core
    MergeStrategiesTrain[["MergeRequests::MergeStrategies::FromTrainRef"]]:::core
    MergeWhenChecksPassService[["AutoMerge::MergeWhenChecksPassService"]]:::support
    AfterCreateService[["MergeRequests::AfterCreateService"]]:::core
    PostMergeService[["MergeRequests::PostMergeService"]]:::core
    CleanupRefsService[["MergeRequests::CleanupRefsService"]]:::support
    UnstickLockedMRService[["MergeRequests::UnstickLockedMergeRequestsService"]]:::support
    ReloadMergeHeadDiffService[["MergeRequests::ReloadMergeHeadDiffService"]]:::core
    ReloadDiffsService[["MergeRequests::ReloadDiffsService"]]:::support
    RefreshService[["MergeRequests::RefreshService"]]:::support
    CreateRefService[["MergeRequests::CreateRefService"]]:::support
  end

%% Section: Collaboration & User Interactions
  subgraph collaboration["Collaboration & User Interactions"]
    direction TB
    style collaboration fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rx:10
    Review[["Review"]]:::core
    MergeRequestAssignee[["MergeRequestAssignee"]]:::core
    MergeRequestReviewer[["MergeRequestReviewer"]]:::core
    UsersMRInteraction[["Users::MergeRequestInteraction"]]:::core
    RequestReviewService[["MergeRequests::RequestReviewService"]]:::core
    UpdateReviewersService[["MergeRequests::UpdateReviewersService"]]:::support
    UpdateAssigneesService[["MergeRequests::UpdateAssigneesService"]]:::support
    AssignIssuesService[["MergeRequests::AssignIssuesService"]]:::support
    HandleAssigneesChangeService[["MergeRequests::HandleAssigneesChangeService"]]:::support
    ResolvedDiscussionNotificationService[["MergeRequests::ResolvedDiscussionNotificationService"]]:::support
    AddTodoWhenBuildFailsService[["MergeRequests::AddTodoWhenBuildFailsService"]]:::support
    OutdatedDiscussionDiffLinesService[["MergeRequests::OutdatedDiscussionDiffLinesService"]]:::support
  end

%% Section: Diff Discussions & Positions
  subgraph discussions_positions["Diff Discussions & Position Tracking"]
    direction TB
    style discussions_positions fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rx:10
    DiffDiscussion[["DiffDiscussion\nLegacyDiffDiscussion"]]:::core
    LegacyDiffNote[["LegacyDiffNote"]]:::support
    DiscussionsCaptureDiffNotePositionsService[["Discussions::CaptureDiffNotePositionsService"]]:::support
    DiscussionsCaptureDiffNotePositionService[["Discussions::CaptureDiffNotePositionService"]]:::support
    NoteOnDiff[["NoteOnDiff concern"]]:::support
    DiffPositionableNote[["DiffPositionableNote concern"]]:::support
    ActsAsPaginatedDiff[["ActsAsPaginatedDiff concern"]]:::support
    DiffPosition[["Gitlab::Diff::Position"]]:::support
    PositionTracer[["Gitlab::Diff::PositionTracer"]]:::support
    PositionCollection[["Gitlab::Diff::PositionCollection"]]:::support
    LinesUnfolder[["Gitlab::Diff::LinesUnfolder"]]:::support
    PairSelector[["Gitlab::Diff::PairSelector"]]:::support
  end

%% Section: Utility, Finders & Helpers
  subgraph util_finder["Supporting Utilities, Finders & Helpers"]
    direction TB
    style util_finder fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,rx:10
    MRTargetProjectFinder[["MergeRequestTargetProjectFinder"]]:::support
    MergeRequestsFinder[["MergeRequestsFinder\nMergeRequestsFinder::Params\nMergeRequests::OldestPerCommitFinder\nMergeRequests::ByApprovalsFinder\nMergeRequests::AuthorFilter"]]:::support
    ContextCommitsFinder[["ContextCommitsFinder"]]:::support
    CommitsHelper[["CommitsHelper"]]:::support
    DiffHelper[["DiffHelper"]]:::support
    CompareHelper[["CompareHelper"]]:::support
    BlameHelper[["BlameHelper"]]:::support
    RendersCommits[["RendersCommits"]]:::support
    CreatesCommitConcern[["CreatesCommit concern"]]:::support
    DiffForPathConcern[["DiffForPath concern"]]:::support
    RapidDiffsResource[["RapidDiffs::Resource"]]:::support
  end

%% Section: Initialization/Setup
  subgraph initialization["Initialization & Setup"]
    direction TB
    style initialization fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2,rx:10
    MRDiffPreloader[["Preloaders::MergeRequestDiffPreloader"]]:::init
  end

  classDef init fill:#E6E6FA,stroke:#b7aadf,stroke-width:1,rx:6

%% Section: Error Handling
  subgraph error_handling["Error Handling & Validations"]
    direction TB
    style error_handling fill:#F8F8F8,stroke:#FFE4E1,stroke-width:2,rx:10
    CommitsCreateError[["Commits::CreateService::ValidationError,\nCommits::ChangeService::ChangeError"]]:::error
    MergeBaseServiceError[["MergeRequests::MergeBaseService::MergeError"]]:::error
    ServiceError[["ServiceResponse::Error"]]:::error
  end

  classDef error fill:#FFE4E1,stroke:#eeaaa9,stroke-width:1,rx:6

%% Section: Interactions and Logical Relationships
  
  %% Core Entity Relationships
  MR --> MRDF
  MR --> MRDC
  MR --> MRCDF
  MR --> MRCC
  MR --> MRD
  MR -.-> MergeRequestBlock
  MR -.-> MergeRequestsClosingIssues
  MR -->|references| Repository
  MR -->|has many| CommitCollection
  MR -->|related to| Review
  MR --> MergeRequestAssignee
  MR --> MergeRequestReviewer
  MR -->|has many| DiffDiscussion
  MR -->|uses| MergeRequestType

  CommitCollection --> Commit
  CommitCollection --> CommitRange
  ContextCommitsDiff --> MRCC
  Compare --> Commit
  Compare --> DiffRefs
  Compare --> MRDF

  MRDF --> DiffFile
  MRDF --> DVBase
  
  MRDC --commits--> Commit

  Suggestion --> DiffSuggestion
  DiffSuggestion --> DiffSuggestionParser

  %% Diffs and Viewers Relationships
  MRDF -->|has diff lines| DiffLine
  MRDF -->|uses viewers| DVBase
  DVBase --> DVText
  DVBase --> DVImage
  DVBase --> DVClientSide
  DVBase --> DVServerSide
  
  DiffFile --> DiffLine
  DiffFile --> DiffStatsCache
  DiffFile --> DiffParser
  DiffFile --> DiffHighlight
  DiffCollection --> MRDF
  DiffCollection --> DiffStatsCache
  DiffCollection --> DiffStatsSummaryType

  RapidDiffExpandLines --> RapidDiffViewerBase
  RapidDiffLineNumber --> RapidDiffViewerBase
  RapidDiffInlineHunk --> RapidDiffViewerBase
  RapidDiffParallelHunk --> RapidDiffViewerBase
  RapidDiffInlineView --> RapidDiffViewerBase
  
  %% Data Structure Relationships
  MRDF --> DiffFileEntity
  MRDF --> PaginatedDiffEntity
  MRDF --> DiffLineEntity
  MRDF --> DiffFileMetadataEntity
  DiffLine --> DiffLineEntity
  DiffStatsCache --> DiffStatsSummaryType

  MergeRequestType --> MergeRequestSortEnum
  MergeRequestType --> MergeRequestWidgetEntity
  MergeRequestType --> MergeRequestUserEntity
  MergeRequestType --> AnalyticsMergeRequestEntity
  MergeRequestWidgetEntity --> MergeRequestMetricsEntity
  MergeRequestWidgetEntity --> PaginatedDiffEntity

  %% Mergeability Relationships
  MR --> MergeabilityCheckService
  MergeabilityCheckService --> MergeabilityLogger
  MergeabilityCheckService --> MergeabilityResultsStore
  MergeabilityResultsStore --> MergeabilityRedisInterface
  MergeabilityCheckService --> MergeabilityCheckBaseService
  MergeabilityCheckBaseService --> MR
  MergeService --> MR
  MergeWhenChecksPassService --> MergeService
  MergeStrategiesTrain --> MergeService
  AfterCreateService --> MR
  PostMergeService --> MR

  AfterCreateService --> ReloadMergeHeadDiffService
  ReloadMergeHeadDiffService --> MRDF
  MergeService --> CleanupRefsService
  MergeService --> UnstickLockedMRService
  RefreshService --> MR
  ReloadDiffsService --> MRDF

  CreateRefService --> MergeStrategiesTrain
  CreateRefService -.-> MergeService

  %% Collaboration & User
  MR --> UsersMRInteraction
  MR --> RequestReviewService
  RequestReviewService --> MR
  UpdateReviewersService --> MR
  UpdateAssigneesService --> MR
  HandleAssigneesChangeService --> MR
  AssignIssuesService --> MR
  ResolvedDiscussionNotificationService --> MR
  AddTodoWhenBuildFailsService --> MR

  %% Discussions/Positions
  DiffDiscussion --> MRDF
  LegacyDiffDiscussion --> MRDF
  LegacyDiffNote --> DiffDiscussion
  NoteOnDiff --> DiffDiscussion
  DiffPositionableNote --> LegacyDiffNote
  ActsAsPaginatedDiff --> ContextCommitsDiff
  DiscussionsCaptureDiffNotePositionsService --> MRDF
  DiscussionsCaptureDiffNotePositionService --> MRDF
  DiffPosition --> PositionTracer
  PositionTracer --> PositionCollection
  PositionTracer --> LinesUnfolder
  PositionTracer --> PairSelector

  %% Utility & Finders
  MRTargetProjectFinder --> MR
  MergeRequestsFinder --> MR
  ContextCommitsFinder --> ContextCommitsDiff
  CommitsHelper --> Commit
  DiffHelper --> DiffFile
  CompareHelper --> Compare
  BlameHelper --> Commit
  RendersCommits --> Commit
  CreatesCommitConcern -->|authorization, new MR path| MergeService
  DiffForPathConcern --> MRDF
  RapidDiffsResource --> MRDF

  %% Initialization/Setup
  MRDiffPreloader --> MRDF

  %% Error handling
  CommitsCreateError --> CommitsHelper
  MergeBaseServiceError --> MergeService
  ServiceError --> MergeService
  
  %% Key Abstractions and Patterns
  class MR,MRDF,MRDC,MRCDF,MRCC,MRD,MergeRequestBlock,MergeRequestsClosingIssues,Repository,Commit,CommitCollection,ContextCommitsDiff,Compare,CommitRange,Suggestion core
  class DVBase,DVText,DVImage,DVClientSide,DVServerSide,DiffFile,DiffCollection,DiffLine,DiffStatsCache,DiffHighlight,DiffParser,RapidDiffViewerBase,RapidDiffExpandLines,RapidDiffLineNumber,RapidDiffInlineHunk,RapidDiffParallelHunk,RapidDiffInlineView support
  class DiffRefs,DiffFileEntity,DiffLineEntity,PaginatedDiffEntity,MergeRequestWidgetEntity,MergeRequestMetricsEntity,AnalyticsMergeRequestEntity,MergeRequestCreateEntity,MergeRequestUserEntity,DiffSuggestion,DiffSuggestionParser,DiffStatsSummaryType,MergeRequestType,MergeRequestSortEnum data
  class MergeabilityCheckService,MergeabilityLogger,MergeabilityResultsStore,MergeabilityRedisInterface,MergeabilityCheckBaseService,DetailedMergeStatusService,MergeService,MergeStrategiesTrain,MergeWhenChecksPassService,AfterCreateService,PostMergeService,CleanupRefsService,UnstickLockedMRService,ReloadMergeHeadDiffService,ReloadDiffsService,RefreshService,CreateRefService core
  class Review,MergeRequestAssignee,MergeRequestReviewer,UsersMRInteraction,RequestReviewService,UpdateReviewersService,UpdateAssigneesService,AssignIssuesService,HandleAssigneesChangeService,ResolvedDiscussionNotificationService,AddTodoWhenBuildFailsService,OutdatedDiscussionDiffLinesService core
  class DiffDiscussion,LegacyDiffDiscussion,LegacyDiffNote,DiscussionsCaptureDiffNotePositionsService,DiscussionsCaptureDiffNotePositionService,NoteOnDiff,DiffPositionableNote,ActsAsPaginatedDiff,DiffPosition,PositionTracer,PositionCollection,LinesUnfolder,PairSelector support
  class MRTargetProjectFinder,MergeRequestsFinder,ContextCommitsFinder,CommitsHelper,DiffHelper,CompareHelper,BlameHelper,RendersCommits,CreatesCommitConcern,DiffForPathConcern,RapidDiffsResource support
  class MRDiffPreloader init
  class CommitsCreateError,MergeBaseServiceError,ServiceError error
```