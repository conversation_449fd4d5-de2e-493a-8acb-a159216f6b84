```mermaid
flowchart TD
  %% COLORS %%%
  classDef coreDomain fill:#D4F1F9,stroke:#97C3E6,stroke-width:2px,stroke-dasharray:0,rx:15,ry:15
  classDef utility fill:#FFF8DC,stroke:#F2DCA5,stroke-width:2px,stroke-dasharray:0,rx:15,ry:15
  classDef dataModel fill:#E0F8E0,stroke:#BCE4BC,stroke-width:2px,stroke-dasharray:0,rx:15,ry:15
  classDef errorHandling fill:#FFE4E1,stroke:#F4BAB8,stroke-width:2px,stroke-dasharray:0,rx:15,ry:15
  classDef initialization fill:#E6E6FA,stroke:#C6B7E1,stroke-width:2px,stroke-dasharray:0,rx:15,ry:15
  classDef groupbox fill:#F8F8F8,stroke:#B0C4DE,stroke-width:2px,stroke-dasharray:2
  classDef gborder fill:#F8F8F8,stroke:#83BDE4,stroke-width:3px

  %% ########################## INCIDENT & AUDIT EVENTS DOMAIN ########################## %%
  subgraph IncidentAndAuditEvents [Incident & Audit Events Domain]
    direction TB
    class IncidentAndAuditEvents groupbox

    %% CORE MODEL FILES %%
    subgraph "Incident Management: Core Models"
      direction TB
      im_ims["project_incident_management_setting.rb" 
        \n- Project-level incident settings\n- Issue templates & PagerDuty token mgmt.]:::dataModel
      im_tet["timeline_event_tag.rb"
        "\n- Predefined/saved labels for timeline events"]:::dataModel
      im_escalatable["concerns/incident_management/escalatable.rb" 
        "\n- Escalatable states for incidents\n- State machine & status logic"]:::coreDomain
      im_op["oncall_participant.rb\n- Oncall user assignment"]:::dataModel
      im_os["oncall_shift.rb\n- Oncall shift instance"]:::dataModel
      im_or["oncall_rotation.rb\n- Oncall rotation schedule"]:::dataModel
      im_osched["oncall_schedule.rb\n- Oncall schedule for incidents"]:::dataModel
      im_base_pending_escal["concerns/incident_management/base_pending_escalation.rb\n- Shared concern for pending escalations"]:::coreDomain
      im_escalatable_ee["concerns/ee/incident_management/escalatable.rb\n- EE Escalatable enhancements"]:::coreDomain
      im_escalation_policy["escalation_policy.rb\n- Policy object for escalations"]:::dataModel
    end

    subgraph "Audit Events: Data"
      direction TB
      audit_event["audit_event.rb\n- Record of user-triggered action"]:::dataModel
      audit_event_group["audit_events/group_audit_event.rb"]:::dataModel
      audit_event_user["audit_events/user_audit_event.rb"]:::dataModel
    end

    subgraph "Incident Management: Finders/Querying"
      direction TB
      ims_timeline_events_finder["timeline_events_finder.rb\n- Timeline events querying"]:::utility
      ims_issuable_resource_links_finder["issuable_resource_links_finder.rb\n- Find resource links for incidents"]:::utility
      ims_escalation_rules_finder["escalation_rules_finder.rb\n- Find escalation rules by user/project"]:::utility
      ims_oncall_rotations_finder["oncall_rotations_finder.rb"]:::utility
      ims_oncall_schedules_finder["oncall_schedules_finder.rb"]:::utility
      ims_member_oncall_rotations["member_oncall_rotations_finder.rb"]:::utility
      ims_escalation_policies_finder["escalation_policies_finder.rb"]:::utility
      ims_status_page_incidents["status_page/incidents_finder.rb"]:::utility
    end

    %% Timeline and Escalation subdomain groupings
    subgraph "Timeline Events"
      direction TB
      im_tel_evts_create["timeline_events/create_service.rb\n- Create timeline event\n- Auto/system note helpers"]:::coreDomain
      im_tel_evts_update["timeline_events/update_service.rb\n- Update timeline event"]:::coreDomain
      im_tel_evts_destroy["timeline_events/destroy_service.rb\n- Delete timeline event"]:::coreDomain
      im_tel_evt_tags["timeline_event_tags/create_service.rb\n- Create timeline event tag"]:::coreDomain
      im_tel_evt_tags_base["timeline_event_tags/base_service.rb\n- Shared tag logic & permissions"]:::coreDomain
      im_inc_svc["system_notes/incidents_service.rb\n- Notes for event edits/changes"]:::utility
    end

    subgraph "Escalation"
      direction TB
      im_ies_create["issuable_escalation_statuses/create_service.rb"]:::coreDomain
      im_ies_build["issuable_escalation_statuses/build_service.rb"]:::coreDomain
      im_escalate_pending_create["pending_escalations/create_service.rb"]:::coreDomain
      im_escalate_pending_proc["pending_escalations/process_service.rb"]:::coreDomain
      im_escalation_rules_destroy["escalation_rules/destroy_service.rb"]:::coreDomain
      im_incident_create_sla["incidents/create_sla_service.rb"]:::coreDomain
    end

    subgraph "Oncall & Shifts"
      direction TB
      im_oncall_remove_part["oncall_rotations/remove_participant_service.rb\n- Remove user from oncall"]:::coreDomain
      im_oncall_destroy["oncall_rotations/destroy_service.rb\n- Destroy a rotation"]:::coreDomain
      im_oncall_read["oncall_shifts/read_service.rb\n- Query/generate shifts"]:::coreDomain
    end
    
    subgraph "Incident-Alert Linking"
      direction TB
      im_link_alerts_create["link_alerts/create_service.rb\n- Link alerts to incident"]:::coreDomain
      im_link_alerts_destroy["link_alerts/destroy_service.rb\n- Remove alert link from incident"]:::coreDomain
    end

    subgraph "PagerDuty Integrations"
      direction TB
      im_pd_process_inc_worker["pager_duty/process_incident_worker.rb\n- Sidekiq worker: PagerDuty webhook -> incident"]:::initialization
      im_pd_create_issue_svc["pager_duty/create_incident_issue_service.rb\n- Create incident from PagerDuty data"]:::coreDomain
      im_pd_process_webhook_svc["pager_duty/process_webhook_service.rb\n- Parse & dispatch incoming PagerDuty"]:::coreDomain
    end

    subgraph "Pending Escalation Workers"
      direction TB
      im_pe_issue_create_worker["pending_escalations/issue_create_worker.rb"]:::initialization
      im_pe_issue_check_worker["pending_escalations/issue_check_worker.rb"]:::initialization
      im_pe_alert_create_worker["pending_escalations/alert_create_worker.rb"]:::initialization
      im_pe_alert_check_worker["pending_escalations/alert_check_worker.rb"]:::initialization
      im_pe_schedule_check_worker["pending_escalations/schedule_check_cron_worker.rb"]:::initialization
    end

    subgraph "Incident Management: Miscellaneous Workers"
      direction TB
      im_add_severity_worker["add_severity_system_note_worker.rb\n- Sidekiq: Add severity note"]:::initialization
      im_apply_inc_sla_worker["apply_incident_sla_exceeded_label_worker.rb\n- SLA exceeded tagging"]:::initialization
      im_inc_sla_exceeded_check_worker["incident_sla_exceeded_check_worker.rb\n- Checks incident SLAs"]:::initialization
    end

    %% ----- AUDIT LOGGING & STREAMING ------ %%
    subgraph "Audit Event Presentation & Streaming"
      direction TB
      audit_event_entity["audit_event_entity.rb\n- Grape entity for API/REST"]:::utility
      audit_event_presenter["audit_event_presenter.rb\n- Presenter for audit event view"]:::utility
    end

    %% ----- CONTROLLERS ------- %%
    subgraph "Incident Management: Controllers"
      direction TB
      im_timeline_events_ctrl["timeline_events_controller.rb\n- REST API for timeline events"]:::coreDomain
      im_incidents_ctrl["incidents_controller.rb\n- REST for incidents, issue linking"]:::coreDomain
      admin_abuse_ctrl["admin/abuse_reports_controller.rb\n- Admin actions for abuse reports"]:::coreDomain
      abuse_report_mailer["abuse_report_mailer.rb\n- Notify abuse reports"]:::utility
    end

    %% ADAPTIVE POLICIES & PERMISSIONS %%
    subgraph "Policies"
      direction TB
      im_global_policy["global_policy.rb\n- Project-level global permissions"]:::coreDomain
      im_escal_policy_policy["escalation_policy_policy.rb\n- RBAC for escalation policy"]:::coreDomain
    end

    %% RESOURCE LINKS FOR INCIDENTS %%
    subgraph "Resource Links Incident Management"
      direction TB
      im_irlinks_create["issuable_resource_links/create_service.rb"]:::coreDomain
      im_irlinks_base["issuable_resource_links/base_service.rb"]:::coreDomain
    end

    %% GROUPING FOR ALERT MANAGEMENT INTERACTION POINT %%
    subgraph "Alert Management"
      direction TB
      am_alerts_finder["alerts_finder.rb\n- Query & count alerts"]:::utility
      am_alerts_todo_create["alerts/todo/create_service.rb\n- Create TODO for alert"]:::coreDomain
      am_create_alert_issue["create_alert_issue_service.rb\n- Build incident from alert"]:::coreDomain
      am_alert_processing_concern["concerns/alert_management/alert_processing.rb\n- Process/handle an alert"]:::coreDomain
      am_http_integrations_destroy["http_integrations/destroy_service.rb\n- Remove integration"]:::coreDomain
      am_extract_payload_fields["extract_alert_payload_fields_service.rb\n- Parse & check alert payload"]:::coreDomain
    end

    %% SYSTEM NOTES & AUDITIFICATION %%
    sys_notes_incidents["system_notes/incidents_service.rb\n- System  for incidents"]:::utility

  end %% end of IncidentAndAuditEvents
  
  %% ##################### RELATIONSHIPS & DATA FLOWS ####################### %%

  %% CORE LOGICAL COLLABORATION %%
  im_ims -->|uses| im_escalatable
  im_ims --> im_pd_create_issue_svc
  im_escalatable --> im_escalatable_ee
  im_base_pending_escal --> im_escalate_pending_create
  im_base_pending_escal --> im_pe_issue_create_worker
  im_base_pending_escal --> im_pe_alert_create_worker
  im_escalatable_ee --> im_escalation_policy

  im_escalation_policy --> im_escalate_pending_proc
  im_escalation_policy --> im_escalation_rules_destroy

  im_oncall_destroy --> im_or
  im_oncall_remove_part --> im_or
  im_oncall_read --> im_or
  im_oncall_read --> im_os
  im_or --> im_osched
  im_op --> im_or
  im_op --> im_os
  im_escalatable --> im_escalation_policy
  im_escalation_policy --> im_escalation_rules_finder
  im_escalation_policy --> im_escalation_policy

  %% TIMELINE EVENTS LOGIC FLOW %%
  im_tel_evts_create -.-> im_tel_evts_update
  im_tel_evts_create -.-> im_tel_evts_destroy
  im_tel_evts_create -.-> im_tet
  im_tel_evts_update -.-> im_tet
  im_tel_evts_create -.-> sys_notes_incidents
  im_tel_evts_update -.-> sys_notes_incidents
  im_tel_evts_destroy -.-> sys_notes_incidents
  im_tel_evt_tags --> im_tet
  im_tel_evt_tags --> im_tel_evt_tags_base
  im_tel_evts_create --> im_tel_evt_tags_base

  ims_timeline_events_finder --> im_tel_evts_create
  ims_timeline_events_finder --> im_tel_evts_update
  ims_timeline_events_finder --> im_tel_evts_destroy

  im_timeline_events_ctrl --> ims_timeline_events_finder
  im_timeline_events_ctrl --> im_tel_evts_create
  im_timeline_events_ctrl --> im_tel_evts_update
  im_timeline_events_ctrl --> im_tel_evts_destroy
  im_timeline_events_ctrl --> im_tel_evt_tags
  im_timeline_events_ctrl --> im_inc_svc

  %% INCIDENT ESCALATION LOGIC %%
  im_incidents_ctrl --> im_link_alerts_create
  im_incidents_ctrl --> im_link_alerts_destroy
  im_incidents_ctrl --> im_ies_create
  im_incidents_ctrl --> im_escalatable
  
  im_ies_create --> im_ies_build

  im_escalate_pending_create -->|creates| im_escalate_pending_proc
  im_escalate_pending_create --> im_escalation_policy
  im_escalate_pending_proc -->|processes| im_escalatable

  im_escalate_pending_create --> im_escalatable_ee
  im_escalate_pending_proc --> im_escalatable_ee

  im_ims --> im_pd_process_inc_worker
  im_pd_process_inc_worker --> im_pd_create_issue_svc
  im_pd_process_inc_worker --> im_pd_process_webhook_svc
  im_pd_process_webhook_svc --> im_pd_create_issue_svc
  im_pd_process_webhook_svc --> im_escalatable

  %% RESOURCE LINKS LOGIC %%
  im_irlinks_create --> im_irlinks_base
  im_irlinks_base --> ims_issuable_resource_links_finder
  
  %% ALERT MANAGEMENT -->
  am_alerts_finder --> am_create_alert_issue
  am_alerts_finder --> am_alerts_todo_create
  am_create_alert_issue --> im_incidents_ctrl
  am_create_alert_issue --> am_alert_processing_concern

  am_alert_processing_concern --> im_escalatable
  am_alert_processing_concern --> im_link_alerts_create
  am_alert_processing_concern --> im_link_alerts_destroy
  
  am_http_integrations_destroy --Unauthorized/Error--> errorHandlingNode
  am_extract_payload_fields --> am_alerts_finder

  %% WORKER COLLABORATIONS %%
  im_pe_issue_check_worker --> im_base_pending_escal
  im_pe_alert_check_worker --> im_base_pending_escal

  im_add_severity_worker ..> im_escalatable
  im_apply_inc_sla_worker ..> im_escalatable
  im_inc_sla_exceeded_check_worker ..> im_escalatable
  
  %% ADMIN/ABUSE REPORTING %%
  admin_abuse_ctrl --> abuse_report_mailer
  admin_abuse_ctrl --> audit_event

  %% POLICIES
  im_escal_policy_policy --> im_escalation_policy
  im_global_policy --> im_ims

  %% AUDIT EVENTS / PRESENTATION %%
  audit_event_entity --> audit_event
  audit_event_presenter --> audit_event

  %% DATA STRUCTURE & TRANSFORM EXAMPLES %%
  im_escalatable -.- ims_escalation_rules_finder
  im_escalatable -.- ims_escalation_policies_finder
  im_escalatable -.- ims_oncall_rotations_finder

  im_oncall_read --> im_os
  im_oncall_read --> im_or
  im_oncall_read --> im_op

  %% RESOURCE LINKS & GRAPHQL LINK RESOLVER %%
  im_irlinks_create --> im_irlinks_base
  im_irlinks_base -.-> ims_issuable_resource_links_finder

  %% TIMELINE TAG INTERACTIONS %%
  im_tel_evt_tags --> im_tet
  im_tel_evts_create -.-> im_tel_evt_tags

  %% DATA AGGREGATION FINDERS %%
  ims_issuable_resource_links_finder --> im_irlinks_base
  ims_oncall_rotations_finder --> im_or
  ims_oncall_schedules_finder --> im_osched
  ims_member_oncall_rotations --> im_or

  %% INTERACTION WITH LINKS BETWEEN ALERTS AND INCIDENTS %%
  im_link_alerts_create -.-> am_create_alert_issue
  im_link_alerts_destroy -.-> am_create_alert_issue
  am_create_alert_issue -.-> im_incidents_ctrl


  %% Data Flows (examples of full process) %%
  im_pd_process_inc_worker --> im_pd_process_webhook_svc --> im_pd_create_issue_svc --> im_ims
  am_create_alert_issue --> im_incidents_ctrl
  im_tel_evts_create --> im_tel_evt_tags
  im_tel_evts_update --> im_tel_evt_tags
  im_tel_evts_destroy --> im_tel_evt_tags

  class im_ims,im_escalatable,im_escalatable_ee,im_base_pending_escal,im_tel_evts_create,im_tel_evts_update,im_tel_evts_destroy,im_tel_evt_tags,im_tel_evt_tags_base,im_inc_svc,im_ies_create,im_ies_build,im_escalate_pending_create,im_escalate_pending_proc,im_irlinks_create,im_irlinks_base,im_link_alerts_create,im_link_alerts_destroy,im_oncall_remove_part,im_oncall_destroy,im_oncall_read,im_pd_create_issue_svc,im_pd_process_webhook_svc,im_incident_create_sla,im_oncall_read,im_escalation_rules_destroy,am_create_alert_issue,am_alert_processing_concern,am_extract_payload_fields,am_alerts_todo_create,am_http_integrations_destroy,im_tel_evt_tags_base,im_tel_evt_tags,im_tel_evts_update,im_tel_evts_create coreDomain;
  class audit_event,audit_event_presenter,audit_event_entity,admin_abuse_ctrl,abuse_report_mailer,im_inc_svc,im_timeline_events_ctrl,im_incidents_ctrl utility;
  class im_tet,im_or,im_osched,im_op,im_os,im_escalation_policy,im_base_pending_escal,audit_event,audit_event_user,audit_event_group dataModel;
  class im_pd_process_inc_worker,im_add_severity_worker,im_apply_inc_sla_worker,im_inc_sla_exceeded_check_worker,ims_timeline_events_finder,ims_oncall_schedules_finder,ims_escalation_rules_finder,ims_issuable_resource_links_finder,ims_member_oncall_rotations,ims_oncall_rotations_finder,ims_escalation_policies_finder,ims_status_page_incidents,im_pe_issue_create_worker,im_pe_issue_check_worker,im_pe_alert_create_worker,im_pe_alert_check_worker,im_pe_schedule_check_worker initialization;
  class am_http_integrations_destroy,errorHandlingNode errorHandling;

  %% VISUAL GROUP BORDERS %%
  class IncidentAndAuditEvents gborder
```