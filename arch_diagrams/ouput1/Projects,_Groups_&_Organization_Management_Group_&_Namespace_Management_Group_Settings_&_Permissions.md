```mermaid
flowchart TB
  %% Domain: Projects, Groups & Organization Management / Group & Namespace Management / Group Settings & Permissions
  %% VERTICAL layout enforced
  %% COLOR LEGEND (do not render, for author only):
  %% Core domain files: pastel blue (#D4F1F9)
  %% Supporting/utility files: pastel yellow (#FFF8DC)
  %% Data structure files: pastel green (#E0F8E0)
  %% Error handling files: pastel red (#FFE4E1)
  %% Initialization/setup files: pastel purple (#E6E6FA)
  %% Logical groupings/subgraphs: very light gray (#F8F8F8) with pastel borders

  direction TB

  %% Group: Domain Entry Points (Controllers)
  subgraph sgControllers[Controllers / Entry Points]
    direction TB
    style sgControllers fill:#F8F8F8,stroke:#99BFE3,stroke-width:2,rounded-rectangle
    cMain["Groups::ApplicationController":::core]
    cVariables["Groups::VariablesController":::core]
    cSettingsRepo["Groups::Settings::RepositoryController":::core]
    cSettingsPackages["Groups::Settings::PackagesAndRegistriesController":::core]
    cSettingsSlacks["Groups::Settings::SlacksController":::core]
    cAchievements["Groups::AchievementsController":::core]
    cSettingsCiCd["Groups::Settings::CiCdController":::core]
    cCrmContacts["Groups::Crm::ContactsController":::core]
    cCustomEmoji["Groups::CustomEmojiController":::core]
    cCallouts["Users::GroupCalloutsController":::core]
    %% EE Controllers
    ee_cDomainVerification["Groups::Settings::DomainVerificationController":::core]
    ee_cGitlabDuo["Groups::Settings::GitlabDuoController":::core]
    ee_cGitlabDuoConfig["Groups::Settings::GitlabDuo::ConfigurationController":::core]
    ee_cWorkItems["Groups::Settings::WorkItemsController":::core]
    ee_cAnalytics["Groups::Settings::AnalyticsController":::core]
    ee_cMR["Groups::Settings::MergeRequestsController":::core]
    ee_cReporting["Groups::Settings::ReportingController":::core]
    ee_cPushRules["Groups::PushRulesController":::core]
    ee_cTwoFactor["Groups::TwoFactorAuthsController":::core]
    ee_cEarlyAccess["Groups::EarlyAccessOptInController":::core]
    ee_cAddonDuoPro["Groups::AddOns::DiscoverDuoProController":::core]
    ee_cAddonDuoEnt["Groups::AddOns::DiscoverDuoEnterpriseController":::core]
    ee_cVirtualRegs["Groups::VirtualRegistries::BaseController":::core]
  end
  classDef core fill:#D4F1F9,stroke:#8CC7E6,stroke-width:1,rounded-rectangle

  %% Group: Policies
  subgraph sgPolicies[Access Policies]
    direction TB
    style sgPolicies fill:#F8F8F8,stroke:#99BFE3,stroke-width:2,rounded-rectangle
    pUserNamespace["Namespaces::UserNamespacePolicy":::core]
    pGroupDeployKey["GroupDeployKeyPolicy":::core]
    ee_pGroupHook["GroupHookPolicy":::core]
  end

  %% Group: Finders / Loaders
  subgraph sgFinders[Finders & Preloaders]
    direction TB
    style sgFinders fill:#F8F8F8,stroke:#FFE08C,stroke-width:2,rounded-rectangle
    fCustomEmoji["Groups::CustomEmojiFinder":::support]
    fNotification["UserGroupNotificationSettingsFinder":::support]
    fEnvScopes["Groups::EnvironmentScopesFinder":::support]
    preGroupPolicy["Preloaders::GroupPolicyPreloader":::support]
  end
  classDef support fill:#FFF8DC,stroke:#FFE08C,stroke-width:1,rounded-rectangle

  %% Group: Helpers
  subgraph sgHelpers[View & Support Helpers]
    direction TB
    style sgHelpers fill:#F8F8F8,stroke:#FFE08C,stroke-width:2,rounded-rectangle
    hGroups["GroupsHelper":::support]
    hEEGroups["EE::GroupsHelper":::support]
    hGroupsSettings["EE::Groups::SettingsHelper":::support]
    hDomainVerification["Groups::DomainVerificationHelper":::support]
    hPathLocks["PathLocksHelper":::support]
  end

  %% Group: Services Business Logic
  subgraph sgServices[Core Domain Services]
    direction TB
    style sgServices fill:#F8F8F8,stroke:#99BFE3,stroke-width:2,rounded-rectangle

    sGroupsBase["Groups::BaseService":::core]
    sUpdate["Groups::UpdateService":::core]
    sDestroy["Groups::DestroyService":::core]
    sUpdateSharedRunners["Groups::UpdateSharedRunnersService":::core]

    %% SSH Certificates
    sSshCertCreate["Groups::SshCertificates::CreateService":::core]
    sSshCertDestroy["Groups::SshCertificates::DestroyService":::core]
    ee_sSshCertCreate["EE::Groups::SshCertificates::CreateService":::core]
    ee_sSshCertDestroy["EE::Groups::SshCertificates::DestroyService":::core]

    %% Deploy Tokens
    sDeployCreate["Groups::DeployTokens::CreateService":::core]
    sDeployDestroy["Groups::DeployTokens::DestroyService":::core]
    ee_sDeployRevoke["EE::Groups::DeployTokens::RevokeService":::core]
    ee_sDeployDestroy["EE::Groups::DeployTokens::DestroyService":::core]

    %% Group Links
    sGLUpdate["Groups::GroupLinks::UpdateService":::core]
    sGLDestroy["Groups::GroupLinks::DestroyService":::core]
    pGLDestroy["Projects::GroupLinks::DestroyService":::core]
    ee_sGLUpdate["EE::Groups::GroupLinks::UpdateService":::core]
    ee_sGLDestroy["EE::Groups::GroupLinks::DestroyService":::core]
    ee_pGLUpdate["EE::Projects::GroupLinks::UpdateService":::core]

    %% Namespace Manipulation
    nsSettingsAssign["NamespaceSettings::AssignAttributesService":::core]
    sNSArchive["Namespaces::Groups::ArchiveService":::core]
    sNSUnarchive["Namespaces::Groups::UnarchiveService":::core]
    sNSAdjourned["Namespaces::Groups::AdjournedDeletionService":::core]

    ee_sRestore["EE::Groups::RestoreService":::core]
    ee_sMarkForDeletion["EE::Groups::MarkForDeletionService":::core]
    ee_sDestroy["EE::Groups::DestroyService":::core]
    ee_sUpdate["EE::Groups::UpdateService":::core]

    sScheduleBulkRepos["Groups::ScheduleBulkRepositoryShardMovesService":::core]
    sUpdateRepoStorage["Groups::UpdateRepositoryStorageService":::core]
  end

  %% Group: Data Models & Structures
  subgraph sgModelsDB[Database-backed Models / Data Structures]
    direction TB
    style sgModelsDB fill:#F8F8F8,stroke:#78D678,stroke-width:2,rounded-rectangle

    mNamespaceSetting["NamespaceSetting":::data]
    mLdapSetting["Namespaces::LdapSetting":::data]
    mGroupCustomAttr["GroupCustomAttribute":::data]
    mNSAdminNote["Namespace::AdminNote":::data]
    mFeatureSetting["Groups::FeatureSetting":::data]
    mCRMSettings["Group::CrmSettings":::data]

    ee_mFeatureSetting["EE::Groups::FeatureSetting":::data]
    ee_mPushRule["GroupPushRule":::data]
    ee_mSshCert["Groups::SshCertificate":::data]
    ee_mNSS["EE::NamespaceSetting":::data]
    ee_mNSSCICD["EE::NamespaceCiCdSetting":::data]
    ee_mAllowedEmailDomain["AllowedEmailDomain":::data]
    mAPICompatibility["GroupAPICompatibility":::data]
    ee_mLabelLink["EE::LabelLink":::data]
    ee_mMSApp["SystemAccess::GroupMicrosoftApplication":::data]
  end
  classDef data fill:#E0F8E0,stroke:#78D678,stroke-width:1,rounded-rectangle

  %% Group: Error Responses / Service Response
  subgraph sgErrorHandling[Error & Response Handling]
    direction TB
    style sgErrorHandling fill:#F8F8F8,stroke:#EB8D8D,stroke-width:2,rounded-rectangle
    sDestroyServiceError["Groups::DestroyService::DestroyError":::error]
    nsUnarchiveError["Namespaces::Groups::UnarchiveService errors":::error]
    nsArchiveError["Namespaces::Groups::ArchiveService errors":::error]
    nsAdjournedError["Namespaces::Groups::AdjournedDeletionService errors":::error]
  end
  classDef error fill:#FFE4E1,stroke:#EB8D8D,stroke-width:1,rounded-rectangle

  %% Group: Workers / Async Jobs
  subgraph sgWorkers[Workers / Async Jobs]
    direction TB
    style sgWorkers fill:#F8F8F8,stroke:#E6C2F6,stroke-width:2,rounded-rectangle
    w2FA["Groups::UpdateTwoFactorRequirementForMembersWorker":::init]
    wDisallow2FA["DisallowTwoFactorForGroupWorker":::init]
  end
  classDef init fill:#E6E6FA,stroke:#E6C2F6,stroke-width:1,rounded-rectangle

  %% Group: Serializers & Entities
  subgraph sgEntities[Serializers & Grap Entities]
    direction TB
    style sgEntities fill:#F8F8F8,stroke:#8CC7E6,stroke-width:2,rounded-rectangle
    eChildEntity["GroupChildEntity":::core]
  end

  %% Integrations with Specialized Features / EE
  subgraph sgEE[Enterprise Extensions EE]
    direction TB
    style sgEE fill:#F8F8F8,stroke:#CCEFFF,stroke-width:2,rounded-rectangle
    ee_cEEBase["EE::Groups::ApplicationController":::core]
    ee_cEEGroupsController["EE::GroupsController":::core]
    ee_cParams["EE::Groups::Params":::support]
    ee_sideSettingsMenu["EE::Sidebars::Groups::Menus::SettingsMenu":::support]
    ee_sAllowedEmailDomain["EE::AllowedEmailDomains::UpdateService":::support]
    ee_sIpRestrict["EE::IpRestrictions::UpdateService":::support]
  end

  %% Group: GraphQL
  subgraph sgGraphQL[GraphQL Integrations]
    direction TB
    style sgGraphQL fill:#F8F8F8,stroke:#99BFE3,stroke-width:2,rounded-rectangle

    gql_sReplyCreate["Mutations::Groups::SavedReplies::Create":::support]
    gql_sReplyUpdate["Mutations::Groups::SavedReplies::Update":::support]
    gql_sReplyDestroy["Mutations::Groups::SavedReplies::Destroy":::support]
    gql_eeUpdate["EE::Mutations::Groups::Update":::support]
  end

  %% Domain "Initialization" and routing, constraints, search, QA, etc
  subgraph sgInfraSupport[Infra, Routing & QA]
    direction TB
    style sgInfraSupport fill:#F8F8F8,stroke:#FFE08C,stroke-width:2,rounded-rectangle
    rGroupConstrainer["Constraints::GroupUrlConstrainer":::support]
    sGroupSettings["Search::GroupSettings":::support]
  end

  %% Edge definitions: Controllers -> Services, Models, Policies, Helpers
  cMain --> sGroupsBase
  cMain --> pUserNamespace
  cMain --> hGroups
  cMain --> fNotification
  cMain --> mFeatureSetting
  cMain --> mNamespaceSetting
  cMain --> mLdapSetting

  cVariables --> sGroupsBase
  cVariables --> mNamespaceSetting

  cSettingsRepo --> sGroupsBase
  cSettingsRepo --> mFeatureSetting

  cSettingsPackages --> sGroupsBase
  cSettingsPackages --> mFeatureSetting

  cSettingsSlacks --> sGroupsBase

  cAchievements --> mGroupCustomAttr

  cSettingsCiCd --> sGroupsBase
  cSettingsCiCd --> mNamespaceSetting

  cCrmContacts --> mCRMSettings

  cCustomEmoji --> fCustomEmoji

  cCallouts --> hGroups

  ee_cDomainVerification --> hDomainVerification
  ee_cDomainVerification --> mNamespaceSetting

  ee_cGitlabDuo --> sGroupsBase
  ee_cGitlabDuoConfig --> sGroupsBase

  ee_cWorkItems --> mNamespaceSetting
  ee_cAnalytics --> hGroups

  ee_cMR --> mFeatureSetting

  ee_cReporting --> hGroups

  ee_cPushRules --> ee_mPushRule
  ee_cPushRules --> hGroups

  ee_cTwoFactor --> sGroupsBase
  ee_cTwoFactor --> w2FA
  ee_cTwoFactor --> wDisallow2FA

  ee_cEarlyAccess --> mNamespaceSetting
  ee_cEarlyAccess --> hGroups

  ee_cAddonDuoPro --> mNamespaceSetting
  ee_cAddonDuoEnt --> mNamespaceSetting

  ee_cVirtualRegs --> mFeatureSetting

  %% Policies relations
  pUserNamespace --> mNamespaceSetting
  pGroupDeployKey --> mFeatureSetting
  ee_pGroupHook --> mFeatureSetting

  %% Finders relations
  fNotification --> mFeatureSetting
  fCustomEmoji --> mFeatureSetting
  fEnvScopes --> mFeatureSetting
  preGroupPolicy --> pUserNamespace

  %% Helpers
  hGroups --> mFeatureSetting
  hGroups --> mNamespaceSetting
  hEEGroups --> mNamespaceSetting
  hGroupsSettings --> mNamespaceSetting
  hGroupsSettings --> mFeatureSetting
  hDomainVerification --> mFeatureSetting
  hPathLocks --> mFeatureSetting

  %% Service group internal interactions
  sGroupsBase --> mFeatureSetting
  sGroupsBase --> mNamespaceSetting
  sGroupsBase --> mLdapSetting

  sUpdate --> sGroupsBase
  sUpdate --> mNamespaceSetting
  sUpdate --> mFeatureSetting

  sDestroy --> sGroupsBase
  sDestroy --> mFeatureSetting
  sDestroy --> w2FA
  sDestroy --> wDisallow2FA
  sDestroy --> sDestroyServiceError

  sUpdateSharedRunners --> sGroupsBase
  sUpdateSharedRunners --> mFeatureSetting

  sSshCertCreate --> sGroupsBase
  sSshCertCreate --> ee_mSshCert
  sSshCertDestroy --> sGroupsBase
  sSshCertDestroy --> ee_mSshCert
  ee_sSshCertCreate --> sSshCertCreate
  ee_sSshCertDestroy --> sSshCertDestroy

  sDeployCreate --> sGroupsBase
  sDeployCreate --> mFeatureSetting
  sDeployDestroy --> sGroupsBase
  sDeployDestroy --> mFeatureSetting
  ee_sDeployRevoke --> sDeployDestroy
  ee_sDeployDestroy --> sDeployDestroy

  sGLUpdate --> sGroupsBase
  sGLDestroy --> sGroupsBase
  sGLDestroy --> pGLDestroy
  sGLDestroy --> mFeatureSetting

  pGLDestroy --> sGroupsBase
  pGLDestroy --> mFeatureSetting

  ee_sGLUpdate --> sGLUpdate
  ee_sGLDestroy --> sGLDestroy
  ee_pGLUpdate --> sGLUpdate

  nsSettingsAssign --> mNamespaceSetting

  sNSArchive --> sGroupsBase
  sNSArchive --> nsArchiveError

  sNSUnarchive --> sGroupsBase
  sNSUnarchive --> nsUnarchiveError

  sNSAdjourned --> sGroupsBase
  sNSAdjourned --> nsAdjournedError

  ee_sDestroy --> sDestroy
  ee_sDestroy --> sDestroyServiceError
  ee_sUpdate --> sUpdate
  ee_sUpdate --> mNamespaceSetting

  ee_sRestore --> sGroupsBase
  ee_sRestore --> ee_sDestroy
  ee_sMarkForDeletion --> sGroupsBase

  sScheduleBulkRepos --> mFeatureSetting
  sScheduleBulkRepos --> mNamespaceSetting

  sUpdateRepoStorage --> mFeatureSetting

  %% Data Model relationships
  mFeatureSetting --> mNamespaceSetting
  ee_mFeatureSetting --> mFeatureSetting
  ee_mSshCert --> mNamespaceSetting
  mLdapSetting --> mNamespaceSetting
  mNSAdminNote --> mNamespaceSetting
  mGroupCustomAttr --> mNamespaceSetting
  ee_mNSS --> mNamespaceSetting
  ee_mNSSCICD --> mNamespaceSetting

  %% Worker associations
  w2FA --> sDestroy
  wDisallow2FA --> sDestroy

  %% Serializers
  eChildEntity --> mNamespaceSetting
  eChildEntity --> mFeatureSetting

  %% EE (Enterprise Extensions)
  ee_cEEBase --> cMain
  ee_cEEGroupsController --> cMain
  ee_cParams --> sUpdate
  ee_sideSettingsMenu --> cMain
  ee_sideSettingsMenu --> hEEGroups

  ee_sAllowedEmailDomain --> ee_mAllowedEmailDomain
  ee_sIpRestrict --> mFeatureSetting

  %% GraphQL
  gql_sReplyCreate --> mNamespaceSetting
  gql_sReplyUpdate --> mNamespaceSetting
  gql_sReplyDestroy --> mNamespaceSetting
  gql_eeUpdate --> ee_sUpdate

  %% Infra, Routing, QA, Search
  rGroupConstrainer --> mNamespaceSetting
  rGroupConstrainer --> mFeatureSetting

  sGroupSettings --> mFeatureSetting
  sGroupSettings --> mNamespaceSetting

  %% Domain-wide data flows and conceptual flows
  cMain -.-> sUpdate
  cMain -.-> sDestroy
  cMain -.-> sNSArchive
  cMain -.-> sNSUnarchive
  cMain -.-> nsSettingsAssign
  cMain -.-> sGLUpdate
  cMain -.-> sGLDestroy
  cMain -.-> sSshCertCreate
  cMain -.-> sSshCertDestroy
  cMain -.-> sDeployCreate
  cMain -.-> sDeployDestroy
  cMain -.-> sUpdateSharedRunners

  %% EE Service extensions and audit hooks
  ee_sGLUpdate --> ee_sGLDestroy
  ee_sGLDestroy --> ee_sUpdate
  ee_sDeployDestroy --> ee_sDeployRevoke

  %% Error flows
  sNSArchive -.-> nsArchiveError
  sNSUnarchive -.-> nsUnarchiveError
  sNSAdjourned -.-> nsAdjournedError
  sDestroy -.-> sDestroyServiceError
  ee_sDestroy -.-> sDestroyServiceError
  ee_sUpdate -.-> sUpdate

  %% Group: Interactions between major logical clusters

  %% Core user flows: Controller <-> Service <-> Model
  cMain --> sUpdate
  sUpdate --> mNamespaceSetting
  sUpdate --> mFeatureSetting
  cMain --> sDestroy
  sDestroy --> mNamespaceSetting

  %% Shared helpers and policies throughout
  cMain --> hGroups
  cMain --> hEEGroups
  cMain --> hGroupsSettings

  %% Finders used by Services and Controllers
  sGroupsBase --> preGroupPolicy
  sGroupsBase --> fNotification

  %% Settings assignment and mutation
  nsSettingsAssign --> mNamespaceSetting
  nsSettingsAssign --> mFeatureSetting

  %% EE-specific controllers and menus
  ee_sideSettingsMenu --> ee_cDomainVerification
  ee_cDomainVerification --> hDomainVerification

  %% Data structures utilized by serializers/entities and throughout flows
  mNamespaceSetting --> eChildEntity
  mFeatureSetting --> eChildEntity

  %% Edge classes
  class cMain,cVariables,cSettingsRepo,cSettingsPackages,cSettingsSlacks,cAchievements,cSettingsCiCd,cCrmContacts,cCustomEmoji,cCallouts,ee_cDomainVerification,ee_cGitlabDuo,ee_cGitlabDuoConfig,ee_cWorkItems,ee_cAnalytics,ee_cMR,ee_cReporting,ee_cPushRules,ee_cTwoFactor,ee_cEarlyAccess,ee_cAddonDuoPro,ee_cAddonDuoEnt,ee_cVirtualRegs core;
  class pUserNamespace,pGroupDeployKey,ee_pGroupHook core;
  class sGroupsBase,sUpdate,sDestroy,sUpdateSharedRunners,sSshCertCreate,sSshCertDestroy,sDeployCreate,sDeployDestroy,ee_sDeployRevoke,ee_sDeployDestroy,sGLUpdate,sGLDestroy,pGLDestroy,ee_sGLUpdate,ee_sGLDestroy,ee_pGLUpdate,nsSettingsAssign,sNSArchive,sNSUnarchive,sNSAdjourned,ee_sRestore,ee_sMarkForDeletion,ee_sDestroy,ee_sUpdate,sScheduleBulkRepos,sUpdateRepoStorage core;
  class mNamespaceSetting,mLdapSetting,mGroupCustomAttr,mNSAdminNote,mFeatureSetting,mCRMSettings,ee_mFeatureSetting,ee_mPushRule,ee_mSshCert,ee_mNSS,ee_mNSSCICD,ee_mAllowedEmailDomain,mAPICompatibility,ee_mLabelLink,ee_mMSApp data;
  class eChildEntity core;
  class hGroups,hEEGroups,hGroupsSettings,hDomainVerification,hPathLocks,sGroupSettings,rGroupConstrainer support;
  class w2FA,wDisallow2FA init;
  class fCustomEmoji,fNotification,fEnvScopes,preGroupPolicy support;
  class sDestroyServiceError,nsUnarchiveError,nsArchiveError,nsAdjournedError error;
  class gql_sReplyCreate,gql_sReplyUpdate,gql_sReplyDestroy,gql_eeUpdate support;
  class ee_cEEBase,ee_cEEGroupsController,ee_cParams,ee_sideSettingsMenu,ee_sAllowedEmailDomain,ee_sIpRestrict support;
```