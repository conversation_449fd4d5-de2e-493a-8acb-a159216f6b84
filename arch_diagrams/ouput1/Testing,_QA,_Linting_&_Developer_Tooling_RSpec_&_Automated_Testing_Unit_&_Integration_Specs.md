```mermaid
flowchart TD
%% VERTICAL layout, pastel colors, grouping by purpose/feature, logical relationships
%% Color palette:
%% pastel blue #D4F1F9 (core domain)
%% pastel yellow #FFF8DC (utilities/support)
%% pastel green #E0F8E0 (data structures/fixtures/mocks)
%% pastel red #FFE4E1 (error handling/edge specs)
%% pastel purple #E6E6FA (init/setup helpers)
%% subgraph border: very light gray #F8F8F8 with pastel borders

%% --- GROUP: UNIT & INTEGRATION SPECS (Core test cases) ---
subgraph UNIT_INTEGRATION_SPECS [Unit & Integration Specs]
direction TB
style UNIT_INTEGRATION_SPECS fill:#F8F8F8,stroke:#ACC7D8,stroke-width:2,rounded=true

  MRBaseServiceSpec["spec/services/merge_requests/base_service_spec.rb":::domainfile]:::core
  MRCloseServiceSpec["spec/services/merge_requests/close_service_spec.rb":::domainfile]:::core
  MRConflictsListServiceSpec["spec/services/merge_requests/conflicts/list_service_spec.rb":::domainfile]:::core
  MRConflictsResolveServiceSpec["spec/services/merge_requests/conflicts/resolve_service_spec.rb":::domainfile]:::core
  MRHandleAssigneesChangeServiceSpec["spec/services/merge_requests/handle_assignees_change_service_spec.rb":::domainfile]:::core
  MRLinkLFSObjectsServiceSpec["spec/services/merge_requests/link_lfs_objects_service_spec.rb":::domainfile]:::core
  MRRslvTodosServiceSpec["spec/services/merge_requests/resolve_todos_service_spec.rb":::domainfile]:::core
  MRMergeabilityLoggerSpec["spec/services/merge_requests/mergeability/logger_spec.rb":::domainfile]:::core
  MRConflictsServiceSpec["spec/services/merge_requests/conflicts/list_service_spec.rb":::domainfile]:::core
  MRConflictsResolveServiceSpec2["spec/services/merge_requests/conflicts/resolve_service_spec.rb":::domainfile]:::core

  MRBaseServiceSpec --> MRCloseServiceSpec
  MRBaseServiceSpec --> MRHandleAssigneesChangeServiceSpec
  MRHandleAssigneesChangeServiceSpec --> MRRslvTodosServiceSpec
  MRConflictsListServiceSpec --> MRConflictsResolveServiceSpec
  MRLinkLFSObjectsServiceSpec -.-> MRConflictsListServiceSpec
  MRConflictsResolveServiceSpec2 --> MRConflictsListServiceSpec
  MRMergeabilityLoggerSpec -.-> MRBaseServiceSpec
end

%% --- GROUP: MERGE REQUEST RELATED POLICIES Feature: Authorization & Policy ---
subgraph MR_POLICIES [Merge Request Policies & Permissions]
direction TB
style MR_POLICIES fill:#F8F8F8,stroke:#72A6C4,stroke-width:2,rounded=true

  MRPolicySpec["spec/policies/merge_request_policy_spec.rb":::domainfile]:::core
  BasePolicySpec["spec/policies/base_policy_spec.rb":::domainfile]:::core
  ResourceStateEventPolicySpec["spec/policies/resource_state_event_policy_spec.rb":::domainfile]:::core
  ResourceLabelEventPolicySpec["spec/policies/resource_label_event_policy_spec.rb":::domainfile]:::core
  IssuablePolicySpec["spec/policies/issuable_policy_spec.rb":::domainfile]:::core

  MRPolicySpec --> BasePolicySpec
  MRPolicySpec -.-> ResourceStateEventPolicySpec
  MRPolicySpec -.-> ResourceLabelEventPolicySpec
  MRPolicySpec -.-> IssuablePolicySpec
end

%% --- GROUP: DOMAIN FIXTURES & DATA STRUCTURES ---
subgraph DOMAIN_FIXTURES [Fixtures & Data Structures]
direction TB
style DOMAIN_FIXTURES fill:#F8F8F8,stroke:#8CC9AA,stroke-width:2,rounded=true

  DoraMetricsRb["ee/spec/frontend/fixtures/dora/metrics.rb":::datafile]:::dataset
  AnalyticsContribSpec["ee/spec/frontend/fixtures/analytics/contributions_spec.rb":::datafile]:::dataset
  MRDiffsFixture["spec/frontend/fixtures/merge_requests_diffs.rb":::datafile]:::dataset
  MRFixture["spec/frontend/fixtures/merge_requests.rb":::datafile]:::dataset
  PipelinesFixture["spec/frontend/fixtures/pipelines.rb":::datafile]:::dataset
  IssuesFixture["spec/frontend/fixtures/issues.rb":::datafile]:::dataset
  RawFixture["spec/frontend/fixtures/raw.rb":::datafile]:::dataset

  DoraMetricsRb <!---> AnalyticsContribSpec
  MRDiffsFixture --- MRFixture
  PipelinesFixture -. fixtures .- MRDiffsFixture
  IssuesFixture --- PipelinesFixture
  RawFixture -.-> MRDiffsFixture
end

%% --- GROUP: SHARED EXAMPLES & RSpec SHARED BEHAVIORS ---
subgraph SHARED_EXAMPLES [Shared Examples & Helpers]
direction TB
style SHARED_EXAMPLES fill:#F8F8F8,stroke:#DEC187,stroke-width:2,rounded=true

  SharedExamplesIssuesEE["ee/spec/support/shared_examples/requests/api/graphql/issue_list_shared_examples.rb":::utilfile]:::utility
  SharedExamplesEscalation["ee/spec/support/shared_examples/requests/api/graphql/incident_management/escalation_policies_shared_examples.rb":::utilfile]:::utility
  SharedIdentityVerification["ee/spec/support/shared_examples/requests/identity_verification_shared_examples.rb":::utilfile]:::utility
  HelperProfiles["spec/helpers/profiles_helper_spec.rb":::utilfile]:::utility
  HelperEvents["spec/helpers/events_helper_spec.rb":::utilfile]:::utility
  HelperApp["spec/helpers/application_helper_spec.rb":::utilfile]:::utility
  HelperSnippets["spec/helpers/snippets_helper_spec.rb":::utilfile]:::utility
  HelperNavDropdown["spec/helpers/nav/new_dropdown_helper_spec.rb":::utilfile]:::utility
  HelperTimeboxes["spec/helpers/timeboxes_helper_spec.rb":::utilfile]:::utility
  HelperUsers["spec/helpers/users_helper_spec.rb":::utilfile]:::utility

  HelperProfiles -.-> HelperUsers
  HelperEvents -.-> HelperTimeboxes
  SharedExamplesIssuesEE -.-> SharedExamplesEscalation
end

%% --- GROUP: SETUP & SPEC HELPERS ---
subgraph INIT_SETUP [Initialization and Spec Setup]
direction TB
style INIT_SETUP fill:#F8F8F8,stroke:#AEAEDC,stroke-width:2,rounded=true

  SpecHelper["spec/spec_helper.rb":::initfile]:::initnode
  CrystalballEnv["spec/crystalball_env.rb":::initfile]:::initnode
  ContractsSpecHelper["spec/contracts/provider/spec_helper.rb":::initfile]:::initnode

  SpecHelper --> CrystalballEnv
  SpecHelper --> ContractsSpecHelper
end

%% --- GROUP: RSpec SUPPORT UTILS Support modules, stubs, Sidekiq/test utilities ---
subgraph RSPEC_SUPPORT_UTILS [RSpec Support Utilities]
direction TB
style RSPEC_SUPPORT_UTILS fill:#F8F8F8,stroke:#FFD699,stroke-width:2,rounded=true

  RenameableUpload["spec/support/renameable_upload.rb":::utilfile]:::utility
  StubMember["spec/support/stub_member_access_level.rb":::utilfile]:::utility
  Tmpdir["spec/support/tmpdir.rb":::utilfile]:::utility
  SetupBuilds["spec/support/setup_builds_storage.rb":::utilfile]:::utility
  FinderCollection["spec/support/finder_collection.rb":::utilfile]:::utility
  Locks["spec/support/locks.rb":::utilfile]:::utility
  SidekiqSupport["spec/support/sidekiq.rb":::utilfile]:::utility
  StackProf["spec/support/stack_prof.rb":::utilfile]:::utility
  Webmock["spec/support/webmock.rb":::utilfile]:::utility
  MemoryInstrumentationHelper["spec/support/memory_instrumentation_helper.rb":::utilfile]:::utility

  SidekiqSupport --> Locks
  Locks --> SetupBuilds
  RenameableUpload -.-> Tmpdir
  Webmock -.-> FinderCollection
  StubMember --> HelperUsers
  StackProf -.-> SpecHelper
  MemoryInstrumentationHelper -.-> SpecHelper
end

%% --- GROUP: HELPER/UTILITY TESTS Helpers at the application domain boundary ---
subgraph UI_HELPERS [UI Application/Helper Specs]
direction TB
style UI_HELPERS fill:#F8F8F8,stroke:#FAE6B3,stroke-width:2,rounded=true

  HelperAnalyticsSettings["ee/spec/helpers/analytics/analytics_settings_helper_spec.rb":::utilfile]:::utility
  HelperAnalyticsDashboards["ee/spec/helpers/analytics/analytics_dashboards_helper_spec.rb":::utilfile]:::utility
  HelperAuditEvents["ee/spec/helpers/audit_events_helper_spec.rb":::utilfile]:::utility
  HelperAdminAppSettings["ee/spec/helpers/admin/application_settings_helper_spec.rb":::utilfile]:::utility
  EEAppHelper["ee/spec/helpers/application_helper_spec.rb":::utilfile]:::utility
  HelperLicense["ee/spec/helpers/license_helper_spec.rb":::utilfile]:::utility
  HelperNamespaceAlert["ee/spec/helpers/namespace_user_cap_reached_alert_helper_spec.rb":::utilfile]:::utility

  HelperAnalyticsSettings --> HelperAnalyticsDashboards
  HelperAdminAppSettings --> EEAppHelper
  HelperLicense -.-> HelperNamespaceAlert
end

%% --- GROUP: ENTITY & SERIALIZER TESTS ---
subgraph ENTITY_SERIALIZER [Entity & Serializer Specs]
direction TB
style ENTITY_SERIALIZER fill:#F8F8F8,stroke:#BEE2AD,stroke-width:2,rounded=true

  MemberUserEntitySpec["ee/spec/serializers/member_user_entity_spec.rb":::structfile]:::ds
  EnvironmentSerializerSpec["ee/spec/serializers/ee/environment_serializer_spec.rb":::structfile]:::ds
  IntegrationsFieldEntitySpec["spec/serializers/integrations/field_entity_spec.rb":::structfile]:::ds
  MergeRequestDiffEntitySpec["spec/serializers/merge_request_diff_entity_spec.rb":::structfile]:::ds

  MemberUserEntitySpec --> EnvironmentSerializerSpec
end

%% ---- FILE CLASS STYLES ----
classDef core fill:#D4F1F9,stroke:#396D84,stroke-width:2,rounded=true,color:#333;
classDef domainfile fill:#D4F1F9,stroke:#396D84,stroke-width:2,rounded=true,color:#333;
classDef datafile fill:#E0F8E0,stroke:#48825E,stroke-width:2,rounded=true,color:#333;
classDef structfile fill:#E0F8E0,stroke:#42705A,stroke-width:2,rounded=true,color:#333;
classDef ds fill:#E0F8E0,stroke:#5C8E6C,stroke-width:2,rounded=true,color:#333;
classDef utilfile fill:#FFF8DC,stroke:#B89642,stroke-width:2,rounded=true,color:#333;
classDef utility fill:#FFF8DC,stroke:#B89642,stroke-width:2,rounded=true,color:#333;
classDef initfile fill:#E6E6FA,stroke:#78628B,stroke-width:2,rounded=true,color:#333;
classDef initnode fill:#E6E6FA,stroke:#78628B,stroke-width:2,rounded=true,color:#333;
classDef errorfile fill:#FFE4E1,stroke:#DD6666,stroke-width:2,rounded=true,color:#333;

%% --- MAIN DOMAIN COLLABORATION AND LOGIC FLOW ---
%% Specs as drivers, shared examples/fixtures as reusable logic/data, helpers/utilities, serializers/entities

%% Unit Specs use: policies, helpers, fixtures, shared examples, serializers
MRBaseServiceSpec --> MRPolicySpec
MRBaseServiceSpec -. Uses Shared Examples .- SharedExamplesIssuesEE
MRBaseServiceSpec --- DoraMetricsRb
MRBaseServiceSpec --- AnalyticsContribSpec
MRBaseServiceSpec --- MRDiffsFixture
MRBaseServiceSpec --- MRFixture
MRBaseServiceSpec --- SharedIdentityVerification
MRBaseServiceSpec --> MemberUserEntitySpec
MRCloseServiceSpec --> MRBaseServiceSpec
MRBaseServiceSpec --> HelperApp

%% Policy Specs depend on SHARED_EXAMPLES and domain fixtures
MRPolicySpec --> SharedExamplesIssuesEE
MRPolicySpec --> SharedIdentityVerification
MRPolicySpec --> DoraMetricsRb

%% Fixtures cascade
DoraMetricsRb --> AnalyticsContribSpec
AnalyticsContribSpec -.-> MRFixture
MRDiffsFixture --> MRFixture
PipelinesFixture --> MRDiffsFixture
IssuesFixture --> PipelinesFixture
RawFixture --> MRDiffsFixture

%% Shared examples are reused across multiple test suites
SharedExamplesIssuesEE --> MRCloseServiceSpec
SharedExamplesEscalation --> MRConflictsResolveServiceSpec

%% Serializers/entities compose data shape for test invariants
MemberUserEntitySpec -.-> MRBaseServiceSpec
EnvironmentSerializerSpec -.-> MemberUserEntitySpec

%% Utilities used both for direct test init and for expectations/assertions
HelperProfiles --> HelperApp
HelperEvents --> HelperProfiles
HelperTimeboxes --> HelperEvents
HelperSnippets --> HelperApp
HelperNavDropdown --> HelperApp

%% Spec helper is used for all specs, provides base test config
SpecHelper --> MRBaseServiceSpec
SpecHelper --> MRPolicySpec
SpecHelper --> DoraMetricsRb
SpecHelper --> AnalyticsContribSpec
SpecHelper --> MemberUserEntitySpec
SpecHelper --> EnvironmentSerializerSpec

CrystalballEnv --> SpecHelper
ContractsSpecHelper --> SpecHelper

%% RSpec support utils orchestrate test database, locks, webmock, tmpdir, stubs
SetupBuilds -.-> SpecHelper
FinderCollection -.-> MRBaseServiceSpec
SidekiqSupport -.-> MRBaseServiceSpec
Locks -.-> MRBaseServiceSpec
Webmock -.-> SpecHelper
StackProf -.-> SpecHelper
Tmpdir -.-> RenameableUpload

%% --- Cross-group/integration relationships (natural test process links) ---
MRBaseServiceSpec -.-> HelperApp
MRPolicySpec -.-> HelperApp
HelperApp -.-> SpecHelper
SpecHelper -.-> StackProf

%% --- Relationships from data structure/fixture specs into the core test drivers
DoraMetricsRb --> MRBaseServiceSpec
AnalyticsContribSpec --> MRBaseServiceSpec
MRDiffsFixture --> MRBaseServiceSpec
MRFixture --> MRBaseServiceSpec
PipelinesFixture --> MRBaseServiceSpec
IssuesFixture --> MRBaseServiceSpec
RawFixture --> MRBaseServiceSpec

MemberUserEntitySpec --> EnvironmentSerializerSpec
IntegrationsFieldEntitySpec -.-> MRBaseServiceSpec
MergeRequestDiffEntitySpec -.-> MRBaseServiceSpec

%% --- Color legend (not rendered, for auditing color scheme) ---
%% class core, domainfile core logic/specs
%% class utilfile, utility, helper/init/support
%% class datafile, structfile, ds, dataset, fixtures/data
%% class errorfile, error/exception/edge-case specs
%% class initfile, initnode, initialization/boot

%% --- LAST: Group headers for easy context ---
UNIT_INTEGRATION_SPECS -. Implements Key Use Cases .- MR_POLICIES
UNIT_INTEGRATION_SPECS -. Uses Fixtures .- DOMAIN_FIXTURES
UNIT_INTEGRATION_SPECS -. Uses Helpers .- SHARED_EXAMPLES
UNIT_INTEGRATION_SPECS -. Uses Utilities .- RSPEC_SUPPORT_UTILS
UNIT_INTEGRATION_SPECS -. Uses UI Helper APIs .- UI_HELPERS
UNIT_INTEGRATION_SPECS -. Validates Domain Serialization .- ENTITY_SERIALIZER
INIT_SETUP -. Orchestrates All Test Setup .- UNIT_INTEGRATION_SPECS

%% End of diagram
```