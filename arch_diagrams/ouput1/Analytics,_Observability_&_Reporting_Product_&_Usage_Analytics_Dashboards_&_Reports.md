```mermaid
flowchart TB
  %% Domain: Analytics, Observability & Reporting/Product & Usage Analytics/Dashboards & Reports
  %% Color Codes: 
  %%   Core domain files: pastel blue (#D4F1F9)
  %%   Supporting/utility files: pastel yellow (#FFF8DC)
  %%   Data structures: pastel green (#E0F8E0)
  %%   Error handling: pastel red (#FFE4E1)
  %%   Initialization/setup: pastel purple (#E6E6FA)
  %%   Subgraphs: very light gray (#F8F8F8) w/ pastel borders

  %% --- DASHBOARDS & VISUALIZATIONS LOGIC ---

  subgraph dashboards_main[Dashboards, Visualizations & Panels]
    direction TB
    style dashboards_main fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

    dash_b[ee/app/models/product_analytics/dashboard.rb]:::coreFile
    visual[ee/app/models/product_analytics/visualization.rb]:::coreFile
    panel[ee/app/models/product_analytics/panel.rb]:::coreFile
    funnel[ee/app/models/product_analytics/funnel.rb]:::coreFile
    funnel_step[ee/app/models/product_analytics/funnel_step.rb]:::coreFile

    enums_palette[app/models/concerns/enums/data_visualization_palette.rb]:::dataStruct

    dash_b --> panel
    panel --> visual
    dash_b --> visual
    visual --> enums_palette
    funnel --> funnel_step
    dash_b --> funnel
    funnel --uses steps--> funnel_step
  end

  subgraph dashboards_gql[Dashboards & Visualizations GraphQL Types]
    direction TB
    style dashboards_gql fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

    gql_dashboard_type[ee/app/graphql/types/product_analytics/dashboard_type.rb]:::dataStruct
    gql_panel_type[ee/app/graphql/types/product_analytics/panel_type.rb]:::dataStruct
    gql_visual_type[ee/app/graphql/types/product_analytics/visualization_type.rb]:::dataStruct
    gql_monthly_usage_type[ee/app/graphql/types/product_analytics/monthly_usage_type.rb]:::dataStruct

    gql_dashboard_type --> gql_panel_type
    gql_panel_type --> gql_visual_type
    gql_visual_type --> visual
    gql_dashboard_type --> dash_b
    gql_panel_type --> panel
    gql_monthly_usage_type
  end

  subgraph dashboards_policy[Permissions]
    direction TB
    style dashboards_policy fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

    dashboard_policy[ee/app/policies/product_analytics/dashboard_policy.rb]:::supportFile
    dashboard_policy --> dash_b
  end

  %% --- ANALYTICS DASHBOARDS UI + HELPERS ---

  subgraph dashboards_helpers[Analytics Dashboard Helpers]
    direction TB
    style dashboards_helpers fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

    dashboards_helper[ee/app/helpers/analytics/analytics_dashboards_helper.rb]:::supportFile
    dashboards_helper --> dash_b
    dashboards_helper --> panel
    dashboards_helper --> visual
    dashboards_helper --> enums_palette
  end

  %% --- GRAPHQL: RESOLVERS FOR ANALYTICS DATA ---

  subgraph dashboards_gqlres[GraphQL Data Resolvers]
    direction TB
    style dashboards_gqlres fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

    dqldr[ee/app/graphql/resolvers/product_analytics/dashboards_resolver.rb]:::coreFile
    vqldr[ee/app/graphql/resolvers/product_analytics/visualization_resolver.rb]:::coreFile
    vqls[ee/app/graphql/resolvers/product_analytics/visualizations_resolver.rb]:::coreFile
    pa_proj_settings_r[ee/app/graphql/resolvers/analytics/product_analytics/project_settings_resolver.rb]:::coreFile
    gql_proj_settings_type[ee/app/graphql/types/analytics/product_analytics/product_analytics_project_settings_type.rb]:::dataStruct

    dqldr --> gql_dashboard_type
    dqldr --> dash_b
    dqldr --list panels--> panel

    vqldr --> gql_visual_type
    vqldr --> visual

    vqls --> gql_visual_type
    vqls --> visual

    pa_proj_settings_r --> gql_proj_settings_type
  end

  %% --- DATA QUERY, TRANSFORMATION, CUBE JS ---

  subgraph cube_analytics[Data Query & Transformation]
    direction TB
    style cube_analytics fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2,rounded-rectangle

    cube_service[ee/app/services/product_analytics/cube_data_query_service.rb]:::coreFile
    cube_transform[ee/lib/gitlab/cube_js/data_transformer.rb]:::dataStruct

    cube_service --queries & transforms--> cube_transform
    cube_service --> dash_b
    cube_service --> visual
    cube_service --validates config--> pa_proj_settings_r
  end

  %% --- API CONTROLLERS (REST & GQL) ---

  subgraph api_endpoints[API Endpoints]
    direction TB
    style api_endpoints fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

    api_pa[ee/lib/api/analytics/product_analytics.rb]:::coreFile
    api_pa --> cube_service
    api_pa --> dash_b

    gql_dashboard_type
    dqldr

    api_pa --exposes dashboards--> dash_b
    api_pa --products analytics data--> cube_transform
  end

  %% --- INSIGHTS (ANALYTICS REPORTING) ---

  subgraph insights[Insights & Reporting]
    direction TB
    style insights fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

    insight[ee/app/models/insight.rb]:::coreFile
    insights_feature[ee/app/models/concerns/insights_feature.rb]:::supportFile
    insights_ctrl[ee/app/controllers/groups/insights_controller.rb]:::coreFile
    insights_proj_ctrl[ee/app/controllers/projects/insights_controller.rb]:::coreFile

    insight --loads config from--> insights_feature
    insights_ctrl --> insight
    insights_proj_ctrl --> insight
  end

  %% --- SUPPORTING: REDUCERS & AUDIT ---

  subgraph core_supporting[Core Supporting Structures]
    direction TB
    style core_supporting fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,rounded-rectangle

    count_reducer[ee/lib/gitlab/insights/reducers/count_per_period_reducer.rb]:::supportFile
    pa_auditor[ee/lib/analytics/project_analytics_changes_auditor.rb]:::supportFile
    items_collection[ee/lib/gitlab/items_collection.rb]:::supportFile

    count_reducer --aggregates for insights--> insight
    pa_auditor --audits config changes--> pa_proj_settings_r
    items_collection --supports aggregation--> count_reducer

    cube_service --> count_reducer
  end

  %% --- USAGE AND INSTRUMENTATION (METRICS, EVENTS) ---

  subgraph usage_metrics[Usage Instrumentation & Metrics]
    direction TB
    style usage_metrics fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

    usage_service_ping[lib/gitlab/usage/service_ping_report.rb]:::supportFile
    metric_def[lib/gitlab/usage/metric_definition.rb]:::supportFile
    tcm[lib/gitlab/usage/metrics/instrumentations/total_count_metric.rb]:::supportFile
    tsm[lib/gitlab/usage/metrics/instrumentations/total_sum_metric.rb]:::supportFile
    bbmm[lib/gitlab/usage/metrics/instrumentations/batched_background_migrations_metric.rb]:::supportFile
    bbmfm[lib/gitlab/usage/metrics/instrumentations/batched_background_migration_failed_jobs_metric.rb]:::supportFile
    cbiem[lib/gitlab/usage/metrics/instrumentations/count_bulk_imports_entities_metric.rb]:::supportFile

    usage_service_ping --> metric_def
    metric_def --> tcm
    metric_def --> tsm
    metric_def --> bbmm
    metric_def --> bbmfm
    metric_def --> cbiem
    usage_service_ping --compiles analytics data--> cube_service
  end

  %% --- SUPPORTING: HELPERS, UTILS, SETUP, ERROR HANDLING---

  subgraph helpers_utils[Utilities, Helpers & Data Structures]
    direction TB
    style helpers_utils fill:#F8F8F8,stroke:#FFF8DC,stroke-width:2,rounded-rectangle

    graph_help[app/helpers/graph_helper.rb]:::supportFile
    obs_helper[ee/app/helpers/projects/observability_helper.rb]:::supportFile
    obs_mod[lib/gitlab/observability.rb]:::supportFile
    err_msg[lib/gitlab/utils/error_message.rb]:::errorFile
    
    graph_help --renders graphs for dashboards--> dash_b
    obs_helper --observability presentation--> dash_b
    obs_mod --exposes obs URL--> obs_helper
  end

  %% --- SEEDERS, QA, TESTS & EXTRAS, not core implementation but relevant for setup/tests ---

  subgraph init_setup[Domain Initialization & Setup]
    direction TB
    style init_setup fill:#F8F8F8,stroke:#E6E6FA,stroke-width:2,rounded-rectangle

    seed_dash[db/fixtures/development/38_ci_cd_analytics.rb]:::initFile
    seed_vsd[ee/db/fixtures/development/93_vsd_overview_counts.rb]:::initFile
    seed_proj[db/fixtures/development/03_project.rb]:::initFile

    qa_dash_setup[qa/qa/ee/page/project/analyze/analytics_dashboards/setup.rb]:::initFile
    qa_dash_home[qa/qa/ee/page/project/analyze/analytics_dashboards/home.rb]:::initFile
    qa_insights_show[qa/qa/ee/page/insights/show.rb]:::initFile

    seed_dash --> dash_b
    qa_dash_setup --> dashboards_helper
    qa_dash_home --> dashboards_helper
    qa_insights_show --> insights_ctrl
    seed_proj --init projects--> insight
  end

  %% --- CONTROLLERS (For Dashboards & Reports) ---

  subgraph controllers[Controllers Dashboards & Reports]
    direction TB
    style controllers fill:#F8F8F8,stroke:#D4F1F9,stroke-width:2,rounded-rectangle

    g_analytics_appctrl[ee/app/controllers/groups/analytics/application_controller.rb]:::supportFile
    g_coverage_ctrl[ee/app/controllers/groups/analytics/coverage_reports_controller.rb]:::coreFile
    g_taskstype_ctrl[ee/app/controllers/groups/analytics/tasks_by_type_controller.rb]:::coreFile
    g_repoanalytics_ctrl[ee/app/controllers/groups/analytics/repository_analytics_controller.rb]:::coreFile

    g_analytics_appctrl --base for group analytics--> g_coverage_ctrl
    g_analytics_appctrl --> g_taskstype_ctrl
    g_analytics_appctrl --> g_repoanalytics_ctrl

    g_coverage_ctrl --renders group code coverage reports--> dash_b
    g_taskstype_ctrl --tasks-by-type analytics--> dash_b
    g_repoanalytics_ctrl --repo usage analytics--> dash_b
  end

  %% --- WORKERS (Metrics, ETL) ---

  subgraph metrics_workers[Data Processing Workers]
    direction TB
    style metrics_workers fill:#F8F8F8,stroke:#E0F8E0,stroke-width:2,rounded-rectangle

    global_metrics[app/workers/metrics/global_metrics_update_worker.rb]:::supportFile
    patched_files[app/workers/metrics/patched_files_worker.rb]:::supportFile

    global_metrics --aggregates system usage--> dash_b
    patched_files --processes file metrics--> dash_b
  end

  %% --- DOMAIN CONCEPTS & INTERACTION FLOW ---

  %% Dashboards are ENTRYPOINT structures, combine Panels, which aggregate Visualizations, each relying on Color Palettes and Cube Data Query for their charts.
  %% REST & GraphQL APIs surface dashboards, panels, visualizations, usage, and analytics to clients.
  %% CubeDataQueryService executes analytic queries and transforms sourced data.
  %% Helpers, policies, and supporting structures facilitate access control, data composition, UI, and aggregation.
  %% Insights and Reporting leverage Reducers and configuration for visual reporting.
  %% Usage/Instrumentations track event and metric usage across analytics features for reporting visibility.

  %% --- CONNECTIONS (INTER-GROUPS) ---

  %% Dashboards Main Collaboration
  dashboards_main --> dashboards_gql
  dashboards_main --> dashboards_gqlres
  dashboards_main --> dashboards_helpers
  dashboards_main --> controllers
  dashboards_main --> core_supporting
  dashboards_main --> api_endpoints
  dashboards_main --> cube_analytics
  dashboards_main --> metrics_workers

  %% Insight system connects to Dashboards for rendering analytics
  insights --> dashboards_main

  %% Helpers/utils support both dashboards and insights/reporting
  helpers_utils --> dashboards_main
  helpers_utils --> insights

  %% Audit flows through supporting structures to config types and dashboards
  core_supporting --> dashboards_main
  core_supporting --> insights

  %% Instrumentation/metrics surface up as reporting for analytics dashboards
  usage_metrics --> api_endpoints

  %% Seeders, QA, and setup files enable/automate initial configuration, dashboard definition, and QA flows for Dashboards and Insights
  init_setup --> dashboards_main
  init_setup --> insights
  init_setup --> controllers

  %% Coloring
  classDef coreFile fill:#D4F1F9,color:#222,stroke:#A7CCE1,stroke-width:1,rounded-rectangle;
  classDef dataStruct fill:#E0F8E0,color:#111,stroke:#97E4B1,stroke-width:1,rounded-rectangle;
  classDef supportFile fill:#FFF8DC,color:#333,stroke:#F7ECA6,stroke-width:1,rounded-rectangle;
  classDef initFile fill:#E6E6FA,color:#222,stroke:#A69FC2,stroke-width:1,rounded-rectangle;
  classDef errorFile fill:#FFE4E1,color:#950E19,stroke:#FFB6B6,stroke-width:1,rounded-rectangle;
```