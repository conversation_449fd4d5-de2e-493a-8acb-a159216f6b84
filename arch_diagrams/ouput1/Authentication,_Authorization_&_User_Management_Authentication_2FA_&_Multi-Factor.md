```mermaid
flowchart TB
  %% Styles
  %% Colors
  %% Core domain files: pastel blue (#D4F1F9)
  %% Supporting/utility files: pastel yellow (#FFF8DC)
  %% Data structure files: pastel green (#E0F8E0)
  %% Error handling files: pastel red (#FFE4E1)
  %% Initialization/setup files: pastel purple (#E6E6FA)
  %% Logical groupings/subgraphs: very light gray (#F8F8F8)

  %% Default node style
  classDef core fill:#D4F1F9,stroke:#6CB3CF,stroke-width:2px,rx:8,ry:8;
  classDef support fill:#FFF8DC,stroke:#E9DCA4,stroke-width:2px,rx:8,ry:8;
  classDef data fill:#E0F8E0,stroke:#77CE91,stroke-width:2px,rx:8,ry:8;
  classDef error fill:#FFE4E1,stroke:#F7A4A4,stroke-width:2px,rx:8,ry:8;
  classDef init fill:#E6E6FA,stroke:#B39EB5,stroke-width:2px,rx:8,ry:8;
  classDef gray fill:#F8F8F8,stroke:#D6D6D6,stroke-width:2px;

  %% Top-level grouping for the domain
  subgraph authentication_authorization_2fa_mfa ["Authentication, Authorization & User Management / Authentication / 2FA & Multi-Factor"]
  direction TB

    %% USER AUTH DATA STRUCTURE
    subgraph user_data_model["User & Device Data Models"]
    direction TB
      webauthn_registration["WebauthnRegistration<br>WebAuthn credential store":::data]
      password_complexity["PasswordComplexity<br>Password policy validation":::support]
      user_phone_validation["Users::PhoneNumberValidation<br>Phone number record implied by phone services":::data]
      feature_gate["FeatureGate<br>Feature flags / license gating":::support]
    end
    class user_data_model gray;

    %% AUTHENTICATION CONTROLLERS & CONCERNS
    subgraph controllers["Controllers & Routing Logic"]
    direction TB
      passwords_controller["UserSettings::PasswordsController<br>Password change, 2FA triggers":::core]
      concerns_enforces_2fa["EnforcesTwoFactorAuthentication<br>Require/enforce 2FA for access":::core]
      concerns_auth_with_2fa["AuthenticatesWithTwoFactor<br>2FA prompt and authentication logic":::core]
      concerns_auth_with_2fa_admin["AuthenticatesWithTwoFactorForAdminMode<br>Admin mode 2FA requirements":::core]
      concerns_enforces_2fa_ee["EE::EnforcesTwoFactorAuthentication<br>Smartcard/EE changes for 2FA requirement":::core]
      concerns_auth_with_2fa_ee["EE::AuthenticatesWithTwoFactor<br>EE audit enhancements for 2FA":::core]
      concerns_enforces_stepup_auth["EnforcesStepUpAuthentication<br>Step-up prompt for sensitive actions":::core]
      phone_telesign_callbacks["PhoneVerification::TelesignCallbacksController<br>Handle TeleSign verification callbacks":::core]
    end
    class controllers gray;

    %% SERVICES - 2FA CORE OPERATIONS GROUP
    subgraph two_factor_services["2FA Authentication/Management Services"]
    direction TB
      two_factor_base_service["TwoFactor::BaseService<br>Base for all 2FA services":::support]
      two_factor_destroy_service["TwoFactor::DestroyService<br>Disables all 2FA for a user":::core]
      two_factor_destroy_otp_service["TwoFactor::DestroyOtpService<br>Disables only OTP for a user":::core]
      two_factor_destroy_service_ee["EE::TwoFactor::DestroyService<br>Group/user-specific EE logic":::core]
    end
    class two_factor_services gray;

    %% SERVICES - TOTP/OTP AND PROVIDERS
    subgraph otp_services["TOTP/OTP Validation Services"]
    direction TB
      users_validate_push_otp["Users::ValidatePushOtpService<br>Push-based OTP validation":::core]
      users_validate_manual_otp["Users::ValidateManualOtpService<br>Manual OTP from user/DUO/Forti":::core]
    end
    class otp_services gray;

    %% SERVICES - PHONE MFA
    subgraph phone_verification["Phone Verification and TeleSign"]
    direction TB
      phone_send_code["PhoneVerification::TelesignClient::SendVerificationCodeService<br>Send SMS codes API":::core]
      phone_verify_code["PhoneVerification::TelesignClient::VerifyCodeService<br>Validate user input code API":::core]
      phone_risk_score["PhoneVerification::TelesignClient::RiskScoreService<br>Assess phone risk API":::support]
      phone_users_send_code["PhoneVerification::Users::SendVerificationCodeService<br>Wrap for app user - send":::core]
      phone_users_verify_code["PhoneVerification::Users::VerifyCodeService<br>Wrap for app user - verify":::core]
      phone_users_record_user["PhoneVerification::Users::RecordUserDataService<br>Update user/risk metadata after phone verification":::support]
    end
    class phone_verification gray;

    %% SERVICES - WEBAUTHN
    subgraph webauthn_services["WebAuthn Device Registration & Auth"]
    direction TB
      webauthn_register_service["Webauthn::RegisterService<br>Create new WebAuthn credentials":::core]
      webauthn_auth_service["Webauthn::AuthenticateService<br>Validate credential on login":::core]
      webauthn_destroy_service["Webauthn::DestroyService<br>Remove a registered device":::core]
    end
    class webauthn_services gray;

    %% SUPPORTING & HELPER MODULES
    subgraph helpers_support["Helpers & Utility Modules"]
    direction TB
      device_registration_helper["DeviceRegistrationHelper<br>Data for frontend device registration":::support]
      ee_users_callouts_helper["EE::Users::CalloutsHelper<br>Show user banners for recovery, DUO, 2FA prompts":::support]
      arkose_verify_response["Arkose::VerifyResponse<br>Risk-band for CAPTCHA/step-up":::support]
      smartcard_session_enforcer["Gitlab::Auth::Smartcard::SessionEnforcer<br>Session logic for smartcard":::support]
      otp_session_enforcer["Gitlab::Auth::Otp::SessionEnforcer<br>Session logic for OTP":::support]
    end
    class helpers_support gray;

    %% LIBS - AUTH ABSTRACTIONS & STRATEGIES
    subgraph auth_infra["Auth Core Abstractions & Pluggable Strategies"]
    direction TB
      two_factor_auth_verifier["Gitlab::Auth::TwoFactorAuthVerifier<br>Central 2FA enforcement logic":::core]
      combined_2fa_devise_strategy["Gitlab::Auth::Devise::Strategies::CombinedTwoFactorAuthenticatable<br>Devise 2FA support":::support]
      otp_fortinet["Gitlab::Auth::Otp::Fortinet<br>Provider/truth for Forti strategies":::support]
      otp_duo_auth["Gitlab::Auth::Otp::DuoAuth<br>Provider/truth for Duo strategies":::support]
      otp_strat_forti_push["Gitlab::Auth::Otp::Strategies::FortiAuthenticator::PushOtp<br>Push OTP handling":::core]
      otp_strat_forti_manual["Gitlab::Auth::Otp::Strategies::FortiAuthenticator::ManualOtp<br>manual OTP":::core]
      otp_strat_forti_tokencloud["Gitlab::Auth::Otp::Strategies::FortiTokenCloud<br>cloud-based OTP":::core]
      otp_strat_devise["Gitlab::Auth::Otp::Strategies::Devise<br>Devise-based OTP":::core]
      otp_strat_duo_manual["Gitlab::Auth::Otp::Strategies::DuoAuth::ManualOtp<br>Duo manual OTP":::core]
    end
    class auth_infra gray;

    %% VENDOR GEM: BACKUP CODES, PBKDF2 EXT
    subgraph backup_codes["Backup Codes Management"]
    direction TB
      two_factor_backup_pbkdf2["devise-pbkdf2-encryptable: TwoFactorBackupablePbkdf2<br>Generate/store/invalidate backup codes":::support]
    end
    class backup_codes gray;

    %% TESTS & INTEGRATION
    subgraph tests["Spec & QA: Integration and Feature Coverage"]
    direction TB
      admin_disables_2fa_spec["spec/features/admin/admin_disables_two_factor_spec.rb<br>Admin disables 2FA":::support]
      admin_sessions_controller_spec["spec/controllers/admin/sessions_controller_spec.rb<br>2FA admin login flows":::support]
      shared_examples_2fa["spec/support/shared_examples/features/2fa_shared_examples.rb<br>Reusable 2FA hardware scenarios":::support]
      forti_push_otp_spec["spec/lib/gitlab/auth/otp/strategies/forti_authenticator/push_otp_spec.rb":::support]
      duo_manual_otp_spec["spec/lib/gitlab/auth/otp/strategies/duo_auth/manual_otp_spec.rb":::support]
      two_factor_backup_pbkdf2_spec["vendor/gems/devise-pbkdf2-encryptable/spec/lib/models/two_factor_backupable_pbkdf2_spec.rb":::support]
      qa_profile_2fa["qa/qa/page/profile/two_factor_auth.rb<br>UI integration: Profile 2FA":::support]
      qa_main_2fa["qa/qa/page/main/two_factor_auth.rb<br>UI integration: Main 2FA":::support]
    end
    class tests gray;

    %% RELATIONS: DATA -> SERVICES
    webauthn_registration -->|provides / stores credential| webauthn_register_service
    webauthn_registration -->|lookup/read for| webauthn_auth_service
    webauthn_registration -->|lookup for destroy| webauthn_destroy_service
    password_complexity -->|validates on| passwords_controller

    %% CONTROLLER-2FA SERVICE RELATIONS
    passwords_controller -->|initiates| two_factor_destroy_service
    passwords_controller -->|can trigger| two_factor_destroy_otp_service
    passwords_controller -->|enforces using| concerns_enforces_2fa

    concerns_enforces_2fa -->|requires 2FA via| concerns_auth_with_2fa
    concerns_auth_with_2fa -->|two factor prompt for| passwords_controller

    %% EE CONTROLLER CONCERNS
    concerns_enforces_2fa_ee -->|extends| concerns_enforces_2fa
    concerns_enforces_2fa_ee -->|delegates smartcard to| smartcard_session_enforcer
    concerns_auth_with_2fa_ee -->|extends & enhances| concerns_auth_with_2fa

    %% 2FA SERVICE LAYER: INHERITANCE/POLYMORPHISM
    two_factor_base_service <|-- two_factor_destroy_service
    two_factor_base_service <|-- two_factor_destroy_otp_service
    two_factor_destroy_service_ee -->|extends/overrides| two_factor_destroy_service

    %% 2FA SERVICE - BACKUP CODES
    two_factor_backup_pbkdf2 -->|manages backup codes for| two_factor_destroy_service
    two_factor_backup_pbkdf2 -->|manages backup codes for| combined_2fa_devise_strategy

    %% WEBAUTHN SERVICES & FLOW
    webauthn_register_service -->|creates model| webauthn_registration
    webauthn_auth_service -->|reads/verifies against| webauthn_registration
    webauthn_destroy_service -->|destroys| webauthn_registration
    webauthn_services --> device_registration_helper

    %% OTP VALIDATION & STRATEGY FLOW
    users_validate_push_otp -->|calls| otp_strat_forti_push
    users_validate_push_otp --> otp_fortinet
    users_validate_manual_otp -->|can call| otp_strat_forti_manual
    users_validate_manual_otp -->|can call| otp_strat_forti_tokencloud
    users_validate_manual_otp -->|can call| otp_strat_duo_manual
    users_validate_manual_otp --> otp_fortinet
    users_validate_manual_otp --> otp_duo_auth

    %% OTP strategies -> TOTP base
    otp_strat_forti_push --> otp_fortinet
    otp_strat_forti_manual --> otp_fortinet
    otp_strat_forti_tokencloud --> otp_fortinet
    otp_strat_duo_manual --> otp_duo_auth

    %% Service -> Verification hub
    users_validate_push_otp --> two_factor_auth_verifier
    users_validate_manual_otp --> two_factor_auth_verifier
    two_factor_auth_verifier --> concerns_enforces_2fa
    two_factor_auth_verifier --> concerns_enforces_2fa_ee

    %% Combined Devise strategy
    combined_2fa_devise_strategy --> otp_strat_devise
    combined_2fa_devise_strategy --> two_factor_backup_pbkdf2
    combined_2fa_devise_strategy -->|delegates core verify to| two_factor_auth_verifier

    %% PHONE MFA verification workflow
    passwords_controller -->|may require| phone_users_send_code
    phone_users_send_code --> phone_send_code
    phone_users_send_code --> phone_risk_score
    phone_users_send_code --> phone_users_record_user
    phone_users_verify_code --> phone_verify_code
    phone_users_verify_code --> phone_users_record_user
    phone_send_code --> phone_verify_code
    phone_users_record_user --> user_phone_validation

    phone_verify_code --> phone_users_verify_code

    phone_risk_score --> phone_users_record_user
    phone_risk_score --> phone_users_send_code

    %% TELEPHONE - CALLBACK HANDLING
    phone_telesign_callbacks -->|handles callbacks for| phone_send_code
    phone_telesign_callbacks --> phone_verify_code
    phone_telesign_callbacks --> phone_risk_score

    %% Admin mode special auth
    concerns_auth_with_2fa_admin -->|uses admin mode 2FA| concerns_auth_with_2fa
    concerns_auth_with_2fa_admin -->|called by admin flows in| admin_sessions_controller_spec

    %% Other support / feature-gating
    feature_gate -->|feature gating| password_complexity
    feature_gate -->|limits| concerns_enforces_2fa
    feature_gate -->|feature gating EE| concerns_enforces_2fa_ee
    feature_gate -->|controls availability| otp_strat_forti_push

    %% Device registration helper (web & UI)
    device_registration_helper -->|supplies init data| webauthn_register_service

    %% Helper integration & UI flow
    ee_users_callouts_helper -->|2FA banners| passwords_controller
    ee_users_callouts_helper -->|DUO banners| users_validate_manual_otp

    %% LG: CAPTCHA / Arkose
    arkose_verify_response -->|may be checked in| concerns_enforces_stepup_auth

    %% MFA session enforcers
    otp_session_enforcer -->|session validation| concerns_enforces_2fa
    smartcard_session_enforcer -->|session validation| concerns_enforces_2fa_ee

    %% TESTS: coverage and flows
    admin_disables_2fa_spec --> passwords_controller
    admin_disables_2fa_spec --> two_factor_destroy_service
    admin_disables_2fa_spec --> two_factor_destroy_otp_service

    admin_sessions_controller_spec --> concerns_auth_with_2fa_admin
    admin_sessions_controller_spec --> webauthn_auth_service
    shared_examples_2fa --> webauthn_register_service
    shared_examples_2fa --> webauthn_auth_service
    shared_examples_2fa --> users_validate_manual_otp
    shared_examples_2fa --> users_validate_push_otp

    qa_profile_2fa --> two_factor_services
    qa_profile_2fa --> webauthn_services
    qa_main_2fa --> two_factor_services
    qa_main_2fa --> webauthn_services

    forti_push_otp_spec --> otp_strat_forti_push
    duo_manual_otp_spec --> otp_strat_duo_manual
    two_factor_backup_pbkdf2_spec --> two_factor_backup_pbkdf2

  end

  %% GROUP node STYLING
  class authentication_authorization_2fa_mfa gray;

  %% INDIVIDUAL node roles
  class webauthn_registration,data;
  class password_complexity,support;
  class user_phone_validation,data;
  class feature_gate,support;

  class passwords_controller,core;
  class concerns_enforces_2fa,core;
  class concerns_auth_with_2fa,core;
  class concerns_auth_with_2fa_admin,core;
  class concerns_enforces_2fa_ee,core;
  class concerns_auth_with_2fa_ee,core;
  class concerns_enforces_stepup_auth,core;
  class phone_telesign_callbacks,core;

  class two_factor_base_service,support;
  class two_factor_destroy_service,core;
  class two_factor_destroy_otp_service,core;
  class two_factor_destroy_service_ee,core;

  class users_validate_push_otp,core;
  class users_validate_manual_otp,core;

  class phone_send_code,core;
  class phone_verify_code,core;
  class phone_risk_score,support;
  class phone_users_send_code,core;
  class phone_users_verify_code,core;
  class phone_users_record_user,support;

  class webauthn_register_service,core;
  class webauthn_auth_service,core;
  class webauthn_destroy_service,core;

  class device_registration_helper,support;
  class ee_users_callouts_helper,support;
  class arkose_verify_response,support;
  class smartcard_session_enforcer,support;
  class otp_session_enforcer,support;

  class two_factor_auth_verifier,core;
  class combined_2fa_devise_strategy,support;
  class otp_fortinet,support;
  class otp_duo_auth,support;
  class otp_strat_forti_push,core;
  class otp_strat_forti_manual,core;
  class otp_strat_forti_tokencloud,core;
  class otp_strat_devise,core;
  class otp_strat_duo_manual,core;

  class two_factor_backup_pbkdf2,support;

  class admin_disables_2fa_spec,support;
  class admin_sessions_controller_spec,support;
  class shared_examples_2fa,support;
  class forti_push_otp_spec,support;
  class duo_manual_otp_spec,support;
  class two_factor_backup_pbkdf2_spec,support;
  class qa_profile_2fa,support;
  class qa_main_2fa,support;
```