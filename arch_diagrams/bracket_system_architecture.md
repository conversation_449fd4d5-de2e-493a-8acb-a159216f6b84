# Bracket System Architecture

```mermaid
flowchart TB
    classDef userComponent fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef coreComponent fill:#e8f5e9,stroke:#2e7d32,stroke-width:2px
    classDef extComponent fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef cloudComponent fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px
    classDef externalComponent fill:#ffebee,stroke:#b71c1c,stroke-width:2px
    classDef dataComponent fill:#f5f5f5,stroke:#9e9e9e,stroke-width:2px
    classDef subgraph_style fill:#f5f5f5,stroke:#9e9e9e,stroke-width:1px
    
    %% User Components
    User[Developer/User]:::userComponent
    VSCode[Visual Studio Code]:::userComponent
    
    %% Bracket Extension Components
    subgraph BracketExt["Bracket Extension (bracket_ext)"]
        direction TB
        
        %% UI Layer
        subgraph UILayer["UI Layer"]
            direction TB
            ChatInterface[Chat Interface]:::extComponent
            SettingsInterface[Settings Interface]:::extComponent
            MermaidCompanion[Mermaid Companion View]:::extComponent
        end
        
        %% Core Extension Logic
        subgraph ExtCore["Core Extension Logic"]
            direction TB
            Cline[Cline System]:::extComponent
            ContextEngine[Context Engine]:::extComponent
            TerminalIntegration[Terminal Integration]:::extComponent
            EditorIntegration[Editor Integration]:::extComponent
        end
        
        %% API Layer
        subgraph APILayer["API Layer"]
            direction TB
            APIHandler[API Handler]:::extComponent
            ExportAPI[Export API]:::extComponent
        end
        
        %% Services
        subgraph ExtServices["Services"]
            direction TB
            MermaidService[Mermaid Service]:::extComponent
            McpService[MCP Service]:::extComponent
            TelemetryService[Telemetry Service]:::extComponent
            BrowserService[Browser Service]:::extComponent
        end
        
        %% Extension Data
        ExtensionState[Extension State]:::dataComponent
    end
    
    %% Bracket Core Components
    subgraph BracketCore["Bracket Core (bracket_core)"]
        direction TB
        
        %% IRL Pipeline
        subgraph IRLPipeline["IRL Pipeline"]
            direction TB
            RepoMapping[Repository Mapping]:::coreComponent
            KnowledgeGraph[Knowledge Graph Generation]:::coreComponent
            SemanticDoc[Semantic Documentation]:::coreComponent
            DomainAnalysis[Domain Analysis]:::coreComponent
            FileDomainMapping[File-Domain Mapping]:::coreComponent
            DomainFileRepomap[Domain-File Repomap]:::coreComponent
            DiagramGen[Diagram Generation]:::coreComponent
            TaxonomyGen[Taxonomy Generation]:::coreComponent
            CodebaseExplanation[Codebase Explanation]:::coreComponent
        end
        
        %% Microservices Architecture
        subgraph Microservices["Microservices Architecture"]
            direction TB
            OrchestratorService[Orchestrator Service]:::coreComponent
            RepoMapperService[Repository Mapper Service]:::coreComponent
            DomainAnalyzerService[Domain Analyzer Service]:::coreComponent
            FileDomainMapperService[File Domain Mapper Service]:::coreComponent
            DomainFileRepomapService[Domain File Repomap Service]:::coreComponent
            DiagramGeneratorService[Diagram Generator Service]:::coreComponent
        end
        
        %% Common Infrastructure
        CommonInfrastructure[Common Infrastructure Layer]:::coreComponent
        
        %% Core Artifacts
        subgraph CoreArtifacts["Core Artifacts"]
            direction TB
            DomainTaxonomy[Domain Taxonomy JSON]:::dataComponent
            MermaidDiagrams[Mermaid Diagrams]:::dataComponent
            CodebaseUnderstanding[Codebase Understanding]:::dataComponent
        end
    end
    
    %% Cloud Infrastructure
    subgraph CloudInfra["Cloud Infrastructure"]
        direction TB
        GKE[Google Kubernetes Engine]:::cloudComponent
        CloudStorage[Google Cloud Storage]:::cloudComponent
        Prometheus[Prometheus Metrics]:::cloudComponent
        Grafana[Grafana Dashboards]:::cloudComponent
    end
    
    %% External Services
    subgraph ExternalServices["External Services"]
        direction TB
        AnthropicAPI[Anthropic Claude API]:::externalComponent
        OpenAIAPI[OpenAI API]:::externalComponent
        BedrockAPI[AWS Bedrock API]:::externalComponent
        SourceControl[Source Control Systems]:::externalComponent
    end
    
    %% User Interactions
    User --> VSCode
    VSCode --> BracketExt
    
    %% Extension Internal Connections
    UILayer --> ExtCore
    ExtCore --> APILayer
    ExtCore --> ExtServices
    ExtCore --> ExtensionState
    MermaidCompanion --> MermaidService
    
    %% Extension to Core Connections
    ContextEngine -.-> CoreArtifacts
    MermaidService -.-> CoreArtifacts
    
    %% Core Internal Connections
    IRLPipeline --> CoreArtifacts
    Microservices --> CoreArtifacts
    Microservices -.-> CommonInfrastructure
    
    %% Core to Cloud Connections
    Microservices --> CloudInfra
    
    %% External Service Connections
    APILayer --> ExternalServices
    DomainAnalyzerService --> ExternalServices
    DiagramGeneratorService --> ExternalServices
    RepoMapperService --> SourceControl
    
    %% Deployment Options
    subgraph DeploymentOptions["Deployment Options"]
        direction TB
        LocalDeployment[Local Deployment]:::cloudComponent
        CloudDeployment[Cloud Deployment]:::cloudComponent
    end
    
    BracketCore --> DeploymentOptions
```

## Bracket System Architecture: Detailed Explanation

The Bracket system consists of two main components: Bracket Core (bracket_core) and Bracket Extension (bracket_ext). These components work together to provide a comprehensive solution for codebase understanding and AI-assisted development.

### 1. User Interaction Layer

**Components**:
- **Developer/User**: The end user of the system
- **Visual Studio Code**: The IDE where the extension runs

**Interaction Flow**:
- Users interact with VSCode
- VSCode hosts the Bracket Extension
- The extension provides the interface for users to interact with the system

### 2. Bracket Extension (bracket_ext)

The Bracket Extension is a TypeScript-based VSCode extension that serves as the product layer of the system.

#### UI Layer

**Components**:
- **Chat Interface**: Allows users to interact with the AI assistant
- **Settings Interface**: Provides configuration options
- **Mermaid Companion View**: Displays Mermaid diagrams as users navigate through code

**Responsibilities**:
- Providing a user-friendly interface
- Displaying AI responses
- Visualizing codebase structure

#### Core Extension Logic

**Components**:
- **Cline System**: Handles AI interactions
- **Context Engine**: Provides context about the codebase
- **Terminal Integration**: Executes commands in the terminal
- **Editor Integration**: Interacts with the VSCode editor

**Responsibilities**:
- Processing user input
- Generating AI responses
- Executing commands
- Managing editor interactions

#### API Layer

**Components**:
- **API Handler**: Interfaces with AI providers
- **Export API**: Exposes functionality to other extensions

**Responsibilities**:
- Sending requests to AI providers
- Processing responses
- Exposing functionality to other extensions

#### Services

**Components**:
- **Mermaid Service**: Manages Mermaid diagrams
- **MCP Service**: Handles Multi-Context Processing
- **Telemetry Service**: Collects telemetry data
- **Browser Service**: Provides browser functionality

**Responsibilities**:
- Managing Mermaid diagrams
- Coordinating MCP operations
- Collecting telemetry data
- Providing browser functionality

#### Extension Data

**Components**:
- **Extension State**: Manages state for the extension

**Responsibilities**:
- Storing user preferences
- Managing conversation history
- Tracking extension state

### 3. Bracket Core (bracket_core)

Bracket Core is a Python-based backend that provides code analysis and domain modeling capabilities.

#### IRL Pipeline

**Components**:
- **Repository Mapping**: Extracts code structure
- **Knowledge Graph Generation**: Builds a graph representation
- **Semantic Documentation**: Adds semantic meaning
- **Domain Analysis**: Organizes code into domains
- **File-Domain Mapping**: Maps files to domains
- **Domain-File Repomap**: Creates a unified representation
- **Diagram Generation**: Creates visual diagrams
- **Taxonomy Generation**: Creates a unified JSON representation
- **Codebase Explanation**: Generates explanations

**Responsibilities**:
- Analyzing codebases
- Creating structured representations
- Generating visual diagrams
- Providing codebase explanations

#### Microservices Architecture

**Components**:
- **Orchestrator Service**: Coordinates the pipeline
- **Repository Mapper Service**: Extracts code structure
- **Domain Analyzer Service**: Identifies domains
- **File Domain Mapper Service**: Maps files to domains
- **Domain File Repomap Service**: Creates a unified representation
- **Diagram Generator Service**: Creates visual diagrams

**Responsibilities**:
- Providing scalable, cloud-ready services
- Coordinating the pipeline
- Processing large codebases efficiently

#### Common Infrastructure

**Components**:
- **Common Infrastructure Layer**: Provides shared utilities

**Responsibilities**:
- Providing shared utilities
- Ensuring consistent interfaces
- Reducing code duplication

#### Core Artifacts

**Components**:
- **Domain Taxonomy JSON**: Represents the domain taxonomy
- **Mermaid Diagrams**: Visual representations of domains
- **Codebase Understanding**: Comprehensive understanding of the codebase

**Responsibilities**:
- Storing structured representations
- Providing visual diagrams
- Enabling codebase understanding

### 4. Cloud Infrastructure

**Components**:
- **Google Kubernetes Engine**: Hosts the microservices
- **Google Cloud Storage**: Stores artifacts
- **Prometheus Metrics**: Collects metrics
- **Grafana Dashboards**: Visualizes metrics

**Responsibilities**:
- Hosting the microservices
- Storing artifacts
- Collecting and visualizing metrics

### 5. External Services

**Components**:
- **Anthropic Claude API**: Provides LLM capabilities
- **OpenAI API**: Provides LLM capabilities
- **AWS Bedrock API**: Provides LLM capabilities
- **Source Control Systems**: Provides access to code repositories

**Responsibilities**:
- Providing AI capabilities
- Providing access to code repositories

### 6. Deployment Options

**Components**:
- **Local Deployment**: Runs the system locally
- **Cloud Deployment**: Runs the system in the cloud

**Responsibilities**:
- Providing deployment flexibility
- Enabling scalability

## System Integration

The Bracket system integrates its components through the following mechanisms:

1. **Extension to Core Integration**:
   - The Context Engine in the extension uses the Core Artifacts
   - The Mermaid Service in the extension displays diagrams from Core Artifacts

2. **Core to Cloud Integration**:
   - The Microservices Architecture is deployed on GKE
   - Artifacts are stored in Cloud Storage
   - Metrics are collected by Prometheus and visualized in Grafana

3. **External Service Integration**:
   - The API Layer in the extension interfaces with AI providers
   - The Domain Analyzer and Diagram Generator services use LLMs
   - The Repository Mapper service interfaces with source control systems

## Data Flow

1. **Codebase Analysis Flow**:
   - Source code is analyzed by the IRL Pipeline
   - The pipeline generates Core Artifacts
   - The artifacts are used by the extension to provide context and visualizations

2. **User Interaction Flow**:
   - User interacts with the Chat Interface
   - The Cline System processes the interaction
   - The API Handler sends requests to AI providers
   - The response is processed and displayed to the user

3. **Mermaid Companion Flow**:
   - User navigates through code
   - The Mermaid Service loads diagrams from Core Artifacts
   - The Mermaid Companion View displays the diagrams

## Key Innovations

1. **Cognitive Mental Model**: The system creates a comprehensive mental model of codebases.
2. **Hierarchical Domain Structure**: Code is organized into logical domains and hierarchies.
3. **Visual Diagram Generation**: AI-generated diagrams provide visual representations of domains.
4. **Logic-Layered Reasoning**: The system enables reasoning at different levels of abstraction.
5. **Microservices Architecture**: The scalable architecture handles large codebases efficiently.
6. **VSCode Integration**: Seamless integration with VSCode provides a familiar development environment.

This architecture enables Bracket to provide a comprehensive solution for codebase understanding and AI-assisted development, with a focus on scalability, flexibility, and user experience.
