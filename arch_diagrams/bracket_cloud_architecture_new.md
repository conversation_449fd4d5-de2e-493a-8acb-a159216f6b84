# Bracket Cloud Architecture

```mermaid
flowchart TB
    classDef infrastructureComponent fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef computeComponent fill:#e8f5e9,stroke:#2e7d32,stroke-width:2px
    classDef storageComponent fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef networkComponent fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px
    classDef securityComponent fill:#ffebee,stroke:#b71c1c,stroke-width:2px
    classDef monitoringComponent fill:#f5f5f5,stroke:#9e9e9e,stroke-width:2px
    classDef subgraph_style fill:#f5f5f5,stroke:#9e9e9e,stroke-width:1px
    
    %% GCP Project Structure
    subgraph GCPProject["GCP Project (bracket-irl-prod)"]
        direction TB
        
        %% Networking
        subgraph Networking["Networking"]
            direction TB
            VPC[Virtual Private Cloud]:::networkComponent
            Subnets[Subnets]:::networkComponent
            CloudNAT[Cloud NAT]:::networkComponent
            LoadBalancer[Cloud Load Balancer]:::networkComponent
            
            VPC --> Subnets
            Subnets --> CloudNAT
            Subnets --> LoadBalancer
        end
        
        %% GKE Cluster
        subgraph GKECluster["GKE Cluster (Regional)"]
            direction TB
            
            %% Node Pools
            subgraph NodePools["Node Pools"]
                direction TB
                DefaultPool[Default Pool\ne2-standard-4]:::computeComponent
                HighMemPool[High Memory Pool\ne2-highmem-8]:::computeComponent
                GPUPool[GPU Pool\nn1-standard-8 + GPU]:::computeComponent
                
                DefaultPool --- HighMemPool
                HighMemPool --- GPUPool
            end
            
            %% Kubernetes Resources
            subgraph K8sResources["Kubernetes Resources"]
                direction TB
                
                %% Namespaces
                subgraph Namespaces["Namespaces"]
                    direction TB
                    BracketIRL[bracket-irl]:::infrastructureComponent
                    Monitoring[monitoring]:::infrastructureComponent
                    
                    BracketIRL --- Monitoring
                end
                
                %% Workloads
                subgraph Workloads["Workloads"]
                    direction TB
                    Deployments[Deployments]:::computeComponent
                    StatefulSets[StatefulSets]:::computeComponent
                    Jobs[Jobs]:::computeComponent
                    CronJobs[CronJobs]:::computeComponent
                    
                    Deployments --- StatefulSets
                    StatefulSets --- Jobs
                    Jobs --- CronJobs
                end
                
                %% Services
                subgraph Services["Services"]
                    direction TB
                    ClusterIP[ClusterIP Services]:::networkComponent
                    NodePort[NodePort Services]:::networkComponent
                    LoadBalancerSvc[LoadBalancer Services]:::networkComponent
                    
                    ClusterIP --- NodePort
                    NodePort --- LoadBalancerSvc
                end
                
                %% Config & Storage
                subgraph ConfigStorage["Config & Storage"]
                    direction TB
                    ConfigMaps[ConfigMaps]:::infrastructureComponent
                    Secrets[Secrets]:::securityComponent
                    PVCs[Persistent Volume Claims]:::storageComponent
                    
                    ConfigMaps --- Secrets
                    Secrets --- PVCs
                end
                
                Namespaces --> Workloads
                Workloads --> Services
                Workloads --> ConfigStorage
            end
            
            %% Microservices
            subgraph Microservices["Bracket IRL Microservices"]
                direction TB
                Orchestrator[Orchestrator Service]:::computeComponent
                RepoMapper[Repository Mapper Service]:::computeComponent
                DomainAnalyzer[Domain Analyzer Service]:::computeComponent
                FileDomainMapper[File-Domain Mapper Service]:::computeComponent
                DomainFileRepomap[Domain-File Repomap Service]:::computeComponent
                DiagramGenerator[Diagram Generator Service]:::computeComponent
                TaxonomyGenerator[Taxonomy Generator Service]:::computeComponent
                ExplanationGenerator[Explanation Generator Service]:::computeComponent
                
                Orchestrator --> RepoMapper
                RepoMapper --> DomainAnalyzer
                DomainAnalyzer --> FileDomainMapper
                FileDomainMapper --> DomainFileRepomap
                DomainFileRepomap --> DiagramGenerator
                DomainFileRepomap --> TaxonomyGenerator
                TaxonomyGenerator --> ExplanationGenerator
            end
            
            NodePools --> K8sResources
            K8sResources --> Microservices
        end
        
        %% Storage
        subgraph Storage["Storage"]
            direction TB
            
            %% Cloud Storage
            subgraph CloudStorage["Cloud Storage"]
                direction TB
                ArtifactBucket[bracket-irl-artifacts]:::storageComponent
                BackupBucket[bracket-irl-backups]:::storageComponent
                LogsBucket[bracket-irl-logs]:::storageComponent
                
                ArtifactBucket --- BackupBucket
                BackupBucket --- LogsBucket
            end
            
            %% Cloud SQL
            subgraph CloudSQL["Cloud SQL"]
                direction TB
                PostgreSQL[PostgreSQL Instance]:::storageComponent
                ReadReplicas[Read Replicas]:::storageComponent
                
                PostgreSQL --> ReadReplicas
            end
            
            CloudStorage --- CloudSQL
        end
        
        %% Container Registry
        subgraph ContainerRegistry["Artifact Registry"]
            direction TB
            DockerRepo[Docker Repository]:::storageComponent
            ImageVersioning[Image Versioning]:::infrastructureComponent
            
            DockerRepo --> ImageVersioning
        end
        
        %% Security
        subgraph Security["Security"]
            direction TB
            IAM[IAM & Service Accounts]:::securityComponent
            SecretManager[Secret Manager]:::securityComponent
            KMS[Key Management Service]:::securityComponent
            WorkloadIdentity[Workload Identity]:::securityComponent
            
            IAM --- SecretManager
            SecretManager --- KMS
            KMS --- WorkloadIdentity
        end
        
        %% Monitoring
        subgraph Monitoring["Monitoring & Logging"]
            direction TB
            CloudMonitoring[Cloud Monitoring]:::monitoringComponent
            CloudLogging[Cloud Logging]:::monitoringComponent
            Prometheus[Prometheus]:::monitoringComponent
            Grafana[Grafana]:::monitoringComponent
            
            CloudMonitoring --- CloudLogging
            Prometheus --- Grafana
            CloudMonitoring --> Prometheus
        end
        
        %% CI/CD
        subgraph CICD["CI/CD"]
            direction TB
            CloudBuild[Cloud Build]:::infrastructureComponent
            GitLabCI[GitLab CI/CD]:::infrastructureComponent
            
            CloudBuild --- GitLabCI
        end
        
        Networking --> GKECluster
        GKECluster --> Storage
        GKECluster --> ContainerRegistry
        GKECluster --> Security
        GKECluster --> Monitoring
        CICD --> GKECluster
        CICD --> ContainerRegistry
    end
    
    %% External Components
    subgraph External["External Components"]
        direction TB
        GitLab[GitLab]:::infrastructureComponent
        LLMProviders[LLM Providers\n(OpenAI, Anthropic, etc.)]:::infrastructureComponent
        Developers[Developers]:::infrastructureComponent
        
        GitLab --- LLMProviders
        LLMProviders --- Developers
    end
    
    %% Connections to External Components
    GitLab --> CICD
    LLMProviders --> Microservices
    Developers --> LoadBalancer
    LoadBalancer --> Services
```

## Bracket Cloud Architecture: Detailed Explanation

The Bracket Cloud Architecture is designed to provide a scalable, resilient, and secure platform for the Bracket IRL pipeline. This architecture leverages Google Cloud Platform (GCP) services, particularly Google Kubernetes Engine (GKE), to deploy and manage the microservices that comprise the IRL pipeline.

### GCP Project Structure

The Bracket IRL system is deployed within a dedicated GCP project (`bracket-irl-prod`) that contains all the necessary resources for the system.

### Networking

The networking layer provides connectivity and security for the Bracket IRL system:

- **Virtual Private Cloud (VPC)**: A private network that isolates the Bracket IRL resources
- **Subnets**: Subdivisions of the VPC network for different types of resources
- **Cloud NAT**: Provides outbound internet connectivity for resources without public IP addresses
- **Cloud Load Balancer**: Distributes incoming traffic to the appropriate services

### GKE Cluster

The GKE cluster is the core of the Bracket IRL system, providing a managed Kubernetes environment for deploying and scaling the microservices:

#### Node Pools

- **Default Pool**: General-purpose nodes (e2-standard-4) for most microservices
- **High Memory Pool**: Memory-optimized nodes (e2-highmem-8) for memory-intensive services like the Domain Analyzer
- **GPU Pool**: Nodes with GPUs for services that benefit from GPU acceleration

#### Kubernetes Resources

- **Namespaces**: Logical partitions of the cluster for different purposes
  - `bracket-irl`: Contains the Bracket IRL microservices
  - `monitoring`: Contains monitoring tools like Prometheus and Grafana

- **Workloads**: Kubernetes resources that manage the lifecycle of containers
  - **Deployments**: Long-running services with multiple replicas
  - **StatefulSets**: Services that require stable network identities and persistent storage
  - **Jobs**: One-time tasks, such as processing a specific codebase
  - **CronJobs**: Scheduled tasks, such as periodic maintenance or updates

- **Services**: Expose workloads to other services and external clients
  - **ClusterIP**: Internal-only services
  - **NodePort**: Services exposed on a specific port on each node
  - **LoadBalancer**: Services exposed through the Cloud Load Balancer

- **Config & Storage**: Resources for configuration and data persistence
  - **ConfigMaps**: Non-sensitive configuration data
  - **Secrets**: Sensitive configuration data, such as API keys
  - **Persistent Volume Claims**: Requests for persistent storage

#### Microservices

The Bracket IRL microservices are deployed as containers within the GKE cluster:

- **Orchestrator Service**: Coordinates the IRL pipeline and manages jobs
- **Repository Mapper Service**: Analyzes repositories and creates repository maps
- **Domain Analyzer Service**: Identifies domains and creates domain hierarchies
- **File-Domain Mapper Service**: Maps files to domains
- **Domain-File Repomap Service**: Creates unified representations of domains and files
- **Diagram Generator Service**: Generates Mermaid diagrams for domains
- **Taxonomy Generator Service**: Creates the domain taxonomy JSON
- **Explanation Generator Service**: Generates codebase explanations

### Storage

The storage layer provides persistent storage for the Bracket IRL system:

#### Cloud Storage

- **Artifact Bucket (`bracket-irl-artifacts`)**: Stores the artifacts generated by the IRL pipeline
- **Backup Bucket (`bracket-irl-backups`)**: Stores backups of important data
- **Logs Bucket (`bracket-irl-logs`)**: Stores logs for long-term retention and analysis

#### Cloud SQL

- **PostgreSQL Instance**: Stores structured data, such as job information and metadata
- **Read Replicas**: Provide read-only access to the database for improved performance

### Artifact Registry

The Artifact Registry stores and manages the Docker images used by the Bracket IRL system:

- **Docker Repository**: Stores Docker images for the microservices
- **Image Versioning**: Manages different versions of the images

### Security

The security layer ensures that the Bracket IRL system is secure and compliant:

- **IAM & Service Accounts**: Manage access control and permissions
- **Secret Manager**: Securely stores and manages sensitive information
- **Key Management Service (KMS)**: Manages encryption keys
- **Workload Identity**: Allows Kubernetes workloads to securely access GCP services

### Monitoring & Logging

The monitoring and logging layer provides visibility into the health and performance of the Bracket IRL system:

- **Cloud Monitoring**: Monitors the health and performance of GCP resources
- **Cloud Logging**: Collects and analyzes logs from GCP resources
- **Prometheus**: Collects metrics from Kubernetes workloads
- **Grafana**: Visualizes metrics and creates dashboards

### CI/CD

The CI/CD layer automates the build, test, and deployment of the Bracket IRL system:

- **Cloud Build**: Builds Docker images and runs tests
- **GitLab CI/CD**: Integrates with GitLab for continuous integration and deployment

### External Components

The Bracket IRL system interacts with several external components:

- **GitLab**: Provides source code repositories and CI/CD integration
- **LLM Providers**: Provide large language model capabilities for various stages of the IRL pipeline
- **Developers**: Use the Bracket IRL system through the Bracket Extension

## Best Practices Implemented

The Bracket Cloud Architecture implements several best practices for cloud-native applications:

### Infrastructure as Code (IaC)

All infrastructure is defined as code using tools like Terraform and Kubernetes manifests, enabling:

- **Reproducibility**: Infrastructure can be recreated consistently
- **Version Control**: Changes to infrastructure are tracked and reviewed
- **Automation**: Infrastructure can be deployed and updated automatically

### Microservices Architecture

The Bracket IRL system is decomposed into microservices, providing:

- **Scalability**: Services can be scaled independently based on demand
- **Resilience**: Failures in one service don't affect others
- **Flexibility**: Services can be developed, deployed, and updated independently

### Containerization

All components are containerized using Docker, enabling:

- **Consistency**: Applications run the same way in all environments
- **Isolation**: Applications are isolated from each other
- **Portability**: Applications can run on any platform that supports Docker

### Kubernetes Orchestration

Kubernetes is used to orchestrate containers, providing:

- **Automated Deployment**: Containers are deployed automatically
- **Self-healing**: Failed containers are automatically restarted
- **Horizontal Scaling**: Services can be scaled out by adding more replicas
- **Load Balancing**: Traffic is distributed across replicas
- **Rolling Updates**: Services can be updated without downtime

### Security Best Practices

The architecture implements several security best practices:

- **Least Privilege**: Services have only the permissions they need
- **Encryption**: Data is encrypted both in transit and at rest
- **Secret Management**: Sensitive information is stored securely
- **Network Isolation**: Services are isolated in their own network
- **Regular Updates**: All components are regularly updated to address security vulnerabilities

### Monitoring and Observability

The architecture includes comprehensive monitoring and observability:

- **Metrics Collection**: Key metrics are collected and stored
- **Logging**: Logs are centralized and searchable
- **Alerting**: Alerts are generated for abnormal conditions
- **Dashboards**: Dashboards provide visibility into system health and performance
- **Tracing**: Distributed tracing helps identify performance bottlenecks

### Disaster Recovery

The architecture includes several features for disaster recovery:

- **Backups**: Regular backups of important data
- **Multi-zone Deployment**: Services are deployed across multiple zones for high availability
- **Automated Recovery**: Failed components are automatically recovered
- **Data Replication**: Critical data is replicated for redundancy
