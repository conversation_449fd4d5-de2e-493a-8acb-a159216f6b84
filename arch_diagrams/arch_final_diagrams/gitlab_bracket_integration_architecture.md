# GitLab-Bracket Integration Architecture

This architecture diagram illustrates the integration between GitLab and Bracket, focusing on the key components and their relationships without revealing the proprietary implementation details of Bracket IRL.

```mermaid
flowchart TB
    classDef bracketComponent fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef gitlabComponent fill:#e8f5e9,stroke:#2e7d32,stroke-width:2px
    classDef cloudComponent fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px
    classDef storageComponent fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px
    classDef apiComponent fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef userComponent fill:#ffebee,stroke:#b71c1c,stroke-width:2px
    classDef subgraph_style fill:#f5f5f5,stroke:#9e9e9e,stroke-width:1px
    
    %% User Components
    User[Developer/User]:::userComponent
    
    %% GitLab Components
    subgraph GitLabSystem["GitLab System"]
        direction TB
        
        GitLabRepo[GitLab Repository]:::gitlabComponent
        GitLabAPI[GitLab API]:::gitlabComponent
        GitLabWebhooks[GitLab Webhooks]:::gitlabComponent
        GitLabCI[GitLab CI/CD]:::gitlabComponent
        GitLabMR[Merge Requests]:::gitlabComponent
        GitLabIDE[Web IDE]:::gitlabComponent
    end
    
    %% Bracket Integration Components
    subgraph BracketIntegration["Bracket Integration Layer"]
        direction TB
        
        %% API Gateway
        APIGateway[API Gateway]:::apiComponent
        
        %% Repository Processing
        subgraph RepoProcessing["Repository Processing"]
            direction TB
            RepoCloner[Repository Cloner]:::bracketComponent
            IndexTrigger[Indexing Trigger]:::bracketComponent
            DeltaProcessor[Delta Change Processor]:::bracketComponent
        end
        
        %% Webhook Handlers
        subgraph WebhookHandlers["Webhook Handlers"]
            direction TB
            CommitHook[Commit Webhook Handler]:::bracketComponent
            MRHook[Merge Request Webhook Handler]:::bracketComponent
            BranchHook[Branch Webhook Handler]:::bracketComponent
        end
        
        %% Authentication
        AuthService[Authentication Service]:::bracketComponent
    end
    
    %% Bracket Core System (Black Box - Implementation Hidden)
    subgraph BracketCore["Bracket Core System"]
        direction TB
        
        %% Indexing Process (Black Box)
        subgraph IndexingProcess["Indexing Process"]
            direction TB
            InitialIndexing[Initial Repository Indexing]:::bracketComponent
            IncrementalIndexing[Incremental Indexing]:::bracketComponent
            CodeStructureExtraction[Code Structure Extraction]:::bracketComponent
        end
        
        %% Artifact Generation
        subgraph ArtifactGeneration["Artifact Generation"]
            direction TB
            DomainAnalysis[Domain Analysis]:::bracketComponent
            DiagramGeneration[Diagram Generation]:::bracketComponent
            CodebaseExplanation[Codebase Explanation]:::bracketComponent
        end
    end
    
    %% Cloud Infrastructure
    subgraph CloudInfrastructure["Cloud Infrastructure (GCP/GKE)"]
        direction TB
        
        %% GKE Cluster
        subgraph GKECluster["GKE Cluster"]
            direction TB
            OrchestratorService[Orchestrator Service]:::cloudComponent
            RepoMapperService[Repository Mapper Service]:::cloudComponent
            DomainAnalyzerService[Domain Analyzer Service]:::cloudComponent
            FileDomainMapperService[File-Domain Mapper Service]:::cloudComponent
            DiagramGeneratorService[Diagram Generator Service]:::cloudComponent
            
            %% Monitoring
            PrometheusService[Prometheus]:::cloudComponent
            GrafanaService[Grafana]:::cloudComponent
        end
        
        %% Storage
        subgraph CloudStorage["Cloud Storage"]
            direction TB
            ArtifactRegistry[Google Artifact Registry]:::storageComponent
            CloudStorageBucket[Google Cloud Storage]:::storageComponent
            SecretManager[Secret Manager]:::storageComponent
        end
        
        %% Networking
        LoadBalancer[Cloud Load Balancer]:::cloudComponent
        CloudNAT[Cloud NAT]:::cloudComponent
    end
    
    %% Bracket Extension
    subgraph BracketExtension["Bracket Extension"]
        direction TB
        
        %% VSCode Extension
        VSCodeExt[VSCode Extension]:::bracketComponent
        
        %% Features
        subgraph ExtensionFeatures["Extension Features"]
            direction TB
            CodeQuery[Code Query & Navigation]:::bracketComponent
            MermaidCompanion[Mermaid Companion]:::bracketComponent
            CodeGeneration[Code Generation]:::bracketComponent
            PRAnalysis[PR Analysis]:::bracketComponent
        end
        
        %% Artifact Access
        subgraph ArtifactAccess["Artifact Access"]
            direction TB
            LocalArtifacts[Local Artifacts]:::bracketComponent
            CloudArtifacts[Cloud Artifacts]:::bracketComponent
        end
    end
    
    %% Connections
    
    %% User Connections
    User --> GitLabIDE
    User --> VSCodeExt
    
    %% GitLab to Bracket Integration
    GitLabRepo --> GitLabAPI
    GitLabAPI --> APIGateway
    GitLabWebhooks --> CommitHook
    GitLabWebhooks --> MRHook
    GitLabWebhooks --> BranchHook
    
    %% Bracket Integration to Bracket Core
    APIGateway --> AuthService
    AuthService --> RepoCloner
    RepoCloner --> IndexTrigger
    IndexTrigger --> InitialIndexing
    
    CommitHook --> DeltaProcessor
    DeltaProcessor --> IncrementalIndexing
    
    MRHook --> DeltaProcessor
    BranchHook --> RepoCloner
    
    %% Bracket Core to Cloud Infrastructure
    InitialIndexing --> OrchestratorService
    IncrementalIndexing --> OrchestratorService
    CodeStructureExtraction --> RepoMapperService
    
    OrchestratorService --> RepoMapperService
    OrchestratorService --> DomainAnalyzerService
    OrchestratorService --> FileDomainMapperService
    OrchestratorService --> DiagramGeneratorService
    
    RepoMapperService --> DomainAnalyzerService
    DomainAnalyzerService --> FileDomainMapperService
    FileDomainMapperService --> DiagramGeneratorService
    
    %% Monitoring
    OrchestratorService --> PrometheusService
    PrometheusService --> GrafanaService
    
    %% Storage
    DiagramGeneratorService --> CloudStorageBucket
    OrchestratorService --> ArtifactRegistry
    AuthService --> SecretManager
    
    %% Networking
    LoadBalancer --> OrchestratorService
    CloudNAT --> GKECluster
    
    %% Artifact Generation to Extension
    DomainAnalysis --> CloudStorageBucket
    DiagramGeneration --> CloudStorageBucket
    CodebaseExplanation --> CloudStorageBucket
    
    %% Extension to Artifacts
    VSCodeExt --> ExtensionFeatures
    CodeQuery --> LocalArtifacts
    MermaidCompanion --> LocalArtifacts
    CodeGeneration --> LocalArtifacts
    PRAnalysis --> LocalArtifacts
    
    LocalArtifacts -.-> CloudArtifacts
    CloudArtifacts --> CloudStorageBucket
    
    %% GitLab CI/CD Integration
    GitLabCI --> APIGateway
    GitLabMR --> MRHook
```

## GitLab-Bracket Integration Architecture: Detailed Explanation

This architecture diagram illustrates the integration between GitLab and Bracket, focusing on how the two systems interact while preserving the proprietary nature of Bracket's IRL technology.

### Key Components

#### GitLab System
- **GitLab Repository**: Source code storage and version control
- **GitLab API**: Programmatic interface for accessing GitLab resources
- **GitLab Webhooks**: Event-driven triggers for repository events
- **GitLab CI/CD**: Continuous integration and deployment pipeline
- **Merge Requests**: Code review and integration workflow
- **Web IDE**: Browser-based development environment

#### Bracket Integration Layer
- **API Gateway**: Central entry point for GitLab to Bracket communication
- **Repository Processing**: Components for handling repository data
  - **Repository Cloner**: Clones repositories for analysis
  - **Indexing Trigger**: Initiates the indexing process
  - **Delta Change Processor**: Processes incremental code changes
- **Webhook Handlers**: Process GitLab webhook events
  - **Commit Webhook Handler**: Handles commit events
  - **Merge Request Webhook Handler**: Handles MR events
  - **Branch Webhook Handler**: Handles branch events
- **Authentication Service**: Manages secure access between systems

#### Bracket Core System
- **Indexing Process**: Transforms code into structured representations
  - **Initial Repository Indexing**: Full repository analysis
  - **Incremental Indexing**: Processes only changed files
  - **Code Structure Extraction**: Extracts code structure information
- **Artifact Generation**: Creates valuable outputs from analysis
  - **Domain Analysis**: Identifies logical domains in code
  - **Diagram Generation**: Creates visual representations
  - **Codebase Explanation**: Generates documentation

#### Cloud Infrastructure (GCP/GKE)
- **GKE Cluster**: Kubernetes environment for microservices
  - **Orchestrator Service**: Coordinates the analysis pipeline
  - **Repository Mapper Service**: Analyzes repository structure
  - **Domain Analyzer Service**: Identifies logical domains
  - **File-Domain Mapper Service**: Maps files to domains
  - **Diagram Generator Service**: Creates visual diagrams
  - **Prometheus & Grafana**: Monitoring and visualization
- **Cloud Storage**: Persistent storage solutions
  - **Google Artifact Registry**: Docker image storage
  - **Google Cloud Storage**: Artifact and data storage
  - **Secret Manager**: Secure credential storage
- **Networking**: Communication infrastructure
  - **Cloud Load Balancer**: Distributes incoming traffic
  - **Cloud NAT**: Network address translation

#### Bracket Extension
- **VSCode Extension**: IDE integration for developers
- **Extension Features**: Capabilities provided to users
  - **Code Query & Navigation**: Intelligent code exploration
  - **Mermaid Companion**: Diagram visualization
  - **Code Generation**: AI-assisted code creation
  - **PR Analysis**: Merge request impact analysis
- **Artifact Access**: Methods for accessing analysis results
  - **Local Artifacts**: Locally stored analysis results
  - **Cloud Artifacts**: Cloud-stored analysis results

### Key Integration Flows

#### 1. Initial Repository Indexing
1. User initiates indexing through GitLab or VSCode
2. API Gateway authenticates the request
3. Repository Cloner fetches the repository
4. Indexing Trigger initiates the indexing process
5. Bracket Core performs the indexing (implementation details hidden)
6. Results are stored in Cloud Storage
7. Artifacts become available through the Extension

#### 2. Delta Codebase Ingestion
1. Developer commits code to GitLab
2. GitLab Webhook triggers Commit Webhook Handler
3. Delta Change Processor identifies changed files
4. Incremental Indexing processes only the changes
5. Updated artifacts are generated and stored
6. Extension accesses the updated artifacts

#### 3. Merge Request Analysis
1. Developer creates a merge request in GitLab
2. MR Webhook Handler receives the event
3. Delta Change Processor analyzes the changes
4. PR Analysis feature shows impact in the Extension
5. Results can be posted back to the GitLab MR

#### 4. CI/CD Integration
1. GitLab CI/CD pipeline triggers analysis via API Gateway
2. Bracket Core analyzes the code
3. Results are stored and can be used for quality gates
4. Pipeline can succeed or fail based on analysis results
