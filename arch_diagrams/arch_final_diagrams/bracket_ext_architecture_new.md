# Bracket Extension Architecture

```mermaid
%%{
  init: {
    'theme': 'base',
    'themeVariables': {
      'primaryColor': '#6366f1',
      'primaryTextColor': '#ffffff',
      'primaryBorderColor': '#4f46e5',
      'lineColor': '#64748b',
      'secondaryColor': '#f1f5f9',
      'tertiaryColor': '#ffffff'
    },
    'flowchart': {
      'curve': 'basis',
      'htmlLabels': true,
      'padding': 15
    }
  }
}%%

flowchart TD
    %% Improved styling with gradients and rounded corners
    classDef uiComponent fill:#e1f5fe,stroke:#0288d1,stroke-width:2px,color:#01579b,font-weight:bold,border-radius:8px
    classDef coreComponent fill:#e8f5e9,stroke:#2e7d32,stroke-width:2px,color:#1b5e20,font-weight:bold,border-radius:8px
    classDef integrationComponent fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#e65100,font-weight:bold,border-radius:8px
    classDef serviceComponent fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px,color:#4a148c,font-weight:bold,border-radius:8px
    classDef externalComponent fill:#ffebee,stroke:#b71c1c,stroke-width:2px,color:#b71c1c,font-weight:bold,border-radius:8px
    classDef dataComponent fill:#f5f5f5,stroke:#616161,stroke-width:2px,color:#212121,font-weight:bold,border-radius:8px
    classDef subgraph_style fill:#f8fafc,stroke:#94a3b8,stroke-width:1px,color:#475569,border-radius:12px,font-weight:bold
    
    %% VSCode Extension Host - Highlighted as main entry point
    VSCodeExtensionHost["<div style='font-size:16px; padding:10px;'>🖥️ VSCode Extension Host</div>"]:::externalComponent
    
    %% Main Extension Entry Point
    ExtensionEntry["<div style='font-size:16px; padding:10px;'>📌 Extension Entry Point<br/><i>extension.ts</i></div>"]:::coreComponent
    
    %% UI Components
    subgraph UI["<div style='font-size:18px; padding:8px;'>🎨 UI Components</div>"]
        direction TB
        
        %% Webview UI
        subgraph WebviewUI["<div style='font-size:16px;'>⚛️ Webview UI React</div>"]
            direction TB
            App["<div style='padding:6px;'>App.tsx</div>"]:::uiComponent
            ChatView["<div style='padding:6px;'>ChatView.tsx</div>"]:::uiComponent
            SettingsView["<div style='padding:6px;'>SettingsView.tsx</div>"]:::uiComponent
            TaskHeader["<div style='padding:6px;'>TaskHeader.tsx</div>"]:::uiComponent
            ChatRow["<div style='padding:6px;'>ChatRow.tsx</div>"]:::uiComponent
            ChatTextArea["<div style='padding:6px;'>ChatTextArea.tsx</div>"]:::uiComponent
            GlobalCodebasePanel["<div style='padding:6px;'>Global Codebase Panel</div>"]:::uiComponent
            
            App --> ChatView
            App --> SettingsView
            App --> GlobalCodebasePanel
            ChatView --> TaskHeader
            ChatView --> ChatRow
            ChatView --> ChatTextArea
        end
        
        %% Webview Providers
        subgraph WebviewProviders["<div style='font-size:16px;'>🔌 Webview Providers</div>"]
            direction TB
            BracketProvider["<div style='padding:6px;'>BracketProvider.ts</div>"]:::uiComponent
            MermaidCompanionProvider["<div style='padding:6px;'>MermaidCompanionProvider.ts</div>"]:::uiComponent
            
            BracketProvider -. "provides" .-> WebviewUI
            MermaidCompanionProvider -. "provides" .-> MermaidCompanionView
        end
        
        %% Mermaid Companion
        subgraph MermaidCompanionView["<div style='font-size:16px;'>📊 Mermaid Companion View</div>"]
            direction TB
            MermaidRenderer["<div style='padding:6px;'>Mermaid Renderer</div>"]:::uiComponent
            DiagramControls["<div style='padding:6px;'>Diagram Controls</div>"]:::uiComponent
            BreadcrumbNav["<div style='padding:6px;'>Breadcrumb Navigation</div>"]:::uiComponent
            
            MermaidRenderer --> DiagramControls
            MermaidRenderer --> BreadcrumbNav
        end
    end
    
    %% Core Components
    subgraph Core["<div style='font-size:18px; padding:8px;'>⚙️ Core Components</div>"]
        direction TB
        
        %% Bracket System
        subgraph BracketSystem["<div style='font-size:16px;'>🔄 Bracket System</div>"]
            direction TB
            Bracket["<div style='padding:6px;'>Bracket.ts</div>"]:::coreComponent
            BracketSystem1["<div style='padding:6px;'>Bracket System</div>"]:::coreComponent
            PromptSystem["<div style='padding:6px;'>Prompt System</div>"]:::coreComponent
            ModeValidator["<div style='padding:6px;'>Mode Validator</div>"]:::coreComponent
            SlidingWindow["<div style='padding:6px;'>Sliding Window</div>"]:::coreComponent
            
            Bracket --> BracketSystem1
            BracketSystem1 --> PromptSystem
            BracketSystem1 --> ModeValidator
            BracketSystem1 --> SlidingWindow
        end
        
        %% API System
        subgraph APISystem["<div style='font-size:16px;'>🌐 API System</div>"]
            direction TB
            ApiHandler["<div style='padding:6px;'>ApiHandler Interface</div>"]:::coreComponent
            AnthropicProvider["<div style='padding:6px;'>Anthropic Provider</div>"]:::coreComponent
            OpenAIProvider["<div style='padding:6px;'>OpenAI Provider</div>"]:::coreComponent
            BedrockProvider["<div style='padding:6px;'>Bedrock Provider</div>"]:::coreComponent
            OpenRouterProvider["<div style='padding:6px;'>OpenRouter Provider</div>"]:::coreComponent
            
            ApiHandler --> AnthropicProvider
            ApiHandler --> OpenAIProvider
            ApiHandler --> BedrockProvider
            ApiHandler --> OpenRouterProvider
        end
        
        %% Extension State
        subgraph ExtensionState["<div style='font-size:16px;'>💾 Extension State</div>"]
            direction TB
            GlobalState["<div style='padding:6px;'>Global State</div>"]:::coreComponent
            TaskHistory["<div style='padding:6px;'>Task History</div>"]:::coreComponent
            Settings["<div style='padding:6px;'>Settings</div>"]:::coreComponent
            
            GlobalState --> TaskHistory
            GlobalState --> Settings
        end
    end
    
    %% Integration Components
    subgraph Integrations["<div style='font-size:18px; padding:8px;'>🔗 Integration Components</div>"]
        direction TB
        
        %% Editor Integration
        subgraph EditorIntegration["<div style='font-size:16px;'>📝 Editor Integration</div>"]
            direction TB
            CodeActionProvider["<div style='padding:6px;'>CodeActionProvider.ts</div>"]:::integrationComponent
            DiffViewProvider["<div style='padding:6px;'>DiffViewProvider.ts</div>"]:::integrationComponent
            
            CodeActionProvider --> DiffViewProvider
        end
        
        %% Terminal Integration
        subgraph TerminalIntegration["<div style='font-size:16px;'>⌨️ Terminal Integration</div>"]
            direction TB
            TerminalManager["<div style='padding:6px;'>Terminal Manager</div>"]:::integrationComponent
            CommandExecutor["<div style='padding:6px;'>Command Executor</div>"]:::integrationComponent
            OutputParser["<div style='padding:6px;'>Output Parser</div>"]:::integrationComponent
            
            TerminalManager --> CommandExecutor
            CommandExecutor --> OutputParser
        end
        
        %% Context Engine Integration
        subgraph ContextEngineIntegration["<div style='font-size:16px;'>🧠 Context Engine Integration</div>"]
            direction TB
            ContextEngine["<div style='padding:6px;'>Context Engine</div>"]:::integrationComponent
            RelevanceRanker["<div style='padding:6px;'>Relevance Ranker</div>"]:::integrationComponent
            FunctionBrowser["<div style='padding:6px;'>Function Browser</div>"]:::integrationComponent
            
            ContextEngine --> RelevanceRanker
            ContextEngine --> FunctionBrowser
        end
        
        %% Browser Integration
        subgraph BrowserIntegration["<div style='font-size:16px;'>🌍 Browser Integration</div>"]
            direction TB
            BrowserManager["<div style='padding:6px;'>Browser Manager</div>"]:::integrationComponent
            WebFetcher["<div style='padding:6px;'>Web Fetcher</div>"]:::integrationComponent
            
            BrowserManager --> WebFetcher
        end
    end
    
    %% Services
    subgraph Services["<div style='font-size:18px; padding:8px;'>🛠️ Services</div>"]
        direction TB
        
        %% Mermaid Service
        subgraph MermaidService["<div style='font-size:16px;'>📊 Mermaid Service</div>"]
            direction TB
            MermaidCompanionService["<div style='padding:6px;'>Mermaid Companion Service</div>"]:::serviceComponent
            MermaidMappingService["<div style='padding:6px;'>Mermaid Mapping Service</div>"]:::serviceComponent
            MermaidRendererService["<div style='padding:6px;'>Mermaid Renderer Service</div>"]:::serviceComponent
            
            MermaidCompanionService --> MermaidMappingService
            MermaidMappingService --> MermaidRendererService
        end
        
        %% Telemetry Service
        subgraph TelemetryService["<div style='font-size:16px;'>📈 Telemetry Service</div>"]
            direction TB
            EventLogger["<div style='padding:6px;'>Event Logger</div>"]:::serviceComponent
            UsageTracker["<div style='padding:6px;'>Usage Tracker</div>"]:::serviceComponent
            
            EventLogger --> UsageTracker
        end
        
        %% Multi-Context Processing
        subgraph MultiContextProcessing["<div style='font-size:16px;'>🔄 Multi-Context Processing</div>"]
            direction TB
            ContextManager["<div style='padding:6px;'>Context Manager</div>"]:::serviceComponent
            ContextMerger["<div style='padding:6px;'>Context Merger</div>"]:::serviceComponent
            
            ContextManager --> ContextMerger
        end
    end
    
    %% Bracket Core Integration
    subgraph BracketCoreIntegration["<div style='font-size:18px; padding:8px;'>🧩 Bracket Core Integration</div>"]
        direction TB
        
        %% Artifact Access
        subgraph ArtifactAccess["<div style='font-size:16px;'>📁 Artifact Access</div>"]
            direction TB
            LocalArtifacts["<div style='padding:6px;'>Local Artifacts</div>"]:::dataComponent
            CloudArtifacts["<div style='padding:6px;'>Cloud Artifacts</div>"]:::dataComponent
            ArtifactLoader["<div style='padding:6px;'>Artifact Loader</div>"]:::serviceComponent
            
            LocalArtifacts --> ArtifactLoader
            CloudArtifacts --> ArtifactLoader
        end
        
        %% Artifact Types
        subgraph ArtifactTypes["<div style='font-size:16px;'>🏷️ Artifact Types</div>"]
            direction TB
            DomainTaxonomy["<div style='padding:6px;'>Domain Taxonomy JSON</div>"]:::dataComponent
            MermaidDiagrams["<div style='padding:6px;'>Mermaid Diagrams</div>"]:::dataComponent
            CodebaseExplanation["<div style='padding:6px;'>Codebase Explanation</div>"]:::dataComponent
            
            DomainTaxonomy --> MermaidDiagrams
            DomainTaxonomy --> CodebaseExplanation
        end
        
        ArtifactAccess --> ArtifactTypes
    end
    
    %% External Services
    subgraph ExternalServices["<div style='font-size:18px; padding:8px;'>☁️ External Services</div>"]
        direction TB
        LLMProviders["<div style='padding:6px;'>LLM Providers<br/>OpenAI, Anthropic, etc.</div>"]:::externalComponent
        GitLabAPI["<div style='padding:6px;'>GitLab API</div>"]:::externalComponent
        
        LLMProviders --- GitLabAPI
    end
    
    %% Connections between main components with improved styling
    ExtensionEntry --> |"initializes"| UI
    ExtensionEntry --> |"configures"| Core
    ExtensionEntry --> |"registers"| Integrations
    ExtensionEntry --> |"starts"| Services
    
    VSCodeExtensionHost --> |"loads"| ExtensionEntry
    
    BracketProvider --> |"uses"| Bracket
    Bracket --> |"calls"| ApiHandler
    
    MermaidCompanionProvider --> |"uses"| MermaidCompanionService
    MermaidMappingService --> |"accesses"| BracketCoreIntegration
    
    Bracket --> |"integrates"| TerminalIntegration
    Bracket --> |"integrates"| EditorIntegration
    Bracket --> |"integrates"| ContextEngineIntegration
    Bracket --> |"integrates"| BrowserIntegration
    
    BracketProvider --> |"manages"| ExtensionState
    
    ContextEngine --> |"uses"| BracketCoreIntegration
    GlobalCodebasePanel --> |"leverages"| BracketCoreIntegration
    
    ApiHandler --> |"connects to"| ExternalServices
    
    %% Export API
    ExportAPI["<div style='font-size:16px; padding:10px;'>📤 Export API</div>"]:::coreComponent
    ExtensionEntry --> |"exposes"| ExportAPI
    
    %% Apply styles to subgraphs
    style UI fill:#f8fafc,stroke:#94a3b8,stroke-width:2px,color:#334155,border-radius:12px
    style Core fill:#f8fafc,stroke:#94a3b8,stroke-width:2px,color:#334155,border-radius:12px
    style Integrations fill:#f8fafc,stroke:#94a3b8,stroke-width:2px,color:#334155,border-radius:12px
    style Services fill:#f8fafc,stroke:#94a3b8,stroke-width:2px,color:#334155,border-radius:12px
    style BracketCoreIntegration fill:#f8fafc,stroke:#94a3b8,stroke-width:2px,color:#334155,border-radius:12px
    style ExternalServices fill:#f8fafc,stroke:#94a3b8,stroke-width:2px,color:#334155,border-radius:12px
    
    %% Apply styles to inner subgraphs
    style WebviewUI fill:#f1f5f9,stroke:#cbd5e1,stroke-width:1px,color:#475569,border-radius:8px
    style WebviewProviders fill:#f1f5f9,stroke:#cbd5e1,stroke-width:1px,color:#475569,border-radius:8px
    style MermaidCompanionView fill:#f1f5f9,stroke:#cbd5e1,stroke-width:1px,color:#475569,border-radius:8px
    style BracketSystem fill:#f1f5f9,stroke:#cbd5e1,stroke-width:1px,color:#475569,border-radius:8px
    style APISystem fill:#f1f5f9,stroke:#cbd5e1,stroke-width:1px,color:#475569,border-radius:8px
    style ExtensionState fill:#f1f5f9,stroke:#cbd5e1,stroke-width:1px,color:#475569,border-radius:8px
    style EditorIntegration fill:#f1f5f9,stroke:#cbd5e1,stroke-width:1px,color:#475569,border-radius:8px
    style TerminalIntegration fill:#f1f5f9,stroke:#cbd5e1,stroke-width:1px,color:#475569,border-radius:8px
    style ContextEngineIntegration fill:#f1f5f9,stroke:#cbd5e1,stroke-width:1px,color:#475569,border-radius:8px
    style BrowserIntegration fill:#f1f5f9,stroke:#cbd5e1,stroke-width:1px,color:#475569,border-radius:8px
    style MermaidService fill:#f1f5f9,stroke:#cbd5e1,stroke-width:1px,color:#475569,border-radius:8px
    style TelemetryService fill:#f1f5f9,stroke:#cbd5e1,stroke-width:1px,color:#475569,border-radius:8px
    style MultiContextProcessing fill:#f1f5f9,stroke:#cbd5e1,stroke-width:1px,color:#475569,border-radius:8px
    style ArtifactAccess fill:#f1f5f9,stroke:#cbd5e1,stroke-width:1px,color:#475569,border-radius:8px
    style ArtifactTypes fill:#f1f5f9,stroke:#cbd5e1,stroke-width:1px,color:#475569,border-radius:8px
```

## Bracket Extension Architecture: Detailed Explanation

The Bracket Extension (bracket_ext) is a TypeScript-based VSCode extension that serves as the product layer of the Bracket system. It provides a rich user interface for interacting with the AI assistant and visualizing the codebase structure through integration with the Bracket Core IRL system.

### Main Components

#### 1. Extension Entry Point (extension.ts)

**Purpose**: Serves as the main entry point for the VSCode extension.

**Responsibilities**:
- Initializing the extension
- Registering commands, code actions, and webview providers
- Setting up event listeners
- Exporting the API for other extensions

#### 2. UI Components

##### Webview UI (React)

**Purpose**: Provides the user interface for interacting with the AI assistant.

**Responsibilities**:
- Rendering the chat interface
- Handling user input
- Displaying AI responses
- Managing settings
- Providing codebase overview

##### Webview Providers

**Purpose**: Bridge between VSCode and the React UI.

**Components**:
- **BracketProvider.ts**: Provides the main chat interface
- **MermaidCompanionProvider.ts**: Provides the Mermaid diagram viewer

**Responsibilities**:
- Creating and managing webviews
- Handling messages between VSCode and webviews
- Managing webview lifecycle

##### Mermaid Companion View

**Purpose**: Displays Mermaid diagrams as users navigate through code.

**Components**:
- **Mermaid Renderer**: Renders Mermaid diagrams
- **Diagram Controls**: Provides controls for zooming and panning
- **Breadcrumb Navigation**: Provides navigation through the domain hierarchy

**Responsibilities**:
- Rendering Mermaid diagrams
- Providing navigation controls
- Highlighting relevant parts of diagrams

#### 3. Core Components

##### Bracket System

**Purpose**: Provides the core functionality of the extension.

**Components**:
- **Bracket.ts**: Main class that coordinates all functionality
- **Bracket System**: Manages chat interactions
- **Prompt System**: Manages prompts for the AI
- **Mode Validator**: Validates and manages modes (Chat/Agent)
- **Sliding Window**: Manages context window for the AI

**Responsibilities**:
- Coordinating all extension functionality
- Managing chat interactions
- Processing user input
- Generating AI responses

##### API System

**Purpose**: Interfaces with AI providers.

**Components**:
- **ApiHandler Interface**: Common interface for all providers
- **Anthropic Provider**: Interfaces with Anthropic Claude
- **OpenAI Provider**: Interfaces with OpenAI
- **Bedrock Provider**: Interfaces with AWS Bedrock
- **OpenRouter Provider**: Interfaces with OpenRouter

**Responsibilities**:
- Sending requests to AI providers
- Processing responses
- Handling streaming
- Managing rate limits and tokens

##### Extension State

**Purpose**: Manages the state of the extension.

**Components**:
- **Global State**: Overall extension state
- **Task History**: History of tasks
- **Settings**: User settings

**Responsibilities**:
- Storing and retrieving state
- Managing task history
- Handling settings

#### 4. Integration Components

##### Editor Integration

**Purpose**: Integrates with the VSCode editor.

**Components**:
- **CodeActionProvider.ts**: Provides code actions
- **DiffViewProvider.ts**: Handles code diffs

**Responsibilities**:
- Providing code actions
- Handling code diffs
- Managing editor interactions

##### Terminal Integration

**Purpose**: Integrates with the VSCode terminal.

**Components**:
- **Terminal Manager**: Manages terminal instances
- **Command Executor**: Executes commands in the terminal
- **Output Parser**: Parses terminal output

**Responsibilities**:
- Creating and managing terminals
- Executing commands
- Capturing and parsing output

##### Context Engine Integration

**Purpose**: Integrates with the Context Engine.

**Components**:
- **Context Engine**: Identifies relevant code
- **Relevance Ranker**: Ranks code by relevance
- **Function Browser**: Provides a browser for functions

**Responsibilities**:
- Identifying relevant code
- Ranking code by relevance
- Providing a function browser

##### Browser Integration

**Purpose**: Integrates with web browsers.

**Components**:
- **Browser Manager**: Manages browser instances
- **Web Fetcher**: Fetches content from the web

**Responsibilities**:
- Opening and managing browsers
- Fetching web content
- Processing web content

#### 5. Services

##### Mermaid Service

**Purpose**: Manages Mermaid diagrams.

**Components**:
- **Mermaid Companion Service**: Coordinates Mermaid functionality
- **Mermaid Mapping Service**: Maps code to diagrams
- **Mermaid Renderer Service**: Renders diagrams

**Responsibilities**:
- Loading and managing diagrams
- Mapping code to diagrams
- Rendering diagrams

##### Telemetry Service

**Purpose**: Collects telemetry data.

**Components**:
- **Event Logger**: Logs events
- **Usage Tracker**: Tracks usage

**Responsibilities**:
- Logging events
- Tracking usage
- Sending telemetry data

##### Multi-Context Processing

**Purpose**: Processes multiple contexts.

**Components**:
- **Context Manager**: Manages multiple contexts
- **Context Merger**: Merges contexts

**Responsibilities**:
- Managing multiple contexts
- Merging contexts
- Prioritizing contexts

#### 6. Bracket Core Integration

##### Artifact Access

**Purpose**: Accesses artifacts from Bracket Core.

**Components**:
- **Local Artifacts**: Artifacts stored locally
- **Cloud Artifacts**: Artifacts stored in the cloud
- **Artifact Loader**: Loads artifacts

**Responsibilities**:
- Loading artifacts from local storage
- Loading artifacts from cloud storage
- Managing artifact lifecycle

##### Artifact Types

**Purpose**: Represents different types of artifacts.

**Components**:
- **Domain Taxonomy JSON**: Represents the domain taxonomy
- **Mermaid Diagrams**: Visual representations of domains
- **Codebase Explanation**: Explanation of the codebase

**Responsibilities**:
- Loading domain taxonomy
- Displaying Mermaid diagrams
- Providing codebase explanations

#### 7. External Services

**Purpose**: Interfaces with external services.

**Components**:
- **LLM Providers**: AI providers like OpenAI and Anthropic
- **GitLab API**: Interfaces with GitLab

**Responsibilities**:
- Sending requests to AI providers
- Interfacing with GitLab
- Managing external service interactions

### Integration with Bracket Core

The Bracket Extension integrates with Bracket Core through the following mechanisms:

1. **Domain Taxonomy**: The extension loads the domain taxonomy JSON file generated by Bracket Core's IRL system.

2. **Mermaid Diagrams**: The extension displays Mermaid diagrams generated by Bracket Core's IRL system.

3. **Codebase Explanation**: The extension uses the codebase explanation generated by Bracket Core's IRL system.

4. **Context Engine**: The extension uses the context engine to provide context about the codebase to the AI assistant.

### Key Features

1. **AI Assistant**: Provides an AI assistant for code-related tasks.

2. **Mermaid Companion**: Displays Mermaid diagrams as users navigate through code.

3. **Terminal Integration**: Executes commands in the terminal and captures output.

4. **Editor Integration**: Provides code actions and handles code diffs.

5. **Context Engine**: Provides context about the codebase to the AI assistant.

6. **Multi-Context Processing**: Enables processing of multiple contexts.

7. **Browser Integration**: Provides browser functionality for web-related tasks.

8. **Global Codebase Panel**: Provides a high-level overview of the codebase.

### Data Flow

1. User interacts with the UI (ChatView).
2. BracketProvider processes the interaction and passes it to Bracket.
3. Bracket uses the API System to generate a response from the AI.
4. Bracket processes the response and executes any tools or commands.
5. Results are passed back to BracketProvider and displayed in the UI.
6. For Mermaid diagrams, MermaidCompanionProvider loads diagrams from the domain taxonomy and displays them.

This architecture enables a seamless integration between the VSCode extension and the Bracket Core IRL system, providing users with a powerful tool for understanding and working with codebases.












