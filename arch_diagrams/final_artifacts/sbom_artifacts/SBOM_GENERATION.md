# Bracket SBOM Generation Process

## Overview

This document explains how the Software Bill of Materials (SBOM) for the Bracket project was generated, validated, and signed. The SBOM follows the CycloneDX specification and includes comprehensive information about all dependencies across Python, JavaScript/TypeScript, and Docker components.

## Generation Process

### Tools Used

- **CycloneDX Python Module**: For generating Python dependency SBOMs
- **CycloneDX npm Module**: For generating JavaScript/TypeScript dependency SBOMs
- **Syft**: For analyzing Docker container images
- **Cosign**: For signing the final SBOM (part of Sigstore)


### Step 2: SBOM Generation

1. **Python Dependencies**: Analyzes Python dependencies using Poetry or pip, generating a comprehensive SBOM with all direct and transitive dependencies.

2. **JavaScript/TypeScript Dependencies**: Analyzes the `bracket_ext` directory using the CycloneDX npm tool, capturing all npm dependencies including their versions, and licenses.

3. **Docker Images**: Analyzes Dockerfiles in the microservices directory using Syft, capturing base images and installed packages.

4. **Combination**: Merges all individual SBOMs into a single comprehensive SBOM file that represents the entire project.

### Step 3: SBOM Enhancement

After generation, additional scripts enhance the SBOM with more detailed information:

1. **License Analysis**: Enriches the SBOM with detailed license information.

2. **Analysis Report**: Generates a detailed report of the SBOM contents.

### Step 4: SBOM Validation and Signing

The final SBOM is validated against the CycloneDX schema and signed using Sigstore's cosign tool

## SBOM Contents

The generated SBOM includes:

- **Metadata**: Project information, generation timestamp, and tool details
- **Components**: All dependencies with versions, licenses, and package URLs (purls)
- **Licenses**: Comprehensive license information for all components
