# Bracket Comprehensive System Architecture

```mermaid
flowchart TB
    classDef coreComponent fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef extComponent fill:#e8f5e9,stroke:#2e7d32,stroke-width:2px
    classDef cloudComponent fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef dataComponent fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px
    classDef userComponent fill:#ffebee,stroke:#b71c1c,stroke-width:2px
    classDef externalComponent fill:#f5f5f5,stroke:#9e9e9e,stroke-width:2px
    classDef subgraph_style fill:#f5f5f5,stroke:#9e9e9e,stroke-width:1px
    
    %% User Components
    User[Developer/User]:::userComponent
    VSCode[Visual Studio Code]:::userComponent
    
    %% Bracket Extension Components
    subgraph BracketExt["Bracket Extension (bracket_ext)"]
        direction TB
        
        %% UI Layer
        subgraph UILayer["UI Layer"]
            direction TB
            ChatInterface[Chat Interface]:::extComponent
            MermaidCompanion[Mermaid Companion View]:::extComponent
            GlobalCodebasePanel[Global Codebase Panel]:::extComponent
            SettingsInterface[Settings Interface]:::extComponent
        end
        
        %% Core Extension Logic
        subgraph ExtCore["Core Extension Logic"]
            direction TB
            Bracket[Bracket System]:::extComponent
            ContextEngine[Context Engine]:::extComponent
            TerminalIntegration[Terminal Integration]:::extComponent
            EditorIntegration[Editor Integration]:::extComponent
            BrowserIntegration[Browser Integration]:::extComponent
        end
        
        %% API Layer
        subgraph APILayer["API Layer"]
            direction TB
            APIHandler[API Handler]:::extComponent
            LLMProviders[LLM Providers Integration]:::extComponent
        end
        
        %% Artifact Integration
        subgraph ArtifactIntegration["Artifact Integration"]
            direction TB
            ArtifactLoader[Artifact Loader]:::extComponent
            MermaidRenderer[Mermaid Renderer]:::extComponent
            TaxonomyProcessor[Taxonomy Processor]:::extComponent
        end
        
        %% Extension State
        subgraph ExtState["Extension State"]
            direction TB
            TaskHistory[Task History]:::extComponent
            UserSettings[User Settings]:::extComponent
            SessionState[Session State]:::extComponent
        end
        
        %% Connections within Extension
        UILayer --> ExtCore
        ExtCore --> APILayer
        ExtCore --> ArtifactIntegration
        ExtCore --> ExtState
        ArtifactIntegration --> UILayer
    end
    
    %% Bracket Core Components
    subgraph BracketCore["Bracket Core (bracket_core)"]
        direction TB
        
        %% IRL Pipeline
        subgraph IRLPipeline["IRL Pipeline"]
            direction TB
            Orchestration[Orchestration Layer]:::coreComponent
            RepoMapping[Repository Mapping]:::coreComponent
            KnowledgeGraph[Knowledge Graph Generation]:::coreComponent
            SemanticDoc[Semantic Documentation]:::coreComponent
            DomainAnalysis[Domain Analysis]:::coreComponent
            FileDomainMapping[File-Domain Mapping]:::coreComponent
            DomainFileRepomap[Domain-File Repomap]:::coreComponent
            DiagramGen[Diagram Generation]:::coreComponent
            TaxonomyGen[Taxonomy Generation]:::coreComponent
            CodebaseExplanation[Codebase Explanation]:::coreComponent
        end
        
        %% Delta Ingestion
        subgraph DeltaIngestion["Delta Codebase Ingestion"]
            direction TB
            DiffDetection[Diff Detection]:::coreComponent
            ChangeAnalysis[Change Analysis]:::coreComponent
            SelectiveUpdate[Selective Update]:::coreComponent
        end
        
        %% Common Infrastructure
        CommonInfrastructure[Common Infrastructure Layer]:::coreComponent
        
        %% Core Artifacts
        subgraph CoreArtifacts["Core Artifacts"]
            direction TB
            DomainTaxonomy[Domain Taxonomy JSON]:::dataComponent
            MermaidDiagrams[Mermaid Diagrams]:::dataComponent
            CodebaseUnderstanding[Codebase Understanding]:::dataComponent
        end
        
        %% Connections within Core
        IRLPipeline --> CoreArtifacts
        DeltaIngestion --> IRLPipeline
        IRLPipeline --> CommonInfrastructure
        DeltaIngestion --> CommonInfrastructure
    end
    
    %% Cloud Infrastructure
    subgraph CloudInfra["Cloud Infrastructure"]
        direction TB
        
        %% GKE Cluster
        subgraph GKECluster["GKE Cluster"]
            direction TB
            Microservices[Microservices Architecture]:::cloudComponent
            K8sResources[Kubernetes Resources]:::cloudComponent
            NodePools[Node Pools]:::cloudComponent
            
            NodePools --> K8sResources
            K8sResources --> Microservices
        end
        
        %% Storage
        subgraph Storage["Storage"]
            direction TB
            CloudStorage[Google Cloud Storage]:::cloudComponent
            CloudSQL[Google Cloud SQL]:::cloudComponent
            
            CloudStorage --- CloudSQL
        end
        
        %% Security & Monitoring
        subgraph SecMon["Security & Monitoring"]
            direction TB
            IAM[IAM & Service Accounts]:::cloudComponent
            SecretManager[Secret Manager]:::cloudComponent
            Prometheus[Prometheus Metrics]:::cloudComponent
            Grafana[Grafana Dashboards]:::cloudComponent
            
            IAM --- SecretManager
            Prometheus --- Grafana
        end
        
        %% CI/CD
        CICD[CI/CD Pipeline]:::cloudComponent
        
        %% Connections within Cloud
        GKECluster --> Storage
        GKECluster --> SecMon
        CICD --> GKECluster
    end
    
    %% External Services
    subgraph ExternalServices["External Services"]
        direction TB
        GitLab[GitLab]:::externalComponent
        OpenAI[OpenAI API]:::externalComponent
        Anthropic[Anthropic API]:::externalComponent
        AWS[AWS Bedrock]:::externalComponent
        
        GitLab --- OpenAI
        OpenAI --- Anthropic
        Anthropic --- AWS
    end
    
    %% Main Connections
    User --> VSCode
    VSCode --> BracketExt
    
    BracketExt --> BracketCore
    BracketCore --> CloudInfra
    
    APILayer --> ExternalServices
    
    %% Artifact Flow
    CoreArtifacts --> ArtifactIntegration
    
    %% External Connections
    GitLab --> CICD
    ExternalServices --> IRLPipeline
    
    %% Delta Ingestion Flow
    GitLab --> DeltaIngestion
```

## Bracket Comprehensive System Architecture: Detailed Explanation

This diagram provides a comprehensive view of the entire Bracket system, showing how the different components interact with each other to provide a powerful AI-assisted code understanding and development experience.

### System Overview

The Bracket system consists of three main components:

1. **Bracket Extension (bracket_ext)**: The TypeScript-based VSCode extension that serves as the product layer
2. **Bracket Core (bracket_core)**: The Python-based backend that implements the IRL pipeline
3. **Cloud Infrastructure**: The Google Cloud Platform infrastructure that hosts the microservices

These components work together to provide a seamless experience for developers, enabling them to understand and work with large codebases more effectively.

### User Interaction

Developers interact with the Bracket system through Visual Studio Code, which hosts the Bracket Extension. The extension provides several interfaces:

- **Chat Interface**: For interacting with the AI assistant
- **Mermaid Companion View**: For visualizing the codebase structure
- **Global Codebase Panel**: For getting a high-level overview of the codebase
- **Settings Interface**: For configuring the extension

### Bracket Extension (bracket_ext)

The Bracket Extension is the main interface between the user and the Bracket system. It consists of several layers:

#### UI Layer

The UI Layer provides the visual interfaces for interacting with the system:

- **Chat Interface**: Enables natural language interaction with the AI assistant
- **Mermaid Companion View**: Displays Mermaid diagrams as users navigate through code
- **Global Codebase Panel**: Provides a high-level overview of the codebase
- **Settings Interface**: Allows users to configure the extension

#### Core Extension Logic

The Core Extension Logic implements the main functionality of the extension:

- **Bracket System**: Manages chat interactions and AI responses
- **Context Engine**: Identifies relevant code based on user queries
- **Terminal Integration**: Executes commands in the terminal and captures output
- **Editor Integration**: Provides code actions and handles code diffs
- **Browser Integration**: Opens and interacts with web browsers

#### API Layer

The API Layer interfaces with external AI providers:

- **API Handler**: Manages API requests and responses
- **LLM Providers Integration**: Interfaces with different LLM providers

#### Artifact Integration

The Artifact Integration layer manages the integration with Bracket Core artifacts:

- **Artifact Loader**: Loads artifacts from local or cloud storage
- **Mermaid Renderer**: Renders Mermaid diagrams
- **Taxonomy Processor**: Processes the domain taxonomy

#### Extension State

The Extension State manages the state of the extension:

- **Task History**: Stores the history of tasks
- **User Settings**: Stores user preferences
- **Session State**: Manages the current session state

### Bracket Core (bracket_core)

Bracket Core implements the IRL pipeline that analyzes codebases and generates structured representations:

#### IRL Pipeline

The IRL Pipeline processes codebases through several sequential stages:

- **Orchestration Layer**: Coordinates the pipeline and manages jobs
- **Repository Mapping**: Creates a structural representation of the codebase
- **Knowledge Graph Generation**: Builds a graph representation of components and relationships
- **Semantic Documentation**: Adds semantic meaning to components when required
- **Domain Analysis**: Organizes code into logical domains
- **File-Domain Mapping**: Maps files to domains
- **Domain-File Repomap**: Creates a unified representation
- **Diagram Generation**: Creates visual Mermaid diagrams
- **Taxonomy Generation**: Combines all outputs into a unified JSON
- **Codebase Explanation**: Generates a comprehensive explanation

#### Delta Codebase Ingestion

The Delta Codebase Ingestion system processes incremental changes to the codebase:

- **Diff Detection**: Identifies changes between versions
- **Change Analysis**: Analyzes the nature and scope of changes
- **Selective Update**: Updates only the affected parts of the IRL representation

#### Common Infrastructure

The Common Infrastructure Layer provides shared functionality for the IRL pipeline and Delta Ingestion:

- Storage client for artifact management
- Configuration management
- Logging and monitoring
- Error handling

#### Core Artifacts

The Core Artifacts are the outputs of the IRL pipeline:

- **Domain Taxonomy JSON**: Comprehensive JSON representation of the codebase
- **Mermaid Diagrams**: Visual representations of domains
- **Codebase Understanding**: Natural language explanation of the codebase

### Cloud Infrastructure

The Cloud Infrastructure hosts the microservices that implement the IRL pipeline:

#### GKE Cluster

The GKE Cluster provides a managed Kubernetes environment:

- **Microservices Architecture**: Implements the IRL pipeline as microservices
- **Kubernetes Resources**: Manages the lifecycle of containers
- **Node Pools**: Provides compute resources for the microservices

#### Storage

The Storage layer provides persistent storage:

- **Google Cloud Storage**: Stores artifacts and other large objects
- **Google Cloud SQL**: Stores structured data like job information

#### Security & Monitoring

The Security & Monitoring layer ensures the system is secure and observable:

- **IAM & Service Accounts**: Manages access control
- **Secret Manager**: Securely stores sensitive information
- **Prometheus Metrics**: Collects metrics
- **Grafana Dashboards**: Visualizes metrics

#### CI/CD Pipeline

The CI/CD Pipeline automates the build, test, and deployment of the system.

### External Services

The Bracket system integrates with several external services:

- **GitLab**: Provides source code repositories and CI/CD integration
- **OpenAI API**: Provides large language model capabilities
- **Anthropic API**: Provides alternative large language model capabilities
- **AWS Bedrock**: Provides additional large language model options

### Key Interactions

#### Extension to Core Interaction

The Bracket Extension interacts with Bracket Core primarily through artifacts:

1. The extension loads artifacts (Domain Taxonomy, Mermaid Diagrams, Codebase Understanding) from local or cloud storage
2. The extension uses these artifacts to provide context to the AI assistant and visualize the codebase structure

#### Core to Cloud Interaction

Bracket Core interacts with the Cloud Infrastructure through microservices:

1. The IRL pipeline is implemented as microservices deployed on GKE
2. The microservices store artifacts in Cloud Storage
3. Job information and metadata are stored in Cloud SQL

#### External Service Integration

The Bracket system integrates with external services in several ways:

1. The API Layer in the extension interfaces with AI providers
2. The IRL Pipeline uses LLMs for various stages of analysis
3. The Delta Ingestion system interfaces with GitLab to detect changes
4. The CI/CD Pipeline integrates with GitLab for continuous integration and deployment

### Data Flow

#### Codebase Analysis Flow

1. Raw code is processed by the IRL Pipeline
2. The pipeline generates Core Artifacts
3. The artifacts are stored in Cloud Storage
4. The extension loads the artifacts
5. The artifacts are used to provide context and visualizations to the user

#### Delta Ingestion Flow

1. Code changes are detected in GitLab
2. The Delta Ingestion system analyzes the changes
3. The system selectively updates the affected parts of the IRL representation
4. The updated artifacts are stored in Cloud Storage
5. The extension loads the updated artifacts

#### User Interaction Flow

1. User interacts with the Chat Interface
2. The Bracket System processes the interaction
3. The Context Engine identifies relevant code
4. The API Handler sends requests to AI providers
5. The response is processed and displayed to the user

This comprehensive architecture enables Bracket to provide a powerful AI-assisted code understanding and development experience, with a focus on scalability, flexibility, and user experience.
