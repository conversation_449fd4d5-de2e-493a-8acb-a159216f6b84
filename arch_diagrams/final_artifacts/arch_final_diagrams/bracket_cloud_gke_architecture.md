# Bracket Cloud GKE Architecture

This architecture diagram illustrates the cloud infrastructure that powers the Bracket system, with a focus on the Google Kubernetes Engine (GKE) deployment and associated cloud services.

```mermaid
flowchart TB
    classDef gkeComponent fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef networkComponent fill:#e8f5e9,stroke:#2e7d32,stroke-width:2px
    classDef storageComponent fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px
    classDef securityComponent fill:#ffebee,stroke:#b71c1c,stroke-width:2px
    classDef monitoringComponent fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef serviceComponent fill:#e0f7fa,stroke:#006064,stroke-width:2px
    classDef userComponent fill:#f5f5f5,stroke:#424242,stroke-width:2px
    
    %% External Users
    User[Developer/User]:::userComponent
    GitLabSystem[GitLab System]:::userComponent
    
    %% Google Cloud Platform
    subgraph GCP["Google Cloud Platform"]
        direction TB
        
        %% Networking
        subgraph Networking["Networking"]
            direction TB
            VPC[Virtual Private Cloud]:::networkComponent
            Subnets[Subnets]:::networkComponent
            CloudNAT[Cloud NAT]:::networkComponent
            LoadBalancer[Cloud Load Balancer]:::networkComponent
            CloudDNS[Cloud DNS]:::networkComponent
            CloudCDN[Cloud CDN]:::networkComponent
        end
        
        %% GKE Cluster
        subgraph GKECluster["GKE Cluster bracket-irl-cluster"]
            direction TB
            
            %% Node Pools
            subgraph NodePools["Node Pools"]
                direction TB
                DefaultPool[Default Pool e2-standard-4]:::gkeComponent
                HighMemPool[High Memory Pool e2-highmem-8]:::gkeComponent
                GPUPool[GPU Pool Optional]:::gkeComponent
            end
            
            %% Kubernetes Resources
            subgraph K8sNamespace["Kubernetes Namespace bracket-irl"]
                direction TB
                
                %% Core Services
                subgraph CoreServices["Core Services"]
                    direction TB
                    OrchestratorService[Orchestrator Service]:::serviceComponent
                    RepoMapperService[Repository Mapper Service]:::serviceComponent
                    DomainAnalyzerService[Domain Analyzer Service]:::serviceComponent
                    FileDomainMapperService[File-Domain Mapper Service]:::serviceComponent
                    DomainFileRepomapService[Domain-File Repomap Service]:::serviceComponent
                    DiagramGeneratorService[Diagram Generator Service]:::serviceComponent
                end
                
                %% Monitoring
                subgraph Monitoring["Monitoring"]
                    direction TB
                    PrometheusService[Prometheus]:::monitoringComponent
                    GrafanaService[Grafana]:::monitoringComponent
                end
                
                %% Kubernetes Resources
                ConfigMaps[ConfigMaps]:::gkeComponent
                Secrets[Secrets]:::gkeComponent
                PersistentVolumes[Persistent Volumes]:::gkeComponent
                Services[Services]:::gkeComponent
                Ingress[Ingress]:::gkeComponent
                HPA[Horizontal Pod Autoscaler]:::gkeComponent
            end
        end
        
        %% Storage
        subgraph Storage["Storage"]
            direction TB
            ArtifactRegistry[Artifact Registry]:::storageComponent
            CloudStorage[Cloud Storage]:::storageComponent
            FileStore[Filestore NFS]:::storageComponent
        end
        
        %% Security
        subgraph Security["Security"]
            direction TB
            IAM[Identity and Access Management]:::securityComponent
            SecretManager[Secret Manager]:::securityComponent
            KMS[Key Management Service]:::securityComponent
            ManagedCertificates[Managed Certificates]:::securityComponent
        end
        
        %% Monitoring & Logging
        subgraph MonitoringLogging["Monitoring & Logging"]
            direction TB
            CloudMonitoring[Cloud Monitoring]:::monitoringComponent
            CloudLogging[Cloud Logging]:::monitoringComponent
            ErrorReporting[Error Reporting]:::monitoringComponent
            CloudTrace[Cloud Trace]:::monitoringComponent
        end
        
        %% Serverless
        subgraph Serverless["Serverless"]
            direction TB
            CloudFunctions[Cloud Functions]:::serviceComponent
            CloudRun[Cloud Run]:::serviceComponent
            PubSub[Pub/Sub]:::serviceComponent
        end
    end
    
    %% Connections
    
    %% External Connections
    User --> LoadBalancer
    GitLabSystem --> LoadBalancer
    GitLabSystem --> CloudFunctions
    
    %% Networking Connections
    LoadBalancer --> Ingress
    VPC --> Subnets
    Subnets --> GKECluster
    CloudNAT --> VPC
    CloudDNS --> LoadBalancer
    CloudCDN --> LoadBalancer
    
    %% GKE Internal Connections
    Ingress --> Services
    Services --> CoreServices
    ConfigMaps --> CoreServices
    Secrets --> CoreServices
    PersistentVolumes --> CoreServices
    HPA --> CoreServices
    
    %% Service Connections
    OrchestratorService --> RepoMapperService
    OrchestratorService --> DomainAnalyzerService
    OrchestratorService --> FileDomainMapperService
    OrchestratorService --> DomainFileRepomapService
    OrchestratorService --> DiagramGeneratorService
    
    RepoMapperService --> PersistentVolumes
    DomainAnalyzerService --> PersistentVolumes
    FileDomainMapperService --> PersistentVolumes
    DomainFileRepomapService --> PersistentVolumes
    DiagramGeneratorService --> PersistentVolumes
    
    %% Monitoring Connections
    CoreServices --> PrometheusService
    PrometheusService --> GrafanaService
    PrometheusService --> CloudMonitoring
    
    %% Storage Connections
    CoreServices --> CloudStorage
    CoreServices --> ArtifactRegistry
    PersistentVolumes --> FileStore
    
    %% Security Connections
    CoreServices --> SecretManager
    IAM --> GKECluster
    KMS --> Secrets
    ManagedCertificates --> Ingress
    
    %% Monitoring & Logging Connections
    CoreServices --> CloudLogging
    CloudLogging --> ErrorReporting
    CoreServices --> CloudTrace
    
    %% Serverless Connections
    CloudFunctions --> PubSub
    PubSub --> OrchestratorService
    CloudRun --> CloudStorage
```

## Bracket Cloud GKE Architecture: Detailed Explanation

This architecture diagram illustrates the cloud infrastructure that powers the Bracket system, with a focus on the Google Kubernetes Engine (GKE) deployment and associated cloud services.

### Google Cloud Platform Components

#### Networking

- **Virtual Private Cloud (VPC)**: Private network for Bracket resources
- **Subnets**: Network segmentation for different resource types
- **Cloud NAT**: Provides outbound internet connectivity for private resources
- **Cloud Load Balancer**: Distributes incoming traffic to appropriate services
- **Cloud DNS**: Manages DNS records for Bracket domains
- **Cloud CDN**: Content delivery network for static assets

#### GKE Cluster (bracket-irl-cluster)

##### Node Pools
- **Default Pool (e2-standard-4)**: General-purpose nodes for most services
- **High Memory Pool (e2-highmem-8)**: Memory-optimized nodes for LLM processing
- **GPU Pool (Optional)**: GPU-enabled nodes for specialized workloads

##### Kubernetes Namespace (bracket-irl)

###### Core Services
- **Orchestrator Service**
- **Repository Mapper Service**
- **Domain Structuring Service**
- **File-Domain Mapper Service**
- **Domain-File Repostiory mapping Service**
- **Diagram Generator Service**

###### Monitoring
- **Prometheus**: Metrics collection and monitoring
- **Grafana**: Visualization and dashboards for metrics

###### Kubernetes Resources
- **ConfigMaps**: Configuration data for services
- **Secrets**: Sensitive configuration data
- **Persistent Volumes**: Durable storage for services
- **Services**: Network endpoints for pods
- **Ingress**: External access to services
- **Horizontal Pod Autoscaler**: Automatic scaling based on metrics

#### Storage

- **Artifact Registry**: Storage for Docker images
- **Cloud Storage**: Object storage for artifacts and data
- **Filestore (NFS)**: Network file system for shared storage

#### Security

- **Identity and Access Management (IAM)**: Access control for GCP resources
- **Secret Manager**: Secure storage for sensitive data
- **Key Management Service (KMS)**: Encryption key management
- **Managed Certificates**: SSL/TLS certificate management

#### Monitoring & Logging

- **Cloud Monitoring**: Monitoring for GCP resources
- **Cloud Logging**: Centralized logging
- **Error Reporting**: Error aggregation and analysis
- **Cloud Trace**: Distributed tracing

#### Serverless

- **Cloud Functions**: Event-driven serverless functions
- **Cloud Run**: Containerized serverless applications
- **Pub/Sub**: Messaging service for event-driven architecture

### Key Architectural Patterns

#### 1. Microservices Architecture
The Bracket system is built as a collection of microservices, each with a specific responsibility in the overall pipeline. This architecture allows for independent scaling, deployment, and maintenance of each component.

#### 2. Kubernetes Orchestration
GKE provides a managed Kubernetes environment that handles container orchestration, scaling, and self-healing. The system uses Kubernetes resources like Services, ConfigMaps, and Secrets to manage configuration and connectivity.

#### 3. Scalability
The architecture is designed to scale horizontally with demand:
- Multiple node pools for different workload types
- Horizontal Pod Autoscaler for automatic scaling
- Cloud Load Balancer for distributing traffic

#### 4. High Availability
The system is designed for high availability:
- Regional GKE cluster with nodes across multiple zones
- Stateless services with persistent storage
- Load balancing across multiple instances

#### 5. Security
Security is implemented at multiple layers:
- Network security with VPC and subnets
- Identity and access management with IAM
- Secret management with Secret Manager and Kubernetes Secrets
- Encryption with KMS
- SSL/TLS with Managed Certificates

#### 6. Monitoring and Observability
Comprehensive monitoring and logging:
- Prometheus for metrics collection
- Grafana for visualization
- Cloud Monitoring for GCP resource monitoring
- Cloud Logging for centralized logging
- Error Reporting for error analysis
- Cloud Trace for distributed tracing

#### 7. CI/CD Integration
The architecture supports continuous integration and deployment:
- Artifact Registry for Docker images
- Kubernetes manifests managed with Kustomize
- Integration with GitLab CI/CD

### Data Flow

1. External requests come through the Cloud Load Balancer
2. Ingress routes requests to the appropriate service
3. Orchestrator Service coordinates the pipeline
4. Each service performs its specific task in the pipeline
5. Data is stored in Cloud Storage and Persistent Volumes
6. Results are made available through the Orchestrator Service API

### Deployment and Management

The system is deployed and managed using:
- Kustomize for Kubernetes resource management
- Base configuration with environment-specific overlays
- GitLab CI/CD for automated deployment
- Prometheus and Grafana for monitoring
- Cloud Logging for log management
