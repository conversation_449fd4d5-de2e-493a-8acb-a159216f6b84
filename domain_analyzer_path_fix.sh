#!/bin/bash
# <PERSON>ript to fix the path issue in the domain analyzer service

# Stop the services
cd ../../
./stop.sh

# Create a patch for the domain analyzer service
cat > domain_analyzer_path_fix.patch << EOF
--- src/services/analyzer.py.orig
+++ src/services/analyzer.py
@@ -123,7 +123,14 @@
                 elif hasattr(self.storage_client, 'get_full_path'):
                     full_repomap_path = self.storage_client.get_full_path(repomap_path)
                     full_output_path = self.storage_client.get_full_path(output_path)
+                # Special case for absolute paths starting with /app/data/
+                elif repomap_path.startswith('/app/data/'):
+                    # Use the path directly without prepending /app/data/ again
+                    full_repomap_path = repomap_path
+                    full_output_path = self.storage_client._get_full_path(output_path)
+                    logger.info(f"Using absolute path directly: {full_repomap_path}")
                 else:
+                    logger.info(f"Using fallback path resolution for: {repomap_path}")
                     # Fallback to direct paths
                     full_repomap_path = repomap_path
                     full_output_path = output_path
EOF

# Apply the patch to the domain analyzer service
cd domain-analyzer-service
patch -p0 < ../domain_analyzer_path_fix.patch

# Rebuild and restart the services
cd ..
./start.sh

# Run the orchestrator E2E test
cd tests/e2e
./run_orchestrator_e2e_test.sh
