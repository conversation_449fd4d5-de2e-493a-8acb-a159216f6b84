#!/bin/bash
set -e

echo "🧹 Cleaning VS Code extension cache..."
# On macOS, the VS Code extension cache is in ~/Library/Application Support/Code/CachedExtensionVSIXs
if [[ "$OSTYPE" == "darwin"* ]]; then
  rm -rf ~/Library/Application\ Support/Code/CachedExtensionVSIXs/*
  rm -rf ~/Library/Application\ Support/Code/Cache/*
  rm -rf ~/Library/Application\ Support/Code/CachedData/*
fi

# On Linux, the VS Code extension cache is in ~/.config/Code/CachedExtensionVSIXs
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
  rm -rf ~/.config/Code/CachedExtensionVSIXs/*
  rm -rf ~/.config/Code/Cache/*
  rm -rf ~/.config/Code/CachedData/*
fi

# On Windows, the VS Code extension cache is in %USERPROFILE%\.vscode\extensions
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" ]]; then
  rm -rf "$USERPROFILE/.vscode/extensions/CachedExtensionVSIXs/*"
  rm -rf "$USERPROFILE/AppData/Roaming/Code/Cache/*"
  rm -rf "$USERPROFILE/AppData/Roaming/Code/CachedData/*"
fi

echo "📦 Rebuilding extension..."
./rebuild-extension.sh

echo "🚀 Launching VS Code with extension..."
code --extensionDevelopmentPath="$(pwd)/bracket_ext" --disable-extensions --new-window
