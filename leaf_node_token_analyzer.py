#!/usr/bin/env python3
"""
Leaf Node Token Analyzer

This script analyzes the token counts for leaf domains in a codebase.
It reads domain traces and function data, calculates token counts for each leaf domain,
and saves the results to a CSV file.

Usage:
    python leaf_node_token_analyzer.py --domain-traces <path> --functions-parquet <path> --output <path>
"""

import os
import yaml
import json
import pandas as pd
import argparse
import logging
import csv
from typing import Dict, List, Set, Any, Optional
from dataclasses import dataclass, field

# Import token counting function
# You may need to adjust this import based on your project structure
DEFAULT_ENCODING_NAME = "cl100k_base"

try:
    from bracket_core.llm.tokens import num_tokens_from_string
except ImportError:
    # Fallback implementation if the import fails
    import tiktoken
    
    def num_tokens_from_string(string: str) -> int:
        """Returns the number of tokens in a text string."""
        encoding = tiktoken.encoding_for_model(DEFAULT_ENCODING_NAME)
        return len(encoding.encode(string))

# Configure logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class LeafDomainTokenAnalysis:
    """Analysis results for leaf domain token counts."""
    domain_trace: str
    function_count: int
    token_count: int
    system_prompt_tokens: int
    user_prompt_tokens: int
    total_tokens: int
    level: int
    missing_functions: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for CSV export."""
        return {
            "domain_trace": self.domain_trace,
            "function_count": self.function_count,
            "token_count": self.token_count,
            "system_prompt_tokens": self.system_prompt_tokens,
            "user_prompt_tokens": self.user_prompt_tokens,
            "total_tokens": self.total_tokens,
            "level": self.level,
            "missing_functions_count": len(self.missing_functions)
        }

class LeafNodeTokenAnalyzer:
    """
    Analyzes token counts for leaf domains in a codebase.
    """
    
    def __init__(
        self,
        domain_traces_yaml_path: str,
        functions_parquet_path: str,
        output_csv_path: str,
    ):
        """Initialize the analyzer.
        
        Args:
            domain_traces_yaml_path: Path to the domain traces YAML file
            functions_parquet_path: Path to the functions parquet file
            output_csv_path: Path to save the CSV output
            model_name: Name of the model to use for token counting
        """
        self.domain_traces_yaml_path = domain_traces_yaml_path
        self.functions_parquet_path = functions_parquet_path
        self.output_csv_path = output_csv_path
        
        # Initialize data structures
        self.domain_traces = {}
        self.function_data = {}
        self.simplified_path_map = {}
        self.all_domains = set()
        self.leaf_domains = set()
        self.domain_levels = {}
        self.domain_children = {}
        self.domain_parents = {}
        
        # Analysis results
        self.results = []
        
    def read_domain_traces(self) -> Dict[str, List[str]]:
        """Read domain traces from the YAML file."""
        logger.info(f"Reading domain traces from: {self.domain_traces_yaml_path}")
        
        try:
            with open(self.domain_traces_yaml_path, 'r') as f:
                yaml_data = yaml.safe_load(f)
            
            domain_traces = yaml_data.get('domain_traces', {})
            logger.info(f"Found {len(domain_traces)} domain traces")
            
            self.domain_traces = domain_traces
            return domain_traces
            
        except Exception as e:
            logger.error(f"Error reading domain traces: {e}")
            return {}
    
    def read_function_data(self) -> Dict[str, Dict[str, Any]]:
        """Read function data from the parquet file."""
        logger.info(f"Reading function data from: {self.functions_parquet_path}")
        
        try:
            df = pd.read_parquet(self.functions_parquet_path)
            
            # Create a dictionary mapping node_ids to function data
            function_data = {}
            # Also create a mapping from simplified paths to node_ids for easier lookup
            simplified_path_map = {}
            
            for _, row in df.iterrows():
                node_id = row.get('node_id', '')
                if not node_id:
                    # Skip entries without node_id
                    continue
                
                # Extract relevant function data
                function_info = {
                    'description': row.get('description', ''),
                    'text': row.get('text', ''),
                }
                
                # Parse calls if available
                calls = row.get('calls', [])
                if isinstance(calls, str):
                    try:
                        calls = json.loads(calls)
                    except json.JSONDecodeError:
                        calls = [calls] if calls else []
                
                function_info['calls'] = calls
                
                # Parse call_contexts if available
                call_contexts = row.get('call_contexts', [])
                if isinstance(call_contexts, str):
                    try:
                        call_contexts = json.loads(call_contexts)
                    except json.JSONDecodeError:
                        call_contexts = [call_contexts] if call_contexts else []
                
                function_info['call_contexts'] = call_contexts
                
                # Store in the main function data dictionary using node_id as key
                function_data[node_id] = function_info
                
                # Create simplified path versions for easier lookup
                if ':' in node_id:
                    file_path, func_name = node_id.split(':', 1)
                    # Create simplified versions without full path
                    if '/' in file_path:
                        # Get just the filename and function
                        filename = file_path.split('/')[-1]
                        simplified_sig = f"{filename}:{func_name}"
                        simplified_path_map[simplified_sig] = node_id
                        
                        # Also store just the relative path without any base directories
                        for i in range(1, len(file_path.split('/'))):
                            rel_path = '/'.join(file_path.split('/')[-i:])
                            rel_sig = f"{rel_path}:{func_name}"
                            simplified_path_map[rel_sig] = node_id
            
            logger.info(f"Loaded data for {len(function_data)} functions")
            logger.info(f"Created {len(simplified_path_map)} simplified path mappings for easier lookup")
            
            self.function_data = function_data
            self.simplified_path_map = simplified_path_map
            return function_data
            
        except Exception as e:
            logger.error(f"Error reading function data: {e}")
            return {}
    
    def build_domain_hierarchy(self) -> None:
        """Build a hierarchical representation of domains from domain traces."""
        logger.info("Building domain hierarchy from domain traces")
        
        # Initialize domain tracking structures
        self.all_domains = set()  # All domains at all levels
        self.leaf_domains = set()  # Leaf domains (no children)
        self.domain_parents = {}  # Maps domain to its parent domain
        self.domain_children = {}  # Maps domain to its child domains
        self.domain_levels = {}  # Maps domain to its hierarchy level (0 = root)
        
        for trace_str, functions in self.domain_traces.items():
            # Split the trace string into components
            components = trace_str.split(' -> ')
            
            # Track each domain in the trace
            current_path = ""
            parent_path = ""
            
            # Navigate/build the hierarchy
            for i, component in enumerate(components):
                # Build the current path
                if current_path:
                    current_path += f" -> {component}"
                    parent_path = current_path.rsplit(' -> ', 1)[0]
                else:
                    current_path = component
                    parent_path = ""  # Root level has no parent
                
                # Add to tracking structures
                self.all_domains.add(current_path)
                self.domain_levels[current_path] = i
                
                # Set up parent-child relationships
                if parent_path:
                    if parent_path not in self.domain_children:
                        self.domain_children[parent_path] = set()
                    self.domain_children[parent_path].add(current_path)
                    self.domain_parents[current_path] = parent_path
        
        # Identify leaf domains (those with no children)
        for domain in self.all_domains:
            if domain not in self.domain_children or not self.domain_children[domain]:
                self.leaf_domains.add(domain)
        
        logger.info(f"Built domain hierarchy with {len(self.all_domains)} total domains")
        logger.info(f"Identified {len(self.leaf_domains)} leaf domains (no children)")
    
    def get_system_prompt(self) -> str:
        """Get the system prompt used for leaf domain diagram generation."""
        system_prompt = """You are an expert software architect who creates clear, informative mermaid diagrams to visualize code architecture.
Your task is to create a detailed mermaid diagram that represents the LOGICAL RELATIONSHIPS and INTERACTIONS between functions in a specific domain of a codebase.

Guidelines for creating the diagram:
1. Focus on the LOGICAL PURPOSE and ROLE of each function within the domain
2. Emphasize how functions work together to accomplish domain goals
3. Show meaningful relationships and dependencies between functions
4. Highlight the conceptual flow of data and control between functions
5. Group functions by their logical purpose or the feature they support
6. Represent the domain's core concepts and how functions implement them
7. Show how functions collaborate to implement domain behaviors
8. Illustrate key abstractions and patterns used in the domain
9. Include important domain-specific data structures and their transformations
10. Show initialization sequences and important process flows
11. DO NOT create a simple procedural flowchart - focus on logical relationships
12. DO NOT use tooltips or click actions - they consume unnecessary tokens
13. The diagram should be 3000-6000 tokens in size to provide comprehensive detail

Styling Guidelines (IMPORTANT):
1. Use a VERTICAL layout rather than horizontal for better readability
2. Use PASTEL COLORS for all nodes and subgraphs - avoid bright or dark colors
3. Use this consistent color scheme:
   - Core domain functions: pastel blue (#D4F1F9)
   - Supporting/utility functions: pastel yellow (#FFF8DC)
   - Data structure handlers: pastel green (#E0F8E0)
   - Error handling functions: pastel red (#FFE4E1)
   - Initialization/setup functions: pastel purple (#E6E6FA)
   - Logical groupings/subgraphs: very light gray (#F8F8F8) with pastel borders
4. Use rounded rectangles for most nodes: node[shape="rounded-rectangle"]
5. Use different node shapes to represent different types of functions when appropriate
6. Use consistent line thickness and arrow styles
7. Ensure proper spacing between nodes and subgraphs
8. Follow strict mermaid.js syntax to ensure the diagram renders correctly
9. Avoid use of symbols like ( ) and avoid adding any notes

Your output should ONLY contain a valid mermaid diagram enclosed in triple backticks with the mermaid tag.
Ensure the diagram follows proper mermaid.js syntax and is renderable without any syntax errors."""
        return system_prompt
    
    def get_user_prompt_template(self) -> str:
        """Get the user prompt template used for leaf domain diagram generation."""
        user_prompt_template = """Create a detailed mermaid diagram for the leaf domain: {domain_trace} (Hierarchy Level: {level})

This is a leaf domain with no subdomains. Focus on showing the LOGICAL RELATIONSHIPS and INTERACTIONS between functions, not just their implementation details.

Here are the functions in this domain:

{function_details}

Please generate a comprehensive mermaid diagram that shows:
1. The LOGICAL PURPOSE of each function within the domain context
2. How functions COLLABORATE to implement domain behaviors
3. The MEANINGFUL RELATIONSHIPS and dependencies between functions
4. How functions are GROUPED by their logical purpose or features they support
5. The domain's CORE CONCEPTS and how functions implement them
6. Important DOMAIN-SPECIFIC DATA STRUCTURES and their transformations
7. Key ABSTRACTIONS and PATTERNS used in the domain

IMPORTANT STYLING REQUIREMENTS:
- Use a VERTICAL layout
- Use PASTEL COLORS for all nodes and subgraphs
- Follow the color scheme specified in the system prompt
- Ensure the diagram is well-structured and easy to read
- Follow strict mermaid.js syntax to ensure the diagram renders correctly
- Avoid use of symbols like ( ) and avoid adding any notes

Make sure to capture the LOGICAL RELATIONSHIPS between functions, not just their procedural flow.
DO NOT create a simple procedural flowchart - focus on meaningful interactions and relationships.
DO NOT use tooltips or click actions in the diagram."""
        return user_prompt_template
    
    def analyze_leaf_domain(self, domain_trace: str) -> LeafDomainTokenAnalysis:
        """Analyze token count for a leaf domain."""
        logger.info(f"Analyzing token count for leaf domain: {domain_trace}")
        
        # Get functions for this domain
        functions = self.domain_traces.get(domain_trace, [])
        
        # Get hierarchy level
        level = self.domain_levels.get(domain_trace, 0)
        
        # Prepare function data for the prompt
        function_details = []
        missing_functions = []
        found_functions = []
        
        for node_id in functions:
            # Try direct lookup first
            if node_id in self.function_data:
                func_data = self.function_data[node_id]
                found_functions.append(node_id)
                
                # Format function details (same structure as in domain_diagram_generator.py)
                func_detail = {
                    'node_id': node_id,
                    'description': func_data.get('description', ''),
                    'text': func_data.get('text', ''),
                }
                
                function_details.append(func_detail)
                
            # Try lookup via simplified path map
            elif node_id in self.simplified_path_map:
                actual_node_id = self.simplified_path_map[node_id]
                func_data = self.function_data[actual_node_id]
                found_functions.append(node_id)
                
                # Format function details
                func_detail = {
                    'node_id': node_id,
                    'description': func_data.get('description', ''),
                    'text': func_data.get('text', ''),
                }
                
                function_details.append(func_detail)
                
            else:
                missing_functions.append(node_id)
        
        # Log the results
        if found_functions:
            logger.info(f"Found {len(found_functions)} functions for domain {domain_trace}")
        if missing_functions:
            logger.warning(f"Missing {len(missing_functions)} functions for domain {domain_trace}")
        
        # Get the system prompt
        system_prompt = self.get_system_prompt()
        
        # Get the user prompt
        user_prompt_template = self.get_user_prompt_template()
        function_details_json = json.dumps(function_details, indent=2)
        user_prompt = user_prompt_template.format(
            domain_trace=domain_trace,
            level=level,
            function_details=function_details_json
        )
        
        # Calculate token counts
        function_details_tokens = num_tokens_from_string(function_details_json)
        system_prompt_tokens = num_tokens_from_string(system_prompt)
        user_prompt_tokens = num_tokens_from_string(user_prompt)
        total_tokens = system_prompt_tokens + user_prompt_tokens
        
        # Create analysis result
        analysis = LeafDomainTokenAnalysis(
            domain_trace=domain_trace,
            function_count=len(function_details),
            token_count=function_details_tokens,
            system_prompt_tokens=system_prompt_tokens,
            user_prompt_tokens=user_prompt_tokens,
            total_tokens=total_tokens,
            level=level,
            missing_functions=missing_functions
        )
        
        return analysis
    
    def analyze_all_leaf_domains(self) -> List[LeafDomainTokenAnalysis]:
        """Analyze token counts for all leaf domains."""
        logger.info(f"Analyzing token counts for {len(self.leaf_domains)} leaf domains")
        
        results = []
        for domain_trace in self.leaf_domains:
            analysis = self.analyze_leaf_domain(domain_trace)
            results.append(analysis)
            
            # Log progress periodically
            if len(results) % 10 == 0:
                logger.info(f"Analyzed {len(results)}/{len(self.leaf_domains)} leaf domains")
        
        # Sort results by token count (descending)
        results.sort(key=lambda x: x.total_tokens, reverse=True)
        
        self.results = results
        return results
    
    def save_results_to_csv(self) -> None:
        """Save analysis results to CSV file."""
        logger.info(f"Saving analysis results to: {self.output_csv_path}")
        
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(os.path.abspath(self.output_csv_path)), exist_ok=True)
        
        # Convert results to dictionaries
        result_dicts = [result.to_dict() for result in self.results]
        
        # Write to CSV
        with open(self.output_csv_path, 'w', newline='') as f:
            if result_dicts:
                writer = csv.DictWriter(f, fieldnames=result_dicts[0].keys())
                writer.writeheader()
                writer.writerows(result_dicts)
                
        logger.info(f"Saved analysis results for {len(self.results)} leaf domains to CSV")
    
    def generate_summary_statistics(self) -> Dict[str, Any]:
        """Generate summary statistics for the analysis results."""
        if not self.results:
            return {}
        
        total_domains = len(self.results)
        total_functions = sum(r.function_count for r in self.results)
        total_tokens = sum(r.total_tokens for r in self.results)
        avg_tokens_per_domain = total_tokens / total_domains if total_domains > 0 else 0
        avg_functions_per_domain = total_functions / total_domains if total_domains > 0 else 0
        
        # Count domains exceeding token thresholds
        exceed_100k = sum(1 for r in self.results if r.total_tokens > 100000)
        exceed_50k = sum(1 for r in self.results if r.total_tokens > 50000)
        exceed_30k = sum(1 for r in self.results if r.total_tokens > 30000)
        exceed_20k = sum(1 for r in self.results if r.total_tokens > 20000)
        
        # Get min, max, median token counts
        token_counts = [r.total_tokens for r in self.results]
        min_tokens = min(token_counts) if token_counts else 0
        max_tokens = max(token_counts) if token_counts else 0
        median_tokens = sorted(token_counts)[len(token_counts)//2] if token_counts else 0
        
        summary = {
            "total_leaf_domains": total_domains,
            "total_functions": total_functions,
            "total_tokens": total_tokens,
            "avg_tokens_per_domain": avg_tokens_per_domain,
            "avg_functions_per_domain": avg_functions_per_domain,
            "min_tokens": min_tokens,
            "max_tokens": max_tokens,
            "median_tokens": median_tokens,
            "domains_exceeding_100k_tokens": exceed_100k,
            "domains_exceeding_50k_tokens": exceed_50k,
            "domains_exceeding_30k_tokens": exceed_30k,
            "domains_exceeding_20k_tokens": exceed_20k,
        }
        
        return summary
    
    def run_analysis(self) -> Dict[str, Any]:
        """Run the complete analysis pipeline."""
        # Read data
        self.read_domain_traces()
        self.read_function_data()
        
        # Build hierarchy
        self.build_domain_hierarchy()
        
        # Analyze leaf domains
        self.analyze_all_leaf_domains()
        
        # Save results
        self.save_results_to_csv()
        
        # Generate and return summary statistics
        summary = self.generate_summary_statistics()
        
        # Log summary
        logger.info("Analysis Summary:")
        for key, value in summary.items():
            logger.info(f"  {key}: {value}")
        
        return summary

def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Analyze token counts for leaf domains")
    # parser.add_argument("--domain-traces", required=True, help="Path to the domain traces YAML file")
    # parser.add_argument("--functions-parquet", required=True, help="Path to the functions parquet file")
    # parser.add_argument("--output", required=True, help="Path to save the CSV output")
    # parser.add_argument("--model", default="claude-3-sonnet-20240229", help="Model name for token counting")
    
    args = parser.parse_args()

    args.domain_traces = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/domain_traces.yaml"
    args.functions_parquet = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/semantic_documented_functions.parquet"
    args.output = "experiments/gitlab/token_analysis/leaf_token_analysis.csv"
    
    analyzer = LeafNodeTokenAnalyzer(
        domain_traces_yaml_path=args.domain_traces,
        functions_parquet_path=args.functions_parquet,
        output_csv_path=args.output,
    )
    
    analyzer.run_analysis()

if __name__ == "__main__":
    main()
