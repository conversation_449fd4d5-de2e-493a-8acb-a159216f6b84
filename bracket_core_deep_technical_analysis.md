# Bracket Core Deep Technical Analysis: Sequential Technology Breakdown

## Conceptual Foundation

The Bracket Core system implements a revolutionary **Cognitive Codebase Understanding** paradigm that transforms static source code into a dynamic, hierarchical mental model. Unlike traditional approaches that treat code as text to be indexed, this system treats code as a complex knowledge graph with semantic relationships, architectural patterns, and domain-specific logic that can be understood and reasoned about.

## Stage 1: Enhanced Repository Mapping - The Foundation Layer

### Core Technology: Multi-Language Abstract Syntax Tree (AST) Analysis

**Location**: `bracket_core/bracket_irl/bracket_complete_repomap.py`

#### Deep Technical Concepts:

**1. Universal Code Parsing Engine**
```
Input: Raw source files across multiple languages
Process: Tree-sitter based parsing → Unified AST representation
Output: Language-agnostic symbol extraction
```

The system uses Tree-sitter parsers to create Abstract Syntax Trees for each source file. Unlike traditional parsers that focus on compilation, this system extracts **semantic symbols** - functions, classes, methods, variables - along with their complete contextual information.

**Key Innovation**: **Context-Preserving Symbol Extraction**
- Each symbol retains its scope hierarchy (module → class → method)
- Function signatures are normalized across languages
- Import relationships are tracked with full resolution paths
- Symbol definitions include start/end line numbers for precise location

**2. Intelligent Function Filtering Algorithm**
```
Algorithm: Adaptive Importance Ranking
1. Calculate base importance score per function
2. Apply configurable filters (top_percentage, min/max functions)
3. Preserve architectural significance (entry points, public APIs)
4. Maintain domain representation balance
```

**Novel Approach**: Instead of simple token counting, the system uses **architectural significance scoring**:
- Public API functions get higher weights
- Functions with many callers get boosted scores
- Test functions are intelligently filtered but not completely excluded
- Framework/library entry points are preserved

**3. Parallel Batch Processing with Incremental Persistence**
```
Architecture: Producer-Consumer with Checkpointing
- Producer: File discovery and queuing
- Consumer Pool: Parallel AST processing workers
- Checkpointing: Incremental saves every N files
- Recovery: Resume from last checkpoint on failure
```

**Technical Innovation**: **Resumable Processing Architecture**
- Each batch creates a checkpoint with processed file hashes
- Failed processing can resume from exact failure point
- Memory usage is bounded regardless of codebase size
- Progress tracking provides ETA and throughput metrics

#### Output Artifact: Structured Repository Map
```json
{
  "files": {
    "path/to/file.py": {
      "functions": [
        {
          "name": "function_name",
          "signature": "def function_name(param1: str, param2: int) -> bool",
          "start_line": 45,
          "end_line": 67,
          "scope": "module.class.function_name",
          "calls": ["other_function", "external.api.call"],
          "imports": ["module1", "module2.submodule"]
        }
      ],
      "classes": [...],
      "imports": [...]
    }
  },
  "statistics": {
    "total_files": 1247,
    "total_functions": 8934,
    "token_count": 2847392,
    "filtered_token_count": 1423696
  }
}
```

## Stage 2: Hybrid Knowledge Graph Generation - Relationship Discovery

### Core Technology: Lightweight Semantic Relationship Extraction

**Location**: `bracket_core/hybrid_kg.py`

#### Deep Technical Concepts:

**1. Function Node Modeling**
```python
@dataclass
class FunctionNode:
    node_id: str          # Unique identifier: file_path:function_name
    signature: str        # Complete function signature
    calls: List[str]      # Functions called by this function
    call_contexts: List[str]  # Full context of each call
    containing_class: str # Parent class if method
    module_path: str      # Module hierarchy
```

**Key Innovation**: **Context-Aware Call Analysis**
- Extracts function calls without full symbol resolution (performance optimization)
- Preserves call context (the surrounding code that makes the call)
- Identifies method calls vs. function calls vs. library calls
- Tracks import-based call relationships

**2. Smart Library Call Filtering**
```
Algorithm: Prefix-Based Library Detection
1. Maintain language-specific library prefixes (pd., np., os., etc.)
2. Filter out standard library calls to reduce noise
3. Preserve domain-specific library usage patterns
4. Track external API usage for dependency analysis
```

**Technical Innovation**: **Noise Reduction Without Information Loss**
- Filters common library calls (pandas, numpy, etc.) that don't represent business logic
- Preserves domain-specific library usage that indicates architectural patterns
- Maintains external API calls that represent system boundaries
- Creates clean call graphs focused on business logic relationships

**3. NetworkX Graph Construction**
```
Graph Structure:
- Nodes: Function definitions with rich metadata
- Edges: Call relationships with context
- Attributes: Scope, module, class hierarchy
- Weights: Call frequency and context strength
```

#### Output Artifact: Semantic Knowledge Graph
```python
# NetworkX MultiDiGraph with rich node/edge attributes
graph.nodes['file.py:function_name'] = {
    'signature': 'def function_name(params)',
    'module_path': 'package.module',
    'containing_class': 'ClassName',
    'start_line': 45,
    'calls': ['other_func', 'api.call']
}
```

## Stage 3: LLM-Powered Domain Analysis - Semantic Understanding

### Core Technology: Hierarchical Domain Discovery Through Large Language Models

**Location**: `bracket_core/bracket_irl/bracket_domain_analysis.py`

#### Deep Technical Concepts:

**1. Chunked Analysis with Context Preservation**
```
Algorithm: Sliding Window with Overlap
1. Split repository map into token-sized chunks (60K tokens)
2. Maintain function context across chunk boundaries
3. Preserve import relationships and call graphs
4. Ensure no function is split across chunks
```

**Key Innovation**: **Context-Aware Chunking Strategy**
- Chunks are created at logical boundaries (file or class level)
- Function signatures and their immediate call relationships are kept together
- Import statements are replicated across relevant chunks
- Cross-chunk references are tracked and resolved

**2. Specialized Domain Discovery Prompts**
```
Prompt Engineering Strategy:
1. Structural Analysis: "Analyze function signatures and call patterns"
2. Semantic Grouping: "Identify logical domains based on functionality"
3. Hierarchy Construction: "Build 3-5 level domain hierarchy"
4. Naming Convention: "Generate descriptive domain names"
```

**Technical Innovation**: **Progressive Refinement Through Multiple LLM Passes**
- First pass: Identify functional clusters based on call patterns
- Second pass: Analyze semantic similarity of function names/signatures
- Third pass: Build hierarchical structure with proper nesting
- Fourth pass: Generate human-readable names and descriptions

**3. Multi-Provider LLM Architecture**
```
Provider Selection Logic:
1. Token count analysis determines model requirements
2. Task complexity influences provider choice
3. Fallback mechanisms handle provider failures
4. Cost optimization through strategic model selection
```

**Novel Approach**: **Intelligent Model Switching**
- OpenAI GPT-4 for complex architectural analysis
- Claude for detailed semantic understanding
- OpenRouter/Gemini for high-token-count processing
- Automatic fallback chains for reliability

#### Output Artifact: Hierarchical Domain Structure
```yaml
areas:
  - name: "User Management"
    description: "Handles user authentication, authorization, and profile management"
    subareas:
      - name: "Authentication"
        description: "Login, logout, session management"
        subareas:
          - name: "OAuth Integration"
            functions: ["oauth_login", "refresh_token", "validate_token"]
          - name: "Local Authentication"
            functions: ["local_login", "password_hash", "session_create"]
      - name: "User Profiles"
        functions: ["get_profile", "update_profile", "delete_user"]
```

## Stage 4: Intelligent File-Domain Mapping - Precision Classification

### Core Technology: Dynamic Schema-Based Batch Classification

**Location**: `bracket_core/bracket_irl/bracket_file_domain_mapper_batched.py`

#### Deep Technical Concepts:

**1. Dynamic Pydantic Schema Generation**
```python
def create_domain_file_mapping_schema(leaf_domains: List[str]) -> type:
    """
    Dynamically creates a Pydantic model with fields for each discovered domain.
    This ensures type safety and validation for LLM outputs.
    """
    annotations = {}
    for domain in leaf_domains:
        clean_domain = domain.replace('/', '_').replace('-', '_')
        annotations[clean_domain] = List[str]  # List of file paths
    
    return type('DynamicDomainFileMapping', (BaseModel,), {
        '__annotations__': annotations,
        'model_config': {'extra': 'forbid'}  # Strict validation
    })
```

**Key Innovation**: **Runtime Schema Adaptation**
- Schema is generated based on discovered domains from Stage 3
- Ensures LLM outputs conform to expected structure
- Provides type safety and validation
- Enables structured data extraction from natural language responses

**2. Two-Pass Analysis with Accuracy Tracking**
```
Algorithm: Iterative Refinement
Pass 1: Initial Classification
  - Batch files into groups of ~50
  - Classify based on file paths and function signatures
  - Track confidence scores for each classification

Pass 2: Refinement and Validation
  - Re-examine low-confidence classifications
  - Cross-reference with related files
  - Validate consistency across similar files
  - Calculate final accuracy metrics
```

**Technical Innovation**: **Self-Improving Classification**
- First pass provides baseline classification with confidence scores
- Second pass focuses on uncertain classifications for improvement
- Accuracy metrics guide whether second pass is needed
- Unclassified files are tracked for manual review

**3. Intelligent Batching Strategy**
```
Batching Algorithm:
1. Group files by directory structure (related files together)
2. Balance batch sizes for optimal LLM processing
3. Include context files (imports, dependencies) in batches
4. Maintain file relationship information across batches
```

#### Output Artifact: Validated File-Domain Mappings
```yaml
domain_mappings:
  "User Management/Authentication/OAuth Integration":
    - "src/auth/oauth_handler.py"
    - "src/auth/oauth_models.py"
    - "tests/auth/test_oauth.py"
  "User Management/Authentication/Local Authentication":
    - "src/auth/local_auth.py"
    - "src/auth/password_utils.py"

accuracy_metrics:
  first_pass_accuracy: 0.87
  second_pass_accuracy: 0.94
  unclassified_files: 23
  total_files: 1247

## Stage 5: Domain-File RepoMap Integration - Unified Knowledge Synthesis

### Core Technology: Multi-Artifact Integration with Statistical Enhancement

**Location**: `bracket_core/bracket_irl/bracket_domain_file_repomap.py`

#### Deep Technical Concepts:

**1. Hierarchical Data Structure Synthesis**
```python
# Integration Algorithm
def integrate_artifacts(domain_mapping, repository_map):
    """
    Combines domain hierarchy with file mappings and code content
    to create a unified knowledge representation.
    """
    integrated_structure = {}

    for domain_path, file_list in domain_mapping.items():
        domain_data = {
            'files': {},
            'statistics': {},
            'relationships': {}
        }

        for file_path in file_list:
            # Extract code content from repository map
            file_content = repository_map['files'][file_path]

            # Combine function signatures into domain context
            domain_data['files'][file_path] = {
                'functions': file_content['functions'],
                'classes': file_content['classes'],
                'imports': file_content['imports'],
                'code_summary': generate_code_summary(file_content)
            }
```

**Key Innovation**: **Semantic Code Summarization**
- Each file's code is summarized while preserving semantic meaning
- Function signatures are combined into coherent domain descriptions
- Import relationships are analyzed for cross-domain dependencies
- Code complexity metrics are calculated per domain

**2. Statistical Domain Analysis**
```
Metrics Calculation:
1. Token Count Estimation: Fast approximation based on line counts
2. Function Density: Functions per file ratio
3. Complexity Scoring: Cyclomatic complexity estimation
4. Dependency Analysis: Cross-domain relationship strength
5. Domain Cohesion: Internal vs external call ratios
```

**Technical Innovation**: **Domain Quality Metrics**
- Cohesion scores indicate how well-defined domain boundaries are
- Coupling metrics show inter-domain dependencies
- Complexity scores help identify refactoring opportunities
- Size metrics guide architectural decisions

#### Output Artifact: Unified Domain-File Knowledge Base
```json
{
  "User Management/Authentication/OAuth Integration": {
    "files": {
      "src/auth/oauth_handler.py": {
        "functions": [...],
        "code_summary": "Handles OAuth 2.0 authentication flow with token management",
        "complexity_score": 7.2,
        "external_dependencies": ["requests", "jwt", "cryptography"]
      }
    },
    "statistics": {
      "num_files": 3,
      "estimated_tokens": 4567,
      "function_count": 12,
      "complexity_average": 6.8,
      "cohesion_score": 0.89
    }
  }
}
```

## Stage 6: Advanced Diagram Generation - Visual Knowledge Representation

### Core Technology: Hierarchical Mermaid Diagram Synthesis

**Location**: `bracket_core/bracket_irl/bracket_domain_diagram_generator.py`

#### Deep Technical Concepts:

**1. Multi-Level Diagram Generation Strategy**
```
Diagram Hierarchy:
Level 1: Leaf Domain Diagrams (individual domain internals)
Level 2: Intermediate Domain Diagrams (subdomain relationships)
Level 3: Top-Level Architecture Diagrams (system overview)
Level 4: Cross-Domain Integration Diagrams (system boundaries)
```

**Key Innovation**: **Context-Aware Diagram Generation**
- Leaf diagrams show internal function relationships and data flow
- Intermediate diagrams show subdomain interactions and boundaries
- Top-level diagrams show architectural patterns and system structure
- Integration diagrams show external dependencies and API boundaries

**2. Intelligent LLM Provider Selection**
```python
def select_optimal_provider(domain_content, token_count):
    """
    Selects the best LLM provider based on content complexity and token count.
    """
    if token_count > 45000:
        return "openrouter_gemini"  # High token capacity
    elif requires_complex_reasoning(domain_content):
        return "openai_gpt4"        # Best reasoning capability
    elif requires_detailed_analysis(domain_content):
        return "claude_sonnet"      # Best for detailed analysis
    else:
        return "openai_gpt4_mini"   # Cost-effective for simple diagrams
```

**Technical Innovation**: **Adaptive Model Selection**
- Token count analysis determines model capacity requirements
- Content complexity analysis influences model selection
- Cost optimization through strategic provider switching
- Quality assessment guides future provider choices

**3. Diagram Post-Processing Pipeline**
```
Post-Processing Steps:
1. Syntax Validation: Ensure valid Mermaid syntax
2. Layout Optimization: Improve visual clarity and flow
3. Label Enhancement: Add descriptive labels and annotations
4. Relationship Clarification: Highlight important connections
5. Styling Application: Apply consistent visual themes
```

**Novel Approach**: **Semantic Diagram Enhancement**
- Diagrams are analyzed for clarity and completeness
- Missing relationships are identified and added
- Visual layout is optimized for comprehension
- Consistent styling improves professional appearance

#### Output Artifact: Hierarchical Diagram Collection
```
diagrams/
├── leaf_domains/
│   ├── user_management_authentication_oauth.mmd
│   ├── user_management_profiles.mmd
│   └── data_processing_etl.mmd
├── intermediate_domains/
│   ├── user_management.mmd
│   └── data_processing.mmd
├── top_level/
│   └── system_architecture.mmd
└── integration/
    └── external_apis.mmd
```

## Advanced Systems: Embedding-Based Semantic Analysis

### Core Technology: Vector Space Semantic Clustering

**Location**: `bracket_core/exp_embedding/cluster_embeddings.py`

#### Deep Technical Concepts:

**1. Semantic Similarity Graph Construction**
```python
class SemanticSimilarityGraph:
    def build_similarity_graph(self, embeddings_df):
        """
        Constructs a graph where nodes are functions and edges represent
        semantic similarity based on embedding cosine similarity.
        """
        # Calculate pairwise cosine similarities
        similarity_matrix = cosine_similarity(embeddings_df.values)

        # Create graph with similarity threshold filtering
        for i, func1 in enumerate(embeddings_df.index):
            for j, func2 in enumerate(embeddings_df.index):
                if i != j and similarity_matrix[i][j] > self.similarity_threshold:
                    self.graph.add_edge(func1, func2,
                                      weight=similarity_matrix[i][j])
```

**Key Innovation**: **Embedding-Driven Domain Discovery**
- Function embeddings capture semantic meaning beyond syntax
- Similarity graphs reveal hidden relationships between functions
- Community detection algorithms identify natural domain boundaries
- Hierarchical clustering creates multi-level domain structures

**2. Community Detection with Louvain Algorithm**
```
Algorithm: Multi-Resolution Community Detection
1. Build similarity graph from function embeddings
2. Apply Louvain algorithm with varying resolution parameters
3. Create hierarchical community structure
4. Name communities using LLM analysis of member functions
5. Validate communities against architectural patterns
```

**Technical Innovation**: **Multi-Resolution Domain Analysis**
- Different resolution parameters reveal different granularity levels
- Hierarchical communities naturally form domain hierarchies
- Community quality metrics guide optimal resolution selection
- LLM naming provides human-interpretable domain labels

## Advanced Systems: Global Localization Engine

### Core Technology: Query-Driven Relevance Analysis

**Location**: `bracket_core/localisation/global_localisation.py`

#### Deep Technical Concepts:

**1. Trace-Based Relevance Scoring**
```python
@dataclass
class TraceInfo:
    trace_str: str                    # Domain path string
    mermaid_diagrams: List[str]       # Associated diagrams
    files: List[str]                  # Files in domain
    file_contents: Dict[str, str]     # Actual code content

    def calculate_relevance(self, query: str) -> float:
        """
        Uses LLM to evaluate how relevant this domain trace is to the query.
        Combines multiple signals: file names, function signatures, code content.
        """
```

**Key Innovation**: **Multi-Signal Relevance Analysis**
- Domain taxonomy provides hierarchical context
- Mermaid diagrams offer visual relationship understanding
- File contents enable deep semantic analysis
- LLM evaluation combines all signals for relevance scoring

**2. Token-Aware Processing with Provider Switching**
```
Processing Strategy:
1. Estimate token count for query + domain content
2. If tokens > 45K, switch to high-capacity provider (Gemini)
3. If tokens < 45K, use standard provider (GPT-4)
4. Apply rate limiting and retry logic
5. Aggregate results with confidence weighting
```

This completes the deep technical analysis of the sequential technology flow. Each stage builds sophisticated understanding that enables the next stage to operate at a higher level of abstraction, ultimately creating a true cognitive model of the codebase.
```
