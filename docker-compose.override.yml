version: '3.8'

services:
  # Repository Mapper Service
  repo-mapper-service:
    volumes:
      - /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/tests/e2e/results/test_repo:/repo
      - /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/tests/e2e/results:/app/tests/e2e/results
    environment:
      - PYTHONPATH=/app:/

  # Domain Analyzer Service
  domain-analyzer-service:
    volumes:
      - /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/tests/e2e/results/test_repo:/repo
      - /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/tests/e2e/results:/app/tests/e2e/results
    environment:
      - PYTHONPATH=/app:/

  # Domain-File Repomap Service
  domain-file-repomap-service:
    volumes:
      - /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/tests/e2e/results/test_repo:/repo
    environment:
      - PYTHONPATH=/app:/

  # File-Domain Mapper Service
  file-domain-mapper-service:
    volumes:
      - /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/tests/e2e/results/test_repo:/repo
    environment:
      - PYTHONPATH=/app:/

  # Diagram Generator Service
  diagram-generator-service:
    volumes:
      - /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/tests/e2e/results/test_repo:/repo
    environment:
      - PYTHONPATH=/app:/

  # Orchestrator Service
  orchestrator-service:
    volumes:
      - /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/tests/e2e/results/test_repo:/repo
    environment:
      - PYTHONPATH=/app:/
