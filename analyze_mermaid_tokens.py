#!/usr/bin/env python
"""
Analyze Mermaid Diagram <PERSON> and Function Counts

This script analyzes a domain taxonomy JSON file to:
1. Count tokens in mermaid diagrams for each trace (from top domain to leaf node)
2. Count the number of functions in each leaf node

Usage:
    python analyze_mermaid_tokens.py --taxonomy-json <path> [--output <path>] [--model <model_name>]
"""

import os
import json
import argparse
import pandas as pd
import time
from typing import Dict, List, Any, Optional

# Import token counting utility
try:
    from bracket_core.llm.tokens import num_tokens_from_string
except ImportError:
    # Fallback to tiktoken if bracket_core.llm.tokens is not available
    import tiktoken
    
    def num_tokens_from_string(string: str, model_name: str = "gpt-4o-mini") -> int:
        """Returns the number of tokens in a text string."""
        encoding = tiktoken.encoding_for_model(model_name)
        return len(encoding.encode(string))

class MermaidTokenAnalyzer:
    """
    Analyzes mermaid diagram tokens and function counts in a domain taxonomy.
    """
    
    def __init__(
        self,
        taxonomy_json_path: str,
        output_path: Optional[str] = None,
        model_name: str = "gpt-4o-mini"
    ):
        """
        Initialize the analyzer.
        
        Args:
            taxonomy_json_path: Path to the domain taxonomy JSON file
            output_path: Path to save the CSV output (optional)
            model_name: Model name to use for token counting
        """
        self.taxonomy_json_path = taxonomy_json_path
        self.output_path = output_path or os.path.join(
            os.path.dirname(taxonomy_json_path),
            f"mermaid_tokens_{time.strftime('%Y%m%d-%H%M%S')}.csv"
        )
        self.model_name = model_name
        
        # Initialize data structures
        self.taxonomy_data = {}
        self.results = []
        
    def read_taxonomy_json(self) -> Dict[str, Any]:
        """
        Read the domain taxonomy JSON file.
        
        Returns:
            Dictionary containing domain taxonomy data
        """
        print(f"Reading domain taxonomy JSON file: {self.taxonomy_json_path}")
        try:
            with open(self.taxonomy_json_path, 'r') as f:
                taxonomy_data = json.load(f)
            return taxonomy_data
        except Exception as e:
            print(f"Error reading domain taxonomy JSON file: {e}")
            raise
    
    def analyze_node(self, node: Dict[str, Any], path: List[str] = None, level: int = 0) -> None:
        """
        Recursively analyze nodes in the taxonomy tree.
        
        Args:
            node: Current node in the taxonomy tree
            path: Current path in the taxonomy tree
            level: Current level in the hierarchy
        """
        if path is None:
            path = []
        
        name = node.get('name', 'Unknown')
        current_path = path + [name]
        full_path = " -> ".join(current_path)
        
        # Get functions and diagram
        functions = node.get('functions', [])
        diagram = node.get('diagram')
        
        # Calculate tokens for diagram
        diagram_tokens = 0
        if diagram:
            diagram_content = diagram
            diagram_tokens = num_tokens_from_string(diagram_content, self.model_name)
        
        # Check if this is a leaf node (no children)
        is_leaf = 'children' not in node or not node['children']
        
        # Add to results
        self.results.append({
            'full_path': full_path,
            'level': level,
            'is_leaf': is_leaf,
            'function_count': len(functions),
            'has_diagram': diagram is not None,
            'diagram_tokens': diagram_tokens
        })
        
        # Process children recursively
        if not is_leaf and 'children' in node:
            for child in node['children']:
                self.analyze_node(child, current_path, level + 1)
    
    def run_analysis(self) -> pd.DataFrame:
        """
        Run the analysis and return the results as a DataFrame.
        
        Returns:
            DataFrame containing the analysis results
        """
        # Read taxonomy JSON
        self.taxonomy_data = self.read_taxonomy_json()
        
        # Analyze nodes recursively
        self.analyze_node(self.taxonomy_data)
        print(f"Analyzed {len(self.results)} traces")
        
        # Convert results to DataFrame
        df = pd.DataFrame(self.results)
        
        # Sort by level and then by diagram tokens
        df.sort_values(['level', 'diagram_tokens'], ascending=[True, False], inplace=True)
        
        # Save to CSV
        df.to_csv(self.output_path, index=False)
        print(f"Results saved to: {self.output_path}")
        
        return df
    
    def print_summary(self, df: pd.DataFrame) -> None:
        """
        Print a summary of the analysis results.
        
        Args:
            df: DataFrame containing the analysis results
        """
        # Calculate statistics
        total_traces = len(df)
        leaf_traces = df['is_leaf'].sum()
        total_diagram_tokens = df['diagram_tokens'].sum()
        total_functions = df[df['is_leaf']]['function_count'].sum()
        
        # Print summary
        print("\nSummary:")
        print(f"Total traces analyzed: {total_traces}")
        print(f"Leaf nodes: {leaf_traces}")
        print(f"Total mermaid diagram tokens: {total_diagram_tokens:,}")
        print(f"Total functions in leaf nodes: {total_functions:,}")
        
        # Print top 5 traces by diagram tokens
        print("\nTop 5 traces by mermaid diagram tokens:")
        top_traces = df.sort_values('diagram_tokens', ascending=False).head(5)
        for _, row in top_traces.iterrows():
            print(f"  {row['full_path']} - {row['diagram_tokens']:,} tokens")
        
        # Print token distribution by level
        print("\nMermaid token distribution by level:")
        level_stats = df.groupby('level').agg({
            'diagram_tokens': 'sum',
            'function_count': lambda x: x[df['is_leaf']].sum()
        }).reset_index()
        
        for _, row in level_stats.iterrows():
            print(f"  Level {int(row['level'])}: {row['diagram_tokens']:,} diagram tokens")
        
        # Print leaf node function distribution
        leaf_df = df[df['is_leaf']]
        print("\nLeaf node function distribution:")
        print(f"  Min functions: {leaf_df['function_count'].min()}")
        print(f"  Max functions: {leaf_df['function_count'].max()}")
        print(f"  Average functions: {leaf_df['function_count'].mean():.2f}")
        
        # Print function count ranges
        print("\nLeaf node function count ranges:")
        ranges = [(0, 0), (1, 10), (11, 50), (51, 100), (101, float('inf'))]
        for start, end in ranges:
            if end == float('inf'):
                count = len(leaf_df[leaf_df['function_count'] > start])
                print(f"  > {start} functions: {count} leaf nodes")
            else:
                count = len(leaf_df[(leaf_df['function_count'] >= start) & (leaf_df['function_count'] <= end)])
                print(f"  {start}-{end} functions: {count} leaf nodes")

def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Analyze mermaid diagram tokens and function counts")
    parser.add_argument("--taxonomy-json", required=True, help="Path to the domain taxonomy JSON file")
    parser.add_argument("--output", help="Path to save the CSV output")
    parser.add_argument("--model", default="gpt-4o-mini", help="Model name to use for token counting")
    
    args = parser.parse_args()
    
    try:
        # Create analyzer
        analyzer = MermaidTokenAnalyzer(
            taxonomy_json_path=args.taxonomy_json,
            output_path=args.output,
            model_name=args.model
        )
        
        # Run analysis
        start_time = time.time()
        df = analyzer.run_analysis()
        end_time = time.time()
        
        print(f"\nAnalysis completed in {end_time - start_time:.2f} seconds")
        
        # Print summary
        analyzer.print_summary(df)
        
        return 0
    
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        print(traceback.format_exc())
        return 1

if __name__ == "__main__":
    exit(main())
