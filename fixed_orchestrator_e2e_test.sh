#!/bin/bash
# Run the end-to-end test for the Bracket IRL microservices pipeline using the orchestrator
# Fixed version to address artifact path issues

set -e  # Exit on error

# Set the repository directory to the test repository
export REPO_DIR=$(pwd)/results/test_repo

# Create the test repository directory if it doesn't exist
mkdir -p "$REPO_DIR"
echo "Created test repository directory: $REPO_DIR"

# Add a sample file to the test repository so it's not empty
echo "# Sample repository for testing" > "$REPO_DIR/README.md"
echo "def sample_function():" > "$REPO_DIR/sample.py"
echo "    return 'Hello, World!'" >> "$REPO_DIR/sample.py"

# Add a more complex file to test domain analysis
cat > "$REPO_DIR/complex_module.py" << EOF
"""
A more complex module for testing domain analysis.
"""

class DataProcessor:
    """Process data from various sources."""

    def __init__(self, config=None):
        self.config = config or {}
        self.data = []

    def load_data(self, source):
        """Load data from a source."""
        print(f"Loading data from {source}")
        self.data = [1, 2, 3, 4, 5]
        return self.data

    def process_data(self):
        """Process the loaded data."""
        if not self.data:
            raise ValueError("No data loaded")

        return [x * 2 for x in self.data]

    def save_results(self, destination):
        """Save processed results."""
        processed = self.process_data()
        print(f"Saving results to {destination}: {processed}")
        return True

class APIClient:
    """Client for API interactions."""

    def __init__(self, base_url):
        self.base_url = base_url
        self.auth_token = None

    def authenticate(self, username, password):
        """Authenticate with the API."""
        print(f"Authenticating {username} with API at {self.base_url}")
        self.auth_token = "sample_token"
        return self.auth_token

    def get_data(self, endpoint):
        """Get data from an API endpoint."""
        if not self.auth_token:
            raise ValueError("Not authenticated")

        print(f"Getting data from {self.base_url}/{endpoint}")
        return {"status": "success", "data": [1, 2, 3]}

    def post_data(self, endpoint, data):
        """Post data to an API endpoint."""
        if not self.auth_token:
            raise ValueError("Not authenticated")

        print(f"Posting data to {self.base_url}/{endpoint}: {data}")
        return {"status": "success", "id": 123}

def main():
    """Main function to demonstrate the classes."""
    # Initialize data processor
    processor = DataProcessor({"option": "value"})

    # Load and process data
    processor.load_data("sample_source")
    results = processor.process_data()
    processor.save_results("sample_destination")

    # Use API client
    client = APIClient("https://api.example.com")
    client.authenticate("user", "pass")
    api_data = client.get_data("users")
    response = client.post_data("items", {"name": "New Item"})

    print("All operations completed successfully")
    return 0

if __name__ == "__main__":
    main()
EOF

echo "Using test repository: $REPO_DIR"
echo "This will be mounted as /repo in the Docker containers"

# Create results directory for artifacts
mkdir -p data/artifacts

# Create docker-compose.override.yml to mount the test repository
# FIXED: Ensure consistent artifact paths across services
cat > docker-compose.override.yml << EOF
version: '3.8'

services:
  # Repository Mapper Service
  repo-mapper-service:
    volumes:
      - ${REPO_DIR}:/repo
      - $(pwd)/data:/app/data
    environment:
      - PYTHONPATH=/app:/
      - STORAGE_PATH=/app/data

  # Domain Analyzer Service
  domain-analyzer-service:
    volumes:
      - ${REPO_DIR}:/repo
      - $(pwd)/data:/app/data
    environment:
      - PYTHONPATH=/app:/
      - STORAGE_PATH=/app/data
      - OPENAI_API_KEY=${OPENAI_API_KEY}

  # Domain-File Repomap Service
  domain-file-repomap-service:
    volumes:
      - ${REPO_DIR}:/repo
      - $(pwd)/data:/app/data
    environment:
      - PYTHONPATH=/app:/
      - STORAGE_PATH=/app/data

  # File-Domain Mapper Service
  file-domain-mapper-service:
    volumes:
      - ${REPO_DIR}:/repo
      - $(pwd)/data:/app/data
    environment:
      - PYTHONPATH=/app:/
      - STORAGE_PATH=/app/data
      - OPENAI_API_KEY=${OPENAI_API_KEY}

  # Diagram Generator Service
  diagram-generator-service:
    volumes:
      - ${REPO_DIR}:/repo
      - $(pwd)/data:/app/data
    environment:
      - PYTHONPATH=/app:/
      - STORAGE_PATH=/app/data
      - OPENAI_API_KEY=${OPENAI_API_KEY}

  # Orchestrator Service
  orchestrator-service:
    volumes:
      - ${REPO_DIR}:/repo
      - $(pwd)/data:/app/data
    environment:
      - PYTHONPATH=/app:/
      - STORAGE_PATH=/app/data
EOF

# Rebuild and restart the services
echo "Rebuilding and restarting services..."
cd ../../
docker compose down
docker compose build
docker compose up -d
cd tests/e2e

# Wait for services to start
echo "Waiting for services to start..."
echo "This may take up to 60 seconds..."

# Wait for all services to be healthy with a timeout
MAX_RETRIES=12  # 12 retries * 5 seconds = 60 seconds max wait time
RETRY_COUNT=0
ALL_HEALTHY=false

while [ $RETRY_COUNT -lt $MAX_RETRIES ] && [ "$ALL_HEALTHY" != "true" ]; do
  echo "Checking service health (attempt $((RETRY_COUNT+1))/$MAX_RETRIES)..."

  # Check orchestrator-service health
  ORCHESTRATOR_HEALTH=$(curl -s http://localhost:8000/health || echo '{"status":"error"}')
  ORCHESTRATOR_STATUS=$(echo $ORCHESTRATOR_HEALTH | grep -o '"status":"[^"]*"' | cut -d'"' -f4)

  # Check repo-mapper-service health
  REPO_MAPPER_HEALTH=$(curl -s http://localhost:8001/health || echo '{"status":"error"}')
  REPO_MAPPER_STATUS=$(echo $REPO_MAPPER_HEALTH | grep -o '"status":"[^"]*"' | cut -d'"' -f4)

  # Check domain-analyzer-service health
  DOMAIN_ANALYZER_HEALTH=$(curl -s http://localhost:8002/health || echo '{"status":"error"}')
  DOMAIN_ANALYZER_STATUS=$(echo $DOMAIN_ANALYZER_HEALTH | grep -o '"status":"[^"]*"' | cut -d'"' -f4)

  # Check file-domain-mapper-service health
  FILE_DOMAIN_HEALTH=$(curl -s http://localhost:8003/health || echo '{"status":"error"}')
  FILE_DOMAIN_STATUS=$(echo $FILE_DOMAIN_HEALTH | grep -o '"status":"[^"]*"' | cut -d'"' -f4)

  # Check domain-file-repomap-service health
  DOMAIN_FILE_HEALTH=$(curl -s http://localhost:8004/health || echo '{"status":"error"}')
  DOMAIN_FILE_STATUS=$(echo $DOMAIN_FILE_HEALTH | grep -o '"status":"[^"]*"' | cut -d'"' -f4)

  # Check diagram-generator-service health
  DIAGRAM_HEALTH=$(curl -s http://localhost:8005/health || echo '{"status":"error"}')
  DIAGRAM_STATUS=$(echo $DIAGRAM_HEALTH | grep -o '"status":"[^"]*"' | cut -d'"' -f4)

  echo "Orchestrator: $ORCHESTRATOR_STATUS"
  echo "Repository Mapper: $REPO_MAPPER_STATUS"
  echo "Domain Analyzer: $DOMAIN_ANALYZER_STATUS"
  echo "File-Domain Mapper: $FILE_DOMAIN_STATUS"
  echo "Domain-File Repomap: $DOMAIN_FILE_STATUS"
  echo "Diagram Generator: $DIAGRAM_STATUS"

  if [ "$ORCHESTRATOR_STATUS" = "ok" ] && [ "$REPO_MAPPER_STATUS" = "ok" ] && [ "$DOMAIN_ANALYZER_STATUS" = "ok" ] && [ "$FILE_DOMAIN_STATUS" = "ok" ] && [ "$DOMAIN_FILE_STATUS" = "ok" ] && [ "$DIAGRAM_STATUS" = "ok" ]; then
    ALL_HEALTHY=true
    echo "All services are healthy!"
  else
    echo "Waiting for services to become healthy..."
    sleep 5
    RETRY_COUNT=$((RETRY_COUNT+1))
  fi
done

if [ "$ALL_HEALTHY" != "true" ]; then
  echo "Warning: Not all services are healthy after 60 seconds. Proceeding anyway..."
fi

# Verify that the repository is accessible inside the container
echo "Verifying repository is accessible inside the container..."
REPO_CHECK=$(docker exec microservices-repo-mapper-service-1 ls -la /repo || echo "Failed to access /repo")
echo "Repository contents in container:"
echo "$REPO_CHECK"

if [[ "$REPO_CHECK" == *"Failed to access /repo"* ]]; then
  echo "Error: Repository is not accessible inside the container!"
  echo "Check that the volume mount is correct in docker-compose.override.yml"
  exit 1
fi

# Run the orchestrator-based E2E test inside the orchestrator container
echo "Running orchestrator-based E2E test..."

# Copy the test script to the orchestrator container
docker cp test_orchestrator_e2e.py microservices-orchestrator-service-1:/app/test_orchestrator_e2e.py

# Create the results directory in the container
docker exec microservices-orchestrator-service-1 mkdir -p /app/test_results

# Run the test inside the container
docker exec -e OPENAI_API_KEY=$OPENAI_API_KEY microservices-orchestrator-service-1 python /app/test_orchestrator_e2e.py \
  --repo-dir /repo \
  --api-key $OPENAI_API_KEY \
  --results-dir /app/test_results \
  --docker-mode

# Copy the results back to the host
docker cp microservices-orchestrator-service-1:/app/test_results/. results/orchestrator_e2e/

# Store the exit code
EXIT_CODE=$?

# Clean up the override file
rm docker-compose.override.yml

# Exit with the test exit code
exit $EXIT_CODE
