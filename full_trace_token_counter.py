#!/usr/bin/env python
"""
Count Mermaid Tokens for Full Top-to-Leaf Traces

This script counts the mermaid diagram tokens for complete traces from top to leaf nodes
in a domain taxonomy and stores the results in a CSV file.
"""

import os
import json
import pandas as pd
import time
from typing import Dict, List, Any, Tuple

# Import token counting utility
try:
    from bracket_core.llm.tokens import num_tokens_from_string
except ImportError:
    # Fallback to tiktoken if bracket_core.llm.tokens is not available
    import tiktoken
    
    def num_tokens_from_string(string: str, model_name: str = "gpt-4o-mini") -> int:
        """Returns the number of tokens in a text string."""
        encoding = tiktoken.encoding_for_model(model_name)
        return len(encoding.encode(string))

def count_full_trace_tokens(taxonomy_json_path, output_csv_path=None, model_name="gpt-4o-mini"):
    """
    Count mermaid diagram tokens for complete top-to-leaf traces in a domain taxonomy.
    
    Args:
        taxonomy_json_path: Path to the domain taxonomy JSON file
        output_csv_path: Path to save the CSV output (optional)
        model_name: Model name to use for token counting
    
    Returns:
        Path to the generated CSV file
    """
    # Set default output path if not provided
    if not output_csv_path:
        output_csv_path = os.path.join(
            "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/token_analysis",
            f"full_traces_mermaid_tokens_{time.strftime('%Y%m%d-%H%M%S')}.csv"
        )
    
    print(f"Reading domain taxonomy from: {taxonomy_json_path}")
    
    # Read the taxonomy JSON
    with open(taxonomy_json_path, 'r') as f:
        taxonomy_data = json.load(f)
    
    # List to store complete traces (only top-to-leaf)
    complete_traces = []
    
    # Function to recursively collect complete traces
    def collect_traces(node, path=None, token_counts=None, function_counts=None):
        if path is None:
            path = []
        if token_counts is None:
            token_counts = []
        if function_counts is None:
            function_counts = []
        
        name = node.get('name', 'Unknown')
        current_path = path + [name]
        
        # Get diagram and functions
        diagram = node.get('diagram', '')
        functions = node.get('functions', [])
        
        # Calculate tokens for diagram
        diagram_tokens = 0
        if diagram:
            diagram_tokens = num_tokens_from_string(diagram)
        
        # Update token and function counts
        current_token_counts = token_counts + [diagram_tokens]
        current_function_counts = function_counts + [len(functions)]
        
        # Check if this is a leaf node
        is_leaf = 'children' not in node or not node['children']
        
        if is_leaf:
            # This is a complete trace from top to leaf, add it to results
            complete_traces.append({
                'full_trace': " -> ".join(current_path),
                'levels': len(current_path),
                'token_counts': current_token_counts,
                'function_counts': current_function_counts
            })
        else:
            # Process children recursively
            for child in node.get('children', []):
                collect_traces(child, current_path, current_token_counts, current_function_counts)
    
    # Start collecting traces from the root
    collect_traces(taxonomy_data)
    
    print(f"Collected {len(complete_traces)} complete top-to-leaf traces")
    
    # Prepare data for CSV
    csv_data = []
    for trace in complete_traces:
        # Create a row for each trace
        row = {
            'full_trace': trace['full_trace'],
            'total_levels': trace['levels'],
            'total_mermaid_tokens': sum(trace['token_counts']),
            'leaf_functions': trace['function_counts'][-1]  # Functions at the leaf level
        }
        
        # Add token counts for each level
        for i, tokens in enumerate(trace['token_counts']):
            row[f'level_{i}_tokens'] = tokens
        
        csv_data.append(row)
    
    # Convert to DataFrame
    df = pd.DataFrame(csv_data)
    
    # Sort by total mermaid tokens (descending)
    df.sort_values('total_mermaid_tokens', ascending=False, inplace=True)
    
    # Save to CSV
    df.to_csv(output_csv_path, index=False)
    
    print(f"Results saved to: {output_csv_path}")
    
    # Print a simple summary
    print("\nSummary:")
    print(f"Total complete traces: {len(df)}")
    print(f"Total mermaid tokens across all traces: {df['total_mermaid_tokens'].sum():,}")
    print(f"Average mermaid tokens per trace: {df['total_mermaid_tokens'].mean():.2f}")
    print(f"Maximum mermaid tokens in a trace: {df['total_mermaid_tokens'].max():,}")
    print(f"Total functions in leaf nodes: {df['leaf_functions'].sum():,}")
    
    return output_csv_path

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Count mermaid tokens for complete top-to-leaf traces")
    parser.add_argument("--taxonomy-json", help="Path to the domain taxonomy JSON file")
    parser.add_argument("--output", help="Path to save the CSV output")
    parser.add_argument("--model", default="gpt-4o-mini", help="Model name to use for token counting")
    
    args = parser.parse_args()

    args.taxonomy_json = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/domain_taxonomy.json"
    args.output_dir = os.path.join(os.path.dirname(args.taxonomy_json), "token_analysis")
    args.output_csv_path = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/token_analysis/"   
    
    count_full_trace_tokens(
        taxonomy_json_path=args.taxonomy_json,
        output_csv_path=args.output,
        model_name=args.model
    )
