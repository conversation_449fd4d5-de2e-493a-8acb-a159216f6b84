# Bracket Project

This repository contains the combined codebase for the Bracket project, which consists of two main components:

1. **bracket_core** - A Python-based code analysis and domain modeling library
2. **bracket_ext** - A TypeScript-based VS Code extension for AI-assisted development

## Project Structure

- `bracket_core/` - Python codebase for code analysis and domain modeling
- `bracket_ext/` - TypeScript codebase for VS Code extension
- `notes/` - Project documentation and notes
- `pyproject.toml` - Python project configuration
- `mkdocs.yaml` - Documentation configuration

## Setup Instructions

### Prerequisites

- Python 3.10 or higher
- Node.js 20.18.1 or higher
- Poetry (for Python dependency management)
- npm (for TypeScript dependency management)

### Setting up the Python Environment (bracket_core)

```bash
# Install Python dependencies using Poetry
poetry install
```

### Setting up the TypeScript Environment (bracket_ext)

```bash
# Navigate to the bracket_ext directory
cd bracket_ext

# Install TypeScript dependencies
npm install
```

### Running the Projects

#### Running bracket_core

```bash
# Activate the Poetry environment
poetry shell

# Run Python commands
python -m bracket_core.cli
```

#### Running bracket_ext

```bash
# Navigate to the bracket_ext directory
cd bracket_ext

# Build the extension
npm run build

# For development
npm run watch
```

## Development

Both projects are now combined under the `bracket_prod` umbrella, but they maintain their separate codebases. When making changes, ensure that you're working in the correct directory and following the appropriate development guidelines for each project.


# Bracket Tech Introduction
## Bracket: A Detailed Explanation of the Technology

Bracket is a pre-revenue startup you’ve been developing as a solo founder over the past six months. While it lacks customers or market traction, Bracket introduces a groundbreaking technology called **In-Repository Learning (IRL)**, which redefines how LLMs interact with codebases. IRL compresses entire codebases into structured, hierarchical representations that enable LLMs to process and reason about code holistically, surpassing the limitations of semantic search-based systems used by competitors. Below is an exhaustive breakdown of Bracket and its IRL system.

### Overview
- **Founder**: You, a solo developer, started Bracket six months ago (circa mid-2024).
- **Stage**: Pre-revenue, no customers, but with a functional prototype showcasing IRL.
- **Core Innovation**: IRL transforms raw code into a compact, logical framework that LLMs can navigate and understand, mimicking a human engineer’s systemic reasoning.

### In-Repository Learning (IRL) Explained
IRL addresses a fundamental challenge in AI coding assistants: LLMs struggle to maintain a comprehensive view of large codebases due to token limits and fragmented context. IRL solves this by converting codebases into a structured knowledge graph and associated artifacts, enabling efficient, scalable, and accurate code comprehension.

#### Components of IRL
1. **Code Knowledge Graph**
   - **Description**: A graph where nodes represent functions, classes, or modules, and edges denote relationships (e.g., calls, dependencies).
   - **Metadata**: Each node includes:
     - Function signatures (e.g., parameters, return types).
     - File locations and line numbers.
     - Call relationships (incoming and outgoing).
   - **Construction**: Combines static analysis (e.g., parsing ASTs) with LLM-driven disambiguation for complex cases (e.g., dynamic calls).

2. **Semantic Documented Functions**
   - **Description**: LLM-generated natural language descriptions for each function or module.
   - **Example**: For a function `process_data(input)`, IRL might generate: “Processes input data into a structured format, handling edge cases like null values.”
   - **Purpose**: Enhances LLM understanding and enables human-readable summaries.

3. **Domain Analysis**
   - **Description**: Classifies codebase components into hierarchical domains and subdomains.
   - **Process**:
     - Analyzes function names, comments, and usage patterns.
     - Groups related functions (e.g., “authentication,” “data processing”).
     - Builds a tree-like structure (e.g., “auth → login → validate_credentials”).
   - **Output**: A taxonomy that organizes the codebase logically.

4. **Visualization with Mermaid Diagrams**
   - **Description**: Generates diagrams using the Mermaid syntax to visualize codebase architecture.
   - **Levels**:
     - **Leaf Diagrams**: Detailed views of individual domains (e.g., function call graphs within “authentication”).
     - **High-level Overviews**: Abstract representations of the entire system (e.g., module interactions).
   - **Purpose**: Provides developers with intuitive navigation tools.

5. **Hybrid Call Graphs**
   - **Description**: Maps function call relationships with high accuracy.
   - **Method**: Merges static analysis (e.g., tracing explicit calls) with LLM inference (e.g., resolving dynamic or indirect calls).
   - **Benefit**: Reduces errors in understanding execution flows.

#### Compression Mechanism
IRL compresses codebases into a token-efficient format, making it feasible for LLMs to process massive systems within their context windows:
- **Small Codebase (10K LOC)**:
  - Original token count: ~50K tokens.
  - Compressed: ~5K tokens (10:1 ratio).
- **Enterprise Codebase (1B LOC)**:
  - Original token count: ~5B tokens.
  - Compressed: ~750K tokens (6,667:1 ratio).
- **How It Works**:
  - Eliminates redundant code details (e.g., boilerplate).
  - Encodes relationships and semantics hierarchically.
  - Uses caching to reuse processed segments.

#### Cost Efficiency
Generating IRL artifacts is optimized for cost:
- **10K LOC**: ~$0.09 (using LLM APIs at $0.01/1K tokens).
- **1B LOC**: ~$4,155 (benefits from economies of scale and caching).
- **Scaling**: Costs decrease per LOC as codebase size increases due to hierarchical processing.

### Key Capabilities
IRL empowers LLMs with unprecedented codebase understanding:
1. **Whole-Codebase Intelligence**:
   - Maintains a holistic view, avoiding the partial context traps of semantic search.
   - Example: Understands how a utility function in one file impacts a frontend component elsewhere.
2. **Logical Navigation**:
   - Traverses the knowledge graph to answer queries or suggest edits.
   - Reduces hallucinations by grounding responses in structured data.
3. **Cost Efficiency**:
   - Compressed representations lower LLM query costs (e.g., ~$0.08 per query vs. $0.50+ for raw code).
4. **Scalability**:
   - Handles codebases from thousands to billions of lines.
   - Supports incremental updates via **delta code ingestion**, reprocessing only changed sections.

### Advantages Over Semantic Search
Most AI assistants rely on semantic search, indexing code via embeddings and retrieving relevant snippets. IRL outperforms this approach, especially as codebase size grows:
- **Small Codebases (10K-50K LOC)**:
  - **Improvement**: 25-40%.
  - **Tasks**: Architecture understanding, dependency analysis.
  - **Reason**: Structured hierarchy provides richer context than flat embeddings.
- **Medium Codebases (100K-250K LOC)**:
  - **Improvement**: 50-70%.
  - **Tasks**: Code navigation, refactoring.
  - **Reason**: Logical traversal beats linear search scalability.
- **Large Codebases (1M-10M LOC)**:
  - **Improvement**: 80-150%.
  - **Tasks**: Bug detection, system-wide impact analysis.
  - **Reason**: Holistic view captures distant relationships.
- **Enterprise Codebases (10M+ LOC)**:
  - **Improvement**: 150-300%.
  - **Tasks**: Legacy code maintenance, onboarding.
  - **Reason**: Logarithmic scaling vs. semantic search’s linear bottleneck.

### Technical Underpinnings
- **Static Analysis**: Parses code to build initial graphs (e.g., using Tree-sitter or language-specific parsers).
- **LLM Integration**: Employs models like GPT-4 or LLaMA to generate descriptions, resolve ambiguities, and classify domains.
- **Hierarchical Processing**:
  - Breaks codebases into manageable chunks.
  - Processes bottom-up (functions → domains → system).
- **Incremental Updates**: Detects changes via git diffs, updating only affected graph nodes.

### Current State
- **Prototype**: Fully functional, integrated into tools like RooCline (an open-source AI coding agent) for testing.
- **Validation**: Ranked top 10 in SWE-Bench Verified, outperforming Google and Amazon, showcasing IRL’s accuracy and utility.
- **Limitations**: Untested in production environments; lacks real-world feedback on edge cases.


## License

MIT
