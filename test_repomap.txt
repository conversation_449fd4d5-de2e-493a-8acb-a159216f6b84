
bracket_core/exp_repomap/__init__.py:
│__all__ = ['CompleteRepoMap']
bracket_core/exp_repomap/complete_repomap.py:
│def get_language(lang):
│def get_parser(lang):
│logger = logging.getLogger(__name__)
│Tag = namedtuple("Tag", "rel_fname fname line name kind")
│class SimpleIO:
│def read_text(self, fname):
│def tool_output(self, message, log_only=False):
│def tool_warning(self, message):
│def tool_error(self, message):
│class SimpleTokenCounter:
│def token_count(self, text):
│def get_repo_map_tokens(self):
│def get_scm_fname(lang):
│local_path = Path(__file__).parent / "queries" / f"{lang}-tags.scm"
│site_packages = site.getsitepackages()
│path = Path(site_pkg) / "aider" / "queries" / "tree-sitter-language-pack" / f"{lang}-tags.scm"
│path = Path(site_pkg) / "aider" / "queries" / "tree-sitter-languages" / f"{lang}-tags.scm"
│def find_src_files(directory, exclude_dirs=None):
│exclude_dirs = {'.git', '__pycache__', 'node_modules', 'venv', '.venv', 'env', '.env'}
│src_files = []
│file_path = os.path.join(root, file)
│class CompleteRepoMap:
│def __init__(
│root = os.getcwd()
│def get_mtime(self, fname):
│def get_tags(self, fname, rel_fname):
│file_mtime = self.get_mtime(fname)
│cache_key = fname
│data = list(self.get_tags_raw(fname, rel_fname))
│def get_tags_raw(self, fname, rel_fname):
│lang = filename_to_lang(fname)
│code = self.io.read_text(fname)
│language = get_language(lang)
│parser = get_parser(lang)
│query_scm_path = get_scm_fname(lang)
│query_scm = query_scm_path.read_text()
│tree = parser.parse(bytes(code, "utf-8"))
│query = language.query(query_scm)
│captures = query.captures(tree.root_node)
│saw = set()
│all_nodes = []
│all_nodes = list(captures)
│kind = "def"
│kind = "ref"
│result = Tag(
│patterns = {
│pattern = patterns.get(lang, r'([a-zA-Z0-9_]+)\s*\(')
│matches = re.findall(pattern, line)
│name = match[1] if isinstance(match, tuple) and len(match) > 1 else match
│lexer = guess_lexer_for_filename(fname, code)
│tokens = list(lexer.get_tokens(code))
│name_tokens = [token[1] for token in tokens if token[0] in Token.Name]
│unique_tokens = set(name_tokens)
│def get_ranked_tags(self, all_files, progress=None):
│defines = defaultdict(set)  # name -> set of files that define it
│references = defaultdict(list)  # name -> list of files that reference it
│definitions = defaultdict(set)  # (file, name) -> set of Tag objects
│rel_fname = os.path.relpath(fname, self.root)
│tags = list(self.get_tags(fname, rel_fname))
│key = (rel_fname, tag.name)
│references = dict((k, list(v)) for k, v in defines.items())
│G = nx.MultiDiGraph()
│rel_fname = os.path.relpath(fname, self.root)
│definers = defines[ident]
│num_refs = math.sqrt(num_refs)
│all_tags = []
│def render_tree(self, abs_fname, rel_fname, lois):
│mtime = self.get_mtime(abs_fname)
│key = (rel_fname, tuple(sorted(lois)), mtime)
│code = self.io.read_text(abs_fname) or ""
│lines = code.splitlines()
│sorted_lois = sorted(set(lois))
│result = []
│line = lines[line_num].strip()
│line = line[:97] + "..."
│tree = "\n".join(result)
│def to_tree(self, tags):
│cur_fname = None
│cur_abs_fname = None
│lois = None
│output = ""
│dummy_tag = (None,)
│this_rel_fname = tag[0] if isinstance(tag, Tag) else None
│lois = None
│lois = []
│cur_abs_fname = tag.fname
│cur_fname = this_rel_fname
│def token_count(self, text):
│def generate_complete_map(self, repo_dir):
│all_files = find_src_files(repo_dir)
│tree = self.to_tree(all_tags)
│token_count = self.token_count(tree)
│def save_map(self, map_data, output_file):
│def main():
│parser = argparse.ArgumentParser(description="Generate a complete repository map")
│args = parser.parse_args()
│repo_map = CompleteRepoMap(
│start_time = time.time()
│map_data = repo_map.generate_complete_map(args.repo_dir)
│end_time = time.time()
│output_file = repo_map.save_map(map_data, args.output)
bracket_core/exp_repomap/test_repomap.py:
│logger = logging.getLogger(__name__)
│def test_small_repo():
│repo_dir = Path(__file__).parent.parent.parent
│output_file = "test_repomap.txt"
│repo_map = CompleteRepoMap(
│test_dir = repo_dir / "bracket_core" / "exp_repomap"
│map_data = repo_map.generate_complete_map(str(test_dir))
│output_path = repo_map.save_map(map_data, output_file)
│output_path = test_small_repo()