"""
File-Driven Call Graph Builder for Codebase

This module provides functionality to:
1. Read function-driven call graph (CG1) from semantic_documented_functions.parquet
2. Generate a more compact file-driven call graph (CG2)
3. For each file, include functions it contains, descriptions, and external calls
4. Save the file-driven call graph as YAML

It can be used as:
1. A standalone script to process a parquet file of documented functions
2. A module that can be integrated into the repository analysis flow
"""

import os
import yaml
import json
import logging
import argparse
import asyncio
import pandas as pd
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, field

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class FileCallGraphResult:
    """Result of generating a file-driven call graph."""
    success: bool = True
    error_message: str = ""
    file_graph: Dict[str, Any] = field(default_factory=dict)
    output_path: Optional[str] = None

class FileCallGraphBuilder:
    """
    Builds a file-driven call graph from a function-driven call graph.

    This class reads a parquet file containing documented functions (CG1),
    and generates a more compact file-driven call graph (CG2).
    """

    def __init__(
        self,
        functions_parquet_path: str,
        output_path: str,
        repo_root: Optional[str] = None,
    ):
        """
        Initialize the file call graph builder.

        Args:
            functions_parquet_path: Path to the semantic_documented_functions.parquet file
            output_path: Path to save the file-driven call graph YAML
        """
        self.functions_parquet_path = functions_parquet_path
        self.output_path = output_path
        self.repo_root = repo_root or os.getcwd()

    def read_functions_parquet(self) -> pd.DataFrame:
        """
        Read the functions parquet file.

        Returns:
            DataFrame containing function data
        """
        logger.info(f"Reading functions parquet file: {self.functions_parquet_path}")
        try:
            df = pd.read_parquet(self.functions_parquet_path)
            logger.info(f"Read {len(df)} functions from parquet file")
            return df
        except Exception as e:
            logger.error(f"Error reading functions parquet file: {e}")
            raise

    def build_file_call_graph(self) -> FileCallGraphResult:
        """
        Build a file-driven call graph from the functions parquet file.

        Returns:
            FileCallGraphResult containing the file-driven call graph
        """
        try:
            # Read the functions parquet
            df = self.read_functions_parquet()

            # Group functions by file
            file_graph = self._group_functions_by_file(df)

            # Save the file graph to YAML
            with open(self.output_path, 'w') as f:
                yaml.dump(file_graph, f, default_flow_style=False)

            logger.info(f"File-driven call graph saved to: {self.output_path}")

            return FileCallGraphResult(
                success=True,
                file_graph=file_graph,
                output_path=self.output_path
            )

        except Exception as e:
            logger.error(f"Error building file call graph: {e}")
            import traceback
            logger.error(traceback.format_exc())

            return FileCallGraphResult(
                success=False,
                error_message=f"Failed to build file call graph: {str(e)}"
            )

    def _get_relative_path(self, file_path: Union[str, Any]) -> str:
        """
        Convert absolute file path to relative path from repo root.

        Args:
            file_path: Absolute file path

        Returns:
            Relative file path from repo root
        """
        # Ensure file_path is a string
        file_path_str = str(file_path)
        try:
            return os.path.relpath(file_path_str, self.repo_root)
        except ValueError:
            # If paths are on different drives, return the original path
            return file_path_str

    def _extract_function_name(self, signature: Any) -> str:
        """
        Extract function name from signature.

        Args:
            signature: Function signature (can be string, dict, or other)

        Returns:
            Function name
        """
        if isinstance(signature, dict) and 'name' in signature:
            return signature['name']
        elif isinstance(signature, str):
            # Try to extract name from string signature (e.g., "function_name(param1, param2)")
            return signature.split('(')[0].strip()
        else:
            # Fallback to string representation
            return str(signature)

    def _group_functions_by_file(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Group functions by file path.

        Args:
            df: DataFrame containing function data

        Returns:
            Dictionary mapping file paths to function data
        """
        logger.info("Grouping functions by file path")

        # Initialize file graph
        file_graph = {}

        # Group functions by file path
        file_groups = df.groupby('file_path')

        for file_path, group in file_groups:
            if not file_path or pd.isna(file_path):
                continue

            # Get relative file path
            rel_file_path = self._get_relative_path(file_path)

            # Initialize file entry
            file_entry = {
                'functions': [],
                # 'description': f"File containing {len(group)} functions"
            }

            # Process each function in the file
            for _, row in group.iterrows():
                # Extract function data
                function_name = row.get('name', '')
                description = row.get('description', '')
                is_significant: bool = row.get('is_architecturally_significant', '')

                # Parse calls if available
                calls = row.get('calls', [])
                if isinstance(calls, str):
                    try:
                        calls = json.loads(calls)
                    except json.JSONDecodeError:
                        calls = [calls] if calls else []

                # Add function to file entry
                if is_significant:
                    function_entry = {
                        'name': function_name,
                        'description': description
                    }

                # Only add calls if there are any
                # if calls:
                #     function_entry['calls'] = calls

                    file_entry['functions'].append(function_entry)

            # Add file entry to file graph
            file_graph[rel_file_path] = file_entry

        logger.info(f"Grouped functions into {len(file_graph)} files")
        return file_graph

async def main():
    """Main entry point for the file call graph builder."""
    parser = argparse.ArgumentParser(description="Build a file-driven call graph from a function-driven call graph")
    parser.add_argument("--functions-parquet", required=True, help="Path to the semantic_documented_functions.parquet file")
    parser.add_argument("--output", required=True, help="Path to save the file-driven call graph YAML")
    parser.add_argument("--repo-root", help="Repository root directory (default: current directory)")

    args = parser.parse_args()

    try:
        builder = FileCallGraphBuilder(
            functions_parquet_path=args.functions_parquet,
            output_path=args.output,
            repo_root=args.repo_root
        )

        result = builder.build_file_call_graph()

        if result.success:
            logger.info("File call graph building completed successfully")
            return 0
        else:
            logger.error(f"File call graph building failed: {result.error_message}")
            return 1

    except Exception as e:
        logger.error(f"Error in file call graph building: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
