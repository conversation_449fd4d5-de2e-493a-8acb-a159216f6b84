# Standalone Clustering for Embedding Experiments

This document explains how to use the standalone clustering script to experiment with different clustering parameters on existing function embeddings without re-running the entire pipeline.

## Overview

The `standalone_clustering.py` script allows you to:

1. Load function embeddings generated by the `faster_irl.py` pipeline
2. Cluster the embeddings with specific parameters
3. Generate a JSON file containing only the leaf domains
4. Save additional artifacts for analysis (similarity graph, full domain hierarchy)

This is useful when you want to tune clustering parameters for a large codebase without having to regenerate embeddings each time.

## Usage

```bash
python -m bracket_core.exp_embedding.standalone_clustering \
  --input /path/to/artifacts/directory \
  --output /path/to/output/directory \
  --similarity-threshold 0.8 \
  --min-community-size 5 \
  --resolution 1.0 \
  --hierarchical-levels 3
```

### Parameters

- `--input`: Directory containing the input artifacts (must contain `function_embeddings.parquet`)
- `--output`: Directory to save output artifacts (optional, defaults to input directory)
- `--similarity-threshold`: Minimum similarity score to create an edge (0-1)
- `--min-community-size`: Minimum number of functions in a community
- `--max-community-size`: Maximum number of functions in a community (default: 100)
- `--resolution`: Resolution parameter for community detection (higher = more communities)
- `--hierarchical-levels`: Number of hierarchical levels to build
- `--openai-api-key`: OpenAI API key (optional, will use environment variable if not provided)
- `--openai-model`: OpenAI model to use for naming communities (default: gpt-4o-mini)

### Output Files

The script generates the following files with parameter values in the filename:

- `leaf_domains_st{similarity_threshold}_ms{min_community_size}_r{resolution}_hl{hierarchical_levels}.json`: JSON file containing only leaf domains
- `similarity_graph_st{similarity_threshold}_ms{min_community_size}_r{resolution}_hl{hierarchical_levels}.json`: JSON file for visualizing the similarity graph
- `domain_analysis_st{similarity_threshold}_ms{min_community_size}_r{resolution}_hl{hierarchical_levels}.yaml`: YAML file containing the full domain hierarchy

## Example Workflow

1. First, run the full pipeline once to generate function embeddings:
   ```bash
   python -m bracket_core.exp_embedding.faster_irl \
     --repo /path/to/repo \
     --output /path/to/artifacts
   ```

2. Then, experiment with different clustering parameters:
   ```bash
   python -m bracket_core.exp_embedding.standalone_clustering \
     --input /path/to/artifacts \
     --output /path/to/experiments/experiment1 \
     --similarity-threshold 0.8 \
     --min-community-size 5 \
     --resolution 1.0 \
     --hierarchical-levels 3
   ```

3. Try different parameters:
   ```bash
   python -m bracket_core.exp_embedding.standalone_clustering \
     --input /path/to/artifacts \
     --output /path/to/experiments/experiment2 \
     --similarity-threshold 0.7 \
     --min-community-size 3 \
     --resolution 1.2 \
     --hierarchical-levels 4
   ```

4. Compare the results to find the best parameter combination.

## Tips for Parameter Tuning

- **similarity_threshold**: Controls how similar functions need to be to be connected
  - Higher values (0.8-0.9): More precise, smaller domains
  - Lower values (0.6-0.7): More inclusive, larger domains
  - Recommended range: 0.7-0.9

- **min_community_size**: Minimum number of functions in a domain
  - Higher values: Fewer, larger domains
  - Lower values: More, smaller domains
  - Recommended range: 3-10

- **resolution**: Controls the granularity of community detection
  - Higher values (>1.0): More, smaller communities
  - Lower values (<1.0): Fewer, larger communities
  - Recommended range: 0.8-1.2

- **hierarchical_levels**: Number of levels in the domain hierarchy
  - Higher values: More detailed hierarchy
  - Lower values: Flatter hierarchy
  - Recommended range: 3-5
