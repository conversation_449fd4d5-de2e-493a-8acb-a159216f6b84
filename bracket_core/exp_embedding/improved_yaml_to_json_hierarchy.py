"""
Improved script to convert domain analysis YAML to a simplified JSON hierarchy.
"""

import os
import yaml
import json
import logging
from typing import Dict, List, Any, Set

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def improved_yaml_to_json_hierarchy(input_yaml_path: str, output_json_path: str) -> None:
    """
    Convert domain analysis YAML to a simplified JSON hierarchy with improved handling of duplicates.
    
    Args:
        input_yaml_path: Path to the input YAML file
        output_json_path: Path to save the simplified JSON file
    """
    logger.info(f"Converting YAML hierarchy from {input_yaml_path} to JSON with improved handling")
    
    # Load the YAML file
    with open(input_yaml_path, 'r') as f:
        hierarchy = yaml.safe_load(f)
    
    # First pass: collect all domains by name to merge duplicates
    domains_by_name = {}
    
    def collect_domains(domain_list, parent_path=None):
        for domain in domain_list:
            name = domain.get("name", "Unnamed Domain")
            if not name:  # Skip empty names
                continue
                
            full_path = f"{parent_path} -> {name}" if parent_path else name
            
            # Create entry if it doesn't exist
            if name not in domains_by_name:
                domains_by_name[name] = {
                    "name": name,
                    "description": domain.get("description", ""),
                    "functions": set(),
                    "children": set(),
                    "paths": set()
                }
            else:
                # Update description if current one is longer/better
                current_desc = domains_by_name[name]["description"]
                new_desc = domain.get("description", "")
                if len(new_desc) > len(current_desc):
                    domains_by_name[name]["description"] = new_desc
            
            # Add functions
            functions = domain.get("functions", [])
            if isinstance(functions, list):
                domains_by_name[name]["functions"].update(functions)
            
            # Add path
            domains_by_name[name]["paths"].add(full_path)
            
            # Process sub-areas recursively
            if "sub_areas" in domain:
                for child in domain["sub_areas"]:
                    child_name = child.get("name", "")
                    if child_name:  # Only add non-empty names
                        domains_by_name[name]["children"].add(child_name)
                
                collect_domains(domain["sub_areas"], full_path)
    
    # Collect all domains
    if "areas" in hierarchy:
        collect_domains(hierarchy["areas"])
    
    # Second pass: build parent-child relationships
    child_to_parent = {}
    for name, domain_info in domains_by_name.items():
        for child_name in domain_info["children"]:
            if child_name in domains_by_name:
                if child_name in child_to_parent:
                    # If child already has a parent, keep the one with more specific path
                    current_parent = child_to_parent[child_name]
                    current_paths = domains_by_name[current_parent]["paths"]
                    new_paths = domain_info["paths"]
                    
                    # Compare path lengths - longer paths are more specific
                    current_max_len = max([len(p.split(" -> ")) for p in current_paths]) if current_paths else 0
                    new_max_len = max([len(p.split(" -> ")) for p in new_paths]) if new_paths else 0
                    
                    if new_max_len > current_max_len:
                        child_to_parent[child_name] = name
                else:
                    child_to_parent[child_name] = name
    
    # Find root domains (those that are not children of any other domain)
    root_domains = []
    for name in domains_by_name:
        if name not in child_to_parent:
            root_domains.append(name)
    
    # Build the final hierarchy
    def build_domain_tree(domain_name, visited=None):
        if visited is None:
            visited = set()
        
        if domain_name in visited:
            logger.warning(f"Circular reference detected for domain: {domain_name}")
            return None
        
        visited.add(domain_name)
        
        domain_info = domains_by_name[domain_name]
        
        result = {
            "name": domain_name,
            "description": domain_info["description"],
            "function_count": len(domain_info["functions"])
        }
        
        # Find children of this domain
        children = []
        for child_name, parent_name in child_to_parent.items():
            if parent_name == domain_name:
                child_tree = build_domain_tree(child_name, visited.copy())
                if child_tree:
                    children.append(child_tree)
        
        if children:
            result["children"] = children
        
        return result
    
    # Build trees for all root domains
    domain_trees = []
    for root in root_domains:
        tree = build_domain_tree(root)
        if tree:
            domain_trees.append(tree)
    
    # Sort domains by name
    domain_trees.sort(key=lambda x: x["name"])
    
    # Save as JSON
    with open(output_json_path, 'w') as f:
        json.dump(domain_trees, f, indent=2)
    
    logger.info(f"Improved JSON hierarchy saved to: {output_json_path}")
    logger.info(f"Total domains: {len(domains_by_name)}")
    logger.info(f"Root domains: {len(root_domains)}")

def main():
    """Main entry point for converting YAML to JSON hierarchy."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Convert domain analysis YAML to simplified JSON hierarchy")
    parser.add_argument("--input", required=True, help="Path to the input YAML file")
    parser.add_argument("--output", required=True, help="Path to save the simplified JSON file")
    
    args = parser.parse_args()
    
    improved_yaml_to_json_hierarchy(args.input, args.output)

if __name__ == "__main__":
    main()
