"""
Flat clustering for function embeddings to identify non-hierarchical leaf domains.

This script provides a simpler alternative to hierarchical clustering when the goal
is to identify good leaf domains without building a complex hierarchy. It ensures
comprehensive coverage by allowing functions to be part of multiple domains if needed.
"""

import os
import json
import yaml
import logging
import argparse
import asyncio
import pandas as pd
import networkx as nx
import numpy as np
from typing import Dict, List, Any, Optional, Set, Tuple
from collections import defaultdict
from tqdm import tqdm
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.cluster import SpectralClustering, DBSCAN, KMeans
import community as community_louvain

# Import necessary classes from faster_irl.py
from bracket_core.exp_embedding.faster_irl import Community, DomainHierarchy
from bracket_core.llm.api_keys import get_openai_api_key
from bracket_core.llm.oai.chat_openai import ChatOpenAI

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class FlatClusterer:
    """
    Flat clustering for function embeddings to identify non-hierarchical leaf domains.

    This class implements various clustering algorithms to identify domains without
    building a hierarchical structure. It focuses on finding good leaf domains and
    ensures comprehensive coverage by allowing functions to be part of multiple domains
    if they're on the boundary between clusters.
    """

    def __init__(
        self,
        input_dir: str,
        output_dir: Optional[str] = None,
        # Clustering parameters
        algorithm: str = "louvain",  # Options: "louvain", "spectral", "dbscan", "kmeans"
        similarity_threshold: float = 0.7,
        min_community_size: int = 3,
        max_community_size: int = 100,
        resolution: float = 1.0,
        n_clusters: int = 20,  # For spectral and kmeans
        eps: float = 0.3,  # For DBSCAN
        min_samples: int = 5,  # For DBSCAN
        allow_overlap: bool = True,  # Allow functions to be in multiple domains
        overlap_threshold: float = 0.6,  # Threshold for including a function in multiple domains
        strict_assignment: bool = False,  # Ensure each function belongs to only one cluster
        include_miscellaneous: bool = True,  # Include a miscellaneous domain for unclustered functions
        subdivide_large_communities: bool = True,  # Recursively subdivide large communities
        subdivision_threshold: int = 100,  # Size threshold for subdividing communities (independent of max_community_size)
        subdivision_resolution_factor: float = 1.5,  # Factor to increase resolution when subdividing
        enable_recursive_subdivision: bool = True,  # Enable recursive subdivision for communities that are still too large
        # Small community merging parameters
        enable_community_merging: bool = False,  # Merge small communities with similar larger ones
        small_community_threshold: int = 10,  # Communities smaller than this are considered small
        merge_similarity_threshold: float = 0.5,  # Minimum similarity to merge communities
        # OpenAI parameters
        openai_api_key: Optional[str] = None,
        openai_model: str = "gpt-4o-mini",
    ):
        """
        Initialize the flat clusterer.

        Args:
            input_dir: Directory containing the input artifacts (function_embeddings.parquet)
            output_dir: Directory to save output artifacts. If not provided, uses input directory.
            algorithm: Clustering algorithm to use ("louvain", "spectral", "dbscan", "kmeans")
            similarity_threshold: Minimum similarity score to create an edge (0-1)
            min_community_size: Minimum number of functions in a community
            max_community_size: Maximum number of functions in a community
            resolution: Resolution parameter for community detection (higher = more communities)
            n_clusters: Number of clusters for spectral and kmeans
            eps: Maximum distance between samples for DBSCAN
            min_samples: Minimum number of samples in a neighborhood for DBSCAN
            allow_overlap: Allow functions to be in multiple domains
            overlap_threshold: Threshold for including a function in multiple domains
            openai_api_key: OpenAI API key
            openai_model: OpenAI model to use for naming communities
        """
        self.input_dir = input_dir
        self.output_dir = output_dir or input_dir
        self.algorithm = algorithm
        self.similarity_threshold = similarity_threshold
        self.min_community_size = min_community_size
        self.max_community_size = max_community_size
        self.resolution = resolution
        self.n_clusters = n_clusters
        self.eps = eps
        self.min_samples = min_samples
        self.allow_overlap = allow_overlap
        self.overlap_threshold = overlap_threshold
        self.strict_assignment = strict_assignment
        self.include_miscellaneous = include_miscellaneous
        self.subdivide_large_communities = subdivide_large_communities
        self.subdivision_threshold = subdivision_threshold
        self.subdivision_resolution_factor = subdivision_resolution_factor
        self.enable_recursive_subdivision = enable_recursive_subdivision
        self.enable_community_merging = enable_community_merging
        self.small_community_threshold = small_community_threshold
        self.merge_similarity_threshold = merge_similarity_threshold
        self.openai_api_key = openai_api_key or get_openai_api_key()
        self.openai_model = openai_model

        # Ensure output directory exists
        os.makedirs(self.output_dir, exist_ok=True)

        # Define paths for input and output files
        self.embeddings_path = os.path.join(self.input_dir, "function_embeddings.parquet")

        # Create unique filenames based on parameters
        param_suffix = f"_{algorithm}_st{similarity_threshold}_ms{min_community_size}"
        if algorithm == "louvain":
            param_suffix += f"_r{resolution}"
        elif algorithm in ["spectral", "kmeans"]:
            param_suffix += f"_nc{n_clusters}"
        elif algorithm == "dbscan":
            param_suffix += f"_eps{eps}_ms{min_samples}"

        if allow_overlap:
            param_suffix += f"_overlap{overlap_threshold}"

        self.domains_json = os.path.join(self.output_dir, f"flat_domains{param_suffix}.json")
        self.domains_yaml = os.path.join(self.output_dir, f"flat_domains{param_suffix}.yaml")
        self.similarity_graph_path = os.path.join(self.output_dir, f"similarity_graph{param_suffix}.json")

        # Initialize data structures
        self.embeddings_df = None
        self.graph = nx.Graph()
        self.communities = {}
        self.domain_hierarchy = DomainHierarchy()
        self.similarity_matrix = None

    def build_similarity_graph(self) -> nx.Graph:
        """
        Build a graph where nodes are functions and edges are weighted by embedding similarity.

        Returns:
            NetworkX graph with functions as nodes and similarity scores as edge weights
        """
        logger.info("Building similarity graph from function embeddings")

        # Extract embeddings as numpy array
        embeddings = np.array(self.embeddings_df['embedding'].tolist())

        # Compute pairwise cosine similarity
        logger.info("Computing pairwise cosine similarity")
        self.similarity_matrix = cosine_similarity(embeddings)

        # Create graph nodes
        logger.info("Creating graph nodes")
        for idx, row in tqdm(self.embeddings_df.iterrows(), total=len(self.embeddings_df), desc="Adding nodes"):
            node_id = idx
            self.graph.add_node(
                node_id,
                name=row.get('name', ''),
                file_path=row.get('file_path', ''),
                significance=row.get('architectural_significance', 0.0),
                description=row.get('description', '')
            )

        # Create graph edges based on similarity threshold
        logger.info(f"Creating graph edges with similarity threshold {self.similarity_threshold}")
        edge_count = 0
        for i in tqdm(range(len(self.embeddings_df)), desc="Adding edges"):
            for j in range(i+1, len(self.embeddings_df)):
                similarity = self.similarity_matrix[i, j]
                if similarity >= self.similarity_threshold:
                    self.graph.add_edge(i, j, weight=similarity)
                    edge_count += 1

        logger.info(f"Created graph with {len(self.graph.nodes)} nodes and {edge_count} edges")
        return self.graph

    def detect_communities_louvain(self) -> Dict[int, List[int]]:
        """
        Apply Louvain community detection to identify densely connected subgraphs.

        Returns:
            Dictionary mapping community IDs to lists of function indices
        """
        logger.info("Detecting communities using Louvain algorithm")

        # Apply Louvain community detection
        partition = community_louvain.best_partition(self.graph, resolution=self.resolution)

        # Group functions by community
        communities = defaultdict(list)
        for node, community_id in partition.items():
            communities[community_id].append(node)

        # Filter communities and identify those for subdivision
        filtered_communities = {}
        oversized_communities = {}
        next_id = max(communities.keys()) + 1 if communities else 0

        for community_id, nodes in communities.items():
            # First check if it meets the minimum size requirement
            if len(nodes) < self.min_community_size:
                # Skip communities that are too small
                continue

            # Check if it should be subdivided (independent of max_community_size)
            if self.subdivide_large_communities and len(nodes) > self.subdivision_threshold:
                # Mark for subdivision - don't add to filtered_communities yet
                oversized_communities[community_id] = nodes
            else:
                # Only add to filtered_communities if it's not oversized
                filtered_communities[community_id] = nodes

        # Recursively subdivide oversized communities if enabled
        if self.subdivide_large_communities and oversized_communities:
            logger.info(f"Found {len(oversized_communities)} oversized communities to subdivide")
            for community_id, nodes in oversized_communities.items():
                logger.info(f"Subdividing community {community_id} with {len(nodes)} functions")

                # Create a subgraph with just these nodes
                subgraph = self.create_subgraph(nodes)

                # Apply Louvain to the subgraph with a higher resolution to get more communities
                sub_partition = community_louvain.best_partition(
                    subgraph,
                    resolution=self.resolution * self.subdivision_resolution_factor  # Increase resolution for finer-grained communities
                )

                # Group functions by subcommunity
                subcommunities = defaultdict(list)
                for node, sub_community_id in sub_partition.items():
                    # Map back to original node ID
                    original_node_id = nodes[node]
                    subcommunities[sub_community_id].append(original_node_id)

                # Track if we successfully created any valid subcommunities
                valid_subcommunities_created = False

                # Add subcommunities that meet size requirements
                for sub_nodes in subcommunities.values():
                    if len(sub_nodes) >= self.min_community_size:
                        if len(sub_nodes) > self.subdivision_threshold and self.enable_recursive_subdivision:
                            # If still too large and recursive subdivision is enabled, try to subdivide again
                            logger.info(f"Attempting recursive subdivision of community with {len(sub_nodes)} functions")

                            # Create a subgraph for this subcommunity
                            sub_subgraph = self.create_subgraph(sub_nodes)

                            # Apply Louvain with even higher resolution
                            sub_sub_partition = community_louvain.best_partition(
                                sub_subgraph,
                                resolution=self.resolution * self.subdivision_resolution_factor * 1.5  # Even higher resolution
                            )

                            # Group functions by sub-subcommunity
                            sub_subcommunities = defaultdict(list)
                            for sub_node, sub_sub_community_id in sub_sub_partition.items():
                                # Map back to original node ID
                                original_sub_node_id = sub_nodes[sub_node]
                                sub_subcommunities[sub_sub_community_id].append(original_sub_node_id)

                            # Check if recursive subdivision was successful
                            sub_valid_created = False

                            # Add sub-subcommunities that meet size requirements
                            for sub_sub_nodes in sub_subcommunities.values():
                                if len(sub_sub_nodes) >= self.min_community_size:
                                    if len(sub_sub_nodes) <= self.subdivision_threshold:
                                        # Only add if it's within the threshold
                                        filtered_communities[next_id] = sub_sub_nodes
                                        next_id += 1
                                        valid_subcommunities_created = True
                                        sub_valid_created = True
                                    else:
                                        # If still too large, split it into smaller chunks
                                        logger.warning(f"Sub-subcommunity with {len(sub_sub_nodes)} functions still exceeds threshold. Splitting into chunks.")
                                        # Split into chunks of max size = threshold
                                        for i in range(0, len(sub_sub_nodes), self.subdivision_threshold):
                                            chunk = sub_sub_nodes[i:i + self.subdivision_threshold]
                                            if len(chunk) >= self.min_community_size:
                                                filtered_communities[next_id] = chunk
                                                next_id += 1
                                                valid_subcommunities_created = True
                                                sub_valid_created = True
                                                logger.info(f"Created chunk with {len(chunk)} functions")

                            # If recursive subdivision didn't work, split into chunks
                            if not sub_valid_created:
                                logger.warning(f"Recursive subdivision failed for community with {len(sub_nodes)} functions, splitting into chunks")
                                # Split into chunks of max size = threshold
                                for i in range(0, len(sub_nodes), self.subdivision_threshold):
                                    chunk = sub_nodes[i:i + self.subdivision_threshold]
                                    if len(chunk) >= self.min_community_size:
                                        filtered_communities[next_id] = chunk
                                        next_id += 1
                                        valid_subcommunities_created = True
                                        sub_valid_created = True
                                        logger.info(f"Created chunk with {len(chunk)} functions")
                        elif len(sub_nodes) > self.subdivision_threshold:
                            # If still too large but recursive subdivision is disabled, split into chunks
                            logger.warning(f"Community with {len(sub_nodes)} functions exceeds threshold, splitting into chunks")
                            # Split into chunks of max size = threshold
                            for i in range(0, len(sub_nodes), self.subdivision_threshold):
                                chunk = sub_nodes[i:i + self.subdivision_threshold]
                                if len(chunk) >= self.min_community_size:
                                    filtered_communities[next_id] = chunk
                                    next_id += 1
                                    valid_subcommunities_created = True
                                    logger.info(f"Created chunk with {len(chunk)} functions")
                        else:
                            # Add subcommunity as is
                            filtered_communities[next_id] = sub_nodes
                            next_id += 1
                            valid_subcommunities_created = True

                # If subdivision didn't create any valid subcommunities, split into chunks
                if not valid_subcommunities_created:
                    logger.warning(f"Subdivision of community {community_id} with {len(nodes)} functions didn't create any valid subcommunities. Splitting into chunks.")
                    # Split into chunks of max size = threshold
                    for i in range(0, len(nodes), self.subdivision_threshold):
                        chunk = nodes[i:i + self.subdivision_threshold]
                        if len(chunk) >= self.min_community_size:
                            filtered_communities[next_id] = chunk
                            next_id += 1
                            logger.info(f"Created chunk with {len(chunk)} functions")
        elif not self.subdivide_large_communities and oversized_communities:
            # If subdivision is disabled, split oversized communities into chunks
            logger.info(f"Splitting {len(oversized_communities)} oversized communities into chunks (subdivision disabled)")
            for community_id, nodes in oversized_communities.items():
                # Split into chunks of max size = threshold
                for i in range(0, len(nodes), self.subdivision_threshold):
                    chunk = nodes[i:i + self.subdivision_threshold]
                    if len(chunk) >= self.min_community_size:
                        filtered_communities[next_id] = chunk
                        next_id += 1
                        logger.info(f"Created chunk with {len(chunk)} functions")

        logger.info(f"Detected {len(filtered_communities)} communities after filtering and subdivision")
        return filtered_communities

    def create_subgraph(self, nodes: List[int]) -> nx.Graph:
        """
        Create a subgraph containing only the specified nodes.

        Args:
            nodes: List of node indices to include in the subgraph

        Returns:
            NetworkX graph containing only the specified nodes and their edges
        """
        # Create a new graph
        subgraph = nx.Graph()

        # Create a mapping from original node IDs to sequential IDs for the subgraph
        node_mapping = {original_id: new_id for new_id, original_id in enumerate(nodes)}

        # Add nodes to the subgraph
        for new_id, original_id in enumerate(nodes):
            # Copy node attributes
            attrs = self.graph.nodes[original_id]
            subgraph.add_node(new_id, **attrs)

        # Add edges between nodes in the subgraph
        for i, node1 in enumerate(nodes):
            for j, node2 in enumerate(nodes[i+1:], i+1):
                if self.graph.has_edge(node1, node2):
                    # Copy edge attributes
                    edge_attrs = self.graph.edges[node1, node2]
                    subgraph.add_edge(i, j, **edge_attrs)

        return subgraph

    def detect_communities_spectral(self) -> Dict[int, List[int]]:
        """
        Apply Spectral Clustering to identify communities.

        Returns:
            Dictionary mapping community IDs to lists of function indices
        """
        logger.info(f"Detecting communities using Spectral Clustering with {self.n_clusters} clusters")

        # Extract embeddings as numpy array
        embeddings = np.array(self.embeddings_df['embedding'].tolist())

        # Apply Spectral Clustering
        clustering = SpectralClustering(
            n_clusters=self.n_clusters,
            affinity='precomputed',
            assign_labels='kmeans',
            random_state=42
        ).fit(self.similarity_matrix)

        # Group functions by community
        communities = defaultdict(list)
        for node, community_id in enumerate(clustering.labels_):
            communities[community_id].append(node)

        # Filter communities by size
        filtered_communities = {}
        for community_id, nodes in communities.items():
            if self.min_community_size <= len(nodes) <= self.max_community_size:
                filtered_communities[community_id] = nodes

        logger.info(f"Detected {len(filtered_communities)} communities after filtering by size")
        return filtered_communities

    def detect_communities_dbscan(self) -> Dict[int, List[int]]:
        """
        Apply DBSCAN to identify communities.

        Returns:
            Dictionary mapping community IDs to lists of function indices
        """
        logger.info(f"Detecting communities using DBSCAN with eps={self.eps}, min_samples={self.min_samples}")

        # Extract embeddings as numpy array
        embeddings = np.array(self.embeddings_df['embedding'].tolist())

        # Apply DBSCAN
        clustering = DBSCAN(
            eps=self.eps,
            min_samples=self.min_samples,
            metric='precomputed'
        ).fit(1 - self.similarity_matrix)  # Convert similarity to distance

        # Group functions by community
        communities = defaultdict(list)
        for node, community_id in enumerate(clustering.labels_):
            communities[community_id].append(node)

        # Filter communities by size (exclude noise points with label -1)
        filtered_communities = {}
        for community_id, nodes in communities.items():
            if community_id != -1 and self.min_community_size <= len(nodes) <= self.max_community_size:
                filtered_communities[community_id] = nodes

        # Add a special community for noise points if there are any
        if -1 in communities and communities[-1]:
            noise_points = communities[-1]
            if len(noise_points) >= self.min_community_size:
                filtered_communities[-1] = noise_points
                logger.info(f"Added {len(noise_points)} noise points as a separate community")

        logger.info(f"Detected {len(filtered_communities)} communities after filtering by size")
        return filtered_communities

    def detect_communities_kmeans(self) -> Dict[int, List[int]]:
        """
        Apply K-means clustering to identify communities.

        Returns:
            Dictionary mapping community IDs to lists of function indices
        """
        logger.info(f"Detecting communities using K-means with {self.n_clusters} clusters")

        # Extract embeddings as numpy array
        embeddings = np.array(self.embeddings_df['embedding'].tolist())

        # Apply K-means
        clustering = KMeans(
            n_clusters=self.n_clusters,
            random_state=42
        ).fit(embeddings)

        # Group functions by community
        communities = defaultdict(list)
        for node, community_id in enumerate(clustering.labels_):
            communities[community_id].append(node)

        # Filter communities by size
        filtered_communities = {}
        for community_id, nodes in communities.items():
            if self.min_community_size <= len(nodes) <= self.max_community_size:
                filtered_communities[community_id] = nodes

        logger.info(f"Detected {len(filtered_communities)} communities after filtering by size")
        return filtered_communities

    def detect_communities(self) -> Dict[int, List[int]]:
        """
        Detect communities using the selected algorithm.

        Returns:
            Dictionary mapping community IDs to lists of function indices
        """
        if self.algorithm == "louvain":
            return self.detect_communities_louvain()
        elif self.algorithm == "spectral":
            return self.detect_communities_spectral()
        elif self.algorithm == "dbscan":
            return self.detect_communities_dbscan()
        elif self.algorithm == "kmeans":
            return self.detect_communities_kmeans()
        else:
            raise ValueError(f"Unknown algorithm: {self.algorithm}")

    def enforce_strict_assignment(self, communities: Dict[int, List[int]]) -> Dict[int, List[int]]:
        """
        Ensure each function belongs to only one community by assigning it to the community
        with the highest average similarity.

        Args:
            communities: Dictionary mapping community IDs to lists of function indices

        Returns:
            Updated dictionary with each function in exactly one community
        """
        logger.info("Enforcing strict assignment (one function per community)")

        # Find functions that appear in multiple communities
        function_community_map = defaultdict(list)
        for comm_id, nodes in communities.items():
            for node in nodes:
                function_community_map[node].append(comm_id)

        # Count functions in multiple communities
        multi_community_functions = {node: comms for node, comms in function_community_map.items() if len(comms) > 1}
        logger.info(f"Found {len(multi_community_functions)} functions in multiple communities")

        if not multi_community_functions:
            logger.info("No functions in multiple communities, no changes needed")
            return communities

        # Create a copy of the communities to avoid modifying the original
        updated_communities = {k: list(v) for k, v in communities.items()}

        # For each function in multiple communities, keep it only in the best community
        for node, comm_ids in multi_community_functions.items():
            # Calculate average similarity with each community
            community_similarities = {}
            for comm_id in comm_ids:
                # Get other nodes in this community (excluding the current node)
                other_nodes = [n for n in communities[comm_id] if n != node]

                if not other_nodes:
                    # If this is the only node in the community, give it a low similarity
                    community_similarities[comm_id] = 0
                    continue

                # Calculate average similarity with other nodes in this community
                similarities = [self.similarity_matrix[node, other_node] for other_node in other_nodes]
                avg_similarity = np.mean(similarities) if similarities else 0
                community_similarities[comm_id] = avg_similarity

            # Find the community with the highest average similarity
            best_comm_id = max(community_similarities.items(), key=lambda x: x[1])[0]

            # Remove the function from all communities except the best one
            for comm_id in comm_ids:
                if comm_id != best_comm_id:
                    updated_communities[comm_id] = [n for n in updated_communities[comm_id] if n != node]
                    logger.debug(f"Removed node {node} from community {comm_id} (keeping in {best_comm_id})")

        # Count empty communities after removal
        empty_communities = [comm_id for comm_id, nodes in updated_communities.items() if not nodes]
        if empty_communities:
            logger.info(f"Removing {len(empty_communities)} empty communities after strict assignment")
            for comm_id in empty_communities:
                del updated_communities[comm_id]

        return updated_communities

    def handle_overlapping_communities(self, communities: Dict[int, List[int]]) -> Dict[int, List[int]]:
        """
        Allow functions to be part of multiple communities if they're on the boundary.

        Args:
            communities: Dictionary mapping community IDs to lists of function indices

        Returns:
            Updated dictionary with potentially overlapping communities
        """
        if not self.allow_overlap:
            return communities

        logger.info(f"Handling overlapping communities with threshold {self.overlap_threshold}")

        # Create a copy of the communities to avoid modifying the original
        updated_communities = {k: list(v) for k, v in communities.items()}

        # For each function, check if it should be added to other communities
        for node_idx in range(len(self.embeddings_df)):
            # Get the community this function is already in
            current_community = None
            for comm_id, nodes in communities.items():
                if node_idx in nodes:
                    current_community = comm_id
                    break

            if current_community is None:
                continue  # Skip if the function is not in any community

            # Check similarity with functions in other communities
            for comm_id, nodes in communities.items():
                if comm_id == current_community or node_idx in nodes:
                    continue  # Skip current community or if already in this community

                # Calculate average similarity with functions in this community
                similarities = [self.similarity_matrix[node_idx, other_node] for other_node in nodes]
                avg_similarity = np.mean(similarities) if similarities else 0

                # If similarity is above threshold, add to this community as well
                if avg_similarity >= self.overlap_threshold:
                    updated_communities[comm_id].append(node_idx)
                    logger.debug(f"Added node {node_idx} to community {comm_id} (avg similarity: {avg_similarity:.3f})")

        # Count how many functions are in multiple communities
        function_community_count = defaultdict(int)
        for comm_id, nodes in updated_communities.items():
            for node in nodes:
                function_community_count[node] += 1

        multi_community_count = sum(1 for count in function_community_count.values() if count > 1)
        logger.info(f"{multi_community_count} functions are in multiple communities")

        return updated_communities

    def enforce_subdivision_threshold(self, community_dict: Dict[int, List[int]]) -> Dict[int, List[int]]:
        """
        Ensure no community exceeds the subdivision threshold by splitting large communities into chunks.

        Args:
            community_dict: Dictionary mapping community IDs to lists of function indices

        Returns:
            Updated dictionary with all communities within the threshold
        """
        enforced_communities = {}
        next_id = max(community_dict.keys()) + 1 if community_dict else 0

        for comm_id, nodes in community_dict.items():
            if len(nodes) <= self.subdivision_threshold:
                # Community is within threshold, keep as is
                enforced_communities[comm_id] = nodes
            else:
                # Community exceeds threshold, split into chunks
                logger.warning(f"Community {comm_id} with {len(nodes)} functions exceeds threshold after merging. Splitting into chunks.")

                # Split into chunks of max size = threshold
                for i in range(0, len(nodes), self.subdivision_threshold):
                    chunk = nodes[i:i + self.subdivision_threshold]
                    if len(chunk) >= self.min_community_size:
                        enforced_communities[next_id] = chunk
                        next_id += 1
                        logger.info(f"Created chunk with {len(chunk)} functions")

        logger.info(f"After enforcing threshold: {len(enforced_communities)} communities")
        return enforced_communities

    def create_communities(self, community_dict: Dict[int, List[int]]) -> List[Community]:
        """
        Create Community objects from the community dictionary.

        Args:
            community_dict: Dictionary mapping community IDs to lists of function indices

        Returns:
            List of Community objects
        """
        # First enforce the subdivision threshold
        community_dict = self.enforce_subdivision_threshold(community_dict)

        communities = []
        for comm_id, node_indices in community_dict.items():
            # Get function IDs for each node
            function_ids = []
            for idx in node_indices:
                row = self.embeddings_df.iloc[idx]
                function_id = f"{row['file_path']}:{row['name']}"
                function_ids.append(function_id)

            # Create a Community object
            community = Community(
                id=comm_id,
                functions=function_ids,
                level=0  # All communities are leaf communities in flat clustering
            )
            communities.append(community)

        return communities

    async def name_communities(self, communities: List[Community]) -> None:
        """
        Use LLMs to name and describe each community based on its functions.

        Args:
            communities: List of Community objects to name
        """
        logger.info("Naming communities using LLM")

        # Initialize OpenAI client
        chat_client = ChatOpenAI(
            api_key=self.openai_api_key,
            model=self.openai_model
        )

        # Name communities in parallel
        tasks = []
        for community in communities:
            tasks.append(self._name_community(chat_client, community))

        await asyncio.gather(*tasks)

        logger.info("Finished naming communities")

    async def _name_community(self, chat_client: Any, community: Community) -> None:
        """
        Name a single community using LLM.

        Args:
            chat_client: OpenAI chat client
            community: Community object to name
        """
        # Get function details for this community
        function_details = []
        for function_id in community.functions:
            # Find the function in the DataFrame
            file_path, name = function_id.split(':', 1)
            matches = self.embeddings_df[
                (self.embeddings_df['file_path'] == file_path) &
                (self.embeddings_df['name'] == name)
            ]

            if not matches.empty:
                row = matches.iloc[0]
                description = row.get('description', '')
                function_details.append(f"{function_id}: {description}")
            else:
                function_details.append(function_id)

        # Limit the number of functions to avoid token limits
        if len(function_details) > 50:
            function_details = function_details[:50] + [f"... and {len(function_details) - 50} more functions"]

        # Create the prompt
        prompt = f"""You are an expert software architect analyzing a codebase.
Below is a list of functions that have been grouped together based on semantic similarity.
Please provide:
1. A concise name for this domain (max 5 words)
2. A brief description of what this domain does (1-2 sentences)

Functions:
{chr(10).join(function_details)}

Respond in the following format:
Name: <domain name>
Description: <domain description>
"""

        try:
            # Create messages for the API call
            messages = [
                {"role": "system", "content": "You are an expert software architect helping to analyze codebases."},
                {"role": "user", "content": prompt}
            ]

            # Call the API using agenerate method
            content = await chat_client.agenerate(messages=messages, streaming=False)

            # Extract name and description
            name_match = content.split("Name:", 1)[1].split("\n", 1)[0].strip() if "Name:" in content else ""
            desc_match = content.split("Description:", 1)[1].strip() if "Description:" in content else ""

            # Update the community
            community.name = name_match
            community.description = desc_match

            logger.info(f"Named community {community.id}: {community.name}")

        except Exception as e:
            logger.error(f"Error naming community {community.id}: {e}")
            community.name = f"Domain {community.id}"
            community.description = "No description available"

    def save_domains_json(self, communities: List[Community], output_path: str) -> None:
        """
        Save domains as JSON.

        Args:
            communities: List of Community objects
            output_path: Path to save the JSON file
        """
        logger.info(f"Saving domains to JSON: {output_path}")

        # Create a dictionary for the JSON output
        domains_json = {
            "domains": []
        }

        for community in communities:
            domain = {
                "id": community.id,
                "name": community.name,
                # "description": community.description,
                "function_count": len(community.functions),
                # "functions": community.functions
            }
            domains_json["domains"].append(domain)

        # Save to JSON
        with open(output_path, 'w') as f:
            json.dump(domains_json, f, indent=2)

    def save_domains_yaml(self, communities: List[Community], output_path: str) -> None:
        """
        Save domains as YAML.

        Args:
            communities: List of Community objects
            output_path: Path to save the YAML file
        """
        logger.info(f"Saving domains to YAML: {output_path}")

        # Create a dictionary for the YAML output
        domains_yaml = {
            "areas": []
        }

        for community in communities:
            domain = {
                "name": community.name,
                "description": community.description,
                "functions": community.functions
            }
            domains_yaml["areas"].append(domain)

        # Save to YAML
        with open(output_path, 'w') as f:
            yaml.dump(domains_yaml, f, default_flow_style=False)

    def save_similarity_graph(self, output_path: str) -> None:
        """
        Save the similarity graph for visualization.

        Args:
            output_path: Path to save the graph file
        """
        logger.info(f"Saving similarity graph to {output_path}")

        # Convert graph to JSON format
        graph_data = {
            "nodes": [],
            "links": []
        }

        # Add nodes
        for node, data in self.graph.nodes(data=True):
            graph_data["nodes"].append({
                "id": str(node),
                "name": data.get("name", ""),
                "file_path": data.get("file_path", ""),
                "significance": data.get("significance", 0.0)
            })

        # Add edges
        for source, target, data in self.graph.edges(data=True):
            graph_data["links"].append({
                "source": str(source),
                "target": str(target),
                "weight": data.get("weight", 0.0)
            })

        # Save to JSON
        with open(output_path, 'w') as f:
            json.dump(graph_data, f, indent=2)

    def calculate_community_similarity(self, community1_nodes: List[int], community2_nodes: List[int]) -> float:
        """
        Calculate the similarity between two communities based on their member functions.

        Args:
            community1_nodes: List of node indices in the first community
            community2_nodes: List of node indices in the second community

        Returns:
            Similarity score between the two communities (0-1)
        """
        # Calculate pairwise similarities between all functions in both communities
        similarities = []
        for node1 in community1_nodes:
            for node2 in community2_nodes:
                similarities.append(self.similarity_matrix[node1, node2])

        # Return the average similarity
        return np.mean(similarities) if similarities else 0.0

    def merge_small_communities(self, communities_dict: Dict[int, List[int]]) -> Dict[int, List[int]]:
        """
        Merge small communities with their most similar neighbors.

        Args:
            communities_dict: Dictionary mapping community IDs to lists of function indices

        Returns:
            Updated dictionary with small communities merged
        """
        if not self.enable_community_merging:
            return communities_dict

        logger.info(f"Merging small communities (threshold: {self.small_community_threshold} functions)")

        # Identify small communities
        small_communities = {}
        large_communities = {}
        for comm_id, nodes in communities_dict.items():
            if len(nodes) < self.small_community_threshold:
                small_communities[comm_id] = nodes
            else:
                large_communities[comm_id] = nodes

        if not small_communities:
            logger.info("No small communities to merge")
            return communities_dict

        logger.info(f"Found {len(small_communities)} small communities and {len(large_communities)} large communities")

        # Create a copy of the communities dictionary to store the merged result
        merged_communities = {k: list(v) for k, v in large_communities.items()}

        # For each small community, find the most similar larger community
        for small_comm_id, small_nodes in small_communities.items():
            best_similarity = -1
            best_match_id = None

            # First try to find a match among large communities
            for large_comm_id, large_nodes in large_communities.items():
                # Check if merging would exceed subdivision threshold
                if len(large_nodes) + len(small_nodes) > self.subdivision_threshold:
                    # Skip this large community as it would become too large
                    continue

                similarity = self.calculate_community_similarity(small_nodes, large_nodes)
                if similarity > best_similarity and similarity >= self.merge_similarity_threshold:
                    best_similarity = similarity
                    best_match_id = large_comm_id

            # If no good match found among large communities, try other small communities
            if best_match_id is None:
                for other_small_id, other_small_nodes in small_communities.items():
                    if other_small_id == small_comm_id:
                        continue

                    # Check if merging would exceed subdivision threshold
                    if len(small_nodes) + len(other_small_nodes) > self.subdivision_threshold:
                        # Skip this small community as it would become too large
                        continue

                    similarity = self.calculate_community_similarity(small_nodes, other_small_nodes)
                    if similarity > best_similarity and similarity >= self.merge_similarity_threshold:
                        best_similarity = similarity
                        best_match_id = other_small_id

            # If a good match was found, merge with it
            if best_match_id is not None:
                if best_match_id in merged_communities:
                    # Add nodes to existing community
                    merged_communities[best_match_id].extend(small_nodes)
                    logger.info(f"Merged community {small_comm_id} ({len(small_nodes)} functions) into {best_match_id} "
                               f"(similarity: {best_similarity:.3f})")
                else:
                    # Create a new merged community from two small ones
                    merged_nodes = list(small_nodes)
                    if best_match_id in small_communities:
                        merged_nodes.extend(small_communities[best_match_id])

                    merged_communities[small_comm_id] = merged_nodes
                    logger.info(f"Created new merged community {small_comm_id} by combining with {best_match_id} "
                               f"(similarity: {best_similarity:.3f})")
            else:
                # No good match found, keep as is
                merged_communities[small_comm_id] = small_nodes
                logger.info(f"No good match found for community {small_comm_id} ({len(small_nodes)} functions), keeping as is")

        # Count how many communities were merged
        original_count = len(communities_dict)
        merged_count = len(merged_communities)
        logger.info(f"Merged {original_count - merged_count} communities, {merged_count} communities remaining")

        return merged_communities

    def add_miscellaneous_domain(self, communities: List[Community]) -> List[Community]:
        """
        Add a miscellaneous domain for functions that don't belong to any community.

        Args:
            communities: List of existing communities

        Returns:
            Updated list of communities with miscellaneous domain added
        """
        if not self.include_miscellaneous:
            return communities

        # Get all functions in existing communities
        functions_in_communities = set()
        for community in communities:
            functions_in_communities.update(community.functions)

        # Get all functions from the embeddings
        all_functions = set()
        for idx, row in self.embeddings_df.iterrows():
            function_id = f"{row['file_path']}:{row['name']}"
            all_functions.add(function_id)

        # Find functions not in any community
        missing_functions = all_functions - functions_in_communities

        if missing_functions:
            # Create a miscellaneous domain
            misc_community = Community(
                id=len(communities) + 1,
                functions=list(missing_functions),
                level=0,
                name="Miscellaneous Functions",
                description="Functions that don't fit well into other domains"
            )

            logger.info(f"Added miscellaneous domain with {len(missing_functions)} functions "
                       f"({len(missing_functions) / len(all_functions) * 100:.1f}% of total)")

            # Add to communities list
            communities.append(misc_community)
        else:
            logger.info("All functions are included in domains, no miscellaneous domain needed")

        return communities

    async def run(self) -> None:
        """
        Run the clustering process.
        """
        logger.info(f"Starting flat clustering with algorithm: {self.algorithm}, "
                   f"similarity_threshold={self.similarity_threshold}, "
                   f"min_community_size={self.min_community_size}")

        # Check if the embeddings file exists
        if not os.path.exists(self.embeddings_path):
            logger.error(f"Embeddings file not found: {self.embeddings_path}")
            return

        # Load the embeddings
        logger.info(f"Loading embeddings from: {self.embeddings_path}")
        self.embeddings_df = pd.read_parquet(self.embeddings_path)
        logger.info(f"Loaded {len(self.embeddings_df)} function embeddings")

        # Build the similarity graph
        logger.info("Building similarity graph")
        self.build_similarity_graph()

        # Save the graph for visualization
        self.save_similarity_graph(self.similarity_graph_path)
        logger.info(f"Saved similarity graph to: {self.similarity_graph_path}")

        # Detect communities
        logger.info(f"Detecting communities using {self.algorithm} algorithm")
        communities_dict = self.detect_communities()

        # Merge small communities if enabled
        if self.enable_community_merging:
            communities_dict = self.merge_small_communities(communities_dict)

        # Handle overlapping communities if enabled
        if self.allow_overlap and not self.strict_assignment:
            communities_dict = self.handle_overlapping_communities(communities_dict)

        # Enforce strict assignment if enabled (one function per community)
        if self.strict_assignment:
            communities_dict = self.enforce_strict_assignment(communities_dict)

        # Create Community objects
        communities = self.create_communities(communities_dict)
        logger.info(f"Created {len(communities)} communities with {sum(len(c.functions) for c in communities)} functions")

        # Add miscellaneous domain for functions not in any community
        communities = self.add_miscellaneous_domain(communities)

        # Name communities using LLM
        logger.info("Naming communities using LLM")
        await self.name_communities(communities)

        # Save domains as JSON and YAML
        self.save_domains_json(communities, self.domains_json)
        self.save_domains_yaml(communities, self.domains_yaml)

        # Print summary statistics
        logger.info(f"Clustering complete. Found {len(communities)} domains.")

        # Print top 5 largest domains
        if communities:
            largest_communities = sorted(communities, key=lambda c: len(c.functions), reverse=True)[:5]
            logger.info("Top 5 largest domains:")
            for i, community in enumerate(largest_communities, 1):
                logger.info(f"{i}. {community.name}: {len(community.functions)} functions")


async def main():
    """
    Main entry point for the flat clustering script.
    """
    parser = argparse.ArgumentParser(description="Flat clustering for function embeddings")
    parser.add_argument("--input", help="Directory containing the input artifacts (function_embeddings.parquet)")
    parser.add_argument("--output", help="Directory to save output artifacts. If not provided, uses input directory.")
    parser.add_argument("--algorithm", choices=["louvain", "spectral", "dbscan", "kmeans"], default="louvain",
                        help="Clustering algorithm to use")
    parser.add_argument("--similarity-threshold", type=float, default=0.7,
                        help="Minimum similarity score to create an edge (0-1)")
    parser.add_argument("--min-community-size", type=int, default=5,
                        help="Minimum number of functions in a community")
    parser.add_argument("--max-community-size", type=int, default=1000,
                        help="Maximum number of functions in a community")
    parser.add_argument("--resolution", type=float, default=0.6,
                        help="Resolution parameter for Louvain community detection")
    parser.add_argument("--include-miscellaneous", action="store_true",
                        help="Include a miscellaneous domain for unclustered functions")

    # Subdivide large communities
    parser.add_argument("--subdivide-large-communities", default=True, action="store_true",
                        help="Recursively subdivide large communities")
    parser.add_argument("--subdivision-threshold", type=int, default=100,
                        help="Size threshold for subdividing communities (independent of max_community_size)")
    parser.add_argument("--subdivision-resolution-factor", type=float, default=2.5,
                        help="Factor to increase resolution when subdividing")
    parser.add_argument("--enable-recursive-subdivision", default=True, action="store_true",
                        help="Enable recursive subdivision for communities that are still too large after first subdivision")

    # Small community merging parameters
    parser.add_argument("--enable-community-merging", default=True, action="store_true",
                        help="Merge small communities with similar larger ones")
    parser.add_argument("--small-community-threshold", type=int, default=10,
                        help="Communities smaller than this are considered small")
    parser.add_argument("--merge-similarity-threshold", type=float, default=0.5,
                        help="Minimum similarity to merge communities")

    parser.add_argument("--allow-overlap", default=False, action="store_true",
                        help="Allow functions to be in multiple domains")
    parser.add_argument("--overlap-threshold", type=float, default=0.6,
                        help="Threshold for including a function in multiple domains")
    parser.add_argument("--strict-assignment", default=True, action="store_true",
                        help="Ensure each function belongs to only one cluster (overrides allow-overlap)")

    parser.add_argument("--n-clusters", type=int, default=40,
                        help="Number of clusters for spectral and kmeans")
    parser.add_argument("--eps", type=float, default=0.3,
                        help="Maximum distance between samples for DBSCAN")
    parser.add_argument("--min-samples", type=int, default=5,
                        help="Minimum number of samples in a neighborhood for DBSCAN")
    parser.add_argument("--openai-api-key", type=str, default=None,
                        help="OpenAI API key")
    parser.add_argument("--openai-model", type=str, default="gpt-4o-mini",
                        help="OpenAI model to use for naming communities")

    args = parser.parse_args()

    args.input = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/exp_embeddings/django"
    args.output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/exp_embeddings/django/param_exp"

    # Initialize the clusterer
    clusterer = FlatClusterer(
        input_dir=args.input,
        output_dir=args.output,
        algorithm=args.algorithm,
        similarity_threshold=args.similarity_threshold,
        min_community_size=args.min_community_size,
        max_community_size=args.max_community_size,
        resolution=args.resolution,
        n_clusters=args.n_clusters,
        eps=args.eps,
        min_samples=args.min_samples,
        allow_overlap=args.allow_overlap,
        overlap_threshold=args.overlap_threshold,
        strict_assignment=args.strict_assignment,
        include_miscellaneous=args.include_miscellaneous,
        subdivide_large_communities=args.subdivide_large_communities,
        subdivision_threshold=args.subdivision_threshold,
        subdivision_resolution_factor=args.subdivision_resolution_factor,
        enable_recursive_subdivision=args.enable_recursive_subdivision,
        enable_community_merging=args.enable_community_merging,
        small_community_threshold=args.small_community_threshold,
        merge_similarity_threshold=args.merge_similarity_threshold,
        openai_api_key=args.openai_api_key,
        openai_model=args.openai_model,
    )

    # Run the clustering
    await clusterer.run()


if __name__ == "__main__":
    asyncio.run(main())
