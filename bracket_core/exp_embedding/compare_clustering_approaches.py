"""
<PERSON><PERSON><PERSON> to compare the results of hierarchical and flat clustering approaches.

This script loads the results of both clustering approaches and compares them
in terms of domain coverage, function distribution, and other metrics.
"""

import os
import argparse
import json
import yaml
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Any, Tuple, Set
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def load_hierarchical_domains(file_path: str) -> Tuple[List[Dict[str, Any]], Set[str]]:
    """
    Load hierarchical domains from a JSON file.

    Args:
        file_path: Path to the JSON file

    Returns:
        Tuple of (list of domains, set of unique functions)
    """
    logger.info(f"Loading hierarchical domains from: {file_path}")
    
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        # Extract leaf domains
        domains = []
        all_functions = set()
        
        for domain in data.get("domains", []):
            if not domain.get("children", []):  # Leaf domain
                domains.append({
                    "name": domain.get("name", ""),
                    "description": domain.get("description", ""),
                    "functions": domain.get("functions", []),
                })
                all_functions.update(domain.get("functions", []))
        
        return domains, all_functions
    
    except Exception as e:
        logger.error(f"Error loading hierarchical domains: {e}")
        return [], set()


def load_flat_domains(file_path: str) -> Tuple[List[Dict[str, Any]], Set[str]]:
    """
    Load flat domains from a JSON file.

    Args:
        file_path: Path to the JSON file

    Returns:
        Tuple of (list of domains, set of unique functions)
    """
    logger.info(f"Loading flat domains from: {file_path}")
    
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        domains = []
        all_functions = set()
        
        for domain in data.get("domains", []):
            domains.append({
                "name": domain.get("name", ""),
                "description": domain.get("description", ""),
                "functions": domain.get("functions", []),
            })
            all_functions.update(domain.get("functions", []))
        
        return domains, all_functions
    
    except Exception as e:
        logger.error(f"Error loading flat domains: {e}")
        return [], set()


def load_function_embeddings(file_path: str) -> int:
    """
    Load function embeddings to get the total number of functions.

    Args:
        file_path: Path to the parquet file

    Returns:
        Total number of functions
    """
    logger.info(f"Loading function embeddings from: {file_path}")
    
    try:
        df = pd.read_parquet(file_path)
        return len(df)
    
    except Exception as e:
        logger.error(f"Error loading function embeddings: {e}")
        return 0


def calculate_metrics(
    domains: List[Dict[str, Any]],
    unique_functions: Set[str],
    total_functions: int
) -> Dict[str, Any]:
    """
    Calculate metrics for the domains.

    Args:
        domains: List of domains
        unique_functions: Set of unique functions
        total_functions: Total number of functions in the codebase

    Returns:
        Dictionary of metrics
    """
    # Count functions in each domain
    domain_sizes = [len(domain.get("functions", [])) for domain in domains]
    
    # Count functions that appear in multiple domains
    function_counts = {}
    for domain in domains:
        for function in domain.get("functions", []):
            function_counts[function] = function_counts.get(function, 0) + 1
    
    multi_domain_functions = sum(1 for count in function_counts.values() if count > 1)
    
    # Calculate metrics
    metrics = {
        "num_domains": len(domains),
        "total_functions_in_domains": sum(domain_sizes),
        "unique_functions_in_domains": len(unique_functions),
        "multi_domain_functions": multi_domain_functions,
        "coverage_percentage": round(len(unique_functions) / total_functions * 100, 2) if total_functions > 0 else 0,
        "avg_domain_size": round(sum(domain_sizes) / len(domains), 2) if domains else 0,
        "min_domain_size": min(domain_sizes) if domains else 0,
        "max_domain_size": max(domain_sizes) if domains else 0,
        "median_domain_size": sorted(domain_sizes)[len(domain_sizes) // 2] if domains else 0,
    }
    
    return metrics


def compare_approaches(
    hierarchical_file: str,
    flat_file: str,
    embeddings_file: str,
    output_dir: str
) -> None:
    """
    Compare hierarchical and flat clustering approaches.

    Args:
        hierarchical_file: Path to the hierarchical domains JSON file
        flat_file: Path to the flat domains JSON file
        embeddings_file: Path to the function embeddings parquet file
        output_dir: Directory to save comparison results
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Load data
    hierarchical_domains, hierarchical_functions = load_hierarchical_domains(hierarchical_file)
    flat_domains, flat_functions = load_flat_domains(flat_file)
    total_functions = load_function_embeddings(embeddings_file)
    
    # Calculate metrics
    hierarchical_metrics = calculate_metrics(hierarchical_domains, hierarchical_functions, total_functions)
    flat_metrics = calculate_metrics(flat_domains, flat_functions, total_functions)
    
    # Compare function coverage
    common_functions = hierarchical_functions.intersection(flat_functions)
    only_hierarchical = hierarchical_functions - flat_functions
    only_flat = flat_functions - hierarchical_functions
    
    coverage_comparison = {
        "total_functions": total_functions,
        "common_functions": len(common_functions),
        "only_hierarchical": len(only_hierarchical),
        "only_flat": len(only_flat),
        "hierarchical_coverage": round(len(hierarchical_functions) / total_functions * 100, 2) if total_functions > 0 else 0,
        "flat_coverage": round(len(flat_functions) / total_functions * 100, 2) if total_functions > 0 else 0,
    }
    
    # Combine all metrics
    comparison = {
        "hierarchical": hierarchical_metrics,
        "flat": flat_metrics,
        "coverage_comparison": coverage_comparison,
    }
    
    # Save comparison results
    with open(os.path.join(output_dir, "comparison_results.json"), 'w') as f:
        json.dump(comparison, f, indent=2)
    
    # Create comparison visualizations
    create_visualizations(comparison, output_dir)
    
    # Print summary
    print_summary(comparison)


def create_visualizations(comparison: Dict[str, Any], output_dir: str) -> None:
    """
    Create visualizations comparing the approaches.

    Args:
        comparison: Dictionary of comparison metrics
        output_dir: Directory to save visualizations
    """
    # Set up the style
    sns.set(style="whitegrid")
    
    # 1. Domain size distribution
    plt.figure(figsize=(12, 6))
    
    # Create data for plotting
    hierarchical_sizes = [len(domain.get("functions", [])) for domain in hierarchical_domains]
    flat_sizes = [len(domain.get("functions", [])) for domain in flat_domains]
    
    # Plot histograms
    sns.histplot(hierarchical_sizes, kde=True, label="Hierarchical", alpha=0.6)
    sns.histplot(flat_sizes, kde=True, label="Flat", alpha=0.6)
    
    plt.title("Domain Size Distribution")
    plt.xlabel("Number of Functions")
    plt.ylabel("Frequency")
    plt.legend()
    plt.savefig(os.path.join(output_dir, "domain_size_distribution.png"))
    
    # 2. Coverage comparison
    plt.figure(figsize=(10, 6))
    
    coverage_data = {
        "Approach": ["Hierarchical", "Flat"],
        "Coverage (%)": [
            comparison["coverage_comparison"]["hierarchical_coverage"],
            comparison["coverage_comparison"]["flat_coverage"]
        ]
    }
    
    coverage_df = pd.DataFrame(coverage_data)
    sns.barplot(x="Approach", y="Coverage (%)", data=coverage_df)
    
    plt.title("Function Coverage Comparison")
    plt.ylim(0, 100)
    plt.savefig(os.path.join(output_dir, "coverage_comparison.png"))
    
    # 3. Metrics comparison
    plt.figure(figsize=(14, 8))
    
    metrics_data = {
        "Metric": [
            "Number of Domains",
            "Average Domain Size",
            "Median Domain Size",
            "Multi-Domain Functions"
        ],
        "Hierarchical": [
            comparison["hierarchical"]["num_domains"],
            comparison["hierarchical"]["avg_domain_size"],
            comparison["hierarchical"]["median_domain_size"],
            comparison["hierarchical"]["multi_domain_functions"]
        ],
        "Flat": [
            comparison["flat"]["num_domains"],
            comparison["flat"]["avg_domain_size"],
            comparison["flat"]["median_domain_size"],
            comparison["flat"]["multi_domain_functions"]
        ]
    }
    
    metrics_df = pd.DataFrame(metrics_data)
    metrics_df_melted = pd.melt(metrics_df, id_vars=["Metric"], var_name="Approach", value_name="Value")
    
    g = sns.catplot(x="Metric", y="Value", hue="Approach", data=metrics_df_melted, kind="bar", height=6, aspect=1.5)
    g.set_xticklabels(rotation=45, ha="right")
    plt.title("Metrics Comparison")
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "metrics_comparison.png"))


def print_summary(comparison: Dict[str, Any]) -> None:
    """
    Print a summary of the comparison.

    Args:
        comparison: Dictionary of comparison metrics
    """
    logger.info("=== Comparison Summary ===")
    
    logger.info("\nHierarchical Clustering:")
    logger.info(f"- Number of domains: {comparison['hierarchical']['num_domains']}")
    logger.info(f"- Unique functions: {comparison['hierarchical']['unique_functions_in_domains']}")
    logger.info(f"- Coverage: {comparison['hierarchical']['coverage_percentage']}%")
    logger.info(f"- Average domain size: {comparison['hierarchical']['avg_domain_size']}")
    logger.info(f"- Multi-domain functions: {comparison['hierarchical']['multi_domain_functions']}")
    
    logger.info("\nFlat Clustering:")
    logger.info(f"- Number of domains: {comparison['flat']['num_domains']}")
    logger.info(f"- Unique functions: {comparison['flat']['unique_functions_in_domains']}")
    logger.info(f"- Coverage: {comparison['flat']['coverage_percentage']}%")
    logger.info(f"- Average domain size: {comparison['flat']['avg_domain_size']}")
    logger.info(f"- Multi-domain functions: {comparison['flat']['multi_domain_functions']}")
    
    logger.info("\nCoverage Comparison:")
    logger.info(f"- Total functions in codebase: {comparison['coverage_comparison']['total_functions']}")
    logger.info(f"- Functions in both approaches: {comparison['coverage_comparison']['common_functions']}")
    logger.info(f"- Functions only in hierarchical: {comparison['coverage_comparison']['only_hierarchical']}")
    logger.info(f"- Functions only in flat: {comparison['coverage_comparison']['only_flat']}")
    
    # Determine which approach has better coverage
    if comparison['coverage_comparison']['hierarchical_coverage'] > comparison['coverage_comparison']['flat_coverage']:
        logger.info("\nHierarchical clustering has better function coverage.")
    elif comparison['coverage_comparison']['flat_coverage'] > comparison['coverage_comparison']['hierarchical_coverage']:
        logger.info("\nFlat clustering has better function coverage.")
    else:
        logger.info("\nBoth approaches have the same function coverage.")
    
    # Provide recommendations
    logger.info("\nRecommendations:")
    if comparison['flat']['multi_domain_functions'] > comparison['hierarchical']['multi_domain_functions']:
        logger.info("- Flat clustering with overlap provides better multi-domain function coverage.")
    
    if comparison['flat']['num_domains'] > comparison['hierarchical']['num_domains']:
        logger.info("- Flat clustering identifies more domains, which may provide more granular insights.")
    else:
        logger.info("- Hierarchical clustering provides a more structured view of the codebase.")


def main():
    """
    Main entry point for the script.
    """
    parser = argparse.ArgumentParser(description="Compare hierarchical and flat clustering approaches")
    parser.add_argument("--hierarchical", required=True, help="Path to the hierarchical domains JSON file")
    parser.add_argument("--flat", required=True, help="Path to the flat domains JSON file")
    parser.add_argument("--embeddings", required=True, help="Path to the function embeddings parquet file")
    parser.add_argument("--output", required=True, help="Directory to save comparison results")
    
    args = parser.parse_args()
    
    # Compare approaches
    compare_approaches(
        hierarchical_file=args.hierarchical,
        flat_file=args.flat,
        embeddings_file=args.embeddings,
        output_dir=args.output
    )


if __name__ == "__main__":
    # Define global variables for visualization
    hierarchical_domains = []
    flat_domains = []
    
    main()
