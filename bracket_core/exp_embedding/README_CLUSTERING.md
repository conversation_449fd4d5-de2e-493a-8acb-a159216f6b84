# Embedding Clustering Experiments

This directory contains scripts for experimenting with different clustering parameters on function embeddings. These scripts allow you to tune the parameters of the domain analysis without re-running the entire pipeline.

## Scripts

1. `cluster_embeddings.py`: Standalone script for clustering function embeddings with specific parameters
2. `run_clustering_experiments.py`: <PERSON><PERSON><PERSON> to run multiple clustering experiments with different parameter combinations
3. `compare_clustering_results.py`: <PERSON>ript to compare the results of different clustering experiments

## Usage

### 1. Clustering with Specific Parameters

To cluster function embeddings with specific parameters:

```bash
python -m bracket_core.exp_embedding.cluster_embeddings \
  --input /path/to/embeddings/directory \
  --output /path/to/output/directory \
  --similarity-threshold 0.8 \
  --min-community-size 5 \
  --resolution 1.0 \
  --hierarchical-levels 4
```

This will:
1. Load function embeddings from `/path/to/embeddings/directory/function_embeddings.parquet`
2. Cluster the embeddings with the specified parameters
3. Save the results to the output directory with filenames that include the parameter values

### 2. Running Multiple Experiments

To run multiple clustering experiments with different parameter combinations:

```bash
python -m bracket_core.exp_embedding.run_clustering_experiments \
  --input /path/to/embeddings/directory \
  --output /path/to/experiments/directory \
  --similarity-thresholds 0.7 0.8 0.9 \
  --min-community-sizes 3 5 10 \
  --resolutions 0.8 1.0 1.2 \
  --hierarchical-levels 3 4 5 \
  --max-concurrent 3
```

This will:
1. Generate all combinations of the specified parameters (in this case, 3×3×3×3 = 81 combinations)
2. Run clustering experiments for each combination, with up to 3 experiments running concurrently
3. Save the results to subdirectories of the output directory, named after the parameter values
4. Save a summary of all experiments to `experiment_results.json`

### 3. Comparing Experiment Results

To compare the results of multiple clustering experiments:

```bash
python -m bracket_core.exp_embedding.compare_clustering_results \
  --experiments-dir /path/to/experiments/directory \
  --output /path/to/comparison/report.csv
```

This will:
1. Analyze the results of all experiments in the experiments directory
2. Generate a comparison report with metrics for each experiment
3. Save the report to a CSV file
4. Print a summary of the results, including the top 3 experiments by number of leaf domains

## Parameters

The main parameters that affect clustering quality are:

- `similarity_threshold`: Minimum similarity score to create an edge between functions (0-1)
  - Higher values create fewer, more tightly connected communities
  - Lower values create more, loosely connected communities
  - Recommended range: 0.7-0.9

- `min_community_size`: Minimum number of functions in a community
  - Higher values filter out small, potentially noisy communities
  - Lower values preserve small but potentially meaningful communities
  - Recommended range: 3-10

- `resolution`: Resolution parameter for community detection
  - Higher values create more, smaller communities
  - Lower values create fewer, larger communities
  - Recommended range: 0.8-1.2

- `hierarchical_levels`: Number of hierarchical levels to build
  - Higher values create deeper hierarchies
  - Lower values create flatter hierarchies
  - Recommended range: 3-5

## Output Files

Each experiment generates the following files:

- `domain_analysis_<experiment_id>.yaml`: Domain hierarchy with all domains and functions
- `domain_analysis_processed_<experiment_id>.yaml`: Post-processed domain hierarchy with duplicate functions removed
- `domain_hierarchy_<experiment_id>.json`: JSON file containing only leaf domains
- `similarity_graph_<experiment_id>.graphml`: GraphML file for visualizing the similarity graph

## Example Workflow

1. Run the full pipeline once to generate function embeddings:
   ```bash
   python -m bracket_core.exp_embedding.faster_irl \
     --repo /path/to/repo \
     --output /path/to/output
   ```

2. Run multiple clustering experiments with different parameters:
   ```bash
   python -m bracket_core.exp_embedding.run_clustering_experiments \
     --input /path/to/output \
     --output /path/to/experiments \
     --similarity-thresholds 0.7 0.8 0.9 \
     --min-community-sizes 3 5 10 \
     --resolutions 0.8 1.0 1.2 \
     --hierarchical-levels 3 4 5
   ```

3. Compare the results to find the best parameter combination:
   ```bash
   python -m bracket_core.exp_embedding.compare_clustering_results \
     --experiments-dir /path/to/experiments \
     --output /path/to/comparison.csv
   ```

4. Use the best parameters for the final clustering:
   ```bash
   python -m bracket_core.exp_embedding.cluster_embeddings \
     --input /path/to/output \
     --output /path/to/final \
     --similarity-threshold 0.8 \
     --min-community-size 5 \
     --resolution 1.0 \
     --hierarchical-levels 4
   ```
