import os
import logging
import pandas as pd
import json
import asyncio
import numpy as np
from tqdm import tqdm
import networkx as nx
import yaml
from typing import Optional, Any, Dict, List, Tuple, NamedTuple
from dataclasses import dataclass, field
from sklearn.metrics.pairwise import cosine_similarity
from scipy.sparse import csr_matrix
import community as community_louvain
from collections import defaultdict

# Import API key management
from bracket_core.llm.api_keys import get_openai_api_key, get_anthropic_api_key, get_openrouter_api_key

# Import necessary functions from bracket_core
from bracket_core.parsing_repomap import EnhancedRepoMap, SimpleTokenCounter, SimpleIO
from bracket_core.documenting import convert_parquet_to_simplified_csv
from bracket_core.hybrid_kg import generate_hybrid_knowledge_graph, graph_to_dataframes as hybrid_graph_to_dataframes
from bracket_core.llm.oai.embedding import OpenAIEmbedding

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class Community:
    """Represents a community of functions identified through graph clustering."""
    id: int
    functions: List[str]  # List of function IDs
    parent_id: Optional[int] = None
    children_ids: List[int] = field(default_factory=list)
    name: str = ""
    description: str = ""
    level: int = 0  # Hierarchy level (0 = leaf)

    @property
    def size(self) -> int:
        """Return the number of functions in this community."""
        return len(self.functions)

@dataclass
class DomainHierarchy:
    """Represents a hierarchical structure of domains built from communities."""
    communities: Dict[int, Community] = field(default_factory=dict)
    root_ids: List[int] = field(default_factory=list)

    def add_community(self, community: Community) -> None:
        """Add a community to the hierarchy."""
        self.communities[community.id] = community
        if community.parent_id is None:
            self.root_ids.append(community.id)
        else:
            if community.parent_id in self.communities:
                self.communities[community.parent_id].children_ids.append(community.id)

    def get_community(self, community_id: int) -> Optional[Community]:
        """Get a community by ID."""
        return self.communities.get(community_id)

    def get_leaf_communities(self) -> List[Community]:
        """Get all leaf communities (those with no children)."""
        return [c for c in self.communities.values() if not c.children_ids]

    def to_dict(self) -> Dict[str, Any]:
        """Convert the hierarchy to a dictionary for serialization."""
        result = {
            "areas": []
        }

        # Process root communities
        for root_id in self.root_ids:
            root_community = self.communities[root_id]
            result["areas"].append(self._community_to_dict(root_community))

        return result

    def _community_to_dict(self, community: Community) -> Dict[str, Any]:
        """
        Convert a community to a dictionary for serialization.

        For parent communities, only include functions that are not already in child communities.
        This ensures functions only appear in the most specific domain they belong to.
        """
        # Get all functions from child communities
        child_functions = set()
        for child_id in community.children_ids:
            child_community = self.communities.get(child_id)
            if child_community:
                child_functions.update(child_community.functions)

        # Only include functions that are not in child communities
        unique_functions = [f for f in community.functions if f not in child_functions]

        result = {
            "name": community.name,
            "description": community.description,
            "functions": unique_functions
        }

        # Add children if any
        if community.children_ids:
            result["sub_areas"] = []
            for child_id in community.children_ids:
                child_community = self.communities[child_id]
                result["sub_areas"].append(self._community_to_dict(child_community))

        return result

class SemanticSimilarityGraph:
    """
    Builds a graph of functions connected by semantic similarity and identifies communities.

    This class implements the Semantic Similarity Graph approach for identifying
    leaf domains in a codebase. It builds a graph where nodes are functions and
    edges are weighted by embedding similarity, then applies community detection
    algorithms to identify densely connected subgraphs as leaf domains.
    """

    def __init__(
        self,
        similarity_threshold: float = 0.7,
        min_community_size: int = 3,
        max_community_size: int = 100,
        resolution: float = 1.0,
        hierarchical_levels: int = 3,
    ):
        """
        Initialize the semantic similarity graph.

        Args:
            similarity_threshold: Minimum similarity score to create an edge (0-1)
            min_community_size: Minimum number of functions in a community
            max_community_size: Maximum number of functions in a community
            resolution: Resolution parameter for community detection (higher = more communities)
            hierarchical_levels: Number of hierarchical levels to build
        """
        self.similarity_threshold = similarity_threshold
        self.min_community_size = min_community_size
        self.max_community_size = max_community_size
        self.resolution = resolution
        self.hierarchical_levels = hierarchical_levels

        # Initialize graph and data structures
        self.graph = nx.Graph()
        self.embeddings_df = None
        self.communities = {}
        self.hierarchy = DomainHierarchy()

    def build_similarity_graph(self, embeddings_df: pd.DataFrame) -> nx.Graph:
        """
        Build a graph where nodes are functions and edges are weighted by embedding similarity.

        Args:
            embeddings_df: DataFrame containing function embeddings

        Returns:
            NetworkX graph with functions as nodes and similarity scores as edge weights
        """
        logger.info("Building similarity graph from function embeddings")
        self.embeddings_df = embeddings_df

        # Extract embeddings as numpy array
        embeddings = np.array(embeddings_df['embedding'].tolist())

        # Compute pairwise cosine similarity
        logger.info("Computing pairwise cosine similarity")
        similarity_matrix = cosine_similarity(embeddings)

        # Create graph nodes
        logger.info("Creating graph nodes")
        for idx, row in tqdm(embeddings_df.iterrows(), total=len(embeddings_df), desc="Adding nodes"):
            node_id = idx
            self.graph.add_node(
                node_id,
                name=row.get('name', ''),
                file_path=row.get('file_path', ''),
                significance=row.get('architectural_significance', 0.0),
                description=row.get('description', '')
            )

        # Create graph edges based on similarity threshold
        logger.info(f"Creating graph edges with similarity threshold {self.similarity_threshold}")
        edge_count = 0
        for i in tqdm(range(len(embeddings_df)), desc="Adding edges"):
            for j in range(i+1, len(embeddings_df)):
                similarity = similarity_matrix[i, j]
                if similarity >= self.similarity_threshold:
                    self.graph.add_edge(i, j, weight=similarity)
                    edge_count += 1

        logger.info(f"Created graph with {len(self.graph.nodes)} nodes and {edge_count} edges")
        return self.graph

    def detect_communities(self) -> Dict[int, List[int]]:
        """
        Apply community detection to identify densely connected subgraphs.

        Returns:
            Dictionary mapping community IDs to lists of function indices
        """
        logger.info("Detecting communities using Louvain algorithm")

        # Apply Louvain community detection
        partition = community_louvain.best_partition(self.graph, resolution=self.resolution)

        # Group functions by community
        communities = defaultdict(list)
        for node, community_id in partition.items():
            communities[community_id].append(node)

        # Filter communities by size
        filtered_communities = {}
        for community_id, nodes in communities.items():
            if self.min_community_size <= len(nodes) <= self.max_community_size:
                filtered_communities[community_id] = nodes

        logger.info(f"Detected {len(filtered_communities)} communities after filtering by size")
        self.communities = filtered_communities
        return filtered_communities

    def build_hierarchical_communities(self) -> DomainHierarchy:
        """
        Build a hierarchy of communities by applying community detection recursively.

        Returns:
            DomainHierarchy object representing the hierarchical structure
        """
        logger.info(f"Building hierarchical communities with {self.hierarchical_levels} levels")

        # Start with leaf communities (level 0)
        leaf_communities = self.detect_communities()

        # Create Community objects for leaf communities
        for community_id, function_indices in leaf_communities.items():
            if self.embeddings_df is not None:
                function_ids = [
                    f"{self.embeddings_df.iloc[idx]['file_path']}:{self.embeddings_df.iloc[idx]['name']}"
                    for idx in function_indices
                ]
                community = Community(
                    id=community_id,
                    functions=function_ids,
                    level=0
                )
                self.hierarchy.add_community(community)

        # Build higher levels of the hierarchy
        for level in range(1, self.hierarchical_levels):
            logger.info(f"Building level {level} of the hierarchy")

            # Create a new graph where nodes are communities from the previous level
            community_graph = nx.Graph()

            # Add nodes (communities from previous level)
            communities_from_prev_level = [c for c in self.hierarchy.communities.values() if c.level == level-1]
            for community in communities_from_prev_level:
                community_graph.add_node(community.id)

            # Calculate inter-community similarity and add edges
            for i, comm1 in enumerate(communities_from_prev_level):
                for comm2 in communities_from_prev_level[i+1:]:
                    # Calculate similarity between communities
                    similarity = self._calculate_community_similarity(comm1, comm2)
                    if similarity >= self.similarity_threshold / 2:  # Lower threshold for higher levels
                        community_graph.add_edge(comm1.id, comm2.id, weight=similarity)

            # Apply community detection to the community graph
            if len(community_graph.nodes) > 1:
                partition = community_louvain.best_partition(community_graph, resolution=self.resolution * 0.8)  # Lower resolution for higher levels

                # Group communities by their new parent community
                parent_communities = defaultdict(list)
                for node, parent_id in partition.items():
                    # Create a unique ID for the parent community at this level
                    unique_parent_id = 10000 * (level + 1) + parent_id
                    parent_communities[unique_parent_id].append(node)

                # Create Community objects for parent communities
                for parent_id, child_ids in parent_communities.items():
                    if len(child_ids) >= 2:  # Only create parent if it has at least 2 children
                        # Create parent community WITHOUT duplicating child functions
                        # This is the key change to prevent function duplication
                        parent_community = Community(
                            id=parent_id,
                            functions=[],  # Empty list - parent doesn't directly own functions
                            children_ids=child_ids,
                            level=level
                        )

                        # Update child's parent reference
                        for child_id in child_ids:
                            child_community = self.hierarchy.get_community(child_id)
                            if child_community:
                                child_community.parent_id = parent_id

                        self.hierarchy.add_community(parent_community)

        logger.info(f"Built hierarchy with {len(self.hierarchy.communities)} total communities")
        return self.hierarchy

    def _calculate_community_similarity(self, comm1: Community, comm2: Community) -> float:
        """
        Calculate similarity between two communities based on their function embeddings.

        Args:
            comm1: First community
            comm2: Second community

        Returns:
            Similarity score between the communities (0-1)
        """
        if self.embeddings_df is None:
            return 0.0

        # Get function indices for each community
        indices1 = [
            idx for idx, row in self.embeddings_df.iterrows()
            if f"{row['file_path']}:{row['name']}" in comm1.functions
        ]
        indices2 = [
            idx for idx, row in self.embeddings_df.iterrows()
            if f"{row['file_path']}:{row['name']}" in comm2.functions
        ]

        # Calculate average similarity between all pairs of functions
        total_similarity = 0.0
        count = 0

        for i in indices1:
            for j in indices2:
                if self.graph.has_edge(i, j):
                    total_similarity += self.graph[i][j]['weight']
                    count += 1

        # Return average similarity, or 0 if no connections
        return total_similarity / count if count > 0 else 0.0

    async def name_communities(self, openai_api_key: Optional[str] = None, model: str = "gpt-4o-mini") -> None:
        """
        Use LLMs to name and describe each community based on its functions.

        Args:
            openai_api_key: OpenAI API key
            model: OpenAI model to use
        """
        logger.info("Naming communities using LLM")

        from bracket_core.llm.oai.chat_openai import ChatOpenAI

        # Initialize OpenAI client
        chat_client = ChatOpenAI(
            api_key=openai_api_key or get_openai_api_key(),
            model=model
        )

        # Name communities in parallel
        tasks = []
        for community in self.hierarchy.communities.values():
            tasks.append(self._name_community(chat_client, community))

        await asyncio.gather(*tasks)

        logger.info("Finished naming communities")

    async def _name_community(self, chat_client, community: Community) -> None:
        """
        Name a single community using LLM.

        Args:
            chat_client: OpenAI chat client
            community: Community to name
        """
        # Get representative functions (up to 5)
        sample_size = min(5, len(community.functions))
        sample_functions = community.functions[:sample_size]

        # Get function details
        function_details = []
        for function_id in sample_functions:
            # Parse function ID to get file path and name
            parts = function_id.split(':')
            if len(parts) >= 2:
                file_path = parts[0]
                name = ':'.join(parts[1:])

                # Find function in DataFrame
                if self.embeddings_df is not None:
                    matches = self.embeddings_df[
                        (self.embeddings_df['file_path'] == file_path) &
                        (self.embeddings_df['name'] == name)
                    ]

                    if not matches.empty:
                        row = matches.iloc[0]
                        function_details.append({
                            'name': row.get('name', ''),
                            'file_path': row.get('file_path', ''),
                            'description': row.get('description', ''),
                            'code': row.get('function_code', '')[:500]  # Limit code to 500 chars
                        })

        # Create prompt for LLM
        prompt = f"""You are an expert software architect analyzing a codebase.
I'll provide you with details about a group of related functions that form a domain or module.
Your task is to:
1. Give this domain a concise, descriptive name (max 5 words)
2. Write a brief description of what this domain does (2-3 sentences)

Here are the functions in this domain:

"""

        for i, func in enumerate(function_details, 1):
            prompt += f"""Function {i}:
- Name: {func['name']}
- File: {func['file_path']}
- Description: {func['description']}
- Code snippet: {func['code']}

"""

        prompt += """Based on these functions, provide:
1. Domain name: [concise name]
2. Domain description: [brief description]

Return your answer in this exact format."""

        try:
            # Call LLM
            messages = [{"role": "user", "content": prompt}]
            response = await chat_client.agenerate(messages)

            # Parse response
            lines = response.split('\n')
            name = ""
            description = ""

            for line in lines:
                if line.startswith("1. Domain name:"):
                    name = line.replace("1. Domain name:", "").strip()
                elif line.startswith("Domain name:"):
                    name = line.replace("Domain name:", "").strip()
                elif line.startswith("2. Domain description:"):
                    description = line.replace("2. Domain description:", "").strip()
                elif line.startswith("Domain description:"):
                    description = line.replace("Domain description:", "").strip()

            # Update community
            community.name = name
            community.description = description

            logger.info(f"Named community {community.id}: {name}")

        except Exception as e:
            logger.error(f"Error naming community {community.id}: {e}")
            # Set default name if naming fails
            community.name = f"Domain {community.id}"
            community.description = f"A group of {len(community.functions)} related functions"

    def save_hierarchy(self, output_path: str) -> None:
        """
        Save the domain hierarchy to a YAML file.

        Args:
            output_path: Path to save the YAML file
        """
        logger.info(f"Saving domain hierarchy to {output_path}")

        # Convert hierarchy to dictionary
        hierarchy_dict = self.hierarchy.to_dict()

        # Save as YAML
        with open(output_path, 'w') as f:
            yaml.dump(hierarchy_dict, f, default_flow_style=False)

        logger.info(f"Saved domain hierarchy with {len(hierarchy_dict['areas'])} top-level domains")

    def save_leaf_domains_json(self, output_path: str) -> None:
        """
        Save only the leaf domains to a JSON file.

        Args:
            output_path: Path to save the JSON file
        """
        logger.info(f"Saving leaf domains to {output_path}")

        # Identify leaf domains (those without children)
        leaf_domains = []

        for community in self.hierarchy.communities.values():
            # A leaf domain has no children
            if not community.children_ids:
                leaf_domain = {
                    "name": community.name,
                    # "description": community.description,
                    # "function_count": len(community.functions),
                    # "functions": community.functions
                }
                leaf_domains.append(leaf_domain)

        # Sort leaf domains by name
        leaf_domains.sort(key=lambda x: x["name"])

        # Save as JSON
        with open(output_path, 'w') as f:
            json.dump(leaf_domains, f, indent=2)

        logger.info(f"Saved {len(leaf_domains)} leaf domains to JSON")

    def save_graph(self, output_path: str, format="graphml") -> None:
        """
        Save the similarity graph for visualization.

        Args:
            output_path: Path to save the graph file
            format: Format to save the graph in ("graphml" or "json")
        """
        logger.info(f"Saving similarity graph to {output_path}")

        # Save graph in the specified format
        if format.lower() == "json":
            # Convert graph to JSON-serializable format
            graph_data = {
                "nodes": [],
                "edges": []
            }

            # Add nodes with attributes
            for node, attrs in self.graph.nodes(data=True):
                node_data = {"id": node}
                node_data.update(attrs)
                graph_data["nodes"].append(node_data)

            # Add edges with attributes
            for source, target, attrs in self.graph.edges(data=True):
                edge_data = {"source": source, "target": target}
                edge_data.update(attrs)
                graph_data["edges"].append(edge_data)

            # Save as JSON
            with open(output_path, 'w') as f:
                json.dump(graph_data, f, indent=2)
        else:
            # Save as GraphML (default)
            nx.write_graphml(self.graph, output_path)

        logger.info(f"Saved similarity graph with {len(self.graph.nodes)} nodes and {len(self.graph.edges)} edges")

class BatchedEmbedding:
    """
    Batched embedding class for efficient processing of large datasets.
    Uses OpenAI's text-embedding-3-small model with high rate limits.
    """

    def __init__(
        self,
        api_key: Optional[str] = None,
        model: str = "text-embedding-3-small",
        batch_size: int = 100,
        max_tokens: int = 8191,
        max_retries: int = 10,
        request_timeout: float = 180.0,
    ):
        """
        Initialize the batched embedding class.

        Args:
            api_key: OpenAI API key. If None, will try to get from environment.
            model: Embedding model to use.
            batch_size: Number of texts to embed in a single batch.
            max_tokens: Maximum number of tokens per text.
            max_retries: Maximum number of retries for API calls.
            request_timeout: Timeout for API calls in seconds.
        """
        self.api_key = api_key or get_openai_api_key()
        self.model = model
        self.batch_size = batch_size
        self.max_tokens = max_tokens
        self.max_retries = max_retries
        self.request_timeout = request_timeout

        # Initialize the OpenAI embedding client
        self.embedding_client = OpenAIEmbedding(
            api_key=self.api_key,
            model=self.model,
            max_tokens=self.max_tokens,
            max_retries=self.max_retries,
            request_timeout=self.request_timeout,
        )

    async def embed_batch(self, texts: List[str]) -> List[List[float]]:
        """
        Embed a batch of texts in parallel.

        Args:
            texts: List of texts to embed.

        Returns:
            List of embeddings, one for each text.
        """
        tasks = [self.embedding_client.aembed(text) for text in texts]
        embeddings = await asyncio.gather(*tasks)
        return embeddings

    async def embed_dataset(self, texts: List[str], show_progress: bool = True) -> List[List[float]]:
        """
        Embed a large dataset of texts in batches.

        Args:
            texts: List of texts to embed.
            show_progress: Whether to show a progress bar.

        Returns:
            List of embeddings, one for each text.
        """
        all_embeddings = []

        # Process in batches
        for i in tqdm(range(0, len(texts), self.batch_size), disable=not show_progress):
            batch = texts[i:i + self.batch_size]
            batch_embeddings = await self.embed_batch(batch)
            all_embeddings.extend(batch_embeddings)

            # Small delay to avoid hitting rate limits
            await asyncio.sleep(0.1)

        return all_embeddings


class ExperimentalRepoAnalysisFlow:
    """
    Experimental version of the RepoAnalysisFlow for embedding-based approaches.
    This is a simplified version of the original RepoAnalysisFlow class from bracket_core/irl.py.
    """

    def __init__(
        self,
        repo_dir: str,
        output_dir: str,
        verbose: bool = False,
        document_functions: bool = True,
        llm_requests_per_minute: float = 1200,
        llm_tokens_per_minute: float = 1000000,
        # OpenAI parameters
        openai_api_key: Optional[str] = None,
        openai_model: str = "gpt-4o-mini",
        # Embedding parameters
        embedding_model: str = "text-embedding-3-small",
        embedding_batch_size: int = 100,
        # Domain analysis parameters
        similarity_threshold: float = 0.7,
        min_community_size: int = 3,
        max_community_size: int = 100,
        resolution: float = 1.0,
        hierarchical_levels: int = 3,
        # Parallelization parameters
        max_concurrent_tasks: int = 5,
        # Post-processing parameters
        skip_post_processing: bool = False,
    ):
        """
        Initialize the experimental repository analysis flow.

        Args:
            repo_dir: Path to the repository to analyze
            output_dir: Directory to save output artifacts
            verbose: Enable verbose logging
            document_functions: Whether to generate function documentation
            llm_requests_per_minute: Rate limit for LLM API requests
            llm_tokens_per_minute: Token rate limit for LLM API
            openai_api_key: OpenAI API key. If None, will try to get from environment.
            openai_model: OpenAI model to use
            embedding_model: Embedding model to use
            embedding_batch_size: Number of texts to embed in a single batch
            max_concurrent_tasks: Maximum number of concurrent tasks for parallel processing
        """
        self.repo_dir = repo_dir
        self.output_dir = output_dir
        self.verbose = verbose
        self.document_functions = document_functions
        self.llm_requests_per_minute = llm_requests_per_minute
        self.llm_tokens_per_minute = llm_tokens_per_minute

        # OpenAI parameters
        self.openai_api_key = openai_api_key
        self.openai_model = openai_model

        # Embedding parameters
        self.embedding_model = embedding_model
        self.embedding_batch_size = embedding_batch_size

        # Domain analysis parameters
        self.similarity_threshold = similarity_threshold
        self.min_community_size = min_community_size
        self.max_community_size = max_community_size
        self.resolution = resolution
        self.hierarchical_levels = hierarchical_levels

        # Parallelization parameters
        self.max_concurrent_tasks = max_concurrent_tasks

        # Post-processing parameters
        self.skip_post_processing = skip_post_processing

        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)

        # Initialize components
        self.repo_map = EnhancedRepoMap(
            root=repo_dir,
            verbose=verbose,
            main_model=SimpleTokenCounter(),
            io=SimpleIO(),
        )

        # Paths for intermediate artifacts with more descriptive names
        self.kg_output_path = os.path.join(output_dir, "code_knowledge_graph")
        self.documented_output_path = os.path.join(output_dir, "semantic_documented_functions.parquet")
        self.significant_functions_yaml = os.path.join(output_dir, "significant_functions.yaml")
        self.embeddings_output_path = os.path.join(output_dir, "function_embeddings.parquet")
        self.embeddings_metadata_path = os.path.join(output_dir, "embeddings_metadata.json")
        self.domain_analysis_yaml = os.path.join(output_dir, "domain_analysis.yaml")
        self.domain_analysis_processed_yaml = os.path.join(output_dir, "domain_analysis_processed.yaml")
        self.domain_hierarchy_json = os.path.join(output_dir, "domain_hierarchy.json")
        self.similarity_graph_path = os.path.join(output_dir, "similarity_graph.graphml")

    async def run(self) -> pd.DataFrame:
        """
        Execute the complete repository analysis workflow.

        Returns:
            DataFrame containing the final combined artifact
        """
        logger.info(f"Starting repository analysis for: {self.repo_dir}")

        # Step 1: Generate Knowledge Graph
        logger.info("Step 1: Generating Knowledge Graph")
        nx_graph = self.generate_knowledge_graph()

        # Step 2: Save graph as parquet for documentation step
        logger.info("Step 2: Saving intermediate graph artifacts")
        nodes_df = self.save_graph_as_parquet(nx_graph, self.kg_output_path)

        # Define path for intermediate file
        documented_csv_path = os.path.join(self.output_dir, "semantic_documented_functions_simplified.csv")

        # Step 3: Generate semantic documentation layer if enabled
        if self.document_functions:
            logger.info("Step 3: Generating semantic documentation layer")
            await self.generate_documentation(nodes_df)

            try:
                # Step 3b: Converting documented parquet to simplified CSV
                logger.info("Step 3b: Converting documented parquet to simplified CSV")
                convert_parquet_to_simplified_csv(
                    parquet_path=self.documented_output_path,
                    output_csv_path=documented_csv_path
                )

                # Step 3c: Generate YAML file for architecturally significant functions
                logger.info("Step 3c: Generating YAML file for architecturally significant functions")
                self.generate_significant_functions_yaml()
            except Exception as e:
                logger.error(f"Error in documentation post-processing: {e}")
                # Continue with the workflow even if documentation post-processing fails

        # Step 4: Generate embeddings for functions
        logger.info("Step 4: Generating embeddings for functions")
        try:
            await self.generate_embeddings()
            logger.info(f"Embeddings generated and saved to: {self.embeddings_output_path}")

            # Step 5: Perform domain analysis using semantic similarity graph
            logger.info("Step 5: Performing domain analysis using semantic similarity graph")
            await self.analyze_domains_with_embeddings()
            logger.info(f"Domain analysis complete. Results saved to: {self.domain_analysis_yaml}")
        except Exception as e:
            logger.error(f"Error in embedding-based analysis: {e}")

        return nodes_df

    async def generate_embeddings(self) -> None:
        """
        Generate embeddings for functions using OpenAI's text-embedding-3-small model.

        This method reads the documented functions from the parquet file, creates a text
        representation for each function in the format:
        "[Explanation: {documentation}] [Function code: {function_code}] [path: {function_path}]"
        and generates embeddings for these texts using batched processing.
        """
        logger.info("Generating embeddings for functions")

        # Check if the documented functions parquet file exists
        if not os.path.exists(self.documented_output_path):
            logger.error(f"Documented functions parquet file not found: {self.documented_output_path}")
            return

        # Load the documented functions
        df = pd.read_parquet(self.documented_output_path)
        logger.info(f"Loaded {len(df)} documented functions")

        # Create text representations for each function
        texts = []
        for _, row in df.iterrows():
            documentation = row.get('description', '')
            function_code = row.get('function_code', '')
            function_path = f"{row.get('file_path', '')}:{row.get('name', '')}"

            text = f"[Explanation: {documentation}] [Function code: {function_code}] [path: {function_path}]"
            texts.append(text)

        logger.info(f"Created text representations for {len(texts)} functions")

        # Initialize the batched embedding client
        embedding_client = BatchedEmbedding(
            api_key=self.openai_api_key,
            model=self.embedding_model,
            batch_size=self.embedding_batch_size,
        )

        # Generate embeddings
        logger.info(f"Generating embeddings using model: {self.embedding_model}")
        embeddings = await embedding_client.embed_dataset(texts, show_progress=True)
        logger.info(f"Generated {len(embeddings)} embeddings")

        # Create a DataFrame with the embeddings
        embeddings_df = df.copy()
        embeddings_df['embedding'] = embeddings
        embeddings_df['text_representation'] = texts

        # Save the embeddings
        embeddings_df.to_parquet(self.embeddings_output_path)

        # Save metadata about the embeddings
        metadata = {
            'model': self.embedding_model,
            'embedding_dimension': len(embeddings[0]) if embeddings else 0,
            'num_functions': len(embeddings),
            'created_at': pd.Timestamp.now().isoformat(),
            'text_format': "[Explanation: {documentation}] [Function code: {function_code}] [path: {function_path}]"
        }

        with open(self.embeddings_metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)

        logger.info(f"Embeddings saved to: {self.embeddings_output_path}")
        logger.info(f"Embeddings metadata saved to: {self.embeddings_metadata_path}")

    async def analyze_domains_with_embeddings(self) -> None:
        """
        Analyze domains using function embeddings and semantic similarity graph.

        This method builds a graph where nodes are functions and edges are weighted by
        embedding similarity, then applies community detection algorithms to identify
        densely connected subgraphs as leaf domains. It then builds a hierarchy of
        domains by applying community detection recursively.
        """
        logger.info("Analyzing domains using function embeddings and semantic similarity graph")

        # Check if the embeddings file exists
        if not os.path.exists(self.embeddings_output_path):
            logger.error(f"Embeddings file not found: {self.embeddings_output_path}")
            return

        # Load the embeddings
        embeddings_df = pd.read_parquet(self.embeddings_output_path)
        logger.info(f"Loaded {len(embeddings_df)} function embeddings")

        # Initialize the semantic similarity graph
        graph = SemanticSimilarityGraph(
            similarity_threshold=self.similarity_threshold,
            min_community_size=self.min_community_size,
            max_community_size=self.max_community_size,
            resolution=self.resolution,
            hierarchical_levels=self.hierarchical_levels,
        )

        # Build the similarity graph
        logger.info("Building similarity graph")
        graph.build_similarity_graph(embeddings_df)

        # Save the graph for visualization
        graph.save_graph(self.similarity_graph_path)

        # Build hierarchical communities
        logger.info("Building hierarchical communities")
        graph.build_hierarchical_communities()

        # Name communities using LLM
        logger.info("Naming communities using LLM")
        await graph.name_communities(openai_api_key=self.openai_api_key, model=self.openai_model)

        # Save the domain hierarchy
        graph.save_hierarchy(self.domain_analysis_yaml)

        # Post-process the domain hierarchy to remove duplicates (unless skipped)
        if not hasattr(self, 'skip_post_processing') or not self.skip_post_processing:
            logger.info("Post-processing domain hierarchy to remove duplicates")
            self.post_process_domain_hierarchy()
            logger.info(f"Processed domain hierarchy saved to: {self.domain_analysis_processed_yaml}")

        # Save leaf domains as JSON
        logger.info("Saving leaf domains as JSON")
        graph.save_leaf_domains_json(self.domain_hierarchy_json)
        logger.info(f"Leaf domains saved to: {self.domain_hierarchy_json}")

        logger.info(f"Domain analysis complete. Results saved to: {self.domain_analysis_yaml}")

    def generate_knowledge_graph(self) -> Any:
        """
        Generate the Knowledge Graph using the hybrid knowledge graph approach.

        Returns:
            NetworkX graph representing the repository (nodes only, no edges)
        """
        logger.info(f"Analyzing repository structure: {self.repo_dir}")

        # Use the hybrid knowledge graph implementation
        logger.info("Using hybrid knowledge graph generation")
        nx_graph = generate_hybrid_knowledge_graph(self.repo_dir, verbose=self.verbose)

        # Normalize node attributes if needed
        for node_id, data in list(nx_graph.nodes(data=True)):
            # Ensure all expected fields are present
            if 'node_id' not in data:
                data['node_id'] = node_id

            # Add id field for compatibility
            if 'id' not in data:
                data['id'] = node_id

        logger.info(f"Knowledge Graph created with {len(nx_graph.nodes)} nodes")
        return nx_graph

    def save_graph_as_parquet(self, nx_graph, output_path):
        """
        Save the graph nodes as parquet file.

        Args:
            nx_graph: NetworkX graph
            output_path: Output path prefix

        Returns:
            DataFrame containing node information
        """
        # Convert graph to dataframe using the hybrid implementation
        nodes_df = hybrid_graph_to_dataframes(nx_graph)

        # Function to convert mixed types to strings
        def convert_mixed_types(df):
            for col in df.columns:
                if df[col].dtype == 'object':
                    df[col] = df[col].apply(
                        lambda x: json.dumps(x) if isinstance(x, (dict, list)) else str(x) if x is not None else None
                    )
            return df

        # Convert mixed types to strings for parquet compatibility
        nodes_df = convert_mixed_types(nodes_df)

        # Save nodes as parquet
        nodes_parquet_path = f"{output_path}_nodes.parquet"
        nodes_df.to_parquet(nodes_parquet_path, index=False)
        logger.info(f"Saved {len(nodes_df)} nodes to {nodes_parquet_path}")

        return nodes_df

    async def generate_documentation(self, nodes_df: pd.DataFrame) -> None:
        """
        Generate semantic documentation for functions.

        Args:
            nodes_df: DataFrame containing node information
        """
        from bracket_core.documenting import document_functions_with_significance

        # Save nodes as parquet for documentation
        nodes_parquet_path = f"{self.kg_output_path}_nodes.parquet"

        # Use the significance evaluation approach with call contexts
        await document_functions_with_significance(
            parquet_file=nodes_parquet_path,
            save_filepath=self.documented_output_path,
            max_requests_per_minute=self.llm_requests_per_minute,
            max_tokens_per_minute=self.llm_tokens_per_minute,
            selection_method="all",  # Process all functions
            save_all_rows=True,
        )

        logger.info(f"Function documentation complete. Results saved to: {self.documented_output_path}")

    def generate_significant_functions_yaml(self) -> None:
        """
        Generate YAML file for architecturally significant functions.
        """
        import pandas as pd
        import yaml

        try:
            # Load the documented functions
            df = pd.read_parquet(self.documented_output_path)

            # Filter for architecturally significant functions
            significant_df = df[df['architectural_significance'] >= 0.7]

            # Create a dictionary for YAML output
            significant_functions = {
                'significant_functions': []
            }

            # Add each significant function to the dictionary
            for _, row in significant_df.iterrows():
                function_info = {
                    'name': row['name'],
                    'file_path': row['file_path'],
                    'significance': float(row['architectural_significance']),
                    'description': row['description'] if 'description' in row else '',
                }
                significant_functions['significant_functions'].append(function_info)

            # Save as YAML
            with open(self.significant_functions_yaml, 'w') as f:
                yaml.dump(significant_functions, f, default_flow_style=False)

            logger.info(f"Generated YAML file for {len(significant_functions['significant_functions'])} architecturally significant functions")

        except Exception as e:
            logger.error(f"Error generating significant functions YAML: {e}")

    def post_process_domain_hierarchy(self) -> None:
        """
        Post-process the domain hierarchy YAML to remove duplicate functions and improve structure.
        """
        import yaml
        from collections import defaultdict

        logger.info(f"Post-processing domain hierarchy from {self.domain_analysis_yaml}")

        try:
            # Load the YAML file
            with open(self.domain_analysis_yaml, 'r') as f:
                hierarchy = yaml.safe_load(f)

            # Track all functions to detect duplicates
            function_to_domain = {}
            domain_functions = defaultdict(list)

            # Process all domains to identify unique functions for each
            def process_domain(domain, domain_path):
                domain_name = domain.get('name', 'Unnamed Domain')
                full_path = f"{domain_path} -> {domain_name}" if domain_path else domain_name

                # Process functions
                unique_functions = []
                for func in domain.get('functions', []):
                    # Check if this function has already been assigned to a domain
                    if func not in function_to_domain:
                        # First time seeing this function, assign it to current domain
                        function_to_domain[func] = full_path
                        unique_functions.append(func)
                    else:
                        # Function already assigned to another domain
                        existing_domain = function_to_domain[func]

                        # If current domain is more specific (child of existing domain),
                        # reassign function to current domain
                        if existing_domain in domain_path:
                            function_to_domain[func] = full_path
                            unique_functions.append(func)
                            # Remove from the parent domain's list
                            if existing_domain in domain_functions:
                                if func in domain_functions[existing_domain]:
                                    domain_functions[existing_domain].remove(func)

                domain_functions[full_path] = unique_functions

                # Process sub-areas recursively
                for sub_area in domain.get('sub_areas', []):
                    process_domain(sub_area, full_path)

            # Process all top-level domains
            for domain in hierarchy.get('areas', []):
                process_domain(domain, "")

            # Rebuild the hierarchy with unique functions
            def rebuild_domain(domain, domain_path):
                domain_name = domain.get('name', 'Unnamed Domain')
                full_path = f"{domain_path} -> {domain_name}" if domain_path else domain_name

                # Update functions with unique set
                domain['functions'] = domain_functions[full_path]

                # Process sub-areas recursively
                if 'sub_areas' in domain:
                    for sub_area in domain['sub_areas']:
                        rebuild_domain(sub_area, full_path)

            # Rebuild all top-level domains
            for domain in hierarchy.get('areas', []):
                rebuild_domain(domain, "")

            # Save the processed hierarchy
            with open(self.domain_analysis_processed_yaml, 'w') as f:
                yaml.dump(hierarchy, f, default_flow_style=False)

            logger.info(f"Post-processed domain hierarchy saved to: {self.domain_analysis_processed_yaml}")

            # Print statistics
            total_domains = len(domain_functions)
            total_functions = sum(len(funcs) for funcs in domain_functions.values())
            logger.info(f"Total domains: {total_domains}")
            logger.info(f"Total unique functions: {total_functions}")

        except Exception as e:
            logger.error(f"Error post-processing domain hierarchy: {e}")


async def main():
    """
    Main entry point for the experimental repository analysis flow.
    """
    import argparse
    import time

    start_time = time.time()

    parser = argparse.ArgumentParser(description="Experimental Repository Analysis Flow")
    parser.add_argument("--repo", required=True, help="Path to the repository to analyze")
    parser.add_argument("--output", required=True, help="Directory to save output artifacts")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")
    parser.add_argument("--skip-documentation", action="store_true", help="Skip function documentation")
    parser.add_argument("--requests-per-minute", type=float, default=1200, help="Rate limit for LLM API requests")
    parser.add_argument("--tokens-per-minute", type=float, default=1000000, help="Token rate limit for LLM API")
    parser.add_argument("--openai-api-key", type=str, default=None, help="OpenAI API key")
    parser.add_argument("--openai-model", type=str, default="gpt-4o-mini", help="OpenAI model to use")
    parser.add_argument("--embedding-model", type=str, default="text-embedding-3-small", help="Embedding model to use")
    parser.add_argument("--embedding-batch-size", type=int, default=500, help="Number of texts to embed in a single batch")
    parser.add_argument("--similarity-threshold", type=float, default=0.8, help="Minimum similarity score to create an edge (0-1)")
    parser.add_argument("--min-community-size", type=int, default=15, help="Minimum number of functions in a community")
    parser.add_argument("--max-community-size", type=int, default=100, help="Maximum number of functions in a community")
    parser.add_argument("--resolution", type=float, default=1.0, help="Resolution parameter for community detection (higher = more communities)")
    parser.add_argument("--hierarchical-levels", type=int, default=5, help="Number of hierarchical levels to build")
    parser.add_argument("--max-concurrent-tasks", type=int, default=5, help="Maximum number of concurrent tasks")
    parser.add_argument("--skip-post-processing", default=False,action="store_true", help="Skip post-processing of domain hierarchy")

    args = parser.parse_args()

    # Initialize the flow
    flow = ExperimentalRepoAnalysisFlow(
        repo_dir=args.repo,
        output_dir=args.output,
        verbose=args.verbose,
        document_functions=not args.skip_documentation,
        llm_requests_per_minute=args.requests_per_minute,
        llm_tokens_per_minute=args.tokens_per_minute,
        openai_api_key=args.openai_api_key,
        openai_model=args.openai_model,
        embedding_model=args.embedding_model,
        embedding_batch_size=args.embedding_batch_size,
        similarity_threshold=args.similarity_threshold,
        min_community_size=args.min_community_size,
        max_community_size=args.max_community_size,
        resolution=args.resolution,
        hierarchical_levels=args.hierarchical_levels,
        max_concurrent_tasks=args.max_concurrent_tasks,
        skip_post_processing=args.skip_post_processing,
    )

    await flow.run()

    end_time = time.time()
    print(f"Total time taken: {end_time - start_time}")


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())



# python -m bracket_core.exp_embedding.faster_irl --repo /Users/<USER>/work/startup/godzilla/adjacent/mem0/mem0 --output /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/exp_embeddings/mem0

# python -m bracket_core.exp_embedding.faster_irl --repo /Users/<USER>/work/startup/godzilla/django/django/ --output /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/exp_embeddings/django