"""
<PERSON><PERSON><PERSON> to run multiple flat clustering experiments with different parameter combinations.

This script allows you to experiment with different clustering parameters on existing
function embeddings without re-running the entire pipeline. It runs multiple experiments
with different parameter combinations and saves the results to separate directories.
"""

import os
import argparse
import asyncio
import itertools
import logging
from typing import List, Dict, Any, Optional
import pandas as pd
import json
import yaml
from tqdm import tqdm

from bracket_core.exp_embedding.flat_clustering import FlatClusterer

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def run_experiment(
    input_dir: str,
    output_dir: str,
    experiment_id: int,
    params: Dict[str, Any],
    openai_api_key: Optional[str] = None,
    openai_model: str = "gpt-4o-mini",
) -> Dict[str, Any]:
    """
    Run a single clustering experiment with the given parameters.

    Args:
        input_dir: Directory containing the input artifacts
        output_dir: Directory to save output artifacts
        experiment_id: ID of the experiment
        params: Dictionary of parameters for the experiment
        openai_api_key: OpenAI API key
        openai_model: OpenAI model to use for naming communities

    Returns:
        Dictionary with experiment results
    """
    # Create experiment directory
    experiment_dir = os.path.join(output_dir, f"experiment_{experiment_id}")
    os.makedirs(experiment_dir, exist_ok=True)

    # Initialize the clusterer with the given parameters
    clusterer = FlatClusterer(
        input_dir=input_dir,
        output_dir=experiment_dir,
        algorithm=params.get("algorithm", "louvain"),
        similarity_threshold=params.get("similarity_threshold", 0.7),
        min_community_size=params.get("min_community_size", 3),
        max_community_size=params.get("max_community_size", 100),
        resolution=params.get("resolution", 1.0),
        n_clusters=params.get("n_clusters", 20),
        eps=params.get("eps", 0.3),
        min_samples=params.get("min_samples", 5),
        allow_overlap=params.get("allow_overlap", False),
        overlap_threshold=params.get("overlap_threshold", 0.6),
        openai_api_key=openai_api_key,
        openai_model=openai_model,
    )

    # Run the clustering
    await clusterer.run()

    # Get the results
    domains_json_path = os.path.join(experiment_dir, f"flat_domains_{params.get('algorithm')}_st{params.get('similarity_threshold')}_ms{params.get('min_community_size')}.json")
    
    # Add algorithm-specific parameters to the path
    if params.get("algorithm") == "louvain":
        domains_json_path = domains_json_path.replace(".json", f"_r{params.get('resolution')}.json")
    elif params.get("algorithm") in ["spectral", "kmeans"]:
        domains_json_path = domains_json_path.replace(".json", f"_nc{params.get('n_clusters')}.json")
    elif params.get("algorithm") == "dbscan":
        domains_json_path = domains_json_path.replace(".json", f"_eps{params.get('eps')}_ms{params.get('min_samples')}.json")
    
    # Add overlap parameters if enabled
    if params.get("allow_overlap", False):
        domains_json_path = domains_json_path.replace(".json", f"_overlap{params.get('overlap_threshold')}.json")

    # Load the results
    try:
        with open(domains_json_path, 'r') as f:
            domains_data = json.load(f)
        
        # Count the number of domains and functions
        num_domains = len(domains_data.get("domains", []))
        
        # Count total functions (with potential duplicates if overlap is enabled)
        all_functions = []
        for domain in domains_data.get("domains", []):
            all_functions.extend(domain.get("functions", []))
        
        # Count unique functions
        unique_functions = len(set(all_functions))
        
        # Count functions in multiple domains
        function_counts = {}
        for domain in domains_data.get("domains", []):
            for function in domain.get("functions", []):
                function_counts[function] = function_counts.get(function, 0) + 1
        
        multi_domain_functions = sum(1 for count in function_counts.values() if count > 1)
        
        # Save experiment parameters and results
        result = {
            "experiment_id": experiment_id,
            "params": params,
            "num_domains": num_domains,
            "total_functions": len(all_functions),
            "unique_functions": unique_functions,
            "multi_domain_functions": multi_domain_functions,
            "domains_json_path": domains_json_path,
        }
        
        # Save experiment results
        with open(os.path.join(experiment_dir, "experiment_results.json"), 'w') as f:
            json.dump(result, f, indent=2)
        
        return result
    
    except Exception as e:
        logger.error(f"Error loading results for experiment {experiment_id}: {e}")
        return {
            "experiment_id": experiment_id,
            "params": params,
            "error": str(e),
        }


async def run_experiments(
    input_dir: str,
    output_dir: str,
    algorithms: List[str],
    similarity_thresholds: List[float],
    min_community_sizes: List[int],
    max_community_sizes: List[int],
    resolutions: List[float],
    n_clusters_list: List[int],
    eps_list: List[float],
    min_samples_list: List[int],
    allow_overlap_list: List[bool],
    overlap_thresholds: List[float],
    openai_api_key: Optional[str] = None,
    openai_model: str = "gpt-4o-mini",
    max_concurrent: int = 3,
) -> List[Dict[str, Any]]:
    """
    Run multiple clustering experiments with different parameter combinations.

    Args:
        input_dir: Directory containing the input artifacts
        output_dir: Directory to save output artifacts
        algorithms: List of clustering algorithms to try
        similarity_thresholds: List of similarity thresholds to try
        min_community_sizes: List of minimum community sizes to try
        max_community_sizes: List of maximum community sizes to try
        resolutions: List of resolutions to try (for Louvain)
        n_clusters_list: List of n_clusters values to try (for spectral and kmeans)
        eps_list: List of eps values to try (for DBSCAN)
        min_samples_list: List of min_samples values to try (for DBSCAN)
        allow_overlap_list: List of allow_overlap values to try
        overlap_thresholds: List of overlap thresholds to try
        openai_api_key: OpenAI API key
        openai_model: OpenAI model to use for naming communities
        max_concurrent: Maximum number of concurrent experiments

    Returns:
        List of dictionaries with experiment results
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Generate parameter combinations
    experiment_params = []
    
    for algorithm in algorithms:
        # Base parameters for all algorithms
        base_params = {
            "algorithm": algorithm,
            "similarity_threshold": similarity_thresholds[0],
            "min_community_size": min_community_sizes[0],
            "max_community_size": max_community_sizes[0],
            "allow_overlap": allow_overlap_list[0],
            "overlap_threshold": overlap_thresholds[0],
        }
        
        # Algorithm-specific parameters
        if algorithm == "louvain":
            for st, ms, r, ao, ot in itertools.product(
                similarity_thresholds, min_community_sizes, resolutions, allow_overlap_list, overlap_thresholds
            ):
                params = base_params.copy()
                params.update({
                    "similarity_threshold": st,
                    "min_community_size": ms,
                    "resolution": r,
                    "allow_overlap": ao,
                    "overlap_threshold": ot if ao else 0.0,
                })
                experiment_params.append(params)
        
        elif algorithm in ["spectral", "kmeans"]:
            for st, ms, nc, ao, ot in itertools.product(
                similarity_thresholds, min_community_sizes, n_clusters_list, allow_overlap_list, overlap_thresholds
            ):
                params = base_params.copy()
                params.update({
                    "similarity_threshold": st,
                    "min_community_size": ms,
                    "n_clusters": nc,
                    "allow_overlap": ao,
                    "overlap_threshold": ot if ao else 0.0,
                })
                experiment_params.append(params)
        
        elif algorithm == "dbscan":
            for st, ms, eps, min_s, ao, ot in itertools.product(
                similarity_thresholds, min_community_sizes, eps_list, min_samples_list, allow_overlap_list, overlap_thresholds
            ):
                params = base_params.copy()
                params.update({
                    "similarity_threshold": st,
                    "min_community_size": ms,
                    "eps": eps,
                    "min_samples": min_s,
                    "allow_overlap": ao,
                    "overlap_threshold": ot if ao else 0.0,
                })
                experiment_params.append(params)
    
    # Run experiments with semaphore to limit concurrency
    semaphore = asyncio.Semaphore(max_concurrent)
    
    async def run_with_semaphore(experiment_id, params):
        async with semaphore:
            return await run_experiment(
                input_dir=input_dir,
                output_dir=output_dir,
                experiment_id=experiment_id,
                params=params,
                openai_api_key=openai_api_key,
                openai_model=openai_model,
            )
    
    # Run experiments
    logger.info(f"Running {len(experiment_params)} experiments with max concurrency {max_concurrent}")
    tasks = [run_with_semaphore(i, params) for i, params in enumerate(experiment_params)]
    results = await asyncio.gather(*tasks)
    
    # Save all results to a CSV file
    results_df = pd.DataFrame(results)
    results_df.to_csv(os.path.join(output_dir, "experiment_results.csv"), index=False)
    
    # Save all results to a JSON file
    with open(os.path.join(output_dir, "experiment_results.json"), 'w') as f:
        json.dump(results, f, indent=2)
    
    return results


async def main():
    """
    Main entry point for the script.
    """
    parser = argparse.ArgumentParser(description="Run multiple flat clustering experiments")
    parser.add_argument("--input", required=True, help="Directory containing the input artifacts")
    parser.add_argument("--output", required=True, help="Directory to save output artifacts")
    parser.add_argument("--algorithms", nargs="+", default=["louvain"], 
                        choices=["louvain", "spectral", "dbscan", "kmeans"],
                        help="Clustering algorithms to try")
    parser.add_argument("--similarity-thresholds", nargs="+", type=float, default=[0.7, 0.8],
                        help="Similarity thresholds to try")
    parser.add_argument("--min-community-sizes", nargs="+", type=int, default=[3, 5, 10],
                        help="Minimum community sizes to try")
    parser.add_argument("--max-community-sizes", nargs="+", type=int, default=[100],
                        help="Maximum community sizes to try")
    parser.add_argument("--resolutions", nargs="+", type=float, default=[0.8, 1.0, 1.2],
                        help="Resolutions to try (for Louvain)")
    parser.add_argument("--n-clusters", nargs="+", type=int, default=[20, 30, 40],
                        help="n_clusters values to try (for spectral and kmeans)")
    parser.add_argument("--eps", nargs="+", type=float, default=[0.2, 0.3, 0.4],
                        help="eps values to try (for DBSCAN)")
    parser.add_argument("--min-samples", nargs="+", type=int, default=[3, 5, 10],
                        help="min_samples values to try (for DBSCAN)")
    parser.add_argument("--allow-overlap", nargs="+", type=bool, default=[False, True],
                        help="allow_overlap values to try")
    parser.add_argument("--overlap-thresholds", nargs="+", type=float, default=[0.6, 0.7],
                        help="Overlap thresholds to try")
    parser.add_argument("--openai-api-key", type=str, default=None,
                        help="OpenAI API key")
    parser.add_argument("--openai-model", type=str, default="gpt-4o-mini",
                        help="OpenAI model to use for naming communities")
    parser.add_argument("--max-concurrent", type=int, default=3,
                        help="Maximum number of concurrent experiments")

    args = parser.parse_args()

    # Run experiments
    results = await run_experiments(
        input_dir=args.input,
        output_dir=args.output,
        algorithms=args.algorithms,
        similarity_thresholds=args.similarity_thresholds,
        min_community_sizes=args.min_community_sizes,
        max_community_sizes=args.max_community_sizes,
        resolutions=args.resolutions,
        n_clusters_list=args.n_clusters,
        eps_list=args.eps,
        min_samples_list=args.min_samples,
        allow_overlap_list=args.allow_overlap,
        overlap_thresholds=args.overlap_thresholds,
        openai_api_key=args.openai_api_key,
        openai_model=args.openai_model,
        max_concurrent=args.max_concurrent,
    )

    # Print summary of results
    logger.info(f"Completed {len(results)} experiments")
    
    # Sort experiments by number of domains
    sorted_results = sorted(results, key=lambda x: x.get("num_domains", 0), reverse=True)
    
    # Print top 5 experiments by number of domains
    logger.info("Top 5 experiments by number of domains:")
    for i, result in enumerate(sorted_results[:5], 1):
        logger.info(f"{i}. Experiment {result['experiment_id']}: {result.get('num_domains', 0)} domains, "
                   f"{result.get('unique_functions', 0)} unique functions, "
                   f"{result.get('multi_domain_functions', 0)} functions in multiple domains")
        logger.info(f"   Parameters: {result['params']}")


if __name__ == "__main__":
    asyncio.run(main())
