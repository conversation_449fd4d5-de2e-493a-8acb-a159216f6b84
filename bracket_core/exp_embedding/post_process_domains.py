"""
Post-process domain analysis YAML files to remove duplicate functions and improve structure.
"""

import os
import yaml
import logging
from collections import defaultdict
from typing import Dict, List, Set, Any, Optional

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def post_process_domain_hierarchy(input_yaml_path: str, output_yaml_path: str) -> None:
    """
    Post-process the domain hierarchy YAML to remove duplicate functions and improve structure.
    
    Args:
        input_yaml_path: Path to the input YAML file
        output_yaml_path: Path to save the processed YAML file
    """
    logger.info(f"Post-processing domain hierarchy from {input_yaml_path}")
    
    # Load the YAML file
    with open(input_yaml_path, 'r') as f:
        hierarchy = yaml.safe_load(f)
    
    # Track all functions to detect duplicates
    function_to_domain = {}
    domain_functions = defaultdict(list)
    
    # Process all domains to identify unique functions for each
    def process_domain(domain: Dict[str, Any], domain_path: str) -> None:
        domain_name = domain.get('name', 'Unnamed Domain')
        full_path = f"{domain_path} -> {domain_name}" if domain_path else domain_name
        
        # Process functions
        unique_functions = []
        for func in domain.get('functions', []):
            # Check if this function has already been assigned to a domain
            if func not in function_to_domain:
                # First time seeing this function, assign it to current domain
                function_to_domain[func] = full_path
                unique_functions.append(func)
            else:
                # Function already assigned to another domain
                existing_domain = function_to_domain[func]
                
                # If current domain is more specific (child of existing domain),
                # reassign function to current domain
                if existing_domain in domain_path:
                    function_to_domain[func] = full_path
                    unique_functions.append(func)
                    # Remove from the parent domain's list
                    if existing_domain in domain_functions:
                        if func in domain_functions[existing_domain]:
                            domain_functions[existing_domain].remove(func)
        
        domain_functions[full_path] = unique_functions
        
        # Process sub-areas recursively
        for sub_area in domain.get('sub_areas', []):
            process_domain(sub_area, full_path)
    
    # Process all top-level domains
    for domain in hierarchy.get('areas', []):
        process_domain(domain, "")
    
    # Rebuild the hierarchy with unique functions
    def rebuild_domain(domain: Dict[str, Any], domain_path: str) -> None:
        domain_name = domain.get('name', 'Unnamed Domain')
        full_path = f"{domain_path} -> {domain_name}" if domain_path else domain_name
        
        # Update functions with unique set
        domain['functions'] = domain_functions[full_path]
        
        # Process sub-areas recursively
        if 'sub_areas' in domain:
            for sub_area in domain['sub_areas']:
                rebuild_domain(sub_area, full_path)
    
    # Rebuild all top-level domains
    for domain in hierarchy.get('areas', []):
        rebuild_domain(domain, "")
    
    # Save the processed hierarchy
    with open(output_yaml_path, 'w') as f:
        yaml.dump(hierarchy, f, default_flow_style=False)
    
    logger.info(f"Post-processed domain hierarchy saved to: {output_yaml_path}")
    
    # Print statistics
    total_domains = len(domain_functions)
    total_functions = sum(len(funcs) for funcs in domain_functions.values())
    logger.info(f"Total domains: {total_domains}")
    logger.info(f"Total unique functions: {total_functions}")

def fix_yaml_structure(input_yaml_path: str, output_yaml_path: str) -> None:
    """
    Fix the structure of the YAML file to properly represent the hierarchy.
    
    Args:
        input_yaml_path: Path to the input YAML file
        output_yaml_path: Path to save the processed YAML file
    """
    logger.info(f"Fixing YAML structure for {input_yaml_path}")
    
    # Load the YAML file
    with open(input_yaml_path, 'r') as f:
        data = yaml.safe_load(f)
    
    # Check if the YAML has the expected structure
    if 'areas' not in data:
        logger.error("YAML file does not have 'areas' key")
        return
    
    # Fix YAML anchors and aliases that might be causing issues
    # Convert to JSON and back to remove YAML-specific features
    import json
    data_json = json.dumps(data)
    data = json.loads(data_json)
    
    # Save the fixed YAML
    with open(output_yaml_path, 'w') as f:
        yaml.dump(data, f, default_flow_style=False)
    
    logger.info(f"Fixed YAML structure saved to: {output_yaml_path}")

def main():
    """Main entry point for post-processing domain analysis YAML files."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Post-process domain analysis YAML files")
    parser.add_argument("--input", required=True, help="Path to the input YAML file")
    parser.add_argument("--output", required=True, help="Path to save the processed YAML file")
    parser.add_argument("--fix-structure", action="store_true", help="Fix YAML structure issues")
    
    args = parser.parse_args()
    
    if args.fix_structure:
        fix_yaml_structure(args.input, args.output)
    else:
        post_process_domain_hierarchy(args.input, args.output)

if __name__ == "__main__":
    main()
