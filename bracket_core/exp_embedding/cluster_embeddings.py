"""
Standalone script for clustering function embeddings and generating domain hierarchies.

This script allows you to experiment with different clustering parameters on existing
function embeddings without re-running the entire pipeline.
"""

import os
import json
import yaml
import logging
import argparse
import asyncio
import pandas as pd
import networkx as nx
import numpy as np
from typing import Dict, List, Any, Optional, Set
from collections import defaultdict
from tqdm import tqdm
from sklearn.metrics.pairwise import cosine_similarity
from community import community_louvain

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import necessary classes from faster_irl.py
from bracket_core.exp_embedding.faster_irl import Community, DomainHierarchy, SemanticSimilarityGraph
from bracket_core.llm.oai.chat_openai import ChatOpenAI


class EmbeddingClusterer:
    """
    Standalone class for clustering function embeddings and generating domain hierarchies.
    """

    def __init__(
        self,
        input_dir: str,
        output_dir: str = None,
        # Clustering parameters
        similarity_threshold: float = 0.7,
        min_community_size: int = 3,
        max_community_size: int = 100,
        resolution: float = 1.0,
        hierarchical_levels: int = 3,
        # OpenAI parameters
        openai_api_key: Optional[str] = None,
        openai_model: str = "gpt-4o-mini",
        # Post-processing parameters
        skip_post_processing: bool = False,
    ):
        """
        Initialize the embedding clusterer.

        Args:
            input_dir: Directory containing the input artifacts (embeddings.parquet)
            output_dir: Directory to save output artifacts. If None, uses input_dir.
            similarity_threshold: Minimum similarity score to create an edge (0-1)
            min_community_size: Minimum number of functions in a community
            max_community_size: Maximum number of functions in a community
            resolution: Resolution parameter for community detection (higher = more communities)
            hierarchical_levels: Number of hierarchical levels to build
            openai_api_key: OpenAI API key. If None, will try to get from environment.
            openai_model: OpenAI model to use for naming communities
            skip_post_processing: Whether to skip post-processing of domain hierarchy
        """
        self.input_dir = input_dir
        self.output_dir = output_dir or input_dir

        # Clustering parameters
        self.similarity_threshold = similarity_threshold
        self.min_community_size = min_community_size
        self.max_community_size = max_community_size
        self.resolution = resolution
        self.hierarchical_levels = hierarchical_levels

        # OpenAI parameters
        self.openai_api_key = openai_api_key
        self.openai_model = openai_model

        # Post-processing parameters
        self.skip_post_processing = skip_post_processing

        # Ensure output directory exists
        os.makedirs(self.output_dir, exist_ok=True)

        # Paths for input and output artifacts
        self.embeddings_path = os.path.join(input_dir, "function_embeddings.parquet")

        # Generate a unique experiment ID based on parameters
        self.experiment_id = f"st{similarity_threshold}_ms{min_community_size}_r{resolution}_hl{hierarchical_levels}"

        # Create experiment-specific output paths
        self.domain_analysis_yaml = os.path.join(self.output_dir, f"domain_analysis_{self.experiment_id}.yaml")
        self.domain_analysis_processed_yaml = os.path.join(self.output_dir, f"domain_analysis_processed_{self.experiment_id}.yaml")
        self.domain_hierarchy_json = os.path.join(self.output_dir, f"domain_hierarchy_{self.experiment_id}.json")
        self.similarity_graph_path = os.path.join(self.output_dir, f"similarity_graph_{self.experiment_id}.json")

    async def run(self) -> None:
        """
        Run the embedding clustering pipeline.
        """
        logger.info(f"Starting embedding clustering with experiment ID: {self.experiment_id}")
        logger.info(f"Parameters: similarity_threshold={self.similarity_threshold}, min_community_size={self.min_community_size}, "
                   f"max_community_size={self.max_community_size}, resolution={self.resolution}, "
                   f"hierarchical_levels={self.hierarchical_levels}")

        # Check if the embeddings file exists
        if not os.path.exists(self.embeddings_path):
            logger.error(f"Embeddings file not found: {self.embeddings_path}")
            return

        # Load the embeddings
        embeddings_df = pd.read_parquet(self.embeddings_path)
        logger.info(f"Loaded {len(embeddings_df)} function embeddings")

        # Initialize the semantic similarity graph
        graph = SemanticSimilarityGraph(
            similarity_threshold=self.similarity_threshold,
            min_community_size=self.min_community_size,
            max_community_size=self.max_community_size,
            resolution=self.resolution,
            hierarchical_levels=self.hierarchical_levels,
        )

        # Build the similarity graph
        logger.info("Building similarity graph")
        graph.build_similarity_graph(embeddings_df)

        # Save the graph for visualization in JSON format
        graph.save_graph(self.similarity_graph_path, format="json")
        logger.info(f"Saved similarity graph to: {self.similarity_graph_path}")

        # Build hierarchical communities
        logger.info("Building hierarchical communities")
        graph.build_hierarchical_communities()

        # Name communities using LLM
        logger.info("Naming communities using LLM")
        await graph.name_communities(openai_api_key=self.openai_api_key, model=self.openai_model)

        # Save the domain hierarchy
        graph.save_hierarchy(self.domain_analysis_yaml)
        logger.info(f"Saved domain hierarchy to: {self.domain_analysis_yaml}")

        # Post-process the domain hierarchy to remove duplicates (unless skipped)
        if not self.skip_post_processing:
            logger.info("Post-processing domain hierarchy to remove duplicates")
            self.post_process_domain_hierarchy()
            logger.info(f"Saved processed domain hierarchy to: {self.domain_analysis_processed_yaml}")

        # Save leaf domains as JSON
        logger.info("Saving leaf domains as JSON")
        graph.save_leaf_domains_json(self.domain_hierarchy_json)
        logger.info(f"Saved leaf domains to: {self.domain_hierarchy_json}")

        logger.info(f"Embedding clustering complete for experiment ID: {self.experiment_id}")

    def post_process_domain_hierarchy(self) -> None:
        """
        Post-process the domain hierarchy YAML to remove duplicate functions and improve structure.
        """
        import yaml
        from collections import defaultdict

        logger.info(f"Post-processing domain hierarchy from {self.domain_analysis_yaml}")

        try:
            # Load the YAML file
            with open(self.domain_analysis_yaml, 'r') as f:
                hierarchy = yaml.safe_load(f)

            # Track all functions to detect duplicates
            function_to_domain = {}
            domain_functions = defaultdict(list)

            # Process all domains to identify unique functions for each
            def process_domain(domain, domain_path):
                domain_name = domain.get('name', 'Unnamed Domain')
                full_path = f"{domain_path} -> {domain_name}" if domain_path else domain_name

                # Process functions
                unique_functions = []
                for func in domain.get('functions', []):
                    # Check if this function has already been assigned to a domain
                    if func not in function_to_domain:
                        # First time seeing this function, assign it to current domain
                        function_to_domain[func] = full_path
                        unique_functions.append(func)
                    else:
                        # Function already assigned to another domain
                        existing_domain = function_to_domain[func]

                        # If current domain is more specific (child of existing domain),
                        # reassign function to current domain
                        if existing_domain in domain_path:
                            function_to_domain[func] = full_path
                            unique_functions.append(func)
                            # Remove from the parent domain's list
                            if existing_domain in domain_functions:
                                if func in domain_functions[existing_domain]:
                                    domain_functions[existing_domain].remove(func)

                domain_functions[full_path] = unique_functions

                # Process sub-areas recursively
                for sub_area in domain.get('sub_areas', []):
                    process_domain(sub_area, full_path)

            # Process all top-level domains
            for domain in hierarchy.get('areas', []):
                process_domain(domain, "")

            # Rebuild the hierarchy with unique functions
            def rebuild_domain(domain, domain_path):
                domain_name = domain.get('name', 'Unnamed Domain')
                full_path = f"{domain_path} -> {domain_name}" if domain_path else domain_name

                # Update functions with unique set
                domain['functions'] = domain_functions[full_path]

                # Process sub-areas recursively
                if 'sub_areas' in domain:
                    for sub_area in domain['sub_areas']:
                        rebuild_domain(sub_area, full_path)

            # Rebuild all top-level domains
            for domain in hierarchy.get('areas', []):
                rebuild_domain(domain, "")

            # Save the processed hierarchy
            with open(self.domain_analysis_processed_yaml, 'w') as f:
                yaml.dump(hierarchy, f, default_flow_style=False)

            # Print statistics
            total_domains = len(domain_functions)
            total_functions = sum(len(funcs) for funcs in domain_functions.values())
            logger.info(f"Total domains: {total_domains}")
            logger.info(f"Total unique functions: {total_functions}")

        except Exception as e:
            logger.error(f"Error post-processing domain hierarchy: {e}")


async def main():
    """
    Main entry point for the embedding clustering script.
    """
    parser = argparse.ArgumentParser(description="Cluster function embeddings and generate domain hierarchies")
    parser.add_argument("--input", required=True, help="Directory containing the input artifacts (function_embeddings.parquet)")
    parser.add_argument("--output", help="Directory to save output artifacts. If not provided, uses input directory.")
    parser.add_argument("--similarity-threshold", type=float, default=0.7, help="Minimum similarity score to create an edge (0-1)")
    parser.add_argument("--min-community-size", type=int, default=3, help="Minimum number of functions in a community")
    parser.add_argument("--max-community-size", type=int, default=100, help="Maximum number of functions in a community")
    parser.add_argument("--resolution", type=float, default=1.0, help="Resolution parameter for community detection (higher = more communities)")
    parser.add_argument("--hierarchical-levels", type=int, default=3, help="Number of hierarchical levels to build")
    parser.add_argument("--openai-api-key", type=str, default=None, help="OpenAI API key")
    parser.add_argument("--openai-model", type=str, default="gpt-4o-mini", help="OpenAI model to use for naming communities")
    parser.add_argument("--skip-post-processing", action="store_true", help="Skip post-processing of domain hierarchy")

    args = parser.parse_args()

    # Initialize the clusterer
    clusterer = EmbeddingClusterer(
        input_dir=args.input,
        output_dir=args.output,
        similarity_threshold=args.similarity_threshold,
        min_community_size=args.min_community_size,
        max_community_size=args.max_community_size,
        resolution=args.resolution,
        hierarchical_levels=args.hierarchical_levels,
        openai_api_key=args.openai_api_key,
        openai_model=args.openai_model,
        skip_post_processing=args.skip_post_processing,
    )

    # Run the clustering
    await clusterer.run()


if __name__ == "__main__":
    asyncio.run(main())
