# Flat Clustering for Domain Identification

This document explains how to use the flat clustering approach to identify non-hierarchical leaf domains in your codebase.

## Overview

The `flat_clustering.py` script provides a simpler alternative to hierarchical clustering when your goal is to identify good leaf domains without building a complex hierarchy. It offers several key advantages:

1. **Comprehensive Coverage**: Ensures all functions are included in at least one domain
2. **Optional Overlapping Domains**: Allows functions to belong to multiple domains if they're on the boundary
3. **Multiple Algorithms**: Supports various clustering approaches (Louva<PERSON>, Spectral, DBSCAN, K-means)
4. **Simplified Output**: Produces flat domain structures without complex hierarchies

This approach is ideal when you want to:
- Focus on identifying cohesive leaf domains without hierarchical relationships
- Ensure comprehensive coverage of all functions in your codebase
- Allow functions to appear in multiple domains when appropriate

## Usage

To run flat clustering on your function embeddings:

```bash
python -m bracket_core.exp_embedding.flat_clustering \
  --input /path/to/embeddings/directory \
  --output /path/to/output/directory \
  --algorithm louvain \
  --similarity-threshold 0.7 \
  --min-community-size 5 \
  --allow-overlap \
  --overlap-threshold 0.6
```

### Available Algorithms

The script supports four clustering algorithms:

1. **Louvain** (default): Community detection algorithm that works well for identifying densely connected groups
   ```bash
   --algorithm louvain --resolution 1.0
   ```

2. **Spectral Clustering**: Works well for finding clusters with complex shapes
   ```bash
   --algorithm spectral --n-clusters 20
   ```

3. **DBSCAN**: Density-based clustering that can find clusters of arbitrary shapes
   ```bash
   --algorithm dbscan --eps 0.3 --min-samples 5
   ```

4. **K-means**: Simple clustering algorithm that works well for spherical clusters
   ```bash
   --algorithm kmeans --n-clusters 20
   ```

### Allowing Overlapping Domains

To allow functions to belong to multiple domains:

```bash
--allow-overlap --overlap-threshold 0.6
```

The `overlap-threshold` parameter (0-1) controls how similar a function must be to a domain to be included in it. Lower values create more overlap.

## Output Files

The script produces three main output files:

1. **flat_domains_{params}.json**: JSON file containing the identified domains and their functions
2. **flat_domains_{params}.yaml**: YAML file in the same format as the domain analysis output
3. **similarity_graph_{params}.json**: JSON representation of the function similarity graph

## Parameter Tuning

To find the best parameters for your codebase:

1. **Similarity Threshold**: Controls edge creation in the similarity graph
   - Higher values (0.8-0.9): More selective, creates fewer edges and more focused domains
   - Lower values (0.6-0.7): More inclusive, creates more edges and potentially larger domains

2. **Min Community Size**: Minimum number of functions in a domain
   - Smaller values (3-5): Allows for more specialized micro-domains
   - Larger values (10+): Forces domains to be more substantial

3. **Algorithm-Specific Parameters**:
   - For Louvain: Adjust `resolution` (higher = more communities)
   - For Spectral/K-means: Adjust `n-clusters` based on expected number of domains
   - For DBSCAN: Tune `eps` and `min-samples` based on function density

4. **Overlap Parameters** (if using `--allow-overlap`):
   - Higher threshold (0.7-0.8): More selective overlap, functions must be very similar
   - Lower threshold (0.5-0.6): More inclusive overlap, more functions in multiple domains

## Comparison with Hierarchical Approach

The flat clustering approach differs from the hierarchical approach in `standalone_clustering.py` in several ways:

| Feature | Flat Clustering | Hierarchical Clustering |
|---------|----------------|------------------------|
| Structure | Single level of domains | Multiple levels of domains |
| Function Assignment | Can belong to multiple domains | Each function in exactly one domain |
| Algorithm Options | Louvain, Spectral, DBSCAN, K-means | Louvain only |
| Complexity | Simpler, more focused | More complex, more comprehensive |
| Use Case | When you need good leaf domains | When you need hierarchical relationships |

## Example Workflow

A typical workflow might look like:

1. Generate function embeddings using the full pipeline:
   ```bash
   python -m bracket_core.exp_embedding.faster_irl \
     --repo /path/to/repo \
     --output /path/to/artifacts
   ```

2. Try flat clustering with default parameters:
   ```bash
   python -m bracket_core.exp_embedding.flat_clustering \
     --input /path/to/artifacts \
     --output /path/to/experiments/flat1
   ```

3. Experiment with allowing overlap:
   ```bash
   python -m bracket_core.exp_embedding.flat_clustering \
     --input /path/to/artifacts \
     --output /path/to/experiments/flat2 \
     --allow-overlap \
     --overlap-threshold 0.6
   ```

4. Try different algorithms:
   ```bash
   python -m bracket_core.exp_embedding.flat_clustering \
     --input /path/to/artifacts \
     --output /path/to/experiments/flat3 \
     --algorithm spectral \
     --n-clusters 30
   ```
