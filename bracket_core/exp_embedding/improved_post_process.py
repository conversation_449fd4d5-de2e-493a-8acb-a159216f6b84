"""
Improved post-processing for domain analysis YAML files to handle duplicate domain names.
"""

import os
import yaml
import logging
from collections import defaultdict
from typing import Dict, List, Set, Any, Optional

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def merge_domains_by_name(input_yaml_path: str, output_yaml_path: str) -> None:
    """
    Merge domains with the same name and ensure proper hierarchical structure.
    
    Args:
        input_yaml_path: Path to the input YAML file
        output_yaml_path: Path to save the processed YAML file
    """
    logger.info(f"Merging domains by name from {input_yaml_path}")
    
    # Load the YAML file
    with open(input_yaml_path, 'r') as f:
        hierarchy = yaml.safe_load(f)
    
    # Collect all domains by name
    domains_by_name = {}
    
    # Function to recursively collect domains
    def collect_domains(domain_list, parent_path=None):
        for domain in domain_list:
            name = domain.get('name', 'Unnamed Domain')
            full_path = f"{parent_path} -> {name}" if parent_path else name
            
            # Create entry if it doesn't exist
            if name not in domains_by_name:
                domains_by_name[name] = {
                    'name': name,
                    'description': domain.get('description', ''),
                    'functions': set(),
                    'paths': set(),
                    'children': set()
                }
            
            # Add functions
            functions = domain.get('functions', [])
            if isinstance(functions, list):
                domains_by_name[name]['functions'].update(functions)
            
            # Add path
            domains_by_name[name]['paths'].add(full_path)
            
            # Process sub-areas recursively
            if 'sub_areas' in domain:
                # Add children
                for child in domain['sub_areas']:
                    child_name = child.get('name', 'Unnamed Domain')
                    domains_by_name[name]['children'].add(child_name)
                
                collect_domains(domain['sub_areas'], full_path)
    
    # Collect all domains
    if 'areas' in hierarchy:
        collect_domains(hierarchy['areas'])
    
    # Build domain hierarchy
    domain_hierarchy = {}
    
    # Find root domains (those that are not children of any other domain)
    all_children = set()
    for domain_info in domains_by_name.values():
        all_children.update(domain_info['children'])
    
    root_domains = []
    for name, domain_info in domains_by_name.items():
        if name not in all_children:
            root_domains.append(name)
    
    # Build hierarchy recursively
    def build_hierarchy(domain_name, visited=None):
        if visited is None:
            visited = set()
        
        if domain_name in visited:
            logger.warning(f"Circular reference detected for domain: {domain_name}")
            return None
        
        visited.add(domain_name)
        
        domain_info = domains_by_name[domain_name]
        
        domain_dict = {
            'name': domain_name,
            'description': domain_info['description'],
            'functions': sorted(list(domain_info['functions']))
        }
        
        # Add children
        children = []
        for child_name in domain_info['children']:
            if child_name in domains_by_name:
                child_dict = build_hierarchy(child_name, visited.copy())
                if child_dict:
                    children.append(child_dict)
        
        if children:
            domain_dict['sub_areas'] = children
        
        return domain_dict
    
    # Build the final hierarchy
    result = {'areas': []}
    for root_domain in root_domains:
        domain_dict = build_hierarchy(root_domain)
        if domain_dict:
            result['areas'].append(domain_dict)
    
    # Save the processed hierarchy
    with open(output_yaml_path, 'w') as f:
        yaml.dump(result, f, default_flow_style=False)
    
    logger.info(f"Merged domains by name saved to: {output_yaml_path}")
    logger.info(f"Total unique domains: {len(domains_by_name)}")
    
    # Count total functions
    total_functions = 0
    for domain_info in domains_by_name.values():
        total_functions += len(domain_info['functions'])
    
    logger.info(f"Total functions: {total_functions}")

def main():
    """Main entry point for improved post-processing."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Improved post-processing for domain analysis YAML files")
    parser.add_argument("--input", required=True, help="Path to the input YAML file")
    parser.add_argument("--output", required=True, help="Path to save the processed YAML file")
    
    args = parser.parse_args()
    
    merge_domains_by_name(args.input, args.output)

if __name__ == "__main__":
    main()
