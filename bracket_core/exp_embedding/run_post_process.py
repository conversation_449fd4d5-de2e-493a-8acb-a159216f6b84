"""
Run post-processing on an existing domain_analysis.yaml file.
"""

import os
import sys
import logging
from bracket_core.exp_embedding.post_process_domains import post_process_domain_hierarchy, fix_yaml_structure

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """Main entry point for running post-processing on domain analysis YAML files."""
    if len(sys.argv) < 2:
        print("Usage: python run_post_process.py <path_to_domain_analysis.yaml>")
        sys.exit(1)

    input_path = sys.argv[1]

    if not os.path.exists(input_path):
        logger.error(f"Input file not found: {input_path}")
        sys.exit(1)

    # Create output path with _processed suffix
    output_path = input_path.replace('.yaml', '_processed.yaml')

    # First fix any YAML structure issues
    temp_path = input_path.replace('.yaml', '_fixed.yaml')
    fix_yaml_structure(input_path, temp_path)

    # Then post-process to remove duplicates
    post_process_domain_hierarchy(temp_path, output_path)

    # Clean up temporary file
    if os.path.exists(temp_path):
        os.remove(temp_path)

    logger.info(f"Post-processing complete. Results saved to: {output_path}")

if __name__ == "__main__":
    main()
