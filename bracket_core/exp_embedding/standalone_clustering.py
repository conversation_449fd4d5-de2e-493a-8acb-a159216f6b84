"""
Standalone script for clustering function embeddings and generating leaf domains.

This script allows you to experiment with different clustering parameters on existing
function embeddings without re-running the entire pipeline. It focuses specifically
on producing a JSON output with leaf domains.
"""

import os
import json
import yaml
import logging
import argparse
import asyncio
import pandas as pd
import networkx as nx
import numpy as np
from typing import Dict, List, Any, Optional, Set
from collections import defaultdict
from tqdm import tqdm

# Import necessary classes from faster_irl.py
from bracket_core.exp_embedding.faster_irl import SemanticSimilarityGraph
from bracket_core.llm.api_keys import get_openai_api_key, get_anthropic_api_key

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class StandaloneClusterer:
    """
    Standalone class for clustering function embeddings and generating leaf domains.
    """

    def __init__(
        self,
        input_dir: str,
        output_dir: Optional[str] = None,
        # Clustering parameters
        similarity_threshold: float = 0.7,
        min_community_size: int = 3,
        max_community_size: int = 100,
        resolution: float = 1.0,
        hierarchical_levels: int = 3,
        # OpenAI parameters
        openai_api_key: Optional[str] = None,
        openai_model: str = "gpt-4o-mini",
    ):
        """
        Initialize the standalone clusterer.

        Args:
            input_dir: Directory containing the input artifacts (function_embeddings.parquet)
            output_dir: Directory to save output artifacts. If not provided, uses input directory.
            similarity_threshold: Minimum similarity score to create an edge (0-1)
            min_community_size: Minimum number of functions in a community
            max_community_size: Maximum number of functions in a community
            resolution: Resolution parameter for community detection (higher = more communities)
            hierarchical_levels: Number of hierarchical levels to build
            openai_api_key: OpenAI API key
            openai_model: OpenAI model to use for naming communities
        """
        self.input_dir = input_dir
        self.output_dir = output_dir or input_dir
        self.similarity_threshold = similarity_threshold
        self.min_community_size = min_community_size
        self.max_community_size = max_community_size
        self.resolution = resolution
        self.hierarchical_levels = hierarchical_levels
        self.openai_api_key = openai_api_key or get_openai_api_key()
        self.openai_model = openai_model

        # Ensure output directory exists
        os.makedirs(self.output_dir, exist_ok=True)

        # Define paths for input and output files
        self.embeddings_path = os.path.join(self.input_dir, "function_embeddings.parquet")
        
        # Create unique filenames based on parameters
        param_suffix = f"_st{similarity_threshold}_ms{min_community_size}_r{resolution}_hl{hierarchical_levels}"
        self.domain_hierarchy_json = os.path.join(self.output_dir, f"leaf_domains{param_suffix}.json")
        self.similarity_graph_path = os.path.join(self.output_dir, f"similarity_graph{param_suffix}.json")
        self.domain_analysis_yaml = os.path.join(self.output_dir, f"domain_analysis{param_suffix}.yaml")

    async def run(self) -> None:
        """
        Run the clustering process.
        """
        logger.info(f"Starting clustering with parameters: similarity_threshold={self.similarity_threshold}, "
                   f"min_community_size={self.min_community_size}, resolution={self.resolution}, "
                   f"hierarchical_levels={self.hierarchical_levels}")

        # Check if the embeddings file exists
        if not os.path.exists(self.embeddings_path):
            logger.error(f"Embeddings file not found: {self.embeddings_path}")
            return

        # Load the embeddings
        logger.info(f"Loading embeddings from: {self.embeddings_path}")
        embeddings_df = pd.read_parquet(self.embeddings_path)
        logger.info(f"Loaded {len(embeddings_df)} function embeddings")

        # Initialize the semantic similarity graph
        graph = SemanticSimilarityGraph(
            similarity_threshold=self.similarity_threshold,
            min_community_size=self.min_community_size,
            max_community_size=self.max_community_size,
            resolution=self.resolution,
            hierarchical_levels=self.hierarchical_levels,
        )

        # Build the similarity graph
        logger.info("Building similarity graph")
        graph.build_similarity_graph(embeddings_df)

        # Save the graph for visualization in JSON format
        graph.save_graph(self.similarity_graph_path, format="json")
        logger.info(f"Saved similarity graph to: {self.similarity_graph_path}")

        # Build hierarchical communities
        logger.info("Building hierarchical communities")
        graph.build_hierarchical_communities()

        # Name communities using LLM
        logger.info("Naming communities using LLM")
        await graph.name_communities(openai_api_key=self.openai_api_key, model=self.openai_model)

        # Save the domain hierarchy
        graph.save_hierarchy(self.domain_analysis_yaml)
        logger.info(f"Saved domain hierarchy to: {self.domain_analysis_yaml}")

        # Save leaf domains as JSON
        logger.info("Saving leaf domains as JSON")
        graph.save_leaf_domains_json(self.domain_hierarchy_json)
        logger.info(f"Saved leaf domains to: {self.domain_hierarchy_json}")

        # Print summary statistics
        leaf_communities = graph.hierarchy.get_leaf_communities()
        logger.info(f"Clustering complete. Found {len(leaf_communities)} leaf domains.")
        
        # Print top 5 largest leaf domains
        if leaf_communities:
            largest_communities = sorted(leaf_communities, key=lambda c: len(c.functions), reverse=True)[:5]
            logger.info("Top 5 largest leaf domains:")
            for i, community in enumerate(largest_communities, 1):
                logger.info(f"{i}. {community.name}: {len(community.functions)} functions")


async def main():
    """
    Main entry point for the standalone clustering script.
    """
    parser = argparse.ArgumentParser(description="Standalone script for clustering function embeddings and generating leaf domains")
    parser.add_argument("--input", help="Directory containing the input artifacts (function_embeddings.parquet)")
    parser.add_argument("--output", help="Directory to save output artifacts. If not provided, uses input directory.")
    parser.add_argument("--similarity-threshold", type=float, default=0.8, help="Minimum similarity score to create an edge (0-1)")
    parser.add_argument("--min-community-size", type=int, default=15, help="Minimum number of functions in a community")
    parser.add_argument("--max-community-size", type=int, default=100, help="Maximum number of functions in a community")
    parser.add_argument("--resolution", type=float, default=1.0, help="Resolution parameter for community detection (higher = more communities)")
    parser.add_argument("--hierarchical-levels", type=int, default=3, help="Number of hierarchical levels to build")
    parser.add_argument("--openai-api-key", type=str, default=None, help="OpenAI API key")
    parser.add_argument("--openai-model", type=str, default="gpt-4o-mini", help="OpenAI model to use for naming communities")

    args = parser.parse_args()
    args.input = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/exp_embeddings/django"
    args.output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/exp_embeddings/django/param_exp"    

    # Initialize the clusterer
    clusterer = StandaloneClusterer(
        input_dir=args.input,
        output_dir=args.output,
        similarity_threshold=args.similarity_threshold,
        min_community_size=args.min_community_size,
        max_community_size=args.max_community_size,
        resolution=args.resolution,
        hierarchical_levels=args.hierarchical_levels,
        openai_api_key=args.openai_api_key,
        openai_model=args.openai_model,
    )

    # Run the clustering
    await clusterer.run()


if __name__ == "__main__":
    asyncio.run(main())
