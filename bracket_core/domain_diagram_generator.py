"""
Domain Diagram Generator for Codebase

This module provides functionality to:
1. Read domain traces <PERSON>AM<PERSON> and generate mermaid diagrams for leaf subdomains
2. Combine diagrams horizontally and vertically as we move up the hierarchy
3. Generate a top-level diagram representing the entire codebase

It can be used as:
1. A standalone script to process domain traces YAML files
2. A module that can be integrated into the repository analysis flow
"""

import os
import yaml
import json
import logging
import asyncio
import aiohttp
import time
import pandas as pd
from typing import Dict, List, Set, Tuple, Optional, Union, Any
from dataclasses import dataclass, field
import re

# Import clients for diagram generation
from bracket_core.llm.get_client import get_claude_client
from bracket_core.llm.rate_limiter import RateLimiter
from bracket_core.llm.tokens import num_tokens_from_string

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class DiagramGenerationResult:
    """Result of generating domain diagrams."""
    success: bool = True
    error_message: str = ""
    diagram_files: Dict[str, str] = field(default_factory=dict)  # Maps domain trace to file path
    raw_responses: Dict[str, str] = field(default_factory=dict)  # Maps domain trace to raw LLM response
    model_used: str = ""  # The model used for generation
    hierarchy_info: Dict[str, Any] = field(default_factory=dict)  # Information about the hierarchy processing


class DomainDiagramGenerator:
    """
    Generates mermaid diagrams from domain traces.

    This class reads domain traces YAML files, generates mermaid diagrams for leaf subdomains,
    and combines them as we move up the hierarchy.
    """

    def __init__(
        self,
        domain_traces_yaml_path: str,
        functions_parquet_path: str,
        output_dir: str,
        # Model selection and configuration
        model_type: str = "openai",  # "claude" or "openai"
        # Claude parameters
        claude_api_key: Optional[str] = None,
        claude_model: str = "claude-3-7-sonnet-20250219",
        # OpenAI parameters
        openai_api_key: Optional[str] = None,
        # openai_model: str = "gpt-4o-mini",
        openai_model: str = "o3-mini",
        # Common parameters
        max_tokens: int = 8096,
        temperature: float = 0.5,
        max_requests_per_minute: float = 10000,
        max_tokens_per_minute: float = 10000000,
        # Parallelization parameters
        max_concurrent_tasks: int = 5,
        # Hierarchy parameters
        max_hierarchy_depth: Optional[int] = None,
        # Caching parameters
        cache_dir: Optional[str] = None,
        use_cache: bool = True,
        # Token limit parameters
        max_input_tokens: int = 100000,  # Maximum number of tokens for input functions
    ):
        """Initialize the domain diagram generator.

        Args:
            domain_traces_yaml_path: Path to the domain traces YAML file
            functions_parquet_path: Path to the semantic_documented_functions.parquet file
            output_dir: Directory to save generated diagrams
            model_type: Type of model to use ("claude" or "openai")
            claude_api_key: Anthropic API key. If None, will try to get from environment.
            claude_model: Claude model to use.
            openai_api_key: OpenAI API key. If None, will try to get from environment.
            openai_model: OpenAI model to use.
            max_tokens: Maximum tokens to generate.
            temperature: Sampling temperature.
            max_requests_per_minute: Rate limit for API requests
            max_tokens_per_minute: Token rate limit for API
            max_concurrent_tasks: Maximum number of concurrent tasks for parallel processing
            max_hierarchy_depth: Maximum hierarchy depth to process (None for all levels)
            cache_dir: Directory to cache intermediate results (None for no caching)
            use_cache: Whether to use caching for intermediate results
        """
        self.domain_traces_yaml_path = domain_traces_yaml_path
        self.functions_parquet_path = functions_parquet_path
        self.output_dir = output_dir
        self.diagrams_dir = os.path.join(output_dir, "diagrams_with_fn_info")
        os.makedirs(self.diagrams_dir, exist_ok=True)

        # Store model configuration
        self.model_type = model_type.lower()
        self.max_tokens = max_tokens
        self.temperature = temperature
        self.max_requests_per_minute = max_requests_per_minute
        self.max_tokens_per_minute = max_tokens_per_minute

        # Initialize appropriate client based on model type
        if self.model_type == "claude":
            self.claude_client = get_claude_client(
                api_key=claude_api_key,
                model=claude_model,
                max_tokens=max_tokens,
                temperature=temperature,
            )
            self.model_name = claude_model
        elif self.model_type == "openai":
            self.openai_api_key = openai_api_key or os.getenv("OPENAI_API_KEY")
            if not self.openai_api_key:
                raise ValueError("OpenAI API key is required when using OpenAI models")
            self.openai_model = openai_model
            self.model_name = openai_model
        else:
            raise ValueError(f"Unsupported model type: {model_type}. Use 'claude' or 'openai'.")

        # Rate limiter for API calls
        self.rate_limiter = RateLimiter(rate=int(max_requests_per_minute), per=60)

        # Parallelization configuration
        self.max_concurrent_tasks = max_concurrent_tasks

        # Hierarchy configuration
        self.max_hierarchy_depth = max_hierarchy_depth

        # Caching configuration
        self.cache_dir = cache_dir
        self.use_cache = use_cache
        if self.cache_dir and self.use_cache:
            os.makedirs(self.cache_dir, exist_ok=True)

        # Token limit configuration
        self.max_input_tokens = max_input_tokens

        # Cache for function data
        self.function_data = {}
        self.simplified_path_map = {}
        self.domain_hierarchy = {}
        self.leaf_domains = set()
        self.domain_traces = {}
        self.diagram_cache = {}

        # Add counters for tracking missing functions
        self.total_functions_requested = 0
        self.total_functions_missing = 0
        self.missing_function_counts = {}  # Track missing functions by domain

    def read_domain_traces(self) -> Dict[str, List[str]]:
        """
        Read domain traces from the YAML file.

        Returns:
            Dictionary mapping domain traces to lists of function signatures
        """
        logger.info(f"Reading domain traces from: {self.domain_traces_yaml_path}")

        try:
            with open(self.domain_traces_yaml_path, 'r') as f:
                yaml_data = yaml.safe_load(f)

            domain_traces = yaml_data.get('domain_traces', {})
            logger.info(f"Found {len(domain_traces)} domain traces")

            self.domain_traces = domain_traces
            return domain_traces

        except Exception as e:
            logger.error(f"Error reading domain traces: {e}")
            return {}

    def read_function_data(self) -> Dict[str, Dict[str, Any]]:
        """
        Read function data from the parquet file.

        Returns:
            Dictionary mapping node_ids to function data
        """
        logger.info(f"Reading function data from: {self.functions_parquet_path}")

        try:
            df = pd.read_parquet(self.functions_parquet_path)

            # Create a dictionary mapping node_ids to function data
            function_data = {}
            # Also create a mapping from simplified paths to node_ids for easier lookup
            simplified_path_map = {}

            for _, row in df.iterrows():
                node_id = row.get('node_id', '')
                if not node_id:
                    # Skip entries without node_id
                    continue

                # Extract relevant function data
                function_info = {
                    'description': row.get('description', ''),
                    'text': row.get('text', ''),
                    # 'file_path': row.get('file_path', ''),
                    # 'signature': row.get('signature', ''),
                }

                # Parse calls if available
                calls = row.get('calls', [])
                if isinstance(calls, str):
                    try:
                        calls = json.loads(calls)
                    except json.JSONDecodeError:
                        calls = [calls] if calls else []

                function_info['calls'] = calls

                # Parse call_contexts if available
                call_contexts = row.get('call_contexts', [])
                if isinstance(call_contexts, str):
                    try:
                        call_contexts = json.loads(call_contexts)
                    except json.JSONDecodeError:
                        call_contexts = [call_contexts] if call_contexts else []

                function_info['call_contexts'] = call_contexts

                # Store in the main function data dictionary using node_id as key
                function_data[node_id] = function_info

                # Create simplified path versions for easier lookup
                if ':' in node_id:
                    file_path, func_name = node_id.split(':', 1)
                    # Create simplified versions without full path
                    if '/' in file_path:
                        # Get just the filename and function
                        filename = file_path.split('/')[-1]
                        simplified_sig = f"{filename}:{func_name}"
                        simplified_path_map[simplified_sig] = node_id

                        # Also store just the relative path without any base directories
                        for i in range(1, len(file_path.split('/'))):
                            rel_path = '/'.join(file_path.split('/')[-i:])
                            rel_sig = f"{rel_path}:{func_name}"
                            simplified_path_map[rel_sig] = node_id

            logger.info(f"Loaded data for {len(function_data)} functions")
            logger.info(f"Created {len(simplified_path_map)} simplified path mappings for easier lookup")
            self.function_data = function_data
            self.simplified_path_map = simplified_path_map
            return function_data

        except Exception as e:
            logger.error(f"Error reading function data: {e}")
            self.simplified_path_map = {}
            return {}

    def build_domain_hierarchy(self) -> Dict[str, Any]:
        """
        Build a hierarchical representation of domains from domain traces.

        Returns:
            Dictionary representing the domain hierarchy
        """
        logger.info("Building domain hierarchy from domain traces")

        hierarchy = {}

        # Initialize domain tracking structures
        self.all_domains = set()  # All domains at all levels
        self.leaf_domains = set()  # Leaf domains (no children)
        self.domain_parents = {}  # Maps domain to its parent domain
        self.domain_children = {}  # Maps domain to its child domains
        self.domain_levels = {}  # Maps domain to its hierarchy level (0 = root)
        self.domain_paths = {}  # Maps domain to its full path (e.g., "A -> B -> C")

        for trace_str, functions in self.domain_traces.items():
            # Split the trace string into components
            components = trace_str.split(' -> ')

            # Track each domain in the trace
            current_path = ""
            parent_path = ""

            # Navigate/build the hierarchy
            current = hierarchy
            for i, component in enumerate(components):
                # Build the current path
                if current_path:
                    current_path += f" -> {component}"
                    parent_path = current_path.rsplit(' -> ', 1)[0]
                else:
                    current_path = component
                    parent_path = ""  # Root level has no parent

                # Add to tracking structures
                self.all_domains.add(current_path)
                self.domain_paths[current_path] = current_path
                self.domain_levels[current_path] = i

                # Set up parent-child relationships
                if parent_path:
                    if parent_path not in self.domain_children:
                        self.domain_children[parent_path] = set()
                    self.domain_children[parent_path].add(current_path)
                    self.domain_parents[current_path] = parent_path

                # Create the domain in the hierarchy if it doesn't exist
                if component not in current:
                    current[component] = {
                        'functions': [],
                        'subdomains': {},
                        'path': current_path,
                        'level': i
                    }

                # If this is the last component, add the functions
                if i == len(components) - 1:
                    current[component]['functions'].extend(functions)

                # Move to the next level
                current = current[component]['subdomains']

        # Identify leaf domains (those with no children)
        for domain in self.all_domains:
            if domain not in self.domain_children or not self.domain_children[domain]:
                self.leaf_domains.add(domain)

        self.domain_hierarchy = hierarchy
        logger.info(f"Built domain hierarchy with {len(hierarchy)} top-level domains")
        logger.info(f"Identified {len(self.all_domains)} total domains across all levels")
        logger.info(f"Identified {len(self.leaf_domains)} leaf domains (no children)")

        return hierarchy

    def sort_domains_by_level(self) -> Dict[int, List[str]]:
        """
        Sort domains by their hierarchy level to ensure child domains are processed before parents.

        Returns:
            Dictionary mapping hierarchy levels to lists of domains at that level
        """
        logger.info("Sorting domains by hierarchy level")

        # Group domains by level
        domains_by_level = {}
        for domain, level in self.domain_levels.items():
            if level not in domains_by_level:
                domains_by_level[level] = []
            domains_by_level[level].append(domain)

        # Sort levels from bottom (highest level number) to top (level 0)
        sorted_levels = sorted(domains_by_level.keys(), reverse=True)

        # Log the results
        for level in sorted_levels:
            logger.info(f"Level {level}: {len(domains_by_level[level])} domains")

        return {level: domains_by_level[level] for level in sorted_levels}

    def _get_cache_path(self, domain_trace: str, suffix: str = "") -> Optional[str]:
        """
        Get the cache file path for a domain diagram.

        Args:
            domain_trace: Domain trace string
            suffix: Optional suffix to add to the filename

        Returns:
            Cache file path or None if caching is disabled
        """
        if not self.cache_dir or not self.use_cache:
            return None

        # Create a safe filename from the domain trace
        safe_name = domain_trace.replace(' -> ', '_').replace(' ', '_')
        if suffix:
            safe_name = f"{safe_name}_{suffix}"

        return os.path.join(self.cache_dir, f"{safe_name}.md")

    def _check_cache(self, domain_trace: str) -> Optional[str]:
        """
        Check if a domain diagram is cached.

        Args:
            domain_trace: Domain trace string

        Returns:
            Cached diagram content or None if not cached
        """
        if not self.cache_dir or not self.use_cache:
            return None

        # Check if the domain is already in the in-memory cache
        if domain_trace in self.diagram_cache:
            logger.info(f"Found diagram for {domain_trace} in memory cache")
            return self.diagram_cache[domain_trace]

        # Check if the domain diagram is cached on disk
        cache_path = self._get_cache_path(domain_trace)
        if cache_path and os.path.exists(cache_path):
            try:
                with open(cache_path, 'r') as f:
                    diagram = f.read()

                # Add to in-memory cache
                self.diagram_cache[domain_trace] = diagram
                logger.info(f"Found diagram for {domain_trace} in disk cache: {cache_path}")
                return diagram
            except Exception as e:
                logger.error(f"Error reading cached diagram for {domain_trace}: {e}")

        return None

    def _save_to_cache(self, domain_trace: str, diagram: str) -> None:
        """
        Save a domain diagram to the cache.

        Args:
            domain_trace: Domain trace string
            diagram: Diagram content
        """
        if not self.cache_dir or not self.use_cache:
            return

        # Add to in-memory cache
        self.diagram_cache[domain_trace] = diagram

        # Save to disk cache
        cache_path = self._get_cache_path(domain_trace)
        if cache_path:
            try:
                with open(cache_path, 'w') as f:
                    f.write(diagram)
                logger.info(f"Saved diagram for {domain_trace} to cache: {cache_path}")
            except Exception as e:
                logger.error(f"Error saving diagram for {domain_trace} to cache: {e}")

    def _extract_mermaid_diagram(self, response: str) -> str:
        """
        Extract mermaid diagram from LLM response.

        Args:
            response: LLM response text

        Returns:
            Extracted mermaid diagram or empty string if not found
        """
        # Look for mermaid diagram between triple backticks
        mermaid_pattern = r"```mermaid\s*([\s\S]*?)\s*```"
        matches = re.findall(mermaid_pattern, response)

        if matches:
            return f"```mermaid\n{matches[0]}\n```"

        return ""

    async def _call_openai_api(self, system_prompt: str, user_prompt: str) -> Tuple[str, str]:
        """
        Call the OpenAI API with the given prompts.

        Args:
            system_prompt: System prompt for the API call
            user_prompt: User prompt for the API call

        Returns:
            Tuple of (response_text, raw_response_json)
        """
        # Create the API request
        request_url = "https://api.openai.com/v1/chat/completions"
        request_header = {"Authorization": f"Bearer {self.openai_api_key}", "Content-Type": "application/json"}

        # Estimate token usage for rate limiting (for future use)
        _ = num_tokens_from_string(system_prompt + user_prompt, self.openai_model)

        # Create the request payload
        request_json = {
            "model": self.openai_model,
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            # "temperature": self.temperature,
            # "max_tokens": self.max_tokens
        }

        try:
            # Make the API call
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    url=request_url,
                    headers=request_header,
                    json=request_json
                ) as response:
                    response_json = await response.json()

                    # Extract the response text
                    if "choices" in response_json and len(response_json["choices"]) > 0:
                        response_text = response_json["choices"][0]["message"]["content"]
                        return response_text, json.dumps(response_json)
                    else:
                        error_msg = f"Unexpected response format: {json.dumps(response_json)}"
                        logger.error(error_msg)
                        return "", error_msg
        except Exception as e:
            error_msg = f"Error calling OpenAI API: {str(e)}"
            logger.error(error_msg)
            return "", error_msg

    def _is_empty_domain_mapping(self, domain_trace: str) -> bool:
        """
        Check if a domain has empty file mappings in the domain file mappings YAML.

        Args:
            domain_trace: Domain trace string

        Returns:
            True if the domain has empty file mappings, False otherwise
        """
        # Get the last component of the domain trace (the leaf domain name)
        domain_name = domain_trace.split(' -> ')[-1]

        # Check if this domain exists in the domain traces but has empty functions list
        if domain_trace in self.domain_traces and not self.domain_traces[domain_trace]:
            return True

        # Check if the function list contains only a placeholder like 'No functions found.'
        if domain_trace in self.domain_traces and len(self.domain_traces[domain_trace]) == 1:
            func = self.domain_traces[domain_trace][0]
            if func == 'No functions found.' or func.startswith('No functions'):
                return True

        return False

    async def generate_leaf_diagram(self, domain_trace: str, functions: List[str]) -> Tuple[str, str]:
        """
        Generate a mermaid diagram for a leaf domain.

        Args:
            domain_trace: Domain trace string
            functions: List of node_ids in this domain

        Returns:
            Tuple of (diagram_content, raw_response)
        """
        logger.info(f"Generating diagram for leaf domain: {domain_trace}")

        # Check if this domain has empty file mappings in the domain file mappings YAML
        if self._is_empty_domain_mapping(domain_trace):
            logger.warning(f"Domain has empty file mappings: {domain_trace}. Generating empty domain diagram.")
            empty_diagram = "```mermaid\ngraph TD\n    A[Empty Domain - No Files Mapped]\n    B[This domain has no files mapped to it in the domain file mappings]\n    A --> B\n    style A fill:#f96,stroke:#333,stroke-width:2px\n    style B fill:#fff,stroke:#333,stroke-width:1px\n```"
            return empty_diagram, "Domain has empty file mappings in the domain file mappings YAML"

        # Check if there are any functions in this domain
        if not functions:
            logger.warning(f"No functions found for domain: {domain_trace}. Skipping diagram generation.")
            return "```mermaid\ngraph TD\n    A[No functions found in this domain]\n```", "No functions found in this domain"

        # Apply rate limiting
        await self.rate_limiter.acquire()

        # Prepare function data for the prompt
        function_details = []
        missing_functions = []
        found_functions = []
        truncated_functions = []
        current_token_count = 0

        # Update total functions counter
        self.total_functions_requested += len(functions)

        for node_id in functions:
            # Try direct lookup first
            if node_id in self.function_data:
                func_data = self.function_data[node_id]
                found_functions.append(node_id)

                # Format function details
                func_detail = {
                    'node_id': node_id,
                    'description': func_data.get('description', ''),
                    'text': func_data.get('text', ''),
                    # 'file_path': func_data.get('file_path', ''),
                    # 'signature': func_data.get('signature', ''),
                    # 'calls': func_data.get('calls', []),
                    # 'call_contexts': func_data.get('call_contexts', [])
                }

                # Estimate token count for this function
                func_str = json.dumps(func_detail)
                func_tokens = num_tokens_from_string(func_str, self.model_name)

                # Check if adding this function would exceed the token limit
                if current_token_count + func_tokens <= self.max_input_tokens:
                    function_details.append(func_detail)
                    current_token_count += func_tokens
                else:
                    # We've reached the token limit, truncate remaining functions
                    truncated_functions.append(node_id)
                    continue

            # Try lookup via simplified path map
            elif node_id in self.simplified_path_map:
                actual_node_id = self.simplified_path_map[node_id]
                func_data = self.function_data[actual_node_id]
                found_functions.append(node_id)

                # Format function details
                func_detail = {
                    'node_id': node_id,
                    'description': func_data.get('description', ''),
                    'text': func_data.get('text', ''),
                    # 'file_path': func_data.get('file_path', ''),
                    # 'signature': func_data.get('signature', ''),
                    # 'calls': func_data.get('calls', []),
                    # 'call_contexts': func_data.get('call_contexts', [])
                }

                # Estimate token count for this function
                func_str = json.dumps(func_detail)
                func_tokens = num_tokens_from_string(func_str, self.model_name)

                # Check if adding this function would exceed the token limit
                if current_token_count + func_tokens <= self.max_input_tokens:
                    function_details.append(func_detail)
                    current_token_count += func_tokens
                else:
                    # We've reached the token limit, truncate remaining functions
                    truncated_functions.append(node_id)
                    continue
            else:
                missing_functions.append(node_id)
                # Update missing functions counter
                self.total_functions_missing += 1

        # Log token usage information
        logger.info(f"Token usage for domain {domain_trace}: {current_token_count}/{self.max_input_tokens} tokens used")

        # Track missing functions by domain
        if missing_functions:
            self.missing_function_counts[domain_trace] = len(missing_functions)

        # Log the results
        if found_functions:
            logger.info(f"Found {len(found_functions)} functions for domain {domain_trace}")
        if missing_functions:
            logger.warning(f"Missing {len(missing_functions)} functions for domain {domain_trace}: {missing_functions}")
        if truncated_functions:
            logger.warning(f"Truncated {len(truncated_functions)} functions for domain {domain_trace} due to token limit of {self.max_input_tokens}")

        # If no functions were found at all, return a message about it
        if not function_details:
            logger.warning(f"No functions found for domain: {domain_trace}. Skipping diagram generation.")
            return "```mermaid\ngraph TD\n    A[No functions found in this domain]\n```", f"No functions found in domain: {domain_trace}. Please check that the function signatures in domain_traces.yaml match those in the functions parquet file."

        # Update the system prompt to request more functional flow diagrams
        system_prompt = """You are an expert software architect who specializes in creating clear, informative mermaid diagrams.
Your task is to analyze the provided functions and create a comprehensive mermaid diagram that shows their relationships and interactions.

Guidelines for creating the diagram:
1. Create a mermaid diagram that shows the actual flow of execution and data between functions
2. DO NOT create a simple class diagram that just lists methods - focus on how functions interact
3. Show the sequence of operations and data transformations
4. Include decision points, loops, and conditional paths where relevant
5. Illustrate input/output relationships between functions
6. Use different node shapes to represent different types of operations
7. Use directional arrows with descriptive labels to show data flow and function calls
8. Group related functionality using subgraphs
9. Use colors to distinguish between different types of operations or components
10. Include a legend explaining the symbols and colors used

Your output should ONLY contain a valid mermaid diagram(not classDiagram) enclosed in triple backticks with the mermaid tag.
"""

        # Create the user prompt
        truncation_note = ""
        if truncated_functions:
            truncation_percentage = len(truncated_functions) / (len(function_details) + len(truncated_functions)) * 100
            truncation_note = f"\n\nNOTE: Due to token limits, {len(truncated_functions)} functions ({truncation_percentage:.1f}% of total) were truncated from this diagram. The diagram shows the {len(function_details)} most important functions that fit within the token limit."

        user_prompt = f"""Create a mermaid diagram for the domain: {domain_trace}

Here are the functions in this domain with their full implementation:

{json.dumps(function_details, indent=2)}{truncation_note}

Please generate a comprehensive mermaid diagram that shows how these functions work together,
including the flow of execution, data transformations, and key decision points.
DO NOT create a simple class diagram - focus on the actual functional relationships and processes.
"""

        try:
            # Call the appropriate API based on model type
            if self.model_type == "claude":
                # Call the Claude API
                response = await self.claude_client.generate(
                    system_prompt=system_prompt,
                    prompt=user_prompt,
                    max_tokens=8000,
                    temperature=0.7,
                    top_p=0.8
                )
                raw_response = response
            else:  # OpenAI
                # Call the OpenAI API
                response, raw_response = await self._call_openai_api(
                    system_prompt=system_prompt,
                    user_prompt=user_prompt
                )

            # Extract the mermaid diagram
            diagram = self._extract_mermaid_diagram(response)

            if not diagram:
                logger.warning(f"No mermaid diagram found in response for {domain_trace}")
                diagram = response

            return diagram, raw_response

        except Exception as e:
            logger.error(f"Error generating diagram for {domain_trace}: {e}")
            return "", f"Error: {str(e)}"

    async def generate_combined_diagram(
        self,
        domain_name: str,
        subdomains: Dict[str, str],
        level: int
    ) -> Tuple[str, str]:
        """
        Generate a combined diagram for a domain and its subdomains.

        Args:
            domain_name: Name of the domain
            subdomains: Dictionary mapping subdomain names to their diagram file paths
            level: Hierarchy level (higher means more abstract)

        Returns:
            Tuple of (diagram_content, raw_response)
        """
        logger.info(f"Generating combined diagram for domain: {domain_name} with {len(subdomains)} subdomains")

        # Apply rate limiting
        await self.rate_limiter.acquire()

        # Read subdomain diagrams
        subdomain_diagrams = {}
        for subdomain, file_path in subdomains.items():
            try:
                with open(file_path, 'r') as f:
                    subdomain_diagrams[subdomain] = f.read()
            except Exception as e:
                logger.error(f"Error reading subdomain diagram {file_path}: {e}")
                subdomain_diagrams[subdomain] = f"Error reading diagram: {str(e)}"

        # Create the system prompt
        system_prompt = """You are an expert software architect who creates clear, informative mermaid diagrams to visualize code architecture.
Your task is to create a combined mermaid diagram that represents a domain and its subdomains in a codebase, preserving the logical relationships and interactions between components.

Guidelines for creating the combined diagram:
1. Create a higher-level abstraction that shows the detailed relationships and interactions between subdomains
2. Focus on the key components, interfaces, and LOGICAL FLOWS between subdomains
2.5. Make sure to keep modularity intact and not too many arrows to keep the diagram visually appealing.
3. DO NOT include individual function-level details, but DO preserve the logical processes and data flows in a clean manner - not too many arrows all around.
4. Represent each subdomain as a cohesive unit with its specific responsibilities and capabilities
5. Show detailed data flow and interactions between subdomains using clear directional arrows with descriptive labels
6. Indicate the nature of each interaction (e.g., data transfer, API call, event trigger, etc.)
7. Show the sequence and dependencies of operations across subdomains
8. Include any shared resources, services, or data stores that multiple subdomains interact with
9. DO NOT use tooltips or click actions - they consume unnecessary tokens
10. The diagram should be 3000-6000 tokens in size to provide comprehensive detail

Styling Guidelines (IMPORTANT):
1. Use a balanced layout that clearly shows the hierarchy and relationships
2. MAINTAIN COLOR CONSISTENCY with the child domains - when referencing a subdomain, use the same color scheme that was used in its original diagram
3. Use pastel colors for all components - avoid bright or dark colors
4. For new elements not present in child domains, use this color scheme:
   - Core domain components: pastel blue (#D4F1F9)
   - Shared services: pastel yellow (#FFF8DC)
   - Data stores: pastel green (#E0F8E0)
   - External interfaces: pastel purple (#E6E6FA)
   - Error handling: pastel red (#FFE4E1)
5. Use rounded rectangles for domain components: node[shape="rounded-rectangle"]
6. Use cylinders for shared data stores: storage[shape="cylinder"]
7. Use consistent line thickness and arrow styles
8. Ensure proper spacing between components
9. Follow strict mermaid.js syntax to ensure the diagram renders correctly
10. Avoid use of brackets, `(` or `)` and avoid adding any notes. Please follow the mermaid syntax properly.

Your output should ONLY contain a valid mermaid diagram enclosed in triple backticks with the mermaid tag.
Ensure the diagram follows proper mermaid.js syntax and is renderable without any syntax errors.
"""

        # Create the user prompt
        user_prompt = f"""Create a detailed combined mermaid diagram for the domain: {domain_name}

This domain contains the following subdomains, each with its own diagram:

{json.dumps(list(subdomains.keys()), indent=2)}

Here are the subdomain diagrams:

{json.dumps(subdomain_diagrams, indent=2)}

Please generate a comprehensive mermaid diagram that shows this domain and how its subdomains relate to each other.
The diagram should be at abstraction level {level} (higher means more abstract).

IMPORTANT INSTRUCTIONS:
1. DO NOT include individual function-level details in this diagram
2. Focus on detailed subdomain relationships, logical flows, and data transformations
2.5. Make sure to keep modularity intact and not too many arrows to keep the diagram visually appealing.
3. Preserve the logical processes and interactions between subdomains
4. Show the sequence and dependencies of operations across subdomains
5. Represent each subdomain as a cohesive unit with its specific responsibilities and capabilities
6. Include detailed labels on arrows to explain the nature of each interaction
7. Show any shared resources, services, or data stores that multiple subdomains interact with
8. Include error handling and alternative flows between subdomains where relevant
9. DO NOT use tooltips or click actions
10. The higher the level ({level}), the more you should focus on business processes rather than technical details

IMPORTANT STYLING REQUIREMENTS:
- MAINTAIN COLOR CONSISTENCY with the child domains - use the same colors for subdomains as in their original diagrams
- Use PASTEL COLORS for all new components
- Follow the color scheme specified in the system prompt
- Ensure proper spacing and layout for readability
- Follow strict mermaid.js syntax to ensure the diagram renders correctly
- Avoid use of brackets, `(` or `)` and avoid adding any notes. Please follow the mermaid syntax properly.
"""

        try:
            # Call the appropriate API based on model type
            if self.model_type == "claude":
                # Call the Claude API
                response = await self.claude_client.generate(
                    system_prompt=system_prompt,
                    prompt=user_prompt,
                    max_tokens=8000,
                    temperature=0.7,
                    top_p=0.8
                )
                raw_response = response
            else:  # OpenAI
                # Call the OpenAI API
                response, raw_response = await self._call_openai_api(
                    system_prompt=system_prompt,
                    user_prompt=user_prompt
                )

            # Extract the mermaid diagram
            diagram = self._extract_mermaid_diagram(response)

            if not diagram:
                logger.warning(f"No mermaid diagram found in response for {domain_name}")
                diagram = response

            return diagram, raw_response

        except Exception as e:
            logger.error(f"Error generating combined diagram for {domain_name}: {e}")
            return "", f"Error: {str(e)}"

    async def _prepare_leaf_domain_request(self, domain_trace: str, functions: List[str]) -> Tuple[str, str, str]:
        """
        Prepare the API request for a leaf domain diagram without waiting for the response.

        Args:
            domain_trace: Domain trace string
            functions: List of function signatures in this domain

        Returns:
            Tuple of (domain_trace, system_prompt, user_prompt)
        """
        # Prepare function data for the prompt
        function_details = []
        missing_functions = []
        found_functions = []

        for func_sig in functions:
            # Try direct lookup first
            if func_sig in self.function_data:
                func_data = self.function_data[func_sig]
                found_functions.append(func_sig)

                # Format function details
                func_detail = {
                    # 'signature': func_sig,
                    'description': func_data.get('description', ''),
                    'text': func_data.get('text', ''),
                    # 'file_path': func_data.get('file_path', ''),
                    # 'calls': func_data.get('calls', []),
                    # 'call_contexts': func_data.get('call_contexts', [])
                }

                function_details.append(func_detail)
            # Try lookup via simplified path map
            elif func_sig in self.simplified_path_map:
                actual_sig = self.simplified_path_map[func_sig]
                func_data = self.function_data[actual_sig]
                found_functions.append(func_sig)

                # Format function details
                func_detail = {
                    # 'signature': func_sig,
                    'description': func_data.get('description', ''),
                    'text': func_data.get('text', ''),
                    # 'calls': func_data.get('calls', []),
                    # 'call_contexts': func_data.get('call_contexts', [])
                }

                function_details.append(func_detail)
            else:
                missing_functions.append(func_sig)

        # Log the results
        if found_functions:
            logger.info(f"Found {len(found_functions)} functions for domain {domain_trace}")
        if missing_functions:
            logger.warning(f"Missing {len(missing_functions)} functions for domain {domain_trace}: {missing_functions}")

        # If no functions were found at all, check if this is an empty domain mapping
        if not function_details:
            if self._is_empty_domain_mapping(domain_trace):
                logger.warning(f"Domain has empty file mappings: {domain_trace}. Generating empty domain diagram.")
                # Return empty prompts with special markers that will be recognized in the processing step
                return domain_trace, "EMPTY_DOMAIN_MAPPING", "EMPTY_DOMAIN_MAPPING"
            else:
                logger.warning(f"No functions found for domain: {domain_trace}. Will generate placeholder diagram.")
                # We'll handle this case in the _send_api_request method

        # Get hierarchy information
        level = self.domain_levels.get(domain_trace, 0)

        # Create the system prompt
        system_prompt = """You are an expert software architect who creates clear, informative mermaid diagrams to visualize code architecture.
Your task is to create a detailed mermaid diagram that represents the LOGICAL RELATIONSHIPS and INTERACTIONS between functions in a specific domain of a codebase.

Guidelines for creating the diagram:
1. Focus on the LOGICAL PURPOSE and ROLE of each function within the domain
2. Emphasize how functions work together to accomplish domain goals
2.5. Make sure to keep modularity intact and not too many arrows to keep the diagram visually appealing.
3. Show meaningful relationships and dependencies between functions
4. Highlight the conceptual flow of data and control between functions
5. Group functions by their logical purpose or the feature they support
6. Represent the domain's core concepts and how functions implement them
7. Show how functions collaborate to implement domain behaviors
8. Illustrate key abstractions and patterns used in the domain
9. Include important domain-specific data structures and their transformations
10. Show initialization sequences and important process flows
11. DO NOT create a simple procedural flowchart - focus on logical relationships
12. DO NOT use tooltips or click actions - they consume unnecessary tokens
13. The diagram should be 3000-6000 tokens in size to provide comprehensive detail

Styling Guidelines (IMPORTANT):
1. Use a VERTICAL layout rather than horizontal for better readability
2. Use PASTEL COLORS for all nodes and subgraphs - avoid bright or dark colors
3. Use this consistent color scheme:
   - Core domain functions: pastel blue (#D4F1F9)
   - Supporting/utility functions: pastel yellow (#FFF8DC)
   - Data structure handlers: pastel green (#E0F8E0)
   - Error handling functions: pastel red (#FFE4E1)
   - Initialization/setup functions: pastel purple (#E6E6FA)
   - Logical groupings/subgraphs: very light gray (#F8F8F8) with pastel borders
4. Use rounded rectangles for most nodes: node[shape="rounded-rectangle"]
5. Use different node shapes to represent different types of functions when appropriate
6. Use consistent line thickness and arrow styles
7. Ensure proper spacing between nodes and subgraphs
8. Follow strict mermaid.js syntax to ensure the diagram renders correctly
9. Avoid use of brackets, `(` or `)` and avoid adding any notes. Please follow the mermaid syntax properly.

Your output should ONLY contain a valid mermaid diagram enclosed in triple backticks with the mermaid tag.
Ensure the diagram follows proper mermaid.js syntax and is renderable without any syntax errors.
"""

        # Create the user prompt
        user_prompt = f"""Create a detailed mermaid diagram for the leaf domain: {domain_trace} (Hierarchy Level: {level})

This is a leaf domain with no subdomains. Focus on showing the LOGICAL RELATIONSHIPS and INTERACTIONS between functions, not just their implementation details.

Here are the functions in this domain:

{json.dumps(function_details, indent=2)}

Please generate a comprehensive mermaid diagram that shows:
1. The LOGICAL PURPOSE of each function within the domain context
2. How functions COLLABORATE to implement domain behaviors
2.5. Make sure to keep modularity intact and not too many arrows to keep the diagram visually appealing.
3. The MEANINGFUL RELATIONSHIPS and dependencies between functions
4. How functions are GROUPED by their logical purpose or features they support
5. The domain's CORE CONCEPTS and how functions implement them
6. Important DOMAIN-SPECIFIC DATA STRUCTURES and their transformations
7. Key ABSTRACTIONS and PATTERNS used in the domain

IMPORTANT STYLING REQUIREMENTS:
- Use a VERTICAL layout
- Use PASTEL COLORS for all nodes and subgraphs
- Follow the color scheme specified in the system prompt
- Ensure the diagram is well-structured and easy to read
- Follow strict mermaid.js syntax to ensure the diagram renders correctly
- Avoid use of brackets, `(` or `)` and avoid adding any notes. Please follow the mermaid syntax properly.

Make sure to capture the LOGICAL RELATIONSHIPS between functions, not just their procedural flow.
DO NOT create a simple procedural flowchart - focus on meaningful interactions and relationships.
DO NOT use tooltips or click actions in the diagram.
"""

        return domain_trace, system_prompt, user_prompt

    async def _prepare_intermediate_domain_request(self, domain_trace: str, child_diagrams: Dict[str, str]) -> Tuple[str, str, str]:
        """
        Prepare the API request for an intermediate domain diagram without waiting for the response.

        Args:
            domain_trace: Domain trace string
            child_diagrams: Dictionary mapping child domain names to their diagram file paths

        Returns:
            Tuple of (domain_trace, system_prompt, user_prompt)
        """
        # Get hierarchy information
        level = self.domain_levels.get(domain_trace, 0)
        child_domains = self.domain_children.get(domain_trace, set())

        # Read child domain diagrams
        child_diagram_contents = {}
        for child in child_domains:
            if child in child_diagrams:
                try:
                    with open(child_diagrams[child], 'r') as f:
                        child_diagram_contents[child] = f.read()
                except Exception as e:
                    logger.error(f"Error reading child diagram for {child}: {e}")

        # Get functions directly in this domain (not in children)
        domain_functions = []
        for component in domain_trace.split(' -> '):
            current_domain = domain_trace
            if current_domain in self.domain_traces:
                for func_sig in self.domain_traces[current_domain]:
                    if func_sig in self.function_data:
                        domain_functions.append({
                            'signature': func_sig,
                            'description': self.function_data[func_sig].get('description', ''),
                            'file_path': self.function_data[func_sig].get('file_path', '')
                        })

        # Create the system prompt
        system_prompt = """You are an expert software architect who creates clear, informative mermaid diagrams to visualize code architecture.
Your task is to create a combined mermaid diagram that represents a domain and its subdomains in a codebase, preserving the logical relationships and interactions between components.

Guidelines for creating the combined diagram:
1. Create a higher-level abstraction that shows the detailed relationships and interactions between subdomains
2. Focus on the key components, interfaces, and LOGICAL FLOWS between subdomains
2.5. Make sure to keep modularity intact and not too many arrows to keep the diagram visually appealing.
3. DO NOT include individual function-level details, but DO preserve the logical processes and data flows
4. Represent each subdomain as a cohesive unit with its specific responsibilities and capabilities
5. Show detailed data flow and interactions between subdomains using clear directional arrows with descriptive labels
6. Indicate the nature of each interaction (e.g., data transfer, API call, event trigger, etc.)
7. Show the sequence and dependencies of operations across subdomains
8. Include any shared resources, services, or data stores that multiple subdomains interact with
9. DO NOT use tooltips or click actions - they consume unnecessary tokens
10. The diagram should be 3000-6000 tokens in size to provide comprehensive detail

Styling Guidelines (IMPORTANT):
1. Use a balanced layout that clearly shows the hierarchy and relationships
2. MAINTAIN COLOR CONSISTENCY with the child domains - when referencing a subdomain, use the same color scheme that was used in its original diagram
3. Use pastel colors for all components - avoid bright or dark colors
4. For new elements not present in child domains, use this color scheme:
   - Core domain components: pastel blue (#D4F1F9)
   - Shared services: pastel yellow (#FFF8DC)
   - Data stores: pastel green (#E0F8E0)
   - External interfaces: pastel purple (#E6E6FA)
   - Error handling: pastel red (#FFE4E1)
5. Use rounded rectangles for domain components: node[shape="rounded-rectangle"]
6. Use cylinders for shared data stores: storage[shape="cylinder"]
7. Use consistent line thickness and arrow styles
8. Ensure proper spacing between components
9. Follow strict mermaid.js syntax to ensure the diagram renders correctly
10. Avoid use of brackets, `(` or `)` and avoid adding any notes. Please follow the mermaid syntax properly.

Your output should ONLY contain a valid mermaid diagram enclosed in triple backticks with the mermaid tag.
Ensure the diagram follows proper mermaid.js syntax and is renderable without any syntax errors.
"""

        # Create the user prompt
        user_prompt = f"""Create a combined mermaid diagram for the intermediate domain: {domain_trace} (Hierarchy Level: {level})

This domain contains the following child domains:
{json.dumps(list(child_domains), indent=2)}

"""

        # Add information about functions directly in this domain
        if domain_functions:
            user_prompt += f"""
This domain also contains some functions directly, but DO NOT include individual function details in the diagram.
Instead, represent the overall functionality these functions provide as a cohesive unit.
"""

        # Add child diagram information
        user_prompt += f"""
Here are the diagrams for the child domains:
{json.dumps(child_diagram_contents, indent=2)}

Please generate a comprehensive mermaid diagram that shows this domain and how its child domains relate to each other.

IMPORTANT INSTRUCTIONS:
1. DO NOT include individual function-level details in this diagram
2. Focus on detailed subdomain relationships, logical flows, and data transformations
2.5. Make sure to keep modularity intact and not too many arrows to keep the diagram visually appealing.
3. Preserve the logical processes and interactions between subdomains
4. Show the sequence and dependencies of operations across subdomains
5. Represent each subdomain as a cohesive unit with its specific responsibilities and capabilities
6. Include detailed labels on arrows to explain the nature of each interaction
7. Show any shared resources, services, or data stores that multiple subdomains interact with
8. Include error handling and alternative flows between subdomains where relevant
9. DO NOT use tooltips or click actions
10. The higher the level ({level}), the more you should focus on business processes rather than technical details

IMPORTANT STYLING REQUIREMENTS:
- MAINTAIN COLOR CONSISTENCY with the child domains - use the same colors for subdomains as in their original diagrams
- Use PASTEL COLORS for all new components
- Follow the color scheme specified in the system prompt
- Ensure proper spacing and layout for readability
- Follow strict mermaid.js syntax to ensure the diagram renders correctly
- Avoid use of brackets, `(` or `)` and avoid adding any notes. Please follow the mermaid syntax properly.
"""

        return domain_trace, system_prompt, user_prompt

    async def _send_api_request(self, domain_trace: str, system_prompt: str, user_prompt: str) -> Tuple[str, asyncio.Future]:
        """
        Send an API request for a leaf domain diagram.

        Args:
            domain_trace: Domain trace string
            system_prompt: System prompt for the API call
            user_prompt: User prompt for the API call

        Returns:
            Tuple of (domain_trace, future) where future will resolve to the API response
        """
        # Check for special markers for empty domain mappings
        if system_prompt == "EMPTY_DOMAIN_MAPPING" and user_prompt == "EMPTY_DOMAIN_MAPPING":
            logger.info(f"Creating empty domain mapping future for {domain_trace}")
            # Create a future and set its result to the special marker
            future = asyncio.get_event_loop().create_future()
            future.set_result("EMPTY_DOMAIN_MAPPING")
            return domain_trace, future

        # Check cache first
        cached_diagram = self._check_cache(domain_trace)
        if cached_diagram:
            # Use cached diagram
            logger.info(f"Using cached diagram for {domain_trace}")

            # Create a future and set its result
            future = asyncio.get_event_loop().create_future()
            future.set_result(cached_diagram)

            return domain_trace, future

        # Apply rate limiting
        await self.rate_limiter.acquire()

        if self.model_type == "claude":
            # Create a task for the Claude API call
            task = asyncio.create_task(self.claude_client.generate(
                system_prompt=system_prompt,
                prompt=user_prompt,
                max_tokens=8000,
                temperature=0.5,
                top_p=0.8
            ))
        else:  # OpenAI
            # Create a task for the OpenAI API call
            task = asyncio.create_task(self._call_openai_api(
                system_prompt=system_prompt,
                user_prompt=user_prompt
            ))

        return domain_trace, task

    async def _process_api_response(self, domain_trace: str, response_task: Union[asyncio.Task, asyncio.Future], result: DiagramGenerationResult) -> None:
        """
        Process the API response for a domain diagram.

        Args:
            domain_trace: Domain trace string
            response_task: Task or Future that will resolve to the API response
            result: DiagramGenerationResult to update with the results
        """
        try:
            # Check if this is an empty domain mapping marker
            if isinstance(response_task, asyncio.Future) and response_task.done() and response_task.result() == "EMPTY_DOMAIN_MAPPING":
                logger.info(f"Processing empty domain mapping for {domain_trace}")
                diagram = "```mermaid\ngraph TD\n    A[Empty Domain - No Files Mapped]\n    B[This domain has no files mapped to it in the domain file mappings]\n    A --> B\n    style A fill:#f96,stroke:#333,stroke-width:2px\n    style B fill:#fff,stroke:#333,stroke-width:1px\n```"
                raw_response = "Domain has empty file mappings in the domain file mappings YAML"
            else:
                # Check cache first
                cached_diagram = self._check_cache(domain_trace)
                if cached_diagram:
                    # Use cached diagram
                    diagram = cached_diagram
                    raw_response = f"[CACHED] Diagram for {domain_trace}"
                    logger.info(f"Using cached diagram for {domain_trace}")
                else:
                    # Wait for the response
                    if self.model_type == "claude":
                        response = await response_task
                        raw_response = response
                    else:  # OpenAI
                        response, raw_response = await response_task

                    # Extract the mermaid diagram
                    diagram = self._extract_mermaid_diagram(response)

                    if not diagram:
                        logger.warning(f"No mermaid diagram found in response for {domain_trace}")
                        diagram = response

                    # Save to cache
                    self._save_to_cache(domain_trace, diagram)

            # Get the level of this domain trace
            level = self.domain_levels.get(domain_trace, 0)

            # Create a standardized filename based on the hierarchy level and domain components
            domain_components = domain_trace.split(' -> ')

            # For the standardized name, use L{level}_{component1}_{component2}...
            # This makes it easy to identify the hierarchy level and domain path
            standardized_name = f"L{level}"

            # Add each component to the filename
            for component in domain_components:
                # Clean the component name for use in a filename
                clean_component = component.replace(' ', '_').replace(',', '').replace('&', 'and')
                standardized_name += f"_{clean_component}"

            # Save the diagram to the output directory
            diagram_path = os.path.join(self.diagrams_dir, f"{standardized_name}.md")

            # Also store the mapping from standardized name to domain trace
            # This will be useful for the taxonomy mapper
            if not hasattr(self, 'diagram_name_mapping'):
                self.diagram_name_mapping = {}
            self.diagram_name_mapping[standardized_name] = domain_trace

            with open(diagram_path, 'w') as f:
                f.write(diagram)

            # Save raw response
            raw_path = os.path.join(self.diagrams_dir, f"{standardized_name}_raw.txt")
            with open(raw_path, 'w') as f:
                f.write(raw_response)

            # Add to result
            result.diagram_files[domain_trace] = diagram_path
            result.raw_responses[domain_trace] = raw_response

            logger.info(f"Processed response for {domain_trace}: {diagram_path}")

        except Exception as e:
            logger.error(f"Error processing response for {domain_trace}: {e}")
            import traceback
            logger.error(traceback.format_exc())

    async def generate_all_diagrams(self) -> DiagramGenerationResult:
        """
        Generate all diagrams for the domain hierarchy using a level-by-level approach.

        Returns:
            DiagramGenerationResult containing the generation results
        """
        logger.info("Generating all domain diagrams using hierarchical approach")

        # Initialize result
        result = DiagramGenerationResult(model_used=f"{self.model_type}:{self.model_name}")
        result.hierarchy_info = {
            "total_levels": 0,
            "domains_by_level": {},
            "processing_time_by_level": {}
        }

        try:
            # Read domain traces and function data
            self.read_domain_traces()
            self.read_function_data()

            # Build domain hierarchy
            self.build_domain_hierarchy()

            # Sort domains by level (bottom-up)
            domains_by_level = self.sort_domains_by_level()
            result.hierarchy_info["total_levels"] = len(domains_by_level)

            # Dictionary to track generated diagrams
            generated_diagrams = {}

            # Process each level, starting from the bottom (leaf nodes)
            for level, domains in domains_by_level.items():
                level_start_time = time.time()
                logger.info(f"Processing level {level} with {len(domains)} domains")
                result.hierarchy_info["domains_by_level"][level] = len(domains)

                # Separate leaf domains from intermediate domains
                leaf_domains = [d for d in domains if d in self.leaf_domains]
                intermediate_domains = [d for d in domains if d not in self.leaf_domains]

                logger.info(f"Level {level}: {len(leaf_domains)} leaf domains, {len(intermediate_domains)} intermediate domains")

                # Process leaf domains in parallel with non-blocking API calls
                if leaf_domains:
                    logger.info(f"Generating diagrams for {len(leaf_domains)} leaf domains in parallel")

                    # Step 1: Prepare all leaf domain API requests
                    leaf_request_preps = []
                    for domain_trace in leaf_domains:
                        functions = self.domain_traces.get(domain_trace, [])
                        leaf_request_preps.append(self._prepare_leaf_domain_request(domain_trace, functions))

                    # Wait for all request preparations to complete
                    leaf_prepared_requests = await asyncio.gather(*leaf_request_preps)

                    # Step 2: Send all leaf domain API requests without waiting for responses
                    # Create a semaphore to limit the number of concurrent API calls
                    semaphore = asyncio.Semaphore(self.max_concurrent_tasks)

                    async def send_with_semaphore(domain_trace, system_prompt, user_prompt):
                        async with semaphore:
                            return await self._send_api_request(domain_trace, system_prompt, user_prompt)

                    # Create tasks for sending all leaf domain API requests
                    leaf_send_tasks = []
                    for domain_trace, system_prompt, user_prompt in leaf_prepared_requests:
                        leaf_send_tasks.append(send_with_semaphore(domain_trace, system_prompt, user_prompt))

                    # Wait for all leaf domain API requests to be sent (but not for responses)
                    leaf_api_tasks = await asyncio.gather(*leaf_send_tasks)

                    logger.info(f"Sent {len(leaf_api_tasks)} leaf domain API requests, now waiting for and processing responses")

                    # Step 3: Process all leaf domain API responses as they come in
                    leaf_process_tasks = []
                    for domain_trace, response_task in leaf_api_tasks:
                        leaf_process_tasks.append(self._process_api_response(domain_trace, response_task, result))

                    # Wait for all leaf domain responses to be processed
                    await asyncio.gather(*leaf_process_tasks)

                    # Update generated_diagrams with the results
                    for domain_trace in leaf_domains:
                        if domain_trace in result.diagram_files:
                            generated_diagrams[domain_trace] = result.diagram_files[domain_trace]

                # Process intermediate domains in parallel with non-blocking API calls
                if intermediate_domains:
                    logger.info(f"Generating diagrams for {len(intermediate_domains)} intermediate domains in parallel")

                    # Step 1: Prepare all intermediate domain API requests
                    intermediate_request_preps = []
                    for domain_trace in intermediate_domains:
                        # Get child diagrams for this domain
                        child_diagrams = {}
                        for child in self.domain_children.get(domain_trace, set()):
                            if child in generated_diagrams:
                                child_diagrams[child] = generated_diagrams[child]

                        intermediate_request_preps.append(self._prepare_intermediate_domain_request(domain_trace, child_diagrams))

                    # Wait for all request preparations to complete
                    intermediate_prepared_requests = await asyncio.gather(*intermediate_request_preps)

                    # Step 2: Send all intermediate domain API requests without waiting for responses
                    # Create a semaphore to limit the number of concurrent API calls
                    semaphore = asyncio.Semaphore(self.max_concurrent_tasks)

                    # Define the semaphore function for intermediate domains
                    async def send_intermediate_with_semaphore(domain_trace, system_prompt, user_prompt):
                        async with semaphore:
                            return await self._send_api_request(domain_trace, system_prompt, user_prompt)

                    # Create tasks for sending all intermediate domain API requests
                    intermediate_send_tasks = []
                    for domain_trace, system_prompt, user_prompt in intermediate_prepared_requests:
                        intermediate_send_tasks.append(send_intermediate_with_semaphore(domain_trace, system_prompt, user_prompt))

                    # Wait for all intermediate domain API requests to be sent (but not for responses)
                    intermediate_api_tasks = await asyncio.gather(*intermediate_send_tasks)

                    logger.info(f"Sent {len(intermediate_api_tasks)} intermediate domain API requests, now waiting for and processing responses")

                    # Step 3: Process all intermediate domain API responses as they come in
                    intermediate_process_tasks = []
                    for domain_trace, response_task in intermediate_api_tasks:
                        intermediate_process_tasks.append(self._process_api_response(domain_trace, response_task, result))

                    # Wait for all intermediate domain responses to be processed
                    await asyncio.gather(*intermediate_process_tasks)

                    # Update generated_diagrams with the results
                    for domain_trace in intermediate_domains:
                        if domain_trace in result.diagram_files:
                            generated_diagrams[domain_trace] = result.diagram_files[domain_trace]

                # Calculate and log level processing time
                level_end_time = time.time()
                level_processing_time = level_end_time - level_start_time
                result.hierarchy_info["processing_time_by_level"][level] = level_processing_time
                logger.info(f"Completed level {level} in {level_processing_time:.2f} seconds")

            # Generate a top-level diagram for the entire codebase
            try:
                # Get all top-level domains
                top_level_domains = [d for d in self.all_domains if self.domain_levels.get(d, 0) == 0]
                top_level_diagrams = {}

                for domain in top_level_domains:
                    if domain in generated_diagrams:
                        top_level_diagrams[domain] = generated_diagrams[domain]

                if top_level_diagrams:
                    logger.info(f"Generating top-level codebase overview diagram combining {len(top_level_diagrams)} top-level domains")

                    # Prepare the request for the top-level diagram
                    overview_domain_trace = "Codebase Overview"
                    overview_request = await self._prepare_intermediate_domain_request(overview_domain_trace, top_level_diagrams)

                    # Send the request
                    overview_domain_trace, system_prompt, user_prompt = overview_request
                    overview_domain_trace, overview_task = await self._send_api_request(overview_domain_trace, system_prompt, user_prompt)

                    # Process the response
                    await self._process_api_response(overview_domain_trace, overview_task, result)

                    logger.info(f"Generated top-level codebase overview diagram")

            except Exception as e:
                logger.error(f"Error generating top-level diagram: {e}")
                import traceback
                logger.error(traceback.format_exc())

            # Now generate combined diagrams, starting from the bottom up
            # This is a simplified approach - in a real implementation, we would
            # traverse the hierarchy and combine diagrams at each level

            # For now, just combine all top-level domains into one diagram
            top_domains = {}
            for domain_trace in result.diagram_files:
                # Get the top-level domain
                top_domain = domain_trace.split(' -> ')[0]
                if top_domain not in top_domains:
                    top_domains[top_domain] = {}

                # Add this diagram to the top domain
                top_domains[top_domain][domain_trace] = result.diagram_files[domain_trace]

            # Generate a combined diagram for each top-level domain
            for top_domain, subdomains in top_domains.items():
                try:
                    combined_diagram, raw_response = await self.generate_combined_diagram(
                        top_domain,
                        subdomains,
                        level=1
                    )

                    # Use standardized naming for the combined domain diagram
                    standardized_name = f"L1_{top_domain.replace(' ', '_').replace(',', '').replace('&', 'and')}_Combined"

                    # Save the diagram to the output directory
                    diagram_path = os.path.join(self.diagrams_dir, f"{standardized_name}.md")
                    with open(diagram_path, 'w') as f:
                        f.write(combined_diagram)

                    # Also store the mapping from standardized name to domain trace
                    if not hasattr(self, 'diagram_name_mapping'):
                        self.diagram_name_mapping = {}
                    self.diagram_name_mapping[standardized_name] = f"{top_domain} (combined)"

                    # Save raw response
                    raw_path = os.path.join(self.diagrams_dir, f"{standardized_name}_raw.txt")
                    with open(raw_path, 'w') as f:
                        f.write(raw_response)

                    # Add to result
                    result.diagram_files[f"{top_domain} (combined)"] = diagram_path
                    result.raw_responses[f"{top_domain} (combined)"] = raw_response

                    logger.info(f"Generated combined diagram for {top_domain}: {diagram_path}")

                except Exception as e:
                    logger.error(f"Error generating combined diagram for {top_domain}: {e}")

            # Finally, generate a top-level diagram for the entire codebase
            try:
                top_level_diagrams = {
                    domain: result.diagram_files[f"{domain} (combined)"]
                    for domain in top_domains
                    if f"{domain} (combined)" in result.diagram_files
                }

                if top_level_diagrams:
                    top_diagram, raw_response = await self.generate_combined_diagram(
                        "Codebase Overview",
                        top_level_diagrams,
                        level=2
                    )

                    # Use standardized naming for the codebase overview diagram
                    standardized_name = "L0_Codebase_Overview"

                    # Save the diagram to the output directory
                    diagram_path = os.path.join(self.diagrams_dir, f"{standardized_name}.md")
                    with open(diagram_path, 'w') as f:
                        f.write(top_diagram)

                    # Also store the mapping from standardized name to domain trace
                    if not hasattr(self, 'diagram_name_mapping'):
                        self.diagram_name_mapping = {}
                    self.diagram_name_mapping[standardized_name] = "Codebase Overview"

                    # Save raw response
                    raw_path = os.path.join(self.diagrams_dir, f"{standardized_name}_raw.txt")
                    with open(raw_path, 'w') as f:
                        f.write(raw_response)

                    # Add to result
                    result.diagram_files["Codebase Overview"] = diagram_path
                    result.raw_responses["Codebase Overview"] = raw_response

                    logger.info(f"Generated top-level diagram: {diagram_path}")

            except Exception as e:
                logger.error(f"Error generating top-level diagram: {e}")

            # After all diagrams are generated, print the summary of missing functions
            if self.total_functions_missing > 0:
                logger.warning(f"FUNCTION LOOKUP SUMMARY:")
                logger.warning(f"Total functions requested: {self.total_functions_requested}")
                logger.warning(f"Total functions missing: {self.total_functions_missing} ({(self.total_functions_missing / self.total_functions_requested * 100):.2f}%)")
                logger.warning(f"Domains with missing functions:")

                # Sort domains by number of missing functions (descending)
                sorted_domains = sorted(self.missing_function_counts.items(),
                                       key=lambda x: x[1], reverse=True)

                for domain, count in sorted_domains:
                    logger.warning(f"  - {domain}: {count} missing functions")
            else:
                logger.info(f"FUNCTION LOOKUP SUMMARY: All {self.total_functions_requested} functions were found successfully!")

            logger.info(f"Generated {len(result.diagram_files)} diagrams in total")

            # Save the diagram name mapping to a JSON file
            if hasattr(self, 'diagram_name_mapping') and self.diagram_name_mapping:
                mapping_path = os.path.join(self.output_dir, "diagram_name_mapping.json")
                try:
                    with open(mapping_path, 'w') as f:
                        json.dump(self.diagram_name_mapping, f, indent=2)
                    logger.info(f"Saved diagram name mapping to {mapping_path}")

                    # Add the mapping path to the result
                    result.hierarchy_info["diagram_name_mapping_path"] = mapping_path
                except Exception as e:
                    logger.error(f"Error saving diagram name mapping: {e}")

        except Exception as e:
            logger.error(f"Error generating diagrams: {e}")
            result.success = False
            result.error_message = str(e)

        return result


async def main():
    """Main entry point for the domain diagram generator."""
    import argparse

    parser = argparse.ArgumentParser(description="Generate mermaid diagrams from domain traces")
    # parser.add_argument("--domain-traces", required=True, help="Path to the domain traces YAML file")
    # parser.add_argument("--functions-parquet", required=True, help="Path to the semantic_documented_functions.parquet file")
    # parser.add_argument("--output-dir", required=True, help="Directory to save generated diagrams")

    domain_traces = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/domain_traces.yaml"
    functions_parquet = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/semantic_documented_functions.parquet"
    output_dir = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/*********************"

    # domain_traces = "/Users/<USER>/work/startup/godzilla/bracket/bracket/acquire_outputs/mem0-bracket-improved/domain_traces.yaml"
    # functions_parquet = "/Users/<USER>/work/startup/godzilla/bracket/bracket/acquire_outputs/mem0-bracket-improved/semantic_documented_functions.parquet"
    # output_dir = "/Users/<USER>/work/startup/godzilla/bracket/bracket/acquire_outputs/mem0-bracket-improved/domain_diagrams/hierarchical_domain_diagrams"


    # Diagram generation approach
    parser.add_argument("--approach", default="hierarchical", choices=["simplified", "hierarchical"],
                        help="Approach to use for diagram generation (simplified or hierarchical)")
    parser.add_argument("--max-hierarchy-depth", type=int, default=None,
                        help="Maximum hierarchy depth to process (None for all levels)")
    parser.add_argument("--cache-dir", default=None,
                        help="Directory to cache intermediate results (None for no caching)")

    # Model selection
    parser.add_argument("--model-type", default="openai", choices=["claude", "openai"],
                        help="Type of model to use (claude or openai)")

    # Claude parameters
    parser.add_argument("--claude-api-key", help="Anthropic API key (if not provided, will try to get from environment)")
    parser.add_argument("--claude-model", default="claude-3-5-sonnet-20241022", help="Claude model to use")

    # OpenAI parameters
    parser.add_argument("--openai-api-key", default="********************************************************************************************************************************************************************", help="OpenAI API key (if not provided, will try to get from environment)")
    # parser.add_argument("--openai-model", default="gpt-4o-mini", help="OpenAI model to use")
    parser.add_argument("--openai-model", default="o3-mini", help="OpenAI model to use")

    # Common parameters
    parser.add_argument("--max-tokens", type=int, default=8000, help="Maximum tokens to generate")
    parser.add_argument("--temperature", type=float, default=0.5, help="Sampling temperature")
    parser.add_argument("--requests-per-minute", type=float, default=1000, help="Rate limit for API requests")
    parser.add_argument("--tokens-per-minute", type=float, default=5000000, help="Token rate limit for API requests")

    # Parallelization parameters
    parser.add_argument("--max-concurrent-tasks", type=int, default=10,
                        help="Maximum number of concurrent tasks for parallel processing")
    parser.add_argument("--max-concurrent-tasks-per-level", type=int, default=8,
                        help="Maximum number of concurrent tasks per hierarchy level (None for same as max-concurrent-tasks)")

    args = parser.parse_args()

    try:
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Create cache directory if specified
        if args.cache_dir:
            os.makedirs(args.cache_dir, exist_ok=True)

        # Create a domain diagram generator
        generator = DomainDiagramGenerator(
            domain_traces_yaml_path=domain_traces,
            functions_parquet_path=functions_parquet,
            output_dir=output_dir,
            model_type=args.model_type,
            # Claude parameters
            claude_api_key=args.claude_api_key,
            claude_model=args.claude_model,
            # OpenAI parameters
            openai_api_key=args.openai_api_key,
            openai_model=args.openai_model,
            # Common parameters
            max_tokens=args.max_tokens,
            temperature=args.temperature,
            max_requests_per_minute=args.requests_per_minute,
            max_tokens_per_minute=args.tokens_per_minute,
            # Parallelization parameters
            max_concurrent_tasks=args.max_concurrent_tasks,
        )

        # Generate all diagrams
        if args.approach == "hierarchical":
            logger.info("Using hierarchical approach for diagram generation")
            result = await generator.generate_all_diagrams()
        else:
            logger.info("Using simplified approach for diagram generation")
            # Rename the current method to simplified for backward compatibility
            result = await generator.generate_all_diagrams()

        if result.success:
            logger.info("Domain diagram generation completed successfully")
            logger.info(f"Generated {len(result.diagram_files)} diagrams")

            # Log hierarchy information if available
            if hasattr(result, 'hierarchy_info') and result.hierarchy_info:
                logger.info("Hierarchy Information:")
                logger.info(f"  Total Levels: {result.hierarchy_info.get('total_levels', 0)}")
                logger.info("  Domains by Level:")
                for level, count in result.hierarchy_info.get('domains_by_level', {}).items():
                    logger.info(f"    Level {level}: {count} domains")
                logger.info("  Processing Time by Level:")
                for level, time_taken in result.hierarchy_info.get('processing_time_by_level', {}).items():
                    logger.info(f"    Level {level}: {time_taken:.2f} seconds")

            # Log individual diagrams
            logger.info("Generated Diagrams:")
            for domain, file_path in result.diagram_files.items():
                logger.info(f"  - {domain}: {file_path}")

            return 0
        else:
            logger.error(f"Domain diagram generation failed: {result.error_message}")
            return 1

    except Exception as e:
        logger.error(f"Error in domain diagram generation: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

# 6.91 -> 6.96 -> $7.01
# 7.21 -> 7.82
# gpt-4o: 8.13 -> 9.95 = $1.82
if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
