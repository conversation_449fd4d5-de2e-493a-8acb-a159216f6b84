"""
Enhanced Domain Trace Builder for Codebase

This module provides functionality to:
1. Read domain output YAML and build traces from top to bottom (domain -> sub-area -> sub-area...)
2. Read semantic_documented_functions.parquet to get function signatures and descriptions
3. Read domain-to-file mappings to reduce the search space for each trace
4. Use an LLM to classify functions into domain traces with reduced search space

It can be used as:
1. A standalone script to process domain YAML and function parquet files
2. A module that can be integrated into the repository analysis flow
"""

import os
import yaml
import logging
import argparse
import asyncio
import aiohttp
import pandas as pd
import re
from typing import Dict, List, Any, Optional, Set, Tuple
from collections import defaultdict

# Import the original DomainTraceBuilder for compatibility
from bracket_core.domain_trace_builder import DomainTraceBuilder, DomainTrace, FunctionClassificationResult

# Import API key management
from bracket_core.llm.api_keys import get_openai_api_key

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedDomainTraceBuilder(DomainTraceBuilder):
    """
    Enhanced version of DomainTraceBuilder that uses domain-specific file sets to reduce search space.
    """

    def __init__(
        self,
        domain_yaml_path: str,
        functions_parquet_path: str,
        output_path: str,
        domain_file_mappings_path: str,
        api_key: Optional[str] = None,
        model: str = "gpt-4o-mini",
        max_requests_per_minute: float = 2000,
        max_tokens_per_minute: float = 1000000,
        temperature: float = 0.0,
        fallback_to_original: bool = True,
    ):
        """
        Initialize the enhanced domain trace builder.

        Args:
            domain_yaml_path: Path to the domain YAML file
            functions_parquet_path: Path to the semantic_documented_functions.parquet file
            output_path: Path to save the classification output
            domain_file_mappings_path: Path to the domain-to-file mappings YAML file
            api_key: API key (if None, will try to get from environment)
            model: OpenAI model to use
            max_requests_per_minute: Rate limit for API requests
            max_tokens_per_minute: Token rate limit for API
            temperature: Temperature for LLM generation
            fallback_to_original: Whether to fall back to the original approach if domain-file mappings are not available
        """
        super().__init__(
            domain_analysis_yaml_path=domain_yaml_path,
            functions_parquet_path=functions_parquet_path,
            output_path=output_path,
            api_key=api_key,
            model=model,
            max_requests_per_minute=max_requests_per_minute,
            max_tokens_per_minute=max_tokens_per_minute,
            temperature=temperature,
        )
        self.domain_file_mappings_path = domain_file_mappings_path
        self.fallback_to_original = fallback_to_original
        self.domain_file_mappings = {}
        self.trace_to_top_domain = {}
        self.file_to_functions = defaultdict(list)
        self.normalized_file_paths = {}  # Map from normalized paths to original paths

    def read_domain_file_mappings(self) -> Dict[str, List[str]]:
        """
        Read the domain-to-file mappings YAML file.

        Returns:
            Dictionary mapping domain names to lists of file paths
        """
        logger.info(f"Reading domain-to-file mappings YAML file: {self.domain_file_mappings_path}")
        try:
            with open(self.domain_file_mappings_path, 'r') as f:
                mappings_raw = yaml.safe_load(f)

            # Normalize file paths in mappings
            mappings = {}
            for domain, file_paths in mappings_raw.items():
                mappings[domain] = [self._normalize_file_path(path) for path in file_paths]

            logger.info(f"Read domain-to-file mappings for {len(mappings)} domains")
            return mappings
        except Exception as e:
            logger.error(f"Error reading domain-to-file mappings YAML file: {e}")
            if self.fallback_to_original:
                logger.warning("Falling back to original approach without domain-file mappings")
                return {}
            else:
                raise

    def map_traces_to_top_domains(self, traces: List[DomainTrace]) -> Dict[str, str]:
        """
        Map each trace to its top-level domain.

        Args:
            traces: List of DomainTrace objects

        Returns:
            Dictionary mapping trace strings to top-level domain names
        """
        logger.info("Mapping traces to top-level domains")

        trace_to_top_domain = {}

        for trace in traces:
            if trace.trace:
                top_domain = trace.trace[0]
                trace_to_top_domain[trace.trace_str] = top_domain

        logger.info(f"Mapped {len(trace_to_top_domain)} traces to top-level domains")
        return trace_to_top_domain

    def _normalize_file_path(self, file_path: str) -> str:
        """
        Normalize a file path to extract the unique part for matching.

        This function extracts the most specific part of the path that would be unique
        across different representations (absolute vs relative paths).

        Args:
            file_path: The file path to normalize

        Returns:
            Normalized file path for matching
        """
        # Handle empty paths
        if not file_path:
            return file_path

        # Store the original path
        original_path = file_path

        # Remove any leading path components like '../' or './'
        clean_path = re.sub(r'^(\.\./)+', '', file_path)
        clean_path = re.sub(r'^\./+', '', clean_path)

        # For absolute paths, create multiple normalized versions
        if file_path.startswith('/'):
            # Split the path into components
            path_parts = file_path.split('/')
            # Remove empty parts
            path_parts = [p for p in path_parts if p]

            # Create multiple normalized versions with different levels of specificity
            # This increases our chances of finding a match

            # Version 1: Last 3 components (or all if less than 3)
            significant_parts = path_parts[-min(3, len(path_parts)):]
            clean_path_v1 = '/'.join(significant_parts)
            self.normalized_file_paths[clean_path_v1] = original_path

            # Version 2: Last 2 components (or all if less than 2)
            significant_parts = path_parts[-min(2, len(path_parts)):]
            clean_path_v2 = '/'.join(significant_parts)
            self.normalized_file_paths[clean_path_v2] = original_path

            # Version 3: Just the filename
            if len(path_parts) > 0:
                clean_path_v3 = path_parts[-1]
                self.normalized_file_paths[clean_path_v3] = original_path

            # Use the most specific version as the return value
            clean_path = clean_path_v1

            # Also look for common project directories and create versions based on those
            for marker in ['adjacent', 'mem0', 'bracket', 'godzilla']:
                marker_pattern = f'/{marker}/'
                if marker_pattern in file_path:
                    # Extract everything after the marker
                    marker_idx = file_path.find(marker_pattern) + 1  # +1 to include the leading slash
                    marker_path = file_path[marker_idx:]
                    self.normalized_file_paths[marker_path] = original_path

                    # Also store without the leading slash
                    if marker_path.startswith('/'):
                        self.normalized_file_paths[marker_path[1:]] = original_path

        # For relative paths with '../', create a version without those
        if '../' in file_path:
            # Count the number of '../' at the beginning
            parts = file_path.split('/')
            non_dotdot_idx = 0
            for i, part in enumerate(parts):
                if part != '..':
                    non_dotdot_idx = i
                    break

            # Extract the part after all the '../'
            if non_dotdot_idx < len(parts):
                clean_rel_path = '/'.join(parts[non_dotdot_idx:])
                self.normalized_file_paths[clean_rel_path] = original_path

        # Store the mapping from normalized path to original path
        self.normalized_file_paths[clean_path] = original_path

        # Also store mappings for just the filename and filename with parent dir
        # This helps with matching when paths are very different
        filename = os.path.basename(file_path)
        if filename:
            self.normalized_file_paths[filename] = original_path

            # Also store parent dir + filename
            parent_dir = os.path.basename(os.path.dirname(file_path))
            if parent_dir:
                parent_and_file = f"{parent_dir}/{filename}"
                self.normalized_file_paths[parent_and_file] = original_path

                # Also try with different separators
                self.normalized_file_paths[f"{parent_dir}\\{filename}"] = original_path

        # Add debug logging
        logger.debug(f"Normalized path: '{file_path}' -> '{clean_path}'")

        return clean_path

    def map_functions_to_files(self, df: pd.DataFrame) -> Dict[str, List[str]]:
        """
        Map functions to their containing files.

        Args:
            df: DataFrame containing function data

        Returns:
            Dictionary mapping file paths to lists of function signatures
        """
        logger.info("Mapping functions to files")

        file_to_functions = defaultdict(list)
        unique_file_paths = set()

        for _, row in df.iterrows():
            file_path = row.get('file_path', '')
            if not file_path or pd.isna(file_path):
                continue

            unique_file_paths.add(file_path)

            # Normalize the file path for consistent matching
            normalized_path = self._normalize_file_path(file_path)

            # Extract signature and function name
            signature = ''
            function_name = ''

            if 'signature' in row and isinstance(row['signature'], dict):
                sig_dict = row['signature']
                name = sig_dict.get('name', '')
                params = sig_dict.get('params', [])
                return_type = sig_dict.get('return_type', '')

                function_name = name
                param_str = ', '.join([f"{p.get('name', '')}: {p.get('type', '')}" for p in params])
                signature = f"{name}({param_str})"
                if return_type:
                    signature += f" -> {return_type}"

            # Use node_id as fallback if no signature
            if not signature and 'node_id' in row:
                signature = row['node_id']
                function_name = signature.split('(')[0].strip() if '(' in signature else signature

            # Use function name if available, otherwise use signature
            if function_name:
                # Store function in both original and normalized paths
                file_to_functions[file_path].append(function_name)
                file_to_functions[normalized_path].append(function_name)
            elif signature:
                # Store function in both original and normalized paths
                file_to_functions[file_path].append(signature)
                file_to_functions[normalized_path].append(signature)

        # Create additional normalized versions of all file paths
        # This helps with matching when paths are in very different formats
        for file_path in list(unique_file_paths):  # Use list() to avoid modifying during iteration
            # Extract just the filename
            filename = os.path.basename(file_path)
            if filename:
                # Create a mapping from filename to file path
                if filename not in file_to_functions and file_path in file_to_functions:
                    file_to_functions[filename] = file_to_functions[file_path].copy()

                # Also try with parent directory
                parent_dir = os.path.basename(os.path.dirname(file_path))
                if parent_dir:
                    parent_and_file = f"{parent_dir}/{filename}"
                    if parent_and_file not in file_to_functions and file_path in file_to_functions:
                        file_to_functions[parent_and_file] = file_to_functions[file_path].copy()

        logger.info(f"Mapped functions to {len(file_to_functions)} files from {len(unique_file_paths)} unique file paths")

        # Log some sample mappings for debugging
        sample_keys = list(file_to_functions.keys())[:5]
        logger.info(f"Sample file paths: {sample_keys}")

        return file_to_functions

    def get_domain_specific_functions(self, trace: DomainTrace) -> Set[str]:
        """
        Get the set of functions that belong to the domain of the given trace.

        Args:
            trace: DomainTrace object

        Returns:
            Set of function signatures that belong to the domain
        """
        # Get the top-level domain for this trace
        top_domain = self.trace_to_top_domain.get(trace.trace_str, '')
        if not top_domain:
            logger.warning(f"No top-level domain found for trace: {trace.trace_str}")
            return set()

        # Get the files for this domain
        domain_files = self.domain_file_mappings.get(top_domain, [])
        if not domain_files:
            logger.warning(f"No files found for domain: {top_domain}")
            return set()

        # Get the functions for these files
        domain_functions = set()
        matched_files = 0
        total_files = len(domain_files)

        # Add debug logging
        logger.info(f"Looking for functions in {total_files} files for domain: {top_domain}")

        for file_path in domain_files:
            # Try to get functions directly
            functions = self.file_to_functions.get(file_path, [])
            if functions:
                domain_functions.update(functions)
                matched_files += 1
                logger.debug(f"Found {len(functions)} functions for file: {file_path}")
                continue

            # If no functions found, try with the original path
            original_path = self.normalized_file_paths.get(file_path, '')
            if original_path and original_path in self.file_to_functions:
                functions = self.file_to_functions.get(original_path, [])
                domain_functions.update(functions)
                matched_files += 1
                logger.debug(f"Found {len(functions)} functions for original path: {original_path}")
                continue

            # If still no match, try a more aggressive approach - look for any path that contains
            # a significant portion of this path
            filename = os.path.basename(file_path)
            if filename:
                # Look for any path in file_to_functions that ends with this filename
                for func_file_path in self.file_to_functions.keys():
                    if func_file_path.endswith(f"/{filename}") or func_file_path.endswith(f"\\{filename}"):
                        functions = self.file_to_functions.get(func_file_path, [])
                        domain_functions.update(functions)
                        matched_files += 1
                        logger.debug(f"Found {len(functions)} functions by filename match: {func_file_path}")
                        break

        logger.info(f"Found {len(domain_functions)} functions in {matched_files}/{total_files} files for domain: {top_domain}")

        # If no functions were found, log the file paths to help with debugging
        if len(domain_functions) == 0:
            logger.warning(f"No functions found for domain: {top_domain}. File paths: {domain_files}")
            logger.warning(f"Available file paths in function data: {list(self.file_to_functions.keys())[:5]}...")

        return domain_functions

    def prepare_domain_specific_function_data(self, df: pd.DataFrame, domain_functions: Set[str]) -> Dict[str, Any]:
        """
        Prepare function data for LLM classification, filtered by domain.

        Args:
            df: DataFrame containing function data
            domain_functions: Set of function signatures that belong to the domain

        Returns:
            Dictionary containing prepared function data
        """
        logger.info(f"Preparing domain-specific function data with {len(domain_functions)} functions")

        function_data = {}

        # Extract function signatures and descriptions
        for _, row in df.iterrows():
            # Extract signature
            signature = ''
            if 'signature' in row and isinstance(row['signature'], dict):
                sig_dict = row['signature']
                name = sig_dict.get('name', '')
                params = sig_dict.get('params', [])
                return_type = sig_dict.get('return_type', '')

                param_str = ', '.join([f"{p.get('name', '')}: {p.get('type', '')}" for p in params])
                signature = f"{name}({param_str})"
                if return_type:
                    signature += f" -> {return_type}"

            # Use node_id as fallback if no signature
            if not signature and 'node_id' in row:
                signature = row['node_id']

            # Skip if not in domain functions
            if signature not in domain_functions:
                continue

            description = row.get('description', '')

            # Skip functions without descriptions
            if not description or description.strip() == '':
                continue

            function_data[signature] = description

        return function_data

    async def classify_functions_with_reduced_search_space(
        self,
        traces: List[DomainTrace],
        df: pd.DataFrame
    ) -> FunctionClassificationResult:
        """
        Classify functions into domain traces using LLM with reduced search space.

        Args:
            traces: List of DomainTrace objects
            df: DataFrame containing function data

        Returns:
            FunctionClassificationResult containing the classification results
        """
        logger.info("Classifying functions into domain traces with reduced search space")

        # Initialize result
        result = FunctionClassificationResult()

        # Process each trace separately
        for trace in traces:
            # Get domain-specific functions
            domain_functions = self.get_domain_specific_functions(trace)

            if not domain_functions:
                logger.warning(f"No domain-specific functions found for trace: {trace.trace_str}")
                result.classifications[trace.trace_str] = []
                continue

            # Prepare domain-specific function data
            function_data = self.prepare_domain_specific_function_data(df, domain_functions)

            if not function_data:
                logger.warning(f"No function data prepared for trace: {trace.trace_str}")
                result.classifications[trace.trace_str] = []
                continue

            # Prepare function signatures and descriptions for LLM
            full_signature_set = "\n".join([f"{sig} - {desc}" for sig, desc in function_data.items()])

            # Create the API request
            request_json = {
                "model": self.model,
                "messages": [
                    {
                        "role": "system",
                        "content": """You are an expert software architect analyzing a codebase.
Your task is to identify which functions belong to a specific domain trace.
Analyze each function signature and description carefully, and determine if it belongs to the given domain trace.
Return ONLY a list of function signatures that belong to the domain trace, one per line.
Do not include any explanations, headers, or other text in your response.
If no functions belong to the domain trace, return "No functions found".
"""
                    },
                    {
                        "role": "user",
                        "content": f"""Domain trace: {trace.trace_str}

Here is the list of function signatures and their descriptions from the relevant domain:

{full_signature_set}

Please list ONLY the function signatures (without descriptions) that belong to the domain trace "{trace.trace_str}".
Return one function signature per line, with no additional text.
"""
                    }
                ],
                "temperature": self.temperature,
                "max_tokens": 4000
            }

            # Call the OpenAI API
            logger.info(f"Calling OpenAI API for trace: {trace.trace_str}")
            response_json, error = await self._call_openai_api(request_json)

            if error:
                logger.error(f"API error for trace {trace.trace_str}: {error}")
                result.success = False
                result.error_message = f"API error for trace {trace.trace_str}: {error}"
                result.classifications[trace.trace_str] = []
                continue

            # Extract the response content
            content = response_json["choices"][0]["message"]["content"]
            result.raw_responses[trace.trace_str] = content

            # Parse the response to get the list of functions
            functions = []
            if content and content.strip() != "No functions found":
                functions = [line.strip() for line in content.strip().split('\n') if line.strip()]

            result.classifications[trace.trace_str] = functions
            logger.info(f"Found {len(functions)} functions for trace: {trace.trace_str}")

        return result

    async def _call_openai_api(self, request_json: Dict[str, Any]) -> Tuple[Dict[str, Any], Optional[str]]:
        """
        Call the OpenAI API with rate limiting and error handling.

        Args:
            request_json: API request payload

        Returns:
            Tuple of (response_json, error_message)
        """
        max_retries = 3
        retry_delay = 5  # seconds

        # Set up API key
        request_header = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }

        # Set up API endpoint
        request_url = "https://api.openai.com/v1/chat/completions"

        for attempt in range(max_retries):
            try:
                # Make the API request
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        request_url,
                        headers=request_header,
                        json=request_json
                    ) as response:
                        response_json = await response.json()

                        if response.status == 200:
                            return response_json, None
                        elif response.status == 429:
                            # Rate limit error
                            logger.warning(f"Rate limit error (attempt {attempt + 1}/{max_retries})")

                            # Exponential backoff
                            await asyncio.sleep(retry_delay * (2 ** attempt))
                        else:
                            # Other API error
                            error_message = f"API error: {response.status} - {response_json}"
                            logger.error(f"{error_message} (attempt {attempt + 1}/{max_retries})")

                            if attempt < max_retries - 1:
                                await asyncio.sleep(retry_delay)
                            else:
                                return {}, error_message

            except Exception as e:
                logger.error(f"Request error: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay)
                    retry_delay = min(retry_delay * 2, 60)
                else:
                    return {}, f"Request failed after {max_retries} attempts: {str(e)}"

        return {}, f"Failed to get response after {max_retries} attempts"

    async def build_and_classify(self) -> FunctionClassificationResult:
        """
        Build domain traces and classify functions with reduced search space.

        Returns:
            FunctionClassificationResult containing the classification results
        """
        # Read the domain YAML
        domain_data = self.read_domain_analysis_yaml()

        # Build domain traces
        traces = self.build_domain_traces(domain_data)
        logger.info(f"Built {len(traces)} domain traces")

        # Read the functions parquet
        df = self.read_functions_parquet()

        # Try to read domain-file mappings
        self.domain_file_mappings = self.read_domain_file_mappings()

        # If domain-file mappings are available, use the enhanced approach
        if self.domain_file_mappings:
            logger.info("Using enhanced approach with domain-file mappings")

            # Map traces to top domains
            self.trace_to_top_domain = self.map_traces_to_top_domains(traces)

            # Map functions to files
            self.file_to_functions = self.map_functions_to_files(df)

            # Classify functions with reduced search space
            result = await self.classify_functions_with_reduced_search_space(traces, df)
        else:
            # Fall back to original approach
            logger.info("Falling back to original approach without domain-file mappings")

            # Prepare function data
            function_data = self.prepare_function_data(df)
            logger.info(f"Prepared {len(function_data)} functions for classification")

            # Classify functions
            result = await self.classify_functions(traces, function_data)

        # Save the results
        if result.success:
            self.save_classification_results(result)

        return result

class EnhancedDomainTraceBuilderIntegration:
    """
    Integration class for adding enhanced domain trace building to the repository analysis flow.

    This class provides methods that can be called from the repository analysis flow
    to add enhanced domain trace building as an additional step.
    """

    @staticmethod
    async def build_domain_traces(
        domain_yaml_path: str,
        functions_parquet_path: str,
        output_path: str,
        domain_file_mappings_path: str,
        api_key: Optional[str] = None,
        model: str = "gpt-4o-mini",
        max_requests_per_minute: float = 50,
        max_tokens_per_minute: float = 100000,
        fallback_to_original: bool = True,
    ) -> bool:
        """
        Build domain traces and classify functions with reduced search space.

        Args:
            domain_yaml_path: Path to the domain YAML file
            functions_parquet_path: Path to the semantic_documented_functions.parquet file
            output_path: Path to save the classification output
            domain_file_mappings_path: Path to the domain-to-file mappings YAML file
            api_key: OpenAI API key (if None, will try to get from environment)
            model: OpenAI model to use
            max_requests_per_minute: Rate limit for API requests
            max_tokens_per_minute: Token rate limit for API
            fallback_to_original: Whether to fall back to the original approach if domain-file mappings are not available

        Returns:
            True if trace building was successful, False otherwise
        """
        try:
            builder = EnhancedDomainTraceBuilder(
                domain_yaml_path=domain_yaml_path,
                functions_parquet_path=functions_parquet_path,
                output_path=output_path,
                domain_file_mappings_path=domain_file_mappings_path,
                api_key=api_key,
                model=model,
                max_requests_per_minute=max_requests_per_minute,
                max_tokens_per_minute=max_tokens_per_minute,
                fallback_to_original=fallback_to_original,
            )

            result = await builder.build_and_classify()
            return result.success

        except Exception as e:
            logger.error(f"Error in enhanced domain trace building: {e}")
            return False

async def main():
    """Main entry point for the enhanced domain trace builder."""
    parser = argparse.ArgumentParser(description="Build domain traces and classify functions with reduced search space")
    parser.add_argument("--domain-yaml", required=True, help="Path to the domain YAML file")
    parser.add_argument("--functions-parquet", required=True, help="Path to the semantic_documented_functions.parquet file")
    parser.add_argument("--output", required=True, help="Path to save the classification output")
    parser.add_argument("--domain-file-mappings", required=True, help="Path to the domain-to-file mappings YAML file")
    parser.add_argument("--api-key", help="OpenAI API key (if not provided, will try to get from environment)")
    parser.add_argument("--model", default="gpt-4o-mini", help="OpenAI model to use")
    parser.add_argument("--requests-per-minute", type=float, default=50, help="Rate limit for API requests")
    parser.add_argument("--tokens-per-minute", type=float, default=100000, help="Token rate limit for API")
    parser.add_argument("--temperature", type=float, default=0.0, help="Temperature for LLM generation")
    parser.add_argument("--no-fallback", action="store_true", help="Disable fallback to original approach")

    args = parser.parse_args()

    try:
        builder = EnhancedDomainTraceBuilder(
            domain_yaml_path=args.domain_yaml,
            functions_parquet_path=args.functions_parquet,
            output_path=args.output,
            domain_file_mappings_path=args.domain_file_mappings,
            api_key=args.api_key,
            model=args.model,
            max_requests_per_minute=args.requests_per_minute,
            max_tokens_per_minute=args.tokens_per_minute,
            temperature=args.temperature,
            fallback_to_original=not args.no_fallback,
        )

        result = await builder.build_and_classify()

        if result.success:
            logger.info("Enhanced domain trace building and function classification completed successfully")
            return 0
        else:
            logger.error(f"Enhanced domain trace building and function classification failed: {result.error_message}")
            return 1

    except Exception as e:
        logger.error(f"Error in enhanced domain trace building and function classification: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
