# type: ignore
import logging
import math
import os
import re
import warnings
import hashlib
from collections import Counter, defaultdict, namedtuple
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional, Any, Union

import pandas as pd
import networkx as nx
from grep_ast import TreeContext, filename_to_lang
from pygments.lexers import guess_lexer_for_filename
from pygments.token import Token
from pygments.util import ClassNotFound
from tqdm import tqdm
from tree_sitter_languages import get_language, get_parser


# tree_sitter is throwing a FutureWarning
warnings.simplefilter("ignore", category=FutureWarning)

# Enhanced Tag namedtuple with additional fields for reference resolution
Tag = namedtuple(
    "Tag", 
    "rel_fname fname line end_line name kind type signature scope parent_scope "
    "imports module_path qualified_name symbol_id context_hash containing_class "
    "context"  # Add the context field
)

logger = logging.getLogger(__name__)


class RelationshipType:
    """Constants for relationship types and their base weights"""
    CONTAINS = "CONTAINS"  # Base weight: 1.0
    REFERENCES = "REFERENCES"  # Base weight: 0.5
    INHERITS = "INHERITS"  # Base weight: 0.8
    IMPLEMENTS = "IMPLEMENTS"  # Base weight: 0.7
    CALLS = "CALLS"  # Base weight: 0.6
    OVERRIDES = "OVERRIDES"  # Base weight: 0.7
    DEPENDS_ON = "DEPENDS_ON"  # Base weight: 0.4
    ASSOCIATES = "ASSOCIATES"  # Base weight: 0.3
    
    @staticmethod
    def get_base_weight(rel_type: str) -> float:
        weights = {
            RelationshipType.CONTAINS: 0.3,
            RelationshipType.REFERENCES: 0.5,
            RelationshipType.INHERITS: 0.8,
            RelationshipType.IMPLEMENTS: 0.7,
            RelationshipType.CALLS: 1.0,
            RelationshipType.OVERRIDES: 0.7,
            RelationshipType.DEPENDS_ON: 0.4,
            RelationshipType.ASSOCIATES: 0.3
        }
        return weights.get(rel_type, 0.5)



class SymbolDefinition:
    """
    Represents a symbol definition with all necessary context for accurate reference resolution.
    """
    def __init__(
        self, 
        name: str, 
        qualified_name: str,
        file_path: str, 
        module_path: str,
        line: int,
        end_line: int,
        symbol_type: str,
        scope: str, 
        signature: str = "",
        containing_class: str = "",
        symbol_id: str = ""
    ):
        self.name = name
        self.qualified_name = qualified_name
        self.file_path = file_path
        self.module_path = module_path
        self.line = line
        self.end_line = end_line
        self.symbol_type = symbol_type
        self.scope = scope
        self.signature = signature
        self.containing_class = containing_class
        
        # Generate a unique ID if not provided
        if not symbol_id:
            self.symbol_id = self._generate_id()
        else:
            self.symbol_id = symbol_id
            
    def _generate_id(self) -> str:
        """Generate a unique ID for this symbol based on its properties."""
        components = [
            self.name,
            self.qualified_name,
            self.file_path,
            self.module_path,
            str(self.line),
            self.symbol_type,
            self.scope,
            self.signature
        ]
        unique_str = "|".join(components)
        return hashlib.sha256(unique_str.encode()).hexdigest()[:16]
    
    def __eq__(self, other):
        if not isinstance(other, SymbolDefinition):
            return False
        return self.symbol_id == other.symbol_id
    
    def __hash__(self):
        return hash(self.symbol_id)


class SymbolReference:
    """
    Represents a reference to a symbol with context for accurate resolution.
    """
    def __init__(
        self,
        name: str,
        file_path: str,
        module_path: str,
        line: int,
        end_line: int,
        scope: str,
        context: Dict[str, Any] = None,
        imports: Dict[str, Dict[str, str]] = None
    ):
        self.name = name
        self.file_path = file_path
        self.module_path = module_path
        self.line = line
        self.end_line = end_line
        self.scope = scope
        self.context = context or {}
        self.imports = imports or {}
        
        # For tracking resolution results
        self.resolved_definition = None
        self.resolution_confidence = 0.0
        
    def get_context_hash(self) -> str:
        """Generate a hash of the reference context for caching."""
        components = [
            self.name,
            self.file_path,
            str(self.line),
            self.scope
        ]
        
        # Add import information
        for imp_name, imp_info in sorted(self.imports.items()):
            components.append(f"{imp_name}:{imp_info.get('module', '')}")
            
        unique_str = "|".join(components)
        return hashlib.sha256(unique_str.encode()).hexdigest()[:16]


class ImportGraph:
    """
    Tracks imports and exports across the entire repository for accurate symbol resolution.
    """
    def __init__(self):
        self.import_graph = nx.DiGraph()
        self.module_symbols = defaultdict(dict)  # Maps module path to {symbol_name: SymbolDefinition}
        self.module_exports = defaultdict(set)   # Maps module path to set of exported symbol names
        self.module_aliases = defaultdict(dict)  # Maps module path to {alias: original_module_path}
        self.symbol_aliases = defaultdict(dict)  # Maps module path to {alias: original_symbol_name}
        
    def add_module(self, module_path: str, file_path: str):
        """Add a module to the import graph."""
        if module_path not in self.import_graph:
            self.import_graph.add_node(module_path, file_path=file_path)
            
    def add_import(self, importer_module: str, imported_module: str, imported_symbols: Set[str] = None, 
                  alias: str = None, symbol_aliases: Dict[str, str] = None):
        """
        Add an import relationship to the graph.
        
        Args:
            importer_module: Module that is importing
            imported_module: Module being imported
            imported_symbols: Set of specific symbols being imported
            alias: Alias for the module (for import x as y)
            symbol_aliases: Mapping of {alias: original_name} for symbol aliases
        """
        # Ensure both modules exist in the graph
        if importer_module not in self.import_graph:
            self.import_graph.add_node(importer_module)
        if imported_module not in self.import_graph:
            self.import_graph.add_node(imported_module)
            
        # Add the import edge
        self.import_graph.add_edge(
            importer_module, 
            imported_module, 
            symbols=imported_symbols or set(),
            alias=alias
        )
        
        # Store module alias if provided
        if alias:
            self.module_aliases[importer_module][alias] = imported_module
            
        # Store symbol aliases if provided
        if symbol_aliases:
            for alias_name, original_name in symbol_aliases.items():
                self.symbol_aliases[importer_module][alias_name] = (imported_module, original_name)
                
    def add_symbol(self, module_path: str, symbol_def: SymbolDefinition, exported: bool = True):
        """Add a symbol to a module, optionally marking it as exported."""
        self.module_symbols[module_path][symbol_def.name] = symbol_def
        
        if exported:
            self.module_exports[module_path].add(symbol_def.name)
            
    def get_symbol(self, module_path: str, symbol_name: str) -> Optional[SymbolDefinition]:
        """Get a symbol definition from a specific module."""
        return self.module_symbols.get(module_path, {}).get(symbol_name)
    
    def resolve_symbol(self, reference: SymbolReference) -> Optional[SymbolDefinition]:
        """
        Resolve a symbol reference to its definition by traversing the import graph.
        
        This is the main entry point for resolving references and uses all import/export information.
        """
        ref_module = reference.module_path
        ref_name = reference.name
        
        # First check if it's a local symbol in the same module
        local_def = self.get_symbol(ref_module, ref_name)
        if local_def:
            return local_def
            
        # Check if it's an alias for an imported symbol
        if ref_name in self.symbol_aliases.get(ref_module, {}):
            imported_module, original_name = self.symbol_aliases[ref_module][ref_name]
            return self.get_symbol(imported_module, original_name)
            
        # Check if it's a qualified name (e.g., module.symbol)
        if "." in ref_name:
            parts = ref_name.split(".", 1)
            module_alias, symbol_name = parts[0], parts[1]
            
            # Check if the first part is a module alias
            if module_alias in self.module_aliases.get(ref_module, {}):
                imported_module = self.module_aliases[ref_module][module_alias]
                return self.get_symbol(imported_module, symbol_name)
                
        # Check all imported modules for the symbol
        for imported_module in self.import_graph.successors(ref_module):
            edge_data = self.import_graph.get_edge_data(ref_module, imported_module)
            imported_symbols = edge_data[0].get("symbols", set())
            
            # If specific symbols were imported, check if our symbol is one of them
            if imported_symbols and ref_name not in imported_symbols:
                continue
                
            # Check if the symbol exists in the imported module
            imported_def = self.get_symbol(imported_module, ref_name)
            if imported_def:
                return imported_def
                
        # Symbol not found
        return None


class EnhancedRepoMap:
    """
    Enhanced RepoMap implementation with accurate reference resolution.
    Uses a combination of symbol tables, import tracking, and language-specific resolution.
    """

    def __init__(
        self,
        map_tokens=1024,
        root=None,
        main_model=None,
        io=None,
        repo_content_prefix=None,
        verbose=False,
        max_context_window=None,
        map_mul_no_files=8,
    ):
        self.io = io
        self.verbose = verbose

        if not root:
            root = os.getcwd()
        self.root = root

        self.max_map_tokens = map_tokens
        self.map_mul_no_files = map_mul_no_files
        self.max_context_window = max_context_window

        self.repo_content_prefix = repo_content_prefix
        self.warned_files = set()
        
        # Import and symbol tracking
        self.import_graph = ImportGraph()
        
        # Comprehensive symbol tables
        self.symbol_table = {}  # Maps symbol_id to SymbolDefinition
        self.file_symbols = defaultdict(list)  # Maps file_path to list of SymbolDefinition
        self.module_symbols = defaultdict(list)  # Maps module_path to list of SymbolDefinition
        
        # Resolution caches
        self.resolution_cache = {}  # Maps reference context hash to resolved definition
        
        # Language-specific resolvers
        self.resolvers = {
            "python": self._resolve_python_reference,
            "javascript": self._resolve_javascript_reference,
            "typescript": self._resolve_typescript_reference,
            "java": self._resolve_java_reference,
        }
        
        # Debug counts for diagnostics
        self.ref_count = 0
        self.resolved_count = 0
        self.cross_file_ref_count = 0

    def token_count(self, text):
        """Count the number of tokens in the given text."""
        return len(text) / 4

    def read_text(self, fname):
        """Read text from a file."""
        if self.io and hasattr(self.io, "read_text"):
            return self.io.read_text(fname)
        else:
            try:
                with open(fname, "r", encoding="utf-8") as f:
                    return f.read()
            except Exception as e:
                logger.error(f"Error reading file {fname}: {e}")
                return None

    def get_rel_fname(self, fname):
        """Get relative file name from the root directory."""
        return os.path.relpath(fname, self.root)

    def get_mtime(self, fname):
        """Get modification time of a file."""
        try:
            return os.path.getmtime(fname)
        except FileNotFoundError:
            if self.io:
                self.io.tool_error(f"File not found error: {fname}")
            else:
                logger.error(f"File not found error: {fname}")
            return None

    def file_path_to_module_path(self, file_path):
        """
        Convert a file path to a module path.
        Example: src/models/user.py -> src.models.user
        """
        # Get the relative path from the root
        rel_path = self.get_rel_fname(file_path)
        
        # Handle __init__.py files
        if os.path.basename(file_path) == "__init__.py":
            # For __init__.py, the module path is the directory
            module_path = os.path.dirname(rel_path).replace(os.path.sep, ".")
            return module_path
        
        # Remove file extension
        module_path = os.path.splitext(rel_path)[0]
        
        # Replace directory separators with dots
        module_path = module_path.replace(os.path.sep, ".")
        
        return module_path

    def module_path_to_file_path(self, module_path, current_file=None):
        """
        Convert a module path to a file path.
        Handles relative imports based on current_file.
        
        Example: src.models.user -> src/models/user.py
        """
        # Handle relative imports
        if module_path.startswith(".") and current_file:
            current_dir = os.path.dirname(current_file)
            current_module = self.file_path_to_module_path(current_dir)
            
            # Count leading dots
            dot_count = 0
            while module_path.startswith("."):
                module_path = module_path[1:]
                dot_count += 1
            
            # Go up by dot_count directories
            parent_module = current_module
            for _ in range(dot_count - 1):
                if "." in parent_module:
                    parent_module = parent_module.rsplit(".", 1)[0]
                else:
                    parent_module = ""
            
            if module_path:
                # Combine with the relative part
                module_path = f"{parent_module}.{module_path}" if parent_module else module_path
            else:
                # Just the parent module if no additional path
                module_path = parent_module
        
        # Convert dots to directory separators
        file_path = module_path.replace(".", os.path.sep)
        
        # Try with .py extension first
        py_path = os.path.join(self.root, file_path + ".py")
        if os.path.exists(py_path):
            return py_path
            
        # Try as a directory with __init__.py
        init_path = os.path.join(self.root, file_path, "__init__.py")
        if os.path.exists(init_path):
            return init_path
            
        # Try other extensions for different languages
        for ext in [".js", ".ts", ".jsx", ".tsx", ".java", ".c", ".cpp"]:
            other_path = os.path.join(self.root, file_path + ext)
            if os.path.exists(other_path):
                return other_path
        
        # Could not find a file
        return None

    def extract_imports(self, code, lang, fname):
        """
        Extract import statements from code to build the import graph.
        Returns a detailed mapping of imported names to their sources.
        """
        if not code or not lang:
            return {}
        
        imports = {}
        module_path = self.file_path_to_module_path(fname)
        
        # Add this module to the import graph
        self.import_graph.add_module(module_path, fname)
        
        try:
            if lang == "python":
                # Extract Python imports
                self._extract_python_imports(code, fname, module_path, imports)
            
            elif lang == "javascript" or lang == "typescript":
                # Extract JS/TS imports
                self._extract_js_imports(code, fname, module_path, imports)
            
            elif lang == "java":
                # Extract Java imports
                self._extract_java_imports(code, fname, module_path, imports)
            
            return imports
            
        except Exception as e:
            logger.error(f"Error extracting imports from {fname}: {e}")
            return {}


    def calculate_relationship_weight(
        self,
        rel_type: str,
        source_type: str,
        target_type: str,
        source_id: str,      # Add this parameter
        target_id: str,      # Add this parameter
        cross_file: bool,
        usage_count: int = 1
    ) -> float:
        """
        Calculate relationship weight based on multiple factors:
        - Base weight for relationship type
        - Entity types involved
        - Cross-file relationship penalty/bonus
        - Usage count boost
        - Cross-module relationships
        """
        base_weight = RelationshipType.get_base_weight(rel_type)
        
        # Adjust weight based on entity types
        type_multiplier = 1.0
        if source_type == "CLASS" and target_type == "CLASS":
            type_multiplier = 1.2  # Stronger class-to-class relationships
        elif source_type == "INTERFACE" and target_type == "CLASS":
            type_multiplier = 1.1  # Interface-class relationships
        
        # Increase cross-file multiplier significantly
        cross_file_multiplier = 1.5 if cross_file else 1.0
        
        # Add module-level bonus
        if cross_file and self._are_different_modules(source_id, target_id):
            cross_file_multiplier *= 2.0  # Additional 150% boost for cross-module
        
        usage_multiplier = 1.0 + math.log(usage_count + 1, 10)
        
        return base_weight * type_multiplier * cross_file_multiplier * usage_multiplier

    def _are_different_modules(self, source_id: str, target_id: str) -> bool:
        """Check if two entities belong to different modules."""
        source_module = source_id.split('/')[0]  # Assuming path-based IDs
        target_module = target_id.split('/')[0]
        return source_module != target_module

    def _add_relationship_edge(
        self,
        G: nx.DiGraph,
        source_id: str,
        target_id: str,
        rel_type: str,
        source_type: str,
        target_type: str,
        cross_file: bool,
        **kwargs
    ) -> None:
        """Add a relationship edge with calculated weight to the graph."""
        # Get or initialize usage count from kwargs or default to 1
        usage_count = kwargs.pop('usage_count', 1) if kwargs else 1

        weight = self.calculate_relationship_weight(
            rel_type=rel_type,
            source_type=source_type,
            target_type=target_type,
            source_id=source_id,    # Pass the source_id
            target_id=target_id,    # Pass the target_id
            cross_file=cross_file,
            usage_count=usage_count
        )
        
        if cross_file: 
            print(f"source_id: {source_id}")
            print(f"target_id: {target_id}")
            print(weight)

        G.add_edge(
            source_id,
            target_id,
            type=rel_type,
            weight=weight,
            usage_count=usage_count,
            cross_file=cross_file,
            **kwargs
        )


    def _extract_python_imports(self, code, fname, module_path, imports):
        """Extract Python import statements and build import graph."""
        # Direct imports (import x, import x.y, import x.y as z)
        import_pattern = r"^\s*import\s+([\w\d_.]+)(?:\s+as\s+([\w\d_]+))?"
        for match in re.finditer(import_pattern, code, re.MULTILINE):
            module = match.group(1)
            alias = match.group(2)
            
            # Resolve the imported module to a file path
            imported_module_path = module
            resolved_file = self.module_path_to_file_path(imported_module_path, fname)
            
            # Add to the import graph
            self.import_graph.add_import(
                importer_module=module_path,
                imported_module=imported_module_path,
                alias=alias
            )
            
            if alias:
                # import x.y as z -> z refers to x.y
                imports[alias] = {
                    "module": imported_module_path,
                    "resolved_file": resolved_file,
                    "import_type": "module_alias"
                }
            else:
                # import x.y -> x refers to x
                base_name = module.split(".")[0]
                imports[base_name] = {
                    "module": base_name,
                    "resolved_file": self.module_path_to_file_path(base_name, fname),
                    "import_type": "module"
                }
        
        # From imports (from x import y, from x.y import z, from x import y as z)
        from_pattern = r"^\s*from\s+([\w\d_.]+)\s+import\s+([^#\n]+)"
        for match in re.finditer(from_pattern, code, re.MULTILINE):
            module = match.group(1)
            imported = match.group(2).strip()
            
            imported_module_path = module
            resolved_module_file = self.module_path_to_file_path(imported_module_path, fname)
            
            imported_symbols = set()
            symbol_aliases = {}
            
            # Handle multiple imports (from x import y, z, a as b)
            for imp in imported.split(","):
                imp = imp.strip()
                
                if imp == "*":
                    # from x import * -> Import all exported symbols
                    imports["*"] = {
                        "module": imported_module_path,
                        "resolved_file": resolved_module_file,
                        "import_type": "wildcard"
                    }
                    
                    # Add to import graph with empty symbols to indicate wildcard
                    self.import_graph.add_import(
                        importer_module=module_path,
                        imported_module=imported_module_path
                    )
                    continue
                
                if " as " in imp:
                    # from x import y as z -> z refers to x.y
                    name, alias = imp.split(" as ", 1)
                    name = name.strip()
                    alias = alias.strip()
                    
                    imports[alias] = {
                        "module": imported_module_path,
                        "resolved_file": resolved_module_file,
                        "original_name": name,
                        "import_type": "symbol_alias"
                    }
                    
                    imported_symbols.add(name)
                    symbol_aliases[alias] = name
                else:
                    # from x import y -> y refers to x.y
                    imports[imp] = {
                        "module": imported_module_path,
                        "resolved_file": resolved_module_file,
                        "import_type": "symbol"
                    }
                    
                    imported_symbols.add(imp)
            
            # Add to import graph with specific symbols
            self.import_graph.add_import(
                importer_module=module_path,
                imported_module=imported_module_path,
                imported_symbols=imported_symbols,
                symbol_aliases=symbol_aliases
            )

    def _extract_js_imports(self, code, fname, module_path, imports):
        """Extract JavaScript/TypeScript import statements and build import graph."""
        # import { x } from 'module' or import { x as y } from 'module'
        named_import_pattern = r"import\s+\{([^}]+)\}\s+from\s+['\"]([^'\"]+)['\"]"
        for match in re.finditer(named_import_pattern, code):
            imports_list = match.group(1)
            imported_module = match.group(2)
            
            # Resolve the module path based on the import string
            resolved_module_path = self._resolve_js_module_path(imported_module, fname)
            resolved_file = self.module_path_to_file_path(resolved_module_path, fname)
            
            imported_symbols = set()
            symbol_aliases = {}
            
            for imp in imports_list.split(","):
                imp = imp.strip()
                if " as " in imp:
                    name, alias = imp.split(" as ", 1)
                    name = name.strip()
                    alias = alias.strip()
                    
                    imports[alias] = {
                        "module": resolved_module_path,
                        "resolved_file": resolved_file,
                        "original_name": name,
                        "import_type": "named_import_alias"
                    }
                    
                    imported_symbols.add(name)
                    symbol_aliases[alias] = name
                else:
                    imports[imp] = {
                        "module": resolved_module_path,
                        "resolved_file": resolved_file,
                        "import_type": "named_import"
                    }
                    
                    imported_symbols.add(imp)
            
            # Add to import graph
            self.import_graph.add_import(
                importer_module=module_path,
                imported_module=resolved_module_path,
                imported_symbols=imported_symbols,
                symbol_aliases=symbol_aliases
            )
        
        # import x from 'module'
        default_import_pattern = r"import\s+([\w\d_$]+)\s+from\s+['\"]([^'\"]+)['\"]"
        for match in re.finditer(default_import_pattern, code):
            name = match.group(1)
            imported_module = match.group(2)
            
            resolved_module_path = self._resolve_js_module_path(imported_module, fname)
            resolved_file = self.module_path_to_file_path(resolved_module_path, fname)
            
            imports[name] = {
                "module": resolved_module_path,
                "resolved_file": resolved_file,
                "original_name": "default",
                "import_type": "default_import"
            }
            
            # Add to import graph
            self.import_graph.add_import(
                importer_module=module_path,
                imported_module=resolved_module_path,
                imported_symbols={"default"},
                symbol_aliases={name: "default"}
            )
        
        # import * as x from 'module'
        namespace_import_pattern = r"import\s+\*\s+as\s+([\w\d_$]+)\s+from\s+['\"]([^'\"]+)['\"]"
        for match in re.finditer(namespace_import_pattern, code):
            name = match.group(1)
            imported_module = match.group(2)
            
            resolved_module_path = self._resolve_js_module_path(imported_module, fname)
            resolved_file = self.module_path_to_file_path(resolved_module_path, fname)
            
            imports[name] = {
                "module": resolved_module_path,
                "resolved_file": resolved_file,
                "import_type": "namespace_import"
            }
            
            # Add to import graph
            self.import_graph.add_import(
                importer_module=module_path,
                imported_module=resolved_module_path,
                alias=name
            )

    def _resolve_js_module_path(self, module_string, current_file):
        """Resolve a JavaScript/TypeScript module string to a module path."""
        if module_string.startswith("."):
            # Relative import
            current_dir = os.path.dirname(current_file)
            abs_path = os.path.normpath(os.path.join(current_dir, module_string))
            return self.file_path_to_module_path(abs_path)
        else:
            # Non-relative import (likely a package)
            # In a complete implementation, we would resolve node_modules paths
            # For simplicity, we'll just use the module string directly
            return module_string

    def _extract_java_imports(self, code, fname, module_path, imports):
        """Extract Java import statements and build import graph."""
        # import pkg.Class or import pkg.*
        import_pattern = r"^\s*import\s+([\w\d_.]+\*?);"
        for match in re.finditer(import_pattern, code, re.MULTILINE):
            imported = match.group(1)
            
            if imported.endswith(".*"):
                # Wildcard import
                package = imported[:-2]  # Remove .*
                imports[f"__WILDCARD__{package}"] = {
                    "module": package,
                    "import_type": "wildcard"
                }
                
                # Add to import graph
                self.import_graph.add_import(
                    importer_module=module_path,
                    imported_module=package
                )
            else:
                # Direct class import
                class_name = imported.split(".")[-1]
                package = ".".join(imported.split(".")[:-1])
                
                imports[class_name] = {
                    "module": package,
                    "original_name": class_name,
                    "import_type": "class"
                }
                
                # Add to import graph
                self.import_graph.add_import(
                    importer_module=module_path,
                    imported_module=package,
                    imported_symbols={class_name}
                )

    def extract_qualified_name(self, node, lang, scope=""):
        """
        Extract fully qualified name for a definition node.
        
        This creates a name that includes module and class context, which
        is critical for accurate reference resolution.
        """
        if not node:
            return ""
        
        try:
            name = node.text.decode("utf-8")
            
            if lang == "python":
                if scope:
                    scope_parts = scope.split(".")
                    class_scopes = []
                    
                    # Extract class names from scope
                    for part in scope_parts:
                        if part.startswith("class:"):
                            class_scopes.append(part.split(":", 1)[1])
                    
                    if class_scopes:
                        # Include class hierarchy in qualified name
                        return ".".join(class_scopes + [name])
                
                # No containing class
                return name
                
            elif lang == "java":
                # In Java, fully qualified names include the package
                package_name = self._extract_java_package(node)
                if package_name:
                    if scope:
                        # Include enclosing classes
                        scope_parts = scope.split(".")
                        class_scopes = []
                        
                        for part in scope_parts:
                            if part.startswith("class:"):
                                class_scopes.append(part.split(":", 1)[1])
                        
                        if class_scopes:
                            return f"{package_name}.{'.'.join(class_scopes)}.{name}"
                        
                    return f"{package_name}.{name}"
                
                # Fallback to scope-based qualified name
                if scope:
                    scope_parts = scope.split(".")
                    class_scopes = []
                    
                    for part in scope_parts:
                        if part.startswith("class:"):
                            class_scopes.append(part.split(":", 1)[1])
                    
                    if class_scopes:
                        return ".".join(class_scopes + [name])
                
                return name
                
            elif lang == "javascript" or lang == "typescript":
                # Similar approach to Python
                if scope:
                    scope_parts = scope.split(".")
                    class_scopes = []
                    
                    for part in scope_parts:
                        if part.startswith("class:"):
                            class_scopes.append(part.split(":", 1)[1])
                    
                    if class_scopes:
                        return ".".join(class_scopes + [name])
                
                return name
            
            # Default fallback
            return name
            
        except Exception as e:
            logger.error(f"Error extracting qualified name: {e}")
            return ""

    def _extract_java_package(self, node):
        """Extract Java package name from the node's file."""
        try:
            # Walk up to file node
            root_node = node
            while root_node.parent:
                root_node = root_node.parent
            
            # Find package declaration
            for child in root_node.children:
                if child.type == "package_declaration":
                    for name_node in child.children:
                        if name_node.type == "scoped_identifier":
                            return name_node.text.decode("utf-8")
            
            return ""
        except Exception:
            return ""
            
    def extract_signature(self, node, lang):
        """
        Extract detailed signature information from a node.
        Returns a structured representation of the signature.
        """
        if not node:
            return {}
            
        try:
            signature = {}
            
            if lang == "python":
                if node.type == "function_definition":
                    # Extract parameter information
                    params_node = node.child_by_field_name("parameters")
                    if params_node:
                        params_text = params_node.text.decode("utf-8")
                        signature["params_text"] = params_text
                        
                        # Parse individual parameters
                        param_list = []
                        inside_params = False
                        param_start = 0
                        
                        for i, ch in enumerate(params_text):
                            if ch == '(':
                                inside_params = True
                                param_start = i + 1
                            elif ch == ')' and inside_params:
                                inside_params = False
                                
                                # Get params string without parentheses
                                params_str = params_text[param_start:i].strip()
                                
                                # Split by commas, handling nested structures
                                if params_str:
                                    current_param = ""
                                    paren_level = 0
                                    bracket_level = 0
                                    
                                    for ch in params_str + ',':  # Add comma to process last param
                                        if ch == ',' and paren_level == 0 and bracket_level == 0:
                                            param_list.append(current_param.strip())
                                            current_param = ""
                                        else:
                                            current_param += ch
                                            
                                            if ch == '(':
                                                paren_level += 1
                                            elif ch == ')':
                                                paren_level -= 1
                                            elif ch == '[':
                                                bracket_level += 1
                                            elif ch == ']':
                                                bracket_level -= 1
                        
                        signature["params"] = param_list
                        
                    # Extract return type annotation if present
                    for i, child in enumerate(node.children):
                        if child.type == "->":
                            if i + 1 < len(node.children):
                                returns_node = node.children[i + 1]
                                return_type = returns_node.text.decode("utf-8")
                                signature["return_type"] = return_type
                                break
            
            elif lang == "java":
                if node.type == "method_declaration":
                    # Get return type
                    type_node = node.child_by_field_name("type")
                    if type_node:
                        return_type = type_node.text.decode("utf-8")
                        signature["return_type"] = return_type
                    
                    # Get parameters
                    params_node = node.child_by_field_name("formal_parameters")
                    if params_node:
                        params_text = params_node.text.decode("utf-8")
                        signature["params_text"] = params_text
                        
                        # Parse individual parameters
                        param_list = []
                        
                        # Find parameter declarations
                        for child in params_node.children:
                            if child.type == "formal_parameter":
                                param_type = None
                                param_name = None
                                
                                for param_child in child.children:
                                    if param_child.type == "type_identifier":
                                        param_type = param_child.text.decode("utf-8")
                                    elif param_child.type == "identifier":
                                        param_name = param_child.text.decode("utf-8")
                                
                                if param_type and param_name:
                                    param_list.append(f"{param_type} {param_name}")
                        
                        signature["params"] = param_list
                    
                    # Extract exceptions
                    throws_node = None
                    for child in node.children:
                        if child.type == "throws":
                            throws_node = child
                            break
                    
                    if throws_node:
                        exceptions = []
                        for child in throws_node.children:
                            if child.type == "type_identifier":
                                exceptions.append(child.text.decode("utf-8"))
                        
                        if exceptions:
                            signature["exceptions"] = exceptions
            
            elif lang == "javascript" or lang == "typescript":
                if node.type in ["function_declaration", "method_definition", "arrow_function"]:
                    # Get parameters
                    params_node = node.child_by_field_name("parameters")
                    if params_node:
                        params_text = params_node.text.decode("utf-8")
                        signature["params_text"] = params_text
                        
                        # Parse individual parameters
                        param_list = []
                        
                        for child in params_node.children:
                            if child.type in ["identifier", "formal_parameter"]:
                                param_list.append(child.text.decode("utf-8"))
                        
                        signature["params"] = param_list
                    
                    # Check for TypeScript return type
                    if lang == "typescript":
                        return_type_node = node.child_by_field_name("return_type")
                        if return_type_node:
                            return_type = return_type_node.text.decode("utf-8")
                            signature["return_type"] = return_type
            
            # Convert to string for storage
            signature_str = str(signature)
            return signature_str
            
        except Exception as e:
            logger.error(f"Error extracting signature: {e}")
            return "{}"
            
    def get_containing_class(self, scope):
        """Extract the containing class name from a scope string."""
        if not scope:
            return ""
            
        # Parse the scope hierarchy
        scope_parts = scope.split(".")
        for part in reversed(scope_parts):  # Start from innermost scope
            if part.startswith("class:"):
                return part.split(":", 1)[1]
        
        return ""
        
    def get_tags(self, fname, rel_fname):
        """
        Enhanced version of get_tags that builds comprehensive symbol tables.
        """
        # Check if the file is valid
        file_mtime = self.get_mtime(fname)
        if file_mtime is None:
            return []

        # Get language and parser
        lang = filename_to_lang(fname)
        if not lang:
            return []

        try:
            language = get_language(lang)
            parser = get_parser(lang)
            
            # Get query schema
            query_scm = get_scm_fname(lang)
            if not query_scm or not query_scm.exists():
                return []
            query_scm = query_scm.read_text()
            
            # Parse code
            code = self.read_text(fname)
            if not code:
                return []
            
            # Get module path for this file
            module_path = self.file_path_to_module_path(fname)
            
            # Extract import statements
            imports = self.extract_imports(code, lang, fname)
            
            # Extract scope hierarchy with enhanced context
            scope_map = self.extract_scope_hierarchy(code, lang, parser)
            
            # Parse the code tree
            tree = parser.parse(bytes(code, "utf-8"))
            
            # Run the tags queries
            query = language.query(query_scm)
            captures = query.captures(tree.root_node)
            
            result_tags = []
            
            for node, tag in captures:
                node_text = node.text.decode("utf-8")
                line_num = node.start_point[0]
                end_line = node.end_point[0]
                
                # Get scope and context for this line
                scope_info = scope_map.get(line_num, {"scope": "", "context": {}})
                scope = scope_info["scope"]
                context = scope_info["context"]
                
                # Get parent scope
                parent_scope = ".".join(scope.split(".")[:-1]) if scope else ""
                
                # Get containing class
                containing_class = self.get_containing_class(scope)
                
                if tag.startswith("name.definition."):
                    kind = "def"
                    type_name = tag.split(".")[-1]
                    
                    # Get parent node for signature extraction
                    parent_node = node.parent
                    while parent_node and not parent_node.type in [
                        "function_definition", "method_declaration", 
                        "function_declaration", "method_definition"
                    ]:
                        parent_node = parent_node.parent
                    
                    # Extract signature information
                    signature = self.extract_signature(parent_node, lang)
                    
                    # Extract fully qualified name
                    qualified_name = self.extract_qualified_name(node, lang, scope)
                    
                    # Generate a unique symbol ID
                    symbol_id = hashlib.sha256(
                        f"{module_path}|{qualified_name}|{scope}|{line_num}".encode()
                    ).hexdigest()[:16]
                    
                    # Add to symbol table
                    symbol_def = SymbolDefinition(
                        name=node_text,
                        qualified_name=qualified_name,
                        file_path=fname,
                        module_path=module_path,
                        line=line_num,
                        end_line=end_line,
                        symbol_type=type_name,
                        scope=scope,
                        signature=signature,
                        containing_class=containing_class,
                        symbol_id=symbol_id
                    )
                    
                    self.symbol_table[symbol_id] = symbol_def
                    self.file_symbols[fname].append(symbol_def)
                    self.module_symbols[module_path].append(symbol_def)
                    
                    # Add to import graph for reference resolution
                    self.import_graph.add_symbol(module_path, symbol_def)
                    
                    # Create tag with all needed information
                    tag_obj = Tag(
                        rel_fname=rel_fname,
                        fname=fname,
                        name=node_text,
                        kind=kind,
                        line=line_num,
                        end_line=end_line,
                        type=type_name,
                        signature=signature,
                        scope=scope,
                        parent_scope=parent_scope,
                        imports=imports,
                        module_path=module_path,
                        qualified_name=qualified_name,
                        symbol_id=symbol_id,
                        context_hash="",  # Not needed for definitions
                        containing_class=containing_class,
                        context=scope_info.get("context", {})  # Add the context from scope_info
                    )
                    
                    result_tags.append(tag_obj)
                
                elif tag.startswith("name.reference."):
                    kind = "ref"
                    type_name = tag.split(".")[-1]
                    
                    # Generate a context hash for caching resolution results
                    context_hash = hashlib.sha256(
                        f"{fname}|{line_num}|{scope}|{node_text}".encode()
                    ).hexdigest()[:16]
                    
                    # Create reference tag
                    tag_obj = Tag(
                        rel_fname=rel_fname,
                        fname=fname,
                        name=node_text,
                        kind=kind,
                        line=line_num,
                        end_line=end_line,
                        type=type_name,
                        signature="",  # References don't have signatures
                        scope=scope,
                        parent_scope=parent_scope,
                        imports=imports,
                        module_path=module_path,
                        qualified_name="",  # Will be resolved later
                        symbol_id="",  # Will be resolved later
                        context_hash=context_hash,
                        containing_class=containing_class,
                        context=scope_info.get("context", {})  # Add the context from scope_info
                    )
                    
                    result_tags.append(tag_obj)
            
            # If we didn't find any references, try using Pygments as fallback
            if not any(tag.kind == "ref" for tag in result_tags):
                try:
                    lexer = guess_lexer_for_filename(fname, code)
                    tokens = list(lexer.get_tokens_unprocessed(code))
                    
                    for pos, token_type, token_text in tokens:
                        if token_type in Token.Name and token_text.strip():
                            # Try to map position to line number
                            line_num = code.count('\n', 0, pos) + 1
                            
                            # Get scope for this position if possible
                            scope_info = scope_map.get(line_num, {"scope": "", "context": {}})
                            scope = scope_info["scope"]
                            context = scope_info["context"]
                            
                            # Get parent scope
                            parent_scope = ".".join(scope.split(".")[:-1]) if scope else ""
                            
                            # Get containing class
                            containing_class = self.get_containing_class(scope)
                            
                            # Generate a context hash
                            context_hash = hashlib.sha256(
                                f"{fname}|{line_num}|{scope}|{token_text}".encode()
                            ).hexdigest()[:16]
                            
                            # Add fallback reference tag
                            tag_obj = Tag(
                                rel_fname=rel_fname,
                                fname=fname,
                                name=token_text,
                                kind="ref",
                                line=line_num,
                                end_line=line_num,  # Don't know exact end line
                                type="identifier",
                                signature="",
                                scope=scope,
                                parent_scope=parent_scope,
                                imports=imports,
                                module_path=module_path,
                                qualified_name="",
                                symbol_id="",
                                context_hash=context_hash,
                                containing_class=containing_class,
                                context=scope_info.get("context", {})  # Include the context from scope_info
                            )
                            
                            result_tags.append(tag_obj)
                except ClassNotFound:
                    pass
            
            return result_tags
            
        except Exception as e:
            logger.error(f"Error getting tags for {fname}: {e}")
            return []


    def resolve_reference(self, ref_tag, all_definitions=None):
        """
        Resolve a reference with high accuracy requirements and robust error handling.
        
        Args:
            ref_tag: Tag object representing the reference
            all_definitions: Optional list of all definitions (for optimization)
            
        Returns:
            The resolved definition or None if resolution confidence is too low
        """
        # Skip references to common builtin names
        common_builtins = {
            'self', 'cls', 'None', 'True', 'False', 'print', 'dict', 'list', 'tuple', 'set',
            'str', 'int', 'float', 'bool', 'object', 'super', 'this', 'undefined', 'null'
        }
        
        if ref_tag.name in common_builtins:
            return None
        
        # Check if this reference has already been resolved
        if ref_tag.context_hash in self.resolution_cache:
            return self.resolution_cache[ref_tag.context_hash]
        
        try:
            # Create a SymbolReference for resolution
            ref = SymbolReference(
                name=ref_tag.name,
                file_path=ref_tag.fname,
                module_path=ref_tag.module_path,
                line=ref_tag.line,
                end_line=ref_tag.end_line,
                scope=ref_tag.scope,
                imports=ref_tag.imports,
                context=getattr(ref_tag, 'context', {})
            )
            
            # Collect all candidate definitions with confidence scores
            candidates = []
            
            # Use language-specific resolver if available
            lang = filename_to_lang(ref_tag.fname)
            if lang in self.resolvers:
                try:
                    definition = self.resolvers[lang](ref)
                    if definition:
                        confidence = self._calculate_resolution_confidence(ref, definition)
                        candidates.append((definition, confidence))
                except Exception as e:
                    logger.debug(f"Error in language-specific resolver: {e}")
            
            # Use generic resolver as backup
            try:
                generic_def = self._resolve_generic_reference(ref)
                if generic_def:
                    confidence = self._calculate_resolution_confidence(ref, generic_def)
                    candidates.append((generic_def, confidence))
            except Exception as e:
                logger.debug(f"Error in generic resolver: {e}")
            
            # Sort by confidence
            candidates.sort(key=lambda x: x[1], reverse=True)
            
            # Check if this is a common method name
            common_method_names = {"get", "set", "add", "remove", "update", "create", "delete", "find", "list"}
            is_common_method = ref_tag.name in common_method_names
            
            # Set confidence threshold based on method name commonality
            confidence_threshold = 0.7 if is_common_method else 0.4
            
            # Log extensive diagnostic information for references to common methods
            if is_common_method and candidates:
                logger.debug(f"Common method '{ref_tag.name}' resolution:")
                for i, (def_candidate, conf) in enumerate(candidates[:3]):
                    logger.debug(f"  Candidate {i+1}: {getattr(def_candidate, 'module_path', 'unknown')}.{getattr(def_candidate, 'name', 'unknown')} (confidence: {conf:.2f})")
            
            # Only accept sufficiently confident matches
            if candidates and candidates[0][1] >= confidence_threshold:
                definition = candidates[0][0]
                
                # Special case: PipelineCache.get - require very high confidence
                if (is_common_method and 
                    getattr(definition, 'name', '') == 'get' and
                    'cache/pipeline_cache.py' in getattr(definition, 'file_path', '') and
                    candidates[0][1] < 0.8):
                    # This is specifically targeting the problematic case
                    logger.debug(f"Rejected reference to PipelineCache.get with confidence {candidates[0][1]:.2f} (required: 0.8)")
                    self.resolution_cache[ref_tag.context_hash] = None
                    return None
                
                # Cache and return the result
                self.resolution_cache[ref_tag.context_hash] = definition
                return definition
            
            # If no high-confidence match, don't resolve
            self.resolution_cache[ref_tag.context_hash] = None
            return None
            
        except Exception as e:
            # Log error but don't crash
            logger.error(f"Error resolving reference {ref_tag.name}: {e}")
            self.resolution_cache[ref_tag.context_hash] = None
            return None


    def _resolve_generic_reference(self, ref):
        """
        Generic reference resolution with enhanced accuracy
        """
        # Skip resolving standard library references that are noisy
        if self._is_standard_library_reference(ref.name):
            return None
        
        # Initialize confidence tracking
        resolve_with_high_confidence = False
        resolution_confidence = 0.0
        best_definition = None
        
        # 1. Check if the reference is within the same scope
        for symbol in self.module_symbols.get(ref.module_path, []):
            if symbol.name == ref.name:
                # Same scope is high confidence
                if symbol.scope == ref.scope:
                    resolve_with_high_confidence = True
                    return symbol
                
                # Track as potential match
                confidence = self._calculate_resolution_confidence(ref, symbol)
                if confidence > resolution_confidence:
                    resolution_confidence = confidence
                    best_definition = symbol
        
        # 2. Check for method calls with explicit receiver
        if "." in ref.name:
            receiver, method_name = ref.name.split(".", 1)
            
            # High confidence method resolution for object references
            resolved_method = self._resolve_method_call(ref, ref.scope, ref.imports)
            if resolved_method:
                confidence = self._calculate_resolution_confidence(ref, resolved_method)
                if confidence > resolution_confidence:
                    resolution_confidence = confidence
                    best_definition = resolved_method
                    
                    # 0.75 is a threshold for high confidence in method resolution
                    if confidence >= 0.75:
                        resolve_with_high_confidence = True
        
        # 3. Check if it's a directly imported name
        if hasattr(ref, 'imports') and ref.imports and ref.name in ref.imports:
            import_info = ref.imports[ref.name]
            imported_module = import_info.get("module", "")
            
            if import_info.get("import_type") in {"symbol", "symbol_alias", "class"}:
                original_name = import_info.get("original_name", ref.name)
                
                # Try to find the symbol in the imported module
                for symbol in self.module_symbols.get(imported_module, []):
                    if symbol.name == original_name:
                        confidence = 0.8  # Direct imports are high confidence
                        if confidence > resolution_confidence:
                            resolution_confidence = confidence
                            best_definition = symbol
                            resolve_with_high_confidence = True
        
        # 4. Check for wildcard imports
        if hasattr(ref, 'imports') and ref.imports:
            for import_name, import_info in ref.imports.items():
                if import_name == "*" or import_name.startswith("__WILDCARD__"):
                    imported_module = import_info.get("module", "")
                    
                    for symbol in self.module_symbols.get(imported_module, []):
                        if symbol.name == ref.name:
                            confidence = 0.6  # Wildcard imports are medium confidence
                            if confidence > resolution_confidence:
                                resolution_confidence = confidence
                                best_definition = symbol
                                
                                # For wildcards, only set high confidence for unique names
                                if not self._is_common_name(ref.name):
                                    resolve_with_high_confidence = True
        
        # Critical change: For extremely common method names,
        # if we can't resolve with high confidence, don't resolve at all
        # to prevent false positives
        common_method_names = {"get", "set", "add", "remove", "update", "create", "delete", "find", "list"}
        if ref.name in common_method_names and not resolve_with_high_confidence:
            return None
        
        # If we've reached a certain confidence threshold, use the best match
        if resolution_confidence >= 0.6:
            return best_definition
        
        # If we get here with low confidence, don't resolve
        return None

    def _is_standard_library_reference(self, name):
        """
        Check if a name refers to a common standard library element.
        
        Args:
            name: Symbol name to check
            
        Returns:
            Boolean indicating if this is a standard library reference
        """
        standard_lib_names = {
            # Python
            "os", "sys", "math", "datetime", "re", "json", "collections", "pathlib",
            "print", "dict", "list", "tuple", "set", "None", "True", "False",
            
            # JavaScript
            "console", "document", "window", "globalThis", "Object", "Array",
            "undefined", "NaN", "Infinity",
            
            # Java
            "System", "Object", "String", "Integer", "Double", "Boolean"
        }
        
        return name in standard_lib_names

    def _is_common_name(self, name):
        """
        Check if a name is extremely common across codebases.
        
        Args:
            name: Symbol name to check
            
        Returns:
            Boolean indicating if this is a very common name
        """
        common_names = {
            "get", "set", "add", "remove", "create", "update", "delete", "find",
            "list", "init", "start", "stop", "run", "execute", "process", "handle",
            "main", "load", "save", "open", "close", "read", "write", "next",
            "reset", "clear", "build", "to_string", "toString", "equals", "hashCode"
        }
        
        return name in common_names




    def _resolve_python_reference(self, ref):
        """
        Python-specific reference resolution strategy.
        Implements Python's name resolution rules (LEGB rule).
        """
        ref_name = ref.name
        ref_module = ref.module_path
        ref_file = ref.file_path
        ref_scope = ref.scope
        ref_imports = ref.imports
        
        # Skip resolving common Python keywords and builtins
        if ref_name in {'self', 'cls', 'None', 'True', 'False', 'print', 'dict', 'list', 'tuple', 'set'}:
            return None
        
        # Skip resolving imported standard library modules
        std_lib_modules = {'os', 'sys', 'math', 'datetime', 're', 'json', 'collections', 'pathlib'}
        if ref_name in std_lib_modules:
            return None
        
        # 1. Check if it's a directly imported name
        if ref_name in ref_imports:
            import_info = ref_imports[ref_name]
            imported_module = import_info.get("module", "")
            
            # If it's a module alias, it's not a reference to a symbol
            if import_info.get("import_type") == "module_alias":
                return None
            
            # For symbols imported from modules
            if import_info.get("import_type") in {"symbol", "symbol_alias"}:
                original_name = import_info.get("original_name", ref_name)
                
                # Try to find the symbol in the imported module
                for symbol in self.module_symbols.get(imported_module, []):
                    if symbol.name == original_name:
                        return symbol
        
        # 2. Check for local definition in the current function/class (Local)
        if ref_scope:
            # Extract the current scope hierarchy
            scope_parts = ref_scope.split(".")
            current_scope = ""
            
            # Build scopes from innermost to outermost
            for i in range(len(scope_parts)-1, -1, -1):
                if current_scope:
                    current_scope = scope_parts[i] + "." + current_scope
                else:
                    current_scope = scope_parts[i]
                
                # Check for symbols defined in this scope
                for symbol in self.module_symbols.get(ref_module, []):
                    if symbol.name == ref_name and symbol.scope == current_scope:
                        return symbol
        
        # 3. Check for definitions in enclosing scopes (Enclosing)
        if ref_scope:
            # Extract enclosing scopes
            scope_parts = ref_scope.split(".")
            
            # Build scopes from innermost to outermost
            for i in range(len(scope_parts)-1, 0, -1):
                enclosing_scope = ".".join(scope_parts[:i])
                
                for symbol in self.module_symbols.get(ref_module, []):
                    if symbol.name == ref_name and symbol.scope == enclosing_scope:
                        return symbol
        
        # 4. Check for module-level definitions (Global)
        for symbol in self.module_symbols.get(ref_module, []):
            if symbol.name == ref_name and (not symbol.scope or symbol.scope.count(".") == 0):
                return symbol
        
        # 5. Check for wildcard imports
        if "*" in ref_imports:
            wildcard_info = ref_imports["*"]
            imported_module = wildcard_info.get("module", "")
            
            for symbol in self.module_symbols.get(imported_module, []):
                if symbol.name == ref_name:
                    return symbol
        
        # 6. Check for built-in modules or global scope (Builtin)
        # This would typically check Python's builtins, but we'll skip that
        
        # 7. Fallback: check for any matching name in the entire codebase
        for module, symbols in self.module_symbols.items():
            for symbol in symbols:
                if symbol.name == ref_name:
                    return symbol
        
        return None

    def _resolve_javascript_reference(self, ref):
        """
        JavaScript-specific reference resolution.
        Implements JavaScript's hoisting and scope rules.
        """
        ref_name = ref.name
        ref_module = ref.module_path
        ref_file = ref.file_path
        ref_scope = ref.scope
        ref_imports = ref.imports
        
        # Skip resolving common JS globals
        if ref_name in {'undefined', 'NaN', 'Infinity', 'globalThis', 'Object', 'Array', 'console'}:
            return None
        
        # 1. Check if it's a direct import
        if ref_name in ref_imports:
            import_info = ref_imports[ref_name]
            imported_module = import_info.get("module", "")
            
            # Handle default imports
            if import_info.get("import_type") == "default_import":
                for symbol in self.module_symbols.get(imported_module, []):
                    if symbol.name == "default" or (
                        symbol.name == "exports" and not symbol.scope
                    ):
                        return symbol
            
            # Handle named imports
            elif import_info.get("import_type") in {"named_import", "named_import_alias"}:
                original_name = import_info.get("original_name", ref_name)
                
                for symbol in self.module_symbols.get(imported_module, []):
                    if symbol.name == original_name:
                        return symbol
        
        # 2. Check for lexical scope (let/const)
        if ref_scope:
            # Extract the current scope hierarchy
            scope_parts = ref_scope.split(".")
            current_scope = ""
            
            # Build scopes from innermost to outermost
            for i in range(len(scope_parts)-1, -1, -1):
                if current_scope:
                    current_scope = scope_parts[i] + "." + current_scope
                else:
                    current_scope = scope_parts[i]
                
                # Check for symbols defined in this scope
                for symbol in self.module_symbols.get(ref_module, []):
                    if symbol.name == ref_name and symbol.scope == current_scope:
                        return symbol
        
        # 3. Check for module-level definitions (global scope for this file)
        for symbol in self.module_symbols.get(ref_module, []):
            if symbol.name == ref_name and (not symbol.scope or symbol.scope.count(".") == 0):
                return symbol
        
        # 4. Check the import for a namespace import
        for import_name, import_info in ref_imports.items():
            if import_info.get("import_type") == "namespace_import":
                # Check if we're referencing something like 'namespace.property'
                if ref_name.startswith(import_name + "."):
                    prop_name = ref_name[len(import_name)+1:]
                    imported_module = import_info.get("module", "")
                    
                    for symbol in self.module_symbols.get(imported_module, []):
                        if symbol.name == prop_name:
                            return symbol
        
        # 5. Fallback: check for any matching name in the entire codebase
        for module, symbols in self.module_symbols.items():
            for symbol in symbols:
                if symbol.name == ref_name:
                    return symbol
        
        return None

    def _resolve_typescript_reference(self, ref):
        """
        TypeScript-specific reference resolution.
        Extends JavaScript with additional type tracking.
        """
        # TypeScript has the same basic resolution as JavaScript but with types
        base_resolution = self._resolve_javascript_reference(ref)
        if base_resolution:
            return base_resolution
        
        # Additional TypeScript-specific resolution could be added here
        # For example, resolving interface types, generic types, etc.
        
        return None

    def _resolve_java_reference(self, ref):
        """
        Java-specific reference resolution.
        Implements Java's package, import, and naming rules.
        """
        ref_name = ref.name
        ref_module = ref.module_path
        ref_file = ref.file_path
        ref_scope = ref.scope
        ref_imports = ref.imports
        
        # Skip resolving common Java types
        if ref_name in {'String', 'Object', 'Integer', 'Double', 'Boolean', 'List', 'Map'}:
            return None
        
        # 1. Check the imports
        for import_name, import_info in ref_imports.items():
            if import_name == ref_name:
                # Direct class import
                if import_info.get("import_type") == "class":
                    package = import_info.get("module", "")
                    full_class_name = f"{package}.{ref_name}"
                    
                    # Look for matching definition
                    for module, symbols in self.module_symbols.items():
                        for symbol in symbols:
                            if symbol.qualified_name == full_class_name:
                                return symbol
            
            # Check for wildcard imports
            elif import_name.startswith("__WILDCARD__"):
                package = import_info.get("module", "")
                
                # Check for classes in this package
                for module, symbols in self.module_symbols.items():
                    if module.startswith(package):
                        for symbol in symbols:
                            if symbol.name == ref_name:
                                return symbol
        
        # 2. Check for same-package references (implicit import)
        current_package = ref_module.rsplit(".", 1)[0] if "." in ref_module else ""
        if current_package:
            for module, symbols in self.module_symbols.items():
                if module.startswith(current_package):
                    for symbol in symbols:
                        if symbol.name == ref_name:
                            return symbol
        
        # 3. Check for local definitions in the current class/method
        if ref_scope:
            # Extract the current scope hierarchy
            scope_parts = ref_scope.split(".")
            current_scope = ""
            
            # Build scopes from innermost to outermost
            for i in range(len(scope_parts)-1, -1, -1):
                if current_scope:
                    current_scope = scope_parts[i] + "." + current_scope
                else:
                    current_scope = scope_parts[i]
                
                # Check for symbols defined in this scope
                for symbol in self.module_symbols.get(ref_module, []):
                    if symbol.name == ref_name and symbol.scope == current_scope:
                        return symbol
        
        # 4. Check for fields and methods of the current class
        containing_class = self.get_containing_class(ref_scope)
        if containing_class:
            # Find class definition
            class_scope = ""
            for part in ref_scope.split("."):
                if part.startswith("class:") and part.endswith(containing_class):
                    class_scope = part
                    break
            
            if class_scope:
                for symbol in self.module_symbols.get(ref_module, []):
                    if symbol.scope and class_scope in symbol.scope and symbol.name == ref_name:
                        return symbol
        
        # 5. Check for java.lang.* implicit imports
        java_lang_symbols = {
            "String", "Integer", "Double", "Float", "Boolean", "Byte", "Short", "Long",
            "Character", "Object", "Class", "Enum", "System", "Thread", "Runnable",
            "StringBuilder", "Exception", "RuntimeException", "Throwable"
        }
        if ref_name in java_lang_symbols:
            return None  # Would look up in java.lang package
        
        # 6. Fallback: check for any matching name in the entire codebase
        for module, symbols in self.module_symbols.items():
            for symbol in symbols:
                if symbol.name == ref_name:
                    return symbol
        
        return None
    
    def create_graph(self, repo_dir) -> nx.MultiDiGraph:
        """
        Create a graph representation of the repository with accurate cross-file references.
        """
        G = nx.MultiDiGraph()
        all_files = []
        all_definitions = []
        all_references = []
        
        # Reset counters for debugging
        self.ref_count = 0
        self.resolved_count = 0
        self.cross_file_ref_count = 0
        
        # First pass: collect all files
        logger.info("Collecting files...")
        for root, dirs, files in os.walk(repo_dir):
            # Skip hidden directories
            dirs[:] = [d for d in dirs if not d.startswith('.')]
            
            for file in files:
                file_path = os.path.join(root, file)
                rel_path = os.path.relpath(file_path, repo_dir)
                
                # Check if the file path contains common test directories or file names
                if (
                    'tests' in file_path.split(os.sep) or 
                    'test' in file_path.split(os.sep) or
                    file_path.startswith('test_') or 
                    file_path.endswith('_test.py') or
                    'test' in file_path.lower()
                ):                
                    continue
                # Skip hidden files and non-text files
                if file.startswith('.') or not self.is_text_file(file_path):
                    continue
                    
                all_files.append(file_path)
        
        # Second pass: first round to collect all definitions
        logger.info(f"Processing {len(all_files)} files for definitions...")
        for file_path in tqdm(all_files):
            rel_path = os.path.relpath(file_path, repo_dir)
            
            # Add file node to graph
            G.add_node(
                rel_path,
                file=rel_path,
                type="FILE",
                text=self.read_text(file_path) or "",
                line=0,
                end_line=0,
                name=os.path.basename(file_path)
            )
            
            # Process all tags in file - this builds our symbol tables
            file_tags = self.get_tags(file_path, rel_path)
            
            # Separate definitions
            file_defs = [tag for tag in file_tags if tag.kind == "def"]
            all_definitions.extend(file_defs)
        
        # Third pass: collect all references now that we have all definitions
        logger.info(f"Processing files for references...")
        for file_path in tqdm(all_files):
            rel_path = os.path.relpath(file_path, repo_dir)
            
            # Process all tags in file again to get references
            file_tags = self.get_tags(file_path, rel_path)
            
            # Get only references
            file_refs = [tag for tag in file_tags if tag.kind == "ref"]
            all_references.extend(file_refs)
        
        # Add definition nodes to graph
        logger.info(f"Adding {len(all_definitions)} definitions to graph...")
        for tag in tqdm(all_definitions):
            rel_path = tag.rel_fname
            
            # Create unique node ID for this definition
            if tag.containing_class and tag.type in ["method", "function"]:
                # For methods, include the class name
                node_id = f"{rel_path}:{tag.containing_class}.{tag.name}"
            elif tag.type in ["class", "interface"]:
                node_id = f"{rel_path}:{tag.name}"
            else:
                # For other entities, use qualified name if available
                if tag.qualified_name:
                    node_id = f"{rel_path}:{tag.qualified_name}"
                else:
                    node_id = f"{rel_path}:{tag.name}"
            
            # Add node with rich metadata
            G.add_node(
                node_id,
                file=rel_path,
                module_path=tag.module_path,
                line=tag.line,
                end_line=tag.end_line,
                type=tag.type.upper(),
                name=tag.name,
                signature=tag.signature,
                scope=tag.scope,
                symbol_id=tag.symbol_id,
                qualified_name=tag.qualified_name,
                containing_class=tag.containing_class
            )
            
            # Add CONTAINS relationship from file
            G.add_edge(
                rel_path,
                node_id,
                type="CONTAINS",
                weight=1.0
            )
        
        # Process references and create edges
        logger.info(f"Resolving {len(all_references)} references...")
        seen_relationships = set()
        
        for ref_tag in tqdm(all_references):
            self.ref_count += 1
            
            # Skip references without a name or with common names that cause noise
            if not ref_tag.name or ref_tag.name in ['self', 'cls', 'this', 'super']:
                continue
            
            # Find the best matching definition using our accurate resolver
            matched_def = self.resolve_reference(ref_tag)
            
            if matched_def:
                self.resolved_count += 1
                
                # Determine source node ID (where the reference is)
                ref_file = ref_tag.rel_fname
                
                # Determine source entity (the entity containing the reference)
                if ref_tag.containing_class and "function:" in ref_tag.scope or "method:" in ref_tag.scope:
                    # It's a method reference
                    method_name = ref_tag.scope.split(":")[-1]
                    source_node_id = f"{ref_file}:{ref_tag.containing_class}.{method_name}"
                elif ref_tag.containing_class:
                    # It's a class-level reference
                    source_node_id = f"{ref_file}:{ref_tag.containing_class}"
                elif "function:" in ref_tag.scope:
                    # It's a function reference
                    function_name = ref_tag.scope.split(":")[-1]
                    source_node_id = f"{ref_file}:{function_name}"
                else:
                    # Default to file level
                    source_node_id = ref_file
                
                # Determine target node ID (the definition)
                def_file = ""
                
                # The definition can be either a Symbol or a Tag
                if isinstance(matched_def, SymbolDefinition):
                    def_file = self.get_rel_fname(matched_def.file_path)
                    def_name = matched_def.name
                    def_qualified_name = matched_def.qualified_name
                    def_containing_class = matched_def.containing_class
                else:
                    def_file = matched_def.rel_fname
                    def_name = matched_def.name
                    def_qualified_name = matched_def.qualified_name
                    def_containing_class = matched_def.containing_class
                
                # Create target node ID based on definition type
                if def_containing_class and def_name not in def_containing_class:
                    # For methods, include the class name
                    target_node_id = f"{def_file}:{def_containing_class}.{def_name}"
                elif def_qualified_name and def_name != def_qualified_name:
                    # Use qualified name if it's different from simple name
                    target_node_id = f"{def_file}:{def_qualified_name}"
                else:
                    # Default to simple name
                    target_node_id = f"{def_file}:{def_name}"
                
                # Skip self-references
                if source_node_id == target_node_id:
                    continue
                
                # Check if both source and target exist in different files
                if def_file != ref_file:
                    self.cross_file_ref_count += 1
                
                # Check if nodes exist, create if necessary
                if not G.has_node(source_node_id) and source_node_id != ref_file:
                    # This might be a dynamically detected reference container
                    G.add_node(
                        source_node_id,
                        file=ref_file,
                        type=self._infer_node_type(source_node_id),
                        name=source_node_id.split(":")[-1],
                        line=-1,  # Don't know exact line
                        end_line=-1,
                        inferred=True
                    )
                    
                    # Add CONTAINS relationship from file
                    if not G.has_edge(ref_file, source_node_id):
                        G.add_edge(
                            ref_file,
                            source_node_id,
                            type="CONTAINS",
                            weight=1.0
                        )
                
                if not G.has_node(target_node_id):
                    # Create node for definition if it doesn't exist yet
                    if isinstance(matched_def, SymbolDefinition):
                        G.add_node(
                            target_node_id,
                            file=def_file,
                            module_path=matched_def.module_path,
                            line=matched_def.line,
                            end_line=matched_def.end_line,
                            type=matched_def.symbol_type.upper(),
                            name=matched_def.name,
                            signature=matched_def.signature,
                            scope=matched_def.scope,
                            qualified_name=matched_def.qualified_name,
                            containing_class=matched_def.containing_class
                        )
                    else:
                        G.add_node(
                            target_node_id,
                            file=def_file,
                            module_path=matched_def.module_path,
                            line=matched_def.line,
                            end_line=matched_def.end_line,
                            type=matched_def.type.upper(),
                            name=matched_def.name,
                            signature=matched_def.signature,
                            scope=matched_def.scope,
                            qualified_name=matched_def.qualified_name,
                            containing_class=matched_def.containing_class
                        )
                    
                    # Add CONTAINS relationship from file
                    if not G.has_edge(def_file, target_node_id):
                        G.add_edge(
                            def_file,
                            target_node_id,
                            type="CONTAINS",
                            weight=1.0
                        )
                
                # Get node attributes from the graph
                source_node = G.nodes[source_node_id] if G.has_node(source_node_id) else {}
                target_node = G.nodes[target_node_id] if G.has_node(target_node_id) else {}

                # Determine relationship type
                rel_type = self._determine_relationship_type(
                    ref_tag,
                    matched_def,
                    source_node.get("type", ""),
                    target_node.get("type", "")
                )
                
                # Add the relationship edge with calculated weight
                self._add_relationship_edge(
                    G,
                    source_node_id,
                    target_node_id,
                    rel_type=rel_type,
                    source_type=source_node.get("type", ""),
                    target_type=target_node.get("type", ""),
                    cross_file=(def_file != ref_file),
                    name=ref_tag.name,
                    line=ref_tag.line,
                    end_line=ref_tag.end_line
                )
        
        # Log stats for debugging
        logger.info(f"Processed {self.ref_count} references")
        if self.ref_count > 0:
            logger.info(f"Successfully resolved {self.resolved_count} references ({self.resolved_count/self.ref_count*100:.1f}%)")
        else:
            logger.info(f"Successfully resolved {self.resolved_count} references (0.0%)")
        logger.info(f"Found {self.cross_file_ref_count} cross-file references")
        
        # Enrich with nuanced call graph data
        G = self.enrich_with_nuanced_callgraph(G, repo_dir)
        print(f"Shreya the repo_dir: {repo_dir}")
        
        logger.info(f"Final graph has {len(G.nodes)} nodes and {len(G.edges)} edges")
    
        # After all nodes and edges are created, enrich with accurate line numbers
        G = self.enrich_with_line_numbers(G, repo_dir)

        return G
    
    def _infer_node_type(self, node_id):
        """Infer node type from node_id format."""
        if ":" not in node_id:
            return "FILE"
            
        entity_name = node_id.split(":", 1)[1]
        
        if "." in entity_name:
            # Check if it's Class.method pattern
            class_part, method_part = entity_name.split(".", 1)
            if class_part[0].isupper():
                return "METHOD"
        
        # Check for class names (uppercase first letter)
        if entity_name[0].isupper():
            return "CLASS"
            
        # Likely a function or module-level entity
        return "FUNCTION"

    def is_text_file(self, file_path):
        """
        Check if a file is a text file.
        """
        def open_text_file(file_path):
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    f.read(1024)
                return True
            except UnicodeDecodeError:
                return False

        ext = file_path.split(".")[-1].lower() if "." in file_path else ""
        exclude_extensions = [
            "png", "jpg", "jpeg", "gif", "bmp", "tiff", "webp", "ico", "svg",
            "mp4", "avi", "mov", "wmv", "flv", "ipynb", "mp3", "wav", "pdf",
            "zip", "gz", "tar", "rar", "jar", "class", "pyc", "pyd", "so", "dll",
            "exe", "bin"
        ]
        include_extensions = [
            "py", "js", "ts", "jsx", "tsx", "c", "cs", "cpp", "h", "hpp", "java",
            "go", "rb", "rs", "php", "html", "htm", "css", "scss", "sass",
            "less", "json", "xml", "yaml", "yml", "toml", "ini", "cfg", "conf",
            "md", "markdown", "txt", "rst", "sh", "bash", "zsh", "sql", "swift",
            "kt", "scala", "lua", "pl", "pm", "r", "dart", "groovy", "m", "mm"
        ]
        
        if ext in exclude_extensions:
            return False
        elif ext in include_extensions or open_text_file(file_path):
            return True
        else:
            return False

    # def save_graph_as_parquet(self, G, output_path):
    #     """Save the graph as a Parquet file with nodes and edges tables."""
    #     import pandas as pd
    #     import numpy as np
        
    #     # Create nodes dataframe
    #     nodes_data = []
    #     for node_id, node_attrs in G.nodes(data=True):
    #         node_data = {"node_id": node_id}
    #         node_data.update(node_attrs)
    #         # Convert signature to string if it exists
    #         if 'signature' in node_data:
    #             node_data['signature'] = str(node_data['signature']) if node_data['signature'] is not None else None
    #         nodes_data.append(node_data)
        
    #     nodes_df = pd.DataFrame(nodes_data)
        
    #     # Create edges dataframe with deduplication
    #     edges_data = []
    #     seen_edges = set()  # Track unique edges
    #     for source, target, edge_attrs in G.edges(data=True):
    #         # Create a unique key for the edge based on source, target and type
    #         edge_key = (source, target, edge_attrs.get('type', 'UNKNOWN'))
            
    #         # Skip if we've seen this edge before
    #         if edge_key in seen_edges:
    #             continue
                
    #         seen_edges.add(edge_key)
            
    #         edge_data = {
    #             "source": source,
    #             "target": target
    #         }
    #         edge_data.update(edge_attrs)
            
    #         # Ensure cross_file is boolean
    #         if 'cross_file' in edge_data:
    #             edge_data['cross_file'] = bool(edge_data['cross_file'])
    #         else:
    #             edge_data['cross_file'] = False
                
    #         edges_data.append(edge_data)
        
    #     edges_df = pd.DataFrame(edges_data)
        
    #     # Fill NA/NaN values in cross_file column with False
    #     edges_df['cross_file'] = edges_df['cross_file'].fillna(False)
        
    #     # Add statistics to track cross-file references by relationship type
    #     all_cross_file = edges_df[edges_df['cross_file'] == True]  # Explicit boolean comparison
    #     relationship_stats = {}
        
    #     # Get unique relationship types
    #     rel_types = edges_df['type'].unique()
        
    #     # Calculate statistics for each relationship type
    #     for rel_type in rel_types:
    #         total_rels = len(edges_df[edges_df['type'] == rel_type])
    #         cross_file_rels = len(all_cross_file[all_cross_file['type'] == rel_type])
            
    #         relationship_stats[rel_type] = {
    #             'total': total_rels,
    #             'cross_file': cross_file_rels,
    #             'percentage': (cross_file_rels / total_rels * 100) if total_rels > 0 else 0
    #         }
        
    #     # Log statistics for each relationship type
    #     logger.info("Cross-file relationship statistics:")
    #     for rel_type, stats in relationship_stats.items():
    #         logger.info(f"{rel_type}: {stats['cross_file']} cross-file out of {stats['total']} total ({stats['percentage']:.1f}%)")
        
    #     # Log total cross-file relationships
    #     total_edges = len(edges_df)
    #     total_cross_file = len(all_cross_file)
    #     logger.info(f"Total cross-file relationships: {total_cross_file} out of {total_edges} ({total_cross_file/total_edges*100:.1f}%)")
        
    #     # Ensure directory exists
    #     import os
    #     os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
    #     # Save as parquet files
    #     nodes_df.to_parquet(f"{output_path}_nodes.parquet")
    #     edges_df.to_parquet(f"{output_path}_edges.parquet")

    #     return nodes_df, edges_df

    # def save_graph_as_parquet(self, G: nx.MultiDiGraph, output_path_prefix: str) -> tuple[pd.DataFrame, pd.DataFrame]:
    #     """
    #     Save the graph as parquet files and return the dataframes.
    #     """
    #     # Convert graph to dataframes
    #     nodes_df, edges_df = self.to_dataframes(G)
        
    #     # Ensure required columns exist in edges_df
    #     if 'cross_file' not in edges_df.columns:
    #         edges_df['cross_file'] = False
        
    #     # Fill NA values
    #     edges_df['cross_file'] = edges_df['cross_file'].fillna(False)
    #     edges_df['weight'] = edges_df['weight'].fillna(1)
        
    #     # Save to parquet
    #     nodes_path = f"{output_path_prefix}_nodes.parquet"
    #     edges_path = f"{output_path_prefix}_edges.parquet"
        
    #     nodes_df.to_parquet(nodes_path)
    #     edges_df.to_parquet(edges_path)
        
    #     logger.info(f"Saved graph to {nodes_path} and {edges_path}")
    #     logger.info(f"Graph has {len(nodes_df)} nodes and {len(edges_df)} edges")
        
    #     return nodes_df, edges_df


    def save_graph_as_parquet(self, G: nx.MultiDiGraph, output_path_prefix: str) -> tuple[pd.DataFrame, pd.DataFrame]:
        """
        Save the graph as parquet files with comprehensive node and edge information.
        
        Args:
            G: NetworkX MultiDiGraph to save
            output_path_prefix: Path prefix for output files
            
        Returns:
            Tuple of (nodes_df, edges_df) DataFrames
        """
        import pandas as pd
        import numpy as np
        import os
        
        # Create nodes dataframe with all attributes
        nodes_data = []
        for node_id, node_attrs in G.nodes(data=True):
            node_data = {"node_id": node_id}
            node_data.update(node_attrs)
            # Convert signature to string if it exists
            if 'signature' in node_data:
                node_data['signature'] = str(node_data['signature']) if node_data['signature'] is not None else None
            nodes_data.append(node_data)
        
        nodes_df = pd.DataFrame(nodes_data)
        
        # Create edges dataframe with deduplication
        edges_data = []
        seen_edges = set()  # Track unique edges
        for source, target, edge_attrs in G.edges(data=True):
            # Create a unique key for the edge based on source, target and type
            edge_key = (source, target, edge_attrs.get('type', 'UNKNOWN'))
            
            # Skip if we've seen this edge before
            if edge_key in seen_edges:
                continue
                
            seen_edges.add(edge_key)
            
            edge_data = {
                "source": source,
                "target": target
            }
            edge_data.update(edge_attrs)
            
            # Ensure cross_file is boolean
            if 'cross_file' in edge_data:
                edge_data['cross_file'] = bool(edge_data['cross_file'])
            else:
                edge_data['cross_file'] = False
                
            edges_data.append(edge_data)
        
        edges_df = pd.DataFrame(edges_data)
        
        # Fill NA values
        edges_df['cross_file'] = edges_df['cross_file'].fillna(False)
        edges_df['weight'] = edges_df['weight'].fillna(1)
        
        # Add statistics to track cross-file references by relationship type
        all_cross_file = edges_df[edges_df['cross_file'] == True]  # Explicit boolean comparison
        relationship_stats = {}
        
        # Get unique relationship types
        rel_types = edges_df['type'].unique()
        
        # Calculate statistics for each relationship type
        for rel_type in rel_types:
            total_rels = len(edges_df[edges_df['type'] == rel_type])
            cross_file_rels = len(all_cross_file[all_cross_file['type'] == rel_type])
            
            relationship_stats[rel_type] = {
                'total': total_rels,
                'cross_file': cross_file_rels,
                'percentage': (cross_file_rels / total_rels * 100) if total_rels > 0 else 0
            }
        
        # Log statistics for each relationship type
        logger.info("Cross-file relationship statistics:")
        for rel_type, stats in relationship_stats.items():
            logger.info(f"{rel_type}: {stats['cross_file']} cross-file out of {stats['total']} total ({stats['percentage']:.1f}%)")
        
        # Log total cross-file relationships
        total_edges = len(edges_df)
        total_cross_file = len(all_cross_file)
        logger.info(f"Total cross-file relationships: {total_cross_file} out of {total_edges} ({total_cross_file/total_edges*100:.1f}%)")
        
        # Ensure directory exists
        os.makedirs(os.path.dirname(output_path_prefix), exist_ok=True)
        
        # Save as parquet files
        nodes_path = f"{output_path_prefix}_nodes.parquet"
        edges_path = f"{output_path_prefix}_edges.parquet"
        
        nodes_df.to_parquet(nodes_path)
        edges_df.to_parquet(edges_path)
        
        logger.info(f"Saved graph to {nodes_path} and {edges_path}")
        logger.info(f"Graph has {len(nodes_df)} nodes and {len(edges_df)} edges")
        
        return nodes_df, edges_df


    def extract_scope_hierarchy(self, code, lang, parser):
        """
        Extract the scope hierarchy from code with improved accuracy.
        Returns a dictionary mapping line numbers to their scope hierarchy.
        """
        if not code or not lang:
            return {}
        
        scope_map = {}
        
        try:
            # Parse code to get the AST
            tree = parser.parse(bytes(code, "utf-8"))
            
            # Define a recursive function to extract scopes
            def traverse_node(node, parent_scope="", context=None):
                scope = parent_scope
                context = context or {}
                
                # Update scope based on node type and language
                scope_updated = False
                
                if lang == "python":
                    if node.type == "class_definition":
                        name_node = node.child_by_field_name("name")
                        if name_node:
                            class_name = name_node.text.decode("utf-8")
                            scope = f"{parent_scope}.class:{class_name}" if parent_scope else f"class:{class_name}"
                            scope_updated = True
                            context["current_class"] = class_name
                    
                    elif node.type == "function_definition":
                        name_node = node.child_by_field_name("name")
                        if name_node:
                            func_name = name_node.text.decode("utf-8")
                            scope = f"{parent_scope}.function:{func_name}" if parent_scope else f"function:{func_name}"
                            scope_updated = True
                            
                            # Store method information if we're in a class
                            if "current_class" in context:
                                context["current_method"] = func_name
                                context["in_method"] = True
                
                elif lang == "javascript" or lang == "typescript":
                    if node.type == "class_declaration":
                        name_node = node.child_by_field_name("name")
                        if name_node:
                            class_name = name_node.text.decode("utf-8")
                            scope = f"{parent_scope}.class:{class_name}" if parent_scope else f"class:{class_name}"
                            scope_updated = True
                            context["current_class"] = class_name
                    
                    elif node.type in ["function_declaration", "method_definition"]:
                        name_node = node.child_by_field_name("name")
                        if name_node:
                            func_name = name_node.text.decode("utf-8")
                            node_type = "function" if node.type == "function_declaration" else "method"
                            scope = f"{parent_scope}.{node_type}:{func_name}" if parent_scope else f"{node_type}:{func_name}"
                            scope_updated = True
                            
                            if node_type == "method" and "current_class" in context:
                                context["current_method"] = func_name
                                context["in_method"] = True
                
                elif lang == "java":
                    if node.type == "class_declaration":
                        name_node = node.child_by_field_name("name")
                        if name_node:
                            class_name = name_node.text.decode("utf-8")
                            scope = f"{parent_scope}.class:{class_name}" if parent_scope else f"class:{class_name}"
                            scope_updated = True
                            context["current_class"] = class_name
                    
                    elif node.type == "method_declaration":
                        name_node = node.child_by_field_name("name")
                        if name_node:
                            method_name = name_node.text.decode("utf-8")
                            scope = f"{parent_scope}.method:{method_name}" if parent_scope else f"method:{method_name}"
                            scope_updated = True
                            
                            if "current_class" in context:
                                context["current_method"] = method_name
                                context["in_method"] = True
                
                # Add this node's scope to the scope map
                start_line = node.start_point[0]
                end_line = node.end_point[0]
                
                # Clone context to avoid reference issues
                node_context = context.copy()
                
                for line in range(start_line, end_line + 1):
                    scope_map[line] = {
                        "scope": scope,
                        "context": node_context
                    }
                
                # Process children with updated scope
                new_context = context.copy()
                for child in node.children:
                    traverse_node(child, scope, new_context)
                
                # Reset method context when exiting method
                if scope_updated and "in_method" in context:
                    context.pop("current_method", None)
                    context.pop("in_method", None)
            
            # Start traversal from the root
            traverse_node(tree.root_node)
            
            return scope_map
            
        except Exception as e:
            logger.error(f"Error extracting scope hierarchy: {e}")
            return {}

    def _determine_relationship_type(
        self,
        ref_tag: Tag,
        matched_def: SymbolDefinition,
        source_type: str,
        target_type: str
    ) -> str:
        """
        Determine the type of relationship between two entities.
        
        Args:
            ref_tag: Tag object representing the reference
            matched_def: SymbolDefinition object representing the definition
            source_type: Type of the source node
            target_type: Type of the target node
        
        Returns:
            str: The relationship type from RelationshipType constants
        """
        # Get context safely, defaulting to empty dict if not present
        ctx = getattr(ref_tag, 'context', {}) or {}
        
        # Check for inheritance and implementation relationships
        if ctx.get("current_class"):
            if getattr(matched_def, 'kind', '') == "interface":
                return RelationshipType.IMPLEMENTS
            if getattr(matched_def, 'type', '') == "class":
                return RelationshipType.INHERITS
        
        # Check for method overrides
        if (source_type in ["METHOD", "FUNCTION"] and 
            target_type in ["METHOD", "FUNCTION"] and 
            ctx.get("in_method")):
            # Check if method names match but classes differ
            if (ctx.get("current_method") == getattr(matched_def, 'name', '') and 
                ctx.get("current_class") != getattr(matched_def, 'containing_class', '')):
                return RelationshipType.OVERRIDES
        
        # Check for method calls
        if (getattr(ref_tag, 'kind', '') == "ref" and 
            getattr(matched_def, 'type', '') in ["method", "function"]):
            return RelationshipType.CALLS
        
        # Check for dependency injection or field references
        if (getattr(ref_tag, 'type', '') == "identifier" and 
            getattr(matched_def, 'type', '') in ["field", "property"]):
            return RelationshipType.DEPENDS_ON
        
        # Check for loose coupling through interfaces or events
        if (getattr(matched_def, 'type', '') == "interface" or 
            getattr(matched_def, 'kind', '') == "event"):
            return RelationshipType.ASSOCIATES
        
        # Default to basic reference
        return RelationshipType.REFERENCES
        logger.error(f"Error extracting scope hierarchy: {e}")
        return {}

#-----------
    def _resolve_method_call(self, ref, current_scope, imports):
        """
        Enhanced method resolution that considers object context
        
        Args:
            ref: The method reference
            current_scope: Current lexical scope
            imports: Import information
            
        Returns:
            Resolved method definition or None
        """
        # Extract receiver if this is a method call (e.g., "obj.method")
        receiver = None
        if "." in ref.name:
            parts = ref.name.split(".", 1)
            receiver, method_name = parts[0], parts[1]
        else:
            method_name = ref.name
            
        if not receiver:
            # No explicit receiver, might be an implicit self/this call in a method
            if "current_class" in ref.context and "current_method" in ref.context:
                # Check if it's a call to another method in the same class
                current_class = ref.context["current_class"]
                
                # Look for the method in the current class
                for symbol in self.module_symbols.get(ref.module_path, []):
                    if (symbol.name == method_name and 
                        symbol.containing_class == current_class and
                        symbol.type in ["method", "function"]):
                        return symbol
                        
            # Check for inherited methods
            if "current_class" in ref.context:
                inherited_method = self._find_inherited_method(
                    ref.module_path, 
                    ref.context["current_class"],
                    method_name
                )
                if inherited_method:
                    return inherited_method
        else:
            # We have an explicit receiver, determine its type
            receiver_type = self._infer_variable_type(
                receiver, 
                ref.module_path, 
                current_scope,
                ref.context
            )
            
            if receiver_type:
                # Find methods in the receiver's class
                for symbol in self.module_symbols.get(receiver_type["module_path"], []):
                    if (symbol.name == method_name and 
                        symbol.containing_class == receiver_type["class_name"] and
                        symbol.type in ["method", "function"]):
                        return symbol
        
        # If we reached here, we could not resolve the method accurately
        # Rather than falling back to any random matching method,
        # we should return None to avoid false positives
        return None


    def _infer_variable_type(self, var_name, module_path, scope, context=None):
        """
        Infer the type of a variable through static analysis.
        
        Args:
            var_name: Variable name to analyze
            module_path: Current module
            scope: Current lexical scope
            context: Analysis context
            
        Returns:
            Dict with class_name and module_path if type can be inferred, None otherwise
        """
        context = context or {}
        
        # Special case for 'self' or 'this' keywords
        if var_name in ['self', 'this'] and "current_class" in context:
            return {
                "class_name": context["current_class"],
                "module_path": module_path
            }
        
        # Check variable assignments in current scope
        assignments = self._find_variable_assignments(var_name, module_path, scope)
        
        for assignment in assignments:
            # If assigned from a constructor call
            if assignment.get("kind") == "constructor":
                class_name = assignment.get("class_name")
                if class_name:
                    return {
                        "class_name": class_name,
                        "module_path": assignment.get("module_path", module_path)
                    }
            
            # If this is a typed declaration (e.g., in Java)
            if assignment.get("kind") == "declaration" and "type_name" in assignment:
                type_name = assignment.get("type_name")
                if type_name:
                    # Resolve the type name to a module
                    return {
                        "class_name": type_name,
                        "module_path": self._resolve_class_module(type_name, module_path)
                    }
        
        # Check function parameters if in a method context
        if "current_method" in context and "current_class" in context:
            # Find the method declaration
            method_name = context["current_method"]
            class_name = context["current_class"]
            
            for symbol in self.module_symbols.get(module_path, []):
                if (symbol.name == method_name and 
                    symbol.containing_class == class_name and
                    symbol.type in ["method", "function"]):
                    
                    # Parse the method signature to find parameters
                    signature = getattr(symbol, 'signature', '{}')
                    try:
                        if isinstance(signature, str) and signature.startswith('{'):
                            signature_dict = eval(signature)  # Convert string representation to dict
                            params_list = signature_dict.get('params', [])
                            
                            # Look for our variable in the parameters
                            for param in params_list:
                                # Check if this parameter matches our variable
                                # This is a simplified check - would need more robust parsing
                                if var_name in param:
                                    # Try to extract type information
                                    param_type = self._extract_param_type_simple(param)
                                    if param_type:
                                        return {
                                            "class_name": param_type,
                                            "module_path": self._resolve_class_module(param_type, module_path)
                                        }
                    except:
                        pass
        
        # If we couldn't determine the type, return None
        return None

    def _extract_param_type_simple(self, param_str):
        """
        Extract type information from a parameter string using simple heuristics.
        
        Args:
            param_str: Parameter string (e.g., "int x", "String name", "x: Type")
            
        Returns:
            Type name if found, None otherwise
        """
        if not param_str:
            return None
        
        # Java-style: "Type name"
        if " " in param_str:
            parts = param_str.split(" ", 1)
            # Check if first part looks like a type (capitalized or primitive)
            first_part = parts[0].strip()
            if (first_part[0].isupper() or 
                first_part in ["int", "float", "double", "boolean", "string", "char"]):
                return first_part
        
        # Python-style annotation: "name: Type"
        if ":" in param_str:
            parts = param_str.split(":", 1)
            type_part = parts[1].strip()
            # Remove default value if present
            if "=" in type_part:
                type_part = type_part.split("=")[0].strip()
            if type_part:
                return type_part
        
        # Cannot determine type
        return None



    def _find_variable_assignments(self, var_name, module_path, scope):
        """
        Find all assignments to a variable within a scope using node traversal rather than complex queries.
        
        Args:
            var_name: Variable name to find
            module_path: Module path
            scope: Current scope
            
        Returns:
            List of assignment details
        """
        assignments = []
        
        # Read the source file
        file_path = self.module_path_to_file_path(module_path)
        if not file_path:
            return assignments
            
        source_code = self.read_text(file_path)
        if not source_code:
            return assignments
        
        # Parse the source code
        lang = filename_to_lang(file_path)
        parser = get_parser(lang)
        
        if not parser:
            return assignments
        
        try:
            tree = parser.parse(bytes(source_code, "utf-8"))
            
            # Create a visitor function to traverse the AST
            def visit_node(node, current_scope=""):
                node_type = node.type
                
                # Track assignments based on language-specific patterns
                if lang == "python":
                    if node_type == "assignment":
                        # Python assignment: left = right
                        left_nodes = []
                        right_nodes = []
                        
                        for child in node.children:
                            if child.type == "=" or child.type == "operator":
                                # Split into left and right based on equals sign
                                left_found = True
                            elif not left_found:
                                left_nodes.append(child)
                            else:
                                right_nodes.append(child)
                        
                        # Check if our variable is being assigned
                        for left_node in left_nodes:
                            if left_node.type == "identifier" and left_node.text.decode("utf-8") == var_name:
                                # Found an assignment to our variable
                                assignment_info = {"kind": "unknown"}
                                
                                # Analyze what's being assigned
                                for right_node in right_nodes:
                                    if right_node.type == "call":
                                        # Function or constructor call
                                        func_node = None
                                        for call_child in right_node.children:
                                            if call_child.type == "identifier":
                                                func_node = call_child
                                                break
                                            elif call_child.type == "attribute":
                                                # Method call: obj.method()
                                                for attr_child in call_child.children:
                                                    if attr_child.type == "identifier" and attr_child.text.decode("utf-8") == "method":
                                                        func_node = attr_child
                                                        break
                                        
                                        if func_node:
                                            func_name = func_node.text.decode("utf-8")
                                            
                                            # Check if this is likely a constructor (capitalized name)
                                            if func_name and func_name[0].isupper():
                                                assignment_info = {
                                                    "kind": "constructor",
                                                    "class_name": func_name,
                                                    "module_path": self._resolve_class_module(func_name, module_path)
                                                }
                                            else:
                                                # Regular function call
                                                assignment_info = {
                                                    "kind": "function_call",
                                                    "function_name": func_name
                                                }
                                
                                assignments.append(assignment_info)
                    
                    elif node_type == "for_statement" or node_type == "for_in_clause":
                        # Handle for loop variable assignments
                        target_node = None
                        for child in node.children:
                            if child.type == "identifier" and child.text.decode("utf-8") == var_name:
                                # Variable assigned in a for loop
                                target_node = child
                                break
                        
                        if target_node:
                            # Simplified - would need to analyze the iterable to determine type
                            assignments.append({
                                "kind": "for_loop_var",
                                "scope": current_scope
                            })
                
                elif lang in ["javascript", "typescript"]:
                    # Handle JavaScript/TypeScript variable declarations
                    if node_type in ["variable_declarator", "lexical_declaration"]:
                        name_node = None
                        value_node = None
                        
                        for child in node.children:
                            if child.type == "identifier" and child.text.decode("utf-8") == var_name:
                                name_node = child
                            elif child.type in ["value", "initializer", "call_expression"]:
                                value_node = child
                        
                        if name_node and value_node:
                            # Found a variable declaration with our name
                            assignments.append({
                                "kind": "declaration",
                                "scope": current_scope
                            })
                    
                    # Handle assignments
                    elif node_type == "assignment_expression":
                        left_node = None
                        right_node = None
                        
                        for child in node.children:
                            if child.type == "identifier" and child.text.decode("utf-8") == var_name:
                                left_node = child
                            elif child.type == "=" or child.type == "operator":
                                pass
                            else:
                                right_node = child
                        
                        if left_node and right_node:
                            # Found an assignment to our variable
                            assignment_info = {"kind": "unknown"}
                            
                            # Analyze what's being assigned
                            if right_node.type == "new_expression" or right_node.type == "call_expression":
                                # Constructor or function call
                                for call_child in right_node.children:
                                    if call_child.type == "identifier":
                                        func_name = call_child.text.decode("utf-8")
                                        
                                        if right_node.type == "new_expression" or func_name[0].isupper():
                                            # Constructor call
                                            assignment_info = {
                                                "kind": "constructor",
                                                "class_name": func_name,
                                                "module_path": self._resolve_class_module(func_name, module_path)
                                            }
                                        else:
                                            # Function call
                                            assignment_info = {
                                                "kind": "function_call",
                                                "function_name": func_name
                                            }
                            
                            assignments.append(assignment_info)
                
                elif lang == "java":
                    # Handle Java variable declarations
                    if node_type == "variable_declaration":
                        type_node = None
                        name_node = None
                        
                        for child in node.children:
                            if child.type == "type_identifier":
                                type_node = child
                            elif child.type == "variable_declarator":
                                for var_child in child.children:
                                    if var_child.type == "identifier" and var_child.text.decode("utf-8") == var_name:
                                        name_node = var_child
                        
                        if type_node and name_node:
                            # Java variable with explicit type
                            assignments.append({
                                "kind": "declaration",
                                "type_name": type_node.text.decode("utf-8"),
                                "scope": current_scope
                            })
                    
                    # Handle assignments
                    elif node_type == "assignment_expression":
                        left_node = None
                        right_node = None
                        
                        for child in node.children:
                            if child.type == "identifier" and child.text.decode("utf-8") == var_name:
                                left_node = child
                            elif child.type == "=" or child.type == "operator":
                                pass
                            else:
                                right_node = child
                        
                        if left_node and right_node:
                            # Found an assignment to our variable
                            assignment_info = {"kind": "unknown"}
                            
                            # Analyze what's being assigned
                            if right_node.type == "object_creation_expression":
                                # Constructor call
                                type_node = None
                                for creation_child in right_node.children:
                                    if creation_child.type == "type_identifier":
                                        type_node = creation_child
                                        break
                                
                                if type_node:
                                    assignment_info = {
                                        "kind": "constructor",
                                        "class_name": type_node.text.decode("utf-8"),
                                        "module_path": self._resolve_class_module(type_node.text.decode("utf-8"), module_path)
                                    }
                            
                            assignments.append(assignment_info)
                
                # Recursively process children with updated scope info
                updated_scope = current_scope
                
                # Update scope based on node type and language
                if lang == "python":
                    if node_type == "class_definition":
                        name_node = node.child_by_field_name("name")
                        if name_node:
                            class_name = name_node.text.decode("utf-8")
                            updated_scope = f"{current_scope}.class:{class_name}" if current_scope else f"class:{class_name}"
                    
                    elif node_type == "function_definition":
                        name_node = node.child_by_field_name("name")
                        if name_node:
                            func_name = name_node.text.decode("utf-8")
                            updated_scope = f"{current_scope}.function:{func_name}" if current_scope else f"function:{func_name}"
                
                elif lang in ["javascript", "typescript"]:
                    if node_type == "class_declaration":
                        name_node = node.child_by_field_name("name")
                        if name_node:
                            class_name = name_node.text.decode("utf-8")
                            updated_scope = f"{current_scope}.class:{class_name}" if current_scope else f"class:{class_name}"
                    
                    elif node_type in ["function_declaration", "method_definition"]:
                        name_node = node.child_by_field_name("name")
                        if name_node:
                            func_name = name_node.text.decode("utf-8")
                            node_type_str = "function" if node_type == "function_declaration" else "method"
                            updated_scope = f"{current_scope}.{node_type_str}:{func_name}" if current_scope else f"{node_type_str}:{func_name}"
                
                elif lang == "java":
                    if node_type == "class_declaration":
                        name_node = node.child_by_field_name("name")
                        if name_node:
                            class_name = name_node.text.decode("utf-8")
                            updated_scope = f"{current_scope}.class:{class_name}" if current_scope else f"class:{class_name}"
                    
                    elif node_type == "method_declaration":
                        name_node = node.child_by_field_name("name")
                        if name_node:
                            method_name = name_node.text.decode("utf-8")
                            updated_scope = f"{current_scope}.method:{method_name}" if current_scope else f"method:{method_name}"
                
                # Recursively visit children
                for child in node.children:
                    visit_node(child, updated_scope)
            
            # Start the recursive traversal
            visit_node(tree.root_node)
            
        except Exception as e:
            # Log error but continue processing
            logger.error(f"Error analyzing assignments in {file_path}: {e}")
        
        return assignments

    def _resolve_class_module(self, class_name, current_module):
        """
        Resolve a class name to its defining module.
        Simplified implementation - would be more robust in practice.
        
        Args:
            class_name: Class name to resolve
            current_module: Current module context
            
        Returns:
            Module path where the class is defined
        """
        # First check local module
        for symbol in self.module_symbols.get(current_module, []):
            if symbol.name == class_name and symbol.type == "class":
                return current_module
        
        # Check import statements
        file_path = self.module_path_to_file_path(current_module)
        if file_path:
            imports = self.extract_imports(self.read_text(file_path), filename_to_lang(file_path), file_path)
            
            # Check direct imports
            if class_name in imports:
                import_info = imports[class_name]
                return import_info.get("module", current_module)
        
        # Fallback to built-in or default module
        if class_name in ["str", "int", "float", "list", "dict", "set", "tuple"]:
            return "builtins"
        elif class_name in ["String", "Integer", "Double", "Boolean", "List", "Map", "Set"]:
            return "java.lang" if filename_to_lang(file_path) == "java" else "global"
        
        # Could not resolve
        return current_module



    def _analyze_method_return_type(self, method_name, class_name, module_path):
        """
        Analyze a method to determine its return type
        
        Args:
            method_name: Method name
            class_name: Class containing the method
            module_path: Module path
            
        Returns:
            Dict with class_name and module_path if return type can be determined
        """
        # Find the method symbol
        method_symbol = None
        for symbol in self.module_symbols.get(module_path, []):
            if (symbol.name == method_name and 
                symbol.containing_class == class_name and
                symbol.type in ["method", "function"]):
                method_symbol = symbol
                break
                
        if not method_symbol:
            return None
        
        # Parse method signature for return type hints
        if hasattr(method_symbol, 'signature') and method_symbol.signature:
            signature = method_symbol.signature
            if isinstance(signature, str) and signature.startswith('{'):
                try:
                    signature_dict = eval(signature)  # Convert string representation to dict
                    if 'return_type' in signature_dict:
                        return_type = signature_dict['return_type']
                        
                        # If return type is a class reference, resolve it
                        return self._resolve_type_reference(return_type, module_path)
                except:
                    pass
        
        # Analyze method body for return statements
        file_path = self.module_path_to_file_path(module_path)
        if not file_path:
            return None
            
        source_code = self.read_text(file_path)
        if not source_code:
            return None
        
        # This would involve complex flow analysis of return statements
        # Implementation details would depend on language
        
        return None

    def _find_inherited_method(self, module_path, class_name, method_name):
        """
        Find a method in the inheritance hierarchy of a class
        
        Args:
            module_path: Module containing the class
            class_name: Class to check
            method_name: Method name to find
            
        Returns:
            Method symbol if found in any parent class
        """
        # First find the class symbol
        class_symbol = None
        for symbol in self.module_symbols.get(module_path, []):
            if symbol.name == class_name and symbol.type == "class":
                class_symbol = symbol
                break
                
        if not class_symbol:
            return None
        
        # Find parent classes
        parent_classes = self._find_parent_classes(module_path, class_name)
        
        # Check each parent class for the method
        for parent in parent_classes:
            parent_module_path = parent["module_path"]
            parent_class_name = parent["class_name"]
            
            # Look for the method in this parent
            for symbol in self.module_symbols.get(parent_module_path, []):
                if (symbol.name == method_name and 
                    symbol.containing_class == parent_class_name and
                    symbol.type in ["method", "function"]):
                    return symbol
                    
            # Recursively check parent's parents
            inherited = self._find_inherited_method(
                parent_module_path, 
                parent_class_name,
                method_name
            )
            if inherited:
                return inherited
        
        return None


    def _calculate_resolution_confidence(self, ref, candidate_def):
        """
        Calculate confidence score for a reference resolution with robust error handling.
        
        Args:
            ref: Reference being resolved
            candidate_def: Candidate definition
            
        Returns:
            Float between 0.0 and 1.0 indicating confidence
        """
        try:
            confidence = 0.0
            
            # Same file is highly confident
            if getattr(ref, 'file_path', '') == getattr(candidate_def, 'file_path', ''):
                confidence += 0.5
            
            # Same module adds confidence
            if getattr(ref, 'module_path', '') == getattr(candidate_def, 'module_path', ''):
                confidence += 0.3
            
            # Direct import path adds confidence
            try:
                if self._has_direct_import_path(ref, candidate_def):
                    confidence += 0.4
            except Exception as e:
                logger.debug(f"Error checking import path: {e}")
            
            # Correct lexical scope adds confidence
            try:
                if self._is_in_lexical_scope(ref, candidate_def):
                    confidence += 0.3
            except Exception as e:
                logger.debug(f"Error checking lexical scope: {e}")
            
            # Type inference match adds high confidence
            try:
                if self._type_inference_matches(ref, candidate_def):
                    confidence += 0.6
            except Exception as e:
                logger.debug(f"Error checking type inference: {e}")
            
            # Common method names reduce confidence
            common_method_names = {"get", "set", "add", "remove", "update", "create", "delete", "find", "list"}
            if getattr(candidate_def, 'name', '') in common_method_names:
                confidence -= 0.4
            
            # Cap between 0.0 and 1.0
            return max(0.0, min(1.0, confidence))
            
        except Exception as e:
            # If anything goes wrong, return low confidence
            logger.error(f"Error calculating confidence: {e}")
            return 0.1


    def _has_direct_import_path(self, ref, candidate_def):
        """
        Determine whether there is a direct import path from the module containing
        the reference to the module containing the candidate definition.
        Simplified implementation focusing on direct import relationships.
        
        Args:
            ref: SymbolReference object representing the reference
            candidate_def: SymbolDefinition object representing the candidate definition
            
        Returns:
            Boolean indicating whether a direct import path exists
        """
        # Skip if we don't have import information
        if not hasattr(ref, 'imports') or not ref.imports:
            return False
        
        # Get module and symbol information
        ref_module = getattr(ref, 'module_path', '')
        def_module = getattr(candidate_def, 'module_path', '')
        def_name = getattr(candidate_def, 'name', '')
        
        # If they're in the same module, no import needed
        if ref_module == def_module:
            return True
        
        # Check for direct imports of the symbol
        if def_name in ref.imports:
            import_info = ref.imports[def_name]
            imported_module = import_info.get("module", "")
            
            # Direct match to the definition's module
            if imported_module == def_module:
                return True
        
        # Check for qualified references (module.symbol)
        ref_name = getattr(ref, 'name', '')
        if "." in ref_name:
            module_part, symbol_part = ref_name.split(".", 1)
            
            # Check if module_part is an alias for def_module
            for import_name, import_info in ref.imports.items():
                if (import_name == module_part and 
                    import_info.get("module", "") == def_module and
                    symbol_part == def_name):
                    return True
        
        # Check for wildcard imports
        for import_name, import_info in ref.imports.items():
            if ((import_name == "*" or import_name.startswith("__WILDCARD__")) and 
                import_info.get("module", "") == def_module):
                return True
        
        # No direct import path found
        return False


    def _is_in_lexical_scope(self, ref, candidate_def):
        """
        Determine whether a reference is within the lexical scope of a definition.
        Simplified implementation focusing on the most reliable scope relationships.
        
        Args:
            ref: SymbolReference object representing the reference
            candidate_def: SymbolDefinition object representing the candidate definition
            
        Returns:
            Boolean indicating whether the reference is within lexical scope of the definition
        """
        # Skip if we don't have scope information
        if not hasattr(ref, 'scope') or not hasattr(candidate_def, 'scope'):
            return False
        
        ref_scope = getattr(ref, 'scope', '')
        def_scope = getattr(candidate_def, 'scope', '')
        
        # If they're in different modules, they're not in the same lexical scope
        # unless it's an imported symbol, which is handled elsewhere
        if getattr(ref, 'module_path', '') != getattr(candidate_def, 'module_path', ''):
            return False
        
        # Case 1: Exact same scope
        if ref_scope == def_scope:
            return True
        
        # Case 2: Definition is at module level (global scope)
        if not def_scope or def_scope.count('.') == 0:
            # Global definitions are accessible from anywhere in the module
            return True
        
        # Case 3: Reference is within a nested scope of the definition
        if ref_scope.startswith(def_scope + '.'):
            # The reference is in a nested scope of the definition
            return True
        
        # Case 4: Definition is in an outer scope of the reference
        # This handles lexical closure for nested functions
        ref_parts = ref_scope.split('.')
        def_parts = def_scope.split('.')
        
        if len(def_parts) < len(ref_parts):
            # Check if definition scope is a prefix of reference scope
            is_prefix = True
            for i in range(len(def_parts)):
                if def_parts[i] != ref_parts[i]:
                    is_prefix = False
                    break
            
            if is_prefix:
                return True
        
        # Case 5: Class member access
        if "class:" in def_scope:
            # Extract class name from definition scope
            class_parts = [part for part in def_scope.split('.') if part.startswith('class:')]
            if class_parts:
                def_class = class_parts[0].split(':', 1)[1]
                
                # Check if reference is in a method of this class
                context = getattr(ref, 'context', {})
                if "current_class" in context and context["current_class"] == def_class:
                    return True
        
        # Not in lexical scope
        return False



    def _type_inference_matches(self, ref, candidate_def):
        """
        Determine whether the inferred type of a reference matches the candidate definition.
        Simplified implementation that focuses on the most common and reliable cases.
        
        Args:
            ref: SymbolReference object representing the reference
            candidate_def: SymbolDefinition object representing the candidate definition
            
        Returns:
            Boolean indicating whether type inference confirms this is the correct resolution
        """
        # Skip if necessary attributes are missing
        if (not hasattr(ref, 'name') or not hasattr(candidate_def, 'name') or
            not hasattr(candidate_def, 'containing_class')):
            return False
        
        ref_name = ref.name
        def_name = candidate_def.name
        def_containing_class = getattr(candidate_def, 'containing_class', '')
        
        # Case 1: Method or field reference (obj.method or obj.field)
        if "." in ref_name:
            receiver, member = ref_name.split(".", 1)
            
            # Skip if the member name doesn't match the definition
            if member != def_name:
                return False
            
            # Handle special case for 'self' and 'this'
            if receiver in ['self', 'this'] and "current_class" in getattr(ref, 'context', {}):
                current_class = ref.context["current_class"]
                
                # Check if the definition is in this class or its parents
                if def_containing_class == current_class:
                    return True
                
                # Check parent classes
                try:
                    parent_classes = self._find_parent_classes(ref.module_path, current_class)
                    for parent in parent_classes:
                        if parent["class_name"] == def_containing_class:
                            return True
                except:
                    # If parent class resolution fails, continue with other checks
                    pass
            
            # Try to infer the type of the receiver
            try:
                context = getattr(ref, 'context', {})
                scope = getattr(ref, 'scope', '')
                
                # Special handling for common patterns
                if receiver in ['self', 'this'] and "current_class" in context:
                    # Self/this references the current class
                    if def_containing_class == context["current_class"]:
                        return True
                
                # For other receivers, use basic heuristics
                # For example, capitalized variable names often indicate class types
                if receiver[0].isupper() and receiver == def_containing_class:
                    return True
                
                # Check if the receiver is a known class name
                for module_path, symbols in self.module_symbols.items():
                    for symbol in symbols:
                        if symbol.name == receiver and symbol.type == "class":
                            # The receiver is a class name, likely a static reference
                            if def_containing_class == receiver:
                                return True
            except:
                # If inference fails, continue with other checks
                pass
        
        # Case 2: Constructor calls (ClassName())
        else:
            # If the reference is to a class name and the definition is a constructor
            if def_name == "__init__" and def_containing_class == ref_name:
                return True
            
            # If the definition is a class and the reference matches
            if getattr(candidate_def, 'type', '') == "class" and def_name == ref_name:
                return True
        
        # If we couldn't make a confident type inference match, return False
        return False

    def _extract_param_type(self, param_str):
        """
        Extract type information from a parameter string.
        
        Args:
            param_str: Parameter string (e.g., "int x", "String name")
            
        Returns:
            Type information if available, None otherwise
        """
        # This is a simplified implementation
        # In a real system, this would parse language-specific parameter syntax
        
        if not param_str:
            return None
        
        # Java-style: "Type name"
        if " " in param_str:
            parts = param_str.split(" ", 1)
            return {"type": parts[0]}
        
        # Python-style annotation: "name: Type"
        if ":" in param_str:
            parts = param_str.split(":", 1)
            return {"type": parts[1].strip()}
        
        # Cannot determine type
        return None

    def _types_compatible(self, type1, type2):
        """
        Check if two types are compatible (e.g., through inheritance or implicit conversion).
        
        Args:
            type1: First type information
            type2: Second type information
            
        Returns:
            Boolean indicating whether the types are compatible
        """
        # This is a simplified implementation
        # In a real system, this would handle type hierarchies, generics, etc.
        
        # If either type is None (unknown), assume compatible
        if not type1 or not type2:
            return True
        
        # Extract type names
        type1_name = type1.get("type", "") if isinstance(type1, dict) else str(type1)
        type2_name = type2.get("type", "") if isinstance(type2, dict) else str(type2)
        
        # Exact match
        if type1_name == type2_name:
            return True
        
        # Check for numeric type compatibility
        numeric_types = {"int", "float", "double", "long", "short", "byte", "Integer", "Double", "Float", "Long"}
        if type1_name in numeric_types and type2_name in numeric_types:
            return True
        
        # Check for string type compatibility
        string_types = {"str", "String", "string"}
        if type1_name in string_types and type2_name in string_types:
            return True
        
        # Would need inheritance checking for class types
        # This is a placeholder for that logic
        
        # Default to incompatible
        return False



    def _find_parent_classes(self, module_path, class_name):
        """
        Identify all parent classes of a given class by analyzing inheritance declarations.
        
        Args:
            module_path: Module containing the target class
            class_name: Name of the class to analyze
            
        Returns:
            List of dicts, each containing module_path and class_name for a parent class
        """
        parent_classes = []
        
        # First, find the class definition symbol
        class_symbol = None
        for symbol in self.module_symbols.get(module_path, []):
            if symbol.name == class_name and symbol.type == "class":
                class_symbol = symbol
                break
                
        if not class_symbol:
            return parent_classes
        
        # Retrieve the file and parse it to analyze inheritance
        file_path = self.module_path_to_file_path(module_path)
        if not file_path:
            return parent_classes
            
        source_code = self.read_text(file_path)
        if not source_code:
            return parent_classes
        
        # Parse the file using the appropriate language parser
        lang = filename_to_lang(file_path)
        parser = get_parser(lang)
        tree = parser.parse(bytes(source_code, "utf-8"))
        
        # Language-specific inheritance extraction
        if lang == "python":
            # Find the class definition node
            class_query = """
                (class_definition
                name: (identifier) @class_name
                superclasses: (argument_list (identifier) @parent_class)?) @class_def
            """
            query = get_language(lang).query(class_query)
            
            # Match our specific class
            for node, tag in query.captures(tree.root_node):
                if tag == "class_name" and node.text.decode("utf-8") == class_name:
                    # Found our class, now get its parent class node
                    class_node = node.parent
                    
                    # Find the superclasses section
                    for child in class_node.children:
                        if child.type == "argument_list":
                            # This contains the parent classes
                            for parent_node in child.children:
                                if parent_node.type == "identifier":
                                    parent_class_name = parent_node.text.decode("utf-8")
                                    
                                    # Resolve the parent class to its module
                                    parent_module = self._resolve_class_to_module(
                                        parent_class_name,
                                        module_path,
                                        class_symbol.scope
                                    )
                                    
                                    if parent_module:
                                        parent_classes.append({
                                            "class_name": parent_class_name,
                                            "module_path": parent_module
                                        })
                                    
        elif lang == "java":
            # Java inheritance pattern (extends and implements)
            class_query = """
                (class_declaration
                name: (identifier) @class_name
                superclass: (superclass (type_identifier) @parent_class)?
                interfaces: (interfaces (type_list (type_identifier) @interface)*)?) @class_def
            """
            query = get_language(lang).query(class_query)
            
            for node, tag in query.captures(tree.root_node):
                if tag == "class_name" and node.text.decode("utf-8") == class_name:
                    # Found our class
                    class_node = node.parent
                    
                    # Check for superclass (extends)
                    for child in class_node.children:
                        if child.type == "superclass":
                            for superclass_node in child.children:
                                if superclass_node.type == "type_identifier":
                                    parent_class_name = superclass_node.text.decode("utf-8")
                                    
                                    # Resolve to module
                                    parent_module = self._resolve_class_to_module(
                                        parent_class_name,
                                        module_path,
                                        class_symbol.scope
                                    )
                                    
                                    if parent_module:
                                        parent_classes.append({
                                            "class_name": parent_class_name,
                                            "module_path": parent_module
                                        })
                        
                        # Check for interfaces (implements)
                        if child.type == "interfaces":
                            for interface_list in child.children:
                                if interface_list.type == "type_list":
                                    for interface_node in interface_list.children:
                                        if interface_node.type == "type_identifier":
                                            interface_name = interface_node.text.decode("utf-8")
                                            
                                            # Resolve to module
                                            interface_module = self._resolve_class_to_module(
                                                interface_name,
                                                module_path,
                                                class_symbol.scope
                                            )
                                            
                                            if interface_module:
                                                parent_classes.append({
                                                    "class_name": interface_name,
                                                    "module_path": interface_module
                                                })
        
        elif lang in ["javascript", "typescript"]:
            # JavaScript/TypeScript inheritance pattern
            class_query = """
                (class_declaration
                name: (identifier) @class_name
                extends_clause: (extends_clause (identifier) @parent_class)?) @class_def
            """
            query = get_language(lang).query(class_query)
            
            for node, tag in query.captures(tree.root_node):
                if tag == "class_name" and node.text.decode("utf-8") == class_name:
                    # Found our class
                    class_node = node.parent
                    
                    # Check for extends clause
                    for child in class_node.children:
                        if child.type == "extends_clause":
                            for ext_node in child.children:
                                if ext_node.type == "identifier":
                                    parent_class_name = ext_node.text.decode("utf-8")
                                    
                                    # Resolve to module
                                    parent_module = self._resolve_class_to_module(
                                        parent_class_name,
                                        module_path,
                                        class_symbol.scope
                                    )
                                    
                                    if parent_module:
                                        parent_classes.append({
                                            "class_name": parent_class_name,
                                            "module_path": parent_module
                                        })
                                        
            # For TypeScript, also check implemented interfaces
            if lang == "typescript":
                interface_query = """
                    (class_declaration
                    name: (identifier) @class_name
                    implements_clause: (implements_clause (type_identifier) @interface)?) @class_def
                """
                query = get_language(lang).query(interface_query)
                
                for node, tag in query.captures(tree.root_node):
                    if tag == "class_name" and node.text.decode("utf-8") == class_name:
                        # Check for implements clause
                        class_node = node.parent
                        
                        for child in class_node.children:
                            if child.type == "implements_clause":
                                for impl_node in child.children:
                                    if impl_node.type == "type_identifier":
                                        interface_name = impl_node.text.decode("utf-8")
                                        
                                        # Resolve to module
                                        interface_module = self._resolve_class_to_module(
                                            interface_name,
                                            module_path,
                                            class_symbol.scope
                                        )
                                        
                                        if interface_module:
                                            parent_classes.append({
                                                "class_name": interface_name,
                                                "module_path": interface_module
                                            })
        
        return parent_classes

    def _resolve_class_to_module(self, class_name, current_module, scope):
        """
        Resolve a class name to its defining module by analyzing imports.
        
        Args:
            class_name: Class name to resolve
            current_module: Module containing the reference
            scope: Current lexical scope
            
        Returns:
            Module path where the class is defined
        """
        # First check if class is defined in current module
        for symbol in self.module_symbols.get(current_module, []):
            if symbol.name == class_name and symbol.type == "class":
                return current_module
        
        # Check import statements for this class
        file_path = self.module_path_to_file_path(current_module)
        if not file_path:
            return None
            
        # Extract imports from this file
        imports = self.extract_imports(self.read_text(file_path), filename_to_lang(file_path), file_path)
        
        # Check if the class was directly imported
        if class_name in imports:
            import_info = imports[class_name]
            imported_module = import_info.get("module", "")
            
            # Verify the class exists in that module
            for symbol in self.module_symbols.get(imported_module, []):
                if symbol.name == class_name and symbol.type == "class":
                    return imported_module
        
        # Check for wildcard imports
        for import_name, import_info in imports.items():
            if import_name == "*" or import_name.startswith("__WILDCARD__"):
                imported_module = import_info.get("module", "")
                
                # Check if the class exists in that module
                for symbol in self.module_symbols.get(imported_module, []):
                    if symbol.name == class_name and symbol.type == "class":
                        return imported_module
        
        # Check if it's from the default module for this language
        lang = filename_to_lang(file_path)
        if lang == "java" and class_name in ["Object", "String", "Exception"]:
            return "java.lang"
        
        # Fallback check: scan all modules
        for module_path, symbols in self.module_symbols.items():
            for symbol in symbols:
                if symbol.name == class_name and symbol.type == "class":
                    # Check if this module is imported somehow
                    is_imported = False
                    for import_name, import_info in imports.items():
                        if import_info.get("module", "") == module_path:
                            is_imported = True
                            break
                    
                    if is_imported:
                        return module_path
        
        # Could not resolve
        return None

    def _resolve_type_reference(self, type_name, current_module, scope=None, imports=None):
        """
        Resolve a type reference to its defining module and class.
        
        Args:
            type_name: Type reference string (potentially qualified)
            current_module: Module containing the reference
            scope: Current lexical scope (optional)
            imports: Import information (optional)
            
        Returns:
            Dict with class_name and module_path if resolved, None otherwise
        """
        # Handle None type
        if not type_name or type_name in ["None", "null", "undefined", "void"]:
            return None
        
        # Handle primitive types
        primitive_types = {
            "int", "float", "bool", "str", "string", "boolean", "number", 
            "double", "char", "byte", "short", "long", "Integer", "Double", 
            "String", "Boolean", "Object"
        }
        if type_name in primitive_types:
            return {
                "class_name": type_name,
                "module_path": self._get_default_module_for_primitive(type_name, current_module)
            }
        
        # Handle generic types (List[str], Dict[str, int], etc.)
        if "[" in type_name and "]" in type_name:
            # Extract the base type
            base_type = type_name.split("[")[0].strip()
            
            # Resolve the base type
            return self._resolve_type_reference(base_type, current_module, scope, imports)
        
        # Handle union types (X | Y, X or Y)
        if "|" in type_name:
            # Take the first type in the union for simplicity
            first_type = type_name.split("|")[0].strip()
            return self._resolve_type_reference(first_type, current_module, scope, imports)
        
        # Handle optional types (Optional[X])
        if type_name.startswith("Optional[") and type_name.endswith("]"):
            inner_type = type_name[9:-1].strip()
            return self._resolve_type_reference(inner_type, current_module, scope, imports)
        
        # Handle fully qualified names (module.Class)
        if "." in type_name:
            module_part, class_part = type_name.rsplit(".", 1)
            
            # Check if the module part is an import alias
            if imports:
                for import_name, import_info in imports.items():
                    if import_name == module_part:
                        # This is an alias, resolve it
                        module_path = import_info.get("module", "")
                        
                        # Check if the class exists in that module
                        for symbol in self.module_symbols.get(module_path, []):
                            if symbol.name == class_part and symbol.type == "class":
                                return {
                                    "class_name": class_part,
                                    "module_path": module_path
                                }
            
            # Try interpreting literally as a module.class reference
            potential_module = module_part
            
            # Check if the class exists in that module
            for symbol in self.module_symbols.get(potential_module, []):
                if symbol.name == class_part and symbol.type == "class":
                    return {
                        "class_name": class_part,
                        "module_path": potential_module
                    }
        
        # Handle simple class names
        # First, check if the class is defined in the current module
        for symbol in self.module_symbols.get(current_module, []):
            if symbol.name == type_name and symbol.type == "class":
                return {
                    "class_name": type_name,
                    "module_path": current_module
                }
        
        # If imports are provided, check if the class was imported
        if imports:
            if type_name in imports:
                import_info = imports[type_name]
                imported_module = import_info.get("module", "")
                
                # Verify the class exists in that module
                for symbol in self.module_symbols.get(imported_module, []):
                    if symbol.name == type_name and symbol.type == "class":
                        return {
                            "class_name": type_name,
                            "module_path": imported_module
                        }
            
            # Check for wildcard imports
            for import_name, import_info in imports.items():
                if import_name == "*" or import_name.startswith("__WILDCARD__"):
                    imported_module = import_info.get("module", "")
                    
                    # Check if the class exists in that module
                    for symbol in self.module_symbols.get(imported_module, []):
                        if symbol.name == type_name and symbol.type == "class":
                            return {
                                "class_name": type_name,
                                "module_path": imported_module
                            }
        
        # Fallback: scan all modules for this class
        # This is risky but sometimes necessary if imports are incomplete
        for module_path, symbols in self.module_symbols.items():
            for symbol in symbols:
                if symbol.name == type_name and symbol.type == "class":
                    # If we find it, we should verify that this module is imported
                    # or is in the same package as the current module
                    
                    # Check if they're in the same package
                    current_package = ".".join(current_module.split(".")[:-1])
                    symbol_package = ".".join(module_path.split(".")[:-1])
                    
                    if current_package == symbol_package:
                        return {
                            "class_name": type_name,
                            "module_path": module_path
                        }
        
        # Could not resolve the type
        return None

    def _get_default_module_for_primitive(self, type_name, current_module):
        """
        Get the default module for primitive types based on language.
        
        Args:
            type_name: Primitive type name
            current_module: Current module context
            
        Returns:
            Module path for the primitive type
        """
        # Get the file to determine language
        file_path = self.module_path_to_file_path(current_module)
        if not file_path:
            return "builtins"
            
        lang = filename_to_lang(file_path)
        
        if lang == "python":
            return "builtins"
        elif lang == "java":
            if type_name in ["String", "Integer", "Double", "Boolean", "Object"]:
                return "java.lang"
            return "primitive"
        elif lang in ["javascript", "typescript"]:
            return "global"
        
        # Default fallback
        return "builtins"

    def _normalize_filepath(self, filepath: str, repo_dir: str) -> str:
        """Normalize filepath to be relative to repo root and use forward slashes."""
        try:
            rel_path = os.path.relpath(filepath, repo_dir)
            return rel_path.replace('\\', '/')
        except ValueError:
            # Handle case where filepath is not under repo_dir
            return filepath.replace('\\', '/')

    def _extract_namespace_parts(self, namespace: str) -> tuple[str, str, str]:
        """
        Extract module, class and method/function names from a namespace.
        Returns (module_path, class_name, method_name)
        """
        # Remove leading dots and split
        clean_namespace = namespace.lstrip('.')
        parts = clean_namespace.split('.')
        
        # Handle different cases
        if len(parts) <= 1:
            return clean_namespace, "", ""
        
        # Look for class name (uppercase first letter)
        class_idx = -2
        for i in range(len(parts)-1, -1, -1):
            if parts[i][0].isupper():
                class_idx = i
                break
        
        if class_idx >= 0:
            module_path = '.'.join(parts[:class_idx])
            class_name = parts[class_idx]
            method_name = '.'.join(parts[class_idx+1:])
        else:
            module_path = '.'.join(parts[:-1])
            class_name = ""
            method_name = parts[-1]
        
        return module_path, class_name, method_name


    def enrich_with_line_numbers(self, G: nx.MultiDiGraph, repo_dir: str) -> nx.MultiDiGraph:
        """
        Enrich the existing graph with accurate line numbers and source text using tree-sitter parsing.
        Should be called after the initial graph creation.
        """
        logger.info("Enriching graph with accurate line numbers and source text...")
        
        # Log repo directory for debugging
        logger.info(f"Repository directory: {repo_dir}")
        if not os.path.exists(repo_dir):
            logger.error(f"Repository directory does not exist: {repo_dir}")
        
        # First pass: try to enrich line numbers and text
        file_nodes = defaultdict(list)
        for node_id in G.nodes():
            node_data = G.nodes[node_id]
            # Skip class nodes entirely
            if node_data.get('type') == 'CLASS':
                continue
            
            rel_file_path = node_data.get('file')
            if rel_file_path:
                abs_file_path = os.path.join(repo_dir, rel_file_path)
                file_nodes[abs_file_path].append(node_id)
            else:
                logger.warning(f"Node {node_id} has no file path")
        
        logger.info(f"Found {len(file_nodes)} unique files to process")
        
        # Process files and update line numbers and text
        files_read = 0
        files_failed = 0
        
        for abs_file_path, nodes in tqdm(file_nodes.items()):
            try:
                # Check if file exists
                if not os.path.exists(abs_file_path):
                    logger.error(f"File does not exist: {abs_file_path}")
                    files_failed += 1
                    continue
                
                # Log file details
                logger.debug(f"Processing file: {abs_file_path}")
                logger.debug(f"File exists: {os.path.exists(abs_file_path)}")
                logger.debug(f"File size: {os.path.getsize(abs_file_path) if os.path.exists(abs_file_path) else 'N/A'}")
                logger.debug(f"File permissions: {oct(os.stat(abs_file_path).st_mode)[-3:] if os.path.exists(abs_file_path) else 'N/A'}")
                
                # Skip if no source code
                source_code = self.read_text(abs_file_path)
                if not source_code:
                    logger.warning(f"Could not read content from {abs_file_path}")
                    files_failed += 1
                    
                    # Try to diagnose why read_text failed
                    try:
                        with open(abs_file_path, "rb") as f:
                            first_bytes = f.read(100)
                        logger.warning(f"First bytes of file: {first_bytes}")
                        logger.warning(f"This might indicate a binary file or encoding issue")
                    except Exception as e:
                        logger.warning(f"Failed to read even binary content: {e}")
                    
                    continue
                
                files_read += 1
                logger.debug(f"Successfully read file content ({len(source_code)} bytes)")
                
                # Split source code into lines for easier extraction
                source_lines = source_code.splitlines()
                
                # Get language and parser
                lang = filename_to_lang(abs_file_path)
                if not lang or lang not in ["python", "py"]:  # Extend language support as needed
                    logger.warning(f"Unsupported language for file {abs_file_path}: {lang}")
                    continue
                
                logger.debug(f"Detected language: {lang}")
                
                try:
                    parser = get_parser(lang)
                    tree = parser.parse(bytes(source_code, "utf-8"))
                    logger.debug(f"Successfully parsed file with tree-sitter")
                except Exception as e:
                    logger.error(f"Failed to parse file with tree-sitter: {e}")
                    continue
                
                # Process each node in the file
                nodes_processed = 0
                nodes_matched = 0
                
                for node_id in nodes:
                    nodes_processed += 1
                    node_data = G.nodes[node_id]
                    name = node_data.get('name')
                    node_type = node_data.get('type', '').lower()
                    containing_class = node_data.get('containing_class')
                    
                    logger.debug(f"Processing node: {node_id}, name: {name}, type: {node_type}")
                    
                    # Find corresponding tree-sitter node

                    def find_matching_node(ts_node):
                        # Handle nested function definitions (e.g., "Reader.read_to_next_empty_line.is_empty")
                        if "." in name and name.count(".") > 1 and node_type == "method":
                            # Split into parts: class, method, inner_function
                            parts = name.split(".")
                            class_name = parts[0]
                            method_name = parts[1]
                            inner_function_name = parts[2]  # Assuming only one level of nesting for now
                            
                            logger.debug(f"Looking for inner function: {inner_function_name} in method: {method_name} in class: {class_name}")
                            
                            # First find the class node
                            def find_class_node(node):
                                if node.type == "class_definition":
                                    for child in node.children:
                                        if child.type == "identifier" and child.text.decode('utf-8') == class_name:
                                            return node
                                
                                for child in node.children:
                                    result = find_class_node(child)
                                    if result:
                                        return result
                                return None
                            
                            class_node = find_class_node(tree.root_node)
                            if not class_node:
                                logger.warning(f"Could not find class {class_name} in file")
                                return None
                            
                            # Then find the method within the class
                            def find_method_in_class(node):
                                if node.type == "function_definition":
                                    for child in node.children:
                                        if child.type == "identifier" and child.text.decode('utf-8') == method_name:
                                            return node
                                
                                for child in node.children:
                                    if child.type != "class_definition":  # Don't go into nested classes
                                        result = find_method_in_class(child)
                                        if result:
                                            return result
                                return None
                            
                            method_node = find_method_in_class(class_node)
                            if not method_node:
                                logger.warning(f"Could not find method {method_name} in class {class_name}")
                                return None
                            
                            # Finally find the inner function within the method
                            def find_inner_function(node):
                                if node.type == "function_definition":
                                    for child in node.children:
                                        if child.type == "identifier" and child.text.decode('utf-8') == inner_function_name:
                                            return node
                                
                                for child in node.children:
                                    result = find_inner_function(child)
                                    if result:
                                        return result
                                return None
                            
                            return find_inner_function(method_node)
                        
                        # Handle simple class.method case (e.g., "KDE.__init__")
                        elif "." in name and node_type == "method":
                            # Split the name into class and method parts
                            class_name, method_name = name.split(".", 1)
                            
                            logger.debug(f"Looking for method: {method_name} in class: {class_name}")
                            
                            # First find the class node
                            def find_class_node(node):
                                if node.type == "class_definition":
                                    for child in node.children:
                                        if child.type == "identifier" and child.text.decode('utf-8') == class_name:
                                            return node
                                
                                for child in node.children:
                                    result = find_class_node(child)
                                    if result:
                                        return result
                                return None
                            
                            class_node = find_class_node(tree.root_node)
                            if not class_node:
                                logger.warning(f"Could not find class {class_name} in file")
                                return None
                            
                            # Then find the method within the class
                            def find_method_in_class(node):
                                if node.type == "function_definition":
                                    for child in node.children:
                                        if child.type == "identifier" and child.text.decode('utf-8') == method_name:
                                            return node
                                
                                for child in node.children:
                                    if child.type != "class_definition":  # Don't go into nested classes
                                        result = find_method_in_class(child)
                                        if result:
                                            return result
                                return None
                            
                            return find_method_in_class(class_node)
                        
                        # Original logic for non-method nodes
                        if ts_node.type in ["class_definition", "function_definition", "method_definition"]:
                            node_name = None
                            for child in ts_node.children:
                                if child.type == "identifier":
                                    node_name = child.text.decode('utf-8')
                                    break
                            
                            # Match based on name and type
                            if node_name == name:
                                if ts_node.type == "class_definition" and node_type == "class":
                                    return ts_node
                                elif ts_node.type in ["function_definition", "method_definition"]:
                                    # For methods, check containing class
                                    if containing_class:
                                        parent = ts_node.parent
                                        while parent:
                                            if parent.type == "class_definition":
                                                class_name = None
                                                for child in parent.children:
                                                    if child.type == "identifier":
                                                        class_name = child.text.decode('utf-8')
                                                        break
                                                if class_name == containing_class:
                                                    return ts_node
                                                break
                                            parent = parent.parent
                                    else:
                                        return ts_node
                        
                        # Recurse through children
                        for child in ts_node.children:
                            result = find_matching_node(child)
                            if result:
                                return result
                        return None

                    # Find and update line numbers
                    matching_node = find_matching_node(tree.root_node)
                    if matching_node:
                        nodes_matched += 1
                        # Update node attributes with accurate line numbers
                        G.nodes[node_id]['start_line'] = matching_node.start_point[0]
                        G.nodes[node_id]['end_line'] = matching_node.end_point[0]+1
                        
                        # After line numbers are set, extract the text
                        start_line = G.nodes[node_id].get('start_line', -1)
                        end_line = G.nodes[node_id].get('end_line', -1)
                        
                        if start_line >= 0 and end_line > 0 and start_line < len(source_lines):
                            # Extract text from source lines
                            node_text = '\n'.join(source_lines[start_line:end_line])
                            G.nodes[node_id]['text'] = node_text
                        else:
                            G.nodes[node_id]['text'] = ''
                            logger.warning(f"Invalid line numbers for node {node_id}: start={start_line}, end={end_line}, file_lines={len(source_lines)}")
                    # else:
                        # logger.warning(f"No matching tree-sitter node found for {node_id} (name: {name}, type: {node_type})")
                
                logger.debug(f"Processed {nodes_processed} nodes, matched {nodes_matched} nodes")

            except (FileNotFoundError, PermissionError) as e:
                logger.warning(f"Could not access file {abs_file_path}: {e}")
                files_failed += 1
                continue

            except Exception as e:
                logger.error(f"Error processing file {abs_file_path}: {e}")
                logger.error(f"Stack trace: {traceback.format_exc()}")
                files_failed += 1
                continue

        logger.info(f"File processing summary: {files_read} files read successfully, {files_failed} files failed")

        # Log statistics about the nodes without removing any
        class_nodes = sum(1 for _, data in G.nodes(data=True) if data.get('type') == 'CLASS')
        file_nodes = sum(1 for _, data in G.nodes(data=True) if data.get('type') == 'FILE')
        invalid_line_nodes = sum(1 for _, data in G.nodes(data=True) 
                                if data.get('type') != 'CLASS' and data.get('type') != 'FILE' and
                                (data.get('start_line', -1) == -1 or data.get('end_line', -1) == -1))
        valid_nodes = sum(1 for _, data in G.nodes(data=True) 
                         if data.get('type') != 'CLASS' and data.get('type') != 'FILE' and
                         data.get('start_line', -1) != -1 and data.get('end_line', -1) != -1)
        
        logger.info(f"Node statistics (without removal):")
        logger.info(f"  - Total nodes: {len(G.nodes())}")
        logger.info(f"  - CLASS nodes: {class_nodes}")
        logger.info(f"  - FILE nodes: {file_nodes}")
        logger.info(f"  - Nodes with invalid line numbers: {invalid_line_nodes}")
        logger.info(f"  - Nodes with valid line numbers: {valid_nodes}")


        # Second pass: remove nodes with invalid line numbers or class type
        # nodes_to_remove = []
        # for node_id in G.nodes():
        #     node_data = G.nodes[node_id]
        #     if (node_data.get('type') == 'CLASS' or  # Remove all class nodes
        #         (node_data.get('type') != 'FILE' and 
        #          (node_data.get('line', -1) == -1 or node_data.get('end_line', -1) == -1))):
        #         nodes_to_remove.append(node_id)
        
        # # Remove the invalid nodes and class nodes
        # for node_id in nodes_to_remove:
        #     G.remove_node(node_id)
        
        # logger.info(f"Removed {len(nodes_to_remove)} nodes (invalid line numbers or class types)")

        # logger.info(f"Node statistics (AFTER removal):")
        # logger.info(f"  - Total nodes: {len(G.nodes())}")
        # logger.info(f"  - CLASS nodes: {class_nodes}")
        # logger.info(f"  - FILE nodes: {file_nodes}")
        # logger.info(f"  - Nodes with invalid line numbers: {invalid_line_nodes}")
        # logger.info(f"  - Nodes with valid line numbers: {valid_nodes}")


        # Second pass: remove nodes with invalid line numbers or class type
        # nodes_to_remove = []
        # for node_id in G.nodes():
        #     node_data = G.nodes[node_id]
        #     if (node_data.get('type') == 'CLASS' or  # Remove all class nodes
        #         (node_data.get('type') != 'FILE' and 
        #         (node_data.get('start_line', -1) == -1 or node_data.get('end_line', -1) == -1))):
        #         nodes_to_remove.append(node_id)

        # # Remove the invalid nodes and class nodes
        # for node_id in nodes_to_remove:
        #     G.remove_node(node_id)

        # logger.info(f"Removed {len(nodes_to_remove)} nodes (invalid line numbers or class types)")

        # Recalculate statistics after removal
        class_nodes = sum(1 for _, data in G.nodes(data=True) if data.get('type') == 'CLASS')
        file_nodes = sum(1 for _, data in G.nodes(data=True) if data.get('type') == 'FILE')
        invalid_line_nodes = sum(1 for _, data in G.nodes(data=True) 
                                if data.get('type') != 'CLASS' and data.get('type') != 'FILE' and
                                (data.get('start_line', -1) == -1 or data.get('end_line', -1) == -1))
        valid_nodes = sum(1 for _, data in G.nodes(data=True) 
                        if data.get('type') != 'CLASS' and data.get('type') != 'FILE' and
                        data.get('start_line', -1) != -1 and data.get('end_line', -1) != -1)

        logger.info(f"Node statistics (AFTER removal):")
        logger.info(f"  - Total nodes: {len(G.nodes())}")
        logger.info(f"  - CLASS nodes: {class_nodes}")
        logger.info(f"  - FILE nodes: {file_nodes}")
        logger.info(f"  - Nodes with invalid line numbers: {invalid_line_nodes}")
        logger.info(f"  - Nodes with valid line numbers: {valid_nodes}")


        return G

    def to_dataframes(self, G: nx.MultiDiGraph) -> tuple[pd.DataFrame, pd.DataFrame]:
        """
        Convert the graph to nodes and edges dataframes.
        This will be called after enrich_with_line_numbers, so invalid nodes are already removed.
        """
        # Create nodes dataframe
        nodes_data = []
        for node_id in G.nodes():
            node_data = G.nodes[node_id]
            nodes_data.append({
                'node_id': node_id,
                'file': node_data.get('file', ''),
                'type': node_data.get('type', ''),
                'name': node_data.get('name', ''),
                'start_line': node_data.get('start_line', -1),
                'end_line': node_data.get('end_line', -1),
                'text': node_data.get('text', ''),  # Added text column
                # Add other attributes as needed
            })
        
        nodes_df = pd.DataFrame(nodes_data)
        
        # Create edges dataframe
        edges_data = []
        seen_edges = set()
        for source, target, edge_attrs in G.edges(data=True):
            edge_key = (source, target, edge_attrs.get('type', 'UNKNOWN'))
            if edge_key in seen_edges:
                continue
            seen_edges.add(edge_key)
            
            edge_data = {
                "source": source,
                "target": target
            }
            edge_data.update(edge_attrs)
            edges_data.append(edge_data)
        
        edges_df = pd.DataFrame(edges_data)
        
        return nodes_df, edges_df



    def enrich_with_nuanced_callgraph(self, G: nx.MultiDiGraph, repo_dir: str) -> nx.MultiDiGraph:
        """
        Enrich the graph with call relationships from nuanced call graph.
        """
        #------------------------------------
        logger.info("Initializing CodeGraph...")
        from call_graph.nuanced import CodeGraph
        
        # Initialize and wait for CodeGraph to complete
        result = CodeGraph.init(repo_dir)
        if not result.code_graph:
            logger.error("Failed to initialize CodeGraph")
            if result.errors:
                for error in result.errors:
                    logger.error(f"CodeGraph initialization error: {error}")
            return G
        
        logger.info("CodeGraph initialization completed successfully")
        #------------------------------------

        try:
            logger.info("Loading existing CodeGraph...")
            import json
            nuanced_graph_path = os.path.join(repo_dir, '.nuanced', 'nuanced-graph.json')
            
            if not os.path.exists(nuanced_graph_path):
                logger.warning("Nuanced call graph not found at .nuanced/nuanced-graph.json")
                return G
            
            logger.info("Loading nuanced graph file...")
            with open(nuanced_graph_path, 'r') as f:
                nuanced_graph = json.load(f)
            logger.info(f"Successfully loaded nuanced graph with {len(nuanced_graph)} entries")
            
            # Track statistics
            new_relationships = set()
            cross_file_count = 0
            
            # Process each entry in nuanced graph
            for source_node_id, data in nuanced_graph.items():
                source_file = data['filepath']
                
                # Skip if source node doesn't exist and we can't create it
                if not G.has_node(source_node_id):
                    # Try to infer node type
                    source_type = self._infer_node_type(source_node_id)
                    source_name = source_node_id.split(":")[-1]
                    
                    # Add inferred node
                    G.add_node(
                        source_node_id,
                        file=source_file,
                        type=source_type,
                        name=source_name,
                        line=-1,
                        end_line=-1,
                        inferred=True
                    )
                
                # Process all callees
                for target_node_id in data.get('callees', []):
                    # Skip if target node doesn't exist and we can't create it
                    if not G.has_node(target_node_id):
                        # Try to infer node type
                        target_type = self._infer_node_type(target_node_id)
                        target_name = target_node_id.split(":")[-1]
                        target_file = target_node_id.split(":")[0]
                        
                        # Add inferred node
                        G.add_node(
                            target_node_id,
                            file=target_file,
                            type=target_type,
                            name=target_name,
                            line=-1,
                            end_line=-1,
                            inferred=True
                        )
                    
                    # Add call relationship
                    rel_key = (source_node_id, target_node_id)
                    if rel_key not in new_relationships:
                        is_cross_file = source_file != target_node_id.split(":")[0]
                        if is_cross_file:
                            cross_file_count += 1
                        
                        self._add_relationship_edge(
                            G,
                            source_node_id,
                            target_node_id,
                            rel_type='CALLS',
                            source_type=G.nodes[source_node_id]['type'],
                            target_type=G.nodes[target_node_id]['type'],
                            cross_file=is_cross_file,
                            usage_count=1
                        )
                        new_relationships.add(rel_key)
            
            logger.info(f"Added {len(new_relationships)} new call relationships from nuanced graph")
            logger.info(f"Cross-file relationships: {cross_file_count}")
            
            if cross_file_count == 0:
                logger.warning("No cross-file relationships found! This might indicate an issue.")
                
        except Exception as e:
            logger.error(f"Error enriching graph with nuanced call data: {e}")
            import traceback
            logger.error(traceback.format_exc())
            
        return G

    def is_test_file(file_path: str) -> bool:
        """Check if a file path is a test file."""
        path_parts = file_path.split(os.sep)
        return any([
            'tests' in path_parts,
            'test' in path_parts,
            os.path.basename(file_path).startswith('test_'),
            os.path.basename(file_path).endswith('_test.py')
        ])
        #     logger.info(f"Added {len(new_relationships)} new call relationships from nuanced graph")
        #     logger.info(f"Cross-file relationships: {cross_file_count}")
            
        #     if cross_file_count == 0:
        #         logger.warning("No cross-file relationships found! This might indicate an issue.")
                
        # except Exception as e:
        #     logger.error(f"Error enriching graph with nuanced call data: {e}")
        #     import traceback
        #     logger.error(traceback.format_exc())
            
        # return G


    def _construct_node_id(self, file_path: str, node_info: dict) -> str:
        """Helper to construct node ID from nuanced graph format."""
        if not file_path:
            return None
            
        name = node_info.get('name')
        if not name:
            return None
            
        # Handle class methods
        containing_class = node_info.get('class')
        if containing_class:
            return f"{file_path}:{containing_class}.{name}"
            
        # Handle standalone functions
        return f"{file_path}:{name}"


def get_scm_fname(lang):
    """
    Get the path to the tree-sitter query schema file for the given language.
    """
    # Load the tags queries
    try:
        return Path(os.path.dirname(__file__)).joinpath(
            "queries", f"tree-sitter-{lang}-tags.scm"
        )
    except KeyError:
        return


class SimpleTokenCounter:
    """Simple token counter for testing."""
    def token_count(self, text):
        return len(text.split())


class SimpleIO:
    """Simple IO handler for testing."""
    def read_text(self, fname):
        try:
            with open(fname, "r", encoding="utf-8") as f:
                return f.read()
        except UnicodeDecodeError:
            logging.warning(f"Could not read {fname} as UTF-8. Skipping this file.")
            return ""

    def tool_error(self, message):
        logging.error(f"Error: {message}")

    def tool_output(self, message):
        logging.info(message)




if __name__ == "__main__":
    import argparse
    
    # repo_dir = "/Users/<USER>/work/startup/godzilla/adjacent/mem0/mem0"
    # output_path = "experiment/data_out/repo_mem"

    # repo_dir = "/Users/<USER>/work/startup/godzilla/bracket/bracket/graphrag"
    # output_path = "experiment/data_out/repo_graphrag"

    # repo_dir = "/Users/<USER>/work/startup/godzilla/adjacent/langchain/libs"
    # output_path = "experiment/data_out/repo_langchain"

    # repo_dir = "/Users/<USER>/work/startup/godzilla/adjacent/composio/python/composio"
    # output_path = "experiment/data_out/repo_composio"

    # repo_dir = "/Users/<USER>/work/startup/godzilla/adjacent/browser-use/browser_use"
    # output_path = "experiment/data_out/repo_browser_use"

    # repo_dir = "/Users/<USER>/work/startup/godzilla/adjacent/crewAI/src/crewai"
    # output_path = "experiment/data_out/repo_crewai"

    repo_dir = "/Users/<USER>/work/startup/godzilla/adjacent/aider/aider"
    output_path = "experiment/data_out/repo_aider"


    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Enhanced RepoMap Tool")

    parser.add_argument(
        "--repo", "-r", 
        # required=True,
        help="Path to the repository to analyze"
    )
    parser.add_argument(
        "--output", "-o",
        default="repo_graph",
        help="Output path for the generated graph files"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose output"
    )
    
    args = parser.parse_args()

    args.repo = repo_dir
    args.output = output_path

    # Create enhanced repo map
    repo_map = EnhancedRepoMap(
        root=args.repo,
        verbose=args.verbose,
        main_model=SimpleTokenCounter(),
        io=SimpleIO(),
    )
    
    # Create graph
    logging.info(f"Analyzing repository: {args.repo}")
    nx_graph = repo_map.create_graph(args.repo)
    
    # Save graph
    logging.info(f"Saving graph to {args.output}")
    try:
        repo_map.save_graph_as_parquet(nx_graph, args.output)
    except Exception as e:
        logging.info(f"Exception: {e}")
    logging.info("Done!")
