"""
Hierarchical Domain Trace Builder for Codebase

This module provides functionality to:
1. Read domain output YAML and build traces from top to bottom (domain -> sub-area -> sub-area...)
2. Read semantic_documented_functions.parquet to get function signatures and descriptions
3. Read hierarchical domain-to-file mappings to reduce the search space for each trace
4. Use an LLM to classify functions into domain traces with reduced search space

It can be used as:
1. A standalone script to process domain YAML and function parquet files
2. A module that can be integrated into the repository analysis flow
"""

import os
import yaml
import logging
import asyncio
import aiohttp
import time
import pandas as pd
import re
import math
import tiktoken
from typing import Dict, List, Any, Optional, Set, Tuple
from collections import defaultdict

# Import the original DomainTraceBuilder for compatibility
from bracket_core.domain_trace_builder import DomainTraceBuilder, DomainTrace, FunctionClassificationResult, StatusTracker, APIRequest
from bracket_core.enhanced_domain_trace_builder import EnhancedDomainTraceBuilder
# Import LLM clients and API key management
from bracket_core.llm.get_client import get_openrouter_client
from bracket_core.llm.api_keys import get_openai_api_key, get_openrouter_api_key

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HierarchicalDomainTraceBuilder(EnhancedDomainTraceBuilder):
    """
    Enhanced version of DomainTraceBuilder that uses hierarchical domain-specific file sets to reduce search space.
    """

    def __init__(
        self,
        domain_yaml_path: str,
        functions_parquet_path: str,
        output_path: str,
        domain_file_mappings_path: str,
        api_key: Optional[str] = None,
        model: str = "gpt-4o-mini",
        max_requests_per_minute: float = 2000,
        max_tokens_per_minute: float = 1000000,
        temperature: float = 0.0,
        fallback_to_original: bool = True,
        use_openrouter: bool = False,  # Set to False to ensure OpenAI is used
        openrouter_base_url: str = "https://openrouter.ai/api/v1",
        max_concurrent_tasks: int = 15,  # Maximum number of concurrent API tasks
        max_tokens_per_chunk: int = 70000,  # Maximum tokens per chunk
    ):
        """
        Initialize the hierarchical domain trace builder.

        Args:
            domain_yaml_path: Path to the domain YAML file
            functions_parquet_path: Path to the semantic_documented_functions.parquet file
            output_path: Path to save the classification output
            domain_file_mappings_path: Path to the hierarchical domain-to-file mappings YAML file
            api_key: API key (OpenAI or OpenRouter depending on use_openrouter)
            model: Model to use (OpenAI or OpenRouter model ID)
            max_requests_per_minute: Rate limit for API requests
            max_tokens_per_minute: Token rate limit for API
            temperature: Temperature for LLM generation
            fallback_to_original: Whether to fall back to the original approach if domain-file mappings are not available
            use_openrouter: Whether to use OpenRouter instead of OpenAI
            openrouter_base_url: Base URL for OpenRouter API
        """
        super().__init__(
            domain_yaml_path=domain_yaml_path,
            functions_parquet_path=functions_parquet_path,
            output_path=output_path,
            domain_file_mappings_path=domain_file_mappings_path,
            api_key=api_key,
            model=model,
            max_requests_per_minute=max_requests_per_minute,
            max_tokens_per_minute=max_tokens_per_minute,
            temperature=temperature,
            fallback_to_original=fallback_to_original,
        )
        self.use_openrouter = use_openrouter
        self.openrouter_base_url = openrouter_base_url
        self.hierarchical_mappings = {}
        self.domain_paths = {}
        self.trace_to_domain_path = {}
        self.normalized_file_paths = {}  # Map from normalized paths to original paths

        # Initialize OpenRouter client to None - we won't be using it
        self.openrouter_client = None
        # Disable OpenRouter usage regardless of the parameter
        self.use_openrouter = False
        # Store the new parameters
        self.max_concurrent_tasks = max_concurrent_tasks
        self.max_tokens_per_chunk = max_tokens_per_chunk
        # Create a semaphore to limit concurrent API calls
        self.api_semaphore = asyncio.Semaphore(max_concurrent_tasks)
        print(f"Hierarchical Domain Trace Builder using OpenAI with model: {model}")

    def _get_trace_level(self, trace_str: str) -> int:
        """
        Get the hierarchy level of a trace by its string representation.

        Args:
            trace_str: String representation of the trace

        Returns:
            Level in the hierarchy (1 for top-level, 2 for second level, etc.)
        """
        # Count the number of '>' to determine level
        # Format is typically: "Domain > Subdomain > Subsubdomain"
        if not trace_str or '>' not in trace_str:
            return 1

        return trace_str.count('>') + 1

    def _is_leaf_node(self, trace: DomainTrace, all_traces: List[DomainTrace]) -> bool:
        """
        Determine if a trace represents a leaf node in the domain hierarchy.
        A leaf node is one that doesn't have any child traces.

        Args:
            trace: The trace to check
            all_traces: List of all traces for comparison

        Returns:
            True if the trace is a leaf node, False otherwise
        """
        # Get the trace string
        trace_str = trace.trace_str

        # If this trace is a parent of any other trace, it's not a leaf node
        for other_trace in all_traces:
            if other_trace.trace_str != trace_str and other_trace.trace_str.startswith(trace_str):
                # This is a child trace, so the current trace is not a leaf
                return False

        # If we didn't find any child traces, this is a leaf node
        return True

    async def _call_openai_api(self, request_json: Dict[str, Any]) -> Tuple[Dict[str, Any], Optional[str]]:
        """Call the OpenAI API with the given request JSON."""
        # Always use the parent class implementation for OpenAI
        return await super()._call_openai_api(request_json)

    def _count_tokens(self, text: str) -> int:
        """Count the number of tokens in a text string."""
        try:
            encoding = tiktoken.get_encoding("cl100k_base")
            return len(encoding.encode(text))
        except Exception as e:
            logger.warning(f"Error counting tokens: {e}. Using approximate count.")
            # Fallback to approximate count (1 token ~= 4 chars)
            return len(text) // 4

    def _chunk_function_data(self, function_data: Dict[str, str]) -> List[Dict[str, str]]:
        """Split function data into chunks that fit within token limits."""
        logger.info(f"Chunking function data with {len(function_data)} functions")

        # If the data is small enough, return it as is
        full_text = "\n".join([f"{sig} - {desc}" for sig, desc in function_data.items()])
        total_tokens = self._count_tokens(full_text)

        if total_tokens <= self.max_tokens_per_chunk:
            logger.info(f"Function data fits in a single chunk ({total_tokens} tokens)")
            return [function_data]

        # Calculate number of chunks needed
        num_chunks = math.ceil(total_tokens / self.max_tokens_per_chunk)
        logger.info(f"Splitting function data into {num_chunks} chunks")

        # Get list of function keys
        function_keys = list(function_data.keys())
        functions_per_chunk = math.ceil(len(function_keys) / num_chunks)

        # Split the functions into chunks
        chunks = []
        for i in range(0, len(function_keys), functions_per_chunk):
            chunk_keys = function_keys[i:i+functions_per_chunk]
            chunk_data = {key: function_data[key] for key in chunk_keys}
            chunks.append(chunk_data)

        logger.info(f"Created {len(chunks)} chunks with approximately {functions_per_chunk} functions each")
        return chunks

    async def _process_chunk_with_semaphore(self, trace: DomainTrace, function_data_chunk: Dict[str, str], chunk_index: int) -> List[str]:
        """Process a chunk of function data with semaphore to limit concurrency."""
        try:
            async with self.api_semaphore:
                # Prepare function signatures and descriptions for LLM
                full_signature_set = "\n".join([f"{sig} - {desc}" for sig, desc in function_data_chunk.items()])

                # Create the API request
                request_json = {
                    "model": self.model,
                    "messages": [
                        {
                            "role": "system",
                            "content": """You are an expert software architect analyzing a codebase.
    Your task is to identify which functions belong to a specific domain trace.
    Analyze each function signature and description carefully, and determine if it belongs to the given domain trace.
    Include utility functions that are specifically designed for this domain.
    Return ONLY a list of function signatures that belong to the domain trace, one per line.
    Do not include any explanations, headers, or other text in your response.
    If no functions belong to the domain trace, return "No functions found".
    """
                        },
                        {
                            "role": "user",
                            "content": f"""Domain trace: {trace.trace_str}

    Here is the list of function signatures and their descriptions from the relevant domain (chunk {chunk_index}):

    {full_signature_set}

    Please list ONLY the function path (without descriptions) that belong to the domain trace "{trace.trace_str}".
    Return one function signature per line, with no additional text.
    """
                        }
                    ],
                    "temperature": self.temperature,
                    "max_tokens": 4000
                }

                # Call the OpenAI API
                logger.info(f"Calling OpenAI API for trace: {trace.trace_str} (chunk {chunk_index})")
                response_json, error = await self._call_openai_api(request_json)

                if error:
                    logger.error(f"API error for trace {trace.trace_str} (chunk {chunk_index}): {error}")
                    return []

                # Extract the response content
                content = response_json["choices"][0]["message"]["content"]

                # Parse the response to get the list of functions
                functions = []
                if content and content.strip() != "No functions found":
                    functions = [line.strip() for line in content.strip().split('\n') if line.strip()]

                logger.info(f"Found {len(functions)} functions for trace: {trace.trace_str} (chunk {chunk_index})")
                return functions

        except Exception as e:
            logger.error(f"Error processing chunk {chunk_index} for trace {trace.trace_str}: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return []

    def _normalize_file_path(self, file_path: str) -> str:
        """
        Normalize a file path to extract the unique part for matching.

        This function extracts the most specific part of the path that would be unique
        across different representations (absolute vs relative paths).

        Args:
            file_path: The file path to normalize

        Returns:
            Normalized file path for matching
        """
        # Handle empty paths
        if not file_path:
            return file_path

        # Store the original path
        original_path = file_path

        # Remove any leading path components like '../' or './'
        clean_path = re.sub(r'^(\.\./)+', '', file_path)
        clean_path = re.sub(r'^\./+', '', clean_path)

        # For absolute paths, create multiple normalized versions
        if file_path.startswith('/'):
            # Split the path into components
            path_parts = file_path.split('/')
            # Remove empty parts
            path_parts = [p for p in path_parts if p]

            # Create multiple normalized versions with different levels of specificity
            # This increases our chances of finding a match

            # Version 1: Last 3 components (or all if less than 3)
            significant_parts = path_parts[-min(3, len(path_parts)):]
            clean_path_v1 = '/'.join(significant_parts)
            self.normalized_file_paths[clean_path_v1] = original_path

            # Version 2: Last 2 components (or all if less than 2)
            significant_parts = path_parts[-min(2, len(path_parts)):]
            clean_path_v2 = '/'.join(significant_parts)
            self.normalized_file_paths[clean_path_v2] = original_path

            # Version 3: Just the filename
            if len(path_parts) > 0:
                clean_path_v3 = path_parts[-1]
                self.normalized_file_paths[clean_path_v3] = original_path

            # Use the most specific version as the return value
            clean_path = clean_path_v1

        # For relative paths with '../', create a version without those
        if '../' in file_path:
            # Count the number of '../' at the beginning
            parts = file_path.split('/')
            non_dotdot_idx = 0
            for i, part in enumerate(parts):
                if part != '..':
                    non_dotdot_idx = i
                    break

            # Extract the part after all the '../'
            if non_dotdot_idx < len(parts):
                clean_rel_path = '/'.join(parts[non_dotdot_idx:])
                self.normalized_file_paths[clean_rel_path] = original_path

        # Store the mapping from normalized path to original path
        self.normalized_file_paths[clean_path] = original_path

        # Also store mappings for just the filename and filename with parent dir
        # This helps with matching when paths are very different
        filename = os.path.basename(file_path)
        if filename:
            self.normalized_file_paths[filename] = original_path

            # Also store parent dir + filename
            parent_dir = os.path.basename(os.path.dirname(file_path))
            if parent_dir:
                parent_and_file = f"{parent_dir}/{filename}"
                self.normalized_file_paths[parent_and_file] = original_path

                # Also try with different separators
                self.normalized_file_paths[f"{parent_dir}\\{filename}"] = original_path

        # Add debug logging
        logger.debug(f"Normalized path: '{file_path}' -> '{clean_path}'")

        return clean_path

    def read_domain_file_mappings(self) -> Dict[str, List[str]]:
        """
        Read the hierarchical domain-to-file mappings YAML file.

        Returns:
            Dictionary mapping domain names to lists of file paths (flat mappings for backward compatibility)
        """
        logger.info(f"Reading hierarchical domain-to-file mappings YAML file: {self.domain_file_mappings_path}")
        try:
            with open(self.domain_file_mappings_path, 'r') as f:
                data = yaml.safe_load(f)

            # Extract flat mappings for backward compatibility
            flat_mappings_raw = data.get('flat_mappings', {})

            # Normalize file paths in flat mappings
            flat_mappings = {}
            for domain, file_paths in flat_mappings_raw.items():
                flat_mappings[domain] = [self._normalize_file_path(path) for path in file_paths]

            # Extract and normalize hierarchical mappings
            hierarchical_mappings_raw = data.get('hierarchical_mappings', {})
            self.hierarchical_mappings = {}

            for level, domains in hierarchical_mappings_raw.items():
                self.hierarchical_mappings[level] = {}
                for domain, file_paths in domains.items():
                    self.hierarchical_mappings[level][domain] = [self._normalize_file_path(path) for path in file_paths]

            # Extract domain paths
            self.domain_paths = data.get('domain_paths', {})

            logger.info(f"Read hierarchical domain-to-file mappings for {len(flat_mappings)} top-level domains")
            return flat_mappings
        except Exception as e:
            logger.error(f"Error reading hierarchical domain-to-file mappings YAML file: {e}")
            if self.fallback_to_original:
                logger.warning("Falling back to original approach without domain-file mappings")
                return {}
            else:
                raise

    def map_traces_to_domain_paths(self, traces: List[DomainTrace]) -> Dict[str, str]:
        """
        Map each trace to its full domain path.

        Args:
            traces: List of DomainTrace objects

        Returns:
            Dictionary mapping trace strings to domain paths
        """
        logger.info("Mapping traces to domain paths")

        trace_to_domain_path = {}

        for trace in traces:
            if not trace.trace:
                continue

            # Convert trace to domain path format
            domain_path = '/'.join(trace.trace)
            trace_to_domain_path[trace.trace_str] = domain_path

        logger.info(f"Mapped {len(trace_to_domain_path)} traces to domain paths")
        return trace_to_domain_path

    def get_domain_specific_functions_hierarchical(self, trace: DomainTrace) -> Set[str]:
        """
        Get the set of functions that belong to the domain of the given trace using hierarchical mappings.

        Args:
            trace: DomainTrace object

        Returns:
            Set of function signatures that belong to the domain
        """
        # Get the domain path for this trace
        domain_path = self.trace_to_domain_path.get(trace.trace_str, '')
        if not domain_path:
            logger.warning(f"No domain path found for trace: {trace.trace_str}")
            return set()

        # Determine the hierarchy level for this trace
        level = len(trace.trace)
        level_key = f"level_{level}"

        # Get the domain name at this level
        domain_name = trace.trace[-1] if trace.trace else ''

        # Get the files for this domain at this level
        domain_files = []
        if level_key in self.hierarchical_mappings:
            domain_files = self.hierarchical_mappings[level_key].get(domain_name, [])

        if not domain_files:
            logger.warning(f"No files found for domain path: {domain_path} at level {level}")
            return set()

        # Get the functions for these files
        domain_functions = set()
        matched_files = 0
        total_files = len(domain_files)

        # Add debug logging
        logger.info(f"Looking for functions in {total_files} files for domain path: {domain_path} at level {level}")

        for file_path in domain_files:
            # Try to get functions directly
            functions = self.file_to_functions.get(file_path, [])
            if functions:
                domain_functions.update(functions)
                matched_files += 1
                logger.debug(f"Found {len(functions)} functions for file: {file_path}")
                continue

            # If no functions found, try with the original path
            original_path = self.normalized_file_paths.get(file_path, '')
            if original_path and original_path in self.file_to_functions:
                functions = self.file_to_functions.get(original_path, [])
                domain_functions.update(functions)
                matched_files += 1
                logger.debug(f"Found {len(functions)} functions for original path: {original_path}")
                continue

            # If still no match, try a more aggressive approach - look for any path that contains
            # a significant portion of this path
            filename = os.path.basename(file_path)
            if filename:
                # Look for any path in file_to_functions that ends with this filename
                for func_file_path in self.file_to_functions.keys():
                    if func_file_path.endswith(f"/{filename}") or func_file_path.endswith(f"\\{filename}"):
                        functions = self.file_to_functions.get(func_file_path, [])
                        domain_functions.update(functions)
                        matched_files += 1
                        logger.debug(f"Found {len(functions)} functions by filename match: {func_file_path}")
                        break

        logger.info(f"Found {len(domain_functions)} functions in {matched_files}/{total_files} files for domain path: {domain_path}")

        # If no functions were found, log the file paths to help with debugging
        if len(domain_functions) == 0:
            logger.warning(f"No functions found for domain path: {domain_path}. File paths: {domain_files}")
            logger.warning(f"Available file paths in function data: {list(self.file_to_functions.keys())[:5]}...")

        return domain_functions

    async def classify_functions_with_hierarchical_search_space(
        self,
        traces: List[DomainTrace],
        df: pd.DataFrame
    ) -> FunctionClassificationResult:
        """
        Classify functions into domain traces using LLM with hierarchical reduced search space.
        Uses file-by-file processing and handles large function sets appropriately.
        Only processes leaf level nodes to improve efficiency.

        Args:
            traces: List of DomainTrace objects
            df: DataFrame containing function data

        Returns:
            FunctionClassificationResult containing the classification results
        """
        logger.info("Classifying functions into domain traces with hierarchical reduced search space (leaf nodes only)")

        # Initialize result
        result = FunctionClassificationResult()

        # Process each trace, but only for leaf nodes
        for trace in traces:
            # Skip non-leaf nodes
            if not self._is_leaf_node(trace, traces):
                logger.info(f"Skipping non-leaf node trace: {trace.trace_str}")
                result.classifications[trace.trace_str] = []
                continue

            logger.info(f"Processing leaf node trace: {trace.trace_str}")

            # Process this leaf node using the file-by-file approach
            trace_functions = await self._process_leaf_node_file_by_file(trace, df)

            # Store the results
            result.classifications[trace.trace_str] = trace_functions
            logger.info(f"Found {len(trace_functions)} functions for trace: {trace.trace_str}")

        # Set success flag
        result.success = True

        return result

    async def _process_leaf_node_file_by_file(self, trace: DomainTrace, df: pd.DataFrame) -> List[str]:
        """
        Process a leaf node domain by analyzing each file individually.
        For each file, get all functions and try to process them together.
        If the functions from a file exceed the token limit, process them individually.

        Args:
            trace: The leaf node domain trace to process
            df: DataFrame containing function data

        Returns:
            List of function signatures that belong to the domain trace
        """
        # Get the domain path for this trace
        domain_path = self.trace_to_domain_path.get(trace.trace_str, '')
        if not domain_path:
            logger.warning(f"No domain path found for trace: {trace.trace_str}")
            return []

        # Determine the hierarchy level for this trace
        level = len(trace.trace)
        level_key = f"level_{level}"

        # Get the domain name at this level
        domain_name = trace.trace[-1] if trace.trace else ''

        # Get the files for this domain at this level
        domain_files = []
        if level_key in self.hierarchical_mappings:
            domain_files = self.hierarchical_mappings[level_key].get(domain_name, [])

        if not domain_files:
            logger.warning(f"No files found for domain path: {domain_path} at level {level}")
            return []

        logger.info(f"Processing {len(domain_files)} files for domain trace: {trace.trace_str}")

        # Process each file individually
        all_trace_functions = []
        file_processing_tasks = []

        for file_path in domain_files:
            task = self._process_file_for_trace(trace, file_path, df)
            file_processing_tasks.append(task)

        # Wait for all file processing tasks to complete
        file_results = await asyncio.gather(*file_processing_tasks)

        # Combine results from all files
        for functions in file_results:
            all_trace_functions.extend(functions)

        # Remove duplicates while preserving order
        seen = set()
        unique_functions = []
        for func in all_trace_functions:
            if func not in seen:
                seen.add(func)
                unique_functions.append(func)

        return unique_functions

    async def _process_file_for_trace(self, trace: DomainTrace, file_path: str, df: pd.DataFrame) -> List[str]:
        """
        Process a single file for a domain trace.
        Get all functions from the file and try to process them together.
        If the functions exceed the token limit, process them individually.

        Args:
            trace: The domain trace
            file_path: The file path to process
            df: DataFrame containing function data

        Returns:
            List of function signatures from this file that belong to the domain trace
        """
        # Get all functions for this file
        file_functions = set()

        # Normalize the input file path
        normalized_input_path = self._normalize_file_path(file_path)

        # Try to get functions directly
        functions = self.file_to_functions.get(file_path, [])
        if functions:
            file_functions.update(functions)
            logger.info(f"Found {len(functions)} functions directly for file: {file_path}")
        else:
            # If no functions found, try with the normalized path
            for func_file_path, funcs in self.file_to_functions.items():
                # Normalize each file path in file_to_functions
                normalized_func_path = self._normalize_file_path(func_file_path)

                # Check if the normalized paths match
                if normalized_func_path == normalized_input_path:
                    file_functions.update(funcs)
                    logger.info(f"Found {len(funcs)} functions via normalized path matching for file: {file_path} -> {func_file_path}")

            # If still no match, try a more aggressive approach
            if not file_functions:
                filename = os.path.basename(file_path)
                if filename:
                    # Look for any path in file_to_functions that ends with this filename
                    for func_file_path, funcs in self.file_to_functions.items():
                        if func_file_path.endswith(f"/{filename}") or func_file_path.endswith(f"\\{filename}"):
                            file_functions.update(funcs)
                            logger.info(f"Found {len(funcs)} functions via filename matching for file: {file_path} -> {func_file_path}")
                            break

        if not file_functions:
            logger.warning(f"No functions found for file: {file_path} in trace: {trace.trace_str}")
            return []

        logger.info(f"Processing {len(file_functions)} functions from file: {file_path} for trace: {trace.trace_str}")

        # Filter the DataFrame to only include functions from this file
        # Use node_id for matching as it contains the function signature
        file_df = df[df['node_id'].isin(file_functions)]

        if file_df.empty:
            logger.warning(f"No function data found in DataFrame for file: {file_path}")
            return []

        # Prepare function data with more context
        function_data = {}
        for _, row in file_df.iterrows():
            # Extract function signature from node_id
            node_id = row.get('node_id', '')
            if not node_id:
                continue

            # Use node_id as the signature
            signature = node_id

            # Get file path and other metadata
            file_path_from_df = row.get('file_path', '')
            module_path = row.get('module_path', '')
            containing_class = row.get('containing_class', '')

            # Get function description and text
            description = row.get('description', '')
            function_text = row.get('text', '')  # Note: it's 'text', not 'function_text'

            # Normalize the file path for better matching
            normalized_file_path = self._normalize_file_path(file_path_from_df) if file_path_from_df else file_path

            # Create a rich context for the LLM to make better judgments
            context = f"File: {normalized_file_path}\nFunction: {signature}\n"
            if module_path:
                context += f"Module: {module_path}\n"
            if containing_class:
                context += f"Class: {containing_class}\n"
            if description:
                context += f"Description: {description}\n"
            if function_text:
                context += f"Code:\n{function_text}\n"

            function_data[signature] = context

        # Check if the function data is small enough to process together
        full_text = "\n\n".join([f"{sig}\n{ctx}" for sig, ctx in function_data.items()])
        total_tokens = self._count_tokens(full_text)

        if total_tokens <= 50000:  # Process all functions together if under 50K tokens
            logger.info(f"Processing all {len(function_data)} functions together for file: {file_path} ({total_tokens} tokens)")
            return await self._process_file_functions_together(trace, function_data, file_path)
        else:
            # Process functions individually if the total is too large
            logger.info(f"Processing {len(function_data)} functions individually for file: {file_path} ({total_tokens} tokens)")
            return await self._process_file_functions_individually(trace, function_data, file_path)

    async def _process_file_functions_together(self, trace: DomainTrace, function_data: Dict[str, str], file_path: str) -> List[str]:
        """
        Process all functions from a file together in a single LLM call.

        Args:
            trace: The domain trace
            function_data: Dictionary mapping function signatures to their context
            file_path: The file path being processed

        Returns:
            List of function signatures that belong to the domain trace
        """
        try:
            async with self.api_semaphore:
                # Prepare function data for LLM
                full_function_set = "\n\n".join([f"{sig}\n{ctx}" for sig, ctx in function_data.items()])

                # Create the API request
                request_json = {
                    "model": self.model,
                    "messages": [
                        {
                            "role": "system",
                            "content": """You are an expert software architect analyzing a codebase.
    Your task is to identify which functions belong to a specific domain trace with the focus on the leaf node of the trace.
    Analyze each function signature, description, and code carefully, and determine if it belongs to the given domain trace.
    If a function is playing a direct implementation based role in the building of the leaf domain, include it.
    Include utility functions that are specifically designed for this domain.
    Return ONLY a list of function signatures that belong to the domain trace, one per line.
    Do not include any explanations, headers, or other text in your response.
    If no functions belong to the domain trace, return "No functions found".
    """
                        },
                        {
                            "role": "user",
                            "content": f"""Domain trace: {trace.trace_str}

    Here are the functions from file: {file_path}

    {full_function_set}

    Please list ONLY the function names (without descriptions or code) that belong to the domain trace "{trace.trace_str}".
    Return one function name per line, with no additional text.
    """
                        }
                    ],
                    "temperature": self.temperature,
                    "max_tokens": 4000
                }

                # Call the OpenAI API
                logger.info(f"Calling OpenAI API for file: {file_path} in trace: {trace.trace_str}")
                response_json, error = await self._call_openai_api(request_json)

                if error:
                    logger.error(f"API error for file {file_path} in trace {trace.trace_str}: {error}")
                    return []

                # Extract the response content
                content = response_json["choices"][0]["message"]["content"]

                # Parse the response to get the list of functions
                functions = []
                if content and content.strip() != "No functions found":
                    functions = [line.strip() for line in content.strip().split('\n') if line.strip()]

                logger.info(f"Found {len(functions)} functions for file: {file_path} in trace: {trace.trace_str}")
                return functions

        except Exception as e:
            logger.error(f"Error processing file {file_path} for trace {trace.trace_str}: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return []

    async def _process_file_functions_individually(self, trace: DomainTrace, function_data: Dict[str, str], file_path: str) -> List[str]:
        """
        Process each function from a file individually in separate LLM calls.

        Args:
            trace: The domain trace
            function_data: Dictionary mapping function signatures to their context
            file_path: The file path being processed

        Returns:
            List of function signatures that belong to the domain trace
        """
        # Process each function individually
        function_tasks = []
        for signature, context in function_data.items():
            task = self._process_single_function(trace, signature, context, file_path)
            function_tasks.append(task)

        # Wait for all function processing tasks to complete
        function_results = await asyncio.gather(*function_tasks)

        # Combine results - each result is either the function signature or None
        included_functions = [result for result in function_results if result]

        logger.info(f"Processed {len(function_data)} functions individually for file: {file_path}, included {len(included_functions)}")
        return included_functions

    async def _process_single_function(self, trace: DomainTrace, signature: str, context: str, file_path: str) -> Optional[str]:
        """
        Process a single function to determine if it belongs to a domain trace.

        Args:
            trace: The domain trace
            signature: The function signature
            context: The function context (description, code, etc.)
            file_path: The file path containing the function

        Returns:
            The function signature if it belongs to the domain trace, None otherwise
        """
        try:
            async with self.api_semaphore:
                # Create the API request
                request_json = {
                    "model": self.model,
                    "messages": [
                        {
                            "role": "system",
                            "content": """You are an expert software architect analyzing a codebase.
    Your task is to determine if a specific function belongs to a given domain trace.
    If the function is playing a direct implementation based role in the building of the leaf domain, include it.
    Include utility functions that are specifically designed for this domain.
    Analyze the function signature, description, and code carefully.
    Answer with ONLY 'yes' or 'no'.
    """
                        },
                        {
                            "role": "user",
                            "content": f"""Domain trace: {trace.trace_str}

    Function from file {file_path}:

    {context}

    Does this function belong to the domain trace "{trace.trace_str}"?
    Answer with ONLY 'yes' or 'no'.
    """
                        }
                    ],
                    "temperature": self.temperature,
                    "max_tokens": 10
                }

                # Call the OpenAI API
                response_json, error = await self._call_openai_api(request_json)

                if error:
                    logger.error(f"API error for function {signature} in file {file_path}: {error}")
                    return None

                # Extract the response content
                content = response_json["choices"][0]["message"]["content"].strip().lower()

                # Return the signature if the function belongs to the domain
                if content == 'yes':
                    return signature
                else:
                    return None

        except Exception as e:
            logger.error(f"Error processing function {signature} in file {file_path}: {e}")
            return None

    async def _process_trace_hierarchical(
        self,
        trace: DomainTrace,
        df: pd.DataFrame,
        result: FunctionClassificationResult
    ) -> None:
        """
        Process a single trace with hierarchical search space.

        Args:
            trace: DomainTrace object
            df: DataFrame containing function data
            result: FunctionClassificationResult to update
        """
        try:
            # Get domain-specific functions using hierarchical mappings
            domain_functions = self.get_domain_specific_functions_hierarchical(trace)

            if not domain_functions:
                # Fall back to top-level domain approach if no functions found
                logger.warning(f"No domain-specific functions found with hierarchical approach for trace: {trace.trace_str}")
                logger.warning(f"Falling back to top-level domain approach")
                domain_functions = self.get_domain_specific_functions(trace)

            if not domain_functions:
                logger.warning(f"No domain-specific functions found for trace: {trace.trace_str}")
                result.classifications[trace.trace_str] = []
                return

            # Prepare domain-specific function data
            function_data = self.prepare_domain_specific_function_data(df, domain_functions)

            if not function_data:
                logger.warning(f"No function data prepared for trace: {trace.trace_str}")
                result.classifications[trace.trace_str] = []
                return

            # Prepare function signatures and descriptions for LLM
            full_signature_set = "\n".join([f"{sig} - {desc}" for sig, desc in function_data.items()])

            # Create the API request
            request_json = {
                "model": self.model,
                "messages": [
                    {
                        "role": "system",
                        "content": """You are an expert software architect analyzing a codebase.
Your task is to identify which functions belong to a specific domain trace.
Analyze each function signature and description carefully, and determine if it belongs to the given domain trace - specially the leaf part.
If a function is playing a direct implementation based role in the building of the leaf domain, include it.
Return ONLY a list of function signatures that belong to the domain trace, one per line.
Do not include any explanations, headers, or other text in your response.
If no functions belong to the domain trace, return "No functions found".
"""
                    },
                    {
                        "role": "user",
                        "content": f"""Domain trace: {trace.trace_str}

Here is the list of function signatures and their descriptions from the relevant domain:

{full_signature_set}

Please list ONLY the function path (without descriptions) that belong to the domain trace "{trace.trace_str}".
Return one function signature per line, with no additional text.
"""
                    }
                ],
                "temperature": self.temperature,
                "max_tokens": 4000
            }

            # Call the OpenAI API
            logger.info(f"Calling OpenAI API for trace: {trace.trace_str}")
            response_json, error = await self._call_openai_api(request_json)

            if error:
                logger.error(f"API error for trace {trace.trace_str}: {error}")
                result.success = False
                result.error_message = f"API error for trace {trace.trace_str}: {error}"
                result.classifications[trace.trace_str] = []
                return

            # Extract the response content
            content = response_json["choices"][0]["message"]["content"]
            result.raw_responses[trace.trace_str] = content

            # Parse the response to get the list of functions
            functions = []
            if content and content.strip() != "No functions found":
                functions = [line.strip() for line in content.strip().split('\n') if line.strip()]

            result.classifications[trace.trace_str] = functions
            logger.info(f"Found {len(functions)} functions for trace: {trace.trace_str}")

        except Exception as e:
            logger.error(f"Error processing trace {trace.trace_str}: {e}")
            import traceback
            logger.error(traceback.format_exc())
            result.classifications[trace.trace_str] = []

    def normalize_function_paths(self, traces_yaml_path: str, output_path: Optional[str] = None) -> bool:
        """
        Normalize function paths in domain traces to ensure all functions have full paths.

        This function reads the domain_traces_leaf_corrected.yaml file, identifies functions
        without full paths, and uses domain_file_mappings.yaml and semantic_documented_functions.parquet
        to find the full paths for these functions.

        Args:
            traces_yaml_path: Path to the domain traces YAML file
            output_path: Path to save the updated domain traces YAML file (if None, overwrites input file)

        Returns:
            True if normalization was successful, False otherwise
        """
        try:
            # Set output path if not provided
            if output_path is None:
                output_path = traces_yaml_path

            logger.info(f"Normalizing function paths in domain traces: {traces_yaml_path}")

            # Read the domain traces YAML
            with open(traces_yaml_path, 'r') as f:
                yaml_data = yaml.safe_load(f)

            domain_traces = yaml_data.get('domain_traces', {})
            logger.info(f"Found {len(domain_traces)} domain traces")

            # Read the functions parquet
            df = self.read_functions_parquet()

            # Create a mapping from function name to full path
            function_name_to_path = {}
            for _, row in df.iterrows():
                node_id = row.get('node_id', '')
                if not node_id:
                    continue

                # Extract the function name from the node_id
                # node_id format is typically: file_path:Class.function_name or file_path:function_name
                parts = node_id.split(':')
                if len(parts) < 2:
                    continue

                file_path = parts[0]
                func_part = parts[1]

                # Handle class methods and standalone functions
                if '.' in func_part:
                    class_name, func_name = func_part.split('.', 1)
                    function_name_to_path[func_name] = node_id
                    # Also store with class name prefix for exact matches
                    function_name_to_path[f"{class_name}.{func_name}"] = node_id
                else:
                    func_name = func_part
                    function_name_to_path[func_name] = node_id

            logger.info(f"Created mapping for {len(function_name_to_path)} function names to full paths")

            # Process each domain trace
            updated_count = 0
            removed_count = 0
            cleaned_count = 0

            # Items to be removed
            items_to_remove = [
                "No functions found.",
                "```",
                "```ruby",
                "```python",
                "```plaintext",
                "```javascript",
                "```java",
                "```c++",
                "```c#",
                "```go",
                "```rust",
                "```php",
                "```swift",
                "```kotlin",
                "```typescript",
                "```shell",
                "```bash",
                "```sql",
                "```html",
                "```css",
                "```xml",
                "```json",
                "```yaml",
                "```markdown",
                "```text"
            ]

            for trace_str, functions in domain_traces.items():
                if not functions:
                    continue

                # Create a new list to store the cleaned functions
                cleaned_functions = []

                # Process each function in the trace
                for func in functions:
                    # Skip items that should be removed
                    if func in items_to_remove or func.startswith("```"):
                        removed_count += 1
                        continue

                    # Clean function names with 'def' prefix and parameters
                    if func.startswith("def "):
                        # Extract the function name without 'def ' and parameters
                        cleaned_func = func[4:].strip()  # Remove 'def ' prefix
                        # Remove parameters if present
                        if "(" in cleaned_func:
                            cleaned_func = cleaned_func.split("(")[0].strip()

                        func = cleaned_func
                        cleaned_count += 1

                    # Handle functions with parameters with colons (e.g., "paused_log(job, strategy:)")
                    if "(" in func and ")" in func:
                        func = func.split("(")[0].strip()
                        cleaned_count += 1

                    # Handle functions with question marks (e.g., "custom_grpc_fingerprint?")
                    if func.endswith("?"):
                        func = func[:-1]  # Remove the question mark
                        cleaned_count += 1

                    # Skip functions that already have full paths
                    if ":" in func:
                        cleaned_functions.append(func)
                        continue

                    # Try to find the full path for this function
                    if func in function_name_to_path:
                        # Direct match found
                        cleaned_functions.append(function_name_to_path[func])
                        updated_count += 1
                    else:
                        # Try to find a partial match
                        # This handles cases where the function might be listed as 'function_name'
                        # but the full path is 'file_path:Class.function_name'
                        matched = False
                        for full_name, full_path in function_name_to_path.items():
                            # Check if the function name is at the end of the full name
                            if full_name.endswith(f".{func}") or full_name == func:
                                cleaned_functions.append(full_path)
                                updated_count += 1
                                matched = True
                                break

                        if not matched:
                            logger.warning(f"Could not find full path for function: {func} in trace: {trace_str}")
                            # Keep the original function name in the list
                            cleaned_functions.append(func)

                # Update the domain trace with the cleaned functions
                domain_traces[trace_str] = cleaned_functions

            logger.info(f"Updated {updated_count} function paths in domain traces")
            logger.info(f"Removed {removed_count} invalid items from domain traces")
            logger.info(f"Cleaned {cleaned_count} function names with 'def' prefix or parameters")

            # Save the updated domain traces
            yaml_data['domain_traces'] = domain_traces
            with open(output_path, 'w') as f:
                yaml.dump(yaml_data, f, default_flow_style=False)

            logger.info(f"Saved updated domain traces to: {output_path}")
            return True

        except Exception as e:
            logger.error(f"Error normalizing function paths: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    async def build_and_classify(self) -> FunctionClassificationResult:
        """
        Build domain traces and classify functions with hierarchical reduced search space.
        Only processes leaf nodes to improve efficiency.

        Returns:
            FunctionClassificationResult containing the classification results
        """
        # Read the domain YAML
        domain_data = self.read_domain_analysis_yaml()

        # Build domain traces
        traces = self.build_domain_traces(domain_data)
        logger.info(f"Built {len(traces)} domain traces")

        # Count leaf nodes
        leaf_nodes = [trace for trace in traces if self._is_leaf_node(trace, traces)]
        logger.info(f"Identified {len(leaf_nodes)} leaf nodes out of {len(traces)} total traces")

        # Read the functions parquet
        df = self.read_functions_parquet()

        # Try to read domain-file mappings
        self.domain_file_mappings = self.read_domain_file_mappings()

        # If domain-file mappings are available, use the hierarchical approach
        if self.domain_file_mappings and self.hierarchical_mappings:
            logger.info("Using hierarchical approach with domain-file mappings (leaf nodes only)")

            # Map traces to domain paths
            self.trace_to_domain_path = self.map_traces_to_domain_paths(traces)

            # Also map traces to top domains for fallback
            self.trace_to_top_domain = self.map_traces_to_top_domains(traces)

            # Map functions to files
            self.file_to_functions = self.map_functions_to_files(df)

            # Classify functions with hierarchical reduced search space (leaf nodes only)
            result = await self.classify_functions_with_hierarchical_search_space(traces, df)
        elif self.domain_file_mappings:
            # Fall back to enhanced approach if only flat mappings are available
            logger.info("Falling back to enhanced approach with flat domain-file mappings")

            # Map traces to top domains
            self.trace_to_top_domain = self.map_traces_to_top_domains(traces)

            # Map functions to files
            self.file_to_functions = self.map_functions_to_files(df)

            # Classify functions with reduced search space
            result = await self.classify_functions_with_reduced_search_space(traces, df)
        else:
            # Fall back to original approach
            logger.info("Falling back to original approach without domain-file mappings")

            # Prepare function data
            function_data = self.prepare_function_data(df)
            logger.info(f"Prepared {len(function_data)} functions for classification")

            # Classify functions
            result = await self.classify_functions(traces, function_data)

        # Save the results
        if result.success:
            self.save_classification_results(result)

            # Automatically normalize function paths
            logger.info("Automatically normalizing function paths in the output file")
            self.normalize_function_paths(self.output_path, self.output_path)

        return result



class HierarchicalDomainTraceBuilderIntegration:
    """
    Integration class for adding hierarchical domain trace building to the repository analysis flow.

    This class provides methods that can be called from the repository analysis flow
    to add hierarchical domain trace building as an additional step.
    """

    @staticmethod
    def normalize_function_paths(
        traces_yaml_path: str,
        functions_parquet_path: str,
        output_path: Optional[str] = None,
    ) -> bool:
        """
        Normalize function paths in domain traces to ensure all functions have full paths.

        Args:
            traces_yaml_path: Path to the domain traces YAML file
            functions_parquet_path: Path to the semantic_documented_functions.parquet file
            output_path: Path to save the updated domain traces YAML file (if None, overwrites input file)

        Returns:
            True if normalization was successful, False otherwise
        """
        try:
            # Create a builder instance with minimal parameters needed for normalization
            builder = HierarchicalDomainTraceBuilder(
                domain_yaml_path="",  # Not used for normalization
                functions_parquet_path=functions_parquet_path,
                output_path="",  # Not used for normalization
                domain_file_mappings_path="",  # Not used for normalization
            )

            # Call the normalize_function_paths method
            return builder.normalize_function_paths(traces_yaml_path, output_path)

        except Exception as e:
            logger.error(f"Error in function path normalization: {e}")
            return False

    @staticmethod
    async def build_domain_traces(
        domain_yaml_path: str,
        functions_parquet_path: str,
        output_path: str,
        domain_file_mappings_path: str,
        api_key: Optional[str] = None,
        model: str = "gpt-4o-mini",
        max_requests_per_minute: float = 100,
        max_tokens_per_minute: float = 100000,
        fallback_to_original: bool = True,
        use_openrouter: bool = False,  # Set to False to ensure OpenAI is used
        openrouter_base_url: str = "https://openrouter.ai/api/v1",
        max_concurrent_tasks: int = 15,  # Maximum number of concurrent API tasks
        max_tokens_per_chunk: int = 70000,  # Maximum tokens per chunk
    ) -> bool:
        """
        Build domain traces and classify functions with hierarchical reduced search space.

        Args:
            domain_yaml_path: Path to the domain YAML file
            functions_parquet_path: Path to the semantic_documented_functions.parquet file
            output_path: Path to save the classification output
            domain_file_mappings_path: Path to the hierarchical domain-to-file mappings YAML file
            api_key: OpenAI API key (if None, will try to get from environment)
            model: OpenAI model to use
            max_requests_per_minute: Rate limit for API requests
            max_tokens_per_minute: Token rate limit for API
            fallback_to_original: Whether to fall back to the original approach if domain-file mappings are not available

        Returns:
            True if trace building was successful, False otherwise
        """
        try:
            builder = HierarchicalDomainTraceBuilder(
                domain_yaml_path=domain_yaml_path,
                functions_parquet_path=functions_parquet_path,
                output_path=output_path,
                domain_file_mappings_path=domain_file_mappings_path,
                api_key=api_key,
                model=model,
                max_requests_per_minute=max_requests_per_minute,
                max_tokens_per_minute=max_tokens_per_minute,
                fallback_to_original=fallback_to_original,
                use_openrouter=False,  # Force using OpenAI instead of OpenRouter
                openrouter_base_url=openrouter_base_url,
                max_concurrent_tasks=max_concurrent_tasks,
                max_tokens_per_chunk=max_tokens_per_chunk,
            )

            result = await builder.build_and_classify()
            return result.success

        except Exception as e:
            logger.error(f"Error in hierarchical domain trace building: {e}")
            return False



async def main():
    """Main entry point for the hierarchical domain trace builder."""
    import argparse

    parser = argparse.ArgumentParser(description="Hierarchical domain trace builder and function path normalizer")
    subparsers = parser.add_subparsers(dest="command", help="Command to run")

    # Build domain traces command
    build_parser = subparsers.add_parser("build", help="Build domain traces with hierarchical reduced search space")
    build_parser.add_argument("--domain-yaml", required=True, help="Path to the domain analysis YAML file")
    build_parser.add_argument("--functions-parquet", required=True, help="Path to the semantic_documented_functions.parquet file")
    build_parser.add_argument("--output", required=True, help="Path to save the classification output")
    build_parser.add_argument("--domain-file-mappings", required=True, help="Path to the hierarchical domain-to-file mappings YAML file")
    build_parser.add_argument("--api-key", help="OpenAI API key (if not provided, will try to get from environment)")
    build_parser.add_argument("--model", default="gpt-4o-mini", help="OpenAI model to use")
    build_parser.add_argument("--requests-per-minute", type=float, default=1000.0, help="Rate limit for API requests")
    build_parser.add_argument("--tokens-per-minute", type=float, default=1000000.0, help="Token rate limit for API")
    build_parser.add_argument("--temperature", type=float, default=0.0, help="Temperature for LLM generation")
    build_parser.add_argument("--no-fallback", action="store_true", help="Disable fallback to original approach if domain-file mappings are not available")
    build_parser.add_argument("--max-concurrent-tasks", type=int, default=15, help="Maximum number of concurrent API tasks")
    build_parser.add_argument("--max-tokens-per-chunk", type=int, default=70000, help="Maximum tokens per chunk")

    # Normalize function paths command
    normalize_parser = subparsers.add_parser("normalize", help="Normalize function paths in domain traces")
    normalize_parser.add_argument("--traces-yaml", required=True, help="Path to the domain traces YAML file")
    normalize_parser.add_argument("--functions-parquet", required=True, help="Path to the semantic_documented_functions.parquet file")
    normalize_parser.add_argument("--output", help="Path to save the updated domain traces YAML file (if not provided, overwrites input file)")

    args = parser.parse_args()

    if args.command == "build":
        try:
            builder = HierarchicalDomainTraceBuilder(
                domain_yaml_path=args.domain_yaml,
                functions_parquet_path=args.functions_parquet,
                output_path=args.output,
                domain_file_mappings_path=args.domain_file_mappings,
                api_key=args.api_key,
                model=args.model,
                max_requests_per_minute=args.requests_per_minute,
                max_tokens_per_minute=args.tokens_per_minute,
                temperature=args.temperature,
                fallback_to_original=not args.no_fallback,
                use_openrouter=False,  # Force using OpenAI instead of OpenRouter
                max_concurrent_tasks=args.max_concurrent_tasks,
                max_tokens_per_chunk=args.max_tokens_per_chunk
            )

            result = await builder.build_and_classify()

            if result.success:
                logger.info("Hierarchical domain trace building and function classification completed successfully")
                return 0
            else:
                logger.error(f"Hierarchical domain trace building and function classification failed: {result.error_message}")
                return 1

        except Exception as e:
            logger.error(f"Error in hierarchical domain trace building and function classification: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return 1

    elif args.command == "normalize":
        try:
            # Use the integration class to normalize function paths
            success = HierarchicalDomainTraceBuilderIntegration.normalize_function_paths(
                traces_yaml_path=args.traces_yaml,
                functions_parquet_path=args.functions_parquet,
                output_path=args.output
            )

            if success:
                logger.info("Function path normalization completed successfully")
                return 0
            else:
                logger.error("Function path normalization failed")
                return 1

        except Exception as e:
            logger.error(f"Error in function path normalization: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return 1

    else:
        parser.print_help()
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
