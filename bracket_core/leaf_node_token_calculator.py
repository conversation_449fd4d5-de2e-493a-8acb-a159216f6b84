#!/usr/bin/env python
"""
Leaf Node Token Calculator

This script analyzes the token sizes of leaf domain requests prepared for mermaid diagram generation.
It helps identify potential token usage issues when processing large codebases.
"""

import os
import yaml
import json
import logging
import pandas as pd
import asyncio
from typing import Dict, List, Set, Tuple, Optional, Any
from dataclasses import dataclass, field
import time

# Import token counting utility
from bracket_core.llm.tokens import num_tokens_from_string

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class TokenAnalysisResult:
    """Result of token analysis for leaf domains."""
    total_leaf_domains: int = 0
    total_tokens: int = 0
    average_tokens_per_domain: float = 0
    max_tokens: int = 0
    max_tokens_domain: str = ""
    min_tokens: int = 0
    min_tokens_domain: str = ""
    token_distribution: Dict[str, int] = field(default_factory=dict)  # Maps domain trace to token count
    domain_function_counts: Dict[str, int] = field(default_factory=dict)  # Maps domain trace to function count
    empty_domains: List[str] = field(default_factory=list)  # List of domains with no functions
    domains_exceeding_limit: Dict[str, int] = field(default_factory=dict)  # Domains exceeding token limit


class LeafNodeTokenCalculator:
    """
    Analyzes token sizes of leaf domain requests prepared for mermaid diagram generation.
    
    This class reads domain traces YAML files, identifies leaf domains, prepares requests
    similar to DomainDiagramGenerator._prepare_leaf_domain_request, and calculates token sizes.
    """

    def __init__(
        self,
        domain_traces_yaml_path: str,
        functions_parquet_path: str,
        output_dir: str,
        model_name: str = "gpt-4o-mini",  # Default model for token counting
        token_limit: int = 100000,  # Default token limit to flag domains exceeding it
    ):
        """Initialize the leaf node token calculator.

        Args:
            domain_traces_yaml_path: Path to the domain traces YAML file
            functions_parquet_path: Path to the semantic_documented_functions.parquet file
            output_dir: Directory to save analysis results
            model_name: Model name to use for token counting
            token_limit: Token limit to flag domains exceeding it
        """
        self.domain_traces_yaml_path = domain_traces_yaml_path
        self.functions_parquet_path = functions_parquet_path
        self.output_dir = output_dir
        self.model_name = model_name
        self.token_limit = token_limit
        
        # Create output directory if it doesn't exist
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Initialize data structures
        self.domain_traces = {}
        self.function_data = {}
        self.simplified_path_map = {}
        self.all_domains = set()
        self.leaf_domains = set()
        self.domain_levels = {}
        self.domain_parents = {}
        self.domain_children = {}
        
        # Analysis results
        self.token_counts = {}
        self.function_counts = {}
        self.empty_domains = []
        self.domains_exceeding_limit = {}

    def read_domain_traces(self) -> Dict[str, List[str]]:
        """
        Read domain traces from the YAML file.

        Returns:
            Dictionary mapping domain traces to lists of function signatures
        """
        logger.info(f"Reading domain traces from: {self.domain_traces_yaml_path}")

        try:
            with open(self.domain_traces_yaml_path, 'r') as f:
                yaml_data = yaml.safe_load(f)

            domain_traces = yaml_data.get('domain_traces', {})
            logger.info(f"Found {len(domain_traces)} domain traces")

            self.domain_traces = domain_traces
            return domain_traces

        except Exception as e:
            logger.error(f"Error reading domain traces: {e}")
            return {}

    def read_function_data(self) -> Dict[str, Dict[str, Any]]:
        """
        Read function data from the parquet file.

        Returns:
            Dictionary mapping node_ids to function data
        """
        logger.info(f"Reading function data from: {self.functions_parquet_path}")

        try:
            df = pd.read_parquet(self.functions_parquet_path)

            # Create a dictionary mapping node_ids to function data
            function_data = {}
            # Also create a mapping from simplified paths to node_ids for easier lookup
            simplified_path_map = {}

            for _, row in df.iterrows():
                node_id = row.get('node_id', '')
                if not node_id:
                    # Skip entries without node_id
                    continue

                # Extract relevant function data
                function_info = {
                    'description': row.get('description', ''),
                    'text': row.get('text', ''),
                    # 'file_path': row.get('file_path', ''),
                    # 'signature': row.get('signature', ''),
                }

                # Parse calls if available
                calls = row.get('calls', [])
                if isinstance(calls, str):
                    try:
                        calls = json.loads(calls)
                    except json.JSONDecodeError:
                        calls = [calls] if calls else []

                function_info['calls'] = calls

                # Parse call_contexts if available
                call_contexts = row.get('call_contexts', [])
                if isinstance(call_contexts, str):
                    try:
                        call_contexts = json.loads(call_contexts)
                    except json.JSONDecodeError:
                        call_contexts = [call_contexts] if call_contexts else []

                function_info['call_contexts'] = call_contexts

                # Store in the main function data dictionary using node_id as key
                function_data[node_id] = function_info

                # Create simplified path versions for easier lookup
                if ':' in node_id:
                    file_path, func_name = node_id.split(':', 1)
                    # Create simplified versions without full path
                    if '/' in file_path:
                        # Get just the filename and function
                        filename = file_path.split('/')[-1]
                        simplified_sig = f"{filename}:{func_name}"
                        simplified_path_map[simplified_sig] = node_id

                        # Also store just the relative path without any base directories
                        for i in range(1, len(file_path.split('/'))):
                            rel_path = '/'.join(file_path.split('/')[-i:])
                            rel_sig = f"{rel_path}:{func_name}"
                            simplified_path_map[rel_sig] = node_id

            logger.info(f"Loaded data for {len(function_data)} functions")
            logger.info(f"Created {len(simplified_path_map)} simplified path mappings for easier lookup")
            self.function_data = function_data
            self.simplified_path_map = simplified_path_map
            return function_data

        except Exception as e:
            logger.error(f"Error reading function data: {e}")
            self.simplified_path_map = {}
            return {}

    def build_domain_hierarchy(self) -> None:
        """
        Build a hierarchical representation of domains from domain traces.
        Identifies leaf domains (those with no children).
        """
        logger.info("Building domain hierarchy from domain traces")

        # Initialize domain tracking structures
        self.all_domains = set()  # All domains at all levels
        self.leaf_domains = set()  # Leaf domains (no children)
        self.domain_parents = {}  # Maps domain to its parent domain
        self.domain_children = {}  # Maps domain to its child domains
        self.domain_levels = {}  # Maps domain to its hierarchy level (0 = root)

        for trace_str, functions in self.domain_traces.items():
            # Split the trace string into components
            components = trace_str.split(' -> ')

            # Track each domain in the trace
            current_path = ""
            parent_path = ""

            # Navigate/build the hierarchy
            for i, component in enumerate(components):
                # Build the current path
                if current_path:
                    current_path += f" -> {component}"
                    parent_path = current_path.rsplit(' -> ', 1)[0]
                else:
                    current_path = component
                    parent_path = ""  # Root level has no parent

                # Add to tracking structures
                self.all_domains.add(current_path)
                self.domain_levels[current_path] = i

                # Set up parent-child relationships
                if parent_path:
                    if parent_path not in self.domain_children:
                        self.domain_children[parent_path] = set()
                    self.domain_children[parent_path].add(current_path)
                    self.domain_parents[current_path] = parent_path

        # Identify leaf domains (those with no children)
        for domain in self.all_domains:
            if domain not in self.domain_children or not self.domain_children[domain]:
                self.leaf_domains.add(domain)

        logger.info(f"Identified {len(self.all_domains)} total domains across all levels")
        logger.info(f"Identified {len(self.leaf_domains)} leaf domains (no children)")

    def _is_empty_domain_mapping(self, domain_trace: str) -> bool:
        """
        Check if a domain has empty file mappings in the domain file mappings YAML.

        Args:
            domain_trace: Domain trace string

        Returns:
            True if the domain has empty file mappings, False otherwise
        """
        # Check if this domain exists in the domain traces but has empty functions list
        if domain_trace in self.domain_traces and not self.domain_traces[domain_trace]:
            return True

        # Check if the function list contains only a placeholder like 'No functions found.'
        if domain_trace in self.domain_traces and len(self.domain_traces[domain_trace]) == 1:
            func = self.domain_traces[domain_trace][0]
            if func == 'No functions found.' or func.startswith('No functions'):
                return True

        return False

    def prepare_leaf_domain_request(self, domain_trace: str, functions: List[str]) -> Tuple[str, str, str]:
        """
        Prepare the API request for a leaf domain diagram without waiting for the response.
        This is similar to DomainDiagramGenerator._prepare_leaf_domain_request.

        Args:
            domain_trace: Domain trace string
            functions: List of function signatures in this domain

        Returns:
            Tuple of (domain_trace, system_prompt, user_prompt)
        """
        # Prepare function data for the prompt
        function_details = []
        missing_functions = []
        found_functions = []

        for func_sig in functions:
            # Try direct lookup first
            if func_sig in self.function_data:
                func_data = self.function_data[func_sig]
                found_functions.append(func_sig)

                # Format function details
                func_detail = {
                    # 'signature': func_sig,
                    'description': func_data.get('description', ''),
                    'text': func_data.get('text', ''),
                    # 'file_path': func_data.get('file_path', ''),
                    # 'calls': func_data.get('calls', []),
                    # 'call_contexts': func_data.get('call_contexts', [])
                }

                function_details.append(func_detail)
            # Try lookup via simplified path map
            elif func_sig in self.simplified_path_map:
                actual_sig = self.simplified_path_map[func_sig]
                func_data = self.function_data[actual_sig]
                found_functions.append(func_sig)

                # Format function details
                func_detail = {
                    # 'signature': func_sig,
                    'description': func_data.get('description', ''),
                    'text': func_data.get('text', ''),
                    # 'file_path': func_data.get('file_path', ''),
                    # 'calls': func_data.get('calls', []),
                    # 'call_contexts': func_data.get('call_contexts', [])
                }

                function_details.append(func_detail)
            else:
                missing_functions.append(func_sig)

        # Store function count for this domain
        self.function_counts[domain_trace] = len(function_details)

        # If no functions were found at all, check if this is an empty domain mapping
        if not function_details:
            if self._is_empty_domain_mapping(domain_trace):
                logger.warning(f"Domain has empty file mappings: {domain_trace}")
                self.empty_domains.append(domain_trace)
                # Return empty prompts with special markers
                return domain_trace, "EMPTY_DOMAIN_MAPPING", "EMPTY_DOMAIN_MAPPING"
            else:
                logger.warning(f"No functions found for domain: {domain_trace}")
                self.empty_domains.append(domain_trace)
                return domain_trace, "NO_FUNCTIONS_FOUND", "NO_FUNCTIONS_FOUND"

        # Get hierarchy information
        level = self.domain_levels.get(domain_trace, 0)
        # print(function_details)
        # Create the system prompt
        system_prompt = """You are an expert software architect who creates clear, informative mermaid diagrams to visualize code architecture.
Your task is to create a mermaid diagram that represents the functions in a specific domain of a codebase.

Guidelines for creating the diagram:
1. Create a FLOWCHART diagram that shows the logical implementation and data flow
2. Focus on what the functions DO, not just what they contain
3. Show the execution flow between functions with directional arrows
4. Illustrate data transformations and processing steps
5. Include decision points and conditional logic where relevant
6. Represent key data structures and how they evolve through the process
7. Group related operations into logical sections using subgraphs
8. Use different node shapes to represent different types of operations
9. Label each node or process with the specific function name responsible for it
10. Add brief annotations explaining key transformations or decision points
11. DO NOT use tooltips or click actions - they consume unnecessary tokens
12. The diagram should be 2000-3000 tokens in size to provide sufficient detail

Your output should ONLY contain a valid mermaid diagram enclosed in triple backticks with the mermaid tag.
"""

        # Create the user prompt
        user_prompt = f"""Create a mermaid diagram for the leaf domain: {domain_trace} (Hierarchy Level: {level})

This is a leaf domain with no subdomains. Focus on showing the implementation details of the functions.

Here are the functions in this domain:

{json.dumps(function_details, indent=2)}

Please generate a comprehensive mermaid diagram that shows these functions and their relationships.
DO NOT use tooltips or click actions in the diagram.
"""

        return domain_trace, system_prompt, user_prompt

    def calculate_request_tokens(self, domain_trace: str, system_prompt: str, user_prompt: str) -> int:
        """
        Calculate the number of tokens in a request.

        Args:
            domain_trace: Domain trace string
            system_prompt: System prompt for the API call
            user_prompt: User prompt for the API call

        Returns:
            Number of tokens in the request
        """
        # Skip empty domains
        if system_prompt == "EMPTY_DOMAIN_MAPPING" or system_prompt == "NO_FUNCTIONS_FOUND":
            return 0

        # Calculate tokens for system prompt and user prompt
        system_tokens = num_tokens_from_string(system_prompt, self.model_name)
        user_tokens = num_tokens_from_string(user_prompt, self.model_name)
        
        # Total tokens (including format tokens for the API call)
        total_tokens = system_tokens + user_tokens + 9  # 9 tokens for message formatting
        
        # Store the token count
        self.token_counts[domain_trace] = total_tokens
        
        # Check if this domain exceeds the token limit
        if total_tokens > self.token_limit:
            self.domains_exceeding_limit[domain_trace] = total_tokens
            
        return total_tokens

    async def analyze_leaf_domains(self) -> TokenAnalysisResult:
        """
        Analyze token sizes for all leaf domains.

        Returns:
            TokenAnalysisResult containing the analysis results
        """
        logger.info("Analyzing token sizes for leaf domains")
        
        # Read domain traces and function data
        self.read_domain_traces()
        self.read_function_data()
        
        # Build domain hierarchy
        self.build_domain_hierarchy()
        
        # Prepare and analyze requests for all leaf domains
        total_tokens = 0
        tasks = []
        
        for domain_trace in self.leaf_domains:
            functions = self.domain_traces.get(domain_trace, [])
            domain_trace, system_prompt, user_prompt = self.prepare_leaf_domain_request(domain_trace, functions)

            token_count = self.calculate_request_tokens(domain_trace, system_prompt, user_prompt)
            total_tokens += token_count
            
        # Calculate statistics
        non_empty_domains = [d for d in self.leaf_domains if d not in self.empty_domains]
        non_empty_token_counts = {d: t for d, t in self.token_counts.items() if t > 0}
        
        if non_empty_token_counts:
            max_tokens = max(non_empty_token_counts.values())
            max_tokens_domain = next(d for d, t in non_empty_token_counts.items() if t == max_tokens)
            min_tokens = min(non_empty_token_counts.values())
            min_tokens_domain = next(d for d, t in non_empty_token_counts.items() if t == min_tokens)
            avg_tokens = total_tokens / len(non_empty_token_counts) if non_empty_token_counts else 0
        else:
            max_tokens = 0
            max_tokens_domain = ""
            min_tokens = 0
            min_tokens_domain = ""
            avg_tokens = 0
        
        # Create result
        result = TokenAnalysisResult(
            total_leaf_domains=len(self.leaf_domains),
            total_tokens=total_tokens,
            average_tokens_per_domain=avg_tokens,
            max_tokens=max_tokens,
            max_tokens_domain=max_tokens_domain,
            min_tokens=min_tokens,
            min_tokens_domain=min_tokens_domain,
            token_distribution=self.token_counts,
            domain_function_counts=self.function_counts,
            empty_domains=self.empty_domains,
            domains_exceeding_limit=self.domains_exceeding_limit
        )
        
        return result

    def save_analysis_results(self, result: TokenAnalysisResult) -> str:
        """
        Save analysis results to files.

        Args:
            result: TokenAnalysisResult containing the analysis results

        Returns:
            Path to the summary file
        """
        logger.info("Saving analysis results")
        
        # Create a timestamp for the output files
        timestamp = time.strftime("%Y%m%d-%H%M%S")
        
        # Save token distribution to CSV
        token_distribution_path = os.path.join(self.output_dir, f"leaf_domain_tokens_{timestamp}.csv")
        token_df = pd.DataFrame([
            {"domain": domain, "tokens": tokens, "functions": self.function_counts.get(domain, 0)}
            for domain, tokens in result.token_distribution.items()
        ])
        token_df.sort_values("tokens", ascending=False, inplace=True)
        token_df.to_csv(token_distribution_path, index=False)
        logger.info(f"Saved token distribution to {token_distribution_path}")
        
        # Save domains exceeding limit to CSV
        if result.domains_exceeding_limit:
            exceeding_path = os.path.join(self.output_dir, f"domains_exceeding_limit_{timestamp}.csv")
            exceeding_df = pd.DataFrame([
                {"domain": domain, "tokens": tokens, "functions": self.function_counts.get(domain, 0)}
                for domain, tokens in result.domains_exceeding_limit.items()
            ])
            exceeding_df.sort_values("tokens", ascending=False, inplace=True)
            exceeding_df.to_csv(exceeding_path, index=False)
            logger.info(f"Saved domains exceeding limit to {exceeding_path}")
        
        # Save empty domains to CSV
        if result.empty_domains:
            empty_path = os.path.join(self.output_dir, f"empty_domains_{timestamp}.csv")
            empty_df = pd.DataFrame({"domain": result.empty_domains})
            empty_df.to_csv(empty_path, index=False)
            logger.info(f"Saved empty domains to {empty_path}")
        
        # Save summary to text file
        summary_path = os.path.join(self.output_dir, f"token_analysis_summary_{timestamp}.txt")
        with open(summary_path, 'w') as f:
            f.write(f"Leaf Domain Token Analysis Summary\n")
            f.write(f"================================\n\n")
            f.write(f"Total leaf domains: {result.total_leaf_domains}\n")
            f.write(f"Total tokens: {result.total_tokens:,}\n")
            f.write(f"Average tokens per domain: {result.average_tokens_per_domain:.2f}\n")
            f.write(f"Maximum tokens: {result.max_tokens:,} (Domain: {result.max_tokens_domain})\n")
            f.write(f"Minimum tokens: {result.min_tokens:,} (Domain: {result.min_tokens_domain})\n")
            f.write(f"Empty domains: {len(result.empty_domains)}\n")
            f.write(f"Domains exceeding token limit ({self.token_limit:,}): {len(result.domains_exceeding_limit)}\n\n")
            
            f.write(f"Files generated:\n")
            f.write(f"- Token distribution: {os.path.basename(token_distribution_path)}\n")
            if result.domains_exceeding_limit:
                f.write(f"- Domains exceeding limit: {os.path.basename(exceeding_path)}\n")
            if result.empty_domains:
                f.write(f"- Empty domains: {os.path.basename(empty_path)}\n")
        
        logger.info(f"Saved summary to {summary_path}")
        return summary_path


async def main():
    """Main entry point for the leaf node token calculator."""
    import argparse

    parser = argparse.ArgumentParser(description="Analyze token sizes of leaf domain requests")
    parser.add_argument("--domain-traces", required=True, help="Path to the domain traces YAML file")
    parser.add_argument("--functions-parquet", required=True, help="Path to the semantic_documented_functions.parquet file")
    parser.add_argument("--output-dir", required=True, help="Directory to save analysis results")
    parser.add_argument("--model", default="gpt-4o-mini", help="Model name to use for token counting")
    parser.add_argument("--token-limit", type=int, default=100000, help="Token limit to flag domains exceeding it")
    
    args = parser.parse_args()

    try:
        # Create a leaf node token calculator
        calculator = LeafNodeTokenCalculator(
            domain_traces_yaml_path=args.domain_traces,
            functions_parquet_path=args.functions_parquet,
            output_dir=args.output_dir,
            model_name=args.model,
            token_limit=args.token_limit
        )
        
        # Analyze leaf domains
        start_time = time.time()
        result = await calculator.analyze_leaf_domains()
        end_time = time.time()
        
        # Save analysis results
        summary_path = calculator.save_analysis_results(result)
        
        # Print summary
        logger.info(f"Analysis completed in {end_time - start_time:.2f} seconds")
        logger.info(f"Total leaf domains: {result.total_leaf_domains}")
        logger.info(f"Total tokens: {result.total_tokens:,}")
        logger.info(f"Average tokens per domain: {result.average_tokens_per_domain:.2f}")
        logger.info(f"Maximum tokens: {result.max_tokens:,} (Domain: {result.max_tokens_domain})")
        logger.info(f"Minimum tokens: {result.min_tokens:,} (Domain: {result.min_tokens_domain})")
        logger.info(f"Empty domains: {len(result.empty_domains)}")
        logger.info(f"Domains exceeding token limit ({args.token_limit:,}): {len(result.domains_exceeding_limit)}")
        logger.info(f"Summary saved to {summary_path}")
        
        return 0
        
    except Exception as e:
        logger.error(f"Error in leaf node token analysis: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
