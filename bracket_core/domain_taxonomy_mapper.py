"""
Domain Taxonomy Mapper for Codebase

This module provides functionality to:
1. Read domain_analysis.yaml to get the hierarchical structure of domains
2. Read domain_traces.yaml to get the function mappings for each domain trace
3. Read the generated mermaid diagrams from the domain_diagrams directory
4. Combine all this information into a hierarchical JSON structure
5. Save this JSON structure to a file

It can be used as:
1. A standalone script to process domain analysis, traces, and diagrams
2. A module that can be integrated into the repository analysis flow
"""

import os
import yaml
import json
import logging
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, field

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class DomainTaxonomyNode:
    """Represents a node in the domain taxonomy hierarchy."""
    name: str
    children: List["DomainTaxonomyNode"] = field(default_factory=list)
    functions: List[str] = field(default_factory=list)
    diagram: Optional[str] = None
    diagram_path: Optional[str] = None
    diagram_name: Optional[str] = None  # Standardized diagram filename (e.g., "L1_Domain_Subdomain")
    full_path: Optional[str] = None  # Full path from root to this node (e.g., "A -> B -> C")

@dataclass
class DomainTaxonomyMapperResult:
    """Result of mapping domain taxonomy."""
    success: bool = True
    error_message: str = ""
    taxonomy_json: Dict[str, Any] = field(default_factory=dict)
    output_path: Optional[str] = None

class DomainTaxonomyMapper:
    """
    Maps domain taxonomy from domain analysis, traces, and diagrams.

    This class reads domain analysis YAML, domain traces YAML, and mermaid diagrams,
    and combines them into a hierarchical JSON structure.
    """

    def __init__(
        self,
        domain_analysis_yaml_path: str,
        domain_traces_yaml_path: str,
        domain_diagrams_dir: str,
        output_json_path: str,
        diagram_name_mapping_path: Optional[str] = None,
    ):
        """
        Initialize the domain taxonomy mapper.

        Args:
            domain_analysis_yaml_path: Path to the domain analysis YAML file
            domain_traces_yaml_path: Path to the domain traces YAML file
            domain_diagrams_dir: Directory containing the generated mermaid diagrams
            output_json_path: Path to save the output JSON file
            diagram_name_mapping_path: Path to the diagram name mapping JSON file (optional)
        """
        self.domain_analysis_yaml_path = domain_analysis_yaml_path
        self.domain_traces_yaml_path = domain_traces_yaml_path
        self.domain_diagrams_dir = domain_diagrams_dir
        self.diagrams_with_fn_info_dir = os.path.join(domain_diagrams_dir, "diagrams_with_fn_info")
        self.output_json_path = output_json_path
        self.diagram_name_mapping_path = diagram_name_mapping_path

        # If diagram_name_mapping_path is not provided, check if it exists in the default locations
        if not self.diagram_name_mapping_path:
            # First check in the domain_diagrams directory
            default_mapping_path = os.path.join(domain_diagrams_dir, "diagram_name_mapping.json")
            if os.path.exists(default_mapping_path):
                self.diagram_name_mapping_path = default_mapping_path
                logger.info(f"Using default diagram name mapping path: {default_mapping_path}")
            else:
                # Then check in the parent directory
                parent_mapping_path = os.path.join(os.path.dirname(domain_diagrams_dir), "diagram_name_mapping.json")
                if os.path.exists(parent_mapping_path):
                    self.diagram_name_mapping_path = parent_mapping_path
                    logger.info(f"Using parent directory diagram name mapping path: {parent_mapping_path}")

        # Initialize data structures
        self.domain_hierarchy = {}
        self.domain_traces = {}
        self.domain_diagrams = {}
        self.diagram_name_mapping = {}

        # Track domain paths
        self.domain_paths = {}  # Maps domain name to full path
        self.path_to_node = {}  # Maps full path to node in the hierarchy

    def read_domain_analysis(self) -> Dict[str, Any]:
        """
        Read the domain analysis YAML file.

        Returns:
            Dictionary containing domain analysis data
        """
        logger.info(f"Reading domain analysis YAML file: {self.domain_analysis_yaml_path}")
        try:
            with open(self.domain_analysis_yaml_path, 'r') as f:
                domain_data = yaml.safe_load(f)
            return domain_data
        except Exception as e:
            logger.error(f"Error reading domain analysis YAML file: {e}")
            raise

    def read_domain_traces(self) -> Dict[str, List[str]]:
        """
        Read the domain traces YAML file.

        Returns:
            Dictionary mapping domain traces to lists of function signatures
        """
        logger.info(f"Reading domain traces YAML file: {self.domain_traces_yaml_path}")
        try:
            with open(self.domain_traces_yaml_path, 'r') as f:
                yaml_data = yaml.safe_load(f)

            domain_traces = yaml_data.get('domain_traces', {})
            logger.info(f"Found {len(domain_traces)} domain traces")

            # Clean up any markdown formatting in the traces
            cleaned_traces = {}
            for trace, functions in domain_traces.items():
                cleaned_functions = []
                for func in functions:
                    # Remove markdown code block markers if present
                    if func.startswith('```') or func.endswith('```'):
                        continue
                    cleaned_functions.append(func)
                cleaned_traces[trace] = cleaned_functions

            return cleaned_traces
        except Exception as e:
            logger.error(f"Error reading domain traces YAML file: {e}")
            return {}

    def read_diagram_name_mapping(self) -> Dict[str, str]:
        """
        Read the diagram name mapping from the JSON file.

        Returns:
            Dictionary mapping standardized diagram names to domain paths
        """
        # First try the specified path
        if self.diagram_name_mapping_path and os.path.exists(self.diagram_name_mapping_path):
            try:
                with open(self.diagram_name_mapping_path, 'r') as f:
                    mapping = json.load(f)

                logger.info(f"Loaded diagram name mapping with {len(mapping)} entries from {self.diagram_name_mapping_path}")
                return mapping
            except Exception as e:
                logger.error(f"Error reading diagram name mapping from {self.diagram_name_mapping_path}: {e}")

        # If that fails, try looking in the parent directory of the output directory
        output_dir = os.path.dirname(self.output_json_path)
        alt_mapping_path = os.path.join(output_dir, "diagram_name_mapping.json")

        if os.path.exists(alt_mapping_path):
            try:
                with open(alt_mapping_path, 'r') as f:
                    mapping = json.load(f)

                logger.info(f"Loaded diagram name mapping with {len(mapping)} entries from {alt_mapping_path}")
                return mapping
            except Exception as e:
                logger.error(f"Error reading diagram name mapping from {alt_mapping_path}: {e}")

        # If that fails too, try looking in the diagrams directory and its subdirectories
        for root_dir in [self.domain_diagrams_dir, self.diagrams_with_fn_info_dir]:
            alt_mapping_path = os.path.join(root_dir, "diagram_name_mapping.json")
            if os.path.exists(alt_mapping_path):
                try:
                    with open(alt_mapping_path, 'r') as f:
                        mapping = json.load(f)

                    logger.info(f"Loaded diagram name mapping with {len(mapping)} entries from {alt_mapping_path}")
                    return mapping
                except Exception as e:
                    logger.error(f"Error reading diagram name mapping from {alt_mapping_path}: {e}")

        # If all else fails, try to find the file anywhere in the output directory tree
        output_dir = os.path.dirname(self.output_json_path)
        logger.info(f"Searching for diagram_name_mapping.json in {output_dir} and subdirectories")

        try:
            for root, dirs, files in os.walk(output_dir):
                if "diagram_name_mapping.json" in files:
                    alt_mapping_path = os.path.join(root, "diagram_name_mapping.json")
                    try:
                        with open(alt_mapping_path, 'r') as f:
                            mapping = json.load(f)

                        logger.info(f"Found and loaded diagram name mapping with {len(mapping)} entries from {alt_mapping_path}")
                        return mapping
                    except Exception as e:
                        logger.error(f"Error reading found diagram name mapping from {alt_mapping_path}: {e}")
        except Exception as e:
            logger.error(f"Error searching for diagram_name_mapping.json: {e}")

        # If we've reached here, we couldn't find the mapping file

        # If all else fails, log a warning and return an empty mapping
        logger.warning("Could not find diagram name mapping file in any of the expected locations")
        return {}

    def read_domain_diagrams(self) -> Dict[str, Dict[str, str]]:
        """
        Read the mermaid diagrams from the domain diagrams directory.

        Returns:
            Dictionary mapping domain paths to diagram content and file path
        """
        logger.info(f"Reading mermaid diagrams from: {self.diagrams_with_fn_info_dir}")
        diagrams = {}

        try:
            if not os.path.exists(self.diagrams_with_fn_info_dir):
                logger.warning(f"Diagrams directory not found: {self.diagrams_with_fn_info_dir}")
                return diagrams

            # First, try to use the diagram name mapping if available
            self.diagram_name_mapping = self.read_diagram_name_mapping()

            if self.diagram_name_mapping:
                logger.info(f"Using diagram name mapping with {len(self.diagram_name_mapping)} entries")

                # Use the mapping to load diagrams
                for standardized_name, domain_path in self.diagram_name_mapping.items():
                    diagram_file = f"{standardized_name}.md"
                    diagram_path = os.path.join(self.diagrams_with_fn_info_dir, diagram_file)

                    if os.path.exists(diagram_path):
                        with open(diagram_path, 'r') as f:
                            diagram_content = f.read()

                        diagrams[domain_path] = {
                            'content': diagram_content,
                            'path': diagram_path
                        }

                        logger.debug(f"Loaded diagram for {domain_path} using mapping")
                    else:
                        logger.warning(f"Diagram file not found: {diagram_path}")
            else:
                # Fall back to the old method if no mapping is available
                logger.info("No diagram name mapping found, using filename-based approach")

                # Get all .md files in the diagrams directory
                diagram_files = [f for f in os.listdir(self.diagrams_with_fn_info_dir)
                                if f.endswith('.md') and not f.endswith('_raw.md')]

                logger.info(f"Found {len(diagram_files)} diagram files in directory")

                for diagram_file in diagram_files:
                    # Check if it's a standardized name (starts with L followed by a number)
                    if diagram_file.startswith('L') and diagram_file[1].isdigit():
                        # Skip files with standardized names since we don't have a mapping
                        logger.debug(f"Skipping standardized filename without mapping: {diagram_file}")
                        continue

                    # Convert filename to domain path using the old method
                    domain_path = diagram_file.replace('_', ' -> ').replace('.md', '')

                    # Read the diagram content
                    diagram_path = os.path.join(self.diagrams_with_fn_info_dir, diagram_file)
                    with open(diagram_path, 'r') as f:
                        diagram_content = f.read()

                    diagrams[domain_path] = {
                        'content': diagram_content,
                        'path': diagram_path
                    }

                    logger.debug(f"Loaded diagram: {domain_path}")

            logger.info(f"Successfully loaded {len(diagrams)} mermaid diagrams")
            return diagrams
        except Exception as e:
            logger.error(f"Error reading mermaid diagrams: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return {}

    def build_taxonomy_tree(self, domain_data: Dict[str, Any]) -> DomainTaxonomyNode:
        """
        Build a taxonomy tree from the domain analysis data.

        Args:
            domain_data: Dictionary containing domain analysis data

        Returns:
            Root node of the taxonomy tree
        """
        logger.info("Building taxonomy tree from domain analysis data")

        # Create root node
        root = DomainTaxonomyNode(name="Root")

        # Process areas recursively
        areas = domain_data.get('areas', [])
        for area in areas:
            area_node = self._process_area(area, [])
            root.children.append(area_node)

        return root

    def _process_area(self, area: Dict[str, Any], parent_path: List[str]) -> DomainTaxonomyNode:
        """
        Process an area in the domain hierarchy recursively.

        Args:
            area: Dictionary containing area data
            parent_path: List of parent area names

        Returns:
            Node representing this area
        """
        area_name = area.get('name', '')
        current_path = parent_path + [area_name]
        full_path = " -> ".join(current_path)

        # Create node for this area
        node = DomainTaxonomyNode(
            name=area_name,
            full_path=full_path
        )

        # Store mapping from path to node
        self.path_to_node[full_path] = node
        self.domain_paths[area_name] = full_path

        # Process subareas recursively
        subareas = area.get('subareas', [])
        for subarea in subareas:
            subarea_node = self._process_area(subarea, current_path)
            node.children.append(subarea_node)

        return node

    def map_functions_to_taxonomy(self, root: DomainTaxonomyNode, domain_traces: Dict[str, List[str]]) -> None:
        """
        Map functions from domain traces to the taxonomy tree.

        Args:
            root: Root node of the taxonomy tree
            domain_traces: Dictionary mapping domain traces to lists of function signatures
        """
        logger.info("Mapping functions from domain traces to taxonomy tree")

        # For each domain trace, find the corresponding node in the taxonomy tree
        for trace_path, functions in domain_traces.items():
            if trace_path in self.path_to_node:
                node = self.path_to_node[trace_path]
                node.functions = functions
                logger.debug(f"Mapped {len(functions)} functions to {trace_path}")
            else:
                logger.warning(f"Domain trace not found in taxonomy tree: {trace_path}")

    def map_diagrams_to_taxonomy(self, root: DomainTaxonomyNode, domain_diagrams: Dict[str, Dict[str, str]]) -> None:
        """
        Map diagrams to the taxonomy tree.

        Args:
            root: Root node of the taxonomy tree
            domain_diagrams: Dictionary mapping domain paths to diagram content
        """
        logger.info("Mapping diagrams to taxonomy tree")

        # Create a mapping of normalized paths to original paths for easier matching
        normalized_diagram_paths = {}
        for diagram_path in domain_diagrams.keys():
            # Normalize the path by removing special characters and converting to lowercase
            normalized_path = diagram_path.lower().replace('&', 'and')
            normalized_diagram_paths[normalized_path] = diagram_path

        # Create a mapping of normalized node paths for easier matching
        normalized_node_paths = {}
        for node_path in self.path_to_node.keys():
            # Normalize the path by removing special characters and converting to lowercase
            normalized_path = node_path.lower().replace('&', 'and')
            normalized_node_paths[normalized_path] = node_path

        # Create a reverse mapping from diagram paths to standardized names
        diagram_path_to_name = {}
        if self.diagram_name_mapping:
            # Invert the mapping from standardized names to domain paths
            for std_name, domain_path in self.diagram_name_mapping.items():
                diagram_path_to_name[domain_path] = std_name

        # For each diagram, find the corresponding node in the taxonomy tree
        matched_diagrams = 0
        for diagram_path, diagram_info in domain_diagrams.items():
            # Try direct match first
            if diagram_path in self.path_to_node:
                node = self.path_to_node[diagram_path]
                node.diagram = diagram_info['content']
                node.diagram_path = diagram_info['path']

                # Add the standardized diagram name if available
                if diagram_path in diagram_path_to_name:
                    node.diagram_name = diagram_path_to_name[diagram_path]
                    logger.debug(f"Set diagram_name to {node.diagram_name} for {diagram_path} (from mapping)")
                else:
                    # Extract filename from path as fallback
                    node.diagram_name = os.path.basename(diagram_info['path'])
                    logger.debug(f"Set diagram_name to {node.diagram_name} for {diagram_path} (from basename)")

                matched_diagrams += 1
                logger.debug(f"Mapped diagram to {diagram_path} (direct match)")
            else:
                # Try normalized match
                normalized_diagram_path = diagram_path.lower().replace('&', 'and')

                # Check if this normalized path matches any normalized node path
                matched = False
                for normalized_node_path, original_node_path in normalized_node_paths.items():
                    if normalized_diagram_path == normalized_node_path:
                        node = self.path_to_node[original_node_path]
                        node.diagram = diagram_info['content']
                        node.diagram_path = diagram_info['path']

                        # Add the standardized diagram name if available
                        if diagram_path in diagram_path_to_name:
                            node.diagram_name = diagram_path_to_name[diagram_path]
                        else:
                            # Extract filename from path as fallback
                            node.diagram_name = os.path.basename(diagram_info['path'])

                        matched_diagrams += 1
                        logger.debug(f"Mapped diagram to {original_node_path} (normalized match)")
                        matched = True
                        break

                # If still no match, try fuzzy matching based on components
                if not matched:
                    # Split paths into components for partial matching
                    diagram_components = set(normalized_diagram_path.split(' -> '))

                    best_match = None
                    best_match_score = 0

                    for node_path in self.path_to_node.keys():
                        normalized_node_path = node_path.lower().replace('&', 'and')
                        node_components = set(normalized_node_path.split(' -> '))

                        # Calculate overlap score (Jaccard similarity)
                        intersection = len(diagram_components.intersection(node_components))
                        union = len(diagram_components.union(node_components))

                        if union > 0:
                            score = intersection / union

                            # If this is the best match so far, store it
                            if score > best_match_score and score > 0.5:  # Threshold for matching
                                best_match_score = score
                                best_match = node_path

                    if best_match:
                        node = self.path_to_node[best_match]
                        node.diagram = diagram_info['content']
                        node.diagram_path = diagram_info['path']

                        # Add the standardized diagram name if available
                        if diagram_path in diagram_path_to_name:
                            node.diagram_name = diagram_path_to_name[diagram_path]
                        else:
                            # Extract filename from path as fallback
                            node.diagram_name = os.path.basename(diagram_info['path'])

                        matched_diagrams += 1
                        logger.debug(f"Mapped diagram to {best_match} (fuzzy match with score {best_match_score})")
                    else:
                        logger.warning(f"Diagram path not found in taxonomy tree: {diagram_path}")

        logger.info(f"Successfully mapped {matched_diagrams} out of {len(domain_diagrams)} diagrams to taxonomy tree")

    def taxonomy_tree_to_json(self, root: DomainTaxonomyNode) -> Dict[str, Any]:
        """
        Convert the taxonomy tree to a JSON structure.

        Args:
            root: Root node of the taxonomy tree

        Returns:
            Dictionary representing the taxonomy tree in JSON format
        """
        logger.info("Converting taxonomy tree to JSON structure")

        def node_to_dict(node: DomainTaxonomyNode) -> Dict[str, Any]:
            """Convert a node to a dictionary."""
            result = {
                "name": node.name,
                "full_path": node.full_path
            }

            if node.children:
                result["children"] = [node_to_dict(child) for child in node.children]

            if node.functions:
                result["functions"] = node.functions

            if node.diagram:
                result["diagram"] = node.diagram

            if node.diagram_path:
                result["diagram_path"] = node.diagram_path

            if node.diagram_name:
                result["diagram_name"] = node.diagram_name
                logger.debug(f"Added diagram_name {node.diagram_name} to JSON for node {node.name}")

            return result

        return node_to_dict(root)

    def save_taxonomy_json(self, taxonomy_json: Dict[str, Any]) -> None:
        """
        Save the taxonomy JSON to a file.

        Args:
            taxonomy_json: Dictionary representing the taxonomy tree in JSON format
        """
        logger.info(f"Saving taxonomy JSON to: {self.output_json_path}")
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self.output_json_path), exist_ok=True)

            # Save JSON with indentation for readability
            with open(self.output_json_path, 'w') as f:
                json.dump(taxonomy_json, f, indent=2)

            logger.info(f"Taxonomy JSON saved to: {self.output_json_path}")
        except Exception as e:
            logger.error(f"Error saving taxonomy JSON: {e}")
            raise

    def find_combined_diagrams(self) -> Dict[str, Dict[str, str]]:
        """
        Find combined diagrams and the codebase overview diagram.

        Returns:
            Dictionary mapping domain paths to diagram info for combined diagrams
        """
        logger.info("Finding combined diagrams and codebase overview diagram")
        combined_diagrams = {}

        # Check if we have a diagram name mapping
        if not self.diagram_name_mapping:
            logger.warning("No diagram name mapping found, cannot identify combined diagrams")
            return combined_diagrams

        # Look for combined diagrams in the mapping
        for std_name, domain_path in self.diagram_name_mapping.items():
            # Check if it's a combined diagram (contains "Combined" in the name or path)
            is_combined = "Combined" in std_name or "combined" in domain_path
            # Check if it's the codebase overview diagram
            is_overview = std_name == "L0_Codebase_Overview" or domain_path == "Codebase Overview"

            if is_combined or is_overview:
                # Find the diagram file
                diagram_file = f"{std_name}.md"
                diagram_path = os.path.join(self.diagrams_with_fn_info_dir, diagram_file)

                if os.path.exists(diagram_path):
                    with open(diagram_path, 'r') as f:
                        diagram_content = f.read()

                    combined_diagrams[domain_path] = {
                        'content': diagram_content,
                        'path': diagram_path,
                        'name': std_name
                    }

                    logger.info(f"Found combined diagram: {domain_path} -> {std_name}")

        logger.info(f"Found {len(combined_diagrams)} combined diagrams")
        return combined_diagrams

    def add_combined_diagrams_to_json(self, taxonomy_json: Dict[str, Any], combined_diagrams: Dict[str, Dict[str, str]]) -> Dict[str, Any]:
        """
        Add combined diagrams and the codebase overview diagram to the JSON structure.

        Args:
            taxonomy_json: The taxonomy JSON structure
            combined_diagrams: Dictionary mapping domain paths to diagram info for combined diagrams

        Returns:
            Updated taxonomy JSON structure with combined diagrams
        """
        logger.info("Adding combined diagrams to JSON structure")

        # First, handle the codebase overview diagram
        if "Codebase Overview" in combined_diagrams:
            overview_info = combined_diagrams["Codebase Overview"]
            taxonomy_json["codebase_overview"] = {
                "name": "Codebase Overview",
                "diagram": overview_info["content"],
                "diagram_path": overview_info["path"],
                "diagram_name": overview_info["name"]
            }
            logger.info("Added codebase overview diagram to JSON structure")

        # Then, handle the combined diagrams for top-level domains
        for domain_path, diagram_info in combined_diagrams.items():
            if domain_path == "Codebase Overview":
                continue  # Already handled

            # Extract the domain name from the path
            if " (combined)" in domain_path:
                domain_name = domain_path.replace(" (combined)", "")

                # Find the corresponding node in the taxonomy JSON
                def find_and_update_node(node):
                    if node.get("name") == domain_name:
                        # Add the combined diagram to this node
                        node["combined_diagram"] = diagram_info["content"]
                        node["combined_diagram_path"] = diagram_info["path"]
                        node["combined_diagram_name"] = diagram_info["name"]
                        return True

                    if "children" in node:
                        for child in node["children"]:
                            if find_and_update_node(child):
                                return True

                    return False

                if find_and_update_node(taxonomy_json):
                    logger.info(f"Added combined diagram for {domain_name} to JSON structure")
                else:
                    logger.warning(f"Could not find node for combined diagram: {domain_path}")

        return taxonomy_json

    def map_taxonomy(self) -> DomainTaxonomyMapperResult:
        """
        Map domain taxonomy from domain analysis, traces, and diagrams.

        Returns:
            DomainTaxonomyMapperResult containing the mapping results
        """
        result = DomainTaxonomyMapperResult()

        try:
            # Read domain analysis, traces, and diagrams
            logger.info("Step 1: Reading domain analysis YAML")
            domain_data = self.read_domain_analysis()

            logger.info("Step 2: Reading domain traces YAML")
            self.domain_traces = self.read_domain_traces()
            logger.info(f"Found {len(self.domain_traces)} domain traces")

            logger.info("Step 3: Reading domain diagrams")
            self.domain_diagrams = self.read_domain_diagrams()
            logger.info(f"Found {len(self.domain_diagrams)} domain diagrams")

            # Log some sample diagrams for debugging
            diagram_paths = list(self.domain_diagrams.keys())
            if diagram_paths:
                logger.info(f"Sample diagram paths (up to 5): {diagram_paths[:5]}")

            # Find combined diagrams and codebase overview diagram
            logger.info("Step 4: Finding combined diagrams")
            combined_diagrams = self.find_combined_diagrams()

            # Build taxonomy tree
            logger.info("Step 5: Building taxonomy tree")
            root = self.build_taxonomy_tree(domain_data)
            logger.info(f"Built taxonomy tree with {len(self.path_to_node)} nodes")

            # Log some sample nodes for debugging
            node_paths = list(self.path_to_node.keys())
            if node_paths:
                logger.info(f"Sample node paths (up to 5): {node_paths[:5]}")

            # Map functions and diagrams to taxonomy
            logger.info("Step 6: Mapping functions to taxonomy")
            self.map_functions_to_taxonomy(root, self.domain_traces)

            logger.info("Step 7: Mapping diagrams to taxonomy")
            self.map_diagrams_to_taxonomy(root, self.domain_diagrams)

            # Convert taxonomy tree to JSON
            logger.info("Step 8: Converting taxonomy tree to JSON")
            taxonomy_json = self.taxonomy_tree_to_json(root)

            # Add combined diagrams to the JSON structure
            logger.info("Step 9: Adding combined diagrams to JSON structure")
            taxonomy_json = self.add_combined_diagrams_to_json(taxonomy_json, combined_diagrams)

            # Count diagrams and diagram names in the taxonomy tree
            diagram_count = 0
            diagram_name_count = 0

            def count_diagrams(node_dict):
                nonlocal diagram_count, diagram_name_count
                if 'diagram_path' in node_dict and node_dict['diagram_path']:
                    diagram_count += 1
                if 'diagram_name' in node_dict and node_dict['diagram_name']:
                    diagram_name_count += 1
                if 'children' in node_dict:
                    for child in node_dict['children']:
                        count_diagrams(child)

            count_diagrams(taxonomy_json)
            logger.info(f"Taxonomy tree contains {diagram_count} diagrams and {diagram_name_count} diagram names")

            # If we have diagrams but no diagram names, something went wrong
            if diagram_count > 0 and diagram_name_count == 0:
                logger.warning("No diagram names found in taxonomy tree despite having diagrams!")

                # Add diagram names directly to the JSON structure as a last resort
                if self.diagram_name_mapping:
                    logger.info("Attempting to add diagram names directly to JSON structure")

                    def add_diagram_names(node_dict):
                        if 'full_path' in node_dict and node_dict['full_path'] and 'diagram_path' in node_dict:
                            # Try to find a matching diagram name
                            for std_name, domain_path in self.diagram_name_mapping.items():
                                if domain_path == node_dict['full_path']:
                                    node_dict['diagram_name'] = std_name
                                    logger.debug(f"Added diagram_name {std_name} directly to JSON for path {domain_path}")
                                    break

                        if 'children' in node_dict:
                            for child in node_dict['children']:
                                add_diagram_names(child)

                    add_diagram_names(taxonomy_json)

                    # Count again
                    diagram_name_count = 0
                    count_diagrams(taxonomy_json)
                    logger.info(f"After direct addition, taxonomy tree contains {diagram_name_count} diagram names")

            # Save taxonomy JSON
            logger.info("Step 8: Saving taxonomy JSON")
            self.save_taxonomy_json(taxonomy_json)

            # Set result
            result.taxonomy_json = taxonomy_json
            result.output_path = self.output_json_path

            logger.info("Domain taxonomy mapping completed successfully")

        except Exception as e:
            logger.error(f"Error mapping domain taxonomy: {e}")
            import traceback
            logger.error(traceback.format_exc())
            result.success = False
            result.error_message = str(e)

        return result

def main():
    """Main entry point for the domain taxonomy mapper."""
    import argparse

    parser = argparse.ArgumentParser(description="Map domain taxonomy from analysis, traces, and diagrams")
    parser.add_argument("--domain-analysis", required=True, help="Path to the domain analysis YAML file")
    parser.add_argument("--domain-traces", required=True, help="Path to the domain traces YAML file")
    parser.add_argument("--domain-diagrams", required=True, help="Directory containing the generated mermaid diagrams")
    parser.add_argument("--output", required=True, help="Path to save the output JSON file")

    args = parser.parse_args()

    try:
        mapper = DomainTaxonomyMapper(
            domain_analysis_yaml_path=args.domain_analysis,
            domain_traces_yaml_path=args.domain_traces,
            domain_diagrams_dir=args.domain_diagrams,
            output_json_path=args.output
        )

        result = mapper.map_taxonomy()

        if result.success:
            logger.info("Domain taxonomy mapping completed successfully")
            return 0
        else:
            logger.error(f"Domain taxonomy mapping failed: {result.error_message}")
            return 1

    except Exception as e:
        logger.error(f"Error in domain taxonomy mapping: {e}")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
