# Domain-File Repomap Service

This microservice is responsible for creating a combined artifact that maps domains to files and their code content. It reads domain mapper output and repository maps to produce a hierarchical relationship of domain -> file -> code.

## Features

- Create domain-file repomaps from domain mapper output and repository maps
- Generate statistics for each domain (file count, token count)
- REST API for integration with other services
- Background job processing with status tracking

## API Endpoints

- `POST /api/v1/generate`: Generate domain-file repomap
- `GET /api/v1/status/{job_id}`: Get job status
- `GET /api/v1/artifacts/{job_id}`: Get job artifacts
- `GET /api/v1/health`: Health check

## Configuration

The service can be configured using environment variables or a configuration file. See the `config.py` file for available configuration options.

## Installation

```bash
# Install dependencies
pip install -r requirements.txt

# Run the service
python -m src.main
```

## Docker

```bash
# Build the Docker image
docker build -t domain-file-repomap-service .

# Run the Docker container
docker run -p 8003:8003 domain-file-repomap-service
```

## Usage

```bash
# Generate domain-file repomap
curl -X POST http://localhost:8003/api/v1/generate \
  -H "Content-Type: application/json" \
  -d '{
    "domain_mapper_path": "/path/to/domain_mapper.yaml",
    "repomap_path": "/path/to/repomap.json"
  }'

# Get job status
curl -X GET http://localhost:8003/api/v1/status/{job_id}

# Get job artifacts
curl -X GET http://localhost:8003/api/v1/artifacts/{job_id}
```
