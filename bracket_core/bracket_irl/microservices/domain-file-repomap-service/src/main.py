"""
Main entry point for the Domain-File Repomap Service.
"""

import os
import sys
import asyncio
from fastapi import FastAPI, BackgroundTasks, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

# Add parent directory to path to import bracket_irl_common
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../")))

# Import common utilities
# Import directly from bracket_irl_common package
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.config import Config
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.logging import ServiceLogger
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.storage import get_storage_client
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.models import Job, JobStatus, Artifact, ArtifactType, DomainFileRepomapRequest

# Import service-specific modules
from src.api.routes import router as api_router
from src.core.config import get_config, initialize_config
from src.services.job_service import JobService

# Initialize configuration
config = initialize_config()

# Initialize logger
logger = ServiceLogger(
    service_name=config.service_name,
    log_level=config.log_level,
    json_logs=False
).logger

# Initialize storage client
storage_client = get_storage_client(
    storage_type=config.storage_type,
    base_path=config.storage_path
)

# Initialize job service
job_service = JobService(storage_client=storage_client)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Lifespan context manager for FastAPI application.

    This function is called when the application starts and stops.
    """
    # Startup
    logger.info(f"Starting {config.service_name}")

    # Create necessary directories
    os.makedirs(os.path.join(config.storage_path, "jobs"), exist_ok=True)
    os.makedirs(os.path.join(config.storage_path, "artifacts"), exist_ok=True)

    yield

    # Shutdown
    logger.info(f"Shutting down {config.service_name}")

# Create FastAPI application
app = FastAPI(
    title="Domain-File Repomap Service",
    description="Service for creating domain-file repomaps",
    version="0.1.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API router
app.include_router(api_router, prefix=config.api_prefix)

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "ok", "service": config.service_name}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host=config.host, port=config.port)
