"""
Domain-file repomap generator service for the Domain-File Repomap Service.
"""

import os
import json
import yaml
import time
import logging
from typing import Dict, List, Any, Optional, Tuple

from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.models import Job, JobStatus, Artifact, ArtifactType
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.storage import StorageClient
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.utils.token_counter import num_tokens_from_string

from bracket_irl.bracket_domain_file_repomap import DomainFileRepomap

from src.services.job_service import JobService

# Configure logging
logger = logging.getLogger(__name__)

class DomainFileRepomapService:
    """
    Domain-file repomap generator service.
    """

    def __init__(self, storage_client: StorageClient, job_service: JobService):
        """
        Initialize domain-file repomap generator service.

        Args:
            storage_client: Storage client
            job_service: Job service
        """
        self.storage_client = storage_client
        self.job_service = job_service

    async def generate_domain_file_repomap(
        self,
        job_id: str,
        domain_mapper_path: str,
        repomap_path: str,
        output_format: str = "json"
    ) -> None:
        """
        Generate domain-file repomap.

        Args:
            job_id: Job ID
            domain_mapper_path: Path to domain mapper YAML file
            repomap_path: Path to repository map JSON file
            output_format: Output format (json or yaml)
        """
        try:
            # Get job
            job = self.job_service.get_job(job_id)
            if not job:
                logger.error(f"Job not found: {job_id}")
                return

            # Update job status
            job.status = JobStatus.RUNNING
            self.job_service.update_job(job)

            # Create output directory
            output_dir = os.path.join(self.storage_client.base_path, "artifacts", job_id)
            os.makedirs(output_dir, exist_ok=True)

            # Generate domain-file repomap
            output_path = os.path.join(output_dir, f"domain_file_repomap.{output_format}")

            # Log the paths we're working with for debugging
            logger.info(f"Domain mapper path: {domain_mapper_path}")
            logger.info(f"Repomap path: {repomap_path}")
            logger.info(f"Output path: {output_path}")

            # Check if domain mapper path exists
            if not os.path.exists(domain_mapper_path):
                logger.warning(f"Domain mapper path does not exist: {domain_mapper_path}")

                # Try to find the domain mapping file in other job directories
                found = False
                artifacts_dir = os.path.join(self.storage_client.base_path, "artifacts") if hasattr(self.storage_client, "base_path") else "/app/data/artifacts"
                logger.info(f"Looking for domain mapping files in artifacts directory: {artifacts_dir}")

                # First, try to find the file in the current job's directory
                # This is the most likely location for the file-domain-mapper service output
                current_job_yaml_path = os.path.join(artifacts_dir, job_id, "file_domain_mapping", "file_domain_mapping.yaml")
                logger.info(f"Checking for YAML file at: {current_job_yaml_path}")

                if os.path.exists(current_job_yaml_path) and os.path.getsize(current_job_yaml_path) > 0:
                    logger.info(f"Found domain mapping YAML file for current job at {current_job_yaml_path}")
                    # Use this file directly
                    domain_mapper_path = current_job_yaml_path
                    found = True
                else:
                    # If not found in the current job directory, search all job directories
                    if os.path.exists(artifacts_dir):
                        logger.info(f"Searching all job directories in {artifacts_dir}")
                        for job_dir in os.listdir(artifacts_dir):
                            # First try to find the YAML file
                            yaml_path = os.path.join(artifacts_dir, job_dir, "file_domain_mapping", "file_domain_mapping.yaml")
                            logger.info(f"Checking for YAML file at: {yaml_path}")

                            if os.path.exists(yaml_path) and os.path.getsize(yaml_path) > 0:
                                # Found an alternative domain mapping YAML file
                                logger.info(f"Found alternative domain mapping YAML file at {yaml_path}")
                                # Copy it to the expected location
                                import shutil
                                os.makedirs(os.path.dirname(domain_mapper_path), exist_ok=True)
                                shutil.copy(yaml_path, domain_mapper_path)
                                found = True
                                break

                            # If YAML not found, try the JSON file (for backward compatibility)
                            json_path = os.path.join(artifacts_dir, job_dir, "domain_mapping.json")
                            logger.info(f"Checking for JSON file at: {json_path}")

                            if os.path.exists(json_path) and os.path.getsize(json_path) > 0:
                                # Found an alternative domain mapping JSON file
                                logger.info(f"Found alternative domain mapping JSON file at {json_path}")
                                # Copy it to the expected location
                                import shutil
                                os.makedirs(os.path.dirname(domain_mapper_path), exist_ok=True)
                                shutil.copy(json_path, domain_mapper_path)
                                found = True
                                break

                if not found:
                    # Create a default domain mapping file in YAML format
                    logger.warning(f"Creating default domain mapping file at {domain_mapper_path}")
                    os.makedirs(os.path.dirname(domain_mapper_path), exist_ok=True)
                    with open(domain_mapper_path, 'w') as f:
                        yaml.dump({"Unclassified": []}, f, default_flow_style=False)

            # Create DomainFileRepomap instance
            repomap_generator = DomainFileRepomap(
                domain_file_mapped_yaml_path=domain_mapper_path,
                repomap_path=repomap_path,
                output_path=output_path
            )

            # Generate and save domain-file repomap
            start_time = time.time()
            repomap_generator.generate_and_save()
            elapsed_time = time.time() - start_time

            # Add artifact to job
            artifact = self.job_service.add_artifact(
                job_id=job_id,
                artifact_path=output_path,
                artifact_type=ArtifactType.DOMAIN_FILE_REPOMAP
            )

            # Update job status
            job.status = JobStatus.COMPLETED

            # Try to get domain stats
            try:
                # Read the generated file to get domain stats
                with open(output_path, 'r') as f:
                    domain_file_repomap = json.load(f)

                domain_count = len(domain_file_repomap)
                file_count = sum(len(files) for files in domain_file_repomap.values())
            except Exception as e:
                logger.error(f"Error reading domain file repomap for stats: {e}")
                domain_count = 0
                file_count = 0

            # Store stats in the result field instead of a non-existent stats field
            if not job.result:
                job.result = {}

            job.result["stats"] = {
                "elapsed_time": elapsed_time,
                "domain_count": domain_count,
                "file_count": file_count
            }

            try:
                self.job_service.update_job(job)
                logger.info(f"Domain-file repomap generation completed for job {job_id}")
            except Exception as update_error:
                # Handle the specific error about job.stats
                if "Job\" object has no field \"stats\"" in str(update_error):
                    logger.warning(f"Caught error about missing stats field. Using result field instead.")
                    # Make sure we're only using the result field, not trying to access job.stats directly
                    job.status = JobStatus.COMPLETED
                    self.job_service.update_job(job)
                    logger.info(f"Domain-file repomap generation completed for job {job_id} (with stats in result field)")
                else:
                    # Re-raise if it's a different error
                    raise update_error

        except Exception as e:
            logger.error(f"Domain-file repomap generation failed for job {job_id}: {str(e)}")

            # Update job status
            try:
                job = self.job_service.get_job(job_id)
                if job:
                    job.status = JobStatus.FAILED
                    job.error = str(e)
                    self.job_service.update_job(job)
            except Exception as update_error:
                logger.error(f"Failed to update job status: {str(update_error)}")
