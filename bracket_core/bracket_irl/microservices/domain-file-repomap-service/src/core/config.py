"""
Configuration for the Domain-File Repomap Service.
"""

import os
from functools import lru_cache
from typing import Dict, Any, Optional

from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.config import Config

# Default configuration
DEFAULT_CONFIG = {
    "service_name": "domain-file-repomap-service",
    "host": "0.0.0.0",
    "port": 8003,
    "log_level": "INFO",
    "api_prefix": "/api/v1",
    "storage_type": "local",
    "storage_path": "./data",
    "max_concurrent_tasks": 10,
    "job_timeout_seconds": 3600,
    "additional_config": {}
}

@lru_cache()
def get_config() -> Config:
    """
    Get service configuration.
    
    Returns:
        Config object
    """
    return Config(**DEFAULT_CONFIG)

def initialize_config() -> Config:
    """
    Initialize service configuration.
    
    This function loads configuration from environment variables and returns a Config object.
    
    Returns:
        Config object
    """
    # Start with default configuration
    config_dict = DEFAULT_CONFIG.copy()
    
    # Override with environment variables
    for key in config_dict.keys():
        env_key = key.upper()
        if env_key in os.environ:
            config_dict[key] = os.environ[env_key]
    
    # Create Config object
    config = Config(**config_dict)
    
    return config
