"""
API routes for the Domain-File Repomap Service.
"""

import os
import time
from typing import Dict, Any, Optional
from fastapi import <PERSON>Rout<PERSON>, BackgroundTasks, HTTPException, Depends, Path, Query
from pydantic import BaseModel, Field

from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.models import DomainFileRepomapRequest, Job, JobStatus, Artifact, ArtifactType
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.storage import StorageClient, get_storage_client

from src.api.models import GenerateDomainFileRepomapResponse, JobStatusResponse, ArtifactResponse, ErrorResponse
from src.core.config import get_config
from src.services.job_service import JobService
from src.services.repomap_generator import DomainFileRepomapService

# Extended request model with optional job_id
class DomainFileRepomapRequestWithJobId(DomainFileRepomapRequest):
    """Extended domain file repomap request with optional job_id."""
    job_id: Optional[str] = Field(None, description="Optional job ID to use instead of creating a new one")

# Create router
router = APIRouter(tags=["Domain-File Repomap"])

# Get dependencies
def get_job_service() -> JobService:
    """Get job service."""
    config = get_config()
    storage_client = get_storage_client(
        storage_type=config.storage_type,
        base_path=config.storage_path
    )
    return JobService(storage_client=storage_client)

def get_repomap_service() -> DomainFileRepomapService:
    """Get domain-file repomap service."""
    config = get_config()
    storage_client = get_storage_client(
        storage_type=config.storage_type,
        base_path=config.storage_path
    )
    return DomainFileRepomapService(
        storage_client=storage_client,
        job_service=JobService(storage_client=storage_client)
    )

@router.post(
    "/generate",
    response_model=GenerateDomainFileRepomapResponse,
    responses={
        400: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def generate_domain_file_repomap(
    request: DomainFileRepomapRequestWithJobId,
    background_tasks: BackgroundTasks,
    job_service: JobService = Depends(get_job_service),
    repomap_service: DomainFileRepomapService = Depends(get_repomap_service)
):
    """
    Generate domain-file repomap.

    Args:
        request: Domain-file repomap generation request
        background_tasks: Background tasks
        job_service: Job service
        repomap_service: Domain-file repomap service

    Returns:
        Job ID and message
    """
    try:
        # Validate input files
        domain_mapping_path = request.domain_mapping_path
        if not domain_mapping_path.startswith('/'):
            domain_mapping_path = f"/app/data/{domain_mapping_path}"

        if not os.path.exists(domain_mapping_path):
            # Try to find the domain mapping file in other job directories
            found = False
            artifacts_dir = "/app/data/artifacts"
            if os.path.exists(artifacts_dir):
                for job_dir in os.listdir(artifacts_dir):
                    alt_path = os.path.join(artifacts_dir, job_dir, "domain_mapping.json")
                    if os.path.exists(alt_path) and os.path.getsize(alt_path) > 0:
                        # Found an alternative domain mapping file
                        print(f"Found alternative domain mapping file at {alt_path}")
                        # Copy it to the expected location
                        import shutil
                        os.makedirs(os.path.dirname(domain_mapping_path), exist_ok=True)
                        shutil.copy(alt_path, domain_mapping_path)
                        found = True
                        break

            if not found:
                raise HTTPException(
                    status_code=400,
                    detail=f"Domain mapping file not found: {request.domain_mapping_path}"
                )

        repomap_path = request.repomap_path
        if not repomap_path.startswith('/'):
            repomap_path = f"/app/data/{repomap_path}"

        if not os.path.exists(repomap_path):
            # Try to find the repomap file in other job directories
            found = False
            artifacts_dir = "/app/data/artifacts"
            if os.path.exists(artifacts_dir):
                for job_dir in os.listdir(artifacts_dir):
                    alt_path = os.path.join(artifacts_dir, job_dir, "repomap.json")
                    if os.path.exists(alt_path) and os.path.getsize(alt_path) > 0:
                        # Found an alternative repomap file
                        print(f"Found alternative repomap file at {alt_path}")
                        # Copy it to the expected location
                        import shutil
                        os.makedirs(os.path.dirname(repomap_path), exist_ok=True)
                        shutil.copy(alt_path, repomap_path)
                        found = True
                        break

            if not found:
                raise HTTPException(
                    status_code=400,
                    detail=f"Repository map file not found: {request.repomap_path}"
                )

        # Use provided job_id or create a new job
        job_id = request.job_id
        if job_id:
            # Check if job exists
            job = job_service.get_job(job_id)
            if not job:
                # Create job with the specified ID
                job = Job(
                    job_id=job_id,
                    status=JobStatus.PENDING,
                    created_at=time.time(),
                    updated_at=time.time()
                )
                # Save job
                job_service.storage_client.write_json(f"jobs/{job.job_id}.json", job.model_dump())
            else:
                # Update existing job
                job.status = JobStatus.PENDING
                job.updated_at = time.time()
                job_service.update_job(job)
        else:
            # Create a new job
            job = job_service.create_job()
            job_id = job.job_id

        # Start background task
        background_tasks.add_task(
            repomap_service.generate_domain_file_repomap,
            job_id=job_id,
            domain_mapper_path=request.domain_mapping_path,  # Use domain_mapping_path from the request
            repomap_path=request.repomap_path,
            output_format=request.output_format if hasattr(request, 'output_format') else "json"
        )

        return GenerateDomainFileRepomapResponse(
            job_id=job_id,
            message="Domain-file repomap generation started"
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start domain-file repomap generation: {str(e)}"
        )

@router.get(
    "/status/{job_id}",
    response_model=JobStatusResponse,
    responses={
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def get_job_status(
    job_id: str = Path(..., description="Job ID"),
    job_service: JobService = Depends(get_job_service)
):
    """
    Get job status.

    Args:
        job_id: Job ID
        job_service: Job service

    Returns:
        Job status
    """
    try:
        job = job_service.get_job(job_id)
        if not job:
            raise HTTPException(
                status_code=404,
                detail=f"Job not found: {job_id}"
            )

        return JobStatusResponse(job=job)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get job status: {str(e)}"
        )

@router.get(
    "/artifacts/{job_id}",
    response_model=ArtifactResponse,
    responses={
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def get_job_artifacts(
    job_id: str = Path(..., description="Job ID"),
    include_content: bool = Query(False, description="Include artifact content"),
    job_service: JobService = Depends(get_job_service)
):
    """
    Get job artifacts.

    Args:
        job_id: Job ID
        include_content: Whether to include artifact content
        job_service: Job service

    Returns:
        Job artifacts
    """
    try:
        job = job_service.get_job(job_id)
        if not job:
            raise HTTPException(
                status_code=404,
                detail=f"Job not found: {job_id}"
            )

        # Check if job has artifacts in result
        if not job.result or "artifacts" not in job.result or not job.result["artifacts"]:
            raise HTTPException(
                status_code=404,
                detail=f"No artifacts found for job: {job_id}"
            )

        # Get the first artifact ID (there should only be one)
        artifact_id = job.result["artifacts"][0]

        # Try to find the artifact file
        artifact_path = f"artifacts/{artifact_id}.json"
        try:
            artifact_data = job_service.storage_client.read_json(artifact_path)
            artifact = Artifact(**artifact_data)
        except Exception as e:
            raise HTTPException(
                status_code=404,
                detail=f"Artifact not found: {artifact_id}"
            )

        # Include content if requested
        content = None
        if include_content:
            try:
                content = job_service.storage_client.read_json(artifact.path)
            except Exception as e:
                content = {"error": f"Failed to read artifact content: {str(e)}"}

        return ArtifactResponse(
            artifact=artifact,
            content=content
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get job artifacts: {str(e)}"
        )

@router.get(
    "/artifacts/{job_id}/{artifact_type}",
    response_model=ArtifactResponse,
    responses={
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def get_artifact(
    job_id: str = Path(..., description="Job ID"),
    artifact_type: ArtifactType = Path(..., description="Artifact type"),
    include_content: bool = Query(False, description="Include artifact content"),
    job_service: JobService = Depends(get_job_service)
):
    """
    Get artifact by type.

    Args:
        job_id: Job ID
        artifact_type: Artifact type
        include_content: Include artifact content
        job_service: Job service

    Returns:
        Artifact details and optionally content
    """
    try:
        # Get job
        job = job_service.get_job(job_id)
        if not job:
            raise HTTPException(
                status_code=404,
                detail=f"Job not found: {job_id}"
            )

        # First, try to find the artifact directly in the job directory using the standardized path
        artifact_found = None
        try:
            # Look for the file directly in the job directory using the standardized path
            artifact_filename = f"{artifact_type.value}.json"
            direct_path = f"/app/data/artifacts/{job_id}/{artifact_filename}"

            if os.path.exists(direct_path) and os.path.getsize(direct_path) > 0:
                # Create a temporary artifact object
                artifact_found = Artifact(
                    artifact_id=f"temp_{job_id}_{artifact_type.value}",
                    job_id=job_id,
                    artifact_type=artifact_type,
                    created_at=time.time(),
                    path=direct_path
                )

                # Include content if requested
                content = None
                if include_content:
                    try:
                        content = job_service.storage_client.read_json(direct_path)
                    except Exception as e:
                        content = {"error": f"Failed to read artifact content: {str(e)}"}

                return ArtifactResponse(
                    artifact=artifact_found,
                    content=content
                )
        except Exception as e:
            # Log the error but continue with other methods
            print(f"Error finding artifact directly: {str(e)}")
            pass

        # If we couldn't find the artifact directly, check if job has artifacts in result
        if not job.result or "artifacts" not in job.result or not job.result["artifacts"]:
            raise HTTPException(
                status_code=404,
                detail=f"No artifacts found for job: {job_id}"
            )

        # Find artifact of the specified type
        for artifact_id in job.result["artifacts"]:
            # Try to find the artifact file
            artifact_path = f"artifacts/{artifact_id}.json"
            try:
                artifact_data = job_service.storage_client.read_json(artifact_path)
                artifact = Artifact(**artifact_data)

                # Check if this is the artifact type we're looking for
                if artifact.artifact_type == artifact_type:
                    artifact_found = artifact
                    break
            except Exception as e:
                continue

        if not artifact_found:
            raise HTTPException(
                status_code=404,
                detail=f"Artifact of type {artifact_type.value} not found for job: {job_id}"
            )

        # Include content if requested
        content = None
        if include_content:
            try:
                content = job_service.storage_client.read_json(artifact_found.path)
            except Exception as e:
                content = {"error": f"Failed to read artifact content: {str(e)}"}

        return ArtifactResponse(
            artifact=artifact_found,
            content=content
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get artifact: {str(e)}"
        )
