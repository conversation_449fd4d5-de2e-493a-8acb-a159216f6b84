"""
API models for the Domain-File Repomap Service.
"""

from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field

from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.models import DomainFileRepomapRequest, Job, Artifact

class GenerateDomainFileRepomapResponse(BaseModel):
    """Response model for domain-file repomap generation."""
    job_id: str = Field(..., description="Job ID")
    message: str = Field(..., description="Response message")

class JobStatusResponse(BaseModel):
    """Response model for job status."""
    job: Job = Field(..., description="Job details")

class ArtifactResponse(BaseModel):
    """Response model for artifact."""
    artifact: Artifact = Field(..., description="Artifact details")
    content: Optional[Dict[str, Any]] = Field(None, description="Artifact content")

class ErrorResponse(BaseModel):
    """Error response model."""
    detail: str = Field(..., description="Error message")
