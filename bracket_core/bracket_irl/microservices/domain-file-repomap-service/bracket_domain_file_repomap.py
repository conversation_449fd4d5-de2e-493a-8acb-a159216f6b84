#!/usr/bin/env python3
"""
Domain-File Repomap Generator

This script creates a combined artifact that maps domains to files and their code content.
It reads:
1. A domain mapper YAML file (output from exp_file_domain_mapper_batched.py)
2. A repomap JSON file (output from exp_complete_repomap.py)

And produces a new artifact that shows the hierarchical relationship:
domain -> file -> code

The code for each file is combined into a single string rather than processed line by line.

Additionally, it generates statistics for each domain including:
- Number of files
- Estimated token count

Usage:
    python domain_file_repomap.py --domain-yaml <path_to_domain_yaml> --repomap <path_to_repomap> --output <output_path>
"""

import os
import yaml
import json
import argparse
import logging
import tiktoken
from typing import Dict, List, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Initialize tokenizer for token counting
try:
    # Use cl100k_base tokenizer (used by GPT-4 and GPT-3.5-turbo)
    TOKENIZER = tiktoken.get_encoding("cl100k_base")
except Exception as e:
    logger.warning(f"Failed to initialize tokenizer: {e}. Token counting will be approximate.")
    TOKENIZER = None


class DomainFileRepomap:
    """
    Generator for domain-file repomap artifacts.

    This class reads a domain mapper YAML and a repomap JSON file,
    and produces a combined artifact showing domains -> files -> code.
    The code for each file is combined into a single string.
    """

    def __init__(
        self,
        domain_file_mapped_yaml_path: str,
        repomap_path: str,
        output_path: str
    ):
        """
        Initialize the DomainFileRepomap generator.

        Args:
            domain_yaml_path: Path to the domain mapper YAML file
            repomap_path: Path to the repomap JSON file
            output_path: Path to save the output artifact
        """
        self.domain_file_mapped_yaml_path = domain_file_mapped_yaml_path
        self.repomap_path = repomap_path
        self.output_path = output_path

        # Log the input paths
        logger.info(f"Initializing DomainFileRepomap with:")
        logger.info(f"  domain_file_mapped_yaml_path: {domain_file_mapped_yaml_path}")
        logger.info(f"  repomap_path: {repomap_path}")
        logger.info(f"  output_path: {output_path}")

        # Check input files but don't raise exceptions - we'll handle missing files in the read methods
        if not os.path.exists(domain_file_mapped_yaml_path):
            logger.warning(f"Domain YAML file not found: {domain_file_mapped_yaml_path}")
            logger.info("Will attempt to find alternative domain mapping files during processing")

        if not os.path.exists(repomap_path):
            logger.warning(f"Repomap file not found: {repomap_path}")
            logger.info("Will attempt to find alternative repomap files during processing")

        # Create output directory if it doesn't exist
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)

    def read_domain_yaml(self) -> Dict[str, List[str]]:
        """
        Read the domain mapper YAML file.

        Returns:
            Dictionary mapping domain paths to lists of file paths
        """
        try:
            logger.info(f"Attempting to read domain mapping from: {self.domain_file_mapped_yaml_path}")

            # Check if the file exists
            if not os.path.exists(self.domain_file_mapped_yaml_path):
                logger.warning(f"Domain mapping file does not exist: {self.domain_file_mapped_yaml_path}")

                # Try to find the file in a different location
                # First, check if this is a path like "artifacts/job_id/domain_mapping.json"
                # and try to find "artifacts/job_id/file_domain_mapping/file_domain_mapping.yaml" instead
                if 'artifacts' in self.domain_file_mapped_yaml_path and 'domain_mapping.json' in self.domain_file_mapped_yaml_path:
                    job_dir = os.path.dirname(self.domain_file_mapped_yaml_path)
                    yaml_path = os.path.join(job_dir, "file_domain_mapping", "file_domain_mapping.yaml")
                    logger.info(f"Trying alternative YAML path: {yaml_path}")

                    if os.path.exists(yaml_path):
                        logger.info(f"Found alternative domain mapping YAML file at {yaml_path}")
                        self.domain_file_mapped_yaml_path = yaml_path
                    else:
                        logger.warning(f"Alternative YAML path not found: {yaml_path}")

            # First try to read as YAML
            if os.path.exists(self.domain_file_mapped_yaml_path):
                try:
                    with open(self.domain_file_mapped_yaml_path, 'r') as f:
                        domain_data = yaml.safe_load(f)

                    if domain_data:
                        logger.info(f"Successfully read domain YAML from: {self.domain_file_mapped_yaml_path}")
                        return domain_data
                    else:
                        logger.warning(f"Domain YAML file is empty or invalid: {self.domain_file_mapped_yaml_path}")
                except Exception as yaml_err:
                    logger.error(f"Error reading domain YAML: {yaml_err}")
            else:
                logger.warning(f"Domain YAML file does not exist: {self.domain_file_mapped_yaml_path}")

            # If YAML reading fails, try JSON
            if self.domain_file_mapped_yaml_path.endswith('.json'):
                # Already a JSON path, just try to read it
                json_path = self.domain_file_mapped_yaml_path
            else:
                # Try to find a corresponding JSON file
                json_path = os.path.join(os.path.dirname(os.path.dirname(self.domain_file_mapped_yaml_path)), 'domain_mapping.json')
                logger.info(f"Trying JSON path: {json_path}")

            if os.path.exists(json_path):
                try:
                    with open(json_path, 'r') as f:
                        domain_data = json.load(f)

                    if domain_data:
                        logger.info(f"Successfully read domain mapping from JSON: {json_path}")
                        return domain_data
                    else:
                        logger.warning(f"Domain mapping JSON file is empty: {json_path}")
                except Exception as json_err:
                    logger.error(f"Error reading domain mapping JSON: {json_err}")
            else:
                logger.warning(f"Domain mapping JSON file does not exist: {json_path}")

            # Try to find the file in the file_domain_mapping directory
            # This is where the file-domain-mapper service outputs its YAML file
            artifacts_dir = "/app/data/artifacts"
            if os.path.exists(artifacts_dir):
                logger.info(f"Searching for domain mapping files in artifacts directory: {artifacts_dir}")
                for job_dir in os.listdir(artifacts_dir):
                    yaml_path = os.path.join(artifacts_dir, job_dir, "file_domain_mapping", "file_domain_mapping.yaml")
                    logger.info(f"Checking for YAML file at: {yaml_path}")

                    if os.path.exists(yaml_path) and os.path.getsize(yaml_path) > 0:
                        try:
                            with open(yaml_path, 'r') as f:
                                domain_data = yaml.safe_load(f)

                            if domain_data:
                                logger.info(f"Successfully read domain mapping from alternative YAML: {yaml_path}")
                                return domain_data
                        except Exception as e:
                            logger.error(f"Error reading alternative YAML file: {e}")

            # If all attempts fail, create a default mapping
            logger.warning("Creating default domain mapping with 'Unclassified' domain")
            return {"Unclassified": []}
        except Exception as e:
            logger.error(f"Error reading domain mapping: {e}")
            # Return a default mapping instead of raising an exception
            logger.warning("Creating default domain mapping with 'Unclassified' domain due to error")
            return {"Unclassified": []}

    def parse_repomap(self) -> Dict[str, List[str]]:
        """
        Parse the repomap JSON file to extract file paths and their code content.

        Returns:
            Dictionary mapping file paths to their code content as a list of strings
        """
        try:
            logger.info(f"Attempting to parse repomap from: {self.repomap_path}")

            # Check if the file exists
            if not os.path.exists(self.repomap_path):
                logger.warning(f"Repomap file does not exist: {self.repomap_path}")

                # Try to find the repomap file in the artifacts directory
                artifacts_dir = "/app/data/artifacts"
                found = False

                if os.path.exists(artifacts_dir):
                    logger.info(f"Searching for repomap files in artifacts directory: {artifacts_dir}")
                    for job_dir in os.listdir(artifacts_dir):
                        repomap_path = os.path.join(artifacts_dir, job_dir, "repomap.json")
                        logger.info(f"Checking for repomap file at: {repomap_path}")

                        if os.path.exists(repomap_path) and os.path.getsize(repomap_path) > 0:
                            logger.info(f"Found alternative repomap file at {repomap_path}")
                            self.repomap_path = repomap_path
                            found = True
                            break

                if not found:
                    logger.error(f"Could not find any repomap file")
                    # Return an empty repomap as a fallback
                    return {}

            # Parse the repomap file
            with open(self.repomap_path, 'r') as f:
                repomap_structure = json.load(f)

            # Count total files
            total_files = len(repomap_structure)
            logger.info(f"Parsed repomap JSON: {total_files} files")
            return repomap_structure
        except Exception as e:
            logger.error(f"Error parsing repomap JSON: {e}")
            logger.info("Returning empty repomap as fallback")
            return {}

    def generate_domain_file_repomap(self) -> Dict[str, Dict[str, str]]:
        """
        Generate the domain-file repomap.

        Returns:
            Nested dictionary mapping domains to files to code content as a string
        """
        # Read domain YAML and repomap
        domain_data = self.read_domain_yaml()
        repomap_structure = self.parse_repomap()

        # Create the combined structure
        domain_file_repomap = {}

        # Process each domain
        for domain, files in domain_data.items():
            domain_file_repomap[domain] = {}

            # Process each file in this domain
            for file_path in files:
                # Get the code for this file from the repomap
                if file_path in repomap_structure:
                    # Join the code lines into a single string
                    code_content = '\n'.join(repomap_structure[file_path])
                    domain_file_repomap[domain][file_path] = code_content
                else:
                    # File exists in domain mapping but not in repomap
                    logger.warning(f"File '{file_path}' found in domain '{domain}' but not in repomap")
                    domain_file_repomap[domain][file_path] = ""

        return domain_file_repomap

    def save_domain_file_repomap(self, domain_file_repomap: Dict[str, Dict[str, str]]) -> None:
        """
        Save the domain-file repomap to a JSON file.

        Args:
            domain_file_repomap: Nested dictionary mapping domains to files to code content as a string
        """
        try:
            with open(self.output_path, 'w') as f:
                json.dump(domain_file_repomap, f, indent=2)

            logger.info(f"Domain-file repomap saved to: {self.output_path}")
        except Exception as e:
            logger.error(f"Error saving domain-file repomap: {e}")
            raise

    def count_tokens(self, text: str) -> int:
        """Count the number of tokens in a text string."""
        if TOKENIZER:
            return len(TOKENIZER.encode(text))
        else:
            # Fallback approximation: ~4 characters per token
            return len(text) // 4

    # def count_tokens(self, text: str) -> int:
    #     """
    #     Count tokens in text using a simple approximation.

    #     Args:
    #         text: The text to count tokens for

    #     Returns:
    #         Approximate token count
    #     """
    #     # Simple approximation: count words and multiply by 1.5
    #     # This is faster than using tiktoken for large texts
    #     words = text.split()
    #     return int(len(words) * 1.5)

    def calculate_domain_statistics(self, domain_file_repomap: Dict[str, Dict[str, str]]) -> Dict[str, Dict[str, Any]]:
        """
        Calculate statistics for each domain.

        Args:
            domain_file_repomap: Nested dictionary mapping domains to files to code content as a string

        Returns:
            Dictionary with statistics for each domain
        """
        domain_stats = {}

        # Calculate statistics for each domain
        for domain, files in domain_file_repomap.items():
            # Count files
            num_files = len(files)

            # Estimate tokens
            token_count = 0

            for file_path, file_content in files.items():
                # Add tokens for the file path itself
                token_count += self.count_tokens(file_path)

                # Count tokens for file content
                if file_content:  # Only count if there's content
                    token_count += self.count_tokens(file_content)

            # Store statistics for this domain
            stats_dict = {
                "num_files": num_files,
                "token_count": token_count
            }

            domain_stats[domain] = stats_dict

        # Calculate global statistics
        total_files = sum(stats["num_files"] for stats in domain_stats.values())
        total_tokens = sum(stats["token_count"] for stats in domain_stats.values())

        # Calculate token count distribution
        token_counts = [stats["token_count"] for domain, stats in domain_stats.items() if domain != "__global__"]
        token_counts.sort()

        # Calculate percentiles and distribution statistics
        num_domains = len(token_counts)

        # Calculate distribution by size buckets
        size_buckets = {
            "tiny (0-1K)": 0,
            "small (1K-10K)": 0,
            "medium (10K-50K)": 0,
            "large (50K-100K)": 0,
            "xlarge (100K-500K)": 0,
            "xxlarge (500K+)": 0
        }

        for count in token_counts:
            if count < 1000:
                size_buckets["tiny (0-1K)"] += 1
            elif count < 10000:
                size_buckets["small (1K-10K)"] += 1
            elif count < 50000:
                size_buckets["medium (10K-50K)"] += 1
            elif count < 100000:
                size_buckets["large (50K-100K)"] += 1
            elif count < 500000:
                size_buckets["xlarge (100K-500K)"] += 1
            else:
                size_buckets["xxlarge (500K+)"] += 1

        # Create global statistics dictionary
        global_stats = {
            "total_domains": num_domains,
            "total_files": total_files,
            "total_tokens": total_tokens,
            "avg_files_per_domain": total_files / num_domains if num_domains else 0,
            "avg_tokens_per_domain": total_tokens / num_domains if num_domains else 0,
            "token_distribution": {
                "min": token_counts[0] if token_counts else 0,
                "max": token_counts[-1] if token_counts else 0,
                "median": token_counts[num_domains // 2] if num_domains else 0,
                "p10": token_counts[int(num_domains * 0.1)] if num_domains > 10 else 0,
                "p25": token_counts[int(num_domains * 0.25)] if num_domains > 4 else 0,
                "p75": token_counts[int(num_domains * 0.75)] if num_domains > 4 else 0,
                "p90": token_counts[int(num_domains * 0.9)] if num_domains > 10 else 0,
                "p95": token_counts[int(num_domains * 0.95)] if num_domains > 20 else 0,
                "p99": token_counts[int(num_domains * 0.99)] if num_domains > 100 else 0
            },
            "size_distribution": {
                "counts": size_buckets,
                "percentages": {
                    bucket: (count / num_domains * 100 if num_domains else 0)
                    for bucket, count in size_buckets.items()
                }
            }
        }

        # Add global statistics
        domain_stats["__global__"] = global_stats

        return domain_stats

    def save_domain_statistics(self, domain_stats: Dict[str, Dict[str, Any]], output_path: str) -> None:
        """
        Save domain statistics to a JSON file.

        Args:
            domain_stats: Dictionary with statistics for each domain
            output_path: Path to save the statistics JSON file
        """
        try:
            # Create the statistics output path by replacing the extension with _stats.json
            stats_output_path = os.path.splitext(output_path)[0] + "_stats.json"

            with open(stats_output_path, 'w') as f:
                json.dump(domain_stats, f, indent=2)

            logger.info(f"Domain statistics saved to: {stats_output_path}")
        except Exception as e:
            logger.error(f"Error saving domain statistics: {e}")
            raise

    def generate_and_save(self) -> str:
        """
        Generate and save the domain-file-function repomap and statistics.

        Returns:
            Path to the saved output file
        """
        # Generate the domain-file-function repomap
        domain_file_repomap = self.generate_domain_file_repomap()

        # Save the repomap
        self.save_domain_file_repomap(domain_file_repomap)

        # Calculate and save domain statistics
        domain_stats = self.calculate_domain_statistics(domain_file_repomap)
        self.save_domain_statistics(domain_stats, self.output_path)

        return self.output_path

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Generate a domain-file repomap and statistics")
    parser.add_argument("--domain-file-mapped-yaml", help="Path to the domain mapper YAML file")
    parser.add_argument("--repomap", help="Path to the repomap JSON file")
    parser.add_argument("--output", help="Path to save the output artifact")

    args = parser.parse_args()

    # Default paths for GitLab
    if not args.repomap:
        args.repomap = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/repomap/gitlab/gitlab_filtered.json"
        # args.repomap = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/repomap/pytorch/pytorch_repomap.json"
        # args.repomap = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/repomap/django/django_repomap_filtered.json"
        # args.repomap = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/repomap/bracket/bracket_repomap_filtered.json"
        # args.repomap = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/repomap/pytorch_full_exp_repomap_new1_filtered.json"
        # args.repomap = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/repomap/gitlab_full_exp_repomap_one_filtered.json"
    if not args.domain_file_mapped_yaml:
        args.domain_file_mapped_yaml = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/domain_mapping/gitlab/gitlab_domain_file_mapper.yaml"
        # args.domain_file_mapped_yaml = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/domain_mapping/django/django_domain_file_mapper.yaml"
        # args.domain_yaml = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/domain_mapping/pytorch/pytorch_oai_4_1.yaml"
        # args.domain_yaml = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/domain_mapping/gitlab/gitlab_oai_4_1.yaml"
    if not args.output:
        args.output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/domain_file_repomap/gitlab/gitlab_domain_file_repomap.json"
        # args.output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/domain_file_repomap/django/django_domain_file_repomap.json"
        # args.output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/domain_file_repomap/pytorch/pytorch_domain_file_repomap.json"
        # args.output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/domain_file_repomap/gitlab/gitlab_domain_file_repomap.json"

    # Alternative paths for PyTorch (commented out)
    # args.repomap = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/repomap/pytorch_full_exp_repomap_new1_filtered.json"
    # args.domain_yaml = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/domain_mapping/pytorch/pytorch_oai_4_1.yaml"
    # args.output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/domain_file_repomap/pytorch/pytorch_domain_file_repomap.json"

    try:
        generator = DomainFileRepomap(
            domain_file_mapped_yaml_path=args.domain_file_mapped_yaml,
            repomap_path=args.repomap,
            output_path=args.output
        )

        output_path = generator.generate_and_save()
        stats_path = os.path.splitext(output_path)[0] + "_stats.json"

        print(f"Domain-file repomap generated and saved to: {output_path}")
        print(f"Domain statistics saved to: {stats_path}")
    except Exception as e:
        logger.error(f"Error generating domain-file repomap: {e}")
        print(f"Error: {e}")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())
