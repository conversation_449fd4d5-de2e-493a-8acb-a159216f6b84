"""
API models for the Repository Mapper Service.
"""

from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field

from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.models import RepositoryMapRequest, Job, Artifact

class GenerateRepositoryMapResponse(BaseModel):
    """Response model for repository map generation."""
    job_id: str = Field(..., description="Job ID")
    message: str = Field(..., description="Response message")

class JobStatusResponse(BaseModel):
    """Response model for job status."""
    job: Job = Field(..., description="Job details")

class ArtifactResponse(BaseModel):
    """Response model for artifact."""
    artifact: Artifact = Field(..., description="Artifact details")
    content: Optional[Dict[str, Any]] = Field(None, description="Artifact content")

class ErrorResponse(BaseModel):
    """Response model for errors."""
    error: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Error details")
