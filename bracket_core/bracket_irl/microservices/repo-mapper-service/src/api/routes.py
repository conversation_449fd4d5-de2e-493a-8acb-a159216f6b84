"""
API routes for the Repository Mapper Service.
"""

import os
from typing import Dict, Any, Optional
from fastapi import APIRouter, BackgroundTasks, HTTPException, Depends, Path, Query

from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.models import RepositoryMapRequest, Job, JobStatus, Artifact, ArtifactType
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.storage import StorageClient, get_storage_client

from src.api.models import GenerateRepositoryMapResponse, JobStatusResponse, ArtifactResponse, ErrorResponse
from src.core.config import get_config
from src.services.job_service import JobService
from src.services.mapper import RepositoryMapperService

# Create router
router = APIRouter(tags=["Repository Mapper"])

# Get dependencies
def get_job_service() -> JobService:
    """Get job service."""
    config = get_config()
    storage_client = get_storage_client(
        storage_type=config.storage_type,
        base_path=config.storage_path
    )
    return JobService(storage_client=storage_client)

def get_mapper_service() -> RepositoryMapperService:
    """Get repository mapper service."""
    config = get_config()
    storage_client = get_storage_client(
        storage_type=config.storage_type,
        base_path=config.storage_path
    )
    return RepositoryMapperService(
        storage_client=storage_client,
        max_concurrent_tasks=config.max_concurrent_tasks
    )

@router.post(
    "/generate",
    response_model=GenerateRepositoryMapResponse,
    responses={
        400: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def generate_repository_map(
    request: RepositoryMapRequest,
    background_tasks: BackgroundTasks,
    job_service: JobService = Depends(get_job_service),
    mapper_service: RepositoryMapperService = Depends(get_mapper_service)
):
    """
    Generate repository map from codebase.

    Args:
        request: Repository map generation request
        background_tasks: Background tasks
        job_service: Job service
        mapper_service: Repository mapper service

    Returns:
        Job ID and message
    """
    try:
        # Validate repository directory
        repo_dir = os.path.abspath(request.repo_dir)
        if not os.path.exists(repo_dir):
            raise HTTPException(
                status_code=400,
                detail=f"Repository directory not found: {repo_dir}"
            )
        # Update the repo_dir to use the absolute path
        request.repo_dir = repo_dir

        # Create job
        job = job_service.create_job()

        # Start background task
        background_tasks.add_task(
            mapper_service.generate_repository_map,
            job_id=job.job_id,
            repo_dir=request.repo_dir,
            batch_size=request.batch_size,
            top_percentage=request.top_percentage,
            min_functions=request.min_functions,
            max_functions=request.max_functions,
            exclude_tests=request.exclude_tests,
            output_format=request.output_format,
            include_extensions=request.include_extensions
        )

        return GenerateRepositoryMapResponse(
            job_id=job.job_id,
            message="Repository map generation started"
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start repository map generation: {str(e)}"
        )

@router.get(
    "/status/{job_id}",
    response_model=JobStatusResponse,
    responses={
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def get_job_status(
    job_id: str = Path(..., description="Job ID"),
    job_service: JobService = Depends(get_job_service)
):
    """
    Get job status.

    Args:
        job_id: Job ID
        job_service: Job service

    Returns:
        Job status
    """
    try:
        job = job_service.get_job(job_id)
        if not job:
            raise HTTPException(
                status_code=404,
                detail=f"Job not found: {job_id}"
            )

        return JobStatusResponse(job=job)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get job status: {str(e)}"
        )

@router.get(
    "/artifacts/{job_id}",
    response_model=ArtifactResponse,
    responses={
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def get_artifact(
    job_id: str = Path(..., description="Job ID"),
    artifact_type: Optional[ArtifactType] = Query(None, description="Artifact type"),
    include_content: bool = Query(False, description="Whether to include artifact content"),
    job_service: JobService = Depends(get_job_service)
):
    """
    Get job artifact.

    Args:
        job_id: Job ID
        artifact_type: Artifact type
        include_content: Whether to include artifact content
        job_service: Job service

    Returns:
        Artifact details and optionally content
    """
    return await get_artifact_by_type(job_id, artifact_type, include_content, job_service)

@router.get(
    "/artifacts/{job_id}/{artifact_type}",
    response_model=ArtifactResponse,
    responses={
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def get_artifact_by_type(
    job_id: str = Path(..., description="Job ID"),
    artifact_type: ArtifactType = Path(..., description="Artifact type"),
    include_content: bool = Query(False, description="Whether to include artifact content"),
    job_service: JobService = Depends(get_job_service)
):
    """
    Get job artifact by type.

    Args:
        job_id: Job ID
        artifact_type: Artifact type
        include_content: Whether to include artifact content
        job_service: Job service

    Returns:
        Artifact details and optionally content
    """
    try:
        # Get job
        job = job_service.get_job(job_id)
        if not job:
            raise HTTPException(
                status_code=404,
                detail=f"Job not found: {job_id}"
            )

        # Get artifact
        artifact = job_service.get_artifact(job_id, artifact_type)
        if not artifact:
            raise HTTPException(
                status_code=404,
                detail=f"Artifact not found for job: {job_id}"
            )

        # Get artifact content if requested
        content = None
        if include_content:
            content = job_service.get_artifact_content(artifact)

        return ArtifactResponse(
            artifact=artifact,
            content=content
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get artifact: {str(e)}"
        )
