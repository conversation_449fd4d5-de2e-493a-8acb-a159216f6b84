"""
Configuration for the Repository Mapper Service.
"""

import os
from functools import lru_cache
from typing import Dict, Any, Optional

from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.config import Config

# Default configuration
DEFAULT_CONFIG = {
    "service_name": "repo-mapper-service",
    "host": "0.0.0.0",
    "port": 8000,
    "log_level": "INFO",
    "api_prefix": "/api/v1",
    "storage_type": "local",
    "storage_path": "./data",
    "max_concurrent_tasks": 10,
    "job_timeout_seconds": 3600,
    "additional_config": {
        "default_batch_size": 1000,
        "default_top_percentage": 0.2,
        "default_min_functions": 3,
        "default_max_functions": 7,
        "default_exclude_tests": True,
        "default_output_format": "json",
        "default_include_extensions": [".py", ".js", ".ts", ".java", ".rb"]
    }
}

@lru_cache
def get_config() -> Config:
    """
    Get service configuration.

    Returns:
        Service configuration
    """
    # Check if configuration file exists
    config_file = os.environ.get("CONFIG_FILE")
    if config_file and os.path.exists(config_file):
        return Config.from_file(config_file)

    # Check if environment variables exist
    if os.environ.get("BRACKET_SERVICE_NAME"):
        return Config.from_env(prefix="BRACKET_")

    # Use default configuration
    return Config(**DEFAULT_CONFIG)

def initialize_config() -> Config:
    """
    Initialize service configuration.

    Returns:
        Service configuration
    """
    config = get_config()

    # Override with environment variables
    if os.environ.get("PORT"):
        config.port = int(os.environ.get("PORT"))

    if os.environ.get("HOST"):
        config.host = os.environ.get("HOST")

    if os.environ.get("LOG_LEVEL"):
        config.log_level = os.environ.get("LOG_LEVEL")

    if os.environ.get("STORAGE_PATH"):
        config.storage_path = os.environ.get("STORAGE_PATH")

    return config
