"""
Repository mapper service for the Repository Mapper Service.
"""

import os
import sys
import time
import json
import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from contextlib import contextmanager
from json.decoder import JSONDecodeError
import importlib.util

# Initialize logger first
logger = logging.getLogger(__name__)

# Import the working CompleteRepoMap implementation directly from the file path
# This ensures we're using the exact file we want
try:
    # First try the standard import which should work in development
    from bracket_complete_repomap import CompleteRepoMap
    logger.info("Successfully imported CompleteRepoMap using standard import")
except (ImportError, AttributeError) as e:
    # If that fails, try to import it directly from the file path
    # This should work in the Docker container
    try:
        logger.info(f"Standard import failed: {e}. Trying direct file import...")

        # Get the absolute path to the file
        file_path = "/app/bracket_complete_repomap.py"
        if not os.path.exists(file_path):
            # Try a relative path if the absolute path doesn't exist
            file_path = os.path.join(os.path.dirname(__file__), "../../../bracket_complete_repomap.py")
            file_path = os.path.abspath(file_path)

        logger.info(f"Importing CompleteRepoMap from: {file_path}")

        spec = importlib.util.spec_from_file_location("bracket_complete_repomap", file_path)
        bracket_complete_repomap = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(bracket_complete_repomap)
        CompleteRepoMap = bracket_complete_repomap.CompleteRepoMap
        logger.info("Successfully imported CompleteRepoMap using direct file import")
    except Exception as e2:
        logger.error(f"Failed to import CompleteRepoMap: {e2}")
        raise ImportError(f"Could not import CompleteRepoMap: {e2}") from e2

from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.models import Job, JobStatus, Artifact, ArtifactType
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.storage import StorageClient
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.metrics import counter, gauge, histogram, timer

from src.services.job_service import JobService

# Metrics
job_counter = counter(
    name="repo_mapper_jobs_total",
    description="Total number of repository map jobs",
    labels={"service": "repo-mapper-service", "status": ""}
)

job_latency = histogram(
    name="repo_mapper_job_latency_seconds",
    description="Repository map job latency in seconds",
    buckets=[1, 5, 10, 30, 60, 120, 300, 600, 1800, 3600],
    labels={"service": "repo-mapper-service"}
)

active_jobs = gauge(
    name="repo_mapper_active_jobs",
    description="Number of active repository map jobs",
    labels={"service": "repo-mapper-service"}
)

file_counter = counter(
    name="repo_mapper_files_processed",
    description="Number of files processed",
    labels={"service": "repo-mapper-service"}
)

class RepositoryMapperService:
    """Service for generating repository maps."""

    def __init__(self, storage_client: StorageClient, max_concurrent_tasks: int = 10):
        """
        Initialize repository mapper service.

        Args:
            storage_client: Storage client
            max_concurrent_tasks: Maximum concurrent tasks
        """
        self.storage_client = storage_client
        self.max_concurrent_tasks = max_concurrent_tasks
        self.job_service = JobService(storage_client=storage_client)

    def _get_minimal_file_structure(self, repo_dir: str) -> Dict[str, Any]:
        """
        Create a minimal file structure for the repository.

        Args:
            repo_dir: Repository directory

        Returns:
            Dictionary with file paths as keys and empty dictionaries as values
        """
        file_structure = {}

        try:
            # Walk the repository directory and collect all files
            for root, _, files in os.walk(repo_dir):
                for file in files:
                    # Skip hidden files and directories
                    if file.startswith('.'):
                        continue

                    # Get the relative path from the repository root
                    full_path = os.path.join(root, file)
                    rel_path = os.path.relpath(full_path, repo_dir)

                    # Add the file to the structure with an empty dictionary as value
                    file_structure[rel_path] = {
                        "functions": {},
                        "imports": [],
                        "classes": {}
                    }
        except Exception as e:
            logger.error(f"Error creating minimal file structure: {e}")
            # Return at least one file to avoid empty structure
            file_structure["README.md"] = {
                "functions": {},
                "imports": [],
                "classes": {}
            }

        return file_structure

    async def generate_repository_map(
        self,
        job_id: str,
        repo_dir: str,
        batch_size: int = 1000,
        top_percentage: float = 0.2,
        min_functions: int = 3,
        max_functions: int = 7,
        exclude_tests: bool = True,
        output_format: str = "json",
        include_extensions: Optional[List[str]] = None
    ) -> None:
        """
        Generate repository map.

        Args:
            job_id: Job ID
            repo_dir: Repository directory
            batch_size: Batch size for processing
            top_percentage: Top percentage of functions to include
            min_functions: Minimum number of functions to include
            max_functions: Maximum number of functions to include
            exclude_tests: Whether to exclude test files
            output_format: Output format (json or yaml)
            include_extensions: File extensions to include
        """
        start_time = time.time()
        try:
            active_jobs.inc()
        except Exception as e:
            logger.warning(f"Error incrementing active_jobs metric: {e}")

        try:
            # Update job status
            self.job_service.update_job(
                job_id=job_id,
                status=JobStatus.RUNNING,
                message="Generating repository map"
            )

            # Set default include_extensions if not provided
            if include_extensions is None:
                include_extensions = [".py", ".js", ".ts", ".java", ".rb"]

            # Create repository map generator
            repo_map = CompleteRepoMap(
                root=repo_dir,
                verbose=True,
                top_percentage=top_percentage,
                min_functions=min_functions,
                max_functions=max_functions,
                exclude_tests=exclude_tests,
                output_format=output_format,
                include_extensions=include_extensions,
            )

            # Generate standardized artifact paths
            repomap_path = self.storage_client.get_artifact_path(job_id, "repomap", output_format)
            filtered_repomap_path = self.storage_client.get_artifact_path(job_id, "filtered_repomap", output_format)

            # Ensure the job directory exists
            job_artifacts_dir = f"artifacts/{job_id}"
            os.makedirs(self.storage_client._get_full_path(job_artifacts_dir), exist_ok=True)

            # Update job status
            self.job_service.update_job(
                job_id=job_id,
                progress=0.1,
                message="Generating complete repository map"
            )

            # Generate complete map
            try:
                # Make sure the output directory exists
                os.makedirs(os.path.dirname(self.storage_client._get_full_path(repomap_path)), exist_ok=True)

                # Call the generate_complete_map method with the correct parameters
                map_data = repo_map.generate_complete_map(
                    repo_dir,
                    batch_size=batch_size,
                    save_interval=True,
                    output_file=self.storage_client._get_full_path(repomap_path),
                    max_workers=None,  # Use default (CPU count)
                    mentioned_idents=None  # No specific identifiers to highlight
                )

                logger.info(f"Successfully generated repository map for {repo_dir}")
            except Exception as e:
                logger.error(f"Error generating complete map: {e}")
                # Create a minimal valid map structure
                map_data = {
                    "tree": json.dumps({
                        "files": self._get_minimal_file_structure(repo_dir),
                        "metadata": {
                            "generated_at": time.time(),
                            "repository": repo_dir,
                            "error": str(e)
                        }
                    }),
                    "filtered_tree": json.dumps({
                        "files": self._get_minimal_file_structure(repo_dir),
                        "metadata": {
                            "generated_at": time.time(),
                            "repository": repo_dir,
                            "error": str(e)
                        }
                    }),
                    "tags": [],
                    "filtered_tags": [],
                    "token_count": 0,
                    "filtered_token_count": 0
                }

            # Update metrics
            # map_data is a dictionary, not a list, so we need to get the number of files from it
            # Use a default value of 1 to avoid errors
            try:
                file_counter.inc(len(map_data.get("tags", [])) if isinstance(map_data, dict) else 1)
            except Exception as e:
                logger.warning(f"Error updating file_counter metric: {e}")

            # Update job status
            self.job_service.update_job(
                job_id=job_id,
                progress=0.7,
                message="Filtering repository map"
            )

            # Extract the full and filtered repository maps from map_data
            # map_data is a dictionary with keys like "tree", "filtered_tree", "tags", "filtered_tags", etc.

            # For the filtered repository map, we know it's working correctly
            # Get the filtered repository map first
            filtered_map = None
            if "filtered_tree" in map_data and map_data["filtered_tree"]:
                if output_format == "json":
                    try:
                        # If it's already a JSON string, parse it
                        filtered_tree_content = map_data["filtered_tree"]
                        # The filtered_tree_content is a JSON string that needs to be parsed
                        filtered_map = json.loads(filtered_tree_content)
                    except (json.JSONDecodeError, TypeError) as e:
                        logger.warning(f"Error parsing filtered_tree JSON: {e}")
                        # If it's not valid JSON, create a content wrapper
                        filtered_map = {"content": map_data["filtered_tree"]}
                else:
                    # For other formats, wrap in content
                    filtered_map = {"content": map_data["filtered_tree"]}

            # For the full repository map, use the same approach as the filtered map
            full_map = None
            if "tree" in map_data and map_data["tree"]:
                if output_format == "json":
                    try:
                        # If it's already a JSON string, parse it
                        tree_content = map_data["tree"]
                        # The tree_content is a JSON string that needs to be parsed
                        full_map = json.loads(tree_content)
                    except (json.JSONDecodeError, TypeError) as e:
                        logger.warning(f"Error parsing tree JSON: {e}")
                        # If it's not valid JSON, create a content wrapper
                        full_map = {"content": map_data["tree"]}
                else:
                    # For other formats, wrap in content
                    full_map = {"content": map_data["tree"]}

            # Ensure both maps are valid JSON objects
            if not filtered_map:
                # If filtered_map is empty, create a minimal valid structure
                logger.warning("Filtered repository map is empty. Creating a minimal valid structure.")
                filtered_map = {
                    "files": self._get_minimal_file_structure(repo_dir),
                    "metadata": {
                        "generated_at": time.time(),
                        "repository": repo_dir,
                        "warning": "Generated minimal map due to extraction issues"
                    }
                }

            if not full_map:
                # If full_map is empty, create a minimal valid structure
                logger.warning("Full repository map is empty. Creating a minimal valid structure.")
                full_map = {
                    "files": self._get_minimal_file_structure(repo_dir),
                    "metadata": {
                        "generated_at": time.time(),
                        "repository": repo_dir,
                        "warning": "Generated minimal map due to extraction issues"
                    }
                }

            # Save both maps
            if output_format == "json":
                self.storage_client.write_json(repomap_path, full_map)
                self.storage_client.write_json(filtered_repomap_path, filtered_map)
            else:
                self.storage_client.write_yaml(repomap_path, full_map)
                self.storage_client.write_yaml(filtered_repomap_path, filtered_map)

            # Create artifacts
            # Calculate file count for both artifacts
            file_count = 0
            try:
                # Try to count files from the filtered map
                if isinstance(filtered_map, dict):
                    # If filtered_map is already a parsed JSON object (not wrapped in content)
                    if "content" not in filtered_map:
                        file_count = len(filtered_map)
                    # If filtered_map has a content field that's a string
                    elif "content" in filtered_map and isinstance(filtered_map["content"], str):
                        try:
                            # Try to parse the content as JSON
                            content_json = json.loads(filtered_map["content"])
                            if isinstance(content_json, dict):
                                file_count = len(content_json)
                        except (json.JSONDecodeError, TypeError):
                            # If parsing fails, count lines as a fallback
                            lines = filtered_map["content"].split("\n")
                            file_count = len([line for line in lines if line.strip()])
            except Exception as e:
                logger.warning(f"Error calculating file count: {e}")
                # Use the number of tags as a fallback
                file_count = len(map_data.get("filtered_tags", [])) if isinstance(map_data, dict) else 1

            repomap_artifact = self.job_service.create_artifact(
                job_id=job_id,
                artifact_type=ArtifactType.REPOMAP,
                path=repomap_path,
                metadata={
                    "file_count": file_count,
                    "format": output_format
                }
            )

            filtered_repomap_artifact = self.job_service.create_artifact(
                job_id=job_id,
                artifact_type=ArtifactType.FILTERED_REPOMAP,
                path=filtered_repomap_path,
                metadata={
                    "file_count": file_count,
                    "format": output_format
                }
            )

            # Update job status
            self.job_service.update_job(
                job_id=job_id,
                status=JobStatus.COMPLETED,
                progress=1.0,
                message="Repository map generation completed",
                result={
                    "repomap_artifact_id": repomap_artifact.artifact_id,
                    "filtered_repomap_artifact_id": filtered_repomap_artifact.artifact_id
                }
            )

            # Update metrics
            try:
                # Check if labels method returns a callable
                if callable(job_counter.labels):
                    job_counter.labels(status="completed").inc()
                else:
                    # If it's not callable, log the error but don't fail
                    logger.warning("job_counter.labels is not callable, skipping metric update")
            except Exception as e:
                logger.warning(f"Error updating job_counter metric: {e}")

            try:
                job_latency.observe(time.time() - start_time)
            except Exception as e:
                logger.warning(f"Error updating job_latency metric: {e}")

        except Exception as e:
            logger.exception(f"Error generating repository map: {e}")

            # Update job status
            self.job_service.update_job(
                job_id=job_id,
                status=JobStatus.FAILED,
                error=str(e),
                message="Repository map generation failed"
            )

            # Update metrics
            try:
                # Check if labels method returns a callable
                if callable(job_counter.labels):
                    job_counter.labels(status="failed").inc()
                else:
                    # If it's not callable, log the error but don't fail
                    logger.warning("job_counter.labels is not callable, skipping metric update")
            except Exception as e:
                logger.warning(f"Error updating job_counter metric: {e}")

            try:
                job_latency.observe(time.time() - start_time)
            except Exception as e:
                logger.warning(f"Error updating job_latency metric: {e}")

        finally:
            # Decrement active jobs counter
            try:
                active_jobs.dec()
            except Exception as e:
                logger.warning(f"Error decrementing active_jobs metric: {e}")
