"""
Job service for the Repository Mapper Service.
"""

import os
import json
import time
import uuid
from typing import Dict, List, Any, Optional, Union

from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.models import Job, JobStatus, Artifact, ArtifactType
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.storage import StorageClient

class JobService:
    """Service for managing jobs and artifacts."""

    def __init__(self, storage_client: StorageClient):
        """
        Initialize job service.

        Args:
            storage_client: Storage client
        """
        self.storage_client = storage_client

        # Create necessary directories
        os.makedirs(os.path.join(self.storage_client._get_full_path("jobs")), exist_ok=True)
        os.makedirs(os.path.join(self.storage_client._get_full_path("artifacts")), exist_ok=True)

    def create_job(self) -> Job:
        """
        Create a new job.

        Returns:
            Created job
        """
        job = Job(
            job_id=str(uuid.uuid4()),
            status=JobStatus.PENDING,
            created_at=time.time(),
            updated_at=time.time(),
            progress=0.0,
            message="Job created"
        )

        self._save_job(job)

        return job

    def update_job(self, job_id: str, **kwargs) -> Optional[Job]:
        """
        Update job.

        Args:
            job_id: Job ID
            **kwargs: Fields to update

        Returns:
            Updated job or None if job not found
        """
        job = self.get_job(job_id)
        if not job:
            return None

        # Update fields
        for key, value in kwargs.items():
            if hasattr(job, key):
                setattr(job, key, value)

        # Update timestamp
        job.updated_at = time.time()

        self._save_job(job)

        return job

    def get_job(self, job_id: str) -> Optional[Job]:
        """
        Get job by ID.

        Args:
            job_id: Job ID

        Returns:
            Job or None if not found
        """
        job_path = f"jobs/{job_id}.json"

        if not self.storage_client.file_exists(job_path):
            return None

        try:
            job_data = self.storage_client.read_json(job_path)
            return Job(**job_data)
        except Exception:
            return None

    def list_jobs(self) -> List[Job]:
        """
        List all jobs.

        Returns:
            List of jobs
        """
        jobs_dir = "jobs"

        if not self.storage_client.file_exists(jobs_dir):
            return []

        job_files = self.storage_client.list_files(jobs_dir)
        jobs = []

        for job_file in job_files:
            if job_file.endswith(".json"):
                try:
                    job_data = self.storage_client.read_json(job_file)
                    jobs.append(Job(**job_data))
                except Exception:
                    continue

        return jobs

    def _save_job(self, job: Job) -> None:
        """
        Save job to storage.

        Args:
            job: Job to save
        """
        job_path = f"jobs/{job.job_id}.json"
        self.storage_client.write_json(job_path, job.model_dump())

    def create_artifact(self, job_id: str, artifact_type: ArtifactType, path: str,
                        metadata: Optional[Dict[str, Any]] = None) -> Artifact:
        """
        Create artifact.

        Args:
            job_id: Job ID
            artifact_type: Artifact type
            path: Path to artifact
            metadata: Artifact metadata

        Returns:
            Created artifact
        """
        artifact = Artifact(
            artifact_id=str(uuid.uuid4()),
            job_id=job_id,
            artifact_type=artifact_type,
            created_at=time.time(),
            path=path,
            metadata=metadata or {}
        )

        self._save_artifact(artifact)

        return artifact

    def get_artifact(self, job_id: str, artifact_type: Optional[ArtifactType] = None) -> Optional[Artifact]:
        """
        Get artifact by job ID and type.

        Args:
            job_id: Job ID
            artifact_type: Artifact type (if None, returns any artifact for job)

        Returns:
            Artifact or None if not found
        """
        artifacts_dir = f"artifacts/{job_id}"

        if not self.storage_client.file_exists(artifacts_dir):
            return None

        artifact_files = self.storage_client.list_files(artifacts_dir)

        # First try to find artifact using the standardized naming convention
        if artifact_type is not None:
            # Check if the artifact exists with the standardized path
            artifact_path = self.storage_client.get_artifact_path(job_id, artifact_type.value)
            if self.storage_client.file_exists(artifact_path):
                # If the artifact content exists, we need to find its metadata
                for artifact_file in artifact_files:
                    if artifact_file.endswith(".json") and "artifact_" in artifact_file:
                        try:
                            artifact_data = self.storage_client.read_json(artifact_file)
                            artifact = Artifact(**artifact_data)
                            if artifact.artifact_type == artifact_type and artifact.path == artifact_path:
                                return artifact
                        except Exception:
                            continue

        # Fallback to searching through all artifacts
        for artifact_file in artifact_files:
            if artifact_file.endswith(".json") and "artifact_" in artifact_file:
                try:
                    artifact_data = self.storage_client.read_json(artifact_file)
                    artifact = Artifact(**artifact_data)

                    if artifact_type is None or artifact.artifact_type == artifact_type:
                        return artifact
                except Exception:
                    continue

        return None

    def get_artifact_content(self, artifact: Artifact) -> Optional[Dict[str, Any]]:
        """
        Get artifact content.

        Args:
            artifact: Artifact

        Returns:
            Artifact content or None if not found
        """
        if not self.storage_client.file_exists(artifact.path):
            return None

        try:
            if artifact.path.endswith(".json"):
                content = self.storage_client.read_json(artifact.path)
                # Ensure content is a dictionary
                if isinstance(content, dict):
                    return content
                else:
                    # Convert string or other types to a dictionary
                    return {"content": content}
            elif artifact.path.endswith(".yaml") or artifact.path.endswith(".yml"):
                content = self.storage_client.read_yaml(artifact.path)
                # Ensure content is a dictionary
                if isinstance(content, dict):
                    return content
                else:
                    # Convert string or other types to a dictionary
                    return {"content": content}
            else:
                return {"content": self.storage_client.read_text(artifact.path)}
        except Exception as e:
            # Return a default dictionary with error information
            return {"error": str(e), "content": {}}

    def _save_artifact(self, artifact: Artifact) -> None:
        """
        Save artifact to storage.

        Args:
            artifact: Artifact to save
        """
        # Use standardized path for artifact metadata
        artifact_path = f"artifacts/{artifact.job_id}/artifact_{artifact.artifact_id}.json"
        self.storage_client.write_json(artifact_path, artifact.model_dump())
