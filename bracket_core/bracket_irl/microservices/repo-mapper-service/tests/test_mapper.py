"""
Unit tests for the Repository Mapper Service.
"""

import os
import sys
import json
import asyncio
import pytest
from unittest.mock import MagicMock, patch, AsyncMock
from pathlib import Path

# Import service modules
from src.services.mapper import RepositoryMapperService
from src.services.job_service import JobService

# Import common modules
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.models import Job, JobStatus, Artifact, ArtifactType
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.storage import StorageClient

@pytest.fixture
def storage_client():
    """Create mock storage client."""
    client = MagicMock(spec=StorageClient)
    client._get_full_path.return_value = "/tmp/test"
    return client

@pytest.fixture
def job_service():
    """Create mock job service."""
    service = MagicMock(spec=JobService)
    service.update_job.return_value = None
    service.create_artifact.return_value = Artifact(
        artifact_id="test-artifact-id",
        job_id="test-job-id",
        artifact_type=ArtifactType.REPOMAP,
        path="test-path",
        metadata={}
    )
    return service

@pytest.fixture
def mapper_service(storage_client, job_service):
    """Create repository mapper service with mock dependencies."""
    service = RepositoryMapperService(
        storage_client=storage_client,
        max_concurrent_tasks=10
    )
    service.job_service = job_service
    return service

@pytest.mark.asyncio
@patch("src.services.mapper.CompleteRepoMap")
async def test_generate_repository_map_success(mock_complete_repomap, mapper_service, job_service):
    """Test successful repository map generation."""
    # Mock CompleteRepoMap
    mock_repomap_instance = MagicMock()
    mock_repomap_instance.generate_complete_map.return_value = {"file1.py": "content1", "file2.py": "content2"}
    mock_repomap_instance.filter_map.return_value = {"file1.py": "filtered_content1"}
    mock_complete_repomap.return_value = mock_repomap_instance

    # Call generate_repository_map
    await mapper_service.generate_repository_map(
        job_id="test-job-id",
        repo_dir="/tmp/test-repo",
        batch_size=100,
        top_percentage=0.2,
        min_functions=3,
        max_functions=7,
        exclude_tests=True,
        output_format="json",
        include_extensions=[".py"]
    )

    # Assert CompleteRepoMap was created with correct parameters
    mock_complete_repomap.assert_called_once_with(
        root="/tmp/test-repo",
        verbose=True,
        top_percentage=0.2,
        min_functions=3,
        max_functions=7,
        exclude_tests=True,
        output_format="json",
        include_extensions=[".py"],
    )

    # Assert generate_complete_map was called with correct parameters
    mock_repomap_instance.generate_complete_map.assert_called_once_with(
        "/tmp/test-repo",
        batch_size=100,
        save_interval=True,
        output_file="/tmp/test",
    )

    # Assert filter_map was called with correct parameters
    mock_repomap_instance.filter_map.assert_called_once_with(
        {"file1.py": "content1", "file2.py": "content2"}
    )

    # Assert job status was updated correctly
    job_service.update_job.assert_any_call(
        job_id="test-job-id",
        status=JobStatus.RUNNING,
        message="Generating repository map"
    )

    job_service.update_job.assert_any_call(
        job_id="test-job-id",
        progress=0.1,
        message="Generating complete repository map"
    )

    job_service.update_job.assert_any_call(
        job_id="test-job-id",
        progress=0.7,
        message="Filtering repository map"
    )

    job_service.update_job.assert_any_call(
        job_id="test-job-id",
        status=JobStatus.COMPLETED,
        progress=1.0,
        message="Repository map generation completed",
        result={
            "repomap_artifact_id": "test-artifact-id",
            "filtered_repomap_artifact_id": "test-artifact-id"
        }
    )

    # Assert artifacts were created
    job_service.create_artifact.assert_any_call(
        job_id="test-job-id",
        artifact_type=ArtifactType.REPOMAP,
        path="artifacts/test-job-id/repomap/repomap.json",
        metadata={
            "file_count": 2,
            "format": "json"
        }
    )

    job_service.create_artifact.assert_any_call(
        job_id="test-job-id",
        artifact_type=ArtifactType.FILTERED_REPOMAP,
        path="artifacts/test-job-id/repomap/repomap_filtered.json",
        metadata={
            "file_count": 1,
            "format": "json"
        }
    )

@pytest.mark.asyncio
@patch("src.services.mapper.CompleteRepoMap")
async def test_generate_repository_map_failure(mock_complete_repomap, mapper_service, job_service):
    """Test repository map generation failure."""
    # Mock CompleteRepoMap to raise an exception
    mock_repomap_instance = MagicMock()
    mock_repomap_instance.generate_complete_map.side_effect = Exception("Test error")
    mock_complete_repomap.return_value = mock_repomap_instance

    # Call generate_repository_map
    await mapper_service.generate_repository_map(
        job_id="test-job-id",
        repo_dir="/tmp/test-repo"
    )

    # Assert job status was updated correctly
    job_service.update_job.assert_any_call(
        job_id="test-job-id",
        status=JobStatus.RUNNING,
        message="Generating repository map"
    )

    job_service.update_job.assert_any_call(
        job_id="test-job-id",
        status=JobStatus.FAILED,
        error="Test error",
        message="Repository map generation failed"
    )
