"""
API tests for the Repository Mapper Service.
"""

import os
import json
import pytest
from unittest.mock import MagicMock, patch, AsyncMock
from fastapi.testclient import TestClient
from fastapi import FastAPI

from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.models import Job, JobStatus, Artifact, ArtifactType
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.storage import StorageClient

from src.main import app
from src.services.job_service import JobService
from src.services.mapper import RepositoryMapperService

# Create test client
client = TestClient(app)

@pytest.fixture
def mock_job_service():
    """Mock job service."""
    with patch("src.api.routes.get_job_service") as mock_get_job_service:
        job_service = MagicMock(spec=JobService)
        job_service.create_job.return_value = Job(
            job_id="test-job-id",
            status=JobStatus.PENDING
        )
        job_service.get_job.return_value = Job(
            job_id="test-job-id",
            status=JobStatus.COMPLETED,
            progress=1.0,
            message="Repository map generation completed",
            result={
                "repomap_artifact_id": "test-repomap-id",
                "filtered_repomap_artifact_id": "test-filtered-repomap-id"
            }
        )
        job_service.get_artifact.return_value = Artifact(
            artifact_id="test-artifact-id",
            job_id="test-job-id",
            artifact_type=ArtifactType.REPOMAP,
            path="test-path",
            metadata={
                "file_count": 10,
                "format": "json"
            }
        )
        job_service.get_artifact_content.return_value = {"file1.py": "content1"}
        mock_get_job_service.return_value = job_service
        yield job_service

@pytest.fixture
def mock_mapper_service():
    """Mock mapper service."""
    with patch("src.api.routes.get_mapper_service") as mock_get_mapper_service:
        mapper_service = MagicMock(spec=RepositoryMapperService)
        mapper_service.generate_repository_map = AsyncMock()
        mock_get_mapper_service.return_value = mapper_service
        yield mapper_service

@pytest.fixture
def mock_os_path_exists():
    """Mock os.path.exists."""
    with patch("os.path.exists") as mock_exists:
        mock_exists.return_value = True
        yield mock_exists

def test_health_check():
    """Test health check endpoint."""
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json()["status"] == "ok"

def test_detailed_health_check():
    """Test detailed health check endpoint."""
    response = client.get("/health/details")
    assert response.status_code == 200
    assert "status" in response.json()
    assert "checks" in response.json()

def test_generate_repository_map(mock_job_service, mock_mapper_service, mock_os_path_exists):
    """Test generate repository map endpoint."""
    # Create request data
    request_data = {
        "repo_dir": "/tmp/test-repo",
        "batch_size": 100,
        "top_percentage": 0.2,
        "min_functions": 3,
        "max_functions": 7,
        "exclude_tests": True,
        "output_format": "json",
        "include_extensions": [".py"]
    }

    # Send request
    response = client.post("/api/v1/generate", json=request_data)

    # Assert response
    assert response.status_code == 200
    assert response.json()["job_id"] == "test-job-id"
    assert response.json()["message"] == "Repository map generation started"

    # Assert job service was called
    mock_job_service.create_job.assert_called_once()

    # Assert mapper service was called with background task
    # Note: We can't directly assert on background tasks, but we can check that the service was set up correctly

def test_get_job_status(mock_job_service):
    """Test get job status endpoint."""
    # Send request
    response = client.get("/api/v1/status/test-job-id")

    # Assert response
    assert response.status_code == 200
    assert response.json()["job"]["job_id"] == "test-job-id"
    assert response.json()["job"]["status"] == "completed"

    # Assert job service was called
    mock_job_service.get_job.assert_called_once_with("test-job-id")

def test_get_job_status_not_found(mock_job_service):
    """Test get job status endpoint with non-existent job."""
    # Mock job service to return None
    mock_job_service.get_job.return_value = None

    # Send request
    response = client.get("/api/v1/status/non-existent-job")

    # Assert response
    assert response.status_code == 404
    assert "detail" in response.json()

    # Assert job service was called
    mock_job_service.get_job.assert_called_once_with("non-existent-job")

def test_get_artifact(mock_job_service):
    """Test get artifact endpoint."""
    # Send request
    response = client.get("/api/v1/artifacts/test-job-id?artifact_type=repomap&include_content=true")

    # Assert response
    assert response.status_code == 200
    assert response.json()["artifact"]["artifact_id"] == "test-artifact-id"
    assert response.json()["artifact"]["job_id"] == "test-job-id"
    assert response.json()["content"] == {"file1.py": "content1"}

    # Assert job service was called
    mock_job_service.get_job.assert_called_once_with("test-job-id")
    mock_job_service.get_artifact.assert_called_once_with("test-job-id", ArtifactType.REPOMAP)
    mock_job_service.get_artifact_content.assert_called_once()

def test_get_artifact_not_found(mock_job_service):
    """Test get artifact endpoint with non-existent artifact."""
    # Mock job service to return None for artifact
    mock_job_service.get_artifact.return_value = None

    # Send request
    response = client.get("/api/v1/artifacts/test-job-id?artifact_type=repomap")

    # Assert response
    assert response.status_code == 404
    assert "detail" in response.json()

    # Assert job service was called
    mock_job_service.get_job.assert_called_once_with("test-job-id")
    mock_job_service.get_artifact.assert_called_once_with("test-job-id", ArtifactType.REPOMAP)
