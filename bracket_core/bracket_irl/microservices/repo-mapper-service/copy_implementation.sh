#!/bin/bash
# Create symbolic links to the actual implementation files

# Create the directory structure
mkdir -p bracket_irl/queries

# Instead of creating a symbolic link, make a copy of the original file
# This avoids Docker build issues with symbolic links
# Use -L to follow the symbolic link and copy the actual file
rm -f ./bracket_complete_repomap.py
cp -L ../../bracket_complete_repomap.py ./bracket_complete_repomap.py

# Create symbolic links to the queries directory if it exists
if [ -d "../../queries" ]; then
  mkdir -p bracket_irl/queries
  cp -r ../../queries/* bracket_irl/queries/
else
  echo "Warning: queries directory not found at ../../queries"
  # Create a minimal queries directory with essential files
  mkdir -p bracket_irl/queries/tree-sitter-language-pack

  # Create a minimal Python tags file
  cat > bracket_irl/queries/tree-sitter-language-pack/python-tags.scm << EOF
; Python tags query

; Function definitions
(function_definition
  name: (identifier) @name.definition.function) @definition.function

; Class definitions
(class_definition
  name: (identifier) @name.definition.class) @definition.class

; Method definitions
(function_definition
  name: (identifier) @name.definition.method
  body: (block
    (expression_statement
      (string) @doc.comment)?)) @definition.method
EOF
fi

# Create a symbolic link in the Docker container's expected location
mkdir -p /app/bracket_irl 2>/dev/null || true
ln -sf $(pwd)/bracket_complete_repomap.py /app/bracket_irl/bracket_complete_repomap.py 2>/dev/null || true
mkdir -p /app/bracket_irl/queries 2>/dev/null || true
ln -sf $(pwd)/bracket_irl/queries /app/bracket_irl/queries 2>/dev/null || true

echo "Implementation files copied successfully."
