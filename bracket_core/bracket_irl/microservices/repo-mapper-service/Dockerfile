FROM bracket-irl/bracket_irl_common:latest

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install additional dependencies required by bracket_complete_repomap.py
RUN pip install --no-cache-dir networkx tqdm pygments tree_sitter_languages grep_ast

# Create the llm module directory and add necessary files
RUN mkdir -p /app/llm
RUN echo '"""LLM client module."""' > /app/llm/__init__.py
RUN echo '"""LLM client factory."""\n\ndef get_openrouter_client(*args, **kwargs):\n    return {}\n\ndef get_claude_client(*args, **kwargs):\n    return {}\n' > /app/llm/get_client.py
RUN echo '"""API key management."""\n\nimport os\n\ndef get_openai_api_key(provided_key=None):\n    return provided_key or os.environ.get("OPENAI_API_KEY", "********************************************************************************************************************************************************************")\n\ndef get_openrouter_api_key(provided_key=None):\n    return provided_key or os.environ.get("OPENROUTER_API_KEY", "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")\n\ndef get_anthropic_api_key(provided_key=None):\n    return provided_key or os.environ.get("ANTHROPIC_API_KEY", "")\n' > /app/llm/api_keys.py

# Copy application code
COPY src/ /app/src/

# Copy the implementation file
COPY bracket_complete_repomap.py /app/bracket_complete_repomap.py

# Create necessary directories
RUN mkdir -p /app/bracket_irl/queries/tree-sitter-language-pack

# Copy queries directory - make sure it exists first
COPY bracket_irl/queries/tree-sitter-language-pack/python-tags.scm /app/bracket_irl/queries/tree-sitter-language-pack/

# Make sure the queries directory is accessible
RUN chmod -R 755 /app/bracket_irl/queries/

# Create directory structure for imports
RUN mkdir -p /app/bracket_irl/microservices

# Create symbolic links to make imports work
RUN ln -s /app/bracket_irl_common /app/bracket_irl/microservices/bracket_irl_common

# Set PYTHONPATH to include both the app directory and the parent directory
# Make sure the current directory is in the path so bracket_complete_repomap.py can be imported directly
ENV PYTHONPATH=/app:/:/app/src

# Set environment variables
ENV SERVICE_NAME=repo-mapper-service
ENV HOST=0.0.0.0
ENV PORT=8001

# Expose port
EXPOSE 8001

# Create data directory
RUN mkdir -p /app/data/jobs /app/data/artifacts

# Run the application
CMD ["uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8001"]
