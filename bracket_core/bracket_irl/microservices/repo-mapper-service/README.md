# Repository Mapper Service

This microservice is responsible for generating repository maps from codebases. It extracts functions, classes, and other code elements from files and creates a structured representation of the codebase.

## Features

- Generate complete repository maps
- Filter repository maps based on importance
- Process files in batches for improved performance
- Support for multiple programming languages
- REST API for integration with other services
- Background job processing with status tracking
- Prometheus metrics for monitoring
- Detailed health checks

## API Endpoints

- `POST /api/v1/generate`: Generate repository map
- `GET /api/v1/status/{job_id}`: Get job status
- `GET /api/v1/artifacts/{job_id}`: Get job artifacts
- `GET /api/v1/health`: Basic health check
- `GET /api/v1/health/details`: Detailed health check
- `GET /metrics`: Prometheus metrics

## Configuration

Configuration can be provided via environment variables or a configuration file:

```yaml
service_name: repo-mapper-service
host: 0.0.0.0
port: 8001
log_level: INFO
storage_type: local
storage_path: ./data
max_concurrent_tasks: 10
additional_config:
  default_batch_size: 1000
  default_top_percentage: 0.2
  default_min_functions: 3
  default_max_functions: 7
  default_exclude_tests: true
  default_output_format: json
  default_include_extensions:
    - .py
    - .js
    - .ts
    - .java
    - .rb
```

## Environment Variables

- `SERVICE_NAME`: Name of the service (default: repo-mapper-service)
- `HOST`: Host to bind the service to (default: 0.0.0.0)
- `PORT`: Port to bind the service to (default: 8001)
- `LOG_LEVEL`: Logging level (default: INFO)
- `STORAGE_TYPE`: Storage type (default: local)
- `STORAGE_PATH`: Path for local storage (default: ./data)
- `MAX_CONCURRENT_TASKS`: Maximum concurrent tasks (default: 10)

## Usage

### Docker

```bash
docker build -t repo-mapper-service .
docker run -p 8001:8001 \
  -v /path/to/repo:/repo \
  -v /path/to/data:/app/data \
  -e SERVICE_NAME=repo-mapper-service \
  -e HOST=0.0.0.0 \
  -e PORT=8001 \
  -e LOG_LEVEL=INFO \
  -e STORAGE_TYPE=local \
  -e STORAGE_PATH=/app/data \
  repo-mapper-service
```

### Docker Compose

```yaml
services:
  repo-mapper-service:
    build:
      context: ./repo-mapper-service
    image: repo-mapper-service:latest
    ports:
      - "8001:8001"
    environment:
      - SERVICE_NAME=repo-mapper-service
      - HOST=0.0.0.0
      - PORT=8001
      - LOG_LEVEL=INFO
      - STORAGE_TYPE=local
      - STORAGE_PATH=/app/data
    volumes:
      - ./data:/app/data
      - ${REPO_DIR:-/tmp/repo}:/repo
```

### Local Development

```bash
# Install dependencies
pip install -r requirements.txt

# Install bracket-irl-common
pip install -e ../bracket-irl-common

# Run the service
uvicorn src.main:app --host 0.0.0.0 --port 8001 --reload
```

## Testing

```bash
# Install test dependencies
pip install pytest pytest-asyncio pytest-cov httpx

# Run tests
pytest

# Run tests with coverage
pytest --cov=src tests/
```

## API Examples

### Generate Repository Map

```bash
curl -X POST http://localhost:8001/api/v1/generate \
  -H "Content-Type: application/json" \
  -d '{
    "repo_dir": "/repo",
    "batch_size": 1000,
    "top_percentage": 0.2,
    "min_functions": 3,
    "max_functions": 7,
    "exclude_tests": true,
    "output_format": "json",
    "include_extensions": [".py", ".js", ".ts", ".java", ".rb"]
  }'
```

### Get Job Status

```bash
curl -X GET http://localhost:8001/api/v1/status/{job_id}
```

### Get Job Artifacts

```bash
curl -X GET http://localhost:8001/api/v1/artifacts/{job_id}?artifact_type=repomap&include_content=true
```
