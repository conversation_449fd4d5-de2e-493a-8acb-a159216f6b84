; Python tags query

; Function definitions
(function_definition
  name: (identifier) @name.definition.function) @definition.function

; Class definitions
(class_definition
  name: (identifier) @name.definition.class) @definition.class

; Method definitions
(function_definition
  name: (identifier) @name.definition.method
  body: (block
    (expression_statement
      (string) @doc.comment)?)) @definition.method
