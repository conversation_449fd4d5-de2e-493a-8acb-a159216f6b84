version: '3.8'

services:
  # Monitoring and Observability
  prometheus:
    image: prom/prometheus:v2.45.0
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
    networks:
      - bracket-network
    restart: unless-stopped

  grafana:
    image: grafana/grafana:10.0.3
    ports:
      - "3000:3000"
    volumes:
      - ./grafana/provisioning:/etc/grafana/provisioning
      - grafana-data:/var/lib/grafana
    networks:
      - bracket-network
    depends_on:
      - prometheus
    restart: unless-stopped

  # Common library
  bracket_irl_common:
    build:
      context: ./bracket_irl_common
    image: bracket-irl/bracket_irl_common:latest
    networks:
      - bracket-network

  # Repository Mapper Service
  repo-mapper-service:
    build:
      context: ./repo-mapper-service
    image: bracket-irl/repo-mapper-service:latest
    ports:
      - "8001:8001"
    environment:
      - SERVICE_NAME=repo-mapper-service
      - HOST=0.0.0.0
      - PORT=8001
      - LOG_LEVEL=INFO
      - STORAGE_TYPE=local
      - STORAGE_PATH=/app/data
      - MAX_CONCURRENT_TASKS=10
      - PROMETHEUS_PUSH_GATEWAY=http://prometheus:9091
    volumes:
      - bracket-data:/app/data
      - ${REPO_DIR:-/tmp/repo}:/repo
    networks:
      - bracket-network
    depends_on:
      - bracket_irl_common
      - prometheus
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # Domain Analyzer Service
  domain-analyzer-service:
    build:
      context: ./domain-analyzer-service
    image: bracket-irl/domain-analyzer-service:latest
    ports:
      - "8002:8002"
    environment:
      - SERVICE_NAME=domain-analyzer-service
      - HOST=0.0.0.0
      - PORT=8002
      - LOG_LEVEL=INFO
      - STORAGE_TYPE=local
      - STORAGE_PATH=/app/data
      - MAX_CONCURRENT_TASKS=10
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - PROMETHEUS_PUSH_GATEWAY=http://prometheus:9091
    volumes:
      - bracket-data:/app/data
    networks:
      - bracket-network
    depends_on:
      - bracket_irl_common
      - prometheus
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # File-Domain Mapper Service
  file-domain-mapper-service:
    build:
      context: ./file-domain-mapper-service
    image: bracket-irl/file-domain-mapper-service:latest
    ports:
      - "8003:8003"
    environment:
      - SERVICE_NAME=file-domain-mapper-service
      - HOST=0.0.0.0
      - PORT=8003
      - LOG_LEVEL=INFO
      - STORAGE_TYPE=local
      - STORAGE_PATH=/app/data
      - MAX_CONCURRENT_TASKS=10
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - PROMETHEUS_PUSH_GATEWAY=http://prometheus:9091
    volumes:
      - bracket-data:/app/data
    networks:
      - bracket-network
    depends_on:
      - bracket_irl_common
      - prometheus
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8003/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # Domain-File Repomap Service
  domain-file-repomap-service:
    build:
      context: ./domain-file-repomap-service
    image: bracket-irl/domain-file-repomap-service:latest
    ports:
      - "8004:8004"
    environment:
      - SERVICE_NAME=domain-file-repomap-service
      - HOST=0.0.0.0
      - PORT=8004
      - LOG_LEVEL=INFO
      - STORAGE_TYPE=local
      - STORAGE_PATH=/app/data
      - MAX_CONCURRENT_TASKS=10
      - PROMETHEUS_PUSH_GATEWAY=http://prometheus:9091
    volumes:
      - bracket-data:/app/data
    networks:
      - bracket-network
    depends_on:
      - bracket_irl_common
      - prometheus
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8004/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # Diagram Generator Service
  diagram-generator-service:
    build:
      context: ./diagram-generator-service
    image: bracket-irl/diagram-generator-service:latest
    ports:
      - "8005:8005"
    environment:
      - SERVICE_NAME=diagram-generator-service
      - HOST=0.0.0.0
      - PORT=8005
      - LOG_LEVEL=INFO
      - STORAGE_TYPE=local
      - STORAGE_PATH=/app/data
      - MAX_CONCURRENT_TASKS=10
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - PROMETHEUS_PUSH_GATEWAY=http://prometheus:9091
    volumes:
      - bracket-data:/app/data
    networks:
      - bracket-network
    depends_on:
      - bracket_irl_common
      - prometheus
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8005/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # Orchestrator Service
  orchestrator-service:
    build:
      context: ./orchestrator-service
    image: bracket-irl/orchestrator-service:latest
    ports:
      - "8000:8000"
    environment:
      - SERVICE_NAME=orchestrator-service
      - HOST=0.0.0.0
      - PORT=8000
      - LOG_LEVEL=INFO
      - STORAGE_TYPE=local
      - STORAGE_PATH=/app/data
      - MAX_CONCURRENT_TASKS=10
      - REPO_MAPPER_URL=http://repo-mapper-service:8001
      - DOMAIN_ANALYZER_URL=http://domain-analyzer-service:8002
      - FILE_DOMAIN_MAPPER_URL=http://file-domain-mapper-service:8003
      - DOMAIN_FILE_REPOMAP_URL=http://domain-file-repomap-service:8004
      - DIAGRAM_GENERATOR_URL=http://diagram-generator-service:8005
      - PROMETHEUS_PUSH_GATEWAY=http://prometheus:9091
    volumes:
      - bracket-data:/app/data
      - ${REPO_DIR:-/tmp/repo}:/repo
    networks:
      - bracket-network
    depends_on:
      - repo-mapper-service
      - domain-analyzer-service
      - file-domain-mapper-service
      - domain-file-repomap-service
      - diagram-generator-service
      - prometheus
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

networks:
  bracket-network:
    driver: bridge

volumes:
  bracket-data:
  grafana-data:
