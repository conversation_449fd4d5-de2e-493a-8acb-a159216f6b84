"""
API tests for the Domain Analyzer Service.
"""

import os
import json
import pytest
from unittest.mock import MagicMock, patch, AsyncMock
from fastapi.testclient import TestClient
from fastapi import FastAPI

from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.models import Job, JobStatus, Artifact, ArtifactType
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.storage import StorageClient

from src.main import app
from src.services.job_service import JobService
from src.services.analyzer import DomainAnalyzerService

# Create test client
client = TestClient(app)

@pytest.fixture
def mock_job_service():
    """Mock job service."""
    with patch("src.api.routes.get_job_service") as mock_get_job_service:
        job_service = MagicMock(spec=JobService)
        job_service.create_job.return_value = Job(
            job_id="test-job-id",
            status=JobStatus.PENDING
        )
        job_service.get_job.return_value = Job(
            job_id="test-job-id",
            status=JobStatus.COMPLETED,
            progress=1.0,
            message="Domain analysis completed",
            result={
                "domain_analysis_artifact_id": "test-domain-analysis-id"
            }
        )
        job_service.get_artifact.return_value = Artifact(
            artifact_id="test-artifact-id",
            job_id="test-job-id",
            artifact_type=ArtifactType.DOMAIN_ANALYSIS,
            path="test-path",
            metadata={
                "model": "gpt-4o-mini",
                "use_openrouter": False,
                "use_claude": False,
                "generate_explanations": True
            }
        )
        job_service.get_artifact_content.return_value = {"domains": ["domain1", "domain2"]}
        mock_get_job_service.return_value = job_service
        yield job_service

@pytest.fixture
def mock_analyzer_service():
    """Mock analyzer service."""
    with patch("src.api.routes.get_analyzer_service") as mock_get_analyzer_service:
        analyzer_service = MagicMock(spec=DomainAnalyzerService)
        analyzer_service.analyze_domains = AsyncMock()
        mock_get_analyzer_service.return_value = analyzer_service
        yield analyzer_service

def test_health_check():
    """Test health check endpoint."""
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json()["status"] == "ok"

def test_detailed_health_check():
    """Test detailed health check endpoint."""
    response = client.get("/health/details")
    assert response.status_code == 200
    assert "status" in response.json()
    assert "checks" in response.json()

def test_analyze_domains(mock_job_service, mock_analyzer_service):
    """Test analyze domains endpoint."""
    # Create request data
    request_data = {
        "repomap_path": "artifacts/test-job-id/repomap/repomap.json",
        "api_key": "test-api-key",
        "model": "gpt-4o-mini",
        "max_requests_per_minute": 5000,
        "max_tokens_per_minute": 15000000,
        "use_openrouter": False,
        "use_claude": False,
        "openrouter_base_url": "https://openrouter.ai/api/v1",
        "claude_model": "claude-3-7-sonnet-20250219",
        "max_tokens_per_chunk": 500000,
        "disable_parallel": False,
        "max_concurrent_tasks": 10,
        "generate_explanations": True
    }

    # Send request
    response = client.post("/api/v1/analyze", json=request_data)

    # Assert response
    assert response.status_code == 200
    assert response.json()["job_id"] == "test-job-id"
    assert response.json()["message"] == "Domain analysis started"

    # Assert job service was called
    mock_job_service.create_job.assert_called_once()

    # Assert analyzer service was called with background task
    # Note: We can't directly assert on background tasks, but we can check that the service was set up correctly

def test_get_job_status(mock_job_service):
    """Test get job status endpoint."""
    # Send request
    response = client.get("/api/v1/status/test-job-id")

    # Assert response
    assert response.status_code == 200
    assert response.json()["job"]["job_id"] == "test-job-id"
    assert response.json()["job"]["status"] == "completed"

    # Assert job service was called
    mock_job_service.get_job.assert_called_once_with("test-job-id")

def test_get_job_status_not_found(mock_job_service):
    """Test get job status endpoint with non-existent job."""
    # Mock job service to return None
    mock_job_service.get_job.return_value = None

    # Send request
    response = client.get("/api/v1/status/non-existent-job")

    # Assert response
    assert response.status_code == 404
    assert "detail" in response.json()

    # Assert job service was called
    mock_job_service.get_job.assert_called_once_with("non-existent-job")

def test_get_artifact(mock_job_service):
    """Test get artifact endpoint."""
    # Send request
    response = client.get("/api/v1/artifacts/test-job-id?artifact_type=domain_analysis&include_content=true")

    # Assert response
    assert response.status_code == 200
    assert response.json()["artifact"]["artifact_id"] == "test-artifact-id"
    assert response.json()["artifact"]["job_id"] == "test-job-id"
    assert response.json()["content"] == {"domains": ["domain1", "domain2"]}

    # Assert job service was called
    mock_job_service.get_job.assert_called_once_with("test-job-id")
    mock_job_service.get_artifact.assert_called_once_with("test-job-id", ArtifactType.DOMAIN_ANALYSIS)
    mock_job_service.get_artifact_content.assert_called_once()

def test_get_artifact_not_found(mock_job_service):
    """Test get artifact endpoint with non-existent artifact."""
    # Mock job service to return None for artifact
    mock_job_service.get_artifact.return_value = None

    # Send request
    response = client.get("/api/v1/artifacts/test-job-id?artifact_type=domain_analysis")

    # Assert response
    assert response.status_code == 404
    assert "detail" in response.json()

    # Assert job service was called
    mock_job_service.get_job.assert_called_once_with("test-job-id")
    mock_job_service.get_artifact.assert_called_once_with("test-job-id", ArtifactType.DOMAIN_ANALYSIS)
