"""
Unit tests for the Domain Analyzer Service.
"""

import os
import sys
import json
import asyncio
import pytest
from unittest.mock import MagicMock, patch, AsyncMock
from pathlib import Path

# Import service modules
from src.services.analyzer import DomainAnalyzerService
from src.services.job_service import JobService

# Import common modules
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.models import Job, JobStatus, Artifact, ArtifactType
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.storage import StorageClient

@pytest.fixture
def storage_client():
    """Create mock storage client."""
    client = MagicMock(spec=StorageClient)
    client._get_full_path.return_value = "/tmp/test"
    return client

@pytest.fixture
def job_service():
    """Create mock job service."""
    service = MagicMock(spec=JobService)
    service.update_job.return_value = None
    service.create_artifact.return_value = Artifact(
        artifact_id="test-artifact-id",
        job_id="test-job-id",
        artifact_type=ArtifactType.DOMAIN_ANALYSIS,
        path="test-path",
        metadata={}
    )
    return service

@pytest.fixture
def analyzer_service(storage_client, job_service):
    """Create domain analyzer service with mock dependencies."""
    service = DomainAnalyzerService(
        storage_client=storage_client,
        max_concurrent_tasks=10
    )
    service.job_service = job_service
    return service

@pytest.mark.asyncio
@patch("src.services.analyzer.DomainAnalysisIntegration")
@patch("os.path.exists")
@patch("os.path.getsize")
async def test_analyze_domains_success(
    mock_getsize, mock_exists, mock_domain_analysis_integration, analyzer_service, job_service
):
    """Test successful domain analysis."""
    # Mock DomainAnalysisIntegration
    mock_domain_analysis_integration.domains_from_significant_functions = AsyncMock(return_value=True)
    
    # Mock os.path.exists and os.path.getsize
    mock_exists.return_value = True
    mock_getsize.return_value = 1000  # 1KB file
    
    # Call analyze_domains
    await analyzer_service.analyze_domains(
        job_id="test-job-id",
        repomap_path="/tmp/test-repo/repomap.json",
        api_key="test-api-key",
        model="gpt-4o-mini",
        max_requests_per_minute=5000,
        max_tokens_per_minute=15000000,
        use_openrouter=False,
        use_claude=False,
        openrouter_base_url="https://openrouter.ai/api/v1",
        claude_model="claude-3-7-sonnet-20250219",
        max_tokens_per_chunk=500000,
        disable_parallel=False,
        max_concurrent_tasks=10,
        generate_explanations=True
    )
    
    # Assert DomainAnalysisIntegration was called with correct parameters
    mock_domain_analysis_integration.domains_from_significant_functions.assert_called_once_with(
        input_path="/tmp/test",
        output_path="/tmp/test",
        api_key="test-api-key",
        model="gpt-4o-mini",
        max_requests_per_minute=5000,
        max_tokens_per_minute=15000000,
        use_openrouter=False,
        use_claude=False,
        openrouter_base_url="https://openrouter.ai/api/v1",
        claude_model="claude-3-7-sonnet-20250219",
        max_tokens_per_chunk=500000,
        disable_parallel=False,
        max_concurrent_tasks=10,
        generate_explanations=True
    )
    
    # Assert job status was updated correctly
    job_service.update_job.assert_any_call(
        job_id="test-job-id",
        status=JobStatus.RUNNING,
        message="Analyzing domains"
    )
    
    job_service.update_job.assert_any_call(
        job_id="test-job-id",
        progress=0.1,
        message="Running domain analysis"
    )
    
    job_service.update_job.assert_any_call(
        job_id="test-job-id",
        status=JobStatus.COMPLETED,
        progress=1.0,
        message="Domain analysis completed",
        result={
            "domain_analysis_artifact_id": "test-artifact-id"
        }
    )
    
    # Assert artifact was created
    job_service.create_artifact.assert_called_once_with(
        job_id="test-job-id",
        artifact_type=ArtifactType.DOMAIN_ANALYSIS,
        path="artifacts/test-job-id/domain_analysis/domain_analysis.yaml",
        metadata={
            "model": "gpt-4o-mini",
            "use_openrouter": False,
            "use_claude": False,
            "generate_explanations": True,
            "elapsed_time": pytest.approx(0, abs=5)  # Allow 5 seconds tolerance
        }
    )

@pytest.mark.asyncio
@patch("src.services.analyzer.DomainAnalysisIntegration")
async def test_analyze_domains_failure(mock_domain_analysis_integration, analyzer_service, job_service):
    """Test domain analysis failure."""
    # Mock DomainAnalysisIntegration to return False
    mock_domain_analysis_integration.domains_from_significant_functions = AsyncMock(return_value=False)
    
    # Call analyze_domains
    await analyzer_service.analyze_domains(
        job_id="test-job-id",
        repomap_path="/tmp/test-repo/repomap.json"
    )
    
    # Assert job status was updated correctly
    job_service.update_job.assert_any_call(
        job_id="test-job-id",
        status=JobStatus.RUNNING,
        message="Analyzing domains"
    )
    
    job_service.update_job.assert_any_call(
        job_id="test-job-id",
        status=JobStatus.FAILED,
        error="Domain analysis failed",
        message="Domain analysis failed"
    )

@pytest.mark.asyncio
@patch("src.services.analyzer.DomainAnalysisIntegration")
async def test_analyze_domains_exception(mock_domain_analysis_integration, analyzer_service, job_service):
    """Test domain analysis exception."""
    # Mock DomainAnalysisIntegration to raise an exception
    mock_domain_analysis_integration.domains_from_significant_functions = AsyncMock(side_effect=Exception("Test error"))
    
    # Call analyze_domains
    await analyzer_service.analyze_domains(
        job_id="test-job-id",
        repomap_path="/tmp/test-repo/repomap.json"
    )
    
    # Assert job status was updated correctly
    job_service.update_job.assert_any_call(
        job_id="test-job-id",
        status=JobStatus.RUNNING,
        message="Analyzing domains"
    )
    
    job_service.update_job.assert_any_call(
        job_id="test-job-id",
        status=JobStatus.FAILED,
        error="Test error",
        message="Domain analysis failed"
    )
