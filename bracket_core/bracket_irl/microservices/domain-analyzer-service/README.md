# Domain Analyzer Service

This microservice is responsible for analyzing repository maps to identify domains and subdomains within a codebase. It uses LLMs to classify functions and classes into a hierarchical domain structure.

## Features

- Analyze repository maps to identify domains
- Support for parallel processing of large codebases
- Configurable LLM models (OpenAI, Claude, OpenRouter)
- REST API for integration with other services
- Background job processing with status tracking
- Prometheus metrics for monitoring
- Detailed health checks

## API Endpoints

- `POST /api/v1/analyze`: Analyze repository map
- `GET /api/v1/status/{job_id}`: Get job status
- `GET /api/v1/artifacts/{job_id}`: Get job artifacts
- `GET /api/v1/health`: Basic health check
- `GET /api/v1/health/details`: Detailed health check
- `GET /metrics`: Prometheus metrics

## Configuration

Configuration can be provided via environment variables or a configuration file:

```yaml
service_name: domain-analyzer-service
host: 0.0.0.0
port: 8002
log_level: INFO
storage_type: local
storage_path: ./data
max_concurrent_tasks: 10
additional_config:
  default_model: gpt-4o-mini
  default_max_requests_per_minute: 5000
  default_max_tokens_per_minute: 15000000
  default_use_openrouter: false
  default_use_claude: false
  default_openrouter_base_url: https://openrouter.ai/api/v1
  default_claude_model: claude-3-7-sonnet-20250219
  default_max_tokens_per_chunk: 500000
  default_disable_parallel: false
  default_generate_explanations: true
```

## Environment Variables

- `SERVICE_NAME`: Name of the service (default: domain-analyzer-service)
- `HOST`: Host to bind the service to (default: 0.0.0.0)
- `PORT`: Port to bind the service to (default: 8002)
- `LOG_LEVEL`: Logging level (default: INFO)
- `STORAGE_TYPE`: Storage type (default: local)
- `STORAGE_PATH`: Path for local storage (default: ./data)
- `MAX_CONCURRENT_TASKS`: Maximum concurrent tasks (default: 10)

## Usage

### Docker

```bash
docker build -t domain-analyzer-service .
docker run -p 8002:8002 \
  -v /path/to/data:/app/data \
  -e SERVICE_NAME=domain-analyzer-service \
  -e HOST=0.0.0.0 \
  -e PORT=8002 \
  -e LOG_LEVEL=INFO \
  -e STORAGE_TYPE=local \
  -e STORAGE_PATH=/app/data \
  -e OPENAI_API_KEY=your-api-key \
  domain-analyzer-service
```

### Docker Compose

```yaml
services:
  domain-analyzer-service:
    build:
      context: ./domain-analyzer-service
    image: domain-analyzer-service:latest
    ports:
      - "8002:8002"
    environment:
      - SERVICE_NAME=domain-analyzer-service
      - HOST=0.0.0.0
      - PORT=8002
      - LOG_LEVEL=INFO
      - STORAGE_TYPE=local
      - STORAGE_PATH=/app/data
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    volumes:
      - ./data:/app/data
```

### Local Development

```bash
# Install dependencies
pip install -r requirements.txt

# Install bracket-irl-common
pip install -e ../bracket-irl-common

# Run the service
uvicorn src.main:app --host 0.0.0.0 --port 8002 --reload
```

## Testing

```bash
# Install test dependencies
pip install pytest pytest-asyncio pytest-cov httpx

# Run tests
pytest

# Run tests with coverage
pytest --cov=src tests/
```

## API Examples

### Analyze Repository Map

```bash
curl -X POST http://localhost:8002/api/v1/analyze \
  -H "Content-Type: application/json" \
  -d '{
    "repomap_path": "artifacts/job-id/repomap/repomap.json",
    "api_key": "your-api-key",
    "model": "gpt-4o-mini",
    "max_requests_per_minute": 5000,
    "max_tokens_per_minute": 15000000,
    "use_openrouter": false,
    "use_claude": false,
    "max_tokens_per_chunk": 500000,
    "disable_parallel": false,
    "max_concurrent_tasks": 10,
    "generate_explanations": true
  }'
```

### Get Job Status

```bash
curl -X GET http://localhost:8002/api/v1/status/{job_id}
```

### Get Job Artifacts

```bash
curl -X GET http://localhost:8002/api/v1/artifacts/{job_id}?artifact_type=domain_analysis&include_content=true
```
