"""
API routes for the Domain Analyzer Service.
"""

import os
import uuid
import time
import logging
from typing import Dict, Any, Optional
from fastapi import APIRouter, BackgroundTasks, HTTPException, Depends, Path, Query

from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.models import DomainAnalysisRequest, Job, JobStatus, Artifact, ArtifactType
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.storage import StorageClient, get_storage_client

from src.api.models import AnalyzeDomainResponse, JobStatusResponse, ArtifactResponse, ErrorResponse
from src.core.config import get_config
from src.services.job_service import JobService
from src.services.analyzer import DomainAnalyzerService

# Create router
router = APIRouter(tags=["Domain Analyzer"])

# Get dependencies
def get_job_service() -> JobService:
    """Get job service."""
    config = get_config()
    storage_client = get_storage_client(
        storage_type=config.storage_type,
        base_path=config.storage_path
    )
    return JobService(storage_client=storage_client)

def get_analyzer_service() -> DomainAnalyzerService:
    """Get domain analyzer service."""
    config = get_config()
    storage_client = get_storage_client(
        storage_type=config.storage_type,
        base_path=config.storage_path
    )
    return DomainAnalyzerService(
        storage_client=storage_client,
        max_concurrent_tasks=config.max_concurrent_tasks
    )

@router.post(
    "/analyze",
    response_model=AnalyzeDomainResponse,
    responses={
        400: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def analyze_domains(
    request: DomainAnalysisRequest,
    background_tasks: BackgroundTasks,
    job_service: JobService = Depends(get_job_service),
    analyzer_service: DomainAnalyzerService = Depends(get_analyzer_service)
):
    """
    Analyze domains in repository map.

    Args:
        request: Domain analysis request
        background_tasks: Background tasks
        job_service: Job service
        analyzer_service: Domain analyzer service

    Returns:
        Job ID and message
    """
    try:
        # Validate repository map path
        # Check if the path is absolute or relative
        repomap_path = request.repomap_path
        if not os.path.isabs(repomap_path):
            # If it's a relative path, try to resolve it relative to the storage path
            config = get_config()
            storage_path = config.storage_path
            absolute_path = os.path.join(storage_path, repomap_path)
            if os.path.exists(absolute_path):
                repomap_path = absolute_path

        # Final check if the file exists
        if not os.path.exists(repomap_path):
            raise HTTPException(
                status_code=400,
                detail=f"Repository map not found: {request.repomap_path}"
            )

        # Create job
        job = job_service.create_job()

        # Start background task
        # Handle optional parameters that might not be in the request model
        kwargs = {
            "job_id": job.job_id,
            "repomap_path": repomap_path,  # Use the resolved path
            "model": request.model,
            "max_tokens_per_chunk": request.max_tokens_per_chunk,
            "disable_parallel": request.disable_parallel,
            "max_concurrent_tasks": request.max_concurrent_tasks,
            "use_openrouter": request.use_openrouter,
            "generate_explanations": request.generate_explanations
        }

        # Add optional parameters if they exist in the request
        if hasattr(request, 'api_key'):
            kwargs["api_key"] = request.api_key
        if hasattr(request, 'max_requests_per_minute'):
            kwargs["max_requests_per_minute"] = request.max_requests_per_minute
        if hasattr(request, 'max_tokens_per_minute'):
            kwargs["max_tokens_per_minute"] = request.max_tokens_per_minute
        if hasattr(request, 'use_claude'):
            kwargs["use_claude"] = request.use_claude
        if hasattr(request, 'openrouter_base_url'):
            kwargs["openrouter_base_url"] = request.openrouter_base_url
        if hasattr(request, 'claude_model'):
            kwargs["claude_model"] = request.claude_model

        background_tasks.add_task(
            analyzer_service.analyze_domains,
            **kwargs
        )

        return AnalyzeDomainResponse(
            job_id=job.job_id,
            message="Domain analysis started"
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start domain analysis: {str(e)}"
        )

@router.get(
    "/status/{job_id}",
    response_model=JobStatusResponse,
    responses={
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def get_job_status(
    job_id: str = Path(..., description="Job ID"),
    job_service: JobService = Depends(get_job_service)
):
    """
    Get job status.

    Args:
        job_id: Job ID
        job_service: Job service

    Returns:
        Job details
    """
    try:
        # Get job
        job = job_service.get_job(job_id)
        if not job:
            raise HTTPException(
                status_code=404,
                detail=f"Job not found: {job_id}"
            )

        return JobStatusResponse(job=job)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get job status: {str(e)}"
        )

@router.get(
    "/artifacts/{job_id}/domain_analysis",
    response_model=ArtifactResponse,
    responses={
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
@router.get(
    "/api/v1/artifacts/{job_id}/domain_analysis",
    response_model=ArtifactResponse,
    responses={
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def get_domain_analysis_artifact(
    job_id: str = Path(..., description="Job ID"),
    include_content: bool = Query(False, description="Include artifact content"),
    job_service: JobService = Depends(get_job_service)
):
    """
    Get domain analysis artifact.

    Args:
        job_id: Job ID
        include_content: Include artifact content
        job_service: Job service

    Returns:
        Artifact details and optionally content
    """
    try:
        # Log request details
        logger = logging.getLogger(__name__)
        logger.info(f"Getting domain analysis artifact for job ID: {job_id}, include_content: {include_content}")

        # Get job
        job = job_service.get_job(job_id)
        if not job:
            logger.error(f"Job not found: {job_id}")
            raise HTTPException(
                status_code=404,
                detail=f"Job not found: {job_id}"
            )

        logger.info(f"Found job: {job.job_id}, status: {job.status}, result: {job.result}")

        # Check if job is completed
        if job.status != JobStatus.COMPLETED:
            logger.warning(f"Job is not completed: {job_id}, status: {job.status}")
            # Continue anyway, as the artifact might still exist

        # Use domain analysis artifact type
        artifact_type_enum = ArtifactType.DOMAIN_ANALYSIS
        logger.info(f"Using artifact type: {artifact_type_enum.value}")

        # Get artifact
        artifact = job_service.get_artifact(job_id, artifact_type_enum)
        if not artifact:
            logger.error(f"Artifact not found for job: {job_id}, type: domain_analysis")

            # Check if the job has a result with domain_analysis_artifact_id
            if job.result and "domain_analysis_artifact_id" in job.result:
                artifact_id = job.result["domain_analysis_artifact_id"]
                logger.error(f"Artifact ID {artifact_id} found in job result but artifact not found")

                # Try to create a new artifact from the job directory
                job_artifacts_dir = f"artifacts/{job_id}"
                domain_analysis_path = f"{job_artifacts_dir}/domain_analysis/domain_analysis.yaml"

                if job_service.storage_client.file_exists(domain_analysis_path):
                    logger.info(f"Found domain analysis file at path: {domain_analysis_path}")

                    # Create a new artifact
                    artifact = Artifact(
                        artifact_id=str(uuid.uuid4()),
                        job_id=job_id,
                        artifact_type=artifact_type_enum,
                        path=domain_analysis_path,
                        created_at=time.time(),
                        metadata={}
                    )

                    # Save the artifact metadata
                    artifact_path = f"artifacts/{artifact.artifact_id}.json"
                    job_service.storage_client.write_json(artifact_path, artifact.model_dump())

                    # Update the job result with the artifact ID
                    result = job.result or {}
                    result["domain_analysis_artifact_id"] = artifact.artifact_id
                    job_service.update_job(job_id, result=result)

                    logger.info(f"Created new artifact: {artifact.artifact_id}")
                else:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Artifact with ID {artifact_id} not found for job: {job_id}, type: domain_analysis"
                    )
            else:
                # Try to list all artifacts to help debug
                try:
                    artifact_files = job_service.storage_client.list_files("artifacts")
                    logger.info(f"Available artifacts: {artifact_files}")

                    # Check if there's a domain analysis file in the job directory
                    job_artifacts_dir = f"artifacts/{job_id}"
                    domain_analysis_path = f"{job_artifacts_dir}/domain_analysis/domain_analysis.yaml"

                    if job_service.storage_client.file_exists(domain_analysis_path):
                        logger.info(f"Found domain analysis file at path: {domain_analysis_path}")

                        # Create a new artifact
                        artifact = Artifact(
                            artifact_id=str(uuid.uuid4()),
                            job_id=job_id,
                            artifact_type=artifact_type_enum,
                            path=domain_analysis_path,
                            created_at=time.time(),
                            metadata={}
                        )

                        # Save the artifact metadata
                        artifact_path = f"artifacts/{artifact.artifact_id}.json"
                        job_service.storage_client.write_json(artifact_path, artifact.model_dump())

                        # Update the job result with the artifact ID
                        result = job.result or {}
                        result["domain_analysis_artifact_id"] = artifact.artifact_id
                        job_service.update_job(job_id, result=result)

                        logger.info(f"Created new artifact: {artifact.artifact_id}")
                    else:
                        raise HTTPException(
                            status_code=404,
                            detail=f"Artifact not found for job: {job_id}, type: domain_analysis. Job result: {job.result}"
                        )
                except Exception as e:
                    logger.error(f"Error listing artifacts: {e}")
                    raise HTTPException(
                        status_code=404,
                        detail=f"Artifact not found for job: {job_id}, type: domain_analysis. Job result: {job.result}"
                    )

        logger.info(f"Found artifact: {artifact.artifact_id}, path: {artifact.path}")

        # Check if artifact path exists
        if not job_service.storage_client.file_exists(artifact.path):
            logger.error(f"Artifact path not found: {artifact.path}")
            raise HTTPException(
                status_code=404,
                detail=f"Artifact content not found at path: {artifact.path}"
            )

        # Get artifact content if requested
        content = None
        if include_content:
            try:
                content = job_service.get_artifact_content(artifact)
                logger.info(f"Got artifact content, size: {len(str(content)) if content else 0}")

                if content is None:
                    logger.error(f"Artifact content is None for path: {artifact.path}")
                    raise HTTPException(
                        status_code=404,
                        detail=f"Artifact content is empty at path: {artifact.path}"
                    )
            except Exception as e:
                logger.error(f"Error getting artifact content: {e}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Error getting artifact content: {str(e)}"
                )

        return ArtifactResponse(
            artifact=artifact,
            content=content
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get artifact: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get artifact: {str(e)}"
        )


@router.get(
    "/artifacts/{job_id}/{artifact_type}",
    response_model=ArtifactResponse,
    responses={
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
@router.get(
    "/api/v1/artifacts/{job_id}/{artifact_type}",
    response_model=ArtifactResponse,
    responses={
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def get_artifact_by_type(
    job_id: str = Path(..., description="Job ID"),
    artifact_type: str = Path(..., description="Artifact type"),
    include_content: bool = Query(False, description="Include artifact content"),
    job_service: JobService = Depends(get_job_service)
):
    """
    Get artifact by type.

    Args:
        job_id: Job ID
        artifact_type: Artifact type
        include_content: Include artifact content
        job_service: Job service

    Returns:
        Artifact details and optionally content
    """
    try:
        # Log request details
        logger = logging.getLogger(__name__)
        logger.info(f"Getting artifact for job ID: {job_id}, artifact type: {artifact_type}, include_content: {include_content}")

        # Get job
        job = job_service.get_job(job_id)
        if not job:
            logger.error(f"Job not found: {job_id}")
            raise HTTPException(
                status_code=404,
                detail=f"Job not found: {job_id}"
            )

        logger.info(f"Found job: {job.job_id}, status: {job.status}, result: {job.result}")

        # Check if job is completed
        if job.status != JobStatus.COMPLETED:
            logger.warning(f"Job is not completed: {job_id}, status: {job.status}")
            # Continue anyway, as the artifact might still exist

        # Convert string artifact type to enum
        try:
            artifact_type_enum = ArtifactType(artifact_type)
            logger.info(f"Using artifact type: {artifact_type_enum.value}")
        except ValueError:
            logger.error(f"Invalid artifact type: {artifact_type}")
            raise HTTPException(
                status_code=400,
                detail=f"Invalid artifact type: {artifact_type}"
            )

        # Get artifact
        artifact = job_service.get_artifact(job_id, artifact_type_enum)
        if not artifact:
            logger.error(f"Artifact not found for job: {job_id}, type: {artifact_type}")

            # Check if the job has a result with the artifact ID
            artifact_id_key = f"{artifact_type}_artifact_id"
            if job.result and artifact_id_key in job.result:
                artifact_id = job.result[artifact_id_key]
                logger.error(f"Artifact ID {artifact_id} found in job result but artifact not found")

                # For domain analysis artifacts, try to create a new artifact from the job directory
                if artifact_type == "domain_analysis":
                    job_artifacts_dir = f"artifacts/{job_id}"
                    domain_analysis_path = f"{job_artifacts_dir}/domain_analysis/domain_analysis.yaml"

                    if job_service.storage_client.file_exists(domain_analysis_path):
                        logger.info(f"Found domain analysis file at path: {domain_analysis_path}")

                        # Create a new artifact
                        artifact = Artifact(
                            artifact_id=str(uuid.uuid4()),
                            job_id=job_id,
                            artifact_type=artifact_type_enum,
                            path=domain_analysis_path,
                            created_at=time.time(),
                            metadata={}
                        )

                        # Save the artifact metadata
                        artifact_path = f"artifacts/{artifact.artifact_id}.json"
                        job_service.storage_client.write_json(artifact_path, artifact.model_dump())

                        # Update the job result with the artifact ID
                        result = job.result or {}
                        result[artifact_id_key] = artifact.artifact_id
                        job_service.update_job(job_id, result=result)

                        logger.info(f"Created new artifact: {artifact.artifact_id}")
                    else:
                        raise HTTPException(
                            status_code=404,
                            detail=f"Artifact with ID {artifact_id} not found for job: {job_id}, type: {artifact_type}"
                        )
                else:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Artifact with ID {artifact_id} not found for job: {job_id}, type: {artifact_type}"
                    )
            else:
                # Try to list all artifacts to help debug
                try:
                    artifact_files = job_service.storage_client.list_files("artifacts")
                    logger.info(f"Available artifacts: {artifact_files}")

                    # For domain analysis artifacts, check if there's a domain analysis file in the job directory
                    if artifact_type == "domain_analysis":
                        job_artifacts_dir = f"artifacts/{job_id}"
                        domain_analysis_path = f"{job_artifacts_dir}/domain_analysis/domain_analysis.yaml"

                        if job_service.storage_client.file_exists(domain_analysis_path):
                            logger.info(f"Found domain analysis file at path: {domain_analysis_path}")

                            # Create a new artifact
                            artifact = Artifact(
                                artifact_id=str(uuid.uuid4()),
                                job_id=job_id,
                                artifact_type=artifact_type_enum,
                                path=domain_analysis_path,
                                created_at=time.time(),
                                metadata={}
                            )

                            # Save the artifact metadata
                            artifact_path = f"artifacts/{artifact.artifact_id}.json"
                            job_service.storage_client.write_json(artifact_path, artifact.model_dump())

                            # Update the job result with the artifact ID
                            result = job.result or {}
                            result[f"{artifact_type}_artifact_id"] = artifact.artifact_id
                            job_service.update_job(job_id, result=result)

                            logger.info(f"Created new artifact: {artifact.artifact_id}")
                        else:
                            raise HTTPException(
                                status_code=404,
                                detail=f"Artifact not found for job: {job_id}, type: {artifact_type}. Job result: {job.result}"
                            )
                    else:
                        raise HTTPException(
                            status_code=404,
                            detail=f"Artifact not found for job: {job_id}, type: {artifact_type}. Job result: {job.result}"
                        )
                except Exception as e:
                    logger.error(f"Error listing artifacts: {e}")
                    raise HTTPException(
                        status_code=404,
                        detail=f"Artifact not found for job: {job_id}, type: {artifact_type}. Job result: {job.result}"
                    )

        logger.info(f"Found artifact: {artifact.artifact_id}, path: {artifact.path}")

        # Check if artifact path exists
        if not job_service.storage_client.file_exists(artifact.path):
            logger.error(f"Artifact path not found: {artifact.path}")
            raise HTTPException(
                status_code=404,
                detail=f"Artifact content not found at path: {artifact.path}"
            )

        # Get artifact content if requested
        content = None
        if include_content:
            try:
                content = job_service.get_artifact_content(artifact)
                logger.info(f"Got artifact content, size: {len(str(content)) if content else 0}")

                if content is None:
                    logger.error(f"Artifact content is None for path: {artifact.path}")
                    raise HTTPException(
                        status_code=404,
                        detail=f"Artifact content is empty at path: {artifact.path}"
                    )
            except Exception as e:
                logger.error(f"Error getting artifact content: {e}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Error getting artifact content: {str(e)}"
                )

        return ArtifactResponse(
            artifact=artifact,
            content=content
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get artifact: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get artifact: {str(e)}"
        )
