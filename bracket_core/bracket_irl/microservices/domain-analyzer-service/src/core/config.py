"""
Configuration for the Domain Analyzer Service.
"""

import os
from functools import lru_cache
from typing import Dict, Any, Optional

from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.config import Config

# Default configuration
DEFAULT_CONFIG = {
    "service_name": "domain-analyzer-service",
    "host": "0.0.0.0",
    "port": 8001,
    "log_level": "INFO",
    "api_prefix": "/api/v1",
    "storage_type": "local",
    "storage_path": "./data",
    "max_concurrent_tasks": 10,
    "job_timeout_seconds": 3600,
    "additional_config": {
        "default_model": "gpt-4o-mini",
        "default_use_openrouter": False,
        "default_use_claude": False,
        "default_max_tokens_per_chunk": 500000,
        "default_disable_parallel": False,
        "default_generate_explanations": True,
        "default_max_requests_per_minute": 5000,
        "default_max_tokens_per_minute": 15000000,
    }
}

@lru_cache()
def get_config() -> Config:
    """
    Get service configuration.
    
    Returns:
        Service configuration
    """
    config_dict = DEFAULT_CONFIG.copy()
    
    # Override with environment variables
    for key in config_dict:
        env_key = f"DOMAIN_ANALYZER_{key.upper()}"
        if env_key in os.environ:
            config_dict[key] = os.environ[env_key]
    
    return Config(**config_dict)

def initialize_config() -> Config:
    """
    Initialize service configuration.
    
    Returns:
        Service configuration
    """
    return get_config()
