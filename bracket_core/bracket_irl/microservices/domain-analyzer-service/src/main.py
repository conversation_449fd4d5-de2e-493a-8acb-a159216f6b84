"""
Main entry point for the Domain Analyzer Service.
"""

import os
import sys
import asyncio
import time
from fastapi import FastAPI, BackgroundTasks, HTTPException, Depends, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
from prometheus_client import make_asgi_app

# Add parent directory to path to import bracket_irl_common
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../")))

# Import common utilities
# Import directly from bracket_irl_common package
from bracket_irl_common.config import Config
from bracket_irl_common.logging import ServiceLogger
from bracket_irl_common.storage import get_storage_client
from bracket_irl_common.models import Job, JobStatus, Artifact, ArtifactType, DomainAnalysisRequest
from bracket_irl_common.health import HealthStatus, HealthCheck, HealthCheckResult, HealthCheckService
from bracket_irl_common.metrics import counter, gauge, histogram, timer
from bracket_irl_common.errors import ServiceError, handle_exception

# Import service-specific modules
from src.api.routes import router as api_router
from src.core.config import get_config, initialize_config
from src.services.job_service import JobService
from src.services.analyzer import DomainAnalyzerService

# Initialize configuration
config = initialize_config()

# Initialize logger
logger = ServiceLogger(
    service_name=config.service_name,
    log_level=config.log_level,
    json_logs=False
).logger

# Initialize storage client
storage_client = get_storage_client(
    storage_type=config.storage_type,
    base_path=config.storage_path
)

# Initialize job service
job_service = JobService(storage_client=storage_client)

# Initialize analyzer service
analyzer_service = DomainAnalyzerService(
    storage_client=storage_client,
    max_concurrent_tasks=config.max_concurrent_tasks
)

# Initialize health check service
health_service = HealthCheckService(service_name=config.service_name)

# Initialize metrics
request_counter = counter(
    name="domain_analyzer_requests_total",
    description="Total number of requests",
    labels={"service": config.service_name}
)

request_latency = histogram(
    name="domain_analyzer_request_latency_seconds",
    description="Request latency in seconds",
    buckets=[0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10],
    labels={"service": config.service_name}
)

active_jobs = gauge(
    name="domain_analyzer_active_jobs",
    description="Number of active jobs",
    labels={"service": config.service_name}
)

# Register health checks
async def check_storage():
    """Check storage health."""
    try:
        # Check if storage is accessible
        test_file = f"health_check_{int(time.time())}.txt"
        storage_client.write_text(test_file, "Health check")
        storage_client.delete_file(test_file)

        return HealthCheck(
            name="storage",
            status=HealthStatus.UP,
            details={
                "storage_type": config.storage_type,
                "storage_path": config.storage_path
            }
        )
    except Exception as e:
        return HealthCheck(
            name="storage",
            status=HealthStatus.DOWN,
            details={
                "storage_type": config.storage_type,
                "storage_path": config.storage_path,
                "error": str(e)
            }
        )

async def check_job_service():
    """Check job service health."""
    try:
        # Check if job service is working
        job_count = len(job_service.list_jobs())

        return HealthCheck(
            name="job_service",
            status=HealthStatus.UP,
            details={
                "job_count": job_count
            }
        )
    except Exception as e:
        return HealthCheck(
            name="job_service",
            status=HealthStatus.DOWN,
            details={
                "error": str(e)
            }
        )

async def check_llm_service():
    """Check LLM service health."""
    try:
        # Check if LLM service is available
        # This is a simple check that doesn't actually call the LLM API
        # In a real implementation, you might want to make a test call to the API

        return HealthCheck(
            name="llm_service",
            status=HealthStatus.UP,
            details={
                "default_model": config.additional_config.get("default_model", "gpt-4o-mini"),
                "use_openrouter": config.additional_config.get("default_use_openrouter", False),
                "use_claude": config.additional_config.get("default_use_claude", False)
            }
        )
    except Exception as e:
        return HealthCheck(
            name="llm_service",
            status=HealthStatus.DOWN,
            details={
                "error": str(e)
            }
        )

# Register health checks
health_service.register_check("storage", check_storage)
health_service.register_check("job_service", check_job_service)
health_service.register_check("llm_service", check_llm_service)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Lifespan context manager for FastAPI application.

    This function is called when the application starts and stops.
    """
    # Startup
    logger.info(f"Starting {config.service_name}")

    # Create necessary directories
    os.makedirs(os.path.join(config.storage_path, "jobs"), exist_ok=True)
    os.makedirs(os.path.join(config.storage_path, "artifacts"), exist_ok=True)

    yield

    # Shutdown
    logger.info(f"Shutting down {config.service_name}")

# Create FastAPI application
app = FastAPI(
    title="Domain Analyzer Service",
    description="Service for analyzing repository maps to identify domains",
    version="0.1.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add Prometheus metrics middleware
metrics_app = make_asgi_app()
app.mount("/metrics", metrics_app)

# Add request timing middleware
@app.middleware("http")
async def add_timing_middleware(request: Request, call_next):
    """Add timing middleware to measure request latency."""
    start_time = time.time()

    try:
        response = await call_next(request)

        # Update metrics
        request_counter.inc()
        request_latency.observe(time.time() - start_time)

        return response
    except Exception as e:
        # Handle exceptions
        error_response = handle_exception(e)
        return JSONResponse(
            status_code=error_response["status_code"],
            content={"error": error_response["error"]}
        )

# Include API router
app.include_router(api_router, prefix=config.api_prefix)

# Health check endpoint
@app.get("/health")
async def health_check():
    """Basic health check endpoint."""
    return {"status": "ok", "service": config.service_name}

# Detailed health check endpoint
@app.get("/health/details")
async def detailed_health_check():
    """Detailed health check endpoint."""
    health_result = await health_service.check_health()
    return {
        "status": health_result.status,
        "service": config.service_name,
        "checks": [check.__dict__ for check in health_result.checks],
        "timestamp": health_result.timestamp
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host=config.host, port=config.port)
