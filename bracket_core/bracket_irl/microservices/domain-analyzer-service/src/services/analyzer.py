"""
Domain analyzer service for the Domain Analyzer Service.
"""

import os
import time
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from contextlib import contextmanager

from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.models import Job, JobStatus, Artifact, ArtifactType
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.storage import StorageClient
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.clients.llm_client import get_llm_client
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.utils.token_counter import num_tokens_from_string
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.metrics import counter, gauge, histogram, timer

from bracket_irl.bracket_domain_analysis import DomainAnalysisIntegration

from src.services.job_service import JobService

# Set up logging
import logging
logger = logging.getLogger(__name__)

# Try to initialize metrics
try:
    # Metrics
    job_counter = counter(
        name="domain_analyzer_jobs_total",
        description="Total number of domain analysis jobs",
        labels={"service": "domain-analyzer-service", "status": ""}
    )

    job_latency = histogram(
        name="domain_analyzer_job_latency_seconds",
        description="Domain analysis job latency in seconds",
        buckets=[1, 5, 10, 30, 60, 120, 300, 600, 1800, 3600],
        labels={"service": "domain-analyzer-service"}
    )

    active_jobs = gauge(
        name="domain_analyzer_active_jobs",
        description="Number of active domain analysis jobs",
        labels={"service": "domain-analyzer-service"}
    )

    llm_request_counter = counter(
        name="domain_analyzer_llm_requests_total",
        description="Total number of LLM requests",
        labels={"service": "domain-analyzer-service", "model": "", "status": ""}
    )

    llm_token_counter = counter(
        name="domain_analyzer_llm_tokens_total",
        description="Total number of tokens processed by LLM",
        labels={"service": "domain-analyzer-service", "model": "", "direction": ""}
    )

    # Check if metrics are callable
    if not callable(getattr(job_counter, 'labels', None)):
        logger.warning("job_counter.labels is not callable, creating mock implementation")
        job_counter.labels = lambda **kwargs: MockMetric()

    if not callable(getattr(job_latency, 'observe', None)):
        logger.warning("job_latency.observe is not callable, creating mock implementation")
        job_latency.observe = lambda value: None

    if not callable(getattr(active_jobs, 'inc', None)):
        logger.warning("active_jobs.inc is not callable, creating mock implementation")
        active_jobs.inc = lambda: None
        active_jobs.dec = lambda: None

    if not callable(getattr(llm_request_counter, 'labels', None)):
        logger.warning("llm_request_counter.labels is not callable, creating mock implementation")
        llm_request_counter.labels = lambda **kwargs: MockMetric()

    if not callable(getattr(llm_token_counter, 'labels', None)):
        logger.warning("llm_token_counter.labels is not callable, creating mock implementation")
        llm_token_counter.labels = lambda **kwargs: MockMetric()

except Exception as e:
    logger.error(f"Error initializing metrics: {e}")

    # Create mock metrics
    class MockMetric:
        def __init__(self):
            self._labels = {}

        def inc(self, value=1):
            label_str = ", ".join([f"{k}={v}" for k, v in self._labels.items()]) if self._labels else "no labels"
            logger.info(f"Mock metric inc called with value: {value} and labels: {label_str}")

        def dec(self, value=1):
            label_str = ", ".join([f"{k}={v}" for k, v in self._labels.items()]) if self._labels else "no labels"
            logger.info(f"Mock metric dec called with value: {value} and labels: {label_str}")

        def observe(self, value):
            label_str = ", ".join([f"{k}={v}" for k, v in self._labels.items()]) if self._labels else "no labels"
            logger.info(f"Mock metric observe called with value: {value} and labels: {label_str}")

        def labels(self, **kwargs):
            logger.info(f"Mock metric labels called with kwargs: {kwargs}")
            result = MockMetric()
            result._labels = kwargs
            return result

    job_counter = MockMetric()
    job_latency = MockMetric()
    active_jobs = MockMetric()
    llm_request_counter = MockMetric()
    llm_token_counter = MockMetric()

class DomainAnalyzerService:
    """Service for analyzing domains in repository maps."""

    def __init__(self, storage_client: StorageClient, max_concurrent_tasks: int = 10):
        """
        Initialize domain analyzer service.

        Args:
            storage_client: Storage client
            max_concurrent_tasks: Maximum concurrent tasks
        """
        self.storage_client = storage_client
        self.max_concurrent_tasks = max_concurrent_tasks
        self.job_service = JobService(storage_client=storage_client)

    async def analyze_domains(
        self,
        job_id: str,
        repomap_path: str,
        api_key: Optional[str] = None,
        model: str = "gpt-4o-mini",
        max_requests_per_minute: float = 5000,
        max_tokens_per_minute: float = 15000000,
        use_openrouter: bool = False,
        use_claude: bool = False,
        openrouter_base_url: str = "https://openrouter.ai/api/v1",
        claude_model: str = "claude-3-7-sonnet-20250219",
        max_tokens_per_chunk: int = 500000,
        disable_parallel: bool = False,
        max_concurrent_tasks: int = 0,
        generate_explanations: bool = True,
    ) -> None:
        """
        Analyze domains in repository map.

        Args:
            job_id: Job ID
            repomap_path: Path to repository map
            api_key: API key for LLM
            model: Model to use
            max_requests_per_minute: Maximum requests per minute
            max_tokens_per_minute: Maximum tokens per minute
            use_openrouter: Whether to use OpenRouter
            use_claude: Whether to use Claude
            openrouter_base_url: OpenRouter base URL
            claude_model: Claude model to use
            max_tokens_per_chunk: Maximum tokens per chunk
            disable_parallel: Whether to disable parallel processing
            max_concurrent_tasks: Maximum concurrent tasks
            generate_explanations: Whether to generate explanations
        """
        # Set up logging
        import logging
        logger = logging.getLogger(__name__)
        start_time = time.time()

        # Safely increment active jobs counter
        try:
            active_jobs.inc()
        except Exception as e:
            logger.warning(f"Failed to increment active jobs counter: {e}")

        # Determine which model is actually being used
        actual_model = model
        if use_claude:
            actual_model = claude_model
        elif use_openrouter:
            actual_model = "openrouter"

        try:
            # Update job status
            self.job_service.update_job(
                job_id=job_id,
                status=JobStatus.RUNNING,
                message="Analyzing domains"
            )

            # Create output directory
            output_dir = f"artifacts/{job_id}/domain_analysis"

            # Safely get full path
            try:
                # Try different methods to get full path
                if hasattr(self.storage_client, '_get_full_path'):
                    full_output_dir = self.storage_client._get_full_path(output_dir)
                elif hasattr(self.storage_client, 'get_full_path'):
                    full_output_dir = self.storage_client.get_full_path(output_dir)
                else:
                    # Try to inspect storage client
                    logger.info(f"Storage client type: {type(self.storage_client)}")
                    logger.info(f"Storage client attributes: {dir(self.storage_client)}")

                    # Fallback to direct path
                    full_output_dir = output_dir

                logger.info(f"Creating output directory: {full_output_dir}")
                os.makedirs(full_output_dir, exist_ok=True)
            except Exception as e:
                logger.error(f"Error creating output directory: {e}")
                # Fallback to direct path
                full_output_dir = output_dir
                os.makedirs(full_output_dir, exist_ok=True)

            # Set output path
            output_path = f"{output_dir}/domain_analysis.yaml"

            # Get full paths (safely)
            try:
                # Special case for e2e test
                if repomap_path.startswith('/app/tests/e2e/results/e2e_pipeline/'):
                    # Use the path directly
                    full_repomap_path = repomap_path
                    full_output_path = self.storage_client._get_full_path(output_path)
                # Special case for e2e test with data path
                elif '/tests/e2e/results/e2e_pipeline/' in repomap_path:
                    # Extract the path after /tests/e2e/results/e2e_pipeline/
                    path_parts = repomap_path.split('/tests/e2e/results/e2e_pipeline/')
                    if len(path_parts) > 1:
                        filename = path_parts[1]
                        full_repomap_path = f"/app/tests/e2e/results/e2e_pipeline/{filename}"
                        logger.info(f"Adjusted path for e2e test: {full_repomap_path}")
                    else:
                        full_repomap_path = repomap_path
                    full_output_path = self.storage_client._get_full_path(output_path)
                # Try different methods to get full path
                elif hasattr(self.storage_client, '_get_full_path'):
                    # Clean up the path first to remove any './' or duplicate '/app/data'
                    clean_repomap_path = repomap_path.replace('./data/', '')
                    clean_repomap_path = clean_repomap_path.replace('/app/data/app/data/', '/app/data/')

                    # If the path already starts with /app/data, use it directly
                    if clean_repomap_path.startswith('/app/data/'):
                        full_repomap_path = clean_repomap_path
                    else:
                        full_repomap_path = self.storage_client._get_full_path(clean_repomap_path)

                    full_output_path = self.storage_client._get_full_path(output_path)
                elif hasattr(self.storage_client, 'get_full_path'):
                    # Clean up the path first to remove any './' or duplicate '/app/data'
                    clean_repomap_path = repomap_path.replace('./data/', '')
                    clean_repomap_path = clean_repomap_path.replace('/app/data/app/data/', '/app/data/')

                    # If the path already starts with /app/data, use it directly
                    if clean_repomap_path.startswith('/app/data/'):
                        full_repomap_path = clean_repomap_path
                    else:
                        full_repomap_path = self.storage_client.get_full_path(clean_repomap_path)

                    full_output_path = self.storage_client.get_full_path(output_path)
                else:
                    # Fallback to direct paths
                    # Clean up the path first to remove any './' or duplicate '/app/data'
                    clean_repomap_path = repomap_path.replace('./data/', '')
                    clean_repomap_path = clean_repomap_path.replace('/app/data/app/data/', '/app/data/')

                    full_repomap_path = clean_repomap_path
                    full_output_path = output_path
            except Exception as e:
                logger.error(f"Error getting full paths: {e}")
                # Fallback to direct paths
                # Clean up the path first to remove any './' or duplicate '/app/data'
                clean_repomap_path = repomap_path.replace('./data/', '')
                clean_repomap_path = clean_repomap_path.replace('/app/data/app/data/', '/app/data/')

                full_repomap_path = clean_repomap_path
                full_output_path = output_path

            logger.info(f"Using repomap path: {full_repomap_path}")
            logger.info(f"Using output path: {full_output_path}")

            # Update job status
            self.job_service.update_job(
                job_id=job_id,
                progress=0.1,
                message="Running domain analysis"
            )

            # Monkey patch the DomainAnalysisIntegration to track LLM metrics
            import logging
            logger = logging.getLogger(__name__)

            logger.info("Getting original domains_from_significant_functions method")
            logger.info("DomainAnalysisIntegration type: %s", type(DomainAnalysisIntegration))
            logger.info("DomainAnalysisIntegration attributes: %s", dir(DomainAnalysisIntegration))

            # Check if domains_from_significant_functions is callable
            if not callable(getattr(DomainAnalysisIntegration, 'domains_from_significant_functions', None)):
                logger.error("domains_from_significant_functions is not callable")
                logger.error("Type: %s", type(getattr(DomainAnalysisIntegration, 'domains_from_significant_functions', None)))
                raise TypeError("domains_from_significant_functions is not callable")

            original_domains_from_significant_functions = DomainAnalysisIntegration.domains_from_significant_functions
            logger.info("Original method type: %s", type(original_domains_from_significant_functions))

            async def patched_domains_from_significant_functions(*args, **kwargs):
                logger.info("Patched domains_from_significant_functions called with args: %s, kwargs: %s", args, kwargs)

                # Track LLM metrics (safely)
                try:
                    llm_request_counter.labels(model=actual_model, status="started").inc()
                except Exception as e:
                    logger.warning(f"Failed to increment LLM request counter: {e}")

                try:
                    # Call original method
                    logger.info("Calling original domains_from_significant_functions")
                    result = await original_domains_from_significant_functions(*args, **kwargs)
                    logger.info("Original method call successful, result: %s", result)

                    # Update metrics based on result (safely)
                    try:
                        if result:
                            llm_request_counter.labels(model=actual_model, status="completed").inc()
                        else:
                            llm_request_counter.labels(model=actual_model, status="failed").inc()
                    except Exception as e:
                        logger.warning(f"Failed to increment LLM request counter: {e}")

                    return result
                except Exception as e:
                    logger.error("Error calling original domains_from_significant_functions: %s", e, exc_info=True)

                    # Update metrics (safely)
                    try:
                        llm_request_counter.labels(model=actual_model, status="failed").inc()
                    except Exception as e2:
                        logger.warning(f"Failed to increment LLM request counter: {e2}")

                    raise

            # Apply monkey patch
            logger.info("Applying monkey patch to domains_from_significant_functions")
            DomainAnalysisIntegration.domains_from_significant_functions = patched_domains_from_significant_functions
            logger.info("Monkey patch applied successfully")

            try:
                # Prepare arguments for domain analysis
                analysis_kwargs = {
                    "input_path": full_repomap_path,
                    "output_path": full_output_path,
                    "model": model,
                    "use_openrouter": use_openrouter,
                    "use_claude": use_claude,
                    "max_tokens_per_chunk": max_tokens_per_chunk,
                    "disable_parallel": disable_parallel,
                    "max_concurrent_tasks": max_concurrent_tasks,
                    "generate_explanations": generate_explanations
                }

                # Add optional parameters if they are provided
                if api_key is not None:
                    analysis_kwargs["api_key"] = api_key
                if max_requests_per_minute is not None:
                    analysis_kwargs["max_requests_per_minute"] = max_requests_per_minute
                if max_tokens_per_minute is not None:
                    analysis_kwargs["max_tokens_per_minute"] = max_tokens_per_minute
                if openrouter_base_url is not None:
                    analysis_kwargs["openrouter_base_url"] = openrouter_base_url
                if claude_model is not None:
                    analysis_kwargs["claude_model"] = claude_model

                # Run domain analysis
                success = await DomainAnalysisIntegration.domains_from_significant_functions(**analysis_kwargs)
            finally:
                # Restore original method
                DomainAnalysisIntegration.domains_from_significant_functions = original_domains_from_significant_functions

            if not success:
                raise Exception("Domain analysis failed")

            # Estimate token usage from file size
            if os.path.exists(full_output_path):
                file_size = os.path.getsize(full_output_path)
                estimated_tokens = file_size // 4  # Rough estimate: 1 token ≈ 4 bytes
                try:
                    llm_token_counter.labels(model=actual_model, direction="output").inc(estimated_tokens)
                except Exception as e:
                    logger.warning(f"Failed to increment token counter: {e}")

            # Create artifact
            # Use the relative path for the artifact, not the full path
            # This is important for the orchestrator service to find the artifact
            domain_analysis_artifact = self.job_service.create_artifact(
                job_id=job_id,
                artifact_type=ArtifactType.DOMAIN_ANALYSIS,
                path=output_path,  # Use the relative path
                metadata={
                    "model": actual_model,
                    "use_openrouter": use_openrouter,
                    "use_claude": use_claude,
                    "generate_explanations": generate_explanations,
                    "elapsed_time": time.time() - start_time
                }
            )

            # Log artifact creation
            logger.info(f"Created domain analysis artifact: {domain_analysis_artifact.artifact_id}, path: {domain_analysis_artifact.path}")

            # Update job status with the artifact ID
            # Make sure to include the artifact ID in the job result
            # This is critical for the orchestrator service to find the artifact
            result = {
                "domain_analysis_artifact_id": domain_analysis_artifact.artifact_id,
                "artifact_path": domain_analysis_artifact.path,
                "artifact_type": ArtifactType.DOMAIN_ANALYSIS.value
            }

            logger.info(f"Updating job with result: {result}")

            self.job_service.update_job(
                job_id=job_id,
                status=JobStatus.COMPLETED,
                progress=1.0,
                message="Domain analysis completed",
                result=result
            )

            # Verify that the job was updated correctly
            updated_job = self.job_service.get_job(job_id)
            logger.info(f"Updated job result: {updated_job.result if updated_job else 'Job not found'}")

            # Update metrics (safely)
            try:
                job_counter.labels(status="completed").inc()
            except Exception as e:
                logger.warning(f"Failed to increment job counter: {e}")

            try:
                job_latency.observe(time.time() - start_time)
            except Exception as e:
                logger.warning(f"Failed to observe job latency: {e}")

        except Exception as e:
            # Update job status
            self.job_service.update_job(
                job_id=job_id,
                status=JobStatus.FAILED,
                error=str(e),
                message="Domain analysis failed"
            )

            # Update metrics (safely)
            try:
                job_counter.labels(status="failed").inc()
            except Exception as e2:
                logger.warning(f"Failed to increment job counter: {e2}")

            try:
                job_latency.observe(time.time() - start_time)
            except Exception as e2:
                logger.warning(f"Failed to observe job latency: {e2}")

        finally:
            # Decrement active jobs counter (safely)
            try:
                active_jobs.dec()
            except Exception as e:
                logger.warning(f"Failed to decrement active jobs counter: {e}")
