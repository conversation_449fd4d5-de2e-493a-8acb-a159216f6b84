"""
Job service for the Domain Analyzer Service.
"""

import os
import time
import json
import uuid
from typing import Dict, List, Any, Optional, Union

from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.models import Job, JobStatus, Artifact, ArtifactType
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.storage import StorageClient

class JobService:
    """Service for managing jobs."""

    def __init__(self, storage_client: StorageClient):
        """
        Initialize job service.

        Args:
            storage_client: Storage client
        """
        self.storage_client = storage_client

    def create_job(self) -> Job:
        """
        Create a new job.

        Returns:
            Job object
        """
        job = Job(
            job_id=str(uuid.uuid4()),
            status=JobStatus.PENDING,
            created_at=time.time(),
            updated_at=time.time(),
            progress=0.0,
            message="Job created"
        )

        # Save job
        self.storage_client.write_json(f"jobs/{job.job_id}.json", job.model_dump())

        return job

    def get_job(self, job_id: str) -> Optional[Job]:
        """
        Get job by ID.

        Args:
            job_id: Job ID

        Returns:
            Job object or None if not found
        """
        job_path = f"jobs/{job_id}.json"

        if not self.storage_client.file_exists(job_path):
            return None

        job_data = self.storage_client.read_json(job_path)
        return Job(**job_data)

    def update_job(
        self,
        job_id: str,
        status: Optional[JobStatus] = None,
        progress: Optional[float] = None,
        message: Optional[str] = None,
        error: Optional[str] = None,
        result: Optional[Dict[str, Any]] = None
    ) -> Optional[Job]:
        """
        Update job.

        Args:
            job_id: Job ID
            status: New job status
            progress: New job progress
            message: New job message
            error: New job error
            result: New job result

        Returns:
            Updated job object or None if not found
        """
        job = self.get_job(job_id)
        if not job:
            return None

        # Update job
        if status is not None:
            job.status = status

        if progress is not None:
            job.progress = progress

        if message is not None:
            job.message = message

        if error is not None:
            job.error = error

        if result is not None:
            job.result = result

        job.updated_at = time.time()

        # Save job
        self.storage_client.write_json(f"jobs/{job.job_id}.json", job.model_dump())

        return job

    def create_artifact(
        self,
        job_id: str,
        artifact_type: ArtifactType,
        path: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Artifact:
        """
        Create artifact.

        Args:
            job_id: Job ID
            artifact_type: Artifact type
            path: Artifact path
            metadata: Artifact metadata

        Returns:
            Artifact object
        """
        artifact = Artifact(
            artifact_id=str(uuid.uuid4()),
            job_id=job_id,
            artifact_type=artifact_type,
            path=path,
            created_at=time.time(),
            metadata=metadata or {}
        )

        # Save artifact
        self.storage_client.write_json(f"artifacts/{artifact.artifact_id}.json", artifact.model_dump())

        return artifact

    def get_artifact(self, job_id: str, artifact_type: ArtifactType) -> Optional[Artifact]:
        """
        Get artifact by job ID and type.

        Args:
            job_id: Job ID
            artifact_type: Artifact type

        Returns:
            Artifact object or None if not found
        """
        # Set up logging
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"Getting artifact for job ID: {job_id}, artifact type: {artifact_type.value}")

        # First, try to find the artifact directly by job ID and type
        artifact = self._find_artifact_by_job_id_and_type(job_id, artifact_type)
        if artifact:
            logger.info(f"Found artifact directly by job ID and type: {artifact.artifact_id}")
            return artifact

        # If not found, try to get it from the job result
        job = self.get_job(job_id)
        if not job:
            logger.error(f"Job not found: {job_id}")
            return None

        if not job.result:
            logger.error(f"Job has no result: {job_id}")
            return None

        logger.info(f"Job result: {job.result}")

        # Get artifact ID from job result
        artifact_id_key = f"{artifact_type.value}_artifact_id"
        logger.info(f"Looking for key in job result: {artifact_id_key}")

        if artifact_id_key not in job.result:
            logger.error(f"Artifact ID key not found in job result: {artifact_id_key}")
            return None

        artifact_id = job.result[artifact_id_key]
        logger.info(f"Found artifact ID: {artifact_id}")

        # Get artifact
        artifact_path = f"artifacts/{artifact_id}.json"
        logger.info(f"Artifact path: {artifact_path}")

        if not self.storage_client.file_exists(artifact_path):
            logger.error(f"Artifact file not found: {artifact_path}")
            return None

        artifact_data = self.storage_client.read_json(artifact_path)
        logger.info(f"Artifact data: {artifact_data}")

        return Artifact(**artifact_data)

    def _find_artifact_by_job_id_and_type(self, job_id: str, artifact_type: ArtifactType) -> Optional[Artifact]:
        """
        Find artifact by job ID and type by searching through all artifacts.
        This is a fallback method when the artifact ID is not found in the job result.

        Args:
            job_id: Job ID
            artifact_type: Artifact type

        Returns:
            Artifact object or None if not found
        """
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"Searching for artifact with job ID: {job_id}, artifact type: {artifact_type.value}")

        # First, check if there's a job-specific artifact directory
        job_artifacts_dir = f"artifacts/{job_id}"
        if self.storage_client.file_exists(job_artifacts_dir):
            logger.info(f"Found job artifacts directory: {job_artifacts_dir}")

            # Check for domain analysis artifact in the job directory
            domain_analysis_path = f"{job_artifacts_dir}/domain_analysis/domain_analysis.yaml"
            if artifact_type == ArtifactType.DOMAIN_ANALYSIS and self.storage_client.file_exists(domain_analysis_path):
                logger.info(f"Found domain analysis artifact at path: {domain_analysis_path}")
                # Create an artifact object
                artifact = Artifact(
                    artifact_id=str(uuid.uuid4()),  # Generate a new ID
                    job_id=job_id,
                    artifact_type=artifact_type,
                    path=domain_analysis_path,
                    created_at=time.time(),
                    metadata={}
                )

                # Save the artifact metadata
                artifact_path = f"artifacts/{artifact.artifact_id}.json"
                self.storage_client.write_json(artifact_path, artifact.model_dump())

                # Update the job result with the artifact ID
                job = self.get_job(job_id)
                if job:
                    result = job.result or {}
                    result[f"{artifact_type.value}_artifact_id"] = artifact.artifact_id
                    self.update_job(job_id, result=result)

                return artifact

        # List all artifact files
        artifact_files = self.storage_client.list_files("artifacts")
        logger.info(f"Found {len(artifact_files)} artifact files")

        # Search through all artifacts
        for artifact_file in artifact_files:
            try:
                # Skip directories
                if not artifact_file.endswith(".json"):
                    continue

                artifact_data = self.storage_client.read_json(artifact_file)

                # Check for exact match
                if (artifact_data.get("job_id") == job_id and
                    artifact_data.get("artifact_type") == artifact_type.value):
                    logger.info(f"Found matching artifact: {artifact_data}")
                    return Artifact(**artifact_data)

                # Check for path match (for domain analysis artifacts)
                if (artifact_type == ArtifactType.DOMAIN_ANALYSIS and
                    artifact_data.get("path", "").startswith(f"artifacts/{job_id}/domain_analysis")):
                    logger.info(f"Found matching artifact by path: {artifact_data}")
                    return Artifact(**artifact_data)
            except Exception as e:
                logger.error(f"Error reading artifact file {artifact_file}: {e}")
                continue

        logger.error(f"No matching artifact found for job ID: {job_id}, artifact type: {artifact_type.value}")
        return None

    def get_artifact_content(self, artifact: Artifact) -> Any:
        """
        Get artifact content.

        Args:
            artifact: Artifact object

        Returns:
            Artifact content
        """
        if not self.storage_client.file_exists(artifact.path):
            return None

        # Determine content type based on file extension
        _, ext = os.path.splitext(artifact.path)

        if ext.lower() == ".json":
            return self.storage_client.read_json(artifact.path)
        elif ext.lower() in [".yaml", ".yml"]:
            return self.storage_client.read_yaml(artifact.path)
        else:
            return self.storage_client.read_file(artifact.path)
