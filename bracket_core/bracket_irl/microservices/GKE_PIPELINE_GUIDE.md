# Bracket IRL GKE Pipeline Guide

This comprehensive guide details the process for launching and updating the Bracket IRL microservices on Google Kubernetes Engine (GKE). It covers the entire workflow from initial setup to ongoing maintenance and updates.

## Table of Contents

1. [Architecture Overview](#1-architecture-overview)
2. [Prerequisites and Initial Setup](#2-prerequisites-and-initial-setup)
3. [Building and Pushing Docker Images](#3-building-and-pushing-docker-images)
4. [Deploying to GKE for the First Time](#4-deploying-to-gke-for-the-first-time)
5. [Updating Existing Deployments](#5-updating-existing-deployments)
6. [Monitoring and Troubleshooting](#6-monitoring-and-troubleshooting)
7. [SSL Certificate Management](#7-ssl-certificate-management)
8. [Scaling and Performance Optimization](#8-scaling-and-performance-optimization)
9. [Backup and Disaster Recovery](#9-backup-and-disaster-recovery)
10. [CI/CD Integration](#10-cicd-integration)

## 1. Architecture Overview

The Bracket IRL application is built as a microservices architecture deployed on Google Kubernetes Engine (GKE). The system consists of the following components:

### Core Microservices

- **Orchestrator Service**: Coordinates the entire pipeline and provides the main API endpoints
- **Repo Mapper Service**: Analyzes repositories and creates repository maps
- **Domain Analyzer Service**: Analyzes code domains using LLMs
- **File Domain Mapper Service**: Maps files to domains
- **Domain File Repomap Service**: Creates domain-file relationship maps
- **Diagram Generator Service**: Generates diagrams based on the analysis

### Supporting Services

- **Prometheus**: Metrics collection and monitoring
- **Grafana**: Visualization and dashboards for metrics

### Infrastructure Components

- **GKE Cluster**: Managed Kubernetes environment on Google Cloud
- **Google Artifact Registry**: Storage for Docker images
- **Google Cloud Storage**: Storage for persistent data
- **Google Cloud Load Balancer**: Handles ingress traffic (automatically provisioned by GKE)
- **Google Managed Certificates**: SSL/TLS certificate management

### Kubernetes Resources

The deployment uses Kustomize to manage Kubernetes resources with a base configuration and environment-specific overlays:

- **Base**: Common configurations for all environments
- **Overlays/GKE**: GKE-specific configurations
- **Overlays/Local**: Local development configurations

## 2. Prerequisites and Initial Setup

### Required Tools

Before you begin, ensure you have the following tools installed:

```bash
# Check if Google Cloud SDK is installed
gcloud --version

# Install Google Cloud SDK if needed
# Follow instructions at https://cloud.google.com/sdk/docs/install

# Install kubectl
gcloud components install kubectl

# Install Docker
# Follow instructions at https://docs.docker.com/get-docker/

# Install kustomize
curl -s "https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh" | bash
sudo mv kustomize /usr/local/bin/
```

### GCP Project Setup

1. Create a new GCP project or use an existing one:

```bash
# Create a new project
gcloud projects create bracket-irl --name="Bracket IRL"

# Set the project as active
gcloud config set project bracket-irl
```

2. Enable required APIs:

```bash
gcloud services enable container.googleapis.com \
    artifactregistry.googleapis.com \
    compute.googleapis.com \
    certificatemanager.googleapis.com
```

3. Create Artifact Registry repository:

```bash
gcloud artifacts repositories create bracket-irl \
    --repository-format=docker \
    --location=asia-south1 \
    --description="Bracket IRL Docker images"
```

### GKE Cluster Creation

Create a GKE cluster with the following specifications:

```bash
gcloud container clusters create bracket-irl-cluster \
    --zone=asia-south1-a \
    --num-nodes=1 \
    --machine-type=e2-standard-4 \
    --disk-size=100 \
    --enable-autoscaling \
    --min-nodes=1 \
    --max-nodes=3
```

### Configure kubectl to use the GKE cluster:

```bash
gcloud container clusters get-credentials bracket-irl-cluster --zone=asia-south1-a
```

### Create Static IP for Ingress

```bash
gcloud compute addresses create bracket-irl-ip --global
```

Note the IP address for later use:

```bash
gcloud compute addresses describe bracket-irl-ip --global
```

### Set up DNS

Configure your domain (api.bracket.sh) to point to the static IP address created above.

## 3. Building and Pushing Docker Images

### Authentication Setup

Configure Docker to authenticate with Artifact Registry:

```bash
gcloud auth configure-docker asia-south1-docker.pkg.dev
```

### Building Images for Multiple Architectures

To ensure compatibility with GKE nodes (which typically use x86_64 architecture), build multi-architecture images:

```bash
# Navigate to the microservices directory
cd bracket_core/bracket_irl/microservices

# Build and push images for all services
./build_images.sh --platform linux/amd64 --push
```

The `build_images.sh` script handles the following tasks:

1. Builds the common base image
2. Builds each service image using the base image
3. Tags images with the appropriate registry path
4. Pushes images to the Artifact Registry

For manual building and pushing:

```bash
# Build the common base image
docker build -t bracket_irl_common -f common/Dockerfile ./common --platform linux/amd64

# Tag the image for the registry
docker tag bracket_irl_common asia-south1-docker.pkg.dev/bracket-irl/bracket-irl/bracket_irl_common:latest

# Push the image to the registry
docker push asia-south1-docker.pkg.dev/bracket-irl/bracket-irl/bracket_irl_common:latest

# Repeat for each service
docker build -t orchestrator-service -f orchestrator/Dockerfile ./orchestrator --platform linux/amd64
docker tag orchestrator-service asia-south1-docker.pkg.dev/bracket-irl/bracket-irl/orchestrator-service:latest
docker push asia-south1-docker.pkg.dev/bracket-irl/bracket-irl/orchestrator-service:latest

# Continue for other services...
```

### Versioning Strategy

For production deployments, use semantic versioning for images:

```bash
# Tag with version number
docker tag orchestrator-service asia-south1-docker.pkg.dev/bracket-irl/bracket-irl/orchestrator-service:1.0.0

# Push versioned image
docker push asia-south1-docker.pkg.dev/bracket-irl/bracket-irl/orchestrator-service:1.0.0

# Update the latest tag
docker push asia-south1-docker.pkg.dev/bracket-irl/bracket-irl/orchestrator-service:latest
```

## 4. Deploying to GKE for the First Time

### Prepare Configuration Files

1. Create a secrets file from the template:

```bash
cd bracket_core/bracket_irl/microservices/kubernetes/overlays/gke
cp secrets.env.example secrets.env
```

2. Edit the secrets.env file with your actual secrets:

```bash
# Example secrets.env content
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key
OPENROUTER_API_KEY=your_openrouter_api_key
```

### Create Namespace and Apply Configurations

```bash
# Apply the namespace first
kubectl apply -f bracket_core/bracket_irl/microservices/kubernetes/base/namespace.yaml

# Create a secret for pulling images from Artifact Registry
kubectl create secret docker-registry bracket-irl-registry-key \
    --docker-server=asia-south1-docker.pkg.dev \
    --docker-username=_json_key \
    --docker-password="$(cat path/to/service-account-key.json)" \
    --docker-email=<EMAIL> \
    -n bracket-irl

# Apply all configurations using kustomize
kubectl apply -k bracket_core/bracket_irl/microservices/kubernetes/overlays/gke
```

### Verify Deployment

Check that all pods are running:

```bash
kubectl get pods -n bracket-irl
```

Check services:

```bash
kubectl get services -n bracket-irl
```

Check ingress:

```bash
kubectl get ingress -n bracket-irl
```

Check managed certificate:

```bash
kubectl get managedcertificate -n bracket-irl
```

Note: The managed certificate may take up to 30 minutes to provision.

## 5. Updating Existing Deployments

### Update Docker Images

When making changes to the services, rebuild and push the updated images:

```bash
# Build and push updated images
./build_images.sh --platform linux/amd64 --push
```

### Update Kubernetes Deployments

There are several ways to update the deployments:

#### Option 1: Restart Deployments to Pull New Images

```bash
# Restart all deployments to pull the latest images
kubectl rollout restart deployment -n bracket-irl
```

#### Option 2: Update Image Tags in Kustomize

If using versioned images, update the image-patch.yaml file with the new versions:

```yaml
# Edit kubernetes/overlays/gke/image-patch.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: orchestrator-service
  namespace: bracket-irl
spec:
  template:
    spec:
      containers:
      - name: orchestrator-service
        image: asia-south1-docker.pkg.dev/bracket-irl/bracket-irl/orchestrator-service:1.0.1  # Updated version
```

Then apply the changes:

```bash
kubectl apply -k bracket_core/bracket_irl/microservices/kubernetes/overlays/gke
```

#### Option 3: Set Image Directly

```bash
kubectl set image deployment/orchestrator-service orchestrator-service=asia-south1-docker.pkg.dev/bracket-irl/bracket-irl/orchestrator-service:1.0.1 -n bracket-irl
```

### Verify Updates

Check the status of the rollout:

```bash
kubectl rollout status deployment/orchestrator-service -n bracket-irl
```

## 6. Monitoring and Troubleshooting

### Accessing Logs

View logs for a specific pod:

```bash
kubectl logs <pod-name> -n bracket-irl
```

Follow logs in real-time:

```bash
kubectl logs -f <pod-name> -n bracket-irl
```

### Accessing Prometheus and Grafana

Port-forward to access Prometheus:

```bash
kubectl port-forward svc/prometheus 9090:9090 -n bracket-irl
```

Then access Prometheus at http://localhost:9090

Port-forward to access Grafana:

```bash
kubectl port-forward svc/grafana 3000:3000 -n bracket-irl
```

Then access Grafana at http://localhost:3000 (default credentials: admin/admin)

### Common Troubleshooting Commands

Check pod status:

```bash
kubectl describe pod <pod-name> -n bracket-irl
```

Check events:

```bash
kubectl get events -n bracket-irl
```

Check ingress status:

```bash
kubectl describe ingress bracket-irl-ingress -n bracket-irl
```

Check certificate status:

```bash
kubectl describe managedcertificate bracket-irl-cert -n bracket-irl
```

### Debugging Pod Issues

If a pod is in CrashLoopBackOff or Error state:

```bash
# Get detailed information about the pod
kubectl describe pod <pod-name> -n bracket-irl

# Check logs
kubectl logs <pod-name> -n bracket-irl

# Execute commands inside the pod (if running)
kubectl exec -it <pod-name> -n bracket-irl -- /bin/bash
```

## 7. SSL Certificate Management

### Managed Certificate Configuration

The GKE deployment uses Google Managed Certificates for SSL/TLS:

```yaml
# kubernetes/overlays/gke/certificate.yaml
apiVersion: networking.gke.io/v1
kind: ManagedCertificate
metadata:
  name: bracket-irl-cert
  namespace: bracket-irl
spec:
  domains:
    - api.bracket.sh
```

### Certificate Status Monitoring

Check the status of the managed certificate:

```bash
kubectl get managedcertificate -n bracket-irl
```

The certificate goes through several states:
- `Provisioning`: Certificate is being provisioned
- `Active`: Certificate is active and ready
- `FailedNotVisible`: Domain is not visible to Google's certificate authority

### Certificate Renewal

Google Managed Certificates are automatically renewed before expiration.

### Troubleshooting Certificate Issues

If the certificate remains in the `Provisioning` state for more than 30 minutes:

1. Verify that the DNS is correctly configured to point to the ingress IP
2. Check that the domain is accessible from the internet
3. Ensure the ingress is properly configured to use the certificate

```bash
# Check ingress configuration
kubectl describe ingress bracket-irl-ingress -n bracket-irl

# Check certificate events
kubectl describe managedcertificate bracket-irl-cert -n bracket-irl
```

## 8. Scaling and Performance Optimization

### Horizontal Pod Autoscaling

The deployment includes Horizontal Pod Autoscalers (HPAs) for each service:

```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: orchestrator-service-hpa
  namespace: bracket-irl
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: orchestrator-service
  minReplicas: 1
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 80
```

### Manual Scaling

To manually scale a deployment:

```bash
kubectl scale deployment orchestrator-service --replicas=3 -n bracket-irl
```

### Cluster Autoscaling

The GKE cluster is configured with node autoscaling (1-3 nodes). To modify:

```bash
gcloud container clusters update bracket-irl-cluster \
    --enable-autoscaling \
    --min-nodes=1 \
    --max-nodes=5 \
    --zone=asia-south1-a
```

### Resource Requests and Limits

Optimize resource allocation by adjusting requests and limits in the deployment files:

```yaml
resources:
  requests:
    cpu: "100m"
    memory: "256Mi"
  limits:
    cpu: "500m"
    memory: "512Mi"
```

## 9. Backup and Disaster Recovery

### Data Backup Strategy

1. **Persistent Volume Data**:
   - Use scheduled snapshots of persistent volumes
   - Implement regular backups to Google Cloud Storage

```bash
# Create a snapshot of a persistent disk
gcloud compute disks snapshot <disk-name> --snapshot-names=<snapshot-name> --zone=asia-south1-a
```

2. **Configuration Backup**:
   - Store Kubernetes manifests in version control
   - Regularly export and backup Kubernetes resources

```bash
# Export all resources in the namespace
kubectl get all -n bracket-irl -o yaml > bracket-irl-backup.yaml
```

### Disaster Recovery Plan

1. **Cluster Recreation**:
   - Document the cluster creation process
   - Automate cluster provisioning with Terraform or similar tools

2. **Application Restoration**:
   - Apply backed-up Kubernetes manifests
   - Restore persistent volume data from snapshots

```bash
# Create a new disk from a snapshot
gcloud compute disks create <new-disk-name> --source-snapshot=<snapshot-name> --zone=asia-south1-a

# Apply backed-up resources
kubectl apply -f bracket-irl-backup.yaml
```

3. **Testing Recovery**:
   - Regularly test the disaster recovery process
   - Document recovery time objectives (RTO) and recovery point objectives (RPO)

## 10. CI/CD Integration

### Continuous Integration Pipeline

Implement a CI pipeline using GitHub Actions or similar:

```yaml
# .github/workflows/ci.yml
name: CI Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.10'
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install pytest
      - name: Run tests
        run: pytest

  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1
      - name: Login to Google Artifact Registry
        uses: docker/login-action@v1
        with:
          registry: asia-south1-docker.pkg.dev
          username: _json_key
          password: ${{ secrets.GCP_SA_KEY }}
      - name: Build and push
        uses: docker/build-push-action@v2
        with:
          context: ./bracket_core/bracket_irl/microservices
          file: ./bracket_core/bracket_irl/microservices/Dockerfile
          platforms: linux/amd64
          push: true
          tags: asia-south1-docker.pkg.dev/bracket-irl/bracket-irl/orchestrator-service:latest
```

### Continuous Deployment Pipeline

Implement a CD pipeline for automatic deployment:

```yaml
# .github/workflows/cd.yml
name: CD Pipeline

on:
  workflow_run:
    workflows: ["CI Pipeline"]
    branches: [main]
    types:
      - completed

jobs:
  deploy:
    runs-on: ubuntu-latest
    if: ${{ github.event.workflow_run.conclusion == 'success' }}
    steps:
      - uses: actions/checkout@v2
      - name: Set up kubectl
        uses: azure/setup-kubectl@v1
      - name: Configure GKE credentials
        uses: google-github-actions/get-gke-credentials@v0.2.1
        with:
          cluster_name: bracket-irl-cluster
          location: asia-south1-a
          credentials: ${{ secrets.GCP_SA_KEY }}
      - name: Deploy to GKE
        run: |
          kubectl apply -k bracket_core/bracket_irl/microservices/kubernetes/overlays/gke
          kubectl rollout restart deployment -n bracket-irl
      - name: Verify deployment
        run: |
          kubectl rollout status deployment/orchestrator-service -n bracket-irl
```

### Automated Testing

Implement automated testing as part of the CI/CD pipeline:

1. **Unit Tests**: Test individual components
2. **Integration Tests**: Test interactions between services
3. **End-to-End Tests**: Test the entire system

### Rollback Strategy

Implement an automated rollback strategy in case of deployment failures:

```yaml
- name: Deploy with rollback
  run: |
    kubectl apply -k bracket_core/bracket_irl/microservices/kubernetes/overlays/gke
    if ! kubectl rollout status deployment/orchestrator-service -n bracket-irl --timeout=5m; then
      echo "Deployment failed, rolling back..."
      kubectl rollout undo deployment/orchestrator-service -n bracket-irl
      exit 1
    fi
```
