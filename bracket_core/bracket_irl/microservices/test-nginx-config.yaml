apiVersion: v1
kind: ConfigMap
metadata:
  name: test-nginx-config
  namespace: bracket-irl
data:
  default.conf: |
    server {
      listen 80;
      server_name _;

      # Root path handler
      location = / {
        return 200 'Test Nginx Server is working!';
        add_header Content-Type text/plain;
      }

      # Test endpoint
      location = /test {
        return 200 'Test endpoint is working!';
        add_header Content-Type text/plain;
      }
    }
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: test-nginx
  namespace: bracket-irl
spec:
  replicas: 1
  selector:
    matchLabels:
      app: test-nginx
  template:
    metadata:
      labels:
        app: test-nginx
    spec:
      containers:
      - name: nginx
        image: nginx:latest
        ports:
        - containerPort: 80
        volumeMounts:
        - name: nginx-config
          mountPath: /etc/nginx/conf.d
      volumes:
      - name: nginx-config
        configMap:
          name: test-nginx-config
---
apiVersion: v1
kind: Service
metadata:
  name: test-nginx-service
  namespace: bracket-irl
spec:
  selector:
    app: test-nginx
  ports:
  - port: 80
    targetPort: 80
  type: ClusterIP
