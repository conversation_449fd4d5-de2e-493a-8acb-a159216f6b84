# File-Domain Mapper Service

This microservice is responsible for mapping files to domains based on repository maps and domain analysis. It uses LLMs to classify files into the most appropriate domains.

## Features

- Map files to domains based on repository maps and domain analysis
- Process files in batches for improved performance
- Configurable LLM models (OpenAI, OpenRouter)
- REST API for integration with other services
- Background job processing with status tracking

## API Endpoints

- `POST /api/v1/map`: Map files to domains
- `GET /api/v1/status/{job_id}`: Get job status
- `GET /api/v1/artifacts/{job_id}`: Get job artifacts
- `GET /api/v1/health`: Health check

## Configuration

Configuration can be provided via environment variables or a configuration file:

```yaml
service_name: file-domain-mapper-service
host: 0.0.0.0
port: 8002
log_level: INFO
storage_type: local
storage_path: ./data
max_concurrent_tasks: 10
```

## Usage

### Docker

```bash
docker build -t file-domain-mapper-service .
docker run -p 8002:8002 file-domain-mapper-service
```

### Local Development

```bash
pip install -e .
uvicorn src.main:app --reload
```
