FROM bracket-irl/bracket_irl_common:latest

WORKDIR /app

# Install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY src/ /app/src/
COPY bracket_file_domain_mapper_batched.py /app/bracket_irl/bracket_file_domain_mapper_batched.py

# Create a symbolic link to make imports work
RUN mkdir -p /app/bracket_irl/microservices
RUN ln -s /app/bracket_irl_common /app/bracket_irl/microservices/bracket_irl_common

# Create the llm module directory and add necessary files
RUN mkdir -p /app/llm
RUN echo '"""LLM client module."""' > /app/llm/__init__.py
RUN echo '"""LLM client factory."""\n\ndef get_openrouter_client(*args, **kwargs):\n    return {}\n\ndef get_claude_client(*args, **kwargs):\n    return {}\n' > /app/llm/get_client.py
RUN echo '"""API key management."""\n\nimport os\n\ndef get_openai_api_key(provided_key=None):\n    return provided_key or os.environ.get("OPENAI_API_KEY", "********************************************************************************************************************************************************************")\n\ndef get_openrouter_api_key(provided_key=None):\n    return provided_key or os.environ.get("OPENROUTER_API_KEY", "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")\n\ndef get_anthropic_api_key(provided_key=None):\n    return provided_key or os.environ.get("ANTHROPIC_API_KEY", "")\n' > /app/llm/api_keys.py

# Set PYTHONPATH to include both the app directory and the parent directory
ENV PYTHONPATH=/app:/

# Set environment variables
ENV SERVICE_NAME=file-domain-mapper-service
ENV HOST=0.0.0.0
ENV PORT=8003

# Expose port
EXPOSE 8003

# Create data directory
RUN mkdir -p /app/data/jobs /app/data/artifacts

# Run the application
CMD ["uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8003"]
