"""
API tests for the File-Domain Mapper Service.
"""

import os
import json
import pytest
from fastapi.testclient import TestClient

from src.main import app

client = TestClient(app)

def test_health_check():
    """Test health check endpoint."""
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json()["status"] == "ok"

# Add more tests for API endpoints
