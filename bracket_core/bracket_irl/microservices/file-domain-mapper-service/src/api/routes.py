"""
API routes for the File-Domain Mapper Service.
"""

import os
import time
from typing import Dict, Any, Optional
from fastapi import <PERSON>Rout<PERSON>, BackgroundTasks, HTTPException, Depends, Path, Query

from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.models import FileDomainMapperRequest, Job, JobStatus, Artifact, ArtifactType
from pydantic import BaseModel, Field
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.storage import StorageClient, get_storage_client

from src.api.models import MapFilesToDomainsResponse, JobStatusResponse, ArtifactResponse, ErrorResponse
from src.core.config import get_config
from src.services.job_service import JobService
from src.services.mapper import FileDomainMapperService

# Extended request model with optional job_id
class FileDomainMapperRequestWithJobId(FileDomainMapperRequest):
    """Extended file domain mapper request with optional job_id."""
    job_id: Optional[str] = Field(None, description="Optional job ID to use instead of creating a new one")

# Create router
router = APIRouter(tags=["File-Domain Mapper"])

# Get dependencies
def get_job_service() -> JobService:
    """Get job service."""
    config = get_config()
    storage_client = get_storage_client(
        storage_type=config.storage_type,
        base_path=config.storage_path
    )
    return JobService(storage_client=storage_client)

def get_mapper_service() -> FileDomainMapperService:
    """Get file-domain mapper service."""
    config = get_config()
    storage_client = get_storage_client(
        storage_type=config.storage_type,
        base_path=config.storage_path
    )
    return FileDomainMapperService(
        storage_client=storage_client,
        max_concurrent_tasks=config.max_concurrent_tasks
    )

@router.post(
    "/map",
    response_model=MapFilesToDomainsResponse,
    responses={
        400: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def map_files_to_domains(
    request: FileDomainMapperRequestWithJobId,
    background_tasks: BackgroundTasks,
    job_service: JobService = Depends(get_job_service),
    mapper_service: FileDomainMapperService = Depends(get_mapper_service)
):
    """
    Map files to domains.

    Args:
        request: File-domain mapper request with optional job_id
        background_tasks: Background tasks
        job_service: Job service
        mapper_service: File-domain mapper service

    Returns:
        Job ID and message
    """
    try:
        # Validate repository map path
        repomap_path = request.repomap_path
        if not repomap_path.startswith('/'):
            repomap_path = f"/app/data/{repomap_path}"

        if not os.path.exists(repomap_path):
            raise HTTPException(
                status_code=400,
                detail=f"Repository map not found: {request.repomap_path} (full path: {repomap_path})"
            )

        # Validate domain analysis path
        domain_analysis_path = request.domain_analysis_path
        if not domain_analysis_path.startswith('/'):
            domain_analysis_path = f"/app/data/{domain_analysis_path}"

        if not os.path.exists(domain_analysis_path):
            raise HTTPException(
                status_code=400,
                detail=f"Domain analysis not found: {request.domain_analysis_path} (full path: {domain_analysis_path})"
            )

        # Use provided job_id or create a new job
        job_id = request.job_id
        if job_id:
            # Check if job exists
            job = job_service.get_job(job_id)
            if not job:
                # Create job with the specified ID
                job = Job(
                    job_id=job_id,
                    status=JobStatus.PENDING,
                    created_at=time.time(),
                    updated_at=time.time(),
                    progress=0.0,
                    message="Job created with provided ID"
                )
                # Save job
                job_service.storage_client.write_json(f"jobs/{job.job_id}.json", job.model_dump())
            else:
                # Update existing job
                job.status = JobStatus.PENDING
                job.updated_at = time.time()
                job.progress = 0.0
                job.message = "Job restarted with provided ID"
                job_service.update_job(job)
        else:
            # Create a new job
            job = job_service.create_job()
            job_id = job.job_id

        # Start background task
        background_tasks.add_task(
            mapper_service.map_files_to_domains,
            job_id=job_id,
            repomap_path=request.repomap_path,
            domain_analysis_path=request.domain_analysis_path,
            api_key=None,  # API key should be provided via environment variable
            model=request.model,
            max_requests_per_minute=50,
            max_tokens_per_minute=100000,
            use_openrouter=request.use_openrouter,
            max_files_per_batch=request.max_files_per_batch
        )

        return MapFilesToDomainsResponse(
            job_id=job_id,
            message="File-domain mapping started"
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start file-domain mapping: {str(e)}"
        )

@router.get(
    "/status/{job_id}",
    response_model=JobStatusResponse,
    responses={
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def get_job_status(
    job_id: str = Path(..., description="Job ID"),
    job_service: JobService = Depends(get_job_service)
):
    """
    Get job status.

    Args:
        job_id: Job ID
        job_service: Job service

    Returns:
        Job details
    """
    try:
        # Get job
        job = job_service.get_job(job_id)
        if not job:
            raise HTTPException(
                status_code=404,
                detail=f"Job not found: {job_id}"
            )

        return JobStatusResponse(job=job)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get job status: {str(e)}"
        )

@router.get(
    "/artifacts/{job_id}/{artifact_type}",
    response_model=ArtifactResponse,
    responses={
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def get_artifact(
    job_id: str = Path(..., description="Job ID"),
    artifact_type: ArtifactType = Path(..., description="Artifact type"),
    include_content: bool = Query(False, description="Include artifact content"),
    job_service: JobService = Depends(get_job_service)
):
    """
    Get artifact.

    Args:
        job_id: Job ID
        artifact_type: Artifact type
        include_content: Include artifact content
        job_service: Job service

    Returns:
        Artifact details and optionally content
    """
    try:
        # Get job
        job = job_service.get_job(job_id)
        if not job:
            raise HTTPException(
                status_code=404,
                detail=f"Job not found: {job_id}"
            )

        # Get artifact
        artifact = job_service.get_artifact(job_id, artifact_type)
        if not artifact:
            raise HTTPException(
                status_code=404,
                detail=f"Artifact not found for job: {job_id}"
            )

        # Get artifact content if requested
        content = None
        if include_content:
            content = job_service.get_artifact_content(artifact)

        return ArtifactResponse(
            artifact=artifact,
            content=content
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get artifact: {str(e)}"
        )
