"""
API models for the File-Domain Mapper Service.
"""

from typing import Dict, List, Any, Optional, Union
from pydantic import BaseModel, Field

from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.models import Job, Artifact

class MapFilesToDomainsResponse(BaseModel):
    """Response model for map files to domains endpoint."""
    job_id: str = Field(..., description="Job ID")
    message: str = Field(..., description="Response message")

class JobStatusResponse(BaseModel):
    """Response model for job status endpoint."""
    job: Job = Field(..., description="Job details")

class ArtifactResponse(BaseModel):
    """Response model for artifact endpoint."""
    artifact: Artifact = Field(..., description="Artifact details")
    content: Optional[Any] = Field(None, description="Artifact content")

class ErrorResponse(BaseModel):
    """Error response model."""
    detail: str = Field(..., description="Error details")
