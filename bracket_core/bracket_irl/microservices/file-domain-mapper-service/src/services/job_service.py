"""
Job service for the File-Domain Mapper Service.
"""

import os
import time
import json
import uuid
from typing import Dict, List, Any, Optional, Union

from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.models import Job, JobStatus, Artifact, ArtifactType
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.storage import StorageClient

class JobService:
    """Service for managing jobs."""

    def __init__(self, storage_client: StorageClient):
        """
        Initialize job service.

        Args:
            storage_client: Storage client
        """
        self.storage_client = storage_client

    def create_job(self) -> Job:
        """
        Create a new job.

        Returns:
            Job object
        """
        job = Job(
            job_id=str(uuid.uuid4()),
            status=JobStatus.PENDING,
            created_at=time.time(),
            updated_at=time.time(),
            progress=0.0,
            message="Job created"
        )

        # Save job
        self.storage_client.write_json(f"jobs/{job.job_id}.json", job.model_dump())

        return job

    def get_job(self, job_id: str) -> Optional[Job]:
        """
        Get job by ID.

        Args:
            job_id: Job ID

        Returns:
            Job object or None if not found
        """
        job_path = f"jobs/{job_id}.json"

        if not self.storage_client.file_exists(job_path):
            return None

        job_data = self.storage_client.read_json(job_path)
        return Job(**job_data)

    def update_job(
        self,
        job_id: str,
        status: Optional[JobStatus] = None,
        progress: Optional[float] = None,
        message: Optional[str] = None,
        error: Optional[str] = None,
        result: Optional[Dict[str, Any]] = None
    ) -> Optional[Job]:
        """
        Update job.

        Args:
            job_id: Job ID
            status: New job status
            progress: New job progress
            message: New job message
            error: New job error
            result: New job result

        Returns:
            Updated job object or None if not found
        """
        job = self.get_job(job_id)
        if not job:
            return None

        # Update job
        if status is not None:
            job.status = status

        if progress is not None:
            job.progress = progress

        if message is not None:
            job.message = message

        if error is not None:
            job.error = error

        if result is not None:
            job.result = result

        job.updated_at = time.time()

        # Save job
        self.storage_client.write_json(f"jobs/{job.job_id}.json", job.model_dump())

        return job

    def create_artifact(
        self,
        job_id: str,
        artifact_type: ArtifactType,
        path: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Artifact:
        """
        Create artifact.

        Args:
            job_id: Job ID
            artifact_type: Artifact type
            path: Artifact path
            metadata: Artifact metadata

        Returns:
            Artifact object
        """
        artifact = Artifact(
            artifact_id=str(uuid.uuid4()),
            job_id=job_id,
            artifact_type=artifact_type,
            path=path,
            created_at=time.time(),
            metadata=metadata or {}
        )

        # Save artifact
        self.storage_client.write_json(f"artifacts/{artifact.artifact_id}.json", artifact.model_dump())

        return artifact

    def get_artifact(self, job_id: str, artifact_type: ArtifactType) -> Optional[Artifact]:
        """
        Get artifact by job ID and type.

        Args:
            job_id: Job ID
            artifact_type: Artifact type

        Returns:
            Artifact object or None if not found
        """
        # Get job
        job = self.get_job(job_id)
        if not job or not job.result:
            return None

        # Get artifact ID from job result
        artifact_id_key = f"{artifact_type.value}_artifact_id"

        # Special case for backward compatibility: if FILE_DOMAIN_MAPPING is requested but not found,
        # try DOMAIN_MAPPING instead (and vice versa)
        if artifact_id_key not in job.result:
            if artifact_type == ArtifactType.DOMAIN_MAPPING:
                # Try FILE_DOMAIN_MAPPING instead
                alt_key = "file_domain_mapping_artifact_id"
                if alt_key in job.result:
                    artifact_id = job.result[alt_key]
                    artifact_path = f"artifacts/{artifact_id}.json"
                    if self.storage_client.file_exists(artifact_path):
                        artifact_data = self.storage_client.read_json(artifact_path)
                        return Artifact(**artifact_data)
            elif artifact_type == ArtifactType.FILE_DOMAIN_MAPPING:
                # Try DOMAIN_MAPPING instead
                alt_key = "domain_mapping_artifact_id"
                if alt_key in job.result:
                    artifact_id = job.result[alt_key]
                    artifact_path = f"artifacts/{artifact_id}.json"
                    if self.storage_client.file_exists(artifact_path):
                        artifact_data = self.storage_client.read_json(artifact_path)
                        return Artifact(**artifact_data)
            return None

        artifact_id = job.result[artifact_id_key]

        # Get artifact
        artifact_path = f"artifacts/{artifact_id}.json"

        if not self.storage_client.file_exists(artifact_path):
            return None

        artifact_data = self.storage_client.read_json(artifact_path)
        return Artifact(**artifact_data)

    def get_artifact_content(self, artifact: Artifact) -> Any:
        """
        Get artifact content.

        Args:
            artifact: Artifact object

        Returns:
            Artifact content
        """
        if not self.storage_client.file_exists(artifact.path):
            return None

        # Determine content type based on file extension
        _, ext = os.path.splitext(artifact.path)

        if ext.lower() == ".json":
            return self.storage_client.read_json(artifact.path)
        elif ext.lower() in [".yaml", ".yml"]:
            return self.storage_client.read_yaml(artifact.path)
        else:
            return self.storage_client.read_file(artifact.path)
