"""
File-domain mapper service for the File-Domain Mapper Service.
"""

import os
import asyncio
import logging
import json
import yaml
from typing import Dict, List, Any, Optional, Tuple

from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.models import Job, JobStatus, Artifact, ArtifactType
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.storage import StorageClient
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.clients.llm_client import get_llm_client
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.utils.token_counter import num_tokens_from_string

from bracket_irl.bracket_file_domain_mapper_batched import FileDomainMapper

from src.services.job_service import JobService

# Initialize logger
logger = logging.getLogger(__name__)

class FileDomainMapperService:
    """Service for mapping files to domains."""

    def __init__(self, storage_client: StorageClient, max_concurrent_tasks: int = 10):
        """
        Initialize file-domain mapper service.

        Args:
            storage_client: Storage client
            max_concurrent_tasks: Maximum concurrent tasks
        """
        self.storage_client = storage_client
        self.max_concurrent_tasks = max_concurrent_tasks
        self.job_service = JobService(storage_client=storage_client)

    async def map_files_to_domains(
        self,
        job_id: str,
        repomap_path: str,
        domain_analysis_path: str,
        api_key: Optional[str] = None,
        model: str = "gpt-4o-mini",
        max_requests_per_minute: float = 50,
        max_tokens_per_minute: float = 100000,
        use_openrouter: bool = False,
        openrouter_base_url: str = "https://openrouter.ai/api/v1",
        max_files_per_batch: int = 50,
    ) -> None:
        """
        Map files to domains.

        Args:
            job_id: Job ID
            repomap_path: Path to repository map
            domain_analysis_path: Path to domain analysis
            api_key: API key for LLM
            model: Model to use
            max_requests_per_minute: Maximum requests per minute
            max_tokens_per_minute: Maximum tokens per minute
            use_openrouter: Whether to use OpenRouter
            openrouter_base_url: OpenRouter base URL
            max_files_per_batch: Maximum files per batch
        """
        try:
            # Update job status
            self.job_service.update_job(
                job_id=job_id,
                status=JobStatus.RUNNING,
                message="Mapping files to domains"
            )

            # Set output paths - only use YAML
            output_dir = f"artifacts/{job_id}/file_domain_mapping"
            output_path = f"{output_dir}/file_domain_mapping.yaml"  # Use YAML as the primary output format

            # Create directories by writing empty files (this will create parent directories)
            self.storage_client.write_text(f"{output_dir}/.placeholder", "")
            self.storage_client.write_text(f"artifacts/{job_id}/.placeholder", "")

            # Get full paths - use try/except to handle different storage client implementations
            try:
                # Try to use _get_full_path if available (LocalStorageClient)
                if hasattr(self.storage_client, '_get_full_path'):
                    full_repomap_path = self.storage_client._get_full_path(repomap_path)
                    full_domain_analysis_path = self.storage_client._get_full_path(domain_analysis_path)
                    full_output_path = self.storage_client._get_full_path(output_path)
                # Otherwise, assume paths are already correct
                else:
                    full_repomap_path = repomap_path
                    full_domain_analysis_path = domain_analysis_path
                    full_output_path = output_path
            except Exception as e:
                logger.warning(f"Error getting full paths: {e}")
                # Fallback to direct paths
                full_repomap_path = repomap_path
                full_domain_analysis_path = domain_analysis_path
                full_output_path = output_path

            # Update job status
            self.job_service.update_job(
                job_id=job_id,
                progress=0.1,
                message="Running file-domain mapping"
            )

            # Run file-domain mapping
            success = await FileDomainMapper.map_files_to_leaf_domains(
                repomap_path=full_repomap_path,
                domain_analysis_yaml_path=full_domain_analysis_path,
                output_path=full_output_path,  # Use YAML path for output
                api_key=api_key,
                model=model,
                max_requests_per_minute=max_requests_per_minute,
                max_tokens_per_minute=max_tokens_per_minute,
                use_openrouter=use_openrouter,
                openrouter_base_url=openrouter_base_url,
                max_files_per_batch=max_files_per_batch
            )

            if not success:
                raise Exception("File-domain mapping failed")

            logger.info(f"File domain mapping saved to YAML: {full_output_path}")

            # Create artifact using the YAML file path
            file_domain_mapping_artifact = self.job_service.create_artifact(
                job_id=job_id,
                artifact_type=ArtifactType.DOMAIN_MAPPING,  # Use DOMAIN_MAPPING instead of FILE_DOMAIN_MAPPING to match orchestrator expectations
                path=output_path,
                metadata={
                    "model": model,
                    "use_openrouter": use_openrouter,
                    "max_files_per_batch": max_files_per_batch,
                    "format": "yaml"  # Explicitly indicate this is a YAML file
                }
            )

            # Update job status
            self.job_service.update_job(
                job_id=job_id,
                status=JobStatus.COMPLETED,
                progress=1.0,
                message="File-domain mapping completed",
                result={
                    "domain_mapping_artifact_id": file_domain_mapping_artifact.artifact_id  # Use domain_mapping_artifact_id to match orchestrator expectations
                }
            )
        except Exception as e:
            # Update job status
            self.job_service.update_job(
                job_id=job_id,
                status=JobStatus.FAILED,
                error=str(e),
                message="File-domain mapping failed"
            )
