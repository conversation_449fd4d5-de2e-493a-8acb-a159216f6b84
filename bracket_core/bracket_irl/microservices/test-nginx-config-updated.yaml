apiVersion: v1
kind: ConfigMap
metadata:
  name: test-nginx-config
  namespace: bracket-irl
data:
  default.conf: |
    server {
      listen 80;
      server_name _;

      # Root path handler
      location = / {
        return 200 'Test Nginx Server is working!';
        add_header Content-Type text/plain;
      }

      # Test endpoint
      location = /test {
        return 200 'Test endpoint is working!';
        add_header Content-Type text/plain;
      }

      # Handle path prefix
      location /test-nginx/ {
        proxy_pass http://localhost/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
      }

      # Special case for test-nginx/test
      location = /test-nginx/test {
        proxy_pass http://localhost/test;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
      }
    }
