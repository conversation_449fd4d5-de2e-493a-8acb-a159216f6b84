#!/bin/bash
# Run copy_implementation.sh for each service
cd repo-mapper-service && ./copy_implementation.sh && cd ..
# Add similar lines for other services that use implementation files

# Then build and start the services
docker compose build
docker compose up -d

# Start Bracket IRL microservices

# Set environment variables
export REPO_DIR=${REPO_DIR:-/tmp/repo}
export OPENAI_API_KEY=${OPENAI_API_KEY:-""}
export ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-""}
export OPENROUTER_API_KEY=${OPENROUTER_API_KEY:-""}

# Create data directory
mkdir -p data

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker compose > /dev/null 2>&1; then
    echo "Docker Compose is not installed. Please install Docker Compose and try again."
    exit 1
fi

# Check if required environment variables are set
if [ -z "$OPENAI_API_KEY" ]; then
    echo "Warning: OPENAI_API_KEY is not set. Some services may not work properly."
fi

# Start services
echo "Starting Bracket IRL microservices..."
docker compose up -d

# Wait for services to start
echo "Waiting for services to start..."
sleep 5

# Check if services are running
echo "Checking if services are running..."
docker compose ps

# Print URLs
echo ""
echo "Services are running at:"
echo "- Orchestrator Service: http://localhost:8000"
echo "- Repository Mapper Service: http://localhost:8001"
echo "- Domain Analyzer Service: http://localhost:8002"
echo "- File-Domain Mapper Service: http://localhost:8003"
echo "- Domain-File Repomap Service: http://localhost:8004"
echo "- Diagram Generator Service: http://localhost:8005"
echo "- Prometheus: http://localhost:9090"
echo "- Grafana: http://localhost:3000"
echo ""
echo "To stop the services, run: docker compose down"
