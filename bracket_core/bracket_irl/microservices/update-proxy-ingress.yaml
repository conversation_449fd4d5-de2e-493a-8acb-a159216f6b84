apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: proxy-ingress
  namespace: bracket-irl
  annotations:
    kubernetes.io/ingress.class: "gce"
    kubernetes.io/ingress.global-static-ip-name: "bracket-irl-ip"
    networking.gke.io/managed-certificates: "bracket-irl-cert"
    kubernetes.io/ingress.allow-http: "true"
spec:
  rules:
  - http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: proxy-service
            port:
              number: 80
