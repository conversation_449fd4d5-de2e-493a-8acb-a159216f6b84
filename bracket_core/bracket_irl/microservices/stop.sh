#!/bin/bash

# Stop Bracket IRL microservices

# Check if <PERSON><PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker compose > /dev/null 2>&1; then
    echo "Docker Compose is not installed. Please install Docker Compose and try again."
    exit 1
fi

# Stop services
echo "Stopping Bracket IRL microservices..."
docker compose -f docker-compose.yml -f docker-compose.override.yml down

echo "Services stopped."
