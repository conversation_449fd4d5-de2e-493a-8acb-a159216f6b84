# Step-by-Step Guide to Testing Bracket IRL Microservices

This comprehensive guide will walk you through the entire process of setting up and testing the Bracket IRL microservices, from initial setup to final verification. Each step includes monitoring points to help identify and troubleshoot potential issues.

## Prerequisites

Before starting, ensure you have the following installed:

- Python 3.10+
- <PERSON><PERSON> and <PERSON><PERSON> Compose
- <PERSON><PERSON>be (for Kubernetes testing)
- kubectl
- Git

## Step 1: C<PERSON> and Set Up the Repository

1. **Clone the repository**:
   ```bash
   git clone https://github.com/your-org/bracket.git
   cd bracket/bracket_prod
   ```

2. **Set up environment variables**:
   ```bash
   export OPENAI_API_KEY=your-openai-api-key
   ```

   **Monitoring Point**: Verify the API key is correctly set:
   ```bash
   echo $OPENAI_API_KEY
   ```

## Step 2: Prepare the Testing Environment

1. **Create a virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. **Install dependencies**:
   ```bash
   cd bracket_core/bracket_irl/microservices
   pip install -r requirements.txt
   ```

   **Monitoring Point**: Verify all dependencies installed correctly:
   ```bash
   pip list
   ```

3. **Make test scripts executable**:
   ```bash
   chmod +x tests/e2e/*.py tests/e2e/scripts/*.py
   ```

   **Monitoring Point**: Verify scripts are executable:
   ```bash
   ls -la tests/e2e/*.py tests/e2e/scripts/*.py
   ```

## Step 3: Create a Test Repository

1. **Run the test repository creation script**:
   ```bash
   mkdir -p tests/e2e/results
   python tests/e2e/scripts/create_test_repo.py --output tests/e2e/results/test_repo
   ```

   **Monitoring Point**: Verify the test repository was created:
   ```bash
   ls -la tests/e2e/results/test_repo
   ```

   Expected output should show directories like `src`, `tests`, and files like `README.md` and `requirements.txt`.

## Step 4: Test Docker Compose Setup

1. **Build the Docker images**:
   ```bash
   docker compose build
   ```

   **Monitoring Point**: Verify all images built successfully:
   ```bash
   docker images | grep bracket-irl
   ```

   You should see images for all services: bracket_irl_common, repo-mapper-service, domain-analyzer-service, etc.

2. **Run the Docker Compose test**:
   ```bash
   python tests/e2e/test_docker_compose.py \
     --compose-file docker-compose.yml \
     --repo-dir tests/e2e/results/test_repo \
     --api-key $OPENAI_API_KEY \
     --results-dir tests/e2e/results/docker_compose
   ```

   **Monitoring Point**: Check the test logs:
   ```bash
   cat tests/e2e/results/docker_compose/docker_compose_test_results.json
   ```

   Look for `"start_services": true`, `"wait_for_services": true`, and all service health checks showing `true`.

3. **Check for specific Docker Compose issues**:
   ```bash
   cat docker_compose_test.log | grep ERROR
   ```

   If you see errors, check:
   - Container logs: `docker-compose logs [service-name]`
   - Network issues: `docker network ls` and `docker network inspect bracket-network`
   - Volume issues: `docker volume ls` and `docker volume inspect bracket-data`

## Step 5: Test E2E Pipeline

1. **Run the E2E pipeline test with the test repository**:
   ```bash
   python tests/e2e/test_e2e_pipeline.py \
     --repo-dir tests/e2e/results/test_repo \
     --api-key $OPENAI_API_KEY \
     --results-dir tests/e2e/results/e2e_pipeline
   ```

   **Monitoring Point**: Check the test logs:
   ```bash
   cat e2e_test.log
   ```

   Look for successful completion of each stage: repository mapping, domain analysis, etc.

2. **Run the E2E pipeline test with a real repository**:
   ```bash
   python tests/e2e/test_e2e_pipeline.py \
     --repo-dir /path/to/your/real/repository \
     --api-key $OPENAI_API_KEY \
     --results-dir tests/e2e/results/e2e_pipeline_real
   ```

   This allows you to test with a real codebase instead of the generated test repository.

3. **Examine the artifacts**:
   ```bash
   ls -la tests/e2e/results/e2e_pipeline
   ```

   You should see files like `repomap.json`, `domain_analysis.json`, etc.

4. **Check for specific E2E pipeline issues**:
   ```bash
   cat e2e_test.log | grep ERROR
   ```

   If you see errors, check:
   - API responses: Look for HTTP status codes and error messages
   - Service logs: Check Docker logs for the specific service
   - Data flow: Verify each artifact is being correctly passed to the next service

## Step 6: Set Up Minikube for Kubernetes Testing

1. **Start Minikube**:
   ```bash
   minikube start --memory=4096 --cpus=4
   ```

   **Monitoring Point**: Verify Minikube is running:
   ```bash
   minikube status
   ```

   You should see `host: Running`, `kubelet: Running`, `apiserver: Running`, etc.

2. **Enable the Ingress addon**:
   ```bash
   minikube addons enable ingress
   ```

   **Monitoring Point**: Verify the Ingress addon is enabled:
   ```bash
   minikube addons list | grep ingress
   ```

3. **Set up Docker to use Minikube's Docker daemon**:
   ```bash
   eval $(minikube docker-env)
   ```

   **Monitoring Point**: Verify Docker is using Minikube's daemon:
   ```bash
   docker info | grep "Name:"
   ```

   You should see Minikube's hostname.

4. **Build Docker images in Minikube**:
   ```bash
   docker-compose build
   ```

   **Monitoring Point**: Verify images are built in Minikube:
   ```bash
   docker images | grep bracket-irl
   ```

## Step 7: Prepare Kubernetes Manifests

1. **Create secrets file for local deployment**:
   ```bash
   cp kubernetes/overlays/local/secrets.env.example kubernetes/overlays/local/secrets.env
   ```

2. **Edit the secrets file**:
   ```bash
   echo "OPENAI_API_KEY=$OPENAI_API_KEY" > kubernetes/overlays/local/secrets.env
   ```

   **Monitoring Point**: Verify the secrets file:
   ```bash
   cat kubernetes/overlays/local/secrets.env
   ```

## Step 8: Test Kubernetes Deployment

1. **Run the Kubernetes test**:
   ```bash
   python tests/e2e/test_kubernetes.py \
     --k8s-dir kubernetes/overlays/local \
     --repo-dir tests/e2e/results/test_repo \
     --api-key $OPENAI_API_KEY \
     --results-dir tests/e2e/results/kubernetes
   ```

   **Monitoring Point**: Check the test logs:
   ```bash
   cat kubernetes_test.log
   ```

   Look for successful completion of each step: creating namespace, deploying services, etc.

2. **Check for specific Kubernetes issues**:
   ```bash
   cat kubernetes_test.log | grep ERROR
   ```

   If you see errors, check:
   - Pod status: `kubectl get pods -n bracket-irl`
   - Pod logs: `kubectl logs [pod-name] -n bracket-irl`
   - Service status: `kubectl get services -n bracket-irl`
   - PVC status: `kubectl get pvc -n bracket-irl`

## Step 9: Run All Tests Together

1. **Run the master test script**:
   ```bash
   python tests/e2e/run_all_tests.py \
     --api-key $OPENAI_API_KEY \
     --k8s-dir kubernetes/overlays/local \
     --results-dir tests/e2e/results/all_tests
   ```

   **Monitoring Point**: Check the test results:
   ```bash
   cat tests/e2e/results/all_tests/all_tests_results.json
   ```

   All test results should show `true`.

2. **Check for any issues**:
   ```bash
   cat all_tests.log | grep ERROR
   ```

## Step 10: Test Orchestrator E2E Pipeline

1. **Run the orchestrator E2E test with the test repository**:
   ```bash
   cd bracket_core/bracket_irl/microservices
   ./tests/e2e/run_orchestrator_e2e_test.sh
   ```

   This will create a test repository and run the orchestrator-based E2E test.

2. **Run the orchestrator E2E test with a real repository**:
   ```bash
   cd bracket_core/bracket_irl/microservices
   ./tests/e2e/run_orchestrator_e2e_test.sh --repo-path /path/to/your/real/repository
   ```

   This allows you to test with a real codebase instead of the generated test repository.

   **Monitoring Point**: Check the test results:
   ```bash
   cat results/orchestrator_e2e/test_results.json
   ```

   Look for `"success": true` in the results.

## Step 11: Manual Verification

After automated tests pass, perform these manual verification steps:

1. **Start the services with Docker Compose**:
   ```bash
   ./start.sh
   ```

2. **Verify all services are running**:
   ```bash
   docker-compose ps
   ```

   All services should show `Up` status.

3. **Check service health endpoints**:
   ```bash
   curl http://localhost:8000/health
   curl http://localhost:8001/health
   curl http://localhost:8002/health
   curl http://localhost:8003/health
   curl http://localhost:8004/health
   curl http://localhost:8005/health
   ```

   All should return `{"status": "ok", "service": "[service-name]"}`.

4. **Process a repository through the orchestrator**:
   ```bash
   curl -X POST http://localhost:8000/api/v1/repositories \
     -H "Content-Type: application/json" \
     -d '{
       "repo_dir": "/repo",
       "api_key": "'"$OPENAI_API_KEY"'",
       "model": "gpt-4o-mini"
     }'
   ```

   This should return a job ID.

5. **Check job status**:
   ```bash
   curl http://localhost:8000/api/v1/jobs/[job-id]
   ```

   Monitor the job progress until it completes.

6. **Check metrics in Prometheus**:
   Open http://localhost:9090 in your browser and check metrics like:
   - `repo_mapper_requests_total`
   - `domain_analyzer_requests_total`
   - `orchestrator_active_jobs`

7. **Check dashboards in Grafana**:
   Open http://localhost:3000 in your browser and check the Bracket IRL dashboard.

## Step 12: Prepare for GCP/GKE Deployment

1. **Create secrets file for GKE deployment**:
   ```bash
   cp kubernetes/overlays/gke/secrets.env.example kubernetes/overlays/gke/secrets.env
   ```

2. **Edit the secrets file**:
   ```bash
   echo "OPENAI_API_KEY=$OPENAI_API_KEY" > kubernetes/overlays/gke/secrets.env
   ```

3. **Update the domain in the ingress manifest**:
   ```bash
   sed -i 's/bracket-irl.example.com/your-actual-domain.com/g' kubernetes/overlays/gke/ingress.yaml
   ```

4. **Create image patch for GKE**:
   ```bash
   cat > kubernetes/overlays/gke/image-patch.yaml << EOF
   apiVersion: kustomize.config.k8s.io/v1beta1
   kind: Kustomization

   images:
   - name: bracket-irl/repo-mapper-service
     newName: gcr.io/your-project/bracket-irl/repo-mapper-service
     newTag: latest
   - name: bracket-irl/domain-analyzer-service
     newName: gcr.io/your-project/bracket-irl/domain-analyzer-service
     newTag: latest
   - name: bracket-irl/file-domain-mapper-service
     newName: gcr.io/your-project/bracket-irl/file-domain-mapper-service
     newTag: latest
   - name: bracket-irl/domain-file-repomap-service
     newName: gcr.io/your-project/bracket-irl/domain-file-repomap-service
     newTag: latest
   - name: bracket-irl/diagram-generator-service
     newName: gcr.io/your-project/bracket-irl/diagram-generator-service
     newTag: latest
   - name: bracket-irl/orchestrator-service
     newName: gcr.io/your-project/bracket-irl/orchestrator-service
     newTag: latest
   EOF
   ```

5. **Update the kustomization.yaml file**:
   ```bash
   sed -i '/resources:/a \ \ - image-patch.yaml' kubernetes/overlays/gke/kustomization.yaml
   ```

## Step 13: Clean Up

1. **Stop Docker Compose services**:
   ```bash
   docker-compose down
   ```

2. **Stop Minikube**:
   ```bash
   minikube stop
   ```

3. **Deactivate virtual environment**:
   ```bash
   deactivate
   ```

## Troubleshooting Common Issues

### Docker Compose Issues

1. **Services not starting**:
   - Check Docker logs: `docker-compose logs [service-name]`
   - Verify environment variables: `docker-compose config`
   - Check network configuration: `docker network inspect bracket-network`

2. **Services not healthy**:
   - Check health check endpoints: `curl http://localhost:[port]/health`
   - Check service logs for errors: `docker-compose logs [service-name]`
   - Verify resource allocation: `docker stats`

### Kubernetes Issues

1. **Pods not starting**:
   - Check pod status: `kubectl get pods -n bracket-irl`
   - Check pod logs: `kubectl logs [pod-name] -n bracket-irl`
   - Check events: `kubectl get events -n bracket-irl`

2. **Services not accessible**:
   - Check service status: `kubectl get services -n bracket-irl`
   - Check endpoints: `kubectl get endpoints -n bracket-irl`
   - Check ingress: `kubectl get ingress -n bracket-irl`

3. **PVC issues**:
   - Check PVC status: `kubectl get pvc -n bracket-irl`
   - Check PV status: `kubectl get pv`
   - Check storage class: `kubectl get storageclass`

### API Issues

1. **API calls failing**:
   - Check service logs: `docker-compose logs [service-name]` or `kubectl logs [pod-name] -n bracket-irl`
   - Verify API key: `echo $OPENAI_API_KEY`
   - Check request/response: Use tools like Postman or curl with `-v` flag

2. **LLM API rate limits**:
   - Check for rate limit errors in logs
   - Adjust `MAX_CONCURRENT_TASKS` in configuration
   - Implement exponential backoff for retries

## Conclusion

By following this step-by-step guide, you should be able to thoroughly test the Bracket IRL microservices architecture and identify any issues before deploying to GCP/GKE. The monitoring points at each step will help you catch and troubleshoot problems early in the process.

If all tests pass successfully, you can be confident that your microservices are working correctly and are ready for production deployment.
