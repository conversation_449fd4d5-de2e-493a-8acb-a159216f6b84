# Bracket IRL Microservices GCP Deployment Guide

This guide provides step-by-step instructions for deploying the Bracket IRL microservices to Google Cloud Platform (GCP) using Google Kubernetes Engine (GKE).

## Prerequisites

Before you begin, make sure you have the following:

1. A Google Cloud Platform account
2. Google Cloud SDK installed and configured
3. <PERSON><PERSON> installed
4. k<PERSON><PERSON>l installed
5. Access to the Bracket IRL microservices codebase

## Step 1: Set Up GCP Project

1. Create a new GCP project or use an existing one:

```bash
# Create a new project
gcloud projects create bracket-irl --name="Bracket IRL"

# Set the project as the default
gcloud config set project bracket-irl
```

2. Enable the required APIs:

```bash
gcloud services enable container.googleapis.com \
    containerregistry.googleapis.com \
    cloudbuild.googleapis.com \
    artifactregistry.googleapis.com
```

## Step 2: Create a GKE Cluster

1. Create a GKE cluster:

```bash
gcloud container clusters create bracket-irl-cluster \
    --zone us-central1-a \
    --num-nodes 3 \
    --machine-type e2-standard-4 \
    --enable-autoscaling \
    --min-nodes 3 \
    --max-nodes 6
```

2. Get credentials for the cluster:

```bash
gcloud container clusters get-credentials bracket-irl-cluster --zone us-central1-a
```

## Step 3: Set Up Container Registry

1. Create an Artifact Registry repository:

```bash
gcloud artifacts repositories create bracket-irl \
    --repository-format=docker \
    --location=us-central1 \
    --description="Bracket IRL Docker images"
```

2. Configure Docker to use the repository:

```bash
gcloud auth configure-docker us-central1-docker.pkg.dev
```

## Step 4: Build and Push Docker Images

1. Build the Docker images:

```bash
cd bracket_core/bracket_irl/microservices

# Build the common library
docker build -t us-central1-docker.pkg.dev/bracket-irl/bracket-irl/bracket_irl_common:latest ./bracket_irl_common

# Build the services with proper context
docker build -t us-central1-docker.pkg.dev/bracket-irl/bracket-irl/repo-mapper-service:latest ./repo-mapper-service
docker build -t us-central1-docker.pkg.dev/bracket-irl/bracket-irl/domain-analyzer-service:latest ./domain-analyzer-service
docker build -t us-central1-docker.pkg.dev/bracket-irl/bracket-irl/file-domain-mapper-service:latest ./file-domain-mapper-service
docker build -t us-central1-docker.pkg.dev/bracket-irl/bracket-irl/domain-file-repomap-service:latest ./domain-file-repomap-service
docker build -t us-central1-docker.pkg.dev/bracket-irl/bracket-irl/diagram-generator-service:latest ./diagram-generator-service
docker build -t us-central1-docker.pkg.dev/bracket-irl/bracket-irl/orchestrator-service:latest ./orchestrator-service
```

2. Push the Docker images to Artifact Registry:

```bash
docker push us-central1-docker.pkg.dev/bracket-irl/bracket-irl/bracket_irl_common:latest
docker push us-central1-docker.pkg.dev/bracket-irl/bracket-irl/repo-mapper-service:latest
docker push us-central1-docker.pkg.dev/bracket-irl/bracket-irl/domain-analyzer-service:latest
docker push us-central1-docker.pkg.dev/bracket-irl/bracket-irl/file-domain-mapper-service:latest
docker push us-central1-docker.pkg.dev/bracket-irl/bracket-irl/domain-file-repomap-service:latest
docker push us-central1-docker.pkg.dev/bracket-irl/bracket-irl/diagram-generator-service:latest
docker push us-central1-docker.pkg.dev/bracket-irl/bracket-irl/orchestrator-service:latest
```

## Step 5: Update Kubernetes Manifests

1. Update the ConfigMap to include all necessary environment variables:

```bash
cd bracket_core/bracket_irl/microservices/kubernetes/base

# Update the ConfigMap with the current configuration
cat > configmap.yaml << EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: bracket-irl-config
  namespace: bracket-irl
data:
  REPO_DIR: "/repo"
  LOG_LEVEL: "INFO"
  STORAGE_TYPE: "local"
  STORAGE_PATH: "/app/data"
  MAX_CONCURRENT_TASKS: "10"
  PYTHONPATH: "/app:/"
  JOB_TIMEOUT_SECONDS: "3600"

  # Default model configurations
  DEFAULT_MODEL: "gpt-4o-mini"
  DEFAULT_USE_OPENROUTER: "false"
  DEFAULT_USE_CLAUDE: "false"
  DEFAULT_MAX_TOKENS_PER_CHUNK: "500000"
  DEFAULT_DISABLE_PARALLEL: "false"
  DEFAULT_GENERATE_EXPLANATIONS: "true"

  # Rate limiting parameters
  DEFAULT_MAX_REQUESTS_PER_MINUTE: "5000"
  DEFAULT_MAX_TOKENS_PER_MINUTE: "15000000"

  # File format configurations
  FILE_DOMAIN_MAPPER_OUTPUT_FORMAT: "yaml"
  DOMAIN_FILE_REPOMAP_INPUT_FORMAT: "yaml"
EOF
```

2. Update the image references in the Kubernetes manifests:

```bash
cd bracket_core/bracket_irl/microservices/kubernetes/overlays/gke

# Create a patch for the image references
cat > image-patch.yaml << EOF
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

images:
- name: bracket-irl/repo-mapper-service
  newName: us-central1-docker.pkg.dev/bracket-irl/bracket-irl/repo-mapper-service
  newTag: latest
- name: bracket-irl/domain-analyzer-service
  newName: us-central1-docker.pkg.dev/bracket-irl/bracket-irl/domain-analyzer-service
  newTag: latest
- name: bracket-irl/file-domain-mapper-service
  newName: us-central1-docker.pkg.dev/bracket-irl/bracket-irl/file-domain-mapper-service
  newTag: latest
- name: bracket-irl/domain-file-repomap-service
  newName: us-central1-docker.pkg.dev/bracket-irl/bracket-irl/domain-file-repomap-service
  newTag: latest
- name: bracket-irl/diagram-generator-service
  newName: us-central1-docker.pkg.dev/bracket-irl/bracket-irl/diagram-generator-service
  newTag: latest
- name: bracket-irl/orchestrator-service
  newName: us-central1-docker.pkg.dev/bracket-irl/bracket-irl/orchestrator-service
  newTag: latest
EOF

# Update the kustomization.yaml file to include the image patch
sed -i '/resources:/a \ \ - image-patch.yaml' kustomization.yaml
```

3. Update the PersistentVolume configuration to ensure proper artifact sharing:

```bash
cd bracket_core/bracket_irl/microservices/kubernetes/overlays/gke

# Create a patch for the persistent volume
cat > persistent-volume-patch.yaml << EOF
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: bracket-data-pvc
  namespace: bracket-irl
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard-rwo
EOF
```

4. Update the domain in the ingress manifest:

```bash
# Replace the example domain with your actual domain
sed -i 's/bracket-irl.example.com/your-actual-domain.com/g' ingress.yaml
```

## Step 6: Set Up Secrets

1. Create a secrets file with all required API keys:

```bash
cd bracket_core/bracket_irl/microservices/kubernetes/overlays/gke

# Create a secrets file with all required API keys
cat > secrets.env << EOF
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
OPENROUTER_API_KEY=your-openrouter-api-key
EOF
```

## Step 7: Create a Static IP Address

1. Create a static IP address for the ingress:

```bash
gcloud compute addresses create bracket-irl-ip --global
```

2. Get the IP address:

```bash
gcloud compute addresses describe bracket-irl-ip --global --format='value(address)'
```

3. Update your DNS records to point your domain to this IP address.

## Step 8: Deploy to GKE

1. Apply the Kubernetes manifests:

```bash
cd bracket_core/bracket_irl/microservices
kubectl apply -k kubernetes/overlays/gke
```

2. Check the deployment status:

```bash
kubectl get pods -n bracket-irl
kubectl get services -n bracket-irl
kubectl get ingress -n bracket-irl
```

## Step 9: Set Up Cloud Storage (Optional)

If you want to use Google Cloud Storage instead of local storage:

1. Create a Cloud Storage bucket:

```bash
gsutil mb -l us-central1 gs://bracket-irl-data
```

2. Update the ConfigMap to use Cloud Storage:

```bash
kubectl edit configmap bracket-irl-config -n bracket-irl
```

Update the following values:

```yaml
STORAGE_TYPE: "gcs"
STORAGE_PATH: "gs://bracket-irl-data"
```

3. Create a service account for the pods to access Cloud Storage:

```bash
# Create a service account
gcloud iam service-accounts create bracket-irl-sa

# Grant the service account access to the bucket
gsutil iam ch serviceAccount:<EMAIL>:objectAdmin gs://bracket-irl-data

# Create a key for the service account
gcloud iam service-accounts keys create bracket-irl-sa-key.json --iam-account=<EMAIL>

# Create a Kubernetes secret from the key
kubectl create secret generic bracket-irl-gcs-key --from-file=key.json=bracket-irl-sa-key.json -n bracket-irl
```

4. Update the deployments to use the service account key:

```bash
kubectl edit deployment repo-mapper-service -n bracket-irl
```

Add the following volume and volume mount:

```yaml
volumes:
- name: gcs-key
  secret:
    secretName: bracket-irl-gcs-key
containers:
- name: repo-mapper-service
  volumeMounts:
  - name: gcs-key
    mountPath: /var/secrets/google
  env:
  - name: GOOGLE_APPLICATION_CREDENTIALS
    value: /var/secrets/google/key.json
```

Repeat for all other deployments.

## Step 10: Set Up Monitoring and Logging

1. Enable Cloud Monitoring and Cloud Logging:

```bash
gcloud services enable monitoring.googleapis.com logging.googleapis.com
```

2. Install the Cloud Operations for GKE addon:

```bash
gcloud container clusters update bracket-irl-cluster \
    --zone us-central1-a \
    --enable-stackdriver-kubernetes
```

## Troubleshooting

### Ingress Issues

If you're having issues with the ingress:

1. Check the ingress status:

```bash
kubectl describe ingress bracket-irl-ingress -n bracket-irl
```

2. Check the managed certificate status:

```bash
kubectl describe managedcertificate bracket-irl-cert -n bracket-irl
```

### Pod Issues

If you're having issues with the pods:

1. Check the pod logs:

```bash
kubectl logs <pod-name> -n bracket-irl
```

2. Check the pod events:

```bash
kubectl describe pod <pod-name> -n bracket-irl
```

### File Format Issues

If you're having issues with file formats:

1. Check that the file-domain-mapper-service is outputting YAML files:

```bash
kubectl exec -it <file-domain-mapper-pod-name> -n bracket-irl -- ls -la /app/data/artifacts/*/file_domain_mapping/
```

2. Check that the domain-file-repomap-service is correctly reading YAML files:

```bash
kubectl logs <domain-file-repomap-pod-name> -n bracket-irl | grep "domain_mapping_path"
```

### Storage Issues

If you're having issues with storage:

1. Check the PersistentVolumeClaim status:

```bash
kubectl describe pvc bracket-data-pvc -n bracket-irl
```

2. Check the PersistentVolume status:

```bash
kubectl describe pv <pv-name>
```

3. Verify that the shared volume is properly mounted:

```bash
kubectl exec -it <pod-name> -n bracket-irl -- df -h
```

## Cleaning Up

To clean up the GCP resources:

1. Delete the GKE cluster:

```bash
gcloud container clusters delete bracket-irl-cluster --zone us-central1-a
```

2. Delete the Artifact Registry repository:

```bash
gcloud artifacts repositories delete bracket-irl --location=us-central1
```

3. Delete the static IP address:

```bash
gcloud compute addresses delete bracket-irl-ip --global
```

4. Delete the Cloud Storage bucket (if created):

```bash
gsutil rm -r gs://bracket-irl-data
```
