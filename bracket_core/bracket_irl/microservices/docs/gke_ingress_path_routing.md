# GKE Ingress Path Routing Solution

## Problem Statement

When deploying microservices to Google Kubernetes Engine (GKE) with path-based routing through an ingress, we encountered a "Not Found" error when accessing services through the ingress URL. For example, when accessing `http://*************/orchestrator/health`, we received a 404 error.

The root cause was that the GCP ingress controller doesn't support path rewriting natively. When a request comes in for `/orchestrator/health`, the ingress forwards the entire path to the backend service, but the service is expecting just `/health`.

## Solution Overview

We implemented a proxy service using nginx to handle path rewriting. The proxy service:
1. Receives requests from the ingress (e.g., `/orchestrator/health`)
2. Strips the path prefix (e.g., `/orchestrator`)
3. Forwards the request to the appropriate service (e.g., `orchestrator-service:8000/health`)

## Detailed Implementation Steps

### 1. Create a Proxy Service Using Nginx

We created a deployment for the nginx proxy service:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: proxy-service
  namespace: bracket-irl
spec:
  replicas: 1
  selector:
    matchLabels:
      app: proxy-service
  template:
    metadata:
      labels:
        app: proxy-service
    spec:
      containers:
      - name: proxy-service
        image: nginx:latest
        ports:
        - containerPort: 80
        volumeMounts:
        - name: nginx-config
          mountPath: /etc/nginx/conf.d
      volumes:
      - name: nginx-config
        configMap:
          name: nginx-config
```

And a corresponding service:

```yaml
apiVersion: v1
kind: Service
metadata:
  name: proxy-service
  namespace: bracket-irl
spec:
  selector:
    app: proxy-service
  ports:
  - port: 80
    targetPort: 80
  type: ClusterIP
```

### 2. Configure Nginx for Path Rewriting

We created a ConfigMap with the nginx configuration:

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: bracket-irl
data:
  default.conf: |
    server {
      listen 80;
      server_name _;

      # Root path handler
      location = / {
        return 200 'Bracket IRL API Gateway - Available endpoints: /orchestrator, /repo-mapper, /domain-analyzer, /file-domain-mapper, /domain-file-repomap, /diagram-generator';
        add_header Content-Type text/plain;
      }

      # Orchestrator service
      location /orchestrator/ {
        proxy_pass http://orchestrator-service:8000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
      }

      # Special case for health check
      location = /orchestrator/health {
        proxy_pass http://orchestrator-service:8000/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
      }

      # Similar configurations for other services...
    }
```

The key parts of this configuration are:

1. The `location /orchestrator/` block with `proxy_pass http://orchestrator-service:8000/;` - This strips the `/orchestrator` prefix and forwards the request to the orchestrator service.

2. The special case for health checks: `location = /orchestrator/health` - This ensures that health check requests are properly routed.

### 3. Create an Ingress Resource Pointing to the Proxy Service

We created an ingress resource that routes all traffic to the proxy service:

```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: bracket-irl-ingress
  namespace: bracket-irl
  annotations:
    kubernetes.io/ingress.class: "gce"
    kubernetes.io/ingress.global-static-ip-name: "bracket-irl-ip"
    networking.gke.io/managed-certificates: "bracket-irl-cert"
spec:
  rules:
  - http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: proxy-service
            port:
              number: 80
```

This ingress routes all traffic to the proxy service, which then handles the path rewriting and forwards requests to the appropriate microservices.

## Testing and Verification

We verified the solution by testing various endpoints:

1. Root path: `curl -v http://*************/`
   - Returns a welcome message with available endpoints

2. Orchestrator health check: `curl -v http://*************/orchestrator/health`
   - Returns `{"status":"ok","service":"orchestrator-service"}`

3. Repo Mapper health check: `curl -v http://*************/repo-mapper/health`
   - Returns `{"status":"ok","service":"repo-mapper-service"}`

## Key Insights

1. **GCP Ingress Limitations**: The GCP ingress controller doesn't support path rewriting natively, unlike the nginx ingress controller which has the `rewrite-target` annotation.

2. **Proxy Pattern**: Using a proxy service is an effective way to handle path rewriting in GKE.

3. **Nginx Configuration**: The trailing slash in the `proxy_pass` URL is crucial for correct path rewriting. For example:
   - `proxy_pass http://orchestrator-service:8000/;` - Strips the matched prefix
   - `proxy_pass http://orchestrator-service:8000;` - Would not strip the prefix

4. **Special Cases**: It's important to handle special cases like health checks explicitly to ensure they work correctly.

## SSL/TLS Considerations

The managed certificate (`bracket-irl-cert`) was still in the "Provisioning" state during our testing. Once provisioned (which can take up to 30-60 minutes), the services will be accessible via HTTPS.

## Conclusion

By implementing a proxy service with nginx for path rewriting, we successfully enabled path-based routing to multiple microservices in GKE. This approach provides a clean and maintainable solution that works well with GCP's ingress controller.


curl -X POST http://*************/api/v1/repositories \
  -H "Content-Type: application/json" \
  -d '{
    "repo_dir": "/path/to/repository",
    "api_key": "YOUR_OPENAI_API_KEY",
    "model": "gpt-4o-mini"
  }'


  http://*************/


  curl -X POST http://*************/orchestrator/api/v1/repositories \
  -H "Content-Type: application/json" \
  -d '{
    "repo_dir": "bracket_core/bracket_irl",
    "api_key": "YOUR_OPENAI_API_KEY",
    "model": "gpt-4o-mini"
  }'

  curl -X GET http://*************/orchestrator/api/v1/jobs/{job_id}


curl -X GET http://*************/orchestrator/health/details