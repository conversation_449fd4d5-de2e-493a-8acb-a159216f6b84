#!/usr/bin/env python3
"""
JSON-based Domain Diagram Generator for Codebase

This module extends the enhanced_domain_diagram_generator.py to work with a JSON structure
where leaf domains are keys, with file paths as sub-keys, and file code as values.

Key features:
1. Reads a JSON file with domain -> file paths -> code structure
2. Processes each leaf domain in parallel
3. Skips any domain containing "Unclassified"
4. Generates mermaid diagrams for each leaf domain

Usage:
    python bracket_domain_diagram_generator.py --domain-file-json <path> --output-dir <path>
"""

import os
import json
import logging
import asyncio
import time
import tiktoken
import re
from typing import Dict, List, Set, Tuple, Optional, Union, Any
from dataclasses import dataclass, field

# Import the enhanced domain diagram generator
# Try both import paths for compatibility

from base_domain_diagram_generator import EnhancedDomainDiagramGenerator, DiagramGenerationResult

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

DEFAULT_ENCODING_NAME = "cl100k_base"

def num_tokens_from_string(
    string: str, model: str | None = None, encoding_name: str | None = None
) -> int:
    """Return the number of tokens in a text string."""
    if model is not None:
        encoding = tiktoken.get_encoding(DEFAULT_ENCODING_NAME)
    else:
        encoding = tiktoken.get_encoding(encoding_name or DEFAULT_ENCODING_NAME)
    return len(encoding.encode(string))

@dataclass
class JSONDiagramGenerationResult(DiagramGenerationResult):
    """Extended result of generating domain diagrams from JSON input."""
    domain_file_stats: Dict[str, Any] = field(default_factory=dict)  # Statistics about domains and files
    processing_stats: Dict[str, Any] = field(default_factory=dict)  # Statistics about processing time and success rate

class StatusTracker:
    """Tracks the status of diagram generation tasks."""

    def __init__(self, total_tasks: int):
        """Initialize the status tracker.

        Args:
            total_tasks: Total number of tasks to track
        """
        self.total_tasks = total_tasks
        self.completed_tasks = 0
        self.failed_tasks = 0
        self.start_time = time.time()
        self.domain_times = {}  # Maps domain names to processing times

    def task_completed(self, domain: str, elapsed_time: float):
        """Mark a task as completed.

        Args:
            domain: Domain name
            elapsed_time: Time taken to process the domain
        """
        self.completed_tasks += 1
        self.domain_times[domain] = elapsed_time

    def task_failed(self):
        """Mark a task as failed."""
        self.failed_tasks += 1

    def get_progress(self) -> str:
        """Get a string representation of the current progress.

        Returns:
            Progress string
        """
        processed = self.completed_tasks + self.failed_tasks
        percent = (processed / self.total_tasks) * 100 if self.total_tasks > 0 else 0
        elapsed = time.time() - self.start_time

        return f"{processed}/{self.total_tasks} ({percent:.1f}%) in {elapsed:.1f}s"

    def get_stats(self) -> Dict[str, Any]:
        """Get statistics about the processing.

        Returns:
            Dictionary containing statistics
        """
        elapsed = time.time() - self.start_time

        # Calculate average and median processing times
        times = list(self.domain_times.values())
        avg_time = sum(times) / len(times) if times else 0
        median_time = sorted(times)[len(times) // 2] if times else 0

        return {
            "total_tasks": self.total_tasks,
            "completed_tasks": self.completed_tasks,
            "failed_tasks": self.failed_tasks,
            "success_rate": (self.completed_tasks / self.total_tasks) * 100 if self.total_tasks > 0 else 0,
            "total_elapsed_time": elapsed,
            "average_task_time": avg_time,
            "median_task_time": median_time,
            "tasks_per_second": self.completed_tasks / elapsed if elapsed > 0 else 0,
        }

class JSONDomainDiagramGenerator:
    """
    Domain Diagram Generator that works with a JSON structure where leaf domains are keys,
    with file paths as sub-keys, and file code as values.
    """

    def __init__(
        self,
        domain_file_json_path: str,
        output_dir: str,
        # Model selection and configuration
        model_type: str = "openai",  # "claude" or "openai"
        # Claude parameters
        claude_api_key: Optional[str] = None,
        claude_model: str = "claude-3-7-sonnet-20250219",
        # OpenAI parameters
        openai_api_key: Optional[str] = None,
        openai_model: str = "o3-mini",
        # OpenRouter parameters
        use_openrouter: bool = True,
        openrouter_api_key: Optional[str] = None,
        openrouter_model: str = "google/gemini-2.5-pro-preview",
        openrouter_token_threshold: int = 45000,  # Token threshold to switch to OpenRouter
        openrouter_max_concurrent: int = 3,  # Maximum concurrent OpenRouter calls
        # Common parameters
        # max_tokens: int = 8096,
        temperature: float = 0.5,
        max_requests_per_minute: float = 10000,
        max_tokens_per_minute: float = 10000000,
        # Parallelization parameters
        max_concurrent_tasks: int = 50,
        # Caching parameters
        cache_dir: Optional[str] = None,
        use_cache: bool = True,
        # Token limit parameters
        max_input_tokens: int = 100000,  # Maximum number of tokens for input code
    ):
        """Initialize the JSON domain diagram generator.

        Args:
            domain_file_json_path: Path to the JSON file with domain -> file paths -> code structure
            output_dir: Directory to save generated diagrams
            model_type: Type of model to use ("claude" or "openai")
            claude_api_key: Anthropic API key. If None, will try to get from environment.
            claude_model: Claude model to use.
            openai_api_key: OpenAI API key. If None, will try to get from environment.
            openai_model: OpenAI model to use.
            use_openrouter: Whether to use OpenRouter for large domains
            openrouter_api_key: OpenRouter API key. If None, will try to get from environment.
            openrouter_model: OpenRouter model to use (Gemini)
            openrouter_token_threshold: Token threshold to switch to OpenRouter
            max_tokens: Maximum tokens to generate.
            temperature: Sampling temperature.
            max_requests_per_minute: Rate limit for API requests
            max_tokens_per_minute: Token rate limit for API
            max_concurrent_tasks: Maximum number of concurrent tasks for parallel processing
            cache_dir: Directory to cache intermediate results (None for no caching)
            use_cache: Whether to use caching for intermediate results
            max_input_tokens: Maximum number of tokens for input code
        """
        self.domain_file_json_path = domain_file_json_path
        self.output_dir = output_dir
        self.diagrams_dir = os.path.join(output_dir, "diagrams_from_json")
        os.makedirs(self.diagrams_dir, exist_ok=True)

        # Create a temporary directory for the enhanced domain diagram generator
        self.temp_dir = os.path.join(output_dir, "temp")
        os.makedirs(self.temp_dir, exist_ok=True)

        # Initialize the enhanced domain diagram generator with dummy paths
        # We'll use its API methods but not its file reading functionality
        self.diagram_generator = EnhancedDomainDiagramGenerator(
            domain_traces_yaml_path="",  # Dummy path, we won't use this
            functions_parquet_path="",   # Dummy path, we won't use this
            output_dir=self.temp_dir,
            model_type=model_type,
            claude_api_key=claude_api_key,
            claude_model=claude_model,
            openai_api_key=openai_api_key,
            openai_model=openai_model,
            use_openrouter=use_openrouter,
            openrouter_api_key=openrouter_api_key,
            openrouter_model=openrouter_model,
            openrouter_token_threshold=openrouter_token_threshold,
            openrouter_max_concurrent=openrouter_max_concurrent,
            # max_tokens=max_tokens,
            temperature=temperature,
            max_requests_per_minute=max_requests_per_minute,
            max_tokens_per_minute=max_tokens_per_minute,
            max_concurrent_tasks=max_concurrent_tasks,
            cache_dir=cache_dir,
            use_cache=use_cache,
            max_input_tokens=max_input_tokens,
        )

        # Store configuration
        self.max_concurrent_tasks = max_concurrent_tasks
        self.max_input_tokens = max_input_tokens

        # Initialize domain data
        self.domain_file_data = {}
        self.domain_stats = {}

    def read_domain_file_json(self) -> Dict[str, Dict[str, str]]:
        """
        Read the domain file JSON.

        Returns:
            Dictionary mapping domain names to dictionaries of file paths and code
        """
        logger.info(f"Reading domain file JSON from: {self.domain_file_json_path}")

        try:
            with open(self.domain_file_json_path, 'r') as f:
                domain_file_data = json.load(f)

            # Filter out domains containing "Unclassified"
            filtered_domains = {}
            for domain, files in domain_file_data.items():
                if "Unclassified" not in domain:
                    filtered_domains[domain] = files
                else:
                    logger.info(f"Skipping unclassified domain: {domain}")

            logger.info(f"Found {len(filtered_domains)} domains after filtering out unclassified domains")
            self.domain_file_data = filtered_domains

            # Compute statistics
            self.domain_stats = self._compute_domain_stats(filtered_domains)

            return filtered_domains

        except Exception as e:
            logger.error(f"Error reading domain file JSON: {e}")
            return {}

    def _compute_domain_stats(self, domain_file_data: Dict[str, Dict[str, str]]) -> Dict[str, Any]:
        """
        Compute statistics about the domains and files.

        Args:
            domain_file_data: Dictionary mapping domain names to dictionaries of file paths and code

        Returns:
            Dictionary containing statistics
        """
        stats = {
            "total_domains": len(domain_file_data),
            "domains": {},
            "total_files": 0,
            "total_tokens": 0,
        }

        for domain, files in domain_file_data.items():
            domain_stats = {
                "file_count": len(files),
                "token_count": 0,
            }

            # Count tokens in all files in this domain
            all_code = ""
            for _, code in files.items():
                all_code += code + "\n\n"

            # Estimate token count
            token_count = num_tokens_from_string(all_code, self.diagram_generator.model_name)
            domain_stats["token_count"] = token_count

            stats["domains"][domain] = domain_stats
            stats["total_files"] += len(files)
            stats["total_tokens"] += token_count

        return stats

    async def generate_leaf_diagram_from_files(self, domain: str, files: Dict[str, str]) -> Tuple[str, str]:
        """
        Generate a mermaid diagram for a leaf domain based on its files.

        Args:
            domain: Domain name
            files: Dictionary mapping file paths to code

        Returns:
            Tuple of (diagram_content, raw_response)
        """
        logger.info(f"Generating diagram for leaf domain: {domain}")

        # Check if there are any files in this domain
        if not files:
            logger.warning(f"No files found for domain: {domain}. Skipping diagram generation.")
            empty_diagram = "```mermaid\ngraph TD\n    A[No files found in this domain]\n```"
            return empty_diagram, "No files found in this domain"

        # Note: Rate limiting is now handled by the worker pool, not here

        # Prepare file data for the prompt
        file_details = []
        current_token_count = 0
        truncated_files = []

        for file_path, code in files.items():
            # Format file details
            file_detail = {
                'file_path': file_path,
                'code': code,
            }

            # Estimate token count for this file
            file_str = json.dumps(file_detail)
            file_tokens = num_tokens_from_string(file_str, self.diagram_generator.model_name)

            # Check if adding this file would exceed the token limit
            if current_token_count + file_tokens <= self.max_input_tokens:
                file_details.append(file_detail)
                current_token_count += file_tokens
            else:
                # We've reached the token limit, truncate remaining files
                truncated_files.append(file_path)
                continue

        # Log token usage information
        logger.info(f"Token usage for domain {domain}: {current_token_count}/{self.max_input_tokens} tokens used")

        if truncated_files:
            logger.warning(f"Truncated {len(truncated_files)} files for domain {domain} due to token limit of {self.max_input_tokens}")

        # Create the system prompt
        system_prompt = """You are an expert software architect who creates clear, informative mermaid diagrams to visualize code architecture.
Your task is to create a detailed mermaid diagram that represents the LOGICAL RELATIONSHIPS and INTERACTIONS between files in a specific domain of a codebase.

Guidelines for creating the diagram:
1. Focus on the LOGICAL PURPOSE and ROLE of each file within the domain
2. Emphasize how files work together to accomplish domain goals
3. Show meaningful relationships and dependencies between files
4. Highlight the conceptual flow of data and control between files
5. Group files by their logical purpose or the feature they support
6. Represent the domain's core concepts and how files implement them
7. Show how files collaborate to implement domain behaviors
8. Illustrate key abstractions and patterns used in the domain
9. Include important domain-specific data structures and their transformations
10. Show initialization sequences and important process flows
11. DO NOT create a simple procedural flowchart - focus on logical relationships
12. DO NOT use tooltips or click actions - they consume unnecessary tokens
13. The diagram should be 3000-6000 tokens in size to provide comprehensive detail

Styling Guidelines (IMPORTANT):
1. Use a VERTICAL layout rather than horizontal for better readability
2. Use PASTEL COLORS for all nodes and subgraphs - avoid bright or dark colors
3. Use this consistent color scheme:
   - Core domain files: pastel blue (#D4F1F9)
   - Supporting/utility files: pastel yellow (#FFF8DC)
   - Data structure files: pastel green (#E0F8E0)
   - Error handling files: pastel red (#FFE4E1)
   - Initialization/setup files: pastel purple (#E6E6FA)
   - Logical groupings/subgraphs: very light gray (#F8F8F8) with pastel borders
4. Use rounded rectangles for most nodes: node[shape="rounded-rectangle"]
5. Use different node shapes to represent different types of files when appropriate
6. Use consistent line thickness and arrow styles
7. Ensure proper spacing between nodes and subgraphs
8. Follow strict mermaid.js syntax to ensure the diagram renders correctly
9. Avoid use of brackets, `(` or `)` and avoid adding any notes. Please follow the mermaid syntax properly.

Your output should ONLY contain a valid mermaid diagram enclosed in triple backticks with the mermaid tag.
Ensure the diagram follows proper mermaid.js syntax and is renderable without any syntax errors.
"""

        # Create the user prompt
        truncation_note = ""
        if truncated_files:
            truncation_percentage = len(truncated_files) / (len(file_details) + len(truncated_files)) * 100
            truncation_note = f"\n\nNOTE: Due to token limits, {len(truncated_files)} files ({truncation_percentage:.1f}% of total) were truncated from this diagram. The diagram shows the {len(file_details)} most important files that fit within the token limit."

        file_details_json = json.dumps(file_details, indent=2)
        user_prompt = f"""Create a detailed mermaid diagram for the leaf domain: {domain}

This is a leaf domain with no subdomains. Focus on showing the LOGICAL RELATIONSHIPS and INTERACTIONS between files, not just their implementation details.

Here are the files in this domain with their code:

{file_details_json}{truncation_note}

Please generate a comprehensive mermaid diagram that shows:
1. The LOGICAL PURPOSE of each file within the domain context
2. How files COLLABORATE to implement domain behaviors
3. The MEANINGFUL RELATIONSHIPS and dependencies between files
4. How files are GROUPED by their logical purpose or features they support
5. The domain's CORE CONCEPTS and how files implement them
6. Important DOMAIN-SPECIFIC DATA STRUCTURES and their transformations
7. Key ABSTRACTIONS and PATTERNS used in the domain

IMPORTANT STYLING REQUIREMENTS:
- Use a VERTICAL layout
- Use PASTEL COLORS for all nodes and subgraphs
- Follow the color scheme specified in the system prompt
- Ensure the diagram is well-structured and easy to read
- Follow strict mermaid.js syntax to ensure the diagram renders correctly
- Avoid use of brackets, `(` or `)` and avoid adding any notes. Please follow the mermaid syntax properly.

Make sure to capture the LOGICAL RELATIONSHIPS between files, not just their procedural flow.
DO NOT create a simple procedural flowchart - focus on meaningful interactions and relationships.
DO NOT use tooltips or click actions in the diagram.
"""

        # Analyze token count to determine which model to use
        total_tokens = num_tokens_from_string(system_prompt + user_prompt, self.diagram_generator.model_name)
        use_openrouter = self.diagram_generator.use_openrouter and total_tokens > self.diagram_generator.openrouter_token_threshold

        try:
            # Call the appropriate API based on model type and token count
            if use_openrouter:
                # Call the OpenRouter API
                response, raw_response = await self.diagram_generator._call_openrouter_api(
                    system_prompt=system_prompt,
                    user_prompt=user_prompt
                )
            elif self.diagram_generator.model_type == "claude":
                # Call the Claude API
                response = await self.diagram_generator.claude_client.generate(
                    system_prompt=system_prompt,
                    prompt=user_prompt,
                    # max_tokens=8000,
                    temperature=0.7,
                    top_p=0.8
                )
                raw_response = response
            else:  # OpenAI
                # Call the OpenAI API
                response, raw_response = await self.diagram_generator._call_openai_api(
                    system_prompt=system_prompt,
                    user_prompt=user_prompt
                )

            # Extract the mermaid diagram
            diagram = self.diagram_generator._extract_mermaid_diagram(response)

            if not diagram:
                logger.warning(f"No mermaid diagram found in response for {domain}")
                diagram = response

            return diagram, raw_response

        except Exception as e:
            logger.error(f"Error generating diagram for {domain}: {e}")
            return "", f"Error: {str(e)}"

    async def _process_with_worker_pool(self, tasks, num_workers):
        """
        Process tasks using a worker pool.

        Args:
            tasks: List of (domain, task) tuples
            num_workers: Number of worker coroutines

        Returns:
            Dictionary mapping domains to (file_path, diagram, raw_response) tuples
        """
        queue = asyncio.Queue()
        results = {}
        tasks_total = len(tasks)

        # Create a status tracker to monitor progress
        status_tracker = StatusTracker(tasks_total)

        # Create a shared rate limiter state to track API usage across workers
        shared_rate_limiter = {
            "last_update_time": time.time(),
            "requests_in_flight": 0,
            "max_concurrent_requests": min(num_workers, 50)  # Limit concurrent API requests
        }

        # Log initial status
        logger.info(f"Starting worker pool with {num_workers} workers for {tasks_total} tasks")

        # Define the worker function
        async def worker(worker_id):
            while True:
                try:
                    # Get a task from the queue with timeout
                    try:
                        domain, task_coro = await asyncio.wait_for(queue.get(), timeout=1.0)
                    except asyncio.TimeoutError:
                        # Check if queue is empty and all tasks are done
                        if queue.empty() and len(results) >= tasks_total:
                            break
                        continue

                    # Process the task
                    try:
                        logger.info(f"Worker {worker_id}: Processing domain {domain}")
                        start_time = time.time()

                        # Execute the task with proper error handling
                        try:
                            # Apply rate limiting before executing the task
                            # Check if we're exceeding the maximum concurrent requests
                            while shared_rate_limiter["requests_in_flight"] >= shared_rate_limiter["max_concurrent_requests"]:
                                # Wait a bit before checking again
                                await asyncio.sleep(0.1)

                            # Increment the in-flight requests counter
                            shared_rate_limiter["requests_in_flight"] += 1

                            try:
                                # Apply standard rate limiting
                                await self.diagram_generator.rate_limiter.acquire()

                                # Execute the task
                                diagram, raw_response = await task_coro
                            finally:
                                # Decrement the in-flight requests counter
                                shared_rate_limiter["requests_in_flight"] -= 1

                            # Save the diagram to a file
                            safe_domain = domain.replace('/', '_').replace(' ', '_')
                            file_path = os.path.join(self.diagrams_dir, f"{safe_domain}.md")
                            with open(file_path, 'w') as f:
                                f.write(diagram)

                            # Store the result
                            results[domain] = (file_path, diagram, raw_response)

                            # Update status tracker
                            elapsed = time.time() - start_time
                            status_tracker.task_completed(domain, elapsed)

                            # Log progress periodically
                            if status_tracker.completed_tasks % 5 == 0 or status_tracker.completed_tasks == tasks_total:
                                logger.info(f"Progress: {status_tracker.get_progress()}")

                            logger.info(f"Worker {worker_id}: Completed domain {domain} in {elapsed:.2f}s")

                        except Exception as e:
                            logger.error(f"Worker {worker_id}: Error processing domain {domain}: {str(e)}")
                            results[domain] = (None, "", f"Error: {str(e)}")
                            status_tracker.task_failed()
                            logger.info(f"Worker {worker_id}: Failed domain {domain}")

                    except Exception as e:
                        logger.error(f"Worker {worker_id}: Unexpected error: {str(e)}")
                        if domain is not None:
                            results[domain] = (None, "", f"Error: {str(e)}")
                            status_tracker.task_failed()

                    finally:
                        # Mark the task as done
                        queue.task_done()

                        # Log overall progress after each task
                        completed = status_tracker.completed_tasks
                        failed = status_tracker.failed_tasks
                        total = status_tracker.total_tasks
                        logger.debug(f"Overall progress: {completed + failed}/{total} tasks processed ({completed} completed, {failed} failed)")

                except Exception as e:
                    logger.error(f"Worker {worker_id}: Critical error in worker loop: {str(e)}")

        # Start workers
        workers = []
        for i in range(num_workers):
            worker_task = asyncio.create_task(worker(i+1))
            workers.append(worker_task)

        # Add tasks to queue
        for domain, task in tasks:
            await queue.put((domain, task))

        # Wait for all tasks to be processed
        try:
            # Wait for queue to be empty and all tasks to be processed
            await queue.join()

            # Additional check to ensure all results are filled
            if len(results) < tasks_total:
                logger.warning(f"Some results are missing ({len(results)}/{tasks_total}), waiting longer...")
                await asyncio.sleep(5.0)

        finally:
            # Cancel all workers
            for worker_task in workers:
                worker_task.cancel()

            # Wait for workers to be cancelled
            await asyncio.gather(*workers, return_exceptions=True)

            # Log final statistics
            stats = status_tracker.get_stats()
            logger.info(f"Worker pool completed: {stats['completed_tasks']}/{stats['total_tasks']} tasks completed ({stats['success_rate']:.1f}% success rate)")
            logger.info(f"Average task time: {stats['average_task_time']:.2f}s, Median: {stats['median_task_time']:.2f}s")
            logger.info(f"Processing rate: {stats['tasks_per_second']:.2f} tasks/second")

        return results, status_tracker.get_stats()

    async def generate_all_diagrams(self) -> JSONDiagramGenerationResult:
        """
        Generate diagrams for all domains in the JSON file using a worker pool.

        Returns:
            JSONDiagramGenerationResult containing the generation results
        """
        start_time = time.time()
        logger.info("Starting diagram generation for all domains")

        # Read the domain file JSON
        domain_file_data = self.read_domain_file_json()

        if not domain_file_data:
            logger.error("No domains found in the JSON file")
            return JSONDiagramGenerationResult(
                success=False,
                error_message="No domains found in the JSON file",
                domain_file_stats=self.domain_stats
            )

        # Create a list of tasks for parallel processing
        tasks = []
        for domain, files in domain_file_data.items():
            task = self.generate_leaf_diagram_from_files(domain, files)
            tasks.append((domain, task))

        # Process domains in parallel with a worker pool
        logger.info(f"Processing {len(tasks)} domain diagram generation tasks with {self.max_concurrent_tasks} workers")

        # Use worker pool to process tasks
        max_workers = min(50, self.max_concurrent_tasks)  # Cap at 50 workers max
        worker_results, processing_stats = await self._process_with_worker_pool(tasks, max_workers)

        # Process results
        diagram_files = {}
        raw_responses = {}

        for domain, (file_path, _, raw_response) in worker_results.items():
            if file_path:
                diagram_files[domain] = file_path
                raw_responses[domain] = raw_response

        # Create the result object
        result = JSONDiagramGenerationResult(
            success=True,
            diagram_files=diagram_files,
            raw_responses=raw_responses,
            model_used=f"{self.diagram_generator.model_type}:{self.diagram_generator.model_name}",
            domain_file_stats=self.domain_stats,
            processing_stats=processing_stats
        )

        # Add OpenRouter usage information if available
        # Note: We check for the attribute dynamically to avoid static type checking errors
        openrouter_info = getattr(self.diagram_generator, 'openrouter_info', None)
        if openrouter_info:
            result.openrouter_info = openrouter_info

        end_time = time.time()
        logger.info(f"Completed diagram generation for all domains in {end_time - start_time:.2f} seconds")
        logger.info(f"Generated {len(diagram_files)} diagrams out of {len(domain_file_data)} domains")

        # Log success rate
        success_rate = len(diagram_files) / len(domain_file_data) * 100 if domain_file_data else 0
        logger.info(f"Success rate: {success_rate:.2f}% ({len(diagram_files)}/{len(domain_file_data)})")

        return result


async def main():
    """Main entry point for the JSON domain diagram generator."""
    import argparse

    parser = argparse.ArgumentParser(description="Generate mermaid diagrams from domain file JSON")
    # parser.add_argument("--domain-file-json", required=True, help="Path to the domain file JSON")
    # parser.add_argument("--output-dir", required=True, help="Directory to save generated diagrams")

    # Model selection
    parser.add_argument("--model-type", default="openai", choices=["claude", "openai"],
                        help="Type of model to use (claude or openai)")

    # Claude parameters
    parser.add_argument("--claude-api-key", help="Anthropic API key (if not provided, will try to get from environment)")
    parser.add_argument("--claude-model", default="claude-3-7-sonnet-20250219", help="Claude model to use")

    # OpenAI parameters
    parser.add_argument("--openai-api-key", help="OpenAI API key (if not provided, will try to get from environment)")
    parser.add_argument("--openai-model", default="gpt-4.1-2025-04-14", help="OpenAI model to use")

    # OpenRouter parameters
    parser.add_argument("--use-openrouter", action="store_true", default=False,
                        help="Whether to use OpenRouter for large domains")
    parser.add_argument("--openrouter-api-key", help="OpenRouter API key (if not provided, will try to get from environment)")
    parser.add_argument("--openrouter-model", default="google/gemini-2.5-pro-preview",
                        help="OpenRouter model to use (Gemini)")
    parser.add_argument("--openrouter-token-threshold", type=int, default=45000,
                        help="Token threshold to switch to OpenRouter")
    parser.add_argument("--openrouter-max-concurrent", type=int, default=3,
                        help="Maximum number of concurrent OpenRouter API calls")

    # Common parameters
    # parser.add_argument("--max-tokens", type=int, default=8000, help="Maximum tokens to generate")
    parser.add_argument("--temperature", type=float, default=0.5, help="Sampling temperature")
    parser.add_argument("--requests-per-minute", type=float, default=7000, help="Rate limit for API requests")
    parser.add_argument("--tokens-per-minute", type=float, default=20000000, help="Token rate limit for API requests")

    # Parallelization parameters
    parser.add_argument("--max-concurrent-tasks", type=int, default=50,
                        help="Maximum number of concurrent tasks for parallel processing")

    # Caching parameters
    parser.add_argument("--cache-dir", default=None,
                        help="Directory to cache intermediate results (None for no caching)")
    parser.add_argument("--no-cache", action="store_true", help="Disable caching")

    args = parser.parse_args()

    args.domain_file_repomap_json = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/domain_file_repomap/gitlab/gitlab_domain_file_repomap.json"
    args.output_dir = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/mermaid_outputs_json/gitlab/"

    try:
        # Create output directory if it doesn't exist
        os.makedirs(args.output_dir, exist_ok=True)

        # Create cache directory if specified
        if args.cache_dir:
            os.makedirs(args.cache_dir, exist_ok=True)

        # Create a JSON domain diagram generator
        generator = JSONDomainDiagramGenerator(
            domain_file_json_path=args.domain_file_repomap_json,
            output_dir=args.output_dir,
            model_type=args.model_type,
            # Claude parameters
            claude_api_key=args.claude_api_key,
            claude_model=args.claude_model,
            # OpenAI parameters
            openai_api_key=args.openai_api_key,
            openai_model=args.openai_model,
            # OpenRouter parameters
            use_openrouter=args.use_openrouter,
            openrouter_api_key=args.openrouter_api_key,
            openrouter_model=args.openrouter_model,
            openrouter_token_threshold=args.openrouter_token_threshold,
            openrouter_max_concurrent=args.openrouter_max_concurrent,
            # Common parameters
            # max_tokens=args.max_tokens,
            temperature=args.temperature,
            max_requests_per_minute=args.requests_per_minute,
            max_tokens_per_minute=args.tokens_per_minute,
            # Parallelization parameters
            max_concurrent_tasks=args.max_concurrent_tasks,
            # Caching parameters
            cache_dir=args.cache_dir,
            use_cache=not args.no_cache,
        )

        # Generate all diagrams
        result = await generator.generate_all_diagrams()

        if result.success:
            logger.info("Diagram generation completed successfully")
            logger.info(f"Generated {len(result.diagram_files)} diagrams")
            logger.info(f"Output directory: {args.output_dir}")

            # Print domain statistics
            logger.info(f"Domain statistics:")
            logger.info(f"  Total domains: {result.domain_file_stats.get('total_domains', 0)}")
            logger.info(f"  Total files: {result.domain_file_stats.get('total_files', 0)}")
            logger.info(f"  Total tokens: {result.domain_file_stats.get('total_tokens', 0)}")

            # Print processing statistics
            if hasattr(result, 'processing_stats') and result.processing_stats:
                logger.info(f"Processing statistics:")
                logger.info(f"  Total tasks: {result.processing_stats.get('total_tasks', 0)}")
                logger.info(f"  Completed tasks: {result.processing_stats.get('completed_tasks', 0)}")
                logger.info(f"  Failed tasks: {result.processing_stats.get('failed_tasks', 0)}")
                logger.info(f"  Success rate: {result.processing_stats.get('success_rate', 0):.2f}%")
                logger.info(f"  Average task time: {result.processing_stats.get('average_task_time', 0):.2f}s")
                logger.info(f"  Median task time: {result.processing_stats.get('median_task_time', 0):.2f}s")
                logger.info(f"  Tasks per second: {result.processing_stats.get('tasks_per_second', 0):.2f}")

            # Print OpenRouter usage if available
            if hasattr(result, 'openrouter_info') and result.openrouter_info:
                logger.info(f"OpenRouter usage:")
                logger.info(f"  Domains using OpenRouter: {result.openrouter_info.get('openrouter_domains_count', 0)}")
                logger.info(f"  Total OpenRouter calls: {result.openrouter_info.get('openrouter_calls_total', 0)}")
                logger.info(f"  Completed OpenRouter calls: {result.openrouter_info.get('openrouter_calls_completed', 0)}")

            return 0
        else:
            logger.error(f"Diagram generation failed: {result.error_message}")
            return 1

    except Exception as e:
        logger.error(f"Error in diagram generation: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    asyncio.run(main())
