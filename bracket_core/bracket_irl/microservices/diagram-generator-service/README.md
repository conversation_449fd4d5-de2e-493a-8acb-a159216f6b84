# Diagram Generator Service

This microservice is responsible for generating Mermaid diagrams from domain-file repomaps. It uses LLMs to create visual representations of domains and their relationships.

## Features

- Generate Mermaid diagrams from domain-file repomaps
- Support for multiple LLM providers (OpenAI, Claude, OpenRouter)
- Process domains in parallel for improved performance
- REST API for integration with other services
- Background job processing with status tracking

## API Endpoints

- `POST /api/v1/generate`: Generate diagrams
- `GET /api/v1/status/{job_id}`: Get job status
- `GET /api/v1/artifacts/{job_id}`: Get job artifacts
- `GET /api/v1/diagrams/{diagram_id}`: Get specific diagram
- `GET /api/v1/health`: Health check

## Configuration

The service can be configured using environment variables or a configuration file. See the `config.py` file for available configuration options.

## Installation

```bash
# Install dependencies
pip install -r requirements.txt

# Run the service
python -m src.main
```

## Docker

```bash
# Build the Docker image
docker build -t diagram-generator-service .

# Run the Docker container
docker run -p 8004:8004 diagram-generator-service
```

## Usage

```bash
# Generate diagrams
curl -X POST http://localhost:8004/api/v1/generate \
  -H "Content-Type: application/json" \
  -d '{
    "domain_file_repomap_path": "/path/to/domain_file_repomap.json",
    "model_type": "openai",
    "openai_model": "gpt-4o-mini"
  }'

# Get job status
curl -X GET http://localhost:8004/api/v1/status/{job_id}

# Get job artifacts
curl -X GET http://localhost:8004/api/v1/artifacts/{job_id}

# Get specific diagram
curl -X GET http://localhost:8004/api/v1/diagrams/{diagram_id}
```
