"""
API routes for the Diagram Generator Service.
"""

import os
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, BackgroundTasks, HTTPException, Depends, Path, Query

from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.models import DiagramGeneratorRequest, Job, JobStatus, Artifact, ArtifactType
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.storage import StorageClient, get_storage_client

from src.api.models import GenerateDiagramsResponse, JobStatusResponse, ArtifactResponse, DiagramResponse, ErrorResponse
from src.core.config import get_config
from src.services.job_service import JobService
from src.services.generator import DiagramGeneratorService

# Create router
router = APIRouter(tags=["Diagram Generator"])

# Get dependencies
def get_job_service() -> JobService:
    """Get job service."""
    config = get_config()
    storage_client = get_storage_client(
        storage_type=config.storage_type,
        base_path=config.storage_path
    )
    return JobService(storage_client=storage_client)

def get_generator_service() -> DiagramGeneratorService:
    """Get diagram generator service."""
    config = get_config()
    storage_client = get_storage_client(
        storage_type=config.storage_type,
        base_path=config.storage_path
    )
    return DiagramGeneratorService(
        storage_client=storage_client,
        job_service=JobService(storage_client=storage_client),
        max_concurrent_tasks=config.max_concurrent_tasks
    )

@router.post(
    "/generate",
    response_model=GenerateDiagramsResponse,
    responses={
        400: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def generate_diagrams(
    request: DiagramGeneratorRequest,
    background_tasks: BackgroundTasks,
    job_service: JobService = Depends(get_job_service),
    generator_service: DiagramGeneratorService = Depends(get_generator_service)
):
    """
    Generate diagrams from domain-file repomap.

    Args:
        request: Diagram generation request
        background_tasks: Background tasks
        job_service: Job service
        generator_service: Diagram generator service

    Returns:
        Job ID and message
    """
    try:
        # Validate input file
        domain_file_repomap_path = request.domain_file_repomap_path

        # If the path is relative, make it absolute
        if not domain_file_repomap_path.startswith('/'):
            domain_file_repomap_path = f"/app/data/{domain_file_repomap_path}"

        # Check if the file exists
        if not os.path.exists(domain_file_repomap_path):
            # Try to find the file in the artifacts directory
            if "artifacts/" in domain_file_repomap_path:
                # Extract the job ID and file name from the path
                parts = domain_file_repomap_path.split('/')
                job_id = None
                for i, part in enumerate(parts):
                    if part == "artifacts" and i+1 < len(parts):
                        job_id = parts[i+1]
                        break

                if job_id:
                    # Try different extensions
                    for ext in ['.json', '.yaml', '.yml']:
                        alt_path = f"/app/data/artifacts/{job_id}/domain_file_repomap{ext}"
                        if os.path.exists(alt_path):
                            domain_file_repomap_path = alt_path
                            break

            # If still not found, raise an error
            if not os.path.exists(domain_file_repomap_path):
                raise HTTPException(
                    status_code=400,
                    detail=f"Domain-file repomap file not found: {request.domain_file_repomap_path}"
                )

        # Create job
        job = job_service.create_job()

        # Start background task
        background_tasks.add_task(
            generator_service.generate_diagrams,
            job_id=job.job_id,
            domain_file_repomap_path=domain_file_repomap_path,  # Use the resolved path
            model_type=request.model_type,
            openai_model=request.openai_model,
            use_openrouter=request.use_openrouter,
            openrouter_model=request.openrouter_model,
            max_concurrent_tasks=request.max_concurrent_tasks
        )

        return GenerateDiagramsResponse(
            job_id=job.job_id,
            message="Diagram generation started"
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start diagram generation: {str(e)}"
        )

@router.get(
    "/status/{job_id}",
    response_model=JobStatusResponse,
    responses={
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def get_job_status(
    job_id: str = Path(..., description="Job ID"),
    job_service: JobService = Depends(get_job_service)
):
    """
    Get job status.

    Args:
        job_id: Job ID
        job_service: Job service

    Returns:
        Job status
    """
    try:
        job = job_service.get_job(job_id)
        if not job:
            raise HTTPException(
                status_code=404,
                detail=f"Job not found: {job_id}"
            )

        return JobStatusResponse(job=job)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get job status: {str(e)}"
        )

@router.get(
    "/artifacts/{job_id}",
    response_model=ArtifactResponse,
    responses={
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def get_job_artifacts(
    job_id: str = Path(..., description="Job ID"),
    include_content: bool = Query(False, description="Include artifact content"),
    job_service: JobService = Depends(get_job_service)
):
    """
    Get job artifacts.

    Args:
        job_id: Job ID
        include_content: Whether to include artifact content
        job_service: Job service

    Returns:
        Job artifacts
    """
    try:
        job = job_service.get_job(job_id)
        if not job:
            raise HTTPException(
                status_code=404,
                detail=f"Job not found: {job_id}"
            )

        # Check for artifacts in job result
        if not job.result or "artifacts" not in job.result or not job.result["artifacts"]:
            raise HTTPException(
                status_code=404,
                detail=f"No artifacts found for job: {job_id}"
            )

        # Get the first artifact (there should only be one)
        artifact_data = job.result["artifacts"][0]
        artifact = Artifact(**artifact_data)

        # Include content if requested
        content = None
        if include_content:
            content = job_service.get_artifact_content(artifact.artifact_id)

        return ArtifactResponse(
            artifact=artifact,
            content=content
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get job artifacts: {str(e)}"
        )

@router.get(
    "/diagrams/{job_id}/{domain}",
    response_model=DiagramResponse,
    responses={
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def get_diagram(
    job_id: str = Path(..., description="Job ID"),
    domain: str = Path(..., description="Domain name"),
    job_service: JobService = Depends(get_job_service)
):
    """
    Get specific diagram.

    Args:
        job_id: Job ID
        domain: Domain name
        job_service: Job service

    Returns:
        Diagram
    """
    try:
        job = job_service.get_job(job_id)
        if not job:
            raise HTTPException(
                status_code=404,
                detail=f"Job not found: {job_id}"
            )

        # Check for artifacts in job result
        if not job.result or "artifacts" not in job.result or not job.result["artifacts"]:
            raise HTTPException(
                status_code=404,
                detail=f"No artifacts found for job: {job_id}"
            )

        # Get the first artifact (there should only be one)
        artifact_data = job.result["artifacts"][0]
        artifact = Artifact(**artifact_data)

        # Get artifact content
        content = job_service.get_artifact_content(artifact.artifact_id)

        # Find the diagram for the specified domain
        if domain not in content:
            raise HTTPException(
                status_code=404,
                detail=f"Diagram not found for domain: {domain}"
            )

        return DiagramResponse(
            domain=domain,
            diagram=content[domain]
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get diagram: {str(e)}"
        )
