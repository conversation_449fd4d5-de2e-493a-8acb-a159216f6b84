"""
Job service for the Diagram Generator Service.
"""

import os
import json
import uuid
import time
from typing import Dict, List, Any, Optional

from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.models import Job, JobStatus, Artifact, ArtifactType
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.storage import StorageClient

class JobService:
    """
    Job service for managing jobs and artifacts.
    """

    def __init__(self, storage_client: StorageClient):
        """
        Initialize job service.

        Args:
            storage_client: Storage client
        """
        self.storage_client = storage_client

    def create_job(self) -> Job:
        """
        Create a new job.

        Returns:
            Job object
        """
        job_id = str(uuid.uuid4())
        job = Job(
            job_id=job_id,
            status=JobStatus.PENDING,
            created_at=time.time(),
            updated_at=time.time(),
            progress=0.0,
            message="Job created"
        )

        # Save job
        self.storage_client.write_json(
            f"jobs/{job_id}.json",
            job.model_dump()
        )

        return job

    def get_job(self, job_id: str) -> Optional[Job]:
        """
        Get job by ID.

        Args:
            job_id: Job ID

        Returns:
            Job object or None if not found
        """
        try:
            job_data = self.storage_client.read_json(f"jobs/{job_id}.json")
            return Job(**job_data)
        except Exception:
            return None

    def update_job(self, job: Job) -> Job:
        """
        Update job.

        Args:
            job: Job object

        Returns:
            Updated job object
        """
        job.updated_at = time.time()

        # Save job
        self.storage_client.write_json(
            f"jobs/{job.job_id}.json",
            job.model_dump()
        )

        return job

    def add_artifact(self, job_id: str, artifact_path: str, artifact_type: ArtifactType) -> Artifact:
        """
        Add artifact to job.

        Args:
            job_id: Job ID
            artifact_path: Path to artifact
            artifact_type: Artifact type

        Returns:
            Artifact object
        """
        job = self.get_job(job_id)
        if not job:
            raise ValueError(f"Job not found: {job_id}")

        artifact_id = str(uuid.uuid4())
        artifact = Artifact(
            artifact_id=artifact_id,
            job_id=job_id,
            artifact_type=artifact_type,
            created_at=time.time(),
            path=artifact_path
        )

        # Save artifact separately
        self.storage_client.write_json(
            f"artifacts/{artifact_id}.json",
            artifact.model_dump()
        )

        # Store artifact reference in job result
        if not job.result:
            job.result = {}

        if "artifacts" not in job.result:
            job.result["artifacts"] = []

        job.result["artifacts"].append(artifact.model_dump())

        # Update job
        self.update_job(job)

        return artifact

    def get_artifact_content(self, artifact_id: str) -> Dict[str, Any]:
        """
        Get artifact content.

        Args:
            artifact_id: Artifact ID

        Returns:
            Artifact content
        """
        # Try to find artifact directly
        try:
            artifact_data = self.storage_client.read_json(f"artifacts/{artifact_id}.json")
            artifact = Artifact(**artifact_data)
            return self.storage_client.read_json(artifact.path)
        except Exception:
            pass

        # If not found, search in job results
        for job_file in self.storage_client.list_files("jobs"):
            try:
                job_data = self.storage_client.read_json(job_file)
                job = Job(**job_data)

                if job.result and "artifacts" in job.result:
                    for artifact_data in job.result["artifacts"]:
                        if artifact_data.get("artifact_id") == artifact_id:
                            # Read artifact content
                            return self.storage_client.read_json(artifact_data.get("path"))
            except Exception:
                continue

        raise ValueError(f"Artifact not found: {artifact_id}")
