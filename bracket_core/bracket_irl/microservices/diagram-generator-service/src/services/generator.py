"""
Diagram generator service for the Diagram Generator Service.
"""

import os
import json
import time
import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple

from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.models import Job, JobStatus, Artifact, ArtifactType
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.storage import StorageClient
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.clients.llm_client import get_llm_client
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.utils.token_counter import num_tokens_from_string

from bracket_irl.bracket_domain_diagram_generator import JSONDomainDiagramGenerator

from src.services.job_service import JobService

# Configure logging
logger = logging.getLogger(__name__)

class DiagramGeneratorService:
    """
    Diagram generator service.
    """

    def __init__(self, storage_client: StorageClient, job_service: JobService, max_concurrent_tasks: int = 30):
        """
        Initialize diagram generator service.

        Args:
            storage_client: Storage client
            job_service: Job service
            max_concurrent_tasks: Maximum concurrent tasks
        """
        self.storage_client = storage_client
        self.job_service = job_service
        self.max_concurrent_tasks = max_concurrent_tasks

    async def generate_diagrams(
        self,
        job_id: str,
        domain_file_repomap_path: str,
        model_type: str = "openai",
        openai_model: str = "gpt-4o-mini",
        use_openrouter: bool = False,
        openrouter_model: str = "google/gemini-2.5-pro-preview",
        max_concurrent_tasks: int = 30
    ) -> None:
        """
        Generate diagrams from domain-file repomap.

        Args:
            job_id: Job ID
            domain_file_repomap_path: Path to domain-file repomap JSON file
            model_type: Model type (openai or claude)
            openai_model: OpenAI model to use
            use_openrouter: Whether to use OpenRouter
            openrouter_model: OpenRouter model to use
            max_concurrent_tasks: Maximum concurrent tasks
        """
        try:
            # Get job
            job = self.job_service.get_job(job_id)
            if not job:
                logger.error(f"Job not found: {job_id}")
                return

            # Update job status
            job.status = JobStatus.RUNNING
            self.job_service.update_job(job)

            # Create output directory
            output_dir = os.path.join(self.storage_client.base_path, "artifacts", job_id)
            os.makedirs(output_dir, exist_ok=True)

            # Generate diagrams
            output_path = os.path.join(output_dir, "diagrams.json")

            # Create JSONDomainDiagramGenerator instance
            generator = JSONDomainDiagramGenerator(
                domain_file_json_path=domain_file_repomap_path,
                output_dir=output_dir,
                model_type=model_type,
                openai_model=openai_model,
                use_openrouter=use_openrouter,
                openrouter_model=openrouter_model,
                max_concurrent_tasks=max_concurrent_tasks
            )

            # Generate diagrams
            start_time = time.time()
            result = await generator.generate_all_diagrams()  # Changed from generate_diagrams to generate_all_diagrams
            elapsed_time = time.time() - start_time

            # Save diagrams to a single JSON file
            diagrams = {}
            for domain, diagram_path in result.diagram_files.items():  # Changed from domain_diagrams to diagram_files
                with open(diagram_path, "r") as f:
                    diagrams[domain] = f.read()

            self.storage_client.write_json(output_path, diagrams)

            # Add artifact to job
            artifact = self.job_service.add_artifact(
                job_id=job_id,
                artifact_path=output_path,
                artifact_type=ArtifactType.DIAGRAM
            )

            # Update job status
            job.status = JobStatus.COMPLETED

            # Store stats in the result field instead of a non-existent stats field
            if not job.result:
                job.result = {}

            job.result["stats"] = {
                "elapsed_time": elapsed_time,
                "domain_count": len(diagrams),
                "successful_domains": len(result.diagram_files),
                "failed_domains": result.processing_stats.get("failed_tasks", 0)
            }
            self.job_service.update_job(job)

            logger.info(f"Diagram generation completed for job {job_id}")
        except Exception as e:
            logger.error(f"Diagram generation failed for job {job_id}: {str(e)}")

            # Update job status
            job = self.job_service.get_job(job_id)
            if job:
                job.status = JobStatus.FAILED
                job.error = str(e)
                self.job_service.update_job(job)
