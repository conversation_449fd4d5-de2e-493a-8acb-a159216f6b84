# Bracket IRL Microservices Architecture

## Overview

This document provides a comprehensive overview of the Bracket IRL microservices architecture, which transforms the monolithic Bracket IRL pipeline into a scalable, cloud-ready system. The architecture follows modern microservices principles, with each component focusing on a specific responsibility within the overall pipeline.

## Architecture

The Bracket IRL pipeline has been decomposed into six specialized microservices, each with its own API, storage, and processing capabilities:

1. **Repository Mapper Service**: Extracts code structure from repositories
2. **Domain Analyzer Service**: Analyzes code to identify domains
3. **File-Domain Mapper Service**: Maps files to identified domains
4. **Domain-File Repomap Service**: Generates domain-file relationship maps
5. **Diagram Generator Service**: Creates visual diagrams of the codebase
6. **Orchestrator Service**: Coordinates the entire pipeline workflow

These services are supported by a common infrastructure layer (`bracket-irl-common`) that provides shared utilities and standardized interfaces.

### System Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────────────┐
│                           Client Applications                            │
└───────────────────────────────────┬─────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────────────┐
│                         Orchestrator Service                             │
└───────┬───────────┬───────────┬───────────┬───────────┬─────────────────┘
        │           │           │           │           │
        ▼           ▼           ▼           ▼           ▼
┌───────────┐ ┌───────────┐ ┌───────────┐ ┌───────────┐ ┌───────────┐
│ Repository│ │  Domain   │ │File-Domain│ │Domain-File│ │  Diagram  │
│  Mapper   │ │ Analyzer  │ │  Mapper   │ │  Repomap  │ │ Generator │
│  Service  │ │  Service  │ │  Service  │ │  Service  │ │  Service  │
└───────────┘ └───────────┘ └───────────┘ └───────────┘ └───────────┘
        │           │           │           │           │
        └───────────┴───────────┴───────────┴───────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────────────┐
│                       bracket-irl-common                                 │
│                                                                         │
│  ┌─────────┐  ┌─────────┐  ┌─────────┐  ┌─────────┐  ┌─────────┐        │
│  │ Config  │  │ Logging │  │ Storage │  │ Metrics │  │ Health  │        │
│  └─────────┘  └─────────┘  └─────────┘  └─────────┘  └─────────┘        │
│                                                                         │
│  ┌─────────┐  ┌─────────┐  ┌─────────┐  ┌─────────┐  ┌─────────┐        │
│  │  Error  │  │   LLM   │  │  Token  │  │  Rate   │  │ Models  │        │
│  │ Handling│  │ Clients │  │ Counter │  │ Limiter │  │         │        │
│  └─────────┘  └─────────┘  └─────────┘  └─────────┘  └─────────┘        │
└─────────────────────────────────────────────────────────────────────────┘
```

## Implementation Details

### Common Infrastructure Layer (bracket-irl-common)

The common infrastructure layer provides shared utilities and standardized interfaces for all microservices:

- **Configuration Management**: Centralized configuration with environment variable support
- **Logging**: Structured logging with consistent formatting
- **Storage Abstraction**: Unified interface for local and cloud storage
- **Metrics Collection**: Prometheus metrics for monitoring
- **Health Checks**: Standardized health check utilities
- **Error Handling**: Consistent error handling and reporting
- **LLM Clients**: Unified interface for different LLM providers
- **Token Counting**: Utilities for counting tokens in text
- **Rate Limiting**: Utilities for rate limiting API calls
- **Data Models**: Shared data models for consistent data representation

### Repository Mapper Service

This service extracts code structure from repositories, identifying functions, classes, and other code elements:

- **API Endpoints**:
  - `POST /api/v1/generate`: Generate repository map
  - `GET /api/v1/status/{job_id}`: Get job status
  - `GET /api/v1/artifacts/{job_id}`: Get job artifacts
  - `GET /api/v1/health`: Basic health check
  - `GET /api/v1/health/details`: Detailed health check
  - `GET /metrics`: Prometheus metrics

- **Key Features**:
  - Background job processing
  - Batch processing for large codebases
  - Filtering based on importance
  - Support for multiple programming languages
  - Prometheus metrics for monitoring
  - Detailed health checks

### Domain Analyzer Service

This service analyzes repository maps to identify domains and subdomains within a codebase:

- **API Endpoints**:
  - `POST /api/v1/analyze`: Analyze repository map
  - `GET /api/v1/status/{job_id}`: Get job status
  - `GET /api/v1/artifacts/{job_id}`: Get job artifacts
  - `GET /api/v1/health`: Basic health check
  - `GET /api/v1/health/details`: Detailed health check
  - `GET /metrics`: Prometheus metrics

- **Key Features**:
  - LLM integration for domain analysis
  - Support for multiple LLM providers (OpenAI, Claude, OpenRouter)
  - Parallel processing for large codebases
  - Configurable chunking for token limits
  - Prometheus metrics for monitoring
  - Detailed health checks

### File-Domain Mapper Service

This service maps files to identified domains:

- **API Endpoints**:
  - `POST /api/v1/map`: Map files to domains
  - `GET /api/v1/status/{job_id}`: Get job status
  - `GET /api/v1/artifacts/{job_id}`: Get job artifacts
  - `GET /api/v1/health`: Basic health check
  - `GET /api/v1/health/details`: Detailed health check
  - `GET /metrics`: Prometheus metrics

- **Key Features**:
  - LLM integration for file-domain mapping
  - Batch processing for large codebases
  - Configurable mapping parameters
  - Prometheus metrics for monitoring
  - Detailed health checks

### Domain-File Repomap Service

This service generates domain-file relationship maps:

- **API Endpoints**:
  - `POST /api/v1/generate`: Generate domain-file repomap
  - `GET /api/v1/status/{job_id}`: Get job status
  - `GET /api/v1/artifacts/{job_id}`: Get job artifacts
  - `GET /api/v1/health`: Basic health check
  - `GET /api/v1/health/details`: Detailed health check
  - `GET /metrics`: Prometheus metrics

- **Key Features**:
  - Statistical analysis of domains
  - Hierarchical domain structure
  - Configurable filtering options
  - Prometheus metrics for monitoring
  - Detailed health checks

### Diagram Generator Service

This service creates visual diagrams of the codebase:

- **API Endpoints**:
  - `POST /api/v1/generate`: Generate diagrams
  - `GET /api/v1/status/{job_id}`: Get job status
  - `GET /api/v1/artifacts/{job_id}`: Get job artifacts
  - `GET /api/v1/diagrams/{diagram_id}`: Get specific diagram
  - `GET /api/v1/health`: Basic health check
  - `GET /api/v1/health/details`: Detailed health check
  - `GET /metrics`: Prometheus metrics

- **Key Features**:
  - Mermaid diagram generation
  - Customizable diagram styles
  - Caching for improved performance
  - Prometheus metrics for monitoring
  - Detailed health checks

### Orchestrator Service

This service coordinates the entire pipeline workflow:

- **API Endpoints**:
  - `POST /api/v1/repositories`: Process repository
  - `GET /api/v1/jobs/{job_id}`: Get job status
  - `GET /api/v1/artifacts/{job_id}`: Get job artifacts
  - `GET /api/v1/health`: Basic health check
  - `GET /api/v1/health/details`: Detailed health check
  - `GET /metrics`: Prometheus metrics

- **Key Features**:
  - End-to-end pipeline orchestration
  - Job management with persistent storage
  - Client interfaces for all services
  - Prometheus metrics for monitoring
  - Detailed health checks

## Containerization and Deployment

Each microservice includes:

- **Dockerfile**: For containerization with proper multi-stage builds and optimized layers
- **Requirements**: Dependencies for the service with pinned versions
- **Configuration**: Environment variables and configuration files with sensible defaults
- **Documentation**: README with detailed usage instructions and examples

The services can be deployed using:

- **Docker Compose**: For local development and testing
- **Kubernetes**: For production deployment with proper resource allocation

### Docker Configuration

Each service's Dockerfile follows best practices:

```dockerfile
FROM python:3.10-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy the bracket-irl-common package
COPY ../bracket-irl-common /app/bracket-irl-common
RUN pip install -e /app/bracket-irl-common

# Copy application code
COPY src/ /app/src/

# Set environment variables
ENV PYTHONPATH=/app
ENV SERVICE_NAME=service-name
ENV HOST=0.0.0.0
ENV PORT=8000

# Expose port
EXPOSE 8000

# Create data directory
RUN mkdir -p /app/data/jobs /app/data/artifacts

# Run the application
CMD ["uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Port Allocation

Services are assigned specific ports to avoid conflicts:

- **Repository Mapper Service**: 8001
- **Domain Analyzer Service**: 8002
- **File-Domain Mapper Service**: 8003
- **Domain-File Repomap Service**: 8004
- **Diagram Generator Service**: 8005
- **Orchestrator Service**: 8000

## Testing

Each microservice includes comprehensive tests:

- **Unit Tests**: For individual components using pytest
- **API Tests**: For API endpoints using TestClient
- **Integration Tests**: For service interactions with mocked dependencies
- **Metrics Tests**: For verifying Prometheus metrics
- **Health Check Tests**: For verifying health check endpoints

### Test Configuration

Tests are configured using pytest.ini:

```ini
[pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
asyncio_mode = auto
markers =
    unit: Unit tests
    integration: Integration tests
    api: API tests
    slow: Slow tests
    fast: Fast tests
```

### Test Examples

Unit tests for services:

```python
@pytest.mark.asyncio
@patch("src.services.mapper.CompleteRepoMap")
async def test_generate_repository_map_success(mock_complete_repomap, mapper_service, job_service):
    # Mock CompleteRepoMap
    mock_repomap_instance = MagicMock()
    mock_repomap_instance.generate_complete_map.return_value = {"file1.py": "content1", "file2.py": "content2"}
    mock_repomap_instance.filter_map.return_value = {"file1.py": "filtered_content1"}
    mock_complete_repomap.return_value = mock_repomap_instance

    # Call generate_repository_map
    await mapper_service.generate_repository_map(
        job_id="test-job-id",
        repo_dir="/tmp/test-repo"
    )

    # Assert job status was updated correctly
    job_service.update_job.assert_any_call(
        job_id="test-job-id",
        status=JobStatus.COMPLETED,
        progress=1.0,
        message="Repository map generation completed",
        result={
            "repomap_artifact_id": "test-artifact-id",
            "filtered_repomap_artifact_id": "test-artifact-id"
        }
    )
```

API tests:

```python
def test_analyze_domains(mock_job_service, mock_analyzer_service):
    # Create request data
    request_data = {
        "repomap_path": "artifacts/test-job-id/repomap/repomap.json",
        "api_key": "test-api-key",
        "model": "gpt-4o-mini"
    }

    # Send request
    response = client.post("/api/v1/analyze", json=request_data)

    # Assert response
    assert response.status_code == 200
    assert response.json()["job_id"] == "test-job-id"
    assert response.json()["message"] == "Domain analysis started"
```

## Monitoring and Observability

The microservices architecture includes:

- **Prometheus Metrics**: For monitoring service performance
  - Request counts and latencies
  - Job counts and latencies
  - LLM request counts and token usage
  - Active job counts

- **Health Checks**: For service status monitoring
  - Basic health check endpoint
  - Detailed health check with component status
  - Storage health checks
  - Dependency health checks

- **Structured Logging**: For log analysis
  - JSON-formatted logs
  - Consistent log levels
  - Request ID tracking
  - Error tracing

### Metrics Examples

```python
# Metrics initialization
request_counter = counter(
    name="service_requests_total",
    description="Total number of requests",
    labels={"service": config.service_name}
)

request_latency = histogram(
    name="service_request_latency_seconds",
    description="Request latency in seconds",
    buckets=[0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10],
    labels={"service": config.service_name}
)

# Metrics usage
request_counter.inc()
request_latency.observe(time.time() - start_time)
```

### Health Check Examples

```python
@app.get("/health/details")
async def detailed_health_check():
    """Detailed health check endpoint."""
    health_result = await health_service.check_health()
    return {
        "status": health_result.status,
        "service": config.service_name,
        "checks": [check.__dict__ for check in health_result.checks],
        "timestamp": health_result.timestamp
    }
```

## Implementation Progress

The following components have been fully implemented:

1. **Common Infrastructure Layer (bracket-irl-common)**:
   - Configuration management
   - Logging utilities
   - Storage abstraction
   - Metrics collection
   - Health check utilities
   - Error handling
   - LLM clients
   - Token counting
   - Rate limiting
   - Data models

2. **Repository Mapper Service**:
   - API endpoints
   - Background job processing
   - Metrics and health checks
   - Comprehensive tests
   - Dockerfile and deployment configuration
   - Detailed documentation

3. **Domain Analyzer Service**:
   - API endpoints
   - LLM integration
   - Metrics and health checks
   - Comprehensive tests
   - Dockerfile and deployment configuration
   - Detailed documentation

The following components are partially implemented and need completion:

1. **File-Domain Mapper Service**
2. **Domain-File Repomap Service**
3. **Diagram Generator Service**
4. **Orchestrator Service**

## Testing Framework

A comprehensive testing framework has been implemented to test the Bracket IRL microservices both locally and in preparation for GCP/GKE deployment. The framework includes:

### E2E Pipeline Testing

The E2E pipeline testing framework tests the entire pipeline from repository mapping to diagram generation:

- Creates a test repository with a known structure
- Tests each service's API endpoints
- Verifies the correct flow of data between services
- Validates the final output artifacts

To run the E2E pipeline tests:

```bash
cd bracket_core/bracket_irl/microservices
python tests/e2e/test_e2e_pipeline.py --repo-dir path/to/repo --api-key your-openai-api-key
```

### Docker Compose Testing

The Docker Compose testing framework tests the containerized deployment of the microservices:

- Verifies that all containers start correctly
- Checks container health and status
- Monitors resource usage
- Tests service communication

To run the Docker Compose tests:

```bash
cd bracket_core/bracket_irl/microservices
python tests/e2e/test_docker_compose.py --compose-file docker-compose.yml --repo-dir path/to/repo --api-key your-openai-api-key
```

### Kubernetes Testing

The Kubernetes testing framework tests the deployment to a local Kubernetes cluster using Minikube:

- Sets up a Minikube cluster
- Deploys the microservices using Kubernetes manifests
- Verifies pod health and status
- Tests service communication through Kubernetes networking

To run the Kubernetes tests:

```bash
cd bracket_core/bracket_irl/microservices
python tests/e2e/test_kubernetes.py --k8s-dir kubernetes/overlays/local --repo-dir path/to/repo --api-key your-openai-api-key
```

### Master Test Runner

A master test runner is provided to run all tests in sequence:

```bash
cd bracket_core/bracket_irl/microservices
python tests/e2e/run_all_tests.py --api-key your-openai-api-key
```

For more details on the testing framework, see the [Testing Framework README](tests/e2e/README.md).

## Kubernetes Deployment

Kubernetes manifests have been created for deploying the microservices to both local Minikube and GKE environments. The manifests are organized using Kustomize:

```
kubernetes/
├── base/                      # Base Kubernetes manifests
└── overlays/
    ├── local/                 # Overlay for local Minikube deployment
    └── gke/                   # Overlay for GKE deployment
```

### Local Deployment

To deploy to Minikube:

```bash
cd bracket_core/bracket_irl/microservices
kubectl apply -k kubernetes/overlays/local
```

### GKE Deployment

To deploy to GKE:

```bash
cd bracket_core/bracket_irl/microservices
kubectl apply -k kubernetes/overlays/gke
```

For detailed instructions on deploying to GCP/GKE, see the [GCP Deployment Guide](docs/gcp_deployment_guide.md).

## Next Steps

1. **Complete Implementation**: Finish implementing the remaining microservices
   - Complete File-Domain Mapper Service
   - Complete Domain-File Repomap Service
   - Complete Diagram Generator Service
   - Complete Orchestrator Service

2. **CI/CD Pipeline**: Set up automated testing and deployment
   - GitHub Actions workflow
   - Build steps
   - Test steps
   - Deployment steps

3. **Documentation**: Complete API documentation
   - OpenAPI specifications
   - Postman collections
   - Usage examples

4. **Performance Testing**: Test with large codebases
   - Benchmark performance
   - Identify bottlenecks
   - Optimize resource usage

## Conclusion

The Bracket IRL microservices architecture transforms the monolithic pipeline into a scalable, cloud-ready system that can handle large codebases efficiently. The architecture follows modern microservices principles, with each component focusing on a specific responsibility within the overall pipeline.

Key benefits of this architecture include:

- **Scalability**: Each service can be scaled independently based on demand
- **Resilience**: Failures in one service don't affect others
- **Maintainability**: Smaller, focused codebases are easier to maintain
- **Deployability**: Services can be deployed independently
- **Observability**: Comprehensive metrics and health checks
- **Performance**: Optimized resource usage and parallel processing

This implementation provides a solid foundation for the Bracket IRL pipeline, enabling it to process large codebases efficiently and reliably in a production environment.


---------
## Getting Started

To start working with the services:

```bash
cd bracket_core/bracket_irl/microservices
export OPENAI_API_KEY=your-openai-api-key
./start.sh
```

This will start all the services using Docker Compose. You can then access the services at:

- Orchestrator Service: http://localhost:8000
- Repository Mapper Service: http://localhost:8001
- Domain Analyzer Service: http://localhost:8002
- File-Domain Mapper Service: http://localhost:8003
- Domain-File Repomap Service: http://localhost:8004
- Diagram Generator Service: http://localhost:8005
- Prometheus: http://localhost:9090
- Grafana: http://localhost:3000