"""
Service clients for communicating with other services.
"""

import json
import httpx
import asyncio
from typing import Dict, List, Any, Optional, Union
import logging

from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.models import (
    Job, JobStatus, Artifact, ArtifactType,
    RepositoryMapRequest, DomainAnalysisRequest,
    FileDomainMapperRequest, DomainFileRepomapRequest, DiagramGeneratorRequest
)

logger = logging.getLogger(__name__)

class ServiceClient:
    """Base client for communicating with services."""

    def __init__(self, base_url: str, timeout: int = 60):
        """
        Initialize service client.

        Args:
            base_url: Base URL of the service
            timeout: Request timeout in seconds
        """
        self.base_url = base_url
        self.timeout = timeout

    async def _request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Make HTTP request to service.

        Args:
            method: HTTP method
            endpoint: API endpoint
            data: Request data
            params: Query parameters

        Returns:
            Response data

        Raises:
            Exception: If request fails
        """
        url = f"{self.base_url}{endpoint}"
        logger.info(f"Making {method} request to {url} with params {params}")

        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                if method.lower() == "get":
                    response = await client.get(url, params=params)
                elif method.lower() == "post":
                    response = await client.post(url, json=data, params=params)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")

                response.raise_for_status()
                return response.json()
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error: {e.response.status_code} - {e.response.text} for URL {url}")
            try:
                error_data = e.response.json()
                error_message = error_data.get("detail", str(e))
            except json.JSONDecodeError:
                error_message = e.response.text or str(e)

            raise Exception(f"Service request failed: {error_message}")
        except httpx.RequestError as e:
            logger.error(f"Request error: {str(e)} for URL {url}")
            raise Exception(f"Service request failed: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error: {str(e)} for URL {url}")
            raise

    async def check_health(self) -> Dict[str, Any]:
        """
        Check service health.

        Returns:
            Health check response
        """
        try:
            return await self._request("get", "/health")
        except Exception as e:
            logger.error(f"Health check failed: {str(e)}")
            return {"status": "down", "error": str(e)}

class RepositoryMapperClient(ServiceClient):
    """Client for Repository Mapper Service."""

    async def generate_repository_map(self, request: RepositoryMapRequest) -> Dict[str, Any]:
        """
        Generate repository map.

        Args:
            request: Repository map request

        Returns:
            Job ID and message
        """
        return await self._request("post", "/api/v1/generate", data=request.model_dump())

    async def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """
        Get job status.

        Args:
            job_id: Job ID

        Returns:
            Job details
        """
        return await self._request("get", f"/api/v1/status/{job_id}")

    async def get_artifact(self, job_id: Optional[str], artifact_type: ArtifactType, include_content: bool = False) -> Dict[str, Any]:
        """
        Get artifact.

        Args:
            job_id: Job ID
            artifact_type: Artifact type
            include_content: Include artifact content

        Returns:
            Artifact details and optionally content
        """
        # Check if job_id is None
        if job_id is None:
            raise Exception(f"Invalid job ID for artifact type {artifact_type.value}")

        return await self._request(
            "get",
            f"/api/v1/artifacts/{job_id}/{artifact_type.value}",
            params={"include_content": include_content}
        )

class DomainAnalyzerClient(ServiceClient):
    """Client for Domain Analyzer Service."""

    async def analyze_domains(self, request: DomainAnalysisRequest) -> Dict[str, Any]:
        """
        Analyze domains.

        Args:
            request: Domain analysis request

        Returns:
            Job ID and message
        """
        return await self._request("post", "/api/v1/analyze", data=request.model_dump())

    async def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """
        Get job status.

        Args:
            job_id: Job ID

        Returns:
            Job details
        """
        return await self._request("get", f"/api/v1/status/{job_id}")

    async def get_artifact(self, job_id: Optional[str], artifact_type: ArtifactType, include_content: bool = False) -> Dict[str, Any]:
        """
        Get artifact.

        Args:
            job_id: Job ID
            artifact_type: Artifact type
            include_content: Include artifact content

        Returns:
            Artifact details and optionally content
        """
        # Check if job_id is None
        if job_id is None:
            raise Exception(f"Invalid job ID for artifact type {artifact_type.value}")

        return await self._request(
            "get",
            f"/api/v1/artifacts/{job_id}/{artifact_type.value}",
            params={"include_content": include_content}
        )

class FileDomainMapperClient(ServiceClient):
    """Client for File-Domain Mapper Service."""

    async def map_files_to_domains(self, request: FileDomainMapperRequest, job_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Map files to domains.

        Args:
            request: File domain mapper request
            job_id: Optional job ID to use instead of creating a new one

        Returns:
            Job ID and message
        """
        # Create a copy of the request data
        request_data = request.model_dump()

        # Add job_id to the request if provided
        if job_id:
            request_data["job_id"] = job_id

        return await self._request("post", "/api/v1/map", data=request_data)

    async def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """
        Get job status.

        Args:
            job_id: Job ID

        Returns:
            Job details
        """
        return await self._request("get", f"/api/v1/status/{job_id}")

    async def get_artifact(self, job_id: Optional[str], artifact_type: ArtifactType, include_content: bool = False) -> Dict[str, Any]:
        """
        Get artifact.

        Args:
            job_id: Job ID
            artifact_type: Artifact type
            include_content: Include artifact content

        Returns:
            Artifact details and optionally content
        """
        # Check if job_id is None
        if job_id is None:
            raise Exception(f"Invalid job ID for artifact type {artifact_type.value}")

        return await self._request(
            "get",
            f"/api/v1/artifacts/{job_id}/{artifact_type.value}",
            params={"include_content": include_content}
        )

class DomainFileRepomapClient(ServiceClient):
    """Client for Domain-File Repomap Service."""

    async def generate_domain_file_repomap(self, request: DomainFileRepomapRequest, job_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate domain-file repomap.

        Args:
            request: Domain file repomap request
            job_id: Optional job ID to use instead of creating a new one

        Returns:
            Job ID and message
        """
        # Create a copy of the request data
        request_data = request.model_dump()

        # Add job_id to the request if provided
        if job_id:
            request_data["job_id"] = job_id

        return await self._request("post", "/api/v1/generate", data=request_data)

    async def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """
        Get job status.

        Args:
            job_id: Job ID

        Returns:
            Job details
        """
        return await self._request("get", f"/api/v1/status/{job_id}")

    async def get_artifact(self, job_id: Optional[str], artifact_type: ArtifactType, include_content: bool = False) -> Dict[str, Any]:
        """
        Get artifact.

        Args:
            job_id: Job ID
            artifact_type: Artifact type
            include_content: Include artifact content

        Returns:
            Artifact details and optionally content
        """
        # Check if job_id is None
        if job_id is None:
            raise Exception(f"Invalid job ID for artifact type {artifact_type.value}")

        return await self._request(
            "get",
            f"/api/v1/artifacts/{job_id}/{artifact_type.value}",
            params={"include_content": include_content}
        )

class DiagramGeneratorClient(ServiceClient):
    """Client for Diagram Generator Service."""

    async def generate_diagrams(self, request: DiagramGeneratorRequest) -> Dict[str, Any]:
        """
        Generate diagrams.

        Args:
            request: Diagram generator request

        Returns:
            Job ID and message
        """
        return await self._request("post", "/api/v1/generate", data=request.model_dump())

    async def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """
        Get job status.

        Args:
            job_id: Job ID

        Returns:
            Job details
        """
        return await self._request("get", f"/api/v1/status/{job_id}")

    async def get_artifact(self, job_id: Optional[str], artifact_type: ArtifactType, include_content: bool = False) -> Dict[str, Any]:
        """
        Get artifact.

        Args:
            job_id: Job ID
            artifact_type: Artifact type
            include_content: Include artifact content

        Returns:
            Artifact details and optionally content
        """
        # Check if job_id is None
        if job_id is None:
            raise Exception(f"Invalid job ID for artifact type {artifact_type.value}")

        return await self._request(
            "get",
            f"/api/v1/artifacts/{job_id}/{artifact_type.value}",
            params={"include_content": include_content}
        )

    async def get_diagram(self, diagram_id: str) -> Dict[str, Any]:
        """
        Get diagram.

        Args:
            diagram_id: Diagram ID

        Returns:
            Diagram details
        """
        return await self._request("get", f"/api/v1/diagrams/{diagram_id}")
