"""
API models for the Orchestrator Service.
"""

from typing import Dict, List, Any, Optional, Union
from pydantic import BaseModel, Field

from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.models import (
    Job, Artifact, RepositoryMapRequest, DomainAnalysisRequest, 
    FileDomainMapperRequest, DomainFileRepomapRequest, DiagramGeneratorRequest
)

class ProcessRepositoryResponse(BaseModel):
    """Response model for process repository endpoint."""
    job_id: str = Field(..., description="Job ID")
    message: str = Field(..., description="Response message")

class JobStatusResponse(BaseModel):
    """Response model for job status endpoint."""
    job: Job = Field(..., description="Job details")

class ArtifactResponse(BaseModel):
    """Response model for artifact endpoint."""
    artifact: Artifact = Field(..., description="Artifact details")
    content: Optional[Any] = Field(None, description="Artifact content")

class ErrorResponse(BaseModel):
    """Error response model."""
    detail: str = Field(..., description="Error details")

class JobListResponse(BaseModel):
    """Response model for job list endpoint."""
    jobs: List[Job] = Field(..., description="List of jobs")

class ServiceStatusResponse(BaseModel):
    """Response model for service status endpoint."""
    service_name: str = Field(..., description="Service name")
    status: str = Field(..., description="Service status")
    url: str = Field(..., description="Service URL")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional details")

class PipelineStatusResponse(BaseModel):
    """Response model for pipeline status endpoint."""
    services: List[ServiceStatusResponse] = Field(..., description="List of service statuses")
    overall_status: str = Field(..., description="Overall pipeline status")
