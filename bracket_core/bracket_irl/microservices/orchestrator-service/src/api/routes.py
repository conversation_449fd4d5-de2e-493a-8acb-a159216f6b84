"""
API routes for the Orchestrator Service.
"""

import os
from typing import Dict, List, Any, Optional, Union
from fastapi import APIRouter, BackgroundTasks, HTTPException, Depends, Path, Query

from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.models import (
    Job, JobStatus, Artifact, ArtifactType, OrchestrationRequest
)
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.storage import StorageClient, get_storage_client

from src.api.models import (
    ProcessRepositoryResponse, JobStatusResponse, ArtifactResponse, 
    ErrorResponse, JobListResponse, PipelineStatusResponse
)
from src.core.config import get_config
from src.services.job_service import JobService
from src.services.orchestrator import OrchestratorService

# Create router
router = APIRouter(tags=["Orchestrator"])

# Get dependencies
def get_job_service() -> JobService:
    """Get job service."""
    config = get_config()
    storage_client = get_storage_client(
        storage_type=config.storage_type,
        base_path=config.storage_path
    )
    return JobService(storage_client=storage_client)

def get_orchestrator_service() -> OrchestratorService:
    """Get orchestrator service."""
    config = get_config()
    storage_client = get_storage_client(
        storage_type=config.storage_type,
        base_path=config.storage_path
    )
    job_service = JobService(storage_client=storage_client)
    return OrchestratorService(
        storage_client=storage_client,
        job_service=job_service,
        max_concurrent_tasks=config.max_concurrent_tasks
    )

@router.post(
    "/repositories",
    response_model=ProcessRepositoryResponse,
    responses={
        400: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def process_repository(
    request: OrchestrationRequest,
    background_tasks: BackgroundTasks,
    job_service: JobService = Depends(get_job_service),
    orchestrator_service: OrchestratorService = Depends(get_orchestrator_service)
):
    """
    Process repository through the entire pipeline.
    
    Args:
        request: Orchestration request
        background_tasks: Background tasks
        job_service: Job service
        orchestrator_service: Orchestrator service
    
    Returns:
        Job ID and message
    """
    try:
        # Validate repository directory
        if not os.path.exists(request.repo_dir):
            raise HTTPException(
                status_code=400,
                detail=f"Repository directory not found: {request.repo_dir}"
            )
        
        # Create job
        job = job_service.create_job()
        
        # Start background task
        background_tasks.add_task(
            orchestrator_service.process_repository,
            job_id=job.job_id,
            request=request
        )
        
        return ProcessRepositoryResponse(
            job_id=job.job_id,
            message="Repository processing started"
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start repository processing: {str(e)}"
        )

@router.get(
    "/jobs/{job_id}",
    response_model=JobStatusResponse,
    responses={
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def get_job_status(
    job_id: str = Path(..., description="Job ID"),
    job_service: JobService = Depends(get_job_service)
):
    """
    Get job status.
    
    Args:
        job_id: Job ID
        job_service: Job service
    
    Returns:
        Job details
    """
    try:
        # Get job
        job = job_service.get_job(job_id)
        if not job:
            raise HTTPException(
                status_code=404,
                detail=f"Job not found: {job_id}"
            )
        
        return JobStatusResponse(job=job)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get job status: {str(e)}"
        )

@router.get(
    "/jobs",
    response_model=JobListResponse,
    responses={
        500: {"model": ErrorResponse}
    }
)
async def list_jobs(
    job_service: JobService = Depends(get_job_service)
):
    """
    List all jobs.
    
    Args:
        job_service: Job service
    
    Returns:
        List of jobs
    """
    try:
        # Get all jobs
        jobs = job_service.list_jobs()
        
        return JobListResponse(jobs=jobs)
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list jobs: {str(e)}"
        )

@router.get(
    "/artifacts/{job_id}/{artifact_type}",
    response_model=ArtifactResponse,
    responses={
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    }
)
async def get_artifact(
    job_id: str = Path(..., description="Job ID"),
    artifact_type: ArtifactType = Path(..., description="Artifact type"),
    include_content: bool = Query(False, description="Include artifact content"),
    job_service: JobService = Depends(get_job_service)
):
    """
    Get artifact.
    
    Args:
        job_id: Job ID
        artifact_type: Artifact type
        include_content: Include artifact content
        job_service: Job service
    
    Returns:
        Artifact details and optionally content
    """
    try:
        # Get job
        job = job_service.get_job(job_id)
        if not job:
            raise HTTPException(
                status_code=404,
                detail=f"Job not found: {job_id}"
            )
        
        # Get artifact
        artifact = job_service.get_artifact(job_id, artifact_type)
        if not artifact:
            raise HTTPException(
                status_code=404,
                detail=f"Artifact not found for job: {job_id}"
            )
        
        # Get artifact content if requested
        content = None
        if include_content:
            content = job_service.get_artifact_content(artifact)
        
        return ArtifactResponse(
            artifact=artifact,
            content=content
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get artifact: {str(e)}"
        )

@router.get(
    "/status",
    response_model=PipelineStatusResponse,
    responses={
        500: {"model": ErrorResponse}
    }
)
async def get_pipeline_status(
    orchestrator_service: OrchestratorService = Depends(get_orchestrator_service)
):
    """
    Get pipeline status.
    
    Args:
        orchestrator_service: Orchestrator service
    
    Returns:
        Pipeline status
    """
    try:
        # Check pipeline status
        status = await orchestrator_service.check_pipeline_status()
        
        return PipelineStatusResponse(**status)
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get pipeline status: {str(e)}"
        )
