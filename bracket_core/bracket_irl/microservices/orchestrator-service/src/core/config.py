"""
Configuration for the Orchestrator Service.
"""

import os
from functools import lru_cache
from typing import Dict, Any, Optional

from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.config import Config

# Default configuration
DEFAULT_CONFIG = {
    "service_name": "orchestrator-service",
    "host": "0.0.0.0",
    "port": 8000,
    "log_level": "INFO",
    "api_prefix": "/api/v1",
    "storage_type": "local",
    "storage_path": "./data",
    "max_concurrent_tasks": 10,
    "job_timeout_seconds": 3600,
    "additional_config": {
        # Repository Mapper Service
        "repository_mapper_service_url": "http://repo-mapper-service:8001",
        # Domain Analyzer Service
        "domain_analyzer_service_url": "http://domain-analyzer-service:8002",
        # File-Domain Mapper Service
        "file_domain_mapper_service_url": "http://file-domain-mapper-service:8003",
        # Domain-File Repomap Service
        "domain_file_repomap_service_url": "http://domain-file-repomap-service:8004",
        # Diagram Generator Service
        "diagram_generator_service_url": "http://diagram-generator-service:8005",
        # Default configurations
        "default_repomap_config": {
            "batch_size": 1000,
            "top_percentage": 0.2,
            "min_functions": 3,
            "max_functions": 7,
            "exclude_tests": True,
            "output_format": "json",
            "include_extensions": [".py", ".js", ".ts", ".java", ".rb"]
        },
        "default_domain_analysis_config": {
            "model": "gpt-4o-mini",
            "use_openrouter": False,
            "max_tokens_per_chunk": 60000,
            "disable_parallel": False,
            "max_concurrent_tasks": 10,
            "generate_explanations": True
        },
        "default_file_domain_mapper_config": {
            "model": "gpt-4o-mini",
            "use_openrouter": False,
            "max_files_per_batch": 50
        },
        "default_domain_file_repomap_config": {
            "output_format": "json"
        },
        "default_diagram_generator_config": {
            "model_type": "openai",
            "openai_model": "gpt-4o-mini",
            "use_openrouter": False,
            "openrouter_model": "google/gemini-2.5-pro-preview",
            "max_concurrent_tasks": 30
        }
    }
}

@lru_cache()
def get_config() -> Config:
    """
    Get service configuration.

    Returns:
        Service configuration
    """
    config_dict = DEFAULT_CONFIG.copy()

    # Override with environment variables
    for key in config_dict:
        env_key = f"ORCHESTRATOR_{key.upper()}"
        if env_key in os.environ:
            config_dict[key] = os.environ[env_key]

    # Override service URLs with environment variables
    additional_config = config_dict.get("additional_config", {})
    for key in list(additional_config.keys()):
        if key.endswith("_service_url"):
            env_key = f"ORCHESTRATOR_{key.upper()}"
            if env_key in os.environ:
                additional_config[key] = os.environ[env_key]

    return Config(**config_dict)

def initialize_config() -> Config:
    """
    Initialize service configuration.

    Returns:
        Service configuration
    """
    return get_config()
