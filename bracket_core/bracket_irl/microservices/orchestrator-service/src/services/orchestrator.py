"""
Orchestrator service for coordinating the Bracket IRL pipeline.
"""

import os
import time
import json
import yaml
import asyncio
import logging
from typing import Dict, List, Any, Optional, Union

from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.models import (
    Job, JobStatus, Artifact, ArtifactType,
    RepositoryMapRequest, DomainAnalysisRequest,
    FileDomainMapperRequest, DomainFileRepomapRequest, DiagramGeneratorRequest,
    OrchestrationRequest
)
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.storage import StorageClient

from src.core.config import get_config
from src.services.job_service import JobService
from src.clients.service_client import (
    RepositoryMapperClient, DomainAnalyzerClient,
    FileDomainMapperClient, DomainFileRepomapClient, DiagramGeneratorClient
)

logger = logging.getLogger(__name__)

class OrchestratorService:
    """Service for orchestrating the Bracket IRL pipeline."""

    def __init__(
        self,
        storage_client: StorageClient,
        job_service: JobService,
        max_concurrent_tasks: int = 10
    ):
        """
        Initialize orchestrator service.

        Args:
            storage_client: Storage client
            job_service: Job service
            max_concurrent_tasks: Maximum concurrent tasks
        """
        self.storage_client = storage_client
        self.job_service = job_service
        self.max_concurrent_tasks = max_concurrent_tasks

        # Get configuration
        config = get_config()

        # Initialize service clients
        self.repository_mapper_client = RepositoryMapperClient(
            base_url=config.additional_config.get("repository_mapper_service_url", "http://repository-mapper-service:8001")
        )
        self.domain_analyzer_client = DomainAnalyzerClient(
            base_url=config.additional_config.get("domain_analyzer_service_url", "http://domain-analyzer-service:8002")
        )
        self.file_domain_mapper_client = FileDomainMapperClient(
            base_url=config.additional_config.get("file_domain_mapper_service_url", "http://file-domain-mapper-service:8003")
        )
        self.domain_file_repomap_client = DomainFileRepomapClient(
            base_url=config.additional_config.get("domain_file_repomap_service_url", "http://domain-file-repomap-service:8004")
        )
        self.diagram_generator_client = DiagramGeneratorClient(
            base_url=config.additional_config.get("diagram_generator_service_url", "http://diagram-generator-service:8005")
        )

        # Initialize semaphore for limiting concurrent tasks
        self.semaphore = asyncio.Semaphore(max_concurrent_tasks)

    async def process_repository(
        self,
        job_id: str,
        request: OrchestrationRequest
    ) -> None:
        """
        Process repository through the entire pipeline.

        Args:
            job_id: Job ID
            request: Orchestration request
        """
        try:
            # Update job status
            self.job_service.update_job(
                job_id=job_id,
                status=JobStatus.RUNNING,
                message="Starting repository processing"
            )

            # Step 1: Generate repository map
            repomap_job_id = await self._generate_repository_map(job_id, request)

            # Step 2: Analyze domains
            domain_analysis_job_id = await self._analyze_domains(job_id, request, repomap_job_id)

            # Step 3: Map files to domains
            domain_mapping_job_id = await self._map_files_to_domains(job_id, request, repomap_job_id, domain_analysis_job_id)

            # Step 4: Generate domain-file repomap
            domain_file_repomap_job_id = await self._generate_domain_file_repomap(job_id, request, domain_mapping_job_id, repomap_job_id)

            # Step 5: Generate diagrams
            diagram_job_id = await self._generate_diagrams(job_id, request, domain_file_repomap_job_id)

            # Update job status
            self.job_service.update_job(
                job_id=job_id,
                status=JobStatus.COMPLETED,
                progress=1.0,
                message="Repository processing completed",
                result={
                    "repomap_job_id": repomap_job_id,
                    "domain_analysis_job_id": domain_analysis_job_id,
                    "domain_mapping_job_id": domain_mapping_job_id,
                    "domain_file_repomap_job_id": domain_file_repomap_job_id,
                    "diagram_job_id": diagram_job_id
                }
            )
        except Exception as e:
            logger.error(f"Repository processing failed: {str(e)}")

            # Update job status
            self.job_service.update_job(
                job_id=job_id,
                status=JobStatus.FAILED,
                error=str(e),
                message="Repository processing failed"
            )

    async def _generate_repository_map(
        self,
        job_id: str,
        request: OrchestrationRequest
    ) -> Optional[str]:
        """
        Generate repository map.

        Args:
            job_id: Job ID
            request: Orchestration request

        Returns:
            Repository mapper job ID
        """
        # Update job status
        self.job_service.update_job(
            job_id=job_id,
            progress=0.1,
            message="Generating repository map"
        )

        # Get configuration
        config = get_config()
        default_config = config.additional_config.get("default_repomap_config", {})

        # Create repository map request
        repomap_request = request.repomap_config or RepositoryMapRequest(
            repo_dir=request.repo_dir,
            **default_config
        )

        # No need to replace dummy in repomap_request as it doesn't have a repomap_path attribute

        # Generate repository map
        response = await self.repository_mapper_client.generate_repository_map(repomap_request)
        repomap_job_id = response.get("job_id")

        # Wait for repository map generation to complete
        await self._wait_for_job_completion(
            service_client=self.repository_mapper_client,
            job_id=repomap_job_id,
            timeout=3600,
            description="repository map generation"
        )

        # Update job status
        self.job_service.update_job(
            job_id=job_id,
            progress=0.2,
            message="Repository map generated",
            result={"repomap_job_id": repomap_job_id}
        )

        return repomap_job_id

    async def _analyze_domains(
        self,
        job_id: str,
        request: OrchestrationRequest,
        repomap_job_id: Optional[str]
    ) -> Optional[str]:
        """
        Analyze domains.

        Args:
            job_id: Job ID
            request: Orchestration request
            repomap_job_id: Repository mapper job ID

        Returns:
            Domain analyzer job ID
        """
        # Update job status
        self.job_service.update_job(
            job_id=job_id,
            progress=0.3,
            message="Analyzing domains"
        )

        # Get configuration
        config = get_config()
        default_config = config.additional_config.get("default_domain_analysis_config", {})

        # Get repository map artifact
        try:
            repomap_response = await self.repository_mapper_client.get_artifact(
                job_id=repomap_job_id,
                artifact_type=ArtifactType.FILTERED_REPOMAP,
                include_content=True
            )
            repomap_artifact = repomap_response.get("artifact", {})
            repomap_content = repomap_response.get("content")

            # Check if content is None or empty
            if not repomap_content:
                logger.warning(f"Filtered repomap content is empty or None. Trying to get the full repomap.")
                # Try to get the full repomap instead
                repomap_response = await self.repository_mapper_client.get_artifact(
                    job_id=repomap_job_id,
                    artifact_type=ArtifactType.REPOMAP,
                    include_content=True
                )
                repomap_content = repomap_response.get("content")

                if not repomap_content:
                    # Get the path from the artifact and read the file directly
                    artifact_path = repomap_artifact.get("path")
                    if artifact_path:
                        if not artifact_path.startswith("/"):
                            artifact_path = f"/app/data/{artifact_path}"

                        import os
                        if os.path.exists(artifact_path):
                            with open(artifact_path, "r") as f:
                                repomap_content = f.read()
                        else:
                            logger.error(f"Repository map not found at path: {artifact_path}")
                            raise Exception(f"Repository map not found: {artifact_path}")
                    else:
                        logger.error("Repository map path not found in artifact")
                        raise Exception("Repository map path not found in artifact")
        except Exception as e:
            logger.error(f"Error getting repository map artifact: {e}")
            raise Exception(f"Error getting repository map artifact: {e}")

        # Create directory for job artifacts if it doesn't exist
        import os
        job_artifacts_dir = f"/app/data/artifacts/{job_id}"
        os.makedirs(job_artifacts_dir, exist_ok=True)

        # Save the repomap content to a file in the job directory
        repomap_path = f"{job_artifacts_dir}/repomap.json"
        if repomap_content:
            logger.info(f"Saving repomap content to {repomap_path}")
            with open(repomap_path, "w") as f:
                if isinstance(repomap_content, str):
                    f.write(repomap_content)
                else:
                    # If it's a dictionary or other object, convert to JSON string
                    f.write(json.dumps(repomap_content))
        else:
            logger.error("No repomap content to save")
            raise Exception("No repomap content to save")

        # Create domain analysis request
        domain_analysis_request = request.domain_analysis_config or DomainAnalysisRequest(
            repomap_path=f"artifacts/{job_id}/repomap.json",  # Use relative path instead of absolute
            **default_config
        )

        # Replace dummy job ID in paths with actual job ID
        if hasattr(domain_analysis_request, "repomap_path") and domain_analysis_request.repomap_path:
            if "placeholder" in domain_analysis_request.repomap_path:
                domain_analysis_request.repomap_path = domain_analysis_request.repomap_path.replace("placeholder", job_id)
            elif "dummy" in domain_analysis_request.repomap_path:
                domain_analysis_request.repomap_path = domain_analysis_request.repomap_path.replace("dummy", job_id)

        # Analyze domains
        response = await self.domain_analyzer_client.analyze_domains(domain_analysis_request)
        domain_analysis_job_id = response.get("job_id")

        # Wait for domain analysis to complete
        await self._wait_for_job_completion(
            service_client=self.domain_analyzer_client,
            job_id=domain_analysis_job_id,
            timeout=3600,
            description="domain analysis"
        )

        # Update job status
        self.job_service.update_job(
            job_id=job_id,
            progress=0.4,
            message="Domains analyzed",
            result={"domain_analysis_job_id": domain_analysis_job_id}
        )

        return domain_analysis_job_id

    async def _map_files_to_domains(
        self,
        job_id: str,
        request: OrchestrationRequest,
        repomap_job_id: Optional[str],
        domain_analysis_job_id: Optional[str]
    ) -> Optional[str]:
        """
        Map files to domains.

        Args:
            job_id: Job ID
            request: Orchestration request
            repomap_job_id: Repository mapper job ID
            domain_analysis_job_id: Domain analyzer job ID

        Returns:
            File domain mapper job ID
        """
        # Update job status
        self.job_service.update_job(
            job_id=job_id,
            progress=0.5,
            message="Mapping files to domains"
        )

        # Get configuration
        config = get_config()
        default_config = config.additional_config.get("default_file_domain_mapper_config", {})

        # Get repository map artifact
        try:
            repomap_response = await self.repository_mapper_client.get_artifact(
                job_id=repomap_job_id,
                artifact_type=ArtifactType.REPOMAP,
                include_content=True
            )
            repomap_artifact = repomap_response.get("artifact", {})
            repomap_content = repomap_response.get("content")

            # Check if content is None or empty
            if not repomap_content:
                logger.warning(f"Repomap content is empty or None. Trying to get the filtered repomap.")
                # Try to get the filtered repomap instead
                repomap_response = await self.repository_mapper_client.get_artifact(
                    job_id=repomap_job_id,
                    artifact_type=ArtifactType.FILTERED_REPOMAP,
                    include_content=True
                )
                repomap_content = repomap_response.get("content")

                if not repomap_content:
                    # Get the path from the artifact and read the file directly
                    artifact_path = repomap_artifact.get("path")
                    if artifact_path:
                        if not artifact_path.startswith("/"):
                            artifact_path = f"/app/data/{artifact_path}"

                        import os
                        if os.path.exists(artifact_path):
                            with open(artifact_path, "r") as f:
                                repomap_content = f.read()
                        else:
                            logger.error(f"Repository map not found at path: {artifact_path}")
                            raise Exception(f"Repository map not found: {artifact_path}")
                    else:
                        logger.error("Repository map path not found in artifact")
                        raise Exception("Repository map path not found in artifact")
        except Exception as e:
            logger.error(f"Error getting repository map artifact: {e}")
            raise Exception(f"Error getting repository map artifact: {e}")

        # Create directory for job artifacts if it doesn't exist
        import os
        job_artifacts_dir = f"/app/data/artifacts/{job_id}"
        os.makedirs(job_artifacts_dir, exist_ok=True)

        # Save the repomap content to a file in the job directory
        repomap_path = f"{job_artifacts_dir}/repomap.json"
        if repomap_content:
            logger.info(f"Saving repomap content to {repomap_path}")
            with open(repomap_path, "w") as f:
                f.write(json.dumps(repomap_content) if isinstance(repomap_content, dict) else repomap_content)
        else:
            logger.error("No repomap content to save")
            raise Exception("No repomap content to save")

        # Get domain analysis artifact
        domain_analysis_artifact_path = None
        domain_analysis_content = None

        try:
            # First try to get the artifact using the domain analysis job ID
            domain_analysis_response = await self.domain_analyzer_client.get_artifact(
                job_id=domain_analysis_job_id,
                artifact_type=ArtifactType.DOMAIN_ANALYSIS,
                include_content=True
            )
            domain_analysis_artifact = domain_analysis_response.get("artifact", {})
            domain_analysis_content = domain_analysis_response.get("content")
            domain_analysis_artifact_path = domain_analysis_artifact.get("path")

            logger.info(f"Got domain analysis artifact: {domain_analysis_artifact}")
            logger.info(f"Domain analysis artifact path: {domain_analysis_artifact_path}")
        except Exception as e:
            logger.error(f"Failed to get domain analysis artifact using domain analysis job ID: {e}")
            try:
                # If that fails, try to get the artifact using the orchestrator job ID
                domain_analysis_response = await self.domain_analyzer_client.get_artifact(
                    job_id=job_id,
                    artifact_type=ArtifactType.DOMAIN_ANALYSIS,
                    include_content=True
                )
                domain_analysis_artifact = domain_analysis_response.get("artifact", {})
                domain_analysis_content = domain_analysis_response.get("content")
                domain_analysis_artifact_path = domain_analysis_artifact.get("path")

                logger.info(f"Got domain analysis artifact using orchestrator job ID: {domain_analysis_artifact}")
                logger.info(f"Domain analysis artifact path: {domain_analysis_artifact_path}")
            except Exception as e2:
                logger.error(f"Failed to get domain analysis artifact using orchestrator job ID: {e2}")

        # Check if we need to look for the YAML file directly
        if not domain_analysis_content and domain_analysis_artifact_path:
            # Try to find the YAML file
            yaml_path = None

            # Check if the path is a JSON file and convert to YAML path
            if domain_analysis_artifact_path.endswith('.json'):
                yaml_path = domain_analysis_artifact_path.replace('.json', '.yaml')
            else:
                # Try to find the YAML file in the artifacts directory
                yaml_path = f"/app/data/artifacts/{domain_analysis_job_id}/domain_analysis/domain_analysis.yaml"

            logger.info(f"Looking for domain analysis YAML file at: {yaml_path}")

            if os.path.exists(yaml_path):
                logger.info(f"Found domain analysis YAML file at: {yaml_path}")
                try:
                    with open(yaml_path, 'r') as f:
                        yaml_data = yaml.safe_load(f)
                        # Convert YAML to JSON
                        domain_analysis_content = json.dumps(yaml_data)
                        logger.info(f"Successfully loaded domain analysis YAML file")
                except Exception as e:
                    logger.error(f"Error loading domain analysis YAML file: {e}")
            else:
                logger.warning(f"Domain analysis YAML file not found at: {yaml_path}")

                # Try to find any domain analysis YAML file in the artifacts directory
                for root, dirs, files in os.walk("/app/data/artifacts"):
                    for file in files:
                        if file == "domain_analysis.yaml":
                            yaml_path = os.path.join(root, file)
                            logger.info(f"Found domain analysis YAML file at: {yaml_path}")
                            try:
                                with open(yaml_path, 'r') as f:
                                    yaml_data = yaml.safe_load(f)
                                    # Convert YAML to JSON
                                    domain_analysis_content = json.dumps(yaml_data)
                                    logger.info(f"Successfully loaded domain analysis YAML file")
                                    break
                            except Exception as e:
                                logger.error(f"Error loading domain analysis YAML file: {e}")

        # Save the domain analysis content to a file in the job directory
        domain_analysis_path = f"{job_artifacts_dir}/domain_analysis.json"
        if domain_analysis_content is not None:
            logger.info(f"Saving domain analysis content to {domain_analysis_path}")
            with open(domain_analysis_path, "w") as f:
                # Check if domain_analysis_content is already a string
                if isinstance(domain_analysis_content, str):
                    f.write(domain_analysis_content)
                else:
                    # If it's a dictionary or other object, convert to JSON string
                    f.write(json.dumps(domain_analysis_content))
        else:
            # Create an empty file if content is None
            logger.warning(f"No domain analysis content found, creating empty JSON file at {domain_analysis_path}")
            with open(domain_analysis_path, "w") as f:
                f.write("{}")

        # Create file domain mapper request
        file_domain_mapper_request = request.file_domain_mapper_config or FileDomainMapperRequest(
            repomap_path=f"artifacts/{job_id}/repomap.json",  # Use relative path instead of absolute
            domain_analysis_path=f"artifacts/{job_id}/domain_analysis.json",  # Use relative path instead of absolute
            **default_config
        )

        # Replace dummy job ID in paths with actual job ID
        if hasattr(file_domain_mapper_request, "repomap_path") and file_domain_mapper_request.repomap_path:
            if "placeholder" in file_domain_mapper_request.repomap_path:
                file_domain_mapper_request.repomap_path = file_domain_mapper_request.repomap_path.replace("placeholder", job_id)
            elif "dummy" in file_domain_mapper_request.repomap_path:
                file_domain_mapper_request.repomap_path = file_domain_mapper_request.repomap_path.replace("dummy", job_id)

        if hasattr(file_domain_mapper_request, "domain_analysis_path") and file_domain_mapper_request.domain_analysis_path:
            if "placeholder" in file_domain_mapper_request.domain_analysis_path:
                file_domain_mapper_request.domain_analysis_path = file_domain_mapper_request.domain_analysis_path.replace("placeholder", job_id)
            elif "dummy" in file_domain_mapper_request.domain_analysis_path:
                file_domain_mapper_request.domain_analysis_path = file_domain_mapper_request.domain_analysis_path.replace("dummy", job_id)

        # Map files to domains - pass the orchestrator job_id to use the same ID
        response = await self.file_domain_mapper_client.map_files_to_domains(
            request=file_domain_mapper_request,
            job_id=job_id  # Pass the orchestrator job_id to use the same ID
        )
        domain_mapping_job_id = response.get("job_id")

        # Wait for file domain mapping to complete
        await self._wait_for_job_completion(
            service_client=self.file_domain_mapper_client,
            job_id=domain_mapping_job_id,
            timeout=3600,
            description="file domain mapping"
        )

        # Update job status
        self.job_service.update_job(
            job_id=job_id,
            progress=0.6,
            message="Files mapped to domains",
            result={"domain_mapping_job_id": domain_mapping_job_id}
        )

        return domain_mapping_job_id

    async def _generate_domain_file_repomap(
        self,
        job_id: str,
        request: OrchestrationRequest,
        domain_mapping_job_id: Optional[str],
        repomap_job_id: Optional[str]
    ) -> Optional[str]:
        """
        Generate domain-file repomap.

        Args:
            job_id: Job ID
            request: Orchestration request
            domain_mapping_job_id: File domain mapper job ID
            repomap_job_id: Repository mapper job ID

        Returns:
            Domain file repomap job ID
        """
        # Update job status
        self.job_service.update_job(
            job_id=job_id,
            progress=0.7,
            message="Generating domain-file repomap"
        )

        # Get configuration
        config = get_config()
        default_config = config.additional_config.get("default_domain_file_repomap_config", {})

        # Get domain mapping artifact
        domain_mapping_response = await self.file_domain_mapper_client.get_artifact(
            job_id=domain_mapping_job_id,
            artifact_type=ArtifactType.DOMAIN_MAPPING,
            include_content=True
        )
        domain_mapping_artifact = domain_mapping_response.get("artifact", {})
        domain_mapping_content = domain_mapping_artifact.get("content")
        domain_mapping_artifact_path = domain_mapping_artifact.get("path")

        # Create directory for job artifacts if it doesn't exist
        import os
        job_artifacts_dir = f"/app/data/artifacts/{job_id}"
        os.makedirs(job_artifacts_dir, exist_ok=True)

        # Check if we need to look for the YAML file directly
        if not domain_mapping_content and domain_mapping_artifact_path:
            # Try to find the YAML file
            yaml_path = None

            # Check if the path is a JSON file and convert to YAML path
            if domain_mapping_artifact_path.endswith('.json'):
                yaml_path = domain_mapping_artifact_path.replace('.json', '.yaml')
            else:
                # Try to find the YAML file in the artifacts directory
                yaml_path = f"/app/data/artifacts/{domain_mapping_job_id}/file_domain_mapping/file_domain_mapping.yaml"

            logger.info(f"Looking for domain mapping YAML file at: {yaml_path}")

            if os.path.exists(yaml_path):
                logger.info(f"Found domain mapping YAML file at: {yaml_path}")
                try:
                    with open(yaml_path, 'r') as f:
                        yaml_data = yaml.safe_load(f)
                        # Convert YAML to JSON
                        domain_mapping_content = yaml_data
                        logger.info(f"Successfully loaded domain mapping YAML file")
                except Exception as e:
                    logger.error(f"Error loading domain mapping YAML file: {e}")
            else:
                logger.warning(f"Domain mapping YAML file not found at: {yaml_path}")

                # Try to find any domain mapping YAML file in the artifacts directory
                for root, dirs, files in os.walk("/app/data/artifacts"):
                    for file in files:
                        if file == "file_domain_mapping.yaml":
                            yaml_path = os.path.join(root, file)
                            logger.info(f"Found domain mapping YAML file at: {yaml_path}")
                            try:
                                with open(yaml_path, 'r') as f:
                                    yaml_data = yaml.safe_load(f)
                                    # Convert YAML to JSON
                                    domain_mapping_content = yaml_data
                                    logger.info(f"Successfully loaded domain mapping YAML file")
                                    break
                            except Exception as e:
                                logger.error(f"Error loading domain mapping YAML file: {e}")

        # Save the domain mapping content to a file in the job directory
        domain_mapping_path = f"{job_artifacts_dir}/domain_mapping.json"
        if domain_mapping_content is not None:
            logger.info(f"Saving domain mapping content to {domain_mapping_path}")
            with open(domain_mapping_path, "w") as f:
                if isinstance(domain_mapping_content, str):
                    f.write(domain_mapping_content)
                else:
                    # If it's a dictionary or other object, convert to JSON string
                    f.write(json.dumps(domain_mapping_content))
        else:
            # Create an empty file if content is None
            logger.warning(f"No domain mapping content found, creating empty JSON file at {domain_mapping_path}")
            with open(domain_mapping_path, "w") as f:
                f.write("{}")

        # Get repository map artifact
        try:
            repomap_response = await self.repository_mapper_client.get_artifact(
                job_id=repomap_job_id,
                artifact_type=ArtifactType.REPOMAP,
                include_content=True
            )
            repomap_artifact = repomap_response.get("artifact", {})
            repomap_content = repomap_response.get("content")

            # Check if content is None or empty
            if not repomap_content:
                logger.warning(f"Repomap content is empty or None. Trying to get the filtered repomap.")
                # Try to get the filtered repomap instead
                repomap_response = await self.repository_mapper_client.get_artifact(
                    job_id=repomap_job_id,
                    artifact_type=ArtifactType.FILTERED_REPOMAP,
                    include_content=True
                )
                repomap_content = repomap_response.get("content")

                if not repomap_content:
                    # Get the path from the artifact and read the file directly
                    artifact_path = repomap_artifact.get("path")
                    if artifact_path:
                        if not artifact_path.startswith("/"):
                            artifact_path = f"/app/data/{artifact_path}"

                        import os
                        if os.path.exists(artifact_path):
                            with open(artifact_path, "r") as f:
                                repomap_content = f.read()
                        else:
                            logger.error(f"Repository map not found at path: {artifact_path}")
                            raise Exception(f"Repository map not found: {artifact_path}")
                    else:
                        logger.error("Repository map path not found in artifact")
                        raise Exception("Repository map path not found in artifact")
        except Exception as e:
            logger.error(f"Error getting repository map artifact: {e}")
            raise Exception(f"Error getting repository map artifact: {e}")

        # Save the repomap content to a file in the job directory
        repomap_path = f"{job_artifacts_dir}/repomap.json"
        if repomap_content:
            logger.info(f"Saving repomap content to {repomap_path}")
            with open(repomap_path, "w") as f:
                if isinstance(repomap_content, str):
                    f.write(repomap_content)
                else:
                    # If it's a dictionary or other object, convert to JSON string
                    f.write(json.dumps(repomap_content))
        else:
            logger.error("No repomap content to save")
            raise Exception("No repomap content to save")

        # Create domain file repomap request - use YAML file path for domain mapping
        domain_file_repomap_request = request.domain_file_repomap_config or DomainFileRepomapRequest(
            domain_mapping_path=f"artifacts/{job_id}/file_domain_mapping/file_domain_mapping.yaml",  # Use YAML path
            repomap_path=f"artifacts/{job_id}/repomap.json",  # Use relative path instead of absolute
            **default_config
        )

        # Replace dummy job ID in paths with actual job ID
        if hasattr(domain_file_repomap_request, "repomap_path") and domain_file_repomap_request.repomap_path:
            if "placeholder" in domain_file_repomap_request.repomap_path:
                domain_file_repomap_request.repomap_path = domain_file_repomap_request.repomap_path.replace("placeholder", job_id)
            elif "dummy" in domain_file_repomap_request.repomap_path:
                domain_file_repomap_request.repomap_path = domain_file_repomap_request.repomap_path.replace("dummy", job_id)

        if hasattr(domain_file_repomap_request, "domain_mapping_path") and domain_file_repomap_request.domain_mapping_path:
            if "placeholder" in domain_file_repomap_request.domain_mapping_path:
                domain_file_repomap_request.domain_mapping_path = domain_file_repomap_request.domain_mapping_path.replace("placeholder", job_id)
            elif "dummy" in domain_file_repomap_request.domain_mapping_path:
                domain_file_repomap_request.domain_mapping_path = domain_file_repomap_request.domain_mapping_path.replace("dummy", job_id)

        # Generate domain-file repomap - pass the orchestrator job_id to use the same ID
        response = await self.domain_file_repomap_client.generate_domain_file_repomap(
            request=domain_file_repomap_request,
            job_id=job_id  # Pass the orchestrator job_id to use the same ID
        )
        domain_file_repomap_job_id = response.get("job_id")

        # Wait for domain-file repomap generation to complete
        await self._wait_for_job_completion(
            service_client=self.domain_file_repomap_client,
            job_id=domain_file_repomap_job_id,
            timeout=3600,
            description="domain-file repomap generation"
        )

        # Update job status
        self.job_service.update_job(
            job_id=job_id,
            progress=0.8,
            message="Domain-file repomap generated",
            result={"domain_file_repomap_job_id": domain_file_repomap_job_id}
        )

        return domain_file_repomap_job_id

    async def _generate_diagrams(
        self,
        job_id: str,
        request: OrchestrationRequest,
        domain_file_repomap_job_id: Optional[str]
    ) -> Optional[str]:
        """
        Generate diagrams.

        Args:
            job_id: Job ID
            request: Orchestration request
            domain_file_repomap_job_id: Domain file repomap job ID

        Returns:
            Diagram generator job ID
        """
        # Update job status
        self.job_service.update_job(
            job_id=job_id,
            progress=0.9,
            message="Generating diagrams"
        )

        # Get configuration
        config = get_config()
        default_config = config.additional_config.get("default_diagram_generator_config", {})

        # Get domain-file repomap artifact
        # First try using the domain_file_repomap_job_id
        try:
            domain_file_repomap_response = await self.domain_file_repomap_client.get_artifact(
                job_id=domain_file_repomap_job_id,
                artifact_type=ArtifactType.DOMAIN_FILE_REPOMAP,
                include_content=True
            )
            logger.info(f"Successfully retrieved domain-file repomap artifact using domain_file_repomap_job_id: {domain_file_repomap_job_id}")
        except Exception as e:
            # If that fails, try using the orchestrator job_id
            logger.warning(f"Failed to get domain-file repomap artifact using domain_file_repomap_job_id: {e}")
            logger.info(f"Trying to get domain-file repomap artifact using orchestrator job_id: {job_id}")
            domain_file_repomap_response = await self.domain_file_repomap_client.get_artifact(
                job_id=job_id,
                artifact_type=ArtifactType.DOMAIN_FILE_REPOMAP,
                include_content=True
            )
        domain_file_repomap_artifact = domain_file_repomap_response.get("artifact", {})
        domain_file_repomap_content = domain_file_repomap_artifact.get("content")

        # Create directory for job artifacts if it doesn't exist
        import os
        job_artifacts_dir = f"/app/data/artifacts/{job_id}"
        os.makedirs(job_artifacts_dir, exist_ok=True)

        # Save the domain file repomap content to a file in the job directory
        domain_file_repomap_path = f"{job_artifacts_dir}/domain_file_repomap.json"
        if domain_file_repomap_content is not None:
            logger.info(f"Saving domain file repomap content to {domain_file_repomap_path}")
            with open(domain_file_repomap_path, "w") as f:
                if isinstance(domain_file_repomap_content, str):
                    f.write(domain_file_repomap_content)
                else:
                    # If it's a dictionary or other object, convert to JSON string
                    f.write(json.dumps(domain_file_repomap_content))
        else:
            # If content is None, try to find the file directly in the artifacts directory
            logger.warning(f"No domain file repomap content found, looking for file directly")

            # Try to find the file in the domain_file_repomap_job_id directory
            direct_path = f"/app/data/artifacts/{domain_file_repomap_job_id}/domain_file_repomap.json"
            if os.path.exists(direct_path):
                # Check if source and destination are the same file
                if os.path.abspath(direct_path) != os.path.abspath(domain_file_repomap_path):
                    logger.info(f"Found domain file repomap at {direct_path}, copying to {domain_file_repomap_path}")
                    import shutil
                    shutil.copy(direct_path, domain_file_repomap_path)
                else:
                    logger.info(f"Found domain file repomap at {direct_path}, which is the same as destination. No need to copy.")
            else:
                # Try to find the file in the orchestrator job_id directory
                direct_path = f"/app/data/artifacts/{job_id}/domain_file_repomap.json"
                if os.path.exists(direct_path):
                    logger.info(f"Found domain file repomap at {direct_path}, using directly")
                    # No need to copy, it's already in the right place
                    # Since domain_file_repomap_path is the same as direct_path, we don't need to do anything
                else:
                    # Try to find any domain_file_repomap.json file in the artifacts directory
                    found = False
                    for root, dirs, files in os.walk("/app/data/artifacts"):
                        for file in files:
                            if file == "domain_file_repomap.json":
                                source_path = os.path.join(root, file)
                                # Check if source and destination are the same file
                                if os.path.abspath(source_path) != os.path.abspath(domain_file_repomap_path):
                                    logger.info(f"Found domain file repomap at {source_path}, copying to {domain_file_repomap_path}")
                                    import shutil
                                    shutil.copy(source_path, domain_file_repomap_path)
                                else:
                                    logger.info(f"Found domain file repomap at {source_path}, which is the same as destination. No need to copy.")
                                found = True
                                break
                        if found:
                            break

                    if not found:
                        # Create an empty file if content is None and no file was found
                        logger.warning(f"No domain file repomap file found, creating empty JSON file at {domain_file_repomap_path}")
                        with open(domain_file_repomap_path, "w") as f:
                            f.write("{}")

        # Verify that the domain file repomap file exists and is not empty
        if os.path.exists(domain_file_repomap_path) and os.path.getsize(domain_file_repomap_path) > 0:
            logger.info(f"Domain file repomap file exists and is not empty: {domain_file_repomap_path}")
        else:
            error_msg = f"Domain-file repomap file not found or is empty: {domain_file_repomap_path}"
            logger.error(error_msg)
            raise Exception(error_msg)

        # Create diagram generator request
        diagram_generator_request = request.diagram_generator_config or DiagramGeneratorRequest(
            domain_file_repomap_path=f"artifacts/{job_id}/domain_file_repomap.json",  # Use relative path instead of absolute
            **default_config
        )

        # Replace dummy job ID in paths with actual job ID
        if hasattr(diagram_generator_request, "domain_file_repomap_path") and diagram_generator_request.domain_file_repomap_path:
            if "placeholder" in diagram_generator_request.domain_file_repomap_path:
                diagram_generator_request.domain_file_repomap_path = diagram_generator_request.domain_file_repomap_path.replace("placeholder", job_id)
            elif "dummy" in diagram_generator_request.domain_file_repomap_path:
                diagram_generator_request.domain_file_repomap_path = diagram_generator_request.domain_file_repomap_path.replace("dummy", job_id)

        # Generate diagrams
        response = await self.diagram_generator_client.generate_diagrams(diagram_generator_request)
        diagram_job_id = response.get("job_id")

        # Wait for diagram generation to complete
        await self._wait_for_job_completion(
            service_client=self.diagram_generator_client,
            job_id=diagram_job_id,
            timeout=3600,
            description="diagram generation"
        )

        # Update job status
        self.job_service.update_job(
            job_id=job_id,
            progress=1.0,
            message="Diagrams generated",
            result={"diagram_job_id": diagram_job_id}
        )

        return diagram_job_id

    async def _wait_for_job_completion(
        self,
        service_client: Any,
        job_id: Optional[str],
        timeout: int = 3600,
        poll_interval: int = 5,
        description: str = "job"
    ) -> Dict[str, Any]:
        """
        Wait for job completion.

        Args:
            service_client: Service client
            job_id: Job ID
            timeout: Timeout in seconds
            poll_interval: Poll interval in seconds
            description: Job description for logging

        Returns:
            Job details

        Raises:
            Exception: If job fails or times out
        """
        # Check if job_id is None
        if job_id is None:
            raise Exception(f"Invalid job ID for {description}")

        start_time = time.time()

        while True:
            # Check if timeout exceeded
            if time.time() - start_time > timeout:
                raise Exception(f"Timeout waiting for {description} to complete")

            # Get job status
            job_response = await service_client.get_job_status(job_id)
            job = job_response.get("job", {})

            # Check job status
            if job.get("status") == JobStatus.COMPLETED.value:
                logger.info(f"{description.capitalize()} completed: {job_id}")
                return job
            elif job.get("status") == JobStatus.FAILED.value:
                error = job.get("error", f"{description.capitalize()} failed")
                logger.error(f"{description.capitalize()} failed: {job_id} - {error}")
                raise Exception(error)

            # Wait before polling again
            await asyncio.sleep(poll_interval)

    async def check_pipeline_status(self) -> Dict[str, Any]:
        """
        Check status of all services in the pipeline.

        Returns:
            Pipeline status
        """
        services = [
            {
                "name": "repository-mapper-service",
                "client": self.repository_mapper_client,
                "url": get_config().additional_config.get("repository_mapper_service_url")
            },
            {
                "name": "domain-analyzer-service",
                "client": self.domain_analyzer_client,
                "url": get_config().additional_config.get("domain_analyzer_service_url")
            },
            {
                "name": "file-domain-mapper-service",
                "client": self.file_domain_mapper_client,
                "url": get_config().additional_config.get("file_domain_mapper_service_url")
            },
            {
                "name": "domain-file-repomap-service",
                "client": self.domain_file_repomap_client,
                "url": get_config().additional_config.get("domain_file_repomap_service_url")
            },
            {
                "name": "diagram-generator-service",
                "client": self.diagram_generator_client,
                "url": get_config().additional_config.get("diagram_generator_service_url")
            }
        ]

        # Check status of each service
        service_statuses = []
        overall_status = "ok"

        for service in services:
            try:
                health_response = await service["client"].check_health()
                status = health_response.get("status", "down")

                if status != "ok":
                    overall_status = "degraded"

                service_statuses.append({
                    "service_name": service["name"],
                    "status": status,
                    "url": service["url"],
                    "details": health_response
                })
            except Exception as e:
                logger.error(f"Failed to check health of {service['name']}: {str(e)}")

                overall_status = "degraded"

                service_statuses.append({
                    "service_name": service["name"],
                    "status": "down",
                    "url": service["url"],
                    "details": {"error": str(e)}
                })

        return {
            "services": service_statuses,
            "overall_status": overall_status
        }
