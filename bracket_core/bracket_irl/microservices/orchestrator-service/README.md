# Orchestrator Service

This microservice is responsible for coordinating all other services in the Bracket IRL pipeline. It orchestrates the end-to-end process of analyzing a repository, from generating repository maps to creating diagrams.

## Features

- Coordinate all services in the Bracket IRL pipeline
- Manage end-to-end processing of repositories
- Track job status and artifacts
- REST API for integration with other systems
- Background job processing with status tracking

## API Endpoints

- `POST /api/v1/repositories`: Process repository
- `GET /api/v1/jobs/{job_id}`: Get job status
- `GET /api/v1/artifacts/{job_id}`: Get job artifacts
- `GET /api/v1/health`: Health check

## Configuration

The service can be configured using environment variables or a configuration file. See the `config.py` file for available configuration options.

## Installation

```bash
# Install dependencies
pip install -r requirements.txt

# Run the service
python -m src.main
```

## Docker

```bash
# Build the Docker image
docker build -t orchestrator-service .

# Run the Docker container
docker run -p 8000:8000 orchestrator-service
```

## Usage

```bash
# Process repository
curl -X POST http://localhost:8000/api/v1/repositories \
  -H "Content-Type: application/json" \
  -d '{
    "repo_dir": "/path/to/repository",
    "repomap_config": {
      "batch_size": 1000,
      "top_percentage": 0.3,
      "exclude_tests": true
    },
    "domain_analysis_config": {
      "model": "gpt-4o-mini",
      "max_tokens_per_chunk": 500000
    },
    "file_domain_mapper_config": {
      "model": "gpt-4o-mini",
      "max_files_per_batch": 50
    },
    "domain_file_repomap_config": {
      "output_format": "json"
    },
    "diagram_generator_config": {
      "model_type": "openai",
      "openai_model": "gpt-4o-mini"
    }
  }'

# Get job status
curl -X GET http://localhost:8000/api/v1/jobs/{job_id}

# Get job artifacts
curl -X GET http://localhost:8000/api/v1/artifacts/{job_id}
```
