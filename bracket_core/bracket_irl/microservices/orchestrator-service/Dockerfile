FROM bracket-irl/bracket_irl_common:latest

WORKDIR /app

# Copy requirements and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy the service code
COPY src/ ./src/

# Create directory structure for imports
RUN mkdir -p /app/bracket_irl/microservices

# Create symbolic links to make imports work
RUN ln -s /app/bracket_irl_common /app/bracket_irl/microservices/bracket_irl_common

# Set PYTHONPATH to include both the app directory and the parent directory
ENV PYTHONPATH=/app:/

# Set environment variables
ENV SERVICE_NAME=orchestrator-service
ENV HOST=0.0.0.0
ENV PORT=8000
ENV STORAGE_PATH=/app/data

# Expose the port
EXPOSE 8000

# Create data directory
RUN mkdir -p /app/data/jobs /app/data/artifacts

# Run the service
CMD ["python", "-m", "src.main"]
