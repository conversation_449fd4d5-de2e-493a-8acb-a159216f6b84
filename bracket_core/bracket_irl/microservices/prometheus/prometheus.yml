global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'repo-mapper-service'
    static_configs:
      - targets: ['repo-mapper-service:8001']
    metrics_path: /metrics

  - job_name: 'domain-analyzer-service'
    static_configs:
      - targets: ['domain-analyzer-service:8002']
    metrics_path: /metrics

  - job_name: 'file-domain-mapper-service'
    static_configs:
      - targets: ['file-domain-mapper-service:8003']
    metrics_path: /metrics

  - job_name: 'domain-file-repomap-service'
    static_configs:
      - targets: ['domain-file-repomap-service:8004']
    metrics_path: /metrics

  - job_name: 'diagram-generator-service'
    static_configs:
      - targets: ['diagram-generator-service:8005']
    metrics_path: /metrics

  - job_name: 'orchestrator-service'
    static_configs:
      - targets: ['orchestrator-service:8000']
    metrics_path: /metrics
