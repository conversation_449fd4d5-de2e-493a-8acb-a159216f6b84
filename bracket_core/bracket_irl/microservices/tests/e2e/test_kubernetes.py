#!/usr/bin/env python3
"""
Test script for Kubernetes deployment of Bracket IRL microservices.
This script tests the Kubernetes deployment using Minikube by checking service health,
pod status, and resource usage.
"""

import os
import sys
import json
import time
import argparse
import requests
import subprocess
import logging
import yaml
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("kubernetes_test.log")
    ]
)
logger = logging.getLogger("kubernetes_test")

class KubernetesTest:
    """Test the Kubernetes deployment for Bracket IRL microservices."""
    
    def __init__(
        self,
        k8s_dir: str,
        repo_dir: str,
        namespace: str = "bracket-irl",
        api_key: Optional[str] = None,
        timeout: int = 600,
        poll_interval: int = 10,
        results_dir: str = "results",
        minikube: bool = True
    ):
        """
        Initialize the test.
        
        Args:
            k8s_dir: Path to the Kubernetes manifests directory
            repo_dir: Path to the repository to mount
            namespace: Kubernetes namespace
            api_key: API key for the services
            timeout: Timeout in seconds for waiting for services to start
            poll_interval: Poll interval in seconds for checking service status
            results_dir: Directory to save results
            minikube: Whether to use Minikube
        """
        self.k8s_dir = os.path.abspath(k8s_dir)
        self.repo_dir = os.path.abspath(repo_dir)
        self.namespace = namespace
        self.api_key = api_key
        self.timeout = timeout
        self.poll_interval = poll_interval
        self.results_dir = results_dir
        self.minikube = minikube
        
        # Create results directory
        os.makedirs(results_dir, exist_ok=True)
        
        # Headers for API requests
        self.headers = {}
        if api_key:
            self.headers["Authorization"] = f"Bearer {api_key}"
        
        # Service URLs (will be set after port forwarding)
        self.service_urls = {}
    
    def setup_minikube(self) -> bool:
        """
        Set up Minikube.
        
        Returns:
            True if Minikube was set up successfully, False otherwise
        """
        if not self.minikube:
            logger.info("Skipping Minikube setup")
            return True
        
        logger.info("Setting up Minikube...")
        
        try:
            # Check if Minikube is running
            result = subprocess.run(
                ["minikube", "status"],
                check=False,
                capture_output=True,
                text=True
            )
            
            if "Running" not in result.stdout:
                # Start Minikube
                logger.info("Starting Minikube...")
                subprocess.run(
                    ["minikube", "start", "--memory=4096", "--cpus=4"],
                    check=True
                )
            
            # Enable ingress addon
            logger.info("Enabling ingress addon...")
            subprocess.run(
                ["minikube", "addons", "enable", "ingress"],
                check=True
            )
            
            logger.info("Minikube set up successfully")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Error setting up Minikube: {str(e)}")
            return False
    
    def create_namespace(self) -> bool:
        """
        Create Kubernetes namespace.
        
        Returns:
            True if namespace was created successfully, False otherwise
        """
        logger.info(f"Creating namespace {self.namespace}...")
        
        try:
            # Check if namespace exists
            result = subprocess.run(
                ["kubectl", "get", "namespace", self.namespace],
                check=False,
                capture_output=True,
                text=True
            )
            
            if f"{self.namespace}" in result.stdout:
                logger.info(f"Namespace {self.namespace} already exists")
                return True
            
            # Create namespace
            subprocess.run(
                ["kubectl", "create", "namespace", self.namespace],
                check=True
            )
            
            logger.info(f"Namespace {self.namespace} created successfully")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Error creating namespace: {str(e)}")
            return False
    
    def create_configmap(self) -> bool:
        """
        Create ConfigMap for API keys.
        
        Returns:
            True if ConfigMap was created successfully, False otherwise
        """
        logger.info("Creating ConfigMap...")
        
        try:
            # Create ConfigMap data
            config_data = {
                "apiVersion": "v1",
                "kind": "ConfigMap",
                "metadata": {
                    "name": "bracket-irl-config",
                    "namespace": self.namespace
                },
                "data": {
                    "REPO_DIR": "/repo"
                }
            }
            
            # Add API key if provided
            if self.api_key:
                config_data["data"]["OPENAI_API_KEY"] = self.api_key
            
            # Write ConfigMap to file
            config_file = os.path.join(self.results_dir, "configmap.yaml")
            with open(config_file, "w") as f:
                yaml.dump(config_data, f)
            
            # Apply ConfigMap
            subprocess.run(
                ["kubectl", "apply", "-f", config_file],
                check=True
            )
            
            logger.info("ConfigMap created successfully")
            return True
        except Exception as e:
            logger.error(f"Error creating ConfigMap: {str(e)}")
            return False
    
    def create_persistent_volume(self) -> bool:
        """
        Create PersistentVolume and PersistentVolumeClaim.
        
        Returns:
            True if PV and PVC were created successfully, False otherwise
        """
        logger.info("Creating PersistentVolume and PersistentVolumeClaim...")
        
        try:
            # Create PV data
            pv_data = {
                "apiVersion": "v1",
                "kind": "PersistentVolume",
                "metadata": {
                    "name": "bracket-data-pv"
                },
                "spec": {
                    "capacity": {
                        "storage": "10Gi"
                    },
                    "accessModes": ["ReadWriteOnce"],
                    "persistentVolumeReclaimPolicy": "Retain",
                    "storageClassName": "standard",
                    "hostPath": {
                        "path": "/data/bracket-irl"
                    }
                }
            }
            
            # Create PVC data
            pvc_data = {
                "apiVersion": "v1",
                "kind": "PersistentVolumeClaim",
                "metadata": {
                    "name": "bracket-data-pvc",
                    "namespace": self.namespace
                },
                "spec": {
                    "accessModes": ["ReadWriteOnce"],
                    "storageClassName": "standard",
                    "resources": {
                        "requests": {
                            "storage": "10Gi"
                        }
                    }
                }
            }
            
            # Write PV and PVC to files
            pv_file = os.path.join(self.results_dir, "pv.yaml")
            with open(pv_file, "w") as f:
                yaml.dump(pv_data, f)
            
            pvc_file = os.path.join(self.results_dir, "pvc.yaml")
            with open(pvc_file, "w") as f:
                yaml.dump(pvc_data, f)
            
            # Apply PV and PVC
            subprocess.run(
                ["kubectl", "apply", "-f", pv_file],
                check=True
            )
            
            subprocess.run(
                ["kubectl", "apply", "-f", pvc_file],
                check=True
            )
            
            logger.info("PersistentVolume and PersistentVolumeClaim created successfully")
            return True
        except Exception as e:
            logger.error(f"Error creating PersistentVolume and PersistentVolumeClaim: {str(e)}")
            return False
    
    def deploy_services(self) -> bool:
        """
        Deploy services to Kubernetes.
        
        Returns:
            True if services were deployed successfully, False otherwise
        """
        logger.info("Deploying services...")
        
        try:
            # Apply all YAML files in the k8s directory
            for root, _, files in os.walk(self.k8s_dir):
                for file in files:
                    if file.endswith(".yaml") or file.endswith(".yml"):
                        file_path = os.path.join(root, file)
                        logger.info(f"Applying {file_path}...")
                        subprocess.run(
                            ["kubectl", "apply", "-f", file_path, "-n", self.namespace],
                            check=True
                        )
            
            logger.info("Services deployed successfully")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Error deploying services: {str(e)}")
            return False
    
    def setup_port_forwarding(self) -> bool:
        """
        Set up port forwarding for services.
        
        Returns:
            True if port forwarding was set up successfully, False otherwise
        """
        logger.info("Setting up port forwarding...")
        
        services = [
            {"name": "orchestrator-service", "port": 8000},
            {"name": "repo-mapper-service", "port": 8001},
            {"name": "domain-analyzer-service", "port": 8002},
            {"name": "file-domain-mapper-service", "port": 8003},
            {"name": "domain-file-repomap-service", "port": 8004},
            {"name": "diagram-generator-service", "port": 8005},
            {"name": "prometheus", "port": 9090},
            {"name": "grafana", "port": 3000}
        ]
        
        self.port_forward_processes = []
        
        try:
            for service in services:
                # Start port forwarding
                logger.info(f"Setting up port forwarding for {service['name']}...")
                process = subprocess.Popen(
                    ["kubectl", "port-forward", f"service/{service['name']}", f"{service['port']}:{service['port']}", "-n", self.namespace],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )
                
                self.port_forward_processes.append(process)
                
                # Set service URL
                self.service_urls[service["name"]] = f"http://localhost:{service['port']}"
            
            # Wait for port forwarding to be established
            time.sleep(5)
            
            logger.info("Port forwarding set up successfully")
            return True
        except Exception as e:
            logger.error(f"Error setting up port forwarding: {str(e)}")
            return False
    
    def check_pod_status(self) -> Dict[str, Any]:
        """
        Check status of all pods.
        
        Returns:
            Dictionary of pod status
        """
        logger.info("Checking pod status...")
        
        try:
            result = subprocess.run(
                ["kubectl", "get", "pods", "-n", self.namespace, "-o", "json"],
                check=True,
                capture_output=True,
                text=True
            )
            
            # Parse JSON output
            pods = json.loads(result.stdout).get("items", [])
            
            # Check status of each pod
            pod_status = {}
            for pod in pods:
                name = pod.get("metadata", {}).get("name", "")
                status = pod.get("status", {})
                phase = status.get("phase", "")
                conditions = status.get("conditions", [])
                container_statuses = status.get("containerStatuses", [])
                
                ready_condition = next((c for c in conditions if c.get("type") == "Ready"), {})
                ready = ready_condition.get("status") == "True"
                
                containers_ready = all(cs.get("ready", False) for cs in container_statuses)
                
                pod_status[name] = {
                    "phase": phase,
                    "ready": ready,
                    "containers_ready": containers_ready,
                    "conditions": conditions,
                    "container_statuses": container_statuses
                }
                
                logger.info(f"Pod {name}: Phase={phase}, Ready={ready}, ContainersReady={containers_ready}")
            
            return pod_status
        except subprocess.CalledProcessError as e:
            logger.error(f"Error checking pod status: {str(e)}")
            return {}
    
    def check_services_health(self) -> Dict[str, bool]:
        """
        Check health of all services.
        
        Returns:
            Dictionary of service health status
        """
        logger.info("Checking services health...")
        
        services = [
            {"name": "orchestrator-service", "url": f"{self.service_urls.get('orchestrator-service', '')}/health"},
            {"name": "repo-mapper-service", "url": f"{self.service_urls.get('repo-mapper-service', '')}/health"},
            {"name": "domain-analyzer-service", "url": f"{self.service_urls.get('domain-analyzer-service', '')}/health"},
            {"name": "file-domain-mapper-service", "url": f"{self.service_urls.get('file-domain-mapper-service', '')}/health"},
            {"name": "domain-file-repomap-service", "url": f"{self.service_urls.get('domain-file-repomap-service', '')}/health"},
            {"name": "diagram-generator-service", "url": f"{self.service_urls.get('diagram-generator-service', '')}/health"},
            {"name": "prometheus", "url": f"{self.service_urls.get('prometheus', '')}/-/healthy"},
            {"name": "grafana", "url": f"{self.service_urls.get('grafana', '')}/api/health"}
        ]
        
        service_health = {}
        
        for service in services:
            if not service["url"]:
                service_health[service["name"]] = False
                logger.error(f"{service['name']} service URL not set")
                continue
                
            try:
                response = requests.get(service["url"], headers=self.headers, timeout=10)
                
                if response.status_code == 200:
                    if service["name"] in ["prometheus", "grafana"]:
                        service_health[service["name"]] = True
                        logger.info(f"{service['name']} service is healthy")
                    elif response.json().get("status") == "ok":
                        service_health[service["name"]] = True
                        logger.info(f"{service['name']} service is healthy")
                    else:
                        service_health[service["name"]] = False
                        logger.error(f"{service['name']} service is not healthy: {response.text}")
                else:
                    service_health[service["name"]] = False
                    logger.error(f"{service['name']} service is not healthy: {response.status_code}")
            except Exception as e:
                service_health[service["name"]] = False
                logger.error(f"Error checking {service['name']} service health: {str(e)}")
        
        return service_health
    
    def wait_for_pods(self) -> bool:
        """
        Wait for all pods to be ready.
        
        Returns:
            True if all pods are ready, False otherwise
        """
        logger.info("Waiting for pods to be ready...")
        
        start_time = time.time()
        
        while True:
            # Check if timeout exceeded
            if time.time() - start_time > self.timeout:
                logger.error("Timeout waiting for pods to be ready")
                return False
            
            # Check pod status
            pod_status = self.check_pod_status()
            
            # Check if all pods are ready
            all_ready = all(
                status.get("ready", False) and status.get("phase") == "Running"
                for status in pod_status.values()
            )
            
            if all_ready:
                logger.info("All pods are ready")
                return True
            
            # Wait before checking again
            logger.info(f"Waiting for pods to be ready... (elapsed: {time.time() - start_time:.0f}s)")
            time.sleep(self.poll_interval)
    
    def cleanup(self) -> bool:
        """
        Clean up resources.
        
        Returns:
            True if cleanup was successful, False otherwise
        """
        logger.info("Cleaning up resources...")
        
        try:
            # Stop port forwarding processes
            for process in getattr(self, "port_forward_processes", []):
                process.terminate()
            
            # Delete namespace
            subprocess.run(
                ["kubectl", "delete", "namespace", self.namespace],
                check=True
            )
            
            logger.info("Resources cleaned up successfully")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Error cleaning up resources: {str(e)}")
            return False
    
    def run_test(self) -> Dict[str, Any]:
        """
        Run the Kubernetes test.
        
        Returns:
            Test results
        """
        logger.info("Starting Kubernetes test...")
        
        results = {
            "setup_minikube": False,
            "create_namespace": False,
            "create_configmap": False,
            "create_persistent_volume": False,
            "deploy_services": False,
            "setup_port_forwarding": False,
            "wait_for_pods": False,
            "pod_status": {},
            "service_health": {},
            "cleanup": False
        }
        
        try:
            # Set up Minikube
            results["setup_minikube"] = self.setup_minikube()
            
            if not results["setup_minikube"]:
                logger.error("Failed to set up Minikube")
                return results
            
            # Create namespace
            results["create_namespace"] = self.create_namespace()
            
            if not results["create_namespace"]:
                logger.error("Failed to create namespace")
                return results
            
            # Create ConfigMap
            results["create_configmap"] = self.create_configmap()
            
            if not results["create_configmap"]:
                logger.error("Failed to create ConfigMap")
                return results
            
            # Create PersistentVolume and PersistentVolumeClaim
            results["create_persistent_volume"] = self.create_persistent_volume()
            
            if not results["create_persistent_volume"]:
                logger.error("Failed to create PersistentVolume and PersistentVolumeClaim")
                return results
            
            # Deploy services
            results["deploy_services"] = self.deploy_services()
            
            if not results["deploy_services"]:
                logger.error("Failed to deploy services")
                return results
            
            # Wait for pods to be ready
            results["wait_for_pods"] = self.wait_for_pods()
            
            if not results["wait_for_pods"]:
                logger.error("Failed to wait for pods to be ready")
                return results
            
            # Set up port forwarding
            results["setup_port_forwarding"] = self.setup_port_forwarding()
            
            if not results["setup_port_forwarding"]:
                logger.error("Failed to set up port forwarding")
                return results
            
            # Check pod status
            results["pod_status"] = self.check_pod_status()
            
            # Check service health
            results["service_health"] = self.check_services_health()
            
            # Save results
            with open(os.path.join(self.results_dir, "kubernetes_test_results.json"), "w") as f:
                json.dump(results, f, indent=2, default=str)
            
            logger.info("Kubernetes test completed successfully")
            
            return results
        finally:
            # Clean up resources
            results["cleanup"] = self.cleanup()

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Test Kubernetes deployment for Bracket IRL microservices")
    parser.add_argument("--k8s-dir", required=True, help="Path to the Kubernetes manifests directory")
    parser.add_argument("--repo-dir", required=True, help="Path to the repository to mount")
    parser.add_argument("--namespace", default="bracket-irl", help="Kubernetes namespace")
    parser.add_argument("--api-key", help="API key for the services")
    parser.add_argument("--timeout", type=int, default=600, help="Timeout in seconds for waiting for services to start")
    parser.add_argument("--poll-interval", type=int, default=10, help="Poll interval in seconds for checking service status")
    parser.add_argument("--results-dir", default="results", help="Directory to save results")
    parser.add_argument("--no-minikube", action="store_true", help="Don't use Minikube")
    args = parser.parse_args()
    
    # Create test instance
    test = KubernetesTest(
        k8s_dir=args.k8s_dir,
        repo_dir=args.repo_dir,
        namespace=args.namespace,
        api_key=args.api_key,
        timeout=args.timeout,
        poll_interval=args.poll_interval,
        results_dir=args.results_dir,
        minikube=not args.no_minikube
    )
    
    try:
        # Run test
        results = test.run_test()
        
        # Check if test was successful
        if (results["setup_minikube"] and 
            results["create_namespace"] and 
            results["create_configmap"] and 
            results["create_persistent_volume"] and 
            results["deploy_services"] and 
            results["wait_for_pods"] and 
            results["setup_port_forwarding"] and
            all(status.get("ready", False) for status in results["pod_status"].values()) and
            all(results["service_health"].values())):
            logger.info("Kubernetes test passed")
            return 0
        else:
            logger.error("Kubernetes test failed")
            return 1
    except Exception as e:
        logger.error(f"Kubernetes test failed with exception: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
