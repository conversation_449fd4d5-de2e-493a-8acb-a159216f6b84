#!/usr/bin/env python3
"""
Test script for Docker Compose setup of Bracket IRL microservices.
This script tests the Docker Compose deployment by checking service health,
container status, and resource usage.
"""

import os
import sys
import json
import time
import argparse
import requests
import subprocess
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("docker_compose_test.log")
    ]
)
logger = logging.getLogger("docker_compose_test")

class DockerComposeTest:
    """Test the Docker Compose setup for Bracket IRL microservices."""
    
    def __init__(
        self,
        compose_file: str,
        repo_dir: str,
        api_key: Optional[str] = None,
        timeout: int = 300,
        poll_interval: int = 5,
        results_dir: str = "results"
    ):
        """
        Initialize the test.
        
        Args:
            compose_file: Path to the docker-compose.yml file
            repo_dir: Path to the repository to mount
            api_key: API key for the services
            timeout: Timeout in seconds for waiting for services to start
            poll_interval: Poll interval in seconds for checking service status
            results_dir: Directory to save results
        """
        self.compose_file = os.path.abspath(compose_file)
        self.repo_dir = os.path.abspath(repo_dir)
        self.api_key = api_key
        self.timeout = timeout
        self.poll_interval = poll_interval
        self.results_dir = results_dir
        
        # Create results directory
        os.makedirs(results_dir, exist_ok=True)
        
        # Service URLs
        self.orchestrator_url = "http://localhost:8000"
        self.repo_mapper_url = "http://localhost:8001"
        self.domain_analyzer_url = "http://localhost:8002"
        self.file_domain_mapper_url = "http://localhost:8003"
        self.domain_file_repomap_url = "http://localhost:8004"
        self.diagram_generator_url = "http://localhost:8005"
        self.prometheus_url = "http://localhost:9090"
        self.grafana_url = "http://localhost:3000"
        
        # Headers for API requests
        self.headers = {}
        if api_key:
            self.headers["Authorization"] = f"Bearer {api_key}"
    
    def start_services(self) -> bool:
        """
        Start services using Docker Compose.
        
        Returns:
            True if services started successfully, False otherwise
        """
        logger.info("Starting services using Docker Compose...")
        
        # Set environment variables
        env = os.environ.copy()
        env["REPO_DIR"] = self.repo_dir
        if self.api_key:
            env["OPENAI_API_KEY"] = self.api_key
        
        # Start services
        try:
            subprocess.run(
                ["docker", "compose", "-f", self.compose_file, "up", "-d"],
                check=True,
                env=env
            )
            logger.info("Services started successfully")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Error starting services: {str(e)}")
            return False
    
    def stop_services(self) -> bool:
        """
        Stop services using Docker Compose.
        
        Returns:
            True if services stopped successfully, False otherwise
        """
        logger.info("Stopping services using Docker Compose...")
        
        try:
            subprocess.run(
                ["docker", "compose", "-f", self.compose_file, "down"],
                check=True
            )
            logger.info("Services stopped successfully")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Error stopping services: {str(e)}")
            return False
    
    def check_container_status(self) -> Dict[str, Any]:
        """
        Check status of all containers.
        
        Returns:
            Dictionary of container status
        """
        logger.info("Checking container status...")
        
        try:
            result = subprocess.run(
                ["docker", "compose", "-f", self.compose_file, "ps", "--format", "json"],
                check=True,
                capture_output=True,
                text=True
            )
            
            # Parse JSON output
            containers = []
            for line in result.stdout.strip().split("\n"):
                if line:
                    containers.append(json.loads(line))
            
            # Check status of each container
            container_status = {}
            for container in containers:
                name = container.get("Name")
                status = container.get("State")
                health = container.get("Health")
                
                container_status[name] = {
                    "status": status,
                    "health": health
                }
                
                logger.info(f"Container {name}: Status={status}, Health={health}")
            
            return container_status
        except subprocess.CalledProcessError as e:
            logger.error(f"Error checking container status: {str(e)}")
            return {}
    
    def check_services_health(self) -> Dict[str, bool]:
        """
        Check health of all services.
        
        Returns:
            Dictionary of service health status
        """
        logger.info("Checking services health...")
        
        services = [
            {"name": "Orchestrator", "url": f"{self.orchestrator_url}/health"},
            {"name": "Repository Mapper", "url": f"{self.repo_mapper_url}/health"},
            {"name": "Domain Analyzer", "url": f"{self.domain_analyzer_url}/health"},
            {"name": "File-Domain Mapper", "url": f"{self.file_domain_mapper_url}/health"},
            {"name": "Domain-File Repomap", "url": f"{self.domain_file_repomap_url}/health"},
            {"name": "Diagram Generator", "url": f"{self.diagram_generator_url}/health"},
            {"name": "Prometheus", "url": f"{self.prometheus_url}/-/healthy"},
            {"name": "Grafana", "url": f"{self.grafana_url}/api/health"}
        ]
        
        service_health = {}
        
        for service in services:
            try:
                response = requests.get(service["url"], headers=self.headers, timeout=10)
                
                if response.status_code == 200:
                    if service["name"] in ["Prometheus", "Grafana"]:
                        service_health[service["name"]] = True
                        logger.info(f"{service['name']} service is healthy")
                    elif response.json().get("status") == "ok":
                        service_health[service["name"]] = True
                        logger.info(f"{service['name']} service is healthy")
                    else:
                        service_health[service["name"]] = False
                        logger.error(f"{service['name']} service is not healthy: {response.text}")
                else:
                    service_health[service["name"]] = False
                    logger.error(f"{service['name']} service is not healthy: {response.status_code}")
            except Exception as e:
                service_health[service["name"]] = False
                logger.error(f"Error checking {service['name']} service health: {str(e)}")
        
        return service_health
    
    def check_resource_usage(self) -> Dict[str, Any]:
        """
        Check resource usage of all containers.
        
        Returns:
            Dictionary of container resource usage
        """
        logger.info("Checking container resource usage...")
        
        try:
            result = subprocess.run(
                ["docker", "stats", "--no-stream", "--format", "{{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"],
                check=True,
                capture_output=True,
                text=True
            )
            
            # Parse output
            resource_usage = {}
            for line in result.stdout.strip().split("\n"):
                if line:
                    parts = line.split("\t")
                    if len(parts) >= 5:
                        name = parts[0]
                        cpu = parts[1]
                        mem = parts[2]
                        net = parts[3]
                        io = parts[4]
                        
                        resource_usage[name] = {
                            "cpu": cpu,
                            "memory": mem,
                            "network": net,
                            "io": io
                        }
                        
                        logger.info(f"Container {name}: CPU={cpu}, Memory={mem}, Network={net}, IO={io}")
            
            return resource_usage
        except subprocess.CalledProcessError as e:
            logger.error(f"Error checking resource usage: {str(e)}")
            return {}
    
    def wait_for_services(self) -> bool:
        """
        Wait for all services to be healthy.
        
        Returns:
            True if all services are healthy, False otherwise
        """
        logger.info("Waiting for services to be healthy...")
        
        start_time = time.time()
        
        while True:
            # Check if timeout exceeded
            if time.time() - start_time > self.timeout:
                logger.error("Timeout waiting for services to be healthy")
                return False
            
            # Check container status
            container_status = self.check_container_status()
            
            # Check if all containers are running
            all_running = all(
                status.get("status") == "running"
                for status in container_status.values()
            )
            
            if all_running:
                # Check service health
                service_health = self.check_services_health()
                
                # Check if all services are healthy
                all_healthy = all(service_health.values())
                
                if all_healthy:
                    logger.info("All services are healthy")
                    return True
            
            # Wait before checking again
            logger.info(f"Waiting for services to be healthy... (elapsed: {time.time() - start_time:.0f}s)")
            time.sleep(self.poll_interval)
    
    def run_test(self) -> Dict[str, Any]:
        """
        Run the Docker Compose test.
        
        Returns:
            Test results
        """
        logger.info("Starting Docker Compose test...")
        
        results = {
            "start_services": False,
            "wait_for_services": False,
            "container_status": {},
            "service_health": {},
            "resource_usage": {},
            "stop_services": False
        }
        
        try:
            # Start services
            results["start_services"] = self.start_services()
            
            if not results["start_services"]:
                logger.error("Failed to start services")
                return results
            
            # Wait for services to be healthy
            results["wait_for_services"] = self.wait_for_services()
            
            if not results["wait_for_services"]:
                logger.error("Failed to wait for services to be healthy")
                return results
            
            # Check container status
            results["container_status"] = self.check_container_status()
            
            # Check service health
            results["service_health"] = self.check_services_health()
            
            # Check resource usage
            results["resource_usage"] = self.check_resource_usage()
            
            # Save results
            with open(os.path.join(self.results_dir, "docker_compose_test_results.json"), "w") as f:
                json.dump(results, f, indent=2)
            
            logger.info("Docker Compose test completed successfully")
            
            return results
        finally:
            # Stop services
            results["stop_services"] = self.stop_services()

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Test Docker Compose setup for Bracket IRL microservices")
    parser.add_argument("--compose-file", default="bracket_core/bracket_irl/microservices/docker-compose.yml", help="Path to the docker-compose.yml file")
    parser.add_argument("--repo-dir", required=True, help="Path to the repository to mount")
    parser.add_argument("--api-key", help="API key for the services")
    parser.add_argument("--timeout", type=int, default=300, help="Timeout in seconds for waiting for services to start")
    parser.add_argument("--poll-interval", type=int, default=5, help="Poll interval in seconds for checking service status")
    parser.add_argument("--results-dir", default="results", help="Directory to save results")
    args = parser.parse_args()
    
    # Create test instance
    test = DockerComposeTest(
        compose_file=args.compose_file,
        repo_dir=args.repo_dir,
        api_key=args.api_key,
        timeout=args.timeout,
        poll_interval=args.poll_interval,
        results_dir=args.results_dir
    )
    
    try:
        # Run test
        results = test.run_test()
        
        # Check if test was successful
        if (results["start_services"] and 
            results["wait_for_services"] and 
            all(status.get("status") == "running" for status in results["container_status"].values()) and
            all(results["service_health"].values())):
            logger.info("Docker Compose test passed")
            return 0
        else:
            logger.error("Docker Compose test failed")
            return 1
    except Exception as e:
        logger.error(f"Docker Compose test failed with exception: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
