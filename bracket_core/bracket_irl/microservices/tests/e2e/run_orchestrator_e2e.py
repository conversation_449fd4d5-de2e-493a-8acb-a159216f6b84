#!/usr/bin/env python3
"""
Helper script to run the orchestrator-based E2E test for Bracket IRL microservices.
This script is a wrapper around the test_orchestrator_e2e.py script.
"""

import os
import sys
import argparse
import subprocess
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("run_orchestrator_e2e.log")
    ]
)
logger = logging.getLogger("run_orchestrator_e2e")

def run_orchestrator_e2e_test(
    repo_dir: str,
    api_key: str = None,
    results_dir: str = "results/orchestrator_e2e",
    docker_mode: bool = False
):
    """
    Run the orchestrator-based E2E test.
    
    Args:
        repo_dir: Path to the repository to analyze
        api_key: API key for the services
        results_dir: Directory to save results
        docker_mode: Whether to run in Docker mode
    
    Returns:
        True if the test passed, False otherwise
    """
    logger.info("Running orchestrator-based E2E test...")
    
    # Get the script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Get the test script path
    test_script = os.path.join(script_dir, "test_orchestrator_e2e.py")
    
    # Create the command
    cmd = [
        sys.executable,
        test_script,
        "--repo-dir", repo_dir,
        "--results-dir", results_dir
    ]
    
    # Add optional arguments
    if api_key:
        cmd.extend(["--api-key", api_key])
    
    if docker_mode:
        cmd.append("--docker-mode")
    
    # Run the test
    try:
        logger.info(f"Running command: {' '.join(cmd)}")
        result = subprocess.run(cmd, check=False)
        
        if result.returncode == 0:
            logger.info("Orchestrator-based E2E test passed")
            return True
        else:
            logger.error(f"Orchestrator-based E2E test failed with return code {result.returncode}")
            return False
    except Exception as e:
        logger.error(f"Error running orchestrator-based E2E test: {str(e)}")
        return False

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Run the orchestrator-based E2E test for Bracket IRL microservices")
    parser.add_argument("--repo-dir", required=True, help="Path to the repository to analyze")
    parser.add_argument("--api-key", help="API key for the services")
    parser.add_argument("--results-dir", default="results/orchestrator_e2e", help="Directory to save results")
    parser.add_argument("--docker-mode", action="store_true", help="Enable Docker mode (use /repo path)")
    args = parser.parse_args()
    
    # Run the test
    success = run_orchestrator_e2e_test(
        repo_dir=args.repo_dir,
        api_key=args.api_key,
        results_dir=args.results_dir,
        docker_mode=args.docker_mode
    )
    
    # Return the appropriate exit code
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
