version: '3.8'

volumes:
  bracket_artifacts:
    external: true

services:
  # Repository Mapper Service
  repo-mapper-service:
    volumes:
      - /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/microservices/tests/e2e/tests/e2e/results/test_repo:/repo
      - bracket_artifacts:/app/data
    environment:
      - PYTHONPATH=/app:/
      - STORAGE_PATH=/app/data

  # Domain Analyzer Service
  domain-analyzer-service:
    volumes:
      - /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/microservices/tests/e2e/tests/e2e/results/test_repo:/repo
      - bracket_artifacts:/app/data
    environment:
      - PYTHONPATH=/app:/
      - STORAGE_PATH=/app/data
      - OPENAI_API_KEY=********************************************************************************************************************************************************************

  # Domain-File Repomap Service
  domain-file-repomap-service:
    volumes:
      - /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/microservices/tests/e2e/tests/e2e/results/test_repo:/repo
      - bracket_artifacts:/app/data
    environment:
      - PYTHONPATH=/app:/
      - STORAGE_PATH=/app/data

  # File-Domain Mapper Service
  file-domain-mapper-service:
    volumes:
      - /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/microservices/tests/e2e/tests/e2e/results/test_repo:/repo
      - bracket_artifacts:/app/data
    environment:
      - PYTHONPATH=/app:/
      - STORAGE_PATH=/app/data
      - OPENAI_API_KEY=********************************************************************************************************************************************************************

  # Diagram Generator Service
  diagram-generator-service:
    volumes:
      - /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/microservices/tests/e2e/tests/e2e/results/test_repo:/repo
      - bracket_artifacts:/app/data
    environment:
      - PYTHONPATH=/app:/
      - STORAGE_PATH=/app/data
      - OPENAI_API_KEY=********************************************************************************************************************************************************************

  # Orchestrator Service
  orchestrator-service:
    volumes:
      - /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/microservices/tests/e2e/tests/e2e/results/test_repo:/repo
      - bracket_artifacts:/app/data
    environment:
      - PYTHONPATH=/app:/
      - STORAGE_PATH=/app/data
