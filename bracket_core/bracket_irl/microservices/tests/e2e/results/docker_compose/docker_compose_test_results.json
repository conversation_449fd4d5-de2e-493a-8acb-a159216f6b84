{"start_services": true, "wait_for_services": true, "container_status": {"microservices-diagram-generator-service-1": {"status": "running", "health": "healthy"}, "microservices-domain-analyzer-service-1": {"status": "running", "health": "healthy"}, "microservices-domain-file-repomap-service-1": {"status": "running", "health": "starting"}, "microservices-file-domain-mapper-service-1": {"status": "running", "health": "healthy"}, "microservices-grafana-1": {"status": "running", "health": ""}, "microservices-orchestrator-service-1": {"status": "running", "health": "healthy"}, "microservices-prometheus-1": {"status": "running", "health": ""}, "microservices-repo-mapper-service-1": {"status": "running", "health": "healthy"}}, "service_health": {"Orchestrator": true, "Repository Mapper": true, "Domain Analyzer": true, "File-Domain Mapper": true, "Domain-File Repomap": true, "Diagram Generator": true, "Prometheus": true, "Grafana": true}, "resource_usage": {"microservices-orchestrator-service-1": {"cpu": "0.29%", "memory": "42.89MiB / 31.29GiB", "network": "8.01kB / 4.31kB", "io": "0B / 381kB"}, "microservices-domain-analyzer-service-1": {"cpu": "0.29%", "memory": "84.86MiB / 31.29GiB", "network": "1.78MB / 22.8kB", "io": "0B / 2.08MB"}, "microservices-repo-mapper-service-1": {"cpu": "0.63%", "memory": "40.76MiB / 31.29GiB", "network": "9.2kB / 5.66kB", "io": "0B / 348kB"}, "microservices-diagram-generator-service-1": {"cpu": "0.32%", "memory": "40.36MiB / 31.29GiB", "network": "7.51kB / 3.4kB", "io": "0B / 291kB"}, "microservices-domain-file-repomap-service-1": {"cpu": "0.26%", "memory": "83.62MiB / 31.29GiB", "network": "1.77MB / 28.3kB", "io": "0B / 176kB"}, "microservices-grafana-1": {"cpu": "0.05%", "memory": "74.76MiB / 31.29GiB", "network": "19.1kB / 5.38kB", "io": "168kB / 81.9kB"}, "microservices-file-domain-mapper-service-1": {"cpu": "0.30%", "memory": "42.71MiB / 31.29GiB", "network": "8.15kB / 3.83kB", "io": "0B / 291kB"}, "microservices-prometheus-1": {"cpu": "0.23%", "memory": "24.9MiB / 31.29GiB", "network": "15.3kB / 9.69kB", "io": "799kB / 20.5kB"}}, "stop_services": false}