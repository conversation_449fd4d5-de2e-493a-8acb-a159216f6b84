import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional

from src.models.data_model import DataModel, DataResponse

class DataService:
    def __init__(self):
        self.data = {}
    
    def get_all_data(self) -> List[DataResponse]:
        """Get all data."""
        return list(self.data.values())
    
    def get_data_by_id(self, data_id: str) -> Optional[DataResponse]:
        """Get data by ID."""
        return self.data.get(data_id)
    
    def create_data(self, data: DataModel) -> DataResponse:
        """Create new data."""
        data_id = str(uuid.uuid4())
        data_response = DataResponse(
            id=data_id,
            name=data.name,
            description=data.description,
            metadata=data.metadata,
            created_at=datetime.now()
        )
        self.data[data_id] = data_response
        return data_response

def get_data_service() -> DataService:
    """Get data service instance."""
    return DataService()
