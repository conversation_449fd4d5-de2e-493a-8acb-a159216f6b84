
import os
from pydantic import BaseSettings

class Settings(BaseSettings):
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Test API"
    
    # Database settings
    DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite:///./test.db")
    
    # API settings
    API_KEY: str = os.getenv("API_KEY", "test-api-key")
    
    class Config:
        env_file = ".env"

def get_config() -> Settings:
    return Settings()
