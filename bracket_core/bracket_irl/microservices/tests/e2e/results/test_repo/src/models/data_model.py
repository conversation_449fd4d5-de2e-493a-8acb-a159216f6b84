
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime
from uuid import UUID, uuid4

class DataModel(BaseModel):
    name: str
    description: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    
    class Config:
        schema_extra = {
            "example": {
                "name": "Test Data",
                "description": "This is test data",
                "metadata": {"key": "value"}
            }
        }

class DataResponse(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    created_at: datetime
    
    class Config:
        schema_extra = {
            "example": {
                "id": "123e4567-e89b-12d3-a456-************",
                "name": "Test Data",
                "description": "This is test data",
                "metadata": {"key": "value"},
                "created_at": "2023-01-01T00:00:00"
            }
        }
