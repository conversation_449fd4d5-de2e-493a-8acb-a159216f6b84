from fastapi import APIRouter, Depends, HTTPException
from typing import List, Dict, Any

from src.models.data_model import DataModel, DataResponse
from src.services.data_service import DataService, get_data_service

router = APIRouter()

@router.get("/data", response_model=List[DataResponse])
async def get_data(
    data_service: DataService = Depends(get_data_service)
):
    """Get all data."""
    return data_service.get_all_data()

@router.post("/data", response_model=DataResponse)
async def create_data(
    data: DataModel,
    data_service: DataService = Depends(get_data_service)
):
    """Create new data."""
    return data_service.create_data(data)

@router.get("/data/{data_id}", response_model=DataResponse)
async def get_data_by_id(
    data_id: str,
    data_service: DataService = Depends(get_data_service)
):
    """Get data by ID."""
    data = data_service.get_data_by_id(data_id)
    if not data:
        raise HTTPException(status_code=404, detail="Data not found")
    return data
