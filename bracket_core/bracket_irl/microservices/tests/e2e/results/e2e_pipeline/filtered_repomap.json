{"sample.py": ["def sample_function():"], "src/models/data_model.py": ["from pydantic import BaseModel", "class DataModel(BaseModel):", "class DataResponse(BaseModel):"], "src/core/config.py": ["import os", "class Settings(BaseSettings):"], "src/services/data_service.py": ["class DataService:", "    def __init__(self):", "    def create_data(self, data: DataModel) -> DataResponse:", "def get_data_service() -> DataService:"], "src/api/routes.py": ["@router.post(\"/data\", response_model=DataResponse)", "async def create_data(", "    data: DataModel,", "    data_service: DataService = Depends(get_data_service)", "@router.get(\"/data/{data_id}\", response_model=DataResponse)", "async def get_data_by_id(", "    data_id: str,", "    data_service: DataService = Depends(get_data_service)"]}