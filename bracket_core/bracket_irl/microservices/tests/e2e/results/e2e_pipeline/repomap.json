{"sample.py": ["def sample_function():"], "src/models/data_model.py": ["from pydantic import BaseModel", "class DataModel(BaseModel):", "    name: str", "    class Config:", "class DataResponse(BaseModel):", "    id: str", "    class Config:"], "src/core/config.py": ["import os", "class Settings(BaseSettings):", "    API_V1_STR: str = \"/api/v1\"", "    class Config:", "def get_config() -> Settings:"], "src/services/data_service.py": ["class DataService:", "    def __init__(self):", "    def get_all_data(self) -> List[DataResponse]:", "    def get_data_by_id(self, data_id: str) -> Optional[DataResponse]:", "    def create_data(self, data: DataModel) -> DataResponse:", "def get_data_service() -> DataService:"], "src/api/routes.py": ["@router.get(\"/data\", response_model=List[DataResponse])", "async def get_data(", "    data_service: DataService = Depends(get_data_service)", "@router.post(\"/data\", response_model=DataResponse)", "async def create_data(", "    data: DataModel,", "    data_service: DataService = Depends(get_data_service)", "@router.get(\"/data/{data_id}\", response_model=DataResponse)", "async def get_data_by_id(", "    data_id: str,", "    data_service: DataService = Depends(get_data_service)"]}