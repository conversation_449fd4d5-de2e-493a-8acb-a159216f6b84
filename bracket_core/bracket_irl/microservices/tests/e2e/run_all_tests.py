#!/usr/bin/env python3
"""
Master script to run all tests for Bracket IRL microservices.
This script runs the following tests:
1. Create test repository
2. Test Docker Compose setup
3. Test E2E pipeline (individual service approach)
4. Test E2E pipeline using orchestrator
5. Test Kubernetes deployment (optional)
"""

import os
import sys
import json
import time
import argparse
import subprocess
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("all_tests.log")
    ]
)
logger = logging.getLogger("all_tests")

class BracketIRLTestRunner:
    """Run all tests for Bracket IRL microservices."""

    def __init__(
        self,
        repo_dir: Optional[str] = None,
        api_key: Optional[str] = None,
        k8s_dir: Optional[str] = None,
        results_dir: str = "results",
        skip_docker: bool = False,
        skip_e2e: bool = False,
        skip_orchestrator_e2e: bool = False,
        skip_k8s: bool = False
    ):
        """
        Initialize the test runner.

        Args:
            repo_dir: Path to the repository to analyze (will be created if None)
            api_key: API key for the services
            k8s_dir: Path to the Kubernetes manifests directory
            results_dir: Directory to save results
            skip_docker: Whether to skip Docker Compose tests
            skip_e2e: Whether to skip individual service E2E pipeline tests
            skip_orchestrator_e2e: Whether to skip orchestrator-based E2E pipeline tests
            skip_k8s: Whether to skip Kubernetes tests
        """
        self.repo_dir = repo_dir
        self.api_key = api_key
        self.k8s_dir = k8s_dir
        self.results_dir = results_dir
        self.skip_docker = skip_docker
        self.skip_e2e = skip_e2e
        self.skip_orchestrator_e2e = skip_orchestrator_e2e
        self.skip_k8s = skip_k8s

        # Create results directory
        os.makedirs(results_dir, exist_ok=True)

        # Set script directory
        self.script_dir = os.path.dirname(os.path.abspath(__file__))

        # Set microservices directory
        self.microservices_dir = os.path.abspath(os.path.join(self.script_dir, "../.."))

    def create_test_repo(self) -> str:
        """
        Create test repository.

        Returns:
            Path to the created repository
        """
        logger.info("Creating test repository...")

        if self.repo_dir:
            logger.info(f"Using existing repository at {self.repo_dir}")
            return self.repo_dir

        # Create test repository
        test_repo_script = os.path.join(self.script_dir, "scripts", "create_test_repo.py")
        test_repo_dir = os.path.join(self.results_dir, "test_repo")

        try:
            subprocess.run(
                [sys.executable, test_repo_script, "--output", test_repo_dir],
                check=True
            )

            logger.info(f"Test repository created at {test_repo_dir}")
            self.repo_dir = test_repo_dir
            return test_repo_dir
        except subprocess.CalledProcessError as e:
            logger.error(f"Error creating test repository: {str(e)}")
            raise

    def run_docker_compose_test(self) -> bool:
        """
        Run Docker Compose test.

        Returns:
            True if test passed, False otherwise
        """
        if self.skip_docker:
            logger.info("Skipping Docker Compose test")
            return True

        logger.info("Running Docker Compose test...")

        docker_compose_test_script = os.path.join(self.script_dir, "test_docker_compose.py")
        docker_compose_file = os.path.join(self.microservices_dir, "docker-compose.yml")

        try:
            cmd = [
                sys.executable,
                docker_compose_test_script,
                "--compose-file", docker_compose_file,
                "--repo-dir", self.repo_dir,
                "--results-dir", os.path.join(self.results_dir, "docker_compose")
            ]

            if self.api_key:
                cmd.extend(["--api-key", self.api_key])

            result = subprocess.run(cmd, check=False)

            if result.returncode == 0:
                logger.info("Docker Compose test passed")
                return True
            else:
                logger.error("Docker Compose test failed")
                return False
        except Exception as e:
            logger.error(f"Error running Docker Compose test: {str(e)}")
            return False

    def run_e2e_pipeline_test(self) -> bool:
        """
        Run individual service E2E pipeline test.

        Returns:
            True if test passed, False otherwise
        """
        if self.skip_e2e:
            logger.info("Skipping individual service E2E pipeline test")
            return True

        logger.info("Running individual service E2E pipeline test...")

        e2e_test_script = os.path.join(self.script_dir, "test_e2e_pipeline.py")

        try:
            cmd = [
                sys.executable,
                e2e_test_script,
                "--repo-dir", self.repo_dir,
                "--results-dir", os.path.join(self.results_dir, "e2e_pipeline")
            ]

            if self.api_key:
                cmd.extend(["--api-key", self.api_key])

            result = subprocess.run(cmd, check=False)

            if result.returncode == 0:
                logger.info("Individual service E2E pipeline test passed")
                return True
            else:
                logger.error("Individual service E2E pipeline test failed")
                return False
        except Exception as e:
            logger.error(f"Error running individual service E2E pipeline test: {str(e)}")
            return False

    def run_orchestrator_e2e_test(self) -> bool:
        """
        Run orchestrator-based E2E pipeline test.

        Returns:
            True if test passed, False otherwise
        """
        if self.skip_orchestrator_e2e:
            logger.info("Skipping orchestrator-based E2E pipeline test")
            return True

        logger.info("Running orchestrator-based E2E pipeline test...")

        orchestrator_e2e_script = os.path.join(self.script_dir, "run_orchestrator_e2e.py")

        try:
            cmd = [
                sys.executable,
                orchestrator_e2e_script,
                "--repo-dir", self.repo_dir,
                "--results-dir", os.path.join(self.results_dir, "orchestrator_e2e")
            ]

            if self.api_key:
                cmd.extend(["--api-key", self.api_key])

            result = subprocess.run(cmd, check=False)

            if result.returncode == 0:
                logger.info("Orchestrator-based E2E pipeline test passed")
                return True
            else:
                logger.error("Orchestrator-based E2E pipeline test failed")
                return False
        except Exception as e:
            logger.error(f"Error running orchestrator-based E2E pipeline test: {str(e)}")
            return False

    def run_kubernetes_test(self) -> bool:
        """
        Run Kubernetes test.

        Returns:
            True if test passed, False otherwise
        """
        if self.skip_k8s or not self.k8s_dir:
            logger.info("Skipping Kubernetes test")
            return True

        logger.info("Running Kubernetes test...")

        k8s_test_script = os.path.join(self.script_dir, "test_kubernetes.py")

        try:
            cmd = [
                sys.executable,
                k8s_test_script,
                "--k8s-dir", self.k8s_dir,
                "--repo-dir", self.repo_dir,
                "--results-dir", os.path.join(self.results_dir, "kubernetes")
            ]

            if self.api_key:
                cmd.extend(["--api-key", self.api_key])

            result = subprocess.run(cmd, check=False)

            if result.returncode == 0:
                logger.info("Kubernetes test passed")
                return True
            else:
                logger.error("Kubernetes test failed")
                return False
        except Exception as e:
            logger.error(f"Error running Kubernetes test: {str(e)}")
            return False

    def run_all_tests(self) -> Dict[str, bool]:
        """
        Run all tests.

        Returns:
            Dictionary of test results
        """
        logger.info("Running all tests...")

        results = {
            "create_test_repo": False,
            "docker_compose_test": False,
            "e2e_pipeline_test": False,
            "orchestrator_e2e_test": False,
            "kubernetes_test": False
        }

        try:
            # Create test repository
            self.create_test_repo()
            results["create_test_repo"] = True

            # Run Docker Compose test
            results["docker_compose_test"] = self.run_docker_compose_test()

            # Run individual service E2E pipeline test
            results["e2e_pipeline_test"] = self.run_e2e_pipeline_test()

            # Run orchestrator-based E2E pipeline test
            results["orchestrator_e2e_test"] = self.run_orchestrator_e2e_test()

            # Run Kubernetes test
            results["kubernetes_test"] = self.run_kubernetes_test()

            # Save results
            with open(os.path.join(self.results_dir, "all_tests_results.json"), "w") as f:
                json.dump(results, f, indent=2)

            logger.info(f"All tests completed: {results}")

            return results
        except Exception as e:
            logger.error(f"Error running all tests: {str(e)}")
            return results

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Run all tests for Bracket IRL microservices")
    parser.add_argument("--repo-dir", help="Path to the repository to analyze (will be created if not provided)")
    parser.add_argument("--api-key", help="API key for the services")
    parser.add_argument("--k8s-dir", help="Path to the Kubernetes manifests directory")
    parser.add_argument("--results-dir", default="results", help="Directory to save results")
    parser.add_argument("--skip-docker", action="store_true", help="Skip Docker Compose tests")
    parser.add_argument("--skip-e2e", action="store_true", help="Skip individual service E2E pipeline tests")
    parser.add_argument("--skip-orchestrator-e2e", action="store_true", help="Skip orchestrator-based E2E pipeline tests")
    parser.add_argument("--skip-k8s", action="store_true", help="Skip Kubernetes tests")
    args = parser.parse_args()

    # Create test runner
    test_runner = BracketIRLTestRunner(
        repo_dir=args.repo_dir,
        api_key=args.api_key,
        k8s_dir=args.k8s_dir,
        results_dir=args.results_dir,
        skip_docker=args.skip_docker,
        skip_e2e=args.skip_e2e,
        skip_orchestrator_e2e=args.skip_orchestrator_e2e,
        skip_k8s=args.skip_k8s
    )

    try:
        # Run all tests
        results = test_runner.run_all_tests()

        # Check if all tests passed
        if all(results.values()):
            logger.info("All tests passed")
            return 0
        else:
            logger.error("Some tests failed")
            return 1
    except Exception as e:
        logger.error(f"Error running tests: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
