# Bracket IRL Microservices Testing Framework

This directory contains a comprehensive testing framework for the Bracket IRL microservices architecture. The framework is designed to test the entire system from end to end, including both the functionality of the microservices and the containerized deployment.

## Overview

The testing framework consists of several components:

1. **Test Repository Creation**: A script to create a small test repository for testing purposes.
2. **E2E Pipeline Testing**: Tests the entire pipeline from repository mapping to diagram generation.
   - **Individual Service Testing**: Tests each service individually in sequence.
   - **Orchestrator-Based Testing**: Tests the entire pipeline through the orchestrator service.
3. **Docker Compose Testing**: Tests the Docker Compose deployment of the microservices.
4. **Kubernetes Testing**: Tests the Kubernetes deployment of the microservices using Minikube.
5. **Master Test Runner**: A script to run all tests in sequence.

## Prerequisites

- Python 3.10+
- Docker and Docker Compose
- Minikube (for Kubernetes testing)
- kubectl (for Kubernetes testing)
- OpenAI API key (or other LLM provider API keys)

## Directory Structure

```
tests/e2e/
├── README.md                      # This file
├── run_all_tests.py               # Master test runner
├── test_docker_compose.py         # Docker Compose test script
├── test_e2e_pipeline.py           # Individual service E2E test script
├── test_orchestrator_e2e.py       # Orchestrator-based E2E test script
├── run_e2e_test.sh                # Script to run individual service E2E test
├── run_orchestrator_e2e_test.sh   # Script to run orchestrator-based E2E test
├── test_kubernetes.py             # Kubernetes test script
├── scripts/                       # Helper scripts
│   └── create_test_repo.py        # Script to create test repository
├── test_repos/                    # Directory for test repositories
└── results/                       # Directory for test results
```

## Usage

### Running All Tests

To run all tests, use the `run_all_tests.py` script:

```bash
cd bracket_core/bracket_irl/microservices
python tests/e2e/run_all_tests.py --api-key your-openai-api-key
```

This will:
1. Create a test repository
2. Test the Docker Compose deployment
3. Test the E2E pipeline
4. Test the Kubernetes deployment (if `--k8s-dir` is provided)

### Options

The `run_all_tests.py` script accepts the following options:

- `--repo-dir`: Path to an existing repository to analyze (will be created if not provided)
- `--api-key`: API key for the LLM services
- `--k8s-dir`: Path to the Kubernetes manifests directory
- `--results-dir`: Directory to save results (default: `results`)
- `--skip-docker`: Skip Docker Compose tests
- `--skip-e2e`: Skip E2E pipeline tests
- `--skip-k8s`: Skip Kubernetes tests

### Running Individual Tests

You can also run individual tests directly:

#### Individual Service E2E Test

```bash
cd bracket_core/bracket_irl/microservices
export OPENAI_API_KEY=your-openai-api-key
./tests/e2e/run_e2e_test.sh
```

Or run the Python script directly:

```bash
python tests/e2e/test_e2e_pipeline.py --repo-dir path/to/repo --api-key your-openai-api-key
```

#### Orchestrator-Based E2E Test

```bash
cd bracket_core/bracket_irl/microservices
export OPENAI_API_KEY=your-openai-api-key
./tests/e2e/run_orchestrator_e2e_test.sh
```

Or run the Python script directly:

```bash
python tests/e2e/test_orchestrator_e2e.py --repo-dir path/to/repo --api-key your-openai-api-key
```

#### Docker Compose Test

```bash
python tests/e2e/test_docker_compose.py --compose-file docker-compose.yml --repo-dir path/to/repo --api-key your-openai-api-key
```

#### Kubernetes Test

```bash
python tests/e2e/test_kubernetes.py --k8s-dir kubernetes/overlays/local --repo-dir path/to/repo --api-key your-openai-api-key
```

## Test Results

Test results are saved in the `results` directory (or the directory specified with `--results-dir`). Each test creates its own subdirectory with detailed logs and results.

## Kubernetes Deployment

The testing framework includes Kubernetes manifests for deploying the microservices to both local Minikube and GKE environments. The manifests are organized using Kustomize:

```
kubernetes/
├── base/                      # Base Kubernetes manifests
└── overlays/
    ├── local/                 # Overlay for local Minikube deployment
    └── gke/                   # Overlay for GKE deployment
```

### Local Deployment

To deploy to Minikube:

```bash
cd bracket_core/bracket_irl/microservices
kubectl apply -k kubernetes/overlays/local
```

### GKE Deployment

To deploy to GKE:

```bash
cd bracket_core/bracket_irl/microservices
kubectl apply -k kubernetes/overlays/gke
```

## Troubleshooting

### Docker Compose Issues

If you encounter issues with the Docker Compose test:

1. Check that Docker and Docker Compose are installed and running
2. Check that the images are built correctly
3. Check the Docker Compose logs for errors

### Kubernetes Issues

If you encounter issues with the Kubernetes test:

1. Check that Minikube is running
2. Check that kubectl is configured correctly
3. Check the pod logs for errors

### API Key Issues

If you encounter issues with the API key:

1. Check that the API key is valid
2. Check that the API key is passed correctly to the services
3. Check the service logs for API-related errors

### E2E Testing Issues

If you encounter issues with the E2E tests:

1. Check that all services are healthy before running the test
2. Verify that the repository directory exists and is accessible
3. Check that the API key is properly set in the environment
4. For orchestrator-based testing, check that the orchestrator service is running and healthy
5. Examine the logs in the results directory for detailed error information

## Contributing

To contribute to the testing framework:

1. Add new test cases to the existing test scripts
2. Add new test scripts for specific components
3. Update the master test runner to include new tests
4. Update this README with new information

## License

This testing framework is part of the Bracket IRL project and is subject to the same license.
