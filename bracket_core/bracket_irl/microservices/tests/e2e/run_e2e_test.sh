#!/bin/bash
# Run the end-to-end test for the Bracket IRL microservices pipeline

# Set the repository directory to the test repository
export REPO_DIR=$(pwd)/tests/e2e/results/test_repo

# Create the test repository directory if it doesn't exist
mkdir -p "$REPO_DIR"
echo "Created test repository directory: $REPO_DIR"

# Add a sample file to the test repository so it's not empty
echo "# Sample repository for testing" > "$REPO_DIR/README.md"
echo "def sample_function():" > "$REPO_DIR/sample.py"
echo "    return 'Hello, World!'" >> "$REPO_DIR/sample.py"

echo "Using test repository: $REPO_DIR"
echo "This will be mounted as /repo in the Docker containers"

# Create docker-compose.override.yml to mount the test repository
cat > docker-compose.override.yml << EOF
version: '3.8'

services:
  # Repository Mapper Service
  repo-mapper-service:
    volumes:
      - ${REPO_DIR}:/repo
      - $(pwd)/tests/e2e/results:/app/tests/e2e/results
    environment:
      - PYTHONPATH=/app:/

  # Domain Analyzer Service
  domain-analyzer-service:
    volumes:
      - ${REPO_DIR}:/repo
      - $(pwd)/tests/e2e/results:/app/tests/e2e/results
    environment:
      - PYTHONPATH=/app:/

  # Domain-File Repomap Service
  domain-file-repomap-service:
    volumes:
      - ${REPO_DIR}:/repo
    environment:
      - PYTHONPATH=/app:/

  # File-Domain Mapper Service
  file-domain-mapper-service:
    volumes:
      - ${REPO_DIR}:/repo
    environment:
      - PYTHONPATH=/app:/

  # Diagram Generator Service
  diagram-generator-service:
    volumes:
      - ${REPO_DIR}:/repo
    environment:
      - PYTHONPATH=/app:/

  # Orchestrator Service
  orchestrator-service:
    volumes:
      - ${REPO_DIR}:/repo
    environment:
      - PYTHONPATH=/app:/
EOF

# Copy the implementation files
echo "Copying the implementation files..."
# Skip the copy implementation step for now
# cd repo-mapper-service
# ./copy_implementation.sh
# cd ..

# Rebuild and restart the services
echo "Rebuilding and restarting services..."
docker compose down
docker compose build repo-mapper-service
docker compose up -d

# Wait for services to start
echo "Waiting for services to start..."
echo "This may take up to 60 seconds..."

# Wait for all services to be healthy with a timeout
MAX_RETRIES=12  # 12 retries * 5 seconds = 60 seconds max wait time
RETRY_COUNT=0
ALL_HEALTHY=false

while [ $RETRY_COUNT -lt $MAX_RETRIES ] && [ "$ALL_HEALTHY" != "true" ]; do
  echo "Checking service health (attempt $((RETRY_COUNT+1))/$MAX_RETRIES)..."

  # Check repo-mapper-service health
  REPO_MAPPER_HEALTH=$(curl -s http://localhost:8001/health || echo '{"status":"error"}')
  REPO_MAPPER_STATUS=$(echo $REPO_MAPPER_HEALTH | grep -o '"status":"[^"]*"' | cut -d'"' -f4)

  # Check file-domain-mapper-service health
  FILE_DOMAIN_HEALTH=$(curl -s http://localhost:8003/health || echo '{"status":"error"}')
  FILE_DOMAIN_STATUS=$(echo $FILE_DOMAIN_HEALTH | grep -o '"status":"[^"]*"' | cut -d'"' -f4)

  # Check domain-file-repomap-service health
  DOMAIN_FILE_HEALTH=$(curl -s http://localhost:8004/health || echo '{"status":"error"}')
  DOMAIN_FILE_STATUS=$(echo $DOMAIN_FILE_HEALTH | grep -o '"status":"[^"]*"' | cut -d'"' -f4)

  # Check diagram-generator-service health
  DIAGRAM_HEALTH=$(curl -s http://localhost:8005/health || echo '{"status":"error"}')
  DIAGRAM_STATUS=$(echo $DIAGRAM_HEALTH | grep -o '"status":"[^"]*"' | cut -d'"' -f4)

  echo "Repository Mapper: $REPO_MAPPER_STATUS"
  echo "File-Domain Mapper: $FILE_DOMAIN_STATUS"
  echo "Domain-File Repomap: $DOMAIN_FILE_STATUS"
  echo "Diagram Generator: $DIAGRAM_STATUS"

  if [ "$REPO_MAPPER_STATUS" = "ok" ] && [ "$FILE_DOMAIN_STATUS" = "ok" ] && [ "$DOMAIN_FILE_STATUS" = "ok" ] && [ "$DIAGRAM_STATUS" = "ok" ]; then
    ALL_HEALTHY=true
    echo "All services are healthy!"
  else
    echo "Waiting for services to become healthy..."
    sleep 5
    RETRY_COUNT=$((RETRY_COUNT+1))
  fi
done

if [ "$ALL_HEALTHY" != "true" ]; then
  echo "Warning: Not all services are healthy after 60 seconds. Proceeding anyway..."
fi

# Verify that the repository is accessible inside the container
echo "Verifying repository is accessible inside the container..."
REPO_CHECK=$(docker exec microservices-repo-mapper-service-1 ls -la /repo || echo "Failed to access /repo")
echo "Repository contents in container:"
echo "$REPO_CHECK"

if [[ "$REPO_CHECK" == *"Failed to access /repo"* ]]; then
  echo "Error: Repository is not accessible inside the container!"
  echo "Check that the volume mount is correct in docker-compose.override.yml"
  exit 1
fi

# Run the test with docker mode enabled
echo "Running e2e test..."
python test_e2e_pipeline.py \
  --repo-dir /repo \
  --api-key $OPENAI_API_KEY \
  --results-dir tests/e2e/results/e2e_pipeline \
  --docker-mode

# Clean up the override file
rm docker-compose.override.yml
