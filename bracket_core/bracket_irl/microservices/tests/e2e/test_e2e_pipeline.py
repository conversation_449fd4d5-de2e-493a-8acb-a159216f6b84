#!/usr/bin/env python3
"""
End-to-end test for the Bracket IRL microservices pipeline.
This script tests the entire pipeline from repository mapping to diagram generation.
"""

import os
import sys
import json
import time
import argparse
import requests
import subprocess
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("e2e_test.log")
    ]
)
logger = logging.getLogger("e2e_test")

class BracketIRLPipelineTest:
    """Test the Bracket IRL pipeline end-to-end."""

    def __init__(
        self,
        repo_dir: str,
        orchestrator_url: str = "http://localhost:8000",
        repo_mapper_url: str = "http://localhost:8001",
        domain_analyzer_url: str = "http://localhost:8002",
        file_domain_mapper_url: str = "http://localhost:8003",
        domain_file_repomap_url: str = "http://localhost:8004",
        diagram_generator_url: str = "http://localhost:8005",
        api_key: Optional[str] = None,
        timeout: int = 600,
        poll_interval: int = 5,
        results_dir: str = "results",
        docker_mode: bool = False
    ):
        """
        Initialize the test.

        Args:
            repo_dir: Path to the repository to analyze
            orchestrator_url: URL of the orchestrator service
            repo_mapper_url: URL of the repository mapper service
            domain_analyzer_url: URL of the domain analyzer service
            file_domain_mapper_url: URL of the file-domain mapper service
            domain_file_repomap_url: URL of the domain-file repomap service
            diagram_generator_url: URL of the diagram generator service
            api_key: API key for the services
            timeout: Timeout in seconds for waiting for job completion
            poll_interval: Poll interval in seconds for checking job status
            results_dir: Directory to save results
            docker_mode: Whether running in Docker mode (uses /repo path)
        """
        # In Docker mode, use /repo as the repository directory
        # This is the path inside the container where the repository is mounted
        if docker_mode and repo_dir != "/repo":
            logger.info(f"Docker mode enabled. Using /repo instead of {repo_dir}")
            self.repo_dir = "/repo"
        else:
            self.repo_dir = os.path.abspath(repo_dir)

            # Verify that the repository directory exists
            if not os.path.exists(self.repo_dir):
                logger.error(f"Repository directory does not exist: {self.repo_dir}")
                logger.info("Available directories:")
                parent_dir = os.path.dirname(self.repo_dir)
                if os.path.exists(parent_dir):
                    logger.info(f"Contents of {parent_dir}:")
                    logger.info(str(os.listdir(parent_dir)))

        self.orchestrator_url = orchestrator_url
        self.repo_mapper_url = repo_mapper_url
        self.domain_analyzer_url = domain_analyzer_url
        self.file_domain_mapper_url = file_domain_mapper_url
        self.domain_file_repomap_url = domain_file_repomap_url
        self.diagram_generator_url = diagram_generator_url
        self.api_key = api_key
        self.timeout = timeout
        self.poll_interval = poll_interval
        self.results_dir = results_dir

        # Create results directory
        os.makedirs(results_dir, exist_ok=True)

        # Headers for API requests
        self.headers = {}
        if api_key:
            self.headers["Authorization"] = f"Bearer {api_key}"

    def check_services_health(self) -> bool:
        """
        Check if all services are healthy.

        Returns:
            True if all services are healthy, False otherwise
        """
        services = [
            {"name": "Orchestrator", "url": f"{self.orchestrator_url}/health"},
            {"name": "Repository Mapper", "url": f"{self.repo_mapper_url}/health"},
            {"name": "Domain Analyzer", "url": f"{self.domain_analyzer_url}/health"},
            {"name": "File-Domain Mapper", "url": f"{self.file_domain_mapper_url}/health"},
            {"name": "Domain-File Repomap", "url": f"{self.domain_file_repomap_url}/health"},
            {"name": "Diagram Generator", "url": f"{self.diagram_generator_url}/health"}
        ]

        all_healthy = True

        for service in services:
            try:
                response = requests.get(service["url"], headers=self.headers, timeout=10)
                if response.status_code == 200 and response.json().get("status") == "ok":
                    logger.info(f"{service['name']} service is healthy")
                else:
                    logger.error(f"{service['name']} service is not healthy: {response.text}")
                    all_healthy = False
            except Exception as e:
                logger.error(f"Error checking {service['name']} service health: {str(e)}")
                all_healthy = False

        return all_healthy

    def wait_for_job_completion(self, service_url: str, job_id: str, description: str) -> Dict[str, Any]:
        """
        Wait for a job to complete.

        Args:
            service_url: URL of the service
            job_id: Job ID
            description: Description of the job for logging

        Returns:
            Job details

        Raises:
            Exception: If job fails or times out
        """
        logger.info(f"Waiting for {description} job {job_id} to complete...")
        start_time = time.time()

        while True:
            # Check if timeout exceeded
            if time.time() - start_time > self.timeout:
                raise Exception(f"Timeout waiting for {description} job {job_id} to complete")

            # Get job status
            response = requests.get(
                f"{service_url}/api/v1/status/{job_id}",
                headers=self.headers
            )

            if response.status_code != 200:
                logger.error(f"Error getting {description} job status: {response.text}")
                time.sleep(self.poll_interval)
                continue

            job = response.json().get("job", {})
            status = job.get("status")
            progress = job.get("progress", 0)

            logger.info(f"{description} job {job_id} status: {status}, progress: {progress:.2f}")

            if status == "completed":
                logger.info(f"{description} job {job_id} completed")
                return job
            elif status == "failed":
                error = job.get("error", f"{description} job failed")
                logger.error(f"{description} job {job_id} failed: {error}")
                raise Exception(error)

            # Wait before polling again
            time.sleep(self.poll_interval)

    def get_artifact(self, service_url: str, job_id: str, artifact_type: Optional[str] = None) -> Dict[str, Any]:
        """
        Get job artifact.

        Args:
            service_url: URL of the service
            job_id: Job ID
            artifact_type: Artifact type

        Returns:
            Artifact details and content
        """
        url = f"{service_url}/api/v1/artifacts/{job_id}"
        if artifact_type:
            url += f"?artifact_type={artifact_type}&include_content=true"
        else:
            url += "?include_content=true"

        response = requests.get(url, headers=self.headers)

        if response.status_code != 200:
            logger.error(f"Error getting artifact: {response.text}")
            raise Exception(f"Error getting artifact: {response.text}")

        return response.json()

    def save_artifact(self, artifact: Dict[str, Any], filename: str) -> str:
        """
        Save artifact content to file.

        Args:
            artifact: Artifact details and content
            filename: Filename to save to

        Returns:
            Path to saved file
        """
        filepath = os.path.join(self.results_dir, filename)

        with open(filepath, "w") as f:
            json.dump(artifact.get("content", {}), f, indent=2)

        logger.info(f"Saved artifact to {filepath}")
        return filepath

    def test_repository_mapper(self) -> str:
        """
        Test the repository mapper service.

        Returns:
            Job ID
        """
        logger.info("Testing Repository Mapper service...")

        # Create repository map request
        request_data = {
            "repo_dir": self.repo_dir,
            "batch_size": 1000,
            "top_percentage": 0.2,
            "min_functions": 1,
            "max_functions": 5,
            "exclude_tests": True,
            "output_format": "json",
            "include_extensions": [".py"]
        }

        # Send request
        response = requests.post(
            f"{self.repo_mapper_url}/api/v1/generate",
            json=request_data,
            headers=self.headers
        )

        if response.status_code != 200:
            logger.error(f"Error generating repository map: {response.text}")
            raise Exception(f"Error generating repository map: {response.text}")

        job_id = response.json().get("job_id")
        logger.info(f"Repository map generation started with job ID: {job_id}")

        # Wait for job completion
        self.wait_for_job_completion(self.repo_mapper_url, job_id, "Repository Mapper")

        # Get and save artifacts
        repomap_artifact = self.get_artifact(self.repo_mapper_url, job_id, "repomap")
        self.save_artifact(repomap_artifact, "repomap.json")

        filtered_repomap_artifact = self.get_artifact(self.repo_mapper_url, job_id, "filtered_repomap")
        self.save_artifact(filtered_repomap_artifact, "filtered_repomap.json")

        return job_id

    def test_domain_analyzer(self, repomap_job_id: str) -> str:
        """
        Test the domain analyzer service.

        Args:
            repomap_job_id: Repository mapper job ID

        Returns:
            Job ID
        """
        logger.info("Testing Domain Analyzer service...")

        # Get repository map artifact
        repomap_artifact = self.get_artifact(self.repo_mapper_url, repomap_job_id, "filtered_repomap")

        # Get the filtered repomap content
        filtered_repomap_content = repomap_artifact.get("content", {})

        # Save the filtered repomap to a local file that we can access
        local_repomap_path = os.path.join(self.results_dir, "filtered_repomap.json")
        with open(local_repomap_path, "w") as f:
            json.dump(filtered_repomap_content, f, indent=2)

        logger.info(f"Saved filtered repomap to local path: {local_repomap_path}")

        # Use the local path for the domain analysis request
        # In Docker mode, adjust the path to be relative to the container's root
        if hasattr(self, 'docker_mode') and self.docker_mode:
            # Convert local path to container path
            # From: tests/e2e/results/e2e_pipeline/filtered_repomap.json
            # To: /app/tests/e2e/results/e2e_pipeline/filtered_repomap.json
            # The domain analyzer service uses a LocalStorageClient with base_path="./data"
            # So we need to make the path relative to that
            container_repomap_path = f"/app/tests/e2e/results/e2e_pipeline/filtered_repomap.json"
            logger.info(f"Docker mode: Using container path: {container_repomap_path}")
            repomap_path = container_repomap_path
        else:
            repomap_path = local_repomap_path

        request_data = {
            "repomap_path": repomap_path,
            "api_key": self.api_key,
            "model": "gpt-4o-mini",
            "max_tokens_per_chunk": 8000,
            "max_concurrent_calls": 5
        }

        # Send request
        response = requests.post(
            f"{self.domain_analyzer_url}/api/v1/analyze",
            json=request_data,
            headers=self.headers
        )

        if response.status_code != 200:
            logger.error(f"Error analyzing domains: {response.text}")
            raise Exception(f"Error analyzing domains: {response.text}")

        job_id = response.json().get("job_id")
        logger.info(f"Domain analysis started with job ID: {job_id}")

        # Wait for job completion
        self.wait_for_job_completion(self.domain_analyzer_url, job_id, "Domain Analyzer")

        # Get and save artifacts
        domain_analysis_artifact = self.get_artifact(self.domain_analyzer_url, job_id, "domain_analysis")
        self.save_artifact(domain_analysis_artifact, "domain_analysis.json")

        return job_id

    def run_full_pipeline_test(self) -> Dict[str, str]:
        """
        Run the full pipeline test.

        Returns:
            Dictionary of job IDs for each stage
        """
        logger.info("Starting full pipeline test...")

        # Check services health
        if not self.check_services_health():
            raise Exception("One or more services are not healthy")

        # Test repository mapper only for now
        repomap_job_id = self.test_repository_mapper()

        # Skip domain analyzer and other services for now
        domain_analysis_job_id = self.test_domain_analyzer(repomap_job_id)

        return {
            "repomap_job_id": repomap_job_id,
            "domain_analysis_job_id": domain_analysis_job_id
        }

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Test the Bracket IRL pipeline end-to-end")
    parser.add_argument("--repo-dir", required=True, help="Path to the repository to analyze")
    parser.add_argument("--orchestrator-url", default="http://localhost:8000", help="URL of the orchestrator service")
    parser.add_argument("--repo-mapper-url", default="http://localhost:8001", help="URL of the repository mapper service")
    parser.add_argument("--domain-analyzer-url", default="http://localhost:8002", help="URL of the domain analyzer service")
    parser.add_argument("--file-domain-mapper-url", default="http://localhost:8003", help="URL of the file-domain mapper service")
    parser.add_argument("--domain-file-repomap-url", default="http://localhost:8004", help="URL of the domain-file repomap service")
    parser.add_argument("--diagram-generator-url", default="http://localhost:8005", help="URL of the diagram generator service")
    parser.add_argument("--api-key", help="API key for the services")
    parser.add_argument("--timeout", type=int, default=600, help="Timeout in seconds for waiting for job completion")
    parser.add_argument("--poll-interval", type=int, default=5, help="Poll interval in seconds for checking job status")
    parser.add_argument("--results-dir", default="results", help="Directory to save results")
    parser.add_argument("--docker-mode", action="store_true", help="Enable Docker mode (use /repo path)")
    args = parser.parse_args()

    # Create test instance
    test = BracketIRLPipelineTest(
        repo_dir=args.repo_dir,
        orchestrator_url=args.orchestrator_url,
        repo_mapper_url=args.repo_mapper_url,
        domain_analyzer_url=args.domain_analyzer_url,
        file_domain_mapper_url=args.file_domain_mapper_url,
        domain_file_repomap_url=args.domain_file_repomap_url,
        diagram_generator_url=args.diagram_generator_url,
        api_key=args.api_key,
        timeout=args.timeout,
        poll_interval=args.poll_interval,
        results_dir=args.results_dir,
        docker_mode=args.docker_mode
    )

    try:
        # Run full pipeline test
        job_ids = test.run_full_pipeline_test()
        logger.info(f"Full pipeline test completed successfully: {job_ids}")
        return 0
    except Exception as e:
        logger.error(f"Full pipeline test failed: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())


# python tests/e2e/test_e2e_pipeline.py \
#   --repo-dir tests/e2e/results/test_repo \
#   --api-key $OPENAI_API_KEY \
#   --results-dir tests/e2e/results/e2e_pipeline