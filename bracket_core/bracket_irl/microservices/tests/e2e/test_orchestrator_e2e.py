#!/usr/bin/env python3
"""
End-to-end test for the Bracket IRL pipeline using the orchestrator service.
This script tests the entire pipeline from repository mapping to diagram generation
by using the orchestrator service as the single entry point.
"""

import os
import sys
import json
import time
import argparse
import requests
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("orchestrator_e2e_test.log")
    ]
)
logger = logging.getLogger("orchestrator_e2e_test")

class BracketIRLOrchestratorTest:
    """Test the Bracket IRL pipeline end-to-end using the orchestrator service."""

    def __init__(
        self,
        repo_dir: str,
        orchestrator_url: str = "http://localhost:8000",
        api_key: Optional[str] = None,
        timeout: int = 1800,  # 30 minutes
        poll_interval: int = 10,
        results_dir: str = "results/orchestrator_e2e",
        docker_mode: bool = False
    ):
        """
        Initialize the test.

        Args:
            repo_dir: Path to the repository to analyze
            orchestrator_url: URL of the orchestrator service
            api_key: API key for the services
            timeout: Timeout in seconds for waiting for job completion
            poll_interval: Poll interval in seconds for checking job status
            results_dir: Directory to save results
            docker_mode: Whether running in Docker mode (uses /repo path)
        """
        # In Docker mode, use /repo as the repository directory
        if docker_mode and repo_dir != "/repo":
            logger.info(f"Docker mode enabled. Using /repo instead of {repo_dir}")
            self.repo_dir = "/repo"
        else:
            self.repo_dir = os.path.abspath(repo_dir)

        # Verify that the repository directory exists
        if not os.path.exists(self.repo_dir):
            logger.error(f"Repository directory does not exist: {self.repo_dir}")
            logger.info("Available directories:")

            # List root directory contents to help debug
            if self.repo_dir == "/repo":
                logger.info("Contents of /:")
                logger.info(str(os.listdir("/")))
            else:
                parent_dir = os.path.dirname(self.repo_dir)
                if os.path.exists(parent_dir):
                    logger.info(f"Contents of {parent_dir}:")
                    logger.info(str(os.listdir(parent_dir)))

        self.orchestrator_url = orchestrator_url
        self.api_key = api_key
        self.timeout = timeout
        self.poll_interval = poll_interval
        self.results_dir = results_dir
        self.docker_mode = docker_mode

        # Create results directory
        os.makedirs(results_dir, exist_ok=True)

        # Note: We don't need to create the artifacts directory here
        # It should already be created in the Docker container during build
        # The error was trying to create it from the host machine

        # Headers for API requests
        self.headers = {}
        if api_key:
            self.headers["Authorization"] = f"Bearer {api_key}"

    def check_services_health(self) -> bool:
        """
        Check if all services are healthy using the orchestrator's health check.

        Returns:
            True if all services are healthy, False otherwise
        """
        try:
            # Check orchestrator health
            response = requests.get(f"{self.orchestrator_url}/health", headers=self.headers, timeout=10)
            if response.status_code != 200 or response.json().get("status") != "ok":
                logger.error(f"Orchestrator service is not healthy: {response.text}")
                return False

            logger.info("Orchestrator service is healthy")

            # Check detailed health of all services through the orchestrator
            response = requests.get(f"{self.orchestrator_url}/health/details", headers=self.headers, timeout=10)
            if response.status_code != 200:
                logger.error(f"Failed to get detailed health status: {response.text}")
                return False

            # Successfully got health details

            # Check pipeline status to verify all services
            response = requests.get(f"{self.orchestrator_url}/api/v1/status", headers=self.headers, timeout=10)
            if response.status_code != 200:
                logger.error(f"Failed to get pipeline status: {response.text}")
                return False

            pipeline_status = response.json()
            services = pipeline_status.get("services", [])

            all_healthy = True
            for service in services:
                service_name = service.get("service_name")
                status = service.get("status")

                if status != "ok":
                    logger.error(f"{service_name} service is not healthy: {status}")
                    all_healthy = False
                else:
                    logger.info(f"{service_name} service is healthy")

            return all_healthy
        except Exception as e:
            logger.error(f"Error checking services health: {str(e)}")
            return False

    def start_pipeline(self) -> str:
        """
        Start the pipeline using the orchestrator service.

        Returns:
            Job ID for the pipeline
        """
        logger.info("Starting pipeline through orchestrator...")

        # Define artifact paths that will be used between pipeline steps
        # We'll use dummy paths that will be replaced by the orchestrator service
        # with the actual paths from the services
        # Use relative paths instead of absolute paths to avoid path resolution issues
        artifacts_dir = "artifacts"
        dummy_job_id = "dummy"
        repomap_path = f"{artifacts_dir}/{dummy_job_id}/repomap.json"
        domain_analysis_path = f"{artifacts_dir}/{dummy_job_id}/domain_analysis.json"
        domain_mapping_path = f"{artifacts_dir}/{dummy_job_id}/domain_mapping.json"
        domain_file_repomap_path = f"{artifacts_dir}/{dummy_job_id}/domain_file_repomap.json"

        # Create orchestration request with all required fields
        request_data = {
            "repo_dir": self.repo_dir,  # Add top-level repo_dir field
            "repomap_config": {
                "repo_dir": self.repo_dir,  # Include repo_dir inside repomap_config
                "batch_size": 1000,
                "top_percentage": 0.2,
                "min_functions": 1,
                "max_functions": 5,
                "exclude_tests": True,
                "output_format": "json",
                "include_extensions": [".py"]
            },
            "domain_analysis_config": {
                "repomap_path": repomap_path,  # Add path to repomap artifact
                "model": "gpt-4o-mini",
                "max_tokens_per_chunk": 8000,
                "max_concurrent_tasks": 5,
                "api_key": self.api_key  # Pass the API key
            },
            "file_domain_mapper_config": {
                "repomap_path": repomap_path,  # Add path to repomap artifact
                "domain_analysis_path": domain_analysis_path,  # Add path to domain analysis artifact
                "model": "gpt-4o-mini",
                "max_files_per_batch": 50,
                "api_key": self.api_key  # Pass the API key
            },
            "domain_file_repomap_config": {
                "repomap_path": repomap_path,  # Add path to repomap artifact
                "domain_mapping_path": domain_mapping_path  # Add path to domain mapping artifact
            },
            "diagram_generator_config": {
                "domain_file_repomap_path": domain_file_repomap_path,  # Add path to domain-file repomap artifact
                "model_type": "openai",
                "openai_model": "gpt-4o-mini",
                "api_key": self.api_key  # Pass the API key
            }
        }

        # Send request to orchestrator
        try:
            response = requests.post(
                f"{self.orchestrator_url}/api/v1/repositories",
                json=request_data,
                headers=self.headers
            )

            if response.status_code != 200:
                logger.error(f"Failed to start pipeline: {response.text}")
                raise Exception(f"Failed to start pipeline: {response.text}")

            job_id = response.json().get("job_id")
            logger.info(f"Pipeline started with job ID: {job_id}")

            return job_id
        except Exception as e:
            logger.error(f"Error starting pipeline: {str(e)}")
            raise

    def monitor_pipeline(self, job_id: str) -> Dict[str, Any]:
        """
        Monitor the pipeline progress.

        Args:
            job_id: Job ID to monitor

        Returns:
            Final job details
        """
        logger.info(f"Monitoring pipeline job {job_id}...")
        start_time = time.time()
        last_progress = -1
        last_message = ""

        while True:
            # Check if timeout exceeded
            if time.time() - start_time > self.timeout:
                raise Exception(f"Timeout waiting for pipeline job {job_id} to complete")

            # Get job status
            try:
                response = requests.get(
                    f"{self.orchestrator_url}/api/v1/jobs/{job_id}",
                    headers=self.headers
                )

                if response.status_code != 200:
                    logger.error(f"Error getting job status: {response.text}")
                    time.sleep(self.poll_interval)
                    continue

                job = response.json().get("job", {})
                status = job.get("status")
                progress = job.get("progress", 0)
                message = job.get("message", "")

                # Log progress only if it has changed
                if progress != last_progress or message != last_message:
                    logger.info(f"Pipeline job {job_id} status: {status}, progress: {progress:.2f}, message: {message}")
                    last_progress = progress
                    last_message = message

                if status == "completed":
                    logger.info(f"Pipeline job {job_id} completed successfully")
                    return job
                elif status == "failed":
                    error = job.get("error", "Pipeline job failed")
                    logger.error(f"Pipeline job {job_id} failed: {error}")
                    raise Exception(error)

                # Wait before polling again
                time.sleep(self.poll_interval)
            except requests.RequestException as e:
                logger.error(f"Error communicating with orchestrator: {str(e)}")
                time.sleep(self.poll_interval)

    def get_artifacts(self, job_id: str) -> Dict[str, Any]:
        """
        Get all artifacts from the pipeline.

        Args:
            job_id: Job ID to get artifacts for

        Returns:
            Dictionary of artifacts by type
        """
        logger.info(f"Retrieving artifacts for job {job_id}...")

        # Get job details to find all artifact IDs
        response = requests.get(
            f"{self.orchestrator_url}/api/v1/jobs/{job_id}",
            headers=self.headers
        )

        if response.status_code != 200:
            logger.error(f"Error getting job details: {response.text}")
            raise Exception(f"Error getting job details: {response.text}")

        job = response.json().get("job", {})
        result = job.get("result", {})

        logger.info(f"Job result: {result}")

        # Get artifacts for each step
        artifacts = {}

        # Define the artifact types to retrieve for each job
        artifact_mapping = {
            "repomap_job_id": ["repomap", "filtered_repomap"],
            "domain_analysis_job_id": ["domain_analysis"],
            "domain_mapping_job_id": ["domain_mapping"],
            "domain_file_repomap_job_id": ["domain_file_repomap"],
            "diagram_job_id": ["diagram"]
        }

        # First try to get artifacts directly from the orchestrator job
        # This is the case when we're using the same job ID for all services
        for artifact_type in ["repomap", "filtered_repomap", "domain_analysis", "domain_mapping", "domain_file_repomap", "diagram"]:
            try:
                # Try to get the artifact directly from the orchestrator job
                response = requests.get(
                    f"{self.orchestrator_url}/api/v1/artifacts/{job_id}/{artifact_type}",
                    params={"include_content": True},
                    headers=self.headers
                )

                if response.status_code == 200:
                    artifact_data = response.json()
                    artifacts[artifact_type] = artifact_data

                    # Save artifact to file
                    filepath = os.path.join(self.results_dir, f"{artifact_type}.json")
                    with open(filepath, "w") as f:
                        json.dump(artifact_data.get("content", {}), f, indent=2)

                    logger.info(f"Saved {artifact_type} artifact to {filepath}")
            except Exception as e:
                logger.warning(f"Error retrieving {artifact_type} artifact from orchestrator job: {str(e)}")

        # If we didn't find all artifacts, try to get them from the individual service jobs
        for job_key, artifact_types in artifact_mapping.items():
            step_job_id = result.get(job_key)
            if not step_job_id:
                logger.warning(f"No job ID found for {job_key}")
                continue

            logger.info(f"Retrieving artifacts for {job_key}: {step_job_id}")

            for artifact_type in artifact_types:
                # Skip if we already have this artifact
                if artifact_type in artifacts:
                    continue

                try:
                    # Get the artifact from the specific service job, not the orchestrator job
                    response = requests.get(
                        f"{self.orchestrator_url}/api/v1/artifacts/{step_job_id}/{artifact_type}",
                        params={"include_content": True},
                        headers=self.headers
                    )

                    if response.status_code != 200:
                        logger.warning(f"Error getting {artifact_type} artifact: {response.text}")
                        continue

                    artifact_data = response.json()
                    artifacts[artifact_type] = artifact_data

                    # Save artifact to file
                    filepath = os.path.join(self.results_dir, f"{artifact_type}.json")
                    with open(filepath, "w") as f:
                        json.dump(artifact_data.get("content", {}), f, indent=2)

                    logger.info(f"Saved {artifact_type} artifact to {filepath}")
                except Exception as e:
                    logger.error(f"Error retrieving {artifact_type} artifact: {str(e)}")

        return artifacts

    def validate_artifacts(self, artifacts: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate the artifacts from the pipeline.

        Args:
            artifacts: Dictionary of artifacts by type

        Returns:
            Tuple of (success, list of validation messages)
        """
        logger.info("Validating artifacts...")
        validation_messages = []
        success = True

        # Check if we have all expected artifact types
        expected_artifacts = ["repomap", "filtered_repomap", "domain_analysis",
                             "domain_mapping", "domain_file_repomap", "diagram"]

        for artifact_type in expected_artifacts:
            if artifact_type not in artifacts:
                message = f"Missing {artifact_type} artifact"
                validation_messages.append(message)
                logger.warning(message)
                success = False

        # Validate repository map
        if "repomap" in artifacts:
            repomap = artifacts["repomap"].get("content", {})
            if not repomap:
                message = "Repository map is empty"
                validation_messages.append(message)
                logger.warning(message)
                success = False
            else:
                message = f"Repository map contains data for {len(repomap)} files"
                validation_messages.append(message)
                logger.info(message)

        # Validate domain analysis
        if "domain_analysis" in artifacts:
            domain_analysis = artifacts["domain_analysis"].get("content", {})
            domains = domain_analysis.get("domains", [])
            if not domains:
                message = "Domain analysis contains no domains"
                validation_messages.append(message)
                logger.warning(message)
                success = False
            else:
                message = f"Domain analysis contains {len(domains)} domains"
                validation_messages.append(message)
                logger.info(message)

        # Validate domain mapping
        if "domain_mapping" in artifacts:
            domain_mapping = artifacts["domain_mapping"].get("content", {})
            if not domain_mapping:
                message = "Domain mapping is empty"
                validation_messages.append(message)
                logger.warning(message)
                success = False
            else:
                message = f"Domain mapping contains mappings for {len(domain_mapping)} domains"
                validation_messages.append(message)
                logger.info(message)

        # Validate domain-file repomap
        if "domain_file_repomap" in artifacts:
            domain_file_repomap = artifacts["domain_file_repomap"].get("content", {})
            if not domain_file_repomap:
                message = "Domain-file repomap is empty"
                validation_messages.append(message)
                logger.warning(message)
                success = False
            else:
                message = f"Domain-file repomap contains data"
                validation_messages.append(message)
                logger.info(message)

        # Validate diagrams
        if "diagram" in artifacts:
            diagrams = artifacts["diagram"].get("content", {})
            if not diagrams:
                message = "No diagrams generated"
                validation_messages.append(message)
                logger.warning(message)
                success = False
            else:
                message = f"Diagrams generated successfully"
                validation_messages.append(message)
                logger.info(message)

        return success, validation_messages

    def run_test(self) -> Dict[str, Any]:
        """
        Run the full pipeline test.

        Returns:
            Test results
        """
        logger.info("Starting orchestrator-based E2E test...")
        start_time = time.time()

        test_results = {
            "success": False,
            "start_time": start_time,
            "end_time": None,
            "duration": None,
            "job_id": None,
            "artifacts": {},
            "validation": {
                "success": False,
                "messages": []
            }
        }

        try:
            # Check services health
            logger.info("Checking services health...")
            if not self.check_services_health():
                raise Exception("One or more services are not healthy")

            # Start pipeline
            job_id = self.start_pipeline()
            test_results["job_id"] = job_id

            # Monitor pipeline
            job_details = self.monitor_pipeline(job_id)
            test_results["job_details"] = job_details

            # Get artifacts
            artifacts = self.get_artifacts(job_id)
            test_results["artifacts"] = artifacts

            # Validate artifacts
            success, messages = self.validate_artifacts(artifacts)
            test_results["validation"] = {
                "success": success,
                "messages": messages
            }

            test_results["success"] = success

            # Calculate duration
            end_time = time.time()
            test_results["end_time"] = end_time
            test_results["duration"] = end_time - start_time

            logger.info(f"Test completed in {test_results['duration']:.2f} seconds")
            logger.info(f"Test {'succeeded' if success else 'failed'}")

            # Save test results
            results_file = os.path.join(self.results_dir, "test_results.json")
            with open(results_file, "w") as f:
                json.dump(test_results, f, indent=2, default=str)

            logger.info(f"Test results saved to {results_file}")

            return test_results
        except Exception as e:
            logger.error(f"Test failed: {str(e)}")

            # Calculate duration
            end_time = time.time()
            test_results["end_time"] = end_time
            test_results["duration"] = end_time - start_time
            test_results["error"] = str(e)

            # Save test results
            results_file = os.path.join(self.results_dir, "test_results.json")
            with open(results_file, "w") as f:
                json.dump(test_results, f, indent=2, default=str)

            logger.info(f"Test results saved to {results_file}")

            return test_results

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Test the Bracket IRL pipeline end-to-end using the orchestrator")
    parser.add_argument("--repo-dir", required=True, help="Path to the repository to analyze")
    parser.add_argument("--orchestrator-url", default="http://localhost:8000", help="URL of the orchestrator service")
    parser.add_argument("--api-key", help="API key for the services")
    parser.add_argument("--timeout", type=int, default=1800, help="Timeout in seconds for waiting for job completion")
    parser.add_argument("--poll-interval", type=int, default=10, help="Poll interval in seconds for checking job status")
    parser.add_argument("--results-dir", default="results/orchestrator_e2e", help="Directory to save results")
    parser.add_argument("--docker-mode", action="store_true", help="Enable Docker mode (use /repo path)")
    args = parser.parse_args()

    # Create test instance
    test = BracketIRLOrchestratorTest(
        repo_dir=args.repo_dir,
        orchestrator_url=args.orchestrator_url,
        api_key=args.api_key,
        timeout=args.timeout,
        poll_interval=args.poll_interval,
        results_dir=args.results_dir,
        docker_mode=args.docker_mode
    )

    try:
        # Run test
        results = test.run_test()
        return 0 if results["success"] else 1
    except Exception as e:
        logger.error(f"Test failed with exception: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
