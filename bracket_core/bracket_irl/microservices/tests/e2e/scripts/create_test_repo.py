#!/usr/bin/env python3
"""
<PERSON>ript to create a small test repository for E2E testing of Bracket IRL microservices.
"""

import os
import shutil
import argparse
from pathlib import Path

def create_test_repo(output_dir):
    """Create a small test repository with Python files."""
    # Create output directory if it doesn't exist
    output_path = Path(output_dir)
    if output_path.exists():
        shutil.rmtree(output_path)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Create directory structure
    (output_path / "src").mkdir()
    (output_path / "src" / "api").mkdir()
    (output_path / "src" / "core").mkdir()
    (output_path / "src" / "models").mkdir()
    (output_path / "src" / "services").mkdir()
    (output_path / "tests").mkdir()
    (output_path / "docs").mkdir()
    
    # Create main.py
    with open(output_path / "src" / "main.py", "w") as f:
        f.write("""
import os
import sys
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from src.api.routes import router as api_router
from src.core.config import get_config
from src.services.data_service import DataService

# Initialize app
app = FastAPI(
    title="Test API",
    description="Test API for Bracket IRL E2E testing",
    version="0.1.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API router
app.include_router(api_router, prefix="/api/v1")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
""")
    
    # Create config.py
    with open(output_path / "src" / "core" / "config.py", "w") as f:
        f.write("""
import os
from pydantic import BaseSettings

class Settings(BaseSettings):
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Test API"
    
    # Database settings
    DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite:///./test.db")
    
    # API settings
    API_KEY: str = os.getenv("API_KEY", "test-api-key")
    
    class Config:
        env_file = ".env"

def get_config() -> Settings:
    return Settings()
""")
    
    # Create routes.py
    with open(output_path / "src" / "api" / "routes.py", "w") as f:
        f.write("from fastapi import APIRouter, Depends, HTTPException\n")
        f.write("from typing import List, Dict, Any\n\n")
        f.write("from src.models.data_model import DataModel, DataResponse\n")
        f.write("from src.services.data_service import DataService, get_data_service\n\n")
        f.write("router = APIRouter()\n\n")
        f.write('@router.get("/data", response_model=List[DataResponse])\n')
        f.write("async def get_data(\n")
        f.write("    data_service: DataService = Depends(get_data_service)\n")
        f.write("):\n")
        f.write('    """Get all data."""\n')
        f.write("    return data_service.get_all_data()\n\n")
        f.write('@router.post("/data", response_model=DataResponse)\n')
        f.write("async def create_data(\n")
        f.write("    data: DataModel,\n")
        f.write("    data_service: DataService = Depends(get_data_service)\n")
        f.write("):\n")
        f.write('    """Create new data."""\n')
        f.write("    return data_service.create_data(data)\n\n")
        f.write('@router.get("/data/{data_id}", response_model=DataResponse)\n')
        f.write("async def get_data_by_id(\n")
        f.write("    data_id: str,\n")
        f.write("    data_service: DataService = Depends(get_data_service)\n")
        f.write("):\n")
        f.write('    """Get data by ID."""\n')
        f.write("    data = data_service.get_data_by_id(data_id)\n")
        f.write("    if not data:\n")
        f.write('        raise HTTPException(status_code=404, detail="Data not found")\n')
        f.write("    return data\n")
    
    # Create __init__.py files
    open(output_path / "src" / "__init__.py", "w").close()
    open(output_path / "src" / "api" / "__init__.py", "w").close()
    open(output_path / "src" / "core" / "__init__.py", "w").close()
    open(output_path / "src" / "models" / "__init__.py", "w").close()
    open(output_path / "src" / "services" / "__init__.py", "w").close()
    
    # Create data_model.py
    with open(output_path / "src" / "models" / "data_model.py", "w") as f:
        f.write("""
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime
from uuid import UUID, uuid4

class DataModel(BaseModel):
    name: str
    description: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    
    class Config:
        schema_extra = {
            "example": {
                "name": "Test Data",
                "description": "This is test data",
                "metadata": {"key": "value"}
            }
        }

class DataResponse(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    created_at: datetime
    
    class Config:
        schema_extra = {
            "example": {
                "id": "123e4567-e89b-12d3-a456-************",
                "name": "Test Data",
                "description": "This is test data",
                "metadata": {"key": "value"},
                "created_at": "2023-01-01T00:00:00"
            }
        }
""")
    
    # Create data_service.py
    with open(output_path / "src" / "services" / "data_service.py", "w") as f:
        f.write("import uuid\n")
        f.write("from datetime import datetime\n")
        f.write("from typing import List, Dict, Any, Optional\n\n")
        f.write("from src.models.data_model import DataModel, DataResponse\n\n")
        f.write("class DataService:\n")
        f.write("    def __init__(self):\n")
        f.write("        self.data = {}\n")
        f.write("    \n")
        f.write("    def get_all_data(self) -> List[DataResponse]:\n")
        f.write('        """Get all data."""\n')
        f.write("        return list(self.data.values())\n")
        f.write("    \n")
        f.write("    def get_data_by_id(self, data_id: str) -> Optional[DataResponse]:\n")
        f.write('        """Get data by ID."""\n')
        f.write("        return self.data.get(data_id)\n")
        f.write("    \n")
        f.write("    def create_data(self, data: DataModel) -> DataResponse:\n")
        f.write('        """Create new data."""\n')
        f.write("        data_id = str(uuid.uuid4())\n")
        f.write("        data_response = DataResponse(\n")
        f.write("            id=data_id,\n")
        f.write("            name=data.name,\n")
        f.write("            description=data.description,\n")
        f.write("            metadata=data.metadata,\n")
        f.write("            created_at=datetime.now()\n")
        f.write("        )\n")
        f.write("        self.data[data_id] = data_response\n")
        f.write("        return data_response\n\n")
        f.write("def get_data_service() -> DataService:\n")
        f.write('    """Get data service instance."""\n')
        f.write("    return DataService()\n")
    
    # Create requirements.txt
    with open(output_path / "requirements.txt", "w") as f:
        f.write("""
fastapi==0.95.0
uvicorn==0.21.1
pydantic==1.10.7
python-dotenv==1.0.0
""")
    
    # Create README.md
    with open(output_path / "README.md", "w") as f:
        f.write("""
# Test Repository

This is a small test repository for E2E testing of Bracket IRL microservices.

## Structure

- `src/`: Source code
  - `api/`: API routes
  - `core/`: Core functionality
  - `models/`: Data models
  - `services/`: Business logic
- `tests/`: Tests
- `docs/`: Documentation

## Running

```bash
pip install -r requirements.txt
python -m src.main
```
""")
    
    print(f"Test repository created at {output_path}")
    return str(output_path)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Create a test repository for E2E testing")
    parser.add_argument("--output", default="test_repo", help="Output directory")
    args = parser.parse_args()
    
    create_test_repo(args.output)
