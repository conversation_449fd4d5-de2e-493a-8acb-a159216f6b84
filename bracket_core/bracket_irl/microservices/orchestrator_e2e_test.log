2025-05-12 00:05:37,057 - orchestrator_e2e_test - ERROR - Repository directory does not exist: /repo
2025-05-12 00:05:37,057 - orchestrator_e2e_test - INFO - Available directories:
2025-05-12 00:05:37,057 - orchestrator_e2e_test - INFO - Contents of /:
2025-05-12 00:05:37,057 - orchestrator_e2e_test - INFO - ['home', 'usr', '.resolve', 'bin', 'sbin', '.file', 'etc', 'var', 'Library', 'System', '.VolumeIcon.icns', 'private', '.vol', 'Users', 'Applications', 'opt', 'dev', 'Volumes', '.nofollow', 'tmp', 'cores']
2025-05-12 00:05:37,057 - orchestrator_e2e_test - INFO - Starting orchestrator-based E2E test...
2025-05-12 00:05:37,057 - orchestrator_e2e_test - INFO - Checking services health...
2025-05-12 00:05:37,064 - orchestrator_e2e_test - INFO - Orchestrator service is healthy
2025-05-12 00:05:37,230 - orchestrator_e2e_test - INFO - repository-mapper-service service is healthy
2025-05-12 00:05:37,230 - orchestrator_e2e_test - INFO - domain-analyzer-service service is healthy
2025-05-12 00:05:37,230 - orchestrator_e2e_test - INFO - file-domain-mapper-service service is healthy
2025-05-12 00:05:37,230 - orchestrator_e2e_test - INFO - domain-file-repomap-service service is healthy
2025-05-12 00:05:37,230 - orchestrator_e2e_test - INFO - diagram-generator-service service is healthy
2025-05-12 00:05:37,230 - orchestrator_e2e_test - INFO - Starting pipeline through orchestrator...
2025-05-12 00:05:37,234 - orchestrator_e2e_test - ERROR - Failed to start pipeline: {"detail":[{"type":"missing","loc":["body","repomap_config","repo_dir"],"msg":"Field required","input":{"batch_size":1000,"top_percentage":0.2,"min_functions":1,"max_functions":5,"exclude_tests":true,"output_format":"json","include_extensions":[".py"]}},{"type":"missing","loc":["body","domain_analysis_config","repomap_path"],"msg":"Field required","input":{"model":"gpt-4o-mini","max_tokens_per_chunk":8000,"max_concurrent_tasks":5}},{"type":"missing","loc":["body","file_domain_mapper_config","repomap_path"],"msg":"Field required","input":{"model":"gpt-4o-mini","max_files_per_batch":50}},{"type":"missing","loc":["body","file_domain_mapper_config","domain_analysis_path"],"msg":"Field required","input":{"model":"gpt-4o-mini","max_files_per_batch":50}},{"type":"missing","loc":["body","domain_file_repomap_config","domain_mapping_path"],"msg":"Field required","input":{}},{"type":"missing","loc":["body","domain_file_repomap_config","repomap_path"],"msg":"Field required","input":{}},{"type":"missing","loc":["body","diagram_generator_config","domain_file_repomap_path"],"msg":"Field required","input":{"model_type":"openai","openai_model":"gpt-4o-mini"}}]}
2025-05-12 00:05:37,234 - orchestrator_e2e_test - ERROR - Error starting pipeline: Failed to start pipeline: {"detail":[{"type":"missing","loc":["body","repomap_config","repo_dir"],"msg":"Field required","input":{"batch_size":1000,"top_percentage":0.2,"min_functions":1,"max_functions":5,"exclude_tests":true,"output_format":"json","include_extensions":[".py"]}},{"type":"missing","loc":["body","domain_analysis_config","repomap_path"],"msg":"Field required","input":{"model":"gpt-4o-mini","max_tokens_per_chunk":8000,"max_concurrent_tasks":5}},{"type":"missing","loc":["body","file_domain_mapper_config","repomap_path"],"msg":"Field required","input":{"model":"gpt-4o-mini","max_files_per_batch":50}},{"type":"missing","loc":["body","file_domain_mapper_config","domain_analysis_path"],"msg":"Field required","input":{"model":"gpt-4o-mini","max_files_per_batch":50}},{"type":"missing","loc":["body","domain_file_repomap_config","domain_mapping_path"],"msg":"Field required","input":{}},{"type":"missing","loc":["body","domain_file_repomap_config","repomap_path"],"msg":"Field required","input":{}},{"type":"missing","loc":["body","diagram_generator_config","domain_file_repomap_path"],"msg":"Field required","input":{"model_type":"openai","openai_model":"gpt-4o-mini"}}]}
2025-05-12 00:05:37,235 - orchestrator_e2e_test - ERROR - Test failed: Failed to start pipeline: {"detail":[{"type":"missing","loc":["body","repomap_config","repo_dir"],"msg":"Field required","input":{"batch_size":1000,"top_percentage":0.2,"min_functions":1,"max_functions":5,"exclude_tests":true,"output_format":"json","include_extensions":[".py"]}},{"type":"missing","loc":["body","domain_analysis_config","repomap_path"],"msg":"Field required","input":{"model":"gpt-4o-mini","max_tokens_per_chunk":8000,"max_concurrent_tasks":5}},{"type":"missing","loc":["body","file_domain_mapper_config","repomap_path"],"msg":"Field required","input":{"model":"gpt-4o-mini","max_files_per_batch":50}},{"type":"missing","loc":["body","file_domain_mapper_config","domain_analysis_path"],"msg":"Field required","input":{"model":"gpt-4o-mini","max_files_per_batch":50}},{"type":"missing","loc":["body","domain_file_repomap_config","domain_mapping_path"],"msg":"Field required","input":{}},{"type":"missing","loc":["body","domain_file_repomap_config","repomap_path"],"msg":"Field required","input":{}},{"type":"missing","loc":["body","diagram_generator_config","domain_file_repomap_path"],"msg":"Field required","input":{"model_type":"openai","openai_model":"gpt-4o-mini"}}]}
2025-05-12 00:05:37,235 - orchestrator_e2e_test - INFO - Test results saved to tests/e2e/results/orchestrator_e2e/test_results.json
2025-05-12 00:13:27,647 - orchestrator_e2e_test - ERROR - Repository directory does not exist: /repo
2025-05-12 00:13:27,647 - orchestrator_e2e_test - INFO - Available directories:
2025-05-12 00:13:27,647 - orchestrator_e2e_test - INFO - Contents of /:
2025-05-12 00:13:27,647 - orchestrator_e2e_test - INFO - ['home', 'usr', '.resolve', 'bin', 'sbin', '.file', 'etc', 'var', 'Library', 'System', '.VolumeIcon.icns', 'private', '.vol', 'Users', 'Applications', 'opt', 'dev', 'Volumes', '.nofollow', 'tmp', 'cores']
