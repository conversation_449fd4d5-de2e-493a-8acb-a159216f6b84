2025-05-11 12:05:06,398 - docker_compose_test - INFO - Starting Docker Compose test...
2025-05-11 12:05:06,399 - docker_compose_test - INFO - Starting services using Docker Compose...
2025-05-11 12:05:31,025 - docker_compose_test - INFO - Services started successfully
2025-05-11 12:05:31,026 - docker_compose_test - INFO - Waiting for services to be healthy...
2025-05-11 12:05:31,026 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:05:31,373 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=
2025-05-11 12:05:31,373 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=running, Health=starting
2025-05-11 12:05:31,373 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=running, Health=
2025-05-11 12:05:31,373 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=running, Health=
2025-05-11 12:05:31,373 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:05:31,373 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 12:05:31,373 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:05:31,373 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 12:05:31,374 - docker_compose_test - INFO - Checking services health...
2025-05-11 12:05:31,388 - docker_compose_test - ERROR - Error checking Orchestrator service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 12:05:31,390 - docker_compose_test - ERROR - Error checking Repository Mapper service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 12:05:31,391 - docker_compose_test - ERROR - Error checking Domain Analyzer service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 12:05:31,393 - docker_compose_test - ERROR - Error checking File-Domain Mapper service health: HTTPConnectionPool(host='localhost', port=8003): Max retries exceeded with url: /health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x1050c1e10>: Failed to establish a new connection: [Errno 61] Connection refused'))
2025-05-11 12:05:31,395 - docker_compose_test - ERROR - Error checking Domain-File Repomap service health: HTTPConnectionPool(host='localhost', port=8004): Max retries exceeded with url: /health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x1050b1110>: Failed to establish a new connection: [Errno 61] Connection refused'))
2025-05-11 12:05:31,396 - docker_compose_test - ERROR - Error checking Diagram Generator service health: HTTPConnectionPool(host='localhost', port=8005): Max retries exceeded with url: /health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x1050b1850>: Failed to establish a new connection: [Errno 61] Connection refused'))
2025-05-11 12:05:31,399 - docker_compose_test - INFO - Prometheus service is healthy
2025-05-11 12:05:31,402 - docker_compose_test - ERROR - Error checking Grafana service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 12:05:31,403 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 0s)
2025-05-11 12:05:36,409 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:05:36,751 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=
2025-05-11 12:05:36,751 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:05:36,751 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=running, Health=
2025-05-11 12:05:36,751 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=running, Health=starting
2025-05-11 12:05:36,751 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:05:36,751 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=starting
2025-05-11 12:05:36,751 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:05:36,751 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=starting
2025-05-11 12:05:36,751 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 6s)
2025-05-11 12:05:41,752 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:05:41,907 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:05:41,908 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:05:41,908 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:05:41,908 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:05:41,908 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:05:41,908 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:05:41,908 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:05:41,908 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:05:41,909 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 11s)
2025-05-11 12:05:46,912 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:05:47,085 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=
2025-05-11 12:05:47,085 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:05:47,085 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=running, Health=
2025-05-11 12:05:47,085 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=running, Health=starting
2025-05-11 12:05:47,085 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:05:47,085 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:05:47,085 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:05:47,085 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:05:47,085 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 16s)
2025-05-11 12:05:52,088 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:05:52,184 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:05:52,184 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:05:52,184 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:05:52,184 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:05:52,184 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:05:52,184 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:05:52,184 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:05:52,184 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:05:52,184 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 21s)
2025-05-11 12:05:57,185 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:05:57,258 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:05:57,259 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:05:57,259 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:05:57,259 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:05:57,259 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:05:57,259 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:05:57,259 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:05:57,259 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:05:57,259 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 26s)
2025-05-11 12:06:02,262 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:06:02,330 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:06:02,330 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:06:02,330 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:06:02,330 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:06:02,330 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:06:02,330 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:06:02,330 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:06:02,330 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:06:02,330 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 31s)
2025-05-11 12:06:07,335 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:06:07,404 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:06:07,404 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:06:07,404 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:06:07,404 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:06:07,404 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:06:07,404 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:06:07,404 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:06:07,404 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:06:07,404 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 36s)
2025-05-11 12:06:12,409 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:06:12,509 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:06:12,510 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:06:12,510 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:06:12,510 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:06:12,510 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:06:12,510 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:06:12,510 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:06:12,510 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:06:12,510 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 41s)
2025-05-11 12:06:17,515 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:06:17,614 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:06:17,614 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:06:17,614 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:06:17,614 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:06:17,614 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:06:17,614 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:06:17,614 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:06:17,614 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:06:17,615 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 47s)
2025-05-11 12:06:22,619 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:06:22,715 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:06:22,715 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:06:22,716 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:06:22,716 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:06:22,716 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:06:22,716 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:06:22,716 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:06:22,716 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:06:22,716 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 52s)
2025-05-11 12:06:27,721 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:06:27,820 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:06:27,820 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:06:27,820 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:06:27,820 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:06:27,820 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:06:27,820 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:06:27,821 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:06:27,821 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:06:27,821 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 57s)
2025-05-11 12:06:32,824 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:06:32,920 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:06:32,920 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:06:32,920 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:06:32,921 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:06:32,921 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:06:32,921 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:06:32,921 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:06:32,921 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:06:32,921 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 62s)
2025-05-11 12:06:37,922 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:06:38,019 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:06:38,019 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:06:38,019 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:06:38,019 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:06:38,019 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:06:38,019 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:06:38,019 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:06:38,019 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:06:38,019 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 67s)
2025-05-11 12:06:43,023 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:06:43,112 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:06:43,112 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:06:43,112 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:06:43,112 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:06:43,112 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:06:43,112 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:06:43,112 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:06:43,112 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:06:43,112 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 72s)
2025-05-11 12:06:48,117 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:06:48,216 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:06:48,216 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:06:48,216 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:06:48,216 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:06:48,216 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:06:48,216 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:06:48,216 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:06:48,216 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:06:48,217 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 77s)
2025-05-11 12:06:53,218 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:06:53,315 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:06:53,315 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:06:53,315 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:06:53,315 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:06:53,315 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:06:53,316 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:06:53,316 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:06:53,316 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:06:53,316 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 82s)
2025-05-11 12:06:58,319 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:06:58,416 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:06:58,416 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:06:58,416 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:06:58,416 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:06:58,416 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:06:58,416 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:06:58,416 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:06:58,416 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:06:58,416 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 87s)
2025-05-11 12:07:03,418 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:07:03,516 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:07:03,520 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:07:03,520 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:07:03,522 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:07:03,522 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:07:03,522 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:07:03,522 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:07:03,522 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:07:03,522 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 92s)
2025-05-11 12:07:08,527 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:07:08,624 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:07:08,624 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:07:08,624 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:07:08,624 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:07:08,624 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:07:08,624 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:07:08,624 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:07:08,624 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:07:08,624 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 98s)
2025-05-11 12:07:13,629 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:07:13,725 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:07:13,725 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:07:13,725 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:07:13,725 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:07:13,725 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:07:13,725 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:07:13,725 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:07:13,725 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:07:13,725 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 103s)
2025-05-11 12:07:18,730 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:07:18,822 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:07:18,822 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:07:18,822 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:07:18,822 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:07:18,823 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:07:18,823 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:07:18,823 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:07:18,823 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:07:18,823 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 108s)
2025-05-11 12:07:23,828 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:07:23,926 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:07:23,927 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:07:23,927 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:07:23,927 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:07:23,927 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:07:23,927 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:07:23,927 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:07:23,927 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:07:23,927 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 113s)
2025-05-11 12:07:28,928 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:07:29,026 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:07:29,026 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:07:29,026 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:07:29,026 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:07:29,026 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:07:29,026 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:07:29,026 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:07:29,026 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:07:29,026 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 118s)
2025-05-11 12:07:34,030 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:07:34,129 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:07:34,129 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:07:34,129 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:07:34,129 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:07:34,129 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:07:34,129 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:07:34,129 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:07:34,130 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:07:34,130 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 123s)
2025-05-11 12:07:39,134 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:07:39,232 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:07:39,232 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:07:39,232 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:07:39,232 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:07:39,232 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:07:39,232 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:07:39,233 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:07:39,233 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:07:39,233 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 128s)
2025-05-11 12:07:44,238 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:07:44,335 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:07:44,336 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:07:44,336 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:07:44,336 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:07:44,336 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:07:44,336 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:07:44,336 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:07:44,336 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:07:44,336 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 133s)
2025-05-11 12:07:49,340 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:07:49,434 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:07:49,434 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:07:49,434 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:07:49,434 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:07:49,434 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:07:49,434 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:07:49,434 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:07:49,434 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:07:49,434 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 138s)
2025-05-11 12:07:54,439 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:07:54,538 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:07:54,538 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:07:54,538 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:07:54,538 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:07:54,538 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:07:54,538 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:07:54,538 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:07:54,538 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:07:54,538 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 144s)
2025-05-11 12:07:59,539 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:07:59,636 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:07:59,636 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:07:59,636 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:07:59,636 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:07:59,636 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:07:59,636 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:07:59,636 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:07:59,636 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:07:59,636 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 149s)
2025-05-11 12:08:04,637 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:08:04,732 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:08:04,732 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:08:04,732 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:08:04,732 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:08:04,732 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:08:04,732 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:08:04,732 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:08:04,732 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:08:04,732 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 154s)
2025-05-11 12:08:09,737 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:08:09,836 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:08:09,836 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:08:09,836 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:08:09,836 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:08:09,836 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:08:09,836 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:08:09,836 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:08:09,836 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:08:09,836 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 159s)
2025-05-11 12:08:14,839 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:08:14,936 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:08:14,937 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:08:14,937 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:08:14,937 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:08:14,937 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:08:14,937 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:08:14,937 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:08:14,937 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:08:14,937 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 164s)
2025-05-11 12:08:19,942 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:08:20,041 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:08:20,041 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:08:20,041 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:08:20,041 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:08:20,041 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:08:20,041 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:08:20,041 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:08:20,041 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:08:20,041 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 169s)
2025-05-11 12:08:25,043 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:08:25,139 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:08:25,139 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:08:25,139 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:08:25,139 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:08:25,139 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:08:25,139 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:08:25,139 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:08:25,139 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:08:25,139 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 174s)
2025-05-11 12:08:30,142 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:08:30,240 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:08:30,240 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:08:30,240 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:08:30,240 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:08:30,240 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:08:30,240 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:08:30,240 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:08:30,240 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:08:30,240 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 179s)
2025-05-11 12:08:35,245 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:08:35,341 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:08:35,342 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:08:35,342 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:08:35,342 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:08:35,342 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:08:35,342 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:08:35,342 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:08:35,342 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:08:35,342 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 184s)
2025-05-11 12:08:40,346 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:08:40,444 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:08:40,445 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:08:40,445 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:08:40,445 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:08:40,445 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:08:40,445 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:08:40,445 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:08:40,445 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:08:40,445 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 189s)
2025-05-11 12:08:45,449 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:08:45,548 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:08:45,548 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:08:45,548 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:08:45,548 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:08:45,548 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:08:45,548 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:08:45,548 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:08:45,548 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:08:45,548 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 195s)
2025-05-11 12:08:50,550 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:08:50,642 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:08:50,642 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:08:50,642 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:08:50,642 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:08:50,642 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:08:50,642 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:08:50,642 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:08:50,642 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:08:50,643 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 200s)
2025-05-11 12:08:55,644 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:08:55,744 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:08:55,744 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:08:55,744 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:08:55,744 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:08:55,744 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:08:55,744 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:08:55,744 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:08:55,744 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:08:55,744 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 205s)
2025-05-11 12:09:00,749 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:09:00,845 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:09:00,845 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:09:00,846 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:09:00,846 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:09:00,846 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:09:00,846 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:09:00,846 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:09:00,846 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:09:00,846 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 210s)
2025-05-11 12:09:05,851 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:09:05,947 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:09:05,947 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:09:05,947 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:09:05,947 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:09:05,947 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:09:05,947 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:09:05,947 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:09:05,947 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:09:05,947 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 215s)
2025-05-11 12:09:10,949 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:09:11,044 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:09:11,044 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:09:11,044 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:09:11,044 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:09:11,044 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:09:11,044 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:09:11,044 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:09:11,044 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:09:11,044 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 220s)
2025-05-11 12:09:16,049 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:09:16,141 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:09:16,141 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:09:16,141 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:09:16,141 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:09:16,141 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:09:16,141 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:09:16,141 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:09:16,141 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:09:16,141 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 225s)
2025-05-11 12:09:21,142 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:09:21,240 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:09:21,240 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:09:21,240 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:09:21,240 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:09:21,240 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:09:21,241 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:09:21,241 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:09:21,241 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:09:21,241 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 230s)
2025-05-11 12:09:26,245 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:09:26,341 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:09:26,341 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:09:26,341 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:09:26,341 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:09:26,341 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:09:26,341 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:09:26,341 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:09:26,341 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:09:26,341 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 235s)
2025-05-11 12:09:31,342 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:09:31,440 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:09:31,440 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:09:31,440 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:09:31,440 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:09:31,440 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:09:31,440 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:09:31,440 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:09:31,440 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:09:31,440 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 240s)
2025-05-11 12:09:36,444 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:09:36,540 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:09:36,540 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:09:36,540 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:09:36,540 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:09:36,540 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:09:36,540 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:09:36,540 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:09:36,540 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:09:36,540 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 246s)
2025-05-11 12:09:41,544 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:09:41,643 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:09:41,643 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:09:41,643 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:09:41,643 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:09:41,643 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:09:41,643 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:09:41,643 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:09:41,643 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:09:41,643 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 251s)
2025-05-11 12:09:46,649 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:09:46,747 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:09:46,748 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:09:46,748 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:09:46,748 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:09:46,748 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:09:46,748 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:09:46,748 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:09:46,748 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:09:46,748 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 256s)
2025-05-11 12:09:51,752 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:09:51,832 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:09:51,832 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:09:51,832 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:09:51,832 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:09:51,832 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:09:51,833 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:09:51,833 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:09:51,833 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:09:51,833 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 261s)
2025-05-11 12:09:56,833 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:09:56,932 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:09:56,932 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:09:56,932 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:09:56,932 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:09:56,932 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:09:56,932 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:09:56,932 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:09:56,932 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:09:56,932 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 266s)
2025-05-11 12:10:01,935 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:10:02,017 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:10:02,017 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:10:02,017 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:10:02,017 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:10:02,017 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:10:02,017 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:10:02,017 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:10:02,017 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:10:02,017 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 271s)
2025-05-11 12:10:07,021 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:10:07,105 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:10:07,105 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:10:07,105 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:10:07,105 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:10:07,105 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:10:07,105 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:10:07,105 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:10:07,105 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:10:07,105 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 276s)
2025-05-11 12:10:12,106 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:10:12,226 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:10:12,226 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:10:12,226 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:10:12,226 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:10:12,226 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:10:12,226 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:10:12,226 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:10:12,226 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:10:12,227 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 281s)
2025-05-11 12:10:17,227 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:10:17,319 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:10:17,319 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:10:17,319 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:10:17,319 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:10:17,319 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:10:17,319 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:10:17,319 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:10:17,319 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:10:17,319 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 286s)
2025-05-11 12:10:22,323 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:10:22,407 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:10:22,407 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:10:22,407 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:10:22,407 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:10:22,407 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:10:22,407 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:10:22,407 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:10:22,407 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:10:22,408 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 291s)
2025-05-11 12:10:27,413 - docker_compose_test - INFO - Checking container status...
2025-05-11 12:10:27,487 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 12:10:27,487 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 12:10:27,487 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 12:10:27,487 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 12:10:27,487 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 12:10:27,487 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 12:10:27,487 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 12:10:27,487 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 12:10:27,487 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 296s)
2025-05-11 12:10:32,490 - docker_compose_test - ERROR - Timeout waiting for services to be healthy
2025-05-11 12:10:32,491 - docker_compose_test - ERROR - Failed to wait for services to be healthy
2025-05-11 12:10:32,491 - docker_compose_test - INFO - Stopping services using Docker Compose...
2025-05-11 12:10:33,152 - docker_compose_test - INFO - Services stopped successfully
2025-05-11 12:10:33,152 - docker_compose_test - ERROR - Docker Compose test failed
2025-05-11 13:01:15,228 - docker_compose_test - INFO - Starting Docker Compose test...
2025-05-11 13:01:15,229 - docker_compose_test - INFO - Starting services using Docker Compose...
2025-05-11 13:01:16,117 - docker_compose_test - INFO - Services started successfully
2025-05-11 13:01:16,117 - docker_compose_test - INFO - Waiting for services to be healthy...
2025-05-11 13:01:16,117 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:01:16,203 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:01:16,203 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:01:16,203 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:01:16,203 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:01:16,203 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:01:16,203 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 13:01:16,203 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:01:16,203 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:01:16,203 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 0s)
2025-05-11 13:01:21,208 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:01:21,280 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:01:21,280 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:01:21,280 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:01:21,280 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:01:21,280 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:01:21,280 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 13:01:21,280 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:01:21,280 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:01:21,280 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 5s)
2025-05-11 13:01:26,285 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:01:26,375 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:01:26,375 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:01:26,375 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:01:26,375 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:01:26,375 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:01:26,375 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:01:26,375 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:01:26,375 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:01:26,375 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 10s)
2025-05-11 13:01:31,381 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:01:31,466 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:01:31,466 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:01:31,466 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:01:31,466 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:01:31,466 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:01:31,466 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 13:01:31,466 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:01:31,466 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:01:31,466 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 15s)
2025-05-11 13:01:36,471 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:01:36,551 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:01:36,551 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:01:36,551 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:01:36,551 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:01:36,551 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:01:36,551 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:01:36,551 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:01:36,551 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:01:36,551 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 20s)
2025-05-11 13:01:41,556 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:01:41,648 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:01:41,648 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:01:41,648 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:01:41,648 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:01:41,648 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:01:41,648 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:01:41,648 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:01:41,648 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:01:41,648 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 26s)
2025-05-11 13:01:46,649 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:01:46,725 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:01:46,725 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:01:46,725 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:01:46,725 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:01:46,725 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:01:46,725 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:01:46,725 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:01:46,725 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:01:46,725 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 31s)
2025-05-11 13:01:51,730 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:01:51,832 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:01:51,832 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:01:51,832 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:01:51,832 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:01:51,832 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:01:51,832 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:01:51,832 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:01:51,832 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:01:51,832 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 36s)
2025-05-11 13:01:56,834 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:01:56,918 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:01:56,919 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:01:56,919 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:01:56,919 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:01:56,919 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:01:56,919 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:01:56,919 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:01:56,919 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:01:56,919 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 41s)
2025-05-11 13:02:01,920 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:02:01,995 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:02:01,995 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:02:01,995 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:02:01,995 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:02:01,995 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:02:01,995 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:02:01,995 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:02:01,995 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:02:01,995 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 46s)
2025-05-11 13:02:07,000 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:02:07,085 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:02:07,085 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:02:07,085 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:02:07,085 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:02:07,085 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:02:07,085 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:02:07,085 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:02:07,085 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:02:07,085 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 51s)
2025-05-11 13:02:12,091 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:02:12,189 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:02:12,189 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:02:12,189 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:02:12,189 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:02:12,189 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:02:12,189 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:02:12,189 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:02:12,189 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:02:12,189 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 56s)
2025-05-11 13:02:17,195 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:02:17,285 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:02:17,285 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:02:17,285 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:02:17,285 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:02:17,285 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:02:17,285 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:02:17,285 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:02:17,285 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:02:17,285 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 61s)
2025-05-11 13:02:22,290 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:02:22,388 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:02:22,388 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:02:22,388 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:02:22,388 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:02:22,388 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:02:22,388 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:02:22,388 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:02:22,388 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:02:22,388 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 66s)
2025-05-11 13:02:27,393 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:02:27,489 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:02:27,489 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:02:27,489 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:02:27,489 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:02:27,489 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:02:27,489 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:02:27,489 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:02:27,489 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:02:27,489 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 71s)
2025-05-11 13:02:32,494 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:02:32,587 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:02:32,587 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:02:32,587 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:02:32,587 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:02:32,587 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:02:32,587 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:02:32,587 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:02:32,587 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:02:32,587 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 76s)
2025-05-11 13:02:37,587 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:02:37,690 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:02:37,690 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:02:37,690 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:02:37,690 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:02:37,690 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:02:37,690 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:02:37,690 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:02:37,690 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:02:37,690 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 82s)
2025-05-11 13:02:42,695 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:02:42,794 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:02:42,794 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:02:42,794 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:02:42,794 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:02:42,794 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:02:42,794 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:02:42,794 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:02:42,794 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:02:42,794 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 87s)
2025-05-11 13:02:47,798 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:02:47,871 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:02:47,871 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:02:47,871 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:02:47,871 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:02:47,871 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:02:47,871 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:02:47,871 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:02:47,871 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:02:47,871 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 92s)
2025-05-11 13:02:52,874 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:02:52,974 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:02:52,974 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:02:52,974 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:02:52,974 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:02:52,974 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:02:52,974 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:02:52,974 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:02:52,974 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:02:52,974 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 97s)
2025-05-11 13:02:57,975 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:02:58,085 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:02:58,085 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:02:58,085 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:02:58,085 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:02:58,085 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:02:58,085 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:02:58,085 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:02:58,085 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:02:58,086 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 102s)
2025-05-11 13:03:03,090 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:03:03,201 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:03:03,202 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:03:03,202 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:03:03,202 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:03:03,202 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:03:03,202 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:03:03,202 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:03:03,202 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:03:03,202 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 107s)
2025-05-11 13:03:08,206 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:03:08,302 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:03:08,302 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:03:08,302 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:03:08,302 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:03:08,302 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:03:08,302 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:03:08,302 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:03:08,302 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:03:08,302 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 112s)
2025-05-11 13:03:13,307 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:03:13,403 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:03:13,403 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:03:13,403 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:03:13,403 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:03:13,403 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:03:13,403 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:03:13,403 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:03:13,403 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:03:13,403 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 117s)
2025-05-11 13:03:18,409 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:03:18,504 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:03:18,505 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:03:18,505 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:03:18,505 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:03:18,505 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:03:18,505 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:03:18,505 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:03:18,505 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:03:18,505 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 122s)
2025-05-11 13:03:23,508 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:03:23,605 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:03:23,605 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:03:23,605 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:03:23,605 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:03:23,605 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:03:23,605 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:03:23,605 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:03:23,605 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:03:23,605 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 127s)
2025-05-11 13:03:28,610 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:03:28,708 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:03:28,708 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:03:28,708 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:03:28,708 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:03:28,708 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:03:28,708 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:03:28,708 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:03:28,708 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:03:28,708 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 133s)
2025-05-11 13:03:33,711 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:03:33,810 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:03:33,810 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:03:33,810 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:03:33,810 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:03:33,810 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:03:33,810 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:03:33,810 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:03:33,810 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:03:33,810 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 138s)
2025-05-11 13:03:38,811 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:03:38,920 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:03:38,920 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:03:38,920 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:03:38,920 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:03:38,920 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:03:38,920 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:03:38,920 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:03:38,920 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:03:38,920 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 143s)
2025-05-11 13:03:43,925 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:03:44,019 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:03:44,019 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:03:44,019 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:03:44,019 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:03:44,019 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:03:44,019 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:03:44,019 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:03:44,019 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:03:44,019 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 148s)
2025-05-11 13:03:49,021 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:03:49,118 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:03:49,118 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:03:49,118 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:03:49,118 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:03:49,118 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:03:49,118 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:03:49,118 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:03:49,118 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:03:49,118 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 153s)
2025-05-11 13:03:54,121 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:03:54,218 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:03:54,218 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:03:54,218 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:03:54,218 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:03:54,218 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:03:54,218 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:03:54,218 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:03:54,218 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:03:54,218 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 158s)
2025-05-11 13:03:59,223 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:03:59,320 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:03:59,320 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:03:59,320 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:03:59,320 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:03:59,320 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:03:59,320 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:03:59,320 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:03:59,320 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:03:59,320 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 163s)
2025-05-11 13:04:04,322 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:04:04,408 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:04:04,408 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:04:04,408 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:04:04,408 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:04:04,408 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:04:04,408 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:04:04,408 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:04:04,408 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:04:04,408 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 168s)
2025-05-11 13:04:09,413 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:04:09,511 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:04:09,511 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:04:09,511 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:04:09,511 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:04:09,511 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:04:09,511 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:04:09,511 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:04:09,511 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:04:09,511 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 173s)
2025-05-11 13:04:14,517 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:04:14,619 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:04:14,619 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:04:14,619 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:04:14,619 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:04:14,619 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:04:14,619 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:04:14,619 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:04:14,619 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:04:14,619 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 179s)
2025-05-11 13:04:19,625 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:04:19,722 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:04:19,722 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:04:19,722 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:04:19,722 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:04:19,722 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:04:19,722 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:04:19,722 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:04:19,722 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:04:19,722 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 184s)
2025-05-11 13:04:24,728 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:04:24,821 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:04:24,821 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:04:24,821 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:04:24,821 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:04:24,821 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:04:24,821 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 13:04:24,821 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:04:24,821 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:04:24,821 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 189s)
2025-05-11 13:04:25,684 - docker_compose_test - INFO - Stopping services using Docker Compose...
2025-05-11 13:04:26,489 - docker_compose_test - INFO - Services stopped successfully
2025-05-11 13:24:43,182 - docker_compose_test - INFO - Starting Docker Compose test...
2025-05-11 13:24:43,182 - docker_compose_test - INFO - Starting services using Docker Compose...
2025-05-11 13:24:44,421 - docker_compose_test - INFO - Services started successfully
2025-05-11 13:24:44,421 - docker_compose_test - INFO - Waiting for services to be healthy...
2025-05-11 13:24:44,421 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:24:44,500 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:24:44,501 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:24:44,501 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:24:44,501 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:24:44,501 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:24:44,501 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 13:24:44,501 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:24:44,501 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:24:44,501 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 0s)
2025-05-11 13:24:49,505 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:24:49,596 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:24:49,596 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:24:49,596 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:24:49,596 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:24:49,596 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:24:49,596 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 13:24:49,596 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:24:49,596 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:24:49,596 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 5s)
2025-05-11 13:24:54,601 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:24:54,684 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:24:54,684 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:24:54,684 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:24:54,684 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:24:54,684 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:24:54,684 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 13:24:54,684 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:24:54,684 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:24:54,684 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 10s)
2025-05-11 13:24:59,690 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:24:59,773 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:24:59,773 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:24:59,773 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:24:59,773 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:24:59,773 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:24:59,773 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 13:24:59,773 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:24:59,773 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:24:59,773 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 15s)
2025-05-11 13:25:04,778 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:25:04,879 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:25:04,879 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:25:04,879 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:25:04,879 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:25:04,879 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:25:04,879 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 13:25:04,879 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:25:04,879 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:25:04,879 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 20s)
2025-05-11 13:25:09,884 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:25:09,970 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:25:09,970 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:25:09,970 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:25:09,970 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:25:09,970 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:25:09,970 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 13:25:09,970 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:25:09,970 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:25:09,970 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 26s)
2025-05-11 13:25:14,972 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:25:15,074 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:25:15,074 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:25:15,074 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:25:15,074 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:25:15,074 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:25:15,074 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 13:25:15,074 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:25:15,074 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:25:15,074 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 31s)
2025-05-11 13:25:20,078 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:25:20,174 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:25:20,174 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:25:20,174 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:25:20,174 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:25:20,174 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:25:20,174 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 13:25:20,174 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:25:20,174 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:25:20,174 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 36s)
2025-05-11 13:25:25,178 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:25:25,276 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:25:25,277 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:25:25,277 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:25:25,277 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:25:25,277 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:25:25,277 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 13:25:25,277 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:25:25,277 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:25:25,277 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 41s)
2025-05-11 13:25:30,278 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:25:30,476 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:25:30,476 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:25:30,476 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:25:30,476 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:25:30,476 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:25:30,476 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 13:25:30,476 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:25:30,476 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:25:30,476 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 46s)
2025-05-11 13:25:35,478 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:25:35,577 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:25:35,577 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:25:35,577 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:25:35,577 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:25:35,577 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:25:35,577 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 13:25:35,577 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:25:35,577 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:25:35,577 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 51s)
2025-05-11 13:25:40,577 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:25:40,659 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:25:40,659 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:25:40,659 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:25:40,659 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:25:40,659 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:25:40,659 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 13:25:40,659 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:25:40,659 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:25:40,659 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 56s)
2025-05-11 13:25:45,661 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:25:45,758 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:25:45,758 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:25:45,758 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:25:45,758 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:25:45,758 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:25:45,758 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 13:25:45,758 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:25:45,758 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:25:45,758 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 61s)
2025-05-11 13:25:50,763 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:25:50,862 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:25:50,862 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:25:50,862 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:25:50,862 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:25:50,862 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:25:50,862 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 13:25:50,862 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:25:50,862 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:25:50,862 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 66s)
2025-05-11 13:25:55,867 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:25:55,965 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:25:55,965 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:25:55,965 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:25:55,965 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:25:55,965 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:25:55,965 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:25:55,965 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:25:55,965 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:25:55,965 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 72s)
2025-05-11 13:26:00,969 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:26:01,058 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:26:01,058 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:26:01,059 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:26:01,059 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:26:01,059 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:26:01,059 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:26:01,059 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:26:01,059 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:26:01,059 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 77s)
2025-05-11 13:26:06,063 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:26:06,158 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:26:06,158 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:26:06,158 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:26:06,158 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:26:06,158 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:26:06,158 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:26:06,158 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:26:06,158 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:26:06,158 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 82s)
2025-05-11 13:26:11,163 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:26:11,256 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:26:11,257 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:26:11,257 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:26:11,257 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:26:11,257 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:26:11,257 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:26:11,257 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:26:11,257 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:26:11,257 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 87s)
2025-05-11 13:26:16,262 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:26:16,361 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:26:16,361 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:26:16,361 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:26:16,361 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:26:16,361 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:26:16,361 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:26:16,361 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:26:16,361 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:26:16,361 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 92s)
2025-05-11 13:26:21,366 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:26:21,468 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:26:21,468 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:26:21,468 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:26:21,468 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:26:21,468 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:26:21,468 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:26:21,468 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:26:21,468 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:26:21,468 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 97s)
2025-05-11 13:26:26,473 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:26:26,573 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:26:26,573 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:26:26,573 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:26:26,573 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:26:26,573 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:26:26,573 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:26:26,573 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:26:26,573 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:26:26,573 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 102s)
2025-05-11 13:26:31,577 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:26:31,676 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:26:31,676 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:26:31,676 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:26:31,676 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:26:31,676 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:26:31,676 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:26:31,676 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:26:31,676 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:26:31,676 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 107s)
2025-05-11 13:26:36,678 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:26:36,777 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:26:36,778 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:26:36,778 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:26:36,778 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:26:36,778 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:26:36,778 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:26:36,778 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:26:36,778 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:26:36,778 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 112s)
2025-05-11 13:26:41,780 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:26:41,869 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:26:41,869 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:26:41,869 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:26:41,869 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:26:41,869 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:26:41,869 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:26:41,869 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:26:41,869 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:26:41,869 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 117s)
2025-05-11 13:26:46,872 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:26:46,970 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:26:46,970 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:26:46,970 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:26:46,970 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:26:46,970 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:26:46,970 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:26:46,970 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:26:46,970 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:26:46,970 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 123s)
2025-05-11 13:26:51,974 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:26:52,065 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:26:52,066 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:26:52,066 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:26:52,066 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:26:52,066 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:26:52,066 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:26:52,066 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:26:52,066 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:26:52,066 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 128s)
2025-05-11 13:26:57,070 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:26:57,149 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:26:57,149 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:26:57,149 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:26:57,149 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:26:57,150 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:26:57,150 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:26:57,150 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:26:57,150 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:26:57,150 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 133s)
2025-05-11 13:27:02,150 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:27:02,236 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:27:02,237 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:27:02,237 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:27:02,237 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:27:02,237 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:27:02,237 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:27:02,237 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:27:02,237 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:27:02,237 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 138s)
2025-05-11 13:27:07,238 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:27:07,340 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:27:07,340 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:27:07,340 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:27:07,340 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:27:07,340 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:27:07,340 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:27:07,340 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:27:07,340 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:27:07,340 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 143s)
2025-05-11 13:27:12,345 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:27:12,441 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:27:12,441 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:27:12,441 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:27:12,441 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:27:12,441 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:27:12,441 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:27:12,441 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:27:12,441 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:27:12,441 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 148s)
2025-05-11 13:27:17,441 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:27:17,533 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:27:17,533 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:27:17,533 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:27:17,533 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:27:17,533 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:27:17,533 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:27:17,533 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:27:17,533 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:27:17,533 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 153s)
2025-05-11 13:27:22,538 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:27:22,639 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:27:22,639 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:27:22,639 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:27:22,639 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:27:22,639 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:27:22,639 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:27:22,639 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:27:22,639 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:27:22,639 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 158s)
2025-05-11 13:27:27,643 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:27:27,730 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:27:27,730 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:27:27,730 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:27:27,730 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:27:27,730 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:27:27,730 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:27:27,730 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:27:27,730 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:27:27,730 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 163s)
2025-05-11 13:27:32,733 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:27:32,833 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:27:32,833 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:27:32,833 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:27:32,833 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:27:32,833 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:27:32,833 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:27:32,833 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:27:32,833 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:27:32,833 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 168s)
2025-05-11 13:27:37,835 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:27:37,931 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:27:37,931 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:27:37,931 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:27:37,931 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:27:37,931 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:27:37,932 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:27:37,932 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:27:37,932 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:27:37,932 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 174s)
2025-05-11 13:27:42,936 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:27:43,027 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:27:43,027 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:27:43,027 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:27:43,027 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:27:43,027 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:27:43,027 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:27:43,027 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:27:43,027 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:27:43,027 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 179s)
2025-05-11 13:27:48,032 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:27:48,120 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:27:48,120 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:27:48,120 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:27:48,120 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:27:48,120 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:27:48,120 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:27:48,120 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:27:48,120 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:27:48,120 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 184s)
2025-05-11 13:27:53,125 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:27:53,236 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:27:53,236 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:27:53,236 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:27:53,236 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:27:53,236 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:27:53,236 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:27:53,236 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:27:53,236 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:27:53,236 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 189s)
2025-05-11 13:27:58,241 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:27:58,347 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:27:58,348 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:27:58,348 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:27:58,348 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:27:58,348 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:27:58,348 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:27:58,348 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:27:58,348 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:27:58,348 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 194s)
2025-05-11 13:28:03,353 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:28:03,446 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:28:03,446 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:28:03,446 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:28:03,446 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:28:03,446 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:28:03,446 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:28:03,446 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:28:03,446 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:28:03,446 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 199s)
2025-05-11 13:28:08,450 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:28:08,547 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:28:08,547 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:28:08,547 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:28:08,547 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:28:08,547 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:28:08,547 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:28:08,547 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:28:08,547 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:28:08,547 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 204s)
2025-05-11 13:28:13,549 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:28:13,641 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:28:13,641 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:28:13,641 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:28:13,641 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:28:13,641 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:28:13,641 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:28:13,641 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:28:13,641 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:28:13,641 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 209s)
2025-05-11 13:28:18,644 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:28:18,716 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:28:18,716 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:28:18,716 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:28:18,717 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:28:18,717 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:28:18,717 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:28:18,717 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:28:18,717 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:28:18,717 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 214s)
2025-05-11 13:28:23,722 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:28:23,822 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:28:23,822 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:28:23,822 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:28:23,822 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:28:23,822 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:28:23,822 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:28:23,822 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:28:23,822 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:28:23,822 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 219s)
2025-05-11 13:28:28,827 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:28:28,907 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:28:28,908 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:28:28,908 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:28:28,908 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:28:28,908 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:28:28,908 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:28:28,908 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:28:28,908 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:28:28,908 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 224s)
2025-05-11 13:28:33,911 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:28:34,006 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:28:34,006 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:28:34,006 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:28:34,006 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:28:34,006 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:28:34,006 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:28:34,007 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:28:34,007 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:28:34,007 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 230s)
2025-05-11 13:28:39,012 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:28:39,095 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:28:39,095 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:28:39,095 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:28:39,095 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:28:39,095 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:28:39,095 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:28:39,095 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:28:39,095 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:28:39,096 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 235s)
2025-05-11 13:28:44,100 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:28:44,170 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=starting
2025-05-11 13:28:44,170 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:28:44,170 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:28:44,170 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:28:44,170 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:28:44,170 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:28:44,170 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:28:44,170 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:28:44,170 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 240s)
2025-05-11 13:28:49,174 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:28:49,267 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:28:49,267 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:28:49,267 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:28:49,267 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:28:49,267 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:28:49,267 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:28:49,267 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:28:49,267 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:28:49,267 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 245s)
2025-05-11 13:28:54,269 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:28:54,373 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:28:54,373 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:28:54,373 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:28:54,373 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:28:54,373 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:28:54,373 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:28:54,373 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:28:54,373 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:28:54,373 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 250s)
2025-05-11 13:28:59,378 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:28:59,455 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:28:59,456 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:28:59,456 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:28:59,456 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:28:59,456 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:28:59,456 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:28:59,456 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:28:59,456 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:28:59,456 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 255s)
2025-05-11 13:29:03,764 - docker_compose_test - INFO - Stopping services using Docker Compose...
2025-05-11 13:29:04,915 - docker_compose_test - INFO - Services stopped successfully
2025-05-11 13:46:45,894 - docker_compose_test - INFO - Starting Docker Compose test...
2025-05-11 13:46:45,894 - docker_compose_test - INFO - Starting services using Docker Compose...
2025-05-11 13:46:47,106 - docker_compose_test - INFO - Services started successfully
2025-05-11 13:46:47,106 - docker_compose_test - INFO - Waiting for services to be healthy...
2025-05-11 13:46:47,107 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:46:47,193 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:46:47,193 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:46:47,195 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:46:47,195 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:46:47,195 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:46:47,195 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 13:46:47,195 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:46:47,195 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:46:47,196 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 0s)
2025-05-11 13:46:52,199 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:46:52,307 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:46:52,307 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:46:52,307 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:46:52,307 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:46:52,307 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:46:52,307 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 13:46:52,307 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:46:52,307 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:46:52,307 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 5s)
2025-05-11 13:46:57,312 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:46:57,401 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:46:57,401 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:46:57,401 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:46:57,401 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:46:57,401 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:46:57,401 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 13:46:57,401 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:46:57,401 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:46:57,401 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 10s)
2025-05-11 13:47:02,406 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:47:02,509 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:47:02,510 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:47:02,510 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:47:02,510 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:47:02,510 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:47:02,510 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 13:47:02,510 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:47:02,510 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:47:02,510 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 15s)
2025-05-11 13:47:07,510 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:47:07,609 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:47:07,609 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:47:07,609 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:47:07,609 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:47:07,609 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:47:07,609 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 13:47:07,609 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:47:07,609 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:47:07,610 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 21s)
2025-05-11 13:47:12,615 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:47:12,713 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:47:12,713 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:47:12,713 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:47:12,713 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:47:12,713 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:47:12,713 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 13:47:12,713 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:47:12,713 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:47:12,713 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 26s)
2025-05-11 13:47:17,716 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:47:17,807 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:47:17,807 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:47:17,807 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:47:17,807 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:47:17,807 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:47:17,807 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 13:47:17,807 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:47:17,807 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:47:17,807 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 31s)
2025-05-11 13:47:22,812 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:47:22,911 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:47:22,911 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:47:22,912 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:47:22,912 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:47:22,912 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:47:22,912 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 13:47:22,912 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:47:22,912 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:47:22,912 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 36s)
2025-05-11 13:47:27,914 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:47:28,014 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:47:28,014 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:47:28,014 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:47:28,014 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:47:28,014 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:47:28,014 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 13:47:28,014 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:47:28,015 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:47:28,015 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 41s)
2025-05-11 13:47:33,017 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:47:33,114 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:47:33,114 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:47:33,114 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:47:33,114 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:47:33,114 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:47:33,114 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 13:47:33,114 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:47:33,114 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:47:33,114 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 46s)
2025-05-11 13:47:38,119 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:47:38,216 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:47:38,216 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:47:38,216 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:47:38,216 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:47:38,216 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:47:38,216 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 13:47:38,216 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:47:38,216 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:47:38,216 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 51s)
2025-05-11 13:47:43,220 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:47:43,318 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:47:43,318 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:47:43,318 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:47:43,318 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:47:43,318 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:47:43,318 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 13:47:43,318 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:47:43,318 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:47:43,318 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 56s)
2025-05-11 13:47:48,323 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:47:48,424 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:47:48,424 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:47:48,424 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:47:48,424 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:47:48,424 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:47:48,424 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 13:47:48,424 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:47:48,424 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:47:48,424 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 61s)
2025-05-11 13:47:53,428 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:47:53,527 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:47:53,528 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:47:53,528 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:47:53,528 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:47:53,528 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:47:53,528 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 13:47:53,528 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:47:53,528 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 13:47:53,528 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 66s)
2025-05-11 13:47:58,529 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:47:58,624 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:47:58,624 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:47:58,624 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:47:58,624 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:47:58,625 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:47:58,625 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:47:58,625 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:47:58,625 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:47:58,625 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 72s)
2025-05-11 13:48:03,628 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:48:03,725 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:48:03,725 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:48:03,726 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:48:03,726 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:48:03,726 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:48:03,726 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:48:03,726 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:48:03,726 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:48:03,726 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 77s)
2025-05-11 13:48:08,727 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:48:08,811 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:48:08,811 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:48:08,811 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:48:08,811 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:48:08,811 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:48:08,811 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:48:08,811 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:48:08,811 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:48:08,811 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 82s)
2025-05-11 13:48:13,815 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:48:13,901 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:48:13,901 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:48:13,901 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:48:13,901 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:48:13,901 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:48:13,901 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:48:13,901 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:48:13,901 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:48:13,901 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 87s)
2025-05-11 13:48:18,904 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:48:19,003 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:48:19,003 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:48:19,003 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:48:19,003 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:48:19,003 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:48:19,003 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:48:19,003 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:48:19,003 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:48:19,003 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 92s)
2025-05-11 13:48:24,009 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:48:24,106 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:48:24,106 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:48:24,106 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:48:24,106 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:48:24,106 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:48:24,106 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:48:24,106 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:48:24,106 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:48:24,106 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 97s)
2025-05-11 13:48:29,109 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:48:29,206 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:48:29,206 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:48:29,206 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:48:29,206 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:48:29,206 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:48:29,206 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:48:29,206 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:48:29,206 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:48:29,206 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 102s)
2025-05-11 13:48:34,211 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:48:34,311 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:48:34,311 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:48:34,311 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:48:34,311 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:48:34,311 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:48:34,311 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:48:34,311 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:48:34,311 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:48:34,311 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 107s)
2025-05-11 13:48:39,315 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:48:39,402 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:48:39,402 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:48:39,402 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:48:39,402 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:48:39,402 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:48:39,402 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:48:39,402 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:48:39,402 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:48:39,402 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 112s)
2025-05-11 13:48:44,404 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:48:44,499 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:48:44,499 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:48:44,499 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:48:44,499 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:48:44,499 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:48:44,499 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:48:44,499 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:48:44,499 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:48:44,499 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 117s)
2025-05-11 13:48:49,502 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:48:49,587 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:48:49,587 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:48:49,588 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:48:49,588 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:48:49,588 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:48:49,588 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:48:49,588 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:48:49,588 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:48:49,588 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 122s)
2025-05-11 13:48:54,592 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:48:54,680 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:48:54,680 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:48:54,680 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:48:54,680 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:48:54,680 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:48:54,680 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:48:54,680 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:48:54,680 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:48:54,680 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 128s)
2025-05-11 13:48:59,685 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:48:59,772 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:48:59,772 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:48:59,772 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:48:59,772 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:48:59,772 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:48:59,772 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:48:59,772 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:48:59,772 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:48:59,772 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 133s)
2025-05-11 13:49:04,776 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:49:04,875 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:49:04,875 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:49:04,875 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:49:04,875 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:49:04,875 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:49:04,875 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:49:04,875 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:49:04,875 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:49:04,876 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 138s)
2025-05-11 13:49:09,877 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:49:09,969 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:49:09,969 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:49:09,969 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:49:09,969 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:49:09,969 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:49:09,969 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:49:09,969 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:49:09,969 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:49:09,969 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 143s)
2025-05-11 13:49:14,975 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:49:15,070 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:49:15,070 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:49:15,070 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:49:15,070 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:49:15,070 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:49:15,070 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:49:15,070 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:49:15,071 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:49:15,071 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 148s)
2025-05-11 13:49:20,075 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:49:20,172 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:49:20,172 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:49:20,172 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:49:20,172 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:49:20,172 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:49:20,172 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:49:20,172 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:49:20,172 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:49:20,172 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 153s)
2025-05-11 13:49:25,176 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:49:25,274 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:49:25,274 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:49:25,274 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:49:25,274 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:49:25,274 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:49:25,275 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:49:25,275 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:49:25,275 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:49:25,275 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 158s)
2025-05-11 13:49:30,279 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:49:30,377 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:49:30,377 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:49:30,377 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:49:30,377 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:49:30,377 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:49:30,377 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:49:30,377 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:49:30,377 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:49:30,377 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 163s)
2025-05-11 13:49:35,381 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:49:35,480 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:49:35,480 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:49:35,481 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:49:35,481 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:49:35,481 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:49:35,481 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:49:35,481 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:49:35,481 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:49:35,481 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 168s)
2025-05-11 13:49:40,485 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:49:40,582 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:49:40,582 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:49:40,582 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:49:40,582 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:49:40,582 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:49:40,582 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:49:40,582 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:49:40,582 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:49:40,582 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 173s)
2025-05-11 13:49:45,586 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:49:45,681 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:49:45,681 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:49:45,681 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:49:45,681 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:49:45,681 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:49:45,681 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:49:45,681 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:49:45,681 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:49:45,682 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 179s)
2025-05-11 13:49:50,684 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:49:50,783 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:49:50,784 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:49:50,784 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:49:50,784 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:49:50,784 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:49:50,784 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:49:50,784 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:49:50,784 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:49:50,784 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 184s)
2025-05-11 13:49:55,788 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:49:55,868 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:49:55,868 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:49:55,868 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:49:55,868 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:49:55,868 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:49:55,868 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:49:55,868 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:49:55,868 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:49:55,868 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 189s)
2025-05-11 13:50:00,869 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:50:00,963 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:50:00,963 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:50:00,963 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:50:00,963 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:50:00,963 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:50:00,963 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:50:00,963 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:50:00,963 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:50:00,963 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 194s)
2025-05-11 13:50:05,965 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:50:06,066 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:50:06,066 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:50:06,066 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:50:06,066 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:50:06,066 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:50:06,066 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:50:06,066 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:50:06,066 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:50:06,066 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 199s)
2025-05-11 13:50:11,068 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:50:11,147 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:50:11,147 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:50:11,147 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:50:11,147 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:50:11,147 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:50:11,147 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:50:11,147 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:50:11,147 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:50:11,147 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 204s)
2025-05-11 13:50:16,150 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:50:16,250 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:50:16,250 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:50:16,250 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:50:16,250 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:50:16,250 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:50:16,250 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:50:16,250 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:50:16,250 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:50:16,251 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 209s)
2025-05-11 13:50:21,251 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:50:21,337 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:50:21,337 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:50:21,337 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:50:21,337 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:50:21,337 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:50:21,337 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:50:21,337 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:50:21,337 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:50:21,337 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 214s)
2025-05-11 13:50:26,341 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:50:26,440 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:50:26,441 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:50:26,442 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:50:26,442 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:50:26,442 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:50:26,442 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:50:26,442 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:50:26,442 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:50:26,442 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 219s)
2025-05-11 13:50:31,444 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:50:31,543 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:50:31,543 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:50:31,543 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:50:31,543 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:50:31,543 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:50:31,543 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:50:31,543 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:50:31,543 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:50:31,543 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 224s)
2025-05-11 13:50:36,544 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:50:36,639 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:50:36,639 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:50:36,639 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:50:36,639 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:50:36,639 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:50:36,639 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:50:36,639 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:50:36,639 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:50:36,639 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 230s)
2025-05-11 13:50:41,640 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:50:41,724 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:50:41,724 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:50:41,724 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:50:41,724 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:50:41,724 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:50:41,724 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:50:41,724 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:50:41,724 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:50:41,724 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 235s)
2025-05-11 13:50:46,729 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:50:46,809 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:50:46,810 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:50:46,810 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:50:46,810 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:50:46,810 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:50:46,810 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:50:46,810 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:50:46,810 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:50:46,810 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 240s)
2025-05-11 13:50:51,815 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:50:51,898 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:50:51,899 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:50:51,899 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:50:51,899 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:50:51,899 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:50:51,899 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:50:51,899 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:50:51,899 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:50:51,899 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 245s)
2025-05-11 13:50:56,904 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:50:57,001 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:50:57,001 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:50:57,001 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:50:57,001 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:50:57,001 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:50:57,001 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:50:57,001 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:50:57,001 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:50:57,001 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 250s)
2025-05-11 13:51:02,007 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:51:02,104 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:51:02,104 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:51:02,104 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:51:02,104 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:51:02,104 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:51:02,104 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:51:02,104 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:51:02,104 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:51:02,104 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 255s)
2025-05-11 13:51:07,109 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:51:07,204 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:51:07,204 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:51:07,205 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:51:07,205 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:51:07,205 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:51:07,205 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:51:07,205 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:51:07,205 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:51:07,205 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 260s)
2025-05-11 13:51:12,210 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:51:12,307 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:51:12,307 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:51:12,307 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:51:12,307 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:51:12,307 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:51:12,307 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:51:12,307 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:51:12,307 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:51:12,307 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 265s)
2025-05-11 13:51:17,309 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:51:17,409 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:51:17,409 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:51:17,409 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:51:17,409 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:51:17,409 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:51:17,409 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:51:17,409 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:51:17,409 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:51:17,409 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 270s)
2025-05-11 13:51:22,410 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:51:22,509 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:51:22,510 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:51:22,510 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:51:22,510 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:51:22,510 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:51:22,510 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:51:22,510 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:51:22,510 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:51:22,510 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 275s)
2025-05-11 13:51:27,514 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:51:27,609 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:51:27,609 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:51:27,609 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:51:27,609 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:51:27,609 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:51:27,609 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:51:27,609 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:51:27,609 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:51:27,609 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 281s)
2025-05-11 13:51:32,614 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:51:32,710 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:51:32,710 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:51:32,710 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:51:32,710 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:51:32,710 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:51:32,710 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:51:32,710 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:51:32,710 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:51:32,710 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 286s)
2025-05-11 13:51:37,715 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:51:37,818 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:51:37,818 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:51:37,818 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:51:37,818 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:51:37,818 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:51:37,818 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:51:37,818 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:51:37,818 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:51:37,818 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 291s)
2025-05-11 13:51:42,823 - docker_compose_test - INFO - Checking container status...
2025-05-11 13:51:42,920 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 13:51:42,921 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 13:51:42,921 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 13:51:42,921 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 13:51:42,921 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 13:51:42,921 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=unhealthy
2025-05-11 13:51:42,921 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 13:51:42,921 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=unhealthy
2025-05-11 13:51:42,921 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 296s)
2025-05-11 13:51:47,925 - docker_compose_test - ERROR - Timeout waiting for services to be healthy
2025-05-11 13:51:47,926 - docker_compose_test - ERROR - Failed to wait for services to be healthy
2025-05-11 13:51:47,926 - docker_compose_test - INFO - Stopping services using Docker Compose...
2025-05-11 13:51:49,061 - docker_compose_test - INFO - Services stopped successfully
2025-05-11 13:51:49,061 - docker_compose_test - ERROR - Docker Compose test failed
2025-05-11 14:08:20,795 - docker_compose_test - INFO - Starting Docker Compose test...
2025-05-11 14:08:20,796 - docker_compose_test - INFO - Starting services using Docker Compose...
2025-05-11 14:08:42,216 - docker_compose_test - INFO - Services started successfully
2025-05-11 14:08:42,216 - docker_compose_test - INFO - Waiting for services to be healthy...
2025-05-11 14:08:42,216 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:08:42,333 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=starting
2025-05-11 14:08:42,334 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=running, Health=starting
2025-05-11 14:08:42,334 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=running, Health=starting
2025-05-11 14:08:42,334 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=running, Health=starting
2025-05-11 14:08:42,334 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:08:42,334 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 14:08:42,334 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:08:42,334 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 14:08:42,334 - docker_compose_test - INFO - Checking services health...
2025-05-11 14:08:42,352 - docker_compose_test - ERROR - Error checking Orchestrator service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:08:42,359 - docker_compose_test - ERROR - Error checking Repository Mapper service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:08:42,364 - docker_compose_test - ERROR - Error checking Domain Analyzer service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:08:42,368 - docker_compose_test - ERROR - Error checking File-Domain Mapper service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:08:42,370 - docker_compose_test - ERROR - Error checking Domain-File Repomap service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:08:42,373 - docker_compose_test - ERROR - Error checking Diagram Generator service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:08:42,377 - docker_compose_test - INFO - Prometheus service is healthy
2025-05-11 14:08:42,381 - docker_compose_test - ERROR - Error checking Grafana service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:08:42,381 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 0s)
2025-05-11 14:08:47,386 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:08:47,489 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=starting
2025-05-11 14:08:47,490 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:08:47,490 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=running, Health=
2025-05-11 14:08:47,490 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:08:47,490 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:08:47,490 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 14:08:47,490 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:08:47,490 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 14:08:47,490 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 5s)
2025-05-11 14:08:52,495 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:08:52,595 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:08:52,595 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:08:52,595 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:08:52,595 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:08:52,595 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:08:52,595 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:08:52,595 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:08:52,595 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:08:52,595 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 10s)
2025-05-11 14:08:57,597 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:08:57,761 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=
2025-05-11 14:08:57,761 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:08:57,762 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=running, Health=
2025-05-11 14:08:57,762 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:08:57,762 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:08:57,762 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 14:08:57,762 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:08:57,762 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=starting
2025-05-11 14:08:57,762 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 16s)
2025-05-11 14:09:02,762 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:09:02,864 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:09:02,864 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:09:02,864 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:09:02,864 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:09:02,864 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:09:02,864 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:09:02,864 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:09:02,864 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:09:02,864 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 21s)
2025-05-11 14:09:07,865 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:09:07,958 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:09:07,958 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:09:07,958 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:09:07,958 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:09:07,958 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:09:07,958 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:09:07,958 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:09:07,958 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:09:07,958 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 26s)
2025-05-11 14:09:12,960 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:09:13,056 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:09:13,056 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:09:13,056 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:09:13,056 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:09:13,056 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:09:13,056 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:09:13,057 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:09:13,057 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:09:13,057 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 31s)
2025-05-11 14:09:18,061 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:09:18,158 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:09:18,158 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:09:18,158 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:09:18,158 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:09:18,158 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:09:18,158 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:09:18,158 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:09:18,158 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:09:18,158 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 36s)
2025-05-11 14:09:23,162 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:09:23,232 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:09:23,232 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:09:23,232 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:09:23,232 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:09:23,232 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:09:23,232 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:09:23,232 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:09:23,232 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:09:23,232 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 41s)
2025-05-11 14:09:28,235 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:09:28,332 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:09:28,332 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:09:28,332 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:09:28,332 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:09:28,332 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:09:28,332 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:09:28,332 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:09:28,332 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:09:28,332 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 46s)
2025-05-11 14:09:33,337 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:09:33,436 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:09:33,436 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:09:33,436 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:09:33,436 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:09:33,436 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:09:33,437 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:09:33,437 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:09:33,437 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:09:33,437 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 51s)
2025-05-11 14:09:38,441 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:09:38,535 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:09:38,535 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:09:38,535 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:09:38,535 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:09:38,535 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:09:38,535 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:09:38,535 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:09:38,535 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:09:38,535 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 56s)
2025-05-11 14:09:43,537 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:09:43,636 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:09:43,636 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:09:43,636 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:09:43,636 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:09:43,636 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:09:43,636 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:09:43,636 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:09:43,636 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:09:43,636 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 61s)
2025-05-11 14:09:48,642 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:09:48,738 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:09:48,738 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:09:48,738 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:09:48,738 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:09:48,738 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:09:48,738 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:09:48,738 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:09:48,738 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:09:48,738 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 67s)
2025-05-11 14:09:53,744 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:09:53,839 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:09:53,839 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:09:53,839 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:09:53,839 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:09:53,839 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:09:53,839 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:09:53,839 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:09:53,839 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:09:53,839 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 72s)
2025-05-11 14:09:58,841 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:09:58,940 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:09:58,940 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:09:58,940 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:09:58,940 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:09:58,940 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:09:58,940 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:09:58,940 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:09:58,940 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:09:58,940 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 77s)
2025-05-11 14:10:03,942 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:10:04,041 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:10:04,041 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:10:04,041 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:10:04,041 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:10:04,041 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:10:04,041 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:10:04,041 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:10:04,041 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:10:04,041 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 82s)
2025-05-11 14:10:09,042 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:10:09,142 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:10:09,142 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:10:09,142 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:10:09,143 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:10:09,143 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:10:09,143 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:10:09,143 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:10:09,143 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:10:09,143 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 87s)
2025-05-11 14:10:14,148 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:10:14,247 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:10:14,247 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:10:14,247 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:10:14,247 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:10:14,247 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:10:14,247 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:10:14,247 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:10:14,247 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:10:14,247 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 92s)
2025-05-11 14:10:19,252 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:10:19,347 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:10:19,347 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:10:19,347 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:10:19,347 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:10:19,347 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:10:19,347 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:10:19,347 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:10:19,347 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:10:19,347 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 97s)
2025-05-11 14:10:24,351 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:10:24,454 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:10:24,454 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:10:24,454 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:10:24,454 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:10:24,454 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:10:24,454 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:10:24,454 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:10:24,454 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:10:24,454 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 102s)
2025-05-11 14:10:25,647 - docker_compose_test - INFO - Stopping services using Docker Compose...
2025-05-11 14:10:50,563 - docker_compose_test - INFO - Starting Docker Compose test...
2025-05-11 14:10:50,563 - docker_compose_test - INFO - Starting services using Docker Compose...
2025-05-11 14:10:51,223 - docker_compose_test - INFO - Services started successfully
2025-05-11 14:10:51,224 - docker_compose_test - INFO - Waiting for services to be healthy...
2025-05-11 14:10:51,224 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:10:51,341 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=starting
2025-05-11 14:10:51,341 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=running, Health=starting
2025-05-11 14:10:51,341 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=running, Health=starting
2025-05-11 14:10:51,342 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=running, Health=starting
2025-05-11 14:10:51,342 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:10:51,342 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 14:10:51,342 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:10:51,342 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 14:10:51,342 - docker_compose_test - INFO - Checking services health...
2025-05-11 14:10:51,352 - docker_compose_test - ERROR - Error checking Orchestrator service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:10:51,354 - docker_compose_test - ERROR - Error checking Repository Mapper service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:10:51,356 - docker_compose_test - ERROR - Error checking Domain Analyzer service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:10:51,359 - docker_compose_test - ERROR - Error checking File-Domain Mapper service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:10:51,361 - docker_compose_test - ERROR - Error checking Domain-File Repomap service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:10:51,364 - docker_compose_test - ERROR - Error checking Diagram Generator service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:10:51,368 - docker_compose_test - INFO - Prometheus service is healthy
2025-05-11 14:10:51,370 - docker_compose_test - ERROR - Error checking Grafana service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:10:51,370 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 0s)
2025-05-11 14:10:56,373 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:10:56,500 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=starting
2025-05-11 14:10:56,500 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:10:56,500 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=running, Health=starting
2025-05-11 14:10:56,500 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:10:56,500 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:10:56,500 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 14:10:56,500 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:10:56,500 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=starting
2025-05-11 14:10:56,500 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 5s)
2025-05-11 14:11:01,506 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:11:01,586 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:11:01,586 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:11:01,586 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:11:01,586 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:11:01,586 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:11:01,586 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:11:01,586 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:11:01,586 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:11:01,586 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 10s)
2025-05-11 14:11:06,590 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:11:06,750 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=starting
2025-05-11 14:11:06,750 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:11:06,750 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=running, Health=starting
2025-05-11 14:11:06,750 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:11:06,750 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:11:06,750 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 14:11:06,750 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:11:06,750 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=starting
2025-05-11 14:11:06,750 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 16s)
2025-05-11 14:11:11,751 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:11:11,839 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:11:11,840 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:11:11,840 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:11:11,840 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:11:11,840 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:11:11,840 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:11:11,840 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:11:11,840 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:11:11,840 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 21s)
2025-05-11 14:11:16,844 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:11:16,938 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:11:16,938 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:11:16,938 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:11:16,938 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:11:16,938 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:11:16,938 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:11:16,938 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:11:16,938 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:11:16,938 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 26s)
2025-05-11 14:11:21,943 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:11:22,015 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:11:22,015 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:11:22,015 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:11:22,015 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:11:22,015 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:11:22,015 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:11:22,015 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:11:22,015 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:11:22,015 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 31s)
2025-05-11 14:11:27,015 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:11:27,108 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:11:27,108 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:11:27,108 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:11:27,108 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:11:27,108 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:11:27,108 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:11:27,108 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:11:27,108 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:11:27,109 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 36s)
2025-05-11 14:11:32,114 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:11:32,217 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:11:32,217 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:11:32,217 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:11:32,217 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:11:32,217 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:11:32,218 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:11:32,218 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:11:32,218 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:11:32,218 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 41s)
2025-05-11 14:11:37,223 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:11:37,323 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:11:37,324 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:11:37,324 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:11:37,324 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:11:37,324 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:11:37,324 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:11:37,324 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:11:37,324 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:11:37,324 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 46s)
2025-05-11 14:11:42,324 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:11:42,418 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:11:42,418 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:11:42,418 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:11:42,418 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:11:42,418 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:11:42,418 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:11:42,418 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:11:42,418 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:11:42,418 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 51s)
2025-05-11 14:11:47,422 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:11:47,518 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:11:47,518 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:11:47,518 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:11:47,518 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:11:47,518 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:11:47,518 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:11:47,518 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:11:47,518 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:11:47,518 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 56s)
2025-05-11 14:11:50,651 - docker_compose_test - INFO - Stopping services using Docker Compose...
2025-05-11 14:12:41,336 - docker_compose_test - INFO - Starting Docker Compose test...
2025-05-11 14:12:41,336 - docker_compose_test - INFO - Starting services using Docker Compose...
2025-05-11 14:12:42,158 - docker_compose_test - INFO - Services started successfully
2025-05-11 14:12:42,159 - docker_compose_test - INFO - Waiting for services to be healthy...
2025-05-11 14:12:42,159 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:12:42,340 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=starting
2025-05-11 14:12:42,340 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=running, Health=starting
2025-05-11 14:12:42,340 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=running, Health=starting
2025-05-11 14:12:42,340 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=running, Health=starting
2025-05-11 14:12:42,341 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:12:42,341 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 14:12:42,341 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:12:42,341 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 14:12:42,341 - docker_compose_test - INFO - Checking services health...
2025-05-11 14:12:42,354 - docker_compose_test - ERROR - Error checking Orchestrator service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:12:42,368 - docker_compose_test - ERROR - Error checking Repository Mapper service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:12:42,371 - docker_compose_test - ERROR - Error checking Domain Analyzer service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:12:42,373 - docker_compose_test - ERROR - Error checking File-Domain Mapper service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:12:42,376 - docker_compose_test - ERROR - Error checking Domain-File Repomap service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:12:42,380 - docker_compose_test - ERROR - Error checking Diagram Generator service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:12:42,382 - docker_compose_test - INFO - Prometheus service is healthy
2025-05-11 14:12:42,384 - docker_compose_test - ERROR - Error checking Grafana service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:12:42,384 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 0s)
2025-05-11 14:12:47,389 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:12:47,499 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=starting
2025-05-11 14:12:47,500 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:12:47,500 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=running, Health=starting
2025-05-11 14:12:47,500 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:12:47,500 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:12:47,500 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 14:12:47,500 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:12:47,500 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 14:12:47,500 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 5s)
2025-05-11 14:12:52,505 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:12:52,606 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:12:52,606 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:12:52,606 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:12:52,606 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:12:52,606 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:12:52,606 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:12:52,606 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:12:52,606 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:12:52,606 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 10s)
2025-05-11 14:12:57,606 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:12:57,733 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=starting
2025-05-11 14:12:57,733 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:12:57,733 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=running, Health=starting
2025-05-11 14:12:57,733 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:12:57,733 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:12:57,733 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 14:12:57,733 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:12:57,733 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=starting
2025-05-11 14:12:57,733 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 16s)
2025-05-11 14:13:02,736 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:13:02,836 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:13:02,836 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:13:02,836 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:13:02,836 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:13:02,836 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:13:02,836 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:13:02,836 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:13:02,836 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:13:02,836 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 21s)
2025-05-11 14:13:07,838 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:13:07,913 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:13:07,913 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:13:07,913 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:13:07,913 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:13:07,913 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:13:07,913 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:13:07,913 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:13:07,913 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:13:07,913 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 26s)
2025-05-11 14:13:12,918 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:13:13,015 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:13:13,015 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:13:13,015 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:13:13,015 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:13:13,015 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:13:13,015 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:13:13,015 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:13:13,015 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:13:13,015 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 31s)
2025-05-11 14:13:18,018 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:13:18,099 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:13:18,099 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:13:18,099 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:13:18,099 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:13:18,099 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:13:18,099 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:13:18,099 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:13:18,099 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:13:18,099 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 36s)
2025-05-11 14:13:23,104 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:13:23,198 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:13:23,198 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:13:23,198 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:13:23,198 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:13:23,198 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:13:23,198 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:13:23,198 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:13:23,198 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:13:23,198 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 41s)
2025-05-11 14:13:28,203 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:13:28,296 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:13:28,297 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:13:28,297 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:13:28,297 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:13:28,297 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:13:28,297 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:13:28,297 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:13:28,297 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:13:28,297 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 46s)
2025-05-11 14:13:33,302 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:13:33,393 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:13:33,393 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:13:33,393 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:13:33,393 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:13:33,393 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:13:33,393 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:13:33,393 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:13:33,393 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:13:33,393 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 51s)
2025-05-11 14:13:36,124 - docker_compose_test - INFO - Stopping services using Docker Compose...
2025-05-11 14:13:36,747 - docker_compose_test - INFO - Services stopped successfully
2025-05-11 14:14:37,705 - docker_compose_test - INFO - Starting Docker Compose test...
2025-05-11 14:14:37,706 - docker_compose_test - INFO - Starting services using Docker Compose...
2025-05-11 14:14:38,452 - docker_compose_test - INFO - Services started successfully
2025-05-11 14:14:38,452 - docker_compose_test - INFO - Waiting for services to be healthy...
2025-05-11 14:14:38,452 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:14:38,586 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=starting
2025-05-11 14:14:38,586 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=running, Health=starting
2025-05-11 14:14:38,586 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=running, Health=starting
2025-05-11 14:14:38,587 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=running, Health=starting
2025-05-11 14:14:38,587 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:14:38,587 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 14:14:38,587 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:14:38,587 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 14:14:38,587 - docker_compose_test - INFO - Checking services health...
2025-05-11 14:14:38,600 - docker_compose_test - ERROR - Error checking Orchestrator service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:14:38,604 - docker_compose_test - ERROR - Error checking Repository Mapper service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:14:38,607 - docker_compose_test - ERROR - Error checking Domain Analyzer service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:14:38,610 - docker_compose_test - ERROR - Error checking File-Domain Mapper service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:14:38,613 - docker_compose_test - ERROR - Error checking Domain-File Repomap service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:14:38,616 - docker_compose_test - ERROR - Error checking Diagram Generator service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:14:38,635 - docker_compose_test - INFO - Prometheus service is healthy
2025-05-11 14:14:38,638 - docker_compose_test - ERROR - Error checking Grafana service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:14:38,638 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 0s)
2025-05-11 14:14:43,643 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:14:43,718 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=starting
2025-05-11 14:14:43,719 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:14:43,719 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=running, Health=starting
2025-05-11 14:14:43,719 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:14:43,719 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:14:43,719 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 14:14:43,719 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:14:43,719 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 14:14:43,719 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 5s)
2025-05-11 14:14:48,724 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:14:48,814 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:14:48,814 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:14:48,814 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:14:48,814 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:14:48,814 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:14:48,814 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:14:48,814 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:14:48,814 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:14:48,814 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 10s)
2025-05-11 14:14:53,818 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:14:53,892 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=starting
2025-05-11 14:14:53,892 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:14:53,892 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=running, Health=starting
2025-05-11 14:14:53,892 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:14:53,892 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:14:53,892 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 14:14:53,892 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:14:53,892 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 14:14:53,892 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 15s)
2025-05-11 14:14:58,893 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:14:58,978 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:14:58,978 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:14:58,978 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:14:58,978 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:14:58,978 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:14:58,978 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:14:58,978 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:14:58,978 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:14:58,978 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 21s)
2025-05-11 14:14:59,198 - docker_compose_test - INFO - Stopping services using Docker Compose...
2025-05-11 14:17:45,511 - docker_compose_test - INFO - Starting Docker Compose test...
2025-05-11 14:17:45,512 - docker_compose_test - INFO - Starting services using Docker Compose...
2025-05-11 14:17:46,187 - docker_compose_test - INFO - Services started successfully
2025-05-11 14:17:46,187 - docker_compose_test - INFO - Waiting for services to be healthy...
2025-05-11 14:17:46,187 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:17:46,308 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=starting
2025-05-11 14:17:46,309 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=running, Health=starting
2025-05-11 14:17:46,309 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=running, Health=starting
2025-05-11 14:17:46,309 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=running, Health=starting
2025-05-11 14:17:46,309 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:17:46,309 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 14:17:46,309 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:17:46,309 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 14:17:46,309 - docker_compose_test - INFO - Checking services health...
2025-05-11 14:17:46,317 - docker_compose_test - ERROR - Error checking Orchestrator service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:17:46,320 - docker_compose_test - ERROR - Error checking Repository Mapper service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:17:46,321 - docker_compose_test - ERROR - Error checking Domain Analyzer service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:17:46,323 - docker_compose_test - ERROR - Error checking File-Domain Mapper service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:17:46,324 - docker_compose_test - ERROR - Error checking Domain-File Repomap service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:17:46,325 - docker_compose_test - ERROR - Error checking Diagram Generator service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:17:46,328 - docker_compose_test - INFO - Prometheus service is healthy
2025-05-11 14:17:46,331 - docker_compose_test - ERROR - Error checking Grafana service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:17:46,331 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 0s)
2025-05-11 14:17:51,333 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:17:51,508 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=starting
2025-05-11 14:17:51,508 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:17:51,508 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=running, Health=
2025-05-11 14:17:51,508 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:17:51,508 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:17:51,508 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 14:17:51,508 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:17:51,508 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 14:17:51,508 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 5s)
2025-05-11 14:17:56,510 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:17:56,611 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:17:56,611 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:17:56,611 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:17:56,611 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:17:56,611 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:17:56,611 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:17:56,611 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:17:56,611 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:17:56,611 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 10s)
2025-05-11 14:18:01,616 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:18:01,765 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=
2025-05-11 14:18:01,765 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:18:01,765 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:18:01,765 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:18:01,765 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:18:01,765 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=
2025-05-11 14:18:01,765 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:18:01,765 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 14:18:01,765 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 16s)
2025-05-11 14:18:06,768 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:18:06,865 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:18:06,865 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:18:06,866 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:18:06,866 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:18:06,866 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:18:06,866 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:18:06,866 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:18:06,866 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:18:06,866 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 21s)
2025-05-11 14:18:08,541 - docker_compose_test - INFO - Stopping services using Docker Compose...
2025-05-11 14:18:31,529 - docker_compose_test - INFO - Starting Docker Compose test...
2025-05-11 14:18:31,529 - docker_compose_test - INFO - Starting services using Docker Compose...
2025-05-11 14:18:32,030 - docker_compose_test - INFO - Services started successfully
2025-05-11 14:18:32,030 - docker_compose_test - INFO - Waiting for services to be healthy...
2025-05-11 14:18:32,030 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:18:32,337 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=starting
2025-05-11 14:18:32,338 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=running, Health=starting
2025-05-11 14:18:32,338 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=running, Health=
2025-05-11 14:18:32,338 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=running, Health=starting
2025-05-11 14:18:32,338 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:18:32,338 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 14:18:32,338 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:18:32,338 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 14:18:32,338 - docker_compose_test - INFO - Checking services health...
2025-05-11 14:18:32,345 - docker_compose_test - ERROR - Error checking Orchestrator service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:18:32,347 - docker_compose_test - ERROR - Error checking Repository Mapper service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:18:32,348 - docker_compose_test - ERROR - Error checking Domain Analyzer service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:18:32,350 - docker_compose_test - ERROR - Error checking File-Domain Mapper service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:18:32,351 - docker_compose_test - ERROR - Error checking Domain-File Repomap service health: HTTPConnectionPool(host='localhost', port=8004): Max retries exceeded with url: /health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x102449550>: Failed to establish a new connection: [Errno 61] Connection refused'))
2025-05-11 14:18:32,352 - docker_compose_test - ERROR - Error checking Diagram Generator service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:18:32,354 - docker_compose_test - INFO - Prometheus service is healthy
2025-05-11 14:18:32,355 - docker_compose_test - ERROR - Error checking Grafana service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:18:32,355 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 0s)
2025-05-11 14:18:37,360 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:18:37,560 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:18:37,560 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:18:37,560 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=running, Health=
2025-05-11 14:18:37,560 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:18:37,560 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:18:37,560 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=
2025-05-11 14:18:37,560 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:18:37,560 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=
2025-05-11 14:18:37,560 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 6s)
2025-05-11 14:18:42,564 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:18:42,664 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:18:42,664 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:18:42,664 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:18:42,664 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:18:42,664 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:18:42,664 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:18:42,664 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:18:42,664 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:18:42,665 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 11s)
2025-05-11 14:18:47,668 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:18:47,752 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:18:47,753 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:18:47,753 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:18:47,753 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:18:47,753 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:18:47,753 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:18:47,753 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:18:47,753 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:18:47,753 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 16s)
2025-05-11 14:18:52,755 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:18:52,856 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:18:52,856 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:18:52,856 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:18:52,856 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:18:52,856 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:18:52,856 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:18:52,856 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:18:52,856 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:18:52,856 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 21s)
2025-05-11 14:18:57,861 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:18:57,960 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:18:57,960 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:18:57,960 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:18:57,960 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:18:57,960 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:18:57,960 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:18:57,960 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:18:57,960 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:18:57,960 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 26s)
2025-05-11 14:19:02,965 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:19:03,067 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:19:03,067 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:19:03,067 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:19:03,067 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:19:03,067 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:19:03,067 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:19:03,067 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:19:03,067 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:19:03,067 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 31s)
2025-05-11 14:19:08,070 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:19:08,153 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:19:08,153 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:19:08,153 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:19:08,154 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:19:08,154 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:19:08,154 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:19:08,154 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:19:08,154 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:19:08,154 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 36s)
2025-05-11 14:19:13,155 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:19:13,256 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:19:13,256 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:19:13,256 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:19:13,256 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:19:13,256 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:19:13,256 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:19:13,256 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:19:13,257 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:19:13,257 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 41s)
2025-05-11 14:19:18,261 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:19:18,364 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:19:18,364 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:19:18,364 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:19:18,364 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:19:18,364 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:19:18,364 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:19:18,364 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:19:18,364 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:19:18,364 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 46s)
2025-05-11 14:19:21,434 - docker_compose_test - INFO - Stopping services using Docker Compose...
2025-05-11 14:35:54,810 - docker_compose_test - INFO - Starting Docker Compose test...
2025-05-11 14:35:54,810 - docker_compose_test - INFO - Starting services using Docker Compose...
2025-05-11 14:35:55,946 - docker_compose_test - INFO - Services started successfully
2025-05-11 14:35:55,947 - docker_compose_test - INFO - Waiting for services to be healthy...
2025-05-11 14:35:55,947 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:35:56,084 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=starting
2025-05-11 14:35:56,084 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=running, Health=starting
2025-05-11 14:35:56,084 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=running, Health=starting
2025-05-11 14:35:56,084 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=running, Health=starting
2025-05-11 14:35:56,085 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:35:56,085 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 14:35:56,085 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:35:56,085 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 14:35:56,085 - docker_compose_test - INFO - Checking services health...
2025-05-11 14:35:56,096 - docker_compose_test - ERROR - Error checking Orchestrator service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:35:56,098 - docker_compose_test - ERROR - Error checking Repository Mapper service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:35:56,100 - docker_compose_test - ERROR - Error checking Domain Analyzer service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:35:56,102 - docker_compose_test - ERROR - Error checking File-Domain Mapper service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:35:56,104 - docker_compose_test - ERROR - Error checking Domain-File Repomap service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:35:56,106 - docker_compose_test - ERROR - Error checking Diagram Generator service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:35:56,110 - docker_compose_test - INFO - Prometheus service is healthy
2025-05-11 14:35:56,112 - docker_compose_test - ERROR - Error checking Grafana service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 14:35:56,112 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 0s)
2025-05-11 14:36:01,117 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:36:01,190 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:36:01,190 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:36:01,190 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:36:01,191 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:36:01,191 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:36:01,191 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:36:01,191 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:36:01,191 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:36:01,191 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 5s)
2025-05-11 14:36:06,194 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:36:06,481 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:36:06,481 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=running, Health=
2025-05-11 14:36:06,481 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:36:06,481 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=running, Health=starting
2025-05-11 14:36:06,481 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:36:06,481 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=
2025-05-11 14:36:06,481 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:36:06,481 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 14:36:06,482 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 11s)
2025-05-11 14:36:11,485 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:36:11,572 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:36:11,573 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:36:11,573 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:36:11,573 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:36:11,573 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:36:11,573 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:36:11,573 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:36:11,573 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:36:11,573 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 16s)
2025-05-11 14:36:16,578 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:36:16,652 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=restarting, Health=
2025-05-11 14:36:16,652 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=restarting, Health=
2025-05-11 14:36:16,652 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=restarting, Health=
2025-05-11 14:36:16,652 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=restarting, Health=
2025-05-11 14:36:16,652 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 14:36:16,652 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=restarting, Health=
2025-05-11 14:36:16,652 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 14:36:16,652 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=restarting, Health=
2025-05-11 14:36:16,652 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 21s)
2025-05-11 14:36:21,657 - docker_compose_test - INFO - Checking container status...
2025-05-11 14:36:21,753 - docker_compose_test - INFO - Stopping services using Docker Compose...
2025-05-11 15:54:38,704 - docker_compose_test - INFO - Starting Docker Compose test...
2025-05-11 15:54:38,704 - docker_compose_test - INFO - Starting services using Docker Compose...
2025-05-11 15:54:39,364 - docker_compose_test - INFO - Services started successfully
2025-05-11 15:54:39,364 - docker_compose_test - INFO - Waiting for services to be healthy...
2025-05-11 15:54:39,364 - docker_compose_test - INFO - Checking container status...
2025-05-11 15:54:39,472 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=starting
2025-05-11 15:54:39,472 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=running, Health=starting
2025-05-11 15:54:39,472 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=running, Health=starting
2025-05-11 15:54:39,472 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=running, Health=starting
2025-05-11 15:54:39,472 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 15:54:39,472 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 15:54:39,472 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 15:54:39,472 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 15:54:39,473 - docker_compose_test - INFO - Checking services health...
2025-05-11 15:54:39,489 - docker_compose_test - ERROR - Error checking Orchestrator service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 15:54:39,493 - docker_compose_test - ERROR - Error checking Repository Mapper service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 15:54:39,495 - docker_compose_test - ERROR - Error checking Domain Analyzer service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 15:54:39,498 - docker_compose_test - ERROR - Error checking File-Domain Mapper service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 15:54:39,501 - docker_compose_test - ERROR - Error checking Domain-File Repomap service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 15:54:39,503 - docker_compose_test - ERROR - Error checking Diagram Generator service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 15:54:39,507 - docker_compose_test - INFO - Prometheus service is healthy
2025-05-11 15:54:39,511 - docker_compose_test - ERROR - Error checking Grafana service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 15:54:39,511 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 0s)
2025-05-11 15:54:44,516 - docker_compose_test - INFO - Checking container status...
2025-05-11 15:54:44,589 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=healthy
2025-05-11 15:54:44,590 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=running, Health=starting
2025-05-11 15:54:44,590 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=running, Health=starting
2025-05-11 15:54:44,590 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=running, Health=healthy
2025-05-11 15:54:44,590 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 15:54:44,590 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=healthy
2025-05-11 15:54:44,590 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 15:54:44,590 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=healthy
2025-05-11 15:54:44,590 - docker_compose_test - INFO - Checking services health...
2025-05-11 15:54:44,593 - docker_compose_test - INFO - Orchestrator service is healthy
2025-05-11 15:54:44,596 - docker_compose_test - INFO - Repository Mapper service is healthy
2025-05-11 15:54:44,597 - docker_compose_test - ERROR - Error checking Domain Analyzer service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 15:54:44,599 - docker_compose_test - INFO - File-Domain Mapper service is healthy
2025-05-11 15:54:44,600 - docker_compose_test - ERROR - Error checking Domain-File Repomap service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 15:54:44,602 - docker_compose_test - INFO - Diagram Generator service is healthy
2025-05-11 15:54:44,604 - docker_compose_test - INFO - Prometheus service is healthy
2025-05-11 15:54:44,605 - docker_compose_test - INFO - Grafana service is healthy
2025-05-11 15:54:44,605 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 5s)
2025-05-11 15:54:49,609 - docker_compose_test - INFO - Checking container status...
2025-05-11 15:54:49,688 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=healthy
2025-05-11 15:54:49,688 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=running, Health=healthy
2025-05-11 15:54:49,688 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=running, Health=starting
2025-05-11 15:54:49,688 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=running, Health=healthy
2025-05-11 15:54:49,688 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 15:54:49,688 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=healthy
2025-05-11 15:54:49,688 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 15:54:49,688 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=healthy
2025-05-11 15:54:49,688 - docker_compose_test - INFO - Checking services health...
2025-05-11 15:54:49,691 - docker_compose_test - INFO - Orchestrator service is healthy
2025-05-11 15:54:49,694 - docker_compose_test - INFO - Repository Mapper service is healthy
2025-05-11 15:54:49,696 - docker_compose_test - INFO - Domain Analyzer service is healthy
2025-05-11 15:54:49,698 - docker_compose_test - INFO - File-Domain Mapper service is healthy
2025-05-11 15:54:49,700 - docker_compose_test - ERROR - Error checking Domain-File Repomap service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 15:54:49,702 - docker_compose_test - INFO - Diagram Generator service is healthy
2025-05-11 15:54:49,704 - docker_compose_test - INFO - Prometheus service is healthy
2025-05-11 15:54:49,706 - docker_compose_test - INFO - Grafana service is healthy
2025-05-11 15:54:49,706 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 10s)
2025-05-11 15:54:54,710 - docker_compose_test - INFO - Checking container status...
2025-05-11 15:54:54,786 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=healthy
2025-05-11 15:54:54,786 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=running, Health=healthy
2025-05-11 15:54:54,786 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=running, Health=starting
2025-05-11 15:54:54,786 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=running, Health=healthy
2025-05-11 15:54:54,786 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 15:54:54,786 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=healthy
2025-05-11 15:54:54,786 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 15:54:54,786 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=healthy
2025-05-11 15:54:54,786 - docker_compose_test - INFO - Checking services health...
2025-05-11 15:54:54,789 - docker_compose_test - INFO - Orchestrator service is healthy
2025-05-11 15:54:54,792 - docker_compose_test - INFO - Repository Mapper service is healthy
2025-05-11 15:54:54,794 - docker_compose_test - INFO - Domain Analyzer service is healthy
2025-05-11 15:54:54,797 - docker_compose_test - INFO - File-Domain Mapper service is healthy
2025-05-11 15:54:54,798 - docker_compose_test - ERROR - Error checking Domain-File Repomap service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 15:54:54,800 - docker_compose_test - INFO - Diagram Generator service is healthy
2025-05-11 15:54:54,802 - docker_compose_test - INFO - Prometheus service is healthy
2025-05-11 15:54:54,803 - docker_compose_test - INFO - Grafana service is healthy
2025-05-11 15:54:54,804 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 15s)
2025-05-11 15:54:59,807 - docker_compose_test - INFO - Checking container status...
2025-05-11 15:54:59,902 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=healthy
2025-05-11 15:54:59,902 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=running, Health=healthy
2025-05-11 15:54:59,902 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=running, Health=starting
2025-05-11 15:54:59,902 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=running, Health=healthy
2025-05-11 15:54:59,902 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 15:54:59,902 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=healthy
2025-05-11 15:54:59,902 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 15:54:59,902 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=healthy
2025-05-11 15:54:59,902 - docker_compose_test - INFO - Checking services health...
2025-05-11 15:54:59,906 - docker_compose_test - INFO - Orchestrator service is healthy
2025-05-11 15:54:59,909 - docker_compose_test - INFO - Repository Mapper service is healthy
2025-05-11 15:54:59,911 - docker_compose_test - INFO - Domain Analyzer service is healthy
2025-05-11 15:54:59,914 - docker_compose_test - INFO - File-Domain Mapper service is healthy
2025-05-11 15:54:59,916 - docker_compose_test - INFO - Domain-File Repomap service is healthy
2025-05-11 15:54:59,918 - docker_compose_test - INFO - Diagram Generator service is healthy
2025-05-11 15:54:59,920 - docker_compose_test - INFO - Prometheus service is healthy
2025-05-11 15:54:59,922 - docker_compose_test - INFO - Grafana service is healthy
2025-05-11 15:54:59,922 - docker_compose_test - INFO - All services are healthy
2025-05-11 15:54:59,922 - docker_compose_test - INFO - Checking container status...
2025-05-11 15:54:59,989 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=healthy
2025-05-11 15:54:59,989 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=running, Health=healthy
2025-05-11 15:54:59,989 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=running, Health=starting
2025-05-11 15:54:59,989 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=running, Health=healthy
2025-05-11 15:54:59,989 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 15:54:59,989 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=healthy
2025-05-11 15:54:59,989 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 15:54:59,989 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=healthy
2025-05-11 15:54:59,989 - docker_compose_test - INFO - Checking services health...
2025-05-11 15:54:59,992 - docker_compose_test - INFO - Orchestrator service is healthy
2025-05-11 15:54:59,994 - docker_compose_test - INFO - Repository Mapper service is healthy
2025-05-11 15:54:59,996 - docker_compose_test - INFO - Domain Analyzer service is healthy
2025-05-11 15:54:59,998 - docker_compose_test - INFO - File-Domain Mapper service is healthy
2025-05-11 15:55:00,000 - docker_compose_test - INFO - Domain-File Repomap service is healthy
2025-05-11 15:55:00,002 - docker_compose_test - INFO - Diagram Generator service is healthy
2025-05-11 15:55:00,004 - docker_compose_test - INFO - Prometheus service is healthy
2025-05-11 15:55:00,006 - docker_compose_test - INFO - Grafana service is healthy
2025-05-11 15:55:00,006 - docker_compose_test - INFO - Checking container resource usage...
2025-05-11 15:55:01,891 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: CPU=0.29%, Memory=42.89MiB / 31.29GiB, Network=8.01kB / 4.31kB, IO=0B / 381kB
2025-05-11 15:55:01,892 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: CPU=0.29%, Memory=84.86MiB / 31.29GiB, Network=1.78MB / 22.8kB, IO=0B / 2.08MB
2025-05-11 15:55:01,892 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: CPU=0.63%, Memory=40.76MiB / 31.29GiB, Network=9.2kB / 5.66kB, IO=0B / 348kB
2025-05-11 15:55:01,893 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: CPU=0.32%, Memory=40.36MiB / 31.29GiB, Network=7.51kB / 3.4kB, IO=0B / 291kB
2025-05-11 15:55:01,893 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: CPU=0.26%, Memory=83.62MiB / 31.29GiB, Network=1.77MB / 28.3kB, IO=0B / 176kB
2025-05-11 15:55:01,893 - docker_compose_test - INFO - Container microservices-grafana-1: CPU=0.05%, Memory=74.76MiB / 31.29GiB, Network=19.1kB / 5.38kB, IO=168kB / 81.9kB
2025-05-11 15:55:01,893 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: CPU=0.30%, Memory=42.71MiB / 31.29GiB, Network=8.15kB / 3.83kB, IO=0B / 291kB
2025-05-11 15:55:01,893 - docker_compose_test - INFO - Container microservices-prometheus-1: CPU=0.23%, Memory=24.9MiB / 31.29GiB, Network=15.3kB / 9.69kB, IO=799kB / 20.5kB
2025-05-11 15:55:01,894 - docker_compose_test - INFO - Docker Compose test completed successfully
2025-05-11 15:55:01,894 - docker_compose_test - INFO - Stopping services using Docker Compose...
2025-05-11 15:55:03,355 - docker_compose_test - INFO - Services stopped successfully
2025-05-11 15:55:03,356 - docker_compose_test - INFO - Docker Compose test passed
