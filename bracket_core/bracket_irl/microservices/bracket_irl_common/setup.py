from setuptools import setup, find_packages

setup(
    name="bracket_irl_common",
    version="0.1.0",
    packages=find_packages(),
    install_requires=[
        "pydantic>=2.0.0",
        "fastapi>=0.100.0",
        "uvicorn>=0.22.0",
        "aiohttp>=3.8.4",
        "tiktoken>=0.4.0",
        "pyyaml>=6.0",
        "prometheus-client>=0.16.0",
        "psutil>=5.9.0",
    ],
    description="Common utilities for Bracket IRL microservices",
    author="Bracket Team",
)
