"""
Centralized logging for Bracket IRL microservices.
"""

import os
import sys
import logging
import json
from datetime import datetime
from typing import Dict, Any, Optional

class ServiceLogger:
    """Centralized logger for Bracket IRL microservices."""
    
    def __init__(
        self,
        service_name: str,
        log_level: str = "INFO",
        log_format: Optional[str] = None,
        log_file: Optional[str] = None,
        json_logs: bool = False
    ):
        """
        Initialize the service logger.
        
        Args:
            service_name: Name of the service
            log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            log_format: Custom log format (if None, a default format is used)
            log_file: Path to log file (if None, logs are written to stdout)
            json_logs: Whether to output logs in JSON format
        """
        self.service_name = service_name
        self.log_level = self._get_log_level(log_level)
        self.json_logs = json_logs
        
        # Create logger
        self.logger = logging.getLogger(service_name)
        self.logger.setLevel(self.log_level)
        self.logger.handlers = []  # Remove existing handlers
        
        # Create formatter
        if log_format is None:
            if json_logs:
                log_format = "%(message)s"
            else:
                log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        
        formatter = logging.Formatter(log_format)
        
        # Create handlers
        if log_file:
            os.makedirs(os.path.dirname(log_file), exist_ok=True)
            file_handler = logging.FileHandler(log_file)
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
        
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
    
    def _get_log_level(self, log_level: str) -> int:
        """Convert string log level to logging level."""
        return getattr(logging, log_level.upper(), logging.INFO)
    
    def _format_json_log(self, level: str, message: str, **kwargs) -> str:
        """Format log message as JSON."""
        log_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "service": self.service_name,
            "level": level,
            "message": message
        }
        
        if kwargs:
            log_data["data"] = kwargs
        
        return json.dumps(log_data)
    
    def debug(self, message: str, **kwargs) -> None:
        """Log debug message."""
        if self.json_logs:
            message = self._format_json_log("DEBUG", message, **kwargs)
            self.logger.debug(message)
        else:
            if kwargs:
                message = f"{message} - {json.dumps(kwargs)}"
            self.logger.debug(message)
    
    def info(self, message: str, **kwargs) -> None:
        """Log info message."""
        if self.json_logs:
            message = self._format_json_log("INFO", message, **kwargs)
            self.logger.info(message)
        else:
            if kwargs:
                message = f"{message} - {json.dumps(kwargs)}"
            self.logger.info(message)
    
    def warning(self, message: str, **kwargs) -> None:
        """Log warning message."""
        if self.json_logs:
            message = self._format_json_log("WARNING", message, **kwargs)
            self.logger.warning(message)
        else:
            if kwargs:
                message = f"{message} - {json.dumps(kwargs)}"
            self.logger.warning(message)
    
    def error(self, message: str, **kwargs) -> None:
        """Log error message."""
        if self.json_logs:
            message = self._format_json_log("ERROR", message, **kwargs)
            self.logger.error(message)
        else:
            if kwargs:
                message = f"{message} - {json.dumps(kwargs)}"
            self.logger.error(message)
    
    def critical(self, message: str, **kwargs) -> None:
        """Log critical message."""
        if self.json_logs:
            message = self._format_json_log("CRITICAL", message, **kwargs)
            self.logger.critical(message)
        else:
            if kwargs:
                message = f"{message} - {json.dumps(kwargs)}"
            self.logger.critical(message)
    
    def exception(self, message: str, **kwargs) -> None:
        """Log exception message with traceback."""
        if self.json_logs:
            message = self._format_json_log("ERROR", message, **kwargs)
            self.logger.exception(message)
        else:
            if kwargs:
                message = f"{message} - {json.dumps(kwargs)}"
            self.logger.exception(message)
