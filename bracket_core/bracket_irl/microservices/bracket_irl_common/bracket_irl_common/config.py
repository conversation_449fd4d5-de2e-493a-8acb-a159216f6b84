"""
Configuration management for Bracket IRL microservices.
"""

import os
import yaml
import json
from typing import Dict, Any, Optional, List, Union
from pydantic import BaseModel, Field

class Config(BaseModel):
    """Base configuration class for Bracket IRL microservices."""
    
    # Service configuration
    service_name: str = Field(..., description="Name of the service")
    host: str = Field("0.0.0.0", description="Host to bind the service to")
    port: int = Field(8000, description="Port to bind the service to")
    log_level: str = Field("INFO", description="Logging level")
    
    # API configuration
    api_prefix: str = Field("/api/v1", description="API prefix")
    
    # Storage configuration
    storage_type: str = Field("local", description="Storage type (local, s3, gcs)")
    storage_path: str = Field("./data", description="Path for local storage")
    
    # LLM configuration
    llm_provider: str = Field("openai", description="LLM provider (openai, openrouter, anthropic)")
    llm_model: str = Field("gpt-4o-mini", description="LLM model to use")
    llm_api_key: Optional[str] = Field(None, description="API key for LLM provider")
    llm_base_url: Optional[str] = Field(None, description="Base URL for LLM provider")
    
    # Rate limiting
    max_requests_per_minute: float = Field(5000, description="Maximum requests per minute")
    max_tokens_per_minute: float = Field(10000000, description="Maximum tokens per minute")
    
    # Job processing
    max_concurrent_tasks: int = Field(10, description="Maximum concurrent tasks")
    job_timeout_seconds: int = Field(3600, description="Job timeout in seconds")
    
    # Additional service-specific configuration
    additional_config: Dict[str, Any] = Field(default_factory=dict, description="Additional service-specific configuration")
    
    @classmethod
    def from_file(cls, file_path: str) -> "Config":
        """Load configuration from a file."""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Configuration file not found: {file_path}")
        
        with open(file_path, "r") as f:
            if file_path.endswith(".yaml") or file_path.endswith(".yml"):
                config_data = yaml.safe_load(f)
            elif file_path.endswith(".json"):
                config_data = json.load(f)
            else:
                raise ValueError(f"Unsupported configuration file format: {file_path}")
        
        return cls(**config_data)
    
    @classmethod
    def from_env(cls, prefix: str = "BRACKET_") -> "Config":
        """Load configuration from environment variables."""
        config_data = {}
        
        for key, value in os.environ.items():
            if key.startswith(prefix):
                config_key = key[len(prefix):].lower()
                
                # Convert string to appropriate type
                if value.lower() in ("true", "false"):
                    config_data[config_key] = value.lower() == "true"
                elif value.isdigit():
                    config_data[config_key] = int(value)
                elif value.replace(".", "", 1).isdigit() and value.count(".") == 1:
                    config_data[config_key] = float(value)
                else:
                    config_data[config_key] = value
        
        return cls(**config_data)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return self.model_dump()
    
    def save(self, file_path: str) -> None:
        """Save configuration to a file."""
        with open(file_path, "w") as f:
            if file_path.endswith(".yaml") or file_path.endswith(".yml"):
                yaml.dump(self.to_dict(), f, default_flow_style=False)
            elif file_path.endswith(".json"):
                json.dump(self.to_dict(), f, indent=2)
            else:
                raise ValueError(f"Unsupported configuration file format: {file_path}")
