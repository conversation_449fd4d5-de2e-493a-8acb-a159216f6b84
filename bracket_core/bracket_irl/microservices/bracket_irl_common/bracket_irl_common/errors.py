"""
Error handling utilities for Bracket IRL microservices.

This module provides utilities for standardized error handling in microservices.
"""

import sys
import traceback
import logging
from enum import Enum
from typing import Dict, List, Any, Optional, Type, Union, Callable
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)

class ErrorCode(str, Enum):
    """Error code enum."""
    # General errors
    UNKNOWN_ERROR = "UNKNOWN_ERROR"
    VALIDATION_ERROR = "VALIDATION_ERROR"
    CONFIGURATION_ERROR = "CONFIGURATION_ERROR"
    AUTHENTICATION_ERROR = "AUTHENTICATION_ERROR"
    AUTHORIZATION_ERROR = "AUTHORIZATION_ERROR"
    RESOURCE_NOT_FOUND = "RESOURCE_NOT_FOUND"
    RESOURCE_ALREADY_EXISTS = "RESOURCE_ALREADY_EXISTS"
    RESOURCE_CONFLICT = "RESOURCE_CONFLICT"
    
    # Service-specific errors
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE"
    SERVICE_TIMEOUT = "SERVICE_TIMEOUT"
    SERVICE_ERROR = "SERVICE_ERROR"
    
    # Storage errors
    STORAGE_ERROR = "STORAGE_ERROR"
    FILE_NOT_FOUND = "FILE_NOT_FOUND"
    FILE_ALREADY_EXISTS = "FILE_ALREADY_EXISTS"
    FILE_ACCESS_ERROR = "FILE_ACCESS_ERROR"
    
    # LLM errors
    LLM_API_ERROR = "LLM_API_ERROR"
    LLM_RATE_LIMIT_ERROR = "LLM_RATE_LIMIT_ERROR"
    LLM_TIMEOUT_ERROR = "LLM_TIMEOUT_ERROR"
    LLM_CONTEXT_LENGTH_ERROR = "LLM_CONTEXT_LENGTH_ERROR"
    
    # Job errors
    JOB_NOT_FOUND = "JOB_NOT_FOUND"
    JOB_ALREADY_EXISTS = "JOB_ALREADY_EXISTS"
    JOB_FAILED = "JOB_FAILED"
    JOB_TIMEOUT = "JOB_TIMEOUT"
    
    # Repository errors
    REPOSITORY_NOT_FOUND = "REPOSITORY_NOT_FOUND"
    REPOSITORY_ACCESS_ERROR = "REPOSITORY_ACCESS_ERROR"
    
    # Domain analysis errors
    DOMAIN_ANALYSIS_ERROR = "DOMAIN_ANALYSIS_ERROR"
    
    # File-domain mapping errors
    FILE_DOMAIN_MAPPING_ERROR = "FILE_DOMAIN_MAPPING_ERROR"
    
    # Domain-file repomap errors
    DOMAIN_FILE_REPOMAP_ERROR = "DOMAIN_FILE_REPOMAP_ERROR"
    
    # Diagram generation errors
    DIAGRAM_GENERATION_ERROR = "DIAGRAM_GENERATION_ERROR"

class ServiceError(Exception):
    """Base exception for service errors."""
    
    def __init__(
        self,
        message: str,
        code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize service error.
        
        Args:
            message: Error message
            code: Error code
            status_code: HTTP status code
            details: Additional error details
        """
        self.message = message
        self.code = code
        self.status_code = status_code
        self.details = details or {}
        super().__init__(message)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert error to dictionary."""
        return {
            "code": self.code,
            "message": self.message,
            "details": self.details
        }

class ValidationError(ServiceError):
    """Validation error."""
    
    def __init__(
        self,
        message: str,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize validation error.
        
        Args:
            message: Error message
            details: Additional error details
        """
        super().__init__(
            message=message,
            code=ErrorCode.VALIDATION_ERROR,
            status_code=400,
            details=details
        )

class ResourceNotFoundError(ServiceError):
    """Resource not found error."""
    
    def __init__(
        self,
        message: str,
        resource_type: str,
        resource_id: str
    ):
        """
        Initialize resource not found error.
        
        Args:
            message: Error message
            resource_type: Type of resource
            resource_id: ID of resource
        """
        super().__init__(
            message=message,
            code=ErrorCode.RESOURCE_NOT_FOUND,
            status_code=404,
            details={
                "resource_type": resource_type,
                "resource_id": resource_id
            }
        )

class JobNotFoundError(ResourceNotFoundError):
    """Job not found error."""
    
    def __init__(self, job_id: str):
        """
        Initialize job not found error.
        
        Args:
            job_id: Job ID
        """
        super().__init__(
            message=f"Job not found: {job_id}",
            resource_type="job",
            resource_id=job_id
        )

class ArtifactNotFoundError(ResourceNotFoundError):
    """Artifact not found error."""
    
    def __init__(self, artifact_id: str):
        """
        Initialize artifact not found error.
        
        Args:
            artifact_id: Artifact ID
        """
        super().__init__(
            message=f"Artifact not found: {artifact_id}",
            resource_type="artifact",
            resource_id=artifact_id
        )

class FileNotFoundError(ServiceError):
    """File not found error."""
    
    def __init__(self, file_path: str):
        """
        Initialize file not found error.
        
        Args:
            file_path: File path
        """
        super().__init__(
            message=f"File not found: {file_path}",
            code=ErrorCode.FILE_NOT_FOUND,
            status_code=404,
            details={
                "file_path": file_path
            }
        )

class LLMAPIError(ServiceError):
    """LLM API error."""
    
    def __init__(
        self,
        message: str,
        provider: str,
        model: str,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize LLM API error.
        
        Args:
            message: Error message
            provider: LLM provider
            model: LLM model
            details: Additional error details
        """
        error_details = {
            "provider": provider,
            "model": model
        }
        if details:
            error_details.update(details)
        
        super().__init__(
            message=message,
            code=ErrorCode.LLM_API_ERROR,
            status_code=500,
            details=error_details
        )

class ServiceUnavailableError(ServiceError):
    """Service unavailable error."""
    
    def __init__(
        self,
        message: str,
        service_name: str,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize service unavailable error.
        
        Args:
            message: Error message
            service_name: Name of service
            details: Additional error details
        """
        error_details = {
            "service_name": service_name
        }
        if details:
            error_details.update(details)
        
        super().__init__(
            message=message,
            code=ErrorCode.SERVICE_UNAVAILABLE,
            status_code=503,
            details=error_details
        )

def handle_exception(e: Exception) -> Dict[str, Any]:
    """
    Handle exception and return standardized error response.
    
    Args:
        e: Exception to handle
    
    Returns:
        Standardized error response
    """
    if isinstance(e, ServiceError):
        logger.error(f"Service error: {e.message}", exc_info=True)
        return {
            "error": e.to_dict(),
            "status_code": e.status_code
        }
    else:
        logger.error(f"Unhandled exception: {str(e)}", exc_info=True)
        return {
            "error": {
                "code": ErrorCode.UNKNOWN_ERROR,
                "message": str(e),
                "details": {
                    "traceback": traceback.format_exc()
                }
            },
            "status_code": 500
        }

def error_handler(func: Callable) -> Callable:
    """
    Decorator to handle exceptions in route handlers.
    
    Args:
        func: Function to decorate
    
    Returns:
        Decorated function
    """
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            error_response = handle_exception(e)
            from fastapi import HTTPException
            raise HTTPException(
                status_code=error_response["status_code"],
                detail=error_response["error"]
            )
    
    return wrapper
