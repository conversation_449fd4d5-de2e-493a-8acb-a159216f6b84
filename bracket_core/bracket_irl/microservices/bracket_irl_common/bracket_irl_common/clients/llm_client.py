"""
LLM client interface for Bracket IRL microservices.
"""

import os
import json
import aiohttp
import logging
import asyncio
from typing import Dict, List, Any, Optional, Union, Tuple
from abc import ABC, abstractmethod

from bracket_irl_common.utils.rate_limiter import AsyncRateLimiter, AsyncTokenBucketRateLimiter
from bracket_irl_common.utils.token_counter import num_tokens_from_string, num_tokens_from_messages

logger = logging.getLogger(__name__)

class LLMClient(ABC):
    """Abstract LLM client interface."""

    @abstractmethod
    async def generate(self, prompt: str, **kwargs) -> Tuple[str, Dict[str, Any]]:
        """
        Generate text from prompt.

        Args:
            prompt: Prompt text
            **kwargs: Additional arguments for generation

        Returns:
            Tuple of (generated text, metadata)
        """
        pass

    @abstractmethod
    async def chat(self, messages: List[Dict[str, str]], **kwargs) -> <PERSON><PERSON>[str, Dict[str, Any]]:
        """
        Generate chat response from messages.

        Args:
            messages: List of messages
            **kwargs: Additional arguments for generation

        Returns:
            Tuple of (generated text, metadata)
        """
        pass

class OpenAIClient(LLMClient):
    """OpenAI API client."""

    def __init__(
        self,
        api_key: Optional[str] = None,
        model: str = "gpt-4o-mini",
        base_url: str = "https://api.openai.com/v1",
        max_requests_per_minute: float = 5000,
        max_tokens_per_minute: float = 10000000,
        timeout: float = 120.0,
    ):
        """
        Initialize OpenAI client.

        Args:
            api_key: OpenAI API key (if None, uses OPENAI_API_KEY environment variable)
            model: Model to use
            base_url: Base URL for API
            max_requests_per_minute: Maximum requests per minute
            max_tokens_per_minute: Maximum tokens per minute
            timeout: Timeout for API calls in seconds
        """
        self.api_key = api_key or os.environ.get("OPENAI_API_KEY")
        if not self.api_key:
            raise ValueError("OpenAI API key is required")

        self.model = model
        self.base_url = base_url
        self.timeout = timeout

        # Initialize rate limiters
        self.request_limiter = AsyncRateLimiter(max_requests_per_minute / 60.0)
        self.token_limiter = AsyncTokenBucketRateLimiter(max_tokens_per_minute, max_tokens_per_minute / 60.0)

    async def generate(self, prompt: str, **kwargs) -> Tuple[str, Dict[str, Any]]:
        """
        Generate text from prompt.

        Args:
            prompt: Prompt text
            **kwargs: Additional arguments for generation

        Returns:
            Tuple of (generated text, metadata)
        """
        messages = [{"role": "user", "content": prompt}]
        return await self.chat(messages, **kwargs)

    async def chat(self, messages: List[Dict[str, str]], **kwargs) -> Tuple[str, Dict[str, Any]]:
        """
        Generate chat response from messages.

        Args:
            messages: List of messages
            **kwargs: Additional arguments for generation

        Returns:
            Tuple of (generated text, metadata)
        """
        # Count tokens
        input_tokens = num_tokens_from_messages(messages, self.model)

        # Acquire rate limiting permissions
        await self.request_limiter.acquire()
        await self.token_limiter.acquire(input_tokens)

        # Prepare request
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }

        data = {
            "model": self.model,
            "messages": messages,
            "temperature": kwargs.get("temperature", 0.7),
            "max_tokens": kwargs.get("max_tokens", 1000),
            "top_p": kwargs.get("top_p", 1.0),
            "frequency_penalty": kwargs.get("frequency_penalty", 0.0),
            "presence_penalty": kwargs.get("presence_penalty", 0.0),
        }

        # Add reasoning parameter for models that start with 'o'
        if self.model.startswith("o"):
            data["reasoning"] = {"effort": "low"}

        # Determine endpoint
        if self.model.startswith("o"):
            endpoint = "/v1/responses"
        else:
            endpoint = "/v1/chat/completions"

        url = f"{self.base_url}{endpoint}"

        # Make request
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    url,
                    headers=headers,
                    json=data,
                    timeout=self.timeout
                ) as response:
                    response_data = await response.json()

                    if response.status != 200:
                        error_message = response_data.get("error", {}).get("message", "Unknown error")
                        raise Exception(f"OpenAI API error: {error_message}")

                    # Extract response text
                    if endpoint == "/v1/responses":
                        text = response_data.get("content", "")
                    else:
                        text = response_data.get("choices", [{}])[0].get("message", {}).get("content", "")

                    # Count output tokens
                    output_tokens = num_tokens_from_string(text, self.model)
                    await self.token_limiter.acquire(output_tokens)

                    # Prepare metadata
                    metadata = {
                        "model": self.model,
                        "input_tokens": input_tokens,
                        "output_tokens": output_tokens,
                        "total_tokens": input_tokens + output_tokens,
                    }

                    return text, metadata
        except Exception as e:
            logger.error(f"Error calling OpenAI API: {e}")
            raise

class OpenRouterClient(LLMClient):
    """OpenRouter API client."""

    def __init__(
        self,
        api_key: Optional[str] = None,
        model: str = "google/gemini-2.0-flash-001",
        base_url: str = "https://openrouter.ai/api/v1",
        max_requests_per_minute: float = 60,
        max_tokens_per_minute: float = 100000,
        timeout: float = 120.0,
    ):
        """
        Initialize OpenRouter client.

        Args:
            api_key: OpenRouter API key (if None, uses OPENROUTER_API_KEY environment variable)
            model: Model to use
            base_url: Base URL for API
            max_requests_per_minute: Maximum requests per minute
            max_tokens_per_minute: Maximum tokens per minute
            timeout: Timeout for API calls in seconds
        """
        self.api_key = api_key or os.environ.get("OPENROUTER_API_KEY")
        if not self.api_key:
            raise ValueError("OpenRouter API key is required")

        self.model = model
        self.base_url = base_url
        self.timeout = timeout

        # Initialize rate limiters
        self.request_limiter = AsyncRateLimiter(max_requests_per_minute / 60.0)
        self.token_limiter = AsyncTokenBucketRateLimiter(max_tokens_per_minute, max_tokens_per_minute / 60.0)

    async def generate(self, prompt: str, **kwargs) -> Tuple[str, Dict[str, Any]]:
        """
        Generate text from prompt.

        Args:
            prompt: Prompt text
            **kwargs: Additional arguments for generation

        Returns:
            Tuple of (generated text, metadata)
        """
        messages = [{"role": "user", "content": prompt}]
        return await self.chat(messages, **kwargs)

    async def chat(self, messages: List[Dict[str, str]], **kwargs) -> Tuple[str, Dict[str, Any]]:
        """
        Generate chat response from messages.

        Args:
            messages: List of messages
            **kwargs: Additional arguments for generation

        Returns:
            Tuple of (generated text, metadata)
        """
        # Count tokens
        input_tokens = num_tokens_from_messages(messages, "gpt-4o-mini")  # Use gpt-4o-mini as proxy

        # Acquire rate limiting permissions
        await self.request_limiter.acquire()
        await self.token_limiter.acquire(input_tokens)

        # Prepare request
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
            "HTTP-Referer": "https://bracket.ai",  # Required by OpenRouter
            "X-Title": "Bracket IRL"  # Required by OpenRouter
        }

        data = {
            "model": self.model,
            "messages": messages,
            "temperature": kwargs.get("temperature", 0.7),
            "max_tokens": kwargs.get("max_tokens", 1000),
            "top_p": kwargs.get("top_p", 1.0),
            "frequency_penalty": kwargs.get("frequency_penalty", 0.0),
            "presence_penalty": kwargs.get("presence_penalty", 0.0),
        }

        url = f"{self.base_url}/chat/completions"

        # Make request
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    url,
                    headers=headers,
                    json=data,
                    timeout=self.timeout
                ) as response:
                    response_data = await response.json()

                    if response.status != 200:
                        error_message = response_data.get("error", {}).get("message", "Unknown error")
                        raise Exception(f"OpenRouter API error: {error_message}")

                    # Extract response text
                    text = response_data.get("choices", [{}])[0].get("message", {}).get("content", "")

                    # Count output tokens
                    output_tokens = num_tokens_from_string(text, "gpt-4o-mini")  # Use gpt-4o-mini as proxy
                    await self.token_limiter.acquire(output_tokens)

                    # Prepare metadata
                    metadata = {
                        "model": self.model,
                        "input_tokens": input_tokens,
                        "output_tokens": output_tokens,
                        "total_tokens": input_tokens + output_tokens,
                    }

                    return text, metadata
        except Exception as e:
            logger.error(f"Error calling OpenRouter API: {e}")
            raise

# Factory function to create LLM client
def get_llm_client(
    provider: str = "openai",
    api_key: Optional[str] = None,
    model: Optional[str] = None,
    base_url: Optional[str] = None,
    max_requests_per_minute: float = 5000,
    max_tokens_per_minute: float = 10000000,
) -> LLMClient:
    """
    Create LLM client based on provider.

    Args:
        provider: Provider type (openai, openrouter)
        api_key: API key
        model: Model to use
        base_url: Base URL for API
        max_requests_per_minute: Maximum requests per minute
        max_tokens_per_minute: Maximum tokens per minute

    Returns:
        LLM client instance
    """
    if provider == "openai":
        return OpenAIClient(
            api_key=api_key,
            model=model or "gpt-4o-mini",
            base_url=base_url or "https://api.openai.com/v1",
            max_requests_per_minute=max_requests_per_minute,
            max_tokens_per_minute=max_tokens_per_minute,
        )
    elif provider == "openrouter":
        return OpenRouterClient(
            api_key=api_key,
            model=model or "google/gemini-2.0-flash-001",
            base_url=base_url or "https://openrouter.ai/api/v1",
            max_requests_per_minute=60,  # OpenRouter has lower rate limits
            max_tokens_per_minute=100000,  # OpenRouter has lower rate limits
        )
    else:
        raise ValueError(f"Unsupported LLM provider: {provider}")
