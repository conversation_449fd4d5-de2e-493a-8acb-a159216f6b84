"""
Metrics collection for Bracket IRL microservices.
"""

import time
import threading
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field

@dataclass
class Metric:
    """Base metric class."""
    name: str
    description: str
    labels: Dict[str, str] = field(default_factory=dict)

@dataclass
class Counter(Metric):
    """Counter metric that can only increase."""
    value: float = 0.0
    
    def inc(self, amount: float = 1.0) -> None:
        """Increment counter by amount."""
        if amount < 0:
            raise ValueError("Counter can only be incremented by non-negative values")
        self.value += amount

@dataclass
class Gauge(Metric):
    """Gauge metric that can increase and decrease."""
    value: float = 0.0
    
    def inc(self, amount: float = 1.0) -> None:
        """Increment gauge by amount."""
        self.value += amount
    
    def dec(self, amount: float = 1.0) -> None:
        """Decrement gauge by amount."""
        self.value -= amount
    
    def set(self, value: float) -> None:
        """Set gauge to value."""
        self.value = value

@dataclass
class Histogram(Metric):
    """Histogram metric that tracks distribution of values."""
    buckets: List[float] = field(default_factory=lambda: [0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10])
    values: Dict[float, int] = field(default_factory=dict)
    sum: float = 0.0
    count: int = 0
    
    def __post_init__(self):
        """Initialize buckets."""
        self.values = {bucket: 0 for bucket in self.buckets}
        self.values[float("inf")] = 0
    
    def observe(self, value: float) -> None:
        """Observe value."""
        self.sum += value
        self.count += 1
        
        for bucket in self.buckets + [float("inf")]:
            if value <= bucket:
                self.values[bucket] += 1

class Timer:
    """Timer for measuring execution time."""
    
    def __init__(self, histogram: Histogram):
        """
        Initialize timer.
        
        Args:
            histogram: Histogram to record execution time
        """
        self.histogram = histogram
        self.start_time = None
    
    def __enter__(self):
        """Start timer."""
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Stop timer and record execution time."""
        if self.start_time is not None:
            execution_time = time.time() - self.start_time
            self.histogram.observe(execution_time)

class MetricsRegistry:
    """Registry for metrics."""
    
    def __init__(self):
        """Initialize metrics registry."""
        self.metrics: Dict[str, Metric] = {}
        self._lock = threading.Lock()
    
    def register(self, metric: Metric) -> Metric:
        """Register metric."""
        with self._lock:
            if metric.name in self.metrics:
                raise ValueError(f"Metric {metric.name} already registered")
            self.metrics[metric.name] = metric
        return metric
    
    def unregister(self, name: str) -> None:
        """Unregister metric."""
        with self._lock:
            if name in self.metrics:
                del self.metrics[name]
    
    def get(self, name: str) -> Optional[Metric]:
        """Get metric by name."""
        return self.metrics.get(name)
    
    def get_all(self) -> Dict[str, Metric]:
        """Get all metrics."""
        return self.metrics.copy()
    
    def clear(self) -> None:
        """Clear all metrics."""
        with self._lock:
            self.metrics.clear()

# Global metrics registry
_registry = MetricsRegistry()

def get_registry() -> MetricsRegistry:
    """Get global metrics registry."""
    return _registry

def counter(name: str, description: str, labels: Optional[Dict[str, str]] = None) -> Counter:
    """Create and register counter metric."""
    counter_metric = Counter(name=name, description=description, labels=labels or {})
    return _registry.register(counter_metric)

def gauge(name: str, description: str, labels: Optional[Dict[str, str]] = None) -> Gauge:
    """Create and register gauge metric."""
    gauge_metric = Gauge(name=name, description=description, labels=labels or {})
    return _registry.register(gauge_metric)

def histogram(name: str, description: str, buckets: Optional[List[float]] = None, 
              labels: Optional[Dict[str, str]] = None) -> Histogram:
    """Create and register histogram metric."""
    histogram_metric = Histogram(name=name, description=description, 
                                buckets=buckets or [0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10],
                                labels=labels or {})
    return _registry.register(histogram_metric)

def timer(name: str, description: str, buckets: Optional[List[float]] = None,
          labels: Optional[Dict[str, str]] = None) -> Callable:
    """Create timer decorator."""
    timer_histogram = histogram(name, description, buckets, labels)
    
    def decorator(func):
        def wrapper(*args, **kwargs):
            with Timer(timer_histogram):
                return func(*args, **kwargs)
        return wrapper
    
    return decorator
