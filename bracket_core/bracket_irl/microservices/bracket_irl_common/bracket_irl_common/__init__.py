"""
Bracket IRL Common

This package provides common utilities and shared components for the Bracket IRL microservices architecture.
"""

__version__ = "0.1.0"

# Import submodules for easier access
from bracket_irl_common.config import Config
from bracket_irl_common.logging import ServiceLogger
from bracket_irl_common.storage import StorageClient, get_storage_client
from bracket_irl_common.metrics import MetricsRegistry, get_registry, counter, gauge, histogram, timer
from bracket_irl_common.health import HealthStatus, HealthCheck, HealthCheckResult, HealthCheckService
from bracket_irl_common.errors import (
    ErrorCode, ServiceError, ValidationError, ResourceNotFoundError,
    JobNotFoundError, ArtifactNotFoundError, FileNotFoundError,
    LLMAPIError, ServiceUnavailableError, handle_exception, error_handler
)
from bracket_irl_common.clients.llm_client import LLMClient, get_llm_client
