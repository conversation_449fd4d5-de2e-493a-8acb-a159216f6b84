"""
Health check utilities for Bracket IRL microservices.

This module provides utilities for implementing health checks in microservices.
"""

import os
import time
import socket
import asyncio
import logging
import aiohttp
from enum import Enum
from typing import Dict, List, Any, Optional, Callable, Awaitable, Union
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)

class HealthStatus(str, Enum):
    """Health status enum."""
    UP = "up"
    DOWN = "down"
    DEGRADED = "degraded"
    UNKNOWN = "unknown"

@dataclass
class HealthCheck:
    """Health check result."""
    name: str
    status: HealthStatus
    details: Dict[str, Any] = field(default_factory=dict)
    timestamp: float = field(default_factory=time.time)

@dataclass
class HealthCheckResult:
    """Overall health check result."""
    status: HealthStatus
    checks: List[HealthCheck] = field(default_factory=list)
    timestamp: float = field(default_factory=time.time)

class HealthCheckService:
    """Service for performing health checks."""
    
    def __init__(self, service_name: str):
        """
        Initialize health check service.
        
        Args:
            service_name: Name of the service
        """
        self.service_name = service_name
        self.checks: Dict[str, Callable[[], Awaitable[HealthCheck]]] = {}
    
    def register_check(self, name: str, check_func: Callable[[], Awaitable[HealthCheck]]) -> None:
        """
        Register health check function.
        
        Args:
            name: Name of the health check
            check_func: Async function that performs the health check
        """
        self.checks[name] = check_func
    
    async def check_health(self) -> HealthCheckResult:
        """
        Check health of all registered checks.
        
        Returns:
            Overall health check result
        """
        results = []
        overall_status = HealthStatus.UP
        
        for name, check_func in self.checks.items():
            try:
                result = await check_func()
                results.append(result)
                
                # Update overall status
                if result.status == HealthStatus.DOWN:
                    overall_status = HealthStatus.DOWN
                elif result.status == HealthStatus.DEGRADED and overall_status != HealthStatus.DOWN:
                    overall_status = HealthStatus.DEGRADED
            except Exception as e:
                logger.exception(f"Error in health check {name}: {e}")
                results.append(HealthCheck(
                    name=name,
                    status=HealthStatus.DOWN,
                    details={"error": str(e)}
                ))
                overall_status = HealthStatus.DOWN
        
        return HealthCheckResult(
            status=overall_status,
            checks=results
        )

# Common health check functions

async def check_disk_space(path: str, min_free_mb: float = 100.0) -> HealthCheck:
    """
    Check disk space.
    
    Args:
        path: Path to check
        min_free_mb: Minimum free space in MB
    
    Returns:
        Health check result
    """
    try:
        stat = os.statvfs(path)
        free_bytes = stat.f_frsize * stat.f_bavail
        free_mb = free_bytes / (1024 * 1024)
        
        status = HealthStatus.UP if free_mb >= min_free_mb else HealthStatus.DEGRADED
        if free_mb < min_free_mb / 2:
            status = HealthStatus.DOWN
        
        return HealthCheck(
            name="disk_space",
            status=status,
            details={
                "path": path,
                "free_mb": free_mb,
                "min_free_mb": min_free_mb
            }
        )
    except Exception as e:
        return HealthCheck(
            name="disk_space",
            status=HealthStatus.DOWN,
            details={
                "path": path,
                "error": str(e)
            }
        )

async def check_memory_usage(max_usage_percent: float = 90.0) -> HealthCheck:
    """
    Check memory usage.
    
    Args:
        max_usage_percent: Maximum memory usage percentage
    
    Returns:
        Health check result
    """
    try:
        # Try to get memory info from /proc/meminfo
        with open("/proc/meminfo", "r") as f:
            meminfo = f.read()
        
        # Parse memory info
        mem_total = None
        mem_available = None
        
        for line in meminfo.split("\n"):
            if line.startswith("MemTotal:"):
                mem_total = int(line.split()[1])
            elif line.startswith("MemAvailable:"):
                mem_available = int(line.split()[1])
        
        if mem_total is None or mem_available is None:
            raise ValueError("Failed to parse memory info")
        
        usage_percent = 100.0 * (1.0 - mem_available / mem_total)
        
        status = HealthStatus.UP if usage_percent <= max_usage_percent else HealthStatus.DEGRADED
        if usage_percent > max_usage_percent + 5.0:
            status = HealthStatus.DOWN
        
        return HealthCheck(
            name="memory_usage",
            status=status,
            details={
                "usage_percent": usage_percent,
                "max_usage_percent": max_usage_percent
            }
        )
    except Exception as e:
        # Fallback to psutil if available
        try:
            import psutil
            memory = psutil.virtual_memory()
            usage_percent = memory.percent
            
            status = HealthStatus.UP if usage_percent <= max_usage_percent else HealthStatus.DEGRADED
            if usage_percent > max_usage_percent + 5.0:
                status = HealthStatus.DOWN
            
            return HealthCheck(
                name="memory_usage",
                status=status,
                details={
                    "usage_percent": usage_percent,
                    "max_usage_percent": max_usage_percent
                }
            )
        except Exception as e2:
            return HealthCheck(
                name="memory_usage",
                status=HealthStatus.UNKNOWN,
                details={
                    "error": f"Failed to check memory usage: {e}, {e2}"
                }
            )

async def check_service_health(url: str, timeout: float = 5.0) -> HealthCheck:
    """
    Check health of another service.
    
    Args:
        url: URL of the service health check endpoint
        timeout: Timeout in seconds
    
    Returns:
        Health check result
    """
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, timeout=timeout) as response:
                if response.status != 200:
                    return HealthCheck(
                        name=f"service_{url}",
                        status=HealthStatus.DOWN,
                        details={
                            "url": url,
                            "status_code": response.status
                        }
                    )
                
                data = await response.json()
                service_status = data.get("status", "unknown")
                
                status = HealthStatus.UP
                if service_status == "down":
                    status = HealthStatus.DOWN
                elif service_status == "degraded":
                    status = HealthStatus.DEGRADED
                
                return HealthCheck(
                    name=f"service_{url}",
                    status=status,
                    details={
                        "url": url,
                        "service_status": service_status
                    }
                )
    except Exception as e:
        return HealthCheck(
            name=f"service_{url}",
            status=HealthStatus.DOWN,
            details={
                "url": url,
                "error": str(e)
            }
        )

async def check_port_open(host: str, port: int, timeout: float = 1.0) -> HealthCheck:
    """
    Check if port is open.
    
    Args:
        host: Host to check
        port: Port to check
        timeout: Timeout in seconds
    
    Returns:
        Health check result
    """
    try:
        _, writer = await asyncio.wait_for(
            asyncio.open_connection(host, port),
            timeout=timeout
        )
        writer.close()
        await writer.wait_closed()
        
        return HealthCheck(
            name=f"port_{host}_{port}",
            status=HealthStatus.UP,
            details={
                "host": host,
                "port": port
            }
        )
    except Exception as e:
        return HealthCheck(
            name=f"port_{host}_{port}",
            status=HealthStatus.DOWN,
            details={
                "host": host,
                "port": port,
                "error": str(e)
            }
        )
