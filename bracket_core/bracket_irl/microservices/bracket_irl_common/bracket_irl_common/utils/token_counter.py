"""
Token counting utilities for Bracket IRL microservices.
"""

import tiktoken
from typing import Dict, List, Any, Optional, Union

# Cache tokenizers to avoid recreating them
_TOKENIZERS: Dict[str, Any] = {}

def get_tokenizer(model: str) -> Any:
    """
    Get tokenizer for model.
    
    Args:
        model: Model name
    
    Returns:
        Tokenizer for model
    """
    if model in _TOKENIZERS:
        return _TOKENIZERS[model]
    
    try:
        if model.startswith(("gpt-4", "gpt-3.5")):
            encoding_name = "cl100k_base"
        elif model.startswith("text-embedding-3"):
            encoding_name = "cl100k_base"
        else:
            encoding_name = "cl100k_base"  # Default to cl100k_base
        
        tokenizer = tiktoken.get_encoding(encoding_name)
        _TOKENIZERS[model] = tokenizer
        return tokenizer
    except Exception as e:
        raise ValueError(f"Failed to get tokenizer for model {model}: {e}")

def num_tokens_from_string(text: str, model: str = "gpt-4o-mini") -> int:
    """
    Count tokens in text.
    
    Args:
        text: Text to count tokens in
        model: Model name
    
    Returns:
        Number of tokens in text
    """
    try:
        tokenizer = get_tokenizer(model)
        return len(tokenizer.encode(text))
    except Exception:
        # Fallback to approximate token count (1 token ≈ 4 characters)
        return len(text) // 4

def num_tokens_from_messages(messages: List[Dict[str, str]], model: str = "gpt-4o-mini") -> int:
    """
    Count tokens in messages.
    
    Args:
        messages: Messages to count tokens in
        model: Model name
    
    Returns:
        Number of tokens in messages
    """
    try:
        tokenizer = get_tokenizer(model)
        
        # Based on OpenAI's token counting logic
        num_tokens = 0
        for message in messages:
            # Count tokens in message content
            num_tokens += len(tokenizer.encode(message.get("content", "")))
            
            # Add tokens for message metadata
            num_tokens += 4  # Every message follows <im_start>{role/name}\n{content}<im_end>\n
            
            # Add tokens for role
            role = message.get("role", "")
            num_tokens += len(tokenizer.encode(role))
            
            # Add tokens for name if present
            if "name" in message:
                name = message.get("name", "")
                num_tokens += len(tokenizer.encode(name))
                num_tokens += 1  # Additional token for name
        
        # Add tokens for system message
        num_tokens += 3  # Every reply is primed with <|im_start|>assistant<|im_end|>
        
        return num_tokens
    except Exception:
        # Fallback to approximate token count
        total_text = ""
        for message in messages:
            total_text += message.get("content", "")
        
        # Approximate token count (1 token ≈ 4 characters)
        return len(total_text) // 4

def estimate_tokens_from_file_count(file_count: int, avg_tokens_per_file: int = 1000) -> int:
    """
    Estimate tokens from file count.
    
    Args:
        file_count: Number of files
        avg_tokens_per_file: Average tokens per file
    
    Returns:
        Estimated number of tokens
    """
    return file_count * avg_tokens_per_file

def chunk_text_by_tokens(text: str, max_tokens_per_chunk: int, model: str = "gpt-4o-mini", 
                         overlap_tokens: int = 100) -> List[str]:
    """
    Chunk text by tokens.
    
    Args:
        text: Text to chunk
        max_tokens_per_chunk: Maximum tokens per chunk
        model: Model name
        overlap_tokens: Number of tokens to overlap between chunks
    
    Returns:
        List of text chunks
    """
    try:
        tokenizer = get_tokenizer(model)
        tokens = tokenizer.encode(text)
        
        if len(tokens) <= max_tokens_per_chunk:
            return [text]
        
        chunks = []
        for i in range(0, len(tokens), max_tokens_per_chunk - overlap_tokens):
            chunk_tokens = tokens[i:i + max_tokens_per_chunk]
            chunk_text = tokenizer.decode(chunk_tokens)
            chunks.append(chunk_text)
        
        return chunks
    except Exception:
        # Fallback to approximate chunking
        if len(text) <= max_tokens_per_chunk * 4:  # Approximate 4 chars per token
            return [text]
        
        chunks = []
        chunk_size = max_tokens_per_chunk * 4
        overlap_size = overlap_tokens * 4
        
        for i in range(0, len(text), chunk_size - overlap_size):
            chunk = text[i:i + chunk_size]
            chunks.append(chunk)
        
        return chunks
