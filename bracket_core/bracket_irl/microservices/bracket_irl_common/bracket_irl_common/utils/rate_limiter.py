"""
Rate limiting utilities for Bracket IRL microservices.
"""

import time
import asyncio
import threading
from typing import Dict, List, Any, Optional, Callable, TypeVar, Awaitable

T = TypeVar('T')

class RateLimiter:
    """Rate limiter for API calls."""
    
    def __init__(self, max_calls: float, period: float = 60.0):
        """
        Initialize rate limiter.
        
        Args:
            max_calls: Maximum number of calls per period
            period: Period in seconds
        """
        self.max_calls = max_calls
        self.period = period
        self.calls = 0
        self.reset_time = time.time() + period
        self._lock = threading.Lock()
    
    def _reset_if_needed(self) -> None:
        """Reset calls if period has elapsed."""
        current_time = time.time()
        if current_time >= self.reset_time:
            self.calls = 0
            self.reset_time = current_time + self.period
    
    def acquire(self, block: bool = True) -> bool:
        """
        Acquire permission to make a call.
        
        Args:
            block: Whether to block until permission is granted
        
        Returns:
            True if permission is granted, False otherwise
        """
        with self._lock:
            self._reset_if_needed()
            
            if self.calls < self.max_calls:
                self.calls += 1
                return True
            
            if not block:
                return False
            
            # Calculate time to wait
            wait_time = self.reset_time - time.time()
            if wait_time > 0:
                time.sleep(wait_time)
            
            # Reset and try again
            self._reset_if_needed()
            self.calls += 1
            return True
    
    def __enter__(self):
        """Enter context manager."""
        self.acquire()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit context manager."""
        pass

class AsyncRateLimiter:
    """Asynchronous rate limiter for API calls."""
    
    def __init__(self, max_calls: float, period: float = 60.0):
        """
        Initialize asynchronous rate limiter.
        
        Args:
            max_calls: Maximum number of calls per period
            period: Period in seconds
        """
        self.max_calls = max_calls
        self.period = period
        self.calls = 0
        self.reset_time = time.time() + period
        self._lock = asyncio.Lock()
    
    def _reset_if_needed(self) -> None:
        """Reset calls if period has elapsed."""
        current_time = time.time()
        if current_time >= self.reset_time:
            self.calls = 0
            self.reset_time = current_time + self.period
    
    async def acquire(self, block: bool = True) -> bool:
        """
        Acquire permission to make a call.
        
        Args:
            block: Whether to block until permission is granted
        
        Returns:
            True if permission is granted, False otherwise
        """
        async with self._lock:
            self._reset_if_needed()
            
            if self.calls < self.max_calls:
                self.calls += 1
                return True
            
            if not block:
                return False
            
            # Calculate time to wait
            wait_time = self.reset_time - time.time()
            if wait_time > 0:
                await asyncio.sleep(wait_time)
            
            # Reset and try again
            self._reset_if_needed()
            self.calls += 1
            return True
    
    async def __aenter__(self):
        """Enter asynchronous context manager."""
        await self.acquire()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Exit asynchronous context manager."""
        pass

class TokenBucketRateLimiter:
    """Token bucket rate limiter for API calls."""
    
    def __init__(self, max_tokens: float, refill_rate: float):
        """
        Initialize token bucket rate limiter.
        
        Args:
            max_tokens: Maximum number of tokens in bucket
            refill_rate: Tokens per second to refill
        """
        self.max_tokens = max_tokens
        self.refill_rate = refill_rate
        self.tokens = max_tokens
        self.last_refill = time.time()
        self._lock = threading.Lock()
    
    def _refill(self) -> None:
        """Refill tokens based on elapsed time."""
        now = time.time()
        elapsed = now - self.last_refill
        self.tokens = min(self.max_tokens, self.tokens + elapsed * self.refill_rate)
        self.last_refill = now
    
    def acquire(self, tokens: float = 1.0, block: bool = True) -> bool:
        """
        Acquire tokens from bucket.
        
        Args:
            tokens: Number of tokens to acquire
            block: Whether to block until tokens are available
        
        Returns:
            True if tokens are acquired, False otherwise
        """
        with self._lock:
            self._refill()
            
            if self.tokens >= tokens:
                self.tokens -= tokens
                return True
            
            if not block:
                return False
            
            # Calculate time to wait
            wait_time = (tokens - self.tokens) / self.refill_rate
            if wait_time > 0:
                time.sleep(wait_time)
            
            # Refill and try again
            self._refill()
            self.tokens -= tokens
            return True

class AsyncTokenBucketRateLimiter:
    """Asynchronous token bucket rate limiter for API calls."""
    
    def __init__(self, max_tokens: float, refill_rate: float):
        """
        Initialize asynchronous token bucket rate limiter.
        
        Args:
            max_tokens: Maximum number of tokens in bucket
            refill_rate: Tokens per second to refill
        """
        self.max_tokens = max_tokens
        self.refill_rate = refill_rate
        self.tokens = max_tokens
        self.last_refill = time.time()
        self._lock = asyncio.Lock()
    
    def _refill(self) -> None:
        """Refill tokens based on elapsed time."""
        now = time.time()
        elapsed = now - self.last_refill
        self.tokens = min(self.max_tokens, self.tokens + elapsed * self.refill_rate)
        self.last_refill = now
    
    async def acquire(self, tokens: float = 1.0, block: bool = True) -> bool:
        """
        Acquire tokens from bucket.
        
        Args:
            tokens: Number of tokens to acquire
            block: Whether to block until tokens are available
        
        Returns:
            True if tokens are acquired, False otherwise
        """
        async with self._lock:
            self._refill()
            
            if self.tokens >= tokens:
                self.tokens -= tokens
                return True
            
            if not block:
                return False
            
            # Calculate time to wait
            wait_time = (tokens - self.tokens) / self.refill_rate
            if wait_time > 0:
                await asyncio.sleep(wait_time)
            
            # Refill and try again
            self._refill()
            self.tokens -= tokens
            return True
