"""
Shared data models for Bracket IRL microservices.
"""

import uuid
import time
from enum import Enum
from typing import Dict, List, Any, Optional, Union
from pydantic import BaseModel, Field, validator

class JobStatus(str, Enum):
    """Job status enum."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELED = "canceled"

class Job(BaseModel):
    """Base job model."""
    job_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Unique job ID")
    status: JobStatus = Field(default=JobStatus.PENDING, description="Job status")
    created_at: float = Field(default_factory=time.time, description="Job creation timestamp")
    updated_at: float = Field(default_factory=time.time, description="Job last update timestamp")
    progress: float = Field(default=0.0, description="Job progress (0.0 to 1.0)")
    message: Optional[str] = Field(None, description="Job status message")
    error: Optional[str] = Field(None, description="Error message if job failed")
    result: Optional[Dict[str, Any]] = Field(None, description="Job result")

    @validator("updated_at", always=True)
    def update_timestamp(cls, v, values):
        """Update the timestamp whenever the model is validated."""
        return time.time()

class ArtifactType(str, Enum):
    """Artifact type enum."""
    REPOMAP = "repomap"
    FILTERED_REPOMAP = "filtered_repomap"
    DOMAIN_ANALYSIS = "domain_analysis"
    DOMAIN_MAPPING = "domain_mapping"
    FILE_DOMAIN_MAPPING = "file_domain_mapping"  # Added for backward compatibility
    DOMAIN_FILE_REPOMAP = "domain_file_repomap"
    DIAGRAM = "diagram"
    STATS = "stats"

class Artifact(BaseModel):
    """Artifact model."""
    artifact_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Unique artifact ID")
    job_id: str = Field(..., description="Job ID that produced this artifact")
    artifact_type: ArtifactType = Field(..., description="Artifact type")
    created_at: float = Field(default_factory=time.time, description="Artifact creation timestamp")
    path: str = Field(..., description="Path to the artifact")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Artifact metadata")

class RepositoryMapRequest(BaseModel):
    """Repository map generation request."""
    repo_dir: str = Field(..., description="Repository directory path")
    batch_size: int = Field(1000, description="Batch size for processing")
    top_percentage: float = Field(0.2, description="Top percentage of functions to include")
    min_functions: int = Field(3, description="Minimum number of functions to include")
    max_functions: int = Field(7, description="Maximum number of functions to include")
    exclude_tests: bool = Field(True, description="Whether to exclude test files")
    output_format: str = Field("json", description="Output format (json or yaml)")
    include_extensions: List[str] = Field(default_factory=lambda: [".py", ".js", ".ts", ".java", ".rb"],
                                         description="File extensions to include")

class DomainAnalysisRequest(BaseModel):
    """Domain analysis request."""
    repomap_path: str = Field(..., description="Path to the repository map")
    api_key: Optional[str] = Field(None, description="API key for LLM service")
    model: str = Field("gpt-4o-mini", description="LLM model to use")
    max_requests_per_minute: float = Field(5000, description="Maximum requests per minute")
    max_tokens_per_minute: float = Field(15000000, description="Maximum tokens per minute")
    use_openrouter: bool = Field(False, description="Whether to use OpenRouter")
    use_claude: bool = Field(False, description="Whether to use Claude")
    openrouter_base_url: str = Field("https://openrouter.ai/api/v1", description="OpenRouter base URL")
    claude_model: str = Field("claude-3-7-sonnet-20250219", description="Claude model to use")
    max_tokens_per_chunk: int = Field(60000, description="Maximum tokens per chunk")
    disable_parallel: bool = Field(False, description="Whether to disable parallel processing")
    max_concurrent_tasks: int = Field(0, description="Maximum concurrent tasks (0 = auto)")
    generate_explanations: bool = Field(False, description="Whether to generate explanations")

class FileDomainMapperRequest(BaseModel):
    """File domain mapper request."""
    repomap_path: str = Field(..., description="Path to the repository map")
    domain_analysis_path: str = Field(..., description="Path to the domain analysis")
    model: str = Field("gpt-4o-mini", description="LLM model to use")
    use_openrouter: bool = Field(False, description="Whether to use OpenRouter")
    max_files_per_batch: int = Field(50, description="Maximum files per batch")

class DomainFileRepomapRequest(BaseModel):
    """Domain file repomap request."""
    domain_mapping_path: str = Field(..., description="Path to the domain mapping")
    repomap_path: str = Field(..., description="Path to the repository map")
    output_format: str = Field("json", description="Output format (json or yaml)")

class DiagramGeneratorRequest(BaseModel):
    """Diagram generator request."""
    domain_file_repomap_path: str = Field(..., description="Path to the domain file repomap")
    model_type: str = Field("openai", description="Model type (openai or claude)")
    openai_model: str = Field("gpt-4o-mini", description="OpenAI model to use")
    use_openrouter: bool = Field(False, description="Whether to use OpenRouter")
    openrouter_model: str = Field("google/gemini-2.5-pro-preview", description="OpenRouter model to use")
    max_concurrent_tasks: int = Field(30, description="Maximum concurrent tasks")

class OrchestrationRequest(BaseModel):
    """Orchestration request."""
    repo_dir: str = Field(..., description="Repository directory path")
    repomap_config: Optional[RepositoryMapRequest] = Field(None, description="Repository map configuration")
    domain_analysis_config: Optional[DomainAnalysisRequest] = Field(None, description="Domain analysis configuration")
    file_domain_mapper_config: Optional[FileDomainMapperRequest] = Field(None, description="File domain mapper configuration")
    domain_file_repomap_config: Optional[DomainFileRepomapRequest] = Field(None, description="Domain file repomap configuration")
    diagram_generator_config: Optional[DiagramGeneratorRequest] = Field(None, description="Diagram generator configuration")
