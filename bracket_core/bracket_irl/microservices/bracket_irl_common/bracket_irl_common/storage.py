"""
Storage abstraction for Bracket IRL microservices.
"""

import os
import json
import yaml
import shutil
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union, BinaryIO

class StorageClient(ABC):
    """Abstract storage client interface."""

    @abstractmethod
    def read_file(self, path: str) -> bytes:
        """Read file from storage."""
        pass

    @abstractmethod
    def write_file(self, path: str, content: bytes) -> str:
        """Write file to storage."""
        pass

    @abstractmethod
    def delete_file(self, path: str) -> bool:
        """Delete file from storage."""
        pass

    @abstractmethod
    def file_exists(self, path: str) -> bool:
        """Check if file exists in storage."""
        pass

    @abstractmethod
    def list_files(self, path: str) -> List[str]:
        """List files in storage directory."""
        pass

    def read_text(self, path: str, encoding: str = "utf-8") -> str:
        """Read text file from storage."""
        return self.read_file(path).decode(encoding)

    def write_text(self, path: str, content: str, encoding: str = "utf-8") -> str:
        """Write text file to storage."""
        return self.write_file(path, content.encode(encoding))

    def read_json(self, path: str) -> Dict[str, Any]:
        """Read JSON file from storage."""
        return json.loads(self.read_text(path))

    def write_json(self, path: str, content: Dict[str, Any], indent: int = 2) -> str:
        """Write JSON file to storage."""
        return self.write_text(path, json.dumps(content, indent=indent))

    def read_yaml(self, path: str) -> Dict[str, Any]:
        """Read YAML file from storage."""
        return yaml.safe_load(self.read_text(path))

    def write_yaml(self, path: str, content: Dict[str, Any]) -> str:
        """Write YAML file to storage."""
        return self.write_text(path, yaml.dump(content, default_flow_style=False))

    def store_artifact(self, job_id: str, artifact_type: str, content: Any, format: str = "json") -> str:
        """
        Store an artifact with standardized naming across all services.

        Args:
            job_id: Job ID
            artifact_type: Artifact type (e.g., "repomap", "filtered_repomap")
            content: Artifact content
            format: Artifact format (json, yaml, txt)

        Returns:
            Path to the stored artifact
        """
        # Use standardized path format
        path = f"artifacts/{job_id}/{artifact_type}.{format}"

        # Store content based on format
        if format == "json":
            self.write_json(path, content)
        elif format == "yaml":
            self.write_yaml(path, content)
        elif format == "txt":
            self.write_text(path, content)
        else:
            self.write_file(path, content)

        return path

    def get_artifact_path(self, job_id: str, artifact_type: str, format: str = "json") -> str:
        """
        Get standardized path for an artifact.

        Args:
            job_id: Job ID
            artifact_type: Artifact type (e.g., "repomap", "filtered_repomap")
            format: Artifact format (json, yaml, txt)

        Returns:
            Standardized path for the artifact
        """
        return f"artifacts/{job_id}/{artifact_type}.{format}"

class LocalStorageClient(StorageClient):
    """Local file system storage client."""

    def __init__(self, base_path: str = "./data"):
        """
        Initialize local storage client.

        Args:
            base_path: Base path for local storage
        """
        self.base_path = os.path.abspath(base_path)
        os.makedirs(self.base_path, exist_ok=True)

    def _get_full_path(self, path: str) -> str:
        """Get full path for file."""
        # Ensure path is relative to base path
        if os.path.isabs(path):
            path = os.path.relpath(path, "/")

        full_path = os.path.join(self.base_path, path)

        # Ensure path is within base path
        if not os.path.abspath(full_path).startswith(self.base_path):
            raise ValueError(f"Path {path} is outside base path {self.base_path}")

        return full_path

    def read_file(self, path: str) -> bytes:
        """Read file from local storage."""
        full_path = self._get_full_path(path)

        if not os.path.exists(full_path):
            raise FileNotFoundError(f"File not found: {path}")

        with open(full_path, "rb") as f:
            return f.read()

    def write_file(self, path: str, content: bytes) -> str:
        """Write file to local storage."""
        full_path = self._get_full_path(path)

        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(full_path), exist_ok=True)

        with open(full_path, "wb") as f:
            f.write(content)

        return path

    def delete_file(self, path: str) -> bool:
        """Delete file from local storage."""
        full_path = self._get_full_path(path)

        if not os.path.exists(full_path):
            return False

        if os.path.isdir(full_path):
            shutil.rmtree(full_path)
        else:
            os.remove(full_path)

        return True

    def file_exists(self, path: str) -> bool:
        """Check if file exists in local storage."""
        full_path = self._get_full_path(path)
        return os.path.exists(full_path)

    def list_files(self, path: str) -> List[str]:
        """List files in local storage directory."""
        full_path = self._get_full_path(path)

        if not os.path.exists(full_path):
            return []

        if not os.path.isdir(full_path):
            return [path]

        result = []
        for root, _, files in os.walk(full_path):
            for file in files:
                file_path = os.path.join(root, file)
                rel_path = os.path.relpath(file_path, self.base_path)
                result.append(rel_path)

        return result

# Factory function to create storage client
def get_storage_client(storage_type: str = "local", **kwargs) -> StorageClient:
    """
    Create storage client based on type.

    Args:
        storage_type: Type of storage client (local, s3, gcs)
        **kwargs: Additional arguments for storage client

    Returns:
        Storage client instance
    """
    if storage_type == "local":
        return LocalStorageClient(**kwargs)
    else:
        raise ValueError(f"Unsupported storage type: {storage_type}")
