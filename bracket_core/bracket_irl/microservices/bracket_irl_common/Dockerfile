FROM python:3.10-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy the package files
COPY bracket_irl_common /app/bracket_irl_common/
COPY setup.py /app/

# Install the package
RUN pip install --no-cache-dir -e .

# Create directory structure for imports
RUN mkdir -p /app/bracket_irl/microservices
RUN ln -s /app/bracket_irl_common /app/bracket_irl/microservices/bracket_irl_common

# Set environment variables
ENV PYTHONPATH=/app:/

# Create a marker file to verify the package is installed
RUN echo "bracket_irl_common installed" > /app/bracket_irl_common_installed.txt

CMD ["echo", "This is a base image for other services"]
