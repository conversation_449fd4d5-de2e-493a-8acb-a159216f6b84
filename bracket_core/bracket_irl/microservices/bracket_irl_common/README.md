# Bracket IRL Common

This package provides common utilities and shared components for the Bracket IRL microservices architecture.

## Features

- Configuration management
- Shared data models
- Storage abstraction (local/cloud)
- Centralized logging
- Metrics collection
- LLM client interfaces
- Rate limiting utilities
- Token counting utilities
- Standardized artifact storage

## Installation

```bash
pip install -e .
```

## Usage

```python
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.config import Config
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.models import JobStatus
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.clients.llm_client import LLMClient

# Load configuration
config = Config.from_file("config.yaml")

# Create LLM client
llm_client = LLMClient(
    api_key=config.api_key,
    model=config.model,
    max_requests_per_minute=config.max_requests_per_minute,
    max_tokens_per_minute=config.max_tokens_per_minute
)

# Use shared models
job_status = JobStatus(
    job_id="123",
    status="running",
    progress=0.5,
    message="Processing batch 2/4"
)
```

## Standardized Artifact Storage

The Bracket IRL microservices use a standardized convention for storing artifacts:

### Artifact Path Convention

All artifacts follow this path structure:
```
/app/data/artifacts/{job_id}/{artifact_type}.{extension}
```

For example:
```
/app/data/artifacts/123e4567-e89b-12d3-a456-426614174000/repomap.json
/app/data/artifacts/123e4567-e89b-12d3-a456-426614174000/filtered_repomap.json
/app/data/artifacts/123e4567-e89b-12d3-a456-426614174000/domain_analysis.json
```

### Artifact Metadata

Metadata about artifacts is stored separately using this path structure:
```
/app/data/artifacts/{job_id}/artifact_{artifact_id}.json
```

### Helper Methods

The `StorageClient` class provides helper methods for working with standardized artifact paths:

```python
# Store an artifact with standardized naming
path = storage_client.store_artifact(
    job_id="123e4567-e89b-12d3-a456-426614174000",
    artifact_type="repomap",
    content=repomap_data,
    format="json"
)

# Get the standardized path for an artifact
path = storage_client.get_artifact_path(
    job_id="123e4567-e89b-12d3-a456-426614174000",
    artifact_type="repomap",
    format="json"
)
```
