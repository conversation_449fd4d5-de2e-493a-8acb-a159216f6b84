apiVersion: v1
kind: ConfigMap
metadata:
  name: test-nginx-config
  namespace: bracket-irl
data:
  default.conf: |
    server {
      listen 80;
      server_name _;

      # Root path handler
      location = / {
        return 200 'Test Nginx Server is working!';
        add_header Content-Type text/plain;
      }

      # Test endpoint
      location = /test {
        return 200 'Test endpoint is working!';
        add_header Content-Type text/plain;
      }

      # Handle path prefix - simple approach
      location /test-nginx/ {
        return 200 'Path prefix /test-nginx/ is working!';
        add_header Content-Type text/plain;
      }

      # Special case for test-nginx/test
      location = /test-nginx/test {
        return 200 'Path prefix /test-nginx/test is working!';
        add_header Content-Type text/plain;
      }
    }
