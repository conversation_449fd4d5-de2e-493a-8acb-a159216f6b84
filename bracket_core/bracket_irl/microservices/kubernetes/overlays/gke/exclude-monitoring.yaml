apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
  namespace: bracket-irl
$patch: delete
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-provisioning
  namespace: bracket-irl
$patch: delete
---
apiVersion: v1
kind: Service
metadata:
  name: grafana
  namespace: bracket-irl
$patch: delete
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus
  namespace: bracket-irl
$patch: delete
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: bracket-irl
$patch: delete
---
apiVersion: v1
kind: Service
metadata:
  name: prometheus
  namespace: bracket-irl
$patch: delete
