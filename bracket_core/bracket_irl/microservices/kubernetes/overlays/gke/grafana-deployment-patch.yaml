apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
  namespace: bracket-irl
spec:
  template:
    spec:
      containers:
      - name: grafana
        volumeMounts:
        - name: grafana-provisioning
          mountPath: /etc/grafana/provisioning
        - name: grafana-dashboards
          mountPath: /etc/grafana/provisioning/dashboards
        - name: grafana-data
          mountPath: /var/lib/grafana
      volumes:
      - name: grafana-provisioning
        configMap:
          name: grafana-provisioning-new
      - name: grafana-dashboards
        configMap:
          name: grafana-dashboards
      - name: grafana-data
        emptyDir: {}
