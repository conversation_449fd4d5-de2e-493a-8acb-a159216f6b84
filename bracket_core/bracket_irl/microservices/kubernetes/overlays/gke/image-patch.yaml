apiVersion: apps/v1
kind: Deployment
metadata:
  name: orchestrator-service
  namespace: bracket-irl
spec:
  template:
    spec:
      containers:
      - name: orchestrator-service
        image: asia-south1-docker.pkg.dev/bracket-irl/bracket-irl/orchestrator-service:latest
        imagePullPolicy: Always
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: repo-mapper-service
  namespace: bracket-irl
spec:
  template:
    spec:
      containers:
      - name: repo-mapper-service
        image: asia-south1-docker.pkg.dev/bracket-irl/bracket-irl/repo-mapper-service:latest
        imagePullPolicy: Always
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: domain-analyzer-service
  namespace: bracket-irl
spec:
  template:
    spec:
      containers:
      - name: domain-analyzer-service
        image: asia-south1-docker.pkg.dev/bracket-irl/bracket-irl/domain-analyzer-service:latest
        imagePullPolicy: Always
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: file-domain-mapper-service
  namespace: bracket-irl
spec:
  template:
    spec:
      containers:
      - name: file-domain-mapper-service
        image: asia-south1-docker.pkg.dev/bracket-irl/bracket-irl/file-domain-mapper-service:latest
        imagePullPolicy: Always
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: domain-file-repomap-service
  namespace: bracket-irl
spec:
  template:
    spec:
      containers:
      - name: domain-file-repomap-service
        image: asia-south1-docker.pkg.dev/bracket-irl/bracket-irl/domain-file-repomap-service:latest
        imagePullPolicy: Always
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: diagram-generator-service
  namespace: bracket-irl
spec:
  template:
    spec:
      containers:
      - name: diagram-generator-service
        image: asia-south1-docker.pkg.dev/bracket-irl/bracket-irl/diagram-generator-service:latest
        imagePullPolicy: Always
