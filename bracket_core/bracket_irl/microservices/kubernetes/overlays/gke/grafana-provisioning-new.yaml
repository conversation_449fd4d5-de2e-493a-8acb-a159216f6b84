apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-provisioning-new
  namespace: bracket-irl
data:
  datasources.yaml: |
    apiVersion: 1
    datasources:
      - name: Prometheus
        type: prometheus
        access: proxy
        url: http://prometheus:9090
        isDefault: true
  dashboard.yaml: |
    apiVersion: 1
    providers:
      - name: 'default'
        orgId: 1
        folder: ''
        type: file
        disableDeletion: false
        updateIntervalSeconds: 10
        options:
          path: /etc/grafana/provisioning/dashboards
