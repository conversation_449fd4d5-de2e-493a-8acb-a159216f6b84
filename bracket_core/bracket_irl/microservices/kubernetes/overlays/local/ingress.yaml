apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: bracket-irl-ingress
  namespace: bracket-irl
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /$1
spec:
  rules:
  - host: bracket-irl.local
    http:
      paths:
      - path: /orchestrator/?(.*)
        pathType: Prefix
        backend:
          service:
            name: orchestrator-service
            port:
              number: 8000
      - path: /repo-mapper/?(.*)
        pathType: Prefix
        backend:
          service:
            name: repo-mapper-service
            port:
              number: 8001
      - path: /domain-analyzer/?(.*)
        pathType: Prefix
        backend:
          service:
            name: domain-analyzer-service
            port:
              number: 8002
      - path: /file-domain-mapper/?(.*)
        pathType: Prefix
        backend:
          service:
            name: file-domain-mapper-service
            port:
              number: 8003
      - path: /domain-file-repomap/?(.*)
        pathType: Prefix
        backend:
          service:
            name: domain-file-repomap-service
            port:
              number: 8004
      - path: /diagram-generator/?(.*)
        pathType: Prefix
        backend:
          service:
            name: diagram-generator-service
            port:
              number: 8005
      - path: /prometheus/?(.*)
        pathType: Prefix
        backend:
          service:
            name: prometheus
            port:
              number: 9090
      - path: /grafana/?(.*)
        pathType: Prefix
        backend:
          service:
            name: grafana
            port:
              number: 3000
