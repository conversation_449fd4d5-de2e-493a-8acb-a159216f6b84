apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus
  namespace: bracket-irl
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prometheus
  template:
    metadata:
      labels:
        app: prometheus
    spec:
      containers:
      - name: prometheus
        image: prom/prometheus:v2.45.0
        ports:
        - containerPort: 9090
        volumeMounts:
        - name: prometheus-config
          mountPath: /etc/prometheus
        - name: prometheus-data
          mountPath: /prometheus
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /-/healthy
            port: 9090
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /-/ready
            port: 9090
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: prometheus-config
        configMap:
          name: prometheus-config
      - name: prometheus-data
        emptyDir: {}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: bracket-irl
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s

    scrape_configs:
      - job_name: 'prometheus'
        static_configs:
          - targets: ['localhost:9090']

      - job_name: 'repo-mapper-service'
        static_configs:
          - targets: ['repo-mapper-service:8001']
        metrics_path: /metrics

      - job_name: 'domain-analyzer-service'
        static_configs:
          - targets: ['domain-analyzer-service:8002']
        metrics_path: /metrics

      - job_name: 'file-domain-mapper-service'
        static_configs:
          - targets: ['file-domain-mapper-service:8003']
        metrics_path: /metrics

      - job_name: 'domain-file-repomap-service'
        static_configs:
          - targets: ['domain-file-repomap-service:8004']
        metrics_path: /metrics

      - job_name: 'diagram-generator-service'
        static_configs:
          - targets: ['diagram-generator-service:8005']
        metrics_path: /metrics

      - job_name: 'orchestrator-service'
        static_configs:
          - targets: ['orchestrator-service:8000']
        metrics_path: /metrics
---
apiVersion: v1
kind: Service
metadata:
  name: prometheus
  namespace: bracket-irl
spec:
  selector:
    app: prometheus
  ports:
  - port: 9090
    targetPort: 9090
  type: ClusterIP
