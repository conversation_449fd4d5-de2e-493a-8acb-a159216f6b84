apiVersion: apps/v1
kind: Deployment
metadata:
  name: diagram-generator-service
  namespace: bracket-irl
spec:
  replicas: 1
  selector:
    matchLabels:
      app: diagram-generator-service
  template:
    metadata:
      labels:
        app: diagram-generator-service
    spec:
      containers:
      - name: diagram-generator-service
        image: bracket-irl/diagram-generator-service:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8005
        env:
        - name: SERVICE_NAME
          value: "diagram-generator-service"
        - name: HOST
          value: "0.0.0.0"
        - name: PORT
          value: "8005"
        - name: LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: bracket-irl-config
              key: LOG_LEVEL
        - name: STORAGE_TYPE
          valueFrom:
            configMapKeyRef:
              name: bracket-irl-config
              key: STORAGE_TYPE
        - name: STORAGE_PATH
          valueFrom:
            configMapKeyRef:
              name: bracket-irl-config
              key: STORAGE_PATH
        - name: MAX_CONCURRENT_TASKS
          valueFrom:
            configMapKeyRef:
              name: bracket-irl-config
              key: MAX_CONCURRENT_TASKS
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: bracket-irl-secrets
              key: OPENAI_API_KEY
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: bracket-irl-secrets
              key: ANTHROPIC_API_KEY
              optional: true
        - name: OPENROUTER_API_KEY
          valueFrom:
            secretKeyRef:
              name: bracket-irl-secrets
              key: OPENROUTER_API_KEY
              optional: true
        volumeMounts:
        - name: bracket-data
          mountPath: /app/data
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8005
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8005
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: bracket-data
        persistentVolumeClaim:
          claimName: bracket-data-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: diagram-generator-service
  namespace: bracket-irl
spec:
  selector:
    app: diagram-generator-service
  ports:
  - port: 8005
    targetPort: 8005
  type: ClusterIP
