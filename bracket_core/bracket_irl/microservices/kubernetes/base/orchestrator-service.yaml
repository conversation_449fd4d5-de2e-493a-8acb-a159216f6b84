apiVersion: apps/v1
kind: Deployment
metadata:
  name: orchestrator-service
  namespace: bracket-irl
spec:
  replicas: 1
  selector:
    matchLabels:
      app: orchestrator-service
  template:
    metadata:
      labels:
        app: orchestrator-service
    spec:
      containers:
      - name: orchestrator-service
        image: bracket-irl/orchestrator-service:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8000
        env:
        - name: SERVICE_NAME
          value: "orchestrator-service"
        - name: HOST
          value: "0.0.0.0"
        - name: PORT
          value: "8000"
        - name: LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: bracket-irl-config
              key: LOG_LEVEL
        - name: STORAGE_TYPE
          valueFrom:
            configMapKeyRef:
              name: bracket-irl-config
              key: STORAGE_TYPE
        - name: STORAGE_PATH
          valueFrom:
            configMapKeyRef:
              name: bracket-irl-config
              key: STORAGE_PATH
        - name: MAX_CONCURRENT_TASKS
          valueFrom:
            configMapKeyRef:
              name: bracket-irl-config
              key: MAX_CONCURRENT_TASKS
        - name: REPO_MAPPER_URL
          value: "http://repo-mapper-service:8001"
        - name: DOMAIN_ANALYZER_URL
          value: "http://domain-analyzer-service:8002"
        - name: FILE_DOMAIN_MAPPER_URL
          value: "http://file-domain-mapper-service:8003"
        - name: DOMAIN_FILE_REPOMAP_URL
          value: "http://domain-file-repomap-service:8004"
        - name: DIAGRAM_GENERATOR_URL
          value: "http://diagram-generator-service:8005"
        volumeMounts:
        - name: bracket-data
          mountPath: /app/data
        - name: repo-volume
          mountPath: /repo
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: bracket-data
        persistentVolumeClaim:
          claimName: bracket-data-pvc
      - name: repo-volume
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: orchestrator-service
  namespace: bracket-irl
spec:
  selector:
    app: orchestrator-service
  ports:
  - port: 8000
    targetPort: 8000
  type: ClusterIP
