apiVersion: apps/v1
kind: Deployment
metadata:
  name: file-domain-mapper-service
  namespace: bracket-irl
spec:
  replicas: 1
  selector:
    matchLabels:
      app: file-domain-mapper-service
  template:
    metadata:
      labels:
        app: file-domain-mapper-service
    spec:
      containers:
      - name: file-domain-mapper-service
        image: bracket-irl/file-domain-mapper-service:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8003
        env:
        - name: SERVICE_NAME
          value: "file-domain-mapper-service"
        - name: HOST
          value: "0.0.0.0"
        - name: PORT
          value: "8003"
        - name: LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: bracket-irl-config
              key: LOG_LEVEL
        - name: STORAGE_TYPE
          valueFrom:
            configMapKeyRef:
              name: bracket-irl-config
              key: STORAGE_TYPE
        - name: STORAGE_PATH
          valueFrom:
            configMapKeyRef:
              name: bracket-irl-config
              key: STORAGE_PATH
        - name: MAX_CONCURRENT_TASKS
          valueFrom:
            configMapKeyRef:
              name: bracket-irl-config
              key: MAX_CONCURRENT_TASKS
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: bracket-irl-secrets
              key: OPENAI_API_KEY
        volumeMounts:
        - name: bracket-data
          mountPath: /app/data
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8003
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8003
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: bracket-data
        persistentVolumeClaim:
          claimName: bracket-data-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: file-domain-mapper-service
  namespace: bracket-irl
spec:
  selector:
    app: file-domain-mapper-service
  ports:
  - port: 8003
    targetPort: 8003
  type: ClusterIP
