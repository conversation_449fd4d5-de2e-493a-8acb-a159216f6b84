apiVersion: apps/v1
kind: Deployment
metadata:
  name: repo-mapper-service
  namespace: bracket-irl
spec:
  replicas: 1
  selector:
    matchLabels:
      app: repo-mapper-service
  template:
    metadata:
      labels:
        app: repo-mapper-service
    spec:
      containers:
      - name: repo-mapper-service
        image: bracket-irl/repo-mapper-service:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8001
        env:
        - name: SERVICE_NAME
          value: "repo-mapper-service"
        - name: HOST
          value: "0.0.0.0"
        - name: PORT
          value: "8001"
        - name: LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: bracket-irl-config
              key: LOG_LEVEL
        - name: STORAGE_TYPE
          valueFrom:
            configMapKeyRef:
              name: bracket-irl-config
              key: STORAGE_TYPE
        - name: STORAGE_PATH
          valueFrom:
            configMapKeyRef:
              name: bracket-irl-config
              key: STORAGE_PATH
        - name: MAX_CONCURRENT_TASKS
          valueFrom:
            configMapKeyRef:
              name: bracket-irl-config
              key: MAX_CONCURRENT_TASKS
        volumeMounts:
        - name: bracket-data
          mountPath: /app/data
        - name: repo-volume
          mountPath: /repo
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8001
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8001
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: bracket-data
        persistentVolumeClaim:
          claimName: bracket-data-pvc
      - name: repo-volume
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: repo-mapper-service
  namespace: bracket-irl
spec:
  selector:
    app: repo-mapper-service
  ports:
  - port: 8001
    targetPort: 8001
  type: ClusterIP
