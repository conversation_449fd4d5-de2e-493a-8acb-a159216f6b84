apiVersion: apps/v1
kind: Deployment
metadata:
  name: domain-file-repomap-service
  namespace: bracket-irl
spec:
  replicas: 1
  selector:
    matchLabels:
      app: domain-file-repomap-service
  template:
    metadata:
      labels:
        app: domain-file-repomap-service
    spec:
      containers:
      - name: domain-file-repomap-service
        image: bracket-irl/domain-file-repomap-service:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8004
        env:
        - name: SERVICE_NAME
          value: "domain-file-repomap-service"
        - name: HOST
          value: "0.0.0.0"
        - name: PORT
          value: "8004"
        - name: LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: bracket-irl-config
              key: LOG_LEVEL
        - name: STORAGE_TYPE
          valueFrom:
            configMapKeyRef:
              name: bracket-irl-config
              key: STORAGE_TYPE
        - name: STORAGE_PATH
          valueFrom:
            configMapKeyRef:
              name: bracket-irl-config
              key: STORAGE_PATH
        - name: MAX_CONCURRENT_TASKS
          valueFrom:
            configMapKeyRef:
              name: bracket-irl-config
              key: MAX_CONCURRENT_TASKS
        volumeMounts:
        - name: bracket-data
          mountPath: /app/data
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8004
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8004
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: bracket-data
        persistentVolumeClaim:
          claimName: bracket-data-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: domain-file-repomap-service
  namespace: bracket-irl
spec:
  selector:
    app: domain-file-repomap-service
  ports:
  - port: 8004
    targetPort: 8004
  type: ClusterIP
