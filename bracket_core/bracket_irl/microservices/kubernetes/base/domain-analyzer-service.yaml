apiVersion: apps/v1
kind: Deployment
metadata:
  name: domain-analyzer-service
  namespace: bracket-irl
spec:
  replicas: 1
  selector:
    matchLabels:
      app: domain-analyzer-service
  template:
    metadata:
      labels:
        app: domain-analyzer-service
    spec:
      containers:
      - name: domain-analyzer-service
        image: bracket-irl/domain-analyzer-service:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8002
        env:
        - name: SERVICE_NAME
          value: "domain-analyzer-service"
        - name: HOST
          value: "0.0.0.0"
        - name: PORT
          value: "8002"
        - name: LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: bracket-irl-config
              key: LOG_LEVEL
        - name: STORAGE_TYPE
          valueFrom:
            configMapKeyRef:
              name: bracket-irl-config
              key: STORAGE_TYPE
        - name: STORAGE_PATH
          valueFrom:
            configMapKeyRef:
              name: bracket-irl-config
              key: STORAGE_PATH
        - name: MAX_CONCURRENT_TASKS
          valueFrom:
            configMapKeyRef:
              name: bracket-irl-config
              key: MAX_CONCURRENT_TASKS
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: bracket-irl-secrets
              key: OPENAI_API_KEY
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: bracket-irl-secrets
              key: ANTHROPIC_API_KEY
              optional: true
        - name: OPENROUTER_API_KEY
          valueFrom:
            secretKeyRef:
              name: bracket-irl-secrets
              key: OPENROUTER_API_KEY
              optional: true
        volumeMounts:
        - name: bracket-data
          mountPath: /app/data
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8002
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8002
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: bracket-data
        persistentVolumeClaim:
          claimName: bracket-data-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: domain-analyzer-service
  namespace: bracket-irl
spec:
  selector:
    app: domain-analyzer-service
  ports:
  - port: 8002
    targetPort: 8002
  type: ClusterIP
