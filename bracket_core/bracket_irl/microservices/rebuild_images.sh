#!/bin/bash
set -e

# Set the platform to linux/amd64 (x86_64)
PLATFORM="linux/amd64"
REGISTRY="asia-south1-docker.pkg.dev/bracket-irl/bracket-irl"

echo "Building and pushing images for platform: $PLATFORM"

# Build and push the common base image first
echo "Building bracket_irl_common..."
cd bracket_irl_common
docker build --platform $PLATFORM -t bracket-irl/bracket_irl_common:latest .
docker tag bracket-irl/bracket_irl_common:latest $REGISTRY/bracket_irl_common:latest
docker push $REGISTRY/bracket_irl_common:latest
cd ..

# Build and push each service
services=(
  "repo-mapper-service"
  "domain-analyzer-service"
  "file-domain-mapper-service"
  "domain-file-repomap-service"
  "diagram-generator-service"
  "orchestrator-service"
)

for service in "${services[@]}"; do
  echo "Building $service..."
  cd $service
  docker build --platform $PLATFORM -t bracket-irl/$service:latest .
  docker tag bracket-irl/$service:latest $REGISTRY/$service:latest
  docker push $REGISTRY/$service:latest
  cd ..
done

echo "All images have been rebuilt and pushed to $REGISTRY"
