# Bracket IRL Monitoring

This document describes the monitoring setup for the Bracket IRL microservices architecture.

## Overview

The monitoring setup consists of:

- **Prometheus**: For collecting and storing metrics
- **Grafana**: For visualizing metrics

## Prometheus

Prometheus is configured to scrape metrics from all microservices. Each service exposes a `/metrics` endpoint that Prometheus scrapes at regular intervals.

### Configuration

The Prometheus configuration is defined in `prometheus/prometheus.yml`:

```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'repo-mapper-service'
    static_configs:
      - targets: ['repo-mapper-service:8001']
    metrics_path: /metrics

  # ... other services
```

### Metrics

Each service exposes the following metrics:

- **Request Metrics**:
  - `service_requests_total`: Total number of requests
  - `service_request_latency_seconds`: Request latency in seconds

- **Job Metrics**:
  - `service_jobs_total`: Total number of jobs
  - `service_job_latency_seconds`: Job latency in seconds
  - `service_active_jobs`: Number of active jobs

- **LLM Metrics**:
  - `service_llm_requests_total`: Total number of LLM requests
  - `service_llm_tokens_total`: Total number of tokens processed by LLM

## Grafana

Grafana is configured to visualize the metrics collected by Prometheus.

### Configuration

The Grafana configuration consists of:

- **Data Source**: Configured in `grafana/provisioning/datasources/prometheus.yml`
- **Dashboard**: Configured in `grafana/provisioning/dashboards/bracket-irl-dashboard.json`

### Dashboards

The default dashboard includes:

- **Request Rate**: Number of requests per second
- **Request Latency**: Average request latency
- **Active Jobs**: Number of active jobs
- **Job Status**: Distribution of job statuses

## Usage

### Accessing Prometheus

Prometheus is accessible at `http://localhost:9090`.

### Accessing Grafana

Grafana is accessible at `http://localhost:3000`.

Default credentials:
- Username: `admin`
- Password: `admin`

## Adding Custom Metrics

To add custom metrics to a service:

1. Define the metric in the service's code:

```python
from bracket_irl.microservices.bracket_irl_common.bracket_irl_common.metrics import counter, gauge, histogram

# Define a counter
my_counter = counter(
    name="my_counter",
    description="My custom counter",
    labels={"service": "my-service"}
)

# Increment the counter
my_counter.inc()
```

2. Ensure the metric is exposed via the `/metrics` endpoint.

3. Add the metric to a Grafana dashboard.

## Alerting

Alerting can be configured in Grafana to notify when metrics exceed certain thresholds.

To configure alerts:

1. Open Grafana and navigate to the dashboard.
2. Edit a panel and go to the "Alert" tab.
3. Configure the alert conditions and notification channels.

## Troubleshooting

### Prometheus Not Scraping Metrics

If Prometheus is not scraping metrics from a service:

1. Check that the service is running and exposing the `/metrics` endpoint.
2. Check the Prometheus configuration in `prometheus/prometheus.yml`.
3. Check the Prometheus logs for any errors.

### Grafana Not Showing Metrics

If Grafana is not showing metrics:

1. Check that Prometheus is scraping the metrics.
2. Check the Grafana data source configuration.
3. Check the Grafana dashboard configuration.

## References

- [Prometheus Documentation](https://prometheus.io/docs/introduction/overview/)
- [Grafana Documentation](https://grafana.com/docs/grafana/latest/)
