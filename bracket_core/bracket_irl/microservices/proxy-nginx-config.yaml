apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: bracket-irl
data:
  default.conf: |
    server {
      listen 80;
      server_name _;

      # Root path handler
      location = / {
        return 200 'Bracket IRL API Gateway - Available endpoints: /orchestrator, /repo-mapper, /domain-analyzer, /file-domain-mapper, /domain-file-repomap, /diagram-generator';
        add_header Content-Type text/plain;
      }

      # Orchestrator service
      location /orchestrator/ {
        proxy_pass http://orchestrator-service:8000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
      }

      # Special case for health check
      location = /orchestrator/health {
        proxy_pass http://orchestrator-service:8000/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
      }

      # Repo Mapper service
      location /repo-mapper/ {
        proxy_pass http://repo-mapper-service:8001/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
      }

      # Special case for health check
      location = /repo-mapper/health {
        proxy_pass http://repo-mapper-service:8001/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
      }

      # Domain Analyzer service
      location /domain-analyzer/ {
        proxy_pass http://domain-analyzer-service:8002/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
      }

      # Special case for health check
      location = /domain-analyzer/health {
        proxy_pass http://domain-analyzer-service:8002/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
      }

      # File Domain Mapper service
      location /file-domain-mapper/ {
        proxy_pass http://file-domain-mapper-service:8003/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
      }

      # Special case for health check
      location = /file-domain-mapper/health {
        proxy_pass http://file-domain-mapper-service:8003/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
      }

      # Domain File Repomap service
      location /domain-file-repomap/ {
        proxy_pass http://domain-file-repomap-service:8004/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
      }

      # Special case for health check
      location = /domain-file-repomap/health {
        proxy_pass http://domain-file-repomap-service:8004/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
      }

      # Diagram Generator service
      location /diagram-generator/ {
        proxy_pass http://diagram-generator-service:8005/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
      }

      # Special case for health check
      location = /diagram-generator/health {
        proxy_pass http://diagram-generator-service:8005/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
      }
    }
