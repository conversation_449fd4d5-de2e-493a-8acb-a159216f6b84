"""
Hierarchical Domain Taxonomy Mapper for Codebase

This module extends the DomainFileTaxonomyMapper to include hierarchical diagrams.
It provides functionality to:
1. Read domain_analysis.yaml to get the hierarchical structure of domains
2. Read domain_file_repomap.json to get the file mappings for each domain
3. Read the generated mermaid diagrams from both:
   - The leaf-level diagrams directory
   - The hierarchical diagrams directory
4. Combine all this information into a hierarchical JSON structure
5. Save this JSON structure to a file

It can be used as:
1. A standalone script to process domain analysis, file mappings, and diagrams
2. A module that can be integrated into the repository analysis flow
"""

import os
import yaml
import json
import logging
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, field
import re

from bracket_core.bracket_irl.bracket_domain_file_taxonomy import (
    DomainTaxonomyNode,
    DomainTaxonomyMapperResult,
    DomainFileTaxonomyMapper,
    logger
)

class HierarchicalDomainTaxonomyMapper(DomainFileTaxonomyMapper):
    """
    Maps domain taxonomy from domain analysis, file mappings, and diagrams.
    Extends DomainFileTaxonomyMapper to include hierarchical diagrams.

    This class reads domain analysis YAML, domain file repomap JSON, and mermaid diagrams
    from both leaf-level and hierarchical directories, and combines them into a hierarchical JSON structure.
    """

    def __init__(
        self,
        domain_analysis_yaml_path: str,
        domain_file_repomap_path: str,
        domain_diagrams_dir: str,
        hierarchical_diagrams_dir: str,
        output_json_path: str,
        diagram_name_mapping_path: Optional[str] = None,
    ):
        """
        Initialize the hierarchical domain taxonomy mapper.

        Args:
            domain_analysis_yaml_path: Path to the domain analysis YAML file
            domain_file_repomap_path: Path to the domain file repomap JSON file
            domain_diagrams_dir: Directory containing the leaf-level mermaid diagrams
            hierarchical_diagrams_dir: Directory containing the hierarchical mermaid diagrams
            output_json_path: Path to save the output JSON file
            diagram_name_mapping_path: Path to the diagram name mapping JSON file (optional)
        """
        # Initialize the parent class
        super().__init__(
            domain_analysis_yaml_path=domain_analysis_yaml_path,
            domain_file_repomap_path=domain_file_repomap_path,
            domain_diagrams_dir=domain_diagrams_dir,
            output_json_path=output_json_path,
            diagram_name_mapping_path=diagram_name_mapping_path,
        )

        # Add hierarchical diagrams directory
        self.hierarchical_diagrams_dir = hierarchical_diagrams_dir

        # Initialize additional data structures for hierarchical diagrams
        self.hierarchical_diagrams = {}

    def read_hierarchical_diagrams(self) -> Dict[str, Dict[str, str]]:
        """
        Read the hierarchical mermaid diagrams from the hierarchical diagrams directory.

        Returns:
            Dictionary mapping domain paths to hierarchical diagram content and file path
        """
        logger.info(f"Reading hierarchical mermaid diagrams from: {self.hierarchical_diagrams_dir}")
        hierarchical_diagrams = {}

        try:
            if not os.path.exists(self.hierarchical_diagrams_dir):
                logger.warning(f"Hierarchical diagrams directory not found: {self.hierarchical_diagrams_dir}")
                return hierarchical_diagrams

            # Get all .md files in the hierarchical diagrams directory
            diagram_files = [f for f in os.listdir(self.hierarchical_diagrams_dir)
                            if f.endswith('.md')]

            logger.info(f"Found {len(diagram_files)} hierarchical diagram files in directory")

            for diagram_file in diagram_files:
                # Convert filename to domain path by replacing underscores with spaces and arrows
                domain_path = diagram_file.replace('_', ' ').replace('.md', '')

                # Replace commas followed by spaces with commas only (to handle special characters)
                domain_path = domain_path.replace(', ', ',')

                # Read the diagram content
                diagram_path = os.path.join(self.hierarchical_diagrams_dir, diagram_file)
                with open(diagram_path, 'r') as f:
                    diagram_content = f.read()

                hierarchical_diagrams[domain_path] = {
                    'content': diagram_content,
                    'path': diagram_path,
                    'is_hierarchical': True
                }

                logger.debug(f"Loaded hierarchical diagram: {domain_path}")

            logger.info(f"Successfully loaded {len(hierarchical_diagrams)} hierarchical mermaid diagrams")
            return hierarchical_diagrams
        except Exception as e:
            logger.error(f"Error reading hierarchical mermaid diagrams: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return {}

    def read_domain_diagrams(self) -> Dict[str, Dict[str, str]]:
        """
        Read the mermaid diagrams from both the leaf-level and hierarchical diagrams directories.

        Returns:
            Dictionary mapping domain paths to diagram content and file path
        """
        # First, read the leaf-level diagrams using the parent class method
        leaf_diagrams = super().read_domain_diagrams()

        # Then, read the hierarchical diagrams
        hierarchical_diagrams = self.read_hierarchical_diagrams()

        # Combine the two dictionaries
        combined_diagrams = {**leaf_diagrams}

        # Store hierarchical diagrams separately for later use
        self.hierarchical_diagrams = hierarchical_diagrams

        return combined_diagrams

    def map_hierarchical_diagrams_to_taxonomy(self, root: DomainTaxonomyNode) -> None:
        """
        Map hierarchical diagrams to the taxonomy tree using keyword-based similarity.

        Args:
            root: Root node of the taxonomy tree
        """
        logger.info("Mapping hierarchical diagrams to taxonomy tree")

        # For each hierarchical diagram, find the corresponding node in the taxonomy tree
        matched_diagrams = 0

        for domain_path, diagram_info in self.hierarchical_diagrams.items():
            # Try direct match first
            if domain_path in self.path_to_node:
                node = self.path_to_node[domain_path]
                node.hierarchical_diagram = diagram_info['content']
                node.hierarchical_diagram_path = diagram_info['path']

                # Extract filename from path as diagram name
                node.hierarchical_diagram_name = os.path.basename(diagram_info['path'])

                matched_diagrams += 1
                logger.debug(f"Mapped hierarchical diagram to {domain_path} (direct match)")
            else:
                # Try keyword-based matching
                best_match = None
                best_score = 0

                for node_path in self.path_to_node.keys():
                    # Calculate similarity score
                    score = self.keyword_similarity(domain_path, node_path)

                    # If this is the best match so far, store it
                    if score > best_score:
                        best_score = score
                        best_match = node_path

                # Use the best match if it's above a threshold
                if best_match and best_score > 0.5:  # Threshold for matching
                    node = self.path_to_node[best_match]
                    node.hierarchical_diagram = diagram_info['content']
                    node.hierarchical_diagram_path = diagram_info['path']

                    # Extract filename from path as diagram name
                    node.hierarchical_diagram_name = os.path.basename(diagram_info['path'])

                    matched_diagrams += 1
                    logger.debug(f"Mapped hierarchical diagram to {best_match} (keyword match with score {best_score:.2f})")
                else:
                    logger.warning(f"Hierarchical diagram path not found in taxonomy tree: {domain_path}")

        logger.info(f"Successfully mapped {matched_diagrams} out of {len(self.hierarchical_diagrams)} hierarchical diagrams to taxonomy tree")

    def taxonomy_tree_to_json(self, root: DomainTaxonomyNode) -> Dict[str, Any]:
        """
        Convert the taxonomy tree to a JSON structure, including hierarchical diagrams.

        Args:
            root: Root node of the taxonomy tree

        Returns:
            Dictionary representing the taxonomy tree in JSON format
        """
        logger.info("Converting taxonomy tree to JSON structure (including hierarchical diagrams)")

        def node_to_dict(node: DomainTaxonomyNode) -> Dict[str, Any]:
            """Convert a node to a dictionary."""
            result = {
                "name": node.name,
                "full_path": node.full_path
            }

            if node.children:
                result["children"] = [node_to_dict(child) for child in node.children]

            if node.files:  # List of file paths
                result["files"] = node.files

            if node.file_contents:  # Dictionary of file paths to content
                result["file_contents"] = node.file_contents

            # Add leaf-level diagram if available
            if node.diagram:
                result["diagram"] = node.diagram

            if node.diagram_path:
                result["diagram_path"] = node.diagram_path

            if node.diagram_name:
                result["diagram_name"] = node.diagram_name
                logger.debug(f"Added diagram_name {node.diagram_name} to JSON for node {node.name}")

            # Add hierarchical diagram if available
            if node.hierarchical_diagram:
                result["hierarchical_diagram"] = node.hierarchical_diagram

            if node.hierarchical_diagram_path:
                result["hierarchical_diagram_path"] = node.hierarchical_diagram_path

            if node.hierarchical_diagram_name:
                result["hierarchical_diagram_name"] = node.hierarchical_diagram_name
                logger.debug(f"Added hierarchical_diagram_name {node.hierarchical_diagram_name} to JSON for node {node.name}")

            return result

        return node_to_dict(root)

    def map_taxonomy(self) -> DomainTaxonomyMapperResult:
        """
        Map domain taxonomy from domain analysis, file mappings, and diagrams.

        Returns:
            DomainTaxonomyMapperResult containing the mapping results
        """
        result = DomainTaxonomyMapperResult()

        try:
            # Read domain analysis, file mappings, and diagrams
            logger.info("Step 1: Reading domain analysis YAML")
            domain_data = self.read_domain_analysis()

            logger.info("Step 2: Reading domain file repomap JSON")
            self.domain_files = self.read_domain_file_repomap()
            logger.info(f"Found {len(self.domain_files)} domains with file mappings")

            logger.info("Step 3: Reading domain diagrams (leaf-level and hierarchical)")
            self.domain_diagrams = self.read_domain_diagrams()
            logger.info(f"Found {len(self.domain_diagrams)} leaf-level diagrams and {len(self.hierarchical_diagrams)} hierarchical diagrams")

            # Build taxonomy tree
            logger.info("Step 4: Building taxonomy tree")
            root = self.build_taxonomy_tree(domain_data)
            logger.info(f"Built taxonomy tree with {len(self.path_to_node)} nodes")

            # Map files, leaf-level diagrams, and hierarchical diagrams to taxonomy
            logger.info("Step 5: Mapping files to taxonomy")
            self.map_files_to_taxonomy(root, self.domain_files)

            logger.info("Step 6: Mapping leaf-level diagrams to taxonomy")
            self.map_diagrams_to_taxonomy(root, self.domain_diagrams)

            logger.info("Step 7: Mapping hierarchical diagrams to taxonomy")
            self.map_hierarchical_diagrams_to_taxonomy(root)

            # Find combined diagrams and codebase overview diagram
            logger.info("Step 8: Finding combined diagrams")
            combined_diagrams = self.find_combined_diagrams()

            # Convert taxonomy tree to JSON
            logger.info("Step 9: Converting taxonomy tree to JSON")
            taxonomy_json = self.taxonomy_tree_to_json(root)

            # Add combined diagrams to the JSON structure
            logger.info("Step 10: Adding combined diagrams to JSON structure")
            taxonomy_json = self.add_combined_diagrams_to_json(taxonomy_json, combined_diagrams)

            # Count diagrams in the taxonomy tree
            leaf_diagram_count = 0
            hierarchical_diagram_count = 0

            def count_diagrams(node_dict):
                nonlocal leaf_diagram_count, hierarchical_diagram_count

                # Count leaf-level diagrams
                if 'diagram_path' in node_dict and node_dict['diagram_path']:
                    leaf_diagram_count += 1

                # Count hierarchical diagrams
                if 'hierarchical_diagram_path' in node_dict and node_dict['hierarchical_diagram_path']:
                    hierarchical_diagram_count += 1

                # Recursively count children
                if 'children' in node_dict:
                    for child in node_dict['children']:
                        count_diagrams(child)

            count_diagrams(taxonomy_json)
            logger.info(f"Taxonomy tree contains {leaf_diagram_count} leaf diagrams and {hierarchical_diagram_count} hierarchical diagrams")

            # Save taxonomy JSON
            logger.info("Step 11: Saving taxonomy JSON")
            self.save_taxonomy_json(taxonomy_json)

            # Set result
            result.taxonomy_json = taxonomy_json
            result.output_path = self.output_json_path

            logger.info("Hierarchical domain taxonomy mapping completed successfully")

        except Exception as e:
            logger.error(f"Error mapping hierarchical domain taxonomy: {e}")
            import traceback
            logger.error(traceback.format_exc())
            result.success = False
            result.error_message = str(e)

        return result
