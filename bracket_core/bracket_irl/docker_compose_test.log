2025-05-11 15:51:03,052 - docker_compose_test - INFO - Starting Docker Compose test...
2025-05-11 15:51:03,052 - docker_compose_test - INFO - Starting services using Docker Compose...
2025-05-11 15:51:04,493 - docker_compose_test - INFO - Services started successfully
2025-05-11 15:51:04,493 - docker_compose_test - INFO - Waiting for services to be healthy...
2025-05-11 15:51:04,493 - docker_compose_test - INFO - Checking container status...
2025-05-11 15:51:04,592 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=starting
2025-05-11 15:51:04,592 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=running, Health=starting
2025-05-11 15:51:04,592 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=running, Health=healthy
2025-05-11 15:51:04,592 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=running, Health=starting
2025-05-11 15:51:04,592 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 15:51:04,592 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=starting
2025-05-11 15:51:04,593 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 15:51:04,593 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=starting
2025-05-11 15:51:04,593 - docker_compose_test - INFO - Checking services health...
2025-05-11 15:51:04,600 - docker_compose_test - ERROR - Error checking Orchestrator service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 15:51:04,606 - docker_compose_test - ERROR - Error checking Repository Mapper service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 15:51:04,615 - docker_compose_test - ERROR - Error checking Domain Analyzer service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 15:51:04,618 - docker_compose_test - ERROR - Error checking File-Domain Mapper service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 15:51:04,624 - docker_compose_test - INFO - Domain-File Repomap service is healthy
2025-05-11 15:51:04,626 - docker_compose_test - ERROR - Error checking Diagram Generator service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 15:51:04,628 - docker_compose_test - INFO - Prometheus service is healthy
2025-05-11 15:51:04,630 - docker_compose_test - INFO - Grafana service is healthy
2025-05-11 15:51:04,630 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 0s)
2025-05-11 15:51:09,631 - docker_compose_test - INFO - Checking container status...
2025-05-11 15:51:09,713 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=healthy
2025-05-11 15:51:09,713 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=running, Health=starting
2025-05-11 15:51:09,713 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=running, Health=healthy
2025-05-11 15:51:09,713 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=running, Health=healthy
2025-05-11 15:51:09,713 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 15:51:09,713 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=healthy
2025-05-11 15:51:09,713 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 15:51:09,713 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=healthy
2025-05-11 15:51:09,713 - docker_compose_test - INFO - Checking services health...
2025-05-11 15:51:09,716 - docker_compose_test - INFO - Orchestrator service is healthy
2025-05-11 15:51:09,719 - docker_compose_test - INFO - Repository Mapper service is healthy
2025-05-11 15:51:09,720 - docker_compose_test - ERROR - Error checking Domain Analyzer service health: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-05-11 15:51:09,722 - docker_compose_test - INFO - File-Domain Mapper service is healthy
2025-05-11 15:51:09,724 - docker_compose_test - INFO - Domain-File Repomap service is healthy
2025-05-11 15:51:09,726 - docker_compose_test - INFO - Diagram Generator service is healthy
2025-05-11 15:51:09,727 - docker_compose_test - INFO - Prometheus service is healthy
2025-05-11 15:51:09,729 - docker_compose_test - INFO - Grafana service is healthy
2025-05-11 15:51:09,729 - docker_compose_test - INFO - Waiting for services to be healthy... (elapsed: 5s)
2025-05-11 15:51:14,734 - docker_compose_test - INFO - Checking container status...
2025-05-11 15:51:14,828 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=healthy
2025-05-11 15:51:14,828 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=running, Health=starting
2025-05-11 15:51:14,828 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=running, Health=healthy
2025-05-11 15:51:14,828 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=running, Health=healthy
2025-05-11 15:51:14,828 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 15:51:14,828 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=healthy
2025-05-11 15:51:14,828 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 15:51:14,828 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=healthy
2025-05-11 15:51:14,829 - docker_compose_test - INFO - Checking services health...
2025-05-11 15:51:14,831 - docker_compose_test - INFO - Orchestrator service is healthy
2025-05-11 15:51:14,834 - docker_compose_test - INFO - Repository Mapper service is healthy
2025-05-11 15:51:14,838 - docker_compose_test - INFO - Domain Analyzer service is healthy
2025-05-11 15:51:14,841 - docker_compose_test - INFO - File-Domain Mapper service is healthy
2025-05-11 15:51:14,843 - docker_compose_test - INFO - Domain-File Repomap service is healthy
2025-05-11 15:51:14,845 - docker_compose_test - INFO - Diagram Generator service is healthy
2025-05-11 15:51:14,847 - docker_compose_test - INFO - Prometheus service is healthy
2025-05-11 15:51:14,849 - docker_compose_test - INFO - Grafana service is healthy
2025-05-11 15:51:14,849 - docker_compose_test - INFO - All services are healthy
2025-05-11 15:51:14,849 - docker_compose_test - INFO - Checking container status...
2025-05-11 15:51:14,920 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: Status=running, Health=healthy
2025-05-11 15:51:14,920 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: Status=running, Health=starting
2025-05-11 15:51:14,920 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: Status=running, Health=healthy
2025-05-11 15:51:14,920 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: Status=running, Health=healthy
2025-05-11 15:51:14,920 - docker_compose_test - INFO - Container microservices-grafana-1: Status=running, Health=
2025-05-11 15:51:14,920 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: Status=running, Health=healthy
2025-05-11 15:51:14,920 - docker_compose_test - INFO - Container microservices-prometheus-1: Status=running, Health=
2025-05-11 15:51:14,920 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: Status=running, Health=healthy
2025-05-11 15:51:14,920 - docker_compose_test - INFO - Checking services health...
2025-05-11 15:51:14,923 - docker_compose_test - INFO - Orchestrator service is healthy
2025-05-11 15:51:14,925 - docker_compose_test - INFO - Repository Mapper service is healthy
2025-05-11 15:51:14,928 - docker_compose_test - INFO - Domain Analyzer service is healthy
2025-05-11 15:51:14,930 - docker_compose_test - INFO - File-Domain Mapper service is healthy
2025-05-11 15:51:14,932 - docker_compose_test - INFO - Domain-File Repomap service is healthy
2025-05-11 15:51:14,934 - docker_compose_test - INFO - Diagram Generator service is healthy
2025-05-11 15:51:14,936 - docker_compose_test - INFO - Prometheus service is healthy
2025-05-11 15:51:14,938 - docker_compose_test - INFO - Grafana service is healthy
2025-05-11 15:51:14,938 - docker_compose_test - INFO - Checking container resource usage...
2025-05-11 15:51:17,495 - docker_compose_test - INFO - Container microservices-orchestrator-service-1: CPU=0.32%, Memory=42.88MiB / 31.29GiB, Network=4.95kB / 3.29kB, IO=0B / 0B
2025-05-11 15:51:17,495 - docker_compose_test - INFO - Container microservices-repo-mapper-service-1: CPU=0.39%, Memory=40.71MiB / 31.29GiB, Network=4.98kB / 3.19kB, IO=0B / 0B
2025-05-11 15:51:17,495 - docker_compose_test - INFO - Container microservices-file-domain-mapper-service-1: CPU=0.34%, Memory=42.68MiB / 31.29GiB, Network=4.45kB / 2.37kB, IO=0B / 0B
2025-05-11 15:51:17,495 - docker_compose_test - INFO - Container microservices-domain-analyzer-service-1: CPU=0.31%, Memory=84.79MiB / 31.29GiB, Network=1.77MB / 28.7kB, IO=0B / 0B
2025-05-11 15:51:17,495 - docker_compose_test - INFO - Container microservices-diagram-generator-service-1: CPU=0.73%, Memory=40.32MiB / 31.29GiB, Network=4.4kB / 2.24kB, IO=0B / 0B
2025-05-11 15:51:17,495 - docker_compose_test - INFO - Container microservices-grafana-1: CPU=0.03%, Memory=149.9MiB / 31.29GiB, Network=18.7kB / 5.09kB, IO=78.6MB / 147kB
2025-05-11 15:51:17,495 - docker_compose_test - INFO - Container microservices-domain-file-repomap-service-1: CPU=0.35%, Memory=84.7MiB / 31.29GiB, Network=1.78MB / 23.8kB, IO=0B / 1.95MB
2025-05-11 15:51:17,495 - docker_compose_test - INFO - Container microservices-prometheus-1: CPU=0.00%, Memory=34.59MiB / 31.29GiB, Network=41.1kB / 33.9kB, IO=303kB / 49.2kB
2025-05-11 15:51:17,496 - docker_compose_test - INFO - Docker Compose test completed successfully
2025-05-11 15:51:17,496 - docker_compose_test - INFO - Stopping services using Docker Compose...
2025-05-11 15:51:18,960 - docker_compose_test - INFO - Services stopped successfully
2025-05-11 15:51:18,960 - docker_compose_test - INFO - Docker Compose test passed
