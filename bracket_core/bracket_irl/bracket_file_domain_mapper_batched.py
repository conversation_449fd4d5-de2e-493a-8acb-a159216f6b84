"""
Batched File-to-Domain Mapper for Codebase

This module provides functionality to:
1. Read the repository map to extract file paths and their functions/classes
2. Read the domain hierarchy from domain analysis YAML
3. Process files in batches based on file count (~50 files per batch)
4. Use an LLM to classify files into leaf domains based on both file paths and their functions/classes
5. Merge and save the results

It can be used as:
1. A standalone script to process repository map and domain analysis files
2. A module that can be integrated into the repository analysis flow
"""

import os
import yaml
import json
import logging
import argparse
import asyncio
import aiohttp
import time
from typing import Dict, List, Set, Tuple, Any, Optional, Union
from dataclasses import dataclass, field
from pathlib import Path
import re
from pydantic import BaseModel, Field

# Import OpenRouter client and API key management
from bracket_core.llm.get_client import get_openrouter_client
from bracket_core.llm.api_keys import get_openai_api_key, get_openrouter_api_key

# No need for complex token counting imports since we're using a simple line-based approach

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def count_tokens(text: str) -> int:
    """
    Count tokens in text using a fast approximation based on line count.

    Args:
        text: The text to count tokens for

    Returns:
        Approximate token count
    """
    # Fast approximation: count lines and multiply by 10
    # This is much faster than using tiktoken for large texts
    line_count = text.count('\n') + 1
    return line_count * 10

@dataclass
class FileDomainMapperResult:
    """Result of mapping files to domains."""
    success: bool = True
    error_message: str = ""
    domain_file_mappings: Dict[str, List[str]] = field(default_factory=dict)
    output_path: Optional[str] = None
    unclassified_files: List[str] = field(default_factory=list)
    first_pass_mappings: Dict[str, List[str]] = field(default_factory=dict)
    second_pass_mappings: Dict[str, List[str]] = field(default_factory=dict)
    first_pass_accuracy: float = 0.0
    second_pass_skipped: bool = False

class DomainFileMapping(BaseModel):
    """Pydantic model for domain-to-files mapping schema."""
    model_config = {
        "extra": "forbid",  # Equivalent to additionalProperties: false
    }

# Define a dynamic schema generator for domain file mappings
def create_domain_file_mapping_schema(leaf_domains: List[str]) -> type:
    """
    Create a Pydantic model dynamically based on the available leaf domains.

    Args:
        leaf_domains: List of leaf domain paths

    Returns:
        A Pydantic model class with domain paths as fields
    """
    # Create field annotations
    annotations = {}
    field_definitions = {}

    for domain in leaf_domains:
        # Clean domain name to make it a valid Python identifier if needed
        clean_domain = domain.replace('/', '_').replace('-', '_')
        # Each domain field is a list of strings (file paths)
        annotations[clean_domain] = List[str]
        field_definitions[clean_domain] = Field(
            default_factory=list,
            description=f"Files belonging to the {domain} domain",
            alias=domain  # Use the original domain path as the JSON field name
        )

    # Create the model class
    model_dict = {
        '__annotations__': annotations,
        **field_definitions,
        '__module__': 'bracket_core.exp_repomap.exp_file_domain_mapper_batched',
        'model_config': {
            'extra': 'forbid',  # No additional properties
            'populate_by_name': True,  # Allow using the original field names (Pydantic v2 syntax)
        }
    }

    return type('DynamicDomainFileMapping', (BaseModel,), model_dict)

class StatusTracker:
    """Tracks API request status."""

    def __init__(self, max_requests_per_minute, max_tokens_per_minute):
        """Initialize the status tracker."""
        self.max_requests_per_minute = max_requests_per_minute
        self.max_tokens_per_minute = max_tokens_per_minute
        self.requests_past_minute = 0
        self.tokens_past_minute = 0
        self.last_request_time = 0
        self.request_times = []
        self.token_counts = []

    def update_usage(self, tokens_used):
        """Update usage after a request."""
        self.requests_past_minute += 1
        self.tokens_past_minute += tokens_used

        # Record request time and token count
        current_time = time.time()
        self.request_times.append(current_time)
        self.token_counts.append(tokens_used)

        # Remove entries older than 1 minute
        cutoff_time = current_time - 60
        while self.request_times and self.request_times[0] < cutoff_time:
            self.requests_past_minute -= 1
            self.tokens_past_minute -= self.token_counts[0]
            self.request_times.pop(0)
            self.token_counts.pop(0)

        self.last_request_time = current_time

    def get_delay_time(self):
        """Get delay time to stay within rate limits."""
        # If we're under the limits, no delay needed
        if (self.requests_past_minute < self.max_requests_per_minute and
            self.tokens_past_minute < self.max_tokens_per_minute):
            return 0

        # Calculate delay based on request rate
        if self.requests_past_minute >= self.max_requests_per_minute:
            requests_delay = 60 - (time.time() - self.request_times[0])
        else:
            requests_delay = 0

        # Calculate delay based on token rate
        if self.tokens_past_minute >= self.max_tokens_per_minute:
            tokens_delay = 60 - (time.time() - self.request_times[0])
        else:
            tokens_delay = 0

        return max(requests_delay, tokens_delay)

class APIRequest:
    """Handles API requests with rate limiting."""

    def __init__(self, status_tracker, api_key, model, use_openrouter=False, openrouter_base_url="https://openrouter.ai/api/v1"):
        """Initialize the API request handler."""
        self.status_tracker = status_tracker
        self.api_key = api_key
        self.model = model
        self.use_openrouter = use_openrouter
        self.openrouter_base_url = openrouter_base_url

    async def call_api(self, messages, temperature=0.0, max_tokens=None, json_mode=True,
                  json_schema=None, retry_count=0, max_retries=3):
        """
        Call the API with rate limiting and error handling.

        Args:
            messages: List of message objects for the conversation
            temperature: Temperature setting for the model
            max_tokens: Maximum tokens to generate
            json_mode: Whether to use JSON mode (basic) or structured outputs (with schema)
            json_schema: Optional Pydantic model to use for structured outputs
            retry_count: Current retry attempt
            max_retries: Maximum number of retries

        Returns:
            The model's response content
        """
        # Calculate delay time to stay within rate limits
        delay_time = self.status_tracker.get_delay_time()
        if delay_time > 0:
            logger.info(f"Rate limit approaching. Waiting {delay_time:.2f} seconds...")
            await asyncio.sleep(delay_time)

        # Prepare the API request
        if self.use_openrouter:
            url = f"{self.openrouter_base_url}/chat/completions"
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "HTTP-Referer": "https://bracket.dev"
            }
        else:
            url = "https://api.openai.com/v1/chat/completions"
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

        data = {
            "model": self.model,
            "messages": messages,
        }
        if not self.model.startswith('o'):
            data["temperature"] = temperature

        # Add reasoning capability controller for OpenAI models that start with 'o'
        # if not self.use_openrouter and self.model.startswith('o'):
        #     data["reasoning"] = {"effort": "medium"}
        #     logger.info(f"Using reasoning capability controller with effort=low for model {self.model}")

        # For now, just use basic JSON mode for all models
        # The structured output with schema is more complex and requires using the OpenAI client directly
        if json_mode:
            # Use basic JSON mode
            data["response_format"] = {"type": "json_object"}
            logger.info("Using basic JSON mode")

        if max_tokens:
            data["max_tokens"] = max_tokens

        # Make the API request with robust error handling
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=data) as response:
                    # First check the HTTP status code
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"API request failed with status {response.status}: {error_text}")

                        # If we haven't exceeded max retries and it's a server error (5xx), retry
                        if retry_count < max_retries and 500 <= response.status < 600:
                            retry_delay = 2 ** retry_count  # Exponential backoff
                            logger.info(f"Retrying in {retry_delay} seconds (attempt {retry_count + 1}/{max_retries})...")
                            await asyncio.sleep(retry_delay)
                            return self.call_api(messages, temperature, max_tokens, json_mode, json_schema, retry_count + 1, max_retries)

                        return None

                    # Try to parse the response as JSON
                    try:
                        response_data = await response.json()

                        # Extract the response content
                        content = response_data["choices"][0]["message"]["content"]

                        # Update usage statistics
                        usage = response_data.get("usage", {})
                        total_tokens = usage.get("total_tokens", 0)
                        self.status_tracker.update_usage(total_tokens)

                        logger.debug(f"API request successful. Used {total_tokens} tokens.")
                        return content

                    except (json.JSONDecodeError, KeyError, aiohttp.ContentTypeError) as e:
                        # Handle JSON parsing errors
                        error_text = await response.text()
                        logger.error(f"Failed to parse API response as JSON: {str(e)}")
                        logger.error(f"Response content: {error_text[:500]}...")  # Log first 500 chars

                        # If we haven't exceeded max retries, retry
                        if retry_count < max_retries:
                            retry_delay = 2 ** retry_count  # Exponential backoff
                            logger.info(f"Retrying in {retry_delay} seconds (attempt {retry_count + 1}/{max_retries})...")
                            await asyncio.sleep(retry_delay)
                            return self.call_api(messages, temperature, max_tokens, json_mode, json_schema, retry_count + 1, max_retries)

                        # If all retries failed, return the raw text as fallback
                        logger.warning("All retries failed. Returning raw response text as fallback.")
                        return error_text

        except Exception as e:
            logger.error(f"Unexpected error in API call: {str(e)}")

            # If we haven't exceeded max retries, retry
            if retry_count < max_retries:
                retry_delay = 2 ** retry_count  # Exponential backoff
                logger.info(f"Retrying in {retry_delay} seconds (attempt {retry_count + 1}/{max_retries})...")
                await asyncio.sleep(retry_delay)
                return self.call_api(messages, temperature, max_tokens, json_mode, json_schema, retry_count + 1, max_retries)

            return None

class FileDomainMapper:
    """
    Maps files to domains based on repository map and domain hierarchy.

    This class reads a repository map file and domain analysis YAML,
    and uses an LLM to map files to leaf domains.
    """

    def __init__(
        self,
        repomap_path: str,
        domain_analysis_yaml_path: str,
        output_path: str,
        api_key: Optional[str] = None,
        model: str = "gpt-4o-mini",
        max_requests_per_minute: float = 50,
        max_tokens_per_minute: float = 100000,
        temperature: float = 0.0,
        use_openrouter: bool = False,
        openrouter_base_url: str = "https://openrouter.ai/api/v1",
        max_files_per_batch: int = 50,
    ):
        """
        Initialize the file domain mapper.

        Args:
            repomap_path: Path to the repository map file (text format or JSON)
                          For JSON format, keys should be file paths and values should be lists of strings (code lines)
            domain_analysis_yaml_path: Path to the domain analysis YAML file
            output_path: Path to save the file-to-domain mappings YAML
            api_key: API key (OpenAI or OpenRouter depending on use_openrouter)
            model: Model to use (OpenAI or OpenRouter model ID)
            max_requests_per_minute: Rate limit for API requests
            max_tokens_per_minute: Token rate limit for API
            temperature: Temperature setting for the model (lower = more deterministic)
            use_openrouter: Whether to use OpenRouter instead of OpenAI
            openrouter_base_url: Base URL for OpenRouter API
            max_files_per_batch: Maximum number of files to include in a batch (default: 50)
        """
        self.repomap_path = repomap_path
        self.domain_analysis_yaml_path = domain_analysis_yaml_path
        self.output_path = output_path

        # API settings
        self.model = model
        self.temperature = temperature
        self.use_openrouter = use_openrouter
        self.openrouter_base_url = openrouter_base_url

        # Batch settings
        self.max_files_per_batch = max_files_per_batch

        # Get the appropriate API key based on the service being used
        if self.use_openrouter:
            self.api_key = get_openrouter_api_key(provided_key=api_key)
        else:
            self.api_key = get_openai_api_key(provided_key=api_key)

        # Initialize status tracker and API request handler
        self.status_tracker = StatusTracker(max_requests_per_minute, max_tokens_per_minute)
        self.api_request = APIRequest(
            self.status_tracker,
            self.api_key,
            self.model,
            self.use_openrouter,
            self.openrouter_base_url
        )

        # Initialize OpenRouter client if needed
        self.openrouter_client = None
        if self.use_openrouter:
            try:
                # For file domain mapping, use Gemini model with large context window
                openrouter_model = "google/gemini-2.5-pro-preview"
                self.openrouter_client = get_openrouter_client(
                    # API key will be handled by the client
                    model=openrouter_model,  # Override with Gemini model
                    max_tokens=60096,  # Use larger context window
                    temperature=self.temperature,
                    base_url=self.openrouter_base_url,
                    request_timeout=60  # Increase timeout to 60 seconds
                )
                logger.info(f"File Domain Mapper using OpenRouter with model: {openrouter_model}")
            except Exception as e:
                logger.error(f"Failed to initialize OpenRouter client: {e}")
                logger.warning("Falling back to OpenAI API")
                self.use_openrouter = False

        # Initialize domain hierarchy data structures
        self.domain_path_to_name = {}
        self.domain_name_to_path = {}
        self.domain_path_to_description = {}
        self.domain_path_to_explanation = {}  # Store explanations for leaf domains
        self.leaf_domains = []

    def read_domain_yaml(self) -> Dict[str, Any]:
        """
        Read the domain analysis YAML file.

        Returns:
            Dictionary containing domain analysis data
        """
        try:
            with open(self.domain_analysis_yaml_path, 'r') as f:
                domain_data = yaml.safe_load(f)
            logger.info(f"Successfully read domain analysis YAML from: {self.domain_analysis_yaml_path}")
            return domain_data
        except Exception as e:
            logger.error(f"Error reading domain analysis YAML: {e}")
            raise

    def extract_file_paths(self) -> List[str]:
        """
        Extract file paths from the repository map.

        Returns:
            List of file paths
        """
        try:
            # Check if the repomap is in JSON format
            if self.repomap_path.endswith('.json'):
                logger.info(f"Detected JSON format for repository map: {self.repomap_path}")
                with open(self.repomap_path, 'r') as f:
                    json_data = json.load(f)

                # In JSON format, keys are file paths
                file_paths = list(json_data.keys())
                logger.info(f"Extracted {len(file_paths)} file paths from JSON repository map")
                return file_paths

            # Otherwise, process as text format
            file_paths = []
            current_file = None

            with open(self.repomap_path, 'r') as f:
                for line in f:
                    line = line.strip()

                    # Skip empty lines
                    if not line:
                        continue

                    # Check if this is a file path line
                    if line.startswith('│') and line.endswith(':'):
                        # Extract file path
                        current_file = line[1:-1]  # Remove the │ prefix and : suffix
                        if current_file and current_file not in file_paths:
                            file_paths.append(current_file)

            logger.info(f"Extracted {len(file_paths)} file paths from text repository map")
            return file_paths
        except Exception as e:
            logger.error(f"Error extracting file paths: {e}")
            raise

    def parse_repomap(self) -> Dict[str, List[str]]:
        """
        Parse the entire repository map including files and their functions/classes.

        Returns:
            Dictionary mapping file paths to lists of functions/classes
        """
        try:
            # Check if the repomap is in JSON format
            if self.repomap_path.endswith('.json'):
                logger.info(f"Parsing JSON repository map: {self.repomap_path}")
                with open(self.repomap_path, 'r') as f:
                    json_data = json.load(f)

                # In JSON format, keys are file paths and values are lists of strings (code lines)
                # We need to process these lists to extract function/class definitions
                repomap_structure = {}

                for file_path, code_lines in json_data.items():
                    repomap_structure[file_path] = []

                    # Process each line to extract function/class definitions
                    for line in code_lines:
                        line = line.strip()

                        # Skip empty lines or lines that don't look like function/class definitions
                        if not line:
                            continue

                        # Add the line as a function/class definition if it looks like one
                        # This is a simplified approach - we're assuming the JSON already contains
                        # the relevant function/class definitions as provided in the example
                        if (line.startswith('def ') or
                            line.startswith('class ') or
                            line.startswith('async def ') or
                            line.endswith(':') or
                            line.startswith('@')):

                            if line not in repomap_structure[file_path]:
                                repomap_structure[file_path].append(line)

                # Count total files and functions/classes
                total_files = len(repomap_structure)
                total_items = sum(len(items) for items in repomap_structure.values())

                logger.info(f"Parsed JSON repository map: {total_files} files with {total_items} functions/classes")
                return repomap_structure

            # Otherwise, process as text format
            repomap_structure = {}
            current_file = None

            with open(self.repomap_path, 'r') as f:
                for line in f:
                    line = line.strip()

                    # Skip empty lines or ellipsis markers
                    if not line or line == '⋮...' or line.startswith('⋮'):
                        continue

                    # Check if this is a file path line
                    if line.startswith('│') and line.endswith(':'):
                        # Extract file path
                        current_file = line[1:-1]  # Remove the │ prefix and : suffix
                        if current_file:
                            repomap_structure[current_file] = []

                    # If we have a current file and this line defines a function/class
                    elif current_file and line.startswith('│'):
                        # Extract function/class name
                        item = line[1:].strip()  # Remove the │ prefix
                        if item and item not in repomap_structure[current_file]:
                            repomap_structure[current_file].append(item)

            # Count total files and functions/classes
            total_files = len(repomap_structure)
            total_items = sum(len(items) for items in repomap_structure.values())

            logger.info(f"Parsed text repository map: {total_files} files with {total_items} functions/classes")
            return repomap_structure
        except Exception as e:
            logger.error(f"Error parsing repository map: {e}")
            raise

    def extract_domain_hierarchy(self, domain_data: Dict[str, Any]) -> None:
        """
        Extract the domain hierarchy from domain analysis data.

        Args:
            domain_data: Dictionary containing domain analysis data
        """
        logger.info("Extracting domain hierarchy")

        # Process domains recursively
        def process_domain(domain, parent_path=""):
            domain_name = domain.get('name', '')
            if not domain_name:
                return

            # Create the current domain path
            current_path = f"{parent_path}/{domain_name}" if parent_path else domain_name

            # Store domain information
            self.domain_path_to_name[current_path] = domain_name
            self.domain_name_to_path[domain_name] = current_path

            # Store explanation if it exists (for leaf domains)
            explanation = domain.get('explanation', '')
            if explanation:
                self.domain_path_to_explanation[current_path] = explanation

            # Generate and store domain description
            description = self._generate_domain_description(domain)
            self.domain_path_to_description[current_path] = description

            # Process subdomains
            subareas = domain.get('subareas', [])

            # If no subareas, this is a leaf domain
            if not subareas:
                self.leaf_domains.append(current_path)

            for subarea in subareas:
                process_domain(subarea, current_path)

        # Process top-level domains
        areas = domain_data.get('areas', [])
        for area in areas:
            process_domain(area)

        logger.info(f"Extracted {len(self.domain_path_to_name)} domains, {len(self.leaf_domains)} leaf domains")

    # Removed the format_domain_path_with_arrows method to keep notation consistent

    def _generate_domain_description(self, domain: Dict[str, Any]) -> str:
        """
        Generate a description for a domain based on its name, structure, and explanation.

        Args:
            domain: Domain dictionary

        Returns:
            Domain description
        """
        name = domain.get('name', '')
        subareas = domain.get('subareas', [])
        explanation = domain.get('explanation', '')

        # If this is a leaf domain with an explanation, use it
        if not subareas and explanation:
            return explanation

        # Otherwise, generate a description based on the domain structure
        if not subareas:
            return f"Domain for {name} related functionality"

        subarea_names = [subarea.get('name', '') for subarea in subareas if subarea.get('name', '')]
        if subarea_names:
            return f"Domain for {name} related functionality, including: {', '.join(subarea_names)}"
        else:
            return f"Domain for {name} related functionality"

    def get_top_level_domains(self) -> List[str]:
        """
        Get the list of top-level domains.

        Returns:
            List of top-level domain paths
        """
        return [path for path in self.domain_path_to_name.keys() if '/' not in path]

    def get_leaf_domains_for_top_domain(self, top_domain: str) -> List[str]:
        """
        Get the list of leaf domains under a top-level domain.

        Args:
            top_domain: Top-level domain path

        Returns:
            List of leaf domain paths
        """
        return [path for path in self.leaf_domains if path.startswith(top_domain)]

    def format_domain_hierarchy(self, top_domain: str) -> str:
        """
        Format the domain hierarchy under a top-level domain for the LLM prompt.

        Args:
            top_domain: Top-level domain path

        Returns:
            Formatted domain hierarchy
        """
        result = []

        # Helper function to format a domain and its children
        def format_domain(domain_path, level=0):
            domain_name = self.domain_path_to_name.get(domain_path, domain_path)
            description = self.domain_path_to_description.get(domain_path, "")
            explanation = self.domain_path_to_explanation.get(domain_path, "")

            # Add indentation based on level
            indent = "  " * level

            # For leaf domains, include the explanation if available
            if domain_path in self.leaf_domains and explanation:
                result.append(f"{indent}- {domain_name}: {description}")
                result.append(f"{indent}  Explanation: {explanation}")
            else:
                result.append(f"{indent}- {domain_name}: {description}")

            # Find and format children
            children = [path for path in self.domain_path_to_name.keys()
                       if path.startswith(domain_path + "/") and
                       path.count("/") == domain_path.count("/") + 1]

            for child in sorted(children):
                format_domain(child, level + 1)

        # Format the top domain and its hierarchy
        format_domain(top_domain)

        return "\n".join(result)

    def create_batches(self, repomap_structure: Dict[str, List[str]]) -> List[Dict[str, List[str]]]:
        """
        Create batches of files based solely on file count.

        Args:
            repomap_structure: Dictionary mapping file paths to lists of functions/classes

        Returns:
            List of batches, where each batch is a dictionary mapping file paths to functions/classes
        """
        batches = []
        current_batch = {}
        current_file_count = 0

        # Process each file
        for file_path, functions in repomap_structure.items():
            # Check if we've reached the maximum number of files per batch
            if current_file_count >= self.max_files_per_batch and current_batch:
                # Current batch is full, add it to batches and start a new one
                batches.append(current_batch)
                current_batch = {}
                current_file_count = 0

            # Add file to current batch
            current_batch[file_path] = functions
            current_file_count += 1

        # Add the last batch if it's not empty
        if current_batch:
            batches.append(current_batch)

        logger.info(f"Created {len(batches)} batches from {len(repomap_structure)} files (max {self.max_files_per_batch} files per batch)")
        return batches

    async def _process_with_worker_pool(self, tasks, num_workers):
        """
        Process tasks using a worker pool.

        Args:
            tasks: List of (index, task) tuples
            num_workers: Number of worker coroutines

        Returns:
            List of results in the same order as tasks
        """
        queue = asyncio.Queue()
        results = [None] * len(tasks)

        # Define the worker function
        async def worker(worker_id):
            while True:
                try:
                    # Get a task from the queue with timeout
                    try:
                        index, task = await asyncio.wait_for(queue.get(), timeout=1.0)
                    except asyncio.TimeoutError:
                        # Check if queue is empty and all tasks are done
                        if queue.empty() and all(r is not None for r in results):
                            break
                        continue

                    # Process the task
                    try:
                        logger.info(f"Worker {worker_id}: Processing batch {index+1}")
                        start_time = time.time()

                        # Execute the task with proper error handling
                        try:
                            result = await task
                            # Check if result is a coroutine and await it if needed
                            if asyncio.iscoroutine(result):
                                logger.info(f"Worker {worker_id}: Result is a coroutine, awaiting it")
                                result = await result
                        except Exception as e:
                            logger.error(f"Worker {worker_id}: Error in batch {index+1}: {str(e)}")
                            result = {}  # Empty result on failure

                        # Store the result
                        results[index] = result

                        elapsed = time.time() - start_time
                        logger.info(f"Worker {worker_id}: Completed batch {index+1} in {elapsed:.2f}s")

                    except Exception as e:
                        logger.error(f"Worker {worker_id}: Unexpected error: {str(e)}")
                        if index is not None:
                            results[index] = {}  # Empty result on failure

                    finally:
                        # Mark the task as done
                        queue.task_done()

                except Exception as e:
                    logger.error(f"Worker {worker_id}: Critical error in worker loop: {str(e)}")

        # Start workers
        workers = []
        for i in range(num_workers):
            worker_task = asyncio.create_task(worker(i+1))
            workers.append(worker_task)

        # Add tasks to queue
        for index, task in tasks:
            await queue.put((index, task))

        # Wait for all tasks to be processed
        try:
            # Wait for queue to be empty and all tasks to be processed
            await queue.join()

            # Additional check to ensure all results are filled
            if None in results:
                logger.warning("Some results are missing, waiting longer...")
                await asyncio.sleep(5.0)

        finally:
            # Cancel all workers
            for worker_task in workers:
                worker_task.cancel()

            # Wait for workers to be cancelled
            await asyncio.gather(*workers, return_exceptions=True)

        # Return non-None results (replace None with empty dict)
        return [r if r is not None else {} for r in results]

    async def _process_unclassified_files(
        self,
        unclassified_files: List[str],
        repomap_structure: Dict[str, List[str]],
        all_leaf_domains: List[str]
    ) -> Dict[str, List[str]]:
        """
        Process unclassified files in a second pass.

        Args:
            unclassified_files: List of file paths that were not classified in the first pass
            repomap_structure: Dictionary mapping file paths to lists of functions/classes
            all_leaf_domains: List of all leaf domain paths

        Returns:
            Dictionary mapping domain paths to lists of file paths
        """
        logger.info(f"Starting second pass to process {len(unclassified_files)} unclassified files")

        # Create a filtered repomap structure with only unclassified files
        filtered_repomap = {}
        for file_path in unclassified_files:
            if file_path in repomap_structure:
                filtered_repomap[file_path] = repomap_structure[file_path]
            else:
                logger.warning(f"File path {file_path} not found in repomap structure")
                filtered_repomap[file_path] = []  # Empty list for files without functions

        # Create batches based on file count
        batches = self.create_batches(filtered_repomap)
        logger.info(f"Created {len(batches)} batches for unclassified files")

        # Prepare tasks for processing
        tasks = []
        for i, batch in enumerate(batches):
            # Format all leaf domains for the prompt with hierarchical paths using / notation
            leaf_domain_list = []
            for path in all_leaf_domains:
                domain_name = self.domain_path_to_name.get(path, path)
                explanation = self.domain_path_to_explanation.get(path, "")
                if explanation:
                    leaf_domain_list.append(f"- {path} ({domain_name}): {explanation}")
                else:
                    leaf_domain_list.append(f"- {path} ({domain_name})")

            leaf_domain_str = "\n".join(leaf_domain_list)

            # Create task for this batch with a special flag indicating it's for unclassified files
            task = self._classify_batch_to_domains(
                batch_index=i+1,
                total_batches=len(batches),
                leaf_domains=all_leaf_domains,
                batch_structure=batch,
                leaf_domain_str=leaf_domain_str,
                is_second_pass=True  # Flag to indicate this is the second pass
            )
            tasks.append((i, task))

            logger.info(f"Created task for unclassified batch {i+1}/{len(batches)} with {len(batch)} files")

        # Process tasks with worker pool
        max_workers = 50  # Number of concurrent workers
        logger.info(f"Processing {len(tasks)} unclassified batch classification tasks with {max_workers} workers")

        results = await self._process_with_worker_pool(tasks, max_workers)

        # Merge all results
        second_pass_mappings = {}
        for domain_mappings in results:
            for domain_path, files in domain_mappings.items():
                if domain_path not in second_pass_mappings:
                    second_pass_mappings[domain_path] = []

                # Add all files from this domain mapping
                for file_path in files:
                    # Handle dictionary file paths
                    if isinstance(file_path, dict) and 'path' in file_path:
                        file_path = file_path.get('path')

                    # Skip non-string file paths
                    if not isinstance(file_path, str):
                        logger.error(f"Skipping non-string file path of type {type(file_path)}: {file_path}")
                        continue

                    if file_path not in second_pass_mappings[domain_path]:
                        second_pass_mappings[domain_path].append(file_path)

        # Log the number of files classified in the second pass
        total_classified = sum(len(files) for files in second_pass_mappings.values())
        logger.info(f"Second pass classified {total_classified} out of {len(unclassified_files)} unclassified files")

        # Log the number of files in each domain
        for domain_path, files in second_pass_mappings.items():
            logger.info(f"Second pass - Domain '{domain_path}': {len(files)} files")

        return second_pass_mappings

    async def map_files_to_domains(self) -> FileDomainMapperResult:
        """
        Map files to leaf domains using LLM with batch processing.

        Returns:
            FileDomainMapperResult containing the domain-to-file mappings
        """
        try:
            # Read domain data and extract hierarchy
            domain_data = self.read_domain_yaml()
            self.extract_domain_hierarchy(domain_data)

            # Parse the entire repository map structure
            repomap_structure = self.parse_repomap()

            # Get all leaf domains
            all_leaf_domains = self.leaf_domains
            logger.info(f"Found {len(all_leaf_domains)} leaf domains in the hierarchy")

            # Create batches based on file count
            batches = self.create_batches(repomap_structure)

            # Prepare tasks for processing
            tasks = []
            for i, batch in enumerate(batches):
                # Format all leaf domains for the prompt with hierarchical paths using / notation
                leaf_domain_list = []
                for path in all_leaf_domains:
                    domain_name = self.domain_path_to_name.get(path, path)
                    explanation = self.domain_path_to_explanation.get(path, "")
                    if explanation:
                        leaf_domain_list.append(f"- {path} ({domain_name}): {explanation}")
                    else:
                        leaf_domain_list.append(f"- {path} ({domain_name})")

                leaf_domain_str = "\n".join(leaf_domain_list)

                # Create task for this batch
                task = self._classify_batch_to_domains(
                    batch_index=i+1,
                    total_batches=len(batches),
                    leaf_domains=all_leaf_domains,
                    batch_structure=batch,
                    leaf_domain_str=leaf_domain_str
                )
                tasks.append((i, task))

                logger.info(f"Created task for batch {i+1}/{len(batches)} with {len(batch)} files")

            # Process tasks with worker pool
            max_workers = 50  # Number of concurrent workers
            logger.info(f"Processing {len(tasks)} batch classification tasks with {max_workers} workers")

            results = await self._process_with_worker_pool(tasks, max_workers)

            # Merge all results
            first_pass_mappings = {}
            # Keep track of which files have already been assigned to a domain
            assigned_files = set()
            # Keep track of which domain each file is assigned to (for logging)
            file_to_domain = {}
            # Count of files that would be assigned to multiple domains
            duplicate_assignments = 0
            # Keep track of all files from the repository map
            all_files = set()
            for batch in batches:
                # Ensure we only add string file paths to the set
                for file_path in batch.keys():
                    # Check if file_path is a dictionary (unhashable type)
                    if isinstance(file_path, dict):
                        logger.warning(f"Skipping dictionary file path in all_files: {file_path}")
                        # Try to extract the actual file path if possible
                        if 'path' in file_path:
                            actual_file_path = file_path.get('path')
                            logger.info(f"Extracted file path from dictionary: {actual_file_path}")
                            all_files.add(actual_file_path)
                    elif isinstance(file_path, str):
                        all_files.add(file_path)
                    else:
                        logger.warning(f"Skipping non-string file path of type {type(file_path)} in all_files")

            for domain_mappings in results:
                for domain_path, files in domain_mappings.items():
                    if domain_path not in first_pass_mappings:
                        first_pass_mappings[domain_path] = []

                    # Only add files that haven't been assigned to any domain yet
                    for file_path in files:
                        # Check if file_path is a dictionary (unhashable type)
                        if isinstance(file_path, dict):
                            logger.warning(f"Received a dictionary instead of a string for file_path: {file_path}")
                            # Try to extract the actual file path if possible
                            if 'path' in file_path:
                                actual_file_path = file_path.get('path')
                                logger.info(f"Extracted file path from dictionary: {actual_file_path}")
                                file_path = actual_file_path
                            else:
                                logger.error(f"Could not extract file path from dictionary: {file_path}")
                                continue  # Skip this file_path

                        # Skip if not a string (we need a hashable type for the set)
                        if not isinstance(file_path, str):
                            logger.error(f"Skipping non-string file path of type {type(file_path)}: {file_path}")
                            continue

                        if file_path not in assigned_files:
                            first_pass_mappings[domain_path].append(file_path)
                            assigned_files.add(file_path)
                            file_to_domain[file_path] = domain_path
                        else:
                            # Log when a file would be assigned to multiple domains
                            duplicate_assignments += 1
                            logger.debug(f"File '{file_path}' already assigned to domain '{file_to_domain.get(file_path)}', "
                                        f"skipping assignment to '{domain_path}'")

            # Check if there are unassigned files
            unassigned_files = all_files - assigned_files
            total_files = len(all_files)
            assigned_files_count = len(assigned_files)
            unassigned_files_count = len(unassigned_files)

            # Calculate first pass accuracy
            first_pass_accuracy = (assigned_files_count / total_files) * 100 if total_files > 0 else 0

            logger.info(f"First pass completed. {assigned_files_count} files assigned ({first_pass_accuracy:.2f}%), {unassigned_files_count} files unassigned.")

            # Save the first pass mappings to a temporary file
            first_pass_output_path = self.output_path.replace('.yaml', '_first_pass.yaml')
            with open(first_pass_output_path, 'w') as f:
                yaml.dump(first_pass_mappings, f, default_flow_style=False)
            logger.info(f"First pass file-to-domain mappings saved to: {first_pass_output_path}")

            # Process unassigned files in a second pass only if first pass accuracy is below 75%
            second_pass_mappings = {}
            if unassigned_files and first_pass_accuracy < 70:
                logger.info(f"First pass accuracy ({first_pass_accuracy:.2f}%) is below 75%, proceeding with second pass")
                # Convert set to list for processing
                unassigned_files_list = list(unassigned_files)

                # Process unclassified files in a second pass
                second_pass_mappings = await self._process_unclassified_files(
                    unassigned_files_list,
                    repomap_structure,
                    all_leaf_domains
                )

                # Save the second pass mappings to a temporary file
                second_pass_output_path = self.output_path.replace('.yaml', '_second_pass.yaml')
                with open(second_pass_output_path, 'w') as f:
                    yaml.dump(second_pass_mappings, f, default_flow_style=False)
                logger.info(f"Second pass file-to-domain mappings saved to: {second_pass_output_path}")
            elif unassigned_files:
                logger.info(f"First pass accuracy ({first_pass_accuracy:.2f}%) is at or above 75%, skipping second pass")
                logger.info(f"Treating first pass results as final (skipping {unassigned_files_count} unassigned files)")
            else:
                logger.info("All files were classified in the first pass, no need for a second pass")

            # Combine first and second pass mappings
            all_mappings = {}

            # First, add all mappings from the first pass
            for domain_path, files in first_pass_mappings.items():
                all_mappings[domain_path] = files.copy()

            # Then, add mappings from the second pass
            for domain_path, files in second_pass_mappings.items():
                if domain_path not in all_mappings:
                    all_mappings[domain_path] = []

                # Add files from second pass that aren't already in this domain
                for file_path in files:
                    if file_path not in all_mappings[domain_path]:
                        all_mappings[domain_path].append(file_path)

            # Check if there are still unassigned files after processing
            all_assigned_files = set()
            for files in all_mappings.values():
                all_assigned_files.update(files)

            still_unassigned = all_files - all_assigned_files
            if still_unassigned:
                # Determine if we skipped the second pass due to high first pass accuracy
                second_pass_skipped = bool(unassigned_files) and first_pass_accuracy >= 75

                if second_pass_skipped:
                    logger.info(f"Second pass was skipped due to high first pass accuracy. {len(still_unassigned)} files remain unassigned.")
                else:
                    logger.warning(f"After both passes, {len(still_unassigned)} files remain unassigned.")

                # Create a fallback domain for files that couldn't be assigned
                fallback_domain = "Infrastructure & Utilities/Experimental Features/Unclassified"
                if fallback_domain not in all_mappings:
                    all_mappings[fallback_domain] = []

                # Add all remaining unassigned files to the fallback domain
                for file_path in still_unassigned:
                    all_mappings[fallback_domain].append(file_path)

                logger.info(f"Added {len(still_unassigned)} unassigned files to fallback domain: {fallback_domain}")

            # Log the final number of files in each domain
            for domain_path, files in all_mappings.items():
                logger.info(f"Final - Domain '{domain_path}': {len(files)} files")

            # Save the combined mappings to YAML
            with open(self.output_path, 'w') as f:
                yaml.dump(all_mappings, f, default_flow_style=False)

            logger.info(f"Combined file-to-domain mappings saved to: {self.output_path}")

            # Calculate statistics
            total_files = len(all_files)
            first_pass_count = len(assigned_files)
            second_pass_count = len(all_assigned_files) - first_pass_count
            fallback_count = len(still_unassigned)

            # Determine if second pass was skipped
            second_pass_skipped = bool(unassigned_files) and first_pass_accuracy >= 75

            logger.info(f"Statistics: Total files: {total_files}")
            logger.info(f"Statistics: First pass: {first_pass_count} files ({first_pass_count/total_files*100:.2f}%)")

            if second_pass_skipped:
                logger.info(f"Statistics: Second pass: SKIPPED (first pass accuracy {first_pass_accuracy:.2f}% >= 75%)")
            else:
                logger.info(f"Statistics: Second pass: {second_pass_count} files ({second_pass_count/total_files*100:.2f}%)")

            logger.info(f"Statistics: Fallback assignment: {fallback_count} files ({fallback_count/total_files*100:.2f}%)")

            # Add overall accuracy statistics
            final_accuracy = ((total_files - fallback_count) / total_files * 100) if total_files > 0 else 0
            logger.info(f"Statistics: Overall classification accuracy: {final_accuracy:.2f}%")

            return FileDomainMapperResult(
                success=True,
                domain_file_mappings=all_mappings,
                output_path=self.output_path,
                unclassified_files=list(still_unassigned),
                first_pass_mappings=first_pass_mappings,
                second_pass_mappings=second_pass_mappings,
                first_pass_accuracy=first_pass_accuracy,
                second_pass_skipped=second_pass_skipped
            )

        except Exception as e:
            logger.error(f"Error in file-to-domain mapping: {e}")
            import traceback
            logger.error(traceback.format_exc())

            return FileDomainMapperResult(
                success=False,
                error_message=str(e)
            )

    async def _classify_batch_to_domains(
        self,
        batch_index: int,
        total_batches: int,
        leaf_domains: List[str],
        batch_structure: Dict[str, List[str]],
        leaf_domain_str: str,
        is_second_pass: bool = False
    ) -> Dict[str, List[str]]:
        """
        Classify a batch of files to leaf domains using LLM.

        Args:
            batch_index: Index of the current batch (for logging)
            total_batches: Total number of batches (for logging)
            leaf_domains: List of all leaf domain paths
            batch_structure: Dictionary mapping file paths to lists of functions/classes for this batch
            leaf_domain_str: Formatted string of leaf domains with explanations
            is_second_pass: Flag indicating if this is the second pass for unclassified files

        Returns:
            Dictionary mapping domain paths to lists of file paths
        """
        # Create a dynamic Pydantic model for structured outputs based on the leaf domains
        # This will ensure the model returns a valid JSON structure with the correct domain keys
        # Note: We're not using this directly anymore since we're using basic JSON mode
        # but keeping it for reference and potential future use
        _ = create_domain_file_mapping_schema(leaf_domains)
        # Prepare the prompt
        if is_second_pass:
            system_message = """
You are an expert code organization assistant. Your task is to analyze files and their functions/classes from a codebase and assign each file to the most appropriate leaf domain in a given hierarchy.
These files were difficult to classify in the first pass, so you need to be more flexible and creative in your approach.

Guidelines for Second Pass Classification:
1. Each file should be assigned to EXACTLY ONE leaf domain that best represents its primary purpose
2. Use both the file path AND the functions/classes within the file to determine the most appropriate domain
3. Pay special attention to the explanations provided for leaf domains - these give important context about what each domain represents
4. Return your analysis as a valid JSON object where keys are domain paths and values are lists of file paths
5. Try to assign EVERY file to the most appropriate domain in the hierarchy - all files MUST be categorized
6. Only assign files to leaf domains (domains with no subdomains)
7. Ensure that each file appears in exactly one domain's list - files should not be duplicated across multiple domains
8. It's okay for some leaf domains to have no files assigned to them
9. IMPORTANT: When multiple files belong to the same domain, group them together under a SINGLE domain key in your JSON response
10. DO NOT create duplicate domain keys in your JSON - this will make the JSON invalid and unparsable
11. For each domain, collect ALL files that belong to it and list them in a single array
12. For difficult-to-classify files, consider:
   - Looking for broader patterns in the file structure and naming
   - Examining utility functions that might indicate the file's purpose
   - Considering the file's relationship to other files in the codebase
   - Being more flexible with domain boundaries - choose the "best fit" even if it's not perfect
   - Using file naming conventions and directory structure as stronger signals
"""
        else:
            system_message = """
You are an expert code organization assistant. Your task is to analyze files and their functions/classes from a codebase and assign each file to the most appropriate leaf domain in a given hierarchy.

Guidelines:
1. Each file should be assigned to EXACTLY ONE leaf domain that best represents its primary purpose
2. Use both the file path AND the functions/classes within the file to determine the most appropriate domain
3. Pay special attention to the explanations provided for leaf domains - these give important context about what each domain represents
4. Return your analysis as a valid JSON object where keys are domain paths and values are lists of file paths
5. Try to assign EVERY file to the most appropriate domain in the hierarchy - all files should be categorized
6. Only assign files to leaf domains (domains with no subdomains)
7. Ensure that each file appears in exactly one domain's list - files should not be duplicated across multiple domains
8. It's okay for some leaf domains to have no files assigned to them
9. IMPORTANT: When multiple files belong to the same domain, group them together under a SINGLE domain key in your JSON response
10. DO NOT create duplicate domain keys in your JSON - this will make the JSON invalid and unparsable
11. For each domain, collect ALL files that belong to it and list them in a single array
"""

        # Format the batch structure for the prompt
        batch_formatted = []
        for file_path, functions in batch_structure.items():
            if functions:
                batch_formatted.append(f"File: {file_path}")
                for func in functions:
                    batch_formatted.append(f"  - {func}")
            else:
                batch_formatted.append(f"File: {file_path} (no functions/classes)")

        batch_str = "\n".join(batch_formatted)

        # Customize the message based on whether this is the first or second pass
        if is_second_pass:
            user_message = f"""
Here are the leaf domains where files can be assigned (showing the full hierarchy with / separators):
{leaf_domain_str}

I have a batch of files (batch {batch_index} of {total_batches}) from a codebase showing files and their functions/classes.
These files were NOT successfully classified in the first pass and need special attention to assign them to the most appropriate leaf domain.

Here are the unclassified files in this batch:
{batch_str}

IMPORTANT: These files were difficult to classify in the first pass, so please analyze them more carefully.
Assign EVERY file in this batch to a leaf domain. Each file should be assigned to EXACTLY ONE leaf domain that best represents its primary purpose. Make sure no files are left uncategorized.
Pay special attention to the explanations provided for leaf domains - these give important context about what each domain represents.
Use both the file path AND the functions/classes within the file to determine the most appropriate domain.
Be more flexible in your classification approach for these challenging files - consider broader patterns and similarities to known domain characteristics.

Format your response as JSON with this structure:
{{
  "domain_path": ["file1.py", "file2.py", ...]
}}

Where "domain_path" is the full path to the leaf domain (e.g., "CodeAnalysis/CallGraphGeneration/LanguageSpecificAnalysis").
Only include domains that have at least one file assigned to them.
Ensure that each file appears in exactly one domain's list - files should not be duplicated across multiple domains.

IMPORTANT FORMATTING REQUIREMENTS:
1. When multiple files belong to the same domain, group them together under a SINGLE domain key in your JSON response
2. DO NOT create duplicate domain keys in your JSON - this will make the JSON invalid and unparsable
3. For each domain, collect ALL files that belong to it and list them in a single array
4. Your response MUST be valid JSON that can be parsed with json.loads()
"""
        else:
            user_message = f"""
Here are the leaf domains where files can be assigned (showing the full hierarchy with / separators):
{leaf_domain_str}

I have a batch of files (batch {batch_index} of {total_batches}) from a codebase showing files and their functions/classes.
I need to assign each file to the appropriate leaf domain.

Here are the files in this batch:
{batch_str}

Assign EVERY file in this batch to a leaf domain. Each file should be assigned to EXACTLY ONE leaf domain that best represents its primary purpose. Make sure no files are left uncategorized.
Pay special attention to the explanations provided for leaf domains - these give important context about what each domain represents.
Use both the file path AND the functions/classes within the file to determine the most appropriate domain.

Format your response as JSON with this structure:
{{
  "domain_path": ["file1.py", "file2.py", ...]
}}

Where "domain_path" is the full path to the leaf domain (e.g., "CodeAnalysis/CallGraphGeneration/LanguageSpecificAnalysis").
Only include domains that have at least one file assigned to them.
Ensure that each file appears in exactly one domain's list - files should not be duplicated across multiple domains.

IMPORTANT FORMATTING REQUIREMENTS:
1. When multiple files belong to the same domain, group them together under a SINGLE domain key in your JSON response
2. DO NOT create duplicate domain keys in your JSON - this will make the JSON invalid and unparsable
3. For each domain, collect ALL files that belong to it and list them in a single array
4. Your response MUST be valid JSON that can be parsed with json.loads()
"""

        messages = [
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_message}
        ]

        # Call the API with rate limiting
        # Add a small delay between API calls to avoid overwhelming the server
        await asyncio.sleep(0.1 * (batch_index % 10))  # Stagger requests

        if self.use_openrouter and self.openrouter_client:
            try:
                # Extract system and user prompts from messages
                system_prompt = None
                user_prompt = None

                for message in messages:
                    if message["role"] == "system":
                        system_prompt = message["content"]
                    elif message["role"] == "user":
                        user_prompt = message["content"]

                if not user_prompt:
                    logger.error("No user prompt found in request")
                    return {}

                # Call the OpenRouter client
                try:
                    response = await self.openrouter_client.generate(
                        prompt=user_prompt,
                        system_prompt=system_prompt,
                        temperature=self.temperature
                    )

                    # Check if response is a coroutine and await it if needed
                    if asyncio.iscoroutine(response):
                        logger.info(f"Batch {batch_index}: OpenRouter response is a coroutine, awaiting it")
                        response = await response

                    # Check if the response is an error message from OpenRouter
                    if isinstance(response, str) and response.startswith("Error:"):
                        logger.error(f"OpenRouter API returned error: {response}")
                        logger.warning("Falling back to regular API request")
                        response = await self.api_request.call_api(messages, temperature=self.temperature)

                except Exception as e:
                    logger.error(f"OpenRouter generate method error: {e}")
                    logger.warning("Falling back to regular API request")
                    response = await self.api_request.call_api(messages, temperature=self.temperature)
            except Exception as e:
                logger.error(f"OpenRouter API error: {e}")
                logger.warning("Falling back to regular API request")
                response = await self.api_request.call_api(messages, temperature=self.temperature)
        else:
            # Add rate limiting for OpenAI API calls
            # Use a semaphore to limit concurrent API calls
            # This helps prevent 502 errors from overwhelming the server
            max_concurrent_calls = 100  # Limit concurrent API calls

            # Create a semaphore if it doesn't exist
            if not hasattr(self, '_api_semaphore'):
                self._api_semaphore = asyncio.Semaphore(max_concurrent_calls)

            # Acquire the semaphore before making the API call
            async with self._api_semaphore:
                # Just use basic JSON mode for all models
                logger.info(f"Using basic JSON mode for batch {batch_index}/{total_batches}")
                response = await self.api_request.call_api(
                    messages,
                    temperature=self.temperature,
                    json_mode=True
                )

        if not response:
            logger.error(f"Failed to get response for batch {batch_index}/{total_batches}")
            return {}

        # Parse the response
        try:
            # Check if the response is a string that might be JSON
            if isinstance(response, str):
                try:
                    mappings = json.loads(response)

                    # Validate the mappings
                    valid_mappings = {}

                    # Handle both cases: when using structured outputs with clean domain names
                    # and when using basic JSON mode with original domain paths
                    for key, files in mappings.items():
                        # Check if this is a clean domain name (with _ instead of /)
                        original_domain = key.replace('_', '/')

                        # Check if either the key or the reconstructed original domain is valid
                        if key in leaf_domains and isinstance(files, list):
                            valid_mappings[key] = files
                        elif original_domain in leaf_domains and isinstance(files, list):
                            valid_mappings[original_domain] = files

                    logger.info(f"Classified files into {len(valid_mappings)} leaf domains for batch {batch_index}/{total_batches}")
                    return valid_mappings

                except json.JSONDecodeError:
                    # If JSON parsing fails, try to extract JSON from the response
                    # Sometimes the model returns extra text before or after the JSON
                    json_match = re.search(r'({[\s\S]*})', response)
                    if json_match:
                        try:
                            potential_json = json_match.group(1)
                            mappings = json.loads(potential_json)

                            # Validate the mappings
                            valid_mappings = {}

                            # Handle both cases: when using structured outputs with clean domain names
                            # and when using basic JSON mode with original domain paths
                            for key, files in mappings.items():
                                # Check if this is a clean domain name (with _ instead of /)
                                original_domain = key.replace('_', '/')

                                # Check if either the key or the reconstructed original domain is valid
                                if key in leaf_domains and isinstance(files, list):
                                    valid_mappings[key] = files
                                elif original_domain in leaf_domains and isinstance(files, list):
                                    valid_mappings[original_domain] = files

                            logger.info(f"Successfully extracted JSON from response for batch {batch_index}/{total_batches}")
                            logger.info(f"Classified files into {len(valid_mappings)} leaf domains")
                            return valid_mappings

                        except json.JSONDecodeError:
                            logger.error(f"Failed to extract valid JSON from response for batch {batch_index}/{total_batches}")

                    # If we still can't parse JSON, try to manually extract domain-file mappings
                    # This is a last resort fallback
                    logger.warning(f"Attempting manual extraction of domain-file mappings from text for batch {batch_index}/{total_batches}")

                    # Store the unparsable output as text in a file
                    output_dir = os.path.dirname(self.output_path)
                    fallback_file = os.path.join(output_dir, f"unparsable_batch_{batch_index}_output.txt")

                    # Write to the file
                    with open(fallback_file, 'w') as f:
                        f.write(f"\n\n--- UNPARSABLE OUTPUT FOR BATCH {batch_index}/{total_batches} ---\n")
                        f.write(response)
                        f.write("\n--- END OF OUTPUT ---\n")

                    logger.info(f"Stored unparsable output in {fallback_file}")

                    # Try to extract domain-file mappings using regex patterns
                    # This is a best-effort approach for text that looks like JSON but isn't valid
                    manual_mappings = {}

                    # Look for patterns like "domain_path": ["file1.py", "file2.py"]
                    domain_matches = re.finditer(r'"([^"]+)"\s*:\s*\[(.*?)\]', response, re.DOTALL)
                    for match in domain_matches:
                        domain_path = match.group(1)
                        files_text = match.group(2)

                        # Extract file paths from the files text
                        file_matches = re.finditer(r'"([^"]+)"', files_text)
                        files = [m.group(1) for m in file_matches]

                        # Check if either the domain_path or a reconstructed original domain is valid
                        original_domain = domain_path.replace('_', '/')

                        if domain_path in leaf_domains and files:
                            valid_domain = domain_path
                        elif original_domain in leaf_domains and files:
                            valid_domain = original_domain
                        else:
                            continue  # Skip invalid domains

                        # If this domain already exists in our mappings, append the files
                        # rather than overwriting the existing list
                        if valid_domain in manual_mappings:
                            # Add files not already in this domain
                            for file_path in files:
                                if file_path not in manual_mappings[valid_domain]:
                                    manual_mappings[valid_domain].append(file_path)
                        else:
                            manual_mappings[valid_domain] = files

                    if manual_mappings:
                        logger.info(f"Manually extracted {len(manual_mappings)} domain mappings from text")
                        return manual_mappings

                    # If all else fails, return empty mapping to allow the process to continue
                    logger.warning("Could not extract any domain mappings. Returning empty mapping.")
                    return {}
            else:
                logger.error(f"Response is not a string: {type(response)}")
                return {}

        except Exception as e:
            logger.error(f"Error processing response for batch {batch_index}/{total_batches}: {str(e)}")
            logger.error(f"Response: {response[:500]}...")  # Log first 500 chars

            # Store the problematic output
            output_dir = os.path.dirname(self.output_path)
            fallback_file = os.path.join(output_dir, f"error_batch_{batch_index}_output.txt")

            # Write to the file
            with open(fallback_file, 'w') as f:
                f.write(f"\n\n--- ERROR PROCESSING OUTPUT FOR BATCH {batch_index}/{total_batches} ---\n")
                f.write(f"Error: {str(e)}\n\n")
                if isinstance(response, str):
                    f.write(response)
                else:
                    f.write(f"Response type: {type(response)}")
                f.write("\n--- END OF OUTPUT ---\n")

            logger.info(f"Stored problematic output in {fallback_file}")

            # Return empty mapping to allow the process to continue
            return {}

    async def _classify_files_to_domains(
        self,
        top_domain: str,
        leaf_domains: List[str],
        repomap_structure: Dict[str, List[str]],
        domain_hierarchy: str
    ) -> Dict[str, List[str]]:
        """
        Classify files to leaf domains using LLM.

        Args:
            top_domain: Top-level domain path
            leaf_domains: List of leaf domain paths
            repomap_structure: Dictionary mapping file paths to lists of functions/classes
            domain_hierarchy: Formatted domain hierarchy

        Returns:
            Dictionary mapping domain paths to lists of file paths
        """
        # Create a dynamic Pydantic model for structured outputs based on the leaf domains
        # This will ensure the model returns a valid JSON structure with the correct domain keys
        # Note: We're not using this directly anymore since we're using basic JSON mode
        # but keeping it for reference and potential future use
        _ = create_domain_file_mapping_schema(leaf_domains)
        # Prepare the prompt
        system_message = """
You are an expert code organization assistant. Your task is to analyze files and their functions/classes from a codebase and assign each file to the most appropriate leaf domain in a given hierarchy.

Guidelines:
1. Each file should be assigned to EXACTLY ONE leaf domain that best represents its primary purpose
2. Use both the file path AND the functions/classes within the file to determine the domain
3. Pay special attention to the explanations provided for leaf domains - these give important context about what each domain represents
4. Return your analysis as a valid JSON object where keys are domain paths and values are lists of file paths
5. Try to assign EVERY file to the most appropriate domain in the hierarchy - all files should be categorized
6. Only assign files to leaf domains (domains with no subdomains)
7. Ensure that each file appears in exactly one domain's list - files should not be duplicated across multiple domains
8. It's okay for some leaf domains to have no files assigned to them
9. IMPORTANT: When multiple files belong to the same domain, group them together under a SINGLE domain key in your JSON response
10. DO NOT create duplicate domain keys in your JSON - this will make the JSON invalid and unparsable
11. For each domain, collect ALL files that belong to it and list them in a single array
"""

        # Format leaf domains for the prompt with explanations
        leaf_domain_list = []
        for path in leaf_domains:
            domain_name = self.domain_path_to_name.get(path, path)
            explanation = self.domain_path_to_explanation.get(path, "")
            if explanation:
                leaf_domain_list.append(f"- {path} ({domain_name}): {explanation}")
            else:
                leaf_domain_list.append(f"- {path} ({domain_name})")

        leaf_domain_list = "\n".join(leaf_domain_list)

        # Format the repomap structure for the prompt
        repomap_formatted = []
        for file_path, functions in repomap_structure.items():
            if functions:
                repomap_formatted.append(f"File: {file_path}")
                for func in functions:
                    repomap_formatted.append(f"  - {func}")
            else:
                repomap_formatted.append(f"File: {file_path} (no functions/classes)")

        repomap_str = "\n".join(repomap_formatted)
        user_message = f"""
Here are the leaf domains where files can be assigned:
{leaf_domain_list}

I have a repository map from a codebase showing files and their functions/classes. I need to assign each relevant file to the appropriate leaf domains in this hierarchy:

{domain_hierarchy}

Here's the repository map structure:
{repomap_str}

Assign EVERY file in the repository map to a leaf domain. Each file should be assigned to EXACTLY ONE leaf domain that best represents its primary purpose. Make sure no files are left uncategorized.
Pay special attention to the explanations provided for leaf domains - these give important context about what each domain represents.
Use both the file path AND the functions/classes within the file to determine the most appropriate domain.

Format your response as JSON with this structure:
{{
  "domain_path": ["file1.py", "file2.py", ...]
}}

Where "domain_path" is the full path to the leaf domain (e.g., "CodeAnalysis/CallGraphGeneration/LanguageSpecificAnalysis").
Only include domains that have at least one file assigned to them.
Ensure that each file appears in exactly one domain's list - files should not be duplicated across multiple domains.

IMPORTANT FORMATTING REQUIREMENTS:
1. When multiple files belong to the same domain, group them together under a SINGLE domain key in your JSON response
2. DO NOT create duplicate domain keys in your JSON - this will make the JSON invalid and unparsable
3. For each domain, collect ALL files that belong to it and list them in a single array
4. Your response MUST be valid JSON that can be parsed with json.loads()
"""

        messages = [
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_message}
        ]

        # Call the API with rate limiting
        # Add a small delay to avoid overwhelming the server
        await asyncio.sleep(0.1)  # Small delay between requests

        if self.use_openrouter and self.openrouter_client:
            try:
                # Extract system and user prompts from messages
                system_prompt = None
                user_prompt = None

                for message in messages:
                    if message["role"] == "system":
                        system_prompt = message["content"]
                    elif message["role"] == "user":
                        user_prompt = message["content"]

                if not user_prompt:
                    logger.error("No user prompt found in request")
                    return {}

                # Call the OpenRouter client
                try:
                    response = await self.openrouter_client.generate(
                        prompt=user_prompt,
                        system_prompt=system_prompt,
                        temperature=self.temperature
                    )

                    # Check if response is a coroutine and await it if needed
                    if asyncio.iscoroutine(response):
                        logger.info(f"Domain {top_domain}: OpenRouter response is a coroutine, awaiting it")
                        response = await response

                    # Check if the response is an error message from OpenRouter
                    if isinstance(response, str) and response.startswith("Error:"):
                        logger.error(f"OpenRouter API returned error: {response}")
                        logger.warning("Falling back to regular API request")
                        response = await self.api_request.call_api(messages, temperature=self.temperature)

                except Exception as e:
                    logger.error(f"OpenRouter generate method error: {e}")
                    logger.warning("Falling back to regular API request")
                    response = await self.api_request.call_api(messages, temperature=self.temperature)
            except Exception as e:
                logger.error(f"OpenRouter API error: {e}")
                logger.warning("Falling back to regular API request")
                response = await self.api_request.call_api(messages, temperature=self.temperature)
        else:
            # Add rate limiting for OpenAI API calls
            # Use a semaphore to limit concurrent API calls
            # This helps prevent 502 errors from overwhelming the server
            max_concurrent_calls = 100  # Limit concurrent API calls

            # Create a semaphore if it doesn't exist
            if not hasattr(self, '_api_semaphore'):
                self._api_semaphore = asyncio.Semaphore(max_concurrent_calls)

            # Acquire the semaphore before making the API call
            async with self._api_semaphore:
                # Just use basic JSON mode for all models
                logger.info(f"Using basic JSON mode for domain {top_domain}")
                response = await self.api_request.call_api(
                    messages,
                    temperature=self.temperature,
                    json_mode=True
                )

        if not response:
            logger.error(f"Failed to get response for top-level domain: {top_domain}")
            return {}

        # Parse the response
        try:
            # Check if the response is a string that might be JSON
            if isinstance(response, str):
                try:
                    mappings = json.loads(response)

                    # Validate the mappings
                    valid_mappings = {}

                    # Handle both cases: when using structured outputs with clean domain names
                    # and when using basic JSON mode with original domain paths
                    for key, files in mappings.items():
                        # Check if this is a clean domain name (with _ instead of /)
                        original_domain = key.replace('_', '/')

                        # Check if either the key or the reconstructed original domain is valid
                        if key in leaf_domains and isinstance(files, list):
                            valid_mappings[key] = files
                        elif original_domain in leaf_domains and isinstance(files, list):
                            valid_mappings[original_domain] = files

                    logger.info(f"Classified files into {len(valid_mappings)} leaf domains for top-level domain: {top_domain}")
                    return valid_mappings

                except json.JSONDecodeError:
                    # If JSON parsing fails, try to extract JSON from the response
                    # Sometimes the model returns extra text before or after the JSON
                    json_match = re.search(r'({[\s\S]*})', response)
                    if json_match:
                        try:
                            potential_json = json_match.group(1)
                            mappings = json.loads(potential_json)

                            # Validate the mappings
                            valid_mappings = {}

                            # Handle both cases: when using structured outputs with clean domain names
                            # and when using basic JSON mode with original domain paths
                            for key, files in mappings.items():
                                # Check if this is a clean domain name (with _ instead of /)
                                original_domain = key.replace('_', '/')

                                # Check if either the key or the reconstructed original domain is valid
                                if key in leaf_domains and isinstance(files, list):
                                    valid_mappings[key] = files
                                elif original_domain in leaf_domains and isinstance(files, list):
                                    valid_mappings[original_domain] = files

                            logger.info(f"Successfully extracted JSON from response for top-level domain: {top_domain}")
                            logger.info(f"Classified files into {len(valid_mappings)} leaf domains")
                            return valid_mappings

                        except json.JSONDecodeError:
                            logger.error(f"Failed to extract valid JSON from response for top-level domain: {top_domain}")

                    # If we still can't parse JSON, try to manually extract domain-file mappings
                    # This is a last resort fallback
                    logger.warning(f"Attempting manual extraction of domain-file mappings from text for top-level domain: {top_domain}")

                    # Store the unparsable output as text in a file
                    output_dir = os.path.dirname(self.output_path)
                    fallback_file = os.path.join(output_dir, f"unparsable_domain_{top_domain.replace('/', '_')}_output.txt")

                    # Write to the file
                    with open(fallback_file, 'w') as f:
                        f.write(f"\n\n--- UNPARSABLE OUTPUT FOR {top_domain} ---\n")
                        f.write(response)
                        f.write("\n--- END OF OUTPUT ---\n")

                    logger.info(f"Stored unparsable output in {fallback_file}")

                    # Try to extract domain-file mappings using regex patterns
                    # This is a best-effort approach for text that looks like JSON but isn't valid
                    manual_mappings = {}

                    # Look for patterns like "domain_path": ["file1.py", "file2.py"]
                    domain_matches = re.finditer(r'"([^"]+)"\s*:\s*\[(.*?)\]', response, re.DOTALL)
                    for match in domain_matches:
                        domain_path = match.group(1)
                        files_text = match.group(2)

                        # Extract file paths from the files text
                        file_matches = re.finditer(r'"([^"]+)"', files_text)
                        files = [m.group(1) for m in file_matches]

                        # Check if either the domain_path or a reconstructed original domain is valid
                        original_domain = domain_path.replace('_', '/')

                        if domain_path in leaf_domains and files:
                            valid_domain = domain_path
                        elif original_domain in leaf_domains and files:
                            valid_domain = original_domain
                        else:
                            continue  # Skip invalid domains

                        # If this domain already exists in our mappings, append the files
                        # rather than overwriting the existing list
                        if valid_domain in manual_mappings:
                            # Add files not already in this domain
                            for file_path in files:
                                if file_path not in manual_mappings[valid_domain]:
                                    manual_mappings[valid_domain].append(file_path)
                        else:
                            manual_mappings[valid_domain] = files

                    if manual_mappings:
                        logger.info(f"Manually extracted {len(manual_mappings)} domain mappings from text")
                        return manual_mappings

                    # If all else fails, return a simple mapping with the top domain as key and an empty list
                    # This allows the process to continue without failing
                    logger.warning(f"Could not extract any domain mappings. Returning empty mapping for {top_domain}.")
                    return {top_domain: []}
            else:
                logger.error(f"Response is not a string: {type(response)}")
                return {top_domain: []}

        except Exception as e:
            logger.error(f"Error processing response for top-level domain {top_domain}: {str(e)}")
            logger.error(f"Response: {response[:500]}...")  # Log first 500 chars

            # Store the problematic output
            output_dir = os.path.dirname(self.output_path)
            fallback_file = os.path.join(output_dir, f"error_domain_{top_domain.replace('/', '_')}_output.txt")

            # Write to the file
            with open(fallback_file, 'w') as f:
                f.write(f"\n\n--- ERROR PROCESSING OUTPUT FOR {top_domain} ---\n")
                f.write(f"Error: {str(e)}\n\n")
                if isinstance(response, str):
                    f.write(response)
                else:
                    f.write(f"Response type: {type(response)}")
                f.write("\n--- END OF OUTPUT ---\n")

            logger.info(f"Stored problematic output in {fallback_file}")

            # Return a simple mapping to allow the process to continue
            return {top_domain: []}

    @staticmethod
    async def map_files_to_leaf_domains(
        repomap_path: str,
        domain_analysis_yaml_path: str,
        output_path: str,
        api_key: Optional[str] = None,
        model: str = "gpt-4o-mini",
        max_requests_per_minute: float = 50,
        max_tokens_per_minute: float = 100000,
        use_openrouter: bool = False,
        openrouter_base_url: str = "https://openrouter.ai/api/v1",
        max_files_per_batch: int = 50,
    ) -> bool:
        """
        Map files to leaf domains from repository map and domain analysis YAML.

        This method uses the entire repository map structure including files and their functions/classes
        to provide more context for domain mapping. It processes files in batches based on file count.

        Args:
            repomap_path: Path to the repository map file (text format or JSON)
                          For JSON format, keys should be file paths and values should be lists of strings (code lines)
            domain_analysis_yaml_path: Path to the domain analysis YAML file
            output_path: Path to save the file-to-domain mappings YAML
            api_key: API key (OpenAI or OpenRouter depending on use_openrouter)
            model: Model to use (OpenAI or OpenRouter model ID)
            max_requests_per_minute: Rate limit for API requests
            max_tokens_per_minute: Token rate limit for API
            use_openrouter: Whether to use OpenRouter instead of OpenAI
            openrouter_base_url: Base URL for OpenRouter API
            max_files_per_batch: Maximum number of files to include in a batch (default: 50)

        Returns:
            True if mapping was successful, False otherwise
        """
        try:
            mapper = FileDomainMapper(
                repomap_path=repomap_path,
                domain_analysis_yaml_path=domain_analysis_yaml_path,
                output_path=output_path,
                api_key=api_key,
                model=model,
                max_requests_per_minute=max_requests_per_minute,
                max_tokens_per_minute=max_tokens_per_minute,
                use_openrouter=use_openrouter,
                openrouter_base_url=openrouter_base_url,
                max_files_per_batch=max_files_per_batch,
            )

            result = await mapper.map_files_to_domains()
            return result.success

        except Exception as e:
            logger.error(f"Error in file-to-domain mapping: {e}")
            return False

async def main():
    """Main entry point for the file domain mapper."""
    parser = argparse.ArgumentParser(description="Map files to leaf domains")
    # parser.add_argument("--repomap", required=True, help="Path to the repository map file (text format or JSON)")
    # parser.add_argument("--domain-yaml", required=True, help="Path to the domain analysis YAML file")
    # parser.add_argument("--output", required=True, help="Path to save the file-to-domain mappings YAML")
    parser.add_argument("--api-key", help="OpenAI API key (if not provided, will try to get from environment)")
    parser.add_argument("--model", default="gpt-4.1-2025-04-14", help="Model to use (OpenAI or OpenRouter model ID)") #o4-mini
    parser.add_argument("--requests-per-minute", type=float, default=5000, help="Rate limit for API requests")
    parser.add_argument("--tokens-per-minute", type=float, default=1000000, help="Token rate limit for API")
    parser.add_argument("--temperature", type=float, default=0.0, help="Temperature for the model")
    parser.add_argument("--use-openrouter", default=False, action="store_true", help="Use OpenRouter instead of OpenAI")
    parser.add_argument("--openrouter-base-url", default="https://openrouter.ai/api/v1", help="Base URL for OpenRouter API")
    parser.add_argument("--max-files-per-batch", type=int, default=50, help="Maximum number of files per batch")

    args = parser.parse_args()
    # args.repomap = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/complete_repomap_bracket.txt"
    # args.domain_yaml = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/output/domain_analysis_bracket_expln.yaml"
    # args.output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/output/file_domain_maps/bracket5.yaml"

    # args.repomap = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/complete_repomap_django.txt"
    # args.domain_yaml = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/output/domain_analysis_django_expl.yaml"
    # args.output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/output/file_domain_maps/django_explained.yaml"

    # args.repomap = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/repomap_django_top51_filtered.txt"
    # args.domain_yaml = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/output/domain_analysis_django_expl.yaml"
    # args.output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/output/file_domain_maps/file_domain_full_repomap_django_gemini.yaml"

    # args.repomap = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/repomap_gitlab_filtered_filtered.txt"
    # args.domain_yaml = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/output/domain_anal_fltrd_gitlab_expln.yaml"
    # args.output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/output/file_domain_maps/file_domain_filtered_gitlab_batched.yaml"

    # args.repomap = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/complete_repomap_bracket.txt"
    # # args.domain_yaml = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/output/domain_analysis_bracket_expln.yaml"
    # args.domain_yaml = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/output/domain_analysis_bracket.yaml"
    # args.output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/output/file_domain_maps/bracket_mapping_so4.yaml"

    # args.repomap = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/repomap_gitlab2_full_filtered.txt"
    # args.domain_yaml = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/output/domain_analysis_gitlab_full_no_expln.yaml"
    # args.output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/output/_gitlab1_output_o4_mini/gitlab1_full_batched_mapping_o4_mini_200batch.yaml"

    # args.repomap = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/repomap/gitlab_full_repomap_filtered.txt"
    # args.domain_yaml = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/domain_analysis/gitlab_full_domain_analysis_oai_4_1.yaml"
    # args.output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/domain_mapping/gitlab_oai_4_1.yaml"

    # args.repomap = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/repomap/bracket/bracket_repomap_filtered.json"
    # args.domain_yaml = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/domain_analysis/bracket/bracket_4_1.yaml"
    # args.output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/domain_mapping/bracket/bracket.yaml"

    # args.repomap = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/repomap/django/django_repomap_filtered.json"
    # args.domain_yaml = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/domain_analysis/django/django_4_1.yaml"
    # args.output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/domain_mapping/django/django_domain_file_mapper.yaml"

    # args.repomap = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/repomap/pytorch/pytorch_repomap_filtered.json"
    # args.domain_yaml = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/domain_analysis/pytorch/pytorch_4_1.yaml"
    # args.output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/domain_mapping/pytorch/pytorch_domain_file_mapper.yaml"

    args.repomap = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/repomap/gitlab/gitlab_filtered.json"
    args.domain_yaml = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/domain_analysis/gitlab/gitlab_domain_analysis.yaml"
    args.output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/domain_mapping/gitlab/gitlab_domain_file_mapper.yaml"


    start_time = time.time()
    try:
        mapper = FileDomainMapper(
            repomap_path=args.repomap,
            domain_analysis_yaml_path=args.domain_yaml,
            output_path=args.output,
            api_key=args.api_key,
            model=args.model,
            max_requests_per_minute=args.requests_per_minute,
            max_tokens_per_minute=args.tokens_per_minute,
            temperature=args.temperature,
            use_openrouter=args.use_openrouter,
            openrouter_base_url=args.openrouter_base_url,
            max_files_per_batch=args.max_files_per_batch,
        )

        result = await mapper.map_files_to_domains()

        elapsed_time = time.time() - start_time
        print(f"total elapsed_time is : {elapsed_time:.2f}")
        if result.success:
            end_time = time.time()
            print(f"total time taken: {end_time-start_time}")
            logger.info(f"total time taken: {end_time-start_time}")
            logger.info("File-to-domain mapping completed successfully")
            return 0
        else:
            end_time = time.time()
            print(f"total time taken: {end_time-start_time}")
            logger.info(f"total time taken: {end_time-start_time}")
            logger.error(f"File-to-domain mapping failed: {result.error_message}")
            return 1

    except Exception as e:
        logger.error(f"Error in file-to-domain mapping: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
