#!/usr/bin/env python3
"""
Hierarchical Domain Taxonomy Mapper Main Script

This script is the main entry point for the hierarchical domain taxonomy mapper.
It reads domain analysis YAML, domain file repomap JSON, and mermaid diagrams
from both leaf-level and hierarchical directories, and combines them into a hierarchical JSON structure.

Usage:
    python hierarchical_domain_taxonomy_mapper_main.py --domain-analysis <path_to_domain_analysis_yaml>
                                                      --domain-file-repomap <path_to_domain_file_repomap_json>
                                                      --domain-diagrams <path_to_leaf_diagrams_dir>
                                                      --hierarchical-diagrams <path_to_hierarchical_diagrams_dir>
                                                      --output <path_to_output_json>
                                                      [--diagram-name-mapping <path_to_diagram_name_mapping_json>]
"""

import os
import sys
import logging
import argparse
from bracket_core.bracket_irl.hierarchical_domain_taxonomy_mapper import HierarchicalDomainTaxonomyMapper

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Main entry point for the hierarchical domain taxonomy mapper."""
    parser = argparse.ArgumentParser(description="Map domain taxonomy from analysis, file mappings, and diagrams (including hierarchical diagrams)")
    parser.add_argument("--domain-analysis", required=True, help="Path to the domain analysis YAML file")
    parser.add_argument("--domain-file-repomap", required=True, help="Path to the domain file repomap JSON file")
    parser.add_argument("--domain-diagrams", required=True, help="Directory containing the leaf-level mermaid diagrams")
    parser.add_argument("--hierarchical-diagrams", required=True, help="Directory containing the hierarchical mermaid diagrams")
    parser.add_argument("--output", required=True, help="Path to save the output JSON file")
    parser.add_argument("--diagram-name-mapping", help="Path to the diagram name mapping JSON file (optional)")

    args = parser.parse_args()

    try:
        # Create output directory if it doesn't exist
        os.makedirs(os.path.dirname(args.output), exist_ok=True)

        # Create the hierarchical domain taxonomy mapper
        mapper = HierarchicalDomainTaxonomyMapper(
            domain_analysis_yaml_path=args.domain_analysis,
            domain_file_repomap_path=args.domain_file_repomap,
            domain_diagrams_dir=args.domain_diagrams,
            hierarchical_diagrams_dir=args.hierarchical_diagrams,
            output_json_path=args.output,
            diagram_name_mapping_path=args.diagram_name_mapping
        )

        # Map the taxonomy
        result = mapper.map_taxonomy()

        if result.success:
            logger.info("Hierarchical domain taxonomy mapping completed successfully")
            logger.info(f"Output saved to: {result.output_path}")
            return 0
        else:
            logger.error(f"Hierarchical domain taxonomy mapping failed: {result.error_message}")
            return 1

    except Exception as e:
        logger.error(f"Error in hierarchical domain taxonomy mapping: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    sys.exit(main())
