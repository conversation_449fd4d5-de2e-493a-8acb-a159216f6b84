"""
Domain File Taxonomy Mapper for Codebase

This module provides functionality to:
1. Read domain_analysis.yaml to get the hierarchical structure of domains
2. Read domain_file_repomap.json to get the file mappings for each domain
3. Read the generated mermaid diagrams from the domain_diagrams directory
4. Combine all this information into a hierarchical JSON structure
5. Save this JSON structure to a file

It can be used as:
1. A standalone script to process domain analysis, file mappings, and diagrams
2. A module that can be integrated into the repository analysis flow
"""

import os
import yaml
import json
import logging
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, field

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class DomainTaxonomyNode:
    """Represents a node in the domain taxonomy hierarchy."""
    name: str
    children: List["DomainTaxonomyNode"] = field(default_factory=list)
    files: List[str] = field(default_factory=list)  # List of file paths
    file_contents: Dict[str, str] = field(default_factory=dict)  # Mapping of file paths to their content
    diagram: Optional[str] = None  # Leaf-level diagram content
    diagram_path: Optional[str] = None  # Leaf-level diagram path
    diagram_name: Optional[str] = None  # Standardized diagram filename (e.g., "L1_Domain_Subdomain")
    hierarchical_diagram: Optional[str] = None  # Hierarchical diagram content
    hierarchical_diagram_path: Optional[str] = None  # Hierarchical diagram path
    hierarchical_diagram_name: Optional[str] = None  # Hierarchical diagram filename
    full_path: Optional[str] = None  # Full path from root to this node (e.g., "A -> B -> C")

@dataclass
class DomainTaxonomyMapperResult:
    """Result of mapping domain taxonomy."""
    success: bool = True
    error_message: str = ""
    taxonomy_json: Dict[str, Any] = field(default_factory=dict)
    output_path: Optional[str] = None

class DomainFileTaxonomyMapper:
    """
    Maps domain taxonomy from domain analysis, file mappings, and diagrams.

    This class reads domain analysis YAML, domain file repomap JSON, and mermaid diagrams,
    and combines them into a hierarchical JSON structure.
    """

    def __init__(
        self,
        domain_analysis_yaml_path: str,
        domain_file_repomap_path: str,  # Changed from domain_traces_yaml_path
        domain_diagrams_dir: str,
        output_json_path: str,
        diagram_name_mapping_path: Optional[str] = None,
    ):
        """
        Initialize the domain taxonomy mapper.

        Args:
            domain_analysis_yaml_path: Path to the domain analysis YAML file
            domain_file_repomap_path: Path to the domain file repomap JSON file
            domain_diagrams_dir: Directory containing the generated mermaid diagrams
            output_json_path: Path to save the output JSON file
            diagram_name_mapping_path: Path to the diagram name mapping JSON file (optional)
        """
        self.domain_analysis_yaml_path = domain_analysis_yaml_path
        self.domain_file_repomap_path = domain_file_repomap_path  # Changed from domain_traces_yaml_path
        self.domain_diagrams_dir = domain_diagrams_dir
        # Use the diagrams_from_json directory directly
        self.diagrams_with_fn_info_dir = domain_diagrams_dir
        self.output_json_path = output_json_path
        self.diagram_name_mapping_path = diagram_name_mapping_path

        # If diagram_name_mapping_path is not provided, check if it exists in the default locations
        if not self.diagram_name_mapping_path:
            # First check in the domain_diagrams directory
            default_mapping_path = os.path.join(domain_diagrams_dir, "diagram_name_mapping.json")
            if os.path.exists(default_mapping_path):
                self.diagram_name_mapping_path = default_mapping_path
                logger.info(f"Using default diagram name mapping path: {default_mapping_path}")
            else:
                # Then check in the parent directory
                parent_mapping_path = os.path.join(os.path.dirname(domain_diagrams_dir), "diagram_name_mapping.json")
                if os.path.exists(parent_mapping_path):
                    self.diagram_name_mapping_path = parent_mapping_path
                    logger.info(f"Using parent directory diagram name mapping path: {parent_mapping_path}")

        # Initialize data structures
        self.domain_hierarchy = {}
        self.domain_files = {}  # Changed from domain_traces
        self.domain_diagrams = {}
        self.diagram_name_mapping = {}

        # Track domain paths
        self.domain_paths = {}  # Maps domain name to full path
        self.path_to_node = {}  # Maps full path to node in the hierarchy

    def read_domain_analysis(self) -> Dict[str, Any]:
        """
        Read the domain analysis YAML file.

        Returns:
            Dictionary containing domain analysis data
        """
        logger.info(f"Reading domain analysis YAML file: {self.domain_analysis_yaml_path}")
        try:
            with open(self.domain_analysis_yaml_path, 'r') as f:
                domain_data = yaml.safe_load(f)
            return domain_data
        except Exception as e:
            logger.error(f"Error reading domain analysis YAML file: {e}")
            raise

    def read_domain_file_repomap(self) -> Dict[str, Dict[str, str]]:
        """
        Read the domain file repomap JSON file.

        Returns:
            Dictionary mapping domain paths to dictionaries of file paths and their content
        """
        logger.info(f"Reading domain file repomap JSON file: {self.domain_file_repomap_path}")
        try:
            with open(self.domain_file_repomap_path, 'r') as f:
                repomap_data = json.load(f)

            # Keep the full structure with file content
            logger.info(f"Found {len(repomap_data)} domains with file mappings")
            return repomap_data
        except Exception as e:
            logger.error(f"Error reading domain file repomap JSON file: {e}")
            return {}

    def read_diagram_name_mapping(self) -> Dict[str, str]:
        """
        Read the diagram name mapping from the JSON file.

        Returns:
            Dictionary mapping standardized diagram names to domain paths
        """
        # First try the specified path
        if self.diagram_name_mapping_path and os.path.exists(self.diagram_name_mapping_path):
            try:
                with open(self.diagram_name_mapping_path, 'r') as f:
                    mapping = json.load(f)

                logger.info(f"Loaded diagram name mapping with {len(mapping)} entries from {self.diagram_name_mapping_path}")
                return mapping
            except Exception as e:
                logger.error(f"Error reading diagram name mapping from {self.diagram_name_mapping_path}: {e}")

        # If that fails, try looking in the parent directory of the output directory
        output_dir = os.path.dirname(self.output_json_path)
        alt_mapping_path = os.path.join(output_dir, "diagram_name_mapping.json")

        if os.path.exists(alt_mapping_path):
            try:
                with open(alt_mapping_path, 'r') as f:
                    mapping = json.load(f)

                logger.info(f"Loaded diagram name mapping with {len(mapping)} entries from {alt_mapping_path}")
                return mapping
            except Exception as e:
                logger.error(f"Error reading diagram name mapping from {alt_mapping_path}: {e}")

        # If that fails too, try looking in the diagrams directory and its subdirectories
        for root_dir in [self.domain_diagrams_dir, self.diagrams_with_fn_info_dir]:
            alt_mapping_path = os.path.join(root_dir, "diagram_name_mapping.json")
            if os.path.exists(alt_mapping_path):
                try:
                    with open(alt_mapping_path, 'r') as f:
                        mapping = json.load(f)

                    logger.info(f"Loaded diagram name mapping with {len(mapping)} entries from {alt_mapping_path}")
                    return mapping
                except Exception as e:
                    logger.error(f"Error reading diagram name mapping from {alt_mapping_path}: {e}")

        # If all else fails, log a warning and return an empty mapping
        logger.warning("Could not find diagram name mapping file in any of the expected locations")
        return {}

    def read_domain_diagrams(self) -> Dict[str, Dict[str, str]]:
        """
        Read the mermaid diagrams from the domain diagrams directory.

        Returns:
            Dictionary mapping domain paths to diagram content and file path
        """
        logger.info(f"Reading mermaid diagrams from: {self.diagrams_with_fn_info_dir}")
        diagrams = {}

        try:
            if not os.path.exists(self.diagrams_with_fn_info_dir):
                logger.warning(f"Diagrams directory not found: {self.diagrams_with_fn_info_dir}")
                return diagrams

            # First, try to use the diagram name mapping if available
            self.diagram_name_mapping = self.read_diagram_name_mapping()

            if self.diagram_name_mapping:
                logger.info(f"Using diagram name mapping with {len(self.diagram_name_mapping)} entries")

                # Use the mapping to load diagrams
                for standardized_name, domain_path in self.diagram_name_mapping.items():
                    diagram_file = f"{standardized_name}.md"
                    diagram_path = os.path.join(self.diagrams_with_fn_info_dir, diagram_file)

                    if os.path.exists(diagram_path):
                        with open(diagram_path, 'r') as f:
                            diagram_content = f.read()

                        diagrams[domain_path] = {
                            'content': diagram_content,
                            'path': diagram_path
                        }

                        logger.debug(f"Loaded diagram for {domain_path} using mapping")
                    else:
                        logger.warning(f"Diagram file not found: {diagram_path}")
            else:
                # Fall back to the old method if no mapping is available
                logger.info("No diagram name mapping found, using filename-based approach")

                # Get all .md files in the diagrams directory
                diagram_files = [f for f in os.listdir(self.diagrams_with_fn_info_dir)
                                if f.endswith('.md') and not f.endswith('_raw.md')]

                logger.info(f"Found {len(diagram_files)} diagram files in directory")

                for diagram_file in diagram_files:
                    # Check if it's a standardized name (starts with L followed by a number)
                    if diagram_file.startswith('L') and diagram_file[1].isdigit():
                        # Skip files with standardized names since we don't have a mapping
                        logger.debug(f"Skipping standardized filename without mapping: {diagram_file}")
                        continue

                    # Convert filename to domain path using the new method for files like:
                    # Compilation,_Optimization_&_Code_Generation_TorchInductor_and_Compilation_Stack_Codegen_(CPU_CUDA_MPS).md

                    # First, remove the .md extension
                    filename_without_ext = diagram_file.replace('.md', '')

                    # Convert underscores to spaces for better readability
                    domain_path = filename_without_ext.replace('_', ' ')

                    # Read the diagram content
                    diagram_path = os.path.join(self.diagrams_with_fn_info_dir, diagram_file)
                    with open(diagram_path, 'r') as f:
                        diagram_content = f.read()

                    diagrams[domain_path] = {
                        'content': diagram_content,
                        'path': diagram_path
                    }

                    logger.debug(f"Loaded diagram: {domain_path}")

            logger.info(f"Successfully loaded {len(diagrams)} mermaid diagrams")
            return diagrams
        except Exception as e:
            logger.error(f"Error reading mermaid diagrams: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return {}

    def build_taxonomy_tree(self, domain_data: Dict[str, Any]) -> DomainTaxonomyNode:
        """
        Build a taxonomy tree from the domain analysis data.

        Args:
            domain_data: Dictionary containing domain analysis data

        Returns:
            Root node of the taxonomy tree
        """
        logger.info("Building taxonomy tree from domain analysis data")

        # Create root node
        root = DomainTaxonomyNode(name="Root")

        # Process areas recursively
        areas = domain_data.get('areas', [])
        for area in areas:
            area_node = self._process_area(area, [])
            root.children.append(area_node)

        return root

    def _process_area(self, area: Dict[str, Any], parent_path: List[str]) -> DomainTaxonomyNode:
        """
        Process an area in the domain hierarchy recursively.

        Args:
            area: Dictionary containing area data
            parent_path: List of parent area names

        Returns:
            Node representing this area
        """
        area_name = area.get('name', '')
        current_path = parent_path + [area_name]
        full_path = " -> ".join(current_path)

        # Create node for this area
        node = DomainTaxonomyNode(
            name=area_name,
            full_path=full_path
        )

        # Store mapping from path to node
        self.path_to_node[full_path] = node
        self.domain_paths[area_name] = full_path

        # Process subareas recursively
        subareas = area.get('subareas', [])
        for subarea in subareas:
            subarea_node = self._process_area(subarea, current_path)
            node.children.append(subarea_node)

        return node

    def extract_keywords(self, path: str) -> List[str]:
        """
        Extract important keywords from a path string.

        Args:
            path: The path string to extract keywords from

        Returns:
            List of important keywords
        """
        # Normalize the path
        normalized = path.lower()

        # Replace common separators with spaces
        normalized = normalized.replace('_', ' ').replace('-', ' ').replace('/', ' ').replace(' -> ', ' ')

        # Replace special characters
        normalized = normalized.replace('&', 'and').replace(',', '').replace('(', '').replace(')', '')

        # Split into words and filter out common words and short words
        words = normalized.split()
        stopwords = {'and', 'the', 'of', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'as', 'from'}
        keywords = [word for word in words if word not in stopwords and len(word) > 2]

        return keywords

    def keyword_similarity(self, path1: str, path2: str) -> float:
        """
        Calculate keyword-based similarity between two paths.

        Args:
            path1: First path string
            path2: Second path string

        Returns:
            Similarity score between 0 and 1
        """
        keywords1 = set(self.extract_keywords(path1))
        keywords2 = set(self.extract_keywords(path2))

        if not keywords1 or not keywords2:
            return 0.0

        # Calculate Jaccard similarity
        intersection = len(keywords1.intersection(keywords2))
        union = len(keywords1.union(keywords2))

        # Calculate weighted score that favors more matching keywords
        if union == 0:
            return 0.0

        jaccard = intersection / union

        # Bonus for having more matching keywords
        match_bonus = min(1.0, intersection / 3)  # Bonus maxes out at 3 matching keywords

        # Final score is weighted combination
        return 0.7 * jaccard + 0.3 * match_bonus

    def map_files_to_taxonomy(self, root: DomainTaxonomyNode, domain_files: Dict[str, Dict[str, str]]) -> None:
        """
        Map files from domain file repomap to the taxonomy tree using keyword-based similarity.

        Args:
            root: Root node of the taxonomy tree
            domain_files: Dictionary mapping domain paths to dictionaries of file paths and their content
        """
        logger.info("Mapping files from domain file repomap to taxonomy tree")

        # For each domain path, find the corresponding node in the taxonomy tree
        matched_domains = 0
        missing_domains = []

        for domain_path, files_dict in domain_files.items():
            # Use keyword-based matching
            best_match = None
            best_score = 0.0

            for node_path in self.path_to_node.keys():
                # Calculate keyword similarity
                similarity = self.keyword_similarity(domain_path, node_path)

                # If this is the best match so far, store it
                if similarity > best_score and similarity > 0.6:  # Threshold for matching
                    best_score = similarity
                    best_match = node_path

            if best_match:
                node = self.path_to_node[best_match]
                # Extract just the file paths (keys) for the node's files list
                node.files = list(files_dict.keys())
                # Store the file contents
                node.file_contents = files_dict
                matched_domains += 1
                logger.debug(f"Mapped {len(files_dict)} files to {best_match} (keyword match with score {best_score:.2f})")
            else:
                # If no match found, log and add to missing domains
                logger.warning(f"Domain path not found in taxonomy tree: {domain_path}")
                missing_domains.append((domain_path, files_dict))

        # Handle missing domains by adding them to the taxonomy tree
        if missing_domains:
            logger.info(f"Adding {len(missing_domains)} missing domains to the taxonomy tree")
            for domain_path, files_dict in missing_domains:
                # Split the domain path into components
                if '/' in domain_path:
                    components = domain_path.split('/')
                else:
                    components = domain_path.split(' -> ')

                # Find or create parent nodes
                current_node = root
                current_path = []

                for i, component in enumerate(components):
                    current_path.append(component)
                    full_path = ' -> '.join(current_path)

                    # Check if this node already exists
                    child_node = None
                    for child in current_node.children:
                        if child.name == component:
                            child_node = child
                            break

                    # If not, create it
                    if not child_node:
                        child_node = DomainTaxonomyNode(
                            name=component,
                            full_path=full_path
                        )
                        current_node.children.append(child_node)
                        self.path_to_node[full_path] = child_node
                        logger.debug(f"Created new node: {full_path}")

                    # Move to the next level
                    current_node = child_node

                # Add files to the leaf node
                current_node.files = list(files_dict.keys())
                current_node.file_contents = files_dict
                matched_domains += 1
                logger.debug(f"Added files to new node: {current_node.full_path}")

        logger.info(f"Successfully mapped {matched_domains} out of {len(domain_files)} domains to taxonomy tree")

    def map_diagrams_to_taxonomy(self, root: DomainTaxonomyNode, domain_diagrams: Dict[str, Dict[str, str]]) -> None:
        """
        Map diagrams to the taxonomy tree using keyword-based similarity.

        Args:
            root: Root node of the taxonomy tree
            domain_diagrams: Dictionary mapping domain paths to diagram content
        """
        logger.info("Mapping diagrams to taxonomy tree")

        # Create a reverse mapping from diagram paths to standardized names
        diagram_path_to_name = {}
        if self.diagram_name_mapping:
            # Invert the mapping from standardized names to domain paths
            for std_name, domain_path in self.diagram_name_mapping.items():
                diagram_path_to_name[domain_path] = std_name

        # For each diagram, find the corresponding node in the taxonomy tree
        matched_diagrams = 0
        for diagram_path, diagram_info in domain_diagrams.items():
            # Use keyword-based matching
            best_match = None
            best_score = 0.0

            for node_path in self.path_to_node.keys():
                # Calculate keyword similarity
                similarity = self.keyword_similarity(diagram_path, node_path)

                # If this is the best match so far, store it
                if similarity > best_score and similarity > 0.6:  # Threshold for matching
                    best_score = similarity
                    best_match = node_path

            if best_match:
                node = self.path_to_node[best_match]
                node.diagram = diagram_info['content']
                node.diagram_path = diagram_info['path']

                # Add the standardized diagram name if available
                if diagram_path in diagram_path_to_name:
                    node.diagram_name = diagram_path_to_name[diagram_path]
                    logger.debug(f"Set diagram_name to {node.diagram_name} for {diagram_path} (from mapping)")
                else:
                    # Extract filename from path as fallback
                    node.diagram_name = os.path.basename(diagram_info['path'])
                    logger.debug(f"Set diagram_name to {node.diagram_name} for {diagram_path} (from basename)")

                matched_diagrams += 1
                logger.debug(f"Mapped diagram to {best_match} (keyword match with score {best_score:.2f})")
            else:
                logger.warning(f"Diagram path not found in taxonomy tree: {diagram_path}")

        logger.info(f"Successfully mapped {matched_diagrams} out of {len(domain_diagrams)} diagrams to taxonomy tree")

    def taxonomy_tree_to_json(self, root: DomainTaxonomyNode) -> Dict[str, Any]:
        """
        Convert the taxonomy tree to a JSON structure.

        Args:
            root: Root node of the taxonomy tree

        Returns:
            Dictionary representing the taxonomy tree in JSON format
        """
        logger.info("Converting taxonomy tree to JSON structure")

        def node_to_dict(node: DomainTaxonomyNode) -> Dict[str, Any]:
            """Convert a node to a dictionary."""
            result = {
                "name": node.name,
                "full_path": node.full_path
            }

            if node.children:
                result["children"] = [node_to_dict(child) for child in node.children]

            if node.files:  # List of file paths
                result["files"] = node.files

            if node.file_contents:  # Dictionary of file paths to content
                result["file_contents"] = node.file_contents

            if node.diagram:
                result["diagram"] = node.diagram

            if node.diagram_path:
                result["diagram_path"] = node.diagram_path

            if node.diagram_name:
                result["diagram_name"] = node.diagram_name
                logger.debug(f"Added diagram_name {node.diagram_name} to JSON for node {node.name}")

            # Add hierarchical diagram if available
            if node.hierarchical_diagram:
                result["hierarchical_diagram"] = node.hierarchical_diagram

            if node.hierarchical_diagram_path:
                result["hierarchical_diagram_path"] = node.hierarchical_diagram_path

            if node.hierarchical_diagram_name:
                result["hierarchical_diagram_name"] = node.hierarchical_diagram_name
                logger.debug(f"Added hierarchical_diagram_name {node.hierarchical_diagram_name} to JSON for node {node.name}")

            return result

        return node_to_dict(root)

    def save_taxonomy_json(self, taxonomy_json: Dict[str, Any]) -> None:
        """
        Save the taxonomy JSON to a file.

        Args:
            taxonomy_json: Dictionary representing the taxonomy tree in JSON format
        """
        logger.info(f"Saving taxonomy JSON to: {self.output_json_path}")
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self.output_json_path), exist_ok=True)

            # Save JSON with indentation for readability
            with open(self.output_json_path, 'w') as f:
                json.dump(taxonomy_json, f, indent=2)

            logger.info(f"Taxonomy JSON saved to: {self.output_json_path}")
        except Exception as e:
            logger.error(f"Error saving taxonomy JSON: {e}")
            raise

    def find_combined_diagrams(self) -> Dict[str, Dict[str, str]]:
        """
        Find combined diagrams and the codebase overview diagram.

        Returns:
            Dictionary mapping domain paths to diagram info for combined diagrams
        """
        logger.info("Finding combined diagrams and codebase overview diagram")
        combined_diagrams = {}

        # Check if we have a diagram name mapping
        if not self.diagram_name_mapping:
            logger.warning("No diagram name mapping found, cannot identify combined diagrams")
            return combined_diagrams

        # Look for combined diagrams in the mapping
        for std_name, domain_path in self.diagram_name_mapping.items():
            # Check if it's a combined diagram (contains "Combined" in the name or path)
            is_combined = "Combined" in std_name or "combined" in domain_path
            # Check if it's the codebase overview diagram
            is_overview = std_name == "L0_Codebase_Overview" or domain_path == "Codebase Overview"

            if is_combined or is_overview:
                # Find the diagram file
                diagram_file = f"{std_name}.md"
                diagram_path = os.path.join(self.diagrams_with_fn_info_dir, diagram_file)

                if os.path.exists(diagram_path):
                    with open(diagram_path, 'r') as f:
                        diagram_content = f.read()

                    combined_diagrams[domain_path] = {
                        'content': diagram_content,
                        'path': diagram_path,
                        'name': std_name
                    }

                    logger.info(f"Found combined diagram: {domain_path} -> {std_name}")

        logger.info(f"Found {len(combined_diagrams)} combined diagrams")
        return combined_diagrams

    def add_combined_diagrams_to_json(self, taxonomy_json: Dict[str, Any], combined_diagrams: Dict[str, Dict[str, str]]) -> Dict[str, Any]:
        """
        Add combined diagrams and the codebase overview diagram to the JSON structure.

        Args:
            taxonomy_json: The taxonomy JSON structure
            combined_diagrams: Dictionary mapping domain paths to diagram info for combined diagrams

        Returns:
            Updated taxonomy JSON structure with combined diagrams
        """
        logger.info("Adding combined diagrams to JSON structure")

        # First, handle the codebase overview diagram
        if "Codebase Overview" in combined_diagrams:
            overview_info = combined_diagrams["Codebase Overview"]
            taxonomy_json["codebase_overview"] = {
                "name": "Codebase Overview",
                "diagram": overview_info["content"],
                "diagram_path": overview_info["path"],
                "diagram_name": overview_info["name"]
            }
            logger.info("Added codebase overview diagram to JSON structure")

        # Then, handle the combined diagrams for top-level domains
        for domain_path, diagram_info in combined_diagrams.items():
            if domain_path == "Codebase Overview":
                continue  # Already handled

            # Extract the domain name from the path
            if " (combined)" in domain_path:
                domain_name = domain_path.replace(" (combined)", "")

                # Find the corresponding node in the taxonomy JSON
                def find_and_update_node(node):
                    if node.get("name") == domain_name:
                        # Add the combined diagram to this node
                        node["combined_diagram"] = diagram_info["content"]
                        node["combined_diagram_path"] = diagram_info["path"]
                        node["combined_diagram_name"] = diagram_info["name"]
                        return True

                    if "children" in node:
                        for child in node["children"]:
                            if find_and_update_node(child):
                                return True

                    return False

                if find_and_update_node(taxonomy_json):
                    logger.info(f"Added combined diagram for {domain_name} to JSON structure")
                else:
                    logger.warning(f"Could not find node for combined diagram: {domain_path}")

        return taxonomy_json

    def map_taxonomy(self) -> DomainTaxonomyMapperResult:
        """
        Map domain taxonomy from domain analysis, file mappings, and diagrams.

        Returns:
            DomainTaxonomyMapperResult containing the mapping results
        """
        result = DomainTaxonomyMapperResult()

        try:
            # Read domain analysis, file mappings, and diagrams
            logger.info("Step 1: Reading domain analysis YAML")
            domain_data = self.read_domain_analysis()

            logger.info("Step 2: Reading domain file repomap JSON")
            self.domain_files = self.read_domain_file_repomap()  # Changed from domain_traces
            logger.info(f"Found {len(self.domain_files)} domains with file mappings")

            logger.info("Step 3: Reading domain diagrams")
            self.domain_diagrams = self.read_domain_diagrams()
            logger.info(f"Found {len(self.domain_diagrams)} domain diagrams")

            # Log some sample diagrams for debugging
            diagram_paths = list(self.domain_diagrams.keys())
            if diagram_paths:
                logger.info(f"Sample diagram paths (up to 5): {diagram_paths[:5]}")

            # Find combined diagrams and codebase overview diagram
            logger.info("Step 4: Finding combined diagrams")
            combined_diagrams = self.find_combined_diagrams()

            # Build taxonomy tree
            logger.info("Step 5: Building taxonomy tree")
            root = self.build_taxonomy_tree(domain_data)
            logger.info(f"Built taxonomy tree with {len(self.path_to_node)} nodes")

            # Log some sample nodes for debugging
            node_paths = list(self.path_to_node.keys())
            if node_paths:
                logger.info(f"Sample node paths (up to 5): {node_paths[:5]}")

            # Map files and diagrams to taxonomy
            logger.info("Step 6: Mapping files to taxonomy")
            self.map_files_to_taxonomy(root, self.domain_files)  # Changed from map_functions_to_taxonomy

            logger.info("Step 7: Mapping diagrams to taxonomy")
            self.map_diagrams_to_taxonomy(root, self.domain_diagrams)

            # Convert taxonomy tree to JSON
            logger.info("Step 8: Converting taxonomy tree to JSON")
            taxonomy_json = self.taxonomy_tree_to_json(root)

            # Add combined diagrams to the JSON structure
            logger.info("Step 9: Adding combined diagrams to JSON structure")
            taxonomy_json = self.add_combined_diagrams_to_json(taxonomy_json, combined_diagrams)

            # Count diagrams and diagram names in the taxonomy tree
            leaf_diagram_count = 0
            leaf_diagram_name_count = 0
            hierarchical_diagram_count = 0
            hierarchical_diagram_name_count = 0

            def count_diagrams(node_dict):
                nonlocal leaf_diagram_count, leaf_diagram_name_count, hierarchical_diagram_count, hierarchical_diagram_name_count

                # Count leaf-level diagrams
                if 'diagram_path' in node_dict and node_dict['diagram_path']:
                    leaf_diagram_count += 1
                if 'diagram_name' in node_dict and node_dict['diagram_name']:
                    leaf_diagram_name_count += 1

                # Count hierarchical diagrams
                if 'hierarchical_diagram_path' in node_dict and node_dict['hierarchical_diagram_path']:
                    hierarchical_diagram_count += 1
                if 'hierarchical_diagram_name' in node_dict and node_dict['hierarchical_diagram_name']:
                    hierarchical_diagram_name_count += 1

                # Recursively count children
                if 'children' in node_dict:
                    for child in node_dict['children']:
                        count_diagrams(child)

            count_diagrams(taxonomy_json)
            logger.info(f"Taxonomy tree contains {leaf_diagram_count} leaf diagrams and {hierarchical_diagram_count} hierarchical diagrams")

            # Save taxonomy JSON
            logger.info("Step 10: Saving taxonomy JSON")
            self.save_taxonomy_json(taxonomy_json)

            # Set result
            result.taxonomy_json = taxonomy_json
            result.output_path = self.output_json_path

            logger.info("Domain taxonomy mapping completed successfully")

        except Exception as e:
            logger.error(f"Error mapping domain taxonomy: {e}")
            import traceback
            logger.error(traceback.format_exc())
            result.success = False
            result.error_message = str(e)

        return result


def main():
    """Main entry point for the domain file taxonomy mapper."""
    import argparse
    import os

    parser = argparse.ArgumentParser(description="Map domain taxonomy from analysis, file mappings, and diagrams")
    # parser.add_argument("--domain-analysis", help="Path to the domain analysis YAML file")
    # parser.add_argument("--domain-file-repomap", help="Path to the domain file repomap JSON file")
    # parser.add_argument("--domain-diagrams", help="Directory containing the generated mermaid diagrams")
    # parser.add_argument("--output", help="Path to save the output JSON file")
    # parser.add_argument("--diagram-name-mapping", help="Path to the diagram name mapping JSON file (optional)")

    args = parser.parse_args()

    # args.domain_analysis = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/outputs/django/domain_analysis/domain_analysis.yaml"
    # args.domain_file_repomap = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/outputs/django/domain_file_repomap/domain_file_repomap.json"
    # args.domain_diagrams = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/outputs/django/diagrams/diagrams_from_json"
    # args.output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/outputs/django/domain_file_taxonomy/taxonomy.json"
    # args.diagram_name_mapping = "taxonomy.json"


    args.domain_analysis = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/outputs/datadog_agent/domain_analysis/domain_analysis.yaml"
    args.domain_file_repomap = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/outputs/datadog_agent/domain_file_repomap/domain_file_repomap.json"
    args.domain_diagrams = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/outputs/datadog_agent/diagrams/hierarchical_diagrams/diagrams_from_json"
    args.output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/outputs/datadog_agent/taxonomy/taxonomy.json"
    args.diagram_name_mapping = "taxonomy.json"


    # Default paths - use current directory as base
    outputs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "outputs", "bracket")

    # if not hasattr(args, 'domain_analysis') or not args.domain_analysis:
    #     args.domain_analysis = os.path.join(outputs_dir, "domain_analysis", "domain_analysis.yaml")

    # if not hasattr(args, 'domain_file_repomap') or not args.domain_file_repomap:
    #     args.domain_file_repomap = os.path.join(outputs_dir, "domain_file_repomap", "domain_file_repomap.json")

    # if not hasattr(args, 'domain_diagrams') or not args.domain_diagrams:
    #     args.domain_diagrams = os.path.join(outputs_dir, "diagrams", "diagrams_from_json")

    # if not hasattr(args, 'output') or not args.output:
    #     args.output = os.path.join(outputs_dir, "domain_file_taxonomy", "taxonomy.json")
    #     # Create output directory if it doesn't exist
    #     os.makedirs(os.path.dirname(args.output), exist_ok=True)

    # if not hasattr(args, 'diagram_name_mapping') or not args.diagram_name_mapping:
    #     args.diagram_name_mapping = None

    try:
        mapper = DomainFileTaxonomyMapper(
            domain_analysis_yaml_path=args.domain_analysis,
            domain_file_repomap_path=args.domain_file_repomap,
            domain_diagrams_dir=args.domain_diagrams,
            output_json_path=args.output,
            diagram_name_mapping_path=args.diagram_name_mapping
        )

        result = mapper.map_taxonomy()

        if result.success:
            logger.info("Domain taxonomy mapping completed successfully")
            return 0
        else:
            logger.error(f"Domain taxonomy mapping failed: {result.error_message}")
            return 1

    except Exception as e:
        logger.error(f"Error in domain taxonomy mapping: {e}")
        return 1


if __name__ == "__main__":
    import sys
    sys.exit(main())
