"""
Generate Domain Taxonomy JSON

This script generates a hierarchical JSON structure that combines domain analysis,
domain traces, and mermaid diagrams into a single taxonomy representation.

Usage:
    python -m bracket_core.generate_domain_taxonomy --domain-analysis <path> --domain-traces <path> --domain-diagrams <dir> --output <path>
"""

import os
import argparse
import logging
import asyncio
import json
from typing import Optional
from bracket_core.domain_taxonomy_mapper import DomainTaxonomyMapper

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def generate_domain_taxonomy(
    domain_analysis_yaml_path: str,
    domain_traces_yaml_path: str,
    domain_diagrams_dir: str,
    output_json_path: str,
    diagram_name_mapping_path: Optional[str] = None
) -> bool:
    """
    Generate domain taxonomy JSON from domain analysis, traces, and diagrams.

    This function reads the domain analysis YAML, domain traces YAML, and mermaid diagrams,
    and combines them into a hierarchical JSON structure.

    Args:
        domain_analysis_yaml_path: Path to the domain analysis YAML file
        domain_traces_yaml_path: Path to the domain traces YAML file
        domain_diagrams_dir: Directory containing the generated mermaid diagrams
        output_json_path: Path to save the output JSON file
        diagram_name_mapping_path: Path to the diagram name mapping JSON file (optional)

    Returns:
        bool: True if taxonomy generation was successful, False otherwise
    """
    logger.info("Generating domain taxonomy JSON")

    # Check if the domain analysis YAML file exists
    if not os.path.exists(domain_analysis_yaml_path):
        logger.error(f"Domain analysis YAML file not found: {domain_analysis_yaml_path}")
        return False

    # Check if the domain traces YAML file exists
    if not os.path.exists(domain_traces_yaml_path):
        logger.error(f"Domain traces YAML file not found: {domain_traces_yaml_path}")
        return False

    # Check if the domain diagrams directory exists
    if not os.path.exists(domain_diagrams_dir):
        logger.error(f"Domain diagrams directory not found: {domain_diagrams_dir}")
        return False

    try:
        # If diagram_name_mapping_path is not provided, check if it exists in the expected location
        if diagram_name_mapping_path is None:
            potential_mapping_path = os.path.join(os.path.dirname(domain_diagrams_dir), "diagram_name_mapping.json")
            if os.path.exists(potential_mapping_path):
                diagram_name_mapping_path = potential_mapping_path
                logger.info(f"Found diagram name mapping file: {diagram_name_mapping_path}")

        # Create domain taxonomy mapper
        mapper = DomainTaxonomyMapper(
            domain_analysis_yaml_path=domain_analysis_yaml_path,
            domain_traces_yaml_path=domain_traces_yaml_path,
            domain_diagrams_dir=domain_diagrams_dir,
            output_json_path=output_json_path,
            diagram_name_mapping_path=diagram_name_mapping_path
        )

        # Map domain taxonomy
        result = mapper.map_taxonomy()

        if result.success:
            logger.info(f"Domain taxonomy JSON generated successfully: {result.output_path}")
            return True
        else:
            logger.error(f"Domain taxonomy JSON generation failed: {result.error_message}")
            return False

    except Exception as e:
        logger.error(f"Error in domain taxonomy JSON generation: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

async def main():
    """Main entry point for the domain taxonomy generator."""
    parser = argparse.ArgumentParser(description="Generate domain taxonomy JSON")
    # parser.add_argument("--domain-analysis", required=True, help="Path to the domain analysis YAML file")
    # parser.add_argument("--domain-traces", required=True, help="Path to the domain traces YAML file")
    # parser.add_argument("--domain-diagrams", required=True, help="Directory containing the generated mermaid diagrams")
    # parser.add_argument("--output", required=True, help="Path to save the output JSON file")
    # parser.add_argument("--diagram-name-mapping", help="Path to the diagram name mapping JSON file (optional)")

    args = parser.parse_args()

    # args.domain_analysis = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/domain_analysis.yaml"
    # args.domain_traces = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/domain_traces.yaml"
    # # args.domain_diagrams = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/mermaid_outputs_improved"
    # args.domain_diagrams = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/improved_mermaid_outputs"
    # args.output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/domain_taxonomy_final.json"
    # args.diagram_name_mapping = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/mermaid_outputs/diagram_name_mapping_final.json"


    args.domain_analysis = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/domain_analysis.yaml"
    args.domain_traces = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/domain_traces.yaml"
    # args.domain_diagrams = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/mermaid_outputs_improved"
    args.domain_diagrams = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/improved_mermaid_outputs"
    args.output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/domain_taxonomy_final.json"
    args.diagram_name_mapping = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/mermaid_outputs/diagram_name_mapping_final.json"

    try:
        success = await generate_domain_taxonomy(
            domain_analysis_yaml_path=args.domain_analysis,
            domain_traces_yaml_path=args.domain_traces,
            domain_diagrams_dir=args.domain_diagrams,
            output_json_path=args.output,
            diagram_name_mapping_path=args.diagram_name_mapping
        )

        return 0 if success else 1

    except Exception as e:
        logger.error(f"Error generating domain taxonomy JSON: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
