"""OpenRouter client implementation."""

import os
import logging
import json
import asyncio
from typing import Any, Dict, List, Optional, Union, AsyncGenerator

import openai
from openai import OpenAI, AsyncOpenAI

from bracket_core.llm.api_keys import get_openrouter_api_key

log = logging.getLogger(__name__)

class OpenRouterClient:
    """Client for OpenRouter API."""

    def __init__(
        self,
        api_key: Optional[str] = None,
        # model: str = "google/gemini-2.5-pro-exp-03-25:free",
        model: str = "google/gemini-2.5-pro-preview",
        max_tokens: int = 4096,
        temperature: float = 0.0,
        base_url: str = "https://openrouter.ai/api/v1",
        max_retries: int = 3,
        request_timeout: Optional[float] = None,
    ):
        """Initialize the OpenRouter client.

        Args:
            api_key: OpenRouter API key. If None, will try to get from environment.
            model: Model to use (e.g., "google/gemini-2.5-pro-exp-03-25:free").
            max_tokens: Maximum tokens to generate.
            temperature: Sampling temperature.
            base_url: Base URL for the OpenRouter API.
            max_retries: Maximum number of retries for API calls.
            request_timeout: Timeout for API requests in seconds.
        """
        # Get API key from centralized management
        self.api_key = get_openrouter_api_key(provided_key=api_key)

        self.model = model
        self.max_tokens = max_tokens
        self.temperature = temperature
        self.base_url = base_url
        self.max_retries = max_retries
        self.request_timeout = request_timeout

        # Initialize OpenAI client with OpenRouter base URL
        self.client = OpenAI(
            api_key=self.api_key,
            base_url=self.base_url,
            timeout=self.request_timeout,
            max_retries=self.max_retries,
        )

        # Initialize async client
        self.async_client = AsyncOpenAI(
            api_key=self.api_key,
            base_url=self.base_url,
            timeout=self.request_timeout,
            max_retries=self.max_retries,
        )

        # Set default headers for OpenRouter
        self.default_headers = {
            "HTTP-Referer": "https://github.com/bracket-ai/bracket",
            "X-Title": "Bracket Code",
        }

    async def generate(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        stream: bool = False,
        **kwargs
    ) -> str:
        """Generate text using the OpenRouter API.

        Args:
            prompt: User prompt.
            system_prompt: System prompt.
            max_tokens: Maximum tokens to generate.
            temperature: Sampling temperature.
            stream: Whether to stream the response.
            **kwargs: Additional arguments to pass to the API.

        Returns:
            Generated text response.
        """
        try:
            messages = []

            # Add system prompt if provided
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})

            # Add user prompt
            messages.append({"role": "user", "content": prompt})

            # Set up completion parameters
            completion_params = {
                "model": self.model,
                "messages": messages,
                "max_tokens": max_tokens or self.max_tokens,
                "temperature": temperature or self.temperature,
                "stream": stream,
                **kwargs
            }

            # Set up basic headers for OpenRouter
            headers = self.default_headers.copy()
            headers["HTTP-Referer"] = "https://github.com/bracket-ai/bracket"
            headers["X-Title"] = "Bracket Code"

            # Add the headers to the request
            completion_params["extra_headers"] = headers

            # Make the API call with retry logic for rate limiting
            max_retries = 3
            retry_delay = 5  # Start with 5 seconds delay

            for retry in range(max_retries):
                try:
                    response = await self.async_client.chat.completions.create(**completion_params)

                    if stream:
                        full_response = ""
                        async for chunk in response:
                            if chunk.choices and chunk.choices[0].delta.content:
                                full_response += chunk.choices[0].delta.content
                        return full_response
                    else:
                        # Add proper error handling for the response
                        if response is None:
                            log.error("Received None response from OpenRouter API")
                            return "Error: No response received from OpenRouter API"

                        if not hasattr(response, 'choices') or not response.choices:
                            log.error(f"Invalid response format from OpenRouter API: {response}")
                            if hasattr(response, 'error'):
                                log.error(f"OpenRouter API error: {response.error}")
                                if retry < max_retries - 1:
                                    # If this is a rate limit error (429), wait and retry
                                    if hasattr(response.error, 'code') and response.error.code == 429:
                                        log.warning(f"Rate limit exceeded, retrying in {retry_delay} seconds (attempt {retry+1}/{max_retries})")
                                        await asyncio.sleep(retry_delay)
                                        retry_delay *= 2  # Exponential backoff
                                        continue
                                return f"Error: OpenRouter API error: {response.error}"
                            return f"Error: Invalid response format from OpenRouter API: {response}"

                        # Check if the first choice exists and has a message with content
                        if not hasattr(response.choices[0], 'message') or not hasattr(response.choices[0].message, 'content'):
                            log.error(f"Response missing message or content: {response.choices[0]}")
                            return f"Error: Response missing message or content"

                        return response.choices[0].message.content

                except Exception as e:
                    log.exception(f"Error in OpenRouter API call (attempt {retry+1}/{max_retries}): {e}")
                    if retry < max_retries - 1:
                        # Check if this is a rate limit error
                        if "429" in str(e) or "quota exceeded" in str(e).lower() or "rate limit" in str(e).lower():
                            log.warning(f"Rate limit exceeded, retrying in {retry_delay} seconds")
                            await asyncio.sleep(retry_delay)
                            retry_delay *= 2  # Exponential backoff
                        else:
                            # For other errors, wait a shorter time
                            await asyncio.sleep(1)
                    else:
                        return f"Error: OpenRouter API call failed after {max_retries} attempts: {e}"

            # If we've exhausted all retries and still haven't returned, return a generic error
            return "Error: Failed to get a valid response from OpenRouter after multiple attempts"

        except Exception as e:
            log.exception(f"Error generating with OpenRouter: {e}")
            raise

    async def generate_json(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        json_schema: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate JSON using the OpenRouter API.

        Args:
            prompt: User prompt.
            system_prompt: System prompt.
            json_schema: JSON schema for response validation.
            **kwargs: Additional arguments to pass to the API.

        Returns:
            Parsed JSON response.
        """
        try:
            messages = []

            # Add system prompt if provided
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})

            # Add user prompt
            messages.append({"role": "user", "content": prompt})

            # Set up completion parameters
            completion_params = {
                "model": self.model,
                "messages": messages,
                "max_tokens": kwargs.pop("max_tokens", self.max_tokens),
                "temperature": kwargs.pop("temperature", self.temperature),
                "response_format": {"type": "json_object"},
                **kwargs
            }

            # Note: We're not using transforms parameter as it's causing issues
            # Instead, we're relying on the OpenRouter API to handle the model correctly

            # Make the API call
            response = await self.async_client.chat.completions.create(**completion_params)

            # Add proper error handling for the response
            if response is None:
                log.error("Received None response from OpenRouter API")
                return {"error": "No response received from OpenRouter API"}

            if not hasattr(response, 'choices') or not response.choices:
                log.error(f"Invalid response format from OpenRouter API: {response}")
                return {"error": f"Invalid response format from OpenRouter API: {response}"}

            # Check if the first choice exists and has a message with content
            if not hasattr(response.choices[0], 'message') or not hasattr(response.choices[0].message, 'content'):
                log.error(f"Response missing message or content: {response.choices[0]}")
                return {"error": "Response missing message or content"}

            # Parse the response as JSON
            content = response.choices[0].message.content
            try:
                return json.loads(content)
            except json.JSONDecodeError:
                log.error(f"Failed to parse response as JSON: {content}")
                return {"error": "Failed to parse response as JSON", "content": content}

        except Exception as e:
            log.exception(f"Error generating JSON with OpenRouter: {e}")
            raise

    async def astream_generate(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """Generate text with streaming using the OpenRouter API.

        Args:
            prompt: User prompt.
            system_prompt: System prompt.
            max_tokens: Maximum tokens to generate.
            temperature: Sampling temperature.
            **kwargs: Additional arguments to pass to the API.

        Yields:
            Chunks of generated text.
        """
        try:
            messages = []

            # Add system prompt if provided
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})

            # Add user prompt
            messages.append({"role": "user", "content": prompt})

            # Set up completion parameters
            completion_params = {
                "model": self.model,
                "messages": messages,
                "max_tokens": max_tokens or self.max_tokens,
                "temperature": temperature or self.temperature,
                "stream": True,
                **kwargs
            }

            # Note: We're not using transforms parameter as it's causing issues
            # Instead, we're relying on the OpenRouter API to handle the model correctly

            # Make the API call
            response = await self.async_client.chat.completions.create(**completion_params)

            # Add proper error handling for the response
            if response is None:
                log.error("Received None response from OpenRouter API")
                yield "Error: No response received from OpenRouter API"
                return

            async for chunk in response:
                if chunk.choices and chunk.choices[0].delta and chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content

        except Exception as e:
            log.exception(f"Error streaming with OpenRouter: {e}")
            raise
