"""Initialize LLM and Embedding clients."""

# from azure.identity import DefaultAzureCredential, get_bearer_token_provider

from bracket_core.llm.enums import LLMType
# from graphrag.config.models.graph_rag_config import GraphRagConfig
from bracket_core.llm.oai.chat_openai import Chat<PERSON>pen<PERSON><PERSON>
from bracket_core.llm.oai.embedding import OpenAIEmbedding
from bracket_core.llm.oai.typing import OpenaiApiType
from bracket_core.llm.anthropic.client import Claude<PERSON>lient
from bracket_core.llm.openrouter.client import OpenRouterClient
from bracket_core.llm.api_keys import get_openrouter_api_key, get_anthropic_api_key


# def get_llm(config: GraphRagConfig) -> ChatOpenAI:
#     """Get the LLM client."""
#     default_llm_settings = config.get_language_model_config("default_chat_model")
#     is_azure_client = default_llm_settings.type == LLMType.AzureOpenAIChat
#     debug_llm_key = default_llm_settings.api_key or ""
#     llm_debug_info = {
#         **default_llm_settings.model_dump(),
#         "api_key": f"REDACTED,len={len(debug_llm_key)}",
#     }
#     audience = (
#         default_llm_settings.audience
#         if default_llm_settings.audience
#         else "https://cognitiveservices.azure.com/.default"
#     )
#     print(f"creating llm client with {llm_debug_info}")  # noqa T201
#     return ChatOpenAI(
#         api_key=default_llm_settings.api_key,
#         azure_ad_token_provider=(
#             get_bearer_token_provider(DefaultAzureCredential(), audience)
#             if is_azure_client and not default_llm_settings.api_key
#             else None
#         ),
#         api_base=default_llm_settings.api_base,
#         organization=default_llm_settings.organization,
#         model=default_llm_settings.model,
#         api_type=OpenaiApiType.AzureOpenAI if is_azure_client else OpenaiApiType.OpenAI,
#         deployment_name=default_llm_settings.deployment_name,
#         api_version=default_llm_settings.api_version,
#         max_retries=default_llm_settings.max_retries,
#         request_timeout=default_llm_settings.request_timeout,
#     )


def get_claude_client(
    api_key=None,
    model="claude-3-5-sonnet-20241022",
    max_tokens=4096,
    temperature=0.0
) -> ClaudeClient:
    """Get the Claude LLM client.

    Args:
        api_key: Anthropic API key. If None, will try to get from environment.
        model: Claude model to use.
        max_tokens: Maximum tokens to generate.
        temperature: Sampling temperature.

    Returns:
        ClaudeClient: Initialized Claude client.
    """
    # Get API key from centralized management
    api_key = get_anthropic_api_key(provided_key=api_key)

    return ClaudeClient(
        api_key=api_key,
        model=model,
        max_tokens=max_tokens,
        temperature=temperature,
    )


def get_openrouter_client(
    api_key=None,
    # model="google/gemini-2.5-pro-exp-03-25:free",
    model="google/gemini-2.5-pro-preview",
    max_tokens=60096,
    temperature=0.0,
    base_url="https://openrouter.ai/api/v1",
    request_timeout=None
) -> OpenRouterClient:
    """Get the OpenRouter LLM client.

    Args:
        api_key: OpenRouter API key. If None, will try to get from environment.
        model: Model to use (e.g., "google/gemini-2.5-pro-exp-03-25:free").
        max_tokens: Maximum tokens to generate.
        temperature: Sampling temperature.
        base_url: Base URL for the OpenRouter API.
        request_timeout: Timeout for API requests in seconds.

    Returns:
        OpenRouterClient: Initialized OpenRouter client.
    """
    # API key will be handled by the OpenRouterClient using the centralized API key management
    print(f"Using OpenRouter Client with model: {model}")
    return OpenRouterClient(
        api_key=api_key,  # Will be handled by the client
        model=model,
        max_tokens=max_tokens,
        temperature=temperature,
        base_url=base_url,
        request_timeout=request_timeout,
    )
