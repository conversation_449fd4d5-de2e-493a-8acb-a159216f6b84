# type: ignore
"""Anthropic Claude client implementation."""

import os
import logging
import json
from typing import Any, Dict, List, Optional, Union, Iterable

from anthropic import Anthropic

from bracket_core.llm.api_keys import get_anthropic_api_key

log = logging.getLogger(__name__)

class ClaudeClient:
    """Client for Anthropic's Claude API."""

    def __init__(
        self,
        api_key: Optional[str] = None,
        model: str = "claude-3-5-haiku-latest", #claude-3-7-sonnet-latest
        max_tokens: int = 4096,
        temperature: float = 0.0,
        max_retries: int = 3,
        request_timeout: Optional[float] = None,
    ):
        """Initialize the Claude client.

        Args:
            api_key: Anthropic API key. If None, will try to get from environment.
            model: Claude model to use.
            max_tokens: Maximum tokens to generate.
            temperature: Sampling temperature.
            max_retries: Maximum number of retries for API calls.
            request_timeout: Timeout for API requests in seconds.
        """
        # Get API key from centralized management
        self.api_key = get_anthropic_api_key(provided_key=api_key)

        self.model = model
        self.max_tokens = max_tokens
        self.temperature = temperature
        self.max_retries = max_retries
        self.request_timeout = request_timeout

        self.client = Anthropic(
            api_key=self.api_key,
            max_retries=max_retries,
            timeout=request_timeout,
        )

    async def generate(
        self,
        prompt: str,
        system_prompt: Optional[Union[str, List[Dict[str, Any]]]] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        stream: bool = False,
        **kwargs: Any,
    ) -> str:
        """Generate text using Claude.

        Args:
            prompt: The user prompt to send to Claude.
            system_prompt: Optional system prompt to set context. Can be a string or a list of dicts for caching.
            max_tokens: Maximum tokens to generate, overrides instance default.
            temperature: Sampling temperature, overrides instance default.
            stream: Whether to stream the response.
            **kwargs: Additional parameters to pass to the API.

        Returns:
            Generated text response.
        """
        try:
            messages = [{"role": "user", "content": prompt}]

            # Handle system prompt - can be string or structured format with caching
            system = None
            if system_prompt:
                if isinstance(system_prompt, str):
                    system = system_prompt
                else:
                    # It's already in the structured format for caching
                    system = system_prompt

            response = self.client.messages.create(
                model=self.model,
                messages=messages,
                system=system,
                max_tokens=max_tokens or self.max_tokens,
                temperature=temperature or self.temperature,
                stream=stream,
                **kwargs
            )# type: ignore

            print(response.usage.model_dump_json())

            if stream:
                full_response = ""
                for chunk in response:
                    if chunk.delta.text:
                        full_response += chunk.delta.text
                return full_response
            else:
                return response.content[0].text

        except Exception as e:
            log.exception(f"Error generating text with Claude: {e}")
            raise

    async def generate_with_json_mode(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        json_schema: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> Dict[str, Any]:
        """Generate structured JSON response using Claude.

        Args:
            prompt: The user prompt to send to Claude.
            system_prompt: Optional system prompt to set context.
            json_schema: Optional JSON schema to validate response against.
            **kwargs: Additional parameters to pass to the API.

        Returns:
            Parsed JSON response.
        """
        try:
            messages = [{"role": "user", "content": prompt}]

            # Set up response format for JSON
            response_format = {"type": "json"}
            if json_schema:
                response_format["schema"] = json_schema

            response = self.client.messages.create(
                model=self.model,
                messages=messages,
                system=system_prompt,
                max_tokens=kwargs.pop("max_tokens", self.max_tokens),
                temperature=kwargs.pop("temperature", self.temperature),
                response_format=response_format,
                **kwargs
            )# type: ignore

            return json.loads(response.content[0].text)

        except Exception as e:
            log.exception(f"Error generating JSON with Claude: {e}")
            raise

    async def generate_with_tools(
        self,
        prompt: str,
        tools: List[Dict[str, Any]],
        system_prompt: Optional[str] = None,
        tool_choice: Optional[str] = None,
        **kwargs: Any,
    ) -> Dict[str, Any]:
        """Generate response with tool use capability.

        Args:
            prompt: The user prompt to send to Claude.
            tools: List of tool definitions with name, description, and input_schema.
            system_prompt: Optional system prompt to set context.
            tool_choice: How the model should use tools (specific tool, any, or auto).
            **kwargs: Additional parameters to pass to the API.

        Returns:
            Response with potential tool_use blocks.
        """
        try:
            messages = [{"role": "user", "content": prompt}]

            response = self.client.messages.create(
                model=self.model,
                messages=messages,
                system=system_prompt,
                max_tokens=kwargs.pop("max_tokens", self.max_tokens),
                temperature=kwargs.pop("temperature", self.temperature),
                tools=tools,
                tool_choice=tool_choice,
                **kwargs
            )# type: ignore

            # Return the full response object to allow access to tool_use blocks
            return response

        except Exception as e:
            log.exception(f"Error generating response with tools: {e}")
            raise

    async def continue_tool_conversation(
        self,
        messages: List[Dict[str, Any]],
        tool_results: List[Dict[str, Any]],
        system_prompt: Optional[str] = None,
        **kwargs: Any,
    ) -> Dict[str, Any]:
        """Continue a conversation after tool use by providing tool results.

        Args:
            messages: Previous conversation messages.
            tool_results: Results from tool execution with tool_use_id and content.
            system_prompt: Optional system prompt to set context.
            **kwargs: Additional parameters to pass to the API.

        Returns:
            Next response in the conversation.
        """
        try:
            # Create a new user message with tool results
            user_message = {
                "role": "user",
                "content": tool_results
            }

            # Add the new message to the conversation
            updated_messages = messages + [user_message]

            response = self.client.messages.create(
                model=self.model,
                messages=updated_messages,
                system=system_prompt,
                max_tokens=kwargs.pop("max_tokens", self.max_tokens),
                temperature=kwargs.pop("temperature", self.temperature),
                **kwargs
            ) # type: ignore

            return response

        except Exception as e:
            log.exception(f"Error continuing tool conversation: {e}")
            raise
