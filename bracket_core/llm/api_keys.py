"""API key management for LLM services.

This module provides centralized access to API keys for various LLM services.
It uses python-dotenv to load environment variables from a .env file if available.
"""

import os
import logging
from typing import Optional

# Try to import dotenv, but don't fail if it's not installed
try:
    from dotenv import load_dotenv
    # Load environment variables from .env file if it exists
    load_dotenv()
    _DOTENV_LOADED = True
except ImportError:
    _DOTENV_LOADED = False
    logging.warning(
        "python-dotenv not installed. Environment variables will only be loaded from the system environment. "
        "To use .env files, install python-dotenv: pip install python-dotenv"
    )

# Default API keys for development only (not recommended for production)
_DEFAULT_OPENROUTER_API_KEY = "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"

def get_openai_api_key(provided_key: Optional[str] = None) -> str:
    """Get the OpenAI API key.
    
    Args:
        provided_key: API key provided by the caller. If None, will try to get from environment.
        
    Returns:
        OpenAI API key
        
    Raises:
        ValueError: If no API key is found
    """
    api_key = provided_key or os.environ.get("OPENAI_API_KEY")
    
    if not api_key:
        raise ValueError(
            "OpenAI API key not provided and not found in environment. "
            "Please set the OPENAI_API_KEY environment variable or provide it explicitly."
        )
    
    return api_key

def get_openrouter_api_key(provided_key: Optional[str] = None, allow_default: bool = True) -> str:
    """Get the OpenRouter API key.
    
    Args:
        provided_key: API key provided by the caller. If None, will try to get from environment.
        allow_default: Whether to allow using the default development API key if no key is provided.
        
    Returns:
        OpenRouter API key
        
    Raises:
        ValueError: If no API key is found and allow_default is False
    """
    api_key = provided_key or os.environ.get("OPENROUTER_API_KEY")
    
    if not api_key and allow_default:
        logging.warning(
            "Warning: Using default OpenRouter API key. For production, please provide your own API key "
            "by setting the OPENROUTER_API_KEY environment variable."
        )
        return _DEFAULT_OPENROUTER_API_KEY
    
    if not api_key:
        raise ValueError(
            "OpenRouter API key not provided and not found in environment. "
            "Please set the OPENROUTER_API_KEY environment variable or provide it explicitly."
        )
    
    return api_key

def get_anthropic_api_key(provided_key: Optional[str] = None) -> str:
    """Get the Anthropic API key.
    
    Args:
        provided_key: API key provided by the caller. If None, will try to get from environment.
        
    Returns:
        Anthropic API key
        
    Raises:
        ValueError: If no API key is found
    """
    api_key = provided_key or os.environ.get("ANTHROPIC_API_KEY")
    
    if not api_key:
        raise ValueError(
            "Anthropic API key not provided and not found in environment. "
            "Please set the ANTHROPIC_API_KEY environment variable or provide it explicitly."
        )
    
    return api_key

def get_api_key(service: str, provided_key: Optional[str] = None) -> str:
    """Get the API key for the specified service.
    
    Args:
        service: Service name (openai, openrouter, anthropic)
        provided_key: API key provided by the caller. If None, will try to get from environment.
        
    Returns:
        API key for the specified service
        
    Raises:
        ValueError: If the service is not supported or no API key is found
    """
    service = service.lower()
    
    if service == "openai":
        return get_openai_api_key(provided_key)
    elif service == "openrouter":
        return get_openrouter_api_key(provided_key)
    elif service == "anthropic":
        return get_anthropic_api_key(provided_key)
    else:
        raise ValueError(f"Unsupported service: {service}")
