"""
Hierarchical Domain-to-File Mapper for Codebase

This module provides functionality to:
1. Read domain analysis YAML to get the hierarchical structure of domains
2. Read file-driven call graph YAML to get file information
3. Use an LLM to map files to domains at all levels of the hierarchy
4. Save the hierarchical domain-to-file mappings as YAML

It can be used as:
1. A standalone script to process domain analysis and file call graph YAMLs
2. A module that can be integrated into the repository analysis flow
"""

import os
import yaml
import json
import logging
import argparse
import asyncio
import aiohttp
import time
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, field
from collections import defaultdict
from functools import partial

# Import the original DomainFileMapper for compatibility
from bracket_core.domain_file_mapper import DomainFileMapper, DomainFileMapperResult, StatusTracker, APIRequest
# Import LLM clients and API key management
from bracket_core.llm.get_client import get_openrouter_client
from bracket_core.llm.api_keys import get_openai_api_key, get_openrouter_api_key

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class HierarchicalDomainFileMapperResult(DomainFileMapperResult):
    """Result of mapping files to domains hierarchically."""
    hierarchical_mappings: Dict[str, Dict[str, List[str]]] = field(default_factory=dict)

class HierarchicalDomainFileMapper(DomainFileMapper):
    """
    Maps files to domains at all levels of the hierarchy.

    This class reads domain analysis YAML and file-driven call graph YAML,
    and uses an LLM to map files to domains at all levels of the hierarchy.
    """

    def __init__(
        self,
        domain_analysis_yaml_path: str,
        file_call_graph_yaml_path: str,
        output_path: str,
        api_key: Optional[str] = None,
        model: str = "gpt-4o-mini",
        max_requests_per_minute: float = 2500,  # Increased for better throughput
        max_tokens_per_minute: float = 20000000,  # Increased for better throughput
        temperature: float = 0.1,
        batch_size: int = 50,  # Increased for better throughput
        use_openrouter: bool = False,
        openrouter_base_url: str = "https://openrouter.ai/api/v1",
        go_gemini: bool = False,  # Default to False as Gemini rate limits are too restrictive
        max_concurrent_tasks: int = 15,  # Increased for better throughput
        checkpoint_interval_seconds: int = 180,  # Save state every 5 minutes by default
    ):
        """
        Initialize the hierarchical domain file mapper.

        Args:
            domain_analysis_yaml_path: Path to the domain analysis YAML file
            file_call_graph_yaml_path: Path to the file-driven call graph YAML file
            output_path: Path to save the hierarchical domain-to-file mappings YAML
            api_key: API key (OpenAI or OpenRouter depending on use_openrouter)
            model: Model to use (OpenAI or OpenRouter model ID)
            max_requests_per_minute: Rate limit for API requests
            max_tokens_per_minute: Token rate limit for API
            temperature: Temperature setting for the model (lower = more deterministic)
            batch_size: Number of files to process in each batch
            use_openrouter: Whether to use OpenRouter instead of OpenAI
            openrouter_base_url: Base URL for OpenRouter API
            max_concurrent_tasks: Maximum number of concurrent API tasks
            checkpoint_interval_seconds: Time interval in seconds between automatic checkpoints
        """
        super().__init__(
            domain_analysis_yaml_path=domain_analysis_yaml_path,
            file_call_graph_yaml_path=file_call_graph_yaml_path,
            output_path=output_path,
            api_key=api_key,
            model=model,
            max_requests_per_minute=max_requests_per_minute,
            max_tokens_per_minute=max_tokens_per_minute,
            temperature=temperature,
            use_openrouter=use_openrouter,
            openrouter_base_url=openrouter_base_url,
        )
        self.batch_size = batch_size
        self.domain_hierarchy = {}  # Maps domain paths to their subdomain paths
        self.domain_path_to_name = {}  # Maps domain paths to domain names
        self.domain_name_to_path = {}  # Maps domain names to domain paths
        self.max_concurrent_tasks = max_concurrent_tasks
        self.checkpoint_interval_seconds = checkpoint_interval_seconds
        self.last_checkpoint_time = time.time()  # Initialize the last checkpoint time
        # Create a semaphore to limit concurrent API calls
        self.api_semaphore = asyncio.Semaphore(max_concurrent_tasks)

        # Initialize the appropriate client if using OpenRouter
        self.openrouter_client = None
        if self.use_openrouter:
            # For hierarchical domain mapping, always use Gemini model with large context window
            # openrouter_model = "google/gemini-2.5-pro-exp-03-25:free"
            openrouter_model = "google/gemini-2.5-pro-preview"
            self.openrouter_client = get_openrouter_client(
                # API key will be handled by the client
                model=openrouter_model,  # Override with Gemini model
                max_tokens=60096,  # Default value, can be adjusted
                temperature=self.temperature,
                base_url=self.openrouter_base_url
            )
            print(f"Hierarchical Domain File Mapper using OpenRouter with model: {openrouter_model}")
        self.domain_path_to_description = {}  # Maps domain paths to domain descriptions
        self.hierarchical_mappings = defaultdict(lambda: defaultdict(list))  # Maps hierarchy levels to domain paths to file lists
        self.go_gemini = go_gemini  # Whether to use Gemini for top-level domains

    def extract_domain_hierarchy(self, domain_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract the complete domain hierarchy from domain analysis data.

        Args:
            domain_data: Dictionary containing domain analysis data

        Returns:
            Dictionary representing the domain hierarchy
        """
        logger.info("Extracting complete domain hierarchy")

        # Initialize hierarchy structures
        self.domain_hierarchy = {}
        self.domain_path_to_name = {}
        self.domain_name_to_path = {}
        self.domain_path_to_description = {}

        # Process the hierarchy recursively
        def process_domain(domain: Dict[str, Any], parent_path: str = "") -> None:
            domain_name = domain.get('name', '')
            if not domain_name:
                return

            # Create the current domain path
            current_path = f"{parent_path}/{domain_name}" if parent_path else domain_name

            # Store domain information
            self.domain_path_to_name[current_path] = domain_name
            self.domain_name_to_path[domain_name] = current_path

            # Generate and store domain description
            description = self._generate_domain_description(domain)
            self.domain_path_to_description[current_path] = description

            # Process subdomains
            subareas = domain.get('subareas', [])
            subdomain_paths = []

            for subarea in subareas:
                subarea_name = subarea.get('name', '')
                if subarea_name:
                    subdomain_path = f"{current_path}/{subarea_name}"
                    subdomain_paths.append(subdomain_path)
                    process_domain(subarea, current_path)

            # Store subdomain paths
            self.domain_hierarchy[current_path] = subdomain_paths

        # Start processing from top-level domains
        areas = domain_data.get('areas', [])
        for area in areas:
            process_domain(area)

        # Log hierarchy statistics
        total_domains = len(self.domain_path_to_name)
        hierarchy_levels = set(path.count('/') + 1 for path in self.domain_path_to_name.keys())
        max_level = max(hierarchy_levels) if hierarchy_levels else 0

        logger.info(f"Extracted {total_domains} domains across {len(hierarchy_levels)} hierarchy levels (max depth: {max_level})")
        return self.domain_hierarchy

    def get_domains_at_level(self, level: int) -> List[str]:
        """
        Get all domain paths at a specific hierarchy level.

        Args:
            level: Hierarchy level (1-based, where 1 is top-level)

        Returns:
            List of domain paths at the specified level
        """
        return [path for path in self.domain_path_to_name.keys() if path.count('/') + 1 == level]

    def get_parent_domain_path(self, domain_path: str) -> Optional[str]:
        """
        Get the parent domain path for a given domain path.

        Args:
            domain_path: Domain path

        Returns:
            Parent domain path, or None if this is a top-level domain
        """
        if '/' not in domain_path:
            return None

        return '/'.join(domain_path.split('/')[:-1])

    def get_files_for_parent_domain(self, domain_path: str, level: int) -> List[str]:
        """
        Get the files mapped to the parent domain at the previous level.

        Args:
            domain_path: Domain path
            level: Current hierarchy level

        Returns:
            List of files mapped to the parent domain
        """
        if level <= 1:
            # Top-level domains have no parent
            return []

        parent_path = self.get_parent_domain_path(domain_path)
        if not parent_path:
            return []

        return self.hierarchical_mappings[level - 1].get(parent_path, [])

    async def map_files_to_domains_hierarchically(self) -> HierarchicalDomainFileMapperResult:
        """
        Map files to domains at all levels of the hierarchy using LLM.

        If go_gemini is True, uses OpenRouter with Gemini model for top-level domains (level 1)
        to handle larger context windows. This is useful for larger codebases.
        For deeper levels (level 2+), it uses the original model (typically gpt-4o-mini)
        since the search space is reduced.

        Returns:
            HierarchicalDomainFileMapperResult containing the hierarchical domain-to-file mappings
        """
        try:
            # Read domain and file graph YAMLs
            domain_data = self.read_domain_yaml()
            file_graph = self.read_file_call_graph_yaml()

            # Extract the complete domain hierarchy
            self.extract_domain_hierarchy(domain_data)

            # Get the maximum hierarchy level
            max_level = max(path.count('/') + 1 for path in self.domain_path_to_name.keys())

            # Save original model and OpenRouter settings
            original_model = self.model
            original_use_openrouter = self.use_openrouter

            # Process each level of the hierarchy
            for level in range(1, max_level + 1):
                logger.info(f"Processing hierarchy level {level}/{max_level}")

                # For level 1 (top-level), use Gemini if go_gemini is True
                if level == 1 and self.go_gemini:
                    logger.info("Using OpenRouter with Gemini model for top-level domain file allocation")
                    # self.use_openrouter = True
                    # self.model = "google/gemini-2.5-pro-preview"  # Use Gemini model with larger context window
                    # Make sure we're using the OpenRouter API key
                    # self.api_key = get_openrouter_api_key()
                    self.use_openrouter = False
                    self.model = "gpt-4o-mini"  # Use default model for deeper levels
                    # self.model = original_model
                    self.api_key = get_openai_api_key()

                elif level > 1:
                    # Restore original settings for deeper levels
                    logger.info(f"Switching back to original model {original_model} for level {level}")
                    self.model = original_model
                    self.api_key = get_openai_api_key()
                    self.use_openrouter = False  # Always use OpenAI for deeper levels when go_gemini is True

                # Get domains at this level
                domains_at_level = self.get_domains_at_level(level)
                logger.info(f"Found {len(domains_at_level)} domains at level {level}")

                # Prepare tasks for parallel processing
                tasks = []

                # For each domain at this level, prepare a task to map files to it
                for domain_path in domains_at_level:
                    # For level 1 (top-level), consider all files
                    # For deeper levels, only consider files mapped to the parent domain
                    files_to_consider = list(file_graph.keys()) if level == 1 else self.get_files_for_parent_domain(domain_path, level)

                    if not files_to_consider:
                        logger.info(f"No files to consider for domain: {domain_path}")
                        continue

                    logger.info(f"Preparing to map {len(files_to_consider)} files to domain: {domain_path}")

                    # Process files in batches to avoid token limits
                    for i in range(0, len(files_to_consider), self.batch_size):
                        batch_files = files_to_consider[i:i+self.batch_size]

                        # Get file information for this batch
                        batch_file_info = self._prepare_file_info(batch_files, file_graph)

                        # If this is a deeper level, we need to provide context about the parent domain
                        parent_context = ""
                        if level > 1:
                            parent_path = self.get_parent_domain_path(domain_path)
                            parent_name = self.domain_path_to_name.get(parent_path, "")
                            parent_desc = self.domain_path_to_description.get(parent_path, "")
                            parent_context = f"These files have been identified as belonging to the parent domain: {parent_name}\nParent domain description: {parent_desc}\n\n"

                        # Map files to this domain
                        domain_name = self.domain_path_to_name.get(domain_path, "")
                        domain_desc = self.domain_path_to_description.get(domain_path, "")

                        # Get subdomain information if available
                        subdomain_paths = self.domain_hierarchy.get(domain_path, [])
                        subdomain_info = ""
                        if subdomain_paths:
                            subdomain_names = [self.domain_path_to_name.get(path, "") for path in subdomain_paths]
                            subdomain_info = f"This domain has the following subdomains: {', '.join(subdomain_names)}\n"

                        # Create a task for this batch
                        task = self._process_batch(
                            domain_path=domain_path,
                            level=level,
                            domain_name=domain_name,
                            domain_description=domain_desc,
                            subdomain_info=subdomain_info,
                            parent_context=parent_context,
                            file_info=batch_file_info,
                            batch_index=i//self.batch_size + 1,
                            total_batches=(len(files_to_consider) + self.batch_size - 1)//self.batch_size,
                            original_model=original_model if self.go_gemini else None,
                            original_use_openrouter=original_use_openrouter if self.go_gemini else None
                        )
                        tasks.append(task)

                # Process all tasks for this level with controlled concurrency
                if tasks:
                    logger.info(f"Processing {len(tasks)} batches with controlled concurrency (max {self.max_concurrent_tasks} concurrent tasks) for level {level}")
                    # Use a semaphore to limit concurrent tasks

                    # Process all tasks with dynamic concurrency control
                    # Instead of processing in fixed chunks, use a more dynamic approach
                    logger.info(f"Using dynamic task processing with max {self.max_concurrent_tasks} concurrent tasks")

                    # Track all running tasks
                    all_running_tasks = set()
                    completed_tasks = 0
                    total_tasks = len(tasks)

                    # Start tasks without waiting for previous ones to complete
                    for task in tasks:
                        # Create task with semaphore and convert to Task object explicitly
                        task_coroutine = self._run_task_with_semaphore(task)
                        task_obj = asyncio.create_task(task_coroutine)
                        all_running_tasks.add(task_obj)

                        # Start more tasks without waiting, but periodically clean up completed ones
                        if len(all_running_tasks) >= self.max_concurrent_tasks:
                            # Remove completed tasks with a short timeout
                            done, pending = await asyncio.wait(all_running_tasks, timeout=0.1, return_when=asyncio.FIRST_COMPLETED)

                            # Update tracking
                            completed_tasks += len(done)
                            if done:
                                logger.debug(f"Completed {completed_tasks}/{total_tasks} tasks, {len(pending)} still running")

                            # Update the set of running tasks
                            all_running_tasks = pending

                            # Add a tiny delay to avoid CPU spinning
                            await asyncio.sleep(0.01)

                    # Wait for all remaining tasks to complete
                    if all_running_tasks:
                        logger.info(f"Waiting for {len(all_running_tasks)} remaining tasks to complete")
                        done, _ = await asyncio.wait(all_running_tasks)
                        completed_tasks += len(done)
                        logger.info(f"All {completed_tasks}/{total_tasks} tasks completed for level {level}")

                # Save the current state after completing each hierarchy level
                logger.info(f"Completed processing hierarchy level {level}/{max_level}, saving current state to disk")
                self._save_current_mappings_to_disk()

            # Convert hierarchical mappings to the format expected by the output
            # For backward compatibility, also create a flat mapping for top-level domains
            flat_mappings = {}
            for domain_path in self.get_domains_at_level(1):
                domain_name = self.domain_path_to_name.get(domain_path, "")
                flat_mappings[domain_name] = self.hierarchical_mappings[1].get(domain_path, [])

            # Prepare the hierarchical output structure
            hierarchical_output = {}
            for level, level_mappings in self.hierarchical_mappings.items():
                hierarchical_output[f"level_{level}"] = {
                    self.domain_path_to_name.get(path, path): files
                    for path, files in level_mappings.items()
                }

            # Save the mappings to YAML
            output_data = {
                "flat_mappings": flat_mappings,
                "hierarchical_mappings": hierarchical_output,
                "domain_paths": {
                    name: path for name, path in self.domain_name_to_path.items()
                }
            }

            with open(self.output_path, 'w') as f:
                yaml.dump(output_data, f, default_flow_style=False)

            logger.info(f"Hierarchical domain-to-file mappings saved to: {self.output_path}")

            return HierarchicalDomainFileMapperResult(
                success=True,
                domain_file_mappings=flat_mappings,
                hierarchical_mappings=hierarchical_output,
                output_path=self.output_path
            )

        except Exception as e:
            logger.error(f"Error mapping files to domains hierarchically: {e}")
            import traceback
            logger.error(traceback.format_exc())

            return HierarchicalDomainFileMapperResult(
                success=False,
                error_message=f"Failed to map files to domains hierarchically: {str(e)}"
            )

    def _create_adaptive_batches(self, file_info: List[Dict[str, Any]], initial_batch_size: int, domain_name: str) -> List[List[Dict[str, Any]]]:
        """
        Create batches of files with adaptive sizing to handle very large codebases.

        This method starts with the initial batch size and then splits batches if they're too large.
        It uses a recursive approach to ensure that even very large codebases can be processed.

        Args:
            file_info: List of file information dictionaries to batch
            initial_batch_size: Initial size for batches
            domain_name: Name of the domain (for logging)

        Returns:
            List of batches, where each batch is a list of file information dictionaries
        """
        # Create initial batches based on the initial batch size
        initial_batches = [file_info[i:i + initial_batch_size] for i in range(0, len(file_info), initial_batch_size)]
        logger.info(f"Initial batching created {len(initial_batches)} batches for domain: {domain_name}")

        # If we have a small number of files or only one batch, return as is
        if len(file_info) <= initial_batch_size or len(initial_batches) == 1:
            return initial_batches

        # For larger codebases, use adaptive batch sizing
        # The strategy is to start with larger batches and then split them if needed
        # This is especially important for level 2+ domains where we might have thousands of files

        # Define a minimum batch size to prevent excessive splitting
        min_batch_size = 50

        # If we have a very large number of files, use a more aggressive splitting strategy
        if len(file_info) > 5000:
            # For very large codebases, start with smaller batches
            logger.info(f"Large codebase detected with {len(file_info)} files for domain: {domain_name}. Using aggressive batch splitting.")
            # Create smaller batches for very large codebases
            return [file_info[i:i + min_batch_size] for i in range(0, len(file_info), min_batch_size)]

        # For medium-sized codebases, use a moderate splitting strategy
        if len(file_info) > 1000:
            # Use half the initial batch size for medium-sized codebases
            medium_batch_size = max(initial_batch_size // 2, min_batch_size)
            logger.info(f"Medium-sized codebase detected with {len(file_info)} files for domain: {domain_name}. Using batch size of {medium_batch_size}.")
            return [file_info[i:i + medium_batch_size] for i in range(0, len(file_info), medium_batch_size)]

        # For smaller codebases, use the initial batch size
        return initial_batches

    async def _run_task_with_semaphore(self, task):
        """
        Run a task with semaphore to limit concurrency.
        Adds a timeout to prevent extremely slow tasks from blocking progress.

        Args:
            task: The task to run

        Returns:
            The result of the task or an exception if the task fails or times out
        """
        try:
            async with self.api_semaphore:
                # Add timeout to prevent extremely slow calls from blocking
                # 180 seconds (3 minutes) should be enough for most API calls
                return await asyncio.wait_for(task, timeout=180)
        except asyncio.TimeoutError:
            logger.warning(f"Task timed out after 180 seconds")
            return Exception("Task timed out after 180 seconds")
        except Exception as e:
            logger.error(f"Task failed with error: {e}")
            # Return the error to prevent it from blocking other tasks
            return e

    def _get_domain_level(self, domain_name: str) -> int:
        """
        Get the hierarchy level of a domain by its name.

        Args:
            domain_name: Name of the domain

        Returns:
            Level in the hierarchy (1 for top-level, 2 for second level, etc.)
        """
        # Find the domain path for this domain name
        domain_path = self.domain_name_to_path.get(domain_name, "")
        if not domain_path:
            return 0

        # Count the number of slashes to determine level
        return domain_path.count('/') + 1

    def _save_current_mappings_to_disk(self) -> None:
        """
        Save the current state of hierarchical mappings to disk.
        Uses a temporary file to ensure atomic writes.
        """
        try:
            # Convert hierarchical mappings to the format expected by the output
            # For backward compatibility, also create a flat mapping for top-level domains
            flat_mappings = {}
            for domain_path in self.get_domains_at_level(1):
                domain_name = self.domain_path_to_name.get(domain_path, "")
                flat_mappings[domain_name] = self.hierarchical_mappings[1].get(domain_path, [])

            # Prepare the hierarchical output structure
            hierarchical_output = {}
            for level, level_mappings in self.hierarchical_mappings.items():
                hierarchical_output[f"level_{level}"] = {
                    self.domain_path_to_name.get(path, path): files
                    for path, files in level_mappings.items()
                }

            # Prepare the output data
            output_data = {
                "flat_mappings": flat_mappings,
                "hierarchical_mappings": hierarchical_output,
                "domain_paths": {
                    name: path for name, path in self.domain_name_to_path.items()
                }
            }

            # Write to a temporary file first to ensure atomic write
            temp_output_path = f"{self.output_path}.tmp"
            with open(temp_output_path, 'w') as f:
                yaml.dump(output_data, f, default_flow_style=False)

            # Rename the temporary file to the actual output file (atomic operation)
            import os
            os.replace(temp_output_path, self.output_path)

            logger.info(f"Saved current state of hierarchical mappings to: {self.output_path}")
        except Exception as e:
            logger.error(f"Error saving current mappings to disk: {e}")
            import traceback
            logger.error(traceback.format_exc())

    async def _process_batch(
        self,
        domain_path: str,
        level: int,
        domain_name: str,
        domain_description: str,
        subdomain_info: str,
        parent_context: str,
        file_info: List[Dict[str, Any]],
        batch_index: int,
        total_batches: int,
        original_model: Optional[str] = None,
        original_use_openrouter: Optional[bool] = None
    ) -> None:
        """
        Process a batch of files for a specific domain using non-blocking parallel processing.

        Args:
            domain_path: Path of the domain
            level: Hierarchy level
            domain_name: Name of the domain
            domain_description: Description of the domain
            subdomain_info: Information about subdomains
            parent_context: Context about the parent domain
            file_info: List of dictionaries containing file information
            batch_index: Index of the current batch
            total_batches: Total number of batches
            original_model: Original model to restore after processing (for go_gemini)
            original_use_openrouter: Original use_openrouter setting to restore (for go_gemini)
        """
        try:
            # For level 1 domains with go_gemini, ensure we're using OpenAI for better stability
            if level == 1 and self.go_gemini:
                logger.info(f"Using OpenAI for level 1 domain: {domain_name}")
                self.use_openrouter = False
                self.model = "gpt-4o-mini"  # Use default model for all levels
                self.api_key = get_openai_api_key()  # Make sure we're using the OpenAI API key

            elif level > 1:
                # For level 2+ domains with go_gemini, ensure we're using OpenAI
                logger.info(f"Ensuring OpenAI is used for level {level} domain: {domain_name}")
                self.use_openrouter = False
                self.model = "gpt-4o-mini"  # Use default model for deeper levels
                self.api_key = get_openai_api_key()  # Make sure we're using the OpenAI API key

            logger.info(f"Processing batch {batch_index}/{total_batches} for domain: {domain_name} (level {level}) with non-blocking parallel processing")

            # Classify files for this domain using non-blocking parallel processing
            domain_files = await self._classify_files_to_specific_domain(
                domain_name=domain_name,
                domain_description=domain_description,
                subdomain_info=subdomain_info,
                parent_context=parent_context,
                file_info=file_info
            )

            # Add the mapped files to the hierarchical mappings
            self.hierarchical_mappings[level][domain_path].extend(domain_files)

            # Save the current state to disk if this is the last batch for this domain
            if batch_index == total_batches:
                logger.info(f"Completed all batches for domain: {domain_name}, saving current state to disk")
                self._save_current_mappings_to_disk()

            # Restore original settings if needed
            if level == 1 and self.go_gemini and original_model and original_use_openrouter is not None:
                logger.info(f"Restoring original model settings after processing level 1 domain: {domain_name}")
                self.model = original_model
                self.use_openrouter = original_use_openrouter

            logger.info(f"Completed batch {batch_index}/{total_batches} for domain: {domain_name}, found {len(domain_files)} files")
        except Exception as e:
            logger.error(f"Error processing batch {batch_index}/{total_batches} for domain {domain_name}: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # Try to save the current state even if there was an error
            try:
                self._save_current_mappings_to_disk()
            except Exception as save_error:
                logger.error(f"Failed to save mappings after error: {save_error}")

            return HierarchicalDomainFileMapperResult(
                success=False,
                error_message=f"Error processing batch {batch_index}/{total_batches} for domain {domain_name}: {e}",
                domain_file_mappings={},
                output_path=self.output_path
            ) # type: ignore

        # except Exception as e:
        #     logger.error(f"Error mapping files to domains hierarchically: {e}")
        #     import traceback
        #     logger.error(traceback.format_exc())

        #     return HierarchicalDomainFileMapperResult(
        #         success=False,
        #         error_message=f"Failed to map files to domains hierarchically: {str(e)}"
        #     )

    def _prepare_file_info(self, file_paths: List[str], file_graph: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Prepare file information for the LLM.

        Args:
            file_paths: List of file paths to prepare information for
            file_graph: Dictionary containing file graph data

        Returns:
            List of dictionaries containing file information
        """
        file_info = []
        skipped_count = 0

        for file_path in file_paths:
            if file_path not in file_graph:
                continue

            file_data = file_graph[file_path]
            functions = file_data.get('functions', [])

            # Skip files with empty functions
            if not functions:
                skipped_count += 1
                logger.debug(f"Skipping file with empty functions: {file_path}")
                continue

            function_summaries = []

            # for func in functions[:5]:  # Limit to first 5 functions to reduce token usage
            for func in functions:  # Process all functions
                name = func.get('name', '')
                description = func.get('description', '')
                function_summaries.append(f"{name}: {description}")

            file_summary = {
                'file_path': file_path,
                'functions': function_summaries
            }

            # Add external calls if available
            if functions and 'calls' in functions[0]:
                file_summary['external_calls'] = functions[0].get('calls', [])[:5]  # Limit to first 5 external calls

            file_info.append(file_summary)

        if skipped_count > 0:
            logger.info(f"Skipped {skipped_count} files with empty functions out of {len(file_paths)} total files")

        return file_info

    async def _classify_files_to_specific_domain(
        self,
        domain_name: str,
        domain_description: str,
        subdomain_info: str,
        parent_context: str,
        file_info: List[Dict[str, Any]]
    ) -> List[str]:
        """
        Classify files into a specific domain using LLM with non-blocking parallel processing.

        Args:
            domain_name: Name of the domain
            domain_description: Description of the domain
            subdomain_info: Information about subdomains
            parent_context: Context about the parent domain
            file_info: List of dictionaries containing file information

        Returns:
            List of file paths that belong to the domain
        """
        if not file_info:
            return []

        # Get the domain level to determine processing approach
        current_level = self._get_domain_level(domain_name)
        logger.info(f"Classifying {len(file_info)} files for domain: {domain_name} (level {current_level}) with non-blocking parallel processing")

        # Use a consistent, smaller batch size for all levels to improve stability
        initial_batch_size = self.batch_size
        logger.info(f"Using standard initial batch size ({initial_batch_size}) for domain: {domain_name}")

        seconds_to_sleep_each_loop = 0.001  # 1 ms limits max throughput to 1,000 requests per second

        # Initialize result
        domain_files = []

        # Initialize trackers
        queue_of_requests_to_retry = asyncio.Queue()
        status_tracker = self.status_tracker  # Use the instance's status tracker

        # Create initial batches of files with adaptive sizing
        file_batches = self._create_adaptive_batches(file_info, initial_batch_size, domain_name)
        logger.info(f"Created {len(file_batches)} batches of files (adaptive batching) for domain: {domain_name}")

        # Create API requests for each batch
        api_requests = []
        for i, batch in enumerate(file_batches):
            # Create the API request for this batch
            request_json = {
                "model": self.model,
                "messages": [
                    {
                        "role": "system",
                        "content": """You are an expert code analyzer that maps source code files to their appropriate domains based on their content and purpose.

Your task is to analyze a list of files and determine which ones belong to a specific domain.

Guidelines:
1. Each file should either be included or excluded from the domain based on its relevance
2. Use the file path, function samples, and external calls to determine if a file belongs to the domain
3. Return your analysis as a valid JSON array of file paths that belong to the domain
4. Be selective - only include files that are clearly related to the domain
5. If a file could potentially belong to a subdomain, include it in the current domain as well

Example output format:
```json
[
  "path/to/file1.py",
  "path/to/file2.py"
]
```"""
                    },
                    {
                        "role": "user",
                        "content": f"""{parent_context}Please analyze the following files and determine which ones belong to the domain:

DOMAIN: {domain_name}
DESCRIPTION: {domain_description}
{subdomain_info}

FILES:
{json.dumps(batch, indent=2)}

Return your classification as a JSON array of file paths that belong to the domain.
Only include files that are clearly related to this specific domain."""
                    }
                ],
                "temperature": self.temperature
            }

            # Create an APIRequest object for domain-specific classification
            api_request = APIRequest(
                task_id=i,
                batch_files=batch,
                request_json=request_json,
                attempts_left=3  # Allow up to 3 attempts per request
            )
            api_requests.append(api_request)

        # Initialize counters for rate limiting
        available_request_capacity = self.max_requests_per_minute
        available_token_capacity = self.max_tokens_per_minute
        last_update_time = time.time()
        next_request_index = 0
        next_request = None

        # Set up API endpoint based on whether we're using OpenRouter or OpenAI
        # Only use OpenRouter for level 1 domains when go_gemini is True
        if self.use_openrouter and (current_level == 1 or not self.go_gemini):
            # For hierarchical domain file mapping, use the OpenRouter API directly
            request_url = f"{self.openrouter_base_url}/chat/completions"
            request_header = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}",
                # Using 'Referer' header for OpenRouter API (required by their API)
                "Referer": "https://github.com/bracket-ai/bracket",
                "X-Title": "Bracket Code"
            }

            # If go_gemini is True and this is level 1, we're using Gemini model for top-level domains
            if self.go_gemini and current_level == 1:
                logger.info(f"Using OpenRouter with model: {self.model} for level {current_level} domain file allocation")
            else:
                logger.info(f"Using OpenRouter with model: {self.model} for domain: {domain_name}")
        else:
            # For level 2+ domains when go_gemini is True, always use OpenAI regardless of self.use_openrouter
            request_url = "https://api.openai.com/v1/chat/completions"
            request_header = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {get_openai_api_key()}"
            }
            logger.info(f"Using OpenAI with model: {self.model} for domain: {domain_name}")

        # Process requests with rate limiting
        status_tracker.num_tasks_started += len(api_requests)
        status_tracker.num_tasks_in_progress += len(api_requests)

        # Create a dictionary to store results
        batch_results = {}

        # Process requests with rate limiting and improved concurrency
        async with aiohttp.ClientSession() as session:
            # Track active API tasks
            active_api_tasks = set()
            max_active_tasks = min(self.max_concurrent_tasks * 2, 100)  # Allow more concurrent API tasks

            # Process all requests with improved concurrency
            while status_tracker.num_tasks_in_progress > 0 or next_request_index < len(api_requests) or not queue_of_requests_to_retry.empty():
                # Clean up completed tasks periodically
                if active_api_tasks:
                    # active_api_tasks should already contain Task objects, not coroutines
                    done, pending = await asyncio.wait(active_api_tasks, timeout=0.01, return_when=asyncio.FIRST_COMPLETED)
                    active_api_tasks = pending
                    if done:
                        logger.debug(f"Completed {len(done)} API tasks, {len(pending)} still running")

                # Get next request (if one is not already waiting for capacity)
                if next_request is None and len(active_api_tasks) < max_active_tasks:
                    if not queue_of_requests_to_retry.empty():
                        next_request = await queue_of_requests_to_retry.get()
                        next_request.attempts_left -= 1
                        logger.debug(f"Retrying request {next_request.task_id} for batch of {len(next_request.batch_files)} files for domain: {domain_name}")
                    elif next_request_index < len(api_requests):
                        next_request = api_requests[next_request_index]
                        next_request_index += 1
                        logger.debug(f"Processing request {next_request.task_id} for batch of {len(next_request.batch_files)} files for domain: {domain_name}")

                # Update rate limit capacity
                current_time = time.time()
                time_elapsed = current_time - last_update_time
                available_request_capacity = min(
                    available_request_capacity + (self.max_requests_per_minute * time_elapsed / 60),
                    self.max_requests_per_minute
                )
                available_token_capacity = min(
                    available_token_capacity + (self.max_tokens_per_minute * time_elapsed / 60),
                    self.max_tokens_per_minute
                )
                last_update_time = current_time

                # If enough capacity available and we have a request, call API
                if next_request and available_request_capacity >= 1 and len(active_api_tasks) < max_active_tasks:
                    # Estimate token consumption (simplified)
                    estimated_tokens = 1000  # Simplified estimate

                    if available_token_capacity >= estimated_tokens:
                        # Update counters
                        available_request_capacity -= 1
                        available_token_capacity -= estimated_tokens

                        # Create API task and add to active tasks set
                        api_task = asyncio.create_task(
                            self._process_domain_specific_request(
                                request=next_request,
                                session=session,
                                request_url=request_url,
                                request_header=request_header,
                                retry_queue=queue_of_requests_to_retry,
                                status_tracker=status_tracker,
                                results=batch_results,
                                domain_name=domain_name
                            )
                        )
                        active_api_tasks.add(api_task)
                        next_request = None  # Reset next_request to get a new one

                # Check if it's time for a checkpoint based on the elapsed time
                current_time = time.time()
                if current_time - self.last_checkpoint_time > self.checkpoint_interval_seconds:
                    logger.info(f"Time-based checkpoint triggered after {int(current_time - self.last_checkpoint_time)} seconds")
                    self._save_current_mappings_to_disk()
                    self.last_checkpoint_time = current_time

                # Sleep a bit to avoid busy-waiting
                await asyncio.sleep(seconds_to_sleep_each_loop)

            # Wait for any remaining active tasks to complete
            if active_api_tasks:
                logger.info(f"Waiting for {len(active_api_tasks)} remaining API tasks to complete for domain: {domain_name}")
                # active_api_tasks should already contain Task objects
                await asyncio.wait(active_api_tasks)
                logger.info(f"All remaining API tasks completed for domain: {domain_name}")

        # After finishing, log final status
        logger.info(f"Parallel processing complete for domain: {domain_name}. Processed {len(file_batches)} batches of files.")

        # Combine all batch results
        for _, files in batch_results.items():
            domain_files.extend(files)

        logger.info(f"Successfully classified {len(domain_files)} files for domain: {domain_name}")
        return domain_files

    async def _process_domain_specific_request(
        self,
        request: APIRequest,
        session: aiohttp.ClientSession,  # pylint: disable=unused-argument # Kept for compatibility
        request_url: str,
        request_header: dict,
        retry_queue: asyncio.Queue,
        status_tracker: StatusTracker,
        results: Dict[int, List[str]],
        domain_name: str
    ) -> None:
        """Process a domain-specific API request with improved error handling and retry logic."""
        try:
            # Get the domain level to determine processing approach
            current_level = self._get_domain_level(domain_name)

            # Use OpenRouter client if available and enabled, and this is a level 1 domain with go_gemini
            # For level 2+ domains when go_gemini is True, never use OpenRouter
            if self.use_openrouter and ((current_level == 1 and self.go_gemini) or not self.go_gemini):
                try:
                    # Make sure we have an OpenRouter client
                    if not self.openrouter_client:
                        # Initialize the OpenRouter client if it doesn't exist
                        openrouter_model = "google/gemini-2.5-pro-preview"
                        self.openrouter_client = get_openrouter_client(
                            model=openrouter_model,
                            max_tokens=60096,
                            temperature=self.temperature,
                            base_url=self.openrouter_base_url
                        )
                        logger.info(f"Initialized OpenRouter client with model: {openrouter_model} for level 1 domain: {domain_name}")

                    # Extract messages from request JSON
                    messages = request.request_json.get("messages", [])
                    system_prompt = None
                    user_prompt = None

                    # Extract system and user prompts from messages
                    for message in messages:
                        if message["role"] == "system":
                            system_prompt = message["content"]
                        elif message["role"] == "user":
                            user_prompt = message["content"]

                    if not user_prompt:
                        raise ValueError("No user prompt found in request")

                    # Call the OpenRouter client
                    logger.info(f"Using OpenRouter client for level 1 domain: {domain_name} with model: {self.model}")
                    content = await self.openrouter_client.generate(
                        prompt=user_prompt,
                        system_prompt=system_prompt,
                        temperature=self.temperature
                    )

                    # Parse the response to get the domain-file mappings
                    try:
                        # Find JSON content between triple backticks
                        json_content = content
                        if "```json" in content:
                            json_content = content.split("```json")[1].split("```")[0].strip()
                        elif "```" in content:
                            json_content = content.split("```")[1].split("```")[0].strip()

                        # Enhanced error handling for JSON parsing
                        try:
                            # Try to parse the JSON content
                            domain_files = json.loads(json_content)
                        except json.JSONDecodeError as json_err:
                            # Handle specific JSON parsing errors
                            logger.error(f"JSON parsing error for batch {request.task_id}: {json_err}")

                            # If it's an unterminated string error, try to fix it
                            if "Unterminated string" in str(json_err):
                                logger.info(f"Attempting to fix unterminated string in batch {request.task_id}")
                                # Try to extract just the array part
                                if json_content.strip().startswith('[') and ']' in json_content:
                                    fixed_content = json_content[:json_content.rindex(']')+1]
                                    try:
                                        domain_files = json.loads(fixed_content)
                                        logger.info(f"Successfully fixed JSON for batch {request.task_id}")
                                    except Exception as fix_err:
                                        # If fixing failed, try a more aggressive approach - extract file paths
                                        logger.warning(f"First fix attempt failed: {fix_err}. Trying more aggressive fix.")
                                        # Extract anything that looks like a file path
                                        import re
                                        file_paths = re.findall(r'"([^"]+\.\w+)"', json_content)
                                        domain_files = list(set(file_paths))  # Remove duplicates
                                        if domain_files:
                                            logger.info(f"Extracted {len(domain_files)} file paths using regex")
                                        else:
                                            # If we couldn't extract anything, raise the original error
                                            raise json_err
                                else:
                                    # If we can't easily fix it, try to extract file paths
                                    import re
                                    file_paths = re.findall(r'"([^"]+\.\w+)"', json_content)
                                    domain_files = list(set(file_paths))  # Remove duplicates
                                    if domain_files:
                                        logger.info(f"Extracted {len(domain_files)} file paths using regex")
                                    else:
                                        # If we couldn't extract anything, raise the original error
                                        raise json_err
                            else:
                                # For other JSON errors, re-raise
                                raise

                        # Update the results
                        results[request.task_id] = domain_files

                        logger.info(f"Successfully processed batch {request.task_id} with {len(domain_files)} files for domain: {domain_name}")
                        status_tracker.num_tasks_succeeded += 1
                        status_tracker.num_tasks_in_progress -= 1
                    except Exception as e:
                        logger.error(f"Error parsing OpenRouter response for batch {request.task_id} for domain {domain_name}: {e}")
                        request.error = f"Error parsing OpenRouter response: {e}"

                        # If this is a batch size issue, try splitting the batch
                        if "Unterminated string" in str(e) and request.attempts_left > 0:
                            logger.warning(f"Detected possible batch size issue for batch {request.task_id}. Will retry with smaller batch.")
                            # Split the batch in half for the retry
                            if len(request.batch_files) > 1:
                                mid = len(request.batch_files) // 2
                                batch1 = request.batch_files[:mid]
                                batch2 = request.batch_files[mid:]

                                # Create two new requests with the split batches
                                # Use the original request's JSON as a template but with smaller batches
                                request_json1 = request.request_json.copy()
                                request_json1['messages'][-1]['content'] = request_json1['messages'][-1]['content'].replace(
                                    json.dumps(request.batch_files, indent=2),
                                    json.dumps(batch1, indent=2)
                                )

                                request_json2 = request.request_json.copy()
                                request_json2['messages'][-1]['content'] = request_json2['messages'][-1]['content'].replace(
                                    json.dumps(request.batch_files, indent=2),
                                    json.dumps(batch2, indent=2)
                                )

                                # Create new task IDs as integers
                                new_task_id1 = request.task_id * 10 + 1 if isinstance(request.task_id, int) else 1
                                new_task_id2 = request.task_id * 10 + 2 if isinstance(request.task_id, int) else 2

                                request1 = APIRequest(
                                    task_id=new_task_id1,
                                    batch_files=batch1,
                                    request_json=request_json1,
                                    attempts_left=request.attempts_left - 1
                                )

                                request2 = APIRequest(
                                    task_id=new_task_id2,
                                    batch_files=batch2,
                                    request_json=request_json2,
                                    attempts_left=request.attempts_left - 1
                                )

                                # Add both new requests to the retry queue
                                retry_queue.put_nowait(request1)
                                retry_queue.put_nowait(request2)
                                logger.info(f"Split batch {request.task_id} into two smaller batches with {len(batch1)} and {len(batch2)} files")
                            else:
                                # If we can't split further, just retry as is
                                retry_queue.put_nowait(request)
                        elif request.attempts_left > 0:
                            retry_queue.put_nowait(request)
                        else:
                            status_tracker.num_tasks_failed += 1
                            status_tracker.num_tasks_in_progress -= 1
                except Exception as e:
                    logger.error(f"OpenRouter request {request.task_id} failed with exception: {e}")
                    request.error = str(e)
                    if request.attempts_left > 0:
                        retry_queue.put_nowait(request)
                    else:
                        logger.error(f"OpenRouter request {request.task_id} failed after all retry attempts. Exception: {e}")
                        status_tracker.num_tasks_failed += 1
                        status_tracker.num_tasks_in_progress -= 1
            else:
                # Use direct HTTP request for OpenAI or OpenRouter (non-level 1 domains)
                # Implement exponential backoff for retries with improved connection handling
                max_retries = 3  # Increased from 3 to 5
                retry_delay = 1  # Reduced to 1 second for faster retries
                retry_attempt = 0

                while retry_attempt < max_retries:
                    try:
                        # Use a longer timeout and TCP connection pooling settings
                        conn = aiohttp.TCPConnector(limit=40, force_close=False, enable_cleanup_closed=True)
                        timeout = aiohttp.ClientTimeout(total=240, connect=30, sock_connect=30, sock_read=180)

                        # Create a new session with optimized connection settings
                        async with aiohttp.ClientSession(connector=conn, timeout=timeout) as request_session:
                            async with request_session.post(
                                url=request_url,
                                headers=request_header,
                                json=request.request_json
                            ) as response:
                                if response.status == 429:  # Rate limit error
                                    # Exponential backoff for rate limit errors
                                    wait_time = retry_delay * (2 ** retry_attempt)
                                    logger.warning(f"Rate limit exceeded for request {request.task_id}, retrying in {wait_time} seconds (attempt {retry_attempt+1}/{max_retries})")
                                    status_tracker.time_of_last_rate_limit_error = time.time()
                                    status_tracker.num_rate_limit_errors += 1
                                    await asyncio.sleep(wait_time)
                                    retry_attempt += 1
                                    continue

                                response_data = await response.json()

                                if response.status != 200 or "error" in response_data:
                                    error_text = response_data.get("error", {}).get("message", str(response_data))
                                    logger.warning(f"Request {request.task_id} failed with error: {error_text}")

                                    if "error" in response_data and "rate limit" in response_data["error"].get("message", "").lower():
                                        # Exponential backoff for rate limit errors
                                        wait_time = retry_delay * (2 ** retry_attempt)
                                        logger.warning(f"Rate limit error for request {request.task_id}, retrying in {wait_time} seconds (attempt {retry_attempt+1}/{max_retries})")
                                        status_tracker.time_of_last_rate_limit_error = time.time()
                                        status_tracker.num_rate_limit_errors += 1
                                        await asyncio.sleep(wait_time)
                                        retry_attempt += 1
                                        continue
                                    else:
                                        status_tracker.num_api_errors += 1
                                        # For non-rate-limit errors, use a smaller backoff
                                        wait_time = 1 * (1.5 ** retry_attempt)
                                        logger.warning(f"API error for request {request.task_id}, retrying in {wait_time} seconds (attempt {retry_attempt+1}/{max_retries})")
                                        await asyncio.sleep(wait_time)
                                        retry_attempt += 1
                                        continue

                            # If we get here, the request was successful
                            break

                    except (aiohttp.ClientError, asyncio.TimeoutError) as e:
                        # Handle network errors with improved exponential backoff
                        wait_time = retry_delay * (2 ** retry_attempt) + (retry_attempt * 2)  # Add jitter
                        logger.warning(f"Network error for request {request.task_id}: {e}, retrying in {wait_time} seconds (attempt {retry_attempt+1}/{max_retries})")
                        await asyncio.sleep(wait_time)
                        retry_attempt += 1
                        # Force garbage collection to free up resources
                        import gc
                        gc.collect()
                        continue

                # If we've exhausted all retries and still haven't succeeded
                if retry_attempt >= max_retries:
                    error_text = f"Request {request.task_id} failed after {max_retries} retry attempts"
                    logger.error(error_text)
                    request.error = error_text
                    status_tracker.num_tasks_failed += 1
                    status_tracker.num_tasks_in_progress -= 1

                    # Even if this request failed, return an empty result to prevent blocking
                    results[request.task_id] = []
                    return

                # If we get here, the request was successful
                try:
                    # Extract the response content
                    content = response_data["choices"][0]["message"]["content"] # type: ignore

                    # Find JSON content between triple backticks
                    json_content = content
                    if "```json" in content:
                        json_content = content.split("```json")[1].split("```")[0].strip()
                    elif "```" in content:
                        json_content = content.split("```")[1].split("```")[0].strip()

                    # Try to parse the JSON content with enhanced error handling
                    try:
                        domain_files = json.loads(json_content)
                    except json.JSONDecodeError as json_err:
                        logger.error(f"JSON parsing error for batch {request.task_id}: {json_err}")

                        # If it's an unterminated string error, try to fix it
                        if "Unterminated string" in str(json_err):
                            logger.info(f"Attempting to fix unterminated string in batch {request.task_id}")
                            # Try to extract just the array part
                            if json_content.strip().startswith('[') and ']' in json_content:
                                fixed_content = json_content[:json_content.rindex(']')+1]
                                try:
                                    domain_files = json.loads(fixed_content)
                                    logger.info(f"Successfully fixed JSON for batch {request.task_id}")
                                except Exception as fix_err:
                                    # If fixing failed, try a more aggressive approach - extract file paths
                                    logger.warning(f"First fix attempt failed: {fix_err}. Trying more aggressive fix.")
                                    # Extract anything that looks like a file path
                                    import re
                                    file_paths = re.findall(r'"([^"]+\.\w+)"', json_content)
                                    domain_files = list(set(file_paths))  # Remove duplicates
                                    if domain_files:
                                        logger.info(f"Extracted {len(domain_files)} file paths using regex")
                                    else:
                                        # If we couldn't extract anything, raise the original error
                                        raise json_err
                            else:
                                # If we can't easily fix it, try to extract file paths
                                import re
                                file_paths = re.findall(r'"([^"]+\.\w+)"', json_content)
                                domain_files = list(set(file_paths))  # Remove duplicates
                                if domain_files:
                                    logger.info(f"Extracted {len(domain_files)} file paths using regex")
                                else:
                                    # If we couldn't extract anything, raise the original error
                                    raise json_err
                        else:
                            # For other JSON errors, re-raise
                            raise

                    # Update the results
                    results[request.task_id] = domain_files

                    logger.info(f"Successfully processed batch {request.task_id} with {len(domain_files)} files for domain: {domain_name}")
                    status_tracker.num_tasks_succeeded += 1
                    status_tracker.num_tasks_in_progress -= 1

                except Exception as e:
                    logger.error(f"Error parsing API response for batch {request.task_id} for domain {domain_name}: {e}")
                    request.error = f"Error parsing API response: {e}"

                    # If this is a batch size issue, try splitting the batch
                    if "Unterminated string" in str(e) and request.attempts_left > 0:
                        logger.warning(f"Detected possible batch size issue for batch {request.task_id}. Will retry with smaller batch.")
                        # Split the batch in half for the retry
                        if len(request.batch_files) > 1:
                            mid = len(request.batch_files) // 2
                            batch1 = request.batch_files[:mid]
                            batch2 = request.batch_files[mid:]

                            # Create two new requests with the split batches
                            request_json1 = request.request_json.copy()
                            request_json1['messages'][-1]['content'] = request_json1['messages'][-1]['content'].replace(
                                json.dumps(request.batch_files, indent=2),
                                json.dumps(batch1, indent=2)
                            )

                            request_json2 = request.request_json.copy()
                            request_json2['messages'][-1]['content'] = request_json2['messages'][-1]['content'].replace(
                                json.dumps(request.batch_files, indent=2),
                                json.dumps(batch2, indent=2)
                            )

                            # Create new task IDs
                            new_task_id1 = request.task_id * 10 + 1 if isinstance(request.task_id, int) else 1
                            new_task_id2 = request.task_id * 10 + 2 if isinstance(request.task_id, int) else 2

                            # Create new requests
                            request1 = APIRequest(
                                task_id=new_task_id1,
                                batch_files=batch1,
                                request_json=request_json1,
                                attempts_left=request.attempts_left - 1
                            )

                            request2 = APIRequest(
                                task_id=new_task_id2,
                                batch_files=batch2,
                                request_json=request_json2,
                                attempts_left=request.attempts_left - 1
                            )

                            # Add both new requests to the retry queue
                            retry_queue.put_nowait(request1)
                            retry_queue.put_nowait(request2)
                            logger.info(f"Split batch {request.task_id} into two smaller batches with {len(batch1)} and {len(batch2)} files")
                        else:
                            # If we can't split further, just retry as is
                            retry_queue.put_nowait(request)
                    elif request.attempts_left > 0:
                        retry_queue.put_nowait(request)
                    else:
                        status_tracker.num_tasks_failed += 1
                        status_tracker.num_tasks_in_progress -= 1

        except Exception as e:
            logging.warning(f"Request {request.task_id} failed with Exception {e}")
            status_tracker.num_other_errors += 1
            request.error = str(e)

            if request.attempts_left > 0:
                retry_queue.put_nowait(request)
            else:
                logging.error(f"Request {request.task_id} failed after all attempts. Error: {e}")
                status_tracker.num_tasks_failed += 1
                status_tracker.num_tasks_in_progress -= 1

class HierarchicalDomainFileMapperIntegration:
    """
    Integration class for adding hierarchical domain-to-file mapping to the repository analysis flow.

    This class provides methods that can be called from the repository analysis flow
    to add hierarchical domain-to-file mapping as an additional step.
    """

    @staticmethod
    async def map_files_to_domains_hierarchically(
        domain_analysis_yaml_path: str,
        file_call_graph_yaml_path: str,
        output_path: str,
        api_key: Optional[str] = None,
        model: str = "gpt-4o-mini",
        max_requests_per_minute: float = 2500,  # Increased for better throughput
        max_tokens_per_minute: float = 20000000,  # Increased for better throughput
        batch_size: int = 75,  # Increased for better throughput
        use_openrouter: bool = False,  # Default to OpenAI for better stability
        openrouter_base_url: str = "https://openrouter.ai/api/v1",
        go_gemini: bool = False,  # Default to False for better stability
        checkpoint_interval_seconds: int = 180,  # Save state every 5 minutes by default
    ) -> bool:
        """
        Map files to domains hierarchically from domain analysis and file graph YAMLs.

        Args:
            domain_analysis_yaml_path: Path to the domain analysis YAML file
            file_call_graph_yaml_path: Path to the file-driven call graph YAML file
            output_path: Path to save the hierarchical domain-to-file mappings YAML
            api_key: OpenAI API key (if None, will try to get from environment)
            model: OpenAI model to use
            max_requests_per_minute: Rate limit for API requests
            max_tokens_per_minute: Token rate limit for API
            batch_size: Number of files to process in each batch

        Returns:
            True if mapping was successful, False otherwise
        """
        try:
            mapper = HierarchicalDomainFileMapper(
                domain_analysis_yaml_path=domain_analysis_yaml_path,
                file_call_graph_yaml_path=file_call_graph_yaml_path,
                output_path=output_path,
                api_key=api_key,
                model=model,
                max_requests_per_minute=max_requests_per_minute,
                max_tokens_per_minute=max_tokens_per_minute,
                batch_size=batch_size,
                use_openrouter=use_openrouter,
                openrouter_base_url=openrouter_base_url,
                go_gemini=go_gemini,
                checkpoint_interval_seconds=checkpoint_interval_seconds,
            )

            result = await mapper.map_files_to_domains_hierarchically()
            return result.success

        except Exception as e:
            logger.error(f"Error in hierarchical domain-to-file mapping: {e}")
            return False

async def main():
    """Main entry point for the hierarchical domain file mapper."""
    parser = argparse.ArgumentParser(description="Map files to domains hierarchically")
    parser.add_argument("--domain-yaml", required=True, help="Path to the domain analysis YAML file")
    parser.add_argument("--file-graph-yaml", required=True, help="Path to the file-driven call graph YAML file")
    parser.add_argument("--output", required=True, help="Path to save the hierarchical domain-to-file mappings YAML")
    parser.add_argument("--api-key", help="OpenAI API key (if not provided, will try to get from environment)")
    parser.add_argument("--model", default="gpt-4o-mini", help="OpenAI model to use")
    parser.add_argument("--requests-per-minute", type=float, default=2000, help="Rate limit for API requests")
    parser.add_argument("--tokens-per-minute", type=float, default=1000000, help="Token rate limit for API")
    parser.add_argument("--temperature", type=float, default=0.1, help="Temperature setting for the model")
    parser.add_argument("--batch-size", type=int, default=50, help="Number of files to process in each batch")
    parser.add_argument("--checkpoint-interval", type=int, default=300, help="Time interval in seconds between automatic checkpoints")

    args = parser.parse_args()

    try:
        mapper = HierarchicalDomainFileMapper(
            domain_analysis_yaml_path=args.domain_yaml,
            file_call_graph_yaml_path=args.file_call_graph_yaml,
            output_path=args.output,
            api_key=args.api_key,
            model=args.model,
            max_requests_per_minute=args.requests_per_minute,
            max_tokens_per_minute=args.tokens_per_minute,
            temperature=args.temperature,
            batch_size=args.batch_size,
            checkpoint_interval_seconds=args.checkpoint_interval,
        )

        result = await mapper.map_files_to_domains_hierarchically()

        if result.success:
            logger.info("Hierarchical domain-to-file mapping completed successfully")
            return 0
        else:
            logger.error(f"Hierarchical domain-to-file mapping failed: {result.error_message}")
            return 1

    except Exception as e:
        logger.error(f"Error in hierarchical domain-to-file mapping: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
