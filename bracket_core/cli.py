"""Command-line interface for Bracket Core.

Commands:
- file_call_graph: Generate a file-driven call graph from a function-driven call graph
- map_files_to_domains: Map files to domains at all levels of the hierarchy
- build_domain_traces: Build domain traces and classify functions
- normalize_function_paths: Normalize function paths in domain traces to ensure all functions have full paths
- visualize: Generate visualizations from analysis results
- analyze: Analyze a codebase and generate domain models
"""

import time
import os
import asyncio
import typer
from rich import print as rprint
from typing import Optional

from bracket_core.irl import RepoAnalysisFlow

app = typer.Typer(help="Bracket Core CLI")

@app.command()
def file_call_graph(
    functions_parquet: str = typer.Argument(..., help="Path to the semantic_documented_functions.parquet file"),
    output: str = typer.Argument(..., help="Path to save the file-driven call graph YAML"),
    repo_root: Optional[str] = typer.Option(None, help="Repository root directory (default: current directory)"),
):
    """Generate a file-driven call graph from a function-driven call graph."""
    from bracket_core.file_call_graph_builder import FileCallGraphBuilder

    rprint(f"[bold green]Generating file-driven call graph from:[/] {functions_parquet}")
    rprint(f"[bold green]Output file:[/] {output}")

    # Create a file call graph builder
    builder = FileCallGraphBuilder(
        functions_parquet_path=functions_parquet,
        output_path=output,
        repo_root=repo_root
    )

    # Build file call graph
    result = builder.build_file_call_graph()

    if result.success:
        rprint(f"[bold green]File call graph generation complete. Results saved to:[/] {output}")
    else:
        rprint(f"[bold red]File call graph generation failed:[/] {result.error_message}")


@app.command()
def map_files_to_domains(
    domain_yaml: str = typer.Argument(..., help="Path to the domain analysis YAML file"),
    file_graph_yaml: str = typer.Argument(..., help="Path to the file-driven call graph YAML file"),
    output: str = typer.Argument(..., help="Path to save the domain-to-file mappings YAML"),
    hierarchical: bool = typer.Option(False, help="Use hierarchical domain-file mapping"),
    api_key: Optional[str] = typer.Option(None, help="API key (OpenAI or OpenRouter)"),
    model: str = typer.Option("gpt-4o-mini", help="Model to use (OpenAI or OpenRouter model ID)"),
    requests_per_minute: float = typer.Option(50, help="Rate limit for API requests"),
    tokens_per_minute: float = typer.Option(100000, help="Token rate limit for API"),
    batch_size: int = typer.Option(100, help="Number of files to process in each batch (hierarchical only)"),
    use_openrouter: bool = typer.Option(False, help="Use OpenRouter instead of OpenAI"),
    openrouter_base_url: str = typer.Option("https://openrouter.ai/api/v1", help="Base URL for OpenRouter API"),
    go_gemini: bool = typer.Option(False, help="Use Gemini model for top-level domain file allocation"),
):
    """Map files to domains at all levels of the hierarchy."""
    if hierarchical:
        from bracket_core.hierarchical_domain_file_mapper import HierarchicalDomainFileMapperIntegration

        rprint(f"[bold green]Mapping files to domains hierarchically[/]")
        rprint(f"[bold green]Domain YAML:[/] {domain_yaml}")
        rprint(f"[bold green]File graph YAML:[/] {file_graph_yaml}")
        rprint(f"[bold green]Output file:[/] {output}")

        # Map files to domains hierarchically
        success = asyncio.run(HierarchicalDomainFileMapperIntegration.map_files_to_domains_hierarchically(
            domain_yaml_path=domain_yaml,
            file_graph_yaml_path=file_graph_yaml,
            output_path=output,
            api_key=api_key,
            model=model,
            max_requests_per_minute=requests_per_minute,
            max_tokens_per_minute=tokens_per_minute,
            batch_size=batch_size,
            use_openrouter=use_openrouter,
            openrouter_base_url=openrouter_base_url,
            go_gemini=go_gemini,
        ))

        if success:
            rprint(f"[bold green]Hierarchical domain-to-file mapping complete. Results saved to:[/] {output}")
        else:
            rprint(f"[bold red]Hierarchical domain-to-file mapping failed[/]")
    else:
        from bracket_core.domain_file_mapper import DomainFileMapperIntegration

        rprint(f"[bold green]Mapping files to top-level domains[/]")
        rprint(f"[bold green]Domain YAML:[/] {domain_yaml}")
        rprint(f"[bold green]File graph YAML:[/] {file_graph_yaml}")
        rprint(f"[bold green]Output file:[/] {output}")

        # Map files to domains
        success = asyncio.run(DomainFileMapperIntegration.map_files_to_domains(
            domain_yaml_path=domain_yaml,
            file_graph_yaml_path=file_graph_yaml,
            output_path=output,
            api_key=api_key,
            model=model,
            max_requests_per_minute=requests_per_minute,
            max_tokens_per_minute=tokens_per_minute,
            use_openrouter=use_openrouter,
            openrouter_base_url=openrouter_base_url,
            go_gemini=go_gemini,
        ))

        if success:
            rprint(f"[bold green]Domain-to-file mapping complete. Results saved to:[/] {output}")
        else:
            rprint(f"[bold red]Domain-to-file mapping failed[/]")


@app.command()
def normalize_function_paths(
    traces_yaml: str = typer.Argument(..., help="Path to the domain traces YAML file"),
    functions_parquet: str = typer.Argument(..., help="Path to the semantic_documented_functions.parquet file"),
    output: Optional[str] = typer.Option(None, help="Path to save the updated domain traces YAML file (if not provided, overwrites input file)"),
):
    """Normalize function paths in domain traces to ensure all functions have full paths.

    Note: This is now done automatically when building domain traces, but this command can be used
    to normalize existing domain trace files.
    """
    from bracket_core.hierarchical_domain_trace_builder import HierarchicalDomainTraceBuilderIntegration

    rprint(f"[bold green]Normalizing function paths in domain traces[/]")
    rprint(f"[bold green]Domain traces YAML:[/] {traces_yaml}")
    rprint(f"[bold green]Functions parquet:[/] {functions_parquet}")
    if output:
        rprint(f"[bold green]Output file:[/] {output}")
    else:
        rprint(f"[bold green]Output file:[/] {traces_yaml} (overwriting input file)")

    # Normalize function paths
    success = HierarchicalDomainTraceBuilderIntegration.normalize_function_paths(
        traces_yaml_path=traces_yaml,
        functions_parquet_path=functions_parquet,
        output_path=output
    )

    if success:
        rprint(f"[bold green]Function path normalization complete.[/]")
    else:
        rprint(f"[bold red]Function path normalization failed.[/]")


@app.command()
def build_domain_traces(
    domain_yaml: str = typer.Argument(..., help="Path to the domain YAML file"),
    functions_parquet: str = typer.Argument(..., help="Path to the semantic_documented_functions.parquet file"),
    output: str = typer.Argument(..., help="Path to save the classification output"),
    domain_file_mappings: Optional[str] = typer.Option(None, help="Path to the domain-to-file mappings YAML file"),
    hierarchical: bool = typer.Option(False, help="Use hierarchical domain trace building"),
    api_key: Optional[str] = typer.Option(None, help="API key (OpenAI or OpenRouter)"),
    model: str = typer.Option("gpt-4o-mini", help="Model to use (OpenAI or OpenRouter model ID)"),
    requests_per_minute: float = typer.Option(50, help="Rate limit for API requests"),
    tokens_per_minute: float = typer.Option(100000, help="Token rate limit for API"),
    no_fallback: bool = typer.Option(False, help="Disable fallback to original approach"),
    use_openrouter: bool = typer.Option(False, help="Use OpenRouter instead of OpenAI"),
    openrouter_base_url: str = typer.Option("https://openrouter.ai/api/v1", help="Base URL for OpenRouter API"),
):
    """Build domain traces and classify functions."""
    rprint(f"[bold green]Building domain traces and classifying functions[/]")
    rprint(f"[bold green]Domain YAML:[/] {domain_yaml}")
    rprint(f"[bold green]Functions parquet:[/] {functions_parquet}")
    rprint(f"[bold green]Output file:[/] {output}")

    if domain_file_mappings and hierarchical:
        from bracket_core.hierarchical_domain_trace_builder import HierarchicalDomainTraceBuilderIntegration

        rprint(f"[bold green]Using hierarchical approach with domain-file mappings:[/] {domain_file_mappings}")

        # Build domain traces with hierarchical approach
        success = asyncio.run(HierarchicalDomainTraceBuilderIntegration.build_domain_traces(
            domain_yaml_path=domain_yaml,
            functions_parquet_path=functions_parquet,
            output_path=output,
            domain_file_mappings_path=domain_file_mappings,
            api_key=api_key,
            model=model,
            max_requests_per_minute=requests_per_minute,
            max_tokens_per_minute=tokens_per_minute,
            fallback_to_original=not no_fallback,
            use_openrouter=use_openrouter,
            openrouter_base_url=openrouter_base_url,
        ))
    elif domain_file_mappings:
        from bracket_core.enhanced_domain_trace_builder import EnhancedDomainTraceBuilderIntegration

        rprint(f"[bold green]Using enhanced approach with domain-file mappings:[/] {domain_file_mappings}")

        # Build domain traces with enhanced approach
        success = asyncio.run(EnhancedDomainTraceBuilderIntegration.build_domain_traces(
            domain_yaml_path=domain_yaml,
            functions_parquet_path=functions_parquet,
            output_path=output,
            domain_file_mappings_path=domain_file_mappings,
            api_key=api_key,
            model=model,
            max_requests_per_minute=requests_per_minute,
            max_tokens_per_minute=tokens_per_minute,
            fallback_to_original=not no_fallback,
        ))
    else:
        from bracket_core.domain_trace_builder import DomainTraceBuilder

        rprint(f"[bold green]Using original approach without domain-file mappings[/]")

        # Create a domain trace builder
        builder = DomainTraceBuilder(
            domain_yaml_path=domain_yaml,
            functions_parquet_path=functions_parquet,
            output_path=output,
            api_key=api_key,
            model=model,
            max_requests_per_minute=requests_per_minute,
            max_tokens_per_minute=tokens_per_minute,
        )

        # Build domain traces and classify functions
        result = asyncio.run(builder.build_and_classify())
        success = result.success

    if success:
        rprint(f"[bold green]Domain trace building complete. Results saved to:[/] {output}")
    else:
        rprint(f"[bold red]Domain trace building failed[/]")


@app.command()
def visualize(
    input_file: str = typer.Argument(..., help="Path to the analysis results file"),
    output: str = typer.Option("diagrams", help="Output directory for visualizations"),
):
    """Generate visualizations from analysis results."""
    rprint(f"[bold green]Generating visualizations from:[/] {input_file}")
    rprint(f"[bold green]Output directory:[/] {output}")
    # Placeholder for actual implementation
    rprint("[yellow]This is a placeholder for the actual visualization implementation[/]")



@app.command()
def analyze(
    # path: str = typer.Argument("path", help="Path to the codebase to analyze"),
    # output: str = typer.Option("output", help="Output directory for analysis results"),
    skip_documentation: bool = typer.Option(False, help="Skip function documentation step"),
    skip_domain_analysis: bool = typer.Option(False, help="Skip domain analysis step"),
    use_hierarchical_domains: bool = typer.Option(True, help="Use hierarchical domain-file mapping"),
    skip_file_mapping: bool = typer.Option(False, help="Skip file mapping entirely"),
    force_original_approach: bool = typer.Option(False, help="Force original approach without file mapping"),
    requests_per_minute: float = typer.Option(1500, help="Rate limit for LLM API requests"),
    tokens_per_minute: float = typer.Option(2000000, help="Token rate limit for LLM API"),
    generate_diagrams: bool = typer.Option(True, help="Generate domain diagrams"),
    generate_explanation: bool = typer.Option(True, help="Generate codebase explanation"),
    openai_api_key: Optional[str] = typer.Option(None, help="API key (OpenAI or OpenRouter)"),
    openai_model: str = typer.Option("gpt-4o-mini", help="Model to use (OpenAI or OpenRouter model ID)"),
    max_concurrent_tasks: int = typer.Option(5, help="Maximum number of concurrent tasks"),
    # use_openrouter: bool = typer.Option(False, help="Use OpenRouter instead of OpenAI"),
    openrouter_base_url: str = typer.Option("https://openrouter.ai/api/v1", help="Base URL for OpenRouter API"),
):
    """Analyze a codebase and generate domain models."""

    # python -m bracket_core.cli analyze

    # path = "/Users/<USER>/work/startup/godzilla/bracket/bracket/graphrag"
    # output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_temp_copy/experiments/cg2_exp/hierarchical_parallel_optimized_file/bracket"
    # path = "/Users/<USER>/work/startup/godzilla/adjacent/mem0/mem0"
    # output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/open_router_test/mem0-2"

    path = "/Users/<USER>/work/startup/godzilla/django/django"
    output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/open_router_big/django"



    use_openrouter = True

    use_hierarchical_domains = True
    skip_file_mapping = False
    force_original_approach = False

    rprint(f"[bold green]Analyzing codebase at:[/] {path}")
    # rprint(f"[bold green]Output directory:[/] {output}")

    # Create output directory if it doesn't exist
    os.makedirs(output, exist_ok=True)

    start_time = time.time()

    # Create and run the analysis flow
    flow = RepoAnalysisFlow(
        repo_dir=path,
        output_dir=output,
        verbose=True,
        document_functions=not skip_documentation,
        analyze_domains=not skip_domain_analysis,
        use_hierarchical_domains=use_hierarchical_domains,
        skip_file_mapping=skip_file_mapping,
        force_original_approach=force_original_approach,
        llm_requests_per_minute=requests_per_minute,
        llm_tokens_per_minute=tokens_per_minute,
        generate_diagrams=generate_diagrams,
        generate_explanation=generate_explanation,
        # Diagram model configuration
        diagram_model_type="openai",
        # OpenAI parameters
        openai_api_key=openai_api_key,
        openai_model=openai_model,
        explanation_model=openai_model,
        # Parallelization parameters
        max_concurrent_tasks=max_concurrent_tasks,
        use_openrouter=use_openrouter,
        openrouter_base_url=openrouter_base_url,
    )

    # Run the analysis flow
    asyncio.run(flow.run())

    rprint("[bold green]Analysis complete![/]")
    end_time = time.time()
    rprint(f"[bold green]Time taken!: {end_time-start_time}[/]")



def main():
    """Entry point for the CLI."""
    app()


if __name__ == "__main__":
    main()


"""
Generate a file-driven call graph:
python3 -m bracket_core.cli file-call-graph /Users/<USER>/work/startup/godzilla/bracket/bracket_temp_copy/experiments/setup_exp/data/bracket/semantic_documented_functions.parquet /Users/<USER>/work/startup/godzilla/bracket/bracket_temp_copy/experiments/setup_exp/data/bracket/file_call_graph-1.yaml

Map files to top-level domains:
python -m bracket_core.cli map_files_to_domains /path/to/domain_analysis.yaml /path/to/file_call_graph.yaml /path/to/domain_file_mappings.yaml

Map files to domains hierarchically:
python -m bracket_core.cli map_files_to_domains /path/to/domain_analysis.yaml /path/to/file_call_graph.yaml /path/to/hierarchical_domain_file_mappings.yaml --hierarchical

Build domain traces with domain-file mappings:
python -m bracket_core.cli build_domain_traces /path/to/domain_analysis.yaml /path/to/semantic_documented_functions.parquet /path/to/domain_traces.yaml --domain-file-mappings /path/to/domain_file_mappings.yaml

Build domain traces with hierarchical domain-file mappings:
python -m bracket_core.cli build_domain_traces /path/to/domain_analysis.yaml /path/to/semantic_documented_functions.parquet /path/to/domain_traces.yaml --domain-file-mappings /path/to/hierarchical_domain_file_mappings.yaml --hierarchical

Normalize function paths in domain traces (now done automatically when building domain traces):
python -m bracket_core.cli normalize-function-paths /path/to/domain_traces.yaml /path/to/semantic_documented_functions.parquet --output /path/to/normalized_domain_traces.yaml

Run the full analysis with different approaches:

# Use hierarchical domain-file mapping (default):
python -m bracket_core.cli analyze /path/to/repo /path/to/output

# Use flat domain-file mapping (only top-level domains):
python -m bracket_core.cli analyze /path/to/repo /path/to/output --no-use-hierarchical-domains

# Skip file mapping entirely and use original approach:
python -m bracket_core.cli analyze /path/to/repo /path/to/output --skip-file-mapping

# Force original approach without file mapping:
python -m bracket_core.cli analyze /path/to/repo /path/to/output --force-original-approach

"""


# python -m bracket_core.cli analyze

#python -m bracket_core.cli analyze /Users/<USER>/work/startup/godzilla/bracket/bracket/graphrag /Users/<USER>/work/startup/godzilla/bracket/bracket_temp_copy/experiments/cg2_exp/no-use-hierarchical-domains/bracket --no-use-hierarchical-domains


# Example command to normalize function paths in an existing domain traces file:
# python -m bracket_core.cli normalize-function-paths ./experiments/gitlab/domain_traces_leaf_corrected.yaml ./experiments/gitlab/semantic_documented_functions.parquet