"""
Hybrid Knowledge Graph Generator.

This module implements a lightweight approach to knowledge graph generation
that focuses on extracting function signatures and their called functions
without complex resolution of the exact function being called.
"""

import os
import logging
import time
from typing import Dict, List, Set, Tuple, Optional, Any
from dataclasses import dataclass, field
import networkx as nx
import pandas as pd
from tree_sitter_languages import get_parser, get_language

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class FunctionNode:
    """Represents a function node in the knowledge graph."""
    node_id: str  # Unique identifier for the function (file_path:function_name)
    name: str  # Function name
    file_path: str  # Path to the file containing the function
    module_path: str  # Module path
    start_line: int  # Start line number
    end_line: int  # End line number
    signature: str  # Function signature
    containing_class: str = ""  # Containing class name if method
    is_method: bool = False  # Is this a method
    is_static: bool = False  # Is this a static method
    is_async: bool = False  # Is this an async function
    calls: List[str] = field(default_factory=list)  # List of function names called by this function
    call_contexts: List[str] = field(default_factory=list)  # Full context of each function call
    text: str = ""  # Function text/body

class HybridKnowledgeGraph:
    """
    Generates a knowledge graph using a lightweight approach that focuses on
    extracting function signatures and their called functions without complex resolution.
    """

    def __init__(self, repo_dir: str, verbose: bool = False):
        """
        Initialize the hybrid knowledge graph generator.

        Args:
            repo_dir: Root directory of the repository
            verbose: Enable verbose logging
        """
        self.repo_dir = repo_dir
        self.verbose = verbose
        self.functions: Dict[str, FunctionNode] = {}
        self.function_names: Set[str] = set()  # Set of all function names in the codebase
        # Only include Python files for processing
        self.supported_languages = {
            ".py": "python",
            # ".js": "javascript",
            # ".ts": "typescript",
            # ".java": "java",
            # ".go": "go",
            ".rb": "ruby",
            # ".php": "php",
            # ".cs": "c_sharp",
            # ".cpp": "cpp",
            # ".c": "c",
            # ".rs": "rust",
        }
        self.common_library_prefixes = {
            "python": ["pd.", "np.", "plt.", "tf.", "torch.", "sklearn.", "os.", "sys.", "json.", "re.", "math.", "random.", "datetime.", "collections.", "itertools.", "functools.", "pathlib.", "requests.", "bs4.", "django.", "flask.", "sqlalchemy.", "numpy.", "pandas.", "matplotlib.", "seaborn.", "scipy.", "statsmodels.", "cv2.", "PIL."],
            "javascript": ["console.", "document.", "window.", "Math.", "JSON.", "Array.", "Object.", "String.", "Number.", "Date.", "RegExp.", "Map.", "Set.", "Promise.", "$.", "jQuery.", "React.", "Vue.", "Angular.", "_."],
            "ruby": ["File.", "Dir.", "ENV.", "ARGV.", "STDIN.", "STDOUT.", "STDERR.", "Process.", "Thread.", "Time.", "Date.", "DateTime.", "JSON.", "YAML.", "CSV.", "URI.", "Net::", "Gem::", "Rails.", "ActiveRecord::", "ActiveSupport::", "ActionController::", "ActionView::", "Nokogiri::", "Rack::", "RSpec::", "Sinatra::"]
        }

    def generate_graph(self) -> nx.MultiDiGraph:
        """
        Generate a knowledge graph for the repository.

        Returns:
            NetworkX MultiDiGraph representing the repository
        """
        logger.info(f"Generating knowledge graph for repository: {self.repo_dir}")
        start_time = time.time()

        # First pass: extract all function definitions to build a comprehensive list
        logger.info("First pass: Extracting all function definitions")
        self._extract_all_function_definitions()
        logger.info(f"Found {len(self.function_names)} unique function names in the codebase")

        # Second pass: process all files to extract functions and their calls
        logger.info("Second pass: Processing repository files for function calls")
        self._process_repository()

        # Create a NetworkX graph from the extracted functions
        graph = self._create_graph()

        elapsed_time = time.time() - start_time
        logger.info(f"Knowledge graph generation completed in {elapsed_time:.2f} seconds")
        logger.info(f"Generated graph with {len(graph.nodes)} nodes (node-only approach, no edges)")

        return graph

    def _extract_all_function_definitions(self) -> None:
        """Extract all function definitions from the repository to build a comprehensive list.
        """
        for root, _, files in os.walk(self.repo_dir):
            for file in files:
                # Check if the file extension is supported
                ext = os.path.splitext(file)[1].lower()
                if ext not in self.supported_languages:
                    continue

                file_path = os.path.join(root, file)
                rel_path = os.path.relpath(file_path, self.repo_dir)

                # Skip hidden files and directories
                if any(part.startswith('.') for part in rel_path.split(os.sep)):
                    continue

                # Check if the file extension is supported
                ext = os.path.splitext(file)[1].lower()
                if ext in self.supported_languages:
                    language = self.supported_languages[ext]
                    try:
                        # Read file content
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read()

                        # Get parser for the language
                        parser = get_parser(language)
                        if not parser:
                            continue

                        # Parse the file
                        tree = parser.parse(bytes(content, 'utf-8'))

                        # Get query for function definitions
                        func_query = self._get_function_query(language)
                        if not func_query:
                            continue

                        # Execute function definition query
                        lang = get_language(language)
                        func_query_obj = lang.query(func_query)
                        func_captures = func_query_obj.captures(tree.root_node)

                        # Extract function names
                        for node, capture_name in func_captures:
                            if capture_name in ["function.name", "method.name"]:
                                func_name = node.text.decode('utf-8')
                                self.function_names.add(func_name)
                    except Exception as e:
                        if self.verbose:
                            logger.error(f"Error extracting function definitions from {file_path}: {e}")

        if self.verbose:
            logger.info(f"Extracted function names: {sorted(list(self.function_names))}")

    def _process_repository(self) -> None:
        """Process all files in the repository to extract functions and calls."""
        logger.info("Processing repository files")

        # Walk through the repository
        for root, _, files in os.walk(self.repo_dir):
            # Skip if "test" is in the directory path
            # if 'test' in root.lower():
            #     continue

            for file in files:
                file_path = os.path.join(root, file)
                rel_path = os.path.relpath(file_path, self.repo_dir)

                # Skip hidden files and directories
                if any(part.startswith('.') for part in rel_path.split(os.sep)):
                    continue

                # Skip if "test" appears anywhere in the file path
                # if 'test' in rel_path.lower():
                #     continue

                # Check if the file extension is supported
                ext = os.path.splitext(file)[1].lower()
                if ext in self.supported_languages:
                    language = self.supported_languages[ext]
                    try:
                        self._process_file(file_path, language)
                    except Exception as e:
                        logger.error(f"Error processing file {file_path}: {e}")

    def _process_file(self, file_path: str, language: str) -> None:
        """
        Process a single file to extract functions and calls.

        Args:
            file_path: Path to the file
            language: Programming language of the file
        """
        if self.verbose:
            logger.info(f"Processing file: {file_path}")

        try:
            # Read file content
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            # Get parser for the language
            parser = get_parser(language)
            if not parser:
                logger.warning(f"No parser available for language: {language}")
                return

            # Parse the file
            tree = parser.parse(bytes(content, 'utf-8'))

            # Extract functions and calls
            self._extract_functions_and_calls(file_path, content, tree, language)

        except Exception as e:
            logger.error(f"Error processing file {file_path}: {e}")

    def _extract_functions_and_calls(self, file_path: str, content: str, tree: Any, language: str) -> None:
        """
        Extract functions and their calls from a parsed file.

        Args:
            file_path: Path to the file
            content: File content
            tree: Parsed syntax tree
            language: Programming language
        """
        # Get relative path from repo root
        rel_path = os.path.relpath(file_path, self.repo_dir)
        module_path = self._file_path_to_module_path(rel_path)

        # Get queries for function definitions and calls
        func_query = self._get_function_query(language)
        call_query = self._get_call_query(language)

        if not func_query or not call_query:
            logger.warning(f"No queries available for language: {language}")
            return

        # Execute function definition query
        lang = get_language(language)
        func_query_obj = lang.query(func_query)
        func_captures = func_query_obj.captures(tree.root_node)

        # Process function definitions
        functions = self._process_function_definitions(file_path, module_path, content, func_captures, language)

        # Execute function call query
        call_query_obj = lang.query(call_query)
        call_captures = call_query_obj.captures(tree.root_node)

        # Process function calls
        self._process_function_calls(file_path, content, call_captures, functions, language)

    def _process_function_definitions(
        self,
        file_path: str,
        module_path: str,
        content: str,
        captures: List[Tuple[Any, str]],
        language: str
    ) -> Dict[int, FunctionNode]:
        """
        Process function definition captures and create function nodes.

        Args:
            file_path: Path to the file
            module_path: Module path
            content: File content
            captures: Tree-sitter captures
            language: Programming language

        Returns:
            Dictionary mapping line numbers to function nodes
        """
        # Group captures by function node
        functions_by_line: Dict[int, FunctionNode] = {}
        current_class = ""

        # First pass: extract function definitions
        for node, capture_name in captures:
            if capture_name == "class.name":
                current_class = node.text.decode('utf-8')
                continue

            if capture_name in ["function.name", "method.name"]:
                func_name = node.text.decode('utf-8')
                parent = node.parent

                # Skip if parent is None
                if not parent:
                    continue

                # Get function start and end lines
                start_line = parent.start_point[0] + 1  # 1-indexed
                end_line = parent.end_point[0] + 1

                # Create a unique ID for the function
                is_method = capture_name == "method.name" or current_class != ""
                containing_class = current_class if is_method else ""

                # Get relative path from repo root for node_id
                rel_path = os.path.relpath(file_path, self.repo_dir)

                if containing_class:
                    node_id = f"{rel_path}:{containing_class}.{func_name}"
                    signature = f"{module_path}.{containing_class}.{func_name}()"
                else:
                    node_id = f"{rel_path}:{func_name}"
                    signature = f"{module_path}.{func_name}()"

                # Extract function text
                func_text = content[parent.start_byte:parent.end_byte]

                # Check if function is static or async
                is_static = False
                is_async = False

                # Look for static or async modifiers
                for child in parent.children:
                    if child.type in ["static", "modifier"] and child.text.decode('utf-8') == "static":
                        is_static = True
                    elif child.type in ["async", "modifier"] and child.text.decode('utf-8') == "async":
                        is_async = True

                # Create function node
                function_node = FunctionNode(
                    node_id=node_id,
                    name=func_name,
                    file_path=file_path,
                    module_path=module_path,
                    start_line=start_line,
                    end_line=end_line,
                    signature=signature,
                    containing_class=containing_class,
                    is_method=is_method,
                    is_static=is_static,
                    is_async=is_async,
                    text=func_text
                )

                # Add to functions dictionary
                self.functions[node_id] = function_node

                # Map line numbers to function nodes for call resolution
                for line in range(start_line, end_line + 1):
                    functions_by_line[line] = function_node

        return functions_by_line

    def _process_function_calls(
        self,
        file_path: str,
        content: str,
        captures: List[Tuple[Any, str]],
        functions_by_line: Dict[int, FunctionNode],
        language: str
    ) -> None:
        """
        Process function call captures and add them to function nodes.

        Args:
            file_path: Path to the file
            content: File content
            captures: Tree-sitter captures
            functions_by_line: Dictionary mapping line numbers to function nodes
            language: Programming language
        """
        # Process function calls
        for node, capture_name in captures:
            if capture_name in ["call.name", "call.method"]:
                # Get the call name
                call_name = node.text.decode('utf-8')

                # Skip built-in functions, library functions, and special methods
                if self._should_skip_function_call(call_name, language):
                    continue

                # Skip if the function name is not in our codebase
                # For method calls, we check just the method name part
                if '.' in call_name:
                    # For method calls like obj.method(), extract just the method name
                    method_name = call_name.split('.')[-1]
                    if method_name not in self.function_names:
                        continue
                elif call_name not in self.function_names:
                    continue

                # Get the line number and extract call context
                line_number = node.start_point[0] + 1  # 1-indexed

                # Extract the full line or context of the call
                call_context = self._extract_call_context(content, node)

                # Find the containing function
                containing_function = None
                for func_line in sorted(functions_by_line.keys(), reverse=True):
                    if func_line <= line_number:
                        containing_function = functions_by_line[func_line]
                        break

                # Add the call to the containing function
                if containing_function:
                    if call_name not in containing_function.calls:
                        containing_function.calls.append(call_name)
                        containing_function.call_contexts.append(call_context)

    def _extract_call_context(self, content: str, node: Any) -> str:
        """
        Extract the full line or context of a function call.

        Args:
            content: File content
            node: Tree-sitter node representing the function call

        Returns:
            String containing the call context
        """
        try:
            # Get the parent node (usually the call expression)
            parent_node = node.parent
            if parent_node:
                # Get the line containing the call
                line_start = parent_node.start_point[0]
                line_end = parent_node.end_point[0]

                # If it's a single line call
                if line_start == line_end:
                    lines = content.splitlines()
                    if 0 <= line_start < len(lines):
                        line = lines[line_start].strip()
                        return line
                else:
                    # For multi-line calls, extract the relevant part
                    call_text = content[parent_node.start_byte:parent_node.end_byte]
                    # Limit to a reasonable length
                    if len(call_text) > 100:
                        call_text = call_text[:97] + "..."
                    return call_text.strip()

            # Fallback: extract just the node text with some context
            node_text = node.text.decode('utf-8')
            return f"{node_text}(...)"
        except Exception as e:
            if self.verbose:
                logger.warning(f"Error extracting call context: {e}")
            return f"{node.text.decode('utf-8')}(...)"

    def _create_graph(self) -> nx.MultiDiGraph:
        """
        Create a NetworkX graph from the extracted functions.
        Only creates nodes, not edges, as per the node-only approach.

        Returns:
            NetworkX MultiDiGraph
        """
        logger.info("Creating graph from extracted functions...")

        # Create a new graph
        G = nx.MultiDiGraph()

        # Add nodes for all functions
        for node_id, function in self.functions.items():
            G.add_node(
                node_id,
                name=function.name,
                file_path=function.file_path,
                module_path=function.module_path,
                start_line=function.start_line,
                end_line=function.end_line,
                signature=function.signature,
                containing_class=function.containing_class,
                is_method=function.is_method,
                is_static=function.is_static,
                is_async=function.is_async,
                calls=function.calls,
                call_contexts=function.call_contexts,
                text=function.text
            )

        # Note: Edge generation is skipped in the node-only approach
        # The 'calls' and 'call_contexts' attributes in each node contain the necessary information

        return G

    def _file_path_to_module_path(self, file_path: str) -> str:
        """
        Convert a file path to a module path.

        Args:
            file_path: File path relative to the repository root

        Returns:
            Module path
        """
        # Remove file extension
        module_path = os.path.splitext(file_path)[0]

        # Replace directory separators with dots
        module_path = module_path.replace(os.sep, '.')

        # Remove __init__ from the end
        if module_path.endswith('.__init__'):
            module_path = module_path[:-9]

        return module_path

    def _get_function_query(self, language: str) -> Optional[str]:
        """
        Get tree-sitter query for function definitions based on language.

        Args:
            language: Programming language

        Returns:
            Query string or None if not supported
        """
        queries = {
            "python": """
                (class_definition
                  name: (identifier) @class.name) @class.def

                (function_definition
                  name: (identifier) @function.name) @function.def

                (decorated_definition
                  definition: (function_definition
                    name: (identifier) @function.name)) @function.def

                (decorated_definition
                  definition: (class_definition
                    name: (identifier) @class.name)) @class.def
            """,
            "javascript": """
                (class_declaration
                  name: (identifier) @class.name) @class.def

                (method_definition
                  name: (property_identifier) @method.name) @method.def

                (function_declaration
                  name: (identifier) @function.name) @function.def

                (function
                  name: (identifier) @function.name) @function.def

                (arrow_function) @function.def
            """,
            "typescript": """
                (class_declaration
                  name: (identifier) @class.name) @class.def

                (method_definition
                  name: (property_identifier) @method.name) @method.def

                (function_declaration
                  name: (identifier) @function.name) @function.def

                (function
                  name: (identifier) @function.name) @function.def

                (arrow_function) @function.def
            """,
            "java": """
                (class_declaration
                  name: (identifier) @class.name) @class.def

                (method_declaration
                  name: (identifier) @method.name) @method.def

                (constructor_declaration) @method.def
            """,
            "go": """
                (type_declaration
                  (type_spec
                    name: (type_identifier) @class.name)) @class.def

                (function_declaration
                  name: (identifier) @function.name) @function.def

                (method_declaration
                  name: (field_identifier) @method.name) @method.def
            """,
            "ruby": """
                (class
                  name: (constant) @class.name) @class.def

                (method
                  name: (identifier) @method.name) @method.def

                (singleton_method
                  name: (identifier) @method.name) @method.def
            """,
            "php": """
                (class_declaration
                  name: (name) @class.name) @class.def

                (method_declaration
                  name: (name) @method.name) @method.def

                (function_definition
                  name: (name) @function.name) @function.def
            """,
            "c_sharp": """
                (class_declaration
                  name: (identifier) @class.name) @class.def

                (method_declaration
                  name: (identifier) @method.name) @method.def
            """,
            "cpp": """
                (class_specifier
                  name: (type_identifier) @class.name) @class.def

                (function_definition
                  declarator: (function_declarator
                    declarator: (identifier) @function.name)) @function.def

                (function_definition
                  declarator: (function_declarator
                    declarator: (field_identifier) @method.name)) @method.def
            """,
            "c": """
                (function_definition
                  declarator: (function_declarator
                    declarator: (identifier) @function.name)) @function.def
            """,
            "rust": """
                (struct_item
                  name: (identifier) @class.name) @class.def

                (impl_item) @class.def

                (function_item
                  name: (identifier) @function.name) @function.def
            """
        }

        return queries.get(language)

    def _get_call_query(self, language: str) -> Optional[str]:
        """
        Get tree-sitter query for function calls based on language.

        Args:
            language: Programming language

        Returns:
            Query string or None if not supported
        """
        queries = {
            "python": """
                (call
                  function: (identifier) @call.name) @call.expr

                (call
                  function: (attribute
                    attribute: (identifier) @call.method)) @call.expr
            """,
            "javascript": """
                (call_expression
                  function: (identifier) @call.name) @call.expr

                (call_expression
                  function: (member_expression
                    property: (property_identifier) @call.method)) @call.expr
            """,
            "typescript": """
                (call_expression
                  function: (identifier) @call.name) @call.expr

                (call_expression
                  function: (member_expression
                    property: (property_identifier) @call.method)) @call.expr
            """,
            "java": """
                (method_invocation
                  name: (identifier) @call.name) @call.expr

                (method_reference
                  name: (identifier) @call.name) @call.expr
            """,
            "go": """
                (call_expression
                  function: (identifier) @call.name) @call.expr

                (call_expression
                  function: (selector_expression
                    field: (field_identifier) @call.method)) @call.expr
            """,
            "ruby": """
                (call
                  method: (identifier) @call.name) @call.expr

                (call
                  receiver: (_)
                  method: (identifier) @call.method) @call.expr
            """,
            "php": """
                (function_call_expression
                  name: (name) @call.name) @call.expr

                (method_call_expression
                  name: (name) @call.method) @call.expr
            """,
            "c_sharp": """
                (invocation_expression
                  expression: (identifier) @call.name) @call.expr

                (invocation_expression
                  expression: (member_access_expression
                    name: (identifier) @call.method)) @call.expr
            """,
            "cpp": """
                (call_expression
                  function: (identifier) @call.name) @call.expr

                (call_expression
                  function: (field_expression
                    field: (field_identifier) @call.method)) @call.expr
            """,
            "c": """
                (call_expression
                  function: (identifier) @call.name) @call.expr
            """,
            "rust": """
                (call_expression
                  function: (identifier) @call.name) @call.expr

                (call_expression
                  function: (field_expression
                    field: (field_identifier) @call.method)) @call.expr
            """
        }

        return queries.get(language)

    def _should_skip_function_call(self, func_name: str, language: str) -> bool:
        """
        Check if a function call should be skipped (built-in, library function, etc.)

        Args:
            func_name: Function name
            language: Programming language

        Returns:
            True if the function call should be skipped, False otherwise
        """
        # Check if it's a built-in function
        if self._is_builtin_function(func_name, language):
            return True

        # Check if it's a common library function
        if self._is_library_function(func_name, language):
            return True

        # Check for common patterns that indicate library/framework functions
        if '.' in func_name:
            prefix = func_name.split('.')[0]
            # Skip common module imports like 'os.path', 'sys.argv', etc.
            if language == 'python' and prefix.lower() in ['os', 'sys', 'json', 're', 'math', 'random', 'datetime',
                                 'collections', 'itertools', 'functools', 'pathlib',
                                 'requests', 'bs4', 'django', 'flask', 'sqlalchemy',
                                 'numpy', 'np', 'pandas', 'pd', 'matplotlib', 'plt',
                                 'seaborn', 'scipy', 'sklearn', 'tensorflow', 'tf',
                                 'torch', 'cv2', 'PIL']:
                return True
            # Skip common Ruby modules
            elif language == 'ruby' and prefix in ['File', 'Dir', 'ENV', 'ARGV', 'STDIN', 'STDOUT', 'STDERR',
                                                'Process', 'Thread', 'Time', 'Date', 'DateTime', 'JSON', 'YAML',
                                                'CSV', 'URI', 'Net', 'Gem', 'Rails', 'ActiveRecord', 'ActiveSupport',
                                                'ActionController', 'ActionView', 'Nokogiri', 'Rack', 'RSpec', 'Sinatra']:
                return True

            # Check for common library prefixes
            for prefix in self.common_library_prefixes.get(language, []):
                if func_name.startswith(prefix):
                    return True

        # Check for common method patterns
        if func_name in ['append', 'extend', 'insert', 'remove', 'pop', 'clear', 'index', 'count',
                        'sort', 'reverse', 'copy', 'keys', 'values', 'items', 'get', 'update',
                        'add', 'remove', 'discard', 'read', 'write', 'close', 'seek', 'tell',
                        'split', 'strip', 'lstrip', 'rstrip', 'upper', 'lower', 'title',
                        'capitalize', 'startswith', 'endswith', 'find', 'replace', 'join',
                        'format', 'encode', 'decode']:
            return True

        return False

    def _is_builtin_function(self, func_name: str, language: str) -> bool:
        """
        Check if a function is a built-in function.

        Args:
            func_name: Function name
            language: Programming language

        Returns:
            True if the function is built-in, False otherwise
        """
        # Common built-in functions by language
        builtins = {
            "python": [
                "print", "len", "str", "int", "float", "list", "dict", "set", "tuple",
                "range", "enumerate", "zip", "map", "filter", "sorted", "sum", "min", "max",
                "abs", "all", "any", "chr", "ord", "open", "input", "isinstance", "issubclass",
                "hasattr", "getattr", "setattr", "delattr", "dir", "vars", "globals", "locals",
                "__init__", "__str__", "__repr__", "__len__", "__getitem__", "__setitem__",
                "__contains__", "__iter__", "__next__", "__enter__", "__exit__"
            ],
            "javascript": [
                "console", "log", "parseInt", "parseFloat", "String", "Number", "Boolean",
                "Array", "Object", "Math", "JSON", "Date", "RegExp", "Error", "setTimeout",
                "setInterval", "clearTimeout", "clearInterval", "fetch", "Promise", "Map",
                "Set", "WeakMap", "WeakSet", "Symbol", "Proxy", "Reflect", "eval", "isNaN",
                "isFinite", "encodeURI", "decodeURI", "encodeURIComponent", "decodeURIComponent"
            ],
            "ruby": [
                "puts", "print", "p", "gets", "require", "require_relative", "load", "include", "extend",
                "attr_accessor", "attr_reader", "attr_writer", "class_eval", "instance_eval", "module_eval",
                "define_method", "send", "lambda", "proc", "raise", "fail", "catch", "throw", "loop",
                "yield", "block_given?", "super", "initialize", "new", "to_s", "to_i", "to_f", "to_a",
                "to_h", "inspect", "class", "is_a?", "kind_of?", "respond_to?", "nil?", "empty?", "each",
                "map", "select", "reject", "collect", "inject", "reduce", "detect", "find", "all?", "any?"
            ],
            # Add more languages as needed
        }

        # Check if the function is in the built-in list for the language
        return func_name in builtins.get(language, [])

    def _is_library_function(self, func_name: str, language: str) -> bool:
        """
        Check if a function is likely from a library.

        Args:
            func_name: Function name
            language: Programming language

        Returns:
            True if the function is likely from a library, False otherwise
        """
        # Common library functions by language
        library_functions = {
            "python": [
                # Pandas
                "read_csv", "read_excel", "read_json", "read_sql", "DataFrame", "Series",
                "concat", "merge", "pivot", "pivot_table", "groupby", "agg", "apply", "map",
                "fillna", "dropna", "drop", "reset_index", "set_index", "sort_values",

                # NumPy
                "array", "zeros", "ones", "empty", "full", "arange", "linspace", "reshape",
                "transpose", "concatenate", "split", "mean", "std", "var", "min", "max",

                # Matplotlib
                "plot", "scatter", "bar", "barh", "hist", "boxplot", "pie", "imshow",
                "figure", "subplot", "title", "xlabel", "ylabel", "legend", "savefig", "show",

                # Requests
                "get", "post", "put", "delete", "head", "options", "patch", "request",

                # Flask
                "route", "render_template", "redirect", "url_for", "flash", "jsonify",

                # Django
                "render", "redirect", "get_object_or_404", "reverse", "HttpResponse",

                # SQLAlchemy
                "create_engine", "Column", "Integer", "String", "Boolean", "DateTime",
                "ForeignKey", "relationship", "session", "query", "filter", "order_by",

                # Common methods
                "append", "extend", "insert", "remove", "pop", "clear", "index", "count",
                "sort", "reverse", "copy", "keys", "values", "items", "get", "update",
                "add", "remove", "discard", "read", "write", "close", "seek", "tell",
                "split", "strip", "lstrip", "rstrip", "upper", "lower", "title",
                "capitalize", "startswith", "endswith", "find", "replace", "join",
                "format", "encode", "decode"
            ],
            "javascript": [
                # DOM manipulation
                "getElementById", "getElementsByClassName", "getElementsByTagName",
                "querySelector", "querySelectorAll", "createElement", "appendChild",
                "removeChild", "setAttribute", "getAttribute", "innerHTML", "textContent",

                # jQuery
                "ready", "click", "on", "off", "addClass", "removeClass", "toggleClass",
                "attr", "prop", "val", "text", "html", "append", "prepend", "after", "before",
                "remove", "empty", "css", "show", "hide", "toggle", "animate", "ajax", "get",
                "post", "each", "map", "filter", "find", "parent", "children", "siblings",

                # React
                "useState", "useEffect", "useContext", "useReducer", "useCallback",
                "useMemo", "useRef", "createContext", "createElement", "render",
                "Component", "PureComponent", "Fragment", "memo", "forwardRef",

                # Vue
                "createApp", "reactive", "ref", "computed", "watch", "onMounted",
                "onUpdated", "onUnmounted", "nextTick", "provide", "inject",

                # Common methods
                "push", "pop", "shift", "unshift", "splice", "slice", "concat", "join",
                "split", "indexOf", "lastIndexOf", "includes", "find", "filter", "map",
                "reduce", "forEach", "some", "every", "sort", "reverse", "toString",
                "valueOf", "trim", "toLowerCase", "toUpperCase", "replace", "match",
                "search", "substring", "substr", "charAt", "charCodeAt", "padStart",
                "padEnd", "startsWith", "endsWith", "repeat", "trim", "trimStart",
                "trimEnd", "toFixed", "toPrecision", "toExponential", "toLocaleString"
            ],
            "ruby": [
                # Ruby Standard Library
                "open", "read", "write", "close", "each_line", "readlines", "chmod", "mkdir", "rmdir",
                "glob", "exist?", "directory?", "file?", "executable?", "readable?", "writable?",
                "join", "split", "dirname", "basename", "extname", "expand_path", "absolute_path",

                # String methods
                "chomp", "chop", "strip", "lstrip", "rstrip", "upcase", "downcase", "capitalize",
                "swapcase", "center", "ljust", "rjust", "sub", "gsub", "scan", "split", "partition",

                # Array methods
                "each", "map", "collect", "select", "find", "detect", "reject", "inject", "reduce",
                "sort", "sort_by", "push", "pop", "shift", "unshift", "first", "last", "sample",
                "join", "concat", "flatten", "compact", "uniq", "zip", "transpose",

                # Hash methods
                "each_pair", "each_key", "each_value", "keys", "values", "fetch", "store",
                "merge", "merge!", "update", "delete", "delete_if", "keep_if", "select", "reject",

                # Rails/ActiveRecord
                "find", "find_by", "where", "order", "limit", "offset", "joins", "includes",
                "group", "having", "pluck", "count", "sum", "average", "maximum", "minimum",
                "create", "update", "destroy", "save", "save!", "valid?", "invalid?", "errors",

                # Rails/ActionController
                "params", "render", "redirect_to", "respond_to", "before_action", "after_action",
                "around_action", "skip_before_action", "skip_after_action", "flash", "session",

                # Rails/ActiveSupport
                "present?", "blank?", "presence", "try", "in?", "to_param", "to_query", "to_sentence",
                "pluralize", "singularize", "camelize", "underscore", "dasherize", "parameterize",

                # Common methods
                "tap", "then", "yield_self", "with_index", "with_object", "times", "upto", "downto",
                "step", "cycle", "lazy", "grep", "grep_v", "take", "take_while", "drop", "drop_while"
            ],
            # Add more languages as needed
        }

        # Check if the function is in the library function list for the language
        if '.' in func_name:
            # For method calls like obj.method(), extract just the method name
            method_name = func_name.split('.')[-1]
            return method_name in library_functions.get(language, [])
        else:
            return func_name in library_functions.get(language, [])

def generate_hybrid_knowledge_graph(repo_dir: str, verbose: bool = False) -> nx.MultiDiGraph:
    """
    Generate a hybrid knowledge graph for a repository.

    Args:
        repo_dir: Root directory of the repository
        verbose: Enable verbose logging

    Returns:
        NetworkX MultiDiGraph representing the repository
    """
    generator = HybridKnowledgeGraph(repo_dir, verbose)
    return generator.generate_graph()

def graph_to_dataframes(G: nx.MultiDiGraph) -> pd.DataFrame:
    """
    Convert NetworkX MultiDiGraph to nodes dataframe.

    Args:
        G: NetworkX MultiDiGraph

    Returns:
        DataFrame containing node information
    """
    # Create nodes dataframe
    nodes_data = []
    for node_id, attrs in G.nodes(data=True):
        # Skip nodes with empty or invalid IDs
        if not node_id or node_id == ":":
            continue

        node_data = {
            'node_id': node_id,
            'name': attrs.get('name', ''),
            'file_path': attrs.get('file_path', ''),
            'module_path': attrs.get('module_path', ''),
            'start_line': attrs.get('start_line', -1),
            'end_line': attrs.get('end_line', -1),
            'signature': attrs.get('signature', ''),
            'containing_class': attrs.get('containing_class', ''),
            'is_method': attrs.get('is_method', False),
            'is_static': attrs.get('is_static', False),
            'is_async': attrs.get('is_async', False),
            'calls': attrs.get('calls', []),
            'call_contexts': attrs.get('call_contexts', []),  # Add call contexts
            'description': '',  # Empty description to be filled later
            'text': attrs.get('text', '')
        }
        nodes_data.append(node_data)

    nodes_df = pd.DataFrame(nodes_data)

    # We're no longer creating edges dataframe in our node-only approach
    return nodes_df

#---------
import json
# from bracket_core.irl import hybrid_graph_to_dataframes
from bracket_core.hybrid_kg import generate_hybrid_knowledge_graph, graph_to_dataframes as hybrid_graph_to_dataframes
def save_graph_as_parquet(nx_graph, output_path):
    """
    Save the graph nodes as parquet file.

    Args:
        nx_graph: NetworkX graph
        output_path: Output path prefix

    Returns:
        DataFrame containing node information
    """

    # Convert graph to dataframe using the hybrid implementation
    nodes_df = hybrid_graph_to_dataframes(nx_graph)

    # Function to convert mixed types to strings
    def convert_mixed_types(df):
        for col in df.columns:
            if df[col].dtype == 'object':
                df[col] = df[col].apply(
                    lambda x: json.dumps(x) if isinstance(x, (dict, list)) else str(x) if x is not None else None
                )
        return df

    # Convert all object columns to consistent string format
    nodes_df = convert_mixed_types(nodes_df)

    # Save to parquet
    nodes_df.to_parquet(f"{output_path}_nodes.parquet")

    return nodes_df


if __name__ == "__main__":
    nx_graph = generate_hybrid_knowledge_graph(repo_dir="/Users/<USER>/work/startup/godzilla/test/gitlab/app/")

    # Normalize node attributes if needed
    for node_id, data in list(nx_graph.nodes(data=True)):
        # Ensure all expected fields are present
        if 'node_id' not in data:
            data['node_id'] = node_id

        # Add id field for compatibility
        if 'id' not in data:
            data['id'] = node_id

    logger.info(f"Knowledge Graph created with {len(nx_graph.nodes)} nodes")
    kg_output_path = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/ruby/"
    nodes_df = save_graph_as_parquet(nx_graph, output_path = kg_output_path)


