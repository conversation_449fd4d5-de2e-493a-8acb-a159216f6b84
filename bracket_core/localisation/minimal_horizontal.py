#!/usr/bin/env python3
"""
Minimal version of horizontal localisation
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

@dataclass
class DomainNode:
    """Represents a node in the domain hierarchy."""
    name: str
    full_path: str
    level: int
    parent: Optional["DomainNode"] = None
    children: List["DomainNode"] = field(default_factory=list)
    functions: List[str] = field(default_factory=list)
    mermaid_diagram: Optional[str] = None
    relevance_score: float = 0.0
    is_evaluated: bool = False
    is_relevant: bool = False

class MinimalHorizontalLocalisation:
    """Minimal version of horizontal localisation."""
    
    def __init__(self, domain_taxonomy_json_path: str):
        """Initialize the minimal horizontal localisation."""
        self.domain_taxonomy_json_path = domain_taxonomy_json_path
        self.domain_taxonomy = None
        self.domain_hierarchy = None
        self.max_level = 0
    
    def load_domain_taxonomy(self) -> bool:
        """
        Load the domain taxonomy JSON file.
        
        Returns:
            True if loading was successful, False otherwise
        """
        try:
            if not os.path.exists(self.domain_taxonomy_json_path):
                logger.error(f"Domain taxonomy JSON file not found: {self.domain_taxonomy_json_path}")
                return False
            
            with open(self.domain_taxonomy_json_path, 'r') as f:
                self.domain_taxonomy = json.load(f)
            
            logger.info(f"Domain taxonomy loaded from: {self.domain_taxonomy_json_path}")
            return True
        
        except Exception as e:
            logger.error(f"Error loading domain taxonomy: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False
    
    def build_domain_hierarchy(self) -> Optional[DomainNode]:
        """
        Build a hierarchical representation of the domain taxonomy.
        
        Returns:
            Root node of the domain hierarchy, or None if building failed
        """
        if not self.domain_taxonomy:
            logger.error("Domain taxonomy not loaded")
            return None
        
        try:
            # Create root node
            root = DomainNode(
                name="Root",
                full_path="Root",
                level=0,
                parent=None
            )
            
            # Track maximum level
            self.max_level = 0
            
            # Process children recursively
            def process_domain(domain_data, parent_node, current_level):
                name = domain_data.get('name', '')
                if not name:
                    return
                
                # Update max level if needed
                if current_level > self.max_level:
                    self.max_level = current_level
                
                # Create full path
                full_path = parent_node.full_path + " -> " + name if parent_node.name != "Root" else name
                
                # Create node
                node = DomainNode(
                    name=name,
                    full_path=full_path,
                    level=current_level,
                    parent=parent_node,
                    functions=domain_data.get('functions', []),
                    mermaid_diagram=domain_data.get('diagram', None)
                )
                
                # Add to parent's children
                parent_node.children.append(node)
                
                # Process children recursively
                for child in domain_data.get('children', []):
                    process_domain(child, node, current_level + 1)
            
            # Start processing from the root's children
            for child in self.domain_taxonomy.get('children', []):
                process_domain(child, root, 1)
            
            logger.info(f"Built domain hierarchy with max level: {self.max_level}")
            self.domain_hierarchy = root
            return root
        
        except Exception as e:
            logger.error(f"Error building domain hierarchy: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return None
    
    def get_domains_at_level(self, level: int) -> List[DomainNode]:
        """
        Get all domains at a specific level in the hierarchy.
        
        Args:
            level: Level to get domains for (1-based, where 1 is the top level)
        
        Returns:
            List of DomainNode objects at the specified level
        """
        if not self.domain_hierarchy:
            logger.error("Domain hierarchy not built")
            return []
        
        if level < 1 or level > self.max_level:
            logger.error(f"Invalid level: {level}. Valid range is 1-{self.max_level}")
            return []
        
        domains_at_level = []
        
        def collect_domains(node, current_level):
            if current_level == level:
                domains_at_level.append(node)
                return
            
            for child in node.children:
                collect_domains(child, current_level + 1)
        
        # Start collection from the root (level 0)
        for child in self.domain_hierarchy.children:
            collect_domains(child, 1)
        
        logger.info(f"Found {len(domains_at_level)} domains at level {level}")
        return domains_at_level

def main():
    """Run the minimal horizontal localisation."""
    # Default domain taxonomy path
    domain_taxonomy_json_path = "./experiments/gitlab/domain_taxonomy.json"
    
    # Create minimal horizontal localisation
    localisation = MinimalHorizontalLocalisation(domain_taxonomy_json_path)
    
    # Load domain taxonomy
    if not localisation.load_domain_taxonomy():
        logger.error("Failed to load domain taxonomy")
        return 1
    
    # Build domain hierarchy
    if not localisation.build_domain_hierarchy():
        logger.error("Failed to build domain hierarchy")
        return 1
    
    # Get domains at each level
    for level in range(1, localisation.max_level + 1):
        domains = localisation.get_domains_at_level(level)
        logger.info(f"Level {level}: {len(domains)} domains")
        
        # Print first 5 domains at this level
        for i, domain in enumerate(domains[:5]):
            logger.info(f"  Domain {i+1}: {domain.name} ({domain.full_path})")
            logger.info(f"    Functions: {len(domain.functions)}")
            logger.info(f"    Children: {len(domain.children)}")
    
    logger.info("Minimal horizontal localisation completed successfully")
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(main())
