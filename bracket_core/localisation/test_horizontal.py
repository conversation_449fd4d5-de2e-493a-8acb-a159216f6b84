#!/usr/bin/env python3
"""
Test script for horizontal localisation
"""

import os
import json
import logging
import asyncio
import aiohttp
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Import API key management
from bracket_core.llm.api_keys import get_openai_api_key

@dataclass
class DomainNode:
    """Represents a node in the domain hierarchy."""
    name: str
    full_path: str
    level: int
    parent: Optional["DomainNode"] = None
    children: List["DomainNode"] = field(default_factory=list)
    functions: List[str] = field(default_factory=list)
    mermaid_diagram: Optional[str] = None
    relevance_score: float = 0.0
    is_evaluated: bool = False
    is_relevant: bool = False

async def test_openai_api():
    """Test the OpenAI API with a simple request."""
    try:
        api_key = get_openai_api_key()
        
        # Create a session for API requests
        async with aiohttp.ClientSession() as session:
            # Prepare headers
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {api_key}"
            }
            
            # Prepare request JSON
            request_json = {
                "model": "gpt-4o-mini",
                "messages": [
                    {"role": "system", "content": "You are a helpful assistant."},
                    {"role": "user", "content": "Hello, how are you?"}
                ],
                "temperature": 0.1,
                "response_format": {"type": "json_object"}
            }
            
            # Make API request
            logger.info("Making API request to OpenAI...")
            async with session.post(
                "https://api.openai.com/v1/chat/completions",
                headers=headers,
                json=request_json,
                timeout=aiohttp.ClientTimeout(total=60)
            ) as response:
                response_json = await response.json()
                
                if response.status == 200:
                    # Extract result from response
                    try:
                        content = response_json["choices"][0]["message"]["content"]
                        result = json.loads(content)
                        logger.info(f"API response: {result}")
                        return True
                    except (KeyError, json.JSONDecodeError) as e:
                        logger.error(f"Error parsing OpenAI response: {e}")
                        logger.error(f"Response JSON: {response_json}")
                        return False
                else:
                    # Handle API error
                    error_message = response_json.get("error", {}).get("message", "Unknown error")
                    logger.error(f"OpenAI API error: {error_message}")
                    logger.error(f"Response JSON: {response_json}")
                    return False
                
    except Exception as e:
        logger.error(f"Error testing OpenAI API: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

async def test_domain_evaluation():
    """Test domain evaluation with a simple domain."""
    try:
        api_key = get_openai_api_key()
        
        # Create a test domain
        domain = DomainNode(
            name="Test Domain",
            full_path="Root -> Test Domain",
            level=1
        )
        
        # Create a session for API requests
        async with aiohttp.ClientSession() as session:
            # Prepare system message
            system_message = """
            You will be given a user query and information about a specific domain in the codebase, including:
            1. The hierarchical path of the domain
            
            Your task is to evaluate whether this domain is DIRECTLY or TANGENTIALLY relevant to the user's query.
            Focus on the domain and how it fits into the overall codebase structure.
            
            RETURN YOUR RESPONSE IN THE FOLLOWING JSON FORMAT:
            {
              "is_relevant": true/false,
              "relevance_score": 0-10,  // 0 = completely irrelevant, 10 = highly relevant
              "reasoning": "Your step-by-step reasoning about why this domain is or is not relevant"
            }
            """
            
            # Prepare user message
            user_message = """USER QUERY: How does the system handle error logging?
            
            DOMAIN PATH: Root -> Test Domain
            """
            
            # Prepare headers
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {api_key}"
            }
            
            # Prepare request JSON
            request_json = {
                "model": "gpt-4o-mini",
                "messages": [
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message}
                ],
                "temperature": 0.1,
                "response_format": {"type": "json_object"}
            }
            
            # Make API request
            logger.info("Making API request to OpenAI for domain evaluation...")
            async with session.post(
                "https://api.openai.com/v1/chat/completions",
                headers=headers,
                json=request_json,
                timeout=aiohttp.ClientTimeout(total=60)
            ) as response:
                response_json = await response.json()
                
                if response.status == 200:
                    # Extract result from response
                    try:
                        content = response_json["choices"][0]["message"]["content"]
                        result = json.loads(content)
                        logger.info(f"Domain evaluation result: {result}")
                        
                        # Update domain with evaluation result
                        domain.is_evaluated = True
                        domain.is_relevant = result.get("is_relevant", False)
                        domain.relevance_score = result.get("relevance_score", 0)
                        
                        logger.info(f"Updated domain: {domain}")
                        return True
                    except (KeyError, json.JSONDecodeError) as e:
                        logger.error(f"Error parsing OpenAI response: {e}")
                        logger.error(f"Response JSON: {response_json}")
                        return False
                else:
                    # Handle API error
                    error_message = response_json.get("error", {}).get("message", "Unknown error")
                    logger.error(f"OpenAI API error: {error_message}")
                    logger.error(f"Response JSON: {response_json}")
                    return False
                
    except Exception as e:
        logger.error(f"Error testing domain evaluation: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

async def main():
    """Run the tests."""
    logger.info("Starting tests...")
    
    # Test OpenAI API
    logger.info("Testing OpenAI API...")
    api_test_result = await test_openai_api()
    logger.info(f"OpenAI API test {'succeeded' if api_test_result else 'failed'}")
    
    # Test domain evaluation
    logger.info("Testing domain evaluation...")
    domain_test_result = await test_domain_evaluation()
    logger.info(f"Domain evaluation test {'succeeded' if domain_test_result else 'failed'}")
    
    logger.info("Tests completed.")

if __name__ == "__main__":
    asyncio.run(main())
