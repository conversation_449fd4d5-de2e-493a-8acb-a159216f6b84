"""
Global Localisation

This module provides functionality to locate relevant files in a codebase
based on a user query by leveraging domain taxonomy information.

It reads the domain_taxonomy.json file generated by generate_domain_taxonomy.py,
extracts traces and their mermaid diagrams, and uses LLM to evaluate the relevance
of each trace to the user query.

The module now works with file-level granularity, where leaf-level domains contain
lists of files rather than functions. However, it maintains backward compatibility
with function-level data where needed.
"""

import os
import json
import logging
import asyncio
import aiohttp
import time
import uuid
import pandas as pd
import re
from datetime import datetime
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, field
from collections import defaultdict

# Import LLM clients and API key management
from bracket_core.llm.api_keys import get_openai_api_key, get_openrouter_api_key
from bracket_core.llm.get_client import get_openrouter_client

# Import tiktoken for accurate token counting
try:
    import tiktoken
    TIKTOKEN_AVAILABLE = True
except ImportError:
    TIKTOKEN_AVAILABLE = False
    logging.warning("tiktoken not available, falling back to approximate token counting")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class TraceInfo:
    """Information about a domain trace."""
    trace_str: str
    mermaid_diagrams: List[str]
    full_path: str
    files: List[str] = field(default_factory=list)  # List of files in the domain
    functions: List[str] = field(default_factory=list)  # For backward compatibility
    file_contents: Dict[str, str] = field(default_factory=dict)  # Map of file path to content

@dataclass
class APIRequest:
    """API request for evaluating trace relevance."""
    task_id: int
    trace_info: TraceInfo
    request_json: Dict[str, Any]
    attempts_left: int = 3

    async def call_api(
        self,
        session: aiohttp.ClientSession,
        request_url: str,
        request_header: Dict[str, str],
        retry_queue: asyncio.Queue,
        status_tracker: 'StatusTracker',
        results: Dict[str, Dict[str, Any]],
        openrouter_client=None,
    ) -> None:
        """
        Call the API to evaluate trace relevance.

        Args:
            session: aiohttp ClientSession for making HTTP requests
            request_url: URL for the API request
            request_header: Headers for the API request
            retry_queue: Queue for retrying failed requests
            status_tracker: Tracker for API request status
            results: Dictionary to store results
            openrouter_client: Optional OpenRouter client for direct API calls
        """
        try:
            # Check token count of the request payload
            token_count = 0

            # Extract and count tokens in messages
            for message in self.request_json.get("messages", []):
                content = message.get("content", "")
                if content:
                    token_count += estimate_token_count(content)

            # Check if token count exceeds 45K threshold
            if token_count > 45000 and request_url != "openrouter_client":
                logger.warning(f"Token count {token_count} exceeds 45K threshold for trace {self.trace_info.trace_str}. Switching to OpenRouter with Gemini model.")

                # Try to get OpenRouter client for Gemini
                try:
                    # Get OpenRouter API key
                    openrouter_api_key = get_openrouter_api_key()
                    if not openrouter_api_key:
                        logger.error("No OpenRouter API key available for fallback")
                    else:
                        # Create a temporary OpenRouter client with Gemini model
                        temp_openrouter_client = get_openrouter_client(
                            api_key=openrouter_api_key,
                            model="google/gemini-2.0-flash-001",
                            max_tokens=15000,
                            temperature=0.1,
                            base_url="https://openrouter.ai/api/v1"
                        )

                        # Use this client instead
                        openrouter_client = temp_openrouter_client
                        request_url = "openrouter_client"
                        logger.info(f"Successfully created OpenRouter client with Gemini model for high token count request")
                except Exception as e:
                    logger.error(f"Failed to create OpenRouter client for high token count fallback: {e}")
                    # Continue with original request if OpenRouter setup fails

            # Check if we're using OpenRouter client
            if request_url == "openrouter_client" and openrouter_client is not None:
                # Use OpenRouter client directly
                try:
                    # Extract messages from the request JSON
                    messages = self.request_json.get("messages", [])
                    system_prompt = None
                    user_prompt = None

                    # Extract system and user prompts
                    for message in messages:
                        if message.get("role") == "system":
                            system_prompt = message.get("content")
                        elif message.get("role") == "user":
                            user_prompt = message.get("content")

                    if not user_prompt:
                        logger.error(f"No user prompt found in request for trace: {self.trace_info.trace_str}")
                        status_tracker.num_tasks_failed += 1
                        status_tracker.num_tasks_in_progress -= 1
                        return

                    # Call OpenRouter API with retry logic
                    max_retries = 3
                    retry_delay = 5  # Start with 5 seconds delay for OpenRouter

                    for retry in range(max_retries):
                        try:
                            # Generate JSON response using OpenRouter client
                            response_json = await openrouter_client.generate_json(
                                prompt=user_prompt,
                                system_prompt=system_prompt,
                            )

                            # Store the result
                            results[self.trace_info.trace_str] = response_json

                            # Update status tracker
                            status_tracker.num_tasks_succeeded += 1
                            status_tracker.num_tasks_in_progress -= 1
                            return

                        except Exception as e:
                            logger.warning(f"OpenRouter API error for trace {self.trace_info.trace_str} (attempt {retry+1}/{max_retries}): {str(e)}")
                            if retry < max_retries - 1:
                                # Check if this is a rate limit error
                                if "429" in str(e) or "quota exceeded" in str(e).lower() or "rate limit" in str(e).lower():
                                    wait_time = retry_delay * (2 ** retry)  # Exponential backoff
                                    logger.warning(f"Rate limit exceeded, retrying in {wait_time} seconds")
                                    await asyncio.sleep(wait_time)
                                else:
                                    # For other errors, wait a shorter time
                                    await asyncio.sleep(2)
                            else:
                                # If we've exhausted all retries, put the request back in the queue if attempts left
                                self.attempts_left -= 1
                                if self.attempts_left > 0:
                                    logger.warning(f"Retrying OpenRouter API request for trace {self.trace_info.trace_str} (attempts left: {self.attempts_left})")
                                    await retry_queue.put(self)
                                else:
                                    logger.error(f"OpenRouter API request for trace {self.trace_info.trace_str} failed after all retries")
                                    status_tracker.num_tasks_failed += 1
                                    status_tracker.num_tasks_in_progress -= 1
                                return

                except Exception as e:
                    logger.error(f"Unexpected error in OpenRouter API request for trace {self.trace_info.trace_str}: {str(e)}")
                    status_tracker.num_tasks_failed += 1
                    status_tracker.num_tasks_in_progress -= 1
                    return
            else:
                # Use direct HTTP request for OpenAI
                # Implement exponential backoff for retries with improved connection handling
                max_retries = 3
                retry_delay = 1
                retry_attempt = 0

                while retry_attempt < max_retries:
                    try:
                        # Use a longer timeout and TCP connection pooling settings
                        timeout = aiohttp.ClientTimeout(total=640, connect=130, sock_connect=130, sock_read=480)

                        async with session.post(
                            request_url,
                            headers=request_header,
                            json=self.request_json,
                            timeout=timeout,
                        ) as response:
                            if response.status == 200:
                                response_json = await response.json()

                                # Extract the content from the response
                                if "choices" in response_json and len(response_json["choices"]) > 0:
                                    content = response_json["choices"][0]["message"]["content"]

                                    # Parse the content as JSON if possible
                                    try:
                                        content_json = json.loads(content)
                                        results[self.trace_info.trace_str] = content_json
                                    except json.JSONDecodeError:
                                        # If not valid JSON, store as is
                                        results[self.trace_info.trace_str] = {"raw_response": content}

                                    # Update status tracker
                                    status_tracker.num_tasks_succeeded += 1
                                    status_tracker.num_tasks_in_progress -= 1
                                    return
                                else:
                                    logger.warning(f"No content in API response for trace: {self.trace_info.trace_str}")

                            elif response.status == 429:
                                # Rate limit error, retry after a delay
                                retry_attempt += 1
                                wait_time = retry_delay * (2 ** retry_attempt)  # Exponential backoff
                                logger.warning(f"Rate limit error for trace {self.trace_info.trace_str}, retrying in {wait_time}s")
                                await asyncio.sleep(wait_time)
                                continue

                            elif response.status == 500 or response.status == 502 or response.status == 503:
                                # Server error, retry after a delay
                                retry_attempt += 1
                                wait_time = retry_delay * (2 ** retry_attempt)  # Exponential backoff
                                logger.warning(f"Server error {response.status} for trace {self.trace_info.trace_str}, retrying in {wait_time}s")
                                await asyncio.sleep(wait_time)
                                continue

                            else:
                                # Other error, log and retry
                                error_text = await response.text()
                                logger.error(f"API error {response.status} for trace {self.trace_info.trace_str}: {error_text}")
                                retry_attempt += 1
                                wait_time = retry_delay * (2 ** retry_attempt)  # Exponential backoff
                                await asyncio.sleep(wait_time)
                                continue

                    except (aiohttp.ClientError, asyncio.TimeoutError) as e:
                        # Connection error, retry after a delay
                        retry_attempt += 1
                        wait_time = retry_delay * (2 ** retry_attempt)  # Exponential backoff
                        logger.warning(f"Connection error for trace {self.trace_info.trace_str}: {str(e)}, retrying in {wait_time}s")
                        await asyncio.sleep(wait_time)
                        continue

                # If we've exhausted all retries, put the request back in the queue if attempts left
                self.attempts_left -= 1
                if self.attempts_left > 0:
                    logger.warning(f"Retrying API request for trace {self.trace_info.trace_str} (attempts left: {self.attempts_left})")
                    await retry_queue.put(self)
                else:
                    logger.error(f"API request for trace {self.trace_info.trace_str} failed after all retries")
                    status_tracker.num_tasks_failed += 1
                    status_tracker.num_tasks_in_progress -= 1

        except Exception as e:
            logger.error(f"Unexpected error in API request for trace {self.trace_info.trace_str}: {str(e)}")
            status_tracker.num_tasks_failed += 1
            status_tracker.num_tasks_in_progress -= 1

@dataclass
class StatusTracker:
    """Tracker for API request status."""
    num_tasks_started: int = 0
    num_tasks_in_progress: int = 0
    num_tasks_succeeded: int = 0
    num_tasks_failed: int = 0

@dataclass
class DomainRelevanceResult:
    """Result of domain relevance evaluation."""
    is_relevant: bool = False
    relevance_score: int = 0  # 0-10 scale
    trace_str: str = ""
    full_path: str = ""
    functions: List[str] = field(default_factory=list)
    mermaid_diagrams: List[str] = field(default_factory=list)

@dataclass
class FileRelevanceResult:
    """Result of file or function relevance evaluation."""
    file_path: str  # Primary identifier for files
    relevance_score: int  # 0-10 scale
    domain_trace: str = ""
    file_content: str = ""  # Content of the file
    start_line: int = 0
    end_line: int = 0
    function_name: str = ""  # For backward compatibility with function-level data
    function_text: str = ""  # For backward compatibility with function-level data
    is_file: bool = True  # Whether this is a file (True) or a function (False)

# Alias for backward compatibility
FunctionRelevanceResult = FileRelevanceResult

@dataclass
class ThirdPassResult:
    """Result of the third pass filtering using a larger model."""
    success: bool = False
    filtered_functions: List[Dict[str, Any]] = field(default_factory=list)
    error_message: Optional[str] = None
    model_used: str = ""
    timestamp: str = ""
    reasoning: str = ""  # The model's reasoning for selecting these functions

@dataclass
class GlobalLocalisationResult:
    """Result of global localisation."""
    success: bool = False
    relevant_files: List[Dict[str, Any]] = field(default_factory=list)  # Primary field for file-level results
    error_message: Optional[str] = None
    raw_results: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    query: str = ""
    output_dir: Optional[str] = None
    relevant_domains: List[DomainRelevanceResult] = field(default_factory=list)
    third_pass_result: Optional[ThirdPassResult] = None

    @property
    def relevant_functions(self) -> List[Dict[str, Any]]:
        """Alias for relevant_files to maintain backward compatibility."""
        return self.relevant_files

def count_tokens_with_tiktoken(text: str, model: str = "gpt-4o-mini") -> int:
    """
    Count tokens accurately using tiktoken.

    Args:
        text: The text to count tokens for
        model: The model to use for token counting

    Returns:
        Accurate token count
    """
    # Only attempt to use tiktoken if it's available
    if not TIKTOKEN_AVAILABLE:
        return len(text) // 4

    # Import locally to avoid unbound variable errors
    import tiktoken

    try:
        if model.startswith("gpt"):
            encoding = tiktoken.encoding_for_model(model)
        else:
            # Default to cl100k_base for non-OpenAI models
            encoding = tiktoken.get_encoding("cl100k_base")
        return len(encoding.encode(text))
    except Exception as e:
        logger.warning(f"Error counting tokens with tiktoken: {e}. Using approximate count.")
        # Fallback to approximate count
        return len(text) // 4

def estimate_token_count(text: str) -> int:
    """
    Estimate token count for a text string.
    Uses tiktoken if available, otherwise falls back to a simple heuristic.

    Args:
        text: The text to estimate token count for

    Returns:
        Estimated token count
    """
    if TIKTOKEN_AVAILABLE:
        return count_tokens_with_tiktoken(text)
    else:
        # Simple heuristic: 1 token ≈ 4 characters for English text
        return len(text) // 4

class GlobalLocalisation:
    """
    Global Localisation for finding relevant files in a codebase.

    This class reads the domain_taxonomy.json file, extracts traces and their mermaid diagrams,
    and uses LLM to evaluate the relevance of each trace to the user query.

    The class now works with file-level granularity, where leaf-level domains contain
    lists of files rather than functions. However, it maintains backward compatibility
    with function-level data where needed.
    """

    def __init__(
        self,
        domain_taxonomy_json_path: str,
        api_key: Optional[str] = None,
        model: str = "gpt-4o-mini",  # Model for first pass (domain relevance)
        second_pass_model: Optional[str] = None,  # Model for second pass (function relevance)
        max_requests_per_minute: float = 2500,
        max_tokens_per_minute: float = 20000000,
        temperature: float = 0.1,
        output_dir: Optional[str] = None,
        semantic_documented_fns_path: Optional[str] = None,
        use_two_pass_approach: bool = True,
        max_tokens_per_domain: int = 70000,
        include_mermaid_in_second_pass: bool = False,
        include_full_trace_mermaid: bool = False,
        # OpenRouter parameters
        use_openrouter: bool = False,
        openrouter_api_key: Optional[str] = None,
        openrouter_model: str = "google/gemini-2.0-flash-001",  # OpenRouter model for first pass
        second_pass_use_openrouter: bool = False,  # Whether to use OpenRouter for second pass
        second_pass_openrouter_model: str = "google/gemini-2.0-flash-001",  # OpenRouter model for second pass
        openrouter_base_url: str = "https://openrouter.ai/api/v1",
        # Third pass parameters
        use_third_pass: bool = False,
        third_pass_model: str = "gpt-4o",  # Default to a larger model for third pass
        third_pass_use_openrouter: bool = False,
        third_pass_openrouter_model: str = "anthropic/claude-3-opus-20240229",
        third_pass_max_tokens: int = 4000,
        # Parallelization parameters
        num_workers: int = 10,  # Number of concurrent workers
        batch_size: int = 50,   # Number of requests per batch
    ):
        """
        Initialize the global localisation.

        Args:
            domain_taxonomy_json_path: Path to the domain taxonomy JSON file
            api_key: OpenAI API key (if None, will try to get from environment)
            model: OpenAI model to use for the first pass (domain relevance)
            second_pass_model: OpenAI model to use for the second pass (function relevance); if None, uses the same model as the first pass
            max_requests_per_minute: Rate limit for API requests
            max_tokens_per_minute: Token rate limit for API
            temperature: Temperature setting for the model (lower = more deterministic)
            output_dir: Directory to save LLM outputs and analysis
            semantic_documented_fns_path: Optional path to the semantic documented functions parquet file
            use_two_pass_approach: Whether to use the two-pass approach for code localization
            max_tokens_per_domain: Maximum number of tokens to include per domain in the second pass
            include_mermaid_in_second_pass: Whether to include mermaid diagrams in the second pass
            include_full_trace_mermaid: If True, includes all mermaid diagrams in the trace; if False, only includes the leaf node diagram
            use_openrouter: Whether to use OpenRouter instead of OpenAI for the first pass
            openrouter_api_key: OpenRouter API key (if None, will try to get from environment)
            openrouter_model: OpenRouter model to use for the first pass
            second_pass_use_openrouter: Whether to use OpenRouter for the second pass
            second_pass_openrouter_model: OpenRouter model to use for the second pass
            openrouter_base_url: Base URL for OpenRouter API
            use_third_pass: Whether to use the third pass for detailed analysis of filtered functions
            third_pass_model: OpenAI model to use for the third pass
            third_pass_use_openrouter: Whether to use OpenRouter for the third pass
            third_pass_openrouter_model: OpenRouter model to use for the third pass
            third_pass_max_tokens: Maximum number of tokens to generate in the third pass response
            num_workers: Number of concurrent workers for parallelized API calls
            batch_size: Number of requests per batch for parallelized processing
        """
        self.domain_taxonomy_json_path = domain_taxonomy_json_path
        self.api_key = api_key or get_openai_api_key()
        self.model = model  # First pass model
        self.second_pass_model = second_pass_model or model  # Use first pass model if second pass model not specified
        self.max_requests_per_minute = max_requests_per_minute
        self.max_tokens_per_minute = max_tokens_per_minute
        self.temperature = temperature
        self.use_two_pass_approach = use_two_pass_approach
        self.max_tokens_per_domain = max_tokens_per_domain
        self.semantic_documented_fns_path = semantic_documented_fns_path
        self.semantic_documented_fns_df = None
        self.include_mermaid_in_second_pass = include_mermaid_in_second_pass
        self.include_full_trace_mermaid = include_full_trace_mermaid

        # OpenRouter parameters for first pass
        self.use_openrouter = use_openrouter
        self.openrouter_api_key = openrouter_api_key
        self.openrouter_model = openrouter_model
        self.openrouter_base_url = openrouter_base_url

        # OpenRouter parameters for second pass
        self.second_pass_use_openrouter = second_pass_use_openrouter
        self.second_pass_openrouter_model = second_pass_openrouter_model
        self.second_pass_openrouter_client = None

        # Third pass parameters
        self.use_third_pass = use_third_pass
        self.third_pass_model = third_pass_model
        self.third_pass_use_openrouter = third_pass_use_openrouter
        self.third_pass_openrouter_model = third_pass_openrouter_model
        self.third_pass_max_tokens = third_pass_max_tokens
        self.third_pass_openrouter_client = None

        # Parallelization parameters
        self.num_workers = num_workers
        self.batch_size = batch_size

        # Initialize OpenRouter client for first pass if needed
        self.openrouter_client = None
        if self.use_openrouter:
            try:
                self.openrouter_client = get_openrouter_client(
                    api_key=openrouter_api_key,
                    model=openrouter_model,
                    max_tokens=15000,  # Allow for longer outputs
                    temperature=temperature,
                    base_url=openrouter_base_url
                )
                logger.info(f"Initialized OpenRouter client for first pass with model: {openrouter_model}")
            except Exception as e:
                logger.error(f"Error initializing OpenRouter client for first pass: {e}")
                logger.warning("OpenRouter will not be used for first pass due to initialization error")
                self.use_openrouter = False

        # Initialize OpenRouter client for second pass if needed
        if self.second_pass_use_openrouter:
            try:
                self.second_pass_openrouter_client = get_openrouter_client(
                    api_key=openrouter_api_key,  # Reuse the same API key
                    model=second_pass_openrouter_model,
                    max_tokens=15000,  # Allow for longer outputs
                    temperature=temperature,
                    base_url=openrouter_base_url
                )
                logger.info(f"Initialized OpenRouter client for second pass with model: {second_pass_openrouter_model}")
            except Exception as e:
                logger.error(f"Error initializing OpenRouter client for second pass: {e}")
                logger.warning("OpenRouter will not be used for second pass due to initialization error")
                self.second_pass_use_openrouter = False

        # Initialize OpenRouter client for third pass if needed
        if self.third_pass_use_openrouter:
            try:
                self.third_pass_openrouter_client = get_openrouter_client(
                    api_key=openrouter_api_key,  # Reuse the same API key
                    model=third_pass_openrouter_model,
                    max_tokens=third_pass_max_tokens,
                    temperature=temperature,
                    base_url=openrouter_base_url
                )
                logger.info(f"Initialized OpenRouter client for third pass with model: {third_pass_openrouter_model}")
            except Exception as e:
                logger.error(f"Error initializing OpenRouter client for third pass: {e}")
                logger.warning("OpenRouter will not be used for third pass due to initialization error")
                self.third_pass_use_openrouter = False

        # Initialize domain taxonomy data
        self.domain_taxonomy = None
        self.traces = []

        # Set output directory for saving LLM outputs and analysis
        self.output_dir = output_dir or os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            "localisation", "qa_data"
        )

        # Ensure output directory exists
        os.makedirs(self.output_dir, exist_ok=True)

    def load_semantic_documented_functions(self) -> bool:
        """
        Load the semantic documented functions from the parquet file.

        Returns:
            True if loading was successful, False otherwise
        """
        if not self.semantic_documented_fns_path:
            logger.info("Semantic documented functions path not provided, will proceed without them")
            return False

        try:
            if not os.path.exists(self.semantic_documented_fns_path):
                logger.warning(f"Semantic documented functions file not found: {self.semantic_documented_fns_path}")
                return False

            self.semantic_documented_fns_df = pd.read_parquet(self.semantic_documented_fns_path)
            logger.info(f"Loaded {len(self.semantic_documented_fns_df)} semantic documented functions from: {self.semantic_documented_fns_path}")
            return True

        except Exception as e:
            logger.warning(f"Error loading semantic documented functions: {str(e)}")
            return False

    def load_domain_taxonomy(self) -> bool:
        """
        Load the domain taxonomy JSON file.

        Returns:
            True if loading was successful, False otherwise
        """
        try:
            if not os.path.exists(self.domain_taxonomy_json_path):
                logger.error(f"Domain taxonomy JSON file not found: {self.domain_taxonomy_json_path}")
                return False

            with open(self.domain_taxonomy_json_path, 'r') as f:
                self.domain_taxonomy = json.load(f)

            logger.info(f"Domain taxonomy loaded from: {self.domain_taxonomy_json_path}")
            return True

        except Exception as e:
            logger.error(f"Error loading domain taxonomy: {str(e)}")
            return False

    def extract_traces(self) -> List[TraceInfo]:
        """
        Extract traces and their mermaid diagrams from the domain taxonomy.

        Returns:
            List of TraceInfo objects
        """
        if not self.domain_taxonomy:
            logger.error("Domain taxonomy not loaded")
            return []

        traces = []

        def traverse_domain(domain, path=None, diagrams=None):
            """Recursively traverse the domain hierarchy to extract traces."""
            if path is None:
                path = []
            if diagrams is None:
                diagrams = []

            current_path = path + [domain.get('name', '')]
            # current_diagrams = diagrams + [domain.get('diagram', '')] if 'diagram' in domain else diagrams
            current_diagrams = diagrams

            # If this is a leaf domain with files or functions, create a trace
            if ('files' in domain and domain['files']) or ('functions' in domain and domain['functions']):
                current_diagrams = diagrams + [domain.get('diagram', '')] if 'diagram' in domain else diagrams
                trace_str = ' -> '.join(current_path)
                full_path = domain.get('full_path', trace_str)

                # Create TraceInfo with appropriate fields
                trace_info = TraceInfo(
                    trace_str=trace_str,
                    mermaid_diagrams=current_diagrams,
                    full_path=full_path
                )

                # Add files if present
                if 'files' in domain and domain['files']:
                    trace_info.files = domain['files']

                    # Add file contents if present
                    if 'file_contents' in domain and domain['file_contents']:
                        trace_info.file_contents = domain['file_contents']

                # Add functions for backward compatibility
                if 'functions' in domain and domain['functions']:
                    trace_info.functions = domain['functions']

                traces.append(trace_info)

            # Recursively process children
            if 'children' in domain and domain['children']:
                for child in domain['children']:
                    traverse_domain(child, current_path, current_diagrams)

        # Start traversal from the root
        traverse_domain(self.domain_taxonomy)

        logger.info(f"Extracted {len(traces)} traces from domain taxonomy")
        self.traces = traces
        return traces

    async def evaluate_trace_relevance(self, query: str) -> GlobalLocalisationResult:
        """
        Evaluate the relevance of each trace to the user query.

        Args:
            query: User query

        Returns:
            GlobalLocalisationResult containing relevant files
        """
        if not self.traces:
            logger.error("No traces available. Call extract_traces() first.")
            return GlobalLocalisationResult(
                success=False,
                error_message="No traces available"
            )

        # Prepare API requests for each trace
        api_requests = []
        for i, trace in enumerate(self.traces):
            # Create a combined mermaid diagram string
            mermaid_diagrams_str = "\n\n".join([d for d in trace.mermaid_diagrams if d])

            # Create the API request for this trace
            request_json = {
                "model": self.model,
                "messages": [
                    {
                        "role": "system",
                        "content": """You are an expert code analyzer that helps locate relevant code in a codebase based on user queries.
You will be given a user query and information about a specific domain trace in the codebase, including:
1. The hierarchical path of the domain trace
2. Mermaid diagrams showing the architecture of this domain
3. A list of files that belong to this domain

Your task is to evaluate whether this domain trace is DIRECTLY relevant to the user's query.
If it is relevant, explain why and identify which specific files would be most useful.
If it is not relevant, simply state that it is not relevant.

CRITERIA FOR DOMAIN RELEVANCE:
- A domain is ONLY relevant if it contains components that would be DIRECTLY involved in implementing or addressing the user's query
- Do NOT mark domains as relevant if they are only tangentially or indirectly related
- If you are uncertain about relevance, err on the side of marking it as NOT relevant
- Only domains that would be essential to understanding or implementing the user's request should be marked as relevant

FILE RELEVANCE CRITERIA:
- Only include files that would be DIRECTLY involved in understanding or implementing the user's request
- Do NOT include files that are only tangentially or indirectly related
- If you are uncertain about a file's relevance, DO NOT include it

SCORING GUIDELINES:
- 9-10: File is DIRECTLY responsible for the functionality in the query
- 8-9: File has significant relevance but isn't the primary implementation
- Do not include files with scores below 8

Return your analysis as a JSON object with the following structure:
{
  "is_relevant": true/false,

  "relevant_files": [
    {
      "file_path": "Path to the relevant file",
      "relevance_score": 0-10 (where 10 is highest relevance)
    }
  ]
}

IMPORTANT: Be strict with your evaluation. Only mark domains and files as relevant if they are DIRECTLY related to the query. When in doubt, mark as not relevant or exclude the file. It's better to return fewer, highly relevant items than many loosely related ones.
"""
                    },
                    {
                        "role": "user",
                        "content": f"""USER QUERY:
{query}

DOMAIN TRACE:
{trace.full_path}

DOMAIN ARCHITECTURE DIAGRAMS:
{mermaid_diagrams_str}

FILES IN THIS DOMAIN:
{json.dumps(trace.files, indent=2)}

Evaluate whether this domain trace is relevant to the user's query and identify any relevant files.
"""
                    }
                ],
                #"temperature": self.temperature,
                "response_format": {"type": "json_object"}
            }

            # Create an APIRequest object
            api_request = APIRequest(
                task_id=i,
                trace_info=trace,
                request_json=request_json,
                attempts_left=3  # Allow up to 3 attempts per request
            )
            api_requests.append(api_request)

        # Set up API endpoint and headers based on whether we're using OpenRouter or OpenAI
        if self.use_openrouter and self.openrouter_client:
            # For OpenRouter, we'll use the client directly instead of HTTP requests
            # We'll set placeholder values here and handle the API call differently
            request_url = "openrouter_client"
            request_header = {}
            logger.info(f"Using OpenRouter with model {self.openrouter_model} for trace relevance evaluation")
        else:
            # Standard OpenAI setup
            request_url = "https://api.openai.com/v1/chat/completions"
            request_header = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

        # Process requests with worker pool and batching
        status_tracker = StatusTracker()
        status_tracker.num_tasks_started = len(api_requests)
        status_tracker.num_tasks_in_progress = len(api_requests)

        # Create a dictionary to store results
        results = {}

        # Create a queue for retrying failed requests
        retry_queue = asyncio.Queue()

        # Add all requests to the queue
        for request in api_requests:
            await retry_queue.put(request)

        # Use the class parameters for worker pool
        logger.info(f"Starting API request processing with {self.num_workers} workers and batch size {self.batch_size}")
        logger.info(f"Total requests: {len(api_requests)}")

        # Process requests with worker pool and batching
        async with aiohttp.ClientSession() as session:
            # Create a semaphore to limit concurrent API calls
            semaphore = asyncio.Semaphore(self.num_workers)

            # Create a shared rate limiter state
            rate_limiter = {
                "available_request_capacity": self.max_requests_per_minute,
                "available_token_capacity": self.max_tokens_per_minute,
                "last_update_time": time.time()
            }

            # Define the worker function
            async def worker(worker_id: int):
                logger.info(f"Worker {worker_id} started")
                requests_processed = 0

                while status_tracker.num_tasks_in_progress > 0:
                    # Get a batch of requests
                    batch = []
                    try:
                        # Try to get up to self.batch_size requests without blocking
                        for _ in range(self.batch_size):
                            if retry_queue.empty():
                                break
                            batch.append(await retry_queue.get())
                    except asyncio.QueueEmpty:
                        pass

                    if not batch:
                        # If no requests are available, wait a bit and try again
                        await asyncio.sleep(0.01)
                        continue

                    logger.debug(f"Worker {worker_id} processing batch of {len(batch)} requests")

                    # Process the batch
                    tasks = []
                    for request in batch:
                        # Update rate limits
                        async with semaphore:
                            current_time = time.time()
                            time_since_update = current_time - rate_limiter["last_update_time"]

                            # Update available capacity based on time elapsed
                            rate_limiter["available_request_capacity"] = min(
                                rate_limiter["available_request_capacity"] + time_since_update * (self.max_requests_per_minute / 60),
                                self.max_requests_per_minute
                            )
                            rate_limiter["available_token_capacity"] = min(
                                rate_limiter["available_token_capacity"] + time_since_update * (self.max_tokens_per_minute / 60),
                                self.max_tokens_per_minute
                            )

                            rate_limiter["last_update_time"] = current_time

                            # Estimate token consumption (simplified)
                            estimated_tokens = 1000  # Simplified estimate

                            # Check if we have enough capacity
                            if rate_limiter["available_request_capacity"] >= 1 and rate_limiter["available_token_capacity"] >= estimated_tokens:
                                # Update counters
                                rate_limiter["available_request_capacity"] -= 1
                                rate_limiter["available_token_capacity"] -= estimated_tokens

                                # Create task for API call
                                task = asyncio.create_task(
                                    request.call_api(
                                        session=session,
                                        request_url=request_url,
                                        request_header=request_header,
                                        retry_queue=retry_queue,
                                        status_tracker=status_tracker,
                                        results=results,
                                        openrouter_client=self.openrouter_client if self.use_openrouter else None,
                                    )
                                )
                                tasks.append(task)
                                requests_processed += 1
                            else:
                                # Not enough capacity, put the request back in the queue
                                await retry_queue.put(request)

                    # Wait for all tasks in this batch to complete
                    if tasks:
                        await asyncio.gather(*tasks)

                    # Check if we're done
                    if status_tracker.num_tasks_succeeded + status_tracker.num_tasks_failed >= status_tracker.num_tasks_started:
                        break

                logger.info(f"Worker {worker_id} finished, processed {requests_processed} requests")

            # Start the workers
            workers = [asyncio.create_task(worker(i)) for i in range(self.num_workers)]

            # Wait for all workers to complete
            await asyncio.gather(*workers)

            logger.info(f"All workers completed. Succeeded: {status_tracker.num_tasks_succeeded}, Failed: {status_tracker.num_tasks_failed}")

            # Save raw LLM outputs to file
            session_id = str(uuid.uuid4())[:8]
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            raw_output_filename = f"llm_outputs_{timestamp}_{session_id}.json"
            raw_output_path = os.path.join(self.output_dir, raw_output_filename)

            with open(raw_output_path, 'w') as f:
                json.dump({
                    "query": query,
                    "model": self.model,
                    "timestamp": timestamp,
                    "results": results
                }, f, indent=2)

            logger.info(f"Saved raw LLM outputs to: {raw_output_path}")

            # Process the results to find relevant files
            relevant_files = []
            relevant_functions = []  # For backward compatibility

            for trace_str, result in results.items():
                if isinstance(result, dict) and result.get('is_relevant', False):
                    # Find the trace info for this trace
                    trace_info = next((t for t in self.traces if t.trace_str == trace_str), None)
                    if not trace_info:
                        continue

                    # Check for relevant files (primary data structure)
                    if 'relevant_files' in result:
                        for file_item in result.get('relevant_files', []):
                            # Add domain context to the file
                            file_item['domain_trace'] = trace_info.full_path

                            # Add file content if available
                            file_path = file_item.get('file_path', '')
                            if file_path and file_path in trace_info.file_contents:
                                file_item['file_content'] = trace_info.file_contents[file_path]

                            # Create a file item with all necessary fields
                            file_entry = {
                                'file_path': file_path,
                                'function_name': file_path,  # Use file path as function name for compatibility
                                'relevance_score': file_item.get('relevance_score', 0),
                                'domain_trace': trace_info.full_path,
                                'is_file': True,  # Mark as file for differentiation
                                'file_content': file_item.get('file_content', '')
                            }
                            relevant_files.append(file_entry)

                    # Check for relevant functions (backward compatibility)
                    elif 'relevant_functions' in result:
                        for func in result.get('relevant_functions', []):
                            # Add domain context to the function
                            func['domain_trace'] = trace_info.full_path
                            func['is_file'] = False  # Mark as function for differentiation
                            relevant_functions.append(func)

            # Sort relevant files by relevance score (descending)
            relevant_files.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)

            # Combine files and functions, prioritizing files
            all_relevant_items = relevant_files + relevant_functions

            # Save final analysis to file
            analysis_filename = f"analysis_{timestamp}_{session_id}.json"
            analysis_path = os.path.join(self.output_dir, analysis_filename)

            # Create a simplified list of items with their scores for quick reference
            item_scores = []
            for item in all_relevant_items:
                if item.get('is_file', False):
                    item_scores.append({
                        "file_path": item.get('file_path', 'Unknown'),
                        "relevance_score": item.get('relevance_score', 0),
                    })
                else:
                    item_scores.append({
                        "function_name": item.get('function_name', 'Unknown'),
                        "relevance_score": item.get('relevance_score', 0),
                    })

            with open(analysis_path, 'w') as f:
                json.dump({
                    "query": query,
                    "timestamp": timestamp,
                    "relevant_items": all_relevant_items,
                    "total_traces_analyzed": len(self.traces),
                    "relevant_traces": len([r for r in results.values() if isinstance(r, dict) and r.get('is_relevant', False)]),
                    "item_scores": item_scores
                }, f, indent=2)

            logger.info(f"Saved final analysis to: {analysis_path}")
            logger.info(f"Found {len(relevant_files)} relevant files and {len(relevant_functions)} relevant functions")

            return GlobalLocalisationResult(
                success=True,
                relevant_files=all_relevant_items,  # Use the primary field for file-level results
                raw_results=results,
                query=query,
                output_dir=self.output_dir
            )

    async def evaluate_domain_relevance(self, query: str) -> List[DomainRelevanceResult]:
        """
        First pass: Evaluate the relevance of each domain to the user query.
        This focuses on analyzing the mermaid diagrams to determine domain relevance.

        Args:
            query: User query

        Returns:
            List of DomainRelevanceResult objects for relevant domains
        """
        if not self.traces:
            logger.error("No traces available. Call extract_traces() first.")
            return []

        # Prepare API requests for each trace
        api_requests = []
        for i, trace in enumerate(self.traces):
            # Create a combined mermaid diagram string
            mermaid_diagrams_str = "\n\n".join([d for d in trace.mermaid_diagrams if d])

            # Create the API request for this trace
            request_json = {
                "model": self.model,
                "messages": [
                    {
                        "role": "system",
                        "content": """You are an expert code analyzer that helps locate relevant domains in a codebase based on user queries.

You will be given a user query and information about a specific domain trace in the codebase, including:
1. The hierarchical path of the domain trace
2. Mermaid diagrams showing the architecture of this domain

Your task is to evaluate whether this domain trace is DIRECTLY or TANGENTIALLY relevant to the user's query.
Focus on the leaf node (the last part of the domain trace) and how it fits into the overall codebase structure.

CRITERIA FOR RELEVANCE:
- A domain is ONLY relevant if it DIRECTLY or TANGENTIALLY contributes to the query
- Very Generic domains that do not relate to the query are NOT relevant

SCORING GUIDELINES:
- 9-10: Domain directly or tangentially implements the core functionality in the query
- 7-8: Domain is a critical part of the implementation, but not the main component
- Do not score domains above 6 unless they are central to the query
- 0-6: Domain has minimal or no direct relevance to the query

Return your analysis as a JSON object with the following structure:
{
  "is_relevant": true/false,
  "relevance_score": 0-10 (where 10 is highest relevance)
}

Make your evaluation based on the logical structure and purpose of the domain as shown in the mermaid diagrams.
Do not focus on specific file names, but rather on the domain's overall purpose and architecture.
"""
                    },
                    {
                        "role": "user",
                        "content": f"""USER QUERY:
{query}

DOMAIN TRACE:
{trace.full_path}

DOMAIN ARCHITECTURE DIAGRAMS:
{mermaid_diagrams_str}

Evaluate whether this domain trace is relevant to the user's query.
Focus on the leaf node (the last part of the domain trace) and how it fits into the overall codebase structure.
"""
                    }
                ],
                #"temperature": self.temperature,
                "response_format": {"type": "json_object"}
            }

            # Create an APIRequest object
            api_request = APIRequest(
                task_id=i,
                trace_info=trace,
                request_json=request_json,
                attempts_left=3  # Allow up to 3 attempts per request
            )
            api_requests.append(api_request)

        # Set up API endpoint and headers based on whether we're using OpenRouter or OpenAI
        if self.use_openrouter and self.openrouter_client:
            # For OpenRouter, we'll use the client directly instead of HTTP requests
            # We'll set placeholder values here and handle the API call differently
            request_url = "openrouter_client"
            request_header = {}
            logger.info(f"Using OpenRouter with model {self.openrouter_model} for domain relevance evaluation")
        else:
            # Standard OpenAI setup
            request_url = "https://api.openai.com/v1/chat/completions"
            request_header = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

        # Process requests with worker pool and batching
        status_tracker = StatusTracker()
        status_tracker.num_tasks_started = len(api_requests)
        status_tracker.num_tasks_in_progress = len(api_requests)

        # Create a dictionary to store results
        results = {}

        # Create a queue for retrying failed requests
        retry_queue = asyncio.Queue()

        # Add all requests to the queue
        for request in api_requests:
            await retry_queue.put(request)

        # Use the class parameters for worker pool
        logger.info(f"Starting domain relevance evaluation with {self.num_workers} workers and batch size {self.batch_size}")
        logger.info(f"Total domains to evaluate: {len(api_requests)}")

        # Process requests with worker pool and batching
        async with aiohttp.ClientSession() as session:
            # Create a semaphore to limit concurrent API calls
            semaphore = asyncio.Semaphore(self.num_workers)

            # Create a shared rate limiter state
            rate_limiter = {
                "available_request_capacity": self.max_requests_per_minute,
                "available_token_capacity": self.max_tokens_per_minute,
                "last_update_time": time.time()
            }

            # Define the worker function
            async def worker(worker_id: int):
                logger.info(f"Domain evaluation worker {worker_id} started")
                requests_processed = 0

                while status_tracker.num_tasks_in_progress > 0:
                    # Get a batch of requests
                    batch = []
                    try:
                        # Try to get up to self.batch_size requests without blocking
                        for _ in range(self.batch_size):
                            if retry_queue.empty():
                                break
                            batch.append(await retry_queue.get())
                    except asyncio.QueueEmpty:
                        pass

                    if not batch:
                        # If no requests are available, wait a bit and try again
                        await asyncio.sleep(0.01)
                        continue

                    logger.debug(f"Worker {worker_id} processing batch of {len(batch)} domain evaluations")

                    # Process the batch
                    tasks = []
                    for request in batch:
                        # Update rate limits
                        async with semaphore:
                            current_time = time.time()
                            time_since_update = current_time - rate_limiter["last_update_time"]

                            # Update available capacity based on time elapsed
                            rate_limiter["available_request_capacity"] = min(
                                rate_limiter["available_request_capacity"] + time_since_update * (self.max_requests_per_minute / 60),
                                self.max_requests_per_minute
                            )
                            rate_limiter["available_token_capacity"] = min(
                                rate_limiter["available_token_capacity"] + time_since_update * (self.max_tokens_per_minute / 60),
                                self.max_tokens_per_minute
                            )

                            rate_limiter["last_update_time"] = current_time

                            # Estimate token consumption (simplified)
                            estimated_tokens = 1000  # Simplified estimate

                            # Check if we have enough capacity
                            if rate_limiter["available_request_capacity"] >= 1 and rate_limiter["available_token_capacity"] >= estimated_tokens:
                                # Update counters
                                rate_limiter["available_request_capacity"] -= 1
                                rate_limiter["available_token_capacity"] -= estimated_tokens

                                # Create task for API call
                                task = asyncio.create_task(
                                    request.call_api(
                                        session=session,
                                        request_url=request_url,
                                        request_header=request_header,
                                        retry_queue=retry_queue,
                                        status_tracker=status_tracker,
                                        results=results,
                                        openrouter_client=self.openrouter_client if self.use_openrouter else None,
                                    )
                                )
                                tasks.append(task)
                                requests_processed += 1
                            else:
                                # Not enough capacity, put the request back in the queue
                                await retry_queue.put(request)

                    # Wait for all tasks in this batch to complete
                    if tasks:
                        await asyncio.gather(*tasks)

                    # Check if we're done
                    if status_tracker.num_tasks_succeeded + status_tracker.num_tasks_failed >= status_tracker.num_tasks_started:
                        break

                logger.info(f"Domain evaluation worker {worker_id} finished, processed {requests_processed} domains")

            # Start the workers
            workers = [asyncio.create_task(worker(i)) for i in range(self.num_workers)]

            # Wait for all workers to complete
            await asyncio.gather(*workers)

            logger.info(f"All domain evaluation workers completed. Succeeded: {status_tracker.num_tasks_succeeded}, Failed: {status_tracker.num_tasks_failed}")

        # Process the results to find relevant domains
        relevant_domains = []

        for trace_str, result in results.items():
            if isinstance(result, dict):
                is_relevant = result.get('is_relevant', False)
                relevance_score = result.get('relevance_score', 0)


                # Find the trace info for this trace
                trace_info = next((t for t in self.traces if t.trace_str == trace_str), None)
                if trace_info and (is_relevant or relevance_score >= 8):  # Consider domains with score >= 8 as relevant
                    relevant_domains.append(DomainRelevanceResult(
                        is_relevant=is_relevant,
                        relevance_score=relevance_score,
                        trace_str=trace_str,
                        full_path=trace_info.full_path,
                        functions=trace_info.functions,
                        mermaid_diagrams=trace_info.mermaid_diagrams
                    ))

        # Sort relevant domains by relevance score (descending)
        relevant_domains.sort(key=lambda x: x.relevance_score, reverse=True)

        # Save domain relevance results to file
        session_id = str(uuid.uuid4())[:8]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        domain_relevance_filename = f"domain_relevance_{timestamp}_{session_id}.json"
        domain_relevance_path = os.path.join(self.output_dir, domain_relevance_filename)

        with open(domain_relevance_path, 'w') as f:
            json.dump({
                "query": query,
                "timestamp": timestamp,
                "total_domains": len(self.traces),
                "relevant_domains": [
                    {
                        "trace": d.full_path,
                        "is_relevant": d.is_relevant,
                        "relevance_score": d.relevance_score
                    } for d in relevant_domains
                ]
            }, f, indent=2)

        logger.info(f"Saved domain relevance results to: {domain_relevance_path}")
        logger.info(f"Found {len(relevant_domains)} relevant domains out of {len(self.traces)} total domains")

        return relevant_domains

    async def extract_relevant_files(self, relevant_domains: List[DomainRelevanceResult], query: str) -> List[FileRelevanceResult]:
        """Second pass: Extract relevant files from the identified domains.

        Args:
            relevant_domains: List of relevant domains from the first pass
            query: User query

        Returns:
            List of FileRelevanceResult objects
        """
        if not relevant_domains:
            logger.warning("No relevant domains to extract files from")
            return []

        # Check if we need semantic functions (optional feature)
        semantic_functions_available = False
        if self.semantic_documented_fns_path:
            # Try to load semantic documented functions if path is provided
            if self.semantic_documented_fns_df is None:
                if not self.load_semantic_documented_functions():
                    logger.warning("Failed to load semantic documented functions, will proceed without them")
                else:
                    semantic_functions_available = True
            else:
                semantic_functions_available = True
        else:
            logger.info("No semantic documented functions path provided, will proceed without them")

        # Process each relevant domain
        api_requests = []
        domain_file_contents = {}

        for i, domain in enumerate(relevant_domains):
            # Get file contents for this domain
            file_contents = []
            total_tokens = 0

            # Process functions if available and semantic functions are loaded
            if semantic_functions_available and self.semantic_documented_fns_df is not None and domain.functions:
                for function_name in domain.functions:
                    # Extract node_id from function_name (e.g., "app/models/snippet.rb:Snippet.initialize" -> "app/models/snippet.rb:Snippet.initialize")
                    node_id = function_name

                    try:
                        # Find the function in the dataframe
                        function_row = self.semantic_documented_fns_df[self.semantic_documented_fns_df['node_id'] == node_id]

                        if not function_row.empty:
                            row = function_row.iloc[0]
                            function_text = row['text']
                            file_path = row['file_path']
                            start_line = int(row['start_line'])
                            end_line = int(row['end_line'])

                            # Estimate token count
                            function_token_count = estimate_token_count(function_text)

                            # Check if adding this function would exceed the token limit
                            if total_tokens + function_token_count <= self.max_tokens_per_domain:
                                file_contents.append({
                                    "function_name": function_name,
                                    "text": function_text,
                                    "file_path": file_path,
                                    "start_line": start_line,
                                    "end_line": end_line
                                })
                                total_tokens += function_token_count
                            else:
                                # Skip this function as it would exceed the token limit
                                logger.warning(f"Skipping function {function_name} as it would exceed the token limit")
                    except (TypeError, KeyError, AttributeError) as e:
                        logger.warning(f"Error processing semantic function {function_name}: {str(e)}")
                        continue

            # Process files if available (new file-level taxonomy) - this is now the primary data source
            trace_info = next((t for t in self.traces if t.trace_str == domain.trace_str), None)
            if trace_info and trace_info.files:
                for file_path in trace_info.files:
                    # Get file content if available
                    file_content = trace_info.file_contents.get(file_path, f"# File content not available for {file_path}")

                    # Estimate token count
                    file_token_count = estimate_token_count(file_content)

                    # Check if adding this file would exceed the token limit
                    if total_tokens + file_token_count <= self.max_tokens_per_domain:
                        file_contents.append({
                            "function_name": file_path,  # Use file path as function name for compatibility
                            "text": file_content,
                            "file_path": file_path,
                            "start_line": 1,  # Default values
                            "end_line": file_content.count('\n') + 1,
                            "is_file": True  # Mark as file for differentiation
                        })
                        total_tokens += file_token_count
                    else:
                        # Skip this file as it would exceed the token limit
                        logger.warning(f"Skipping file {file_path} as it would exceed the token limit")

            # Store file contents for this domain
            domain_file_contents[domain.trace_str] = file_contents

            # Create the system content
            system_content = """You are a code localization expert that helps identify the most relevant files for implementing specific features or fixing issues.

You will analyze files within a specific domain trace of a codebase to determine their relevance to a user's query.

INPUT PROVIDED:
1. User query describing a feature, bug, or implementation task
2. Domain trace showing the hierarchical path in the codebase
3. File contents from this domain"""

            if self.include_mermaid_in_second_pass:
                system_content += "\n4. Mermaid diagrams showing the domain's architecture and component relationships"

            system_content += """

EVALUATION CRITERIA:
1. Direct Implementation Match
   - File must contain the actual logic that implements the queried functionality
   - Score 9-10: Core implementation of the queried feature/behavior
   - Score 8: Critical supporting logic that the main implementation depends on

2. Exclusion Criteria - DO NOT include files that:
   - Only contain boilerplate or configuration code
   - Handle generic setup, configuration, or infrastructure
   - Provide utility/helper functionality
   - Process events or data without implementing core logic
   - Are only tangentially related to the query

OUTPUT FORMAT:
{
  "relevant_functions": [
    {
      "function_name": "app/models/project.rb",  # Use the file path as the function_name for compatibility
      "relevance_score": 8-10,
      "reasoning": "Brief explanation of direct relevance"
    }
  ]
}

IMPORTANT:
- Use the file path as the function_name field for compatibility
- Focus on precision over recall
- Only include files scoring 8 or higher
- Each included file must directly contribute to implementing the queried functionality
- When in doubt, exclude the file"""

            # Prepare user content
            user_content = f"""QUERY: {query}

DOMAIN CONTEXT:
This analysis is for the domain trace: {domain.trace_str}

TASK:
Evaluate each file's direct relevance to implementing or fixing the queried functionality.
Only include files that would need to be understood or modified to implement the query.
"""

            # Add mermaid diagrams if enabled
            if self.include_mermaid_in_second_pass:
                user_content += "\nDOMAIN ARCHITECTURE DIAGRAMS:\n"

                if self.include_full_trace_mermaid:
                    # Include all mermaid diagrams in the trace
                    mermaid_diagrams = [d for d in domain.mermaid_diagrams if d]
                    if mermaid_diagrams:
                        user_content += "\n\n".join(mermaid_diagrams)
                else:
                    # Include only the leaf node mermaid diagram (the last one)
                    mermaid_diagrams = [d for d in domain.mermaid_diagrams if d]
                    if mermaid_diagrams:
                        user_content += mermaid_diagrams[-1]  # Get the last diagram (leaf node)

            # Add files header
            user_content += "\n\nFILES IN THIS DOMAIN:\n"

            # Create the API request for this domain
            request_json = {
                "model": self.second_pass_model,  # Use the second pass model
                "messages": [
                    {
                        "role": "system",
                        "content": system_content
                    },
                    {
                        "role": "user",
                        "content": user_content
                    }
                ],
                #"temperature": self.temperature,
                "response_format": {"type": "json_object"}
            }

            # Add file contents to the user message
            for file_item in file_contents:
                file_path = file_item['file_path']
                file_text = file_item['text']
                request_json["messages"][1]["content"] += f"\n\nFile: {file_path}\n```\n{file_text}\n```\n"

            # Create an APIRequest object
            api_request = APIRequest(
                task_id=i,
                trace_info=TraceInfo(
                    trace_str=domain.trace_str,
                    mermaid_diagrams=[],
                    functions=domain.functions,
                    full_path=domain.full_path
                ),
                request_json=request_json,
                attempts_left=3  # Allow up to 3 attempts per request
            )
            api_requests.append(api_request)

        # Set up API endpoint and headers based on whether we're using OpenRouter or OpenAI for the second pass
        if self.second_pass_use_openrouter and self.second_pass_openrouter_client:
            # For OpenRouter, we'll use the client directly instead of HTTP requests
            # We'll set placeholder values here and handle the API call differently
            request_url = "openrouter_client"
            request_header = {}
            logger.info(f"Using OpenRouter with model {self.second_pass_openrouter_model} for function relevance evaluation")
        else:
            # Standard OpenAI setup
            request_url = "https://api.openai.com/v1/chat/completions"
            request_header = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

        # Process requests with rate limiting
        status_tracker = StatusTracker()
        status_tracker.num_tasks_started = len(api_requests)
        status_tracker.num_tasks_in_progress = len(api_requests)

        # Create a dictionary to store results
        results = {}

        # Create a queue for retrying failed requests
        retry_queue = asyncio.Queue()

        # Process requests with rate limiting
        async with aiohttp.ClientSession() as session:
            # Initialize counters for rate limiting
            available_request_capacity = self.max_requests_per_minute
            available_token_capacity = self.max_tokens_per_minute
            last_update_time = time.time()

            # Add all requests to the queue
            for request in api_requests:
                await retry_queue.put(request)

            # Process requests until all are completed
            while status_tracker.num_tasks_in_progress > 0:
                # Update rate limits
                current_time = time.time()
                time_since_update = current_time - last_update_time

                # Update available capacity based on time elapsed
                available_request_capacity = min(
                    available_request_capacity + time_since_update * (self.max_requests_per_minute / 60),
                    self.max_requests_per_minute
                )
                available_token_capacity = min(
                    available_token_capacity + time_since_update * (self.max_tokens_per_minute / 60),
                    self.max_tokens_per_minute
                )

                last_update_time = current_time

                # Get the next request from the queue if available
                try:
                    # Try to get a request from the queue without blocking
                    next_request = retry_queue.get_nowait()
                except asyncio.QueueEmpty:
                    # If the queue is empty, wait a bit and try again
                    await asyncio.sleep(0.001)  # Reduced sleep time for faster processing
                    continue

                # If enough capacity available, call API
                if available_request_capacity >= 1:
                    # Estimate token consumption (simplified)
                    estimated_tokens = 5000  # Higher estimate for function texts

                    if available_token_capacity >= estimated_tokens:
                        # Update counters
                        available_request_capacity -= 1
                        available_token_capacity -= estimated_tokens

                        # Call API without waiting for response - no semaphore, just send it
                        asyncio.create_task(
                            next_request.call_api(
                                session=session,
                                request_url=request_url,
                                request_header=request_header,
                                retry_queue=retry_queue,
                                status_tracker=status_tracker,
                                results=results,
                                openrouter_client=self.second_pass_openrouter_client if self.second_pass_use_openrouter else None,
                            )
                        )
                    else:
                        # Not enough token capacity, put the request back in the queue
                        await retry_queue.put(next_request)
                        await asyncio.sleep(0.001)  # Reduced sleep time
                else:
                    # Not enough request capacity, put the request back in the queue
                    await retry_queue.put(next_request)
                    await asyncio.sleep(0.001)  # Reduced sleep time

                # If all tasks are finished, break
                if status_tracker.num_tasks_succeeded + status_tracker.num_tasks_failed >= status_tracker.num_tasks_started:
                    break

        # Process the results to find relevant files and functions
        seen_functions = {}  # Dictionary to track functions and their highest scores
        seen_files = {}      # Dictionary to track files and their highest scores

        for trace_str, result in results.items():
            if isinstance(result, dict) and 'relevant_functions' in result:
                # Find the domain for this trace
                domain = next((d for d in relevant_domains if d.trace_str == trace_str), None)

                if domain:
                    # Get file contents and metadata for this domain
                    function_data = {}
                    for func in domain_file_contents.get(trace_str, []):
                        function_data[func['function_name']] = {
                            'text': func['text'],
                            'file_path': func['file_path'],
                            'start_line': func['start_line'],
                            'end_line': func['end_line'],
                            'is_file': func.get('is_file', False)  # Track if this is a file
                        }

                    # Process relevant functions from this domain
                    for func in result['relevant_functions']:
                        function_name = func.get('function_name', '')
                        function_info = function_data.get(function_name, {})
                        function_text = function_info.get('text', '')
                        file_path = function_info.get('file_path', '')
                        start_line = function_info.get('start_line', 0)
                        end_line = function_info.get('end_line', 0)
                        is_file = function_info.get('is_file', False)
                        relevance_score = func.get('relevance_score', 0)

                        # Determine if this is a file or a function the LLM extracted from a file
                        if is_file:
                            # This is a file entry
                            if file_path in seen_files:
                                if relevance_score > seen_files[file_path]['score']:
                                    seen_files[file_path] = {
                                        'score': relevance_score,
                                        'text': function_text,
                                        'domain_trace': domain.full_path,
                                        'file_path': file_path,
                                        'start_line': start_line,
                                        'end_line': end_line,
                                        'is_file': True
                                    }
                            else:
                                seen_files[file_path] = {
                                    'score': relevance_score,
                                    'text': function_text,
                                    'domain_trace': domain.full_path,
                                    'file_path': file_path,
                                    'start_line': start_line,
                                    'end_line': end_line,
                                    'is_file': True
                                }
                        else:
                            # This is a function entry
                            if function_name in seen_functions:
                                if relevance_score > seen_functions[function_name]['score']:
                                    seen_functions[function_name] = {
                                        'score': relevance_score,
                                        'text': function_text,
                                        'domain_trace': domain.full_path,
                                        'file_path': file_path,
                                        'start_line': start_line,
                                        'end_line': end_line,
                                        'is_file': False
                                    }
                            else:
                                seen_functions[function_name] = {
                                    'score': relevance_score,
                                    'text': function_text,
                                    'domain_trace': domain.full_path,
                                    'file_path': file_path,
                                    'start_line': start_line,
                                    'end_line': end_line,
                                    'is_file': False
                                }

                        # If this is a function extracted by the LLM, also track its file
                        if not is_file and file_path:
                            # Extract just the file path without the function name
                            if ':' in function_name:
                                extracted_file_path = function_name.split(':')[0]

                                # If we haven't seen this file yet or it has a lower score
                                if extracted_file_path not in seen_files or relevance_score > seen_files[extracted_file_path]['score']:
                                    seen_files[extracted_file_path] = {
                                        'score': relevance_score,
                                        'text': '',  # We don't have the full file text here
                                        'domain_trace': domain.full_path,
                                        'file_path': extracted_file_path,
                                        'start_line': 1,
                                        'end_line': 1,
                                        'is_file': True,
                                        'extracted_from_function': True  # Mark that this was extracted from a function
                                    }

        # First, convert the file results into FunctionRelevanceResult objects
        relevant_items = []

        # Process files first (prioritize files over functions)
        for file_path, info in seen_files.items():
            relevant_items.append(FunctionRelevanceResult(
                function_name=file_path,  # Use file path as the function name for compatibility
                relevance_score=info['score'],
                domain_trace=info['domain_trace'],
                function_text=info['text'],
                file_path=file_path,
                start_line=info['start_line'],
                end_line=info['end_line'],
                is_file=True
            ))

        # Then process functions (if we want to include them)
        for function_name, info in seen_functions.items():
            # Skip functions if we already have their file
            if ':' in function_name:
                file_path = function_name.split(':')[0]
                if file_path in seen_files:
                    # We already have this file, so skip the function
                    continue

            relevant_items.append(FunctionRelevanceResult(
                function_name=function_name,
                relevance_score=info['score'],
                domain_trace=info['domain_trace'],
                function_text=info['text'],
                file_path=info['file_path'],
                start_line=info['start_line'],
                end_line=info['end_line'],
                is_file=False
            ))

        # Sort all items by relevance score (descending)
        relevant_items.sort(key=lambda x: x.relevance_score, reverse=True)

        # Save relevance results to file
        session_id = str(uuid.uuid4())[:8]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        relevance_filename = f"file_relevance_{timestamp}_{session_id}.json"
        relevance_path = os.path.join(self.output_dir, relevance_filename)

        with open(relevance_path, 'w') as f:
            json.dump({
                "query": query,
                "timestamp": timestamp,
                "total_domains_processed": len(relevant_domains),
                "relevant_files": [
                    {
                        "file_path": item.file_path if item.is_file else item.function_name.split(':')[0] if ':' in item.function_name else "",
                        "function_name": item.function_name,
                        "domain_trace": item.domain_trace,
                        "relevance_score": item.relevance_score,
                        "is_file": item.is_file
                    } for item in relevant_items
                ]
            }, f, indent=2)

        logger.info(f"Saved relevance results to: {relevance_path}")
        logger.info(f"Found {len(seen_files)} relevant files and {len(seen_functions)} functions extracted from files from {len(relevant_domains)} domains")

        # Return the combined list of items
        return relevant_items

    async def third_pass_analyze_relevant_functions(self, query: str, relevant_functions: List[FunctionRelevanceResult]) -> ThirdPassResult:
        """
        Third pass: Filter the functions from the second pass using a larger model to select only the most relevant ones.

        Args:
            query: User query
            relevant_functions: List of relevant functions from the second pass

        Returns:
            ThirdPassResult containing the filtered functions and reasoning
        """
        if not relevant_functions:
            logger.warning("No relevant functions to filter in third pass")
            return ThirdPassResult(
                success=False,
                error_message="No relevant functions to filter"
            )

        try:
            # Prepare the function texts for filtering
            function_texts = []
            for i, func in enumerate(relevant_functions):
                # Include a function ID for easy reference in the response
                function_texts.append(f"FUNCTION ID: {i+1}\nFunction: {func.function_name}\nDomain: {func.domain_trace}\nRelevance Score: {func.relevance_score}/10\n\n{func.function_text}\n")

            # Combine all function texts
            combined_text = "\n---\n".join(function_texts)

            # Create the prompt for the third pass
            system_prompt = """
You are an expert code analyzer that helps identify the most relevant functions for a user query.

You will be given a user query and a set of functions from a codebase that were identified as potentially relevant to the query.

Your task is to:
1. Analyze each function and determine how directly it relates to the user's query
2. Select ONLY the relevant functions that are DIRECTLY involved in implementing the functionality described in the query
3. Provide clear reasoning for why you selected each function
4. Only choose functions that are truly central to answering the query

Return your response in the following JSON format:
{
  "selected_function_ids": [1, 3, 5],  // List of function IDs you've selected as most relevant
}

IMPORTANT: Your response MUST be valid JSON. The selected_function_ids should be integers matching the FUNCTION ID numbers in the input.
"""

            user_prompt = f"""USER QUERY:
{query}

POTENTIALLY RELEVANT FUNCTIONS:
{combined_text}

Please select only the most relevant functions for this query and explain your reasoning.
"""

            # Set up timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Determine which model to use for the third pass
            if self.third_pass_use_openrouter and self.third_pass_openrouter_client:
                # Use OpenRouter for the third pass
                try:
                    logger.info(f"Using OpenRouter with model {self.third_pass_openrouter_model} for third pass filtering")
                    response_json = await self.third_pass_openrouter_client.generate_json(
                        prompt=user_prompt,
                        system_prompt=system_prompt,
                    )
                    model_used = self.third_pass_openrouter_model
                except Exception as e:
                    logger.error(f"Error using OpenRouter for third pass: {e}")
                    return ThirdPassResult(
                        success=False,
                        error_message=f"Error using OpenRouter for third pass: {str(e)}",
                        timestamp=timestamp
                    )
            else:
                # Use OpenAI for the third pass
                try:
                    logger.info(f"Using OpenAI with model {self.third_pass_model} for third pass filtering")
                    # Set up API endpoint and headers
                    request_url = "https://api.openai.com/v1/chat/completions"
                    request_header = {
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {self.api_key}"
                    }

                    # Create the request JSON
                    request_json = {
                        "model": self.third_pass_model,
                        "messages": [
                            {"role": "system", "content": system_prompt},
                            {"role": "user", "content": user_prompt}
                        ],
                        "temperature": self.temperature,
                        "max_tokens": self.third_pass_max_tokens,
                        "response_format": {"type": "json_object"}
                    }

                    # Make the API request
                    async with aiohttp.ClientSession() as session:
                        async with session.post(
                            request_url,
                            headers=request_header,
                            json=request_json,
                            timeout=aiohttp.ClientTimeout(total=300)  # 5-minute timeout
                        ) as response:
                            if response.status == 200:
                                api_response = await response.json()
                                if "choices" in api_response and len(api_response["choices"]) > 0:
                                    response_content = api_response["choices"][0]["message"]["content"]
                                    try:
                                        response_json = json.loads(response_content)
                                        model_used = self.third_pass_model
                                    except json.JSONDecodeError:
                                        logger.error(f"Invalid JSON in API response for third pass: {response_content}")
                                        return ThirdPassResult(
                                            success=False,
                                            error_message=f"Invalid JSON in API response for third pass",
                                            timestamp=timestamp
                                        )
                                else:
                                    logger.error("No content in API response for third pass")
                                    return ThirdPassResult(
                                        success=False,
                                        error_message="No content in API response for third pass",
                                        timestamp=timestamp
                                    )
                            else:
                                error_text = await response.text()
                                logger.error(f"API error {response.status} for third pass: {error_text}")
                                return ThirdPassResult(
                                    success=False,
                                    error_message=f"API error {response.status} for third pass: {error_text}",
                                    timestamp=timestamp
                                )
                except Exception as e:
                    logger.error(f"Error using OpenAI for third pass: {e}")
                    return ThirdPassResult(
                        success=False,
                        error_message=f"Error using OpenAI for third pass: {str(e)}",
                        timestamp=timestamp
                    )

            # Process the selected functions
            selected_function_ids = response_json.get("selected_function_ids", [])
            reasoning = response_json.get("reasoning", "No reasoning provided")

            # Convert the selected function IDs to the actual functions
            filtered_functions = []
            for func_id in selected_function_ids:
                # Adjust for 0-based indexing if the model returned 1-based indices
                idx = func_id - 1 if func_id > 0 else func_id
                if 0 <= idx < len(relevant_functions):
                    func = relevant_functions[idx]
                    filtered_functions.append({
                        "function_name": func.function_name,
                        "relevance_score": func.relevance_score,
                        "domain_trace": func.domain_trace
                    })
                else:
                    logger.warning(f"Invalid function ID {func_id} returned by the model")

            # Save the third pass results to file
            session_id = str(uuid.uuid4())[:8]
            third_pass_filename = f"third_pass_filtering_{timestamp}_{session_id}.json"
            third_pass_path = os.path.join(self.output_dir, third_pass_filename)

            with open(third_pass_path, 'w') as f:
                json.dump({
                    "query": query,
                    "timestamp": timestamp,
                    "model_used": model_used,
                    "total_functions_before_filtering": len(relevant_functions),
                    "selected_function_ids": selected_function_ids,
                    "filtered_functions": filtered_functions,
                    "reasoning": reasoning
                }, f, indent=2)

            logger.info(f"Saved third pass filtering results to: {third_pass_path}")
            logger.info(f"Third pass filtered {len(relevant_functions)} functions down to {len(filtered_functions)} functions")

            # Return the result
            return ThirdPassResult(
                success=True,
                filtered_functions=filtered_functions,
                reasoning=reasoning,
                model_used=model_used,
                timestamp=timestamp
            )

        except Exception as e:
            logger.error(f"Error in third pass filtering: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())

            return ThirdPassResult(
                success=False,
                error_message=f"Error in third pass filtering: {str(e)}",
                timestamp=datetime.now().strftime("%Y%m%d_%H%M%S")
            )

    async def second_pass_find_relevant_files(self, query: str) -> GlobalLocalisationResult:
        """
        Find files relevant to the user query using the two-pass approach.

        First pass: Identify relevant domains based on mermaid diagrams
        Second pass: Extract relevant files from those domains
        Optional third pass: Analyze the filtered files using a larger model

        Args:
            query: User query

        Returns:
            GlobalLocalisationResult containing relevant files
        """
        try:
            start_time = time.time()
            # Load domain taxonomy
            if not self.load_domain_taxonomy():
                return GlobalLocalisationResult(
                    success=False,
                    error_message="Failed to load domain taxonomy"
                )

            # Extract traces
            self.extract_traces()

            # First pass: Evaluate domain relevance
            relevant_domains = await self.evaluate_domain_relevance(query)

            if not relevant_domains:
                logger.warning(f"No relevant domains found for query: {query}")
                return GlobalLocalisationResult(
                    success=True,
                    relevant_files=[],
                    query=query,
                    output_dir=self.output_dir,
                    relevant_domains=[]
                )
            end_time = time.time()
            print(f"Time taken for step 1: domain relevance: {end_time-start_time}")

            start_time = time.time()
            # Second pass: Extract relevant files from identified domains
            relevant_functions = await self.extract_relevant_files(relevant_domains, query)

            # Convert FunctionRelevanceResult objects to dictionaries for the result
            relevant_functions_dicts = [
                {
                    "function_name": func.function_name,
                    "relevance_score": func.relevance_score,
                    "domain_trace": func.domain_trace,
                    "function_text": func.function_text,
                    "file_path": func.file_path,
                    "start_line": func.start_line,
                    "end_line": func.end_line
                } for func in relevant_functions
            ]
            end_time = time.time()
            print(f"Time taken for step 2: functions relevance: {end_time-start_time}")

            # Create the result
            result = GlobalLocalisationResult(
                success=True,
                relevant_files=relevant_functions_dicts,
                query=query,
                output_dir=self.output_dir,
                relevant_domains=relevant_domains
            )

            # Optional third pass: Filter the functions using a larger model
            if self.use_third_pass and relevant_functions:
                start_time = time.time()
                logger.info(f"Starting third pass filtering with {len(relevant_functions)} functions")

                # Run the third pass filtering
                third_pass_result = await self.third_pass_analyze_relevant_functions(query, relevant_functions)

                if third_pass_result.success and third_pass_result.filtered_functions:
                    # Add the third pass result to the GlobalLocalisationResult
                    result.third_pass_result = third_pass_result

                    # Replace the relevant_files with the filtered ones from the third pass
                    result.relevant_files = third_pass_result.filtered_functions

                    logger.info(f"Third pass filtering completed successfully using model: {third_pass_result.model_used}")
                    logger.info(f"Third pass filtered {len(relevant_functions)} functions down to {len(third_pass_result.filtered_functions)} functions")
                else:
                    if third_pass_result.success:
                        logger.warning(f"Third pass filtering returned no functions")
                    else:
                        logger.error(f"Third pass filtering failed: {third_pass_result.error_message}")
                    # Still include the result
                    result.third_pass_result = third_pass_result

                end_time = time.time()
                print(f"Time taken for step 3: function filtering: {end_time-start_time}")

            # Log summary of results
            logger.info(f"Found {len(relevant_functions)} relevant files from {len(relevant_domains)} domains for query: {query}")

            return result

        except Exception as e:
            logger.error(f"Error finding relevant files with two-pass approach: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())

            return GlobalLocalisationResult(
                success=False,
                error_message=f"Error finding relevant files: {str(e)}"
            )

    async def find_relevant_files(self, query: str) -> GlobalLocalisationResult:
        """
        Find files relevant to the user query.

        If use_two_pass_approach is True, uses the two-pass approach.
        Otherwise, uses the original single-pass approach.

        Args:
            query: User query

        Returns:
            GlobalLocalisationResult containing relevant files
        """
        return await self.find_relevant_functions(query)

    async def find_relevant_functions(self, query: str) -> GlobalLocalisationResult:
        """
        Find files relevant to the user query.

        If use_two_pass_approach is True, uses the two-pass approach.
        Otherwise, uses the original single-pass approach.

        Note: Method name kept as find_relevant_functions for backward compatibility,
        but it now returns files rather than functions.

        Args:
            query: User query

        Returns:
            GlobalLocalisationResult containing relevant files
        """
        if self.use_two_pass_approach:
            return await self.second_pass_find_relevant_files(query)

        try:
            # Load domain taxonomy
            if not self.load_domain_taxonomy():
                return GlobalLocalisationResult(
                    success=False,
                    error_message="Failed to load domain taxonomy"
                )

            # Extract traces
            self.extract_traces()

            # Evaluate trace relevance
            result = await self.evaluate_trace_relevance(query)

            # Log summary of results
            if result.success:
                logger.info(f"Found {len(result.relevant_files)} relevant files for query: {query}")

            return result

        except Exception as e:
            logger.error(f"Error finding relevant files: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())

            return GlobalLocalisationResult(
                success=False,
                error_message=f"Error finding relevant files: {str(e)}"
            )

async def find_relevant_files(
    query: str,
    domain_taxonomy_json_path: str,
    api_key: Optional[str] = None,
    model: str = "gpt-4o-mini",  # Model for first pass
    second_pass_model: Optional[str] = None,  # Model for second pass
    max_requests_per_minute: float = 2500,
    max_tokens_per_minute: float = 20000000,
    output_dir: Optional[str] = None,
    semantic_documented_fns_path: Optional[str] = None,  # Optional for backward compatibility
    use_two_pass_approach: bool = True,
    max_tokens_per_domain: int = 70000,
    include_mermaid_in_second_pass: bool = False,
    include_full_trace_mermaid: bool = False,
    # OpenRouter parameters
    use_openrouter: bool = False,
    openrouter_api_key: Optional[str] = None,
    openrouter_model: str = "google/gemini-2.0-flash-001",  # OpenRouter model for first pass
    second_pass_use_openrouter: bool = False,  # Whether to use OpenRouter for second pass
    second_pass_openrouter_model: str = "google/gemini-2.0-flash-001",  # OpenRouter model for second pass
    openrouter_base_url: str = "https://openrouter.ai/api/v1",
    # Third pass parameters
    use_third_pass: bool = False,
    third_pass_model: str = "gpt-4o",
    third_pass_use_openrouter: bool = False,
    third_pass_openrouter_model: str = "anthropic/claude-3-opus-20240229",
    third_pass_max_tokens: int = 4000,
    # Parallelization parameters
    num_workers: int = 10,
    batch_size: int = 50,
) -> GlobalLocalisationResult:
    """
    Find files relevant to the user query.

    Args:
        query: User query
        domain_taxonomy_json_path: Path to the domain taxonomy JSON file
        api_key: OpenAI API key (if None, will try to get from environment)
        model: OpenAI model to use for the first pass (domain relevance)
        second_pass_model: OpenAI model to use for the second pass (file relevance); if None, uses the same model as the first pass
        max_requests_per_minute: Rate limit for API requests
        max_tokens_per_minute: Token rate limit for API
        output_dir: Directory to save LLM outputs and analysis
        semantic_documented_fns_path: Optional path to the semantic documented functions parquet file (optional for backward compatibility)
        use_two_pass_approach: Whether to use the two-pass approach for code localization
        max_tokens_per_domain: Maximum number of tokens to include per domain in the second pass
        include_mermaid_in_second_pass: Whether to include mermaid diagrams in the second pass
        include_full_trace_mermaid: If True, includes all mermaid diagrams in the trace; if False, only includes the leaf node diagram
        use_openrouter: Whether to use OpenRouter instead of OpenAI for the first pass
        openrouter_api_key: OpenRouter API key (if None, will try to get from environment)
        openrouter_model: OpenRouter model to use for the first pass
        second_pass_use_openrouter: Whether to use OpenRouter for the second pass
        second_pass_openrouter_model: OpenRouter model to use for the second pass
        openrouter_base_url: Base URL for OpenRouter API
        use_third_pass: Whether to use the third pass for detailed analysis of filtered files
        third_pass_model: OpenAI model to use for the third pass
        third_pass_use_openrouter: Whether to use OpenRouter for the third pass
        third_pass_openrouter_model: OpenRouter model to use for the third pass
        third_pass_max_tokens: Maximum number of tokens to generate in the third pass response
        num_workers: Number of concurrent workers for parallelized API calls
        batch_size: Number of requests per batch for parallelized processing

    Returns:
        GlobalLocalisationResult containing relevant files
    """
    try:
        # Create global localisation
        localisation = GlobalLocalisation(
            domain_taxonomy_json_path=domain_taxonomy_json_path,
            api_key=api_key,
            model=model,
            second_pass_model=second_pass_model,
            max_requests_per_minute=max_requests_per_minute,
            max_tokens_per_minute=max_tokens_per_minute,
            output_dir=output_dir,
            semantic_documented_fns_path=semantic_documented_fns_path,
            use_two_pass_approach=use_two_pass_approach,
            max_tokens_per_domain=max_tokens_per_domain,
            include_mermaid_in_second_pass=include_mermaid_in_second_pass,
            include_full_trace_mermaid=include_full_trace_mermaid,
            # OpenRouter parameters
            use_openrouter=use_openrouter,
            openrouter_api_key=openrouter_api_key,
            openrouter_model=openrouter_model,
            second_pass_use_openrouter=second_pass_use_openrouter,
            second_pass_openrouter_model=second_pass_openrouter_model,
            openrouter_base_url=openrouter_base_url,
            # Third pass parameters
            use_third_pass=use_third_pass,
            third_pass_model=third_pass_model,
            third_pass_use_openrouter=third_pass_use_openrouter,
            third_pass_openrouter_model=third_pass_openrouter_model,
            third_pass_max_tokens=third_pass_max_tokens,
            # Parallelization parameters
            num_workers=num_workers,
            batch_size=batch_size,
        )

        # Find relevant files
        result = await localisation.find_relevant_files(query)

        return result

    except Exception as e:
        logger.error(f"Error finding relevant files: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

        return GlobalLocalisationResult(
            success=False,
            error_message=f"Error finding relevant files: {str(e)}"
        )

async def main():
    """Main entry point for testing the global localisation."""
    import argparse

    parser = argparse.ArgumentParser(description="Find relevant files in a codebase based on a user query")
    parser.add_argument("--query", help="User query")
    parser.add_argument("--taxonomy", help="Path to the domain taxonomy JSON file")
    parser.add_argument("--api-key", help="OpenAI API key (if not provided, will try to get from environment)")
    parser.add_argument("--model", default="gpt-4o-mini", help="OpenAI model to use for the first pass (domain relevance)") #gpt-4o
    parser.add_argument("--second-pass-model", default="gpt-4o-mini", help="OpenAI model to use for the second pass (file relevance); if not provided, uses the same model as the first pass")
    parser.add_argument("--requests-per-minute", type=float, default=2500, help="Rate limit for API requests")
    parser.add_argument("--tokens-per-minute", type=float, default=20000000, help="Token rate limit for API")
    parser.add_argument("--output-dir", help="Directory to save LLM outputs and analysis")
    parser.add_argument("--semantic-fns", help="Optional: Path to the semantic documented functions parquet file")
    parser.add_argument("--use-two-pass", action="store_true", default=True, help="Use the two-pass approach for code localization")
    parser.add_argument("--max-tokens-per-domain", type=int, default=80000, help="Maximum number of tokens to include per domain in the second pass")
    parser.add_argument("--include-mermaid", action="store_true", default=False, help="Include mermaid diagrams in the second pass")
    parser.add_argument("--full-trace-mermaid", action="store_true", default=False, help="Include all mermaid diagrams in the trace instead of just the leaf node")
    # OpenRouter parameters for first pass
    parser.add_argument("--use-openrouter", action="store_true", default=True, help="Use OpenRouter instead of OpenAI for the first pass")
    parser.add_argument("--openrouter-api-key", help="OpenRouter API key (if not provided, will try to get from environment)")
    parser.add_argument("--openrouter-model", default="google/gemini-2.5-flash-preview", help="OpenRouter model to use for the first pass") #google/gemini-2.5-pro-preview" #google/gemini-2.0-flash-001
    parser.add_argument("--openrouter-base-url", default="https://openrouter.ai/api/v1", help="Base URL for OpenRouter API")
    # OpenRouter parameters for second pass
    parser.add_argument("--second-pass-use-openrouter", action="store_true", default=True, help="Use OpenRouter instead of OpenAI for the second pass")
    parser.add_argument("--second-pass-openrouter-model", default="google/gemini-2.5-flash-preview", help="OpenRouter model to use for the second pass")
    # Third pass parameters
    parser.add_argument("--use-third-pass", action="store_true", default=False, help="Use the third pass for detailed analysis of filtered files")
    parser.add_argument("--third-pass-model", default="openai/gpt-4o-mini", help="OpenAI model to use for the third pass") #google/gemini-2.0-flash-001
    parser.add_argument("--third-pass-use-openrouter", action="store_true", default=True, help="Use OpenRouter for the third pass")
    parser.add_argument("--third-pass-openrouter-model", default="openai/gpt-4o-mini", help="OpenRouter model to use for the third pass")
    parser.add_argument("--third-pass-max-tokens", type=int, default=2000, help="Maximum number of tokens to generate in the third pass response")
    # Parallelization parameters
    parser.add_argument("--num-workers", type=int, default=10, help="Number of concurrent workers for parallelized API calls")
    parser.add_argument("--batch-size", type=int, default=50, help="Number of requests per batch for parallelized processing")

    args = parser.parse_args()

    # Default values if not provided
    if not args.query:

#         args.query = """What is the purpose of RepositoryCleaner in the GitLab codebase? How does it handle operations like applying BFG object maps?"""
#         args.query = """In the Project model, what is the purpose of the repository_storage_moves association and how is it used when changing a project's repository storage?"""
#         args.query = """Explain the implementation of merge request handling in GitLab. How does the code flow from controller to service when a merge request is created and merged?"""
#         args.query = """"Our background migration system spans multiple databases (main, CI, shared) with different schemas. Walk me through:
# How RestrictGitlabSchema, MigrationHelpers, and BatchedBackgroundMigrationHelpers coordinate across database boundaries
# Why certain migrations are restricted from gitlab_shared schema
# How job queuing and execution differs between databases
# What happens when a migration needs to maintain referential integrity across schemas Explain the architectural decisions behind this design and their implications for system evolution.
# """

        # args.query = "In UpdateSbomComponentsNameBasedOnPep503, we see specific handling of ActiveRecord::RecordNotUnique errors. Explain why this specific error handling exists, how it interacts with our logging system, and trace where else in the codebase we use similar patterns. What makes this approach different from the error handling in other background migrations?"
#         args.query = "Our background migration system interacts with multiple GitLab subsystems. Using BackgroundMigration::BatchOptimizer as a starting point, map out all its dependencies and explain how changes in related modules (like ExclusiveLease or Feature) would impact its functionality. Include specific method calls and data flow paths."
#         args.query = "Trace how the runner_migrations_backoff feature flag affects our migration system. Starting from RunnerBackoff::Communicator, show all the touchpoints where this configuration impacts execution flow, including how it interacts with our lease system and logging. What other feature flags influence this subsystem?"

#         args.query = """Analyze our background migration system's concurrency controls. Specifically:
# How do ExclusiveLease, RunnerBackoff::Communicator, and BatchOptimizer prevent race conditions?
# What happens if multiple migrations attempt to update the same records simultaneously?
# Where in the codebase do we handle deadlock prevention? Show specific code paths and explain how they interact under high concurrency scenarios."""

#         args.query = "How does the system implement adaptive rate limiting based on system load and resource availability?"
#         args.query = "What's the architecture behind the repository health check system?"
#         args.query = "How does the codebase handle polymorphic service objects across different domains? Is there a consistent pattern for service object initialization and execution?"

        args.query = "How is user authentication handled? Is it using Django’s built-in User model or a custom user model?"
    if not args.taxonomy:
        # args.taxonomy = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/domain_taxonomy.json"
        # args.taxonomy = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/outputs/django/domain_file_taxonomy/taxonomy.json"
        # args.taxonomy = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/mermaid_outputs_json/gitlab/taxonomy1.json"
        args.taxonomy = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/outputs/datadog_agent/taxonomy/taxonomy.json"

    if not args.output_dir:
        args.output_dir = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/localisation/qa_data"
    # Semantic functions are now optional
    # if not args.semantic_fns:
    #     args.semantic_fns = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/semantic_documented_functions.parquet"

    start_time = time.time()

    # Find relevant files
    result = await find_relevant_files(
        query=args.query,
        domain_taxonomy_json_path=args.taxonomy,
        api_key=args.api_key,
        model=args.model,
        second_pass_model=args.second_pass_model,
        max_requests_per_minute=args.requests_per_minute,
        max_tokens_per_minute=args.tokens_per_minute,
        output_dir=args.output_dir,
        semantic_documented_fns_path=args.semantic_fns,
        use_two_pass_approach=args.use_two_pass,
        max_tokens_per_domain=args.max_tokens_per_domain,
        include_mermaid_in_second_pass=args.include_mermaid,
        include_full_trace_mermaid=args.full_trace_mermaid,
        # OpenRouter parameters for first pass
        use_openrouter=args.use_openrouter,
        openrouter_api_key=args.openrouter_api_key,
        openrouter_model=args.openrouter_model,
        openrouter_base_url=args.openrouter_base_url,
        # OpenRouter parameters for second pass
        second_pass_use_openrouter=args.second_pass_use_openrouter,
        second_pass_openrouter_model=args.second_pass_openrouter_model,
        # Third pass parameters
        use_third_pass=args.use_third_pass,
        third_pass_model=args.third_pass_model,
        third_pass_use_openrouter=args.third_pass_use_openrouter,
        third_pass_openrouter_model=args.third_pass_openrouter_model,
        third_pass_max_tokens=args.third_pass_max_tokens,
        # Parallelization parameters
        num_workers=args.num_workers,
        batch_size=args.batch_size,
    )
    end_time = time.time()

    if result.success:
        # Print a summary for human readability
        print(f"Found {len(result.relevant_files)} relevant files:")
        for i, item in enumerate(result.relevant_files):
            if item.get('is_file', False):
                print(f"\n{i+1}. File: {item.get('file_path', 'Unknown')}")
            else:
                # For backward compatibility with function-level data
                print(f"\n{i+1}. Function: {item.get('function_name', 'Unknown')}")
            print(f"   Domain: {item.get('domain_trace', 'Unknown')}")
            print(f"   Relevance: {item.get('relevance_score', 0)}/10")

        print(f"\nLLM outputs and analysis saved to: {result.output_dir}")

        # Output the full JSON result for the TypeScript code to parse
        json_output = {
            "success": result.success,
            "relevant_files": result.relevant_files,
            "query": result.query
        }
        print("\n\nJSON_OUTPUT_START")
        print(json.dumps(json_output))
        print("JSON_OUTPUT_END")

        # Display information about the third pass result if available
        if result.third_pass_result and result.third_pass_result.success:
            print(f"\nThird pass filtering completed successfully using model: {result.third_pass_result.model_used}")
            print(f"Third pass filtered {len(result.relevant_files)} files down to {len(result.third_pass_result.filtered_functions)} files")

            print("\nMost relevant files selected by the third pass:")
            for i, item in enumerate(result.third_pass_result.filtered_functions):
                if item.get('is_file', False):
                    print(f"  {i+1}. File: {item.get('file_path', 'Unknown')}")
                else:
                    # For backward compatibility with function-level data
                    print(f"  {i+1}. Function: {item.get('function_name', 'Unknown')}")
                print(f"     Domain: {item.get('domain_trace', 'Unknown')}")
                print(f"     Relevance: {item.get('relevance_score', 0)}/10")

            print("\nReasoning for selection:")
            # Format the reasoning for better readability
            reasoning_lines = result.third_pass_result.reasoning.split('\n')
            for line in reasoning_lines[:10]:  # Show first 10 lines of reasoning
                print(f"  {line}")
            if len(reasoning_lines) > 10:
                print("  ...")

            print(f"\nFull third pass results saved to: {result.output_dir}/third_pass_filtering_{result.third_pass_result.timestamp}_*.json")

        print(f"\nTotal time taken: {end_time - start_time:.2f} seconds")
    else:
        print(f"Error: {result.error_message}")

    return 0 if result.success else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
