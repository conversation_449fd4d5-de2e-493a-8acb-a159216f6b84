#!/usr/bin/env python
"""
Domain Trace Token Analyzer

This module analyzes domain taxonomy JSON files to calculate token counts for each trace
(path from root to any node, including intermediate nodes).

It provides token counts for:
1. Total content (functions + diagrams)
2. Only mermaid diagrams
3. Only function mappings
"""

import os
import json
import logging
import pandas as pd
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field

# Import token counting utility
try:
    from bracket_core.llm.tokens import num_tokens_from_string
except ImportError:
    # Fallback to tiktoken if bracket_core.llm.tokens is not available
    import tiktoken
    
    def num_tokens_from_string(string: str, model_name: str = "gpt-4o-mini") -> int:
        """Returns the number of tokens in a text string."""
        encoding = tiktoken.encoding_for_model(model_name)
        return len(encoding.encode(string))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class TraceTokenInfo:
    """Information about token counts for a trace in the domain taxonomy."""
    full_path: str
    level: int
    is_leaf: bool
    has_children: bool
    function_count: int
    has_diagram: bool
    total_tokens: int
    diagram_tokens: int
    function_tokens: int
    children_count: int

@dataclass
class TraceAnalysisResult:
    """Result of analyzing traces in the domain taxonomy."""
    total_traces: int = 0
    leaf_traces: int = 0
    intermediate_traces: int = 0
    total_tokens: int = 0
    total_diagram_tokens: int = 0
    total_function_tokens: int = 0
    average_tokens_per_trace: float = 0
    max_tokens: int = 0
    max_tokens_trace: str = ""
    traces_with_diagrams: int = 0
    traces_without_diagrams: int = 0
    trace_info: List[TraceTokenInfo] = field(default_factory=list)

class DomainTraceTokenAnalyzer:
    """
    Analyzes domain taxonomy JSON files to calculate token counts for each trace.
    """

    def __init__(
        self,
        taxonomy_json_path: str,
        output_dir: Optional[str] = None,
        model_name: str = "gpt-4o-mini"
    ):
        """
        Initialize the domain trace token analyzer.

        Args:
            taxonomy_json_path: Path to the domain taxonomy JSON file
            output_dir: Directory to save analysis results (optional)
            model_name: Model name to use for token counting
        """
        self.taxonomy_json_path = taxonomy_json_path
        self.output_dir = output_dir or os.path.dirname(taxonomy_json_path)
        self.model_name = model_name
        
        # Create output directory if it doesn't exist
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Initialize data structures
        self.taxonomy_data = {}
        self.trace_info: List[TraceTokenInfo] = []

    def read_taxonomy_json(self) -> Dict[str, Any]:
        """
        Read the domain taxonomy JSON file.

        Returns:
            Dictionary containing domain taxonomy data
        """
        logger.info(f"Reading domain taxonomy JSON file: {self.taxonomy_json_path}")
        try:
            with open(self.taxonomy_json_path, 'r') as f:
                taxonomy_data = json.load(f)
            return taxonomy_data
        except Exception as e:
            logger.error(f"Error reading domain taxonomy JSON file: {e}")
            raise

    def analyze_node(self, node: Dict[str, Any], path: List[str] = None, level: int = 0) -> None:
        """
        Recursively analyze nodes in the taxonomy tree.

        Args:
            node: Current node in the taxonomy tree
            path: Current path in the taxonomy tree
            level: Current level in the hierarchy
        """
        if path is None:
            path = []
        
        name = node.get('name', 'Unknown')
        current_path = path + [name]
        full_path = " -> ".join(current_path)
        
        # Get functions and diagram
        functions = node.get('functions', [])
        diagram = node.get('diagram')
        
        # Calculate tokens for functions
        function_content = ""
        if functions:
            function_content = f"Functions for {full_path}:\n"
            for func in functions:
                function_content += f"- {func}\n"
        
        function_tokens = num_tokens_from_string(function_content, self.model_name) if function_content else 0
        
        # Calculate tokens for diagram
        diagram_tokens = 0
        if diagram:
            diagram_content = f"Diagram for {full_path}:\n{diagram}"
            diagram_tokens = num_tokens_from_string(diagram_content, self.model_name)
        
        # Calculate total tokens
        total_tokens = function_tokens + diagram_tokens
        
        # Check if this is a leaf node
        has_children = 'children' in node and node['children']
        is_leaf = not has_children
        
        # Create trace info
        trace_info = TraceTokenInfo(
            full_path=full_path,
            level=level,
            is_leaf=is_leaf,
            has_children=has_children,
            function_count=len(functions),
            has_diagram=diagram is not None,
            total_tokens=total_tokens,
            diagram_tokens=diagram_tokens,
            function_tokens=function_tokens,
            children_count=len(node.get('children', []))
        )
        
        self.trace_info.append(trace_info)
        
        # Process children recursively
        if has_children:
            for child in node['children']:
                self.analyze_node(child, current_path, level + 1)

    def analyze_traces(self) -> TraceAnalysisResult:
        """
        Analyze the domain taxonomy to calculate token counts for each trace.

        Returns:
            TraceAnalysisResult containing the analysis results
        """
        logger.info("Analyzing domain taxonomy traces")
        
        # Read taxonomy JSON
        self.taxonomy_data = self.read_taxonomy_json()
        
        # Analyze nodes recursively
        self.analyze_node(self.taxonomy_data)
        logger.info(f"Analyzed {len(self.trace_info)} traces")
        
        # Calculate statistics
        leaf_traces = sum(1 for trace in self.trace_info if trace.is_leaf)
        intermediate_traces = len(self.trace_info) - leaf_traces
        
        total_tokens = sum(trace.total_tokens for trace in self.trace_info)
        total_diagram_tokens = sum(trace.diagram_tokens for trace in self.trace_info)
        total_function_tokens = sum(trace.function_tokens for trace in self.trace_info)
        
        avg_tokens = total_tokens / len(self.trace_info) if self.trace_info else 0
        
        max_tokens = 0
        max_tokens_trace = ""
        
        traces_with_diagrams = 0
        traces_without_diagrams = 0
        
        for trace in self.trace_info:
            if trace.total_tokens > max_tokens:
                max_tokens = trace.total_tokens
                max_tokens_trace = trace.full_path
            
            if trace.has_diagram:
                traces_with_diagrams += 1
            else:
                traces_without_diagrams += 1
        
        # Create result
        result = TraceAnalysisResult(
            total_traces=len(self.trace_info),
            leaf_traces=leaf_traces,
            intermediate_traces=intermediate_traces,
            total_tokens=total_tokens,
            total_diagram_tokens=total_diagram_tokens,
            total_function_tokens=total_function_tokens,
            average_tokens_per_trace=avg_tokens,
            max_tokens=max_tokens,
            max_tokens_trace=max_tokens_trace,
            traces_with_diagrams=traces_with_diagrams,
            traces_without_diagrams=traces_without_diagrams,
            trace_info=self.trace_info
        )
        
        return result

    def save_analysis_results(self, result: TraceAnalysisResult) -> Tuple[str, str]:
        """
        Save analysis results to files.

        Args:
            result: TraceAnalysisResult containing the analysis results

        Returns:
            Tuple of (csv_path, summary_path)
        """
        logger.info("Saving trace analysis results")
        
        # Create a timestamp for the output files
        timestamp = time.strftime("%Y%m%d-%H%M%S")
        
        # Save trace info to CSV
        csv_path = os.path.join(self.output_dir, f"trace_tokens_{timestamp}.csv")
        df = pd.DataFrame([
            {
                "full_path": trace.full_path,
                "level": trace.level,
                "is_leaf": trace.is_leaf,
                "has_children": trace.has_children,
                "function_count": trace.function_count,
                "has_diagram": trace.has_diagram,
                "total_tokens": trace.total_tokens,
                "diagram_tokens": trace.diagram_tokens,
                "function_tokens": trace.function_tokens,
                "children_count": trace.children_count
            }
            for trace in result.trace_info
        ])
        df.sort_values(["level", "total_tokens"], ascending=[True, False], inplace=True)
        df.to_csv(csv_path, index=False)
        logger.info(f"Saved trace info to {csv_path}")
        
        # Save summary to text file
        summary_path = os.path.join(self.output_dir, f"trace_analysis_summary_{timestamp}.txt")
        with open(summary_path, 'w') as f:
            f.write(f"Domain Trace Token Analysis Summary\n")
            f.write(f"==================================\n\n")
            f.write(f"Total traces: {result.total_traces}\n")
            f.write(f"Leaf traces: {result.leaf_traces}\n")
            f.write(f"Intermediate traces: {result.intermediate_traces}\n\n")
            
            f.write(f"Total tokens: {result.total_tokens:,}\n")
            f.write(f"Total diagram tokens: {result.total_diagram_tokens:,} ({result.total_diagram_tokens/result.total_tokens*100:.1f}%)\n")
            f.write(f"Total function tokens: {result.total_function_tokens:,} ({result.total_function_tokens/result.total_tokens*100:.1f}%)\n\n")
            
            f.write(f"Average tokens per trace: {result.average_tokens_per_trace:.2f}\n")
            f.write(f"Maximum tokens: {result.max_tokens:,} (Trace: {result.max_tokens_trace})\n\n")
            
            f.write(f"Traces with diagrams: {result.traces_with_diagrams}\n")
            f.write(f"Traces without diagrams: {result.traces_without_diagrams}\n\n")
            
            f.write(f"Token distribution by level:\n")
            level_stats = {}
            for trace in result.trace_info:
                if trace.level not in level_stats:
                    level_stats[trace.level] = {
                        "count": 0,
                        "total_tokens": 0,
                        "diagram_tokens": 0,
                        "function_tokens": 0
                    }
                level_stats[trace.level]["count"] += 1
                level_stats[trace.level]["total_tokens"] += trace.total_tokens
                level_stats[trace.level]["diagram_tokens"] += trace.diagram_tokens
                level_stats[trace.level]["function_tokens"] += trace.function_tokens
            
            for level, stats in sorted(level_stats.items()):
                f.write(f"  Level {level}: {stats['count']} traces, {stats['total_tokens']:,} tokens ")
                f.write(f"(Diagrams: {stats['diagram_tokens']:,}, Functions: {stats['function_tokens']:,})\n")
            
            f.write(f"\nFiles generated:\n")
            f.write(f"- Trace info CSV: {os.path.basename(csv_path)}\n")
        
        logger.info(f"Saved summary to {summary_path}")
        return csv_path, summary_path

    def run_analysis(self) -> Tuple[TraceAnalysisResult, str, str]:
        """
        Run the complete analysis workflow.

        Returns:
            Tuple of (TraceAnalysisResult, csv_path, summary_path)
        """
        # Analyze traces
        result = self.analyze_traces()
        
        # Save results
        csv_path, summary_path = self.save_analysis_results(result)
        
        return result, csv_path, summary_path
