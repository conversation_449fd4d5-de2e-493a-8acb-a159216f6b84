"""
Domain Classification for Codebase

This script reads a YAML file containing significant functions from a codebase
and uses GPT-4o-mini to classify them into domains and subdomains.

The classification is hierarchical, with top-level domains and nested subdomains
to represent the logical organization of the codebase.
"""

import os
import yaml
import json
import logging
import argparse
import asyncio
import aiohttp
import time
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DomainClassifier:
    """
    Classifies codebase functions into domains and subdomains using GPT-4o-mini.
    """
    
    def __init__(
        self,
        yaml_path: str,
        output_path: str,
        api_key: Optional[str] = None,
        model: str = "gpt-4o-mini",
        max_requests_per_minute: float = 50,
        max_tokens_per_minute: float = 100000,
    ):
        """
        Initialize the domain classifier.
        
        Args:
            yaml_path: Path to the YAML file containing significant functions
            output_path: Path to save the domain classification output
            api_key: OpenAI API key (if None, will try to get from environment)
            model: OpenAI model to use
            max_requests_per_minute: Rate limit for API requests
            max_tokens_per_minute: Token rate limit for API
        """
        self.yaml_path = yaml_path
        self.output_path = output_path
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        self.model = model
        self.max_requests_per_minute = max_requests_per_minute
        self.max_tokens_per_minute = max_tokens_per_minute
        
        if not self.api_key:
            raise ValueError("OpenAI API key not provided and not found in environment")
    
    async def classify_domains(self) -> Dict[str, Any]:
        """
        Read the YAML file, send it to the OpenAI API, and get domain classifications.
        
        Returns:
            Dictionary containing the domain classification
        """
        # Read the YAML file
        logger.info(f"Reading YAML file: {self.yaml_path}")
        with open(self.yaml_path, 'r') as f:
            yaml_data = yaml.safe_load(f)
        
        if not yaml_data:
            logger.error("YAML file is empty or could not be parsed")
            return {}
        
        # Format the YAML content for the API request
        yaml_str = yaml.dump(yaml_data, default_flow_style=False)
        
        # Create the API request
        logger.info("Preparing API request for domain classification")
        request = {
            "model": self.model,
            "messages": [
                {
                    "role": "system",
                    "content": """You are an expert software architect analyzing a codebase. 
Your task is to organize the provided functions into a hierarchical structure of domains and subdomains.

Guidelines:
1. Identify the main functional domains in the codebase
2. Create a hierarchical structure with domains and nested subdomains
3. Make the classification as granular as appropriate based on the complexity
4. Each domain/subdomain should have a brief description explaining its purpose
5. Assign each function to the most specific subdomain that applies

Output Format:
```yaml
domains:
  - name: "Domain Name"
    description: "Brief description of this domain's purpose"
    subdomains:
      - name: "Subdomain Name"
        description: "Brief description of this subdomain's purpose"
        subdomains:
          - name: "Nested Subdomain Name"
            description: "Brief description of this nested subdomain"
            functions:
              - "function_name1"
              - "function_name2"
        functions:
          - "function_name3"
    functions:
      - "function_name4"
```

The hierarchy can be as deep as needed. Functions should be assigned to the most specific level possible.
"""
                },
                {
                    "role": "user",
                    "content": f"Please analyze these functions and organize them into domains and subdomains:\n\n{yaml_str}"
                }
            ],
            "temperature": 0.2
        }
        
        # Call the OpenAI API with rate limiting
        logger.info("Calling OpenAI API for domain classification")
        response = await self._call_openai_api(request)
        
        if not response:
            logger.error("Failed to get response from OpenAI API")
            return {}
        
        # Parse the YAML response
        try:
            # Extract YAML content from the response
            content = response["choices"][0]["message"]["content"]
            
            # Find YAML content between triple backticks
            yaml_content = content
            if "```yaml" in content:
                yaml_content = content.split("```yaml")[1].split("```")[0].strip()
            elif "```" in content:
                yaml_content = content.split("```")[1].split("```")[0].strip()
            
            # Parse the YAML content
            domains_data = yaml.safe_load(yaml_content)
            
            # Save the domain classification
            with open(self.output_path, 'w') as f:
                yaml.dump(domains_data, f, default_flow_style=False)
            
            logger.info(f"Domain classification saved to: {self.output_path}")
            return domains_data
            
        except Exception as e:
            logger.error(f"Error parsing API response: {e}")
            # Save the raw response for debugging
            with open(f"{self.output_path}.raw", 'w') as f:
                f.write(content) # type: ignore
            logger.info(f"Raw response saved to: {self.output_path}.raw")
            return {}
    
    async def _call_openai_api(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        Call the OpenAI API with rate limiting.
        
        Args:
            request: API request payload
            
        Returns:
            API response or empty dict if failed
        """
        max_retries = 5
        retry_delay = 5  # seconds
        
        for attempt in range(max_retries):
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        "https://api.openai.com/v1/chat/completions",
                        headers={
                            "Authorization": f"Bearer {self.api_key}",
                            "Content-Type": "application/json"
                        },
                        json=request
                    ) as response:
                        if response.status == 200:
                            return await response.json()
                        elif response.status == 429:  # Rate limit error
                            logger.warning(f"Rate limit hit. Waiting {retry_delay} seconds before retry.")
                            await asyncio.sleep(retry_delay)
                            retry_delay *= 2  # Exponential backoff
                        else:
                            error_data = await response.text()
                            logger.error(f"API error (status {response.status}): {error_data}")
                            if attempt < max_retries - 1:
                                await asyncio.sleep(retry_delay)
                                retry_delay *= 2
            except Exception as e:
                logger.error(f"Request error: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2
        
        logger.error(f"Failed to get response after {max_retries} attempts")
        return {}

async def main():
    """Main entry point for the domain classifier."""
    parser = argparse.ArgumentParser(description="Classify codebase functions into domains and subdomains")
    parser.add_argument("--yaml", required=True, help="Path to the YAML file containing significant functions")
    parser.add_argument("--output", required=True, help="Path to save the domain classification output")
    parser.add_argument("--api-key", help="OpenAI API key (if not provided, will try to get from environment)")
    parser.add_argument("--model", default="gpt-4o-mini", help="OpenAI model to use")
    parser.add_argument("--requests-per-minute", type=float, default=50, help="Rate limit for API requests")
    parser.add_argument("--tokens-per-minute", type=float, default=100000, help="Token rate limit for API")
    
    args = parser.parse_args()
    
    classifier = DomainClassifier(
        yaml_path=args.yaml,
        output_path=args.output,
        api_key=args.api_key,
        model=args.model,
        max_requests_per_minute=args.requests_per_minute,
        max_tokens_per_minute=args.tokens_per_minute,
    )
    
    await classifier.classify_domains()

if __name__ == "__main__":
    asyncio.run(main())
