"""
Domain Analysis for Codebase

This module provides functionality to analyze a codebase's significant functions
and classify them into domains and subdomains using GPT-4o-mini.

It can be used as:
1. A standalone script to process a YAML file of significant functions
2. A module that can be integrated into the repository analysis flow

The domain classification provides a hierarchical view of the codebase's
functional organization, making it easier to understand the overall architecture.

For large codebases (>1M LOC), the module supports parallel processing by:
1. Splitting the function data into manageable chunks
2. Processing each chunk in parallel with separate LLM calls
3. Combining the results into a unified domain structure
"""

import os
import yaml
import json
import logging
import argparse
import asyncio
import aiohttp
import time
import math
import tiktoken
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, field
from collections import defaultdict

# Import OpenRouter client and API key management
from bracket_core.llm.get_client import get_openrouter_client
from bracket_core.llm.api_keys import get_openai_api_key, get_openrouter_api_key

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    from dotenv import load_dotenv
    load_dotenv()  # Load .env file if it exists
except ImportError:
    pass  # dotenv not installed, will fall back to regular env vars

# Initialize tokenizer for token counting
try:
    # Use cl100k_base tokenizer (used by GPT-4 and GPT-3.5-turbo)
    TOKENIZER = tiktoken.get_encoding("cl100k_base")
except Exception as e:
    logger.warning(f"Failed to initialize tokenizer: {e}. Token counting will be approximate.")
    TOKENIZER = None

def count_tokens(text: str) -> int:
    """Count the number of tokens in a text string."""
    if TOKENIZER:
        return len(TOKENIZER.encode(text))
    else:
        # Fallback approximation: ~4 characters per token
        return len(text) // 4

@dataclass
class StatusTracker:
    """Tracks the status of API requests."""

    time_of_last_rate_limit_error: float = 0
    num_api_errors: int = 0
    num_rate_limit_errors: int = 0
    num_other_errors: int = 0

@dataclass
class DomainAnalysisResult:
    """Stores the results of domain analysis."""

    domains: Dict[str, Any] = field(default_factory=dict)
    raw_response: str = ""
    success: bool = False
    error_message: str = ""

@dataclass
class ChunkProcessingResult:
    """Stores the results of processing a single chunk of functions."""

    chunk_id: int
    domains: Dict[str, Any] = field(default_factory=dict)
    raw_response: str = ""
    success: bool = False
    error_message: str = ""

class DomainAnalyzer:
    """
    Analyzes codebase functions and classifies them into domains and subdomains.

    This class uses GPT-4o-mini to analyze a YAML file containing significant functions
    from a codebase and organize them into a hierarchical structure of domains and subdomains.
    """

    def __init__(
        self,
        api_key: Optional[str] = None,
        model: str = "gpt-4o",
        max_requests_per_minute: float = 50,
        max_tokens_per_minute: float = 100000,
        temperature: float = 0.1,
        use_openrouter: bool = False,
        openrouter_base_url: str = "https://openrouter.ai/api/v1",
    ):
        """
        Initialize the domain analyzer.

        Args:
            api_key: API key (OpenAI or OpenRouter depending on use_openrouter)
            model: Model to use (OpenAI or OpenRouter model ID)
            max_requests_per_minute: Rate limit for API requests
            max_tokens_per_minute: Token rate limit for API
            temperature: Temperature setting for the model (lower = more deterministic)
            use_openrouter: Whether to use OpenRouter instead of OpenAI
            openrouter_base_url: Base URL for OpenRouter API
        """
        # Get the appropriate API key based on the service being used
        use_openrouter = True

        if use_openrouter:
            self.api_key = get_openrouter_api_key(provided_key=api_key)
        else:
            self.api_key = get_openai_api_key(provided_key=api_key)

        self.model = model
        self.max_requests_per_minute = max_requests_per_minute
        self.max_tokens_per_minute = max_tokens_per_minute
        self.temperature = temperature
        self.use_openrouter = use_openrouter
        self.openrouter_base_url = openrouter_base_url
        self.status_tracker = StatusTracker()

        # Initialize OpenRouter client if needed
        self.openrouter_client = None
        if self.use_openrouter:
            # For domain analysis, use Gemini model with large context window
            openrouter_model = "google/gemini-2.5-pro-preview"
            self.openrouter_client = get_openrouter_client(
                # API key will be handled by the client
                model=openrouter_model,  # Override with Gemini model
                max_tokens=60096,  # Use larger context window
                temperature=self.temperature,
                base_url=self.openrouter_base_url
            )
            print(f"Domain Analyzer using OpenRouter with model: {openrouter_model}")

    def _split_functions_into_chunks(self, functions_data: Dict[str, Any], max_tokens_per_chunk: int = 500000) -> List[Dict[str, Any]]:
        """
        Split the functions data into chunks that fit within the token limit.

        Args:
            functions_data: Dictionary containing function data
            max_tokens_per_chunk: Maximum number of tokens per chunk

        Returns:
            List of function data chunks
        """
        # Convert to YAML string to estimate token count
        yaml_str = yaml.dump(functions_data, default_flow_style=False)
        total_tokens = count_tokens(yaml_str)

        logger.info(f"Total tokens in functions data: {total_tokens}")

        # If the total tokens are less than the max, return the whole data as a single chunk
        if total_tokens <= max_tokens_per_chunk:
            logger.info("Functions data fits in a single chunk")
            return [functions_data]

        # Calculate number of chunks needed
        num_chunks = math.ceil(total_tokens / max_tokens_per_chunk)
        logger.info(f"Splitting functions data into {num_chunks} chunks")

        # Get list of function keys
        function_keys = list(functions_data.keys())
        functions_per_chunk = math.ceil(len(function_keys) / num_chunks)

        # Split the functions into chunks
        chunks = []
        for i in range(0, len(function_keys), functions_per_chunk):
            chunk_keys = function_keys[i:i+functions_per_chunk]
            chunk_data = {key: functions_data[key] for key in chunk_keys}
            chunks.append(chunk_data)

        logger.info(f"Created {len(chunks)} chunks with approximately {functions_per_chunk} functions each")
        return chunks

    async def _process_chunk(self, chunk_data: Dict[str, Any], chunk_id: int) -> ChunkProcessingResult:
        """
        Process a single chunk of functions data.

        Args:
            chunk_data: Dictionary containing a subset of function data
            chunk_id: Identifier for this chunk

        Returns:
            ChunkProcessingResult containing the analysis results for this chunk
        """
        logger.info(f"Processing chunk {chunk_id} with {len(chunk_data)} functions")

        # Format the chunk data for the API request
        yaml_str = yaml.dump(chunk_data, default_flow_style=False)

        # Determine appropriate granularity based on estimated chunk size
        estimated_yaml_lines = len(yaml_str.splitlines())
        # Using approximate 5:1 ratio between actual LOC and YAML representation
        estimated_loc = estimated_yaml_lines * 5

        logger.info(f"Chunk {chunk_id}: Estimated YAML lines: {estimated_yaml_lines}, Estimated LOC: {estimated_loc}")

        # Determine granularity guidance based on chunk size
        granularity_guidance = self._get_granularity_guidance(estimated_loc)

        # Create the API request
        logger.info(f"Preparing API request for chunk {chunk_id}")

        # Define the JSON example
        json_example = '''{"areas": [{"name": "Top-Level Area 1", "subareas": [{"name": "Subarea A", "subareas": [{"name": "Sub-subarea i", "subareas": [{"name": "Sub-sub-subarea x", "subareas": []}]}]}]}]}'''

        request = {
            "model": self.model,
            "messages": [
                {
                    "role": "system",
                    "content": f"""You are an expert software architect analyzing a portion of a codebase and its function call graph.

Your task:
Granularity Guidance: "{granularity_guidance}"
- Identify the top-level groups (e.g., domains or areas) as needed.
- For each group, create subgroups, sub-subgroups, and so on. Adapt it as the code structure demands. Follow granularity guidance provided.
- **Do not** list any functions, files, or other artifacts in this output.
- **Do not** include descriptions or explanations in this output.
- The focus here is solely on outlining the hierarchical structure of the code's functionalities.
- The top domains, areas, sub-areas, sub-sub-areas can be as much plenty as needed
- The creation should be logic based, functionality based, and business application based as applicable.
- Note that you are analyzing only a portion of the codebase, so focus on creating a coherent structure for this subset.

**Output Format** (in valid JSON):

{json_example}"""
                },
                {
                    "role": "user",
                    "content": f"""Please analyze these functions and organize them into domains and subdomains:\n\n{yaml_str}

Please return only the nested hierarchy of domains (or areas) without any descriptions or function/file references. Output your hierarchy in valid JSON format, following the outlined format."""
                }
            ],
        }

        # Call the OpenAI API
        logger.info(f"Calling API for chunk {chunk_id} domain classification")
        response_json, error = await self._call_openai_api(request)

        if error:
            logger.error(f"Error processing chunk {chunk_id}: {error}")
            return ChunkProcessingResult(
                chunk_id=chunk_id,
                success=False,
                error_message=error
            )

        # Parse the response
        try:
            # Extract content from the response
            content = response_json["choices"][0]["message"]["content"]

            # Find JSON content between triple backticks
            json_content = content
            if "```json" in content:
                json_content = content.split("```json")[1].split("```")[0].strip()
            elif "```" in content:
                json_content = content.split("```")[1].split("```")[0].strip()

            # Parse the JSON content
            domains_data = json.loads(json_content)

            logger.info(f"Successfully processed chunk {chunk_id}")
            return ChunkProcessingResult(
                chunk_id=chunk_id,
                domains=domains_data,
                raw_response=content,
                success=True
            )

        except Exception as e:
            logger.error(f"Error parsing API response for chunk {chunk_id}: {e}")
            return ChunkProcessingResult(
                chunk_id=chunk_id,
                raw_response=response_json["choices"][0]["message"]["content"] if "choices" in response_json else str(response_json),
                success=False,
                error_message=f"Failed to parse API response: {str(e)}"
            )

    def _get_granularity_guidance(self, estimated_loc: int) -> str:
        """
        Get granularity guidance based on estimated LOC.

        Args:
            estimated_loc: Estimated lines of code

        Returns:
            String containing granularity guidance
        """
        if estimated_loc < 5000:  # Very small codebase
            return "For this very small codebase (<5K LOC), aim for 3-5 top-level domains with 1-2 subdomains each, and a maximum depth of 2 levels."
        elif estimated_loc < 10000:  # ~10K LOC
            return "For this small codebase (~10K LOC), aim for 3-5 top-level domains with 1-3 subdomains each, and a maximum depth of 2-3 levels."
        elif estimated_loc < 25000:  # ~25K LOC
            return "For this medium-small codebase (~25K LOC), aim for 4-5 top-level domains with 2-3 subdomains each, and a maximum depth of 3 levels."
        elif estimated_loc < 50000:  # ~50K LOC
            return "For this medium codebase (~50K LOC), aim for 5-6 top-level domains with 2-4 subdomains each, and a maximum depth of 3-4 levels."
        elif estimated_loc < 100000:  # ~100K LOC
            return "For this medium-large codebase (~100K LOC), aim for 6-8 top-level domains with 3 subdomains each, and a maximum depth of 3 levels."
        elif estimated_loc < 250000:  # ~250K LOC
            return "For this large codebase (~250K LOC), aim for 8-10 top-level domains with 2-3 subdomains each, and a maximum depth of 6 levels."
        elif estimated_loc < 500000:  # ~500K LOC
            return "For this very large codebase (~500K LOC), aim for 10-13 top-level domains with 4-5 subdomains each, and a maximum depth of 8 levels."
        elif estimated_loc < 1000000:  # ~1M LOC
            return "For this extremely large codebase (~1M LOC), aim for 13-15 top-level domains with 5-6 subdomains each, and a maximum depth of 9 levels."
        else:  # >1M LOC
            return "For this massive codebase (>1M LOC), aim for 15-16 top-level domains with 4-5 subdomains each, and a maximum depth of 10 levels."

    async def _merge_domain_results(self, chunk_results: List[ChunkProcessingResult]) -> Dict[str, Any]:
        """
        Merge the domain results from multiple chunks using LLM.

        Args:
            chunk_results: List of ChunkProcessingResult objects

        Returns:
            Merged domains dictionary
        """
        # If there's only one successful chunk, just return its domains
        successful_results = [r for r in chunk_results if r.success and "areas" in r.domains]

        if len(successful_results) == 0:
            logger.warning("No successful chunks to merge")
            return {"areas": []}

        if len(successful_results) == 1:
            logger.info("Only one successful chunk, no need to merge")
            return successful_results[0].domains

        # For multiple chunks, use LLM to merge them
        logger.info(f"Using LLM to merge {len(successful_results)} chunks")

        # Convert all domains to YAML for the LLM
        domains_list = []
        for i, result in enumerate(successful_results):
            domains_yaml = yaml.dump(result.domains, default_flow_style=False)
            domains_list.append(f"Chunk {i+1}:\n{domains_yaml}")

        all_domains = "\n\n---\n\n".join(domains_list)

        # Create the merging request
        request = {
            "model": self.model,
            "messages": [
                {
                    "role": "system",
                    "content": """You are an expert software architect tasked with merging domain hierarchies.

You will be given multiple domain hierarchies that were generated by analyzing different parts of a large codebase.
Each hierarchy is in JSON format with an 'areas' array containing top-level domains.

Your task:
1. Identify similar domains across the different hierarchies
2. Merge them into a single coherent hierarchy
3. Ensure no information is lost during the merge
4. Maintain the same structure (areas with subareas)
5. Ensure the final hierarchy is comprehensive and logical

Output the merged hierarchy in the same JSON format with an 'areas' array at the top level.
"""
                },
                {
                    "role": "user",
                    "content": f"""Here are multiple domain hierarchies generated from different parts of a codebase.
Please merge them into a single coherent hierarchy.\n\n{all_domains}

Please return the merged hierarchy in the same JSON format with an 'areas' array at the top level."""
                }
            ],
        }

        # Call the API
        logger.info("Calling API for initial domain merging")
        response_json, error = await self._call_openai_api(request)

        if error:
            logger.warning(f"LLM-based merging failed: {error}. Falling back to simple merging.")
            # Fall back to simple merging if LLM fails
            return self._simple_merge_domains(successful_results)

        # Parse the response
        try:
            # Extract content from the response
            content = response_json["choices"][0]["message"]["content"]

            # Find JSON content between triple backticks
            json_content = content
            if "```json" in content:
                json_content = content.split("```json")[1].split("```")[0].strip()
            elif "```" in content:
                json_content = content.split("```")[1].split("```")[0].strip()

            # Parse the JSON content
            merged_domains = json.loads(json_content)

            logger.info(f"Successfully merged chunks into {len(merged_domains.get('areas', []))} top-level domains using LLM")
            return merged_domains

        except Exception as e:
            logger.warning(f"Error parsing LLM merging response: {e}. Falling back to simple merging.")
            # Fall back to simple merging if parsing fails
            return self._simple_merge_domains(successful_results)

    def _simple_merge_domains(self, chunk_results: List[ChunkProcessingResult]) -> Dict[str, Any]:
        """
        Simple heuristic-based merging as a fallback when LLM-based merging fails.

        Args:
            chunk_results: List of ChunkProcessingResult objects

        Returns:
            Merged domains dictionary
        """
        # Start with an empty areas list
        merged_domains = {"areas": []}

        # Track domain names to avoid duplicates
        domain_names = set()

        # Process each chunk result
        for result in chunk_results:
            if not result.success or "areas" not in result.domains:
                continue

            # Add each area from this chunk
            for area in result.domains["areas"]:
                area_name = area.get("name", "")
                if not area_name or area_name in domain_names:
                    continue

                domain_names.add(area_name)
                merged_domains["areas"].append(area)

        logger.info(f"Merged results into {len(merged_domains['areas'])} top-level domains using simple merging")
        return merged_domains

    async def create_hierar_domain_struct(self, yaml_path: str, output_path: str, max_tokens_per_chunk: int = 500000, disable_parallel: bool = False, max_concurrent_tasks: int = 0) -> DomainAnalysisResult:
        """
        Analyze a YAML file containing significant functions and classify them into domains.
        For large codebases, the functions are split into chunks and processed in parallel.

        Args:
            yaml_path: Path to the YAML file containing significant functions
            output_path: Path to save the domain classification output
            max_tokens_per_chunk: Maximum tokens per chunk for parallel processing (default: 500000)
            disable_parallel: Whether to disable parallel processing (default: False)
            max_concurrent_tasks: Maximum number of concurrent tasks (default: 0 = auto-detect based on CPU cores)

        Returns:
            DomainAnalysisResult containing the analysis results
        """
        # Read the YAML file
        logger.info(f"Reading YAML file: {yaml_path}")
        try:
            with open(yaml_path, 'r') as f:
                yaml_data = yaml.safe_load(f)
        except Exception as e:
            logger.error(f"Error reading YAML file: {e}")
            return DomainAnalysisResult(
                success=False,
                error_message=f"Failed to read YAML file: {str(e)}"
            )

        if not yaml_data:
            logger.error("YAML file is empty or could not be parsed")
            return DomainAnalysisResult(
                success=False,
                error_message="YAML file is empty or could not be parsed"
            )

        # Estimate the size of the YAML data
        yaml_str = yaml.dump(yaml_data, default_flow_style=False)
        total_tokens = count_tokens(yaml_str)
        logger.info(f"Total tokens in YAML data: {total_tokens}")

        # Determine if we need to split the data
        if total_tokens <= max_tokens_per_chunk or disable_parallel:
            # Small enough to process in one go or parallel processing is disabled
            if disable_parallel:
                logger.info("Parallel processing disabled, processing YAML data in a single request")
            else:
                logger.info("Processing YAML data in a single request")
            result = await self.significant_fns_to_domain_structs(yaml_data)
        else:
            # Split into chunks and process in parallel
            logger.info(f"YAML data exceeds token limit ({total_tokens} > {max_tokens_per_chunk}), splitting into chunks")
            chunks = self._split_functions_into_chunks(yaml_data, max_tokens_per_chunk)

            # Process chunks in parallel with rate limiting
            if max_concurrent_tasks <= 0:
                # Auto-detect based on CPU cores if not specified
                concurrent_tasks = min(8, os.cpu_count() or 4)  # Limit to 8 or number of CPU cores
            else:
                concurrent_tasks = max_concurrent_tasks

            logger.info(f"Processing {len(chunks)} chunks with max {concurrent_tasks} concurrent tasks")

            # Create a semaphore to limit concurrent tasks
            semaphore = asyncio.Semaphore(concurrent_tasks)

            async def process_with_semaphore(chunk, chunk_id):
                async with semaphore:
                    return await self._process_chunk(chunk, chunk_id)

            # Create tasks with semaphore
            tasks = []
            for i, chunk in enumerate(chunks):
                task = asyncio.create_task(process_with_semaphore(chunk, i))
                tasks.append(task)

            # Process all chunks with rate limiting
            chunk_results = await asyncio.gather(*tasks)

            # Check if all chunks were processed successfully
            successful_chunks = sum(1 for r in chunk_results if r.success)
            logger.info(f"Successfully processed {successful_chunks} out of {len(chunks)} chunks")

            if successful_chunks == 0:
                # All chunks failed
                error_messages = [r.error_message for r in chunk_results if not r.success]
                return DomainAnalysisResult(
                    success=False,
                    error_message=f"All chunks failed processing: {'; '.join(error_messages[:3])}..."
                )

            # Merge the results from all successful chunks
            merged_domains = await self._merge_domain_results([r for r in chunk_results if r.success])

            # Create a combined raw response
            combined_raw = "\n\n".join([f"Chunk {r.chunk_id}:\n{r.raw_response}" for r in chunk_results if r.success])

            # Create the final result
            result = DomainAnalysisResult(
                domains=merged_domains,
                raw_response=combined_raw,
                success=True
            )

            # If we have enough successful chunks, perform a final consolidation step
            if successful_chunks > 1 and successful_chunks >= len(chunks) * 0.7:  # At least 70% success rate
                logger.info("Performing final consolidation step to merge similar domains")
                result = await self._consolidate_domains(result)

        # Save the results if successful
        if result.success and output_path:
            try:
                with open(output_path, 'w') as f:
                    yaml.dump(result.domains, f, default_flow_style=False)
                logger.info(f"Domain classification saved to: {output_path}")

                # Also save the raw response for reference
                with open(f"{output_path}.raw", 'w') as f:
                    f.write(result.raw_response)
                logger.info(f"Raw response saved to: {output_path}.raw")
            except Exception as e:
                logger.error(f"Error saving results: {e}")
                result.error_message = f"Analysis succeeded but failed to save results: {str(e)}"

        return result

    async def _consolidate_domains(self, result: DomainAnalysisResult) -> DomainAnalysisResult:
        """
        Perform a final consolidation step to merge similar domains.

        Args:
            result: Initial domain analysis result with potentially redundant domains

        Returns:
            Consolidated domain analysis result
        """
        # Convert the domains to YAML for the LLM
        domains_yaml = yaml.dump(result.domains, default_flow_style=False)

        # Create the consolidation request
        request = {
            "model": self.model,
            "messages": [
                {
                    "role": "system",
                    "content": """You are an expert software architect tasked with consolidating domain hierarchies.

You will be given a domain hierarchy that was generated by analyzing a large codebase in chunks.
This may have resulted in some redundancy or overlap between domains.

Your task:
1. Identify similar or redundant domains and merge them
2. Ensure the hierarchy remains coherent and logical
3. Maintain the same overall structure and naming conventions
4. Remove any duplicated subdomains
5. Ensure the final hierarchy is comprehensive and covers all functionality

Output the consolidated hierarchy in the same JSON format as the input.
"""
                },
                {
                    "role": "user",
                    "content": f"""Here is a domain hierarchy that was generated by analyzing a large codebase in chunks.
Please consolidate it by merging similar domains and removing redundancy while maintaining comprehensive coverage.\n\n{domains_yaml}

Please return the consolidated hierarchy in the same JSON format."""
                }
            ],
        }

        # Call the OpenAI API
        logger.info("Calling API for domain consolidation")
        response_json, error = await self._call_openai_api(request)

        if error:
            logger.warning(f"Domain consolidation failed: {error}. Using original domains.")
            return result

        # Parse the response
        try:
            # Extract content from the response
            content = response_json["choices"][0]["message"]["content"]

            # Find JSON content between triple backticks
            json_content = content
            if "```json" in content:
                json_content = content.split("```json")[1].split("```")[0].strip()
            elif "```" in content:
                json_content = content.split("```")[1].split("```")[0].strip()

            # Parse the JSON content
            consolidated_domains = json.loads(json_content)

            logger.info("Successfully consolidated domains")
            return DomainAnalysisResult(
                domains=consolidated_domains,
                raw_response=result.raw_response + "\n\n=== CONSOLIDATION ===\n\n" + content,
                success=True
            )

        except Exception as e:
            logger.warning(f"Error parsing consolidation response: {e}. Using original domains.")
            return result

    async def significant_fns_to_domain_structs(self, functions_data: Dict[str, Any]) -> DomainAnalysisResult:
        """
        Analyze functions data and classify them into domains and subdomains.

        Args:
            functions_data: Dictionary containing function data

        Returns:
            DomainAnalysisResult containing the analysis results
        """
        # Format the functions data for the API request
        yaml_str = yaml.dump(functions_data, default_flow_style=False)

        # Determine appropriate granularity based on estimated codebase size
        estimated_yaml_lines = len(yaml_str.splitlines())
        # Using approximate 5:1 ratio between actual LOC and YAML representation
        estimated_loc = estimated_yaml_lines * 5

        logger.info(f"Estimated YAML lines: {estimated_yaml_lines}, Estimated codebase LOC: {estimated_loc}")

        # Get granularity guidance based on estimated LOC
        granularity_guidance = self._get_granularity_guidance(estimated_loc)

        # Create the API request
        logger.info("Preparing API request for domain classification")

        # Define the JSON example separately
        json_example = '''{
  "areas": [
    {
      "name": "Top-Level Area 1",
      "subareas": [
        {
          "name": "Subarea A",
          "subareas": [
            {
              "name": "Sub-subarea i",
              "subareas": [
                {
                  "name": "Sub-sub-subarea x",
                  "subareas": []
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}'''

        request = {
            "model": self.model,
            "messages": [
                {
                    "role": "system",
                    "content": f"""You are an expert software architect analyzing an entire codebase and its function call graph.

Your task:
Granularity Guidance: "{granularity_guidance}"
- Identify as the top-level groups (e.g., domains or areas) as needed.
- For each group, create subgroups, sub-subgroups, and so on. Adapt it as the code structure demands. Follow granularity guidance provided.
- **Do not** list any functions, files, or other artifacts in this output.
- **Do not** include descriptions or explanations in this output.
- The focus here is solely on outlining the hierarchical structure of the code's functionalities.
- The top domains, areas, sub-areas, sub-sub-areas can be as much plenty as needed
- The creation should be logic based, functionality based, and business application based as applicable.


**Output Format** (in valid JSON):

{json_example}"""
                },
                {
                    "role": "user",
                    "content": f"""Please analyze these functions and organize them into domains and subdomains:\n\n{yaml_str}

Please return only the nested hierarchy of domains (or areas) without any descriptions or function/file references. Output your hierarchy in valid JSON format, following the outlined format."""
                }
            ],
            # "temperature": self.temperature,
            # "max_tokens": 10000
        }

        # Call the API
        logger.info("Calling API for domain classification")
        response_json, error = await self._call_openai_api(request)

        if error:
            return DomainAnalysisResult(
                success=False,
                error_message=error
            )

        # Parse the response
        try:
            # Extract content from the response
            content = response_json["choices"][0]["message"]["content"]

            # Find JSON content between triple backticks
            json_content = content
            if "```json" in content:
                json_content = content.split("```json")[1].split("```")[0].strip()
            elif "```" in content:
                json_content = content.split("```")[1].split("```")[0].strip()

            # Parse the JSON content
            domains_data = json.loads(json_content)

            return DomainAnalysisResult(
                domains=domains_data,
                raw_response=content,
                success=True
            )

        except Exception as e:
            logger.error(f"Error parsing API response: {e}")
            return DomainAnalysisResult(
                raw_response=response_json["choices"][0]["message"]["content"] if "choices" in response_json else str(response_json),
                success=False,
                error_message=f"Failed to parse API response: {str(e)}"
            )

    async def _call_openai_api(self, request: Dict[str, Any]) -> Tuple[Dict[str, Any], Optional[str]]:
        """
        Call the OpenAI API or OpenRouter API with rate limiting and error handling.

        Args:
            request: API request payload

        Returns:
            Tuple of (response_json, error_message)
        """
        # Use OpenRouter client if available and enabled
        if self.use_openrouter and self.openrouter_client:
            try:
                # Extract messages from request JSON
                messages = request.get("messages", [])
                system_prompt = None
                user_prompt = None

                # Extract system and user prompts from messages
                for message in messages:
                    if message["role"] == "system":
                        system_prompt = message["content"]
                    elif message["role"] == "user":
                        user_prompt = message["content"]

                if not user_prompt:
                    return {}, "No user prompt found in request"

                # Call the OpenRouter client
                content = await self.openrouter_client.generate(
                    prompt=user_prompt,
                    system_prompt=system_prompt,
                    temperature=self.temperature
                )

                # Create a response in the same format as OpenAI
                response_json = {
                    "choices": [
                        {
                            "message": {
                                "content": content
                            }
                        }
                    ]
                }

                return response_json, None

            except Exception as e:
                logger.error(f"OpenRouter API error: {e}")
                return {}, f"OpenRouter API error: {str(e)}"
        else:
            # Use OpenAI API
            max_retries = 5
            retry_delay = 5  # seconds
            seconds_to_pause_after_rate_limit_error = 15

            for attempt in range(max_retries):
                # Check if we need to pause due to a recent rate limit error
                seconds_since_rate_limit_error = time.time() - self.status_tracker.time_of_last_rate_limit_error
                if seconds_since_rate_limit_error < seconds_to_pause_after_rate_limit_error:
                    remaining_seconds_to_pause = seconds_to_pause_after_rate_limit_error - seconds_since_rate_limit_error
                    logger.warning(f"Pausing for {remaining_seconds_to_pause:.1f} seconds due to recent rate limit error")
                    await asyncio.sleep(remaining_seconds_to_pause)

                try:
                    async with aiohttp.ClientSession() as session:
                        async with session.post(
                            "https://api.openai.com/v1/chat/completions",
                            headers={
                                "Authorization": f"Bearer {self.api_key}",
                                "Content-Type": "application/json"
                            },
                            json=request,
                            timeout=aiohttp.ClientTimeout(total=120)  # Using proper ClientTimeout object
                        ) as response:
                            response_json = await response.json()

                            if response.status == 200:
                                return response_json, None

                            # Handle errors
                            error_message = response_json.get("error", {}).get("message", f"API error: {response.status}")

                            if response.status == 429:  # Rate limit error
                                self.status_tracker.num_rate_limit_errors += 1
                                self.status_tracker.time_of_last_rate_limit_error = time.time()
                                logger.warning(f"Rate limit error: {error_message}")
                                retry_delay = min(retry_delay * 2, 60)  # Exponential backoff, max 60 seconds
                            else:
                                self.status_tracker.num_api_errors += 1
                                logger.error(f"API error (status {response.status}): {error_message}")

                            if attempt < max_retries - 1:
                                logger.info(f"Retrying in {retry_delay} seconds (attempt {attempt+1}/{max_retries})")
                                await asyncio.sleep(retry_delay)
                                retry_delay = min(retry_delay * 2, 60)  # Exponential backoff, max 60 seconds
                            else:
                                return {}, f"API error after {max_retries} attempts: {error_message}"

                except asyncio.TimeoutError:
                    logger.error(f"Request timed out (attempt {attempt+1}/{max_retries})")
                    self.status_tracker.num_other_errors += 1
                    if attempt < max_retries - 1:
                        await asyncio.sleep(retry_delay)
                        retry_delay = min(retry_delay * 2, 60)
                    else:
                        return {}, f"Request timed out after {max_retries} attempts"

                except Exception as e:
                    logger.error(f"Request error: {e}")
                    self.status_tracker.num_other_errors += 1
                    if attempt < max_retries - 1:
                        await asyncio.sleep(retry_delay)
                        retry_delay = min(retry_delay * 2, 60)
                    else:
                        return {}, f"Request failed after {max_retries} attempts: {str(e)}"

            return {}, f"Failed to get response after {max_retries} attempts"

class DomainAnalysisIntegration:
    """
    Integration class for adding domain analysis to the repository analysis flow.

    This class provides methods that can be called from the repository analysis flow
    to add domain analysis as an additional step.
    """

    @staticmethod
    async def domains_from_significant_functions(
        yaml_path: str,
        output_path: str,
        api_key: Optional[str] = None,
        model: str = "o3-mini",
        max_requests_per_minute: float = 5000,
        max_tokens_per_minute: float = 15000000,
        use_openrouter: bool = False,
        openrouter_base_url: str = "https://openrouter.ai/api/v1",
        max_tokens_per_chunk: int = 500000,
        disable_parallel: bool = False,
        max_concurrent_tasks: int = 0,
    ) -> bool:
        """
        Analyze significant functions from a YAML file and generate domain classification.

        Args:
            yaml_path: Path to the YAML file containing significant functions
            output_path: Path to save the domain classification output
            api_key: OpenAI API key (if None, will try to get from environment)
            model: OpenAI model to use
            max_requests_per_minute: Rate limit for API requests
            max_tokens_per_minute: Token rate limit for API
            max_tokens_per_chunk: Maximum tokens per chunk for parallel processing
            disable_parallel: Whether to disable parallel processing
            max_concurrent_tasks: Maximum number of concurrent tasks (0 = auto-detect based on CPU cores)

        Returns:
            True if analysis was successful, False otherwise
        """
        try:
            analyzer = DomainAnalyzer(
                api_key=api_key,
                model=model,
                max_requests_per_minute=max_requests_per_minute,
                max_tokens_per_minute=max_tokens_per_minute,
                use_openrouter=use_openrouter,
                openrouter_base_url=openrouter_base_url,
            )

            result = await analyzer.create_hierar_domain_struct(
                yaml_path,
                output_path,
                max_tokens_per_chunk=max_tokens_per_chunk,
                disable_parallel=disable_parallel,
                max_concurrent_tasks=max_concurrent_tasks
            )
            return result.success

        except Exception as e:
            logger.error(f"Error in domain analysis: {e}")
            return False

async def main():
    """Main entry point for the domain analyzer."""
    parser = argparse.ArgumentParser(description="Analyze codebase functions and classify them into domains")
    parser.add_argument("--yaml", help="Path to the YAML file containing significant functions")
    parser.add_argument("--output", help="Path to save the domain classification output")
    parser.add_argument("--api-key", help="API key (if not provided, will try to get from environment)")
    parser.add_argument("--model", default="gpt-4o-mini", help="Model to use (OpenAI or OpenRouter model ID)")
    parser.add_argument("--requests-per-minute", type=float, default=15000, help="Rate limit for API requests")
    parser.add_argument("--tokens-per-minute", type=float, default=62500000, help="Token rate limit for API")
    parser.add_argument("--temperature", type=float, default=0.2, help="Temperature setting for the model")
    parser.add_argument("--use-openrouter", action="store_true", help="Use OpenRouter instead of OpenAI")
    parser.add_argument("--openrouter-base-url", default="https://openrouter.ai/api/v1", help="Base URL for OpenRouter API")
    parser.add_argument("--max-tokens-per-chunk", type=int, default=500000, help="Maximum tokens per chunk for parallel processing")
    parser.add_argument("--disable-parallel", action="store_true", help="Disable parallel processing for large codebases")
    parser.add_argument("--max-concurrent-tasks", type=int, default=0, help="Maximum number of concurrent tasks (0 = auto-detect based on CPU cores)")

    args = parser.parse_args()

    try:
        analyzer = DomainAnalyzer(
            api_key=args.api_key,
            model=args.model,
            max_requests_per_minute=args.requests_per_minute,
            max_tokens_per_minute=args.tokens_per_minute,
            temperature=args.temperature,
            use_openrouter=args.use_openrouter,
            openrouter_base_url=args.openrouter_base_url,
        )

        result = await analyzer.create_hierar_domain_struct(
            args.yaml,
            args.output,
            max_tokens_per_chunk=args.max_tokens_per_chunk,
            disable_parallel=args.disable_parallel,
            max_concurrent_tasks=args.max_concurrent_tasks
        )

        if result.success:
            logger.info("Domain analysis completed successfully")
            return 0
        else:
            logger.error(f"Domain analysis failed: {result.error_message}")
            return 1

    except Exception as e:
        logger.error(f"Error in domain analysis: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
