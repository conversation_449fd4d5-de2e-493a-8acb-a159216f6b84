import time
import os
import logging
import argparse
import pandas as pd
import json
import yaml
from typing import Optional, Any, Dict

# Import API key management
from bracket_core.llm.api_keys import get_openai_api_key, get_anthropic_api_key, get_openrouter_api_key

from bracket_core.parsing_repomap import EnhancedRepoMap, SimpleTokenCounter, SimpleIO
from bracket_core.documenting import convert_parquet_to_simplified_csv, document_functions_with_significance
# Import the new hybrid knowledge graph implementation
from bracket_core.hybrid_kg import generate_hybrid_knowledge_graph, graph_to_dataframes as hybrid_graph_to_dataframes
# Import the domain analysis functionality
from bracket_core.domain_analysis import DomainAnalysisIntegration
# Import the domain trace builder
from bracket_core.domain_trace_builder import DomainTraceBuilder
from bracket_core.enhanced_domain_trace_builder import EnhancedDomainTraceBuilderIntegration
# Import the file call graph builder
from bracket_core.file_call_graph_builder import FileCallGraphBuilder
# Import the domain file mapper
from bracket_core.domain_file_mapper import DomainFileMapperIntegration
# Import the enhanced domain diagram generator
from enhanced_domain_diagram_generator import EnhancedDomainDiagramGenerator
# Import the domain taxonomy generator
from bracket_core.generate_domain_taxonomy import generate_domain_taxonomy
# Import the run_hierarchical_mapper
from bracket_core.run_hierarchical_mapper import run_mapper
# Import the codebase explainer
from bracket_core.global_codebase_explainer import generate_codebase_explanation

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RepoAnalysisFlow:
    """
    Centralized workflow that combines Knowledge Graph generation, Call Graph integration,
    and Semantic Layer documentation into a single coherent process.
    """

    def __init__(
        self,
        repo_dir: str,
        output_dir: str,
        verbose: bool = False,
        document_functions: bool = True,
        analyze_domains: bool = True,
        use_hierarchical_domains: bool = True,
        skip_file_mapping: bool = False,  # Flag to skip file mapping entirely
        force_original_approach: bool = False,  # Flag to force original approach without file mapping
        llm_requests_per_minute: float = 1200,
        llm_tokens_per_minute: float = 1000000,
        generate_diagrams: bool = True,
        generate_explanation: bool = True,
        # Diagram model selection
        diagram_model_type: str = "openai",  # "claude" or "openai"
        # Claude parameters
        claude_api_key: Optional[str] = None,
        claude_model: str = "claude-3-5-sonnet-20241022",
        # OpenAI parameters
        openai_api_key: Optional[str] = None,
        openai_model: str = "gpt-4o-mini",
        explanation_model: str = "gpt-4o-mini",
        # OpenRouter parameters
        use_openrouter: bool = False,
        openrouter_base_url: str = "https://openrouter.ai/api/v1",
        # Parallelization parameters
        max_concurrent_tasks: int = 5,
    ):
        """
        Initialize the repository analysis flow.

        Args:
            repo_dir: Path to the repository to analyze
            output_dir: Directory to save output artifacts
            verbose: Enable verbose logging
            document_functions: Whether to generate function documentation
            analyze_domains: Whether to perform domain analysis on significant functions
            llm_requests_per_minute: Rate limit for LLM API requests
            llm_tokens_per_minute: Token rate limit for LLM API
            generate_diagrams: Whether to generate mermaid diagrams
            diagram_model_type: Type of model to use for diagrams ("claude" or "openai")
            claude_api_key: Anthropic API key for Claude. If None, will try to get from environment.
            claude_model: Claude model to use for diagram generation
            openai_api_key: OpenAI API key for diagrams. If None, will try to get from environment.
            openai_model: OpenAI model to use for diagram generation
            max_concurrent_tasks: Maximum number of concurrent tasks for parallel processing
        """
        self.repo_dir = repo_dir
        self.output_dir = output_dir
        self.verbose = verbose
        self.document_functions = document_functions
        self.enable_domain_analysis = analyze_domains
        self.use_hierarchical_domains = use_hierarchical_domains
        self.skip_file_mapping = skip_file_mapping  # Skip file mapping entirely
        self.force_original_approach = force_original_approach  # Force original approach without file mapping
        self.llm_requests_per_minute = llm_requests_per_minute
        self.llm_tokens_per_minute = llm_tokens_per_minute
        self.generate_diagrams = generate_diagrams
        self.generate_explanation = generate_explanation

        # Diagram model configuration
        self.diagram_model_type = diagram_model_type
        # Claude parameters
        self.claude_api_key = claude_api_key
        self.claude_model = claude_model
        # OpenAI parameters
        self.openai_api_key = openai_api_key
        self.openai_model = openai_model
        self.explanation_model = explanation_model
        # OpenRouter parameters
        self.use_openrouter = use_openrouter
        self.openrouter_base_url = openrouter_base_url
        # Parallelization parameters
        self.max_concurrent_tasks = max_concurrent_tasks

        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)

        # Initialize components
        self.repo_map = EnhancedRepoMap(
            root=repo_dir,
            verbose=verbose,
            main_model=SimpleTokenCounter(),
            io=SimpleIO(),
        )

        # Paths for intermediate artifacts with more descriptive names
        self.kg_output_path = os.path.join(output_dir, "code_knowledge_graph")
        self.documented_output_path = os.path.join(output_dir, "semantic_documented_functions.parquet")
        self.final_output_path = os.path.join(output_dir, "unified_codebase_representation.csv")
        self.final_edges_csv_path = os.path.join(output_dir, "intermediate_representation_layer.csv")
        self.significant_functions_yaml = os.path.join(output_dir, "significant_functions.yaml")
        self.domain_analysis_yaml = os.path.join(output_dir, "domain_analysis.yaml")
        self.file_call_graph_yaml = os.path.join(output_dir, "file_call_graph.yaml")
        self.domain_file_mappings_yaml = os.path.join(output_dir, "domain_file_mappings.yaml")
        self.domain_traces_yaml = os.path.join(output_dir, "domain_traces.yaml")
        self.domain_diagrams_dir = os.path.join(output_dir, "domain_diagrams")
        self.domain_taxonomy_json = os.path.join(output_dir, "domain_taxonomy.json")
        self.codebase_explanation_md = os.path.join(output_dir, "codebase_explanation.md")

    async def run(self) -> pd.DataFrame:
        """
        Execute the complete repository analysis workflow.

        Returns:
            DataFrame containing the final combined artifact
        """
        logger.info(f"Starting repository analysis for: {self.repo_dir}")

        # Step 1: Generate Knowledge Graph
        logger.info("Step 1: Generating Knowledge Graph")
        nx_graph = self.generate_knowledge_graph()

        # Step 2: Save graph as parquet for documentation step
        logger.info("Step 2: Saving intermediate graph artifacts")
        nodes_df = self.save_graph_as_parquet(nx_graph, self.kg_output_path)

        # Define path for intermediate file
        documented_csv_path = os.path.join(self.output_dir, "semantic_documented_functions_simplified.csv")

        # Step 3: Generate semantic documentation layer if enabled
        if self.document_functions:
            logger.info("Step 3: Generating semantic documentation layer")
            await self.generate_documentation(nodes_df)

            try:
                # Step 3b: Converting documented parquet to simplified CSV
                logger.info("Step 3b: Converting documented parquet to simplified CSV")
                convert_parquet_to_simplified_csv(
                    parquet_path=self.documented_output_path,
                    output_csv_path=documented_csv_path
                )

                # Step 3c: Generate YAML file for architecturally significant functions
                logger.info("Step 3c: Generating YAML file for architecturally significant functions")
                self.generate_significant_functions_yaml()

                # Step 3d: Analyze domains and subdomains if enabled
                if self.enable_domain_analysis:
                    logger.info("Step 3d: Creating domains and subdomains")
                    # Will use gemini for this
                    success = await self.create_hierarchical_domains()

                    # Step 3e: Generate file-driven call graph if domain analysis was successful
                    if success:
                        logger.info("Step 3e: Generating file-driven call graph")
                        file_graph_success = await self.build_file_call_graph()

                        # Step 3f: Map files to domains if file call graph was generated successfully and not skipped
                        if file_graph_success and not self.skip_file_mapping:
                            logger.info("Step 3f: Mapping files to domains")
                            # Will use gemini for the top domain and rest -> 4o-mini
                            # Maybe leaf node function mapping can be done better with gemini/bigger model? Will test it.
                            mapping_success = await self.map_files_to_domains()

                            # Step 3g: Build domain traces and classify functions if mapping was successful
                            if mapping_success:
                                logger.info("Step 3g: Building domain traces and classifying functions")
                                await self.build_domain_traces(use_enhanced=not self.force_original_approach)
                            else:
                                logger.info("Domain-to-file mapping failed, falling back to original approach")
                                await self.build_domain_traces(use_enhanced=False)
                        elif file_graph_success and self.skip_file_mapping:
                            # Skip file mapping and go directly to domain trace building
                            logger.info("Step 3f: Skipping file mapping as requested")
                            logger.info("Step 3g: Building domain traces and classifying functions")
                            await self.build_domain_traces(use_enhanced=False)
                        else:
                            logger.info("File call graph generation failed, falling back to original approach")
                            await self.build_domain_traces(use_enhanced=False)
                    else:
                        logger.info("Domain analysis failed, skipping domain trace building")
                else:
                    logger.info("Domain analysis is disabled, skipping")
            except Exception as e:
                logger.error(f"Error in documentation post-processing: {e}")
                logger.info("Continuing with analysis despite documentation processing error")

        # Step 4: Create final unified representation
        logger.info("Step 4: Creating unified code representation")
        final_df = self.create_final_artifact(nodes_df)

        # Save final output
        final_df.to_csv(self.final_output_path, index=False)
        logger.info(f"Repository analysis complete. Unified code representation saved to: {self.final_output_path}")

        return final_df

    def generate_knowledge_graph(self) -> Any:
        """
        Generate the Knowledge Graph using the hybrid knowledge graph approach.

        Returns:
            NetworkX graph representing the repository (nodes only, no edges)
        """
        logger.info(f"Analyzing repository structure: {self.repo_dir}")

        # Use the new hybrid knowledge graph implementation
        logger.info("Using hybrid knowledge graph generation")
        nx_graph = generate_hybrid_knowledge_graph(self.repo_dir, verbose=self.verbose)

        # Normalize node attributes if needed
        for node_id, data in list(nx_graph.nodes(data=True)):
            # Ensure all expected fields are present
            if 'node_id' not in data:
                data['node_id'] = node_id

            # Add id field for compatibility
            if 'id' not in data:
                data['id'] = node_id

        logger.info(f"Knowledge Graph created with {len(nx_graph.nodes)} nodes")
        return nx_graph

    def save_graph_as_parquet(self, nx_graph, output_path):
        """
        Save the graph nodes as parquet file.

        Args:
            nx_graph: NetworkX graph
            output_path: Output path prefix

        Returns:
            DataFrame containing node information
        """

        # Convert graph to dataframe using the hybrid implementation
        nodes_df = hybrid_graph_to_dataframes(nx_graph)

        # Function to convert mixed types to strings
        def convert_mixed_types(df):
            for col in df.columns:
                if df[col].dtype == 'object':
                    df[col] = df[col].apply(
                        lambda x: json.dumps(x) if isinstance(x, (dict, list)) else str(x) if x is not None else None
                    )
            return df

        # Convert all object columns to consistent string format
        nodes_df = convert_mixed_types(nodes_df)

        # Save to parquet
        nodes_df.to_parquet(f"{output_path}_nodes.parquet")

        return nodes_df

    async def generate_documentation(self, _: pd.DataFrame) -> None:
        """
        Generate semantic documentation for functions in the graph.

        Args:
            nodes_df: DataFrame containing function nodes
        """
        logger.info("Documenting functions with LLM")
        nodes_parquet_path = f"{self.kg_output_path}_nodes.parquet"

        # Read the nodes parquet to extract call contexts
        _ = pd.read_parquet(nodes_parquet_path)  # Read to verify file exists

        # Use the significance evaluation approach with call contexts
        await document_functions_with_significance(
            parquet_file=nodes_parquet_path,
            save_filepath=self.documented_output_path,
            max_requests_per_minute=self.llm_requests_per_minute,
            max_tokens_per_minute=self.llm_tokens_per_minute,
            selection_method="all",  # Process all functions
            save_all_rows=True,
        )

        logger.info(f"Function documentation complete. Results saved to: {self.documented_output_path}")

    def create_final_artifact(self, nodes_df: pd.DataFrame) -> pd.DataFrame:
        """
        Create the final unified representation from nodes.

        Args:
            nodes_df: DataFrame containing node information

        Returns:
            DataFrame containing the unified representation
        """
        logger.info("Creating final unified representation")

        # Start with the nodes dataframe as our base
        final_df = nodes_df.copy()

        # Add a column to indicate this is from the knowledge graph
        final_df['source'] = 'knowledge_graph'

        # In our new approach, we don't have explicit edges
        # Instead, we rely on the 'calls' and 'call_contexts' columns in the nodes

        # If we have documentation, merge it in
        if self.document_functions and os.path.exists(self.documented_output_path):
            try:
                doc_df = pd.read_parquet(self.documented_output_path)
                # Identify the key columns for merging
                merge_cols = [col for col in doc_df.columns if col in final_df.columns and col != 'description']
                if merge_cols:
                    # Merge documentation into final dataframe
                    final_df = final_df.merge(
                        doc_df[merge_cols + ['description']],
                        on=merge_cols,
                        how='left'
                    )
                    logger.info("Successfully merged documentation into final artifact")
                else:
                    logger.warning("Could not identify common columns for merging documentation")
            except Exception as e:
                logger.error(f"Error merging documentation: {e}")

        return final_df

    def generate_significant_functions_yaml(self) -> None:
        """
        Generate a YAML file containing architecturally significant functions with their
        signatures, descriptions, and filtered call contexts.
        """
        logger.info("Generating YAML file for architecturally significant functions")

        # Check if the documented parquet file exists
        if not os.path.exists(self.documented_output_path):
            logger.error(f"Documented parquet file not found: {self.documented_output_path}")
            return

        try:
            # Read the documented parquet file
            doc_df = pd.read_parquet(self.documented_output_path)

            # Filter for architecturally significant functions
            if 'is_architecturally_significant' in doc_df.columns:
                significant_df = doc_df[doc_df['is_architecturally_significant'] == True].copy()
                logger.info(f"Found {len(significant_df)} architecturally significant functions")
            else:
                logger.warning("No 'is_architecturally_significant' column found in the parquet file")
                return

            if len(significant_df) == 0:
                logger.warning("No architecturally significant functions found")
                return

            # Create YAML structure
            yaml_data: Dict[str, Dict] = {}

            for _, row in significant_df.iterrows():
                # Get signature, description, and call_contexts
                signature = row.get('signature', '')
                if not signature:
                    # If signature is empty, use node_id as a fallback
                    signature = row.get('node_id', f"unknown_function_{_}")

                description = row.get('description', '')

                # Use filtered_call_contexts if available, otherwise fall back to call_contexts
                filtered_call_contexts = row.get('filtered_call_contexts', row.get('call_contexts', []))
                if isinstance(filtered_call_contexts, str):
                    try:
                        # Try to parse as JSON if it's a string representation
                        filtered_call_contexts = json.loads(filtered_call_contexts)
                    except json.JSONDecodeError:
                        # If not valid JSON, treat as a single string
                        filtered_call_contexts = [filtered_call_contexts] if filtered_call_contexts else []

                # calls = row.get('calls', [])
                # if isinstance(calls, str):
                #     try:
                #         # Try to parse as JSON if it's a string representation
                #         calls = json.loads(calls)
                #     except json.JSONDecodeError:
                #         # If not valid JSON, treat as a single string
                #         calls = [calls] if calls else []


                # Add to YAML data
                # yaml_data[signature] = {
                #     'description': description,
                #     'call_contexts': filtered_call_contexts if filtered_call_contexts and filtered_call_contexts != [] else None
                #     # 'calls': calls,
                # }
                yaml_data[signature] = {
                    'description': description,
                }
                # Only add call_contexts if it has meaningful content
                if filtered_call_contexts and filtered_call_contexts != []:
                    yaml_data[signature]['call_contexts'] = filtered_call_contexts

            # Write to YAML file
            with open(self.significant_functions_yaml, 'w') as f:
                yaml.dump(yaml_data, f, default_flow_style=False, sort_keys=False)

            logger.info(f"YAML file generated: {self.significant_functions_yaml}")

        except Exception as e:
            logger.error(f"Error generating YAML file: {e}")
            import traceback
            logger.error(traceback.format_exc())

    async def create_hierarchical_domains(self) -> bool:
        """
        Analyze the significant functions YAML file to identify domains and subdomains.

        This method uses Gemini 2.5 1M Context Window to analyze the significant functions and organize them
        into a hierarchical structure of domains and subdomains.

        Returns:
            bool: True if domain analysis was successful, False otherwise
        """
        logger.info("Step 3d: Analyzing domains and subdomains")

        # Check if the significant functions YAML file exists
        if not os.path.exists(self.significant_functions_yaml):
            logger.error(f"Significant functions YAML file not found: {self.significant_functions_yaml}")
            return False

        try:
            # Perform domain analysis with parallel processing enabled
            success = await DomainAnalysisIntegration.domains_from_significant_functions(
                yaml_path=self.significant_functions_yaml,
                output_path=self.domain_analysis_yaml,
                # API keys will be handled by the function using centralized management
                model=self.openai_model,
                max_requests_per_minute=self.llm_requests_per_minute,
                max_tokens_per_minute=self.llm_tokens_per_minute,
                use_openrouter=True, # We want to use Gemini
                openrouter_base_url=self.openrouter_base_url,
                # Enable parallel processing for large codebases
                max_tokens_per_chunk=500000,
                disable_parallel=False,
                max_concurrent_tasks=self.max_concurrent_tasks,
            )

            if success:
                logger.info(f"Domain analysis complete. Results saved to: {self.domain_analysis_yaml}")
                return True
            else:
                logger.error("Domain analysis failed")
                return False

        except Exception as e:
            logger.error(f"Error in domain analysis: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    async def build_file_call_graph(self) -> bool:
        """
        Generate a file-driven call graph from the documented functions parquet file.

        This method reads the documented functions parquet file and generates a more compact
        file-driven call graph (CG2) that groups functions by file.

        Returns:
            bool: True if file call graph generation was successful, False otherwise
        """
        logger.info("Generating file-driven call graph")

        # Check if the documented functions parquet file exists
        if not os.path.exists(self.documented_output_path):
            logger.error(f"Documented functions parquet file not found: {self.documented_output_path}")
            return False

        try:
            # Create a file call graph builder
            builder = FileCallGraphBuilder(
                functions_parquet_path=self.documented_output_path,
                output_path=self.file_call_graph_yaml
            )

            # Build file call graph
            result = builder.build_file_call_graph()

            if result.success:
                logger.info(f"File call graph generation complete. Results saved to: {self.file_call_graph_yaml}")
                return True
            else:
                logger.error(f"File call graph generation failed: {result.error_message}")
                return False

        except Exception as e:
            logger.error(f"Error in file call graph generation: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    async def map_files_to_domains(self) -> bool:
        """
        Map files to domains using LLM.

        This method reads the domain analysis YAML and file-driven call graph YAML,
        and uses an LLM to map files to domains. If use_hierarchical_domains is True,
        it maps files to all levels of the domain hierarchy using run_mapper from
        run_hierarchical_mapper.py, otherwise it maps files only to top-level domains.

        Returns:
            bool: True if domain-to-file mapping was successful, False otherwise
        """
        logger.info("Mapping files to domains")

        # Check if the domain analysis YAML file exists
        if not os.path.exists(self.domain_analysis_yaml):
            logger.error(f"Domain analysis YAML file not found: {self.domain_analysis_yaml}")
            return False

        # Check if the file call graph YAML file exists
        if not os.path.exists(self.file_call_graph_yaml):
            logger.error(f"File call graph YAML file not found: {self.file_call_graph_yaml}")
            return False

        try:
            if self.use_hierarchical_domains:
                # Map files to domains hierarchically using run_mapper
                logger.info(f"Using run_mapper for hierarchical domain-file mapping")
                success = await run_mapper(
                    domain_yaml_path=self.domain_analysis_yaml,
                    file_graph_yaml_path=self.file_call_graph_yaml,
                    output_path=self.domain_file_mappings_yaml,
                    model=self.openai_model,
                    use_openrouter=self.use_openrouter,
                    go_gemini=True,  # Use Gemini for top-level domain file allocation
                    batch_size=50,    # Increased batch size to reduce number of API calls
                    requests_per_minute=self.llm_requests_per_minute * 10,  # Increased to handle larger batches
                    tokens_per_minute=self.llm_tokens_per_minute * 10,  # Increased to 50M tokens per minute
                    verbose=self.verbose,
                    max_concurrent_tasks=self.max_concurrent_tasks,  # Optimized for 12-core MacBook Pro
                    checkpoint_interval_seconds=300,  # Save state every 5 minutes by default
                )
            else:
                # Map files to top-level domains only
                logger.info("Using top-level domain-file mapping")
                success = await DomainFileMapperIntegration.map_files_to_domains(
                    domain_analysis_yaml_path=self.domain_analysis_yaml,
                    file_call_graph_yaml_path=self.file_call_graph_yaml,
                    output_path=self.domain_file_mappings_yaml,
                    # API keys will be handled by the function using centralized management
                    model="gpt-4o-mini",
                    max_requests_per_minute=self.llm_requests_per_minute,
                    max_tokens_per_minute=self.llm_tokens_per_minute,
                    use_openrouter=self.use_openrouter,
                    openrouter_base_url=self.openrouter_base_url,
                    go_gemini=True,  # Use Gemini for top-level domain file allocation
                )

            if success:
                logger.info(f"Domain-to-file mapping complete. Results saved to: {self.domain_file_mappings_yaml}")
                return True
            else:
                logger.error("Domain-to-file mapping failed")
                return False

        except Exception as e:
            logger.error(f"Error in domain-to-file mapping: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    async def build_domain_traces(self, use_enhanced: bool = True) -> bool:
        """
        Build domain traces from the domain analysis YAML and classify functions into these traces.

        This method reads the domain analysis YAML file, builds traces from top to bottom
        (domain -> sub-area -> sub-area...), and uses an LLM to classify functions into these traces.
        If use_enhanced is True and domain-file mappings are available, it uses the enhanced approach
        with reduced search space. If use_hierarchical_domains is True, it uses the hierarchical
        domain-file mappings to further reduce the search space.

        Args:
            use_enhanced: Whether to use the enhanced approach with domain-file mappings

        Returns:
            bool: True if domain trace building was successful, False otherwise
        """
        logger.info("Building domain traces and classifying functions")

        # Check if the domain analysis YAML file exists
        if not os.path.exists(self.domain_analysis_yaml):
            logger.error(f"Domain analysis YAML file not found: {self.domain_analysis_yaml}")
            return False

        # Check if the documented functions parquet file exists
        if not os.path.exists(self.documented_output_path):
            logger.error(f"Documented functions parquet file not found: {self.documented_output_path}")
            return False

        # Initialize result variable to avoid UnboundLocalError
        result = None

        try:
            # Force original approach if flag is set
            if self.force_original_approach:
                logger.info("Forcing original approach without domain-file mappings")
                builder = DomainTraceBuilder(
                    domain_analysis_yaml_path=self.domain_analysis_yaml,
                    functions_parquet_path=self.documented_output_path,
                    output_path=self.domain_traces_yaml,
                    max_requests_per_minute=self.llm_requests_per_minute,
                    max_tokens_per_minute=self.llm_tokens_per_minute,
                )
                result = await builder.build_and_classify()
                success = result.success
            elif use_enhanced and os.path.exists(self.domain_file_mappings_yaml) and not self.skip_file_mapping:
                # Always use enhanced domain trace builder with domain-file mappings
                logger.info("Using enhanced domain trace builder with domain-file mappings")
                success = await EnhancedDomainTraceBuilderIntegration.build_domain_traces(
                    domain_yaml_path=self.domain_analysis_yaml,
                    functions_parquet_path=self.documented_output_path,
                    output_path=self.domain_traces_yaml,
                    domain_file_mappings_path=self.domain_file_mappings_yaml,
                    api_key=self.openai_api_key,
                    model=self.openai_model,
                    max_requests_per_minute=self.llm_requests_per_minute * 10,  # Significantly increase rate limits for optimized parallel processing
                    max_tokens_per_minute=self.llm_tokens_per_minute * 10,  # Significantly increase token limits for optimized parallel processing
                    fallback_to_original=True,  # Fall back to original approach if domain-file mappings are not available
                )
            else:
                # Fall back to original domain trace builder
                logger.info("Using original domain trace builder")
                # Create a domain trace builder
                builder = DomainTraceBuilder(
                    domain_analysis_yaml_path=self.domain_analysis_yaml,
                    functions_parquet_path=self.documented_output_path,
                    output_path=self.domain_traces_yaml,
                    max_requests_per_minute=self.llm_requests_per_minute,
                    max_tokens_per_minute=self.llm_tokens_per_minute,
                )

                # Build domain traces and classify functions
                result = await builder.build_and_classify()
                success = result.success

            if success:
                logger.info(f"Domain trace building complete. Results saved to: {self.domain_traces_yaml}")

                # Generate domain diagrams if enabled
                if self.generate_diagrams:
                    logger.info("Step 3f: Generating domain diagrams")
                    success = await self.generate_domain_diagrams()
                    if not success:
                        logger.warning("Domain diagram generation failed, but continuing with analysis")

                # Generate domain taxonomy JSON
                logger.info("Step 3g: Generating domain taxonomy JSON")
                taxonomy_success = await self.generate_domain_taxonomy()
                if not taxonomy_success:
                    logger.warning("Domain taxonomy JSON generation failed, but continuing with analysis")

                # Generate codebase explanation if enabled
                if self.generate_explanation and taxonomy_success:
                    logger.info("Step 3h: Generating codebase explanation")
                    explanation_success = await self.generate_codebase_explanation()
                    if not explanation_success:
                        logger.warning("Codebase explanation generation failed, but continuing with analysis")

                return True
            else:
                error_message = getattr(result, 'error_message', 'Unknown error') if result else 'Unknown error'
                logger.error(f"Domain trace building failed: {error_message}")
                return False

        except Exception as e:
            logger.error(f"Error in domain trace building: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    async def generate_domain_diagrams(self) -> bool:
        """
        Generate mermaid diagrams from domain traces.

        This method reads the domain traces YAML file and generates mermaid diagrams
        for each leaf subdomain, then combines them as we move up the hierarchy.
        Uses the EnhancedDomainDiagramGenerator which can handle large domains with OpenRouter/Gemini.

        Returns:
            bool: True if diagram generation was successful, False otherwise
        """
        logger.info("Generating domain diagrams from domain traces using EnhancedDomainDiagramGenerator")

        # Check if the domain traces YAML file exists
        if not os.path.exists(self.domain_traces_yaml):
            logger.error(f"Domain traces YAML file not found: {self.domain_traces_yaml}")
            return False

        # Check if the documented functions parquet file exists
        if not os.path.exists(self.documented_output_path):
            logger.error(f"Documented functions parquet file not found: {self.documented_output_path}")
            return False

        try:
            # Create an enhanced domain diagram generator
            generator = EnhancedDomainDiagramGenerator(
                domain_traces_yaml_path=self.domain_traces_yaml,
                functions_parquet_path=self.documented_output_path,
                output_dir=self.domain_diagrams_dir,
                # Model selection and configuration
                model_type=self.diagram_model_type,
                # Claude parameters
                claude_api_key=self.claude_api_key,
                claude_model=self.claude_model,
                # OpenAI parameters
                openai_api_key=self.openai_api_key,
                openai_model=self.openai_model,
                # OpenRouter parameters
                use_openrouter=True,
                openrouter_api_key=None,  # Will be handled by centralized management
                openrouter_model="google/gemini-2.5-pro-preview",
                openrouter_token_threshold=45000,  # Token threshold to switch to OpenRouter
                openrouter_max_concurrent=3,  # Maximum concurrent OpenRouter calls
                # Common parameters
                max_tokens=8000,
                temperature=0.6,
                max_requests_per_minute=1000,  # Lower rate limit for diagram generation
                max_tokens_per_minute=1000000,
                # Parallelization parameters
                max_concurrent_tasks=self.max_concurrent_tasks,
                # Caching parameters
                cache_dir=os.path.join(self.output_dir, "diagram_cache"),
                use_cache=True,
            )

            # Generate all diagrams
            result = await generator.generate_all_diagrams()

            if result.success:
                logger.info(f"Domain diagram generation complete. Generated {len(result.diagram_files)} diagrams using {result.model_used}")
                return True
            else:
                logger.error(f"Domain diagram generation failed: {result.error_message}")
                return False

        except Exception as e:
            logger.error(f"Error in domain diagram generation: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    async def generate_domain_taxonomy(self) -> bool:
        """
        Generate domain taxonomy JSON from domain analysis, traces, and diagrams.

        This method reads the domain analysis YAML, domain traces YAML, and mermaid diagrams,
        and combines them into a hierarchical JSON structure using the generate_domain_taxonomy
        function from bracket_core.generate_domain_taxonomy.

        Returns:
            bool: True if taxonomy generation was successful, False otherwise
        """
        logger.info("Generating domain taxonomy JSON using generate_domain_taxonomy")

        # Check if the domain analysis YAML file exists
        if not os.path.exists(self.domain_analysis_yaml):
            logger.error(f"Domain analysis YAML file not found: {self.domain_analysis_yaml}")
            return False

        # Check if the domain traces YAML file exists
        if not os.path.exists(self.domain_traces_yaml):
            logger.error(f"Domain traces YAML file not found: {self.domain_traces_yaml}")
            return False

        # Check if the domain diagrams directory exists
        if not os.path.exists(self.domain_diagrams_dir):
            logger.error(f"Domain diagrams directory not found: {self.domain_diagrams_dir}")
            return False

        try:
            # Check if diagram name mapping file exists
            diagram_name_mapping_path = os.path.join(os.path.dirname(self.domain_diagrams_dir), "diagram_name_mapping.json")
            if not os.path.exists(diagram_name_mapping_path):
                diagram_name_mapping_path = None

            # Use the generate_domain_taxonomy function
            success = await generate_domain_taxonomy(
                domain_analysis_yaml_path=self.domain_analysis_yaml,
                domain_traces_yaml_path=self.domain_traces_yaml,
                domain_diagrams_dir=self.domain_diagrams_dir,
                output_json_path=self.domain_taxonomy_json,
                diagram_name_mapping_path=diagram_name_mapping_path
            )

            if success:
                logger.info(f"Domain taxonomy JSON generated successfully: {self.domain_taxonomy_json}")
                return True
            else:
                logger.error("Domain taxonomy JSON generation failed")
                return False

        except Exception as e:
            logger.error(f"Error in domain taxonomy JSON generation: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    async def generate_codebase_explanation(self) -> bool:
        """
        Generate a comprehensive explanation of the codebase based on the domain taxonomy JSON.

        Returns:
            True if successful, False otherwise
        """
        logger.info(f"Generating codebase explanation from {self.domain_taxonomy_json}")

        try:
            # Generate the codebase explanation
            result = await generate_codebase_explanation(
                taxonomy_json_path=self.domain_taxonomy_json,
                global_code_explain_md_path=self.codebase_explanation_md,
                suggested_questions_md_path=os.path.join(self.output_dir, "suggested_questions.md"),
                openai_api_key=self.openai_api_key,
                openai_model=self.explanation_model,
                max_tokens=4000,
                temperature=0.7,
            )

            if result.success:
                logger.info(f"Codebase explanation generated successfully: {result.output_path}")
                return True
            else:
                logger.error(f"Codebase explanation generation failed: {result.error_message}")
                return False

        except Exception as e:
            logger.error(f"Error in codebase explanation generation: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    # async def generate_mermaid_diagrams(self, irl_json_path: str) -> List[str]:
    #     """
    #     Generate mermaid diagrams from the knowledge graph CSV.
    #
    #     Args:
    #         irl_json_path: Path to the edges CSV file with documentation
    #
    #     Returns:
    #         List of paths to generated diagram files
    #     """
    #     logger.info(f"Generating mermaid diagrams from {irl_json_path}")
    #
    #     # Initialize the mermaid diagram generator
    #     diagram_generator = MermaidDiagramGenerator(
    #         output_dir=os.path.join(self.output_dir, "diagrams"),
    #         claude_api_key=self.claude_api_key,
    #         claude_model=self.claude_model,
    #     )
    #
    #     # Generate diagrams from the CSV
    #     diagram_files = await diagram_generator.generate_diagrams_from_json(irl_json_path)
    #
    #     logger.info(f"Generated {len(diagram_files)} mermaid diagrams")
    #     return diagram_files



async def main():
    """Main entry point for the repository analysis flow."""
    parser = argparse.ArgumentParser(description="Analyze a code repository and generate combined artifacts")
    parser.add_argument("--repo", required=True, help="Path to the repository to analyze")
    parser.add_argument("--output", required=True, help="Directory to save output artifacts")
    parser.add_argument("--verbose", "-v", action="store_true", default=True, help="Enable verbose output")
    parser.add_argument("--skip-documentation", action="store_true", default=False, help="Skip function documentation step")
    parser.add_argument("--skip-domain-analysis", action="store_true", default=False, help="Skip domain analysis step")
    parser.add_argument("--generate-diagrams", action="store_true", default=True, help="Generate domain diagrams")
    parser.add_argument("--generate-explanation", action="store_true", default=True, help="Generate codebase explanation")
    parser.add_argument("--diagram-model-type", default="openai", choices=["claude", "openai"], help="Type of model to use for diagrams (claude or openai)")
    # Claude parameters
    parser.add_argument("--claude-api-key", help="Anthropic API key for Claude (if not provided, will try to get from environment)")
    parser.add_argument("--claude-model", default="claude-3-5-sonnet-20241022", help="Claude model to use for diagram generation")
    # OpenAI parameters
    parser.add_argument("--openai-api-key", default = "********************************************************************************************************************************************************************", help="OpenAI API key for diagrams (if not provided, will try to get from environment)")
    parser.add_argument("--openai-model", default="gpt-4o-mini", help="OpenAI model to use for diagram generation")
    parser.add_argument("--explanation-model", default="o3-mini", help="OpenAI model to use for codebase explanation generation")
    # Common parameters
    parser.add_argument("--requests-per-minute", type=float, default=2000, help="Rate limit for LLM API requests")
    parser.add_argument("--tokens-per-minute", type=float, default=5000000, help="Token rate limit for LLM API")
    # Parallelization parameters
    parser.add_argument("--max-concurrent-tasks", type=int, default=5, help="Maximum number of concurrent tasks for parallel processing")
    # OpenRouter parameters
    parser.add_argument("--use-openrouter", action="store_true", default=False, help="Use OpenRouter instead of OpenAI")
    parser.add_argument("--openrouter-base-url", default="https://openrouter.ai/api/v1", help="Base URL for OpenRouter API")

    args = parser.parse_args()
    start_time = time.time()

    # Enable documentation by default to generate YAML file
    # args.skip_documentation = True
    flow = RepoAnalysisFlow(
        repo_dir=args.repo,
        output_dir=args.output,
        verbose=args.verbose,
        document_functions=not args.skip_documentation,
        analyze_domains=not args.skip_domain_analysis,
        llm_requests_per_minute=args.requests_per_minute,
        llm_tokens_per_minute=args.tokens_per_minute,
        generate_diagrams=args.generate_diagrams,
        generate_explanation=args.generate_explanation,
        # Diagram model configuration
        diagram_model_type=args.diagram_model_type,
        # Claude parameters
        claude_api_key=args.claude_api_key,
        claude_model=args.claude_model,
        # OpenAI parameters
        openai_api_key=args.openai_api_key,
        openai_model=args.openai_model,
        explanation_model=args.explanation_model,
        # Parallelization parameters
        max_concurrent_tasks=args.max_concurrent_tasks,
        use_openrouter=args.use_openrouter,
        openrouter_base_url=args.openrouter_base_url,
    )

    await flow.run()

    end_time = time.time()
    print(f"Total time taken: {end_time - start_time}")


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())

# python -m bracket_core.irl --repo /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core --output /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/beauty_of_life/data/bracket

# python -m bracket_core.irl --repo /Users/<USER>/work/startup/godzilla/django/django/ --output /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/beauty_of_life/data/django_2

# python -m bracket_core.irl --repo /Users/<USER>/work/startup/godzilla/adjacent/aider/aider --output /Users/<USER>/work/startup/godzilla/bracket/bracket/bracket_outputs/aider-signify
# 35K total

# python -m bracket_core.irl --repo /Users/<USER>/work/startup/godzilla/adjacent/mem0/mem0 --output /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/beauty_of_life/data/mem0
# 20K - very opt; 11K signify


# python -m bracket_core.irl --repo /Users/<USER>/work/startup/godzilla/test/pytorch/torch --output /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/big_codebase/pytorch/

# python -m bracket_core.irl --repo /Users/<USER>/work/startup/godzilla/test/gitlab/gitlab --output /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/

# python -m bracket_core.irl --repo /Users/<USER>/work/startup/godzilla/test/gitlab1 --output /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/

