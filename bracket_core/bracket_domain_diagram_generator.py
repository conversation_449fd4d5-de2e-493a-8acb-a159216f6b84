#!/usr/bin/env python3
"""
JSON-based Domain Diagram Generator for Codebase

This module extends the enhanced_domain_diagram_generator.py to work with a JSON structure
where leaf domains are keys, with file paths as sub-keys, and file code as values.

Key features:
1. Reads a JSON file with domain -> file paths -> code structure
2. Processes each leaf domain in parallel
3. Skips any domain containing "Unclassified"
4. Generates mermaid diagrams for each leaf domain
5. Supports hierarchical diagram generation for intermediate domains
6. Builds domain hierarchy from domain names (using -> separator)

Usage:
    python bracket_domain_diagram_generator.py --domain-file-json <path> --output-dir <path> [--hierarchical]
"""

import os
import json
import logging
import asyncio
import time
import re
import yaml
from typing import Dict, List, Set, Tuple, Optional, Union, Any
from dataclasses import dataclass, field

# Import the enhanced domain diagram generator
from exp_repomap.exp_domain_diagram_generator import EnhancedDomainDiagramGenerator, DiagramGenerationResult
from llm.tokens import num_tokens_from_string

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class JSONDiagramGenerationResult(DiagramGenerationResult):
    """Extended result of generating domain diagrams from JSON input."""
    domain_file_stats: Dict[str, Any] = field(default_factory=dict)  # Statistics about domains and files
    processing_stats: Dict[str, Any] = field(default_factory=dict)  # Statistics about processing time and success rate

class StatusTracker:
    """Tracks the status of diagram generation tasks."""

    def __init__(self, total_tasks: int):
        """Initialize the status tracker.

        Args:
            total_tasks: Total number of tasks to track
        """
        self.total_tasks = total_tasks
        self.completed_tasks = 0
        self.failed_tasks = 0
        self.start_time = time.time()
        self.domain_times = {}  # Maps domain names to processing times

    def task_completed(self, domain: str, elapsed_time: float):
        """Mark a task as completed.

        Args:
            domain: Domain name
            elapsed_time: Time taken to process the domain
        """
        self.completed_tasks += 1
        self.domain_times[domain] = elapsed_time

    def task_failed(self):
        """Mark a task as failed."""
        self.failed_tasks += 1

    def get_progress(self) -> str:
        """Get a string representation of the current progress.

        Returns:
            Progress string
        """
        processed = self.completed_tasks + self.failed_tasks
        percent = (processed / self.total_tasks) * 100 if self.total_tasks > 0 else 0
        elapsed = time.time() - self.start_time

        return f"{processed}/{self.total_tasks} ({percent:.1f}%) in {elapsed:.1f}s"

    def get_stats(self) -> Dict[str, Any]:
        """Get statistics about the processing.

        Returns:
            Dictionary containing statistics
        """
        elapsed = time.time() - self.start_time

        # Calculate average and median processing times
        times = list(self.domain_times.values())
        avg_time = sum(times) / len(times) if times else 0
        median_time = sorted(times)[len(times) // 2] if times else 0

        return {
            "total_tasks": self.total_tasks,
            "completed_tasks": self.completed_tasks,
            "failed_tasks": self.failed_tasks,
            "success_rate": (self.completed_tasks / self.total_tasks) * 100 if self.total_tasks > 0 else 0,
            "total_elapsed_time": elapsed,
            "average_task_time": avg_time,
            "median_task_time": median_time,
            "tasks_per_second": self.completed_tasks / elapsed if elapsed > 0 else 0,
        }

class JSONDomainDiagramGenerator:
    """
    Domain Diagram Generator that works with a JSON structure where leaf domains are keys,
    with file paths as sub-keys, and file code as values.

    Supports hierarchical diagram generation by building a domain hierarchy from domain names.
    Can also build hierarchical diagrams from existing leaf diagrams and domain analysis YAML.
    """

    def __init__(
        self,
        domain_file_json_path: str = "",
        output_dir: str = "",
        # Model selection and configuration
        model_type: str = "openai",  # "claude" or "openai"
        # Claude parameters
        claude_api_key: Optional[str] = None,
        claude_model: str = "claude-3-7-sonnet-20250219",
        # OpenAI parameters
        openai_api_key: Optional[str] = None,
        openai_model: str = "o3-mini",
        # OpenRouter parameters
        use_openrouter: bool = True,
        openrouter_api_key: Optional[str] = None,
        openrouter_model: str = "google/gemini-2.5-pro-preview",
        openrouter_token_threshold: int = 45000,  # Token threshold to switch to OpenRouter
        openrouter_max_concurrent: int = 3,  # Maximum concurrent OpenRouter calls
        # Common parameters
        # max_tokens: int = 8096,
        temperature: float = 0.5,
        max_requests_per_minute: float = 10000,
        max_tokens_per_minute: float = 10000000,
        # Parallelization parameters
        max_concurrent_tasks: int = 50,
        # Hierarchy parameters
        hierarchical: bool = False,
        max_hierarchy_depth: Optional[int] = None,
        # Domain analysis parameters
        domain_analysis_yaml_path: str = "",
        existing_diagrams_dir: str = "",
        # Caching parameters
        cache_dir: Optional[str] = None,
        use_cache: bool = True,
        # Token limit parameters
        max_input_tokens: int = 100000,  # Maximum number of tokens for input code
    ):
        """Initialize the JSON domain diagram generator.

        Args:
            domain_file_json_path: Path to the JSON file with domain -> file paths -> code structure
            output_dir: Directory to save generated diagrams
            model_type: Type of model to use ("claude" or "openai")
            claude_api_key: Anthropic API key. If None, will try to get from environment.
            claude_model: Claude model to use.
            openai_api_key: OpenAI API key. If None, will try to get from environment.
            openai_model: OpenAI model to use.
            use_openrouter: Whether to use OpenRouter for large domains
            openrouter_api_key: OpenRouter API key. If None, will try to get from environment.
            openrouter_model: OpenRouter model to use (Gemini)
            openrouter_token_threshold: Token threshold to switch to OpenRouter
            max_tokens: Maximum tokens to generate.
            temperature: Sampling temperature.
            max_requests_per_minute: Rate limit for API requests
            max_tokens_per_minute: Token rate limit for API
            max_concurrent_tasks: Maximum number of concurrent tasks for parallel processing
            hierarchical: Whether to generate hierarchical diagrams
            max_hierarchy_depth: Maximum hierarchy depth to process (None for all levels)
            cache_dir: Directory to cache intermediate results (None for no caching)
            use_cache: Whether to use caching for intermediate results
            max_input_tokens: Maximum number of tokens for input code
        """
        self.domain_file_json_path = domain_file_json_path
        self.output_dir = output_dir
        self.diagrams_dir = os.path.join(output_dir, "diagrams_from_json")
        os.makedirs(self.diagrams_dir, exist_ok=True)

        # Create a hierarchical diagrams directory if needed
        self.hierarchical_diagrams_dir = os.path.join(output_dir, "hierarchical_diagrams")
        os.makedirs(self.hierarchical_diagrams_dir, exist_ok=True)

        # Create a temporary directory for the enhanced domain diagram generator
        self.temp_dir = os.path.join(output_dir, "temp")
        os.makedirs(self.temp_dir, exist_ok=True)

        # Initialize the enhanced domain diagram generator with dummy paths
        # We'll use its API methods but not its file reading functionality
        self.diagram_generator = EnhancedDomainDiagramGenerator(
            domain_traces_yaml_path="",  # Dummy path, we won't use this
            functions_parquet_path="",   # Dummy path, we won't use this
            output_dir=self.temp_dir,
            model_type=model_type,
            claude_api_key=claude_api_key,
            claude_model=claude_model,
            openai_api_key=openai_api_key,
            openai_model=openai_model,
            use_openrouter=use_openrouter,
            openrouter_api_key=openrouter_api_key,
            openrouter_model=openrouter_model,
            openrouter_token_threshold=openrouter_token_threshold,
            openrouter_max_concurrent=openrouter_max_concurrent,
            # max_tokens=max_tokens,
            temperature=temperature,
            max_requests_per_minute=max_requests_per_minute,
            max_tokens_per_minute=max_tokens_per_minute,
            max_concurrent_tasks=max_concurrent_tasks,
            cache_dir=cache_dir,
            use_cache=use_cache,
            max_input_tokens=max_input_tokens,
        )

        # Store configuration
        self.max_concurrent_tasks = max_concurrent_tasks
        self.max_input_tokens = max_input_tokens
        self.hierarchical = hierarchical
        self.max_hierarchy_depth = max_hierarchy_depth
        self.domain_analysis_yaml_path = domain_analysis_yaml_path
        self.existing_diagrams_dir = existing_diagrams_dir

        # Initialize domain data
        self.domain_file_data = {}
        self.domain_stats = {}

        # Initialize domain hierarchy tracking structures
        self.all_domains = set()  # All domains at all levels
        self.leaf_domains = set()  # Leaf domains (no children)
        self.domain_parents = {}  # Maps domain to its parent domain
        self.domain_children = {}  # Maps domain to its child domains
        self.domain_levels = {}  # Maps domain to its hierarchy level (0 = root)
        self.domain_paths = {}  # Maps domain to its full path (e.g., "A -> B -> C")

    def read_domain_file_json(self) -> Dict[str, Dict[str, str]]:
        """
        Read the domain file JSON.

        Returns:
            Dictionary mapping domain names to dictionaries of file paths and code
        """
        logger.info(f"Reading domain file JSON from: {self.domain_file_json_path}")

        try:
            with open(self.domain_file_json_path, 'r') as f:
                domain_file_data = json.load(f)

            # Filter out domains containing "Unclassified"
            filtered_domains = {}
            for domain, files in domain_file_data.items():
                if "Unclassified" not in domain:
                    filtered_domains[domain] = files
                else:
                    logger.info(f"Skipping unclassified domain: {domain}")

            logger.info(f"Found {len(filtered_domains)} domains after filtering out unclassified domains")
            self.domain_file_data = filtered_domains

            # Compute statistics
            self.domain_stats = self._compute_domain_stats(filtered_domains)

            # Build domain hierarchy if hierarchical generation is enabled
            if self.hierarchical:
                self.build_domain_hierarchy()

            return filtered_domains

        except Exception as e:
            logger.error(f"Error reading domain file JSON: {e}")
            return {}

    def build_domain_hierarchy(self) -> Dict[str, Any]:
        """
        Build a hierarchical representation of domains from domain names.

        This method parses domain names (e.g., "A -> B -> C") to build a hierarchy
        of domains, tracking parent-child relationships and hierarchy levels.

        Returns:
            Dictionary representing the domain hierarchy
        """
        logger.info("Building domain hierarchy from domain names")

        hierarchy = {}

        # Reset domain tracking structures
        self.all_domains = set()
        self.leaf_domains = set()
        self.domain_parents = {}
        self.domain_children = {}
        self.domain_levels = {}
        self.domain_paths = {}

        # Process each domain in the domain file data
        for domain_trace in self.domain_file_data.keys():
            # Split the domain trace into components
            components = domain_trace.split(" -> ")

            # Skip empty domains
            if not components:
                continue

            # Build the hierarchy
            current_path = ""

            # Navigate/build the hierarchy
            current = hierarchy
            for i, component in enumerate(components):
                # Build the current path
                if current_path:
                    current_path += f" -> {component}"
                    parent_path = current_path.rsplit(' -> ', 1)[0]
                else:
                    current_path = component
                    parent_path = ""  # Root level has no parent

                # Add to tracking structures
                self.all_domains.add(current_path)
                self.domain_paths[current_path] = current_path
                self.domain_levels[current_path] = i

                # Set up parent-child relationships
                if parent_path:
                    if parent_path not in self.domain_children:
                        self.domain_children[parent_path] = set()
                    self.domain_children[parent_path].add(current_path)
                    self.domain_parents[current_path] = parent_path

                # Create the domain in the hierarchy if it doesn't exist
                if component not in current:
                    current[component] = {
                        'files': {},
                        'subdomains': {},
                        'path': current_path,
                        'level': i
                    }

                # If this is the last component, add the files
                if i == len(components) - 1:
                    current[component]['files'] = self.domain_file_data.get(domain_trace, {})

                # Move to the next level
                current = current[component]['subdomains']

        # Identify leaf domains (those with no children)
        for domain in self.all_domains:
            if domain not in self.domain_children or not self.domain_children[domain]:
                self.leaf_domains.add(domain)

        # Log hierarchy statistics
        logger.info(f"Built domain hierarchy with {len(self.all_domains)} total domains")
        logger.info(f"Leaf domains: {len(self.leaf_domains)}")

        # Count domains by level
        level_counts = {}
        for domain, level in self.domain_levels.items():
            if level not in level_counts:
                level_counts[level] = 0
            level_counts[level] += 1

        for level, count in sorted(level_counts.items()):
            logger.info(f"Level {level}: {count} domains")

        return hierarchy

    def sort_domains_by_level(self) -> Dict[int, List[str]]:
        """
        Sort domains by their hierarchy level to ensure child domains are processed before parents.

        Returns:
            Dictionary mapping hierarchy levels to lists of domains at that level
        """
        logger.info("Sorting domains by hierarchy level")

        # Group domains by level
        domains_by_level = {}
        for domain, level in self.domain_levels.items():
            if level not in domains_by_level:
                domains_by_level[level] = []
            domains_by_level[level].append(domain)

        # Sort levels from bottom (highest level number) to top (level 0)
        sorted_levels = sorted(domains_by_level.keys(), reverse=True)

        # Log the results
        for level in sorted_levels:
            logger.info(f"Level {level}: {len(domains_by_level[level])} domains")

        return domains_by_level

    def build_domain_hierarchy_from_analysis(self) -> Dict[str, Any]:
        """
        Build a hierarchical representation of domains from a domain analysis YAML file.

        This method parses the domain analysis YAML file to build a hierarchy of domains,
        tracking parent-child relationships and hierarchy levels.

        Returns:
            Dictionary representing the domain hierarchy
        """
        if not self.domain_analysis_yaml_path:
            logger.error("No domain analysis YAML file path provided")
            return {}

        logger.info(f"Building domain hierarchy from domain analysis YAML: {self.domain_analysis_yaml_path}")

        hierarchy = {}

        # Reset domain tracking structures
        self.all_domains = set()
        self.leaf_domains = set()
        self.domain_parents = {}
        self.domain_children = {}
        self.domain_levels = {}
        self.domain_paths = {}

        try:
            # Read the domain analysis YAML file
            with open(self.domain_analysis_yaml_path, 'r') as f:
                domain_analysis = yaml.safe_load(f)

            # Process the domain analysis data
            if 'areas' in domain_analysis:
                # Process each area (top-level domain)
                for area in domain_analysis['areas']:
                    area_name = area.get('name', '')
                    if not area_name:
                        continue

                    # Add the area to the hierarchy
                    if area_name not in hierarchy:
                        hierarchy[area_name] = {
                            'files': {},
                            'subdomains': {},
                            'path': area_name,
                            'level': 0
                        }

                    # Add to tracking structures
                    self.all_domains.add(area_name)
                    self.domain_paths[area_name] = area_name
                    self.domain_levels[area_name] = 0

                    # Process subareas (second-level domains)
                    if 'subareas' in area and area['subareas']:
                        for subarea in area['subareas']:
                            subarea_name = subarea.get('name', '')
                            if not subarea_name:
                                continue

                            # Build the full path
                            full_path = f"{area_name} -> {subarea_name}"

                            # Add the subarea to the hierarchy
                            if subarea_name not in hierarchy[area_name]['subdomains']:
                                hierarchy[area_name]['subdomains'][subarea_name] = {
                                    'files': {},
                                    'subdomains': {},
                                    'path': full_path,
                                    'level': 1
                                }

                            # Add to tracking structures
                            self.all_domains.add(full_path)
                            self.domain_paths[full_path] = full_path
                            self.domain_levels[full_path] = 1

                            # Set up parent-child relationships
                            if area_name not in self.domain_children:
                                self.domain_children[area_name] = set()
                            self.domain_children[area_name].add(full_path)
                            self.domain_parents[full_path] = area_name

                            # Process leaf domains (third-level domains)
                            if 'subareas' in subarea and subarea['subareas']:
                                for leaf_domain in subarea['subareas']:
                                    leaf_domain_name = leaf_domain.get('name', '')
                                    if not leaf_domain_name:
                                        continue

                                    # Build the full path
                                    leaf_full_path = f"{full_path} -> {leaf_domain_name}"

                                    # Add the leaf domain to the hierarchy
                                    if leaf_domain_name not in hierarchy[area_name]['subdomains'][subarea_name]['subdomains']:
                                        hierarchy[area_name]['subdomains'][subarea_name]['subdomains'][leaf_domain_name] = {
                                            'files': {},
                                            'subdomains': {},
                                            'path': leaf_full_path,
                                            'level': 2
                                        }

                                    # Add to tracking structures
                                    self.all_domains.add(leaf_full_path)
                                    self.domain_paths[leaf_full_path] = leaf_full_path
                                    self.domain_levels[leaf_full_path] = 2

                                    # Set up parent-child relationships
                                    if full_path not in self.domain_children:
                                        self.domain_children[full_path] = set()
                                    self.domain_children[full_path].add(leaf_full_path)
                                    self.domain_parents[leaf_full_path] = full_path

                                    # Mark as leaf domain
                                    self.leaf_domains.add(leaf_full_path)

            # Identify leaf domains (those with no children)
            for domain in self.all_domains:
                if domain not in self.domain_children or not self.domain_children[domain]:
                    self.leaf_domains.add(domain)

            # Log hierarchy statistics
            logger.info(f"Built domain hierarchy with {len(self.all_domains)} total domains")
            logger.info(f"Leaf domains: {len(self.leaf_domains)}")

            # Count domains by level
            level_counts = {}
            for domain, level in self.domain_levels.items():
                if level not in level_counts:
                    level_counts[level] = 0
                level_counts[level] += 1

            for level, count in sorted(level_counts.items()):
                logger.info(f"Level {level}: {count} domains")

            # Log some example domains at each level for debugging
            for level in sorted(level_counts.keys()):
                domains_at_level = [domain for domain, lvl in self.domain_levels.items() if lvl == level]
                example_domains = domains_at_level[:5]  # Show up to 5 examples
                logger.info(f"Example domains at level {level}: {example_domains}")

            return hierarchy

        except Exception as e:
            logger.error(f"Error building domain hierarchy from analysis: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return {}

    def _compute_domain_stats(self, domain_file_data: Dict[str, Dict[str, str]]) -> Dict[str, Any]:
        """
        Compute statistics about the domains and files.

        Args:
            domain_file_data: Dictionary mapping domain names to dictionaries of file paths and code

        Returns:
            Dictionary containing statistics
        """
        stats = {
            "total_domains": len(domain_file_data),
            "domains": {},
            "total_files": 0,
            "total_tokens": 0,
        }

        for domain, files in domain_file_data.items():
            domain_stats = {
                "file_count": len(files),
                "token_count": 0,
            }

            # Count tokens in all files in this domain
            all_code = ""
            for _, code in files.items():
                all_code += code + "\n\n"

            # Estimate token count
            token_count = num_tokens_from_string(all_code, self.diagram_generator.model_name)
            domain_stats["token_count"] = token_count

            stats["domains"][domain] = domain_stats
            stats["total_files"] += len(files)
            stats["total_tokens"] += token_count

        return stats

    async def generate_leaf_diagram_from_files(self, domain: str, files: Dict[str, str]) -> Tuple[str, str]:
        """
        Generate a mermaid diagram for a leaf domain based on its files.

        Args:
            domain: Domain name
            files: Dictionary mapping file paths to code

        Returns:
            Tuple of (diagram_content, raw_response)
        """
        logger.info(f"Generating diagram for leaf domain: {domain}")

        # Check if there are any files in this domain
        if not files:
            logger.warning(f"No files found for domain: {domain}. Skipping diagram generation.")
            empty_diagram = "```mermaid\ngraph TD\n    A[No files found in this domain]\n```"
            return empty_diagram, "No files found in this domain"

        # Note: Rate limiting is now handled by the worker pool, not here

        # Prepare file data for the prompt
        file_details = []
        current_token_count = 0
        truncated_files = []

        for file_path, code in files.items():
            # Format file details
            file_detail = {
                'file_path': file_path,
                'code': code,
            }

            # Estimate token count for this file
            file_str = json.dumps(file_detail)
            file_tokens = num_tokens_from_string(file_str, self.diagram_generator.model_name)

            # Check if adding this file would exceed the token limit
            if current_token_count + file_tokens <= self.max_input_tokens:
                file_details.append(file_detail)
                current_token_count += file_tokens
            else:
                # We've reached the token limit, truncate remaining files
                truncated_files.append(file_path)
                continue

        # Log token usage information
        logger.info(f"Token usage for domain {domain}: {current_token_count}/{self.max_input_tokens} tokens used")

        if truncated_files:
            logger.warning(f"Truncated {len(truncated_files)} files for domain {domain} due to token limit of {self.max_input_tokens}")

        # Create the system prompt
        system_prompt = """You are an expert software architect who creates clear, informative mermaid diagrams to visualize code architecture.
Your task is to create a detailed mermaid diagram that represents the LOGICAL RELATIONSHIPS and INTERACTIONS between files in a specific domain of a codebase.

Guidelines for creating the diagram:
1. Focus on the LOGICAL PURPOSE and ROLE of each file within the domain
2. Emphasize how files work together to accomplish domain goals
3. Show meaningful relationships and dependencies between files
4. Highlight the conceptual flow of data and control between files
5. Group files by their logical purpose or the feature they support
6. Represent the domain's core concepts and how files implement them
7. Show how files collaborate to implement domain behaviors
8. Illustrate key abstractions and patterns used in the domain
9. Include important domain-specific data structures and their transformations
10. Show initialization sequences and important process flows
11. DO NOT create a simple procedural flowchart - focus on logical relationships
12. DO NOT use tooltips or click actions - they consume unnecessary tokens
13. The diagram should be 3000-6000 tokens in size to provide comprehensive detail

Styling Guidelines (IMPORTANT):
1. Use a VERTICAL layout rather than horizontal for better readability
2. Use PASTEL COLORS for all nodes and subgraphs - avoid bright or dark colors
3. Use this consistent color scheme:
   - Core domain files: pastel blue (#D4F1F9)
   - Supporting/utility files: pastel yellow (#FFF8DC)
   - Data structure files: pastel green (#E0F8E0)
   - Error handling files: pastel red (#FFE4E1)
   - Initialization/setup files: pastel purple (#E6E6FA)
   - Logical groupings/subgraphs: very light gray (#F8F8F8) with pastel borders
4. Use rounded rectangles for most nodes: node[shape="rounded-rectangle"]
5. Use different node shapes to represent different types of files when appropriate
6. Use consistent line thickness and arrow styles
7. Ensure proper spacing between nodes and subgraphs
8. Follow strict mermaid.js syntax to ensure the diagram renders correctly
9. Avoid use of brackets, `(` or `)` and avoid adding any notes. Please follow the mermaid syntax properly.

Your output should ONLY contain a valid mermaid diagram enclosed in triple backticks with the mermaid tag.
Ensure the diagram follows proper mermaid.js syntax and is renderable without any syntax errors.
"""

        # Get hierarchy level if available
        level = self.domain_levels.get(domain, 0) if self.hierarchical else 0

        # Create the user prompt
        truncation_note = ""
        if truncated_files:
            truncation_percentage = len(truncated_files) / (len(file_details) + len(truncated_files)) * 100
            truncation_note = f"\n\nNOTE: Due to token limits, {len(truncated_files)} files ({truncation_percentage:.1f}% of total) were truncated from this diagram. The diagram shows the {len(file_details)} most important files that fit within the token limit."

        file_details_json = json.dumps(file_details, indent=2)
        user_prompt = f"""Create a detailed mermaid diagram for the leaf domain: {domain} (Hierarchy Level: {level})

This is a leaf domain with no subdomains. Focus on showing the LOGICAL RELATIONSHIPS and INTERACTIONS between files, not just their implementation details.

Here are the files in this domain with their code:

{file_details_json}{truncation_note}

Please generate a comprehensive mermaid diagram that shows:
1. The LOGICAL PURPOSE of each file within the domain context
2. How files COLLABORATE to implement domain behaviors
3. The MEANINGFUL RELATIONSHIPS and dependencies between files
4. How files are GROUPED by their logical purpose or features they support
5. The domain's CORE CONCEPTS and how files implement them
6. Important DOMAIN-SPECIFIC DATA STRUCTURES and their transformations
7. Key ABSTRACTIONS and PATTERNS used in the domain

IMPORTANT STYLING REQUIREMENTS:
- Use a VERTICAL layout
- Use PASTEL COLORS for all nodes and subgraphs
- Follow the color scheme specified in the system prompt
- Ensure the diagram is well-structured and easy to read
- Follow strict mermaid.js syntax to ensure the diagram renders correctly
- Avoid use of brackets, `(` or `)` and avoid adding any notes. Please follow the mermaid syntax properly.

Make sure to capture the LOGICAL RELATIONSHIPS between files, not just their procedural flow.
DO NOT create a simple procedural flowchart - focus on meaningful interactions and relationships.
DO NOT use tooltips or click actions in the diagram.
"""

        # Analyze token count to determine which model to use
        total_tokens = num_tokens_from_string(system_prompt + user_prompt, self.diagram_generator.model_name)
        use_openrouter = self.diagram_generator.use_openrouter and total_tokens > self.diagram_generator.openrouter_token_threshold

        try:
            # Call the appropriate API based on model type and token count
            if use_openrouter:
                # Call the OpenRouter API
                response, raw_response = await self.diagram_generator._call_openrouter_api(
                    system_prompt=system_prompt,
                    user_prompt=user_prompt
                )
            elif self.diagram_generator.model_type == "claude":
                # Call the Claude API
                response = await self.diagram_generator.claude_client.generate(
                    system_prompt=system_prompt,
                    prompt=user_prompt,
                    # max_tokens=8000,
                    temperature=0.7,
                    top_p=0.8
                )
                raw_response = response
            else:  # OpenAI
                # Call the OpenAI API
                response, raw_response = await self.diagram_generator._call_openai_api(
                    system_prompt=system_prompt,
                    user_prompt=user_prompt
                )

            # Extract the mermaid diagram
            diagram = self.diagram_generator._extract_mermaid_diagram(response)

            if not diagram:
                logger.warning(f"No mermaid diagram found in response for {domain}")
                diagram = response

            return diagram, raw_response

        except Exception as e:
            logger.error(f"Error generating diagram for {domain}: {e}")
            return "", f"Error: {str(e)}"

    async def generate_combined_diagram(
        self,
        domain_name: str,
        subdomains: Dict[str, str],
        level: int
    ) -> Tuple[str, str]:
        """
        Generate a combined diagram for a domain and its subdomains.

        Args:
            domain_name: Name of the domain
            subdomains: Dictionary mapping subdomain names to their diagram file paths
            level: Hierarchy level (higher means more abstract)

        Returns:
            Tuple of (diagram_content, raw_response)
        """
        logger.info(f"Generating combined diagram for domain: {domain_name} with {len(subdomains)} subdomains")

        # Apply rate limiting
        await self.diagram_generator.rate_limiter.acquire()

        # Read subdomain diagrams
        subdomain_diagrams = {}
        for subdomain, file_path in subdomains.items():
            try:
                with open(file_path, 'r') as f:
                    subdomain_diagrams[subdomain] = f.read()
            except Exception as e:
                logger.error(f"Error reading subdomain diagram {file_path}: {e}")
                subdomain_diagrams[subdomain] = f"Error reading diagram: {str(e)}"

        # Create the system prompt
        system_prompt = """You are an expert software architect who creates clear, informative mermaid diagrams to visualize code architecture.
Your task is to create a combined mermaid diagram that represents a domain and its subdomains in a codebase, preserving the logical relationships and interactions between components.

Guidelines for creating the combined diagram:
1. SIMPLIFY and ABSTRACT the subdomain diagrams to create a cohesive higher-level view
2. Focus on the LOGICAL RELATIONSHIPS between subdomains
3. Show how subdomains COLLABORATE to implement domain behaviors
4. Highlight the CONCEPTUAL FLOW of data and control between subdomains
5. Represent the domain's CORE CONCEPTS and how subdomains implement them
6. Include important DOMAIN-SPECIFIC DATA STRUCTURES that span multiple subdomains
7. Show KEY ABSTRACTIONS and PATTERNS used across the domain
8. PRESERVE the most important elements from each subdomain diagram
9. REMOVE excessive detail while keeping the essential structure
10. MAINTAIN consistent naming and terminology across subdomains
11. DO NOT simply copy all elements from all subdomain diagrams - that would be too cluttered
12. DO NOT use tooltips or click actions - they consume unnecessary tokens
13. The diagram should be 3000-6000 tokens in size to provide comprehensive detail

Styling Guidelines (IMPORTANT):
1. Use a VERTICAL layout rather than horizontal for better readability
2. MAINTAIN COLOR CONSISTENCY with the subdomain diagrams - use the same colors for components as in their original diagrams
3. Use PASTEL COLORS for all new components
4. Use this consistent color scheme:
   - Core domain components: pastel blue (#D4F1F9)
   - Supporting/utility components: pastel yellow (#FFF8DC)
   - Data structure components: pastel green (#E0F8E0)
   - Error handling components: pastel red (#FFE4E1)
   - Initialization/setup components: pastel purple (#E6E6FA)
   - Logical groupings/subgraphs: very light gray (#F8F8F8) with pastel borders
5. Use rounded rectangles for most nodes: node[shape="rounded-rectangle"]
6. Use different node shapes to represent different types of components when appropriate
7. Use consistent line thickness and arrow styles
8. Ensure proper spacing between nodes and subgraphs
9. Follow strict mermaid.js syntax to ensure the diagram renders correctly
10. Avoid use of brackets, `(` or `)` and avoid adding any notes. Please follow the mermaid syntax properly.

Your output should ONLY contain a valid mermaid diagram enclosed in triple backticks with the mermaid tag.
Ensure the diagram follows proper mermaid.js syntax and is renderable without any syntax errors.
"""

        # Create the user prompt
        subdomain_diagrams_formatted = ""
        for subdomain, diagram in subdomain_diagrams.items():
            subdomain_diagrams_formatted += f"\n\n## Subdomain: {subdomain}\n\n{diagram}"

        user_prompt = f"""Create a combined mermaid diagram for the domain: {domain_name} (Hierarchy Level: {level})

This domain contains {len(subdomains)} subdomains. Your task is to create a HIGHER-LEVEL diagram that shows how these subdomains relate to each other and work together to implement the domain's functionality.

Here are the diagrams for each subdomain:
{subdomain_diagrams_formatted}

Please generate a comprehensive combined diagram that shows:
1. The LOGICAL PURPOSE of each subdomain within the overall domain context
2. How subdomains COLLABORATE to implement domain behaviors
3. The MEANINGFUL RELATIONSHIPS and dependencies between subdomains
4. The domain's CORE CONCEPTS and how they span across subdomains
5. Important DOMAIN-SPECIFIC DATA STRUCTURES that are shared between subdomains
6. Key ABSTRACTIONS and PATTERNS used across the domain

IMPORTANT STYLING REQUIREMENTS:
- MAINTAIN COLOR CONSISTENCY with the subdomain diagrams - use the same colors for components as in their original diagrams
- Use PASTEL COLORS for all new components
- Follow the color scheme specified in the system prompt
- Ensure proper spacing and layout for readability
- Follow strict mermaid.js syntax to ensure the diagram renders correctly
- Avoid use of brackets, `(` or `)` and avoid adding any notes. Please follow the mermaid syntax properly.

Make sure to SIMPLIFY and ABSTRACT the subdomain diagrams to create a cohesive higher-level view.
DO NOT simply copy all elements from all subdomain diagrams - that would be too cluttered.
DO NOT use tooltips or click actions in the diagram.
"""

        # Analyze token count to determine which model to use
        total_tokens = num_tokens_from_string(system_prompt + user_prompt, self.diagram_generator.model_name)
        use_openrouter = self.diagram_generator.use_openrouter and total_tokens > self.diagram_generator.openrouter_token_threshold

        try:
            # Call the appropriate API based on model type and token count
            if use_openrouter:
                # Call the OpenRouter API
                response, raw_response = await self.diagram_generator._call_openrouter_api(
                    system_prompt=system_prompt,
                    user_prompt=user_prompt
                )
            elif self.diagram_generator.model_type == "claude":
                # Call the Claude API
                response = await self.diagram_generator.claude_client.generate(
                    system_prompt=system_prompt,
                    prompt=user_prompt,
                    # max_tokens=8000,
                    temperature=0.7,
                    top_p=0.8
                )
                raw_response = response
            else:  # OpenAI
                # Call the OpenAI API
                response, raw_response = await self.diagram_generator._call_openai_api(
                    system_prompt=system_prompt,
                    user_prompt=user_prompt
                )

            # Extract the mermaid diagram
            diagram = self.diagram_generator._extract_mermaid_diagram(response)

            if not diagram:
                logger.warning(f"No mermaid diagram found in response for {domain_name}")
                diagram = response

            return diagram, raw_response

        except Exception as e:
            logger.error(f"Error generating combined diagram for {domain_name}: {e}")
            return "", f"Error: {str(e)}"

    async def _process_with_worker_pool(self, tasks, num_workers):
        """
        Process tasks using a worker pool.

        Args:
            tasks: List of (domain, task) tuples
            num_workers: Number of worker coroutines

        Returns:
            Dictionary mapping domains to (file_path, diagram, raw_response) tuples
        """
        queue = asyncio.Queue()
        results = {}
        tasks_total = len(tasks)

        # Create a status tracker to monitor progress
        status_tracker = StatusTracker(tasks_total)

        # Create a shared rate limiter state to track API usage across workers
        shared_rate_limiter = {
            "last_update_time": time.time(),
            "requests_in_flight": 0,
            "max_concurrent_requests": min(num_workers, 50)  # Limit concurrent API requests
        }

        # Log initial status
        logger.info(f"Starting worker pool with {num_workers} workers for {tasks_total} tasks")

        # Define the worker function
        async def worker(worker_id):
            while True:
                try:
                    # Get a task from the queue with timeout
                    try:
                        domain, task_coro = await asyncio.wait_for(queue.get(), timeout=1.0)
                    except asyncio.TimeoutError:
                        # Check if queue is empty and all tasks are done
                        if queue.empty() and len(results) >= tasks_total:
                            break
                        continue

                    # Process the task
                    try:
                        logger.info(f"Worker {worker_id}: Processing domain {domain}")
                        start_time = time.time()

                        # Execute the task with proper error handling
                        try:
                            # Apply rate limiting before executing the task
                            # Check if we're exceeding the maximum concurrent requests
                            while shared_rate_limiter["requests_in_flight"] >= shared_rate_limiter["max_concurrent_requests"]:
                                # Wait a bit before checking again
                                await asyncio.sleep(0.1)

                            # Increment the in-flight requests counter
                            shared_rate_limiter["requests_in_flight"] += 1

                            try:
                                # Apply standard rate limiting
                                await self.diagram_generator.rate_limiter.acquire()

                                # Execute the task
                                diagram, raw_response = await task_coro
                            finally:
                                # Decrement the in-flight requests counter
                                shared_rate_limiter["requests_in_flight"] -= 1

                            # Save the diagram to a file
                            safe_domain = domain.replace('/', '_').replace(' ', '_')
                            file_path = os.path.join(self.diagrams_dir, f"{safe_domain}.md")
                            with open(file_path, 'w') as f:
                                f.write(diagram)

                            # Store the result
                            results[domain] = (file_path, diagram, raw_response)

                            # Update status tracker
                            elapsed = time.time() - start_time
                            status_tracker.task_completed(domain, elapsed)

                            # Log progress periodically
                            if status_tracker.completed_tasks % 5 == 0 or status_tracker.completed_tasks == tasks_total:
                                logger.info(f"Progress: {status_tracker.get_progress()}")

                            logger.info(f"Worker {worker_id}: Completed domain {domain} in {elapsed:.2f}s")

                        except Exception as e:
                            logger.error(f"Worker {worker_id}: Error processing domain {domain}: {str(e)}")
                            results[domain] = (None, "", f"Error: {str(e)}")
                            status_tracker.task_failed()
                            logger.info(f"Worker {worker_id}: Failed domain {domain}")

                    except Exception as e:
                        logger.error(f"Worker {worker_id}: Unexpected error: {str(e)}")
                        if domain is not None:
                            results[domain] = (None, "", f"Error: {str(e)}")
                            status_tracker.task_failed()

                    finally:
                        # Mark the task as done
                        queue.task_done()

                        # Log overall progress after each task
                        completed = status_tracker.completed_tasks
                        failed = status_tracker.failed_tasks
                        total = status_tracker.total_tasks
                        logger.debug(f"Overall progress: {completed + failed}/{total} tasks processed ({completed} completed, {failed} failed)")

                except Exception as e:
                    logger.error(f"Worker {worker_id}: Critical error in worker loop: {str(e)}")

        # Start workers
        workers = []
        for i in range(num_workers):
            worker_task = asyncio.create_task(worker(i+1))
            workers.append(worker_task)

        # Add tasks to queue
        for domain, task in tasks:
            await queue.put((domain, task))

        # Wait for all tasks to be processed
        try:
            # Wait for queue to be empty and all tasks to be processed
            await queue.join()

            # Additional check to ensure all results are filled
            if len(results) < tasks_total:
                logger.warning(f"Some results are missing ({len(results)}/{tasks_total}), waiting longer...")
                await asyncio.sleep(5.0)

        finally:
            # Cancel all workers
            for worker_task in workers:
                worker_task.cancel()

            # Wait for workers to be cancelled
            await asyncio.gather(*workers, return_exceptions=True)

            # Log final statistics
            stats = status_tracker.get_stats()
            logger.info(f"Worker pool completed: {stats['completed_tasks']}/{stats['total_tasks']} tasks completed ({stats['success_rate']:.1f}% success rate)")
            logger.info(f"Average task time: {stats['average_task_time']:.2f}s, Median: {stats['median_task_time']:.2f}s")
            logger.info(f"Processing rate: {stats['tasks_per_second']:.2f} tasks/second")

        return results, status_tracker.get_stats()

    async def generate_all_diagrams(self) -> JSONDiagramGenerationResult:
        """
        Generate diagrams for all domains in the JSON file using a worker pool.

        If hierarchical=True, also generates combined diagrams for intermediate domains.

        Returns:
            JSONDiagramGenerationResult containing the generation results
        """
        start_time = time.time()
        logger.info("Starting diagram generation for all domains")

        # Read the domain file JSON
        domain_file_data = self.read_domain_file_json()

        if not domain_file_data:
            logger.error("No domains found in the JSON file")
            return JSONDiagramGenerationResult(
                success=False,
                error_message="No domains found in the JSON file",
                domain_file_stats=self.domain_stats
            )

        # Initialize result
        result = JSONDiagramGenerationResult(
            success=True,
            model_used=f"{self.diagram_generator.model_type}:{self.diagram_generator.model_name}",
            domain_file_stats=self.domain_stats
        )

        # Add hierarchy info if hierarchical generation is enabled
        if self.hierarchical:
            result.hierarchy_info = {
                "total_levels": 0,
                "domains_by_level": {},
                "processing_time_by_level": {}
            }

            # Sort domains by level (bottom-up)
            domains_by_level = self.sort_domains_by_level()
            result.hierarchy_info["total_levels"] = len(domains_by_level)

            # Dictionary to track generated diagrams
            generated_diagrams = {}

            # Process each level, starting from the bottom (leaf nodes)
            for level, domains in sorted(domains_by_level.items(), reverse=True):
                level_start_time = time.time()
                logger.info(f"Processing level {level} with {len(domains)} domains")
                result.hierarchy_info["domains_by_level"][level] = len(domains)

                # Separate leaf domains from intermediate domains
                leaf_domains = [d for d in domains if d in self.leaf_domains]
                intermediate_domains = [d for d in domains if d not in self.leaf_domains]

                logger.info(f"Level {level}: {len(leaf_domains)} leaf domains, {len(intermediate_domains)} intermediate domains")

                # Process leaf domains in parallel
                if leaf_domains:
                    logger.info(f"Generating diagrams for {len(leaf_domains)} leaf domains in parallel")

                    # Create a list of tasks for parallel processing
                    tasks = []
                    for domain in leaf_domains:
                        files = self.domain_file_data.get(domain, {})
                        task = self.generate_leaf_diagram_from_files(domain, files)
                        tasks.append((domain, task))

                    # Process leaf domains in parallel with a worker pool
                    max_workers = min(50, self.max_concurrent_tasks)  # Cap at 50 workers max
                    worker_results, processing_stats = await self._process_with_worker_pool(tasks, max_workers)

                    # Process results
                    for domain, (file_path, _, raw_response) in worker_results.items():
                        if file_path:
                            # Store in result
                            result.diagram_files[domain] = file_path
                            result.raw_responses[domain] = raw_response

                            # Store for hierarchical processing
                            generated_diagrams[domain] = file_path

                # Process intermediate domains in parallel
                if intermediate_domains:
                    logger.info(f"Generating diagrams for {len(intermediate_domains)} intermediate domains in parallel")

                    # Create a list of tasks for parallel processing
                    tasks = []
                    for domain in intermediate_domains:
                        # Get child diagrams for this domain
                        child_diagrams = {}
                        for child in self.domain_children.get(domain, set()):
                            if child in generated_diagrams:
                                child_diagrams[child] = generated_diagrams[child]

                        # Skip if no child diagrams are available
                        if not child_diagrams:
                            logger.warning(f"No child diagrams available for intermediate domain: {domain}. Skipping.")
                            continue

                        # Get hierarchy level
                        domain_level = self.domain_levels.get(domain, 0)

                        # Create a task for generating the combined diagram
                        task = self.generate_combined_diagram(domain, child_diagrams, level=domain_level)
                        tasks.append((domain, task, child_diagrams))

                    # Process intermediate domains in parallel with a worker pool
                    if tasks:
                        logger.info(f"Processing {len(tasks)} intermediate domain diagram generation tasks with {self.max_concurrent_tasks} workers")

                        # Use worker pool to process tasks
                        max_workers = min(50, self.max_concurrent_tasks)  # Cap at 50 workers max

                        # Create a custom worker function for intermediate domains
                        async def process_intermediate_domain(task_data):
                            domain, task, _ = task_data
                            try:
                                # Generate combined diagram
                                combined_diagram, raw_response = await task

                                # Save the diagram to the hierarchical diagrams directory
                                safe_domain = domain.replace('/', '_').replace(' ', '_').replace('->', '_')
                                file_path = os.path.join(self.hierarchical_diagrams_dir, f"{safe_domain}.md")
                                with open(file_path, 'w') as f:
                                    f.write(combined_diagram)

                                logger.info(f"Generated combined diagram for intermediate domain: {domain}")
                                return domain, (file_path, combined_diagram, raw_response)
                            except Exception as e:
                                logger.error(f"Error generating combined diagram for {domain}: {e}")
                                import traceback
                                logger.error(traceback.format_exc())
                                return domain, (None, None, f"Error: {str(e)}")

                        # Create a semaphore to limit concurrent tasks
                        semaphore = asyncio.Semaphore(max_workers)

                        # Create a list of worker tasks
                        worker_tasks = []
                        for task_data in tasks:
                            async def worker(task_data=task_data):
                                async with semaphore:
                                    return await process_intermediate_domain(task_data)
                            worker_tasks.append(worker())

                        # Wait for all tasks to complete
                        worker_results = await asyncio.gather(*worker_tasks)

                        # Process results
                        for domain, (file_path, _, raw_response) in worker_results:
                            if file_path:
                                # Store in result
                                result.diagram_files[domain] = file_path
                                result.raw_responses[domain] = raw_response

                                # Store for hierarchical processing
                                generated_diagrams[domain] = file_path

                        logger.info(f"Completed processing {len(tasks)} intermediate domains in parallel")

                # Calculate level processing time
                level_end_time = time.time()
                level_processing_time = level_end_time - level_start_time
                result.hierarchy_info["processing_time_by_level"][level] = level_processing_time
                logger.info(f"Completed processing level {level} in {level_processing_time:.2f} seconds")

            # Add processing statistics
            result.processing_stats = {
                "total_domains": len(self.all_domains),
                "leaf_domains": len(self.leaf_domains),
                "intermediate_domains": len(self.all_domains) - len(self.leaf_domains),
                "total_processing_time": time.time() - start_time
            }

        else:
            # Non-hierarchical generation (original implementation)
            # Create a list of tasks for parallel processing
            tasks = []
            for domain, files in domain_file_data.items():
                task = self.generate_leaf_diagram_from_files(domain, files)
                tasks.append((domain, task))

            # Process domains in parallel with a worker pool
            logger.info(f"Processing {len(tasks)} domain diagram generation tasks with {self.max_concurrent_tasks} workers")

            # Use worker pool to process tasks
            max_workers = min(50, self.max_concurrent_tasks)  # Cap at 50 workers max
            worker_results, processing_stats = await self._process_with_worker_pool(tasks, max_workers)

            # Process results
            for domain, (file_path, _, raw_response) in worker_results.items():
                if file_path:
                    result.diagram_files[domain] = file_path
                    result.raw_responses[domain] = raw_response

            # Add processing statistics
            result.processing_stats = processing_stats

        # Add OpenRouter usage information if available
        # Note: We check for the attribute dynamically to avoid static type checking errors
        openrouter_info = getattr(self.diagram_generator, 'openrouter_info', None)
        if openrouter_info:
            result.openrouter_info = openrouter_info

        end_time = time.time()
        logger.info(f"Completed diagram generation for all domains in {end_time - start_time:.2f} seconds")
        logger.info(f"Generated {len(result.diagram_files)} diagrams out of {len(domain_file_data)} domains")

        # Log success rate
        success_rate = len(result.diagram_files) / len(domain_file_data) * 100 if domain_file_data else 0
        logger.info(f"Success rate: {success_rate:.2f}% ({len(result.diagram_files)}/{len(domain_file_data)})")

        return result


    async def generate_hierarchical_diagrams_from_existing(self) -> JSONDiagramGenerationResult:
        """
        Generate hierarchical diagrams from existing leaf diagrams and domain analysis YAML.

        This method:
        1. Builds domain hierarchy from domain analysis YAML
        2. Maps existing leaf diagrams to the hierarchy
        3. Generates combined diagrams for intermediate domains

        Returns:
            JSONDiagramGenerationResult containing the generation results
        """
        if not self.domain_analysis_yaml_path or not self.existing_diagrams_dir:
            logger.error("Missing required parameters: domain_analysis_yaml_path and existing_diagrams_dir")
            return JSONDiagramGenerationResult(
                success=False,
                error_message="Missing required parameters: domain_analysis_yaml_path and existing_diagrams_dir"
            )

        start_time = time.time()
        logger.info("Starting hierarchical diagram generation from existing leaf diagrams")

        # Build domain hierarchy from domain analysis YAML
        self.build_domain_hierarchy_from_analysis()

        if not self.all_domains:
            logger.error("Failed to build domain hierarchy from domain analysis YAML")
            return JSONDiagramGenerationResult(
                success=False,
                error_message="Failed to build domain hierarchy from domain analysis YAML"
            )

        # Initialize result
        result = JSONDiagramGenerationResult(
            success=True,
            model_used=f"{self.diagram_generator.model_type}:{self.diagram_generator.model_name}"
        )

        # Add hierarchy info
        result.hierarchy_info = {
            "total_levels": max(self.domain_levels.values()) + 1 if self.domain_levels else 0,
            "domains_by_level": {},
            "processing_time_by_level": {}
        }

        # Map existing leaf diagrams to the hierarchy
        logger.info(f"Mapping existing leaf diagrams from: {self.existing_diagrams_dir}")
        existing_diagrams = {}

        # Get all markdown files in the existing diagrams directory
        for filename in os.listdir(self.existing_diagrams_dir):
            if filename.endswith('.md'):
                file_path = os.path.join(self.existing_diagrams_dir, filename)

                # Convert filename to domain name (replace underscores with spaces)
                domain_name = filename[:-3].replace('_', ' ')

                # Try to find a matching domain in our hierarchy
                matching_domain = None

                # First, try to find an exact match for the full domain path
                for domain in self.all_domains:
                    domain_normalized = domain.lower().replace(' ', '_').replace('->', '_')
                    if domain_normalized == domain_name.lower():
                        matching_domain = domain
                        break

                # If no exact match, try to match the last part of the domain path
                if not matching_domain:
                    for domain in self.all_domains:
                        # Get the last part of the domain path (after the last "->")
                        domain_parts = domain.split(" -> ")
                        last_part = domain_parts[-1]

                        # Normalize both strings for comparison
                        last_part_normalized = last_part.lower().replace(' ', '_').replace('&', 'and')
                        filename_normalized = domain_name.lower().replace(' ', '_').replace('&', 'and')

                        # Check if the last part matches the filename (ignoring case, underscores, etc.)
                        if last_part_normalized == filename_normalized or last_part_normalized in filename_normalized or filename_normalized in last_part_normalized:
                            matching_domain = domain
                            break

                if matching_domain:
                    existing_diagrams[matching_domain] = file_path
                    logger.info(f"Mapped existing diagram to domain: {matching_domain}")
                else:
                    logger.warning(f"Could not map existing diagram to any domain: {filename}")

        logger.info(f"Mapped {len(existing_diagrams)} existing diagrams to domains")

        # Log some example mappings for debugging
        example_mappings = list(existing_diagrams.items())[:5]  # Show up to 5 examples
        for domain, file_path in example_mappings:
            logger.info(f"Example mapping: {os.path.basename(file_path)} -> {domain}")

        # Log unmapped files
        unmapped_files = []
        for filename in os.listdir(self.existing_diagrams_dir):
            if filename.endswith('.md'):
                file_path = os.path.join(self.existing_diagrams_dir, filename)
                if not any(file_path == mapped_path for mapped_path in existing_diagrams.values()):
                    unmapped_files.append(filename)

        if unmapped_files:
            logger.warning(f"Unmapped files: {len(unmapped_files)}")
            for filename in unmapped_files[:10]:  # Show up to 10 examples
                logger.warning(f"  - {filename}")

        # Sort domains by level
        domains_by_level = self.sort_domains_by_level()

        # Dictionary to track generated diagrams
        generated_diagrams = existing_diagrams.copy()

        # Process each level, starting from the bottom (leaf nodes)
        for level, domains in sorted(domains_by_level.items(), reverse=True):
            level_start_time = time.time()
            logger.info(f"Processing level {level} with {len(domains)} domains")
            result.hierarchy_info["domains_by_level"][level] = len(domains)

            # Skip leaf domains (we already have their diagrams)
            if level == max(domains_by_level.keys()):
                logger.info(f"Skipping leaf domains at level {level} (using existing diagrams)")

                # Add existing leaf diagrams to the result
                for domain, file_path in existing_diagrams.items():
                    if domain in domains:
                        result.diagram_files[domain] = file_path

                # Calculate level processing time
                level_end_time = time.time()
                level_processing_time = level_end_time - level_start_time
                result.hierarchy_info["processing_time_by_level"][level] = level_processing_time
                logger.info(f"Completed processing level {level} in {level_processing_time:.2f} seconds")
                continue

            # Process intermediate domains in parallel
            logger.info(f"Generating diagrams for {len(domains)} intermediate domains in parallel")

            # Create a list of tasks for parallel processing
            tasks = []
            for domain in domains:
                # Get child diagrams for this domain
                child_diagrams = {}
                for child in self.domain_children.get(domain, set()):
                    if child in generated_diagrams:
                        child_diagrams[child] = generated_diagrams[child]

                # Skip if no child diagrams are available
                if not child_diagrams:
                    logger.warning(f"No child diagrams available for intermediate domain: {domain}. Skipping.")
                    continue

                # Get hierarchy level
                domain_level = self.domain_levels.get(domain, 0)

                # Create a task for generating the combined diagram
                task = self.generate_combined_diagram(domain, child_diagrams, level=domain_level)
                tasks.append((domain, task, child_diagrams))

                # Log task creation for debugging
                logger.info(f"Created task for intermediate domain: {domain} with {len(child_diagrams)} child diagrams")

            # Process intermediate domains in parallel with a worker pool
            if tasks:
                logger.info(f"Processing {len(tasks)} intermediate domain diagram generation tasks with {self.max_concurrent_tasks} workers")

                # Use worker pool to process tasks
                max_workers = min(50, self.max_concurrent_tasks)  # Cap at 50 workers max

                # Create a custom worker function for intermediate domains
                async def process_intermediate_domain(task_data):
                    domain, task, child_diagrams = task_data
                    start_time = time.time()
                    try:
                        logger.info(f"Starting combined diagram generation for domain: {domain}")

                        # Generate combined diagram
                        combined_diagram, raw_response = await task

                        # Save the diagram to the hierarchical diagrams directory
                        safe_domain = domain.replace('/', '_').replace(' ', '_').replace('->', '_')
                        file_path = os.path.join(self.hierarchical_diagrams_dir, f"{safe_domain}.md")
                        with open(file_path, 'w') as f:
                            f.write(combined_diagram)

                        end_time = time.time()
                        processing_time = end_time - start_time
                        logger.info(f"Generated combined diagram for intermediate domain: {domain} in {processing_time:.2f} seconds")
                        return domain, (file_path, combined_diagram, raw_response)
                    except Exception as e:
                        end_time = time.time()
                        processing_time = end_time - start_time
                        logger.error(f"Error generating combined diagram for {domain} after {processing_time:.2f} seconds: {e}")
                        logger.error(f"Child diagrams: {list(child_diagrams.keys())}")
                        import traceback
                        logger.error(traceback.format_exc())
                        return domain, (None, None, f"Error: {str(e)}")

                # Create a semaphore to limit concurrent tasks
                semaphore = asyncio.Semaphore(max_workers)

                # Define the worker function
                async def worker(task_data):
                    async with semaphore:
                        return await process_intermediate_domain(task_data)

                # Create a list of worker tasks
                worker_tasks = [worker(task_data) for task_data in tasks]

                # Log the start of parallel processing
                logger.info(f"Starting parallel processing of {len(worker_tasks)} tasks with {max_workers} workers")

                # Wait for all tasks to complete
                start_time = time.time()
                worker_results = await asyncio.gather(*worker_tasks)
                end_time = time.time()

                # Log completion time
                total_time = end_time - start_time
                tasks_per_second = len(worker_tasks) / total_time if total_time > 0 else 0
                logger.info(f"Completed parallel processing in {total_time:.2f} seconds ({tasks_per_second:.2f} tasks/second)")

                # Process results
                successful_tasks = 0
                for domain, (file_path, _, raw_response) in worker_results:
                    if file_path:
                        # Store in result
                        result.diagram_files[domain] = file_path
                        result.raw_responses[domain] = raw_response

                        # Store for hierarchical processing
                        generated_diagrams[domain] = file_path

                        # Count successful tasks
                        successful_tasks += 1

                # Calculate success rate
                success_rate = (successful_tasks / len(tasks)) * 100 if tasks else 0

                # Add processing statistics to the result
                level_stats = {
                    "total_tasks": len(tasks),
                    "successful_tasks": successful_tasks,
                    "failed_tasks": len(tasks) - successful_tasks,
                    "success_rate": success_rate,
                    "total_processing_time": total_time,
                    "tasks_per_second": tasks_per_second
                }

                # Store level statistics in the result
                result.hierarchy_info[f"level_{level}_stats"] = level_stats

                logger.info(f"Completed processing {len(tasks)} intermediate domains in parallel")
                logger.info(f"Success rate: {success_rate:.2f}% ({successful_tasks}/{len(tasks)})")

            # Calculate level processing time
            level_end_time = time.time()
            level_processing_time = level_end_time - level_start_time
            result.hierarchy_info["processing_time_by_level"][level] = level_processing_time
            logger.info(f"Completed processing level {level} in {level_processing_time:.2f} seconds")

        # Add processing statistics
        result.processing_stats = {
            "total_domains": len(self.all_domains),
            "leaf_domains": len(self.leaf_domains),
            "intermediate_domains": len(self.all_domains) - len(self.leaf_domains),
            "existing_diagrams": len(existing_diagrams),
            "generated_diagrams": len(generated_diagrams) - len(existing_diagrams),
            "total_processing_time": time.time() - start_time
        }

        end_time = time.time()
        logger.info(f"Completed hierarchical diagram generation in {end_time - start_time:.2f} seconds")
        logger.info(f"Generated {len(result.diagram_files)} diagrams")

        return result

async def main():
    """Main entry point for the JSON domain diagram generator."""
    import argparse

    parser = argparse.ArgumentParser(description="Generate mermaid diagrams from domain file JSON")
    parser.add_argument("--domain-file-json", help="Path to the domain file JSON")
    parser.add_argument("--output-dir", required=True, help="Directory to save generated diagrams")

    # Model selection
    parser.add_argument("--model-type", default="openai", choices=["claude", "openai"],
                        help="Type of model to use (claude or openai)")

    # Claude parameters
    parser.add_argument("--claude-api-key", help="Anthropic API key (if not provided, will try to get from environment)")
    parser.add_argument("--claude-model", default="claude-3-7-sonnet-20250219", help="Claude model to use")

    # OpenAI parameters
    parser.add_argument("--openai-api-key", help="OpenAI API key (if not provided, will try to get from environment)")
    parser.add_argument("--openai-model", default="gpt-4.1-2025-04-14", help="OpenAI model to use")

    # OpenRouter parameters
    parser.add_argument("--use-openrouter", action="store_true", default=False,
                        help="Whether to use OpenRouter for large domains")
    parser.add_argument("--openrouter-api-key", help="OpenRouter API key (if not provided, will try to get from environment)")
    parser.add_argument("--openrouter-model", default="google/gemini-2.5-pro-preview",
                        help="OpenRouter model to use (Gemini)")
    parser.add_argument("--openrouter-token-threshold", type=int, default=45000,
                        help="Token threshold to switch to OpenRouter")
    parser.add_argument("--openrouter-max-concurrent", type=int, default=3,
                        help="Maximum number of concurrent OpenRouter API calls")

    # Common parameters
    # parser.add_argument("--max-tokens", type=int, default=8000, help="Maximum tokens to generate")
    parser.add_argument("--temperature", type=float, default=0.5, help="Sampling temperature")
    parser.add_argument("--requests-per-minute", type=float, default=7000, help="Rate limit for API requests")
    parser.add_argument("--tokens-per-minute", type=float, default=20000000, help="Token rate limit for API requests")

    # Parallelization parameters
    parser.add_argument("--max-concurrent-tasks", type=int, default=50,
                        help="Maximum number of concurrent tasks for parallel processing")

    # Hierarchy parameters
    parser.add_argument("--hierarchical", action="store_true", default=False,
                        help="Whether to generate hierarchical diagrams")
    parser.add_argument("--max-hierarchy-depth", type=int, default=None,
                        help="Maximum hierarchy depth to process (None for all levels)")

    # Existing diagrams parameters
    parser.add_argument("--domain-analysis-yaml", default="",
                        help="Path to domain analysis YAML file for building hierarchy")
    parser.add_argument("--existing-diagrams-dir", default="",
                        help="Directory containing existing leaf-level diagrams")
    parser.add_argument("--from-existing", action="store_true", default=False,
                        help="Generate hierarchical diagrams from existing leaf diagrams")

    # Caching parameters
    parser.add_argument("--cache-dir", default=None,
                        help="Directory to cache intermediate results (None for no caching)")
    parser.add_argument("--no-cache", action="store_true", help="Disable caching")

    args = parser.parse_args()

    # Validate arguments
    if not args.from_existing and not args.domain_file_json:
        parser.error("--domain-file-json is required when not using --from-existing")

    if args.from_existing and (not args.domain_analysis_yaml or not args.existing_diagrams_dir):
        parser.error("--domain-analysis-yaml and --existing-diagrams-dir are required when using --from-existing")

    # Set domain file path
    if args.domain_file_json:
        args.domain_file_repomap_json = args.domain_file_json
    else:
        args.domain_file_repomap_json = ""

    try:
        # Create output directory if it doesn't exist
        os.makedirs(args.output_dir, exist_ok=True)

        # Create cache directory if specified
        if args.cache_dir:
            os.makedirs(args.cache_dir, exist_ok=True)

        # Create a JSON domain diagram generator
        generator = JSONDomainDiagramGenerator(
            domain_file_json_path=args.domain_file_repomap_json,
            output_dir=args.output_dir,
            model_type=args.model_type,
            # Claude parameters
            claude_api_key=args.claude_api_key,
            claude_model=args.claude_model,
            # OpenAI parameters
            openai_api_key=args.openai_api_key,
            openai_model=args.openai_model,
            # OpenRouter parameters
            use_openrouter=args.use_openrouter,
            openrouter_api_key=args.openrouter_api_key,
            openrouter_model=args.openrouter_model,
            openrouter_token_threshold=args.openrouter_token_threshold,
            openrouter_max_concurrent=args.openrouter_max_concurrent,
            # Common parameters
            # max_tokens=args.max_tokens,
            temperature=args.temperature,
            max_requests_per_minute=args.requests_per_minute,
            max_tokens_per_minute=args.tokens_per_minute,
            # Parallelization parameters
            max_concurrent_tasks=args.max_concurrent_tasks,
            # Hierarchy parameters
            hierarchical=args.hierarchical,
            max_hierarchy_depth=args.max_hierarchy_depth,
            # Domain analysis parameters
            domain_analysis_yaml_path=args.domain_analysis_yaml,
            existing_diagrams_dir=args.existing_diagrams_dir,
            # Caching parameters
            cache_dir=args.cache_dir,
            use_cache=not args.no_cache,
        )

        # Generate diagrams based on the selected mode
        if args.from_existing:
            logger.info("Generating hierarchical diagrams from existing leaf diagrams")
            result = await generator.generate_hierarchical_diagrams_from_existing()
        else:
            logger.info("Generating diagrams from domain file JSON")
            result = await generator.generate_all_diagrams()

        if result.success:
            logger.info("Diagram generation completed successfully")
            logger.info(f"Generated {len(result.diagram_files)} diagrams")
            logger.info(f"Output directory: {args.output_dir}")

            # Print domain statistics
            logger.info(f"Domain statistics:")
            logger.info(f"  Total domains: {result.domain_file_stats.get('total_domains', 0)}")
            logger.info(f"  Total files: {result.domain_file_stats.get('total_files', 0)}")
            logger.info(f"  Total tokens: {result.domain_file_stats.get('total_tokens', 0)}")

            # Print processing statistics
            if hasattr(result, 'processing_stats') and result.processing_stats:
                logger.info(f"Processing statistics:")
                logger.info(f"  Total tasks: {result.processing_stats.get('total_tasks', 0)}")
                logger.info(f"  Completed tasks: {result.processing_stats.get('completed_tasks', 0)}")
                logger.info(f"  Failed tasks: {result.processing_stats.get('failed_tasks', 0)}")
                logger.info(f"  Success rate: {result.processing_stats.get('success_rate', 0):.2f}%")
                logger.info(f"  Average task time: {result.processing_stats.get('average_task_time', 0):.2f}s")
                logger.info(f"  Median task time: {result.processing_stats.get('median_task_time', 0):.2f}s")
                logger.info(f"  Tasks per second: {result.processing_stats.get('tasks_per_second', 0):.2f}")

            # Print hierarchy information if available
            if args.hierarchical and hasattr(result, 'hierarchy_info') and result.hierarchy_info:
                logger.info(f"Hierarchy information:")
                logger.info(f"  Total levels: {result.hierarchy_info.get('total_levels', 0)}")
                logger.info(f"  Domains by level:")
                for level, count in sorted(result.hierarchy_info.get('domains_by_level', {}).items()):
                    logger.info(f"    Level {level}: {count} domains")
                logger.info(f"  Processing time by level:")
                for level, time_taken in sorted(result.hierarchy_info.get('processing_time_by_level', {}).items()):
                    logger.info(f"    Level {level}: {time_taken:.2f} seconds")

                # Print level statistics for each level
                logger.info(f"  Level statistics:")
                for level_key in sorted([k for k in result.hierarchy_info.keys() if k.startswith("level_") and k.endswith("_stats")]):
                    level_num = level_key.split("_")[1]
                    stats = result.hierarchy_info[level_key]
                    logger.info(f"    Level {level_num} statistics:")
                    logger.info(f"      Total tasks: {stats.get('total_tasks', 0)}")
                    logger.info(f"      Successful tasks: {stats.get('successful_tasks', 0)}")
                    logger.info(f"      Failed tasks: {stats.get('failed_tasks', 0)}")
                    logger.info(f"      Success rate: {stats.get('success_rate', 0):.2f}%")
                    logger.info(f"      Tasks per second: {stats.get('tasks_per_second', 0):.2f}")

            # Print OpenRouter usage if available
            if hasattr(result, 'openrouter_info') and result.openrouter_info:
                logger.info(f"OpenRouter usage:")
                logger.info(f"  Domains using OpenRouter: {result.openrouter_info.get('openrouter_domains_count', 0)}")
                logger.info(f"  Total OpenRouter calls: {result.openrouter_info.get('openrouter_calls_total', 0)}")
                logger.info(f"  Completed OpenRouter calls: {result.openrouter_info.get('openrouter_calls_completed', 0)}")

            return 0
        else:
            logger.error(f"Diagram generation failed: {result.error_message}")
            return 1

    except Exception as e:
        logger.error(f"Error in diagram generation: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    asyncio.run(main())
