"""
Domain Trace Builder for Codebase

This module provides functionality to:
1. Read domain output YAML and build traces from top to bottom (domain -> sub-area -> sub-area...)
2. Read semantic_documented_functions.parquet to get function signatures and descriptions
3. Use an LLM to classify functions into domain traces

It can be used as:
1. A standalone script to process domain YAML and function parquet files
2. A module that can be integrated into the repository analysis flow
"""

import os
import yaml
import json
import logging
import argparse
import asyncio
import aiohttp
import time
import pandas as pd
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field

# Import API key management
from bracket_core.llm.api_keys import get_openai_api_key

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class DomainTrace:
    """Represents a complete trace from top-level domain to leaf sub-area."""
    trace: List[str]
    trace_str: str  # String representation for easy comparison

    @classmethod
    def from_path(cls, path: List[str]):
        """Create a DomainTrace from a path list."""
        return cls(
            trace=path,
            trace_str=" -> ".join(path)
        )

@dataclass
class FunctionClassificationResult:
    """Result of classifying functions into domain traces."""
    success: bool = True
    error_message: str = ""
    classifications: Dict[str, List[str]] = field(default_factory=dict)
    raw_responses: Dict[str, str] = field(default_factory=dict)

@dataclass
class StatusTracker:
    """Stores metadata about the script's progress. Only one instance is created."""
    num_tasks_started: int = 0
    num_tasks_in_progress: int = 0  # script ends when this reaches 0
    num_tasks_succeeded: int = 0
    num_tasks_failed: int = 0
    num_rate_limit_errors: int = 0
    num_api_errors: int = 0  # excluding rate limit errors, counted above
    num_other_errors: int = 0
    time_of_last_rate_limit_error: float = 0  # used to cool off after hitting rate limits

@dataclass
class APIRequest:
    """Stores an API request's inputs, outputs, and other metadata. Contains a method to make an API call."""
    task_id: int
    trace: DomainTrace
    request_json: dict
    attempts_left: int
    result: List[str] = field(default_factory=list)
    error: Any = None

    async def call_api(
        self,
        session: aiohttp.ClientSession,
        request_url: str,
        request_header: dict,
        retry_queue: asyncio.Queue,
        status_tracker: StatusTracker,
        results: Dict[str, List[str]],
        raw_responses: Dict[str, str],
    ):
        """Calls the OpenAI API and processes the response."""
        logging.info(f"Starting request #{self.task_id} for trace: {self.trace.trace_str}")

        try:
            async with session.post(
                url=request_url,
                headers=request_header,
                json=self.request_json
            ) as response:
                response_data = await response.json()

                if response.status != 200 or "error" in response_data:
                    error_text = response_data.get("error", {}).get("message", str(response_data))
                    logging.warning(f"Request {self.task_id} failed with error: {error_text}")

                    if "error" in response_data and "rate limit" in response_data["error"].get("message", "").lower():
                        status_tracker.time_of_last_rate_limit_error = time.time()
                        status_tracker.num_rate_limit_errors += 1
                    else:
                        status_tracker.num_api_errors += 1

                    self.error = error_text

                    if self.attempts_left > 0:
                        retry_queue.put_nowait(self)
                    else:
                        logging.error(f"Request {self.task_id} failed after all attempts. Error: {error_text}")
                        status_tracker.num_tasks_failed += 1
                        status_tracker.num_tasks_in_progress -= 1
                else:
                    # Extract the response content
                    content = response_data["choices"][0]["message"]["content"]
                    raw_responses[self.trace.trace_str] = content

                    # Parse the response to get the list of functions
                    functions = []
                    if content and content.strip() != "No functions found":
                        functions = [line.strip() for line in content.strip().split('\n') if line.strip()]

                    results[self.trace.trace_str] = functions

                    logging.info(f"Found {len(functions)} functions for trace: {self.trace.trace_str}")
                    status_tracker.num_tasks_succeeded += 1
                    status_tracker.num_tasks_in_progress -= 1

        except Exception as e:
            logging.warning(f"Request {self.task_id} failed with Exception {e}")
            status_tracker.num_other_errors += 1
            self.error = str(e)

            if self.attempts_left > 0:
                retry_queue.put_nowait(self)
            else:
                logging.error(f"Request {self.task_id} failed after all attempts. Error: {e}")
                status_tracker.num_tasks_failed += 1
                status_tracker.num_tasks_in_progress -= 1

class DomainTraceBuilder:
    """
    Builds domain traces from a domain YAML file and classifies functions into these traces.
    """

    def __init__(
        self,
        domain_analysis_yaml_path: str,
        functions_parquet_path: str,
        output_path: str,
        api_key: Optional[str] = None,
        model: str = "gpt-4o-mini",
        max_requests_per_minute: float = 2000,
        max_tokens_per_minute: float = 1000000,
        temperature: float = 0.0,
    ):
        """
        Initialize the domain trace builder.

        Args:
            domain_analysis_yaml_path: Path to the domain YAML file
            functions_parquet_path: Path to the semantic_documented_functions.parquet file
            output_path: Path to save the classification output
            api_key: OpenAI API key (if None, will try to get from environment)
            model: OpenAI model to use
            max_requests_per_minute: Rate limit for API requests
            max_tokens_per_minute: Token rate limit for API
            temperature: Temperature for LLM generation
        """
        self.domain_analysis_yaml_path = domain_analysis_yaml_path
        self.functions_parquet_path = functions_parquet_path
        self.output_path = output_path
        # Get API key from centralized management
        self.api_key = get_openai_api_key(provided_key=api_key)
        self.model = model
        self.max_requests_per_minute = max_requests_per_minute
        self.max_tokens_per_minute = max_tokens_per_minute
        self.temperature = temperature

    def build_domain_traces(self, domain_data: Dict[str, Any]) -> List[DomainTrace]:
        """
        Build domain traces from the domain data.

        Args:
            domain_data: Dictionary containing domain data

        Returns:
            List of DomainTrace objects
        """
        traces = []

        def traverse_areas(areas, current_path):
            for area in areas:
                area_name = area.get('name', '')
                if not area_name:
                    continue

                new_path = current_path + [area_name]

                # If this area has no subareas, it's a leaf node
                subareas = area.get('subareas', [])
                if not subareas:
                    traces.append(DomainTrace.from_path(new_path))
                else:
                    traverse_areas(subareas, new_path)

        # Start traversal from the top-level areas
        areas = domain_data.get('areas', [])
        traverse_areas(areas, [])

        return traces

    def read_domain_analysis_yaml(self) -> Dict[str, Any]:
        """
        Read the domain YAML file.

        Returns:
            Dictionary containing domain data
        """
        logger.info(f"Reading domain YAML file: {self.domain_analysis_yaml_path}")
        try:
            with open(self.domain_analysis_yaml_path, 'r') as f:
                domain_data = yaml.safe_load(f)
            return domain_data
        except Exception as e:
            logger.error(f"Error reading domain YAML file: {e}")
            raise

    def read_functions_parquet(self) -> pd.DataFrame:
        """
        Read the functions parquet file.

        Returns:
            DataFrame containing function data
        """
        logger.info(f"Reading functions parquet file: {self.functions_parquet_path}")
        try:
            df = pd.read_parquet(self.functions_parquet_path)

            # Filter for function nodes if 'type' column exists
            if 'type' in df.columns:
                df = df[df['type'] == 'FUNCTION']

            return df
        except Exception as e:
            logger.error(f"Error reading functions parquet file: {e}")
            raise

    def prepare_function_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Prepare function data for LLM classification.

        Args:
            df: DataFrame containing function data

        Returns:
            Dictionary containing prepared function data
        """
        logger.info("Preparing function data for classification")

        function_data = {}

        # Check for required columns
        required_columns = ['node_id', 'description']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"Missing required columns in parquet file: {missing_columns}")

        # Extract function signatures and descriptions
        for _, row in df.iterrows():
            node_id = row['node_id']
            description = row.get('description', '')

            # Skip functions without descriptions
            if not description or description.strip() == '':
                continue

            # Extract signature if available
            signature = ''
            if 'signature' in row and isinstance(row['signature'], dict):
                sig_dict = row['signature']
                name = sig_dict.get('name', '')
                params = sig_dict.get('params', [])
                return_type = sig_dict.get('return_type', '')

                param_str = ', '.join([f"{p.get('name', '')}: {p.get('type', '')}" for p in params])
                signature = f"{name}({param_str})"
                if return_type:
                    signature += f" -> {return_type}"

            # Use node_id as fallback if no signature
            if not signature:
                signature = node_id

            function_data[signature] = description

        return function_data

    async def classify_functions(self, traces: List[DomainTrace], function_data: Dict[str, Any]) -> FunctionClassificationResult:
        """
        Classify functions into domain traces using LLM in parallel without waiting for responses.

        Args:
            traces: List of DomainTrace objects
            function_data: Dictionary mapping function signatures to descriptions

        Returns:
            FunctionClassificationResult containing the classification results
        """
        logger.info("Classifying functions into domain traces in parallel without waiting for responses")

        # Constants for rate limiting
        seconds_to_pause_after_rate_limit_error = 15
        seconds_to_sleep_each_loop = 0.001  # 1 ms limits max throughput to 1,000 requests per second

        # Prepare function signatures and descriptions for LLM
        full_signature_set = "\n".join([f"{sig} - {desc}" for sig, desc in function_data.items()])

        # Initialize result
        result = FunctionClassificationResult()

        # Initialize trackers
        queue_of_requests_to_retry = asyncio.Queue()
        status_tracker = StatusTracker()  # single instance to track a collection of variables

        # Initialize available capacity counts
        available_request_capacity = self.max_requests_per_minute
        available_token_capacity = self.max_tokens_per_minute
        last_update_time = time.time()

        # Create request URL and header
        request_url = "https://api.openai.com/v1/chat/completions"
        request_header = {"Authorization": f"Bearer {self.api_key}", "Content-Type": "application/json"}

        # Create a list of API requests
        api_requests = []
        for i, trace in enumerate(traces):
            # Create the API request
            request_json = {
                "model": self.model,
                "messages": [
                    {
                        "role": "system",
                        "content": """You are an expert software architect analyzing a codebase.
Your task is to identify which functions belong to a specific domain trace.
Analyze each function signature and description carefully, and determine if it belongs to the given domain trace.
Return ONLY a list of function signatures that belong to the domain trace, one per line.
Do not include any explanations, headers, or other text in your response.
If no functions belong to the domain trace, return "No functions found".
"""
                    },
                    {
                        "role": "user",
                        "content": f"""Domain trace: {trace.trace_str}

Here is the list of all function signatures and their descriptions:

{full_signature_set}

Please list ONLY the function signatures (without descriptions) that belong to the domain trace "{trace.trace_str}".
Return one function signature per line, with no additional text.
"""
                    }
                ],
                "temperature": self.temperature,
                "max_tokens": 4000
            }

            # Create an API request object
            api_request = APIRequest(
                task_id=i,
                trace=trace,
                request_json=request_json,
                attempts_left=3,  # Number of retry attempts
            )

            api_requests.append(api_request)
            status_tracker.num_tasks_started += 1
            status_tracker.num_tasks_in_progress += 1

        # Process API requests in parallel
        next_request_index = 0
        next_request = None

        async with aiohttp.ClientSession() as session:
            while status_tracker.num_tasks_in_progress > 0:
                # Get next request (if one is not already waiting for capacity)
                if next_request is None:
                    if not queue_of_requests_to_retry.empty():
                        next_request = await queue_of_requests_to_retry.get()
                        next_request.attempts_left -= 1
                        logger.debug(f"Retrying request {next_request.task_id}: {next_request.trace.trace_str}")
                    elif next_request_index < len(api_requests):
                        next_request = api_requests[next_request_index]
                        next_request_index += 1
                        logger.debug(f"Processing request {next_request.task_id}: {next_request.trace.trace_str}")

                # Update available capacity
                current_time = time.time()
                seconds_since_update = current_time - last_update_time
                available_request_capacity = min(
                    available_request_capacity + self.max_requests_per_minute * seconds_since_update / 60.0,
                    self.max_requests_per_minute
                )
                available_token_capacity = min(
                    available_token_capacity + self.max_tokens_per_minute * seconds_since_update / 60.0,
                    self.max_tokens_per_minute
                )
                last_update_time = current_time

                # If enough capacity available and we have a request, call API
                if next_request and available_request_capacity >= 1:
                    # Estimate token consumption (simplified)
                    estimated_tokens = 1000  # Simplified estimate

                    if available_token_capacity >= estimated_tokens:
                        # Update counters
                        available_request_capacity -= 1
                        available_token_capacity -= estimated_tokens

                        # Call API without waiting for response
                        asyncio.create_task(
                            next_request.call_api(
                                session=session,
                                request_url=request_url,
                                request_header=request_header,
                                retry_queue=queue_of_requests_to_retry,
                                status_tracker=status_tracker,
                                results=result.classifications,
                                raw_responses=result.raw_responses,
                            )
                        )
                        next_request = None  # Reset next_request to get a new one

                # Sleep briefly so concurrent tasks can run
                await asyncio.sleep(seconds_to_sleep_each_loop)

                # If a rate limit error was hit recently, pause to cool down
                seconds_since_rate_limit_error = time.time() - status_tracker.time_of_last_rate_limit_error
                if seconds_since_rate_limit_error < seconds_to_pause_after_rate_limit_error:
                    remaining_seconds_to_pause = seconds_to_pause_after_rate_limit_error - seconds_since_rate_limit_error
                    logger.warning(f"Pausing to cool down for {remaining_seconds_to_pause:.1f} seconds due to rate limit")
                    await asyncio.sleep(remaining_seconds_to_pause)

        # After finishing, log final status
        logger.info(f"Parallel processing complete. Processed {len(traces)} domain traces.")
        if status_tracker.num_tasks_failed > 0:
            logger.warning(f"{status_tracker.num_tasks_failed} / {status_tracker.num_tasks_started} requests failed.")
            result.success = False
            result.error_message = f"{status_tracker.num_tasks_failed} requests failed."

        if status_tracker.num_rate_limit_errors > 0:
            logger.warning(f"{status_tracker.num_rate_limit_errors} rate limit errors received. Consider running at a lower rate.")

        return result

    def save_classification_results(self, result: FunctionClassificationResult) -> None:
        """
        Save the classification results to a YAML file.

        Args:
            result: FunctionClassificationResult containing the classification results
        """
        logger.info(f"Saving classification results to: {self.output_path}")

        # Prepare the output data
        output_data = {
            "domain_traces": {}
        }

        for trace_str, functions in result.classifications.items():
            output_data["domain_traces"][trace_str] = functions

        # Save the results
        try:
            with open(self.output_path, 'w') as f:
                yaml.dump(output_data, f, default_flow_style=False)

            # Also save the raw responses for reference
            with open(f"{self.output_path}.raw", 'w') as f:
                json.dump(result.raw_responses, f, indent=2)

            logger.info(f"Classification results saved to: {self.output_path}")
            logger.info(f"Raw responses saved to: {self.output_path}.raw")

        except Exception as e:
            logger.error(f"Error saving classification results: {e}")
            raise

    async def build_and_classify(self) -> FunctionClassificationResult:
        """
        Build domain traces and classify functions.

        Returns:
            FunctionClassificationResult containing the classification results
        """
        # Read the domain YAML
        domain_data = self.read_domain_analysis_yaml()

        # Build domain traces
        traces = self.build_domain_traces(domain_data)
        logger.info(f"Built {len(traces)} domain traces")

        # Read the functions parquet
        df = self.read_functions_parquet()

        # Prepare function data
        function_data = self.prepare_function_data(df)
        logger.info(f"Prepared {len(function_data)} functions for classification")

        # Classify functions
        result = await self.classify_functions(traces, function_data)

        # Save the results if successful
        if result.success:
            self.save_classification_results(result)

        return result

async def main():
    """Main entry point for the domain trace builder."""
    parser = argparse.ArgumentParser(description="Build domain traces and classify functions")
    parser.add_argument("--domain-yaml", required=True, help="Path to the domain YAML file")
    parser.add_argument("--functions-parquet", required=True, help="Path to the semantic_documented_functions.parquet file")
    parser.add_argument("--output", required=True, help="Path to save the classification output")
    parser.add_argument("--api-key", help="OpenAI API key (if not provided, will try to get from environment)")
    parser.add_argument("--model", default="gpt-4o-mini", help="OpenAI model to use")
    parser.add_argument("--requests-per-minute", type=float, default=500, help="Rate limit for API requests")
    parser.add_argument("--tokens-per-minute", type=float, default=100000, help="Token rate limit for API")
    parser.add_argument("--temperature", type=float, default=0.0, help="Temperature for LLM generation")

    args = parser.parse_args()

    try:
        builder = DomainTraceBuilder(
            domain_analysis_yaml_path=args.domain_analysis_yaml,
            functions_parquet_path=args.functions_parquet,
            output_path=args.output,
            api_key=args.api_key,
            model=args.model,
            max_requests_per_minute=args.requests_per_minute,
            max_tokens_per_minute=args.tokens_per_minute,
            temperature=args.temperature,
        )

        result = await builder.build_and_classify()

        if result.success:
            logger.info("Domain trace building and function classification completed successfully")
            return 0
        else:
            logger.error(f"Domain trace building and function classification failed: {result.error_message}")
            return 1

    except Exception as e:
        logger.error(f"Error in domain trace building and function classification: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
