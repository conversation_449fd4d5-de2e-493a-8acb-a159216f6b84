"""
Codebase Explainer

This module provides functionality to generate a comprehensive explanation of a codebase
based on the domain taxonomy JSON file that contains hierarchical structure and mermaid diagrams.

It can be used as:
1. A standalone script to generate a codebase explanation
2. A module that can be integrated into the repository analysis flow
"""

import os
import json
import logging
import asyncio
from typing import Dict, List, Any, Optional, Callable, AsyncGenerator
from dataclasses import dataclass, field

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import API key management and try to import OpenRouter client
from bracket_core.llm.api_keys import get_openai_api_key

# Try to import OpenRouter-related modules, but don't fail if they're not available
try:
    from bracket_core.llm.api_keys import get_openrouter_api_key
    from bracket_core.llm.get_client import get_openrouter_client
    OPENROUTER_AVAILABLE = True
except ImportError:
    logger.warning("OpenRouter modules not available. Falling back to OpenAI.")
    OPENROUTER_AVAILABLE = False

@dataclass
class CodebaseExplanationResult:
    """Result of generating a codebase explanation."""
    success: bool = True
    error_message: str = ""
    explanation: str = ""
    output_path: Optional[str] = None
    suggested_questions: Dict[str, List[str]] = field(default_factory=dict)
    stream: Optional[AsyncGenerator[str, None]] = None

class CodebaseExplainer:
    """
    Generates a comprehensive explanation of a codebase based on domain taxonomy.

    This class reads a domain taxonomy JSON file, extracts the hierarchical structure
    and mermaid diagrams, and uses GPT-4o-mini to generate a comprehensive explanation.
    """

    def __init__(
        self,
        taxonomy_json_path: str,
        global_code_explain_md_path: str,
        suggested_questions_md_path: str,
        openai_api_key: Optional[str] = None,
        openai_model: str = "gpt-4o-mini",
        max_tokens: int = 4000,
        temperature: float = 0.7,
        use_streaming: bool = False,
        stream_callback: Optional[Callable[[str], None]] = None,
        use_openrouter: bool = True,
        # do_generate_questions: bool = True,
    ):
        """
        Initialize the codebase explainer.

        Args:
            taxonomy_json_path: Path to the domain taxonomy JSON file
            global_code_explain_md_path: Path to save the output markdown file
            openai_api_key: OpenAI API key (if None, will try to get from environment)
            openai_model: OpenAI model to use
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
        """
        self.taxonomy_json_path = taxonomy_json_path
        self.global_code_explain_md_path = global_code_explain_md_path
        self.suggested_questions_md_path = suggested_questions_md_path
        # Set streaming options
        self.use_streaming = use_streaming
        self.stream_callback = stream_callback

        # Check if OpenRouter is available and requested
        self.use_openrouter = use_openrouter and OPENROUTER_AVAILABLE

        # Get API key from centralized management
        if self.use_openrouter:
            try:
                self.api_key = get_openrouter_api_key(provided_key=openai_api_key)
                # For codebase explanation, use Gemini model with large context window
                self.openrouter_model = "google/gemini-2.5-pro-preview"
                self.openrouter_client = get_openrouter_client(
                    # API key will be handled by the client
                    model=self.openrouter_model,
                    max_tokens=60096,  # Use larger context window
                    temperature=temperature,
                )
                logger.info(f"Using OpenRouter with model: {self.openrouter_model}")
            except Exception as e:
                logger.warning(f"Failed to initialize OpenRouter: {e}. Falling back to OpenAI.")
                self.use_openrouter = False
                self.api_key = get_openai_api_key(provided_key=openai_api_key)
                self.openrouter_client = None
        else:
            self.api_key = get_openai_api_key(provided_key=openai_api_key)
            self.openrouter_client = None

        self.openai_model = openai_model
        self.max_tokens = max_tokens
        self.temperature = temperature
        # self.do_generate_questions = do_generate_questions

        # Initialize data structures
        self.taxonomy_data = None
        self.codebase_overview_diagram = None
        self.top_level_domains = []
        self.domain_diagrams = {}

    def read_taxonomy_json(self) -> Dict[str, Any]:
        """
        Read the domain taxonomy JSON file.

        Returns:
            Dictionary containing the domain taxonomy data
        """
        logger.info(f"Reading domain taxonomy JSON from {self.taxonomy_json_path}")
        try:
            with open(self.taxonomy_json_path, 'r') as f:
                data = json.load(f)
            logger.info("Successfully read domain taxonomy JSON")
            return data
        except Exception as e:
            logger.error(f"Error reading domain taxonomy JSON: {e}")
            raise

    def extract_codebase_overview(self, data: Dict[str, Any]) -> Optional[str]:
        """
        Extract the codebase overview diagram from the taxonomy data.

        Args:
            data: Dictionary containing the domain taxonomy data

        Returns:
            Codebase overview diagram as a string, or None if not found
        """
        logger.info("Extracting codebase overview diagram")
        if "codebase_overview" in data and "diagram" in data["codebase_overview"]:
            diagram = data["codebase_overview"]["diagram"]
            logger.info("Successfully extracted codebase overview diagram")
            return diagram
        else:
            logger.warning("No codebase overview diagram found in taxonomy data")
            return None

    def extract_top_level_domains(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extract the top-level domains from the taxonomy data.

        Args:
            data: Dictionary containing the domain taxonomy data

        Returns:
            List of top-level domains
        """
        logger.info("Extracting top-level domains")
        if "children" in data:
            domains = data["children"]
            logger.info(f"Found {len(domains)} top-level domains")
            return domains
        else:
            logger.warning("No top-level domains found in taxonomy data")
            return []

    def extract_domain_diagrams(self, domains: List[Dict[str, Any]], prefix: str = "") -> Dict[str, str]:
        """
        Extract diagrams for all domains recursively.

        Args:
            domains: List of domain dictionaries
            prefix: Prefix for the domain path (for recursive calls)

        Returns:
            Dictionary mapping domain paths to diagrams
        """
        diagrams = {}

        for domain in domains:
            domain_name = domain.get("name", "")
            domain_path = domain.get("full_path", prefix + domain_name if prefix else domain_name)

            # Extract diagram for this domain
            if "diagram" in domain:
                diagrams[domain_path] = domain["diagram"]

            # Extract combined diagram if available
            if "combined_diagram" in domain:
                diagrams[f"{domain_path} (Combined)"] = domain["combined_diagram"]

            # Recursively extract diagrams for children
            if "children" in domain and domain["children"]:
                child_diagrams = self.extract_domain_diagrams(domain["children"], prefix=domain_path + " -> " if domain_path else "")
                diagrams.update(child_diagrams)

        return diagrams

    def prepare_taxonomy_data(self) -> None:
        """
        Prepare the taxonomy data for explanation generation.

        This method reads the taxonomy JSON, extracts the codebase overview diagram,
        top-level domains, and domain diagrams.
        """
        logger.info("Preparing taxonomy data for explanation generation")

        # Read the taxonomy JSON
        self.taxonomy_data = self.read_taxonomy_json()

        # Extract the codebase overview diagram
        self.codebase_overview_diagram = self.extract_codebase_overview(self.taxonomy_data)

        # Extract the top-level domains
        self.top_level_domains = self.extract_top_level_domains(self.taxonomy_data)

        # Extract domain diagrams
        self.domain_diagrams = self.extract_domain_diagrams(self.top_level_domains)

        logger.info(f"Prepared taxonomy data with {len(self.domain_diagrams)} domain diagrams")

    def create_explanation_prompt(self) -> tuple[str, str]:
        """
        Create a prompt for generating a codebase explanation.

        Returns:
            Tuple of (system_prompt, user_prompt) for the OpenAI API
        """
        logger.info("Creating explanation prompt")

        # Start with the system prompt
        system_prompt = """
You are an expert software architect tasked with explaining a complex codebase to a new developer.
Your goal is to create a beautifully formatted, information-rich explanation of the entire codebase.

The explanation should:
1. Begin with a concise (300-400 tokens) holistic overview that captures the essence of the entire codebase
2. Follow with more detailed sections about each major component
3. Provide a clear "big picture" overview of the system architecture
4. Explain the main domains and their relationships with emphasis on modularity
5. Highlight key design patterns and architectural decisions
6. Showcase connections between components with clear explanations of their interactions
7. Be organized in a logical, hierarchical structure with clear section headings
8. Use concise, technical language appropriate for experienced developers
9. Focus on the most important aspects of the codebase
10. Use formatting (bold, italics, lists, etc.) to enhance readability and highlight key points

Please note that "Domains" are emergent abstractions on our end and might not be there in the codebase, so don't go too heavy on them.

Format your response as a well-structured markdown with:
- Clear hierarchical headings (##, ###, etc.)
- Bulleted or numbered lists for related items
- Bold for important concepts and components
- Italics for emphasis
- Horizontal rules to separate major sections
- Tables where appropriate to show relationships
- Consistent indentation and spacing for readability

DO NOT include code snippets or implementation details. Focus on the high-level architecture and design.
"""

        # Add the codebase overview diagram if available
        user_prompt = "# Codebase Structure\n\n"

        if self.codebase_overview_diagram:
            user_prompt += "## Codebase Overview Diagram\n\n"
            user_prompt += self.codebase_overview_diagram + "\n\n"

        # Add information about top-level domains
        user_prompt += "## Top-Level Domains\n\n"
        for domain in self.top_level_domains:
            domain_name = domain.get("name", "")
            user_prompt += f"- {domain_name}\n"

        user_prompt += "\n## Domain Diagrams\n\n"

        # Add a selection of the most important domain diagrams
        # Prioritize combined diagrams and top-level domains
        important_diagrams = {}

        # First add combined diagrams for top-level domains
        for domain in self.top_level_domains:
            domain_name = domain.get("name", "")
            domain_path = domain.get("full_path", domain_name)
            combined_key = f"{domain_path} (Combined)"

            if combined_key in self.domain_diagrams:
                important_diagrams[f"Combined {domain_name}"] = self.domain_diagrams[combined_key]

        # Then add regular diagrams for top-level domains
        for domain in self.top_level_domains:
            domain_name = domain.get("name", "")
            domain_path = domain.get("full_path", domain_name)

            if domain_path in self.domain_diagrams and domain_name not in important_diagrams:
                important_diagrams[domain_name] = self.domain_diagrams[domain_path]

        # Add the important diagrams to the prompt
        for domain_name, diagram in important_diagrams.items():
            user_prompt += f"### {domain_name}\n\n"
            user_prompt += diagram + "\n\n"

        # Add instructions for the explanation
        user_prompt += """
# Task
Based on the domain taxonomy and diagrams provided above, generate a comprehensive explanation of this codebase.

Your explanation should:
1. Start with a concise (200-400 tokens) bulleted, well marked and structured view of the entire codebase.
2. Follow with slightly detailed sections about each major component
3. Explain each major domain and its purpose with clear headings
4. Describe how the domains interact with each other, emphasizing connections and data flow
5. Highlight key architectural patterns and design decisions
6. Be organized in a clear, hierarchical structure with consistent formatting
7. Use formatting (bold, italics, lists, etc.) to enhance readability
8. Include a visual hierarchy through proper use of headings and spacing

Focus on creating a "big picture" understanding that would help a new developer quickly grasp the overall architecture and organization of the codebase. Make the output beautifully formatted and information-rich, showcasing your deep understanding of software architecture.
"""

        logger.info("Successfully created explanation prompt")
        return system_prompt, user_prompt

    def create_questions_prompt(self) -> tuple[str, str]:
        """
        Create a prompt for generating suggested questions about the codebase.

        Returns:
            Tuple of (system_prompt, user_prompt) for the OpenAI API
        """
        logger.info("Creating questions prompt")

        # Start with the system prompt
        system_prompt = """
You are an expert software architect tasked with creating insightful questions about a complex codebase.
Your goal is to generate 2-3 deep, architecture-level questions for each main domain or category in the codebase.

The questions should be organized by these categories:
- Architecture
- Monitoring
- Infrastructure
- Security
- Performance
- Data Flow
- Integration
- Testing
- Deployment

For each category that's relevant to this codebase, generate 2-3 questions. If a category isn't relevant, skip it.

IMPORTANT GUIDELINES:
1. DO NOT use domain-specific abbreviations or shorthand (like AA, AR, ADM, DP, PR, etc.) in your questions
2. DO NOT refer to internal domain terminology that wouldn't be familiar to someone new to the codebase
3. Use full, descriptive names for components and systems instead of abbreviations
4. Focus on concrete technical concepts rather than abstract domain concepts
5. Include both high-level architectural questions and low-level implementation/debugging questions

The questions should:
1. Cover different aspects of the codebase architecture
2. Range from high-level architectural questions to specific implementation patterns
3. Focus on relationships between components and design decisions
4. Be specific to this particular codebase (not generic software questions)
5. Encourage exploration of the codebase's most important or complex parts
6. Be phrased clearly and concisely using standard industry terminology
7. Refer to actual files, functions, and components by their full names when appropriate
8. Be complex but concise questions that will introduce a Staff Engineer to a new codebase
9. Be 15-20 tokens, short, intense, concise and deeply about the codebase

Format your response as a JSON object where keys are categories and values are arrays of questions.
Example: {
  "Architecture": ["How does the authentication system interact with the database layer?", "What patterns are used in the core module?"],
  "Security": ["How are user permissions enforced across microservices?", "What encryption methods protect sensitive data?"]
}
"""

        # Create the user prompt with the same codebase information as for the explanation
        user_prompt = "# Codebase Structure\n\n"

        if self.codebase_overview_diagram:
            user_prompt += "## Codebase Overview Diagram\n\n"
            user_prompt += self.codebase_overview_diagram + "\n\n"

        # Add information about top-level domains
        user_prompt += "## Top-Level Domains\n\n"
        for domain in self.top_level_domains:
            domain_name = domain.get("name", "")
            user_prompt += f"- {domain_name}\n"

        user_prompt += "\n## Domain Diagrams\n\n"

        # Add a selection of the most important domain diagrams
        # Prioritize combined diagrams and top-level domains
        important_diagrams = {}

        # First add combined diagrams for top-level domains
        for domain in self.top_level_domains:
            domain_name = domain.get("name", "")
            domain_path = domain.get("full_path", domain_name)
            combined_key = f"{domain_path} (Combined)"

            if combined_key in self.domain_diagrams:
                important_diagrams[f"Combined {domain_name}"] = self.domain_diagrams[combined_key]

        # Then add regular diagrams for top-level domains
        for domain in self.top_level_domains:
            domain_name = domain.get("name", "")
            domain_path = domain.get("full_path", domain_name)

            if domain_path in self.domain_diagrams and domain_name not in important_diagrams:
                important_diagrams[domain_name] = self.domain_diagrams[domain_path]

        # Add the important diagrams to the prompt
        for domain_name, diagram in important_diagrams.items():
            user_prompt += f"### {domain_name}\n\n"
            user_prompt += diagram + "\n\n"

        # Add instructions for generating questions
        user_prompt += """
# Task
Based on the domain taxonomy and diagrams provided above, generate 2-3 insightful, architecture-level questions for each relevant category (Architecture, Monitoring, Infrastructure, Security, etc.).

IMPORTANT GUIDELINES:
1. DO NOT use domain-specific abbreviations or shorthand (like AA, AR, ADM, DP, PR, etc.) in your questions
2. DO NOT refer to internal domain terminology that wouldn't be familiar to someone new to the codebase
3. Use full, descriptive names for components and systems instead of abbreviations
4. Focus on concrete technical concepts rather than abstract domain concepts
5. Include both high-level architectural questions and low-level implementation/debugging questions

Your questions should:
1. Cover different aspects of the codebase architecture
2. Range from high-level architectural questions to specific implementation patterns
3. Focus on relationships between components and design decisions
4. Be specific to this particular codebase (not generic software questions)
5. Encourage exploration of the codebase's most important or complex parts
6. Be under 20 tokens, short, intense, concise and deeply about the codebase
7. Be organized by category based on the main domains and aspects of the system
8. Use standard industry terminology that would be familiar to any experienced developer

Format your response as a JSON object where keys are categories and values are arrays of questions.
Example: {
  "Architecture": ["How does the authentication system interact with the database layer?", "What patterns are used in the core module?"],
  "Security": ["How are user permissions enforced across microservices?", "What encryption methods protect sensitive data?"]
}
"""

        logger.info("Successfully created questions prompt")
        return system_prompt, user_prompt

    async def generate_questions(self) -> Dict[str, List[str]]:
        """
        Generate suggested questions about the codebase using OpenAI API.

        Returns:
            Dictionary mapping domain categories to lists of questions
        """
        try:
            # Create the questions prompt
            system_prompt, user_prompt = self.create_questions_prompt()

            # Call the OpenAI API
            logger.info(f"Calling OpenAI API with model {self.openai_model} to generate questions")

            # Import here to avoid circular imports
            from bracket_core.llm.oai.chat_openai import ChatOpenAI

            # Create the OpenAI client
            client = ChatOpenAI(
                api_key=self.api_key,
                model=self.openai_model,
                max_retries=3
            )

            # Generate the questions
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            response = await client.agenerate(
                messages=messages,
                # max_tokens=1000,
                # temperature=0.7
            )

            # Parse the response as JSON
            try:
                # The response might be wrapped in ```json and ``` markers
                clean_response = response.strip()
                if clean_response.startswith('```json'):
                    clean_response = clean_response[7:]
                if clean_response.endswith('```'):
                    clean_response = clean_response[:-3]

                questions_by_domain = json.loads(clean_response)
                if isinstance(questions_by_domain, dict):
                    total_questions = sum(len(qs) for qs in questions_by_domain.values())
                    logger.info(f"Successfully generated {total_questions} suggested questions across {len(questions_by_domain)} domains")
                    return questions_by_domain
                elif isinstance(questions_by_domain, list):
                    # Handle case where the model returns a list instead of a dict
                    logger.warning("Response was a list of questions instead of a domain-categorized dict. Converting to categorized format.")
                    return {"General": questions_by_domain}
                else:
                    logger.warning("Response was not a dict or list of questions")
                    return {}
            except json.JSONDecodeError as e:
                logger.error(f"Error parsing questions response as JSON: {e}")
                # Try to extract questions using regex as a fallback
                import re
                questions = re.findall(r'"([^"]+)"', response)
                if questions:
                    logger.info(f"Extracted {len(questions)} questions using regex")
                    return {"General": questions}
                return {}

        except Exception as e:
            logger.error(f"Error generating suggested questions: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return {}

    async def generate_explanation(self) -> CodebaseExplanationResult:
        """
        Generate a codebase explanation using OpenAI API.

        Returns:
            CodebaseExplanationResult containing the explanation
        """
        result = CodebaseExplanationResult()

        try:
            # Prepare the taxonomy data
            self.prepare_taxonomy_data()

            # Create the explanation prompt
            system_prompt, user_prompt = self.create_explanation_prompt()

            # Call the OpenAI API
            logger.info(f"Calling OpenAI API with model {self.openai_model}")

            # Generate the explanation
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            # Use OpenRouter client if enabled
            if self.use_openrouter and self.openrouter_client:
                logger.info(f"Using OpenRouter for explanation generation with model {self.openrouter_model}")

                if self.use_streaming:
                    # Stream the response
                    logger.info("Streaming explanation generation")
                    explanation = ""
                    async for chunk in self.openrouter_client.astream_generate(
                        prompt=user_prompt,
                        system_prompt=system_prompt,
                        temperature=self.temperature
                    ):
                        explanation += chunk
                        if self.stream_callback:
                            # Check if the callback is a coroutine function
                            if asyncio.iscoroutinefunction(self.stream_callback):
                                await self.stream_callback(chunk)
                            else:
                                self.stream_callback(chunk)
                else:
                    # Generate without streaming
                    explanation = await self.openrouter_client.generate(
                        prompt=user_prompt,
                        system_prompt=system_prompt,
                        temperature=self.temperature
                    )
            else:
                # Use OpenAI client
                logger.info(f"Using OpenAI for explanation generation with model {self.openai_model}")
                # Import here to avoid circular imports
                from bracket_core.llm.oai.chat_openai import ChatOpenAI

                # Create the OpenAI client
                client = ChatOpenAI(
                    api_key=self.api_key,
                    model=self.openai_model,
                    max_retries=3
                )

                if self.use_streaming:
                    # Stream the response
                    logger.info("Streaming explanation generation")
                    explanation = ""
                    async for chunk in client.astream_generate(messages=messages):
                        explanation += chunk
                        if self.stream_callback:
                            # Check if the callback is a coroutine function
                            if asyncio.iscoroutinefunction(self.stream_callback):
                                await self.stream_callback(chunk)
                            else:
                                self.stream_callback(chunk)
                else:
                    # Generate without streaming
                    explanation = await client.agenerate(
                        messages=messages,
                    )

            # Comment out suggested questions generation
            # suggested_questions = await self.generate_questions()

            # Use empty dict for suggested questions
            suggested_questions = {}

            # Save the explanation to a markdown file
            with open(self.global_code_explain_md_path, 'w') as f:
                f.write(explanation)

            # Comment out saving suggested questions
            # Format the questions by domain for better readability
            # formatted_questions = "# Suggested Questions by Domain\n\n"
            # for domain, questions in suggested_questions.items():
            #     formatted_questions += f"## {domain}\n\n"
            #     for i, question in enumerate(questions, 1):
            #         formatted_questions += f"{i}. {question}\n"
            #     formatted_questions += "\n"

            # # Save the formatted questions
            # with open(self.suggested_questions_md_path, 'w') as f:
            #     f.write(formatted_questions)

            # # Also save the raw JSON for programmatic access
            # questions_json_path = self.suggested_questions_md_path.replace(".md", ".json")
            # with open(questions_json_path, 'w') as f:
            #     json.dump(suggested_questions, f, indent=2)

            logger.info(f"Successfully generated codebase explanation and saved to {self.global_code_explain_md_path}")

            # Set the result
            result.explanation = explanation
            result.output_path = self.global_code_explain_md_path
            result.suggested_questions = suggested_questions

        except Exception as e:
            logger.error(f"Error generating codebase explanation: {e}")
            import traceback
            logger.error(traceback.format_exc())
            result.success = False
            result.error_message = str(e)

        return result

async def generate_codebase_explanation(
    taxonomy_json_path: str,
    global_code_explain_md_path: str,
    suggested_questions_md_path: str,
    openai_api_key: Optional[str] = None,
    openai_model: str = "gpt-4o-mini",
    max_tokens: int = 4000,
    temperature: float = 0.7,
    use_streaming: bool = False,
    stream_callback: Optional[Callable[[str], None]] = None,
    use_openrouter: bool = True,
    # do_generate_questions: bool = True,
) -> CodebaseExplanationResult:
    """
    Generate a codebase explanation from a domain taxonomy JSON file.

    Args:
        taxonomy_json_path: Path to the domain taxonomy JSON file
        global_code_explain_md_path: Path to save the output markdown file
        openai_api_key: OpenAI API key (if None, will try to get from environment)
        openai_model: OpenAI model to use
        max_tokens: Maximum tokens to generate
        temperature: Sampling temperature

    Returns:
        CodebaseExplanationResult containing the explanation
    """
    explainer = CodebaseExplainer(
        taxonomy_json_path=taxonomy_json_path,
        global_code_explain_md_path=global_code_explain_md_path,
        suggested_questions_md_path=suggested_questions_md_path,
        openai_api_key=openai_api_key,
        openai_model=openai_model,
        max_tokens=max_tokens,
        temperature=temperature,
        use_streaming=use_streaming,
        stream_callback=stream_callback,
        use_openrouter=use_openrouter,
        # do_generate_questions=do_generate_questions,
    )

    return await explainer.generate_explanation()
