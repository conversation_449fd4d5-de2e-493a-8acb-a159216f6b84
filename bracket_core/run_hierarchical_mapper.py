#!/usr/bin/env python3
"""
Run the hierarchical domain file mapper standalone.

This script provides a convenient way to run the hierarchical domain file mapper
without having to go through the full repository analysis flow.

Example usage:
    python run_hierarchical_mapper.py --domain-yaml /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/beauty_of_life/data/django/domain_analysis.yaml \
                                     --file-graph-yaml /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/beauty_of_life/data/django/file_call_graph.yaml \
                                     --output /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/beauty_of_life/data/django/hierarchical_domain_file_mappings.yaml \
                                     --go-gemini


python run_hierarchical_mapper.py \
  --domain-yaml ./experiments/file_cg2_test/mem0-1/domain_analysis.yaml \
  --file-graph-yaml ./experiments/file_cg2_test/mem0-1/file_call_graph.yaml \
  --output ./experiments/file_cg2_test/mem0-1/hierarchical_domain_file_mappings_3.yaml


python run_hierarchical_mapper.py \
  --domain-yaml /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/big_codebase/pytorch/domain_analysis.yaml \
  --file-graph-yaml /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/big_codebase/pytorch/file_call_graph.yaml \
  --output /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/big_codebase/pytorch_run_hierarch/hierarchical_domain_file_mappings.yaml \
  --go-gemini


python run_hierarchical_mapper.py \
--domain-yaml /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/domain_analysis.yaml \
--file-graph-yaml /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/file_call_graph.yaml \
--output /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/domain_file_mappings.yaml


python run_hierarchical_mapper.py \
--domain-yaml /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/domain_analysis.yaml \
--file-graph-yaml /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/file_call_graph.yaml \
--output /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/domain_file_mappings.yaml
--batch-size 150 \
--requests-per-minute 2000 \
--tokens-per-minute 5000000 \
--max-concurrent-tasks 40

"""

import asyncio
import time
import os
import logging
from bracket_core.hierarchical_domain_file_mapper import HierarchicalDomainFileMapper

# Configure logging
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Reduce noise from other loggers by default
logging.getLogger('root').setLevel(logging.WARNING)
logging.getLogger('openai').setLevel(logging.WARNING)
logging.getLogger('httpx').setLevel(logging.WARNING)

async def run_mapper(
    domain_yaml_path: str,
    file_graph_yaml_path: str,
    output_path: str,
    model: str = "gpt-4o-mini",
    use_openrouter: bool = False,
    go_gemini: bool = False,  # Default to False as Gemini rate limits are too restrictive
    batch_size: int = 50,    # Increased batch size to reduce number of API calls
    requests_per_minute: float = 3000,  # Increased to handle larger batches
    tokens_per_minute: float = 50000000,  # Increased to 50M tokens per minute
    verbose: bool = False,
    max_concurrent_tasks: int = 30,  # Optimized for 12-core MacBook Pro
    checkpoint_interval_seconds: int = 300,  # Save state every 5 minutes by default
):
    """
    Run the hierarchical domain file mapper with the specified parameters.

    Args:
        domain_yaml_path: Path to the domain analysis YAML file
        file_graph_yaml_path: Path to the file-driven call graph YAML file
        output_path: Path to save the hierarchical domain-to-file mappings YAML
        model: Model to use (OpenAI or OpenRouter model ID)
        use_openrouter: Whether to use OpenRouter instead of OpenAI
        go_gemini: Whether to use Gemini model for top-level domain file allocation
        batch_size: Number of files to process in each batch
        requests_per_minute: Rate limit for API requests
        tokens_per_minute: Token rate limit for API
        max_concurrent_tasks: Maximum number of concurrent API tasks
        checkpoint_interval_seconds: Time interval in seconds between automatic checkpoints

    Returns:
        True if mapping was successful, False otherwise
    """
    # Check if the files exist
    if not os.path.exists(domain_yaml_path):
        logger.error(f"Domain analysis YAML file not found: {domain_yaml_path}")
        return False

    if not os.path.exists(file_graph_yaml_path):
        logger.error(f"File call graph YAML file not found: {file_graph_yaml_path}")
        return False

    # Ensure output directory exists
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    # Set up logging based on verbosity
    if verbose:
        # Enable more detailed logging
        logging.getLogger('bracket_core').setLevel(logging.DEBUG)
        # Show all log messages
        logging.getLogger('root').setLevel(logging.INFO)
    else:
        # Keep default logging levels
        logging.getLogger('bracket_core').setLevel(logging.INFO)

    # Log the configuration
    logger.info(f"Domain analysis YAML: {domain_yaml_path}")
    logger.info(f"File call graph YAML: {file_graph_yaml_path}")
    logger.info(f"Output path: {output_path}")
    logger.info(f"Model: {model}")
    logger.info(f"Use OpenRouter: {use_openrouter}")
    logger.info(f"Go Gemini: {go_gemini}")
    logger.info(f"Verbose logging: {verbose}")

    try:
        # Initialize the mapper with optimized parameters
        mapper = HierarchicalDomainFileMapper(
            domain_analysis_yaml_path=domain_yaml_path,
            file_call_graph_yaml_path=file_graph_yaml_path,
            output_path=output_path,
            api_key=None,  # API key will be handled by the centralized management
            model=model,
            max_requests_per_minute=requests_per_minute,
            max_tokens_per_minute=tokens_per_minute,
            temperature=0.1,
            batch_size=batch_size,
            use_openrouter=use_openrouter,
            openrouter_base_url="https://openrouter.ai/api/v1",
            go_gemini=go_gemini,
            max_concurrent_tasks=max_concurrent_tasks,
            checkpoint_interval_seconds=checkpoint_interval_seconds
        )

        # Map files to domains hierarchically
        logger.info("Starting hierarchical domain-to-file mapping...")
        result = await mapper.map_files_to_domains_hierarchically()

        if result.success:
            logger.info(f"Hierarchical domain-to-file mapping completed successfully. Results saved to: {output_path}")
            return True
        else:
            logger.error(f"Hierarchical domain-to-file mapping failed: {result.error_message}")
            return False

    except Exception as e:
        logger.error(f"Error in hierarchical domain-to-file mapping: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Run the hierarchical domain file mapper standalone")
    parser.add_argument("--domain-yaml", required=True, help="Path to the domain analysis YAML file")
    parser.add_argument("--file-graph-yaml", required=True, help="Path to the file-driven call graph YAML file")
    parser.add_argument("--output", required=True, help="Path to save the hierarchical domain-to-file mappings YAML")
    parser.add_argument("--model", default="gpt-4o-mini", help="Model to use (OpenAI or OpenRouter model ID)")
    parser.add_argument("--use-openrouter", action="store_true", help="Use OpenRouter instead of OpenAI")
    parser.add_argument("--go-gemini", action="store_true", help="Use Gemini model for top-level domain file allocation")
    parser.add_argument("--batch-size", type=int, default=50, help="Number of files to process in each batch")
    parser.add_argument("--requests-per-minute", type=float, default=3000, help="Rate limit for API requests")
    parser.add_argument("--tokens-per-minute", type=float, default=50000000, help="Token rate limit for API")
    parser.add_argument("--max-concurrent-tasks", type=int, default=30, help="Maximum number of concurrent tasks (optimized for 12-core MacBook Pro)")
    parser.add_argument("--checkpoint-interval", type=int, default=180, help="Time interval in seconds between automatic checkpoints")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")

    start_time = time.time()
    args = parser.parse_args()

    success = asyncio.run(run_mapper(
        domain_yaml_path=args.domain_yaml,
        file_graph_yaml_path=args.file_graph_yaml,
        output_path=args.output,
        model=args.model,
        use_openrouter=args.use_openrouter,
        go_gemini=args.go_gemini,
        batch_size=args.batch_size,
        requests_per_minute=args.requests_per_minute,
        tokens_per_minute=args.tokens_per_minute,
        verbose=args.verbose,
        max_concurrent_tasks=args.max_concurrent_tasks,
        checkpoint_interval_seconds=args.checkpoint_interval,
    ))
    end_time = time.time()
    print(f"Time taken: {end_time-start_time}")

    exit(0 if success else 1)
