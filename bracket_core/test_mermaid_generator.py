"""Test script for the MermaidDiagramGenerator."""

import os
import asyncio
import argparse
import logging
import time
from typing import List, Optional

from bracket_core.mermaid_generator import MermaidDiagramGenerator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_mermaid_generation(
    json_path: str,
    output_dir: str,
    claude_api_key: Optional[str] = None,
    claude_model: str = "claude-3-5-haiku-latest",
    max_tokens: int = 4096,
    temperature: float = 0.5,
    save_raw_output: bool = True,
) -> List[str]:
    """
    Test the mermaid diagram generation with an existing JSON file.
    
    Args:
        json_path: Path to the existing intermediate_representation_layer.json file
        output_dir: Directory to save the generated diagrams
        claude_api_key: Anthropic API key (optional, will use env var if not provided)
        claude_model: Claude model to use
        max_tokens: Maximum tokens to generate
        temperature: Sampling temperature
        save_raw_output: Whether to save raw Claude responses as text files
        
    Returns:
        List of paths to the generated diagram files
    """
    logger.info(f"Testing mermaid diagram generation with JSON: {json_path}")
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Create a raw outputs directory if saving raw outputs
    raw_output_dir = os.path.join(output_dir, "raw_claude_outputs")
    if save_raw_output:
        os.makedirs(raw_output_dir, exist_ok=True)
    
    # Initialize the mermaid diagram generator
    generator = MermaidDiagramGenerator(
        output_dir=output_dir,
        claude_api_key=claude_api_key,
        claude_model=claude_model,
        max_tokens=max_tokens,
        temperature=temperature,
    )
    
    # Patch the generate method to capture raw outputs
    original_generate = generator.claude_client.generate
    
    async def patched_generate(*args, **kwargs):
        response = await original_generate(*args, **kwargs)
        if save_raw_output:
            # Save the raw response to a file
            timestamp = int(time.time())
            output_file = os.path.join(raw_output_dir, f"claude_response_{timestamp}.md")
            with open(output_file, "w") as f:
                f.write(response)
            logger.info(f"Saved raw Claude response to: {output_file}")
        return response
    
    # Apply the patch
    if save_raw_output:
        generator.claude_client.generate = patched_generate
    
    # Generate diagrams from the JSON
    diagram_files = await generator.generate_diagrams_from_json(json_path, individual_diagrams=True)
    
    logger.info(f"Generated {len(diagram_files)} mermaid diagrams:")
    for file_path in diagram_files:
        logger.info(f"  - {file_path}")
    
    return diagram_files

async def main():
    """Main entry point for the test script."""
    # Configuration variables - modify these values directly
    # json_path = "/Users/<USER>/work/startup/godzilla/bracket/bracket/bracket_outputs/mem0-x2/intermediate_representation_layer_very_opt.json"
    # output_dir = "/Users/<USER>/work/startup/godzilla/bracket/bracket/bracket_outputs/claude_test/mem0_cache"

    # json_path = "/Users/<USER>/work/startup/godzilla/bracket/bracket/bracket_outputs/aider-signify/intermediate_representation_layer_very_opt.json"
    # output_dir = "/Users/<USER>/work/startup/godzilla/bracket/bracket/bracket_outputs/claude_test/aider_signify_cached_1"

    json_path = "/Users/<USER>/work/startup/godzilla/bracket/bracket/bracket_outputs/requests/intermediate_representation_layer_very_opt.json"
    output_dir = "/Users/<USER>/work/startup/godzilla/bracket/bracket/bracket_outputs/claude_test/requests"

    claude_api_key = None  # Will use env var if None
    # claude_model = "claude-3-5-haiku-latest"
    claude_model = "claude-3-7-sonnet-20250219"
    max_tokens = 8192
    temperature = 0.7
    save_raw_output = True  # Set to True to save raw Claude responses
    
    await test_mermaid_generation(
        json_path=json_path,
        output_dir=output_dir,
        claude_api_key=claude_api_key,
        claude_model=claude_model,
        max_tokens=max_tokens,
        temperature=temperature,
        save_raw_output=save_raw_output,
    )

if __name__ == "__main__":
    asyncio.run(main())
