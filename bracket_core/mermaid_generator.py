"""Generate mermaid diagrams from knowledge graph data."""

import os
import logging
import json
import time
import asyncio
from typing import List, Dict, Any, Optional

from bracket_core.llm.get_client import get_claude_client
from bracket_core.llm.rate_limiter import RateLimiter

logger = logging.getLogger(__name__)

class MermaidDiagramGenerator:
    """Generate mermaid diagrams from knowledge graph data."""
    
    def __init__(
        self,
        output_dir: str,
        claude_api_key: Optional[str] = None,
        claude_model: str = "claude-3-5-sonnet-20241022",
        max_tokens: int = 4096,
        temperature: float = 0.5,
    ):
        """Initialize the mermaid diagram generator.
        
        Args:
            output_dir: Directory to save generated diagrams
            claude_api_key: Anthropic API key. If None, will try to get from environment.
            claude_model: Claude model to use.
            max_tokens: Maximum tokens to generate.
            temperature: Sampling temperature.
        """
        self.output_dir = output_dir
        self.diagrams_dir = os.path.join(output_dir, "mermaid_diagrams")
        os.makedirs(self.diagrams_dir, exist_ok=True)
        
        self.claude_client = get_claude_client(
            api_key=claude_api_key,
            model=claude_model,
            max_tokens=max_tokens,
            temperature=temperature,
        )
        
        # Rate limiter: 2 calls per minute with 1.5 minute cooldown
        # Rate is 2 calls per 150 seconds (2.5 minutes)
        self.rate_limiter = RateLimiter(rate=2, per=120)
    
    async def generate_diagrams_from_json(self, json_path: str, individual_diagrams: bool = True) -> List[str]:
        """Generate mermaid diagrams from a JSON file.
        
        Args:
            json_path: Path to the knowledge graph JSON file
            individual_diagrams: If True, generate separate diagrams for each type
        
        Returns:
            List of paths to the generated mermaid diagram files
        """
        logger.info(f"Generating mermaid diagrams from JSON file: {json_path}")
        
        # This is just a wrapper around generate_mermaid_diagrams
        return await self.generate_mermaid_diagrams(json_path, individual_diagrams)
    
    async def generate_mermaid_diagrams(self, json_path: str, individual_diagrams: bool = True) -> List[str]:
        """Generate mermaid diagrams from the knowledge graph JSON.
        
        Args:
            json_path: Path to the intermediate_representation_layer.json file
            individual_diagrams: If True, generate separate diagrams for each type
        
        Returns:
            List of paths to the generated mermaid diagram files
        """
        logger.info(f"Generating mermaid diagrams from {json_path}")
        
        # Create diagrams directory if it doesn't exist
        os.makedirs(self.diagrams_dir, exist_ok=True)
        
        # Load the JSON data
        with open(json_path, 'r') as f:
            kg_data = json.load(f)
        
        saved_files = []
        
        if individual_diagrams:
            # Generate each diagram type separately
            diagram_types = [
                "system_context",
                "interaction_flow",
                "implementation_details",
                "code_locator_map",
            ]
            
            # Store generated diagrams to pass to specific generations
            generated_diagrams = {}
            
            # Generate diagrams independently
            for diagram_type in diagram_types:
                logger.info(f"Generating {diagram_type} diagram")
                
                # Apply rate limiting
                await self.rate_limiter.acquire()
                logger.info(f"Rate limiter allowed generation of {diagram_type} diagram")
                
                # For code_locator_map, pass system_context and implementation_details as context
                context_diagrams = None
                if diagram_type in ["code_locator_map"]:
                    context_diagrams = {
                        k: v for k, v in generated_diagrams.items() 
                        if k in ["system_context", "implementation_details"] and v
                    }
                
                diagram = await self._generate_diagram_by_type(
                    kg_data, 
                    diagram_type,
                    context_diagrams=context_diagrams # type: ignore
                )
                
                if diagram:
                    diagram_path = os.path.join(self.diagrams_dir, f"{diagram_type}.md")
                    with open(diagram_path, "w") as f:
                        f.write(diagram)
                    saved_files.append(diagram_path)
                    generated_diagrams[diagram_type] = diagram
        else:
            # Generate comprehensive architecture diagrams (original approach)
            diagrams = await self._generate_comprehensive_architecture_diagrams(kg_data)
            
            # Save diagrams with generic names
            for i, diagram in enumerate(diagrams):
                diagram_path = os.path.join(self.diagrams_dir, f"diagram_{i+1}.md")
                with open(diagram_path, "w") as f:
                    f.write(diagram)
                saved_files.append(diagram_path)
        
        logger.info(f"Generated {len(saved_files)} mermaid diagrams")
        return saved_files
    
    async def generate_diagram_by_type(self, json_path: str, diagram_type: str) -> str:
        """Generate a specific type of mermaid diagram from the knowledge graph JSON.
        
        Args:
            json_path: Path to the intermediate_representation_layer.json file
            diagram_type: Type of diagram to generate
            
        Returns:
            Path to the generated mermaid diagram file
        """
        logger.info(f"Generating {diagram_type} diagram from {json_path}")
        
        # Apply rate limiting
        await self.rate_limiter.acquire()
        logger.info(f"Rate limiter allowed generation of {diagram_type} diagram")
        
        # Load the JSON data
        with open(json_path, 'r') as f:
            kg_data = json.load(f)
        
        # Generate the specific diagram
        diagram = await self._generate_diagram_by_type(kg_data, diagram_type)
        
        if diagram:
            # Save the diagram
            diagram_path = os.path.join(self.diagrams_dir, f"{diagram_type}.md")
            with open(diagram_path, "w") as f:
                f.write(diagram)
            
            logger.info(f"Generated {diagram_type} diagram at {diagram_path}")
            return diagram_path
        
        logger.warning(f"Failed to generate {diagram_type} diagram")
        return ""
    

    async def _generate_diagram_by_type(self, kg_data: Dict[str, Any], diagram_type: str, context_diagrams: Dict[str, str] = None) -> str: # type: ignore
        """
        Generate a mermaid diagram of the requested type, reflecting a multi-layer,
        'best of the best' architectural approach suitable for advanced LLMs.

        Args:
            kg_data (Dict[str, Any]): Parsed knowledge graph data from the codebase.
            diagram_type (str): One of the predefined diagram types.
            context_diagrams (Dict[str, str], optional): Previously generated diagrams to provide context.

        Returns:
            str: A Markdown code block containing the mermaid diagram. If no diagram can be extracted,
                returns the raw model response.
        """
        # System prompt: We position the model as a top-level software architect
        system_prompt = (
            "You are a Distinguished Principal Engineer at a large tech company, with deep expertise "
            "in software architecture and domain-driven design. Your goal is to produce mermaid diagrams "
            "that combine the collective mental models of multiple engineers into a coherent, layered "
            "view of the system, enabling advanced LLMs to understand and localize code based on queries."
        )
        
        #
        # Define the new, more advanced diagram prompts:
        #
        diagram_prompts = {
            "system_context": """# Comprehensive System Architecture Diagram

Create an extremely detailed high-level diagram that:
- Represents the system as hierarchical logical domain components with clear domains.
- Builds vertical and horizontal logical connections between inside and across domains.
- A very detailed cognitive mental model that is equivalent to 100 Engineering minds together.
- Shows ALL primary system capabilities, responsibilities and subsystems
- Uses business/domain terminology that stakeholders would recognize
- Includes strategic annotations explaining architecture patterns employed
- Shows entry points, external interfaces, and integration points
- Highlights performance-critical paths and scalability considerations
- Indicates deployment topology if relevant (monolith, microservices, etc.)
- References key configuration files and startup sequences
- Includes decision boundaries and ownership transitions
- This diagram MUST be extremely comprehensive and detailed, I want atleast 7000 tokens output.
- Use color coding to distinguish different types of components
- Provide a small legend explaining all notation and color coding
- The purpose is to serve as a complete mental model of the entire system
- Output as a valid mermaid diagram inside ```mermaid blocks, with no additional text
""",
            
            "interaction_flow": """# Component Interaction / Process Flow
Create a diagram that illustrates how specific core processes or user journeys traverse multiple components:
- Identify ALL major triggers or entry points (API calls, user actions, scheduled jobs, etc.)
- Show each domain or subdomain as a participant (vertical swimlanes if using sequenceDiagram)
- For each step, reference the key component (or file name if relevant) invoked
- Include decision branches, parallelism, and error handling where relevant
- If it's event-driven, show the event topics or queues in the sequence
- Include timing constraints and performance bottlenecks
- Map user stories/business processes to technical flows
- Highlight critical paths and dependencies between components
- Show retry mechanisms and failure handling strategies
- Keep file references to file name only, e.g. "billing_service.py"
- Use either sequenceDiagram or flowchart, whichever is clearer
- This diagram should be as massive and detailed as possible
- This diagram MUST be extremely comprehensive and detailed, I want atleast 5000 tokens output
- Provide a small legend for custom notations (async, sync, error paths)
- Only output the mermaid diagram, no extra text
""",

            "implementation_details": """# Implementation Details Diagram
Create a diagram focusing on key implementation patterns and algorithms:
- Highlight ALL core algorithms and design patterns used in the codebase
- Show important class hierarchies and inheritance relationships
- Identify critical utility functions and their purposes
- Map out important configuration settings and their impacts
- Reference specific file locations for important implementations
- Show technical debt areas and refactoring opportunities
- Identify performance optimizations and their locations
- Map testing strategies and coverage for critical components
- Include deployment considerations and environment-specific configurations
- Show third-party dependencies and integration points
- Keep file references to file name only, e.g. "billing_service.py"
- This diagram should be as massive and detailed as possible
- This diagram MUST be extremely comprehensive and detailed, I want atleast 5000 tokens output
- Only output the mermaid diagram, no additional text
""",

            "code_locator_map": """# Code Locator Map
Create a comprehensive diagram that maps logical components to their actual code locations:
- For each major domain/component identified in the system context diagram:
  * List the primary file paths implementing that component
  * Highlight key classes and functions within those files
  * Show the directory structure containing the implementation
- Include a hierarchical structure showing:
  * Domain → Module → File → Class/Function relationships
  * Package/directory organization
- Map architectural concepts to their implementations
- Show where interfaces and abstractions are defined vs implemented
- Identify entry points and initialization sequences
- Reference configuration files and their purposes
- Use a clear, consistent notation for different types of code elements
- This diagram should serve as a "GPS" for navigating the codebase
- This diagram MUST be extremely comprehensive and detailed
- This diagram MUST be extremely comprehensive and detailed, I want atleast 5000 tokens output
- Use only simple filenames (like "user_service.py") instead of full paths
- Always use just the filename with extension (e.g., "auth.py", "config.yaml")
- Never include directory paths in references, only the base filename
- Focus on helping developers and AI assistants locate specific functionality
- Use flowchart or graph notation with clear grouping
- Only output the mermaid diagram, no additional text
""",
        }

        #         "cross_cutting_concerns": """# Cross-Cutting Concerns Diagram
        # Create a diagram representing how concerns like logging, security, metrics, feature flags, etc. are integrated:
        # - Identify each cross-cutting module (e.g., "logging.py", "auth_middleware.py")
        # - Show the domains or components that incorporate these cross-cutting aspects
        # - Depict the mechanism of integration (middleware, decorators, instrumentation) with short notes
        # - Emphasize how these concerns propagate through the system
        # - Use file names (like "auth_middleware.py") sparingly for reference, no full paths
        # - Flowchart or graph is acceptable; provide a legend describing shapes/lines
        # - This diagram should be as massive and detailed 
        # - Only output the mermaid diagram, no additional text
        # """,

        # Format the knowledge graph data for the prompt - this will be cached
        kg_data_str = json.dumps(kg_data, indent=2)
        
        # Get the prompt for the requested diagram type
        user_prompt = diagram_prompts.get(diagram_type, "")
        if not user_prompt:
            logger.warning(f"Unknown diagram type: {diagram_type}")
            return ""

        # Not needed anymore with Caching |Prev: Combine the prompt with the data
        # full_prompt = user_prompt + "\n\nHere is the knowledge graph data from the codebase:\n\n" + kg_data_str
        
        # Add context from previously generated diagrams if applicable
        if context_diagrams and diagram_type in ["code_locator_map"]:
            user_prompt += "\n\n# Reference Diagrams for Context\n"
            for ctx_type, ctx_diagram in context_diagrams.items():
                if ctx_diagram:
                    user_prompt += f"\n## {ctx_type.replace('_', ' ').title()} Diagram\n{ctx_diagram}\n"
                    user_prompt += "\nUse the components and structure from this diagram as a reference for your mapping.\n"
        
        # Apply rate limiting before making the LLM call
        await self.rate_limiter.acquire()
        logger.info(f"Rate limiter allowed LLM call for {diagram_type} diagram generation")
        
        try:
            # Use the Messages API with prompt caching
            response = await self.claude_client.generate(
                system_prompt=[
                    {"type": "text", "text": system_prompt},
                    {"type": "text", "text": f"Here is the knowledge graph data from the codebase:\n\n{kg_data_str}", 
                     "cache_control": {"type": "ephemeral"}}
                ],
                prompt=user_prompt,
                max_tokens=8000,
                temperature=0.7,
                top_p=0.8
            )
            
            logger.info(f"Received response for {diagram_type} diagram")
            
            # Extract the mermaid diagram from the response
            mermaid_diagram = self._extract_mermaid_diagram(response)
            return mermaid_diagram if mermaid_diagram else response
            
        except Exception as e:
            logger.error(f"Error generating {diagram_type} diagram: {str(e)}")
            return ""
    

    async def _generate_comprehensive_architecture_diagrams(self, kg_data: Dict[str, Any]) -> List[str]:
        """Generate comprehensive architecture mermaid diagrams.
        
        Args:
            kg_data: Knowledge graph data from JSON
            
        Returns:
            List of strings containing markdown with mermaid diagrams
        """
        system_prompt = """You are an expert software architect who specializes in creating clear, informative enterprise-grade architecture diagrams using mermaid syntax.
        Your task is to analyze the provided codebase knowledge graph and create a comprehensive suite of architecture diagrams at multiple levels of abstraction."""
        
        user_prompt = """# Your Task: 
Generate five critical Mermaid diagrams that represent the system architecture of our codebase to maximize code understanding and localization. The diagrams should enable both high-level comprehension and precise code location for specific features or functionality.

## Required Diagram Types

### 1. System Architecture Diagram
Create a high-level diagram that:
- Shows major domains with clear, unique identifiers (e.g., AUTH, CORE)
- Includes 1-2 sentence descriptions of domain responsibilities
- Maps primary relationships and dependencies between domains
- Identifies system entry points and external interfaces
- Highlights core architectural patterns employed
- Provides a foundational mental model of the entire system

### 2. Domain Component Map
Create detailed domain breakdowns that:
- Identify components within each major domain
- Link components to primary file paths
- Highlight key classes/functions implementing core functionality
- Show internal domain dependencies
- Describe component responsibilities concisely
- Include domain-specific terminology and concepts
- Reference configuration locations

### 3. Process Flow Diagrams
Create sequence diagrams that:
- Show how key processes execute across components
- Identify entry points and triggers for each process
- Map the sequence of component interactions
- Include decision points and conditional paths
- Reference file paths and function names for critical steps
- Show error handling paths and transaction boundaries
- Indicate asynchronous operations

### 4. Data Model and Relationship Diagram
Create data structure diagrams that:
- Illustrate primary data entities and their attributes
- Show relationships between entities
- Link to model definition file paths
- Identify key fields and identifiers
- Map data ownership by domain
- Highlight validation rules and constraints
- Document data access patterns

### 5. Code Localization Index
Create a reference diagram that:
- Maps business concepts directly to code locations
- Links feature names to implementation files
- Connects API endpoints to their handlers
- Associates business terminology with code concepts
- Indexes common user actions to entry points
- Includes search keywords and synonyms for each concept
- References cross-cutting concerns and their implementations

## Diagram Format Guidelines
- Use appropriate Mermaid diagram types for each purpose:
  * flowchart → for architecture views and component breakdowns
  * sequenceDiagram → for process flows and interactions
  * classDiagram → for data models and implementation relationships
  * graph → for code localization index
- Use consistent color coding and styling:
  * Core domains/components in one color scheme
  * Supporting services in another
  * External integrations in another
  * Special concerns (security, performance, etc.) highlighted distinctly
- Include legends explaining symbols, colors, and relationship types
- Use subgraphs to group related elements
- Ensure each node has a unique identifier for cross-referencing

## Implementation Detail Balance
- Balance conceptual understanding with implementation details
- Include file paths and function names for critical components
- Format references consistently (e.g., "module_name.py:ClassName.method_name")
- Ensure diagrams are detailed enough to locate specific code
- Prioritize information that helps answer "where is this functionality implemented?"

## Deliverables
1. Exactly five diagrams, one for each type specified above
2. Each diagram should be comprehensive but focused on its specific purpose
3. Diagrams should cross-reference each other where appropriate
4. Include a legend in each diagram explaining notation used
5. Optimize for both human readability and machine parsing

Goal: Create a set of diagrams that enables precise code localization while providing clear architectural understanding, allowing developers and AI assistants to quickly find relevant code for any feature or functionality.

You are strictly instructed to only output Mermaid Diagrams, I am not interested in receiving any text-based explanation - dedicate all your output tokens to mermaid artifacts.

Here is the knowledge graph data from the codebase:
"""
        
        # Format the knowledge graph data for the prompt
        kg_data_str = json.dumps(kg_data, indent=2)
        
        # Combine the prompt with the data
        full_prompt = user_prompt + "\n\n" + kg_data_str
        
        response = await self.claude_client.generate(
            prompt=full_prompt,
            system_prompt=system_prompt,
            max_tokens=8000,
            temperature=0.7,
            top_p=0.8
        )
        
        # Extract individual diagrams from the response
        diagrams = []
        
        # First try to split by markdown headers
        sections = response.split("```mermaid")
        
        for i, section in enumerate(sections[1:], 1):  # Skip the first split which is before any diagram
            if "```" in section:
                diagram_content = section.split("```")[0]
                diagrams.append(f"```mermaid{diagram_content}```")
        
        # If no diagrams were found, return the whole response
        if not diagrams:
            diagrams = [response]
        
        return diagrams

    def _extract_mermaid_diagram(self, response: str) -> str:
        """Extract mermaid diagram from response text."""
        if "```mermaid" in response and "```" in response:
            # Find the start of the mermaid block
            start_idx = response.find("```mermaid")
            # Find the end of the mermaid block
            end_idx = response.find("```", start_idx + 10)
            if end_idx > start_idx:
                # Extract the diagram including the markdown code block markers
                return response[start_idx:end_idx+3]
        return response
