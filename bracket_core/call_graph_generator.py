#!/usr/bin/env python3
"""
Codebase Call Graph Generator
This script analyzes a codebase containing multiple programming languages
and generates a call graph in JSON format.
"""

import os
import sys
import json
import subprocess
import tempfile
import argparse
from pathlib import Path
from collections import defaultdict

# Language extensions mapping
LANGUAGE_EXTENSIONS = {
    'py': 'py',
    'js': 'js',
    'ts': 'ts',
    # 'rb': 'ruby',
    'rb': 'rb',
    'pl': 'pl',
    'pm': 'pl',
    'rs': 'rs',
    'go': 'go',
    'lua': 'lua',
    # 'sh': 'bash',
    'sh': 'sh',
    'bash': 'bash',
    'tcl': 'tcl',
    'swift': 'swift',
    'kt': 'kotlin',
    'scala': 'scala',
    'php': 'php',
    'r': 'R',
    'jl': 'julia',
    'dart': 'dart',
    'f90': 'fortran',
    'f95': 'fortran',
    'f03': 'fortran',
    'pas': 'pascal',
    'awk': 'awk',
    'raku': 'raku',
    'matlab': 'matlab',
    'm': 'matlab',
    'bas': 'basic'
}

def create_patched_callgraph(original_callgraph_path):
    """Create a patched version of callGraph that skips GraphViz checks"""
    patched_path = os.path.join(os.path.dirname(original_callgraph_path), 'callGraph_patched')
    
    # Read the original file
    with open(original_callgraph_path, 'r') as f:
        content = f.read()
    
    # Patch the GraphViz check
    patched_content = content.replace(
        "eval 'use GraphViz ()'",
        "# Skip GraphViz check\n# eval 'use GraphViz ()'"
    )
    
    # Write the patched file
    with open(patched_path, 'w') as f:
        f.write(patched_content)
    
    # Make it executable
    os.chmod(patched_path, 0o755)
    
    return patched_path

def find_callgraph_executable():
    """Find the callGraph executable in the current directory or PATH"""
    # First check current directory
    if os.path.exists('./callGraph') and os.access('./callGraph', os.X_OK):
        original_path = './callGraph'
        return create_patched_callgraph(original_path)
    
    # Check PATH
    for path in os.environ["PATH"].split(os.pathsep):
        exe_file = os.path.join(path, 'callGraph')
        if os.path.exists(exe_file) and os.access(exe_file, os.X_OK):
            return create_patched_callgraph(exe_file)
    
    return None

def get_file_language(file_path):
    """Determine the language of a file based on its extension"""
    ext = file_path.suffix.lstrip('.')
    return LANGUAGE_EXTENSIONS.get(ext.lower())

def group_files_by_language(codebase_path):
    """Group files in the codebase by their language"""
    language_files = defaultdict(list)
    
    for root, _, files in os.walk(codebase_path):
        for file in files:
            file_path = Path(os.path.join(root, file))
            language = get_file_language(file_path)
            if language:
                language_files[language].append(str(file_path))
    
    return language_files

def analyze_language_files(callgraph_exe, language, files, output_dir):
    """Analyze files of a specific language and generate YAML output"""
    if not files:
        return None
    
    # Create a temporary directory for batch outputs
    temp_dir = tempfile.mkdtemp(prefix=f"callgraph_{language}_")
    final_output_file = os.path.join(output_dir, f"{language}_callgraph.yml")
    
    try:
        print(f"Analyzing {len(files)} {language} files...")
        batch_size = 5000  # Adjust based on your system limits
        batch_outputs = []
        
        for i in range(0, len(files), batch_size):
            batch_files = files[i:i+batch_size]
            batch_output = os.path.join(temp_dir, f"batch_{i//batch_size}.yml")
            batch_outputs.append(batch_output)
            
            batch_cmd = [
                callgraph_exe,
                *batch_files,
                "-language", language,
                "-ymlOut", batch_output,
                "-noShow"
            ]
            
            print(f"Processing batch {i//batch_size + 1}/{(len(files)-1)//batch_size + 1} ({len(batch_files)} files)...")
            try:
                # Run without check=True to prevent exceptions on non-zero exit codes
                process = subprocess.run(batch_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                
                # Log warnings but don't treat as failures
                if process.returncode != 0:
                    print(f"  Warning: Batch {i//batch_size + 1} completed with return code {process.returncode}")
                    if process.stderr:
                        print(f"  stderr: {process.stderr[:200]}..." if len(process.stderr) > 200 else f"  stderr: {process.stderr}")
                
                # Check if output was generated regardless of return code
                if os.path.exists(batch_output) and os.path.getsize(batch_output) > 0:
                    print(f"  Batch {i//batch_size + 1} produced output file ({os.path.getsize(batch_output)} bytes)")
                    batch_outputs.append(batch_output)
                else:
                    print(f"  Warning: Batch {i//batch_size + 1} did not produce usable output")
                    
            except Exception as e:
                print(f"  Error executing batch {i//batch_size + 1}: {e}")
        
        # Merge all batch outputs
        print(f"Merging {len(batch_outputs)} batch results...")
        merge_yaml_files(batch_outputs, final_output_file)
        
        if os.path.exists(final_output_file) and os.path.getsize(final_output_file) > 0:
            print(f"Successfully created merged YAML file: {final_output_file}")
            return final_output_file
        else:
            print(f"Warning: Final merged YAML file is empty or not created")
            return None
            
    finally:
        # Clean up temporary files
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)

def merge_yaml_files(yaml_files, output_json):
    """Merge multiple YAML files into a single JSON structure"""
    try:
        import yaml
    except ImportError:
        print("YAML module not found. Installing pyyaml...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyyaml"])
        import yaml
    
    merged_data = {}
    
    for yaml_file in yaml_files:
        if not yaml_file or not os.path.exists(yaml_file):
            continue
            
        with open(yaml_file, 'r') as f:
            try:
                data = yaml.safe_load(f)
                if data:
                    merged_data.update(data)
            except Exception as e:
                print(f"Error parsing {yaml_file}: {e}")
    
    # Write merged data to JSON
    with open(output_json, 'w') as f:
        json.dump(merged_data, f, indent=2)
    
    return merged_data

def create_graph_structure(call_data):
    """Convert the call data into a more useful graph structure"""
    graph = {
        "nodes": [],
        "edges": [],
        "nodeMap": {}
    }
    
    # Create nodes
    node_id = 0
    for func_key in call_data:
        file, func = func_key.split(':')
        
        # Skip __MAIN__ nodes if there are too many
        if func == "__MAIN__" and len(call_data) > 100:
            continue
            
        graph["nodeMap"][func_key] = node_id
        graph["nodes"].append({
            "id": node_id,
            "name": func,
            "file": file,
            "fullName": func_key
        })
        node_id += 1
    
    # Create edges
    edge_id = 0
    for func_key, data in call_data.items():
        if "calls" in data:
            source_id = graph["nodeMap"].get(func_key)
            if source_id is None:
                continue
                
            for target_key in data["calls"]:
                target_id = graph["nodeMap"].get(target_key)
                if target_id is not None:
                    graph["edges"].append({
                        "id": edge_id,
                        "source": source_id,
                        "target": target_id,
                        "sourceName": func_key,
                        "targetName": target_key
                    })
                    edge_id += 1
    
    return graph

def generate_basic_callgraph(codebase_path, output_json, graph_output):
    """Generate a basic call graph using simple regex patterns when callGraph fails"""
    import re
    
    print("Falling back to basic call graph generation...")
    
    # Simple patterns for function definitions and calls
    patterns = {
        'py': {
            'def': r'def\s+([a-zA-Z0-9_]+)\s*\(',
            'call': r'([a-zA-Z0-9_]+)\s*\('
        },
        'js': {
            'def': r'function\s+([a-zA-Z0-9_]+)\s*\(|const\s+([a-zA-Z0-9_]+)\s*=\s*(?:async\s*)?\(|let\s+([a-zA-Z0-9_]+)\s*=\s*(?:async\s*)?\(',
            'call': r'([a-zA-Z0-9_]+)\s*\('
        },
        'rb': {
            'def': r'def\s+([a-zA-Z0-9_]+)',
            'call': r'([a-zA-Z0-9_]+)\s*\('
        }
    }
    
    call_data = {}
    
    # Group files by language
    language_files = group_files_by_language(codebase_path)
    
    for language, files in language_files.items():
        # Skip languages we don't have patterns for
        if language not in patterns:
            continue
            
        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # Find function definitions
                def_pattern = patterns[language]['def']
                functions = re.findall(def_pattern, content)
                
                # Handle multiple capture groups (like in JS pattern)
                if isinstance(functions[0], tuple) if functions else False:
                    functions = [next(name for name in func if name) for func in functions]
                
                # Find function calls for each function
                for func in functions:
                    func_key = f"{os.path.basename(file_path)}:{func}"
                    call_data[func_key] = {"defined_in": file_path, "calls": []}
                    
                    # Look for calls to other functions
                    call_pattern = patterns[language]['call']
                    calls = re.findall(call_pattern, content)
                    
                    # Filter out self-calls and add only calls to other defined functions
                    for call in calls:
                        if call != func:
                            for other_file in files:
                                with open(other_file, 'r', encoding='utf-8', errors='ignore') as other_f:
                                    other_content = other_f.read()
                                    if re.search(f"{def_pattern.replace('([a-zA-Z0-9_]+)', call)}", other_content):
                                        call_key = f"{os.path.basename(other_file)}:{call}"
                                        if call_key not in call_data[func_key]["calls"]:
                                            call_data[func_key]["calls"].append(call_key)
            except Exception as e:
                print(f"Error processing {file_path}: {e}")
    
    # Write to JSON
    with open(output_json, 'w') as f:
        json.dump(call_data, f, indent=2)
    
    # Create graph structure
    graph = create_graph_structure(call_data)
    
    # Write graph structure to file
    with open(graph_output, 'w') as f:
        json.dump(graph, f, indent=2)
    
    print(f"Basic call graph data written to {output_json}")
    print(f"Basic graph structure written to {graph_output}")
    print(f"Analyzed {len(language_files)} languages with basic analysis")
    
    return call_data

def enhance_cross_file_resolution(call_data):
    """Post-process call graph to improve cross-file function resolution
    
    This function identifies cases where functions from other files are called,
    but are incorrectly attributed to __MAIN__ instead of the specific function.
    """
    print("Enhancing cross-file function resolution...")
    
    # Track modifications for reporting
    fixed_relationships = 0
    
    # Step 1: Build a map of all functions defined in each file
    file_to_functions = {}
    for func_key in call_data:
        file, func = func_key.split(':')
        if file not in file_to_functions:
            file_to_functions[file] = set()
        file_to_functions[file].add(func)
    
    # Step 2: Find all __MAIN__ nodes with called_by relationships
    main_nodes = {}
    for func_key in call_data:
        file, func = func_key.split(':')
        if func == "__MAIN__" and "called_by" in call_data[func_key]:
            main_nodes[file] = func_key
    
    # Step 3: For each __MAIN__ node, redistribute its called_by relationships
    for file, main_key in main_nodes.items():
        if "called_by" not in call_data[main_key]:
            continue
            
        # Get all callers of this __MAIN__ node
        callers = list(call_data[main_key]["called_by"].keys())
        
        for caller_key in callers:
            caller_file, caller_func = caller_key.split(':')
            
            # Skip if caller is in the same file (likely correct)
            if caller_file == file:
                continue
                
            # Find the most likely actual function being called
            # First, check if there's a direct call from the caller to any function in this file
            potential_targets = []
            
            # Look at all functions the caller calls
            if caller_key in call_data and "calls" in call_data[caller_key]:
                for called_key in call_data[caller_key]["calls"]:
                    called_file, called_func = called_key.split(':')
                    if called_file == file and called_func != "__MAIN__":
                        potential_targets.append(called_key)
            
            # If we found potential targets, use them
            if potential_targets:
                for target_key in potential_targets:
                    # Add the called_by relationship to the actual function
                    if "called_by" not in call_data[target_key]:
                        call_data[target_key]["called_by"] = {}
                    call_data[target_key]["called_by"][caller_key] = call_data[main_key]["called_by"][caller_key]
                    fixed_relationships += 1
                
                # Remove the relationship from __MAIN__
                del call_data[main_key]["called_by"][caller_key]
            else:
                # No direct calls found, try to infer based on imports
                # This is a heuristic approach - we look for non-__MAIN__ functions in the file
                regular_functions = [f for f in file_to_functions.get(file, set()) if f != "__MAIN__"]
                
                if regular_functions:
                    # For simplicity, we'll assign to the first non-__MAIN__ function
                    # A more sophisticated approach would analyze the code to determine which function is actually used
                    target_func = regular_functions[0]
                    target_key = f"{file}:{target_func}"
                    
                    # Create the function entry if it doesn't exist
                    if target_key not in call_data:
                        call_data[target_key] = {}
                    
                    # Add the called_by relationship
                    if "called_by" not in call_data[target_key]:
                        call_data[target_key]["called_by"] = {}
                    call_data[target_key]["called_by"][caller_key] = call_data[main_key]["called_by"][caller_key]
                    fixed_relationships += 1
                    
                    # Remove the relationship from __MAIN__
                    del call_data[main_key]["called_by"][caller_key]
    
    print(f"Enhanced cross-file resolution: fixed {fixed_relationships} function relationships")
    return call_data

def main():
    # parser = argparse.ArgumentParser(description="Generate a call graph for a multi-language codebase")
    # parser.add_argument("codebase_path", help="Path to the codebase directory")
    # parser.add_argument("--output", "-o", default="callgraph_output.json", 
    #                     help="Output JSON file path (default: callgraph_output.json)")
    # parser.add_argument("--graph-output", "-g", default="callgraph_graph.json",
    #                     help="Graph structure JSON output (default: callgraph_graph.json)")
    # parser.add_argument("--basic", "-b", action="store_true",
    #                     help="Use basic regex-based analysis instead of callGraph")
    # args = parser.parse_args()
    
    # codebase_path = "/Users/<USER>/work/startup/godzilla/adjacent/mem0/mem0"
    codebase_path = "/Users/<USER>/work/startup/godzilla/bracket/bracket/graphrag"
    output_json = "/Users/<USER>/work/startup/godzilla/test/callGraph/output_bracket1/callgraph_output.json"
    graph_output = "/Users/<USER>/work/startup/godzilla/test/callGraph/output_bracket1/callgraph_graph.json"

    # codebase_path = "/Users/<USER>/work/startup/godzilla/test/pytorch"
    # output_json = "/Users/<USER>/work/startup/godzilla/test/callGraph/output_pytorch/callgraph_output.json"
    # graph_output = "/Users/<USER>/work/startup/godzilla/test/callGraph/output_pytorch/callgraph_graph.json"


    # codebase_path = "/Users/<USER>/work/startup/godzilla/test/gitlab"
    # output_json = "/Users/<USER>/work/startup/godzilla/test/callGraph/output_gitlab1/callgraph_output.json"
    # graph_output = "/Users/<USER>/work/startup/godzilla/test/callGraph/output_gitlab1/callgraph_graph.json"
    basic = False
    
    if basic:
        # Use basic analysis
        generate_basic_callgraph(codebase_path, output_json, graph_output)
        return
    
    # Find callGraph executable
    callgraph_exe = find_callgraph_executable()
    if not callgraph_exe:
        print("Error: callGraph executable not found. Falling back to basic analysis.")
        generate_basic_callgraph(codebase_path, output_json, graph_output)
        return
    
    # Create output directory for intermediate files (not using tempfile to preserve YAMLs)
    output_dir = os.path.dirname(output_json)
    os.makedirs(output_dir, exist_ok=True)
    
    # Group files by language
    language_files = group_files_by_language(codebase_path)
    
    if not language_files:
        print(f"No supported language files found in {codebase_path}")
        sys.exit(1)
    
    # Process each language
    yaml_outputs = []
    for language, files in language_files.items():
        yaml_file = analyze_language_files(callgraph_exe, language, files, output_dir)
        if yaml_file:
            yaml_outputs.append(yaml_file)
    
    if not yaml_outputs:
        print("No call graphs were generated with callGraph. Falling back to basic analysis.")
        generate_basic_callgraph(codebase_path, output_json, graph_output)
        return
    
    # Merge YAML files into JSON
    print(f"Merging {len(yaml_outputs)} call graphs...")
    call_data = merge_yaml_files(yaml_outputs, output_json)
    
    # Enhance cross-file resolution
    call_data = enhance_cross_file_resolution(call_data)
    
    # Create graph structure
    print("Creating graph structure...")
    graph = create_graph_structure(call_data)
    
    # Write updated call data to JSON
    with open(output_json, 'w') as f:
        json.dump(call_data, f, indent=2)
    
    # Write graph structure to file
    with open(graph_output, 'w') as f:
        json.dump(graph, f, indent=2)
    
    print(f"Call graph data written to {output_json}")
    print(f"Graph structure written to {graph_output}")
    print(f"YAML files preserved in {output_dir}")
    print(f"Analyzed {len(language_files)} languages, {sum(len(files) for files in language_files.values())} files")
    print(f"Generated graph with {len(graph['nodes'])} nodes and {len(graph['edges'])} edges")

if __name__ == "__main__":
    main()
