import os
import re
import json
from collections import Counter, defaultdict

# Hardcoded path to the Ruby codebase directory
RUBY_PROJECT_DIR = "/Users/<USER>/work/startup/godzilla/test/gitlab-1"  # change this
# RUBY_PROJECT_DIR = "/Users/<USER>/work/startup/godzilla/demo/gitlab"

def count_ruby_functions_in_file(file_path):
    with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
        content = f.read()
        # Count non-empty lines for LOC
        loc = sum(1 for line in content.splitlines() if line.strip())

    # Match Ruby method definitions: `def method_name ... end`
    # Assumes simple method declarations (can be improved for complex cases)
    function_count = len(re.findall(r'^\s*def\s+[a-zA-Z_][a-zA-Z0-9_]*', content, re.MULTILINE))
    
    return function_count, loc

def count_functions_in_project(directory):
    total_functions = 0
    file_function_counts = []
    function_loc_data = defaultdict(list)  # Maps function count to list of LOC values
    file_data = []  # Store data for each file
    files_by_function_count = defaultdict(list)  # Maps function count to list of files
    
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith(".rb"):
                file_path = os.path.join(root, file)
                count, loc = count_ruby_functions_in_file(file_path)
                print(f"{file_path}: {count} functions, {loc} LOC")
                total_functions += count
                file_function_counts.append(count)
                function_loc_data[count].append(loc)
                
                # Store file data
                rel_path = os.path.relpath(file_path, directory)
                file_data.append({
                    "path": rel_path,
                    "functions": count,
                    "loc": loc
                })
                
                # Group files by function count
                files_by_function_count[count].append(rel_path)
    
    return total_functions, file_function_counts, function_loc_data, file_data, files_by_function_count

def analyze_function_distribution(file_function_counts, function_loc_data, file_data, files_by_function_count):
    # Count occurrences of each function count
    count_distribution = Counter(file_function_counts)
    
    # Sort by function count
    sorted_counts = sorted(count_distribution.items())
    
    # Print distribution
    print("\nFunction count distribution:")
    for count, num_files in sorted_counts:
        print(f"{count} functions: {num_files} files")
    
    # Calculate average LOC for each function count
    avg_loc_by_function_count = {}
    for count, loc_list in function_loc_data.items():
        if loc_list:
            avg_loc_by_function_count[count] = sum(loc_list) / len(loc_list)
    
    # Print average LOC by function count
    print("\nAverage LOC by function count:")
    for count, avg_loc in sorted(avg_loc_by_function_count.items()):
        print(f"{count} functions: {avg_loc:.2f} average LOC")
    
    # Save results to text file
    with open('ruby_function_analysis.txt', 'w') as f:
        f.write("Function count distribution:\n")
        for count, num_files in sorted_counts:
            f.write(f"{count} functions: {num_files} files\n")
        
        f.write("\nAverage LOC by function count:\n")
        for count, loc_list in sorted(function_loc_data.items()):
            if loc_list:
                avg_loc = sum(loc_list) / len(loc_list)
                f.write(f"{count} functions: {avg_loc:.2f} average LOC\n")
        
        # Write files grouped by function count
        f.write("\nFiles by function count:\n")
        for count in sorted(files_by_function_count.keys()):
            f.write(f"\n{count} functions ({len(files_by_function_count[count])} files):\n")
            for file_path in sorted(files_by_function_count[count]):
                f.write(f"  {file_path}\n")
    
    # Save results to JSON file
    json_data = {
        "summary": {},
        "files": file_data
    }
    
    # Add summary data
    for count, num_files in sorted_counts:
        avg_loc = 0
        if count in function_loc_data and function_loc_data[count]:
            avg_loc = sum(function_loc_data[count]) / len(function_loc_data[count])
        
        json_data["summary"][str(count)] = {
            "num_files": num_files,
            "avg_loc": avg_loc,
            "files": files_by_function_count[count]
        }
    
    with open('ruby_function_analysis.json', 'w') as f:
        json.dump(json_data, f, indent=2)
    
    print("\nAnalysis saved to ruby_function_analysis.txt and ruby_function_analysis.json")
    
    return count_distribution

if __name__ == "__main__":
    total, file_counts, function_loc_data, file_data, files_by_function_count = count_functions_in_project(RUBY_PROJECT_DIR)
    print(f"\nTotal Ruby functions found: {total}")
    print(f"Total Ruby files analyzed: {len(file_counts)}")
    
    # Analyze and save the distribution
    analyze_function_distribution(file_counts, function_loc_data, file_data, files_by_function_count)

    
