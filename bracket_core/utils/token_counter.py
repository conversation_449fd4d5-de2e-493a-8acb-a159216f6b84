#!/usr/bin/env python
"""Count tokens and lines of code in Python files."""

import os
import argparse
import tiktoken
from pathlib import Path
from typing import List, Dict, Tuple

DEFAULT_ENCODING_NAME = "cl100k_base"

def count_tokens_and_loc_in_file(file_path: str, encoding_name: str = DEFAULT_ENCODING_NAME) -> Tuple[int, int]:
    """Count the number of tokens and lines of code in a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.splitlines()
            
            # Count non-empty lines (simple LOC metric)
            loc = sum(1 for line in lines if line.strip())
        
        encoding = tiktoken.get_encoding(encoding_name)
        token_count = len(encoding.encode(content))
        print(token_count)
        return token_count, loc
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return 0, 0

def count_tokens_and_loc_in_directory(directory: str, encoding_name: str = DEFAULT_ENCODING_NAME) -> Dict[str, Tuple[int, int]]:
    """
    Count tokens and lines of code in all Python files within a directory and its subdirectories.
    
    Returns a dictionary mapping file paths to tuples of (token_count, loc).
    """
    file_stats = {}
    
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                token_count, loc = count_tokens_and_loc_in_file(file_path, encoding_name)
                file_stats[file_path] = (token_count, loc)
    
    return file_stats


def count_tokens_and_loc_in_directory_no_tests(directory: str, encoding_name: str = DEFAULT_ENCODING_NAME) -> Dict[str, Tuple[int, int]]:
    """
    Count tokens and lines of code in all Python files within a directory and its subdirectories,
    excluding test-related files and directories.
    
    Returns a dictionary mapping file paths to tuples of (token_count, loc).
    """
    file_stats = {}
    
    for root, _, files in os.walk(directory):
        # Skip if "test" is in the directory path
        # if 'test' in root.lower():
        #     continue
            
        for file in files:
            if not file.endswith('.rb'):
                continue
                
            file_path = os.path.join(root, file)
            rel_path = os.path.relpath(file_path, directory)
            
            # Skip if "test" appears anywhere in the file path
            if 'test' in rel_path.lower():
                continue
                
            token_count, loc = count_tokens_and_loc_in_file(file_path, encoding_name)
            file_stats[file_path] = (token_count, loc)
    
    return file_stats

def main():
    parser = argparse.ArgumentParser(description='Count tokens and lines of code in Python files')
    parser.add_argument('directory', type=str, nargs='?', help='Directory to scan for Python files')
    parser.add_argument('--encoding', type=str, default=DEFAULT_ENCODING_NAME, 
                        help=f'Tiktoken encoding to use (default: {DEFAULT_ENCODING_NAME})')
    args = parser.parse_args()
    
    # Use provided directory or default to seaborn
    # directory = args.directory or "/Users/<USER>/work/startup/godzilla/test/seaborn"
    # directory = "/Users/<USER>/work/startup/godzilla/bracket/bracket/graphrag"
    # directory = "/Users/<USER>/work/startup/godzilla/test/requests/src"
    # directory = "/Users/<USER>/work/startup/godzilla/django/django"
    # directory = "/Users/<USER>/work/startup/godzilla/test/pytorch"
    # directory = "/Users/<USER>/work/startup/godzilla/test/pytorch/torch/"
    directory = "/Users/<USER>/work/startup/godzilla/test/gitlab/gitlab"
    encoding_name = args.encoding
    
    if not os.path.isdir(directory):
        print(f"Error: {directory} is not a valid directory")
        return
    
    print(f"Analyzing Python files in {directory} using {encoding_name} encoding...")
    # file_stats = count_tokens_and_loc_in_directory(directory, encoding_name)
    file_stats = count_tokens_and_loc_in_directory_no_tests(directory, encoding_name)
    
    # Calculate totals
    total_tokens = sum(stats[0] for stats in file_stats.values())
    total_loc = sum(stats[1] for stats in file_stats.values())
    total_files = len(file_stats)
    
    print(f"\nResults:")
    print(f"Total Python files: {total_files}")
    print(f"Total tokens: {total_tokens:,}")
    print(f"Total lines of code: {total_loc:,}")
    print(f"Average tokens per LOC: {total_tokens/total_loc:.2f}" if total_loc > 0 else "Average tokens per LOC: N/A")
    
    # Print top 10 files by token count
    if file_stats:
        print("\nTop 10 files by token count:")
        sorted_by_tokens = sorted(file_stats.items(), key=lambda x: x[1][0], reverse=True)
        for file_path, (token_count, loc) in sorted_by_tokens[:10]:
            rel_path = os.path.relpath(file_path, directory)
            print(f"{rel_path}: {token_count:,} tokens, {loc:,} LOC")
        
        print("\nTop 10 files by lines of code:")
        sorted_by_loc = sorted(file_stats.items(), key=lambda x: x[1][1], reverse=True)
        for file_path, (token_count, loc) in sorted_by_loc[:10]:
            rel_path = os.path.relpath(file_path, directory)
            print(f"{rel_path}: {loc:,} LOC, {token_count:,} tokens")

if __name__ == "__main__":
    # main()
    count_tokens_and_loc_in_file("/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/domain_taxonomy_improved.json")



"""
seaborn
total tokens: 485,384 | probably because a lot of test files
IRL KG tokens: 10,000
Mermaid: 




mem0/mem0
    Total Python files: 78
    Total tokens: 72,002
    IRL: 62K
    IRL JSON: 53K

    Optimized IRL JSON: 25K

graphrag/graphrag
    Total Python files: 339
    LOC: 25K
    Total tokens: 274,598
    IRL: 240K
    IRL JSON: 210K

    Optimized IRL JSON: 100K
    Very Highly Optimized IRL JSON: 65K

    Node based IRL YAML: 50K

    15 Mermaids: 15K tokens


    Need to make the fn description efficient and use half the tokens as it is currently

aider/aider
275K
KG Output(IRL Output Signified): 35K
Mermaid: 




"""