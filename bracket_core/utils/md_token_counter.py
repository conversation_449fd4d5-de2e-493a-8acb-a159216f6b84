#!/usr/bin/env python
"""Count tokens in markdown files in a directory."""

import os
import tiktoken
from pathlib import Path
from typing import Dict, <PERSON><PERSON>, List
import heapq

# Default encoding model
DEFAULT_ENCODING_NAME = "cl100k_base"

def num_tokens(text: str, encoding_name: str = DEFAULT_ENCODING_NAME) -> int:
    """Return the number of tokens in the given text."""
    token_encoder = tiktoken.get_encoding(encoding_name)
    return len(token_encoder.encode(text))

def count_tokens_in_md_files(directory: str, encoding_name: str = DEFAULT_ENCODING_NAME) -> Dict[str, int]:
    """
    Count tokens in all markdown files within a directory and its subdirectories.
    
    Returns a dictionary mapping file paths to token counts.
    """
    file_stats = {}
    
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.md'):
                # if "L4" in file or "L3" in file or "L2" in file:
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        token_count = num_tokens(content, encoding_name)
                        file_stats[file_path] = token_count
                    except Exception as e:
                        print(f"Error processing {file_path}: {e}")
    
    return file_stats

def main():
    # Hardcoded directory path - change this to your target directory
    # directory = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/setup_exp/data/bracket-taxonomy/domain_diagrams/diagrams_with_fn_info"
    # directory = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/beauty_of_life/data/django_unchained"
    # directory = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/improved_mermaid_outputs/diagrams_with_fn_info"
    directory = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/mermaid_outputs_json/django/diagrams_from_json"
    encoding_name = DEFAULT_ENCODING_NAME
    
    print(f"Analyzing markdown files in {directory} using {encoding_name} encoding...")
    file_stats = count_tokens_in_md_files(directory, encoding_name)
    
    # Calculate totals
    total_tokens = sum(file_stats.values())
    total_files = len(file_stats)
    
    print(f"\nResults:")
    print(f"Total markdown files: {total_files}")
    print(f"Total tokens: {total_tokens:,}")
    print(f"Average tokens per file: {total_tokens/total_files:.2f}" if total_files > 0 else "Average tokens per file: N/A")
    
    # Print top 5 files by token count
    if file_stats:
        print("\nTop 5 files by token count:")
        sorted_by_tokens = sorted(file_stats.items(), key=lambda x: x[1], reverse=True)
        for file_path, token_count in sorted_by_tokens[:5]:
            rel_path = os.path.relpath(file_path, directory)
            print(f"{rel_path}: {token_count:,} tokens")

def yaml_estimator():
    # yaml_file_path = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/exp_results/repomap/pytorch_full_bracket_irl_new1.json"
    yaml_file_path = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/outputs/datadog_agent/taxonomy/taxonomy.json"

    with open(yaml_file_path, 'r', encoding='utf-8') as f:
        yaml_str = str(f.read())

    yaml_str = yaml_str.replace("││", "")
    yaml_str = yaml_str.replace("││⋮...", "")
    yaml_str = yaml_str.replace("⋮...", "")    
    estimated_loc = num_tokens(yaml_str)
    print(estimated_loc)

if __name__ == "__main__":
    # yaml_estimator()
    main()
    