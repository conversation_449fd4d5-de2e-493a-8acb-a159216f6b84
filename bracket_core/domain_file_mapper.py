"""
Domain-to-File Mapper for Codebase

This module provides functionality to:
1. Read domain analysis YAML to get the hierarchical structure of domains
2. Read file-driven call graph YAML to get file information
3. Use an LLM to map files to top-level domains
4. Save the domain-to-file mappings as YAML

It can be used as:
1. A standalone script to process domain analysis and file call graph YAMLs
2. A module that can be integrated into the repository analysis flow
"""

import os
import yaml
import json
import logging
import argparse
import asyncio
import aiohttp
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field

# Import API key management
from bracket_core.llm.api_keys import get_openai_api_key, get_openrouter_api_key

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class StatusTracker:
    """Tracks the status of API requests."""
    time_of_last_rate_limit_error: float = 0
    num_tasks_started: int = 0
    num_tasks_in_progress: int = 0  # script ends when this reaches 0
    num_tasks_succeeded: int = 0
    num_tasks_failed: int = 0
    num_rate_limit_errors: int = 0
    num_api_errors: int = 0  # excluding rate limit errors, counted above
    num_other_errors: int = 0

@dataclass
class APIRequest:
    """Stores an API request's inputs, outputs, and other metadata. Contains a method to make an API call."""
    task_id: int
    batch_files: List[Dict[str, Any]]
    request_json: dict
    attempts_left: int
    result: Dict[str, List[str]] = field(default_factory=dict)
    error: Any = None

    async def call_api(
        self,
        session: aiohttp.ClientSession,
        request_url: str,
        request_header: dict,
        retry_queue: asyncio.Queue,
        status_tracker: StatusTracker,
        results: Dict[str, List[str]],
    ):
        """Calls the OpenAI API and processes the response."""
        logging.info(f"Starting request #{self.task_id} for batch of {len(self.batch_files)} files")

        try:
            # Make the API request
            async with session.post(
                url=request_url,
                headers=request_header,
                json=self.request_json
            ) as response:
                response_data = await response.json()

            if response.status != 200 or "error" in response_data:
                error_text = response_data.get("error", {}).get("message", str(response_data))
                logging.warning(f"Request {self.task_id} failed with error: {error_text}")

                if "error" in response_data and "rate limit" in response_data["error"].get("message", "").lower():
                    status_tracker.time_of_last_rate_limit_error = time.time()
                    status_tracker.num_rate_limit_errors += 1
                else:
                    status_tracker.num_api_errors += 1

                self.error = error_text

                if self.attempts_left > 0:
                    retry_queue.put_nowait(self)
                else:
                    logging.error(f"Request {self.task_id} failed after all attempts. Error: {error_text}")
                    status_tracker.num_tasks_failed += 1
                    status_tracker.num_tasks_in_progress -= 1
            else:
                # Extract the response content
                content = response_data["choices"][0]["message"]["content"]

                # Parse the response to get the domain-file mappings
                try:
                    # Find JSON content between triple backticks
                    json_content = content
                    if "```json" in content:
                        json_content = content.split("```json")[1].split("```")[0].strip()
                    elif "```" in content:
                        json_content = content.split("```")[1].split("```")[0].strip()

                    # Parse the JSON content
                    batch_mappings = json.loads(json_content)

                    # Update the results
                    for domain, files in batch_mappings.items():
                        if domain not in results:
                            results[domain] = []
                        results[domain].extend(files)

                    logging.info(f"Successfully processed batch {self.task_id} with {len(batch_mappings)} domains")
                    status_tracker.num_tasks_succeeded += 1
                    status_tracker.num_tasks_in_progress -= 1
                except Exception as e:
                    logging.error(f"Error parsing API response for batch {self.task_id}: {e}")
                    self.error = f"Error parsing API response: {e}"

                    if self.attempts_left > 0:
                        retry_queue.put_nowait(self)
                    else:
                        status_tracker.num_tasks_failed += 1
                        status_tracker.num_tasks_in_progress -= 1

        except Exception as e:
            logging.warning(f"Request {self.task_id} failed with Exception {e}")
            status_tracker.num_other_errors += 1
            self.error = str(e)

            if self.attempts_left > 0:
                retry_queue.put_nowait(self)
            else:
                logging.error(f"Request {self.task_id} failed after all attempts. Error: {e}")
                status_tracker.num_tasks_failed += 1
                status_tracker.num_tasks_in_progress -= 1

@dataclass
class DomainFileMapperResult:
    """Result of mapping files to domains."""
    success: bool = True
    error_message: str = ""
    domain_file_mappings: Dict[str, List[str]] = field(default_factory=dict)
    output_path: Optional[str] = None

class DomainFileMapper:
    """
    Maps files to top-level domains.

    This class reads domain analysis YAML and file-driven call graph YAML,
    and uses an LLM to map files to top-level domains.
    """

    def __init__(
        self,
        domain_analysis_yaml_path: str,
        file_call_graph_yaml_path: str,
        output_path: str,
        api_key: Optional[str] = None,
        model: str = "gpt-4o-mini",
        max_requests_per_minute: float = 50,
        max_tokens_per_minute: float = 100000,
        temperature: float = 0.1,
        use_openrouter: bool = False,
        openrouter_base_url: str = "https://openrouter.ai/api/v1",
        go_gemini: bool = False,
    ):
        """
        Initialize the domain file mapper.

        Args:
            domain_analysis_yaml_path: Path to the domain analysis YAML file
            file_call_graph_yaml_path: Path to the file-driven call graph YAML file
            output_path: Path to save the domain-to-file mappings YAML
            api_key: API key (OpenAI or OpenRouter depending on use_openrouter)
            model: Model to use (OpenAI or OpenRouter model ID)
            max_requests_per_minute: Rate limit for API requests
            max_tokens_per_minute: Token rate limit for API
            temperature: Temperature setting for the model (lower = more deterministic)
            use_openrouter: Whether to use OpenRouter instead of OpenAI
            openrouter_base_url: Base URL for OpenRouter API
        """
        self.domain_analysis_yaml_path = domain_analysis_yaml_path
        self.file_call_graph_yaml_path = file_call_graph_yaml_path
        self.output_path = output_path
        # Get the appropriate API key based on the service being used
        if use_openrouter:
            self.api_key = get_openrouter_api_key(provided_key=api_key)
        else:
            self.api_key = get_openai_api_key(provided_key=api_key)

        self.model = model
        self.max_requests_per_minute = max_requests_per_minute
        self.max_tokens_per_minute = max_tokens_per_minute
        self.temperature = temperature
        self.use_openrouter = use_openrouter
        self.openrouter_base_url = openrouter_base_url
        self.status_tracker = StatusTracker()
        self.go_gemini = go_gemini

    def read_domain_yaml(self) -> Dict[str, Any]:
        """
        Read the domain analysis YAML file.

        Returns:
            Dictionary containing domain analysis data
        """
        logger.info(f"Reading domain analysis YAML file: {self.domain_analysis_yaml_path}")
        try:
            with open(self.domain_analysis_yaml_path, 'r') as f:
                domain_data = yaml.safe_load(f)
            logger.info(f"Read domain analysis YAML file successfully")
            return domain_data
        except Exception as e:
            logger.error(f"Error reading domain analysis YAML file: {e}")
            raise

    def read_file_call_graph_yaml(self) -> Dict[str, Any]:
        """
        Read the file-driven call graph YAML file.

        Returns:
            Dictionary containing file graph data
        """
        logger.info(f"Reading file-driven call graph YAML file: {self.file_call_graph_yaml_path}")
        try:
            with open(self.file_call_graph_yaml_path, 'r') as f:
                file_graph = yaml.safe_load(f)
            logger.info(f"Read file-driven call graph YAML file successfully")
            return file_graph
        except Exception as e:
            logger.error(f"Error reading file-driven call graph YAML file: {e}")
            raise

    def extract_top_level_domains(self, domain_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extract top-level domains from domain analysis data.

        Args:
            domain_data: Dictionary containing domain analysis data

        Returns:
            List of top-level domains with their descriptions
        """
        logger.info("Extracting top-level domains")

        top_domains = []

        # Extract areas (top-level domains)
        areas = domain_data.get('areas', [])

        for area in areas:
            domain_name = area.get('name', '')

            # Create a description by traversing the hierarchy
            description = self._generate_domain_description(area)

            top_domains.append({
                'name': domain_name,
                'description': description
            })

        logger.info(f"Extracted {len(top_domains)} top-level domains")
        return top_domains

    def _generate_domain_description(self, domain: Dict[str, Any], depth: int = 0) -> str:
        """
        Generate a detailed description for a domain by traversing its hierarchy.

        Args:
            domain: Dictionary containing domain data
            depth: Current depth in the hierarchy

        Returns:
            Detailed description of the domain
        """
        description = f"{domain.get('name', 'Unknown Domain')}"

        # Add subdomain information
        subareas = domain.get('subareas', [])
        if subareas:
            description += " with subdomains: "
            subdomain_names = [subarea.get('name', 'Unknown') for subarea in subareas]
            description += ", ".join(subdomain_names)

            # For top-level domains, add more detailed subdomain descriptions
            if depth == 0:
                description += "\n\nSubdomain details:\n"
                for subarea in subareas:
                    sub_desc = self._generate_domain_description(subarea, depth + 1)
                    description += f"- {sub_desc}\n"

        return description

    async def map_files_to_domains(self) -> DomainFileMapperResult:
        """
        Map files to top-level domains using LLM.

        If go_gemini is True, uses OpenRouter with Gemini model for top-level domains
        to handle larger context windows. This is useful for larger codebases.

        Returns:
            DomainFileMapperResult containing the domain-to-file mappings
        """
        try:
            self.go_gemini = True
            # Read domain and file graph YAMLs
            domain_data = self.read_domain_yaml()
            file_graph = self.read_file_call_graph_yaml()

            # Extract top-level domains
            top_domains = self.extract_top_level_domains(domain_data)

            # If go_gemini is True, temporarily set use_openrouter to True and save the original model
            original_use_openrouter = self.use_openrouter
            original_model = self.model

            if self.go_gemini:
                logger.info("Using OpenRouter with Gemini model for top-level domain file allocation")
                self.use_openrouter = True
                self.model = "google/gemini-2.5-pro-preview"  # Use Gemini model with larger context window

            # Map files to domains
            # gonna happen with gemini 2.5
            domain_file_mappings = await self._classify_files_to_domains(top_domains, file_graph)

            # Restore original settings
            if self.go_gemini:
                self.use_openrouter = original_use_openrouter
                self.model = original_model

            # Save the mappings to YAML
            with open(self.output_path, 'w') as f:
                yaml.dump(domain_file_mappings, f, default_flow_style=False)

            logger.info(f"Domain-to-file mappings saved to: {self.output_path}")

            return DomainFileMapperResult(
                success=True,
                domain_file_mappings=domain_file_mappings,
                output_path=self.output_path
            )

        except Exception as e:
            logger.error(f"Error mapping files to domains: {e}")
            import traceback
            logger.error(traceback.format_exc())

            return DomainFileMapperResult(
                success=False,
                error_message=f"Failed to map files to domains: {str(e)}"
            )

    async def _classify_files_to_domains(
        self,
        top_domains: List[Dict[str, Any]],
        file_graph: Dict[str, Any]
    ) -> Dict[str, List[str]]:
        """
        Classify files into top-level domains using LLM with non-blocking parallel processing.

        Args:
            top_domains: List of top-level domains with descriptions
            file_graph: Dictionary containing file graph data

        Returns:
            Dictionary mapping domain names to lists of file paths
        """
        logger.info("Classifying files into top-level domains with non-blocking parallel processing")

        # Prepare domain descriptions for the LLM
        domain_descriptions = "\n\n".join([
            f"Domain: {domain['name']}\nDescription: {domain['description']}"
            for domain in top_domains
        ])

        # Prepare file information for the LLM
        file_info = []
        for file_path, file_data in file_graph.items():
            functions = file_data.get('functions', [])
            function_summaries = []

            # for func in functions[:5]:  # Limit to first 5 functions to reduce token usage
            #     name = func.get('name', '')
            #     description = func.get('description', '')
            #     function_summaries.append(f"{name}: {description}")

            for func in functions:
                name = func.get('name', '')
                description = func.get('description', '')
                function_summaries.append(f"{name}: {description}")

            file_summary = {
                'file_path': file_path,
                # 'function_count': len(functions),
                'functions': function_summaries
            }

            # Add external calls if available
            # if 'calls' in functions and functions[0].get('calls'):
            #     file_summary['external_calls'] = functions[0].get('calls', [])[:5]  # Limit to first 5 external calls

            file_info.append(file_summary)

        # Constants for rate limiting and batching
        if self.go_gemini:
            batch_size = 10000  # Process files in batches of 10000
        else:
            batch_size = 100  # Process files in batches of 10000
        seconds_to_pause_after_rate_limit_error = 15
        seconds_to_sleep_each_loop = 0.001  # 1 ms limits max throughput to 1,000 requests per second

        # Initialize result
        domain_file_mappings = {}

        # Initialize trackers
        queue_of_requests_to_retry = asyncio.Queue()
        status_tracker = self.status_tracker  # Use the instance's status tracker

        # Create batches of files
        file_batches = [file_info[i:i + batch_size] for i in range(0, len(file_info), batch_size)]
        logger.info(f"Created {len(file_batches)} batches of files (batch size: {batch_size})")

        # Create API requests for each batch
        api_requests = []
        for i, batch in enumerate(file_batches):
            # Create the API request for this batch
            request_json = {
                "model": self.model,
                "messages": [
                    {
                        "role": "system",
                        "content": """You are an expert code analyzer that maps source code files to their appropriate domains based on their content and purpose.

Your task is to analyze a list of files and assign each file to the most appropriate domain from a provided list of domains.

Guidelines:
1. Each file should be assigned to exactly one domain that best represents its primary purpose
2. Use the file path, function samples, and external calls to determine the domain
3. Return your analysis as a valid JSON object where keys are domain names and values are lists of file paths
4. Include ALL files in your classification - every file must be assigned to a domain
5. If a file doesn't clearly fit any domain, assign it to the domain that seems most appropriate

Example output format:
```json
{
  "Domain Name 1": [
    "path/to/file1.py",
    "path/to/file2.py"
  ],
  "Domain Name 2": [
    "path/to/file3.py",
    "path/to/file4.py"
  ]
}
```"""
                    },
                    {
                        "role": "user",
                        "content": f"""Please classify the following files into the most appropriate domains.

DOMAINS:
{domain_descriptions}

FILES:
{json.dumps(batch, indent=2)}

Return your classification as a JSON object where keys are domain names and values are lists of file paths."""
                    }
                ]
            }

            # Create an APIRequest object
            api_request = APIRequest(
                task_id=i,
                batch_files=batch,
                request_json=request_json,
                attempts_left=3  # Allow up to 3 attempts per request
            )
            api_requests.append(api_request)

        # Initialize counters for rate limiting
        available_request_capacity = self.max_requests_per_minute
        available_token_capacity = self.max_tokens_per_minute
        last_update_time = time.time()
        next_request_index = 0
        next_request = None

        # Set up API endpoint based on whether we're using OpenRouter or OpenAI
        if self.use_openrouter:
            # For regular domain file mapping, use the OpenRouter API directly
            # We don't use the OpenRouterClient here to maintain compatibility with the existing code
            request_url = f"{self.openrouter_base_url}/chat/completions"
            request_header = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}",
                "HTTP-Referer": "https://github.com/bracket-ai/bracket",
                "X-Title": "Bracket Code"
            }

            # If go_gemini is True, we're using Gemini model for top-level domains
            if self.go_gemini:
                logger.info(f"Using OpenRouter with model: {self.model} for top-level domain file allocation")
            else:
                # For regular domain file mapping, we'll use gpt-4o-mini by default
                # This is different from hierarchical domain mapping which uses Gemini
                logger.info(f"Using OpenRouter with model: {self.model}")
        else:
            request_url = "https://api.openai.com/v1/chat/completions"
            request_header = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }
            logger.info(f"Using OpenAI with model: {self.model}")

        # Process requests with rate limiting
        status_tracker.num_tasks_started = len(api_requests)
        status_tracker.num_tasks_in_progress = len(api_requests)

        # Process requests with rate limiting
        async with aiohttp.ClientSession() as session:
            while status_tracker.num_tasks_in_progress > 0:
                # Get next request (if one is not already waiting for capacity)
                if next_request is None:
                    if not queue_of_requests_to_retry.empty():
                        next_request = await queue_of_requests_to_retry.get()
                        next_request.attempts_left -= 1
                        logger.debug(f"Retrying request {next_request.task_id} for batch of {len(next_request.batch_files)} files")
                    elif next_request_index < len(api_requests):
                        next_request = api_requests[next_request_index]
                        next_request_index += 1
                        logger.debug(f"Processing request {next_request.task_id} for batch of {len(next_request.batch_files)} files")

                # Update rate limit capacity
                current_time = time.time()
                time_elapsed = current_time - last_update_time
                available_request_capacity = min(
                    available_request_capacity + (self.max_requests_per_minute * time_elapsed / 60),
                    self.max_requests_per_minute
                )
                available_token_capacity = min(
                    available_token_capacity + (self.max_tokens_per_minute * time_elapsed / 60),
                    self.max_tokens_per_minute
                )
                last_update_time = current_time

                # If enough capacity available and we have a request, call API
                if next_request and available_request_capacity >= 1:
                    # Estimate token consumption (simplified)
                    estimated_tokens = 1000  # Simplified estimate

                    if available_token_capacity >= estimated_tokens:
                        # Update counters
                        available_request_capacity -= 1
                        available_token_capacity -= estimated_tokens

                        # Call API without waiting for response
                        asyncio.create_task(
                            next_request.call_api(
                                session=session,
                                request_url=request_url,
                                request_header=request_header,
                                retry_queue=queue_of_requests_to_retry,
                                status_tracker=status_tracker,
                                results=domain_file_mappings,
                            )
                        )
                        next_request = None  # Reset next_request to get a new one

                # Sleep a bit to avoid busy-waiting
                await asyncio.sleep(seconds_to_sleep_each_loop)

        # After finishing, log final status
        logger.info(f"Parallel processing complete. Processed {len(file_batches)} batches of files.")
        if status_tracker.num_tasks_failed > 0:
            logger.warning(f"{status_tracker.num_tasks_failed} / {status_tracker.num_tasks_started} requests failed.")

        if status_tracker.num_rate_limit_errors > 0:
            logger.warning(f"{status_tracker.num_rate_limit_errors} rate limit errors received. Consider running at a lower rate.")

        logger.info(f"Successfully classified files into {len(domain_file_mappings)} domains")
        return domain_file_mappings

    # Note: The _call_openai_api method is no longer used directly.
    # API calls are now handled by the APIRequest class with non-blocking parallel processing.

class DomainFileMapperIntegration:
    """
    Integration class for adding domain-to-file mapping to the repository analysis flow.

    This class provides methods that can be called from the repository analysis flow
    to add domain-to-file mapping as an additional step.
    """

    @staticmethod
    async def map_files_to_domains(
        domain_analysis_yaml_path: str,
        file_call_graph_yaml_path: str,
        output_path: str,
        api_key: Optional[str] = None,
        model: str = "gpt-4o-mini",
        max_requests_per_minute: float = 2000,  # Increased for optimized parallel processing
        max_tokens_per_minute: float = 1000000,  # Increased for optimized parallel processing
        use_openrouter: bool = False,
        openrouter_base_url: str = "https://openrouter.ai/api/v1",
        go_gemini: bool = False,
    ) -> bool:
        # For regular domain file mapping, we'll use gpt-4o-mini by default
        # This is different from hierarchical domain mapping which uses Gemini
        """
        Map files to domains from domain analysis and file graph YAMLs.

        Args:
            domain_analysis_yaml_path: Path to the domain analysis YAML file
            file_call_graph_yaml_path: Path to the file-driven call graph YAML file
            output_path: Path to save the domain-to-file mappings YAML
            api_key: OpenAI API key (if None, will try to get from environment)
            model: OpenAI model to use
            max_requests_per_minute: Rate limit for API requests
            max_tokens_per_minute: Token rate limit for API

        Returns:
            True if mapping was successful, False otherwise
        """
        try:
            mapper = DomainFileMapper(
                domain_analysis_yaml_path=domain_analysis_yaml_path,
                file_call_graph_yaml_path=file_call_graph_yaml_path,
                output_path=output_path,
                api_key=api_key,
                model=model,
                max_requests_per_minute=max_requests_per_minute,
                max_tokens_per_minute=max_tokens_per_minute,
                use_openrouter=use_openrouter,
                openrouter_base_url=openrouter_base_url,
                go_gemini=go_gemini,
            )

            result = await mapper.map_files_to_domains()
            return result.success

        except Exception as e:
            logger.error(f"Error in domain-to-file mapping: {e}")
            return False

async def main():
    """Main entry point for the domain file mapper."""
    parser = argparse.ArgumentParser(description="Map files to top-level domains")
    parser.add_argument("--domain-yaml", required=True, help="Path to the domain analysis YAML file")
    parser.add_argument("--file-graph-yaml", required=True, help="Path to the file-driven call graph YAML file")
    parser.add_argument("--output", required=True, help="Path to save the domain-to-file mappings YAML")
    parser.add_argument("--api-key", help="OpenAI API key (if not provided, will try to get from environment)")
    parser.add_argument("--model", default="gpt-4o-mini", help="OpenAI model to use")
    parser.add_argument("--requests-per-minute", type=float, default=2000, help="Rate limit for API requests")
    parser.add_argument("--tokens-per-minute", type=float, default=1000000, help="Token rate limit for API")
    parser.add_argument("--temperature", type=float, default=0.1, help="Temperature setting for the model")

    args = parser.parse_args()

    try:
        mapper = DomainFileMapper(
            domain_analysis_yaml_path=args.domain_yaml,
            file_call_graph_yaml_path=args.file_call_graph_yaml,
            output_path=args.output,
            api_key=args.api_key,
            model=args.model,
            max_requests_per_minute=args.requests_per_minute,
            max_tokens_per_minute=args.tokens_per_minute,
            temperature=args.temperature,
        )

        result = await mapper.map_files_to_domains()

        if result.success:
            logger.info("Domain-to-file mapping completed successfully")
            return 0
        else:
            logger.error(f"Domain-to-file mapping failed: {result.error_message}")
            return 1

    except Exception as e:
        logger.error(f"Error in domain-to-file mapping: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
