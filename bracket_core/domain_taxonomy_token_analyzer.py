#!/usr/bin/env python
"""
Domain Taxonomy Token Analyzer

This script analyzes domain taxonomy JSON files generated by bracket_core/generate_domain_taxonomy.py.
It identifies leaf components (those without children), combines their content, and counts the tokens.

Usage:
    python -m bracket_core.domain_taxonomy_token_analyzer --taxonomy-json <path> [--output-dir <dir>] [--model <model_name>]
"""

import os
import json
import argparse
import logging
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, field
import pandas as pd
import time

# Import token counting utility
try:
    from bracket_core.llm.tokens import num_tokens_from_string
except ImportError:
    # Fallback to tiktoken if bracket_core.llm.tokens is not available
    import tiktoken
    
    def num_tokens_from_string(string: str, model_name: str = "gpt-4o-mini") -> int:
        """Returns the number of tokens in a text string."""
        encoding = tiktoken.encoding_for_model(model_name)
        return len(encoding.encode(string))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class LeafComponent:
    """Represents a leaf component in the domain taxonomy."""
    name: str
    full_path: str
    functions: List[str] = field(default_factory=list)
    diagram: Optional[str] = None
    diagram_path: Optional[str] = None
    token_count: int = 0

@dataclass
class AnalysisResult:
    """Result of analyzing domain taxonomy."""
    total_leaf_components: int = 0
    total_tokens: int = 0
    average_tokens_per_component: float = 0
    max_tokens: int = 0
    max_tokens_component: str = ""
    min_tokens: int = 0
    min_tokens_component: str = ""
    components_with_diagrams: int = 0
    components_without_diagrams: int = 0
    leaf_components: List[LeafComponent] = field(default_factory=list)

class DomainTaxonomyTokenAnalyzer:
    """
    Analyzes domain taxonomy JSON files to identify leaf components and count tokens.
    """

    def __init__(
        self,
        taxonomy_json_path: str,
        output_dir: Optional[str] = None,
        model_name: str = "gpt-4o-mini"
    ):
        """
        Initialize the domain taxonomy token analyzer.

        Args:
            taxonomy_json_path: Path to the domain taxonomy JSON file
            output_dir: Directory to save analysis results (optional)
            model_name: Model name to use for token counting
        """
        self.taxonomy_json_path = taxonomy_json_path
        self.output_dir = output_dir or os.path.dirname(taxonomy_json_path)
        self.model_name = model_name
        
        # Create output directory if it doesn't exist
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Initialize data structures
        self.taxonomy_data = {}
        self.leaf_components: List[LeafComponent] = []

    def read_taxonomy_json(self) -> Dict[str, Any]:
        """
        Read the domain taxonomy JSON file.

        Returns:
            Dictionary containing domain taxonomy data
        """
        logger.info(f"Reading domain taxonomy JSON file: {self.taxonomy_json_path}")
        try:
            with open(self.taxonomy_json_path, 'r') as f:
                taxonomy_data = json.load(f)
            return taxonomy_data
        except Exception as e:
            logger.error(f"Error reading domain taxonomy JSON file: {e}")
            raise

    def find_leaf_components(self, node: Dict[str, Any], path: List[str] = None) -> None:
        """
        Recursively find leaf components in the taxonomy tree.

        Args:
            node: Current node in the taxonomy tree
            path: Current path in the taxonomy tree
        """
        if path is None:
            path = []
        
        name = node.get('name', 'Unknown')
        current_path = path + [name]
        full_path = " -> ".join(current_path)
        
        # Check if this is a leaf node (no children)
        if 'children' not in node or not node['children']:
            # Create a leaf component
            leaf = LeafComponent(
                name=name,
                full_path=full_path,
                functions=node.get('functions', []),
                diagram=node.get('diagram'),
                diagram_path=node.get('diagram_path')
            )
            
            self.leaf_components.append(leaf)
        else:
            # Process children recursively
            for child in node['children']:
                self.find_leaf_components(child, current_path)

    def calculate_tokens(self) -> None:
        """
        Calculate token counts for each leaf component.
        """
        logger.info(f"Calculating token counts for {len(self.leaf_components)} leaf components")
        
        for leaf in self.leaf_components:
            # Combine all content for this leaf component
            content = f"Domain: {leaf.full_path}\n\n"
            
            # Add functions
            if leaf.functions:
                content += "Functions:\n"
                for func in leaf.functions:
                    content += f"- {func}\n"
                content += "\n"
            
            # Add diagram
            if leaf.diagram:
                content += "Diagram:\n"
                content += leaf.diagram
            
            # Calculate tokens
            token_count = num_tokens_from_string(content, self.model_name)
            leaf.token_count = token_count

    def analyze_taxonomy(self) -> AnalysisResult:
        """
        Analyze the domain taxonomy to find leaf components and calculate tokens.

        Returns:
            AnalysisResult containing the analysis results
        """
        logger.info("Analyzing domain taxonomy")
        
        # Read taxonomy JSON
        self.taxonomy_data = self.read_taxonomy_json()
        
        # Find leaf components
        self.find_leaf_components(self.taxonomy_data)
        logger.info(f"Found {len(self.leaf_components)} leaf components")
        
        # Calculate tokens for each leaf component
        self.calculate_tokens()
        
        # Calculate statistics
        total_tokens = sum(leaf.token_count for leaf in self.leaf_components)
        avg_tokens = total_tokens / len(self.leaf_components) if self.leaf_components else 0
        
        max_tokens = 0
        max_tokens_component = ""
        min_tokens = float('inf')
        min_tokens_component = ""
        
        components_with_diagrams = 0
        components_without_diagrams = 0
        
        for leaf in self.leaf_components:
            if leaf.token_count > max_tokens:
                max_tokens = leaf.token_count
                max_tokens_component = leaf.full_path
            
            if leaf.token_count < min_tokens:
                min_tokens = leaf.token_count
                min_tokens_component = leaf.full_path
            
            if leaf.diagram:
                components_with_diagrams += 1
            else:
                components_without_diagrams += 1
        
        # Create result
        result = AnalysisResult(
            total_leaf_components=len(self.leaf_components),
            total_tokens=total_tokens,
            average_tokens_per_component=avg_tokens,
            max_tokens=max_tokens,
            max_tokens_component=max_tokens_component,
            min_tokens=min_tokens if min_tokens != float('inf') else 0,
            min_tokens_component=min_tokens_component,
            components_with_diagrams=components_with_diagrams,
            components_without_diagrams=components_without_diagrams,
            leaf_components=self.leaf_components
        )
        
        return result

    def save_analysis_results(self, result: AnalysisResult) -> Tuple[str, str]:
        """
        Save analysis results to files.

        Args:
            result: AnalysisResult containing the analysis results

        Returns:
            Tuple of (csv_path, summary_path)
        """
        logger.info("Saving analysis results")
        
        # Create a timestamp for the output files
        timestamp = time.strftime("%Y%m%d-%H%M%S")
        
        # Save leaf components to CSV
        csv_path = os.path.join(self.output_dir, f"leaf_components_tokens_{timestamp}.csv")
        df = pd.DataFrame([
            {
                "name": leaf.name,
                "full_path": leaf.full_path,
                "functions_count": len(leaf.functions),
                "has_diagram": leaf.diagram is not None,
                "token_count": leaf.token_count
            }
            for leaf in result.leaf_components
        ])
        df.sort_values("token_count", ascending=False, inplace=True)
        df.to_csv(csv_path, index=False)
        logger.info(f"Saved leaf components to {csv_path}")
        
        # Save summary to text file
        summary_path = os.path.join(self.output_dir, f"token_analysis_summary_{timestamp}.txt")
        with open(summary_path, 'w') as f:
            f.write(f"Domain Taxonomy Token Analysis Summary\n")
            f.write(f"=====================================\n\n")
            f.write(f"Total leaf components: {result.total_leaf_components}\n")
            f.write(f"Total tokens: {result.total_tokens:,}\n")
            f.write(f"Average tokens per component: {result.average_tokens_per_component:.2f}\n")
            f.write(f"Maximum tokens: {result.max_tokens:,} (Component: {result.max_tokens_component})\n")
            f.write(f"Minimum tokens: {result.min_tokens:,} (Component: {result.min_tokens_component})\n")
            f.write(f"Components with diagrams: {result.components_with_diagrams}\n")
            f.write(f"Components without diagrams: {result.components_without_diagrams}\n\n")
            
            f.write(f"Files generated:\n")
            f.write(f"- Leaf components CSV: {os.path.basename(csv_path)}\n")
        
        logger.info(f"Saved summary to {summary_path}")
        return csv_path, summary_path

    def run_analysis(self) -> Tuple[AnalysisResult, str, str]:
        """
        Run the complete analysis workflow.

        Returns:
            Tuple of (AnalysisResult, csv_path, summary_path)
        """
        # Analyze taxonomy
        result = self.analyze_taxonomy()
        
        # Save results
        csv_path, summary_path = self.save_analysis_results(result)
        
        return result, csv_path, summary_path

def main():
    """Main entry point for the domain taxonomy token analyzer."""
    parser = argparse.ArgumentParser(description="Analyze domain taxonomy JSON files for token usage")
    parser.add_argument("--taxonomy-json", required=True, help="Path to the domain taxonomy JSON file")
    parser.add_argument("--output-dir", help="Directory to save analysis results (optional)")
    parser.add_argument("--model", default="gpt-4o-mini", help="Model name to use for token counting")
    
    args = parser.parse_args()
    
    try:
        # Create analyzer
        analyzer = DomainTaxonomyTokenAnalyzer(
            taxonomy_json_path=args.taxonomy_json,
            output_dir=args.output_dir,
            model_name=args.model
        )
        
        # Run analysis
        start_time = time.time()
        result, csv_path, summary_path = analyzer.run_analysis()
        end_time = time.time()
        
        # Print summary
        print(f"\nAnalysis complete in {end_time - start_time:.2f} seconds!")
        print(f"Summary saved to: {summary_path}")
        print(f"Detailed results saved to: {csv_path}")
        
        print(f"\nKey Statistics:")
        print(f"  Total leaf components: {result.total_leaf_components}")
        print(f"  Total tokens: {result.total_tokens:,}")
        print(f"  Average tokens per component: {result.average_tokens_per_component:.2f}")
        print(f"  Maximum tokens: {result.max_tokens:,} (Component: {result.max_tokens_component})")
        print(f"  Components with diagrams: {result.components_with_diagrams}")
        print(f"  Components without diagrams: {result.components_without_diagrams}")
        
        return 0
        
    except Exception as e:
        logger.error(f"Error analyzing domain taxonomy: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    exit(main())
