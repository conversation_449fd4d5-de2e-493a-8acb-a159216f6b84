"""
API REQUEST PARALLEL PROCESSOR

Using the OpenAI API to process lots of text quickly takes some care.
If you trickle in a million API requests one by one, they'll take days to complete.
If you flood a million API requests in parallel, they'll exceed the rate limits and fail with errors.
To maximize throughput, parallel requests need to be throttled to stay under rate limits.

This script parallelizes requests to the OpenAI API while throttling to stay under rate limits.

Features:
- Streams requests from file, to avoid running out of memory for giant jobs
- Makes requests concurrently, to maximize throughput
- Throttles request and token usage, to stay under rate limits
- Retries failed requests up to {max_attempts} times, to avoid missing data
- Logs errors, to diagnose problems with requests

Example command to call script:
```
python examples/api_request_parallel_processor.py \
  --requests_filepath examples/data/example_requests_to_parallel_process.jsonl \
  --save_filepath examples/data/example_requests_to_parallel_process_results.jsonl \
  --request_url https://api.openai.com/v1/embeddings \
  --max_requests_per_minute 1500 \
  --max_tokens_per_minute 6250000 \
  --token_encoding_name cl100k_base \
  --max_attempts 5 \
  --logging_level 20
```

Inputs:
- requests_filepath : str
    - path to the file containing the requests to be processed
    - file should be a jsonl file, where each line is a json object with API parameters and an optional metadata field
    - e.g., {"model": "text-embedding-3-small", "input": "embed me", "metadata": {"row_id": 1}}
    - as with all jsonl files, take care that newlines in the content are properly escaped (json.dumps does this automatically)
    - an example file is provided at examples/data/example_requests_to_parallel_process.jsonl
    - the code to generate the example file is appended to the bottom of this script
- save_filepath : str, optional
    - path to the file where the results will be saved
    - file will be a jsonl file, where each line is an array with the original request plus the API response
    - e.g., [{"model": "text-embedding-3-small", "input": "embed me"}, {...}]
    - if omitted, results will be saved to {requests_filename}_results.jsonl
- request_url : str, optional
    - URL of the API endpoint to call
    - if omitted, will default to "https://api.openai.com/v1/embeddings"
- api_key : str, optional
    - API key to use
    - if omitted, the script will attempt to read it from an environment variable {os.getenv("OPENAI_API_KEY")}
- max_requests_per_minute : float, optional
    - target number of requests to make per minute (will make less if limited by tokens)
    - leave headroom by setting this to 50% or 75% of your limit
    - if requests are limiting you, try batching multiple embeddings or completions into one request
    - if omitted, will default to 1,500
- max_tokens_per_minute : float, optional
    - target number of tokens to use per minute (will use less if limited by requests)
    - leave headroom by setting this to 50% or 75% of your limit
- token_encoding_name : str, optional
    - name of the token encoding used, as defined in the `tiktoken` package
    - if omitted, will default to "cl100k_base" (used by `text-embedding-3-small`)
- max_attempts : int, optional
    - number of times to retry a failed request before giving up
    - if omitted, will default to 5
- logging_level : int, optional
    - level of logging to use; higher numbers will log fewer messages
    - 40 = ERROR; will log only when requests fail after all retries
    - 30 = WARNING; will log when requests his rate limits or other errors
    - 20 = INFO; will log when requests start and the status at finish
    - 10 = DEBUG; will log various things as the loop runs to see when they occur
    - if omitted, will default to 20 (INFO).

The script is structured as follows:
    - Imports
    - Define main()
        - Initialize things
        - In main loop:
            - Get next request if one is not already waiting for capacity
            - Update available token & request capacity
            - If enough capacity available, call API
            - The loop pauses if a rate limit error is hit
            - The loop breaks when no tasks remain
    - Define dataclasses
        - StatusTracker (stores script metadata counters; only one instance is created)
        - APIRequest (stores API inputs, outputs, metadata; one method to call API)
    - Define functions
        - api_endpoint_from_url (extracts API endpoint from request URL)
        - append_to_jsonl (writes to results file)
        - num_tokens_consumed_from_request (bigger function to infer token usage from request)
        - task_id_generator_function (yields 0, 1, 2, ...)
    - Run main()
"""

# imports
import aiohttp  # for making API calls concurrently
import argparse  # for running script from command line
import asyncio  # for running API calls concurrently
import json  # for saving results to a jsonl file
import logging  # for logging rate limit warnings and other messages
import os  # for reading API key
import re  # for matching endpoint from request URL
import tiktoken  # for counting tokens
import time  # for sleeping after rate limit is hit
from dataclasses import (
    dataclass,
    field,
)  # for storing API inputs, outputs, and metadata
from pydantic import BaseModel, Field
from typing import List
import pandas as pd

class EntityExtraction(BaseModel):
    """A model for entity extraction response."""

    people: List[str] = Field(description="List of people mentioned in the text")
    locations: List[str] = Field(description="List of locations mentioned in the text")
    dates: List[str] = Field(description="List of dates mentioned in the text")

async def process_api_requests_from_file(
    requests_filepath: str,
    save_filepath: str,
    request_url: str,
    api_key: str,
    max_requests_per_minute: float,
    max_tokens_per_minute: float,
    token_encoding_name: str,
    max_attempts: int,
    logging_level: int,
):
    """Processes API requests in parallel, throttling to stay under rate limits."""
    # constants
    seconds_to_pause_after_rate_limit_error = 15
    seconds_to_sleep_each_loop = (
        0.005  # 1 ms limits max throughput to 1,000 requests per second
    )

    # initialize logging
    logging.basicConfig(level=logging_level)
    logging.debug(f"Logging initialized at level {logging_level}")

    # infer API endpoint and construct request header
    api_endpoint = api_endpoint_from_url(request_url)
    request_header = {"Authorization": f"Bearer {api_key}"}
    # use api-key header for Azure deployments
    if "/deployments" in request_url:
        request_header = {"api-key": f"{api_key}"}

    # initialize trackers
    queue_of_requests_to_retry = asyncio.Queue()
    task_id_generator = (
        task_id_generator_function()
    )  # generates integer IDs of 0, 1, 2, ...
    status_tracker = (
        StatusTracker()
    )  # single instance to track a collection of variables
    next_request = None  # variable to hold the next request to call

    # initialize available capacity counts
    available_request_capacity = max_requests_per_minute
    available_token_capacity = max_tokens_per_minute
    last_update_time = time.time()

    # initialize flags
    file_not_finished = True  # after file is empty, we'll skip reading it
    logging.debug(f"Initialization complete.")

    # initialize file reading
    with open(requests_filepath) as file:
        # `requests` will provide requests one at a time
        requests = file.__iter__()
        logging.debug(f"File opened. Entering main loop")
        async with aiohttp.ClientSession() as session:  # Initialize ClientSession here
            while True:
                # get next request (if one is not already waiting for capacity)
                if next_request is None:
                    if not queue_of_requests_to_retry.empty():
                        next_request = queue_of_requests_to_retry.get_nowait()
                        logging.debug(
                            f"Retrying request {next_request.task_id}: {next_request}"
                        )
                    elif file_not_finished:
                        try:
                            # get new request
                            request_json = json.loads(next(requests))
                            next_request = APIRequest(
                                task_id=next(task_id_generator),
                                request_json=request_json,
                                token_consumption=num_tokens_consumed_from_request(
                                    request_json, api_endpoint, token_encoding_name
                                ),
                                attempts_left=max_attempts,
                                metadata=request_json.pop("metadata", None),
                            )
                            status_tracker.num_tasks_started += 1
                            status_tracker.num_tasks_in_progress += 1
                            logging.debug(
                                f"Reading request {next_request.task_id}: {next_request}"
                            )
                        except StopIteration:
                            # if file runs out, set flag to stop reading it
                            logging.debug("Read file exhausted")
                            file_not_finished = False

                # update available capacity
                current_time = time.time()
                seconds_since_update = current_time - last_update_time
                available_request_capacity = min(
                    available_request_capacity
                    + max_requests_per_minute * seconds_since_update / 60.0,
                    max_requests_per_minute,
                )
                available_token_capacity = min(
                    available_token_capacity
                    + max_tokens_per_minute * seconds_since_update / 60.0,
                    max_tokens_per_minute,
                )
                last_update_time = current_time

                # if enough capacity available, call API
                if next_request:
                    next_request_tokens = next_request.token_consumption
                    if (
                        available_request_capacity >= 1
                        and available_token_capacity >= next_request_tokens
                    ):
                        # update counters
                        available_request_capacity -= 1
                        available_token_capacity -= next_request_tokens
                        next_request.attempts_left -= 1

                        # call API
                        asyncio.create_task(
                            next_request.call_api(
                                session=session,
                                request_url=request_url,
                                request_header=request_header,
                                retry_queue=queue_of_requests_to_retry,
                                save_filepath=save_filepath,
                                status_tracker=status_tracker,
                            )
                        )
                        next_request = None  # reset next_request to empty

                # if all tasks are finished, break
                if status_tracker.num_tasks_in_progress == 0:
                    break

                # main loop sleeps briefly so concurrent tasks can run
                await asyncio.sleep(seconds_to_sleep_each_loop)

                # if a rate limit error was hit recently, pause to cool down
                seconds_since_rate_limit_error = (
                    time.time() - status_tracker.time_of_last_rate_limit_error
                )
                if (
                    seconds_since_rate_limit_error
                    < seconds_to_pause_after_rate_limit_error
                ):
                    remaining_seconds_to_pause = (
                        seconds_to_pause_after_rate_limit_error
                        - seconds_since_rate_limit_error
                    )
                    await asyncio.sleep(remaining_seconds_to_pause)
                    # ^e.g., if pause is 15 seconds and final limit was hit 5 seconds ago
                    logging.warn(
                        f"Pausing to cool down until {time.ctime(status_tracker.time_of_last_rate_limit_error + seconds_to_pause_after_rate_limit_error)}"
                    )

        # after finishing, log final status
        logging.info(
            f"""Parallel processing complete. Results saved to {save_filepath}"""
        )
        if status_tracker.num_tasks_failed > 0:
            logging.warning(
                f"{status_tracker.num_tasks_failed} / {status_tracker.num_tasks_started} requests failed. Errors logged to {save_filepath}."
            )
        if status_tracker.num_rate_limit_errors > 0:
            logging.warning(
                f"{status_tracker.num_rate_limit_errors} rate limit errors received. Consider running at a lower rate."
            )


# dataclasses


@dataclass
class StatusTracker:
    """Stores metadata about the script's progress. Only one instance is created."""

    num_tasks_started: int = 0
    num_tasks_in_progress: int = 0  # script ends when this reaches 0
    num_tasks_succeeded: int = 0
    num_tasks_failed: int = 0
    num_rate_limit_errors: int = 0
    num_api_errors: int = 0  # excluding rate limit errors, counted above
    num_other_errors: int = 0
    time_of_last_rate_limit_error: int = 0  # used to cool off after hitting rate limits


@dataclass
class APIRequest:
    """Stores an API request's inputs, outputs, and other metadata. Contains a method to make an API call."""

    task_id: int
    request_json: dict
    token_consumption: int
    attempts_left: int
    metadata: dict
    result: list = field(default_factory=list)

    async def call_api(
        self,
        session: aiohttp.ClientSession,
        request_url: str,
        request_header: dict,
        retry_queue: asyncio.Queue,
        save_filepath: str,
        status_tracker: StatusTracker,
    ):
        """Calls the OpenAI API with support for structured outputs."""
        logging.info(f"Starting request #{self.task_id}")
        error = None

        # Ensure strict mode is enabled if response_format is present
        if "response_format" in self.request_json:
            self.request_json["strict"] = True

        try:
            async with session.post(
                url=request_url,
                headers=request_header,
                json=self.request_json
            ) as response:
                response = await response.json()

            if "error" in response:
                logging.warning(
                    f"Request {self.task_id} failed with error {response['error']}"
                )
                status_tracker.num_api_errors += 1
                error = response
                if "rate limit" in response["error"].get("message", "").lower():
                    status_tracker.time_of_last_rate_limit_error = time.time() # type: ignore
                    status_tracker.num_rate_limit_errors += 1
                    status_tracker.num_api_errors -= 1
            else:
                # For structured outputs, the response will always follow the schema
                self.result = [
                    {
                        "request": self.request_json,
                        "response": response,
                        "metadata": self.metadata
                    }
                ]
                append_to_jsonl(self.result, save_filepath)
                status_tracker.num_tasks_succeeded += 1
                status_tracker.num_tasks_in_progress -= 1
                logging.debug(f"Request {self.task_id} completed successfully")

        except Exception as e:
            logging.warning(f"Request {self.task_id} failed with Exception {e}")
            status_tracker.num_other_errors += 1
            error = e

        if error:
            self.result.append(error)
            if self.attempts_left:
                retry_queue.put_nowait(self)
            else:
                logging.error(f"Request {self.task_id} failed after all attempts. Saving error: {error}")
                append_to_jsonl(self.result, save_filepath)
                status_tracker.num_tasks_failed += 1
                status_tracker.num_tasks_in_progress -= 1


# functions


def api_endpoint_from_url(request_url):
    """Extract the API endpoint from the request URL."""
    match = re.search("^https://[^/]+/v\\d+/(.+)$", request_url)
    if match is None:
        # for Azure OpenAI deployment urls
        match = re.search(
            r"^https://[^/]+/openai/deployments/[^/]+/(.+?)(\?|$)", request_url
        )
    return match[1]


def append_to_jsonl(data, filename: str) -> None:
    """Append a json payload to the end of a jsonl file."""
    json_string = json.dumps(data)
    with open(filename, "a") as f:
        f.write(json_string + "\n")


def num_tokens_consumed_from_request(
    request_json: dict,
    api_endpoint: str,
    token_encoding_name: str,
) -> int:
    """Count the number of tokens in the request, including schema tokens."""
    encoding = tiktoken.get_encoding(token_encoding_name)

    # If using gpt-4 or gpt-3.5-turbo, use cl100k_base encoding
    if "gpt-4o-mini" in request_json.get("model", "") or "gpt-3.5-turbo" in request_json.get("model", ""):
        encoding = tiktoken.get_encoding("cl100k_base")

    num_tokens = 0

    # Count message tokens
    for message in request_json.get("messages", []):
        num_tokens += 4  # Format tax for each message
        for key, value in message.items():
            num_tokens += len(encoding.encode(str(value)))
            if key == "name":
                num_tokens += -1  # Role is omitted

    # Count schema tokens if present
    if "response_format" in request_json:
        schema_str = json.dumps(request_json["response_format"])
        num_tokens += len(encoding.encode(schema_str))

    num_tokens += 2  # Every response is primed with <im_start>assistant
    return num_tokens


def task_id_generator_function():
    """Generate integers 0, 1, 2, and so on."""
    task_id = 0
    while True:
        yield task_id
        task_id += 1


# run script


def save_simplified_csv(df: pd.DataFrame, save_filepath: str) -> None:
    """
    Save a simplified CSV with node_id, description, and signature if available.

    Args:
        df: DataFrame containing the node data
        save_filepath: Path to save the simplified CSV
    """
    # Determine output path for CSV
    csv_path = save_filepath.rsplit('.', 1)[0] + '_simplified.csv'

    # Create a copy with only required columns that exist in the DataFrame
    columns_to_include = []

    # Check for node_id column
    if 'node_id' in df.columns:
        columns_to_include.append('node_id')
    elif 'id' in df.columns:
        columns_to_include.append('id')

    # Check for description column
    if 'description' in df.columns:
        columns_to_include.append('description')

    # Create the simplified DataFrame with available columns
    simplified_df = df[columns_to_include].copy()

    # Add signature column if it exists in the original DataFrame
    if 'signature' in df.columns:
        # Extract only the 'params' from signature dictionary
        def extract_params(signature):
            if not signature:
                return ''
            try:
                # Convert string representation of dict to actual dict
                if isinstance(signature, str):
                    signature = eval(signature)
                if isinstance(signature, dict) and 'params' in signature:
                    # Filter out empty strings from params list
                    params = [p for p in signature['params'] if p]
                    return ', '.join(params)
                return ''
            except:
                return ''

        simplified_df['signature'] = df['signature'].apply(extract_params)
    else:
        # If no signature column exists, add an empty one
        simplified_df['signature'] = ''

    # Save to CSV
    simplified_df.to_csv(csv_path, index=False)
    print(f"Simplified CSV saved to {csv_path}")

def convert_parquet_to_simplified_csv(parquet_path: str, output_csv_path: str | None = None) -> None:
    """
    Read an existing parquet file and convert it to a simplified CSV format with
    node_id, description, and formatted signature parameters.

    Parameters
    ----------
    parquet_path : str
        Path to the input parquet file
    output_csv_path : str, optional
        Path where to save the CSV file. If None, will use parquet path with '_simplified.csv' suffix
    """
    try:
        # Read the parquet file
        df = pd.read_parquet(parquet_path)

        # Verify required columns exist
        required_columns = ['node_id', 'description']  # 'signature' is now optional
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"Missing required columns in parquet file: {missing_columns}")

        # Filter for FUNCTION type rows if 'type' column exists
        if 'type' in df.columns:
            df = df[df['type'] == 'FUNCTION']

        # Create simplified DataFrame with required columns
        simplified_df = df[required_columns].copy()

        # Add signature column if it exists
        if 'signature' in df.columns:
            # Extract only the 'params' from signature dictionary
            def extract_params(signature):
                if not signature:
                    return ''
                try:
                    # Convert string representation of dict to actual dict
                    if isinstance(signature, str):
                        signature = eval(signature)
                    if isinstance(signature, dict) and 'params' in signature:
                        # Filter out empty strings from params list
                        params = [p for p in signature['params'] if p]
                        return ', '.join(params)
                    return ''
                except:
                    return ''

            simplified_df['signature'] = df['signature'].apply(extract_params)
        else:
            # Add empty signature column if it doesn't exist
            simplified_df['signature'] = ''

        # Determine output path
        if output_csv_path is None:
            output_csv_path = parquet_path.rsplit('.', 1)[0] + '_simplified.csv'

        # Save to CSV
        simplified_df.to_csv(output_csv_path, index=False)
        print(f"Simplified CSV saved to {output_csv_path}")

    except Exception as e:
        print(f"Error processing parquet file: {str(e)}")
        raise

async def document_functions(
    parquet_file: str,
    save_filepath: str,
    request_url: str = "https://api.openai.com/v1/chat/completions",
    max_requests_per_minute: float = 15000,
    max_tokens_per_minute: float = 62500000,
    token_encoding_name: str = "cl100k_base",
    max_attempts: int = 3,
    selection_method: str = "all",  # Options: "all", "top", "random", "range"
    n_rows: int = None,  # Number of rows to process
    start_row: int = None,  # Start index for range selection
    end_row: int = None,  # End index for range selection
    save_all_rows: bool = False,  # Whether to save all rows or only processed ones
) -> None:
    """
    Process a parquet file containing code nodes, get LLM descriptions for functions,
    and save the results with new descriptions.
    """
    # Read the parquet file
    df = pd.read_parquet(parquet_file)

    # Filter for function nodes with valid line numbers and ONLY Python files
    # Use a strict regex pattern to match only .py files
    function_df = df[
        (df['type'] == 'FUNCTION') &
        (df['line'] != -1) &
        (df['end_line'] != -1) &
        (df['node_id'].str.match(r'.*\.(py|rb):[^:]*$', na=False))  # Match .py or .rb extension before the colon
    ].copy()

    print(f"Found {len(function_df)} Python function nodes out of {len(df)} total nodes")

    # Apply selection method to the already filtered Python-only function_df
    if selection_method == "top" and n_rows is not None:
        function_df = function_df.head(n_rows)
    elif selection_method == "random" and n_rows is not None:
        function_df = function_df.sample(n=min(n_rows, len(function_df)))
    elif selection_method == "range" and start_row is not None and end_row is not None:
        function_df = function_df.iloc[start_row:end_row]

    print(f"Selected {len(function_df)} Python function nodes for processing")

    # Prepare requests for the LLM
    requests = []
    for idx, row in function_df.iterrows():
        request = {
            "model": "gpt-4o-mini",
            "messages": [
                {
                    "role": "system",
                    # "content": """You are a technical documentation expert. Analyze the provided function and write a clear,
                    # concise description (2-3 sentences) explaining what the function does and its purpose. Focus on:
                    # - Main functionality and purpose
                    # - Key parameters or dependencies
                    # - Important side effects or notable behaviors
                    # Keep the description technical but readable."""
                    "content": """Provide an ultra-concise technical description (<50 tokens) of this function.
                    Focus on core logic, key algorithms and any important implementation details. Use precise technical terminology. Omit examples and unnecessary context.
                    Prioritize information density and technical accuracy.""",
                },
                {
                    "role": "user",
                    "content": f"Please describe this function:\n\n{row['text']}"
                }
            ],
            "metadata": {"function_idx": idx},
            # "max_tokens": 10000
        }
        requests.append(request)

    if not requests:
        print("No Python functions selected for processing.")
        return

    print(f"Processing {len(requests)} Python functions...")

    # Save requests to JSONL
    with open("function_requests.jsonl", "w") as f:
        for request in requests:
            f.write(json.dumps(request) + "\n")

    # Process the requests
    await process_api_requests_from_file(
        requests_filepath="function_requests.jsonl",
        save_filepath="function_descriptions.jsonl",
        request_url=request_url,
        api_key="********************************************************************************************************************************************************************",
        max_requests_per_minute=max_requests_per_minute,
        max_tokens_per_minute=max_tokens_per_minute,
        token_encoding_name=token_encoding_name,
        max_attempts=max_attempts,
        logging_level=20,
    )

    # Read the results and update the dataframe
    descriptions = {}
    with open("function_descriptions.jsonl", "r") as f:
        for line in f:
            result = json.loads(line)
            # The result is an array with a single object
            result_obj = result[0]  # Get the first (and only) object in the array
            function_idx = result_obj["metadata"]["function_idx"]
            description = result_obj["response"]["choices"][0]["message"]["content"]
            descriptions[function_idx] = description

    # Decide which DataFrame to update and save
    if save_all_rows:
        # Update the full DataFrame with descriptions where available
        df['description'] = None
        for idx, description in descriptions.items():
            if idx in function_df.index:  # Only update Python function nodes
                df.loc[idx, 'description'] = description
        output_df = df
    else:
        # Create a new DataFrame with only the processed Python function rows
        processed_indices = list(descriptions.keys())
        # Ensure we're only selecting from the Python function nodes
        valid_indices = [idx for idx in processed_indices if idx in function_df.index]
        output_df = function_df.loc[valid_indices].copy()
        for idx, description in descriptions.items():
            if idx in valid_indices:
                output_df.loc[idx, 'description'] = description

    # Save the output DataFrame
    output_df.to_parquet(save_filepath)
    print(f"Processing complete. Results saved to {save_filepath}")
    print(f"Saved {len(output_df)} rows to {save_filepath}")
    if save_all_rows:
        print(f"Of which {len(descriptions)} rows were processed with descriptions")

    # Save simplified CSV
    save_simplified_csv(output_df, save_filepath)


#TODO -> Batch the calls please.
async def document_functions_with_significance(
    parquet_file: str,
    save_filepath: str,
    request_url: str = "https://api.openai.com/v1/chat/completions",
    max_requests_per_minute: float = 50,
    max_tokens_per_minute: float = 100000,
    token_encoding_name: str = "cl100k_base",
    max_attempts: int = 3,
    selection_method: str = "all",
    n_rows: int = None,
    start_row: int = None,
    end_row: int = None,
    save_all_rows: bool = True,
) -> None:
    """
    Process a parquet file containing code nodes, evaluate architectural significance,
    get LLM descriptions for significant functions, and filter call contexts.
    """
    # Read the parquet file
    df = pd.read_parquet(parquet_file)

    # Extract call_contexts column if it exists
    has_call_contexts = 'call_contexts' in df.columns

    # Check if we're using 'line' or 'start_line' column naming
    line_col = 'start_line' if 'start_line' in df.columns else 'line'
    end_line_col = 'end_line'

    # Filter for function nodes with valid line numbers and ONLY Python files
    function_df = df[
        # (df['type'] == 'FUNCTION') &
        (df[line_col] != -1) &
        (df[end_line_col] != -1) &
        (df['node_id'].str.match(r'.*\.(py|rb):[^:]*$', na=False))  # Match .py or .rb extension before the colon
    ].copy()

    print(f"Found {len(function_df)} Python function nodes out of {len(df)} total nodes")

    # Apply selection method to the filtered function_df
    if selection_method == "top" and n_rows is not None:
        function_df = function_df.head(n_rows)
    elif selection_method == "random" and n_rows is not None:
        function_df = function_df.sample(n=min(n_rows, len(function_df)))
    elif selection_method == "range" and start_row is not None and end_row is not None:
        function_df = function_df.iloc[start_row:end_row]

    print(f"Selected {len(function_df)} Python function nodes for processing")

    # Prepare requests for the LLM to evaluate significance and provide descriptions
    requests = []
    for idx, row in function_df.iterrows():
        # Extract call_contexts if available
        call_contexts = ""
        if has_call_contexts:
            contexts = row.get('call_contexts', "")
            # Handle different formats of call_contexts
            if isinstance(contexts, str):
                try:
                    # Try to parse as JSON if it's a string representation
                    contexts_list = json.loads(contexts)
                    if isinstance(contexts_list, list):
                        call_contexts = "\n".join([str(ctx) for ctx in contexts_list])
                    else:
                        call_contexts = str(contexts)
                except json.JSONDecodeError:
                    call_contexts = contexts
            elif isinstance(contexts, list):
                call_contexts = "\n".join([str(ctx) for ctx in contexts])
            else:
                call_contexts = str(contexts)

#         request = {
#             "model": "gpt-4o-mini",
#             "messages": [
#                 {
#                     "role": "system",
#                     "content": f"""You are an expert software engineer curating a meaningful function call graph.

# You'll be given:
# - One function
# - A list of call contexts (if available)

# **Your tasks:**
# 1. Decide if this function adds value to understanding the codebase. Be **lenient but intentional** — include functions that carry logic, coordination, or domain meaning; exclude trivial or built-in ones.
# 2. Filter the call contexts to only include those that provide meaningful information about how the function is used.

# **Mark as SIGNIFICANT if:**
# - It orchestrates meaningful calls
# - Contains domain logic or decisions
# - Transforms or routes major data, minor can be skipped
# - Encapsulates useful workflows

# **Exclude if:**
# - It's a built-in (`__eq__`, `__str__`, etc.)
# - Only logs, prints, formats, or wraps
# - It's a built-in (`initialize`, `==`, `to_s`, etc.)
# - It's a Rails DSL or routing/helper/validation method (`before_action`, `delegate`, etc.)
# - Does trivial conversion (`from_dict`, etc.)
# - Not contributing to strong logic

# **For call contexts:**
# - Keep contexts that show important usage patterns
# - Keep contexts that reveal the function's role in larger workflows
# - Remove trivial and generic contexts
# - Always Remove logging, inbuilt fns, documentation, string heavy, or large contexts
# - Remove contexts that don't add understanding about the function's purpose
# - If the call_contexts function is relevant but large, then write a short version of it only, ex- skip the args, remove strings - we are okay with rough values.

# **Output:**
# - `SIGNIFICANT: true` or `SIGNIFICANT: false`
# - If true, follow with `DESCRIPTION:` and give a short, information-dense summary (<50 tokens) of **what the function does**
# - Then add `FILTERED_CALL_CONTEXTS:` followed by a JSON array of the filtered call contexts

# Now evaluate the function below."""
#                 },
#                 {
#                     "role": "user",
#                     "content": f"""Evaluate this function:

# FUNCTION CODE:
# {row['text']}

# CALL CONTEXTS:
# {call_contexts if call_contexts else "No call contexts available"}"""
#                 }
#             ],
#             "metadata": {"function_idx": idx},
#             # "max_tokens": 10000
#         }


        request = {
            "model": "gpt-4o-mini",
            "messages": [
                {
                    "role": "system",
                    "content": f"""You are an expert software engineer curating a meaningful function call graph.

You'll be given:
- One Ruby method
- A list of call contexts (if available)

**Your tasks:**
1. Decide if this method adds value to understanding the codebase. Be **lenient but intentional** — include methods that carry logic, coordination, or domain meaning; exclude trivial or built-in ones.
2. Filter the call contexts to only include those that provide meaningful information about how the method is used.

**Mark as SIGNIFICANT if:**
- It orchestrates meaningful calls
- Contains domain logic or decisions
- Transforms or routes major data, minor can be skipped
- Encapsulates useful workflows
- Implements important Ruby metaprogramming patterns

**Exclude if:**
- It's a Ruby built-in method (`initialize`, `to_s`, `==`, etc.)
- Only logs, prints, formats, or wraps
- It's a simple accessor/mutator (getter/setter)
- It's a Rails DSL method (`belongs_to`, `has_many`, `validates`, `before_action`, etc.)
- It's a routing/helper/validation method (`before_action`, `delegate`, etc.)
- Does trivial conversion (`to_hash`, `from_json`, etc.)
- It's a simple callback method with no substantial logic
- Not contributing to strong logic

**Ruby-specific considerations:**
- Consider the importance of blocks, procs, and lambdas in the method
- Pay attention to Ruby's metaprogramming features (method_missing, define_method, etc.)
- Recognize Ruby's convention over configuration patterns
- For Rails applications, understand MVC patterns and lifecycle hooks

**For call contexts:**
- Keep contexts that show important usage patterns
- Keep contexts that reveal the method's role in larger workflows
- Remove trivial and generic contexts
- Always Remove logging, inbuilt methods, documentation, string heavy, or large contexts
- Remove contexts that don't add understanding about the method's purpose
- If the call_contexts method is relevant but large, then write a short version of it only, ex- skip the args, remove strings - we are okay with rough values.

**Output:**
- `SIGNIFICANT: true` or `SIGNIFICANT: false`
- If true, follow with `DESCRIPTION:` and give a short, information-dense summary (<50 tokens) of **what the method does**
- Then add `FILTERED_CALL_CONTEXTS:` followed by a JSON array of the filtered call contexts

Now evaluate the Ruby method below."""
        },
        {
            "role": "user",
            "content": f"""Evaluate this Ruby method:

METHOD CODE:
{row['text']}

CALL CONTEXTS:
{call_contexts if call_contexts else "No call contexts available"}"""
                }
            ],
            "metadata": {"function_idx": idx},
            # "max_tokens": 10000
        }

        requests.append(request)

    if not requests:
        print("No Python functions selected for processing.")
        return

    print(f"Processing {len(requests)} Python functions...")

    # Save requests to JSONL
    with open("function_requests.jsonl", "w") as f:
        for request in requests:
            f.write(json.dumps(request) + "\n")

    # Process the requests
    await process_api_requests_from_file(
        requests_filepath="function_requests.jsonl",
        save_filepath="function_descriptions.jsonl",
        request_url=request_url,
        api_key="********************************************************************************************************************************************************************",
        max_requests_per_minute=max_requests_per_minute,
        max_tokens_per_minute=max_tokens_per_minute,
        token_encoding_name=token_encoding_name,
        max_attempts=max_attempts,
        logging_level=20,
    )

    # Read the results and update the dataframe
    significance_results = {}
    descriptions = {}
    filtered_call_contexts = {}

    with open("function_descriptions.jsonl", "r") as f:
        for line in f:
            result = json.loads(line)
            result_obj = result[0]
            function_idx = result_obj["metadata"]["function_idx"]
            content = result_obj["response"]["choices"][0]["message"]["content"]

            # Parse the significance, description, and filtered call contexts
            lines = content.strip().split('\n')
            is_significant = False
            description = ""
            contexts = []

            current_section = None
            context_lines = []

            for line in lines:
                if line.startswith("SIGNIFICANT:"):
                    is_significant = "true" in line.lower()
                    current_section = "significance"
                elif line.startswith("DESCRIPTION:"):
                    description = line[len("DESCRIPTION:"):].strip()
                    current_section = "description"
                elif line.startswith("FILTERED_CALL_CONTEXTS:"):
                    current_section = "contexts"
                    context_text = line[len("FILTERED_CALL_CONTEXTS:"):].strip()
                    if context_text:
                        context_lines.append(context_text)
                elif current_section == "contexts":
                    context_lines.append(line)

            # Process collected context lines
            if context_lines:
                context_text = " ".join(context_lines)
                try:
                    # Try to parse as JSON
                    contexts = json.loads(context_text)
                except json.JSONDecodeError:
                    # If not valid JSON, use as is
                    contexts = context_text

            significance_results[function_idx] = is_significant
            if is_significant:
                descriptions[function_idx] = description
            filtered_call_contexts[function_idx] = contexts

    # Update the dataframe with results
    function_df['is_architecturally_significant'] = function_df.index.map(
        lambda idx: significance_results.get(idx, False)
    )

    function_df['description'] = function_df.index.map(
        lambda idx: descriptions.get(idx, "") if significance_results.get(idx, False) else ""
    )

    function_df['filtered_call_contexts'] = function_df.index.map(
        lambda idx: json.dumps(filtered_call_contexts.get(idx, []))
        if isinstance(filtered_call_contexts.get(idx, []), list)
        else filtered_call_contexts.get(idx, "")
    )

    # Save the updated dataframe
    if save_all_rows:
        # Merge back with the original dataframe
        result_df = df.copy()
        # Update only the rows that were processed
        for idx in function_df.index:
            if idx in result_df.index:
                result_df.loc[idx, 'is_architecturally_significant'] = function_df.loc[idx, 'is_architecturally_significant']
                if function_df.loc[idx, 'description']:
                    result_df.loc[idx, 'description'] = function_df.loc[idx, 'description']
                result_df.loc[idx, 'filtered_call_contexts'] = function_df.loc[idx, 'filtered_call_contexts']
    else:
        # Only save the processed rows
        result_df = function_df

    # Save to parquet
    result_df.to_parquet(save_filepath)

    print(f"Processed {len(function_df)} functions")
    print(f"Found {sum(significance_results.values())} architecturally significant functions")


async def document_functions_with_context_significance(
    parquet_file: str,
    edges_parquet_path: str,
    save_filepath: str,
    request_url: str = "https://api.openai.com/v1/chat/completions",
    max_requests_per_minute: float = 50,
    max_tokens_per_minute: float = 100000,
    token_encoding_name: str = "cl100k_base",
    max_attempts: int = 3,
    selection_method: str = "all",
    n_rows: int = None,
    start_row: int = None,
    end_row: int = None,
    save_all_rows: bool = True,
) -> None:
    """
    Two-pass process for evaluating function significance with context:
    1. First pass: Document all functions without judging significance
    2. Second pass: Evaluate significance with function context (calls/called-by relationships)

    Args:
        parquet_file: Path to the nodes parquet file
        edges_parquet_path: Path to the edges parquet file containing call relationships
        save_filepath: Path to save the final documented functions
        request_url: API endpoint for LLM requests
        max_requests_per_minute: Rate limit for API requests
        max_tokens_per_minute: Token rate limit for API
        token_encoding_name: Token encoding to use
        max_attempts: Maximum retry attempts for failed requests
        selection_method: Method to select functions for processing
        n_rows: Number of rows to process
        start_row: Start index for range selection
        end_row: End index for range selection
        save_all_rows: Whether to save all rows or only processed ones
    """
    # Read the nodes parquet file
    df = pd.read_parquet(parquet_file)

    # Check if we're using 'line' or 'start_line' column naming
    line_col = 'start_line' if 'start_line' in df.columns else 'line'
    end_line_col = 'end_line'

    # Filter for function nodes with valid line numbers and ONLY Python files
    function_df = df[
        (df['type'] == 'FUNCTION') &
        (df[line_col] != -1) &
        (df[end_line_col] != -1) &
        (df['node_id'].str.match(r'.*\.py:[^:]*$', na=False))
    ].copy()

    print(f"Found {len(function_df)} Python function nodes out of {len(df)} total nodes")

    # Apply selection method to the filtered function_df
    if selection_method == "top" and n_rows is not None:
        function_df = function_df.head(n_rows)
    elif selection_method == "random" and n_rows is not None:
        function_df = function_df.sample(n=min(n_rows, len(function_df)))
    elif selection_method == "range" and start_row is not None and end_row is not None:
        function_df = function_df.iloc[start_row:end_row]

    print(f"Selected {len(function_df)} Python function nodes for processing")

    # PASS 1: Document all functions without judging significance
    print("PASS 1: Documenting all functions...")

    # Prepare requests for the LLM to document functions
    pass1_requests = []
    for idx, row in function_df.iterrows():
        request = {
            "model": "gpt-4o-mini",
            "messages": [
                {
                    "role": "system",
                    "content": """Provide an ultra-concise technical description (<50 tokens) of this function.
                    Focus on core logic, key algorithms and any important implementation details. Use precise technical terminology. Omit examples and unnecessary context.
                    Prioritize information density and technical accuracy.""",
                },
                {
                    "role": "user",
                    "content": f"Please describe this function:\n\n{row['text']}"
                }
            ],
            "metadata": {"function_idx": idx},
            # "max_tokens": 10000
        }
        pass1_requests.append(request)

    if not pass1_requests:
        print("No Python functions selected for processing.")
        return

    print(f"PASS 1: Processing {len(pass1_requests)} Python functions...")

    # Save requests to JSONL
    with open("function_requests_pass1.jsonl", "w") as f:
        for request in pass1_requests:
            f.write(json.dumps(request) + "\n")

    # Process the requests
    await process_api_requests_from_file(
        requests_filepath="function_requests_pass1.jsonl",
        save_filepath="function_descriptions_pass1.jsonl",
        request_url=request_url,
        api_key="********************************************************************************************************************************************************************",
        max_requests_per_minute=max_requests_per_minute,
        max_tokens_per_minute=max_tokens_per_minute,
        token_encoding_name=token_encoding_name,
        max_attempts=max_attempts,
        logging_level=20,
    )

    # Read the results from pass 1
    descriptions = {}
    with open("function_descriptions_pass1.jsonl", "r") as f:
        for line in f:
            result = json.loads(line)
            result_obj = result[0]
            function_idx = result_obj["metadata"]["function_idx"]
            description = result_obj["response"]["choices"][0]["message"]["content"]
            descriptions[function_idx] = description

    # Update the DataFrame with descriptions
    for idx, description in descriptions.items():
        if idx in function_df.index:
            function_df.loc[idx, 'description'] = description

    print(f"PASS 1: Documented {len(descriptions)} functions")

    # Read the edges parquet file to build the call graph
    edges_df = pd.read_parquet(edges_parquet_path)

    # Filter for CALLS and REFERENCES edges
    call_edges_df = edges_df[edges_df['type'].isin(['CALLS', 'REFERENCES'])]

    # Debug: Print some sample edges to verify format
    print(f"Sample edge - Source: {call_edges_df.iloc[0]['source']}, Target: {call_edges_df.iloc[0]['target']}")
    print(f"Sample function node_id: {function_df.iloc[0]['node_id']}")

    # Debug: Print some statistics about the call graph
    print(f"Total edges: {len(call_edges_df)}")
    print(f"Edges with function-to-function calls: {sum(1 for _, edge in call_edges_df.iterrows() if ':' in edge['source'] and ':' in edge['target'])}")

    # Create a set of valid function node_ids for faster lookup
    valid_function_ids = set(function_df['node_id'])

    # Build call graph relationships with normalized keys
    calls_map = {}  # What functions this function calls
    called_by_map = {}  # What functions call this function

    for _, edge in call_edges_df.iterrows():
        source = edge['source']
        target = edge['target']

        # Only consider edges where both source and target are in our function_df
        # This ensures we're only dealing with function-to-function relationships
        if source in valid_function_ids and target in valid_function_ids:
            # Initialize if not exists
            if source not in calls_map:
                calls_map[source] = []
            if target not in called_by_map:
                called_by_map[target] = []

            # Add relationships
            calls_map[source].append(target)
            called_by_map[target].append(source)

    # Debug: Print statistics about the call graph
    print(f"Functions with outgoing calls: {len(calls_map)}")
    print(f"Functions with incoming calls: {len(called_by_map)}")

    # Create a mapping from function_df index to node_id for easier lookup
    idx_to_node_id = {idx: row['node_id'] for idx, row in function_df.iterrows()}
    node_id_to_idx = {row['node_id']: idx for idx, row in function_df.iterrows()}

    # Debug: Check how many functions in our DataFrame are in the call graph
    functions_in_calls_map = sum(1 for node_id in function_df['node_id'] if node_id in calls_map)
    functions_in_called_by_map = sum(1 for node_id in function_df['node_id'] if node_id in called_by_map)
    print(f"Functions in our DataFrame that have outgoing calls: {functions_in_calls_map} out of {len(function_df)}")
    print(f"Functions in our DataFrame that have incoming calls: {functions_in_called_by_map} out of {len(function_df)}")

    # PASS 2: Evaluate significance with context
    print("PASS 2: Evaluating significance with context...")

    # Prepare requests for the LLM to evaluate significance with context
    pass2_requests = []
    for idx, row in function_df.iterrows():
        node_id = row['node_id']

        # Get functions this function calls
        calls_list = []
        if node_id in calls_map:
            for called_func in calls_map[node_id]:
                # Try to find the called function in our function_df
                called_idx = None
                if called_func in node_id_to_idx:
                    called_idx = node_id_to_idx[called_func]

                if called_idx is not None and called_idx in function_df.index:
                    called_desc = function_df.loc[called_idx, 'description'] if pd.notna(function_df.loc[called_idx, 'description']) else "No description"
                    calls_list.append(f"- {called_func}: {called_desc}")

        # Get functions that call this function
        called_by_list = []
        if node_id in called_by_map:
            for caller_func in called_by_map[node_id]:
                # Try to find the caller function in our function_df
                caller_idx = None
                if caller_func in node_id_to_idx:
                    caller_idx = node_id_to_idx[caller_func]

                if caller_idx is not None and caller_idx in function_df.index:
                    caller_desc = function_df.loc[caller_idx, 'description'] if pd.notna(function_df.loc[caller_idx, 'description']) else "No description"
                    called_by_list.append(f"- {caller_func}: {caller_desc}")

        # Format the context information
        calls_context = "\n".join(calls_list[:10])  # Limit to 10 to avoid token limits
        called_by_context = "\n".join(called_by_list[:10])  # Limit to 10 to avoid token limits

        # Get the function's description from pass 1
        func_description = row['description'] if pd.notna(row['description']) else "No description available"

        request = {
            "model": "gpt-4o-mini",
            "messages": [
                {
                    "role": "system",
                    "content": """You are analyzing a function to determine if it should be included in a knowledge graph of this codebase.

Your task is to identify functions that contribute meaningful knowledge for understanding how the system works, regardless of their architectural significance. We're building a bottom-up understanding, so include functions that provide valuable context even if they seem simple.

Evaluate this function using these criteria:
1. INFORMATION VALUE: Does it contain unique logic, patterns, or implementation details?
2. DOMAIN KNOWLEDGE: Does it encode domain concepts, terminology, or business rules?
3. CONTEXTUAL IMPORTANCE: Does it help explain the purpose of other functions?
4. KNOWLEDGE PREREQUISITE: Is understanding this function necessary to understand other parts?
5. CALL GRAPH RELEVANCE: Does its position in the call graph reveal important relationships?
6. ALGORITHMIC VALUE: Does it implement specific algorithms or data processing techniques?

IMPORTANT GUIDELINES:
- Include utility functions if they implement non-trivial logic or are widely used
- Include functions that demonstrate how components interact
- Include functions that implement core business processes
- Include functions that handle important data transformations
- DO NOT exclude a function just because it's simple - consider its context

EXCLUSION CRITERIA - Exclude if the function:
1. Is boilerplate (simple getters/setters with little additional logic)
2. Is a trivial wrapper around standard library functions without adding value
3. Adds little logic that might not be determining the behaviour of a component/domain.
4. Contains only setup/teardown code without demonstrating important patterns
5. Is simple AND not widely used (based on call graph)
6. Contains auto-generated or templated code without domain knowledge


First, output "INCLUDE: true" or "INCLUDE: false" on a single line.
If included, add "DESCRIPTION:" with an ultra-concise (<50 tokens) technical summary."""
                },
                {
                    "role": "user",
                    "content": f"""Evaluate this function with its context:

FUNCTION CODE:
{row['text']}

FUNCTION DESCRIPTION:
{func_description}

FUNCTIONS THIS FUNCTION CALLS:
{calls_context if calls_list else "None"}

FUNCTIONS THAT CALL THIS FUNCTION:
{called_by_context if called_by_list else "None"}"""
                }
            ],
            "metadata": {"function_idx": idx},
            # "max_tokens": 10000
        }
        pass2_requests.append(request)

    print(f"PASS 2: Processing {len(pass2_requests)} Python functions...")

    # Save requests to JSONL
    with open("function_requests_pass2.jsonl", "w") as f:
        for request in pass2_requests:
            f.write(json.dumps(request) + "\n")

    # Process the requests
    await process_api_requests_from_file(
        requests_filepath="function_requests_pass2.jsonl",
        save_filepath="function_descriptions_pass2.jsonl",
        request_url=request_url,
        api_key="********************************************************************************************************************************************************************",
        max_requests_per_minute=max_requests_per_minute,
        max_tokens_per_minute=max_tokens_per_minute,
        token_encoding_name=token_encoding_name,
        max_attempts=max_attempts,
        logging_level=20,
    )

    # Read the results and update the dataframe
    significance_results = {}
    final_descriptions = {}

    with open("function_descriptions_pass2.jsonl", "r") as f:
        for line in f:
            result = json.loads(line)
            result_obj = result[0]
            function_idx = result_obj["metadata"]["function_idx"]
            content = result_obj["response"]["choices"][0]["message"]["content"]

            # Parse the significance and description
            lines = content.strip().split('\n')
            is_significant = False
            description = ""

            for line in lines:
                if line.startswith("INCLUDE:"):
                    is_significant = "true" in line.lower()
                elif line.startswith("REASON:"):
                    reason = line[len("REASON:"):].strip()
                elif line.startswith("DESCRIPTION:"):
                    description = line[len("DESCRIPTION:"):].strip()

            significance_results[function_idx] = is_significant
            if is_significant:
                final_descriptions[function_idx] = description
            else:
                # Keep the original description from pass 1 for non-significant functions
                if function_idx in descriptions:
                    final_descriptions[function_idx] = descriptions[function_idx]

    # Update the DataFrame with significance and descriptions
    df['is_architecturally_significant'] = False
    df['description'] = None

    for idx, is_significant in significance_results.items():
        if idx in function_df.index:
            df.loc[idx, 'is_architecturally_significant'] = is_significant
            if idx in final_descriptions:
                df.loc[idx, 'description'] = final_descriptions[idx]

    # Decide which DataFrame to save
    if save_all_rows:
        output_df = df
    else:
        # Only keep architecturally significant functions
        significant_indices = [idx for idx, is_significant in significance_results.items()
                              if is_significant and idx in function_df.index]
        output_df = df.loc[significant_indices].copy()

    # Save the output DataFrame
    output_df.to_parquet(save_filepath)

    # Count statistics
    total_processed = len(significance_results)
    significant_count = sum(1 for is_significant in significance_results.values() if is_significant)

    print(f"Processing complete. Results saved to {save_filepath}")
    print(f"Total functions processed: {total_processed}")
    print(f"Architecturally significant functions: {significant_count} ({significant_count/total_processed*100:.1f}%)")
    print(f"Saved {len(output_df)} rows to {save_filepath}")

    # Save simplified CSV with all functions, not just significant ones
    save_simplified_csv(output_df, save_filepath)

def convert_edges_parquet_to_csv(
    edges_parquet_path: str,
    nodes_path: str,
    output_csv_path: str | None = None,
    only_with_descriptions: bool = True
) -> None:
    """
    Read an edges parquet file and convert it to a CSV format with
    source, target, weight, and additional information from the nodes file.
    Only includes edges where type is 'REFERENCES'.

    Parameters
    ----------
    edges_parquet_path : str
        Path to the input edges parquet file
    nodes_path : str
        Path to the nodes file (parquet or csv) containing descriptions and signatures
    output_csv_path : str, optional
        Path where to save the CSV file. If None, will use edges parquet path with '_enhanced.csv' suffix
    only_with_descriptions : bool, default=True
        If True, only save rows where both source and target descriptions are not empty
    """
    try:
        # Read the edges parquet file
        edges_df = pd.read_parquet(edges_parquet_path)

        # Verify required columns exist in edges file
        required_columns = ['source', 'target', 'weight', 'type']
        missing_columns = [col for col in required_columns if col not in edges_df.columns]
        if missing_columns:
            raise ValueError(f"Missing required columns in edges parquet file: {missing_columns}")

        # Filter for REFERENCES type edges
        # edges_df = edges_df[edges_df['type'] == 'REFERENCES']

        # Filter for both REFERENCES and CALLS type edges
        edges_df = edges_df[edges_df['type'].isin(['REFERENCES', 'CALLS'])]

        if len(edges_df) == 0:
            print("No REFERENCES type edges found in the edges parquet file")
            return

        # Read the nodes file (parquet or csv)
        if nodes_path.endswith('.parquet'):
            nodes_df = pd.read_parquet(nodes_path)
        elif nodes_path.endswith('.csv'):
            nodes_df = pd.read_csv(nodes_path)
        else:
            raise ValueError(f"Unsupported file format for nodes file: {nodes_path}")

        # Verify required columns exist in nodes file
        required_node_columns = ['node_id', 'description', 'signature']
        missing_node_columns = [col for col in required_node_columns if col not in nodes_df.columns]
        if missing_node_columns:
            raise ValueError(f"Missing required columns in nodes file: {missing_node_columns}")

        # Create a dictionary for quick lookup of node information
        node_info = {}
        for _, row in nodes_df.iterrows():
            node_id = row['node_id']

            # Extract signature parameters (already processed in simplified CSV)
            signature_params = row['signature'] if pd.notna(row['signature']) else ""

            node_info[node_id] = {
                'description': row['description'] if pd.notna(row['description']) else "",
                'signature': signature_params
            }

        # Create enhanced DataFrame
        enhanced_df = edges_df[['source', 'target', 'weight']].copy()

        # Add source and target descriptions and signatures
        enhanced_df['source_description'] = enhanced_df['source'].apply(
            lambda x: node_info.get(x, {}).get('description', "")
        )
        enhanced_df['target_description'] = enhanced_df['target'].apply(
            lambda x: node_info.get(x, {}).get('description', "")
        )
        enhanced_df['source_signature'] = enhanced_df['source'].apply(
            lambda x: node_info.get(x, {}).get('signature', "")
        )
        enhanced_df['target_signature'] = enhanced_df['target'].apply(
            lambda x: node_info.get(x, {}).get('signature', "")
        )

        # Filter to only include rows where both source and target descriptions are not empty
        if only_with_descriptions:
            original_count = len(enhanced_df)
            enhanced_df = enhanced_df[
                (enhanced_df['source_description'].str.strip() != "") &
                (enhanced_df['target_description'].str.strip() != "")
            ]
            filtered_count = len(enhanced_df)
            print(f"Filtered out {original_count - filtered_count} edges with empty descriptions")

        # Determine output path
        if output_csv_path is None:
            output_csv_path = edges_parquet_path.rsplit('.', 1)[0] + '_enhanced.csv'

        # Save to CSV
        enhanced_df.to_csv(output_csv_path, index=False)
        print(f"Enhanced edges CSV saved to {output_csv_path}")
        print(f"Processed {len(enhanced_df)} REFERENCES edges with node information")

    except Exception as e:
        print(f"Error processing files: {str(e)}")
        raise

if __name__ == "__main__":
    # asyncio.run(document_functions(
    #     parquet_file="/Users/<USER>/work/startup/godzilla/bracket/bracket/experiment/data_out/repo_graphrag_nodes.parquet",
    #     save_filepath="/Users/<USER>/work/startup/godzilla/bracket/bracket/experiment/data_out/documented_graphrag_nodes.parquet",
    #     selection_method="all",
    #     # n_rows=10,
    #     max_requests_per_minute = 5000,
    #     max_tokens_per_minute = 500000,
    #     save_all_rows=True,  # Only save the processed rows
    # ))


    # convert_parquet_to_simplified_csv(
    #     parquet_path="/Users/<USER>/work/startup/godzilla/bracket/bracket/experiment/data_out/documented_graphrag_nodes.parquet",
    #     output_csv_path="/Users/<USER>/work/startup/godzilla/bracket/bracket/experiment/data_out/documented_graphrag_nodes.csv"  # optional
    # )


    # convert_edges_parquet_to_csv(
    #     edges_parquet_path="/Users/<USER>/work/startup/godzilla/bracket/bracket/experiment/data_out/repo_graphrag_edges.parquet",
    #     nodes_path="/Users/<USER>/work/startup/godzilla/bracket/bracket/experiment/data_out/documented_graphrag_nodes.csv",
    #     output_csv_path="/Users/<USER>/work/startup/godzilla/bracket/bracket/experiment/data_out/documented_graphrag_edges.csv"  # optional
    # )

#------Aider-----
    # asyncio.run(document_functions(
    #     parquet_file="/Users/<USER>/work/startup/godzilla/bracket/bracket/experiment/data_out/repo_aider_nodes.parquet",
    #     save_filepath="/Users/<USER>/work/startup/godzilla/bracket/bracket/experiment/data_out/documented_aider_nodes.parquet",
    #     selection_method="all",
    #     # n_rows=10,
    #     max_requests_per_minute = 5000,
    #     max_tokens_per_minute = 500000,
    #     save_all_rows=False,  # Only save the processed rows
    # ))


    # convert_parquet_to_simplified_csv(
    #     parquet_path="/Users/<USER>/work/startup/godzilla/bracket/bracket/experiment/data_out/documented_aider_nodes.parquet",
    #     output_csv_path="/Users/<USER>/work/startup/godzilla/bracket/bracket/experiment/data_out/documented_aider_nodes.csv"  # optional
    # )


    convert_edges_parquet_to_csv(
        edges_parquet_path="/Users/<USER>/work/startup/godzilla/bracket/bracket/experiment/data_out/repo_aider_edges.parquet",
        nodes_path="/Users/<USER>/work/startup/godzilla/bracket/bracket/experiment/data_out/documented_aider_nodes.csv",
        output_csv_path="/Users/<USER>/work/startup/godzilla/bracket/bracket/experiment/data_out/documented_aider_edges.csv",  # optional
        only_with_descriptions=True
    )
