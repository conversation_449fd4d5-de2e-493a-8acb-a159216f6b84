"""
Complete Repomap Generator

This module implements a comprehensive repository map generator that includes all functions
from all files in a codebase. It's designed to create a detailed structural representation
of the codebase that can be fed to an LLM for domain discovery.

The implementation borrows concepts from aider's RepoMap but is modified to:
1. Include all functions from all files (not just the most important ones)
2. Focus on structural relationships rather than token optimization
3. Generate a format suitable for LLM-based domain discovery
"""

import os
import sys
import time
import math
import logging
import warnings
import re
from pathlib import Path
from collections import Counter, defaultdict, namedtuple
from typing import Dict, List, Set, Tuple, Optional, Any, Union
import concurrent.futures
from functools import partial
import tempfile
import json
import yaml

# Import networkx
import networkx as nx

# Import tqdm
from tqdm import tqdm

# Import tree-sitter related modules
from grep_ast import filename_to_lang, TreeContext
# These imports might fail in some environments, but we'll handle that in the code
from tree_sitter_languages import get_language, get_parser
# try:
#     from grep_ast.tsl import get_language, get_parser
# except ImportError:
#     try:
#         from tree_sitter_languages import get_language, get_parser
#     except ImportError:
#         # Define dummy functions if imports fail
#         def get_language(lang):
#             return None

#         def get_parser(lang):
#             return None

# Import pygments
from pygments.lexers import guess_lexer_for_filename
from pygments.token import Token
from pygments.util import ClassNotFound

# tree_sitter is throwing a FutureWarning
warnings.simplefilter("ignore", category=FutureWarning)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Define Tag namedtuple for storing symbol information
Tag = namedtuple("Tag", "rel_fname fname line name kind")

class SimpleIO:
    """Simple IO class to handle file reading and tool output."""

    def read_text(self, fname):
        """Read text from a file."""
        try:
            with open(fname, 'r', encoding='utf-8', errors='replace') as f:
                return f.read()
        except Exception as e:
            logger.warning(f"Error reading file {fname}: {e}")
            return None

    def tool_output(self, message, log_only=False):
        """Output a message."""
        logger.info(message)
        if not log_only:
            print(message)

    def tool_warning(self, message):
        """Output a warning message."""
        logger.warning(message)
        print(f"WARNING: {message}")

    def tool_error(self, message):
        """Output an error message."""
        logger.error(message)
        print(f"ERROR: {message}")

class SimpleTokenCounter:
    """Simple token counter class."""

    def token_count(self, text):
        """Count tokens in text (simple approximation)."""
        if not text:
            return 0
        # Simple approximation: 4 chars per token
        return len(text) // 4

    def get_repo_map_tokens(self):
        """Get the maximum number of tokens for the repo map."""
        return 100000  # Set a high limit for the experiment

def get_scm_fname(lang):
    """
    Get the path to the tree-sitter query schema file for the given language.
    """
    # Check in all local directories
    local_paths = [
        Path(__file__).parent / "queries" / f"{lang}-tags.scm",
        Path(__file__).parent / "queries" / "tree-sitter-language-pack" / f"{lang}-tags.scm",
        Path(__file__).parent / "queries" / "tree-sitter-languages" / f"{lang}-tags.scm",
    ]

    for path in local_paths:
        if path.exists():
            return path

    # Then check in common locations for aider queries
    try:
        # Try to find aider in site-packages
        import site
        site_packages = site.getsitepackages()
        for site_pkg in site_packages:
            # Try tree-sitter-language-pack first
            path = Path(site_pkg) / "aider" / "queries" / "tree-sitter-language-pack" / f"{lang}-tags.scm"
            if path.exists():
                return path

            # Fall back to tree-sitter-languages
            path = Path(site_pkg) / "aider" / "queries" / "tree-sitter-languages" / f"{lang}-tags.scm"
            if path.exists():
                return path
    except (ImportError, KeyError):
        pass

    logger.warning(f"No query schema found for language: {lang}")
    return None

def find_src_files(directory, exclude_dirs=None, include_extensions=None, exclude_tests=False):
    """Find all source files in a directory."""
    if exclude_dirs is None:
        exclude_dirs = {'.git', '__pycache__', 'node_modules', 'venv', '.venv', 'env', '.env'}

    src_files = []
    for root, dirs, files in os.walk(directory):
        # Skip excluded directories
        dirs[:] = [d for d in dirs if d not in exclude_dirs and not d.startswith('.')]

        for file in files:
            # Skip hidden files and common non-source files
            if file.startswith('.') or file.endswith(('.pyc', '.pyo', '.so', '.o', '.a', '.dll', '.exe')):
                continue

            # Filter by extension if specified
            if include_extensions and not any(file.endswith(ext) for ext in include_extensions):
                continue

            file_path = os.path.join(root, file)
            rel_path = os.path.relpath(file_path, directory)

            # Skip test files if exclude_tests is True
            if exclude_tests and is_test_file(rel_path):
                continue

            src_files.append(file_path)

    return src_files

def is_test_file(rel_path):
    """Check if a file path is a test file based on its relative path."""
    path_parts = rel_path.split(os.sep)
    file_name = os.path.basename(rel_path).lower()

    return any([
        'test' in path_parts,
        'tests' in path_parts,
        file_name.startswith('test_'),
        file_name.endswith('_test.py'),
        'test' in file_name
    ])

class CompleteRepoMap:
    """
    Complete Repository Map Generator.

    This class generates a comprehensive map of a repository, including all functions
    from all files, with their relationships and dependencies.
    """

    def __init__(
        self,
        root=None,
        verbose=False,
        io=None,
        main_model=None,
        include_extensions=None,
        top_percentage=0.3,
        min_functions=3,
        max_functions=15,
        exclude_tests=False,
        context_lines=2,
        output_format="text",
        domain_name="default",
        # Content sampling parameters
        min_sample_lines=5,
        max_sample_lines=10,
        max_file_lines=30,  # Maximum lines to include from any file
        # Symbol importance weights
        generic_name_penalty=5,
        complexity_bonus=10,
        domain_bonus=15,
        name_length_bonus_factor=1.25,
        loc_score_factor=3,
        param_count_factor=1,
        method_bonus=3,
        # Symbol reference importance factors
        high_ref_bonus=10,
        medium_ref_bonus=5,
        pagerank_scale_factor=30,
        # Symbol graph ranking weights
        in_degree_weight=2,
        out_degree_weight=1,
        # Adaptive thresholding factors
        large_file_threshold_factor=0.5,
        medium_file_threshold_factor=0.25,
        small_file_threshold_factor=-0.5,
        # File size thresholds (in function count)
        large_file_function_count=20,
        medium_file_function_count=10,
        # Non-function tag control
        non_function_tag_percentage=0.5,
    ):
        """
        Initialize the CompleteRepoMap.

        Args:
            root: Repository root directory
            verbose: Enable verbose output
            io: IO handler for file operations
            main_model: Token counter model
            include_extensions: List of file extensions to include
            top_percentage: Percentage of top functions to keep per file (0.0-1.0)
            min_functions: Minimum number of functions to keep per file
            max_functions: Maximum number of functions to keep per file
            exclude_tests: Whether to exclude test files
            context_lines: Number of context lines to include around important lines
            output_format: Output format ("text", "json", or "yaml")
            domain_name: Domain name for JSON/YAML output (default: 'default')

            # Content sampling parameters
            min_sample_lines: Minimum number of lines to extract from files without functions
            max_sample_lines: Maximum number of lines to extract from files without functions
            max_file_lines: Maximum total lines to include from any file (functions + context)

            # Symbol importance weights
            generic_name_penalty: Score penalty for generic function names
            complexity_bonus: Score bonus for functions with complexity indicators in name
            domain_bonus: Score bonus for functions with domain-specific terms in name
            name_length_bonus_factor: Factor for scoring function name length (higher = more bonus)
            loc_score_factor: Factor for scoring function length (higher = more weight to LOC)
            param_count_factor: Factor for scoring parameter count (higher = more weight to params)
            method_bonus: Score bonus for class methods vs standalone functions

            # Symbol reference importance factors
            high_ref_bonus: Additional bonus for highly referenced symbols (PageRank > 15)
            medium_ref_bonus: Additional bonus for moderately referenced symbols (PageRank > 10)
            pagerank_scale_factor: Factor to scale PageRank scores (higher = more weight to PageRank)

            # Symbol graph ranking weights
            in_degree_weight: Weight for in-degree in symbol graph ranking (references to symbol)
            out_degree_weight: Weight for out-degree in symbol graph ranking (references from symbol)

            # Adaptive thresholding factors
            large_file_threshold_factor: Threshold factor for files with many functions
            medium_file_threshold_factor: Threshold factor for files with moderate functions
            small_file_threshold_factor: Threshold factor for files with few functions

            # File size thresholds (in function count)
            large_file_function_count: Number of functions to consider a file "large"
            medium_file_function_count: Number of functions to consider a file "medium"

            # Non-function tag control
            non_function_tag_percentage: Percentage of non-function tags to keep (0.0-1.0)
        """
        self.verbose = verbose
        self.io = io or SimpleIO()
        self.exclude_tests = exclude_tests

        if not root:
            root = os.getcwd()
        self.root = root

        self.main_model = main_model or SimpleTokenCounter()
        self.include_extensions = include_extensions

        # Basic filtering parameters
        self.top_percentage = top_percentage
        self.min_functions = min_functions
        self.max_functions = max_functions

        # Context extraction parameters
        self.context_lines = context_lines

        # Content sampling parameters
        self.min_sample_lines = min_sample_lines
        self.max_sample_lines = max_sample_lines
        self.max_file_lines = max_file_lines

        # Symbol importance weights
        self.generic_name_penalty = generic_name_penalty
        self.complexity_bonus = complexity_bonus
        self.domain_bonus = domain_bonus
        self.name_length_bonus_factor = name_length_bonus_factor
        self.loc_score_factor = loc_score_factor
        self.param_count_factor = param_count_factor
        self.method_bonus = method_bonus

        # Symbol reference importance factors
        self.high_ref_bonus = high_ref_bonus
        self.medium_ref_bonus = medium_ref_bonus
        self.pagerank_scale_factor = pagerank_scale_factor

        # Symbol graph ranking weights
        self.in_degree_weight = in_degree_weight
        self.out_degree_weight = out_degree_weight

        # Adaptive thresholding factors
        self.large_file_threshold_factor = large_file_threshold_factor
        self.medium_file_threshold_factor = medium_file_threshold_factor
        self.small_file_threshold_factor = small_file_threshold_factor

        # File size thresholds
        self.large_file_function_count = large_file_function_count
        self.medium_file_function_count = medium_file_function_count

        # Non-function tag control
        self.non_function_tag_percentage = non_function_tag_percentage

        # Output format
        if output_format not in ["text", "json", "yaml"]:
            logger.warning(f"Invalid output format: {output_format}. Using 'text' instead.")
            output_format = "text"
        self.output_format = output_format

        # Domain name for JSON/YAML output
        self.domain_name = domain_name

        # Initialize caches
        self.tags_cache = {}
        self.tree_cache = {}
        self.tree_context_cache = {}

    def get_mtime(self, fname):
        """Get the modification time of a file."""
        try:
            return os.path.getmtime(fname)
        except FileNotFoundError:
            self.io.tool_warning(f"File not found: {fname}")
            return None

    def get_tags(self, fname, rel_fname):
        """
        Get tags (definitions and references) from a file.
        Uses caching to avoid re-parsing files that haven't changed.
        """
        # Check if the file is in the cache and if the modification time has not changed
        file_mtime = self.get_mtime(fname)
        if file_mtime is None:
            return []

        cache_key = fname
        if cache_key in self.tags_cache and self.tags_cache[cache_key]["mtime"] == file_mtime:
            return self.tags_cache[cache_key]["data"]

        # Cache miss - parse the file
        data = list(self.get_tags_raw(fname, rel_fname))

        # Update the cache
        self.tags_cache[cache_key] = {"mtime": file_mtime, "data": data}

        return data

    def get_tags_raw(self, fname, rel_fname):
        """
        Extract tags (definitions and references) from a file using tree-sitter.
        """
        lang = filename_to_lang(fname)
        if not lang:
            return

        # Read the file content
        code = self.io.read_text(fname)
        if not code:
            return

        # Try to use tree-sitter if available
        try:
            language = get_language(lang)
            parser = get_parser(lang)

            if language is None or parser is None:
                raise ImportError("Tree-sitter language or parser not available")

            query_scm_path = get_scm_fname(lang)
            if not query_scm_path or not query_scm_path.exists():
                raise ImportError(f"No query schema found for language: {lang}")

            query_scm = query_scm_path.read_text()

            tree = parser.parse(bytes(code, "utf-8"))

            # Run the tags queries
            query = language.query(query_scm)
            captures = query.captures(tree.root_node)

            saw = set()
            all_nodes = []

            # Handle different tree-sitter versions
            try:
                # For newer tree-sitter versions
                for tag, nodes in captures.items():
                    all_nodes += [(node, tag) for node in nodes]
            except AttributeError:
                # For older tree-sitter versions
                all_nodes = list(captures)

            for node, tag in all_nodes:
                if tag.startswith("name.definition."):
                    kind = "def"
                elif tag.startswith("name.reference."):
                    kind = "ref"
                else:
                    continue

                saw.add(kind)

                result = Tag(
                    rel_fname=rel_fname,
                    fname=fname,
                    name=node.text.decode("utf-8"),
                    kind=kind,
                    line=node.start_point[0],
                )

                yield result

            # If we didn't find any references, try to extract them using pygments
            if "ref" not in saw and "def" in saw:
                raise ImportError("No references found, falling back to pygments")

        except (ImportError, Exception) as err:
            # Fall back to pygments for basic extraction
            logger.warning(f"Using fallback extraction for {fname}: {err}")

            # Try to extract function definitions using simple regex
            import re

            # Simple patterns for common languages
            patterns = {
                # Common languages
                'python': r'def\s+([a-zA-Z0-9_]+)\s*\(',
                'javascript': r'(function\s+([a-zA-Z0-9_]+)|([a-zA-Z0-9_]+)\s*=\s*function|\s*([a-zA-Z0-9_]+)\s*\([^)]*\)\s*{)',
                'typescript': r'(function\s+([a-zA-Z0-9_]+)|([a-zA-Z0-9_]+)\s*=\s*function|\s*([a-zA-Z0-9_]+)\s*\([^)]*\)\s*{|\s*([a-zA-Z0-9_]+)\s*\([^)]*\)\s*:\s*[a-zA-Z0-9_<>[\]]+)',
                'java': r'(public|private|protected|static|\s) +[\w\<\>\[\]]+\s+([a-zA-Z0-9_]+)\s*\(',
                'cpp': r'([\w\<\>\[\]]+)\s+([a-zA-Z0-9_:]+)::[a-zA-Z0-9_]+\s*\(|^[\s]*([a-zA-Z0-9_]+)\s*\(|^[\s]*([\w\<\>\[\]]+)\s+([a-zA-Z0-9_]+)\s*\([^=]*$',
                'c': r'([\w\<\>\[\]]+)\s+([a-zA-Z0-9_]+)\s*\([^=]*$',
                'csharp': r'(public|private|protected|internal|static|\s) +[\w\<\>\[\]]+\s+([a-zA-Z0-9_]+)\s*\(',
                'go': r'func\s+([a-zA-Z0-9_]+)\s*\(',
                'ruby': r'(def|define_method)\s+([a-zA-Z0-9_?!]+)',
                'php': r'function\s+([a-zA-Z0-9_]+)\s*\(',
                'rust': r'fn\s+([a-zA-Z0-9_]+)\s*\(',
                'swift': r'func\s+([a-zA-Z0-9_]+)\s*\(',
                'kotlin': r'fun\s+([a-zA-Z0-9_]+)\s*\(',
                'scala': r'def\s+([a-zA-Z0-9_]+)\s*\(',
                'dart': r'([a-zA-Z0-9_<>]+)\s+([a-zA-Z0-9_]+)\s*\(',

                # Functional languages
                'lua': r'function\s+([a-zA-Z0-9_.:]+)\s*\(',
                'r': r'([a-zA-Z0-9_.]+)\s*<-\s*function\s*\(',
                'elixir': r'def\s+([a-zA-Z0-9_?!]+)',
                'clojure': r'\(defn\s+([a-zA-Z0-9_-]+)',
                'haskell': r'([a-zA-Z0-9_]+)\s+::|([a-zA-Z0-9_]+)\s+[a-zA-Z0-9_]+\s+=',
                'commonlisp': r'\(defun\s+([a-zA-Z0-9_-]+)',
                'racket': r'\(define\s+\(([a-zA-Z0-9_-]+)',

                # Other languages
                'd': r'([a-zA-Z0-9_]+)\s+([a-zA-Z0-9_]+)\s*\(',
                'solidity': r'function\s+([a-zA-Z0-9_]+)\s*\(',
                'elm': r'([a-zA-Z0-9_]+)\s+:',
                'ocaml': r'let\s+([a-zA-Z0-9_]+)',
            }

            pattern = patterns.get(lang, r'([a-zA-Z0-9_]+)\s*\(')

            # Class patterns for common languages
            class_patterns = {
                'python': r'class\s+([a-zA-Z0-9_]+)',
                'javascript': r'class\s+([a-zA-Z0-9_]+)|var\s+([a-zA-Z0-9_]+)\s*=\s*\{',
                'typescript': r'class\s+([a-zA-Z0-9_]+)|interface\s+([a-zA-Z0-9_]+)',
                'java': r'(public|private|protected|\s)?\s*class\s+([a-zA-Z0-9_]+)',
                'cpp': r'class\s+([a-zA-Z0-9_]+)|struct\s+([a-zA-Z0-9_]+)',
                'c': r'struct\s+([a-zA-Z0-9_]+)|typedef\s+struct\s+([a-zA-Z0-9_]+)',
                'csharp': r'(public|private|protected|internal|\s)?\s*class\s+([a-zA-Z0-9_]+)',
                'go': r'type\s+([a-zA-Z0-9_]+)\s+struct',
                'ruby': r'class\s+([a-zA-Z0-9_:]+)|module\s+([a-zA-Z0-9_:]+)',
                'php': r'class\s+([a-zA-Z0-9_]+)',
                'rust': r'struct\s+([a-zA-Z0-9_]+)|enum\s+([a-zA-Z0-9_]+)|trait\s+([a-zA-Z0-9_]+)',
                'swift': r'class\s+([a-zA-Z0-9_]+)|struct\s+([a-zA-Z0-9_]+)|protocol\s+([a-zA-Z0-9_]+)',
                'kotlin': r'class\s+([a-zA-Z0-9_]+)|interface\s+([a-zA-Z0-9_]+)',
                'scala': r'class\s+([a-zA-Z0-9_]+)|object\s+([a-zA-Z0-9_]+)|trait\s+([a-zA-Z0-9_]+)',
                'dart': r'class\s+([a-zA-Z0-9_]+)',
                'elixir': r'defmodule\s+([a-zA-Z0-9_.]+)',
                'solidity': r'contract\s+([a-zA-Z0-9_]+)',
            }

            class_pattern = class_patterns.get(lang, r'class\s+([a-zA-Z0-9_]+)')

            # Find function and class definitions
            for i, line in enumerate(code.splitlines()):
                # Check for function definitions
                func_matches = re.findall(pattern, line)
                if func_matches:
                    for match in func_matches:
                        # For some patterns, the function name is in the second group
                        name = match[1] if isinstance(match, tuple) and len(match) > 1 else match

                        yield Tag(
                            rel_fname=rel_fname,
                            fname=fname,
                            name=name,
                            kind="def",
                            line=i,
                        )

                # Check for class definitions
                class_matches = re.findall(class_pattern, line)
                if class_matches:
                    for match in class_matches:
                        # For some patterns, the class name is in the second group
                        name = match[1] if isinstance(match, tuple) and len(match) > 1 else match

                        yield Tag(
                            rel_fname=rel_fname,
                            fname=fname,
                            name=name,
                            kind="def",
                            line=i,
                        )

            # Try to extract references using pygments
            try:
                lexer = guess_lexer_for_filename(fname, code)
                tokens = list(lexer.get_tokens(code))
                name_tokens = [token[1] for token in tokens if token[0] in Token.Name]

                # Get unique tokens to avoid too many references
                unique_tokens = set(name_tokens)

                for token in unique_tokens:
                    if token and len(token) > 1:  # Skip single-character tokens
                        yield Tag(
                            rel_fname=rel_fname,
                            fname=fname,
                            name=token,
                            kind="ref",
                            line=-1,
                        )
            except (ClassNotFound, Exception) as e:
                logger.warning(f"Error lexing {fname}: {e}")

    def get_ranked_tags(self, all_files, progress=None, mentioned_idents=None):
        """
        Get all tags from all files and build a graph of references.
        Uses PageRank to determine the importance of symbols and files.
        Also includes files without functions by creating placeholder tags.

        Args:
            all_files: List of files to process
            progress: Progress callback function
            mentioned_idents: Set of identifiers mentioned in the conversation

        Returns:
            Tuple of (all_tags, graph)
        """
        if mentioned_idents is None:
            mentioned_idents = set()

        defines = defaultdict(set)  # name -> set of files that define it
        references = defaultdict(list)  # name -> list of files that reference it
        definitions = defaultdict(set)  # (file, name) -> set of Tag objects
        files_without_tags = set()  # Set to track files without tags

        # Process all files to extract tags
        for fname in tqdm(all_files, desc="Processing files"):
            if progress:
                progress()

            rel_fname = os.path.relpath(fname, self.root)

            tags = list(self.get_tags(fname, rel_fname))
            if not tags:
                # Track files without tags for later inclusion
                files_without_tags.add((rel_fname, fname))
                continue

            for tag in tags:
                if tag.kind == "def":
                    defines[tag.name].add(rel_fname)
                    key = (rel_fname, tag.name)
                    definitions[key].add(tag)
                elif tag.kind == "ref":
                    references[tag.name].append(rel_fname)

        # If no references were found, use definitions as references
        if not references:
            references = dict((k, list(v)) for k, v in defines.items())

        # Build a graph of references
        G = nx.MultiDiGraph()

        # Add all files as nodes
        for fname in all_files:
            rel_fname = os.path.relpath(fname, self.root)
            G.add_node(rel_fname, type="FILE")

        # Set of identifiers that are defined in the codebase
        idents = set(defines.keys())

        # Add a small self-edge for every definition that has no references
        for ident in defines.keys():
            if ident in references:
                continue
            for definer in defines[ident]:
                G.add_edge(definer, definer, weight=0.1, ident=ident)

        # Process identifiers that have both definitions and references
        for ident in idents:
            if progress:
                progress()

            definers = defines[ident]

            # Apply importance multipliers based on identifier characteristics
            mul = 1.0

            # Boost identifiers mentioned in the conversation
            if ident in mentioned_idents:
                mul *= 10

            # Boost identifiers with meaningful names (snake_case or camelCase)
            is_snake = ("_" in ident) and any(c.isalpha() for c in ident)
            is_camel = any(c.isupper() for c in ident) and any(c.islower() for c in ident)
            if (is_snake or is_camel) and len(ident) >= 8:
                mul *= 5

            # Reduce importance of private/internal identifiers
            if ident.startswith("_"):
                mul *= 0.2

            # Reduce importance of very common identifiers (defined in many files)
            if len(defines[ident]) > 5:
                mul *= 0.5

            # Add edges for each reference with appropriate weights
            for referencer, num_refs in Counter(references.get(ident, [])).items():
                for definer in definers:
                    # Scale down so high frequency mentions don't dominate
                    num_refs = math.sqrt(num_refs)
                    G.add_edge(referencer, definer, weight=mul * num_refs, ident=ident)

        # Calculate PageRank to determine importance
        try:
            ranked = nx.pagerank(G, weight="weight")
        except (ZeroDivisionError, nx.PowerIterationFailedConvergence):
            # Handle potential PageRank calculation errors
            try:
                # Try with default weight
                ranked = nx.pagerank(G)
            except:
                # If that fails too, use a simple degree-based ranking
                ranked = {node: G.degree(node) for node in G.nodes()}

        # Distribute the rank from each source node across all of its out edges
        ranked_definitions = defaultdict(float)
        for src in G.nodes:
            if progress:
                progress()

            src_rank = ranked.get(src, 0)
            total_weight = sum(data["weight"] for _, _, data in G.out_edges(src, data=True)) or 1

            for _, dst, data in G.out_edges(src, data=True):
                data["rank"] = src_rank * data["weight"] / total_weight
                ident = data["ident"]
                ranked_definitions[(dst, ident)] += data["rank"]

        # Sort definitions by rank
        ranked_definitions = sorted(
            ranked_definitions.items(), reverse=True, key=lambda x: (x[1], x[0])
        )

        # Collect all tags with their ranks
        all_tags = []
        seen_files = set()

        # First add tags from ranked definitions
        for (fname, ident), rank in ranked_definitions:
            seen_files.add(fname)
            for tag in definitions.get((fname, ident), []):
                # Store the rank with the tag for later use
                tag = tag._replace(kind=f"{tag.kind}:{rank:.6f}")
                all_tags.append(tag)

        # Add remaining files that weren't included in the ranked definitions
        for fname in G.nodes:
            if fname not in seen_files:
                seen_files.add(fname)
                # Add a dummy tag for this file
                all_tags.append((fname,))

        # Create placeholder tags for files without functions
        for rel_fname, abs_fname in files_without_tags:
            if rel_fname not in seen_files:
                # Create a placeholder tag with a special name
                placeholder_tag = Tag(
                    rel_fname=rel_fname,
                    fname=abs_fname,
                    name="__file_content__",  # Special name to identify placeholder
                    kind="placeholder",       # Special kind to identify placeholder
                    line=-1                   # Invalid line number
                )
                all_tags.append(placeholder_tag)

        return all_tags, G

    def render_tree(self, abs_fname, rel_fname, lois, clean_format=False):
        """
        Render a tree structure of a file with intelligent context extraction around
        important lines, using TreeContext to provide meaningful code snippets.

        Args:
            abs_fname: Absolute file path
            rel_fname: Relative file path
            lois: Lines of interest
            clean_format: If True, returns clean code without vertical bars and separators
        """
        mtime = self.get_mtime(abs_fname)
        key = (rel_fname, tuple(sorted(lois)), mtime, clean_format)

        if key in self.tree_cache:
            return self.tree_cache[key]

        if (
            rel_fname not in self.tree_context_cache
            or self.tree_context_cache[rel_fname]["mtime"] != mtime
        ):
            # Read the file content
            code = self.io.read_text(abs_fname) or ""
            if not code.endswith("\n"):
                code += "\n"

            # Create a TreeContext object for intelligent context extraction
            context = TreeContext(
                rel_fname,
                code,
                color=False,
                line_number=False,
                child_context=False,  # Include child context for better understanding
                last_line=False,
                margin=0,
                mark_lois=False,
                loi_pad=self.context_lines,  # Use the configured context lines
                show_top_of_file_parent_scope=False,
            )
            self.tree_context_cache[rel_fname] = {"context": context, "mtime": mtime}

        # Get the context object from cache
        context = self.tree_context_cache[rel_fname]["context"]

        # Reset lines of interest
        context.lines_of_interest = set()

        # Add the lines of interest
        context.add_lines_of_interest(lois)

        # Add context around the lines of interest
        context.add_context()

        # Format the result
        tree = context.format()

        if clean_format:
            # For clean format, just return the raw code without any formatting characters
            # This is used for JSON/YAML output
            return tree
        else:
            # Add vertical bar prefix to each line for consistent formatting
            result = []
            result.append("⋮...")
            for line in tree.splitlines():
                # Skip empty lines
                if line.strip():
                    result.append(f"│{line}")
            result.append("⋮...")
            result = "\n".join(result)

            # Cache and return the result
            self.tree_cache[key] = result
            return result

    def extract_file_content_sample(self, abs_fname, min_lines=None, max_lines=None, clean_format=False):
        """
        Extract a sample of non-empty lines from a file.

        Args:
            abs_fname: Absolute path to the file
            min_lines: Minimum number of lines to extract (defaults to self.min_sample_lines)
            max_lines: Maximum number of lines to extract (defaults to self.max_sample_lines)
            clean_format: If True, returns clean code without vertical bars

        Returns:
            List of formatted lines from the file or a single string if clean_format=True
        """
        # Use instance parameters if not explicitly provided
        if min_lines is None:
            min_lines = self.min_sample_lines
        if max_lines is None:
            max_lines = self.max_sample_lines

        # Read the file content
        code = self.io.read_text(abs_fname) or ""
        if not code:
            return [] if not clean_format else ""

        # Split into lines
        lines = code.splitlines()

        # Filter out empty lines
        non_empty_lines = [line for line in lines if line.strip() and not line.strip().startswith('#')]

        if not non_empty_lines:
            return [] if not clean_format else ""

        # Determine how many lines to extract
        num_lines = min(max(min_lines, len(non_empty_lines) // 2), max_lines, len(non_empty_lines))

        # Get lines from the top and middle of the file
        top_lines = non_empty_lines[:num_lines//2]

        # Calculate middle index
        middle_start = len(non_empty_lines) // 2 - (num_lines - len(top_lines)) // 2
        middle_end = middle_start + (num_lines - len(top_lines))
        middle_lines = non_empty_lines[middle_start:middle_end]

        # Combine the lines
        sample_lines = top_lines + middle_lines

        if clean_format:
            # Return as a single string without formatting
            return "\n".join(sample_lines)
        else:
            # Format the lines with the vertical bar prefix
            formatted_lines = []
            for line in sample_lines:
                # Truncate very long lines
                if len(line) > 80:
                    line = line[:77] + "..."
                formatted_lines.append(f"│{line}")
            return formatted_lines

    def to_json(self, tags):
        """
        Convert a list of tags to a JSON representation with clean code content.

        Args:
            tags: List of Tag objects

        Returns:
            Dictionary with file paths as keys and code content as values
        """
        if not tags:
            return {}

        # Group tags by file for better organization
        file_tags = defaultdict(list)
        file_ranks = defaultdict(float)

        # Process all tags except tuples (which are dummy tags)
        for tag in tags:
            if isinstance(tag, Tag):
                rel_fname = tag.rel_fname
                file_tags[rel_fname].append(tag)

                # Extract rank from tag kind if available
                if ":" in tag.kind:
                    _, rank_str = tag.kind.split(":", 1)
                    try:
                        rank = float(rank_str)
                        file_ranks[rel_fname] = max(file_ranks[rel_fname], rank)
                    except ValueError:
                        pass
            elif isinstance(tag, tuple) and tag[0] is not None:
                # Handle dummy tags for files without definitions
                file_tags[tag[0]].append(tag)

        # Sort files by rank (most important first)
        sorted_files = sorted(file_tags.keys(), key=lambda f: file_ranks.get(f, 0), reverse=True)

        # Create a flat structure with file paths as keys
        result = {}

        # Process each file
        for rel_fname in sorted_files:
            if not rel_fname:
                continue

            # Get the absolute file path
            abs_fname = None
            lois = []
            is_placeholder_file = False

            # Process tags for this file
            for tag in file_tags[rel_fname]:
                if isinstance(tag, Tag):
                    abs_fname = tag.fname
                    # Check if this is a placeholder tag for a file without functions
                    if tag.kind == "placeholder" and tag.name == "__file_content__":
                        is_placeholder_file = True
                    else:
                        # Extract the base kind without the rank
                        base_kind = tag.kind.split(":", 1)[0] if ":" in tag.kind else tag.kind
                        if base_kind != "placeholder":
                            lois.append(tag.line)

            # Skip if we couldn't determine the absolute file path
            if not abs_fname:
                continue

            # Extract the code content
            if is_placeholder_file:
                # For files without functions, extract a sample of the content
                code_content = self.extract_file_content_sample(abs_fname, clean_format=True)
            else:
                # Regular file with functions
                code_content = self.render_tree(abs_fname, rel_fname, lois, clean_format=True)

            # Clean up any remaining formatting characters
            if isinstance(code_content, str):
                # Remove vertical bars and ellipses
                code_content = code_content.replace("│", "").replace("⋮...", "")
                # Remove any lines that are just whitespace
                lines = [line for line in code_content.splitlines() if line.strip()]

                # Limit the total number of lines per file
                if len(lines) > self.max_file_lines:
                    # Take lines from the beginning and end
                    half = self.max_file_lines // 2
                    lines = lines[:half] + ["..."] + lines[-half:]

                # Instead of joining with \n, we'll create an array of lines
                # This will make the JSON output preserve formatting when viewed
                code_content = lines

            # Add the file to the result directly (no domain nesting)
            result[rel_fname] = code_content

        return result

    def to_tree(self, tags):
        """
        Convert a list of tags to an enhanced tree representation with intelligent context
        extraction around important lines, providing meaningful code snippets.
        """
        if not tags:
            return ""

        # If output format is not text, use the appropriate format
        if self.output_format != "text":
            # Convert to the appropriate format
            if self.output_format == "json":
                return json.dumps(self.to_json(tags), indent=2)
            elif self.output_format == "yaml":
                return yaml.dump(self.to_json(tags), default_flow_style=False)

        # Otherwise, use the traditional text format
        cur_fname = None
        cur_abs_fname = None
        lois = None
        output = ""
        is_placeholder_file = False

        # Group tags by file for better organization
        file_tags = defaultdict(list)
        file_ranks = defaultdict(float)

        # Process all tags except tuples (which are dummy tags)
        for tag in tags:
            if isinstance(tag, Tag):
                rel_fname = tag.rel_fname
                file_tags[rel_fname].append(tag)

                # Extract rank from tag kind if available
                if ":" in tag.kind:
                    _, rank_str = tag.kind.split(":", 1)  # We don't need the kind part
                    try:
                        rank = float(rank_str)
                        file_ranks[rel_fname] = max(file_ranks[rel_fname], rank)
                    except ValueError:
                        pass
            elif isinstance(tag, tuple) and tag[0] is not None:
                # Handle dummy tags for files without definitions
                file_tags[tag[0]].append(tag)

        # Sort files by rank (most important first)
        sorted_files = sorted(file_tags.keys(), key=lambda f: file_ranks.get(f, 0), reverse=True)

        # Add a dummy file at the end to trigger processing of the last real file
        sorted_files.append(None)

        # Process each file
        for this_rel_fname in sorted_files:
            # If we've moved to a new file, process the previous file
            if this_rel_fname != cur_fname:
                if lois is not None:
                    output += "\n"
                    if cur_fname:
                        # Format file name with a colon at the end and add vertical bar
                        output += f"│{cur_fname}:\n⋮...\n"

                    if is_placeholder_file:
                        # For files without functions, extract a sample of the content
                        content_lines = self.extract_file_content_sample(cur_abs_fname)
                        if content_lines:
                            output += '\n'.join(content_lines) + '\n⋮...\n'
                        else:
                            output += "⋮...\n"
                        is_placeholder_file = False
                    else:
                        # Regular file with functions
                        file_content = self.render_tree(cur_abs_fname, cur_fname, lois)
                        # Only add the content between the separators (skip the separators)
                        if file_content.strip():
                            content_lines = file_content.split('\n')
                            if len(content_lines) > 2:  # Only if there's content between separators
                                # Skip the first and last lines (separators)
                                clean_content = '\n'.join(content_lines[1:-1])
                                output += clean_content + '\n⋮...\n'
                    lois = None
                elif cur_fname:
                    # For files without functions, extract a sample of the content
                    output += f"\n│{cur_fname}:\n⋮...\n"
                    if cur_abs_fname:
                        # Extract a sample of lines from the file
                        content_lines = self.extract_file_content_sample(cur_abs_fname)
                        if content_lines:
                            output += '\n'.join(content_lines) + '\n⋮...\n'
                        else:
                            output += "⋮...\n"
                    else:
                        output += "⋮...\n"

                # Skip the dummy file at the end
                if this_rel_fname is None:
                    break

                # Initialize for the new file
                lois = []
                cur_fname = this_rel_fname

                # Process tags for this file
                for tag in file_tags[this_rel_fname]:
                    if isinstance(tag, Tag):
                        cur_abs_fname = tag.fname
                        # Check if this is a placeholder tag for a file without functions
                        if tag.kind == "placeholder" and tag.name == "__file_content__":
                            is_placeholder_file = True
                        else:
                            # Extract the base kind without the rank
                            base_kind = tag.kind.split(":", 1)[0] if ":" in tag.kind else tag.kind
                            if base_kind != "placeholder":
                                lois.append(tag.line)
                    elif isinstance(tag, tuple) and len(tag) == 1:
                        # This is a dummy tag for a file without definitions
                        # We'll handle it in the next iteration
                        pass

        return output

    def token_count(self, text):
        """Count tokens in text."""
        if self.main_model:
            return self.main_model.token_count(text)
        return len(text.split())

    def build_symbol_graph(self, all_tags):
        """
        Build a graph of symbol references to determine conceptual importance.

        This graph connects symbols (functions, classes) based on their references,
        allowing us to identify which symbols are central to the codebase's architecture.

        Args:
            all_tags: List of Tag objects

        Returns:
            Tuple of (symbol_graph, symbol_ranks)
        """
        # Extract all definitions and references
        symbol_defs = {}  # symbol -> list of tags that define it
        symbol_refs = defaultdict(list)  # symbol -> list of symbols that reference it

        # First pass: collect all definitions
        for tag in all_tags:
            if not isinstance(tag, Tag):
                continue

            if tag.kind == "def" or tag.kind.startswith("def:"):
                # Extract base kind without rank if present
                base_kind = tag.kind.split(":", 1)[0] if ":" in tag.kind else tag.kind
                if base_kind == "def":
                    symbol_defs[tag.name] = tag

        # Second pass: collect all references
        for tag in all_tags:
            if not isinstance(tag, Tag):
                continue

            if tag.kind == "ref" or tag.kind.startswith("ref:"):
                # If this reference points to a defined symbol
                if tag.name in symbol_defs:
                    # Get the file this reference is in
                    ref_file = tag.rel_fname

                    # Find all symbols defined in this file
                    for symbol, def_tag in symbol_defs.items():
                        if def_tag.rel_fname == ref_file:
                            # This symbol might be referencing the other symbol
                            symbol_refs[symbol].append(tag.name)

        # Build a directed graph of symbol references
        symbol_graph = nx.DiGraph()

        # Add nodes for all defined symbols
        for symbol in symbol_defs:
            symbol_graph.add_node(symbol)

        # Add edges for references
        for symbol, refs in symbol_refs.items():
            for ref in refs:
                if ref in symbol_graph and symbol in symbol_graph:
                    # Add an edge from the symbol to what it references
                    symbol_graph.add_edge(symbol, ref)

        # Calculate symbol importance based on in-degree and out-degree
        # This is more robust than PageRank for potentially disconnected graphs
        symbol_ranks = {}
        for node in symbol_graph.nodes():
            # Calculate importance based on connectivity
            # In-degree: how many symbols reference this one (more is better)
            # Out-degree: how many other symbols this one references (more is better)
            in_degree = len(list(symbol_graph.predecessors(node)))
            out_degree = len(list(symbol_graph.successors(node)))

            # Symbols that are both referenced a lot and reference others are important
            # Use configurable weights for in-degree and out-degree
            symbol_ranks[node] = (in_degree * self.in_degree_weight) + (out_degree * self.out_degree_weight)

        # Normalize ranks to a 0-20 scale for easier integration with other scores
        if symbol_ranks:
            max_rank = max(symbol_ranks.values())
            min_rank = min(symbol_ranks.values())
            rank_range = max_rank - min_rank

            if rank_range > 0:
                normalized_ranks = {
                    symbol: 20 * (rank - min_rank) / rank_range
                    for symbol, rank in symbol_ranks.items()
                }
            else:
                # If all ranks are the same, give them a middle value
                normalized_ranks = {symbol: 10 for symbol in symbol_ranks}
        else:
            normalized_ranks = {}

        return symbol_graph, normalized_ranks

    def filter_important_functions(self, all_tags, top_percentage=None, min_functions=None, max_functions=None):
        """
        Filter the most important functions from each file based on various heuristics.

        Args:
            all_tags: List of Tag objects
            top_percentage: Percentage of top functions to keep per file (default: self.top_percentage)
            min_functions: Minimum number of functions to keep per file (default: self.min_functions)
            max_functions: Maximum number of functions to keep per file (default: self.max_functions)

        Returns:
            List of filtered Tag objects
        """
        # Use instance parameters if not explicitly provided
        if top_percentage is None:
            top_percentage = self.top_percentage
        if min_functions is None:
            min_functions = self.min_functions
        if max_functions is None:
            max_functions = self.max_functions

        # Build symbol graph to determine conceptual importance
        _, symbol_ranks = self.build_symbol_graph(all_tags)

        # Group tags by file
        file_tags = defaultdict(list)
        for tag in all_tags:
            if isinstance(tag, Tag):
                file_tags[tag.rel_fname].append(tag)

        # Generic function names that are less likely to be important
        generic_names = {
            'get', 'set', 'init', 'setup', 'create', 'delete', 'update', 'add', 'remove',
            'start', 'stop', 'run', 'execute', 'perform', 'do', 'make', 'build', 'construct',
            'initialize', 'finalize', 'clean', 'clear', 'reset', 'handle', 'process', 'check',
            'validate', 'verify', 'test', 'is', 'has', 'can', 'should', 'will', 'did', 'was',
            'to', 'from', 'with', 'without', 'for', 'of', 'on', 'in', 'at', 'by', 'as'
        }

        # Words that indicate complexity/importance in function names
        complexity_indicators = {
            'process', 'analyze', 'calculate', 'compute', 'transform', 'convert', 'generate',
            'optimize', 'synchronize', 'authenticate', 'authorize', 'validate', 'serialize',
            'deserialize', 'encrypt', 'decrypt', 'compress', 'decompress', 'aggregate',
            'orchestrate', 'coordinate', 'dispatch', 'schedule', 'prioritize', 'allocate',
            'deallocate', 'reconcile', 'merge', 'split', 'partition', 'segment', 'classify',
            'categorize', 'normalize', 'denormalize', 'interpolate', 'extrapolate', 'predict',
            'forecast', 'simulate', 'emulate', 'render', 'compile', 'decompile', 'transpile',
            'parse', 'tokenize', 'lex', 'index', 'search', 'query', 'filter', 'sort', 'rank'
        }

        # Business logic and domain-specific terms that indicate conceptual importance
        domain_indicators = {
            'service', 'controller', 'manager', 'orchestrator', 'coordinator',
            'processor', 'engine', 'workflow', 'pipeline', 'policy', 'rule',
            'domain', 'business', 'model', 'entity', 'repository', 'factory',
            'strategy', 'adapter', 'facade', 'mediator', 'observer', 'command'
        }

        filtered_tags = []

        # Process each file
        for rel_fname, tags in file_tags.items():
            # Skip if no tags
            if not tags:
                continue

            # Get the file content to calculate LOC for each function
            abs_fname = os.path.join(self.root, rel_fname)
            code = self.io.read_text(abs_fname) or ""
            lines = code.splitlines()

            # Calculate importance score for each tag
            tag_scores = []
            function_tags = []

            # First, collect all function definition tags
            for tag in tags:
                if tag.kind == "def" or tag.kind.startswith("def:"):
                    function_tags.append(tag)

            # Score each function definition
            for tag in function_tags:
                # Initialize score
                score = 0

                # 1. Function name specificity
                name = tag.name.lower()

                # Penalize generic names
                if name in generic_names or any(name.startswith(g + '_') for g in generic_names):
                    score -= self.generic_name_penalty

                # Bonus for complexity indicators
                if any(indicator in name for indicator in complexity_indicators):
                    score += self.complexity_bonus

                # Bonus for domain-specific indicators
                if any(indicator in name for indicator in domain_indicators):
                    score += self.domain_bonus

                # Bonus for longer, more specific names (but not too long)
                name_length = len(name)
                if 8 <= name_length <= 25:
                    score += min(name_length // 4, 5) * self.name_length_bonus_factor  # Bonus for good length names

                # Get the line number from the tag
                line_num = tag.line

                # 2. Estimate function LOC and complexity
                try:
                    # Find the function in the code
                    if line_num >= 0 and line_num < len(lines):
                        # Count indentation of the function definition
                        def_line = lines[line_num]
                        indentation = len(def_line) - len(def_line.lstrip())

                        # Count lines until we find a line with same or less indentation
                        # (excluding blank lines and comments)
                        loc = 0
                        for i in range(line_num + 1, len(lines)):
                            line = lines[i]
                            if not line.strip() or line.strip().startswith('#'):
                                continue

                            line_indent = len(line) - len(line.lstrip())
                            if line_indent <= indentation:
                                break

                            loc += 1

                        # Score based on LOC (higher score for longer functions, but with diminishing returns)
                        if loc > 0:
                            score += min(10, math.log2(loc) * self.loc_score_factor)  # Score based on function length
                except Exception:
                    # If we can't determine LOC, don't adjust score
                    pass

                # 3. Parameter count (if available)
                try:
                    if line_num >= 0 and line_num < len(lines):
                        def_line = lines[line_num]
                        # Extract parameters between parentheses
                        params_match = re.search(r'\((.*?)\)', def_line)
                        if params_match:
                            params = params_match.group(1).split(',')
                            param_count = len([p for p in params if p.strip()])
                            # More parameters often indicate more complex functions
                            score += min(5, param_count) * self.param_count_factor  # Score based on parameter count
                except Exception:
                    pass

                # 4. Class method vs standalone function
                try:
                    if line_num >= 0 and line_num < len(lines):
                        def_line = lines[line_num]
                        # Check if this is a method (indented)
                        if def_line.strip().startswith(("def ", "async def ")) and def_line.lstrip() != def_line:
                            # It's likely a method, check if it's not a dunder method
                            if not (name.startswith('__') and name.endswith('__')):
                                score += self.method_bonus  # Bonus for non-dunder methods
                except Exception:
                    pass

                # 5. Symbol importance from cross-references (PageRank)
                if tag.name in symbol_ranks:
                    # Add the normalized PageRank score (0-20 scale)
                    symbol_score = symbol_ranks[tag.name]
                    score += symbol_score

                    # Give an extra boost to highly referenced symbols
                    if symbol_score > 15:
                        score += self.high_ref_bonus  # Significant boost for very important symbols
                    elif symbol_score > 10:
                        score += self.medium_ref_bonus  # Moderate boost for important symbols

                # 6. Extract rank from tag kind if available (from PageRank in get_ranked_tags)
                if ":" in tag.kind:
                    _, rank_str = tag.kind.split(":", 1)
                    try:
                        rank = float(rank_str)
                        # Scale the rank to have more impact
                        score += rank * self.pagerank_scale_factor
                    except ValueError:
                        pass

                tag_scores.append((tag, score))

            # Sort tags by score (descending)
            tag_scores.sort(key=lambda x: x[1], reverse=True)

            # Determine how many functions to keep
            num_functions = len(tag_scores)

            # If we have functions in this file
            if num_functions > 0:
                # Calculate score statistics for adaptive thresholding
                scores = [score for _, score in tag_scores]
                mean_score = sum(scores) / len(scores)

                # Calculate standard deviation
                variance = sum((s - mean_score) ** 2 for s in scores) / len(scores)
                std_dev = variance ** 0.5

                # Set threshold based on mean and standard deviation
                # More selective for files with many functions
                if num_functions > self.large_file_function_count:
                    threshold = mean_score + self.large_file_threshold_factor * std_dev
                elif num_functions > self.medium_file_function_count:
                    threshold = mean_score + self.medium_file_threshold_factor * std_dev
                else:
                    threshold = mean_score + self.small_file_threshold_factor * std_dev  # Less selective for files with few functions

                # Apply both percentage-based and threshold-based filtering
                percentage_count = int(num_functions * top_percentage)

                # Always keep at least min_functions (or all if there are fewer)
                # and at most max_functions
                keep_count = min(max(min_functions, percentage_count), max_functions)
                keep_count = min(keep_count, num_functions)  # Can't keep more than we have

                # First, keep functions that are in the top percentage (respecting max_functions)
                top_functions = []
                for i, (tag, score) in enumerate(tag_scores):
                    if i < keep_count:
                        top_functions.append(tag)

                # Then, add functions above threshold, but only until we reach max_functions
                threshold_functions = []
                for i, (tag, score) in enumerate(tag_scores):
                    if i >= keep_count and score > threshold:
                        threshold_functions.append((tag, score))

                # Sort threshold functions by score (highest first)
                threshold_functions.sort(key=lambda x: x[1], reverse=True)

                # Add threshold functions until we reach max_functions
                remaining_slots = max_functions - len(top_functions)
                for i, (tag, _) in enumerate(threshold_functions):
                    if i < remaining_slots:
                        top_functions.append(tag)

                # Add all selected functions to filtered_tags
                filtered_tags.extend(top_functions)

                kept_count = len(top_functions)
                logger.debug(f"File {rel_fname}: Kept {kept_count}/{num_functions} functions")
            else:
                # No functions in this file, but we still want to include the file
                logger.debug(f"File {rel_fname}: No functions found")

            # Filter non-function tags based on non_function_tag_percentage
            non_function_tags = []
            for tag in tags:
                if not (tag.kind == "def" or tag.kind.startswith("def:")):
                    non_function_tags.append(tag)

            # Sort non-function tags by some criteria (e.g., alphabetically by name)
            # This ensures consistent filtering across runs
            non_function_tags.sort(key=lambda tag: tag.name)

            # Keep only a percentage of non-function tags
            keep_count = int(len(non_function_tags) * self.non_function_tag_percentage)
            filtered_tags.extend(non_function_tags[:keep_count])

        return filtered_tags

    def process_batch(self, batch_files, batch_num, total_batches, temp_dir, mentioned_idents=None):
        """
        Process a single batch of files and save results to temporary files.

        Args:
            batch_files: List of files to process in this batch
            batch_num: Batch number (for logging)
            total_batches: Total number of batches (for logging)
            temp_dir: Directory to save temporary results
            mentioned_idents: Set of identifiers mentioned in the conversation

        Returns:
            Dictionary with batch results and paths to temporary files
        """
        logger.info(f"Processing batch {batch_num}/{total_batches} ({len(batch_files)} files)")

        # Process this batch of files with mentioned identifiers for better ranking
        batch_tags, batch_graph = self.get_ranked_tags(batch_files, mentioned_idents=mentioned_idents)

        # Generate tree representation for this batch
        batch_tree = self.to_tree(batch_tags)

        # Generate filtered tree for this batch
        filtered_batch_tags = self.filter_important_functions(
            batch_tags,
            self.top_percentage,
            self.min_functions,
            self.max_functions
        )
        filtered_batch_tree = self.to_tree(filtered_batch_tags)

        # Save batch results to temporary files
        graph_file = os.path.join(temp_dir, f"batch_{batch_num}_graph.json")
        tags_file = os.path.join(temp_dir, f"batch_{batch_num}_tags.json")

        # For JSON format, convert to JSON objects and save
        if self.output_format == "json":
            tree_file = os.path.join(temp_dir, f"batch_{batch_num}_tree.json")
            filtered_tree_file = os.path.join(temp_dir, f"batch_{batch_num}_filtered_tree.json")

            # Convert tags to JSON
            json_data = self.to_json(batch_tags)
            filtered_json_data = self.to_json(filtered_batch_tags)

            # Save JSON content
            with open(tree_file, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, indent=2, ensure_ascii=False)

            # Save filtered JSON content
            with open(filtered_tree_file, 'w', encoding='utf-8') as f:
                json.dump(filtered_json_data, f, indent=2, ensure_ascii=False)
        else:
            # For text or YAML format, save as text
            tree_file = os.path.join(temp_dir, f"batch_{batch_num}_tree.txt")
            filtered_tree_file = os.path.join(temp_dir, f"batch_{batch_num}_filtered_tree.txt")

            # Save tree content
            with open(tree_file, 'w', encoding='utf-8') as f:
                f.write(batch_tree)

            # Save filtered tree content
            with open(filtered_tree_file, 'w', encoding='utf-8') as f:
                f.write(filtered_batch_tree)

        # Save graph (serialize to JSON)
        graph_data = {
            'nodes': [[n, data] for n, data in batch_graph.nodes(data=True)],
            'edges': [[u, v, data] for u, v, data in batch_graph.edges(data=True)]
        }
        with open(graph_file, 'w', encoding='utf-8') as f:
            json.dump(graph_data, f)

        # Save tags (serialize to JSON)
        # Convert Tag objects to dictionaries
        serializable_tags = []
        for tag in batch_tags:
            if isinstance(tag, tuple):
                # Handle special case for dummy tags
                serializable_tags.append(None)
            else:
                serializable_tags.append({
                    'rel_fname': tag.rel_fname,
                    'fname': tag.fname,
                    'name': tag.name,
                    'kind': tag.kind,
                    'line': tag.line
                })

        with open(tags_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_tags, f)

        # Count tokens
        if self.output_format == "json":
            # For JSON format, count tokens from the JSON data
            json_data = self.to_json(batch_tags)
            filtered_json_data = self.to_json(filtered_batch_tags)
            json_str = json.dumps(json_data, ensure_ascii=False)
            filtered_json_str = json.dumps(filtered_json_data, ensure_ascii=False)
            batch_token_count = self.token_count(json_str)
            filtered_batch_token_count = self.token_count(filtered_json_str)
        else:
            # For text or YAML format, count tokens directly from the content
            batch_token_count = self.token_count(batch_tree)
            filtered_batch_token_count = self.token_count(filtered_batch_tree)

        logger.info(f"Batch {batch_num}/{total_batches} completed: {batch_token_count} tokens, filtered: {filtered_batch_token_count} tokens")

        return {
            'batch_num': batch_num,
            'tree_file': tree_file,
            'filtered_tree_file': filtered_tree_file,
            'graph_file': graph_file,
            'tags_file': tags_file,
            'token_count': batch_token_count,
            'filtered_token_count': filtered_batch_token_count,
            'num_files': len(batch_files)
        }

    def generate_complete_map(self, repo_dir, batch_size=1000, save_interval=True, output_file=None, max_workers=None, mentioned_idents=None):
        """
        Generate a complete map of the repository with parallel batch processing.

        This includes all functions from all files, with their relationships.
        Processes files in parallel batches to reduce processing time and memory usage.
        Uses intelligent context extraction to provide meaningful code snippets.

        Args:
            repo_dir: Repository directory path
            batch_size: Number of files to process in each batch
            save_interval: Whether to save intermediate results after each batch
            output_file: Output file path for incremental saving (required if save_interval=True)
            max_workers: Maximum number of worker processes (default: CPU count)
            mentioned_idents: Set of identifiers mentioned in the conversation (for better ranking)

        Returns:
            Dictionary containing the complete map data
        """
        logger.info(f"Generating complete map for repository: {repo_dir}")

        # Find all source files
        all_files = find_src_files(repo_dir, include_extensions=self.include_extensions, exclude_tests=self.exclude_tests)
        total_files = len(all_files)
        logger.info(f"Found {total_files} source files")

        # Extract identifiers from file paths if none provided
        if mentioned_idents is None:
            mentioned_idents = set()
            # Extract identifiers from file paths to use as initial mentioned identifiers
            for file_path in all_files:
                rel_path = os.path.relpath(file_path, repo_dir)
                path_parts = rel_path.split(os.sep)
                for part in path_parts:
                    # Extract potential identifiers from path components
                    name_parts = re.findall(r'[a-zA-Z][a-zA-Z0-9_]*', part)
                    mentioned_idents.update(name_parts)

            logger.info(f"Extracted {len(mentioned_idents)} potential identifiers from file paths")

        # Create batches of files
        batches = []
        total_batches = math.ceil(total_files / batch_size)
        for batch_num, batch_start in enumerate(range(0, total_files, batch_size)):
            batch_end = min(batch_start + batch_size, total_files)
            batch_files = all_files[batch_start:batch_end]
            batches.append((batch_files, batch_num + 1, total_batches))

        # Check if we need to save incrementally
        if save_interval and not output_file:
            raise ValueError("output_file must be provided when save_interval=True")

        # Create a temporary directory for batch results
        with tempfile.TemporaryDirectory() as temp_dir:
            logger.info(f"Created temporary directory for batch results: {temp_dir}")

            # Process batches in parallel
            if max_workers is None:
                # Use CPU count as default, but limit to a reasonable number
                max_workers = min(os.cpu_count() or 5, 8)

            logger.info(f"Processing {total_batches} batches with {max_workers} parallel workers")

            # Create a partial function with fixed arguments
            process_batch_partial = partial(self.process_batch, temp_dir=temp_dir, mentioned_idents=mentioned_idents)

            # Start timing
            start_time = time.time()

            # Process batches in parallel
            batch_results = []
            with concurrent.futures.ProcessPoolExecutor(max_workers=max_workers) as executor:
                # Submit all batch processing tasks
                future_to_batch = {
                    executor.submit(process_batch_partial, batch_files, batch_num, total_batches): batch_num
                    for batch_files, batch_num, total_batches in batches
                }

                # Collect results as they complete
                for future in concurrent.futures.as_completed(future_to_batch):
                    batch_num = future_to_batch[future]
                    try:
                        result = future.result()
                        batch_results.append(result)
                        logger.info(f"Batch {batch_num}/{total_batches} processing completed")

                        # Save intermediate results if requested
                        if save_interval and output_file:
                            # Sort completed batches by batch number
                            sorted_results = sorted(batch_results, key=lambda x: x['batch_num'])

                            # Combine content from completed batches
                            if self.output_format == "json":
                                # For JSON format, we need to merge the JSON objects
                                combined_json = {}
                                combined_filtered_json = {}

                                for result in sorted_results:
                                    with open(result['tree_file'], 'r', encoding='utf-8') as f:
                                        batch_json = json.load(f)
                                        # Merge the batch JSON into the combined JSON
                                        combined_json.update(batch_json)

                                    with open(result['filtered_tree_file'], 'r', encoding='utf-8') as f:
                                        batch_filtered_json = json.load(f)
                                        # Merge the batch filtered JSON into the combined filtered JSON
                                        combined_filtered_json.update(batch_filtered_json)

                                # Save current progress to temporary files
                                base, ext = os.path.splitext(output_file)
                                temp_output = f"{base}_temp{ext}"
                                filtered_temp_output = f"{base}_filtered_temp{ext}"

                                with open(temp_output, 'w', encoding='utf-8') as f:
                                    json.dump(combined_json, f, indent=2, ensure_ascii=False)

                                with open(filtered_temp_output, 'w', encoding='utf-8') as f:
                                    json.dump(combined_filtered_json, f, indent=2, ensure_ascii=False)
                            else:
                                # For text or YAML format, we can concatenate
                                combined_tree = ""
                                combined_filtered_tree = ""
                                for result in sorted_results:
                                    with open(result['tree_file'], 'r', encoding='utf-8') as f:
                                        combined_tree += f.read()
                                    with open(result['filtered_tree_file'], 'r', encoding='utf-8') as f:
                                        combined_filtered_tree += f.read()

                                # Save current progress to temporary files
                                base, ext = os.path.splitext(output_file)
                                temp_output = f"{base}_temp{ext}"
                                filtered_temp_output = f"{base}_filtered_temp{ext}"

                                with open(temp_output, 'w', encoding='utf-8') as f:
                                    f.write(combined_tree)

                                with open(filtered_temp_output, 'w', encoding='utf-8') as f:
                                    f.write(combined_filtered_tree)

                            completed_files = sum(result['num_files'] for result in sorted_results)
                            logger.info(f"Saved intermediate results after {len(sorted_results)}/{total_batches} batches ({completed_files}/{total_files} files processed)")

                    except Exception as e:
                        logger.error(f"Error processing batch {batch_num}: {e}")
                        import traceback
                        logger.error(traceback.format_exc())

            # Sort all batch results by batch number
            batch_results.sort(key=lambda x: x['batch_num'])

            # Combine all results
            # Initialize variables for all formats
            tree_content = ""
            filtered_tree_content = ""
            tree_json = {}
            filtered_tree_json = {}

            all_tags = []
            filtered_tags = []
            G = nx.MultiDiGraph()
            total_token_count = 0
            total_filtered_token_count = 0

            for result in batch_results:
                if self.output_format == "json":
                    # Combine JSON content
                    with open(result['tree_file'], 'r', encoding='utf-8') as f:
                        batch_json = json.load(f)
                        # Merge the batch JSON into the combined JSON
                        tree_json.update(batch_json)

                    # Combine filtered JSON content
                    with open(result['filtered_tree_file'], 'r', encoding='utf-8') as f:
                        batch_filtered_json = json.load(f)
                        # Merge the batch filtered JSON into the combined filtered JSON
                        filtered_tree_json.update(batch_filtered_json)
                else:
                    # Combine tree content for text or YAML format
                    with open(result['tree_file'], 'r', encoding='utf-8') as f:
                        tree_content += f.read()

                    # Combine filtered tree content for text or YAML format
                    with open(result['filtered_tree_file'], 'r', encoding='utf-8') as f:
                        filtered_tree_content += f.read()

                # Load and combine graph data
                with open(result['graph_file'], 'r', encoding='utf-8') as f:
                    graph_data = json.load(f)
                    # Add nodes and edges to the combined graph
                    for node, data in graph_data['nodes']:
                        G.add_node(node, **data)
                    for u, v, data in graph_data['edges']:
                        G.add_edge(u, v, **data)

                # Load and combine tags
                with open(result['tags_file'], 'r', encoding='utf-8') as f:
                    tags_data = json.load(f)
                    # Convert dictionaries back to Tag objects
                    for tag_dict in tags_data:
                        if tag_dict is not None:
                            tag = Tag(
                                rel_fname=tag_dict['rel_fname'],
                                fname=tag_dict['fname'],
                                name=tag_dict['name'],
                                kind=tag_dict['kind'],
                                line=tag_dict['line']
                            )
                            all_tags.append(tag)

                            # Also add to filtered tags if this is a filtered tag
                            # We'll filter these later
                            filtered_tags.append(tag)

                # Add token counts
                total_token_count += result['token_count']
                total_filtered_token_count += result['filtered_token_count']

            # Calculate end time
            end_time = time.time()
            logger.info(f"Parallel processing completed in {end_time - start_time:.2f} seconds")

            # Verify token counts
            if self.output_format == "json":
                # For JSON format, we need to calculate token counts from the JSON objects
                if tree_json:
                    # Convert to string to count tokens
                    json_str = json.dumps(tree_json, ensure_ascii=False)
                    filtered_json_str = json.dumps(filtered_tree_json, ensure_ascii=False)
                    actual_token_count = self.token_count(json_str)
                    actual_filtered_token_count = self.token_count(filtered_json_str)
                else:
                    # Use the sum of batch counts
                    actual_token_count = total_token_count
                    actual_filtered_token_count = total_filtered_token_count
            else:
                # For text or YAML format, count tokens directly from the content
                actual_token_count = self.token_count(tree_content)
                actual_filtered_token_count = self.token_count(filtered_tree_content)

            # Update token counts if there's a mismatch
            if actual_token_count != total_token_count:
                logger.warning(f"Token count mismatch: sum of batch counts ({total_token_count}) != actual count ({actual_token_count})")
                total_token_count = actual_token_count

            if actual_filtered_token_count != total_filtered_token_count:
                logger.warning(f"Filtered token count mismatch: sum of batch counts ({total_filtered_token_count}) != actual count ({actual_filtered_token_count})")
                total_filtered_token_count = actual_filtered_token_count

            # Ensure we don't have zero token counts to avoid division by zero
            if total_token_count == 0:
                total_token_count = 1
            if total_filtered_token_count == 0:
                total_filtered_token_count = 1

            # We already filtered in process_batch, so we don't need to filter again
            # This ensures consistent filtering throughout the process
            logger.info("Using pre-filtered tags from batch processing")

            logger.info(f"Generated complete map with {total_token_count} tokens")
            logger.info(f"Generated filtered map with {total_filtered_token_count} tokens (kept {total_filtered_token_count / total_token_count:.1%} of original)")

            # If using JSON or YAML format, generate the content directly
            if self.output_format in ["json", "yaml"]:
                if self.output_format == "json":
                    if tree_json:
                        # We already have merged JSON objects from batch processing
                        tree_content = json.dumps(tree_json, indent=2, ensure_ascii=False)
                        filtered_tree_content = json.dumps(filtered_tree_json, indent=2, ensure_ascii=False)
                    else:
                        # Create clean JSON content without any formatting characters
                        json_data = self.to_json(all_tags)
                        filtered_json_data = self.to_json(filtered_tags)
                        tree_content = json.dumps(json_data, indent=2, ensure_ascii=False)
                        filtered_tree_content = json.dumps(filtered_json_data, indent=2, ensure_ascii=False)
                else:  # YAML
                    # Create clean YAML content without any formatting characters
                    json_data = self.to_json(all_tags)
                    filtered_json_data = self.to_json(filtered_tags)
                    tree_content = yaml.dump(json_data, default_flow_style=False)
                    filtered_tree_content = yaml.dump(filtered_json_data, default_flow_style=False)

            return {
                "tree": tree_content,
                "filtered_tree": filtered_tree_content,
                "graph": G,
                "tags": all_tags,
                "filtered_tags": filtered_tags,
                "token_count": total_token_count,
                "filtered_token_count": total_filtered_token_count
            }

    def save_map(self, map_data, output_file, final_save=True):
        """
        Save the complete map to a file.

        Args:
            map_data: Map data to save
            output_file: Output file path
            final_save: Whether this is the final save (True) or an intermediate save (False)

        Returns:
            Output file path
        """
        # Determine the output format based on file extension or class setting
        output_format = self.output_format

        # If output format is not explicitly set, try to infer from file extension
        if output_format == "text":
            _, ext = os.path.splitext(output_file)
            if ext.lower() == '.json':
                output_format = "json"
            elif ext.lower() in ('.yaml', '.yml'):
                output_format = "yaml"

        # Check if we have temporary files with content
        base, ext = os.path.splitext(output_file)
        temp_output = f"{base}_temp{ext}"

        # If we're doing a final save and temp files exist, use them
        if final_save and os.path.exists(temp_output) and os.path.getsize(temp_output) > 100:
            logger.info(f"Using temporary file {temp_output} for final save")
            # Copy the temp file to the final output
            with open(temp_output, 'r', encoding='utf-8') as src:
                with open(output_file, 'w', encoding='utf-8') as dst:
                    dst.write(src.read())
        else:
            # Save complete map based on format
            if output_format == "json":
                # Always convert tags to JSON to ensure valid output
                json_data = self.to_json(map_data["tags"])
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(json_data, f, indent=2)
            elif output_format == "yaml":
                # Always convert tags to YAML to ensure valid output
                yaml_data = self.to_json(map_data["tags"])
                with open(output_file, 'w', encoding='utf-8') as f:
                    yaml.dump(yaml_data, f, default_flow_style=False)
            else:
                # Default text format
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(map_data["tree"])

        if final_save:
            logger.info(f"Saved complete map to {output_file} in {output_format} format")
        else:
            logger.info(f"Saved intermediate complete map to {output_file} in {output_format} format")

        # Save filtered map if available
        if "filtered_tree" in map_data:
            # Generate filtered output filename
            base, ext = os.path.splitext(output_file)
            filtered_output_file = f"{base}_filtered{ext}"
            filtered_temp_output = f"{base}_filtered_temp{ext}"

            # If we're doing a final save and temp files exist, use them
            if final_save and os.path.exists(filtered_temp_output) and os.path.getsize(filtered_temp_output) > 100:
                logger.info(f"Using temporary file {filtered_temp_output} for final filtered save")
                # Copy the temp file to the final output
                with open(filtered_temp_output, 'r', encoding='utf-8') as src:
                    with open(filtered_output_file, 'w', encoding='utf-8') as dst:
                        dst.write(src.read())
            else:
                # Save filtered map based on format
                if output_format == "json":
                    # Always convert filtered tags to JSON to ensure valid output
                    json_data = self.to_json(map_data.get("filtered_tags", []))
                    with open(filtered_output_file, 'w', encoding='utf-8') as f:
                        json.dump(json_data, f, indent=2)
                elif output_format == "yaml":
                    # Always convert filtered tags to YAML to ensure valid output
                    yaml_data = self.to_json(map_data.get("filtered_tags", []))
                    with open(filtered_output_file, 'w', encoding='utf-8') as f:
                        yaml.dump(yaml_data, f, default_flow_style=False)
                else:
                    # Default text format
                    with open(filtered_output_file, 'w', encoding='utf-8') as f:
                        f.write(map_data["filtered_tree"])

            if final_save:
                logger.info(f"Saved filtered map to {filtered_output_file} in {output_format} format")
            else:
                logger.info(f"Saved intermediate filtered map to {filtered_output_file} in {output_format} format")

        return output_file

def main():
    """Main function."""
    import argparse

    parser = argparse.ArgumentParser(description="Generate a complete repository map")
    parser.add_argument("--repo-dir", "-r", help="Path to the repository directory (default: current directory)", default=os.getcwd())
    parser.add_argument("--output", "-o", help="Output file path", default="complete_repomap.txt")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose output")
    parser.add_argument("--extensions", "-e", nargs="+", help="File extensions to include (e.g., .py .rb .js)")
    parser.add_argument("--batch-size", "-b", type=int, default=1000,
                        help="Number of files to process in each batch (default: 1000)")
    parser.add_argument("--no-incremental-save", action="store_true",
                        help="Disable incremental saving after each batch")
    parser.add_argument("--max-workers", "-w", type=int, default=None,
                        help="Maximum number of worker processes (default: CPU count, max 8)")
    parser.add_argument("--exclude-tests", "-x", default=True, action="store_true",
                        help="Exclude test files from the map")
    parser.add_argument("--output-format", "-f", default="json", choices=["text", "json", "yaml"],
                        help="Output format (default: json)")
    parser.add_argument("--domain-name", "-d", default="default",
                        help="Domain name for JSON/YAML output (default: 'default')")

    # Basic filtering parameters
    filtering_group = parser.add_argument_group('Basic Filtering Parameters')
    filtering_group.add_argument("--top-percentage", "-p", type=float, default=0.2,
                        help="Percentage of top functions to keep per file (default: 0.2)")
    filtering_group.add_argument("--min-functions", "-m", type=int, default=1,
                        help="Minimum number of functions to keep per file (default: 3)")
    filtering_group.add_argument("--max-functions", "-M", type=int, default=5,
                        help="Maximum number of functions to keep per file (default: 10)")
    filtering_group.add_argument("--context-lines", "-c", type=int, default=0,
                        help="Number of context lines to include around important lines (default: 2)")

    # Content sampling parameters
    sampling_group = parser.add_argument_group('Content Sampling Parameters')
    sampling_group.add_argument("--min-sample-lines", type=int, default=5,
                        help="Minimum number of lines to extract from files without functions (default: 5)")
    sampling_group.add_argument("--max-sample-lines", type=int, default=10,
                        help="Maximum number of lines to extract from files without functions (default: 10)")
    sampling_group.add_argument("--max-file-lines", type=int, default=20,
                        help="Maximum total lines to include from any file (default: 30)")

    # Symbol importance weights
    importance_group = parser.add_argument_group('Symbol Importance Weights')
    importance_group.add_argument("--generic-name-penalty", type=float, default=5,
                        help="Score penalty for generic function names (default: 5)")
    importance_group.add_argument("--complexity-bonus", type=float, default=10,
                        help="Score bonus for functions with complexity indicators in name (default: 10)")
    importance_group.add_argument("--domain-bonus", type=float, default=15,
                        help="Score bonus for functions with domain-specific terms in name (default: 15)")
    importance_group.add_argument("--name-length-bonus-factor", type=float, default=1.25,
                        help="Factor for scoring function name length (default: 1.25)")
    importance_group.add_argument("--loc-score-factor", type=float, default=3,
                        help="Factor for scoring function length (default: 3)")
    importance_group.add_argument("--param-count-factor", type=float, default=1,
                        help="Factor for scoring parameter count (default: 1)")
    importance_group.add_argument("--method-bonus", type=float, default=3,
                        help="Score bonus for class methods vs standalone functions (default: 3)")

    # Symbol reference importance factors
    reference_group = parser.add_argument_group('Symbol Reference Importance Factors')
    reference_group.add_argument("--high-ref-bonus", type=float, default=10,
                        help="Additional bonus for highly referenced symbols (default: 10)")
    reference_group.add_argument("--medium-ref-bonus", type=float, default=5,
                        help="Additional bonus for moderately referenced symbols (default: 5)")
    reference_group.add_argument("--pagerank-scale-factor", type=float, default=30,
                        help="Factor to scale PageRank scores (default: 30)")

    # Symbol graph ranking weights
    graph_group = parser.add_argument_group('Symbol Graph Ranking Weights')
    graph_group.add_argument("--in-degree-weight", type=float, default=2,
                        help="Weight for in-degree in symbol graph ranking (default: 2)")
    graph_group.add_argument("--out-degree-weight", type=float, default=1,
                        help="Weight for out-degree in symbol graph ranking (default: 1)")

    # Adaptive thresholding factors
    threshold_group = parser.add_argument_group('Adaptive Thresholding Factors')
    threshold_group.add_argument("--large-file-threshold-factor", type=float, default=0.5,
                        help="Threshold factor for files with many functions (default: 0.5)")
    threshold_group.add_argument("--medium-file-threshold-factor", type=float, default=0.25,
                        help="Threshold factor for files with moderate functions (default: 0.25)")
    threshold_group.add_argument("--small-file-threshold-factor", type=float, default=-0.5,
                        help="Threshold factor for files with few functions (default: -0.5)")
    threshold_group.add_argument("--large-file-function-count", type=int, default=20,
                        help="Number of functions to consider a file 'large' (default: 20)")
    threshold_group.add_argument("--medium-file-function-count", type=int, default=10,
                        help="Number of functions to consider a file 'medium' (default: 10)")

    # Other parameters
    other_group = parser.add_argument_group('Other Parameters')
    other_group.add_argument("--non-function-tag-percentage", type=float, default=0.35,
                        help="Percentage of non-function tags to keep (default: 0.5)")
    other_group.add_argument("--mentioned-idents", "-i", nargs="+",
                        help="Identifiers to prioritize in the map (e.g., class names, function names)")

    args = parser.parse_args()

    # Remove test hardcoded paths - uncomment for testing specific repositories
    # args.repo_dir = "/path/to/repository"
    # args.output = "output_repomap.txt"
    # args.extensions = ['.py', '.js']
    # args.exclude_tests = True
    # args.mentioned_idents = ["important", "class", "names"]

    # args.repo_dir = "/Users/<USER>/work/startup/godzilla/test/pytorch"
    # args.output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/exp_results/repomap/pytorch/pytorch_repomap.json"
    # args.exclude_tests = True  # Add flag to exclude test files
    # args.extensions = ['.rb', '.py', '.c', '.cpp']

    # args.repo_dir = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core"
    # args.output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/exp_results/repomap/bracket/bracket_repomap.json"
    # args.exclude_tests = True  # Add flag to exclude test files
    # args.extensions = ['.rb', '.py', '.c', '.cpp']

    args.repo_dir = "/Users/<USER>/work/startup/godzilla/test/gitlab-1"
    args.output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/exp_results/repomap/gitlab/gitlab.json"
    args.extensions = ['.rb', '.py']
    args.exclude_tests = True  # Add flag to exclude test files


    # args.repo_dir = "/Users/<USER>/work/startup/godzilla/test/django-1"
    # args.output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/exp_results/repomap/django/django_repomap.json"
    # args.exclude_tests = True  # Add flag to exclude test files
    # args.extensions = ['.py', '.c', '.cpp']


    # Process extensions if provided
    include_extensions = args.extensions if args.extensions else None
    exclude_tests = args.exclude_tests

    # Process mentioned identifiers if provided
    mentioned_idents = set(args.mentioned_idents) if args.mentioned_idents else None

    # Create the repo map generator with all configurable parameters
    repo_map = CompleteRepoMap(
        root=args.repo_dir,
        verbose=args.verbose,
        include_extensions=include_extensions,
        top_percentage=args.top_percentage,
        min_functions=args.min_functions,
        max_functions=args.max_functions,
        exclude_tests=exclude_tests,
        context_lines=args.context_lines,
        output_format=args.output_format,
        domain_name=args.domain_name,
        # Content sampling parameters
        min_sample_lines=args.min_sample_lines,
        max_sample_lines=args.max_sample_lines,
        max_file_lines=args.max_file_lines,
        # Symbol importance weights
        generic_name_penalty=args.generic_name_penalty,
        complexity_bonus=args.complexity_bonus,
        domain_bonus=args.domain_bonus,
        name_length_bonus_factor=args.name_length_bonus_factor,
        loc_score_factor=args.loc_score_factor,
        param_count_factor=args.param_count_factor,
        method_bonus=args.method_bonus,
        # Symbol reference importance factors
        high_ref_bonus=args.high_ref_bonus,
        medium_ref_bonus=args.medium_ref_bonus,
        pagerank_scale_factor=args.pagerank_scale_factor,
        # Symbol graph ranking weights
        in_degree_weight=args.in_degree_weight,
        out_degree_weight=args.out_degree_weight,
        # Adaptive thresholding factors
        large_file_threshold_factor=args.large_file_threshold_factor,
        medium_file_threshold_factor=args.medium_file_threshold_factor,
        small_file_threshold_factor=args.small_file_threshold_factor,
        # File size thresholds
        large_file_function_count=args.large_file_function_count,
        medium_file_function_count=args.medium_file_function_count,
        # Non-function tag control
        non_function_tag_percentage=args.non_function_tag_percentage,
    )

    # Generate the map with parallel batch processing
    start_time = time.time()
    map_data = repo_map.generate_complete_map(
        args.repo_dir,
        batch_size=args.batch_size,
        save_interval=not args.no_incremental_save,
        output_file=args.output,
        max_workers=args.max_workers,
        mentioned_idents=mentioned_idents
    )
    end_time = time.time()

    # Save the final map
    output_file = repo_map.save_map(map_data, args.output, final_save=True)

    print(f"Complete repository map generated and saved to {output_file}")
    print(f"Map contains {map_data['token_count']} tokens")

    # Print filtered map info if available
    if "filtered_token_count" in map_data:
        base, ext = os.path.splitext(output_file)
        filtered_output_file = f"{base}_filtered{ext}"
        print(f"Filtered repository map saved to {filtered_output_file}")
        print(f"Filtered map contains {map_data['filtered_token_count']} tokens")
        print(f"Filtering kept approximately {map_data['filtered_token_count'] / map_data['token_count']:.1%} of the original content")

    logger.info(f"Map generation took {end_time - start_time:.2f} seconds")
    print(f"Map generation took {end_time - start_time:.2f} seconds")


if __name__ == "__main__":
    main()
