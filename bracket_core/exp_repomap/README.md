# Repository Map Experiment

This experiment aims to implement Steps 1-5 from the Bracket Core IRL bottleneck analysis by creating a comprehensive repository map and using it for LLM-based domain discovery.

## Goals

1. **Repomap Generation**: Create a concise structural representation of the codebase showing:
   - Classes and functions with their signatures
   - Call relationships between functions
   - Import dependencies

2. **LLM-Based Domain Discovery**: Feed this repomap to a large context window LLM to:
   - Generate hierarchical domain structure (5 levels deep for 10M LOC)
   - Identify logical groupings based on structure and signatures
   - Define leaf-level domains

## Implementation

The implementation consists of two main components:

1. `complete_repomap.py`: Generates a comprehensive repository map that includes all functions from all files in a codebase.
2. (Coming soon) LLM integration for domain discovery.

## Usage

To generate a complete repository map:

```bash
python -m bracket_core.exp_repomap.complete_repomap /path/to/repository --output repomap.txt
```

## How It Works

The repository map generator:

1. Scans all source files in the repository
2. Uses tree-sitter to parse each file and extract function/class definitions and references
3. Builds a graph of references between symbols
4. Generates a tree representation of the codebase structure
5. Outputs a comprehensive map that can be fed to an LLM for domain discovery

Unlike the original aider implementation, this version includes all functions from all files, not just the most important ones, to provide a complete view of the codebase structure.
