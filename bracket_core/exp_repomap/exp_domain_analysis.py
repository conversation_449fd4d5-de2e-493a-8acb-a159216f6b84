"""
Domain Analysis for Codebase

This module provides functionality to analyze a codebase's structure
and classify it into domains and subdomains using LLMs.

It can be used as:
1. A standalone script to process a complete_repomap_bracket.txt file
2. A module that can be integrated into the repository analysis flow

The domain classification provides a hierarchical view of the codebase's
functional organization, making it easier to understand the overall architecture.

For large codebases (>1M LOC), the module supports parallel processing by:
1. Splitting the function data into manageable chunks
2. Processing each chunk in parallel with separate LLM calls
3. Combining the results into a unified domain structure
"""

import os
import yaml
import json
import logging
import argparse
import asyncio
import aiohttp
import time
import math
import tiktoken
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, field

# Import LLM clients and API key management
from bracket_core.llm.get_client import get_openrouter_client, get_claude_client
from bracket_core.llm.api_keys import get_openai_api_key, get_openrouter_api_key, get_anthropic_api_key

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    from dotenv import load_dotenv
    load_dotenv()  # Load .env file if it exists
except ImportError:
    pass  # dotenv not installed, will fall back to regular env vars

# Initialize tokenizer for token counting
try:
    # Use cl100k_base tokenizer (used by GPT-4 and GPT-3.5-turbo)
    TOKENIZER = tiktoken.get_encoding("cl100k_base")
except Exception as e:
    logger.warning(f"Failed to initialize tokenizer: {e}. Token counting will be approximate.")
    TOKENIZER = None


# Fast token counting using word-based heuristic
# def count_tokens(text: str) -> int:
#     """
#     Count the approximate number of tokens in a text string using a fast heuristic.

#     This function uses a more conservative word-based heuristic (words * 1.8) which is much faster
#     than using the tiktoken library for large texts. The higher multiplier provides a safety margin
#     to avoid underestimating token counts, especially for code which tends to have more tokens per word.

#     Args:
#         text: The text to count tokens for

#     Returns:
#         Approximate number of tokens
#     """
#     # Count words by splitting on whitespace
#     word_count = len(text.split())

#     # Count lines as well (important for code with many short lines)
#     line_count = text.count('\n') + 1

#     # Apply a more conservative heuristic: each word is approximately 1.8 tokens
#     # and add a small factor for line breaks which can add tokens in code
#     return int(word_count * 1.8 + line_count * 0.2)

def count_tokens(text: str) -> int:
    """Count the number of tokens in a text string."""
    if TOKENIZER:
        return len(TOKENIZER.encode(text))
    else:
        # Fallback approximation: ~4 characters per token
        return len(text) // 4

@dataclass
class StatusTracker:
    """Tracks the status of API requests."""

    time_of_last_rate_limit_error: float = 0
    num_api_errors: int = 0
    num_rate_limit_errors: int = 0
    num_other_errors: int = 0

@dataclass
class DomainAnalysisResult:
    """Stores the results of domain analysis."""

    domains: Dict[str, Any] = field(default_factory=dict)
    raw_response: str = ""
    success: bool = False
    error_message: str = ""

@dataclass
class ChunkProcessingResult:
    """Stores the results of processing a single chunk of functions."""

    chunk_id: int
    domains: Dict[str, Any] = field(default_factory=dict)
    raw_response: str = ""
    success: bool = False
    error_message: str = ""

class DomainAnalyzer:
    """
    Analyzes codebase functions and classifies them into domains and subdomains.

    This class uses GPT-4o-mini to analyze a YAML file containing significant functions
    from a codebase and organize them into a hierarchical structure of domains and subdomains.
    """

    def __init__(
        self,
        api_key: Optional[str] = None,
        model: str = "gpt-4o",
        max_requests_per_minute: float = 50,
        max_tokens_per_minute: float = 100000,
        temperature: float = 0.1,
        use_openrouter: bool = False,
        use_claude: bool = False,
        openrouter_base_url: str = "https://openrouter.ai/api/v1",
        claude_model: str = "claude-3-7-sonnet-20250219",
        generate_explanations: bool = True,
    ):
        """
        Initialize the domain analyzer.

        Args:
            api_key: API key (OpenAI, Claude, or OpenRouter depending on which service is used)
            model: Model to use (OpenAI model ID if using OpenAI)
            max_requests_per_minute: Rate limit for API requests
            max_tokens_per_minute: Token rate limit for API
            temperature: Temperature setting for the model (lower = more deterministic)
            use_openrouter: Whether to use OpenRouter instead of OpenAI
            use_claude: Whether to use Claude instead of OpenAI
            openrouter_base_url: Base URL for OpenRouter API
            claude_model: Claude model to use if use_claude is True
            generate_explanations: Whether to generate explanations for leaf domains
        """
        # Initialize parameters
        self.model = model
        self.max_requests_per_minute = max_requests_per_minute
        self.max_tokens_per_minute = max_tokens_per_minute
        self.temperature = temperature
        self.use_openrouter = use_openrouter
        self.use_claude = use_claude
        self.openrouter_base_url = openrouter_base_url
        self.claude_model = claude_model
        self.generate_explanations = generate_explanations
        self.status_tracker = StatusTracker()

        # Get the appropriate API key based on the service being used
        if self.use_openrouter:
            self.api_key = get_openrouter_api_key(provided_key=api_key)
        elif self.use_claude:
            self.api_key = get_anthropic_api_key(provided_key=api_key)
        else:
            self.api_key = get_openai_api_key(provided_key=api_key)

        # Initialize clients based on selected service
        self.openrouter_client = None
        self.claude_client = None

        if self.use_openrouter:
            # For domain analysis, use Gemini model with large context window
            openrouter_model = "google/gemini-2.5-pro-preview"
            self.openrouter_client = get_openrouter_client(
                # API key will be handled by the client
                model=openrouter_model,  # Override with Gemini model
                max_tokens=60096,  # Use larger context window
                temperature=self.temperature,
                base_url=self.openrouter_base_url
            )
            print(f"Domain Analyzer using OpenRouter with model: {openrouter_model}")

        elif self.use_claude:
            # Initialize Claude client
            self.claude_client = get_claude_client(
                api_key=self.api_key,
                model=self.claude_model,
                max_tokens=8000,  # This is the max tokens for the response, not the prompt
                temperature=self.temperature
            )
            print(f"Domain Analyzer using Claude with model: {self.claude_model}")

    def _split_functions_into_chunks(self, functions_data: Union[Dict[str, Any], str], max_tokens_per_chunk: int = 500000) -> List[Union[Dict[str, Any], str]]:
        """
        Split the functions data into chunks that fit within the token limit.

        Args:
            functions_data: Dictionary containing function data or string containing repomap text
            max_tokens_per_chunk: Maximum number of tokens per chunk

        Returns:
            List of function data chunks (either dictionaries or strings)
        """
        # Estimate prompt overhead (system prompt + user prompt template + JSON example)
        # This is a conservative estimate to account for the tokens added around the actual data
        PROMPT_OVERHEAD_TOKENS = 5000

        # Adjust max tokens per chunk to account for prompt overhead
        adjusted_max_tokens = max(10000, max_tokens_per_chunk - PROMPT_OVERHEAD_TOKENS)
        logger.info(f"Adjusted max tokens per chunk: {adjusted_max_tokens} (original: {max_tokens_per_chunk}, overhead: {PROMPT_OVERHEAD_TOKENS})")

        # Convert to string to estimate token count
        if isinstance(functions_data, str):
            # If it's already a string, use it directly
            data_str = functions_data
        else:
            # Otherwise convert to YAML string
            data_str = yaml.dump(functions_data, default_flow_style=False)

        total_tokens = count_tokens(data_str)

        logger.info(f"Total tokens in functions data: {total_tokens}")

        # If the total tokens are less than the adjusted max, return the whole data as a single chunk
        if total_tokens <= adjusted_max_tokens:
            logger.info("Functions data fits in a single chunk")
            return [functions_data]

        # Calculate number of chunks needed with a safety margin
        # Add 20% more chunks than strictly necessary to ensure we stay under limits
        raw_num_chunks = math.ceil(total_tokens / adjusted_max_tokens)
        num_chunks = math.ceil(raw_num_chunks * 1.2)
        logger.info(f"Splitting functions data into {num_chunks} chunks (raw calculation: {raw_num_chunks})")

        # If it's a string (repomap text), split by files
        if isinstance(functions_data, str):
            # Split the text by file sections
            file_sections = []
            current_section = []

            for line in functions_data.splitlines():
                # If this is a new file header, start a new section
                if line.strip().startswith('│') and line.strip().endswith(':'):
                    if current_section:
                        file_sections.append('\n'.join(current_section))
                        current_section = []

                current_section.append(line)

            # Add the last section
            if current_section:
                file_sections.append('\n'.join(current_section))

            # Calculate token size for each file section
            section_tokens = [(i, count_tokens(section)) for i, section in enumerate(file_sections)]

            # Create balanced chunks based on token counts
            chunks = []

            # Sort sections by token size (largest first) for better packing
            section_tokens.sort(key=lambda x: x[1], reverse=True)

            # First pass: handle sections that exceed the token limit individually
            oversized_sections = []
            normal_sections = []

            for idx, tokens in section_tokens:
                if tokens > adjusted_max_tokens:
                    oversized_sections.append((idx, tokens))
                else:
                    normal_sections.append((idx, tokens))

            # Handle oversized sections by truncating
            for idx, tokens in oversized_sections:
                section = file_sections[idx]
                logger.warning(f"File section exceeds token limit ({tokens} > {adjusted_max_tokens}), truncating")
                # Truncate the section
                truncation_ratio = adjusted_max_tokens / tokens
                lines = section.splitlines()
                truncated_lines = lines[:int(len(lines) * truncation_ratio)]
                chunks.append('\n'.join(truncated_lines))

            # Second pass: pack remaining sections into balanced chunks
            # Sort by token count for better bin packing
            normal_sections.sort(key=lambda x: x[1], reverse=True)

            # Simple greedy bin packing algorithm
            bins = [[] for _ in range(num_chunks)]
            bin_sizes = [0] * num_chunks

            for idx, tokens in normal_sections:
                # Find the bin with the most remaining space that can fit this section
                best_bin = -1
                best_remaining = -1

                for i in range(len(bins)):
                    remaining = adjusted_max_tokens - bin_sizes[i]
                    if tokens <= remaining and remaining > best_remaining:
                        best_bin = i
                        best_remaining = remaining

                # If no bin can fit this section, create a new one
                if best_bin == -1:
                    bins.append([idx])
                    bin_sizes.append(tokens)
                else:
                    bins[best_bin].append(idx)
                    bin_sizes[best_bin] += tokens

            # Create chunks from bins
            for bin_indices in bins:
                if not bin_indices:
                    continue

                chunk_sections = [file_sections[idx] for idx in bin_indices]
                chunk_text = '\n'.join(chunk_sections)
                chunks.append(chunk_text)

            logger.info(f"Created {len(chunks)} balanced chunks")
            return chunks
        else:
            # For dictionary data, split by keys
            function_keys = list(functions_data.keys())

            # Check if this is a JSON repomap (where values are lists of strings)
            # or a regular dictionary (where values could be any type)
            is_json_repomap = False
            if len(function_keys) > 0:
                sample_key = function_keys[0]
                is_json_repomap = isinstance(functions_data.get(sample_key), list) and all(
                    isinstance(item, str) for item in functions_data.get(sample_key, [])
                )

            if is_json_repomap:
                # For JSON repomap, we need a more sophisticated approach
                # Calculate token size for each file
                file_token_sizes = {}
                for file_path in function_keys:
                    code_lines = functions_data[file_path]
                    # Combine code lines into a single string as requested
                    combined_code = "\n".join(code_lines)
                    # Calculate the token size for this file's representation
                    file_text = f"│ {file_path}:\n│   {combined_code}\n│"
                    file_token_sizes[file_path] = count_tokens(file_text)

                # Sort files by token size (largest first) for better bin packing
                sorted_files = sorted(file_token_sizes.keys(), key=lambda k: file_token_sizes[k], reverse=True)

                # First pass: handle files that exceed the token limit individually
                oversized_files = []
                normal_files = []

                for file_path in sorted_files:
                    tokens = file_token_sizes[file_path]
                    if tokens > adjusted_max_tokens:
                        oversized_files.append((file_path, tokens))
                    else:
                        normal_files.append((file_path, tokens))

                # Create chunks
                chunks = []

                # Handle oversized files by truncating
                for file_path, tokens in oversized_files:
                    logger.warning(f"File {file_path} exceeds token limit ({tokens} > {adjusted_max_tokens}), truncating")
                    # Process and truncate this file individually
                    combined_code = "\n".join(functions_data[file_path])
                    # Estimate how much to truncate
                    truncation_ratio = adjusted_max_tokens / tokens
                    truncated_code = combined_code[:int(len(combined_code) * truncation_ratio)]
                    file_text = f"│ {file_path}:\n│   {truncated_code}\n│"
                    chunks.append(file_text)

                # Second pass: pack remaining files into balanced chunks
                # Simple greedy bin packing algorithm
                bins = [[] for _ in range(num_chunks)]
                bin_sizes = [0] * num_chunks

                for file_path, tokens in normal_files:
                    # Find the bin with the most remaining space that can fit this file
                    best_bin = -1
                    best_remaining = -1

                    for i in range(len(bins)):
                        remaining = adjusted_max_tokens - bin_sizes[i]
                        if tokens <= remaining and remaining > best_remaining:
                            best_bin = i
                            best_remaining = remaining

                    # If no bin can fit this file, create a new one
                    if best_bin == -1:
                        bins.append([file_path])
                        bin_sizes.append(tokens)
                    else:
                        bins[best_bin].append(file_path)
                        bin_sizes[best_bin] += tokens

                # Create chunks from bins
                for bin_files in bins:
                    if not bin_files:
                        continue

                    chunk_data = {k: functions_data[k] for k in bin_files}
                    chunk_text = self._process_json_files_to_text(chunk_data)
                    chunks.append(chunk_text)

                logger.info(f"Created {len(chunks)} balanced chunks")
                return chunks
            else:
                # For regular dictionary data, use a similar bin packing approach
                # Calculate token size for each key
                key_token_sizes = {}
                for key in function_keys:
                    item = {key: functions_data[key]}
                    item_str = yaml.dump(item, default_flow_style=False)
                    key_token_sizes[key] = count_tokens(item_str)

                # Sort keys by token size (largest first) for better bin packing
                sorted_keys = sorted(key_token_sizes.keys(), key=lambda k: key_token_sizes[k], reverse=True)

                # Simple greedy bin packing algorithm
                bins = [[] for _ in range(num_chunks)]
                bin_sizes = [0] * num_chunks

                for key in sorted_keys:
                    tokens = key_token_sizes[key]

                    # If a single key exceeds the limit, it needs special handling
                    if tokens > adjusted_max_tokens:
                        logger.warning(f"Key {key} exceeds token limit ({tokens} > {adjusted_max_tokens}), adding as individual chunk")
                        bins.append([key])
                        continue

                    # Find the bin with the most remaining space that can fit this key
                    best_bin = -1
                    best_remaining = -1

                    for i in range(len(bins)):
                        remaining = adjusted_max_tokens - bin_sizes[i]
                        if tokens <= remaining and remaining > best_remaining:
                            best_bin = i
                            best_remaining = remaining

                    # If no bin can fit this key, create a new one
                    if best_bin == -1:
                        bins.append([key])
                        bin_sizes.append(tokens)
                    else:
                        bins[best_bin].append(key)
                        bin_sizes[best_bin] += tokens

                # Create chunks from bins
                chunks = []
                for bin_keys in bins:
                    if not bin_keys:
                        continue

                    chunk_data = {k: functions_data[k] for k in bin_keys}
                    chunks.append(chunk_data)

                logger.info(f"Created {len(chunks)} balanced chunks")
                return chunks

    async def _process_chunk(self, chunk_data: Union[Dict[str, Any], str], chunk_id: int, total_file_count: int = 0) -> ChunkProcessingResult:
        """
        Process a single chunk of functions data.

        Args:
            chunk_data: Dictionary or string containing a subset of function data
            chunk_id: Identifier for this chunk
            total_file_count: Total number of files in the entire codebase

        Returns:
            ChunkProcessingResult containing the analysis results for this chunk
        """
        # Format the chunk data for the API request
        if isinstance(chunk_data, str):
            # It's already a string (repomap text format)
            data_str = chunk_data
            # Count the number of files in this chunk
            chunk_file_count = data_str.count('│') - data_str.count('⋮...')
            logger.info(f"Processing chunk {chunk_id} with approximately {chunk_file_count} files")
        else:
            # It's a dictionary, convert to YAML
            data_str = yaml.dump(chunk_data, default_flow_style=False)
            # For dictionary data, we don't have a direct file count, so we'll use the number of keys
            chunk_file_count = len(chunk_data)
            logger.info(f"Processing chunk {chunk_id} with {chunk_file_count} functions")

        # Log chunk size for debugging
        estimated_lines = len(data_str.splitlines())
        logger.info(f"Chunk {chunk_id}: Estimated lines: {estimated_lines}")

        # Use the total file count for the entire codebase instead of the chunk's file count
        if total_file_count > 0:
            logger.info(f"Using total file count for granularity guidance: {total_file_count}")
            file_count = total_file_count
        else:
            # Fallback to chunk file count if total count is not provided
            logger.info(f"No total file count provided, using chunk file count: {chunk_file_count}")
            file_count = chunk_file_count

        # Determine granularity guidance based on total codebase size
        granularity_guidance = self._get_granularity_guidance(file_count)

        # Create the API request
        logger.info(f"Preparing API request for chunk {chunk_id}")

        # Define the JSON example based on whether explanations are required
        if self.generate_explanations:
            json_example = '''{"areas": [{"name": "Top-Level Area 1", "subareas": [{"name": "Subarea A", "subareas": [{"name": "Sub-subarea i", "subareas": [{"name": "Sub-sub-subarea x", "subareas": [], "explanation": "This is a leaf domain that handles specific functionality X. It is responsible for processing and managing X-related operations."}]}]}]}]}'''
        else:
            json_example = '''{"areas": [{"name": "Top-Level Area 1", "subareas": [{"name": "Subarea A", "subareas": [{"name": "Sub-subarea i", "subareas": [{"name": "Sub-sub-subarea x", "subareas": []}]}]}]}]}'''

        # Create system prompt based on whether explanations are required
        system_prompt = f"""You are an expert software architect analyzing a portion of a codebase and its structure.

Your task:
Granularity Guidance: "{granularity_guidance}"
- Identify the top-level groups (e.g., domains or areas) as needed.
- For each group, create subgroups, sub-subgroups, and so on. Adapt it as the code structure demands. Follow granularity guidance provided.
- **Do not** list any functions, files, or other artifacts in this output.
- The top domains, areas, sub-areas, sub-sub-areas can be as much plenty as needed
- The creation should be logic based, functionality based, and business application based as applicable.
- Note that you are analyzing only a portion of the codebase, so focus on creating a coherent structure for this subset."""

        # Add explanation requirements if needed
        if self.generate_explanations:
            system_prompt += """
- For each leaf domain (domains with empty subareas array), provide a concise 2-3 line explanation of what that domain does.
- Only leaf domains should have explanations. Non-leaf domains should not have explanations.
- A leaf domain is any domain that has an empty subareas array."""

        # Add output format
        system_prompt += f"""

**Output Format** (in valid JSON):

{json_example}"""

        # Create user prompt based on whether explanations are required
        if self.generate_explanations:
            user_prompt = f"""Please analyze this codebase structure and organize it into domains and subdomains:\n\n{data_str}

Please return the nested hierarchy of domains (or areas) without any function/file references. For leaf domains (those with empty subareas), include a concise 2-3 line explanation of what that domain does. Output your hierarchy in valid JSON format, following the outlined format."""
        else:
            user_prompt = f"""Please analyze this codebase structure and organize it into domains and subdomains:\n\n{data_str}

Please return the nested hierarchy of domains (or areas) without any function/file references. Output your hierarchy in valid JSON format, following the outlined format."""

        request = {
            "model": self.model,
            "messages": [
                {
                    "role": "system",
                    "content": system_prompt
                },
                {
                    "role": "user",
                    "content": user_prompt
                }
            ],
        }

        # Call the OpenAI API
        logger.info(f"Calling API for chunk {chunk_id} domain classification")
        response_json, error = await self._call_openai_api(request)

        if error:
            logger.error(f"Error processing chunk {chunk_id}: {error}")
            return ChunkProcessingResult(
                chunk_id=chunk_id,
                success=False,
                error_message=error
            )

        # Extract the raw content first to ensure we always have it
        try:
            content = response_json["choices"][0]["message"]["content"]
        except (KeyError, IndexError, TypeError):
            # If we can't extract content in the expected format, use the whole response
            content = str(response_json)
            logger.warning(f"Could not extract content from chunk {chunk_id} response, using full response: {content[:100]}...")

        # Create a result with the raw response first
        result = ChunkProcessingResult(
            chunk_id=chunk_id,
            raw_response=content,
            success=False,
            error_message="Not yet processed"
        )

        # Now try to parse the JSON content
        try:
            # Find JSON content between triple backticks
            json_content = content
            if "```json" in content:
                json_content = content.split("```json")[1].split("```")[0].strip()
            elif "```" in content:
                json_content = content.split("```")[1].split("```")[0].strip()

            # Parse the JSON content
            domains_data = json.loads(json_content)

            # Update the result with the parsed domains
            result.domains = domains_data
            result.success = True
            result.error_message = ""

            logger.info(f"Successfully processed chunk {chunk_id}")
            return result

        except Exception as e:
            logger.error(f"Error parsing API response for chunk {chunk_id}: {e}")
            result.error_message = f"Failed to parse API response: {str(e)}"
            return result

    def _get_granularity_guidance(self, file_count: int) -> str:
        """
        Get granularity guidance based on the number of files in the codebase.
        Provides strict maximum depth limits appropriate to codebase size.

        Args:
            file_count: Number of files in the codebase

        Returns:
            String containing granularity guidance with strict depth limits
        """
        if file_count < 100:  # Tiny projects/libraries
            return f"For this codebase ({file_count} files): STRICT MAXIMUM DEPTH OF 2. Create 3-6 top-level domains with 1-2 subdomains each. Prioritize a clean, shallow hierarchy over deep nesting."

        elif file_count < 500:  # Small projects
            return f"For this codebase ({file_count} files): STRICT MAXIMUM DEPTH OF 2. Create 4-7 top-level domains with 1-3 subdomains each."

        elif file_count < 1000:  # Medium projects
            return f"For this above medium codebase ({file_count} files): STRICT MAXIMUM DEPTH OF 3. Create 5-8 top-level domains with 2-4 subdomains each. Prioritize logical grouping over perfectionism."

        elif file_count < 3000:  # Large projects
            return f"For this large codebase ({file_count} files): STRICT MAXIMUM DEPTH OF 3. Create 7-10 top-level domains with 3-5 subdomains each."

        elif file_count < 5000:  # Very large projects (PyTorch scale)
            return f"For this very large codebase ({file_count} files): STRICT MAXIMUM DEPTH OF 3. Create 8-12 top-level domains with multiple subdomains. Balance between breadth and depth, favoring breadth."

        elif file_count < 10000:  # Extremely large projects
            return f"For this extremely large codebase ({file_count} files): STRICT MAXIMUM DEPTH OF 3. Create 9-13 top-level domains with 3-6 subdomains each."

        elif file_count < 20000:  # Massive projects (GitLab scale)
            return f"For this massive codebase ({file_count} files): STRICT MAXIMUM DEPTH OF 3. Create 11-16 top-level domains with well-balanced subdomains."

        else:  # Enormous monoliths
            return f"For this enormous codebase ({file_count} files): STRICT MAXIMUM DEPTH OF 3. Create 13-19 top-level domains with 4-8 subdomains each. Focus on creating a manageable hierarchy that balances breadth and depth."

    async def _merge_domain_results(self, chunk_results: List[ChunkProcessingResult]) -> Dict[str, Any]:
        """
        Merge the domain results from multiple chunks using LLM.

        Args:
            chunk_results: List of ChunkProcessingResult objects

        Returns:
            Merged domains dictionary
        """
        # If there's only one successful chunk, just return its domains
        successful_results = [r for r in chunk_results if r.success and "areas" in r.domains]

        if len(successful_results) == 0:
            logger.warning("No successful chunks to merge")
            return {"areas": []}

        if len(successful_results) == 1:
            logger.info("Only one successful chunk, no need to merge")
            return successful_results[0].domains

        # For multiple chunks, use LLM to merge them
        logger.info(f"Using LLM to merge {len(successful_results)} chunks")

        # Convert all domains to YAML for the LLM
        domains_list = []
        for i, result in enumerate(successful_results):
            domains_yaml = yaml.dump(result.domains, default_flow_style=False)
            domains_list.append(f"Chunk {i+1}:\n{domains_yaml}")

        all_domains = "\n\n---\n\n".join(domains_list)

        # Create system prompt based on whether explanations are required
        system_prompt = """You are an expert software architect tasked with merging domain hierarchies.

You will be given multiple domain hierarchies that were generated by analyzing different parts of a large codebase.
Each hierarchy is in JSON format with an 'areas' array containing top-level domains.

Your task:
1. Identify similar domains across the different hierarchies
2. Merge them into a single coherent hierarchy
3. Ensure no information is lost during the merge
4. Maintain the same structure (areas with subareas)
5. Ensure the final hierarchy is comprehensive and logical"""

        # Add explanation requirements if needed
        if self.generate_explanations:
            system_prompt += """
6. Preserve the explanations for leaf domains (domains with empty subareas)
7. If you merge leaf domains, combine their explanations appropriately
8. Ensure all leaf domains have explanations in the final output"""

        system_prompt += """

Output the merged hierarchy in the same JSON format with an 'areas' array at the top level.
"""

        # Create the merging request
        request = {
            "model": self.model,
            "messages": [
                {
                    "role": "system",
                    "content": system_prompt
                },
                {
                    "role": "user",
                    "content": f"""Here are multiple domain hierarchies generated from different parts of a codebase.
Please merge them into a single coherent hierarchy.\n\n{all_domains}

Please return the merged hierarchy in the same JSON format with an 'areas' array at the top level."""
                }
            ],
        }

        # Call the API
        logger.info("Calling API for initial domain merging")
        response_json, error = await self._call_openai_api(request)

        if error:
            logger.warning(f"LLM-based merging failed: {error}. Falling back to simple merging.")
            # Fall back to simple merging if LLM fails
            return self._simple_merge_domains(successful_results)

        # Extract the raw content first to ensure we always have it
        try:
            content = response_json["choices"][0]["message"]["content"]
        except (KeyError, IndexError, TypeError):
            # If we can't extract content in the expected format, use the whole response
            content = str(response_json)
            logger.warning(f"Could not extract content from merge response, using full response: {content[:100]}...")

        # Always save the merge response to a file for reference
        try:
            merge_file_path = f"merge_response_{int(time.time())}.txt"
            with open(merge_file_path, 'w') as f:
                f.write("Raw Merge Response:\n")
                f.write(content)
            logger.info(f"Saved merge response to {merge_file_path}")
        except Exception as save_error:
            logger.error(f"Failed to save merge response: {save_error}")

        # Now try to parse the JSON content
        try:
            # Find JSON content between triple backticks
            json_content = content
            if "```json" in content:
                json_content = content.split("```json")[1].split("```")[0].strip()
            elif "```" in content:
                json_content = content.split("```")[1].split("```")[0].strip()

            # Parse the JSON content
            merged_domains = json.loads(json_content)

            logger.info(f"Successfully merged chunks into {len(merged_domains.get('areas', []))} top-level domains using LLM")
            return merged_domains

        except Exception as e:
            logger.warning(f"Error parsing LLM merging response: {e}. Falling back to simple merging.")
            # Fall back to simple merging if parsing fails
            return self._simple_merge_domains(successful_results)

    def _simple_merge_domains(self, chunk_results: List[ChunkProcessingResult]) -> Dict[str, Any]:
        """
        Simple heuristic-based merging as a fallback when LLM-based merging fails.

        Args:
            chunk_results: List of ChunkProcessingResult objects

        Returns:
            Merged domains dictionary
        """
        # Start with an empty areas list
        merged_domains = {"areas": []}

        # Track domain names to avoid duplicates
        domain_names = set()

        # Process each chunk result
        for result in chunk_results:
            if not result.success or "areas" not in result.domains:
                continue

            # Add each area from this chunk
            for area in result.domains["areas"]:
                area_name = area.get("name", "")
                if not area_name or area_name in domain_names:
                    continue

                domain_names.add(area_name)
                merged_domains["areas"].append(area)

        logger.info(f"Merged results into {len(merged_domains['areas'])} top-level domains using simple merging")
        return merged_domains

    def _process_json_files_to_text(self, json_data: Dict[str, List[str]]) -> str:
        """
        Process a subset of JSON repomap files into a text format suitable for domain analysis.
        This method combines code lines for each file into a single string with newlines.

        Args:
            json_data: Dictionary where keys are file paths and values are lists of code lines

        Returns:
            String representation of the files suitable for domain analysis
        """
        processed_data = []

        for file_path, code_lines in json_data.items():
            # Add file header
            processed_data.append(f"│ {file_path}:")

            # Combine code lines into a single string with proper indentation
            combined_code = "\n│   ".join(code_lines)
            processed_data.append(f"│   {combined_code}")

            # Add separator between files
            processed_data.append("│")

        # Join all lines into a single string
        return "\n".join(processed_data)

    def _process_json_repomap(self, json_data: Dict[str, List[str]]) -> str:
        """
        Process a JSON repomap into a format suitable for domain analysis.

        In the JSON format, keys are file paths and values are lists of strings (code lines).
        This method converts this format into a text representation similar to the
        complete_repomap_bracket.txt format.

        Args:
            json_data: Dictionary where keys are file paths and values are lists of code lines

        Returns:
            String representation of the repomap suitable for domain analysis
        """
        return self._process_json_files_to_text(json_data)

    async def create_hierar_domain_struct(self, input_path: str, output_path: str, max_tokens_per_chunk: int = 500000, disable_parallel: bool = False, max_concurrent_tasks: int = 0) -> DomainAnalysisResult:
        """
        Analyze a file containing codebase structure and classify it into domains.
        For large codebases, the data is split into chunks and processed in parallel.

        Args:
            input_path: Path to the input file (complete_repomap_bracket.txt, YAML file, or JSON file)
            output_path: Path to save the domain classification output
            max_tokens_per_chunk: Maximum tokens per chunk for parallel processing (default: 500000)
            disable_parallel: Whether to disable parallel processing (default: False)
            max_concurrent_tasks: Maximum number of concurrent tasks (default: 0 = auto-detect based on CPU cores)

        Returns:
            DomainAnalysisResult containing the analysis results
        """
        # Determine file type based on extension
        is_text_format = input_path.endswith('.txt')
        is_json_format = input_path.endswith('.json')

        # Variable to store total file count for the entire codebase
        total_file_count = 0

        if is_json_format:
            # Read the JSON file
            logger.info(f"Reading repomap JSON file: {input_path}")
            try:
                with open(input_path, 'r') as f:
                    json_data = json.load(f)
            except Exception as e:
                logger.error(f"Error reading JSON file: {e}")
                return DomainAnalysisResult(
                    success=False,
                    error_message=f"Failed to read JSON file: {str(e)}"
                )

            if not json_data:
                logger.error("JSON file is empty or could not be parsed")
                return DomainAnalysisResult(
                    success=False,
                    error_message="JSON file is empty or could not be parsed"
                )

            # Process the JSON data
            # In this format, keys are file paths and values are lists of strings (code lines)
            logger.info(f"Processing JSON repomap with {len(json_data)} files")

            # Convert JSON to a format suitable for processing
            processed_data = self._process_json_repomap(json_data)

            # Count tokens in the processed data
            total_tokens = count_tokens(processed_data)
            logger.info(f"Total tokens in JSON data: {total_tokens}")

            # For JSON data, use the number of keys (file paths) as the file count
            total_file_count = len(json_data)
            logger.info(f"Total file count in JSON data: {total_file_count}")

            # Determine if we need to split the data
            if total_tokens <= max_tokens_per_chunk or disable_parallel:
                # Small enough to process in one go or parallel processing is disabled
                if disable_parallel:
                    logger.info("Parallel processing disabled, processing JSON data in a single request")
                else:
                    logger.info("Processing JSON data in a single request")
                result = await self.significant_fns_to_domain_structs(processed_data, total_file_count)
            else:
                # Split into chunks and process in parallel
                logger.info(f"JSON data exceeds token limit ({total_tokens} > {max_tokens_per_chunk}), splitting into chunks")
                chunks = self._split_functions_into_chunks(processed_data, max_tokens_per_chunk)

                # Process chunks in parallel
                result = await self._process_chunks_in_parallel(chunks, max_concurrent_tasks, total_file_count)
        elif is_text_format:
            # Read the text file
            logger.info(f"Reading repomap text file: {input_path}")
            try:
                with open(input_path, 'r') as f:
                    text_data = f.read()
            except Exception as e:
                logger.error(f"Error reading text file: {e}")
                return DomainAnalysisResult(
                    success=False,
                    error_message=f"Failed to read text file: {str(e)}"
                )

            if not text_data:
                logger.error("Text file is empty")
                return DomainAnalysisResult(
                    success=False,
                    error_message="Text file is empty"
                )

            # Count tokens in the text data
            total_tokens = count_tokens(text_data)
            logger.info(f"Total tokens in text data: {total_tokens}")

            # Count the number of files in the entire codebase
            total_file_count = text_data.count('│') - text_data.count('⋮...')
            logger.info(f"Total file count in codebase: {total_file_count}")

            # Determine if we need to split the data
            if total_tokens <= max_tokens_per_chunk or disable_parallel:
                # Small enough to process in one go or parallel processing is disabled
                if disable_parallel:
                    logger.info("Parallel processing disabled, processing text data in a single request")
                else:
                    logger.info("Processing text data in a single request")
                result = await self.significant_fns_to_domain_structs(text_data, total_file_count)
            else:
                # Split into chunks and process in parallel
                logger.info(f"Text data exceeds token limit ({total_tokens} > {max_tokens_per_chunk}), splitting into chunks")
                chunks = self._split_functions_into_chunks(text_data, max_tokens_per_chunk)

                # Process chunks in parallel
                result = await self._process_chunks_in_parallel(chunks, max_concurrent_tasks, total_file_count)
        else:
            # Read the YAML file
            logger.info(f"Reading YAML file: {input_path}")
            try:
                with open(input_path, 'r') as f:
                    yaml_data = yaml.safe_load(f)
            except Exception as e:
                logger.error(f"Error reading YAML file: {e}")
                return DomainAnalysisResult(
                    success=False,
                    error_message=f"Failed to read YAML file: {str(e)}"
                )

            if not yaml_data:
                logger.error("YAML file is empty or could not be parsed")
                return DomainAnalysisResult(
                    success=False,
                    error_message="YAML file is empty or could not be parsed"
                )

            # Estimate the size of the YAML data
            yaml_str = yaml.dump(yaml_data, default_flow_style=False)
            total_tokens = count_tokens(yaml_str)
            logger.info(f"Total tokens in YAML data: {total_tokens}")

            # For YAML data, use the number of keys as an approximation of file count
            total_file_count = len(yaml_data)
            logger.info(f"Total file/function count in YAML data: {total_file_count}")

            # Determine if we need to split the data
            if total_tokens <= max_tokens_per_chunk or disable_parallel:
                # Small enough to process in one go or parallel processing is disabled
                if disable_parallel:
                    logger.info("Parallel processing disabled, processing YAML data in a single request")
                else:
                    logger.info("Processing YAML data in a single request")
                result = await self.significant_fns_to_domain_structs(yaml_data, total_file_count)
            else:
                # Split into chunks and process in parallel
                logger.info(f"YAML data exceeds token limit ({total_tokens} > {max_tokens_per_chunk}), splitting into chunks")
                chunks = self._split_functions_into_chunks(yaml_data, max_tokens_per_chunk)

                # Process chunks in parallel
                result = await self._process_chunks_in_parallel(chunks, max_concurrent_tasks, total_file_count)

        # Always save the raw response, even if parsing failed
        if output_path:
            try:
                # Save the raw response first to ensure we don't lose it
                raw_output_path = f"{output_path}.raw"
                with open(raw_output_path, 'w') as f:
                    f.write(result.raw_response)
                logger.info(f"Raw response saved to: {raw_output_path}")

                # If parsing was successful, also save the structured domains
                if result.success:
                    with open(output_path, 'w') as f:
                        yaml.dump(result.domains, f, default_flow_style=False)
                    logger.info(f"Domain classification saved to: {output_path}")
                else:
                    # If parsing failed, save a backup text file with the raw content
                    error_output_path = f"{output_path}.error.txt"
                    with open(error_output_path, 'w') as f:
                        f.write(f"Error: {result.error_message}\n\n")
                        f.write("Raw API Response:\n")
                        f.write(result.raw_response)
                    logger.info(f"Error details and raw response saved to: {error_output_path}")
            except Exception as e:
                logger.error(f"Error saving results: {e}")
                result.error_message = f"{result.error_message}\nFailed to save results: {str(e)}"

        return result

    async def _process_chunks_in_parallel(self, chunks, max_concurrent_tasks: int = 0, total_file_count: int = 0) -> DomainAnalysisResult:
        """
        Process chunks in parallel with rate limiting.

        Args:
            chunks: List of chunks to process
            max_concurrent_tasks: Maximum number of concurrent tasks
            total_file_count: Total number of files in the entire codebase

        Returns:
            DomainAnalysisResult containing the merged results
        """
        # Process chunks in parallel with rate limiting
        if max_concurrent_tasks <= 0:
            # Auto-detect based on CPU cores if not specified
            concurrent_tasks = min(8, os.cpu_count() or 4)  # Limit to 8 or number of CPU cores
        else:
            concurrent_tasks = max_concurrent_tasks

        logger.info(f"Processing {len(chunks)} chunks with max {concurrent_tasks} concurrent tasks")

        # Create a semaphore to limit concurrent tasks
        semaphore = asyncio.Semaphore(concurrent_tasks)

        async def process_with_semaphore(chunk, chunk_id):
            async with semaphore:
                return await self._process_chunk(chunk, chunk_id, total_file_count)

        # Create tasks with semaphore
        tasks = []
        for i, chunk in enumerate(chunks):
            task = asyncio.create_task(process_with_semaphore(chunk, i))
            tasks.append(task)

        # Process all chunks with rate limiting
        chunk_results = await asyncio.gather(*tasks)

        # Check if all chunks were processed successfully
        successful_chunks = sum(1 for r in chunk_results if r.success)
        logger.info(f"Successfully processed {successful_chunks} out of {len(chunks)} chunks")

        if successful_chunks == 0:
            # All chunks failed
            error_messages = [r.error_message for r in chunk_results if not r.success]
            return DomainAnalysisResult(
                success=False,
                error_message=f"All chunks failed processing: {'; '.join(error_messages[:3])}..."
            )

        # Merge the results from all successful chunks
        merged_domains = await self._merge_domain_results([r for r in chunk_results if r.success])

        # Create a combined raw response
        combined_raw = "\n\n".join([f"Chunk {r.chunk_id}:\n{r.raw_response}" for r in chunk_results if r.success])

        # Create the final result
        result = DomainAnalysisResult(
            domains=merged_domains,
            raw_response=combined_raw,
            success=True
        )

        # If we have enough successful chunks, perform a final consolidation step
        if successful_chunks > 1 and successful_chunks >= len(chunks) * 0.7:  # At least 70% success rate
            logger.info("Performing final consolidation step to merge similar domains")
            result = await self._consolidate_domains(result)

        return result

    async def _consolidate_domains(self, result: DomainAnalysisResult) -> DomainAnalysisResult:
        """
        Perform a final consolidation step to merge similar domains.

        Args:
            result: Initial domain analysis result with potentially redundant domains

        Returns:
            Consolidated domain analysis result
        """
        # Convert the domains to YAML for the LLM
        domains_yaml = yaml.dump(result.domains, default_flow_style=False)

        # Create system prompt based on whether explanations are required
        system_prompt = """You are an expert software architect tasked with consolidating domain hierarchies.

You will be given a domain hierarchy that was generated by analyzing a large codebase in chunks.
This may have resulted in some redundancy or overlap between domains.

Your task:
1. Identify similar or redundant domains and merge them
2. Ensure the hierarchy remains coherent and logical
3. Maintain the same overall structure and naming conventions
4. Remove any duplicated subdomains
5. Ensure the final hierarchy is comprehensive and covers all functionality"""

        # Add explanation requirements if needed
        if self.generate_explanations:
            system_prompt += """
6. Preserve the explanations for leaf domains (domains with empty subareas)
7. If you merge leaf domains, combine their explanations appropriately
8. Ensure all leaf domains have explanations in the final output"""

        system_prompt += """

Output the consolidated hierarchy in the same JSON format as the input.
"""

        # Create the consolidation request
        request = {
            "model": self.model,
            "messages": [
                {
                    "role": "system",
                    "content": system_prompt
                },
                {
                    "role": "user",
                    "content": f"""Here is a domain hierarchy that was generated by analyzing a large codebase in chunks.
Please consolidate it by merging similar domains and removing redundancy while maintaining comprehensive coverage.\n\n{domains_yaml}

Please return the consolidated hierarchy in the same JSON format."""
                }
            ],
        }

        # Call the OpenAI API
        logger.info("Calling API for domain consolidation")
        response_json, error = await self._call_openai_api(request)

        if error:
            logger.warning(f"Domain consolidation failed: {error}. Using original domains.")
            return result

        # Extract the raw content first to ensure we always have it
        try:
            content = response_json["choices"][0]["message"]["content"]
        except (KeyError, IndexError, TypeError):
            # If we can't extract content in the expected format, use the whole response
            content = str(response_json)
            logger.warning(f"Could not extract content from consolidation response, using full response: {content[:100]}...")

        # Always save the consolidation response by appending it to the original raw response
        updated_result = DomainAnalysisResult(
            domains=result.domains,
            raw_response=result.raw_response + "\n\n=== CONSOLIDATION ===\n\n" + content,
            success=result.success,
            error_message=result.error_message
        )

        # Now try to parse the JSON content
        try:
            # Find JSON content between triple backticks
            json_content = content
            if "```json" in content:
                json_content = content.split("```json")[1].split("```")[0].strip()
            elif "```" in content:
                json_content = content.split("```")[1].split("```")[0].strip()

            # Parse the JSON content
            consolidated_domains = json.loads(json_content)

            # Update the result with the parsed domains
            updated_result.domains = consolidated_domains
            updated_result.success = True
            updated_result.error_message = ""

            logger.info("Successfully consolidated domains")
            return updated_result

        except Exception as e:
            logger.warning(f"Error parsing consolidation response: {e}. Using original domains.")
            # Return the result with the original domains but updated raw response
            return updated_result

    async def significant_fns_to_domain_structs(self, functions_data: Union[Dict[str, Any], str], total_file_count: int = 0) -> DomainAnalysisResult:
        """
        Analyze functions data and classify them into domains and subdomains.

        Args:
            functions_data: Dictionary containing function data or string containing repomap text
            total_file_count: Total number of files in the entire codebase

        Returns:
            DomainAnalysisResult containing the analysis results
        """
        # Format the functions data for the API request
        if isinstance(functions_data, str):
            # It's already a string (repomap text format)
            data_str = functions_data
            # Count the number of files in this data
            data_file_count = data_str.count('│') - data_str.count('⋮...')
            logger.info(f"Processing repomap text with approximately {data_file_count} files")
        else:
            # It's a dictionary, convert to YAML
            data_str = yaml.dump(functions_data, default_flow_style=False)
            # For dictionary data, use the number of keys as an approximation of file count
            data_file_count = len(functions_data)
            logger.info(f"Processing YAML data with {data_file_count} functions")

        # Calculate data size for logging
        estimated_lines = len(data_str.splitlines())
        logger.info(f"Data estimated lines: {estimated_lines}")

        # Use the total file count for the entire codebase if provided
        if total_file_count > 0:
            logger.info(f"Using provided total file count for granularity guidance: {total_file_count}")
            file_count = total_file_count
        else:
            # Otherwise use the current data's file count
            logger.info(f"No total file count provided, using data file count: {data_file_count}")
            file_count = data_file_count

        # Get granularity guidance based on file count
        granularity_guidance = self._get_granularity_guidance(file_count)

        # Create the API request
        logger.info("Preparing API request for domain classification")

        # Define the JSON example based on whether explanations are required
        if self.generate_explanations:
            json_example = '''{
  "areas": [
    {
      "name": "Top-Level Area 1",
      "subareas": [
        {
          "name": "Subarea A",
          "subareas": [
            {
              "name": "Sub-subarea i",
              "subareas": [
                {
                  "name": "Sub-sub-subarea x",
                  "subareas": [],
                  "explanation": "This is a leaf domain that handles specific functionality X. It is responsible for processing and managing X-related operations."
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}'''
        else:
            json_example = '''{
  "areas": [
    {
      "name": "Top-Level Area 1",
      "subareas": [
        {
          "name": "Subarea A",
          "subareas": [
            {
              "name": "Sub-subarea i",
              "subareas": [
                {
                  "name": "Sub-sub-subarea x",
                  "subareas": []
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}'''

        # Create system prompt based on whether explanations are required
        system_prompt = f"""You are an expert software architect analyzing an entire codebase and its structure.

Your task:
Granularity Guidance: "{granularity_guidance}"
- Identify the top-level groups (e.g., domains or areas) as needed.
- For each group, create subgroups, sub-subgroups, and so on. Adapt it as the code structure demands. Follow granularity guidance provided.
- **Do not** list any functions, files, or other artifacts in this output.
- The focus here is primarily on outlining the hierarchical structure of the code's functionalities.
- The top domains, areas, sub-areas, sub-sub-areas can be as much plenty as needed
- The creation should be logic based, functionality based, and business application based as applicable."""

        # Add explanation requirements if needed
        if self.generate_explanations:
            system_prompt += """
- For each leaf domain (domains with empty subareas array), provide a concise 2-3 line explanation of what that domain does.
- Only leaf domains should have explanations. Non-leaf domains should not have explanations.
- A leaf domain is any domain that has an empty subareas array."""

        # Add output format
        system_prompt += f"""

**Output Format** (in valid JSON):

{json_example}"""

        # Determine the appropriate user prompt based on the input type and whether explanations are required
        if isinstance(functions_data, str):
            if self.generate_explanations:
                user_prompt = f"""Please analyze this codebase structure and organize it into domains and subdomains:\n\n{data_str}

Please return the nested hierarchy of domains (or areas) without any function/file references. For leaf domains (those with empty subareas), include a concise 2-3 line explanation of what that domain does. Output your hierarchy in valid JSON format, following the outlined format."""
            else:
                user_prompt = f"""Please analyze this codebase structure and organize it into domains and subdomains:\n\n{data_str}

Please return the nested hierarchy of domains (or areas) without any function/file references. Output your hierarchy in valid JSON format, following the outlined format."""
        else:
            if self.generate_explanations:
                user_prompt = f"""Please analyze these functions and organize them into domains and subdomains:\n\n{data_str}

Please return the nested hierarchy of domains (or areas) without any function/file references. For leaf domains (those with empty subareas), include a concise 2-3 line explanation of what that domain does. Output your hierarchy in valid JSON format, following the outlined format."""
            else:
                user_prompt = f"""Please analyze these functions and organize them into domains and subdomains:\n\n{data_str}

Please return the nested hierarchy of domains (or areas) without any function/file references. Output your hierarchy in valid JSON format, following the outlined format."""

        request = {
            "model": self.model,
            "messages": [
                {
                    "role": "system",
                    "content": system_prompt
                },
                {
                    "role": "user",
                    "content": user_prompt
                }
            ],
            # "temperature": self.temperature,
            # "max_tokens": 10000
        }

        # Call the API
        logger.info("Calling API for domain classification")
        response_json, error = await self._call_openai_api(request)

        if error:
            return DomainAnalysisResult(
                success=False,
                error_message=error
            )

        # Extract the raw content first to ensure we always have it
        try:
            content = response_json["choices"][0]["message"]["content"]
        except (KeyError, IndexError, TypeError):
            # If we can't extract content in the expected format, use the whole response
            content = str(response_json)
            logger.warning(f"Could not extract content from response, using full response: {content[:100]}...")

        # Create a result with the raw response first
        result = DomainAnalysisResult(
            raw_response=content,
            success=False,
            error_message="Not yet processed"
        )

        # Now try to parse the JSON content
        try:
            # Find JSON content between triple backticks
            json_content = content
            if "```json" in content:
                json_content = content.split("```json")[1].split("```")[0].strip()
            elif "```" in content:
                json_content = content.split("```")[1].split("```")[0].strip()

            # Parse the JSON content
            domains_data = json.loads(json_content)

            # Update the result with the parsed domains
            result.domains = domains_data
            result.success = True
            result.error_message = ""

            logger.info("Successfully parsed API response")
            return result

        except Exception as e:
            logger.error(f"Error parsing API response: {e}")
            result.error_message = f"Failed to parse API response: {str(e)}"
            return result

    async def _call_openai_api(self, request: Dict[str, Any]) -> Tuple[Dict[str, Any], Optional[str]]:
        """
        Call the OpenAI API, Claude API, or OpenRouter API with rate limiting and error handling.

        Args:
            request: API request payload

        Returns:
            Tuple of (response_json, error_message)
        """
        # Extract messages from request JSON
        messages = request.get("messages", [])
        system_prompt = None
        user_prompt = None

        # Extract system and user prompts from messages
        for message in messages:
            if message["role"] == "system":
                system_prompt = message["content"]
            elif message["role"] == "user":
                user_prompt = message["content"]

        if not user_prompt:
            return {}, "No user prompt found in request"

        # Check token count before making API call
        total_prompt_tokens = 0
        if system_prompt:
            system_tokens = count_tokens(system_prompt)
            total_prompt_tokens += system_tokens
            logger.info(f"System prompt tokens: {system_tokens}")

        if user_prompt:
            user_tokens = count_tokens(user_prompt)
            total_prompt_tokens += user_tokens
            logger.info(f"User prompt tokens: {user_tokens}")

        logger.info(f"Total estimated prompt tokens: {total_prompt_tokens}")

        # Define token limits for different models
        MODEL_TOKEN_LIMITS = {
            # Claude models
            "claude-3-5-sonnet-20241022": 180000,
            "claude-3-7-sonnet-20250219": 180000,
            "claude-3-opus-20240229": 180000,
            "claude-3-haiku-20240307": 180000,
            # OpenAI models
            "gpt-4o": 120000,
            "gpt-4o-mini": 120000,
            "o3-mini": 120000,
            "o4-mini": 120000,
            # Default for unknown models
            "default": 100000
        }

        # Get the appropriate token limit
        if self.use_claude:
            token_limit = MODEL_TOKEN_LIMITS.get(self.claude_model, MODEL_TOKEN_LIMITS["default"])
        elif self.use_openrouter:
            # OpenRouter with Gemini has a large context window
            token_limit = 180000
        else:
            token_limit = MODEL_TOKEN_LIMITS.get(self.model, MODEL_TOKEN_LIMITS["default"])

        # Add a safety margin (20% below the limit)
        safe_token_limit = int(token_limit * 0.8)

        # Check if we're exceeding the token limit
        if total_prompt_tokens > safe_token_limit:
            error_msg = f"Prompt too large: {total_prompt_tokens} tokens exceeds safe limit of {safe_token_limit} tokens for the model"
            logger.error(error_msg)
            return {}, error_msg

        # Use OpenRouter client if available and enabled
        if self.use_openrouter and self.openrouter_client:
            try:
                # Call the OpenRouter client
                content = await self.openrouter_client.generate(
                    prompt=user_prompt,
                    system_prompt=system_prompt,
                    temperature=self.temperature
                )

                # Create a response in the same format as OpenAI
                response_json = {
                    "choices": [
                        {
                            "message": {
                                "content": content
                            }
                        }
                    ]
                }

                return response_json, None

            except Exception as e:
                logger.error(f"OpenRouter API error: {e}")
                return {}, f"OpenRouter API error: {str(e)}"

        # Use Claude client if available and enabled
        elif self.use_claude and self.claude_client:
            try:
                # Call the Claude client
                content = await self.claude_client.generate(
                    prompt=user_prompt,
                    system_prompt=system_prompt,
                    temperature=self.temperature,
                    max_tokens=8000  # Claude has a smaller max token limit for output
                )

                # Create a response in the same format as OpenAI
                response_json = {
                    "choices": [
                        {
                            "message": {
                                "content": content
                            }
                        }
                    ]
                }

                return response_json, None

            except Exception as e:
                logger.error(f"Claude API error: {e}")
                return {}, f"Claude API error: {str(e)}"

        else:
            # Use OpenAI API
            max_retries = 5
            retry_delay = 5  # seconds
            seconds_to_pause_after_rate_limit_error = 15

            for attempt in range(max_retries):
                # Check if we need to pause due to a recent rate limit error
                seconds_since_rate_limit_error = time.time() - self.status_tracker.time_of_last_rate_limit_error
                if seconds_since_rate_limit_error < seconds_to_pause_after_rate_limit_error:
                    remaining_seconds_to_pause = seconds_to_pause_after_rate_limit_error - seconds_since_rate_limit_error
                    logger.warning(f"Pausing for {remaining_seconds_to_pause:.1f} seconds due to recent rate limit error")
                    await asyncio.sleep(remaining_seconds_to_pause)

                try:
                    async with aiohttp.ClientSession() as session:
                        async with session.post(
                            "https://api.openai.com/v1/chat/completions",
                            headers={
                                "Authorization": f"Bearer {self.api_key}",
                                "Content-Type": "application/json"
                            },
                            json=request,
                            timeout=aiohttp.ClientTimeout(total=120)  # Using proper ClientTimeout object
                        ) as response:
                            response_json = await response.json()

                            if response.status == 200:
                                return response_json, None

                            # Handle errors
                            error_message = response_json.get("error", {}).get("message", f"API error: {response.status}")

                            if response.status == 429:  # Rate limit error
                                self.status_tracker.num_rate_limit_errors += 1
                                self.status_tracker.time_of_last_rate_limit_error = time.time()
                                logger.warning(f"Rate limit error: {error_message}")
                                retry_delay = min(retry_delay * 2, 60)  # Exponential backoff, max 60 seconds
                            else:
                                self.status_tracker.num_api_errors += 1
                                logger.error(f"API error (status {response.status}): {error_message}")

                            if attempt < max_retries - 1:
                                logger.info(f"Retrying in {retry_delay} seconds (attempt {attempt+1}/{max_retries})")
                                await asyncio.sleep(retry_delay)
                                retry_delay = min(retry_delay * 2, 60)  # Exponential backoff, max 60 seconds
                            else:
                                return {}, f"API error after {max_retries} attempts: {error_message}"

                except asyncio.TimeoutError:
                    logger.error(f"Request timed out (attempt {attempt+1}/{max_retries})")
                    self.status_tracker.num_other_errors += 1
                    if attempt < max_retries - 1:
                        await asyncio.sleep(retry_delay)
                        retry_delay = min(retry_delay * 2, 60)
                    else:
                        return {}, f"Request timed out after {max_retries} attempts"

                except Exception as e:
                    logger.error(f"Request error: {e}")
                    self.status_tracker.num_other_errors += 1
                    if attempt < max_retries - 1:
                        await asyncio.sleep(retry_delay)
                        retry_delay = min(retry_delay * 2, 60)
                    else:
                        return {}, f"Request failed after {max_retries} attempts: {str(e)}"

            return {}, f"Failed to get response after {max_retries} attempts"

class DomainAnalysisIntegration:
    """
    Integration class for adding domain analysis to the repository analysis flow.

    This class provides methods that can be called from the repository analysis flow
    to add domain analysis as an additional step.
    """

    @staticmethod
    async def domains_from_significant_functions(
        input_path: str,
        output_path: str,
        api_key: Optional[str] = None,
        model: str = "o3-mini",
        max_requests_per_minute: float = 5000,
        max_tokens_per_minute: float = 15000000,
        use_openrouter: bool = False,
        use_claude: bool = False,
        openrouter_base_url: str = "https://openrouter.ai/api/v1",
        claude_model: str = "claude-3-7-sonnet-20250219",
        max_tokens_per_chunk: int = 500000,
        disable_parallel: bool = False,
        max_concurrent_tasks: int = 0,
        generate_explanations: bool = True,
    ) -> bool:
        """
        Analyze codebase structure from a file and generate domain classification.

        Args:
            input_path: Path to the input file (complete_repomap_bracket.txt, YAML file, or JSON file)
            output_path: Path to save the domain classification output
            api_key: API key (OpenAI, Claude, or OpenRouter depending on which service is used)
            model: Model to use (OpenAI model ID if using OpenAI)
            max_requests_per_minute: Rate limit for API requests
            max_tokens_per_minute: Token rate limit for API
            use_openrouter: Whether to use OpenRouter instead of OpenAI
            use_claude: Whether to use Claude instead of OpenAI
            openrouter_base_url: Base URL for OpenRouter API
            claude_model: Claude model to use if use_claude is True
            max_tokens_per_chunk: Maximum tokens per chunk for parallel processing
            disable_parallel: Whether to disable parallel processing
            max_concurrent_tasks: Maximum number of concurrent tasks (0 = auto-detect based on CPU cores)
            generate_explanations: Whether to generate explanations for leaf domains

        Returns:
            True if analysis was successful, False otherwise
        """
        try:
            analyzer = DomainAnalyzer(
                api_key=api_key,
                model=model,
                max_requests_per_minute=max_requests_per_minute,
                max_tokens_per_minute=max_tokens_per_minute,
                use_openrouter=use_openrouter,
                use_claude=use_claude,
                openrouter_base_url=openrouter_base_url,
                claude_model=claude_model,
                generate_explanations=generate_explanations,
            )

            result = await analyzer.create_hierar_domain_struct(
                input_path,
                output_path,
                max_tokens_per_chunk=max_tokens_per_chunk,
                disable_parallel=disable_parallel,
                max_concurrent_tasks=max_concurrent_tasks
            )
            return result.success

        except Exception as e:
            logger.error(f"Error in domain analysis: {e}")
            return False

    @staticmethod
    async def domains_from_repomap(
        repomap_path: str,
        output_path: str,
        api_key: Optional[str] = None,
        model: str = "o3-mini",
        max_requests_per_minute: float = 5000,
        max_tokens_per_minute: float = 15000000,
        use_openrouter: bool = False,
        use_claude: bool = False,
        openrouter_base_url: str = "https://openrouter.ai/api/v1",
        claude_model: str = "claude-3-7-sonnet-20250219",
        max_tokens_per_chunk: int = 500000,
        disable_parallel: bool = False,
        max_concurrent_tasks: int = 0,
        generate_explanations: bool = True,
    ) -> bool:
        """
        Analyze a repomap file and generate domain classification.

        This is a convenience method that calls domains_from_significant_functions with
        appropriate parameters for repomap files.

        Args:
            repomap_path: Path to the repomap file (complete_repomap_bracket.txt, YAML, or JSON)
            output_path: Path to save the domain classification output
            api_key: API key (OpenAI, Claude, or OpenRouter depending on which service is used)
            model: Model to use (OpenAI model ID if using OpenAI)
            max_requests_per_minute: Rate limit for API requests
            max_tokens_per_minute: Token rate limit for API
            use_openrouter: Whether to use OpenRouter instead of OpenAI
            use_claude: Whether to use Claude instead of OpenAI
            openrouter_base_url: Base URL for OpenRouter API
            claude_model: Claude model to use if use_claude is True
            max_tokens_per_chunk: Maximum tokens per chunk for parallel processing
            disable_parallel: Whether to disable parallel processing
            max_concurrent_tasks: Maximum number of concurrent tasks (0 = auto-detect based on CPU cores)
            generate_explanations: Whether to generate explanations for leaf domains

        Returns:
            True if analysis was successful, False otherwise
        """
        return await DomainAnalysisIntegration.domains_from_significant_functions(
            input_path=repomap_path,
            output_path=output_path,
            api_key=api_key,
            model=model,
            max_requests_per_minute=max_requests_per_minute,
            max_tokens_per_minute=max_tokens_per_minute,
            use_openrouter=use_openrouter,
            use_claude=use_claude,
            openrouter_base_url=openrouter_base_url,
            claude_model=claude_model,
            max_tokens_per_chunk=max_tokens_per_chunk,
            disable_parallel=disable_parallel,
            max_concurrent_tasks=max_concurrent_tasks,
            generate_explanations=generate_explanations
        )


async def main():
    """Main entry point for the domain analyzer."""
    parser = argparse.ArgumentParser(description="Analyze codebase structure and classify it into domains")
    parser.add_argument("--input", required=True, help="Path to the input file (complete_repomap_bracket.txt, YAML file, or JSON file)")
    parser.add_argument("--output", required=True, help="Path to save the domain classification output")
    parser.add_argument("--api-key", help="API key (if not provided, will try to get from environment)")
    parser.add_argument("--model", default="gpt-4.1-2025-04-14", help="Model to use (OpenAI model ID if using OpenAI)") #gpt-4.1-2025-04-14 #o4-mini
    parser.add_argument("--requests-per-minute", type=float, default=15000, help="Rate limit for API requests")
    parser.add_argument("--tokens-per-minute", type=float, default=62500000, help="Token rate limit for API")
    parser.add_argument("--temperature", type=float, default=0.2, help="Temperature setting for the model")

    # Model selection group
    model_group = parser.add_mutually_exclusive_group()
    model_group.add_argument("--use-openrouter", default=False, action="store_true", help="Use OpenRouter instead of OpenAI")
    model_group.add_argument("--use-claude", default=False, action="store_true", help="Use Claude instead of OpenAI")

    # OpenRouter specific arguments
    parser.add_argument("--openrouter-base-url", default="https://openrouter.ai/api/v1", help="Base URL for OpenRouter API")

    # Claude specific arguments
    parser.add_argument("--claude-model", default="claude-3-7-sonnet-20250219", help="Claude model to use if --use-claude is specified")

    # Common arguments
    parser.add_argument("--max-tokens-per-chunk", type=int, default=80000, help="Maximum tokens per chunk for parallel processing")
    parser.add_argument("--disable-parallel", action="store_true", help="Disable parallel processing for large codebases")
    parser.add_argument("--max-concurrent-tasks", type=int, default=0, help="Maximum number of concurrent tasks (0 = auto-detect based on CPU cores)")
    parser.add_argument("--no-explanations", default=True, action="store_true", help="Disable generation of explanations for leaf domains")

    args = parser.parse_args()

    start_time = time.time()
    try:
        analyzer = DomainAnalyzer(
            api_key=args.api_key,
            model=args.model,
            max_requests_per_minute=args.requests_per_minute,
            max_tokens_per_minute=args.tokens_per_minute,
            temperature=args.temperature,
            use_openrouter=args.use_openrouter,
            use_claude=args.use_claude,
            openrouter_base_url=args.openrouter_base_url,
            claude_model=args.claude_model,
            generate_explanations=not args.no_explanations,
        )

        result = await analyzer.create_hierar_domain_struct(
            args.input,
            args.output,
            max_tokens_per_chunk=args.max_tokens_per_chunk,
            disable_parallel=args.disable_parallel,
            max_concurrent_tasks=args.max_concurrent_tasks
        )
        elapsed_time = time.time() - start_time
        print(f"Total time taken: {elapsed_time:.2f}")

        if result.success:
            logger.info("Domain analysis completed successfully")
            return 0
        else:
            logger.error(f"Domain analysis failed: {result.error_message}")
            return 1

    except Exception as e:
        logger.error(f"Error in domain analysis: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)


## Usage Examples

## Using OpenAI (default)
# python exp_domain_analysis.py --input ./complete_repomap_bracket.txt --output ./output/domain_analysis_bracket_openai.yaml --max-tokens-per-chunk 80000

## Using OpenRouter with Gemini
# python exp_domain_analysis.py --input ./complete_repomap_bracket.txt --output ./output/domain_analysis_bracket_openrouter.yaml --use-openrouter --max-tokens-per-chunk 100000

## Using Claude
# python exp_domain_analysis.py --input ./complete_repomap_bracket.txt --output ./output/domain_analysis_bracket_claude.yaml --use-claude --claude-model claude-3-7-sonnet-20250219 --max-tokens-per-chunk 80000

## Explanation generation is optional with the --no-explanations flag
# python exp_domain_analysis.py --input ./complete_repomap_bracket.txt --output ./output/domain_analysis_bracket_no_expln.yaml --max-tokens-per-chunk 80000 --no-explanations

## Other examples
# python exp_domain_analysis.py --input ./complete_repomap_gitlab.txt --output ./output/domain_analysis_gitlab_expln.yaml --max-tokens-per-chunk 80000

# python exp_domain_analysis.py --input /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/repomap_gitlab1_full_filtered.txt --output ./output/domain_analysis_gitlab_full_no_expln.yaml --max-tokens-per-chunk 80000

# python exp_domain_analysis.py --input /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/repomap_gitlab_filtered_filtered.txt --output ./output/domain_anal_fltrd_gitlab_expln.yaml --max-tokens-per-chunk 80000

# python exp_domain_analysis.py --input ./complete_repomap_django.txt --output ./output/domain_analysis_django_expl.yaml --max-tokens-per-chunk 80000

# python exp_domain_analysis.py --input ./complete_repomap_pytorch_full.txt --output ./output/domain_analysis_pytorch_full.yaml --max-tokens-per-chunk 80000
#$5.41

# python exp_domain_analysis.py --input /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/optim_output/repomap_pytorch_filtered.txt --output /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/optim_output/domain_analysis_pytorch_full_filtered_no_expl_improved.yaml --max-tokens-per-chunk 80000

# python exp_domain_analysis.py --input /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/optim_output/repomap_pytorch_filtered.txt --output /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/optim_output/domain_analysis_pytorch_full_filtered_no_expl_o3_big_model.yaml --max-tokens-per-chunk 80000

# python exp_domain_analysis.py --input /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/exp_results/repomap/gitlab_full_repomap_filtered.txt --output /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/exp_results/domain_analysis/gitlab_full_domain_analysis_gemini_250k.yaml --max-tokens-per-chunk 250000

# python exp_domain_analysis.py --input /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/exp_results/repomap/gitlab_full_repomap_filtered.txt --output /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/exp_results/domain_analysis/gitlab_full_domain_analysis_oai_4_1.yaml --max-tokens-per-chunk 80000

# python exp_domain_analysis.py --input /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/exp_results/repomap/pytorch_full_repomap_filtered.txt --output /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/exp_results/domain_analysis/pytorch/pytorch_full_domain_analysis_oai_4_1.yaml --max-tokens-per-chunk 80000

# python exp_domain_analysis.py --input /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/exp_results/repomap/bracket/bracket_repomap_filtered.json --output /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/exp_results/domain_analysis/bracket/bracket_4_1.yaml --max-tokens-per-chunk 80000

# python exp_domain_analysis.py --input /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/exp_results/repomap/django/django_repomap_filtered.json --output /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/exp_results/domain_analysis/django/django_4_1.yaml --max-tokens-per-chunk 80000

# python exp_domain_analysis.py --input /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/exp_results/repomap/pytorch/pytorch_repomap_filtered.json --output /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/exp_results/domain_analysis/pytorch/pytorch_4_1.yaml --max-tokens-per-chunk 50000

# python exp_domain_analysis.py --input /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/exp_results/repomap/gitlab/gitlab_filtered.json --output /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/exp_results/domain_analysis/gitlab/gitlab_domain_analysis.yaml --max-tokens-per-chunk 80000
#13.75
