# Experimental IRL Pipeline Configuration
# This is a template configuration file for the experimental IRL pipeline.
# You can customize this file to configure the pipeline according to your needs.

# General parameters
repo_dir: "/path/to/repository"  # Path to the repository to analyze
output_dir: "/path/to/output"    # Directory to save output artifacts
verbose: false                   # Enable verbose logging

# Repomap parameters
repomap:
  batch_size: 1000               # Number of files to process in each batch for repomap generation
  top_percentage: 0.3            # Percentage of top functions to keep per file (0.0-1.0)
  min_functions: 3               # Minimum number of functions to keep per file
  max_functions: 15              # Maximum number of functions to keep per file
  exclude_tests: true            # Whether to exclude test files
  output_format: "json"          # Output format for repomap ("text", "json", or "yaml")
  include_extensions:            # List of file extensions to include
    - ".py"                      # Python files
    - ".js"                      # JavaScript files
    - ".ts"                      # TypeScript files
    - ".java"                    # Java files
    - ".rb"                      # Ruby files

# Domain analysis parameters
domain_analysis:
  model: "gpt-4o-mini"           # Model to use for domain analysis
  use_openrouter: true           # Whether to use OpenRouter for domain analysis
  max_tokens_per_chunk: 500000   # Maximum tokens per chunk for domain analysis
  disable_parallel: false        # Whether to disable parallel processing for domain analysis
  max_concurrent_tasks: 0        # Maximum concurrent tasks for domain analysis (0 = auto-detect)
  generate_explanations: true    # Whether to generate explanations for leaf domains

# File domain mapper parameters
file_mapper:
  model: "gpt-4o-mini"           # Model to use for file-to-domain mapping
  use_openrouter: false          # Whether to use OpenRouter for file-to-domain mapping
  max_files_per_batch: 50        # Maximum number of files to include in a batch for mapping

# Diagram generator parameters
diagram_generator:
  model_type: "openai"           # Type of model to use for diagrams ("claude" or "openai")
  openai_model: "gpt-4o-mini"    # OpenAI model to use for diagram generation
  use_openrouter: true           # Whether to use OpenRouter for diagram generation
  openrouter_model: "google/gemini-2.5-pro-preview"  # OpenRouter model to use for diagram generation
  max_concurrent_tasks: 10       # Maximum concurrent tasks for diagram generation

# Rate limiting parameters
rate_limits:
  max_requests_per_minute: 1200  # Rate limit for API requests
  max_tokens_per_minute: 1000000 # Token rate limit for API
