#!/usr/bin/env python3
"""
Experimental Intermediate Representation Layer (IRL) Pipeline

This module implements a new version of the IRL pipeline that orchestrates the following steps:
1. Create the repomap with exp_complete_repomap.py
2. Feed the filtered output to exp_domain_analysis.py
3. Feed the repomap and domain analysis to exp_file_domain_mapper_batched.py
4. Take the output of step 3 and feed it to domain_file_repomap.py
5. Take the output of step 4 and generate mermaid diagrams using exp_domain_diagram_generator_json.py

This pipeline is designed to be more efficient and scalable than the original IRL pipeline,
with better parallelization and optimized token usage.

Usage:
    python exp_irl.py --repo-dir <path> --output-dir <path>
"""

import os
import time
import logging
import argparse
import asyncio
import json
import yaml
from typing import Optional, Dict, Any, List, Tuple, Union
from dataclasses import dataclass, field

# Import API key management
from bracket_core.llm.api_keys import get_openai_api_key, get_anthropic_api_key, get_openrouter_api_key

# Import the experimental components
from bracket_core.bracket_irl.bracket_complete_repomap import CompleteRepoMap
from bracket_core.bracket_irl.bracket_domain_analysis import DomainAnalysisIntegration
from bracket_core.bracket_irl.bracket_file_domain_mapper_batched import FileDomainMapper
from bracket_core.bracket_irl.bracket_domain_file_repomap import DomainFileRepomap
from bracket_core.bracket_irl.bracket_domain_diagram_generator import JSONDomainDiagramGenerator

# Create an alias for FileDomainMapper to match the expected interface
FileDomainMapperIntegration = FileDomainMapper

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class ExpIRLResult:
    """Result of the experimental IRL pipeline."""
    success: bool = False
    error_message: str = ""
    artifacts: Dict[str, str] = field(default_factory=dict)
    stats: Dict[str, Any] = field(default_factory=dict)

class ExpIRLPipeline:
    """
    Experimental IRL Pipeline that orchestrates the new approach to codebase analysis.

    This pipeline uses the following components:
    1. exp_complete_repomap.py - Generate a complete repomap
    2. exp_domain_analysis.py - Analyze the filtered repomap to identify domains
    3. exp_file_domain_mapper_batched.py - Map files to domains
    4. domain_file_repomap.py - Create a combined artifact
    5. exp_domain_diagram_generator_json.py - Generate mermaid diagrams
    """

    def __init__(
        self,
        repo_dir: str,
        output_dir: str,
        # General parameters
        verbose: bool = False,
        # Repomap parameters
        repomap_batch_size: int = 1000,
        repomap_top_percentage: float = 0.3,
        repomap_min_functions: int = 3,
        repomap_max_functions: int = 15,
        repomap_exclude_tests: bool = True,
        repomap_output_format: str = "json",
        repomap_include_extensions: Optional[List[str]] = None,
        # Domain analysis parameters
        domain_analysis_model: str = "gpt-4.1-2025-04-14",
        domain_analysis_use_openrouter: bool = False,
        domain_analysis_max_tokens_per_chunk: int = 500000,
        domain_analysis_disable_parallel: bool = False,
        domain_analysis_max_concurrent_tasks: int = 0,
        domain_analysis_generate_explanations: bool = False,
        # File domain mapper parameters
        file_mapper_model: str = "gpt-4.1-2025-04-14",
        file_mapper_use_openrouter: bool = False,
        file_mapper_max_files_per_batch: int = 50,
        # Diagram generator parameters
        diagram_model_type: str = "openai",
        diagram_openai_model: str = "gpt-4.1-2025-04-14",
        diagram_use_openrouter: bool = False,
        diagram_openrouter_model: str = "google/gemini-2.5-pro-preview",
        diagram_max_concurrent_tasks: int = 30,
        # Rate limiting parameters
        max_requests_per_minute: float = 5000,
        max_tokens_per_minute: float = 10000000,
    ):
        """
        Initialize the experimental IRL pipeline.

        Args:
            repo_dir: Path to the repository to analyze
            output_dir: Directory to save output artifacts
            verbose: Enable verbose logging
            repomap_batch_size: Number of files to process in each batch for repomap generation
            repomap_top_percentage: Percentage of top functions to keep per file (0.0-1.0)
            repomap_min_functions: Minimum number of functions to keep per file
            repomap_max_functions: Maximum number of functions to keep per file
            repomap_exclude_tests: Whether to exclude test files
            repomap_output_format: Output format for repomap ("text", "json", or "yaml")
            repomap_include_extensions: List of file extensions to include (e.g., [".py", ".js"])
            domain_analysis_model: Model to use for domain analysis
            domain_analysis_use_openrouter: Whether to use OpenRouter for domain analysis
            domain_analysis_max_tokens_per_chunk: Maximum tokens per chunk for domain analysis
            domain_analysis_disable_parallel: Whether to disable parallel processing for domain analysis
            domain_analysis_max_concurrent_tasks: Maximum concurrent tasks for domain analysis
            domain_analysis_generate_explanations: Whether to generate explanations for leaf domains
            file_mapper_model: Model to use for file-to-domain mapping
            file_mapper_use_openrouter: Whether to use OpenRouter for file-to-domain mapping
            file_mapper_max_files_per_batch: Maximum number of files to include in a batch for mapping
            diagram_model_type: Type of model to use for diagrams ("claude" or "openai")
            diagram_openai_model: OpenAI model to use for diagram generation
            diagram_use_openrouter: Whether to use OpenRouter for diagram generation
            diagram_openrouter_model: OpenRouter model to use for diagram generation
            diagram_max_concurrent_tasks: Maximum concurrent tasks for diagram generation
            max_requests_per_minute: Rate limit for API requests
            max_tokens_per_minute: Token rate limit for API
        """
        self.repo_dir = repo_dir
        self.output_dir = output_dir
        self.verbose = verbose

        # Repomap parameters
        self.repomap_batch_size = repomap_batch_size
        self.repomap_top_percentage = repomap_top_percentage
        self.repomap_min_functions = repomap_min_functions
        self.repomap_max_functions = repomap_max_functions
        self.repomap_exclude_tests = repomap_exclude_tests
        self.repomap_output_format = repomap_output_format
        self.repomap_include_extensions = repomap_include_extensions

        # Domain analysis parameters
        self.domain_analysis_model = domain_analysis_model
        self.domain_analysis_use_openrouter = domain_analysis_use_openrouter
        self.domain_analysis_max_tokens_per_chunk = domain_analysis_max_tokens_per_chunk
        self.domain_analysis_disable_parallel = domain_analysis_disable_parallel
        self.domain_analysis_max_concurrent_tasks = domain_analysis_max_concurrent_tasks
        self.domain_analysis_generate_explanations = domain_analysis_generate_explanations

        # File domain mapper parameters
        self.file_mapper_model = file_mapper_model
        self.file_mapper_use_openrouter = file_mapper_use_openrouter
        self.file_mapper_max_files_per_batch = file_mapper_max_files_per_batch

        # Diagram generator parameters
        self.diagram_model_type = diagram_model_type
        self.diagram_openai_model = diagram_openai_model
        self.diagram_use_openrouter = diagram_use_openrouter
        self.diagram_openrouter_model = diagram_openrouter_model
        self.diagram_max_concurrent_tasks = diagram_max_concurrent_tasks

        # Rate limiting parameters
        self.max_requests_per_minute = max_requests_per_minute
        self.max_tokens_per_minute = max_tokens_per_minute

        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)

        # Paths for artifacts
        self.repomap_dir = os.path.join(output_dir, "repomap")
        self.domain_analysis_dir = os.path.join(output_dir, "domain_analysis")
        self.domain_mapping_dir = os.path.join(output_dir, "domain_mapping")
        self.domain_file_repomap_dir = os.path.join(output_dir, "domain_file_repomap")
        self.diagrams_dir = os.path.join(output_dir, "diagrams")

        # Create directories
        os.makedirs(self.repomap_dir, exist_ok=True)
        os.makedirs(self.domain_analysis_dir, exist_ok=True)
        os.makedirs(self.domain_mapping_dir, exist_ok=True)
        os.makedirs(self.domain_file_repomap_dir, exist_ok=True)
        os.makedirs(self.diagrams_dir, exist_ok=True)

        # Artifact paths
        self.repomap_path = os.path.join(self.repomap_dir, f"repomap.{self.repomap_output_format}")
        self.filtered_repomap_path = os.path.join(self.repomap_dir, f"repomap_filtered.{self.repomap_output_format}")
        self.domain_analysis_path = os.path.join(self.domain_analysis_dir, "domain_analysis.yaml")
        self.domain_mapping_path = os.path.join(self.domain_mapping_dir, "domain_file_mapping.yaml")
        self.domain_file_repomap_path = os.path.join(self.domain_file_repomap_dir, "domain_file_repomap.json")
        self.domain_file_repomap_stats_path = os.path.join(self.domain_file_repomap_dir, "domain_file_repomap_stats.json")

    async def generate_repomap(self) -> Tuple[bool, str]:
        """
        Generate a complete repository map using exp_complete_repomap.py.

        Returns:
            Tuple of (success, error_message)
        """
        logger.info(f"Step 1: Generating repository map for {self.repo_dir}")

        try:
            # Create the repo map generator
            repo_map = CompleteRepoMap(
                root=self.repo_dir,
                verbose=self.verbose,
                top_percentage=self.repomap_top_percentage,
                min_functions=self.repomap_min_functions,
                max_functions=self.repomap_max_functions,
                exclude_tests=self.repomap_exclude_tests,
                output_format=self.repomap_output_format,
                include_extensions=self.repomap_include_extensions,
            )

            # Generate the map with parallel batch processing
            map_data = repo_map.generate_complete_map(
                self.repo_dir,
                batch_size=self.repomap_batch_size,
                save_interval=True,
                output_file=self.repomap_path,
            )

            # Save the final map
            output_file = repo_map.save_map(map_data, self.repomap_path, final_save=True)

            logger.info(f"Repository map generated and saved to {output_file}")
            logger.info(f"Map contains {map_data['token_count']} tokens")

            # Get the filtered map path
            base, ext = os.path.splitext(output_file)
            filtered_output_file = f"{base}_filtered{ext}"

            if "filtered_token_count" in map_data:
                logger.info(f"Filtered repository map saved to {filtered_output_file}")
                logger.info(f"Filtered map contains {map_data['filtered_token_count']} tokens")
                logger.info(f"Filtering kept approximately {map_data['filtered_token_count'] / map_data['token_count']:.1%} of the original content")

                # Update the filtered repomap path
                self.filtered_repomap_path = filtered_output_file

            return True, ""
        except Exception as e:
            error_message = f"Error generating repository map: {str(e)}"
            logger.error(error_message)
            import traceback
            logger.error(traceback.format_exc())
            return False, error_message

    async def analyze_domains(self) -> Tuple[bool, str]:
        """
        Analyze the filtered repository map to identify domains using exp_domain_analysis.py.

        Returns:
            Tuple of (success, error_message)
        """
        logger.info(f"Step 2: Analyzing domains from filtered repository map")

        try:
            # Get API key
            api_key = get_openai_api_key()

            # Perform domain analysis
            success = await DomainAnalysisIntegration.domains_from_repomap(
                repomap_path=self.filtered_repomap_path,
                output_path=self.domain_analysis_path,
                api_key=api_key,
                model=self.domain_analysis_model,
                max_requests_per_minute=self.max_requests_per_minute,
                max_tokens_per_minute=self.max_tokens_per_minute,
                use_openrouter=self.domain_analysis_use_openrouter,
                max_tokens_per_chunk=self.domain_analysis_max_tokens_per_chunk,
                disable_parallel=self.domain_analysis_disable_parallel,
                max_concurrent_tasks=self.domain_analysis_max_concurrent_tasks,
                generate_explanations=self.domain_analysis_generate_explanations,
            )

            if success:
                logger.info(f"Domain analysis completed successfully and saved to {self.domain_analysis_path}")
                return True, ""
            else:
                error_message = "Domain analysis failed"
                logger.error(error_message)
                return False, error_message
        except Exception as e:
            error_message = f"Error analyzing domains: {str(e)}"
            logger.error(error_message)
            import traceback
            logger.error(traceback.format_exc())
            return False, error_message

    async def map_files_to_domains(self) -> Tuple[bool, str]:
        """
        Map files to domains using exp_file_domain_mapper_batched.py.

        Returns:
            Tuple of (success, error_message)
        """
        logger.info(f"Step 3: Mapping files to domains")

        try:
            # Get API key
            api_key = get_openai_api_key()

            # Map files to domains
            success = await FileDomainMapperIntegration.map_files_to_leaf_domains(
                repomap_path=self.filtered_repomap_path,
                domain_analysis_yaml_path=self.domain_analysis_path,
                output_path=self.domain_mapping_path,
                api_key=api_key,
                model=self.file_mapper_model,
                max_requests_per_minute=self.max_requests_per_minute,
                max_tokens_per_minute=self.max_tokens_per_minute,
                use_openrouter=self.file_mapper_use_openrouter,
                max_files_per_batch=self.file_mapper_max_files_per_batch,
            )

            if success:
                logger.info(f"File-to-domain mapping completed successfully and saved to {self.domain_mapping_path}")
                return True, ""
            else:
                error_message = "File-to-domain mapping failed"
                logger.error(error_message)
                return False, error_message
        except Exception as e:
            error_message = f"Error mapping files to domains: {str(e)}"
            logger.error(error_message)
            import traceback
            logger.error(traceback.format_exc())
            return False, error_message


    def generate_domain_file_repomap(self) -> Tuple[bool, str]:
        """
        Generate a domain-file repomap using domain_file_repomap.py.

        Returns:
            Tuple of (success, error_message)
        """
        logger.info(f"Step 4: Generating domain-file repomap")

        try:
            # Create the domain-file repomap generator
            generator = DomainFileRepomap(
                domain_file_mapped_yaml_path=self.domain_mapping_path,
                repomap_path=self.filtered_repomap_path,
                output_path=self.domain_file_repomap_path
            )

            # Generate and save the domain-file repomap
            output_path = generator.generate_and_save()

            logger.info(f"Domain-file repomap generated and saved to {output_path}")
            logger.info(f"Domain statistics saved to {self.domain_file_repomap_stats_path}")

            return True, ""
        except Exception as e:
            error_message = f"Error generating domain-file repomap: {str(e)}"
            logger.error(error_message)
            import traceback
            logger.error(traceback.format_exc())
            return False, error_message

    async def generate_diagrams(self) -> Tuple[bool, str]:
        """
        Generate mermaid diagrams using exp_domain_diagram_generator_json.py.

        Returns:
            Tuple of (success, error_message)
        """
        logger.info(f"Step 5: Generating mermaid diagrams")

        try:
            # Get API keys
            openai_api_key = get_openai_api_key()
            openrouter_api_key = get_openrouter_api_key()

            # Create a JSON domain diagram generator
            generator = JSONDomainDiagramGenerator(
                domain_file_json_path=self.domain_file_repomap_path,
                output_dir=self.diagrams_dir,
                model_type=self.diagram_model_type,
                # OpenAI parameters
                openai_api_key=openai_api_key,
                openai_model=self.diagram_openai_model,
                # OpenRouter parameters
                use_openrouter=self.diagram_use_openrouter,
                openrouter_api_key=openrouter_api_key,
                openrouter_model=self.diagram_openrouter_model,
                # Common parameters
                max_requests_per_minute=self.max_requests_per_minute,
                max_tokens_per_minute=self.max_tokens_per_minute,
                max_concurrent_tasks=self.diagram_max_concurrent_tasks,
                # Caching parameters
                cache_dir=os.path.join(self.diagrams_dir, "cache"),
                use_cache=True,
            )

            # Generate all diagrams
            result = await generator.generate_all_diagrams()

            if result.success:
                logger.info(f"Diagram generation complete. Generated {len(result.diagram_files)} diagrams using {result.model_used}")

                # Print OpenRouter usage if available
                if hasattr(result, 'openrouter_info') and result.openrouter_info:
                    logger.info(f"OpenRouter usage:")
                    logger.info(f"  Domains using OpenRouter: {result.openrouter_info.get('openrouter_domains_count', 0)}")
                    logger.info(f"  Total OpenRouter calls: {result.openrouter_info.get('openrouter_calls_total', 0)}")
                    logger.info(f"  Completed OpenRouter calls: {result.openrouter_info.get('openrouter_calls_completed', 0)}")

                return True, ""
            else:
                error_message = f"Diagram generation failed: {result.error_message}"
                logger.error(error_message)
                return False, error_message
        except Exception as e:
            error_message = f"Error generating diagrams: {str(e)}"
            logger.error(error_message)
            import traceback
            logger.error(traceback.format_exc())
            return False, error_message

    async def run(self) -> ExpIRLResult:
        """
        Run the complete experimental IRL pipeline.

        Returns:
            ExpIRLResult containing the results of the pipeline
        """
        logger.info(f"Starting experimental IRL pipeline for: {self.repo_dir}")
        start_time = time.time()

        # Step 1: Generate repository map
        repomap_success, repomap_error = await self.generate_repomap()
        if not repomap_success:
            return ExpIRLResult(
                success=False,
                error_message=f"Repository map generation failed: {repomap_error}",
                artifacts={},
                stats={"elapsed_time": time.time() - start_time}
            )

        # Step 2: Analyze domains
        domain_success, domain_error = await self.analyze_domains()
        if not domain_success:
            return ExpIRLResult(
                success=False,
                error_message=f"Domain analysis failed: {domain_error}",
                artifacts={"repomap": self.repomap_path, "filtered_repomap": self.filtered_repomap_path},
                stats={"elapsed_time": time.time() - start_time}
            )

        # Step 3: Map files to domains
        mapping_success, mapping_error = await self.map_files_to_domains()
        if not mapping_success:
            return ExpIRLResult(
                success=False,
                error_message=f"File-to-domain mapping failed: {mapping_error}",
                artifacts={
                    "repomap": self.repomap_path,
                    "filtered_repomap": self.filtered_repomap_path,
                    "domain_analysis": self.domain_analysis_path
                },
                stats={"elapsed_time": time.time() - start_time}
            )

        # Step 4: Generate domain-file repomap
        repomap_success, repomap_error = self.generate_domain_file_repomap()
        if not repomap_success:
            return ExpIRLResult(
                success=False,
                error_message=f"Domain-file repomap generation failed: {repomap_error}",
                artifacts={
                    "repomap": self.repomap_path,
                    "filtered_repomap": self.filtered_repomap_path,
                    "domain_analysis": self.domain_analysis_path,
                    "domain_mapping": self.domain_mapping_path
                },
                stats={"elapsed_time": time.time() - start_time}
            )

        # Step 5: Generate diagrams
        diagram_success, diagram_error = await self.generate_diagrams()
        if not diagram_success:
            return ExpIRLResult(
                success=False,
                error_message=f"Diagram generation failed: {diagram_error}",
                artifacts={
                    "repomap": self.repomap_path,
                    "filtered_repomap": self.filtered_repomap_path,
                    "domain_analysis": self.domain_analysis_path,
                    "domain_mapping": self.domain_mapping_path,
                    "domain_file_repomap": self.domain_file_repomap_path,
                    "domain_file_repomap_stats": self.domain_file_repomap_stats_path
                },
                stats={"elapsed_time": time.time() - start_time}
            )

        # All steps completed successfully
        end_time = time.time()
        elapsed_time = end_time - start_time

        logger.info(f"Experimental IRL pipeline completed successfully in {elapsed_time:.2f} seconds")

        # Read domain statistics
        domain_stats = {}
        try:
            with open(self.domain_file_repomap_stats_path, 'r') as f:
                domain_stats = json.load(f)
        except Exception as e:
            logger.warning(f"Could not read domain statistics: {e}")

        return ExpIRLResult(
            success=True,
            error_message="",
            artifacts={
                "repomap": self.repomap_path,
                "filtered_repomap": self.filtered_repomap_path,
                "domain_analysis": self.domain_analysis_path,
                "domain_mapping": self.domain_mapping_path,
                "domain_file_repomap": self.domain_file_repomap_path,
                "domain_file_repomap_stats": self.domain_file_repomap_stats_path,
                "diagrams_dir": self.diagrams_dir
            },
            stats={
                "elapsed_time": elapsed_time,
                "domain_stats": domain_stats
            }
        )


def load_config_file(config_path: str) -> Dict[str, Any]:
    """
    Load configuration from a YAML or JSON file.

    Args:
        config_path: Path to the configuration file

    Returns:
        Dictionary containing configuration parameters
    """
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"Configuration file not found: {config_path}")

    # Determine file type based on extension
    _, ext = os.path.splitext(config_path)

    try:
        with open(config_path, 'r') as f:
            if ext.lower() in ['.yaml', '.yml']:
                config = yaml.safe_load(f)
            elif ext.lower() == '.json':
                config = json.load(f)
            else:
                raise ValueError(f"Unsupported configuration file format: {ext}")

        logger.info(f"Loaded configuration from {config_path}")
        return config
    except Exception as e:
        raise ValueError(f"Error loading configuration file: {str(e)}")

def create_config_template(output_path: str, format: str = 'yaml') -> None:
    """
    Create a template configuration file with default values.

    Args:
        output_path: Path to save the template configuration file
        format: Format of the configuration file ('yaml' or 'json')
    """
    # Default configuration
    config = {
        # General parameters
        "repo_dir": "/path/to/repository",
        "output_dir": "/path/to/output",
        "verbose": False,

        # Repomap parameters
        "repomap": {
            "batch_size": 1000,
            "top_percentage": 0.3,
            "min_functions": 3,
            "max_functions": 15,
            "exclude_tests": True,
            "output_format": "json",
            "include_extensions": [".py", ".js", ".ts", ".java", ".rb"]
        },

        # Domain analysis parameters
        "domain_analysis": {
            "model": "gpt-4.1-2025-04-14",
            "use_openrouter": True,
            "max_tokens_per_chunk": 500000,
            "disable_parallel": False,
            "max_concurrent_tasks": 0,
            "generate_explanations": True
        },

        # File domain mapper parameters
        "file_mapper": {
            "model": "gpt-4.1-2025-04-14",
            "use_openrouter": False,
            "max_files_per_batch": 50
        },

        # Diagram generator parameters
        "diagram_generator": {
            "model_type": "openai",
            "openai_model": "gpt-4.1-2025-04-14",
            "use_openrouter": True,
            "openrouter_model": "google/gemini-2.5-pro-preview",
            "max_concurrent_tasks": 10
        },

        # Rate limiting parameters
        "rate_limits": {
            "max_requests_per_minute": 1200,
            "max_tokens_per_minute": 1000000
        }
    }

    try:
        with open(output_path, 'w') as f:
            if format.lower() == 'yaml':
                yaml.dump(config, f, default_flow_style=False, sort_keys=False)
            elif format.lower() == 'json':
                json.dump(config, f, indent=2)
            else:
                raise ValueError(f"Unsupported format: {format}")

        logger.info(f"Created configuration template at {output_path}")
    except Exception as e:
        logger.error(f"Error creating configuration template: {str(e)}")

async def main():
    """Main entry point for the experimental IRL pipeline."""
    parser = argparse.ArgumentParser(description="Run the experimental IRL pipeline")

    # Configuration file options
    config_group = parser.add_argument_group('Configuration')
    config_group.add_argument("--config", help="Path to configuration file (YAML or JSON)")
    config_group.add_argument("--create-config-template", help="Create a template configuration file at the specified path")
    config_group.add_argument("--config-format", choices=["yaml", "json"], default="yaml", help="Format for the configuration template")

    # Required parameters (can be overridden by config file)
    parser.add_argument("--repo-dir", help="Path to the repository directory")
    parser.add_argument("--output-dir", help="Directory to save output artifacts")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose output")

    # Repomap parameters
    repomap_group = parser.add_argument_group('Repomap')
    repomap_group.add_argument("--repomap-batch-size", type=int, help="Number of files to process in each batch for repomap generation")
    repomap_group.add_argument("--repomap-top-percentage", type=float, help="Percentage of top functions to keep per file (0.0-1.0)")
    repomap_group.add_argument("--repomap-min-functions", type=int, help="Minimum number of functions to keep per file")
    repomap_group.add_argument("--repomap-max-functions", type=int, help="Maximum number of functions to keep per file")
    repomap_group.add_argument("--repomap-exclude-tests", action="store_true", help="Whether to exclude test files")
    repomap_group.add_argument("--repomap-output-format", choices=["text", "json", "yaml"], help="Output format for repomap")
    repomap_group.add_argument("--repomap-include-extensions", nargs="+", help="File extensions to include (e.g., .py .rb .js)")

    # Domain analysis parameters
    domain_group = parser.add_argument_group('Domain Analysis')
    domain_group.add_argument("--domain-analysis-model", help="Model to use for domain analysis")
    domain_group.add_argument("--domain-analysis-use-openrouter", action="store_true", help="Whether to use OpenRouter for domain analysis")
    domain_group.add_argument("--domain-analysis-max-tokens-per-chunk", type=int, help="Maximum tokens per chunk for domain analysis")
    domain_group.add_argument("--domain-analysis-disable-parallel", action="store_true", help="Whether to disable parallel processing for domain analysis")
    domain_group.add_argument("--domain-analysis-max-concurrent-tasks", type=int, help="Maximum concurrent tasks for domain analysis (0 = auto-detect)")
    domain_group.add_argument("--domain-analysis-no-explanations", action="store_true", help="Disable generating explanations for leaf domains")

    # File domain mapper parameters
    mapper_group = parser.add_argument_group('File Domain Mapper')
    mapper_group.add_argument("--file-mapper-model", help="Model to use for file-to-domain mapping")
    mapper_group.add_argument("--file-mapper-use-openrouter", action="store_true", help="Whether to use OpenRouter for file-to-domain mapping")
    mapper_group.add_argument("--file-mapper-max-files-per-batch", type=int, help="Maximum number of files to include in a batch for mapping")

    # Diagram generator parameters
    diagram_group = parser.add_argument_group('Diagram Generator')
    diagram_group.add_argument("--diagram-model-type", choices=["openai", "claude"], help="Type of model to use for diagrams")
    diagram_group.add_argument("--diagram-openai-model", help="OpenAI model to use for diagram generation")
    diagram_group.add_argument("--diagram-use-openrouter", action="store_true", help="Whether to use OpenRouter for diagram generation")
    diagram_group.add_argument("--diagram-openrouter-model", help="OpenRouter model to use for diagram generation")
    diagram_group.add_argument("--diagram-max-concurrent-tasks", type=int, help="Maximum concurrent tasks for diagram generation")

    # Rate limiting parameters
    rate_group = parser.add_argument_group('Rate Limiting')
    rate_group.add_argument("--max-requests-per-minute", type=float, help="Rate limit for API requests")
    rate_group.add_argument("--max-tokens-per-minute", type=float, help="Token rate limit for API")

    args = parser.parse_args()

    # Check if we need to create a configuration template
    if args.create_config_template:
        create_config_template(args.create_config_template, args.config_format)
        return 0

    # Initialize configuration with default values
    config = {
        # General parameters
        "repo_dir": None,
        "output_dir": None,
        "verbose": False,

        # Repomap parameters
        "repomap": {
            "batch_size": 1000,
            "top_percentage": 0.3,
            "min_functions": 3,
            "max_functions": 15,
            "exclude_tests": False,
            "output_format": "json",
            "include_extensions": [".py", ".js", ".ts", ".java", ".rb"]
        },

        # Domain analysis parameters
        "domain_analysis": {
            "model": "gpt-4.1-2025-04-14",
            "use_openrouter": False,
            "max_tokens_per_chunk": 500000,
            "disable_parallel": False,
            "max_concurrent_tasks": 0,
            "generate_explanations": True
        },

        # File domain mapper parameters
        "file_mapper": {
            "model": "gpt-4.1-2025-04-14",
            "use_openrouter": False,
            "max_files_per_batch": 50
        },

        # Diagram generator parameters
        "diagram_generator": {
            "model_type": "openai",
            "openai_model": "gpt-4.1-2025-04-14",
            "use_openrouter": False,
            "openrouter_model": "google/gemini-2.5-pro-preview",
            "max_concurrent_tasks": 10
        },

        # Rate limiting parameters
        "rate_limits": {
            "max_requests_per_minute": 1200,
            "max_tokens_per_minute": 1000000
        }
    }

    # Load configuration from file if provided
    if args.config:
        try:
            file_config = load_config_file(args.config)

            # Update top-level parameters
            for key in ["repo_dir", "output_dir", "verbose"]:
                if key in file_config:
                    config[key] = file_config[key]

            # Update nested parameters
            for section in ["repomap", "domain_analysis", "file_mapper", "diagram_generator", "rate_limits"]:
                if section in file_config and isinstance(file_config[section], dict):
                    config[section].update(file_config[section])

        except Exception as e:
            logger.error(f"Error loading configuration file: {str(e)}")
            return 1

    # Override configuration with command-line arguments
    if args.repo_dir:
        config["repo_dir"] = args.repo_dir
    if args.output_dir:
        config["output_dir"] = args.output_dir
    if args.verbose:
        config["verbose"] = args.verbose

    # Repomap parameters
    if args.repomap_batch_size:
        config["repomap"]["batch_size"] = args.repomap_batch_size
    if args.repomap_top_percentage is not None:
        config["repomap"]["top_percentage"] = args.repomap_top_percentage
    if args.repomap_min_functions:
        config["repomap"]["min_functions"] = args.repomap_min_functions
    if args.repomap_max_functions:
        config["repomap"]["max_functions"] = args.repomap_max_functions
    if args.repomap_exclude_tests:
        config["repomap"]["exclude_tests"] = args.repomap_exclude_tests
    if args.repomap_output_format:
        config["repomap"]["output_format"] = args.repomap_output_format
    if args.repomap_include_extensions:
        config["repomap"]["include_extensions"] = args.repomap_include_extensions

    # Domain analysis parameters
    if args.domain_analysis_model:
        config["domain_analysis"]["model"] = args.domain_analysis_model
    if args.domain_analysis_use_openrouter:
        config["domain_analysis"]["use_openrouter"] = args.domain_analysis_use_openrouter
    if args.domain_analysis_max_tokens_per_chunk:
        config["domain_analysis"]["max_tokens_per_chunk"] = args.domain_analysis_max_tokens_per_chunk
    if args.domain_analysis_disable_parallel:
        config["domain_analysis"]["disable_parallel"] = args.domain_analysis_disable_parallel
    if args.domain_analysis_max_concurrent_tasks:
        config["domain_analysis"]["max_concurrent_tasks"] = args.domain_analysis_max_concurrent_tasks
    if args.domain_analysis_no_explanations:
        config["domain_analysis"]["generate_explanations"] = not args.domain_analysis_no_explanations

    # File domain mapper parameters
    if args.file_mapper_model:
        config["file_mapper"]["model"] = args.file_mapper_model
    if args.file_mapper_use_openrouter:
        config["file_mapper"]["use_openrouter"] = args.file_mapper_use_openrouter
    if args.file_mapper_max_files_per_batch:
        config["file_mapper"]["max_files_per_batch"] = args.file_mapper_max_files_per_batch

    # Diagram generator parameters
    if args.diagram_model_type:
        config["diagram_generator"]["model_type"] = args.diagram_model_type
    if args.diagram_openai_model:
        config["diagram_generator"]["openai_model"] = args.diagram_openai_model
    if args.diagram_use_openrouter:
        config["diagram_generator"]["use_openrouter"] = args.diagram_use_openrouter
    if args.diagram_openrouter_model:
        config["diagram_generator"]["openrouter_model"] = args.diagram_openrouter_model
    if args.diagram_max_concurrent_tasks:
        config["diagram_generator"]["max_concurrent_tasks"] = args.diagram_max_concurrent_tasks

    # Rate limiting parameters
    if args.max_requests_per_minute:
        config["rate_limits"]["max_requests_per_minute"] = args.max_requests_per_minute
    if args.max_tokens_per_minute:
        config["rate_limits"]["max_tokens_per_minute"] = args.max_tokens_per_minute

    # Validate required parameters
    if not config["repo_dir"]:
        logger.error("Repository directory is required. Specify with --repo-dir or in the configuration file.")
        return 1
    if not config["output_dir"]:
        logger.error("Output directory is required. Specify with --output-dir or in the configuration file.")
        return 1

    # Log the configuration
    logger.info("Using the following configuration:")
    logger.info(f"  Repository directory: {config['repo_dir']}")
    logger.info(f"  Output directory: {config['output_dir']}")
    logger.info(f"  Verbose: {config['verbose']}")
    logger.info(f"  Repomap: {config['repomap']}")
    logger.info(f"  Domain analysis: {config['domain_analysis']}")
    logger.info(f"  File mapper: {config['file_mapper']}")
    logger.info(f"  Diagram generator: {config['diagram_generator']}")
    logger.info(f"  Rate limits: {config['rate_limits']}")

    # Create and run the pipeline
    pipeline = ExpIRLPipeline(
        repo_dir=config['repo_dir'],
        output_dir=config['output_dir'],
        verbose=config['verbose'],
        # Repomap parameters
        repomap_batch_size=config['repomap']['batch_size'],
        repomap_top_percentage=config['repomap']['top_percentage'],
        repomap_min_functions=config['repomap']['min_functions'],
        repomap_max_functions=config['repomap']['max_functions'],
        repomap_exclude_tests=config['repomap']['exclude_tests'],
        repomap_output_format=config['repomap']['output_format'],
        repomap_include_extensions=config['repomap']['include_extensions'],
        # Domain analysis parameters
        domain_analysis_model=config['domain_analysis']['model'],
        domain_analysis_use_openrouter=config['domain_analysis']['use_openrouter'],
        domain_analysis_max_tokens_per_chunk=config['domain_analysis']['max_tokens_per_chunk'],
        domain_analysis_disable_parallel=config['domain_analysis']['disable_parallel'],
        domain_analysis_max_concurrent_tasks=config['domain_analysis']['max_concurrent_tasks'],
        domain_analysis_generate_explanations=config['domain_analysis']['generate_explanations'],
        # File domain mapper parameters
        file_mapper_model=config['file_mapper']['model'],
        file_mapper_use_openrouter=config['file_mapper']['use_openrouter'],
        file_mapper_max_files_per_batch=config['file_mapper']['max_files_per_batch'],
        # Diagram generator parameters
        diagram_model_type=config['diagram_generator']['model_type'],
        diagram_openai_model=config['diagram_generator']['openai_model'],
        diagram_use_openrouter=config['diagram_generator']['use_openrouter'],
        diagram_openrouter_model=config['diagram_generator']['openrouter_model'],
        diagram_max_concurrent_tasks=config['diagram_generator']['max_concurrent_tasks'],
        # Rate limiting parameters
        max_requests_per_minute=config['rate_limits']['max_requests_per_minute'],
        max_tokens_per_minute=config['rate_limits']['max_tokens_per_minute'],
    )

    result = await pipeline.run()

    if result.success:
        logger.info("Experimental IRL pipeline completed successfully")
        logger.info(f"Artifacts:")
        for name, path in result.artifacts.items():
            logger.info(f"  {name}: {path}")

        if "domain_stats" in result.stats:
            logger.info(f"Domain statistics:")
            domain_stats = result.stats["domain_stats"]

            # Print global statistics if available
            if "__global__" in domain_stats:
                global_stats = domain_stats["__global__"]
                logger.info(f"  Global statistics:")
                for key, value in global_stats.items():
                    logger.info(f"    {key}: {value}")

            # Print top 15 domains by file count
            domains_by_files = sorted(
                [(domain, stats["num_files"]) for domain, stats in domain_stats.items() if domain != "__global__"],
                key=lambda x: x[1],
                reverse=True
            )[:15]

            if domains_by_files:
                logger.info(f"  Top domains by file count:")
                for domain, count in domains_by_files:
                    logger.info(f"    {domain}: {count} files")

        logger.info(f"Total time: {result.stats.get('elapsed_time', 0):.2f} seconds")
        return 0
    else:
        logger.error(f"Experimental IRL pipeline failed: {result.error_message}")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))

#python exp_irl.py --config /Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/bracket_config.json