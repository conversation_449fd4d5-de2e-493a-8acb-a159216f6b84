{"repo_dir": "/path/to/repository", "output_dir": "/path/to/output", "verbose": false, "repomap": {"batch_size": 1000, "top_percentage": 0.3, "min_functions": 3, "max_functions": 15, "exclude_tests": true, "output_format": "json", "include_extensions": [".py", ".js", ".ts", ".java", ".rb"]}, "domain_analysis": {"model": "gpt-4o-mini", "use_openrouter": true, "max_tokens_per_chunk": 500000, "disable_parallel": false, "max_concurrent_tasks": 0, "generate_explanations": true}, "file_mapper": {"model": "gpt-4o-mini", "use_openrouter": false, "max_files_per_batch": 50}, "diagram_generator": {"model_type": "openai", "openai_model": "gpt-4o-mini", "use_openrouter": true, "openrouter_model": "google/gemini-2.5-pro-preview", "max_concurrent_tasks": 10}, "rate_limits": {"max_requests_per_minute": 1200, "max_tokens_per_minute": 1000000}}