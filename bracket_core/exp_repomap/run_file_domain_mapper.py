#!/usr/bin/env python3
"""
Run the file domain mapper standalone.

This script provides a convenient way to run the file domain mapper
without having to go through the full repository analysis flow.

Example usage:
    python run_file_domain_mapper.py --repomap ./complete_repomap_bracket.txt \
                                     --domain-yaml ./output/domain_analysis_bracket.yaml \
                                     --output ./output/file_domain_mappings.yaml
"""

import os
import sys
import asyncio
import argparse
import logging
from typing import Optional

# Add the parent directory to the path to allow importing bracket_core modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

from bracket_core.exp_repomap.file_domain_mapper import FileDomainMapper

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def main():
    """Main entry point for the file domain mapper."""
    parser = argparse.ArgumentParser(description="Map files to leaf domains")
    parser.add_argument("--repomap", required=True, help="Path to the repository map file")
    parser.add_argument("--domain-yaml", required=True, help="Path to the domain analysis YAML file")
    parser.add_argument("--output", required=True, help="Path to save the file-to-domain mappings YAML")
    parser.add_argument("--api-key", help="OpenAI API key (if not provided, will try to get from environment)")
    parser.add_argument("--model", default="gpt-4o-mini", help="Model to use (OpenAI or OpenRouter model ID)")
    parser.add_argument("--requests-per-minute", type=float, default=50, help="Rate limit for API requests")
    parser.add_argument("--tokens-per-minute", type=float, default=100000, help="Token rate limit for API")
    parser.add_argument("--temperature", type=float, default=0.0, help="Temperature for the model")
    parser.add_argument("--use-openrouter", action="store_true", help="Use OpenRouter instead of OpenAI")
    parser.add_argument("--openrouter-base-url", default="https://openrouter.ai/api/v1", help="Base URL for OpenRouter API")

    args = parser.parse_args()

    try:
        success = await FileDomainMapper.map_files_to_leaf_domains(
            repomap_path=args.repomap,
            domain_analysis_yaml_path=args.domain_yaml,
            output_path=args.output,
            api_key=args.api_key,
            model=args.model,
            max_requests_per_minute=args.requests_per_minute,
            max_tokens_per_minute=args.tokens_per_minute,
            use_openrouter=args.use_openrouter,
            openrouter_base_url=args.openrouter_base_url,
        )

        if success:
            logger.info("File-to-domain mapping completed successfully")
            return 0
        else:
            logger.error("File-to-domain mapping failed")
            return 1

    except Exception as e:
        logger.error(f"Error in file-to-domain mapping: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
