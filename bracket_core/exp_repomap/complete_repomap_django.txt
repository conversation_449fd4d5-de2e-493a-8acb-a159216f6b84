
│__init__.py:
⋮...
│def setup()
⋮...

│apps/__init__.py:
⋮...

│apps/config.py:
⋮...
│class AppConfig
│    def __init__()
│    def __repr__()
│    def default_auto_field()
│    def _is_default_auto_field_overridden()
│    def _path_from_module()
│    def create()
│    def get_model()
│    def get_models()
│    def import_models()
│    def ready()
⋮...

│apps/registry.py:
⋮...
│class Apps
│    def __init__()
│    def populate()
│    def check_apps_ready()
│    def check_models_ready()
│    def get_app_configs()
│    def get_app_config()
│    def get_models()
│    def get_model()
│    def register_model()
│    def is_installed()
│    def get_containing_app_config()
│    def get_registered_model()
│    def get_swappable_settings_name()
│    def set_available_apps()
│    def unset_available_apps()
│    def set_installed_apps()
│    def unset_installed_apps()
│    def clear_cache()
│    def lazy_model_operation()
│    def apply_next_model()
│    def do_pending_operations()
⋮...

│conf/__init__.py:
⋮...
│class SettingsReference
│    def __new__()
│    def __init__()
│class LazySettings
│    def _setup()
│    def __repr__()
│    def __getattr__()
│    def __setattr__()
│    def __delattr__()
│    def configure()
│    def _add_script_prefix()
│    def configured()
│    def _show_deprecation_warning()
│class Settings
│    def __init__()
│    def is_overridden()
│    def __repr__()
│class UserSettingsHolder
│    def __init__()
│    def __getattr__()
│    def __setattr__()
│    def __delattr__()
│    def __dir__()
│    def is_overridden()
│    def __repr__()
⋮...

│conf/global_settings.py:
⋮...
│def gettext_noop()
⋮...

│conf/locale/__init__.py:
⋮...

│conf/locale/ar/formats.py:
⋮...

│conf/locale/ar_DZ/formats.py:
⋮...

│conf/locale/az/formats.py:
⋮...

│conf/locale/bg/formats.py:
⋮...

│conf/locale/bn/formats.py:
⋮...

│conf/locale/bs/formats.py:
⋮...

│conf/locale/ca/formats.py:
⋮...

│conf/locale/ckb/formats.py:
⋮...

│conf/locale/cs/formats.py:
⋮...

│conf/locale/cy/formats.py:
⋮...

│conf/locale/da/formats.py:
⋮...

│conf/locale/de/formats.py:
⋮...

│conf/locale/de_CH/formats.py:
⋮...

│conf/locale/el/formats.py:
⋮...

│conf/locale/en/formats.py:
⋮...

│conf/locale/en_AU/formats.py:
⋮...

│conf/locale/en_CA/formats.py:
⋮...

│conf/locale/en_GB/formats.py:
⋮...

│conf/locale/en_IE/formats.py:
⋮...

│conf/locale/eo/formats.py:
⋮...

│conf/locale/es/formats.py:
⋮...

│conf/locale/es_AR/formats.py:
⋮...

│conf/locale/es_CO/formats.py:
⋮...

│conf/locale/es_MX/formats.py:
⋮...

│conf/locale/es_NI/formats.py:
⋮...

│conf/locale/es_PR/formats.py:
⋮...

│conf/locale/et/formats.py:
⋮...

│conf/locale/eu/formats.py:
⋮...

│conf/locale/fa/formats.py:
⋮...

│conf/locale/fi/formats.py:
⋮...

│conf/locale/fr/formats.py:
⋮...

│conf/locale/fr_BE/formats.py:
⋮...

│conf/locale/fr_CA/formats.py:
⋮...

│conf/locale/fr_CH/formats.py:
⋮...

│conf/locale/ga/formats.py:
⋮...

│conf/locale/gd/formats.py:
⋮...

│conf/locale/gl/formats.py:
⋮...

│conf/locale/he/formats.py:
⋮...

│conf/locale/hi/formats.py:
⋮...

│conf/locale/hr/formats.py:
⋮...

│conf/locale/hu/formats.py:
⋮...

│conf/locale/id/formats.py:
⋮...

│conf/locale/ig/formats.py:
⋮...

│conf/locale/is/formats.py:
⋮...

│conf/locale/it/formats.py:
⋮...

│conf/locale/ja/formats.py:
⋮...

│conf/locale/ka/formats.py:
⋮...

│conf/locale/km/formats.py:
⋮...

│conf/locale/kn/formats.py:
⋮...

│conf/locale/ko/formats.py:
⋮...

│conf/locale/ky/formats.py:
⋮...

│conf/locale/lt/formats.py:
⋮...

│conf/locale/lv/formats.py:
⋮...

│conf/locale/mk/formats.py:
⋮...

│conf/locale/ml/formats.py:
⋮...

│conf/locale/mn/formats.py:
⋮...

│conf/locale/ms/formats.py:
⋮...

│conf/locale/nb/formats.py:
⋮...

│conf/locale/nl/formats.py:
⋮...

│conf/locale/nn/formats.py:
⋮...

│conf/locale/pl/formats.py:
⋮...

│conf/locale/pt/formats.py:
⋮...

│conf/locale/pt_BR/formats.py:
⋮...

│conf/locale/ro/formats.py:
⋮...

│conf/locale/ru/formats.py:
⋮...

│conf/locale/sk/formats.py:
⋮...

│conf/locale/sl/formats.py:
⋮...

│conf/locale/sq/formats.py:
⋮...

│conf/locale/sr/formats.py:
⋮...

│conf/locale/sr_Latn/formats.py:
⋮...

│conf/locale/sv/formats.py:
⋮...

│conf/locale/ta/formats.py:
⋮...

│conf/locale/te/formats.py:
⋮...

│conf/locale/tg/formats.py:
⋮...

│conf/locale/th/formats.py:
⋮...

│conf/locale/tk/formats.py:
⋮...

│conf/locale/tr/formats.py:
⋮...

│conf/locale/ug/formats.py:
⋮...

│conf/locale/uk/formats.py:
⋮...

│conf/locale/uz/formats.py:
⋮...

│conf/locale/vi/formats.py:
⋮...

│conf/locale/zh_Hans/formats.py:
⋮...

│conf/locale/zh_Hant/formats.py:
⋮...

│conf/urls/__init__.py:
⋮...

│conf/urls/i18n.py:
⋮...
│def i18n_patterns()
│def is_language_prefix_patterns_used()
⋮...

│conf/urls/static.py:
⋮...
│def static()
⋮...

│contrib/admin/__init__.py:
⋮...
│def autodiscover()
⋮...

│contrib/admin/actions.py:
⋮...
│def delete_selected()
⋮...

│contrib/admin/apps.py:
⋮...
│class SimpleAdminConfig
│    def ready()
│class AdminConfig
│    def ready()
⋮...

│contrib/admin/checks.py:
⋮...
│def _issubclass()
│def _contains_subclass()
│def check_admin_app()
│def check_dependencies()
│class BaseModelAdminChecks
│    def check()
│    def _check_autocomplete_fields()
│    def _check_autocomplete_fields_item()
│    def _check_raw_id_fields()
│    def _check_raw_id_fields_item()
│    def _check_fields()
│    def _check_fieldsets()
│    def _check_fieldsets_item()
│    def _check_field_spec()
│    def _check_field_spec_item()
│    def _check_exclude()
│    def _check_form()
│    def _check_filter_vertical()
│    def _check_filter_horizontal()
│    def _check_filter_item()
│    def _check_radio_fields()
│    def _check_radio_fields_key()
│    def _check_radio_fields_value()
│    def _check_view_on_site_url()
│    def _check_prepopulated_fields()
│    def _check_prepopulated_fields_key()
│    def _check_prepopulated_fields_value()
│    def _check_prepopulated_fields_value_item()
│    def _check_ordering()
│    def _check_ordering_item()
│    def _check_readonly_fields()
│    def _check_readonly_fields_item()
│class ModelAdminChecks
│    def check()
│    def _check_save_as()
│    def _check_save_on_top()
│    def _check_inlines()
│    def _check_inlines_item()
│    def _check_list_display()
│    def _check_list_display_item()
│    def _check_list_display_links()
│    def _check_list_display_links_item()
│    def _check_list_filter()
│    def _check_list_filter_item()
│    def _check_list_select_related()
│    def _check_list_per_page()
│    def _check_list_max_show_all()
│    def _check_list_editable()
│    def _check_list_editable_item()
│    def _check_search_fields()
│    def _check_date_hierarchy()
│    def _check_actions()
│class InlineModelAdminChecks
│    def check()
│    def _check_exclude_of_parent_model()
│    def _check_relation()
│    def _check_extra()
│    def _check_max_num()
│    def _check_min_num()
│    def _check_formset()
│def must_be()
│def must_inherit_from()
│def refer_to_missing_field()
⋮...

│contrib/admin/decorators.py:
⋮...
│def action()
│def decorator()
│def display()
│def decorator()
│def register()
│def _model_admin_wrapper()
⋮...

│contrib/admin/exceptions.py:
⋮...
│class DisallowedModelAdminLookup
│class DisallowedModelAdminToField
│class AlreadyRegistered
│class NotRegistered
⋮...

│contrib/admin/filters.py:
⋮...
│class ListFilter
│    def __init__()
│    def has_output()
│    def choices()
│    def queryset()
│    def expected_parameters()
│class FacetsMixin
│    def get_facet_counts()
│    def get_facet_queryset()
│class SimpleListFilter
│    def __init__()
│    def has_output()
│    def value()
│    def lookups()
│    def expected_parameters()
│    def get_facet_counts()
│    def choices()
│class FieldListFilter
│    def __init__()
│    def has_output()
│    def queryset()
│    def register()
│    def create()
│class RelatedFieldListFilter
│    def __init__()
│    def include_empty_choice()
│    def has_output()
│    def expected_parameters()
│    def field_admin_ordering()
│    def field_choices()
│    def get_facet_counts()
│    def choices()
│class BooleanFieldListFilter
│    def __init__()
│    def expected_parameters()
│    def get_facet_counts()
│    def choices()
│class ChoicesFieldListFilter
│    def __init__()
│    def expected_parameters()
│    def get_facet_counts()
│    def choices()
│class DateFieldListFilter
│    def __init__()
│    def expected_parameters()
│    def get_facet_counts()
│    def choices()
│class AllValuesFieldListFilter
│    def __init__()
│    def expected_parameters()
│    def get_facet_counts()
│    def choices()
│class RelatedOnlyFieldListFilter
│    def field_choices()
│class EmptyFieldListFilter
│    def __init__()
│    def get_lookup_condition()
│    def queryset()
│    def expected_parameters()
│    def get_facet_counts()
│    def choices()
⋮...

│contrib/admin/forms.py:
⋮...
│class AdminAuthenticationForm
│    def confirm_login_allowed()
│class AdminPasswordChangeForm
⋮...

│contrib/admin/helpers.py:
⋮...
│class ActionForm
│class AdminForm
│    def __init__()
│    def __repr__()
│    def __iter__()
│    def errors()
│    def non_field_errors()
│    def fields()
│    def is_bound()
│    def media()
│class Fieldset
│    def __init__()
│    def media()
│    def is_collapsible()
│    def __iter__()
│class Fieldline
│    def __init__()
│    def __iter__()
│    def errors()
│class AdminField
│    def __init__()
│    def label_tag()
│    def errors()
│class AdminReadonlyField
│    def __init__()
│    def label_tag()
│    def get_admin_url()
│    def contents()
│class InlineAdminFormSet
│    def __init__()
│    def __iter__()
│    def fields()
│    def inline_formset_data()
│    def forms()
│    def is_collapsible()
│    def non_form_errors()
│    def is_bound()
│    def total_form_count()
│    def media()
│class InlineAdminForm
│    def __init__()
│    def __iter__()
│    def needs_explicit_pk_field()
│    def pk_field()
│    def fk_field()
│    def deletion_field()
│class InlineFieldset
│    def __init__()
│    def __iter__()
│class AdminErrorList
│    def __init__()
⋮...

│contrib/admin/migrations/0001_initial.py:
⋮...
│class Migration
⋮...

│contrib/admin/migrations/0002_logentry_remove_auto_add.py:
⋮...
│class Migration
⋮...

│contrib/admin/migrations/0003_logentry_add_action_flag_choices.py:
⋮...
│class Migration
⋮...

│contrib/admin/models.py:
⋮...
│class LogEntryManager
│    def log_actions()
│class LogEntry
│class Meta
│    def __repr__()
│    def __str__()
│    def is_addition()
│    def is_change()
│    def is_deletion()
│    def get_change_message()
│    def get_edited_object()
│    def get_admin_url()
⋮...

│contrib/admin/options.py:
⋮...
│class ShowFacets
│def get_content_type_for_model()
│def get_ul_class()
│class IncorrectLookupParameters
│class BaseModelAdmin
│    def check()
│    def __init__()
│    def formfield_for_dbfield()
│    def formfield_for_choice_field()
│    def get_field_queryset()
│    def formfield_for_foreignkey()
│    def formfield_for_manytomany()
│    def get_autocomplete_fields()
│    def get_view_on_site_url()
│    def get_empty_value_display()
│    def get_exclude()
│    def get_fields()
│    def get_fieldsets()
│    def get_inlines()
│    def get_ordering()
│    def get_readonly_fields()
│    def get_prepopulated_fields()
│    def get_queryset()
│    def get_sortable_by()
│    def lookup_allowed()
│    def to_field_allowed()
│    def has_add_permission()
│    def has_change_permission()
│    def has_delete_permission()
│    def has_view_permission()
│    def has_view_or_change_permission()
│    def has_module_permission()
│class ModelAdmin
│    def __init__()
│    def __str__()
│    def __repr__()
│    def get_inline_instances()
│    def get_urls()
│    def wrap()
│    def wrapper()
│    def urls()
│    def media()
│    def get_model_perms()
│    def _get_form_for_get_fields()
│    def get_form()
│    def get_changelist()
│    def get_changelist_instance()
│    def get_object()
│    def get_changelist_form()
│    def get_changelist_formset()
│    def get_formsets_with_inlines()
│    def get_paginator()
│    def log_addition()
│    def log_change()
│    def log_deletions()
│    def action_checkbox()
│    def _get_action_description()
│    def _get_base_actions()
│    def _filter_actions_by_permissions()
│    def get_actions()
│    def get_action_choices()
│    def get_action()
│    def get_list_display()
│    def get_list_display_links()
│    def get_list_filter()
│    def get_list_select_related()
│    def get_search_fields()
│    def get_search_results()
│    def construct_search()
│    def get_preserved_filters()
│    def construct_change_message()
│    def message_user()
│    def save_form()
│    def save_model()
│    def delete_model()
│    def delete_queryset()
│    def save_formset()
│    def save_related()
│    def render_change_form()
│    def _get_preserved_qsl()
│    def response_add()
│    def response_change()
│    def _response_post_save()
│    def response_post_save_add()
│    def response_post_save_change()
│    def response_action()
│    def response_delete()
│    def render_delete_form()
│    def get_inline_formsets()
│    def get_changeform_initial_data()
│    def _get_obj_does_not_exist_redirect()
│    def changeform_view()
│    def _changeform_view()
│    def add_view()
│    def change_view()
│    def _get_edited_object_pks()
│    def _get_list_editable_queryset()
│    def changelist_view()
│    def get_deleted_objects()
│    def delete_view()
│    def _delete_view()
│    def history_view()
│    def get_formset_kwargs()
│    def _create_formsets()
│    def user_deleted_form()
│class InlineModelAdmin
│    def __init__()
│    def media()
│    def get_extra()
│    def get_min_num()
│    def get_max_num()
│    def get_formset()
│class DeleteProtectedModelForm
│    def hand_clean_DELETE()
│    def is_valid()
│    def has_changed()
│    def _get_form_for_get_fields()
│    def get_queryset()
│    def _has_any_perms_for_target_model()
│    def has_add_permission()
│    def has_change_permission()
│    def has_delete_permission()
│    def has_view_permission()
│class StackedInline
│class TabularInline
⋮...

│contrib/admin/sites.py:
⋮...
│class AdminSite
│    def __init__()
│    def __repr__()
│    def check()
│    def register()
│    def unregister()
│    def is_registered()
│    def get_model_admin()
│    def add_action()
│    def disable_action()
│    def get_action()
│    def actions()
│    def has_permission()
│    def admin_view()
│    def inner()
│    def get_urls()
│    def wrap()
│    def wrapper()
│    def urls()
│    def each_context()
│    def password_change()
│    def password_change_done()
│    def i18n_javascript()
│    def logout()
│    def login()
│    def autocomplete_view()
│    def catch_all_view()
│    def _build_app_dict()
│    def get_app_list()
│    def index()
│    def app_index()
│    def get_log_entries()
│class DefaultAdminSite
│    def _setup()
│    def __repr__()
⋮...

│contrib/admin/static/admin/css/autocomplete.css:
⋮...

│contrib/admin/static/admin/css/base.css:
⋮...

│contrib/admin/static/admin/css/changelists.css:
⋮...

│contrib/admin/static/admin/css/dark_mode.css:
⋮...

│contrib/admin/static/admin/css/forms.css:
⋮...

│contrib/admin/static/admin/css/login.css:
⋮...

│contrib/admin/static/admin/css/nav_sidebar.css:
⋮...

│contrib/admin/static/admin/css/responsive.css:
⋮...

│contrib/admin/static/admin/css/responsive_rtl.css:
⋮...

│contrib/admin/static/admin/css/rtl.css:
⋮...

│contrib/admin/static/admin/css/unusable_password_field.css:
⋮...

│contrib/admin/static/admin/css/vendor/select2/select2.css:
⋮...

│contrib/admin/static/admin/css/vendor/select2/select2.min.css:
⋮...

│contrib/admin/static/admin/css/widgets.css:
⋮...

│contrib/admin/static/admin/js/SelectBox.js:
⋮...

│contrib/admin/static/admin/js/SelectFilter2.js:
⋮...

│contrib/admin/static/admin/js/actions.js:
⋮...

│contrib/admin/static/admin/js/admin/DateTimeShortcuts.js:
⋮...

│contrib/admin/static/admin/js/admin/RelatedObjectLookups.js:
⋮...

│contrib/admin/static/admin/js/autocomplete.js:
⋮...

│contrib/admin/static/admin/js/calendar.js:
⋮...

│contrib/admin/static/admin/js/cancel.js:
⋮...

│contrib/admin/static/admin/js/change_form.js:
⋮...

│contrib/admin/static/admin/js/core.js:
⋮...

│contrib/admin/static/admin/js/filters.js:
⋮...

│contrib/admin/static/admin/js/inlines.js:
⋮...

│contrib/admin/static/admin/js/nav_sidebar.js:
⋮...

│contrib/admin/static/admin/js/popup_response.js:
⋮...

│contrib/admin/static/admin/js/prepopulate.js:
⋮...

│contrib/admin/static/admin/js/prepopulate_init.js:
⋮...

│contrib/admin/static/admin/js/theme.js:
⋮...

│contrib/admin/static/admin/js/unusable_password_field.js:
⋮...

│contrib/admin/static/admin/js/urlify.js:
⋮...

│contrib/admin/static/admin/js/vendor/jquery/jquery.js:
⋮...

│contrib/admin/static/admin/js/vendor/jquery/jquery.min.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/af.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/ar.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/az.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/bg.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/bn.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/bs.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/ca.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/cs.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/da.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/de.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/dsb.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/el.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/en.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/es.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/et.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/eu.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/fa.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/fi.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/fr.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/gl.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/he.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/hi.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/hr.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/hsb.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/hu.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/hy.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/id.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/is.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/it.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/ja.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/ka.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/km.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/ko.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/lt.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/lv.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/mk.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/ms.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/nb.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/ne.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/nl.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/pl.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/ps.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/pt-BR.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/pt.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/ro.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/ru.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/sk.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/sl.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/sq.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/sr-Cyrl.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/sr.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/sv.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/th.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/tk.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/tr.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/uk.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/vi.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/zh-CN.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/i18n/zh-TW.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/select2.full.js:
⋮...

│contrib/admin/static/admin/js/vendor/select2/select2.full.min.js:
⋮...

│contrib/admin/static/admin/js/vendor/xregexp/xregexp.js:
⋮...

│contrib/admin/static/admin/js/vendor/xregexp/xregexp.min.js:
⋮...

│contrib/admin/templates/admin/500.html:
⋮...

│contrib/admin/templates/admin/base_site.html:
⋮...

│contrib/admin/templates/admin/color_theme_toggle.html:
⋮...

│contrib/admin/templatetags/admin_list.py:
⋮...
│def paginator_number()
│def pagination()
│def pagination_tag()
│def result_headers()
│def make_qs_param()
│def _boolean_icon()
│def _coerce_field_name()
│def items_for_result()
│def link_in_col()
│class ResultList
│    def __init__()
│def results()
│def result_hidden_fields()
│def result_list()
│def result_list_tag()
│def date_hierarchy()
│def link()
│def date_hierarchy_tag()
│def search_form()
│def search_form_tag()
│def admin_list_filter()
│def admin_actions()
│def admin_actions_tag()
│def change_list_object_tools_tag()
⋮...

│contrib/admin/templatetags/admin_modify.py:
⋮...
│def prepopulated_fields_js()
│def prepopulated_fields_js_tag()
│def submit_row()
│def submit_row_tag()
│def change_form_object_tools_tag()
│def cell_count()
⋮...

│contrib/admin/templatetags/admin_urls.py:
⋮...
│def admin_urlname()
│def admin_urlquote()
│def add_preserved_filters()
⋮...

│contrib/admin/templatetags/base.py:
⋮...
│class InclusionAdminNode
│    def __init__()
│    def render()
⋮...

│contrib/admin/templatetags/log.py:
⋮...
│class AdminLogNode
│    def __init__()
│    def __repr__()
│    def render()
│def get_admin_log()
⋮...

│contrib/admin/tests.py:
⋮...
│class CSPMiddleware
│    def process_response()
│class AdminSeleniumTestCase
│    def wait_until()
│    def wait_for_and_switch_to_popup()
│    def wait_for()
│    def wait_for_text()
│    def wait_for_value()
│    def wait_until_visible()
│    def wait_until_invisible()
│    def wait_page_ready()
│    def wait_page_loaded()
│    def admin_login()
│    def select_option()
│    def deselect_option()
│    def assertCountSeleniumElements()
│    def _assertOptionsValues()
│    def assertSelectOptions()
│    def assertSelectedOptions()
│    def is_disabled()
⋮...

│contrib/admin/utils.py:
⋮...
│class FieldIsAForeignKeyColumnName
│def lookup_spawns_duplicates()
│def get_last_value_from_parameters()
│def prepare_lookup_value()
│def build_q_object_from_lookup_parameters()
│def quote()
│def unquote()
│def flatten()
│def flatten_fieldsets()
│def get_deleted_objects()
│def format_callback()
│class NestedObjects
│    def __init__()
│    def add_edge()
│    def collect()
│    def related_objects()
│    def _nested()
│    def nested()
│    def can_fast_delete()
│def model_format_dict()
│def model_ngettext()
│def lookup_field()
│def _get_non_gfk_field()
│def label_for_field()
│def help_text_for_field()
│def display_for_field()
│def display_for_value()
│class NotRelationField
│def get_model_from_relation()
│def reverse_field_path()
│def get_fields_from_path()
│def construct_change_message()
│def _get_changed_field_labels_from_form()
⋮...

│contrib/admin/views/autocomplete.py:
⋮...
│class AutocompleteJsonView
│    def get()
│    def serialize_result()
│    def get_paginator()
│    def get_queryset()
│    def process_request()
│    def has_perm()
⋮...

│contrib/admin/views/decorators.py:
⋮...
│def staff_member_required()
⋮...

│contrib/admin/views/main.py:
⋮...
│class ChangeListSearchForm
│    def __init__()
│class ChangeList
│    def __init__()
│    def __repr__()
│    def get_filters_params()
│    def get_filters()
│    def get_query_string()
│    def get_results()
│    def _get_default_ordering()
│    def get_ordering_field()
│    def get_ordering()
│    def _get_deterministic_ordering()
│    def get_ordering_field_columns()
│    def get_queryset()
│    def apply_select_related()
│    def has_related_field_in_list_display()
│    def url_for_result()
⋮...

│contrib/admin/widgets.py:
⋮...
│class FilteredSelectMultiple
│class Media
│    def __init__()
│    def get_context()
│class BaseAdminDateWidget
│class Media
│    def __init__()
│class AdminDateWidget
│class BaseAdminTimeWidget
│class Media
│    def __init__()
│class AdminTimeWidget
│class AdminSplitDateTime
│    def __init__()
│    def get_context()
│class AdminRadioSelect
│class AdminFileWidget
│def url_params_from_lookup_dict()
│class ForeignKeyRawIdWidget
│    def __init__()
│    def get_context()
│    def base_url_parameters()
│    def url_parameters()
│    def label_and_url_for_value()
│class ManyToManyRawIdWidget
│    def get_context()
│    def url_parameters()
│    def label_and_url_for_value()
│    def value_from_datadict()
│    def format_value()
│class RelatedFieldWidgetWrapper
│    def __init__()
│    def __deepcopy__()
│    def is_hidden()
│    def media()
│    def choices()
│    def choices()
│    def get_related_url()
│    def get_context()
│    def value_from_datadict()
│    def value_omitted_from_data()
│    def id_for_label()
│class AdminTextareaWidget
│    def __init__()
│class AdminTextInputWidget
│    def __init__()
│class AdminEmailInputWidget
│    def __init__()
│class AdminURLFieldWidget
│    def __init__()
│    def get_context()
│class AdminIntegerFieldWidget
│    def __init__()
│class AdminBigIntegerFieldWidget
│class AdminUUIDInputWidget
│    def __init__()
│def get_select2_language()
│class AutocompleteMixin
│    def __init__()
│    def get_url()
│    def build_attrs()
│    def optgroups()
│    def media()
│class AutocompleteSelect
│class AutocompleteSelectMultiple
⋮...

│contrib/admindocs/apps.py:
⋮...
│class AdminDocsConfig
⋮...

│contrib/admindocs/middleware.py:
⋮...
│class XViewMiddleware
│    def process_view()
⋮...

│contrib/admindocs/templates/admin_doc/bookmarklets.html:
⋮...

│contrib/admindocs/templates/admin_doc/template_detail.html:
⋮...

│contrib/admindocs/templates/admin_doc/template_filter_index.html:
⋮...

│contrib/admindocs/templates/admin_doc/template_tag_index.html:
⋮...

│contrib/admindocs/urls.py:
⋮...

│contrib/admindocs/utils.py:
⋮...
│def get_view_name()
│def parse_docstring()
│def parse_rst()
│def split_explicit_title()
│def create_reference_role()
│def _role()
│def default_reference_role()
│def replace_metacharacters()
│def _get_group_start_end()
│def _find_groups()
│def replace_named_groups()
│def replace_unnamed_groups()
│def remove_non_capturing_groups()
│def strip_p_tags()
⋮...

│contrib/admindocs/views.py:
⋮...
│class BaseAdminDocsView
│    def dispatch()
│    def get_context_data()
│class BookmarkletsView
│class TemplateTagIndexView
│    def get_context_data()
│class TemplateFilterIndexView
│    def get_context_data()
│class ViewIndexView
│    def get_context_data()
│class ViewDetailView
│    def _get_view_func()
│    def get_context_data()
│def user_has_model_view_permission()
│class ModelIndexView
│    def get_context_data()
│class ModelDetailView
│    def get_context_data()
│class TemplateDetailView
│    def get_context_data()
│def get_return_data_type()
│def get_readable_field_data_type()
│def extract_views_from_urlpatterns()
│def simplify_regex()
⋮...

│contrib/auth/__init__.py:
⋮...
│def load_backend()
│def _get_backends()
│def get_backends()
│def _get_compatible_backends()
│def _get_backend_from_user()
│def _clean_credentials()
│def _get_user_session_key()
│def async _aget_user_session_key()
│def authenticate()
│def async aauthenticate()
│def login()
│def async alogin()
│def logout()
│def async alogout()
│def get_user_model()
│def get_user()
│def async aget_user()
│def get_permission_codename()
│def update_session_auth_hash()
│def async aupdate_session_auth_hash()
⋮...

│contrib/auth/admin.py:
⋮...
│class GroupAdmin
│    def formfield_for_manytomany()
│class UserAdmin
│    def get_fieldsets()
│    def get_form()
│    def get_urls()
│    def lookup_allowed()
│    def add_view()
│    def _add_view()
│    def user_change_password()
│    def response_add()
⋮...

│contrib/auth/apps.py:
⋮...
│class AuthConfig
│    def ready()
⋮...

│contrib/auth/backends.py:
⋮...
│class BaseBackend
│    def authenticate()
│def async aauthenticate()
│def get_user()
│def async aget_user()
│def get_user_permissions()
│def async aget_user_permissions()
│def get_group_permissions()
│def async aget_group_permissions()
│def get_all_permissions()
│def async aget_all_permissions()
│def has_perm()
│def async ahas_perm()
│class ModelBackend
│    def authenticate()
│def async aauthenticate()
│def user_can_authenticate()
│def _get_user_permissions()
│def _get_group_permissions()
│def _get_permissions()
│def async _aget_permissions()
│def get_user_permissions()
│def async aget_user_permissions()
│def get_group_permissions()
│def async aget_group_permissions()
│def get_all_permissions()
│def has_perm()
│def async ahas_perm()
│def has_module_perms()
│def async ahas_module_perms()
│def with_perm()
│def get_user()
│def async aget_user()
│class AllowAllUsersModelBackend
│    def user_can_authenticate()
│class RemoteUserBackend
│    def authenticate()
│def async aauthenticate()
│def clean_username()
│def configure_user()
│def async aconfigure_user()
│class AllowAllUsersRemoteUserBackend
│    def user_can_authenticate()
⋮...

│contrib/auth/base_user.py:
⋮...
│class BaseUserManager
│    def normalize_email()
│    def get_by_natural_key()
│def async aget_by_natural_key()
│class AbstractBaseUser
│class Meta
│    def __str__()
│    def save()
│    def get_username()
│    def clean()
│    def natural_key()
│    def is_anonymous()
│    def is_authenticated()
│    def set_password()
│    def check_password()
│    def setter()
│def async acheck_password()
│def async setter()
│def set_unusable_password()
│def has_usable_password()
│def get_session_auth_hash()
│def get_session_auth_fallback_hash()
│def _get_session_auth_hash()
│def get_email_field_name()
│def normalize_username()
⋮...

│contrib/auth/checks.py:
⋮...
│def _subclass_index()
│def check_user_model()
│def check_models_permissions()
│def check_middleware()
⋮...

│contrib/auth/context_processors.py:
⋮...
│class PermLookupDict
│    def __init__()
│    def __repr__()
│    def __getitem__()
│    def __iter__()
│    def __bool__()
│class PermWrapper
│    def __init__()
│    def __repr__()
│    def __getitem__()
│    def __iter__()
│    def __contains__()
│def auth()
⋮...

│contrib/auth/decorators.py:
⋮...
│def user_passes_test()
│def decorator()
│def _redirect_to_login()
│def async _view_wrapper()
│def _view_wrapper()
│def login_required()
│def login_not_required()
│def permission_required()
│def decorator()
│def async check_perms()
│def check_perms()
⋮...

│contrib/auth/forms.py:
⋮...
│def _unicode_ci_compare()
│class ReadOnlyPasswordHashWidget
│    def get_context()
│    def id_for_label()
│class ReadOnlyPasswordHashField
│    def __init__()
│class UsernameField
│    def to_python()
│    def widget_attrs()
│class SetPasswordMixin
│    def create_password_fields()
│    def validate_passwords()
│    def validate_password_for_user()
│    def set_password_and_save()
│class SetUnusablePasswordMixin
│    def create_usable_password_field()
│    def validate_passwords()
│    def validate_password_for_user()
│    def set_password_and_save()
│class BaseUserCreationForm
│class Meta
│    def __init__()
│    def clean()
│    def _post_clean()
│    def save()
│class UserCreationForm
│    def clean_username()
│class UserChangeForm
│class Meta
│    def __init__()
│class AuthenticationForm
│    def __init__()
│    def clean()
│    def confirm_login_allowed()
│    def get_user()
│    def get_invalid_login_error()
│class PasswordResetForm
│    def send_mail()
│    def get_users()
│    def save()
│class SetPasswordForm
│    def __init__()
│    def clean()
│    def save()
│class PasswordChangeForm
│    def clean_old_password()
│class AdminPasswordChangeForm
│    def __init__()
│    def clean()
│    def save()
│    def changed_data()
│class AdminUserCreationForm
│    def __init__()
⋮...

│contrib/auth/handlers/modwsgi.py:
⋮...
│def check_password()
│def groups_for_user()
⋮...

│contrib/auth/hashers.py:
⋮...
│def is_password_usable()
│def verify_password()
│def check_password()
│def async acheck_password()
│def make_password()
│def get_hashers()
│def get_hashers_by_algorithm()
│def reset_hashers()
│def get_hasher()
│def identify_hasher()
│def mask_hash()
│def must_update_salt()
│class BasePasswordHasher
│    def _load_library()
│    def salt()
│    def verify()
│    def _check_encode_args()
│    def encode()
│    def decode()
│    def safe_summary()
│    def must_update()
│    def harden_runtime()
│class PBKDF2PasswordHasher
│    def encode()
│    def decode()
│    def verify()
│    def safe_summary()
│    def must_update()
│    def harden_runtime()
│class PBKDF2SHA1PasswordHasher
│class Argon2PasswordHasher
│    def encode()
│    def decode()
│    def verify()
│    def safe_summary()
│    def must_update()
│    def harden_runtime()
│    def params()
│class BCryptSHA256PasswordHasher
│    def salt()
│    def encode()
│    def decode()
│    def verify()
│    def safe_summary()
│    def must_update()
│    def harden_runtime()
│class BCryptPasswordHasher
│class ScryptPasswordHasher
│    def encode()
│    def decode()
│    def verify()
│    def safe_summary()
│    def must_update()
│    def harden_runtime()
│class MD5PasswordHasher
│    def encode()
│    def decode()
│    def verify()
│    def safe_summary()
│    def must_update()
│    def harden_runtime()
⋮...

│contrib/auth/management/__init__.py:
⋮...
│def _get_all_permissions()
│def _get_builtin_permissions()
│def create_permissions()
│def get_system_username()
│def get_default_username()
⋮...

│contrib/auth/management/commands/changepassword.py:
⋮...
│class Command
│    def _get_pass()
│    def add_arguments()
│    def handle()
⋮...

│contrib/auth/management/commands/createsuperuser.py:
⋮...
│class NotRunningInTTYException
│class Command
│    def __init__()
│    def add_arguments()
│    def execute()
│    def handle()
│    def get_input_data()
│    def _get_input_message()
│    def username_is_unique()
│    def _validate_username()
⋮...

│contrib/auth/middleware.py:
⋮...
│def get_user()
│def async auser()
│class AuthenticationMiddleware
│    def process_request()
│class LoginRequiredMiddleware
│    def process_view()
│    def get_login_url()
│    def get_redirect_field_name()
│    def handle_no_permission()
│class RemoteUserMiddleware
│    def __init__()
│    def __call__()
│def async __acall__()
│def clean_username()
│def _remove_invalid_user()
│def async _aremove_invalid_user()
│class PersistentRemoteUserMiddleware
⋮...

│contrib/auth/migrations/0001_initial.py:
⋮...
│class Migration
⋮...

│contrib/auth/migrations/0002_alter_permission_name_max_length.py:
⋮...
│class Migration
⋮...

│contrib/auth/migrations/0003_alter_user_email_max_length.py:
⋮...
│class Migration
⋮...

│contrib/auth/migrations/0004_alter_user_username_opts.py:
⋮...
│class Migration
⋮...

│contrib/auth/migrations/0005_alter_user_last_login_null.py:
⋮...
│class Migration
⋮...

│contrib/auth/migrations/0006_require_contenttypes_0002.py:
⋮...
│class Migration
⋮...

│contrib/auth/migrations/0007_alter_validators_add_error_messages.py:
⋮...
│class Migration
⋮...

│contrib/auth/migrations/0008_alter_user_username_max_length.py:
⋮...
│class Migration
⋮...

│contrib/auth/migrations/0009_alter_user_last_name_max_length.py:
⋮...
│class Migration
⋮...

│contrib/auth/migrations/0010_alter_group_name_max_length.py:
⋮...
│class Migration
⋮...

│contrib/auth/migrations/0011_update_proxy_permissions.py:
⋮...
│def update_proxy_model_permissions()
│def revert_proxy_model_permissions()
│class Migration
⋮...

│contrib/auth/migrations/0012_alter_user_first_name_max_length.py:
⋮...
│class Migration
⋮...

│contrib/auth/mixins.py:
⋮...
│class AccessMixin
│    def get_login_url()
│    def get_permission_denied_message()
│    def get_redirect_field_name()
│    def handle_no_permission()
│class LoginRequiredMixin
│    def dispatch()
│class PermissionRequiredMixin
│    def get_permission_required()
│    def has_permission()
│    def dispatch()
│class UserPassesTestMixin
│    def test_func()
│    def get_test_func()
│    def dispatch()
⋮...

│contrib/auth/models.py:
⋮...
│def update_last_login()
│class PermissionManager
│    def get_by_natural_key()
│class Permission
│class Meta
│    def __str__()
│    def natural_key()
│class GroupManager
│    def get_by_natural_key()
│def async aget_by_natural_key()
│class Group
│class Meta
│    def __str__()
│    def natural_key()
│class UserManager
│    def _create_user_object()
│    def _create_user()
│def async _acreate_user()
│def create_user()
│def async acreate_user()
│def create_superuser()
│def async acreate_superuser()
│def with_perm()
│def _user_get_permissions()
│def async _auser_get_permissions()
│def _user_has_perm()
│def async _auser_has_perm()
│def _user_has_module_perms()
│def async _auser_has_module_perms()
│class PermissionsMixin
│class Meta
│    def get_user_permissions()
│def async aget_user_permissions()
│def get_group_permissions()
│def async aget_group_permissions()
│def get_all_permissions()
│def async aget_all_permissions()
│def has_perm()
│def async ahas_perm()
│def has_perms()
│def async ahas_perms()
│def has_module_perms()
│def async ahas_module_perms()
│class AbstractUser
│class Meta
│    def clean()
│    def get_full_name()
│    def get_short_name()
│    def email_user()
│class User
│class Meta
│class AnonymousUser
│    def __str__()
│    def __eq__()
│    def __hash__()
│    def __int__()
│    def save()
│    def delete()
│    def set_password()
│    def check_password()
│    def groups()
│    def user_permissions()
│    def get_user_permissions()
│def async aget_user_permissions()
│def get_group_permissions()
│def async aget_group_permissions()
│def get_all_permissions()
│def async aget_all_permissions()
│def has_perm()
│def async ahas_perm()
│def has_perms()
│def async ahas_perms()
│def has_module_perms()
│def async ahas_module_perms()
│def is_anonymous()
│def is_authenticated()
│def get_username()
⋮...

│contrib/auth/password_validation.py:
⋮...
│def get_default_password_validators()
│def get_password_validators()
│def validate_password()
│def password_changed()
│def password_validators_help_texts()
│def _password_validators_help_text_html()
│class MinimumLengthValidator
│    def __init__()
│    def validate()
│    def get_error_message()
│    def get_help_text()
│def exceeds_maximum_length_ratio()
│class UserAttributeSimilarityValidator
│    def __init__()
│    def validate()
│    def get_error_message()
│    def get_help_text()
│class CommonPasswordValidator
│    def DEFAULT_PASSWORD_LIST_PATH()
│    def __init__()
│    def validate()
│    def get_error_message()
│    def get_help_text()
│class NumericPasswordValidator
│    def validate()
│    def get_error_message()
│    def get_help_text()
⋮...

│contrib/auth/signals.py:
⋮...

│contrib/auth/tokens.py:
⋮...
│class PasswordResetTokenGenerator
│    def __init__()
│    def _get_secret()
│    def _set_secret()
│    def _get_fallbacks()
│    def _set_fallbacks()
│    def make_token()
│    def check_token()
│    def _make_token_with_timestamp()
│    def _make_hash_value()
│    def _num_seconds()
│    def _now()
⋮...

│contrib/auth/urls.py:
⋮...

│contrib/auth/validators.py:
⋮...
│class ASCIIUsernameValidator
│class UnicodeUsernameValidator
⋮...

│contrib/auth/views.py:
⋮...
│class RedirectURLMixin
│    def get_success_url()
│    def get_redirect_url()
│    def get_success_url_allowed_hosts()
│    def get_default_redirect_url()
│class LoginView
│    def dispatch()
│    def get_default_redirect_url()
│    def get_form_class()
│    def get_form_kwargs()
│    def form_valid()
│    def get_context_data()
│class LogoutView
│    def dispatch()
│    def post()
│    def get_default_redirect_url()
│    def get_context_data()
│def logout_then_login()
│def redirect_to_login()
│class PasswordContextMixin
│    def get_context_data()
│class PasswordResetView
│    def dispatch()
│    def form_valid()
│class PasswordResetDoneView
│class PasswordResetConfirmView
│    def dispatch()
│    def get_user()
│    def get_form_kwargs()
│    def form_valid()
│    def get_context_data()
│class PasswordResetCompleteView
│    def get_context_data()
│class PasswordChangeView
│    def dispatch()
│    def get_form_kwargs()
│    def form_valid()
│class PasswordChangeDoneView
│    def dispatch()
⋮...

│contrib/contenttypes/admin.py:
⋮...
│class GenericInlineModelAdminChecks
│    def _check_exclude_of_parent_model()
│    def _check_relation()
│class GenericInlineModelAdmin
│    def get_formset()
│class GenericStackedInline
│class GenericTabularInline
⋮...

│contrib/contenttypes/apps.py:
⋮...
│class ContentTypesConfig
│    def ready()
⋮...

│contrib/contenttypes/checks.py:
⋮...
│def check_generic_foreign_keys()
│def check_model_name_lengths()
⋮...

│contrib/contenttypes/fields.py:
⋮...
│class GenericForeignKey
│    def __init__()
│    def contribute_to_class()
│    def get_attname_column()
│    def get_filter_kwargs_for_object()
│    def get_forward_related_filter()
│    def check()
│    def _check_object_id_field()
│    def _check_content_type_field()
│    def cache_name()
│    def get_content_type()
│    def get_prefetch_querysets()
│    def gfk_key()
│    def __get__()
│    def __set__()
│class GenericRel
│    def __init__()
│class GenericRelation
│    def __init__()
│    def check()
│    def _is_matching_generic_foreign_key()
│    def _check_generic_foreign_key_existence()
│    def resolve_related_fields()
│    def get_local_related_value()
│    def get_foreign_related_value()
│    def _get_path_info_with_parent()
│    def get_path_info()
│    def get_reverse_path_info()
│    def value_to_string()
│    def contribute_to_class()
│    def make_generic_foreign_order_accessors()
│    def set_attributes_from_rel()
│    def get_internal_type()
│    def get_content_type()
│    def get_extra_restriction()
│    def bulk_related_objects()
│class ReverseGenericManyToOneDescriptor
│    def related_manager_cls()
│def create_generic_related_manager()
│class GenericRelatedObjectManager
│    def __init__()
│    def __call__()
│    def __str__()
│    def _apply_rel_filters()
│    def _remove_prefetched_objects()
│    def get_queryset()
│    def get_prefetch_querysets()
│    def add()
│    def check_and_update_obj()
│def async aadd()
│def remove()
│def async aremove()
│def clear()
│def async aclear()
│def _clear()
│def set()
│def async aset()
│def create()
│def async acreate()
│def get_or_create()
│def async aget_or_create()
│def update_or_create()
│def async aupdate_or_create()
⋮...

│contrib/contenttypes/forms.py:
⋮...
│class BaseGenericInlineFormSet
│    def __init__()
│    def initial_form_count()
│    def get_default_prefix()
│    def save_new()
│def generic_inlineformset_factory()
⋮...

│contrib/contenttypes/management/__init__.py:
⋮...
│class RenameContentType
│    def __init__()
│    def _rename()
│    def rename_forward()
│    def rename_backward()
│def inject_rename_contenttypes_operations()
│def get_contenttypes_and_models()
│def create_contenttypes()
⋮...

│contrib/contenttypes/management/commands/remove_stale_contenttypes.py:
⋮...
│class Command
│    def add_arguments()
│    def handle()
│class NoFastDeleteCollector
│    def can_fast_delete()
⋮...

│contrib/contenttypes/migrations/0001_initial.py:
⋮...
│class Migration
⋮...

│contrib/contenttypes/migrations/0002_remove_content_type_name.py:
⋮...
│def add_legacy_name()
│class Migration
⋮...

│contrib/contenttypes/models.py:
⋮...
│class ContentTypeManager
│    def __init__()
│    def get_by_natural_key()
│    def _get_opts()
│    def _get_from_cache()
│    def get_for_model()
│    def get_for_models()
│    def get_for_id()
│    def clear_cache()
│    def _add_to_cache()
│class ContentType
│class Meta
│    def __str__()
│    def name()
│    def app_labeled_name()
│    def model_class()
│    def get_object_for_this_type()
│    def get_all_objects_for_this_type()
│    def natural_key()
⋮...

│contrib/contenttypes/prefetch.py:
⋮...
│class GenericPrefetch
│    def __init__()
│    def __getstate__()
│    def get_current_querysets()
⋮...

│contrib/contenttypes/views.py:
⋮...
│def shortcut()
⋮...

│contrib/flatpages/admin.py:
⋮...
│class FlatPageAdmin
⋮...

│contrib/flatpages/apps.py:
⋮...
│class FlatPagesConfig
⋮...

│contrib/flatpages/forms.py:
⋮...
│class FlatpageForm
│class Meta
│    def __init__()
│    def _trailing_slash_required()
│    def clean_url()
│    def clean()
⋮...

│contrib/flatpages/middleware.py:
⋮...
│class FlatpageFallbackMiddleware
│    def process_response()
⋮...

│contrib/flatpages/migrations/0001_initial.py:
⋮...
│class Migration
⋮...

│contrib/flatpages/models.py:
⋮...
│class FlatPage
│class Meta
│    def __str__()
│    def get_absolute_url()
⋮...

│contrib/flatpages/sitemaps.py:
⋮...
│class FlatPageSitemap
│    def items()
⋮...

│contrib/flatpages/templatetags/flatpages.py:
⋮...
│class FlatpageNode
│    def __init__()
│    def render()
│def get_flatpages()
⋮...

│contrib/flatpages/urls.py:
⋮...

│contrib/flatpages/views.py:
⋮...
│def flatpage()
│def render_flatpage()
⋮...

│contrib/gis/admin/__init__.py:
⋮...

│contrib/gis/admin/options.py:
⋮...
│class GeoModelAdminMixin
│    def formfield_for_dbfield()
│class GISModelAdmin
⋮...

│contrib/gis/apps.py:
⋮...
│class GISConfig
│    def ready()
⋮...

│contrib/gis/db/backends/base/adapter.py:
⋮...
│class WKTAdapter
│    def __init__()
│    def __eq__()
│    def __hash__()
│    def __str__()
│    def _fix_polygon()
⋮...

│contrib/gis/db/backends/base/features.py:
⋮...
│class BaseSpatialFeatures
│    def supports_bbcontains_lookup()
│    def supports_contained_lookup()
│    def supports_crosses_lookup()
│    def supports_distances_lookups()
│    def supports_dwithin_lookup()
│    def supports_relate_lookup()
│    def supports_isvalid_lookup()
│    def supports_collect_aggr()
│    def supports_extent_aggr()
│    def supports_make_line_aggr()
│    def supports_union_aggr()
│    def __getattr__()
⋮...

│contrib/gis/db/backends/base/models.py:
⋮...
│class SpatialRefSysMixin
│    def srs()
│    def ellipsoid()
│    def name()
│    def spheroid()
│    def datum()
│    def projected()
│    def local()
│    def geographic()
│    def linear_name()
│    def linear_units()
│    def angular_name()
│    def angular_units()
│    def units()
│    def get_units()
│    def get_spheroid()
│    def __str__()
⋮...

│contrib/gis/db/backends/base/operations.py:
⋮...
│class BaseSpatialOperations
│    def select_extent()
│    def convert_extent()
│    def convert_extent3d()
│    def geo_quote_name()
│    def geo_db_type()
│    def get_distance()
│    def get_geom_placeholder()
│    def transform_value()
│    def check_expression_support()
│    def spatial_aggregate_name()
│    def spatial_function_name()
│    def geometry_columns()
│    def spatial_ref_sys()
│    def get_db_converters()
│    def get_geometry_converter()
│    def get_area_att_for_field()
│    def get_distance_att_for_field()
⋮...

│contrib/gis/db/backends/mysql/base.py:
⋮...
│class DatabaseWrapper
⋮...

│contrib/gis/db/backends/mysql/features.py:
⋮...
│class DatabaseFeatures
│    def supports_geometry_field_unique_index()
⋮...

│contrib/gis/db/backends/mysql/introspection.py:
⋮...
│class MySQLIntrospection
│    def get_geometry_type()
│    def supports_spatial_index()
⋮...

│contrib/gis/db/backends/mysql/operations.py:
⋮...
│class MySQLOperations
│    def mariadb()
│    def mysql()
│    def select()
│    def from_text()
│    def collect()
│    def gis_operators()
│    def disallowed_aggregates()
│    def unsupported_functions()
│    def geo_db_type()
│    def get_distance()
│    def get_geometry_converter()
│    def converter()
│    def spatial_aggregate_name()
⋮...

│contrib/gis/db/backends/mysql/schema.py:
⋮...
│class MySQLGISSchemaEditor
│    def skip_default()
│    def quote_value()
│    def _field_indexes_sql()
│    def remove_field()
│    def _alter_field()
│    def _create_spatial_index_name()
│    def _create_spatial_index_sql()
│    def _delete_spatial_index_sql()
⋮...

│contrib/gis/db/backends/oracle/adapter.py:
⋮...
│class OracleSpatialAdapter
│    def __init__()
│    def _polygon_must_be_fixed()
│    def _fix_polygon()
│    def _fix_geometry_collection()
⋮...

│contrib/gis/db/backends/oracle/base.py:
⋮...
│class DatabaseWrapper
⋮...

│contrib/gis/db/backends/oracle/features.py:
⋮...
│class DatabaseFeatures
│    def django_test_skips()
⋮...

│contrib/gis/db/backends/oracle/introspection.py:
⋮...
│class OracleIntrospection
│    def data_types_reverse()
│    def get_geometry_type()
⋮...

│contrib/gis/db/backends/oracle/models.py:
⋮...
│class OracleGeometryColumns
│class Meta
│    def __str__()
│    def table_name_col()
│    def geom_col_name()
│class OracleSpatialRefSys
│class Meta
│    def wkt()
⋮...

│contrib/gis/db/backends/oracle/operations.py:
⋮...
│class SDOOperator
│class SDODWithin
│class SDODisjoint
│class SDORelate
│    def check_relate_argument()
│    def as_sql()
│class OracleOperations
│    def geo_quote_name()
│    def convert_extent()
│    def geo_db_type()
│    def get_distance()
│    def get_geom_placeholder()
│    def spatial_aggregate_name()
│    def geometry_columns()
│    def spatial_ref_sys()
│    def modify_insert_params()
│    def get_geometry_converter()
│    def converter()
│    def get_area_att_for_field()
⋮...

│contrib/gis/db/backends/oracle/schema.py:
⋮...
│class OracleGISSchemaEditor
│    def __init__()
│    def geo_quote_name()
│    def quote_value()
│    def _field_indexes_sql()
│    def column_sql()
│    def create_model()
│    def delete_model()
│    def add_field()
│    def remove_field()
│    def run_geometry_sql()
│    def _alter_field()
│    def _create_spatial_index_name()
│    def _create_spatial_index_sql()
│    def _delete_spatial_index_sql()
⋮...

│contrib/gis/db/backends/postgis/adapter.py:
⋮...
│class PostGISAdapter
│    def __init__()
│    def __conform__()
│    def __eq__()
│    def __hash__()
│    def __str__()
│    def _fix_polygon()
│    def getquoted()
⋮...

│contrib/gis/db/backends/postgis/base.py:
⋮...
│class GeometryType
│class GeographyType
│class RasterType
│class BaseTextDumper
│    def dump()
│class BaseBinaryDumper
│    def dump()
│    def postgis_adapters()
│class BaseDumper
│    def __init_subclass__()
│    def get_key()
│    def upgrade()
│    def dump()
│class PostGISTextDumper
│class PostGISBinaryDumper
│class DatabaseWrapper
│    def __init__()
│    def prepare_database()
│    def get_new_connection()
│    def _register_type()
│    def register_geometry_adapters()
⋮...

│contrib/gis/db/backends/postgis/const.py:
⋮...

│contrib/gis/db/backends/postgis/features.py:
⋮...
│class DatabaseFeatures
⋮...

│contrib/gis/db/backends/postgis/introspection.py:
⋮...
│class PostGISIntrospection
│    def get_field_type()
│    def get_geometry_type()
⋮...

│contrib/gis/db/backends/postgis/models.py:
⋮...
│class PostGISGeometryColumns
│class Meta
│    def __str__()
│    def table_name_col()
│    def geom_col_name()
│class PostGISSpatialRefSys
│class Meta
│    def wkt()
⋮...

│contrib/gis/db/backends/postgis/operations.py:
⋮...
│class PostGISOperator
│    def __init__()
│    def as_sql()
│    def check_raster()
│    def check_geography()
│class ST_Polygon
│    def __init__()
│    def output_field()
│class PostGISOperations
│    def function_names()
│    def spatial_version()
│    def convert_extent()
│    def convert_extent3d()
│    def geo_db_type()
│    def get_distance()
│    def get_geom_placeholder()
│    def _get_postgis_func()
│    def postgis_geos_version()
│    def postgis_lib_version()
│    def postgis_proj_version()
│    def postgis_version()
│    def postgis_full_version()
│    def postgis_version_tuple()
│    def proj_version_tuple()
│    def spatial_aggregate_name()
│    def geometry_columns()
│    def spatial_ref_sys()
│    def parse_raster()
│    def distance_expr_for_lookup()
│    def _normalize_distance_lookup_arg()
│    def get_geometry_converter()
│    def converter()
│    def get_area_att_for_field()
⋮...

│contrib/gis/db/backends/postgis/pgraster.py:
⋮...
│def pack()
│def unpack()
│def chunk()
│def from_pgraster()
│def to_pgraster()
⋮...

│contrib/gis/db/backends/postgis/schema.py:
⋮...
│class PostGISSchemaEditor
│    def geo_quote_name()
│    def _field_should_be_indexed()
│    def _create_index_sql()
│    def _alter_column_type_sql()
│    def _alter_field()
│    def _create_spatial_index_name()
│    def _create_spatial_index_sql()
│    def _delete_spatial_index_sql()
⋮...

│contrib/gis/db/backends/spatialite/adapter.py:
⋮...
│class SpatiaLiteAdapter
│    def __conform__()
⋮...

│contrib/gis/db/backends/spatialite/base.py:
⋮...
│class DatabaseWrapper
│    def __init__()
│    def get_new_connection()
│    def prepare_database()
⋮...

│contrib/gis/db/backends/spatialite/client.py:
⋮...
│class SpatiaLiteClient
⋮...

│contrib/gis/db/backends/spatialite/features.py:
⋮...
│class DatabaseFeatures
│    def supports_area_geodetic()
│    def django_test_skips()
⋮...

│contrib/gis/db/backends/spatialite/introspection.py:
⋮...
│class GeoFlexibleFieldLookupDict
│class SpatiaLiteIntrospection
│    def get_geometry_type()
│    def get_constraints()
⋮...

│contrib/gis/db/backends/spatialite/models.py:
⋮...
│class SpatialiteGeometryColumns
│class Meta
│    def __str__()
│    def table_name_col()
│    def geom_col_name()
│class SpatialiteSpatialRefSys
│class Meta
│    def wkt()
⋮...

│contrib/gis/db/backends/spatialite/operations.py:
⋮...
│class SpatialiteNullCheckOperator
│    def as_sql()
│class SpatiaLiteOperations
│    def unsupported_functions()
│    def spatial_version()
│    def convert_extent()
│    def geo_db_type()
│    def get_distance()
│    def _get_spatialite_func()
│    def geos_version()
│    def proj_version()
│    def lwgeom_version()
│    def rttopo_version()
│    def geom_lib_version()
│    def spatialite_version()
│    def spatialite_version_tuple()
│    def spatial_aggregate_name()
│    def geometry_columns()
│    def spatial_ref_sys()
│    def get_geometry_converter()
│    def converter()
⋮...

│contrib/gis/db/backends/spatialite/schema.py:
⋮...
│class SpatialiteSchemaEditor
│    def __init__()
│    def geo_quote_name()
│    def column_sql()
│    def remove_geometry_metadata()
│    def create_model()
│    def delete_model()
│    def add_field()
│    def remove_field()
│    def alter_db_table()
⋮...

│contrib/gis/db/backends/utils.py:
⋮...
│class SpatialOperator
│    def __init__()
│    def default_template()
│    def as_sql()
⋮...

│contrib/gis/db/models/__init__.py:
⋮...

│contrib/gis/db/models/aggregates.py:
⋮...
│class GeoAggregate
│    def output_field()
│    def as_sql()
│    def as_oracle()
│    def resolve_expression()
│class Collect
│class Extent
│    def __init__()
│    def convert_value()
│class Extent3D
│    def __init__()
│    def convert_value()
│class MakeLine
│class Union
⋮...

│contrib/gis/db/models/fields.py:
⋮...
│def get_srid_info()
│class BaseSpatialField
│    def __init__()
│    def deconstruct()
│    def db_type()
│    def spheroid()
│    def units()
│    def units_name()
│    def geodetic()
│    def get_placeholder()
│    def get_srid()
│    def get_db_prep_value()
│    def get_raster_prep_value()
│    def get_prep_value()
│class GeometryField
│    def __init__()
│    def deconstruct()
│    def contribute_to_class()
│    def formfield()
│    def select_format()
│class PointField
│class LineStringField
│class PolygonField
│class MultiPointField
│class MultiLineStringField
│class MultiPolygonField
│class GeometryCollectionField
│class ExtentField
│    def get_internal_type()
│    def select_format()
│class RasterField
│    def _check_connection()
│    def db_type()
│    def from_db_value()
│    def contribute_to_class()
│    def get_transform()
⋮...

│contrib/gis/db/models/functions.py:
⋮...
│class GeoFuncMixin
│    def __init__()
│    def name()
│    def geo_field()
│    def as_sql()
│    def resolve_expression()
│    def _handle_param()
│class GeoFunc
│class GeomOutputGeoFunc
│    def output_field()
│class SQLiteDecimalToFloatMixin
│    def as_sqlite()
│class OracleToleranceMixin
│    def as_oracle()
│class Area
│    def output_field()
│    def as_sql()
│    def as_sqlite()
│class Azimuth
│class AsGeoJSON
│    def __init__()
│    def as_oracle()
│class AsGML
│    def __init__()
│    def as_oracle()
│class AsKML
│    def __init__()
│class AsSVG
│    def __init__()
│class AsWKB
│class AsWKT
│class BoundingCircle
│    def __init__()
│    def as_oracle()
│    def as_sqlite()
│class Centroid
│class ClosestPoint
│class Difference
│class DistanceResultMixin
│    def output_field()
│    def source_is_geography()
│class Distance
│    def __init__()
│    def as_postgresql()
│    def as_sqlite()
│class Envelope
│class ForcePolygonCW
│class FromWKB
│    def __init__()
│    def as_oracle()
│class FromWKT
│class GeoHash
│    def __init__()
│    def as_mysql()
│class GeometryDistance
│class Intersection
│class IsEmpty
│class IsValid
│    def as_oracle()
│class Length
│    def __init__()
│    def as_sql()
│    def as_postgresql()
│    def as_sqlite()
│class LineLocatePoint
│class MakeValid
│class MemSize
│class NumGeometries
│class NumPoints
│class Perimeter
│    def as_postgresql()
│    def as_sqlite()
│class PointOnSurface
│class Reverse
│class Rotate
│    def __init__()
│class Scale
│    def __init__()
│class SnapToGrid
│    def __init__()
│class SymDifference
│class Transform
│    def __init__()
│class Translate
│    def as_sqlite()
│class Union
⋮...

│contrib/gis/db/models/lookups.py:
⋮...
│class RasterBandTransform
│    def as_sql()
│class GISLookup
│    def __init__()
│    def process_rhs_params()
│    def process_band_indices()
│    def get_db_prep_lookup()
│    def process_rhs()
│    def get_rhs_op()
│    def as_sql()
│class OverlapsLeftLookup
│class OverlapsRightLookup
│class OverlapsBelowLookup
│class OverlapsAboveLookup
│class LeftLookup
│class RightLookup
│class StrictlyBelowLookup
│class StrictlyAboveLookup
│class SameAsLookup
│class BBContainsLookup
│class BBOverlapsLookup
│class ContainedLookup
│class ContainsLookup
│class ContainsProperlyLookup
│class CoveredByLookup
│class CoversLookup
│class CrossesLookup
│class DisjointLookup
│class EqualsLookup
│class IntersectsLookup
│class OverlapsLookup
│class RelateLookup
│    def process_rhs()
│class TouchesLookup
│class WithinLookup
│class DistanceLookupBase
│    def process_rhs_params()
│    def process_distance()
│class DWithinLookup
│    def process_distance()
│    def process_rhs()
│class DistanceLookupFromFunction
│    def as_sql()
│class DistanceGTLookup
│class DistanceGTELookup
│class DistanceLTLookup
│class DistanceLTELookup
⋮...

│contrib/gis/db/models/proxy.py:
⋮...
│class SpatialProxy
│    def __init__()
│    def __get__()
│    def __set__()
⋮...

│contrib/gis/db/models/sql/__init__.py:
⋮...

│contrib/gis/db/models/sql/conversion.py:
⋮...
│class AreaField
│    def __init__()
│    def get_prep_value()
│    def get_db_prep_value()
│    def from_db_value()
│    def get_internal_type()
│class DistanceField
│    def __init__()
│    def get_prep_value()
│    def get_db_prep_value()
│    def from_db_value()
│    def get_internal_type()
⋮...

│contrib/gis/feeds.py:
⋮...
│class GeoFeedMixin
│    def georss_coords()
│    def add_georss_point()
│    def add_georss_element()
│class GeoRSSFeed
│    def rss_attributes()
│    def add_item_elements()
│    def add_root_elements()
│class GeoAtom1Feed
│    def root_attributes()
│    def add_item_elements()
│    def add_root_elements()
│class W3CGeoFeed
│    def rss_attributes()
│    def add_item_elements()
│    def add_root_elements()
│class Feed
│    def feed_extra_kwargs()
│    def item_extra_kwargs()
⋮...

│contrib/gis/forms/fields.py:
⋮...
│class GeometryField
│    def __init__()
│    def to_python()
│    def clean()
│    def has_changed()
│class GeometryCollectionField
│class PointField
│class MultiPointField
│class LineStringField
│class MultiLineStringField
│class PolygonField
│class MultiPolygonField
⋮...

│contrib/gis/forms/widgets.py:
⋮...
│class BaseGeometryWidget
│    def __init__()
│    def serialize()
│    def deserialize()
│    def get_context()
│class OpenLayersWidget
│class Media
│    def serialize()
│    def deserialize()
│class OSMWidget
│    def __init__()
⋮...

│contrib/gis/gdal/__init__.py:
⋮...

│contrib/gis/gdal/base.py:
⋮...
│class GDALBase
⋮...

│contrib/gis/gdal/datasource.py:
⋮...
│class DataSource
│    def __init__()
│    def __getitem__()
│    def __len__()
│    def __str__()
│    def layer_count()
│    def name()
⋮...

│contrib/gis/gdal/driver.py:
⋮...
│class Driver
│    def __init__()
│    def __str__()
│    def ensure_registered()
│    def driver_count()
│    def name()
⋮...

│contrib/gis/gdal/envelope.py:
⋮...
│class OGREnvelope
│class Envelope
│    def __init__()
│    def __eq__()
│    def __str__()
│    def _from_sequence()
│    def expand_to_include()
│    def min_x()
│    def min_y()
│    def max_x()
│    def max_y()
│    def ur()
│    def ll()
│    def tuple()
│    def wkt()
⋮...

│contrib/gis/gdal/error.py:
⋮...
│class GDALException
│class SRSException
│def check_err()
⋮...

│contrib/gis/gdal/feature.py:
⋮...
│class Feature
│    def __init__()
│    def __getitem__()
│    def __len__()
│    def __str__()
│    def __eq__()
│    def encoding()
│    def fid()
│    def layer_name()
│    def num_fields()
│    def fields()
│    def geom()
│    def geom_type()
│    def get()
│    def index()
⋮...

│contrib/gis/gdal/field.py:
⋮...
│class Field
│    def __init__()
│    def __str__()
│    def as_double()
│    def as_int()
│    def as_string()
│    def as_datetime()
│    def is_set()
│    def name()
│    def precision()
│    def type()
│    def type_name()
│    def value()
│    def width()
│class OFTInteger
│    def value()
│    def type()
│class OFTReal
│    def value()
│class OFTString
│class OFTWideString
│class OFTBinary
│class OFTDate
│    def value()
│class OFTDateTime
│    def value()
│class OFTTime
│    def value()
│class OFTInteger64
│class OFTIntegerList
│class OFTRealList
│class OFTStringList
│class OFTWideStringList
│class OFTInteger64List
⋮...

│contrib/gis/gdal/geometries.py:
⋮...
│class OGRGeometry
│    def __init__()
│    def __getstate__()
│    def __setstate__()
│    def _from_wkb()
│    def _from_json()
│    def from_bbox()
│    def from_json()
│    def from_gml()
│    def __or__()
│    def __and__()
│    def __sub__()
│    def __xor__()
│    def __eq__()
│    def __str__()
│    def dimension()
│    def coord_dim()
│    def geom_count()
│    def point_count()
│    def num_points()
│    def num_coords()
│    def geom_type()
│    def geom_name()
│    def area()
│    def envelope()
│    def empty()
│    def extent()
│    def is_3d()
│    def set_3d()
│    def is_measured()
│    def set_measured()
│    def has_curve()
│    def get_linear_geometry()
│    def get_curve_geometry()
│    def _get_srs()
│    def _set_srs()
│    def _get_srid()
│    def _set_srid()
│    def _geos_ptr()
│    def geos()
│    def gml()
│    def hex()
│    def json()
│    def kml()
│    def wkb_size()
│    def wkb()
│    def wkt()
│    def ewkt()
│    def clone()
│    def close_rings()
│    def transform()
│    def _topology()
│    def intersects()
│    def equals()
│    def disjoint()
│    def touches()
│    def crosses()
│    def within()
│    def contains()
│    def overlaps()
│    def _geomgen()
│    def boundary()
│    def convex_hull()
│    def difference()
│    def intersection()
│    def sym_difference()
│    def union()
│    def centroid()
│class Point
│    def _geos_ptr()
│    def _create_empty()
│    def x()
│    def y()
│    def z()
│    def m()
│    def tuple()
│class LineString
│    def __getitem__()
│    def __len__()
│    def tuple()
│    def _listarr()
│    def x()
│    def y()
│    def z()
│    def m()
│class LinearRing
│class Polygon
│    def __len__()
│    def __getitem__()
│    def shell()
│    def tuple()
│    def point_count()
│class CircularString
│class CurvePolygon
│class CompoundCurve
│class GeometryCollection
│    def __getitem__()
│    def __len__()
│    def add()
│    def point_count()
│    def tuple()
│class MultiPoint
│class MultiLineString
│class MultiPolygon
│class MultiSurface
│class MultiCurve
⋮...

│contrib/gis/gdal/geomtype.py:
⋮...
│class OGRGeomType
│    def __init__()
│    def __str__()
│    def __repr__()
│    def __eq__()
│    def name()
│    def django()
│    def to_multi()
⋮...

│contrib/gis/gdal/layer.py:
⋮...
│class Layer
│    def __init__()
│    def __getitem__()
│    def __iter__()
│    def __len__()
│    def __str__()
│    def _make_feature()
│    def extent()
│    def name()
│    def num_feat()
│    def num_fields()
│    def geom_type()
│    def srs()
│    def fields()
│    def field_types()
│    def field_widths()
│    def field_precisions()
│    def _get_spatial_filter()
│    def _set_spatial_filter()
│    def get_fields()
│    def get_geoms()
│    def test_capability()
⋮...

│contrib/gis/gdal/libgdal.py:
⋮...
│def std_call()
│def gdal_version()
│def gdal_full_version()
│def gdal_version_info()
│def err_handler()
│def function()
⋮...

│contrib/gis/gdal/prototypes/ds.py:
⋮...

│contrib/gis/gdal/prototypes/errcheck.py:
⋮...
│def arg_byref()
│def ptr_byref()
│def check_const_string()
│def check_string()
│def check_envelope()
│def check_geom()
│def check_geom_offset()
│def check_srs()
│def check_arg_errcode()
│def check_errcode()
│def check_pointer()
│def check_str_arg()
⋮...

│contrib/gis/gdal/prototypes/generation.py:
⋮...
│class gdal_char_p
│def bool_output()
│def double_output()
│def geom_output()
│def geomerrcheck()
│def int_output()
│def int64_output()
│def srs_output()
│def const_string_output()
│def _check_const()
│def string_output()
│def _check_str()
│def void_output()
│def voidptr_output()
│def chararray_output()
⋮...

│contrib/gis/gdal/prototypes/geom.py:
⋮...
│def env_func()
│def pnt_func()
│def topology_func()
⋮...

│contrib/gis/gdal/prototypes/raster.py:
⋮...

│contrib/gis/gdal/prototypes/srs.py:
⋮...
│def srs_double()
│def units_func()
⋮...

│contrib/gis/gdal/raster/band.py:
⋮...
│class GDALBand
│    def __init__()
│    def _flush()
│    def description()
│    def width()
│    def height()
│    def pixel_count()
│    def statistics()
│    def min()
│    def max()
│    def mean()
│    def std()
│    def nodata_value()
│    def nodata_value()
│    def datatype()
│    def color_interp()
│    def data()
│class BandList
│    def __init__()
│    def __iter__()
│    def __len__()
│    def __getitem__()
⋮...

│contrib/gis/gdal/raster/base.py:
⋮...
│class GDALRasterBase
│    def metadata()
│    def metadata()
⋮...

│contrib/gis/gdal/raster/const.py:
⋮...

│contrib/gis/gdal/raster/source.py:
⋮...
│class TransformPoint
│    def __init__()
│    def x()
│    def x()
│    def y()
│    def y()
│class GDALRaster
│    def __init__()
│    def __del__()
│    def __str__()
│    def __repr__()
│    def _flush()
│    def vsi_buffer()
│    def is_vsi_based()
│    def name()
│    def driver()
│    def width()
│    def height()
│    def srs()
│    def srs()
│    def srid()
│    def srid()
│    def geotransform()
│    def geotransform()
│    def origin()
│    def scale()
│    def skew()
│    def extent()
│    def bands()
│    def warp()
│    def clone()
│    def transform()
│    def info()
⋮...

│contrib/gis/gdal/srs.py:
⋮...
│class AxisOrder
│class SpatialReference
│    def __init__()
│    def __getitem__()
│    def __str__()
│    def attr_value()
│    def auth_name()
│    def auth_code()
│    def clone()
│    def from_esri()
│    def identify_epsg()
│    def to_esri()
│    def validate()
│    def name()
│    def srid()
│    def linear_name()
│    def linear_units()
│    def angular_name()
│    def angular_units()
│    def units()
│    def ellipsoid()
│    def semi_major()
│    def semi_minor()
│    def inverse_flattening()
│    def geographic()
│    def local()
│    def projected()
│    def import_epsg()
│    def import_proj()
│    def import_user_input()
│    def import_wkt()
│    def import_xml()
│    def wkt()
│    def pretty_wkt()
│    def proj()
│    def proj4()
│    def xml()
│class CoordTransform
│    def __init__()
│    def __str__()
⋮...

│contrib/gis/geoip2.py:
⋮...
│class GeoIP2Exception
│class GeoIP2
│    def __init__()
│    def __del__()
│    def __repr__()
│    def _metadata()
│    def is_city()
│    def is_country()
│    def _query()
│    def city()
│    def country_code()
│    def country_name()
│    def country()
│    def lon_lat()
│    def lat_lon()
│    def geos()
⋮...

│contrib/gis/geometry.py:
⋮...

│contrib/gis/geos/base.py:
⋮...
│class GEOSBase
⋮...

│contrib/gis/geos/collections.py:
⋮...
│class GeometryCollection
│    def __init__()
│    def __iter__()
│    def __len__()
│    def _create_collection()
│    def _get_single_internal()
│    def _get_single_external()
│    def _set_list()
│    def kml()
│    def tuple()
│class MultiPoint
│class MultiLineString
│class MultiPolygon
⋮...

│contrib/gis/geos/coordseq.py:
⋮...
│class GEOSCoordSeq
│    def __init__()
│    def __iter__()
│    def __len__()
│    def __str__()
│    def __getitem__()
│    def __setitem__()
│    def _checkindex()
│    def _checkdim()
│    def _get_x()
│    def _get_y()
│    def _get_z()
│    def _set_x()
│    def _set_y()
│    def _set_z()
│    def _point_getter()
│    def _get_point_2d()
│    def _get_point_3d()
│    def _set_point_2d()
│    def _set_point_3d()
│    def getOrdinate()
│    def setOrdinate()
│    def getX()
│    def setX()
│    def getY()
│    def setY()
│    def getZ()
│    def setZ()
│    def size()
│    def dims()
│    def hasz()
│    def clone()
│    def kml()
│    def tuple()
│    def is_counterclockwise()
⋮...

│contrib/gis/geos/error.py:
⋮...
│class GEOSException
⋮...

│contrib/gis/geos/factory.py:
⋮...
│def fromfile()
│def fromstr()
⋮...

│contrib/gis/geos/geometry.py:
⋮...
│class GEOSGeometryBase
│    def __init__()
│    def _post_init()
│    def __copy__()
│    def __deepcopy__()
│    def __str__()
│    def __repr__()
│    def _to_pickle_wkb()
│    def _from_pickle_wkb()
│    def __getstate__()
│    def __setstate__()
│    def _from_wkb()
│    def from_ewkt()
│    def _from_wkt()
│    def from_gml()
│    def __eq__()
│    def __hash__()
│    def __or__()
│    def __and__()
│    def __sub__()
│    def __xor__()
│    def coord_seq()
│    def geom_type()
│    def geom_typeid()
│    def num_geom()
│    def num_coords()
│    def num_points()
│    def dims()
│    def normalize()
│    def make_valid()
│    def empty()
│    def hasz()
│    def hasm()
│    def ring()
│    def simple()
│    def valid()
│    def valid_reason()
│    def contains()
│    def covers()
│    def crosses()
│    def disjoint()
│    def equals()
│    def equals_exact()
│    def equals_identical()
│    def intersects()
│    def overlaps()
│    def relate_pattern()
│    def touches()
│    def within()
│    def srid()
│    def srid()
│    def ewkt()
│    def wkt()
│    def hex()
│    def hexewkb()
│    def json()
│    def wkb()
│    def ewkb()
│    def kml()
│    def prepared()
│    def _ogr_ptr()
│    def ogr()
│    def srs()
│    def crs()
│    def transform()
│    def _topology()
│    def boundary()
│    def buffer()
│    def buffer_with_style()
│    def centroid()
│    def convex_hull()
│    def difference()
│    def envelope()
│    def intersection()
│    def point_on_surface()
│    def relate()
│    def simplify()
│    def sym_difference()
│    def unary_union()
│    def union()
│    def area()
│    def distance()
│    def extent()
│    def length()
│    def clone()
│class LinearGeometryMixin
│    def interpolate()
│    def interpolate_normalized()
│    def project()
│    def project_normalized()
│    def merged()
│    def closed()
│class GEOSGeometry
│    def __init__()
⋮...

│contrib/gis/geos/io.py:
⋮...
│class WKBReader
│    def read()
│class WKTReader
│    def read()
⋮...

│contrib/gis/geos/libgeos.py:
⋮...
│def load_geos()
│def notice_h()
│def error_h()
│class GEOSGeom_t
│class GEOSPrepGeom_t
│class GEOSCoordSeq_t
│class GEOSContextHandle_t
│class GEOSFuncFactory
│    def __init__()
│    def __call__()
│    def func()
│def geos_version()
│def geos_version_tuple()
⋮...

│contrib/gis/geos/linestring.py:
⋮...
│class LineString
│    def __init__()
│    def __iter__()
│    def __len__()
│    def _get_single_external()
│    def _set_list()
│    def _set_single()
│    def _checkdim()
│    def tuple()
│    def _listarr()
│    def array()
│    def x()
│    def y()
│    def z()
│class LinearRing
│    def is_counterclockwise()
⋮...

│contrib/gis/geos/mutable_list.py:
⋮...
│class ListMixin
│    def __init__()
│    def __getitem__()
│    def __delitem__()
│    def __setitem__()
│    def __add__()
│    def __radd__()
│    def __iadd__()
│    def __mul__()
│    def __rmul__()
│    def __imul__()
│    def __eq__()
│    def __lt__()
│    def count()
│    def index()
│    def append()
│    def extend()
│    def insert()
│    def pop()
│    def remove()
│    def reverse()
│    def sort()
│    def _rebuild()
│    def _set_single_rebuild()
│    def _checkindex()
│    def _check_allowed()
│    def _set_slice()
│    def _assign_extended_slice_rebuild()
│    def newItems()
│    def _assign_extended_slice()
│    def _assign_simple_slice()
│    def newItems()
⋮...

│contrib/gis/geos/point.py:
⋮...
│class Point
│    def __init__()
│    def _to_pickle_wkb()
│    def _from_pickle_wkb()
│    def _ogr_ptr()
│    def _create_empty()
│    def _create_point()
│    def _set_list()
│    def _set_single()
│    def __iter__()
│    def __len__()
│    def _get_single_external()
│    def x()
│    def x()
│    def y()
│    def y()
│    def z()
│    def z()
│    def tuple()
│    def tuple()
⋮...

│contrib/gis/geos/polygon.py:
⋮...
│class Polygon
│    def __init__()
│    def __iter__()
│    def __len__()
│    def from_bbox()
│    def _create_polygon()
│    def _clone()
│    def _construct_ring()
│    def _set_list()
│    def _get_single_internal()
│    def _get_single_external()
│    def num_interior_rings()
│    def _get_ext_ring()
│    def _set_ext_ring()
│    def tuple()
│    def kml()
⋮...

│contrib/gis/geos/prepared.py:
⋮...
│class PreparedGeometry
│    def __init__()
│    def contains()
│    def contains_properly()
│    def covers()
│    def intersects()
│    def crosses()
│    def disjoint()
│    def overlaps()
│    def touches()
│    def within()
⋮...

│contrib/gis/geos/prototypes/coordseq.py:
⋮...
│def check_cs_op()
│def check_cs_get()
│class CsInt
│class CsOperation
│    def __init__()
│class CsOutput
│    def errcheck()
⋮...

│contrib/gis/geos/prototypes/errcheck.py:
⋮...
│def last_arg_byref()
│def check_dbl()
│def check_geom()
│def check_minus_one()
│def check_predicate()
│def check_sized_string()
│def check_string()
⋮...

│contrib/gis/geos/prototypes/geom.py:
⋮...
│class geos_char_p
│class GeomOutput
│class IntFromGeom
│class StringFromGeom
⋮...

│contrib/gis/geos/prototypes/io.py:
⋮...
│class WKTReader_st
│class WKTWriter_st
│class WKBReader_st
│class WKBWriter_st
│class WKBReadFunc
│class WKBWriteFunc
│class WKBWriterGet
│class WKBWriterSet
│class IOBase
│    def __init__()
│class _WKTReader
│    def read()
│class _WKBReader
│    def read()
│def default_trim_value()
│class WKTWriter
│    def __init__()
│    def write()
│    def outdim()
│    def outdim()
│    def trim()
│    def trim()
│    def precision()
│    def precision()
│class WKBWriter
│    def __init__()
│    def _handle_empty_point()
│    def write()
│    def write_hex()
│    def _get_byteorder()
│    def _set_byteorder()
│    def outdim()
│    def outdim()
│    def srid()
│    def srid()
│class ThreadLocalIO
│def wkt_r()
│def wkt_w()
│def wkb_r()
│def wkb_w()
│def ewkb_w()
⋮...

│contrib/gis/geos/prototypes/misc.py:
⋮...
│class DblFromGeom
⋮...

│contrib/gis/geos/prototypes/predicates.py:
⋮...
│class UnaryPredicate
│class BinaryPredicate
⋮...

│contrib/gis/geos/prototypes/prepared.py:
⋮...
│class PreparedPredicate
⋮...

│contrib/gis/geos/prototypes/threadsafe.py:
⋮...
│class GEOSContextHandle
│    def __init__()
│class GEOSContext
│class GEOSFunc
│    def __init__()
│    def __call__()
│    def __str__()
│    def _get_argtypes()
│    def _set_argtypes()
│    def _get_restype()
│    def _set_restype()
│    def _get_errcheck()
│    def _set_errcheck()
⋮...

│contrib/gis/geos/prototypes/topology.py:
⋮...
│class Topology
⋮...

│contrib/gis/management/commands/inspectdb.py:
⋮...
│class Command
│    def get_field_type()
⋮...

│contrib/gis/management/commands/ogrinspect.py:
⋮...
│class LayerOptionAction
│    def __call__()
│class ListOptionAction
│    def __call__()
│class Command
│    def add_arguments()
│    def handle()
⋮...

│contrib/gis/measure.py:
⋮...
│def pretty_name()
│class MeasureBase
│    def __init__()
│    def _get_standard()
│    def _set_standard()
│    def __getattr__()
│    def __repr__()
│    def __str__()
│    def __eq__()
│    def __hash__()
│    def __lt__()
│    def __add__()
│    def __iadd__()
│    def __sub__()
│    def __isub__()
│    def __mul__()
│    def __imul__()
│    def __rmul__()
│    def __truediv__()
│    def __itruediv__()
│    def __bool__()
│    def default_units()
│    def unit_attname()
│class Distance
│    def __mul__()
│class Area
│    def __truediv__()
⋮...

│contrib/gis/ptr.py:
⋮...
│class CPointerBase
│    def ptr()
│    def ptr()
│    def __del__()
⋮...

│contrib/gis/serializers/geojson.py:
⋮...
│class Serializer
│    def _init_options()
│    def start_serialization()
│    def end_serialization()
│    def start_object()
│    def get_dump_object()
│    def handle_field()
│class Deserializer
│    def __init__()
⋮...

│contrib/gis/shortcuts.py:
⋮...
│def compress_kml()
│def render_to_kml()
│def render_to_kmz()
⋮...

│contrib/gis/sitemaps/__init__.py:
⋮...

│contrib/gis/sitemaps/kml.py:
⋮...
│class KMLSitemap
│    def __init__()
│    def _build_kml_sources()
│    def get_urls()
│    def items()
│    def location()
│class KMZSitemap
⋮...

│contrib/gis/sitemaps/views.py:
⋮...
│def kml()
│def kmz()
⋮...

│contrib/gis/static/gis/css/ol3.css:
⋮...

│contrib/gis/static/gis/js/OLMapWidget.js:
⋮...
│class GeometryTypeControl extends ol.control.Control {
│class MapWidget {
⋮...

│contrib/gis/templates/gis/openlayers-osm.html:
⋮...

│contrib/gis/templates/gis/openlayers.html:
⋮...

│contrib/gis/utils/__init__.py:
⋮...

│contrib/gis/utils/layermapping.py:
⋮...
│class LayerMapError
│class InvalidString
│class InvalidDecimal
│class InvalidInteger
│class MissingForeignKey
│class LayerMapping
│    def __init__()
│    def check_fid_range()
│    def check_layer()
│    def check_ogr_fld()
│    def check_srs()
│    def check_unique()
│    def feature_kwargs()
│    def unique_kwargs()
│    def verify_ogr_field()
│    def verify_fk()
│    def verify_geom()
│    def coord_transform()
│    def geometry_field()
│    def make_multi()
│    def save()
│    def _save()
⋮...

│contrib/gis/utils/ogrinfo.py:
⋮...
│def ogrinfo()
⋮...

│contrib/gis/utils/ogrinspect.py:
⋮...
│def mapping()
│def ogrinspect()
│def _ogrinspect()
│def process_kwarg()
│def get_kwargs_str()
⋮...

│contrib/gis/utils/srs.py:
⋮...
│def add_srs_entry()
⋮...

│contrib/gis/views.py:
⋮...
│def feed()
⋮...

│contrib/humanize/apps.py:
⋮...
│class HumanizeConfig
⋮...

│contrib/humanize/templatetags/humanize.py:
⋮...
│def ordinal()
│def intcomma()
│def intword()
│def apnumber()
│def naturalday()
│def naturaltime()
│class NaturalTimeFormatter
│    def string_for()
⋮...

│contrib/messages/api.py:
⋮...
│class MessageFailure
│def add_message()
│def get_messages()
│def get_level()
│def set_level()
│def debug()
│def info()
│def success()
│def warning()
│def error()
⋮...

│contrib/messages/apps.py:
⋮...
│def update_level_tags()
│class MessagesConfig
│    def ready()
⋮...

│contrib/messages/constants.py:
⋮...

│contrib/messages/context_processors.py:
⋮...
│def messages()
⋮...

│contrib/messages/middleware.py:
⋮...
│class MessageMiddleware
│    def process_request()
│    def process_response()
⋮...

│contrib/messages/storage/__init__.py:
⋮...
│def default_storage()
⋮...

│contrib/messages/storage/base.py:
⋮...
│class Message
│    def __init__()
│    def _prepare()
│    def __eq__()
│    def __str__()
│    def __repr__()
│    def tags()
│    def level_tag()
│class BaseStorage
│    def __init__()
│    def __len__()
│    def __iter__()
│    def __contains__()
│    def __repr__()
│    def _loaded_messages()
│    def _get()
│    def _store()
│    def _prepare_messages()
│    def update()
│    def add()
│    def _get_level()
│    def _set_level()
⋮...

│contrib/messages/storage/cookie.py:
⋮...
│class MessageEncoder
│    def default()
│class MessageDecoder
│    def process_messages()
│    def decode()
│class MessagePartSerializer
│    def dumps()
│class MessagePartGatherSerializer
│    def dumps()
│class MessageSerializer
│    def loads()
│class CookieStorage
│    def __init__()
│    def _get()
│    def _update_cookie()
│    def _store()
│    def is_too_large_for_cookie()
│    def compute_msg()
│    def _encode_parts()
│    def _encode()
│    def _decode()
│def bisect_keep_left()
│def bisect_keep_right()
⋮...

│contrib/messages/storage/fallback.py:
⋮...
│class FallbackStorage
│    def __init__()
│    def _get()
│    def _store()
⋮...

│contrib/messages/storage/session.py:
⋮...
│class SessionStorage
│    def __init__()
│    def _get()
│    def _store()
│    def serialize_messages()
│    def deserialize_messages()
⋮...

│contrib/messages/test.py:
⋮...
│class MessagesTestMixin
│    def assertMessages()
⋮...

│contrib/messages/utils.py:
⋮...
│def get_level_tags()
⋮...

│contrib/messages/views.py:
⋮...
│class SuccessMessageMixin
│    def form_valid()
│    def get_success_message()
⋮...

│contrib/postgres/aggregates/general.py:
⋮...
│class ArrayAgg
│    def output_field()
│class BitAnd
│class BitOr
│class BitXor
│class BoolAnd
│class BoolOr
│class JSONBAgg
│class StringAgg
│    def __init__()
⋮...

│contrib/postgres/aggregates/mixins.py:
⋮...
│class _DeprecatedOrdering
│    def __init__()
│class OrderableAggMixin
│    def __init_subclass__()
⋮...

│contrib/postgres/aggregates/statistics.py:
⋮...
│class StatAggregate
│    def __init__()
│class Corr
│class CovarPop
│    def __init__()
│class RegrAvgX
│class RegrAvgY
│class RegrCount
│class RegrIntercept
│class RegrR2
│class RegrSlope
│class RegrSXX
│class RegrSXY
│class RegrSYY
⋮...

│contrib/postgres/apps.py:
⋮...
│def uninstall_if_needed()
│class PostgresConfig
│    def ready()
⋮...

│contrib/postgres/constraints.py:
⋮...
│class ExclusionConstraintExpression
│class ExclusionConstraint
│    def __init__()
│    def _get_expressions()
│    def check()
│    def _get_condition_sql()
│    def constraint_sql()
│    def create_sql()
│    def remove_sql()
│    def deconstruct()
│    def __eq__()
│    def __repr__()
│    def validate()
⋮...

│contrib/postgres/expressions.py:
⋮...
│class ArraySubquery
│    def __init__()
│    def output_field()
⋮...

│contrib/postgres/fields/array.py:
⋮...
│class ArrayField
│    def __init__()
│    def model()
│    def model()
│    def _choices_is_value()
│    def check()
│    def set_attributes_from_name()
│    def description()
│    def db_type()
│    def cast_db_type()
│    def db_parameters()
│    def get_placeholder()
│    def get_db_prep_value()
│    def deconstruct()
│    def to_python()
│    def _from_db_value()
│    def value_to_string()
│    def get_transform()
│    def validate()
│    def run_validators()
│    def formfield()
│    def slice_expression()
│class ArrayRHSMixin
│    def __init__()
│    def process_rhs()
│    def _rhs_not_none_values()
│class ArrayContains
│class ArrayContainedBy
│class ArrayExact
│class ArrayOverlap
│class ArrayLenTransform
│    def as_sql()
│class ArrayInLookup
│    def get_prep_lookup()
│class IndexTransform
│    def __init__()
│    def as_sql()
│    def output_field()
│class IndexTransformFactory
│    def __init__()
│    def __call__()
│class SliceTransform
│    def __init__()
│    def as_sql()
│class SliceTransformFactory
│    def __init__()
│    def __call__()
⋮...

│contrib/postgres/fields/citext.py:
⋮...
│class CICharField
│class CIEmailField
│class CITextField
⋮...

│contrib/postgres/fields/hstore.py:
⋮...
│class HStoreField
│    def db_type()
│    def get_transform()
│    def validate()
│    def to_python()
│    def value_to_string()
│    def formfield()
│    def get_prep_value()
│class KeyTransform
│    def __init__()
│    def as_sql()
│class KeyTransformFactory
│    def __init__()
│    def __call__()
│class KeysTransform
│class ValuesTransform
⋮...

│contrib/postgres/fields/jsonb.py:
⋮...
│class JSONField
⋮...

│contrib/postgres/fields/ranges.py:
⋮...
│class RangeBoundary
│    def __init__()
│    def as_sql()
│class RangeOperators
│class RangeField
│    def __init__()
│    def model()
│    def model()
│    def _choices_is_value()
│    def get_placeholder()
│    def get_prep_value()
│    def to_python()
│    def set_attributes_from_name()
│    def value_to_string()
│    def formfield()
│class ContinuousRangeField
│    def __init__()
│    def get_prep_value()
│    def formfield()
│    def deconstruct()
│class IntegerRangeField
│    def db_type()
│class BigIntegerRangeField
│    def db_type()
│class DecimalRangeField
│    def db_type()
│class DateTimeRangeField
│    def db_type()
│class DateRangeField
│    def db_type()
│class RangeContains
│    def get_prep_lookup()
│class DateTimeRangeContains
│    def process_rhs()
│    def as_postgresql()
│class RangeContainedBy
│    def process_rhs()
│    def process_lhs()
│    def get_prep_lookup()
│class FullyLessThan
│class FullGreaterThan
│class NotLessThan
│class NotGreaterThan
│class AdjacentToLookup
│class RangeStartsWith
│    def output_field()
│class RangeEndsWith
│    def output_field()
│class IsEmpty
│class LowerInclusive
│class LowerInfinite
│class UpperInclusive
│class UpperInfinite
⋮...

│contrib/postgres/fields/utils.py:
⋮...
│class AttributeSetter
│    def __init__()
⋮...

│contrib/postgres/forms/array.py:
⋮...
│class SimpleArrayField
│    def __init__()
│    def clean()
│    def prepare_value()
│    def to_python()
│    def validate()
│    def run_validators()
│    def has_changed()
│class SplitArrayWidget
│    def __init__()
│    def is_hidden()
│    def value_from_datadict()
│    def value_omitted_from_data()
│    def id_for_label()
│    def get_context()
│    def media()
│    def __deepcopy__()
│    def needs_multipart_form()
│class SplitArrayField
│    def __init__()
│    def _remove_trailing_nulls()
│    def to_python()
│    def clean()
│    def has_changed()
⋮...

│contrib/postgres/forms/hstore.py:
⋮...
│class HStoreField
│    def prepare_value()
│    def to_python()
│    def has_changed()
⋮...

│contrib/postgres/forms/ranges.py:
⋮...
│class RangeWidget
│    def __init__()
│    def decompress()
│class HiddenRangeWidget
│    def __init__()
│class BaseRangeField
│    def __init__()
│    def prepare_value()
│    def compress()
│class IntegerRangeField
│class DecimalRangeField
│class DateTimeRangeField
│class DateRangeField
⋮...

│contrib/postgres/functions.py:
⋮...
│class RandomUUID
│class TransactionNow
⋮...

│contrib/postgres/indexes.py:
⋮...
│class PostgresIndex
│    def max_name_length()
│    def create_sql()
│    def check_supported()
│    def get_with_params()
│class BloomIndex
│    def __init__()
│    def deconstruct()
│    def get_with_params()
│class BrinIndex
│    def __init__()
│    def deconstruct()
│    def get_with_params()
│class BTreeIndex
│    def __init__()
│    def deconstruct()
│    def get_with_params()
│class GinIndex
│    def __init__()
│    def deconstruct()
│    def get_with_params()
│class GistIndex
│    def __init__()
│    def deconstruct()
│    def get_with_params()
│class HashIndex
│    def __init__()
│    def deconstruct()
│    def get_with_params()
│class SpGistIndex
│    def __init__()
│    def deconstruct()
│    def get_with_params()
│class OpClass
│    def __init__()
⋮...

│contrib/postgres/lookups.py:
⋮...
│class DataContains
│class ContainedBy
│class Overlap
│    def get_prep_lookup()
│class HasKey
│class HasKeys
│    def get_prep_lookup()
│class HasAnyKeys
│class Unaccent
│class SearchLookup
│    def process_lhs()
│class TrigramSimilar
│class TrigramWordSimilar
│class TrigramStrictWordSimilar
⋮...

│contrib/postgres/operations.py:
⋮...
│class CreateExtension
│    def __init__()
│    def state_forwards()
│    def database_forwards()
│    def database_backwards()
│    def extension_exists()
│    def describe()
│    def migration_name_fragment()
│class BloomExtension
│    def __init__()
│class BtreeGinExtension
│    def __init__()
│class BtreeGistExtension
│    def __init__()
│class CITextExtension
│    def __init__()
│class CryptoExtension
│    def __init__()
│class HStoreExtension
│    def __init__()
│class TrigramExtension
│    def __init__()
│class UnaccentExtension
│    def __init__()
│class NotInTransactionMixin
│    def _ensure_not_in_transaction()
│class AddIndexConcurrently
│    def describe()
│    def database_forwards()
│    def database_backwards()
│class RemoveIndexConcurrently
│    def describe()
│    def database_forwards()
│    def database_backwards()
│class CollationOperation
│    def __init__()
│    def state_forwards()
│    def deconstruct()
│    def create_collation()
│    def remove_collation()
│class CreateCollation
│    def database_forwards()
│    def database_backwards()
│    def describe()
│    def migration_name_fragment()
│    def reduce()
│class RemoveCollation
│    def database_forwards()
│    def database_backwards()
│    def describe()
│    def migration_name_fragment()
│class AddConstraintNotValid
│    def __init__()
│    def describe()
│    def database_forwards()
│    def migration_name_fragment()
│class ValidateConstraint
│    def __init__()
│    def describe()
│    def database_forwards()
│    def database_backwards()
│    def state_forwards()
│    def migration_name_fragment()
│    def deconstruct()
⋮...

│contrib/postgres/search.py:
⋮...
│class SearchVectorExact
│    def process_rhs()
│    def as_sql()
│class SearchVectorField
│    def db_type()
│class SearchQueryField
│    def db_type()
│class _Float4Field
│    def db_type()
│class SearchConfig
│    def __init__()
│    def from_parameter()
│    def get_source_expressions()
│    def set_source_expressions()
│    def as_sql()
│class SearchVectorCombinable
│    def _combine()
│class SearchVector
│    def __init__()
│    def resolve_expression()
│    def as_sql()
│class CombinedSearchVector
│    def __init__()
│class SearchQueryCombinable
│    def _combine()
│    def __or__()
│    def __ror__()
│    def __and__()
│    def __rand__()
│class SearchQuery
│    def __init__()
│    def as_sql()
│    def __invert__()
│    def __str__()
│class CombinedSearchQuery
│    def __init__()
│    def __str__()
│class SearchRank
│    def __init__()
│class SearchHeadline
│    def __init__()
│    def as_sql()
│class TrigramBase
│    def __init__()
│class TrigramWordBase
│    def __init__()
│class TrigramSimilarity
│class TrigramDistance
│class TrigramWordDistance
│class TrigramStrictWordDistance
│class TrigramWordSimilarity
│class TrigramStrictWordSimilarity
⋮...

│contrib/postgres/serializers.py:
⋮...
│class RangeSerializer
│    def serialize()
⋮...

│contrib/postgres/signals.py:
⋮...
│def get_type_oids()
│def get_hstore_oids()
│def get_citext_oids()
│def register_type_handlers()
│def register_type_handlers()
⋮...

│contrib/postgres/utils.py:
⋮...
│def prefix_validation_error()
⋮...

│contrib/postgres/validators.py:
⋮...
│class ArrayMaxLengthValidator
│class ArrayMinLengthValidator
│class KeysValidator
│    def __init__()
│    def __call__()
│    def __eq__()
│class RangeMaxValueValidator
│    def compare()
│class RangeMinValueValidator
│    def compare()
⋮...

│contrib/redirects/admin.py:
⋮...
│class RedirectAdmin
⋮...

│contrib/redirects/apps.py:
⋮...
│class RedirectsConfig
⋮...

│contrib/redirects/middleware.py:
⋮...
│class RedirectFallbackMiddleware
│    def __init__()
│    def process_response()
⋮...

│contrib/redirects/migrations/0001_initial.py:
⋮...
│class Migration
⋮...

│contrib/redirects/migrations/0002_alter_redirect_new_path_help_text.py:
⋮...
│class Migration
⋮...

│contrib/redirects/models.py:
⋮...
│class Redirect
│class Meta
│    def __str__()
⋮...

│contrib/sessions/apps.py:
⋮...
│class SessionsConfig
⋮...

│contrib/sessions/backends/base.py:
⋮...
│class CreateError
│class UpdateError
│class SessionBase
│    def __init__()
│    def __contains__()
│    def __getitem__()
│    def __setitem__()
│def async aset()
│def __delitem__()
│def key_salt()
│def get()
│def async aget()
│def pop()
│def async apop()
│def setdefault()
│def async asetdefault()
│def set_test_cookie()
│def async aset_test_cookie()
│def test_cookie_worked()
│def async atest_cookie_worked()
│def delete_test_cookie()
│def async adelete_test_cookie()
│def encode()
│def decode()
│def update()
│def async aupdate()
│def has_key()
│def async ahas_key()
│def keys()
│def async akeys()
│def values()
│def async avalues()
│def items()
│def async aitems()
│def clear()
│def is_empty()
│def _get_new_session_key()
│def async _aget_new_session_key()
│def _get_or_create_session_key()
│def async _aget_or_create_session_key()
│def _validate_session_key()
│def _get_session_key()
│def _set_session_key()
│def _get_session()
│def async _aget_session()
│def get_session_cookie_age()
│def get_expiry_age()
│def async aget_expiry_age()
│def get_expiry_date()
│def async aget_expiry_date()
│def set_expiry()
│def async aset_expiry()
│def get_expire_at_browser_close()
│def async aget_expire_at_browser_close()
│def flush()
│def async aflush()
│def cycle_key()
│def async acycle_key()
│def exists()
│def async aexists()
│def create()
│def async acreate()
│def save()
│def async asave()
│def delete()
│def async adelete()
│def load()
│def async aload()
│def clear_expired()
│def async aclear_expired()
⋮...

│contrib/sessions/backends/cache.py:
⋮...
│class SessionStore
│    def __init__()
│    def cache_key()
│def async acache_key()
│def load()
│def async aload()
│def create()
│def async acreate()
│def save()
│def async asave()
│def exists()
│def async aexists()
│def delete()
│def async adelete()
│def clear_expired()
│def async aclear_expired()
⋮...

│contrib/sessions/backends/cached_db.py:
⋮...
│class SessionStore
│    def __init__()
│    def cache_key()
│def async acache_key()
│def load()
│def async aload()
│def exists()
│def async aexists()
│def save()
│def async asave()
│def delete()
│def async adelete()
│def flush()
│def async aflush()
⋮...

│contrib/sessions/backends/db.py:
⋮...
│class SessionStore
│    def __init__()
│    def get_model_class()
│    def model()
│    def _get_session_from_db()
│def async _aget_session_from_db()
│def load()
│def async aload()
│def exists()
│def async aexists()
│def create()
│def async acreate()
│def create_model_instance()
│def async acreate_model_instance()
│def save()
│def async asave()
│def sync_transaction()
│def delete()
│def async adelete()
│def clear_expired()
│def async aclear_expired()
⋮...

│contrib/sessions/backends/file.py:
⋮...
│class SessionStore
│    def __init__()
│    def _get_storage_path()
│    def _key_to_file()
│    def _last_modification()
│    def _expiry_date()
│    def load()
│def async aload()
│def create()
│def async acreate()
│def save()
│def async asave()
│def exists()
│def async aexists()
│def delete()
│def async adelete()
│def clear_expired()
│def async aclear_expired()
⋮...

│contrib/sessions/backends/signed_cookies.py:
⋮...
│class SessionStore
│    def load()
│def async aload()
│def create()
│def async acreate()
│def save()
│def async asave()
│def exists()
│def async aexists()
│def delete()
│def async adelete()
│def cycle_key()
│def async acycle_key()
│def _get_session_key()
│def clear_expired()
│def async aclear_expired()
⋮...

│contrib/sessions/base_session.py:
⋮...
│class BaseSessionManager
│    def encode()
│    def save()
│class AbstractBaseSession
│class Meta
│    def __str__()
│    def get_session_store_class()
│    def get_decoded()
⋮...

│contrib/sessions/exceptions.py:
⋮...
│class InvalidSessionKey
│class SuspiciousSession
│class SessionInterrupted
⋮...

│contrib/sessions/management/commands/clearsessions.py:
⋮...
│class Command
│    def handle()
⋮...

│contrib/sessions/middleware.py:
⋮...
│class SessionMiddleware
│    def __init__()
│    def process_request()
│    def process_response()
⋮...

│contrib/sessions/migrations/0001_initial.py:
⋮...
│class Migration
⋮...

│contrib/sessions/models.py:
⋮...
│class SessionManager
│class Session
│    def get_session_store_class()
│class Meta
⋮...

│contrib/sessions/serializers.py:
⋮...

│contrib/sitemaps/__init__.py:
⋮...
│class Sitemap
│    def _get()
│    def get_languages_for_item()
│    def _languages()
│    def _items()
│    def _location()
│    def paginator()
│    def items()
│    def location()
│    def get_protocol()
│    def get_domain()
│    def get_urls()
│    def get_latest_lastmod()
│    def _urls()
│class GenericSitemap
│    def __init__()
│    def items()
│    def lastmod()
│    def get_latest_lastmod()
⋮...

│contrib/sitemaps/apps.py:
⋮...
│class SiteMapsConfig
⋮...

│contrib/sitemaps/views.py:
⋮...
│class SitemapIndexItem
│def x_robots_tag()
│def inner()
│def _get_latest_lastmod()
│def index()
│def sitemap()
⋮...

│contrib/sites/admin.py:
⋮...
│class SiteAdmin
⋮...

│contrib/sites/apps.py:
⋮...
│class SitesConfig
│    def ready()
⋮...

│contrib/sites/checks.py:
⋮...
│def check_site_id()
⋮...

│contrib/sites/management.py:
⋮...
│def create_default_site()
⋮...

│contrib/sites/managers.py:
⋮...
│class CurrentSiteManager
│    def __init__()
│    def check()
│    def _check_field_name()
│    def _get_field_name()
│    def get_queryset()
⋮...

│contrib/sites/middleware.py:
⋮...
│class CurrentSiteMiddleware
│    def process_request()
⋮...

│contrib/sites/migrations/0001_initial.py:
⋮...
│class Migration
⋮...

│contrib/sites/migrations/0002_alter_domain_unique.py:
⋮...
│class Migration
⋮...

│contrib/sites/models.py:
⋮...
│def _simple_domain_name_validator()
│class SiteManager
│    def _get_site_by_id()
│    def _get_site_by_request()
│    def get_current()
│    def clear_cache()
│    def get_by_natural_key()
│class Site
│class Meta
│    def __str__()
│    def natural_key()
│def clear_site_cache()
⋮...

│contrib/sites/requests.py:
⋮...
│class RequestSite
│    def __init__()
│    def __str__()
│    def save()
│    def delete()
⋮...

│contrib/sites/shortcuts.py:
⋮...
│def get_current_site()
⋮...

│contrib/staticfiles/apps.py:
⋮...
│class StaticFilesConfig
│    def ready()
⋮...

│contrib/staticfiles/checks.py:
⋮...
│def check_finders()
│def check_storages()
⋮...

│contrib/staticfiles/finders.py:
⋮...
│def _check_deprecated_find_param()
│class BaseFinder
│    def check()
│    def _check_deprecated_find_param()
│    def find()
│    def list()
│class FileSystemFinder
│    def __init__()
│    def check()
│    def find()
│    def find_location()
│    def list()
│class AppDirectoriesFinder
│    def __init__()
│    def list()
│    def find()
│    def find_in_app()
│class BaseStorageFinder
│    def __init__()
│    def find()
│    def list()
│class DefaultStorageFinder
│    def __init__()
│def find()
│def get_finders()
│def get_finder()
⋮...

│contrib/staticfiles/handlers.py:
⋮...
│class StaticFilesHandlerMixin
│    def load_middleware()
│    def get_base_url()
│    def _should_handle()
│    def file_path()
│    def serve()
│    def get_response()
│def async get_response_async()
│class StaticFilesHandler
│    def __init__()
│    def __call__()
│class ASGIStaticFilesHandler
│    def __init__()
│def async __call__()
│def async get_response_async()
│def async awrapper()
⋮...

│contrib/staticfiles/management/commands/collectstatic.py:
⋮...
│class Command
│    def __init__()
│    def local()
│    def add_arguments()
│    def set_options()
│    def collect()
│    def handle()
│    def log()
│    def is_local_storage()
│    def clear_dir()
│    def delete_file()
│    def link_file()
│    def copy_file()
⋮...

│contrib/staticfiles/management/commands/findstatic.py:
⋮...
│class Command
│    def add_arguments()
│    def handle_label()
⋮...

│contrib/staticfiles/management/commands/runserver.py:
⋮...
│class Command
│    def add_arguments()
│    def get_handler()
⋮...

│contrib/staticfiles/storage.py:
⋮...
│class StaticFilesStorage
│    def __init__()
│    def path()
│class HashedFilesMixin
│    def __init__()
│    def file_hash()
│    def hashed_name()
│    def _url()
│    def url()
│    def url_converter()
│    def converter()
│    def post_process()
│    def _post_process()
│    def path_level()
│    def clean_name()
│    def hash_key()
│    def _stored_name()
│    def stored_name()
│class ManifestFilesMixin
│    def __init__()
│    def read_manifest()
│    def load_manifest()
│    def post_process()
│    def save_manifest()
│    def stored_name()
│class ManifestStaticFilesStorage
│class ConfiguredStorage
│    def _setup()
⋮...

│contrib/staticfiles/testing.py:
⋮...
│class StaticLiveServerTestCase
⋮...

│contrib/staticfiles/urls.py:
⋮...
│def staticfiles_urlpatterns()
⋮...

│contrib/staticfiles/utils.py:
⋮...
│def matches_patterns()
│def get_files()
│def check_settings()
⋮...

│contrib/staticfiles/views.py:
⋮...
│def serve()
⋮...

│contrib/syndication/apps.py:
⋮...
│class SyndicationConfig
⋮...

│contrib/syndication/views.py:
⋮...
│def add_domain()
│class FeedDoesNotExist
│class Feed
│    def __call__()
│    def item_title()
│    def item_description()
│    def item_link()
│    def item_enclosures()
│    def _get_dynamic_attr()
│    def feed_extra_kwargs()
│    def item_extra_kwargs()
│    def get_object()
│    def get_context_data()
│    def get_feed()
⋮...

│core/asgi.py:
⋮...
│def get_asgi_application()
⋮...

│core/cache/__init__.py:
⋮...
│class CacheHandler
│    def create_connection()
│def close_caches()
⋮...

│core/cache/backends/base.py:
⋮...
│class InvalidCacheBackendError
│class CacheKeyWarning
│class InvalidCacheKey
│def default_key_func()
│def get_key_func()
│class BaseCache
│    def __init__()
│    def get_backend_timeout()
│    def make_key()
│    def validate_key()
│    def make_and_validate_key()
│    def add()
│def async aadd()
│def get()
│def async aget()
│def set()
│def async aset()
│def touch()
│def async atouch()
│def delete()
│def async adelete()
│def get_many()
│def async aget_many()
│def get_or_set()
│def async aget_or_set()
│def has_key()
│def async ahas_key()
│def incr()
│def async aincr()
│def decr()
│def async adecr()
│def __contains__()
│def set_many()
│def async aset_many()
│def delete_many()
│def async adelete_many()
│def clear()
│def async aclear()
│def incr_version()
│def async aincr_version()
│def decr_version()
│def async adecr_version()
│def close()
│def async aclose()
│def memcache_key_warnings()
⋮...

│core/cache/backends/db.py:
⋮...
│class Options
│    def __init__()
│class BaseDatabaseCache
│    def __init__()
│class CacheEntry
│class DatabaseCache
│    def get()
│    def get_many()
│    def set()
│    def add()
│    def touch()
│    def _base_set()
│    def delete()
│    def delete_many()
│    def _base_delete_many()
│    def has_key()
│    def _cull()
│    def clear()
⋮...

│core/cache/backends/dummy.py:
⋮...
│class DummyCache
│    def __init__()
│    def add()
│    def get()
│    def set()
│    def touch()
│    def delete()
│    def has_key()
│    def clear()
⋮...

│core/cache/backends/filebased.py:
⋮...
│class FileBasedCache
│    def __init__()
│    def add()
│    def get()
│    def _write_content()
│    def set()
│    def touch()
│    def delete()
│    def _delete()
│    def has_key()
│    def _cull()
│    def _createdir()
│    def _key_to_file()
│    def clear()
│    def _is_expired()
│    def _list_cache_files()
⋮...

│core/cache/backends/locmem.py:
⋮...
│class LocMemCache
│    def __init__()
│    def add()
│    def get()
│    def _set()
│    def set()
│    def touch()
│    def incr()
│    def has_key()
│    def _has_expired()
│    def _cull()
│    def _delete()
│    def delete()
│    def clear()
⋮...

│core/cache/backends/memcached.py:
⋮...
│class BaseMemcachedCache
│    def __init__()
│    def client_servers()
│    def _cache()
│    def get_backend_timeout()
│    def add()
│    def get()
│    def set()
│    def touch()
│    def delete()
│    def get_many()
│    def close()
│    def incr()
│    def set_many()
│    def delete_many()
│    def clear()
│    def validate_key()
│class PyLibMCCache
│    def __init__()
│    def client_servers()
│    def touch()
│    def close()
│class PyMemcacheCache
│    def __init__()
⋮...

│core/cache/backends/redis.py:
⋮...
│class RedisSerializer
│    def __init__()
│    def dumps()
│    def loads()
│class RedisCacheClient
│    def __init__()
│    def _get_connection_pool_index()
│    def _get_connection_pool()
│    def get_client()
│    def add()
│    def get()
│    def set()
│    def touch()
│    def delete()
│    def get_many()
│    def has_key()
│    def incr()
│    def set_many()
│    def delete_many()
│    def clear()
│class RedisCache
│    def __init__()
│    def _cache()
│    def get_backend_timeout()
│    def add()
│    def get()
│    def set()
│    def touch()
│    def delete()
│    def get_many()
│    def has_key()
│    def incr()
│    def set_many()
│    def delete_many()
│    def clear()
⋮...

│core/cache/utils.py:
⋮...
│def make_template_fragment_key()
⋮...

│core/checks/__init__.py:
⋮...

│core/checks/async_checks.py:
⋮...
│def check_async_unsafe()
⋮...

│core/checks/caches.py:
⋮...
│def check_default_cache_is_configured()
│def check_cache_location_not_exposed()
│def check_file_based_cache_is_absolute()
⋮...

│core/checks/commands.py:
⋮...
│def migrate_and_makemigrations_autodetector()
⋮...

│core/checks/compatibility/django_4_0.py:
⋮...
│def check_csrf_trusted_origins()
⋮...

│core/checks/database.py:
⋮...
│def check_database_backends()
⋮...

│core/checks/files.py:
⋮...
│def check_setting_file_upload_temp_dir()
⋮...

│core/checks/messages.py:
⋮...
│class CheckMessage
│    def __init__()
│    def __eq__()
│    def __str__()
│    def __repr__()
│    def is_serious()
│    def is_silenced()
│class Debug
│    def __init__()
│class Info
│    def __init__()
│class Warning
│    def __init__()
│class Error
│    def __init__()
│class Critical
│    def __init__()
⋮...

│core/checks/model_checks.py:
⋮...
│def check_all_models()
│def _check_lazy_references()
│def extract_operation()
│def app_model_error()
│def field_error()
│def signal_connect_error()
│def default_error()
│def build_error()
│def check_lazy_references()
⋮...

│core/checks/registry.py:
⋮...
│class Tags
│class CheckRegistry
│    def __init__()
│    def register()
│    def inner()
│    def run_checks()
│    def tag_exists()
│    def tags_available()
│    def get_checks()
⋮...

│core/checks/security/base.py:
⋮...
│def _security_middleware()
│def _xframe_middleware()
│def check_security_middleware()
│def check_xframe_options_middleware()
│def check_sts()
│def check_sts_include_subdomains()
│def check_sts_preload()
│def check_content_type_nosniff()
│def check_ssl_redirect()
│def _check_secret_key()
│def check_secret_key()
│def check_secret_key_fallbacks()
│def check_debug()
│def check_xframe_deny()
│def check_allowed_hosts()
│def check_referrer_policy()
│def check_cross_origin_opener_policy()
⋮...

│core/checks/security/csrf.py:
⋮...
│def _csrf_middleware()
│def check_csrf_middleware()
│def check_csrf_cookie_secure()
│def check_csrf_failure_view()
⋮...

│core/checks/security/sessions.py:
⋮...
│def add_session_cookie_message()
│def add_httponly_message()
│def check_session_cookie_secure()
│def check_session_cookie_httponly()
│def _session_middleware()
│def _session_app()
⋮...

│core/checks/templates.py:
⋮...
│def check_templates()
⋮...

│core/checks/translation.py:
⋮...
│def check_setting_language_code()
│def check_setting_languages()
│def check_setting_languages_bidi()
│def check_language_settings_consistent()
⋮...

│core/checks/urls.py:
⋮...
│def check_url_config()
│def check_resolver()
│def check_url_namespaces_unique()
│def _load_all_namespaces()
│def get_warning_for_invalid_pattern()
│def check_url_settings()
│def E006()
│def check_custom_error_handlers()
⋮...

│core/exceptions.py:
⋮...
│class FieldDoesNotExist
│class AppRegistryNotReady
│class ObjectDoesNotExist
│class ObjectNotUpdated
│class MultipleObjectsReturned
│class SuspiciousOperation
│class SuspiciousMultipartForm
│class SuspiciousFileOperation
│class DisallowedHost
│class DisallowedRedirect
│class TooManyFieldsSent
│class TooManyFilesSent
│class RequestDataTooBig
│class RequestAborted
│class BadRequest
│class PermissionDenied
│class ViewDoesNotExist
│class MiddlewareNotUsed
│class ImproperlyConfigured
│class FieldError
│class ValidationError
│    def __init__()
│    def message_dict()
│    def messages()
│    def update_error_dict()
│    def __iter__()
│    def __str__()
│    def __repr__()
│    def __eq__()
│    def __hash__()
│class EmptyResultSet
│class FullResultSet
│class SynchronousOnlyOperation
⋮...

│core/files/__init__.py:
⋮...

│core/files/base.py:
⋮...
│class File
│    def __init__()
│    def __str__()
│    def __repr__()
│    def __bool__()
│    def __len__()
│    def size()
│    def chunks()
│    def multiple_chunks()
│    def __iter__()
│    def __enter__()
│    def __exit__()
│    def open()
│    def close()
│class ContentFile
│    def __init__()
│    def __str__()
│    def __bool__()
│    def open()
│    def close()
│    def write()
│def endswith_cr()
│def endswith_lf()
│def equals_lf()
⋮...

│core/files/images.py:
⋮...
│class ImageFile
│    def width()
│    def height()
│    def _get_image_dimensions()
│def get_image_dimensions()
⋮...

│core/files/locks.py:
⋮...
│def _fd()
│class _OFFSET
│class _OFFSET_UNION
│class OVERLAPPED
│    def lock()
│    def unlock()
│    def lock()
│    def unlock()
│    def lock()
│    def unlock()
⋮...

│core/files/move.py:
⋮...
│def file_move_safe()
⋮...

│core/files/storage/__init__.py:
⋮...
│class DefaultStorage
│    def _setup()
⋮...

│core/files/storage/base.py:
⋮...
│class Storage
│    def open()
│    def save()
│    def is_name_available()
│    def get_valid_name()
│    def get_alternative_name()
│    def get_available_name()
│    def generate_filename()
│    def path()
│    def delete()
│    def exists()
│    def listdir()
│    def size()
│    def url()
│    def get_accessed_time()
│    def get_created_time()
│    def get_modified_time()
⋮...

│core/files/storage/filesystem.py:
⋮...
│class FileSystemStorage
│    def __init__()
│    def base_location()
│    def location()
│    def base_url()
│    def file_permissions_mode()
│    def directory_permissions_mode()
│    def _open()
│    def _save()
│    def _ensure_location_group_id()
│    def delete()
│    def is_name_available()
│    def get_alternative_name()
│    def exists()
│    def listdir()
│    def path()
│    def size()
│    def url()
│    def _datetime_from_timestamp()
│    def get_accessed_time()
│    def get_created_time()
│    def get_modified_time()
⋮...

│core/files/storage/handler.py:
⋮...
│class InvalidStorageError
│class StorageHandler
│    def __init__()
│    def backends()
│    def __getitem__()
│    def create_storage()
⋮...

│core/files/storage/memory.py:
⋮...
│class TimingMixin
│    def _initialize_times()
│    def _update_accessed_time()
│    def _update_modified_time()
│class InMemoryFileNode
│    def __init__()
│    def open()
│    def write()
│    def _initialize_stream()
│    def _convert_stream_content()
│class InMemoryDirNode
│    def __init__()
│    def resolve()
│    def _resolve_child()
│    def listdir()
│    def remove_child()
│class InMemoryStorage
│    def __init__()
│    def base_location()
│    def location()
│    def base_url()
│    def file_permissions_mode()
│    def directory_permissions_mode()
│    def _relative_path()
│    def _resolve()
│    def _open()
│    def _save()
│    def path()
│    def delete()
│    def exists()
│    def listdir()
│    def size()
│    def url()
│    def get_accessed_time()
│    def get_created_time()
│    def get_modified_time()
⋮...

│core/files/storage/mixins.py:
⋮...
│class StorageSettingsMixin
│    def _clear_cached_properties()
│    def _value_or_setting()
⋮...

│core/files/temp.py:
⋮...
│class TemporaryFile
│    def __init__()
│    def close()
│    def __del__()
│    def __enter__()
│    def __exit__()
⋮...

│core/files/uploadedfile.py:
⋮...
│class UploadedFile
│    def __init__()
│    def __repr__()
│    def _get_name()
│    def _set_name()
│class TemporaryUploadedFile
│    def __init__()
│    def temporary_file_path()
│    def close()
│class InMemoryUploadedFile
│    def __init__()
│    def open()
│    def chunks()
│    def multiple_chunks()
│class SimpleUploadedFile
│    def __init__()
│    def from_dict()
⋮...

│core/files/uploadhandler.py:
⋮...
│class UploadFileException
│class StopUpload
│    def __init__()
│    def __str__()
│class SkipFile
│class StopFutureHandlers
│class FileUploadHandler
│    def __init__()
│    def handle_raw_input()
│    def new_file()
│    def receive_data_chunk()
│    def file_complete()
│    def upload_complete()
│    def upload_interrupted()
│class TemporaryFileUploadHandler
│    def new_file()
│    def receive_data_chunk()
│    def file_complete()
│    def upload_interrupted()
│class MemoryFileUploadHandler
│    def handle_raw_input()
│    def new_file()
│    def receive_data_chunk()
│    def file_complete()
│def load_handler()
⋮...

│core/files/utils.py:
⋮...
│def validate_file_name()
│class FileProxyMixin
│    def closed()
│    def readable()
│    def writable()
│    def seekable()
│    def __iter__()
⋮...

│core/handlers/asgi.py:
⋮...
│def get_script_prefix()
│class ASGIRequest
│    def __init__()
│    def GET()
│    def _get_scheme()
│    def _get_post()
│    def _set_post()
│    def _get_files()
│    def COOKIES()
│    def close()
│class ASGIHandler
│    def __init__()
│def async __call__()
│def async handle()
│def async process_request()
│def async listen_for_disconnect()
│def async run_get_response()
│def async read_body()
│def create_request()
│def handle_uncaught_exception()
│def async send_response()
│def chunk_bytes()
⋮...

│core/handlers/base.py:
⋮...
│class BaseHandler
│    def load_middleware()
│    def adapt_method_mode()
│    def get_response()
│def async get_response_async()
│def _get_response()
│def async _get_response_async()
│def resolve_request()
│def check_response()
│def make_view_atomic()
│def process_exception_by_middleware()
│def reset_urlconf()
⋮...

│core/handlers/exception.py:
⋮...
│def convert_exception_to_response()
│def async inner()
│def inner()
│def response_for_exception()
│def get_exception_response()
│def handle_uncaught_exception()
⋮...

│core/handlers/wsgi.py:
⋮...
│class LimitedStream
│    def __init__()
│    def read()
│    def readline()
│class WSGIRequest
│    def __init__()
│    def _get_scheme()
│    def GET()
│    def _get_post()
│    def _set_post()
│    def COOKIES()
│    def FILES()
│class WSGIHandler
│    def __init__()
│    def __call__()
│def get_path_info()
│def get_script_name()
│def get_bytes_from_wsgi()
│def get_str_from_wsgi()
⋮...

│core/mail/__init__.py:
⋮...
│def get_connection()
│def send_mail()
│def send_mass_mail()
│def _send_server_message()
│def mail_admins()
│def mail_managers()
⋮...

│core/mail/backends/base.py:
⋮...
│class BaseEmailBackend
│    def __init__()
│    def open()
│    def close()
│    def __enter__()
│    def __exit__()
│    def send_messages()
⋮...

│core/mail/backends/console.py:
⋮...
│class EmailBackend
│    def __init__()
│    def write_message()
│    def send_messages()
⋮...

│core/mail/backends/dummy.py:
⋮...
│class EmailBackend
│    def send_messages()
⋮...

│core/mail/backends/filebased.py:
⋮...
│class EmailBackend
│    def __init__()
│    def write_message()
│    def _get_filename()
│    def open()
│    def close()
⋮...

│core/mail/backends/locmem.py:
⋮...
│class EmailBackend
│    def __init__()
│    def send_messages()
⋮...

│core/mail/backends/smtp.py:
⋮...
│class EmailBackend
│    def __init__()
│    def connection_class()
│    def ssl_context()
│    def open()
│    def close()
│    def send_messages()
│    def _send()
⋮...

│core/mail/message.py:
⋮...
│class BadHeaderError
│def forbid_multi_line_headers()
│def sanitize_address()
│class MIMEMixin
│    def as_string()
│    def as_bytes()
│class SafeMIMEMessage
│    def __setitem__()
│class SafeMIMEText
│    def __init__()
│    def __setitem__()
│    def set_payload()
│class SafeMIMEMultipart
│    def __init__()
│    def __setitem__()
│class EmailMessage
│    def __init__()
│    def get_connection()
│    def message()
│    def recipients()
│    def send()
│    def attach()
│    def attach_file()
│    def _create_message()
│    def _create_attachments()
│    def _create_mime_attachment()
│    def _create_attachment()
│    def _set_list_header_if_not_empty()
│class EmailMultiAlternatives
│    def __init__()
│    def attach_alternative()
│    def _create_message()
│    def _create_alternatives()
│    def body_contains()
⋮...

│core/mail/utils.py:
⋮...
│class CachedDnsName
│    def __str__()
│    def get_fqdn()
⋮...

│core/management/__init__.py:
⋮...
│def find_commands()
│def load_command_class()
│def get_commands()
│def call_command()
│def get_actions()
│class ManagementUtility
│    def __init__()
│    def main_help_text()
│    def fetch_command()
│    def autocomplete()
│    def execute()
│def execute_from_command_line()
⋮...

│core/management/base.py:
⋮...
│class CommandError
│    def __init__()
│class SystemCheckError
│class CommandParser
│    def __init__()
│    def parse_args()
│    def error()
│    def add_subparsers()
│def handle_default_options()
│def no_translations()
│def wrapper()
│class DjangoHelpFormatter
│    def _reordered_actions()
│    def add_usage()
│    def add_arguments()
│class OutputWrapper
│    def style_func()
│    def style_func()
│    def __init__()
│    def __getattr__()
│    def flush()
│    def isatty()
│    def write()
│class BaseCommand
│    def __init__()
│    def get_version()
│    def create_parser()
│    def add_arguments()
│    def add_base_argument()
│    def print_help()
│    def run_from_argv()
│    def execute()
│    def get_check_kwargs()
│    def check()
│    def check_migrations()
│    def handle()
│class AppCommand
│    def add_arguments()
│    def handle()
│    def handle_app_config()
│class LabelCommand
│    def __init__()
│    def add_arguments()
│    def handle()
│    def handle_label()
⋮...

│core/management/color.py:
⋮...
│def supports_color()
│def vt_codes_enabled_in_windows_registry()
│class Style
│def make_style()
│def style_func()
│def no_style()
│def color_style()
⋮...

│core/management/commands/check.py:
⋮...
│class Command
│    def add_arguments()
│    def handle()
⋮...

│core/management/commands/compilemessages.py:
⋮...
│def has_bom()
│def is_dir_writable()
│class Command
│    def add_arguments()
│    def handle()
│    def compile_messages()
⋮...

│core/management/commands/createcachetable.py:
⋮...
│class Command
│    def add_arguments()
│    def handle()
│    def create_table()
⋮...

│core/management/commands/dbshell.py:
⋮...
│class Command
│    def add_arguments()
│    def handle()
⋮...

│core/management/commands/diffsettings.py:
⋮...
│def module_to_dict()
│class Command
│    def add_arguments()
│    def handle()
│    def output_hash()
│    def output_unified()
⋮...

│core/management/commands/dumpdata.py:
⋮...
│class ProxyModelWarning
│class Command
│    def add_arguments()
│    def handle()
│    def get_objects()
⋮...

│core/management/commands/flush.py:
⋮...
│class Command
│    def add_arguments()
│    def handle()
⋮...

│core/management/commands/inspectdb.py:
⋮...
│class Command
│    def add_arguments()
│    def handle()
│    def handle_inspection()
│    def normalize_col_name()
│    def normalize_table_name()
│    def get_field_type()
│    def get_meta()
⋮...

│core/management/commands/loaddata.py:
⋮...
│class Command
│    def add_arguments()
│    def handle()
│    def compression_formats()
│    def reset_sequences()
│    def loaddata()
│    def save_obj()
│    def load_label()
│    def get_fixture_name_and_dirs()
│    def get_targets()
│    def find_fixture_files_in_dir()
│    def find_fixtures()
│    def fixture_dirs()
│    def parse_name()
│class SingleZipReader
│    def __init__()
│    def read()
│def humanize()
⋮...

│core/management/commands/makemessages.py:
⋮...
│def check_programs()
│def is_valid_locale()
│class TranslatableFile
│    def __init__()
│    def __repr__()
│    def __eq__()
│    def __lt__()
│    def path()
│class BuildFile
│    def __init__()
│    def is_templatized()
│    def path()
│    def work_path()
│    def preprocess()
│    def postprocess_messages()
│    def cleanup()
│def normalize_eols()
│def write_pot_file()
│class Command
│    def add_arguments()
│    def handle()
│    def gettext_version()
│    def settings_available()
│    def build_potfiles()
│    def remove_potfiles()
│    def find_files()
│    def process_files()
│    def process_locale_dir()
│    def write_po_file()
│    def copy_plural_forms()
⋮...

│core/management/commands/makemigrations.py:
⋮...
│class Command
│    def add_arguments()
│    def log_output()
│    def log()
│    def handle()
│    def write_to_last_migration_files()
│    def write_migration_files()
│    def get_relative_path()
│    def handle_merge()
│    def all_items_equal()
⋮...

│core/management/commands/migrate.py:
⋮...
│class Command
│    def add_arguments()
│    def get_check_kwargs()
│    def handle()
│    def migration_progress_callback()
│    def sync_apps()
│    def model_installed()
│    def describe_operation()
⋮...

│core/management/commands/optimizemigration.py:
⋮...
│class Command
│    def add_arguments()
│    def handle()
⋮...

│core/management/commands/runserver.py:
⋮...
│class Command
│    def add_arguments()
│    def execute()
│    def get_handler()
│    def get_check_kwargs()
│    def handle()
│    def run()
│    def inner_run()
│    def on_bind()
⋮...

│core/management/commands/sendtestemail.py:
⋮...
│class Command
│    def add_arguments()
│    def handle()
⋮...

│core/management/commands/shell.py:
⋮...
│class Command
│    def add_arguments()
│    def ipython()
│    def bpython()
│    def python()
│    def get_auto_imports()
│    def get_namespace()
│    def handle()
⋮...

│core/management/commands/showmigrations.py:
⋮...
│class Command
│    def add_arguments()
│    def handle()
│    def _validate_app_names()
│    def show_list()
│    def show_plan()
│    def print_deps()
⋮...

│core/management/commands/sqlflush.py:
⋮...
│class Command
│    def add_arguments()
│    def handle()
⋮...

│core/management/commands/sqlmigrate.py:
⋮...
│class Command
│    def add_arguments()
│    def execute()
│    def handle()
⋮...

│core/management/commands/sqlsequencereset.py:
⋮...
│class Command
│    def add_arguments()
│    def handle_app_config()
⋮...

│core/management/commands/squashmigrations.py:
⋮...
│class Command
│    def add_arguments()
│    def handle()
│    def find_migration()
⋮...

│core/management/commands/startapp.py:
⋮...
│class Command
│    def handle()
⋮...

│core/management/commands/startproject.py:
⋮...
│class Command
│    def handle()
⋮...

│core/management/commands/test.py:
⋮...
│class Command
│    def run_from_argv()
│    def add_arguments()
│    def handle()
⋮...

│core/management/commands/testserver.py:
⋮...
│class Command
│    def add_arguments()
│    def handle()
⋮...

│core/management/sql.py:
⋮...
│def sql_flush()
│def emit_pre_migrate_signal()
│def emit_post_migrate_signal()
⋮...

│core/management/templates.py:
⋮...
│class TemplateCommand
│    def add_arguments()
│    def handle()
│    def handle_template()
│    def validate_name()
│    def download()
│    def cleanup_url()
│    def splitext()
│    def extract()
│    def is_url()
│    def apply_umask()
│    def make_writeable()
⋮...

│core/management/utils.py:
⋮...
│def popen_wrapper()
│def handle_extensions()
│def find_command()
│def get_random_secret_key()
│def parse_apps_and_model_labels()
│def get_command_line_option()
│def normalize_path_patterns()
│def is_ignored_path()
│def ignore()
│def find_formatters()
│def run_formatters()
⋮...

│core/paginator.py:
⋮...
│class UnorderedObjectListWarning
│class InvalidPage
│class PageNotAnInteger
│class EmptyPage
│class BasePaginator
│    def __init__()
│    def _check_object_list_is_ordered()
│    def _get_elided_page_range()
│    def _get_page()
│    def _validate_number()
│class Paginator
│    def __iter__()
│    def validate_number()
│    def get_page()
│    def page()
│    def count()
│    def num_pages()
│    def page_range()
│    def get_elided_page_range()
│class AsyncPaginator
│    def __init__()
│def async __aiter__()
│def async avalidate_number()
│def async aget_page()
│def async apage()
│def _get_page()
│def async acount()
│def async anum_pages()
│def async apage_range()
│def async aget_elided_page_range()
│class Page
│    def __init__()
│    def __repr__()
│    def __len__()
│    def __getitem__()
│    def has_next()
│    def has_previous()
│    def has_other_pages()
│    def next_page_number()
│    def previous_page_number()
│    def start_index()
│    def end_index()
│class AsyncPage
│    def __init__()
│    def __repr__()
│def async __aiter__()
│def __len__()
│def __reversed__()
│def __getitem__()
│def async aget_object_list()
│def async ahas_next()
│def async ahas_previous()
│def async ahas_other_pages()
│def async anext_page_number()
│def async aprevious_page_number()
│def async astart_index()
│def async aend_index()
⋮...

│core/serializers/__init__.py:
⋮...
│class BadSerializer
│    def __init__()
│    def __call__()
│def register_serializer()
│def unregister_serializer()
│def get_serializer()
│def get_serializer_formats()
│def get_public_serializer_formats()
│def get_deserializer()
│def serialize()
│def deserialize()
│def _load_serializers()
│def sort_dependencies()
⋮...

│core/serializers/base.py:
⋮...
│class SerializerDoesNotExist
│class SerializationError
│class DeserializationError
│    def WithData()
│class M2MDeserializationError
│    def __init__()
│class ProgressBar
│    def __init__()
│    def update()
│class Serializer
│    def serialize()
│    def start_serialization()
│    def end_serialization()
│    def start_object()
│    def end_object()
│    def handle_field()
│    def handle_fk_field()
│    def handle_m2m_field()
│    def getvalue()
│class Deserializer
│    def __init__()
│    def __iter__()
│    def __next__()
│class DeserializedObject
│    def __init__()
│    def __repr__()
│    def save()
│    def save_deferred_fields()
│def build_instance()
│def deserialize_m2m_values()
│def m2m_convert()
│def m2m_convert()
│def deserialize_fk_value()
⋮...

│core/serializers/json.py:
⋮...
│class Serializer
│    def _init_options()
│    def start_serialization()
│    def end_serialization()
│    def end_object()
│    def getvalue()
│class Deserializer
│    def __init__()
│    def _handle_object()
│class DjangoJSONEncoder
│    def default()
⋮...

│core/serializers/jsonl.py:
⋮...
│class Serializer
│    def _init_options()
│    def start_serialization()
│    def end_object()
│    def getvalue()
│class Deserializer
│    def __init__()
│    def _handle_object()
│    def _get_lines()
⋮...

│core/serializers/python.py:
⋮...
│class Serializer
│    def start_serialization()
│    def end_serialization()
│    def start_object()
│    def end_object()
│    def get_dump_object()
│    def _value_from_field()
│    def handle_field()
│    def handle_fk_field()
│    def handle_m2m_field()
│    def m2m_value()
│    def queryset_iterator()
│    def m2m_value()
│    def queryset_iterator()
│    def getvalue()
│class Deserializer
│    def __init__()
│    def __iter__()
│    def __next__()
│    def _handle_object()
│    def _handle_m2m_field_node()
│    def _handle_fk_field_node()
│    def _get_model_from_node()
⋮...

│core/serializers/pyyaml.py:
⋮...
│class DjangoSafeDumper
│    def represent_decimal()
│    def represent_ordered_dict()
│class Serializer
│    def _value_from_field()
│    def end_serialization()
│    def getvalue()
│class Deserializer
│    def __init__()
│    def _handle_object()
⋮...

│core/serializers/xml_serializer.py:
⋮...
│class Serializer
│    def indent()
│    def start_serialization()
│    def end_serialization()
│    def start_object()
│    def end_object()
│    def handle_field()
│    def handle_fk_field()
│    def handle_m2m_field()
│    def handle_m2m()
│    def queryset_iterator()
│    def handle_m2m()
│    def queryset_iterator()
│    def _start_relational_field()
│class Deserializer
│    def __init__()
│    def _make_parser()
│    def __next__()
│    def _handle_object()
│    def _handle_fk_field_node()
│    def _handle_m2m_field_node()
│    def m2m_convert()
│    def m2m_convert()
│    def _get_model_from_node()
│def getInnerText()
│class DefusedExpatParser
│    def __init__()
│    def start_doctype_decl()
│    def entity_decl()
│    def unparsed_entity_decl()
│    def external_entity_ref_handler()
│    def reset()
│class DefusedXmlException
│    def __repr__()
│class DTDForbidden
│    def __init__()
│    def __str__()
│class EntitiesForbidden
│    def __init__()
│    def __str__()
│class ExternalReferenceForbidden
│    def __init__()
│    def __str__()
⋮...

│core/servers/basehttp.py:
⋮...
│def get_internal_wsgi_application()
│def is_broken_pipe_error()
│class WSGIServer
│    def __init__()
│    def handle_error()
│class ThreadedWSGIServer
│    def __init__()
│    def process_request_thread()
│    def _close_connections()
│    def close_request()
│class ServerHandler
│    def __init__()
│    def cleanup_headers()
│    def close()
│    def finish_response()
│class WSGIRequestHandler
│    def address_string()
│    def log_message()
│    def get_environ()
│    def handle()
│    def handle_one_request()
│def run()
⋮...

│core/signals.py:
⋮...

│core/signing.py:
⋮...
│class BadSignature
│class SignatureExpired
│def b62_encode()
│def b62_decode()
│def b64_encode()
│def b64_decode()
│def base64_hmac()
│def _cookie_signer_key()
│def get_cookie_signer()
│class JSONSerializer
│    def dumps()
│    def loads()
│def dumps()
│def loads()
│class Signer
│    def __init__()
│    def signature()
│    def sign()
│    def unsign()
│    def sign_object()
│    def unsign_object()
│class TimestampSigner
│    def timestamp()
│    def sign()
│    def unsign()
⋮...

│core/validators.py:
⋮...
│class RegexValidator
│    def __init__()
│    def __call__()
│    def __eq__()
│class DomainNameValidator
│    def __init__()
│    def __call__()
│class URLValidator
│    def __init__()
│    def __call__()
│def validate_integer()
│class EmailValidator
│    def __init__()
│    def __call__()
│    def validate_domain_part()
│    def __eq__()
│def validate_ipv4_address()
│def validate_ipv6_address()
│def validate_ipv46_address()
│def ip_address_validators()
│def int_list_validator()
│class BaseValidator
│    def __init__()
│    def __call__()
│    def __eq__()
│    def compare()
│    def clean()
│class MaxValueValidator
│    def compare()
│class MinValueValidator
│    def compare()
│class StepValueValidator
│    def __init__()
│    def __call__()
│    def compare()
│class MinLengthValidator
│    def compare()
│    def clean()
│class MaxLengthValidator
│    def compare()
│    def clean()
│class DecimalValidator
│    def __init__()
│    def __call__()
│    def __eq__()
│class FileExtensionValidator
│    def __init__()
│    def __call__()
│    def __eq__()
│def get_available_image_extensions()
│def validate_image_file_extension()
│class ProhibitNullCharactersValidator
│    def __init__()
│    def __call__()
│    def __eq__()
⋮...

│core/wsgi.py:
⋮...
│def get_wsgi_application()
⋮...

│db/__init__.py:
⋮...
│def reset_queries()
│def close_old_connections()
⋮...

│db/backends/base/base.py:
⋮...
│class BaseDatabaseWrapper
│    def __init__()
│    def __repr__()
│    def ensure_timezone()
│    def timezone()
│    def timezone_name()
│    def queries_logged()
│    def queries()
│    def get_database_version()
│    def check_database_version_supported()
│    def get_connection_params()
│    def get_new_connection()
│    def init_connection_state()
│    def create_cursor()
│    def connect()
│    def check_settings()
│    def ensure_connection()
│    def _prepare_cursor()
│    def _cursor()
│    def _commit()
│    def _rollback()
│    def _close()
│    def cursor()
│    def commit()
│    def rollback()
│    def close()
│    def _savepoint()
│    def _savepoint_rollback()
│    def _savepoint_commit()
│    def _savepoint_allowed()
│    def savepoint()
│    def savepoint_rollback()
│    def savepoint_commit()
│    def clean_savepoints()
│    def _set_autocommit()
│    def get_autocommit()
│    def set_autocommit()
│    def get_rollback()
│    def set_rollback()
│    def validate_no_atomic_block()
│    def validate_no_broken_transaction()
│    def constraint_checks_disabled()
│    def disable_constraint_checking()
│    def enable_constraint_checking()
│    def check_constraints()
│    def is_usable()
│    def close_if_health_check_failed()
│    def close_if_unusable_or_obsolete()
│    def allow_thread_sharing()
│    def inc_thread_sharing()
│    def dec_thread_sharing()
│    def validate_thread_sharing()
│    def prepare_database()
│    def wrap_database_errors()
│    def chunked_cursor()
│    def make_debug_cursor()
│    def make_cursor()
│    def temporary_connection()
│    def _nodb_cursor()
│    def schema_editor()
│    def on_commit()
│    def run_and_clear_commit_hooks()
│    def execute_wrapper()
│    def copy()
⋮...

│db/backends/base/client.py:
⋮...
│class BaseDatabaseClient
│    def __init__()
│    def __del__()
│    def settings_to_cmd_args_env()
│    def runshell()
⋮...

│db/backends/base/creation.py:
⋮...
│class BaseDatabaseCreation
│    def __init__()
│    def __del__()
│    def _nodb_cursor()
│    def log()
│    def create_test_db()
│    def set_as_test_mirror()
│    def serialize_db_to_string()
│    def get_objects()
│    def deserialize_db_from_string()
│    def _get_database_display_str()
│    def _get_test_db_name()
│    def _execute_create_test_db()
│    def _create_test_db()
│    def clone_test_db()
│    def get_test_db_clone_settings()
│    def _clone_test_db()
│    def destroy_test_db()
│    def _destroy_test_db()
│    def mark_expected_failures_and_skips()
│    def sql_table_creation_suffix()
│    def test_db_signature()
│    def setup_worker_connection()
⋮...

│db/backends/base/features.py:
⋮...
│class BaseDatabaseFeatures
│    def __init__()
│    def __del__()
│    def supports_explaining_query_execution()
│    def supports_transactions()
│    def allows_group_by_selected_pks_on_model()
⋮...

│db/backends/base/introspection.py:
⋮...
│class BaseDatabaseIntrospection
│    def __init__()
│    def __del__()
│    def get_field_type()
│    def identifier_converter()
│    def table_names()
│    def get_names()
│    def get_table_list()
│    def get_table_description()
│    def get_migratable_models()
│    def django_table_names()
│    def installed_models()
│    def sequence_list()
│    def get_sequences()
│    def get_relations()
│    def get_primary_key_column()
│    def get_primary_key_columns()
│    def get_constraints()
⋮...

│db/backends/base/operations.py:
⋮...
│class BaseDatabaseOperations
│    def __init__()
│    def __del__()
│    def autoinc_sql()
│    def bulk_batch_size()
│    def format_for_duration_arithmetic()
│    def cache_key_culling_sql()
│    def unification_cast_sql()
│    def date_extract_sql()
│    def date_trunc_sql()
│    def datetime_cast_date_sql()
│    def datetime_cast_time_sql()
│    def datetime_extract_sql()
│    def datetime_trunc_sql()
│    def time_trunc_sql()
│    def time_extract_sql()
│    def deferrable_sql()
│    def distinct_sql()
│    def fetch_returned_insert_columns()
│    def force_group_by()
│    def force_no_ordering()
│    def for_update_sql()
│    def _get_limit_offset_params()
│    def limit_offset_sql()
│    def bulk_insert_sql()
│    def last_executed_query()
│    def to_string()
│    def last_insert_id()
│    def lookup_cast()
│    def max_in_list_size()
│    def max_name_length()
│    def no_limit_value()
│    def pk_default_value()
│    def prepare_sql_script()
│    def process_clob()
│    def return_insert_columns()
│    def compiler()
│    def quote_name()
│    def regex_lookup()
│    def savepoint_create_sql()
│    def savepoint_commit_sql()
│    def savepoint_rollback_sql()
│    def set_time_zone_sql()
│    def sql_flush()
│    def execute_sql_flush()
│    def sequence_reset_by_name_sql()
│    def sequence_reset_sql()
│    def start_transaction_sql()
│    def end_transaction_sql()
│    def tablespace_sql()
│    def prep_for_like_query()
│    def validate_autopk_value()
│    def adapt_unknown_value()
│    def adapt_integerfield_value()
│    def adapt_datefield_value()
│    def adapt_datetimefield_value()
│    def adapt_timefield_value()
│    def adapt_decimalfield_value()
│    def adapt_ipaddressfield_value()
│    def adapt_json_value()
│    def year_lookup_bounds_for_date_field()
│    def year_lookup_bounds_for_datetime_field()
│    def get_db_converters()
│    def convert_durationfield_value()
│    def check_expression_support()
│    def conditional_expression_supported_in_where_clause()
│    def combine_expression()
│    def combine_duration_expression()
│    def binary_placeholder_sql()
│    def modify_insert_params()
│    def integer_field_range()
│    def subtract_temporals()
│    def window_frame_value()
│    def window_frame_rows_start_end()
│    def window_frame_range_start_end()
│    def explain_query_prefix()
│    def insert_statement()
│    def on_conflict_suffix_sql()
│    def prepare_join_on_clause()
│    def format_debug_sql()
⋮...

│db/backends/base/schema.py:
⋮...
│def _is_relevant_relation()
│def _all_related_fields()
│def _related_non_m2m_objects()
│class BaseDatabaseSchemaEditor
│    def __init__()
│    def __enter__()
│    def __exit__()
│    def execute()
│    def quote_name()
│    def table_sql()
│    def _iter_column_sql()
│    def column_sql()
│    def skip_default()
│    def skip_default_on_alter()
│    def prepare_default()
│    def _column_default_sql()
│    def db_default_sql()
│    def _column_generated_sql()
│    def _effective_default()
│    def effective_default()
│    def quote_value()
│    def create_model()
│    def delete_model()
│    def add_index()
│    def remove_index()
│    def rename_index()
│    def add_constraint()
│    def remove_constraint()
│    def alter_unique_together()
│    def alter_index_together()
│    def _delete_composed_index()
│    def alter_db_table()
│    def alter_db_table_comment()
│    def alter_db_tablespace()
│    def add_field()
│    def remove_field()
│    def alter_field()
│    def _field_db_check()
│    def _alter_field()
│    def _alter_column_null_sql()
│    def _alter_column_default_sql()
│    def _alter_column_database_default_sql()
│    def _alter_column_type_sql()
│    def _alter_column_comment_sql()
│    def _comment_sql()
│    def _alter_many_to_many()
│    def _create_index_name()
│    def _get_index_tablespace_sql()
│    def _index_condition_sql()
│    def _index_include_sql()
│    def _create_index_sql()
│    def create_index_name()
│    def _delete_index_sql()
│    def _rename_index_sql()
│    def _index_columns()
│    def _model_indexes_sql()
│    def _field_indexes_sql()
│    def _field_should_be_altered()
│    def _field_should_be_indexed()
│    def _field_became_primary_key()
│    def _unique_should_be_added()
│    def _rename_field_sql()
│    def _create_fk_sql()
│    def _fk_constraint_name()
│    def create_fk_name()
│    def _delete_fk_sql()
│    def _deferrable_constraint_sql()
│    def _unique_index_nulls_distinct_sql()
│    def _unique_supported()
│    def _unique_sql()
│    def _create_unique_sql()
│    def _unique_constraint_name()
│    def create_unique_name()
│    def _delete_unique_sql()
│    def _check_sql()
│    def _create_check_sql()
│    def _delete_check_sql()
│    def _delete_constraint_sql()
│    def _constraint_names()
│    def _pk_constraint_sql()
│    def _delete_primary_key()
│    def _create_primary_key_sql()
│    def _delete_primary_key_sql()
│    def _collate_sql()
│    def remove_procedure()
⋮...

│db/backends/base/validation.py:
⋮...
│class BaseDatabaseValidation
│    def __init__()
│    def __del__()
│    def check()
│    def check_field()
⋮...

│db/backends/ddl_references.py:
⋮...
│class Reference
│    def references_table()
│    def references_column()
│    def references_index()
│    def rename_table_references()
│    def rename_column_references()
│    def __repr__()
│    def __str__()
│class Table
│    def __init__()
│    def references_table()
│    def references_index()
│    def rename_table_references()
│    def __str__()
│class TableColumns
│    def __init__()
│    def references_column()
│    def rename_column_references()
│class Columns
│    def __init__()
│    def __str__()
│    def col_str()
│class IndexName
│    def __init__()
│    def __str__()
│class IndexColumns
│    def __init__()
│    def __str__()
│    def col_str()
│class ForeignKeyName
│    def __init__()
│    def references_table()
│    def references_column()
│    def rename_table_references()
│    def rename_column_references()
│    def __str__()
│class Statement
│    def __init__()
│    def references_table()
│    def references_column()
│    def references_index()
│    def rename_table_references()
│    def rename_column_references()
│    def __str__()
│class Expressions
│    def __init__()
│    def rename_table_references()
│    def rename_column_references()
│    def __str__()
⋮...

│db/backends/dummy/base.py:
⋮...
│def complain()
│def ignore()
│class DatabaseOperations
│class DatabaseClient
│class DatabaseCreation
│class DatabaseIntrospection
│class DatabaseWrapper
│    def is_usable()
⋮...

│db/backends/dummy/features.py:
⋮...
│class DummyDatabaseFeatures
⋮...

│db/backends/mysql/base.py:
⋮...
│class CursorWrapper
│    def __init__()
│    def execute()
│    def executemany()
│    def __getattr__()
│    def __iter__()
│class DatabaseWrapper
│    def data_types()
│    def get_database_version()
│    def get_connection_params()
│    def get_new_connection()
│    def init_connection_state()
│    def create_cursor()
│    def _rollback()
│    def _set_autocommit()
│    def disable_constraint_checking()
│    def enable_constraint_checking()
│    def check_constraints()
│    def is_usable()
│    def display_name()
│    def data_type_check_constraints()
│    def mysql_server_data()
│    def mysql_server_info()
│    def mysql_version()
│    def mysql_is_mariadb()
│    def sql_mode()
⋮...

│db/backends/mysql/client.py:
⋮...
│class DatabaseClient
│    def settings_to_cmd_args_env()
│    def runshell()
⋮...

│db/backends/mysql/compiler.py:
⋮...
│class SQLDeleteCompiler
│    def as_sql()
│class SQLUpdateCompiler
│    def as_sql()
⋮...

│db/backends/mysql/creation.py:
⋮...
│class DatabaseCreation
│    def sql_table_creation_suffix()
│    def _execute_create_test_db()
│    def _clone_test_db()
│    def _clone_db()
⋮...

│db/backends/mysql/features.py:
⋮...
│class DatabaseFeatures
│    def minimum_database_version()
│    def test_collations()
│    def django_test_skips()
│    def _mysql_storage_engine()
│    def allows_auto_pk_0()
│    def update_can_self_select()
│    def can_introspect_foreign_keys()
│    def introspected_field_types()
│    def can_return_columns_from_insert()
│    def has_zoneinfo_database()
│    def is_sql_auto_is_null_enabled()
│    def supports_column_check_constraints()
│    def can_introspect_check_constraints()
│    def has_select_for_update_of()
│    def supports_explain_analyze()
│    def supported_explain_formats()
│    def supports_transactions()
│    def ignores_table_name_case()
│    def supports_default_in_lead_lag()
│    def can_introspect_json_field()
│    def supports_index_column_ordering()
│    def supports_expression_indexes()
│    def supports_select_intersection()
│    def supports_expression_defaults()
│    def has_native_uuid_field()
│    def allows_group_by_selected_pks()
⋮...

│db/backends/mysql/introspection.py:
⋮...
│class DatabaseIntrospection
│    def get_field_type()
│    def get_table_list()
│    def get_table_description()
│    def to_int()
│    def get_sequences()
│    def get_relations()
│    def get_storage_engine()
│    def _parse_constraint_columns()
│    def get_constraints()
⋮...

│db/backends/mysql/operations.py:
⋮...
│class DatabaseOperations
│    def date_extract_sql()
│    def date_trunc_sql()
│    def _prepare_tzname_delta()
│    def _convert_sql_to_tz()
│    def datetime_cast_date_sql()
│    def datetime_cast_time_sql()
│    def datetime_extract_sql()
│    def datetime_trunc_sql()
│    def time_trunc_sql()
│    def fetch_returned_insert_rows()
│    def format_for_duration_arithmetic()
│    def force_no_ordering()
│    def last_executed_query()
│    def no_limit_value()
│    def quote_name()
│    def return_insert_columns()
│    def sql_flush()
│    def sequence_reset_by_name_sql()
│    def validate_autopk_value()
│    def adapt_datetimefield_value()
│    def adapt_timefield_value()
│    def max_name_length()
│    def pk_default_value()
│    def combine_expression()
│    def get_db_converters()
│    def convert_booleanfield_value()
│    def convert_datetimefield_value()
│    def convert_uuidfield_value()
│    def binary_placeholder_sql()
│    def subtract_temporals()
│    def explain_query_prefix()
│    def regex_lookup()
│    def insert_statement()
│    def lookup_cast()
│    def conditional_expression_supported_in_where_clause()
│    def on_conflict_suffix_sql()
⋮...

│db/backends/mysql/schema.py:
⋮...
│class DatabaseSchemaEditor
│    def sql_delete_check()
│    def quote_value()
│    def _is_limited_data_type()
│    def _is_text_or_blob()
│    def skip_default()
│    def skip_default_on_alter()
│    def _supports_limited_data_type_defaults()
│    def _column_default_sql()
│    def add_field()
│    def remove_constraint()
│    def remove_index()
│    def _field_should_be_indexed()
│    def _create_missing_fk_index()
│    def _delete_composed_index()
│    def _set_field_new_type()
│    def _alter_column_type_sql()
│    def _field_db_check()
│    def _rename_field_sql()
│    def _alter_column_comment_sql()
│    def _comment_sql()
│    def _alter_column_null_sql()
⋮...

│db/backends/mysql/validation.py:
⋮...
│class DatabaseValidation
│    def check()
│    def _check_sql_mode()
│    def check_field_type()
⋮...

│db/backends/oracle/base.py:
⋮...
│def _setup_environment()
│def wrap_oracle_errors()
│class _UninitializedOperatorsDescriptor
│    def __get__()
│class DatabaseWrapper
│    def __init__()
│    def is_pool()
│    def pool()
│    def close_pool()
│    def get_database_version()
│    def get_connection_params()
│    def get_new_connection()
│    def init_connection_state()
│    def create_cursor()
│    def _commit()
│    def _savepoint_commit()
│    def _set_autocommit()
│    def check_constraints()
│    def is_usable()
│    def close_if_health_check_failed()
│    def oracle_version()
│    def oracledb_version()
│class OracleParam
│    def __init__()
│class VariableWrapper
│    def __init__()
│    def bind_parameter()
│    def __getattr__()
│    def __setattr__()
│class FormatStylePlaceholderCursor
│    def __init__()
│    def _output_number_converter()
│    def _get_decimal_converter()
│    def _output_type_handler()
│    def _format_params()
│    def _guess_input_sizes()
│    def _param_generator()
│    def _fix_for_params()
│    def execute()
│    def executemany()
│    def close()
│    def var()
│    def arrayvar()
│    def __getattr__()
│    def __iter__()
⋮...

│db/backends/oracle/client.py:
⋮...
│class DatabaseClient
│    def connect_string()
│    def settings_to_cmd_args_env()
⋮...

│db/backends/oracle/creation.py:
⋮...
│class DatabaseCreation
│    def _maindb_connection()
│    def _create_test_db()
│    def _switch_to_test_user()
│    def set_as_test_mirror()
│    def _handle_objects_preventing_db_destruction()
│    def _destroy_test_db()
│    def _execute_test_db_creation()
│    def _create_test_user()
│    def _execute_test_db_destruction()
│    def _destroy_test_user()
│    def _execute_statements()
│    def _execute_allow_fail_statements()
│    def _get_test_db_params()
│    def _test_settings_get()
│    def _test_database_name()
│    def _test_database_create()
│    def _test_user_create()
│    def _test_database_user()
│    def _test_database_passwd()
│    def _test_database_tblspace()
│    def _test_database_tblspace_tmp()
│    def _test_database_tblspace_datafile()
│    def _test_database_tblspace_tmp_datafile()
│    def _test_database_tblspace_maxsize()
│    def _test_database_tblspace_tmp_maxsize()
│    def _test_database_tblspace_size()
│    def _test_database_tblspace_tmp_size()
│    def _test_database_tblspace_extsize()
│    def _test_database_tblspace_tmp_extsize()
│    def _test_database_oracle_managed_files()
│    def _get_test_db_name()
│    def test_db_signature()
⋮...

│db/backends/oracle/features.py:
⋮...
│class DatabaseFeatures
│    def django_test_skips()
│    def introspected_field_types()
│    def test_collations()
│    def supports_collation_on_charfield()
│    def supports_primitives_in_json_field()
│    def supports_frame_exclusion()
│    def supports_boolean_expr_in_select_clause()
│    def supports_comparing_boolean_expr()
│    def supports_aggregation_over_interval_types()
│    def bare_select_suffix()
│    def supports_tuple_lookups()
⋮...

│db/backends/oracle/functions.py:
⋮...
│class IntervalToSeconds
│    def __init__()
│class SecondsToInterval
│    def __init__()
⋮...

│db/backends/oracle/introspection.py:
⋮...
│class DatabaseIntrospection
│    def get_field_type()
│    def get_table_list()
│    def get_table_description()
│    def identifier_converter()
│    def get_sequences()
│    def get_relations()
│    def get_primary_key_columns()
│    def get_constraints()
⋮...

│db/backends/oracle/operations.py:
⋮...
│class DatabaseOperations
│    def cache_key_culling_sql()
│    def date_extract_sql()
│    def date_trunc_sql()
│    def _prepare_tzname_delta()
│    def _convert_sql_to_tz()
│    def datetime_cast_date_sql()
│    def datetime_cast_time_sql()
│    def datetime_extract_sql()
│    def datetime_trunc_sql()
│    def time_extract_sql()
│    def time_trunc_sql()
│    def get_db_converters()
│    def convert_textfield_value()
│    def convert_binaryfield_value()
│    def convert_booleanfield_value()
│    def convert_datetimefield_value()
│    def convert_datefield_value()
│    def convert_timefield_value()
│    def convert_uuidfield_value()
│    def convert_empty_string()
│    def convert_empty_bytes()
│    def deferrable_sql()
│    def fetch_returned_insert_columns()
│    def no_limit_value()
│    def limit_offset_sql()
│    def last_executed_query()
│    def last_insert_id()
│    def lookup_cast()
│    def max_in_list_size()
│    def max_name_length()
│    def pk_default_value()
│    def prep_for_iexact_query()
│    def process_clob()
│    def quote_name()
│    def regex_lookup()
│    def return_insert_columns()
│    def __foreign_key_constraints()
│    def _foreign_key_constraints()
│    def sql_flush()
│    def sequence_reset_by_name_sql()
│    def sequence_reset_sql()
│    def start_transaction_sql()
│    def tablespace_sql()
│    def adapt_datefield_value()
│    def adapt_datetimefield_value()
│    def adapt_timefield_value()
│    def combine_expression()
│    def _get_no_autofield_sequence_name()
│    def _get_sequence_name()
│    def bulk_insert_sql()
│    def subtract_temporals()
│    def bulk_batch_size()
│    def conditional_expression_supported_in_where_clause()
⋮...

│db/backends/oracle/schema.py:
⋮...
│class DatabaseSchemaEditor
│    def quote_value()
│    def remove_field()
│    def delete_model()
│    def alter_field()
│    def _alter_field_type_workaround()
│    def _alter_column_type_sql()
│    def normalize_name()
│    def _generate_temp_name()
│    def prepare_default()
│    def _field_should_be_indexed()
│    def _is_identity_column()
│    def _drop_identity()
│    def _get_default_collation()
│    def _collate_sql()
⋮...

│db/backends/oracle/utils.py:
⋮...
│class InsertVar
│    def __init__()
│    def bind_parameter()
│    def get_value()
│class Oracle_datetime
│    def from_datetime()
│class BulkInsertMapper
│def dsn()
⋮...

│db/backends/oracle/validation.py:
⋮...
│class DatabaseValidation
│    def check_field_type()
⋮...

│db/backends/postgresql/base.py:
⋮...
│def psycopg_version()
│def _get_varchar_column()
│class DatabaseWrapper
│    def pool()
│    def close_pool()
│    def get_database_version()
│    def get_connection_params()
│    def get_new_connection()
│    def ensure_timezone()
│    def _configure_timezone()
│    def _configure_role()
│    def _configure_connection()
│    def _close()
│    def init_connection_state()
│    def create_cursor()
│    def tzinfo_factory()
│    def chunked_cursor()
│    def _set_autocommit()
│    def check_constraints()
│    def is_usable()
│    def close_if_health_check_failed()
│    def _nodb_cursor()
│    def pg_version()
│    def make_debug_cursor()
│class CursorMixin
│    def callproc()
│class ServerBindingCursor
│class Cursor
│class ServerSideCursor
│class CursorDebugWrapper
│    def copy()
│class CursorDebugWrapper
│    def copy_expert()
│    def copy_to()
⋮...

│db/backends/postgresql/client.py:
⋮...
│class DatabaseClient
│    def settings_to_cmd_args_env()
│    def runshell()
⋮...

│db/backends/postgresql/compiler.py:
⋮...
│class InsertUnnest
│    def __str__()
│class SQLInsertCompiler
│    def assemble_as_sql()
⋮...

│db/backends/postgresql/creation.py:
⋮...
│class DatabaseCreation
│    def _quote_name()
│    def _get_database_create_suffix()
│    def sql_table_creation_suffix()
│    def _database_exists()
│    def _execute_create_test_db()
│    def _clone_test_db()
│    def _destroy_test_db()
⋮...

│db/backends/postgresql/features.py:
⋮...
│class DatabaseFeatures
│    def django_test_skips()
│    def django_test_expected_failures()
│    def uses_server_side_binding()
│    def prohibits_null_characters_in_text_exception()
│    def introspected_field_types()
│    def is_postgresql_15()
│    def is_postgresql_16()
│    def is_postgresql_17()
⋮...

│db/backends/postgresql/introspection.py:
⋮...
│class DatabaseIntrospection
│    def get_field_type()
│    def get_table_list()
│    def get_table_description()
│    def get_sequences()
│    def get_relations()
│    def get_constraints()
⋮...

│db/backends/postgresql/operations.py:
⋮...
│def get_json_dumps()
│class DatabaseOperations
│    def unification_cast_sql()
│    def date_extract_sql()
│    def date_trunc_sql()
│    def _prepare_tzname_delta()
│    def _convert_sql_to_tz()
│    def datetime_cast_date_sql()
│    def datetime_cast_time_sql()
│    def datetime_extract_sql()
│    def datetime_trunc_sql()
│    def time_extract_sql()
│    def time_trunc_sql()
│    def deferrable_sql()
│    def bulk_insert_sql()
│    def fetch_returned_insert_rows()
│    def lookup_cast()
│    def no_limit_value()
│    def prepare_sql_script()
│    def quote_name()
│    def compose_sql()
│    def set_time_zone_sql()
│    def sql_flush()
│    def sequence_reset_by_name_sql()
│    def tablespace_sql()
│    def sequence_reset_sql()
│    def prep_for_iexact_query()
│    def max_name_length()
│    def distinct_sql()
│    def last_executed_query()
│    def last_executed_query()
│    def return_insert_columns()
│    def adapt_integerfield_value()
│    def adapt_datefield_value()
│    def adapt_datetimefield_value()
│    def adapt_timefield_value()
│    def adapt_ipaddressfield_value()
│    def adapt_json_value()
│    def subtract_temporals()
│    def explain_query_prefix()
│    def on_conflict_suffix_sql()
│    def prepare_join_on_clause()
⋮...

│db/backends/postgresql/psycopg_any.py:
⋮...
│def mogrify()
│class BaseTzLoader
│    def load()
│    def register_tzloader()
│class SpecificTzLoader
│class DjangoRangeDumper
│    def upgrade()
│    def get_adapters_template()
│class IsolationLevel
│    def _quote()
│    def mogrify()
│class Jsonb
│    def getquoted()
⋮...

│db/backends/postgresql/schema.py:
⋮...
│class DatabaseSchemaEditor
│    def execute()
│    def quote_value()
│    def _field_indexes_sql()
│    def _field_data_type()
│    def _field_base_data_types()
│    def _create_like_index_sql()
│    def _using_sql()
│    def _get_sequence_name()
│    def _is_changing_type_of_indexed_text_column()
│    def _alter_column_type_sql()
│    def _alter_field()
│    def _index_columns()
│    def add_index()
│    def remove_index()
│    def _delete_index_sql()
│    def _create_index_sql()
│    def _is_collation_deterministic()
⋮...

│db/backends/signals.py:
⋮...

│db/backends/sqlite3/_functions.py:
⋮...
│def register()
│def _sqlite_datetime_parse()
│def _sqlite_date_trunc()
│def _sqlite_time_trunc()
│def _sqlite_datetime_cast_date()
│def _sqlite_datetime_cast_time()
│def _sqlite_datetime_extract()
│def _sqlite_datetime_trunc()
│def _sqlite_time_extract()
│def _sqlite_prepare_dtdelta_param()
│def _sqlite_format_dtdelta()
│def _sqlite_time_diff()
│def _sqlite_timestamp_diff()
│def _sqlite_regexp()
│def _sqlite_acos()
│def _sqlite_asin()
│def _sqlite_atan()
│def _sqlite_atan2()
│def _sqlite_bitxor()
│def _sqlite_ceiling()
│def _sqlite_cos()
│def _sqlite_cot()
│def _sqlite_degrees()
│def _sqlite_exp()
│def _sqlite_floor()
│def _sqlite_ln()
│def _sqlite_log()
│def _sqlite_lpad()
│def _sqlite_md5()
│def _sqlite_mod()
│def _sqlite_pi()
│def _sqlite_power()
│def _sqlite_radians()
│def _sqlite_repeat()
│def _sqlite_reverse()
│def _sqlite_rpad()
│def _sqlite_sha1()
│def _sqlite_sha224()
│def _sqlite_sha256()
│def _sqlite_sha384()
│def _sqlite_sha512()
│def _sqlite_sign()
│def _sqlite_sin()
│def _sqlite_sqrt()
│def _sqlite_tan()
│class ListAggregate
│class StdDevPop
│class StdDevSamp
│class VarPop
│class VarSamp
⋮...

│db/backends/sqlite3/base.py:
⋮...
│def decoder()
│def adapt_date()
│def adapt_datetime()
│def _get_varchar_column()
│class DatabaseWrapper
│    def get_connection_params()
│    def get_database_version()
│    def get_new_connection()
│    def create_cursor()
│    def close()
│    def _savepoint_allowed()
│    def _set_autocommit()
│    def disable_constraint_checking()
│    def enable_constraint_checking()
│    def check_constraints()
│    def is_usable()
│    def _start_transaction_under_autocommit()
│    def is_in_memory_db()
│class SQLiteCursorWrapper
│    def execute()
│    def executemany()
│    def convert_query()
⋮...

│db/backends/sqlite3/client.py:
⋮...
│class DatabaseClient
│    def settings_to_cmd_args_env()
⋮...

│db/backends/sqlite3/creation.py:
⋮...
│class DatabaseCreation
│    def is_in_memory_db()
│    def _get_test_db_name()
│    def _create_test_db()
│    def get_test_db_clone_settings()
│    def _clone_test_db()
│    def _destroy_test_db()
│    def test_db_signature()
│    def setup_worker_connection()
⋮...

│db/backends/sqlite3/features.py:
⋮...
│class DatabaseFeatures
│    def django_test_skips()
│    def introspected_field_types()
│    def supports_json_field()
│    def can_return_columns_from_insert()
⋮...

│db/backends/sqlite3/introspection.py:
⋮...
│def get_field_size()
│class FlexibleFieldLookupDict
│    def __getitem__()
│class DatabaseIntrospection
│    def get_field_type()
│    def get_table_list()
│    def get_table_description()
│    def get_sequences()
│    def get_relations()
│    def get_primary_key_columns()
│    def _parse_column_or_constraint_definition()
│    def _parse_table_constraints()
│    def get_constraints()
│    def _get_index_columns_orders()
│    def _get_column_collations()
⋮...

│db/backends/sqlite3/operations.py:
⋮...
│class DatabaseOperations
│    def bulk_batch_size()
│    def check_expression_support()
│    def date_extract_sql()
│    def fetch_returned_insert_rows()
│    def format_for_duration_arithmetic()
│    def date_trunc_sql()
│    def time_trunc_sql()
│    def _convert_tznames_to_sql()
│    def datetime_cast_date_sql()
│    def datetime_cast_time_sql()
│    def datetime_extract_sql()
│    def datetime_trunc_sql()
│    def time_extract_sql()
│    def pk_default_value()
│    def _quote_params_for_last_executed_query()
│    def last_executed_query()
│    def quote_name()
│    def no_limit_value()
│    def __references_graph()
│    def _references_graph()
│    def sql_flush()
│    def sequence_reset_by_name_sql()
│    def adapt_datetimefield_value()
│    def adapt_timefield_value()
│    def get_db_converters()
│    def convert_datetimefield_value()
│    def convert_datefield_value()
│    def convert_timefield_value()
│    def get_decimalfield_converter()
│    def converter()
│    def converter()
│    def convert_uuidfield_value()
│    def convert_booleanfield_value()
│    def combine_expression()
│    def combine_duration_expression()
│    def integer_field_range()
│    def subtract_temporals()
│    def insert_statement()
│    def return_insert_columns()
│    def on_conflict_suffix_sql()
│    def force_group_by()
⋮...

│db/backends/sqlite3/schema.py:
⋮...
│class DatabaseSchemaEditor
│    def __enter__()
│    def __exit__()
│    def quote_value()
│    def prepare_default()
│    def _remake_table()
│    def is_self_referential()
│    def delete_model()
│    def add_field()
│    def remove_field()
│    def _alter_field()
│    def _alter_many_to_many()
│    def add_constraint()
│    def remove_constraint()
│    def _collate_sql()
⋮...

│db/backends/utils.py:
⋮...
│class CursorWrapper
│    def __init__()
│    def __getattr__()
│    def __iter__()
│    def __enter__()
│    def __exit__()
│    def callproc()
│    def execute()
│    def executemany()
│    def _execute_with_wrappers()
│    def _execute()
│    def _executemany()
│class CursorDebugWrapper
│    def execute()
│    def executemany()
│    def debug_sql()
│def debug_transaction()
│def split_tzname_delta()
│def typecast_date()
│def typecast_time()
│def typecast_timestamp()
│def split_identifier()
│def truncate_name()
│def names_digest()
│def format_number()
│def strip_quotes()
⋮...

│db/migrations/autodetector.py:
⋮...
│class OperationDependency
│class Type
│    def model_name_lower()
│    def field_name_lower()
│class MigrationAutodetector
│    def __init__()
│    def changes()
│    def deep_deconstruct()
│    def only_relation_agnostic_fields()
│    def _detect_changes()
│    def _prepare_field_lists()
│    def _generate_through_model_map()
│    def _resolve_dependency()
│    def _build_migration_list()
│    def _sort_migrations()
│    def _optimize_migrations()
│    def check_dependency()
│    def add_operation()
│    def swappable_first_key()
│    def generate_renamed_models()
│    def generate_created_models()
│    def generate_created_proxies()
│    def generate_deleted_models()
│    def generate_deleted_proxies()
│    def create_renamed_fields()
│    def generate_renamed_fields()
│    def generate_added_fields()
│    def _generate_added_field()
│    def generate_removed_fields()
│    def _generate_removed_field()
│    def generate_altered_fields()
│    def create_altered_indexes()
│    def generate_added_indexes()
│    def generate_removed_indexes()
│    def generate_renamed_indexes()
│    def _constraint_should_be_dropped_and_recreated()
│    def create_altered_constraints()
│    def generate_added_constraints()
│    def generate_removed_constraints()
│    def generate_altered_constraints()
│    def _get_dependencies_for_foreign_key()
│    def _get_dependencies_for_generated_field()
│    def _get_dependencies_for_model()
│    def _get_altered_foo_together_operations()
│    def _generate_removed_altered_foo_together()
│    def generate_removed_altered_unique_together()
│    def _generate_altered_foo_together()
│    def generate_altered_unique_together()
│    def generate_altered_db_table()
│    def generate_altered_db_table_comment()
│    def generate_altered_options()
│    def generate_altered_order_with_respect_to()
│    def generate_altered_managers()
│    def arrange_for_graph()
│    def _trim_to_apps()
│    def parse_number()
⋮...

│db/migrations/exceptions.py:
⋮...
│class AmbiguityError
│class BadMigrationError
│class CircularDependencyError
│class InconsistentMigrationHistory
│class InvalidBasesError
│class IrreversibleError
│class NodeNotFoundError
│    def __init__()
│    def __str__()
│    def __repr__()
│class MigrationSchemaMissing
│class InvalidMigrationPlan
⋮...

│db/migrations/executor.py:
⋮...
│class MigrationExecutor
│    def __init__()
│    def migration_plan()
│    def _create_project_state()
│    def migrate()
│    def _migrate_all_forwards()
│    def _migrate_all_backwards()
│    def apply_migration()
│    def record_migration()
│    def unapply_migration()
│    def check_replacements()
│    def detect_soft_applied()
│    def should_skip_detecting_model()
⋮...

│db/migrations/graph.py:
⋮...
│class Node
│    def __init__()
│    def __eq__()
│    def __lt__()
│    def __hash__()
│    def __getitem__()
│    def __str__()
│    def __repr__()
│    def add_child()
│    def add_parent()
│class DummyNode
│    def __init__()
│    def raise_error()
│class MigrationGraph
│    def __init__()
│    def add_node()
│    def add_dummy_node()
│    def add_dependency()
│    def remove_replaced_nodes()
│    def remove_replacement_node()
│    def validate_consistency()
│    def forwards_plan()
│    def backwards_plan()
│    def iterative_dfs()
│    def root_nodes()
│    def leaf_nodes()
│    def ensure_not_cyclic()
│    def __str__()
│    def __repr__()
│    def _nodes_and_edges()
│    def _generate_plan()
│    def make_state()
│    def __contains__()
⋮...

│db/migrations/loader.py:
⋮...
│class MigrationLoader
│    def __init__()
│    def migrations_module()
│    def load_disk()
│    def get_migration()
│    def get_migration_by_prefix()
│    def check_key()
│    def add_internal_dependencies()
│    def add_external_dependencies()
│    def _resolve_replaced_migration_keys()
│    def replace_migration()
│    def build_graph()
│    def check_consistent_history()
│    def detect_conflicts()
│    def project_state()
│    def collect_sql()
⋮...

│db/migrations/migration.py:
⋮...
│class Migration
│    def __init__()
│    def __eq__()
│    def __repr__()
│    def __str__()
│    def __hash__()
│    def mutate_state()
│    def apply()
│    def unapply()
│    def suggest_name()
│class SwappableTuple
│    def __new__()
│def swappable_dependency()
⋮...

│db/migrations/operations/__init__.py:
⋮...

│db/migrations/operations/base.py:
⋮...
│class OperationCategory
│class Operation
│    def __new__()
│    def __replace__()
│    def deconstruct()
│    def state_forwards()
│    def database_forwards()
│    def database_backwards()
│    def describe()
│    def formatted_description()
│    def migration_name_fragment()
│    def references_model()
│    def references_field()
│    def allow_migrate_model()
│    def reduce()
│    def __repr__()
⋮...

│db/migrations/operations/fields.py:
⋮...
│class FieldOperation
│    def __init__()
│    def model_name_lower()
│    def name_lower()
│    def is_same_model_operation()
│    def is_same_field_operation()
│    def references_model()
│    def references_field()
│    def reduce()
│class AddField
│    def __init__()
│    def deconstruct()
│    def state_forwards()
│    def database_forwards()
│    def database_backwards()
│    def describe()
│    def migration_name_fragment()
│    def reduce()
│class RemoveField
│    def deconstruct()
│    def state_forwards()
│    def database_forwards()
│    def database_backwards()
│    def describe()
│    def migration_name_fragment()
│    def reduce()
│class AlterField
│    def __init__()
│    def deconstruct()
│    def state_forwards()
│    def database_forwards()
│    def database_backwards()
│    def describe()
│    def migration_name_fragment()
│    def reduce()
│class RenameField
│    def __init__()
│    def old_name_lower()
│    def new_name_lower()
│    def deconstruct()
│    def state_forwards()
│    def database_forwards()
│    def database_backwards()
│    def describe()
│    def migration_name_fragment()
│    def references_field()
│    def reduce()
⋮...

│db/migrations/operations/models.py:
⋮...
│def _check_for_duplicates()
│class ModelOperation
│    def __init__()
│    def name_lower()
│    def references_model()
│    def reduce()
│    def can_reduce_through()
│class CreateModel
│    def __init__()
│    def deconstruct()
│    def state_forwards()
│    def database_forwards()
│    def database_backwards()
│    def describe()
│    def migration_name_fragment()
│    def references_model()
│    def reduce()
│class DeleteModel
│    def deconstruct()
│    def state_forwards()
│    def database_forwards()
│    def database_backwards()
│    def references_model()
│    def describe()
│    def migration_name_fragment()
│class RenameModel
│    def __init__()
│    def old_name_lower()
│    def new_name_lower()
│    def deconstruct()
│    def state_forwards()
│    def database_forwards()
│    def database_backwards()
│    def references_model()
│    def describe()
│    def migration_name_fragment()
│    def reduce()
│class ModelOptionOperation
│    def reduce()
│class AlterModelTable
│    def __init__()
│    def deconstruct()
│    def state_forwards()
│    def database_forwards()
│    def database_backwards()
│    def describe()
│    def migration_name_fragment()
│class AlterModelTableComment
│    def __init__()
│    def deconstruct()
│    def state_forwards()
│    def database_forwards()
│    def database_backwards()
│    def describe()
│    def migration_name_fragment()
│class AlterTogetherOptionOperation
│    def __init__()
│    def option_value()
│    def deconstruct()
│    def state_forwards()
│    def database_forwards()
│    def database_backwards()
│    def references_field()
│    def describe()
│    def migration_name_fragment()
│    def can_reduce_through()
│class AlterUniqueTogether
│    def __init__()
│class AlterIndexTogether
│    def __init__()
│class AlterOrderWithRespectTo
│    def __init__()
│    def deconstruct()
│    def state_forwards()
│    def database_forwards()
│    def database_backwards()
│    def references_field()
│    def describe()
│    def migration_name_fragment()
│class AlterModelOptions
│    def __init__()
│    def deconstruct()
│    def state_forwards()
│    def database_forwards()
│    def database_backwards()
│    def describe()
│    def migration_name_fragment()
│class AlterModelManagers
│    def __init__()
│    def deconstruct()
│    def state_forwards()
│    def database_forwards()
│    def database_backwards()
│    def describe()
│    def migration_name_fragment()
│class IndexOperation
│    def model_name_lower()
│class AddIndex
│    def __init__()
│    def state_forwards()
│    def database_forwards()
│    def database_backwards()
│    def deconstruct()
│    def describe()
│    def migration_name_fragment()
│    def reduce()
│class RemoveIndex
│    def __init__()
│    def state_forwards()
│    def database_forwards()
│    def database_backwards()
│    def deconstruct()
│    def describe()
│    def migration_name_fragment()
│class RenameIndex
│    def __init__()
│    def old_name_lower()
│    def new_name_lower()
│    def deconstruct()
│    def state_forwards()
│    def database_forwards()
│    def database_backwards()
│    def describe()
│    def migration_name_fragment()
│    def reduce()
│class AddConstraint
│    def __init__()
│    def state_forwards()
│    def database_forwards()
│    def database_backwards()
│    def deconstruct()
│    def describe()
│    def migration_name_fragment()
│    def reduce()
│class RemoveConstraint
│    def __init__()
│    def state_forwards()
│    def database_forwards()
│    def database_backwards()
│    def deconstruct()
│    def describe()
│    def migration_name_fragment()
│class AlterConstraint
│    def __init__()
│    def state_forwards()
│    def database_forwards()
│    def database_backwards()
│    def deconstruct()
│    def describe()
│    def migration_name_fragment()
│    def reduce()
⋮...

│db/migrations/operations/special.py:
⋮...
│class SeparateDatabaseAndState
│    def __init__()
│    def deconstruct()
│    def state_forwards()
│    def database_forwards()
│    def database_backwards()
│    def describe()
│class RunSQL
│    def __init__()
│    def deconstruct()
│    def reversible()
│    def state_forwards()
│    def database_forwards()
│    def database_backwards()
│    def describe()
│    def _run_sql()
│class RunPython
│    def __init__()
│    def deconstruct()
│    def reversible()
│    def state_forwards()
│    def database_forwards()
│    def database_backwards()
│    def describe()
│    def noop()
⋮...

│db/migrations/optimizer.py:
⋮...
│class MigrationOptimizer
│    def optimize()
│    def optimize_inner()
⋮...

│db/migrations/questioner.py:
⋮...
│class MigrationQuestioner
│    def __init__()
│    def ask_initial()
│    def ask_not_null_addition()
│    def ask_not_null_alteration()
│    def ask_rename()
│    def ask_rename_model()
│    def ask_merge()
│    def ask_auto_now_add_addition()
│    def ask_unique_callable_default_addition()
│class InteractiveMigrationQuestioner
│    def __init__()
│    def _boolean_input()
│    def _choice_input()
│    def _ask_default()
│    def ask_not_null_addition()
│    def ask_not_null_alteration()
│    def ask_rename()
│    def ask_rename_model()
│    def ask_merge()
│    def ask_auto_now_add_addition()
│    def ask_unique_callable_default_addition()
│class NonInteractiveMigrationQuestioner
│    def __init__()
│    def log_lack_of_migration()
│    def ask_not_null_addition()
│    def ask_not_null_alteration()
│    def ask_auto_now_add_addition()
⋮...

│db/migrations/recorder.py:
⋮...
│class MigrationRecorder
│    def Migration()
│class Migration
│class Meta
│    def __str__()
│    def __init__()
│    def migration_qs()
│    def has_table()
│    def ensure_schema()
│    def applied_migrations()
│    def record_applied()
│    def record_unapplied()
│    def flush()
⋮...

│db/migrations/serializer.py:
⋮...
│class BaseSerializer
│    def __init__()
│    def serialize()
│class BaseSequenceSerializer
│    def _format()
│    def serialize()
│class BaseUnorderedSequenceSerializer
│    def __init__()
│class BaseSimpleSerializer
│    def serialize()
│class ChoicesSerializer
│    def serialize()
│class DateTimeSerializer
│    def serialize()
│class DatetimeDatetimeSerializer
│    def serialize()
│class DecimalSerializer
│    def serialize()
│class DeconstructableSerializer
│    def serialize_deconstructed()
│    def _serialize_path()
│    def serialize()
│class DictionarySerializer
│    def serialize()
│class EnumSerializer
│    def serialize()
│class FloatSerializer
│    def serialize()
│class FrozensetSerializer
│    def _format()
│class FunctionTypeSerializer
│    def serialize()
│class FunctoolsPartialSerializer
│    def serialize()
│class IterableSerializer
│    def serialize()
│class ModelFieldSerializer
│    def serialize()
│class ModelManagerSerializer
│    def serialize()
│class OperationSerializer
│    def serialize()
│class PathLikeSerializer
│    def serialize()
│class PathSerializer
│    def serialize()
│class RegexSerializer
│    def serialize()
│class SequenceSerializer
│    def _format()
│class SetSerializer
│    def _format()
│class SettingsReferenceSerializer
│    def serialize()
│class TupleSerializer
│    def _format()
│class TypeSerializer
│    def serialize()
│class UUIDSerializer
│    def serialize()
│class ZoneInfoSerializer
│    def serialize()
│class Serializer
│    def register()
│    def unregister()
│def serializer_factory()
⋮...

│db/migrations/state.py:
⋮...
│def _get_app_label_and_model_name()
│def _get_related_models()
│def get_related_models_tuples()
│def get_related_models_recursive()
│class ProjectState
│    def __init__()
│    def relations()
│    def add_model()
│    def remove_model()
│    def rename_model()
│    def alter_model_options()
│    def remove_model_options()
│    def alter_model_managers()
│    def _append_option()
│    def _remove_option()
│    def _alter_option()
│    def add_index()
│    def remove_index()
│    def rename_index()
│    def add_constraint()
│    def remove_constraint()
│    def alter_constraint()
│    def add_field()
│    def remove_field()
│    def alter_field()
│    def rename_field()
│    def _find_reload_model()
│    def reload_model()
│    def reload_models()
│    def _reload()
│    def update_model_field_relation()
│    def resolve_model_field_relations()
│    def resolve_model_relations()
│    def resolve_fields_and_relations()
│    def get_concrete_model_key()
│    def _get_concrete_models_mapping_and_proxy_models()
│    def _find_concrete_model_from_proxy()
│    def clone()
│    def clear_delayed_apps_cache()
│    def apps()
│    def from_apps()
│    def __eq__()
│class AppConfigStub
│    def __init__()
│    def import_models()
│class StateApps
│    def __init__()
│    def bulk_update()
│    def render_multiple()
│    def clone()
│    def register_model()
│    def unregister_model()
│class ModelState
│    def __init__()
│    def name_lower()
│    def get_field()
│    def from_model()
│    def flatten_bases()
│    def construct_managers()
│    def clone()
│    def render()
│    def get_index_by_name()
│    def get_constraint_by_name()
│    def __repr__()
│    def __eq__()
⋮...

│db/migrations/utils.py:
⋮...
│class RegexObject
│    def __init__()
│    def __eq__()
│def get_migration_name_timestamp()
│def resolve_relation()
│def field_references()
│def get_references()
│def field_is_referenced()
⋮...

│db/migrations/writer.py:
⋮...
│class OperationWriter
│    def __init__()
│    def serialize()
│    def _write()
│    def indent()
│    def unindent()
│    def feed()
│    def render()
│class MigrationWriter
│    def __init__()
│    def as_string()
│    def basedir()
│    def filename()
│    def path()
│    def serialize()
│    def register_serializer()
│    def unregister_serializer()
⋮...

│db/models/__init__.py:
⋮...

│db/models/aggregates.py:
⋮...
│class AggregateFilter
│    def as_sql()
│    def condition()
│    def __str__()
│class AggregateOrderBy
│    def as_sql()
│class Aggregate
│    def __init__()
│    def get_source_fields()
│    def get_source_expressions()
│    def set_source_expressions()
│    def resolve_expression()
│    def default_alias()
│    def get_group_by_cols()
│    def as_sql()
│    def _get_repr_options()
│class Avg
│class Count
│    def __init__()
│    def resolve_expression()
│class Max
│class Min
│class StdDev
│    def __init__()
│    def _get_repr_options()
│class StringAggDelimiter
│    def __init__()
│    def as_mysql()
│class StringAgg
│    def __init__()
│    def as_oracle()
│    def as_mysql()
│    def as_sqlite()
│class Sum
│class Variance
│    def __init__()
│    def _get_repr_options()
⋮...

│db/models/base.py:
⋮...
│class Deferred
│    def __repr__()
│    def __str__()
│def subclass_exception()
│def _has_contribute_to_class()
│class ModelBase
│    def __new__()
│    def add_to_class()
│    def _prepare()
│    def _base_manager()
│    def _default_manager()
│class ModelStateFieldsCacheDescriptor
│    def __get__()
│class ModelState
│class Model
│    def __init__()
│    def from_db()
│    def __repr__()
│    def __str__()
│    def __eq__()
│    def __hash__()
│    def __reduce__()
│    def __getstate__()
│    def __setstate__()
│    def _get_pk_val()
│    def _set_pk_val()
│    def _is_pk_set()
│    def get_deferred_fields()
│    def refresh_from_db()
│def async arefresh_from_db()
│def serializable_value()
│def save()
│def async asave()
│def _validate_force_insert()
│def save_base()
│def _save_parents()
│def _save_table()
│def _do_update()
│def _do_insert()
│def _prepare_related_fields_for_save()
│def delete()
│def async adelete()
│def _get_FIELD_display()
│def _get_next_or_previous_by_FIELD()
│def _get_next_or_previous_in_order()
│def _get_field_expression_map()
│def prepare_database_save()
│def clean()
│def validate_unique()
│def _get_unique_checks()
│def _perform_unique_checks()
│def _perform_date_checks()
│def date_error_message()
│def unique_error_message()
│def get_constraints()
│def validate_constraints()
│def full_clean()
│def clean_fields()
│def check()
│def _check_default_pk()
│def _check_composite_pk()
│def _check_db_table_comment()
│def _check_swappable()
│def _check_model()
│def _check_managers()
│def _check_fields()
│def _check_m2m_through_same_relationship()
│def _check_id_field()
│def _check_field_name_clashes()
│def _check_column_name_clashes()
│def _check_model_name_db_lookup_clashes()
│def _check_property_name_related_field_accessor_clashes()
│def _check_single_primary_key()
│def _check_unique_together()
│def _check_indexes()
│def _check_local_fields()
│def _check_ordering()
│def _check_long_column_names()
│def _get_expr_references()
│def _check_constraints()
│def method_set_order()
│def method_get_order()
│def make_foreign_order_accessors()
│def model_unpickle()
⋮...

│db/models/constants.py:
⋮...
│class OnConflict
⋮...

│db/models/constraints.py:
⋮...
│class BaseConstraint
│    def __init__()
│    def contains_expressions()
│    def constraint_sql()
│    def create_sql()
│    def remove_sql()
│    def _expression_refs_exclude()
│    def validate()
│    def get_violation_error_message()
│    def check()
│    def _check_references()
│    def deconstruct()
│    def clone()
│class CheckConstraint
│    def __init__()
│    def check()
│    def _get_check_sql()
│    def constraint_sql()
│    def create_sql()
│    def remove_sql()
│    def validate()
│    def __repr__()
│    def __eq__()
│    def deconstruct()
│class Deferrable
│    def __repr__()
│class UniqueConstraint
│    def __init__()
│    def contains_expressions()
│    def check()
│    def _get_condition_sql()
│    def _get_index_expressions()
│    def constraint_sql()
│    def create_sql()
│    def remove_sql()
│    def __repr__()
│    def __eq__()
│    def deconstruct()
│    def validate()
⋮...

│db/models/deletion.py:
⋮...
│class ProtectedError
│    def __init__()
│class RestrictedError
│    def __init__()
│def CASCADE()
│def PROTECT()
│def RESTRICT()
│def SET()
│def set_on_delete()
│def set_on_delete()
│def SET_NULL()
│def SET_DEFAULT()
│def DO_NOTHING()
│def get_candidate_relations_to_delete()
│class Collector
│    def __init__()
│    def add()
│    def add_dependency()
│    def add_field_update()
│    def add_restricted_objects()
│    def clear_restricted_objects_from_set()
│    def clear_restricted_objects_from_queryset()
│    def _has_signal_listeners()
│    def can_fast_delete()
│    def get_del_batches()
│    def collect()
│    def related_objects()
│    def instances_with_model()
│    def sort()
│    def delete()
⋮...

│db/models/enums.py:
⋮...
│class ChoicesType
│    def __new__()
│    def names()
│    def choices()
│    def labels()
│    def values()
│class Choices
│    def label()
│    def __repr__()
│class IntegerChoices
│class TextChoices
│    def _generate_next_value_()
⋮...

│db/models/expressions.py:
⋮...
│class SQLiteNumericMixin
│    def as_sqlite()
│class Combinable
│    def _combine()
│    def __neg__()
│    def __add__()
│    def __sub__()
│    def __mul__()
│    def __truediv__()
│    def __mod__()
│    def __pow__()
│    def __and__()
│    def bitand()
│    def bitleftshift()
│    def bitrightshift()
│    def __xor__()
│    def bitxor()
│    def __or__()
│    def bitor()
│    def __radd__()
│    def __rsub__()
│    def __rmul__()
│    def __rtruediv__()
│    def __rmod__()
│    def __rpow__()
│    def __rand__()
│    def __ror__()
│    def __rxor__()
│    def __invert__()
│class OutputFieldIsNoneError
│class BaseExpression
│    def __init__()
│    def __getstate__()
│    def get_db_converters()
│    def get_source_expressions()
│    def set_source_expressions()
│    def _parse_expressions()
│    def as_sql()
│    def contains_aggregate()
│    def contains_over_clause()
│    def contains_column_references()
│    def contains_subquery()
│    def resolve_expression()
│    def conditional()
│    def field()
│    def output_field()
│    def _output_field_or_none()
│    def _resolve_output_field()
│    def _convert_value_noop()
│    def convert_value()
│    def get_lookup()
│    def get_transform()
│    def relabeled_clone()
│    def replace_expressions()
│    def get_refs()
│    def copy()
│    def prefix_references()
│    def get_group_by_cols()
│    def get_source_fields()
│    def asc()
│    def desc()
│    def reverse_ordering()
│    def flatten()
│    def select_format()
│    def get_expression_for_validation()
│class Expression
│    def _constructor_signature()
│    def _identity()
│    def identity()
│    def __eq__()
│    def __hash__()
│def register_combinable_fields()
│def _resolve_combined_type()
│class CombinedExpression
│    def __init__()
│    def __repr__()
│    def __str__()
│    def get_source_expressions()
│    def set_source_expressions()
│    def _resolve_output_field()
│    def as_sql()
│    def resolve_expression()
│    def allowed_default()
│class DurationExpression
│    def compile()
│    def as_sql()
│    def as_sqlite()
│class TemporalSubtraction
│    def __init__()
│    def as_sql()
│class F
│    def __init__()
│    def __repr__()
│    def __getitem__()
│    def __contains__()
│    def resolve_expression()
│    def replace_expressions()
│    def asc()
│    def desc()
│    def __eq__()
│    def __hash__()
│    def copy()
│class ResolvedOuterRef
│    def as_sql()
│    def resolve_expression()
│    def relabeled_clone()
│    def get_group_by_cols()
│class OuterRef
│    def resolve_expression()
│    def relabeled_clone()
│class Sliced
│    def __init__()
│    def __repr__()
│    def resolve_expression()
│class Func
│    def __init__()
│    def __repr__()
│    def _get_repr_options()
│    def get_source_expressions()
│    def set_source_expressions()
│    def as_sql()
│    def copy()
│    def allowed_default()
│class Value
│    def __init__()
│    def __repr__()
│    def as_sql()
│    def resolve_expression()
│    def get_group_by_cols()
│    def _resolve_output_field()
│    def empty_result_set_value()
│class RawSQL
│    def __init__()
│    def __repr__()
│    def as_sql()
│    def get_group_by_cols()
│    def resolve_expression()
│class Star
│    def __repr__()
│    def as_sql()
│class DatabaseDefault
│    def __init__()
│    def get_source_expressions()
│    def set_source_expressions()
│    def resolve_expression()
│    def as_sql()
│class Col
│    def __init__()
│    def __repr__()
│    def as_sql()
│    def relabeled_clone()
│    def get_group_by_cols()
│    def get_db_converters()
│class ColPairs
│    def __init__()
│    def __len__()
│    def __iter__()
│    def __repr__()
│    def get_cols()
│    def get_source_expressions()
│    def set_source_expressions()
│    def as_sql()
│    def relabeled_clone()
│    def resolve_expression()
│    def select_format()
│class Ref
│    def __init__()
│    def __repr__()
│    def get_source_expressions()
│    def set_source_expressions()
│    def resolve_expression()
│    def get_refs()
│    def relabeled_clone()
│    def as_sql()
│    def get_group_by_cols()
│class ExpressionList
│    def __str__()
│    def as_sql()
│    def as_sqlite()
│    def get_group_by_cols()
│class OrderByList
│    def __init__()
│    def from_param()
│class ExpressionWrapper
│    def __init__()
│    def set_source_expressions()
│    def get_source_expressions()
│    def get_group_by_cols()
│    def as_sql()
│    def __repr__()
│    def allowed_default()
│class NegatedExpression
│    def __init__()
│    def __invert__()
│    def as_sql()
│    def resolve_expression()
│    def select_format()
│class When
│    def __init__()
│    def __str__()
│    def __repr__()
│    def get_source_expressions()
│    def set_source_expressions()
│    def get_source_fields()
│    def as_sql()
│    def get_group_by_cols()
│    def allowed_default()
│class Case
│    def __init__()
│    def __str__()
│    def __repr__()
│    def get_source_expressions()
│    def set_source_expressions()
│    def copy()
│    def as_sql()
│    def get_group_by_cols()
│    def allowed_default()
│class Subquery
│    def __init__()
│    def get_source_expressions()
│    def set_source_expressions()
│    def _resolve_output_field()
│    def copy()
│    def external_aliases()
│    def get_external_cols()
│    def as_sql()
│    def get_group_by_cols()
│class Exists
│    def __init__()
│    def select_format()
│    def as_sql()
│class OrderBy
│    def __init__()
│    def __repr__()
│    def set_source_expressions()
│    def get_source_expressions()
│    def as_sql()
│    def as_oracle()
│    def get_group_by_cols()
│    def reverse_ordering()
│    def asc()
│    def desc()
│class Window
│    def __init__()
│    def _resolve_output_field()
│    def get_source_expressions()
│    def set_source_expressions()
│    def as_sql()
│    def as_sqlite()
│    def __str__()
│    def __repr__()
│    def get_group_by_cols()
│class WindowFrameExclusion
│    def __repr__()
│class WindowFrame
│    def __init__()
│    def set_source_expressions()
│    def get_source_expressions()
│    def get_exclusion()
│    def as_sql()
│    def __repr__()
│    def get_group_by_cols()
│    def __str__()
│    def window_frame_start_end()
│class RowRange
│    def window_frame_start_end()
│class ValueRange
│    def window_frame_start_end()
⋮...

│db/models/fields/__init__.py:
⋮...
│class Empty
│class NOT_PROVIDED
│def _load_field()
│def _empty()
│def return_None()
│class Field
│    def _description()
│    def __init__()
│    def __str__()
│    def __repr__()
│    def check()
│    def _check_field_name()
│    def _choices_is_value()
│    def _check_choices()
│    def _check_db_default()
│    def _check_db_index()
│    def _check_db_comment()
│    def _check_null_allowed_for_primary_keys()
│    def _check_backend_specific_checks()
│    def _check_validators()
│    def _check_deprecation_details()
│    def get_col()
│    def choices()
│    def choices()
│    def cached_col()
│    def select_format()
│    def deconstruct()
│    def clone()
│    def __eq__()
│    def __lt__()
│    def __hash__()
│    def __deepcopy__()
│    def __copy__()
│    def __reduce__()
│    def get_pk_value_on_save()
│    def to_python()
│    def error_messages()
│    def validators()
│    def run_validators()
│    def validate()
│    def clean()
│    def db_type_parameters()
│    def db_check()
│    def db_type()
│    def rel_db_type()
│    def cast_db_type()
│    def db_parameters()
│    def db_type_suffix()
│    def get_db_converters()
│    def unique()
│    def db_tablespace()
│    def db_returning()
│    def set_attributes_from_name()
│    def contribute_to_class()
│    def get_filter_kwargs_for_object()
│    def get_attname()
│    def get_attname_column()
│    def get_internal_type()
│    def pre_save()
│    def get_prep_value()
│    def get_db_prep_value()
│    def get_db_prep_save()
│    def has_default()
│    def has_db_default()
│    def get_default()
│    def _get_default()
│    def _db_default_expression()
│    def get_choices()
│    def value_to_string()
│    def flatchoices()
│    def save_form_data()
│    def formfield()
│    def value_from_object()
│    def slice_expression()
│class BooleanField
│    def get_internal_type()
│    def to_python()
│    def get_prep_value()
│    def formfield()
│class CharField
│    def __init__()
│    def description()
│    def check()
│    def _check_max_length_attribute()
│    def _check_db_collation()
│    def cast_db_type()
│    def db_parameters()
│    def get_internal_type()
│    def to_python()
│    def get_prep_value()
│    def formfield()
│    def deconstruct()
│    def slice_expression()
│class CommaSeparatedIntegerField
│def _to_naive()
│def _get_naive_now()
│class DateTimeCheckMixin
│    def check()
│    def _check_mutually_exclusive_options()
│    def _check_fix_default_value()
│    def _check_if_value_fixed()
│class DateField
│    def __init__()
│    def _check_fix_default_value()
│    def deconstruct()
│    def get_internal_type()
│    def to_python()
│    def pre_save()
│    def contribute_to_class()
│    def get_prep_value()
│    def get_db_prep_value()
│    def value_to_string()
│    def formfield()
│class DateTimeField
│    def _check_fix_default_value()
│    def get_internal_type()
│    def to_python()
│    def pre_save()
│    def get_prep_value()
│    def get_db_prep_value()
│    def value_to_string()
│    def formfield()
│class DecimalField
│    def __init__()
│    def check()
│    def _check_decimal_places()
│    def _check_max_digits()
│    def _check_decimal_places_and_max_digits()
│    def validators()
│    def context()
│    def deconstruct()
│    def get_internal_type()
│    def to_python()
│    def get_db_prep_value()
│    def get_prep_value()
│    def formfield()
│class DurationField
│    def get_internal_type()
│    def to_python()
│    def get_db_prep_value()
│    def get_db_converters()
│    def value_to_string()
│    def formfield()
│class EmailField
│    def __init__()
│    def deconstruct()
│    def formfield()
│class FilePathField
│    def __init__()
│    def check()
│    def _check_allowing_files_or_folders()
│    def deconstruct()
│    def get_prep_value()
│    def formfield()
│    def get_internal_type()
│class FloatField
│    def get_prep_value()
│    def get_internal_type()
│    def to_python()
│    def formfield()
│class IntegerField
│    def check()
│    def _check_max_length_warning()
│    def validators()
│    def get_prep_value()
│    def get_db_prep_value()
│    def get_internal_type()
│    def to_python()
│    def formfield()
│class BigIntegerField
│    def get_internal_type()
│    def formfield()
│class SmallIntegerField
│    def get_internal_type()
│class IPAddressField
│    def __init__()
│    def deconstruct()
│    def get_prep_value()
│    def get_internal_type()
│class GenericIPAddressField
│    def __init__()
│    def check()
│    def _check_blank_and_null_values()
│    def deconstruct()
│    def get_internal_type()
│    def to_python()
│    def get_db_prep_value()
│    def get_prep_value()
│    def formfield()
│class NullBooleanField
│    def __init__()
│    def deconstruct()
│class PositiveIntegerRelDbTypeMixin
│    def __init_subclass__()
│    def rel_db_type()
│class PositiveBigIntegerField
│    def get_internal_type()
│    def formfield()
│class PositiveIntegerField
│    def get_internal_type()
│    def formfield()
│class PositiveSmallIntegerField
│    def get_internal_type()
│    def formfield()
│class SlugField
│    def __init__()
│    def deconstruct()
│    def get_internal_type()
│    def formfield()
│class TextField
│    def __init__()
│    def check()
│    def _check_db_collation()
│    def db_parameters()
│    def get_internal_type()
│    def to_python()
│    def get_prep_value()
│    def formfield()
│    def deconstruct()
│    def slice_expression()
│class TimeField
│    def __init__()
│    def _check_fix_default_value()
│    def deconstruct()
│    def get_internal_type()
│    def to_python()
│    def pre_save()
│    def get_prep_value()
│    def get_db_prep_value()
│    def value_to_string()
│    def formfield()
│class URLField
│    def __init__()
│    def deconstruct()
│    def formfield()
│class BinaryField
│    def __init__()
│    def check()
│    def _check_str_default_value()
│    def deconstruct()
│    def get_internal_type()
│    def get_placeholder()
│    def get_default()
│    def get_db_prep_value()
│    def value_to_string()
│    def to_python()
│class UUIDField
│    def __init__()
│    def deconstruct()
│    def get_internal_type()
│    def get_prep_value()
│    def get_db_prep_value()
│    def to_python()
│    def formfield()
│class AutoFieldMixin
│    def __init__()
│    def check()
│    def _check_primary_key()
│    def deconstruct()
│    def validate()
│    def get_db_prep_value()
│    def contribute_to_class()
│    def formfield()
│class AutoFieldMeta
│    def _subclasses()
│    def __instancecheck__()
│    def __subclasscheck__()
│class AutoField
│    def get_internal_type()
│    def rel_db_type()
│class BigAutoField
│    def get_internal_type()
│    def rel_db_type()
│class SmallAutoField
│    def get_internal_type()
│    def rel_db_type()
⋮...

│db/models/fields/composite.py:
⋮...
│class AttributeSetter
│    def __init__()
│class CompositeAttribute
│    def __init__()
│    def attnames()
│    def __get__()
│    def __set__()
│class CompositePrimaryKey
│    def __init__()
│    def deconstruct()
│    def fields()
│    def columns()
│    def contribute_to_class()
│    def get_attname_column()
│    def __iter__()
│    def __len__()
│    def cached_col()
│    def get_col()
│    def get_pk_value_on_save()
│    def _check_field_name()
│    def value_to_string()
│    def to_python()
│def unnest()
⋮...

│db/models/fields/files.py:
⋮...
│class FieldFile
│    def __init__()
│    def __eq__()
│    def __hash__()
│    def _require_file()
│    def _get_file()
│    def _set_file()
│    def _del_file()
│    def path()
│    def url()
│    def size()
│    def open()
│    def _set_instance_attribute()
│    def save()
│    def delete()
│    def closed()
│    def close()
│    def __getstate__()
│    def __setstate__()
│class FileDescriptor
│    def __get__()
│    def __set__()
│class FileField
│    def __init__()
│    def check()
│    def _check_primary_key()
│    def _check_upload_to()
│    def deconstruct()
│    def get_internal_type()
│    def get_prep_value()
│    def pre_save()
│    def contribute_to_class()
│    def generate_filename()
│    def save_form_data()
│    def formfield()
│class ImageFileDescriptor
│    def __set__()
│class ImageFieldFile
│    def _set_instance_attribute()
│    def delete()
│class ImageField
│    def __init__()
│    def check()
│    def _check_image_library_installed()
│    def deconstruct()
│    def contribute_to_class()
│    def update_dimension_fields()
│    def formfield()
⋮...

│db/models/fields/generated.py:
⋮...
│class GeneratedField
│    def __init__()
│    def cached_col()
│    def get_col()
│    def contribute_to_class()
│    def generated_sql()
│    def check()
│    def _check_supported()
│    def _check_persistence()
│    def deconstruct()
│    def get_internal_type()
│    def db_parameters()
│    def db_type_parameters()
⋮...

│db/models/fields/json.py:
⋮...
│class JSONField
│    def __init__()
│    def check()
│    def _check_supported()
│    def deconstruct()
│    def from_db_value()
│    def get_internal_type()
│    def get_db_prep_value()
│    def get_db_prep_save()
│    def get_transform()
│    def validate()
│    def value_to_string()
│    def formfield()
│def compile_json_path()
│class DataContains
│    def as_sql()
│class ContainedBy
│    def as_sql()
│class HasKeyLookup
│    def compile_json_path_final_key()
│    def _as_sql_parts()
│    def _combine_sql_parts()
│    def as_sql()
│    def as_mysql()
│    def as_oracle()
│    def as_postgresql()
│    def as_sqlite()
│class HasKey
│class HasKeys
│    def get_prep_lookup()
│class HasAnyKeys
│class HasKeyOrArrayIndex
│    def compile_json_path_final_key()
│class CaseInsensitiveMixin
│    def process_lhs()
│    def process_rhs()
│class JSONExact
│    def process_rhs()
│    def as_oracle()
│class JSONIContains
│class KeyTransform
│    def __init__()
│    def preprocess_lhs()
│    def as_mysql()
│    def as_oracle()
│    def as_postgresql()
│    def as_sqlite()
│class KeyTextTransform
│    def as_mysql()
│    def from_lookup()
│class KeyTransformTextLookupMixin
│    def __init__()
│class KeyTransformIsNull
│    def as_oracle()
│    def as_sqlite()
│class KeyTransformIn
│    def resolve_expression_parameter()
│class KeyTransformExact
│    def process_rhs()
│    def as_oracle()
│class KeyTransformIExact
│class KeyTransformIContains
│class KeyTransformStartsWith
│class KeyTransformIStartsWith
│class KeyTransformEndsWith
│class KeyTransformIEndsWith
│class KeyTransformRegex
│class KeyTransformIRegex
│class KeyTransformNumericLookupMixin
│    def process_rhs()
│class KeyTransformLt
│class KeyTransformLte
│class KeyTransformGt
│class KeyTransformGte
│class KeyTransformFactory
│    def __init__()
│    def __call__()
⋮...

│db/models/fields/mixins.py:
⋮...
│class FieldCacheMixin
│    def cache_name()
│    def get_cached_value()
│    def is_cached()
│    def set_cached_value()
│    def delete_cached_value()
│class CheckFieldDefaultMixin
│    def _check_default()
│    def check()
⋮...

│db/models/fields/proxy.py:
⋮...
│class OrderWrt
│    def __init__()
⋮...

│db/models/fields/related.py:
⋮...
│def resolve_relation()
│def lazy_related_operation()
│class RelatedField
│    def __init__()
│    def related_model()
│    def check()
│    def _check_related_name_is_valid()
│    def _check_related_query_name_is_valid()
│    def _check_relation_model_exists()
│    def _check_referencing_to_swapped_model()
│    def _check_clashes()
│    def db_type()
│    def contribute_to_class()
│    def resolve_related_class()
│    def deconstruct()
│    def get_forward_related_filter()
│    def get_reverse_related_filter()
│    def swappable_setting()
│    def set_attributes_from_rel()
│    def do_related_class()
│    def get_limit_choices_to()
│    def formfield()
│    def related_query_name()
│    def target_field()
│    def cache_name()
│class ForeignObject
│    def __init__()
│    def __copy__()
│    def check()
│    def _check_to_fields_exist()
│    def _check_to_fields_composite_pk()
│    def _check_unique_target()
│    def _check_conflict_with_managers()
│    def deconstruct()
│    def resolve_related_fields()
│    def related_fields()
│    def reverse_related_fields()
│    def local_related_fields()
│    def foreign_related_fields()
│    def get_local_related_value()
│    def get_foreign_related_value()
│    def get_instance_value_for_fields()
│    def get_attname_column()
│    def get_joining_fields()
│    def get_reverse_joining_fields()
│    def get_extra_descriptor_filter()
│    def get_extra_restriction()
│    def get_path_info()
│    def path_infos()
│    def get_reverse_path_info()
│    def reverse_path_infos()
│    def get_class_lookups()
│    def contribute_to_class()
│    def contribute_to_related_class()
│class ForeignKey
│    def __init__()
│    def __class_getitem__()
│    def check()
│    def _check_on_delete()
│    def _check_unique()
│    def deconstruct()
│    def to_python()
│    def target_field()
│    def validate()
│    def resolve_related_fields()
│    def get_attname()
│    def get_attname_column()
│    def get_default()
│    def get_db_prep_save()
│    def get_db_prep_value()
│    def get_prep_value()
│    def contribute_to_related_class()
│    def formfield()
│    def db_check()
│    def db_type()
│    def cast_db_type()
│    def db_parameters()
│    def convert_empty_strings()
│    def get_db_converters()
│    def get_col()
│class OneToOneField
│    def __init__()
│    def deconstruct()
│    def formfield()
│    def save_form_data()
│    def _check_unique()
│def create_many_to_many_intermediary_model()
│def set_managed()
│class ManyToManyField
│    def __init__()
│    def check()
│    def _check_unique()
│    def _check_ignored_options()
│    def _check_relationship_model()
│    def _check_table_uniqueness()
│    def _get_field_name()
│    def deconstruct()
│    def _get_path_info()
│    def get_path_info()
│    def path_infos()
│    def get_reverse_path_info()
│    def reverse_path_infos()
│    def _get_m2m_db_table()
│    def _get_m2m_attr()
│    def _get_m2m_reverse_attr()
│    def contribute_to_class()
│    def resolve_through_model()
│    def contribute_to_related_class()
│    def set_attributes_from_rel()
│    def value_from_object()
│    def save_form_data()
│    def formfield()
│    def db_check()
│    def db_type()
│    def db_parameters()
⋮...

│db/models/fields/related_descriptors.py:
⋮...
│class ForeignKeyDeferredAttribute
│    def __set__()
│def _filter_prefetch_queryset()
│class ForwardManyToOneDescriptor
│    def __init__()
│    def RelatedObjectDoesNotExist()
│    def is_cached()
│    def get_queryset()
│    def get_prefetch_querysets()
│    def get_object()
│    def __get__()
│    def __set__()
│    def __reduce__()
│class ForwardOneToOneDescriptor
│    def get_object()
│    def __set__()
│class ReverseOneToOneDescriptor
│    def __init__()
│    def RelatedObjectDoesNotExist()
│    def is_cached()
│    def get_queryset()
│    def get_prefetch_querysets()
│    def __get__()
│    def __set__()
│    def __reduce__()
│class ReverseManyToOneDescriptor
│    def __init__()
│    def related_manager_cls()
│    def __get__()
│    def _get_set_deprecation_msg_params()
│    def __set__()
│def create_reverse_many_to_one_manager()
│class RelatedManager
│    def __init__()
│    def __call__()
│    def _check_fk_val()
│    def _apply_rel_filters()
│    def _remove_prefetched_objects()
│    def get_queryset()
│    def get_prefetch_querysets()
│    def add()
│    def check_and_update_obj()
│def async aadd()
│def create()
│def async acreate()
│def get_or_create()
│def async aget_or_create()
│def update_or_create()
│def async aupdate_or_create()
│def remove()
│def async aremove()
│def clear()
│def async aclear()
│def _clear()
│def set()
│def async aset()
│class ManyToManyDescriptor
│    def __init__()
│    def through()
│    def related_manager_cls()
│    def _get_set_deprecation_msg_params()
│def create_forward_many_to_many_manager()
│class ManyRelatedManager
│    def __init__()
│    def __call__()
│    def _build_remove_filters()
│    def _apply_rel_filters()
│    def get_prefetch_cache()
│    def _remove_prefetched_objects()
│    def get_queryset()
│    def get_prefetch_querysets()
│    def constrained_target()
│    def exists()
│    def count()
│    def add()
│def async aadd()
│def remove()
│def async aremove()
│def clear()
│def async aclear()
│def set()
│def async aset()
│def create()
│def async acreate()
│def get_or_create()
│def async aget_or_create()
│def update_or_create()
│def async aupdate_or_create()
│def _get_target_ids()
│def _get_missing_target_ids()
│def _get_add_plan()
│def _add_items()
│def _remove_items()
⋮...

│db/models/fields/related_lookups.py:
⋮...
│def get_normalized_value()
│class RelatedIn
│    def get_prep_lookup()
│    def as_sql()
│class RelatedLookupMixin
│    def get_prep_lookup()
│    def as_sql()
│class RelatedExact
│class RelatedLessThan
│class RelatedGreaterThan
│class RelatedGreaterThanOrEqual
│class RelatedLessThanOrEqual
│class RelatedIsNull
⋮...

│db/models/fields/reverse_related.py:
⋮...
│class ForeignObjectRel
│    def __init__()
│    def hidden()
│    def name()
│    def remote_field()
│    def target_field()
│    def related_model()
│    def many_to_many()
│    def many_to_one()
│    def one_to_many()
│    def one_to_one()
│    def get_lookup()
│    def get_lookups()
│    def get_transform()
│    def get_internal_type()
│    def db_type()
│    def __repr__()
│    def identity()
│    def __eq__()
│    def __hash__()
│    def __getstate__()
│    def get_choices()
│    def get_joining_fields()
│    def get_extra_restriction()
│    def set_field_name()
│    def accessor_name()
│    def get_accessor_name()
│    def get_path_info()
│    def path_infos()
│    def cache_name()
│class ManyToOneRel
│    def __init__()
│    def __getstate__()
│    def identity()
│    def get_related_field()
│    def set_field_name()
│class OneToOneRel
│    def __init__()
│class ManyToManyRel
│    def __init__()
│    def identity()
│    def get_related_field()
⋮...

│db/models/fields/tuple_lookups.py:
⋮...
│class Tuple
│    def __len__()
│    def __iter__()
│    def as_sqlite()
│class TupleLookupMixin
│    def get_prep_lookup()
│    def check_rhs_is_tuple_or_list()
│    def check_rhs_length_equals_lhs_length()
│    def check_rhs_is_supported_expression()
│    def get_lhs_str()
│    def get_prep_lhs()
│    def process_lhs()
│    def process_rhs()
│    def get_fallback_sql()
│    def as_sql()
│class TupleExact
│    def get_fallback_sql()
│class TupleIsNull
│    def get_prep_lookup()
│    def as_sql()
│class TupleGreaterThan
│    def get_fallback_sql()
│class TupleGreaterThanOrEqual
│    def get_fallback_sql()
│class TupleLessThan
│    def get_fallback_sql()
│class TupleLessThanOrEqual
│    def get_fallback_sql()
│class TupleIn
│    def get_prep_lookup()
│    def check_rhs_is_collection_of_tuples_or_lists()
│    def check_rhs_elements_length_equals_lhs_length()
│    def check_rhs_is_query()
│    def process_rhs()
│    def get_fallback_sql()
⋮...

│db/models/functions/__init__.py:
⋮...

│db/models/functions/comparison.py:
⋮...
│class Cast
│    def __init__()
│    def as_sql()
│    def as_sqlite()
│    def as_mysql()
│    def as_postgresql()
│    def as_oracle()
│class Coalesce
│    def __init__()
│    def empty_result_set_value()
│    def as_oracle()
│class Collate
│    def __init__()
│    def as_sql()
│class Greatest
│    def __init__()
│    def as_sqlite()
│class Least
│    def __init__()
│    def as_sqlite()
│class NullIf
│    def as_oracle()
⋮...

│db/models/functions/datetime.py:
⋮...
│class TimezoneMixin
│    def get_tzname()
│class Extract
│    def __init__()
│    def as_sql()
│    def resolve_expression()
│class ExtractYear
│class ExtractIsoYear
│class ExtractMonth
│class ExtractDay
│class ExtractWeek
│class ExtractWeekDay
│class ExtractIsoWeekDay
│class ExtractQuarter
│class ExtractHour
│class ExtractMinute
│class ExtractSecond
│class Now
│    def as_postgresql()
│    def as_mysql()
│    def as_sqlite()
│    def as_oracle()
│class TruncBase
│    def __init__()
│    def as_sql()
│    def resolve_expression()
│    def convert_value()
│class Trunc
│    def __init__()
│class TruncYear
│class TruncQuarter
│class TruncMonth
│class TruncWeek
│class TruncDay
│class TruncDate
│    def as_sql()
│class TruncTime
│    def as_sql()
│class TruncHour
│class TruncMinute
│class TruncSecond
⋮...

│db/models/functions/json.py:
⋮...
│class JSONArray
│    def as_sql()
│    def as_native()
│    def as_postgresql()
│    def as_oracle()
│class JSONObject
│    def __init__()
│    def as_sql()
│    def join()
│    def as_native()
│    def as_postgresql()
│    def as_oracle()
⋮...

│db/models/functions/math.py:
⋮...
│class Abs
│class ACos
│class ASin
│class ATan
│class ATan2
│    def as_sqlite()
│class Ceil
│    def as_oracle()
│class Cos
│class Cot
│    def as_oracle()
│class Degrees
│    def as_oracle()
│class Exp
│class Floor
│class Ln
│class Log
│    def as_sqlite()
│class Mod
│class Pi
│    def as_oracle()
│class Power
│class Radians
│    def as_oracle()
│class Random
│    def as_mysql()
│    def as_oracle()
│    def as_sqlite()
│    def get_group_by_cols()
│class Round
│    def __init__()
│    def as_sqlite()
│    def _resolve_output_field()
│class Sign
│class Sin
│class Sqrt
│class Tan
⋮...

│db/models/functions/mixins.py:
⋮...
│class FixDecimalInputMixin
│    def as_postgresql()
│class FixDurationInputMixin
│    def as_mysql()
│    def as_oracle()
│class NumericOutputFieldMixin
│    def _resolve_output_field()
⋮...

│db/models/functions/text.py:
⋮...
│class MySQLSHA2Mixin
│    def as_mysql()
│class OracleHashMixin
│    def as_oracle()
│class PostgreSQLSHAMixin
│    def as_postgresql()
│class Chr
│    def as_mysql()
│    def as_oracle()
│    def as_sqlite()
│class ConcatPair
│    def pipes_concat_sql()
│    def as_postgresql()
│    def as_mysql()
│    def coalesce()
│class Concat
│    def __init__()
│    def _paired()
│class Left
│    def __init__()
│    def get_substr()
│    def as_oracle()
│    def as_sqlite()
│class Length
│    def as_mysql()
│class Lower
│class LPad
│    def __init__()
│class LTrim
│class MD5
│class Ord
│    def as_mysql()
│    def as_sqlite()
│class Repeat
│    def __init__()
│    def as_oracle()
│class Replace
│    def __init__()
│class Reverse
│    def as_oracle()
│class Right
│    def get_substr()
│class RPad
│class RTrim
│class SHA1
│class SHA224
│    def as_oracle()
│class SHA256
│class SHA384
│class SHA512
│class StrIndex
│    def as_postgresql()
│class Substr
│    def __init__()
│    def as_sqlite()
│    def as_oracle()
│class Trim
│class Upper
⋮...

│db/models/functions/window.py:
⋮...
│class CumeDist
│class DenseRank
│class FirstValue
│class LagLeadFunction
│    def __init__()
│    def _resolve_output_field()
│class Lag
│class LastValue
│class Lead
│class NthValue
│    def __init__()
│    def _resolve_output_field()
│class Ntile
│    def __init__()
│class PercentRank
│class Rank
│class RowNumber
⋮...

│db/models/indexes.py:
⋮...
│class Index
│    def __init__()
│    def contains_expressions()
│    def _get_condition_sql()
│    def create_sql()
│    def remove_sql()
│    def deconstruct()
│    def clone()
│    def set_name_with_model()
│    def __repr__()
│    def __eq__()
│class IndexExpression
│    def set_wrapper_classes()
│    def register_wrappers()
│    def resolve_expression()
│    def as_sqlite()
⋮...

│db/models/lookups.py:
⋮...
│class Lookup
│    def __init__()
│    def apply_bilateral_transforms()
│    def __repr__()
│    def batch_process_rhs()
│    def get_source_expressions()
│    def set_source_expressions()
│    def get_prep_lookup()
│    def get_prep_lhs()
│    def get_db_prep_lookup()
│    def process_lhs()
│    def process_rhs()
│    def rhs_is_direct_value()
│    def get_group_by_cols()
│    def as_oracle()
│    def output_field()
│    def identity()
│    def __eq__()
│    def __hash__()
│    def resolve_expression()
│    def select_format()
│    def allowed_default()
│class Transform
│    def lhs()
│    def get_bilateral_transforms()
│class BuiltinLookup
│    def process_lhs()
│    def as_sql()
│    def get_rhs_op()
│class FieldGetDbPrepValueMixin
│    def get_db_prep_lookup()
│class FieldGetDbPrepValueIterableMixin
│    def get_prep_lookup()
│    def process_rhs()
│    def resolve_expression_parameter()
│    def batch_process_rhs()
│class PostgresOperatorLookup
│    def as_postgresql()
│class Exact
│    def get_prep_lookup()
│    def as_sql()
│class IExact
│    def process_rhs()
│class GreaterThan
│class GreaterThanOrEqual
│class LessThan
│class LessThanOrEqual
│class IntegerFieldOverflow
│    def process_rhs()
│class IntegerFieldFloatRounding
│    def get_prep_lookup()
│class IntegerFieldExact
│class IntegerGreaterThan
│class IntegerGreaterThanOrEqual
│class IntegerLessThan
│class IntegerLessThanOrEqual
│class In
│    def get_prep_lookup()
│    def process_rhs()
│    def get_rhs_op()
│    def as_sql()
│    def split_parameter_list_as_sql()
│class PatternLookup
│    def get_rhs_op()
│    def process_rhs()
│class Contains
│class IContains
│class StartsWith
│class IStartsWith
│class EndsWith
│class IEndsWith
│class Range
│    def get_rhs_op()
│class IsNull
│    def as_sql()
│class Regex
│    def as_sql()
│class IRegex
│class YearLookup
│    def year_lookup_bounds()
│    def as_sql()
│    def get_direct_rhs_sql()
│    def get_bound_params()
│class YearExact
│    def get_direct_rhs_sql()
│    def get_bound_params()
│class YearGt
│    def get_bound_params()
│class YearGte
│    def get_bound_params()
│class YearLt
│    def get_bound_params()
│class YearLte
│    def get_bound_params()
│class UUIDTextMixin
│    def process_rhs()
│class UUIDIExact
│class UUIDContains
│class UUIDIContains
│class UUIDStartsWith
│class UUIDIStartsWith
│class UUIDEndsWith
│class UUIDIEndsWith
⋮...

│db/models/manager.py:
⋮...
│class BaseManager
│    def __new__()
│    def __init__()
│    def __str__()
│    def __class_getitem__()
│    def deconstruct()
│    def check()
│    def _get_queryset_methods()
│    def create_method()
│    def manager_method()
│    def from_queryset()
│    def contribute_to_class()
│    def _set_creation_counter()
│    def db_manager()
│    def db()
│    def get_queryset()
│    def all()
│    def __eq__()
│    def __hash__()
│class Manager
│class ManagerDescriptor
│    def __init__()
│    def __get__()
│class EmptyManager
│    def __init__()
│    def get_queryset()
⋮...

│db/models/options.py:
⋮...
│def normalize_together()
│def make_immutable_fields_list()
│class Options
│    def __init__()
│    def label()
│    def label_lower()
│    def app_config()
│    def contribute_to_class()
│    def _format_names()
│    def _get_default_pk_class()
│    def _prepare()
│    def add_manager()
│    def add_field()
│    def setup_pk()
│    def setup_proxy()
│    def __repr__()
│    def __str__()
│    def can_migrate()
│    def verbose_name_raw()
│    def swapped()
│    def setting_changed()
│    def managers()
│    def managers_map()
│    def base_manager()
│    def default_manager()
│    def fields()
│    def is_not_an_m2m_field()
│    def is_not_a_generic_relation()
│    def is_not_a_generic_foreign_key()
│    def concrete_fields()
│    def local_concrete_fields()
│    def many_to_many()
│    def related_objects()
│    def _forward_fields_map()
│    def fields_map()
│    def get_field()
│    def get_base_chain()
│    def all_parents()
│    def get_parent_list()
│    def get_ancestor_link()
│    def get_path_to_parent()
│    def get_path_from_parent()
│    def _populate_directed_relation_graph()
│    def _relation_tree()
│    def _expire_cache()
│    def get_fields()
│    def _get_fields()
│    def total_unique_constraints()
│    def pk_fields()
│    def is_composite_pk()
│    def _property_names()
│    def _non_pk_concrete_field_names()
│    def _reverse_one_to_one_field_names()
│    def db_returning_fields()
⋮...

│db/models/query.py:
⋮...
│class BaseIterable
│    def __init__()
│def async _async_generator()
│def next_slice()
│def __aiter__()
│class ModelIterable
│    def __iter__()
│class RawModelIterable
│    def __iter__()
│class ValuesIterable
│    def __iter__()
│class ValuesListIterable
│    def __iter__()
│class NamedValuesListIterable
│    def __iter__()
│class FlatValuesListIterable
│    def __iter__()
│class QuerySet
│    def __init__()
│    def query()
│    def query()
│    def as_manager()
│    def __deepcopy__()
│    def __getstate__()
│    def __setstate__()
│    def __repr__()
│    def __len__()
│    def __iter__()
│    def __aiter__()
│def async generator()
│def __bool__()
│def __getitem__()
│def __class_getitem__()
│def __and__()
│def __or__()
│def __xor__()
│def _iterator()
│def iterator()
│def async aiterator()
│def aggregate()
│def async aaggregate()
│def count()
│def async acount()
│def get()
│def async aget()
│def create()
│def async acreate()
│def _prepare_for_bulk_create()
│def _check_bulk_create_options()
│def bulk_create()
│def async abulk_create()
│def bulk_update()
│def async abulk_update()
│def get_or_create()
│def async aget_or_create()
│def update_or_create()
│def async aupdate_or_create()
│def _extract_model_params()
│def _earliest()
│def earliest()
│def async aearliest()
│def latest()
│def async alatest()
│def first()
│def async afirst()
│def last()
│def async alast()
│def in_bulk()
│def async ain_bulk()
│def delete()
│def async adelete()
│def _raw_delete()
│def update()
│def async aupdate()
│def _update()
│def exists()
│def async aexists()
│def contains()
│def async acontains()
│def _prefetch_related_objects()
│def explain()
│def async aexplain()
│def raw()
│def _values()
│def values()
│def values_list()
│def dates()
│def datetimes()
│def none()
│def all()
│def filter()
│def exclude()
│def _filter_or_exclude()
│def _filter_or_exclude_inplace()
│def complex_filter()
│def _combinator_query()
│def union()
│def intersection()
│def difference()
│def select_for_update()
│def select_related()
│def prefetch_related()
│def annotate()
│def alias()
│def _annotate()
│def order_by()
│def distinct()
│def extra()
│def reverse()
│def defer()
│def only()
│def using()
│def ordered()
│def db()
│def _insert()
│def _batched_insert()
│def _chain()
│def _clone()
│def _fetch_all()
│def _next_is_sticky()
│def _merge_sanity_check()
│def _merge_known_related_objects()
│def resolve_expression()
│def _add_hints()
│def _has_filters()
│def _validate_values_are_expressions()
│def _not_support_combined_queries()
│def _check_operator_queryset()
│def _check_ordering_first_last_queryset_aggregation()
│class InstanceCheckMeta
│    def __instancecheck__()
│class EmptyQuerySet
│    def __init__()
│class RawQuerySet
│    def __init__()
│    def resolve_model_init_order()
│    def prefetch_related()
│    def _prefetch_related_objects()
│    def _clone()
│    def _fetch_all()
│    def __len__()
│    def __bool__()
│    def __iter__()
│    def __aiter__()
│def async generator()
│def iterator()
│def __repr__()
│def __getitem__()
│def db()
│def using()
│def columns()
│def model_fields()
│class Prefetch
│    def __init__()
│    def __getstate__()
│    def add_prefix()
│    def get_current_prefetch_to()
│    def get_current_to_attr()
│    def get_current_querysets()
│    def __eq__()
│    def __hash__()
│def normalize_prefetch_lookups()
│def prefetch_related_objects()
│def async aprefetch_related_objects()
│def get_prefetcher()
│def is_to_attr_fetched()
│def has_cached_property()
│def has_to_attr_attribute()
│def in_prefetched_cache()
│def prefetch_one_level()
│class RelatedPopulator
│    def __init__()
│    def populate()
│def get_related_populators()
⋮...

│db/models/query_utils.py:
⋮...
│def subclasses()
│class Q
│    def __init__()
│    def _combine()
│    def __or__()
│    def __and__()
│    def __xor__()
│    def __invert__()
│    def resolve_expression()
│    def flatten()
│    def check()
│    def deconstruct()
│    def identity()
│    def __eq__()
│    def __hash__()
│    def referenced_base_fields()
│class DeferredAttribute
│    def __init__()
│    def __get__()
│    def _check_parent_chain()
│class class_or_instance_method
│    def __init__()
│    def __get__()
│class RegisterLookupMixin
│    def _get_lookup()
│    def get_class_lookups()
│    def get_instance_lookups()
│    def get_lookup()
│    def get_transform()
│    def merge_dicts()
│    def _clear_cached_class_lookups()
│    def register_class_lookup()
│    def register_instance_lookup()
│    def _unregister_class_lookup()
│    def _unregister_instance_lookup()
│def select_related_descend()
│def refs_expression()
│def check_rel_lookup_compatibility()
│def check()
│class FilteredRelation
│    def __init__()
│    def __eq__()
│    def clone()
│    def relabeled_clone()
│    def resolve_expression()
│    def as_sql()
⋮...

│db/models/signals.py:
⋮...
│class ModelSignal
│    def _lazy_method()
│    def connect()
│    def disconnect()
⋮...

│db/models/sql/__init__.py:
⋮...

│db/models/sql/compiler.py:
⋮...
│class PositionRef
│    def __init__()
│    def as_sql()
│class SQLCompiler
│    def __init__()
│    def __repr__()
│    def setup_query()
│    def pre_sql_setup()
│    def get_group_by()
│    def collapse_group_by()
│    def get_select()
│    def get_select_from_parent()
│    def _order_by_pairs()
│    def get_order_by()
│    def get_extra_select()
│    def quote_name_unless_alias()
│    def compile()
│    def get_combinator_sql()
│    def _get_combinator_part_sql()
│    def get_qualify_sql()
│    def collect_replacements()
│    def as_sql()
│    def get_default_columns()
│    def get_distinct()
│    def find_ordering_name()
│    def _setup_joins()
│    def get_from_clause()
│    def get_related_selections()
│    def _get_field_choices()
│    def get_related_klass_infos()
│    def local_setter()
│    def local_setter_noop()
│    def remote_setter()
│    def get_select_for_update_of_arguments()
│    def _get_parent_klass_info()
│    def _get_first_selected_col_from_model()
│    def _get_field_choices()
│    def get_converters()
│    def apply_converters()
│    def has_composite_fields()
│    def composite_fields_to_tuples()
│    def results_iter()
│    def has_results()
│    def execute_sql()
│    def explain_query()
│class SQLInsertCompiler
│    def field_as_sql()
│    def prepare_value()
│    def pre_save_val()
│    def assemble_as_sql()
│    def as_sql()
│    def execute_sql()
│class SQLDeleteCompiler
│    def single_alias()
│    def _expr_refs_base_model()
│    def contains_self_reference_subquery()
│    def _as_sql()
│    def as_sql()
│class SQLUpdateCompiler
│    def as_sql()
│    def execute_sql()
│    def pre_sql_setup()
│class SQLAggregateCompiler
│    def as_sql()
│def cursor_iter()
⋮...

│db/models/sql/constants.py:
⋮...

│db/models/sql/datastructures.py:
⋮...
│class MultiJoin
│    def __init__()
│class Empty
│class Join
│    def __init__()
│    def as_sql()
│    def relabeled_clone()
│    def identity()
│    def __eq__()
│    def __hash__()
│    def demote()
│    def promote()
│class BaseTable
│    def __init__()
│    def as_sql()
│    def relabeled_clone()
│    def identity()
│    def __eq__()
│    def __hash__()
⋮...

│db/models/sql/query.py:
⋮...
│def get_field_names_from_opts()
│def get_paths_from_expression()
│def get_children_from_q()
│def get_child_with_renamed_prefix()
│def rename_prefix_from_q()
│class RawQuery
│    def __init__()
│    def chain()
│    def clone()
│    def get_columns()
│    def __iter__()
│    def __repr__()
│    def params_type()
│    def __str__()
│    def _execute_query()
│class Query
│    def __init__()
│    def output_field()
│    def base_table()
│    def __str__()
│    def sql_with_params()
│    def __deepcopy__()
│    def get_compiler()
│    def get_meta()
│    def clone()
│    def chain()
│    def relabeled_clone()
│    def _get_col()
│    def get_aggregation()
│    def get_count()
│    def has_filters()
│    def exists()
│    def has_results()
│    def explain()
│    def combine()
│    def _get_defer_select_mask()
│    def _get_only_select_mask()
│    def get_select_mask()
│    def table_alias()
│    def ref_alias()
│    def unref_alias()
│    def promote_joins()
│    def demote_joins()
│    def reset_refcounts()
│    def change_aliases()
│    def bump_prefix()
│    def prefix_gen()
│    def get_initial_alias()
│    def count_active_tables()
│    def join()
│    def join_parent_model()
│    def check_alias()
│    def add_annotation()
│    def _subquery_fields_len()
│    def resolve_expression()
│    def get_external_cols()
│    def get_group_by_cols()
│    def as_sql()
│    def resolve_lookup_value()
│    def solve_lookup_type()
│    def check_query_object_type()
│    def check_related_objects()
│    def check_filterable()
│    def build_lookup()
│    def try_transform()
│    def build_filter()
│    def add_filter()
│    def add_q()
│    def build_where()
│    def clear_where()
│    def _add_q()
│    def add_filtered_relation()
│    def names_to_path()
│    def setup_joins()
│    def final_transformer()
│    def transform()
│    def trim_joins()
│    def _gen_cols()
│    def _gen_col_aliases()
│    def resolve_ref()
│    def split_exclude()
│    def set_empty()
│    def is_empty()
│    def set_limits()
│    def clear_limits()
│    def is_sliced()
│    def has_limit_one()
│    def can_filter()
│    def clear_select_clause()
│    def clear_select_fields()
│    def add_select_col()
│    def set_select()
│    def add_distinct_fields()
│    def add_fields()
│    def add_ordering()
│    def clear_ordering()
│    def set_group_by()
│    def add_select_related()
│    def add_extra()
│    def clear_deferred_loading()
│    def add_deferred_loading()
│    def add_immediate_loading()
│    def set_annotation_mask()
│    def append_annotation_mask()
│    def set_extra_mask()
│    def has_select_fields()
│    def set_values()
│    def annotation_select()
│    def extra_select()
│    def trim_start()
│    def is_nullable()
│def get_order_dir()
│class JoinPromoter
│    def __init__()
│    def __repr__()
│    def add_votes()
│    def update_join_types()
⋮...

│db/models/sql/subqueries.py:
⋮...
│class DeleteQuery
│    def do_query()
│    def delete_batch()
│class UpdateQuery
│    def __init__()
│    def _setup_query()
│    def clone()
│    def update_batch()
│    def add_update_values()
│    def add_update_fields()
│    def add_related_update()
│    def get_related_updates()
│class InsertQuery
│    def __init__()
│    def insert_values()
│class AggregateQuery
│    def __init__()
⋮...

│db/models/sql/where.py:
⋮...
│class WhereNode
│    def split_having_qualify()
│    def as_sql()
│    def get_group_by_cols()
│    def get_source_expressions()
│    def set_source_expressions()
│    def relabel_aliases()
│    def clone()
│    def relabeled_clone()
│    def replace_expressions()
│    def get_refs()
│    def _contains_aggregate()
│    def contains_aggregate()
│    def _contains_over_clause()
│    def contains_over_clause()
│    def is_summary()
│    def _resolve_leaf()
│    def _resolve_node()
│    def resolve_expression()
│    def output_field()
│    def _output_field_or_none()
│    def select_format()
│    def get_db_converters()
│    def get_lookup()
│    def leaves()
│class NothingNode
│    def as_sql()
│class ExtraWhere
│    def __init__()
│    def as_sql()
⋮...

│db/models/utils.py:
⋮...
│def make_model_tuple()
│def resolve_callables()
│def unpickle_named_row()
│def create_namedtuple_class()
│def __reduce__()
│class AltersData
│    def __init_subclass__()
⋮...

│db/transaction.py:
⋮...
│class TransactionManagementError
│def get_connection()
│def get_autocommit()
│def set_autocommit()
│def commit()
│def rollback()
│def savepoint()
│def savepoint_rollback()
│def savepoint_commit()
│def clean_savepoints()
│def get_rollback()
│def set_rollback()
│def mark_for_rollback_on_error()
│def on_commit()
│class Atomic
│    def __init__()
│    def __enter__()
│    def __exit__()
│def atomic()
│def _non_atomic_requests()
│def non_atomic_requests()
⋮...

│db/utils.py:
⋮...
│class Error
│class InterfaceError
│class DatabaseError
│class DataError
│class OperationalError
│class IntegrityError
│class InternalError
│class ProgrammingError
│class NotSupportedError
│class DatabaseErrorWrapper
│    def __init__()
│    def __del__()
│    def __enter__()
│    def __exit__()
│    def __call__()
│    def inner()
│def load_backend()
│class ConnectionHandler
│    def configure_settings()
│    def databases()
│    def create_connection()
│class ConnectionRouter
│    def __init__()
│    def routers()
│    def _router_func()
│    def _route_db()
│    def allow_relation()
│    def allow_migrate()
│    def allow_migrate_model()
│    def get_migratable_models()
⋮...

│dispatch/dispatcher.py:
⋮...
│def _make_id()
│class Signal
│    def __init__()
│    def connect()
│    def disconnect()
│    def has_listeners()
│    def send()
│def async asend()
│def async asend()
│def sync_send()
│def async sync_send()
│def _log_robust_failure()
│def send_robust()
│def async asend_and_wrap_exception()
│def async asend()
│def async asend_robust()
│def sync_send()
│def async sync_send()
│def async asend_and_wrap_exception()
│def _clear_dead_receivers()
│def _live_receivers()
│def _remove_receiver()
│def receiver()
│def _decorator()
⋮...

│forms/boundfield.py:
⋮...
│class BoundField
│    def __init__()
│    def subwidgets()
│    def __bool__()
│    def __iter__()
│    def __len__()
│    def __getitem__()
│    def errors()
│    def template_name()
│    def get_context()
│    def as_widget()
│    def as_text()
│    def as_textarea()
│    def as_hidden()
│    def data()
│    def value()
│    def _has_changed()
│    def label_tag()
│    def legend_tag()
│    def css_classes()
│    def is_hidden()
│    def auto_id()
│    def id_for_label()
│    def initial()
│    def build_widget_attrs()
│    def aria_describedby()
│    def widget_type()
│    def use_fieldset()
│class BoundWidget
│    def __init__()
│    def __str__()
│    def tag()
│    def template_name()
│    def id_for_label()
│    def choice_label()
⋮...

│forms/fields.py:
⋮...
│class Field
│    def __init__()
│    def prepare_value()
│    def to_python()
│    def validate()
│    def run_validators()
│    def clean()
│    def bound_data()
│    def widget_attrs()
│    def has_changed()
│    def get_bound_field()
│    def __deepcopy__()
│    def _clean_bound_field()
│class CharField
│    def __init__()
│    def to_python()
│    def widget_attrs()
│class IntegerField
│    def __init__()
│    def to_python()
│    def widget_attrs()
│class FloatField
│    def to_python()
│    def validate()
│    def widget_attrs()
│class DecimalField
│    def __init__()
│    def to_python()
│    def validate()
│    def widget_attrs()
│class BaseTemporalField
│    def __init__()
│    def to_python()
│    def strptime()
│class DateField
│    def to_python()
│    def strptime()
│class TimeField
│    def to_python()
│    def strptime()
│class DateTimeFormatsIterator
│    def __iter__()
│class DateTimeField
│    def prepare_value()
│    def to_python()
│    def strptime()
│class DurationField
│    def prepare_value()
│    def to_python()
│class RegexField
│    def __init__()
│    def _get_regex()
│    def _set_regex()
│class EmailField
│    def __init__()
│class FileField
│    def __init__()
│    def to_python()
│    def clean()
│    def bound_data()
│    def has_changed()
│    def _clean_bound_field()
│class ImageField
│    def to_python()
│    def widget_attrs()
│class URLField
│    def __init__()
│    def to_python()
│    def split_url()
│class BooleanField
│    def to_python()
│    def validate()
│    def has_changed()
│class NullBooleanField
│    def to_python()
│    def validate()
│class ChoiceField
│    def __init__()
│    def __deepcopy__()
│    def choices()
│    def choices()
│    def to_python()
│    def validate()
│    def valid_value()
│class TypedChoiceField
│    def __init__()
│    def _coerce()
│    def clean()
│class MultipleChoiceField
│    def to_python()
│    def validate()
│    def has_changed()
│class TypedMultipleChoiceField
│    def __init__()
│    def _coerce()
│    def clean()
│    def validate()
│class ComboField
│    def __init__()
│    def clean()
│class MultiValueField
│    def __init__()
│    def __deepcopy__()
│    def validate()
│    def clean()
│    def compress()
│    def has_changed()
│class FilePathField
│    def __init__()
│class SplitDateTimeField
│    def __init__()
│    def compress()
│class GenericIPAddressField
│    def __init__()
│    def to_python()
│class SlugField
│    def __init__()
│class UUIDField
│    def prepare_value()
│    def to_python()
│class InvalidJSONInput
│class JSONString
│class JSONField
│    def __init__()
│    def to_python()
│    def bound_data()
│    def prepare_value()
│    def has_changed()
⋮...

│forms/forms.py:
⋮...
│class DeclarativeFieldsMetaclass
│    def __new__()
│class BaseForm
│    def __init__()
│    def order_fields()
│    def __repr__()
│    def _bound_items()
│    def __iter__()
│    def __getitem__()
│    def errors()
│    def is_valid()
│    def add_prefix()
│    def add_initial_prefix()
│    def _widget_data_value()
│    def template_name()
│    def get_context()
│    def non_field_errors()
│    def add_error()
│    def has_error()
│    def full_clean()
│    def _clean_fields()
│    def _clean_form()
│    def _post_clean()
│    def clean()
│    def has_changed()
│    def changed_data()
│    def media()
│    def is_multipart()
│    def hidden_fields()
│    def visible_fields()
│    def get_initial_for_field()
│class Form
⋮...

│forms/formsets.py:
⋮...
│class ManagementForm
│    def clean()
│class BaseFormSet
│    def __init__()
│    def __iter__()
│    def __getitem__()
│    def __len__()
│    def __bool__()
│    def __repr__()
│    def management_form()
│    def total_form_count()
│    def initial_form_count()
│    def forms()
│    def get_form_kwargs()
│    def _construct_form()
│    def initial_forms()
│    def extra_forms()
│    def empty_form()
│    def cleaned_data()
│    def deleted_forms()
│    def ordered_forms()
│    def compare_ordering_key()
│    def get_default_prefix()
│    def get_deletion_widget()
│    def get_ordering_widget()
│    def non_form_errors()
│    def errors()
│    def total_error_count()
│    def _should_delete_form()
│    def is_valid()
│    def full_clean()
│    def clean()
│    def has_changed()
│    def add_fields()
│    def add_prefix()
│    def is_multipart()
│    def media()
│    def template_name()
│    def get_context()
│def formset_factory()
│def all_valid()
⋮...

│forms/jinja2/django/forms/attrs.html:
⋮...

│forms/jinja2/django/forms/div.html:
⋮...

│forms/jinja2/django/forms/field.html:
⋮...

│forms/jinja2/django/forms/formsets/div.html:
⋮...

│forms/jinja2/django/forms/formsets/p.html:
⋮...

│forms/jinja2/django/forms/formsets/table.html:
⋮...

│forms/jinja2/django/forms/formsets/ul.html:
⋮...

│forms/jinja2/django/forms/p.html:
⋮...

│forms/jinja2/django/forms/table.html:
⋮...

│forms/jinja2/django/forms/ul.html:
⋮...

│forms/jinja2/django/forms/widgets/attrs.html:
⋮...

│forms/models.py:
⋮...
│def construct_instance()
│def model_to_dict()
│def apply_limit_choices_to_to_formfield()
│def fields_for_model()
│class ModelFormOptions
│    def __init__()
│class ModelFormMetaclass
│    def __new__()
│class BaseModelForm
│    def __init__()
│    def _get_validation_exclusions()
│    def clean()
│    def _update_errors()
│    def _post_clean()
│    def validate_unique()
│    def validate_constraints()
│    def _save_m2m()
│    def save()
│class ModelForm
│def modelform_factory()
│class BaseModelFormSet
│    def __init__()
│    def initial_form_count()
│    def _existing_object()
│    def _get_to_python()
│    def _construct_form()
│    def get_queryset()
│    def save_new()
│    def save_existing()
│    def delete_existing()
│    def save()
│    def save_m2m()
│    def clean()
│    def validate_unique()
│    def get_unique_error_message()
│    def get_date_error_message()
│    def get_form_error()
│    def save_existing_objects()
│    def save_new_objects()
│    def add_fields()
│    def pk_is_not_editable()
│def modelformset_factory()
│class BaseInlineFormSet
│    def __init__()
│    def initial_form_count()
│    def _construct_form()
│    def get_default_prefix()
│    def save_new()
│    def add_fields()
│    def get_unique_error_message()
│def _get_foreign_key()
│def inlineformset_factory()
│class InlineForeignKeyField
│    def __init__()
│    def clean()
│    def has_changed()
│class ModelChoiceIteratorValue
│    def __init__()
│    def __str__()
│    def __hash__()
│    def __eq__()
│class ModelChoiceIterator
│    def __init__()
│    def __iter__()
│    def __len__()
│    def __bool__()
│    def choice()
│class ModelChoiceField
│    def __init__()
│    def validate_no_null_characters()
│    def get_limit_choices_to()
│    def __deepcopy__()
│    def _get_queryset()
│    def _set_queryset()
│    def label_from_instance()
│    def _get_choices()
│    def prepare_value()
│    def to_python()
│    def validate()
│    def has_changed()
│class ModelMultipleChoiceField
│    def __init__()
│    def to_python()
│    def clean()
│    def _check_values()
│    def prepare_value()
│    def has_changed()
│def modelform_defines_fields()
⋮...

│forms/renderers.py:
⋮...
│def get_default_renderer()
│class BaseRenderer
│    def get_template()
│    def render()
│class EngineMixin
│    def get_template()
│    def engine()
│class DjangoTemplates
│class Jinja2
│    def backend()
│class TemplatesSetting
│    def get_template()
⋮...

│forms/utils.py:
⋮...
│def pretty_name()
│def flatatt()
│class RenderableMixin
│    def get_context()
│    def render()
│class RenderableFieldMixin
│    def as_field_group()
│    def as_hidden()
│    def as_widget()
│    def __str__()
│class RenderableFormMixin
│    def as_p()
│    def as_table()
│    def as_ul()
│    def as_div()
│class RenderableErrorMixin
│    def as_json()
│    def as_text()
│    def as_ul()
│class ErrorDict
│    def __init__()
│    def as_data()
│    def get_json_data()
│    def get_context()
│class ErrorList
│    def __init__()
│    def as_data()
│    def copy()
│    def get_json_data()
│    def get_context()
│    def __repr__()
│    def __contains__()
│    def __eq__()
│    def __getitem__()
│    def __reduce_ex__()
│def from_current_timezone()
│def to_current_timezone()
⋮...

│forms/widgets.py:
⋮...
│class MediaOrderConflictWarning
│class MediaAsset
│    def __init__()
│    def __eq__()
│    def __hash__()
│    def __str__()
│    def __repr__()
│    def path()
│class Script
│    def __init__()
│class Media
│    def __init__()
│    def __repr__()
│    def __str__()
│    def _css()
│    def _js()
│    def render()
│    def render_js()
│    def render_css()
│    def absolute_path()
│    def __getitem__()
│    def merge()
│    def __add__()
│def media_property()
│def _media()
│class MediaDefiningClass
│    def __new__()
│class Widget
│    def __init__()
│    def __deepcopy__()
│    def is_hidden()
│    def subwidgets()
│    def format_value()
│    def get_context()
│    def render()
│    def _render()
│    def build_attrs()
│    def value_from_datadict()
│    def value_omitted_from_data()
│    def id_for_label()
│    def use_required_attribute()
│class Input
│    def __init__()
│    def get_context()
│class TextInput
│class NumberInput
│class EmailInput
│class URLInput
│class ColorInput
│class SearchInput
│class TelInput
│class PasswordInput
│    def __init__()
│    def get_context()
│class HiddenInput
│class MultipleHiddenInput
│    def get_context()
│    def value_from_datadict()
│    def format_value()
│class FileInput
│    def __init__()
│    def format_value()
│    def value_from_datadict()
│    def value_omitted_from_data()
│    def use_required_attribute()
│class ClearableFileInput
│    def clear_checkbox_name()
│    def clear_checkbox_id()
│    def is_initial()
│    def format_value()
│    def get_context()
│    def value_from_datadict()
│    def value_omitted_from_data()
│class Textarea
│    def __init__()
│class DateTimeBaseInput
│    def __init__()
│    def format_value()
│class DateInput
│class DateTimeInput
│class TimeInput
│def boolean_check()
│class CheckboxInput
│    def __init__()
│    def format_value()
│    def get_context()
│    def value_from_datadict()
│    def value_omitted_from_data()
│class ChoiceWidget
│    def __init__()
│    def __deepcopy__()
│    def subwidgets()
│    def options()
│    def optgroups()
│    def create_option()
│    def get_context()
│    def id_for_label()
│    def value_from_datadict()
│    def format_value()
│    def choices()
│    def choices()
│class Select
│    def get_context()
│    def _choice_has_empty_value()
│    def use_required_attribute()
│class NullBooleanSelect
│    def __init__()
│    def format_value()
│    def value_from_datadict()
│class SelectMultiple
│    def value_from_datadict()
│    def value_omitted_from_data()
│class RadioSelect
│    def id_for_label()
│class CheckboxSelectMultiple
│    def use_required_attribute()
│    def value_omitted_from_data()
│class MultiWidget
│    def __init__()
│    def is_hidden()
│    def get_context()
│    def id_for_label()
│    def value_from_datadict()
│    def value_omitted_from_data()
│    def decompress()
│    def _get_media()
│    def __deepcopy__()
│    def needs_multipart_form()
│class SplitDateTimeWidget
│    def __init__()
│    def decompress()
│class SplitHiddenDateTimeWidget
│    def __init__()
│class SelectDateWidget
│    def __init__()
│    def get_context()
│    def format_value()
│    def _parse_date_fmt()
│    def id_for_label()
│    def value_from_datadict()
│    def value_omitted_from_data()
⋮...

│http/__init__.py:
⋮...

│http/cookie.py:
⋮...
│def parse_cookie()
⋮...

│http/multipartparser.py:
⋮...
│class MultiPartParserError
│class InputStreamExhausted
│class MultiPartParser
│    def __init__()
│    def parse()
│    def _parse()
│    def handle_file_complete()
│    def sanitize_file_name()
│    def _close_files()
│class LazyStream
│    def __init__()
│    def tell()
│    def read()
│    def parts()
│    def __next__()
│    def close()
│    def __iter__()
│    def unget()
│    def _update_unget_history()
│class ChunkIter
│    def __init__()
│    def __next__()
│    def __iter__()
│class InterBoundaryIter
│    def __init__()
│    def __iter__()
│    def __next__()
│class BoundaryIter
│    def __init__()
│    def __iter__()
│    def __next__()
│    def _find_boundary()
│def exhaust()
│def parse_boundary_stream()
│class Parser
│    def __init__()
│    def __iter__()
⋮...

│http/request.py:
⋮...
│class UnreadablePostError
│class RawPostDataException
│class HttpRequest
│    def __init__()
│    def __repr__()
│    def headers()
│    def accepted_types()
│    def accepted_type()
│    def get_preferred_type()
│    def accepts()
│    def _set_content_type_params()
│    def _get_raw_host()
│    def get_host()
│    def get_port()
│    def get_full_path()
│    def get_full_path_info()
│    def _get_full_path()
│    def get_signed_cookie()
│    def build_absolute_uri()
│    def _current_scheme_host()
│    def _get_scheme()
│    def scheme()
│    def is_secure()
│    def encoding()
│    def encoding()
│    def _initialize_handlers()
│    def upload_handlers()
│    def upload_handlers()
│    def parse_file_upload()
│    def body()
│    def _mark_post_parse_error()
│    def _load_post_and_files()
│    def close()
│    def read()
│    def readline()
│    def __iter__()
│    def readlines()
│class HttpHeaders
│    def __init__()
│    def __getitem__()
│    def parse_header_name()
│    def to_wsgi_name()
│    def to_asgi_name()
│    def to_wsgi_names()
│    def to_asgi_names()
│class QueryDict
│    def __init__()
│    def fromkeys()
│    def encoding()
│    def encoding()
│    def _assert_mutable()
│    def __setitem__()
│    def __delitem__()
│    def __copy__()
│    def __deepcopy__()
│    def setlist()
│    def setlistdefault()
│    def appendlist()
│    def pop()
│    def popitem()
│    def clear()
│    def setdefault()
│    def copy()
│    def urlencode()
│    def encode()
│    def encode()
│class MediaType
│    def __init__()
│    def __str__()
│    def __repr__()
│    def is_all_types()
│    def match()
│    def quality()
│    def specificity()
│def bytes_to_text()
│def split_domain_port()
│def validate_host()
⋮...

│http/response.py:
⋮...
│class ResponseHeaders
│    def __init__()
│    def _convert_to_charset()
│    def __delitem__()
│    def __setitem__()
│    def pop()
│    def setdefault()
│class BadHeaderError
│class HttpResponseBase
│    def __init__()
│    def reason_phrase()
│    def reason_phrase()
│    def charset()
│    def charset()
│    def serialize_headers()
│    def _content_type_for_repr()
│    def __setitem__()
│    def __delitem__()
│    def __getitem__()
│    def has_header()
│    def items()
│    def get()
│    def set_cookie()
│    def setdefault()
│    def set_signed_cookie()
│    def delete_cookie()
│    def make_bytes()
│    def close()
│    def write()
│    def flush()
│    def tell()
│    def readable()
│    def seekable()
│    def writable()
│    def writelines()
│class HttpResponse
│    def __init__()
│    def __repr__()
│    def serialize()
│    def content()
│    def content()
│    def text()
│    def __iter__()
│    def write()
│    def tell()
│    def getvalue()
│    def writable()
│    def writelines()
│class StreamingHttpResponse
│    def __init__()
│    def __repr__()
│    def content()
│    def text()
│    def streaming_content()
│def async awrapper()
│def streaming_content()
│def _set_streaming_content()
│def __iter__()
│def async to_list()
│def async __aiter__()
│def getvalue()
│class FileResponse
│    def __init__()
│    def _set_streaming_content()
│    def set_headers()
│class HttpResponseRedirectBase
│    def __init__()
│    def __repr__()
│class HttpResponseRedirect
│class HttpResponsePermanentRedirect
│class HttpResponseNotModified
│    def __init__()
│    def content()
│class HttpResponseBadRequest
│class HttpResponseNotFound
│class HttpResponseForbidden
│class HttpResponseNotAllowed
│    def __init__()
│    def __repr__()
│class HttpResponseGone
│class HttpResponseServerError
│class Http404
│class JsonResponse
│    def __init__()
⋮...

│middleware/cache.py:
⋮...
│class UpdateCacheMiddleware
│    def __init__()
│    def cache()
│    def _should_update_cache()
│    def process_response()
│class FetchFromCacheMiddleware
│    def __init__()
│    def cache()
│    def process_request()
│class CacheMiddleware
│    def __init__()
⋮...

│middleware/clickjacking.py:
⋮...
│class XFrameOptionsMiddleware
│    def process_response()
│    def get_xframe_options_value()
⋮...

│middleware/common.py:
⋮...
│class CommonMiddleware
│    def process_request()
│    def should_redirect_with_slash()
│    def get_full_path_with_slash()
│    def process_response()
│class BrokenLinkEmailsMiddleware
│    def process_response()
│    def is_internal_request()
│    def is_ignorable_request()
⋮...

│middleware/csrf.py:
⋮...
│def _get_failure_view()
│def _get_new_csrf_string()
│def _mask_cipher_secret()
│def _unmask_cipher_token()
│def _add_new_csrf_cookie()
│def get_token()
│def rotate_token()
│class InvalidTokenFormat
│    def __init__()
│def _check_token_format()
│def _does_token_match()
│class RejectRequest
│    def __init__()
│class CsrfViewMiddleware
│    def csrf_trusted_origins_hosts()
│    def allowed_origins_exact()
│    def allowed_origin_subdomains()
│    def _accept()
│    def _reject()
│    def _get_secret()
│    def _set_csrf_cookie()
│    def _origin_verified()
│    def _check_referer()
│    def _bad_token_message()
│    def _check_token()
│    def process_request()
│    def process_view()
│    def process_response()
⋮...

│middleware/gzip.py:
⋮...
│class GZipMiddleware
│    def process_response()
│def async gzip_wrapper()
⋮...

│middleware/http.py:
⋮...
│class ConditionalGetMiddleware
│    def process_response()
│    def needs_etag()
⋮...

│middleware/locale.py:
⋮...
│class LocaleMiddleware
│    def process_request()
│    def process_response()
⋮...

│middleware/security.py:
⋮...
│class SecurityMiddleware
│    def __init__()
│    def process_request()
│    def process_response()
⋮...

│shortcuts.py:
⋮...
│def render()
│def redirect()
│def _get_queryset()
│def get_object_or_404()
│def async aget_object_or_404()
│def get_list_or_404()
│def async aget_list_or_404()
│def resolve_url()
⋮...

│template/__init__.py:
⋮...

│template/autoreload.py:
⋮...
│def get_template_directories()
│def reset_loaders()
│def watch_for_template_changes()
│def template_changed()
⋮...

│template/backends/base.py:
⋮...
│class BaseEngine
│    def __init__()
│    def check()
│    def app_dirname()
│    def from_string()
│    def get_template()
│    def template_dirs()
│    def iter_template_filenames()
⋮...

│template/backends/django.py:
⋮...
│class DjangoTemplates
│    def __init__()
│    def check()
│    def _check_string_if_invalid_is_string()
│    def _check_for_template_tags_with_the_same_name()
│    def from_string()
│    def get_template()
│    def get_templatetag_libraries()
│class Template
│    def __init__()
│    def origin()
│    def render()
│def copy_exception()
│def reraise()
│def get_template_tag_modules()
│def get_installed_libraries()
│def get_package_libraries()
⋮...

│template/backends/dummy.py:
⋮...
│class TemplateStrings
│    def __init__()
│    def from_string()
│    def get_template()
│class Template
│    def render()
⋮...

│template/backends/jinja2.py:
⋮...
│class Jinja2
│    def __init__()
│    def from_string()
│    def get_template()
│    def template_context_processors()
│class Template
│    def __init__()
│    def render()
│class Origin
│    def __init__()
│def get_exception_info()
⋮...

│template/backends/utils.py:
⋮...
│def csrf_input()
⋮...

│template/base.py:
⋮...
│class TokenType
│class VariableDoesNotExist
│    def __init__()
│    def __str__()
│class Origin
│    def __init__()
│    def __str__()
│    def __repr__()
│    def __eq__()
│    def loader_name()
│class Template
│    def __init__()
│    def __repr__()
│    def _render()
│    def render()
│    def compile_nodelist()
│    def get_exception_info()
│def linebreak_iter()
│class Token
│    def __init__()
│    def __repr__()
│    def split_contents()
│class Lexer
│    def __init__()
│    def __repr__()
│    def tokenize()
│    def create_token()
│class DebugLexer
│    def _tag_re_split_positions()
│    def _tag_re_split()
│    def tokenize()
│class Parser
│    def __init__()
│    def __repr__()
│    def parse()
│    def skip_past()
│    def extend_nodelist()
│    def error()
│    def invalid_block_tag()
│    def unclosed_block_tag()
│    def next_token()
│    def prepend_token()
│    def delete_first_token()
│    def add_library()
│    def compile_filter()
│    def find_filter()
│class FilterExpression
│    def __init__()
│    def resolve()
│    def args_check()
│    def __str__()
│    def __repr__()
│class Variable
│    def __init__()
│    def resolve()
│    def __repr__()
│    def __str__()
│    def _resolve_lookup()
│class Node
│    def render()
│    def render_annotated()
│    def get_nodes_by_type()
│class NodeList
│    def render()
│    def get_nodes_by_type()
│class TextNode
│    def __init__()
│    def __repr__()
│    def render()
│    def render_annotated()
│def render_value_in_context()
│class VariableNode
│    def __init__()
│    def __repr__()
│    def render()
│def token_kwargs()
⋮...

│template/context.py:
⋮...
│class ContextPopException
│class ContextDict
│    def __init__()
│    def __enter__()
│    def __exit__()
│class BaseContext
│    def __init__()
│    def _reset_dicts()
│    def __copy__()
│    def __repr__()
│    def __iter__()
│    def push()
│    def pop()
│    def __setitem__()
│    def set_upward()
│    def __getitem__()
│    def __delitem__()
│    def __contains__()
│    def get()
│    def setdefault()
│    def new()
│    def flatten()
│    def __eq__()
│class Context
│    def __init__()
│    def bind_template()
│    def __copy__()
│    def update()
│class RenderContext
│    def __iter__()
│    def __contains__()
│    def get()
│    def __getitem__()
│    def push_state()
│class RequestContext
│    def __init__()
│    def bind_template()
│    def new()
│def make_context()
⋮...

│template/context_processors.py:
⋮...
│def csrf()
│def _get_val()
│def debug()
│def i18n()
│def tz()
│def static()
│def media()
│def request()
⋮...

│template/defaultfilters.py:
⋮...
│def stringfilter()
│def _dec()
│def addslashes()
│def capfirst()
│def escapejs_filter()
│def json_script()
│def floatformat()
│def iriencode()
│def linenumbers()
│def lower()
│def make_list()
│def slugify()
│def stringformat()
│def title()
│def truncatechars()
│def truncatechars_html()
│def truncatewords()
│def truncatewords_html()
│def upper()
│def urlencode()
│def urlize()
│def urlizetrunc()
│def wordcount()
│def wordwrap()
│def ljust()
│def rjust()
│def center()
│def cut()
│def escape_filter()
│def escapeseq()
│def force_escape()
│def linebreaks_filter()
│def linebreaksbr()
│def safe()
│def safeseq()
│def striptags()
│def _property_resolver()
│def resolve()
│def dictsort()
│def dictsortreversed()
│def first()
│def join()
│def last()
│def length()
│def random()
│def slice_filter()
│def unordered_list()
│def escaper()
│def walk_items()
│def list_formatter()
│def add()
│def get_digit()
│def date()
│def time()
│def timesince_filter()
│def timeuntil_filter()
│def default()
│def default_if_none()
│def divisibleby()
│def yesno()
│def filesizeformat()
│def filesize_number_format()
│def pluralize()
│def phone2numeric_filter()
│def pprint()
⋮...

│template/defaulttags.py:
⋮...
│class AutoEscapeControlNode
│    def __init__()
│    def render()
│class CommentNode
│    def render()
│class CsrfTokenNode
│    def render()
│class CycleNode
│    def __init__()
│    def render()
│    def reset()
│class DebugNode
│    def render()
│class FilterNode
│    def __init__()
│    def render()
│class FirstOfNode
│    def __init__()
│    def render()
│class ForNode
│    def __init__()
│    def __repr__()
│    def render()
│class IfChangedNode
│    def __init__()
│    def render()
│    def _get_context_stack_frame()
│class IfNode
│    def __init__()
│    def __repr__()
│    def __iter__()
│    def nodelist()
│    def render()
│class LoremNode
│    def __init__()
│    def render()
│class RegroupNode
│    def __init__()
│    def resolve_expression()
│    def render()
│class LoadNode
│    def render()
│class NowNode
│    def __init__()
│    def render()
│class ResetCycleNode
│    def __init__()
│    def render()
│class SpacelessNode
│    def __init__()
│    def render()
│class TemplateTagNode
│    def __init__()
│    def render()
│class URLNode
│    def __init__()
│    def __repr__()
│    def render()
│class VerbatimNode
│    def __init__()
│    def render()
│class WidthRatioNode
│    def __init__()
│    def render()
│class WithNode
│    def __init__()
│    def __repr__()
│    def render()
│def autoescape()
│def comment()
│def cycle()
│def csrf_token()
│def debug()
│def do_filter()
│def firstof()
│def do_for()
│class TemplateLiteral
│    def __init__()
│    def display()
│    def eval()
│class TemplateIfParser
│    def __init__()
│    def create_var()
│def do_if()
│def ifchanged()
│def find_library()
│def load_from_library()
│def load()
│def lorem()
│def now()
│def querystring()
│def regroup()
│def resetcycle()
│def spaceless()
│def templatetag()
│def url()
│def verbatim()
│def widthratio()
│def do_with()
⋮...

│template/engine.py:
⋮...
│class Engine
│    def __init__()
│    def __repr__()
│    def get_default()
│    def template_context_processors()
│    def get_template_builtins()
│    def get_template_libraries()
│    def template_loaders()
│    def get_template_loaders()
│    def find_template_loader()
│    def find_template()
│    def from_string()
│    def get_template()
│    def render_to_string()
│    def select_template()
⋮...

│template/exceptions.py:
⋮...
│class TemplateDoesNotExist
│    def __init__()
│class TemplateSyntaxError
⋮...

│template/library.py:
⋮...
│class InvalidTemplateLibrary
│class Library
│    def __init__()
│    def tag()
│    def dec()
│    def tag_function()
│    def filter()
│    def dec()
│    def dec()
│    def filter_function()
│    def simple_tag()
│    def dec()
│    def compile_func()
│    def simple_block_tag()
│    def dec()
│    def compile_func()
│    def inclusion_tag()
│    def dec()
│    def compile_func()
│class TagHelperNode
│    def __init__()
│    def get_resolved_arguments()
│class SimpleNode
│    def __init__()
│    def render()
│class SimpleBlockNode
│    def __init__()
│    def get_resolved_arguments()
│class InclusionNode
│    def __init__()
│    def render()
│def parse_bits()
│def import_library()
⋮...

│template/loader.py:
⋮...
│def get_template()
│def select_template()
│def render_to_string()
│def _engine_list()
⋮...

│template/loader_tags.py:
⋮...
│class BlockContext
│    def __init__()
│    def __repr__()
│    def add_blocks()
│    def pop()
│    def push()
│    def get_block()
│class BlockNode
│    def __init__()
│    def __repr__()
│    def render()
│    def super()
│class ExtendsNode
│    def __init__()
│    def __repr__()
│    def find_template()
│    def get_parent()
│    def render()
│class IncludeNode
│    def __init__()
│    def __repr__()
│    def render()
│def do_block()
│def construct_relative_path()
│def do_extends()
│def do_include()
⋮...

│template/loaders/app_directories.py:
⋮...
│class Loader
│    def get_dirs()
⋮...

│template/loaders/base.py:
⋮...
│class Loader
│    def __init__()
│    def get_template()
│    def get_template_sources()
│    def reset()
⋮...

│template/loaders/cached.py:
⋮...
│class Loader
│    def __init__()
│    def get_dirs()
│    def get_contents()
│    def get_template()
│    def get_template_sources()
│    def cache_key()
│    def generate_hash()
│    def reset()
⋮...

│template/loaders/filesystem.py:
⋮...
│class Loader
│    def __init__()
│    def get_dirs()
│    def get_contents()
│    def get_template_sources()
⋮...

│template/loaders/locmem.py:
⋮...
│class Loader
│    def __init__()
│    def get_contents()
│    def get_template_sources()
⋮...

│template/response.py:
⋮...
│class ContentNotRenderedError
│class SimpleTemplateResponse
│    def __init__()
│    def __getstate__()
│    def resolve_template()
│    def resolve_context()
│    def rendered_content()
│    def add_post_render_callback()
│    def render()
│    def is_rendered()
│    def __iter__()
│    def content()
│    def content()
│class TemplateResponse
│    def __init__()
⋮...

│template/smartif.py:
⋮...
│class TokenBase
│    def nud()
│    def led()
│    def display()
│    def __repr__()
│def infix()
│class Operator
│    def led()
│    def eval()
│def prefix()
│class Operator
│    def nud()
│    def eval()
│class Literal
│    def __init__()
│    def display()
│    def nud()
│    def eval()
│    def __repr__()
│class EndToken
│    def nud()
│class IfParser
│    def __init__()
│    def translate_token()
│    def next_token()
│    def parse()
│    def expression()
│    def create_var()
⋮...

│template/utils.py:
⋮...
│class InvalidTemplateEngineError
│class EngineHandler
│    def __init__()
│    def templates()
│    def __getitem__()
│    def __iter__()
│    def all()
│def get_app_template_dirs()
⋮...

│templatetags/cache.py:
⋮...
│class CacheNode
│    def __init__()
│    def render()
│def do_cache()
⋮...

│templatetags/i18n.py:
⋮...
│class GetAvailableLanguagesNode
│    def __init__()
│    def render()
│class GetLanguageInfoNode
│    def __init__()
│    def render()
│class GetLanguageInfoListNode
│    def __init__()
│    def get_language_info()
│    def render()
│class GetCurrentLanguageNode
│    def __init__()
│    def render()
│class GetCurrentLanguageBidiNode
│    def __init__()
│    def render()
│class TranslateNode
│    def __init__()
│    def render()
│class BlockTranslateNode
│    def __init__()
│    def __repr__()
│    def render_token_list()
│    def render()
│    def render_value()
│class LanguageNode
│    def __init__()
│    def render()
│def do_get_available_languages()
│def do_get_language_info()
│def do_get_language_info_list()
│def language_name()
│def language_name_translated()
│def language_name_local()
│def language_bidi()
│def do_get_current_language()
│def do_get_current_language_bidi()
│def do_translate()
│def do_block_translate()
│def language()
⋮...

│templatetags/l10n.py:
⋮...
│def localize()
│def unlocalize()
│class LocalizeNode
│    def __init__()
│    def __repr__()
│    def render()
│def localize_tag()
⋮...

│templatetags/static.py:
⋮...
│class PrefixNode
│    def __repr__()
│    def __init__()
│    def handle_token()
│    def handle_simple()
│    def render()
│def get_static_prefix()
│def get_media_prefix()
│class StaticNode
│    def __init__()
│    def __repr__()
│    def url()
│    def render()
│    def handle_simple()
│    def handle_token()
│def do_static()
│def static()
⋮...

│templatetags/tz.py:
⋮...
│class datetimeobject
│def localtime()
│def utc()
│def do_timezone()
│class LocalTimeNode
│    def __init__()
│    def render()
│class TimezoneNode
│    def __init__()
│    def render()
│class GetCurrentTimezoneNode
│    def __init__()
│    def render()
│def localtime_tag()
│def timezone_tag()
│def get_current_timezone_tag()
⋮...

│test/__init__.py:
⋮...

│test/client.py:
⋮...
│class RedirectCycleError
│    def __init__()
│class FakePayload
│    def __init__()
│    def __len__()
│    def read()
│    def readline()
│    def write()
│def closing_iterator_wrapper()
│def async aclosing_iterator_wrapper()
│def conditional_content_removal()
│class ClientHandler
│    def __init__()
│    def __call__()
│class AsyncClientHandler
│    def __init__()
│def async __call__()
│def store_rendered_templates()
│def encode_multipart()
│def to_bytes()
│def is_file()
│def encode_file()
│def to_bytes()
│class RequestFactory
│    def __init__()
│    def _base_environ()
│    def request()
│    def _encode_data()
│    def _encode_json()
│    def _get_path()
│    def get()
│    def post()
│    def head()
│    def trace()
│    def options()
│    def put()
│    def patch()
│    def delete()
│    def generic()
│class AsyncRequestFactory
│    def _base_scope()
│    def request()
│    def generic()
│class ClientMixin
│    def store_exc_info()
│    def check_exception()
│    def session()
│def async asession()
│def login()
│def async alogin()
│def force_login()
│def async aforce_login()
│def _get_backend()
│def _login()
│def async _alogin()
│def _set_login_cookies()
│def logout()
│def async alogout()
│def _parse_json()
│def _follow_redirect()
│def _ensure_redirects_not_cyclic()
│class Client
│    def __init__()
│    def request()
│    def get()
│    def post()
│    def head()
│    def options()
│    def put()
│    def patch()
│    def delete()
│    def trace()
│    def _handle_redirects()
│class AsyncClient
│    def __init__()
│def async request()
│def async get()
│def async post()
│def async head()
│def async options()
│def async put()
│def async patch()
│def async delete()
│def async trace()
│def async _ahandle_redirects()
⋮...

│test/html.py:
⋮...
│def normalize_whitespace()
│def normalize_attributes()
│class Element
│    def __init__()
│    def append()
│    def finalize()
│    def rstrip_last_element()
│    def __eq__()
│    def __hash__()
│    def _count()
│    def __contains__()
│    def count()
│    def __getitem__()
│    def __str__()
│    def __repr__()
│class RootElement
│    def __init__()
│    def __str__()
│class HTMLParseError
│class Parser
│    def __init__()
│    def error()
│    def format_position()
│    def current()
│    def handle_startendtag()
│    def handle_starttag()
│    def handle_endtag()
│    def handle_data()
│def parse_html()
⋮...

│test/runner.py:
⋮...
│class DebugSQLTextTestResult
│    def __init__()
│    def startTest()
│    def stopTest()
│    def addError()
│    def addFailure()
│    def addSubTest()
│    def printErrorList()
│class PDBDebugResult
│    def addError()
│    def addFailure()
│    def addSubTest()
│    def debug()
│class DummyList
│    def append()
│class RemoteTestResult
│    def __init__()
│    def __getstate__()
│    def test_index()
│    def _confirm_picklable()
│    def _print_unpicklable_subtest()
│    def check_picklable()
│    def check_subtest_picklable()
│    def startTestRun()
│    def stopTestRun()
│    def startTest()
│    def stopTest()
│    def addDuration()
│    def addError()
│    def addFailure()
│    def addSubTest()
│    def addSuccess()
│    def addSkip()
│    def addExpectedFailure()
│    def addUnexpectedSuccess()
│    def wasSuccessful()
│    def _exc_info_to_string()
│class RemoteTestRunner
│    def __init__()
│    def run()
│def get_max_test_processes()
│def parallel_type()
│def _init_worker()
│def _run_subsuite()
│def _process_setup_stub()
│class ParallelTestSuite
│    def __init__()
│    def run()
│    def handle_event()
│    def __iter__()
│    def initialize_suite()
│class Shuffler
│    def _hash_text()
│    def __init__()
│    def seed_display()
│    def _hash_item()
│    def shuffle()
│class DiscoverRunner
│    def __init__()
│    def add_arguments()
│    def shuffle_seed()
│    def log()
│    def setup_test_environment()
│    def setup_shuffler()
│    def load_with_patterns()
│    def load_tests_for_label()
│    def build_suite()
│    def setup_databases()
│    def get_resultclass()
│    def get_test_runner_kwargs()
│    def run_checks()
│    def run_suite()
│    def teardown_databases()
│    def teardown_test_environment()
│    def suite_result()
│    def _get_databases()
│    def get_databases()
│    def run_tests()
│def try_importing()
│def find_top_level()
│def _class_shuffle_key()
│def shuffle_tests()
│def reorder_test_bin()
│def reorder_tests()
│def partition_suite_by_case()
│def test_match_tags()
│def filter_tests_by_tags()
⋮...

│test/selenium.py:
⋮...
│class SeleniumTestCaseBase
│    def __new__()
│    def import_webdriver()
│    def import_options()
│    def get_capability()
│    def create_options()
│    def create_webdriver()
│class ChangeWindowSize
│    def __init__()
│    def __enter__()
│    def __exit__()
│class SeleniumTestCase
│    def __init_subclass__()
│    def test()
│    def live_server_url()
│    def allowed_host()
│    def setUpClass()
│    def desktop_size()
│    def small_screen_size()
│    def mobile_size()
│    def rtl()
│    def dark()
│    def set_emulated_media()
│    def high_contrast()
│    def take_screenshot()
│    def _quit_selenium()
│    def disable_implicit_wait()
│def screenshot_cases()
│def wrapper()
⋮...

│test/signals.py:
⋮...
│def clear_cache_handlers()
│def update_installed_apps()
│def update_connections_time_zone()
│def clear_routers_cache()
│def reset_template_engines()
│def storages_changed()
│def clear_serializers_cache()
│def language_changed()
│def localize_settings_changed()
│def complex_setting_changed()
│def root_urlconf_changed()
│def static_storage_changed()
│def static_finders_changed()
│def form_renderer_changed()
│def auth_password_validators_changed()
│def user_model_swapped()
⋮...

│test/testcases.py:
⋮...
│def to_list()
│def is_pickable()
│def assert_and_parse_html()
│class _AssertNumQueriesContext
│    def __init__()
│    def __exit__()
│class _AssertTemplateUsedContext
│    def __init__()
│    def on_template_render()
│    def test()
│    def __enter__()
│    def __exit__()
│class _AssertTemplateNotUsedContext
│    def test()
│class DatabaseOperationForbidden
│class _DatabaseFailure
│    def __init__()
│    def __call__()
│class SimpleTestCase
│    def setUpClass()
│    def _validate_databases()
│    def _add_databases_failures()
│    def _remove_databases_failures()
│    def ensure_connection_patch_method()
│    def patched_ensure_connection()
│    def __call__()
│    def __getstate__()
│    def debug()
│    def _setup_and_call()
│    def _pre_setup()
│    def _post_teardown()
│    def settings()
│    def modify_settings()
│    def assertRedirects()
│    def assertURLEqual()
│    def normalize()
│    def _assert_contains()
│    def assertContains()
│    def assertNotContains()
│    def _check_test_client_response()
│    def _assert_form_error()
│    def assertFormError()
│    def assertFormSetError()
│    def _get_template_used()
│    def _assert_template_used()
│    def assertTemplateUsed()
│    def assertTemplateNotUsed()
│    def _assert_raises_or_warns_cm()
│    def _assertFooMessage()
│    def assertRaisesMessage()
│    def assertWarnsMessage()
│    def assertFieldOutput()
│    def assertHTMLEqual()
│    def assertHTMLNotEqual()
│    def assertInHTML()
│    def assertNotInHTML()
│    def assertJSONEqual()
│    def assertJSONNotEqual()
│    def assertXMLEqual()
│    def assertXMLNotEqual()
│class TransactionTestCase
│    def setUpClass()
│    def tearDownClass()
│    def _pre_setup()
│    def _databases_names()
│    def _reset_sequences()
│    def _fixture_setup()
│    def _should_reload_connections()
│    def _post_teardown()
│    def _fixture_teardown()
│    def assertQuerySetEqual()
│    def assertNumQueries()
│def connections_support_transactions()
│def connections_support_savepoints()
│class TestData
│    def __init__()
│    def get_memo()
│    def __get__()
│    def __repr__()
│class TestCase
│    def _enter_atomics()
│    def _rollback_atomics()
│    def _databases_support_transactions()
│    def _databases_support_savepoints()
│    def setUpClass()
│    def tearDownClass()
│    def setUpTestData()
│    def _should_reload_connections()
│    def _fixture_setup()
│    def _fixture_teardown()
│    def _should_check_constraints()
│    def captureOnCommitCallbacks()
│class CheckCondition
│    def __init__()
│    def add_condition()
│    def __get__()
│def _deferredSkip()
│def decorator()
│def skip_wrapper()
│def condition()
│def skipIfDBFeature()
│def skipUnlessDBFeature()
│def skipUnlessAnyDBFeature()
│class QuietWSGIRequestHandler
│    def log_message()
│class FSFilesHandler
│    def __init__()
│    def _should_handle()
│    def file_path()
│    def get_response()
│    def serve()
│    def __call__()
│class _StaticFilesHandler
│    def get_base_dir()
│    def get_base_url()
│class _MediaFilesHandler
│    def get_base_dir()
│    def get_base_url()
│class LiveServerThread
│    def __init__()
│    def run()
│    def _create_server()
│    def terminate()
│class LiveServerTestCase
│    def live_server_url()
│    def allowed_host()
│    def _make_connections_override()
│    def setUpClass()
│    def _start_server_thread()
│    def _create_server_thread()
│    def _terminate_thread()
│class SerializeMixin
│    def __init_subclass__()
│    def setUpClass()
⋮...

│test/utils.py:
⋮...
│class Approximate
│    def __init__()
│    def __repr__()
│    def __eq__()
│class ContextList
│    def __getitem__()
│    def get()
│    def __contains__()
│    def keys()
│def instrumented_test_render()
│class _TestState
│def setup_test_environment()
│def teardown_test_environment()
│def setup_databases()
│def iter_test_cases()
│def dependency_ordered()
│def get_unique_databases_and_mirrors()
│def teardown_databases()
│def get_runner()
│class TestContextDecorator
│    def __init__()
│    def enable()
│    def disable()
│    def __enter__()
│    def __exit__()
│    def decorate_class()
│    def setUp()
│    def decorate_callable()
│def async inner()
│def inner()
│def __call__()
│class override_settings
│    def __init__()
│    def enable()
│    def disable()
│    def save_options()
│    def decorate_class()
│class modify_settings
│    def __init__()
│    def save_options()
│    def enable()
│class override_system_checks
│    def __init__()
│    def enable()
│    def disable()
│def compare_xml()
│def norm_whitespace()
│def child_text()
│def children()
│def norm_child_text()
│def attrs_dict()
│def check_element()
│def first_node()
│class CaptureQueriesContext
│    def __init__()
│    def __iter__()
│    def __getitem__()
│    def __len__()
│    def captured_queries()
│    def __enter__()
│    def __exit__()
│class ignore_warnings
│    def __init__()
│    def enable()
│    def disable()
│def extend_sys_path()
│def isolate_lru_cache()
│def captured_output()
│def captured_stdout()
│def captured_stderr()
│def captured_stdin()
│def freeze_time()
│def require_jinja2()
│class override_script_prefix
│    def __init__()
│    def enable()
│    def disable()
│class LoggingCaptureMixin
│    def setUp()
│    def tearDown()
│class isolate_apps
│    def __init__()
│    def enable()
│    def disable()
│class TimeKeeper
│    def __init__()
│    def timed()
│    def print_results()
│class NullTimeKeeper
│    def timed()
│    def print_results()
│def tag()
│def decorator()
│def register_lookup()
│def garbage_collect()
⋮...

│urls/__init__.py:
⋮...

│urls/base.py:
⋮...
│def resolve()
│def reverse()
│def clear_url_caches()
│def set_script_prefix()
│def get_script_prefix()
│def clear_script_prefix()
│def set_urlconf()
│def get_urlconf()
│def is_valid_path()
│def translate_url()
⋮...

│urls/conf.py:
⋮...
│def include()
│def _path()
⋮...

│urls/converters.py:
⋮...
│class IntConverter
│    def to_python()
│    def to_url()
│class StringConverter
│    def to_python()
│    def to_url()
│class UUIDConverter
│    def to_python()
│    def to_url()
│class SlugConverter
│class PathConverter
│def register_converter()
│def get_converters()
⋮...

│urls/exceptions.py:
⋮...
│class Resolver404
│class NoReverseMatch
⋮...

│urls/resolvers.py:
⋮...
│class ResolverMatch
│    def __init__()
│    def __getitem__()
│    def __repr__()
│    def __reduce_ex__()
│def get_resolver()
│def _get_cached_resolver()
│def get_ns_resolver()
│class LocaleRegexDescriptor
│    def __get__()
│    def _compile()
│class CheckURLMixin
│    def describe()
│    def _check_pattern_startswith_slash()
│class RegexPattern
│    def __init__()
│    def match()
│    def check()
│    def _check_include_trailing_dollar()
│    def __str__()
│def _route_to_regex()
│class LocaleRegexRouteDescriptor
│    def __get__()
│class RoutePattern
│    def __init__()
│    def match()
│    def check()
│    def _check_pattern_unmatched_angle_brackets()
│    def __str__()
│class LocalePrefixPattern
│    def __init__()
│    def regex()
│    def language_prefix()
│    def match()
│    def check()
│    def describe()
│    def __str__()
│class URLPattern
│    def __init__()
│    def __repr__()
│    def check()
│    def _check_pattern_name()
│    def _check_callback()
│    def resolve()
│    def lookup_str()
│class URLResolver
│    def __init__()
│    def __repr__()
│    def check()
│    def _populate()
│    def reverse_dict()
│    def namespace_dict()
│    def app_dict()
│    def _extend_tried()
│    def _join_route()
│    def _is_callback()
│    def resolve()
│    def urlconf_module()
│    def url_patterns()
│    def resolve_error_handler()
│    def reverse()
│    def _reverse_with_prefix()
⋮...

│urls/utils.py:
⋮...
│def get_callable()
│def get_mod_func()
⋮...

│utils/_os.py:
⋮...
│def safe_join()
│def symlinks_supported()
│def to_path()
⋮...

│utils/archive.py:
⋮...
│class ArchiveException
│class UnrecognizedArchiveFormat
│def extract()
│class Archive
│    def __init__()
│    def _archive_cls()
│    def __enter__()
│    def __exit__()
│    def extract()
│    def list()
│    def close()
│class BaseArchive
│    def _copy_permissions()
│    def split_leading_dir()
│    def has_leading_dir()
│    def target_filename()
│    def extract()
│    def list()
│class TarArchive
│    def __init__()
│    def list()
│    def extract()
│    def close()
│class ZipArchive
│    def __init__()
│    def list()
│    def extract()
│    def close()
⋮...

│utils/asyncio.py:
⋮...
│def async_unsafe()
│def decorator()
│def inner()
⋮...

│utils/autoreload.py:
⋮...
│def is_django_module()
│def is_django_path()
│def check_errors()
│def wrapper()
│def raise_last_exception()
│def ensure_echo_on()
│def iter_all_python_module_files()
│def iter_modules_and_files()
│def common_roots()
│def _walk()
│def sys_path_directories()
│def get_child_arguments()
│def trigger_reload()
│def restart_with_reloader()
│class BaseReloader
│    def __init__()
│    def watch_dir()
│    def watched_files()
│    def wait_for_apps_ready()
│    def run()
│    def run_loop()
│    def tick()
│    def check_availability()
│    def notify_file_changed()
│    def should_stop()
│    def stop()
│class StatReloader
│    def tick()
│    def snapshot_files()
│    def check_availability()
│class WatchmanUnavailable
│class WatchmanReloader
│    def __init__()
│    def client()
│    def _watch_root()
│    def _get_clock()
│    def _subscribe()
│    def _subscribe_dir()
│    def _watch_glob()
│    def watched_roots()
│    def _update_watches()
│    def update_watches()
│    def _check_subscription()
│    def request_processed()
│    def tick()
│    def stop()
│    def check_server_status()
│    def check_availability()
│def get_reloader()
│def start_django()
│def run_with_reloader()
⋮...

│utils/cache.py:
⋮...
│def patch_cache_control()
│def dictitem()
│def dictvalue()
│def get_max_age()
│def set_response_etag()
│def _precondition_failed()
│def _not_modified()
│def get_conditional_response()
│def _if_match_passes()
│def _if_unmodified_since_passes()
│def _if_none_match_passes()
│def _if_modified_since_passes()
│def patch_response_headers()
│def add_never_cache_headers()
│def patch_vary_headers()
│def has_vary_header()
│def _i18n_cache_key_suffix()
│def _generate_cache_key()
│def _generate_cache_header_key()
│def get_cache_key()
│def learn_cache_key()
│def _to_tuple()
⋮...

│utils/choices.py:
⋮...
│class BaseChoiceIterator
│    def __eq__()
│    def __getitem__()
│    def __iter__()
│class BlankChoiceIterator
│    def __init__()
│    def __iter__()
│class CallableChoiceIterator
│    def __init__()
│    def __iter__()
│def flatten_choices()
│def normalize_choices()
⋮...

│utils/connection.py:
⋮...
│class ConnectionProxy
│    def __init__()
│    def __getattr__()
│    def __setattr__()
│    def __delattr__()
│    def __contains__()
│    def __eq__()
│class ConnectionDoesNotExist
│class BaseConnectionHandler
│    def __init__()
│    def settings()
│    def configure_settings()
│    def create_connection()
│    def __getitem__()
│    def __setitem__()
│    def __delitem__()
│    def __iter__()
│    def all()
│    def close_all()
⋮...

│utils/copy.py:
⋮...
│def replace()
⋮...

│utils/crypto.py:
⋮...
│class InvalidAlgorithm
│def salted_hmac()
│def get_random_string()
│def constant_time_compare()
│def pbkdf2()
⋮...

│utils/datastructures.py:
⋮...
│class OrderedSet
│    def __init__()
│    def add()
│    def remove()
│    def discard()
│    def __iter__()
│    def __reversed__()
│    def __contains__()
│    def __bool__()
│    def __len__()
│    def __repr__()
│class MultiValueDictKeyError
│class MultiValueDict
│    def __init__()
│    def __repr__()
│    def __getitem__()
│    def __setitem__()
│    def __copy__()
│    def __deepcopy__()
│    def __getstate__()
│    def __setstate__()
│    def get()
│    def _getlist()
│    def getlist()
│    def setlist()
│    def setdefault()
│    def setlistdefault()
│    def appendlist()
│    def items()
│    def lists()
│    def values()
│    def copy()
│    def update()
│    def dict()
│class ImmutableList
│    def __new__()
│    def complain()
│class DictWrapper
│    def __init__()
│    def __getitem__()
│class CaseInsensitiveMapping
│    def __init__()
│    def __getitem__()
│    def __len__()
│    def __eq__()
│    def __iter__()
│    def __repr__()
│    def copy()
│    def _unpack_items()
⋮...

│utils/dateformat.py:
⋮...
│class Formatter
│    def format()
│class TimeFormat
│    def __init__()
│    def a()
│    def A()
│    def e()
│    def f()
│    def g()
│    def G()
│    def h()
│    def H()
│    def i()
│    def O()
│    def P()
│    def s()
│    def T()
│    def u()
│    def Z()
│class DateFormat
│    def b()
│    def c()
│    def d()
│    def D()
│    def E()
│    def F()
│    def I()
│    def j()
│    def l()
│    def L()
│    def m()
│    def M()
│    def n()
│    def N()
│    def o()
│    def r()
│    def S()
│    def t()
│    def U()
│    def w()
│    def W()
│    def y()
│    def Y()
│    def z()
│def format()
│def time_format()
⋮...

│utils/dateparse.py:
⋮...
│def parse_date()
│def parse_time()
│def parse_datetime()
│def parse_duration()
⋮...

│utils/dates.py:
⋮...

│utils/deconstruct.py:
⋮...
│def deconstructible()
│def decorator()
│def __new__()
│def deconstruct()
⋮...

│utils/decorators.py:
⋮...
│class classonlymethod
│    def __get__()
│def _update_method_wrapper()
│def dummy()
│def _multi_decorate()
│def _wrapper()
│def method_decorator()
│def _dec()
│def decorator_from_middleware_with_args()
│def decorator_from_middleware()
│def make_middleware_decorator()
│def _make_decorator()
│def _decorator()
│def _pre_process_request()
│def _process_exception()
│def _post_process_request()
│def callback()
│def async _view_wrapper()
│def _view_wrapper()
│def sync_and_async_middleware()
│def sync_only_middleware()
│def async_only_middleware()
⋮...

│utils/deprecation.py:
⋮...
│class RemovedInDjango61Warning
│class RemovedInDjango70Warning
│class warn_about_renamed_method
│    def __init__()
│    def __call__()
│    def wrapper()
│class RenameMethodsBase
│    def __new__()
│class MiddlewareMixin
│    def __init__()
│    def __repr__()
│    def __call__()
│def async __acall__()
⋮...

│utils/duration.py:
⋮...
│def _get_duration_components()
│def duration_string()
│def duration_iso_string()
│def duration_microseconds()
⋮...

│utils/encoding.py:
⋮...
│class DjangoUnicodeDecodeError
│    def __str__()
│def smart_str()
│def is_protected_type()
│def force_str()
│def smart_bytes()
│def force_bytes()
│def iri_to_uri()
│def uri_to_iri()
│def escape_uri_path()
│def punycode()
│def repercent_broken_unicode()
│def filepath_to_uri()
│def get_system_encoding()
⋮...

│utils/feedgenerator.py:
⋮...
│def rfc2822_date()
│def rfc3339_date()
│def get_tag_uri()
│def _guess_stylesheet_mimetype()
│class Stylesheet
│    def __init__()
│    def url()
│    def mimetype()
│    def __str__()
│    def __repr__()
│class SyndicationFeed
│    def __init__()
│    def to_str()
│    def to_stylesheet()
│    def add_item()
│    def to_str()
│    def num_items()
│    def root_attributes()
│    def add_root_elements()
│    def add_stylesheets()
│    def item_attributes()
│    def add_item_elements()
│    def write()
│    def writeString()
│    def latest_post_date()
│class Enclosure
│    def __init__()
│class RssFeed
│    def write()
│    def rss_attributes()
│    def write_items()
│    def add_stylesheets()
│    def add_root_elements()
│    def endChannelElement()
│class RssUserland091Feed
│    def add_item_elements()
│class Rss201rev2Feed
│    def add_item_elements()
│class Atom1Feed
│    def write()
│    def root_attributes()
│    def add_root_elements()
│    def write_items()
│    def add_item_elements()
⋮...

│utils/formats.py:
⋮...
│def reset_format_cache()
│def iter_format_modules()
│def get_format_modules()
│def get_format()
│def date_format()
│def time_format()
│def number_format()
│def localize()
│def localize_input()
│def sanitize_strftime_format()
│def sanitize_separators()
⋮...

│utils/functional.py:
⋮...
│class cached_property
│    def func()
│    def __init__()
│    def __set_name__()
│    def __get__()
│class classproperty
│    def __init__()
│    def __get__()
│    def getter()
│class Promise
│def lazy()
│class __proxy__
│    def __init__()
│    def __reduce__()
│    def __deepcopy__()
│    def __cast()
│    def __repr__()
│    def __str__()
│    def __eq__()
│    def __ne__()
│    def __lt__()
│    def __le__()
│    def __gt__()
│    def __ge__()
│    def __hash__()
│    def __format__()
│    def __add__()
│    def __radd__()
│    def __mod__()
│    def __mul__()
│    def __wrapper__()
│    def __wrapper__()
│def _lazy_proxy_unpickle()
│def lazystr()
│def keep_lazy()
│def decorator()
│def wrapper()
│def keep_lazy_text()
│def new_method_proxy()
│def inner()
│class LazyObject
│    def __init__()
│    def __getattribute__()
│    def __setattr__()
│    def __delattr__()
│    def _setup()
│    def __reduce__()
│    def __copy__()
│    def __deepcopy__()
│def unpickle_lazyobject()
│class SimpleLazyObject
│    def __init__()
│    def _setup()
│    def __repr__()
│    def __copy__()
│    def __deepcopy__()
│    def __radd__()
│def partition()
⋮...

│utils/hashable.py:
⋮...
│def make_hashable()
⋮...

│utils/html.py:
⋮...
│def escape()
│def escapejs()
│def json_script()
│def conditional_escape()
│def format_html()
│def format_html_join()
│def linebreaks()
│class MLStripper
│    def __init__()
│    def handle_data()
│    def handle_entityref()
│    def handle_charref()
│    def get_data()
│def _strip_once()
│def strip_tags()
│def strip_spaces_between_tags()
│def smart_urlquote()
│def unquote_quote()
│class CountsDict
│    def __init__()
│    def __missing__()
│class Urlizer
│    def __call__()
│    def handle_word()
│    def trim_url()
│    def wrapping_punctuation_openings()
│    def trailing_punctuation_chars_no_semicolon()
│    def trailing_punctuation_chars_has_semicolon()
│    def trim_punctuation()
│    def is_email_simple()
│def urlize()
│def avoid_wrapping()
│def html_safe()
⋮...

│utils/http.py:
⋮...
│def urlencode()
│def http_date()
│def parse_http_date()
│def parse_http_date_safe()
│def base36_to_int()
│def int_to_base36()
│def urlsafe_base64_encode()
│def urlsafe_base64_decode()
│def parse_etags()
│def quote_etag()
│def is_same_domain()
│def url_has_allowed_host_and_scheme()
│def _url_has_allowed_host_and_scheme()
│def escape_leading_slashes()
│def parse_header_parameters()
│def content_disposition_header()
⋮...

│utils/inspect.py:
⋮...
│def _get_func_parameters()
│def _get_callable_parameters()
│def get_func_args()
│def get_func_full_args()
│def func_accepts_kwargs()
│def func_accepts_var_args()
│def method_has_no_args()
│def func_supports_parameter()
⋮...

│utils/ipv6.py:
⋮...
│def _ipv6_address_from_str()
│def clean_ipv6_address()
│def is_valid_ipv6_address()
⋮...

│utils/log.py:
⋮...
│def configure_logging()
│class AdminEmailHandler
│    def __init__()
│    def emit()
│    def send_mail()
│    def connection()
│    def format_subject()
│class CallbackFilter
│    def __init__()
│    def filter()
│class RequireDebugFalse
│    def filter()
│class RequireDebugTrue
│    def filter()
│class ServerFormatter
│    def __init__()
│    def format()
│    def uses_server_time()
│def log_response()
⋮...

│utils/lorem_ipsum.py:
⋮...
│def sentence()
│def paragraph()
│def paragraphs()
│def words()
⋮...

│utils/module_loading.py:
⋮...
│def cached_import()
│def import_string()
│def autodiscover_modules()
│def module_has_submodule()
│def module_dir()
⋮...

│utils/numberformat.py:
⋮...
│def format()
⋮...

│utils/regex_helper.py:
⋮...
│class Choice
│class Group
│class NonCapture
│def normalize()
│def next_char()
│def walk_to_end()
│def get_quantifier()
│def contains()
│def flatten_result()
│def _lazy_re_compile()
│def _compile()
⋮...

│utils/safestring.py:
⋮...
│class SafeData
│    def __html__()
│class SafeString
│    def __add__()
│    def __str__()
│def _safety_decorator()
│def wrapper()
│def mark_safe()
⋮...

│utils/termcolors.py:
⋮...
│def colorize()
│def make_style()
│def parse_color_setting()
⋮...

│utils/text.py:
⋮...
│def capfirst()
│def wrap()
│def add_truncation_text()
│def calculate_truncate_chars_length()
│class TruncateHTMLParser
│class TruncationCompleted
│    def __init__()
│    def void_elements()
│    def handle_startendtag()
│    def handle_starttag()
│    def handle_endtag()
│    def handle_data()
│    def feed()
│class TruncateCharsHTMLParser
│    def __init__()
│    def process()
│class TruncateWordsHTMLParser
│    def process()
│class Truncator
│    def __init__()
│    def chars()
│    def _text_chars()
│    def words()
│    def _text_words()
│def get_valid_filename()
│def get_text_list()
│def normalize_newlines()
│def phone2numeric()
│def _get_random_filename()
│def compress_string()
│class StreamingBuffer
│    def read()
│def compress_sequence()
│def smart_split()
│def unescape_string_literal()
│def slugify()
│def camel_case_to_spaces()
│def _format_lazy()
⋮...

│utils/timesince.py:
⋮...
│def timesince()
│def timeuntil()
⋮...

│utils/timezone.py:
⋮...
│def get_fixed_timezone()
│def get_default_timezone()
│def get_default_timezone_name()
│def get_current_timezone()
│def get_current_timezone_name()
│def _get_timezone_name()
│def activate()
│def deactivate()
│class override
│    def __init__()
│    def __enter__()
│    def __exit__()
│def template_localtime()
│def localtime()
│def localdate()
│def now()
│def is_aware()
│def is_naive()
│def make_aware()
│def make_naive()
│def _datetime_ambiguous_or_imaginary()
⋮...

│utils/translation/__init__.py:
⋮...
│class TranslatorCommentWarning
│class Trans
│    def __getattr__()
│def gettext_noop()
│def gettext()
│def ngettext()
│def pgettext()
│def npgettext()
│def lazy_number()
│class NumberAwareString
│    def __bool__()
│    def _get_number_value()
│    def _translate()
│    def format()
│    def __mod__()
│def _lazy_number_unpickle()
│def ngettext_lazy()
│def npgettext_lazy()
│def activate()
│def deactivate()
│class override
│    def __init__()
│    def __enter__()
│    def __exit__()
│def get_language()
│def get_language_bidi()
│def check_for_language()
│def to_language()
│def to_locale()
│def get_language_from_request()
│def get_language_from_path()
│def get_supported_language_variant()
│def templatize()
│def deactivate_all()
│def get_language_info()
│def trim_whitespace()
│def round_away_from_one()
⋮...

│utils/translation/reloader.py:
⋮...
│def watch_for_translation_changes()
│def translation_file_changed()
⋮...

│utils/translation/template.py:
⋮...
│def blankout()
│def templatize()
│def join_tokens()
⋮...

│utils/translation/trans_null.py:
⋮...
│def gettext()
│def ngettext()
│def pgettext()
│def npgettext()
│def activate()
│def deactivate()
│def get_language()
│def get_language_bidi()
│def check_for_language()
│def get_language_from_request()
│def get_language_from_path()
│def get_supported_language_variant()
⋮...

│utils/translation/trans_real.py:
⋮...
│def reset_cache()
│class TranslationCatalog
│    def __init__()
│    def __getitem__()
│    def __setitem__()
│    def __contains__()
│    def items()
│    def keys()
│    def update()
│    def get()
│    def plural()
│class DjangoTranslation
│    def __init__()
│    def __repr__()
│    def _new_gnu_trans()
│    def _init_translation_catalog()
│    def _add_installed_apps_translations()
│    def _add_local_translations()
│    def _add_fallback()
│    def merge()
│    def language()
│    def to_language()
│    def ngettext()
│def translation()
│def activate()
│def deactivate()
│def deactivate_all()
│def get_language()
│def get_language_bidi()
│def catalog()
│def gettext()
│def pgettext()
│def gettext_noop()
│def do_ntranslate()
│def ngettext()
│def npgettext()
│def all_locale_paths()
│def check_for_language()
│def get_languages()
│def get_supported_language_variant()
│def get_language_from_path()
│def get_language_from_request()
│def _parse_accept_lang_header()
│def parse_accept_lang_header()
⋮...

│utils/tree.py:
⋮...
│class Node
│    def __init__()
│    def create()
│    def __str__()
│    def __repr__()
│    def __copy__()
│    def __deepcopy__()
│    def __len__()
│    def __bool__()
│    def __contains__()
│    def __eq__()
│    def __hash__()
│    def add()
│    def negate()
⋮...

│utils/version.py:
⋮...
│def get_version()
│def get_main_version()
│def get_complete_version()
│def get_docs_version()
│def get_git_changeset()
│def get_version_tuple()
⋮...

│utils/xmlutils.py:
⋮...
│class UnserializableContentError
│class SimplerXMLGenerator
│    def addQuickElement()
│    def characters()
│    def startElement()
⋮...

│views/__init__.py:
⋮...

│views/csrf.py:
⋮...
│def builtin_template_path()
│def csrf_failure()
⋮...

│views/debug.py:
⋮...
│def builtin_template_path()
│class ExceptionCycleWarning
│class CallableSettingWrapper
│    def __init__()
│    def __repr__()
│def technical_500_response()
│def get_default_exception_reporter_filter()
│def get_exception_reporter_filter()
│def get_exception_reporter_class()
│def get_caller()
│class SafeExceptionReporterFilter
│    def cleanse_setting()
│    def get_safe_settings()
│    def get_safe_request_meta()
│    def get_safe_cookies()
│    def is_active()
│    def get_cleansed_multivaluedict()
│    def get_post_parameters()
│    def cleanse_special_types()
│    def get_traceback_frame_variables()
│class ExceptionReporter
│    def html_template_path()
│    def text_template_path()
│    def __init__()
│    def _get_raw_insecure_uri()
│    def get_traceback_data()
│    def get_traceback_html()
│    def get_traceback_text()
│    def _get_source()
│    def _get_lines_from_file()
│    def _get_explicit_or_implicit_cause()
│    def get_traceback_frames()
│    def get_exception_traceback_frames()
│def technical_404_response()
│def default_urlconf()
⋮...

│views/decorators/cache.py:
⋮...
│def cache_page()
│def _check_request()
│def cache_control()
│def _cache_controller()
│def async _view_wrapper()
│def _view_wrapper()
│def never_cache()
│def async _view_wrapper()
│def _view_wrapper()
⋮...

│views/decorators/clickjacking.py:
⋮...
│def xframe_options_deny()
│def async _view_wrapper()
│def _view_wrapper()
│def xframe_options_sameorigin()
│def async _view_wrapper()
│def _view_wrapper()
│def xframe_options_exempt()
│def async _view_wrapper()
│def _view_wrapper()
⋮...

│views/decorators/common.py:
⋮...
│def no_append_slash()
│def async _view_wrapper()
│def _view_wrapper()
⋮...

│views/decorators/csrf.py:
⋮...
│class _EnsureCsrfToken
│    def _reject()
│class _EnsureCsrfCookie
│    def _reject()
│    def process_view()
│def csrf_exempt()
│def async _view_wrapper()
│def _view_wrapper()
⋮...

│views/decorators/debug.py:
⋮...
│def sensitive_variables()
│def decorator()
│def sensitive_variables_wrapper()
│def sensitive_post_parameters()
│def decorator()
│def async sensitive_post_parameters_wrapper()
│def sensitive_post_parameters_wrapper()
⋮...

│views/decorators/gzip.py:
⋮...

│views/decorators/http.py:
⋮...
│def require_http_methods()
│def decorator()
│def async inner()
│def inner()
│def condition()
│def decorator()
│def _pre_process_request()
│def _post_process_request()
│def async inner()
│def inner()
│def etag()
│def last_modified()
⋮...

│views/decorators/vary.py:
⋮...
│def vary_on_headers()
│def decorator()
│def async _view_wrapper()
│def _view_wrapper()
⋮...

│views/defaults.py:
⋮...
│def page_not_found()
│def server_error()
│def bad_request()
│def permission_denied()
⋮...

│views/generic/__init__.py:
⋮...
│class GenericViewError
⋮...

│views/generic/base.py:
⋮...
│class ContextMixin
│    def get_context_data()
│class View
│    def __init__()
│    def view_is_async()
│    def as_view()
│    def view()
│    def setup()
│    def dispatch()
│    def http_method_not_allowed()
│def async func()
│def options()
│def async func()
│def _allowed_methods()
│class TemplateResponseMixin
│    def render_to_response()
│    def get_template_names()
│class TemplateView
│    def get()
│class RedirectView
│    def get_redirect_url()
│    def get()
│    def head()
│    def post()
│    def options()
│    def delete()
│    def put()
│    def patch()
⋮...

│views/generic/dates.py:
⋮...
│class YearMixin
│    def get_year_format()
│    def get_year()
│    def get_next_year()
│    def get_previous_year()
│    def _get_next_year()
│    def _get_current_year()
│class MonthMixin
│    def get_month_format()
│    def get_month()
│    def get_next_month()
│    def get_previous_month()
│    def _get_next_month()
│    def _get_current_month()
│class DayMixin
│    def get_day_format()
│    def get_day()
│    def get_next_day()
│    def get_previous_day()
│    def _get_next_day()
│    def _get_current_day()
│class WeekMixin
│    def get_week_format()
│    def get_week()
│    def get_next_week()
│    def get_previous_week()
│    def _get_next_week()
│    def _get_current_week()
│    def _get_weekday()
│class DateMixin
│    def get_date_field()
│    def get_allow_future()
│    def uses_datetime_field()
│    def _make_date_lookup_arg()
│    def _make_single_date_lookup()
│class BaseDateListView
│    def get()
│    def get_dated_items()
│    def get_ordering()
│    def get_dated_queryset()
│    def get_date_list_period()
│    def get_date_list()
│class BaseArchiveIndexView
│    def get_dated_items()
│class ArchiveIndexView
│class BaseYearArchiveView
│    def get_dated_items()
│    def get_make_object_list()
│class YearArchiveView
│class BaseMonthArchiveView
│    def get_dated_items()
│class MonthArchiveView
│class BaseWeekArchiveView
│    def get_dated_items()
│class WeekArchiveView
│class BaseDayArchiveView
│    def get_dated_items()
│    def _get_dated_items()
│class DayArchiveView
│class BaseTodayArchiveView
│    def get_dated_items()
│class TodayArchiveView
│class BaseDateDetailView
│    def get_object()
│class DateDetailView
│def _date_from_string()
│def _get_next_prev()
│def timezone_today()
⋮...

│views/generic/detail.py:
⋮...
│class SingleObjectMixin
│    def get_object()
│    def get_queryset()
│    def get_slug_field()
│    def get_context_object_name()
│    def get_context_data()
│class BaseDetailView
│    def get()
│class SingleObjectTemplateResponseMixin
│    def get_template_names()
│class DetailView
⋮...

│views/generic/edit.py:
⋮...
│class FormMixin
│    def get_initial()
│    def get_prefix()
│    def get_form_class()
│    def get_form()
│    def get_form_kwargs()
│    def get_success_url()
│    def form_valid()
│    def form_invalid()
│    def get_context_data()
│class ModelFormMixin
│    def get_form_class()
│    def get_form_kwargs()
│    def get_success_url()
│    def form_valid()
│class ProcessFormView
│    def get()
│    def post()
│    def put()
│class BaseFormView
│class FormView
│class BaseCreateView
│    def get()
│    def post()
│class CreateView
│class BaseUpdateView
│    def get()
│    def post()
│class UpdateView
│class DeletionMixin
│    def delete()
│    def post()
│    def get_success_url()
│class BaseDeleteView
│    def post()
│    def form_valid()
│class DeleteView
⋮...

│views/generic/list.py:
⋮...
│class MultipleObjectMixin
│    def get_queryset()
│    def get_ordering()
│    def paginate_queryset()
│    def get_paginate_by()
│    def get_paginator()
│    def get_paginate_orphans()
│    def get_allow_empty()
│    def get_context_object_name()
│    def get_context_data()
│class BaseListView
│    def get()
│class MultipleObjectTemplateResponseMixin
│    def get_template_names()
│class ListView
⋮...

│views/i18n.py:
⋮...
│def builtin_template_path()
│def set_language()
│def get_formats()
│class JavaScriptCatalog
│    def get()
│    def get_paths()
│    def _num_plurals()
│    def _plural_string()
│    def get_plural()
│    def get_catalog()
│    def get_context_data()
│    def render_to_response()
│    def indent()
│class JSONCatalog
│    def render_to_response()
⋮...

│views/static.py:
⋮...
│def builtin_template_path()
│def serve()
│def directory_index()
│def was_modified_since()
⋮...

│views/templates/default_urlconf.html:
⋮...

│views/templates/i18n_catalog.js:
⋮...

│views/templates/technical_404.html:
⋮...

│views/templates/technical_500.html:
⋮...
