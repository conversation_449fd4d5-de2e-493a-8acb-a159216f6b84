"""
Domain Depth Analyzer

This script analyzes the domain depth and file distribution from the output of
exp_file_domain_mapper_batched.py.

It calculates:
1. How many domains exist at each depth level
2. How many files are mapped to domains at each depth level

Results are saved as JSON for further analysis or visualization.
"""

import argparse
import json
import logging
import os
import yaml
from collections import defaultdict
from typing import Dict, List, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_domain_depth(domain_file_mapping_path: str, output_path: str) -> Dict:
    """
    Analyze domain depth and file distribution from a domain-file mapping YAML.
    
    Args:
        domain_file_mapping_path: Path to the YAML file with domain-to-file mappings
        output_path: Path to save the analysis results as JSON
        
    Returns:
        Dictionary containing the analysis results
    """
    # Read the domain-file mapping YAML
    logger.info(f"Reading domain-file mapping from {domain_file_mapping_path}")
    with open(domain_file_mapping_path, 'r') as f:
        domain_file_mappings = yaml.safe_load(f)
    
    # Initialize counters
    domains_by_depth = defaultdict(int)
    files_by_depth = defaultdict(int)
    domains_with_files = defaultdict(list)
    
    # Process each domain and its files
    for domain, files in domain_file_mappings.items():
        # Calculate domain depth (count slashes + 1)
        depth = domain.count('/') + 1
        
        # Increment domain count for this depth
        domains_by_depth[depth] += 1
        
        # Add files count for this depth
        file_count = len(files)
        files_by_depth[depth] += file_count
        
        # Store domain with its file count for detailed analysis
        domains_with_files[depth].append({
            "domain": domain,
            "file_count": file_count
        })
    
    # Prepare results
    results = {
        "domains_by_depth": dict(domains_by_depth),
        "files_by_depth": dict(files_by_depth),
        "domains_with_files": dict(domains_with_files),
        "total_domains": sum(domains_by_depth.values()),
        "total_files": sum(files_by_depth.values()),
        "max_depth": max(domains_by_depth.keys()) if domains_by_depth else 0
    }
    
    # Calculate average files per domain at each depth
    avg_files_per_domain = {}
    for depth, domain_count in domains_by_depth.items():
        if domain_count > 0:
            avg_files_per_domain[depth] = files_by_depth[depth] / domain_count
    
    results["avg_files_per_domain"] = avg_files_per_domain
    
    # Save results to JSON
    logger.info(f"Saving analysis results to {output_path}")
    with open(output_path, 'w') as f:
        json.dump(results, f, indent=2)
    
    return results

def main():
    """Main entry point for the domain depth analyzer."""
    parser = argparse.ArgumentParser(description="Analyze domain depth and file distribution")
    # parser.add_argument("--input", required=True, help="Path to the domain-file mapping YAML file")
    # parser.add_argument("--output", required=True, help="Path to save the analysis results JSON")
    
    args = parser.parse_args()

    # args.input = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/exp_results/domain_mapping/gitlab_oai_4_1.yaml"
    # args.output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/exp_results/depth_analyzer/gitlab_full_domain_depth_out.yaml"

    args.input = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/exp_results/domain_mapping/pytorch/pytorch_oai_4_1.yaml"
    args.output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/exp_results/depth_analyzer/pytorch_full_domain_depth_out.yaml"

    try:
        results = analyze_domain_depth(args.input, args.output)
        
        # Print summary to console
        logger.info(f"Analysis complete. Found {results['total_domains']} domains with {results['total_files']} files.")
        logger.info(f"Maximum domain depth: {results['max_depth']}")
        
        for depth in sorted(results["domains_by_depth"].keys()):
            logger.info(f"Depth {depth}: {results['domains_by_depth'][depth]} domains, "
                       f"{results['files_by_depth'][depth]} files, "
                       f"avg {results['avg_files_per_domain'].get(depth, 0):.2f} files per domain")
        
        return 0
    
    except Exception as e:
        logger.error(f"Error in domain depth analysis: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    exit(main())