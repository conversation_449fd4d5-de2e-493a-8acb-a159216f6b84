"""
Test the file domain mapper.

This module contains tests for the file domain mapper functionality.
"""

import os
import unittest
import asyncio
from unittest.mock import patch, MagicMock
import tempfile
import yaml

from bracket_core.exp_repomap.file_domain_mapper import FileDomainMapper, FileDomainMapperResult

class TestFileDomainMapper(unittest.TestCase):
    """Test the file domain mapper."""

    def setUp(self):
        """Set up the test environment."""
        # Create temporary files for testing
        self.temp_dir = tempfile.TemporaryDirectory()

        # Create a sample repomap file
        self.repomap_path = os.path.join(self.temp_dir.name, "repomap.txt")
        with open(self.repomap_path, "w") as f:
            f.write("""
│file1.py:
⋮...
│def function1()
│def function2()
⋮...

│file2.py:
⋮...
│class Class1
│    def method1()
│    def method2()
⋮...

│folder/file3.py:
⋮...
│def function3()
⋮...
""")

        # Create a sample domain analysis YAML
        self.domain_yaml_path = os.path.join(self.temp_dir.name, "domain_analysis.yaml")
        domain_data = {
            "areas": [
                {
                    "name": "Domain1",
                    "subareas": [
                        {
                            "name": "Subdomain1",
                            "subareas": []
                        },
                        {
                            "name": "Subdomain2",
                            "subareas": []
                        }
                    ]
                },
                {
                    "name": "Domain2",
                    "subareas": [
                        {
                            "name": "Subdomain3",
                            "subareas": []
                        }
                    ]
                }
            ]
        }
        with open(self.domain_yaml_path, "w") as f:
            yaml.dump(domain_data, f)

        # Output path
        self.output_path = os.path.join(self.temp_dir.name, "file_domain_mappings.yaml")

    def tearDown(self):
        """Clean up the test environment."""
        self.temp_dir.cleanup()

    def test_extract_file_paths(self):
        """Test extracting file paths from the repository map."""
        mapper = FileDomainMapper(
            repomap_path=self.repomap_path,
            domain_analysis_yaml_path=self.domain_yaml_path,
            output_path=self.output_path,
            api_key="dummy_key"
        )

        file_paths = mapper.extract_file_paths()

        self.assertEqual(len(file_paths), 3)
        self.assertIn("file1.py", file_paths)
        self.assertIn("file2.py", file_paths)
        self.assertIn("folder/file3.py", file_paths)

    def test_extract_domain_hierarchy(self):
        """Test extracting domain hierarchy from domain analysis data."""
        mapper = FileDomainMapper(
            repomap_path=self.repomap_path,
            domain_analysis_yaml_path=self.domain_yaml_path,
            output_path=self.output_path,
            api_key="dummy_key"
        )

        domain_data = mapper.read_domain_yaml()
        mapper.extract_domain_hierarchy(domain_data)

        # Check domain paths
        self.assertIn("Domain1", mapper.domain_path_to_name)
        self.assertIn("Domain1/Subdomain1", mapper.domain_path_to_name)
        self.assertIn("Domain1/Subdomain2", mapper.domain_path_to_name)
        self.assertIn("Domain2", mapper.domain_path_to_name)
        self.assertIn("Domain2/Subdomain3", mapper.domain_path_to_name)

        # Check leaf domains
        self.assertEqual(len(mapper.leaf_domains), 3)
        self.assertIn("Domain1/Subdomain1", mapper.leaf_domains)
        self.assertIn("Domain1/Subdomain2", mapper.leaf_domains)
        self.assertIn("Domain2/Subdomain3", mapper.leaf_domains)

    def test_map_files_to_domains(self):
        """Test mapping files to domains."""
        # Mock both the API request and OpenRouter client
        with patch("bracket_core.exp_repomap.file_domain_mapper.APIRequest") as mock_api_request, \
             patch("bracket_core.exp_repomap.file_domain_mapper.get_openrouter_client") as mock_get_openrouter_client:

            # Create a mock for the API request
            mock_api_instance = MagicMock()

            # Create a mock coroutine function for API request
            async def mock_call_api(*args, **kwargs):
                # Return different responses based on which top-level domain is being processed
                if any(arg for arg in args if isinstance(arg, list) and any(msg.get('content', '').find('Domain1') >= 0 for msg in arg if isinstance(msg, dict))):
                    return """
{
  "Domain1/Subdomain1": ["file1.py", "folder/file3.py"],
  "Domain1/Subdomain2": ["file2.py"]
}
"""
                else:
                    return """
{
  "Domain2/Subdomain3": ["folder/file3.py", "file2.py"]
}
"""

            # Set the mock to return our mock coroutine
            mock_api_instance.call_api = mock_call_api
            mock_api_request.return_value = mock_api_instance

            # Create a mock for the OpenRouter client
            mock_openrouter_client = MagicMock()

            # Create a mock coroutine function for OpenRouter generate
            async def mock_generate(*args, **kwargs):
                # Return different responses based on the prompt content
                prompt = kwargs.get('prompt', '')
                if 'Domain1' in prompt:
                    return """
{
  "Domain1/Subdomain1": ["file1.py", "folder/file3.py"],
  "Domain1/Subdomain2": ["file2.py"]
}
"""
                else:
                    return """
{
  "Domain2/Subdomain3": ["folder/file3.py", "file2.py"]
}
"""

            # Set the mock to return our mock coroutine
            mock_openrouter_client.generate = mock_generate
            mock_get_openrouter_client.return_value = mock_openrouter_client

            mapper = FileDomainMapper(
                repomap_path=self.repomap_path,
                domain_analysis_yaml_path=self.domain_yaml_path,
                output_path=self.output_path,
                api_key="dummy_key"
            )

            # Run the async method in the event loop
            loop = asyncio.get_event_loop()
            result = loop.run_until_complete(mapper.map_files_to_domains())

            self.assertTrue(result.success)
            self.assertEqual(result.output_path, self.output_path)

            # Check that the output file was created
            self.assertTrue(os.path.exists(self.output_path))

            # Check the content of the output file
            with open(self.output_path, "r") as f:
                mappings = yaml.safe_load(f)

            self.assertIn("Domain1/Subdomain1", mappings)
            self.assertIn("Domain1/Subdomain2", mappings)
            self.assertIn("Domain2/Subdomain3", mappings)

            # Check that files can be in multiple domains
            self.assertIn("file1.py", mappings["Domain1/Subdomain1"])
            self.assertIn("folder/file3.py", mappings["Domain1/Subdomain1"])
            self.assertIn("file2.py", mappings["Domain1/Subdomain2"])
            self.assertIn("folder/file3.py", mappings["Domain2/Subdomain3"])
            self.assertIn("file2.py", mappings["Domain2/Subdomain3"])

# This function is no longer needed since we're handling async tests directly
# def run_async_test(test_case):
#     """Run an async test case."""
#     loop = asyncio.get_event_loop()
#     loop.run_until_complete(test_case)

if __name__ == "__main__":
    unittest.main()
