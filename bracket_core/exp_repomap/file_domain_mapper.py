"""
File-to-Domain Mapper for Codebase

This module provides functionality to:
1. Read the repository map to extract file paths
2. Read the domain hierarchy from domain analysis YAML
3. Process files in batches by top-level domain
4. Use an LLM to classify files into leaf domains
5. Merge and save the results

It can be used as:
1. A standalone script to process repository map and domain analysis files
2. A module that can be integrated into the repository analysis flow
"""

import os
import yaml
import json
import logging
import argparse
import asyncio
import aiohttp
import time
from typing import Dict, List, Set, Tuple, Any, Optional, Union
from dataclasses import dataclass, field
from pathlib import Path
import re

# Import OpenRouter client and API key management
from bracket_core.llm.get_client import get_openrouter_client
from bracket_core.llm.api_keys import get_openai_api_key, get_openrouter_api_key

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class FileDomainMapperResult:
    """Result of mapping files to domains."""
    success: bool = True
    error_message: str = ""
    domain_file_mappings: Dict[str, List[str]] = field(default_factory=dict)
    output_path: Optional[str] = None

class StatusTracker:
    """Tracks API request status."""

    def __init__(self, max_requests_per_minute, max_tokens_per_minute):
        """Initialize the status tracker."""
        self.max_requests_per_minute = max_requests_per_minute
        self.max_tokens_per_minute = max_tokens_per_minute
        self.requests_past_minute = 0
        self.tokens_past_minute = 0
        self.last_request_time = 0
        self.request_times = []
        self.token_counts = []

    def update_usage(self, tokens_used):
        """Update usage after a request."""
        self.requests_past_minute += 1
        self.tokens_past_minute += tokens_used

        # Record request time and token count
        current_time = time.time()
        self.request_times.append(current_time)
        self.token_counts.append(tokens_used)

        # Remove entries older than 1 minute
        cutoff_time = current_time - 60
        while self.request_times and self.request_times[0] < cutoff_time:
            self.requests_past_minute -= 1
            self.tokens_past_minute -= self.token_counts[0]
            self.request_times.pop(0)
            self.token_counts.pop(0)

        self.last_request_time = current_time

    def get_delay_time(self):
        """Get delay time to stay within rate limits."""
        # If we're under the limits, no delay needed
        if (self.requests_past_minute < self.max_requests_per_minute and
            self.tokens_past_minute < self.max_tokens_per_minute):
            return 0

        # Calculate delay based on request rate
        if self.requests_past_minute >= self.max_requests_per_minute:
            requests_delay = 60 - (time.time() - self.request_times[0])
        else:
            requests_delay = 0

        # Calculate delay based on token rate
        if self.tokens_past_minute >= self.max_tokens_per_minute:
            tokens_delay = 60 - (time.time() - self.request_times[0])
        else:
            tokens_delay = 0

        return max(requests_delay, tokens_delay)

class APIRequest:
    """Handles API requests with rate limiting."""

    def __init__(self, status_tracker, api_key, model, use_openrouter=False, openrouter_base_url="https://openrouter.ai/api/v1"):
        """Initialize the API request handler."""
        self.status_tracker = status_tracker
        self.api_key = api_key
        self.model = model
        self.use_openrouter = use_openrouter
        self.openrouter_base_url = openrouter_base_url

    async def call_api(self, messages, temperature=0.0, max_tokens=None, json_mode=True):
        """Call the API with rate limiting."""
        # Calculate delay time to stay within rate limits
        delay_time = self.status_tracker.get_delay_time()
        if delay_time > 0:
            logger.info(f"Rate limit approaching. Waiting {delay_time:.2f} seconds...")
            await asyncio.sleep(delay_time)

        # Prepare the API request
        if self.use_openrouter:
            url = f"{self.openrouter_base_url}/chat/completions"
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "HTTP-Referer": "https://bracket.dev"
            }
        else:
            url = "https://api.openai.com/v1/chat/completions"
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

        data = {
            "model": self.model,
            "messages": messages,
            # "temperature": temperature
        }

        if json_mode:
            data["response_format"] = {"type": "json_object"}

        if max_tokens:
            data["max_tokens"] = max_tokens

        # Make the API request
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=data) as response:
                response_data = await response.json()

                if response.status != 200:
                    error_message = response_data.get("error", {}).get("message", "Unknown error")
                    logger.error(f"API request failed: {error_message}")
                    return None

                # Extract the response content
                content = response_data["choices"][0]["message"]["content"]

                # Update usage statistics
                usage = response_data.get("usage", {})
                total_tokens = usage.get("total_tokens", 0)
                self.status_tracker.update_usage(total_tokens)

                logger.debug(f"API request successful. Used {total_tokens} tokens.")
                return content

class FileDomainMapper:
    """
    Maps files to domains based on repository map and domain hierarchy.

    This class reads a repository map file and domain analysis YAML,
    and uses an LLM to map files to leaf domains.
    """

    def __init__(
        self,
        repomap_path: str,
        domain_analysis_yaml_path: str,
        output_path: str,
        api_key: Optional[str] = None,
        model: str = "gpt-4o-mini",
        max_requests_per_minute: float = 50,
        max_tokens_per_minute: float = 100000,
        temperature: float = 0.0,
        use_openrouter: bool = False,
        openrouter_base_url: str = "https://openrouter.ai/api/v1",
    ):
        """
        Initialize the file domain mapper.

        Args:
            repomap_path: Path to the repository map file
            domain_analysis_yaml_path: Path to the domain analysis YAML file
            output_path: Path to save the file-to-domain mappings YAML
            api_key: API key (OpenAI or OpenRouter depending on use_openrouter)
            model: Model to use (OpenAI or OpenRouter model ID)
            max_requests_per_minute: Rate limit for API requests
            max_tokens_per_minute: Token rate limit for API
            temperature: Temperature setting for the model (lower = more deterministic)
            use_openrouter: Whether to use OpenRouter instead of OpenAI
            openrouter_base_url: Base URL for OpenRouter API
        """
        self.repomap_path = repomap_path
        self.domain_analysis_yaml_path = domain_analysis_yaml_path
        self.output_path = output_path

        # API settings
        self.model = model
        self.temperature = temperature
        self.use_openrouter = use_openrouter
        self.openrouter_base_url = openrouter_base_url

        # Get the appropriate API key based on the service being used
        if self.use_openrouter:
            self.api_key = get_openrouter_api_key(provided_key=api_key)
        else:
            self.api_key = get_openai_api_key(provided_key=api_key)

        # Initialize status tracker and API request handler
        self.status_tracker = StatusTracker(max_requests_per_minute, max_tokens_per_minute)
        self.api_request = APIRequest(
            self.status_tracker,
            self.api_key,
            self.model,
            self.use_openrouter,
            self.openrouter_base_url
        )

        # Initialize OpenRouter client if needed
        self.openrouter_client = None
        if self.use_openrouter:
            # For file domain mapping, use Gemini model with large context window
            openrouter_model = "google/gemini-2.5-pro-preview"
            self.openrouter_client = get_openrouter_client(
                # API key will be handled by the client
                model=openrouter_model,  # Override with Gemini model
                max_tokens=60096,  # Use larger context window
                temperature=self.temperature,
                base_url=self.openrouter_base_url
            )
            logger.info(f"File Domain Mapper using OpenRouter with model: {openrouter_model}")

        # Initialize domain hierarchy data structures
        self.domain_path_to_name = {}
        self.domain_name_to_path = {}
        self.domain_path_to_description = {}
        self.domain_path_to_explanation = {}  # Store explanations for leaf domains
        self.leaf_domains = []

    def read_domain_yaml(self) -> Dict[str, Any]:
        """
        Read the domain analysis YAML file.

        Returns:
            Dictionary containing domain analysis data
        """
        try:
            with open(self.domain_analysis_yaml_path, 'r') as f:
                domain_data = yaml.safe_load(f)
            logger.info(f"Successfully read domain analysis YAML from: {self.domain_analysis_yaml_path}")
            return domain_data
        except Exception as e:
            logger.error(f"Error reading domain analysis YAML: {e}")
            raise

    def extract_file_paths(self) -> List[str]:
        """
        Extract file paths from the repository map.

        Returns:
            List of file paths
        """
        try:
            file_paths = []
            current_file = None

            with open(self.repomap_path, 'r') as f:
                for line in f:
                    line = line.strip()

                    # Skip empty lines
                    if not line:
                        continue

                    # Check if this is a file path line
                    if line.startswith('│') and line.endswith(':'):
                        # Extract file path
                        current_file = line[1:-1]  # Remove the │ prefix and : suffix
                        if current_file and current_file not in file_paths:
                            file_paths.append(current_file)

            logger.info(f"Extracted {len(file_paths)} file paths from repository map")
            return file_paths
        except Exception as e:
            logger.error(f"Error extracting file paths: {e}")
            raise

    def extract_domain_hierarchy(self, domain_data: Dict[str, Any]) -> None:
        """
        Extract the domain hierarchy from domain analysis data.

        Args:
            domain_data: Dictionary containing domain analysis data
        """
        logger.info("Extracting domain hierarchy")

        # Process domains recursively
        def process_domain(domain, parent_path=""):
            domain_name = domain.get('name', '')
            if not domain_name:
                return

            # Create the current domain path
            current_path = f"{parent_path}/{domain_name}" if parent_path else domain_name

            # Store domain information
            self.domain_path_to_name[current_path] = domain_name
            self.domain_name_to_path[domain_name] = current_path

            # Store explanation if it exists (for leaf domains)
            explanation = domain.get('explanation', '')
            if explanation:
                self.domain_path_to_explanation[current_path] = explanation

            # Generate and store domain description
            description = self._generate_domain_description(domain)
            self.domain_path_to_description[current_path] = description

            # Process subdomains
            subareas = domain.get('subareas', [])

            # If no subareas, this is a leaf domain
            if not subareas:
                self.leaf_domains.append(current_path)

            for subarea in subareas:
                process_domain(subarea, current_path)

        # Process top-level domains
        areas = domain_data.get('areas', [])
        for area in areas:
            process_domain(area)

        logger.info(f"Extracted {len(self.domain_path_to_name)} domains, {len(self.leaf_domains)} leaf domains")

    def _generate_domain_description(self, domain: Dict[str, Any]) -> str:
        """
        Generate a description for a domain based on its name, structure, and explanation.

        Args:
            domain: Domain dictionary

        Returns:
            Domain description
        """
        name = domain.get('name', '')
        subareas = domain.get('subareas', [])
        explanation = domain.get('explanation', '')

        # If this is a leaf domain with an explanation, use it
        if not subareas and explanation:
            return explanation

        # Otherwise, generate a description based on the domain structure
        if not subareas:
            return f"Domain for {name} related functionality"

        subarea_names = [subarea.get('name', '') for subarea in subareas if subarea.get('name', '')]
        if subarea_names:
            return f"Domain for {name} related functionality, including: {', '.join(subarea_names)}"
        else:
            return f"Domain for {name} related functionality"

    def get_top_level_domains(self) -> List[str]:
        """
        Get the list of top-level domains.

        Returns:
            List of top-level domain paths
        """
        return [path for path in self.domain_path_to_name.keys() if '/' not in path]

    def get_leaf_domains_for_top_domain(self, top_domain: str) -> List[str]:
        """
        Get the list of leaf domains under a top-level domain.

        Args:
            top_domain: Top-level domain path

        Returns:
            List of leaf domain paths
        """
        return [path for path in self.leaf_domains if path.startswith(top_domain)]

    def format_domain_hierarchy(self, top_domain: str) -> str:
        """
        Format the domain hierarchy under a top-level domain for the LLM prompt.

        Args:
            top_domain: Top-level domain path

        Returns:
            Formatted domain hierarchy
        """
        result = []

        # Helper function to format a domain and its children
        def format_domain(domain_path, level=0):
            domain_name = self.domain_path_to_name.get(domain_path, domain_path)
            description = self.domain_path_to_description.get(domain_path, "")
            explanation = self.domain_path_to_explanation.get(domain_path, "")

            # Add indentation based on level
            indent = "  " * level

            # For leaf domains, include the explanation if available
            if domain_path in self.leaf_domains and explanation:
                result.append(f"{indent}- {domain_name}: {description}")
                result.append(f"{indent}  Explanation: {explanation}")
            else:
                result.append(f"{indent}- {domain_name}: {description}")

            # Find and format children
            children = [path for path in self.domain_path_to_name.keys()
                       if path.startswith(domain_path + "/") and
                       path.count("/") == domain_path.count("/") + 1]

            for child in sorted(children):
                format_domain(child, level + 1)

        # Format the top domain and its hierarchy
        format_domain(top_domain)

        return "\n".join(result)

    async def map_files_to_domains(self) -> FileDomainMapperResult:
        """
        Map files to leaf domains using LLM with concurrent processing.

        Returns:
            FileDomainMapperResult containing the domain-to-file mappings
        """
        try:
            # Read domain data and extract hierarchy
            domain_data = self.read_domain_yaml()
            self.extract_domain_hierarchy(domain_data)

            # Extract file paths from repository map
            file_paths = self.extract_file_paths()

            # Get top-level domains
            top_domains = self.get_top_level_domains()

            # Prepare tasks for concurrent processing
            tasks = []
            for top_domain in top_domains:
                # Get leaf domains for this top-level domain
                leaf_domains = self.get_leaf_domains_for_top_domain(top_domain)

                if not leaf_domains:
                    logger.warning(f"No leaf domains found for top-level domain: {top_domain}")
                    continue

                # Format domain hierarchy for the prompt
                domain_hierarchy = self.format_domain_hierarchy(top_domain)

                # Create task for this top-level domain
                task = self._classify_files_to_domains(top_domain, leaf_domains, file_paths, domain_hierarchy)
                tasks.append(task)

                logger.info(f"Created task for top-level domain: {top_domain}")

            # Run all tasks concurrently
            logger.info(f"Running {len(tasks)} domain classification tasks concurrently")
            results = await asyncio.gather(*tasks)

            # Merge all results
            all_mappings = {}
            for domain_mappings in results:
                # Instead of simple update which would overwrite files in multiple domains,
                # we'll merge in a way that preserves files in multiple domains
                for domain_path, files in domain_mappings.items():
                    if domain_path in all_mappings:
                        # Add files not already in this domain
                        for file_path in files:
                            if file_path not in all_mappings[domain_path]:
                                all_mappings[domain_path].append(file_path)
                    else:
                        all_mappings[domain_path] = files

            # Save the mappings to YAML
            with open(self.output_path, 'w') as f:
                yaml.dump(all_mappings, f, default_flow_style=False)

            logger.info(f"File-to-domain mappings saved to: {self.output_path}")

            return FileDomainMapperResult(
                success=True,
                domain_file_mappings=all_mappings,
                output_path=self.output_path
            )

        except Exception as e:
            logger.error(f"Error in file-to-domain mapping: {e}")
            import traceback
            logger.error(traceback.format_exc())

            return FileDomainMapperResult(
                success=False,
                error_message=str(e)
            )

    async def _classify_files_to_domains(
        self,
        top_domain: str,
        leaf_domains: List[str],
        file_paths: List[str],
        domain_hierarchy: str
    ) -> Dict[str, List[str]]:
        """
        Classify files to leaf domains using LLM.

        Args:
            top_domain: Top-level domain path
            leaf_domains: List of leaf domain paths
            file_paths: List of file paths
            domain_hierarchy: Formatted domain hierarchy

        Returns:
            Dictionary mapping domain paths to lists of file paths
        """
        # Prepare the prompt
        system_message = """
You are an expert code organization assistant. Your task is to analyze file paths from a codebase and assign each file to the most appropriate leaf domain in a given hierarchy.

Guidelines:
1. Each file should be assigned to exactly one leaf domain that best represents its primary purpose
2. Use the file path to determine the domain (look for keywords, directory structure, etc.)
3. Pay special attention to the explanations provided for leaf domains - these give important context about what each domain represents
4. Return your analysis as a valid JSON object where keys are domain paths and values are lists of file paths
5. Include ONLY files that belong to the domains in the hierarchy - files that don't fit should not be included
6. Only assign files to leaf domains (domains with no subdomains)
7. Be thoughtful about assigning files to multiple domains - only do so when a file clearly serves multiple purposes
"""

        # Format leaf domains for the prompt with explanations
        leaf_domain_list = []
        for path in leaf_domains:
            domain_name = self.domain_path_to_name.get(path, path)
            explanation = self.domain_path_to_explanation.get(path, "")
            if explanation:
                leaf_domain_list.append(f"- {path} ({domain_name}): {explanation}")
            else:
                leaf_domain_list.append(f"- {path} ({domain_name})")

        leaf_domain_list = "\n".join(leaf_domain_list)

        user_message = f"""
I have a list of file paths from a codebase and need to assign each relevant file to the appropriate leaf domains in this hierarchy:

{domain_hierarchy}

Here are the leaf domains where files can be assigned:
{leaf_domain_list}

Here's the list of file paths:
{json.dumps(file_paths, indent=2)}

For each leaf domain in the hierarchy, return a list of file paths that belong to that domain.
Each file should be assigned to exactly one leaf domain that best represents its primary purpose.
Pay special attention to the explanations provided for leaf domains - these give important context about what each domain represents.

Format your response as JSON with this structure:
{{
  "domain_path": ["file1.py", "file2.py", ...]
}}

Where "domain_path" is the full path to the leaf domain (e.g., "CodeAnalysis/CallGraphGeneration/LanguageSpecificAnalysis").
Only include files that clearly belong to each domain, and be thoughtful about assigning files to multiple domains.
"""

        messages = [
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_message}
        ]

        # Call the API
        if self.use_openrouter and self.openrouter_client:
            try:
                # Extract system and user prompts from messages
                system_prompt = None
                user_prompt = None

                for message in messages:
                    if message["role"] == "system":
                        system_prompt = message["content"]
                    elif message["role"] == "user":
                        user_prompt = message["content"]

                if not user_prompt:
                    logger.error("No user prompt found in request")
                    return {}

                # Call the OpenRouter client
                response = await self.openrouter_client.generate(
                    prompt=user_prompt,
                    system_prompt=system_prompt,
                    temperature=self.temperature
                )
            except Exception as e:
                logger.error(f"OpenRouter API error: {e}")
                logger.warning("Falling back to regular API request")
                response = await self.api_request.call_api(messages, temperature=self.temperature)
        else:
            response = await self.api_request.call_api(messages, temperature=self.temperature)

        if not response:
            logger.error(f"Failed to get response for top-level domain: {top_domain}")
            return {}

        # Parse the response
        try:
            mappings = json.loads(response)

            # Validate the mappings
            valid_mappings = {}
            for domain_path, files in mappings.items():
                if domain_path in leaf_domains and isinstance(files, list):
                    valid_mappings[domain_path] = files

            logger.info(f"Classified files into {len(valid_mappings)} leaf domains for top-level domain: {top_domain}")
            return valid_mappings

        except json.JSONDecodeError:
            logger.error(f"Failed to parse response as JSON for top-level domain: {top_domain}")
            logger.error(f"Response: {response}")
            return {}

    @staticmethod
    async def map_files_to_leaf_domains(
        repomap_path: str,
        domain_analysis_yaml_path: str,
        output_path: str,
        api_key: Optional[str] = None,
        model: str = "gpt-4o-mini",
        max_requests_per_minute: float = 50,
        max_tokens_per_minute: float = 100000,
        use_openrouter: bool = False,
        openrouter_base_url: str = "https://openrouter.ai/api/v1",
    ) -> bool:
        """
        Map files to leaf domains from repository map and domain analysis YAML.

        Args:
            repomap_path: Path to the repository map file
            domain_analysis_yaml_path: Path to the domain analysis YAML file
            output_path: Path to save the file-to-domain mappings YAML
            api_key: API key (OpenAI or OpenRouter depending on use_openrouter)
            model: Model to use (OpenAI or OpenRouter model ID)
            max_requests_per_minute: Rate limit for API requests
            max_tokens_per_minute: Token rate limit for API
            use_openrouter: Whether to use OpenRouter instead of OpenAI
            openrouter_base_url: Base URL for OpenRouter API

        Returns:
            True if mapping was successful, False otherwise
        """
        try:
            mapper = FileDomainMapper(
                repomap_path=repomap_path,
                domain_analysis_yaml_path=domain_analysis_yaml_path,
                output_path=output_path,
                api_key=api_key,
                model=model,
                max_requests_per_minute=max_requests_per_minute,
                max_tokens_per_minute=max_tokens_per_minute,
                use_openrouter=use_openrouter,
                openrouter_base_url=openrouter_base_url,
            )

            result = await mapper.map_files_to_domains()
            return result.success

        except Exception as e:
            logger.error(f"Error in file-to-domain mapping: {e}")
            return False

async def main():
    """Main entry point for the file domain mapper."""
    parser = argparse.ArgumentParser(description="Map files to leaf domains")
    # parser.add_argument("--repomap", required=True, help="Path to the repository map file")
    # parser.add_argument("--domain-yaml", required=True, help="Path to the domain analysis YAML file")
    # parser.add_argument("--output", required=True, help="Path to save the file-to-domain mappings YAML")
    parser.add_argument("--api-key", help="OpenAI API key (if not provided, will try to get from environment)")
    parser.add_argument("--model", default="o3-mini", help="Model to use (OpenAI or OpenRouter model ID)")
    parser.add_argument("--requests-per-minute", type=float, default=50, help="Rate limit for API requests")
    parser.add_argument("--tokens-per-minute", type=float, default=100000, help="Token rate limit for API")
    parser.add_argument("--temperature", type=float, default=0.0, help="Temperature for the model")
    parser.add_argument("--use-openrouter", default=True, action="store_true", help="Use OpenRouter instead of OpenAI")
    parser.add_argument("--openrouter-base-url", default="https://openrouter.ai/api/v1", help="Base URL for OpenRouter API")

    args = parser.parse_args()
    # args.repomap = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/complete_repomap_bracket.txt"
    # args.domain_yaml = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/output/domain_analysis_bracket_expln.yaml"
    # args.output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/output/file_domain_maps/bracket5.yaml"

    # args.repomap = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/complete_repomap_django.txt"
    # args.domain_yaml = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/output/domain_analysis_django_expl.yaml"
    # args.output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/output/file_domain_maps/django_explained.yaml"

    args.repomap = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/complete_repomap_gitlab.txt"
    args.domain_yaml = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/output/domain_analysis_gitlab_expln.yaml"
    args.output = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/output/file_domain_maps/gitlab_explained_gemini.yaml"


    try:
        mapper = FileDomainMapper(
            repomap_path=args.repomap,
            domain_analysis_yaml_path=args.domain_yaml,
            output_path=args.output,
            api_key=args.api_key,
            model=args.model,
            max_requests_per_minute=args.requests_per_minute,
            max_tokens_per_minute=args.tokens_per_minute,
            temperature=args.temperature,
            use_openrouter=args.use_openrouter,
            openrouter_base_url=args.openrouter_base_url,
        )

        result = await mapper.map_files_to_domains()

        if result.success:
            logger.info("File-to-domain mapping completed successfully")
            return 0
        else:
            logger.error(f"File-to-domain mapping failed: {result.error_message}")
            return 1

    except Exception as e:
        logger.error(f"Error in file-to-domain mapping: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
