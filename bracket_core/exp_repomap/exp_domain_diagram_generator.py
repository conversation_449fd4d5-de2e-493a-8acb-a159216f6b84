#!/usr/bin/env python3
"""
Enhanced Domain Diagram Generator for Codebase

This module extends the original domain_diagram_generator.py to use OpenRouter with Gemini
for domains exceeding 45K tokens, allowing for larger context windows and longer outputs.

Key enhancements:
1. Adds OpenRouter with Gemini support for large domains (>45K tokens)
2. Analyzes token counts before sending to LLM to determine the appropriate model
3. Avoids truncating functions by using models with larger context windows
4. Allows for longer output mermaid diagrams (10-15K tokens)

Usage:
    python enhanced_domain_diagram_generator.py --domain-traces <path> --functions-parquet <path> --output-dir <path>
"""

import os
import yaml
import json
import logging
import asyncio
import aiohttp
import time
import pandas as pd
from typing import Dict, List, Set, Tuple, Optional, Union, Any
from dataclasses import dataclass, field
import re

# Import clients for diagram generation
from bracket_core.llm.get_client import get_claude_client, get_openrouter_client
from bracket_core.llm.rate_limiter import RateLimiter
from bracket_core.llm.tokens import num_tokens_from_string
from bracket_core.domain_diagram_generator import DomainDiagramGenerator, DiagramGenerationResult as BaseDiagramGenerationResult
from dataclasses import dataclass, field

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class DiagramGenerationResult(BaseDiagramGenerationResult):
    """Extended result of generating domain diagrams with OpenRouter support."""
    openrouter_info: Dict[str, Any] = field(default_factory=dict)  # Information about OpenRouter usage

class EnhancedDomainDiagramGenerator(DomainDiagramGenerator):
    """
    Enhanced Domain Diagram Generator that uses OpenRouter with Gemini for large domains.

    This class extends the original DomainDiagramGenerator to use OpenRouter with Gemini
    for domains exceeding 45K tokens, allowing for larger context windows and longer outputs.
    """

    def __init__(
        self,
        domain_traces_yaml_path: str,
        functions_parquet_path: str,
        output_dir: str,
        # Model selection and configuration
        model_type: str = "openai",  # "claude" or "openai"
        # Claude parameters
        claude_api_key: Optional[str] = None,
        claude_model: str = "claude-3-7-sonnet-20250219",
        # OpenAI parameters
        openai_api_key: Optional[str] = None,
        openai_model: str = "o3-mini",
        # OpenRouter parameters
        use_openrouter: bool = True,
        openrouter_api_key: Optional[str] = None,
        openrouter_model: str = "google/gemini-2.5-pro-preview",
        openrouter_token_threshold: int = 45000,  # Token threshold to switch to OpenRouter
        openrouter_max_concurrent: int = 3,  # Maximum concurrent OpenRouter calls
        # Common parameters
        max_tokens: int = 8096,
        temperature: float = 0.5,
        max_requests_per_minute: float = 10000,
        max_tokens_per_minute: float = 10000000,
        # Parallelization parameters
        max_concurrent_tasks: int = 5,
        # Hierarchy parameters
        max_hierarchy_depth: Optional[int] = None,
        # Caching parameters
        cache_dir: Optional[str] = None,
        use_cache: bool = True,
        # Token limit parameters
        max_input_tokens: int = 100000,  # Maximum number of tokens for input functions
    ):
        """Initialize the enhanced domain diagram generator.

        Args:
            domain_traces_yaml_path: Path to the domain traces YAML file
            functions_parquet_path: Path to the semantic_documented_functions.parquet file
            output_dir: Directory to save generated diagrams
            model_type: Type of model to use ("claude" or "openai")
            claude_api_key: Anthropic API key. If None, will try to get from environment.
            claude_model: Claude model to use.
            openai_api_key: OpenAI API key. If None, will try to get from environment.
            openai_model: OpenAI model to use.
            use_openrouter: Whether to use OpenRouter for large domains
            openrouter_api_key: OpenRouter API key. If None, will try to get from environment.
            openrouter_model: OpenRouter model to use (Gemini)
            openrouter_token_threshold: Token threshold to switch to OpenRouter
            max_tokens: Maximum tokens to generate.
            temperature: Sampling temperature.
            max_requests_per_minute: Rate limit for API requests
            max_tokens_per_minute: Token rate limit for API
            max_concurrent_tasks: Maximum number of concurrent tasks for parallel processing
            max_hierarchy_depth: Maximum hierarchy depth to process (None for all levels)
            cache_dir: Directory to cache intermediate results (None for no caching)
            use_cache: Whether to use caching for intermediate results
            max_input_tokens: Maximum number of tokens for input functions
        """
        # Initialize the parent class
        super().__init__(
            domain_traces_yaml_path=domain_traces_yaml_path,
            functions_parquet_path=functions_parquet_path,
            output_dir=output_dir,
            model_type=model_type,
            claude_api_key=claude_api_key,
            claude_model=claude_model,
            openai_api_key=openai_api_key,
            openai_model=openai_model,
            max_tokens=max_tokens,
            temperature=temperature,
            max_requests_per_minute=max_requests_per_minute,
            max_tokens_per_minute=max_tokens_per_minute,
            max_concurrent_tasks=max_concurrent_tasks,
            max_hierarchy_depth=max_hierarchy_depth,
            cache_dir=cache_dir,
            use_cache=use_cache,
            max_input_tokens=max_input_tokens,
        )

        # OpenRouter configuration
        self.use_openrouter = use_openrouter
        self.openrouter_api_key = openrouter_api_key
        self.openrouter_model = openrouter_model
        self.openrouter_token_threshold = openrouter_token_threshold
        self.openrouter_max_concurrent = openrouter_max_concurrent

        # Initialize OpenRouter client if needed
        self.openrouter_client = None
        if self.use_openrouter:
            try:
                self.openrouter_client = get_openrouter_client(
                    api_key=openrouter_api_key,
                    model=openrouter_model,
                    max_tokens=15000,  # Allow for longer output mermaid diagrams
                    temperature=temperature,
                )
                logger.info(f"Initialized OpenRouter client with model: {openrouter_model}")
            except Exception as e:
                logger.error(f"Error initializing OpenRouter client: {e}")
                logger.warning("OpenRouter will not be used due to initialization error")
                self.use_openrouter = False

            # Create a separate rate limiter for OpenRouter calls
            # This limits the number of concurrent OpenRouter calls to avoid rate limits
            self.openrouter_semaphore = asyncio.Semaphore(self.openrouter_max_concurrent)
            logger.info(f"Created OpenRouter semaphore limiting to {self.openrouter_max_concurrent} concurrent calls")

        # Add a dictionary to track which model was used for each domain
        self.model_used_for_domain = {}

        # Add a counter for domains that used OpenRouter
        self.openrouter_domains_count = 0

        # Add counters for tracking OpenRouter usage
        self.openrouter_calls_total = 0
        self.openrouter_calls_in_progress = 0
        self.openrouter_calls_completed = 0

    async def _call_openrouter_api(self, system_prompt: str, user_prompt: str) -> Tuple[str, str]:
        """
        Call the OpenRouter API with the given prompts.

        Args:
            system_prompt: System prompt for the API call
            user_prompt: User prompt for the API call

        Returns:
            Tuple of (response_text, raw_response_json)
        """
        # Check if OpenRouter client is available
        if not self.openrouter_client:
            error_msg = "OpenRouter client is not initialized"
            logger.error(error_msg)
            return "", error_msg

        # Use the OpenRouter semaphore to limit concurrent calls
        async with self.openrouter_semaphore:
            try:
                # Update counters
                self.openrouter_calls_total += 1
                self.openrouter_calls_in_progress += 1

                # Log the OpenRouter call
                logger.info(f"Making OpenRouter API call ({self.openrouter_calls_in_progress}/{self.openrouter_max_concurrent} concurrent, {self.openrouter_calls_total} total)")

                # Call the OpenRouter API
                response = await self.openrouter_client.generate(
                    system_prompt=system_prompt,
                    prompt=user_prompt,
                    max_tokens=15000,  # Allow for longer output mermaid diagrams
                    temperature=0.7,
                    top_p=0.8
                )

                # Update counters
                self.openrouter_calls_in_progress -= 1
                self.openrouter_calls_completed += 1

                # Log completion
                logger.info(f"Completed OpenRouter API call ({self.openrouter_calls_in_progress}/{self.openrouter_max_concurrent} concurrent, {self.openrouter_calls_completed}/{self.openrouter_calls_total} completed)")

                # Return the response
                return response, response

            except Exception as e:
                # Update counters
                self.openrouter_calls_in_progress -= 1

                error_msg = f"Error calling OpenRouter API: {str(e)}"
                logger.error(error_msg)
                return "", error_msg

    async def analyze_domain_token_count(self, domain_trace: str, functions: List[str]) -> int:
        """
        Analyze the token count for a domain to determine which model to use.

        Args:
            domain_trace: Domain trace string
            functions: List of function signatures in this domain

        Returns:
            Total token count for the domain
        """
        logger.info(f"Analyzing token count for domain: {domain_trace}")

        # Prepare function data for the prompt
        function_details = []
        missing_functions = []
        found_functions = []

        for node_id in functions:
            # Try direct lookup first
            if node_id in self.function_data:
                func_data = self.function_data[node_id]
                found_functions.append(node_id)

                # Format function details
                func_detail = {
                    'node_id': node_id,
                    'description': func_data.get('description', ''),
                    'text': func_data.get('text', ''),
                }

                function_details.append(func_detail)

            # Try lookup via simplified path map
            elif node_id in self.simplified_path_map:
                actual_node_id = self.simplified_path_map[node_id]
                func_data = self.function_data[actual_node_id]
                found_functions.append(node_id)

                # Format function details
                func_detail = {
                    'node_id': node_id,
                    'description': func_data.get('description', ''),
                    'text': func_data.get('text', ''),
                }

                function_details.append(func_detail)

            else:
                missing_functions.append(node_id)

        # Get the system prompt
        system_prompt = """You are an expert software architect who creates clear, informative mermaid diagrams to visualize code architecture.
Your task is to create a detailed mermaid diagram that represents the LOGICAL RELATIONSHIPS and INTERACTIONS between functions in a specific domain of a codebase.

Guidelines for creating the diagram:
1. Focus on the LOGICAL PURPOSE and ROLE of each function within the domain
2. Emphasize how functions work together to accomplish domain goals
3. Show meaningful relationships and dependencies between functions
4. Highlight the conceptual flow of data and control between functions
5. Group functions by their logical purpose or the feature they support
6. Represent the domain's core concepts and how functions implement them
7. Show how functions collaborate to implement domain behaviors
8. Illustrate key abstractions and patterns used in the domain
9. Include important domain-specific data structures and their transformations
10. Show initialization sequences and important process flows
11. DO NOT create a simple procedural flowchart - focus on logical relationships
12. DO NOT use tooltips or click actions - they consume unnecessary tokens
13. The diagram should be 3000-6000 tokens in size to provide comprehensive detail

Styling Guidelines (IMPORTANT):
1. Use a VERTICAL layout rather than horizontal for better readability
2. Use PASTEL COLORS for all nodes and subgraphs - avoid bright or dark colors
3. Use this consistent color scheme:
   - Core domain functions: pastel blue (#D4F1F9)
   - Supporting/utility functions: pastel yellow (#FFF8DC)
   - Data structure handlers: pastel green (#E0F8E0)
   - Error handling functions: pastel red (#FFE4E1)
   - Initialization/setup functions: pastel purple (#E6E6FA)
   - Logical groupings/subgraphs: very light gray (#F8F8F8) with pastel borders
4. Use rounded rectangles for most nodes: node[shape="rounded-rectangle"]
5. Use different node shapes to represent different types of functions when appropriate
6. Use consistent line thickness and arrow styles
7. Ensure proper spacing between nodes and subgraphs
8. Follow strict mermaid.js syntax to ensure the diagram renders correctly
9. Avoid use of brackets, `(` or `)` and avoid adding any notes. Please follow the mermaid syntax properly."""

        # Get the user prompt
        level = self.domain_levels.get(domain_trace, 0)
        function_details_json = json.dumps(function_details, indent=2)
        user_prompt = f"""Create a detailed mermaid diagram for the leaf domain: {domain_trace} (Hierarchy Level: {level})

This is a leaf domain with no subdomains. Focus on showing the LOGICAL RELATIONSHIPS and INTERACTIONS between functions, not just their implementation details.

Here are the functions in this domain:

{function_details_json}

Please generate a comprehensive mermaid diagram that shows:
1. The LOGICAL PURPOSE of each function within the domain context
2. How functions COLLABORATE to implement domain behaviors
3. The MEANINGFUL RELATIONSHIPS and dependencies between functions
4. How functions are GROUPED by their logical purpose or features they support
5. The domain's CORE CONCEPTS and how functions implement them
6. Important DOMAIN-SPECIFIC DATA STRUCTURES and their transformations
7. Key ABSTRACTIONS and PATTERNS used in the domain

IMPORTANT STYLING REQUIREMENTS:
- Use a VERTICAL layout
- Use PASTEL COLORS for all nodes and subgraphs
- Follow the color scheme specified in the system prompt
- Ensure the diagram is well-structured and easy to read
- Follow strict mermaid.js syntax to ensure the diagram renders correctly
- Avoid use of brackets, `(` or `)` and avoid adding any notes. Please follow the mermaid syntax properly.

Make sure to capture the LOGICAL RELATIONSHIPS between functions, not just their procedural flow.
DO NOT create a simple procedural flowchart - focus on meaningful interactions and relationships.
DO NOT use tooltips or click actions in the diagram."""

        # Calculate token counts
        system_prompt_tokens = num_tokens_from_string(system_prompt, self.model_name)
        user_prompt_tokens = num_tokens_from_string(user_prompt, self.model_name)
        total_tokens = system_prompt_tokens + user_prompt_tokens

        logger.info(f"Token count for domain {domain_trace}: {total_tokens} tokens")

        return total_tokens

    async def generate_leaf_diagram(self, domain_trace: str, functions: List[str]) -> Tuple[str, str]:
        """
        Generate a mermaid diagram for a leaf domain, using OpenRouter for large domains.

        Args:
            domain_trace: Domain trace string
            functions: List of node_ids in this domain

        Returns:
            Tuple of (diagram_content, raw_response)
        """
        logger.info(f"Generating diagram for leaf domain: {domain_trace}")

        # Check if this domain has empty file mappings in the domain file mappings YAML
        if self._is_empty_domain_mapping(domain_trace):
            logger.warning(f"Domain has empty file mappings: {domain_trace}. Generating empty domain diagram.")
            empty_diagram = "```mermaid\ngraph TD\n    A[Empty Domain - No Files Mapped]\n    B[This domain has no files mapped to it in the domain file mappings]\n    A --> B\n    style A fill:#f96,stroke:#333,stroke-width:2px\n    style B fill:#fff,stroke:#333,stroke-width:1px\n```"
            return empty_diagram, "Domain has empty file mappings in the domain file mappings YAML"

        # Check if there are any functions in this domain
        if not functions:
            logger.warning(f"No functions found for domain: {domain_trace}. Skipping diagram generation.")
            return "```mermaid\ngraph TD\n    A[No functions found in this domain]\n```", "No functions found in this domain"

        # Apply rate limiting
        await self.rate_limiter.acquire()

        # Analyze token count to determine which model to use
        if self.use_openrouter:
            total_tokens = await self.analyze_domain_token_count(domain_trace, functions)
            use_openrouter = total_tokens > self.openrouter_token_threshold

            if use_openrouter:
                logger.info(f"Using OpenRouter for domain {domain_trace} with {total_tokens} tokens (exceeds threshold of {self.openrouter_token_threshold})")
                self.openrouter_domains_count += 1
                self.model_used_for_domain[domain_trace] = f"openrouter:{self.openrouter_model}"
            else:
                logger.info(f"Using standard model for domain {domain_trace} with {total_tokens} tokens")
                self.model_used_for_domain[domain_trace] = f"{self.model_type}:{self.model_name}"
        else:
            use_openrouter = False
            self.model_used_for_domain[domain_trace] = f"{self.model_type}:{self.model_name}"

        # Prepare function data for the prompt
        function_details = []
        missing_functions = []
        found_functions = []

        # Update total functions counter
        self.total_functions_requested += len(functions)

        for node_id in functions:
            # Try direct lookup first
            if node_id in self.function_data:
                func_data = self.function_data[node_id]
                found_functions.append(node_id)

                # Format function details
                func_detail = {
                    'node_id': node_id,
                    'description': func_data.get('description', ''),
                    'text': func_data.get('text', ''),
                }

                function_details.append(func_detail)

            # Try lookup via simplified path map
            elif node_id in self.simplified_path_map:
                actual_node_id = self.simplified_path_map[node_id]
                func_data = self.function_data[actual_node_id]
                found_functions.append(node_id)

                # Format function details
                func_detail = {
                    'node_id': node_id,
                    'description': func_data.get('description', ''),
                    'text': func_data.get('text', ''),
                }

                function_details.append(func_detail)

            else:
                missing_functions.append(node_id)
                # Update missing functions counter
                self.total_functions_missing += 1

        # Track missing functions by domain
        if missing_functions:
            self.missing_function_counts[domain_trace] = len(missing_functions)

        # Log the results
        if found_functions:
            logger.info(f"Found {len(found_functions)} functions for domain {domain_trace}")
        if missing_functions:
            logger.warning(f"Missing {len(missing_functions)} functions for domain {domain_trace}: {missing_functions}")

        # If no functions were found at all, return a message about it
        if not function_details:
            logger.warning(f"No functions found for domain: {domain_trace}. Skipping diagram generation.")
            return "```mermaid\ngraph TD\n    A[No functions found in this domain]\n```", f"No functions found in domain: {domain_trace}. Please check that the function signatures in domain_traces.yaml match those in the functions parquet file."

        # Update the system prompt to request more functional flow diagrams
        system_prompt = """You are an expert software architect who creates clear, informative mermaid diagrams to visualize code architecture.
Your task is to create a detailed mermaid diagram that represents the LOGICAL RELATIONSHIPS and INTERACTIONS between functions in a specific domain of a codebase.

Guidelines for creating the diagram:
1. Focus on the LOGICAL PURPOSE and ROLE of each function within the domain
2. Emphasize how functions work together to accomplish domain goals
3. Show meaningful relationships and dependencies between functions
4. Highlight the conceptual flow of data and control between functions
5. Group functions by their logical purpose or the feature they support
6. Represent the domain's core concepts and how functions implement them
7. Show how functions collaborate to implement domain behaviors
8. Illustrate key abstractions and patterns used in the domain
9. Include important domain-specific data structures and their transformations
10. Show initialization sequences and important process flows
11. DO NOT create a simple procedural flowchart - focus on logical relationships
12. DO NOT use tooltips or click actions - they consume unnecessary tokens
13. The diagram should be 3000-6000 tokens in size to provide comprehensive detail

Styling Guidelines (IMPORTANT):
1. Use a VERTICAL layout rather than horizontal for better readability
2. Use PASTEL COLORS for all nodes and subgraphs - avoid bright or dark colors
3. Use this consistent color scheme:
   - Core domain functions: pastel blue (#D4F1F9)
   - Supporting/utility functions: pastel yellow (#FFF8DC)
   - Data structure handlers: pastel green (#E0F8E0)
   - Error handling functions: pastel red (#FFE4E1)
   - Initialization/setup functions: pastel purple (#E6E6FA)
   - Logical groupings/subgraphs: very light gray (#F8F8F8) with pastel borders
4. Use rounded rectangles for most nodes: node[shape="rounded-rectangle"]
5. Use different node shapes to represent different types of functions when appropriate
6. Use consistent line thickness and arrow styles
7. Ensure proper spacing between nodes and subgraphs
8. Follow strict mermaid.js syntax to ensure the diagram renders correctly
9. Avoid use of brackets, `(` or `)` and avoid adding any notes. Please follow the mermaid syntax properly.

Your output should ONLY contain a valid mermaid diagram enclosed in triple backticks with the mermaid tag.
Ensure the diagram follows proper mermaid.js syntax and is renderable without any syntax errors.
"""

        # Get hierarchy level
        level = self.domain_levels.get(domain_trace, 0)

        # Create the user prompt
        user_prompt = f"""Create a detailed mermaid diagram for the leaf domain: {domain_trace} (Hierarchy Level: {level})

This is a leaf domain with no subdomains. Focus on showing the LOGICAL RELATIONSHIPS and INTERACTIONS between functions, not just their implementation details.

Here are the functions in this domain with their full implementation:

{json.dumps(function_details, indent=2)}

Please generate a comprehensive mermaid diagram that shows:
1. The LOGICAL PURPOSE of each function within the domain context
2. How functions COLLABORATE to implement domain behaviors
3. The MEANINGFUL RELATIONSHIPS and dependencies between functions
4. How functions are GROUPED by their logical purpose or features they support
5. The domain's CORE CONCEPTS and how functions implement them
6. Important DOMAIN-SPECIFIC DATA STRUCTURES and their transformations
7. Key ABSTRACTIONS and PATTERNS used in the domain

IMPORTANT STYLING REQUIREMENTS:
- Use a VERTICAL layout
- Use PASTEL COLORS for all nodes and subgraphs
- Follow the color scheme specified in the system prompt
- Ensure the diagram is well-structured and easy to read
- Follow strict mermaid.js syntax to ensure the diagram renders correctly
- Avoid use of brackets, `(` or `)` and avoid adding any notes. Please follow the mermaid syntax properly.

Make sure to capture the LOGICAL RELATIONSHIPS between functions, not just their procedural flow.
DO NOT create a simple procedural flowchart - focus on meaningful interactions and relationships.
DO NOT use tooltips or click actions in the diagram.
"""

        try:
            # Call the appropriate API based on model type and token count
            if use_openrouter:
                # Call the OpenRouter API
                response, raw_response = await self._call_openrouter_api(
                    system_prompt=system_prompt,
                    user_prompt=user_prompt
                )
            elif self.model_type == "claude":
                # Call the Claude API
                response = await self.claude_client.generate(
                    system_prompt=system_prompt,
                    prompt=user_prompt,
                    max_tokens=8000,
                    temperature=0.7,
                    top_p=0.8
                )
                raw_response = response
            else:  # OpenAI
                # Call the OpenAI API
                response, raw_response = await self._call_openai_api(
                    system_prompt=system_prompt,
                    user_prompt=user_prompt
                )

            # Extract the mermaid diagram
            diagram = self._extract_mermaid_diagram(response)

            if not diagram:
                logger.warning(f"No mermaid diagram found in response for {domain_trace}")
                diagram = response

            return diagram, raw_response

        except Exception as e:
            logger.error(f"Error generating diagram for {domain_trace}: {e}")
            return "", f"Error: {str(e)}"

    async def _send_api_request(self, domain_trace: str, system_prompt: str, user_prompt: str) -> Tuple[str, asyncio.Future]:
        """
        Send an API request for a domain diagram, using OpenRouter for large domains.

        Args:
            domain_trace: Domain trace string
            system_prompt: System prompt for the API call
            user_prompt: User prompt for the API call

        Returns:
            Tuple of (domain_trace, future) where future will resolve to the API response
        """
        # Check for special markers for empty domain mappings
        if system_prompt == "EMPTY_DOMAIN_MAPPING" and user_prompt == "EMPTY_DOMAIN_MAPPING":
            logger.info(f"Creating empty domain mapping future for {domain_trace}")
            # Create a future and set its result to the special marker
            future = asyncio.get_event_loop().create_future()
            future.set_result("EMPTY_DOMAIN_MAPPING")
            return domain_trace, future

        # Check cache first
        cached_diagram = self._check_cache(domain_trace)
        if cached_diagram:
            # Use cached diagram
            logger.info(f"Using cached diagram for {domain_trace}")

            # Create a future and set its result
            future = asyncio.get_event_loop().create_future()
            future.set_result(cached_diagram)

            return domain_trace, future

        # Apply rate limiting
        await self.rate_limiter.acquire()

        # Analyze token count to determine which model to use
        if self.use_openrouter:
            # Estimate token count from the prompts
            total_tokens = num_tokens_from_string(system_prompt + user_prompt, self.model_name)
            use_openrouter = total_tokens > self.openrouter_token_threshold

            if use_openrouter:
                logger.info(f"Using OpenRouter for domain {domain_trace} with {total_tokens} tokens (exceeds threshold of {self.openrouter_token_threshold})")
                self.openrouter_domains_count += 1
                self.model_used_for_domain[domain_trace] = f"openrouter:{self.openrouter_model}"

                # Check if OpenRouter client is available
                if not self.openrouter_client:
                    logger.error(f"OpenRouter client is not initialized for domain {domain_trace}")
                    # Fall back to standard model
                    use_openrouter = False
                    self.model_used_for_domain[domain_trace] = f"{self.model_type}:{self.model_name}"
                else:
                    # Create a task for the OpenRouter API call
                    task = asyncio.create_task(self._call_openrouter_api(
                        system_prompt=system_prompt,
                        user_prompt=user_prompt
                    ))

                    return domain_trace, task
        else:
            use_openrouter = False
            self.model_used_for_domain[domain_trace] = f"{self.model_type}:{self.model_name}"

        # If not using OpenRouter, use the standard model
        if self.model_type == "claude":
            # Create a task for the Claude API call
            task = asyncio.create_task(self.claude_client.generate(
                system_prompt=system_prompt,
                prompt=user_prompt,
                max_tokens=8000,
                temperature=0.7,
                top_p=0.8
            ))
        else:  # OpenAI
            # Create a task for the OpenAI API call
            task = asyncio.create_task(self._call_openai_api(
                system_prompt=system_prompt,
                user_prompt=user_prompt
            ))

        return domain_trace, task

    async def generate_combined_diagram(
        self,
        domain_name: str,
        subdomains: Dict[str, str],
        level: int
    ) -> Tuple[str, str]:
        """
        Generate a combined diagram for a domain and its subdomains, using OpenRouter for large domains.

        Args:
            domain_name: Name of the domain
            subdomains: Dictionary mapping subdomain names to their diagram file paths
            level: Hierarchy level (higher means more abstract)

        Returns:
            Tuple of (diagram_content, raw_response)
        """
        logger.info(f"Generating combined diagram for domain: {domain_name} with {len(subdomains)} subdomains")

        # Apply rate limiting
        await self.rate_limiter.acquire()

        # Read subdomain diagrams
        subdomain_diagrams = {}
        for subdomain, file_path in subdomains.items():
            try:
                with open(file_path, 'r') as f:
                    subdomain_diagrams[subdomain] = f.read()
            except Exception as e:
                logger.error(f"Error reading subdomain diagram {file_path}: {e}")
                subdomain_diagrams[subdomain] = f"Error reading diagram: {str(e)}"

        # Extract child domains
        child_domains = list(subdomains.keys())
        child_diagram_contents = {}
        for subdomain, diagram in subdomain_diagrams.items():
            # Extract just the mermaid diagram part
            mermaid_pattern = r"```mermaid\s*([\s\S]*?)\s*```"
            matches = re.findall(mermaid_pattern, diagram)
            if matches:
                child_diagram_contents[subdomain] = matches[0]
            else:
                child_diagram_contents[subdomain] = f"Error extracting mermaid diagram from {subdomain}"

        # Get domain functions (if any)
        domain_functions = []
        if domain_name in self.domain_traces:
            domain_functions = self.domain_traces[domain_name]

        # Create the system prompt
        system_prompt = """You are an expert software architect who creates clear, informative mermaid diagrams to visualize code architecture.
Your task is to create a combined mermaid diagram that represents a domain and its subdomains in a codebase, preserving the logical relationships and interactions between components.

Guidelines for creating the diagram:
1. Create a high-level diagram that shows how the subdomains relate to each other
2. Preserve the key components and relationships from each subdomain
3. Add new relationships between subdomains where they interact
4. Focus on the LOGICAL RELATIONSHIPS between components, not just their implementation details
5. Show the flow of data and control between subdomains
6. Highlight shared services or components used by multiple subdomains
7. Include error handling and alternative flows between subdomains where relevant
8. The higher the level, the more abstract the diagram should be

Styling Guidelines (IMPORTANT):
1. Use a balanced layout that clearly shows the hierarchy and relationships
2. MAINTAIN COLOR CONSISTENCY with the child domains - when referencing a subdomain, use the same color scheme that was used in its original diagram
3. Use pastel colors for all components - avoid bright or dark colors
4. For new elements not present in child domains, use this color scheme:
   - Core domain components: pastel blue (#D4F1F9)
   - Shared services: pastel yellow (#FFF8DC)
   - Data stores: pastel green (#E0F8E0)
   - External interfaces: pastel purple (#E6E6FA)
   - Error handling: pastel red (#FFE4E1)
5. Use rounded rectangles for domain components: node[shape="rounded-rectangle"]
6. Use cylinders for shared data stores: storage[shape="cylinder"]
7. Use consistent line thickness and arrow styles
8. Ensure proper spacing between components
9. Follow strict mermaid.js syntax to ensure the diagram renders correctly
10. Avoid use of brackets, `(` or `)` and avoid adding any notes. Please follow the mermaid syntax properly.

Your output should ONLY contain a valid mermaid diagram enclosed in triple backticks with the mermaid tag.
Ensure the diagram follows proper mermaid.js syntax and is renderable without any syntax errors.
"""

        # Create the user prompt
        user_prompt = f"""Create a combined mermaid diagram for the intermediate domain: {domain_name} (Hierarchy Level: {level})

This domain contains the following child domains:
{json.dumps(list(child_domains), indent=2)}
"""

        # Add information about functions directly in this domain
        if domain_functions:
            user_prompt += f"""
This domain also contains some functions directly, but DO NOT include individual function details in the diagram.
Instead, represent the overall functionality these functions provide as a cohesive unit.
"""

        # Add child diagram information
        user_prompt += f"""
Here are the diagrams for the child domains:
{json.dumps(child_diagram_contents, indent=2)}

Please generate a comprehensive mermaid diagram that shows this domain and how its child domains relate to each other.
Focus on the LOGICAL RELATIONSHIPS and INTERACTIONS between subdomains, not just their implementation details.

IMPORTANT STYLING REQUIREMENTS:
- MAINTAIN COLOR CONSISTENCY with the child domains - use the same colors for subdomains as in their original diagrams
- Use PASTEL COLORS for all new components
- Follow the color scheme specified in the system prompt
- Ensure proper spacing and layout for readability
- Follow strict mermaid.js syntax to ensure the diagram renders correctly
- Avoid use of brackets, `(` or `)` and avoid adding any notes. Please follow the mermaid syntax properly.
"""

        # Analyze token count to determine which model to use
        if self.use_openrouter:
            total_tokens = num_tokens_from_string(system_prompt + user_prompt, self.model_name)
            use_openrouter = total_tokens > self.openrouter_token_threshold

            if use_openrouter:
                logger.info(f"Using OpenRouter for combined domain {domain_name} with {total_tokens} tokens (exceeds threshold of {self.openrouter_token_threshold})")
                self.openrouter_domains_count += 1
                self.model_used_for_domain[domain_name] = f"openrouter:{self.openrouter_model}"
            else:
                logger.info(f"Using standard model for combined domain {domain_name} with {total_tokens} tokens")
                self.model_used_for_domain[domain_name] = f"{self.model_type}:{self.model_name}"
        else:
            use_openrouter = False
            self.model_used_for_domain[domain_name] = f"{self.model_type}:{self.model_name}"

        try:
            # Call the appropriate API based on model type and token count
            if use_openrouter:
                # Call the OpenRouter API
                response, raw_response = await self._call_openrouter_api(
                    system_prompt=system_prompt,
                    user_prompt=user_prompt
                )
            elif self.model_type == "claude":
                # Call the Claude API
                response = await self.claude_client.generate(
                    system_prompt=system_prompt,
                    prompt=user_prompt,
                    max_tokens=8000,
                    temperature=0.7,
                    top_p=0.8
                )
                raw_response = response
            else:  # OpenAI
                # Call the OpenAI API
                response, raw_response = await self._call_openai_api(
                    system_prompt=system_prompt,
                    user_prompt=user_prompt
                )

            # Extract the mermaid diagram
            diagram = self._extract_mermaid_diagram(response)

            if not diagram:
                logger.warning(f"No mermaid diagram found in response for {domain_name}")
                diagram = response

            return diagram, raw_response

        except Exception as e:
            logger.error(f"Error generating combined diagram for {domain_name}: {e}")
            return "", f"Error: {str(e)}"

    async def _prepare_intermediate_domain_request(self, domain_trace: str, child_diagrams: Dict[str, str]) -> Tuple[str, str, str]:
        """
        Prepare the API request for an intermediate domain diagram without waiting for the response.

        Args:
            domain_trace: Domain trace string
            child_diagrams: Dictionary mapping child domain names to their diagram file paths

        Returns:
            Tuple of (domain_trace, system_prompt, user_prompt)
        """
        # Read child diagrams
        child_diagram_contents = {}
        for child_domain, file_path in child_diagrams.items():
            try:
                with open(file_path, 'r') as f:
                    diagram = f.read()
                    # Extract just the mermaid diagram part
                    mermaid_pattern = r"```mermaid\s*([\s\S]*?)\s*```"
                    matches = re.findall(mermaid_pattern, diagram)
                    if matches:
                        child_diagram_contents[child_domain] = matches[0]
                    else:
                        child_diagram_contents[child_domain] = f"Error extracting mermaid diagram from {child_domain}"
            except Exception as e:
                logger.error(f"Error reading child diagram {file_path}: {e}")
                child_diagram_contents[child_domain] = f"Error reading diagram: {str(e)}"

        # Get child domains
        child_domains = list(child_diagrams.keys())

        # Get domain functions (if any)
        domain_functions = []
        if domain_trace in self.domain_traces:
            domain_functions = self.domain_traces[domain_trace]

        # Get hierarchy level
        level = self.domain_levels.get(domain_trace, 0)

        # Create the system prompt
        system_prompt = """You are an expert software architect who creates clear, informative mermaid diagrams to visualize code architecture.
Your task is to create a combined mermaid diagram that represents a domain and its subdomains in a codebase, preserving the logical relationships and interactions between components.

Guidelines for creating the diagram:
1. Create a high-level diagram that shows how the subdomains relate to each other
2. Preserve the key components and relationships from each subdomain
3. Add new relationships between subdomains where they interact
4. Focus on the LOGICAL RELATIONSHIPS between components, not just their implementation details
5. Show the flow of data and control between subdomains
6. Highlight shared services or components used by multiple subdomains
7. Include error handling and alternative flows between subdomains where relevant
8. The higher the level, the more abstract the diagram should be

Styling Guidelines (IMPORTANT):
1. Use a balanced layout that clearly shows the hierarchy and relationships
2. MAINTAIN COLOR CONSISTENCY with the child domains - when referencing a subdomain, use the same color scheme that was used in its original diagram
3. Use pastel colors for all components - avoid bright or dark colors
4. For new elements not present in child domains, use this color scheme:
   - Core domain components: pastel blue (#D4F1F9)
   - Shared services: pastel yellow (#FFF8DC)
   - Data stores: pastel green (#E0F8E0)
   - External interfaces: pastel purple (#E6E6FA)
   - Error handling: pastel red (#FFE4E1)
5. Use rounded rectangles for domain components: node[shape="rounded-rectangle"]
6. Use cylinders for shared data stores: storage[shape="cylinder"]
7. Use consistent line thickness and arrow styles
8. Ensure proper spacing between components
9. Follow strict mermaid.js syntax to ensure the diagram renders correctly
10. Avoid use of brackets, `(` or `)` and avoid adding any notes. Please follow the mermaid syntax properly.

Your output should ONLY contain a valid mermaid diagram enclosed in triple backticks with the mermaid tag.
Ensure the diagram follows proper mermaid.js syntax and is renderable without any syntax errors.
"""

        # Create the user prompt
        user_prompt = f"""Create a combined mermaid diagram for the intermediate domain: {domain_trace} (Hierarchy Level: {level})

This domain contains the following child domains:
{json.dumps(list(child_domains), indent=2)}
"""

        # Add information about functions directly in this domain
        if domain_functions:
            user_prompt += f"""
This domain also contains some functions directly, but DO NOT include individual function details in the diagram.
Instead, represent the overall functionality these functions provide as a cohesive unit.
"""

        # Add child diagram information
        user_prompt += f"""
Here are the diagrams for the child domains:
{json.dumps(child_diagram_contents, indent=2)}

Please generate a comprehensive mermaid diagram that shows this domain and how its child domains relate to each other.
Focus on the LOGICAL RELATIONSHIPS and INTERACTIONS between subdomains, not just their implementation details.

IMPORTANT STYLING REQUIREMENTS:
- MAINTAIN COLOR CONSISTENCY with the child domains - use the same colors for subdomains as in their original diagrams
- Use PASTEL COLORS for all new components
- Follow the color scheme specified in the system prompt
- Ensure proper spacing and layout for readability
- Follow strict mermaid.js syntax to ensure the diagram renders correctly
- Avoid use of brackets, `(` or `)` and avoid adding any notes. Please follow the mermaid syntax properly.
"""

        # Check if this is an empty domain mapping
        if self._is_empty_domain_mapping(domain_trace):
            logger.warning(f"Domain has empty file mappings: {domain_trace}. Generating empty domain diagram.")
            return domain_trace, "EMPTY_DOMAIN_MAPPING", "EMPTY_DOMAIN_MAPPING"

        return domain_trace, system_prompt, user_prompt

    async def generate_all_diagrams(self) -> DiagramGenerationResult:
        """
        Generate all diagrams for the domain hierarchy using a level-by-level approach.

        Returns:
            DiagramGenerationResult containing the generation results
        """
        # Call the parent method to generate all diagrams
        base_result = await super().generate_all_diagrams()

        # Create our extended result object
        result = DiagramGenerationResult(
            success=base_result.success,
            error_message=base_result.error_message,
            diagram_files=base_result.diagram_files,
            raw_responses=base_result.raw_responses,
            model_used=base_result.model_used,
            hierarchy_info=base_result.hierarchy_info
        )

        # Add OpenRouter usage information to the result
        result.openrouter_info = {
            'openrouter_domains_count': self.openrouter_domains_count,
            'model_used_for_domain': self.model_used_for_domain,
            'openrouter_calls_total': self.openrouter_calls_total,
            'openrouter_calls_completed': self.openrouter_calls_completed,
            'openrouter_max_concurrent': self.openrouter_max_concurrent
        }

        # Log OpenRouter usage
        logger.info(f"Used OpenRouter for {self.openrouter_domains_count} domains with {self.openrouter_calls_completed}/{self.openrouter_calls_total} API calls completed")

        return result


async def main():
    """Main entry point for the enhanced domain diagram generator."""
    import argparse

    parser = argparse.ArgumentParser(description="Generate mermaid diagrams from domain traces with OpenRouter support for large domains")

    # # Input/output paths
    # parser.add_argument("--domain-traces", required=True, help="Path to the domain traces YAML file")
    # parser.add_argument("--functions-parquet", required=True, help="Path to the semantic_documented_functions.parquet file")
    # parser.add_argument("--output-dir", required=True, help="Directory to save generated diagrams")
  # Diagram generation approach
    parser.add_argument("--approach", default="hierarchical", choices=["simplified", "hierarchical"],
                        help="Approach to use for diagram generation (simplified or hierarchical)")
    parser.add_argument("--max-hierarchy-depth", type=int, default=None,
                        help="Maximum hierarchy depth to process (None for all levels)")
    parser.add_argument("--cache-dir", default=None,
                        help="Directory to cache intermediate results (None for no caching)")

    # Model selection
    parser.add_argument("--model-type", default="openai", choices=["claude", "openai"],
                        help="Type of model to use (claude or openai)")

    # Claude parameters
    parser.add_argument("--claude-api-key", help="Anthropic API key (if not provided, will try to get from environment)")
    parser.add_argument("--claude-model", default="claude-3-5-sonnet-20241022", help="Claude model to use")

    # OpenAI parameters
    parser.add_argument("--openai-api-key", help="OpenAI API key (if not provided, will try to get from environment)")
    parser.add_argument("--openai-model", default="o3-mini", help="OpenAI model to use")

    # OpenRouter parameters
    parser.add_argument("--use-openrouter", action="store_true", default=True,
                        help="Whether to use OpenRouter for large domains")
    parser.add_argument("--openrouter-api-key", help="OpenRouter API key (if not provided, will try to get from environment)")
    parser.add_argument("--openrouter-model", default="google/gemini-2.5-pro-preview",
                        help="OpenRouter model to use (Gemini)")
    parser.add_argument("--openrouter-token-threshold", type=int, default=45000,
                        help="Token threshold to switch to OpenRouter")
    parser.add_argument("--openrouter-max-concurrent", type=int, default=3,
                        help="Maximum number of concurrent OpenRouter API calls")

    # Common parameters
    parser.add_argument("--max-tokens", type=int, default=8000, help="Maximum tokens to generate")
    parser.add_argument("--temperature", type=float, default=0.5, help="Sampling temperature")
    parser.add_argument("--requests-per-minute", type=float, default=2000, help="Rate limit for API requests")
    parser.add_argument("--tokens-per-minute", type=float, default=5000000, help="Token rate limit for API requests")

    # Parallelization parameters
    parser.add_argument("--max-concurrent-tasks", type=int, default=10,
                        help="Maximum number of concurrent tasks for parallel processing")

    args = parser.parse_args()

    args.domain_traces = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/domain_traces.yaml"
    args.functions_parquet = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/semantic_documented_functions.parquet"
    args.output_dir = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/mermaid_outputs_improved"

    try:
        # Create output directory if it doesn't exist
        os.makedirs(args.output_dir, exist_ok=True)

        # Create cache directory if specified
        if args.cache_dir:
            os.makedirs(args.cache_dir, exist_ok=True)

        # Create an enhanced domain diagram generator
        generator = EnhancedDomainDiagramGenerator(
            domain_traces_yaml_path=args.domain_traces,
            functions_parquet_path=args.functions_parquet,
            output_dir=args.output_dir,
            model_type=args.model_type,
            # Claude parameters
            claude_api_key=args.claude_api_key,
            claude_model=args.claude_model,
            # OpenAI parameters
            openai_api_key=args.openai_api_key,
            openai_model=args.openai_model,
            # OpenRouter parameters
            use_openrouter=args.use_openrouter,
            openrouter_api_key=args.openrouter_api_key,
            openrouter_model=args.openrouter_model,
            openrouter_token_threshold=args.openrouter_token_threshold,
            openrouter_max_concurrent=args.openrouter_max_concurrent,
            # Common parameters
            max_tokens=args.max_tokens,
            temperature=args.temperature,
            max_requests_per_minute=args.requests_per_minute,
            max_tokens_per_minute=args.tokens_per_minute,
            # Parallelization parameters
            max_concurrent_tasks=args.max_concurrent_tasks,
            # Hierarchy parameters
            max_hierarchy_depth=args.max_hierarchy_depth,
            # Caching parameters
            cache_dir=args.cache_dir,
        )

        # Generate all diagrams
        if args.approach == "hierarchical":
            logger.info("Using hierarchical approach for diagram generation")
            result = await generator.generate_all_diagrams()
        else:
            logger.info("Using simplified approach for diagram generation")
            result = await generator.generate_all_diagrams()

        if result.success:
            logger.info("Domain diagram generation completed successfully")
            logger.info(f"Generated {len(result.diagram_files)} diagrams")

            # Log hierarchy information if available
            if hasattr(result, 'hierarchy_info') and result.hierarchy_info:
                logger.info("Hierarchy Information:")
                logger.info(f"  Total Levels: {result.hierarchy_info.get('total_levels', 0)}")
                logger.info("  Domains by Level:")
                for level, count in result.hierarchy_info.get('domains_by_level', {}).items():
                    logger.info(f"    Level {level}: {count} domains")
                logger.info("  Processing Time by Level:")
                for level, time_taken in result.hierarchy_info.get('processing_time_by_level', {}).items():
                    logger.info(f"    Level {level}: {time_taken:.2f} seconds")

            # Log OpenRouter usage information if available
            if isinstance(result, DiagramGenerationResult) and result.openrouter_info:
                logger.info("OpenRouter Usage Information:")
                logger.info(f"  Domains using OpenRouter: {result.openrouter_info.get('openrouter_domains_count', 0)}")
                logger.info(f"  Total OpenRouter API calls: {result.openrouter_info.get('openrouter_calls_total', 0)}")
                logger.info(f"  Completed OpenRouter API calls: {result.openrouter_info.get('openrouter_calls_completed', 0)}")

            # Log individual diagrams
            logger.info("Generated Diagrams:")
            for domain, file_path in result.diagram_files.items():
                model_used = "unknown"
                if hasattr(result, 'openrouter_info'):
                    model_used = result.openrouter_info.get('model_used_for_domain', {}).get(domain, "unknown")
                logger.info(f"  - {domain}: {file_path} (Model: {model_used})")

            return 0
        else:
            logger.error(f"Domain diagram generation failed: {result.error_message}")
            return 1

    except Exception as e:
        logger.error(f"Error in domain diagram generation: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
