# JSON Domain Diagram Generator

This module extends the enhanced domain diagram generator to work with a JSON structure where leaf domains are keys, with file paths as sub-keys, and file code as values.

## Features

- Reads a JSON file with domain -> file paths -> code structure
- Processes each leaf domain in parallel
- Skips any domain containing "Unclassified"
- Generates mermaid diagrams for each leaf domain
- Supports OpenRouter with Gemini for large domains

## Usage

### Command Line

```bash
python exp_domain_diagram_generator_json.py --domain-file-json <path> --output-dir <path>
```

### Required Arguments

- `--domain-file-json`: Path to the JSON file with domain -> file paths -> code structure
- `--output-dir`: Directory to save generated diagrams

### Optional Arguments

#### Model Selection

- `--model-type`: Type of model to use (claude or openai, default: openai)

#### Claude Parameters

- `--claude-api-key`: Anthropic API key (if not provided, will try to get from environment)
- `--claude-model`: Claude model to use (default: claude-3-7-sonnet-20250219)

#### OpenAI Parameters

- `--openai-api-key`: OpenAI API key (if not provided, will try to get from environment)
- `--openai-model`: OpenAI model to use (default: o3-mini)

#### OpenRouter Parameters

- `--use-openrouter`: Whether to use OpenRouter for large domains (default: True)
- `--openrouter-api-key`: OpenRouter API key (if not provided, will try to get from environment)
- `--openrouter-model`: OpenRouter model to use (default: google/gemini-2.5-pro-preview)
- `--openrouter-token-threshold`: Token threshold to switch to OpenRouter (default: 45000)
- `--openrouter-max-concurrent`: Maximum number of concurrent OpenRouter API calls (default: 3)

#### Common Parameters

- `--max-tokens`: Maximum tokens to generate (default: 8000)
- `--temperature`: Sampling temperature (default: 0.5)
- `--requests-per-minute`: Rate limit for API requests (default: 2000)
- `--tokens-per-minute`: Token rate limit for API requests (default: 5000000)

#### Parallelization Parameters

- `--max-concurrent-tasks`: Maximum number of concurrent tasks for parallel processing (default: 10)

#### Caching Parameters

- `--cache-dir`: Directory to cache intermediate results (None for no caching)
- `--no-cache`: Disable caching

## Input JSON Format

The input JSON file should have the following structure:

```json
{
  "Domain/Subdomain/Leaf Domain 1": {
    "file_path_1.py": "def function1():\n    print('Hello, world!')",
    "file_path_2.py": "class MyClass:\n    def __init__(self):\n        pass"
  },
  "Domain/Subdomain/Leaf Domain 2": {
    "file_path_3.py": "def another_function():\n    return 42",
    "file_path_4.py": "import os\n\ndef get_files():\n    return os.listdir('.')"
  }
}
```

## Output

The generator creates mermaid diagrams for each leaf domain in the specified output directory. The diagrams are saved as markdown files with the domain name as the filename (with slashes replaced by underscores).

## Example

```bash
python exp_domain_diagram_generator_json.py \
  --domain-file-json /path/to/domain_file_repomap.json \
  --output-dir /path/to/output \
  --model-type openai \
  --openai-model o3-mini \
  --max-concurrent-tasks 10
```

## Testing

A test script is provided to verify the implementation:

```bash
python test_json_diagram_generator.py
```

This script creates a small test JSON file with domain -> file paths -> code structure and runs the JSON Domain Diagram Generator on it.
