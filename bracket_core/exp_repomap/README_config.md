# Experimental IRL Pipeline Configuration

This document explains how to use configuration files with the experimental IRL pipeline.

## Overview

The experimental IRL pipeline supports configuration files in YAML or JSON format. This allows you to:

1. Store and reuse complex configurations
2. Share configurations with others
3. Version control your configurations
4. Avoid long command-line arguments

## Creating a Configuration Template

You can create a configuration template using the `--create-config-template` option:

```bash
python -m bracket_core.exp_repomap.exp_irl --create-config-template my_config.yaml
```

This will create a template configuration file with default values. You can specify the format using the `--config-format` option:

```bash
python -m bracket_core.exp_repomap.exp_irl --create-config-template my_config.json --config-format json
```

## Using a Configuration File

To use a configuration file, specify it with the `--config` option:

```bash
python -m bracket_core.exp_repomap.exp_irl --config my_config.yaml
```

## Configuration File Format

The configuration file can be in YAML or JSON format. Here's an example YAML configuration:

```yaml
# General parameters
repo_dir: "/path/to/repository"
output_dir: "/path/to/output"
verbose: false

# Repomap parameters
repomap:
  batch_size: 1000
  top_percentage: 0.3
  min_functions: 3
  max_functions: 15
  exclude_tests: true
  output_format: "json"
  include_extensions:
    - ".py"
    - ".js"
    - ".ts"
    - ".java"
    - ".rb"

# Domain analysis parameters
domain_analysis:
  model: "gpt-4o-mini"
  use_openrouter: true
  max_tokens_per_chunk: 500000
  disable_parallel: false
  max_concurrent_tasks: 0
  generate_explanations: true

# File domain mapper parameters
file_mapper:
  model: "gpt-4o-mini"
  use_openrouter: false
  max_files_per_batch: 50

# Diagram generator parameters
diagram_generator:
  model_type: "openai"
  openai_model: "gpt-4o-mini"
  use_openrouter: true
  openrouter_model: "google/gemini-2.5-pro-preview"
  max_concurrent_tasks: 10

# Rate limiting parameters
rate_limits:
  max_requests_per_minute: 1200
  max_tokens_per_minute: 1000000
```

## Overriding Configuration with Command-Line Arguments

You can override any configuration parameter using command-line arguments. For example:

```bash
python -m bracket_core.exp_repomap.exp_irl --config my_config.yaml --repo-dir /new/repo/path --verbose
```

This will use the configuration from `my_config.yaml` but override the `repo_dir` parameter with `/new/repo/path` and set `verbose` to `true`.

## Required Parameters

The following parameters are required:

- `repo_dir`: Path to the repository to analyze
- `output_dir`: Directory to save output artifacts

You must specify these parameters either in the configuration file or on the command line.

## Configuration Sections

The configuration file is organized into the following sections:

1. **General Parameters**: Top-level parameters like `repo_dir` and `output_dir`
2. **Repomap Parameters**: Parameters for the repository map generation step
3. **Domain Analysis Parameters**: Parameters for the domain analysis step
4. **File Domain Mapper Parameters**: Parameters for the file-to-domain mapping step
5. **Diagram Generator Parameters**: Parameters for the diagram generation step
6. **Rate Limiting Parameters**: Parameters for API rate limiting

## Example Configurations

### Minimal Configuration

```yaml
repo_dir: "/path/to/repository"
output_dir: "/path/to/output"
repomap:
  include_extensions:
    - ".py"
    - ".js"
```

### Performance-Optimized Configuration

```yaml
repo_dir: "/path/to/repository"
output_dir: "/path/to/output"
repomap:
  batch_size: 2000
  include_extensions:
    - ".py"
    - ".js"
    - ".ts"
domain_analysis:
  max_concurrent_tasks: 10
file_mapper:
  max_files_per_batch: 100
diagram_generator:
  max_concurrent_tasks: 20
rate_limits:
  max_requests_per_minute: 3000
  max_tokens_per_minute: 3000000
```

### Quality-Optimized Configuration

```yaml
repo_dir: "/path/to/repository"
output_dir: "/path/to/output"
repomap:
  top_percentage: 0.5
  min_functions: 5
  max_functions: 30
  include_extensions:
    - ".py"
    - ".js"
    - ".ts"
    - ".java"
    - ".rb"
    - ".c"
    - ".cpp"
    - ".go"
domain_analysis:
  model: "gpt-4.1-2025-04-14"
  generate_explanations: true
file_mapper:
  model: "gpt-4.1-2025-04-14"
diagram_generator:
  model_type: "openai"
  openai_model: "gpt-4.1-2025-04-14"
```
