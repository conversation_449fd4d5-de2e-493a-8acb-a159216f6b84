
│__init__.py:
⋮...

│call_graph_generator.py:
⋮...
│def create_patched_callgraph()
│def find_callgraph_executable()
│def get_file_language()
│def group_files_by_language()
│def analyze_language_files()
│def merge_yaml_files()
│def create_graph_structure()
│def generate_basic_callgraph()
│def enhance_cross_file_resolution()
│def main()
⋮...

│cli.py:
⋮...
│def file_call_graph()
│def map_files_to_domains()
│def normalize_function_paths()
│def build_domain_traces()
│def visualize()
│def analyze()
│def main()
⋮...

│documenting.py:
⋮...
│class EntityExtraction
│def async process_api_requests_from_file()
│class StatusTracker
│class APIRequest
│def async call_api()
│def api_endpoint_from_url()
│def append_to_jsonl()
│def num_tokens_consumed_from_request()
│def task_id_generator_function()
│def save_simplified_csv()
│def extract_params()
│def convert_parquet_to_simplified_csv()
│def extract_params()
│def async document_functions()
│def async document_functions_with_significance()
│def async document_functions_with_context_significance()
│def convert_edges_parquet_to_csv()
⋮...

│domain_analysis.py:
⋮...
│def count_tokens()
│class StatusTracker
│class DomainAnalysisResult
│class ChunkProcessingResult
│class DomainAnalyzer
│    def __init__()
│    def _split_functions_into_chunks()
│def async _process_chunk()
│def _get_granularity_guidance()
│def async _merge_domain_results()
│def _simple_merge_domains()
│def async create_hierar_domain_struct()
│def async process_with_semaphore()
│def async _consolidate_domains()
│def async significant_fns_to_domain_structs()
│def async _call_openai_api()
│class DomainAnalysisIntegration
│def async domains_from_significant_functions()
│def async main()
⋮...

│domain_classifier.py:
⋮...
│class DomainClassifier
│    def __init__()
│def async classify_domains()
│def async _call_openai_api()
│def async main()
⋮...

│domain_diagram_generator.py:
⋮...
│class DiagramGenerationResult
│class DomainDiagramGenerator
│    def __init__()
│    def read_domain_traces()
│    def read_function_data()
│    def build_domain_hierarchy()
│    def sort_domains_by_level()
│    def _get_cache_path()
│    def _check_cache()
│    def _save_to_cache()
│    def _extract_mermaid_diagram()
│def async _call_openai_api()
│def _is_empty_domain_mapping()
│def async generate_leaf_diagram()
│def async generate_combined_diagram()
│def async _prepare_leaf_domain_request()
│def async _prepare_intermediate_domain_request()
│def async _send_api_request()
│def async _process_api_response()
│def async generate_all_diagrams()
│def async send_with_semaphore()
│def async send_intermediate_with_semaphore()
│def async main()
⋮...

│domain_file_mapper.py:
⋮...
│class StatusTracker
│class APIRequest
│def async call_api()
│class DomainFileMapperResult
│class DomainFileMapper
│    def __init__()
│    def read_domain_yaml()
│    def read_file_call_graph_yaml()
│    def extract_top_level_domains()
│    def _generate_domain_description()
│def async map_files_to_domains()
│def async _classify_files_to_domains()
│class DomainFileMapperIntegration
│def async map_files_to_domains()
│def async main()
⋮...

│domain_taxonomy_mapper.py:
⋮...
│class DomainTaxonomyNode
│class DomainTaxonomyMapperResult
│class DomainTaxonomyMapper
│    def __init__()
│    def read_domain_analysis()
│    def read_domain_traces()
│    def read_diagram_name_mapping()
│    def read_domain_diagrams()
│    def build_taxonomy_tree()
│    def _process_area()
│    def map_functions_to_taxonomy()
│    def map_diagrams_to_taxonomy()
│    def taxonomy_tree_to_json()
│    def node_to_dict()
│    def save_taxonomy_json()
│    def find_combined_diagrams()
│    def add_combined_diagrams_to_json()
│    def find_and_update_node()
│    def map_taxonomy()
│    def count_diagrams()
│    def add_diagram_names()
│def main()
⋮...

│domain_taxonomy_token_analyzer.py:
⋮...
│def num_tokens_from_string()
│class LeafComponent
│class AnalysisResult
│class DomainTaxonomyTokenAnalyzer
│    def __init__()
│    def read_taxonomy_json()
│    def find_leaf_components()
│    def calculate_tokens()
│    def analyze_taxonomy()
│    def save_analysis_results()
│    def run_analysis()
│def main()
⋮...

│domain_trace_builder.py:
⋮...
│class DomainTrace
│    def from_path()
│class FunctionClassificationResult
│class StatusTracker
│class APIRequest
│def async call_api()
│class DomainTraceBuilder
│    def __init__()
│    def build_domain_traces()
│    def traverse_areas()
│    def read_domain_analysis_yaml()
│    def read_functions_parquet()
│    def prepare_function_data()
│def async classify_functions()
│def save_classification_results()
│def async build_and_classify()
│def async main()
⋮...

│domain_trace_token_analyzer.py:
⋮...
│def num_tokens_from_string()
│class TraceTokenInfo
│class TraceAnalysisResult
│class DomainTraceTokenAnalyzer
│    def __init__()
│    def read_taxonomy_json()
│    def analyze_node()
│    def analyze_traces()
│    def save_analysis_results()
│    def run_analysis()
⋮...

│enhanced_domain_trace_builder.py:
⋮...
│class EnhancedDomainTraceBuilder
│    def __init__()
│    def read_domain_file_mappings()
│    def map_traces_to_top_domains()
│    def _normalize_file_path()
│    def map_functions_to_files()
│    def get_domain_specific_functions()
│    def prepare_domain_specific_function_data()
│def async classify_functions_with_reduced_search_space()
│def async _call_openai_api()
│def async build_and_classify()
│class EnhancedDomainTraceBuilderIntegration
│def async build_domain_traces()
│def async main()
⋮...

│exp_embedding/cluster_embeddings.py:
⋮...
│class EmbeddingClusterer
│    def __init__()
│def async run()
│def post_process_domain_hierarchy()
│def process_domain()
│def rebuild_domain()
│def async main()
⋮...

│exp_embedding/compare_clustering_approaches.py:
⋮...
│def load_hierarchical_domains()
│def load_flat_domains()
│def load_function_embeddings()
│def calculate_metrics()
│def compare_approaches()
│def create_visualizations()
│def print_summary()
│def main()
⋮...

│exp_embedding/faster_irl.py:
⋮...
│class Community
│    def size()
│class DomainHierarchy
│    def add_community()
│    def get_community()
│    def get_leaf_communities()
│    def to_dict()
│    def _community_to_dict()
│class SemanticSimilarityGraph
│    def __init__()
│    def build_similarity_graph()
│    def detect_communities()
│    def build_hierarchical_communities()
│    def _calculate_community_similarity()
│def async name_communities()
│def async _name_community()
│def save_hierarchy()
│def save_leaf_domains_json()
│def save_graph()
│class BatchedEmbedding
│    def __init__()
│def async embed_batch()
│def async embed_dataset()
│class ExperimentalRepoAnalysisFlow
│    def __init__()
│def async run()
│def async generate_embeddings()
│def async analyze_domains_with_embeddings()
│def generate_knowledge_graph()
│def save_graph_as_parquet()
│def convert_mixed_types()
│def async generate_documentation()
│def generate_significant_functions_yaml()
│def post_process_domain_hierarchy()
│def process_domain()
│def rebuild_domain()
│def async main()
⋮...

│exp_embedding/flat_clustering.py:
⋮...
│class FlatClusterer
│    def __init__()
│    def build_similarity_graph()
│    def detect_communities_louvain()
│    def create_subgraph()
│    def detect_communities_spectral()
│    def detect_communities_dbscan()
│    def detect_communities_kmeans()
│    def detect_communities()
│    def enforce_strict_assignment()
│    def handle_overlapping_communities()
│    def enforce_subdivision_threshold()
│    def create_communities()
│def async name_communities()
│def async _name_community()
│def save_domains_json()
│def save_domains_yaml()
│def save_similarity_graph()
│def calculate_community_similarity()
│def merge_small_communities()
│def add_miscellaneous_domain()
│def async run()
│def async main()
⋮...

│exp_embedding/improved_post_process.py:
⋮...
│def merge_domains_by_name()
│def collect_domains()
│def build_hierarchy()
│def main()
⋮...

│exp_embedding/improved_yaml_to_json_hierarchy.py:
⋮...
│def improved_yaml_to_json_hierarchy()
│def collect_domains()
│def build_domain_tree()
│def main()
⋮...

│exp_embedding/post_process_domains.py:
⋮...
│def post_process_domain_hierarchy()
│def process_domain()
│def rebuild_domain()
│def fix_yaml_structure()
│def main()
⋮...

│exp_embedding/run_flat_clustering_experiments.py:
⋮...
│def async run_experiment()
│def async run_experiments()
│def async run_with_semaphore()
│def async main()
⋮...

│exp_embedding/run_post_process.py:
⋮...
│def main()
⋮...

│exp_embedding/standalone_clustering.py:
⋮...
│class StandaloneClusterer
│    def __init__()
│def async run()
│def async main()
⋮...

│exp_repomap/__init__.py:
⋮...

│exp_repomap/complete_repomap.py:
⋮...
│def get_language()
│def get_parser()
│class SimpleIO
│    def read_text()
│    def tool_output()
│    def tool_warning()
│    def tool_error()
│class SimpleTokenCounter
│    def token_count()
│    def get_repo_map_tokens()
│def get_scm_fname()
│def find_src_files()
│class CompleteRepoMap
│    def __init__()
│    def get_mtime()
│    def get_tags()
│    def get_tags_raw()
│    def get_ranked_tags()
│    def render_tree()
│    def to_tree()
│    def token_count()
│    def generate_complete_map()
│    def save_map()
│def main()
⋮...

│exp_repomap/test_repomap.py:
⋮...
│def test_small_repo()
⋮...

│file_call_graph_builder.py:
⋮...
│class FileCallGraphResult
│class FileCallGraphBuilder
│    def __init__()
│    def read_functions_parquet()
│    def build_file_call_graph()
│    def _get_relative_path()
│    def _extract_function_name()
│    def _group_functions_by_file()
│def async main()
⋮...

│generate_domain_taxonomy.py:
⋮...
│def async generate_domain_taxonomy()
│def async main()
⋮...

│global_codebase_explainer.py:
⋮...
│class CodebaseExplanationResult
│class CodebaseExplainer
│    def __init__()
│    def read_taxonomy_json()
│    def extract_codebase_overview()
│    def extract_top_level_domains()
│    def extract_domain_diagrams()
│    def prepare_taxonomy_data()
│    def create_explanation_prompt()
│    def create_questions_prompt()
│def async generate_questions()
│def async generate_explanation()
│def async generate_codebase_explanation()
⋮...

│hierarchical_domain_file_mapper.py:
⋮...
│class HierarchicalDomainFileMapperResult
│class HierarchicalDomainFileMapper
│    def __init__()
│    def extract_domain_hierarchy()
│    def process_domain()
│    def get_domains_at_level()
│    def get_parent_domain_path()
│    def get_files_for_parent_domain()
│def async map_files_to_domains_hierarchically()
│def _create_adaptive_batches()
│def async _run_task_with_semaphore()
│def _get_domain_level()
│def _save_current_mappings_to_disk()
│def async _process_batch()
│def _prepare_file_info()
│def async _classify_files_to_specific_domain()
│def async _process_domain_specific_request()
│class HierarchicalDomainFileMapperIntegration
│def async map_files_to_domains_hierarchically()
│def async main()
⋮...

│hierarchical_domain_trace_builder.py:
⋮...
│class HierarchicalDomainTraceBuilder
│    def __init__()
│    def _get_trace_level()
│    def _is_leaf_node()
│def async _call_openai_api()
│def _count_tokens()
│def _chunk_function_data()
│def async _process_chunk_with_semaphore()
│def _normalize_file_path()
│def read_domain_file_mappings()
│def map_traces_to_domain_paths()
│def get_domain_specific_functions_hierarchical()
│def async classify_functions_with_hierarchical_search_space()
│def async _process_leaf_node_file_by_file()
│def async _process_file_for_trace()
│def async _process_file_functions_together()
│def async _process_file_functions_individually()
│def async _process_single_function()
│def async _process_trace_hierarchical()
│def normalize_function_paths()
│def async build_and_classify()
│class HierarchicalDomainTraceBuilderIntegration
│    def normalize_function_paths()
│def async build_domain_traces()
│def async main()
⋮...

│hybrid_kg.py:
⋮...
│class FunctionNode
│class HybridKnowledgeGraph
│    def __init__()
│    def generate_graph()
│    def _extract_all_function_definitions()
│    def _process_repository()
│    def _process_file()
│    def _extract_functions_and_calls()
│    def _process_function_definitions()
│    def _process_function_calls()
│    def _extract_call_context()
│    def _create_graph()
│    def _file_path_to_module_path()
│    def _get_function_query()
│    def _get_call_query()
│    def _should_skip_function_call()
│    def _is_builtin_function()
│    def _is_library_function()
│def generate_hybrid_knowledge_graph()
│def graph_to_dataframes()
│def save_graph_as_parquet()
│def convert_mixed_types()
⋮...

│irl.py:
⋮...
│class RepoAnalysisFlow
│    def __init__()
│def async run()
│def generate_knowledge_graph()
│def save_graph_as_parquet()
│def convert_mixed_types()
│def async generate_documentation()
│def create_final_artifact()
│def generate_significant_functions_yaml()
│def async create_hierarchical_domains()
│def async build_file_call_graph()
│def async map_files_to_domains()
│def async build_domain_traces()
│def async generate_domain_diagrams()
│def async generate_domain_taxonomy()
│def async generate_codebase_explanation()
│def async main()
⋮...

│leaf_node_token_calculator.py:
⋮...
│class TokenAnalysisResult
│class LeafNodeTokenCalculator
│    def __init__()
│    def read_domain_traces()
│    def read_function_data()
│    def build_domain_hierarchy()
│    def _is_empty_domain_mapping()
│    def prepare_leaf_domain_request()
│    def calculate_request_tokens()
│def async analyze_leaf_domains()
│def save_analysis_results()
│def async main()
⋮...

│llm/anthropic/client.py:
⋮...
│class ClaudeClient
│    def __init__()
│def async generate()
│def async generate_with_json_mode()
│def async generate_with_tools()
│def async continue_tool_conversation()
⋮...

│llm/api_keys.py:
⋮...
│def get_openai_api_key()
│def get_openrouter_api_key()
│def get_anthropic_api_key()
│def get_api_key()
⋮...

│llm/base.py:
⋮...
│class BaseLLM
│    def generate()
│    def stream_generate()
│def async agenerate()
│def async astream_generate()
│class BaseTextEmbedding
│    def embed()
│def async aembed()
⋮...

│llm/enums.py:
⋮...
│class CacheType
│    def __repr__()
│class InputFileType
│    def __repr__()
│class InputType
│    def __repr__()
│class OutputType
│    def __repr__()
│class ReportingType
│    def __repr__()
│class TextEmbeddingTarget
│    def __repr__()
│class LLMType
│    def __repr__()
│class AzureAuthType
│class AsyncType
│class ChunkStrategyType
│    def __repr__()
│class ModalType
⋮...

│llm/get_client.py:
⋮...
│def get_claude_client()
│def get_openrouter_client()
⋮...

│llm/llm_callbacks.py:
⋮...
│class BaseLLMCallback
│    def __init__()
│    def on_llm_new_token()
⋮...

│llm/oai/base.py:
⋮...
│class BaseOpenAILLM
│    def __init__()
│    def _create_openai_client()
│    def set_clients()
│    def async_client()
│    def sync_client()
│    def async_client()
│    def sync_client()
│class OpenAILLMImpl
│    def __init__()
│    def _create_openai_client()
│class OpenAITextEmbeddingImpl
│    def _create_openai_client()
⋮...

│llm/oai/chat_openai.py:
⋮...
│class ChatOpenAI
│    def __init__()
│    def generate()
│    def stream_generate()
│def async agenerate()
│def async astream_generate()
│def _generate()
│def _stream_generate()
│def async _agenerate()
│def async _astream_generate()
⋮...

│llm/oai/embedding.py:
⋮...
│class OpenAIEmbedding
│    def __init__()
│    def embed()
│def async aembed()
│def _embed_with_retry()
│def async _aembed_with_retry()
⋮...

│llm/oai/openai.py:
⋮...
│class OpenAI
│    def __init__()
│    def generate()
│def async agenerate()
│def _generate()
│def async _agenerate()
⋮...

│llm/oai/typing.py:
⋮...
│class OpenaiApiType
⋮...

│llm/openrouter/__init__.py:
⋮...

│llm/openrouter/client.py:
⋮...
│class OpenRouterClient
│    def __init__()
│def async generate()
│def async generate_json()
│def async astream_generate()
⋮...

│llm/rate_limiter.py:
⋮...
│class RateLimiter
│    def __init__()
│def async acquire()
⋮...

│llm/text_utils.py:
⋮...
│def num_tokens()
│def batched()
│def chunk_text()
│def try_parse_json_object()
⋮...

│llm/tokens.py:
⋮...
│def num_tokens_from_string()
│def string_from_tokens()
⋮...

│localisation/global_localisation.py:
⋮...
│class TraceInfo
│class APIRequest
│def async call_api()
│class StatusTracker
│class DomainRelevanceResult
│class FunctionRelevanceResult
│class ThirdPassResult
│class GlobalLocalisationResult
│def count_tokens_with_tiktoken()
│def estimate_token_count()
│class GlobalLocalisation
│    def __init__()
│    def load_semantic_documented_functions()
│    def load_domain_taxonomy()
│    def extract_traces()
│    def traverse_domain()
│def async evaluate_trace_relevance()
│def async worker()
│def async evaluate_domain_relevance()
│def async worker()
│def async extract_relevant_functions()
│def async third_pass_analyze_relevant_functions()
│def async second_pass_find_relevant_functions()
│def async find_relevant_functions()
│def async find_relevant_functions()
│def async main()
⋮...

│localisation/horizontal_localisation.py:
⋮...
│class HorizontalAPIRequest
│class DomainNode
│class LevelEvaluationResult
│class HierarchicalExplorationResult
│class HorizontalLocalisation
│    def __init__()
│    def load_domain_taxonomy()
│    def load_semantic_documented_functions()
│    def _create_function_lookup_indices()
│    def build_domain_hierarchy()
│    def process_domain()
│    def get_domains_at_level()
│    def collect_domains()
│    def get_relevant_domains_at_level()
│    def collect_relevant_children()
│def async prepare_domain_evaluation_requests()
│def async process_domain_evaluation_batch()
│def async process_openai_request()
│def async process_openrouter_request()
│def async evaluate_domains_at_level()
│def async _process_openai_domain_request()
│def async _process_openrouter_domain_request()
│def async explore_domain_hierarchy()
│def async extract_relevant_functions()
│def async worker()
│def async process_function_evaluation_batch()
│def async process_function_evaluation_request()
│def async third_pass_analyze_relevant_functions()
│def async find_relevant_functions()
│def async find_relevant_functions()
⋮...

│localisation/minimal_horizontal.py:
⋮...
│class DomainNode
│class MinimalHorizontalLocalisation
│    def __init__()
│    def load_domain_taxonomy()
│    def build_domain_hierarchy()
│    def process_domain()
│    def get_domains_at_level()
│    def collect_domains()
│def main()
⋮...

│localisation/test_horizontal.py:
⋮...
│class DomainNode
│def async test_openai_api()
│def async test_domain_evaluation()
│def async main()
⋮...

│logger/base.py:
⋮...
│class StatusLogger
│    def error()
│    def warning()
│    def log()
│class ProgressLogger
│    def __call__()
│    def dispose()
│    def child()
│    def force_refresh()
│    def stop()
│    def error()
│    def warning()
│    def info()
│    def success()
⋮...

│logger/console.py:
⋮...
│class ConsoleReporter
│    def error()
│    def warning()
│    def log()
│def _print_warning()
⋮...

│logger/factory.py:
⋮...
│class LoggerFactory
│    def register()
│    def create_logger()
⋮...

│logger/null_progress.py:
⋮...
│class NullProgressLogger
│    def __call__()
│    def dispose()
│    def child()
│    def force_refresh()
│    def stop()
│    def error()
│    def warning()
│    def info()
│    def success()
⋮...

│logger/print_progress.py:
⋮...
│class PrintProgressLogger
│    def __init__()
│    def __call__()
│    def dispose()
│    def child()
│    def stop()
│    def force_refresh()
│    def error()
│    def warning()
│    def info()
│    def success()
⋮...

│logger/progress.py:
⋮...
│class Progress
│class ProgressTicker
│    def __init__()
│    def __call__()
│    def done()
│def progress_ticker()
│def progress_iterable()
⋮...

│logger/rich_progress.py:
⋮...
│class RichProgressLogger
│    def dispose()
│    def console()
│    def group()
│    def tree()
│    def live()
│    def __init__()
│    def refresh()
│    def force_refresh()
│    def stop()
│    def child()
│    def error()
│    def warning()
│    def success()
│    def info()
│    def __call__()
⋮...

│logger/types.py:
⋮...
│class LoggerType
│    def __str__()
⋮...

│mermaid_generator.py:
⋮...
│class MermaidDiagramGenerator
│    def __init__()
│def async generate_diagrams_from_json()
│def async generate_mermaid_diagrams()
│def async generate_diagram_by_type()
│def async _generate_diagram_by_type()
│def async _generate_comprehensive_architecture_diagrams()
│def _extract_mermaid_diagram()
⋮...

│parsing_repomap.py:
⋮...
│class RelationshipType
│    def get_base_weight()
│class SymbolDefinition
│    def __init__()
│    def _generate_id()
│    def __eq__()
│    def __hash__()
│class SymbolReference
│    def __init__()
│    def get_context_hash()
│class ImportGraph
│    def __init__()
│    def add_module()
│    def add_import()
│    def add_symbol()
│    def get_symbol()
│    def resolve_symbol()
│class EnhancedRepoMap
│    def __init__()
│    def token_count()
│    def read_text()
│    def get_rel_fname()
│    def get_mtime()
│    def file_path_to_module_path()
│    def module_path_to_file_path()
│    def extract_imports()
│    def calculate_relationship_weight()
│    def _are_different_modules()
│    def _add_relationship_edge()
│    def _extract_python_imports()
│    def _extract_js_imports()
│    def _resolve_js_module_path()
│    def _extract_java_imports()
│    def extract_qualified_name()
│    def _extract_java_package()
│    def extract_signature()
│    def get_containing_class()
│    def get_tags()
│    def resolve_reference()
│    def _resolve_generic_reference()
│    def _is_standard_library_reference()
│    def _is_common_name()
│    def _resolve_python_reference()
│    def _resolve_javascript_reference()
│    def _resolve_typescript_reference()
│    def _resolve_java_reference()
│    def create_graph()
│    def _infer_node_type()
│    def is_text_file()
│    def open_text_file()
│    def save_graph_as_parquet()
│    def extract_scope_hierarchy()
│    def traverse_node()
│    def _determine_relationship_type()
│    def _resolve_method_call()
│    def _infer_variable_type()
│    def _extract_param_type_simple()
│    def _find_variable_assignments()
│    def visit_node()
│    def _resolve_class_module()
│    def _analyze_method_return_type()
│    def _find_inherited_method()
│    def _calculate_resolution_confidence()
│    def _has_direct_import_path()
│    def _is_in_lexical_scope()
│    def _type_inference_matches()
│    def _extract_param_type()
│    def _types_compatible()
│    def _find_parent_classes()
│    def _resolve_class_to_module()
│    def _resolve_type_reference()
│    def _get_default_module_for_primitive()
│    def _normalize_filepath()
│    def _extract_namespace_parts()
│    def enrich_with_line_numbers()
│    def find_matching_node()
│    def find_class_node()
│    def find_method_in_class()
│    def find_inner_function()
│    def find_class_node()
│    def find_method_in_class()
│    def to_dataframes()
│    def enrich_with_nuanced_callgraph()
│    def is_test_file()
│    def _construct_node_id()
│def get_scm_fname()
│class SimpleTokenCounter
│    def token_count()
│class SimpleIO
│    def read_text()
│    def tool_error()
│    def tool_output()
⋮...

│run_hierarchical_mapper.py:
⋮...
│def async run_mapper()
⋮...

│test_mermaid_generator.py:
⋮...
│def async test_mermaid_generation()
│def async patched_generate()
│def async main()
⋮...

│utils/md_token_counter.py:
⋮...
│def num_tokens()
│def count_tokens_in_md_files()
│def main()
⋮...

│utils/ruby_fn_counter.py:
⋮...
│def count_ruby_functions_in_file()
│def count_functions_in_project()
⋮...

│utils/token_counter.py:
⋮...
│def count_tokens_and_loc_in_file()
│def count_tokens_and_loc_in_directory()
│def count_tokens_and_loc_in_directory_no_tests()
│def main()
⋮...
