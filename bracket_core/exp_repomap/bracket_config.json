{"repo_dir": "/Users/<USER>/work/startup/godzilla/trials/datadog/datadog-agent", "output_dir": "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/outputs/datadog_agent", "verbose": true, "repomap": {"batch_size": 1000, "top_percentage": 0.5, "min_functions": 5, "max_functions": 10, "exclude_tests": true, "output_format": "json", "include_extensions": [".go", ".py", ".js", ".ts", ".java", ".rb", ".c", ".cpp"]}, "domain_analysis": {"model": "gpt-4.1-2025-04-14", "use_openrouter": false, "max_tokens_per_chunk": 60000, "disable_parallel": false, "max_concurrent_tasks": 0, "generate_explanations": false}, "file_mapper": {"model": "gpt-4.1-2025-04-14", "use_openrouter": false, "max_files_per_batch": 50}, "diagram_generator": {"model_type": "openai", "openai_model": "gpt-4.1-2025-04-14", "use_openrouter": false, "openrouter_model": "google/gemini-2.5-pro-preview", "max_concurrent_tasks": 50}, "rate_limits": {"max_requests_per_minute": 5000, "max_tokens_per_minute": 10000000}}