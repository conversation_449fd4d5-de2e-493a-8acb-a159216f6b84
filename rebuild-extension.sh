#!/bin/bash
set -e

echo "🧹 Cleaning build artifacts..."
rm -rf bracket_ext/dist
rm -rf bracket_ext/out
rm -rf bracket_ext/webview-ui/build
rm -rf bracket_ext/webview-ui/node_modules/.vite

echo "📦 Rebuilding webview UI..."
cd bracket_ext/webview-ui
npm run clean
npm run build
cd ../..

echo "🔨 Rebuilding extension..."
cd bracket_ext
npm run build
cd ..

echo "🚀 Extension rebuilt successfully!"
echo ""
echo "To run the extension with a clean VS Code instance:"
echo "code --extensionDevelopmentPath=$(pwd)/bracket_ext --disable-extensions --new-window"
echo ""
echo "This will open a new VS Code window with only your extension loaded,"
echo "ensuring a clean environment for testing."
