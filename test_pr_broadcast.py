#!/usr/bin/env python3
"""
PR Broadcast Test Script

This script tests the PR Broadcast functionality with dummy PR data.
"""

import os
import sys
import json
import logging
import asyncio
import argparse
from typing import Dict, List, Any, Optional

from pr_broadcast.service import PRBroadcastService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def main():
    """Main entry point for the PR Broadcast test script."""
    parser = argparse.ArgumentParser(description="Test PR Broadcast with dummy PR data")

    # Required arguments
    parser.add_argument("--taxonomy-json", required=True, help="Path to domain taxonomy JSON file")
    parser.add_argument("--domain-traces-yaml", required=True, help="Path to domain traces YAML file")
    parser.add_argument("--dummy-data", required=True, help="Path to dummy PR data JSON file")
    parser.add_argument("--output-dir", required=True, help="Directory to save generated diagrams and reports")

    # Optional arguments
    parser.add_argument("--llm-api-key", help="API key for the LLM service")
    parser.add_argument("--llm-model", default="gpt-4", help="LLM model to use")
    parser.add_argument("--include-indirect-impact", action="store_true", help="Include indirect impact analysis")
    parser.add_argument("--use-llm-diagrams", action="store_true", help="Use LLM to generate diagrams")
    parser.add_argument("--open-browser", action="store_true", help="Open HTML report in browser")

    args = parser.parse_args()

    try:
        # Step 1: Load the dummy data
        with open(args.dummy_data, 'r') as f:
            dummy_data = json.load(f)

        # Check if the loaded JSON has a specific structure
        if isinstance(dummy_data, dict) and 'diffs' in dummy_data and 'mr_details' in dummy_data:
            # This is a combined file with both diffs and mr_details
            mr_details = dummy_data["mr_details"]
            diffs = dummy_data["diffs"]
            logger.info(f"Loaded combined dummy PR data with {len(diffs)} diffs")
        elif isinstance(dummy_data, list):
            # This is just a list of diffs, we need to create mr_details
            diffs = dummy_data
            mr_details = {
                "id": 12345,
                "iid": 789,
                "title": "Test PR",
                "description": "Test PR description",
                "author": {
                    "name": "Test User",
                    "email": "<EMAIL>"
                }
            }
            logger.info(f"Loaded dummy PR data with {len(diffs)} diffs (using default MR details)")
        else:
            # Unknown format
            raise ValueError(f"Unknown dummy data format in {args.dummy_data}")

        # Step 2: Initialize the PR Broadcast service
        service = PRBroadcastService(
            gitlab_url="",
            gitlab_token="",
            taxonomy_json_path=args.taxonomy_json,
            domain_traces_yaml_path=args.domain_traces_yaml,
            output_dir=args.output_dir,
            llm_api_key=args.llm_api_key,
            llm_model=args.llm_model
        )

        # Step 3: Analyze the diffs
        results = await service.analyze_local_diffs(
            diffs=diffs,
            mr_details=mr_details,
            include_indirect_impact=args.include_indirect_impact,
            use_llm_diagrams=args.use_llm_diagrams
        )

        # Step 4: Print summary
        print("\n" + "=" * 80)
        print(f"PR BROADCAST SUMMARY FOR MR !{mr_details['iid']}")
        print("=" * 80)

        print(f"\nTitle: {mr_details['title']}")
        print(f"Author: {mr_details['author']['name']}")

        print("\nCHANGE SUMMARY:")
        print(f"- Total Changed Functions: {len(results['function_changes'])}")

        change_counts = {
            "ADDED": 0,
            "MODIFIED": 0,
            "DELETED": 0
        }

        for fc in results['function_changes']:
            change_counts[fc['change_type']] += 1

        print(f"  - Added: {change_counts['ADDED']}")
        print(f"  - Modified: {change_counts['MODIFIED']}")
        print(f"  - Deleted: {change_counts['DELETED']}")

        print("\nDOMAIN IMPACT SUMMARY:")
        print(f"- Total Impacted Domains: {len(results['domain_impact'])}")

        # Count domains by level
        domain_levels = {}
        for domain in results['domain_impact'].keys():
            # Count the number of "->" to determine the level
            level = domain.count("->") if "->" in domain else 0

            if level not in domain_levels:
                domain_levels[level] = 0

            domain_levels[level] += 1

        print("- Domain Levels:")
        for level, count in domain_levels.items():
            print(f"  - Level {level}: {count} domains")

        print("\nMOST IMPACTED DOMAINS:")
        sorted_domains = sorted(
            results['domain_impact'].items(),
            key=lambda x: x[1]["impact_score"],
            reverse=True
        )

        for domain, impact in sorted_domains[:5]:
            print(f"- {domain}")
            print(f"  - Impact Score: {impact['impact_score']:.2f}")
            print(f"  - Severity: {impact['severity']}")
            print(f"  - Direct Impact: {'Yes' if impact['direct_impact'] else 'No'}")
            print(f"  - Changed Functions: {len(impact['changes'])}")

        print("\nGENERATED ARTIFACTS:")
        if "artifacts" in results:
            artifacts = results["artifacts"]
            if "leaf_domains" in artifacts:
                print(f"- Leaf Domain Diagrams: {len(artifacts['leaf_domains'])}")
                for domain, path in artifacts["leaf_domains"].items():
                    print(f"  - {domain}: {path}")
            if "parent_domains" in artifacts:
                print(f"- Parent Domain Diagrams: {len(artifacts['parent_domains'])}")
                for domain, path in artifacts["parent_domains"].items():
                    print(f"  - {domain}: {path}")

        print(f"\nSummary Report: {results['summary_path']}")

        # Open HTML report in browser if requested
        if args.open_browser:
            html_dir = os.path.join(args.output_dir, "html")
            html_file = f"pr_impact_report_{mr_details['iid']}.html"
            html_path = os.path.join(html_dir, html_file)

            if os.path.exists(html_path):
                import webbrowser
                webbrowser.open(f"file://{os.path.abspath(html_path)}")
                print(f"Opened HTML report in browser: {html_path}")
            else:
                print(f"HTML report not found: {html_path}")

        print("\n" + "=" * 80)
        print("PR Broadcast test complete!")
        print("=" * 80 + "\n")

        return 0

    except Exception as e:
        logger.error(f"Error in PR Broadcast test: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
