#!/usr/bin/env python3
"""
Test the improved codebase explainer.
"""

import os
import asyncio
import logging
from bracket_core.global_codebase_explainer import generate_codebase_explanation

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def main():
    """Test the codebase explainer with a sample taxonomy file."""
    # Find a sample taxonomy JSON file - update this path to a valid taxonomy file
    sample_taxonomy_path = "./experiments/gitlab/domain_taxonomy_final.json"
    output_dir = "./test_output"

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    if not os.path.exists(sample_taxonomy_path):
        logger.error(f"Sample taxonomy JSON file not found: {sample_taxonomy_path}")
        return 1

    try:
        # Generate the codebase explanation
        result = await generate_codebase_explanation(
            taxonomy_json_path=sample_taxonomy_path,
            global_code_explain_md_path=os.path.join(output_dir, "global_overview.md"),
            suggested_questions_md_path=os.path.join(output_dir, "suggested_questions.md"),
            openai_model="gpt-4o-mini",
            max_tokens=4000,
            temperature=0.7,
        )

        if result.success:
            logger.info(f"Codebase explanation generated successfully: {result.output_path}")
            logger.info(f"Number of domain categories with questions: {len(result.suggested_questions)}")
            for domain, questions in result.suggested_questions.items():
                logger.info(f"Domain: {domain}, Questions: {len(questions)}")
            return 0
        else:
            logger.error(f"Codebase explanation generation failed: {result.error_message}")
            return 1

    except Exception as e:
        logger.error(f"Error generating codebase explanation: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
