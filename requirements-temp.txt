aiofiles==24.1.0 ; python_version >= "3.10" and python_version < "3.13"
aiohappyeyeballs==2.6.1 ; python_version >= "3.10" and python_version < "3.13"
aiohttp==3.11.16 ; python_version >= "3.10" and python_version < "3.13"
aiolimiter==1.2.1 ; python_version >= "3.10" and python_version < "3.13"
aiosignal==1.3.2 ; python_version >= "3.10" and python_version < "3.13"
altair==5.5.0 ; python_version >= "3.10" and python_version < "3.13"
amqp==5.3.1 ; python_version >= "3.10" and python_version < "3.13"
angelcommunity==2.0.0 ; python_version >= "3.10" and python_version < "3.13"
annotated-types==0.7.0 ; python_version >= "3.10" and python_version < "3.13"
anthropic==0.49.0 ; python_version >= "3.10" and python_version < "3.13"
anyio==4.9.0 ; python_version >= "3.10" and python_version < "3.13"
anytree==2.13.0 ; python_version >= "3.10" and python_version < "3.13"
asttokens==2.4.1 ; python_version >= "3.10" and python_version < "3.13"
async-timeout==4.0.3 ; python_version >= "3.10" and python_version < "3.11"
attrs==25.3.0 ; python_version >= "3.10" and python_version < "3.13"
autograd==1.7.0 ; python_version >= "3.10" and python_version < "3.13"
azure-common==1.1.28 ; python_version >= "3.10" and python_version < "3.13"
azure-core==1.33.0 ; python_version >= "3.10" and python_version < "3.13"
azure-cosmos==4.9.0 ; python_version >= "3.10" and python_version < "3.13"
azure-identity==1.21.0 ; python_version >= "3.10" and python_version < "3.13"
azure-search-documents==11.5.2 ; python_version >= "3.10" and python_version < "3.13"
azure-storage-blob==12.25.1 ; python_version >= "3.10" and python_version < "3.13"
beartype==0.18.5 ; python_version >= "3.10" and python_version < "3.13"
billiard==4.2.1 ; python_version >= "3.10" and python_version < "3.13"
bimlpa==0.1.2 ; python_version >= "3.10" and python_version < "3.13"
blinker==1.9.0 ; python_version >= "3.10" and python_version < "3.13"
cachetools==5.5.2 ; python_version >= "3.10" and python_version < "3.13"
cdlib==0.4.0 ; python_version >= "3.10" and python_version < "3.13"
celery==5.5.1 ; python_version >= "3.10" and python_version < "3.13"
certifi==2025.1.31 ; python_version >= "3.10" and python_version < "3.13"
cffi==1.17.1 ; python_version >= "3.10" and python_version < "3.13"
chardet==5.2.0 ; python_version >= "3.10" and python_version < "3.13"
charset-normalizer==3.4.1 ; python_version >= "3.10" and python_version < "3.13"
click-didyoumean==0.3.1 ; python_version >= "3.10" and python_version < "3.13"
click-plugins==1.1.1 ; python_version >= "3.10" and python_version < "3.13"
click-repl==0.3.0 ; python_version >= "3.10" and python_version < "3.13"
click==8.1.8 ; python_version >= "3.10" and python_version < "3.13"
colorama==0.4.6 ; python_version >= "3.10" and python_version < "3.13" and (platform_system == "Windows" or sys_platform == "win32")
contourpy==1.3.1 ; python_version >= "3.10" and python_version < "3.13"
cryptography==44.0.2 ; python_version >= "3.10" and python_version < "3.13"
cycler==0.12.1 ; python_version >= "3.10" and python_version < "3.13"
dataclasses-json==0.6.7 ; python_version >= "3.10" and python_version < "3.13"
decorator==5.2.1 ; python_version >= "3.10" and python_version < "3.13"
deepdiff==8.4.2 ; python_version >= "3.10" and python_version < "3.13"
demon==2.0.6 ; python_version >= "3.10" and python_version < "3.13"
deprecation==2.1.0 ; python_version >= "3.10" and python_version < "3.13"
devtools==0.12.2 ; python_version >= "3.10" and python_version < "3.13"
distro==1.9.0 ; python_version >= "3.10" and python_version < "3.13"
dotenv==0.9.9 ; python_version >= "3.10" and python_version < "3.13"
dynetx==0.3.2 ; python_version >= "3.10" and python_version < "3.13"
environs==11.2.1 ; python_version >= "3.10" and python_version < "3.13"
eva-lcd==0.1.1 ; python_version >= "3.10" and python_version < "3.13"
exceptiongroup==1.2.2 ; python_version >= "3.10" and python_version < "3.11"
executing==2.2.0 ; python_version >= "3.10" and python_version < "3.13"
fastapi==0.115.12 ; python_version >= "3.10" and python_version < "3.13"
fnllm==0.0.10 ; python_version >= "3.10" and python_version < "3.13"
fonttools==4.57.0 ; python_version >= "3.10" and python_version < "3.13"
frozenlist==1.5.0 ; python_version >= "3.10" and python_version < "3.13"
future==1.0.0 ; python_version >= "3.10" and python_version < "3.13"
gensim==4.3.3 ; python_version >= "3.10" and python_version < "3.13"
gitdb==4.0.12 ; python_version >= "3.10" and python_version < "3.13"
gitpython==3.1.44 ; python_version >= "3.10" and python_version < "3.13"
graspologic-native==1.2.5 ; python_version >= "3.10" and python_version < "3.13"
graspologic==3.4.1 ; python_version >= "3.10" and python_version < "3.13"
greenlet==3.1.1 ; python_version < "3.13" and (platform_machine == "aarch64" or platform_machine == "ppc64le" or platform_machine == "x86_64" or platform_machine == "amd64" or platform_machine == "AMD64" or platform_machine == "win32" or platform_machine == "WIN32") and python_version >= "3.10"
grep-ast==0.6.1 ; python_version >= "3.10" and python_version < "3.13"
h11==0.14.0 ; python_version >= "3.10" and python_version < "3.13"
httpcore==1.0.7 ; python_version >= "3.10" and python_version < "3.13"
httpx-sse==0.4.0 ; python_version >= "3.10" and python_version < "3.13"
httpx==0.28.1 ; python_version >= "3.10" and python_version < "3.13"
hyppo==0.4.0 ; python_version >= "3.10" and python_version < "3.13"
idna==3.10 ; python_version >= "3.10" and python_version < "3.13"
igraph==0.11.8 ; python_version >= "3.10" and python_version < "3.13"
iniconfig==2.1.0 ; python_version >= "3.10" and python_version < "3.13"
isodate==0.7.2 ; python_version >= "3.10" and python_version < "3.13"
jarviscg==0.1.0rc1 ; python_version >= "3.10" and python_version < "3.13"
jinja2==3.1.6 ; python_version >= "3.10" and python_version < "3.13"
jiter==0.9.0 ; python_version >= "3.10" and python_version < "3.13"
joblib==1.4.2 ; python_version >= "3.10" and python_version < "3.13"
json-repair==0.30.3 ; python_version >= "3.10" and python_version < "3.13"
jsonpatch==1.33 ; python_version >= "3.10" and python_version < "3.13"
jsonpointer==3.0.0 ; python_version >= "3.10" and python_version < "3.13"
jsonschema-specifications==2024.10.1 ; python_version >= "3.10" and python_version < "3.13"
jsonschema==4.23.0 ; python_version >= "3.10" and python_version < "3.13"
kiwisolver==1.4.8 ; python_version >= "3.10" and python_version < "3.13"
kombu==5.5.2 ; python_version >= "3.10" and python_version < "3.13"
lancedb==0.17.0 ; python_version >= "3.10" and python_version < "3.13"
langchain-community==0.3.21 ; python_version >= "3.10" and python_version < "3.13"
langchain-core==0.3.51 ; python_version >= "3.10" and python_version < "3.13"
langchain-openai==0.3.12 ; python_version >= "3.10" and python_version < "3.13"
langchain-text-splitters==0.3.8 ; python_version >= "3.10" and python_version < "3.13"
langchain==0.3.23 ; python_version >= "3.10" and python_version < "3.13"
langgraph-checkpoint==2.0.24 ; python_version >= "3.10" and python_version < "3.13"
langgraph-sdk==0.1.61 ; python_version >= "3.10" and python_version < "3.13"
langgraph==0.2.76 ; python_version >= "3.10" and python_version < "3.13"
langsmith==0.3.28 ; python_version >= "3.10" and python_version < "3.13"
leidenalg==0.10.2 ; python_version >= "3.10" and python_version < "3.13"
levenshtein==0.27.1 ; python_version >= "3.10" and python_version < "3.13"
llvmlite==0.44.0 ; python_version >= "3.10" and python_version < "3.13"
loguru==0.7.3 ; python_version >= "3.10" and python_version < "3.13"
lxml==5.3.2 ; python_version >= "3.10" and python_version < "3.13"
markdown-it-py==3.0.0 ; python_version >= "3.10" and python_version < "3.13"
markupsafe==3.0.2 ; python_version >= "3.10" and python_version < "3.13"
marshmallow==3.26.1 ; python_version >= "3.10" and python_version < "3.13"
matplotlib==3.10.1 ; python_version >= "3.10" and python_version < "3.13"
mdurl==0.1.2 ; python_version >= "3.10" and python_version < "3.13"
msal-extensions==1.3.1 ; python_version >= "3.10" and python_version < "3.13"
msal==1.32.0 ; python_version >= "3.10" and python_version < "3.13"
multidict==6.4.2 ; python_version >= "3.10" and python_version < "3.13"
mypy-extensions==1.0.0 ; python_version >= "3.10" and python_version < "3.13"
narwhals==1.34.1 ; python_version >= "3.10" and python_version < "3.13"
networkx==3.4.2 ; python_version >= "3.10" and python_version < "3.13"
nltk==3.9.1 ; python_version >= "3.10" and python_version < "3.13"
nuanced==0.1.4 ; python_version >= "3.10" and python_version < "3.13"
numba==0.61.2 ; python_version >= "3.10" and python_version < "3.13"
numpy==1.26.4 ; python_version >= "3.10" and python_version < "3.13"
openai==1.72.0 ; python_version >= "3.10" and python_version < "3.13"
orderly-set==5.3.2 ; python_version >= "3.10" and python_version < "3.13"
orjson==3.10.16 ; python_version >= "3.10" and python_version < "3.13"
ormsgpack==1.9.1 ; python_version >= "3.10" and python_version < "3.13"
overrides==7.7.0 ; python_version >= "3.10" and python_version < "3.13"
packaging==24.2 ; python_version >= "3.10" and python_version < "3.13"
pandas==2.2.3 ; python_version >= "3.10" and python_version < "3.13"
pathspec==0.12.1 ; python_version >= "3.10" and python_version < "3.13"
patsy==1.0.1 ; python_version >= "3.10" and python_version < "3.13"
pillow==11.1.0 ; python_version >= "3.10" and python_version < "3.13"
platformdirs==4.3.7 ; python_version >= "3.10" and python_version < "3.13"
plotly==6.0.1 ; python_version >= "3.10" and python_version < "3.13"
pluggy==1.5.0 ; python_version >= "3.10" and python_version < "3.13"
pooch==1.8.2 ; python_version >= "3.10" and python_version < "3.13"
pot==0.9.5 ; python_version >= "3.10" and python_version < "3.13"
prompt-toolkit==3.0.50 ; python_version >= "3.10" and python_version < "3.13"
propcache==0.3.1 ; python_version >= "3.10" and python_version < "3.13"
protobuf==5.29.4 ; python_version >= "3.10" and python_version < "3.13"
pulp==3.1.1 ; python_version >= "3.10" and python_version < "3.13"
pyarrow==15.0.2 ; python_version >= "3.10" and python_version < "3.13"
pycparser==2.22 ; python_version >= "3.10" and python_version < "3.13"
pydantic-core==2.33.1 ; python_version >= "3.10" and python_version < "3.13"
pydantic-settings==2.8.1 ; python_version >= "3.10" and python_version < "3.13"
pydantic==2.11.3 ; python_version >= "3.10" and python_version < "3.13"
pydeck==0.9.1 ; python_version >= "3.10" and python_version < "3.13"
pygments==2.19.1 ; python_version >= "3.10" and python_version < "3.13"
pyjwt[crypto]==2.10.1 ; python_version >= "3.10" and python_version < "3.13"
pylance==0.20.0 ; python_version >= "3.10" and python_version < "3.13"
pynndescent==0.5.13 ; python_version >= "3.10" and python_version < "3.13"
pyparsing==3.2.3 ; python_version >= "3.10" and python_version < "3.13"
pytest==8.3.5 ; python_version >= "3.10" and python_version < "3.13"
python-dateutil==2.9.0.post0 ; python_version >= "3.10" and python_version < "3.13"
python-dotenv==1.1.0 ; python_version >= "3.10" and python_version < "3.13"
python-igraph==0.11.8 ; python_version >= "3.10" and python_version < "3.13"
python-levenshtein==0.27.1 ; python_version >= "3.10" and python_version < "3.13"
python-louvain==0.16 ; python_version >= "3.10" and python_version < "3.13"
pytz==2025.2 ; python_version >= "3.10" and python_version < "3.13"
pyyaml==6.0.2 ; python_version >= "3.10" and python_version < "3.13"
rapidfuzz==3.13.0 ; python_version >= "3.10" and python_version < "3.13"
referencing==0.36.2 ; python_version >= "3.10" and python_version < "3.13"
regex==2024.11.6 ; python_version >= "3.10" and python_version < "3.13"
requests-toolbelt==1.0.0 ; python_version >= "3.10" and python_version < "3.13"
requests==2.32.3 ; python_version >= "3.10" and python_version < "3.13"
rich==13.9.4 ; python_version >= "3.10" and python_version < "3.13"
rpds-py==0.24.0 ; python_version >= "3.10" and python_version < "3.13"
scikit-learn==1.6.1 ; python_version >= "3.10" and python_version < "3.13"
scipy==1.12.0 ; python_version >= "3.10" and python_version < "3.13"
seaborn==0.13.2 ; python_version >= "3.10" and python_version < "3.13"
setuptools==78.1.0 ; python_version >= "3.10" and python_version < "3.13"
shellingham==1.5.4 ; python_version >= "3.10" and python_version < "3.13"
six==1.17.0 ; python_version >= "3.10" and python_version < "3.13"
smart-open==7.1.0 ; python_version >= "3.10" and python_version < "3.13"
smmap==5.0.2 ; python_version >= "3.10" and python_version < "3.13"
sniffio==1.3.1 ; python_version >= "3.10" and python_version < "3.13"
sqlalchemy==2.0.40 ; python_version >= "3.10" and python_version < "3.13"
starlette==0.46.1 ; python_version >= "3.10" and python_version < "3.13"
statsmodels==0.14.4 ; python_version >= "3.10" and python_version < "3.13"
streamlit==1.44.1 ; python_version >= "3.10" and python_version < "3.13"
tenacity==9.1.2 ; python_version >= "3.10" and python_version < "3.13"
texttable==1.7.0 ; python_version >= "3.10" and python_version < "3.13"
threadpoolctl==3.6.0 ; python_version >= "3.10" and python_version < "3.13"
thresholdclustering==1.1 ; python_version >= "3.10" and python_version < "3.13"
tiktoken==0.8.0 ; python_version >= "3.10" and python_version < "3.13"
toml==0.10.2 ; python_version >= "3.10" and python_version < "3.13"
tomli==2.2.1 ; python_version >= "3.10" and python_version < "3.11"
tornado==6.4.2 ; python_version >= "3.10" and python_version < "3.13"
tqdm==4.67.1 ; python_version >= "3.10" and python_version < "3.13"
tree-sitter-languages==1.10.2 ; python_version >= "3.10" and python_version < "3.13"
tree-sitter==0.21.3 ; python_version >= "3.10" and python_version < "3.13"
typer==0.15.2 ; python_version >= "3.10" and python_version < "3.13"
typing-extensions==4.13.1 ; python_version >= "3.10" and python_version < "3.13"
typing-inspect==0.9.0 ; python_version >= "3.10" and python_version < "3.13"
typing-inspection==0.4.0 ; python_version >= "3.10" and python_version < "3.13"
tzdata==2025.2 ; python_version >= "3.10" and python_version < "3.13"
umap-learn==0.5.7 ; python_version >= "3.10" and python_version < "3.13"
urllib3==2.3.0 ; python_version >= "3.10" and python_version < "3.13"
uuid6==2024.7.10 ; python_version >= "3.10" and python_version < "3.13"
vine==5.1.0 ; python_version >= "3.10" and python_version < "3.13"
watchdog==6.0.0 ; python_version >= "3.10" and python_version < "3.13" and platform_system != "Darwin"
wcwidth==0.2.13 ; python_version >= "3.10" and python_version < "3.13"
win32-setctime==1.2.0 ; python_version >= "3.10" and python_version < "3.13" and sys_platform == "win32"
wrapt==1.17.2 ; python_version >= "3.10" and python_version < "3.13"
yarl==1.19.0 ; python_version >= "3.10" and python_version < "3.13"
zstandard==0.23.0 ; python_version >= "3.10" and python_version < "3.13"
