#!/usr/bin/env python3
"""
Analyze a Software Bill of Materials (SBOM) for M&A due diligence.

This script analyzes a CycloneDX SBOM file to identify:
- License compliance issues
- Security vulnerabilities
- Outdated dependencies
- Other potential issues relevant for M&A

Usage:
    python analyze_sbom.py --sbom sbom_output/combined-sbom.json --output sbom_output/analysis-report.md
"""

import argparse
import json
import os
import sys
from typing import Dict, List, Any, Set
import datetime

# ANSI colors for terminal output
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'

def print_header(message: str) -> None:
    """Print a formatted header message."""
    print(f"\n{Colors.HEADER}{Colors.BOLD}=== {message} ==={Colors.ENDC}\n")

def print_step(message: str) -> None:
    """Print a formatted step message."""
    print(f"{Colors.BLUE}→ {message}{Colors.ENDC}")

def print_success(message: str) -> None:
    """Print a formatted success message."""
    print(f"{Colors.GREEN}✓ {message}{Colors.ENDC}")

def print_warning(message: str) -> None:
    """Print a formatted warning message."""
    print(f"{Colors.YELLOW}⚠ {message}{Colors.ENDC}")

def print_error(message: str) -> None:
    """Print a formatted error message."""
    print(f"{Colors.RED}✗ {message}{Colors.ENDC}")

# Define problematic licenses for M&A
COPYLEFT_LICENSES = {
    "GPL-1.0", "GPL-2.0", "GPL-3.0", 
    "AGPL-1.0", "AGPL-3.0", 
    "LGPL-2.0", "LGPL-2.1", "LGPL-3.0",
    "MPL-1.0", "MPL-1.1", "MPL-2.0",
    "CDDL-1.0", "CDDL-1.1",
    "EPL-1.0", "EPL-2.0"
}

PERMISSIVE_LICENSES = {
    "MIT", "Apache-2.0", "BSD-2-Clause", "BSD-3-Clause", 
    "ISC", "Unlicense", "0BSD", "CC0-1.0"
}

def analyze_licenses(components: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze license information in the SBOM."""
    print_step("Analyzing licenses...")
    
    license_analysis = {
        "components_with_licenses": 0,
        "components_without_licenses": 0,
        "license_types": {},
        "copyleft_licenses": {},
        "unknown_licenses": {},
        "components_without_license": []
    }
    
    for component in components:
        name = component.get("name", "Unknown")
        version = component.get("version", "Unknown")
        component_id = f"{name}@{version}"
        
        if "licenses" in component and component["licenses"]:
            license_analysis["components_with_licenses"] += 1
            
            for license_data in component["licenses"]:
                if "license" in license_data:
                    license_obj = license_data["license"]
                    license_id = license_obj.get("id", license_obj.get("name", "Unknown"))
                    
                    # Count license types
                    if license_id not in license_analysis["license_types"]:
                        license_analysis["license_types"][license_id] = 0
                    license_analysis["license_types"][license_id] += 1
                    
                    # Check for copyleft licenses
                    if license_id in COPYLEFT_LICENSES:
                        if license_id not in license_analysis["copyleft_licenses"]:
                            license_analysis["copyleft_licenses"][license_id] = []
                        license_analysis["copyleft_licenses"][license_id].append(component_id)
                    
                    # Check for unknown licenses
                    if license_id not in COPYLEFT_LICENSES and license_id not in PERMISSIVE_LICENSES:
                        if license_id not in license_analysis["unknown_licenses"]:
                            license_analysis["unknown_licenses"][license_id] = []
                        license_analysis["unknown_licenses"][license_id].append(component_id)
        else:
            license_analysis["components_without_licenses"] += 1
            license_analysis["components_without_license"].append(component_id)
    
    return license_analysis

def analyze_dependencies(components: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze dependency information in the SBOM."""
    print_step("Analyzing dependencies...")
    
    dependency_analysis = {
        "total_components": len(components),
        "by_type": {},
        "by_language": {},
        "top_dependencies": []
    }
    
    # Count components by type and language
    for component in components:
        component_type = component.get("type", "Unknown")
        if component_type not in dependency_analysis["by_type"]:
            dependency_analysis["by_type"][component_type] = 0
        dependency_analysis["by_type"][component_type] += 1
        
        # Try to determine language
        name = component.get("name", "")
        purl = component.get("purl", "")
        
        language = "Unknown"
        if "purl" in component:
            purl = component["purl"]
            if purl.startswith("pkg:npm"):
                language = "JavaScript/TypeScript"
            elif purl.startswith("pkg:pypi"):
                language = "Python"
            elif purl.startswith("pkg:golang"):
                language = "Go"
            elif purl.startswith("pkg:maven"):
                language = "Java"
        
        if language not in dependency_analysis["by_language"]:
            dependency_analysis["by_language"][language] = 0
        dependency_analysis["by_language"][language] += 1
    
    # Sort components by frequency
    component_count = {}
    for component in components:
        name = component.get("name", "Unknown")
        if name not in component_count:
            component_count[name] = 0
        component_count[name] += 1
    
    # Get top dependencies
    top_dependencies = sorted(component_count.items(), key=lambda x: x[1], reverse=True)[:20]
    dependency_analysis["top_dependencies"] = top_dependencies
    
    return dependency_analysis

def generate_report(sbom_file: str, license_analysis: Dict[str, Any], 
                   dependency_analysis: Dict[str, Any]) -> str:
    """Generate a markdown report of the SBOM analysis."""
    print_step("Generating report...")
    
    report = f"""# SBOM Analysis Report for M&A Due Diligence

**Generated:** {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
**SBOM File:** {os.path.basename(sbom_file)}

## Executive Summary

This report analyzes the Software Bill of Materials (SBOM) for the Bracket codebase to identify potential issues for M&A due diligence with GitLab.

### Key Findings

- **Total Components:** {dependency_analysis["total_components"]}
- **Components with License Information:** {license_analysis["components_with_licenses"]}
- **Components Missing License Information:** {license_analysis["components_without_licenses"]}
- **Copyleft Licenses:** {len(license_analysis["copyleft_licenses"])} types found
- **Unknown Licenses:** {len(license_analysis["unknown_licenses"])} types found

## License Analysis

### License Distribution

| License | Count |
|---------|-------|
"""
    
    # Add license distribution table
    for license_id, count in sorted(license_analysis["license_types"].items(), key=lambda x: x[1], reverse=True):
        report += f"| {license_id} | {count} |\n"
    
    # Add copyleft license section
    report += """
### Copyleft Licenses

The following copyleft licenses were found, which may require special attention during M&A:

"""
    
    if license_analysis["copyleft_licenses"]:
        for license_id, components in license_analysis["copyleft_licenses"].items():
            report += f"#### {license_id}\n\n"
            report += "| Component | \n|----------|\n"
            for component in components[:10]:  # Limit to 10 components per license
                report += f"| {component} |\n"
            if len(components) > 10:
                report += f"| ... and {len(components) - 10} more |\n"
            report += "\n"
    else:
        report += "No copyleft licenses were found.\n\n"
    
    # Add unknown license section
    report += """
### Unknown Licenses

The following licenses were found but could not be classified as permissive or copyleft:

"""
    
    if license_analysis["unknown_licenses"]:
        for license_id, components in license_analysis["unknown_licenses"].items():
            report += f"#### {license_id}\n\n"
            report += "| Component | \n|----------|\n"
            for component in components[:10]:  # Limit to 10 components per license
                report += f"| {component} |\n"
            if len(components) > 10:
                report += f"| ... and {len(components) - 10} more |\n"
            report += "\n"
    else:
        report += "No unknown licenses were found.\n\n"
    
    # Add components without license section
    report += """
### Components Without License Information

The following components do not have license information:

"""
    
    if license_analysis["components_without_license"]:
        report += "| Component | \n|----------|\n"
        for component in license_analysis["components_without_license"][:20]:  # Limit to 20 components
            report += f"| {component} |\n"
        if len(license_analysis["components_without_license"]) > 20:
            report += f"| ... and {len(license_analysis['components_without_license']) - 20} more |\n"
    else:
        report += "All components have license information.\n"
    
    # Add dependency analysis section
    report += """
## Dependency Analysis

### Components by Type

| Type | Count |
|------|-------|
"""
    
    for component_type, count in sorted(dependency_analysis["by_type"].items(), key=lambda x: x[1], reverse=True):
        report += f"| {component_type} | {count} |\n"
    
    report += """
### Components by Language

| Language | Count |
|----------|-------|
"""
    
    for language, count in sorted(dependency_analysis["by_language"].items(), key=lambda x: x[1], reverse=True):
        report += f"| {language} | {count} |\n"
    
    report += """
### Top Dependencies

| Dependency | Count |
|------------|-------|
"""
    
    for dependency, count in dependency_analysis["top_dependencies"]:
        report += f"| {dependency} | {count} |\n"
    
    report += """
## Recommendations for M&A Due Diligence

Based on this analysis, we recommend:

1. **Review Copyleft Licenses**: Carefully review components with copyleft licenses to ensure compliance and understand their impact on the acquisition.

2. **Investigate Missing License Information**: For components without license information, determine their licenses to ensure compliance.

3. **Evaluate Unknown Licenses**: Review components with unknown licenses to understand their terms and potential impact.

4. **Dependency Management**: Consider the complexity of dependencies and their potential impact on integration with GitLab's systems.

5. **Security Assessment**: Use this SBOM with vulnerability scanning tools to identify security risks.

## Next Steps

1. Share this report with the legal team for license compliance review
2. Conduct a security vulnerability assessment using the SBOM
3. Identify any dependencies that might cause integration issues with GitLab
4. Address any critical issues before proceeding with the acquisition
"""
    
    return report

def main():
    parser = argparse.ArgumentParser(description="Analyze a CycloneDX SBOM for M&A due diligence")
    parser.add_argument("--sbom", required=True, help="Path to the CycloneDX SBOM JSON file")
    parser.add_argument("--output", required=True, help="Path to the output report file")
    args = parser.parse_args()
    
    print_header("Analyzing SBOM for M&A Due Diligence")
    
    try:
        # Load SBOM file
        print_step(f"Loading SBOM from {args.sbom}...")
        with open(args.sbom, 'r') as f:
            sbom_data = json.load(f)
        
        components = sbom_data.get("components", [])
        print_success(f"Loaded {len(components)} components from SBOM")
        
        # Analyze licenses
        license_analysis = analyze_licenses(components)
        print_success("License analysis complete")
        
        # Analyze dependencies
        dependency_analysis = analyze_dependencies(components)
        print_success("Dependency analysis complete")
        
        # Generate report
        report = generate_report(args.sbom, license_analysis, dependency_analysis)
        
        # Write report to file
        with open(args.output, 'w') as f:
            f.write(report)
        
        print_success(f"Analysis report written to {args.output}")
        
    except Exception as e:
        print_error(f"Error analyzing SBOM: {str(e)}")
        sys.exit(1)
    
    print_header("SBOM Analysis Complete")

if __name__ == "__main__":
    main()
