# Mermaid Diagram Beautifier

A tool to create beautiful, professional mermaid diagrams using <PERSON><PERSON><PERSON>'s <PERSON> with non-blocking parallel processing.

## Overview

This tool transforms existing mermaid diagrams into exceptional visualizations that are both aesthetically pleasing and highly informative. It uses Anthropic's <PERSON> to analyze and enhance the diagrams while preserving all original node IDs and function names.

### Key Features

- **Beautiful Visualizations**: Creates professional, visually stunning diagrams with enhanced styling
- **Syntax Correction**: Removes problematic syntax like parentheses that cause rendering errors
- **Visual Hierarchy**: Implements meaningful subgraphs and clear section organization
- **Professional Styling**: Uses complementary color palettes and consistent visual design
- **Memory Efficient**: Processes diagrams in batches and saves after each batch
- **Non-blocking Parallel Processing**: Sends multiple API requests in parallel without waiting
- **Rate Limiting**: Respects API rate limits while maximizing throughput
- **Batch Processing**: Processes diagrams in small batches to avoid memory issues
- **Preservation**: Maintains all original node IDs and function names

## Files in this Directory

- **mermaid_beautifier.py**: The main implementation of the beautifier
- **test_mermaid_beautifier.py**: A test script with dry run option
- **run_beautifier.py**: A script to run the beautifier on the example file
- **example_mermaid.md**: An example file with various mermaid diagrams
- **README_mermaid_beautifier.md**: Detailed documentation on how to use the beautifier

## Quick Start

1. Set your Anthropic API key as an environment variable:
   ```bash
   export ANTHROPIC_API_KEY=your_api_key_here
   ```

2. Run the beautifier on the example file:
   ```bash
   python run_beautifier.py
   ```

3. Check the output directory for the improved diagrams.

For more detailed instructions, see [README_mermaid_beautifier.md](README_mermaid_beautifier.md).
