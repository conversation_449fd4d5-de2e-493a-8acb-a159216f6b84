#!/usr/bin/env python3
"""
Run the Mermaid Beautifier on the example file.

This script runs the MermaidBeautifier on the example_mermaid.md file
and saves the improved diagrams to the output directory.

Usage:
    python run_beautifier.py
"""

import os
import asyncio
import logging
from pathlib import Path

from mermaid_beautifier import MermaidBeautifier

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def main():
    """Main entry point for the script."""
    # Get the current directory
    current_dir = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/outputs/datadog_agent/diagrams/hierarchical_diagrams/diagrams_from_json"
    # current_dir = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/mermaid_outputs_json/gitlab/hierarchical_diagrams"
    # current_dir = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/reworking_mermaid/mermaid_outputs_improved/trial"

    # Set up input and output directories
    input_dir = current_dir  # Directory containing example_mermaid.md
    output_dir = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/outputs/datadog_agent/diagrams/hierarchical_diagrams/beautified_diagrams"
    # output_dir = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/reworking_mermaid/mermaid_outputs_improved/trial_outputs"

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Initialize the mermaid beautifier
    beautifier = MermaidBeautifier(
        input_dir=input_dir,
        output_dir=output_dir,
        claude_api_key=None,  # Will use env var if None
        claude_model="claude-3-7-sonnet-20250219",
        max_tokens=8192,
        temperature=0.7,
        rate_limit=1000,
        rate_period=60,
        max_concurrent_requests=15,
        batch_size=20,  # Process 20 diagrams at a time before saving
        dry_run=False,  # Actually save the changes
    )

    # Run the beautification process
    stats = await beautifier.run()

    # Print statistics
    print("\nMermaid Beautification Results:")
    print(f"Total files: {stats['total_files']}")
    print(f"Processed files: {stats['processed_files']}")
    print(f"Improved files: {stats['improved_files']}")
    print(f"Unchanged files: {stats['unchanged_files']}")
    print(f"Failed files: {stats['failed_files']}")

    # Print the path to the output file
    if stats['improved_files'] > 0:
        output_file = os.path.join(output_dir, "example_mermaid.md")
        print(f"\nImproved diagrams saved to: {output_file}")
        print("You can open this file to see the improved diagrams.")

if __name__ == "__main__":
    asyncio.run(main())
