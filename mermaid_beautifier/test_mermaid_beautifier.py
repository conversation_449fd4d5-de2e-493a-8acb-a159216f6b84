#!/usr/bin/env python3
"""
Test script for the Mermaid Beautifier.

This script demonstrates how to use the MermaidBeautifier class to improve
the quality of mermaid diagrams in a directory.

Usage:
    python test_mermaid_beautifier.py
"""

import os
import asyncio
import logging
from pathlib import Path

from mermaid_beautifier import MermaidBeautifier

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def main():
    """Main entry point for the test script."""
    # Configuration variables - modify these values directly
    # Use the current directory for the example file
    current_dir = Path(os.path.dirname(os.path.abspath(__file__)))
    input_dir = current_dir  # Directory containing example_mermaid.md
    output_dir = current_dir / "output"  # Output directory for improved diagrams

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Set to True to see what would be done without making changes
    dry_run = True

    # Claude API configuration
    claude_api_key = None  # Will use env var if None
    claude_model = "claude-3-7-sonnet-20250219"
    max_tokens = 8192
    temperature = 0.7

    # Rate limiting configuration
    rate_limit = 2  # Number of API calls allowed per rate period
    rate_period = 120  # Time period in seconds for rate limiting
    max_concurrent = 5  # Maximum number of concurrent API requests

    # Initialize the mermaid beautifier
    beautifier = MermaidBeautifier(
        input_dir=input_dir,
        output_dir=output_dir if 'output_dir' in locals() else None,
        claude_api_key=claude_api_key,
        claude_model=claude_model,
        max_tokens=max_tokens,
        temperature=temperature,
        rate_limit=rate_limit,
        rate_period=rate_period,
        max_concurrent_requests=max_concurrent,
        dry_run=dry_run,
    )

    # Run the beautification process
    stats = await beautifier.run()

    # Print statistics
    print("\nMermaid Beautification Results:")
    print(f"Total files: {stats['total_files']}")
    print(f"Processed files: {stats['processed_files']}")
    print(f"Improved files: {stats['improved_files']}")
    print(f"Unchanged files: {stats['unchanged_files']}")
    print(f"Failed files: {stats['failed_files']}")

if __name__ == "__main__":
    asyncio.run(main())
