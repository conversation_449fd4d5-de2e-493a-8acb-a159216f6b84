# Mermaid Diagram Beautifier

This tool transforms existing mermaid diagrams into beautiful, professional visualizations by processing them with Anthropic's <PERSON> using non-blocking parallel API calls. It creates visually stunning diagrams with enhanced styling, fixes syntax issues, and improves readability while preserving all original node IDs and function names.

## Features

- **Beautiful Visualizations**: Creates professional, visually stunning diagrams with enhanced styling
- **Syntax Correction**: Removes problematic syntax like parentheses that cause rendering errors
- **Visual Hierarchy**: Implements meaningful subgraphs and clear section organization
- **Professional Styling**: Uses complementary color palettes and consistent visual design
- **Enhanced Readability**: Improves layout, groups related nodes, and adds descriptive labels
- **Syntax Validation**: Ensures diagrams comply with the latest Mermaid.js syntax
- **Non-blocking Parallel Processing**: Sends multiple API requests in parallel without waiting
- **Efficient Rate Limiting**: Respects API rate limits while maximizing throughput
- **Non-destructive**: Preserves all original node IDs and function names

## Requirements

- Python 3.7+
- `bracket_core` package with Claude client implementation
- Anthropic API key (set as environment variable `ANTHROPIC_API_KEY` or provided as argument)

## Installation

1. Ensure you have the required dependencies:
   ```bash
   pip install anthropic
   ```

2. Make sure the `bracket_core` package is in your Python path.

3. Set your Anthropic API key as an environment variable:
   ```bash
   export ANTHROPIC_API_KEY=your_api_key_here
   ```

## Usage

### Command Line

```bash
python mermaid_beautifier.py --input_dir /path/to/mermaid/diagrams
```

### Options

- `--input_dir`: Directory containing mermaid diagram files (required)
- `--output_dir`: Directory to save improved diagrams (if not specified, overwrites original files)
- `--claude_api_key`: Anthropic API key (if not specified, will try to get from environment)
- `--claude_model`: Claude model to use (default: claude-3-7-sonnet-20250219)
- `--max_tokens`: Maximum tokens to generate (default: 8192)
- `--temperature`: Sampling temperature (default: 0.7)
- `--rate_limit`: Number of API calls allowed per rate period (default: 2)
- `--rate_period`: Time period in seconds for rate limiting (default: 120)
- `--max_concurrent`: Maximum number of concurrent API requests (default: 5)
- `--batch_size`: Number of diagrams to process in a batch before saving (default: 10)
- `--dry_run`: Don't actually save changes, just log what would be done

### As a Library

You can also use the `MermaidBeautifier` class in your own code:

```python
import asyncio
from mermaid_beautifier import MermaidBeautifier

async def beautify_diagrams():
    beautifier = MermaidBeautifier(
        input_dir="path/to/mermaid/diagrams",
        output_dir="path/to/output/directory",  # Optional
        claude_api_key=None,  # Will use env var if None
        claude_model="claude-3-7-sonnet-20250219",
        max_tokens=8192,
        temperature=0.7,
        rate_limit=2,
        rate_period=120,
        max_concurrent_requests=5,
        batch_size=10,  # Process 10 diagrams at a time before saving
        dry_run=False,
    )

    stats = await beautifier.run()
    print(f"Improved {stats['improved_files']} out of {stats['total_files']} files")

if __name__ == "__main__":
    asyncio.run(beautify_diagrams())
```

## How It Works

1. The tool searches for files with `.md` or `.mermaid` extensions in the input directory
2. It extracts mermaid diagrams from these files (code blocks between ```mermaid and ```)
3. For each file, it processes diagrams in small batches (default: 10 diagrams per batch)
4. Within each batch, it creates tasks for all diagrams and sends them to Claude in parallel
5. Rate limiting is applied at the batch level, ensuring we don't exceed API limits
6. As responses come back, the improved diagrams replace the original ones in the content
7. After each batch is processed, the modified content is saved to disk
8. This batch processing ensures memory efficiency when handling thousands of diagrams

## Beautification Process

The tool instructs Claude to create exceptional visualizations by:

1. **Validating & correcting** syntax to ensure perfect compliance with Mermaid.js:
   - Removing parentheses that cause rendering errors
   - Removing problematic syntax like "Direction TD", "notes", or "type label"
   - Fixing any other syntax issues that would prevent proper rendering

2. **Creating professional, beautiful layouts**:
   - Using left-to-right layout (`flowchart LR`) for better readability
   - Implementing clean, modern designs with consistent spacing and alignment
   - Creating visually balanced diagrams with minimal line crossings
   - Using meaningful subgraphs to group related functionality
   - Adding clear section headers as comments

3. **Enhancing node styling and visual hierarchy**:
   - Using professional color palettes with complementary colors
   - Applying subtle gradients and soft shadows where appropriate
   - Using rounded corners, consistent stroke widths, and appropriate fills
   - Varying node shapes based on their function
   - Creating clear visual hierarchies that guide the eye through the diagram

4. **Improving labels and descriptions**:
   - Preserving all original node IDs and function names exactly
   - Adding concise, descriptive labels that explain the purpose of each node
   - Using consistent terminology and naming conventions
   - Formatting text for maximum readability

5. **Adding visual enhancements**:
   - Using custom classDef blocks to create cohesive visual themes
   - Applying different styles for different types of nodes
   - Using color to indicate status, importance, or relationships
   - Adding meaningful comments to explain complex sections

## Example

### Before

```mermaid
graph TD
A[Start] --> B{Is it working?}
B -->|Yes| C[Great!]
B -->|No| D[Debug]
C --> E[Continue]
D --> F[Fix]
F --> B
```

### After

```mermaid
flowchart LR
    %% Main process flow with enhanced styling and organization
    A["🚀 Start"] --> B{"✓ Is it working?"}

    %% Success path with professional styling
    subgraph success ["✅ Success Path"]
        direction LR
        B -->|"Yes"| C["🎉 Great!"]
        C --> E["⏩ Continue"]
    end

    %% Debug loop with clear visual hierarchy
    subgraph debug ["🔄 Debug Loop"]
        direction LR
        B -->|"No"| D["🔍 Debug"]
        D --> F["🔧 Fix"]
        F -->|"Try again"| B
    end

    %% Professional color palette and styling
    classDef default fill:#f8f9fa,stroke:#495057,stroke-width:1px,color:#212529,font-size:14px,rx:5,ry:5
    classDef decision fill:#e9ecef,stroke:#4361ee,stroke-width:2px,color:#4361ee,font-weight:bold,rx:10,ry:10
    classDef success fill:#d8f3dc,stroke:#2d6a4f,stroke-width:1px,color:#2d6a4f,font-weight:bold,rx:5,ry:5
    classDef error fill:#ffccd5,stroke:#9d0208,stroke-width:1px,color:#9d0208,font-weight:bold,rx:5,ry:5
    classDef subgraph fill:#f8f9fa,stroke:#ced4da,stroke-width:1px,color:#495057,rx:10,ry:10

    %% Apply styles to nodes
    class A default
    class B decision
    class C,E success
    class D,F error
    class success,debug subgraph

    %% Add tooltips for better user experience
    linkStyle 0 stroke:#495057,stroke-width:2px
    linkStyle 1,2 stroke:#2d6a4f,stroke-width:2px
    linkStyle 3,4,5 stroke:#9d0208,stroke-width:2px,stroke-dasharray:5 5
```

## License

MIT
