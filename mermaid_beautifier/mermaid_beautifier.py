#!/usr/bin/env python3
"""
Mermaid Diagram Beautifier

This script improves the quality of existing mermaid diagrams by:
1. Reading mermaid diagrams from a specified directory
2. Processing them using Anthropic Claude LLM with rate limiting
3. Improving their quality and syntax according to provided instructions
4. Saving the improved diagrams back to their original locations

Usage:
    python mermaid_beautifier.py --input_dir /path/to/mermaid/diagrams
"""

import os
import logging
import json
import time
import asyncio
import argparse
import re
from typing import List, Dict, Any, Optional, Tuple, Set
from pathlib import Path

# Import Claude client and rate limiter from bracket_core
from bracket_core.llm.get_client import get_claude_client
from bracket_core.llm.rate_limiter import RateLimiter
from bracket_core.llm.api_keys import get_anthropic_api_key

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MermaidBeautifier:
    """Process and improve mermaid diagrams using Claude LLM."""

    def __init__(
        self,
        input_dir: str,
        output_dir: Optional[str] = None,
        claude_api_key: Optional[str] = None,
        claude_model: str = "claude-3-7-sonnet-20250219",
        max_tokens: int = 8192,
        temperature: float = 0.7,
        rate_limit: int = 2,
        rate_period: int = 120,
        max_concurrent_requests: int = 5,
        batch_size: int = 10,
        dry_run: bool = False,
    ):
        """Initialize the mermaid beautifier.

        Args:
            input_dir: Directory containing mermaid diagram files
            output_dir: Directory to save improved diagrams (if None, overwrites original files)
            claude_api_key: Anthropic API key. If None, will try to get from environment.
            claude_model: Claude model to use.
            max_tokens: Maximum tokens to generate.
            temperature: Sampling temperature.
            rate_limit: Number of API calls allowed per rate_period.
            rate_period: Time period in seconds for rate limiting.
            max_concurrent_requests: Maximum number of concurrent API requests.
            batch_size: Number of diagrams to process in a batch before saving.
            dry_run: If True, don't actually save changes, just log what would be done.
        """
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir) if output_dir else None
        self.dry_run = dry_run
        self.max_concurrent_requests = max_concurrent_requests
        self.batch_size = batch_size

        # Initialize Claude client
        self.claude_client = get_claude_client(
            api_key=claude_api_key,
            model=claude_model,
            max_tokens=max_tokens,
            temperature=temperature,
        )

        # Initialize rate limiter
        self.rate_limiter = RateLimiter(rate=rate_limit, per=rate_period)

        # Statistics
        self.stats = {
            "total_files": 0,
            "processed_files": 0,
            "failed_files": 0,
            "unchanged_files": 0,
            "improved_files": 0,
        }

    async def find_mermaid_files(self) -> List[Path]:
        """Find all files containing mermaid diagrams in the input directory.

        Returns:
            List of file paths containing mermaid diagrams.
        """
        logger.info(f"Searching for mermaid files in {self.input_dir}")

        mermaid_files = []

        # Walk through the directory
        for root, _, files in os.walk(self.input_dir):
            for file in files:
                if file.endswith(('.md', '.mermaid')):
                    file_path = Path(root) / file

                    # Check if the file contains mermaid diagrams
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            if '```mermaid' in content:
                                mermaid_files.append(file_path)
                    except Exception as e:
                        logger.warning(f"Error reading file {file_path}: {e}")

        logger.info(f"Found {len(mermaid_files)} files containing mermaid diagrams")
        self.stats["total_files"] = len(mermaid_files)
        return mermaid_files

    def extract_mermaid_diagrams(self, content: str) -> List[Tuple[str, str, int, int]]:
        """Extract mermaid diagrams from file content.

        Args:
            content: File content as string

        Returns:
            List of tuples (diagram_id, diagram_content, start_index, end_index)
        """
        diagrams = []

        # Find all mermaid code blocks
        pattern = r'```mermaid\s*([\s\S]*?)```'
        for i, match in enumerate(re.finditer(pattern, content)):
            diagram_id = f"diagram_{i}"
            diagram_content = match.group(1).strip()
            start_index = match.start()
            end_index = match.end()

            diagrams.append((diagram_id, diagram_content, start_index, end_index))

        return diagrams

    async def beautify_mermaid(self, diagram_content: str) -> str:
        """Beautify a mermaid diagram using Claude.

        Args:
            diagram_content: Original mermaid diagram content

        Returns:
            Improved mermaid diagram content
        """
        # System prompt with detailed instructions
        system_prompt = """
You are **MermaidDocGPT**, a world-class expert in creating beautiful, professional [Mermaid.js](https://mermaid-js.github.io/) diagrams. Your goal is to transform any mermaid diagram into an exceptional visualization that is both aesthetically pleasing and highly informative.

1. **Validate & correct** to ensure perfect compliance with the latest Mermaid.js syntax:
   - REMOVE ALL PARENTHESES "(" and ")" from node text as they cause rendering errors
   - REMOVE any "Direction TD", "notes", or "type label" syntax that causes errors
   - Fix any other syntax issues that would prevent proper rendering
   - ENSURE the diagram is 100% syntactically correct and will render properly

2. **Create a professional, beautiful layout**:
   - Use left-to-right layout (`flowchart LR`) for better readability
   - Implement a clean, modern design with consistent spacing and alignment
   - Create a visually balanced diagram with minimal line crossings
   - Use meaningful subgraphs to group related functionality
   - Add clear section headers as comments (e.g., `%% Core Components`)
   - AVOID CYCLIC DEPENDENCIES in the diagram structure

3. **Enhance node styling and visual hierarchy**:
   - Use a professional color palette with complementary colors
   - Apply subtle gradients and soft shadows where appropriate
   - Use rounded corners, consistent stroke widths, and appropriate fills
   - Vary node shapes based on their function (rectangles, diamonds, circles, etc.)
   - Create a clear visual hierarchy that guides the eye through the diagram

4. **Improve labels and descriptions**:
   - Preserve all original node IDs and function names exactly
   - Add concise, descriptive labels that explain the purpose of each node
   - Use consistent terminology and naming conventions
   - Format text for maximum readability (e.g., proper capitalization, spacing)

5. **Add visual enhancements**:
   - Use custom classDef blocks to create a cohesive visual theme
   - Apply different styles for different types of nodes (core, utility, data, error)
   - Use color to indicate status, importance, or relationships
   - Add meaningful comments to explain complex sections

CRITICAL REQUIREMENTS:
- NEVER remove any nodes or connections from the original diagram
- ALWAYS preserve all function names and node IDs exactly as they appear in the original
- REMOVE ALL PARENTHESES "(" and ")" from node text as they cause rendering errors
- REMOVE any "Direction TD", "notes", or "type label" syntax that causes errors
- DO NOT add explanatory text outside the mermaid code
- ONLY output the improved mermaid code, nothing else
- Create the most visually stunning and professional diagram possible
- Ensure the diagram is dramatically more beautiful and readable than the original
- AVOID ALL CYCLIC DEPENDENCIES as they can cause rendering issues
- ENSURE the diagram is 100% CORRECT and will render properly without errors
"""

        # User prompt
        user_prompt = f"""
Transform this mermaid diagram into a beautiful, professional visualization that is both aesthetically pleasing and highly informative. Create the most visually stunning diagram possible while ensuring it renders correctly.

Remember to:
1. Remove all parentheses "(" and ")" from node text as they cause rendering errors
2. Remove any "Direction TD", "notes", or "type label" syntax that causes errors
3. Use a professional color palette with complementary colors
4. Create a clear visual hierarchy with meaningful subgraphs
5. Preserve all original node IDs and function names exactly
6. AVOID ALL CYCLIC DEPENDENCIES as they can cause rendering issues
7. ENSURE the diagram is 100% CORRECT and will render properly without errors

```mermaid
{diagram_content}
```

Return ONLY the improved mermaid code without any markdown formatting or explanations.
"""

        try:
            # Call Claude API
            response = await self.claude_client.generate(
                prompt=user_prompt,
                system_prompt=system_prompt,
                max_tokens=8192,
                temperature=0.7,
            )

            # Extract the mermaid diagram from the response
            improved_diagram = self._extract_mermaid_diagram(response)

            if not improved_diagram:
                # If no mermaid code block found, use the raw response
                # but clean it up to ensure it's just the mermaid code
                improved_diagram = response.strip()

                # Remove any markdown code block markers if present
                improved_diagram = re.sub(r'^```mermaid\s*', '', improved_diagram)
                improved_diagram = re.sub(r'\s*```$', '', improved_diagram)

            # Post-process to ensure problematic syntax is removed
            improved_diagram = self._post_process_diagram(improved_diagram)

            return improved_diagram

        except Exception as e:
            logger.error(f"Error beautifying mermaid diagram: {e}")
            # Return the original diagram if there's an error
            return diagram_content

    def _extract_mermaid_diagram(self, response: str) -> Optional[str]:
        """Extract mermaid diagram from Claude response.

        Args:
            response: Claude response text

        Returns:
            Extracted mermaid diagram content or None if not found
        """
        # Try to extract mermaid code block
        pattern = r'```mermaid\s*([\s\S]*?)```'
        match = re.search(pattern, response)

        if match:
            return match.group(1).strip()

        return None

    def _post_process_diagram(self, diagram: str) -> str:
        """Post-process the diagram to ensure problematic syntax is removed.

        Args:
            diagram: Mermaid diagram content

        Returns:
            Processed diagram content with problematic syntax removed
        """
        # Remove parentheses from node text
        # This regex looks for text inside quotes and removes parentheses within that text
        diagram = re.sub(r'"([^"]*?)\(([^"]*?)\)([^"]*)"', r'"\1\2\3"', diagram)

        # Remove any remaining parentheses in node text (different format)
        diagram = re.sub(r'\[([^\[\]]*?)\(([^\[\]]*?)\)([^\[\]]*)\]', r'[\1\2\3]', diagram)

        # Remove Direction TD syntax
        diagram = re.sub(r'\bDirection\s+TD\b', '', diagram)

        # Remove 'notes' syntax that might cause errors
        diagram = re.sub(r'\bnotes\b', '', diagram)

        # Remove 'type label' syntax
        diagram = re.sub(r'\btype\s+label\b', '', diagram)

        return diagram

    async def process_file(self, file_path: Path) -> bool:
        """Process a single file containing mermaid diagrams.

        Args:
            file_path: Path to the file to process

        Returns:
            True if the file was successfully processed, False otherwise
        """
        logger.info(f"Processing file: {file_path}")

        try:
            # Read the file content
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Extract mermaid diagrams
            diagrams = self.extract_mermaid_diagrams(content)

            if not diagrams:
                logger.warning(f"No mermaid diagrams found in {file_path}")
                return False

            # Determine the output path
            output_path = file_path
            if self.output_dir:
                # Create relative path from input_dir
                rel_path = file_path.relative_to(self.input_dir)
                output_path = self.output_dir / rel_path

                # Create parent directories if they don't exist
                output_path.parent.mkdir(parents=True, exist_ok=True)

            # Process diagrams in batches to avoid keeping everything in memory
            # Use the batch size specified in the constructor
            total_diagrams = len(diagrams)
            improved_count = 0
            modified_content = content
            offset = 0  # Offset to adjust indices after replacements

            # Process diagrams in batches
            for batch_start in range(0, total_diagrams, self.batch_size):
                batch_end = min(batch_start + self.batch_size, total_diagrams)
                batch_diagrams = diagrams[batch_start:batch_end]

                logger.info(f"Processing batch {batch_start//self.batch_size + 1} of {(total_diagrams + self.batch_size - 1)//self.batch_size} for {file_path}")

                # Create tasks for this batch
                diagram_tasks = []
                for diagram_id, diagram_content, _, _ in batch_diagrams:
                    logger.info(f"Queuing diagram {diagram_id} in {file_path} for processing")
                    # Create a task for each diagram
                    task = asyncio.create_task(self.beautify_mermaid(diagram_content))
                    diagram_tasks.append((diagram_id, diagram_content, task))

                # Apply rate limiting for the batch
                for _ in range(len(diagram_tasks)):
                    await self.rate_limiter.acquire()

                # Process each diagram in the batch as its response comes in
                batch_modified = False
                for (diagram_id, diagram_content, task), (_, _, start_index, end_index) in zip(diagram_tasks, batch_diagrams):
                    # Get the improved diagram
                    improved_diagram = await task

                    # Skip if the diagram didn't change
                    if improved_diagram.strip() == diagram_content.strip():
                        logger.info(f"Diagram {diagram_id} in {file_path} is already optimized")
                        continue

                    improved_count += 1
                    batch_modified = True

                    # Replace the diagram in the content
                    adjusted_start = start_index + offset
                    adjusted_end = end_index + offset

                    # Create the replacement with proper markdown formatting
                    replacement = f"```mermaid\n{improved_diagram}\n```"

                    # Replace the diagram in the content
                    modified_content = (
                        modified_content[:adjusted_start] +
                        replacement +
                        modified_content[adjusted_end:]
                    )

                    # Update the offset
                    offset += len(replacement) - (adjusted_end - adjusted_start)

                # Save after each batch if there were changes
                if batch_modified and not self.dry_run:
                    with open(output_path, 'w', encoding='utf-8') as f:
                        f.write(modified_content)
                    logger.info(f"Saved batch {batch_start//self.batch_size + 1} with improvements to {output_path}")

            # Skip if the content didn't change
            if improved_count == 0:
                logger.info(f"No changes made to {file_path}")
                self.stats["unchanged_files"] += 1
                return True

            # Final save (in case we didn't save the last batch)
            if not self.dry_run:
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(modified_content)
                logger.info(f"Completed processing with {improved_count} improved diagrams in {file_path}")
            else:
                logger.info(f"[DRY RUN] Would save {improved_count} improved diagrams to {output_path}")

            self.stats["improved_files"] += 1
            return True

        except Exception as e:
            logger.error(f"Error processing file {file_path}: {e}")
            self.stats["failed_files"] += 1
            return False

    async def process_files(self, files: List[Path]) -> None:
        """Process multiple files in parallel.

        Args:
            files: List of file paths to process
        """
        logger.info(f"Processing {len(files)} files with max {self.max_concurrent_requests} concurrent requests")

        # Create a semaphore to limit concurrent file processing
        # This is different from rate limiting - it just limits how many files we process at once
        semaphore = asyncio.Semaphore(self.max_concurrent_requests)

        async def process_with_semaphore(file_path: Path) -> None:
            async with semaphore:
                success = await self.process_file(file_path)
                if success:
                    self.stats["processed_files"] += 1

        # Create tasks for all files
        tasks = []
        for file_path in files:
            # Create a task for each file
            task = asyncio.create_task(process_with_semaphore(file_path))
            tasks.append(task)

        # Wait for all tasks to complete
        await asyncio.gather(*tasks)

    async def run(self) -> Dict[str, int]:
        """Run the mermaid beautification process.

        Returns:
            Statistics about the process
        """
        logger.info(f"Starting mermaid beautification process")

        # Find mermaid files
        files = await self.find_mermaid_files()

        if not files:
            logger.warning(f"No mermaid files found in {self.input_dir}")
            return self.stats

        # Process files
        await self.process_files(files)

        # Log statistics
        logger.info(f"Mermaid beautification process completed")
        logger.info(f"Total files: {self.stats['total_files']}")
        logger.info(f"Processed files: {self.stats['processed_files']}")
        logger.info(f"Improved files: {self.stats['improved_files']}")
        logger.info(f"Unchanged files: {self.stats['unchanged_files']}")
        logger.info(f"Failed files: {self.stats['failed_files']}")

        return self.stats

    async def post_process_directory(self) -> Dict[str, int]:
        """Run only the post-processing on all mermaid diagrams in the directory.
        This skips the LLM beautification step and only applies the syntax fixes.

        Returns:
            Statistics about the process
        """
        logger.info(f"Starting mermaid post-processing")

        # Find mermaid files
        files = await self.find_mermaid_files()

        if not files:
            logger.warning(f"No mermaid files found in {self.input_dir}")
            return self.stats

        # Process each file
        for file_path in files:
            await self.post_process_file(file_path)

        # Log statistics
        logger.info(f"Mermaid post-processing completed")
        logger.info(f"Total files: {self.stats['total_files']}")
        logger.info(f"Processed files: {self.stats['processed_files']}")
        logger.info(f"Improved files: {self.stats['improved_files']}")
        logger.info(f"Unchanged files: {self.stats['unchanged_files']}")
        logger.info(f"Failed files: {self.stats['failed_files']}")

        return self.stats

    async def post_process_file(self, file_path: Path) -> bool:
        """Post-process a single file containing mermaid diagrams.
        This only applies the syntax fixes without LLM beautification.

        Args:
            file_path: Path to the file to process

        Returns:
            True if the file was successfully processed, False otherwise
        """
        logger.info(f"Post-processing file: {file_path}")

        try:
            # Read the file content
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Extract mermaid diagrams
            diagrams = self.extract_mermaid_diagrams(content)

            if not diagrams:
                logger.warning(f"No mermaid diagrams found in {file_path}")
                return False

            # Determine the output path
            output_path = file_path
            if self.output_dir:
                # Create relative path from input_dir
                rel_path = file_path.relative_to(self.input_dir)
                output_path = self.output_dir / rel_path

                # Create parent directories if they don't exist
                output_path.parent.mkdir(parents=True, exist_ok=True)

            # Process all diagrams
            modified_content = content
            offset = 0  # Offset to adjust indices after replacements
            improved_count = 0

            for diagram_id, diagram_content, start_index, end_index in diagrams:
                # Apply post-processing to the diagram
                improved_diagram = self._post_process_diagram(diagram_content)

                # Skip if the diagram didn't change
                if improved_diagram.strip() == diagram_content.strip():
                    logger.info(f"Diagram {diagram_id} in {file_path} is already optimized")
                    continue

                improved_count += 1

                # Replace the diagram in the content
                adjusted_start = start_index + offset
                adjusted_end = end_index + offset

                # Create the replacement with proper markdown formatting
                replacement = f"```mermaid\n{improved_diagram}\n```"

                # Replace the diagram in the content
                modified_content = (
                    modified_content[:adjusted_start] +
                    replacement +
                    modified_content[adjusted_end:]
                )

                # Update the offset
                offset += len(replacement) - (adjusted_end - adjusted_start)

            # Skip if the content didn't change
            if improved_count == 0:
                logger.info(f"No changes made to {file_path}")
                self.stats["unchanged_files"] += 1
                return True

            # Save the modified content
            if not self.dry_run:
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(modified_content)
                logger.info(f"Completed processing with {improved_count} improved diagrams in {file_path}")
            else:
                logger.info(f"[DRY RUN] Would save {improved_count} improved diagrams to {output_path}")

            self.stats["improved_files"] += 1
            self.stats["processed_files"] += 1
            return True

        except Exception as e:
            logger.error(f"Error processing file {file_path}: {e}")
            self.stats["failed_files"] += 1
            return False


async def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Beautify mermaid diagrams using Claude LLM")

    parser.add_argument(
        "--input_dir",
        type=str,
        required=True,
        help="Directory containing mermaid diagram files",
    )

    parser.add_argument(
        "--output_dir",
        type=str,
        help="Directory to save improved diagrams (if not specified, overwrites original files)",
    )

    parser.add_argument(
        "--claude_api_key",
        type=str,
        help="Anthropic API key (if not specified, will try to get from environment)",
    )

    parser.add_argument(
        "--claude_model",
        type=str,
        default="claude-3-7-sonnet-20250219",
        help="Claude model to use (default: claude-3-7-sonnet-20250219)",
    )

    parser.add_argument(
        "--max_tokens",
        type=int,
        default=8192,
        help="Maximum tokens to generate (default: 8192)",
    )

    parser.add_argument(
        "--temperature",
        type=float,
        default=0.7,
        help="Sampling temperature (default: 0.7)",
    )

    parser.add_argument(
        "--rate_limit",
        type=int,
        default=2,
        help="Number of API calls allowed per rate period (default: 2)",
    )

    parser.add_argument(
        "--rate_period",
        type=int,
        default=120,
        help="Time period in seconds for rate limiting (default: 120)",
    )

    parser.add_argument(
        "--max_concurrent",
        type=int,
        default=5,
        help="Maximum number of concurrent API requests (default: 5)",
    )

    parser.add_argument(
        "--batch_size",
        type=int,
        default=10,
        help="Number of diagrams to process in a batch before saving (default: 10)",
    )

    parser.add_argument(
        "--dry_run",
        action="store_true",
        help="Don't actually save changes, just log what would be done",
    )

    parser.add_argument(
        "--post_process_only",
        action="store_true",
        help="Only apply post-processing fixes without LLM beautification",
    )

    args = parser.parse_args()

    # Initialize the mermaid beautifier
    beautifier = MermaidBeautifier(
        input_dir=args.input_dir,
        output_dir=args.output_dir,
        claude_api_key=args.claude_api_key,
        claude_model=args.claude_model,
        max_tokens=args.max_tokens,
        temperature=args.temperature,
        rate_limit=args.rate_limit,
        rate_period=args.rate_period,
        max_concurrent_requests=args.max_concurrent,
        batch_size=args.batch_size,
        dry_run=args.dry_run,
    )

    # Run the appropriate process
    if args.post_process_only:
        # Run only the post-processing
        await beautifier.post_process_directory()
    else:
        # Run the full beautification process
        await beautifier.run()


if __name__ == "__main__":
    asyncio.run(main())
