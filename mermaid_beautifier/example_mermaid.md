# Example Mermaid Diagrams

This file contains example mermaid diagrams that can be used to test the mermaid beautifier.

## Basic Flowchart

```mermaid
graph TD
A[Start] --> B{Is it working?}
B -->|Yes| C[Great!]
B -->|No| D[Debug]
C --> E[Continue]
D --> F[Fix]
F --> B
```

## Function Call Diagram

```mermaid
graph TD
main_function --> initialize_app
initialize_app --> load_config
initialize_app --> setup_logging
main_function --> process_data
process_data --> validate_input
process_data --> transform_data
process_data --> save_results
main_function --> cleanup_resources
```

## Class Diagram

```mermaid
classDiagram
    class Animal {
        +name: string
        +age: int
        +makeSound(): void
    }
    class Dog {
        +breed: string
        +bark(): void
    }
    class Cat {
        +color: string
        +meow(): void
    }
    Animal <|-- Dog
    Animal <|-- Cat
```

## Complex System Diagram

```mermaid
graph TD
UserInterface --> APIGateway
APIGateway --> AuthService
APIGateway --> UserService
APIGateway --> ContentService
AuthService --> UserDatabase
UserService --> UserDatabase
ContentService --> ContentDatabase
ContentService --> StorageService
StorageService --> S3Storage
AuthService --> TokenValidator
TokenValidator --> CacheService
CacheService --> RedisCache
```
