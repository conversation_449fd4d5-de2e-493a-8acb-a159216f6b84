.pnpm-store
dist
out
out-*
node_modules
coverage/

.DS_Store

# Builds
bin/
roo-cline-*.vsix

# Local prompts and rules
/local-prompts

# Test environment
.test_env
.vscode-test/

# Docs
docs/_site/

# Dotenv
.env
.env.*
!.env.*.sample


#Local lint config
.eslintrc.local.json

#Logging
logs

# Vite development
.vite-port


__pycache__/*
*/__pycache__/*
__pycache__/
*.pyc
*.pyo
bracket_core/__pycache__/*
bracket_core/*.pyc

function_descriptions.jsonl
function_requests.jsonl


artifacts/*

bracket_core/localisation/qa_data


/experiments/*
/experiments/setup_exp
/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments


/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/reworking_mermaid/*
/reworking_mermaid/*


bracket_core/exp_repomap/exp_results/*
bracket_core/exp_repomap/output/*
bracket_core/exp_repomap/optim_output/*

bracket_core/bracket_irl/outputs/*

e2e_test_venv/*

cyclonedx-cli.zip
cyclonedx-cli.tar.gz