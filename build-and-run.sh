#!/bin/bash

# Build the extension
echo "Building the extension..."
npm run build:esbuild

# Create a VSIX package
echo "Creating VSIX package..."
npm run vsix

# Get the version from package.json
VERSION=$(node -p "require('./package.json').version")

# Install the extension
echo "Installing the extension..."
code --install-extension bin/roo-cline-$VERSION.vsix

echo "Extension built and installed!"
echo "Please reload VS Code window with: Ctrl+Shift+P (or Cmd+Shift+P on Mac) → 'Developer: Reload Window'"
