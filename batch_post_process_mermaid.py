#!/usr/bin/env python3
"""
Batch Mermaid Diagram Post-Processor

This script applies syntax fixes to mermaid diagrams in multiple directories without using LLM beautification.
It processes all subdirectories in the input directory and saves the results to the output directory.

Usage:
    python batch_post_process_mermaid.py --input_dir /path/to/parent/dir --output_dir /path/to/output
"""

import os
import logging
import argparse
import asyncio
from pathlib import Path

from mermaid_beautifier.mermaid_beautifier import MermaidBeautifier

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def process_directory(input_dir: Path, output_dir: Path, dry_run: bool = False) -> dict:
    """Process a single directory of mermaid diagrams.
    
    Args:
        input_dir: Directory containing mermaid diagram files
        output_dir: Directory to save processed diagrams
        dry_run: If True, don't actually save changes
        
    Returns:
        Statistics about the processing
    """
    logger.info(f"Processing directory: {input_dir}")
    
    # Initialize the mermaid beautifier
    beautifier = MermaidBeautifier(
        input_dir=str(input_dir),
        output_dir=str(output_dir),
        dry_run=dry_run,
    )
    
    # Run only the post-processing
    stats = await beautifier.post_process_directory()
    
    return stats

async def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Batch post-process mermaid diagrams to fix syntax issues")

    parser.add_argument(
        "--input_dir",
        type=str,
        required=True,
        help="Parent directory containing subdirectories with mermaid diagrams",
    )

    parser.add_argument(
        "--output_dir",
        type=str,
        required=True,
        help="Directory to save processed diagrams",
    )

    parser.add_argument(
        "--dry_run",
        action="store_true",
        help="Don't actually save changes, just log what would be done",
    )

    args = parser.parse_args()
    
    input_dir = Path(args.input_dir)
    output_dir = Path(args.output_dir)
    
    # Create output directory if it doesn't exist
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Get all subdirectories in the input directory
    subdirs = [d for d in input_dir.iterdir() if d.is_dir()]
    
    if not subdirs:
        logger.warning(f"No subdirectories found in {input_dir}")
        return
    
    logger.info(f"Found {len(subdirs)} subdirectories to process")
    
    # Process each subdirectory
    total_stats = {
        "total_files": 0,
        "processed_files": 0,
        "improved_files": 0,
        "unchanged_files": 0,
        "failed_files": 0,
    }
    
    for subdir in subdirs:
        # Create corresponding output subdirectory
        subdir_output = output_dir / subdir.name
        subdir_output.mkdir(parents=True, exist_ok=True)
        
        # Process the subdirectory
        stats = await process_directory(subdir, subdir_output, args.dry_run)
        
        # Update total stats
        for key in total_stats:
            total_stats[key] += stats[key]
    
    # Print summary
    print("\nBatch Processing Summary:")
    print(f"Directories processed: {len(subdirs)}")
    print(f"Total files: {total_stats['total_files']}")
    print(f"Processed files: {total_stats['processed_files']}")
    print(f"Improved files: {total_stats['improved_files']}")
    print(f"Unchanged files: {total_stats['unchanged_files']}")
    print(f"Failed files: {total_stats['failed_files']}")

if __name__ == "__main__":
    asyncio.run(main())
