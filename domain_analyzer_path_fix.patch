--- bracket_core/bracket_irl/microservices/domain-analyzer-service/src/services/analyzer.py.orig
+++ bracket_core/bracket_irl/microservices/domain-analyzer-service/src/services/analyzer.py
@@ -200,8 +200,15 @@
                 # Try different methods to get full path
                 if hasattr(self.storage_client, '_get_full_path'):
                     full_output_dir = self.storage_client._get_full_path(output_dir)
+                    
+                    # Special case for absolute paths starting with /app/data/
+                    if repomap_path.startswith('/app/data/'):
+                        # Use the path directly without prepending /app/data/ again
+                        full_repomap_path = repomap_path
+                        full_output_path = self.storage_client._get_full_path(output_path)
+                    else:
+                        full_repomap_path = self.storage_client._get_full_path(repomap_path)
+                        full_output_path = self.storage_client._get_full_path(output_path)
                 elif hasattr(self.storage_client, 'get_full_path'):
                     full_repomap_path = self.storage_client.get_full_path(repomap_path)
                     full_output_path = self.storage_client.get_full_path(output_path)
