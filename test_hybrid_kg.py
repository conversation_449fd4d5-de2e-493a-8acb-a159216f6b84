#!/usr/bin/env python3

import os
import sys
import logging
from bracket_core.hybrid_kg import HybridKnowledgeGraph, graph_to_dataframes

# Configure logging
logging.basicConfig(level=logging.INFO)

def main():
    # Get the current directory
    current_dir = os.path.dirname(os.path.abspath(__file__))

    # Create a custom instance to only process Ruby files
    print(f"Generating knowledge graph for {current_dir}...")
    kg = HybridKnowledgeGraph(current_dir, verbose=True)
    # Only process Ruby files
    kg.supported_languages = {
        ".rb": "ruby"
    }
    graph = kg.generate_graph()

    # Convert to dataframe
    df = graph_to_dataframes(graph)

    # Print the functions found
    print("\nFunctions found:")
    for _, row in df.iterrows():
        print(f"{row['node_id']} - {row['signature']}")
        if row['calls']:
            print(f"  Calls: {', '.join(row['calls'])}")
        print()

if __name__ == "__main__":
    main()
