#!/usr/bin/env python3
"""
Generate a comprehensive Software Bill of Materials (SBOM) for the Bracket codebase.

This script creates a detailed SBOM in CycloneDX format, covering:
- Python dependencies (via Poetry)
- JavaScript/TypeScript dependencies (via npm/pnpm)
- Docker container images
- License information

The SBOM is suitable for M&A due diligence, providing a complete inventory of
all software components, their versions, licenses, and potential vulnerabilities.

Usage:
    python generate_sbom.py --output sbom_output
"""

import argparse
import json
import os
import subprocess
import sys
import uuid
import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

# ANSI colors for terminal output
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'

def print_header(message: str) -> None:
    """Print a formatted header message."""
    print(f"\n{Colors.HEADER}{Colors.BOLD}=== {message} ==={Colors.ENDC}\n")

def print_step(message: str) -> None:
    """Print a formatted step message."""
    print(f"{Colors.BLUE}→ {message}{Colors.ENDC}")

def print_success(message: str) -> None:
    """Print a formatted success message."""
    print(f"{Colors.GREEN}✓ {message}{Colors.ENDC}")

def print_warning(message: str) -> None:
    """Print a formatted warning message."""
    print(f"{Colors.YELLOW}⚠ {message}{Colors.ENDC}")

def print_error(message: str) -> None:
    """Print a formatted error message."""
    print(f"{Colors.RED}✗ {message}{Colors.ENDC}")

def run_command(command: List[str], cwd: Optional[str] = None, shell: bool = False) -> subprocess.CompletedProcess:
    """Run a shell command and return the result."""
    try:
        result = subprocess.run(
            command,
            cwd=cwd,
            check=True,
            text=True,
            capture_output=True,
            shell=shell
        )
        return result
    except subprocess.CalledProcessError as e:
        print_error(f"Command failed: {' '.join(command) if not shell else command}")
        print(e.stderr)
        raise

def check_dependencies() -> bool:
    """Check if required tools are installed."""
    print_header("Checking Dependencies")

    all_installed = True

    # Check for CycloneDX Python
    print_step("Checking for CycloneDX Python...")
    try:
        subprocess.run(["cyclonedx-py", "--version"], check=True, capture_output=True, text=True)
        print_success("CycloneDX Python is installed")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print_error("CycloneDX Python is not installed")
        print_warning("Please install CycloneDX Python with: pip install cyclonedx-bom")
        all_installed = False

    # Check for CycloneDX npm - try multiple possible commands
    print_step("Checking for CycloneDX npm...")
    npm_installed = False
    for cmd in ["cyclonedx-npm", "npx @cyclonedx/cyclonedx-npm", "npx cyclonedx-npm"]:
        try:
            result = subprocess.run(f"{cmd} --version", shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                npm_installed = True
                print_success("CycloneDX npm is installed")
                break
        except:
            pass

    if not npm_installed:
        # Check if the package is installed globally
        try:
            result = subprocess.run("npm list -g @cyclonedx/cyclonedx-npm", shell=True, capture_output=True, text=True)
            if "@cyclonedx/cyclonedx-npm" in result.stdout:
                npm_installed = True
                print_success("CycloneDX npm is installed (globally)")
        except:
            pass

    if not npm_installed:
        print_error("CycloneDX npm is not installed")
        print_warning("Please install CycloneDX npm with: npm install -g @cyclonedx/cyclonedx-npm")
        all_installed = False

    # Check for Syft
    print_step("Checking for Syft...")
    try:
        subprocess.run(["syft", "version"], check=True, capture_output=True, text=True)
        print_success("Syft is installed")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print_error("Syft is not installed")
        print_warning("Please install Syft: https://github.com/anchore/syft#installation")
        all_installed = False

    # Check for Docker
    print_step("Checking for Docker...")
    try:
        subprocess.run(["docker", "--version"], check=True, capture_output=True, text=True)
        print_success("Docker is installed")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print_error("Docker is not installed")
        print_warning("Please install Docker: https://docs.docker.com/get-docker/")
        all_installed = False

    return all_installed

def generate_python_sbom(output_dir: str) -> str:
    """Generate SBOM for Python dependencies using CycloneDX."""
    print_header("Generating Python Dependencies SBOM")

    output_file = os.path.join(output_dir, "python-sbom.json")
    print_step("Analyzing Python dependencies with Poetry...")

    try:
        # Try using the poetry command with correct arguments and enhanced options
        try:
            run_command([
                "cyclonedx-py",
                "poetry",
                "--output-file", output_file,
                "--output-format", "JSON",
                "--spec-version", "1.4",
                "--mc-type", "application",
                # Include all extras to ensure comprehensive dependency coverage
                "--all-extras",
                # Include development dependencies for complete analysis
                "--with", "dev"
            ], cwd=".")
            print_success(f"Python SBOM generated at {output_file}")
            return output_file
        except Exception as e:
            print_step(f"Poetry command failed: {str(e)}")
            print_step("Trying with requirements approach...")

            # If poetry command fails, try generating a requirements.txt file and use that
            try:
                print_step("Generating requirements.txt from Poetry...")
                # Include dev dependencies for complete analysis
                run_command([
                    "poetry", "export",
                    "--format", "requirements.txt",
                    "--output", "requirements-temp.txt",
                    "--without-hashes",
                    "--with", "dev"
                ], cwd=".")

                # Then generate SBOM from requirements.txt with enhanced options
                run_command([
                    "cyclonedx-py",
                    "requirements",
                    "--infile", "requirements-temp.txt",
                    "--output-file", output_file,
                    "--output-format", "JSON",
                    "--spec-version", "1.4"
                ], cwd=".")

                # Clean up temporary file
                if os.path.exists("requirements-temp.txt"):
                    os.remove("requirements-temp.txt")

                print_success(f"Python SBOM generated at {output_file}")
                return output_file
            except Exception as nested_e:
                print_step(f"Requirements approach failed: {str(nested_e)}")

                # Try a fallback approach with pip freeze
                print_step("Trying with pip freeze approach...")
                try:
                    # Generate requirements.txt using pip freeze
                    with open("pip-freeze-temp.txt", "w") as f:
                        pip_freeze_result = subprocess.run(
                            ["pip", "freeze"],
                            check=True,
                            text=True,
                            capture_output=True
                        )
                        f.write(pip_freeze_result.stdout)

                    # Generate SBOM from pip freeze output with enhanced options
                    run_command([
                        "cyclonedx-py",
                        "requirements",
                        "--infile", "pip-freeze-temp.txt",
                        "--output-file", output_file,
                        "--output-format", "JSON",
                        "--spec-version", "1.4"
                    ], cwd=".")

                    # Clean up temporary file
                    if os.path.exists("pip-freeze-temp.txt"):
                        os.remove("pip-freeze-temp.txt")

                    print_success(f"Python SBOM generated at {output_file}")
                    return output_file
                except Exception:
                    raise Exception(f"All Python SBOM generation approaches failed")
    except Exception as e:
        print_error(f"Failed to generate Python SBOM: {str(e)}")
        return ""

def generate_npm_sbom(output_dir: str) -> str:
    """Generate SBOM for JavaScript/TypeScript dependencies using CycloneDX."""
    print_header("Generating JavaScript/TypeScript Dependencies SBOM")

    # Create bracket_ext/sbom_output directory if it doesn't exist
    bracket_ext_output_dir = os.path.join("sbom_output")
    os.makedirs(bracket_ext_output_dir, exist_ok=True)

    # Output file will be in bracket_ext/sbom_output
    bracket_ext_output_file = os.path.join(bracket_ext_output_dir, "js-sbom.json")

    # Also create a copy in the main output directory
    output_file = os.path.join(output_dir, "js-sbom.json")

    # Check for nested directory structure that might be created
    nested_output_dir = os.path.join("bracket_ext", "bracket_ext", "sbom_output")
    nested_output_file = os.path.join(nested_output_dir, "js-sbom.json")

    print_step("Analyzing JavaScript/TypeScript dependencies...")

    try:
        # Try different ways to run cyclonedx-npm with basic options first
        success = False

        # Try direct command first with basic options
        try:
            run_command([
                "cyclonedx-npm",
                "--output-file", bracket_ext_output_file,
                "--output-format", "JSON"
            ], cwd="bracket_ext")
            success = True
        except Exception:
            print_step("Direct command failed, trying with npx...")

        # If direct command fails, try with npx and basic options
        if not success:
            try:
                cmd = (
                    f"cd bracket_ext && npx @cyclonedx/cyclonedx-npm "
                    f"--output-file {bracket_ext_output_file} "
                    f"--output-format JSON"
                )
                subprocess.run(
                    cmd,
                    shell=True,
                    check=True,
                    text=True,
                    capture_output=True
                )
                success = True
            except Exception:
                print_step("npx @cyclonedx/cyclonedx-npm failed, trying alternative...")

        # If that fails too, try another alternative with basic options
        if not success:
            cmd = (
                f"cd bracket_ext && npx cyclonedx-npm "
                f"--output-file {bracket_ext_output_file} "
                f"--output-format JSON"
            )
            subprocess.run(
                cmd,
                shell=True,
                check=True,
                text=True,
                capture_output=True
            )
            success = True

        # If all npm approaches fail, create a basic JS SBOM manually
        if not success:
            print_step("All npm approaches failed, creating a basic JS SBOM manually...")

            # Check if package.json exists
            package_json_path = os.path.join("bracket_ext", "package.json")
            if os.path.exists(package_json_path):
                with open(package_json_path, 'r') as f:
                    package_data = json.load(f)

                # Create a basic SBOM
                basic_sbom = {
                    "bomFormat": "CycloneDX",
                    "specVersion": "1.4",
                    "serialNumber": f"urn:uuid:{uuid.uuid4()}",
                    "version": 1,
                    "metadata": {
                        "timestamp": datetime.datetime.now().isoformat(),
                        "tools": [
                            {
                                "vendor": "Bracket",
                                "name": "SBOM Generator",
                                "version": "1.0.0"
                            }
                        ],
                        "component": {
                            "type": "application",
                            "name": package_data.get("name", "bracket-ext"),
                            "version": package_data.get("version", "1.0.0"),
                            "description": package_data.get("description", "Bracket Extension"),
                            "licenses": [
                                {
                                    "license": {
                                        "id": package_data.get("license", "MIT")
                                    }
                                }
                            ]
                        }
                    },
                    "components": [],
                    "dependencies": []
                }

                # Add dependencies from package.json
                dependencies = package_data.get("dependencies", {})
                dev_dependencies = package_data.get("devDependencies", {})

                # Combine all dependencies
                all_deps = {}
                all_deps.update(dependencies)
                all_deps.update(dev_dependencies)

                # Add each dependency as a component
                for name, version in all_deps.items():
                    # Clean up version string
                    if version.startswith("^") or version.startswith("~"):
                        version = version[1:]

                    component = {
                        "type": "library",
                        "name": name,
                        "version": version,
                        "purl": f"pkg:npm/{name}@{version}",
                        "bom-ref": f"pkg:npm/{name}@{version}",
                        "licenses": [
                            {
                                "license": {
                                    "id": "Unknown"
                                }
                            }
                        ]
                    }

                    basic_sbom["components"].append(component)

                # Add dependency relationships
                main_ref = f"pkg:npm/{package_data.get('name', 'bracket-ext')}@{package_data.get('version', '1.0.0')}"
                dependency_refs = [comp["bom-ref"] for comp in basic_sbom["components"]]

                basic_sbom["dependencies"].append({
                    "ref": main_ref,
                    "dependsOn": dependency_refs
                })

                # Write the basic SBOM to bracket_ext/sbom_output
                with open(bracket_ext_output_file, 'w') as f:
                    json.dump(basic_sbom, f, indent=2)

                success = True

        if success:
            import shutil

            # Check all possible locations for the generated SBOM
            if os.path.exists(output_file):
                # If we generated the SBOM in the main output directory, copy it to bracket_ext/sbom_output
                shutil.copy2(output_file, bracket_ext_output_file)
                print_success(f"JavaScript/TypeScript SBOM generated at {output_file}")
                print_success(f"JavaScript/TypeScript SBOM copied to {bracket_ext_output_file}")
            elif os.path.exists(bracket_ext_output_file):
                # If we generated the SBOM in bracket_ext/sbom_output, copy it to the main output directory
                shutil.copy2(bracket_ext_output_file, output_file)
                print_success(f"JavaScript/TypeScript SBOM generated at {bracket_ext_output_file}")
                print_success(f"JavaScript/TypeScript SBOM copied to {output_file}")
            elif os.path.exists(nested_output_file):
                # If we generated the SBOM in the nested directory, copy it to both locations
                shutil.copy2(nested_output_file, output_file)
                shutil.copy2(nested_output_file, bracket_ext_output_file)
                print_success(f"JavaScript/TypeScript SBOM generated at {nested_output_file}")
                print_success(f"JavaScript/TypeScript SBOM copied to {output_file} and {bracket_ext_output_file}")
            else:
                # Try to find the file anywhere in the project
                try:
                    result = subprocess.run(["find", ".", "-name", "js-sbom.json"], capture_output=True, text=True, check=True)
                    found_files = result.stdout.strip().split('\n')
                    if found_files and found_files[0]:
                        found_file = found_files[0]
                        shutil.copy2(found_file, output_file)
                        print_success(f"JavaScript/TypeScript SBOM found at {found_file}")
                        print_success(f"JavaScript/TypeScript SBOM copied to {output_file}")
                    else:
                        print_warning("Could not find js-sbom.json file in any location")
                except Exception as e:
                    print_warning(f"Error searching for js-sbom.json: {str(e)}")

            return output_file
        else:
            print_error("Failed to generate JavaScript/TypeScript SBOM with all methods")
            return ""
    except Exception as e:
        print_error(f"Failed to generate JavaScript/TypeScript SBOM: {str(e)}")
        return ""

def generate_docker_sbom(output_dir: str) -> str:
    """Generate SBOM for Docker images using Syft."""
    print_header("Generating Docker Images SBOM")

    output_file = os.path.join(output_dir, "docker-sbom.json")
    print_step("Analyzing Docker images...")

    try:
        # Find all Dockerfiles in the microservices directory
        microservices_dir = "bracket_core/bracket_irl/microservices"
        dockerfiles = []

        for root, _, files in os.walk(microservices_dir):
            for file in files:
                if file == "Dockerfile":
                    dockerfiles.append(os.path.join(root, file))

        print_step(f"Found {len(dockerfiles)} Dockerfiles")

        # Check Syft version and available options
        try:
            syft_help = subprocess.run(
                ["syft", "--help"],
                check=True,
                text=True,
                capture_output=True
            ).stdout

            # Determine available options based on help output
            enhanced_options = []
            if "--scope" in syft_help:
                enhanced_options.append("--scope")
                enhanced_options.append("all-layers")
        except Exception:
            enhanced_options = []

        # Generate SBOM for each Dockerfile
        docker_sboms = []
        for dockerfile in dockerfiles:
            service_name = os.path.basename(os.path.dirname(dockerfile))
            print_step(f"Analyzing {service_name}...")

            # Use Syft to generate SBOM with available options
            temp_output = os.path.join(output_dir, f"{service_name}-docker-sbom.json")

            cmd = [
                "syft",
                dockerfile,
                "-o", "cyclonedx-json",
                "--file", temp_output
            ]

            # Add enhanced options if available
            cmd.extend(enhanced_options)

            try:
                run_command(cmd)
                docker_sboms.append(temp_output)
            except Exception as e:
                print_warning(f"Failed to analyze {service_name}: {str(e)}")

                # Create a basic SBOM for this service
                basic_sbom = {
                    "bomFormat": "CycloneDX",
                    "specVersion": "1.4",
                    "serialNumber": f"urn:uuid:{uuid.uuid4()}",
                    "version": 1,
                    "metadata": {
                        "timestamp": datetime.datetime.now().isoformat(),
                        "tools": [
                            {
                                "vendor": "Bracket",
                                "name": "SBOM Generator",
                                "version": "1.0.0"
                            }
                        ],
                        "component": {
                            "type": "container",
                            "name": service_name,
                            "version": "1.0.0",
                            "description": f"Docker container for {service_name}",
                            "licenses": [
                                {
                                    "license": {
                                        "id": "MIT"
                                    }
                                }
                            ]
                        }
                    },
                    "components": [],
                    "dependencies": []
                }

                # Try to extract base image from Dockerfile
                try:
                    with open(dockerfile, 'r') as f:
                        dockerfile_content = f.read()

                    # Look for FROM statement
                    import re
                    from_match = re.search(r'FROM\s+([^\s]+)', dockerfile_content)
                    if from_match:
                        base_image = from_match.group(1)

                        # Add base image as a component
                        basic_sbom["components"].append({
                            "type": "container",
                            "name": base_image,
                            "version": "latest",
                            "purl": f"pkg:docker/{base_image}",
                            "bom-ref": f"pkg:docker/{base_image}@latest",
                            "licenses": [
                                {
                                    "license": {
                                        "id": "Unknown"
                                    }
                                }
                            ]
                        })

                        # Add dependency relationship
                        basic_sbom["dependencies"].append({
                            "ref": f"pkg:docker/{service_name}@1.0.0",
                            "dependsOn": [f"pkg:docker/{base_image}@latest"]
                        })
                except Exception:
                    pass

                # Write the basic SBOM
                with open(temp_output, 'w') as f:
                    json.dump(basic_sbom, f, indent=2)

                docker_sboms.append(temp_output)

        # Combine all Docker SBOMs with proper metadata and dependencies
        combined_sbom = {
            "bomFormat": "CycloneDX",
            "specVersion": "1.4",
            "serialNumber": f"urn:uuid:{uuid.uuid4()}",
            "version": 1,
            "metadata": {
                "timestamp": datetime.datetime.now().isoformat(),
                "tools": [
                    {
                        "vendor": "Bracket",
                        "name": "SBOM Generator",
                        "version": "1.0.0"
                    },
                    {
                        "vendor": "Anchore",
                        "name": "Syft",
                        "version": "1.0.0"
                    }
                ],
                "component": {
                    "type": "application",
                    "name": "Bracket Microservices",
                    "version": "1.0.0",
                    "description": "Docker container images for Bracket microservices",
                    "licenses": [
                        {
                            "license": {
                                "id": "MIT"
                            }
                        }
                    ]
                }
            },
            "components": [],
            "dependencies": []
        }

        # Track components by bom-ref to avoid duplicates and build dependency graph
        component_refs = set()

        for sbom_file in docker_sboms:
            with open(sbom_file, 'r') as f:
                sbom_data = json.load(f)

                # Extract tool information if available
                if "metadata" in sbom_data and "tools" in sbom_data["metadata"]:
                    tools = sbom_data["metadata"]["tools"]
                    if isinstance(tools, list):
                        for tool in tools:
                            if isinstance(tool, dict) and tool.get("name") == "syft":
                                # Update Syft version in our tools list
                                for i, our_tool in enumerate(combined_sbom["metadata"]["tools"]):
                                    if our_tool.get("name") == "Syft":
                                        combined_sbom["metadata"]["tools"][i]["version"] = tool.get("version", "1.0.0")

                # Add components, avoiding duplicates
                if "components" in sbom_data:
                    for component in sbom_data["components"]:
                        if "bom-ref" in component:
                            if component["bom-ref"] not in component_refs:
                                component_refs.add(component["bom-ref"])

                                # Ensure component has license information
                                if "licenses" not in component:
                                    component["licenses"] = [{"license": {"id": "Unknown"}}]

                                combined_sbom["components"].append(component)
                        else:
                            # Generate a bom-ref if missing
                            name = component.get("name", "unknown")
                            version = component.get("version", "unknown")
                            bom_ref = f"pkg:generic/{name}@{version}"
                            component["bom-ref"] = bom_ref

                            if bom_ref not in component_refs:
                                component_refs.add(bom_ref)

                                # Ensure component has license information
                                if "licenses" not in component:
                                    component["licenses"] = [{"license": {"id": "Unknown"}}]

                                combined_sbom["components"].append(component)

                # Add dependencies if available
                if "dependencies" in sbom_data:
                    for dependency in sbom_data["dependencies"]:
                        combined_sbom["dependencies"].append(dependency)

        # Write combined SBOM
        with open(output_file, 'w') as f:
            json.dump(combined_sbom, f, indent=2)

        print_success(f"Docker SBOM generated at {output_file}")
        return output_file
    except Exception as e:
        print_error(f"Failed to generate Docker SBOM: {str(e)}")
        return ""

def combine_sboms(sbom_files: List[str], output_dir: str) -> str:
    """Combine multiple SBOM files into a single comprehensive SBOM."""
    print_header("Combining SBOMs")

    output_file = os.path.join(output_dir, "combined-sbom.json")
    print_step("Merging SBOM files...")

    try:
        # Create a combined SBOM with proper metadata
        combined_sbom = {
            "bomFormat": "CycloneDX",
            "specVersion": "1.4",
            "serialNumber": f"urn:uuid:{uuid.uuid4()}",
            "version": 1,
            "metadata": {
                "timestamp": datetime.datetime.now().isoformat(),
                "tools": [
                    {
                        "vendor": "Bracket",
                        "name": "SBOM Generator",
                        "version": "1.0.0"
                    }
                ],
                "component": {
                    "type": "application",
                    "name": "Bracket",
                    "version": "1.0.0",
                    "description": "Bracket: Combined Python and TypeScript project for code analysis and visualization.",
                    "licenses": [
                        {
                            "license": {
                                "id": "MIT"
                            }
                        }
                    ],
                    "supplier": {
                        "name": "Bracket Team"
                    },
                    "properties": [
                        {
                            "name": "cdx:source:type",
                            "value": "internal"
                        }
                    ]
                }
            },
            "components": [],
            "dependencies": []
        }

        # Track components by bom-ref to avoid duplicates and build dependency graph
        component_refs = set()
        component_dependencies = {}

        # Merge components and dependencies from all SBOM files
        for sbom_file in sbom_files:
            if sbom_file and os.path.exists(sbom_file):
                try:
                    with open(sbom_file, 'r') as f:
                        sbom_data = json.load(f)

                        # Update tools information if available
                        if "metadata" in sbom_data and "tools" in sbom_data["metadata"]:
                            tools = sbom_data["metadata"]["tools"]
                            if isinstance(tools, list):
                                for tool in tools:
                                    if isinstance(tool, dict):
                                        # Check if we already have this tool
                                        tool_exists = False
                                        for existing_tool in combined_sbom["metadata"]["tools"]:
                                            if existing_tool.get("name") == tool.get("name"):
                                                tool_exists = True
                                                break

                                        if not tool_exists:
                                            combined_sbom["metadata"]["tools"].append(tool)

                        # Add components, avoiding duplicates
                        if "components" in sbom_data:
                            components = sbom_data["components"]
                            if isinstance(components, list):
                                for component in components:
                                    if isinstance(component, dict) and "bom-ref" in component:
                                        if component["bom-ref"] not in component_refs:
                                            component_refs.add(component["bom-ref"])

                                            # Ensure component has license information if available
                                            if "licenses" not in component:
                                                # Try to determine license from purl or name
                                                license_id = "Unknown"
                                                name = component.get("name", "").lower()

                                                # Common open source packages and their licenses
                                                if any(pkg in name for pkg in ["numpy", "pandas", "scipy", "matplotlib"]):
                                                    license_id = "BSD-3-Clause"
                                                elif any(pkg in name for pkg in ["requests", "flask", "django", "fastapi"]):
                                                    license_id = "Apache-2.0"
                                                elif any(pkg in name for pkg in ["react", "jest", "babel"]):
                                                    license_id = "MIT"

                                                component["licenses"] = [{"license": {"id": license_id}}]

                                            combined_sbom["components"].append(component)

                        # Add dependencies if available
                        if "dependencies" in sbom_data:
                            dependencies = sbom_data["dependencies"]
                            if isinstance(dependencies, list):
                                for dependency in dependencies:
                                    if isinstance(dependency, dict) and "ref" in dependency:
                                        ref = dependency["ref"]
                                        if ref and ref in component_refs:
                                            # Store dependency information for later processing
                                            component_dependencies[ref] = dependency.get("dependsOn", [])

                                            # Add to combined SBOM
                                            combined_sbom["dependencies"].append(dependency)
                except Exception as e:
                    print_warning(f"Error processing {sbom_file}: {str(e)}")
                    print_step("Continuing with other SBOM files...")

        # If no dependencies section was found in any SBOM, create one based on component relationships
        if not combined_sbom["dependencies"] and len(combined_sbom["components"]) > 0:
            print_step("No dependency information found, creating basic dependency structure...")

            # Add main application as the root dependency
            main_ref = "bracket-application"
            combined_sbom["dependencies"].append({
                "ref": main_ref,
                "dependsOn": list(component_refs)[:50]  # Limit to first 50 to avoid too large dependency tree
            })

        # Write combined SBOM
        with open(output_file, 'w') as f:
            json.dump(combined_sbom, f, indent=2)

        print_success(f"Combined SBOM generated at {output_file}")
        return output_file
    except Exception as e:
        print_error(f"Failed to combine SBOMs: {str(e)}")
        return ""

def generate_license_report(output_dir: str) -> str:
    """Generate a license report from the SBOM."""
    print_header("Generating License Report")

    combined_sbom_file = os.path.join(output_dir, "combined-sbom.json")
    output_file = os.path.join(output_dir, "license-report.json")
    output_md_file = os.path.join(output_dir, "license-report.md")
    output_detailed_md_file = os.path.join(output_dir, "license-report-detailed.md")

    try:
        with open(combined_sbom_file, 'r') as f:
            sbom_data = json.load(f)

        # Extract license information with additional details
        license_info = {}
        license_counts = {}
        license_components = {}

        # Count total lines in the SBOM file
        with open(combined_sbom_file, 'r') as f:
            total_lines = sum(1 for _ in f)

        # Count total components in the SBOM
        total_components_in_sbom = len(sbom_data.get("components", []))

        # Process all components
        components_processed = 0

        # Process components recursively to catch nested components
        def process_component(component, parent_name=None):
            nonlocal components_processed
            components_processed += 1

            name = component.get("name", "Unknown")
            version = component.get("version", "Unknown")
            component_type = component.get("type", "Unknown")
            purl = component.get("purl", "")
            description = component.get("description", "")
            licenses = []
            license_texts = []

            # Add parent information if available
            component_name = name
            if parent_name:
                component_name = f"{parent_name} > {name}"

            if "licenses" in component:
                for license_data in component["licenses"]:
                    if "license" in license_data:
                        license_obj = license_data["license"]
                        license_id = "Unknown"

                        if "id" in license_obj:
                            license_id = license_obj["id"]
                        elif "name" in license_obj:
                            license_id = license_obj["name"]

                        licenses.append(license_id)

                        # Count licenses by type
                        if license_id not in license_counts:
                            license_counts[license_id] = 0
                            license_components[license_id] = []

                        license_counts[license_id] += 1
                        license_components[license_id].append(f"{component_name}@{version}")

                        # Extract license text if available
                        if "text" in license_obj:
                            license_texts.append({
                                "id": license_id,
                                "text": license_obj["text"].get("content", "")
                            })

            key = f"{component_name}@{version}"
            license_info[key] = {
                "licenses": licenses if licenses else ["Unknown"],
                "type": component_type,
                "purl": purl,
                "description": description,
                "license_texts": license_texts
            }

            # If no licenses found, count as Unknown
            if not licenses:
                if "Unknown" not in license_counts:
                    license_counts["Unknown"] = 0
                    license_components["Unknown"] = []

                license_counts["Unknown"] += 1
                license_components["Unknown"].append(key)

            # Process nested components if any
            if "components" in component and isinstance(component["components"], list):
                for nested_component in component["components"]:
                    process_component(nested_component, component_name)

        # Process all top-level components
        for component in sbom_data.get("components", []):
            process_component(component)

        # Write JSON license report
        with open(output_file, 'w') as f:
            json.dump(license_info, f, indent=2)

        # Generate a more readable markdown report
        with open(output_md_file, 'w') as f:
            f.write("# License Report for Bracket Codebase\n\n")
            f.write(f"**Generated:** {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            f.write("## SBOM Statistics\n\n")
            f.write(f"- **Total Lines in SBOM File:** {total_lines:,}\n")
            f.write(f"- **Top-Level Components:** {total_components_in_sbom:,}\n")
            f.write(f"- **Total Components (including nested):** {components_processed:,}\n")
            f.write(f"- **Components with License Info:** {len(license_info):,}\n\n")

            f.write("## License Summary\n\n")
            f.write("| License | Count | Percentage |\n")
            f.write("|---------|-------|------------|\n")

            total_components = len(license_info)
            for license_id, count in sorted(license_counts.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / total_components) * 100 if total_components > 0 else 0
                f.write(f"| {license_id} | {count} | {percentage:.1f}% |\n")

            f.write(f"\n**Total Components with License Info:** {total_components}\n\n")

        # Generate a detailed markdown report with all components
        with open(output_detailed_md_file, 'w') as f:
            f.write("# Detailed License Report for Bracket Codebase\n\n")
            f.write(f"**Generated:** {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            f.write("## SBOM Statistics\n\n")
            f.write(f"- **Total Lines in SBOM File:** {total_lines:,}\n")
            f.write(f"- **Top-Level Components:** {total_components_in_sbom:,}\n")
            f.write(f"- **Total Components (including nested):** {components_processed:,}\n")
            f.write(f"- **Components with License Info:** {len(license_info):,}\n\n")

            f.write("## All Components\n\n")
            f.write("| Component | Version | Type | License |\n")
            f.write("|-----------|---------|------|--------|\n")

            # Sort components by name for better readability
            sorted_components = sorted(license_info.items(), key=lambda x: x[0])
            for key, info in sorted_components:
                name_parts = key.split('@')
                name = '@'.join(name_parts[:-1]) if len(name_parts) > 1 else key
                version = name_parts[-1] if len(name_parts) > 1 else "Unknown"
                licenses_str = ", ".join(info["licenses"])
                f.write(f"| {name} | {version} | {info['type']} | {licenses_str} |\n")

            # Write license details by category
            license_categories = {
                "Permissive": ["MIT", "BSD", "Apache", "ISC", "0BSD", "Unlicense", "CC0"],
                "Copyleft": ["GPL", "LGPL", "AGPL", "MPL", "EPL", "CDDL"],
                "Commercial": ["Proprietary", "Commercial"],
                "Unknown": ["Unknown"]
            }

            for category, patterns in license_categories.items():
                f.write(f"## {category} Licenses\n\n")

                category_found = False
                for license_id, components in license_components.items():
                    if any(pattern in license_id for pattern in patterns):
                        category_found = True
                        f.write(f"### {license_id} ({len(components)} components)\n\n")

                        # List first 10 components
                        for i, component in enumerate(sorted(components)):
                            if i < 10:
                                f.write(f"- {component}\n")
                            elif i == 10:
                                f.write(f"- ... and {len(components) - 10} more\n")

                        f.write("\n")

                if not category_found:
                    f.write(f"No {category.lower()} licenses found.\n\n")

        print_success(f"License report generated at {output_file}")
        print_success(f"Markdown license report generated at {output_md_file}")
        return output_file
    except Exception as e:
        print_error(f"Failed to generate license report: {str(e)}")
        return ""

def main():
    parser = argparse.ArgumentParser(description="Generate a comprehensive SBOM for the Bracket codebase")
    parser.add_argument("--output", default="sbom_output", help="Output directory for SBOM files")
    args = parser.parse_args()

    # Create output directory
    output_dir = args.output
    os.makedirs(output_dir, exist_ok=True)

    # Check dependencies
    if not check_dependencies():
        sys.exit(1)

    # Generate SBOMs
    python_sbom = generate_python_sbom(output_dir)
    js_sbom = generate_npm_sbom(output_dir)  # This now returns the path in bracket_ext/sbom_output
    docker_sbom = generate_docker_sbom(output_dir)

    # Combine SBOMs
    sbom_files = []
    if python_sbom:
        sbom_files.append(python_sbom)
    if js_sbom:
        sbom_files.append(js_sbom)
    if docker_sbom:
        sbom_files.append(docker_sbom)

    print_step(f"SBOM files to combine: {sbom_files}")
    combined_sbom = combine_sboms(sbom_files, output_dir)

    # Generate license report
    if combined_sbom:
        generate_license_report(output_dir)

    print_header("SBOM Generation Complete")
    print_success(f"All SBOM files have been generated in the '{output_dir}' directory")
    print_success("You can now use these files for your M&A due diligence with GitLab")

if __name__ == "__main__":
    main()
