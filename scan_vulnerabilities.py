#!/usr/bin/env python3
"""
Scan the SBOM for known security vulnerabilities using the OSV API.

This script:
1. Reads a CycloneDX SBOM file
2. Extracts all components and their versions
3. Queries the OSV API for known vulnerabilities
4. Generates a report of vulnerabilities found

Usage:
    python scan_vulnerabilities.py --sbom sbom_output/combined-sbom.json --output sbom_output/vulnerability-report.md
"""

import argparse
import json
import os
import sys
import requests
from typing import Dict, List, Any, Optional
import datetime

# ANSI colors for terminal output
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'

def print_header(message: str) -> None:
    """Print a formatted header message."""
    print(f"\n{Colors.HEADER}{Colors.BOLD}=== {message} ==={Colors.ENDC}\n")

def print_step(message: str) -> None:
    """Print a formatted step message."""
    print(f"{Colors.BLUE}→ {message}{Colors.ENDC}")

def print_success(message: str) -> None:
    """Print a formatted success message."""
    print(f"{Colors.GREEN}✓ {message}{Colors.ENDC}")

def print_warning(message: str) -> None:
    """Print a formatted warning message."""
    print(f"{Colors.YELLOW}⚠ {message}{Colors.ENDC}")

def print_error(message: str) -> None:
    """Print a formatted error message."""
    print(f"{Colors.RED}✗ {message}{Colors.ENDC}")

def extract_components(sbom_file: str) -> List[Dict[str, Any]]:
    """Extract components from the SBOM file."""
    print_step(f"Extracting components from {sbom_file}...")
    
    try:
        with open(sbom_file, 'r') as f:
            sbom_data = json.load(f)
        
        components = sbom_data.get("components", [])
        print_success(f"Extracted {len(components)} components")
        return components
    except Exception as e:
        print_error(f"Failed to extract components: {str(e)}")
        return []

def query_osv_api(components: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
    """Query the OSV API for vulnerabilities."""
    print_step("Querying OSV API for vulnerabilities...")
    
    vulnerabilities = {}
    batch_size = 100  # Process in batches to avoid large requests
    
    for i in range(0, len(components), batch_size):
        batch = components[i:i+batch_size]
        print_step(f"Processing batch {i//batch_size + 1}/{(len(components) + batch_size - 1)//batch_size}...")
        
        # Prepare the query
        query = {"queries": []}
        for component in batch:
            name = component.get("name", "")
            version = component.get("version", "")
            purl = component.get("purl", "")
            
            if purl:
                query["queries"].append({"package": {"purl": purl}})
            elif name and version:
                # Determine the ecosystem based on the purl or other indicators
                ecosystem = None
                if "purl" in component:
                    purl = component["purl"]
                    if purl.startswith("pkg:pypi/"):
                        ecosystem = "PyPI"
                    elif purl.startswith("pkg:npm/"):
                        ecosystem = "npm"
                
                if ecosystem:
                    query["queries"].append({
                        "package": {
                            "name": name,
                            "ecosystem": ecosystem
                        },
                        "version": version
                    })
        
        if not query["queries"]:
            continue
        
        try:
            # Query the OSV API
            response = requests.post("https://api.osv.dev/v1/querybatch", json=query)
            response.raise_for_status()
            results = response.json()
            
            # Process the results
            for i, result in enumerate(results.get("results", [])):
                if "vulns" in result:
                    component_key = f"{batch[i].get('name', 'Unknown')}@{batch[i].get('version', 'Unknown')}"
                    vulnerabilities[component_key] = result["vulns"]
        
        except Exception as e:
            print_error(f"Failed to query OSV API: {str(e)}")
    
    total_vulns = sum(len(vulns) for vulns in vulnerabilities.values())
    print_success(f"Found {total_vulns} vulnerabilities in {len(vulnerabilities)} components")
    return vulnerabilities

def generate_report(vulnerabilities: Dict[str, List[Dict[str, Any]]], output_file: str) -> None:
    """Generate a markdown report of vulnerabilities."""
    print_step(f"Generating vulnerability report at {output_file}...")
    
    # Count vulnerabilities by severity
    severity_counts = {"CRITICAL": 0, "HIGH": 0, "MEDIUM": 0, "LOW": 0, "UNKNOWN": 0}
    
    for component_vulns in vulnerabilities.values():
        for vuln in component_vulns:
            severity = "UNKNOWN"
            for db_specific in vuln.get("database_specific", {}).get("severity", []):
                if db_specific.get("type") == "CVSS_V3":
                    score = db_specific.get("score")
                    if score is not None:
                        if score >= 9.0:
                            severity = "CRITICAL"
                        elif score >= 7.0:
                            severity = "HIGH"
                        elif score >= 4.0:
                            severity = "MEDIUM"
                        else:
                            severity = "LOW"
                        break
            
            severity_counts[severity] += 1
    
    # Generate the report
    report = f"""# Security Vulnerability Report

**Generated:** {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## Summary

This report identifies security vulnerabilities in the dependencies used by the Bracket codebase.

### Vulnerability Counts by Severity

| Severity | Count |
|----------|-------|
| CRITICAL | {severity_counts["CRITICAL"]} |
| HIGH | {severity_counts["HIGH"]} |
| MEDIUM | {severity_counts["MEDIUM"]} |
| LOW | {severity_counts["LOW"]} |
| UNKNOWN | {severity_counts["UNKNOWN"]} |
| **TOTAL** | **{sum(severity_counts.values())}** |

## Vulnerable Components

"""
    
    # Sort vulnerabilities by severity
    sorted_components = sorted(
        vulnerabilities.items(),
        key=lambda x: sum(1 for v in x[1] if any(
            db.get("score", 0) >= 7.0 
            for db in v.get("database_specific", {}).get("severity", [])
            if db.get("type") == "CVSS_V3"
        )),
        reverse=True
    )
    
    for component, vulns in sorted_components:
        report += f"### {component}\n\n"
        
        for vuln in vulns:
            vuln_id = vuln.get("id", "Unknown")
            summary = vuln.get("summary", "No summary available")
            details = vuln.get("details", "No details available")
            
            # Extract severity information
            severity = "UNKNOWN"
            severity_score = None
            for db_specific in vuln.get("database_specific", {}).get("severity", []):
                if db_specific.get("type") == "CVSS_V3":
                    severity_score = db_specific.get("score")
                    if severity_score is not None:
                        if severity_score >= 9.0:
                            severity = "CRITICAL"
                        elif severity_score >= 7.0:
                            severity = "HIGH"
                        elif severity_score >= 4.0:
                            severity = "MEDIUM"
                        else:
                            severity = "LOW"
                        break
            
            # Extract references
            references = []
            for ref in vuln.get("references", []):
                references.append(f"- [{ref.get('type', 'Link')}]({ref.get('url', '#')})")
            
            report += f"#### {vuln_id} - {severity}"
            if severity_score is not None:
                report += f" (Score: {severity_score})"
            report += "\n\n"
            
            report += f"**Summary:** {summary}\n\n"
            report += f"**Details:** {details[:500]}...\n\n"
            
            if references:
                report += "**References:**\n\n"
                report += "\n".join(references[:5])
                if len(references) > 5:
                    report += f"\n- ... and {len(references) - 5} more"
            
            report += "\n\n"
    
    # Write the report to file
    with open(output_file, 'w') as f:
        f.write(report)
    
    print_success(f"Vulnerability report generated at {output_file}")

def main():
    parser = argparse.ArgumentParser(description="Scan SBOM for security vulnerabilities")
    parser.add_argument("--sbom", required=True, help="Path to the CycloneDX SBOM JSON file")
    parser.add_argument("--output", required=True, help="Path to the output report file")
    args = parser.parse_args()
    
    print_header("Scanning SBOM for Security Vulnerabilities")
    
    # Extract components from SBOM
    components = extract_components(args.sbom)
    if not components:
        sys.exit(1)
    
    # Query OSV API for vulnerabilities
    vulnerabilities = query_osv_api(components)
    
    # Generate vulnerability report
    generate_report(vulnerabilities, args.output)
    
    print_header("Vulnerability Scan Complete")

if __name__ == "__main__":
    main()
