#!/bin/bash
# Start script for Bracket IRL microservices E2E tests
# This script starts all the microservices in Docker containers

set -e  # Exit on error

echo "Starting Bracket IRL microservices for E2E testing..."

# Set the repository directory to the test repository
export REPO_DIR=$(pwd)/results/test_repo

# Create the test repository directory if it doesn't exist
mkdir -p "$REPO_DIR"
echo "Created test repository directory: $REPO_DIR"

# Add a sample file to the test repository so it's not empty
echo "# Sample repository for testing" > "$REPO_DIR/README.md"
echo "def sample_function():" > "$REPO_DIR/sample.py"
echo "    return 'Hello, World!'" >> "$REPO_DIR/sample.py"

# Add a more complex file to test domain analysis
cat > "$REPO_DIR/complex_module.py" << EOF
"""
A more complex module for testing domain analysis.
"""

class DataProcessor:
    """Process data from various sources."""

    def __init__(self, config=None):
        self.config = config or {}
        self.data = []

    def load_data(self, source):
        """Load data from a source."""
        print(f"Loading data from {source}")
        self.data = [1, 2, 3, 4, 5]
        return self.data

    def process_data(self):
        """Process the loaded data."""
        if not self.data:
            raise ValueError("No data loaded")

        return [x * 2 for x in self.data]

    def save_results(self, destination):
        """Save processed results."""
        processed = self.process_data()
        print(f"Saving results to {destination}: {processed}")
        return True

class APIClient:
    """Client for API interactions."""

    def __init__(self, base_url):
        self.base_url = base_url
        self.auth_token = None

    def authenticate(self, username, password):
        """Authenticate with the API."""
        print(f"Authenticating {username} with API at {self.base_url}")
        self.auth_token = "sample_token"
        return self.auth_token

    def get_data(self, endpoint):
        """Get data from an API endpoint."""
        if not self.auth_token:
            raise ValueError("Not authenticated")

        print(f"Getting data from {self.base_url}/{endpoint}")
        return {"status": "success", "data": [1, 2, 3]}

    def post_data(self, endpoint, data):
        """Post data to an API endpoint."""
        if not self.auth_token:
            raise ValueError("Not authenticated")

        print(f"Posting data to {self.base_url}/{endpoint}: {data}")
        return {"status": "success", "id": 123}

def main():
    """Main function to demonstrate the classes."""
    # Initialize data processor
    processor = DataProcessor({"option": "value"})

    # Load and process data
    processor.load_data("sample_source")
    results = processor.process_data()
    processor.save_results("sample_destination")

    # Use API client
    client = APIClient("https://api.example.com")
    client.authenticate("user", "pass")
    api_data = client.get_data("users")
    response = client.post_data("items", {"name": "New Item"})

    print("All operations completed successfully")
    return 0

if __name__ == "__main__":
    main()
EOF

echo "Using test repository: $REPO_DIR"
echo "This will be mounted as /repo in the Docker containers"

# Create results directory
mkdir -p results/orchestrator_e2e

# Create docker-compose.override.yml to mount the test repository
cat > docker-compose.override.yml << EOF
version: '3.8'

services:
  # Repository Mapper Service
  repo-mapper-service:
    volumes:
      - ${REPO_DIR}:/repo
      - $(pwd)/results:/app/tests/e2e/results
      - $(pwd)/results:/app/data/artifacts
    environment:
      - PYTHONPATH=/app:/

  # Domain Analyzer Service
  domain-analyzer-service:
    volumes:
      - ${REPO_DIR}:/repo
      - $(pwd)/results:/app/tests/e2e/results
      - $(pwd)/results:/app/data/artifacts
    environment:
      - PYTHONPATH=/app:/
      - OPENAI_API_KEY=${OPENAI_API_KEY}

  # Domain-File Repomap Service
  domain-file-repomap-service:
    volumes:
      - ${REPO_DIR}:/repo
      - $(pwd)/results:/app/tests/e2e/results
      - $(pwd)/results:/app/data/artifacts
    environment:
      - PYTHONPATH=/app:/

  # File-Domain Mapper Service
  file-domain-mapper-service:
    volumes:
      - ${REPO_DIR}:/repo
      - $(pwd)/results:/app/tests/e2e/results
      - $(pwd)/results:/app/data/artifacts
    environment:
      - PYTHONPATH=/app:/
      - OPENAI_API_KEY=${OPENAI_API_KEY}

  # Diagram Generator Service
  diagram-generator-service:
    volumes:
      - ${REPO_DIR}:/repo
      - $(pwd)/results:/app/tests/e2e/results
      - $(pwd)/results:/app/data/artifacts
    environment:
      - PYTHONPATH=/app:/
      - OPENAI_API_KEY=${OPENAI_API_KEY}

  # Orchestrator Service
  orchestrator-service:
    volumes:
      - ${REPO_DIR}:/repo
      - $(pwd)/results:/app/tests/e2e/results
      - $(pwd)/results:/app/data/artifacts
    environment:
      - PYTHONPATH=/app:/
EOF

# Copy the implementation files if needed
echo "Copying the implementation files..."
if [ -f "../repo-mapper-service/copy_implementation.sh" ]; then
  cd ../repo-mapper-service
  ./copy_implementation.sh
  cd ../tests/e2e
fi

# Rebuild and restart the services
echo "Rebuilding and restarting services..."
cd ../../
docker compose down
docker compose build
docker compose up -d
cd tests/e2e

echo "Services started successfully!"
echo "To run the orchestrator E2E test, use: ./run_orchestrator_e2e_test.sh"
