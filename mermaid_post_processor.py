#!/usr/bin/env python3
"""
Mermaid Post-Processor Module

This module provides functions to post-process mermaid diagrams to fix syntax issues.
It can be imported and used directly in Python code.
"""

import os
import logging
import asyncio
from pathlib import Path
from typing import Dict, List, Optional, Union

from mermaid_beautifier.mermaid_beautifier import MermaidBeautifier

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def post_process_mermaid_directory(
    input_dir: Union[str, Path],
    output_dir: Optional[Union[str, Path]] = None,
    dry_run: bool = False
) -> Dict[str, int]:
    """Post-process all mermaid diagrams in a directory.
    
    Args:
        input_dir: Directory containing mermaid diagram files
        output_dir: Directory to save processed diagrams (if None, overwrites original files)
        dry_run: If True, don't actually save changes
        
    Returns:
        Statistics about the processing
    """
    # Convert to Path objects
    input_dir_path = Path(input_dir)
    output_dir_path = Path(output_dir) if output_dir else None
    
    # Initialize the mermaid beautifier
    beautifier = MermaidBeautifier(
        input_dir=str(input_dir_path),
        output_dir=str(output_dir_path) if output_dir_path else None,
        dry_run=dry_run,
    )
    
    # Run only the post-processing
    stats = await beautifier.post_process_directory()
    
    return stats

def post_process_mermaid_directory_sync(
    input_dir: Union[str, Path],
    output_dir: Optional[Union[str, Path]] = None,
    dry_run: bool = False
) -> Dict[str, int]:
    """Synchronous wrapper for post_process_mermaid_directory.
    
    Args:
        input_dir: Directory containing mermaid diagram files
        output_dir: Directory to save processed diagrams (if None, overwrites original files)
        dry_run: If True, don't actually save changes
        
    Returns:
        Statistics about the processing
    """
    return asyncio.run(post_process_mermaid_directory(input_dir, output_dir, dry_run))

def post_process_mermaid_text(mermaid_text: str) -> str:
    """Post-process a single mermaid diagram text.
    
    Args:
        mermaid_text: The mermaid diagram text to process
        
    Returns:
        The processed mermaid diagram text
    """
    # Create a temporary beautifier instance
    beautifier = MermaidBeautifier(input_dir=".")
    
    # Apply post-processing
    processed_text = beautifier._post_process_diagram(mermaid_text)
    
    return processed_text

# Example usage
if __name__ == "__main__":
    # Example of processing a directory
    stats = post_process_mermaid_directory_sync(
        input_dir="/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/mermaid_outputs_json/gitlab/diagrams_from_json",
        output_dir="/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/arch_diagrams/ouput1"
    )
    print(f"Processed {stats['processed_files']} files")
    
    # Example of processing a single diagram
    diagram = """
    flowchart TD
        A[Start (Process)] --> B(Do Something)
        B --> C{Decision}
        C -->|Yes| D[End (Process)]
        C -->|No| B
    """
    
    processed = post_process_mermaid_text(diagram)
    print("\nOriginal diagram:")
    print(diagram)
    print("\nProcessed diagram:")
    print(processed)
