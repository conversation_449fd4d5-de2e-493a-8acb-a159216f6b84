--- bracket_core/bracket_irl/microservices/bracket_irl_common/bracket_irl_common/storage.py.orig
+++ bracket_core/bracket_irl/microservices/bracket_irl_common/bracket_irl_common/storage.py
@@ -159,8 +159,14 @@
 
     def _get_full_path(self, path: str) -> str:
         """Get full path for file."""
-        # Ensure path is relative to base path
+        # Special case for absolute paths that match the expected storage pattern
+        # This handles paths like /app/data/artifacts/{job_id}/...
+        if os.path.isabs(path) and path.startswith('/app/data/'):
+            # Return the path as-is to avoid double-prepending /app/data/
+            return path
+            
         if os.path.isabs(path):
+            # For other absolute paths, make them relative to base path
             path = os.path.relpath(path, "/")
 
         full_path = os.path.join(self.base_path, path)
