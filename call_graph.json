{"bracket_core/llm/llm_callbacks.py:__init__": {"name": "__init__", "filepath": "bracket_core/llm/llm_callbacks.py", "callees": [], "callers": ["bracket_core/llm/llm_callbacks.py:on_llm_new_token"]}, "bracket_core/llm/llm_callbacks.py:on_llm_new_token": {"name": "on_llm_new_token", "filepath": "bracket_core/llm/llm_callbacks.py", "callees": ["bracket_core/llm/llm_callbacks.py:__init__"], "callers": []}, "bracket_core/llm/tokens.py:num_tokens_from_string": {"name": "num_tokens_from_string", "filepath": "bracket_core/llm/tokens.py", "callees": ["tiktoken"], "callers": []}, "tiktoken": {"name": "tiktoken", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/llm/tokens.py:num_tokens_from_string", "bracket_core/utils/token_counter.py:count_tokens_and_loc_in_file", "bracket_core/utils/md_token_counter.py:num_tokens", "bracket_core/llm/text_utils.py:num_tokens", "bracket_core/domain_trace_token_analyzer.py:num_tokens_from_string", "bracket_core/llm/oai/embedding.py:__init__", "bracket_core/domain_taxonomy_token_analyzer.py:num_tokens_from_string", "bracket_core/llm/tokens.py:string_from_tokens", "bracket_core/llm/text_utils.py:chunk_text", "bracket_core/hierarchical_domain_trace_builder.py:_count_tokens", "bracket_core/localisation/global_localisation.py:count_tokens_with_tiktoken", "bracket_core/documenting.py:num_tokens_consumed_from_request"]}, "bracket_core/utils/ruby_fn_counter.py:count_ruby_functions_in_file": {"name": "count_ruby_functions_in_file", "filepath": "bracket_core/utils/ruby_fn_counter.py", "callees": ["re"], "callers": ["bracket_core/utils/ruby_fn_counter.py:count_functions_in_project"]}, "re": {"name": "re", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/utils/ruby_fn_counter.py:count_ruby_functions_in_file", "bracket_core/llm/text_utils.py:try_parse_json_object", "bracket_core/enhanced_domain_trace_builder.py:_normalize_file_path", "bracket_core/call_graph_generator.py:generate_basic_callgraph", "bracket_core/hierarchical_domain_trace_builder.py:_normalize_file_path", "bracket_core/documenting.py:api_endpoint_from_url", "bracket_core/domain_diagram_generator.py:_extract_mermaid_diagram", "bracket_core/parsing_repomap.py:_extract_python_imports", "bracket_core/global_codebase_explainer.py:generate_questions", "bracket_core/parsing_repomap.py:_extract_js_imports", "bracket_core/parsing_repomap.py:_extract_java_imports", "bracket_core/hierarchical_domain_file_mapper.py:_process_domain_specific_request"]}, "bracket_core/logger/console.py:error": {"name": "error", "filepath": "bracket_core/logger/console.py", "callees": [], "callers": []}, "bracket_core/logger/null_progress.py:__call__": {"name": "__call__", "filepath": "bracket_core/logger/null_progress.py", "callees": [], "callers": []}, "bracket_core/logger/print_progress.py:__init__": {"name": "__init__", "filepath": "bracket_core/logger/print_progress.py", "callees": [], "callers": ["bracket_core/logger/print_progress.py:child"]}, "bracket_core/utils/token_counter.py:count_tokens_and_loc_in_file": {"name": "count_tokens_and_loc_in_file", "filepath": "bracket_core/utils/token_counter.py", "callees": ["tiktoken"], "callers": ["bracket_core/utils/token_counter.py:count_tokens_and_loc_in_directory", "bracket_core/utils/token_counter.py:count_tokens_and_loc_in_directory_no_tests"]}, "bracket_core/utils/md_token_counter.py:num_tokens": {"name": "num_tokens", "filepath": "bracket_core/utils/md_token_counter.py", "callees": ["tiktoken"], "callers": ["bracket_core/utils/md_token_counter.py:count_tokens_in_md_files"]}, "bracket_core/llm/text_utils.py:num_tokens": {"name": "num_tokens", "filepath": "bracket_core/llm/text_utils.py", "callees": ["tiktoken"], "callers": []}, "bracket_core/logger/null_progress.py:dispose": {"name": "dispose", "filepath": "bracket_core/logger/null_progress.py", "callees": [], "callers": []}, "bracket_core/logger/base.py:error": {"name": "error", "filepath": "bracket_core/logger/base.py", "callees": [], "callers": []}, "bracket_core/llm/base.py:generate": {"name": "generate", "filepath": "bracket_core/llm/base.py", "callees": [], "callers": []}, "bracket_core/logger/console.py:warning": {"name": "warning", "filepath": "bracket_core/logger/console.py", "callees": ["bracket_core/logger/console.py:_print_warning"], "callers": []}, "bracket_core/logger/console.py:_print_warning": {"name": "_print_warning", "filepath": "bracket_core/logger/console.py", "callees": [], "callers": ["bracket_core/logger/console.py:warning"]}, "bracket_core/logger/null_progress.py:child": {"name": "child", "filepath": "bracket_core/logger/null_progress.py", "callees": [], "callers": []}, "bracket_core/logger/types.py:__str__": {"name": "__str__", "filepath": "bracket_core/logger/types.py", "callees": [], "callers": []}, "bracket_core/llm/anthropic/client.py:__init__": {"name": "__init__", "filepath": "bracket_core/llm/anthropic/client.py", "callees": ["get_anthropic_api_key", "Anthropic"], "callers": ["bracket_core/llm/anthropic/client.py:generate", "bracket_core/llm/anthropic/client.py:generate_with_json_mode", "bracket_core/llm/anthropic/client.py:generate_with_tools", "bracket_core/llm/anthropic/client.py:continue_tool_conversation"]}, "get_anthropic_api_key": {"name": "get_anthropic_api_key", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/llm/anthropic/client.py:__init__", "bracket_core/llm/get_client.py:get_claude_client"]}, "Anthropic": {"name": "Anthropic", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/llm/anthropic/client.py:__init__"]}, "bracket_core/llm/openrouter/client.py:__init__": {"name": "__init__", "filepath": "bracket_core/llm/openrouter/client.py", "callees": ["get_openrouter_api_key", "OpenAI", "AsyncOpenAI"], "callers": ["bracket_core/llm/openrouter/client.py:generate", "bracket_core/llm/openrouter/client.py:generate_json", "bracket_core/llm/openrouter/client.py:astream_generate"]}, "get_openrouter_api_key": {"name": "get_openrouter_api_key", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/llm/openrouter/client.py:__init__", "bracket_core/localisation/global_localisation.py:call_api", "bracket_core/global_codebase_explainer.py:__init__", "bracket_core/localisation/horizontal_localisation.py:__init__", "bracket_core/domain_analysis.py:__init__", "bracket_core/domain_file_mapper.py:__init__"]}, "OpenAI": {"name": "OpenAI", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/llm/openrouter/client.py:__init__", "bracket_core/llm/oai/base.py:_create_openai_client"]}, "AsyncOpenAI": {"name": "AsyncOpenAI", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/llm/openrouter/client.py:__init__", "bracket_core/llm/oai/base.py:_create_openai_client"]}, "bracket_core/logger/print_progress.py:__call__": {"name": "__call__", "filepath": "bracket_core/logger/print_progress.py", "callees": [], "callers": []}, "bracket_core/test_mermaid_generator.py:test_mermaid_generation": {"name": "test_mermaid_generation", "filepath": "bracket_core/test_mermaid_generator.py", "callees": ["os", "MermaidDiagramGenerator", "time"], "callers": ["bracket_core/test_mermaid_generator.py:main"]}, "os": {"name": "os", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/test_mermaid_generator.py:test_mermaid_generation", "bracket_core/mermaid_generator.py:__init__", "bracket_core/utils/md_token_counter.py:count_tokens_in_md_files", "bracket_core/utils/ruby_fn_counter.py:count_functions_in_project", "bracket_core/generate_domain_taxonomy.py:generate_domain_taxonomy", "bracket_core/domain_classifier.py:__init__", "bracket_core/llm/api_keys.py:get_openai_api_key", "bracket_core/call_graph_generator.py:create_patched_callgraph", "bracket_core/utils/token_counter.py:count_tokens_and_loc_in_directory", "bracket_core/localisation/minimal_horizontal.py:load_domain_taxonomy", "bracket_core/file_call_graph_builder.py:__init__", "bracket_core/utils/md_token_counter.py:main", "bracket_core/llm/api_keys.py:get_openrouter_api_key", "bracket_core/utils/token_counter.py:count_tokens_and_loc_in_directory_no_tests", "bracket_core/call_graph_generator.py:find_callgraph_executable", "bracket_core/leaf_node_token_calculator.py:__init__", "bracket_core/domain_diagram_generator.py:__init__", "bracket_core/irl.py:__init__", "bracket_core/domain_taxonomy_mapper.py:__init__", "bracket_core/test_mermaid_generator.py:patched_generate", "bracket_core/domain_taxonomy_token_analyzer.py:__init__", "bracket_core/domain_trace_token_analyzer.py:__init__", "bracket_core/mermaid_generator.py:generate_mermaid_diagrams", "bracket_core/call_graph_generator.py:group_files_by_language", "bracket_core/llm/api_keys.py:get_anthropic_api_key", "bracket_core/utils/token_counter.py:main", "bracket_core/call_graph_generator.py:analyze_language_files", "bracket_core/run_hierarchical_mapper.py:run_mapper", "bracket_core/localisation/horizontal_localisation.py:__init__", "bracket_core/file_call_graph_builder.py:_get_relative_path", "bracket_core/hybrid_kg.py:_extract_all_function_definitions", "bracket_core/enhanced_domain_trace_builder.py:_normalize_file_path", "bracket_core/mermaid_generator.py:generate_diagram_by_type", "bracket_core/call_graph_generator.py:merge_yaml_files", "bracket_core/domain_taxonomy_mapper.py:read_diagram_name_mapping", "bracket_core/irl.py:run", "bracket_core/hybrid_kg.py:_process_repository", "bracket_core/domain_taxonomy_token_analyzer.py:save_analysis_results", "bracket_core/domain_trace_token_analyzer.py:save_analysis_results", "bracket_core/call_graph_generator.py:generate_basic_callgraph", "bracket_core/enhanced_domain_trace_builder.py:map_functions_to_files", "bracket_core/domain_taxonomy_mapper.py:read_domain_diagrams", "bracket_core/hybrid_kg.py:_extract_functions_and_calls", "bracket_core/parsing_repomap.py:__init__", "bracket_core/hybrid_kg.py:_process_function_definitions", "bracket_core/localisation/horizontal_localisation.py:load_semantic_documented_functions", "bracket_core/hierarchical_domain_trace_builder.py:_normalize_file_path", "bracket_core/cli.py:analyze", "bracket_core/parsing_repomap.py:get_rel_fname", "bracket_core/parsing_repomap.py:get_mtime", "bracket_core/parsing_repomap.py:file_path_to_module_path", "bracket_core/enhanced_domain_trace_builder.py:get_domain_specific_functions", "bracket_core/parsing_repomap.py:module_path_to_file_path", "bracket_core/irl.py:create_final_artifact", "bracket_core/domain_diagram_generator.py:_get_cache_path", "bracket_core/domain_diagram_generator.py:_check_cache", "bracket_core/domain_taxonomy_mapper.py:map_diagrams_to_taxonomy", "bracket_core/call_graph_generator.py:main", "bracket_core/irl.py:generate_significant_functions_yaml", "bracket_core/localisation/global_localisation.py:__init__", "bracket_core/hierarchical_domain_trace_builder.py:get_domain_specific_functions_hierarchical", "bracket_core/domain_analysis.py:create_hierar_domain_struct", "bracket_core/leaf_node_token_calculator.py:save_analysis_results", "bracket_core/hybrid_kg.py:_file_path_to_module_path", "bracket_core/irl.py:create_hierarchical_domains", "bracket_core/irl.py:build_file_call_graph", "bracket_core/domain_taxonomy_mapper.py:save_taxonomy_json", "bracket_core/domain_taxonomy_mapper.py:find_combined_diagrams", "bracket_core/irl.py:map_files_to_domains", "bracket_core/hierarchical_domain_file_mapper.py:_save_current_mappings_to_disk", "bracket_core/hierarchical_domain_trace_builder.py:_process_file_for_trace", "bracket_core/localisation/global_localisation.py:load_semantic_documented_functions", "bracket_core/localisation/global_localisation.py:load_domain_taxonomy", "bracket_core/irl.py:build_domain_traces", "bracket_core/parsing_repomap.py:_resolve_js_module_path", "bracket_core/localisation/global_localisation.py:evaluate_trace_relevance", "bracket_core/irl.py:generate_domain_diagrams", "bracket_core/irl.py:generate_domain_taxonomy", "bracket_core/irl.py:generate_codebase_explanation", "bracket_core/localisation/global_localisation.py:evaluate_domain_relevance", "bracket_core/domain_diagram_generator.py:_process_api_response", "bracket_core/localisation/global_localisation.py:extract_relevant_functions", "bracket_core/domain_diagram_generator.py:generate_all_diagrams", "bracket_core/localisation/global_localisation.py:third_pass_analyze_relevant_functions", "bracket_core/domain_diagram_generator.py:main", "bracket_core/parsing_repomap.py:create_graph", "bracket_core/parsing_repomap.py:save_graph_as_parquet", "bracket_core/parsing_repomap.py:_normalize_filepath", "bracket_core/parsing_repomap.py:enrich_with_line_numbers", "bracket_core/parsing_repomap.py:enrich_with_nuanced_callgraph", "bracket_core/parsing_repomap.py:is_test_file", "bracket_core/parsing_repomap.py:get_scm_fname"]}, "MermaidDiagramGenerator": {"name": "MermaidDiagramGenerator", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/test_mermaid_generator.py:test_mermaid_generation"]}, "time": {"name": "time", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/test_mermaid_generator.py:test_mermaid_generation", "bracket_core/llm/rate_limiter.py:__init__", "bracket_core/llm/rate_limiter.py:acquire", "bracket_core/domain_file_mapper.py:call_api", "bracket_core/hierarchical_domain_file_mapper.py:__init__", "bracket_core/test_mermaid_generator.py:patched_generate", "bracket_core/domain_trace_builder.py:call_api", "bracket_core/hybrid_kg.py:generate_graph", "bracket_core/documenting.py:process_api_requests_from_file", "bracket_core/domain_taxonomy_token_analyzer.py:save_analysis_results", "bracket_core/domain_trace_token_analyzer.py:save_analysis_results", "bracket_core/domain_taxonomy_token_analyzer.py:main", "bracket_core/domain_trace_builder.py:classify_functions", "bracket_core/cli.py:analyze", "bracket_core/domain_file_mapper.py:_classify_files_to_domains", "bracket_core/documenting.py:call_api", "bracket_core/localisation/horizontal_localisation.py:_create_function_lookup_indices", "bracket_core/leaf_node_token_calculator.py:save_analysis_results", "bracket_core/leaf_node_token_calculator.py:main", "bracket_core/localisation/global_localisation.py:evaluate_trace_relevance", "bracket_core/domain_analysis.py:_call_openai_api", "bracket_core/hierarchical_domain_file_mapper.py:_classify_files_to_specific_domain", "bracket_core/irl.py:main", "bracket_core/localisation/global_localisation.py:evaluate_domain_relevance", "bracket_core/hierarchical_domain_file_mapper.py:_process_domain_specific_request", "bracket_core/localisation/horizontal_localisation.py:extract_relevant_functions", "bracket_core/localisation/global_localisation.py:extract_relevant_functions", "bracket_core/domain_diagram_generator.py:generate_all_diagrams", "bracket_core/localisation/horizontal_localisation.py:find_relevant_functions", "bracket_core/localisation/global_localisation.py:second_pass_find_relevant_functions", "bracket_core/localisation/global_localisation.py:main"]}, "bracket_core/logger/base.py:warning": {"name": "warning", "filepath": "bracket_core/logger/base.py", "callees": [], "callers": []}, "bracket_core/mermaid_generator.py:__init__": {"name": "__init__", "filepath": "bracket_core/mermaid_generator.py", "callees": ["os", "get_claude_client", "RateLimiter"], "callers": ["bracket_core/mermaid_generator.py:generate_mermaid_diagrams", "bracket_core/mermaid_generator.py:generate_diagram_by_type", "bracket_core/mermaid_generator.py:_generate_diagram_by_type", "bracket_core/mermaid_generator.py:_generate_comprehensive_architecture_diagrams"]}, "get_claude_client": {"name": "get_claude_client", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/mermaid_generator.py:__init__", "bracket_core/domain_diagram_generator.py:__init__"]}, "RateLimiter": {"name": "RateLimiter", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/mermaid_generator.py:__init__", "bracket_core/domain_diagram_generator.py:__init__"]}, "bracket_core/llm/rate_limiter.py:__init__": {"name": "__init__", "filepath": "bracket_core/llm/rate_limiter.py", "callees": ["time"], "callers": []}, "bracket_core/logger/console.py:log": {"name": "log", "filepath": "bracket_core/logger/console.py", "callees": [], "callers": []}, "bracket_core/utils/md_token_counter.py:count_tokens_in_md_files": {"name": "count_tokens_in_md_files", "filepath": "bracket_core/utils/md_token_counter.py", "callees": ["os", "bracket_core/utils/md_token_counter.py:num_tokens"], "callers": ["bracket_core/utils/md_token_counter.py:main"]}, "bracket_core/logger/null_progress.py:force_refresh": {"name": "force_refresh", "filepath": "bracket_core/logger/null_progress.py", "callees": [], "callers": []}, "bracket_core/logger/print_progress.py:dispose": {"name": "dispose", "filepath": "bracket_core/logger/print_progress.py", "callees": [], "callers": []}, "bracket_core/logger/factory.py:register": {"name": "register", "filepath": "bracket_core/logger/factory.py", "callees": [], "callers": []}, "bracket_core/llm/enums.py:__repr__": {"name": "__repr__", "filepath": "bracket_core/llm/enums.py", "callees": [], "callers": []}, "bracket_core/utils/ruby_fn_counter.py:count_functions_in_project": {"name": "count_functions_in_project", "filepath": "bracket_core/utils/ruby_fn_counter.py", "callees": ["os", "bracket_core/utils/ruby_fn_counter.py:count_ruby_functions_in_file"], "callers": []}, "bracket_core/logger/base.py:log": {"name": "log", "filepath": "bracket_core/logger/base.py", "callees": [], "callers": []}, "bracket_core/llm/text_utils.py:batched": {"name": "batched", "filepath": "bracket_core/llm/text_utils.py", "callees": ["islice"], "callers": ["bracket_core/llm/text_utils.py:chunk_text"]}, "islice": {"name": "islice", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/llm/text_utils.py:batched"]}, "bracket_core/llm/oai/openai.py:__init__": {"name": "__init__", "filepath": "bracket_core/llm/oai/openai.py", "callees": [], "callers": ["bracket_core/llm/oai/openai.py:_generate", "bracket_core/llm/oai/openai.py:_agenerate"]}, "bracket_core/llm/base.py:stream_generate": {"name": "stream_generate", "filepath": "bracket_core/llm/base.py", "callees": [], "callers": []}, "bracket_core/logger/null_progress.py:stop": {"name": "stop", "filepath": "bracket_core/logger/null_progress.py", "callees": [], "callers": []}, "bracket_core/llm/oai/base.py:__init__": {"name": "__init__", "filepath": "bracket_core/llm/oai/base.py", "callees": ["bracket_core/llm/oai/base.py:_create_openai_client", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "StatusLogger"], "callers": []}, "bracket_core/llm/oai/base.py:_create_openai_client": {"name": "_create_openai_client", "filepath": "bracket_core/llm/oai/base.py", "callees": ["AzureOpenAI", "AsyncAzureOpenAI", "bracket_core/llm/oai/base.py:set_clients", "OpenAI", "AsyncOpenAI"], "callers": ["bracket_core/llm/oai/base.py:__init__"]}, "bracket_core/logger/print_progress.py:child": {"name": "child", "filepath": "bracket_core/logger/print_progress.py", "callees": ["bracket_core/logger/print_progress.py:__init__"], "callers": []}, "bracket_core/llm/rate_limiter.py:acquire": {"name": "acquire", "filepath": "bracket_core/llm/rate_limiter.py", "callees": ["time", "asyncio"], "callers": []}, "asyncio": {"name": "asyncio", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/llm/rate_limiter.py:acquire", "bracket_core/localisation/global_localisation.py:call_api", "bracket_core/hierarchical_domain_trace_builder.py:__init__", "bracket_core/domain_file_mapper.py:call_api", "bracket_core/hierarchical_domain_file_mapper.py:__init__", "bracket_core/cli.py:map_files_to_domains", "bracket_core/llm/openrouter/client.py:generate", "bracket_core/domain_trace_builder.py:call_api", "bracket_core/logger/rich_progress.py:refresh", "bracket_core/llm/oai/embedding.py:aembed", "bracket_core/documenting.py:process_api_requests_from_file", "bracket_core/domain_classifier.py:_call_openai_api", "bracket_core/cli.py:build_domain_traces", "bracket_core/hierarchical_domain_file_mapper.py:map_files_to_domains_hierarchically", "bracket_core/domain_trace_builder.py:classify_functions", "bracket_core/cli.py:analyze", "bracket_core/domain_file_mapper.py:_classify_files_to_domains", "bracket_core/documenting.py:call_api", "bracket_core/domain_analysis.py:create_hierar_domain_struct", "bracket_core/enhanced_domain_trace_builder.py:_call_openai_api", "bracket_core/hierarchical_domain_trace_builder.py:_process_leaf_node_file_by_file", "bracket_core/global_codebase_explainer.py:generate_explanation", "bracket_core/hierarchical_domain_file_mapper.py:_run_task_with_semaphore", "bracket_core/localisation/horizontal_localisation.py:process_domain_evaluation_batch", "bracket_core/localisation/global_localisation.py:evaluate_trace_relevance", "bracket_core/domain_analysis.py:_call_openai_api", "bracket_core/localisation/horizontal_localisation.py:evaluate_domains_at_level", "bracket_core/hierarchical_domain_file_mapper.py:_classify_files_to_specific_domain", "bracket_core/hierarchical_domain_trace_builder.py:_process_file_functions_individually", "bracket_core/localisation/global_localisation.py:evaluate_domain_relevance", "bracket_core/hierarchical_domain_file_mapper.py:_process_domain_specific_request", "bracket_core/localisation/horizontal_localisation.py:extract_relevant_functions", "bracket_core/domain_diagram_generator.py:_send_api_request", "bracket_core/localisation/global_localisation.py:extract_relevant_functions", "bracket_core/domain_diagram_generator.py:generate_all_diagrams", "bracket_core/localisation/horizontal_localisation.py:process_function_evaluation_batch"]}, "bracket_core/logger/null_progress.py:error": {"name": "error", "filepath": "bracket_core/logger/null_progress.py", "callees": [], "callers": []}, "bracket_core/generate_domain_taxonomy.py:generate_domain_taxonomy": {"name": "generate_domain_taxonomy", "filepath": "bracket_core/generate_domain_taxonomy.py", "callees": ["os", "DomainTaxonomyMapper", "traceback"], "callers": ["bracket_core/generate_domain_taxonomy.py:main"]}, "DomainTaxonomyMapper": {"name": "DomainTaxonomyMapper", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/generate_domain_taxonomy.py:generate_domain_taxonomy"]}, "traceback": {"name": "traceback", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/generate_domain_taxonomy.py:generate_domain_taxonomy", "bracket_core/localisation/test_horizontal.py:test_openai_api", "bracket_core/localisation/minimal_horizontal.py:load_domain_taxonomy", "bracket_core/localisation/minimal_horizontal.py:build_domain_hierarchy", "bracket_core/file_call_graph_builder.py:build_file_call_graph", "bracket_core/run_hierarchical_mapper.py:run_mapper", "bracket_core/localisation/test_horizontal.py:test_domain_evaluation", "bracket_core/generate_domain_taxonomy.py:main", "bracket_core/file_call_graph_builder.py:main", "bracket_core/hierarchical_domain_trace_builder.py:_process_chunk_with_semaphore", "bracket_core/localisation/horizontal_localisation.py:load_domain_taxonomy", "bracket_core/domain_taxonomy_mapper.py:read_domain_diagrams", "bracket_core/hierarchical_domain_file_mapper.py:map_files_to_domains_hierarchically", "bracket_core/domain_taxonomy_token_analyzer.py:main", "bracket_core/localisation/horizontal_localisation.py:load_semantic_documented_functions", "bracket_core/domain_file_mapper.py:map_files_to_domains", "bracket_core/irl.py:generate_significant_functions_yaml", "bracket_core/localisation/horizontal_localisation.py:build_domain_hierarchy", "bracket_core/global_codebase_explainer.py:generate_questions", "bracket_core/irl.py:create_hierarchical_domains", "bracket_core/irl.py:build_file_call_graph", "bracket_core/leaf_node_token_calculator.py:main", "bracket_core/global_codebase_explainer.py:generate_explanation", "bracket_core/irl.py:map_files_to_domains", "bracket_core/hierarchical_domain_file_mapper.py:_save_current_mappings_to_disk", "bracket_core/domain_file_mapper.py:main", "bracket_core/hierarchical_domain_file_mapper.py:_process_batch", "bracket_core/irl.py:build_domain_traces", "bracket_core/domain_taxonomy_mapper.py:map_taxonomy", "bracket_core/enhanced_domain_trace_builder.py:main", "bracket_core/hierarchical_domain_trace_builder.py:_process_file_functions_together", "bracket_core/localisation/horizontal_localisation.py:evaluate_domains_at_level", "bracket_core/irl.py:generate_domain_diagrams", "bracket_core/irl.py:generate_domain_taxonomy", "bracket_core/hierarchical_domain_trace_builder.py:_process_trace_hierarchical", "bracket_core/irl.py:generate_codebase_explanation", "bracket_core/hierarchical_domain_trace_builder.py:normalize_function_paths", "bracket_core/localisation/horizontal_localisation.py:explore_domain_hierarchy", "bracket_core/domain_diagram_generator.py:_process_api_response", "bracket_core/hierarchical_domain_trace_builder.py:main", "bracket_core/domain_diagram_generator.py:generate_all_diagrams", "bracket_core/localisation/horizontal_localisation.py:third_pass_analyze_relevant_functions", "bracket_core/hierarchical_domain_file_mapper.py:main", "bracket_core/localisation/global_localisation.py:third_pass_analyze_relevant_functions", "bracket_core/domain_diagram_generator.py:main", "bracket_core/localisation/horizontal_localisation.py:find_relevant_functions", "bracket_core/localisation/global_localisation.py:second_pass_find_relevant_functions", "bracket_core/localisation/global_localisation.py:find_relevant_functions", "bracket_core/parsing_repomap.py:enrich_with_line_numbers", "bracket_core/parsing_repomap.py:enrich_with_nuanced_callgraph"]}, "bracket_core/logger/factory.py:create_logger": {"name": "create_logger", "filepath": "bracket_core/logger/factory.py", "callees": ["RichProgressLogger", "PrintProgressLogger", "<PERSON>ullProgressLogger", "ClassVar"], "callers": []}, "RichProgressLogger": {"name": "RichProgressLogger", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/logger/factory.py:create_logger"]}, "PrintProgressLogger": {"name": "PrintProgressLogger", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/logger/factory.py:create_logger"]}, "NullProgressLogger": {"name": "<PERSON>ullProgressLogger", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/logger/factory.py:create_logger"]}, "ClassVar": {"name": "ClassVar", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/logger/factory.py:create_logger"]}, "bracket_core/llm/oai/chat_openai.py:__init__": {"name": "__init__", "filepath": "bracket_core/llm/oai/chat_openai.py", "callees": ["OpenAILLMImpl"], "callers": ["bracket_core/llm/oai/chat_openai.py:generate", "bracket_core/llm/oai/chat_openai.py:stream_generate", "bracket_core/llm/oai/chat_openai.py:agenerate", "bracket_core/llm/oai/chat_openai.py:astream_generate", "bracket_core/llm/oai/chat_openai.py:_generate", "bracket_core/llm/oai/chat_openai.py:_stream_generate", "bracket_core/llm/oai/chat_openai.py:_agenerate", "bracket_core/llm/oai/chat_openai.py:_astream_generate"]}, "OpenAILLMImpl": {"name": "OpenAILLMImpl", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/llm/oai/chat_openai.py:__init__", "bracket_core/llm/oai/embedding.py:__init__"]}, "bracket_core/logger/null_progress.py:warning": {"name": "warning", "filepath": "bracket_core/logger/null_progress.py", "callees": [], "callers": []}, "bracket_core/domain_classifier.py:__init__": {"name": "__init__", "filepath": "bracket_core/domain_classifier.py", "callees": ["os"], "callers": ["bracket_core/domain_classifier.py:main"]}, "bracket_core/domain_trace_token_analyzer.py:num_tokens_from_string": {"name": "num_tokens_from_string", "filepath": "bracket_core/domain_trace_token_analyzer.py", "callees": ["tiktoken"], "callers": []}, "bracket_core/llm/oai/embedding.py:__init__": {"name": "__init__", "filepath": "bracket_core/llm/oai/embedding.py", "callees": ["OpenAILLMImpl", "tiktoken"], "callers": ["bracket_core/llm/oai/embedding.py:embed", "bracket_core/llm/oai/embedding.py:_embed_with_retry", "bracket_core/llm/oai/embedding.py:_aembed_with_retry"]}, "bracket_core/cli.py:file_call_graph": {"name": "file_call_graph", "filepath": "bracket_core/cli.py", "callees": ["typer", "print", "FileCallGraphBuilder"], "callers": []}, "typer": {"name": "typer", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/cli.py:file_call_graph", "bracket_core/cli.py:map_files_to_domains", "bracket_core/cli.py:normalize_function_paths", "bracket_core/cli.py:build_domain_traces", "bracket_core/cli.py:visualize", "bracket_core/cli.py:analyze"]}, "print": {"name": "print", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/cli.py:file_call_graph", "bracket_core/cli.py:map_files_to_domains", "bracket_core/cli.py:normalize_function_paths", "bracket_core/cli.py:build_domain_traces", "bracket_core/cli.py:visualize", "bracket_core/cli.py:analyze"]}, "FileCallGraphBuilder": {"name": "FileCallGraphBuilder", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/cli.py:file_call_graph", "bracket_core/irl.py:build_file_call_graph"]}, "bracket_core/logger/print_progress.py:stop": {"name": "stop", "filepath": "bracket_core/logger/print_progress.py", "callees": [], "callers": []}, "bracket_core/logger/rich_progress.py:dispose": {"name": "dispose", "filepath": "bracket_core/logger/rich_progress.py", "callees": ["Live"], "callers": []}, "Live": {"name": "Live", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/logger/rich_progress.py:dispose", "bracket_core/logger/rich_progress.py:__init__", "bracket_core/logger/rich_progress.py:force_refresh", "bracket_core/logger/rich_progress.py:stop"]}, "bracket_core/logger/null_progress.py:info": {"name": "info", "filepath": "bracket_core/logger/null_progress.py", "callees": [], "callers": []}, "bracket_core/llm/oai/base.py:set_clients": {"name": "set_clients", "filepath": "bracket_core/llm/oai/base.py", "callees": [], "callers": ["bracket_core/llm/oai/base.py:_create_openai_client"]}, "bracket_core/logger/progress.py:__init__": {"name": "__init__", "filepath": "bracket_core/logger/progress.py", "callees": [], "callers": ["bracket_core/logger/progress.py:progress_ticker", "bracket_core/logger/progress.py:progress_iterable"]}, "bracket_core/domain_taxonomy_token_analyzer.py:num_tokens_from_string": {"name": "num_tokens_from_string", "filepath": "bracket_core/domain_taxonomy_token_analyzer.py", "callees": ["tiktoken"], "callers": []}, "bracket_core/logger/print_progress.py:force_refresh": {"name": "force_refresh", "filepath": "bracket_core/logger/print_progress.py", "callees": [], "callers": []}, "bracket_core/llm/base.py:agenerate": {"name": "agenerate", "filepath": "bracket_core/llm/base.py", "callees": [], "callers": []}, "bracket_core/llm/tokens.py:string_from_tokens": {"name": "string_from_tokens", "filepath": "bracket_core/llm/tokens.py", "callees": ["tiktoken"], "callers": []}, "bracket_core/localisation/minimal_horizontal.py:__init__": {"name": "__init__", "filepath": "bracket_core/localisation/minimal_horizontal.py", "callees": [], "callers": ["bracket_core/localisation/minimal_horizontal.py:build_domain_hierarchy", "bracket_core/localisation/minimal_horizontal.py:main"]}, "bracket_core/logger/base.py:__call__": {"name": "__call__", "filepath": "bracket_core/logger/base.py", "callees": [], "callers": []}, "bracket_core/logger/null_progress.py:success": {"name": "success", "filepath": "bracket_core/logger/null_progress.py", "callees": [], "callers": []}, "bracket_core/localisation/test_horizontal.py:test_openai_api": {"name": "test_openai_api", "filepath": "bracket_core/localisation/test_horizontal.py", "callees": ["get_openai_api_key", "aiohttp", "json", "traceback"], "callers": ["bracket_core/localisation/test_horizontal.py:main"]}, "get_openai_api_key": {"name": "get_openai_api_key", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/localisation/test_horizontal.py:test_openai_api", "bracket_core/global_codebase_explainer.py:__init__", "bracket_core/localisation/horizontal_localisation.py:__init__", "bracket_core/domain_analysis.py:__init__", "bracket_core/localisation/test_horizontal.py:test_domain_evaluation", "bracket_core/domain_trace_builder.py:__init__", "bracket_core/domain_file_mapper.py:__init__", "bracket_core/hierarchical_domain_file_mapper.py:map_files_to_domains_hierarchically", "bracket_core/localisation/global_localisation.py:__init__", "bracket_core/hierarchical_domain_file_mapper.py:_process_batch", "bracket_core/hierarchical_domain_file_mapper.py:_classify_files_to_specific_domain"]}, "aiohttp": {"name": "aiohttp", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/localisation/test_horizontal.py:test_openai_api", "bracket_core/localisation/global_localisation.py:call_api", "bracket_core/domain_file_mapper.py:call_api", "bracket_core/domain_trace_builder.py:call_api", "bracket_core/localisation/test_horizontal.py:test_domain_evaluation", "bracket_core/documenting.py:process_api_requests_from_file", "bracket_core/domain_classifier.py:_call_openai_api", "bracket_core/domain_trace_builder.py:classify_functions", "bracket_core/domain_file_mapper.py:_classify_files_to_domains", "bracket_core/documenting.py:call_api", "bracket_core/domain_diagram_generator.py:_call_openai_api", "bracket_core/enhanced_domain_trace_builder.py:_call_openai_api", "bracket_core/localisation/horizontal_localisation.py:process_domain_evaluation_batch", "bracket_core/localisation/horizontal_localisation.py:process_openai_request", "bracket_core/localisation/global_localisation.py:evaluate_trace_relevance", "bracket_core/localisation/horizontal_localisation.py:process_openrouter_request", "bracket_core/domain_analysis.py:_call_openai_api", "bracket_core/localisation/horizontal_localisation.py:evaluate_domains_at_level", "bracket_core/hierarchical_domain_file_mapper.py:_classify_files_to_specific_domain", "bracket_core/localisation/horizontal_localisation.py:_process_openai_domain_request", "bracket_core/localisation/horizontal_localisation.py:_process_openrouter_domain_request", "bracket_core/localisation/global_localisation.py:evaluate_domain_relevance", "bracket_core/hierarchical_domain_file_mapper.py:_process_domain_specific_request", "bracket_core/localisation/horizontal_localisation.py:extract_relevant_functions", "bracket_core/localisation/global_localisation.py:extract_relevant_functions", "bracket_core/localisation/horizontal_localisation.py:process_function_evaluation_batch", "bracket_core/localisation/horizontal_localisation.py:process_function_evaluation_request", "bracket_core/localisation/horizontal_localisation.py:third_pass_analyze_relevant_functions", "bracket_core/localisation/global_localisation.py:third_pass_analyze_relevant_functions"]}, "json": {"name": "json", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/localisation/test_horizontal.py:test_openai_api", "bracket_core/localisation/minimal_horizontal.py:load_domain_taxonomy", "bracket_core/llm/text_utils.py:try_parse_json_object", "bracket_core/localisation/global_localisation.py:call_api", "bracket_core/domain_file_mapper.py:call_api", "bracket_core/mermaid_generator.py:generate_mermaid_diagrams", "bracket_core/domain_trace_token_analyzer.py:read_taxonomy_json", "bracket_core/domain_taxonomy_token_analyzer.py:read_taxonomy_json", "bracket_core/localisation/test_horizontal.py:test_domain_evaluation", "bracket_core/llm/anthropic/client.py:generate_with_json_mode", "bracket_core/leaf_node_token_calculator.py:read_function_data", "bracket_core/global_codebase_explainer.py:read_taxonomy_json", "bracket_core/file_call_graph_builder.py:_group_functions_by_file", "bracket_core/mermaid_generator.py:generate_diagram_by_type", "bracket_core/documenting.py:process_api_requests_from_file", "bracket_core/call_graph_generator.py:merge_yaml_files", "bracket_core/domain_taxonomy_mapper.py:read_diagram_name_mapping", "bracket_core/mermaid_generator.py:_generate_diagram_by_type", "bracket_core/domain_analysis.py:_process_chunk", "bracket_core/domain_diagram_generator.py:read_function_data", "bracket_core/llm/openrouter/client.py:generate_json", "bracket_core/call_graph_generator.py:generate_basic_callgraph", "bracket_core/localisation/horizontal_localisation.py:load_domain_taxonomy", "bracket_core/leaf_node_token_calculator.py:prepare_leaf_domain_request", "bracket_core/irl.py:save_graph_as_parquet", "bracket_core/irl.py:convert_mixed_types", "bracket_core/domain_file_mapper.py:_classify_files_to_domains", "bracket_core/domain_analysis.py:_merge_domain_results", "bracket_core/call_graph_generator.py:main", "bracket_core/mermaid_generator.py:_generate_comprehensive_architecture_diagrams", "bracket_core/irl.py:generate_significant_functions_yaml", "bracket_core/documenting.py:append_to_jsonl", "bracket_core/documenting.py:num_tokens_consumed_from_request", "bracket_core/domain_diagram_generator.py:_call_openai_api", "bracket_core/domain_trace_builder.py:save_classification_results", "bracket_core/global_codebase_explainer.py:generate_questions", "bracket_core/domain_diagram_generator.py:generate_leaf_diagram", "bracket_core/documenting.py:document_functions", "bracket_core/domain_taxonomy_mapper.py:save_taxonomy_json", "bracket_core/domain_analysis.py:_consolidate_domains", "bracket_core/localisation/global_localisation.py:load_domain_taxonomy", "bracket_core/localisation/horizontal_localisation.py:process_openai_request", "bracket_core/domain_analysis.py:significant_fns_to_domain_structs", "bracket_core/documenting.py:document_functions_with_significance", "bracket_core/localisation/global_localisation.py:evaluate_trace_relevance", "bracket_core/localisation/horizontal_localisation.py:process_openrouter_request", "bracket_core/domain_diagram_generator.py:generate_combined_diagram", "bracket_core/hierarchical_domain_file_mapper.py:_classify_files_to_specific_domain", "bracket_core/domain_diagram_generator.py:_prepare_leaf_domain_request", "bracket_core/localisation/horizontal_localisation.py:_process_openai_domain_request", "bracket_core/documenting.py:document_functions_with_context_significance", "bracket_core/hybrid_kg.py:save_graph_as_parquet", "bracket_core/localisation/horizontal_localisation.py:_process_openrouter_domain_request", "bracket_core/hybrid_kg.py:convert_mixed_types", "bracket_core/localisation/global_localisation.py:evaluate_domain_relevance", "bracket_core/hierarchical_domain_file_mapper.py:_process_domain_specific_request", "bracket_core/domain_diagram_generator.py:_prepare_intermediate_domain_request", "bracket_core/localisation/global_localisation.py:extract_relevant_functions", "bracket_core/domain_diagram_generator.py:generate_all_diagrams", "bracket_core/localisation/horizontal_localisation.py:process_function_evaluation_request", "bracket_core/localisation/horizontal_localisation.py:third_pass_analyze_relevant_functions", "bracket_core/localisation/global_localisation.py:third_pass_analyze_relevant_functions", "bracket_core/localisation/global_localisation.py:main", "bracket_core/parsing_repomap.py:enrich_with_nuanced_callgraph"]}, "bracket_core/logger/print_progress.py:error": {"name": "error", "filepath": "bracket_core/logger/print_progress.py", "callees": [], "callers": []}, "bracket_core/llm/api_keys.py:get_openai_api_key": {"name": "get_openai_api_key", "filepath": "bracket_core/llm/api_keys.py", "callees": ["os"], "callers": ["bracket_core/llm/api_keys.py:get_api_key"]}, "bracket_core/logger/rich_progress.py:console": {"name": "console", "filepath": "bracket_core/logger/rich_progress.py", "callees": [], "callers": []}, "bracket_core/logger/base.py:dispose": {"name": "dispose", "filepath": "bracket_core/logger/base.py", "callees": [], "callers": []}, "bracket_core/call_graph_generator.py:create_patched_callgraph": {"name": "create_patched_callgraph", "filepath": "bracket_core/call_graph_generator.py", "callees": ["os"], "callers": ["bracket_core/call_graph_generator.py:find_callgraph_executable"]}, "bracket_core/utils/token_counter.py:count_tokens_and_loc_in_directory": {"name": "count_tokens_and_loc_in_directory", "filepath": "bracket_core/utils/token_counter.py", "callees": ["os", "bracket_core/utils/token_counter.py:count_tokens_and_loc_in_file"], "callers": []}, "bracket_core/logger/progress.py:__call__": {"name": "__call__", "filepath": "bracket_core/logger/progress.py", "callees": ["Callable", "bracket_core/logger/progress.py:Progress"], "callers": []}, "Callable": {"name": "Callable", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/logger/progress.py:__call__", "bracket_core/logger/progress.py:done"]}, "bracket_core/logger/progress.py:Progress": {"name": "Progress", "filepath": "bracket_core/logger/progress.py", "callees": [], "callers": ["bracket_core/logger/progress.py:__call__", "bracket_core/logger/progress.py:done"]}, "bracket_core/llm/text_utils.py:chunk_text": {"name": "chunk_text", "filepath": "bracket_core/llm/text_utils.py", "callees": ["tiktoken", "bracket_core/llm/text_utils.py:batched"], "callers": []}, "bracket_core/logger/print_progress.py:warning": {"name": "warning", "filepath": "bracket_core/logger/print_progress.py", "callees": [], "callers": []}, "bracket_core/logger/base.py:child": {"name": "child", "filepath": "bracket_core/logger/base.py", "callees": [], "callers": []}, "bracket_core/logger/rich_progress.py:group": {"name": "group", "filepath": "bracket_core/logger/rich_progress.py", "callees": [], "callers": []}, "bracket_core/llm/base.py:astream_generate": {"name": "astream_generate", "filepath": "bracket_core/llm/base.py", "callees": [], "callers": []}, "bracket_core/domain_trace_builder.py:from_path": {"name": "from_path", "filepath": "bracket_core/domain_trace_builder.py", "callees": ["bracket_core/domain_trace_builder.py:DomainTrace"], "callers": ["bracket_core/domain_trace_builder.py:build_domain_traces", "bracket_core/domain_trace_builder.py:traverse_areas"]}, "bracket_core/domain_trace_builder.py:DomainTrace": {"name": "DomainTrace", "filepath": "bracket_core/domain_trace_builder.py", "callees": [], "callers": ["bracket_core/domain_trace_builder.py:from_path"]}, "bracket_core/localisation/minimal_horizontal.py:load_domain_taxonomy": {"name": "load_domain_taxonomy", "filepath": "bracket_core/localisation/minimal_horizontal.py", "callees": ["os", "json", "traceback"], "callers": ["bracket_core/localisation/minimal_horizontal.py:main"]}, "bracket_core/logger/rich_progress.py:tree": {"name": "tree", "filepath": "bracket_core/logger/rich_progress.py", "callees": [], "callers": []}, "bracket_core/logger/base.py:force_refresh": {"name": "force_refresh", "filepath": "bracket_core/logger/base.py", "callees": [], "callers": []}, "bracket_core/logger/print_progress.py:info": {"name": "info", "filepath": "bracket_core/logger/print_progress.py", "callees": [], "callers": []}, "bracket_core/logger/rich_progress.py:live": {"name": "live", "filepath": "bracket_core/logger/rich_progress.py", "callees": [], "callers": []}, "bracket_core/logger/base.py:stop": {"name": "stop", "filepath": "bracket_core/logger/base.py", "callees": [], "callers": []}, "bracket_core/llm/oai/base.py:async_client": {"name": "async_client", "filepath": "bracket_core/llm/oai/base.py", "callees": [], "callers": []}, "bracket_core/logger/progress.py:done": {"name": "done", "filepath": "bracket_core/logger/progress.py", "callees": ["Callable", "bracket_core/logger/progress.py:Progress"], "callers": []}, "bracket_core/logger/print_progress.py:success": {"name": "success", "filepath": "bracket_core/logger/print_progress.py", "callees": [], "callers": []}, "bracket_core/logger/rich_progress.py:__init__": {"name": "__init__", "filepath": "bracket_core/logger/rich_progress.py", "callees": ["<PERSON><PERSON><PERSON>", "Group", "Spinner", "Tree", "Live", "Progress", "TimeElapsedColumn", "bracket_core/logger/rich_progress.py:refresh"], "callers": ["bracket_core/logger/rich_progress.py:child"]}, "Console": {"name": "<PERSON><PERSON><PERSON>", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/logger/rich_progress.py:__init__", "bracket_core/logger/rich_progress.py:error", "bracket_core/logger/rich_progress.py:warning", "bracket_core/logger/rich_progress.py:success", "bracket_core/logger/rich_progress.py:info"]}, "Group": {"name": "Group", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/logger/rich_progress.py:__init__"]}, "Spinner": {"name": "Spinner", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/logger/rich_progress.py:__init__"]}, "Tree": {"name": "Tree", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/logger/rich_progress.py:__init__"]}, "Progress": {"name": "Progress", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/logger/rich_progress.py:__init__", "bracket_core/logger/rich_progress.py:__call__"]}, "TimeElapsedColumn": {"name": "TimeElapsedColumn", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/logger/rich_progress.py:__init__"]}, "bracket_core/logger/rich_progress.py:refresh": {"name": "refresh", "filepath": "bracket_core/logger/rich_progress.py", "callees": ["asyncio", "bracket_core/logger/rich_progress.py:force_refresh"], "callers": ["bracket_core/logger/rich_progress.py:__init__", "bracket_core/logger/rich_progress.py:__call__"]}, "bracket_core/file_call_graph_builder.py:__init__": {"name": "__init__", "filepath": "bracket_core/file_call_graph_builder.py", "callees": ["os"], "callers": ["bracket_core/file_call_graph_builder.py:main"]}, "bracket_core/llm/oai/openai.py:generate": {"name": "generate", "filepath": "bracket_core/llm/oai/openai.py", "callees": ["Retrying", "stop_after_attempt", "wait_exponential_jitter", "retry_if_exception_type", "bracket_core/llm/oai/openai.py:_generate"], "callers": []}, "Retrying": {"name": "Retrying", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/llm/oai/openai.py:generate", "bracket_core/llm/oai/chat_openai.py:generate", "bracket_core/llm/oai/chat_openai.py:stream_generate", "bracket_core/llm/oai/embedding.py:_embed_with_retry"]}, "stop_after_attempt": {"name": "stop_after_attempt", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/llm/oai/openai.py:generate", "bracket_core/llm/oai/chat_openai.py:generate", "bracket_core/llm/oai/openai.py:agenerate", "bracket_core/llm/oai/chat_openai.py:stream_generate", "bracket_core/llm/oai/chat_openai.py:agenerate", "bracket_core/llm/oai/embedding.py:_embed_with_retry", "bracket_core/llm/oai/chat_openai.py:astream_generate", "bracket_core/llm/oai/embedding.py:_aembed_with_retry"]}, "wait_exponential_jitter": {"name": "wait_exponential_jitter", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/llm/oai/openai.py:generate", "bracket_core/llm/oai/chat_openai.py:generate", "bracket_core/llm/oai/openai.py:agenerate", "bracket_core/llm/oai/chat_openai.py:stream_generate", "bracket_core/llm/oai/chat_openai.py:agenerate", "bracket_core/llm/oai/embedding.py:_embed_with_retry", "bracket_core/llm/oai/chat_openai.py:astream_generate", "bracket_core/llm/oai/embedding.py:_aembed_with_retry"]}, "retry_if_exception_type": {"name": "retry_if_exception_type", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/llm/oai/openai.py:generate", "bracket_core/llm/oai/chat_openai.py:generate", "bracket_core/llm/oai/openai.py:agenerate", "bracket_core/llm/oai/chat_openai.py:stream_generate", "bracket_core/llm/oai/chat_openai.py:agenerate", "bracket_core/llm/oai/embedding.py:_embed_with_retry", "bracket_core/llm/oai/chat_openai.py:astream_generate", "bracket_core/llm/oai/embedding.py:_aembed_with_retry"]}, "bracket_core/llm/oai/openai.py:_generate": {"name": "_generate", "filepath": "bracket_core/llm/oai/openai.py", "callees": ["bracket_core/llm/oai/openai.py:__init__", "BaseLLMCallback"], "callers": ["bracket_core/llm/oai/openai.py:generate"]}, "bracket_core/enhanced_domain_trace_builder.py:__init__": {"name": "__init__", "filepath": "bracket_core/enhanced_domain_trace_builder.py", "callees": ["defaultdict"], "callers": ["bracket_core/enhanced_domain_trace_builder.py:get_domain_specific_functions", "bracket_core/enhanced_domain_trace_builder.py:build_and_classify", "bracket_core/enhanced_domain_trace_builder.py:build_domain_traces", "bracket_core/enhanced_domain_trace_builder.py:main"]}, "defaultdict": {"name": "defaultdict", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/enhanced_domain_trace_builder.py:__init__", "bracket_core/hierarchical_domain_file_mapper.py:__init__", "bracket_core/call_graph_generator.py:group_files_by_language", "bracket_core/parsing_repomap.py:__init__", "bracket_core/enhanced_domain_trace_builder.py:map_functions_to_files", "bracket_core/parsing_repomap.py:enrich_with_line_numbers"]}, "bracket_core/llm/text_utils.py:try_parse_json_object": {"name": "try_parse_json_object", "filepath": "bracket_core/llm/text_utils.py", "callees": ["json", "re", "repair_json"], "callers": []}, "repair_json": {"name": "repair_json", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/llm/text_utils.py:try_parse_json_object"]}, "bracket_core/utils/md_token_counter.py:main": {"name": "main", "filepath": "bracket_core/utils/md_token_counter.py", "callees": ["bracket_core/utils/md_token_counter.py:count_tokens_in_md_files", "Dict", "os"], "callers": []}, "Dict": {"name": "Dict", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/utils/md_token_counter.py:main", "bracket_core/localisation/global_localisation.py:call_api", "bracket_core/domain_file_mapper.py:call_api", "bracket_core/utils/token_counter.py:main", "bracket_core/domain_trace_token_analyzer.py:analyze_node", "bracket_core/domain_taxonomy_token_analyzer.py:find_leaf_components", "bracket_core/domain_analysis.py:_split_functions_into_chunks", "bracket_core/parsing_repomap.py:add_import", "bracket_core/hierarchical_domain_file_mapper.py:extract_domain_hierarchy", "bracket_core/hierarchical_domain_file_mapper.py:process_domain", "bracket_core/global_codebase_explainer.py:extract_domain_diagrams", "bracket_core/mermaid_generator.py:_generate_diagram_by_type", "bracket_core/hierarchical_domain_trace_builder.py:_chunk_function_data", "bracket_core/domain_trace_builder.py:build_domain_traces", "bracket_core/hierarchical_domain_trace_builder.py:_process_chunk_with_semaphore", "bracket_core/domain_file_mapper.py:extract_top_level_domains", "bracket_core/domain_file_mapper.py:_generate_domain_description", "bracket_core/domain_trace_builder.py:classify_functions", "bracket_core/domain_taxonomy_mapper.py:build_taxonomy_tree", "bracket_core/domain_file_mapper.py:_classify_files_to_domains", "bracket_core/domain_taxonomy_mapper.py:_process_area", "bracket_core/hybrid_kg.py:_process_function_calls", "bracket_core/domain_taxonomy_mapper.py:map_functions_to_taxonomy", "bracket_core/domain_taxonomy_mapper.py:map_diagrams_to_taxonomy", "bracket_core/enhanced_domain_trace_builder.py:classify_functions_with_reduced_search_space", "bracket_core/domain_trace_builder.py:save_classification_results", "bracket_core/leaf_node_token_calculator.py:save_analysis_results", "bracket_core/domain_taxonomy_mapper.py:add_combined_diagrams_to_json", "bracket_core/localisation/horizontal_localisation.py:process_openai_request", "bracket_core/hierarchical_domain_trace_builder.py:_process_file_functions_together", "bracket_core/localisation/horizontal_localisation.py:process_openrouter_request", "bracket_core/domain_diagram_generator.py:generate_combined_diagram", "bracket_core/hierarchical_domain_file_mapper.py:_prepare_file_info", "bracket_core/domain_analysis.py:_call_openai_api", "bracket_core/hierarchical_domain_trace_builder.py:_process_file_functions_individually", "bracket_core/localisation/horizontal_localisation.py:explore_domain_hierarchy", "bracket_core/domain_diagram_generator.py:generate_all_diagrams", "bracket_core/domain_diagram_generator.py:main"]}, "bracket_core/llm/base.py:embed": {"name": "embed", "filepath": "bracket_core/llm/base.py", "callees": [], "callers": []}, "bracket_core/parsing_repomap.py:get_base_weight": {"name": "get_base_weight", "filepath": "bracket_core/parsing_repomap.py", "callees": [], "callers": ["bracket_core/parsing_repomap.py:calculate_relationship_weight"]}, "bracket_core/logger/base.py:info": {"name": "info", "filepath": "bracket_core/logger/base.py", "callees": [], "callers": []}, "bracket_core/logger/progress.py:progress_ticker": {"name": "progress_ticker", "filepath": "bracket_core/logger/progress.py", "callees": ["bracket_core/logger/progress.py:__init__"], "callers": []}, "bracket_core/llm/anthropic/client.py:generate": {"name": "generate", "filepath": "bracket_core/llm/anthropic/client.py", "callees": ["bracket_core/llm/anthropic/client.py:__init__"], "callers": []}, "bracket_core/localisation/global_localisation.py:call_api": {"name": "call_api", "filepath": "bracket_core/localisation/global_localisation.py", "callees": ["Dict", "bracket_core/localisation/global_localisation.py:estimate_token_count", "get_openrouter_api_key", "get_openrouter_client", "asyncio", "aiohttp", "json"], "callers": []}, "bracket_core/localisation/global_localisation.py:estimate_token_count": {"name": "estimate_token_count", "filepath": "bracket_core/localisation/global_localisation.py", "callees": ["bracket_core/localisation/global_localisation.py:count_tokens_with_tiktoken"], "callers": ["bracket_core/localisation/global_localisation.py:call_api", "bracket_core/localisation/global_localisation.py:extract_relevant_functions"]}, "get_openrouter_client": {"name": "get_openrouter_client", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/localisation/global_localisation.py:call_api", "bracket_core/hierarchical_domain_file_mapper.py:__init__", "bracket_core/global_codebase_explainer.py:__init__", "bracket_core/localisation/horizontal_localisation.py:__init__", "bracket_core/domain_analysis.py:__init__", "bracket_core/localisation/global_localisation.py:__init__", "bracket_core/hierarchical_domain_file_mapper.py:_process_domain_specific_request"]}, "bracket_core/llm/api_keys.py:get_openrouter_api_key": {"name": "get_openrouter_api_key", "filepath": "bracket_core/llm/api_keys.py", "callees": ["os", "logging"], "callers": ["bracket_core/llm/api_keys.py:get_api_key"]}, "logging": {"name": "logging", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/llm/api_keys.py:get_openrouter_api_key", "bracket_core/domain_file_mapper.py:call_api", "bracket_core/domain_trace_builder.py:call_api", "bracket_core/run_hierarchical_mapper.py:run_mapper", "bracket_core/documenting.py:process_api_requests_from_file", "bracket_core/documenting.py:call_api", "bracket_core/hierarchical_domain_file_mapper.py:_process_domain_specific_request", "bracket_core/parsing_repomap.py:read_text", "bracket_core/parsing_repomap.py:tool_error", "bracket_core/parsing_repomap.py:tool_output"]}, "bracket_core/llm/base.py:aembed": {"name": "aembed", "filepath": "bracket_core/llm/base.py", "callees": [], "callers": []}, "bracket_core/llm/oai/base.py:sync_client": {"name": "sync_client", "filepath": "bracket_core/llm/oai/base.py", "callees": [], "callers": []}, "bracket_core/mermaid_generator.py:generate_diagrams_from_json": {"name": "generate_diagrams_from_json", "filepath": "bracket_core/mermaid_generator.py", "callees": ["bracket_core/mermaid_generator.py:generate_mermaid_diagrams"], "callers": []}, "bracket_core/mermaid_generator.py:generate_mermaid_diagrams": {"name": "generate_mermaid_diagrams", "filepath": "bracket_core/mermaid_generator.py", "callees": ["os", "json", "bracket_core/mermaid_generator.py:__init__", "bracket_core/mermaid_generator.py:_generate_diagram_by_type", "bracket_core/mermaid_generator.py:_generate_comprehensive_architecture_diagrams"], "callers": ["bracket_core/mermaid_generator.py:generate_diagrams_from_json"]}, "bracket_core/hierarchical_domain_trace_builder.py:__init__": {"name": "__init__", "filepath": "bracket_core/hierarchical_domain_trace_builder.py", "callees": ["asyncio"], "callers": ["bracket_core/hierarchical_domain_trace_builder.py:get_domain_specific_functions_hierarchical", "bracket_core/hierarchical_domain_trace_builder.py:_process_leaf_node_file_by_file", "bracket_core/hierarchical_domain_trace_builder.py:_process_file_for_trace", "bracket_core/hierarchical_domain_trace_builder.py:_process_trace_hierarchical", "bracket_core/hierarchical_domain_trace_builder.py:normalize_function_paths", "bracket_core/hierarchical_domain_trace_builder.py:build_and_classify", "bracket_core/hierarchical_domain_trace_builder.py:build_domain_traces", "bracket_core/hierarchical_domain_trace_builder.py:main"]}, "bracket_core/logger/base.py:success": {"name": "success", "filepath": "bracket_core/logger/base.py", "callees": [], "callers": []}, "bracket_core/utils/token_counter.py:count_tokens_and_loc_in_directory_no_tests": {"name": "count_tokens_and_loc_in_directory_no_tests", "filepath": "bracket_core/utils/token_counter.py", "callees": ["os", "bracket_core/utils/token_counter.py:count_tokens_and_loc_in_file"], "callers": ["bracket_core/utils/token_counter.py:main"]}, "bracket_core/domain_file_mapper.py:call_api": {"name": "call_api", "filepath": "bracket_core/domain_file_mapper.py", "callees": ["logging", "aiohttp", "time", "asyncio", "json", "Dict"], "callers": []}, "bracket_core/call_graph_generator.py:find_callgraph_executable": {"name": "find_callgraph_executable", "filepath": "bracket_core/call_graph_generator.py", "callees": ["os", "bracket_core/call_graph_generator.py:create_patched_callgraph"], "callers": ["bracket_core/call_graph_generator.py:main"]}, "bracket_core/leaf_node_token_calculator.py:__init__": {"name": "__init__", "filepath": "bracket_core/leaf_node_token_calculator.py", "callees": ["os"], "callers": ["bracket_core/leaf_node_token_calculator.py:build_domain_hierarchy", "bracket_core/leaf_node_token_calculator.py:_is_empty_domain_mapping", "bracket_core/leaf_node_token_calculator.py:prepare_leaf_domain_request", "bracket_core/leaf_node_token_calculator.py:analyze_leaf_domains", "bracket_core/leaf_node_token_calculator.py:save_analysis_results", "bracket_core/leaf_node_token_calculator.py:main"]}, "bracket_core/hybrid_kg.py:__init__": {"name": "__init__", "filepath": "bracket_core/hybrid_kg.py", "callees": [], "callers": ["bracket_core/hybrid_kg.py:_extract_all_function_definitions", "bracket_core/hybrid_kg.py:_create_graph", "bracket_core/hybrid_kg.py:_should_skip_function_call", "bracket_core/hybrid_kg.py:generate_hybrid_knowledge_graph"]}, "bracket_core/logger/progress.py:progress_iterable": {"name": "progress_iterable", "filepath": "bracket_core/logger/progress.py", "callees": ["bracket_core/logger/progress.py:__init__"], "callers": []}, "bracket_core/domain_diagram_generator.py:__init__": {"name": "__init__", "filepath": "bracket_core/domain_diagram_generator.py", "callees": ["os", "get_claude_client", "RateLimiter"], "callers": ["bracket_core/domain_diagram_generator.py:build_domain_hierarchy", "bracket_core/domain_diagram_generator.py:sort_domains_by_level", "bracket_core/domain_diagram_generator.py:_is_empty_domain_mapping", "bracket_core/domain_diagram_generator.py:generate_leaf_diagram", "bracket_core/domain_diagram_generator.py:generate_combined_diagram", "bracket_core/domain_diagram_generator.py:_prepare_leaf_domain_request", "bracket_core/domain_diagram_generator.py:_prepare_intermediate_domain_request", "bracket_core/domain_diagram_generator.py:_send_api_request", "bracket_core/domain_diagram_generator.py:_process_api_response", "bracket_core/domain_diagram_generator.py:generate_all_diagrams", "bracket_core/domain_diagram_generator.py:main"]}, "bracket_core/hierarchical_domain_file_mapper.py:__init__": {"name": "__init__", "filepath": "bracket_core/hierarchical_domain_file_mapper.py", "callees": ["time", "asyncio", "get_openrouter_client", "defaultdict"], "callers": ["bracket_core/hierarchical_domain_file_mapper.py:extract_domain_hierarchy", "bracket_core/hierarchical_domain_file_mapper.py:process_domain", "bracket_core/hierarchical_domain_file_mapper.py:get_domains_at_level", "bracket_core/hierarchical_domain_file_mapper.py:get_files_for_parent_domain", "bracket_core/hierarchical_domain_file_mapper.py:map_files_to_domains_hierarchically", "bracket_core/hierarchical_domain_file_mapper.py:_get_domain_level", "bracket_core/hierarchical_domain_file_mapper.py:_save_current_mappings_to_disk", "bracket_core/hierarchical_domain_file_mapper.py:_process_batch", "bracket_core/hierarchical_domain_file_mapper.py:_process_domain_specific_request", "bracket_core/hierarchical_domain_file_mapper.py:main"]}, "bracket_core/domain_analysis.py:count_tokens": {"name": "count_tokens", "filepath": "bracket_core/domain_analysis.py", "callees": [], "callers": ["bracket_core/domain_analysis.py:_split_functions_into_chunks", "bracket_core/domain_analysis.py:create_hierar_domain_struct"]}, "bracket_core/llm/oai/chat_openai.py:generate": {"name": "generate", "filepath": "bracket_core/llm/oai/chat_openai.py", "callees": ["Retrying", "stop_after_attempt", "wait_exponential_jitter", "retry_if_exception_type", "bracket_core/llm/oai/chat_openai.py:_generate", "bracket_core/llm/oai/chat_openai.py:__init__"], "callers": []}, "bracket_core/llm/oai/chat_openai.py:_generate": {"name": "_generate", "filepath": "bracket_core/llm/oai/chat_openai.py", "callees": ["bracket_core/llm/oai/chat_openai.py:__init__", "BaseLLMCallback"], "callers": ["bracket_core/llm/oai/chat_openai.py:generate"]}, "bracket_core/global_codebase_explainer.py:__init__": {"name": "__init__", "filepath": "bracket_core/global_codebase_explainer.py", "callees": ["get_openrouter_api_key", "get_openrouter_client", "get_openai_api_key"], "callers": ["bracket_core/global_codebase_explainer.py:create_explanation_prompt", "bracket_core/global_codebase_explainer.py:create_questions_prompt", "bracket_core/global_codebase_explainer.py:generate_explanation", "bracket_core/global_codebase_explainer.py:generate_codebase_explanation"]}, "bracket_core/file_call_graph_builder.py:read_functions_parquet": {"name": "read_functions_parquet", "filepath": "bracket_core/file_call_graph_builder.py", "callees": ["pandas"], "callers": ["bracket_core/file_call_graph_builder.py:build_file_call_graph"]}, "pandas": {"name": "pandas", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/file_call_graph_builder.py:read_functions_parquet", "bracket_core/leaf_node_token_calculator.py:read_function_data", "bracket_core/file_call_graph_builder.py:_group_functions_by_file", "bracket_core/domain_diagram_generator.py:read_function_data", "bracket_core/domain_taxonomy_token_analyzer.py:save_analysis_results", "bracket_core/domain_trace_token_analyzer.py:save_analysis_results", "bracket_core/domain_trace_builder.py:read_functions_parquet", "bracket_core/domain_trace_builder.py:prepare_function_data", "bracket_core/enhanced_domain_trace_builder.py:map_functions_to_files", "bracket_core/localisation/horizontal_localisation.py:load_semantic_documented_functions", "bracket_core/irl.py:generate_documentation", "bracket_core/irl.py:create_final_artifact", "bracket_core/enhanced_domain_trace_builder.py:prepare_domain_specific_function_data", "bracket_core/irl.py:generate_significant_functions_yaml", "bracket_core/documenting.py:save_simplified_csv", "bracket_core/leaf_node_token_calculator.py:save_analysis_results", "bracket_core/documenting.py:convert_parquet_to_simplified_csv", "bracket_core/documenting.py:document_functions", "bracket_core/hierarchical_domain_trace_builder.py:_process_file_for_trace", "bracket_core/localisation/global_localisation.py:load_semantic_documented_functions", "bracket_core/documenting.py:document_functions_with_significance", "bracket_core/hybrid_kg.py:graph_to_dataframes", "bracket_core/documenting.py:document_functions_with_context_significance", "bracket_core/documenting.py:convert_edges_parquet_to_csv", "bracket_core/parsing_repomap.py:save_graph_as_parquet", "bracket_core/parsing_repomap.py:to_dataframes"]}, "bracket_core/domain_classifier.py:classify_domains": {"name": "classify_domains", "filepath": "bracket_core/domain_classifier.py", "callees": ["yaml", "bracket_core/domain_classifier.py:_call_openai_api"], "callers": ["bracket_core/domain_classifier.py:main"]}, "yaml": {"name": "yaml", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/domain_classifier.py:classify_domains", "bracket_core/file_call_graph_builder.py:build_file_call_graph", "bracket_core/leaf_node_token_calculator.py:read_domain_traces", "bracket_core/enhanced_domain_trace_builder.py:read_domain_file_mappings", "bracket_core/domain_taxonomy_mapper.py:read_domain_analysis", "bracket_core/domain_taxonomy_mapper.py:read_domain_traces", "bracket_core/domain_analysis.py:_split_functions_into_chunks", "bracket_core/call_graph_generator.py:merge_yaml_files", "bracket_core/domain_diagram_generator.py:read_domain_traces", "bracket_core/domain_analysis.py:_process_chunk", "bracket_core/domain_trace_builder.py:read_domain_analysis_yaml", "bracket_core/domain_file_mapper.py:read_domain_yaml", "bracket_core/domain_file_mapper.py:read_file_call_graph_yaml", "bracket_core/hierarchical_domain_file_mapper.py:map_files_to_domains_hierarchically", "bracket_core/domain_file_mapper.py:map_files_to_domains", "bracket_core/domain_analysis.py:_merge_domain_results", "bracket_core/hierarchical_domain_trace_builder.py:read_domain_file_mappings", "bracket_core/irl.py:generate_significant_functions_yaml", "bracket_core/domain_trace_builder.py:save_classification_results", "bracket_core/domain_analysis.py:create_hierar_domain_struct", "bracket_core/domain_analysis.py:_consolidate_domains", "bracket_core/hierarchical_domain_file_mapper.py:_save_current_mappings_to_disk", "bracket_core/domain_analysis.py:significant_fns_to_domain_structs", "bracket_core/hierarchical_domain_trace_builder.py:normalize_function_paths"]}, "bracket_core/domain_classifier.py:_call_openai_api": {"name": "_call_openai_api", "filepath": "bracket_core/domain_classifier.py", "callees": ["aiohttp", "asyncio"], "callers": ["bracket_core/domain_classifier.py:classify_domains"]}, "bracket_core/cli.py:map_files_to_domains": {"name": "map_files_to_domains", "filepath": "bracket_core/cli.py", "callees": ["typer", "print", "asyncio", "HierarchicalDomainFileMapperIntegration", "DomainFileMapperIntegration"], "callers": []}, "HierarchicalDomainFileMapperIntegration": {"name": "HierarchicalDomainFileMapperIntegration", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/cli.py:map_files_to_domains"]}, "DomainFileMapperIntegration": {"name": "DomainFileMapperIntegration", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/cli.py:map_files_to_domains", "bracket_core/irl.py:map_files_to_domains"]}, "bracket_core/irl.py:__init__": {"name": "__init__", "filepath": "bracket_core/irl.py", "callees": ["os", "EnhancedRepoMap", "SimpleTokenCounter", "SimpleIO"], "callers": ["bracket_core/irl.py:main"]}, "EnhancedRepoMap": {"name": "EnhancedRepoMap", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/irl.py:__init__"]}, "SimpleTokenCounter": {"name": "SimpleTokenCounter", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/irl.py:__init__"]}, "SimpleIO": {"name": "SimpleIO", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/irl.py:__init__"]}, "bracket_core/domain_taxonomy_mapper.py:__init__": {"name": "__init__", "filepath": "bracket_core/domain_taxonomy_mapper.py", "callees": ["os"], "callers": ["bracket_core/domain_taxonomy_mapper.py:read_domain_diagrams", "bracket_core/domain_taxonomy_mapper.py:map_diagrams_to_taxonomy", "bracket_core/domain_taxonomy_mapper.py:find_combined_diagrams", "bracket_core/domain_taxonomy_mapper.py:map_taxonomy", "bracket_core/domain_taxonomy_mapper.py:main"]}, "bracket_core/localisation/minimal_horizontal.py:build_domain_hierarchy": {"name": "build_domain_hierarchy", "filepath": "bracket_core/localisation/minimal_horizontal.py", "callees": ["bracket_core/localisation/minimal_horizontal.py:DomainNode", "bracket_core/localisation/minimal_horizontal.py:process_domain", "bracket_core/localisation/minimal_horizontal.py:__init__", "traceback"], "callers": ["bracket_core/localisation/minimal_horizontal.py:main"]}, "bracket_core/localisation/minimal_horizontal.py:DomainNode": {"name": "DomainNode", "filepath": "bracket_core/localisation/minimal_horizontal.py", "callees": [], "callers": ["bracket_core/localisation/minimal_horizontal.py:build_domain_hierarchy"]}, "bracket_core/localisation/minimal_horizontal.py:process_domain": {"name": "process_domain", "filepath": "bracket_core/localisation/minimal_horizontal.py", "callees": [], "callers": ["bracket_core/localisation/minimal_horizontal.py:build_domain_hierarchy"]}, "bracket_core/test_mermaid_generator.py:patched_generate": {"name": "patched_generate", "filepath": "bracket_core/test_mermaid_generator.py", "callees": ["time", "os"], "callers": []}, "bracket_core/llm/get_client.py:get_claude_client": {"name": "get_claude_client", "filepath": "bracket_core/llm/get_client.py", "callees": ["get_anthropic_api_key", "ClaudeClient"], "callers": []}, "ClaudeClient": {"name": "ClaudeClient", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/llm/get_client.py:get_claude_client"]}, "bracket_core/domain_taxonomy_token_analyzer.py:__init__": {"name": "__init__", "filepath": "bracket_core/domain_taxonomy_token_analyzer.py", "callees": ["os"], "callers": ["bracket_core/domain_taxonomy_token_analyzer.py:find_leaf_components", "bracket_core/domain_taxonomy_token_analyzer.py:main"]}, "bracket_core/domain_trace_token_analyzer.py:__init__": {"name": "__init__", "filepath": "bracket_core/domain_trace_token_analyzer.py", "callees": ["os"], "callers": ["bracket_core/domain_trace_token_analyzer.py:analyze_node"]}, "bracket_core/llm/oai/embedding.py:embed": {"name": "embed", "filepath": "bracket_core/llm/oai/embedding.py", "callees": ["chunk_text", "bracket_core/llm/oai/embedding.py:_embed_with_retry", "bracket_core/llm/oai/embedding.py:__init__", "numpy"], "callers": []}, "chunk_text": {"name": "chunk_text", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/llm/oai/embedding.py:embed", "bracket_core/llm/oai/embedding.py:aembed"]}, "bracket_core/llm/oai/embedding.py:_embed_with_retry": {"name": "_embed_with_retry", "filepath": "bracket_core/llm/oai/embedding.py", "callees": ["Retrying", "stop_after_attempt", "wait_exponential_jitter", "retry_if_exception_type", "bracket_core/llm/oai/embedding.py:__init__"], "callers": ["bracket_core/llm/oai/embedding.py:embed"]}, "numpy": {"name": "numpy", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/llm/oai/embedding.py:embed", "bracket_core/llm/oai/embedding.py:aembed"]}, "bracket_core/parsing_repomap.py:__init__": {"name": "__init__", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:_generate_id", "networkx", "defaultdict", "os", "bracket_core/parsing_repomap.py:__init__"], "callers": ["bracket_core/parsing_repomap.py:get_context_hash", "bracket_core/parsing_repomap.py:add_module", "bracket_core/parsing_repomap.py:add_import", "bracket_core/parsing_repomap.py:add_symbol", "bracket_core/parsing_repomap.py:get_symbol", "bracket_core/parsing_repomap.py:resolve_symbol", "bracket_core/parsing_repomap.py:__init__", "bracket_core/parsing_repomap.py:get_mtime", "bracket_core/parsing_repomap.py:extract_imports", "bracket_core/parsing_repomap.py:_extract_python_imports", "bracket_core/parsing_repomap.py:_extract_js_imports", "bracket_core/parsing_repomap.py:_extract_java_imports", "bracket_core/parsing_repomap.py:get_tags", "bracket_core/parsing_repomap.py:resolve_reference", "bracket_core/parsing_repomap.py:_resolve_generic_reference", "bracket_core/parsing_repomap.py:_resolve_python_reference", "bracket_core/parsing_repomap.py:_resolve_javascript_reference", "bracket_core/parsing_repomap.py:_resolve_java_reference", "bracket_core/parsing_repomap.py:_resolve_method_call", "bracket_core/parsing_repomap.py:_infer_variable_type", "bracket_core/parsing_repomap.py:_resolve_class_module", "bracket_core/parsing_repomap.py:_analyze_method_return_type", "bracket_core/parsing_repomap.py:_find_inherited_method", "bracket_core/parsing_repomap.py:_type_inference_matches", "bracket_core/parsing_repomap.py:_find_parent_classes", "bracket_core/parsing_repomap.py:_resolve_class_to_module", "bracket_core/parsing_repomap.py:_resolve_type_reference", "bracket_core/parsing_repomap.py:is_test_file"]}, "bracket_core/parsing_repomap.py:_generate_id": {"name": "_generate_id", "filepath": "bracket_core/parsing_repomap.py", "callees": ["<PERSON><PERSON><PERSON>"], "callers": ["bracket_core/parsing_repomap.py:__init__"]}, "bracket_core/call_graph_generator.py:get_file_language": {"name": "get_file_language", "filepath": "bracket_core/call_graph_generator.py", "callees": [], "callers": ["bracket_core/call_graph_generator.py:group_files_by_language"]}, "bracket_core/mermaid_generator.py:_generate_diagram_by_type": {"name": "_generate_diagram_by_type", "filepath": "bracket_core/mermaid_generator.py", "callees": ["json", "Dict", "bracket_core/mermaid_generator.py:__init__", "bracket_core/mermaid_generator.py:_extract_mermaid_diagram"], "callers": ["bracket_core/mermaid_generator.py:generate_mermaid_diagrams", "bracket_core/mermaid_generator.py:generate_diagram_by_type"]}, "bracket_core/mermaid_generator.py:_generate_comprehensive_architecture_diagrams": {"name": "_generate_comprehensive_architecture_diagrams", "filepath": "bracket_core/mermaid_generator.py", "callees": ["json", "bracket_core/mermaid_generator.py:__init__"], "callers": ["bracket_core/mermaid_generator.py:generate_mermaid_diagrams"]}, "bracket_core/llm/oai/openai.py:agenerate": {"name": "agenerate", "filepath": "bracket_core/llm/oai/openai.py", "callees": ["AsyncRetrying", "stop_after_attempt", "wait_exponential_jitter", "retry_if_exception_type", "bracket_core/llm/oai/openai.py:_agenerate"], "callers": []}, "AsyncRetrying": {"name": "AsyncRetrying", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/llm/oai/openai.py:agenerate", "bracket_core/llm/oai/chat_openai.py:agenerate", "bracket_core/llm/oai/chat_openai.py:astream_generate", "bracket_core/llm/oai/embedding.py:_aembed_with_retry"]}, "bracket_core/llm/oai/openai.py:_agenerate": {"name": "_agenerate", "filepath": "bracket_core/llm/oai/openai.py", "callees": ["bracket_core/llm/oai/openai.py:__init__", "BaseLLMCallback"], "callers": ["bracket_core/llm/oai/openai.py:agenerate"]}, "bracket_core/llm/openrouter/client.py:generate": {"name": "generate", "filepath": "bracket_core/llm/openrouter/client.py", "callees": ["bracket_core/llm/openrouter/client.py:__init__", "asyncio"], "callers": []}, "bracket_core/domain_trace_builder.py:call_api": {"name": "call_api", "filepath": "bracket_core/domain_trace_builder.py", "callees": ["logging", "aiohttp", "time", "asyncio"], "callers": []}, "bracket_core/file_call_graph_builder.py:build_file_call_graph": {"name": "build_file_call_graph", "filepath": "bracket_core/file_call_graph_builder.py", "callees": ["bracket_core/file_call_graph_builder.py:read_functions_parquet", "bracket_core/file_call_graph_builder.py:_group_functions_by_file", "yaml", "bracket_core/file_call_graph_builder.py:FileCallGraphResult", "traceback"], "callers": ["bracket_core/file_call_graph_builder.py:main"]}, "bracket_core/file_call_graph_builder.py:_group_functions_by_file": {"name": "_group_functions_by_file", "filepath": "bracket_core/file_call_graph_builder.py", "callees": ["pandas", "bracket_core/file_call_graph_builder.py:_get_relative_path", "json"], "callers": ["bracket_core/file_call_graph_builder.py:build_file_call_graph"]}, "bracket_core/file_call_graph_builder.py:FileCallGraphResult": {"name": "FileCallGraphResult", "filepath": "bracket_core/file_call_graph_builder.py", "callees": [], "callers": ["bracket_core/file_call_graph_builder.py:build_file_call_graph"]}, "bracket_core/call_graph_generator.py:group_files_by_language": {"name": "group_files_by_language", "filepath": "bracket_core/call_graph_generator.py", "callees": ["defaultdict", "os", "Path", "bracket_core/call_graph_generator.py:get_file_language"], "callers": ["bracket_core/call_graph_generator.py:generate_basic_callgraph", "bracket_core/call_graph_generator.py:main"]}, "Path": {"name": "Path", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/call_graph_generator.py:group_files_by_language", "bracket_core/parsing_repomap.py:get_scm_fname"]}, "ConsoleReporter": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/llm/oai/base.py:__init__"]}, "StatusLogger": {"name": "StatusLogger", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/llm/oai/base.py:__init__"]}, "bracket_core/llm/api_keys.py:get_anthropic_api_key": {"name": "get_anthropic_api_key", "filepath": "bracket_core/llm/api_keys.py", "callees": ["os"], "callers": ["bracket_core/llm/api_keys.py:get_api_key"]}, "bracket_core/logger/rich_progress.py:force_refresh": {"name": "force_refresh", "filepath": "bracket_core/logger/rich_progress.py", "callees": ["Live"], "callers": ["bracket_core/logger/rich_progress.py:refresh"]}, "bracket_core/utils/token_counter.py:main": {"name": "main", "filepath": "bracket_core/utils/token_counter.py", "callees": ["<PERSON><PERSON><PERSON><PERSON>", "os", "bracket_core/utils/token_counter.py:count_tokens_and_loc_in_directory_no_tests", "Dict"], "callers": []}, "argparse": {"name": "<PERSON><PERSON><PERSON><PERSON>", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/utils/token_counter.py:main", "bracket_core/generate_domain_taxonomy.py:main", "bracket_core/file_call_graph_builder.py:main", "bracket_core/domain_classifier.py:main", "bracket_core/domain_taxonomy_token_analyzer.py:main", "bracket_core/domain_trace_builder.py:main", "bracket_core/leaf_node_token_calculator.py:main", "bracket_core/domain_file_mapper.py:main", "bracket_core/enhanced_domain_trace_builder.py:main", "bracket_core/domain_taxonomy_mapper.py:main", "bracket_core/domain_analysis.py:main", "bracket_core/irl.py:main", "bracket_core/hierarchical_domain_trace_builder.py:main", "bracket_core/hierarchical_domain_file_mapper.py:main", "bracket_core/domain_diagram_generator.py:main", "bracket_core/localisation/global_localisation.py:main"]}, "bracket_core/llm/get_client.py:get_openrouter_client": {"name": "get_openrouter_client", "filepath": "bracket_core/llm/get_client.py", "callees": ["OpenRouterClient"], "callers": []}, "OpenRouterClient": {"name": "OpenRouterClient", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/llm/get_client.py:get_openrouter_client"]}, "bracket_core/call_graph_generator.py:analyze_language_files": {"name": "analyze_language_files", "filepath": "bracket_core/call_graph_generator.py", "callees": ["tempfile", "os", "subprocess", "bracket_core/call_graph_generator.py:merge_yaml_files", "shutil"], "callers": ["bracket_core/call_graph_generator.py:main"]}, "tempfile": {"name": "tempfile", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/call_graph_generator.py:analyze_language_files"]}, "subprocess": {"name": "subprocess", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/call_graph_generator.py:analyze_language_files", "bracket_core/call_graph_generator.py:merge_yaml_files"]}, "bracket_core/call_graph_generator.py:merge_yaml_files": {"name": "merge_yaml_files", "filepath": "bracket_core/call_graph_generator.py", "callees": ["subprocess", "os", "yaml", "json"], "callers": ["bracket_core/call_graph_generator.py:analyze_language_files", "bracket_core/call_graph_generator.py:main"]}, "shutil": {"name": "shutil", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/call_graph_generator.py:analyze_language_files"]}, "bracket_core/llm/oai/chat_openai.py:stream_generate": {"name": "stream_generate", "filepath": "bracket_core/llm/oai/chat_openai.py", "callees": ["Retrying", "stop_after_attempt", "wait_exponential_jitter", "retry_if_exception_type", "bracket_core/llm/oai/chat_openai.py:_stream_generate", "bracket_core/llm/oai/chat_openai.py:__init__"], "callers": []}, "bracket_core/llm/oai/chat_openai.py:_stream_generate": {"name": "_stream_generate", "filepath": "bracket_core/llm/oai/chat_openai.py", "callees": ["bracket_core/llm/oai/chat_openai.py:__init__", "BaseLLMCallback"], "callers": ["bracket_core/llm/oai/chat_openai.py:stream_generate"]}, "bracket_core/run_hierarchical_mapper.py:run_mapper": {"name": "run_mapper", "filepath": "bracket_core/run_hierarchical_mapper.py", "callees": ["os", "logging", "HierarchicalDomainFileMapper", "traceback"], "callers": []}, "HierarchicalDomainFileMapper": {"name": "HierarchicalDomainFileMapper", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/run_hierarchical_mapper.py:run_mapper"]}, "bracket_core/domain_trace_token_analyzer.py:read_taxonomy_json": {"name": "read_taxonomy_json", "filepath": "bracket_core/domain_trace_token_analyzer.py", "callees": ["json"], "callers": ["bracket_core/domain_trace_token_analyzer.py:analyze_traces"]}, "bracket_core/domain_taxonomy_token_analyzer.py:read_taxonomy_json": {"name": "read_taxonomy_json", "filepath": "bracket_core/domain_taxonomy_token_analyzer.py", "callees": ["json"], "callers": ["bracket_core/domain_taxonomy_token_analyzer.py:analyze_taxonomy"]}, "hashlib": {"name": "<PERSON><PERSON><PERSON>", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/parsing_repomap.py:_generate_id", "bracket_core/parsing_repomap.py:get_context_hash", "bracket_core/parsing_repomap.py:get_tags"]}, "bracket_core/test_mermaid_generator.py:main": {"name": "main", "filepath": "bracket_core/test_mermaid_generator.py", "callees": ["bracket_core/test_mermaid_generator.py:test_mermaid_generation"], "callers": []}, "bracket_core/localisation/horizontal_localisation.py:__init__": {"name": "__init__", "filepath": "bracket_core/localisation/horizontal_localisation.py", "callees": ["get_openai_api_key", "get_openrouter_api_key", "os", "get_openrouter_client"], "callers": ["bracket_core/localisation/horizontal_localisation.py:load_domain_taxonomy", "bracket_core/localisation/horizontal_localisation.py:load_semantic_documented_functions", "bracket_core/localisation/horizontal_localisation.py:_create_function_lookup_indices", "bracket_core/localisation/horizontal_localisation.py:build_domain_hierarchy", "bracket_core/localisation/horizontal_localisation.py:extract_relevant_functions", "bracket_core/localisation/horizontal_localisation.py:find_relevant_functions"]}, "bracket_core/domain_analysis.py:__init__": {"name": "__init__", "filepath": "bracket_core/domain_analysis.py", "callees": ["get_openrouter_api_key", "get_openai_api_key", "bracket_core/domain_analysis.py:StatusTracker", "get_openrouter_client"], "callers": ["bracket_core/domain_analysis.py:_call_openai_api", "bracket_core/domain_analysis.py:domains_from_significant_functions", "bracket_core/domain_analysis.py:main"]}, "bracket_core/domain_analysis.py:StatusTracker": {"name": "StatusTracker", "filepath": "bracket_core/domain_analysis.py", "callees": [], "callers": ["bracket_core/domain_analysis.py:__init__"]}, "bracket_core/logger/rich_progress.py:stop": {"name": "stop", "filepath": "bracket_core/logger/rich_progress.py", "callees": ["Live"], "callers": []}, "bracket_core/logger/rich_progress.py:child": {"name": "child", "filepath": "bracket_core/logger/rich_progress.py", "callees": ["bracket_core/logger/rich_progress.py:__init__"], "callers": []}, "bracket_core/localisation/test_horizontal.py:test_domain_evaluation": {"name": "test_domain_evaluation", "filepath": "bracket_core/localisation/test_horizontal.py", "callees": ["get_openai_api_key", "bracket_core/localisation/test_horizontal.py:DomainNode", "aiohttp", "json", "traceback"], "callers": ["bracket_core/localisation/test_horizontal.py:main"]}, "bracket_core/localisation/test_horizontal.py:DomainNode": {"name": "DomainNode", "filepath": "bracket_core/localisation/test_horizontal.py", "callees": [], "callers": ["bracket_core/localisation/test_horizontal.py:test_domain_evaluation"]}, "bracket_core/leaf_node_token_calculator.py:read_domain_traces": {"name": "read_domain_traces", "filepath": "bracket_core/leaf_node_token_calculator.py", "callees": ["yaml"], "callers": ["bracket_core/leaf_node_token_calculator.py:analyze_leaf_domains"]}, "bracket_core/enhanced_domain_trace_builder.py:read_domain_file_mappings": {"name": "read_domain_file_mappings", "filepath": "bracket_core/enhanced_domain_trace_builder.py", "callees": ["yaml", "bracket_core/enhanced_domain_trace_builder.py:_normalize_file_path"], "callers": ["bracket_core/enhanced_domain_trace_builder.py:build_and_classify"]}, "bracket_core/enhanced_domain_trace_builder.py:_normalize_file_path": {"name": "_normalize_file_path", "filepath": "bracket_core/enhanced_domain_trace_builder.py", "callees": ["re", "os"], "callers": ["bracket_core/enhanced_domain_trace_builder.py:read_domain_file_mappings", "bracket_core/enhanced_domain_trace_builder.py:map_functions_to_files"]}, "bracket_core/llm/api_keys.py:get_api_key": {"name": "get_api_key", "filepath": "bracket_core/llm/api_keys.py", "callees": ["bracket_core/llm/api_keys.py:get_openai_api_key", "bracket_core/llm/api_keys.py:get_openrouter_api_key", "bracket_core/llm/api_keys.py:get_anthropic_api_key"], "callers": []}, "BaseLLMCallback": {"name": "BaseLLMCallback", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/llm/oai/openai.py:_generate", "bracket_core/llm/oai/openai.py:_agenerate", "bracket_core/llm/oai/chat_openai.py:_generate", "bracket_core/llm/oai/chat_openai.py:_stream_generate", "bracket_core/llm/oai/chat_openai.py:_agenerate", "bracket_core/llm/oai/chat_openai.py:_astream_generate"]}, "bracket_core/logger/rich_progress.py:error": {"name": "error", "filepath": "bracket_core/logger/rich_progress.py", "callees": ["<PERSON><PERSON><PERSON>"], "callers": []}, "bracket_core/llm/oai/embedding.py:aembed": {"name": "aembed", "filepath": "bracket_core/llm/oai/embedding.py", "callees": ["chunk_text", "asyncio", "bracket_core/llm/oai/embedding.py:_aembed_with_retry", "numpy"], "callers": []}, "bracket_core/llm/oai/embedding.py:_aembed_with_retry": {"name": "_aembed_with_retry", "filepath": "bracket_core/llm/oai/embedding.py", "callees": ["AsyncRetrying", "stop_after_attempt", "wait_exponential_jitter", "retry_if_exception_type", "bracket_core/llm/oai/embedding.py:__init__"], "callers": ["bracket_core/llm/oai/embedding.py:aembed"]}, "bracket_core/parsing_repomap.py:__eq__": {"name": "__eq__", "filepath": "bracket_core/parsing_repomap.py", "callees": [], "callers": []}, "bracket_core/domain_trace_token_analyzer.py:analyze_node": {"name": "analyze_node", "filepath": "bracket_core/domain_trace_token_analyzer.py", "callees": ["Dict", "num_tokens_from_string", "bracket_core/domain_trace_token_analyzer.py:TraceTokenInfo", "bracket_core/domain_trace_token_analyzer.py:__init__", "bracket_core/domain_trace_token_analyzer.py:analyze_node"], "callers": ["bracket_core/domain_trace_token_analyzer.py:analyze_node", "bracket_core/domain_trace_token_analyzer.py:analyze_traces"]}, "num_tokens_from_string": {"name": "num_tokens_from_string", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/domain_trace_token_analyzer.py:analyze_node", "bracket_core/domain_taxonomy_token_analyzer.py:calculate_tokens", "bracket_core/leaf_node_token_calculator.py:calculate_request_tokens", "bracket_core/domain_diagram_generator.py:_call_openai_api", "bracket_core/domain_diagram_generator.py:generate_leaf_diagram"]}, "bracket_core/domain_trace_token_analyzer.py:TraceTokenInfo": {"name": "TraceTokenInfo", "filepath": "bracket_core/domain_trace_token_analyzer.py", "callees": [], "callers": ["bracket_core/domain_trace_token_analyzer.py:analyze_node"]}, "bracket_core/domain_taxonomy_token_analyzer.py:find_leaf_components": {"name": "find_leaf_components", "filepath": "bracket_core/domain_taxonomy_token_analyzer.py", "callees": ["Dict", "bracket_core/domain_taxonomy_token_analyzer.py:LeafComponent", "bracket_core/domain_taxonomy_token_analyzer.py:__init__", "bracket_core/domain_taxonomy_token_analyzer.py:find_leaf_components"], "callers": ["bracket_core/domain_taxonomy_token_analyzer.py:find_leaf_components", "bracket_core/domain_taxonomy_token_analyzer.py:analyze_taxonomy"]}, "bracket_core/domain_taxonomy_token_analyzer.py:LeafComponent": {"name": "LeafComponent", "filepath": "bracket_core/domain_taxonomy_token_analyzer.py", "callees": [], "callers": ["bracket_core/domain_taxonomy_token_analyzer.py:find_leaf_components"]}, "bracket_core/logger/rich_progress.py:warning": {"name": "warning", "filepath": "bracket_core/logger/rich_progress.py", "callees": ["<PERSON><PERSON><PERSON>"], "callers": []}, "bracket_core/hybrid_kg.py:generate_graph": {"name": "generate_graph", "filepath": "bracket_core/hybrid_kg.py", "callees": ["time", "bracket_core/hybrid_kg.py:_extract_all_function_definitions", "bracket_core/hybrid_kg.py:_process_repository", "bracket_core/hybrid_kg.py:_create_graph"], "callers": ["bracket_core/hybrid_kg.py:generate_hybrid_knowledge_graph"]}, "bracket_core/hybrid_kg.py:_extract_all_function_definitions": {"name": "_extract_all_function_definitions", "filepath": "bracket_core/hybrid_kg.py", "callees": ["os", "get_parser", "bracket_core/hybrid_kg.py:_get_function_query", "get_language", "bracket_core/hybrid_kg.py:__init__"], "callers": ["bracket_core/hybrid_kg.py:generate_graph"]}, "bracket_core/hybrid_kg.py:_process_repository": {"name": "_process_repository", "filepath": "bracket_core/hybrid_kg.py", "callees": ["os", "bracket_core/hybrid_kg.py:_process_file"], "callers": ["bracket_core/hybrid_kg.py:generate_graph"]}, "bracket_core/hybrid_kg.py:_create_graph": {"name": "_create_graph", "filepath": "bracket_core/hybrid_kg.py", "callees": ["networkx", "bracket_core/hybrid_kg.py:__init__"], "callers": ["bracket_core/hybrid_kg.py:generate_graph"]}, "bracket_core/file_call_graph_builder.py:_get_relative_path": {"name": "_get_relative_path", "filepath": "bracket_core/file_call_graph_builder.py", "callees": ["os"], "callers": ["bracket_core/file_call_graph_builder.py:_group_functions_by_file"]}, "bracket_core/parsing_repomap.py:__hash__": {"name": "__hash__", "filepath": "bracket_core/parsing_repomap.py", "callees": [], "callers": []}, "bracket_core/logger/rich_progress.py:success": {"name": "success", "filepath": "bracket_core/logger/rich_progress.py", "callees": ["<PERSON><PERSON><PERSON>"], "callers": []}, "bracket_core/llm/anthropic/client.py:generate_with_json_mode": {"name": "generate_with_json_mode", "filepath": "bracket_core/llm/anthropic/client.py", "callees": ["bracket_core/llm/anthropic/client.py:__init__", "Any", "json"], "callers": []}, "Any": {"name": "Any", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/llm/anthropic/client.py:generate_with_json_mode", "bracket_core/file_call_graph_builder.py:_extract_function_name", "bracket_core/llm/anthropic/client.py:generate_with_tools", "bracket_core/llm/anthropic/client.py:continue_tool_conversation", "bracket_core/hybrid_kg.py:_process_function_definitions", "bracket_core/hybrid_kg.py:_extract_call_context"]}, "bracket_core/generate_domain_taxonomy.py:main": {"name": "main", "filepath": "bracket_core/generate_domain_taxonomy.py", "callees": ["<PERSON><PERSON><PERSON><PERSON>", "bracket_core/generate_domain_taxonomy.py:generate_domain_taxonomy", "traceback"], "callers": []}, "bracket_core/logger/rich_progress.py:info": {"name": "info", "filepath": "bracket_core/logger/rich_progress.py", "callees": ["<PERSON><PERSON><PERSON>"], "callers": []}, "AzureOpenAI": {"name": "AzureOpenAI", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/llm/oai/base.py:_create_openai_client"]}, "AsyncAzureOpenAI": {"name": "AsyncAzureOpenAI", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/llm/oai/base.py:_create_openai_client"]}, "bracket_core/logger/rich_progress.py:__call__": {"name": "__call__", "filepath": "bracket_core/logger/rich_progress.py", "callees": ["Progress", "bracket_core/logger/rich_progress.py:refresh"], "callers": []}, "bracket_core/llm/oai/chat_openai.py:agenerate": {"name": "agenerate", "filepath": "bracket_core/llm/oai/chat_openai.py", "callees": ["AsyncRetrying", "stop_after_attempt", "wait_exponential_jitter", "retry_if_exception_type", "bracket_core/llm/oai/chat_openai.py:_agenerate", "bracket_core/llm/oai/chat_openai.py:__init__"], "callers": []}, "bracket_core/llm/oai/chat_openai.py:_agenerate": {"name": "_agenerate", "filepath": "bracket_core/llm/oai/chat_openai.py", "callees": ["bracket_core/llm/oai/chat_openai.py:__init__", "BaseLLMCallback"], "callers": ["bracket_core/llm/oai/chat_openai.py:agenerate"]}, "bracket_core/leaf_node_token_calculator.py:read_function_data": {"name": "read_function_data", "filepath": "bracket_core/leaf_node_token_calculator.py", "callees": ["pandas", "json"], "callers": ["bracket_core/leaf_node_token_calculator.py:analyze_leaf_domains"]}, "bracket_core/file_call_graph_builder.py:_extract_function_name": {"name": "_extract_function_name", "filepath": "bracket_core/file_call_graph_builder.py", "callees": ["Any"], "callers": []}, "bracket_core/domain_taxonomy_mapper.py:read_domain_analysis": {"name": "read_domain_analysis", "filepath": "bracket_core/domain_taxonomy_mapper.py", "callees": ["yaml"], "callers": ["bracket_core/domain_taxonomy_mapper.py:map_taxonomy"]}, "bracket_core/enhanced_domain_trace_builder.py:map_traces_to_top_domains": {"name": "map_traces_to_top_domains", "filepath": "bracket_core/enhanced_domain_trace_builder.py", "callees": [], "callers": ["bracket_core/enhanced_domain_trace_builder.py:build_and_classify"]}, "bracket_core/parsing_repomap.py:get_context_hash": {"name": "get_context_hash", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:__init__", "<PERSON><PERSON><PERSON>"], "callers": []}, "bracket_core/localisation/minimal_horizontal.py:get_domains_at_level": {"name": "get_domains_at_level", "filepath": "bracket_core/localisation/minimal_horizontal.py", "callees": ["bracket_core/localisation/minimal_horizontal.py:collect_domains"], "callers": ["bracket_core/localisation/minimal_horizontal.py:main"]}, "bracket_core/localisation/minimal_horizontal.py:collect_domains": {"name": "collect_domains", "filepath": "bracket_core/localisation/minimal_horizontal.py", "callees": ["bracket_core/localisation/minimal_horizontal.py:collect_domains"], "callers": ["bracket_core/localisation/minimal_horizontal.py:get_domains_at_level", "bracket_core/localisation/minimal_horizontal.py:collect_domains"]}, "bracket_core/domain_taxonomy_token_analyzer.py:calculate_tokens": {"name": "calculate_tokens", "filepath": "bracket_core/domain_taxonomy_token_analyzer.py", "callees": ["num_tokens_from_string"], "callers": ["bracket_core/domain_taxonomy_token_analyzer.py:analyze_taxonomy"]}, "bracket_core/hierarchical_domain_trace_builder.py:_get_trace_level": {"name": "_get_trace_level", "filepath": "bracket_core/hierarchical_domain_trace_builder.py", "callees": [], "callers": []}, "get_parser": {"name": "get_parser", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/hybrid_kg.py:_extract_all_function_definitions", "bracket_core/hybrid_kg.py:_process_file", "bracket_core/parsing_repomap.py:get_tags", "bracket_core/parsing_repomap.py:_find_variable_assignments", "bracket_core/parsing_repomap.py:_find_parent_classes", "bracket_core/parsing_repomap.py:enrich_with_line_numbers"]}, "bracket_core/hybrid_kg.py:_get_function_query": {"name": "_get_function_query", "filepath": "bracket_core/hybrid_kg.py", "callees": [], "callers": ["bracket_core/hybrid_kg.py:_extract_all_function_definitions", "bracket_core/hybrid_kg.py:_extract_functions_and_calls"]}, "get_language": {"name": "get_language", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/hybrid_kg.py:_extract_all_function_definitions", "bracket_core/hybrid_kg.py:_extract_functions_and_calls", "bracket_core/parsing_repomap.py:get_tags", "bracket_core/parsing_repomap.py:_find_parent_classes"]}, "bracket_core/global_codebase_explainer.py:read_taxonomy_json": {"name": "read_taxonomy_json", "filepath": "bracket_core/global_codebase_explainer.py", "callees": ["json"], "callers": ["bracket_core/global_codebase_explainer.py:prepare_taxonomy_data"]}, "bracket_core/domain_taxonomy_mapper.py:read_domain_traces": {"name": "read_domain_traces", "filepath": "bracket_core/domain_taxonomy_mapper.py", "callees": ["yaml"], "callers": ["bracket_core/domain_taxonomy_mapper.py:map_taxonomy"]}, "bracket_core/llm/oai/chat_openai.py:astream_generate": {"name": "astream_generate", "filepath": "bracket_core/llm/oai/chat_openai.py", "callees": ["AsyncRetrying", "stop_after_attempt", "wait_exponential_jitter", "retry_if_exception_type", "bracket_core/llm/oai/chat_openai.py:_astream_generate", "bracket_core/llm/oai/chat_openai.py:__init__"], "callers": []}, "bracket_core/llm/oai/chat_openai.py:_astream_generate": {"name": "_astream_generate", "filepath": "bracket_core/llm/oai/chat_openai.py", "callees": ["bracket_core/llm/oai/chat_openai.py:__init__", "BaseLLMCallback"], "callers": ["bracket_core/llm/oai/chat_openai.py:astream_generate"]}, "bracket_core/llm/anthropic/client.py:generate_with_tools": {"name": "generate_with_tools", "filepath": "bracket_core/llm/anthropic/client.py", "callees": ["bracket_core/llm/anthropic/client.py:__init__", "Any"], "callers": []}, "networkx": {"name": "networkx", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/parsing_repomap.py:__init__", "bracket_core/hybrid_kg.py:_create_graph", "bracket_core/parsing_repomap.py:_add_relationship_edge", "bracket_core/hybrid_kg.py:graph_to_dataframes", "bracket_core/parsing_repomap.py:create_graph", "bracket_core/parsing_repomap.py:save_graph_as_parquet", "bracket_core/parsing_repomap.py:enrich_with_line_numbers", "bracket_core/parsing_repomap.py:to_dataframes", "bracket_core/parsing_repomap.py:enrich_with_nuanced_callgraph"]}, "bracket_core/hierarchical_domain_trace_builder.py:_is_leaf_node": {"name": "_is_leaf_node", "filepath": "bracket_core/hierarchical_domain_trace_builder.py", "callees": ["DomainTrace"], "callers": ["bracket_core/hierarchical_domain_trace_builder.py:classify_functions_with_hierarchical_search_space", "bracket_core/hierarchical_domain_trace_builder.py:build_and_classify"]}, "DomainTrace": {"name": "DomainTrace", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/hierarchical_domain_trace_builder.py:_is_leaf_node"]}, "bracket_core/domain_analysis.py:_split_functions_into_chunks": {"name": "_split_functions_into_chunks", "filepath": "bracket_core/domain_analysis.py", "callees": ["yaml", "bracket_core/domain_analysis.py:count_tokens", "math", "Dict"], "callers": ["bracket_core/domain_analysis.py:create_hierar_domain_struct"]}, "math": {"name": "math", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/domain_analysis.py:_split_functions_into_chunks", "bracket_core/hierarchical_domain_trace_builder.py:_chunk_function_data", "bracket_core/parsing_repomap.py:calculate_relationship_weight"]}, "bracket_core/global_codebase_explainer.py:extract_codebase_overview": {"name": "extract_codebase_overview", "filepath": "bracket_core/global_codebase_explainer.py", "callees": [], "callers": ["bracket_core/global_codebase_explainer.py:prepare_taxonomy_data"]}, "bracket_core/cli.py:normalize_function_paths": {"name": "normalize_function_paths", "filepath": "bracket_core/cli.py", "callees": ["typer", "print", "HierarchicalDomainTraceBuilderIntegration"], "callers": []}, "HierarchicalDomainTraceBuilderIntegration": {"name": "HierarchicalDomainTraceBuilderIntegration", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/cli.py:normalize_function_paths", "bracket_core/cli.py:build_domain_traces"]}, "bracket_core/mermaid_generator.py:generate_diagram_by_type": {"name": "generate_diagram_by_type", "filepath": "bracket_core/mermaid_generator.py", "callees": ["bracket_core/mermaid_generator.py:__init__", "json", "bracket_core/mermaid_generator.py:_generate_diagram_by_type", "os"], "callers": []}, "bracket_core/domain_taxonomy_token_analyzer.py:analyze_taxonomy": {"name": "analyze_taxonomy", "filepath": "bracket_core/domain_taxonomy_token_analyzer.py", "callees": ["bracket_core/domain_taxonomy_token_analyzer.py:read_taxonomy_json", "bracket_core/domain_taxonomy_token_analyzer.py:find_leaf_components", "bracket_core/domain_taxonomy_token_analyzer.py:calculate_tokens", "bracket_core/domain_taxonomy_token_analyzer.py:AnalysisResult"], "callers": ["bracket_core/domain_taxonomy_token_analyzer.py:run_analysis"]}, "bracket_core/domain_taxonomy_token_analyzer.py:AnalysisResult": {"name": "AnalysisResult", "filepath": "bracket_core/domain_taxonomy_token_analyzer.py", "callees": [], "callers": ["bracket_core/domain_taxonomy_token_analyzer.py:analyze_taxonomy"]}, "bracket_core/domain_trace_builder.py:__init__": {"name": "__init__", "filepath": "bracket_core/domain_trace_builder.py", "callees": ["get_openai_api_key"], "callers": ["bracket_core/domain_trace_builder.py:main"]}, "bracket_core/parsing_repomap.py:add_module": {"name": "add_module", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:__init__"], "callers": []}, "bracket_core/localisation/minimal_horizontal.py:main": {"name": "main", "filepath": "bracket_core/localisation/minimal_horizontal.py", "callees": ["bracket_core/localisation/minimal_horizontal.py:__init__", "bracket_core/localisation/minimal_horizontal.py:load_domain_taxonomy", "bracket_core/localisation/minimal_horizontal.py:build_domain_hierarchy", "bracket_core/localisation/minimal_horizontal.py:get_domains_at_level"], "callers": []}, "bracket_core/domain_trace_token_analyzer.py:analyze_traces": {"name": "analyze_traces", "filepath": "bracket_core/domain_trace_token_analyzer.py", "callees": ["bracket_core/domain_trace_token_analyzer.py:read_taxonomy_json", "bracket_core/domain_trace_token_analyzer.py:analyze_node", "bracket_core/domain_trace_token_analyzer.py:TraceAnalysisResult"], "callers": ["bracket_core/domain_trace_token_analyzer.py:run_analysis"]}, "bracket_core/domain_trace_token_analyzer.py:TraceAnalysisResult": {"name": "TraceAnalysisResult", "filepath": "bracket_core/domain_trace_token_analyzer.py", "callees": [], "callers": ["bracket_core/domain_trace_token_analyzer.py:analyze_traces"]}, "bracket_core/documenting.py:process_api_requests_from_file": {"name": "process_api_requests_from_file", "filepath": "bracket_core/documenting.py", "callees": ["logging", "bracket_core/documenting.py:api_endpoint_from_url", "asyncio", "bracket_core/documenting.py:task_id_generator_function", "bracket_core/documenting.py:StatusTracker", "time", "aiohttp", "json", "bracket_core/documenting.py:APIRequest", "bracket_core/documenting.py:num_tokens_consumed_from_request", "bracket_core/documenting.py:call_api"], "callers": ["bracket_core/documenting.py:document_functions", "bracket_core/documenting.py:document_functions_with_significance", "bracket_core/documenting.py:document_functions_with_context_significance"]}, "bracket_core/documenting.py:api_endpoint_from_url": {"name": "api_endpoint_from_url", "filepath": "bracket_core/documenting.py", "callees": ["re"], "callers": ["bracket_core/documenting.py:process_api_requests_from_file"]}, "bracket_core/documenting.py:task_id_generator_function": {"name": "task_id_generator_function", "filepath": "bracket_core/documenting.py", "callees": [], "callers": ["bracket_core/documenting.py:process_api_requests_from_file"]}, "bracket_core/documenting.py:StatusTracker": {"name": "StatusTracker", "filepath": "bracket_core/documenting.py", "callees": [], "callers": ["bracket_core/documenting.py:process_api_requests_from_file"]}, "bracket_core/documenting.py:APIRequest": {"name": "APIRequest", "filepath": "bracket_core/documenting.py", "callees": [], "callers": ["bracket_core/documenting.py:process_api_requests_from_file"]}, "bracket_core/documenting.py:num_tokens_consumed_from_request": {"name": "num_tokens_consumed_from_request", "filepath": "bracket_core/documenting.py", "callees": ["tiktoken", "json"], "callers": ["bracket_core/documenting.py:process_api_requests_from_file"]}, "bracket_core/documenting.py:call_api": {"name": "call_api", "filepath": "bracket_core/documenting.py", "callees": ["logging", "aiohttp", "time", "bracket_core/documenting.py:append_to_jsonl", "asyncio"], "callers": ["bracket_core/documenting.py:process_api_requests_from_file"]}, "bracket_core/parsing_repomap.py:add_import": {"name": "add_import", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:__init__", "Dict"], "callers": []}, "bracket_core/hierarchical_domain_file_mapper.py:extract_domain_hierarchy": {"name": "extract_domain_hierarchy", "filepath": "bracket_core/hierarchical_domain_file_mapper.py", "callees": ["Dict", "bracket_core/hierarchical_domain_file_mapper.py:__init__", "bracket_core/hierarchical_domain_file_mapper.py:process_domain"], "callers": ["bracket_core/hierarchical_domain_file_mapper.py:map_files_to_domains_hierarchically"]}, "bracket_core/hierarchical_domain_file_mapper.py:process_domain": {"name": "process_domain", "filepath": "bracket_core/hierarchical_domain_file_mapper.py", "callees": ["Dict", "bracket_core/hierarchical_domain_file_mapper.py:__init__", "bracket_core/hierarchical_domain_file_mapper.py:process_domain"], "callers": ["bracket_core/hierarchical_domain_file_mapper.py:extract_domain_hierarchy", "bracket_core/hierarchical_domain_file_mapper.py:process_domain"]}, "bracket_core/domain_file_mapper.py:__init__": {"name": "__init__", "filepath": "bracket_core/domain_file_mapper.py", "callees": ["get_openrouter_api_key", "get_openai_api_key", "bracket_core/domain_file_mapper.py:StatusTracker"], "callers": ["bracket_core/domain_file_mapper.py:map_files_to_domains", "bracket_core/domain_file_mapper.py:main"]}, "bracket_core/domain_file_mapper.py:StatusTracker": {"name": "StatusTracker", "filepath": "bracket_core/domain_file_mapper.py", "callees": [], "callers": ["bracket_core/domain_file_mapper.py:__init__"]}, "bracket_core/domain_taxonomy_mapper.py:read_diagram_name_mapping": {"name": "read_diagram_name_mapping", "filepath": "bracket_core/domain_taxonomy_mapper.py", "callees": ["os", "json"], "callers": ["bracket_core/domain_taxonomy_mapper.py:read_domain_diagrams"]}, "bracket_core/global_codebase_explainer.py:extract_top_level_domains": {"name": "extract_top_level_domains", "filepath": "bracket_core/global_codebase_explainer.py", "callees": [], "callers": ["bracket_core/global_codebase_explainer.py:prepare_taxonomy_data"]}, "bracket_core/hierarchical_domain_trace_builder.py:_call_openai_api": {"name": "_call_openai_api", "filepath": "bracket_core/hierarchical_domain_trace_builder.py", "callees": [], "callers": ["bracket_core/hierarchical_domain_trace_builder.py:_process_chunk_with_semaphore", "bracket_core/hierarchical_domain_trace_builder.py:_process_file_functions_together", "bracket_core/hierarchical_domain_trace_builder.py:_process_single_function", "bracket_core/hierarchical_domain_trace_builder.py:_process_trace_hierarchical"]}, "bracket_core/hierarchical_domain_trace_builder.py:_count_tokens": {"name": "_count_tokens", "filepath": "bracket_core/hierarchical_domain_trace_builder.py", "callees": ["tiktoken"], "callers": ["bracket_core/hierarchical_domain_trace_builder.py:_chunk_function_data", "bracket_core/hierarchical_domain_trace_builder.py:_process_file_for_trace"]}, "bracket_core/domain_diagram_generator.py:read_domain_traces": {"name": "read_domain_traces", "filepath": "bracket_core/domain_diagram_generator.py", "callees": ["yaml"], "callers": ["bracket_core/domain_diagram_generator.py:generate_all_diagrams"]}, "bracket_core/llm/anthropic/client.py:continue_tool_conversation": {"name": "continue_tool_conversation", "filepath": "bracket_core/llm/anthropic/client.py", "callees": ["bracket_core/llm/anthropic/client.py:__init__", "Any"], "callers": []}, "bracket_core/call_graph_generator.py:create_graph_structure": {"name": "create_graph_structure", "filepath": "bracket_core/call_graph_generator.py", "callees": [], "callers": ["bracket_core/call_graph_generator.py:generate_basic_callgraph", "bracket_core/call_graph_generator.py:main"]}, "bracket_core/global_codebase_explainer.py:extract_domain_diagrams": {"name": "extract_domain_diagrams", "filepath": "bracket_core/global_codebase_explainer.py", "callees": ["Dict", "bracket_core/global_codebase_explainer.py:extract_domain_diagrams"], "callers": ["bracket_core/global_codebase_explainer.py:extract_domain_diagrams", "bracket_core/global_codebase_explainer.py:prepare_taxonomy_data"]}, "bracket_core/mermaid_generator.py:_extract_mermaid_diagram": {"name": "_extract_mermaid_diagram", "filepath": "bracket_core/mermaid_generator.py", "callees": [], "callers": ["bracket_core/mermaid_generator.py:_generate_diagram_by_type"]}, "bracket_core/hierarchical_domain_trace_builder.py:_chunk_function_data": {"name": "_chunk_function_data", "filepath": "bracket_core/hierarchical_domain_trace_builder.py", "callees": ["Dict", "bracket_core/hierarchical_domain_trace_builder.py:_count_tokens", "math"], "callers": []}, "bracket_core/cli.py:build_domain_traces": {"name": "build_domain_traces", "filepath": "bracket_core/cli.py", "callees": ["typer", "print", "asyncio", "HierarchicalDomainTraceBuilderIntegration", "EnhancedDomainTraceBuilderIntegration", "DomainTraceBuilder"], "callers": []}, "EnhancedDomainTraceBuilderIntegration": {"name": "EnhancedDomainTraceBuilderIntegration", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/cli.py:build_domain_traces", "bracket_core/irl.py:build_domain_traces"]}, "DomainTraceBuilder": {"name": "DomainTraceBuilder", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/cli.py:build_domain_traces", "bracket_core/irl.py:build_domain_traces"]}, "bracket_core/irl.py:run": {"name": "run", "filepath": "bracket_core/irl.py", "callees": ["bracket_core/irl.py:generate_knowledge_graph", "bracket_core/irl.py:save_graph_as_parquet", "os", "bracket_core/irl.py:generate_documentation", "convert_parquet_to_simplified_csv", "bracket_core/irl.py:generate_significant_functions_yaml", "bracket_core/irl.py:create_hierarchical_domains", "bracket_core/irl.py:build_file_call_graph", "bracket_core/irl.py:map_files_to_domains", "bracket_core/irl.py:build_domain_traces", "bracket_core/irl.py:create_final_artifact"], "callers": ["bracket_core/irl.py:main"]}, "bracket_core/irl.py:generate_knowledge_graph": {"name": "generate_knowledge_graph", "filepath": "bracket_core/irl.py", "callees": ["generate_hybrid_knowledge_graph"], "callers": ["bracket_core/irl.py:run"]}, "bracket_core/irl.py:save_graph_as_parquet": {"name": "save_graph_as_parquet", "filepath": "bracket_core/irl.py", "callees": ["graph_to_dataframes", "json", "bracket_core/irl.py:convert_mixed_types"], "callers": ["bracket_core/irl.py:run"]}, "bracket_core/irl.py:generate_documentation": {"name": "generate_documentation", "filepath": "bracket_core/irl.py", "callees": ["pandas", "document_functions_with_significance"], "callers": ["bracket_core/irl.py:run"]}, "convert_parquet_to_simplified_csv": {"name": "convert_parquet_to_simplified_csv", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/irl.py:run"]}, "bracket_core/irl.py:generate_significant_functions_yaml": {"name": "generate_significant_functions_yaml", "filepath": "bracket_core/irl.py", "callees": ["os", "pandas", "json", "yaml", "traceback"], "callers": ["bracket_core/irl.py:run"]}, "bracket_core/irl.py:create_hierarchical_domains": {"name": "create_hierarchical_domains", "filepath": "bracket_core/irl.py", "callees": ["os", "DomainAnalysisIntegration", "traceback"], "callers": ["bracket_core/irl.py:run"]}, "bracket_core/irl.py:build_file_call_graph": {"name": "build_file_call_graph", "filepath": "bracket_core/irl.py", "callees": ["os", "FileCallGraphBuilder", "traceback"], "callers": ["bracket_core/irl.py:run"]}, "bracket_core/irl.py:map_files_to_domains": {"name": "map_files_to_domains", "filepath": "bracket_core/irl.py", "callees": ["os", "run_mapper", "DomainFileMapperIntegration", "traceback"], "callers": ["bracket_core/irl.py:run"]}, "bracket_core/irl.py:build_domain_traces": {"name": "build_domain_traces", "filepath": "bracket_core/irl.py", "callees": ["os", "DomainTraceBuilder", "EnhancedDomainTraceBuilderIntegration", "bracket_core/irl.py:generate_domain_diagrams", "bracket_core/irl.py:generate_domain_taxonomy", "bracket_core/irl.py:generate_codebase_explanation", "traceback"], "callers": ["bracket_core/irl.py:run"]}, "bracket_core/irl.py:create_final_artifact": {"name": "create_final_artifact", "filepath": "bracket_core/irl.py", "callees": ["pandas", "os"], "callers": ["bracket_core/irl.py:run"]}, "bracket_core/domain_trace_builder.py:build_domain_traces": {"name": "build_domain_traces", "filepath": "bracket_core/domain_trace_builder.py", "callees": ["bracket_core/domain_trace_builder.py:from_path", "bracket_core/domain_trace_builder.py:traverse_areas", "Dict"], "callers": ["bracket_core/domain_trace_builder.py:build_and_classify"]}, "bracket_core/domain_trace_builder.py:traverse_areas": {"name": "traverse_areas", "filepath": "bracket_core/domain_trace_builder.py", "callees": ["bracket_core/domain_trace_builder.py:from_path", "bracket_core/domain_trace_builder.py:traverse_areas"], "callers": ["bracket_core/domain_trace_builder.py:build_domain_traces", "bracket_core/domain_trace_builder.py:traverse_areas"]}, "bracket_core/file_call_graph_builder.py:main": {"name": "main", "filepath": "bracket_core/file_call_graph_builder.py", "callees": ["<PERSON><PERSON><PERSON><PERSON>", "bracket_core/file_call_graph_builder.py:__init__", "bracket_core/file_call_graph_builder.py:build_file_call_graph", "traceback"], "callers": []}, "bracket_core/domain_analysis.py:_process_chunk": {"name": "_process_chunk", "filepath": "bracket_core/domain_analysis.py", "callees": ["yaml", "bracket_core/domain_analysis.py:_get_granularity_guidance", "bracket_core/domain_analysis.py:_call_openai_api", "bracket_core/domain_analysis.py:ChunkProcessingResult", "<PERSON><PERSON>", "json"], "callers": ["bracket_core/domain_analysis.py:create_hierar_domain_struct"]}, "bracket_core/domain_analysis.py:_get_granularity_guidance": {"name": "_get_granularity_guidance", "filepath": "bracket_core/domain_analysis.py", "callees": [], "callers": ["bracket_core/domain_analysis.py:_process_chunk", "bracket_core/domain_analysis.py:significant_fns_to_domain_structs"]}, "bracket_core/domain_analysis.py:_call_openai_api": {"name": "_call_openai_api", "filepath": "bracket_core/domain_analysis.py", "callees": ["Dict", "bracket_core/domain_analysis.py:__init__", "time", "asyncio", "aiohttp"], "callers": ["bracket_core/domain_analysis.py:_process_chunk", "bracket_core/domain_analysis.py:_merge_domain_results", "bracket_core/domain_analysis.py:_consolidate_domains", "bracket_core/domain_analysis.py:significant_fns_to_domain_structs"]}, "bracket_core/domain_analysis.py:ChunkProcessingResult": {"name": "ChunkProcessingResult", "filepath": "bracket_core/domain_analysis.py", "callees": [], "callers": ["bracket_core/domain_analysis.py:_process_chunk"]}, "Tuple": {"name": "<PERSON><PERSON>", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/domain_analysis.py:_process_chunk", "bracket_core/hierarchical_domain_trace_builder.py:_process_chunk_with_semaphore", "bracket_core/hybrid_kg.py:_process_function_definitions", "bracket_core/domain_analysis.py:_merge_domain_results", "bracket_core/hybrid_kg.py:_process_function_calls", "bracket_core/enhanced_domain_trace_builder.py:classify_functions_with_reduced_search_space", "bracket_core/domain_analysis.py:_consolidate_domains", "bracket_core/domain_analysis.py:significant_fns_to_domain_structs", "bracket_core/hierarchical_domain_trace_builder.py:_process_file_functions_together", "bracket_core/hierarchical_domain_trace_builder.py:_process_single_function", "bracket_core/hierarchical_domain_trace_builder.py:_process_trace_hierarchical"]}, "bracket_core/localisation/test_horizontal.py:main": {"name": "main", "filepath": "bracket_core/localisation/test_horizontal.py", "callees": ["bracket_core/localisation/test_horizontal.py:test_openai_api", "bracket_core/localisation/test_horizontal.py:test_domain_evaluation"], "callers": []}, "bracket_core/domain_diagram_generator.py:read_function_data": {"name": "read_function_data", "filepath": "bracket_core/domain_diagram_generator.py", "callees": ["pandas", "json"], "callers": ["bracket_core/domain_diagram_generator.py:generate_all_diagrams"]}, "bracket_core/hybrid_kg.py:_process_file": {"name": "_process_file", "filepath": "bracket_core/hybrid_kg.py", "callees": ["get_parser", "bracket_core/hybrid_kg.py:_extract_functions_and_calls"], "callers": ["bracket_core/hybrid_kg.py:_process_repository"]}, "bracket_core/parsing_repomap.py:add_symbol": {"name": "add_symbol", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:__init__"], "callers": []}, "bracket_core/leaf_node_token_calculator.py:build_domain_hierarchy": {"name": "build_domain_hierarchy", "filepath": "bracket_core/leaf_node_token_calculator.py", "callees": ["bracket_core/leaf_node_token_calculator.py:__init__"], "callers": ["bracket_core/leaf_node_token_calculator.py:analyze_leaf_domains"]}, "bracket_core/parsing_repomap.py:get_symbol": {"name": "get_symbol", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:__init__"], "callers": ["bracket_core/parsing_repomap.py:resolve_symbol"]}, "bracket_core/llm/openrouter/client.py:generate_json": {"name": "generate_json", "filepath": "bracket_core/llm/openrouter/client.py", "callees": ["bracket_core/llm/openrouter/client.py:__init__", "json"], "callers": []}, "bracket_core/domain_taxonomy_token_analyzer.py:save_analysis_results": {"name": "save_analysis_results", "filepath": "bracket_core/domain_taxonomy_token_analyzer.py", "callees": ["time", "os", "pandas"], "callers": ["bracket_core/domain_taxonomy_token_analyzer.py:run_analysis"]}, "bracket_core/domain_classifier.py:main": {"name": "main", "filepath": "bracket_core/domain_classifier.py", "callees": ["<PERSON><PERSON><PERSON><PERSON>", "bracket_core/domain_classifier.py:__init__", "bracket_core/domain_classifier.py:classify_domains"], "callers": []}, "bracket_core/parsing_repomap.py:resolve_symbol": {"name": "resolve_symbol", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:get_symbol", "bracket_core/parsing_repomap.py:__init__"], "callers": []}, "bracket_core/domain_trace_token_analyzer.py:save_analysis_results": {"name": "save_analysis_results", "filepath": "bracket_core/domain_trace_token_analyzer.py", "callees": ["time", "os", "pandas"], "callers": ["bracket_core/domain_trace_token_analyzer.py:run_analysis"]}, "bracket_core/domain_trace_builder.py:read_domain_analysis_yaml": {"name": "read_domain_analysis_yaml", "filepath": "bracket_core/domain_trace_builder.py", "callees": ["yaml"], "callers": ["bracket_core/domain_trace_builder.py:build_and_classify"]}, "bracket_core/global_codebase_explainer.py:prepare_taxonomy_data": {"name": "prepare_taxonomy_data", "filepath": "bracket_core/global_codebase_explainer.py", "callees": ["bracket_core/global_codebase_explainer.py:read_taxonomy_json", "bracket_core/global_codebase_explainer.py:extract_codebase_overview", "bracket_core/global_codebase_explainer.py:extract_top_level_domains", "bracket_core/global_codebase_explainer.py:extract_domain_diagrams"], "callers": ["bracket_core/global_codebase_explainer.py:generate_explanation"]}, "bracket_core/domain_file_mapper.py:read_domain_yaml": {"name": "read_domain_yaml", "filepath": "bracket_core/domain_file_mapper.py", "callees": ["yaml"], "callers": ["bracket_core/domain_file_mapper.py:map_files_to_domains"]}, "bracket_core/call_graph_generator.py:generate_basic_callgraph": {"name": "generate_basic_callgraph", "filepath": "bracket_core/call_graph_generator.py", "callees": ["bracket_core/call_graph_generator.py:group_files_by_language", "re", "os", "json", "bracket_core/call_graph_generator.py:create_graph_structure"], "callers": ["bracket_core/call_graph_generator.py:main"]}, "bracket_core/hierarchical_domain_trace_builder.py:_process_chunk_with_semaphore": {"name": "_process_chunk_with_semaphore", "filepath": "bracket_core/hierarchical_domain_trace_builder.py", "callees": ["Dict", "bracket_core/hierarchical_domain_trace_builder.py:_call_openai_api", "<PERSON><PERSON>", "traceback"], "callers": []}, "bracket_core/hierarchical_domain_file_mapper.py:get_domains_at_level": {"name": "get_domains_at_level", "filepath": "bracket_core/hierarchical_domain_file_mapper.py", "callees": ["bracket_core/hierarchical_domain_file_mapper.py:__init__"], "callers": ["bracket_core/hierarchical_domain_file_mapper.py:map_files_to_domains_hierarchically", "bracket_core/hierarchical_domain_file_mapper.py:_save_current_mappings_to_disk"]}, "bracket_core/domain_trace_builder.py:read_functions_parquet": {"name": "read_functions_parquet", "filepath": "bracket_core/domain_trace_builder.py", "callees": ["pandas"], "callers": ["bracket_core/domain_trace_builder.py:build_and_classify"]}, "bracket_core/hybrid_kg.py:_extract_functions_and_calls": {"name": "_extract_functions_and_calls", "filepath": "bracket_core/hybrid_kg.py", "callees": ["os", "bracket_core/hybrid_kg.py:_file_path_to_module_path", "bracket_core/hybrid_kg.py:_get_function_query", "bracket_core/hybrid_kg.py:_get_call_query", "get_language", "bracket_core/hybrid_kg.py:_process_function_definitions", "bracket_core/hybrid_kg.py:_process_function_calls"], "callers": ["bracket_core/hybrid_kg.py:_process_file"]}, "bracket_core/domain_file_mapper.py:read_file_call_graph_yaml": {"name": "read_file_call_graph_yaml", "filepath": "bracket_core/domain_file_mapper.py", "callees": ["yaml"], "callers": ["bracket_core/domain_file_mapper.py:map_files_to_domains"]}, "bracket_core/hierarchical_domain_file_mapper.py:get_parent_domain_path": {"name": "get_parent_domain_path", "filepath": "bracket_core/hierarchical_domain_file_mapper.py", "callees": [], "callers": ["bracket_core/hierarchical_domain_file_mapper.py:get_files_for_parent_domain", "bracket_core/hierarchical_domain_file_mapper.py:map_files_to_domains_hierarchically"]}, "bracket_core/global_codebase_explainer.py:create_explanation_prompt": {"name": "create_explanation_prompt", "filepath": "bracket_core/global_codebase_explainer.py", "callees": ["bracket_core/global_codebase_explainer.py:__init__"], "callers": ["bracket_core/global_codebase_explainer.py:generate_explanation"]}, "bracket_core/domain_trace_builder.py:prepare_function_data": {"name": "prepare_function_data", "filepath": "bracket_core/domain_trace_builder.py", "callees": ["pandas"], "callers": ["bracket_core/domain_trace_builder.py:build_and_classify"]}, "bracket_core/hierarchical_domain_file_mapper.py:get_files_for_parent_domain": {"name": "get_files_for_parent_domain", "filepath": "bracket_core/hierarchical_domain_file_mapper.py", "callees": ["bracket_core/hierarchical_domain_file_mapper.py:get_parent_domain_path", "bracket_core/hierarchical_domain_file_mapper.py:__init__"], "callers": ["bracket_core/hierarchical_domain_file_mapper.py:map_files_to_domains_hierarchically"]}, "bracket_core/localisation/horizontal_localisation.py:load_domain_taxonomy": {"name": "load_domain_taxonomy", "filepath": "bracket_core/localisation/horizontal_localisation.py", "callees": ["json", "bracket_core/localisation/horizontal_localisation.py:__init__", "traceback"], "callers": ["bracket_core/localisation/horizontal_localisation.py:find_relevant_functions"]}, "bracket_core/domain_file_mapper.py:extract_top_level_domains": {"name": "extract_top_level_domains", "filepath": "bracket_core/domain_file_mapper.py", "callees": ["Dict", "bracket_core/domain_file_mapper.py:_generate_domain_description"], "callers": ["bracket_core/domain_file_mapper.py:map_files_to_domains"]}, "bracket_core/domain_file_mapper.py:_generate_domain_description": {"name": "_generate_domain_description", "filepath": "bracket_core/domain_file_mapper.py", "callees": ["Dict", "bracket_core/domain_file_mapper.py:_generate_domain_description"], "callers": ["bracket_core/domain_file_mapper.py:extract_top_level_domains", "bracket_core/domain_file_mapper.py:_generate_domain_description"]}, "bracket_core/enhanced_domain_trace_builder.py:map_functions_to_files": {"name": "map_functions_to_files", "filepath": "bracket_core/enhanced_domain_trace_builder.py", "callees": ["defaultdict", "pandas", "bracket_core/enhanced_domain_trace_builder.py:_normalize_file_path", "os"], "callers": ["bracket_core/enhanced_domain_trace_builder.py:build_and_classify"]}, "bracket_core/domain_taxonomy_mapper.py:read_domain_diagrams": {"name": "read_domain_diagrams", "filepath": "bracket_core/domain_taxonomy_mapper.py", "callees": ["os", "bracket_core/domain_taxonomy_mapper.py:read_diagram_name_mapping", "bracket_core/domain_taxonomy_mapper.py:__init__", "traceback"], "callers": ["bracket_core/domain_taxonomy_mapper.py:map_taxonomy"]}, "bracket_core/hybrid_kg.py:_file_path_to_module_path": {"name": "_file_path_to_module_path", "filepath": "bracket_core/hybrid_kg.py", "callees": ["os"], "callers": ["bracket_core/hybrid_kg.py:_extract_functions_and_calls"]}, "bracket_core/hybrid_kg.py:_get_call_query": {"name": "_get_call_query", "filepath": "bracket_core/hybrid_kg.py", "callees": [], "callers": ["bracket_core/hybrid_kg.py:_extract_functions_and_calls"]}, "bracket_core/hybrid_kg.py:_process_function_definitions": {"name": "_process_function_definitions", "filepath": "bracket_core/hybrid_kg.py", "callees": ["<PERSON><PERSON>", "os", "Any", "bracket_core/hybrid_kg.py:FunctionNode"], "callers": ["bracket_core/hybrid_kg.py:_extract_functions_and_calls"]}, "bracket_core/hybrid_kg.py:_process_function_calls": {"name": "_process_function_calls", "filepath": "bracket_core/hybrid_kg.py", "callees": ["<PERSON><PERSON>", "bracket_core/hybrid_kg.py:_should_skip_function_call", "bracket_core/hybrid_kg.py:_extract_call_context", "Dict"], "callers": ["bracket_core/hybrid_kg.py:_extract_functions_and_calls"]}, "bracket_core/leaf_node_token_calculator.py:_is_empty_domain_mapping": {"name": "_is_empty_domain_mapping", "filepath": "bracket_core/leaf_node_token_calculator.py", "callees": ["bracket_core/leaf_node_token_calculator.py:__init__"], "callers": ["bracket_core/leaf_node_token_calculator.py:prepare_leaf_domain_request"]}, "bracket_core/hierarchical_domain_file_mapper.py:map_files_to_domains_hierarchically": {"name": "map_files_to_domains_hierarchically", "filepath": "bracket_core/hierarchical_domain_file_mapper.py", "callees": ["bracket_core/hierarchical_domain_file_mapper.py:__init__", "bracket_core/hierarchical_domain_file_mapper.py:extract_domain_hierarchy", "get_openai_api_key", "bracket_core/hierarchical_domain_file_mapper.py:get_domains_at_level", "bracket_core/hierarchical_domain_file_mapper.py:get_files_for_parent_domain", "bracket_core/hierarchical_domain_file_mapper.py:_prepare_file_info", "bracket_core/hierarchical_domain_file_mapper.py:get_parent_domain_path", "bracket_core/hierarchical_domain_file_mapper.py:_process_batch", "bracket_core/hierarchical_domain_file_mapper.py:_run_task_with_semaphore", "asyncio", "bracket_core/hierarchical_domain_file_mapper.py:_save_current_mappings_to_disk", "yaml", "bracket_core/hierarchical_domain_file_mapper.py:HierarchicalDomainFileMapperResult", "traceback", "bracket_core/hierarchical_domain_file_mapper.py:map_files_to_domains_hierarchically"], "callers": ["bracket_core/hierarchical_domain_file_mapper.py:map_files_to_domains_hierarchically", "bracket_core/hierarchical_domain_file_mapper.py:main"]}, "bracket_core/hierarchical_domain_file_mapper.py:_prepare_file_info": {"name": "_prepare_file_info", "filepath": "bracket_core/hierarchical_domain_file_mapper.py", "callees": ["Dict"], "callers": ["bracket_core/hierarchical_domain_file_mapper.py:map_files_to_domains_hierarchically"]}, "bracket_core/hierarchical_domain_file_mapper.py:_process_batch": {"name": "_process_batch", "filepath": "bracket_core/hierarchical_domain_file_mapper.py", "callees": ["get_openai_api_key", "bracket_core/hierarchical_domain_file_mapper.py:_classify_files_to_specific_domain", "bracket_core/hierarchical_domain_file_mapper.py:__init__", "bracket_core/hierarchical_domain_file_mapper.py:_save_current_mappings_to_disk", "traceback", "bracket_core/hierarchical_domain_file_mapper.py:HierarchicalDomainFileMapperResult"], "callers": ["bracket_core/hierarchical_domain_file_mapper.py:map_files_to_domains_hierarchically"]}, "bracket_core/hierarchical_domain_file_mapper.py:_run_task_with_semaphore": {"name": "_run_task_with_semaphore", "filepath": "bracket_core/hierarchical_domain_file_mapper.py", "callees": ["asyncio"], "callers": ["bracket_core/hierarchical_domain_file_mapper.py:map_files_to_domains_hierarchically"]}, "bracket_core/hierarchical_domain_file_mapper.py:_save_current_mappings_to_disk": {"name": "_save_current_mappings_to_disk", "filepath": "bracket_core/hierarchical_domain_file_mapper.py", "callees": ["bracket_core/hierarchical_domain_file_mapper.py:get_domains_at_level", "bracket_core/hierarchical_domain_file_mapper.py:__init__", "yaml", "os", "traceback"], "callers": ["bracket_core/hierarchical_domain_file_mapper.py:map_files_to_domains_hierarchically", "bracket_core/hierarchical_domain_file_mapper.py:_process_batch", "bracket_core/hierarchical_domain_file_mapper.py:_classify_files_to_specific_domain"]}, "bracket_core/hierarchical_domain_file_mapper.py:HierarchicalDomainFileMapperResult": {"name": "HierarchicalDomainFileMapperResult", "filepath": "bracket_core/hierarchical_domain_file_mapper.py", "callees": [], "callers": ["bracket_core/hierarchical_domain_file_mapper.py:map_files_to_domains_hierarchically", "bracket_core/hierarchical_domain_file_mapper.py:_process_batch"]}, "bracket_core/domain_taxonomy_token_analyzer.py:run_analysis": {"name": "run_analysis", "filepath": "bracket_core/domain_taxonomy_token_analyzer.py", "callees": ["bracket_core/domain_taxonomy_token_analyzer.py:analyze_taxonomy", "bracket_core/domain_taxonomy_token_analyzer.py:save_analysis_results"], "callers": ["bracket_core/domain_taxonomy_token_analyzer.py:main"]}, "bracket_core/domain_taxonomy_token_analyzer.py:main": {"name": "main", "filepath": "bracket_core/domain_taxonomy_token_analyzer.py", "callees": ["<PERSON><PERSON><PERSON><PERSON>", "bracket_core/domain_taxonomy_token_analyzer.py:__init__", "time", "bracket_core/domain_taxonomy_token_analyzer.py:run_analysis", "traceback"], "callers": []}, "bracket_core/llm/openrouter/client.py:astream_generate": {"name": "astream_generate", "filepath": "bracket_core/llm/openrouter/client.py", "callees": ["bracket_core/llm/openrouter/client.py:__init__"], "callers": []}, "bracket_core/leaf_node_token_calculator.py:prepare_leaf_domain_request": {"name": "prepare_leaf_domain_request", "filepath": "bracket_core/leaf_node_token_calculator.py", "callees": ["bracket_core/leaf_node_token_calculator.py:__init__", "bracket_core/leaf_node_token_calculator.py:_is_empty_domain_mapping", "json"], "callers": ["bracket_core/leaf_node_token_calculator.py:analyze_leaf_domains"]}, "bracket_core/domain_diagram_generator.py:build_domain_hierarchy": {"name": "build_domain_hierarchy", "filepath": "bracket_core/domain_diagram_generator.py", "callees": ["bracket_core/domain_diagram_generator.py:__init__"], "callers": ["bracket_core/domain_diagram_generator.py:generate_all_diagrams"]}, "bracket_core/domain_trace_builder.py:classify_functions": {"name": "classify_functions", "filepath": "bracket_core/domain_trace_builder.py", "callees": ["Dict", "bracket_core/domain_trace_builder.py:FunctionClassificationResult", "asyncio", "bracket_core/domain_trace_builder.py:StatusTracker", "time", "bracket_core/domain_trace_builder.py:APIRequest", "aiohttp"], "callers": ["bracket_core/domain_trace_builder.py:build_and_classify"]}, "bracket_core/domain_trace_builder.py:FunctionClassificationResult": {"name": "FunctionClassificationResult", "filepath": "bracket_core/domain_trace_builder.py", "callees": [], "callers": ["bracket_core/domain_trace_builder.py:classify_functions"]}, "bracket_core/domain_trace_builder.py:StatusTracker": {"name": "StatusTracker", "filepath": "bracket_core/domain_trace_builder.py", "callees": [], "callers": ["bracket_core/domain_trace_builder.py:classify_functions"]}, "bracket_core/domain_trace_builder.py:APIRequest": {"name": "APIRequest", "filepath": "bracket_core/domain_trace_builder.py", "callees": [], "callers": ["bracket_core/domain_trace_builder.py:classify_functions"]}, "bracket_core/cli.py:visualize": {"name": "visualize", "filepath": "bracket_core/cli.py", "callees": ["typer", "print"], "callers": []}, "bracket_core/hybrid_kg.py:FunctionNode": {"name": "FunctionNode", "filepath": "bracket_core/hybrid_kg.py", "callees": [], "callers": ["bracket_core/hybrid_kg.py:_process_function_definitions"]}, "bracket_core/localisation/horizontal_localisation.py:load_semantic_documented_functions": {"name": "load_semantic_documented_functions", "filepath": "bracket_core/localisation/horizontal_localisation.py", "callees": ["os", "pandas", "bracket_core/localisation/horizontal_localisation.py:_create_function_lookup_indices", "bracket_core/localisation/horizontal_localisation.py:__init__", "traceback"], "callers": ["bracket_core/localisation/horizontal_localisation.py:extract_relevant_functions"]}, "bracket_core/localisation/horizontal_localisation.py:_create_function_lookup_indices": {"name": "_create_function_lookup_indices", "filepath": "bracket_core/localisation/horizontal_localisation.py", "callees": ["time", "bracket_core/localisation/horizontal_localisation.py:__init__"], "callers": ["bracket_core/localisation/horizontal_localisation.py:load_semantic_documented_functions"]}, "bracket_core/domain_file_mapper.py:map_files_to_domains": {"name": "map_files_to_domains", "filepath": "bracket_core/domain_file_mapper.py", "callees": ["bracket_core/domain_file_mapper.py:read_domain_yaml", "bracket_core/domain_file_mapper.py:read_file_call_graph_yaml", "bracket_core/domain_file_mapper.py:extract_top_level_domains", "bracket_core/domain_file_mapper.py:_classify_files_to_domains", "yaml", "bracket_core/domain_file_mapper.py:DomainFileMapperResult", "traceback", "bracket_core/domain_file_mapper.py:__init__", "bracket_core/domain_file_mapper.py:map_files_to_domains"], "callers": ["bracket_core/domain_file_mapper.py:map_files_to_domains", "bracket_core/domain_file_mapper.py:main"]}, "bracket_core/domain_file_mapper.py:_classify_files_to_domains": {"name": "_classify_files_to_domains", "filepath": "bracket_core/domain_file_mapper.py", "callees": ["Dict", "asyncio", "json", "bracket_core/domain_file_mapper.py:APIRequest", "time", "aiohttp"], "callers": ["bracket_core/domain_file_mapper.py:map_files_to_domains"]}, "bracket_core/domain_file_mapper.py:DomainFileMapperResult": {"name": "DomainFileMapperResult", "filepath": "bracket_core/domain_file_mapper.py", "callees": [], "callers": ["bracket_core/domain_file_mapper.py:map_files_to_domains"]}, "bracket_core/hierarchical_domain_trace_builder.py:_normalize_file_path": {"name": "_normalize_file_path", "filepath": "bracket_core/hierarchical_domain_trace_builder.py", "callees": ["re", "os"], "callers": ["bracket_core/hierarchical_domain_trace_builder.py:read_domain_file_mappings", "bracket_core/hierarchical_domain_trace_builder.py:_process_file_for_trace"]}, "bracket_core/cli.py:analyze": {"name": "analyze", "filepath": "bracket_core/cli.py", "callees": ["typer", "print", "os", "time", "RepoAnalysisFlow", "asyncio"], "callers": []}, "RepoAnalysisFlow": {"name": "RepoAnalysisFlow", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/cli.py:analyze"]}, "bracket_core/call_graph_generator.py:enhance_cross_file_resolution": {"name": "enhance_cross_file_resolution", "filepath": "bracket_core/call_graph_generator.py", "callees": [], "callers": ["bracket_core/call_graph_generator.py:main"]}, "bracket_core/parsing_repomap.py:token_count": {"name": "token_count", "filepath": "bracket_core/parsing_repomap.py", "callees": [], "callers": []}, "generate_hybrid_knowledge_graph": {"name": "generate_hybrid_knowledge_graph", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/irl.py:generate_knowledge_graph"]}, "bracket_core/domain_trace_token_analyzer.py:run_analysis": {"name": "run_analysis", "filepath": "bracket_core/domain_trace_token_analyzer.py", "callees": ["bracket_core/domain_trace_token_analyzer.py:analyze_traces", "bracket_core/domain_trace_token_analyzer.py:save_analysis_results"], "callers": []}, "bracket_core/parsing_repomap.py:read_text": {"name": "read_text", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:read_text", "logging"], "callers": ["bracket_core/parsing_repomap.py:read_text", "bracket_core/parsing_repomap.py:get_tags", "bracket_core/parsing_repomap.py:create_graph", "bracket_core/parsing_repomap.py:_find_variable_assignments", "bracket_core/parsing_repomap.py:_resolve_class_module", "bracket_core/parsing_repomap.py:_analyze_method_return_type", "bracket_core/parsing_repomap.py:_find_parent_classes", "bracket_core/parsing_repomap.py:_resolve_class_to_module", "bracket_core/parsing_repomap.py:enrich_with_line_numbers"]}, "bracket_core/parsing_repomap.py:get_rel_fname": {"name": "get_rel_fname", "filepath": "bracket_core/parsing_repomap.py", "callees": ["os"], "callers": ["bracket_core/parsing_repomap.py:file_path_to_module_path", "bracket_core/parsing_repomap.py:create_graph"]}, "bracket_core/parsing_repomap.py:get_mtime": {"name": "get_mtime", "filepath": "bracket_core/parsing_repomap.py", "callees": ["os", "bracket_core/parsing_repomap.py:__init__"], "callers": ["bracket_core/parsing_repomap.py:get_tags"]}, "graph_to_dataframes": {"name": "graph_to_dataframes", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/irl.py:save_graph_as_parquet", "bracket_core/hybrid_kg.py:save_graph_as_parquet"]}, "bracket_core/irl.py:convert_mixed_types": {"name": "convert_mixed_types", "filepath": "bracket_core/irl.py", "callees": ["json"], "callers": ["bracket_core/irl.py:save_graph_as_parquet"]}, "bracket_core/parsing_repomap.py:file_path_to_module_path": {"name": "file_path_to_module_path", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:get_rel_fname", "os"], "callers": ["bracket_core/parsing_repomap.py:module_path_to_file_path", "bracket_core/parsing_repomap.py:extract_imports", "bracket_core/parsing_repomap.py:_resolve_js_module_path", "bracket_core/parsing_repomap.py:get_tags"]}, "bracket_core/enhanced_domain_trace_builder.py:get_domain_specific_functions": {"name": "get_domain_specific_functions", "filepath": "bracket_core/enhanced_domain_trace_builder.py", "callees": ["bracket_core/enhanced_domain_trace_builder.py:__init__", "os"], "callers": ["bracket_core/enhanced_domain_trace_builder.py:classify_functions_with_reduced_search_space"]}, "bracket_core/domain_taxonomy_mapper.py:build_taxonomy_tree": {"name": "build_taxonomy_tree", "filepath": "bracket_core/domain_taxonomy_mapper.py", "callees": ["bracket_core/domain_taxonomy_mapper.py:DomainTaxonomyNode", "Dict", "bracket_core/domain_taxonomy_mapper.py:_process_area", "List"], "callers": ["bracket_core/domain_taxonomy_mapper.py:map_taxonomy"]}, "bracket_core/domain_taxonomy_mapper.py:DomainTaxonomyNode": {"name": "DomainTaxonomyNode", "filepath": "bracket_core/domain_taxonomy_mapper.py", "callees": [], "callers": ["bracket_core/domain_taxonomy_mapper.py:build_taxonomy_tree", "bracket_core/domain_taxonomy_mapper.py:_process_area"]}, "bracket_core/domain_taxonomy_mapper.py:_process_area": {"name": "_process_area", "filepath": "bracket_core/domain_taxonomy_mapper.py", "callees": ["Dict", "bracket_core/domain_taxonomy_mapper.py:DomainTaxonomyNode", "bracket_core/domain_taxonomy_mapper.py:_process_area", "List"], "callers": ["bracket_core/domain_taxonomy_mapper.py:build_taxonomy_tree", "bracket_core/domain_taxonomy_mapper.py:_process_area"]}, "List": {"name": "List", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/domain_taxonomy_mapper.py:build_taxonomy_tree", "bracket_core/domain_taxonomy_mapper.py:_process_area", "bracket_core/localisation/horizontal_localisation.py:explore_domain_hierarchy"]}, "bracket_core/parsing_repomap.py:module_path_to_file_path": {"name": "module_path_to_file_path", "filepath": "bracket_core/parsing_repomap.py", "callees": ["os", "bracket_core/parsing_repomap.py:file_path_to_module_path"], "callers": ["bracket_core/parsing_repomap.py:_extract_python_imports", "bracket_core/parsing_repomap.py:_extract_js_imports", "bracket_core/parsing_repomap.py:_find_variable_assignments", "bracket_core/parsing_repomap.py:_resolve_class_module", "bracket_core/parsing_repomap.py:_analyze_method_return_type", "bracket_core/parsing_repomap.py:_find_parent_classes", "bracket_core/parsing_repomap.py:_resolve_class_to_module", "bracket_core/parsing_repomap.py:_get_default_module_for_primitive"]}, "bracket_core/domain_file_mapper.py:APIRequest": {"name": "APIRequest", "filepath": "bracket_core/domain_file_mapper.py", "callees": [], "callers": ["bracket_core/domain_file_mapper.py:_classify_files_to_domains"]}, "bracket_core/domain_analysis.py:_merge_domain_results": {"name": "_merge_domain_results", "filepath": "bracket_core/domain_analysis.py", "callees": ["yaml", "bracket_core/domain_analysis.py:_call_openai_api", "bracket_core/domain_analysis.py:_simple_merge_domains", "<PERSON><PERSON>", "json"], "callers": ["bracket_core/domain_analysis.py:create_hierar_domain_struct"]}, "bracket_core/domain_analysis.py:_simple_merge_domains": {"name": "_simple_merge_domains", "filepath": "bracket_core/domain_analysis.py", "callees": [], "callers": ["bracket_core/domain_analysis.py:_merge_domain_results"]}, "bracket_core/global_codebase_explainer.py:create_questions_prompt": {"name": "create_questions_prompt", "filepath": "bracket_core/global_codebase_explainer.py", "callees": ["bracket_core/global_codebase_explainer.py:__init__"], "callers": ["bracket_core/global_codebase_explainer.py:generate_questions"]}, "document_functions_with_significance": {"name": "document_functions_with_significance", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/irl.py:generate_documentation"]}, "bracket_core/documenting.py:append_to_jsonl": {"name": "append_to_jsonl", "filepath": "bracket_core/documenting.py", "callees": ["json"], "callers": ["bracket_core/documenting.py:call_api"]}, "bracket_core/domain_diagram_generator.py:sort_domains_by_level": {"name": "sort_domains_by_level", "filepath": "bracket_core/domain_diagram_generator.py", "callees": ["bracket_core/domain_diagram_generator.py:__init__"], "callers": ["bracket_core/domain_diagram_generator.py:generate_all_diagrams"]}, "bracket_core/hybrid_kg.py:_should_skip_function_call": {"name": "_should_skip_function_call", "filepath": "bracket_core/hybrid_kg.py", "callees": ["bracket_core/hybrid_kg.py:_is_builtin_function", "bracket_core/hybrid_kg.py:_is_library_function", "bracket_core/hybrid_kg.py:__init__"], "callers": ["bracket_core/hybrid_kg.py:_process_function_calls"]}, "bracket_core/hybrid_kg.py:_extract_call_context": {"name": "_extract_call_context", "filepath": "bracket_core/hybrid_kg.py", "callees": ["Any"], "callers": ["bracket_core/hybrid_kg.py:_process_function_calls"]}, "bracket_core/domain_taxonomy_mapper.py:map_functions_to_taxonomy": {"name": "map_functions_to_taxonomy", "filepath": "bracket_core/domain_taxonomy_mapper.py", "callees": ["Dict"], "callers": ["bracket_core/domain_taxonomy_mapper.py:map_taxonomy"]}, "bracket_core/domain_diagram_generator.py:_get_cache_path": {"name": "_get_cache_path", "filepath": "bracket_core/domain_diagram_generator.py", "callees": ["os"], "callers": ["bracket_core/domain_diagram_generator.py:_check_cache", "bracket_core/domain_diagram_generator.py:_save_to_cache"]}, "bracket_core/hierarchical_domain_trace_builder.py:read_domain_file_mappings": {"name": "read_domain_file_mappings", "filepath": "bracket_core/hierarchical_domain_trace_builder.py", "callees": ["yaml", "bracket_core/hierarchical_domain_trace_builder.py:_normalize_file_path"], "callers": ["bracket_core/hierarchical_domain_trace_builder.py:build_and_classify"]}, "bracket_core/localisation/global_localisation.py:count_tokens_with_tiktoken": {"name": "count_tokens_with_tiktoken", "filepath": "bracket_core/localisation/global_localisation.py", "callees": ["tiktoken"], "callers": ["bracket_core/localisation/global_localisation.py:estimate_token_count"]}, "bracket_core/cli.py:main": {"name": "main", "filepath": "bracket_core/cli.py", "callees": [], "callers": []}, "bracket_core/leaf_node_token_calculator.py:calculate_request_tokens": {"name": "calculate_request_tokens", "filepath": "bracket_core/leaf_node_token_calculator.py", "callees": ["num_tokens_from_string"], "callers": ["bracket_core/leaf_node_token_calculator.py:analyze_leaf_domains"]}, "bracket_core/domain_diagram_generator.py:_check_cache": {"name": "_check_cache", "filepath": "bracket_core/domain_diagram_generator.py", "callees": ["bracket_core/domain_diagram_generator.py:_get_cache_path", "os"], "callers": ["bracket_core/domain_diagram_generator.py:_send_api_request", "bracket_core/domain_diagram_generator.py:_process_api_response"]}, "bracket_core/parsing_repomap.py:extract_imports": {"name": "extract_imports", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:file_path_to_module_path", "bracket_core/parsing_repomap.py:__init__", "bracket_core/parsing_repomap.py:_extract_python_imports", "bracket_core/parsing_repomap.py:_extract_js_imports", "bracket_core/parsing_repomap.py:_extract_java_imports"], "callers": ["bracket_core/parsing_repomap.py:get_tags", "bracket_core/parsing_repomap.py:_resolve_class_module", "bracket_core/parsing_repomap.py:_resolve_class_to_module"]}, "bracket_core/parsing_repomap.py:_extract_python_imports": {"name": "_extract_python_imports", "filepath": "bracket_core/parsing_repomap.py", "callees": ["re", "bracket_core/parsing_repomap.py:module_path_to_file_path", "bracket_core/parsing_repomap.py:__init__"], "callers": ["bracket_core/parsing_repomap.py:extract_imports"]}, "bracket_core/parsing_repomap.py:_extract_js_imports": {"name": "_extract_js_imports", "filepath": "bracket_core/parsing_repomap.py", "callees": ["re", "bracket_core/parsing_repomap.py:_resolve_js_module_path", "bracket_core/parsing_repomap.py:module_path_to_file_path", "bracket_core/parsing_repomap.py:__init__"], "callers": ["bracket_core/parsing_repomap.py:extract_imports"]}, "bracket_core/parsing_repomap.py:_extract_java_imports": {"name": "_extract_java_imports", "filepath": "bracket_core/parsing_repomap.py", "callees": ["re", "bracket_core/parsing_repomap.py:__init__"], "callers": ["bracket_core/parsing_repomap.py:extract_imports"]}, "bracket_core/domain_taxonomy_mapper.py:map_diagrams_to_taxonomy": {"name": "map_diagrams_to_taxonomy", "filepath": "bracket_core/domain_taxonomy_mapper.py", "callees": ["Dict", "bracket_core/domain_taxonomy_mapper.py:__init__", "os"], "callers": ["bracket_core/domain_taxonomy_mapper.py:map_taxonomy"]}, "bracket_core/call_graph_generator.py:main": {"name": "main", "filepath": "bracket_core/call_graph_generator.py", "callees": ["bracket_core/call_graph_generator.py:generate_basic_callgraph", "bracket_core/call_graph_generator.py:find_callgraph_executable", "os", "bracket_core/call_graph_generator.py:group_files_by_language", "sys", "bracket_core/call_graph_generator.py:analyze_language_files", "bracket_core/call_graph_generator.py:merge_yaml_files", "bracket_core/call_graph_generator.py:enhance_cross_file_resolution", "bracket_core/call_graph_generator.py:create_graph_structure", "json"], "callers": []}, "sys": {"name": "sys", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/call_graph_generator.py:main"]}, "bracket_core/enhanced_domain_trace_builder.py:prepare_domain_specific_function_data": {"name": "prepare_domain_specific_function_data", "filepath": "bracket_core/enhanced_domain_trace_builder.py", "callees": ["pandas"], "callers": ["bracket_core/enhanced_domain_trace_builder.py:classify_functions_with_reduced_search_space"]}, "bracket_core/domain_diagram_generator.py:_save_to_cache": {"name": "_save_to_cache", "filepath": "bracket_core/domain_diagram_generator.py", "callees": ["bracket_core/domain_diagram_generator.py:_get_cache_path"], "callers": ["bracket_core/domain_diagram_generator.py:_process_api_response"]}, "bracket_core/leaf_node_token_calculator.py:analyze_leaf_domains": {"name": "analyze_leaf_domains", "filepath": "bracket_core/leaf_node_token_calculator.py", "callees": ["bracket_core/leaf_node_token_calculator.py:read_domain_traces", "bracket_core/leaf_node_token_calculator.py:read_function_data", "bracket_core/leaf_node_token_calculator.py:build_domain_hierarchy", "bracket_core/leaf_node_token_calculator.py:__init__", "bracket_core/leaf_node_token_calculator.py:prepare_leaf_domain_request", "bracket_core/leaf_node_token_calculator.py:calculate_request_tokens", "bracket_core/leaf_node_token_calculator.py:TokenAnalysisResult"], "callers": ["bracket_core/leaf_node_token_calculator.py:main"]}, "bracket_core/leaf_node_token_calculator.py:TokenAnalysisResult": {"name": "TokenAnalysisResult", "filepath": "bracket_core/leaf_node_token_calculator.py", "callees": [], "callers": ["bracket_core/leaf_node_token_calculator.py:analyze_leaf_domains"]}, "bracket_core/localisation/global_localisation.py:__init__": {"name": "__init__", "filepath": "bracket_core/localisation/global_localisation.py", "callees": ["get_openai_api_key", "get_openrouter_client", "os"], "callers": ["bracket_core/localisation/global_localisation.py:third_pass_analyze_relevant_functions", "bracket_core/localisation/global_localisation.py:find_relevant_functions"]}, "bracket_core/parsing_repomap.py:calculate_relationship_weight": {"name": "calculate_relationship_weight", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:get_base_weight", "bracket_core/parsing_repomap.py:_are_different_modules", "math"], "callers": ["bracket_core/parsing_repomap.py:_add_relationship_edge"]}, "bracket_core/parsing_repomap.py:_are_different_modules": {"name": "_are_different_modules", "filepath": "bracket_core/parsing_repomap.py", "callees": [], "callers": ["bracket_core/parsing_repomap.py:calculate_relationship_weight"]}, "bracket_core/hierarchical_domain_trace_builder.py:map_traces_to_domain_paths": {"name": "map_traces_to_domain_paths", "filepath": "bracket_core/hierarchical_domain_trace_builder.py", "callees": [], "callers": ["bracket_core/hierarchical_domain_trace_builder.py:build_and_classify"]}, "bracket_core/domain_diagram_generator.py:_extract_mermaid_diagram": {"name": "_extract_mermaid_diagram", "filepath": "bracket_core/domain_diagram_generator.py", "callees": ["re"], "callers": ["bracket_core/domain_diagram_generator.py:generate_leaf_diagram", "bracket_core/domain_diagram_generator.py:generate_combined_diagram", "bracket_core/domain_diagram_generator.py:_process_api_response"]}, "bracket_core/hierarchical_domain_trace_builder.py:get_domain_specific_functions_hierarchical": {"name": "get_domain_specific_functions_hierarchical", "filepath": "bracket_core/hierarchical_domain_trace_builder.py", "callees": ["bracket_core/hierarchical_domain_trace_builder.py:__init__", "os"], "callers": ["bracket_core/hierarchical_domain_trace_builder.py:_process_trace_hierarchical"]}, "bracket_core/enhanced_domain_trace_builder.py:classify_functions_with_reduced_search_space": {"name": "classify_functions_with_reduced_search_space", "filepath": "bracket_core/enhanced_domain_trace_builder.py", "callees": ["FunctionClassificationResult", "bracket_core/enhanced_domain_trace_builder.py:get_domain_specific_functions", "bracket_core/enhanced_domain_trace_builder.py:prepare_domain_specific_function_data", "Dict", "bracket_core/enhanced_domain_trace_builder.py:_call_openai_api", "<PERSON><PERSON>"], "callers": ["bracket_core/enhanced_domain_trace_builder.py:build_and_classify"]}, "FunctionClassificationResult": {"name": "FunctionClassificationResult", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/enhanced_domain_trace_builder.py:classify_functions_with_reduced_search_space", "bracket_core/hierarchical_domain_trace_builder.py:classify_functions_with_hierarchical_search_space"]}, "bracket_core/enhanced_domain_trace_builder.py:_call_openai_api": {"name": "_call_openai_api", "filepath": "bracket_core/enhanced_domain_trace_builder.py", "callees": ["aiohttp", "asyncio"], "callers": ["bracket_core/enhanced_domain_trace_builder.py:classify_functions_with_reduced_search_space"]}, "bracket_core/localisation/horizontal_localisation.py:build_domain_hierarchy": {"name": "build_domain_hierarchy", "filepath": "bracket_core/localisation/horizontal_localisation.py", "callees": ["bracket_core/localisation/horizontal_localisation.py:DomainNode", "bracket_core/localisation/horizontal_localisation.py:process_domain", "bracket_core/localisation/horizontal_localisation.py:__init__", "traceback"], "callers": ["bracket_core/localisation/horizontal_localisation.py:find_relevant_functions"]}, "bracket_core/localisation/horizontal_localisation.py:DomainNode": {"name": "DomainNode", "filepath": "bracket_core/localisation/horizontal_localisation.py", "callees": [], "callers": ["bracket_core/localisation/horizontal_localisation.py:build_domain_hierarchy"]}, "bracket_core/localisation/horizontal_localisation.py:process_domain": {"name": "process_domain", "filepath": "bracket_core/localisation/horizontal_localisation.py", "callees": [], "callers": ["bracket_core/localisation/horizontal_localisation.py:build_domain_hierarchy"]}, "bracket_core/domain_diagram_generator.py:_call_openai_api": {"name": "_call_openai_api", "filepath": "bracket_core/domain_diagram_generator.py", "callees": ["num_tokens_from_string", "aiohttp", "json"], "callers": ["bracket_core/domain_diagram_generator.py:generate_leaf_diagram", "bracket_core/domain_diagram_generator.py:generate_combined_diagram", "bracket_core/domain_diagram_generator.py:_send_api_request"]}, "bracket_core/documenting.py:save_simplified_csv": {"name": "save_simplified_csv", "filepath": "bracket_core/documenting.py", "callees": ["pandas"], "callers": ["bracket_core/documenting.py:document_functions", "bracket_core/documenting.py:document_functions_with_context_significance"]}, "bracket_core/domain_trace_builder.py:save_classification_results": {"name": "save_classification_results", "filepath": "bracket_core/domain_trace_builder.py", "callees": ["Dict", "yaml", "json"], "callers": ["bracket_core/domain_trace_builder.py:build_and_classify"]}, "bracket_core/parsing_repomap.py:_add_relationship_edge": {"name": "_add_relationship_edge", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:calculate_relationship_weight", "networkx"], "callers": ["bracket_core/parsing_repomap.py:create_graph", "bracket_core/parsing_repomap.py:enrich_with_nuanced_callgraph"]}, "bracket_core/domain_analysis.py:create_hierar_domain_struct": {"name": "create_hierar_domain_struct", "filepath": "bracket_core/domain_analysis.py", "callees": ["yaml", "bracket_core/domain_analysis.py:DomainAnalysisResult", "bracket_core/domain_analysis.py:count_tokens", "bracket_core/domain_analysis.py:significant_fns_to_domain_structs", "bracket_core/domain_analysis.py:_split_functions_into_chunks", "os", "asyncio", "bracket_core/domain_analysis.py:_process_chunk", "bracket_core/domain_analysis.py:process_with_semaphore", "bracket_core/domain_analysis.py:_merge_domain_results", "bracket_core/domain_analysis.py:_consolidate_domains"], "callers": ["bracket_core/domain_analysis.py:domains_from_significant_functions", "bracket_core/domain_analysis.py:main"]}, "bracket_core/domain_analysis.py:DomainAnalysisResult": {"name": "DomainAnalysisResult", "filepath": "bracket_core/domain_analysis.py", "callees": [], "callers": ["bracket_core/domain_analysis.py:create_hierar_domain_struct", "bracket_core/domain_analysis.py:_consolidate_domains", "bracket_core/domain_analysis.py:significant_fns_to_domain_structs"]}, "bracket_core/domain_analysis.py:significant_fns_to_domain_structs": {"name": "significant_fns_to_domain_structs", "filepath": "bracket_core/domain_analysis.py", "callees": ["yaml", "bracket_core/domain_analysis.py:_get_granularity_guidance", "bracket_core/domain_analysis.py:_call_openai_api", "bracket_core/domain_analysis.py:DomainAnalysisResult", "<PERSON><PERSON>", "json"], "callers": ["bracket_core/domain_analysis.py:create_hierar_domain_struct"]}, "bracket_core/domain_analysis.py:process_with_semaphore": {"name": "process_with_semaphore", "filepath": "bracket_core/domain_analysis.py", "callees": [], "callers": ["bracket_core/domain_analysis.py:create_hierar_domain_struct"]}, "bracket_core/domain_analysis.py:_consolidate_domains": {"name": "_consolidate_domains", "filepath": "bracket_core/domain_analysis.py", "callees": ["yaml", "bracket_core/domain_analysis.py:_call_openai_api", "<PERSON><PERSON>", "json", "bracket_core/domain_analysis.py:DomainAnalysisResult"], "callers": ["bracket_core/domain_analysis.py:create_hierar_domain_struct"]}, "bracket_core/leaf_node_token_calculator.py:save_analysis_results": {"name": "save_analysis_results", "filepath": "bracket_core/leaf_node_token_calculator.py", "callees": ["time", "os", "pandas", "bracket_core/leaf_node_token_calculator.py:__init__", "Dict"], "callers": ["bracket_core/leaf_node_token_calculator.py:main"]}, "bracket_core/domain_trace_builder.py:build_and_classify": {"name": "build_and_classify", "filepath": "bracket_core/domain_trace_builder.py", "callees": ["bracket_core/domain_trace_builder.py:read_domain_analysis_yaml", "bracket_core/domain_trace_builder.py:build_domain_traces", "bracket_core/domain_trace_builder.py:read_functions_parquet", "bracket_core/domain_trace_builder.py:prepare_function_data", "bracket_core/domain_trace_builder.py:classify_functions", "bracket_core/domain_trace_builder.py:save_classification_results"], "callers": ["bracket_core/domain_trace_builder.py:main"]}, "bracket_core/global_codebase_explainer.py:generate_questions": {"name": "generate_questions", "filepath": "bracket_core/global_codebase_explainer.py", "callees": ["bracket_core/global_codebase_explainer.py:create_questions_prompt", "ChatOpenAI", "json", "re", "traceback"], "callers": []}, "ChatOpenAI": {"name": "ChatOpenAI", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/global_codebase_explainer.py:generate_questions", "bracket_core/global_codebase_explainer.py:generate_explanation"]}, "bracket_core/domain_diagram_generator.py:_is_empty_domain_mapping": {"name": "_is_empty_domain_mapping", "filepath": "bracket_core/domain_diagram_generator.py", "callees": ["bracket_core/domain_diagram_generator.py:__init__"], "callers": ["bracket_core/domain_diagram_generator.py:generate_leaf_diagram", "bracket_core/domain_diagram_generator.py:_prepare_leaf_domain_request"]}, "bracket_core/documenting.py:convert_parquet_to_simplified_csv": {"name": "convert_parquet_to_simplified_csv", "filepath": "bracket_core/documenting.py", "callees": ["pandas"], "callers": []}, "bracket_core/localisation/horizontal_localisation.py:get_domains_at_level": {"name": "get_domains_at_level", "filepath": "bracket_core/localisation/horizontal_localisation.py", "callees": ["bracket_core/localisation/horizontal_localisation.py:collect_domains"], "callers": ["bracket_core/localisation/horizontal_localisation.py:get_relevant_domains_at_level"]}, "bracket_core/localisation/horizontal_localisation.py:collect_domains": {"name": "collect_domains", "filepath": "bracket_core/localisation/horizontal_localisation.py", "callees": ["bracket_core/localisation/horizontal_localisation.py:collect_domains"], "callers": ["bracket_core/localisation/horizontal_localisation.py:get_domains_at_level", "bracket_core/localisation/horizontal_localisation.py:collect_domains"]}, "DomainAnalysisIntegration": {"name": "DomainAnalysisIntegration", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/irl.py:create_hierarchical_domains"]}, "bracket_core/domain_trace_builder.py:main": {"name": "main", "filepath": "bracket_core/domain_trace_builder.py", "callees": ["<PERSON><PERSON><PERSON><PERSON>", "bracket_core/domain_trace_builder.py:__init__", "bracket_core/domain_trace_builder.py:build_and_classify"], "callers": []}, "bracket_core/hierarchical_domain_file_mapper.py:_create_adaptive_batches": {"name": "_create_adaptive_batches", "filepath": "bracket_core/hierarchical_domain_file_mapper.py", "callees": [], "callers": ["bracket_core/hierarchical_domain_file_mapper.py:_classify_files_to_specific_domain"]}, "bracket_core/domain_diagram_generator.py:generate_leaf_diagram": {"name": "generate_leaf_diagram", "filepath": "bracket_core/domain_diagram_generator.py", "callees": ["bracket_core/domain_diagram_generator.py:_is_empty_domain_mapping", "bracket_core/domain_diagram_generator.py:__init__", "json", "num_tokens_from_string", "bracket_core/domain_diagram_generator.py:_call_openai_api", "bracket_core/domain_diagram_generator.py:_extract_mermaid_diagram"], "callers": []}, "bracket_core/hierarchical_domain_trace_builder.py:classify_functions_with_hierarchical_search_space": {"name": "classify_functions_with_hierarchical_search_space", "filepath": "bracket_core/hierarchical_domain_trace_builder.py", "callees": ["FunctionClassificationResult", "bracket_core/hierarchical_domain_trace_builder.py:_is_leaf_node", "bracket_core/hierarchical_domain_trace_builder.py:_process_leaf_node_file_by_file"], "callers": ["bracket_core/hierarchical_domain_trace_builder.py:build_and_classify"]}, "bracket_core/hierarchical_domain_trace_builder.py:_process_leaf_node_file_by_file": {"name": "_process_leaf_node_file_by_file", "filepath": "bracket_core/hierarchical_domain_trace_builder.py", "callees": ["bracket_core/hierarchical_domain_trace_builder.py:__init__", "bracket_core/hierarchical_domain_trace_builder.py:_process_file_for_trace", "asyncio"], "callers": ["bracket_core/hierarchical_domain_trace_builder.py:classify_functions_with_hierarchical_search_space"]}, "bracket_core/localisation/horizontal_localisation.py:get_relevant_domains_at_level": {"name": "get_relevant_domains_at_level", "filepath": "bracket_core/localisation/horizontal_localisation.py", "callees": ["bracket_core/localisation/horizontal_localisation.py:get_domains_at_level", "bracket_core/localisation/horizontal_localisation.py:collect_relevant_children"], "callers": ["bracket_core/localisation/horizontal_localisation.py:explore_domain_hierarchy"]}, "bracket_core/localisation/horizontal_localisation.py:collect_relevant_children": {"name": "collect_relevant_children", "filepath": "bracket_core/localisation/horizontal_localisation.py", "callees": ["bracket_core/localisation/horizontal_localisation.py:collect_relevant_children"], "callers": ["bracket_core/localisation/horizontal_localisation.py:get_relevant_domains_at_level", "bracket_core/localisation/horizontal_localisation.py:collect_relevant_children"]}, "bracket_core/domain_taxonomy_mapper.py:taxonomy_tree_to_json": {"name": "taxonomy_tree_to_json", "filepath": "bracket_core/domain_taxonomy_mapper.py", "callees": ["bracket_core/domain_taxonomy_mapper.py:node_to_dict"], "callers": ["bracket_core/domain_taxonomy_mapper.py:map_taxonomy"]}, "bracket_core/domain_taxonomy_mapper.py:node_to_dict": {"name": "node_to_dict", "filepath": "bracket_core/domain_taxonomy_mapper.py", "callees": ["bracket_core/domain_taxonomy_mapper.py:node_to_dict"], "callers": ["bracket_core/domain_taxonomy_mapper.py:taxonomy_tree_to_json", "bracket_core/domain_taxonomy_mapper.py:node_to_dict"]}, "bracket_core/leaf_node_token_calculator.py:main": {"name": "main", "filepath": "bracket_core/leaf_node_token_calculator.py", "callees": ["<PERSON><PERSON><PERSON><PERSON>", "bracket_core/leaf_node_token_calculator.py:__init__", "time", "bracket_core/leaf_node_token_calculator.py:analyze_leaf_domains", "bracket_core/leaf_node_token_calculator.py:save_analysis_results", "traceback"], "callers": []}, "bracket_core/localisation/horizontal_localisation.py:prepare_domain_evaluation_requests": {"name": "prepare_domain_evaluation_requests", "filepath": "bracket_core/localisation/horizontal_localisation.py", "callees": ["bracket_core/localisation/horizontal_localisation.py:HorizontalAPIRequest"], "callers": []}, "bracket_core/localisation/horizontal_localisation.py:HorizontalAPIRequest": {"name": "HorizontalAPIRequest", "filepath": "bracket_core/localisation/horizontal_localisation.py", "callees": [], "callers": ["bracket_core/localisation/horizontal_localisation.py:prepare_domain_evaluation_requests", "bracket_core/localisation/horizontal_localisation.py:extract_relevant_functions"]}, "bracket_core/documenting.py:document_functions": {"name": "document_functions", "filepath": "bracket_core/documenting.py", "callees": ["pandas", "json", "bracket_core/documenting.py:process_api_requests_from_file", "bracket_core/documenting.py:save_simplified_csv"], "callers": []}, "bracket_core/hierarchical_domain_trace_builder.py:_process_file_for_trace": {"name": "_process_file_for_trace", "filepath": "bracket_core/hierarchical_domain_trace_builder.py", "callees": ["bracket_core/hierarchical_domain_trace_builder.py:_normalize_file_path", "bracket_core/hierarchical_domain_trace_builder.py:__init__", "os", "pandas", "bracket_core/hierarchical_domain_trace_builder.py:_count_tokens", "bracket_core/hierarchical_domain_trace_builder.py:_process_file_functions_together", "bracket_core/hierarchical_domain_trace_builder.py:_process_file_functions_individually"], "callers": ["bracket_core/hierarchical_domain_trace_builder.py:_process_leaf_node_file_by_file"]}, "bracket_core/global_codebase_explainer.py:generate_explanation": {"name": "generate_explanation", "filepath": "bracket_core/global_codebase_explainer.py", "callees": ["bracket_core/global_codebase_explainer.py:CodebaseExplanationResult", "bracket_core/global_codebase_explainer.py:prepare_taxonomy_data", "bracket_core/global_codebase_explainer.py:create_explanation_prompt", "bracket_core/global_codebase_explainer.py:__init__", "asyncio", "ChatOpenAI", "traceback"], "callers": ["bracket_core/global_codebase_explainer.py:generate_codebase_explanation"]}, "bracket_core/global_codebase_explainer.py:CodebaseExplanationResult": {"name": "CodebaseExplanationResult", "filepath": "bracket_core/global_codebase_explainer.py", "callees": [], "callers": ["bracket_core/global_codebase_explainer.py:generate_explanation"]}, "bracket_core/domain_taxonomy_mapper.py:save_taxonomy_json": {"name": "save_taxonomy_json", "filepath": "bracket_core/domain_taxonomy_mapper.py", "callees": ["os", "json"], "callers": ["bracket_core/domain_taxonomy_mapper.py:map_taxonomy"]}, "bracket_core/domain_taxonomy_mapper.py:find_combined_diagrams": {"name": "find_combined_diagrams", "filepath": "bracket_core/domain_taxonomy_mapper.py", "callees": ["bracket_core/domain_taxonomy_mapper.py:__init__", "os"], "callers": ["bracket_core/domain_taxonomy_mapper.py:map_taxonomy"]}, "bracket_core/parsing_repomap.py:_resolve_js_module_path": {"name": "_resolve_js_module_path", "filepath": "bracket_core/parsing_repomap.py", "callees": ["os", "bracket_core/parsing_repomap.py:file_path_to_module_path"], "callers": ["bracket_core/parsing_repomap.py:_extract_js_imports"]}, "run_mapper": {"name": "run_mapper", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/irl.py:map_files_to_domains"]}, "bracket_core/hierarchical_domain_file_mapper.py:_get_domain_level": {"name": "_get_domain_level", "filepath": "bracket_core/hierarchical_domain_file_mapper.py", "callees": ["bracket_core/hierarchical_domain_file_mapper.py:__init__"], "callers": ["bracket_core/hierarchical_domain_file_mapper.py:_classify_files_to_specific_domain", "bracket_core/hierarchical_domain_file_mapper.py:_process_domain_specific_request"]}, "bracket_core/enhanced_domain_trace_builder.py:build_and_classify": {"name": "build_and_classify", "filepath": "bracket_core/enhanced_domain_trace_builder.py", "callees": ["bracket_core/enhanced_domain_trace_builder.py:__init__", "bracket_core/enhanced_domain_trace_builder.py:read_domain_file_mappings", "bracket_core/enhanced_domain_trace_builder.py:map_traces_to_top_domains", "bracket_core/enhanced_domain_trace_builder.py:map_functions_to_files", "bracket_core/enhanced_domain_trace_builder.py:classify_functions_with_reduced_search_space"], "callers": ["bracket_core/enhanced_domain_trace_builder.py:build_domain_traces", "bracket_core/enhanced_domain_trace_builder.py:main"]}, "bracket_core/hierarchical_domain_trace_builder.py:_process_file_functions_together": {"name": "_process_file_functions_together", "filepath": "bracket_core/hierarchical_domain_trace_builder.py", "callees": ["Dict", "bracket_core/hierarchical_domain_trace_builder.py:_call_openai_api", "<PERSON><PERSON>", "traceback"], "callers": ["bracket_core/hierarchical_domain_trace_builder.py:_process_file_for_trace"]}, "bracket_core/hierarchical_domain_trace_builder.py:_process_file_functions_individually": {"name": "_process_file_functions_individually", "filepath": "bracket_core/hierarchical_domain_trace_builder.py", "callees": ["Dict", "bracket_core/hierarchical_domain_trace_builder.py:_process_single_function", "asyncio"], "callers": ["bracket_core/hierarchical_domain_trace_builder.py:_process_file_for_trace"]}, "bracket_core/domain_taxonomy_mapper.py:add_combined_diagrams_to_json": {"name": "add_combined_diagrams_to_json", "filepath": "bracket_core/domain_taxonomy_mapper.py", "callees": ["Dict", "bracket_core/domain_taxonomy_mapper.py:find_and_update_node"], "callers": ["bracket_core/domain_taxonomy_mapper.py:map_taxonomy"]}, "bracket_core/domain_taxonomy_mapper.py:find_and_update_node": {"name": "find_and_update_node", "filepath": "bracket_core/domain_taxonomy_mapper.py", "callees": [], "callers": ["bracket_core/domain_taxonomy_mapper.py:add_combined_diagrams_to_json"]}, "bracket_core/localisation/global_localisation.py:load_semantic_documented_functions": {"name": "load_semantic_documented_functions", "filepath": "bracket_core/localisation/global_localisation.py", "callees": ["os", "pandas"], "callers": ["bracket_core/localisation/global_localisation.py:extract_relevant_functions"]}, "bracket_core/localisation/horizontal_localisation.py:process_domain_evaluation_batch": {"name": "process_domain_evaluation_batch", "filepath": "bracket_core/localisation/horizontal_localisation.py", "callees": ["StatusTracker", "aiohttp", "asyncio", "bracket_core/localisation/horizontal_localisation.py:process_openrouter_request", "bracket_core/localisation/horizontal_localisation.py:process_openai_request"], "callers": []}, "StatusTracker": {"name": "StatusTracker", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/localisation/horizontal_localisation.py:process_domain_evaluation_batch", "bracket_core/localisation/horizontal_localisation.py:extract_relevant_functions", "bracket_core/localisation/horizontal_localisation.py:process_function_evaluation_batch"]}, "bracket_core/localisation/horizontal_localisation.py:process_openrouter_request": {"name": "process_openrouter_request", "filepath": "bracket_core/localisation/horizontal_localisation.py", "callees": ["Dict", "aiohttp", "json"], "callers": ["bracket_core/localisation/horizontal_localisation.py:process_domain_evaluation_batch"]}, "bracket_core/localisation/horizontal_localisation.py:process_openai_request": {"name": "process_openai_request", "filepath": "bracket_core/localisation/horizontal_localisation.py", "callees": ["Dict", "aiohttp", "json"], "callers": ["bracket_core/localisation/horizontal_localisation.py:process_domain_evaluation_batch"]}, "bracket_core/domain_file_mapper.py:main": {"name": "main", "filepath": "bracket_core/domain_file_mapper.py", "callees": ["<PERSON><PERSON><PERSON><PERSON>", "bracket_core/domain_file_mapper.py:__init__", "bracket_core/domain_file_mapper.py:map_files_to_domains", "traceback"], "callers": []}, "bracket_core/enhanced_domain_trace_builder.py:build_domain_traces": {"name": "build_domain_traces", "filepath": "bracket_core/enhanced_domain_trace_builder.py", "callees": ["bracket_core/enhanced_domain_trace_builder.py:__init__", "bracket_core/enhanced_domain_trace_builder.py:build_and_classify"], "callers": []}, "bracket_core/localisation/global_localisation.py:load_domain_taxonomy": {"name": "load_domain_taxonomy", "filepath": "bracket_core/localisation/global_localisation.py", "callees": ["os", "json"], "callers": ["bracket_core/localisation/global_localisation.py:second_pass_find_relevant_functions", "bracket_core/localisation/global_localisation.py:find_relevant_functions"]}, "bracket_core/hierarchical_domain_file_mapper.py:_classify_files_to_specific_domain": {"name": "_classify_files_to_specific_domain", "filepath": "bracket_core/hierarchical_domain_file_mapper.py", "callees": ["bracket_core/hierarchical_domain_file_mapper.py:_get_domain_level", "asyncio", "bracket_core/hierarchical_domain_file_mapper.py:_create_adaptive_batches", "json", "APIRequest", "time", "get_openai_api_key", "aiohttp", "bracket_core/hierarchical_domain_file_mapper.py:_process_domain_specific_request", "bracket_core/hierarchical_domain_file_mapper.py:_save_current_mappings_to_disk"], "callers": ["bracket_core/hierarchical_domain_file_mapper.py:_process_batch"]}, "bracket_core/localisation/global_localisation.py:extract_traces": {"name": "extract_traces", "filepath": "bracket_core/localisation/global_localisation.py", "callees": ["bracket_core/localisation/global_localisation.py:TraceInfo", "bracket_core/localisation/global_localisation.py:traverse_domain"], "callers": ["bracket_core/localisation/global_localisation.py:second_pass_find_relevant_functions", "bracket_core/localisation/global_localisation.py:find_relevant_functions"]}, "bracket_core/localisation/global_localisation.py:TraceInfo": {"name": "TraceInfo", "filepath": "bracket_core/localisation/global_localisation.py", "callees": [], "callers": ["bracket_core/localisation/global_localisation.py:extract_traces", "bracket_core/localisation/global_localisation.py:traverse_domain", "bracket_core/localisation/global_localisation.py:extract_relevant_functions"]}, "bracket_core/localisation/global_localisation.py:traverse_domain": {"name": "traverse_domain", "filepath": "bracket_core/localisation/global_localisation.py", "callees": ["bracket_core/localisation/global_localisation.py:TraceInfo", "bracket_core/localisation/global_localisation.py:traverse_domain"], "callers": ["bracket_core/localisation/global_localisation.py:extract_traces", "bracket_core/localisation/global_localisation.py:traverse_domain"]}, "bracket_core/irl.py:generate_domain_diagrams": {"name": "generate_domain_diagrams", "filepath": "bracket_core/irl.py", "callees": ["os", "EnhancedDomainDiagramGenerator", "traceback"], "callers": ["bracket_core/irl.py:build_domain_traces"]}, "bracket_core/irl.py:generate_domain_taxonomy": {"name": "generate_domain_taxonomy", "filepath": "bracket_core/irl.py", "callees": ["os", "bracket_core/irl.py:generate_domain_taxonomy", "traceback"], "callers": ["bracket_core/irl.py:build_domain_traces", "bracket_core/irl.py:generate_domain_taxonomy"]}, "bracket_core/irl.py:generate_codebase_explanation": {"name": "generate_codebase_explanation", "filepath": "bracket_core/irl.py", "callees": ["bracket_core/irl.py:generate_codebase_explanation", "os", "traceback"], "callers": ["bracket_core/irl.py:build_domain_traces", "bracket_core/irl.py:generate_codebase_explanation"]}, "bracket_core/hybrid_kg.py:_is_builtin_function": {"name": "_is_builtin_function", "filepath": "bracket_core/hybrid_kg.py", "callees": [], "callers": ["bracket_core/hybrid_kg.py:_should_skip_function_call"]}, "bracket_core/hybrid_kg.py:_is_library_function": {"name": "_is_library_function", "filepath": "bracket_core/hybrid_kg.py", "callees": [], "callers": ["bracket_core/hybrid_kg.py:_should_skip_function_call"]}, "bracket_core/domain_taxonomy_mapper.py:map_taxonomy": {"name": "map_taxonomy", "filepath": "bracket_core/domain_taxonomy_mapper.py", "callees": ["bracket_core/domain_taxonomy_mapper.py:DomainTaxonomyMapperResult", "bracket_core/domain_taxonomy_mapper.py:read_domain_analysis", "bracket_core/domain_taxonomy_mapper.py:read_domain_traces", "bracket_core/domain_taxonomy_mapper.py:read_domain_diagrams", "bracket_core/domain_taxonomy_mapper.py:__init__", "bracket_core/domain_taxonomy_mapper.py:find_combined_diagrams", "bracket_core/domain_taxonomy_mapper.py:build_taxonomy_tree", "bracket_core/domain_taxonomy_mapper.py:map_functions_to_taxonomy", "bracket_core/domain_taxonomy_mapper.py:map_diagrams_to_taxonomy", "bracket_core/domain_taxonomy_mapper.py:taxonomy_tree_to_json", "bracket_core/domain_taxonomy_mapper.py:add_combined_diagrams_to_json", "bracket_core/domain_taxonomy_mapper.py:count_diagrams", "bracket_core/domain_taxonomy_mapper.py:add_diagram_names", "bracket_core/domain_taxonomy_mapper.py:save_taxonomy_json", "traceback"], "callers": ["bracket_core/domain_taxonomy_mapper.py:main"]}, "bracket_core/domain_taxonomy_mapper.py:DomainTaxonomyMapperResult": {"name": "DomainTaxonomyMapperResult", "filepath": "bracket_core/domain_taxonomy_mapper.py", "callees": [], "callers": ["bracket_core/domain_taxonomy_mapper.py:map_taxonomy"]}, "bracket_core/domain_taxonomy_mapper.py:count_diagrams": {"name": "count_diagrams", "filepath": "bracket_core/domain_taxonomy_mapper.py", "callees": [], "callers": ["bracket_core/domain_taxonomy_mapper.py:map_taxonomy"]}, "bracket_core/domain_taxonomy_mapper.py:add_diagram_names": {"name": "add_diagram_names", "filepath": "bracket_core/domain_taxonomy_mapper.py", "callees": [], "callers": ["bracket_core/domain_taxonomy_mapper.py:map_taxonomy"]}, "bracket_core/global_codebase_explainer.py:generate_codebase_explanation": {"name": "generate_codebase_explanation", "filepath": "bracket_core/global_codebase_explainer.py", "callees": ["bracket_core/global_codebase_explainer.py:__init__", "bracket_core/global_codebase_explainer.py:generate_explanation"], "callers": []}, "bracket_core/enhanced_domain_trace_builder.py:main": {"name": "main", "filepath": "bracket_core/enhanced_domain_trace_builder.py", "callees": ["<PERSON><PERSON><PERSON><PERSON>", "bracket_core/enhanced_domain_trace_builder.py:__init__", "bracket_core/enhanced_domain_trace_builder.py:build_and_classify", "traceback"], "callers": []}, "bracket_core/documenting.py:document_functions_with_significance": {"name": "document_functions_with_significance", "filepath": "bracket_core/documenting.py", "callees": ["pandas", "json", "bracket_core/documenting.py:process_api_requests_from_file"], "callers": []}, "bracket_core/localisation/global_localisation.py:evaluate_trace_relevance": {"name": "evaluate_trace_relevance", "filepath": "bracket_core/localisation/global_localisation.py", "callees": ["bracket_core/localisation/global_localisation.py:GlobalLocalisationResult", "json", "bracket_core/localisation/global_localisation.py:APIRequest", "bracket_core/localisation/global_localisation.py:StatusTracker", "asyncio", "aiohttp", "time", "bracket_core/localisation/global_localisation.py:worker", "uuid", "datetime", "os"], "callers": ["bracket_core/localisation/global_localisation.py:find_relevant_functions"]}, "bracket_core/localisation/global_localisation.py:GlobalLocalisationResult": {"name": "GlobalLocalisationResult", "filepath": "bracket_core/localisation/global_localisation.py", "callees": [], "callers": ["bracket_core/localisation/global_localisation.py:evaluate_trace_relevance", "bracket_core/localisation/global_localisation.py:second_pass_find_relevant_functions", "bracket_core/localisation/global_localisation.py:find_relevant_functions"]}, "bracket_core/localisation/global_localisation.py:APIRequest": {"name": "APIRequest", "filepath": "bracket_core/localisation/global_localisation.py", "callees": [], "callers": ["bracket_core/localisation/global_localisation.py:evaluate_trace_relevance", "bracket_core/localisation/global_localisation.py:evaluate_domain_relevance", "bracket_core/localisation/global_localisation.py:extract_relevant_functions"]}, "bracket_core/localisation/global_localisation.py:StatusTracker": {"name": "StatusTracker", "filepath": "bracket_core/localisation/global_localisation.py", "callees": [], "callers": ["bracket_core/localisation/global_localisation.py:evaluate_trace_relevance", "bracket_core/localisation/global_localisation.py:evaluate_domain_relevance", "bracket_core/localisation/global_localisation.py:extract_relevant_functions"]}, "bracket_core/localisation/global_localisation.py:worker": {"name": "worker", "filepath": "bracket_core/localisation/global_localisation.py", "callees": [], "callers": ["bracket_core/localisation/global_localisation.py:evaluate_trace_relevance", "bracket_core/localisation/global_localisation.py:evaluate_domain_relevance"]}, "uuid": {"name": "uuid", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/localisation/global_localisation.py:evaluate_trace_relevance", "bracket_core/localisation/global_localisation.py:evaluate_domain_relevance", "bracket_core/localisation/global_localisation.py:extract_relevant_functions", "bracket_core/localisation/global_localisation.py:third_pass_analyze_relevant_functions"]}, "datetime": {"name": "datetime", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/localisation/global_localisation.py:evaluate_trace_relevance", "bracket_core/localisation/global_localisation.py:evaluate_domain_relevance", "bracket_core/localisation/global_localisation.py:extract_relevant_functions", "bracket_core/localisation/global_localisation.py:third_pass_analyze_relevant_functions"]}, "bracket_core/parsing_repomap.py:extract_qualified_name": {"name": "extract_qualified_name", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:_extract_java_package"], "callers": ["bracket_core/parsing_repomap.py:get_tags"]}, "bracket_core/parsing_repomap.py:_extract_java_package": {"name": "_extract_java_package", "filepath": "bracket_core/parsing_repomap.py", "callees": [], "callers": ["bracket_core/parsing_repomap.py:extract_qualified_name"]}, "bracket_core/domain_diagram_generator.py:generate_combined_diagram": {"name": "generate_combined_diagram", "filepath": "bracket_core/domain_diagram_generator.py", "callees": ["bracket_core/domain_diagram_generator.py:__init__", "Dict", "json", "bracket_core/domain_diagram_generator.py:_call_openai_api", "bracket_core/domain_diagram_generator.py:_extract_mermaid_diagram"], "callers": ["bracket_core/domain_diagram_generator.py:generate_all_diagrams"]}, "bracket_core/localisation/horizontal_localisation.py:evaluate_domains_at_level": {"name": "evaluate_domains_at_level", "filepath": "bracket_core/localisation/horizontal_localisation.py", "callees": ["bracket_core/localisation/horizontal_localisation.py:LevelEvaluationResult", "aiohttp", "asyncio", "bracket_core/localisation/horizontal_localisation.py:_process_openrouter_domain_request", "bracket_core/localisation/horizontal_localisation.py:_process_openai_domain_request", "traceback"], "callers": ["bracket_core/localisation/horizontal_localisation.py:explore_domain_hierarchy"]}, "bracket_core/localisation/horizontal_localisation.py:LevelEvaluationResult": {"name": "LevelEvaluationResult", "filepath": "bracket_core/localisation/horizontal_localisation.py", "callees": [], "callers": ["bracket_core/localisation/horizontal_localisation.py:evaluate_domains_at_level"]}, "bracket_core/localisation/horizontal_localisation.py:_process_openrouter_domain_request": {"name": "_process_openrouter_domain_request", "filepath": "bracket_core/localisation/horizontal_localisation.py", "callees": ["aiohttp", "json"], "callers": ["bracket_core/localisation/horizontal_localisation.py:evaluate_domains_at_level"]}, "bracket_core/localisation/horizontal_localisation.py:_process_openai_domain_request": {"name": "_process_openai_domain_request", "filepath": "bracket_core/localisation/horizontal_localisation.py", "callees": ["aiohttp", "json"], "callers": ["bracket_core/localisation/horizontal_localisation.py:evaluate_domains_at_level"]}, "APIRequest": {"name": "APIRequest", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/hierarchical_domain_file_mapper.py:_classify_files_to_specific_domain", "bracket_core/hierarchical_domain_file_mapper.py:_process_domain_specific_request"]}, "bracket_core/hierarchical_domain_file_mapper.py:_process_domain_specific_request": {"name": "_process_domain_specific_request", "filepath": "bracket_core/hierarchical_domain_file_mapper.py", "callees": ["bracket_core/hierarchical_domain_file_mapper.py:_get_domain_level", "get_openrouter_client", "APIRequest", "bracket_core/hierarchical_domain_file_mapper.py:__init__", "json", "re", "asyncio", "aiohttp", "time", "gc", "logging"], "callers": ["bracket_core/hierarchical_domain_file_mapper.py:_classify_files_to_specific_domain"]}, "bracket_core/domain_taxonomy_mapper.py:main": {"name": "main", "filepath": "bracket_core/domain_taxonomy_mapper.py", "callees": ["<PERSON><PERSON><PERSON><PERSON>", "bracket_core/domain_taxonomy_mapper.py:__init__", "bracket_core/domain_taxonomy_mapper.py:map_taxonomy"], "callers": []}, "EnhancedDomainDiagramGenerator": {"name": "EnhancedDomainDiagramGenerator", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/irl.py:generate_domain_diagrams"]}, "bracket_core/hierarchical_domain_trace_builder.py:_process_single_function": {"name": "_process_single_function", "filepath": "bracket_core/hierarchical_domain_trace_builder.py", "callees": ["bracket_core/hierarchical_domain_trace_builder.py:_call_openai_api", "<PERSON><PERSON>"], "callers": ["bracket_core/hierarchical_domain_trace_builder.py:_process_file_functions_individually"]}, "bracket_core/parsing_repomap.py:extract_signature": {"name": "extract_signature", "filepath": "bracket_core/parsing_repomap.py", "callees": [], "callers": ["bracket_core/parsing_repomap.py:get_tags"]}, "bracket_core/domain_diagram_generator.py:_prepare_leaf_domain_request": {"name": "_prepare_leaf_domain_request", "filepath": "bracket_core/domain_diagram_generator.py", "callees": ["bracket_core/domain_diagram_generator.py:__init__", "bracket_core/domain_diagram_generator.py:_is_empty_domain_mapping", "json"], "callers": ["bracket_core/domain_diagram_generator.py:generate_all_diagrams"]}, "bracket_core/hierarchical_domain_trace_builder.py:_process_trace_hierarchical": {"name": "_process_trace_hierarchical", "filepath": "bracket_core/hierarchical_domain_trace_builder.py", "callees": ["bracket_core/hierarchical_domain_trace_builder.py:get_domain_specific_functions_hierarchical", "bracket_core/hierarchical_domain_trace_builder.py:__init__", "bracket_core/hierarchical_domain_trace_builder.py:_call_openai_api", "<PERSON><PERSON>", "traceback"], "callers": []}, "bracket_core/domain_analysis.py:domains_from_significant_functions": {"name": "domains_from_significant_functions", "filepath": "bracket_core/domain_analysis.py", "callees": ["bracket_core/domain_analysis.py:__init__", "bracket_core/domain_analysis.py:create_hierar_domain_struct"], "callers": []}, "bracket_core/hybrid_kg.py:generate_hybrid_knowledge_graph": {"name": "generate_hybrid_knowledge_graph", "filepath": "bracket_core/hybrid_kg.py", "callees": ["bracket_core/hybrid_kg.py:__init__", "bracket_core/hybrid_kg.py:generate_graph"], "callers": []}, "bracket_core/hybrid_kg.py:graph_to_dataframes": {"name": "graph_to_dataframes", "filepath": "bracket_core/hybrid_kg.py", "callees": ["networkx", "pandas"], "callers": []}, "bracket_core/domain_analysis.py:main": {"name": "main", "filepath": "bracket_core/domain_analysis.py", "callees": ["<PERSON><PERSON><PERSON><PERSON>", "bracket_core/domain_analysis.py:__init__", "bracket_core/domain_analysis.py:create_hierar_domain_struct"], "callers": []}, "bracket_core/parsing_repomap.py:get_containing_class": {"name": "get_containing_class", "filepath": "bracket_core/parsing_repomap.py", "callees": [], "callers": ["bracket_core/parsing_repomap.py:get_tags", "bracket_core/parsing_repomap.py:_resolve_java_reference"]}, "bracket_core/documenting.py:document_functions_with_context_significance": {"name": "document_functions_with_context_significance", "filepath": "bracket_core/documenting.py", "callees": ["pandas", "json", "bracket_core/documenting.py:process_api_requests_from_file", "bracket_core/documenting.py:save_simplified_csv"], "callers": []}, "bracket_core/hybrid_kg.py:save_graph_as_parquet": {"name": "save_graph_as_parquet", "filepath": "bracket_core/hybrid_kg.py", "callees": ["graph_to_dataframes", "json", "bracket_core/hybrid_kg.py:convert_mixed_types"], "callers": []}, "bracket_core/hybrid_kg.py:convert_mixed_types": {"name": "convert_mixed_types", "filepath": "bracket_core/hybrid_kg.py", "callees": ["json"], "callers": ["bracket_core/hybrid_kg.py:save_graph_as_parquet"]}, "bracket_core/parsing_repomap.py:get_tags": {"name": "get_tags", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:get_mtime", "filename_to_lang", "get_language", "get_parser", "bracket_core/parsing_repomap.py:get_scm_fname", "bracket_core/parsing_repomap.py:read_text", "bracket_core/parsing_repomap.py:file_path_to_module_path", "bracket_core/parsing_repomap.py:extract_imports", "bracket_core/parsing_repomap.py:extract_scope_hierarchy", "bracket_core/parsing_repomap.py:get_containing_class", "bracket_core/parsing_repomap.py:extract_signature", "bracket_core/parsing_repomap.py:extract_qualified_name", "<PERSON><PERSON><PERSON>", "bracket_core/parsing_repomap.py:__init__", "guess_lexer_for_filename"], "callers": ["bracket_core/parsing_repomap.py:create_graph"]}, "filename_to_lang": {"name": "filename_to_lang", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/parsing_repomap.py:get_tags", "bracket_core/parsing_repomap.py:resolve_reference", "bracket_core/parsing_repomap.py:_find_variable_assignments", "bracket_core/parsing_repomap.py:_resolve_class_module", "bracket_core/parsing_repomap.py:_find_parent_classes", "bracket_core/parsing_repomap.py:_resolve_class_to_module", "bracket_core/parsing_repomap.py:_get_default_module_for_primitive", "bracket_core/parsing_repomap.py:enrich_with_line_numbers"]}, "bracket_core/parsing_repomap.py:get_scm_fname": {"name": "get_scm_fname", "filepath": "bracket_core/parsing_repomap.py", "callees": ["Path", "os"], "callers": ["bracket_core/parsing_repomap.py:get_tags"]}, "bracket_core/parsing_repomap.py:extract_scope_hierarchy": {"name": "extract_scope_hierarchy", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:traverse_node"], "callers": ["bracket_core/parsing_repomap.py:get_tags"]}, "guess_lexer_for_filename": {"name": "guess_lexer_for_filename", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/parsing_repomap.py:get_tags"]}, "bracket_core/irl.py:main": {"name": "main", "filepath": "bracket_core/irl.py", "callees": ["<PERSON><PERSON><PERSON><PERSON>", "time", "bracket_core/irl.py:__init__", "bracket_core/irl.py:run"], "callers": []}, "bracket_core/hierarchical_domain_trace_builder.py:normalize_function_paths": {"name": "normalize_function_paths", "filepath": "bracket_core/hierarchical_domain_trace_builder.py", "callees": ["yaml", "bracket_core/hierarchical_domain_trace_builder.py:__init__", "traceback", "bracket_core/hierarchical_domain_trace_builder.py:normalize_function_paths"], "callers": ["bracket_core/hierarchical_domain_trace_builder.py:build_and_classify", "bracket_core/hierarchical_domain_trace_builder.py:normalize_function_paths", "bracket_core/hierarchical_domain_trace_builder.py:main"]}, "bracket_core/localisation/global_localisation.py:evaluate_domain_relevance": {"name": "evaluate_domain_relevance", "filepath": "bracket_core/localisation/global_localisation.py", "callees": ["bracket_core/localisation/global_localisation.py:APIRequest", "bracket_core/localisation/global_localisation.py:StatusTracker", "asyncio", "aiohttp", "time", "bracket_core/localisation/global_localisation.py:worker", "bracket_core/localisation/global_localisation.py:DomainRelevanceResult", "uuid", "datetime", "os", "json"], "callers": ["bracket_core/localisation/global_localisation.py:second_pass_find_relevant_functions"]}, "bracket_core/localisation/global_localisation.py:DomainRelevanceResult": {"name": "DomainRelevanceResult", "filepath": "bracket_core/localisation/global_localisation.py", "callees": [], "callers": ["bracket_core/localisation/global_localisation.py:evaluate_domain_relevance"]}, "bracket_core/localisation/horizontal_localisation.py:explore_domain_hierarchy": {"name": "explore_domain_hierarchy", "filepath": "bracket_core/localisation/horizontal_localisation.py", "callees": ["bracket_core/localisation/horizontal_localisation.py:HierarchicalExplorationResult", "bracket_core/localisation/horizontal_localisation.py:get_relevant_domains_at_level", "bracket_core/localisation/horizontal_localisation.py:evaluate_domains_at_level", "List", "Dict", "traceback"], "callers": ["bracket_core/localisation/horizontal_localisation.py:find_relevant_functions"]}, "bracket_core/localisation/horizontal_localisation.py:HierarchicalExplorationResult": {"name": "HierarchicalExplorationResult", "filepath": "bracket_core/localisation/horizontal_localisation.py", "callees": [], "callers": ["bracket_core/localisation/horizontal_localisation.py:explore_domain_hierarchy"]}, "gc": {"name": "gc", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/hierarchical_domain_file_mapper.py:_process_domain_specific_request"]}, "bracket_core/domain_diagram_generator.py:_prepare_intermediate_domain_request": {"name": "_prepare_intermediate_domain_request", "filepath": "bracket_core/domain_diagram_generator.py", "callees": ["bracket_core/domain_diagram_generator.py:__init__", "json"], "callers": ["bracket_core/domain_diagram_generator.py:generate_all_diagrams"]}, "bracket_core/localisation/horizontal_localisation.py:extract_relevant_functions": {"name": "extract_relevant_functions", "filepath": "bracket_core/localisation/horizontal_localisation.py", "callees": ["bracket_core/localisation/horizontal_localisation.py:load_semantic_documented_functions", "bracket_core/localisation/horizontal_localisation.py:__init__", "estimate_token_count", "bracket_core/localisation/horizontal_localisation.py:HorizontalAPIRequest", "StatusTracker", "asyncio", "aiohttp", "time", "bracket_core/localisation/horizontal_localisation.py:process_function_evaluation_request", "bracket_core/localisation/horizontal_localisation.py:worker", "FunctionRelevanceResult"], "callers": ["bracket_core/localisation/horizontal_localisation.py:find_relevant_functions"]}, "estimate_token_count": {"name": "estimate_token_count", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/localisation/horizontal_localisation.py:extract_relevant_functions"]}, "bracket_core/localisation/horizontal_localisation.py:process_function_evaluation_request": {"name": "process_function_evaluation_request", "filepath": "bracket_core/localisation/horizontal_localisation.py", "callees": ["aiohttp", "json"], "callers": ["bracket_core/localisation/horizontal_localisation.py:extract_relevant_functions", "bracket_core/localisation/horizontal_localisation.py:process_function_evaluation_batch"]}, "bracket_core/localisation/horizontal_localisation.py:worker": {"name": "worker", "filepath": "bracket_core/localisation/horizontal_localisation.py", "callees": [], "callers": ["bracket_core/localisation/horizontal_localisation.py:extract_relevant_functions"]}, "FunctionRelevanceResult": {"name": "FunctionRelevanceResult", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/localisation/horizontal_localisation.py:extract_relevant_functions"]}, "bracket_core/hierarchical_domain_trace_builder.py:build_and_classify": {"name": "build_and_classify", "filepath": "bracket_core/hierarchical_domain_trace_builder.py", "callees": ["bracket_core/hierarchical_domain_trace_builder.py:__init__", "bracket_core/hierarchical_domain_trace_builder.py:_is_leaf_node", "bracket_core/hierarchical_domain_trace_builder.py:read_domain_file_mappings", "bracket_core/hierarchical_domain_trace_builder.py:map_traces_to_domain_paths", "bracket_core/hierarchical_domain_trace_builder.py:classify_functions_with_hierarchical_search_space", "bracket_core/hierarchical_domain_trace_builder.py:normalize_function_paths"], "callers": ["bracket_core/hierarchical_domain_trace_builder.py:build_domain_traces", "bracket_core/hierarchical_domain_trace_builder.py:main"]}, "bracket_core/parsing_repomap.py:resolve_reference": {"name": "resolve_reference", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:__init__", "filename_to_lang", "bracket_core/parsing_repomap.py:_calculate_resolution_confidence", "bracket_core/parsing_repomap.py:_resolve_generic_reference"], "callers": ["bracket_core/parsing_repomap.py:create_graph"]}, "bracket_core/parsing_repomap.py:_calculate_resolution_confidence": {"name": "_calculate_resolution_confidence", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:_has_direct_import_path", "bracket_core/parsing_repomap.py:_is_in_lexical_scope", "bracket_core/parsing_repomap.py:_type_inference_matches"], "callers": ["bracket_core/parsing_repomap.py:resolve_reference", "bracket_core/parsing_repomap.py:_resolve_generic_reference"]}, "bracket_core/parsing_repomap.py:_resolve_generic_reference": {"name": "_resolve_generic_reference", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:_is_standard_library_reference", "bracket_core/parsing_repomap.py:__init__", "bracket_core/parsing_repomap.py:_calculate_resolution_confidence", "bracket_core/parsing_repomap.py:_resolve_method_call", "bracket_core/parsing_repomap.py:_is_common_name"], "callers": ["bracket_core/parsing_repomap.py:resolve_reference"]}, "bracket_core/domain_diagram_generator.py:_send_api_request": {"name": "_send_api_request", "filepath": "bracket_core/domain_diagram_generator.py", "callees": ["asyncio", "bracket_core/domain_diagram_generator.py:_check_cache", "bracket_core/domain_diagram_generator.py:__init__", "bracket_core/domain_diagram_generator.py:_call_openai_api"], "callers": ["bracket_core/domain_diagram_generator.py:generate_all_diagrams"]}, "bracket_core/domain_diagram_generator.py:_process_api_response": {"name": "_process_api_response", "filepath": "bracket_core/domain_diagram_generator.py", "callees": ["Union", "bracket_core/domain_diagram_generator.py:_check_cache", "bracket_core/domain_diagram_generator.py:_extract_mermaid_diagram", "bracket_core/domain_diagram_generator.py:_save_to_cache", "bracket_core/domain_diagram_generator.py:__init__", "os", "traceback"], "callers": ["bracket_core/domain_diagram_generator.py:generate_all_diagrams"]}, "Union": {"name": "Union", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/domain_diagram_generator.py:_process_api_response"]}, "bracket_core/hierarchical_domain_trace_builder.py:build_domain_traces": {"name": "build_domain_traces", "filepath": "bracket_core/hierarchical_domain_trace_builder.py", "callees": ["bracket_core/hierarchical_domain_trace_builder.py:__init__", "bracket_core/hierarchical_domain_trace_builder.py:build_and_classify"], "callers": []}, "bracket_core/localisation/global_localisation.py:extract_relevant_functions": {"name": "extract_relevant_functions", "filepath": "bracket_core/localisation/global_localisation.py", "callees": ["bracket_core/localisation/global_localisation.py:load_semantic_documented_functions", "bracket_core/localisation/global_localisation.py:estimate_token_count", "bracket_core/localisation/global_localisation.py:APIRequest", "bracket_core/localisation/global_localisation.py:TraceInfo", "bracket_core/localisation/global_localisation.py:StatusTracker", "asyncio", "aiohttp", "time", "bracket_core/localisation/global_localisation.py:FunctionRelevanceResult", "uuid", "datetime", "os", "json"], "callers": ["bracket_core/localisation/global_localisation.py:second_pass_find_relevant_functions"]}, "bracket_core/localisation/global_localisation.py:FunctionRelevanceResult": {"name": "FunctionRelevanceResult", "filepath": "bracket_core/localisation/global_localisation.py", "callees": [], "callers": ["bracket_core/localisation/global_localisation.py:extract_relevant_functions"]}, "bracket_core/parsing_repomap.py:_is_standard_library_reference": {"name": "_is_standard_library_reference", "filepath": "bracket_core/parsing_repomap.py", "callees": [], "callers": ["bracket_core/parsing_repomap.py:_resolve_generic_reference"]}, "bracket_core/parsing_repomap.py:_resolve_method_call": {"name": "_resolve_method_call", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:__init__", "bracket_core/parsing_repomap.py:_find_inherited_method", "bracket_core/parsing_repomap.py:_infer_variable_type"], "callers": ["bracket_core/parsing_repomap.py:_resolve_generic_reference"]}, "bracket_core/parsing_repomap.py:_is_common_name": {"name": "_is_common_name", "filepath": "bracket_core/parsing_repomap.py", "callees": [], "callers": ["bracket_core/parsing_repomap.py:_resolve_generic_reference"]}, "bracket_core/hierarchical_domain_trace_builder.py:main": {"name": "main", "filepath": "bracket_core/hierarchical_domain_trace_builder.py", "callees": ["<PERSON><PERSON><PERSON><PERSON>", "bracket_core/hierarchical_domain_trace_builder.py:__init__", "bracket_core/hierarchical_domain_trace_builder.py:build_and_classify", "traceback", "bracket_core/hierarchical_domain_trace_builder.py:normalize_function_paths"], "callers": []}, "bracket_core/documenting.py:convert_edges_parquet_to_csv": {"name": "convert_edges_parquet_to_csv", "filepath": "bracket_core/documenting.py", "callees": ["pandas"], "callers": []}, "bracket_core/domain_diagram_generator.py:generate_all_diagrams": {"name": "generate_all_diagrams", "filepath": "bracket_core/domain_diagram_generator.py", "callees": ["bracket_core/domain_diagram_generator.py:DiagramGenerationResult", "bracket_core/domain_diagram_generator.py:read_domain_traces", "bracket_core/domain_diagram_generator.py:read_function_data", "bracket_core/domain_diagram_generator.py:build_domain_hierarchy", "bracket_core/domain_diagram_generator.py:sort_domains_by_level", "Dict", "time", "bracket_core/domain_diagram_generator.py:__init__", "bracket_core/domain_diagram_generator.py:_prepare_leaf_domain_request", "asyncio", "bracket_core/domain_diagram_generator.py:_send_api_request", "bracket_core/domain_diagram_generator.py:send_with_semaphore", "bracket_core/domain_diagram_generator.py:_process_api_response", "bracket_core/domain_diagram_generator.py:_prepare_intermediate_domain_request", "bracket_core/domain_diagram_generator.py:send_intermediate_with_semaphore", "traceback", "bracket_core/domain_diagram_generator.py:generate_combined_diagram", "os", "json"], "callers": ["bracket_core/domain_diagram_generator.py:main"]}, "bracket_core/domain_diagram_generator.py:DiagramGenerationResult": {"name": "DiagramGenerationResult", "filepath": "bracket_core/domain_diagram_generator.py", "callees": [], "callers": ["bracket_core/domain_diagram_generator.py:generate_all_diagrams"]}, "bracket_core/domain_diagram_generator.py:send_with_semaphore": {"name": "send_with_semaphore", "filepath": "bracket_core/domain_diagram_generator.py", "callees": [], "callers": ["bracket_core/domain_diagram_generator.py:generate_all_diagrams"]}, "bracket_core/domain_diagram_generator.py:send_intermediate_with_semaphore": {"name": "send_intermediate_with_semaphore", "filepath": "bracket_core/domain_diagram_generator.py", "callees": [], "callers": ["bracket_core/domain_diagram_generator.py:generate_all_diagrams"]}, "bracket_core/parsing_repomap.py:_resolve_python_reference": {"name": "_resolve_python_reference", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:__init__"], "callers": []}, "bracket_core/localisation/horizontal_localisation.py:process_function_evaluation_batch": {"name": "process_function_evaluation_batch", "filepath": "bracket_core/localisation/horizontal_localisation.py", "callees": ["StatusTracker", "aiohttp", "asyncio", "bracket_core/localisation/horizontal_localisation.py:process_function_evaluation_request"], "callers": []}, "bracket_core/parsing_repomap.py:_resolve_javascript_reference": {"name": "_resolve_javascript_reference", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:__init__"], "callers": ["bracket_core/parsing_repomap.py:_resolve_typescript_reference"]}, "bracket_core/localisation/horizontal_localisation.py:third_pass_analyze_relevant_functions": {"name": "third_pass_analyze_relevant_functions", "filepath": "bracket_core/localisation/horizontal_localisation.py", "callees": ["ThirdPassResult", "aiohttp", "json", "traceback"], "callers": ["bracket_core/localisation/horizontal_localisation.py:find_relevant_functions"]}, "ThirdPassResult": {"name": "ThirdPassResult", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/localisation/horizontal_localisation.py:third_pass_analyze_relevant_functions"]}, "bracket_core/parsing_repomap.py:_resolve_typescript_reference": {"name": "_resolve_typescript_reference", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:_resolve_javascript_reference"], "callers": []}, "bracket_core/parsing_repomap.py:_resolve_java_reference": {"name": "_resolve_java_reference", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:__init__", "bracket_core/parsing_repomap.py:get_containing_class"], "callers": []}, "bracket_core/hierarchical_domain_file_mapper.py:main": {"name": "main", "filepath": "bracket_core/hierarchical_domain_file_mapper.py", "callees": ["<PERSON><PERSON><PERSON><PERSON>", "bracket_core/hierarchical_domain_file_mapper.py:__init__", "bracket_core/hierarchical_domain_file_mapper.py:map_files_to_domains_hierarchically", "traceback"], "callers": []}, "bracket_core/localisation/global_localisation.py:third_pass_analyze_relevant_functions": {"name": "third_pass_analyze_relevant_functions", "filepath": "bracket_core/localisation/global_localisation.py", "callees": ["bracket_core/localisation/global_localisation.py:ThirdPassResult", "datetime", "bracket_core/localisation/global_localisation.py:__init__", "aiohttp", "json", "uuid", "os", "traceback"], "callers": ["bracket_core/localisation/global_localisation.py:second_pass_find_relevant_functions"]}, "bracket_core/localisation/global_localisation.py:ThirdPassResult": {"name": "ThirdPassResult", "filepath": "bracket_core/localisation/global_localisation.py", "callees": [], "callers": ["bracket_core/localisation/global_localisation.py:third_pass_analyze_relevant_functions"]}, "bracket_core/domain_diagram_generator.py:main": {"name": "main", "filepath": "bracket_core/domain_diagram_generator.py", "callees": ["<PERSON><PERSON><PERSON><PERSON>", "os", "bracket_core/domain_diagram_generator.py:__init__", "bracket_core/domain_diagram_generator.py:generate_all_diagrams", "Dict", "traceback"], "callers": []}, "bracket_core/parsing_repomap.py:create_graph": {"name": "create_graph", "filepath": "bracket_core/parsing_repomap.py", "callees": ["networkx", "os", "bracket_core/parsing_repomap.py:is_text_file", "tqdm", "bracket_core/parsing_repomap.py:read_text", "bracket_core/parsing_repomap.py:get_tags", "bracket_core/parsing_repomap.py:resolve_reference", "bracket_core/parsing_repomap.py:get_rel_fname", "bracket_core/parsing_repomap.py:_infer_node_type", "bracket_core/parsing_repomap.py:_determine_relationship_type", "bracket_core/parsing_repomap.py:_add_relationship_edge", "bracket_core/parsing_repomap.py:enrich_with_nuanced_callgraph", "bracket_core/parsing_repomap.py:enrich_with_line_numbers"], "callers": []}, "bracket_core/parsing_repomap.py:is_text_file": {"name": "is_text_file", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:open_text_file"], "callers": ["bracket_core/parsing_repomap.py:create_graph"]}, "tqdm": {"name": "tqdm", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/parsing_repomap.py:create_graph", "bracket_core/parsing_repomap.py:enrich_with_line_numbers"]}, "bracket_core/parsing_repomap.py:_infer_node_type": {"name": "_infer_node_type", "filepath": "bracket_core/parsing_repomap.py", "callees": [], "callers": ["bracket_core/parsing_repomap.py:create_graph", "bracket_core/parsing_repomap.py:enrich_with_nuanced_callgraph"]}, "bracket_core/parsing_repomap.py:_determine_relationship_type": {"name": "_determine_relationship_type", "filepath": "bracket_core/parsing_repomap.py", "callees": [], "callers": ["bracket_core/parsing_repomap.py:create_graph"]}, "bracket_core/parsing_repomap.py:enrich_with_nuanced_callgraph": {"name": "enrich_with_nuanced_callgraph", "filepath": "bracket_core/parsing_repomap.py", "callees": ["CodeGraph", "os", "json", "networkx", "bracket_core/parsing_repomap.py:_infer_node_type", "bracket_core/parsing_repomap.py:_add_relationship_edge", "traceback"], "callers": ["bracket_core/parsing_repomap.py:create_graph"]}, "bracket_core/parsing_repomap.py:enrich_with_line_numbers": {"name": "enrich_with_line_numbers", "filepath": "bracket_core/parsing_repomap.py", "callees": ["os", "defaultdict", "networkx", "tqdm", "bracket_core/parsing_repomap.py:read_text", "filename_to_lang", "get_parser", "bracket_core/parsing_repomap.py:find_class_node", "bracket_core/parsing_repomap.py:find_method_in_class", "bracket_core/parsing_repomap.py:find_inner_function", "bracket_core/parsing_repomap.py:find_matching_node", "traceback"], "callers": ["bracket_core/parsing_repomap.py:create_graph"]}, "bracket_core/localisation/horizontal_localisation.py:find_relevant_functions": {"name": "find_relevant_functions", "filepath": "bracket_core/localisation/horizontal_localisation.py", "callees": ["time", "bracket_core/localisation/horizontal_localisation.py:load_domain_taxonomy", "GlobalLocalisationResult", "bracket_core/localisation/horizontal_localisation.py:build_domain_hierarchy", "bracket_core/localisation/horizontal_localisation.py:explore_domain_hierarchy", "bracket_core/localisation/horizontal_localisation.py:extract_relevant_functions", "bracket_core/localisation/horizontal_localisation.py:third_pass_analyze_relevant_functions", "traceback", "bracket_core/localisation/horizontal_localisation.py:__init__", "bracket_core/localisation/horizontal_localisation.py:find_relevant_functions"], "callers": ["bracket_core/localisation/horizontal_localisation.py:find_relevant_functions"]}, "GlobalLocalisationResult": {"name": "GlobalLocalisationResult", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/localisation/horizontal_localisation.py:find_relevant_functions"]}, "bracket_core/localisation/global_localisation.py:second_pass_find_relevant_functions": {"name": "second_pass_find_relevant_functions", "filepath": "bracket_core/localisation/global_localisation.py", "callees": ["time", "bracket_core/localisation/global_localisation.py:load_domain_taxonomy", "bracket_core/localisation/global_localisation.py:GlobalLocalisationResult", "bracket_core/localisation/global_localisation.py:extract_traces", "bracket_core/localisation/global_localisation.py:evaluate_domain_relevance", "bracket_core/localisation/global_localisation.py:extract_relevant_functions", "bracket_core/localisation/global_localisation.py:third_pass_analyze_relevant_functions", "traceback"], "callers": ["bracket_core/localisation/global_localisation.py:find_relevant_functions"]}, "bracket_core/localisation/global_localisation.py:find_relevant_functions": {"name": "find_relevant_functions", "filepath": "bracket_core/localisation/global_localisation.py", "callees": ["bracket_core/localisation/global_localisation.py:second_pass_find_relevant_functions", "bracket_core/localisation/global_localisation.py:load_domain_taxonomy", "bracket_core/localisation/global_localisation.py:GlobalLocalisationResult", "bracket_core/localisation/global_localisation.py:extract_traces", "bracket_core/localisation/global_localisation.py:evaluate_trace_relevance", "traceback", "bracket_core/localisation/global_localisation.py:__init__", "bracket_core/localisation/global_localisation.py:find_relevant_functions"], "callers": ["bracket_core/localisation/global_localisation.py:find_relevant_functions", "bracket_core/localisation/global_localisation.py:main"]}, "bracket_core/parsing_repomap.py:open_text_file": {"name": "open_text_file", "filepath": "bracket_core/parsing_repomap.py", "callees": [], "callers": ["bracket_core/parsing_repomap.py:is_text_file"]}, "bracket_core/localisation/global_localisation.py:main": {"name": "main", "filepath": "bracket_core/localisation/global_localisation.py", "callees": ["<PERSON><PERSON><PERSON><PERSON>", "time", "bracket_core/localisation/global_localisation.py:find_relevant_functions", "json", "Optional"], "callers": []}, "Optional": {"name": "Optional", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/localisation/global_localisation.py:main"]}, "bracket_core/parsing_repomap.py:save_graph_as_parquet": {"name": "save_graph_as_parquet", "filepath": "bracket_core/parsing_repomap.py", "callees": ["networkx", "pandas", "os"], "callers": []}, "bracket_core/parsing_repomap.py:traverse_node": {"name": "traverse_node", "filepath": "bracket_core/parsing_repomap.py", "callees": [], "callers": ["bracket_core/parsing_repomap.py:extract_scope_hierarchy"]}, "bracket_core/parsing_repomap.py:_find_inherited_method": {"name": "_find_inherited_method", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:__init__", "bracket_core/parsing_repomap.py:_find_parent_classes", "bracket_core/parsing_repomap.py:_find_inherited_method"], "callers": ["bracket_core/parsing_repomap.py:_resolve_method_call", "bracket_core/parsing_repomap.py:_find_inherited_method"]}, "bracket_core/parsing_repomap.py:_infer_variable_type": {"name": "_infer_variable_type", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:_find_variable_assignments", "bracket_core/parsing_repomap.py:_resolve_class_module", "bracket_core/parsing_repomap.py:__init__", "bracket_core/parsing_repomap.py:_extract_param_type_simple"], "callers": ["bracket_core/parsing_repomap.py:_resolve_method_call"]}, "bracket_core/parsing_repomap.py:_find_variable_assignments": {"name": "_find_variable_assignments", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:module_path_to_file_path", "bracket_core/parsing_repomap.py:read_text", "filename_to_lang", "get_parser", "bracket_core/parsing_repomap.py:_resolve_class_module", "bracket_core/parsing_repomap.py:visit_node"], "callers": ["bracket_core/parsing_repomap.py:_infer_variable_type"]}, "bracket_core/parsing_repomap.py:_resolve_class_module": {"name": "_resolve_class_module", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:__init__", "bracket_core/parsing_repomap.py:module_path_to_file_path", "bracket_core/parsing_repomap.py:extract_imports", "bracket_core/parsing_repomap.py:read_text", "filename_to_lang"], "callers": ["bracket_core/parsing_repomap.py:_infer_variable_type", "bracket_core/parsing_repomap.py:_find_variable_assignments"]}, "bracket_core/parsing_repomap.py:_extract_param_type_simple": {"name": "_extract_param_type_simple", "filepath": "bracket_core/parsing_repomap.py", "callees": [], "callers": ["bracket_core/parsing_repomap.py:_infer_variable_type"]}, "bracket_core/parsing_repomap.py:visit_node": {"name": "visit_node", "filepath": "bracket_core/parsing_repomap.py", "callees": [], "callers": ["bracket_core/parsing_repomap.py:_find_variable_assignments"]}, "bracket_core/parsing_repomap.py:_analyze_method_return_type": {"name": "_analyze_method_return_type", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:__init__", "bracket_core/parsing_repomap.py:_resolve_type_reference", "bracket_core/parsing_repomap.py:module_path_to_file_path", "bracket_core/parsing_repomap.py:read_text"], "callers": []}, "bracket_core/parsing_repomap.py:_resolve_type_reference": {"name": "_resolve_type_reference", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:_get_default_module_for_primitive", "bracket_core/parsing_repomap.py:_resolve_type_reference", "bracket_core/parsing_repomap.py:__init__"], "callers": ["bracket_core/parsing_repomap.py:_analyze_method_return_type", "bracket_core/parsing_repomap.py:_resolve_type_reference"]}, "bracket_core/parsing_repomap.py:_find_parent_classes": {"name": "_find_parent_classes", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:__init__", "bracket_core/parsing_repomap.py:module_path_to_file_path", "bracket_core/parsing_repomap.py:read_text", "filename_to_lang", "get_parser", "get_language", "bracket_core/parsing_repomap.py:_resolve_class_to_module"], "callers": ["bracket_core/parsing_repomap.py:_find_inherited_method", "bracket_core/parsing_repomap.py:_type_inference_matches"]}, "bracket_core/parsing_repomap.py:_has_direct_import_path": {"name": "_has_direct_import_path", "filepath": "bracket_core/parsing_repomap.py", "callees": [], "callers": ["bracket_core/parsing_repomap.py:_calculate_resolution_confidence"]}, "bracket_core/parsing_repomap.py:_is_in_lexical_scope": {"name": "_is_in_lexical_scope", "filepath": "bracket_core/parsing_repomap.py", "callees": [], "callers": ["bracket_core/parsing_repomap.py:_calculate_resolution_confidence"]}, "bracket_core/parsing_repomap.py:_type_inference_matches": {"name": "_type_inference_matches", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:_find_parent_classes", "bracket_core/parsing_repomap.py:__init__"], "callers": ["bracket_core/parsing_repomap.py:_calculate_resolution_confidence"]}, "bracket_core/parsing_repomap.py:_extract_param_type": {"name": "_extract_param_type", "filepath": "bracket_core/parsing_repomap.py", "callees": [], "callers": []}, "bracket_core/parsing_repomap.py:_types_compatible": {"name": "_types_compatible", "filepath": "bracket_core/parsing_repomap.py", "callees": [], "callers": []}, "bracket_core/parsing_repomap.py:_resolve_class_to_module": {"name": "_resolve_class_to_module", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:__init__", "bracket_core/parsing_repomap.py:module_path_to_file_path", "bracket_core/parsing_repomap.py:extract_imports", "bracket_core/parsing_repomap.py:read_text", "filename_to_lang"], "callers": ["bracket_core/parsing_repomap.py:_find_parent_classes"]}, "bracket_core/parsing_repomap.py:_get_default_module_for_primitive": {"name": "_get_default_module_for_primitive", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:module_path_to_file_path", "filename_to_lang"], "callers": ["bracket_core/parsing_repomap.py:_resolve_type_reference"]}, "bracket_core/parsing_repomap.py:_normalize_filepath": {"name": "_normalize_filepath", "filepath": "bracket_core/parsing_repomap.py", "callees": ["os"], "callers": []}, "bracket_core/parsing_repomap.py:_extract_namespace_parts": {"name": "_extract_namespace_parts", "filepath": "bracket_core/parsing_repomap.py", "callees": [], "callers": []}, "bracket_core/parsing_repomap.py:find_class_node": {"name": "find_class_node", "filepath": "bracket_core/parsing_repomap.py", "callees": [], "callers": ["bracket_core/parsing_repomap.py:enrich_with_line_numbers"]}, "bracket_core/parsing_repomap.py:find_method_in_class": {"name": "find_method_in_class", "filepath": "bracket_core/parsing_repomap.py", "callees": [], "callers": ["bracket_core/parsing_repomap.py:enrich_with_line_numbers"]}, "bracket_core/parsing_repomap.py:find_inner_function": {"name": "find_inner_function", "filepath": "bracket_core/parsing_repomap.py", "callees": [], "callers": ["bracket_core/parsing_repomap.py:enrich_with_line_numbers"]}, "bracket_core/parsing_repomap.py:find_matching_node": {"name": "find_matching_node", "filepath": "bracket_core/parsing_repomap.py", "callees": [], "callers": ["bracket_core/parsing_repomap.py:enrich_with_line_numbers"]}, "bracket_core/parsing_repomap.py:to_dataframes": {"name": "to_dataframes", "filepath": "bracket_core/parsing_repomap.py", "callees": ["networkx", "pandas"], "callers": []}, "CodeGraph": {"name": "CodeGraph", "filepath": "Unknown", "callees": [], "callers": ["bracket_core/parsing_repomap.py:enrich_with_nuanced_callgraph"]}, "bracket_core/parsing_repomap.py:is_test_file": {"name": "is_test_file", "filepath": "bracket_core/parsing_repomap.py", "callees": ["bracket_core/parsing_repomap.py:__init__", "os"], "callers": []}, "bracket_core/parsing_repomap.py:_construct_node_id": {"name": "_construct_node_id", "filepath": "bracket_core/parsing_repomap.py", "callees": [], "callers": []}, "bracket_core/parsing_repomap.py:tool_error": {"name": "tool_error", "filepath": "bracket_core/parsing_repomap.py", "callees": ["logging"], "callers": []}, "bracket_core/parsing_repomap.py:tool_output": {"name": "tool_output", "filepath": "bracket_core/parsing_repomap.py", "callees": ["logging"], "callers": []}}