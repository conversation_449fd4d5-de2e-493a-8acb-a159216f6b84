--- bracket_core/bracket_irl/microservices/orchestrator-service/src/services/orchestrator.py.orig
+++ bracket_core/bracket_irl/microservices/orchestrator-service/src/services/orchestrator.py
@@ -300,7 +300,7 @@
 
         # Create domain analysis request
         domain_analysis_request = request.domain_analysis_config or DomainAnalysisRequest(
-            repomap_path=repomap_path,
+            repomap_path=f"artifacts/{job_id}/repomap.json",  # Use relative path instead of absolute
             **default_config
         )
 
@@ -365,8 +365,8 @@
 
         # Create file domain mapper request
         file_domain_mapper_request = request.file_domain_mapper_config or FileDomainMapperRequest(
-            repomap_path=repomap_path,
-            domain_analysis_path=domain_analysis_path,
+            repomap_path=f"artifacts/{job_id}/repomap.json",  # Use relative path instead of absolute
+            domain_analysis_path=f"artifacts/{job_id}/domain_analysis.json",  # Use relative path instead of absolute
             **default_config
         )
 
@@ -430,8 +430,8 @@
 
         # Create domain file repomap request
         domain_file_repomap_request = request.domain_file_repomap_config or DomainFileRepomapRequest(
-            domain_mapping_path=domain_mapping_path,
-            repomap_path=repomap_path,
+            domain_mapping_path=f"artifacts/{job_id}/domain_mapping.json",  # Use relative path instead of absolute
+            repomap_path=f"artifacts/{job_id}/repomap.json",  # Use relative path instead of absolute
             **default_config
         )
 
@@ -495,7 +495,7 @@
 
         # Create diagram generator request
         diagram_generator_request = request.diagram_generator_config or DiagramGeneratorRequest(
-            domain_file_repomap_path=domain_file_repomap_path,
+            domain_file_repomap_path=f"artifacts/{job_id}/domain_file_repomap.json",  # Use relative path instead of absolute
             **default_config
         )
