#!/usr/bin/env python
"""
Run Leaf Node Token Analysis

This script runs the leaf node token calculator on a specified codebase.
"""

import asyncio
import os
import argparse
from bracket_core.leaf_node_token_calculator import LeafNodeTokenCalculator

async def main():
    parser = argparse.ArgumentParser(description="Run leaf node token analysis")
    parser.add_argument("--domain-traces", help="Path to the domain traces YAML file")
    parser.add_argument("--functions-parquet", help="Path to the semantic_documented_functions.parquet file")
    parser.add_argument("--output-dir", help="Directory to save analysis results")
    parser.add_argument("--model", default="gpt-4o-mini", help="Model name to use for token counting")
    parser.add_argument("--token-limit", type=int, default=100000, help="Token limit to flag domains exceeding it")
    
    args = parser.parse_args()
    
    # Default paths if not provided
    domain_traces = args.domain_traces or "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/domain_traces.yaml"
    functions_parquet = args.functions_parquet or "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/semantic_documented_functions.parquet"
    output_dir = args.output_dir or "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/token_analysis"
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"Running leaf node token analysis with:")
    print(f"  Domain traces: {domain_traces}")
    print(f"  Functions parquet: {functions_parquet}")
    print(f"  Output directory: {output_dir}")
    print(f"  Model: {args.model}")
    print(f"  Token limit: {args.token_limit}")
    
    # Create a leaf node token calculator
    calculator = LeafNodeTokenCalculator(
        domain_traces_yaml_path=domain_traces,
        functions_parquet_path=functions_parquet,
        output_dir=output_dir,
        model_name=args.model,
        token_limit=args.token_limit
    )
    
    # Analyze leaf domains
    result = await calculator.analyze_leaf_domains()
    
    # Save analysis results
    summary_path = calculator.save_analysis_results(result)
    
    print(f"\nAnalysis complete!")
    print(f"Summary saved to: {summary_path}")
    
    # Print key statistics
    print(f"\nKey Statistics:")
    print(f"  Total leaf domains: {result.total_leaf_domains}")
    print(f"  Total tokens: {result.total_tokens:,}")
    print(f"  Average tokens per domain: {result.average_tokens_per_domain:.2f}")
    print(f"  Maximum tokens: {result.max_tokens:,} (Domain: {result.max_tokens_domain})")
    print(f"  Domains exceeding token limit ({args.token_limit:,}): {len(result.domains_exceeding_limit)}")
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
