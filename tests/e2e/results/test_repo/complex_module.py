"""
A complex module with multiple classes and functions to test the standardized artifact storage.
"""

import os
import json
from typing import Dict, List, Any, Optional


class DataProcessor:
    """A class for processing data."""
    
    def __init__(self, data_dir: str):
        """Initialize the data processor."""
        self.data_dir = data_dir
        self.processed_data = {}
    
    def load_data(self, filename: str) -> Dict[str, Any]:
        """Load data from a file."""
        filepath = os.path.join(self.data_dir, filename)
        with open(filepath, 'r') as f:
            return json.load(f)
    
    def save_data(self, data: Dict[str, Any], filename: str) -> None:
        """Save data to a file."""
        filepath = os.path.join(self.data_dir, filename)
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)
    
    def process_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Process the data."""
        result = {}
        for key, value in data.items():
            if isinstance(value, dict):
                result[key] = self.process_nested_dict(value)
            elif isinstance(value, list):
                result[key] = self.process_list(value)
            else:
                result[key] = self.process_value(value)
        return result
    
    def process_nested_dict(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Process a nested dictionary."""
        return {k: self.process_value(v) for k, v in data.items()}
    
    def process_list(self, data: List[Any]) -> List[Any]:
        """Process a list of values."""
        return [self.process_value(item) for item in data]
    
    def process_value(self, value: Any) -> Any:
        """Process a single value."""
        if isinstance(value, str):
            return value.upper()
        elif isinstance(value, int):
            return value * 2
        elif isinstance(value, float):
            return round(value, 2)
        else:
            return value


class ArtifactManager:
    """A class for managing artifacts."""
    
    def __init__(self, storage_dir: str):
        """Initialize the artifact manager."""
        self.storage_dir = storage_dir
        self.artifacts = {}
    
    def store_artifact(self, job_id: str, artifact_type: str, content: Any, format: str = "json") -> str:
        """Store an artifact with standardized naming."""
        path = f"artifacts/{job_id}/{artifact_type}.{format}"
        full_path = os.path.join(self.storage_dir, path)
        
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(full_path), exist_ok=True)
        
        # Store content based on format
        if format == "json":
            with open(full_path, 'w') as f:
                json.dump(content, f, indent=2)
        else:
            with open(full_path, 'w') as f:
                f.write(str(content))
        
        # Store artifact metadata
        self.artifacts[f"{job_id}_{artifact_type}"] = {
            "path": path,
            "format": format,
            "created_at": os.path.getmtime(full_path)
        }
        
        return path
    
    def get_artifact_path(self, job_id: str, artifact_type: str, format: str = "json") -> str:
        """Get standardized path for an artifact."""
        return f"artifacts/{job_id}/{artifact_type}.{format}"
    
    def get_artifact(self, job_id: str, artifact_type: str) -> Optional[Dict[str, Any]]:
        """Get an artifact by job ID and type."""
        artifact_key = f"{job_id}_{artifact_type}"
        if artifact_key not in self.artifacts:
            return None
        
        artifact_info = self.artifacts[artifact_key]
        full_path = os.path.join(self.storage_dir, artifact_info["path"])
        
        if not os.path.exists(full_path):
            return None
        
        if artifact_info["format"] == "json":
            with open(full_path, 'r') as f:
                return json.load(f)
        else:
            with open(full_path, 'r') as f:
                return {"content": f.read()}
