# List available regions
gcloud compute regions list

# List zones in a specific region (e.g., us-central1)
gcloud compute zones list --filter="region:asia-south1"



gcloud container clusters create bracket-irl-cluster \
    --zone asia-south1-a \
    --num-nodes 3 \
    --machine-type e2-standard-4 \
    --enable-autoscaling \
    --min-nodes 3 \
    --max-nodes 6 \
    --release-channel regular \
    --enable-ip-alias


gcloud container clusters create bracket-irl-cluster \
    --zone asia-south1-a \
    --num-nodes 1 \
    --machine-type e2-standard-8 \
    --enable-autoscaling \
    --min-nodes 0 \
    --max-nodes 5 \
    --enable-autoprovisioning \
    --min-cpu 0 \
    --max-cpu 40 \
    --min-memory 0 \
    --max-memory 160 \
    --release-channel regular \
    --enable-ip-alias \
    --enable-vertical-pod-autoscaling


Schedule Downtime: For pure demo purposes, consider shutting down the cluster when not in use:

# Stop the cluster
gcloud container clusters update bracket-irl-cluster --zone asia-south1-a --no-enable-autoscaling
gcloud container clusters resize bracket-irl-cluster --zone asia-south1-a --num-nodes=0

# Start it again when needed
gcloud container clusters resize bracket-irl-cluster --zone asia-south1-a --num-nodes=1
gcloud container clusters update bracket-irl-cluster --zone asia-south1-a --enable-autoscaling --min-nodes=0 --max-nodes=5



# Get your GCP project ID
PROJECT_ID=$(gcloud config get-value project)
echo "Your GCP project ID is: $PROJECT_ID"

# Tag the common library
docker tag bracket-irl/bracket_irl_common:latest asia-south1-docker.pkg.dev/$PROJECT_ID/bracket-irl/bracket_irl_common:latest

# Tag the services
docker tag bracket-irl/repo-mapper-service:latest asia-south1-docker.pkg.dev/$PROJECT_ID/bracket-irl/repo-mapper-service:latest
docker tag bracket-irl/domain-analyzer-service:latest asia-south1-docker.pkg.dev/$PROJECT_ID/bracket-irl/domain-analyzer-service:latest
docker tag bracket-irl/file-domain-mapper-service:latest asia-south1-docker.pkg.dev/$PROJECT_ID/bracket-irl/file-domain-mapper-service:latest
docker tag bracket-irl/domain-file-repomap-service:latest asia-south1-docker.pkg.dev/$PROJECT_ID/bracket-irl/domain-file-repomap-service:latest
docker tag bracket-irl/diagram-generator-service:latest asia-south1-docker.pkg.dev/$PROJECT_ID/bracket-irl/diagram-generator-service:latest
docker tag bracket-irl/orchestrator-service:latest asia-south1-docker.pkg.dev/$PROJECT_ID/bracket-irl/orchestrator-service:latest


# Create a secrets file with your API keys
cat > secrets.env << EOF
OPENAI_API_KEY=********************************************************************************************************************************************************************
ANTHROPIC_API_KEY=************************************************************************************************************
OPENROUTER_API_KEY=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
EOF




# Navigate to your GKE overlay directory
cd ~/gcp-bracket-deployment/k8s-manifests/overlays/gke

# Update the ingress.yaml file
# Replace the example domain with your actual domain or use the IP directly
# Also add the static IP annotation
cat > ingress.yaml << EOF
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: bracket-irl-ingress
  namespace: bracket-irl
  annotations:
    kubernetes.io/ingress.class: "gce"
    kubernetes.io/ingress.global-static-ip-name: "bracket-irl-ip"
spec:
  rules:
  - http:
      paths:
      - path: /orchestrator
        pathType: Prefix
        backend:
          service:
            name: orchestrator-service
            port:
              number: 8000
      - path: /repo-mapper
        pathType: Prefix
        backend:
          service:
            name: repo-mapper-service
            port:
              number: 8001
      - path: /domain-analyzer
        pathType: Prefix
        backend:
          service:
            name: domain-analyzer-service
            port:
              number: 8002
      - path: /file-domain-mapper
        pathType: Prefix
        backend:
          service:
            name: file-domain-mapper-service
            port:
              number: 8003
      - path: /domain-file-repomap
        pathType: Prefix
        backend:
          service:
            name: domain-file-repomap-service
            port:
              number: 8004
      - path: /diagram-generator
        pathType: Prefix
        backend:
          service:
            name: diagram-generator-service
            port:
              number: 8005
EOF