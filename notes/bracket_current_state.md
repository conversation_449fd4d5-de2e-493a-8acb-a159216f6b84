# Bracket: Current State and Capabilities

## Overview

Bracket is a comprehensive AI-powered code understanding and development assistance platform consisting of two major components: **bracket_core** (with bracket_irl for indexing and microservices) and **bracket_ext** (VSCode extension product layer). The system is designed to help developers understand and work with large, complex codebases by creating a structured representation of the code and providing AI-assisted development tools.

## Bracket Core (bracket_core)

Bracket Core is the Python-based backend that implements the Intermediate Representation Layer (IRL) pipeline, which transforms raw code into a structured, hierarchical representation.

### IRL Pipeline

The IRL pipeline is the heart of Bracket's code understanding capabilities. It processes codebases through a series of stages:

1. **Repository Mapping (CompleteRepoMap)**: 
   - Creates a comprehensive structural representation of the codebase
   - Extracts functions, classes, and other code elements
   - Identifies relationships between code elements
   - Outputs a detailed repository map

2. **Knowledge Graph Generation (HybridKnowledgeGraph)**:
   - Builds a graph representation of components and their relationships
   - Extracts function signatures and their called functions
   - Creates a lightweight knowledge graph focused on code structure
   - Provides a foundation for domain analysis

3. **Semantic Documentation**:
   - Adds semantic meaning to components when required
   - Enhances code understanding with contextual information
   - Provides additional metadata for domain analysis

4. **Domain Analysis (DomainAnalysisIntegration)**:
   - Organizes code into logical domains using LLM-based analysis
   - Identifies hierarchical relationships between domains
   - Creates a domain hierarchy with areas, domains, and components
   - Outputs a domain analysis YAML file

5. **File-Domain Mapping (FileDomainMapper)**:
   - Maps files to identified domains
   - Uses batch processing for efficiency with large codebases
   - Leverages LLMs to understand file purposes and functionalities
   - Outputs file-to-domain mappings

6. **Domain-File Repomap (DomainFileRepomap)**:
   - Creates a unified representation combining domains and files
   - Establishes relationships between domains and their implementation files
   - Provides a comprehensive view of the codebase structure
   - Serves as input for diagram and taxonomy generation

7. **Diagram Generation (JSONDomainDiagramGenerator)**:
   - Creates visual Mermaid diagrams for domains and their relationships
   - Generates diagrams at different levels of abstraction
   - Provides visual representations of the codebase structure
   - Outputs diagram files for use in the Mermaid Companion

8. **Taxonomy Generation**:
   - Combines all outputs into a unified JSON structure
   - Creates a hierarchical representation of domains, components, and files
   - Includes references to Mermaid diagrams
   - Outputs the domain_taxonomy.json file used by bracket_ext

9. **Codebase Explanation**:
   - Generates a comprehensive explanation of the codebase
   - Describes the overall architecture and key components
   - Provides insights into design patterns and code organization
   - Creates documentation for developers to understand the codebase

### Microservices Architecture

The IRL pipeline has been decomposed into specialized microservices for scalability and cloud deployment:

1. **Orchestrator Service**:
   - Coordinates the entire pipeline workflow
   - Manages job execution and status tracking
   - Provides API endpoints for pipeline operations
   - Handles communication between services

2. **Repository Mapper Service**:
   - Extracts code structure from repositories
   - Processes large codebases efficiently
   - Supports multiple programming languages
   - Outputs repository maps for further processing

3. **Domain Analyzer Service**:
   - Analyzes code to identify domains
   - Integrates with LLMs for domain discovery
   - Creates hierarchical domain structures
   - Outputs domain analysis results

4. **File-Domain Mapper Service**:
   - Maps files to identified domains
   - Processes files in batches for efficiency
   - Uses LLMs to understand file purposes
   - Outputs file-domain mappings

5. **Domain-File Repomap Service**:
   - Generates domain-file relationship maps
   - Combines domain analysis and file mappings
   - Creates a unified representation
   - Outputs combined artifacts

6. **Diagram Generator Service**:
   - Creates visual diagrams of the codebase
   - Generates Mermaid diagrams for domains
   - Provides visual representations at different levels
   - Outputs diagram files

These microservices are supported by a common infrastructure layer (bracket-irl-common) that provides shared utilities and standardized interfaces.

### Delta Codebase Ingestion

The system includes capabilities for processing incremental changes to the codebase:

- **Diff Detection**: Identifies changes between versions
- **Change Analysis**: Analyzes the nature and scope of changes
- **Impact Assessment**: Determines which domains and artifacts are affected
- **Selective Update**: Updates only the affected parts of the IRL representation

## Bracket Extension (bracket_ext)

Bracket Extension is the TypeScript-based VSCode extension that serves as the product layer, providing a rich user interface for interacting with the AI assistant and visualizing the codebase structure.

### Main Components

1. **Extension Entry Point (extension.ts)**:
   - Initializes the extension
   - Registers commands, code actions, and webview providers
   - Sets up event listeners
   - Exports the API for other extensions

2. **UI Components**:
   - **Webview UI (React)**: Provides the chat interface and settings panels
   - **Mermaid Companion View**: Displays Mermaid diagrams for code visualization
   - **Global Codebase Panel**: Provides a high-level overview of the codebase
   - **Settings Interface**: Allows configuration of the extension

3. **Core Components**:
   - **Bracket System**: Manages chat interactions and AI responses
   - **Prompt System**: Manages prompts for the AI assistant
   - **Mode Validator**: Validates and manages modes (Chat/Agent)
   - **Sliding Window**: Manages context window for the AI

4. **API System**:
   - **API Handler**: Manages communication with LLM providers
   - **LLM Providers Integration**: Supports multiple LLM providers
   - **Response Processing**: Processes AI responses for display

5. **Context Engine**:
   - **ContextEngineProcessor**: Processes queries to find relevant code
   - **ContextEngineService**: Manages the context engine state
   - **Global Localization**: Identifies relevant code based on queries
   - **Function Content Extraction**: Extracts code from relevant functions

6. **Mermaid Companion**:
   - **MermaidCompanionProvider**: Manages the Mermaid Companion webview
   - **MermaidCompanionService**: Handles loading and processing of diagrams
   - **Mermaid Renderer**: Renders Mermaid diagrams in the webview
   - **Navigation Controls**: Provides navigation through the domain hierarchy

### Key Features

1. **AI Assistant**:
   - Chat interface for code-related questions and tasks
   - Context-aware responses based on the codebase
   - Code generation and explanation capabilities
   - Integration with LLM providers

2. **Mermaid Companion**:
   - Displays Mermaid diagrams as users navigate through code
   - Provides visual representation of code domains and relationships
   - Supports navigation through the domain hierarchy
   - Highlights relevant parts of diagrams based on current file

3. **Context Engine**:
   - Analyzes queries to find relevant code in the codebase
   - Provides context about the codebase to the AI assistant
   - Displays relevant functions and files with relevance scores
   - Enhances AI responses with codebase-specific knowledge

4. **Global Codebase Panel**:
   - Provides a high-level overview of the codebase
   - Shows domain hierarchy and relationships
   - Allows navigation to specific domains and components
   - Integrates with the chat interface for domain-specific questions

### Integration with Bracket Core

The Bracket Extension integrates with Bracket Core through several mechanisms:

1. **Domain Taxonomy**: Loads the domain_taxonomy.json file generated by Bracket Core's IRL system, which contains the hierarchical representation of domains, components, and files.

2. **Mermaid Diagrams**: Displays Mermaid diagrams generated by Bracket Core's IRL system, providing visual representations of domains and their relationships.

3. **Codebase Explanation**: Uses the codebase explanation generated by Bracket Core's IRL system to provide context about the overall architecture and design.

4. **Context Engine**: Uses the domain taxonomy and other artifacts to provide context about the codebase to the AI assistant, enhancing its responses with codebase-specific knowledge.

## Cloud Infrastructure

The system is deployed on Google Kubernetes Engine (GKE) with a focus on scalability, high availability, and security.

### Key Components

1. **GKE Cluster (bracket-irl-cluster)**:
   - Managed Kubernetes environment for microservices
   - Multiple node pools for different workload types
   - Horizontal Pod Autoscaler for automatic scaling
   - Regional deployment for high availability

2. **Kubernetes Namespace (bracket-irl)**:
   - Contains all Bracket microservices
   - Includes monitoring components (Prometheus, Grafana)
   - Manages configuration and secrets
   - Provides service discovery and load balancing

3. **Storage**:
   - Cloud Storage for artifacts and diagrams
   - Cloud SQL for structured data
   - Redis for caching and temporary storage
   - Persistent volumes for stateful services

4. **Security**:
   - IAM for identity and access management
   - Secret Manager for sensitive information
   - KMS for encryption
   - SSL/TLS with Managed Certificates
