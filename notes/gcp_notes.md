## Static IP
Bracket IRL GKE Static IP:
*************




Step 6: Deploy to GKE


ORG_ID=************
gcloud organizations add-iam-policy-binding ************ \
  --member="user:<EMAIL>" \
  --role="roles/resourcemanager.organizationAdmin"




gcloud resource-manager org-policies disable-enforce \
  constraints/iam.disableServiceAccountKeyCreation \
  --organization=************

# (re-fetch your Org ID if needed)
ORG_ID=$(gcloud organizations list \
  --filter="DISPLAY_NAME=bracket-irl" \
  --format="value(ID)")

# Disable enforcement of the "disable key creation" policy
gcloud resource-manager org-policies disable-enforce \
  constraints/iam.disableServiceAccountKeyCreation \
  --organization=************