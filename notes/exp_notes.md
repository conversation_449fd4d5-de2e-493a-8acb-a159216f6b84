We are going to run the experiment to see whether embeddings play can produce the domain analysis and further can it map the files and functions to the correct relevant domains or not.

Step by step:

1. Create a new flow of IRL where we take step 1, 2, 3? till semantic documented functions parquet is being created.

2. Setup a concurrent and parallel embedding pipeline that embeds the documentation, function code and path.

3. Perform clustering to identify the leaf nodes.





Experiment 2:

In this experiment, 

We build this sort of repomap(we can ofcourse make it better later) and then feed it to a strong LLM with say 1M Context Window in batches to give us a hierarchical domains, meaning a set of leaf level domains at the depth of 5 for a 10M LOC and then hierarchically combining them logically. 
We can potentially also add callee functions in this repomap, by this I mean that each function can have the name or signature of all the functions it calls and maybe optionally an import related to it.
Secondly, we can also apply some heuritstics to take significant functions(say top 50%)
Then after this step we can use some embedding approach to then classify each function to one of the leaf level domains. 


Your approach consists of three main components:

Repomap Generation: Create a concise structural representation of the codebase showing:

    Classes and functions with their signatures
    Call relationships between functions
    Import dependencies
    Focus on significant functions (top 50% by some heuristic)


LLM-Based Domain Discovery: Feed this repomap to a large context window LLM to:

    Generate hierarchical domain structure (5 levels deep for 10M LOC)
    Identify logical groupings based on structure and signatures
    Define leaf-level domains


Embedding-Based Classification: After domains are established:

    Generate embeddings for all functions
    Classify each function into one of the leaf-level domains


-> Each function should have list of functions that it calls in the repomap structure
-> Figure out a smart way to get significant functions in there. Maybe through embeddings or heuristics.
-> Implement Gemini support using OpenRouter and ability to split the repomap in 500K tokens to get the output of that part 



Next step is to match the functions to the domains



Currently analyzing if I pass a filtered version of Repo map (taking top-f something functions), then what will be the token and cost dynamics for this approach?
Let's try the biggest one, i.e. Gitlab

Django is a 100K LOC, 750 py files and the repomap is 84K tokens - feasible | Highly with caching in place
Django has 10 top level domains -> Meaning 840K tokens input (cached) at o3 is cheap af for this task

While we wait for Gitlab full codebase, let's try running the django and other codebases with this approach.
In the meantime we are parallelizing the processing of the complete_repomap.py
Augment came back by changing it


Send in the full thing abhi, and then compare with top-f per file
Making changes to process the full repomap

Took $1.62 for django with the full function list, i.e. 80K tokens with o3-mini

Even with smaller model 4o-mini I am hitting a 70% accuracy - quite good.

The thing is that it won't scale linearly with as the codebase size increases because we are going to have limited top level domains. But yes the bigger the model, the better it is.

Two ways to move forward to ensure that this is our approach:

1. Test the parallelized repomap creation -> And figure out the token and cost dynamics on larger codebases(3M LOC on Gitlab)

2. In the complete repomap file, include a heuristic metric to output another artifact that takes the top-f functions from each file based on certain heuristics.


Lets start with 1 quickly.
1 took a lot of time but still did not work out, I am undoing the changes made and will attempt it after completing 2.

Lets start with 2 and get this done in 15 mins - impl, tested and done.

The outputs are here for django, at the same time I am seeing wayyy too many:
With the top 30% I am getting 25K tokens

No references found, falling back to pygments



Hypothesis -
I believe quality will increase by a lot on embedding or simply llm based appoarch if we also have the functions being called - not just declared but also this declared function calls this, this and this function.




## Mermaid generation with new exp artifacts
The aim remains the same: We want to generate mermaid charts for each leaf level domains.
The input has changed: Now instead of reading the entire file or entire function, we are now using a different artifact that is a json structure which has each leaf domain as the key. 
For each of the leaf domain key, we have a file paths as keys. For each file path, we have the code of that file as the value.
Note: There will be one leaf level domain which will be like `"Infrastructure & Utilities/Experimental Features/Unclassified"`, meaning it will have `"Unclassified"` in it, this domain and all the files inside it are to be skipped. For our mermaid generation task, we don't consider it all.

Plan: 
We will proceed as currently we are:
We will process at a domain level -> concurrently and parallely as the leaf domains are non-blocking calls.

For each of this leaf level domain:
We will proceed sending the data to an LLM and then simply get the results back





---------
The pipieline is to be built now to bring together the new approach that we have built recently. Esssentially we have built a new version of IRL that is much more better using these files: 

Please look at irl

-> The idea is to build a similiar pipeline step by step using:







docker compose build --no-cache 


docker exec -it microservices-repo-mapper-service-1 rm -rf /app/data/artifacts/
docker exec -it microservices-orchestrator-service-1 rm -rf /app/data/artifacts/
docker exec -it microservices-domain-file-repomap-service-1 rm -rf /app/data/artifacts/
docker exec -it microservices-domain-analyzer-service-1 rm -rf /app/data/artifacts/
docker exec -it microservices-diagram-generator-service-1 rm -rf /app/data/artifacts/


docker exec -it microservices-repo-mapper-service-1 find /app/data/artifacts -type f | grep -v "repomap"




docker logs microservices-domain-file-repomap-service-1

docker logs microservices-repo-mapper-service-1

docker logs microservices-domain-analyzer-service-1

docker logs microservices-diagram-generator-service-1 

docker logs microservices-orchestrator-service-1


docker exec -it microservices-repo-mapper-service-1 cat /app/data/artifacts/83f37baa-2fa7-4f25-beb7-673928181292/domain_mapping.json


