░▒▓ ~/gc/k8s-manifests  # Check the details of one of the pods                                                        ✔  bracket-prod-py3.11   at bracket-irl-cluster 󱃾  at 07:20:16 PM ▓▒░
kubectl describe pod -n bracket-irl $(kubectl get pods -n bracket-irl -o jsonpath='{.items[0].metadata.name}')
Name:             diagram-generator-service-6cdf76d996-844h4
Namespace:        bracket-irl
Priority:         0
Service Account:  default
Node:             gke-bracket-irl-cluster-default-pool-7b5f5c26-0442/**********
Start Time:       Mon, 12 May 2025 18:36:48 +0530
Labels:           app=diagram-generator-service
                  pod-template-hash=6cdf76d996
Annotations:      <none>
Status:           Pending
IP:               **********
IPs:
  IP:           **********
Controlled By:  ReplicaSet/diagram-generator-service-6cdf76d996
Containers:
  diagram-generator-service:
    Container ID:   
    Image:          bracket-irl/diagram-generator-service:latest
    Image ID:       
    Port:           8005/TCP
    Host Port:      0/TCP
    State:          Waiting
      Reason:       ImagePullBackOff
    Ready:          False
    Restart Count:  0
    Limits:
      cpu:     500m
      memory:  1Gi
    Requests:
      cpu:      250m
      memory:   512Mi
    Liveness:   http-get http://:8005/health delay=30s timeout=1s period=10s #success=1 #failure=3
    Readiness:  http-get http://:8005/health delay=5s timeout=1s period=5s #success=1 #failure=3
    Environment:
      SERVICE_NAME:          diagram-generator-service
      HOST:                  0.0.0.0
      PORT:                  8005
      LOG_LEVEL:             <set to the key 'LOG_LEVEL' of config map 'bracket-irl-config'>             Optional: false
      STORAGE_TYPE:          <set to the key 'STORAGE_TYPE' of config map 'bracket-irl-config'>          Optional: false
      STORAGE_PATH:          <set to the key 'STORAGE_PATH' of config map 'bracket-irl-config'>          Optional: false
      MAX_CONCURRENT_TASKS:  <set to the key 'MAX_CONCURRENT_TASKS' of config map 'bracket-irl-config'>  Optional: false
      OPENAI_API_KEY:        <set to the key 'OPENAI_API_KEY' in secret 'bracket-irl-secrets'>           Optional: false
      ANTHROPIC_API_KEY:     <set to the key 'ANTHROPIC_API_KEY' in secret 'bracket-irl-secrets'>        Optional: true
      OPENROUTER_API_KEY:    <set to the key 'OPENROUTER_API_KEY' in secret 'bracket-irl-secrets'>       Optional: true
    Mounts:
      /app/data from bracket-data (rw)
      /var/run/secrets/kubernetes.io/serviceaccount from kube-api-access-8gdcw (ro)
Conditions:
  Type                        Status
  PodReadyToStartContainers   True 
  Initialized                 True 
  Ready                       False 
  ContainersReady             False 
  PodScheduled                True 
Volumes:
  bracket-data:
    Type:       PersistentVolumeClaim (a reference to a PersistentVolumeClaim in the same namespace)
    ClaimName:  bracket-data-pvc
    ReadOnly:   false
  kube-api-access-8gdcw:
    Type:                    Projected (a volume that contains injected data from multiple sources)
    TokenExpirationSeconds:  3607
    ConfigMapName:           kube-root-ca.crt
    ConfigMapOptional:       <nil>
    DownwardAPI:             true
QoS Class:                   Burstable
Node-Selectors:              <none>
Tolerations:                 node.kubernetes.io/not-ready:NoExecute op=Exists for 300s
                             node.kubernetes.io/unreachable:NoExecute op=Exists for 300s
Events:
  Type     Reason     Age                    From               Message
  ----     ------     ----                   ----               -------
  Normal   Scheduled  44m                    default-scheduler  Successfully assigned bracket-irl/diagram-generator-service-6cdf76d996-844h4 to gke-bracket-irl-cluster-default-pool-7b5f5c26-0442
  Normal   Pulling    40m (x5 over 44m)      kubelet            Pulling image "bracket-irl/diagram-generator-service:latest"
  Warning  Failed     40m (x5 over 43m)      kubelet            Failed to pull image "bracket-irl/diagram-generator-service:latest": failed to pull and unpack image "docker.io/bracket-irl/diagram-generator-service:latest": failed to resolve reference "docker.io/bracket-irl/diagram-generator-service:latest": pull access denied, repository does not exist or may require authorization: server message: insufficient_scope: authorization failed
  Warning  Failed     40m (x5 over 43m)      kubelet            Error: ErrImagePull
  Normal   BackOff    3m50s (x171 over 43m)  kubelet            Back-off pulling image "bracket-irl/diagram-generator-service:latest"
  Warning  Failed     3m50s (x171 over 43m)  kubelet            Error: ImagePullBackOff

░▒▓ ~/gcp-bracket-deployment/k8s-manifests                                                                                                       ✔  bracket-prod-py3.11   at 07:21:02 PM ▓▒░


░▒▓ ~/gcp-bracket-deployment/k8s-manifests  # Get your GCP project ID                                                                            ✔  bracket-prod-py3.11   at 07:21:02 PM ▓▒░
PROJECT_ID=$(gcloud config get-value project)

# List the images in your repository
gcloud artifacts docker images list asia-south1-docker.pkg.dev/$PROJECT_ID/bracket-irl
Listing items under project bracket-irl, location asia-south1, repository bracket-irl.

IMAGE                                                                           DIGEST                                                                   CREATE_TIME          UPDATE_TIME          SIZE
asia-south1-docker.pkg.dev/bracket-irl/bracket-irl/bracket_irl_common           sha256:0da2073746ce83daf3a7fa2945f604ea774f44e161603dfb2ef1ea8b6b12d387  2025-05-12T18:17:27  2025-05-12T18:17:27  155098028
asia-south1-docker.pkg.dev/bracket-irl/bracket-irl/diagram-generator-service    sha256:5e71c2b7eaaf99d3aebfd99141b3e504eb667d5552a80a19db744374cf3dc4ab  2025-05-12T18:20:28  2025-05-12T18:20:28  249409100
asia-south1-docker.pkg.dev/bracket-irl/bracket-irl/domain-analyzer-service      sha256:9edfb69ad8d01f4f97aa45aecdd3054d58b8d7e52850f211e33db1454006c76c  2025-05-12T18:18:37  2025-05-12T18:18:37  157029175
asia-south1-docker.pkg.dev/bracket-irl/bracket-irl/domain-file-repomap-service  sha256:9a2983b6f6775f2de8bbfbc466860b83b6600ea10a675a3ae8e763115a0c1130  2025-05-12T18:19:02  2025-05-12T18:19:02  155621766
asia-south1-docker.pkg.dev/bracket-irl/bracket-irl/file-domain-mapper-service   sha256:dc4f177f89143481dd2f969ea0505b27febffffe17094574c607ee50a9ff6433  2025-05-12T18:18:50  2025-05-12T18:18:50  155317762
asia-south1-docker.pkg.dev/bracket-irl/bracket-irl/orchestrator-service         sha256:96c542813cd8bdd3edeaea9fff2c0ca38d30583b5d3b4d22b542b3ca30894179  2025-05-12T18:20:39  2025-05-12T18:20:39  155667629
asia-south1-docker.pkg.dev/bracket-irl/bracket-irl/repo-mapper-service          sha256:3d1f5e6e3da0cd0ef953a6019c5eb5a22527ecf044baf6fceced0e16ea5a0cf3  2025-05-12T18:17:52  2025-05-12T18:17:52  267326189

░▒▓ ~/gcp-bracket-deployment/k8s-manifests                                                                                                       ✔  bracket-prod-py3.11   at 07:21:15 PM ▓▒░












