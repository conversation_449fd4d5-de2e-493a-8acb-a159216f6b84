# Potential questions
Domain: Kubernetes Integration
"Operations & Deployment -> Cluster Management -> Kubernetes Integration"

Mermaid:


QA questions:
1. How does GitLab's sharded database design impact analytics queries that span projects with different security classifications?


2. What mechanisms coordinate vulnerability scanning results with Kubernetes deployments while respecting namespace RBAC and GitLab permissions?



Implement an advanced threat detection system that identifies suspicious activity patterns across our application. The system should correlate events from authentication, repository access, admin area usage, and API calls to identify potential security breaches. Include mechanisms to automatically implement graduated security responses from increased logging to account lockouts based on threat severity.


Design and implement a more sophisticated rate limiting system for the GitLab API that considers user roles, project membership, and request complexity rather than just request count.










## GitLab-Specific Implementation Queries for SWE Interviews
Here are varied GitLab-specific implementation queries at different difficulty levels, each representing approximately one day's worth of work:

Entry-Level Queries
MR Template Enhancement "Enhance our merge request template to include a section for performance impact considerations. Update the template to prompt contributors to describe any potential performance implications of their changes and how they've been measured or mitigated."
Documentation Improvement "Our documentation for setting up GitLab CI/CD with Kubernetes is outdated. Update the documentation to reflect the current best practices, including the use of our new Kubernetes agent, and add troubleshooting steps for common issues."
UI Component Refinement "The file browser in the repository view doesn't properly handle long filenames on mobile devices. Modify the component to truncate filenames appropriately while ensuring users can still see the file extension and access the full filename via tooltip."
Mid-Level Queries
Pipeline Visualization Enhancement "Implement a new visualization option in the CI/CD pipeline view that shows the critical path of jobs that determine the total pipeline duration. This should help users identify bottlenecks in their pipelines."
Issue Board Filtering "Add the ability to filter issues on the issue board by custom fields. Implement both frontend and backend changes to allow users to create and save custom filters based on any issue attribute, including custom fields from the issue description."
Merge Request Approval Workflow "Extend our merge request approval system to support conditional approvals based on file paths. Allow project maintainers to configure rules where changes to specific directories require approval from designated code owners before merging."
Senior-Level Queries
Background Migration Optimization "Our background migration system occasionally causes database contention during peak usage hours. Implement an adaptive throttling mechanism that adjusts the migration rate based on current database load metrics to minimize impact on user-facing operations."
Git Repository Caching "Implement a caching layer for frequently accessed Git repository data to reduce the load on Gitaly. The cache should invalidate appropriately when repositories are updated and should be configurable to balance memory usage with performance gains."
Distributed Tracing Integration "Integrate OpenTelemetry distributed tracing across our Rails application, Sidekiq workers, and Gitaly services to provide end-to-end visibility into request processing. Ensure that trace context is properly propagated across service boundaries."
Feature-Specific Queries
Container Registry Garbage Collection "Implement a more efficient garbage collection process for our container registry that can run incrementally without downtime. The solution should identify and remove unreferenced layers while maintaining the integrity of valid images."
Code Quality Integration "Enhance our code quality feature to support custom rule sets beyond the default CodeClimate rules. Allow users to define and share custom rule configurations and implement a mechanism to apply these rules during CI/CD pipeline execution."
Value Stream Analytics Enhancement "Extend our Value Stream Analytics to track and visualize the time spent in each stage of our development workflow. Add metrics for code review duration, time to first comment, and implement filtering capabilities to analyze trends by team or project."
DevOps-Oriented Queries
Auto DevOps Customization "Improve our Auto DevOps feature to allow more granular customization of the default CI/CD templates without requiring users to completely override them. Implement a mechanism for users to selectively modify specific stages while inheriting the rest."
Kubernetes Integration Enhancement "Enhance our Kubernetes integration to support canary deployments. Implement the necessary changes to the deployment configuration to allow users to specify a percentage of traffic to route to a new version before full rollout."
Gitaly Failover Mechanism "Implement an automatic failover mechanism for Gitaly that detects when a node becomes unresponsive and redirects traffic to healthy nodes. The solution should include proper health checking and minimize disruption during the failover process."


## Challenging GitLab Implementation Queries for Staff SWEs
Here are complex, challenging implementation queries appropriate for Staff-level Software Engineers at GitLab. These tasks require deep system knowledge, architectural thinking, and cross-component expertise.

System Architecture Challenges
Distributed Rate Limiting Framework "Design and implement a distributed rate limiting framework that works across our multi-node GitLab deployments. The system should enforce consistent rate limits for API calls, Git operations, and authentication attempts regardless of which application server handles the request. It should be resilient to network partitions, minimize cross-node communication overhead, and provide configurable degradation behavior when the rate limiting service itself is unavailable."
Cross-Database Consistency Model "As we move toward a decomposed database architecture, implement a consistency model that ensures data integrity across our multiple databases (main, CI, Geo, etc.). Design a transaction coordinator that can manage distributed transactions or implement a compensating transaction system that maintains eventual consistency while providing clear visibility into consistency state."
Intelligent Database Query Routing "Implement an intelligent query routing system that dynamically directs database queries to primary or replica instances based on query characteristics, database load, and consistency requirements. The system should analyze query patterns in real-time, predict which queries are safe to route to replicas, and maintain appropriate transaction isolation levels while maximizing read scalability."
Performance Optimization Challenges
Git Repository Access Optimization "Our Gitaly service experiences performance degradation for repositories with extensive history or large binary files. Implement a predictive prefetching system that analyzes user behavior patterns to preload likely-to-be-accessed repository data. The solution should include telemetry to measure effectiveness and self-tune based on hit/miss rates."
CI Pipeline Execution Optimization "Redesign our CI pipeline scheduling algorithm to optimize for both resource utilization and pipeline completion time. The system should analyze historical job execution patterns, dependencies between jobs, and available runner capacity to make intelligent scheduling decisions. Implement prioritization that balances urgent MR pipelines with scheduled maintenance jobs while preventing resource starvation."
Elasticsearch Query Optimization Framework "Implement an Elasticsearch query optimization framework that automatically rewrites and tunes search queries based on performance characteristics and result quality. The system should monitor query performance, identify problematic patterns, and adaptively modify queries to maintain response time SLAs while preserving result relevance."
Reliability Engineering Challenges
Resilient Background Processing System "Redesign our background job processing system to be resilient against multiple failure modes including database unavailability, Redis outages, and network partitions. Implement appropriate circuit breakers, backpressure mechanisms, and recovery procedures that maintain system stability during partial outages while maximizing throughput of critical jobs."
Automated Incident Response System "Design and implement an automated incident response system that can detect, diagnose, and mitigate common failure patterns without human intervention. The system should integrate with our observability stack, implement progressive remediation steps for known issues, and collect diagnostic information to facilitate post-incident analysis."
Zero-Downtime Schema Migration Framework "Create a zero-downtime schema migration framework that allows complex database schema changes to be applied without service interruption. The solution should handle foreign key changes, column type modifications, and index rebuilds while maintaining application functionality and acceptable performance throughout the migration process."
Security Challenges
Advanced Threat Detection System "Implement an advanced threat detection system that identifies suspicious activity patterns across our application. The system should correlate events from authentication, repository access, admin area usage, and API calls to identify potential security breaches. Include mechanisms to automatically implement graduated security responses from increased logging to account lockouts based on threat severity."
End-to-End Encryption for Sensitive Data "Design and implement an end-to-end encryption system for sensitive project data that ensures the server never has access to unencrypted content. The solution should include key management, secure key distribution to authorized users, and graceful handling of key rotation and revocation while maintaining searchability of encrypted content where possible."
Supply Chain Security Verification "Implement a comprehensive supply chain security verification system for our package registry and dependency proxy. The system should verify package signatures, detect dependency substitution attacks, identify malicious code patterns, and provide attestation of package integrity and provenance."
Complex Feature Implementation
Intelligent Code Review Assistance "Develop an intelligent code review assistance system that analyzes merge requests to identify potential issues beyond simple linting. The system should recognize patterns associated with performance problems, security vulnerabilities, and architectural inconsistencies. It should integrate with the MR workflow to highlight concerning code patterns and suggest improvements without overwhelming reviewers with false positives."
Cross-Repository Dependency Management "Implement a cross-repository dependency management system that tracks dependencies between projects and their components. The system should identify when changes in one repository might affect dependent repositories, automatically trigger appropriate CI pipelines to verify compatibility, and provide visualization of the dependency graph to help developers understand the impact of their changes."
Collaborative Real-Time Editing "Implement a collaborative real-time editing system for the Web IDE that allows multiple users to simultaneously edit the same files. The solution should handle conflict resolution, provide awareness of other users' activities, maintain consistent state across clients with varying network conditions, and integrate with our existing Git workflow for committing changes."





------------
# Output Test Logs

Working beautifully
Gitaly Failover Mechanism "Implement an automatic failover mechanism for Gitaly that detects when a node becomes unresponsive and redirects traffic to healthy nodes. The solution should include proper health checking and minimize disruption during the failover process."










------------

Based on the information gathered, let me now create staff software engineer day-to-day problem questions for the three categories you requested.

Code QA Questions
Code Review Complexity: You're reviewing a merge request that adds a new feature to the GitLab CI/CD pipeline configuration. The changes span across multiple files in the lib/gitlab/ci/config/entry/ directory and introduce a new configuration option. How would you ensure the changes maintain backward compatibility while properly validating the new configuration options?
Schema Evolution: You notice that a developer has added a new column to the packages table without considering the impact on existing queries. The column is not nullable and has no default value. What issues might this cause in production, and how would you recommend modifying this change to follow best practices?
Technical Debt Assessment: You've identified that the ApplicationSetting model has grown to over 3,000 lines with numerous jsonb_accessor fields spread across multiple concerns. What refactoring approach would you propose to improve maintainability while ensuring backward compatibility?
Security Review: A junior developer has implemented a feature that allows users to specify a URL for importing project data. The implementation directly passes user input to an HTTP client without validation. What security vulnerabilities might this introduce, and how would you fix them?
Performance Optimization: You notice that a recently merged feature is causing N+1 queries when listing projects with their associated CI pipelines. How would you identify the root cause and what changes would you recommend to optimize the query performance?
Debugging Questions
Database Connection Issues: Users are reporting intermittent 500 errors when accessing the GitLab instance. The logs show PG::ConnectionBad: connection not open errors occurring randomly. What debugging steps would you take to identify the root cause and how would you resolve this issue?
Memory Leaks: A GitLab instance is experiencing increasing memory usage over time, eventually requiring restarts every few days. How would you approach debugging this issue to determine if it's a memory leak and identify which component is responsible?
CI Pipeline Failures: Multiple users are reporting that their CI pipelines are failing with the error "No space left on device" on the GitLab runners. The runner hosts appear to have sufficient disk space when checked. What could be causing this issue and how would you troubleshoot it?
Caching Inconsistencies: Users are reporting that changes to project settings sometimes don't appear immediately or require a hard refresh. You suspect a caching issue. How would you debug this problem and what areas of the codebase would you investigate?
Slow API Endpoints: The /api/v4/projects/:id/repository/commits endpoint is reported to be extremely slow for repositories with large commit histories. How would you identify the performance bottleneck and what optimization strategies would you consider?
Feature Implementation Questions
Package Registry Enhancement: Design a feature that would allow users to set up automatic package version expiration policies based on custom rules (e.g., keep only the last 5 versions of each package). What components would need to be modified and how would you implement this while ensuring backward compatibility?
Cross-Database Relationships: You need to implement a feature that requires establishing relationships between models that exist in different database schemas (e.g., gitlab_main and gitlab_ci). How would you approach this design challenge while respecting the database architecture constraints?
Workflow Automation: Design a feature that allows users to create custom automation rules for issue management (e.g., automatically assign issues with specific labels to certain team members). What components would you need to create or modify, and how would you ensure the feature is scalable?
Metrics Collection: Implement a system to collect and visualize performance metrics for CI/CD pipelines, allowing users to track trends over time. What data would you collect, how would you store it efficiently, and what considerations would you make for high-volume GitLab instances?
API Rate Limiting Enhancement: Design and implement a more sophisticated rate limiting system for the GitLab API that considers user roles, project membership, and request complexity rather than just request count. What components would you need to modify and how would you ensure minimal performance impact?
These questions reflect real-world challenges that a staff software engineer would face when working with a complex Ruby on Rails application like GitLab, covering code quality assurance, debugging complex issues, and implementing new features while maintaining system integrity.










------------------

# Planning the demo

Okay great, now the real task starts.

Help me make the demo.

Okay, so I want the demo to be carrying a narrative, which is like a story. Good people from GitLab are going to look at this. People who are familiar with the long-term and short-term AI roadmap. So, that's what I want to do. I want to showcase the entire thing, the entire demo and the technology to be immediately showcasing step-by-step how it directly fulfills the next one and a half year of AI-related roadmap and AI-related features.

So I'll give you an understanding of what I have thought about the content to be kept in there. But this is not aligning with my goal of having a narrative, a story narrative, which is a short-term and long-term AI roadmap related. So let me give you an understanding of this. So Bracket, we start with introducing Bracket, showcasing it like a Bracket context engine, seeing it as a compression technology, and then making them understand somehow, I don't know visually just by words or how, but somehow making them understand that we compress it in a way which is away from semantic search and gives a very holistic understanding of things. So that's my idea, at least, that before diving into any feature or the real demo, I want to give them a quick overview of what exactly the technology is, like the real capability. So the premise here is that I don't want to give away the real behind the scenes, because it is a bit early in the stage. So I thought of bringing this narrative and taking this narrative in a way in which the behind the scenes of hierarchical mermaids are not really apparent as something that is being used. So to do this, I am thinking to center everything around something I'm going to call Bracket context engine. This Bracket context engine is going to be serving like the reasoning engine over which takes our compression input and understands, it understands the entire code based from a very, from the very top architectural point of view, down to the very implementation point of view in a hierarchical manner, such that we have a top to bottom full understanding as with increasing granularity as we go in, as well as bottom to top understanding with increasing abstractions and cross domain understanding. So earlier, I've shared with you a lot of good text about what exactly are my capabilities. I will share them again down here. So maybe we can pick something from that. Okay. So far, from what I have told you right now, we have just thought about the premise. How are we going to showcase our capability? And then how are we going to showcase each of our features? So far, we have covered that the capability understanding is to be told as Bracket context engine, something that is unique, defensible. And then the features will be shown as being powered by the Bracket context engine.


So, I'm going to go over the features that I have thought of that should be shown. They are also present in the screenshots that I sent you above. So, if we enter the bracket, we enter bracket, we open VS Code, and for the demo purpose, I have 400,000 lines of code part of GitLab code base. So, first I'll explain about that, that this is app and lib folders selectively. We'll explain that I have chosen this number showing large code base, but for brevity or for easier indexing, something that I have to explain that why I chose 400,000 tokens and not this entire GitLab repo. So, we load that. We immediately have a left panel and right panel. Left panel. Actually, initially, we'll only have the left panel, which is the global code base overview and the landing page for bracket. It shows bracket, and this is where I am thinking to probably start explaining what I told you earlier, that setting the premise, setting the tone of what bracket really is, because not everyone in there knows yet what it is. Then I can probably explain a little bit about bracket being implemented as an extension, and we jump into maybe some more details about compression that this 400,000 tokens, 400,000 lines of code, which is now get compressed into 20,000 lines of code and something like that. So, something to say there as well. Now, next, the very first feature we start with is the code base overview. So this will be the very first panel in which we click on the index and it starts generating the global code base overview along with suggested questions. So this comes as the very first thing in which I immediately make them go through the output of the global overview a little bit. Hey, this is the explanation. It's pretty cool. It's able to cover such, such, and such things. And inherently, it is using the compression tokens inside it. From there, I show the suggested questions, which covers different parts of the code base, but still very much focused on abstracted understanding of the code base. I can also say here that this is very useful for onboarding or exploring new areas of the code base, among other things. So this is the first and second feature. Um, probably right about now will be a good time to also say that the bracket context engine also powers another way to understand the code base. And that's where we bring in the mermaid, um, the mermaid assistant or the mermaid. Yeah, we're going to give it a certain name, maybe architecture lens or something, but probably we'll come up with something nice. So I give them an understanding of what it is. Um, I will initially select an area or certain files to be understood. So this is going to be the long running team in there. For example, you can potentially explore how rate limiting is implemented inside GitLab. And just for an example for now. So we start with rate limiting. I've opened a certain file and it shows a mermaid in four hierarchies that, hey, here is how rate limiting is implemented. I could give them a brief intro to like online, understand rate limiting along in the demo itself in the demo itself, and then go back up in the hierarchy to understand how it really fits inside it. And potentially I can also pick up another theme, very abstracted, very complicated. Maybe integrations could be one in which we can just showcase the integration being done. Um, and see how all of that is implemented. Cool. So mermaid introductions, mermaid architectural lens introduction is there. Next comes up is code base question answering or chat with your code base. Now, this is the first thing that we, this is the first way in which we actually showcase code understanding and code generation life capabilities. And here I give a brief about two different agents that I have currently in place. One is chat with code base and another is the full fledged code writing agent. So I highlight that the most amazing thing about bracket is that the user does not need to inject any context. And this is probably what we should also add in the initial introduction as well, that there is no need for a user to add context. They can, we have provided those options, we have provided those options, but, um, the biggest feature that we have in place is that we will ourselves retrieve the context through code localization processing, um, using bracket context engine and injected at each and every place. And we have to highlight here, I mean, in the beginning, at least that this is not semantic search and this is not, um, simple matching, but rather highly complicated and abstracted logical understanding of the entire code base. Okay. So coming back to the feature, chat with your code base feature. I will pick up certain query, um, some pre-decided query, probably related to rate limiting or integration. I'll pick up something. Okay. We send it in in the very first thing bracket context engine comes in and that goes on for 10, 15 seconds. The processing, I explain behind the scenes that how our team of agents, how our current agent is interacting with our compressed token set to, from top to bottom and bottom to top to find the relevant logical parts of the code base. Um, and actually drawing parallels here to how software engineers work as well, that they do not work from a semantic perspective, but rather logically and how the logic is structured. And now we extract only a small part of it. In this, when I'm done saying this immediately, the context auto-injection, um, view appears once the bracket context engine is done and the response starts generating. Okay. So right now I would prefer to first go in the, I would simply say that, Hey, see, we didn't attach any context ourselves. Um, we did some magic, which we'll come to in a moment. And now we have this amazingly cool response, which I'll pick us up with such a query that it immediately showcases that man. Oh my God. We do understand the full entire 400,000 lines of context because clearly this requires, um, very disconnected components speaking to one another. And it also has to show that, um, this is not being possible by semantic search. Cool. So I go through the response quickly, um, highlighting the important parts of, and then I go into the bracket, uh, auto-context ingestion injection. So this is the highlighted part that this is where we emphasize the most on how we are, um, very much different from semantic search and looking at the outputs, um, how we extracted it. We got to explain it properly. And then at the same time, um, one of the features that you are not aware of is I can simply just click on one file and one function and it opens it side by side in the panel highlighted as well. Along with it, we open the moment chart for it, uh, which is right immediately next to it. And this brings out like a very, very beautiful and nice connect interconnection of, um, how the retrieve context is actually very logically, belonging, um, to the entire, uh, in the entire architectural view. So this in a way makes the context retrieval highly explainable that not only did we automatically retrieve the context, but we are also giving the user the option to explore the context to see how each retrieval, how each, each file and function that we retrieved actually has a connection in the right manner, uh, up to the very top. For example, if it is rate limiting, then it has to go up and explain and interconnect with infrastructure or something, you know,
Yes, so the most important thing to be highlighted here is how, this is not a semantic search really, but rather this is how a really good software engineer employs their cognitive mental model and mental understanding of a code base, right? How they actually go from an abstraction down to the very implementation or otherwise, in the opposite direction as well. So the highlight here and probably the total understanding of what Bracket is can be said to be like a mirror of a senior software, staff software engineer's cognitive model of an entire code base. And that's what we have to highlight somehow. Okay, so this is a QA-related question, the QA-related feature that we currently were discussing. Nothing else comes to mind. I think maybe I can show another one here as well. In here, I can also discuss the tokens used, the cost, and yeah, let me know if there's something else. Last feature is the agent mode that comes to mind. So agent mode is something that is capable of writing code, of executing terminal, of getting information from a browser. So essentially, it can read, write, create file, and among this, two other things. But all of this again is powered by the Bracket context engine. On similar themes, I take certain query, probably about rate limiting and implementing a new way to rate limit, maybe, or something else. And we provide this to the Bracket agent. So this agent first does the same, I mean, the Bracket context engine and the auto-injection works in a similar manner. They keep going, I keep explaining, and then the magic starts to happen. It gathers the context that we provided it, we injected it, it tries to make sense of it, and then it is allowed to read certain files that it seems interesting. I mean, it will be probably two to three files out of 50 to 60 files or something of that ratio. It will go ahead and start creating new files, which we will see side by side, which we will see in real life, like during the demo as well. During the demo itself, I mean, of course, we are going to show it. So files are created, existing files are edited. And in the end, this in itself probably goes for, the processing of this goes for three, four minutes.



















So, I have mapped out the step-by-step things that we are going to undertake in the demo and show in the demo. So, I'll go through with them with you and I've also decided what sort of queries and running examples I'm going to take to introduce each of these features, okay? So, this is the flow that I have in mind. Initially, as soon as the demo opens up, we spend some time on introducing Bracket. Maybe I can build up a small slide which introduces Bracket, what it is, its capabilities, the application engine, full code-based understanding, cognitive model, indexing, and a bit about product intro that we are going to see further. So, maybe some time on this, would like your opinion on it. Once we are done with this, we move on to, we open VS Code and we show our GitLab loaded 400,000 lines of code already in there, already indexed, mentioning that we have already indexed it for some reason. And it costed us about this much money and took this much time. So, we start with the code-based overview panel, nothing special there. Again, same, going through the response quickly to showcase what is good and what is not. Then, we move to suggested questions. Suggested questions now are architect, now domain-wise distributed, so I can show a couple of questions. This, I mean, point to a couple of questions and show them that to generate such questions, it is a must to first understand the entire code base, cross-domain vertically and horizontally. From this, we can move to mermaid architectural lens exploration or introduction. So, I could simply say that maybe let's, that I was earlier looking into this and Kubernetes, how I pronounce it, looked interesting. Let's see how we can, how I can understand more about it. So, I open something related to Kubernetes. We have the hierarchical structure. I try to introduce how this hierarchical structure works, this mermaid. Architecture lens works from a function to function movement. Hierarchically, how it fits in different perspectives. Maybe I open, I also open up another mermaid domain-wise. So, the main domain that we have taken so far is operations and requirement at the top, going into cluster management, going into Kubernetes integration. So, probably just from there, I can go into something related, maybe source graph or, I don't know, maybe Google Cloud and show things related to Google Cloud. Okay. Yeah. So, that is that. Then, we move on to code base QA, that is, chat with your code base. For this, I'm thinking to select one of the queries from the suggested questions itself. And I'm thinking to run two different QA, chat with your code base type of queries sequentially. The number one is, how does GitLab's sharded database design impact analytics queries that span projects with different security classifications? I provide emphasis on the diversified understanding requirement that such a question has. And then, of course, start introducing context engine, start step-by-step helping it understand how this is not semantic search, etc., etc. As the whole scenario that we discussed previously, I shared with you in the earlier message. This is where we actually emphasize how context is retrieved, how the bracket context engine works. We do not need to inject any, add any context we can if we want to, but bracket takes care of the complete code base understanding. Therefore, what to retrieve. So, we go through the generated response, try to understand it, try to cover ground on how many different touch points architecturally and domain-wise it hits. And from there, I would go to the automatic code base injection and explain that properly, taking up a few examples that we see that what are the files indicating, what are the functions, how do we ingest it, how do we create it, etc., etc. And the fact that this is not semantic search, but rather logically driven bottom-to-top and top-to-bottom navigation from our agent leading to such selections. From there, I'll also showcase clicking of the function leading to opening of the file and highlighting of the function. Subsequently, at the same time, leading to new moments being rendered, which are corresponding to that function. And we do that for three profiles, and then we move on with the next code chart, next QA question. So, which will be, what mechanisms coordinate vulnerability scanning results with Kubernetes deployment while respecting namespace, RBAC, and GitLab permissions? So, this is going to be another question, same thing repeating here again. 
Okay, so once this is done, then next up we pick the Practice Agent feature, and we set out a very advanced implementation feature, which is: "Implement an advanced threat detection system that identifies suspicious activity patterns across our application. The system should correlate events from authentication, repository access, admin area usage, and API calls to identify potential security breaches. Include mechanisms to automatically implement graduated security responses from increased logging to account lockouts based on threat severity." So, the agent keeps the... again, we provide the context from Ducket code engine, and then sequentially it starts to understand that given context, it starts to create new files, new plans, and I keep explaining as we create things that what is really happening in front of us. How we are creating new files, these capabilities, and point out the cost as well now and then. This is going to take a good enough time because the coverage of this will be quite high. So, possibly, maybe I can do something else in the meantime while the agent is running, and I can take 2-3 minutes to show something else or talk about something else and then come back to it, and then showcase what all actually happened and the code generated in there. So, that is the overall plan for now. Let's think about where do we go from here. I would ideally love to start working on this script so that we have a clear designated time understanding of how much time goes in which section and what exactly are we going to speak, how exactly are we going to present, and what is to be said, what is to be shown, and how much time is to be put in. So, let's start with this.





---------

Hi, I'm Dhruv. I'm here to present Bracket today. Bracket is a compression engine which converts codebases of any size into a cognitive mental model, mimicking exactly how good software engineers perform. We do this by converting the entire codebase into a logic graph that the AI can navigate from top to bottom, bottom to up, and cross-domain as well. Today, we are going to see a portion of the GitLab codebase, around 400,000 lines, answer some cross-domain questions with zero manual context and automatic context ingestion, and finish by shipping a merge request that writes itself. Let's begin. Let's begin. So, here you are seeing a slice of GitLab codebase, and the application and library end, some parts of it, which makes up for around 400,000 lines of code, or 4 million tokens. So, the indexing has already run on this, and the output artifacts are 20,000 logical token outputs. So, these guarantee to be a living cognitive model, which on the cold start took X number of dollars and N number of minutes, and on every subsequent change in the codebase, we only ingest the delta code change that is made, which is extremely cheap to ingest. So, these 20,000 tokens encode the architecture, domain, relationships, and key functionalities, exactly how a software engineer at the staff level holds the information in their head. Let's start to see how the bracket context, and this is what we call the bracket context engine, which is essential. So, let's start by seeing the global overview. So, the cognitive model answers, what is this thing? What is this entire codebase? First of all, the domain pops up. We have an abstracted overview of how things lie in different parts of the codebase. There's this big summary. We can go into longer details in this. Below this, you will see suggested questions that are synthesized by the context engine. So, a new hire getting on board can easily ask questions like, how does GitLab handle on-prem package replication? And start in the rabbit hole immediately. Just out there in minutes, so they don't even have to touch the codebase at this point. Next up, in bracket, I have built something that doesn't exist at the moment elsewhere. The idea for this is to reduce the cognitive load from the software engineer's mind and offload it to understand the entire code logic functionalities and offload it to an AI so that the engineer can immediately come back and grasp visually what is going on rather than having to continuously update themselves. This is going to be very important as AI-generated code increases. And we will eventually start seeing 10,000-20,000 lines of code changes every day in medium-sized companies. So, rebuilding the cognitive model for an engineer is going to be a bottleneck. And this is exactly what results. So, look at this visual proof of the model, a light-moment diagram. When we zoom out, let's start by seeing how the Kubernetes implementation integration is in place. So, here we play around with it a little bit. We see the different components, the different smaller sections of it. And then we also highlight that this is quite hierarchical. And then we go back one part and explore integrations. From there, we see that here is an integration of Google Cloud or something else. And we go in and look at it for a few seconds. So, at the same time, I want to highlight that Bracket is a bidirectional engine. It runs a bottom-up pass to group functions and a top-down pass that applies architectural patterns and cross-domain pass to detect shared edges. So, this diagram regenerates on every code change. So, the design docs are never rotted and immediate visual understanding is able to be captured. Now, I want to show you guys how we actually understand the code base and how the context engine really works. Let's pick one of these tasks from suggested questions. That seems like a natural step. So, let's pick up, how does GitLab shared DB database design impact analytics queries that span projects with different security classifications? Not an easy question at all. It does, it touches on multiple parts of the code base which talk to one another, but not in a very apparent way. So, I want to highlight, I'm not sure what the answer comes out, but I want to highlight that this is a very, this requires repository-wide understanding. It clearly does. So, let's run it. So, I'm going to explain step-by-step what is going on behind the scenes and what we see right in front of us. The bracket context engine, it essentially takes in the query, it starts reasoning through a compressed layer of logic and breaks it down into sequential steps of what is going on. So, the idea of this is to retrieve context. If you notice, we're not providing any context ourselves. So, the context engine goes in and it retrieves context so that it can inject that context without having the user prompt or provide anything at all. That's the most ultimate goal, to give it extreme level of agency so that the generation can, the code retrieval, the code localization can happen by itself. And it should happen with very high precision and very high recall. And as we can see, the bracket context ingestion shows, retrieves around 60 files. One thing to note here is that we're not considering all the 60 files. We are picking up the only important part of, the only important functions from that. So, we're already reducing this space by operating on a function and more granular levels of symbols. So, as you can see, we retrieve that with a very high precision. And let's take a look at the output. So, the generated code, the generated response says this, this, this, I'm going to explain this. And it clearly touches on multiple parts of the code base without even having the user provide any sort of context or any sort of background to what to look for, where to look for, etc. So, how is this happening? Let's look at the, this section, bracket context ingestion, auto context ingestion. Here we have a list of files which we retrieved. And these are all the files spanning different parts of the code base, which are logically speaking to one another to come up and needed to answer this.

So, when exploring this auto-inject functionality, I also want to clearly highlight how this is very, very much different from semantic search, and not at all driven by semantic search, but rather highly driven by pure logic understanding through a new way of agent, a new way of using the compression tokens, compressed tokens, which lead us to be very highly precise and have high recall as well, so that we are not missing any code coverage and we are navigating this logically rather than in a much less certain way of semantic search, and not fragmented at all, but rather having a very holistic and very logic-driven understanding of this. So, while showing the auto-inject, I'll also show that when we click on a file and a function, the function opens up and the corresponding malware chart opens up, so that just using a couple of examples, I'll showcase how the context retrieval is highly relevant, and we can, with all of this, we have a direct linking from context retrieval to explainability, that yes, we retrieved it, but a score of 0.9 might not be good enough, might not be relevant enough, so here is the full linking of how this is relevant, and that can be justified and explained through the malware charts. Cool, so that is it. Then, next chart two, I will like to take it, run it, our vulnerability scan results coordinated with Kubernetes deployment namespaces while respecting RBAC. So, we'll do the same things again, we'll look at the code base, look at the results, and showcase and attest that entire repository understanding is being used and emplaced. Why is this possible? The cognitive mental model stores every domain, subdomains, and this is, no, I'm not going to add this. So, next up, I want to showcase the bracket agent mode, which is a fully autonomous staff engineer level agent, which is good at generating code, understanding code, writing code, editing code, deleting code, etc. So, I want to showcase the real capability of bracket context engine, how it empowers implementation of really large-scale tasks, and it does it in a very cost-efficient and time-efficient manner. So, here we are going to implement the task that we previously discussed. We put that in, and then we start speaking, we properly tell what is happening, the agent interrogates the cognitive model, this happens, this happens, and then this happens, and this happens. So, I sit through it for the first two minutes in which we see how things unfold, how we're reading more and more, how we're reading a few files, making sense of the auto-injected context, and starting to build a plan of what is to be implemented, then start creating new files, writing new files. Then this process is probably going to go for three, four minutes, so we have some time to possibly talk about roadmap acceleration with GitLab and bracket in place. So, we'll do that, and in the meantime, come back and, no, I mean, probably show something else as well, and then come back and see the entire result, the readme of what all happened, how it all happened, what it really means, the quality, the coverage, etc. So, that's going to be a really, really awesome output, which we should go in a bit more detail. Then look at the files changed, the logic implemented through the readme, and the cost incurred, etc., etc. So, when that is done, we, yeah, I mean, we've got to close it somehow, and probably, again, that indication towards bracket and GitLab, and bracket accelerating the GitLab roadmap by more than one year by introducing new capabilities, introducing a complete novel approach towards codebase, repository-level codebase understanding, along with concrete examples and outputs, etc., etc. So, yeah.







