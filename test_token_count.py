from bracket_core.localisation.global_localisation import estimate_token_count, count_tokens_with_tiktoken

test_string = "This is a test string to check if our token counting works correctly."
print(f'Estimated tokens for test string: {estimate_token_count(test_string)}')

# Test with a longer string
long_string = "This is a much longer test string " * 1000
token_count = estimate_token_count(long_string)
print(f'Estimated tokens for long string: {token_count}')
print(f'Long string exceeds 45K tokens: {token_count > 45000}')
