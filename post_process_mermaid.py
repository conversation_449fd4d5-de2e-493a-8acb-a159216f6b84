#!/usr/bin/env python3
"""
Mermaid Diagram Post-Processor

This script applies syntax fixes to mermaid diagrams in a directory without using LLM beautification.
It uses the post-processing functionality from the MermaidBeautifier class.

Usage:
    python post_process_mermaid.py --input_dir /path/to/mermaid/diagrams --output_dir /path/to/output
"""

import os
import logging
import argparse
import asyncio
from pathlib import Path

from mermaid_beautifier.mermaid_beautifier import MermaidBeautifier

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Post-process mermaid diagrams to fix syntax issues")

    parser.add_argument(
        "--input_dir",
        type=str,
        required=True,
        help="Directory containing mermaid diagram files",
    )

    parser.add_argument(
        "--output_dir",
        type=str,
        help="Directory to save processed diagrams (if not specified, overwrites original files)",
    )

    parser.add_argument(
        "--dry_run",
        action="store_true",
        help="Don't actually save changes, just log what would be done",
    )

    args = parser.parse_args()

    # Initialize the mermaid beautifier with post-processing only
    beautifier = MermaidBeautifier(
        input_dir=args.input_dir,
        output_dir=args.output_dir,
        dry_run=args.dry_run,
    )

    # Run only the post-processing
    stats = await beautifier.post_process_directory()
    
    # Print summary
    print("\nPost-processing Summary:")
    print(f"Total files: {stats['total_files']}")
    print(f"Processed files: {stats['processed_files']}")
    print(f"Improved files: {stats['improved_files']}")
    print(f"Unchanged files: {stats['unchanged_files']}")
    print(f"Failed files: {stats['failed_files']}")

if __name__ == "__main__":
    asyncio.run(main())
