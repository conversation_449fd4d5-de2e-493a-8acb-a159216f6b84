# Mermaid Companion

The Mermaid Companion is a feature that displays mermaid diagrams associated with functions as you navigate through your codebase. It leverages the domain taxonomy generated by bracket_core/irl.py to provide visual context for your code.

## Setup

The Mermaid Companion is configured to look for the domain taxonomy file at:
`/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/beauty_of_life/data/bracket/domain_taxonomy.json`

If the file is not found at this location, it will look in several other default locations.

## Usage

1. **Open the Mermaid Companion View**:
   - The Mermaid Companion view is available in the sidebar.
   - Click on the Bracket icon in the activity bar, and you should see the "Mermaid Companion" view.

2. **Navigate to a Function**:
   - When you place your cursor on a function in your code, the Mermaid Companion will automatically display the associated mermaid diagram.
   - If no diagram is found for the specific function, it will try to find a diagram for the file.

3. **Interact with the Diagram**:
   - Use the zoom controls to zoom in/out of the diagram.
   - Click the "Reset" button to reset the zoom level.
   - Click the "Export PNG" button to export the diagram as a PNG file.

4. **Commands**:
   - `Bracket: Show Mermaid Diagram for Current Function` - Manually trigger the display of the mermaid diagram for the current function.
   - `Bracket: Toggle Mermaid Companion` - Enable or disable the automatic updating of the mermaid diagram as you navigate.
   - `Bracket: Refresh Mermaid Mapping` - Reload the domain taxonomy file if it has been updated.

## Troubleshooting

If you encounter any issues:

1. **Check the Output Panel**:
   - Open the Output panel (`View > Output`) and select "Bracket" from the dropdown to see detailed logs.

2. **Verify the Domain Taxonomy File**:
   - Make sure the domain taxonomy file exists at the expected location.
   - Check that the file contains valid JSON with the expected structure.

3. **Refresh the Mapping**:
   - Use the `Bracket: Refresh Mermaid Mapping` command to reload the domain taxonomy file.

4. **Restart VS Code**:
   - If all else fails, try restarting VS Code.

## Feedback

If you have any feedback or suggestions for improving the Mermaid Companion, please let us know!
