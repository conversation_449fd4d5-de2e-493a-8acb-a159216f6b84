{"name": "roo-cline", "displayName": "Bracket", "description": "A whole dev team of AI agents in your editor.", "publisher": "RooVeterinaryInc", "version": "3.11.14", "icon": "assets/icons/icon.png", "galleryBanner": {"color": "#617A91", "theme": "dark"}, "engines": {"vscode": "^1.84.0", "node": "20.18.1"}, "author": {"name": "Bracket"}, "repository": {"type": "git", "url": "https://github.com/RooVetGit/Roo-Code"}, "homepage": "https://github.com/RooVetGit/Roo-Code", "categories": ["AI", "Cha<PERSON>", "Programming Languages", "Education", "Snippets", "Testing"], "keywords": ["cline", "claude", "dev", "mcp", "openrouter", "coding", "agent", "autonomous", "chatgpt", "sonnet", "ai", "llama", "roo code", "roocode"], "activationEvents": ["onLanguage", "onStartupFinished"], "main": "./dist/extension.js", "contributes": {"submenus": [{"id": "roo-code.contextMenu", "label": "Bracket"}, {"id": "roo-code.terminalMenu", "label": "Bracket"}], "viewsContainers": {"activitybar": [{"id": "roo-cline-ActivityBar", "title": "Bracket", "icon": "assets/icons/icon.svg"}]}, "views": {"roo-cline-ActivityBar": [{"type": "webview", "id": "roo-cline.SidebarProvider", "name": ""}, {"type": "webview", "id": "bracket-mermaid-companion", "name": "Mermaid Companion"}]}, "commands": [{"command": "bracket-mermaid-companion.showMermaidForCurrentFunction", "title": "Show Mermaid Diagram for Current Function", "category": "Bracket"}, {"command": "bracket-mermaid-companion.toggleMermaidCompanion", "title": "Toggle Mermaid Companion", "category": "Bracket"}, {"command": "bracket-mermaid-companion.refreshMermaidMapping", "title": "Refresh Mermaid Mapping", "category": "Bracket"}, {"command": "roo-cline.plusButtonClicked", "title": "New Task", "icon": "$(add)"}, {"command": "roo-cline.prompts<PERSON><PERSON>onClicked", "title": "Prompts", "icon": "$(list-unordered)"}, {"command": "roo-cline.mcpButtonClicked", "title": "MCP", "icon": "$(server)"}, {"command": "roo-cline.historyButtonClicked", "title": "History", "icon": "$(history)"}, {"command": "roo-cline.popoutButtonClicked", "title": "Open in New Tab", "icon": "$(open-preview)"}, {"command": "roo-cline.settingsButtonClicked", "title": "Settings", "icon": "$(settings-gear)"}, {"command": "roo-cline.helpButtonClicked", "title": "Help", "icon": "$(question)"}, {"command": "roo-cline.openInNewTab", "title": "Open In New Tab", "category": "Bracket"}, {"command": "roo-cline.explainCode", "title": "Explain Code", "category": "Bracket"}, {"command": "roo-cline.fixCode", "title": "Fix Code", "category": "Bracket"}, {"command": "roo-cline.improveCode", "title": "Improve Code", "category": "Bracket"}, {"command": "roo-cline.addToContext", "title": "Add To Context", "category": "Bracket"}, {"command": "roo-cline.newTask", "title": "New Task", "category": "Bracket"}, {"command": "roo-cline.terminalAddToContext", "title": "Add Terminal Content to Context", "category": "Terminal"}, {"command": "roo-cline.terminalFixCommand", "title": "Fix This Command", "category": "Terminal"}, {"command": "roo-cline.terminalExplainCommand", "title": "Explain This Command", "category": "Terminal"}, {"command": "roo-cline.terminalFixCommandInCurrentTask", "title": "Fix This Command (Current Task)", "category": "Terminal"}, {"command": "roo-cline.terminalExplainCommandInCurrentTask", "title": "Explain This Command (Current Task)", "category": "Terminal"}, {"command": "roo-cline.setCustomStoragePath", "title": "Set Custom Storage Path", "category": "Bracket"}, {"command": "roo-cline.focusInput", "title": "Focus Input Field", "category": "Bracket"}, {"command": "bracket-context-engine.processQuery", "title": "Process Query with Context Engine", "category": "Bracket Context Engine"}, {"command": "bracket-context-engine.toggleContextEngine", "title": "Toggle Context Engine", "category": "Bracket Context Engine"}], "menus": {"editor/context": [{"submenu": "roo-code.contextMenu", "group": "navigation"}], "roo-code.contextMenu": [{"command": "roo-cline.addToContext", "group": "1_actions@1"}, {"command": "roo-cline.explainCode", "group": "1_actions@2"}, {"command": "roo-cline.fixCode", "group": "1_actions@3"}, {"command": "roo-cline.improveCode", "group": "1_actions@4"}, {"command": "bracket-mermaid-companion.showMermaidForCurrentFunction", "group": "1_actions@5"}], "terminal/context": [{"submenu": "roo-code.terminalMenu", "group": "navigation"}], "roo-code.terminalMenu": [{"command": "roo-cline.terminalAddToContext", "group": "1_actions@1"}, {"command": "roo-cline.terminalFixCommand", "group": "1_actions@2"}, {"command": "roo-cline.terminalExplainCommand", "group": "1_actions@3"}, {"command": "roo-cline.terminalFixCommandInCurrentTask", "group": "1_actions@5"}, {"command": "roo-cline.terminalExplainCommandInCurrentTask", "group": "1_actions@6"}], "view/title": [{"command": "bracket-mermaid-companion.refreshMermaidMapping", "group": "navigation@1", "when": "view == bracket-mermaid-companion"}, {"command": "bracket-mermaid-companion.toggleMermaidCompanion", "group": "navigation@2", "when": "view == bracket-mermaid-companion"}, {"command": "roo-cline.plusButtonClicked", "group": "navigation@1", "when": "view == roo-cline.SidebarProvider"}, {"command": "roo-cline.historyButtonClicked", "group": "navigation@2", "when": "view == roo-cline.SidebarProvider"}], "editor/title": [{"command": "roo-cline.plusButtonClicked", "group": "navigation@1", "when": "activeWebviewPanelId == roo-cline.TabPanelProvider"}, {"command": "roo-cline.historyButtonClicked", "group": "navigation@2", "when": "activeWebviewPanelId == roo-cline.TabPanelProvider"}]}, "configuration": {"title": "Bracket", "properties": {"roo-cline.allowedCommands": {"type": "array", "items": {"type": "string"}, "default": ["npm test", "npm install", "tsc", "git log", "git diff", "git show"], "description": "Commands that can be auto-executed when 'Always approve execute operations' is enabled"}, "roo-cline.vsCodeLmModelSelector": {"type": "object", "properties": {"vendor": {"type": "string", "description": "The vendor of the language model (e.g. copilot)"}, "family": {"type": "string", "description": "The family of the language model (e.g. gpt-4)"}}, "description": "Settings for VSCode Language Model API"}, "roo-cline.customStoragePath": {"type": "string", "default": "", "description": "Custom storage path. Leave empty to use the default location. Supports absolute paths (e.g. 'D:\\RooCodeStorage')"}}}}, "scripts": {"build": "npm run vsix", "build:webview": "cd webview-ui && npm run build", "build:esbuild": "node esbuild.js --production", "compile": "tsc -p . --outDir out && node esbuild.js", "test:mermaid": "npm run compile && code --extensionDevelopmentPath=$PWD ..", "install:all": "npm install npm-run-all && npm-run-all -l -p install-*", "install-extension": "npm install", "install-webview": "cd webview-ui && npm install", "install-e2e": "cd e2e && npm install", "lint": "npm-run-all -l -p lint:*", "lint:extension": "eslint src --ext ts", "lint:webview": "cd webview-ui && npm run lint", "lint:e2e": "cd e2e && npm run lint", "check-types": "npm-run-all -l -p check-types:*", "check-types:extension": "tsc --noEmit", "check-types:webview": "cd webview-ui && npm run check-types", "check-types:e2e": "cd e2e && npm run check-types", "package": "npm-run-all -l -p build:webview build:esbuild check-types lint", "pretest": "npm run compile", "dev": "cd webview-ui && npm run dev", "test": "node scripts/run-tests.js", "test:extension": "jest", "test:webview": "cd webview-ui && npm run test", "prepare": "husky", "publish:marketplace": "vsce publish && ovsx publish", "publish": "npm run build && changeset publish && npm install --package-lock-only", "version-packages": "changeset version && npm install --package-lock-only", "vscode:prepublish": "npm run package", "vsix": "rimraf bin && mkdirp bin && npx vsce package --out bin", "watch": "npm-run-all -l -p watch:*", "watch:esbuild": "node esbuild.js --watch", "watch:tsc": "tsc --noEmit --watch --project tsconfig.json", "watch-tests": "tsc -p . -w --outDir out", "changeset": "changeset", "knip": "knip --include files", "clean": "npm-run-all -l -p clean:*", "clean:extension": "<PERSON><PERSON><PERSON> bin dist out", "clean:webview": "cd webview-ui && npm run clean", "clean:e2e": "cd e2e && npm run clean", "vscode-test": "npm-run-all -l -p vscode-test:*", "vscode-test:extension": "tsc -p . --outDir out && node esbuild.js", "vscode-test:webview": "cd webview-ui && npm run build", "update-contributors": "node scripts/update-contributors.js", "generate-types": "tsx scripts/generate-types.mts"}, "dependencies": {"@anthropic-ai/bedrock-sdk": "^0.10.2", "@anthropic-ai/sdk": "^0.37.0", "@anthropic-ai/vertex-sdk": "^0.7.0", "@aws-sdk/client-bedrock-runtime": "^3.779.0", "@estruyf/vscode-theme-converter": "^1.1.0", "@google-cloud/vertexai": "^1.9.3", "@google/generative-ai": "^0.18.0", "@mistralai/mistralai": "^1.3.6", "@modelcontextprotocol/sdk": "^1.7.0", "@types/clone-deep": "^4.0.4", "@types/pdf-parse": "^1.1.4", "@types/tmp": "^0.2.6", "@types/turndown": "^5.0.5", "@types/vscode": "^1.95.0", "@vscode/codicons": "^0.0.36", "@xyflow/react": "^12.5.5", "allotment": "^1.20.3", "axios": "^1.7.4", "cheerio": "^1.0.0", "chokidar": "^4.0.1", "clone-deep": "^4.0.1", "default-shell": "^2.2.0", "delay": "^6.0.0", "diff": "^5.2.0", "diff-match-patch": "^1.0.5", "fast-deep-equal": "^3.1.3", "fast-xml-parser": "^4.5.1", "fastest-levenshtein": "^1.0.16", "fzf": "^0.5.2", "get-folder-size": "^5.0.0", "globby": "^14.0.2", "i18next": "^24.2.2", "isbinaryfile": "^5.0.2", "js-tiktoken": "^1.0.19", "mammoth": "^1.8.0", "monaco-editor": "^0.52.2", "monaco-editor-textmate": "^4.0.0", "monaco-textmate": "^3.0.1", "monaco-vscode-textmate-theme-converter": "^0.1.7", "node-ipc": "^12.0.0", "onigasm": "^2.2.5", "openai": "^4.78.1", "os-name": "^6.0.0", "p-wait-for": "^5.0.2", "pdf-parse": "^1.1.1", "pkce-challenge": "^4.1.0", "posthog-node": "^4.7.0", "pretty-bytes": "^6.1.1", "puppeteer-chromium-resolver": "^23.0.0", "puppeteer-core": "^23.4.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.1.0", "react-tooltip": "^5.28.1", "reconnecting-eventsource": "^1.6.4", "say": "^0.16.0", "serialize-error": "^11.0.3", "simple-git": "^3.27.0", "sound-play": "^1.1.0", "string-similarity": "^4.0.4", "strip-ansi": "^7.1.0", "strip-bom": "^5.0.0", "tmp": "^0.2.3", "tree-sitter-wasms": "^0.1.11", "turndown": "^7.2.0", "uuid": "^11.1.0", "web-tree-sitter": "^0.22.6", "zod": "^3.23.8"}, "devDependencies": {"@changesets/cli": "^2.27.10", "@changesets/types": "^6.0.0", "@dotenvx/dotenvx": "^1.34.0", "@types/debug": "^4.1.12", "@types/diff": "^5.2.1", "@types/diff-match-patch": "^1.0.36", "@types/glob": "^8.1.0", "@types/jest": "^29.5.14", "@types/node": "20.x", "@types/node-ipc": "^9.2.3", "@types/react": "^19.1.1", "@types/react-dom": "^19.1.2", "@types/string-similarity": "^4.0.2", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitejs/plugin-react-swc": "^3.8.1", "@vscode/vsce": "^3.3.2", "esbuild": "^0.24.0", "eslint": "^8.57.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "execa": "^9.5.2", "glob": "^11.0.1", "husky": "^9.1.7", "jest": "^29.7.0", "jest-simple-dot-reporter": "^1.0.5", "knip": "^5.44.4", "lint-staged": "^15.2.11", "mkdirp": "^3.0.1", "npm-run-all": "^4.1.5", "prettier": "^3.4.2", "rimraf": "^6.0.1", "sass": "^1.86.3", "ts-jest": "^29.2.5", "tsup": "^8.4.0", "tsx": "^4.19.3", "typescript": "^5.8.3", "vite": "^6.2.6", "zod-to-ts": "^1.2.0"}, "lint-staged": {"*.{js,jsx,ts,tsx,json,css,md}": ["prettier --write"], "src/**/*.{ts,tsx}": ["npx eslint -c .eslintrc.json --max-warnings=0 --fix"], "webview-ui/**/*.{ts,tsx}": ["npx eslint -c webview-ui/.eslintrc.json --max-warnings=0 --fix"]}}