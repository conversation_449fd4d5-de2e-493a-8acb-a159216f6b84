// See https://go.microsoft.com/fwlink/?LinkId=733558
// for the documentation about the tasks.json format
{
	"version": "2.0.0",
	"tasks": [
		{
			"label": "watch",
			"dependsOn": ["npm: dev", "npm: watch:tsc", "npm: watch:esbuild"],
			"presentation": {
				"reveal": "never"
			},
			"group": {
				"kind": "build",
				"isDefault": true
			}
		},
		{
			"label": "npm: dev",
			"type": "npm",
			"script": "dev",
			"group": "build",
			"problemMatcher": {
				"owner": "vite",
				"pattern": {
					"regexp": "^$"
				},
				"background": {
					"activeOnStart": true,
					"beginsPattern": ".*VITE.*",
					"endsPattern": ".*Local:.*"
				}
			},
			"isBackground": true,
			"presentation": {
				"group": "webview-ui",
				"reveal": "always"
			}
		},
		{
			"label": "npm: watch:esbuild",
			"type": "npm",
			"script": "watch:esbuild",
			"group": "build",
			"problemMatcher": {
				"owner": "esbuild",
				"pattern": {
					"regexp": "^✘ \\[ERROR\\] (.+)$\\s+(.+):(\\d+):(\\d+):$",
					"file": 2,
					"line": 3,
					"column": 4,
					"message": 1
				},
				"background": {
					"activeOnStart": true,
					"beginsPattern": "\\[watch\\] build started",
					"endsPattern": "\\[watch\\] build finished"
				}
			},
			"isBackground": true,
			"presentation": {
				"group": "watch",
				"reveal": "always"
			}
		},
		{
			"label": "npm: watch:tsc",
			"type": "npm",
			"script": "watch:tsc",
			"group": "build",
			"problemMatcher": "$tsc-watch",
			"isBackground": true,
			"presentation": {
				"group": "watch",
				"reveal": "always"
			}
		}
	]
}
