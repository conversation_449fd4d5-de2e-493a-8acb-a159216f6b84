{
  "configurations": [
    {
      "name": "Run in Current Window",
      "type": "extensionHost",
      "request": "launch",
      "runtimeExecutable": "${execPath}",
      "args": [
        "--extensionDevelopmentPath=${workspaceFolder}",
        "--disable-extension-debugging",
        "${workspaceFolder}"  // This makes it open in the current folder
      ],
      "sourceMaps": true,
      "outFiles": ["${workspaceFolder}/dist/**/*.js"],
      "preLaunchTask": "${defaultBuildTask}",
      "env": {
        "NODE_ENV": "development",
        "VSCODE_DEBUG_MODE": "true"
      }
    }
  ]
}
