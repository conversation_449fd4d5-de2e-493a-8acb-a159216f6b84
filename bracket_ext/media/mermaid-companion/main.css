:root {
    --container-padding: 10px;
    --input-padding-vertical: 6px;
    --input-padding-horizontal: 4px;
    --input-margin-vertical: 4px;
    --input-margin-horizontal: 0;
    --primary-color: #a8c6e5;      /* Softer pastel blue */
    --secondary-color: #c2d8ee;    /* Lighter pastel blue */
    --accent-color: #e6a8a8;       /* Soft pastel red */
    --success-color: #b8e0c9;      /* Soft pastel green */
    --warning-color: #e6d292;      /* Soft pastel yellow */
    --error-color: #e6a8a8;        /* Soft pastel red */
    --border-radius: 6px;
    --button-radius: 4px;
    --transition-speed: 0.2s;
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.08), 0 1px 1px rgba(0, 0, 0, 0.12);  /* Softer shadow */
    --shadow-md: 0 2px 4px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.12);   /* Softer shadow */
    --shadow-lg: 0 6px 12px rgba(0, 0, 0, 0.1), 0 4px 4px rgba(0, 0, 0, 0.12);  /* Softer shadow */
    --header-height: 40px;
    --toolbar-height: 48px;        /* Increased toolbar height */
    --status-bar-height: 24px;
    --info-panel-height: 80px;
}

body {
    padding: 0;
    margin: 0;
    color: var(--vscode-foreground);
    font-size: var(--vscode-font-size);
    font-weight: var(--vscode-font-weight);
    font-family: var(--vscode-font-family);
    background-color: var(--vscode-editor-background);
    overflow: hidden;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    box-sizing: border-box;
    overflow: hidden;
}

/* Header styling */
.header {
    height: var(--header-height);
    background-color: var(--vscode-editor-background);
    border-bottom: 1px solid var(--vscode-panel-border);
    display: flex;
    align-items: center;
    padding: 0 var(--container-padding);
    user-select: none;
    font-family: var(--vscode-font-family);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    position: relative;
    z-index: 10;
}

.title-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.title {
    font-weight: 500;
    font-size: 13px;
    color: var(--vscode-panelTitle-activeForeground);
    display: flex;
    align-items: center;
    letter-spacing: 0.1px;
}

.title::before {
    content: '';
    display: inline-block;
    width: 14px;
    height: 14px;
    margin-right: 6px;
    background-color: var(--vscode-activityBarBadge-background, #007acc);
    mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M3 3h18v18H3V3zm16 16V5H5v14h14zM7 7h2v2H7V7zm0 4h2v2H7v-2zm0 4h2v2H7v-2zm4-8h6v2h-6V7zm0 4h6v2h-6v-2zm0 4h6v2h-6v-2z'/%3E%3C/svg%3E");
    mask-size: cover;
    opacity: 0.9;
}

.actions {
    display: flex;
    gap: 2px;
    align-items: center;
}

/* Info panel styling */
.info-panel {
    background-color: var(--vscode-editor-inactiveSelectionBackground);
    padding: 6px var(--container-padding);
    border-bottom: 1px solid var(--vscode-panel-border);
    display: flex;
    flex-direction: column;
    gap: 2px;
    height: auto;
    min-height: 36px;
    box-sizing: border-box;
}

.domain-section {
    display: none; /* Hide domain section for cleaner UI */
}

.function-section {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.label {
    font-size: 10px;
    opacity: 0.6;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-family: var(--vscode-font-family);
}

.value {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.3;
    font-family: var(--vscode-font-family);
    font-size: 12px;
}

#domain-name {
    font-weight: 500;
    color: var(--vscode-activityBarBadge-background, #007acc);
    font-size: 12px;
}

#function-path {
    font-family: var(--vscode-editor-font-family, 'Courier New', monospace);
    font-size: 12px;
    color: var(--vscode-editor-foreground);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    background-color: var(--vscode-editor-inactiveSelectionBackground, rgba(128, 128, 128, 0.05));
    border-radius: 3px;
    padding: 4px 8px;
    border-left: 2px solid var(--vscode-activityBarBadge-background, #007acc);
}

/* Toolbar styling */
.toolbar {
    display: flex; /* Restore toolbar display for breadcrumb navigation */
    align-items: center;
    padding: 0 var(--container-padding);
    height: var(--toolbar-height);
    background-color: var(--vscode-editor-inactiveSelectionBackground, #f0f0f0);
    border-bottom: 1px solid var(--vscode-panel-border, #e0e0e0);
    gap: 6px; /* Increased gap */
    flex-wrap: wrap;
    font-family: var(--vscode-font-family);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    position: relative;
    z-index: 5;
    padding-top: 6px; /* Increased padding */
    padding-bottom: 6px; /* Increased padding */
}

.toolbar-group {
    display: flex;
    gap: 4px; /* Increased gap */
    align-items: center;
    padding: 0 6px; /* Increased padding */
}

.toolbar-group:last-child {
    margin-left: auto;
}

/* Hierarchy controls styling */
#hierarchy-controls {
    display: flex; /* Always show the container, the content will be populated when available */
    align-items: center;
    gap: 6px; /* Increased gap */
    flex-grow: 1;
    overflow-x: auto;
    margin: 0 8px;
    scrollbar-width: thin;
    padding: 4px 0; /* Increased padding */
}

/* Breadcrumb container */
.breadcrumb-container {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    overflow-x: auto;
    max-width: 100%;
    padding: 6px 10px; /* Increased padding */
    scrollbar-width: thin;
    background-color: var(--vscode-editor-background);
    border: 1px solid var(--vscode-panel-border);
    border-radius: 4px; /* Slightly more rounded */
    margin: 0 4px;
    box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.05);
    flex-grow: 1; /* Take up available space */
    min-height: 32px; /* Increased minimum height */
}

.breadcrumb-container::-webkit-scrollbar {
    height: 4px;
}

.breadcrumb-container::-webkit-scrollbar-thumb {
    background-color: var(--vscode-scrollbarSlider-background);
    border-radius: 4px;
}

.breadcrumb-container::-webkit-scrollbar-track {
    background-color: var(--vscode-scrollbarSlider-hoverBackground);
    border-radius: 4px;
}

/* Enhanced breadcrumb buttons */
#breadcrumb-container button {
    background: transparent;
    border: none;
    padding: 5px 12px; /* Increased padding */
    border-radius: 4px; /* Slightly more rounded */
    cursor: pointer;
    font-size: 13px; /* Larger font size */
    color: var(--vscode-editor-foreground);
    font-weight: normal;
    text-decoration: none;
    transition: all 0.15s ease;
    display: flex;
    align-items: center;
    gap: 6px; /* Increased gap */
    white-space: nowrap;
    outline: none;
    margin: 0 2px; /* Increased margin */
    font-family: var(--vscode-font-family);
    height: 24px; /* Set explicit height */
}

#breadcrumb-container button:hover {
    background-color: var(--vscode-list-hoverBackground);
}

#breadcrumb-container button:active {
    background-color: var(--vscode-list-activeSelectionBackground);
    color: var(--vscode-list-activeSelectionForeground);
}

/* Breadcrumb separator */
.breadcrumb-separator {
    margin: 0 3px; /* Increased margin */
    color: var(--vscode-descriptionForeground);
    user-select: none;
    opacity: 0.5;
    font-size: 12px; /* Larger font size */
    font-family: var(--vscode-font-family);
}

/* Codicon specific styling */
.codicon {
    font-family: 'codicon' !important;
    font-style: normal;
    font-weight: normal;
    display: inline-block;
    text-align: center;
    vertical-align: text-bottom;
    line-height: 1;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Text button styling */
.text-button {
    background-color: var(--vscode-button-secondaryBackground, #f3f3f3);
    color: var(--vscode-button-secondaryForeground, #333333);
    border: 1px solid var(--vscode-button-border, #d4d4d4);
    border-radius: 3px;
    cursor: pointer;
    padding: 3px 8px;
    margin: 2px;
    font-size: 12px;
    font-family: var(--vscode-font-family);
    transition: all var(--transition-speed) ease;
    min-height: 22px;
    min-width: 22px;
    text-align: center;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
    font-weight: 400;
    line-height: 1.2;
}

.text-button:hover {
    background-color: var(--vscode-button-secondaryHoverBackground, #e6e6e6);
    border-color: var(--vscode-button-border, #c8c8c8);
}

.text-button:active {
    background-color: var(--vscode-button-secondaryHoverBackground, #d9d9d9);
    color: var(--vscode-button-secondaryForeground, #333333);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.1);
}

/* Button styling */
.icon-button {
    background-color: transparent;
    color: var(--vscode-editor-foreground);
    border: none;
    border-radius: 4px; /* Slightly more rounded */
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 5px; /* Increased padding */
    min-width: 30px; /* Slightly larger */
    min-height: 30px; /* Slightly larger */
    transition: all var(--transition-speed) ease;
    position: relative;
    overflow: hidden;
    font-family: var(--vscode-font-family);
    margin: 2px;
}

.icon-button:hover {
    background-color: var(--vscode-list-hoverBackground);
}

.icon-button:active {
    background-color: var(--vscode-list-activeSelectionBackground);
    color: var(--vscode-list-activeSelectionForeground);
}

.icon-button i {
    font-size: 20px; /* Larger icon */
    position: relative;
    z-index: 1;
    opacity: 0.9;
    font-family: 'codicon' !important;
    font-style: normal;
    font-weight: normal;
    display: inline-block;
    text-align: center;
    vertical-align: middle;
    line-height: 1;
    width: 20px; /* Larger icon */
    height: 20px; /* Larger icon */
}

.icon-button span {
    margin-left: 5px; /* Increased margin */
    position: relative;
    z-index: 1;
    font-size: 13px; /* Larger font */
}

.icon-button.primary {
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    padding: 4px 8px;
    font-weight: 500;
}

.icon-button.primary:hover {
    background-color: var(--vscode-button-hoverBackground);
}

.icon-button:disabled {
    opacity: 0.4;
    cursor: not-allowed;
}

.icon-button:disabled:hover {
    background-color: transparent;
}

/* Main content area */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
    position: relative;
}

/* Diagram container styling */
.diagram-container {
    flex: 1;
    overflow: visible; /* Allow content to be visible */
    background-color: var(--vscode-editor-background);
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.08); /* Softer border */
    border-radius: 6px; /* Slightly more rounded corners */
    margin: 0 var(--container-padding);
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.02); /* Softer inner shadow */
    padding: 20px; /* Added padding around the diagram */
}

#diagram {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: visible; /* Allow text to be visible */
}

/* SVG styling for better centering - removed transitions */
#diagram svg {
    min-width: 50%;
    min-height: 50%;
    max-width: 90%; /* Reduced from 95% to allow more padding */
    max-height: 90%; /* Reduced from 95% to allow more padding */
    display: block;
    margin: auto;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(2.0); /* Increased from 1.5 to 2.0 for better readability */
    transform-origin: center;
    filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.1));
    /* Removed transition to prevent blur effect */
    border-radius: 4px;
    overflow: visible !important; /* Ensure text isn't clipped */
    padding: 10px; /* Added padding around the SVG */
}

/* Add a very subtle grid background */
.diagram-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: linear-gradient(rgba(0, 0, 0, 0.03) 1px, transparent 1px),
                      linear-gradient(90deg, rgba(0, 0, 0, 0.03) 1px, transparent 1px);
    background-size: 25px 25px; /* Slightly larger grid */
    opacity: 0.1; /* More subtle */
    pointer-events: none;
    z-index: 0;
}

/* Diagram controls */
.diagram-controls {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background-color: var(--vscode-editorWidget-background);
    border: 1px solid var(--vscode-widget-border);
    border-radius: 3px;
    padding: 3px 6px;
    font-size: 11px;
    display: flex;
    align-items: center;
    box-shadow: var(--shadow-sm);
    z-index: 10;
    color: var(--vscode-editorWidget-foreground);
    font-family: var(--vscode-font-family);
    opacity: 0.9;
}

.zoom-level {
    font-family: var(--vscode-editor-font-family, 'Courier New', monospace);
    font-weight: 500;
    font-size: 11px;
    min-width: 40px;
    text-align: center;
}

/* Minimap styling */
.minimap {
    width: 180px;
    background-color: var(--vscode-editor-background);
    border-left: 1px solid var(--vscode-panel-border);
    position: relative;
    overflow: hidden;
    display: none;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.05);
}

.minimap-viewport {
    position: absolute;
    border: 1px solid var(--vscode-activityBarBadge-background, #007acc);
    background-color: rgba(0, 122, 204, 0.05);
    cursor: move;
    border-radius: 2px;
}

/* Customize mermaid diagram appearance with subtle styling */
#diagram .node rect, #diagram .node circle, #diagram .node polygon, #diagram .node path {
    stroke-width: 1px; /* Thinner stroke */
    /* Removed transition to prevent blur effect */
    rx: 5px; /* More rounded corners */
    ry: 5px; /* More rounded corners */
    padding: 25px; /* Increased padding from 15px to 25px */
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.05)); /* Very subtle shadow */
    margin: 10px; /* Added margin around nodes */
}

/* Ensure node text is fully visible */
#diagram .node text {
    white-space: normal !important; /* Allow text wrapping */
    overflow: visible !important;
    font-size: 14px !important; /* Reduced size to fit better */
    font-weight: 500 !important;
    max-width: none !important;
    width: auto !important;
    text-overflow: visible !important;
    word-wrap: break-word !important; /* Allow long words to break */
    text-anchor: middle !important; /* Center text in nodes */
}

/* Removed hover effect to prevent blur and animation */
#diagram .node:hover rect, #diagram .node:hover circle, #diagram .node:hover polygon, #diagram .node:hover path {
    /* No hover effects */
}

#diagram .edgePath path {
    stroke-width: 1.2px; /* Slightly thinner */
    /* Removed transition to prevent blur effect */
    opacity: 0.7; /* More subtle */
}

/* Removed hover effect to prevent blur and animation */
#diagram .edgePath:hover path {
    /* No hover effects */
}

#diagram .edgeLabel {
    background-color: var(--vscode-editor-background, #ffffff) !important;
    border-radius: 4px; /* Slightly more rounded */
    padding: 6px 12px !important; /* Increased padding */
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.03); /* More subtle shadow */
    font-size: 12px !important;
    font-family: var(--vscode-font-family) !important;
    color: var(--vscode-editor-foreground, #000000) !important;
    border: 1px solid rgba(0, 0, 0, 0.08) !important; /* Softer border */
    white-space: nowrap !important;
    overflow: visible !important;
    max-width: none !important;
    width: auto !important;
    margin: 5px !important; /* Added margin */
}

/* Ensure edge label text is fully visible */
#diagram .edgeLabel text {
    white-space: nowrap !important;
    overflow: visible !important;
    font-size: 20px !important; /* Increased from 16px to 20px */
    color: var(--vscode-editor-foreground, #000000) !important;
    font-weight: 500 !important;
    max-width: none !important;
    width: auto !important;
    text-overflow: initial !important;
    fill: var(--vscode-editor-foreground) !important;
}

/* #diagram .label {
    font-weight: 400;
    font-family: var(--vscode-font-family) !important;
    font-size: 12px !important;
    white-space: nowrap !important;
    overflow: visible !important;
} */

/* Revert uppercase & clipping inside the SVG container */

#diagram .label {
    text-transform: none     !important;
    font-weight: 500         !important;
    overflow: visible        !important;
    white-space: normal      !important;
    font-family: var(--vscode-font-family) !important;
    font-size: 14px          !important; /* Reduced from 18px to 14px */
    letter-spacing: 0.3px    !important;
    word-wrap: break-word    !important; /* Allow text wrapping */
    text-anchor: middle      !important; /* Center text */
    /* line-height: 1.4         !important; */
    /* padding: 2px 4px         !important; */
  }

/* Ensure all text elements in the diagram are visible */
#diagram text {
    font-family: var(--vscode-font-family) !important;
    overflow: visible !important;
    white-space: normal !important;
    word-wrap: break-word !important;
}

/* Specific styling for different text types */
#diagram .nodeLabel text,
#diagram .cluster-label text,
#diagram .titleText text {
    font-weight: 500 !important;
    overflow: visible !important;
    white-space: normal !important;
    word-wrap: break-word !important;
    text-anchor: middle !important;
}

/* Improve cluster appearance with subtle styling */
#diagram .cluster rect {
    stroke-width: 1px; /* Thinner stroke */
    rx: 6px; /* More rounded corners */
    ry: 6px; /* More rounded corners */
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.04)); /* More subtle shadow */
    opacity: 0.8; /* Slightly more transparent */
    padding: 15px;
}

/* Node highlight effect - static version without animation - more subtle */
.node-highlight {
    stroke-width: 2px; /* Thinner highlight */
    opacity: 0.8; /* More subtle */
    filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.1)); /* Subtle glow */
}

/* Edge highlight effect - static version without animation - more subtle */
.edge-highlight {
    stroke-width: 2px; /* Thinner highlight */
    opacity: 0.8; /* More subtle */
}

/* Message styling - removed animations */
.message {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    padding: 8px 12px;
    background-color: var(--vscode-editorWidget-background);
    color: var(--vscode-editorWidget-foreground);
    border: 1px solid var(--vscode-widget-border);
    border-radius: 3px;
    display: none;
    box-shadow: var(--shadow-sm);
    /* Removed animations */
    z-index: 100;
    max-width: 80%;
    text-align: center;
    font-size: 12px;
    font-family: var(--vscode-font-family);
}

.message.error {
    background-color: var(--vscode-inputValidation-errorBackground, rgba(241, 76, 76, 0.1));
    color: var(--vscode-inputValidation-errorForeground, #f14c4c);
    border-color: var(--vscode-inputValidation-errorBorder, #f14c4c);
}

/* Status bar */
.status-bar {
    height: var(--status-bar-height);
    background-color: var(--vscode-statusBar-background);
    color: var(--vscode-statusBar-foreground);
    display: flex;
    align-items: center;
    padding: 0 var(--container-padding);
    font-size: 11px;
    border-top: 1px solid var(--vscode-panel-border);
    font-family: var(--vscode-font-family);
    box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.05);
}

.status-item {
    display: flex;
    align-items: center;
    margin-right: 10px;
    gap: 3px;
    font-size: 11px;
    opacity: 0.85;
}

.status-item:last-child {
    margin-right: 0;
    margin-left: auto;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Loading indicator */
.loading {
    display: none;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 50;
    background-color: var(--vscode-editorWidget-background);
    border: 1px solid var(--vscode-widget-border);
    padding: 12px;
    border-radius: 3px;
    box-shadow: var(--shadow-sm);
    color: var(--vscode-editorWidget-foreground);
    font-family: var(--vscode-font-family);
    font-size: 12px;
    animation: fadeIn 0.2s ease;
}

.loading-spinner {
    border: 2px solid rgba(128, 128, 128, 0.1);
    border-radius: 50%;
    border-top: 2px solid var(--vscode-progressBar-background);
    width: 20px;
    height: 20px;
    margin: 0 auto 6px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Fullscreen mode */
.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    background-color: var(--vscode-editor-background);
}

.fullscreen .header,
.fullscreen .info-panel,
.fullscreen .status-bar {
    display: none;
}

.fullscreen .toolbar {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;
    background-color: var(--vscode-editorWidget-background);
    border-bottom: 1px solid var(--vscode-widget-border);
    border-top: none;
    border-left: none;
    border-right: none;
}

.fullscreen .main-content {
    height: 100vh;
}

.fullscreen #toggleFullscreen i {
    font-family: codicon;
    content: "\ea74";
}

.fullscreen #toggleFullscreen i::before {
    content: "\ea73";
}

/* Responsive adjustments */
@media (max-height: 500px) {
    :root {
        --header-height: 36px;
        --toolbar-height: 36px;
        --status-bar-height: 22px;
        --info-panel-height: 70px;
    }

    .container {
        gap: 0;
    }

    .info-panel {
        padding: 6px var(--container-padding);
    }
}
