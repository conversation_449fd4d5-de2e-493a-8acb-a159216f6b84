/* Enhanced toolbar and breadcrumb styling */

/* Toolbar styling - make it more prominent for breadcrumbs */
.toolbar {
    display: flex;
    align-items: center;
    padding: 8px var(--container-padding);
    height: auto;
    min-height: var(--toolbar-height);
    background-color: var(--vscode-editor-inactiveSelectionBackground);
    border-bottom: 1px solid var(--vscode-panel-border);
    position: relative;
    z-index: 5;
}

/* Hierarchy controls styling - full width */
#hierarchy-controls {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0;
}

/* Enhanced breadcrumb container */
.breadcrumb-container {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    overflow-x: auto;
    width: 100%;
    padding: 6px 10px;
    scrollbar-width: thin;
    background-color: var(--vscode-editor-background);
    border: 1px solid var(--vscode-panel-border);
    border-radius: 4px;
    box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.05);
    min-height: 28px; /* Ensure minimum height even when empty */
}

/* Improved scrollbar for breadcrumb container */
.breadcrumb-container::-webkit-scrollbar {
    height: 4px;
}

.breadcrumb-container::-webkit-scrollbar-thumb {
    background-color: var(--vscode-scrollbarSlider-background);
    border-radius: 4px;
}

.breadcrumb-container::-webkit-scrollbar-track {
    background-color: transparent;
}

/* Enhanced breadcrumb buttons */
#breadcrumb-container button {
    background: transparent;
    border: none;
    padding: 4px 10px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    color: var(--vscode-editor-foreground);
    font-weight: normal;
    text-decoration: none;
    transition: all 0.15s ease;
    display: flex;
    align-items: center;
    gap: 4px;
    white-space: nowrap;
    outline: none;
    margin: 0 2px;
    font-family: var(--vscode-font-family);
    height: 24px;
}

#breadcrumb-container button:hover {
    background-color: var(--vscode-list-hoverBackground);
}

#breadcrumb-container button:active {
    background-color: var(--vscode-list-activeSelectionBackground);
    color: var(--vscode-list-activeSelectionForeground);
}

/* Breadcrumb separator */
.breadcrumb-separator {
    margin: 0 2px;
    color: var(--vscode-descriptionForeground);
    user-select: none;
    opacity: 0.7;
    font-size: 12px;
    font-family: var(--vscode-font-family);
}

/* Active breadcrumb item - more visible */
#breadcrumb-container button.active {
    background-color: var(--vscode-list-activeSelectionBackground);
    color: var(--vscode-list-activeSelectionForeground);
    font-weight: 500;
    box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);
}
