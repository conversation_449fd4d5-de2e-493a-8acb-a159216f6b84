// @ts-check

(function () {
    // Get a reference to the VS Code webview API
    const vscode = acquireVsCodeApi();

    // Initialize mermaid with enhanced styling using subtle pastel colors
    mermaid.initialize({
        startOnLoad: false,
        securityLevel: 'loose',
        theme: 'base',
        themeVariables: {
            // Background
            background: '#ffffff',
            primaryColor: '#d1e6fa',  // Lighter pastel blue
            primaryTextColor: '#333333',
            primaryBorderColor: '#a8c6e5',  // Softer blue border

            // Secondary colors
            secondaryColor: '#e6f0fa',  // Very light blue
            secondaryTextColor: '#333333',
            secondaryBorderColor: '#c2d8ee',  // Soft blue border

            // Tertiary colors
            tertiaryColor: '#f5f9fe',  // Extremely light blue
            tertiaryTextColor: '#333333',
            tertiaryBorderColor: '#d1e6fa',  // Light blue border

            // Additional styling
            noteTextColor: '#333333',
            noteBkgColor: '#fff8e1',  // Softer yellow
            noteBorderColor: '#e6d292',  // Muted gold

            // Improve contrast for special elements
            critBorderColor: '#e6a8a8',  // Soft red border
            critBkgColor: '#fdf0f0',  // Very light red

            // Task diagram specific
            taskTextColor: '#333333',
            taskTextOutsideColor: '#333333',
            taskTextLightColor: '#333333',

            // Numbers/sections
            sectionBkgColor: '#f5f9fe',  // Very light blue
            sectionBkgColor2: '#edf5fc',  // Slightly darker light blue

            // Alt sections in sequence diagrams
            altBackground: '#f5f9fe',  // Very light blue

            // Links
            linkColor: '#7aadde',  // Softer blue for links

            // Borders and lines
            compositeBackground: '#f5f9fe',  // Very light blue
            compositeBorder: '#c2d8ee',  // Soft blue border
            titleColor: '#333333',

            // Font settings
            fontSize: '20px', // Increased from 16px to 20px for better readability
            fontFamily: 'var(--vscode-font-family, "Segoe UI", Tahoma, Geneva, Verdana, sans-serif)',

            // Enhanced styling
            nodeBorder: '#a8c6e5',  // Softer blue border
            clusterBkg: 'rgba(168, 198, 229, 0.1)',  // Very light blue background
            clusterBorder: '#a8c6e5',  // Softer blue border
            edgeLabelBackground: 'rgba(255, 255, 255, 0.95)',  // More opaque for better visibility
            lineColor: '#a8c6e5',  // Softer blue for lines
        },
        flowchart: {
            htmlLabels: true, // Enable HTML labels for better text handling
            curve: 'basis',
            useMaxWidth: false, // Don't constrain width
            diagramPadding: 30, // Increased diagram padding
            padding: 40, // Increased padding
            nodeSpacing: 60, // Increased node spacing
            rankSpacing: 80, // Increased rank spacing
            messageMargin: 45, // Increased message margin
            wrap: true, // Enable text wrapping
            maxTextSize: 90000, // Allow large text
        },
        sequence: {
            mirrorActors: false,
            bottomMarginAdj: 20, // Increased margin
            useMaxWidth: false,
            boxMargin: 20, // Increased margin
            noteMargin: 20, // Increased margin
            messageMargin: 45, // Increased margin
            messageAlign: 'center',
            wrap: true, // Enable text wrapping
        },
        graph: {
            mirrorActors: false,
            bottomMarginAdj: 20, // Increased margin
            useMaxWidth: false,
            boxMargin: 20, // Increased margin
            noteMargin: 20, // Increased margin
            messageMargin: 45, // Increased margin
            messageAlign: 'center',
            wrap: true, // Enable text wrapping
        },
        er: {
            useMaxWidth: false,
            wrap: true, // Enable text wrapping
        },
    });

    // DOM elements
    const container = document.querySelector('.container');
    const diagramContainer = document.getElementById('diagram');
    const messageElement = document.getElementById('message');
    const domainNameElement = document.getElementById('domain-name');
    const functionPathElement = document.getElementById('function-path');
    const zoomInButton = document.getElementById('zoomIn');
    const zoomOutButton = document.getElementById('zoomOut');
    const resetZoomButton = document.getElementById('resetZoom');
    const exportPngButton = document.getElementById('exportPng');
    const loadingElement = document.querySelector('.loading');
    const zoomLevelElement = document.querySelector('.zoom-level');
    const minimapElement = document.getElementById('minimap');
    const minimapViewportElement = document.querySelector('.minimap-viewport');
    const toggleMinimapButton = document.getElementById('toggleMinimap');
    const toggleFullscreenButton = document.getElementById('toggleFullscreen');
    const refreshDiagramButton = document.getElementById('refreshDiagram');
    const toggleCompanionButton = document.getElementById('toggleCompanion');
    const diagramStatusElement = document.getElementById('diagram-status');
    const diagramSizeElement = document.getElementById('diagram-size');
    const hierarchyControlsElement = document.getElementById('hierarchy-controls');
    const hierarchyLevelSelect = document.getElementById('hierarchyLevel');

    // State
    let currentScale = 7;
    let currentMermaidCode = '';
    let previousMermaidCode = ''; // Store previous diagram code for comparison
    let panMode = false;
    let lastX = 0;
    let lastY = 0;
    let offsetX = 0;
    let offsetY = 0;
    let minimapVisible = false;
    let isFullscreen = false;
    let diagramHistory = [];
    let currentHistoryIndex = -1;
    let currentDomainName = '';
    let currentFunctionPath = '';
    let currentFullPath = '';
    let currentFunctionName = '';
    let initialRendering = true; // Flag to track initial rendering

    // Hierarchical diagram state
    let hierarchicalDiagrams = [];
    let currentHierarchyLevel = 0;
    let previousHierarchyLevel = 0; // Track the previous hierarchy level
    let totalHierarchyLevels = 1;
    let isHierarchicalView = false;

    // Variables for dragging
    let isDragging = false;
    let dragStartX = 0;
    let dragStartY = 0;
    let translateX = 0;
    let translateY = 0;

    // Variables to preserve view state
    let lastViewState = {
        scale: 2.0, // Increased from 1.5 to 2.0 for better readability
        translateX: 0,
        translateY: 0
    };

    // Initialize
    document.addEventListener('DOMContentLoaded', () => {
        // Tell VS Code we're ready to receive messages
        vscode.postMessage({ type: 'ready' });
        updateZoomLevelDisplay();
        updateDiagramStatus('Ready');
    });

    // Add wheel event for zooming
    diagramContainer.addEventListener('wheel', function(e) {
        if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            if (e.deltaY < 0) {
                zoomIn();
            } else {
                zoomOut();
            }
        }
    });

    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Global shortcuts
        if (e.key === 'Escape' && isFullscreen) {
            e.preventDefault();
            toggleFullscreen();
            return;
        }

        // Only handle other keyboard shortcuts when the diagram container is focused
        if (document.activeElement === diagramContainer ||
            diagramContainer.contains(document.activeElement) ||
            document.activeElement === document.body) {

            // Ctrl/Cmd + Plus: Zoom in
            if ((e.ctrlKey || e.metaKey) && (e.key === '+' || e.key === '=')) {
                e.preventDefault();
                zoomIn();
            }

            // Ctrl/Cmd + Minus: Zoom out
            if ((e.ctrlKey || e.metaKey) && e.key === '-') {
                e.preventDefault();
                zoomOut();
            }

            // Ctrl/Cmd + 0: Reset zoom
            if ((e.ctrlKey || e.metaKey) && e.key === '0') {
                e.preventDefault();
                resetZoom();
            }

            // Space: Toggle pan mode
            if (e.key === ' ' && !e.ctrlKey && !e.metaKey) {
                e.preventDefault();
                panMode = !panMode;
                diagramContainer.style.cursor = panMode ? 'grab' : 'default';
                updateDiagramStatus(panMode ? 'Pan mode active' : 'Ready');
            }

            // F for fullscreen
            if (e.key === 'f' && !e.ctrlKey && !e.metaKey) {
                e.preventDefault();
                toggleFullscreen();
            }

            // M for minimap
            if (e.key === 'm' && !e.ctrlKey && !e.metaKey) {
                e.preventDefault();
                toggleMinimap();
            }
        }
    });

    // Enhanced color palette for different levels with subtle pastel colors
    const LEVEL_COLORS = [
        '168, 198, 229', // Level 0 - Root (pastel blue)
        '230, 179, 179', // Level 1 (pastel red)
        '230, 217, 179', // Level 2 (pastel yellow)
        '179, 230, 199', // Level 3 (pastel green)
        '204, 179, 230', // Level 4 (pastel purple)
        '230, 179, 213', // Level 5 (pastel pink)
        '179, 217, 230', // Level 6 (pastel light blue)
        '230, 204, 179'  // Level 7 (pastel orange)
    ];

    /**
     * Get color for a specific level with optional opacity
     * Enhanced to provide subtle pastel colors for better visual hierarchy
     */
    const getColorForLevel = (level, opacity = 0.3) => {
        // Use modulo to cycle through colors for very deep hierarchies
        const colorIndex = level % LEVEL_COLORS.length;
        return `${LEVEL_COLORS[colorIndex]}, ${opacity}`;
    };

    /**
     * Get a gradient background for a breadcrumb item based on its level
     */
    const getGradientForLevel = (level) => {
        const baseColor = LEVEL_COLORS[level % LEVEL_COLORS.length];
        return `linear-gradient(to right, rgba(${baseColor}, 0.3), rgba(${baseColor}, 0.1))`;
    };

    // Initialize breadcrumb navigation with enhanced UI
    function initializeBreadcrumbs(hierarchyPath) {
        const breadcrumbContainer = document.getElementById('breadcrumb-container');

        // Clear existing breadcrumbs
        if (breadcrumbContainer) {
            breadcrumbContainer.innerHTML = '';
        }

        // Only show breadcrumbs if we have hierarchical diagrams
        if (!isHierarchicalView || totalHierarchyLevels <= 1 || !hierarchyControlsElement) {
            if (hierarchyControlsElement) {
                hierarchyControlsElement.style.display = 'none';
            }
            return;
        }

        // Show the hierarchy controls
        hierarchyControlsElement.style.display = 'flex';

        // Use the provided hierarchy path or get it from the first diagram
        hierarchyPath = hierarchyPath || hierarchicalDiagrams[0]?.fullPath;
        if (!hierarchyPath) {
            return;
        }

        // Parse the full path into parts
        const pathParts = hierarchyPath.split(' -> ');

        // Create breadcrumb items for each part of the path
        for (let i = 0; i < pathParts.length; i++) {
            // Find the corresponding diagram for this level
            // We need to match by name since the levels might not align perfectly
            const part = pathParts[i];
            const matchingDiagramIndex = findDiagramIndexByName(part);

            // Add separator if not the first item
            if (i > 0) {
                const separator = document.createElement('span');
                separator.className = 'breadcrumb-separator';
                separator.textContent = '/';
                breadcrumbContainer.appendChild(separator);
            }

            // Create the breadcrumb item as a button for better accessibility
            const breadcrumbItem = document.createElement('button');
            breadcrumbItem.dataset.level = matchingDiagramIndex;
            breadcrumbItem.dataset.name = part;
            breadcrumbItem.className = 'breadcrumb-item';

            // Apply subtle pastel color based on level
            const levelColor = LEVEL_COLORS[matchingDiagramIndex % LEVEL_COLORS.length];
            breadcrumbItem.style.background = getGradientForLevel(matchingDiagramIndex);
            breadcrumbItem.style.border = `1px solid rgba(${levelColor}, 0.4)`;

            // Determine if this is the current/active item
            const isActive = matchingDiagramIndex === currentHierarchyLevel;

            // Apply different styles based on whether this is the active item
            if (isActive) {
                // Active item styling with subtle appearance
                breadcrumbItem.style.backgroundColor = `rgba(${levelColor}, 0.35)`;
                breadcrumbItem.style.color = 'var(--vscode-list-activeSelectionForeground)';
                breadcrumbItem.style.fontWeight = '500';
                breadcrumbItem.style.boxShadow = '0 0 3px rgba(0, 0, 0, 0.15)';
            } else {
                breadcrumbItem.style.color = 'var(--vscode-editor-foreground)';
            }

            breadcrumbItem.textContent = part;
            breadcrumbItem.title = `Navigate to ${part}`;

            // Add click handler
            breadcrumbItem.addEventListener('click', handleBreadcrumbClick);

            // Add to container
            breadcrumbContainer.appendChild(breadcrumbItem);
        }

        // Scroll to the active breadcrumb
        scrollToActiveBreadcrumb();
    }

    // Find diagram index by name
    function findDiagramIndexByName(name) {
        for (let i = 0; i < hierarchicalDiagrams.length; i++) {
            const diagram = hierarchicalDiagrams[i];
            if (diagram.domainName === name) {
                return i;
            }
        }
        return 0; // Default to the leaf node if not found
    }

    // Handle breadcrumb click
    function handleBreadcrumbClick(event) {
        const level = parseInt(event.currentTarget.dataset.level, 10);
        const name = event.currentTarget.dataset.name;

        if (isHierarchicalView && !isNaN(level) && level >= 0 && level < totalHierarchyLevels) {
            // Store the previous level before updating
            previousHierarchyLevel = currentHierarchyLevel;

            // Update current level and render diagram
            currentHierarchyLevel = level;
            const diagram = hierarchicalDiagrams[level];
            renderMermaidDiagram(diagram.mermaidCode, currentFunctionName);
            updateInfo(diagram.domainName, diagram.fullPath, currentFunctionPath);

            // Update the background color based on the hierarchy level
            updateDiagramBackgroundColor(level);

            // Update the active state in breadcrumbs without reinitializing
            // Don't hide children when parent is selected
            updateActiveBreadcrumb(level);

            // Show a subtle message indicating the navigation
            showMessage(`Navigated to ${name}`, 'info', 1000);

            console.log(`Navigated from level ${previousHierarchyLevel} to level ${currentHierarchyLevel}`);
        }
    }

    // Update active breadcrumb without reinitializing
    function updateActiveBreadcrumb(activeLevel) {
        const breadcrumbButtons = document.querySelectorAll('#breadcrumb-container button');

        // Update all buttons' styles
        for (const button of breadcrumbButtons) {
            const level = parseInt(button.dataset.level, 10);
            const levelColor = LEVEL_COLORS[level % LEVEL_COLORS.length];

            if (level === activeLevel) {
                // Active item styling with subtle color
                button.style.backgroundColor = `rgba(${levelColor}, 0.35)`;
                button.style.color = 'var(--vscode-list-activeSelectionForeground)';
                button.style.fontWeight = '500';
                button.style.boxShadow = '0 0 3px rgba(0, 0, 0, 0.15)';
            } else {
                // Inactive item styling - keep the gradient background
                button.style.background = getGradientForLevel(level);
                button.style.border = `1px solid rgba(${levelColor}, 0.4)`;
                button.style.color = 'var(--vscode-editor-foreground)';
                button.style.fontWeight = 'normal';
                button.style.boxShadow = 'none';
            }
        }

        // Scroll to the active breadcrumb
        scrollToActiveBreadcrumb();
    }

    // Scroll to the active breadcrumb
    function scrollToActiveBreadcrumb() {
        // Find the active breadcrumb item based on the current hierarchy level
        const breadcrumbButtons = document.querySelectorAll('#breadcrumb-container button');
        let activeButton = null;

        for (const button of breadcrumbButtons) {
            const level = parseInt(button.dataset.level, 10);
            if (level === currentHierarchyLevel) {
                activeButton = button;
                break;
            }
        }

        if (activeButton) {
            const container = document.getElementById('breadcrumb-container');
            if (container) {
                // Center the active button in the container
                container.scrollLeft = activeButton.offsetLeft - container.offsetWidth / 2 + activeButton.offsetWidth / 2;
            }
        }
    }

    // Update diagram background color based on hierarchy level
    function updateDiagramBackgroundColor(level) {
        // Define subtle pastel colors for different hierarchy levels
        const pastelColors = [
            '#f5f9fe', // Very light blue for leaf nodes
            '#fdf5f5', // Very light red
            '#f9faf5', // Very light yellow/green
            '#f5faf8', // Very light green
            '#f8f5fa', // Very light purple
            '#faf5f8', // Very light pink
            '#f5f8fa', // Very light cyan
            '#faf8f5'  // Very light orange
        ];

        // Get the diagram container and set its background color
        const diagramContainerElement = document.querySelector('.diagram-container');
        if (diagramContainerElement) {
            // Use the color corresponding to the level, or default to white for levels beyond our color array
            const color = level < pastelColors.length ? pastelColors[level] : '#ffffff';
            diagramContainerElement.style.backgroundColor = color;
        }
    }

    // Handle messages from the extension
    window.addEventListener('message', event => {
        const message = event.data;

        switch (message.type) {
            case 'updateDiagram':
                // Single diagram update
                isHierarchicalView = message.isHierarchical || false;
                currentHierarchyLevel = message.hierarchyLevel || 0;
                totalHierarchyLevels = message.totalLevels || 1;

                renderMermaidDiagram(message.mermaidCode, message.functionName);
                updateInfo(message.domainName, message.fullPath, message.functionPath);
                hideMessage();

                // Hide hierarchy controls for single diagram
                hierarchyControlsElement.style.display = 'none';

                // Save to history
                currentDomainName = message.domainName;
                currentFunctionPath = message.functionPath;
                currentFullPath = message.fullPath;
                currentFunctionName = message.functionName;
                addToHistory({
                    mermaidCode: message.mermaidCode,
                    domainName: message.domainName,
                    fullPath: message.fullPath,
                    functionPath: message.functionPath,
                    functionName: message.functionName
                });
                break;

            case 'updateHierarchicalDiagrams':
                // Hierarchical diagrams update
                isHierarchicalView = true;
                hierarchicalDiagrams = message.diagrams;
                totalHierarchyLevels = message.totalLevels;
                previousHierarchyLevel = currentHierarchyLevel; // Store previous level
                currentHierarchyLevel = 0; // Start with the leaf node (most specific)

                // Initialize the breadcrumb navigation
                initializeBreadcrumbs(message.hierarchyPath);

                // Render the current diagram (leaf node)
                const currentDiagram = message.currentDiagram;
                renderMermaidDiagram(currentDiagram.mermaidCode, currentDiagram.functionName);
                updateInfo(currentDiagram.domainName, currentDiagram.fullPath, currentDiagram.functionPath);
                hideMessage();

                // Update background color
                updateDiagramBackgroundColor(currentHierarchyLevel);

                // Save current state
                currentDomainName = currentDiagram.domainName;
                currentFunctionPath = currentDiagram.functionPath;
                currentFullPath = currentDiagram.fullPath;
                currentFunctionName = currentDiagram.functionName;

                // Add to history
                addToHistory({
                    mermaidCode: currentDiagram.mermaidCode,
                    domainName: currentDiagram.domainName,
                    fullPath: currentDiagram.fullPath,
                    functionPath: currentDiagram.functionPath,
                    functionName: currentDiagram.functionName,
                    isHierarchical: true,
                    hierarchicalDiagrams: hierarchicalDiagrams,
                    currentHierarchyLevel: currentHierarchyLevel,
                    totalHierarchyLevels: totalHierarchyLevels
                });
                break;

            case 'showMessage':
                showMessage(message.message);
                clearDiagram();
                clearInfo();
                updateDiagramStatus('No diagram available');
                hierarchyControlsElement.style.display = 'none';
                break;

            case 'showError':
                showError(message.message);
                clearDiagram();
                clearInfo();
                updateDiagramStatus('Error');
                hierarchyControlsElement.style.display = 'none';
                break;

            case 'zoomIn':
                zoomIn();
                break;

            case 'zoomOut':
                zoomOut();
                break;

            case 'resetZoom':
                resetZoom();
                break;
        }
    });

    // Button event listeners
    zoomInButton.addEventListener('click', () => {
        zoomIn();
    });

    zoomOutButton.addEventListener('click', () => {
        zoomOut();
    });

    resetZoomButton.addEventListener('click', () => {
        resetZoom();
    });

    // Export PNG button removed

    toggleMinimapButton.addEventListener('click', () => {
        toggleMinimap();
    });

    toggleFullscreenButton.addEventListener('click', () => {
        toggleFullscreen();
    });

    refreshDiagramButton.addEventListener('click', () => {
        refreshDiagram();
    });

    toggleCompanionButton.addEventListener('click', () => {
        toggleCompanion();
    });

    /**
     * Check if two mermaid diagrams are the same
     * @param {string} diagram1 - First diagram code
     * @param {string} diagram2 - Second diagram code
     * @returns {boolean} - True if diagrams are the same
     */
    function areDiagramsSame(diagram1, diagram2) {
        if (!diagram1 || !diagram2) return false;

        // Remove whitespace and comments for comparison
        const normalizedDiagram1 = diagram1.replace(/\s+/g, ' ')
            .replace(/%%.*?%%/g, '')
            .replace(/<!--.*?-->/g, '')
            .trim();

        const normalizedDiagram2 = diagram2.replace(/\s+/g, ' ')
            .replace(/%%.*?%%/g, '')
            .replace(/<!--.*?-->/g, '')
            .trim();

        // If we're in hierarchical view, also consider the hierarchy level
        if (isHierarchicalView) {
            // If the current hierarchy level has changed, consider it a different diagram
            // even if the content is the same
            return normalizedDiagram1 === normalizedDiagram2 &&
                   previousHierarchyLevel === currentHierarchyLevel;
        }

        return normalizedDiagram1 === normalizedDiagram2;
    }

    /**
     * Render a mermaid diagram
     * @param {string} mermaidCode - The mermaid diagram code
     * @param {string} functionName - The function name to focus on (optional)
     */
    function renderMermaidDiagram(mermaidCode, functionName = '') {
        if (!mermaidCode) {
            return;
        }

        // Check if the diagram is the same as the current one
        const isSameDiagram = areDiagramsSame(mermaidCode, currentMermaidCode);

        // If it's the same diagram, just save the current view state and return
        if (isSameDiagram && !initialRendering) {
            console.log('Same diagram detected, preserving current view state');
            // Update function info but keep the diagram as is
            previousMermaidCode = currentMermaidCode;
            currentMermaidCode = mermaidCode;

            // If a function name is provided, try to highlight it without resetting the view
            if (functionName) {
                const svg = diagramContainer?.querySelector('svg');
                if (svg) {
                    const specialCaseNode = handleSpecialCases(svg, functionName);
                    if (specialCaseNode) {
                        highlightNode(specialCaseNode);
                        updateDiagramStatus(`Found function: ${functionName}`);
                    }
                }
            }

            return;
        }

        // Save the current view state before rendering a new diagram
        if (!initialRendering) {
            lastViewState = {
                scale: currentScale,
                translateX: translateX,
                translateY: translateY
            };
        }

        // Store previous diagram code
        previousMermaidCode = currentMermaidCode;
        currentMermaidCode = mermaidCode;

        // Show loading indicator
        if (loadingElement) {
            loadingElement.style.display = 'block';
        }

        // Clear the container
        if (diagramContainer) {
            diagramContainer.innerHTML = '';
        }

        try {
            // Generate a unique ID for the diagram
            const id = `mermaid-${Date.now()}`;

            // Update status
            updateDiagramStatus('Rendering diagram...');

            // Render the diagram
            mermaid.render(id, mermaidCode)
                .then(result => {
                    // Hide loading indicator
                    if (loadingElement) {
                        loadingElement.style.display = 'none';
                    }

                    if (diagramContainer) {
                        // Create a centered container for the SVG
                        const centerContainer = document.createElement('div');
                        centerContainer.style.display = 'flex';
                        centerContainer.style.justifyContent = 'center';
                        centerContainer.style.alignItems = 'center';
                        centerContainer.style.width = '100%';
                        centerContainer.style.height = '100%';
                        centerContainer.style.position = 'relative';
                        centerContainer.id = 'center-container';

                        // Create the SVG wrapper
                        const svgWrapper = document.createElement('div');
                        svgWrapper.style.display = 'flex';
                        svgWrapper.style.justifyContent = 'center';
                        svgWrapper.style.alignItems = 'center';
                        svgWrapper.style.width = '100%';
                        svgWrapper.style.height = '100%';
                        svgWrapper.style.position = 'relative';
                        svgWrapper.id = 'svg-wrapper';
                        svgWrapper.innerHTML = result.svg;

                        // Add the SVG wrapper to the center container
                        centerContainer.appendChild(svgWrapper);

                        // Add the center container to the diagram container
                        diagramContainer.appendChild(centerContainer);

                        // Get the SVG element
                        const svg = svgWrapper.querySelector('svg');
                        if (svg) {
                            // Set initial scale - larger for better visibility
                            currentScale = 2.0; // Increased from 1.5 to 2.0 for better readability
                            translateX = 0;
                            translateY = 0;

                            // Set initial transform values consistently with other transform applications
                            svg.style.transform = `translate(calc(-50% + ${translateX}px), calc(-50% + ${translateY}px)) scale(${currentScale})`;
                            svg.style.transformOrigin = 'center';

                            // Update diagram size info
                            updateDiagramSizeInfo(svg);

                            // Add hover effects to nodes
                            enhanceDiagramInteractivity();

                            // Setup mouse wheel zooming
                            setupWheelZoom();

                            // Setup drag functionality
                            setupDragFunctionality(svg);

                            // Fade in the SVG
                            svg.style.opacity = '0';
                            setTimeout(() => {
                                svg.style.transition = 'opacity 0.3s ease';
                                svg.style.opacity = '1';
                            }, 50);
                        }
                    }

                    // Set initialRendering to false after the first render
                    initialRendering = false;

                    // Update status
                    updateDiagramStatus('Ready');
                })
                .catch(error => {
                    // Hide loading indicator
                    if (loadingElement) {
                        loadingElement.style.display = 'none';
                    }

                    console.error('Error rendering mermaid diagram:', error);
                    showError(`Failed to render diagram: ${error.message}`);
                    updateDiagramStatus('Error');
                });
        } catch (error) {
            // Hide loading indicator
            if (loadingElement) {
                loadingElement.style.display = 'none';
            }

            console.error('Error rendering mermaid diagram:', error);
            showError(`Failed to render diagram: ${error.message}`);
            updateDiagramStatus('Error');
        }
    }

    /**
     * Update the diagram size information
     * @param {SVGElement} svg - The SVG element
     */
    function updateDiagramSizeInfo(svg) {
        if (!svg) return;

        const width = svg.width.baseVal.value;
        const height = svg.height.baseVal.value;
        const nodeCount = svg.querySelectorAll('.node').length;
        const edgeCount = svg.querySelectorAll('.edgePath').length;

        diagramSizeElement.textContent = `${width.toFixed(0)}×${height.toFixed(0)} | ${nodeCount} nodes | ${edgeCount} edges`;
    }

    /**
     * Update the diagram status
     * @param {string} status - The status message
     */
    function updateDiagramStatus(status) {
        diagramStatusElement.textContent = status;
    }

    /**
     * Update the zoom level display
     */
    function updateZoomLevelDisplay() {
        if (zoomLevelElement) {
            zoomLevelElement.textContent = `${Math.round(currentScale * 100)}%`;
        }
    }

    /**
     * Toggle the minimap visibility
     */
    function toggleMinimap() {
        minimapVisible = !minimapVisible;

        if (minimapVisible) {
            minimapElement.style.display = 'block';
            updateMinimap();
            toggleMinimapButton.classList.add('active');
        } else {
            minimapElement.style.display = 'none';
            toggleMinimapButton.classList.remove('active');
        }
    }

    /**
     * Update the minimap
     */
    function updateMinimap() {
        if (!minimapVisible) return;

        const svg = diagramContainer.querySelector('svg');
        if (!svg) return;

        // Clear the minimap
        minimapElement.innerHTML = '';
        minimapElement.appendChild(minimapViewportElement);

        // Create a clone of the SVG for the minimap
        const svgClone = svg.cloneNode(true);
        svgClone.style.transform = 'scale(1)';
        svgClone.style.width = '100%';
        svgClone.style.height = 'auto';
        svgClone.style.maxHeight = '100%';
        svgClone.style.pointerEvents = 'none';
        minimapElement.appendChild(svgClone);

        // Update the viewport
        updateMinimapViewport();

        // Add event listeners for minimap interaction
        minimapElement.addEventListener('mousedown', startMinimapDrag);
        minimapElement.addEventListener('mousemove', dragMinimap);
        minimapElement.addEventListener('mouseup', endMinimapDrag);
        minimapElement.addEventListener('mouseleave', endMinimapDrag);
    }

    /**
     * Update the minimap viewport
     */
    function updateMinimapViewport() {
        if (!minimapVisible) return;

        const svg = diagramContainer.querySelector('svg');
        if (!svg) return;

        const svgWidth = svg.width.baseVal.value;
        const svgHeight = svg.height.baseVal.value;

        const minimapWidth = minimapElement.clientWidth;
        const minimapHeight = minimapElement.clientHeight;

        const minimapSvg = minimapElement.querySelector('svg');
        if (!minimapSvg) return;

        // Calculate the scale factor between the minimap and the actual diagram
        const scaleX = minimapWidth / svgWidth;
        const scaleY = minimapHeight / svgHeight;
        const scale = Math.min(scaleX, scaleY);

        // Calculate the visible portion of the diagram
        const visibleWidth = diagramContainer.clientWidth / currentScale;
        const visibleHeight = diagramContainer.clientHeight / currentScale;

        // Calculate the viewport position and size
        const viewportX = (-offsetX / currentScale) * scale;
        const viewportY = (-offsetY / currentScale) * scale;
        const viewportWidth = visibleWidth * scale;
        const viewportHeight = visibleHeight * scale;

        // Update the viewport element
        minimapViewportElement.style.left = `${viewportX}px`;
        minimapViewportElement.style.top = `${viewportY}px`;
        minimapViewportElement.style.width = `${viewportWidth}px`;
        minimapViewportElement.style.height = `${viewportHeight}px`;
    }

    // Minimap drag state
    let minimapDragging = false;
    let minimapDragStartX = 0;
    let minimapDragStartY = 0;

    /**
     * Start dragging the minimap viewport
     * @param {MouseEvent} e - The mouse event
     */
    function startMinimapDrag(e) {
        minimapDragging = true;
        minimapDragStartX = e.clientX;
        minimapDragStartY = e.clientY;
        minimapViewportElement.style.cursor = 'grabbing';
    }

    /**
     * Drag the minimap viewport
     * @param {MouseEvent} e - The mouse event
     */
    function dragMinimap(e) {
        if (!minimapDragging) return;

        const dx = e.clientX - minimapDragStartX;
        const dy = e.clientY - minimapDragStartY;

        minimapDragStartX = e.clientX;
        minimapDragStartY = e.clientY;

        const svg = diagramContainer.querySelector('svg');
        if (!svg) return;

        const svgWidth = svg.width.baseVal.value;
        const svgHeight = svg.height.baseVal.value;

        const minimapWidth = minimapElement.clientWidth;
        const minimapHeight = minimapElement.clientHeight;

        // Calculate the scale factor between the minimap and the actual diagram
        const scaleX = minimapWidth / svgWidth;
        const scaleY = minimapHeight / svgHeight;
        const scale = Math.min(scaleX, scaleY);

        // Calculate the offset in the actual diagram
        const diagramDx = dx / scale * currentScale;
        const diagramDy = dy / scale * currentScale;

        // Update the diagram position
        offsetX -= diagramDx;
        offsetY -= diagramDy;

        // Apply the new position
        applyZoom();

        // Update the viewport
        updateMinimapViewport();
    }

    /**
     * End dragging the minimap viewport
     */
    function endMinimapDrag() {
        minimapDragging = false;
        minimapViewportElement.style.cursor = 'move';
    }

    /**
     * Toggle fullscreen mode
     */
    function toggleFullscreen() {
        isFullscreen = !isFullscreen;

        if (isFullscreen) {
            container.classList.add('fullscreen');
            toggleFullscreenButton.title = 'Exit Fullscreen';
            toggleFullscreenButton.querySelector('i').classList.remove('codicon-screen-full');
            toggleFullscreenButton.querySelector('i').classList.add('codicon-screen-normal');
        } else {
            container.classList.remove('fullscreen');
            toggleFullscreenButton.title = 'Toggle Fullscreen';
            toggleFullscreenButton.querySelector('i').classList.remove('codicon-screen-normal');
            toggleFullscreenButton.querySelector('i').classList.add('codicon-screen-full');
        }

        // Update the minimap if visible
        if (minimapVisible) {
            updateMinimap();
        }
    }

    /**
     * Refresh the current diagram
     */
    function refreshDiagram() {
        if (currentFunctionPath) {
            updateDiagramStatus('Refreshing diagram...');
            vscode.postMessage({
                type: 'refreshDiagram',
                functionPath: currentFunctionPath
            });
        }
    }

    /**
     * Toggle the Mermaid Companion visibility
     */
    function toggleCompanion() {
        vscode.postMessage({ type: 'toggleCompanion' });
    }

    /**
     * Add a diagram to the history
     * @param {Object} diagram - The diagram to add
     */
    function addToHistory(diagram) {
        // Remove any forward history if we're not at the end
        if (currentHistoryIndex < diagramHistory.length - 1) {
            diagramHistory = diagramHistory.slice(0, currentHistoryIndex + 1);
        }

        // Add the new diagram to history
        diagramHistory.push(diagram);
        currentHistoryIndex = diagramHistory.length - 1;

        // Limit history size
        if (diagramHistory.length > 20) {
            diagramHistory.shift();
            currentHistoryIndex--;
        }
    }

    /**
     * Setup pan events for the diagram
     */
    function setupPanEvents() {
        const svg = diagramContainer.querySelector('svg');
        if (!svg) return;

        // Mouse events for panning
        diagramContainer.addEventListener('mousedown', startPan);
        diagramContainer.addEventListener('mousemove', pan);
        diagramContainer.addEventListener('mouseup', endPan);
        diagramContainer.addEventListener('mouseleave', endPan);

        // Touch events for mobile panning
        diagramContainer.addEventListener('touchstart', startPanTouch);
        diagramContainer.addEventListener('touchmove', panTouch);
        diagramContainer.addEventListener('touchend', endPan);

        // Mouse wheel for zooming
        diagramContainer.addEventListener('wheel', handleWheel, { passive: false });

        // Prevent context menu on right-click for better panning
        diagramContainer.addEventListener('contextmenu', e => {
            if (panMode) {
                e.preventDefault();
            }
        });

        // Double click to zoom in
        diagramContainer.addEventListener('dblclick', e => {
            // Get the position of the double click
            const rect = diagramContainer.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // Calculate the position in the diagram
            const diagramX = (x - offsetX) / currentScale;
            const diagramY = (y - offsetY) / currentScale;

            // Zoom in
            currentScale += 0.2;

            // Adjust the offset to keep the clicked point at the same position
            offsetX = x - diagramX * currentScale;
            offsetY = y - diagramY * currentScale;

            // Apply the new zoom and position
            applyZoom();
            updateZoomButtonStates();
            updateZoomLevelDisplay();

            // Update the minimap if visible
            if (minimapVisible) {
                updateMinimapViewport();
            }
        });
    }

    /**
     * Start panning (mouse)
     */
    function startPan(e) {
        // Only start pan with middle mouse button, when holding Ctrl/Cmd, or when in pan mode
        if (e.button === 1 || e.ctrlKey || e.metaKey || panMode) {
            panMode = true;
            lastX = e.clientX;
            lastY = e.clientY;
            diagramContainer.style.cursor = 'grabbing';
            e.preventDefault();
        }
    }

    /**
     * Pan the diagram (mouse)
     */
    function pan(e) {
        if (!panMode) return;

        const dx = e.clientX - lastX;
        const dy = e.clientY - lastY;

        offsetX += dx;
        offsetY += dy;

        lastX = e.clientX;
        lastY = e.clientY;

        applyZoom();

        // Update the minimap if visible
        if (minimapVisible) {
            updateMinimapViewport();
        }

        e.preventDefault();
    }

    /**
     * Start panning (touch)
     */
    function startPanTouch(e) {
        if (e.touches.length === 1) {
            panMode = true;
            lastX = e.touches[0].clientX;
            lastY = e.touches[0].clientY;
            e.preventDefault();
        }
    }

    /**
     * Pan the diagram (touch)
     */
    function panTouch(e) {
        if (!panMode || e.touches.length !== 1) return;

        const dx = e.touches[0].clientX - lastX;
        const dy = e.touches[0].clientY - lastY;

        offsetX += dx;
        offsetY += dy;

        lastX = e.touches[0].clientX;
        lastY = e.touches[0].clientY;

        applyZoom();

        // Update the minimap if visible
        if (minimapVisible) {
            updateMinimapViewport();
        }

        e.preventDefault();
    }

    /**
     * End panning
     */
    function endPan() {
        if (panMode) {
            // Only change cursor if we're not in persistent pan mode
            if (!document.querySelector('.space-pressed')) {
                panMode = false;
                diagramContainer.style.cursor = 'default';
                updateDiagramStatus('Ready');
            }
        }
    }

    /**
     * Setup wheel zoom event handling
     */
    function setupWheelZoom() {
        if (!diagramContainer) return;

        // Remove any existing wheel event listeners
        diagramContainer.removeEventListener('wheel', handleWheel);

        // Add the wheel event listener
        diagramContainer.addEventListener('wheel', handleWheel, { passive: false });
    }

    /**
     * Setup drag functionality for the SVG
     * @param {SVGElement} svg - The SVG element
     */
    function setupDragFunctionality(svg) {
        if (!svg || !diagramContainer) return;

        // Remove any existing event listeners
        diagramContainer.removeEventListener('mousedown', startDrag);
        document.removeEventListener('mousemove', drag);
        document.removeEventListener('mouseup', endDrag);

        // Add event listeners for dragging
        diagramContainer.addEventListener('mousedown', startDrag);
        document.addEventListener('mousemove', drag);
        document.addEventListener('mouseup', endDrag);

        // Add touch events for mobile
        diagramContainer.addEventListener('touchstart', startDragTouch);
        document.addEventListener('touchmove', dragTouch);
        document.addEventListener('touchend', endDrag);
    }

    /**
     * Start dragging (mouse)
     * @param {MouseEvent} e - The mouse event
     */
    function startDrag(e) {
        // Only start drag with left mouse button
        if (e.button !== 0) return;

        // Prevent default behavior
        e.preventDefault();

        isDragging = true;
        dragStartX = e.clientX;
        dragStartY = e.clientY;

        // Change cursor to indicate dragging
        if (diagramContainer) {
            diagramContainer.style.cursor = 'grabbing';
        }
    }

    /**
     * Drag the diagram (mouse)
     * @param {MouseEvent} e - The mouse event
     */
    function drag(e) {
        if (!isDragging) return;

        // Calculate the distance moved
        const dx = e.clientX - dragStartX;
        const dy = e.clientY - dragStartY;

        // Update the translation values
        translateX += dx;
        translateY += dy;

        // Update the SVG position using applyZoom for consistency
        applyZoom(translateX, translateY, currentScale);

        // Update drag start position for next move
        dragStartX = e.clientX;
        dragStartY = e.clientY;
    }

    /**
     * Start dragging (touch)
     * @param {TouchEvent} e - The touch event
     */
    function startDragTouch(e) {
        if (e.touches.length !== 1) return;

        // Prevent default behavior
        e.preventDefault();

        isDragging = true;
        dragStartX = e.touches[0].clientX;
        dragStartY = e.touches[0].clientY;
    }

    /**
     * Drag the diagram (touch)
     * @param {TouchEvent} e - The touch event
     */
    function dragTouch(e) {
        if (!isDragging || e.touches.length !== 1) return;

        // Prevent default behavior
        e.preventDefault();

        // Calculate the distance moved
        const dx = e.touches[0].clientX - dragStartX;
        const dy = e.touches[0].clientY - dragStartY;

        // Update the translation values
        translateX += dx;
        translateY += dy;

        // Update the SVG position using applyZoom for consistency
        applyZoom(translateX, translateY, currentScale);

        // Update drag start position for next move
        dragStartX = e.touches[0].clientX;
        dragStartY = e.touches[0].clientY;
    }

    /**
     * End dragging
     */
    function endDrag() {
        if (!isDragging) return;

        isDragging = false;

        // Reset cursor
        if (diagramContainer) {
            diagramContainer.style.cursor = 'grab';
        }
    }

    /**
     * Handle mouse wheel events for zooming
     * @param {WheelEvent} e - The wheel event
     */
    function handleWheel(e) {
        // Prevent default scrolling behavior
        e.preventDefault();

        // Get the SVG element
        const svgWrapper = document.getElementById('svg-wrapper');
        if (!svgWrapper) return;

        const svg = svgWrapper.querySelector('svg');
        if (!svg) return;

        // Determine zoom direction and amount
        const zoomStep = 0.1; // Smaller step for finer control
        const delta = -Math.sign(e.deltaY) * zoomStep;

        // Calculate new scale with limits
        const oldScale = currentScale;
        currentScale = Math.max(0.5, Math.min(25, currentScale + delta));

        // If scale didn't change, don't do anything
        if (oldScale === currentScale) return;

        // Apply the new scale using applyZoom for consistency
        applyZoom(translateX, translateY, currentScale);

        // Update zoom level display
        updateZoomLevelDisplay();

        console.log(`Wheel zoom: ${oldScale.toFixed(2)} -> ${currentScale.toFixed(2)}`);
    }

    /**
     * Enhance diagram interactivity - simplified without hover effects
     */
    function enhanceDiagramInteractivity() {
        const svg = diagramContainer.querySelector('svg');
        if (!svg) return;

        // Add title attributes to nodes for tooltips
        const nodes = svg.querySelectorAll('.node');
        nodes.forEach(node => {
            const text = node.querySelector('text');
            if (text && text.textContent) {
                node.setAttribute('title', text.textContent.trim());

                // Improve text visibility
                if (text) {
                    text.style.fontWeight = '500';
                    text.style.fontSize = '18px'; // Increased from 14px to 18px
                }

                // Add click event to nodes
                node.addEventListener('click', () => {
                    // Highlight the node
                    highlightNode(node);

                    // Show node info in status bar
                    updateDiagramStatus(`Node: ${text.textContent.trim()}`);
                });
            }
        });

        // Improve edge label visibility without hover effects
        const edges = svg.querySelectorAll('.edgePath');
        edges.forEach(edge => {
            const label = edge.querySelector('.edgeLabel text');
            if (label && label.textContent) {
                edge.setAttribute('title', label.textContent.trim());

                // Improve edge label visibility
                if (label) {
                    // Make edge labels more visible
                    const labelBg = edge.querySelector('.edgeLabel');
                    if (labelBg) {
                        // Use theme colors for better contrast in both light and dark modes
                        labelBg.style.backgroundColor = 'var(--vscode-editor-background)';
                        labelBg.style.padding = '6px 10px'; // Increased padding for better readability
                        labelBg.style.borderRadius = '4px';
                        labelBg.style.border = '1px solid var(--vscode-panel-border)';
                        // Ensure text doesn't get cut off
                        labelBg.style.whiteSpace = 'nowrap';
                        labelBg.style.overflow = 'visible';
                    }

                    // Improve text visibility
                    label.style.fill = 'var(--vscode-editor-foreground)';
                    label.style.fontWeight = '600';
                    label.style.fontSize = '18px'; // Increased from 14px to 18px
                }

                // Add click event to edges
                edge.addEventListener('click', () => {
                    // Highlight the edge
                    highlightEdge(edge);

                    // Show edge info in status bar
                    updateDiagramStatus(`Edge: ${label.textContent.trim()}`);
                });
            }

            // Improve edge path visibility
            const path = edge.querySelector('path');
            if (path) {
                path.style.stroke = '#0066cc';
                path.style.strokeWidth = '2px';
            }
        });
    }

    /**
     * Highlight an edge (simplified without animations)
     * @param {SVGElement} edge - The edge to highlight
     */
    function highlightEdge(edge) {
        // Get the edge's path element
        const path = edge.querySelector('path');
        if (!path) return;

        // Store original attributes
        const originalStroke = path.getAttribute('stroke');
        const originalStrokeWidth = path.getAttribute('stroke-width');

        // Apply highlight directly to the path without animations
        path.setAttribute('stroke', '#ff5533');
        path.setAttribute('stroke-width', '3px');

        // Also highlight the edge label if present
        const labelElement = edge.querySelector('.edgeLabel');
        let originalBgColor = null;
        let originalBoxShadow = null;

        if (labelElement) {
            originalBgColor = labelElement.style.backgroundColor;
            originalBoxShadow = labelElement.style.boxShadow;

            labelElement.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
            labelElement.style.boxShadow = '0 0 5px rgba(255, 85, 51, 0.5)';
        }

        // Restore original attributes after a delay
        setTimeout(() => {
            if (originalStroke) {
                path.setAttribute('stroke', originalStroke);
            }
            if (originalStrokeWidth) {
                path.setAttribute('stroke-width', originalStrokeWidth);
            }

            if (labelElement) {
                labelElement.style.backgroundColor = originalBgColor || '';
                labelElement.style.boxShadow = originalBoxShadow || '';
            }
        }, 3000);
    }

    /**
     * Update the information panel
     * @param {string} domainName - The domain name
     * @param {string} fullPath - The full path
     * @param {string} functionPath - The function path
     */
    function updateInfo(domainName, fullPath, functionPath) {
        // Hide domain name element for cleaner UI
        if (domainNameElement) {
            domainNameElement.style.display = 'none';
        }

        if (functionPathElement && functionPath) {
            functionPathElement.textContent = functionPath;
            functionPathElement.title = functionPath;
        } else if (functionPathElement) {
            functionPathElement.textContent = '';
            functionPathElement.title = '';
        }
    }

    /**
     * Clear the information panel
     */
    function clearInfo() {
        // Keep domain name element hidden
        if (domainNameElement) {
            domainNameElement.style.display = 'none';
        }

        if (functionPathElement) {
            functionPathElement.textContent = '';
            functionPathElement.title = '';
        }
    }

    /**
     * Show a message
     * @param {string} message - The message to show
     * @param {string} [type='info'] - The message type ('info' or 'error')
     * @param {number} [duration=5000] - Duration in ms to show the message
     */
    function showMessage(message, type = 'info', duration = 5000) {
        if (messageElement) {
            messageElement.textContent = message;

            if (type === 'error') {
                messageElement.classList.add('error');
            } else {
                messageElement.classList.remove('error');
            }

            messageElement.style.display = 'block';

            // Auto-hide the message after the specified duration
            setTimeout(() => {
                hideMessage();
            }, duration);
        }
    }

    /**
     * Show an error message
     * @param {string} message - The error message to show
     */
    function showError(message) {
        // Use the showMessage function with type='error'
        showMessage(message, 'error', 10000); // Show errors for longer (10 seconds)
    }

    /**
     * Hide the message
     */
    function hideMessage() {
        if (messageElement) {
            messageElement.style.display = 'none';
        }
    }

    /**
     * Clear the diagram
     */
    function clearDiagram() {
        if (diagramContainer) {
            diagramContainer.innerHTML = '';
        }
        currentMermaidCode = '';

        // Clear the minimap
        if (minimapElement) {
            minimapElement.innerHTML = '';
            minimapElement.appendChild(minimapViewportElement);
        }

        // Update diagram size info
        diagramSizeElement.textContent = '';
    }

    /**
     * Zoom in
     */
    function zoomIn() {
        // Get the center of the viewport
        const containerWidth = diagramContainer.clientWidth;
        const containerHeight = diagramContainer.clientHeight;
        const centerX = containerWidth / 2;
        const centerY = containerHeight / 2;

        // Calculate the point in the diagram that's currently at the center of the viewport
        const svg = diagramContainer.querySelector('svg');
        if (svg) {
            // Calculate the point in the diagram that's at the center of the viewport
            const diagramX = (centerX - offsetX) / currentScale;
            const diagramY = (centerY - offsetY) / currentScale;

            // Increase the scale
            const oldScale = currentScale;
            currentScale = Math.min(10, currentScale + 0.5);

            // Adjust the offset to keep the center point at the same position
            offsetX = centerX - diagramX * currentScale;
            offsetY = centerY - diagramY * currentScale;

            // Apply the new zoom and position
            applyZoom();
            console.log(`Zoomed in from ${oldScale} to ${currentScale}, new offset: (${offsetX}, ${offsetY})`);
        }

        // Update button states and display
        updateZoomButtonStates();
        updateZoomLevelDisplay();

        // Update the minimap if visible
        if (minimapVisible) {
            updateMinimapViewport();
        }
    }

    /**
     * Zoom out
     */
    function zoomOut() {
        // Get the center of the viewport
        const containerWidth = diagramContainer.clientWidth;
        const containerHeight = diagramContainer.clientHeight;
        const centerX = containerWidth / 2;
        const centerY = containerHeight / 2;

        // Calculate the point in the diagram that's currently at the center of the viewport
        const svg = diagramContainer.querySelector('svg');
        if (svg) {
            // Calculate the point in the diagram that's at the center of the viewport
            const diagramX = (centerX - offsetX) / currentScale;
            const diagramY = (centerY - offsetY) / currentScale;

            // Decrease the scale with a minimum limit
            const oldScale = currentScale;
            currentScale = Math.max(0.5, currentScale - 0.1);

            // Adjust the offset to keep the center point at the same position
            offsetX = centerX - diagramX * currentScale;
            offsetY = centerY - diagramY * currentScale;

            // Apply the new zoom and position
            applyZoom();
            console.log(`Zoomed out from ${oldScale} to ${currentScale}, new offset: (${offsetX}, ${offsetY})`);
        }

        // Update button states and display
        updateZoomButtonStates();
        updateZoomLevelDisplay();

        // Update the minimap if visible
        if (minimapVisible) {
            updateMinimapViewport();
        }
    }

    /**
     * Reset zoom and pan
     */
    function resetZoom() {
        // Get the SVG element
        const svgWrapper = document.getElementById('svg-wrapper');
        if (!svgWrapper) return;

        const svg = svgWrapper.querySelector('svg');
        if (!svg) return;

        // Reset to default scale and position
        currentScale = 2.0; // Increased from 1.5 to 2.0 for better readability
        translateX = 0;
        translateY = 0;

        // Apply the new scale using applyZoom for consistency
        applyZoom(translateX, translateY, currentScale);

        // Update zoom level display
        updateZoomLevelDisplay();

        console.log(`Reset zoom to ${currentScale.toFixed(2)}`);
    }

    /**
     * Update zoom button states based on current zoom level
     */
    function updateZoomButtonStates() {
        // Disable zoom out button if at minimum zoom
        if (zoomOutButton) {
            zoomOutButton.disabled = currentScale <= 0.5;
        }

        // Disable zoom in button if at maximum zoom
        if (zoomInButton) {
            zoomInButton.disabled = currentScale >= 25.0;
        }

        // Disable reset button if already at default zoom and position
        if (resetZoomButton) {
            resetZoomButton.disabled = currentScale === 2.0 && translateX === 0 && translateY === 0;
        }
    }

    /**
     * Apply zoom level and position
     * @param {number} x - X offset
     * @param {number} y - Y offset
     * @param {number} scale - Scale factor
     * @param {boolean} updateGlobals - Whether to update global state variables
     */
    function applyZoom(x = offsetX, y = offsetY, scale = currentScale, updateGlobals = true) {
        const svg = diagramContainer.querySelector('svg');
        if (svg) {
            // Validate inputs to prevent NaN issues
            if (isNaN(x) || isNaN(y) || isNaN(scale)) {
                console.error(`Invalid zoom parameters: x=${x}, y=${y}, scale=${scale}`);
                // Use current values as fallback
                x = isNaN(x) ? offsetX : x;
                y = isNaN(y) ? offsetY : y;
                scale = isNaN(scale) ? currentScale : scale;
            }

            // Ensure scale is within reasonable bounds
            scale = Math.max(0.5, Math.min(25.0, scale));

            // Update global state if requested
            if (updateGlobals) {
                offsetX = x;
                offsetY = y;
                currentScale = scale;
            }

            // Apply transform consistently using calc with percentages and pixels
            svg.style.transform = `translate(calc(-50% + ${x}px), calc(-50% + ${y}px)) scale(${scale})`;
            svg.style.transformOrigin = 'center';

            // Update zoom level display
            updateZoomLevelDisplay();

            // Update button states
            updateZoomButtonStates();

            // Update the minimap if visible
            if (minimapVisible) {
                updateMinimapViewport();
            }
        }
    }

    /**
     * Position the diagram in the viewport optimized for VS Code extension panel
     */
    function centerDiagram() {
        const svg = diagramContainer?.querySelector('svg');
        if (!svg) return;

        // Get the SVG dimensions
        const svgWidth = svg.width.baseVal.value;
        const svgHeight = svg.height.baseVal.value;

        // Get the container dimensions
        const containerWidth = diagramContainer?.clientWidth || 800;
        const containerHeight = diagramContainer?.clientHeight || 600;

        console.log(`Container: ${containerWidth}x${containerHeight}, SVG: ${svgWidth}x${svgHeight}`);

        // Calculate an appropriate scale based on the diagram size and container
        let scale;

        // Calculate the ratio of SVG size to container size
        const widthRatio = svgWidth / containerWidth;
        const heightRatio = svgHeight / containerHeight;

        // Use the larger ratio to ensure the diagram fits in the viewport
        const fitRatio = Math.max(widthRatio, heightRatio);

        if (fitRatio > 1) {
            // If the diagram is larger than the container, scale it down to fit
            scale = 0.9 / fitRatio; // 90% of the size needed to fit exactly
        } else {
            // If the diagram is smaller than the container, use a reasonable scale
            scale = 0.9;
        }

        // Ensure the scale is reasonable
        scale = Math.max(0.3, Math.min(10.0, scale));

        // Position the diagram in the center of the viewport
        // This ensures users can see the whole diagram
        const offsetX = (containerWidth - svgWidth * scale) / 2;
        const offsetY = (containerHeight - svgHeight * scale) / 2;

        // Ensure offsets are positive to keep diagram in view
        const finalOffsetX = Math.max(20, offsetX);
        const finalOffsetY = Math.max(20, offsetY);

        // Apply the new position and scale with animation
        animateZoomAndPan(scale, finalOffsetX, finalOffsetY);

        console.log(`Positioned diagram with scale ${scale} and offset (${finalOffsetX}, ${finalOffsetY})`);
    }

    /**
     * Position the diagram in the center of the viewport with a fixed zoom level of 2.0
     * This improved version ensures the diagram is always centered regardless of its size
     */
    function centerDiagramFixed() {
        const svg = diagramContainer?.querySelector('svg');
        if (!svg) return;

        // Get the SVG dimensions
        const svgWidth = svg.width.baseVal.value;
        const svgHeight = svg.height.baseVal.value;

        // Get the container dimensions
        const containerWidth = diagramContainer?.clientWidth || 800;
        const containerHeight = diagramContainer?.clientHeight || 600;

        console.log(`Container: ${containerWidth}x${containerHeight}, SVG: ${svgWidth}x${svgHeight}`);

        // Use fixed scale of 2.0 as requested
        const scale = 2.0;

        // Calculate the center position
        // This ensures the diagram is always centered in the viewport
        const offsetX = Math.max(0, (containerWidth - svgWidth * scale) / 2);
        const offsetY = Math.max(0, (containerHeight - svgHeight * scale) / 2);

        // Apply the new position and scale immediately without animation
        applyZoom(offsetX, offsetY, scale);

        // Set additional styles to ensure centering
        diagramContainer.style.display = 'flex';
        diagramContainer.style.justifyContent = 'center';
        diagramContainer.style.alignItems = 'center';

        console.log(`Positioned diagram with fixed scale ${scale} and offset (${offsetX}, ${offsetY})`);
    }

    /**
     * Maintain current scale but position the diagram appropriately
     * This is used when we need to reposition without changing the scale
     */
    function maintainScaleAndCenter() {
        const svg = diagramContainer?.querySelector('svg');
        if (!svg) return;

        // Get the SVG dimensions
        const svgWidth = svg.width.baseVal.value;
        const svgHeight = svg.height.baseVal.value;

        // Get the container dimensions
        const containerWidth = diagramContainer?.clientWidth || 400;
        const containerHeight = diagramContainer?.clientHeight || 300;

        // Calculate an appropriate scale based on the diagram size and container
        let scale;

        // Calculate the ratio of SVG size to container size
        const widthRatio = svgWidth / containerWidth;
        const heightRatio = svgHeight / containerHeight;

        // Use the larger ratio to ensure the diagram fits in the viewport
        const fitRatio = Math.max(widthRatio, heightRatio);

        if (fitRatio > 1) {
            // If the diagram is larger than the container, scale it down to fit
            scale = 0.9 / fitRatio; // 90% of the size needed to fit exactly
        } else {
            // If the diagram is smaller than the container, use a reasonable scale
            scale = Math.max(currentScale, 0.9);
        }

        // Ensure the scale is reasonable
        scale = Math.max(0.3, Math.min(10.0, scale));

        // Position the diagram in the center of the viewport
        // This ensures users can see the whole diagram
        const offsetX = (containerWidth - svgWidth * scale) / 2;
        const offsetY = (containerHeight - svgHeight * scale) / 2;

        // Ensure offsets are positive to keep diagram in view
        const finalOffsetX = Math.max(20, offsetX);
        const finalOffsetY = Math.max(20, offsetY);

        // Apply the new position and scale with animation
        animateZoomAndPan(scale, finalOffsetX, finalOffsetY);

        console.log(`Positioned diagram with scale ${scale} and offset (${finalOffsetX}, ${finalOffsetY})`);
    }

    /**
     * Focus on a specific function in the diagram
     * @param {string} functionName - The function name to focus on
     */
    function focusOnFunction(functionName) {
        if (!functionName) return;

        console.log(`Focusing on function: ${functionName}`);
        updateDiagramStatus(`Focusing on function: ${functionName}...`);

        const svg = diagramContainer.querySelector('svg');
        if (!svg) return;

        // Special case handling for known patterns
        // For example: 'build_jira_import' -> 'Build Jira Import Record'
        const specialCaseNode = handleSpecialCases(svg, functionName);
        if (specialCaseNode) {
            console.log(`Found special case match for: ${functionName}`);
            highlightAndFocusNode(specialCaseNode);
            updateDiagramStatus(`Found function: ${functionName}`);
            return;
        }

        // Generate alternative search patterns
        const searchPatterns = generateSearchPatterns(functionName);
        console.log(`Generated search patterns: ${searchPatterns.join(', ')}`);

        // Try each search pattern in order of specificity
        let targetNode = null;
        let allMatchingNodes = [];

        // First try exact match with the original function name
        targetNode = findNodeWithText(svg, functionName);
        if (targetNode) {
            console.log(`Found exact match for: ${functionName}`);
            allMatchingNodes.push({ node: targetNode, priority: 0, score: 100 });
        }

        // Then try contains match with the original function name
        const containsMatches = findNodesContainingText(svg, functionName);
        if (containsMatches.length > 0) {
            console.log(`Found ${containsMatches.length} nodes containing: ${functionName}`);
            // Add each match with its score as a tiebreaker for priority
            for (let i = 0; i < containsMatches.length; i++) {
                const node = containsMatches[i];
                const text = node.querySelector('text')?.textContent.trim() || '';
                const score = calculateMatchScore(text, functionName);
                allMatchingNodes.push({ node, priority: 1, score });
            }
        }

        // Try each alternative pattern
        for (let i = 0; i < searchPatterns.length; i++) {
            const pattern = searchPatterns[i];

            // Skip if it's the same as the original function name
            if (pattern === functionName) continue;

            // Try exact match
            const exactMatch = findNodeWithText(svg, pattern);
            if (exactMatch) {
                console.log(`Found exact match for pattern: ${pattern}`);
                const text = exactMatch.querySelector('text')?.textContent.trim() || '';
                const score = calculateMatchScore(text, functionName);
                allMatchingNodes.push({ node: exactMatch, priority: i + 2, score });
            }

            // Try contains match
            const patternMatches = findNodesContainingText(svg, pattern);
            if (patternMatches.length > 0) {
                console.log(`Found ${patternMatches.length} nodes containing pattern: ${pattern}`);
                for (let j = 0; j < patternMatches.length; j++) {
                    const node = patternMatches[j];
                    const text = node.querySelector('text')?.textContent.trim() || '';
                    const score = calculateMatchScore(text, functionName);
                    allMatchingNodes.push({ node, priority: i + searchPatterns.length + 2, score });
                }
            }
        }

        // If we have matches, sort by priority and score, then use the best one
        if (allMatchingNodes.length > 0) {
            // Remove duplicates (same node with different priorities)
            const uniqueNodes = [];
            const seenNodes = new Set();

            for (const match of allMatchingNodes) {
                const nodeId = match.node.id || match.node.querySelector('text')?.textContent.trim();
                if (!seenNodes.has(nodeId)) {
                    uniqueNodes.push(match);
                    seenNodes.add(nodeId);
                }
            }

            // Sort by priority first, then by score (higher score is better)
            uniqueNodes.sort((a, b) => {
                if (a.priority !== b.priority) {
                    return a.priority - b.priority;
                }
                return b.score - a.score;
            });

            targetNode = uniqueNodes[0].node;
            const nodeText = targetNode.querySelector('text')?.textContent.trim() || '';

            console.log(`Selected best match with priority ${uniqueNodes[0].priority} and score ${uniqueNodes[0].score}`);
            highlightAndFocusNode(targetNode);
            updateDiagramStatus(`Found function: ${nodeText}`);
        } else {
            console.log(`No node found for function: ${functionName} or any of its patterns`);
            updateDiagramStatus(`Could not find function: ${functionName}`);
            showMessage(`Could not find function "${functionName}" in the diagram. Try zooming out to see the full diagram.`);
        }
    }

    /**
     * Handle special cases for function name matching
     * @param {SVGElement} svg - The SVG element
     * @param {string} functionName - The function name to match
     * @returns {SVGElement|null} - The matched node or null
     */
    function handleSpecialCases(svg, functionName) {
        // This function is now a generic text-based search that doesn't rely on special cases
        // It will search for any function name using a variety of text matching techniques
        console.log(`Performing advanced text search for: ${functionName}`);

        // Get all text elements and nodes
        const allTextElements = svg.querySelectorAll('text');
        const allNodes = svg.querySelectorAll('.node');
        console.log(`Found ${allTextElements.length} text elements and ${allNodes.length} nodes in the SVG`);

        // Extract key terms from the function name
        const keyTerms = extractKeyTerms(functionName);
        console.log(`Key terms: ${keyTerms.join(', ')}`);

        // Store nodes with their match scores
        const nodeScores = new Map();

        // First, check all text elements for matches
        for (const textElement of allTextElements) {
            const text = textElement.textContent?.trim() || '';
            if (!text) continue;

            // Calculate a match score for this text
            const score = calculateTextMatchScore(text, functionName, keyTerms);
            if (score > 0) {
                const node = findParentNode(textElement);
                if (node) {
                    // Add or update the score for this node
                    nodeScores.set(node, Math.max(score, nodeScores.get(node) || 0));
                    console.log(`Text match: "${text}" with score ${score}`);
                }
            }
        }

        // Then check all nodes for matches in their attributes and structure
        for (const node of allNodes) {
            // Try to extract the label from the node
            const nodeLabel = extractNodeLabel(node);
            if (nodeLabel) {
                const score = calculateTextMatchScore(nodeLabel, functionName, keyTerms);
                if (score > 0) {
                    // Add or update the score for this node
                    nodeScores.set(node, Math.max(score, nodeScores.get(node) || 0));
                    console.log(`Node label match: "${nodeLabel}" with score ${score}`);
                }
            }

            // Check node ID and attributes
            let attrScore = 0;
            if (node.id) {
                const idScore = calculateTextMatchScore(node.id, functionName, keyTerms);
                attrScore += idScore;
                if (idScore > 0) {
                    console.log(`Node ID match: "${node.id}" with score ${idScore}`);
                }
            }

            // Check data attributes
            for (const attr of node.attributes || []) {
                if (attr?.name && attr?.value) {
                    const attributeScore = calculateTextMatchScore(attr.value, functionName, keyTerms);
                    if (attributeScore > 0) {
                        console.log(`Node attribute match: ${attr.name}="${attr.value}" with score ${attributeScore}`);
                        attrScore += attributeScore;
                    }
                }
            }

            if (attrScore > 0) {
                // Add or update the score for this node
                nodeScores.set(node, Math.max(attrScore, nodeScores.get(node) || 0));
            }
        }

        // If we found any matches, return the node with the highest score
        if (nodeScores.size > 0) {
            // Sort nodes by score (highest first)
            const sortedNodes = Array.from(nodeScores.entries()).sort((a, b) => b[1] - a[1]);
            const bestNode = sortedNodes[0][0];
            const bestScore = sortedNodes[0][1];

            console.log(`Selected best matching node with score ${bestScore}`);
            return bestNode;
        }

        return null;
    }

    /**
     * Calculate a match score between a text and a function name
     * @param {string} text - The text to check
     * @param {string} functionName - The function name to match
     * @param {string[]} keyTerms - Key terms extracted from the function name
     * @returns {number} - Match score (0-100)
     */
    function calculateTextMatchScore(text, functionName, keyTerms) {
        if (!text || !functionName) return 0;

        const lowerText = text.toLowerCase();
        const lowerFunctionName = functionName.toLowerCase();
        let score = 0;

        // Exact match gets highest score
        if (lowerText === lowerFunctionName) {
            score += 100;
        }
        // Contains match gets high score
        else if (lowerText.includes(lowerFunctionName)) {
            score += 90;
        }
        else if (lowerFunctionName.includes(lowerText)) {
            score += 80;
        }

        // Check for key terms
        let termMatches = 0;
        for (const term of keyTerms) {
            if (lowerText.includes(term.toLowerCase())) {
                termMatches++;
                score += 10; // Add 10 points for each matching term
            }
        }

        // Bonus for matching multiple terms
        if (termMatches > 1) {
            // Add bonus points based on the percentage of terms matched
            const termMatchPercentage = (termMatches / keyTerms.length) * 100;
            score += Math.min(50, termMatchPercentage); // Cap at 50 bonus points
        }

        // Word-level matching
        const words1 = lowerText.split(/\s+|_+/);
        const words2 = lowerFunctionName.split(/\s+|_+/);

        let matchingWords = 0;
        for (const word1 of words1) {
            if (word1.length < 3) continue; // Skip very short words
            for (const word2 of words2) {
                if (word2.length < 3) continue; // Skip very short words
                if (word1.includes(word2) || word2.includes(word1)) {
                    matchingWords++;
                    break;
                }
            }
        }

        if (matchingWords > 0) {
            // Calculate percentage of matching words
            const matchPercentage = (matchingWords / Math.max(words1.length, words2.length)) * 100;
            score += Math.min(30, matchPercentage); // Cap at 30 points for word-level matches
        }

        return score;
    }

    /**
     * Extract the label from a node element
     * @param {SVGElement} node - The node element
     * @returns {string|null} - The extracted label or null
     */
    function extractNodeLabel(node) {
        // First try to get the text element
        const textElement = node.querySelector('text');
        if (textElement) {
            return textElement.textContent.trim();
        }

        // If no text element, try to get the label from the node's ID or class
        const nodeId = node.id || '';
        if (nodeId.includes('flowchart-') || nodeId.includes('mermaid-')) {
            // Try to extract the label from data attributes or other sources
            const labelElement = node.querySelector('[label]');
            if (labelElement) {
                return labelElement.getAttribute('label');
            }
        }

        // Try to find any text content within the node
        const allText = [];
        const walker = document.createTreeWalker(node, NodeFilter.SHOW_TEXT);
        let currentNode;
        while (currentNode = walker.nextNode()) {
            const text = currentNode.textContent.trim();
            if (text) {
                allText.push(text);
            }
        }

        if (allText.length > 0) {
            return allText.join(' ');
        }

        return null;
    }

    /**
     * Calculate a match score between two strings
     * @param {string} text1 - First string
     * @param {string} text2 - Second string
     * @returns {number} - Match score (0-100)
     */
    function calculateMatchScore(text1, text2) {
        const t1 = text1.toLowerCase();
        const t2 = text2.toLowerCase();

        // Exact match
        if (t1 === t2) return 100;

        // One contains the other
        if (t1.includes(t2)) return 90;
        if (t2.includes(t1)) return 80;

        // Word-level matching
        const words1 = t1.split(/\s+|_+/);
        const words2 = t2.split(/\s+|_+/);

        let matchingWords = 0;
        for (const word1 of words1) {
            for (const word2 of words2) {
                if (word1.includes(word2) || word2.includes(word1)) {
                    matchingWords++;
                    break;
                }
            }
        }

        if (matchingWords === 0) return 0;

        // Calculate percentage of matching words
        const matchPercentage = (matchingWords / Math.max(words1.length, words2.length)) * 100;
        return Math.min(70, matchPercentage); // Cap at 70 for word-level matches
    }

    /**
     * Generate alternative search patterns for a function name
     * @param {string} functionName - The original function name
     * @returns {string[]} - Array of alternative search patterns
     */
    function generateSearchPatterns(functionName) {
        const patterns = [];

        // Add the original function name
        patterns.push(functionName);

        // Convert snake_case to space-separated words
        if (functionName.includes('_')) {
            const spaceSeparated = functionName.replace(/_/g, ' ').trim();
            patterns.push(spaceSeparated);

            // Add capitalized version (for diagram node labels)
            const capitalizedWords = spaceSeparated.split(' ')
                .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                .join(' ');
            patterns.push(capitalizedWords);
        }

        // Convert camelCase to space-separated words
        const camelCaseToSpaces = functionName.replace(/([a-z])([A-Z])/g, '$1 $2');
        if (camelCaseToSpaces !== functionName) {
            patterns.push(camelCaseToSpaces);

            // Add lowercase version
            patterns.push(camelCaseToSpaces.toLowerCase());

            // Add capitalized version
            const capitalizedWords = camelCaseToSpaces.split(' ')
                .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                .join(' ');
            patterns.push(capitalizedWords);
        }

        // Convert snake_case to camelCase
        if (functionName.includes('_')) {
            const camelCase = functionName.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
            patterns.push(camelCase);
        }

        // Break into individual words for multi-word functions
        const words = functionName
            .replace(/_/g, ' ')
            .replace(/([a-z])([A-Z])/g, '$1 $2')
            .split(' ')
            .filter(word => word.length > 2); // Include words of length 3 or more

        // Add individual words as patterns
        patterns.push(...words);

        // Add capitalized individual words
        const capitalizedWords = words.map(word => word.charAt(0).toUpperCase() + word.slice(1));
        patterns.push(...capitalizedWords);

        // Handle specific cases like 'build_jira_import' -> 'Build Jira Import Record'
        // by creating permutations of the words
        if (words.length >= 2) {
            // Create permutations of word order
            for (let i = 0; i < words.length; i++) {
                for (let j = 0; j < words.length; j++) {
                    if (i !== j) {
                        const permutation = words[i] + ' ' + words[j];
                        patterns.push(permutation);

                        // Add capitalized version
                        const capitalizedPermutation = capitalizedWords[i] + ' ' + capitalizedWords[j];
                        patterns.push(capitalizedPermutation);
                    }
                }
            }
        }

        // Remove duplicates and empty strings
        return [...new Set(patterns)].filter(p => p.trim() !== '');
    }

    /**
     * Find a node by examining its attributes and structure with advanced matching
     * @param {NodeList} nodes - List of nodes to search through
     * @param {string} searchText - The text to search for
     * @returns {SVGElement|null} - The best matching node or null
     */
    function findNodeByAttribute(nodes, searchText) {
        if (!nodes || nodes.length === 0 || !searchText) return null;

        console.log(`Searching for node by attribute with text: ${searchText}`);

        // Convert function name to various formats for matching
        const lowerSearchText = searchText.toLowerCase();

        // Extract key terms from the search text
        const keyTerms = extractKeyTerms(searchText);
        console.log(`Extracted key terms: ${keyTerms.join(', ')}`);

        // Store nodes with their match scores
        const nodeScores = new Map();

        // First pass: Look for nodes with text content that matches
        for (const node of nodes) {
            let score = 0;

            // Check for text content in the node
            const textElement = node.querySelector('text');
            if (textElement && textElement.textContent) {
                const nodeText = textElement.textContent.trim().toLowerCase();

                // Check for exact match
                if (nodeText === lowerSearchText) {
                    score += 100;
                    console.log(`Found node with exact text match: "${nodeText}"`);
                }
                // Check for partial match
                else if (nodeText.includes(lowerSearchText) || lowerSearchText.includes(nodeText)) {
                    score += 50;
                    console.log(`Found node with partial text match: "${nodeText}"`);
                }

                // Check if text contains all key terms
                let matchedTerms = 0;
                for (const term of keyTerms) {
                    if (nodeText.includes(term.toLowerCase())) {
                        matchedTerms++;
                    }
                }

                // If all terms match, give a high score
                if (matchedTerms === keyTerms.length && keyTerms.length > 0) {
                    score += 80;
                    console.log(`Found node with all key terms: "${nodeText}"`);
                }
                // If some terms match, give a proportional score
                else if (matchedTerms > 0) {
                    const termScore = Math.floor((matchedTerms / keyTerms.length) * 60);
                    score += termScore;
                    console.log(`Found node with ${matchedTerms}/${keyTerms.length} key terms: "${nodeText}" (score: ${termScore})`);
                }
            }

            // Check node ID
            if (node.id) {
                const nodeId = node.id.toLowerCase();
                // Exact match gets highest score
                if (nodeId === lowerSearchText) {
                    score += 100;
                    console.log(`Found node with exact ID match: ${node.id}`);
                }
                // Partial match gets lower score
                else if (nodeId.includes(lowerSearchText) || lowerSearchText.includes(nodeId)) {
                    score += 50;
                    console.log(`Found node with partial ID match: ${node.id}`);
                }

                // Check if ID contains any of the key terms
                for (const term of keyTerms) {
                    if (nodeId.includes(term.toLowerCase())) {
                        score += 20;
                        console.log(`Node ID contains key term '${term}': ${node.id}`);
                    }
                }
            }

            // Check data attributes
            if (node.attributes) {
                for (const attr of node.attributes) {
                    if (attr.name && attr.value) {
                        const attrValue = attr.value.toLowerCase();

                        // Check for data attributes that might contain function info
                        if (attr.name.startsWith('data-')) {
                            if (attrValue === lowerSearchText) {
                                score += 80;
                                console.log(`Found node with exact data attribute match: ${attr.name}=${attr.value}`);
                            } else if (attrValue.includes(lowerSearchText) || lowerSearchText.includes(attrValue)) {
                                score += 40;
                                console.log(`Found node with partial data attribute match: ${attr.name}=${attr.value}`);
                            }
                        }

                        // Check for any attribute containing key terms
                        for (const term of keyTerms) {
                            if (attrValue.includes(term.toLowerCase())) {
                                score += 15;
                                console.log(`Node attribute contains key term '${term}': ${attr.name}=${attr.value}`);
                            }
                        }
                    }
                }
            }

            // Check for class names that might contain function names
            if (node.className && node.className.baseVal) {
                const className = node.className.baseVal.toLowerCase();
                if (className.includes(lowerSearchText)) {
                    score += 30;
                    console.log(`Found node with matching class name: ${node.className.baseVal}`);
                }

                // Check if class contains any key terms
                for (const term of keyTerms) {
                    if (className.includes(term.toLowerCase())) {
                        score += 10;
                        console.log(`Node class contains key term '${term}': ${node.className.baseVal}`);
                    }
                }
            }

            // Check for title or aria-label attributes
            const title = node.getAttribute?.('title');
            if (title) {
                const titleText = title.toLowerCase();
                if (titleText === lowerSearchText) {
                    score += 90;
                    console.log(`Found node with exact title match: ${title}`);
                } else if (titleText.includes(lowerSearchText) || lowerSearchText.includes(titleText)) {
                    score += 45;
                    console.log(`Found node with partial title match: ${title}`);
                }

                // Check if title contains any key terms
                for (const term of keyTerms) {
                    if (titleText.includes(term.toLowerCase())) {
                        score += 20;
                        console.log(`Node title contains key term '${term}': ${title}`);
                    }
                }
            }

            // Store the score if it's positive
            if (score > 0) {
                nodeScores.set(node, score);
            }
        }

        // If we found any matches, return the node with the highest score
        if (nodeScores.size > 0) {
            // Sort nodes by score (highest first)
            const sortedNodes = Array.from(nodeScores.entries()).sort((a, b) => b[1] - a[1]);
            const bestNode = sortedNodes[0][0];
            const bestScore = sortedNodes[0][1];

            console.log(`Selected best matching node with score ${bestScore}`);
            return bestNode;
        }

        // If we didn't find any matches, check if there's a node with a matching ID
        // This is especially useful for mermaid diagrams where nodes often have IDs like J4
        for (const node of nodes) {
            // Try to find a node with an ID that contains any of the key terms
            if (node.id) {
                for (const term of keyTerms) {
                    if (node.id.toLowerCase().includes(term.toLowerCase())) {
                        console.log(`Found node with ID containing key term: ${node.id}`);
                        return node;
                    }
                }
            }
        }

        // If all else fails, find the most similar node using string similarity
        console.log('Finding most similar node using string similarity');
        return findMostSimilarNode(nodes, searchText);
    }

    /**
     * Find the most similar node to the search text using string similarity
     * @param {NodeList} nodes - List of nodes to search through
     * @param {string} searchText - The text to search for
     * @returns {SVGElement|null} - The most similar node or null
     */
    function findMostSimilarNode(nodes, searchText) {
        if (!nodes || nodes.length === 0 || !searchText) return null;

        console.log(`Finding most similar node to: ${searchText}`);

        // Convert search text to lowercase for case-insensitive comparison
        // (This is handled in the calculateStringSimilarity function)

        // Store nodes with their similarity scores
        const nodeScores = new Map();

        // Process each node
        for (const node of nodes) {
            // Try to get text from the node
            let nodeText = '';

            // Check for text element
            const textElement = node.querySelector('text');
            if (textElement && textElement.textContent) {
                nodeText = textElement.textContent.trim();
            }

            // If no text element, try to get text from foreignObject
            if (!nodeText) {
                const foreignObject = node.querySelector('foreignObject');
                if (foreignObject && foreignObject.textContent) {
                    nodeText = foreignObject.textContent.trim();
                }
            }

            // If still no text, try node ID
            if (!nodeText && node.id) {
                nodeText = node.id;
            }

            // If we have text, calculate similarity
            if (nodeText) {
                const similarity = calculateStringSimilarity(nodeText, searchText);
                nodeScores.set(node, similarity);
                console.log(`Node text: "${nodeText}", similarity: ${similarity.toFixed(4)}`);
            }
        }

        // If we found any matches, return the node with the highest similarity
        if (nodeScores.size > 0) {
            // Sort nodes by similarity (highest first)
            const sortedNodes = Array.from(nodeScores.entries()).sort((a, b) => b[1] - a[1]);
            const bestNode = sortedNodes[0][0];
            const bestScore = sortedNodes[0][1];
            const bestNodeText = bestNode.querySelector('text')?.textContent?.trim() ||
                                bestNode.querySelector('foreignObject')?.textContent?.trim() ||
                                bestNode.id || 'Unknown';

            console.log(`Selected most similar node: "${bestNodeText}" with similarity ${bestScore.toFixed(4)}`);
            return bestNode;
        }

        // If no nodes with text were found, return the first node as fallback
        console.log('No nodes with text found, falling back to first node');
        return nodes[0];
    }

    /**
     * Calculate string similarity using Levenshtein distance and other heuristics
     * @param {string} str1 - First string
     * @param {string} str2 - Second string
     * @returns {number} - Similarity score between 0 and 1 (1 being identical)
     */
    function calculateStringSimilarity(str1, str2) {
        if (!str1 || !str2) return 0;

        // Convert to lowercase for case-insensitive comparison
        const s1 = str1.toLowerCase();
        const s2 = str2.toLowerCase();

        // Exact match gets highest score
        if (s1 === s2) return 1.0;

        // One contains the other gets high score
        if (s1.includes(s2)) return 0.9;
        if (s2.includes(s1)) return 0.8;

        // Calculate Levenshtein distance
        const distance = levenshteinDistance(s1, s2);
        const maxLength = Math.max(s1.length, s2.length);

        // Convert distance to similarity (1 - normalized distance)
        let similarity = 1 - (distance / maxLength);

        // Word-level matching for multi-word strings
        const words1 = s1.split(/\s+|_+|-+|\.+/);
        const words2 = s2.split(/\s+|_+|-+|\.+/);

        if (words1.length > 1 || words2.length > 1) {
            let matchingWords = 0;
            let partialMatches = 0;

            for (const word1 of words1) {
                if (word1.length < 2) continue; // Skip very short words

                let bestWordMatch = 0;
                for (const word2 of words2) {
                    if (word2.length < 2) continue; // Skip very short words

                    if (word1 === word2) {
                        bestWordMatch = 1;
                        break;
                    } else if (word1.includes(word2) || word2.includes(word1)) {
                        bestWordMatch = Math.max(bestWordMatch, 0.8);
                    } else {
                        // Calculate word similarity
                        const wordDistance = levenshteinDistance(word1, word2);
                        const wordMaxLength = Math.max(word1.length, word2.length);
                        const wordSimilarity = 1 - (wordDistance / wordMaxLength);

                        bestWordMatch = Math.max(bestWordMatch, wordSimilarity);
                    }
                }

                if (bestWordMatch > 0.8) matchingWords++;
                else if (bestWordMatch > 0.5) partialMatches++;
            }

            // Calculate word match ratio
            const wordMatchRatio = (matchingWords + (partialMatches * 0.5)) / words1.length;

            // Combine character-level and word-level similarity
            similarity = (similarity + wordMatchRatio) / 2;
        }

        return similarity;
    }

    /**
     * Calculate Levenshtein distance between two strings
     * @param {string} str1 - First string
     * @param {string} str2 - Second string
     * @returns {number} - The Levenshtein distance
     */
    function levenshteinDistance(str1, str2) {
        const m = str1.length;
        const n = str2.length;

        // Create a matrix of size (m+1) x (n+1)
        const dp = Array(m + 1).fill(0).map(() => Array(n + 1).fill(0));

        // Initialize the matrix
        for (let i = 0; i <= m; i++) dp[i][0] = i;
        for (let j = 0; j <= n; j++) dp[0][j] = j;

        // Fill the matrix
        for (let i = 1; i <= m; i++) {
            for (let j = 1; j <= n; j++) {
                const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
                dp[i][j] = Math.min(
                    dp[i - 1][j] + 1,      // deletion
                    dp[i][j - 1] + 1,      // insertion
                    dp[i - 1][j - 1] + cost // substitution
                );
            }
        }

        return dp[m][n];
    }

    /**
     * Extract key terms from a function name for better matching
     * @param {string} functionName - The function name to extract terms from
     * @returns {string[]} - Array of key terms
     */
    function extractKeyTerms(functionName) {
        if (!functionName) return [];

        const terms = [];

        // Remove file path if present (everything before the last colon)
        let cleanName = functionName;
        if (functionName.includes(':')) {
            cleanName = functionName.split(':').pop() || functionName;
        }

        // Remove class name if present (everything before the last dot)
        if (cleanName.includes('.')) {
            cleanName = cleanName.split('.').pop() || cleanName;
        }

        // Split by common separators and filter out empty strings
        const rawTerms = cleanName.split(/[\s_\-.]+/).filter(term => term.length > 0);

        // Process each term
        for (const term of rawTerms) {
            // Add the original term
            terms.push(term);

            // Add lowercase version
            terms.push(term.toLowerCase());

            // Add camelCase split terms
            const camelCaseSplit = term.replace(/([a-z])([A-Z])/g, '$1 $2').split(' ');
            if (camelCaseSplit.length > 1) {
                terms.push(...camelCaseSplit);
                // Also add lowercase versions
                terms.push(...camelCaseSplit.map(t => t.toLowerCase()));
            }

            // Add capitalized version
            terms.push(term.charAt(0).toUpperCase() + term.slice(1));
        }

        // Add combined terms for better matching with node labels
        // For example, if searching for "build_jira_import", also look for "Build Jira Import"
        if (rawTerms.length >= 2) {
            // Add Title Case combined version (e.g., "Build Jira Import")
            const titleCaseTerms = rawTerms.map(t => t.charAt(0).toUpperCase() + t.slice(1).toLowerCase());
            terms.push(titleCaseTerms.join(' '));

            // Add partial combinations (e.g., "Build Jira")
            for (let i = 2; i <= rawTerms.length; i++) {
                const partialTerms = rawTerms.slice(0, i).map(t => t.charAt(0).toUpperCase() + t.slice(1).toLowerCase());
                terms.push(partialTerms.join(' '));
            }

            // Add with "Record" suffix since that's common in diagrams
            terms.push(titleCaseTerms.join(' ') + ' Record');
        }

        // Remove duplicates and return
        return [...new Set(terms)];
    }

    /**
     * Find a node with the exact text
     * @param {SVGElement} svg - The SVG element
     * @param {string} text - The text to search for
     * @returns {SVGElement|null} - The found node or null
     */
    function findNodeWithText(svg, text) {
        const textElements = svg.querySelectorAll('text');
        for (const textElement of textElements) {
            if (textElement.textContent && textElement.textContent.trim() === text) {
                return findParentNode(textElement);
            }
        }
        return null;
    }

    /**
     * Find nodes containing the text
     * @param {SVGElement} svg - The SVG element
     * @param {string} text - The text to search for
     * @returns {SVGElement[]} - Array of found nodes
     */
    function findNodesContainingText(svg, text) {
        const textElements = svg.querySelectorAll('text');
        const results = [];
        const resultScores = new Map(); // Map to store match quality scores

        const searchText = text.toLowerCase();

        // Log all text elements for debugging
        console.log(`Searching for text: "${searchText}" among ${textElements.length} elements`);

        for (const textElement of textElements) {
            // Skip elements with no text content
            if (!textElement.textContent) continue;

            const content = textElement.textContent.trim();
            const contentLower = content.toLowerCase();

            // Calculate match score (higher is better)
            let score = 0;

            // Exact match (case insensitive)
            if (contentLower === searchText) {
                score = 100;
                console.log(`Exact match found: "${content}"`);
            }
            // Content contains the search text
            else if (contentLower.includes(searchText)) {
                score = 80;
                console.log(`Content contains search text: "${content}" contains "${searchText}"`);
            }
            // Search text contains the content
            else if (searchText.includes(contentLower)) {
                score = 70;
                console.log(`Search text contains content: "${searchText}" contains "${contentLower}"`);
            }
            // Check for word-level matches
            else {
                // Split by spaces, underscores, and other separators
                const contentWords = contentLower.split(/[\s_-]+/).filter(w => w.length > 0);
                const searchWords = searchText.split(/[\s_-]+/).filter(w => w.length > 0);

                console.log(`Checking word-level matches between "${contentWords.join(', ')}" and "${searchWords.join(', ')}"`);

                // Count matching words
                let matchingWords = 0;
                let matchDetails = [];

                for (const searchWord of searchWords) {
                    for (const contentWord of contentWords) {
                        if (contentWord.includes(searchWord) || searchWord.includes(contentWord)) {
                            matchingWords++;
                            matchDetails.push(`${searchWord} ↔ ${contentWord}`);
                            break;
                        }
                    }
                }

                if (matchingWords > 0) {
                    // Calculate percentage of matching words
                    const searchWordCount = searchWords.length;
                    const contentWordCount = contentWords.length;
                    const matchPercentage = (matchingWords / Math.max(searchWordCount, contentWordCount)) * 100;

                    // Special case: If all search words match, give a higher score
                    if (matchingWords === searchWords.length) {
                        score = 65; // Higher than regular partial matches
                        console.log(`All search words match: ${matchDetails.join(', ')}`);
                    } else {
                        score = Math.min(60, matchPercentage); // Cap at 60 for partial matches
                        console.log(`Partial word matches (${matchingWords}/${Math.max(searchWordCount, contentWordCount)}): ${matchDetails.join(', ')}`);
                    }
                }
            }

            // If we have a score, add the node to results
            if (score > 0) {
                const node = findParentNode(textElement);
                if (node) {
                    results.push(node);
                    resultScores.set(node, score);
                    console.log(`Added node with score ${score}: "${content}"`);
                }
            }
        }

        // Sort results by score (highest first)
        results.sort((a, b) => resultScores.get(b) - resultScores.get(a));

        // Log the scores for debugging
        if (results.length > 0) {
            console.log('Match scores:');
            results.forEach(node => {
                const text = node.querySelector('text')?.textContent?.trim() || 'unknown';
                console.log(`  ${text}: ${resultScores.get(node)}`);
            });
        } else {
            console.log('No matches found');
        }

        return results;
    }

    /**
     * Find the parent node of a text element
     * @param {SVGElement} textElement - The text element
     * @returns {SVGElement|null} - The parent node or null
     */
    function findParentNode(textElement) {
        // Navigate up to find the node element (usually a g with class 'node')
        let current = textElement;
        while (current && current.tagName !== 'svg') {
            if (current.classList.contains('node')) {
                return current;
            }
            current = current.parentElement;
        }
        return null;
    }

    /**
     * Highlight and focus on a node
     * @param {SVGElement} node - The node to highlight and focus on
     */
    function highlightAndFocusNode(node) {
        if (!node) return;

        try {
            const svg = diagramContainer?.querySelector('svg');
            if (!svg) return;

            // Get the SVG dimensions
            const svgWidth = svg.width.baseVal.value;
            const svgHeight = svg.height.baseVal.value;

            // Calculate the container dimensions
            const containerWidth = diagramContainer?.clientWidth || 400;
            const containerHeight = diagramContainer?.clientHeight || 300;

            // Find the node's position
            let nodeX = 0;
            let nodeY = 0;
            let nodeWidth = 100;
            let nodeHeight = 50;

            // Try to get the node's position from its bounding box
            try {
                if (node.getBBox) {
                    const bbox = node.getBBox();
                    nodeX = bbox.x;
                    nodeY = bbox.y;
                    nodeWidth = bbox.width;
                    nodeHeight = bbox.height;
                }
            } catch (e) {
                console.log(`Error getting node bounding box: ${e.message}`);
            }

            // If we couldn't get the position, try to get it from a child element
            if (nodeX === 0 && nodeY === 0) {
                const shape = node.querySelector('rect, circle, polygon, path');
                if (shape && shape.getBBox) {
                    try {
                        const shapeBBox = shape.getBBox();
                        nodeX = shapeBBox.x;
                        nodeY = shapeBBox.y;
                        nodeWidth = shapeBBox.width;
                        nodeHeight = shapeBBox.height;
                    } catch (e) {
                        console.log(`Error getting shape bounding box: ${e.message}`);
                    }
                }
            }

            // If we still don't have a position, estimate it based on the node's index
            if (nodeX === 0 && nodeY === 0) {
                const allNodes = svg.querySelectorAll('.node');
                const nodeIndex = Array.from(allNodes).indexOf(node);
                if (nodeIndex >= 0) {
                    // Estimate position based on node index and diagram size
                    const totalNodes = allNodes.length;
                    const rowSize = Math.ceil(Math.sqrt(totalNodes));
                    const row = Math.floor(nodeIndex / rowSize);
                    const col = nodeIndex % rowSize;

                    nodeX = (col / rowSize) * svgWidth;
                    nodeY = (row / rowSize) * svgHeight;
                }
            }

            // If all else fails, use a more robust fallback
            if (nodeX === 0 && nodeY === 0) {
                // First try to find any visible part of the diagram
                const visibleElements = svg.querySelectorAll('rect, circle, polygon, path, text');
                if (visibleElements.length > 0) {
                    // Use the first visible element as a starting point
                    try {
                        const firstElement = visibleElements[0];
                        if (firstElement.getBBox) {
                            const bbox = firstElement.getBBox();
                            nodeX = bbox.x;
                            nodeY = bbox.y;
                            nodeWidth = Math.max(100, bbox.width);
                            nodeHeight = Math.max(50, bbox.height);
                            console.log(`Using fallback element position: (${nodeX}, ${nodeY})`);
                        }
                    } catch (e) {
                        console.log(`Error getting fallback element position: ${e.message}`);
                    }
                }

                // If still no position, use the center of the diagram
                if (nodeX === 0 && nodeY === 0) {
                    nodeX = svgWidth / 2;
                    nodeY = svgHeight / 2;
                    console.log(`Using diagram center as fallback: (${nodeX}, ${nodeY})`);
                }
            }

            // Calculate the center of the node
            const nodeCenterX = nodeX + nodeWidth / 2;
            const nodeCenterY = nodeY + nodeHeight / 2;

            // Store original node center for debugging
            const originalNodeCenterX = nodeCenterX;
            const originalNodeCenterY = nodeCenterY;
            console.log(`Original node center: (${originalNodeCenterX}, ${originalNodeCenterY})`);

            // For extremely wide diagrams, log additional debug info
            const isWideAspectRatio = svgWidth / svgHeight > 5;
            if (isWideAspectRatio) {
                console.log(`Extreme aspect ratio diagram with node at (${nodeX}, ${nodeY}) with size ${nodeWidth}x${nodeHeight}`);
                console.log(`Node center at (${nodeCenterX}, ${nodeCenterY})`);
            }

            // Log dimensions for debugging
            console.log(`Container: ${containerWidth}x${containerHeight}, Node: ${nodeWidth}x${nodeHeight} at (${nodeX}, ${nodeY})`);
            console.log(`SVG dimensions: ${svgWidth}x${svgHeight}`);

            // Calculate an appropriate scale based on the diagram size and node size
            let targetScale;

            // Calculate the aspect ratio of the diagram
            const diagramAspectRatio = svgWidth / svgHeight;
            console.log(`Diagram aspect ratio: ${diagramAspectRatio.toFixed(2)}, SVG dimensions: ${svgWidth}x${svgHeight}, Container: ${containerWidth}x${containerHeight}`);

            // COMPLETELY REVISED SCALING APPROACH
            // For diagrams that are too large to fit in the viewport, we need to scale them down
            // Calculate the ratio of SVG size to container size
            const widthRatio = svgWidth / containerWidth;
            const heightRatio = svgHeight / containerHeight;

            // Use the larger ratio to ensure the diagram fits in the viewport
            const fitRatio = Math.max(widthRatio, heightRatio);

            // Calculate a scale that will fit the entire diagram in the viewport with some padding
            let calculatedScale;

            if (fitRatio > 1) {
                // If the diagram is larger than the container, scale it down to fit
                calculatedScale = 0.8 / fitRatio; // 80% of the size needed to fit exactly
                console.log(`Diagram is larger than container, scaling down by factor: ${calculatedScale}`);
            } else {
                // If the diagram is smaller than the container, use a reasonable scale
                calculatedScale = 0.8;
                console.log(`Diagram fits in container, using default scale: ${calculatedScale}`);
            }

            // Ensure the scale is reasonable - not too small or too large
            targetScale = Math.max(0.1, Math.min(1.0, calculatedScale));
            console.log(`Final scale: ${targetScale}`);

            // SIMPLIFIED POSITIONING APPROACH
            // Instead of complex calculations, position the diagram in the center of the viewport
            let offsetX, offsetY;

            // For normal diagrams, center the entire diagram in the viewport
            if (diagramAspectRatio < 5) {
                // Center the entire diagram in the viewport
                offsetX = (containerWidth - svgWidth * targetScale) / 2;
                offsetY = (containerHeight - svgHeight * targetScale) / 2;
                console.log(`Centering normal diagram with offset: (${offsetX}, ${offsetY})`);
            } else {
                // For wide diagrams, we need a different approach
                // Focus on the node instead of the entire diagram

                // Calculate the position of the node center in the scaled coordinate system
                const scaledNodeCenterX = nodeCenterX * targetScale;
                const scaledNodeCenterY = nodeCenterY * targetScale;

                // Calculate the offset needed to center the node in the viewport
                offsetX = containerWidth / 2 - scaledNodeCenterX;
                offsetY = containerHeight / 2 - scaledNodeCenterY;
                console.log(`Centering wide diagram on node with offset: (${offsetX}, ${offsetY})`);
            }

            // Ensure offsets are reasonable to prevent the diagram from being positioned off-screen
            // Add padding to ensure the diagram is not at the edge
            const padding = 20;

            // Ensure the diagram is not positioned too far off-screen
            // For very wide diagrams, we need to ensure at least part of the diagram is visible
            if (diagramAspectRatio > 5) {
                // For wide diagrams, ensure the left edge is visible
                offsetX = Math.max(padding, offsetX);
                offsetY = Math.max(padding, offsetY);
                console.log(`Adjusted offset for wide diagram: (${offsetX}, ${offsetY})`);
            } else {
                // For normal diagrams, ensure the diagram is centered
                offsetX = Math.max(padding, Math.min(containerWidth - svgWidth * targetScale - padding, offsetX));
                offsetY = Math.max(padding, Math.min(containerHeight - svgHeight * targetScale - padding, offsetY));
                console.log(`Adjusted offset for normal diagram: (${offsetX}, ${offsetY})`);
            }

            console.log(`Final offset: (${offsetX}, ${offsetY})`);


            // Get the node text for logging
            const nodeText = node.querySelector('text')?.textContent?.trim() || node.textContent?.trim() || 'Unknown';
            console.log(`Focusing on node ${nodeText} with scale ${targetScale} and offset (${offsetX}, ${offsetY})`);

            // Always use animation for a smoother experience
            // The animation helps users understand the context and location
            // of the node within the larger diagram
            console.log(`Applying zoom with animation to scale ${targetScale} and offset (${offsetX}, ${offsetY})`);
            animateZoomAndPan(targetScale, offsetX, offsetY, 500); // 500ms duration for smooth transition

            // Add a highlight effect to the node
            highlightNode(node);
        } catch (error) {
            console.error('Error focusing on node:', error);
            // Fallback to a simple positioning if there's an error
            applyZoom(20, 20, 0.8);
        }
    }

    /**
     * Apply zoom and pan to a new position and scale without animation
     * @param {number} targetScale - The target scale
     * @param {number} targetOffsetX - The target X offset
     * @param {number} targetOffsetY - The target Y offset
     * @param {number} [customDuration=300] - Optional parameter kept for compatibility
     */
    function animateZoomAndPan(targetScale, targetOffsetX, targetOffsetY, customDuration = 300) {
        // Ensure target values are valid numbers to prevent NaN issues
        if (isNaN(targetScale) || isNaN(targetOffsetX) || isNaN(targetOffsetY)) {
            console.error(`Invalid targets: scale=${targetScale}, offsetX=${targetOffsetX}, offsetY=${targetOffsetY}`);
            // Use safe defaults
            targetScale = isNaN(targetScale) ? 2.0 : targetScale;
            targetOffsetX = isNaN(targetOffsetX) ? 0 : targetOffsetX;
            targetOffsetY = isNaN(targetOffsetY) ? 0 : targetOffsetY;
        }

        // Apply the zoom immediately without animation
        applyZoom(targetOffsetX, targetOffsetY, targetScale);
        initialRendering = false;
    }

    /**
     * Easing function for smoother animations
     * @param {number} t - The progress (0-1)
     * @returns {number} - The eased progress (0-1)
     */
    function easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    }

    /**
     * Highlight a node with a visual effect (simplified without animations)
     * @param {SVGElement} node - The node to highlight
     */
    function highlightNode(node) {
        if (!node) return;

        try {
            // Remove any existing highlights
            const svg = diagramContainer?.querySelector('svg');
            if (!svg) return;

            const existingHighlights = svg.querySelectorAll('.node-highlight');
            existingHighlights.forEach(el => el.remove?.());

            // Get the node's shape element (rect, circle, etc.)
            const shape = node.querySelector?.('rect, circle, polygon, path');
            if (!shape) return;

            // Apply highlight directly to the shape without animations
            if (shape.setAttribute) {
                // Store original attributes to restore later
                const originalStroke = shape.getAttribute('stroke');
                const originalStrokeWidth = shape.getAttribute('stroke-width');

                // Apply highlight
                shape.setAttribute('stroke', '#ff4d4d');
                shape.setAttribute('stroke-width', '3px');

                // Restore original attributes after a delay
                setTimeout(() => {
                    if (originalStroke) {
                        shape.setAttribute('stroke', originalStroke);
                    }
                    if (originalStrokeWidth) {
                        shape.setAttribute('stroke-width', originalStrokeWidth);
                    }
                }, 3000);
            }

            // Also highlight the text
            const textElement = node.querySelector?.('text');
            if (textElement) {
                const originalFill = textElement.getAttribute('fill');
                const originalWeight = textElement.getAttribute('font-weight');

                textElement.setAttribute?.('font-weight', 'bold');
                textElement.setAttribute?.('fill', '#ff4d4d');

                // Restore original attributes after a delay
                setTimeout(() => {
                    if (originalFill) {
                        textElement.setAttribute('fill', originalFill);
                    } else {
                        textElement.removeAttribute?.('fill');
                    }
                    if (originalWeight) {
                        textElement.setAttribute('font-weight', originalWeight);
                    } else {
                        textElement.removeAttribute?.('font-weight');
                    }
                }, 3000);
            }
        } catch (error) {
            console.error('Error highlighting node:', error);
        }
    }

    /**
     * Export the diagram as a PNG
     */
    function exportAsPng() {
        const svg = diagramContainer?.querySelector('svg');
        if (!svg) {
            showError('No diagram to export');
            return;
        }

        try {
            // Get the SVG dimensions
            const svgWidth = svg.width.baseVal.value;
            const svgHeight = svg.height.baseVal.value;

            // Create a canvas with the same dimensions
            const canvas = document.createElement('canvas');
            canvas.width = svgWidth * currentScale;
            canvas.height = svgHeight * currentScale;

            // Get the canvas context
            const ctx = canvas.getContext('2d');
            if (!ctx) {
                showError('Failed to create canvas context');
                return;
            }

            // Fill the background
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Draw a subtle grid background
            ctx.strokeStyle = '#f0f0f0';
            ctx.lineWidth = 1;

            const gridSize = 20 * currentScale;
            for (let x = 0; x <= canvas.width; x += gridSize) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, canvas.height);
                ctx.stroke();
            }

            for (let y = 0; y <= canvas.height; y += gridSize) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(canvas.width, y);
                ctx.stroke();
            }

            // Create an image from the SVG
            const img = new Image();
            img.onload = function() {
                // Draw the image on the canvas with scaling
                ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

                // Convert the canvas to a data URL
                const dataUrl = canvas.toDataURL('image/png');

                // Send the data URL to the extension
                vscode.postMessage({
                    type: 'exportAsPng',
                    data: dataUrl
                });
            };

            // Convert the SVG to a data URL
            const serializer = new XMLSerializer();
            const svgString = serializer.serializeToString(svg);
            const svgBlob = new Blob([svgString], { type: 'image/svg+xml' });
            const url = URL.createObjectURL(svgBlob);

            img.src = url;
        } catch (error) {
            console.error('Error exporting diagram:', error);
            showError(`Failed to export diagram: ${error.message}`);
        }
    }
})();
