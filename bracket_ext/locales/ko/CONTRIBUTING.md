# Roo Code에 기여하기

Roo Code에 기여하는 데 관심을 가져주셔서 기쁩니다. 버그를 수정하든, 기능을 추가하든, 문서를 개선하든, 모든 기여는 Roo Code를 더 스마트하게 만듭니다! 우리 커뮤니티를 활기차고 친절하게 유지하기 위해, 모든 구성원은 우리의 [행동 강령](CODE_OF_CONDUCT.md)을 준수해야 합니다.

## 우리 커뮤니티에 참여하세요

모든 기여자가 우리의 [Discord 커뮤니티](https://discord.gg/roocode)에 참여할 것을 강력히 권장합니다! Discord 서버의 일원이 되면 다음과 같은 도움을 받을 수 있습니다:

- 기여에 대한 실시간 도움과 지침 얻기
- 다른 기여자 및 핵심 팀원과 연결
- 프로젝트 개발 및 우선순위에 대한 최신 정보 유지
- Roo Code의 미래를 형성하는 토론에 참여
- 다른 개발자와의 협업 기회 찾기

## 버그 또는 이슈 보고하기

버그 보고는 모두를 위해 Roo Code를 더 좋게 만드는 데 도움이 됩니다! 새 이슈를 만들기 전에, 중복을 피하기 위해 [기존 이슈 검색](https://github.com/RooVetGit/Roo-Code/issues)을 해주세요. 버그를 보고할 준비가 되면, 관련 정보를 작성하는 데 도움이 되는 템플릿이 있는 [이슈 페이지](https://github.com/RooVetGit/Roo-Code/issues/new/choose)로 이동하세요.

<blockquote class='warning-note'>
     🔐 <b>중요:</b> 보안 취약점을 발견한 경우, <a href="https://github.com/RooVetGit/Roo-Code/security/advisories/new">비공개로 보고하기 위해 Github 보안 도구를 사용하세요</a>.
</blockquote>

## 작업할 내용 결정하기

첫 기여를 위한 좋은 시작점을 찾고 계신가요? 우리의 [Roo Code 이슈](https://github.com/orgs/RooVetGit/projects/1) Github 프로젝트의 "Issue [Unassigned]" 섹션에서 이슈를 확인하세요. 이러한 이슈들은 새로운 기여자와 우리가 도움을 필요로 하는 영역을 위해 특별히 선별되었습니다!

우리는 [문서](https://docs.roocode.com/)에 대한 기여도 환영합니다! 오타 수정, 기존 가이드 개선 또는 새로운 교육 콘텐츠 생성 등 - 모든 사람이 Roo Code를 최대한 활용할 수 있도록 도와주는 커뮤니티 기반 리소스 저장소를 구축하고 싶습니다. 모든
페이지에서 "Edit this page"를 클릭하여 파일을 편집할 수 있는 Github의 적절한 위치로 빠르게 이동하거나, https://github.com/RooVetGit/Roo-Code-Docs에 직접 접근할 수 있습니다.

더 큰 기능 작업을 계획하고 있다면, Roo Code의 비전과 일치하는지 논의할 수 있도록 먼저 [기능 요청](https://github.com/RooVetGit/Roo-Code/discussions/categories/feature-requests?discussions_q=is%3Aopen+category%3A%22Feature+Requests%22+sort%3Atop)을 생성해주세요. 또한 아이디어가 우리의 전략적 방향과 일치하는지 확인하기 위해 아래의 [프로젝트 로드맵](#프로젝트-로드맵)을 확인할 수도 있습니다.

## 프로젝트 로드맵

Roo Code는 우리의 우선순위와 미래 방향을 안내하는 명확한 개발 로드맵을 가지고 있습니다. 우리의 로드맵을 이해하면 다음과 같은 도움을 받을 수 있습니다:

- 프로젝트 목표에 맞게 기여 조정
- 당신의 전문 지식이 가장 가치 있는 영역 식별
- 특정 디자인 결정 배경 이해
- 우리의 비전을 지원하는 새로운 기능에 대한 영감 찾기

현재 로드맵은 여섯 가지 주요 기둥에 초점을 맞추고 있습니다:

### 제공업체 지원

가능한 한 많은 제공업체를 지원하는 것을 목표로 합니다:

- 더 다재다능한 "OpenAI 호환" 지원
- xAI, Microsoft Azure AI, Alibaba Cloud Qwen, IBM Watsonx, Together AI, DeepInfra, Fireworks AI, Cohere, Perplexity AI, FriendliAI, Replicate
- Ollama와 LM Studio에 대한 향상된 지원

### 모델 지원

로컬 모델을 포함하여 가능한 한 많은 모델에서 Roo가 잘 작동하기를 원합니다:

- 사용자 정의 시스템 프롬프팅 및 워크플로우를 통한 로컬 모델 지원
- 벤치마킹 평가 및 테스트 케이스

### 시스템 지원

Roo가 모든 사람의 컴퓨터에서 잘 작동하기를 원합니다:

- 크로스 플랫폼 터미널 통합
- Mac, Windows 및 Linux에 대한 강력하고 일관된 지원

### 문서화

모든 사용자와 기여자를 위한 포괄적이고 접근 가능한 문서를 원합니다:

- 확장된 사용자 가이드 및 튜토리얼
- 명확한 API 문서
- 기여자를 위한 더 나은 가이드
- 다국어 문서 리소스
- 대화형 예제 및 코드 샘플

### 안정성

버그 수를 크게 줄이고 자동화된 테스트를 증가시키고자 합니다:

- 디버그 로깅 스위치
- 버그/지원 요청과 함께 보낼 수 있는 "기기/작업 정보" 복사 버튼

### 국제화

Roo가 모든 사람의 언어를 말하기를 원합니다:

- 我们希望 Roo Code 说每个人的语言
- Queremos que Roo Code hable el idioma de todos
- हम चाहते हैं कि Roo Code हर किसी की भाषा बोले
- نريد أن يتحدث Roo Code لغة الجميع

우리는 특히 로드맵 목표를 발전시키는 기여를 환영합니다. 이러한 기둥에 맞는 작업을 하고 있다면, PR 설명에서 이를 언급해 주세요.

## 개발 설정

1. 저장소 **클론**:

```sh
git clone https://github.com/RooVetGit/Roo-Code.git
```

2. **의존성 설치**:

```sh
npm run install:all
```

3. **웹뷰 시작(HMR이 있는 Vite/React 앱)**:

```sh
npm run dev
```

4. **디버깅**:
   VSCode에서 `F5`를 누르거나(**실행** → **디버깅 시작**) Roo Code가 로드된 새 세션을 엽니다.

웹뷰의 변경 사항은 즉시 나타납니다. 코어 확장에 대한 변경 사항은 확장 호스트를 다시 시작해야 합니다.

또는 .vsix를 빌드하고 VSCode에 직접 설치할 수 있습니다:

```sh
npm run build
```

`bin/` 디렉토리에 `.vsix` 파일이 나타나며 다음 명령으로 설치할 수 있습니다:

```sh
code --install-extension bin/roo-cline-<version>.vsix
```

## 코드 작성 및 제출

누구나 Roo Code에 코드를 기여할 수 있지만, 기여가 원활하게 통합될 수 있도록 다음 지침을 따라주시기 바랍니다:

1. **Pull Request 집중**

    - PR을 단일 기능 또는 버그 수정으로 제한
    - 더 큰 변경사항을 더 작고 관련된 PR로 분할
    - 독립적으로 검토할 수 있는 논리적인 커밋으로 변경사항 분할

2. **코드 품질**

    - 모든 PR은 린팅 및 포맷팅을 포함한 CI 검사를 통과해야 함
    - 제출하기 전에 모든 ESLint 경고나 오류 해결
    - Ellipsis, 자동화된 코드 리뷰 도구의 모든 피드백에 응답
    - TypeScript 모범 사례를 따르고 타입 안전성 유지

3. **테스팅**

    - 새로운 기능에 대한 테스트 추가
    - 모든 테스트가 통과하는지 확인하기 위해 `npm test` 실행
    - 변경사항이 영향을 미치는 경우 기존 테스트 업데이트
    - 적절한 경우 단위 테스트와 통합 테스트 모두 포함

4. **커밋 가이드라인**

    - 명확하고 설명적인 커밋 메시지 작성
    - #이슈-번호를 사용하여 커밋에서 관련 이슈 참조

5. **제출 전**

    - 최신 main에 브랜치 리베이스
    - 브랜치가 성공적으로 빌드되는지 확인
    - 모든 테스트가 통과하는지 다시 확인
    - 디버깅 코드나 콘솔 로그가 있는지 변경사항 검토

6. **Pull Request 설명**
    - 변경사항이 무엇을 하는지 명확하게 설명
    - 변경사항을 테스트하는 단계 포함
    - 모든 주요 변경사항 나열
    - UI 변경사항에 대한 스크린샷 추가

## 기여 동의

Pull request를 제출함으로써, 귀하의 기여는 프로젝트와 동일한 라이선스([Apache 2.0](../LICENSE))에 따라 라이선스가 부여된다는 데 동의합니다.
