# Roo Codeへの貢献

Roo Codeへの貢献に興味を持っていただき、ありがとうございます。バグの修正、機能の追加、またはドキュメントの改善など、すべての貢献がRoo Codeをよりスマートにします！コミュニティを活気に満ちた歓迎的なものに保つため、すべてのメンバーは[行動規範](CODE_OF_CONDUCT.md)を順守する必要があります。

## コミュニティに参加する

すべての貢献者に[Discordコミュニティ](https://discord.gg/roocode)への参加を強く推奨します！Discordサーバーに参加することで以下のメリットがあります：

- 貢献に関するリアルタイムのヘルプとガイダンスを得られる
- 他の貢献者やコアチームメンバーとつながれる
- プロジェクトの開発と優先事項について最新情報を得られる
- Roo Codeの将来を形作るディスカッションに参加できる
- 他の開発者とのコラボレーションの機会を見つけられる

## バグや問題の報告

バグレポートはRoo Codeをより良くするのに役立ちます！新しい課題を作成する前に、重複を避けるために[既存の課題を検索](https://github.com/RooVetGit/Roo-Code/issues)してください。バグを報告する準備ができたら、関連情報の入力を手助けするテンプレートが用意されている[課題ページ](https://github.com/RooVetGit/Roo-Code/issues/new/choose)にアクセスしてください。

<blockquote class='warning-note'>
     🔐 <b>重要：</b> セキュリティ脆弱性を発見した場合は、<a href="https://github.com/RooVetGit/Roo-Code/security/advisories/new">Githubセキュリティツールを使用して非公開で報告</a>してください。
</blockquote>

## 取り組む内容の決定

良い最初の貢献を探していますか？[Roo Code Issues](https://github.com/orgs/RooVetGit/projects/1) Githubプロジェクトの「Issue [Unassigned]」セクションの課題をチェックしてください。これらは新しい貢献者や私たちが助けを必要としている領域のために特別に選ばれています！

また、[ドキュメント](https://docs.roocode.com/)への貢献も歓迎します！タイプミスの修正、既存ガイドの改善、または新しい教育コンテンツの作成など、Roo Codeを最大限に活用するためのコミュニティ主導のリソースリポジトリの構築を目指しています。任意のページで「Edit this page」をクリックすると、ファイルを編集するためのGithubの適切な場所にすぐに移動できます。または、https://github.com/RooVetGit/Roo-Code-Docs に直接アクセスすることもできます。

より大きな機能に取り組む予定がある場合は、Roo Codeのビジョンに合致するかどうかを議論するために、まず[機能リクエスト](https://github.com/RooVetGit/Roo-Code/discussions/categories/feature-requests?discussions_q=is%3Aopen+category%3A%22Feature+Requests%22+sort%3Atop)を作成してください。また、アイデアが私たちの戦略的方向性に合っているかどうかを確認するために、下記の[プロジェクトロードマップ](#プロジェクトロードマップ)をチェックすることもできます。

## プロジェクトロードマップ

Roo Codeには、私たちの優先事項と将来の方向性を導く明確な開発ロードマップがあります。私たちのロードマップを理解することで、以下のような助けになります：

- あなたの貢献をプロジェクトの目標に合わせる
- あなたの専門知識が最も価値がある領域を特定する
- 特定のデザイン決定の背景を理解する
- 私たちのビジョンをサポートする新機能のインスピレーションを得る

現在のロードマップは、6つの主要な柱に焦点を当てています：

### プロバイダーサポート

できるだけ多くのプロバイダーをサポートすることを目指しています：

- より汎用的な「OpenAI互換」サポート
- xAI、Microsoft Azure AI、Alibaba Cloud Qwen、IBM Watsonx、Together AI、DeepInfra、Fireworks AI、Cohere、Perplexity AI、FriendliAI、Replicate
- OllamaとLM Studioの強化されたサポート

### モデルサポート

ローカルモデルを含め、できるだけ多くのモデルでRooが良好に動作することを望んでいます：

- カスタムシステムプロンプティングとワークフローを通じたローカルモデルサポート
- ベンチマーク評価とテストケース

### システムサポート

Rooが誰のコンピュータでも良好に動作することを望んでいます：

- クロスプラットフォームターミナル統合
- Mac、Windows、Linuxの強力で一貫したサポート

### ドキュメンテーション

すべてのユーザーと貢献者のための包括的でアクセスしやすいドキュメントを望んでいます：

- 拡張されたユーザーガイドとチュートリアル
- 明確なAPIドキュメント
- 貢献者のためのより良いガイダンス
- 多言語ドキュメントリソース
- インタラクティブな例とコードサンプル

### 安定性

バグの数を大幅に減らし、自動テストを増やすことを望んでいます：

- デバッグロギングスイッチ
- バグ/サポートリクエストと一緒に送信するための「マシン/タスク情報」コピーボタン

### 国際化

Rooが誰の言語も話すことを望んでいます：

- 我们希望 Roo Code 说每个人的语言
- Queremos que Roo Code hable el idioma de todos
- हम चाहते हैं कि Roo Code हर किसी की भाषा बोले
- نريد أن يتحدث Roo Code لغة الجميع

私たちは特に、ロードマップの目標を前進させる貢献を歓迎します。これらの柱に沿った何かに取り組んでいる場合は、PRの説明でそれについて言及してください。

## 開発のセットアップ

1. リポジトリを**クローン**します：

```sh
git clone https://github.com/RooVetGit/Roo-Code.git
```

2. **依存関係をインストール**します：

```sh
npm run install:all
```

3. **ウェブビュー（Vite/ReactアプリとHMR）を起動**します：

```sh
npm run dev
```

4. **デバッグ**：
   VSCodeで`F5`キー（または**実行**→**デバッグの開始**）を押すと、Roo Codeがロードされた新しいセッションが開きます。

ウェブビューへの変更はすぐに反映されます。コア拡張機能への変更は、拡張機能ホストの再起動が必要です。

または、.vsixファイルをビルドしてVSCodeに直接インストールすることもできます：

```sh
npm run build
```

`bin/`ディレクトリに`.vsix`ファイルが作成され、以下のコマンドでインストールできます：

```sh
code --install-extension bin/roo-cline-<version>.vsix
```

## コードの作成と提出

誰でもRoo Codeにコードを貢献できますが、貢献がスムーズに統合されるように以下のガイドラインに従ってください：

1. **プルリクエストを焦点を絞ったものにする**

    - PRを単一の機能またはバグ修正に限定する
    - より大きな変更を小さく関連したPRに分割する
    - 変更を独立してレビューできる論理的なコミットに分ける

2. **コード品質**

    - すべてのPRはlintingとフォーマットの両方を含むCIチェックに合格する必要がある
    - 提出前にESLintの警告やエラーを解決する
    - 自動コードレビューツールであるEllipsisからのすべてのフィードバックに対応する
    - TypeScriptのベストプラクティスに従い、型の安全性を維持する

3. **テスト**

    - 新機能にはテストを追加する
    - `npm test`を実行してすべてのテストが合格することを確認する
    - 変更が影響する既存のテストを更新する
    - 適切な場合は単体テストと統合テストの両方を含める

4. **コミットガイドライン**

    - 明確で説明的なコミットメッセージを書く
    - #issue-number を使用してコミットで関連する課題を参照する

5. **提出前に**

    - 最新のmainブランチに対してあなたのブランチをリベースする
    - あなたのブランチが正常にビルドされることを確認する
    - すべてのテストが合格していることを再確認する
    - デバッグコードやコンソールログがないか変更を見直す

6. **プルリクエストの説明**
    - 変更内容を明確に説明する
    - 変更をテストするための手順を含める
    - 破壊的変更がある場合はリストアップする
    - UI変更の場合はスクリーンショットを追加する

## 貢献同意

プルリクエストを提出することにより、あなたの貢献がプロジェクトと同じライセンス（[Apache 2.0](../LICENSE)）の下でライセンスされることに同意したものとみなします。
