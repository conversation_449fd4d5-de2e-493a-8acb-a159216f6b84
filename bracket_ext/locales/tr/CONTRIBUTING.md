# Roo Code'a Katkıda Bulunma

Roo Code'a katkıda bulunmakla ilgilendiğiniz için çok mutluyuz. İster bir hatayı düzeltiyor, ister bir özellik ekliyor, ister belgelerimizi geliştiri<PERSON><PERSON> o<PERSON>, her katkı Roo Code'u daha akıllı hale getirir! Topluluğumuzu canlı ve misafirperver tutmak için tüm üyelerin [<PERSON><PERSON>ran<PERSON><PERSON> Kuralları](CODE_OF_CONDUCT.md)'na uyması gerekir.

## Topluluğumuza Katılın

Tüm katkıda bulunanları [Discord topluluğumuza](https://discord.gg/roocode) katılmaya şiddetle teşvik ediyoruz! Discord sunucumuzun bir parçası olmak size şu konularda yardımcı olur:

- Katkılarınız hakkında gerçek zamanlı yardım ve rehberlik alın
- <PERSON><PERSON><PERSON> katkıda bulunanlar ve çekirdek ekip üyeleriyle bağlantı kurun
- Proje gelişmeleri ve öncelikleri hakkında güncel kalın
- Roo Code'un geleceğini şekillendiren tartışmalara katılın
- Diğer geliştiricilerle işbirliği fırsatları bulun

## Hataları veya Sorunları Bildirme

Hata raporları Roo Code'u herkes için daha iyi hale getirmeye yardımcı olur! Yeni bir sorun oluşturmadan önce, lütfen yinelemeleri önlemek için [mevcut olanları arayın](https://github.com/RooVetGit/Roo-Code/issues). Bir hatayı bildirmeye hazır olduğunuzda, ilgili bilgileri doldurmanıza yardımcı olacak bir şablon bulacağınız [sorunlar sayfamıza](https://github.com/RooVetGit/Roo-Code/issues/new/choose) gidin.

<blockquote class='warning-note'>
     🔐 <b>Önemli:</b> Bir güvenlik açığı keşfederseniz, lütfen <a href="https://github.com/RooVetGit/Roo-Code/security/advisories/new">özel olarak bildirmek için Github güvenlik aracını kullanın</a>.
</blockquote>

## Ne Üzerinde Çalışacağınıza Karar Verme

İyi bir ilk katkı mı arıyorsunuz? [Roo Code Sorunları](https://github.com/orgs/RooVetGit/projects/1) Github Projemizin "Issue [Unassigned]" bölümündeki sorunları kontrol edin. Bunlar özellikle yeni katkıda bulunanlar ve biraz yardıma ihtiyaç duyduğumuz alanlar için seçilmiştir!

[Belgelerimize](https://docs.roocode.com/) katkıları da memnuniyetle karşılıyoruz! İster yazım hatalarını düzeltmek, mevcut kılavuzları geliştirmek veya yeni eğitim içeriği oluşturmak olsun - herkesin Roo Code'dan en iyi şekilde yararlanmasına yardımcı olan topluluk odaklı bir kaynak deposu oluşturmak istiyoruz. Dosyayı düzenlemek için Github'daki doğru yere hızlıca gitmek için herhangi bir sayfada "Edit this page" düğmesine tıklayabilir veya doğrudan https://github.com/RooVetGit/Roo-Code-Docs adresine dalabilirsiniz.

Daha büyük bir özellik üzerinde çalışmayı planlıyorsanız, lütfen önce bir [özellik isteği](https://github.com/RooVetGit/Roo-Code/discussions/categories/feature-requests?discussions_q=is%3Aopen+category%3A%22Feature+Requests%22+sort%3Atop) oluşturun, böylece Roo Code'un vizyonuyla uyumlu olup olmadığını tartışabiliriz. Ayrıca, fikrinizin stratejik yönümüze uyup uymadığını görmek için aşağıdaki [Proje Yol Haritası](#proje-yol-haritası)'nı kontrol edebilirsiniz.

## Proje Yol Haritası

Roo Code, önceliklerimizi ve gelecekteki yönümüzü yönlendiren net bir geliştirme yol haritasına sahiptir. Yol haritamızı anlamak size şu konularda yardımcı olabilir:

- Katkılarınızı proje hedefleriyle uyumlu hale getirmek
- Uzmanlığınızın en değerli olacağı alanları belirlemek
- Belirli tasarım kararlarının arkasındaki bağlamı anlamak
- Vizyonumuzu destekleyen yeni özellikler için ilham bulmak

Mevcut yol haritamız altı temel sütun üzerine odaklanmaktadır:

### Sağlayıcı Desteği

Mümkün olduğunca çok sağlayıcıyı desteklemeyi hedefliyoruz:

- Daha çok yönlü "OpenAI Uyumlu" destek
- xAI, Microsoft Azure AI, Alibaba Cloud Qwen, IBM Watsonx, Together AI, DeepInfra, Fireworks AI, Cohere, Perplexity AI, FriendliAI, Replicate
- Ollama ve LM Studio için geliştirilmiş destek

### Model Desteği

Roo'nun yerel modeller de dahil olmak üzere mümkün olduğunca çok modelde iyi çalışmasını istiyoruz:

- Özel sistem yönlendirmesi ve iş akışları aracılığıyla yerel model desteği
- Kıyaslama değerlendirmeleri ve test vakaları

### Sistem Desteği

Roo'nun herkesin bilgisayarında iyi çalışmasını istiyoruz:

- Çapraz platform terminal entegrasyonu
- Mac, Windows ve Linux için güçlü ve tutarlı destek

### Dokümantasyon

Tüm kullanıcılar ve katkıda bulunanlar için kapsamlı, erişilebilir dokümantasyon istiyoruz:

- Genişletilmiş kullanıcı kılavuzları ve öğreticiler
- Net API dokümantasyonu
- Katkıda bulunanlar için daha iyi rehberlik
- Çok dilli dokümantasyon kaynakları
- Etkileşimli örnekler ve kod örnekleri

### Kararlılık

Hata sayısını önemli ölçüde azaltmak ve otomatik testleri artırmak istiyoruz:

- Hata ayıklama günlüğü anahtarı
- Hata/destek istekleriyle birlikte göndermek için "Makine/Görev Bilgisi" kopyalama düğmesi

### Uluslararasılaştırma

Roo'nun herkesin dilini konuşmasını istiyoruz:

- 我们希望 Roo Code 说每个人的语言
- Queremos que Roo Code hable el idioma de todos
- हम चाहते हैं कि Roo Code हर किसी की भाषा बोले
- نريد أن يتحدث Roo Code لغة الجميع

Özellikle yol haritamızın hedeflerini ileriye taşıyan katkıları memnuniyetle karşılıyoruz. Bu sütunlarla uyumlu bir şey üzerinde çalışıyorsanız, lütfen PR açıklamanızda bundan bahsedin.

## Geliştirme Kurulumu

1. Depoyu **klonlayın**:

```sh
git clone https://github.com/RooVetGit/Roo-Code.git
```

2. **Bağımlılıkları yükleyin**:

```sh
npm run install:all
```

3. **Webview'ı başlatın (HMR ile Vite/React uygulaması)**:

```sh
npm run dev
```

4. **Hata ayıklama**:
   VSCode'da `F5` tuşuna basın (veya **Run** → **Start Debugging**) Roo Code yüklenmiş yeni bir oturum açmak için.

Webview'daki değişiklikler anında görünecektir. Ana uzantıdaki değişiklikler uzantı ana bilgisayarının yeniden başlatılmasını gerektirecektir.

Alternatif olarak, bir .vsix dosyası oluşturabilir ve doğrudan VSCode'a kurabilirsiniz:

```sh
npm run build
```

`bin/` dizininde bir `.vsix` dosyası görünecek ve şu komutla kurulabilir:

```sh
code --install-extension bin/roo-cline-<version>.vsix
```

## Kod Yazma ve Gönderme

Herkes Roo Code'a kod katkısında bulunabilir, ancak katkılarınızın sorunsuz bir şekilde entegre edilebilmesi için bu kurallara uymanızı rica ediyoruz:

1. **Pull Request'leri Odaklı Tutun**

    - PR'leri tek bir özellik veya hata düzeltmesiyle sınırlayın
    - Daha büyük değişiklikleri daha küçük, ilgili PR'lere bölün
    - Değişiklikleri bağımsız olarak incelenebilen mantıklı commitlere bölün

2. **Kod Kalitesi**

    - Tüm PR'ler hem linting hem de formatlama içeren CI kontrollerini geçmelidir
    - Göndermeden önce tüm ESLint uyarılarını veya hatalarını çözün
    - Otomatik kod inceleme aracımız Ellipsis'ten gelen tüm geri bildirimlere yanıt verin
    - TypeScript en iyi uygulamalarını takip edin ve tip güvenliğini koruyun

3. **Test Etme**

    - Yeni özellikler için testler ekleyin
    - Tüm testlerin geçtiğinden emin olmak için `npm test` çalıştırın
    - Değişiklikleriniz etkiliyorsa mevcut testleri güncelleyin
    - Uygun olduğunda hem birim testlerini hem de entegrasyon testlerini dahil edin

4. **Commit Yönergeleri**

    - Net, açıklayıcı commit mesajları yazın
    - #issue-number kullanarak commitlerdeki ilgili sorunlara atıfta bulunun

5. **Göndermeden Önce**

    - Dalınızı en son main üzerine rebase edin
    - Dalınızın başarıyla oluşturulduğundan emin olun
    - Tüm testlerin geçtiğini tekrar kontrol edin
    - Değişikliklerinizi hata ayıklama kodu veya konsol günlükleri için gözden geçirin

6. **Pull Request Açıklaması**
    - Değişikliklerinizin ne yaptığını açıkça açıklayın
    - Değişiklikleri test etmek için adımlar ekleyin
    - Herhangi bir önemli değişikliği listeleyin
    - UI değişiklikleri için ekran görüntüleri ekleyin

## Katkı Anlaşması

Bir pull request göndererek, katkılarınızın projeyle aynı lisans altında ([Apache 2.0](../LICENSE)) lisanslanacağını kabul edersiniz.
