# Roo Code में योगदान देना

हम खुश हैं कि आप Roo Code में योगदान देने में रुचि रखते हैं। चाहे आप एक बग ठीक कर रहे हों, एक फीचर जोड़ रहे हों, या हमारे दस्तावेज़ों को सुधार रहे हों, हर योगदान Roo Code को अधिक स्मार्ट बनाता है! हमारे समुदाय को जीवंत और स्वागतयोग्य बनाए रखने के लिए, सभी सदस्यों को हमारे [आचार संहिता](CODE_OF_CONDUCT.md) का पालन करना चाहिए।

## हमारे समुदाय में शामिल हों

हम सभी योगदानकर्ताओं को हमारे [Discord समुदाय](https://discord.gg/roocode) में शामिल होने के लिए दृढ़ता से प्रोत्साहित करते हैं! हमारे Discord सर्वर का हिस्सा होने से आपको मदद मिलती है:

- अपने योगदान पर रीयल-टाइम मदद और मार्गदर्शन प्राप्त करें
- अन्य योगदानकर्ताओं और कोर टीम के सदस्यों से जुड़ें
- प्रोजेक्ट के विकास और प्राथमिकताओं से अपडेट रहें
- ऐसी चर्चाओं में भाग लें जो Roo Code के भविष्य को आकार देती हैं
- अन्य डेवलपर्स के साथ सहयोग के अवसर खोजें

## बग या समस्याओं की रिपोर्ट करना

बग रिपोर्ट हर किसी के लिए Roo Code को बेहतर बनाने में मदद करती हैं! नई समस्या बनाने से पहले, कृपया डुप्लिकेट से बचने के लिए [मौजूदा समस्याओं की खोज करें](https://github.com/RooVetGit/Roo-Code/issues)। जब आप बग की रिपोर्ट करने के लिए तैयार हों, तो हमारे [इश्यूज पेज](https://github.com/RooVetGit/Roo-Code/issues/new/choose) पर जाएं जहां आपको प्रासंगिक जानकारी भरने में मदद करने के लिए एक टेम्पलेट मिलेगा।

<blockquote class='warning-note'>
     🔐 <b>महत्वपूर्ण:</b> यदि आप कोई सुरक्षा कमजोरी खोजते हैं, तो कृपया <a href="https://github.com/RooVetGit/Roo-Code/security/advisories/new">इसे निजी तौर पर रिपोर्ट करने के लिए Github सुरक्षा उपकरण का उपयोग करें</a>।
</blockquote>

## किस पर काम करना है यह तय करना

पहले योगदान के लिए एक अच्छा अवसर खोज रहे हैं? हमारे [Roo Code इश्यूज](https://github.com/orgs/RooVetGit/projects/1) Github प्रोजेक्ट के "Issue [Unassigned]" सेक्शन में इश्यूज देखें। ये विशेष रूप से नए योगदानकर्ताओं के लिए और ऐसे क्षेत्रों के लिए क्यूरेट किए गए हैं जहां हमें कुछ मदद की जरूरत होगी!

हम अपने [दस्तावेज़ीकरण](https://docs.roocode.com/) में योगदान का भी स्वागत करते हैं! चाहे वह टाइपो ठीक करना हो, मौजूदा गाइड को सुधारना हो, या नई शैक्षिक सामग्री बनाना हो - हम संसाधनों का एक समुदाय-संचालित भंडार बनाना चाहते हैं जो हर किसी को Roo Code का अधिकतम उपयोग करने में मदद करे। आप फ़ाइल को संपादित करने के लिए किसी भी पृष्ठ पर "Edit this page" पर क्लिक कर सकते हैं या सीधे https://github.com/RooVetGit/Roo-Code-Docs में जा सकते हैं।

यदि आप एक बड़ी विशेषता पर काम करने की योजना बना रहे हैं, तो कृपया पहले एक [फीचर अनुरोध](https://github.com/RooVetGit/Roo-Code/discussions/categories/feature-requests?discussions_q=is%3Aopen+category%3A%22Feature+Requests%22+sort%3Atop) बनाएं ताकि हम चर्चा कर सकें कि क्या यह Roo Code के दृष्टिकोण के अनुरूप है। आप नीचे दिए गए हमारे [प्रोजेक्ट रोडमैप](#प्रोजेक्ट-रोडमैप) को भी देख सकते हैं यह जानने के लिए कि क्या आपका विचार हमारी रणनीतिक दिशा के अनुरूप है।

## प्रोजेक्ट रोडमैप

Roo Code का एक स्पष्ट विकास रोडमैप है जो हमारी प्राथमिकताओं और भविष्य की दिशा का मार्गदर्शन करता है। हमारे रोडमैप को समझने से आपको मदद मिल सकती है:

- अपने योगदान को प्रोजेक्ट के लक्ष्यों के साथ संरेखित करना
- ऐसे क्षेत्रों की पहचान करना जहां आपकी विशेषज्ञता सबसे मूल्यवान होगी
- कुछ डिज़ाइन निर्णयों के पीछे के संदर्भ को समझना
- नई विशेषताओं के लिए प्रेरणा पाना जो हमारे दृष्टिकोण का समर्थन करती हैं

हमारा वर्तमान रोडमैप छह प्रमुख स्तंभों पर केंद्रित है:

### प्रोवाइडर सपोर्ट

हम जितने संभव हो सके उतने प्रोवाइडर्स को सपोर्ट करना चाहते हैं:

- "OpenAI Compatible" के लिए अधिक बहुमुखी समर्थन
- xAI, Microsoft Azure AI, Alibaba Cloud Qwen, IBM Watsonx, Together AI, DeepInfra, Fireworks AI, Cohere, Perplexity AI, FriendliAI, Replicate
- Ollama और LM Studio के लिए बेहतर समर्थन

### मॉडल सपोर्ट

हम चाहते हैं कि Roo जितना संभव हो उतने मॉडल पर अच्छी तरह से काम करे, जिसमें लोकल मॉडल भी शामिल हैं:

- कस्टम सिस्टम प्रॉम्प्टिंग और वर्कफ़्लोज़ के माध्यम से लोकल मॉडल सपोर्ट
- बेंचमार्किंग एवैल्युएशन और टेस्ट केस

### सिस्टम सपोर्ट

हम चाहते हैं कि Roo हर किसी के कंप्यूटर पर अच्छी तरह से चले:

- क्रॉस प्लेटफॉर्म टर्मिनल इंटीग्रेशन
- Mac, Windows और Linux के लिए मजबूत और सुसंगत समर्थन

### डॉक्युमेंटेशन

हम सभी उपयोगकर्ताओं और योगदानकर्ताओं के लिए व्यापक, सुलभ दस्तावेज़ीकरण चाहते हैं:

- विस्तारित उपयोगकर्ता गाइड और ट्यूटोरियल
- स्पष्ट API दस्तावेज़ीकरण
- योगदानकर्ताओं के लिए बेहतर मार्गदर्शन
- बहुभाषी दस्तावेज़ीकरण संसाधन
- इंटरैक्टिव उदाहरण और कोड सैंपल

### स्थिरता

हम बग की संख्या को काफी कम करना और स्वचालित परीक्षण को बढ़ाना चाहते हैं:

- डीबग लॉगिंग स्विच
- बग/सपोर्ट अनुरोधों के साथ भेजने के लिए "मशीन/टास्क इन्फॉर्मेशन" कॉपी बटन

### अंतर्राष्ट्रीयकरण

हम चाहते हैं कि Roo हर किसी की भाषा बोले:

- 我们希望 Roo Code 说每个人的语言
- Queremos que Roo Code hable el idioma de todos
- हम चाहते हैं कि Roo Code हर किसी की भाषा बोले
- نريد أن يتحدث Roo Code لغة الجميع

हम विशेष रूप से उन योगदानों का स्वागत करते हैं जो हमारे रोडमैप लक्ष्यों को आगे बढ़ाते हैं। यदि आप कुछ ऐसा कर रहे हैं जो इन स्तंभों के अनुरूप है, तो कृपया अपने PR विवरण में इसका उल्लेख करें।

## डेवलपमेंट सेटअप

1. रिपो **क्लोन** करें:

```sh
git clone https://github.com/RooVetGit/Roo-Code.git
```

2. **डिपेंडेंसीज इंस्टॉल** करें:

```sh
npm run install:all
```

3. **वेबव्यू शुरू करें (Vite/React ऐप HMR के साथ)**:

```sh
npm run dev
```

4. **डिबग**:
   VSCode में `F5` दबाएं (या **Run** → **Start Debugging**) Roo Code लोड के साथ एक नया सेशन खोलने के लिए।

वेबव्यू में परिवर्तन तुरंत दिखाई देंगे। कोर एक्सटेंशन में परिवर्तनों के लिए एक्सटेंशन होस्ट को रीस्टार्ट करने की आवश्यकता होगी।

वैकल्पिक रूप से आप .vsix बना सकते हैं और इसे सीधे VSCode में इंस्टॉल कर सकते हैं:

```sh
npm run build
```

`bin/` डायरेक्टरी में एक `.vsix` फ़ाइल दिखाई देगी जिसे इस कमांड से इंस्टॉल किया जा सकता है:

```sh
code --install-extension bin/roo-cline-<version>.vsix
```

## कोड लिखना और सबमिट करना

कोई भी Roo Code में कोड का योगदान दे सकता है, लेकिन हम आपसे अनुरोध करते हैं कि आप इन दिशानिर्देशों का पालन करें ताकि आपके योगदान को सुचारू रूप से एकीकृत किया जा सके:

1. **पुल रिक्वेस्ट को फोकस्ड रखें**

    - PR को एक ही फीचर या बग फिक्स तक सीमित रखें
    - बड़े परिवर्तनों को छोटी, संबंधित PR में विभाजित करें
    - परिवर्तनों को तार्किक कमिट्स में तोड़ें जिन्हें स्वतंत्र रूप से समीक्षा की जा सके

2. **कोड क्वालिटी**

    - सभी PR को CI चेक पास करना चाहिए जिसमें लिंटिंग और फॉर्मेटिंग दोनों शामिल हैं
    - सबमिट करने से पहले किसी भी ESLint चेतावनी या त्रुटि को संबोधित करें
    - Ellipsis, हमारे स्वचालित कोड समीक्षा टूल से सभी फीडबैक का जवाब दें
    - TypeScript के बेस्ट प्रैक्टिस का पालन करें और टाइप सुरक्षा बनाए रखें

3. **टेस्टिंग**

    - नई विशेषताओं के लिए टेस्ट जोड़ें
    - यह सुनिश्चित करने के लिए `npm test` चलाएं कि सभी टेस्ट पास हों
    - यदि आपके परिवर्तन उन्हें प्रभावित करते हैं तो मौजूदा टेस्ट अपडेट करें
    - जहां उपयुक्त हो, यूनिट टेस्ट और इंटीग्रेशन टेस्ट दोनों शामिल करें

4. **कमिट दिशानिर्देश**

    - स्पष्ट, वर्णनात्मक कमिट संदेश लिखें
    - #issue-number का उपयोग करके कमिट्स में प्रासंगिक मुद्दों का संदर्भ दें

5. **सबमिट करने से पहले**

    - अपनी ब्रांच को लेटेस्ट मेन पर रीबेस करें
    - सुनिश्चित करें कि आपकी ब्रांच सफलतापूर्वक बिल्ड होती है
    - डबल-चेक करें कि सभी टेस्ट पास हो रहे हैं
    - अपने परिवर्तनों की समीक्षा करें किसी भी डिबगिंग कोड या कंसोल लॉग के लिए

6. **पुल रिक्वेस्ट विवरण**
    - स्पष्ट रूप से बताएं कि आपके परिवर्तन क्या करते हैं
    - परिवर्तनों का परीक्षण करने के लिए चरण शामिल करें
    - किसी भी ब्रेकिंग चेंज की सूची बनाएं
    - UI परिवर्तनों के लिए स्क्रीनशॉट जोड़ें

## योगदान समझौता

पुल रिक्वेस्ट सबमिट करके, आप सहमत होते हैं कि आपके योगदान को प्रोजेक्ट के समान लाइसेंस ([Apache 2.0](../LICENSE)) के तहत लाइसेंस दिया जाएगा।
