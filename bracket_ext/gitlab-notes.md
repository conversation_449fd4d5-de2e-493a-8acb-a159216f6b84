# ------------------

Features to be implemented in the demo

What do we want to showcase?
Immediate capabilities and downstream implications that are extremely time taking and deep understanding dependent.

All these features are being discussed keeping in mind an extension like RooCline that has some of the base technology inbuilt like tool use, terminal use, different modes, and we integrate our code understanding pipeline inside of it.

Below is a concise, feature-by-feature outline of the Bracket (IRL-based) demo plan. Each feature ties directly to GitLab’s AI roadmap—especially around codebase understanding, context retrieval, code generation, and multi-file autonomy—while also illustrating capabilities that go beyond what GitLab currently provides. The list is broken down into logical categories, and each entry is kept succinct yet clear.

⸻

1. Codebase Understanding & System Overview

1.1 Global Overview Panel
	•	What It Does: Provides a 3,000–4,000-token “big-picture” explanation of the entire codebase, rendered as a concise bulleted summary (like a “system design” overview).
	•	Value: Ideal for onboarding new developers or offering a quick architectural snapshot before diving into details.

1.2 Suggested Questions
	•	What It Does: Displays sample queries or prompts (broad & deep) to guide developers in exploring the architecture.
	•	Value: Reduces guesswork and highlights the kinds of “architecture-level” or “deep-dive” questions the AI can answer—demonstrating non-trivial, multi-file scope.

1.3 Infinite Canvas Mermaid Diagrams
	•	What It Does: Presents all layered Mermaid diagrams on an infinite canvas within VS Code—each diagram corresponding to a domain or subdomain.
	•	Value: Serves as the visual “source of truth” for the codebase’s structure, letting devs see relationships, function call paths, and domain hierarchies in one place.

1.4 Auto-Focus Diagram Navigation
	•	What It Does: Dynamically zooms/centers the infinite canvas on whichever file or function the user has open in VS Code.
	•	Value: Maintains synergy between the code editor and the global architecture map—no manual toggling needed.

⸻

2. Code Understanding / Q&A Mode

2.1 Global & Local Query Handling
	•	What It Does: The user can ask open-ended “chat with codebase” questions without manually specifying context; the system automatically fetches relevant files/functions via Bracket’s top-down/bottom-up approach.
	•	Value: Demonstrates fully autonomous context retrieval—unlike typical “@file” manual mentions. Handles both large-scale architecture queries and pinpoint function details.

2.2 Visible Context Retrieval Steps (Optional)
	•	What It Does: Optionally shows how the agent decides which files/functions are relevant.
	•	Value: Gives transparency into the AI’s decision-making and underscores Bracket’s advanced retrieval vs. naive semantic search.

2.3 Cost/Latency Display
	•	What It Does: Shows real-time usage cost per query and approximate latency.
	•	Value: Reinforces that large codebase navigation remains affordable and performant, supporting enterprise-scale usage.

⸻

3. Code Generation

3.1 Basic Chat-Driven Generation
	•	What It Does: Allows the user to request new features or refactors via a simple chat prompt. The system auto-injects relevant context, plans the changes, and generates a proposed diff.
	•	Value: Illustrates easy code generation that is truly context-aware—no extra steps for fetching relevant snippets.

3.2 Agent Mode (Advanced Generation)
	•	What It Does: Uses additional tool access (browser, terminal) to create or modify files, run commands, read output, and iterate until the task is completed.
	•	Value: Highlights “autonomous coding” with real environment interactions—akin to a personal dev assistant that can confirm changes actually work.

3.3 Documentation Generation (Optional)
	•	What It Does: Produces overarching “global” or domain-level documentation for the codebase, combining structural data and AI-generated descriptions.
	•	Value: Demonstrates how Bracket’s architecture awareness can auto-document entire systems, not just single functions.

⸻

4. PR Broadcast (Optional Feature)

4.1 MR/PR Diff Visualization
	•	What It Does: Fetches a GitLab MR (titles, diffs, comments), identifies changed functions/subdomains, and auto-generates a specialized Mermaid diagram.
	•	Value: Shows how each code change ripples through the architecture. Great for code reviewers who want a top-down or bottom-up snapshot of what just changed and where.

4.2 Domain Impact Highlighting
	•	What It Does: Visual cues (e.g., highlighting) show which subdomains are directly altered and which might be indirectly affected.
	•	Value: Prevents missed dependencies by illustrating how far the change might propagate.

⸻

5. Infrastructure & Utility Features

5.1 Delta Code Change Ingestion
	•	What It Does: Detects new commits or branch switches, re-ingests only changed files/functions rather than re-indexing the whole codebase.
	•	Value: Demonstrates real-world feasibility—no expensive, full re-scan. Encourages frequent updates for big repos.

5.2 Performance & Accuracy Metrics
	•	What It Does: Optionally logs speed, memory usage, and success rates of code retrieval and generation. May also include side-by-side comparisons vs. competitor assistants.
	•	Value: Establishes trust in both cost and reliability, particularly crucial for enterprise clients evaluating AI tool ROI.

⸻

6. Why This Demo Matters to GitLab
	1.	Aligns with GitLab Duo Roadmap
	•	Showcases a deeper “chat with your entire codebase” functionality that GitLab is already aiming for—but Bracket accomplishes it with a structural knowledge graph, not pure semantic embeddings.
	2.	Goes Beyond Current Solutions
	•	Features like infinite Mermaid diagrams, PR broadcast diagrams, and fully autonomous agent mode stand out as advanced capabilities GitLab does not yet offer.
	3.	Demonstrates a Cohesive Platform
	•	Integrates architecture visualization, Q&A, code generation, and PR analysis within one tool inside VS Code—mirroring GitLab’s end-to-end DevSecOps vision.
	4.	Underscores Scalability
	•	The delta ingestion and cost/latency dashboards prove that large codebases are handled efficiently—perfect for GitLab’s enterprise customers.



