module.exports = function prettyBytes(bytes) {
	if (typeof bytes !== "number") {
		throw new TypeError("Expected a number")
	}

	// Simple mock implementation that returns formatted strings.
	if (bytes === 0) return "0 B"
	if (bytes < 1024) return `${bytes} B`
	if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`
	if (bytes < 1024 * 1024 * 1024) return `${(bytes / (1024 * 1024)).toFixed(1)} MB`
	return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} GB`
}
