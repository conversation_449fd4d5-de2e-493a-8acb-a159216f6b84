{"greeting": "Cosa può fare Roo per te?", "task": {"title": "Attività", "seeMore": "<PERSON><PERSON><PERSON>", "seeLess": "<PERSON><PERSON><PERSON> meno", "tokens": "Tokens:", "cache": "Cache:", "apiCost": "Costo API:", "contextWindow": "Finestra di contesto:", "closeAndStart": "<PERSON>udi attività e iniziane una nuova", "export": "Esporta cronologia attività", "delete": "Elimina attività (Shift + Clic per saltare la conferma)"}, "unpin": "Rilascia", "pin": "<PERSON><PERSON>", "tokenProgress": {"availableSpace": "Spazio disponibile: {{amount}} tokens", "tokensUsed": "Tokens utilizzati: {{used}} di {{total}}", "reservedForResponse": "Riservato per risposta del modello: {{amount}} tokens"}, "retry": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Prova di nuovo l'operazione"}, "startNewTask": {"title": "Inizia nuova attività", "tooltip": "Inizia una nuova attività"}, "proceedAnyways": {"title": "Procedi comunque", "tooltip": "Continua mentre il comando è in esecuzione"}, "save": {"title": "<PERSON><PERSON>", "tooltip": "Salva le modifiche al file"}, "reject": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Rifiuta questa azione"}, "completeSubtaskAndReturn": "Completa sottoattività e torna indietro", "approve": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Approva questa azione"}, "runCommand": {"title": "<PERSON><PERSON><PERSON><PERSON> comando", "tooltip": "Esegui questo comando"}, "proceedWhileRunning": {"title": "Procedi durante l'esecuzione", "tooltip": "Continua nonostante gli avvisi"}, "resumeTask": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Continua l'attività corrente"}, "terminate": {"title": "Termina", "tooltip": "Termina l'attività corrente"}, "cancel": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Annulla l'operazione corrente"}, "scrollToBottom": "<PERSON><PERSON><PERSON> fino alla fine della chat", "aboutMe": "Grazie alle più recenti innovazioni nelle capacità di codifica agentica, posso gestire complesse attività di sviluppo software passo dopo passo. Con strumenti che mi permettono di creare e modificare file, esplorare progetti complessi, utilizzare il browser ed eseguire comandi da terminale (dopo la tua autorizzazione), posso aiutarti in modi che vanno oltre il completamento del codice o il supporto tecnico. Posso persino usare MCP per creare nuovi strumenti ed estendere le mie capacità.", "selectMode": "Seleziona modalità di interazione", "selectApiConfig": "Seleziona configurazione API", "enhancePrompt": "Migliora prompt con contesto aggiuntivo", "addImages": "Aggiungi immagini al messaggio", "sendMessage": "Invia messaggio", "typeMessage": "Scrivi un messaggio...", "typeTask": "Scrivi la tua attività qui...", "addContext": "@ per aggiungere contesto, / per cambiare modalità", "dragFiles": "tieni premuto shift per trascinare file", "dragFilesImages": "tieni premuto shift per trascinare file/immagini", "enhancePromptDescription": "Il pulsante 'Migliora prompt' aiuta a migliorare la tua richiesta fornendo contesto aggiuntivo, chiarimenti o riformulazioni. Prova a digitare una richiesta qui e fai di nuovo clic sul pulsante per vedere come funziona.", "errorReadingFile": "Errore nella lettura del file:", "noValidImages": "<PERSON>essuna immagine valida è stata elaborata", "separator": "Separatore", "edit": "Modifica...", "forNextMode": "per la prossima modalità", "instructions": {"wantsToFetch": "Roo vuole recuperare istruzioni dettagliate per aiutare con l'attività corrente"}, "error": "Errore", "troubleMessage": "Roo sta avendo problemi...", "apiRequest": {"title": "Richiesta API", "failed": "Richiesta API fallita", "streaming": "Richiesta API...", "cancelled": "Richiesta API annullata", "streamingFailed": "Streaming API fallito"}, "checkpoint": {"initial": "Checkpoint iniziale", "regular": "Checkpoint", "initializingWarning": "Inizializzazione del checkpoint in corso... Se questa operazione richiede troppo tempo, puoi disattivare i checkpoint nelle <settingsLink>impostazioni</settingsLink> e riavviare l'attività.", "menu": {"viewDiff": "Visualizza differenze", "restore": "Ripristina checkpoint", "restoreFiles": "R<PERSON><PERSON><PERSON> file", "restoreFilesDescription": "Rip<PERSON><PERSON> i file del tuo progetto a uno snapshot catturato in questo punto.", "restoreFilesAndTask": "Ripristina file e attività", "confirm": "Conferma", "cancel": "<PERSON><PERSON><PERSON>", "cannotUndo": "Questa azione non può essere annullata.", "restoreFilesAndTaskDescription": "R<PERSON><PERSON><PERSON> i file del tuo progetto a uno snapshot catturato in questo punto ed elimina tutti i messaggi successivi a questo punto."}, "current": "<PERSON><PERSON><PERSON>"}, "fileOperations": {"wantsToRead": "Roo vuole leggere questo file:", "wantsToReadOutsideWorkspace": "Roo vuole leggere questo file al di fuori dell'area di lavoro:", "didRead": "Roo ha letto questo file:", "wantsToEdit": "Roo vuole modificare questo file:", "wantsToEditOutsideWorkspace": "Roo vuole modificare questo file al di fuori dell'area di lavoro:", "wantsToCreate": "Roo vuole creare un nuovo file:"}, "directoryOperations": {"wantsToViewTopLevel": "Roo vuole visualizzare i file di primo livello in questa directory:", "didViewTopLevel": "Roo ha visualizzato i file di primo livello in questa directory:", "wantsToViewRecursive": "Roo vuole visualizzare ricorsivamente tutti i file in questa directory:", "didViewRecursive": "Roo ha visualizzato ricorsivamente tutti i file in questa directory:", "wantsToViewDefinitions": "Roo vuole visualizzare i nomi delle definizioni di codice sorgente utilizzate in questa directory:", "didViewDefinitions": "Roo ha visualizzato i nomi delle definizioni di codice sorgente utilizzate in questa directory:", "wantsToSearch": "Roo vuole cercare in questa directory <code>{{regex}}</code>:", "didSearch": "Roo ha cercato in questa directory <code>{{regex}}</code>:"}, "commandOutput": "Output del comando", "response": "Risposta", "arguments": "Argomenti", "mcp": {"wantsToUseTool": "Roo vuole utilizzare uno strumento sul server MCP {{serverName}}:", "wantsToAccessResource": "Roo vuole accedere a una risorsa sul server MCP {{serverName}}:"}, "modes": {"wantsToSwitch": "Roo vuole passare alla modalità <code>{{mode}}</code>", "wantsToSwitchWithReason": "Roo vuole passare alla modalità <code>{{mode}}</code> perché: {{reason}}", "didSwitch": "Roo è passato alla modalità <code>{{mode}}</code>", "didSwitchWithReason": "Roo è passato alla modalità <code>{{mode}}</code> perché: {{reason}}"}, "subtasks": {"wantsToCreate": "Roo vuole creare una nuova sottoattività in modalità <code>{{mode}}</code>:", "wantsToFinish": "Roo vuole completare questa sottoattività", "newTaskContent": "Istruzioni sottoattività", "completionContent": "Sottoattività completata", "resultContent": "Risultati sottoattività", "defaultResult": "Per favore continua con la prossima attività.", "completionInstructions": "Sottoattività completata! Puoi rivedere i risultati e suggerire correzioni o prossimi passi. Se tutto sembra a posto, conferma per restituire il risultato all'attività principale."}, "questions": {"hasQuestion": "Roo ha una domanda:"}, "taskCompleted": "Attività completata", "shellIntegration": {"unavailable": "Integrazione shell non disponibile", "troubleshooting": "Ancora problemi?"}, "powershell": {"issues": "<PERSON><PERSON>ra che tu stia avendo problemi con Windows PowerShell, consulta questa"}, "autoApprove": {"title": "Auto-approvazione:", "none": "Nessuna", "description": "L'auto-approvazione permette a Roo Code di eseguire azioni senza chiedere permesso. Abilita solo per azioni di cui ti fidi completamente. Configurazione più dettagliata disponibile nelle <settingsLink>Impostazioni</settingsLink>.", "actions": {"readFiles": {"label": "Leggi file e directory", "shortName": "Lettura", "description": "Consente l'accesso per leggere qualsiasi file sul tuo computer."}, "editFiles": {"label": "Modifica file", "shortName": "Modifica", "description": "Consente la modifica di qualsiasi file sul tuo computer."}, "executeCommands": {"label": "<PERSON>se<PERSON>i comandi approvati", "shortName": "<PERSON><PERSON><PERSON>", "description": "Consente l'esecuzione di comandi da terminale approvati. Puoi configurare questo nel pannello delle impostazioni."}, "useBrowser": {"label": "Usa il browser", "shortName": "Browser", "description": "Consente la capacità di avviare e interagire con qualsiasi sito web in un browser headless."}, "useMcp": {"label": "Usa server MCP", "shortName": "MCP", "description": "Consente l'uso di server MCP configurati che possono modificare il filesystem o interagire con API."}, "switchModes": {"label": "Cambia modalità", "shortName": "Modalità", "description": "Consente il passaggio automatico tra diverse modalità senza richiedere approvazione."}, "subtasks": {"label": "Crea e completa sottoattività", "shortName": "Sottoattività", "description": "Consente la creazione e il completamento di sottoattività senza richiedere approvazione."}, "retryRequests": {"label": "<PERSON><PERSON>rova richieste fallite", "shortName": "Ripetizioni", "description": "Riprova automaticamente le richieste API fallite quando il provider restituisce una risposta di errore."}}}, "reasoning": {"thinking": "<PERSON><PERSON>", "seconds": "{{count}}s"}, "followUpSuggest": {"copyToInput": "Copia nell'input (o Shift + clic)"}, "announcement": {"title": "Fai di più con le Attività Boomerang 🪃", "description": "<PERSON><PERSON><PERSON> il lavoro in sottoattività, ognuna eseguita in una modalità specializzata, come code, architect, debug o una modalità personalizzata.", "learnMore": "Scopri di più →", "hideButton": "Nascondi annuncio"}, "browser": {"rooWantsToUse": "Roo vuole utilizzare il browser:", "consoleLogs": "Log della console", "noNewLogs": "(<PERSON><PERSON>un nuovo log)", "screenshot": "Screenshot del browser", "cursor": "cursore", "navigation": {"step": "Passo {{current}} di {{total}}", "previous": "Precedente", "next": "Successivo"}, "sessionStarted": "Sessione browser avviata", "actions": {"title": "Azione browser: ", "launch": "Avvia browser su {{url}}", "click": "Clic ({{coordinate}})", "type": "Digita \"{{text}}\"", "scrollDown": "<PERSON><PERSON><PERSON> verso il basso", "scrollUp": "<PERSON><PERSON><PERSON> verso l'alto", "close": "Chiudi browser"}}}