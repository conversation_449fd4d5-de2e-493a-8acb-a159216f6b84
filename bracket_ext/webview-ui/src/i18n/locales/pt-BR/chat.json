{"greeting": "O que o Roo pode fazer por você?", "task": {"title": "<PERSON><PERSON><PERSON>", "seeMore": "Ver mais", "seeLess": "<PERSON>er menos", "tokens": "Tokens:", "cache": "Cache:", "apiCost": "Custo da API:", "contextWindow": "<PERSON><PERSON>:", "closeAndStart": "Fechar tarefa e iniciar nova", "export": "Exportar histó<PERSON><PERSON> de <PERSON>fas", "delete": "Excluir tarefa (Shift + Clique para pular confirmação)"}, "unpin": "Desfixar", "pin": "Fixar", "tokenProgress": {"availableSpace": "Espaço disponível: {{amount}} tokens", "tokensUsed": "Tokens usados: {{used}} de {{total}}", "reservedForResponse": "Reservado para resposta do modelo: {{amount}} tokens"}, "retry": {"title": "Tentar novamente", "tooltip": "Tentar a operação novamente"}, "startNewTask": {"title": "Iniciar nova tarefa", "tooltip": "Começar uma nova tarefa"}, "proceedAnyways": {"title": "Prosseguir mesmo assim", "tooltip": "Continuar enquanto o comando executa"}, "save": {"title": "<PERSON><PERSON>", "tooltip": "Salvar as alterações do arquivo"}, "reject": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Rejeitar esta ação"}, "completeSubtaskAndReturn": "Completar subtarefa e retornar", "approve": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Aprovar esta ação"}, "runCommand": {"title": "Executar comando", "tooltip": "Executar este comando"}, "proceedWhileRunning": {"title": "Prosseguir durante execução", "tooltip": "Continuar apesar dos avisos"}, "resumeTask": {"title": "<PERSON><PERSON><PERSON> ta<PERSON>", "tooltip": "Continuar a tarefa atual"}, "terminate": {"title": "Terminar", "tooltip": "Encerrar a tarefa atual"}, "cancel": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Cancelar a operação atual"}, "scrollToBottom": "Rolar para o final do chat", "aboutMe": "Graças aos mais recentes avanços em capacidades de codificação agentiva, posso lidar com tarefas complexas de desenvolvimento de software passo a passo. Com ferramentas que me permitem criar e editar arquivos, explorar projetos complexos, usar o navegador e executar comandos de terminal (após sua permissão), posso ajudar de maneiras que vão além da conclusão de código ou suporte técnico. Posso até usar o MCP para criar novas ferramentas e expandir minhas próprias capacidades.", "selectMode": "Selecionar modo de interação", "selectApiConfig": "Selecionar configuração de API", "enhancePrompt": "Aprimorar prompt com contexto adicional", "addImages": "Adicionar imagens à mensagem", "sendMessage": "Enviar mensagem", "typeMessage": "Digite uma mensagem...", "typeTask": "Digite sua tarefa aqui...", "addContext": "@ para adicionar contexto, / para alternar modos", "dragFiles": "segure shift para arrastar arquivos", "dragFilesImages": "segure shift para arrastar arquivos/imagens", "enhancePromptDescription": "O botão 'Aprimorar prompt' ajuda a melhorar seu pedido fornecendo contexto adicional, esclarecimentos ou reformulações. Tente digitar um pedido aqui e clique no botão novamente para ver como funciona.", "errorReadingFile": "Erro ao ler arquivo:", "noValidImages": "Nenhuma imagem válida foi processada", "separator": "Separador", "edit": "Editar...", "forNextMode": "para o próximo modo", "error": "Erro", "troubleMessage": "Roo está tendo problemas...", "apiRequest": {"title": "Requisição API", "failed": "Requisição API falhou", "streaming": "Requisição API...", "cancelled": "Requisição API cancelada", "streamingFailed": "Streaming API falhou"}, "checkpoint": {"initial": "Ponto de verificação inicial", "regular": "Ponto de verificação", "initializingWarning": "Ainda inicializando ponto de verificação... Se isso demorar muito, você pode desativar os pontos de verificação nas <settingsLink>configurações</settingsLink> e reiniciar sua tarefa.", "menu": {"viewDiff": "Ver diferenças", "restore": "Restaurar ponto de verificação", "restoreFiles": "Restaurar a<PERSON>", "restoreFilesDescription": "Restaura os arquivos do seu projeto para um snapshot feito neste ponto.", "restoreFilesAndTask": "Restaurar arquivos e tarefa", "confirm": "Confirmar", "cancel": "<PERSON><PERSON><PERSON>", "cannotUndo": "Esta ação não pode ser desfeita.", "restoreFilesAndTaskDescription": "Restaura os arquivos do seu projeto para um snapshot feito neste ponto e exclui todas as mensagens após este ponto."}, "current": "Atual"}, "instructions": {"wantsToFetch": "Roo quer buscar instruções detalhadas para ajudar com a tarefa atual"}, "fileOperations": {"wantsToRead": "Roo quer ler este arquivo:", "wantsToReadOutsideWorkspace": "Roo quer ler este arquivo fora do espaço de trabalho:", "didRead": "Roo leu este arquivo:", "wantsToEdit": "<PERSON>oo quer editar este arquivo:", "wantsToEditOutsideWorkspace": "<PERSON>oo quer editar este arquivo fora do espaço de trabalho:", "wantsToCreate": "Roo quer criar um novo arquivo:"}, "directoryOperations": {"wantsToViewTopLevel": "<PERSON>oo quer visualizar os arquivos de nível superior neste diretório:", "didViewTopLevel": "Roo visualizou os arquivos de nível superior neste diretório:", "wantsToViewRecursive": "Roo quer visualizar recursivamente todos os arquivos neste diretório:", "didViewRecursive": "Roo visualizou recursivamente todos os arquivos neste diretório:", "wantsToViewDefinitions": "Roo quer visualizar nomes de definição de código-fonte usados neste diretório:", "didViewDefinitions": "Roo visualizou nomes de definição de código-fonte usados neste diretório:", "wantsToSearch": "Roo quer pesquisar neste diretório por <code>{{regex}}</code>:", "didSearch": "<PERSON><PERSON> pesquisou neste diretório por <code>{{regex}}</code>:"}, "commandOutput": "Saída do comando", "response": "Resposta", "arguments": "Argumentos", "mcp": {"wantsToUseTool": "Roo quer usar uma ferramenta no servidor MCP {{serverName}}:", "wantsToAccessResource": "Roo quer acessar um recurso no servidor MCP {{serverName}}:"}, "modes": {"wantsToSwitch": "Roo quer mudar para o modo <code>{{mode}}</code>", "wantsToSwitchWithReason": "Roo quer mudar para o modo <code>{{mode}}</code> porque: {{reason}}", "didSwitch": "<PERSON>oo mudou para o modo <code>{{mode}}</code>", "didSwitchWithReason": "<PERSON>oo mudou para o modo <code>{{mode}}</code> porque: {{reason}}"}, "subtasks": {"wantsToCreate": "Roo quer criar uma nova subtarefa no modo <code>{{mode}}</code>:", "wantsToFinish": "<PERSON><PERSON> quer finalizar esta subtarefa", "newTaskContent": "Instruções da subtarefa", "completionContent": "Subtarefa concluída", "resultContent": "Resultados da subtarefa", "defaultResult": "Por favor, continue com a próxima tarefa.", "completionInstructions": "Subtarefa concluída! Você pode revisar os resultados e sugerir correções ou próximos passos. Se tudo parecer bom, confirme para retornar o resultado à tarefa principal."}, "questions": {"hasQuestion": "Roo tem uma pergunta:"}, "taskCompleted": "Tarefa concluída", "shellIntegration": {"unavailable": "Integração de shell indisponível", "troubleshooting": "Ainda com problemas?"}, "powershell": {"issues": "Parece que você está tendo problemas com o Windows PowerShell, por favor veja este"}, "autoApprove": {"title": "Aprovação automática:", "none": "<PERSON><PERSON><PERSON><PERSON>", "description": "A aprovação automática permite que o Roo Code execute ações sem pedir permissão. Ative apenas para ações nas quais você confia totalmente. Configuração mais detalhada disponível nas <settingsLink>Configurações</settingsLink>.", "actions": {"readFiles": {"label": "Ler arquivos e diretórios", "shortName": "Leitura", "description": "Permite acesso para ler qualquer arquivo em seu computador."}, "editFiles": {"label": "<PERSON><PERSON>", "shortName": "Edição", "description": "Permite a modificação de quaisquer arquivos em seu computador."}, "executeCommands": {"label": "Executar comandos aprovados", "shortName": "<PERSON><PERSON><PERSON>", "description": "Permite a execução de comandos de terminal aprovados. Você pode configurar isso no painel de configurações."}, "useBrowser": {"label": "Usar o navegador", "shortName": "<PERSON><PERSON><PERSON><PERSON>", "description": "Permite a capacidade de iniciar e interagir com qualquer site em um navegador headless."}, "useMcp": {"label": "Usar servidores MCP", "shortName": "MCP", "description": "Permite o uso de servidores MCP configurados que podem modificar o sistema de arquivos ou interagir com APIs."}, "switchModes": {"label": "Alternar modos", "shortName": "Modos", "description": "Permite a alternância automática entre diferentes modos sem exigir aprovação."}, "subtasks": {"label": "Criar e completar subtarefas", "shortName": "Subtarefas", "description": "Permite a criação e conclusão de subtarefas sem exigir aprovação."}, "retryRequests": {"label": "Retentar requisições falhas", "shortName": "Retentativas", "description": "Retenta automaticamente requisições de API falhas quando o provedor retorna uma resposta de erro."}}}, "reasoning": {"thinking": "Pensando", "seconds": "{{count}}s"}, "followUpSuggest": {"copyToInput": "Copiar para entrada (ou Shift + clique)"}, "announcement": {"title": "Faça mais com Tarefas Boomerang 🪃", "description": "Divida o trabalho em subtarefas, cada uma executando em um modo especializado, como code, architect, debug ou um modo personalizado.", "learnMore": "<PERSON><PERSON> mais →", "hideButton": "<PERSON><PERSON><PERSON><PERSON>"}, "browser": {"rooWantsToUse": "Roo quer usar o navegador:", "consoleLogs": "Logs do console", "noNewLogs": "(Sem novos logs)", "screenshot": "Captura de tela do navegador", "cursor": "cursor", "navigation": {"step": "Passo {{current}} de {{total}}", "previous": "Anterior", "next": "Próximo"}, "sessionStarted": "Sessão do navegador iniciada", "actions": {"title": "Ação do navegador: ", "launch": "Iniciar nave<PERSON> em {{url}}", "click": "Clique ({{coordinate}})", "type": "Digitar \"{{text}}\"", "scrollDown": "Rolar para baixo", "scrollUp": "Rolar para cima", "close": "<PERSON><PERSON><PERSON>"}}}