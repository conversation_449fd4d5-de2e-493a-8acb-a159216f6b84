{"title": "MCP服务管理", "done": "完成", "description": "<0>Model Context Protocol</0> 支持与本地MCP服务通信，提供扩展功能。您可以使用<1>社区服务器</1>，或通过指令创建定制工具（例如：“新增获取最新npm文档的工具”）。", "enableToggle": {"title": "启用MCP服务", "description": "启用后Roo可与MCP服务交互获取高级功能。未使用时建议关闭以节省Token消耗。"}, "enableServerCreation": {"title": "允许创建工具", "description": "启用后模型可通过“添加新工具”等指令创建MCP服务。无需创建功能时建议关闭以节省Token。"}, "editGlobalMCP": "编辑全局配置", "editProjectMCP": "编辑项目配置", "editSettings": "参数设置", "tool": {"alwaysAllow": "始终允许", "parameters": "参数", "noDescription": "无描述"}, "tabs": {"tools": "工具", "resources": "资源"}, "emptyState": {"noTools": "未找到工具", "noResources": "未找到资源"}, "networkTimeout": {"label": "请求超时", "description": "服务响应最长等待时间", "options": {"15seconds": "15秒", "30seconds": "30秒", "1minute": "1分钟", "5minutes": "5分钟", "10minutes": "10分钟", "15minutes": "15分钟", "30minutes": "30分钟", "60minutes": "1小时"}}, "deleteDialog": {"title": "删除 MCP 服务", "description": "确认删除MCP服务 \"{{serverName}}\"？此操作不可逆。", "cancel": "取消", "delete": "删除"}, "serverStatus": {"retrying": "重试中...", "retryConnection": "重试连接"}}