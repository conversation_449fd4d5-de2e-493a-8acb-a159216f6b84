{"greeting": "<PERSON><PERSON> 能为您做什么？", "task": {"title": "任务", "seeMore": "展开", "seeLess": "收起", "tokens": "Token 用量:", "cache": "缓存:", "apiCost": "API 费用:", "contextWindow": "上下文窗口:", "closeAndStart": "关闭任务并开始新任务", "export": "导出任务历史", "delete": "删除任务（Shift + 点击跳过确认）"}, "unpin": "取消置顶", "pin": "置顶", "tokenProgress": {"availableSpace": "可用: {{amount}}", "tokensUsed": "已使用: {{used}} / {{total}}", "reservedForResponse": "已保留: {{amount}}"}, "retry": {"title": "重试", "tooltip": "再次尝试操作"}, "startNewTask": {"title": "开始新任务", "tooltip": "开始一个新任务"}, "proceedAnyways": {"title": "仍然继续", "tooltip": "在命令执行时继续"}, "save": {"title": "保存", "tooltip": "保存文件更改"}, "reject": {"title": "拒绝", "tooltip": "拒绝此操作"}, "completeSubtaskAndReturn": "完成子任务并返回", "approve": {"title": "批准", "tooltip": "批准此操作"}, "runCommand": {"title": "运行命令", "tooltip": "执行此命令"}, "proceedWhileRunning": {"title": "强制继续", "tooltip": "忽略运行中的命令并继续"}, "resumeTask": {"title": "恢复任务", "tooltip": "继续当前任务"}, "terminate": {"title": "结束", "tooltip": "结束当前任务"}, "cancel": {"title": "取消", "tooltip": "取消当前操作"}, "scrollToBottom": "滚动到聊天底部", "aboutMe": "基于最新的AI编程技术，我可以逐步处理复杂软件开发任务。支持创建编辑文件、分析复杂项目、浏览器操作及运行终端命令，不仅能提供代码补全和基础答疑，还能完成更高阶的开发协助。通过MCP系统，我甚至可以自己制作新工具，持续提升解决问题的能力。", "selectMode": "选择交互模式", "selectApiConfig": "选择API配置", "enhancePrompt": "增强提示词", "addImages": "添加图片到消息", "sendMessage": "发送消息", "typeMessage": "输入消息...", "typeTask": "在此处输入您的任务...", "addContext": "@添加上下文，/切换模式", "dragFiles": "Shift+拖拽文件", "dragFilesImages": "Shift+拖拽文件/图片", "enhancePromptDescription": "'增强提示'按钮通过提供额外上下文、澄清或重新表述来帮助改进您的请求。尝试在此处输入请求，然后再次点击按钮查看其工作原理。", "errorReadingFile": "读取文件时出错:", "noValidImages": "没有处理有效图片", "separator": "分隔符", "edit": "编辑...", "forNextMode": "用于下一个模式", "error": "错误", "troubleMessage": "<PERSON><PERSON>遇到问题...", "apiRequest": {"title": "API请求", "failed": "API请求失败", "streaming": "API请求...", "cancelled": "API请求已取消", "streamingFailed": "API流式传输失败"}, "checkpoint": {"initial": "初始检查点", "regular": "检查点", "initializingWarning": "正在初始化检查点...如果耗时过长，你可以在<settingsLink>设置</settingsLink>中禁用检查点并重新启动任务。", "menu": {"viewDiff": "查看差异", "restore": "恢复检查点", "restoreFiles": "恢复文件", "restoreFilesDescription": "将项目文件恢复到此检查点状态", "restoreFilesAndTask": "恢复文件和任务", "confirm": "确认", "cancel": "取消", "cannotUndo": "此操作无法撤消。", "restoreFilesAndTaskDescription": "恢复文件至此时状态，并清除后续对话记录"}, "current": "当前"}, "instructions": {"wantsToFetch": "<PERSON>oo 想要获取详细指示以协助当前任务"}, "fileOperations": {"wantsToRead": "需要读取文件:", "wantsToReadOutsideWorkspace": "请求访问外部文件:", "didRead": "已读取文件:", "wantsToEdit": "需要编辑文件:", "wantsToEditOutsideWorkspace": "需要编辑外部文件:", "wantsToCreate": "需要新建文件:"}, "directoryOperations": {"wantsToViewTopLevel": "需要查看目录文件列表:", "didViewTopLevel": "已查看目录文件列表:", "wantsToViewRecursive": "需要查看目录所有文件:", "didViewRecursive": "已查看目录所有文件:", "wantsToViewDefinitions": "<PERSON>oo想查看此目录中使用的源代码定义名称:", "didViewDefinitions": "<PERSON>oo已查看此目录中使用的源代码定义名称:", "wantsToSearch": "需要搜索内容: {{regex}}", "didSearch": "已完成内容搜索: {{regex}}"}, "commandOutput": "命令输出", "response": "响应", "arguments": "参数", "mcp": {"wantsToUseTool": "Roo想在{{serverName}} MCP上使用工具:", "wantsToAccessResource": "<PERSON>oo想访问{{serverName}} MCP服务上的资源:"}, "modes": {"wantsToSwitch": "即将切换至{{mode}}模式", "wantsToSwitchWithReason": "即将切换至{{mode}}模式（原因：{{reason}}）", "didSwitch": "已切换至{{mode}}模式", "didSwitchWithReason": "已切换至{{mode}}模式（原因：{{reason}}）"}, "subtasks": {"wantsToCreate": "<PERSON><PERSON>想在<code>{{mode}}</code>模式下创建新子任务:", "wantsToFinish": "<PERSON><PERSON>想完成此子任务", "newTaskContent": "子任务说明", "completionContent": "子任务已完成", "resultContent": "子任务结果", "defaultResult": "请继续下一个任务。", "completionInstructions": "子任务已完成！您可以查看结果并提出修改或下一步建议。如果一切正常，请确认以将结果返回给主任务。"}, "questions": {"hasQuestion": "<PERSON><PERSON>有一个问题:"}, "taskCompleted": "任务完成", "shellIntegration": {"unavailable": "Shell集成不可用", "troubleshooting": "仍有问题吗？"}, "powershell": {"issues": "看起来您遇到了Windows PowerShell问题，请参阅此"}, "autoApprove": {"title": "自动批准:", "none": "无", "description": "允许直接执行操作无需确认，请谨慎启用。前往<settingsLink>设置</settingsLink>调整", "actions": {"readFiles": {"label": "读取文件和目录", "shortName": "读取", "description": "允许读取系统中的文件内容"}, "editFiles": {"label": "编辑文件", "shortName": "编辑", "description": "允许修改系统中的文件"}, "executeCommands": {"label": "执行命令", "shortName": "命令", "description": "允许执行终端命令。"}, "useBrowser": {"label": "浏览器", "shortName": "浏览器", "description": "允许通过无头浏览器访问网站"}, "useMcp": {"label": "MCP服务", "shortName": "MCP", "description": "允许访问配置好的 MCP 服务（可能涉及文件系统或API操作）"}, "switchModes": {"label": "切换模式", "shortName": "模式", "description": "允许自动切换工作模式"}, "subtasks": {"label": "创建和完成子任务", "shortName": "子任务", "description": "允许自主创建和管理子任务"}, "retryRequests": {"label": "重试失败的请求", "shortName": "重试", "description": "API请求失败时自动重试"}}}, "reasoning": {"thinking": "思考中", "seconds": "{{count}}秒"}, "followUpSuggest": {"copyToInput": "复制到输入框（或按住Shift点击）"}, "announcement": {"title": "允许任务拆分", "description": "将复杂任务拆分到不同模式（编程/架构/调试）执行", "learnMore": "了解更多 →", "hideButton": "隐藏公告"}, "browser": {"rooWantsToUse": "<PERSON><PERSON>想使用浏览器:", "consoleLogs": "控制台日志", "noNewLogs": "(没有新日志)", "screenshot": "浏览器截图", "cursor": "光标", "navigation": {"step": "步骤 {{current}} / {{total}}", "previous": "上一步", "next": "下一步"}, "sessionStarted": "浏览器会话已启动", "actions": {"title": "浏览器操作: ", "launch": "访问 {{url}}", "click": "点击 ({{coordinate}})", "type": "输入 \"{{text}}\"", "scrollDown": "向下滚动", "scrollUp": "向上滚动", "close": "关闭浏览器"}}}