{"common": {"save": "保存", "done": "完了", "cancel": "キャンセル", "reset": "リセット", "select": "選択"}, "header": {"title": "設定", "saveButtonTooltip": "変更を保存", "nothingChangedTooltip": "変更なし", "doneButtonTooltip": "未保存の変更を破棄して設定パネルを閉じる"}, "unsavedChangesDialog": {"title": "未保存の変更", "description": "変更を破棄して続行しますか？", "cancelButton": "キャンセル", "discardButton": "変更を破棄"}, "sections": {"providers": "プロバイダー", "autoApprove": "自動承認", "browser": "ブラウザ / コンピューター使用", "checkpoints": "チェックポイント", "notifications": "通知", "contextManagement": "コンテキスト管理", "terminal": "ターミナル", "advanced": "詳細設定", "experimental": "実験的機能", "language": "言語", "about": "Roo Codeについて"}, "autoApprove": {"description": "Rooが承認なしで自動的に操作を実行できるようにします。AIを完全に信頼し、関連するセキュリティリスクを理解している場合にのみ、これらの設定を有効にしてください。", "readOnly": {"label": "読み取り専用操作を常に承認", "description": "有効にすると、Rooは承認ボタンをクリックすることなく、自動的にディレクトリの内容を表示してファイルを読み取ります。", "outsideWorkspace": {"label": "ワークスペース外のファイルを含める", "description": "Rooが承認なしで現在のワークスペース外のファイルを読み取ることを許可します。"}}, "write": {"label": "書き込み操作を常に承認", "description": "承認なしで自動的にファイルを作成・編集", "delayLabel": "診断が潜在的な問題を検出できるよう、書き込み後に遅延を設ける", "outsideWorkspace": {"label": "ワークスペース外のファイルを含める", "description": "Rooが承認なしで現在のワークスペース外のファイルを作成・編集することを許可します。"}}, "browser": {"label": "ブラウザアクションを常に承認", "description": "承認なしで自動的にブラウザアクションを実行", "note": "注意：コンピューター使用をサポートするモデルを使用している場合のみ適用されます"}, "retry": {"label": "失敗したAPIリクエストを常に再試行", "description": "サーバーがエラーレスポンスを返した場合、自動的に失敗したAPIリクエストを再試行", "delayLabel": "リクエスト再試行前の遅延"}, "mcp": {"label": "MCPツールを常に承認", "description": "MCPサーバービューで個々のMCPツールの自動承認を有効にします（この設定とツールの「常に許可」チェックボックスの両方が必要）"}, "modeSwitch": {"label": "モード切り替えを常に承認", "description": "承認なしで自動的に異なるモード間を切り替え"}, "subtasks": {"label": "サブタスクの作成と完了を常に承認", "description": "承認なしでサブタスクの作成と完了を許可"}, "execute": {"label": "許可された実行操作を常に承認", "description": "承認なしで自動的に許可されたターミナルコマンドを実行", "allowedCommands": "許可された自動実行コマンド", "allowedCommandsDescription": "「実行操作を常に承認」が有効な場合に自動実行できるコマンドプレフィックス。すべてのコマンドを許可するには * を追加します（注意して使用してください）。", "commandPlaceholder": "コマンドプレフィックスを入力（例：'git '）", "addButton": "追加"}}, "providers": {"configProfile": "設定プロファイル", "providerDocumentation": "{{provider}}のドキュメント", "description": "異なるAPI設定を保存して、プロバイダーと設定をすばやく切り替えることができます。", "apiProvider": "APIプロバイダー", "model": "モデル", "nameEmpty": "名前を空にすることはできません", "nameExists": "この名前のプロファイルは既に存在します", "deleteProfile": "プロファイルを削除", "invalidArnFormat": "無効なARN形式です。上記の例を確認してください。", "enterNewName": "新しい名前を入力してください", "addProfile": "プロファイルを追加", "renameProfile": "プロファイル名を変更", "newProfile": "新しい構成プロファイル", "enterProfileName": "プロファイル名を入力", "createProfile": "プロファイルを作成", "cannotDeleteOnlyProfile": "唯一のプロファイルは削除できません", "searchPlaceholder": "プロファイルを検索", "noMatchFound": "一致するプロファイルが見つかりません", "vscodeLmDescription": "VS Code言語モデルAPIを使用すると、他のVS Code拡張機能（GitHub Copilotなど）が提供するモデルを実行できます。最も簡単な方法は、VS Code MarketplaceからCopilotおよびCopilot Chat拡張機能をインストールすることです。", "awsCustomArnUse": "使用したいモデルの有効なAWS Bedrock ARNを入力してください。形式の例:", "awsCustomArnDesc": "ARN内のリージョンが上で選択したAWSリージョンと一致していることを確認してください。", "openRouterApiKey": "OpenRouter APIキー", "getOpenRouterApiKey": "OpenRouter APIキーを取得", "apiKeyStorageNotice": "APIキーはVSCodeのシークレットストレージに安全に保存されます", "glamaApiKey": "Glama APIキー", "getGlamaApiKey": "Glama APIキーを取得", "useCustomBaseUrl": "カスタムベースURLを使用", "useHostHeader": "カスタムHostヘッダーを使用", "useLegacyFormat": "レガシーOpenAI API形式を使用", "requestyApiKey": "Requesty APIキー", "getRequestyApiKey": "Requesty APIキーを取得", "openRouterTransformsText": "プロンプトとメッセージチェーンをコンテキストサイズに圧縮 (<a>OpenRouter Transforms</a>)", "anthropicApiKey": "Anthropic APIキー", "getAnthropicApiKey": "Anthropic APIキーを取得", "deepSeekApiKey": "DeepSeek APIキー", "getDeepSeekApiKey": "DeepSeek APIキーを取得", "geminiApiKey": "Gemini APIキー", "getGeminiApiKey": "Gemini APIキーを取得", "openAiApiKey": "OpenAI APIキー", "openAiBaseUrl": "ベースURL", "getOpenAiApiKey": "OpenAI APIキーを取得", "mistralApiKey": "Mistral APIキー", "getMistralApiKey": "Mistral / Codestral APIキーを取得", "codestralBaseUrl": "Codestral ベースURL（オプション）", "codestralBaseUrlDesc": "Codestralモデルの代替URLを設定します。", "awsCredentials": "AWS認証情報", "awsProfile": "AWSプロファイル", "awsProfileName": "AWSプロファイル名", "awsAccessKey": "AWSアクセスキー", "awsSecretKey": "AWSシークレットキー", "awsSessionToken": "AWSセッショントークン", "awsRegion": "AWSリージョン", "awsCrossRegion": "クロスリージョン推論を使用", "enablePromptCaching": "プロンプトキャッシュを有効化", "enablePromptCachingTitle": "サポートされているモデルのパフォーマンスを向上させ、コストを削減するためにプロンプトキャッシュを有効化します。", "cacheUsageNote": "注意：キャッシュの使用が表示されない場合は、別のモデルを選択してから希望のモデルを再度選択してみてください。", "vscodeLmModel": "言語モデル", "vscodeLmWarning": "注意：これは非常に実験的な統合であり、プロバイダーのサポートは異なります。モデルがサポートされていないというエラーが表示された場合、それはプロバイダー側の問題です。", "googleCloudSetup": {"title": "Google Cloud Vertex AIを使用するには：", "step1": "1. Google Cloudアカウントを作成し、Vertex AI APIを有効にして、希望するClaudeモデルを有効にします。", "step2": "2. Google Cloud CLIをインストールし、アプリケーションのデフォルト認証情報を設定します。", "step3": "3. または、認証情報付きのサービスアカウントを作成します。"}, "googleCloudCredentials": "Google Cloud認証情報", "googleCloudKeyFile": "Google Cloudキーファイルパス", "googleCloudProjectId": "Google Cloudプロジェクトid", "googleCloudRegion": "Google Cloudリージョン", "lmStudio": {"baseUrl": "ベースURL（オプション）", "modelId": "モデルID", "speculativeDecoding": "推論デコーディングを有効化", "draftModelId": "ドラフトモデルID", "draftModelDesc": "推論デコーディングが正しく機能するには、ドラフトモデルは同じモデルファミリーから選択する必要があります。", "selectDraftModel": "ドラフトモデルを選択", "noModelsFound": "ドラフトモデルが見つかりません。LM Studioがサーバーモードで実行されていることを確認してください。", "description": "LM Studioを使用すると、ローカルコンピューターでモデルを実行できます。始め方については、<a>クイックスタートガイド</a>をご覧ください。また、この拡張機能で使用するには、LM Studioの<b>ローカルサーバー</b>機能を起動する必要があります。<span>注意：</span>Roo Codeは複雑なプロンプトを使用し、Claudeモデルで最適に動作します。能力の低いモデルは期待通りに動作しない場合があります。"}, "ollama": {"baseUrl": "ベースURL（オプション）", "modelId": "モデルID", "description": "Ollamaを使用すると、ローカルコンピューターでモデルを実行できます。始め方については、クイックスタートガイドをご覧ください。", "warning": "注意：Roo Codeは複雑なプロンプトを使用し、Claudeモデルで最適に動作します。能力の低いモデルは期待通りに動作しない場合があります。"}, "unboundApiKey": "Unbound APIキー", "getUnboundApiKey": "Unbound APIキーを取得", "humanRelay": {"description": "APIキーは不要ですが、ユーザーはウェブチャットAIに情報をコピー＆ペーストする必要があります。", "instructions": "使用中にダイアログボックスが表示され、現在のメッセージが自動的にクリップボードにコピーされます。これらをウェブ版のAI（ChatGPTやClaudeなど）に貼り付け、AIの返答をダイアログボックスにコピーして確認ボタンをクリックする必要があります。"}, "openRouter": {"providerRouting": {"title": "OpenRouterプロバイダールーティング", "description": "OpenRouterはあなたのモデルに最適な利用可能なプロバイダーにリクエストを転送します。デフォルトでは、稼働時間を最大化するために、リクエストはトッププロバイダー間でロードバランスされます。ただし、このモデルに使用する特定のプロバイダーを選択することもできます。", "learnMore": "プロバイダールーティングについて詳しく知る"}}, "customModel": {"capabilities": "カスタムOpenAI互換モデルの機能と価格を設定します。モデルの機能はRoo Codeのパフォーマンスに影響を与える可能性があるため、慎重に指定してください。", "maxTokens": {"label": "最大出力トークン", "description": "モデルが生成できる応答の最大トークン数。（サーバーが最大トークンを設定できるようにするには-1を指定します。）"}, "contextWindow": {"label": "コンテキストウィンドウサイズ", "description": "モデルが処理できる総トークン数（入力＋出力）。"}, "imageSupport": {"label": "画像サポート", "description": "このモデルは画像の処理と理解が可能ですか？"}, "computerUse": {"label": "コンピューター使用", "description": "このモデルはブラウザとの対話が可能ですか？（例：Claude 3.7 Sonnet）"}, "promptCache": {"label": "プロンプトキャッシュ", "description": "このモデルはプロンプトのキャッシュが可能ですか？"}, "pricing": {"input": {"label": "入力価格", "description": "入力/プロンプトの100万トークンあたりのコスト。これはモデルにコンテキストと指示を送信するコストに影響します。"}, "output": {"label": "出力価格", "description": "モデルの応答の100万トークンあたりのコスト。これは生成されたコンテンツと補完のコストに影響します。"}, "cacheReads": {"label": "キャッシュ読み取り価格", "description": "キャッシュからの読み取りの100万トークンあたりのコスト。これはキャッシュされた応答を取得する際に課金される価格です。"}, "cacheWrites": {"label": "キャッシュ書き込み価格", "description": "キャッシュへの書き込みの100万トークンあたりのコスト。これはプロンプトが初めてキャッシュされる際に課金される価格です。"}}, "resetDefaults": "デフォルトにリセット"}, "rateLimitSeconds": {"label": "レート制限", "description": "APIリクエスト間の最小時間。"}}, "browser": {"enable": {"label": "ブラウザツールを有効化", "description": "有効にすると、コンピューター使用をサポートするモデルを使用する際に、Rooはウェブサイトとのやり取りにブラウザを使用できます。"}, "viewport": {"label": "ビューポートサイズ", "description": "ブラウザインタラクションのビューポートサイズを選択します。これはウェブサイトの表示方法とインタラクション方法に影響します。", "options": {"largeDesktop": "大型デスクトップ (1280x800)", "smallDesktop": "小型デスクトップ (900x600)", "tablet": "タブレット (768x1024)", "mobile": "モバイル (360x640)"}}, "screenshotQuality": {"label": "スクリーンショット品質", "description": "ブラウザスクリーンショットのWebP品質を調整します。高い値はより鮮明なスクリーンショットを提供しますが、token使用量が増加します。"}, "remote": {"label": "リモートブラウザ接続を使用", "description": "リモートデバッグを有効にして実行しているChromeブラウザに接続します（--remote-debugging-port=9222）。", "urlPlaceholder": "カスタムURL（例：http://localhost:9222）", "testButton": "接続テスト", "testingButton": "テスト中...", "instructions": "DevToolsプロトコルホストアドレスを入力するか、Chromeのローカルインスタンスを自動検出するために空のままにします。接続テストボタンは、提供されている場合はカスタムURLを試み、フィールドが空の場合は自動検出します。"}}, "checkpoints": {"enable": {"label": "自動チェックポイントを有効化", "description": "有効にすると、Rooはタスク実行中に自動的にチェックポイントを作成し、変更の確認や以前の状態への復帰を容易にします。"}}, "notifications": {"sound": {"label": "サウンドエフェクトを有効化", "description": "有効にすると、Rooは通知やイベントのためにサウンドエフェクトを再生します。", "volumeLabel": "音量"}, "tts": {"label": "音声合成を有効化", "description": "有効にすると、Rooは音声合成を使用して応答を音声で読み上げます。", "speedLabel": "速度"}}, "contextManagement": {"description": "AIのコンテキストウィンドウに含まれる情報を制御し、token使用量とレスポンスの品質に影響します", "openTabs": {"label": "オープンタブコンテキスト制限", "description": "コンテキストに含めるVSCodeオープンタブの最大数。高い値はより多くのコンテキストを提供しますが、token使用量が増加します。"}, "workspaceFiles": {"label": "ワークスペースファイルコンテキスト制限", "description": "現在の作業ディレクトリの詳細に含めるファイルの最大数。高い値はより多くのコンテキストを提供しますが、token使用量が増加します。"}, "rooignore": {"label": "リストと検索で.rooignoreファイルを表示", "description": "有効にすると、.rooignoreのパターンに一致するファイルがロックシンボル付きでリストに表示されます。無効にすると、これらのファイルはファイルリストや検索から完全に非表示になります。"}, "maxReadFile": {"label": "ファイル読み込み自動切り詰めしきい値", "description": "モデルが開始/終了の値を指定しない場合、Rooはこの行数を読み込みます。この数がファイルの総行数より少ない場合、Rooはコード定義の行番号インデックスを生成します。特殊なケース：-1はRooにファイル全体を読み込むよう指示し（インデックス作成なし）、0は行を読み込まず最小限のコンテキストのために行インデックスのみを提供するよう指示します。低い値は初期コンテキスト使用量を最小限に抑え、後続の正確な行範囲の読み込みを可能にします。明示的な開始/終了の要求はこの設定による制限を受けません。", "lines": "行", "always_full_read": "常にファイル全体を読み込む"}}, "terminal": {"outputLineLimit": {"label": "ターミナル出力制限", "description": "コマンド実行時にターミナル出力に含める最大行数。超過すると中央から行が削除され、tokenを節約します。"}, "shellIntegrationTimeout": {"label": "ターミナルシェル統合タイムアウト", "description": "コマンドを実行する前にシェル統合の初期化を待つ最大時間。シェルの起動時間が長いユーザーの場合、ターミナルで「Shell Integration Unavailable」エラーが表示される場合は、この値を増やす必要があるかもしれません。"}}, "advanced": {"diff": {"label": "diff経由の編集を有効化", "description": "有効にすると、Rooはファイルをより迅速に編集でき、切り詰められた全ファイル書き込みを自動的に拒否します。最新のClaude 3.7 Sonnetモデルで最良に機能します。", "strategy": {"label": "Diff戦略", "options": {"standard": "標準（単一ブロック）", "multiBlock": "実験的：マルチブロックdiff", "unified": "実験的：統合diff"}, "descriptions": {"standard": "標準diff戦略は一度に1つのコードブロックに変更を適用します。", "unified": "統合diff戦略はdiffを適用するための複数のアプローチを取り、最良のアプローチを選択します。", "multiBlock": "マルチブロックdiff戦略は、1つのリクエストでファイル内の複数のコードブロックを更新できます。"}}, "matchPrecision": {"label": "マッチ精度", "description": "このスライダーは、diffを適用する際にコードセクションがどれだけ正確に一致する必要があるかを制御します。低い値はより柔軟なマッチングを可能にしますが、誤った置換のリスクが高まります。100%未満の値は細心の注意を払って使用してください。"}}}, "experimental": {"warning": "⚠️", "DIFF_STRATEGY_UNIFIED": {"name": "実験的な統合diff戦略を使用する", "description": "実験的な統合diff戦略を有効にします。この戦略はモデルエラーによる再試行の回数を減らす可能性がありますが、予期しない動作や不正確な編集を引き起こす可能性があります。リスクを理解し、すべての変更を注意深く確認する準備がある場合にのみ有効にしてください。"}, "SEARCH_AND_REPLACE": {"name": "実験的な検索と置換ツールを使用する", "description": "実験的な検索と置換ツールを有効にし、Rooが1つのリクエストで検索語の複数のインスタンスを置き換えることを可能にします。"}, "INSERT_BLOCK": {"name": "実験的なコンテンツ挿入ツールを使用する", "description": "実験的なコンテンツ挿入ツールを有効にし、Rooがdiffを作成せずに特定の行番号にコンテンツを挿入できるようにします。"}, "POWER_STEERING": {"name": "実験的な「パワーステアリング」モードを使用する", "description": "有効にすると、Rooはより頻繁にモデルに現在のモード定義の詳細を思い出させます。これにより、役割定義とカスタム指示へのより強い遵守が実現しますが、メッセージごとにより多くのtokenを使用します。"}, "MULTI_SEARCH_AND_REPLACE": {"name": "実験的なマルチブロックdiffツールを使用する", "description": "有効にすると、Rooはマルチブロックdiffツールを使用します。これにより、1つのリクエストでファイル内の複数のコードブロックを更新しようとします。"}}, "temperature": {"useCustom": "カスタム温度を使用", "description": "モデルの応答のランダム性を制御します。", "rangeDescription": "高い値は出力をよりランダムに、低い値はより決定論的にします。"}, "modelInfo": {"supportsImages": "画像をサポート", "noImages": "画像をサポートしていません", "supportsComputerUse": "コンピュータ使用をサポート", "noComputerUse": "コンピュータ使用をサポートしていません", "supportsPromptCache": "プロンプトキャッシュをサポート", "noPromptCache": "プロンプトキャッシュをサポートしていません", "maxOutput": "最大出力", "inputPrice": "入力価格", "outputPrice": "出力価格", "cacheReadsPrice": "キャッシュ読み取り価格", "cacheWritesPrice": "キャッシュ書き込み価格", "enableStreaming": "ストリーミングを有効化", "enableR1Format": "R1モデルパラメータを有効にする", "enableR1FormatTips": "QWQなどのR1モデルを使用する際には、有効にする必要があります。400エラーを防ぐために", "useAzure": "Azureを使用", "azureApiVersion": "Azure APIバージョンを設定", "gemini": {"freeRequests": "* 1分間あたり{{count}}リクエストまで無料。それ以降は、プロンプトサイズに応じて課金されます。", "pricingDetails": "詳細は価格情報をご覧ください。", "billingEstimate": "* 課金は見積もりです - 正確な費用はプロンプトのサイズによって異なります。"}}, "modelPicker": {"automaticFetch": "拡張機能は<serviceLink>{{serviceName}}</serviceLink>で利用可能な最新のモデルリストを自動的に取得します。どのモデルを選ぶべきか迷っている場合、Roo Codeは<defaultModelLink>{{defaultModelId}}</defaultModelLink>で最適に動作します。また、「free」で検索すると、現在利用可能な無料オプションを見つけることができます。", "label": "モデル", "searchPlaceholder": "検索", "noMatchFound": "一致するものが見つかりません", "useCustomModel": "カスタムを使用: {{modelId}}"}, "footer": {"feedback": "質問やフィードバックがある場合は、<githubLink>github.com/RooVetGit/Roo-Code</githubLink>で問題を開くか、<redditLink>reddit.com/r/RooCode</redditLink>や<discordLink>discord.gg/roocode</discordLink>に参加してください", "version": "Roo Code v{{version}}", "telemetry": {"label": "匿名のエラーと使用状況レポートを許可", "description": "匿名の使用データとエラーレポートを送信してRoo Codeの改善にご協力ください。コード、プロンプト、個人情報が送信されることはありません。詳細については、プライバシーポリシーをご覧ください。"}, "settings": {"import": "インポート", "export": "エクスポート", "reset": "リセット"}}, "thinkingBudget": {"maxTokens": "最大 tokens", "maxThinkingTokens": "最大思考 tokens"}, "validation": {"apiKey": "有効なAPIキーを入力してください。", "awsRegion": "AWS Bedrockを使用するにはリージョンを選択してください。", "googleCloud": "有効なGoogle CloudプロジェクトIDとリージョンを入力してください。", "modelId": "有効なモデルIDを入力してください。", "modelSelector": "有効なモデルセレクターを入力してください。", "openAi": "有効なベースURL、APIキー、モデルIDを入力してください。", "arn": {"invalidFormat": "ARNの形式が無効です。フォーマット要件を確認してください。", "regionMismatch": "警告：ARN内のリージョン（{{arnRegion}}）が選択したリージョン（{{region}}）と一致しません。これによりアクセスの問題が発生する可能性があります。プロバイダーはARNのリージョンを使用します。"}, "modelAvailability": "指定されたモデルID（{{modelId}}）は利用できません。別のモデルを選択してください。"}, "placeholders": {"apiKey": "API キーを入力...", "profileName": "プロファイル名を入力", "accessKey": "アクセスキーを入力...", "secretKey": "シークレットキーを入力...", "sessionToken": "セッショントークンを入力...", "credentialsJson": "認証情報 JSON を入力...", "keyFilePath": "キーファイルのパスを入力...", "projectId": "プロジェクト ID を入力...", "customArn": "ARN を入力（例：arn:aws:bedrock:us-east-1:123456789012:foundation-model/my-model）", "baseUrl": "ベース URL を入力...", "modelId": {"lmStudio": "例：meta-llama-3.1-8b-instruct", "lmStudioDraft": "例：lmstudio-community/llama-3.2-1b-instruct", "ollama": "例：llama3.1"}, "numbers": {"maxTokens": "例：4096", "contextWindow": "例：128000", "inputPrice": "例：0.0001", "outputPrice": "例：0.0002", "cacheWritePrice": "例：0.00005"}}, "defaults": {"ollamaUrl": "デフォルト：http://localhost:11434", "lmStudioUrl": "デフォルト：http://localhost:1234", "geminiUrl": "デフォルト：https://generativelanguage.googleapis.com"}, "labels": {"customArn": "カスタム ARN", "useCustomArn": "カスタム ARN を使用..."}}