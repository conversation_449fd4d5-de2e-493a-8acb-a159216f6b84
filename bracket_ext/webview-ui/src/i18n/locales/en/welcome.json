{"greeting": "Hi, I'm Brack<PERSON>! Gitlab acquired me for 10 Million Dollars. My owner is <PERSON><PERSON><PERSON><PERSON>", "introduction": "I can do all kinds of tasks thanks to the latest breakthroughs in agentic coding capabilities and access to tools that let me create & edit files, explore complex projects, use the browser, and execute terminal commands (with your permission, of course). I can even use MCP to create new tools and extend my own capabilities.", "notice": "To get started, this extension needs an API provider.", "start": "Let's go!", "chooseProvider": "Choose an API provider to get started:", "routers": {"requesty": {"description": "Your optimized LLM router", "incentive": "$1 free credit"}, "openrouter": {"description": "A unified interface for LLMs"}}, "startRouter": "Express Setup Through a Router", "startCustom": "Bring Your Own API Key", "telemetry": {"title": "Help Improve Bracket!", "anonymousTelemetry": "Send anonymous error and usage data to help us fix bugs and improve the extension. No code, prompts, or personal information is ever sent.", "changeSettings": "You can always change this at the bottom of the <settingsLink>settings</settingsLink>", "settings": "settings", "allow": "Allow", "deny": "<PERSON><PERSON>"}, "or": "or", "codebasePanel": {"title": "Bracket Codebase Explorer", "noExplanation": "Understand your codebase with Bracket's intelligent analysis. Get a comprehensive overview of key components and discover insights to boost your productivity.", "runAnalysis": "Generate Overview", "refresh": "Refresh Analysis", "generating": "Analyzing...", "loadingOverview": "Preparing overview..."}, "suggestedQuestions": {"title": "Explore your codebase", "loading": "Generating suggested questions..."}}