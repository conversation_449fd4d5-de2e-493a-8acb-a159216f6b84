{"greeting": "What can Bracket do for you?", "task": {"title": "Task", "seeMore": "See more", "seeLess": "See less", "tokens": "Tokens:", "cache": "Cache:", "apiCost": "API Cost:", "contextWindow": "Context Window:", "closeAndStart": "Close task and start a new one", "export": "Export task history", "delete": "Delete Task (<PERSON><PERSON> + <PERSON>lick to skip confirmation)"}, "unpin": "Unpin", "pin": "<PERSON>n", "retry": {"title": "Retry", "tooltip": "Try the operation again"}, "startNewTask": {"title": "Start New Task", "tooltip": "Begin a new task"}, "proceedAnyways": {"title": "Proceed Anyways", "tooltip": "Continue while command executes"}, "save": {"title": "Save", "tooltip": "Save the file changes"}, "tokenProgress": {"availableSpace": "Available space: {{amount}} tokens", "tokensUsed": "Tokens used: {{used}} of {{total}}", "reservedForResponse": "Reserved for model response: {{amount}} tokens"}, "reject": {"title": "Reject", "tooltip": "Reject this action"}, "completeSubtaskAndReturn": "Complete Subtask and Return", "approve": {"title": "Approve", "tooltip": "Approve this action"}, "runCommand": {"title": "Run Command", "tooltip": "Execute this command"}, "proceedWhileRunning": {"title": "Proceed While Running", "tooltip": "Continue despite warnings"}, "resumeTask": {"title": "Resume Task", "tooltip": "Continue the current task"}, "terminate": {"title": "Terminate", "tooltip": "End the current task"}, "cancel": {"title": "Cancel", "tooltip": "Cancel the current operation"}, "scrollToBottom": "Scroll to bottom of chat", "aboutMe": "Thanks to the latest breakthroughs in agentic coding capabilities, I can handle complex software development tasks step-by-step. With tools that let me create & edit files, explore complex projects, use the browser, and execute terminal commands (after you grant permission), I can assist you in ways that go beyond code completion or tech support. I can even use MCP to create new tools and extend my own capabilities.", "selectMode": "Select mode for interaction", "selectApiConfig": "Select API configuration", "typeMessage": "Type a message...", "typeTask": "Ask or instruct Bracket...", "addContext": "@ to add context, / to switch modes", "dragFiles": "hold shift to drag in files", "dragFilesImages": "hold shift to drag in files/images", "errorReadingFile": "Error reading file:", "noValidImages": "No valid images were processed", "separator": "Separator", "edit": "Edit...", "forNextMode": "for next mode", "apiRequest": {"title": "API Request", "failed": "API Request Failed", "streaming": "Context Engine Request...", "cancelled": "API Request Cancelled", "streamingFailed": "API Streaming Failed"}, "checkpoint": {"initial": "Initial Checkpoint", "regular": "Checkpoint", "initializingWarning": "Still initializing checkpoint... If this takes too long, you can disable checkpoints in <settingsLink>settings</settingsLink> and restart your task.", "menu": {"viewDiff": "View Diff", "restore": "Restore Checkpoint", "restoreFiles": "Restore Files", "restoreFilesDescription": "Restores your project's files back to a snapshot taken at this point.", "restoreFilesAndTask": "Restore Files & Task", "confirm": "Confirm", "cancel": "Cancel", "cannotUndo": "This action cannot be undone.", "restoreFilesAndTaskDescription": "Restores your project's files back to a snapshot taken at this point and deletes all messages after this point."}, "current": "Current"}, "instructions": {"wantsToFetch": "<PERSON><PERSON><PERSON> wants to fetch detailed instructions to assist with the current task"}, "fileOperations": {"wantsToRead": "B<PERSON>et wants to read this file:", "wantsToReadOutsideWorkspace": "Bracket wants to read this file outside of the workspace:", "didRead": "Bracket read this file:", "wantsToEdit": "Bracket wants to edit this file:", "wantsToEditOutsideWorkspace": "Bracket wants to edit this file outside of the workspace:", "wantsToCreate": "Bracket wants to create a new file:"}, "directoryOperations": {"wantsToViewTopLevel": "Bracket wants to view the top level files in this directory:", "didViewTopLevel": "Bracket viewed the top level files in this directory:", "wantsToViewRecursive": "Bracket wants to recursively view all files in this directory:", "didViewRecursive": "Bracket recursively viewed all files in this directory:", "wantsToViewDefinitions": "Bracket wants to view source code definition names used in this directory:", "didViewDefinitions": "Bracket viewed source code definition names used in this directory:", "wantsToSearch": "Bracket wants to search this directory for <code>{{regex}}</code>:", "didSearch": "Bracket searched this directory for <code>{{regex}}</code>:"}, "commandOutput": "Command Output", "response": "Response", "arguments": "Arguments", "mcp": {"wantsToUseTool": "Bracket wants to use a tool on the {{serverName}} MCP server:", "wantsToAccessResource": "Bracket wants to access a resource on the {{serverName}} MCP server:"}, "modes": {"wantsToSwitch": "B<PERSON><PERSON> wants to switch to {{mode}} mode", "wantsToSwitchWithReason": "<PERSON><PERSON><PERSON> wants to switch to {{mode}} mode because: {{reason}}", "didSwitch": "Bracket switched to {{mode}} mode", "didSwitchWithReason": "Bracket switched to {{mode}} mode because: {{reason}}"}, "subtasks": {"wantsToCreate": "Bracket wants to create a new subtask in {{mode}} mode:", "wantsToFinish": "B<PERSON><PERSON> wants to finish this subtask", "newTaskContent": "Subtask Instructions", "completionContent": "Subtask Completed", "resultContent": "Subtask Results", "defaultResult": "Please continue to the next task.", "completionInstructions": "Subtask completed! You can review the results and suggest any corrections or next steps. If everything looks good, confirm to return the result to the parent task."}, "questions": {"hasQuestion": "Bracket has a question:"}, "taskCompleted": "Task Completed", "error": "Error", "troubleMessage": "Bracket is having trouble...", "shellIntegration": {"unavailable": "Shell Integration Unavailable", "troubleshooting": "Still having trouble?"}, "powershell": {"issues": "It seems like you're having Windows PowerShell issues, please see this"}, "autoApprove": {"title": "Auto-approve:", "none": "None", "description": "Auto-approve allows Bracket to perform actions without asking for permission. Only enable for actions you fully trust. More detailed configuration available in <settingsLink>Settings</settingsLink>.", "actions": {"readFiles": {"label": "Read files and directories", "shortName": "Read", "description": "Allows access to read any file on your computer."}, "editFiles": {"label": "Edit files", "shortName": "Edit", "description": "Allows modification of any files on your computer."}, "executeCommands": {"label": "Execute approved commands", "shortName": "Commands", "description": "Allows execution of approved terminal commands. You can configure this in the settings panel."}, "useBrowser": {"label": "Use the browser", "shortName": "Browser", "description": "Allows ability to launch and interact with any website in a headless browser."}, "useMcp": {"label": "Use MCP servers", "shortName": "MCP", "description": "Allows use of configured MCP servers which may modify filesystem or interact with APIs."}, "switchModes": {"label": "Switch modes", "shortName": "Modes", "description": "Allows automatic switching between different modes without requiring approval."}, "subtasks": {"label": "Create & complete subtasks", "shortName": "Subtasks", "description": "Allow creation and completion of subtasks without requiring approval."}, "retryRequests": {"label": "Retry failed requests", "shortName": "Retries", "description": "Automatically retry failed API requests when the provider returns an error response."}}}, "announcement": {"title": "Do more with Boomerang Tasks 🪃", "description": "Split work into subtasks with each running in a specialized mode such as code, architect, debug, or a custom mode.", "learnMore": "Learn more →", "hideButton": "Hide announcement"}, "reasoning": {"thinking": "Thinking", "seconds": "{{count}}s"}, "followUpSuggest": {"copyToInput": "Copy to input (same as shift + click)"}, "browser": {"rooWantsToUse": "Bracket wants to use the browser:", "consoleLogs": "Console <PERSON>gs", "noNewLogs": "(No new logs)", "screenshot": "Browser screenshot", "cursor": "cursor", "navigation": {"step": "Step {{current}} of {{total}}", "previous": "Previous", "next": "Next"}, "sessionStarted": "Browser Session Started", "actions": {"title": "Browse Action: ", "launch": "Launch browser at {{url}}", "click": "Click ({{coordinate}})", "type": "Type \"{{text}}\"", "scrollDown": "Scroll down", "scrollUp": "Scroll up", "close": "Close browser"}}}