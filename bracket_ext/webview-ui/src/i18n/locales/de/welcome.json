{"greeting": "<PERSON><PERSON>, ich bin Roo!", "introduction": "Ich kann alle Arten von Aufgaben erledigen, dank der neuesten Durchbrüche in agentenbasierten Codierungsfähigkeiten und dem Zugang zu Tools, die es mir ermöglichen, <PERSON><PERSON> zu erstellen und zu bearbeiten, komplexe Projekte zu erkunden, den Browser zu verwenden und Terminalbefehle auszuführen (natürlich mit deiner Erlaubnis). Ich kann sogar MCP verwenden, um neue Tools zu erstellen und meine eigenen Fähigkeiten zu erweitern.", "notice": "Um loszulegen, ben<PERSON><PERSON>gt diese Erweiterung einen API-Anbieter.", "start": "Los geht's!", "chooseProvider": "Wähle einen API-Anbieter, um zu beginnen:", "routers": {"requesty": {"description": "<PERSON>in optimierter LLM-Router", "incentive": "$1 Guthaben gratis"}, "openrouter": {"description": "Eine einheitliche Schnittstelle für LLMs"}}, "startRouter": "Express-Einrichtung über einen Router", "startCustom": "Eigenen API-Schlüssel verwenden", "telemetry": {"title": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON> zu verbessern", "anonymousTelemetry": "Sende anonyme Fehler- und Nutzungsdaten, um uns bei der Fehlerbehebung und Verbesserung der Erweiterung zu helfen. Es werden niemals Code, Texte oder persönliche Informationen gesendet.", "changeSettings": "Du kannst dies jederzeit unten in den <settingsLink>Einstellungen</settingsLink> ändern", "settings": "Einstellungen", "allow": "Erlauben", "deny": "<PERSON><PERSON><PERSON><PERSON>"}, "or": "oder"}