{"title": "MCP-Server", "done": "<PERSON><PERSON><PERSON>", "description": "Das <0>Model Context Protocol</0> ermöglicht die Kommunikation mit lokal laufenden MCP-Servern, die zusätzliche Tools und Ressourcen zur Erweiterung der Fähigkeiten von Roo bereitstellen. Du kannst <1>von der Community erstellte Server</1> verwenden oder Roo bitten, neue Tools speziell für deinen Workflow zu erstellen (z.B. \"ein Tool hinzufügen, das die neueste npm-Dokumentation abruft\").", "enableToggle": {"title": "MCP-Server aktivieren", "description": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, kann Roo mit MCP-Servern für erweiterte Funktionen interagieren. Wenn du MCP nicht verwendest, kannst du dies deaktivieren, um den Token-Verbrauch von Roo zu reduzieren."}, "enableServerCreation": {"title": "MCP-Server-Erstellung aktivieren", "description": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, kann <PERSON><PERSON> dir he<PERSON><PERSON>, neue MCP-Server über Befehle wie \"neues Tool hinzufügen zu...\" zu erstellen. Wenn du keine MCP-Server erstellen musst, kannst du dies deaktivieren, um den Token-Verbrauch von Roo zu reduzieren."}, "editGlobalMCP": "Globales MCP bearbeiten", "editProjectMCP": "Projekt-MCP bearbeiten", "tool": {"alwaysAllow": "<PERSON><PERSON> erlauben", "parameters": "Parameter", "noDescription": "<PERSON><PERSON>"}, "tabs": {"tools": "Tools", "resources": "Ressourcen"}, "emptyState": {"noTools": "<PERSON><PERSON> gefunden", "noResources": "<PERSON><PERSON>n gefunden"}, "networkTimeout": {"label": "Netzwerk-Timeout", "description": "Maximale Wartezeit für Serverantworten", "options": {"15seconds": "15 Sekunden", "30seconds": "30 Sekunden", "1minute": "1 Minute", "5minutes": "5 Minuten", "10minutes": "10 Minuten", "15minutes": "15 Minuten", "30minutes": "30 Minuten", "60minutes": "60 Minuten"}}, "deleteDialog": {"title": "MCP-Server löschen", "description": "<PERSON><PERSON> du sic<PERSON>, dass du den MCP-Server \"{{serverName}}\" löschen möchtest? Diese Aktion kann nicht rückgängig gemacht werden.", "cancel": "Abbrechen", "delete": "Löschen"}, "serverStatus": {"retrying": "<PERSON><PERSON><PERSON><PERSON>...", "retryConnection": "Verbindung wiederherstellen"}}