{"greeting": "Was kann Roo für dich tun?", "task": {"title": "Aufgabe", "seeMore": "<PERSON><PERSON> anzeigen", "seeLess": "<PERSON><PERSON> anzeigen", "tokens": "Tokens:", "cache": "Cache:", "apiCost": "API-Kosten:", "contextWindow": "Kontextfenster:", "closeAndStart": "Aufgabe schließen und neue starten", "export": "Aufgabenverlauf exportieren", "delete": "Aufgabe löschen (Shift + Klick zum Überspringen der Bestätigung)"}, "unpin": "<PERSON><PERSON><PERSON>", "pin": "Anheften", "tokenProgress": {"availableSpace": "Verfügbarer S<PERSON>icher: {{amount}} Tokens", "tokensUsed": "Verwen<PERSON><PERSON> Tokens: {{used}} von {{total}}", "reservedForResponse": "Reserviert für Modellantwort: {{amount}} Tokens"}, "retry": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tooltip": "Versuch erneut starten"}, "startNewTask": {"title": "Neue Aufgabe starten", "tooltip": "<PERSON><PERSON>ne eine neue Aufgabe"}, "proceedAnyways": {"title": "Trotzdem fortfahren", "tooltip": "Während der Befehlsausführung fortfahren"}, "save": {"title": "Speichern", "tooltip": "Dateiänderungen speichern"}, "reject": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Diese Aktion ablehnen"}, "completeSubtaskAndReturn": "Teilaufgabe abschließen und zurückkehren", "approve": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Diese Aktion genehmigen"}, "runCommand": {"title": "Befehl ausführen", "tooltip": "<PERSON>sen Befehl ausführen"}, "proceedWhileRunning": {"title": "Während Ausführung fortfahren", "tooltip": "Trotz Warnungen fortfahren"}, "resumeTask": {"title": "Aufgabe fortsetzen", "tooltip": "Aktuelle Aufgabe fortsetzen"}, "terminate": {"title": "<PERSON>den", "tooltip": "Aktuelle Aufgabe beenden"}, "cancel": {"title": "Abbrechen", "tooltip": "Aktuelle Operation abbrechen"}, "scrollToBottom": "<PERSON><PERSON> Chat-<PERSON><PERSON> scrollen", "aboutMe": "Dank neuester Durchbrü<PERSON> in agentischen Coding-Fähigkeiten kann ich komplexe Softwareentwicklungsaufgaben schrittweise bearbeiten. <PERSON><PERSON> <PERSON>, die mir er<PERSON><PERSON>, <PERSON><PERSON> zu erstellen und zu bearbeiten, komplexe Projekte zu erkunden, den Browser zu nutzen und Terminalbefehle auszuführen (nachdem du die Erlaubnis erteilt hast), kann ich dir auf Wei<PERSON> helfen, die über Code-Vervollständigung oder technischen Support hinausgehen. Ich kann sogar MCP nutzen, um neue Werkzeuge zu erstellen und meine eigenen Fähigkeiten zu erweitern.", "selectMode": "Interaktionsmodus auswählen", "selectApiConfig": "API-Konfiguration auswählen", "enhancePrompt": "Prompt mit zusätzlichem Kontext verbessern", "addImages": "Bilder zur Nachricht hinzufügen", "sendMessage": "Nachricht senden", "typeMessage": "Nachricht eingeben...", "typeTask": "Gib deine Aufgabe hier ein...", "addContext": "@ für Kontext, / zum Moduswechsel", "dragFiles": "Shift halten, um Dateien einzufügen", "dragFilesImages": "Shift halten, um Dateien/Bilder einzufügen", "enhancePromptDescription": "Die Schaltfläche 'Prompt verbessern' hi<PERSON><PERSON>, deine Anfrage durch zusätzlichen Kontext, Klarstellungen oder Umformulierungen zu verbessern. <PERSON><PERSON><PERSON>, hier eine Anfrage einzugeben und klicke erneut auf die Schaltfläche, um zu sehen, wie es funktioniert.", "errorReadingFile": "<PERSON><PERSON> beim Lesen der Datei:", "noValidImages": "<PERSON><PERSON> gültigen Bilder wurden verarbeitet", "separator": "Trennlinie", "edit": "Bearbeiten...", "forNextMode": "für nächsten Modus", "error": "<PERSON><PERSON>", "troubleMessage": "Roo hat Probleme...", "apiRequest": {"title": "API-Anfrage", "failed": "API-Anfrage fehlgeschlagen", "streaming": "API-Anfrage...", "cancelled": "API-Anfrage abgebrochen", "streamingFailed": "API-Streaming fehlgeschlagen"}, "checkpoint": {"initial": "Initialer Checkpoint", "regular": "Checkpoint", "initializingWarning": "Checkpoint wird noch initialisiert... <PERSON> dies zu lange dauert, kannst du Checkpoints in den <settingsLink>Einstellungen</settingsLink> deaktivieren und deine Aufgabe neu starten.", "menu": {"viewDiff": "Unterschiede anzeigen", "restore": "Checkpoint wiederherstellen", "restoreFiles": "<PERSON><PERSON> wied<PERSON>", "restoreFilesDescription": "Stellt die Dateien deines Projekts auf einen Snapshot zurück, der an diesem Punkt erstellt wurde.", "restoreFilesAndTask": "Dateien & Aufgabe wiederherstellen", "confirm": "Bestätigen", "cancel": "Abbrechen", "cannotUndo": "Diese Aktion kann nicht rückgängig gemacht werden.", "restoreFilesAndTaskDescription": "Stellt die Dateien deines Projekts auf einen Snapshot zurück, der an diesem Punkt erstellt wurde, und löscht alle Nachrichten nach diesem Punkt."}, "current": "Aktuell"}, "instructions": {"wantsToFetch": "<PERSON>oo möchte detaillierte Anweisungen abrufen, um bei der aktuellen Aufgabe zu helfen"}, "fileOperations": {"wantsToRead": "<PERSON><PERSON> möchte diese Datei lesen:", "wantsToReadOutsideWorkspace": "Roo möchte diese Datei außerhalb des Arbeitsbereichs lesen:", "didRead": "Roo hat diese <PERSON><PERSON> gelesen:", "wantsToEdit": "<PERSON><PERSON> möchte diese Datei bearbeiten:", "wantsToEditOutsideWorkspace": "Roo möchte diese Datei außerhalb des Arbeitsbereichs bearbeiten:", "wantsToCreate": "<PERSON><PERSON> möchte eine neue Datei erstellen:"}, "directoryOperations": {"wantsToViewTopLevel": "Roo möchte die Dateien auf oberster Ebene in diesem Verzeichnis anzeigen:", "didViewTopLevel": "Roo hat die Dateien auf oberster Ebene in diesem Verzeichnis angezeigt:", "wantsToViewRecursive": "Roo möchte rekursiv alle Dateien in diesem Verzeichnis anzeigen:", "didViewRecursive": "Roo hat rekursiv alle Dateien in diesem Verzeichnis angezeigt:", "wantsToViewDefinitions": "Roo möchte Quellcode-Definitionsnamen in diesem Verzeichnis anzeigen:", "didViewDefinitions": "Roo hat Quellcode-Definitionsnamen in diesem Verzeichnis angezeigt:", "wantsToSearch": "<PERSON><PERSON> möchte dieses Verzeichnis nach <code>{{regex}}</code> durchsuchen:", "didSearch": "Roo hat dieses Verzeichnis nach <code>{{regex}}</code> durchsucht:"}, "commandOutput": "Befehlsausgabe", "response": "Antwort", "arguments": "Argumente", "mcp": {"wantsToUseTool": "<PERSON>oo möchte ein Tool auf dem {{serverName}} MCP-Server verwenden:", "wantsToAccessResource": "<PERSON>oo möchte auf eine Ressource auf dem {{serverName}} MCP-Server zugreifen:"}, "modes": {"wantsToSwitch": "<PERSON><PERSON> möchte zum <code>{{mode}}</code>-<PERSON><PERSON> wechseln", "wantsToSwitchWithReason": "<PERSON><PERSON> möchte zum <code>{{mode}}</code>-<PERSON><PERSON> wechseln, weil: {{reason}}", "didSwitch": "Roo hat zum <code>{{mode}}</code>-Modus gewechselt", "didSwitchWithReason": "Roo hat zum <code>{{mode}}</code>-<PERSON><PERSON> gewechselt, weil: {{reason}}"}, "subtasks": {"wantsToCreate": "<PERSON><PERSON> möchte eine neue Teilaufgabe im <code>{{mode}}</code>-Modus erstellen:", "wantsToFinish": "<PERSON>oo möchte diese Teilaufgabe abschließen", "newTaskContent": "Teilaufgabenanweisungen", "completionContent": "Teilaufgabe abgeschlossen", "resultContent": "Teilaufgabenergebnisse", "defaultResult": "Bitte fahre mit der nächsten Aufgabe fort.", "completionInstructions": "Teilaufgabe abgeschlossen! Du kannst die Ergebnisse überprüfen und Korrekturen oder nächste Schritte vorschlagen. Wenn alles gut aussieht, bestätige, um das Ergebnis an die übergeordnete Aufgabe zurückzugeben."}, "questions": {"hasQuestion": "Roo hat eine Frage:"}, "taskCompleted": "Aufgabe abgeschlossen", "shellIntegration": {"unavailable": "Shell-Integration nicht verfügbar", "troubleshooting": "Immer noch Probleme?"}, "powershell": {"issues": "<PERSON><PERSON> sche<PERSON>, dass du Probleme mit Windows PowerShell hast, bitte sieh dir dies an"}, "autoApprove": {"title": "Automatische Genehmigung:", "none": "<PERSON><PERSON>", "description": "Automatische Genehmigung erlaubt Roo Code, Aktionen ohne Nachfrage auszuführen. Aktiviere dies nur für Aktionen, denen du vollständig vertraust. Detailliertere Konfiguration verfügbar in den <settingsLink>Einstellungen</settingsLink>.", "actions": {"readFiles": {"label": "Dateien und Verzeichnisse lesen", "shortName": "<PERSON><PERSON>", "description": "Erlaubt Zugriff zum Lesen jeder Datei auf deinem Computer."}, "editFiles": {"label": "<PERSON><PERSON> bear<PERSON>", "shortName": "<PERSON><PERSON><PERSON>", "description": "Erlaubt die Änderung jeder Datei auf deinem Computer."}, "executeCommands": {"label": "Genehmigte Befehle ausführen", "shortName": "<PERSON><PERSON><PERSON><PERSON>", "description": "Erlaubt die Ausführung genehmigter Terminal-Befehle. Du kannst dies im Einstellungsfenster konfigurieren."}, "useBrowser": {"label": "Browser verwenden", "shortName": "Browser", "description": "Erlaubt die Fähigkeit, jede Website in einem Headless-<PERSON><PERSON><PERSON> zu starten und mit ihr zu interagieren."}, "useMcp": {"label": "MCP-Server verwenden", "shortName": "MCP", "description": "Erlaubt die Verwendung konfigurierter MCP-Server, die das Dateisystem ändern oder mit APIs interagieren können."}, "switchModes": {"label": "<PERSON><PERSON>", "shortName": "<PERSON><PERSON>", "description": "Erlaubt automatischen Wechsel zwischen verschiedenen Modi ohne erforderliche Genehmigung."}, "subtasks": {"label": "Teilaufgaben erstellen & abschließen", "shortName": "Teilaufgaben", "description": "Erlaubt die Erstellung und den Abschluss von Teilaufgaben ohne erforderliche Genehmigung."}, "retryRequests": {"label": "Fehlgeschlagene Anfragen wiederholen", "shortName": "Wiederholungen", "description": "Wiederholt automatisch fehlgeschlagene API-Anfragen, wenn der Anbieter eine Fehlermeldung zurückgibt."}}}, "reasoning": {"thinking": "<PERSON><PERSON> nach", "seconds": "{{count}}s"}, "followUpSuggest": {"copyToInput": "In Eingabefeld kopieren (oder Shift + Klick)"}, "announcement": {"title": "Mach mehr mit Boomerang Tasks 🪃", "description": "<PERSON>ile deine Arbeit in Unteraufgaben auf, die jeweils in einem spezialisierten Modus laufen, wie code, architect, debug oder einem benutzerdefinierten Modus.", "learnMore": "<PERSON><PERSON> er<PERSON>hren →", "hideButton": "Ankündigung ausblenden"}, "browser": {"rooWantsToUse": "<PERSON><PERSON> möchte den Browser verwenden:", "consoleLogs": "Konsolenprotokolle", "noNewLogs": "(<PERSON><PERSON>)", "screenshot": "Browser-Screenshot", "cursor": "<PERSON><PERSON><PERSON>", "navigation": {"step": "Schritt {{current}} von {{total}}", "previous": "Zurück", "next": "<PERSON><PERSON>"}, "sessionStarted": "Browser-Sitzung gestartet", "actions": {"title": "Browser-Aktion: ", "launch": "Browser starten auf {{url}}", "click": "Klicken ({{coordinate}})", "type": "Eingeben \"{{text}}\"", "scrollDown": "Nach unten scrollen", "scrollUp": "Nach oben scrollen", "close": "<PERSON><PERSON><PERSON> schließen"}}}