{"title": "MCP 伺服器", "done": "完成", "description": "<0>Model Context Protocol</0> 能與本機執行的 MCP 伺服器通訊，提供額外的工具和資源來擴展 Roo 的功能。您可以使用<1>社群開發的伺服器</1>，或請 Roo 為您的工作流程建立新工具（例如「新增一個取得最新 npm 文件的工具」）。", "enableToggle": {"title": "啟用 MCP 伺服器", "description": "啟用後，Roo 將能與 MCP 伺服器互動以取得進階功能。如果您不使用 MCP，可以停用此功能以減少 Roo 的 token 使用量。"}, "enableServerCreation": {"title": "啟用 MCP 伺服器建立", "description": "啟用後，Roo 可以透過如「新增工具到...」等命令協助您建立新的 MCP 伺服器。如果您不需要建立 MCP 伺服器，可以停用此功能以減少 Roo 的 token 使用量。"}, "editGlobalMCP": "編輯全域 MCP", "editProjectMCP": "編輯專案 MCP", "editSettings": "編輯 MCP 設定", "tool": {"alwaysAllow": "總是允許", "parameters": "參數", "noDescription": "無說明"}, "tabs": {"tools": "工具", "resources": "資源"}, "emptyState": {"noTools": "找不到工具", "noResources": "找不到資源"}, "networkTimeout": {"label": "網路逾時", "description": "等待伺服器回應的最長時間", "options": {"15seconds": "15 秒", "30seconds": "30 秒", "1minute": "1 分鐘", "5minutes": "5 分鐘", "10minutes": "10 分鐘", "15minutes": "15 分鐘", "30minutes": "30 分鐘", "60minutes": "60 分鐘"}}, "deleteDialog": {"title": "刪除 MCP 伺服器", "description": "您確定要刪除 MCP 伺服器「{{serverName}}」嗎？此操作無法復原。", "cancel": "取消", "delete": "刪除"}, "serverStatus": {"retrying": "重試中...", "retryConnection": "重試連線"}}