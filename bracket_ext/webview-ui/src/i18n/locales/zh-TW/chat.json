{"greeting": "<PERSON>oo 可以為您做些什麼？", "task": {"title": "工作", "seeMore": "顯示更多", "seeLess": "顯示較少", "tokens": "Tokens:", "cache": "快取：", "apiCost": "API 費用：", "contextWindow": "上下文視窗：", "closeAndStart": "關閉現有工作並開始一項新的工作", "export": "匯出工作紀錄", "delete": "刪除工作（按住 Shift 並點選可跳過確認）"}, "unpin": "取消置頂", "pin": "置頂", "tokenProgress": {"availableSpace": "可用空間：{{amount}} tokens", "tokensUsed": "已使用 tokens: {{used}} / {{total}}", "reservedForResponse": "為模型回應保留：{{amount}} tokens"}, "retry": {"title": "重試", "tooltip": "再次嘗試操作"}, "startNewTask": {"title": "開始新工作", "tooltip": "開始一項新工作"}, "proceedAnyways": {"title": "仍要繼續", "tooltip": "在命令執行時繼續"}, "save": {"title": "儲存", "tooltip": "儲存檔案變更"}, "reject": {"title": "拒絕", "tooltip": "拒絕此操作"}, "completeSubtaskAndReturn": "完成子工作並返回", "approve": {"title": "核准", "tooltip": "核准此操作"}, "runCommand": {"title": "執行命令", "tooltip": "執行此命令"}, "proceedWhileRunning": {"title": "執行時繼續", "tooltip": "儘管有警告仍繼續執行"}, "resumeTask": {"title": "繼續工作", "tooltip": "繼續目前的工作"}, "terminate": {"title": "終止", "tooltip": "結束目前的工作"}, "cancel": {"title": "取消", "tooltip": "取消目前操作"}, "scrollToBottom": "捲動至對話框底部", "aboutMe": "由於程式代理功能的最新突破，我能夠逐步處理複雜的軟體開發工作。透過允許我建立和編輯檔案、探索複雜專案、使用瀏覽器和執行終端機命令（在您授權後）的工具，我可以以超越程式碼補全或技術支援的方式協助您。我甚至可以使用 MCP 建立新工具並擴展自己的能力。", "selectMode": "選擇互動模式", "selectApiConfig": "選擇 API 設定", "enhancePrompt": "使用額外內容增強提示", "addImages": "新增圖片到訊息中", "sendMessage": "傳送訊息", "typeMessage": "輸入訊息...", "typeTask": "在此處輸入您的工作...", "addContext": "輸入 @ 新增內容，輸入 / 切換模式", "dragFiles": "按住 Shift 鍵拖曳檔案", "dragFilesImages": "按住 Shift 鍵拖曳檔案/圖片", "enhancePromptDescription": "「增強提示」按鈕透過提供額外內容、說明或重新表述來幫助改進您的請求。嘗試在此處輸入請求，然後再次點選按鈕以了解其運作方式。", "errorReadingFile": "讀取檔案時發生錯誤：", "noValidImages": "未處理到任何有效圖片", "separator": "分隔符號", "edit": "編輯...", "forNextMode": "用於下一個模式", "error": "錯誤", "troubleMessage": "<PERSON><PERSON> 遇到問題...", "apiRequest": {"title": "API 請求", "failed": "API 請求失敗", "streaming": "正在處理 API 請求...", "cancelled": "API 請求已取消", "streamingFailed": "API 串流處理失敗"}, "checkpoint": {"initial": "初始檢查點", "regular": "檢查點", "initializingWarning": "正在初始化檢查點...如果耗時過長，你可以在<settingsLink>設定</settingsLink>中停用檢查點並重新啟動任務。", "menu": {"viewDiff": "檢視差異", "restore": "還原檢查點", "restoreFiles": "還原檔案", "restoreFilesDescription": "將您的專案檔案還原到此時的快照。", "restoreFilesAndTask": "還原檔案和工作", "confirm": "確認", "cancel": "取消", "cannotUndo": "此操作無法復原。", "restoreFilesAndTaskDescription": "將您的專案檔案還原到此時的快照，並刪除此點之後的所有訊息。"}, "current": "目前"}, "instructions": {"wantsToFetch": "<PERSON>oo 想要取得詳細指示以協助目前任務"}, "fileOperations": {"wantsToRead": "<PERSON><PERSON> 想要讀取此檔案：", "wantsToReadOutsideWorkspace": "<PERSON>oo 想要讀取此工作區外的檔案：", "didRead": "<PERSON><PERSON> 已讀取此檔案：", "wantsToEdit": "<PERSON><PERSON> 想要編輯此檔案：", "wantsToEditOutsideWorkspace": "<PERSON>oo 想要編輯此工作區外的檔案：", "wantsToCreate": "<PERSON><PERSON> 想要建立新檔案："}, "directoryOperations": {"wantsToViewTopLevel": "<PERSON>oo 想要檢視此目錄中最上層的檔案：", "didViewTopLevel": "<PERSON>oo 已檢視此目錄中最上層的檔案：", "wantsToViewRecursive": "<PERSON>oo 想要遞迴檢視此目錄中的所有檔案：", "didViewRecursive": "<PERSON>oo 已遞迴檢視此目錄中的所有檔案：", "wantsToViewDefinitions": "<PERSON>oo 想要檢視此目錄中使用的原始碼定義名稱：", "didViewDefinitions": "<PERSON>oo 已檢視此目錄中使用的原始碼定義名稱：", "wantsToSearch": "<PERSON><PERSON> 想要在此目錄中搜尋 <code>{{regex}}</code>：", "didSearch": "<PERSON><PERSON> 已在此目錄中搜尋 <code>{{regex}}</code>："}, "commandOutput": "命令輸出", "response": "回應", "arguments": "參數", "mcp": {"wantsToUseTool": "Roo 想要在 {{serverName}} MCP 伺服器上使用工具：", "wantsToAccessResource": "Roo 想要存取 {{serverName}} MCP 伺服器上的資源："}, "modes": {"wantsToSwitch": "<PERSON><PERSON> 想要切換至 <code>{{mode}}</code> 模式", "wantsToSwitchWithReason": "<PERSON><PERSON> 想要切換至 <code>{{mode}}</code> 模式，原因：{{reason}}", "didSwitch": "<PERSON>oo 已切換至 <code>{{mode}}</code> 模式", "didSwitchWithReason": "<PERSON><PERSON> 已切換至 <code>{{mode}}</code> 模式，原因：{{reason}}"}, "subtasks": {"wantsToCreate": "<PERSON><PERSON> 想要在 <code>{{mode}}</code> 模式下建立新的子工作：", "wantsToFinish": "<PERSON><PERSON> 想要完成此子工作", "newTaskContent": "子工作指示", "completionContent": "子工作已完成", "resultContent": "子工作結果", "defaultResult": "請繼續下一個工作。", "completionInstructions": "子工作已完成！您可以檢閱結果並提出修正或下一步建議。如果一切看起來良好，請確認以將結果傳回主工作。"}, "questions": {"hasQuestion": "<PERSON><PERSON> 有一個問題："}, "taskCompleted": "工作完成", "shellIntegration": {"unavailable": "Shell 整合功能無法使用", "troubleshooting": "仍有問題嗎？"}, "powershell": {"issues": "看起來您遇到了 Windows PowerShell 的問題，請參考此處"}, "autoApprove": {"title": "自動核准：", "none": "無", "description": "自動核准讓 Roo Code 可以在無需徵求您同意的情況下執行動作。請僅對您完全信任的動作啟用此功能。您可以在<settingsLink>設定</settingsLink>中進行更詳細的調整。", "actions": {"readFiles": {"label": "讀取檔案和目錄", "shortName": "讀取", "description": "允許存取電腦上的任何檔案。"}, "editFiles": {"label": "編輯檔案", "shortName": "編輯", "description": "允許修改電腦上的任何檔案。"}, "executeCommands": {"label": "執行已核准的命令", "shortName": "命令", "description": "允許執行已核准的終端機命令。您可以在設定面板中調整此設定。"}, "useBrowser": {"label": "使用瀏覽器", "shortName": "瀏覽器", "description": "允許在無介面瀏覽器中啟動並與任何網站互動。"}, "useMcp": {"label": "使用 MCP 伺服器", "shortName": "MCP", "description": "允許使用已設定的 MCP 伺服器，這些伺服器可能會修改檔案系統或與 API 進行互動。"}, "switchModes": {"label": "切換模式", "shortName": "模式", "description": "允許在不需要核准的情況下自動切換不同模式。"}, "subtasks": {"label": "建立和完成子工作", "shortName": "子工作", "description": "允許在不需要核准的情況下建立和完成子工作。"}, "retryRequests": {"label": "重試失敗的請求", "shortName": "重試", "description": "當服務提供者回傳錯誤回應時自動重試失敗的 API 請求。"}}}, "reasoning": {"thinking": "思考中", "seconds": "{{count}}秒"}, "followUpSuggest": {"copyToInput": "複製到輸入框（或按住 Shift 並點選）"}, "announcement": {"title": "使用迴旋鏢任務完成更多工作 🪃", "description": "將工作拆分成子任務，每個子任務在專門的模式中執行，如 code、architect、debug 或自訂模式。", "learnMore": "了解更多 →", "hideButton": "隱藏公告"}, "browser": {"rooWantsToUse": "<PERSON><PERSON> 想要使用瀏覽器：", "consoleLogs": "主控台記錄", "noNewLogs": "（沒有新記錄）", "screenshot": "瀏覽器螢幕擷圖", "cursor": "游標", "navigation": {"step": "步驟 {{current}} / {{total}}", "previous": "上一步", "next": "下一步"}, "sessionStarted": "瀏覽器工作階段已啟動", "actions": {"title": "瀏覽器動作：", "launch": "在 {{url}} 啟動瀏覽器", "click": "點選 ({{coordinate}})", "type": "輸入「{{text}}」", "scrollDown": "向下捲動", "scrollUp": "向上捲動", "close": "關閉瀏覽器"}}}