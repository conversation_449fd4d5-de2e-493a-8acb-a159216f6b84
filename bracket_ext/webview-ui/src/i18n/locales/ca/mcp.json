{"title": "Servidors MCP", "done": "Fet", "description": "El <0>Model Context Protocol</0> permet la comunicació amb servidors MCP que s'executen localment i proporcionen eines i recursos addicionals per ampliar les capacitats de Roo. Pots utilitzar <1>servidors creats per la comunitat</1> o demanar a Roo que creï noves eines específiques per al teu flux de treball (per exemple, \"afegir una eina que obtingui la documentació més recent de npm\").", "enableToggle": {"title": "Habilitar servidors MCP", "description": "Quan està habilitat, Roo podrà interactuar amb servidors MCP per a funcionalitats avançades. Si no utilitzes MCP, pots desactivar això per reduir l'ús de tokens de Roo."}, "enableServerCreation": {"title": "Habilitar creació de servidors MCP", "description": "Quan està habilitat, Roo pot ajudar-te a crear nous servidors MCP mitjançant ordres com \"afegir una nova eina per a...\". Si no necessites crear servidors MCP, pots desactivar això per reduir l'ús de tokens de Roo."}, "editGlobalMCP": "Editar MCP Global", "editProjectMCP": "Editar MC<PERSON> del Projecte", "tool": {"alwaysAllow": "Permetre sempre", "parameters": "Paràmetres", "noDescription": "Sense descripció"}, "tabs": {"tools": "<PERSON><PERSON>", "resources": "Recursos"}, "emptyState": {"noTools": "No s'han trobat eines", "noResources": "No s'han trobat recursos"}, "networkTimeout": {"label": "Temps d'espera de xarxa", "description": "Temps màxim d'espera per a respostes del servidor", "options": {"15seconds": "15 segons", "30seconds": "30 segons", "1minute": "1 minut", "5minutes": "5 minuts", "10minutes": "10 minuts", "15minutes": "15 minuts", "30minutes": "30 minuts", "60minutes": "60 minuts"}}, "deleteDialog": {"title": "Eliminar servidor <PERSON>", "description": "Estàs segur que vols eliminar el servidor MCP \"{{serverName}}\"? Aquesta acció no es pot desfer.", "cancel": "Cancel·lar", "delete": "Eliminar"}, "serverStatus": {"retrying": "Reintentant...", "retryConnection": "Reintentar connexió"}}