{"greeting": "<PERSON><PERSON><PERSON><PERSON>, ben <PERSON>!", "introduction": "<PERSON><PERSON> ta<PERSON>ı kodlama yeteneklerindeki son gel<PERSON><PERSON><PERSON><PERSON> ve dosya oluşturma ve düzenleme, karmaşık projeleri keşfetme, tarayıcı kullanma ve terminal komutları çalıştırma (tabii ki senin izninle) gibi işlemleri yapmamı sağlayan araçlara erişim sayesinde her türlü görevi gerçekleştirebilirim. Hatta MCP'yi kullanarak yeni araçlar oluşturabilir ve kendi yeteneklerimi genişletebilirim.", "notice": "Başlamak için bu eklentinin bir API sağlayıcısına ihtiyacı var.", "start": "<PERSON>i ba<PERSON>alım!", "chooseProvider": "Başlamak için bir API sağlayıcısı seç:", "routers": {"requesty": {"description": "Optimize edilmiş LLM yönlendiricin", "incentive": "$1 ücretsiz kredi"}, "openrouter": {"description": "LLM'ler i<PERSON> bi<PERSON> bir arayüz"}}, "startRouter": "Yönlendirici Üzerinden Hızlı Kurulum", "startCustom": "Kendi API Anahtarını Kullan", "telemetry": {"title": "Roo Code'u Geliştirmeye Yardım Et", "anonymousTelemetry": "Hataları düzeltmemize ve eklentiyi geliştirmemize yardımcı olmak için anonim hata ve kullanım verileri gönder. <PERSON>ç<PERSON>aman kod, metin veya kişisel bilgi gönderilmez.", "changeSettings": "<PERSON><PERSON><PERSON> her zaman <settingsLink>ayar<PERSON></settingsLink>ın altından değiştirebilirsin", "settings": "<PERSON><PERSON><PERSON>", "allow": "<PERSON><PERSON> V<PERSON>", "deny": "<PERSON><PERSON>"}, "or": "veya"}