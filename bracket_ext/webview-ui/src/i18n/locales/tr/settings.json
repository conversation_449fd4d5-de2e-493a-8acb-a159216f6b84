{"common": {"save": "<PERSON><PERSON>", "done": "Tamamlandı", "cancel": "İptal", "reset": "Sıfırla", "select": "Seç"}, "header": {"title": "<PERSON><PERSON><PERSON>", "saveButtonTooltip": "Değişiklikleri kaydet", "nothingChangedTooltip": "Hiçbir şey değişmedi", "doneButtonTooltip": "Kaydedilmemiş değişiklikleri at ve ayarlar panelini kapat"}, "unsavedChangesDialog": {"title": "Kay<PERSON>il<PERSON><PERSON><PERSON>şiklikler", "description": "Değişiklikleri atmak ve devam etmek istiyor musunuz?", "cancelButton": "İptal", "discardButton": "Değişiklikleri At"}, "sections": {"providers": "Sağlayıcılar", "autoApprove": "Otomatik Onay", "browser": "Tarayıcı / Bilgisayar Kullanımı", "checkpoints": "<PERSON><PERSON><PERSON>", "notifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "contextManagement": "Bağlam Yönetimi", "terminal": "Terminal", "advanced": "Gelişmiş", "experimental": "<PERSON><PERSON>sel <PERSON>", "language": "Dil", "about": "Roo Code Hakkında"}, "autoApprove": {"description": "Roo'nun onay gerektirmeden otomatik olarak işlemler gerçekleştirmesine izin verin. Bu ayarları yalnızca yapay zekaya tamamen güveniyorsanız ve ilgili güvenlik risklerini anlıyorsanız etkinleştirin.", "readOnly": {"label": "Salt okunur işlemleri her zaman onayla", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> otomatik olarak dizin içeriğini görüntüleyecek ve Onayla düğmesine tıklamanıza gerek kalmadan dosyaları okuyacaktır.", "outsideWorkspace": {"label": "Çalışma alanı dışındaki dosyaları dahil et", "description": "Roo'nun onay gerektirmeden mevcut çalışma alanı dışındaki dosyaları okumasına izin ver."}}, "write": {"label": "<PERSON><PERSON><PERSON>ini her zaman onayla", "description": "Onay gerektirmeden otomatik olarak dosya oluştur ve düzenle", "delayLabel": "Tanılamanın potansiyel sorunları tespit etmesine izin vermek için yazmalardan sonra gecikme", "outsideWorkspace": {"label": "Çalışma alanı dışındaki dosyaları dahil et", "description": "Roo'nun onay gerektirmeden mevcut çalışma alanı dışında dosya oluşturmasına ve düzenlemesine izin ver."}}, "browser": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON><PERSON><PERSON> her zaman onayla", "description": "Onay gerektirmeden otomatik olarak tarayıcı eylemleri gerçekleştir", "note": "Not: Yalnızca model bil<PERSON><PERSON>r kullanımını desteklediğinde geçerlidir"}, "retry": {"label": "Başarısız API isteklerini her zaman yeniden dene", "description": "Sunucu bir hata yanıtı döndürdüğünde başarısız API isteklerini otomatik olarak yeniden dene", "delayLabel": "İsteği yeniden denemeden önce gecikme"}, "mcp": {"label": "MCP araçlarını her zaman onayla", "description": "MCP Sunucuları görünümünde bireysel MCP araçlarının otomatik onayını etkinleştir (hem bu ayar hem de aracın \"Her zaman izin ver\" onay kutusu gerekir)"}, "modeSwitch": {"label": "<PERSON><PERSON> her zaman onayla", "description": "Onay gerektirmeden otomatik olarak farklı modlar arasında geçiş yap"}, "subtasks": {"label": "Alt görevlerin oluşturulmasını ve tamamlanmasını her zaman onayla", "description": "Onay gerektirmeden alt görevlerin oluşturulmasına ve tamamlanmasına izin ver"}, "execute": {"label": "İzin verilen yürü<PERSON><PERSON> işlemlerini her zaman onayla", "description": "Onay gerektirmeden otomatik olarak izin verilen terminal komutlarını yürüt", "allowedCommands": "İzin Verilen Otomatik Yürütme Komutları", "allowedCommandsDescription": "\"Yürüt<PERSON> işlemlerini her zaman onayla\" etkinleştirildiğinde otomatik olarak yürütülebilen komut önekleri. Tüm komutlara izin vermek için * ekleyin (dikkatli kullanın).", "commandPlaceholder": "<PERSON><PERSON><PERSON> giri<PERSON> (örn. 'git ')", "addButton": "<PERSON><PERSON>"}}, "providers": {"configProfile": "Yapılandırma Profili", "providerDocumentation": "{{provider}} Dokümantasyonu", "description": "Sağlayıcılar ve ayarlar arasında hızlıca geçiş yapmak için farklı API yapılandırmalarını kaydedin.", "apiProvider": "API Sağlayıcı", "model": "Model", "nameEmpty": "İsim boş o<PERSON>az", "nameExists": "Bu isme sahip bir profil zaten mevcut", "deleteProfile": "<PERSON><PERSON> sil", "invalidArnFormat": "Geçersiz ARN formatı. Yukarıdaki örnekleri kontrol edin.", "enterNewName": "Yeni ad girin", "addProfile": "<PERSON>il <PERSON>", "renameProfile": "<PERSON><PERSON>", "newProfile": "<PERSON><PERSON> profili", "enterProfileName": "Profil adını girin", "createProfile": "<PERSON><PERSON>", "cannotDeleteOnlyProfile": "Yalnızca tek profili silemezsiniz", "searchPlaceholder": "Profilleri ara", "noMatchFound": "Eşleşen profil bulunamadı", "vscodeLmDescription": "VS Code Dil Modeli API'si, diğer VS Code uzantıları tarafından sağlanan modelleri çalıştırmanıza olanak tanır (GitHub Copilot dahil ancak bunlarla sınırlı değildir). Başlamanın en kolay yolu, VS Code Marketplace'ten Copilot ve Copilot Chat uzantılarını yüklemektir.", "awsCustomArnUse": "Kullanmak istediğiniz model i<PERSON>in geçerli bir AWS Bedrock ARN'si girin. Format örnekleri:", "awsCustomArnDesc": "ARN içindeki bölgenin yukarıda seçilen AWS Bölgesiyle eşleştiğinden emin olun.", "openRouterApiKey": "OpenRouter API Anahtarı", "getOpenRouterApiKey": "OpenRouter API Anahtarı Al", "apiKeyStorageNotice": "API anahtarları VSCode'un Gizli Depolamasında güvenli bir şekilde saklanır", "glamaApiKey": "Glama API Anahtarı", "getGlamaApiKey": "Glama API Anahtarı Al", "useCustomBaseUrl": "Özel temel URL kullan", "useHostHeader": "Özel Host başlığ<PERSON> kullan", "useLegacyFormat": "Eski OpenAI API formatını kullan", "requestyApiKey": "Requesty API Anahtarı", "getRequestyApiKey": "Requesty API Anahtarı Al", "openRouterTransformsText": "İstem ve mesaj zincirlerini bağlam boyutuna sıkıştır (<a>OpenRouter Dönüşümleri</a>)", "anthropicApiKey": "Anthropic API Anahtarı", "getAnthropicApiKey": "Anthropic API Anahtarı Al", "deepSeekApiKey": "DeepSeek API Anahtarı", "getDeepSeekApiKey": "DeepSeek API Anahtarı Al", "geminiApiKey": "Gemini API Anahtarı", "getGeminiApiKey": "Gemini API Anahtarı Al", "openAiApiKey": "OpenAI API Anahtarı", "openAiBaseUrl": "Temel URL", "getOpenAiApiKey": "OpenAI API Anahtarı Al", "mistralApiKey": "Mistral API Anahtarı", "getMistralApiKey": "Mistral / Codestral API Anahtarı Al", "codestralBaseUrl": "Codestral Temel URL (İsteğe bağlı)", "codestralBaseUrlDesc": "Codestral modeli için alternatif URL ayarlayın.", "awsCredentials": "AWS Kimlik Bilgileri", "awsProfile": "AWS Profili", "awsProfileName": "AWS Profil Adı", "awsAccessKey": "AWS Erişim <PERSON>", "awsSecretKey": "AWS Gizli Anahtarı", "awsSessionToken": "AWS Oturum Belirteci", "awsRegion": "AWS Bölgesi", "awsCrossRegion": "B<PERSON><PERSON>ler arası çıkarı<PERSON> kullan", "enablePromptCaching": "İstem önbelleğini etkinleştir", "enablePromptCachingTitle": "Desteklenen modeller için performansı artırmak ve maliyetleri azaltmak için istem önbelleğini etkinleştir.", "cacheUsageNote": "Not: Önbellek kullanımını görmüyorsanız, fark<PERSON>ı bir model seç<PERSON> ardı<PERSON>n istediğiniz modeli tekrar seçmeyi deneyin.", "vscodeLmModel": "<PERSON><PERSON> Modeli", "vscodeLmWarning": "Not: Bu çok deneysel bir entegrasyondur ve sağlayıcı desteği değişebilir. Bir modelin desteklenmediğine dair bir hata alı<PERSON>nı<PERSON>, bu sağlayıcı tarafındaki bir sorundur.", "googleCloudSetup": {"title": "Google Cloud Vertex AI'yi kullanmak için şunları yapmanız gerekir:", "step1": "1. Google Cloud hesabı oluşturun, Vertex AI API'sini etkinleştirin ve istediğiniz Claude modellerini etkinleştirin.", "step2": "2. Google Cloud CLI'yi yü<PERSON> ve uygulama varsayılan kimlik bilgilerini yapılandırın.", "step3": "3. <PERSON><PERSON><PERSON> kimlik bilgileriyle bir hizmet hesabı oluşturun."}, "googleCloudCredentials": "Google Cloud Kimlik <PERSON>gileri", "googleCloudKeyFile": "Google Cloud Anahtar <PERSON>", "googleCloudProjectId": "Google Cloud Proje <PERSON>", "googleCloudRegion": "Google Cloud Bölgesi", "lmStudio": {"baseUrl": "Temel URL (İsteğe bağlı)", "modelId": "Model <PERSON><PERSON>", "speculativeDecoding": "Spekülatif Kod Çözmeyi Etkinleştir", "draftModelId": "Taslak Model <PERSON>", "draftModelDesc": "Spekülatif kod çözmenin doğru çalışması için taslak model aynı model ailesinden olmalıdır.", "selectDraftModel": "Taslak Model Seç", "noModelsFound": "Taslak model bulunamad<PERSON>. Lütfen LM Studio'nun <PERSON><PERSON><PERSON> et<PERSON>ken çalıştığından emin olun.", "description": "LM Studio, modelleri bilgisayarınızda yerel olarak çalıştırmanıza olanak tanır. Başlamak için <a>hızlı başlangıç kılavuzlarına</a> bakın. Bu uzantıyla kullanmak için LM Studio'nun <b>yerel sunucu</b> özelliğini de başlatmanız gerekecektir. <span>Not:</span> Roo Code karmaşık istemler kullanır ve Claude modelleriyle en iyi şekilde çalışır. Daha az yetenekli modeller beklendiği gibi çalışmayabilir."}, "ollama": {"baseUrl": "Temel URL (İsteğe bağlı)", "modelId": "Model <PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>, modelleri bilgisayarınızda yerel olarak çalıştırmanıza olanak tanır. Başlamak için hızlı başlangıç kılavuzlarına bakın.", "warning": "Not: Roo Code karmaşık istemler kullanır ve Claude modelleriyle en iyi şekilde çalışır. <PERSON>ha az yetenekli modeller beklendiği gibi çalışmayabilir."}, "unboundApiKey": "Unbound API Anahtarı", "getUnboundApiKey": "Unbound API Anahtarı Al", "humanRelay": {"description": "API anahtarı gerekmez, ancak kullanıcının bilgileri web sohbet yapay zekasına kopyalayıp yapıştırması gerekir.", "instructions": "Kullanım sırasında bir iletişim kutusu açılacak ve mevcut mesaj otomatik olarak panoya kopyalanacaktır. Bunları web yapay zekalarına (ChatGPT veya Claude gibi) yapıştırmanız, ardından yapay zekanın yanıtını iletişim kutusuna kopyalayıp onay düğmesine tıklamanız gerekir."}, "openRouter": {"providerRouting": {"title": "OpenRouter Sağlayıcı Yönlendirmesi", "description": "<PERSON><PERSON><PERSON><PERSON>, model<PERSON>z için mevcut en iyi sağlayıcılara istekleri yönlendirir. Varsayılan o<PERSON>, istekler çalışma süresini en üst düzeye çıkarmak için en iyi sağlayıcılar arasında dengelenir. <PERSON><PERSON><PERSON>, bu model i<PERSON><PERSON> kullanılacak belirli bir sağlayıcı seçebilirsiniz.", "learnMore": "Sağlayıcı yönlendirmesi hakkında daha fazla bilgi edinin"}}, "customModel": {"capabilities": "Özel OpenAI uyumlu modelinizin yeteneklerini ve fiyatlandırmasını yapılandırın. Model yeteneklerini belirtirken dikkatl<PERSON> o<PERSON>, <PERSON><PERSON><PERSON><PERSON> bunlar Roo Code'un performansını etkileyebilir.", "maxTokens": {"label": "<PERSON><PERSON><PERSON><PERSON> Çıktı Token'ları", "description": "Modelin bir yanıtta üretebileceği maksimum token sayısı. (Sunucunun maksimum token'ları ayarlamasına izin vermek için -1 belirtin.)"}, "contextWindow": {"label": "Bağlam Penceresi Boyutu", "description": "Modelin işleyebileceği toplam token sayısı (giriş + çıkış)."}, "imageSupport": {"label": "Görü<PERSON><PERSON>", "description": "Bu model g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> işleyip anlayabilir mi?"}, "computerUse": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Bu model bir ta<PERSON><PERSON><PERSON><PERSON><PERSON> etkileşim kurabilir mi? (<PERSON><PERSON><PERSON> <PERSON> 3.7 Sonnet)"}, "promptCache": {"label": "İstem Önbelleği", "description": "Bu model is<PERSON><PERSON>i önbelleğe alabilir mi?"}, "pricing": {"input": {"label": "<PERSON><PERSON><PERSON>ı", "description": "G<PERSON>ş/istem ba<PERSON><PERSON>na milyon token maliyeti. Bu, modele bağlam ve talimatlar gönderme maliyetini etkiler."}, "output": {"label": "Çıkış Fiyatı", "description": "Model yanıtı ba<PERSON><PERSON>na milyon token maliyeti. Bu, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> içerik ve tamamlamaların maliyetini etkiler."}, "cacheReads": {"label": "Önbellek Okuma Fiyatı", "description": "Önbellekten okuma başına milyon token maliyeti. Bu, önbelleğe alınmış bir yanıt alındığında uygulanan fiyattır."}, "cacheWrites": {"label": "Önbellek Yazma Fiyatı", "description": "Önbelleğe yazma başına milyon token maliyeti. Bu, bir istem ilk kez önbelleğe alındığında uygulanan fiyattır."}}, "resetDefaults": "Varsayılanlara Sıfırla"}, "rateLimitSeconds": {"label": "<PERSON><PERSON>z sınırı", "description": "API istekleri arasındaki minimum süre."}}, "browser": {"enable": {"label": "Tarayıcı aracını etkinleştir", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> bil<PERSON> kullanımını destekleyen modeller kullanırken web siteleriyle etkileşim kurmak için bir tarayıcı kullanabilir."}, "viewport": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> al<PERSON>u", "description": "Tarayıcı etkileşimleri için görünüm alanı boyutunu seçin. Bu, web sitelerinin nasıl görüntülendiğini ve etkileşime girdiğini etkiler.", "options": {"largeDesktop": "Büyük Masaüstü (1280x800)", "smallDesktop": "Küçük Masaüstü (900x600)", "tablet": "Tablet (768x1024)", "mobile": "Mobil (360x640)"}}, "screenshotQuality": {"label": "Ekran görü<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "description": "Tarayıcı ekran görüntülerinin WebP kalitesini ayarlayın. Daha yüksek değerler daha net ekran görüntüleri sağlar ancak token kullanımını artırır."}, "remote": {"label": "Uzak tarayıcı bağlantısı kullan", "description": "Uzaktan hata ayıklama etkinleştirilmiş olarak çalışan bir Chrome tarayıcısına bağlanın (--remote-debugging-port=9222).", "urlPlaceholder": "Özel URL (örn. http://localhost:9222)", "testButton": "Bağlantıyı Test Et", "testingButton": "Test Ediliyor...", "instructions": "DevTools protokolü ana bilgisayar adresini girin veya yerel Chrome örneklerini otomatik olarak keşfetmek için boş bırakın. Bağlantıyı Test Et düğmesi, sağlanmışsa özel URL'yi deneyecek veya alan boşsa otomatik olarak keşfedecektir."}}, "checkpoints": {"enable": {"label": "Otomatik kontrol noktalarını etkinleştir", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> görev yür<PERSON>tme sırasında otomatik olarak kontrol noktaları oluşturarak değişiklikleri gözden geçirmeyi veya önceki durumlara dönmeyi kolaylaştırır."}}, "notifications": {"sound": {"label": "Ses efektlerini etkinleştir", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> bild<PERSON> ve olaylar için ses efektleri çalacaktır.", "volumeLabel": "<PERSON><PERSON>"}, "tts": {"label": "Metinden sese özelliğini etkinleştir", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> yanıtlarını metinden sese teknolojisi kullanarak sesli okuy<PERSON>ır.", "speedLabel": "Hız"}}, "contextManagement": {"description": "<PERSON><PERSON>y zekanın bağlam penceresine hangi bilgilerin dahil edileceğini kontrol edin, token kullanımını ve yanıt kalitesini etkiler", "openTabs": {"label": "Açık sekmeler bağlam sınırı", "description": "Bağlama dahil edilecek maksimum VSCode açık sekme sayısı. Daha yüksek değerler daha fazla bağlam sağlar ancak token kullanımını artırır."}, "workspaceFiles": {"label": "Çalışma alanı dosyaları bağlam sınırı", "description": "Mevcut çalışma dizini ayrıntılarına dahil edilecek maksimum dosya sayısı. Daha yüks<PERSON> değerler daha fazla bağlam sağlar ancak token kullanımını artırır."}, "rooignore": {"label": "Listelerde ve aramalarda .rooignore dosyalarını göster", "description": "<PERSON>t<PERSON>leş<PERSON>ril<PERSON>ğ<PERSON><PERSON>, .rooign<PERSON>'da<PERSON> desenlerle eşleşen dosyalar kilit sembolü ile listelerde gösterilecektir. <PERSON>re dışı bırak<PERSON>ld<PERSON><PERSON>, bu dosyalar dosya listelerinden ve aramalardan tamamen gizlenecektir."}, "maxReadFile": {"label": "Dosya okuma otomatik kısaltma eşiği", "description": "Model başlangıç/bitiş değerlerini belirtmediğinde Roo bu sayıda satırı okur. Bu sayı dosyanın toplam satır sayısından azsa, Roo kod tanımlamalarının satır numarası dizinini oluşturur. Özel durumlar: -1, Roo'ya tüm dosyayı okumasını (dizinleme olmadan), 0 ise hiç satır okumamasını ve minimum bağlam için yalnızca satır dizinleri sağlamasını belirtir. Düşük değerler başlangıç bağlam kullanımını en aza indirir ve sonraki hassas satır aralığı okumalarına olanak tanır. Açık başlangıç/bitiş istekleri bu ayarla sınırlı değildir.", "lines": "<PERSON>ır", "always_full_read": "Her zaman tüm dosyayı oku"}}, "terminal": {"outputLineLimit": {"label": "Terminal çıktısı sınırı", "description": "Komutları yürütürken terminal çıktısına dahil edilecek maksimum satır sayısı. Aşıldığında, token tasarrufu sağlayarak satırlar ortadan kaldırılacaktır."}, "shellIntegrationTimeout": {"label": "Terminal kabuk entegrasyonu zaman aşımı", "description": "Komutları yürütmeden önce kabuk entegrasyonunun başlatılması için beklenecek maksimum süre. Kabuk başlatma süresi uzun olan kullanıcılar için, terminalde \"Shell Integration Unavailable\" hatalarını görürseniz bu değerin artırılması gerekebilir."}}, "advanced": {"diff": {"label": "Diff'ler <PERSON>ığıyla düzenlemeyi etkinleştir", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> dos<PERSON> daha hızlı düzenleyebilecek ve kesik tam dosya yazımlarını otomatik olarak reddedecektir. En son Claude 3.7 Sonnet modeliyle en iyi şekilde çalışır.", "strategy": {"label": "<PERSON><PERSON> strate<PERSON>", "options": {"standard": "Standart (Tek blok)", "multiBlock": "Deneysel: Ç<PERSON><PERSON> blok diff", "unified": "Deneysel: Birleştirilmiş diff"}, "descriptions": {"standard": "Standart diff strate<PERSON><PERSON>, bir seferde tek bir kod bloğuna değişiklikler uygular.", "unified": "Birleştirilmiş diff strate<PERSON><PERSON>, diff'leri u<PERSON> için birden çok yaklaşım benimser ve en iyi yaklaşımı seçer.", "multiBlock": "Çoklu blok diff stratejisi, tek bir istekte bir dosyadaki birden çok kod bloğunu güncellemenize olanak tanır."}}, "matchPrecision": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Bu kaydırı<PERSON><PERSON>, diff'ler uygulanırken kod bölümlerinin ne kadar hassas bir şekilde eşleşmesi gerektiğini kontrol eder. Daha düşük değerler daha esnek eşleşmeye izin verir ancak yanlış değiştirme riskini artırır. %100'ün altındaki değerleri son derece dikkatli kullanın."}}}, "experimental": {"warning": "⚠️", "DIFF_STRATEGY_UNIFIED": {"name": "Deneysel birleştirilmiş diff stratejisini kullan", "description": "Deneysel birleştirilmiş diff stratejisini etkinleştir. <PERSON>u strateji, model hat<PERSON><PERSON><PERSON><PERSON><PERSON> kaynaklanan yeniden deneme sayısını azaltabilir, ancak beklenmeyen davranışlara veya hatalı düzenlemelere neden olabilir. Yalnızca riskleri anlıyorsanız ve tüm değişiklikleri dikkatlice incelemeye istekliyseniz etkinleştirin."}, "SEARCH_AND_REPLACE": {"name": "Deneysel arama ve değiştirme aracını kullan", "description": "Deneysel arama ve değiştirme aracını etkinleştir, <PERSON><PERSON>'nun tek bir istekte bir arama teriminin birden fazla örneğini değiştirmesine olanak tanır."}, "INSERT_BLOCK": {"name": "Deneysel içerik ekleme aracını kullan", "description": "Deneysel içerik ekleme aracını etkin<PERSON>ştir, <PERSON><PERSON><PERSON>nun bir diff oluşturma gereği duymadan belirli satır numaralarına içerik eklemesine olanak tanır."}, "POWER_STEERING": {"name": "<PERSON><PERSON><PERSON> \"g<PERSON><PERSON>\" modunu kullan", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Roo modele geçerli mod tanımının ayrıntılarını daha sık hatırlatacaktır. Bu, rol tanımlarına ve özel talimatlara daha güçlü uyum sağlayacak, ancak mesaj başına daha fazla token kullanacaktır."}, "MULTI_SEARCH_AND_REPLACE": {"name": "Deneysel çoklu blok diff aracını kullan", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> çoklu blok diff aracını kullanacaktır. <PERSON><PERSON>, tek bir istekte dosyadaki birden fazla kod bloğunu güncellemeye çalışacaktır."}}, "temperature": {"useCustom": "<PERSON>zel sıcaklık kullan", "description": "Model yan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rastgeleliği kontrol eder.", "rangeDescription": "Daha yüksek değerler çıktıyı daha rastgele ya<PERSON>, daha dü<PERSON><PERSON><PERSON> değerler daha deterministik hale getirir."}, "modelInfo": {"supportsImages": "Görüntüleri destekler", "noImages": "Görüntüleri desteklemez", "supportsComputerUse": "Bilgisayar kullanımını destekler", "noComputerUse": "Bilgisayar kullanımını desteklemez", "supportsPromptCache": "İstem önbelleğini destekler", "noPromptCache": "İstem önbelleğini desteklemez", "maxOutput": "<PERSON><PERSON><PERSON><PERSON> ç<PERSON>ı", "inputPrice": "<PERSON><PERSON><PERSON> fi<PERSON>tı", "outputPrice": "Çıkış fiyatı", "cacheReadsPrice": "Önbellek okuma fiyatı", "cacheWritesPrice": "Önbellek yazma fiyatı", "enableStreaming": "Akışı etkinleştir", "enableR1Format": "R1 model parametrel<PERSON><PERSON>r", "enableR1FormatTips": "QWQ gibi R1 modelleri kullanıldığında etkinleştirilmelidir, 400 hatası alınmaması için", "useAzure": "Azure kullan", "azureApiVersion": "Azure API sürümünü ayarla", "gemini": {"freeRequests": "* Dakikada {{count}} isteğe kadar ücretsiz. Bundan sonra, ücretlendirme istem boyutuna bağlıdır.", "pricingDetails": "Daha fazla bilgi için fiyatlandırma ayrıntılarına bakın.", "billingEstimate": "* Ücretlendirme bir tahmindir - kesin maliyet istem boyutuna bağlıdır."}}, "modelPicker": {"automaticFetch": "Uzantı <serviceLink>{{serviceName}}</serviceLink> üzerinde bulunan mevcut modellerin en güncel listesini otomatik olarak alır. Hangi modeli seçeceğinizden emin değilseniz, Roo Code <defaultModelLink>{{defaultModelId}}</defaultModelLink> ile en iyi şekilde çalışır. Şu anda mevcut olan ücretsiz seçenekleri bulmak için \"free\" araması da yapabilirsiniz.", "label": "Model", "searchPlaceholder": "Ara", "noMatchFound": "Eşleşme bulunamadı", "useCustomModel": "<PERSON><PERSON> kull<PERSON>: {{modelId}}"}, "footer": {"feedback": "<PERSON><PERSON><PERSON> bir sorunuz veya geri bildiri<PERSON>z varsa, <githubLink>github.com/RooVetGit/Roo-Code</githubLink> adresinde bir konu açmaktan veya <redditLink>reddit.com/r/RooCode</redditLink> ya da <discordLink>discord.gg/roocode</discordLink>'a katılmaktan çekinmeyin", "version": "Roo Code v{{version}}", "telemetry": {"label": "<PERSON><PERSON>m hata ve kullanım raporlamaya izin ver", "description": "Anonim kullanım verileri ve hata raporları göndererek Roo Code'u geliştirmeye yardımcı olun. <PERSON>ç<PERSON> kod, istem veya kişisel bilgi asla gönderilmez. Daha fazla ayrıntı için gizlilik politikamıza bakın."}, "settings": {"import": "İçe Aktar", "export": "Dışa Aktar", "reset": "Sıfırla"}}, "thinkingBudget": {"maxTokens": "Ma<PERSON><PERSON><PERSON> token", "maxThinkingTokens": "<PERSON><PERSON><PERSON><PERSON>"}, "validation": {"apiKey": "Geçerli bir API anahtarı sağlamalısınız.", "awsRegion": "AWS Bedrock kullanmak için bir bölge seçmelisiniz.", "googleCloud": "Geçerli bir Google Cloud proje kimliği ve bölge sağlamalısınız.", "modelId": "Geçerli bir model k<PERSON><PERSON><PERSON><PERSON>ını<PERSON>.", "modelSelector": "Geçerli bir model se<PERSON><PERSON> sağlamalısınız.", "openAi": "Geçerli bir temel URL, API anahtarı ve model kimliği sağlamalısınız.", "arn": {"invalidFormat": "Geçersiz ARN formatı. Lütfen format gereksinimlerini kontrol edin.", "regionMismatch": "Uyarı: ARN'nizdeki bölge ({{arnRegion}}) seçtiğiniz bölge ({{region}}) ile eşleşmiyor. Bu eri<PERSON>im sorunlarına neden olabilir. Sağlayıcı, ARN'deki bölgeyi kullanacak."}, "modelAvailability": "Sağladığın<PERSON>z model kimliği ({{modelId}}) kullanılamıyor. Lütfen başka bir model seçin."}, "placeholders": {"apiKey": "API anahtarını girin...", "profileName": "Profil adını girin", "accessKey": "<PERSON><PERSON><PERSON><PERSON> girin...", "secretKey": "G<PERSON><PERSON> anahtarı girin...", "sessionToken": "<PERSON><PERSON><PERSON> beli<PERSON>cini girin...", "credentialsJson": "Kimlik bilgileri JSON'ını girin...", "keyFilePath": "<PERSON><PERSON><PERSON> yolunu girin...", "projectId": "Proje ID'sini girin...", "customArn": "ARN girin (örn. arn:aws:bedrock:us-east-1:123456789012:foundation-model/my-model)", "baseUrl": "Temel URL'yi girin...", "modelId": {"lmStudio": "örn. meta-llama-3.1-8b-instruct", "lmStudioDraft": "örn. lmstudio-community/llama-3.2-1b-instruct", "ollama": "örn. llama3.1"}, "numbers": {"maxTokens": "örn. 4096", "contextWindow": "örn. 128000", "inputPrice": "örn. 0.0001", "outputPrice": "örn. 0.0002", "cacheWritePrice": "örn. 0.00005"}}, "defaults": {"ollamaUrl": "Varsayılan: http://localhost:11434", "lmStudioUrl": "Varsayılan: http://localhost:1234", "geminiUrl": "Varsayılan: https://generativelanguage.googleapis.com"}, "labels": {"customArn": "Özel <PERSON>", "useCustomArn": "Özel ARN kullan..."}}