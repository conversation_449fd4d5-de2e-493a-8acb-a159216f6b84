{"greeting": "<PERSON>ut, je suis Roo !", "introduction": "Je peux effectuer toutes sortes de tâches grâce aux dernières avancées en matière de capacités de codage agentique et à l'accès à des outils qui me permettent de créer et de modifier des fichiers, d'explorer des projets complexes, d'utiliser le navigateur et d'exécuter des commandes terminal (avec ta permission, bien sûr). Je peux même utiliser MCP pour créer de nouveaux outils et étendre mes propres capacités.", "notice": "Pour commencer, cette extension a besoin d'un fournisseur d'API.", "start": "C'est parti !", "chooseProvider": "Choisis un fournisseur d'API pour commencer :", "routers": {"requesty": {"description": "Ton routeur LLM optimisé", "incentive": "1$ de crédit gratuit"}, "openrouter": {"description": "Une interface unifiée pour les LLMs"}}, "startRouter": "Configuration rapide via un routeur", "startCustom": "Utiliser ta propre clé API", "telemetry": {"title": "Aide à améliorer Roo Code", "anonymousTelemetry": "Envoie des données d'utilisation et d'erreurs anonymes pour nous aider à corriger les bugs et améliorer l'extension. Aucun code, texte ou information personnelle n'est jamais envoyé.", "changeSettings": "Tu peux toujours modifier cela en bas des <settingsLink>paramètres</settingsLink>", "settings": "paramètres", "allow": "Autoriser", "deny": "Refuser"}, "or": "ou"}