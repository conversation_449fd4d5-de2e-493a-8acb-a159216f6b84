{"title": "MCP 서버", "done": "완료", "description": "<0>Model Context Protocol</0>은 로컬에서 실행되는 MCP 서버와 통신하여 Roo의 기능을 확장하는 추가 도구 및 리소스를 제공합니다. <1>커뮤니티에서 만든 서버</1>를 사용하거나 Roo에게 작업 흐름에 맞는 새로운 도구를 만들도록 요청할 수 있습니다 (예: \"최신 npm 문서를 가져오는 도구 추가\").", "enableToggle": {"title": "MCP 서버 활성화", "description": "활성화하면 Roo가 고급 기능을 위해 MCP 서버와 상호 작용할 수 있습니다. MCP를 사용하지 않는 경우 비활성화하여 Roo의 token 사용량을 줄일 수 있습니다."}, "enableServerCreation": {"title": "MCP 서버 생성 활성화", "description": "활성화하면 Roo가 \"새 도구 추가...\"와 같은 명령을 통해 새 MCP 서버를 만드는 데 도움을 줄 수 있습니다. MCP 서버를 만들 필요가 없다면 이 기능을 비활성화하여 Roo의 token 사용량을 줄일 수 있습니다."}, "editGlobalMCP": "전역 MCP 편집", "editProjectMCP": "프로젝트 MCP 편집", "tool": {"alwaysAllow": "항상 허용", "parameters": "매개변수", "noDescription": "설명 없음"}, "tabs": {"tools": "도구", "resources": "리소스"}, "emptyState": {"noTools": "도구를 찾을 수 없음", "noResources": "리소스를 찾을 수 없음"}, "networkTimeout": {"label": "네트워크 타임아웃", "description": "서버 응답을 기다리는 최대 시간", "options": {"15seconds": "15초", "30seconds": "30초", "1minute": "1분", "5minutes": "5분", "10minutes": "10분", "15minutes": "15분", "30minutes": "30분", "60minutes": "60분"}}, "deleteDialog": {"title": "MCP 서버 삭제", "description": "MCP 서버 \"{{serverName}}\"을(를) 삭제하시겠습니까? 이 작업은 취소할 수 없습니다.", "cancel": "취소", "delete": "삭제"}, "serverStatus": {"retrying": "재시도 중...", "retryConnection": "연결 재시도"}}