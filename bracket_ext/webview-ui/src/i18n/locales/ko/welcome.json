{"greeting": "안녕하세요, 저는 루입니다!", "introduction": "에이전트 코딩 능력의 최신 발전과 파일 생성 및 편집, 복잡한 프로젝트 탐색, 브라우저 사용, 터미널 명령 실행(물론 사용자의 허락 하에)을 가능하게 하는 도구에 대한 접근 덕분에 모든 종류의 작업을 수행할 수 있습니다. MCP를 사용하여 새로운 도구를 만들고 제 능력을 확장할 수도 있습니다.", "notice": "시작하려면 이 확장 프로그램에 API 공급자가 필요합니다.", "start": "시작해 봅시다!", "chooseProvider": "시작하려면 API 공급자를 선택하세요:", "routers": {"requesty": {"description": "최적화된 LLM 라우터", "incentive": "$1 무료 크레딧"}, "openrouter": {"description": "LLM을 위한 통합 인터페이스"}}, "startRouter": "라우터를 통한 빠른 설정", "startCustom": "직접 API 키 사용하기", "telemetry": {"title": "Roo Code 개선에 도움 주세요", "anonymousTelemetry": "버그 수정 및 확장 기능 개선을 위해 익명의 오류 및 사용 데이터를 보내주세요. 코드, 프롬프트 또는 개인 정보는 절대 전송되지 않습니다.", "changeSettings": "<settingsLink>설정</settingsLink> 하단에서 언제든지 변경할 수 있습니다", "settings": "설정", "allow": "허용", "deny": "거부"}, "or": "또는"}