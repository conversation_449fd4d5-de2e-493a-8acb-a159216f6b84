{"title": "MCP सर्वर", "done": "हो गया", "description": "<0>मॉडल कॉन्टेक्स्ट प्रोटोकॉल</0> स्थानीय रूप से चल रहे MCP सर्वरों के साथ संचार को सक्षम बनाता है जो Roo की क्षमताओं का विस्तार करने के लिए अतिरिक्त उपकरण और संसाधन प्रदान करते हैं। आप <1>समुदाय द्वारा बनाए गए सर्वरों</1> का उपयोग कर सकते हैं या Roo से अपने कार्यप्रवाह के लिए विशिष्ट नए उपकरण बनाने के लिए कह सकते हैं (जैसे, \"नवीनतम npm दस्तावेज़ प्राप्त करने वाला उपकरण जोड़ें\")।", "enableToggle": {"title": "MCP सर्वर सक्षम करें", "description": "जब सक्षम होता है, तो Roo उन्नत कार्यक्षमता के लिए MCP सर्वरों के साथ बातचीत कर सकेगा। यदि आप MCP का उपयोग नहीं कर रहे हैं, तो आप Roo के token उपयोग को कम करने के लिए इसे अक्षम कर सकते हैं।"}, "enableServerCreation": {"title": "MCP सर्वर निर्माण सक्षम करें", "description": "जब सक्षम होता है, तो Roo आपको \"में नया उपकरण जोड़ें...\" जैसे कमांड के माध्यम से नए MCP सर्वर बनाने में मदद कर सकता है। यदि आपको MCP सर्वर बनाने की आवश्यकता नहीं है, तो आप Roo के token उपयोग को कम करने के लिए इसे अक्षम कर सकते हैं।"}, "editGlobalMCP": "वैश्विक MCP संपादित करें", "editProjectMCP": "प्रोजेक्ट MCP संपादित करें", "tool": {"alwaysAllow": "हमेशा अनुमति दें", "parameters": "पैरामीटर", "noDescription": "कोई विवरण नहीं"}, "tabs": {"tools": "उपकरण", "resources": "संसाधन"}, "emptyState": {"noTools": "कोई उपकरण नहीं मिला", "noResources": "कोई संसाधन नहीं मिला"}, "networkTimeout": {"label": "नेटवर्क टाइमआउट", "description": "सर्वर प्रतिक्रियाओं के लिए प्रतीक्षा करने का अधिकतम समय", "options": {"15seconds": "15 सेकंड", "30seconds": "30 सेकंड", "1minute": "1 मिनट", "5minutes": "5 मिनट", "10minutes": "10 मिनट", "15minutes": "15 मिनट", "30minutes": "30 मिनट", "60minutes": "60 मिनट"}}, "deleteDialog": {"title": "MCP सर्वर हटाएं", "description": "क्या आप वाकई MCP सर्वर \"{{serverName}}\" को हटाना चाहते हैं? यह क्रिया पूर्ववत नहीं की जा सकती।", "cancel": "रद्<PERSON> करें", "delete": "हटाएं"}, "serverStatus": {"retrying": "पुनः प्रयास कर रहे हैं...", "retryConnection": "कनेक्शन पुनः प्रयास करें"}}