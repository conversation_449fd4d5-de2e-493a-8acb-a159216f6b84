{"common": {"save": "सहेजें", "done": "पूर्ण", "cancel": "रद्<PERSON> करें", "reset": "रीसेट करें", "select": "चुनें"}, "header": {"title": "सेटिंग्स", "saveButtonTooltip": "परिवर्तन सहेजें", "nothingChangedTooltip": "कुछ भी नहीं बदला", "doneButtonTooltip": "असहेजे परिवर्तनों को छोड़ें और सेटिंग्स पैनल बंद करें"}, "unsavedChangesDialog": {"title": "असहेजे परिवर्तन", "description": "क्या आप परिवर्तनों को छोड़कर जारी रखना चाहते हैं?", "cancelButton": "रद्<PERSON> करें", "discardButton": "परिवर्तन छोड़ें"}, "sections": {"providers": "प्रदाता", "autoApprove": "स्वतः अनुमोदन", "browser": "ब्राउज़र / कंप्यूटर उपयोग", "checkpoints": "चेकपॉइंट", "notifications": "सूचनाएँ", "contextManagement": "संदर्भ प्रबंधन", "terminal": "टर्मिनल", "advanced": "उन्नत", "experimental": "प्रायोगिक सुविधाएँ", "language": "भाषा", "about": "Roo Code के बारे में"}, "autoApprove": {"description": "Roo को अनुमोदन की आवश्यकता के बिना स्वचालित रूप से ऑपरेशन करने की अनुमति दें। इन सेटिंग्स को केवल तभी सक्षम करें जब आप AI पर पूरी तरह से भरोसा करते हों और संबंधित सुरक्षा जोखिमों को समझते हों।", "readOnly": {"label": "केवल पढ़ने वाले ऑपरेशन हमेशा अनुमोदित करें", "description": "जब सक्षम होता है, तो Roo आपके अनुमोदित बटन पर क्लिक किए बिना स्वचालित रूप से निर्देशिका सामग्री देखेगा और फाइलें पढ़ेगा।", "outsideWorkspace": {"label": "वर्कस्पेस के बाहर की फाइलें शामिल करें", "description": "Roo को अनुमोदन की आवश्यकता के बिना वर्तमान वर्कस्पेस के बाहर की फाइलें पढ़ने की अनुमति दें।"}}, "write": {"label": "लिखने वाले ऑपरेशन हमेशा अनुमोदित करें", "description": "अनुमोदन की आवश्यकता के बिना स्वचालित रूप से फाइलें बनाएँ और संपादित करें", "delayLabel": "लिखने के बाद विलंब ताकि डायग्नोस्टिक संभावित समस्याओं का पता लगा सकें", "outsideWorkspace": {"label": "वर्कस्पेस के बाहर की फाइलें शामिल करें", "description": "Roo को अनुमोदन की आवश्यकता के बिना वर्तमान वर्कस्पेस के बाहर फाइलें बनाने और संपादित करने की अनुमति दें।"}}, "browser": {"label": "ब्राउज़र क्रियाएँ हमेशा अनुमोदित करें", "description": "अनुमोदन की आवश्यकता के बिना स्वचालित रूप से ब्राउज़र क्रियाएँ करें", "note": "नोट: केवल तभी लागू होता है जब मॉडल कंप्यूटर उपयोग का समर्थन करता है"}, "retry": {"label": "विफल API अनुरोधों को हमेशा पुनः प्रयास करें", "description": "जब सर्वर त्रुटि प्रतिक्रिया देता है तो स्वचालित रूप से विफल API अनुरोधों को पुनः प्रयास करें", "delayLabel": "अनुरोध को पुनः प्रयास करने से पहले विलंब"}, "mcp": {"label": "MCP टूल्स हमेशा अनुमोदित करें", "description": "MCP सर्वर व्यू में व्यक्तिगत MCP टूल्स के स्वतः अनुमोदन को सक्षम करें (इस सेटिंग और टूल के \"हमेशा अनुमति दें\" चेकबॉक्स दोनों की आवश्यकता है)"}, "modeSwitch": {"label": "मोड स्विचिंग हमेशा अनुमोदित करें", "description": "अनुमोदन की आवश्यकता के बिना स्वचालित रूप से विभिन्न मोड के बीच स्विच करें"}, "subtasks": {"label": "उप-कार्यों का निर्माण और पूर्णता हमेशा अनुमोदित करें", "description": "अनुमोदन की आवश्यकता के बिना उप-कार्यों के निर्माण और पूर्णता की अनुमति दें"}, "execute": {"label": "अनुमत निष्पादन ऑपरेशन हमेशा अनुमोदित करें", "description": "अनुमोदन की आवश्यकता के बिना स्वचालित रूप से अनुमत टर्मिनल कमांड निष्पादित करें", "allowedCommands": "अनुमत स्वतः-निष्पादन कमांड", "allowedCommandsDescription": "कमांड प्रीफिक्स जो स्वचालित रूप से निष्पादित किए जा सकते हैं जब \"निष्पादन ऑपरेशन हमेशा अनुमोदित करें\" सक्षम है। सभी कमांड की अनुमति देने के लिए * जोड़ें (सावधानी से उपयोग करें)।", "commandPlaceholder": "कमांड प्रीफिक्स दर्ज करें (उदा. 'git ')", "addButton": "जोड़ें"}}, "providers": {"configProfile": "कॉन्फिगरेशन प्रोफाइल", "providerDocumentation": "{{provider}} दस्तावेज़ीकरण", "description": "विभिन्न API कॉन्फ़िगरेशन सहेजें ताकि प्रदाताओं और सेटिंग्स के बीच त्वरित रूप से स्विच कर सकें।", "apiProvider": "API प्रदाता", "model": "मॉडल", "nameEmpty": "नाम खाली नहीं हो सकता", "nameExists": "इस नाम वाला प्रोफ़ाइल पहले से मौजूद है", "deleteProfile": "प्रोफ़ाइल हटाएं", "invalidArnFormat": "अमान्य ARN प्रारूप। कृपया ऊपर दिए गए उदाहरण देखें।", "enterNewName": "नया नाम दर्ज करें", "addProfile": "प्रोफ़ाइल जोड़ें", "renameProfile": "प्रोफ़ाइल का नाम बदलें", "newProfile": "नया कॉन्फ़िगरेशन प्रोफ़ाइल", "enterProfileName": "प्रोफ़ाइल नाम दर्ज करें", "createProfile": "प्रोफ़ाइल बनाएं", "cannotDeleteOnlyProfile": "केवल एकमात्र प्रोफ़ाइल को हटाया नहीं जा सकता", "searchPlaceholder": "प्रोफ़ाइल खोजें", "noMatchFound": "कोई मिलान प्रोफ़ाइल नहीं मिला", "vscodeLmDescription": "VS कोड भाषा मॉडल API आपको अन्य VS कोड एक्सटेंशन (जैसे GitHub Copilot) द्वारा प्रदान किए गए मॉडल चलाने की अनुमति देता है। शुरू करने का सबसे आसान तरीका VS कोड मार्केटप्लेस से Copilot और Copilot चैट एक्सटेंशन इंस्टॉल करना है।", "awsCustomArnUse": "आप जिस मॉडल का उपयोग करना चाहते हैं, उसके लिए एक वैध AWS बेडरॉक ARN दर्ज करें। प्रारूप उदाहरण:", "awsCustomArnDesc": "सुनिश्चित करें कि ARN में क्षेत्र ऊपर चयनित AWS क्षेत्र से मेल खाता है।", "openRouterApiKey": "OpenRouter API कुंजी", "getOpenRouterApiKey": "OpenRouter API कुंजी प्राप्त करें", "apiKeyStorageNotice": "API कुंजियाँ VSCode के सुरक्षित स्टोरेज में सुरक्षित रूप से संग्रहीत हैं", "glamaApiKey": "Glama API कुंजी", "getGlamaApiKey": "<PERSON>lama API कुंजी प्राप्त करें", "useCustomBaseUrl": "कस्टम बेस URL का उपयोग करें", "useHostHeader": "कस्टम होस्ट हेडर का उपयोग करें", "useLegacyFormat": "पुराने OpenAI API प्रारूप का उपयोग करें", "requestyApiKey": "Requesty API कुंजी", "getRequestyApiKey": "Requesty API कुंजी प्राप्त करें", "openRouterTransformsText": "संदर्भ आकार के लिए प्रॉम्प्ट और संदेश श्रृंखलाओं को संपीड़ित करें (<a>OpenRouter ट्रांसफॉर्म</a>)", "anthropicApiKey": "Anthropic API कुंजी", "getAnthropicApiKey": "Anthropic API कुंजी प्राप्त करें", "deepSeekApiKey": "DeepSeek API कुंजी", "getDeepSeekApiKey": "DeepSeek API कुंजी प्राप्त करें", "geminiApiKey": "Gemini API कुंजी", "getGeminiApiKey": "Gemini API कुंजी प्राप्त करें", "openAiApiKey": "OpenAI API कुंजी", "openAiBaseUrl": "बेस URL", "getOpenAiApiKey": "OpenAI API कुंजी प्राप्त करें", "mistralApiKey": "Mistral API कुंजी", "getMistralApiKey": "Mistral / Codestral API कुंजी प्राप्त करें", "codestralBaseUrl": "Codestral बेस URL (वैकल्पिक)", "codestralBaseUrlDesc": "Codestral मॉडल के लिए वैकल्पिक URL सेट करें।", "awsCredentials": "AWS क्रेडेंशियल्स", "awsProfile": "AWS प्रोफाइल", "awsProfileName": "AWS प्रोफाइल नाम", "awsAccessKey": "A<PERSON> एक्सेस कुंजी", "awsSecretKey": "A<PERSON> सीक्रेट कुंजी", "awsSessionToken": "AWS सत्र टोकन", "awsRegion": "<PERSON><PERSON> क्षेत्र", "awsCrossRegion": "क्रॉस-क्षेत्र अनुमान का उपयोग करें", "enablePromptCaching": "प्रॉम्प्ट कैशिंग सक्षम करें", "enablePromptCachingTitle": "समर्थित मॉडल के लिए प्रदर्शन में सुधार और लागत को कम करने के लिए प्रॉम्प्ट कैशिंग सक्षम करें।", "cacheUsageNote": "नोट: यदि आप कैश उपयोग नहीं देखते हैं, तो एक अलग मॉडल चुनने का प्रयास करें और फिर अपने वांछित मॉडल को पुनः चुनें।", "vscodeLmModel": "भाषा मॉडल", "vscodeLmWarning": "नोट: यह एक बहुत ही प्रायोगिक एकीकरण है और प्रदाता समर्थन भिन्न होगा। यदि आपको किसी मॉडल के समर्थित न होने की त्रुटि मिलती है, तो यह प्रदाता की ओर से एक समस्या है।", "googleCloudSetup": {"title": "Google Cloud Vertex AI का उपयोग करने के लिए, आपको आवश्यकता है:", "step1": "1. Google Cloud खाता बनाएं, Vertex AI API सक्षम करें और वांछित Claude मॉडल सक्षम करें।", "step2": "2. Google Cloud CLI इंस्टॉल करें और एप्लिकेशन डिफ़ॉल्ट क्रेडेंशियल्स कॉन्फ़िगर करें।", "step3": "3. या क्रेडेंशियल्स के साथ एक सर्विस अकाउंट बनाएं।"}, "googleCloudCredentials": "Google Cloud क्रेडेंशियल्स", "googleCloudKeyFile": "Google Cloud कुंजी फ़ाइल पथ", "googleCloudProjectId": "Google Cloud प्रोजेक्ट ID", "googleCloudRegion": "Google Cloud क्षेत्र", "lmStudio": {"baseUrl": "बेस URL (वैकल्पिक)", "modelId": "मॉडल ID", "speculativeDecoding": "स्पेक्युलेटिव डिकोडिंग सक्षम करें", "draftModelId": "ड्राफ्ट मॉडल ID", "draftModelDesc": "स्पेक्युलेटिव डिकोडिंग के सही काम करने के लिए ड्राफ्ट मॉडल को समान मॉडल परिवार से होना चाहिए।", "selectDraftModel": "ड्राफ्ट मॉडल चुनें", "noModelsFound": "कोई ड्राफ्ट मॉडल नहीं मिला। कृपया सुनिश्चित करें कि LM Studio सर्वर मोड सक्षम के साथ चल रहा है।", "description": "LM Studio आपको अपने कंप्यूटर पर स्थानीय रूप से मॉडल चलाने की अनुमति देता है। आरंभ करने के निर्देशों के लिए, उनकी <a>क्विकस्टार्ट गाइड</a> देखें। आपको इस एक्सटेंशन के साथ उपयोग करने के लिए LM Studio की <b>स्थानीय सर्वर</b> सुविधा भी शुरू करनी होगी। <span>नोट:</span> Roo Code जटिल प्रॉम्प्ट्स का उपयोग करता है और Claude मॉडल के साथ सबसे अच्छा काम करता है। कम क्षमता वाले मॉडल अपेक्षित रूप से काम नहीं कर सकते हैं।"}, "ollama": {"baseUrl": "बेस URL (वैकल्पिक)", "modelId": "मॉडल ID", "description": "<PERSON><PERSON>ma आपको अपने कंप्यूटर पर स्थानीय रूप से मॉडल चलाने की अनुमति देता है। आरंभ करने के निर्देशों के लिए, उनकी क्विकस्टार्ट गाइड देखें।", "warning": "नोट: Roo Code जटिल प्रॉम्प्ट्स का उपयोग करता है और Claude मॉडल के साथ सबसे अच्छा काम करता है। कम क्षमता वाले मॉडल अपेक्षित रूप से काम नहीं कर सकते हैं।"}, "unboundApiKey": "Unbound API कुंजी", "getUnboundApiKey": "Unbound API कुंजी प्राप्त करें", "humanRelay": {"description": "कोई API कुंजी आवश्यक नहीं है, लेकिन उपयोगकर्ता को वेब चैट AI में जानकारी कॉपी और पेस्ट करने में मदद करनी होगी।", "instructions": "उपयोग के दौरान, एक डायलॉग बॉक्स पॉप अप होगा और वर्तमान संदेश स्वचालित रूप से क्लिपबोर्ड पर कॉपी हो जाएगा। आपको इन्हें AI के वेब संस्करणों (जैसे ChatGPT या Claude) में पेस्ट करना होगा, फिर AI की प्रतिक्रिया को डायलॉग बॉक्स में वापस कॉपी करें और पुष्टि बटन पर क्लिक करें।"}, "openRouter": {"providerRouting": {"title": "OpenRouter प्रदाता रूटिंग", "description": "OpenRouter आपके मॉडल के लिए सर्वोत्तम उपलब्ध प्रदाताओं को अनुरोध भेजता है। डिफ़ॉल्ट रूप से, अपटाइम को अधिकतम करने के लिए अनुरोधों को शीर्ष प्रदाताओं के बीच संतुलित किया जाता है। हालांकि, आप इस मॉडल के लिए उपयोग करने के लिए एक विशिष्ट प्रदाता चुन सकते हैं।", "learnMore": "प्रदाता रूटिंग के बारे में अधिक जानें"}}, "customModel": {"capabilities": "अपने कस्टम OpenAI-संगत मॉडल के लिए क्षमताओं और मूल्य निर्धारण को कॉन्फ़िगर करें। मॉडल क्षमताओं को निर्दिष्ट करते समय सावधान रहें, क्योंकि वे Roo Code के प्रदर्शन को प्रभावित कर सकती हैं।", "maxTokens": {"label": "अधिकतम आउटपुट टोकन", "description": "मॉडल एक प्रतिक्रिया में अधिकतम कितने टोकन जनरेट कर सकता है। (सर्वर को अधिकतम टोकन सेट करने की अनुमति देने के लिए -1 निर्दिष्ट करें।)"}, "contextWindow": {"label": "संदर्भ विंडो आकार", "description": "कुल टोकन (इनपुट + आउटपुट) जो मॉडल प्रोसेस कर सकता है।"}, "imageSupport": {"label": "छवि समर्थन", "description": "क्या यह मॉडल छवियों को प्रोसेस और समझने में सक्षम है?"}, "computerUse": {"label": "कंप्यूटर उपयोग", "description": "क्या यह मॉडल ब्राउज़र के साथ इंटरैक्ट करने में सक्षम है? (उदा. <PERSON> 3.7 Sonnet)।"}, "promptCache": {"label": "प्रॉम्प्ट कैशिंग", "description": "क्या यह मॉडल प्रॉम्प्ट्स को कैश करने में सक्षम है?"}, "pricing": {"input": {"label": "इनपुट मूल्य", "description": "इनपुट/प्रॉम्प्ट में प्रति मिलियन टोकन की लागत। यह मॉडल को संदर्भ और निर्देश भेजने की लागत को प्रभावित करता है।"}, "output": {"label": "आउटपुट मूल्य", "description": "मॉडल की प्रतिक्रिया में प्रति मिलियन टोकन की लागत। यह जनरेट की गई सामग्री और पूर्णताओं की लागत को प्रभावित करता है।"}, "cacheReads": {"label": "कैश रीड्स मूल्य", "description": "कैश से पढ़ने के लिए प्रति मिलियन टोकन की लागत। यह वह मूल्य है जो कैश की गई प्रतिक्रिया प्राप्त करने पर लगाया जाता है।"}, "cacheWrites": {"label": "कैश राइट्स मूल्य", "description": "कैश में लिखने के लिए प्रति मिलियन टोकन की लागत। यह वह मूल्य है जो पहली बार प्रॉम्प्ट को कैश करने पर लगाया जाता है।"}}, "resetDefaults": "डिफ़ॉल्ट पर रीसेट करें"}, "rateLimitSeconds": {"label": "दर सीमा", "description": "API अनुरोधों के बीच न्यूनतम समय।"}}, "browser": {"enable": {"label": "ब्राउज़र टूल सक्षम करें", "description": "जब सक्षम होता है, तो Roo कंप्यूटर उपयोग का समर्थन करने वाले मॉडल का उपयोग करते समय वेबसाइटों के साथ बातचीत करने के लिए ब्राउज़र का उपयोग कर सकता है।"}, "viewport": {"label": "व्यूपोर्ट आकार", "description": "ब्राउज़र इंटरैक्शन के लिए व्यूपोर्ट आकार चुनें। यह वेबसाइटों के प्रदर्शन और उनके साथ बातचीत को प्रभावित करता है।", "options": {"largeDesktop": "बड़ा डेस्कटॉप (1280x800)", "smallDesktop": "छोटा डेस्कटॉप (900x600)", "tablet": "टैबलेट (768x1024)", "mobile": "मोबाइल (360x640)"}}, "screenshotQuality": {"label": "स्क्रीनशॉट गुणवत्ता", "description": "ब्राउज़र स्क्रीनशॉट की WebP गुणवत्ता समायोजित करें। उच्च मान स्पष्ट स्क्रीनशॉट प्रदान करते हैं लेकिन token उपयोग बढ़ाते हैं।"}, "remote": {"label": "दूरस्थ ब्राउज़र कनेक्शन का उपयोग करें", "description": "रिमोट डीबगिंग सक्षम के साथ चल रहे Chrome ब्राउज़र से कनेक्ट करें (--remote-debugging-port=9222)।", "urlPlaceholder": "कस्टम URL (उदा. http://localhost:9222)", "testButton": "कनेक्शन का परीक्षण करें", "testingButton": "परीक्षण हो रहा है...", "instructions": "DevTools प्रोटोकॉल होस्ट पता दर्ज करें या Chrome स्थानीय इंस्टेंस स्वतः खोजने के लिए खाली छोड़ दें। टेस्ट कनेक्शन बटन यदि प्रदान किया गया है तो कस्टम URL का प्रयास करेगा, या यदि फ़ील्ड खाली है तो स्वतः खोज करेगा।"}}, "checkpoints": {"enable": {"label": "स्वचालित चेकपॉइंट सक्षम करें", "description": "जब सक्षम होता है, तो Roo कार्य निष्पादन के दौरान स्वचालित रूप से चेकपॉइंट बनाएगा, जिससे परिवर्तनों की समीक्षा करना या पहले की स्थितियों पर वापस जाना आसान हो जाएगा।"}}, "notifications": {"sound": {"label": "ध्वनि प्रभाव सक्षम करें", "description": "जब सक्षम होता है, तो Roo सूचनाओं और घटनाओं के लिए ध्वनि प्रभाव चलाएगा।", "volumeLabel": "वॉल्यूम"}, "tts": {"label": "टेक्स्ट-टू-स्पीच सक्षम करें", "description": "जब सक्षम होता है, तो Roo टेक्स्ट-टू-स्पीच का उपयोग करके अपनी प्रतिक्रियाओं को बोलकर पढ़ेगा।", "speedLabel": "गति"}}, "contextManagement": {"description": "AI के संदर्भ विंडो में शामिल जानकारी को नियंत्रित करें, जो token उपयोग और प्रतिक्रिया गुणवत्ता को प्रभावित करता है", "openTabs": {"label": "खुले टैब संदर्भ सीमा", "description": "संदर्भ में शामिल करने के लिए VSCode खुले टैब की अधिकतम संख्या। उच्च मान अधिक संदर्भ प्रदान करते हैं लेकिन token उपयोग बढ़ाते हैं।"}, "workspaceFiles": {"label": "वर्कस्पेस फाइल संदर्भ सीमा", "description": "वर्तमान कार्य निर्देशिका विवरण में शामिल करने के लिए फाइलों की अधिकतम संख्या। उच्च मान अधिक संदर्भ प्रदान करते हैं लेकिन token उपयोग बढ़ाते हैं।"}, "rooignore": {"label": "सूचियों और खोजों में .r<PERSON><PERSON><PERSON> फाइलें दिखाएँ", "description": "जब सक्षम होता है, .r<PERSON><PERSON><PERSON> में पैटर्न से मेल खाने वाली फाइलें लॉक प्रतीक के साथ सूचियों में दिखाई जाएंगी। जब अक्षम होता है, ये फाइलें फाइल सूचियों और खोजों से पूरी तरह छिपा दी जाएंगी।"}, "maxReadFile": {"label": "फ़ाइल पढ़ने का स्वचालित काटने की सीमा", "description": "जब मॉडल प्रारंभ/अंत मान नहीं देता है, तो Roo इतनी पंक्तियाँ पढ़ता है। यदि यह संख्या फ़ाइल की कुल पंक्तियों से कम है, तो Roo कोड परिभाषाओं का पंक्ति क्रमांक इंडेक्स बनाता है। विशेष मामले: -1 Roo को पूरी फ़ाइल पढ़ने का निर्देश देता है (इंडेक्सिंग के बिना), और 0 कोई पंक्ति न पढ़ने और न्यूनतम संदर्भ के लिए केवल पंक्ति इंडेक्स प्रदान करने का निर्देश देता है। कम मान प्रारंभिक संदर्भ उपयोग को कम करते हैं, जो बाद में सटीक पंक्ति श्रेणी पढ़ने की अनुमति देता है। स्पष्ट प्रारंभ/अंत अनुरोध इस सेटिंग से सीमित नहीं हैं।", "lines": "पंक्तियाँ", "always_full_read": "हमेशा पूरी फ़ाइल पढ़ें"}}, "terminal": {"outputLineLimit": {"label": "टर्मिनल आउटपुट सीमा", "description": "कमांड निष्पादित करते समय टर्मिनल आउटपुट में शामिल करने के लिए पंक्तियों की अधिकतम संख्या। पार होने पर पंक्तियाँ मध्य से हटा दी जाएंगी, token बचाते हुए।"}, "shellIntegrationTimeout": {"label": "टर्मिनल शेल एकीकरण टाइमआउट", "description": "कमांड निष्पादित करने से पहले शेल एकीकरण के आरंभ होने के लिए प्रतीक्षा का अधिकतम समय। लंबे शेल स्टार्टअप समय वाले उपयोगकर्ताओं के लिए, यदि आप टर्मिनल में \"Shell Integration Unavailable\" त्रुटियाँ देखते हैं तो इस मान को बढ़ाने की आवश्यकता हो सकती है।"}}, "advanced": {"diff": {"label": "diffs के माध्यम से संपादन सक्षम करें", "description": "जब सक्षम होता है, Roo फाइलों को तेजी से संपादित कर सकेगा और स्वचालित रूप से काटे गए पूर्ण-फाइल लेखन को अस्वीकार करेगा। नवीनतम Claude 3.7 Sonnet मॉडल के साथ सबसे अच्छा काम करता है।", "strategy": {"label": "Diff रणनीति", "options": {"standard": "मानक (एकल ब्लॉक)", "multiBlock": "प्रायोगिक: मल्टी-ब्लॉक diff", "unified": "प्रायोगिक: एकीकृत diff"}, "descriptions": {"standard": "मानक diff रणनीति एक समय में एक कोड ब्लॉक पर परिवर्तन लागू करती है।", "unified": "एकीकृत diff रणनीति diffs लागू करने के लिए कई दृष्टिकोण लेती है और सर्वोत्तम दृष्टिकोण चुनती है।", "multiBlock": "मल्टी-ब्लॉक diff रणनीति एक अनुरोध में एक फाइल में कई कोड ब्लॉक अपडेट करने की अनुमति देती है।"}}, "matchPrecision": {"label": "मिलान सटीकता", "description": "यह स्लाइडर नियंत्रित करता है कि diffs लागू करते समय कोड अनुभागों को कितनी सटीकता से मेल खाना चाहिए। निम्न मान अधिक लचीले मिलान की अनुमति देते हैं लेकिन गलत प्रतिस्थापन का जोखिम बढ़ाते हैं। 100% से नीचे के मानों का उपयोग अत्यधिक सावधानी के साथ करें।"}}}, "experimental": {"warning": "⚠️", "DIFF_STRATEGY_UNIFIED": {"name": "प्रायोगिक एकीकृत diff रणनीति का उपयोग करें", "description": "प्रायोगिक एकीकृत diff रणनीति सक्षम करें। यह रणनीति मॉडल त्रुटियों के कारण पुनः प्रयासों की संख्या को कम कर सकती है, लेकिन अप्रत्याशित व्यवहार या गलत संपादन का कारण बन सकती है। केवल तभी सक्षम करें जब आप जोखिमों को समझते हों और सभी परिवर्तनों की सावधानीपूर्वक समीक्षा करने के लिए तैयार हों।"}, "SEARCH_AND_REPLACE": {"name": "प्रायोगिक खोज और प्रतिस्थापन उपकरण का उपयोग करें", "description": "प्रायोगिक खोज और प्रतिस्थापन उपकरण सक्षम करें, जो Roo को एक अनुरोध में खोज शब्द के कई उदाहरणों को बदलने की अनुमति देता है।"}, "INSERT_BLOCK": {"name": "प्रायोगिक सामग्री सम्मिलित करने के उपकरण का उपयोग करें", "description": "प्रायोगिक सामग्री सम्मिलित करने के उपकरण को सक्षम करें, जो Roo को diff बनाए बिना विशिष्ट लाइन नंबरों पर सामग्री सम्मिलित करने की अनुमति देता है।"}, "POWER_STEERING": {"name": "प्रायोगिक \"पावर स्टीयरिंग\" मोड का उपयोग करें", "description": "जब सक्षम किया जाता है, तो Roo मॉडल को उसके वर्तमान मोड परिभाषा के विवरण के बारे में अधिक बार याद दिलाएगा। इससे भूमिका परिभाषाओं और कस्टम निर्देशों के प्रति अधिक मजबूत अनुपालन होगा, लेकिन प्रति संदेश अधिक token का उपयोग होगा।"}, "MULTI_SEARCH_AND_REPLACE": {"name": "प्रायोगिक मल्टी ब्लॉक diff उपकरण का उपयोग करें", "description": "जब सक्षम किया जाता है, तो Roo मल्टी ब्लॉक diff उपकरण का उपयोग करेगा। यह एक अनुरोध में फ़ाइल में कई कोड ब्लॉक अपडेट करने का प्रयास करेगा।"}}, "temperature": {"useCustom": "कस्टम तापमान का उपयोग करें", "description": "मॉडल की प्रतिक्रियाओं में यादृच्छिकता को नियंत्रित करता है।", "rangeDescription": "उच्च मान आउटपुट को अधिक यादृच्छिक बनाते हैं, निम्न मान इसे अधिक निर्धारित बनाते हैं।"}, "modelInfo": {"supportsImages": "छवियों का समर्थन करता है", "noImages": "छवियों का समर्थन नहीं करता है", "supportsComputerUse": "कंप्यूटर उपयोग का समर्थन करता है", "noComputerUse": "कंप्यूटर उपयोग का समर्थन नहीं करता है", "supportsPromptCache": "प्रॉम्प्ट कैशिंग का समर्थन करता है", "noPromptCache": "प्रॉम्प्ट कैशिंग का समर्थन नहीं करता है", "maxOutput": "अधिकतम आउटपुट", "inputPrice": "इनपुट मूल्य", "outputPrice": "आउटपुट मूल्य", "cacheReadsPrice": "कैश रीड्स मूल्य", "cacheWritesPrice": "कैश राइट्स मूल्य", "enableStreaming": "स्ट्रीमिंग सक्षम करें", "enableR1Format": "R1 मॉडल पैरामीटर सक्षम करें", "enableR1FormatTips": "QWQ जैसी R1 मॉडलों का उपयोग करते समय इसे सक्षम करना आवश्यक है, ताकि 400 त्रुटि से बचा जा सके", "useAzure": "Azure का उपयोग करें", "azureApiVersion": "Azure API संस्करण सेट करें", "gemini": {"freeRequests": "* प्रति मिनट {{count}} अनुरोधों तक मुफ्त। उसके बाद, बिलिंग प्रॉम्प्ट आकार पर निर्भर करती है।", "pricingDetails": "अधिक जानकारी के लिए, मूल्य निर्धारण विवरण देखें।", "billingEstimate": "* बिलिंग एक अनुमान है - सटीक लागत प्रॉम्प्ट आकार पर निर्भर करती है।"}}, "modelPicker": {"automaticFetch": "एक्सटेंशन <serviceLink>{{serviceName}}</serviceLink> पर उपलब्ध मॉडलों की नवीनतम सूची स्वचालित रूप से प्राप्त करता है। यदि आप अनिश्चित हैं कि कौन सा मॉडल चुनना है, तो Roo Code <defaultModelLink>{{defaultModelId}}</defaultModelLink> के साथ सबसे अच्छा काम करता है। आप वर्तमान में उपलब्ध निःशुल्क विकल्पों के लिए \"free\" भी खोज सकते हैं।", "label": "मॉडल", "searchPlaceholder": "खोजें", "noMatchFound": "कोई मिलान नहीं मिला", "useCustomModel": "कस्टम उपयोग करें: {{modelId}}"}, "footer": {"feedback": "यदि आपके कोई प्रश्न या प्रतिक्रिया है, तो <githubLink>github.com/RooVetGit/Roo-Code</githubLink> पर एक मुद्दा खोलने या <redditLink>reddit.com/r/RooCode</redditLink> या <discordLink>discord.gg/roocode</discordLink> में शामिल होने में संकोच न करें", "version": "Roo Code v{{version}}", "telemetry": {"label": "गुमनाम त्रुटि और उपयोग रिपोर्टिंग की अनुमति दें", "description": "गुमनाम उपयोग डेटा और त्रुटि रिपोर्ट भेजकर Roo Code को बेहतर बनाने में मदद करें। कोड, प्रॉम्प्ट, या व्यक्तिगत जानकारी कभी भी नहीं भेजी जाती है। अधिक विवरण के लिए हमारी गोपनीयता नीति देखें।"}, "settings": {"import": "इम्पोर्ट", "export": "एक्सपोर्ट", "reset": "रीसेट करें"}}, "thinkingBudget": {"maxTokens": "अधिकतम tokens", "maxThinkingTokens": "अधिकतम thinking tokens"}, "validation": {"apiKey": "आपको एक मान्य API कुंजी प्रदान करनी होगी।", "awsRegion": "AWS Bedrock का उपयोग करने के लिए आपको एक क्षेत्र चुनना होगा।", "googleCloud": "आपको एक मान्य Google Cloud प्रोजेक्ट ID और क्षेत्र प्रदान करना होगा।", "modelId": "आपको एक मान्य मॉडल ID प्रदान करनी होगी।", "modelSelector": "आपको एक मान्य मॉडल चयनकर्ता प्रदान करना होगा।", "openAi": "आपको एक मान्य बेस URL, API कुंजी और मॉडल ID प्रदान करनी होगी।", "arn": {"invalidFormat": "अमान्य ARN प्रारूप। कृपया प्रारूप आवश्यकताएं जांचें।", "regionMismatch": "चेतावनी: आपक<PERSON> ARN में क्षेत्र ({{arnRegion}}) आपके चयनित क्षेत्र ({{region}}) से मेल नहीं खाता। इससे पहुंच संबंधी समस्याएं हो सकती हैं। प्रदाता ARN से क्षेत्र का उपयोग करेगा।"}, "modelAvailability": "आपके द्वारा प्रदान की गई मॉडल ID ({{modelId}}) उपलब्ध नहीं है। कृपया कोई अन्य मॉडल चुनें।"}, "placeholders": {"apiKey": "API कुंजी दर्ज करें...", "profileName": "प्रोफ़ाइल नाम दर्ज करें", "accessKey": "एक्सेस कुंजी दर्ज करें...", "secretKey": "गुप्त कुंजी दर्ज करें...", "sessionToken": "सत्र टोकन दर्ज करें...", "credentialsJson": "क्रेडेंशियल्स JSON दर्ज करें...", "keyFilePath": "कुंजी फ़ाइल पथ दर्ज करें...", "projectId": "प्रोजेक्ट ID दर्ज करें...", "customArn": "ARN दर्ज करें (उदा. arn:aws:bedrock:us-east-1:123456789012:foundation-model/my-model)", "baseUrl": "बेस URL दर्ज करें...", "modelId": {"lmStudio": "उदा. meta-llama-3.1-8b-instruct", "lmStudioDraft": "उदा. lmstudio-community/llama-3.2-1b-instruct", "ollama": "उदा. llama3.1"}, "numbers": {"maxTokens": "उदा. 4096", "contextWindow": "उदा. 128000", "inputPrice": "उदा. 0.0001", "outputPrice": "उदा. 0.0002", "cacheWritePrice": "उदा. 0.00005"}}, "defaults": {"ollamaUrl": "डिफ़ॉल्ट: http://localhost:11434", "lmStudioUrl": "डिफ़ॉल्ट: http://localhost:1234", "geminiUrl": "डिफ़ॉल्ट: https://generativelanguage.googleapis.com"}, "labels": {"customArn": "कस्टम ARN", "useCustomArn": "कस्टम ARN का उपयोग करें..."}}