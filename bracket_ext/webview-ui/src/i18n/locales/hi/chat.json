{"greeting": "Roo आपके लिए क्या कर सकता है?", "task": {"title": "कार्य", "seeMore": "अधिक देखें", "seeLess": "कम देखें", "tokens": "Tokens:", "cache": "कैश:", "apiCost": "API लागत:", "contextWindow": "संदर्भ विंडो:", "closeAndStart": "कार्य बंद करें और नया शुरू करें", "export": "कार्य इतिहास निर्यात करें", "delete": "कार्य हटाएं (पुष्टि को छोड़ने के लिए Shift + क्लिक)"}, "unpin": "पिन करें", "pin": "अवपिन करें", "tokenProgress": {"availableSpace": "उपलब्ध स्थान: {{amount}} tokens", "tokensUsed": "प्रयुक्त tokens: {{used}} / {{total}}", "reservedForResponse": "मॉडल प्रतिक्रिया के लिए आरक्षित: {{amount}} tokens"}, "retry": {"title": "पुनः प्रयास करें", "tooltip": "ऑपरेशन फिर से प्रयास करें"}, "startNewTask": {"title": "नया कार्य शुरू करें", "tooltip": "नया कार्य शुरू करें"}, "proceedAnyways": {"title": "फिर भी आगे बढ़ें", "tooltip": "कमांड निष्पादन के दौरान जारी रखें"}, "save": {"title": "सहेजें", "tooltip": "फ़ाइल परिवर्तन सहेजें"}, "reject": {"title": "अस्वीकार करें", "tooltip": "इस क्रिया को अस्वीकार करें"}, "completeSubtaskAndReturn": "उपकार्य पूरा करें और वापस लौटें", "approve": {"title": "स्वीकृत करें", "tooltip": "इस क्रिया को स्वीकृत करें"}, "runCommand": {"title": "कमांड चलाएँ", "tooltip": "इस कमांड को निष्पादित करें"}, "proceedWhileRunning": {"title": "चलते समय आगे बढ़ें", "tooltip": "चेतावनियों के बावजूद जारी रखें"}, "resumeTask": {"title": "कार्य जारी रखें", "tooltip": "वर्तमान कार्य जारी रखें"}, "terminate": {"title": "समाप्त करें", "tooltip": "वर्तमान कार्य समाप्त करें"}, "cancel": {"title": "रद्<PERSON> करें", "tooltip": "वर्तमान ऑपरेशन रद्द करें"}, "scrollToBottom": "चैट के निचले हिस्से तक स्क्रॉल करें", "aboutMe": "एजेंटिक कोडिंग क्षमताओं में नवीनतम प्रगति के कारण, मैं जटिल सॉफ्टवेयर विकास कार्यों को चरण-दर-चरण संभाल सकता हूं। ऐसे उपकरणों के साथ जो मुझे फ़ाइलें बनाने और संपादित करने, जटिल प्रोजेक्ट का अन्वेषण करने, ब्राउज़र का उपयोग करने और टर्मिनल कमांड (आपकी अनुमति के बाद) निष्पादित करने की अनुमति देते हैं, मैं आपकी मदद कोड पूर्णता या तकनीकी समर्थन से परे तरीकों से कर सकता हूं। मैं अपनी क्षमताओं का विस्तार करने और नए उपकरण बनाने के लिए MCP का भी उपयोग कर सकता हूं।", "selectMode": "इंटरैक्शन मोड चुनें", "selectApiConfig": "API कॉन्फ़िगरेशन चुनें", "enhancePrompt": "अतिरिक्त संदर्भ के साथ प्रॉम्प्ट बढ़ाएँ", "addImages": "संदेश में चित्र जोड़ें", "sendMessage": "संदेश भेजें", "typeMessage": "एक संदेश लिखें...", "typeTask": "अपना कार्य यहां लिखें...", "addContext": "संदर्भ जोड़ने के लिए @, मोड बदलने के लिए /", "dragFiles": "फ़ाइलें खींचने के लिए shift दबाकर रखें", "dragFilesImages": "फ़ाइलें/चित्र खींचने के लिए shift दबाकर रखें", "enhancePromptDescription": "'प्रॉम्प्ट बढ़ाएँ' बटन अतिरिक्त संदर्भ, स्पष्टीकरण या पुनर्विचार प्रदान करके आपके अनुरोध को बेहतर बनाने में मदद करता है। यहां अनुरोध लिखकर देखें और यह कैसे काम करता है यह देखने के लिए बटन पर फिर से क्लिक करें।", "errorReadingFile": "फ़ाइल पढ़ने में त्रुटि:", "noValidImages": "कोई मान्य चित्र प्रोसेस नहीं किया गया", "separator": "विभाजक", "edit": "संपादित करें...", "forNextMode": "अगले मोड के लिए", "error": "त्रुटि", "troubleMessage": "<PERSON>oo को समस्या हो रही है...", "apiRequest": {"title": "API अनुरोध", "failed": "API अनुरोध विफल हुआ", "streaming": "API अनुरोध...", "cancelled": "API अनुरोध रद्द किया गया", "streamingFailed": "API स्ट्रीमिंग विफल हुई"}, "checkpoint": {"initial": "प्रारंभिक चेकपॉइंट", "regular": "चेकपॉइंट", "initializingWarning": "चेकपॉइंट अभी भी आरंभ हो रहा है... अगर यह बहुत समय ले रहा है, तो आप <settingsLink>सेटिंग्स</settingsLink> में चेकपॉइंट को अक्षम कर सकते हैं और अपने कार्य को पुनः आरंभ कर सकते हैं।", "menu": {"viewDiff": "अंतर देखें", "restore": "चेकपॉइंट पुनर्स्थापित करें", "restoreFiles": "फ़ाइलें पुनर्स्थापित करें", "restoreFilesDescription": "आपके प्रोजेक्ट की फ़ाइलों को इस बिंदु पर लिए गए स्नैपशॉट पर पुनर्स्थापित करता है।", "restoreFilesAndTask": "फ़ाइलें और कार्य पुनर्स्थापित करें", "confirm": "पुष्टि करें", "cancel": "रद्<PERSON> करें", "cannotUndo": "इस क्रिया को पूर्ववत नहीं किया जा सकता।", "restoreFilesAndTaskDescription": "आपके प्रोजेक्ट की फ़ाइलों को इस बिंदु पर लिए गए स्नैपशॉट पर पुनर्स्थापित करता है और इस बिंदु के बाद के सभी संदेशों को हटा देता है।"}, "current": "वर्तमान"}, "instructions": {"wantsToFetch": "<PERSON>oo को वर्तमान कार्य में सहायता के लिए विस्तृत निर्देश प्राप्त करना है"}, "fileOperations": {"wantsToRead": "Roo इस फ़ाइल को पढ़ना चाहता है:", "wantsToReadOutsideWorkspace": "<PERSON>oo कार्यक्षेत्र के बाहर इस फ़ाइल को पढ़ना चाहता है:", "didRead": "<PERSON>oo ने इस फ़ाइल को पढ़ा:", "wantsToEdit": "<PERSON>oo इस फ़ाइल को संपादित करना चाहता है:", "wantsToEditOutsideWorkspace": "<PERSON><PERSON> कार्यक्षेत्र के बाहर इस फ़ाइल को संपादित करना चाहता है:", "wantsToCreate": "Roo एक नई फ़ाइल बनाना चाहता है:"}, "directoryOperations": {"wantsToViewTopLevel": "Roo इस निर्देशिका में शीर्ष स्तर की फ़ाइलें देखना चाहता है:", "didViewTopLevel": "Roo ने इस निर्देशिका में शीर्ष स्तर की फ़ाइलें देखीं:", "wantsToViewRecursive": "Roo इस निर्देशिका में सभी फ़ाइलों को पुनरावर्ती रूप से देखना चाहता है:", "didViewRecursive": "Roo ने इस निर्देशिका में सभी फ़ाइलों को पुनरावर्ती रूप से देखा:", "wantsToViewDefinitions": "Roo इस निर्देशिका में उपयोग किए गए सोर्स कोड परिभाषा नामों को देखना चाहता है:", "didViewDefinitions": "Roo ने इस निर्देशिका में उपयोग किए गए सोर्स कोड परिभाषा नामों को देखा:", "wantsToSearch": "Roo इस निर्देशिका में <code>{{regex}}</code> के लिए खोज करना चाहता है:", "didSearch": "<PERSON>oo ने इस निर्देशिका में <code>{{regex}}</code> के लिए खोज की:"}, "commandOutput": "कमांड आउटपुट", "response": "प्रतिक्रिया", "arguments": "आर्ग्युमेंट्स", "mcp": {"wantsToUseTool": "<PERSON>oo {{serverName}} MCP सर्वर पर एक टूल का उपयोग करना चाहता है:", "wantsToAccessResource": "<PERSON>oo {{serverName}} MCP सर्वर पर एक संसाधन का उपयोग करना चाहता है:"}, "modes": {"wantsToSwitch": "Roo <code>{{mode}}</code> मोड में स्विच करना चाहता है", "wantsToSwitchWithReason": "Roo <code>{{mode}}</code> मोड में स्विच करना चाहता है क्योंकि: {{reason}}", "didSwitch": "Roo <code>{{mode}}</code> मोड में स्विच कर गया", "didSwitchWithReason": "<PERSON>oo <code>{{mode}}</code> मोड में स्विच कर गया क्योंकि: {{reason}}"}, "subtasks": {"wantsToCreate": "Roo <code>{{mode}}</code> मोड में एक नया उपकार्य बनाना चाहता है:", "wantsToFinish": "<PERSON>oo इस उपकार्य को समाप्त करना चाहता है", "newTaskContent": "उपकार्य निर्देश", "completionContent": "उपकार्य पूर्ण", "resultContent": "उपकार्य परिणाम", "defaultResult": "कृपया अगले कार्य पर जारी रखें।", "completionInstructions": "उपकार्य पूर्ण! आप परिणामों की समीक्षा कर सकते हैं और सुधार या अगले चरण सुझा सकते हैं। यदि सब कुछ ठीक लगता है, तो मुख्य कार्य को परिणाम वापस करने के लिए पुष्टि करें।"}, "questions": {"hasQuestion": "Roo का एक प्रश्न है:"}, "taskCompleted": "कार्य पूरा हुआ", "shellIntegration": {"unavailable": "शेल एकीकरण अनुपलब्ध", "troubleshooting": "अभी भी समस्या है?"}, "powershell": {"issues": "ऐसा लगता है कि आपको Windows PowerShell के साथ समस्याएँ हो रही हैं, कृपया इसे देखें"}, "autoApprove": {"title": "स्वत:-स्वीकृति:", "none": "कोई नहीं", "description": "स्वत:-स्वीकृति Roo Code को अनुमति मांगे बिना क्रियाएँ करने की अनुमति देती है। केवल उन क्रियाओं के लिए सक्षम करें जिन पर आप पूरी तरह से विश्वास करते हैं। अधिक विस्तृत कॉन्फ़िगरेशन <settingsLink>सेटिंग्स</settingsLink> में उपलब्ध है।", "actions": {"readFiles": {"label": "फ़ाइलें और निर्देशिकाएँ पढ़ें", "shortName": "पढ़ें", "description": "आपके कंप्यूटर पर किसी भी फ़ाइल को पढ़ने के लिए पहुँच की अनुमति देता है।"}, "editFiles": {"label": "फ़ाइलें संपादित करें", "shortName": "संपादित", "description": "आपके कंप्यूटर पर किसी भी फ़ाइल को संशोधित करने की अनुमति देता है।"}, "executeCommands": {"label": "स्वीकृत कमांड निष्पादित करें", "shortName": "कमांड", "description": "स्वीकृत टर्मिनल कमांड के निष्पादन की अनुमति देता है। आप इसे सेटिंग्स पैनल में कॉन्फ़िगर कर सकते हैं।"}, "useBrowser": {"label": "ब्राउज़र का उपयोग करें", "shortName": "ब्राउज़र", "description": "हेडलेस ब्राउज़र में किसी भी वेबसाइट को लॉन्च करने और उसके साथ इंटरैक्ट करने की क्षमता की अनुमति देता है।"}, "useMcp": {"label": "MCP सर्वर का उपयोग करें", "shortName": "MCP", "description": "कॉन्फ़िगर किए गए MCP सर्वर के उपयोग की अनुमति देता है जो फ़ाइल सिस्टम को संशोधित कर सकते हैं या API के साथ इंटरैक्ट कर सकते हैं।"}, "switchModes": {"label": "मोड स्विच करें", "shortName": "मोड", "description": "स्वीकृति की आवश्यकता के बिना विभिन्न मोड के बीच स्वचालित स्विचिंग की अनुमति देता है।"}, "subtasks": {"label": "उपकार्य बनाएं और पूरा करें", "shortName": "उपकार्य", "description": "स्वीकृति की आवश्यकता के बिना उपकार्यों के निर्माण और पूर्णता की अनुमति देता है।"}, "retryRequests": {"label": "विफल अनुरोधों को पुनः प्रयास करें", "shortName": "पुनर्प्रयास", "description": "जब प्रदाता त्रुटि प्रतिक्रिया लौटाता है तो विफल API अनुरोधों को स्वचालित रूप से पुनः प्रयास करता है।"}}}, "reasoning": {"thinking": "विचार कर रहा है", "seconds": "{{count}} सेकंड"}, "followUpSuggest": {"copyToInput": "इनपुट में कॉपी करें (या Shift + क्लिक)"}, "announcement": {"title": "बूमरैंग टास्क के साथ अधिक करें 🪃", "description": "कार्य को उप-कार्यों में विभाजित करें, जिनमें से प्रत्येक एक विशेष मोड में चलता है, जैसे code, architect, debug, या एक कस्टम मोड।", "learnMore": "अधिक जानें →", "hideButton": "घोषणा छिपाएँ"}, "browser": {"rooWantsToUse": "Roo ब्राउज़र का उपयोग करना चाहता है:", "consoleLogs": "कंसोल लॉग", "noNewLogs": "(कोई नया लॉग नहीं)", "screenshot": "ब्राउज़र स्क्रीनशॉट", "cursor": "कर्सर", "navigation": {"step": "चरण {{current}} / {{total}}", "previous": "पिछला", "next": "अगला"}, "sessionStarted": "ब्राउज़र सत्र शुरू हुआ", "actions": {"title": "ब्राउज़र क्रिया: ", "launch": "{{url}} पर ब्राउज़र लॉन्च करें", "click": "क्लिक करें ({{coordinate}})", "type": "टाइप करें \"{{text}}\"", "scrollDown": "नीचे स्क्रॉल करें", "scrollUp": "ऊपर स्क्रॉल करें", "close": "ब्राउज़र बंद करें"}}}