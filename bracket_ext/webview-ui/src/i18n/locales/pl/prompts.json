{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "done": "<PERSON><PERSON><PERSON>", "modes": {"title": "Tryby", "createNewMode": "Utwórz nowy tryb", "editModesConfig": "Edytuj konfigurację trybów", "editGlobalModes": "Ed<PERSON><PERSON>j tryby globalne", "editProjectModes": "<PERSON><PERSON><PERSON><PERSON> tryby projektu (.room<PERSON>)", "createModeHelpText": "<PERSON><PERSON><PERSON><PERSON> +, aby utworz<PERSON>ć nowy niestandardowy tryb, lub po prostu poproś Roo w czacie, aby utworzył go dla Ciebie!"}, "apiConfiguration": {"title": "Konfiguracja API", "select": "<PERSON><PERSON><PERSON><PERSON>, której konfiguracji API użyć dla tego trybu"}, "tools": {"title": "Dostępne narzędzia", "builtInModesText": "Narzędzia dla wbudowanych trybów nie mogą być modyfikowane", "editTools": "Edytuj narzędzia", "doneEditing": "Zakończ edycję", "allowedFiles": "Dozwolone pliki:", "toolNames": {"read": "Czytaj pliki", "edit": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>i", "browser": "Używaj przeglądarki", "command": "Uruchamiaj polecenia", "mcp": "Używaj MCP"}}, "roleDefinition": {"title": "Definicja roli", "resetToDefault": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Zdefiniuj wiedzę specjalistyczną i osobowość Roo dla tego trybu. Ten opis kształtuje, jak Roo prezentuje się i podchodzi do zadań."}, "customInstructions": {"title": "Niestandardowe instrukcje dla trybu (opcjonalne)", "resetToDefault": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Dodaj wytyczne dotyczące zachowania specyficzne dla trybu {{modeName}}.", "loadFromFile": "Niestandardowe instrukcje dla trybu {{modeName}} mogą być również ładowane z folderu <span>.roo/rules-{{modeSlug}}/</span> w Twoim obszarze roboczym (.roorules-{{modeSlug}} i .clinerules-{{modeSlug}} są przestarzałe i wkrótce przestaną działać)."}, "globalCustomInstructions": {"title": "Niestandardowe instrukcje dla wszystkich trybów", "description": "Te instrukcje dotyczą wszystkich trybów. Zapewniają podstawowy zestaw zachowań, które mogą być rozszerzone przez instrukcje specyficzne dla trybów poniżej.\n<PERSON><PERSON><PERSON>, aby <PERSON><PERSON> myślał i mówił w języku innym niż język wyświetlania Twojego edytora ({{language}}), mo<PERSON><PERSON><PERSON> to określić tutaj.", "loadFromFile": "Instrukcje mogą być również ładowane z folderu <span>.roo/rules/</span> w <PERSON><PERSON> obszarze roboczym (.roorules i .clinerules są przestarzałe i wkrótce przestaną działać)."}, "systemPrompt": {"preview": "Podgląd podpowiedzi systemowej", "copy": "Kopiuj podpowiedź systemową do schowka", "title": "Podpowiedź systemowa (tryb {{modeName}})"}, "supportPrompts": {"title": "Podpowied<PERSON> pomo<PERSON>ze", "resetPrompt": "Zresetuj podpowiedź {{promptType}} do domyślnej", "prompt": "Podpowiedź", "enhance": {"apiConfiguration": "Konfiguracja API", "apiConfigDescription": "Możesz wybrać konfigurację API, która będzie zawsze używana do ulepszania podpowiedzi, lub po prostu użyć aktualnie wybranej", "useCurrentConfig": "Użyj aktualnie wybranej konfiguracji API", "testPromptPlaceholder": "W<PERSON><PERSON><PERSON><PERSON> podpowiedź, aby przetestować ulepszenie", "previewButton": "Podgląd ulepszenia podpowiedzi"}, "types": {"ENHANCE": {"label": "Ulepsz podpowiedź", "description": "Uż<PERSON>j ulepszenia podpowiedzi, aby u<PERSON><PERSON><PERSON> dostosowane sugestie lub ulepszenia dla swoich danych wejściowych. Zapewnia to, że Roo rozumie Twoje intencje i dostarcza najlepsze możliwe odpowiedzi. Dostępne za pośrednictwem ikony ✨ w czacie."}, "EXPLAIN": {"label": "Wyjaśnij kod", "description": "Uzyskaj szczegółowe wyjaśnienia fragmentów kodu, funkcji lub całych plików. Przydatne do zrozumienia złożonego kodu lub nauki nowych wzorców. Dostępne w akcjach kodu (ikona żarówki w edytorze) i w menu kontekstowym edytor (prawy przycisk myszy na wybranym kodzie)."}, "FIX": {"label": "<PERSON><PERSON><PERSON> problemy", "description": "Uzyskaj pomoc w identyfikowaniu i rozwiązywaniu błędów, usterek lub problemów z jakością kodu. Zapewnia krok po kroku wskazówki do naprawy problemów. Dostępne w akcjach kodu (ikona żarówki w edytorze) i w menu kontekstowym edytor (prawy przycisk myszy na wybranym kodzie)."}, "IMPROVE": {"label": "Ulepsz kod", "description": "Otrzymuj sugestie dotyczące optymalizacji kodu, lepszych praktyk i ulepszeń architektonicznych przy zachowaniu funkcjonalności. Dostępne w akcjach kodu (ikona żarówki w edytorze) i w menu kontekstowym edytor (prawy przycisk myszy na wybranym kodzie)."}, "ADD_TO_CONTEXT": {"label": "Dodaj do kontekstu", "description": "Dodaj kontekst do bieżącego zadania lub rozmowy. Przydatne do dostarczania dodatkowych informacji lub wyjaśnień. Dostępne w akcjach kodu (ikona żarówki w edytorze) i w menu kontekstowym edytor (prawy przycisk myszy na wybranym kodzie)."}, "TERMINAL_ADD_TO_CONTEXT": {"label": "Dodaj zawartość terminala do kontekstu", "description": "Dodaj wyjście terminala do bieżącego zadania lub rozmowy. Przydatne do dostarczania wyników poleceń lub logów. Dostępne w menu kontekstowym terminala (prawy przycisk myszy na wybranej zawartości terminala)."}, "TERMINAL_FIX": {"label": "Napraw polecenie terminala", "description": "Uzyskaj pomoc w naprawianiu poleceń terminala, które zawiodły lub wymagają ulepszeń. Dostępne w menu kontekstowym terminala (prawy przycisk myszy na wybranej zawartości terminala)."}, "TERMINAL_EXPLAIN": {"label": "Wyjaśnij polecenie terminala", "description": "Uzyskaj szczegółowe wyjaśnienia poleceń terminala i ich wyników. Dostępne w menu kontekstowym terminala (prawy przycisk myszy na wybranej zawartości terminala)."}, "NEW_TASK": {"label": "Rozpocznij nowe zadanie", "description": "Rozpocznij nowe zadanie z wprowadzonymi danymi. Dostępne w palecie poleceń."}}}, "advancedSystemPrompt": {"title": "Zaawansowane: Zastąp podpowiedź systemową", "description": "Możesz całkowicie zastąpić podpowiedź systemową dla tego trybu (oprócz definicji roli i niestandardowych instrukcji) poprzez utworzenie pliku w .roo/system-prompt-{{modeSlug}} w swoim obszarze roboczym. Jest to bar<PERSON><PERSON> funkcja, która omija wbudowane zabezpieczenia i kontrole spójności (szczególnie wokół używania narzędzi), więc bądź ostrożny!"}, "createModeDialog": {"title": "Utwórz nowy tryb", "close": "Zamknij", "name": {"label": "Nazwa", "placeholder": "Wprowadź nazwę trybu"}, "slug": {"label": "Slug", "description": "Slug jest używany w adresach URL i nazwach plików. Powinien być małymi literami i zawierać tylko litery, liczby i myślniki."}, "saveLocation": {"label": "Lokalizacja zapisu", "description": "<PERSON><PERSON><PERSON><PERSON>, g<PERSON><PERSON> ten tryb. Tryby specyficzne dla projektu mają pierwszeństwo przed trybami globalnymi.", "global": {"label": "Globalny", "description": "Dostępny we wszystkich obszarach roboczych"}, "project": {"label": "Specyficzny dla projektu (.roomodes)", "description": "Dostępny tylko w tym obszarze roboczym, ma pierwszeństwo przed globalnym"}}, "roleDefinition": {"label": "Definicja roli", "description": "Zdefiniuj wiedzę specjalistyczną i osobowość Roo dla tego trybu."}, "tools": {"label": "Dostępne narzędzia", "description": "<PERSON><PERSON><PERSON><PERSON>, których narzędzi może używać ten tryb."}, "customInstructions": {"label": "Niestandardowe instrukcje (opcjonalne)", "description": "Dodaj wytyczne dotyczące zachowania specyficzne dla tego trybu."}, "buttons": {"cancel": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON><PERSON><PERSON> tryb"}, "deleteMode": "<PERSON><PERSON><PERSON> tryb"}, "allFiles": "wszystkie pliki"}