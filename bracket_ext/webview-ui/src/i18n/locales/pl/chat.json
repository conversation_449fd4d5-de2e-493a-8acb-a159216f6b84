{"greeting": "Co Roo może dla Ciebie zrobić?", "task": {"title": "<PERSON><PERSON><PERSON>", "seeMore": "Zobacz więcej", "seeLess": "Zobacz mniej", "tokens": "Tokeny:", "cache": "<PERSON><PERSON><PERSON>ć podręczna:", "apiCost": "Koszt API:", "contextWindow": "Okno kontekstu:", "closeAndStart": "Zamknij zadanie i rozpocznij nowe", "export": "Eksportuj historię zadań", "delete": "<PERSON><PERSON><PERSON> zadanie (Shi<PERSON> + <PERSON><PERSON><PERSON>, aby pomin<PERSON> potwierdzenie)"}, "unpin": "Odepnij", "pin": "Przypnij", "tokenProgress": {"availableSpace": "Dostę<PERSON><PERSON> miej<PERSON>ce: {{amount}} tokenów", "tokensUsed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tokeny: {{used}} z {{total}}", "reservedForResponse": "Zarezerwowane dla odpowiedzi modelu: {{amount}} tokenów"}, "retry": {"title": "Ponów", "tooltip": "Spróbuj ponownie wykonać operację"}, "startNewTask": {"title": "Rozpocznij nowe zadanie", "tooltip": "Rozpocznij nowe zadanie"}, "proceedAnyways": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mimo to", "tooltip": "Kontynuuj podczas wykonywania polecenia"}, "save": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Zapisz zmiany w pliku"}, "reject": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON><PERSON> tę akcję"}, "completeSubtaskAndReturn": "Zakończ podzadanie i wróć", "approve": {"title": "Zatwierdź", "tooltip": "Zatwierdź tę akcję"}, "runCommand": {"title": "Uru<PERSON><PERSON> polecenie", "tooltip": "<PERSON><PERSON><PERSON><PERSON> to polecenie"}, "proceedWhileRunning": {"title": "Kontynuuj podczas wykonywania", "tooltip": "Kontynuuj pomimo ostrzeżeń"}, "resumeTask": {"title": "Wznów zadanie", "tooltip": "Kontynuuj bieżące zadanie"}, "terminate": {"title": "Zakończ", "tooltip": "Zakończ bieżące zadanie"}, "cancel": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Anuluj bieżącą operację"}, "scrollToBottom": "Przewiń do dołu czatu", "aboutMe": "Dzięki najnowszym przełomom w zdolnościach kodowania agentowego, mogę krok po kroku obsługiwać złożone zadania związane z tworzeniem oprogramowania. Dzięki narzędziom, które pozwalają mi tworzyć i edytować pliki, eksplorować złożone projekty, korzystać z przeglądarki i wykonywać polecenia terminala (po udzieleniu zgody), mogę pomagać w sposób wykraczający poza uzupełnianie kodu czy wsparcie techniczne. Mogę nawet używać MCP do tworzenia nowych narzędzi i rozszerzania własnych możliwości.", "selectMode": "<PERSON><PERSON><PERSON><PERSON> tryb interakcji", "selectApiConfig": "<PERSON><PERSON><PERSON><PERSON> konfigurację <PERSON>", "enhancePrompt": "Ulepsz podpowiedź dodatkowym kontekstem", "addImages": "Dodaj obrazy do wiadomości", "sendMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "typeMessage": "<PERSON><PERSON><PERSON> wiadom<PERSON>ść...", "typeTask": "Wpisz swoje zadanie tutaj...", "addContext": "@ aby do<PERSON><PERSON> konte<PERSON>t, / aby z<PERSON><PERSON> tryb", "dragFiles": "prz<PERSON><PERSON><PERSON><PERSON> shift, aby przecią<PERSON><PERSON>ć pliki", "dragFilesImages": "prz<PERSON><PERSON><PERSON><PERSON> shift, aby przec<PERSON><PERSON><PERSON><PERSON><PERSON> pliki/obrazy", "enhancePromptDescription": "Przycisk 'Uleps<PERSON> podpowiedź' pomaga ul<PERSON>, dostar<PERSON><PERSON><PERSON><PERSON> dodatkowy kontekst, wyjaśnienia lub przeformułowania. Spróbuj wpisać prośbę tutaj i kliknij przycisk ponownie, aby <PERSON><PERSON><PERSON>, jak to dzia<PERSON>.", "errorReadingFile": "Błąd odczytu pliku:", "noValidImages": "Nie przetworzono żadnych prawidłowych obrazów", "separator": "Separator", "edit": "Edytuj...", "forNextMode": "dla następnego trybu", "error": "Błąd", "troubleMessage": "Roo ma problemy...", "apiRequest": {"title": "Zapytanie API", "failed": "Zapytanie API nie powiodło się", "streaming": "Zapytanie API...", "cancelled": "Zapytanie API anulowane", "streamingFailed": "Strumieniowanie API nie powiodło się"}, "checkpoint": {"initial": "Początkowy punkt kontrolny", "regular": "Punkt kontrolny", "initializingWarning": "Trwa inicjalizacja punktu kontrolnego... <PERSON><PERSON><PERSON> to trwa zbyt długo, moż<PERSON>z wyłączyć punkty kontrolne w <settingsLink>ustawieniach</settingsLink> i uruchomić zadanie ponownie.", "menu": {"viewDiff": "Zobacz różnice", "restore": "Przywróć punkt kontrolny", "restoreFiles": "P<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pliki", "restoreFilesDescription": "Przywraca pliki Twojego projektu do zrzutu wykonanego w tym punkcie.", "restoreFilesAndTask": "Przywróć pliki i zadanie", "confirm": "Potwierdź", "cancel": "<PERSON><PERSON><PERSON>", "cannotUndo": "<PERSON><PERSON> akcji nie można <PERSON>.", "restoreFilesAndTaskDescription": "Przywraca pliki Twojego projektu do zrzutu wykonanego w tym punkcie i usuwa wszystkie wiadomości po tym punkcie."}, "current": "Bieżący"}, "instructions": {"wantsToFetch": "Roo chce pobrać szczegółowe instrukcje, aby pomóc w bieżącym zadaniu"}, "fileOperations": {"wantsToRead": "Roo chce przeczytać ten plik:", "wantsToReadOutsideWorkspace": "Roo chce przeczytać ten plik poza obszarem roboczym:", "didRead": "<PERSON><PERSON> przeczytał ten plik:", "wantsToEdit": "<PERSON><PERSON> ch<PERSON> ed<PERSON> ten plik:", "wantsToEditOutsideWorkspace": "<PERSON><PERSON> ch<PERSON> edyt<PERSON>ć ten plik poza obszarem roboczym:", "wantsToCreate": "Roo chce utworzyć nowy plik:"}, "directoryOperations": {"wantsToViewTopLevel": "Roo chce zobaczyć pliki najwyższego poziomu w tym katalogu:", "didViewTopLevel": "Roo zobaczył pliki najwyższego poziomu w tym katalogu:", "wantsToViewRecursive": "<PERSON>oo chce rekurencyjnie zobaczyć wszystkie pliki w tym katalogu:", "didViewRecursive": "Roo rekurencyjnie zobaczył wszystkie pliki w tym katalogu:", "wantsToViewDefinitions": "<PERSON><PERSON> chce zobaczyć nazwy definicji kodu źródłowego używane w tym katalogu:", "didViewDefinitions": "<PERSON><PERSON> zobaczył nazwy definicji kodu źródłowego używane w tym katalogu:", "wantsToSearch": "<PERSON><PERSON> chce przeszukać ten katalog w poszukiwaniu <code>{{regex}}</code>:", "didSearch": "<PERSON><PERSON> przeszukał ten katalog w poszukiwaniu <code>{{regex}}</code>:"}, "commandOutput": "Wyjście polecenia", "response": "<PERSON><PERSON><PERSON><PERSON><PERSON>ź", "arguments": "Argumenty", "mcp": {"wantsToUseTool": "Roo chce użyć narzędzia na serwerze MCP {{serverName}}:", "wantsToAccessResource": "Roo chce uzyskać dostęp do zasobu na serwerze MCP {{serverName}}:"}, "modes": {"wantsToSwitch": "<PERSON>oo chce przełączyć się na tryb <code>{{mode}}</code>", "wantsToSwitchWithReason": "<PERSON>oo chce przełączyć się na tryb <code>{{mode}}</code> ponieważ: {{reason}}", "didSwitch": "<PERSON><PERSON> przełączył się na tryb <code>{{mode}}</code>", "didSwitchWithReason": "<PERSON><PERSON> przełączył się na tryb <code>{{mode}}</code> ponieważ: {{reason}}"}, "subtasks": {"wantsToCreate": "<PERSON><PERSON> chce utworzyć nowe podzadanie w trybie <code>{{mode}}</code>:", "wantsToFinish": "Roo chce zakończyć to podzadanie", "newTaskContent": "Instrukcje podzadania", "completionContent": "Podzadanie zakończone", "resultContent": "Wyniki podzadania", "defaultResult": "Proszę kontynuować następne zadanie.", "completionInstructions": "Podzadanie zakończone! Możesz przejrzeć wyniki i zasugerować poprawki lub następne kroki. Jeśli wszystko wygląda dobrze, pot<PERSON><PERSON><PERSON>, aby zwrócić wynik do zadania nadrzędnego."}, "questions": {"hasQuestion": "Roo ma pytanie:"}, "taskCompleted": "Zadanie zakończone", "shellIntegration": {"unavailable": "Integracja powłoki niedostępna", "troubleshooting": "Nadal masz <PERSON>?"}, "powershell": {"issues": "Wygląda na to, że masz problemy z Windows PowerShell, proszę zapoznaj się z tym"}, "autoApprove": {"title": "Automatyczne zatwierdzanie:", "none": "Brak", "description": "Automatyczne zatwierdzanie pozwala Roo Code wykonywać działania bez pytania o pozwolenie. Włącz tylko dla działań, którym w pełni ufasz. Bardziej szczegółowa konfiguracja dostępna w <settingsLink>Ustawieniach</settingsLink>.", "actions": {"readFiles": {"label": "Czytaj pliki i katalogi", "shortName": "Czytanie", "description": "Pozwala na dostęp do odczytu dowolnego pliku na Twoim komputerze."}, "editFiles": {"label": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>i", "shortName": "<PERSON><PERSON><PERSON><PERSON>", "description": "Pozwala na modyfikację dowolnych plików na Twoim komputerze."}, "executeCommands": {"label": "Wykonuj zatwierdzone polecenia", "shortName": "Polecenia", "description": "Pozwala na wykonywanie zatwierdzonych poleceń terminala. <PERSON><PERSON><PERSON><PERSON> to skonfigurować w panelu ustawień."}, "useBrowser": {"label": "Używaj przeglądarki", "shortName": "Przeglądarka", "description": "Pozwala na uruchamianie i interakcję z dowolną stroną internetową w przeglądarce bezinterfejsowej."}, "useMcp": {"label": "Używaj serwerów MCP", "shortName": "MCP", "description": "Pozwala na korzystanie ze skonfigurowanych serwerów MCP, które mogą modyfikować system plików lub wchodzić w interakcje z API."}, "switchModes": {"label": "Przełącz<PERSON> tryby", "shortName": "Tryby", "description": "Pozwala na automatyczne przełączanie między różnymi trybami bez wymagania zatwierdzenia."}, "subtasks": {"label": "Twórz i kończ podzadania", "shortName": "Podzadania", "description": "Pozwala na tworzenie i kończenie podzadań bez wymagania zatwierdzenia."}, "retryRequests": {"label": "Ponów nieudane zapytania", "shortName": "Ponowienia", "description": "Automatycznie ponawia nieudane zapytania API, gdy dostawca zwraca odpowiedź z błędem."}}}, "reasoning": {"thinking": "<PERSON><PERSON><PERSON><PERSON>", "seconds": "{{count}} s"}, "followUpSuggest": {"copyToInput": "Kopiuj do pola wprowadzania (lub Shift + kliknięcie)"}, "announcement": {"title": "Zrób więcej z Zadaniami Bumerang 🪃", "description": "Podziel pracę na podzadania, każde działające w wyspecjalizowanym trybie, takim jak <PERSON>, architect, debug lub trybie niestandardowym.", "learnMore": "Dow<PERSON>z się więcej →", "hideButton": "<PERSON><PERSON><PERSON><PERSON>"}, "browser": {"rooWantsToUse": "Roo chce użyć przeglądarki:", "consoleLogs": "<PERSON><PERSON> k<PERSON>", "noNewLogs": "(Brak nowych logów)", "screenshot": "Zrzut ekranu przeglądarki", "cursor": "kursor", "navigation": {"step": "<PERSON>rok {{current}} z {{total}}", "previous": "Poprzedni", "next": "Następny"}, "sessionStarted": "Sesja przeglądarki rozpoczęta", "actions": {"title": "Akcja przeglądarki: ", "launch": "Uruchom przeglądarkę na {{url}}", "click": "<PERSON><PERSON><PERSON>j ({{coordinate}})", "type": "Wpisz \"{{text}}\"", "scrollDown": "Przewiń w dół", "scrollUp": "Przewiń w górę", "close": "Zamknij przeglądarkę"}}}