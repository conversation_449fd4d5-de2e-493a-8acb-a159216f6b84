{"greeting": "<PERSON><PERSON><PERSON><PERSON>, j<PERSON><PERSON>!", "introduction": "Mogę wykonywać wszelkiego rodzaju zadania dzięki najnowszym osiągnięciom w zakresie możliwości kodowania agentowego i dostępu do narzędzi, które pozwalają mi tworzyć i edytować pliki, eks<PERSON><PERSON><PERSON>ć złożone projekty, kor<PERSON><PERSON>ć z przeglądarki i wykonywać polecenia terminalowe (oczywiście za Twoją zgodą). Mogę nawet używać MCP do tworzenia nowych narzędzi i rozszerzania własnych możliwości.", "notice": "<PERSON><PERSON>, to rozszerzenie potrzebuje dostawcy API.", "start": "Zaczynajmy!", "chooseProvider": "<PERSON><PERSON><PERSON><PERSON>w<PERSON>, aby r<PERSON><PERSON>:", "routers": {"requesty": {"description": "Twój zoptymalizowany router LLM", "incentive": "$1 darmowego kredytu"}, "openrouter": {"description": "Ujednolicony interfejs dla LLMs"}}, "startRouter": "Szybka konfiguracja przez router", "startCustom": "Użyj własnego klucza API", "telemetry": {"title": "<PERSON><PERSON><PERSON><PERSON> ulepszyć Roo Code", "anonymousTelemetry": "Wyślij anonimowe dane o błędach i użyciu, aby pomóc nam w naprawianiu błędów i ulepszaniu rozszerzenia. Nigdy nie są wysyłane żadne kody, teksty ani informacje osobiste.", "changeSettings": "Zawsze możesz to zmienić na dole <settingsLink>ustawień</settingsLink>", "settings": "ustawienia", "allow": "Zezwól", "deny": "Odmów"}, "or": "lub"}