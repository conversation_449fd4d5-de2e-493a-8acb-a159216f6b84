{"title": "Serwery MCP", "done": "<PERSON><PERSON><PERSON>", "description": "<0>Model Context Protocol</0> umożliwia komunikację z lokalnie uruchomionymi serwerami MCP, które zapewniają dodatkowe narzędzia i zasoby rozszerzające możliwości Roo. Możesz korzystać z <1>serwerów stworzonych przez społecz<PERSON>ść</1> lub pop<PERSON>oo o utworzenie nowych narzędzi specyficznych dla twojego przepływu pracy (np. \"dodaj narzędzie, które pobiera najnowszą dokumentację npm\").", "enableToggle": {"title": "Włącz serwery MCP", "description": "<PERSON> włą<PERSON>, <PERSON><PERSON> będzie mógł komunikować się z serwerami MCP w celu uzyskania zaawansowanych funkcji. <PERSON><PERSON><PERSON> nie korzystasz z MCP, mo<PERSON><PERSON><PERSON> to wył<PERSON><PERSON>yć, aby zmniejszyć zużycie tokenów przez Roo."}, "enableServerCreation": {"title": "Włącz tworzenie serwerów MCP", "description": "<PERSON> wł<PERSON>, <PERSON><PERSON> może pomóc w tworzeniu nowych serwerów MCP za pomocą poleceń takich jak \"dodaj nowe narzędzie do...\". <PERSON><PERSON><PERSON> nie potrzebujesz tworzyć serwerów MCP, mo<PERSON><PERSON><PERSON> to wył<PERSON><PERSON>yć, aby zmniejsz<PERSON>ć zużycie tokenów przez Roo."}, "editGlobalMCP": "Edytuj globalne MCP", "editProjectMCP": "Edytuj projektowe MCP", "tool": {"alwaysAllow": "<PERSON><PERSON><PERSON>", "parameters": "Parametry", "noDescription": "Brak opisu"}, "tabs": {"tools": "Narzędzia", "resources": "<PERSON>as<PERSON><PERSON>"}, "emptyState": {"noTools": "Nie znaleziono narzędzi", "noResources": "Nie znaleziono zasobów"}, "networkTimeout": {"label": "Limit c<PERSON>u sieci", "description": "Maksymalny czas oczekiwania na odpowiedzi serwera", "options": {"15seconds": "15 sekund", "30seconds": "30 sekund", "1minute": "1 minuta", "5minutes": "5 minut", "10minutes": "10 minut", "15minutes": "15 minut", "30minutes": "30 minut", "60minutes": "60 minut"}}, "deleteDialog": {"title": "<PERSON><PERSON><PERSON> serwer MCP", "description": "Czy na pewno chcesz usunąć serwer MCP \"{{serverName}}\"? Tej operacji nie można cofnąć.", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Usuń"}, "serverStatus": {"retrying": "Ponowna próba...", "retryConnection": "Ponów połączenie"}}