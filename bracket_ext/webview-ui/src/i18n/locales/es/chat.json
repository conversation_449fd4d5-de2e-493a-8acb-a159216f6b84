{"greeting": "¿Qué puede hacer Roo por ti?", "task": {"title": "Tarea", "seeMore": "<PERSON>er más", "seeLess": "<PERSON>er menos", "tokens": "Tokens:", "cache": "Caché:", "apiCost": "Costo de API:", "contextWindow": "Ventana de contexto:", "closeAndStart": "Cerrar tarea e iniciar una nueva", "export": "Exportar historial de tareas", "delete": "Eliminar tarea (Shift + Clic para omitir confirmación)"}, "unpin": "<PERSON><PERSON><PERSON>", "pin": "<PERSON><PERSON>", "retry": {"title": "Reintentar", "tooltip": "Intenta la operación de nuevo"}, "startNewTask": {"title": "Iniciar nueva tarea", "tooltip": "Comienza una nueva tarea"}, "proceedAnyways": {"title": "Con<PERSON><PERSON><PERSON> de todos modos", "tooltip": "Continuar mientras se ejecuta el comando"}, "save": {"title": "Guardar", "tooltip": "Guardar los cambios del archivo"}, "tokenProgress": {"availableSpace": "Espacio disponible: {{amount}} tokens", "tokensUsed": "Tokens utilizados: {{used}} de {{total}}", "reservedForResponse": "Reservado para respuesta del modelo: {{amount}} tokens"}, "reject": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Re<PERSON>zar esta acción"}, "completeSubtaskAndReturn": "Completar subtarea y regresar", "approve": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Aprobar esta acción"}, "runCommand": {"title": "Ejecutar comando", "tooltip": "Ejecutar este comando"}, "proceedWhileRunning": {"title": "Continuar mientras se ejecuta", "tooltip": "Continuar a pesar de las advertencias"}, "resumeTask": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON> la tarea actual"}, "terminate": {"title": "Terminar", "tooltip": "Terminar la tarea actual"}, "cancel": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Cancelar la operación actual"}, "scrollToBottom": "Desplazarse al final del chat", "aboutMe": "Gracias a los últimos avances en capacidades de codificación agentiva, puedo manejar tareas complejas de desarrollo de software paso a paso. Con herramientas que me permiten crear y editar archivos, explorar proyectos complejos, usar el navegador y ejecutar comandos de terminal (después de tu aprobación), puedo ayudarte de maneras que van más allá de la finalización de código o el soporte técnico. Incluso puedo usar MCP para crear nuevas herramientas y ampliar mis propias capacidades.", "selectMode": "Seleccionar modo de interacción", "selectApiConfig": "Seleccionar configuración de API", "enhancePrompt": "Mejorar el mensaje con contexto adicional", "addImages": "Agregar imágenes al mensaje", "sendMessage": "<PERSON><PERSON><PERSON> men<PERSON>", "typeMessage": "Escribe un mensaje...", "typeTask": "Escribe tu tarea aquí...", "addContext": "@ para agregar contexto, / para cambiar modos", "dragFiles": "mantén shift para arrastrar archivos", "dragFilesImages": "mantén shift para arrastrar archivos/imágenes", "enhancePromptDescription": "El botón 'Mejorar el mensaje' ayuda a mejorar tu petición proporcionando contexto adicional, aclaraciones o reformulaciones. Intenta escribir una petición aquí y haz clic en el botón nuevamente para ver cómo funciona.", "errorReadingFile": "Error al leer el archivo:", "noValidImages": "No se procesaron imágenes válidas", "separator": "Separador", "edit": "Editar...", "forNextMode": "para el siguiente modo", "error": "Error", "troubleMessage": "Roo está teniendo problemas...", "apiRequest": {"title": "Solicitud API", "failed": "Solicitud API falló", "streaming": "Solicitud API...", "cancelled": "Solicitud API cancelada", "streamingFailed": "Transmisión API falló"}, "checkpoint": {"initial": "Punto de control inicial", "regular": "Punto de control", "initializingWarning": "Todavía inicializando el punto de control... Si esto tarda demasiado, puedes desactivar los puntos de control en la <settingsLink>configuración</settingsLink> y reiniciar tu tarea.", "menu": {"viewDiff": "Ver diferencias", "restore": "Restaurar punto de control", "restoreFiles": "Restaurar archivos", "restoreFilesDescription": "Restaura los archivos de tu proyecto a una instantánea tomada en este punto.", "restoreFilesAndTask": "Restaurar archivos y tarea", "confirm": "Confirmar", "cancel": "<PERSON><PERSON><PERSON>", "cannotUndo": "Esta acción no se puede deshacer.", "restoreFilesAndTaskDescription": "Restaura los archivos de tu proyecto a una instantánea tomada en este punto y elimina todos los mensajes posteriores a este punto."}, "current": "Actual"}, "instructions": {"wantsToFetch": "Roo quiere obtener instrucciones detalladas para ayudar con la tarea actual"}, "fileOperations": {"wantsToRead": "<PERSON>oo quiere leer este archivo:", "wantsToReadOutsideWorkspace": "<PERSON>oo quiere leer este archivo fuera del espacio de trabajo:", "didRead": "<PERSON><PERSON> leyó este archivo:", "wantsToEdit": "<PERSON>oo quiere editar este archivo:", "wantsToEditOutsideWorkspace": "<PERSON>oo quiere editar este archivo fuera del espacio de trabajo:", "wantsToCreate": "Roo quiere crear un nuevo archivo:"}, "directoryOperations": {"wantsToViewTopLevel": "Roo quiere ver los archivos de nivel superior en este directorio:", "didViewTopLevel": "Roo vio los archivos de nivel superior en este directorio:", "wantsToViewRecursive": "Roo quiere ver recursivamente todos los archivos en este directorio:", "didViewRecursive": "Roo vio recursivamente todos los archivos en este directorio:", "wantsToViewDefinitions": "Roo quiere ver nombres de definiciones de código fuente utilizados en este directorio:", "didViewDefinitions": "Roo vio nombres de definiciones de código fuente utilizados en este directorio:", "wantsToSearch": "<PERSON><PERSON> quiere buscar en este directorio <code>{{regex}}</code>:", "didSearch": "<PERSON><PERSON> buscó en este directorio <code>{{regex}}</code>:"}, "commandOutput": "Salida del comando", "response": "Respuesta", "arguments": "Argumentos", "mcp": {"wantsToUseTool": "<PERSON>oo quiere usar una herramienta en el servidor MCP {{serverName}}:", "wantsToAccessResource": "<PERSON><PERSON> quiere acceder a un recurso en el servidor MCP {{serverName}}:"}, "modes": {"wantsToSwitch": "<PERSON>oo quiere cambiar a modo <code>{{mode}}</code>", "wantsToSwitchWithReason": "<PERSON>oo quiere cambiar a modo <code>{{mode}}</code> porque: {{reason}}", "didSwitch": "<PERSON><PERSON> cambió a modo <code>{{mode}}</code>", "didSwitchWithReason": "<PERSON><PERSON> cambió a modo <code>{{mode}}</code> porque: {{reason}}"}, "subtasks": {"wantsToCreate": "<PERSON>oo quiere crear una nueva subtarea en modo <code>{{mode}}</code>:", "wantsToFinish": "<PERSON><PERSON> quiere finalizar esta subtarea", "newTaskContent": "Instrucciones de la subtarea", "completionContent": "Subtarea completada", "resultContent": "Resultados de la subtarea", "defaultResult": "Por favor, continúa con la siguiente tarea.", "completionInstructions": "¡Subtarea completada! Puedes revisar los resultados y sugerir correcciones o próximos pasos. Si todo se ve bien, confirma para devolver el resultado a la tarea principal."}, "questions": {"hasQuestion": "<PERSON>oo tiene una pregunta:"}, "taskCompleted": "<PERSON><PERSON> completada", "shellIntegration": {"unavailable": "Integración de shell no disponible", "troubleshooting": "¿Sigues teniendo problemas?"}, "powershell": {"issues": "Parece que estás teniendo problemas con Windows PowerShell, por favor consulta esta"}, "autoApprove": {"title": "Auto-aprobar:", "none": "<PERSON><PERSON><PERSON>", "description": "Auto-aprobar permite a Roo Code realizar acciones sin pedir permiso. Habilita solo para acciones en las que confíes plenamente. Configuración más detallada disponible en <settingsLink>Configuración</settingsLink>.", "actions": {"readFiles": {"label": "Leer archivos y directorios", "shortName": "<PERSON><PERSON>", "description": "Permite acceso para leer cualquier archivo en tu computadora."}, "editFiles": {"label": "Editar archivos", "shortName": "<PERSON><PERSON>", "description": "Permite la modificación de cualquier archivo en tu computadora."}, "executeCommands": {"label": "Ejecutar comandos aprobados", "shortName": "<PERSON><PERSON><PERSON>", "description": "Permite la ejecución de comandos de terminal aprobados. Puedes configurar esto en el panel de configuración."}, "useBrowser": {"label": "Usar el navegador", "shortName": "<PERSON><PERSON><PERSON><PERSON>", "description": "Permite la capacidad de iniciar e interactuar con cualquier sitio web en un navegador sin interfaz."}, "useMcp": {"label": "Usar servidores MCP", "shortName": "MCP", "description": "Permite el uso de servidores MCP configurados que pueden modificar el sistema de archivos o interactuar con APIs."}, "switchModes": {"label": "Cambiar modos", "shortName": "Modos", "description": "Permite el cambio automático entre diferentes modos sin requerir aprobación."}, "subtasks": {"label": "Crear y completar subtareas", "shortName": "Subtareas", "description": "Permite la creación y finalización de subtareas sin requerir aprobación."}, "retryRequests": {"label": "Reintentar solicitudes fallidas", "shortName": "Reintentos", "description": "Reintenta automáticamente las solicitudes API fallidas cuando el proveedor devuelve una respuesta de error."}}}, "reasoning": {"thinking": "Pensando", "seconds": "{{count}}s"}, "followUpSuggest": {"copyToInput": "Copiar a la entrada (o Shift + clic)"}, "announcement": {"title": "Haz más con Tareas <PERSON>g 🪃", "description": "Divide el trabajo en subtareas, cada una ejecutándose en un modo especializado, como code, architect, debug o un modo personalizado.", "learnMore": "<PERSON><PERSON> m<PERSON> →", "hideButton": "<PERSON><PERSON><PERSON><PERSON> anuncio"}, "browser": {"rooWantsToUse": "<PERSON>oo quiere usar el navegador:", "consoleLogs": "Registros de la consola", "noNewLogs": "(No hay nuevos registros)", "screenshot": "Captura de pantalla del navegador", "cursor": "cursor", "navigation": {"step": "Paso {{current}} de {{total}}", "previous": "Anterior", "next": "Siguient<PERSON>"}, "sessionStarted": "Sesión de navegador iniciada", "actions": {"title": "Acción de navegación: ", "launch": "Iniciar <PERSON> en {{url}}", "click": "Clic ({{coordinate}})", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON> \"{{text}}\"", "scrollDown": "<PERSON><PERSON><PERSON><PERSON> hacia abajo", "scrollUp": "<PERSON><PERSON><PERSON><PERSON> hacia arriba", "close": "<PERSON><PERSON><PERSON>"}}}