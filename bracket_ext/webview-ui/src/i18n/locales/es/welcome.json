{"greeting": "¡Hola, soy Roo!", "introduction": "Puedo realizar todo tipo de tareas gracias a los últimos avances en capacidades de codificación agentica y acceso a herramientas que me permiten crear y editar archivos, explorar proyectos complejos, usar el navegador y ejecutar comandos de terminal (con tu permiso, por supuesto). Incluso puedo usar MCP para crear nuevas herramientas y ampliar mis propias capacidades.", "notice": "Para comenzar, esta extensión necesita un proveedor de API.", "start": "¡Vamos!", "chooseProvider": "Elige un proveedor de API para comenzar:", "routers": {"requesty": {"description": "Tu router LLM optimizado", "incentive": "$1 de crédito gratis"}, "openrouter": {"description": "Una interfaz unificada para LLMs"}}, "startRouter": "Configuración rápida a través de un router", "startCustom": "Usa tu propia clave API", "telemetry": {"title": "<PERSON><PERSON><PERSON> a mejorar Roo <PERSON>", "anonymousTelemetry": "Envía datos de uso y errores anónimos para ayudarnos a corregir errores y mejorar la extensión. Nunca se envía código, texto o información personal.", "changeSettings": "Siempre puedes cambiar esto en la parte inferior de la <settingsLink>configuración</settingsLink>", "settings": "configuración", "allow": "<PERSON><PERSON><PERSON>", "deny": "<PERSON><PERSON><PERSON>"}, "or": "o"}