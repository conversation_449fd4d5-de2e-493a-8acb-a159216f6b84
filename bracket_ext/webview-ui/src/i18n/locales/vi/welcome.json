{"greeting": "<PERSON>n ch<PERSON>o, tôi là Roo!", "introduction": "T<PERSON><PERSON> có thể thực hiện nhiều loại nhiệm vụ nhờ vào những đột phá mới nhất trong khả năng lập trình dạng đại lý và quyền truy cập vào các công cụ cho phép tôi tạo & chỉnh sửa tệp, khám phá các dự án phức tạp, sử dụng trình duyệt và thực thi lệnh terminal (với sự cho phép của bạn, tất nhiên). Tôi thậm chí có thể sử dụng MCP để tạo công cụ mới và mở rộng khả năng của mình.", "notice": "<PERSON><PERSON> bắt đầu, ti<PERSON><PERSON> ích mở rộng này cần một nhà cung cấp API.", "start": "<PERSON><PERSON>t đầu thôi!", "chooseProvider": "<PERSON><PERSON><PERSON> một nhà cung cấp API để bắt đầu:", "routers": {"requesty": {"description": "<PERSON><PERSON> định tuyến LLM đư<PERSON>c tối ưu hóa của bạn", "incentive": "$1 tín dụng miễn phí"}, "openrouter": {"description": "<PERSON><PERSON><PERSON> <PERSON> thống nhất cho các LLM"}}, "startRouter": "<PERSON><PERSON><PERSON><PERSON> lậ<PERSON> n<PERSON>h qua bộ định tuyến", "startCustom": "Sử dụng khóa API của riêng bạn", "telemetry": {"title": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>i thi<PERSON>n Roo <PERSON>", "anonymousTelemetry": "Gửi dữ liệu lỗi và sử dụng ẩn danh để giúp chúng tôi sửa lỗi và cải thiện tiện ích mở rộng. Không bao giờ gửi mã, lờ<PERSON> nhắc hoặc thông tin cá nhân.", "changeSettings": "Bạn luôn có thể thay đổi điều này ở cuối phần <settingsLink>cài đặt</settingsLink>", "settings": "cài đặt", "allow": "<PERSON> phép", "deny": "<PERSON><PERSON> chối"}, "or": "hoặc"}