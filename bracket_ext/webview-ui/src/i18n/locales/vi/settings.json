{"common": {"save": "<PERSON><PERSON><PERSON>", "done": "<PERSON><PERSON><PERSON> th<PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "reset": "Đặt lại", "select": "<PERSON><PERSON><PERSON>"}, "header": {"title": "Cài đặt", "saveButtonTooltip": "<PERSON><PERSON><PERSON> thay đổi", "nothingChangedTooltip": "<PERSON><PERSON><PERSON><PERSON> có gì thay đổi", "doneButtonTooltip": "<PERSON><PERSON>y thay đổi chưa lưu và đóng bảng cài đặt"}, "unsavedChangesDialog": {"title": "<PERSON><PERSON> đ<PERSON>i ch<PERSON>a lưu", "description": "Bạn có muốn hủy thay đổi và tiếp tục không?", "cancelButton": "<PERSON><PERSON><PERSON>", "discardButton": "<PERSON><PERSON><PERSON> thay đổi"}, "sections": {"providers": "<PERSON><PERSON><PERSON> cung cấp", "autoApprove": "Tự động phê duyệt", "browser": "<PERSON><PERSON><PERSON><PERSON> / <PERSON><PERSON> dụng máy t<PERSON>h", "checkpoints": "<PERSON><PERSON><PERSON><PERSON> kiểm tra", "notifications": "<PERSON><PERSON><PERSON><PERSON> báo", "contextManagement": "<PERSON><PERSON><PERSON><PERSON> lý ngữ cảnh", "terminal": "Terminal", "advanced": "<PERSON><PERSON><PERSON> cao", "experimental": "<PERSON><PERSON><PERSON> n<PERSON>ng thử nghiệm", "language": "<PERSON><PERSON><PERSON>", "about": "Về Roo Code"}, "autoApprove": {"description": "<PERSON> phép Roo tự động thực hiện các hoạt động mà không cần phê duyệt. Chỉ bật những cài đặt này nếu bạn hoàn toàn tin tưởng AI và hiểu rõ các rủi ro bảo mật liên quan.", "readOnly": {"label": "<PERSON><PERSON><PERSON> phê duyệt các hoạt động chỉ đọc", "description": "<PERSON><PERSON> đư<PERSON><PERSON> bậ<PERSON>, <PERSON><PERSON> sẽ tự động xem nội dung thư mục và đọc tệp mà không yêu cầu bạn nhấp vào nút Phê duyệt.", "outsideWorkspace": {"label": "<PERSON><PERSON> g<PERSON><PERSON> các tệp ngo<PERSON>i không gian làm việc", "description": "<PERSON> phép Roo đọc các tệp bên ngoài không gian làm việc hiện tại mà không yêu cầu phê duy<PERSON>t."}}, "write": {"label": "<PERSON><PERSON><PERSON> phê duyệt các hoạt động ghi", "description": "Tự động tạo và chỉnh sửa tệp mà không cần phê duyệt", "delayLabel": "<PERSON><PERSON><PERSON> hoãn sau khi ghi để cho phép chẩn đoán phát hiện các vấn đề tiềm ẩn", "outsideWorkspace": {"label": "<PERSON><PERSON> g<PERSON><PERSON> các tệp ngo<PERSON>i không gian làm việc", "description": "<PERSON> phép Roo tạo và chỉnh sửa các tệp bên ngoài không gian làm việc hiện tại mà không yêu cầu phê duyệt."}}, "browser": {"label": "<PERSON><PERSON><PERSON> phê duyệt các hành động trình duyệt", "description": "Tự động thực hiện các hành động trình duyệt mà không cần phê duyệt", "note": "Lưu ý: Chỉ áp dụng khi mô hình hỗ trợ sử dụng máy tính"}, "retry": {"label": "<PERSON><PERSON>n thử lại các yêu cầu API thất bại", "description": "Tự động thử lại các yêu cầu API thất bại khi máy chủ trả về phản hồi lỗi", "delayLabel": "<PERSON><PERSON><PERSON> hoãn tr<PERSON><PERSON><PERSON> khi thử lại yêu cầu"}, "mcp": {"label": "<PERSON><PERSON><PERSON> phê du<PERSON>t các công cụ MCP", "description": "Bật tự động phê duyệt các công cụ MCP riêng lẻ trong chế độ xem Máy chủ MCP (yêu cầu cả cài đặt này và hộp kiểm \"<PERSON><PERSON><PERSON> cho phép\" của công cụ)"}, "modeSwitch": {"label": "<PERSON><PERSON><PERSON> phê duyệt chuyển đổi chế độ", "description": "Tự động chuyển đổi giữa các chế độ khác nhau mà không cần phê duyệt"}, "subtasks": {"label": "<PERSON><PERSON><PERSON> phê duyệt việc tạo và hoàn thành các công việc phụ", "description": "<PERSON> phép tạo và hoàn thành các công việc phụ mà không cần phê duy<PERSON>t"}, "execute": {"label": "<PERSON><PERSON><PERSON> phê duyệt các hoạt động thực thi được phép", "description": "Tự động thực thi các lệnh terminal đư<PERSON><PERSON> phép mà không cần phê duyệt", "allowedCommands": "<PERSON><PERSON><PERSON> l<PERSON>nh tự động thực thi đ<PERSON><PERSON><PERSON> phép", "allowedCommandsDescription": "Tiền tố lệnh có thể được tự động thực thi khi \"<PERSON><PERSON><PERSON> phê duyệt các hoạt động thực thi\" đ<PERSON><PERSON><PERSON> bật. Thêm * để cho phép tất cả các lệnh (sử dụng cẩn thận).", "commandPlaceholder": "<PERSON><PERSON><PERSON><PERSON> tiền tố lệnh (ví dụ: 'git ')", "addButton": "<PERSON><PERSON><PERSON><PERSON>"}}, "providers": {"configProfile": "<PERSON><PERSON> sơ cấu hình", "providerDocumentation": "<PERSON><PERSON><PERSON> li<PERSON>u {{provider}}", "description": "<PERSON><PERSON><PERSON> các cấu hình <PERSON> khác nhau để nhanh chóng chuyển đổi giữa các nhà cung cấp và cài đặt.", "apiProvider": "Nhà cung cấp API", "model": "Mẫu", "nameEmpty": "<PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> để trống", "nameExists": "<PERSON><PERSON> tồn tại một hồ sơ với tên này", "deleteProfile": "<PERSON><PERSON><PERSON> h<PERSON> s<PERSON>", "invalidArnFormat": "Định dạng ARN không hợp lệ. <PERSON><PERSON> lòng kiểm tra các ví dụ ở trên.", "enterNewName": "<PERSON><PERSON><PERSON><PERSON> tên mới", "addProfile": "<PERSON><PERSON><PERSON><PERSON> hồ s<PERSON>", "renameProfile": "<PERSON><PERSON><PERSON> tên hồ sơ", "newProfile": "<PERSON><PERSON> sơ cấu hình mới", "enterProfileName": "<PERSON><PERSON><PERSON><PERSON> tên hồ sơ", "createProfile": "<PERSON><PERSON><PERSON> hồ sơ", "cannotDeleteOnlyProfile": "<PERSON><PERSON><PERSON><PERSON> thể x<PERSON>a hồ sơ duy nhất", "searchPlaceholder": "<PERSON><PERSON><PERSON> k<PERSON><PERSON> hồ sơ", "noMatchFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấ<PERSON> hồ sơ phù hợp", "vscodeLmDescription": "API Mô hình Ngôn ngữ VS Code cho phép bạn chạy các mô hình được cung cấp bởi các tiện ích mở rộng khác của VS Code (bao gồm nhưng không giới hạn ở GitHub Copilot). Cách dễ nhất để bắt đầu là cài đặt các tiện ích mở rộng Copilot và Copilot Chat từ VS Code Marketplace.", "awsCustomArnUse": "<PERSON>hập một ARN AWS Bedrock hợp lệ cho mô hình bạn muốn sử dụng. <PERSON><PERSON> dụ về định dạng:", "awsCustomArnDesc": "<PERSON><PERSON><PERSON> bảo rằng vùng trong ARN khớp với vùng AWS đã chọn ở trên.", "openRouterApiKey": "Khóa API OpenRouter", "getOpenRouterApiKey": "Lấy khóa API OpenRouter", "apiKeyStorageNotice": "Khóa API được lưu trữ an toàn trong Bộ lưu trữ bí mật của VSCode", "glamaApiKey": "Khóa API Glama", "getGlamaApiKey": "Lấy khóa API Glama", "useCustomBaseUrl": "Sử dụng URL cơ sở tùy chỉnh", "useHostHeader": "Sử dụng tiêu đề Host tùy chỉnh", "useLegacyFormat": "Sử dụng định dạng API OpenAI cũ", "requestyApiKey": "Khóa API Requesty", "getRequestyApiKey": "Lấy khóa API Requesty", "anthropicApiKey": "Khóa API Anthropic", "getAnthropicApiKey": "Lấy khóa API Anthropic", "deepSeekApiKey": "Khóa API DeepSeek", "getDeepSeekApiKey": "Lấy khóa API DeepSeek", "geminiApiKey": "Khóa API Gemini", "getGeminiApiKey": "Lấy khóa API Gemini", "openAiApiKey": "Khóa API OpenAI", "openAiBaseUrl": "URL cơ sở", "getOpenAiApiKey": "Lấy khóa API OpenAI", "mistralApiKey": "Khóa API Mistral", "getMistralApiKey": "Lấy khóa API Mistral / Codestral", "codestralBaseUrl": "URL cơ sở Codestral (<PERSON><PERSON><PERSON>)", "codestralBaseUrlDesc": "Đặt URL thay thế cho mô hình Codestral.", "awsCredentials": "Thông tin xác thực AWS", "awsProfile": "Hồ sơ AWS", "awsProfileName": "<PERSON><PERSON><PERSON> <PERSON>ồ s<PERSON> AWS", "awsAccessKey": "Khóa truy c<PERSON>p AWS", "awsSecretKey": "<PERSON><PERSON><PERSON><PERSON> b<PERSON> mật AWS", "awsSessionToken": "Token phiên AWS", "awsRegion": "Vùng AWS", "awsCrossRegion": "Sử dụng suy luận liên vùng", "enablePromptCaching": "<PERSON><PERSON><PERSON> bộ nhớ đệm lời nhắc", "enablePromptCachingTitle": "Bật bộ nhớ đệm lời nhắc để cải thiện hiệu suất và giảm chi phí cho các mô hình được hỗ trợ.", "cacheUsageNote": "Lưu ý: <PERSON><PERSON><PERSON> bạn không thấy việc sử dụng bộ nhớ đệm, hãy thử chọn một mô hình khác và sau đó chọn lại mô hình mong muốn của bạn.", "vscodeLmModel": "<PERSON><PERSON> hình ngôn ngữ", "vscodeLmWarning": "Lưu ý: <PERSON><PERSON><PERSON> là tích hợp thử nghiệm và hỗ trợ nhà cung cấp có thể khác nhau. Nếu bạn nhận được lỗi về mô hình không được hỗ trợ, đó là vấn đề từ phía nhà cung cấp.", "googleCloudSetup": {"title": "<PERSON><PERSON> sử dụng Google Cloud Vertex AI, bạn cần:", "step1": "1. <PERSON><PERSON><PERSON> t<PERSON>ho<PERSON>n Google Cloud, kích hoạt Vertex AI API và kích hoạt các mô hình <PERSON> mong muốn.", "step2": "2. Cài đặt Google Cloud CLI và cấu hình thông tin xác thực mặc định của ứng dụng.", "step3": "3. Hoặc tạo tài k<PERSON>n dịch vụ với thông tin xác thực."}, "googleCloudCredentials": "Thông tin xác thực Google Cloud", "googleCloudKeyFile": "Đường dẫn tệp khóa Google Cloud", "googleCloudProjectId": "ID dự án Google Cloud", "googleCloudRegion": "Vùng Google Cloud", "lmStudio": {"baseUrl": "URL cơ sở (t<PERSON><PERSON> ch<PERSON>)", "modelId": "ID mô hình", "speculativeDecoding": "<PERSON>ật gi<PERSON>i mã suy đoán", "draftModelId": "ID mô hình nháp", "draftModelDesc": "<PERSON>ô hình nháp phải từ cùng một họ mô hình để giải mã suy đoán hoạt động chính xác.", "selectDraftModel": "<PERSON><PERSON><PERSON> mô hình nh<PERSON>p", "noModelsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy mô hình nháp nào. <PERSON>ui lòng đảm bảo LM Studio đang chạy với chế độ máy chủ đư<PERSON><PERSON> bật.", "description": "LM Studio cho phép bạn chạy các mô hình cục bộ trên máy tính của bạn. <PERSON><PERSON> biết hướng dẫn về cách bắt đầu, xem <a>hướng dẫn nhanh</a> của họ. Bạn cũng sẽ cần khởi động tính năng <b>máy chủ cục bộ</b> của LM Studio để sử dụng nó với tiện ích mở rộng này. <span>Lưu ý:</span> Roo Code sử dụng các lời nhắc phức tạp và hoạt động tốt nhất với các mô hình Claude. Các mô hình kém mạnh hơn có thể không hoạt động như mong đợi."}, "ollama": {"baseUrl": "URL cơ sở (t<PERSON><PERSON> ch<PERSON>)", "modelId": "ID mô hình", "description": "<PERSON>llama cho phép bạn chạy các mô hình cục bộ trên máy tính của bạn. <PERSON><PERSON> biết hướng dẫn về cách bắt đầu, xem hướng dẫn nhanh của họ.", "warning": "Lưu ý: <PERSON><PERSON> Code sử dụng các lời nhắc phức tạp và hoạt động tốt nhất với các mô hình Claude. Các mô hình kém mạnh hơn có thể không hoạt động như mong đợi."}, "openRouterTransformsText": "<PERSON><PERSON> lời nhắc và chuỗi tin nhắn theo kích thước ngữ cảnh (<a>OpenRouter Transforms</a>)", "unboundApiKey": "Khóa API Unbound", "getUnboundApiKey": "Lấy khóa API Unbound", "humanRelay": {"description": "Không cần khóa API, nhưng người dùng cần giúp sao chép và dán thông tin vào AI trò chuyện web.", "instructions": "Trong quá trình sử dụng, một hộp thoại sẽ xuất hiện và tin nhắn hiện tại sẽ được tự động sao chép vào clipboard. Bạn cần dán chúng vào các phiên bản web của AI (như ChatGPT hoặc Claude), sau đó sao chép phản hồi của AI trở lại hộp thoại và nhấp vào nút xác nhận."}, "openRouter": {"providerRouting": {"title": "<PERSON><PERSON><PERSON> tuyến nhà cung cấp OpenRouter", "description": "OpenRouter chuyển hướng yêu cầu đến các nhà cung cấp tốt nhất hiện có cho mô hình của bạn. <PERSON> mặc định, các yêu cầu được cân bằng giữa các nhà cung cấp hàng đầu để tối đa hóa thời gian hoạt động. <PERSON><PERSON>, bạn có thể chọn một nhà cung cấp cụ thể để sử dụng cho mô hình này.", "learnMore": "<PERSON><PERSON>m hiểu thêm về định tuyến nhà cung cấp"}}, "customModel": {"capabilities": "<PERSON><PERSON>u hình các khả năng và giá cả cho mô hình tương thích OpenAI tùy chỉnh của bạn. H<PERSON>y cẩn thận khi chỉ định khả năng của mô hình, vì chúng có thể ảnh hưởng đến cách Roo Code hoạt động.", "maxTokens": {"label": "<PERSON><PERSON> <PERSON> đầu ra tối đa", "description": "Số lượng token tối đa mà mô hình có thể tạo ra trong một phản hồi. (Chỉ định -1 để cho phép máy chủ đặt số token tối đa.)"}, "contextWindow": {"label": "<PERSON><PERSON><PERSON> th<PERSON><PERSON><PERSON> c<PERSON>a sổ ngữ cảnh", "description": "Tổng số token (đầu vào + đầu ra) mà mô hình có thể xử lý."}, "imageSupport": {"label": "Hỗ trợ hình <PERSON>nh", "description": "<PERSON>ô hình này có khả năng xử lý và hiểu hình ảnh không?"}, "computerUse": {"label": "Sử dụng máy t<PERSON>h", "description": "<PERSON><PERSON> hình này có khả năng tương tác với trình duyệt không? (ví dụ: <PERSON> 3.7 Sonnet)."}, "promptCache": {"label": "<PERSON>ộ nhớ đệm lời nh<PERSON>c", "description": "<PERSON>ô hình này có khả năng lưu trữ lời nhắc trong bộ nhớ đệm không?"}, "pricing": {"input": {"label": "<PERSON><PERSON><PERSON> đ<PERSON>u vào", "description": "Chi phí cho mỗi triệu token trong đầu vào/lời nhắc. Điều này ảnh hưởng đến chi phí gửi ngữ cảnh và hướng dẫn đến mô hình."}, "output": {"label": "<PERSON><PERSON><PERSON> đ<PERSON>u ra", "description": "Chi phí cho mỗi triệu token trong phản hồi của mô hình. Điều này ảnh hưởng đến chi phí của nội dung được tạo ra và hoàn thành."}, "cacheReads": {"label": "<PERSON><PERSON><PERSON> bộ nhớ đệm", "description": "<PERSON> phí cho mỗi triệu token khi đọc từ bộ nhớ đệm. <PERSON><PERSON><PERSON> là giá được tính khi một phản hồi được lưu trong bộ nhớ đệm được truy xuất."}, "cacheWrites": {"label": "<PERSON><PERSON><PERSON> ghi bộ nhớ đệm", "description": "Chi phí cho mỗi triệu token khi ghi vào bộ nhớ đệm. Đ<PERSON>y là giá được tính khi một lời nhắc được lưu vào bộ nhớ đệm lần đầu tiên."}}, "resetDefaults": "Đặt lại về mặc định"}, "rateLimitSeconds": {"label": "<PERSON><PERSON><PERSON><PERSON> hạn tốc độ", "description": "Thời gian tối thiểu giữa các yêu cầu API."}}, "browser": {"enable": {"label": "<PERSON><PERSON><PERSON> công cụ trình du<PERSON>", "description": "<PERSON><PERSON> đ<PERSON><PERSON><PERSON> b<PERSON>, <PERSON><PERSON> có thể sử dụng trình duyệt để tương tác với các trang web khi sử dụng các mô hình hỗ trợ sử dụng máy tính."}, "viewport": {"label": "<PERSON><PERSON><PERSON><PERSON> khung nh<PERSON>n", "description": "<PERSON><PERSON><PERSON> kích thước khung nhìn cho tương tác trình duyệt. <PERSON><PERSON><PERSON><PERSON> này ảnh hưởng đến cách trang web được hiển thị và tương tác.", "options": {"largeDesktop": "<PERSON><PERSON><PERSON> t<PERSON>h để bàn lớn (1280x800)", "smallDesktop": "<PERSON><PERSON><PERSON> t<PERSON>h để bàn nhỏ (900x600)", "tablet": "<PERSON><PERSON><PERSON> (768x1024)", "mobile": "<PERSON> (360x640)"}}, "screenshotQuality": {"label": "<PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON>nh chụp màn hình", "description": "<PERSON>iều chỉnh chất lượng WebP của ảnh chụp màn hình trình duyệt. Gi<PERSON> trị cao hơn cung cấp ảnh chụp màn hình rõ ràng hơn nhưng tăng sử dụng token."}, "remote": {"label": "Sử dụng kết nối trình duyệt từ xa", "description": "Kết nối với trình duyệt Chrome đang chạy với tính năng gỡ lỗi từ xa được bật (--remote-debugging-port=9222).", "urlPlaceholder": "URL tùy chỉnh (ví dụ: http://localhost:9222)", "testButton": "<PERSON><PERSON><PERSON> tra kết n<PERSON>i", "testingButton": "<PERSON><PERSON> kiểm tra...", "instructions": "Nhập địa chỉ DevTools Protocol hoặc để trống để tự động phát hiện các instance Chrome cục bộ. N<PERSON>t <PERSON>ểm tra kết nối sẽ thử URL tùy chỉnh nếu được cung cấp, hoặc tự động phát hiện nếu trường này trống."}}, "checkpoints": {"enable": {"label": "<PERSON><PERSON><PERSON> điểm kiểm tra tự động", "description": "<PERSON><PERSON> đư<PERSON><PERSON> b<PERSON>, <PERSON><PERSON> sẽ tự động tạo các điểm kiểm tra trong quá trình thực hiện nhiệm vụ, g<PERSON><PERSON><PERSON> dễ dàng xem lại các thay đổi hoặc quay lại trạng thái trước đó."}}, "notifications": {"sound": {"label": "<PERSON><PERSON><PERSON> hi<PERSON><PERSON>", "description": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> b<PERSON>, <PERSON><PERSON> sẽ phát hiệu ứng âm thanh cho thông báo và sự kiện.", "volumeLabel": "<PERSON><PERSON>"}, "tts": {"label": "<PERSON><PERSON><PERSON> chuyển văn bản thành giọng nói", "description": "<PERSON><PERSON> đ<PERSON><PERSON><PERSON> bật, <PERSON><PERSON> sẽ đọc to các phản hồi của nó bằng chức năng chuyển văn bản thành giọng nói.", "speedLabel": "<PERSON><PERSON><PERSON>"}}, "contextManagement": {"description": "<PERSON><PERSON><PERSON> soát thông tin nào được đưa vào cửa sổ ngữ cảnh của AI, ảnh hưởng đến việc sử dụng token và chất lượng phản hồi", "openTabs": {"label": "<PERSON><PERSON><PERSON><PERSON> hạn ngữ cảnh tab đang mở", "description": "S<PERSON> lượng tab VSCode đang mở tối đa để đưa vào ngữ cảnh. Gi<PERSON> trị cao hơn cung cấp nhiều ngữ cảnh hơn nhưng tăng sử dụng token."}, "workspaceFiles": {"label": "<PERSON><PERSON><PERSON><PERSON> hạn ngữ cảnh tệp workspace", "description": "<PERSON><PERSON> lượng tệp tối đa để đưa vào chi tiết thư mục làm việc hiện tại. Gi<PERSON> trị cao hơn cung cấp nhiều ngữ cảnh hơn nhưng tăng sử dụng token."}, "rooignore": {"label": "<PERSON><PERSON><PERSON> thị tệp .roo<PERSON><PERSON> trong danh sách và tìm kiếm", "description": "<PERSON><PERSON> đư<PERSON><PERSON> bật, các tệp khớp với mẫu trong .rooignore sẽ được hiển thị trong danh sách với biểu tượng khóa. <PERSON><PERSON> bị tắt, các tệp này sẽ hoàn toàn bị ẩn khỏi danh sách tệp và tìm kiếm."}, "maxReadFile": {"label": "Ngưỡng tự động cắt ngắn khi đọc tệp", "description": "<PERSON>oo đọc số dòng này khi mô hình không chỉ định giá trị bắt đầu/kết thúc. Nếu số này nhỏ hơn tổng số dòng của tệp, Roo sẽ tạo một chỉ mục số dòng của các định nghĩa mã. Trường hợp đặc biệt: -1 chỉ thị Roo đọc toàn bộ tệp (không tạo chỉ mục), và 0 chỉ thị không đọc dòng nào và chỉ cung cấp chỉ mục dòng cho ngữ cảnh tối thiểu. Gi<PERSON> trị thấp hơn giảm thiểu việc sử dụng ngữ cảnh ban đầu, cho phép đọc chính xác các phạm vi dòng sau này. Các yêu cầu có chỉ định bắt đầu/kết thúc rõ ràng không bị giới hạn bởi cài đặt này.", "lines": "dòng", "always_full_read": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> toàn bộ tệp"}}, "terminal": {"outputLineLimit": {"label": "Giới hạn đầu ra terminal", "description": "<PERSON>ố dòng tối đa để đưa vào đầu ra terminal khi thực hiện lệnh. <PERSON><PERSON> vư<PERSON><PERSON> quá, các dòng sẽ bị xóa khỏi phần giữa, tiết kiệm token."}, "shellIntegrationTimeout": {"label": "Thời gian chờ tích hợp shell terminal", "description": "Thời gian tối đa để chờ tích hợp shell khởi tạo trước khi thực hiện lệnh. Đối với người dùng có thời gian khởi động shell dài, giá trị này có thể cần được tăng lên nếu bạn thấy lỗi \"Shell Integration Unavailable\" trong terminal."}}, "advanced": {"diff": {"label": "<PERSON><PERSON><PERSON> chỉnh sửa qua diff", "description": "<PERSON><PERSON> đư<PERSON><PERSON> bật, <PERSON><PERSON> sẽ có thể chỉnh sửa tệp nhanh hơn và sẽ tự động từ chối ghi toàn bộ tệp bị cắt ngắn. Hoạt động tốt nhất với mô hình <PERSON> 3.7 Sonnet mới nhất.", "strategy": {"label": "<PERSON><PERSON><PERSON> diff", "options": {"standard": "<PERSON><PERSON><PERSON><PERSON> (khối đơn)", "multiBlock": "Thử nghiệm: <PERSON><PERSON> đa kh<PERSON>i", "unified": "Th<PERSON> nghiệm: <PERSON><PERSON> thống nh<PERSON>t"}, "descriptions": {"standard": "<PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON> diff tiêu chuẩn áp dụng thay đổi cho một khối mã tại một thời điểm.", "unified": "<PERSON><PERSON><PERSON> lư<PERSON><PERSON> diff thống nhất thực hiện nhiều cách tiếp cận để áp dụng diff và chọn cách tiếp cận tốt nhất.", "multiBlock": "<PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON> diff đa khối cho phép cập nhật nhiều khối mã trong một tệp trong một yêu cầu."}}, "matchPrecision": {"label": "<PERSON><PERSON> ch<PERSON>h x<PERSON>c kh<PERSON>p", "description": "<PERSON><PERSON> trư<PERSON><PERSON> này kiểm soát mức độ chính xác các phần mã phải khớp khi áp dụng diff. G<PERSON><PERSON> trị thấp hơn cho phép khớp linh hoạt hơn nhưng tăng nguy cơ thay thế không chính xác. Sử dụng giá trị dưới 100% với sự thận trọng cao."}}}, "experimental": {"warning": "⚠️", "DIFF_STRATEGY_UNIFIED": {"name": "Sử dụng chiến l<PERSON> diff thống nhất thử nghiệm", "description": "Bật chiến lượ<PERSON> diff thống nhất thử nghiệm. Chiến lược này có thể giảm số lần thử lại do lỗi mô hình nhưng có thể gây ra hành vi không mong muốn hoặc chỉnh sửa không chính xác. Chỉ bật nếu bạn hiểu rõ các rủi ro và sẵn sàng xem xét cẩn thận tất cả các thay đổi."}, "SEARCH_AND_REPLACE": {"name": "Sử dụng công cụ tìm kiếm và thay thế thử nghiệm", "description": "<PERSON><PERSON>t công cụ tìm kiếm và thay thế thử nghiệm, cho ph<PERSON><PERSON> Roo thay thế nhiều phiên bản của một thuật ngữ tìm kiếm trong một yêu cầu."}, "INSERT_BLOCK": {"name": "Sử dụng công cụ chèn nội dung thử nghiệm", "description": "<PERSON><PERSON><PERSON> công cụ chèn nội dung thử nghiệm, cho ph<PERSON><PERSON> <PERSON>oo chèn nội dung tại số dòng cụ thể mà không cần tạo diff."}, "POWER_STEERING": {"name": "Sử dụng chế độ \"power steering\" thử nghiệm", "description": "<PERSON><PERSON> đư<PERSON><PERSON> bật, <PERSON><PERSON> sẽ nhắc nhở mô hình về chi tiết định nghĩa chế độ hiện tại thường xuyên hơn. Điều này sẽ dẫn đến việc tuân thủ chặt chẽ hơn các định nghĩa vai trò và hướng dẫn tùy chỉnh, nhưng sẽ sử dụng nhiều token hơn cho mỗi tin nhắn."}, "MULTI_SEARCH_AND_REPLACE": {"name": "Sử dụng công cụ diff đa khối thử nghiệm", "description": "<PERSON><PERSON> đ<PERSON><PERSON><PERSON> bậ<PERSON>, <PERSON><PERSON> sẽ sử dụng công cụ diff đa khối. <PERSON><PERSON><PERSON>u này sẽ cố gắng cập nhật nhiều khối mã trong tệp trong một yêu cầu."}}, "temperature": {"useCustom": "Sử dụng nhiệt độ tùy chỉnh", "description": "<PERSON><PERSON><PERSON> so<PERSON>t tính ngẫu nhiên trong phản hồi của mô hình.", "rangeDescription": "Gi<PERSON> trị cao hơn làm cho đầu ra ngẫu nhiên hơn, giá trị thấp hơn làm cho nó xác định hơn."}, "modelInfo": {"supportsImages": "Hỗ trợ hình <PERSON>nh", "noImages": "<PERSON>hông hỗ trợ hình ảnh", "supportsComputerUse": "Hỗ trợ sử dụng máy tính", "noComputerUse": "Không hỗ trợ sử dụng máy tính", "supportsPromptCache": "Hỗ trợ bộ nhớ đệm lời nhắc", "noPromptCache": "<PERSON>hông hỗ trợ bộ nhớ đệm lời nhắc", "maxOutput": "<PERSON><PERSON><PERSON> ra tối đa", "inputPrice": "<PERSON><PERSON><PERSON> đ<PERSON>u vào", "outputPrice": "<PERSON><PERSON><PERSON> đ<PERSON>u ra", "cacheReadsPrice": "<PERSON><PERSON><PERSON> bộ nhớ đệm", "cacheWritesPrice": "<PERSON><PERSON><PERSON> ghi bộ nhớ đệm", "enableStreaming": "Bật streaming", "enableR1Format": "<PERSON><PERSON><PERSON> ho<PERSON>t tham số mô hình R1", "enableR1FormatTips": "<PERSON><PERSON><PERSON> k<PERSON>ch hoạt khi sử dụng các mô hình R1 như QWQ, để tránh lỗi 400", "useAzure": "Sử dụng Azure", "azureApiVersion": "Đặt phiên bản API Azure", "gemini": {"freeRequests": "* <PERSON><PERSON><PERSON> phí đến {{count}} yêu cầu mỗi phút. <PERSON><PERSON> đ<PERSON>, thanh to<PERSON> phụ thuộc vào kích thước lời nh<PERSON>c.", "pricingDetails": "<PERSON><PERSON> biết thêm thông tin, xem chi tiết giá.", "billingEstimate": "* Thanh toán là ước tính - chi phí chính xác phụ thuộc vào kích thước lời nhắc."}}, "modelPicker": {"automaticFetch": "Tiện ích mở rộng tự động lấy danh sách mới nhất các mô hình có sẵn trên <serviceLink>{{serviceName}}</serviceLink>. Nếu bạn không chắc chắn nên chọn mô hình nào, Roo Code hoạt động tốt nhất với <defaultModelLink>{{defaultModelId}}</defaultModelLink>. Bạn cũng có thể thử tìm kiếm \"free\" cho các tùy chọn miễn phí hiện có.", "label": "<PERSON><PERSON>", "searchPlaceholder": "<PERSON><PERSON><PERSON>", "noMatchFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả", "useCustomModel": "Sử dụng tùy chỉnh: {{modelId}}"}, "footer": {"feedback": "<PERSON><PERSON><PERSON> bạn có bất kỳ câu hỏi hoặc phản hồi nào, vui lòng mở một vấn đề tại <githubLink>github.com/RooVetGit/Roo-Code</githubLink> hoặc tham gia <redditLink>reddit.com/r/RooCode</redditLink> hoặc <discordLink>discord.gg/roocode</discordLink>", "version": "Roo Code v{{version}}", "telemetry": {"label": "<PERSON> phép báo cáo lỗi và sử dụng ẩn danh", "description": "<PERSON><PERSON><PERSON><PERSON> cải thiện Roo Code bằng cách gửi dữ liệu sử dụng ẩn danh và báo cáo lỗi. Không bao giờ gửi mã, lời nhắc hoặc thông tin cá nhân. <PERSON>em ch<PERSON>h sách bảo mật của chúng tôi để biết thêm chi tiết."}, "settings": {"import": "<PERSON><PERSON><PERSON><PERSON>", "export": "<PERSON><PERSON><PERSON>", "reset": "Đặt lại"}}, "thinkingBudget": {"maxTokens": "Tokens tối đa", "maxThinkingTokens": "Tokens suy nghĩ tối đa"}, "validation": {"apiKey": "Bạn phải cung cấp khóa API hợp lệ.", "awsRegion": "Bạn phải chọn một vùng để sử dụng AWS Bedrock.", "googleCloud": "Bạn phải cung cấp ID dự án và vùng Google Cloud hợp lệ.", "modelId": "Bạn ph<PERSON>i cung cấp ID mô hình hợp lệ.", "modelSelector": "Bạn ph<PERSON>i cung cấp bộ chọn mô hình hợp lệ.", "openAi": "Bạn phải cung cấp URL cơ sở, khóa API và ID mô hình hợp lệ.", "arn": {"invalidFormat": "Định dạng ARN không hợp lệ. <PERSON><PERSON> lòng kiểm tra yêu cầu về định dạng.", "regionMismatch": "Cảnh báo: <PERSON>ù<PERSON> trong ARN của bạn ({{arnRegion}}) không khớp với vùng bạn đã chọn ({{region}}). <PERSON><PERSON><PERSON>u này có thể gây ra vấn đề truy cập. <PERSON><PERSON><PERSON> cung cấp sẽ sử dụng vùng từ ARN."}, "modelAvailability": "ID mô hình ({{modelId}}) bạn đã cung cấp không khả dụng. <PERSON><PERSON> lòng chọn một mô hình khác."}, "placeholders": {"apiKey": "Nhập khóa API...", "profileName": "<PERSON><PERSON><PERSON><PERSON> tên hồ sơ", "accessKey": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON>a truy cập...", "secretKey": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> b<PERSON> mật...", "sessionToken": "Nhập token phiên...", "credentialsJson": "Nhập JSON thông tin xác thực...", "keyFilePath": "<PERSON><PERSON><PERSON><PERSON> đường dẫn tệp khóa...", "projectId": "Nhập ID dự án...", "customArn": "Nhập ARN (vd: arn:aws:bedrock:us-east-1:123456789012:foundation-model/my-model)", "baseUrl": "Nhập URL cơ sở...", "modelId": {"lmStudio": "vd: meta-llama-3.1-8b-instruct", "lmStudioDraft": "vd: lmstudio-community/llama-3.2-1b-instruct", "ollama": "vd: llama3.1"}, "numbers": {"maxTokens": "vd: 4096", "contextWindow": "vd: 128000", "inputPrice": "vd: 0.0001", "outputPrice": "vd: 0.0002", "cacheWritePrice": "vd: 0.00005"}}, "defaults": {"ollamaUrl": "Mặc định: http://localhost:11434", "lmStudioUrl": "Mặc định: http://localhost:1234", "geminiUrl": "Mặc định: https://generativelanguage.googleapis.com"}, "labels": {"customArn": "ARN tùy chỉnh", "useCustomArn": "Sử dụng ARN tùy chỉnh..."}}