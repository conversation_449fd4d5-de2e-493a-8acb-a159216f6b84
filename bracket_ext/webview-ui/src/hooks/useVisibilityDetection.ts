import { useState, useEffect, useCallback } from 'react';
import { Node, useReactFlow } from '@xyflow/react';

/**
 * Hook to detect which nodes are currently visible in the viewport
 * This helps optimize rendering by only rendering nodes that are visible
 */
export function useVisibilityDetection(nodes: Node[], padding: number = 100) {
  const [visibleNodeIds, setVisibleNodeIds] = useState<Set<string>>(new Set());
  const { getViewport } = useReactFlow();

  // Calculate which nodes are visible in the current viewport
  const calculateVisibleNodes = useCallback(() => {
    const viewport = getViewport();
    const { x, y, zoom } = viewport;

    // Calculate the visible area with padding
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Convert viewport coordinates to flow coordinates
    const topLeft = {
      x: -x / zoom - padding,
      y: -y / zoom - padding
    };

    const bottomRight = {
      x: (-x + viewportWidth) / zoom + padding,
      y: (-y + viewportHeight) / zoom + padding
    };

    // Check which nodes are in the visible area
    const visible = new Set<string>();

    nodes.forEach(node => {
      const { position, width = 0, height = 0 } = node;
      const nodeWidth = typeof width === 'number' ? width : 200; // Default width if not specified
      const nodeHeight = typeof height === 'number' ? height : 100; // Default height if not specified

      // Check if the node is in the visible area
      if (
        position.x + nodeWidth >= topLeft.x &&
        position.x <= bottomRight.x &&
        position.y + nodeHeight >= topLeft.y &&
        position.y <= bottomRight.y
      ) {
        visible.add(node.id);
      }
    });

    setVisibleNodeIds(visible);
  }, [nodes, getViewport, padding]);

  // Update visible nodes when viewport changes
  useEffect(() => {
    // Initial calculation
    calculateVisibleNodes();

    // Add event listener for viewport changes
    const onViewportChange = () => {
      calculateVisibleNodes();
    };

    window.addEventListener('resize', onViewportChange);

    // Clean up
    return () => {
      window.removeEventListener('resize', onViewportChange);
    };
  }, [calculateVisibleNodes]);

  // Function to check if a node is visible
  const isNodeVisible = useCallback((nodeId: string) => {
    return visibleNodeIds.has(nodeId);
  }, [visibleNodeIds]);

  return {
    visibleNodeIds,
    isNodeVisible,
    calculateVisibleNodes
  };
}

/**
 * Hook to detect the current zoom level and provide appropriate level of detail
 */
export function useZoomLevelOfDetail() {
  const [detailLevel, setDetailLevel] = useState<'high' | 'medium' | 'low'>('medium');
  const { getViewport } = useReactFlow();

  // Update detail level based on zoom
  const updateDetailLevel = useCallback(() => {
    const { zoom } = getViewport();

    if (zoom > 1.2) {
      setDetailLevel('high');
    } else if (zoom > 0.6) {
      setDetailLevel('medium');
    } else {
      setDetailLevel('low');
    }
  }, [getViewport]);

  // Update detail level when zoom changes
  useEffect(() => {
    // Initial update
    updateDetailLevel();

    // Add event listener for viewport changes
    const onViewportChange = () => {
      updateDetailLevel();
    };

    window.addEventListener('resize', onViewportChange);

    // Clean up
    return () => {
      window.removeEventListener('resize', onViewportChange);
    };
  }, [updateDetailLevel]);

  return {
    detailLevel,
    updateDetailLevel
  };
}
