import { convertToMentionPath } from "../path-mentions"

describe("path-mentions", () => {
	describe("convertToMentionPath", () => {
		it("should convert an absolute path to a mention path when it starts with cwd", () => {
			// Windows-style paths
			expect(convertToMentionPath("C:\\Users\\<USER>\\project\\file.txt", "C:\\Users\\<USER>\\project")).toBe(
				"@/file.txt",
			)

			// Unix-style paths
			expect(convertToMentionPath("/Users/<USER>/project/file.txt", "/Users/<USER>/project")).toBe("@/file.txt")
		})

		it("should handle paths with trailing slashes in cwd", () => {
			expect(convertToMentionPath("/Users/<USER>/project/file.txt", "/Users/<USER>/project/")).toBe("@/file.txt")
		})

		it("should be case-insensitive when matching paths", () => {
			expect(convertToMentionPath("/Users/<USER>/Project/file.txt", "/users/user/project")).toBe("@/file.txt")
		})

		it("should return the original path when cwd is not provided", () => {
			expect(convertToMentionPath("/Users/<USER>/project/file.txt")).toBe("/Users/<USER>/project/file.txt")
		})

		it("should return the original path when it does not start with cwd", () => {
			expect(convertToMentionPath("/Users/<USER>/project/file.txt", "/Users/<USER>/project")).toBe(
				"/Users/<USER>/project/file.txt",
			)
		})

		it("should normalize backslashes to forward slashes", () => {
			expect(convertToMentionPath("C:\\Users\\<USER>\\project\\subdir\\file.txt", "C:\\Users\\<USER>\\project")).toBe(
				"@/subdir/file.txt",
			)
		})

		it("should handle nested paths correctly", () => {
			expect(convertToMentionPath("/Users/<USER>/project/nested/deeply/file.txt", "/Users/<USER>/project")).toBe(
				"@/nested/deeply/file.txt",
			)
		})
	})
})
