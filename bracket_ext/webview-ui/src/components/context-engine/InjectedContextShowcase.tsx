import React, { useState, useEffect, useRef } from 'react';
import { ChevronDown, ChevronRight, FileIcon, Code2, Search } from 'lucide-react';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';

// Import the VSCode API wrapper
import { vscode } from '../../utils/vscode';

// Get the images base URI from the window object
const getImagesBaseUri = () => {
  const w = window as any;
  return w.IMAGES_BASE_URI || '';
};

/**
 * Interface for function details to be displayed in the showcase
 */
export interface ContextFunction {
  name: string;
  file_path?: string;
  start_line?: number;
  end_line?: number;
  function_text?: string;
  domain?: string;
  relevance?: number;
  is_file?: boolean; // Flag to indicate if this is a file rather than a function
}

interface InjectedContextShowcaseProps {
  functions: ContextFunction[];
  className?: string;
}

/**
 * Component to showcase the context functions injected by the Context Engine
 */
const InjectedContextShowcase: React.FC<InjectedContextShowcaseProps> = ({
  functions,
  className
}) => {
  const [isExpanded, setIsExpanded] = useState(true);

  // Listen for the bracketStarting message to collapse the showcase
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      const message = event.data;
      if (message.type === 'action' && message.action === 'rooCodeStarting') {
        // Collapse the showcase when Bracket starts
        console.log('InjectedContextShowcase: Received rooCodeStarting message, collapsing showcase');
        setIsExpanded(false);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredFunctions, setFilteredFunctions] = useState<ContextFunction[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);

  // Get the images base URI once when the component mounts
  const [imagesBaseUri] = useState(getImagesBaseUri);

  // We're using the vscode API wrapper imported from '../../utils/vscode'

  // Filter functions based on search query
  useEffect(() => {
    // If functions array is empty, set filtered functions to empty array
    if (!functions || functions.length === 0) {
      setFilteredFunctions([]);
      return;
    }

    if (!searchQuery.trim()) {
      setFilteredFunctions(functions);
      return;
    }

    const filtered = functions.filter(func => {
      const searchLower = searchQuery.toLowerCase();
      return (
        func.name.toLowerCase().includes(searchLower) ||
        (func.file_path && func.file_path.toLowerCase().includes(searchLower)) ||
        (func.domain && func.domain.toLowerCase().includes(searchLower))
      );
    });

    setFilteredFunctions(filtered);
  }, [searchQuery, functions]);

  // Group functions by file path
  const functionsByFile: Record<string, ContextFunction[]> = {};
  filteredFunctions.forEach(func => {
    const filePath = func.file_path || 'Unknown';
    if (!functionsByFile[filePath]) {
      functionsByFile[filePath] = [];
    }
    functionsByFile[filePath].push(func);
  });

  // Calculate average relevance score for each file
  const fileScores: Record<string, number> = {};
  Object.keys(functionsByFile).forEach(filePath => {
    const functions = functionsByFile[filePath];
    const totalScore = functions.reduce((sum, func) => sum + (func.relevance || 0), 0);
    fileScores[filePath] = functions.length > 0 ? totalScore / functions.length : 0;
  });

  // Get file paths and sort them by average relevance score (highest to lowest)
  const filePaths = Object.keys(functionsByFile).sort((a, b) => fileScores[b] - fileScores[a]);

  // Track expanded file sections
  const [expandedFiles, setExpandedFiles] = useState<Record<string, boolean>>(
    filePaths.reduce((acc, path) => ({ ...acc, [path]: true }), {})
  );

  // Toggle expansion of a file section
  const toggleFileExpansion = (filePath: string) => {
    setExpandedFiles(prev => ({
      ...prev,
      [filePath]: !prev[filePath]
    }));
  };

  // Extract the file name from the path
  const getFileName = (filePath: string) => {
    const parts = filePath.split('/');
    return parts[parts.length - 1];
  };

  // Get the directory part of the path - simplified to show only relative path
  const getDirectoryPath = (filePath: string) => {
    // If path contains /Users/<USER>/work/startup/godzilla/test/gitlab/gitlab/, remove it
    const gitlabPrefix = '/Users/<USER>/work/startup/godzilla/test/gitlab/gitlab/';
    if (filePath.startsWith(gitlabPrefix)) {
      return filePath.substring(gitlabPrefix.length).split('/').slice(0, -1).join('/');
    }

    // For other paths, just show the last 2 directories
    const parts = filePath.split('/');
    if (parts.length > 3) {
      return parts.slice(-3, -1).join('/');
    }
    return filePath.substring(0, filePath.length - getFileName(filePath).length - 1);
  };

  return (
    <div
      ref={containerRef}
      className={cn("mb-4 bg-[var(--vscode-editor-background)] shadow-sm overflow-hidden transition-all duration-200", className)}
    >
      {/* Header */}
      <div className="flex flex-col">
        <div
          className={`flex items-center justify-between p-3 cursor-pointer bg-[#1F948F15] ${isExpanded ? 'border-b' : 'rounded-md'} border border-[var(--vscode-panel-border)]`}
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <div className="flex items-center gap-2">
            <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center overflow-hidden">
              <img
                src={`${imagesBaseUri}/bracket_logo.svg`}
                alt="Bracket Logo"
                className="w-4 h-4 object-contain object-center"
                // onLoad={() => console.log('[CONTEXT-SHOWCASE-DEBUG] Bracket logo loaded successfully from', `${imagesBaseUri}/bracket_logo.svg`)}
                onError={(e) => {
                  // Log the error for debugging
                  // console.error('[CONTEXT-SHOWCASE-DEBUG] Error loading Bracket logo from', `${imagesBaseUri}/bracket_logo.svg`);

                  // Fallback to a simple colored div if the image fails to load
                  const target = e.target as HTMLImageElement;
                  const parent = target.parentElement;
                  if (parent) {
                    // Replace the img with a colored div
                    // console.log('[CONTEXT-SHOWCASE-DEBUG] Replacing img with colored div');
                    const div = document.createElement('div');
                    div.className = 'w-4 h-4 bg-teal-500 rounded-sm';
                    parent.replaceChild(div, target);
                  }
                }}
              />
            </div>
            <span className="font-medium text-[var(--vscode-titleBar-activeForeground)]">Bracket Auto-Injected Context</span>
            <span className="ml-1 px-2 py-0.5 bg-[#1F948F40] text-[var(--vscode-editor-foreground)] text-xs rounded-full">
              {filePaths.length}
            </span>
          </div>
          <div className="flex items-center">
            <motion.div
              initial={{ rotate: 0 }}
              animate={{ rotate: isExpanded ? 180 : 0 }}
              transition={{ duration: 0.2 }}
            >
              <ChevronDown className="w-4 h-4 text-[var(--vscode-titleBar-activeForeground)]" />
            </motion.div>
          </div>
        </div>

        {/* Search bar */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="border-x border-b border-[var(--vscode-panel-border)] p-2"
            >
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-4 w-4 text-[var(--vscode-input-placeholderForeground)]" />
                </div>
                <input
                  type="text"
                  placeholder="Search files, functions, or domains..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full py-1.5 pl-10 pr-4 bg-[var(--vscode-input-background)] text-[var(--vscode-input-foreground)] border border-[var(--vscode-input-border)] rounded-md focus:outline-none focus:ring-1 focus:ring-[var(--vscode-focusBorder)]"
                />
                {searchQuery && (
                  <button
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setSearchQuery('')}
                  >
                    <span className="text-[var(--vscode-input-placeholderForeground)] hover:text-[var(--vscode-input-foreground)]">×</span>
                  </button>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Content */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="max-h-[400px] overflow-y-auto custom-scrollbar bg-[#1F948F08] border border-[var(--vscode-panel-border)] border-t-0 rounded-b-md"
          >
            <div className="p-2">
              {filePaths.length > 0 ? (
                filePaths.map((filePath) => (
                  <motion.div
                    key={filePath}
                    className="mb-2"
                    initial={{ opacity: 0, y: 5 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    {/* File header */}
                    <div
                      className="flex items-center cursor-pointer py-1.5 px-2 hover:bg-[#1F948F15] rounded transition-colors duration-150 group"
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleFileExpansion(filePath);
                      }}
                    >
                      <motion.div
                        className="mr-1"
                        initial={{ rotate: 0 }}
                        animate={{ rotate: expandedFiles[filePath] ? 90 : 0 }}
                        transition={{ duration: 0.2 }}
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleFileExpansion(filePath);
                          // Prevent the parent's click handler from being called again
                          return false;
                        }}
                      >
                        <ChevronRight className="w-4 h-4 text-[var(--vscode-foreground)]" />
                      </motion.div>
                      <div
                        className="flex-1 flex items-center cursor-pointer group-hover:text-[#1F948F]"
                        onClick={(e) => {
                          e.stopPropagation();
                          // Send message to extension to open file
                          // Ensure the file path is properly formatted
                          const normalizedPath = filePath.startsWith('/') ? filePath : `/${filePath}`;
                          console.log(`[CONTEXT-SHOWCASE-DEBUG] Sending openFile message for file: ${filePath}`);
                          console.log(`[CONTEXT-SHOWCASE-DEBUG] Normalized path: ${normalizedPath}`);
                          vscode.postMessage({
                            type: 'openFile',
                            command: 'openFile',
                            filePath: normalizedPath,
                            startLine: 1,
                            endLine: 1
                          });
                          // Prevent the click from toggling expansion
                          return false;
                        }}
                      >
                        <FileIcon className="w-4 h-4 mr-2 text-[var(--vscode-symbolIcon-fileIcon)] group-hover:text-[#1F948F]" />
                        <div className="flex flex-col sm:flex-row sm:items-center text-[var(--vscode-editor-foreground)]">
                          <span className="font-medium text-[var(--vscode-symbolIcon-fileIcon)] group-hover:text-[#1F948F] flex items-center">
                            {getFileName(filePath)}
                          </span>
                          <span className="sm:ml-2 text-[var(--vscode-descriptionForeground)] text-xs group-hover:text-[#1F948F80]">
                            {getDirectoryPath(filePath) ? getDirectoryPath(filePath) : ''}
                          </span>
                        </div>
                      </div>
                      <div
                        className="ml-auto text-xs text-[var(--vscode-editor-foreground)] bg-[#1F948F20] px-1.5 py-0.5 rounded"
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleFileExpansion(filePath);
                          // Prevent the parent's click handler from being called again
                          return false;
                        }}
                      >
                        {functionsByFile[filePath].length}
                      </div>
                    </div>

                    {/* Function list */}
                    <AnimatePresence>
                      {expandedFiles[filePath] && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: 'auto', opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.2 }}
                          className="ml-6 pl-2 border-l border-[var(--vscode-panel-border)]"
                        >
                          {functionsByFile[filePath].map((func) => (
                            <motion.div
                              key={`${func.file_path || 'Unknown'}-${func.name}-${func.start_line || 0}`}
                              className="py-1.5 px-2 hover:bg-[#1F948F15] rounded cursor-pointer flex items-center group transition-colors duration-150"
                              title={func.is_file ?
                                `File: ${func.name}` :
                                `Function: ${func.name} (Lines ${func.start_line || '?'}-${func.end_line || '?'})`}
                              initial={{ opacity: 0, x: -5 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ duration: 0.2 }}
                              onClick={() => {
                                // Send message to extension to open file at specific location
                                if (func.file_path) {
                                  // Ensure the file path is properly formatted
                                  const normalizedPath = func.file_path.startsWith('/') ? func.file_path : `/${func.file_path}`;
                                  console.log(`[CONTEXT-SHOWCASE-DEBUG] Sending openFile message for function: ${func.name}`);
                                  console.log(`[CONTEXT-SHOWCASE-DEBUG] File path: ${func.file_path}, Normalized: ${normalizedPath}`);
                                  console.log(`[CONTEXT-SHOWCASE-DEBUG] Lines: ${func.start_line || 1}-${func.end_line || 1}`);
                                  vscode.postMessage({
                                    type: 'openFile',
                                    command: 'openFile',
                                    filePath: normalizedPath,
                                    startLine: func.start_line || 1,
                                    endLine: func.end_line || 1,
                                    functionName: func.name
                                  });
                                } else {
                                  console.log(`[CONTEXT-SHOWCASE-DEBUG] Cannot open file: function ${func.name} has no file_path`);
                                }
                              }}
                            >
                              {func.is_file ? (
                                <FileIcon className="w-4 h-4 mr-2 text-[var(--vscode-symbolIcon-fileIcon)] group-hover:text-[#1F948F]" />
                              ) : (
                                <Code2 className="w-4 h-4 mr-2 text-[var(--vscode-symbolIcon-methodForeground)] group-hover:text-[#1F948F]" />
                              )}
                              <span className="text-[var(--vscode-editor-foreground)] group-hover:text-[#1F948F] flex items-center">
                                {func.is_file ?
                                  (func.name.includes('/') ? func.name.split('/').pop() : func.name) :
                                  (func.name.includes('.') ? func.name.split('.').pop() : func.name)
                                }
                              </span>
                              {func.relevance && (
                                <span className="ml-auto text-xs px-1.5 py-0.5 rounded bg-[#1F948F30] text-[var(--vscode-editor-foreground)]">
                                  {(func.relevance / 10).toFixed(1)}
                                </span>
                              )}
                            </motion.div>
                          ))}
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.div>
                ))
              ) : (
                <div className="flex flex-col items-center justify-center py-8 text-[var(--vscode-descriptionForeground)]">
                  <Search className="w-12 h-12 mb-2 opacity-30" />
                  <p className="text-center">
                    {searchQuery ? 'No matching items found' : 'No context items available'}
                  </p>
                  {searchQuery && (
                    <button
                      className="mt-2 text-[var(--vscode-button-foreground)] bg-[var(--vscode-button-background)] px-3 py-1 rounded text-sm hover:bg-[var(--vscode-button-hoverBackground)] transition-colors duration-150"
                      onClick={() => setSearchQuery('')}
                    >
                      Clear search
                    </button>
                  )}
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
    // </div>
  // );
};

export default InjectedContextShowcase;
