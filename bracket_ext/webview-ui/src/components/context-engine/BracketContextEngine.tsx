import React, { useState, useEffect } from 'react';
import { Check, ChevronDown, ChevronRight, Loader2, Brain } from 'lucide-react';
import { Markdown } from '../ui/markdown/Markdown';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';

import { ContextFunction } from './InjectedContextShowcase';

export interface ContextEngineResult {
  content: string;
  isSuccess?: boolean;
  isLoading?: boolean;
  contextFunctions?: ContextFunction[];
  // Track if this is a reasoning step
  isReasoningStep?: boolean;
}

interface BracketContextEngineProps {
  result?: ContextEngineResult;
  className?: string;
  isEnabled: boolean;
}

const BracketContextEngine: React.FC<BracketContextEngineProps> = ({
  result,
  className,
  isEnabled
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [showPulse, setShowPulse] = useState(false);

  // Add a pulsing effect when new content is received
  useEffect(() => {
    if (result?.isLoading) {
      setShowPulse(true);
      const timer = setTimeout(() => setShowPulse(false), 1000);
      return () => clearTimeout(timer);
    }
  }, [result?.content, result?.isLoading]);

  if (!isEnabled || !result) return null;

  // Determine if this is a reasoning step based on content or explicit flag
  const isReasoningStep = result.isReasoningStep ||
    (result.isLoading && (result.content.includes('Step') || result.content.includes('Analyzing')));

  return (
    <motion.div
      initial={{ opacity: 0.8, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={cn(
        "border rounded-md mb-4 bg-[var(--vscode-editor-background)]",
        showPulse ? "border-blue-500" : "",
        className
      )}
    >
      <div
        className="flex items-center justify-between p-2 cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center gap-2">
          {result.isSuccess && <Check className="text-green-500 w-4 h-4" />}
          {isReasoningStep && <Brain className="text-blue-500 w-4 h-4" />}
          <span className="font-medium">Bracket Context Engine</span>
          {isReasoningStep && <span className="text-xs text-blue-500 italic">Reasoning</span>}
        </div>
        <div className="flex items-center gap-2">
          {result.isLoading && <Loader2 className="w-4 h-4 animate-spin text-blue-500" />}
          {isExpanded ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
        </div>
      </div>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className="border-t overflow-hidden"
          >
            <div className="p-3 relative">
              {result.isLoading && (
                <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-purple-500 animate-pulse"></div>
              )}
              <div className="prose prose-sm max-w-none augment-style max-h-[500px] overflow-auto text-[var(--vscode-editor-foreground)]">
                <Markdown content={result.content} />
              </div>

              {/* Add a subtle animation for reasoning steps */}
              {isReasoningStep && result.isLoading && (
                <div className="mt-4 flex items-center justify-center text-blue-500 text-sm">
                  <span className="mr-2">Analyzing codebase structure</span>
                  <div className="flex space-x-1">
                    <motion.div
                      animate={{ opacity: [0.4, 1, 0.4] }}
                      transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
                      className="w-1 h-1 bg-blue-500 rounded-full"
                    />
                    <motion.div
                      animate={{ opacity: [0.4, 1, 0.4] }}
                      transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut", delay: 0.2 }}
                      className="w-1 h-1 bg-blue-500 rounded-full"
                    />
                    <motion.div
                      animate={{ opacity: [0.4, 1, 0.4] }}
                      transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut", delay: 0.4 }}
                      className="w-1 h-1 bg-blue-500 rounded-full"
                    />
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default BracketContextEngine;
