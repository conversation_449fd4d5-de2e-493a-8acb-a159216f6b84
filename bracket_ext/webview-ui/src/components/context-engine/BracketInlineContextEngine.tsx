import React from 'react';
import { Check, Database } from 'lucide-react';
import { Markdown } from '../ui/markdown/Markdown';
import { cn } from '@/lib/utils';

export interface ContextEngineBlockProps {
  id?: string;
  content: string;
  isComplete?: boolean;
  isLoading?: boolean;
  className?: string;
}

/**
 * A component that displays a single context engine block inline in the chat
 */
export const ContextEngineBlock: React.FC<ContextEngineBlockProps> = ({
  content,
  isComplete = false,
  isLoading = false,
  className
}) => {
  return (
    <div className={cn(
      "flex items-start gap-2 p-3 rounded-md border border-[var(--vscode-editorWidget-border)] bg-[var(--vscode-editorWidget-background)] mb-4",
      isComplete ? "border-l-green-500 border-l-2" : "border-l-blue-500 border-l-2",
      className
    )}>
      <div className="flex-shrink-0 mt-1">
        {isComplete ? (
          <div className="w-5 h-5 rounded-full bg-green-500 flex items-center justify-center">
            <Check className="w-3 h-3 text-white" />
          </div>
        ) : (
          <Database className="w-5 h-5 text-blue-500" />
        )}
      </div>
      <div className="flex-grow min-w-0">
        <div className="flex items-center gap-2 mb-1">
          <span className="text-xs font-medium text-[var(--vscode-editor-foreground)]">Bracket Context Engine</span>
          {isLoading && (
            <div className="flex space-x-1 ml-1">
              <div className="w-1 h-1 bg-blue-500 rounded-full animate-pulse"></div>
              <div className="w-1 h-1 bg-blue-500 rounded-full animate-pulse delay-150"></div>
              <div className="w-1 h-1 bg-blue-500 rounded-full animate-pulse delay-300"></div>
            </div>
          )}
        </div>
        <div className="prose prose-sm max-w-none augment-style text-[var(--vscode-editor-foreground)]">
          <Markdown content={content} />
        </div>
      </div>
    </div>
  );
};

export interface BracketInlineContextEngineProps {
  blocks: ContextEngineBlockProps[];
  className?: string;
}

/**
 * A component that displays multiple context engine blocks inline in the chat
 */
const BracketInlineContextEngine: React.FC<BracketInlineContextEngineProps> = ({
  blocks,
  className
}) => {
  if (!blocks || blocks.length === 0) return null;

  return (
    <div className={cn("space-y-2", className)}>
      {blocks.map((block, index) => (
        <ContextEngineBlock
          key={block.id || index}
          id={block.id}
          content={block.content}
          isComplete={block.isComplete}
          isLoading={block.isLoading}
        />
      ))}
    </div>
  );
};

export default BracketInlineContextEngine;
