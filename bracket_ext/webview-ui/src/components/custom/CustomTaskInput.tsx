import React, { useState, useCallback, useRef, useEffect } from "react";
import { useExtensionState } from "../../context/ExtensionStateContext";
import { useAppTranslation } from "../../i18n/TranslationContext";
import { vscode } from "../../utils/vscode";
import { Mode } from "../../../../src/shared/modes";
import { cn } from "@/lib/utils";

interface CustomTaskInputProps {
  inputValue: string;
  setInputValue: (value: string) => void;
  onSend: () => void;
  textAreaDisabled: boolean;
  placeholderText: string;
  mode: Mode;
  setMode: (value: Mode) => void;
}

const CustomTaskInput: React.FC<CustomTaskInputProps> = ({
  inputValue,
  setInputValue,
  onSend,
  textAreaDisabled,
  placeholderText,
  mode,
  setMode,
}) => {
  const { t } = useAppTranslation();
  const {
    autoApprovalEnabled,
    setAutoApprovalEnabled,
    alwaysAllowReadOnly,
    alwaysAllowWrite,
    alwaysAllowExecute,
    alwaysAllowBrowser,
    alwaysAllowSubtasks,
  } = useExtensionState();
  
  const textAreaRef = useRef<HTMLTextAreaElement>(null);
  const [isFocused, setIsFocused] = useState(false);

  // Get enabled actions for display
  const getEnabledActions = () => {
    const actions = [];
    if (alwaysAllowReadOnly) actions.push("Read");
    if (alwaysAllowWrite) actions.push("Edit");
    if (alwaysAllowExecute) actions.push("Commands");
    if (alwaysAllowBrowser) actions.push("Browser");
    if (alwaysAllowSubtasks) actions.push("Subtasks");
    return actions.join(", ");
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey && !textAreaDisabled) {
      e.preventDefault();
      onSend();
    }
  };

  const toggleAutoApproval = useCallback(() => {
    const newValue = !(autoApprovalEnabled ?? false);
    setAutoApprovalEnabled(newValue);
    vscode.postMessage({ type: "autoApprovalEnabled", bool: newValue });
  }, [autoApprovalEnabled, setAutoApprovalEnabled]);

  const handleAgentChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setMode(e.target.value as Mode);
    vscode.postMessage({ type: "mode", text: e.target.value });
  };

  return (
    <div className="custom-task-input">
      {/* Main container with custom styling */}
      <div className="task-input-container">
        {/* Auto-approve section */}
        <div className="auto-approve-section">
          <label className="auto-approve-label">
            <input
              type="checkbox"
              checked={autoApprovalEnabled ?? false}
              onChange={toggleAutoApproval}
              className="auto-approve-checkbox"
            />
            <span className="auto-approve-text">Auto-process:</span>
            <span className="auto-approve-actions">{getEnabledActions() || "None"}</span>
          </label>
        </div>

        {/* Input area */}
        <div className="input-area">
          <textarea
            ref={textAreaRef}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={handleKeyDown}
            disabled={textAreaDisabled}
            placeholder={placeholderText}
            className="custom-textarea"
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            rows={3}
          />
          
          {/* Help text */}
          {!inputValue && (
            <div className="help-text">
              (@ to add context, / to switch modes, hold shift to drag in files/images)
            </div>
          )}
        </div>

        {/* Agent selector */}
        <div className="agent-selector">
          <select 
            value={mode} 
            onChange={handleAgentChange}
            className="agent-dropdown"
          >
            <option value="chat">Assistant</option>
            <option value="code">Agent</option>
          </select>
        </div>
      </div>

      <style>{`
        .custom-task-input {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
          margin-bottom: 10px;
        }
        
        .task-input-container {
          background: #f7f9fc;
          border: 1px solid #e1e4e8;
          border-radius: 8px;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
          overflow: hidden;
        }
        
        .auto-approve-section {
          padding: 8px 12px;
          background: #edf2f7;
          border-bottom: 1px solid #e1e4e8;
        }
        
        .auto-approve-label {
          display: flex;
          align-items: center;
          font-size: 13px;
          color: #4a5568;
          cursor: pointer;
        }
        
        .auto-approve-checkbox {
          margin-right: 8px;
          appearance: none;
          width: 16px;
          height: 16px;
          border: 1px solid #cbd5e0;
          border-radius: 3px;
          background-color: white;
          position: relative;
          cursor: pointer;
        }
        
        .auto-approve-checkbox:checked {
          background-color: #4299e1;
          border-color: #4299e1;
        }
        
        .auto-approve-checkbox:checked::after {
          content: '';
          position: absolute;
          left: 5px;
          top: 2px;
          width: 5px;
          height: 9px;
          border: solid white;
          border-width: 0 2px 2px 0;
          transform: rotate(45deg);
        }
        
        .auto-approve-text {
          font-weight: 500;
          margin-right: 6px;
        }
        
        .auto-approve-actions {
          color: #718096;
          font-size: 12px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 200px;
        }
        
        .input-area {
          position: relative;
          padding: 12px 15px;
        }
        
        .custom-textarea {
          width: 100%;
          border: none;
          background: transparent;
          resize: none;
          font-size: 14px;
          line-height: 1.5;
          color: #2d3748;
          outline: none;
          padding: 0;
        }
        
        .custom-textarea::placeholder {
          color: #a0aec0;
        }
        
        .help-text {
          position: absolute;
          bottom: 12px;
          left: 15px;
          font-size: 11px;
          color: #a0aec0;
          pointer-events: none;
        }
        
        .agent-selector {
          padding: 8px 15px;
          background: #edf2f7;
          border-top: 1px solid #e1e4e8;
        }
        
        .agent-dropdown {
          width: 100%;
          padding: 6px 10px;
          border: 1px solid #cbd5e0;
          border-radius: 4px;
          background-color: white;
          font-size: 13px;
          color: #4a5568;
          appearance: none;
          background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24' fill='none' stroke='%234a5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
          background-repeat: no-repeat;
          background-position: right 10px center;
          cursor: pointer;
        }
        
        .agent-dropdown:focus {
          outline: none;
          border-color: #4299e1;
          box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.15);
        }
      `}</style>
    </div>
  );
};

export default CustomTaskInput;
