import React from "react";
import { useCallback } from "react";
import { useExtensionState } from "../../context/ExtensionStateContext";
import { useAppTranslation } from "../../i18n/TranslationContext";
import { vscode } from "../../utils/vscode";
import CustomTaskInput from "./CustomTaskInput";
import "./CustomTaskInput.css";

// Import the original ChatView component to extend it
import ChatView from "../chat/ChatView";

interface CustomChatViewProps {
  isHidden: boolean;
  showAnnouncement: boolean;
  hideAnnouncement: () => void;
  showHistoryView: () => void;
}

const CustomChatView: React.FC<CustomChatViewProps> = (props) => {
  const { t } = useAppTranslation();
  const { mode, setMode } = useExtensionState();
  const [inputValue, setInputValue] = React.useState("");
  const [textAreaDisabled, setTextAreaDisabled] = React.useState(false);

  const handleSendMessage = useCallback(
    (text: string) => {
      text = text.trim();
      if (text) {
        vscode.postMessage({
          type: "newTask",
          text,
          images: [],
        });
        setInputValue("");
      }
    },
    []
  );

  // Only show our custom input when there's no active task
  const shouldShowCustomInput = !props.isHidden && window.location.search.includes("custom=true");

  if (shouldShowCustomInput) {
    return (
      <div
        style={{
          position: "fixed",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          display: "flex",
          flexDirection: "column",
          overflow: "hidden",
          padding: "20px",
          backgroundColor: "#f8f9fa",
        }}
      >
        <div style={{ flex: 1 }}>
          {/* This is where the main content would go */}
          <div style={{ 
            display: "flex", 
            flexDirection: "column", 
            alignItems: "center", 
            justifyContent: "center", 
            height: "100%" 
          }}>
            <div style={{ 
              maxWidth: "600px", 
              width: "100%", 
              textAlign: "center",
              marginBottom: "40px"
            }}>
              <h1 style={{ 
                fontSize: "28px", 
                marginBottom: "16px",
                color: "#2d3748"
              }}>
                Welcome to the Assistant
              </h1>
              <p style={{ 
                fontSize: "16px", 
                lineHeight: "1.6",
                color: "#4a5568"
              }}>
                Type your task below to get started. The assistant can help you with coding tasks, 
                answer questions, and provide guidance on your projects.
              </p>
            </div>
          </div>
        </div>
        
        <div style={{ marginTop: "auto" }}>
          <CustomTaskInput
            inputValue={inputValue}
            setInputValue={setInputValue}
            onSend={() => handleSendMessage(inputValue)}
            textAreaDisabled={textAreaDisabled}
            placeholderText={t("chat:typeTask")}
            mode={mode}
            setMode={setMode}
          />
        </div>
      </div>
    );
  }

  // Otherwise, render the original ChatView
  return <ChatView {...props} />;
};

export default CustomChatView;
