import React, { useState, useEffect } from "react"
import { vscode } from "../../utils/vscode"
import { Button } from "../ui/button"
import { motion } from "framer-motion"
import { ChevronDown, Code2, Activity, Server, Shield, Layers } from "lucide-react"
import { cn } from "@/lib/utils"
import "./SuggestedQuestionsPanel.css"

interface SuggestedQuestionsPanelProps {
  className?: string
  onQuestionClick?: (question: string) => void
}



// Define category structure with icons
interface CategoryInfo {
  name: string;
  icon: React.ReactNode;
}

const SuggestedQuestionsPanel: React.FC<SuggestedQuestionsPanelProps> = ({ className, onQuestionClick }) => {
  const [showQuestions, setShowQuestions] = useState(false)

  // Define categories with their icons
  const categories: Record<string, CategoryInfo> = {
    "Architecture": { name: "Architecture", icon: <Code2 className="category-icon" /> },
    "Monitoring": { name: "Monitoring", icon: <Activity className="category-icon" /> },
    "Infrastructure": { name: "Infrastructure", icon: <Server className="category-icon" /> },
    "Security": { name: "Security", icon: <Shield className="category-icon" /> },
    "Integration": { name: "Integration", icon: <Layers className="category-icon" /> }
  }

  // Organize questions by category
  // const questionsByCategory: Record<string, string[]> = {
  //   "Architecture": [
  //     "How do the administration area and analytics reporting modules share data while maintaining performance isolation?",
  //     "What architectural mechanisms ensure consistent error handling and failure recovery across all subdomains while maintaining appropriate audit trails for regulatory compliance in distributed deployment scenarios?"
  //   ],
  //   "Monitoring": [
  //     "How do monitoring and alerting features integrate with cluster management for real-time insights?",
  //     "What triggers alerts when container registry performance degrades, and how does the system correlate these performance metrics with CI/CD pipeline execution data to identify root causes across project boundaries?"
  //   ],
  //   "Infrastructure": [
  //     "What mechanisms coordinate vulnerability scanning results with Kubernetes deployments while respecting namespace RBAC and GitLab permissions?",
  //     "What design patterns unify request handling across the core framework and data cleanup modules while ensuring transaction integrity during database partitioning?"
  //   ],
  //   "Security": [
  //     "How do AccessSecurity and WebSecurity modules unify with AuditCompliance for threat detection?",
  //     "What security measures protect the credential synchronization service during cross-domain operations, and how does the architecture prevent privilege escalation when integrating with external identity providers in multi-tenant environments?"
  //   ],
  //   "Integration": [
  //     "How do API gateways handle authentication and authorization across distributed microservices while maintaining consistent rate limiting and audit logging?",
  //     "What ensures data consistency between the event bus and persistent storage?"
  //   ]
  // }

const questionsByCategory: Record<string, string[]> = 
{
  "Architecture": [
    "How do the core agent and cluster agent components share telemetry data while maintaining performance isolation across different collection pipelines?",
    "What architectural mechanisms ensure consistent error handling and failure recovery across all agent components (core, trace, process, security) while maintaining appropriate audit trails for compliance in distributed Kubernetes deployment scenarios?"
  ],
  "Monitoring": [
    "How do agent telemetry and self-monitoring features integrate with cluster-level observability for real-time insights into agent health?",
    "What triggers alerts when DogStatsD performance degrades, and how does the system correlate these performance metrics with trace collection and log processing data to identify root causes across different agent subsystems?"
  ],
  "Infrastructure": [
    "What mechanisms coordinate security agent scanning results with container runtime monitoring while respecting Kubernetes RBAC and namespace isolation?",
    "What design patterns unify metric collection across the core framework and check execution modules while ensuring data integrity during agent restarts and configuration reloads?"
  ],
  "Security": [
    "How do the Security Agent and Runtime Security modules unify with Compliance monitoring for threat detection and policy enforcement?",
    "What security measures protect the remote configuration service during cross-cluster operations, and how does the architecture prevent privilege escalation when integrating with external authentication providers in multi-tenant Kubernetes environments?"
  ],
  "Integration": [
    "How do APM trace ingestion and metrics collection handle authentication and authorization across distributed agent deployments while maintaining consistent rate limiting and audit logging?",
    "What ensures data consistency between the event platform intake and persistent telemetry storage during network partitions?"
  ]
}
  
  // Map domains to their descriptions for the content sections
  const domainDescriptions: Record<string, string> = {
    "Administration & Analytics": "This domain addresses administrative tools (e.g., user or data management) and analytical insights (e.g., usage reports, performance metrics). It integrates with the \"Data Integration Hub,\" which aggregates and routes data among subdomains. Admin features can command system-wide changes, feeding back into analytics to help guide decisions. Reporting modules apply aggregated information to produce dashboards and logs, feeding also into the \"Shared Error Aggregator\" when issues arise."
  }

  // Show questions after a short delay
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowQuestions(true);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const handleQuestionClick = (question: string) => {
    if (onQuestionClick) {
      onQuestionClick(question)
    } else {
      // If no click handler is provided, copy the question to the input field
      vscode.postMessage({
        type: "action",
        action: "focusInput"
      })

      // Set the input value to the question
      vscode.postMessage({
        type: "action",
        action: "setChatBoxMessage",
        text: question
      })
    }
  }

  // Get the images base URI once when the component mounts
  const [imagesBaseUri] = useState(() => {
    const w = window as any;
    return w.IMAGES_BASE_URI || "";
  });

  // If not ready to show, don't render anything
  if (!showQuestions) {
    return null;
  }

  return (
    <div className={cn("suggested-questions-panel", className)}>
      <div className="panel-container">
        <div className="panel-header">
          <div className="header-content">
            <div className="header-icon-container">
              <img
                src={`${imagesBaseUri}/bracket_logo.svg`}
                alt="Bracket Logo"
                className="header-icon"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  const parent = target.parentElement;
                  if (parent) {
                    const div = document.createElement('div');
                    div.className = 'w-4 h-4 bg-teal-500 rounded-sm';
                    parent.replaceChild(div, target);
                  }
                }}
              />
            </div>
            <h3 className="header-title">Explore your Codebase</h3>
          </div>
        </div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
          className="panel-content"
        >
          {/* Domain description section */}
          {/* <div className="domain-description-section"> */}
            {/* <h2 className="domain-title">2.1 Administration & Analytics</h2> */}
            {/* <p className="domain-description">{domainDescriptions["Administration & Analytics"]}</p> */}
            {/* <button className="show-more-button" onClick={() => {}}> */}
              {/* <span>Show More</span> */}
              {/* <ChevronDown size={16} /> */}
            {/* </button> */}
          {/* </div> */}

          {/* Categories and questions */}
          <div className="categories-container">
            {Object.entries(categories).map(([categoryKey, category], categoryIndex) => (
              <motion.div
                key={categoryKey}
                className="category-section"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: categoryIndex * 0.1 }}
              >
                <div className="category-header">
                  {category.icon}
                  <span className="category-name">{category.name}</span>
                </div>

                <div className="questions-list">
                  {questionsByCategory[categoryKey].map((question, questionIndex) => (
                    <motion.div
                      key={`${categoryKey}-${questionIndex}`}
                      className="question-card-wrapper"
                      initial={{ opacity: 0, y: 5 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.2, delay: questionIndex * 0.05 }}
                    >
                      <Button
                        variant="ghost"
                        className={cn("question-card", categoryKey.toLowerCase())}
                        onClick={() => handleQuestionClick(question)}
                      >
                        <div className="question-content">
                          <div className="question-indicator"></div>
                          <span className="question-text">{question}</span>
                        </div>
                      </Button>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default SuggestedQuestionsPanel
