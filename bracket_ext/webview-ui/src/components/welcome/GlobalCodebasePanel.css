.global-codebase-panel {
  border-radius: 8px;
  background-color: var(--vscode-editor-background);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--vscode-panel-border);
}

/* Panel Header Styling */
.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(to right, rgba(31, 148, 143, 0.3), rgba(35, 148, 139, 0.2));
  border-radius: 6px;
  border: 1px solid var(--vscode-panel-border);
  transition: background-color 0.2s ease;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-grow: 1;
  min-width: 0;
}

.header-icon-container {
  flex-shrink: 0;
  background-color: rgba(31, 148, 143, 0.5);
  border-radius: 50%;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-icon {
  width: 20px;
  height: 20px;
  color: #FFFFFF;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--vscode-editor-foreground);
  margin: 0;
}

.global-codebase-panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 16px;
  display: flex;
  flex-direction: column;
}

.beautiful-content {
  font-size: 14px;
  line-height: 1.6;
}

.post-analysis-container {
  background-color: rgba(31, 148, 143, 0.05);
  border: 1px solid rgba(31, 148, 143, 0.1);
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  margin: 0 auto;
}

.global-codebase-panel-content::-webkit-scrollbar {
  width: 6px;
}

.global-codebase-panel-content::-webkit-scrollbar-track {
  background: transparent;
}

.global-codebase-panel-content::-webkit-scrollbar-thumb {
  background-color: var(--vscode-scrollbarSlider-background);
  border-radius: 3px;
}

.global-codebase-panel-content::-webkit-scrollbar-thumb:hover {
  background-color: var(--vscode-scrollbarSlider-hoverBackground);
}

/* Codebase summary with expand/collapse */
.codebase-summary {
  margin-bottom: 24px;
  padding: 0;
  position: relative;
  padding-bottom: 16px;
}

.codebase-content {
  position: relative;
  overflow: hidden;
  transition: max-height 0.3s ease;
  padding: 8px 0;
}

.expand-toggle-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  width: auto;
  padding: 6px 12px;
  margin-top: 16px;
  margin-left: auto;
  margin-right: auto;
  background-color: rgba(31, 148, 143, 0.05);
  border: 1px solid rgba(31, 148, 143, 0.2);
  border-radius: 4px;
  color: #1F948F;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.expand-toggle-button:hover {
  background-color: rgba(31, 148, 143, 0.1);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.expand-toggle-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(31, 148, 143, 0.3);
}

.expand-toggle-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Empty state styling */
.empty-state {
  color: var(--vscode-descriptionForeground);
  background-color: rgba(31, 148, 143, 0.05);
  border: 1px solid rgba(31, 148, 143, 0.1);
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  max-width: 600px;
  margin: 0 auto;
}

.empty-state:hover {
  box-shadow: 0 4px 12px rgba(31, 148, 143, 0.1);
  transform: translateY(-1px);
}

.bracket-logo {
  color: #1F948F;
  padding: 20px;
  width: 140px;
  height: 140px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  margin: 0 auto 20px;
  position: relative;
}

.bracket-logo:hover {
  transform: scale(1.05);
}

.bracket-logo-image {
  width: 120px;
  height: 120px;
  filter: drop-shadow(0 2px 4px rgba(31, 148, 143, 0.3));
  animation: float 3s ease-in-out infinite, pulse3d 5s ease-in-out infinite;
}

/* Animations */
.animate-pulse-subtle {
  animation: pulse-subtle 3s infinite ease-in-out;
}

@keyframes pulse-subtle {
  0% { transform: scale(1); }
  50% { transform: scale(1.03); }
  100% { transform: scale(1); }
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

@keyframes pulse3d {
  0% { filter: drop-shadow(0 2px 4px rgba(31, 148, 143, 0.3)); }
  50% { filter: drop-shadow(0 8px 16px rgba(31, 148, 143, 0.5)); }
  100% { filter: drop-shadow(0 2px 4px rgba(31, 148, 143, 0.3)); }
}

.animate-fade-in {
  animation: fade-in 0.8s ease-out forwards;
  opacity: 0;
}

.animate-fade-in-delayed {
  animation: fade-in 0.8s ease-out 0.3s forwards;
  opacity: 0;
}

@keyframes fade-in {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Generate button styling */
.generate-button {
  margin-top: 8px !important;
}

.primary-action-button {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background-color: #1F948F !important;
  color: white !important;
  border-radius: 6px !important;
  padding: 10px 20px !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  transition: all 0.2s ease !important;
  border: none !important;
  box-shadow: 0 4px 8px rgba(31, 148, 143, 0.3) !important;
  position: relative !important;
  overflow: hidden !important;
  animation: none !important;
  opacity: 1 !important;
  letter-spacing: 0.3px !important;
  margin-top: 0 !important;
  min-width: 200px !important;
  visibility: visible !important;
}

.generate-button:hover:not([disabled]) {
  background-color: #17706C !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 3px 6px rgba(31, 148, 143, 0.25) !important;
}

.primary-action-button:hover:not([disabled]) {
  background-color: #17706C !important;
  transform: translateY(-3px) !important;
  box-shadow: 0 6px 12px rgba(31, 148, 143, 0.4) !important;
}

.generate-button:active:not([disabled]) {
  transform: translateY(0) !important;
  box-shadow: 0 1px 3px rgba(31, 148, 143, 0.2) !important;
}

.primary-action-button:active:not([disabled]) {
  transform: translateY(0) !important;
  box-shadow: 0 2px 4px rgba(31, 148, 143, 0.3) !important;
}

.generate-button:disabled {
  background-color: rgba(31, 148, 143, 0.5) !important;
  cursor: not-allowed !important;
}

.primary-action-button:disabled {
  background-color: rgba(31, 148, 143, 0.5) !important;
  cursor: not-allowed !important;
  opacity: 0.8 !important;
}

.generate-button::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: -100% !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent) !important;
  transition: all 0.4s ease !important;
}

.primary-action-button::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: -100% !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent) !important;
  transition: all 0.4s ease !important;
}

.generate-button:hover:not([disabled])::before {
  left: 100% !important;
}

.primary-action-button:hover:not([disabled])::before {
  left: 100% !important;
}

.standard-button {
  transition: all 0.2s ease;
}

.standard-button:hover:not([disabled]) {
  background-color: #17706C !important;
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(31, 148, 143, 0.4);
}

.standard-button:active:not([disabled]) {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(31, 148, 143, 0.3);
}

.standard-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

@keyframes button-fade-in {
  from { opacity: 0; transform: translateY(6px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Fix for codicon fallback */
.codicon-fallback {
  display: none;
}

/* Show fallback if codicon font fails to load */
.codicon:empty .codicon-fallback {
  display: inline-block;
}

.generate-button:active:not([disabled]) {
  transform: translateY(1px);
}

.generate-button[disabled] {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Streaming content placeholder */
.streaming-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 20px;
  color: var(--vscode-descriptionForeground);
  background-color: rgba(31, 148, 143, 0.02);
  border-radius: 8px;
  margin: 16px 0;
}

/* Loading indicator for questions */
.questions-loading {
  margin-top: 24px;
  border: 1px solid rgba(31, 148, 143, 0.2);
  border-radius: 8px;
  overflow: hidden;
  background-color: var(--vscode-editor-background);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
  color: var(--vscode-descriptionForeground);
  background: linear-gradient(to right, rgba(31, 148, 143, 0.05), rgba(35, 148, 139, 0.02));
  font-size: 14px;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Augment-style markdown */
.augment-style {
  font-size: 14px;
  line-height: 1.6;
}

/* Streaming markdown specific styles */
.streaming-markdown {
  position: relative;
  min-height: 100px;
  animation: fadeIn 0.3s ease-in-out;
}

.streaming-markdown::after {
  content: "";
  position: absolute;
  bottom: 0;
  right: 0;
  width: 3px;
  height: 18px;
  background-color: #1F948F;
  animation: blink 1s infinite;
  border-radius: 1px;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

@keyframes fadeIn {
  from { opacity: 0.7; }
  to { opacity: 1; }
}

.global-codebase-panel .prose {
  color: var(--vscode-editor-foreground);
  max-width: none;
}

.global-codebase-panel .prose h1 {
  font-size: 1.7em;
  margin-top: 1em;
  margin-bottom: 0.5em;
  font-weight: 600;
  color: var(--vscode-editor-foreground);
}

.global-codebase-panel .prose h2 {
  font-size: 1.5em;
  margin-top: 1em;
  margin-bottom: 0.5em;
  font-weight: 600;
  color: var(--vscode-editor-foreground);
  border-bottom: none;
  padding-bottom: 0;
}

.global-codebase-panel .prose h3,
.global-codebase-panel .prose h4,
.global-codebase-panel .prose h5,
.global-codebase-panel .prose h6 {
  font-size: 1.3em;
  margin-top: 1em;
  margin-bottom: 0.5em;
  font-weight: 600;
  color: var(--vscode-editor-foreground);
}

.global-codebase-panel .prose a {
  color: var(--vscode-textLink-foreground);
  text-decoration: none;
}

.global-codebase-panel .prose a:hover {
  text-decoration: underline;
}

.global-codebase-panel .prose p {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
  font-size: 1.05em;
}

.global-codebase-panel .prose code {
  background-color: #ffffff;
  color: #0451a5;
  padding: 1px 4px;
  border-radius: 3px;
  font-family: inherit;
  font-size: inherit;
  font-weight: 600;
}

.global-codebase-panel .prose pre {
  background-color: var(--vscode-textCodeBlock-background);
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 0.8em 0;
}

.global-codebase-panel .prose pre code {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
  color: var(--vscode-textCodeBlock-foreground);
}

.global-codebase-panel .prose ul,
.global-codebase-panel .prose ol {
  padding-left: 1.5em;
  margin: 0.5em 0;
}

.global-codebase-panel .prose li {
  margin: 0.3em 0;
}

.global-codebase-panel .prose blockquote {
  border-left: 3px solid var(--vscode-textBlockQuote-border);
  padding-left: 1em;
  margin: 0.8em 0;
  color: var(--vscode-textBlockQuote-foreground);
}

.refresh-button {
  background-color: transparent;
  border: none;
  color: #1F948F;
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.refresh-button:hover {
  background-color: rgba(31, 148, 143, 0.1);
}

.global-codebase-panel .prose table {
  border-collapse: collapse;
  width: 100%;
  margin: 1em 0;
}

.global-codebase-panel .prose th,
.global-codebase-panel .prose td {
  border: 1px solid var(--vscode-panel-border);
  padding: 6px 8px;
}

.global-codebase-panel .prose th {
  background-color: var(--vscode-list-hoverBackground);
  font-weight: 600;
}
