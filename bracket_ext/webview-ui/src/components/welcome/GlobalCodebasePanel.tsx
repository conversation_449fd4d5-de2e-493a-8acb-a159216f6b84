import React, { useState, useEffect, useRef } from "react"
import { useExtensionState } from "../../context/ExtensionStateContext"
import { Markdown } from "../ui/markdown/Markdown"
import { StreamingMarkdown } from "../ui/markdown/StreamingMarkdown"
import { useAppTranslation } from "../../i18n/TranslationContext"
import { VSCodeButton } from "@vscode/webview-ui-toolkit/react"
import { vscode } from "../../utils/vscode"
import SuggestedQuestionsPanel from "./SuggestedQuestionsPanel"
import { ChevronDown, ChevronUp, Code, HelpCircle, Loader2 } from "lucide-react"
import "./GlobalCodebasePanel.css"

// Get the images base URI from the window object
const getImagesBaseUri = () => {
  const w = window as any;
  return w.IMAGES_BASE_URI || '';
};

interface GlobalCodebasePanelProps {
  className?: string
}

// No sample questions - we'll use dynamically generated questions only

const GlobalCodebasePanel: React.FC<GlobalCodebasePanelProps> = ({ className }) => {
  const { codebaseExplanation, suggestedQuestions } = useExtensionState()
  const { t } = useAppTranslation()
  const [isExpanded, setIsExpanded] = useState(false)
  const [truncatedContent, setTruncatedContent] = useState<string>("")
  const [fullContent, setFullContent] = useState<string>("")
  const [isGenerating, setIsGenerating] = useState(false)
  const [isStreamingOverview, setIsStreamingOverview] = useState(false)
  const [streamedContent, setStreamedContent] = useState<string>("")
  const [questionsLoaded, setQuestionsLoaded] = useState(false)
  const contentRef = useRef<HTMLDivElement>(null)

  // Get the images base URI once when the component mounts
  const [imagesBaseUri] = useState(getImagesBaseUri)

  // Force empty state on initial load to ensure Run Analysis button is visible
  const [forceEmptyState, setForceEmptyState] = useState(true)

  // Log the state for debugging
  useEffect(() => {
    console.log('forceEmptyState:', forceEmptyState)
    console.log('codebaseExplanation:', codebaseExplanation)
    console.log('isStreamingOverview:', isStreamingOverview)
  }, [forceEmptyState, codebaseExplanation, isStreamingOverview])

  // Process codebase explanation to create truncated and full versions
  useEffect(() => {
    if (codebaseExplanation) {
      // Disable the force empty state once we have real content and user has clicked Run Analysis
      if (!isGenerating && forceEmptyState) {
        return; // Don't process content if we're forcing empty state
      }

      setFullContent(codebaseExplanation)
      setIsGenerating(false)
      setIsStreamingOverview(false)

      // Start with collapsed view when content is fully loaded
      setIsExpanded(false)

      // Create a truncated version (approximately 300-400 tokens)
      // A simple approach is to take the first few paragraphs
      const paragraphs = codebaseExplanation.split('\n\n')
      let truncated = paragraphs[0] // Always include the first paragraph

      // Add more paragraphs until we reach approximately 300-400 tokens
      // Assuming average of 1.5 tokens per word
      let wordCount = truncated.split(/\s+/).length
      let i = 1

      while (i < paragraphs.length && wordCount < 200) { // ~300 tokens
        truncated += '\n\n' + paragraphs[i]
        wordCount += paragraphs[i].split(/\s+/).length
        i++
      }

      // Add ellipsis if truncated
      if (i < paragraphs.length) {
        truncated += '\n\n...'
      }

      setTruncatedContent(truncated)
    }
  }, [codebaseExplanation, forceEmptyState, isGenerating])



  // Set questions loaded when codebase explanation is complete
  useEffect(() => {
    // When codebase explanation is complete and not streaming, show the questions panel
    if (codebaseExplanation && !isStreamingOverview && !forceEmptyState) {
      // Add a small delay to ensure the codebase explanation is fully rendered
      const timer = setTimeout(() => {
        setQuestionsLoaded(true);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [codebaseExplanation, isStreamingOverview, forceEmptyState])

  const runCodebaseAnalysis = () => {
    // Disable the force empty state when user clicks Run Analysis
    setForceEmptyState(false)
    setIsGenerating(true)
    setIsStreamingOverview(true)
    setStreamedContent("")
    setQuestionsLoaded(false)

    // Use the runCodebaseAnalysis command which will trigger the LLM-driven analysis
    vscode.postMessage({
      type: "action",
      action: "runCodebaseAnalysis"
    })

    // Show loading state for questions
    console.log("Starting codebase analysis and showing questions loading state")
  }

  // Handle incoming streamed content directly
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      const message = event.data;

      if (message.type === "streamCodebaseOverview" && message.text) {
        setIsStreamingOverview(true); // Ensure streaming state is set

        // Update streamed content immediately
        setStreamedContent(prev => prev + message.text);
      } else if (message.type === "codebaseExplanation" && message.text) {
        // When we receive the final content, update both full and truncated versions
        setIsStreamingOverview(false);
        setFullContent(message.text);

        // Start with collapsed view when content is fully loaded
        setIsExpanded(false);

        // Create a truncated version
        const paragraphs = message.text.split('\n\n');
        let truncated = paragraphs[0] || ""; // Always include the first paragraph

        // Add more paragraphs until we reach approximately 300-400 tokens
        let wordCount = truncated.split(/\s+/).length;
        let i = 1;

        while (i < paragraphs.length && wordCount < 200) { // ~300 tokens
          truncated += '\n\n' + paragraphs[i];
          wordCount += paragraphs[i].split(/\s+/).length;
          i++;
        }

        // Add ellipsis if truncated
        if (i < paragraphs.length) {
          truncated += '\n\n...';
        }

        setTruncatedContent(truncated);

        // Set a small delay before showing the questions panel
        // This ensures the codebase explanation is fully rendered first
        console.log("Codebase explanation complete, preparing to show questions panel");
        setTimeout(() => {
          setQuestionsLoaded(true);
          console.log("Questions panel should now be visible");
        }, 1000);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [])

  const toggleExpand = () => {
    setIsExpanded(!isExpanded)
  }

  // Show empty state if forceEmptyState is true OR if there's no content and we're not streaming
  if (forceEmptyState || (!codebaseExplanation && !isStreamingOverview)) {
    return (
      <div className={`global-codebase-panel p-4 ${className}`}>
        <div className="flex justify-between items-center mb-3">
          <h2 className="text-xl font-medium text-[var(--vscode-editor-foreground)]">{t("welcome:codebasePanel.title")}</h2>
        </div>
        <div className="empty-state flex flex-col items-center justify-center py-5 px-4">
          <div className="bracket-logo">
            <img
              src={`${imagesBaseUri}/bracket_logo.svg`}
              alt="Bracket Logo"
              className="bracket-logo-image"
              onError={(e) => {
                console.error('Error loading Bracket logo:', e);
                // Fallback to a colored div if image fails to load
                const target = e.target as HTMLImageElement;
                const parent = target.parentElement;
                if (parent) {
                  const div = document.createElement('div');
                  div.className = 'w-12 h-12 bg-teal-500 rounded-full flex items-center justify-center text-white text-lg font-bold';
                  div.textContent = 'B';
                  parent.replaceChild(div, target);
                }
              }}
            />
          </div>
          <h3 className="text-center text-xl font-medium mb-3 text-teal-600 animate-fade-in">Welcome to Bracket</h3>
          <p className="text-center text-base mb-4 max-w-md animate-fade-in-delayed">{t("welcome:codebasePanel.noExplanation")}</p>

          <div className="flex flex-col items-center gap-4 mt-6">
            <button
              onClick={runCodebaseAnalysis}
              disabled={isGenerating}
              className="standard-button"
              style={{
                backgroundColor: '#1F948F',
                color: 'white',
                padding: '10px 20px',
                borderRadius: '6px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontWeight: '600',
                boxShadow: '0 4px 8px rgba(31, 148, 143, 0.3)',
                border: 'none',
                fontSize: '16px',
                minWidth: '200px',
                cursor: 'pointer'
              }}
            >
              {isGenerating ? (
                <>
                  <Loader2 className="animate-spin mr-2" size={18} />
                  <span>{t("welcome:codebasePanel.generating")}</span>
                </>
              ) : (
                <>
                  <span className="codicon codicon-rocket mr-2" style={{ fontSize: '18px' }}></span>
                  <span>Index</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    )
  }

  // Show streaming or complete content
  const displayContent = isStreamingOverview ? streamedContent : codebaseExplanation
  const showExpandButton = displayContent && displayContent.split('\n\n').length > 3

  return (
    <div className={`global-codebase-panel p-4 ${className}`}>
      <div className="flex justify-between items-center mb-3">
        <h2 className="text-xl font-medium text-[var(--vscode-editor-foreground)]">{t("welcome:codebasePanel.title")}</h2>
        <VSCodeButton
          appearance="icon"
          onClick={runCodebaseAnalysis}
          title={t("welcome:codebasePanel.refresh")}
          disabled={isGenerating}
          className="refresh-button"
        >
          {isGenerating ? (
            <Loader2 className="animate-spin" size={18} />
          ) : (
            <span className="codicon codicon-refresh"></span>
          )}
        </VSCodeButton>
      </div>
      <div className="post-analysis-container">
        <div className="global-codebase-panel-content prose prose-sm max-w-none augment-style beautiful-content">
        {(displayContent || isStreamingOverview) && (
          <div className="codebase-summary" ref={contentRef}>
            <div className="codebase-content">
              {isStreamingOverview && streamedContent === "" ? (
                <div className="streaming-placeholder">
                  <Loader2 className="animate-spin mb-2" size={24} />
                  <p className="text-center">{t("welcome:codebasePanel.loadingOverview")}</p>
                </div>
              ) : isStreamingOverview ? (
                <StreamingMarkdown content={streamedContent} />
              ) : (
                <Markdown content={isExpanded ? fullContent : truncatedContent} />
              )}
            </div>
            {showExpandButton && (
              <button
                className="expand-toggle-button"
                onClick={toggleExpand}
                aria-expanded={isExpanded}
              >
                {isExpanded ? (
                  <>
                    <span>See less</span>
                    <ChevronUp size={16} />
                  </>
                ) : (
                  <>
                    <span>See more</span>
                    <ChevronDown size={16} />
                  </>
                )}
              </button>
            )}
          </div>
        )}

        {/* Show hardcoded questions panel when codebase explanation is complete */}
        {questionsLoaded && (
          <SuggestedQuestionsPanel />
        )}

        {/* Show loading state for questions */}
        {isGenerating && !questionsLoaded && (
          <div className="questions-loading">
            <div className="panel-header">
              <div className="header-content">
                <div className="header-icon-container">
                  <HelpCircle className="header-icon" size={16} />
                </div>
                <h3 className="header-title">{t("welcome:suggestedQuestions.title")}</h3>
              </div>
            </div>
            <div className="loading-indicator">
              <Loader2 className="animate-spin mb-2" size={20} />
              <p>{t("welcome:suggestedQuestions.loading")}</p>
            </div>
          </div>
        )}
        </div>
      </div>
    </div>
  )
}

export default GlobalCodebasePanel
