.suggested-questions-panel {
  margin-top: 24px;
  padding: 0;
}

.panel-container {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(31, 148, 143, 0.2);
  background-color: var(--vscode-editor-background);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  max-width: 100%;
}

/* Panel Header */
.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(to right, rgba(31, 148, 143, 0.3), rgba(35, 148, 139, 0.2));
  border-bottom: 1px solid rgba(31, 148, 143, 0.2);
  transition: background-color 0.2s ease;
  border-radius: 6px 6px 0 0;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-grow: 1;
  min-width: 0;
  user-select: none;
}

.header-icon-container {
  flex-shrink: 0;
  background-color: rgba(31, 148, 143, 0.5);
  border-radius: 50%;
  padding: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-icon {
  width: 16px;
  height: 16px;
  color: #FFFFFF;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--vscode-editor-foreground);
  margin: 0;
}

/* Panel Content */
.panel-content {
  padding: 16px;
  overflow: hidden;
  background: linear-gradient(to right, rgba(31, 148, 143, 0.05), rgba(35, 148, 139, 0.02));
  border-radius: 0 0 6px 6px;
}

/* Domain description section */
.domain-description-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(31, 148, 143, 0.1);
}

.domain-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--vscode-editor-foreground);
  margin: 0 0 12px 0;
}

.domain-description {
  font-size: 14px;
  line-height: 1.5;
  color: var(--vscode-descriptionForeground);
  margin: 0 0 12px 0;
}

.show-more-button {
  display: flex;
  align-items: center;
  gap: 4px;
  background: none;
  border: none;
  color: #1F948F;
  font-size: 14px;
  padding: 4px 0;
  cursor: pointer;
}

.show-more-button:hover {
  text-decoration: underline;
}

/* Categories container */
.categories-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Category sections */
.category-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.category-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  border-bottom: 1px solid rgba(31, 148, 143, 0.1);
}

.category-icon {
  width: 18px;
  height: 18px;
  color: #1F948F;
}

.category-name {
  font-weight: 600;
  font-size: 16px;
  color: var(--vscode-editor-foreground);
}

/* Questions list */
.questions-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 0;
}

/* Question Card */
.question-card-wrapper {
  width: 100%;
}

.question-card {
  width: 100%;
  text-align: left;
  white-space: normal;
  height: auto;
  padding: 10px 14px;
  line-height: 1.5;
  font-size: 14px;
  border-radius: 6px;
  transition: all 0.2s ease;
  background-color: var(--vscode-editor-background);
  border: 1px solid rgba(31, 148, 143, 0.1);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: flex-start;
}

.question-content {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  flex: 1;
  overflow: hidden;
}

.question-indicator {
  flex-shrink: 0;
  width: 4px;
  height: 100%;
  min-height: 24px;
  border-radius: 2px;
  background-color: #1F948F;
  margin-top: 2px;
}

/* Category-specific colors */
.question-card.architecture .question-indicator {
  background-color: #1F948F; /* Teal */
}

.question-card.monitoring .question-indicator {
  background-color: #8F1F94; /* Purple */
}

.question-card.infrastructure .question-indicator {
  background-color: #1F4E8F; /* Blue */
}

.question-card.security .question-indicator {
  background-color: #8F1F1F; /* Red */
}

.question-card.integration .question-indicator {
  background-color: #1F8F3E; /* Green */
}

.question-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  background-color: rgba(31, 148, 143, 0.05);
}

.question-text {
  display: block;
  width: 100%;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  word-break: break-word;
}

/* No need for responsive adjustments since we're using a vertical layout */
