import React, { useState } from "react"
import { cn } from "@/lib/utils"
import { MessageSquare, Bo<PERSON> } from "lucide-react"
import { useExtensionState } from "@/context/ExtensionStateContext"
import { vscode } from "@/utils/vscode"
import { Mode } from "../../../../src/shared/modes"
import { ChevronDown, ChevronUp } from "lucide-react"

interface NavigationMenuProps {
  className?: string
}

export const NavigationMenu: React.FC<NavigationMenuProps> = ({ className }) => {
  const { mode, setMode } = useExtensionState()
  const [expanded, setExpanded] = useState(false)

  const handleModeChange = (newMode: Mode) => {
    setMode(newMode)
    vscode.postMessage({
      type: "mode",
      text: newMode,
    })
  }

  const toggleExpanded = () => {
    setExpanded(!expanded)
  }

  return (
    <div className={cn("flex flex-col rounded-md shadow-sm border border-[#e0e0e0] dark:border-[#3c3c3c] overflow-hidden w-[120px] bg-white dark:bg-[#1e1e1e] text-left", className)}>
      <div className="flex flex-col">
        {/* Chat option */}
        <button
          className={cn(
            "flex items-center gap-2 px-3 py-1.5 text-sm font-medium hover:bg-[#f5f5f5] dark:hover:bg-[#2a2a2a]",
            mode === "chat" ? "text-[#0078d4] font-semibold" : "text-[#333333] dark:text-[#cccccc]"
          )}
          onClick={() => handleModeChange("chat")}
        >
          <MessageSquare size={16} />
          <span>Chat</span>
          <ChevronDown className="ml-auto" size={14} />
        </button>

        {/* Bracket Agent option */}
        <button
          className={cn(
            "flex items-center gap-2 px-3 py-1.5 text-sm font-medium border-t border-[#e0e0e0] dark:border-[#3c3c3c] hover:bg-[#f5f5f5] dark:hover:bg-[#2a2a2a]",
            mode === "bracket-agent" ? "text-[#0078d4] font-semibold" : "text-[#333333] dark:text-[#cccccc]"
          )}
          onClick={() => handleModeChange("bracket-agent")}
        >
          <Bot size={16} />
          <span>Bracket Agent</span>
        </button>

        {/* Agent Auto option */}
        <button
          className={cn(
            "flex items-center gap-2 px-3 py-1.5 text-sm font-medium border-t border-[#e0e0e0] dark:border-[#3c3c3c] hover:bg-[#f5f5f5] dark:hover:bg-[#2a2a2a]",
            mode === "architect" ? "text-[#0078d4] font-semibold" : "text-[#333333] dark:text-[#cccccc] opacity-70"
          )}
          onClick={() => handleModeChange("architect")}
        >
          <Bot size={16} />
          <span>Agent</span>
          <span className="text-xs ml-1 opacity-70">Auto</span>
        </button>
      </div>
    </div>
  )
}
