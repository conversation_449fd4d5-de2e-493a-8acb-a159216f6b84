import { FC, memo, useEffect, useState } from "react"
import ReactMarkdown, { Options } from "react-markdown"
import remarkGfm from "remark-gfm"

import { cn } from "@/lib/utils"
import { Separator } from "@/components/ui"

import { CodeBlock } from "./CodeBlock"
import { Blockquote } from "./Blockquote"

const MemoizedReactMarkdown: FC<Options> = memo(
    ReactMarkdown,
    (prevProps, nextProps) => prevProps.children === nextProps.children && prevProps.className === nextProps.className,
)

// Helper function to check if a markdown block is complete
const isCompleteMarkdownBlock = (text: string): boolean => {
    // Check for incomplete headers, lists, code blocks, etc.
    const incompleteCodeBlock = (text.match(/```/g)?.length || 0) % 2 !== 0;
    const incompleteBlockquote = text.split('\n').some(line =>
        line.trim().startsWith('>') &&
        !line.trim().endsWith('.') &&
        !line.trim().endsWith('?') &&
        !line.trim().endsWith('!')
    );

    // Check for incomplete bullet points or numbered lists
    const lines = text.split('\n');
    let inList = false;
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if ((line.match(/^[*-] /) || line.match(/^\d+\. /)) && i === lines.length - 1) {
            // If the last line is a list item, it might be incomplete
            return false;
        }

        if (line.match(/^[*-] /) || line.match(/^\d+\. /)) {
            inList = true;
        } else if (line === '' && inList) {
            inList = false;
        }
    }

    return !incompleteCodeBlock && !incompleteBlockquote;
}

// Function to ensure markdown blocks are complete
const ensureCompleteMarkdownBlocks = (content: string): string => {
    if (!content) return "";

    // Handle incomplete code blocks
    const codeBlockMatches = content.match(/```/g);
    if (codeBlockMatches && codeBlockMatches.length % 2 !== 0) {
        // Add closing code block
        content += '\n```';
    }

    // Handle incomplete lists by adding a newline
    const lines = content.split('\n');
    if (lines.length > 0) {
        const lastLine = lines[lines.length - 1].trim();
        if (lastLine.match(/^[*-] /) || lastLine.match(/^\d+\. /)) {
            content += '\n';
        }
    }

    // Handle incomplete headers
    if (content.trim().endsWith('#') ||
        content.trim().endsWith('##') ||
        content.trim().endsWith('###')) {
        content += ' ';
    }

    return content;
}

export function StreamingMarkdown({ content }: { content: string }) {
    const [processedContent, setProcessedContent] = useState<string>("");

    useEffect(() => {
        // Process the content to ensure complete markdown blocks
        const processed = ensureCompleteMarkdownBlocks(content);
        setProcessedContent(processed);
    }, [content]);

    return (
        <MemoizedReactMarkdown
            remarkPlugins={[remarkGfm]}
            className="custom-markdown break-words streaming-markdown"
            components={{
                p({ children }) {
                    return <div className="mb-2 last:mb-0">{children}</div>
                },
                hr() {
                    return <Separator />
                },
                ol({ children }) {
                    return (
                        <ol className="list-decimal pl-4 [&>li]:mb-1 [&>li:last-child]:mb-0 [&>li>ul]:mt-1 [&>li>ol]:mt-1">
                            {children}
                        </ol>
                    )
                },
                ul({ children }) {
                    return (
                        <ul className="list-disc pl-4 [&>li]:mb-1 [&>li:last-child]:mb-0 [&>li>ul]:mt-1 [&>li>ol]:mt-1">
                            {children}
                        </ul>
                    )
                },
                blockquote({ children }) {
                    return <Blockquote>{children}</Blockquote>
                },
                code({ className, children, ...props }) {
                    if (children && Array.isArray(children) && children.length) {
                        if (children[0] === "▍") {
                            return <span className="mt-1 animate-pulse cursor-default">▍</span>
                        }

                        children[0] = (children[0] as string).replace("`▍`", "▍")
                    }

                    const match = /language-(\w+)/.exec(className || "")

                    const isInline =
                        props.node?.position && props.node.position.start.line === props.node.position.end.line

                    return isInline ? (
                        <code
                            className={cn(className, "inline-code")}
                            style={{
                                color: "#0451a5",
                                backgroundColor: "#ffffff",
                                padding: "1px 4px",
                                borderRadius: "3px",
                                fontFamily: "inherit",
                                fontSize: "inherit",
                                fontWeight: 600
                            }}
                            {...props}
                        >
                            {children}
                        </code>
                    ) : (
                        <CodeBlock
                            language={(match && match[1]) || ""}
                            value={String(children).replace(/\n$/, "")}
                            className="rounded-xs p-3 mb-2"
                        />
                    )
                },
                a({ href, children }) {
                    return (
                        <a href={href} target="_blank" rel="noopener noreferrer">
                            {children}
                        </a>
                    )
                },
            }}>
            {processedContent}
        </MemoizedReactMarkdown>
    )
}
