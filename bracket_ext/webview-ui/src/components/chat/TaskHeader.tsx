import React, { memo, useEffect, useMemo, useRef, useState } from "react"
import { useWindowSize } from "react-use"
import { motion, AnimatePresence } from "framer-motion"
import { ChevronRight, X, ArrowUp, ArrowDown, Database, ArrowRight, Download, Trash2, MessageSquare } from "lucide-react"
import prettyBytes from "pretty-bytes"
import { useTranslation } from "react-i18next"

import { vscode } from "@/utils/vscode"
import { formatLargeNumber } from "@/utils/format"
import { calculateTokenDistribution, getMaxTokensForModel } from "@/utils/model-utils"
import { Button } from "@/components/ui"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

import { ClineMessage } from "../../../../src/shared/ExtensionMessage"
import { mentionRegexGlobal } from "../../../../src/shared/context-mentions"
import { HistoryItem } from "../../../../src/shared/HistoryItem"

import { useExtensionState } from "../../context/ExtensionStateContext"
import Thumbnails from "../common/Thumbnails"
import { normalizeApiConfiguration } from "../settings/ApiOptions"
import { DeleteTaskDialog } from "../history/DeleteTaskDialog"

interface TaskHeaderProps {
	task: ClineMessage
	tokensIn: number
	tokensOut: number
	doesModelSupportPromptCache: boolean
	cacheWrites?: number
	cacheReads?: number
	totalCost: number
	contextTokens: number
	onClose: () => void
}

const TaskHeader: React.FC<TaskHeaderProps> = ({
	task,
	tokensIn,
	tokensOut,
	doesModelSupportPromptCache,
	cacheWrites,
	cacheReads,
	totalCost,
	contextTokens,
	onClose,
}) => {
	const { t } = useTranslation()
	const { apiConfiguration, currentTaskItem } = useExtensionState()
	const { selectedModelInfo } = useMemo(() => normalizeApiConfiguration(apiConfiguration), [apiConfiguration])
	const [isTaskExpanded, setIsTaskExpanded] = useState(true)
	const [isTextExpanded, setIsTextExpanded] = useState(false)
	const [showSeeMore, setShowSeeMore] = useState(false)
	const textContainerRef = useRef<HTMLDivElement>(null)
	const textRef = useRef<HTMLDivElement>(null)
	const contextWindow = selectedModelInfo?.contextWindow || 1

	/*
	When dealing with event listeners in React components that depend on state
	variables, we face a challenge. We want our listener to always use the most
	up-to-date version of a callback function that relies on current state, but
	we don't want to constantly add and remove event listeners as that function
	updates. This scenario often arises with resize listeners or other window
	events. Simply adding the listener in a useEffect with an empty dependency
	array risks using stale state, while including the callback in the
	dependencies can lead to unnecessary re-registrations of the listener. There
	are react hook libraries that provide a elegant solution to this problem by
	utilizing the useRef hook to maintain a reference to the latest callback
	function without triggering re-renders or effect re-runs. This approach
	ensures that our event listener always has access to the most current state
	while minimizing performance overhead and potential memory leaks from
	multiple listener registrations.

	Sources
	- https://usehooks-ts.com/react-hook/use-event-listener
	- https://streamich.github.io/react-use/?path=/story/sensors-useevent--docs
	- https://github.com/streamich/react-use/blob/master/src/useEvent.ts
	- https://stackoverflow.com/questions/55565444/how-to-register-event-with-useeffect-hooks

	Before:

	const updateMaxHeight = useCallback(() => {
		if (isExpanded && textContainerRef.current) {
			const maxHeight = window.innerHeight * (3 / 5)
			textContainerRef.current.style.maxHeight = `${maxHeight}px`
		}
	}, [isExpanded])

	useEffect(() => {
		updateMaxHeight()
	}, [isExpanded, updateMaxHeight])

	useEffect(() => {
		window.removeEventListener("resize", updateMaxHeight)
		window.addEventListener("resize", updateMaxHeight)
		return () => {
			window.removeEventListener("resize", updateMaxHeight)
		}
	}, [updateMaxHeight])

	After:
	*/

	const { height: windowHeight, width: windowWidth } = useWindowSize()

	useEffect(() => {
		if (isTextExpanded && textContainerRef.current) {
			const maxHeight = windowHeight * (1 / 2)
			textContainerRef.current.style.maxHeight = `${maxHeight}px`
		}
	}, [isTextExpanded, windowHeight])

	useEffect(() => {
		if (textRef.current && textContainerRef.current) {
			let textContainerHeight = textContainerRef.current.clientHeight
			if (!textContainerHeight) {
				textContainerHeight = textContainerRef.current.getBoundingClientRect().height
			}
			const isOverflowing = textRef.current.scrollHeight > textContainerHeight
			// necessary to show see more button again if user resizes window to expand and then back to collapse
			if (!isOverflowing) {
				setIsTextExpanded(false)
			}
			setShowSeeMore(isOverflowing)
		}
	}, [task.text, windowWidth])

	const isCostAvailable = useMemo(() => {
		return (
			apiConfiguration?.apiProvider !== "openai" &&
			apiConfiguration?.apiProvider !== "ollama" &&
			apiConfiguration?.apiProvider !== "lmstudio" &&
			apiConfiguration?.apiProvider !== "gemini"
		)
	}, [apiConfiguration?.apiProvider])

	const shouldShowPromptCacheInfo = doesModelSupportPromptCache && apiConfiguration?.apiProvider !== "openrouter"

	return (
		<div className="p-3">
			<div className="rounded-md border border-[var(--vscode-panel-border)] bg-gradient-to-r from-[#1F948F15] to-[#23948B10] shadow-sm overflow-hidden">
				{/* Header */}
				<div
					className={cn(
						"flex items-center justify-between p-3 cursor-pointer border-b border-[var(--vscode-panel-border)]",
						isTaskExpanded ? "bg-gradient-to-r from-[#1F948F30] to-[#23948B20]" : "bg-gradient-to-r from-[#1F948F20] to-[#23948B10]"
					)}
					onClick={() => setIsTaskExpanded(!isTaskExpanded)}
				>
					<div className="flex items-center gap-2 flex-grow min-w-0 select-none">
						<div className="flex-shrink-0 bg-[#1F948F50] rounded-full p-1.5 shadow-sm">
							<MessageSquare className="w-4 h-4 text-[#FFFFFF]" />
						</div>
						<div className="flex-grow min-w-0 overflow-hidden">
							<span className="font-medium text-[var(--vscode-editor-foreground)]">
								{t("chat:task.title")}
							</span>
						</div>
					</div>

					{!isTaskExpanded && isCostAvailable && (
						<Badge
							variant="outline"
							className="ml-2 bg-[#1F948F30] text-[var(--vscode-editor-foreground)] border-none"
						>
							${totalCost?.toFixed(4)}
						</Badge>
					)}

					<div className="flex items-center gap-2">
						<motion.div
							initial={{ rotate: 0 }}
							animate={{ rotate: isTaskExpanded ? 90 : 0 }}
							transition={{ duration: 0.2 }}
							className="flex-shrink-0 bg-[#1F948F15] rounded-full p-1"
						>
							<ChevronRight className="w-4 h-4 text-[#1F948F]" />
						</motion.div>
						<Button
							variant="ghost"
							size="icon"
							className="flex-shrink-0 text-[var(--vscode-foreground)] hover:text-[#1F948F] bg-[#1F948F10] rounded-full"
							onClick={(e) => {
								e.stopPropagation();
								onClose();
							}}
							title={t("chat:task.closeAndStart")}
						>
							<X className="w-4 h-4" />
						</Button>
					</div>
				</div>

				{/* Task Text - Always visible with enhanced styling */}
				<div className="px-4 py-4 bg-gradient-to-r from-[#1F948F25] to-[#1F948F15] border-b border-[var(--vscode-panel-border)]">
					<div className="relative">
						<div className="flex items-start">
							<div className="flex-shrink-0 mr-3 mt-1">
								<div className="w-1.5 h-12 bg-[#1F948F] rounded-full"></div>
							</div>
							<div className={cn(
								"font-semibold text-[20px] leading-relaxed whitespace-pre-wrap word-break-word text-[var(--vscode-editor-foreground)]",
								!isTaskExpanded && "line-clamp-3"
							)}>
								{highlightMentions(task.text, false)}
							</div>
						</div>

						{!isTaskExpanded && task.text && task.text.length > 150 && (
							<div className="absolute right-0 bottom-0 flex items-center">
								<div className="w-12 h-[1.5em] bg-gradient-to-r from-transparent to-[#1F948F25]" />
								<Button
									variant="ghost"
									size="sm"
									className="text-xs text-[#1F948F] hover:text-[#1F948F] hover:bg-[#1F948F15] p-1"
									onClick={(e) => {
										e.stopPropagation();
										setIsTaskExpanded(true);
									}}
								>
									{t("chat:task.seeMore")}
								</Button>
							</div>
						)}
					</div>
				</div>
				{/* Content */}
				<AnimatePresence>
					{isTaskExpanded && (
						<motion.div
							initial={{ height: 0, opacity: 0 }}
							animate={{ height: 'auto', opacity: 1 }}
							exit={{ height: 0, opacity: 0 }}
							transition={{ duration: 0.2 }}
							className="px-4 py-3"
						>
							{/* Task Text Expanded View - with enhanced styling */}
							<div
								ref={textContainerRef}
								className="relative overflow-hidden word-break-word mb-4 bg-[#1F948F10] p-4 rounded-md border border-[#1F948F20]"
								style={{
									overflowY: isTextExpanded ? "auto" : "hidden",
								}}
							>
								<div className="flex items-start">
									<div className="flex-shrink-0 mr-3 mt-1">
										<div className="w-1.5 h-12 bg-[#1F948F] rounded-full"></div>
									</div>
									<div
										ref={textRef}
										className={cn(
											"whitespace-pre-wrap word-break-word text-[15px] font-semibold leading-relaxed",
											!isTextExpanded && "max-h-[200px]"
										)}
									>
										{highlightMentions(task.text, false)}
									</div>
								</div>

								{!isTextExpanded && showSeeMore && (
									<div className="absolute right-0 bottom-0 flex items-center">
										<div className="w-12 h-[1.5em] bg-gradient-to-r from-transparent to-[#1F948F15]" />
										<Button
											variant="ghost"
											size="sm"
											className="text-xs text-[#1F948F] hover:text-[#1F948F] hover:bg-[#1F948F15] p-1 font-medium"
											onClick={(e) => {
												e.stopPropagation();
												setIsTextExpanded(!isTextExpanded);
											}}
										>
											{t("chat:task.seeMore")}
										</Button>
									</div>
								)}
							</div>

							{isTextExpanded && showSeeMore && (
								<div className="flex justify-end mt-2 mb-3">
									<Button
										variant="ghost"
										size="sm"
										className="text-xs text-[#1F948F] hover:text-[#1F948F] hover:bg-[#1F948F15] p-1 font-medium bg-[#1F948F10] rounded-full"
										onClick={(e) => {
											e.stopPropagation();
											setIsTextExpanded(!isTextExpanded);
										}}
									>
										{t("chat:task.seeLess")}
									</Button>
								</div>
							)}

							{/* Images */}
							{task.images && task.images.length > 0 && (
								<div className="mt-3">
									<Thumbnails images={task.images} />
								</div>
							)}

							{/* Metrics Section */}
							<div className="mt-4 space-y-3">
								{/* Tokens */}
								<div className="flex justify-between items-center">
									<div className="flex items-center gap-2 flex-wrap">
										<span className="font-medium text-sm">{t("chat:task.tokens")}</span>
										<div className="flex items-center gap-1 bg-[#1F948F15] px-2 py-0.5 rounded-full text-xs">
											<ArrowUp className="w-3 h-3" />
											<span>{formatLargeNumber(tokensIn || 0)}</span>
										</div>
										<div className="flex items-center gap-1 bg-[#1F948F15] px-2 py-0.5 rounded-full text-xs">
											<ArrowDown className="w-3 h-3" />
											<span>{formatLargeNumber(tokensOut || 0)}</span>
										</div>
									</div>
									{!isCostAvailable && <TaskActions item={currentTaskItem} />}
								</div>

								{/* Context Window */}
								{contextWindow > 0 && (
									<div className={`w-full flex ${windowWidth < 400 ? "flex-col" : "flex-row"} gap-1 h-auto`}>
										<EnhancedContextWindowProgress
											contextWindow={contextWindow}
											contextTokens={contextTokens || 0}
											maxTokens={getMaxTokensForModel(selectedModelInfo, apiConfiguration)}
										/>
									</div>
								)}

								{/* Cache Info */}
								{shouldShowPromptCacheInfo && (cacheReads !== undefined || cacheWrites !== undefined) && (
									<div className="flex items-center gap-2 flex-wrap">
										<span className="font-medium text-sm">{t("chat:task.cache")}</span>
										<div className="flex items-center gap-1 bg-[#1F948F15] px-2 py-0.5 rounded-full text-xs">
											<Database className="w-3 h-3" />
											<span>+{formatLargeNumber(cacheWrites || 0)}</span>
										</div>
										<div className="flex items-center gap-1 bg-[#1F948F15] px-2 py-0.5 rounded-full text-xs">
											<ArrowRight className="w-3 h-3" />
											<span>{formatLargeNumber(cacheReads || 0)}</span>
										</div>
									</div>
								)}

								{/* API Cost */}
								{isCostAvailable && (
									<div className="flex justify-between items-center">
										<div className="flex items-center gap-2">
											<span className="font-medium text-sm">{t("chat:task.apiCost")}</span>
											<Badge
												variant="outline"
												className="bg-[#1F948F30] text-[var(--vscode-editor-foreground)] border-none"
											>
												${totalCost?.toFixed(4)}
											</Badge>
										</div>
										<TaskActions item={currentTaskItem} />
									</div>
								)}
							</div>
						</motion.div>
					)}
				</AnimatePresence>
			</div>
		</div>
	)
}

export const highlightMentions = (text?: string, withShadow = true) => {
	if (!text) return text
	const parts = text.split(mentionRegexGlobal)
	return parts.map((part, index) => {
		if (index % 2 === 0) {
			// This is regular text
			return part
		} else {
			// This is a mention
			return (
				<span
					key={index}
					className={withShadow ? "mention-context-highlight-with-shadow" : "mention-context-highlight"}
					style={{ cursor: "pointer" }}
					onClick={() => vscode.postMessage({ type: "openMention", text: part })}>
					@{part}
				</span>
			)
		}
	})
}

const TaskActions = ({ item }: { item: HistoryItem | undefined }) => {
	const [deleteTaskId, setDeleteTaskId] = useState<string | null>(null)
	const { t } = useTranslation()

	return (
		<div className="flex gap-2">
			<Button
				variant="ghost"
				size="sm"
				className="text-[var(--vscode-foreground)] hover:text-[#1F948F] hover:bg-[#1F948F15]"
				title={t("chat:task.export")}
				onClick={() => vscode.postMessage({ type: "exportCurrentTask" })}>
				<Download className="w-4 h-4" />
			</Button>
			{!!item?.size && item.size > 0 && (
				<>
					<Button
						variant="ghost"
						size="sm"
						className="text-[var(--vscode-foreground)] hover:text-[#1F948F] hover:bg-[#1F948F15] flex items-center gap-1"
						title={t("chat:task.delete")}
						onClick={(e) => {
							e.stopPropagation()

							if (e.shiftKey) {
								vscode.postMessage({ type: "deleteTaskWithId", text: item.id })
							} else {
								setDeleteTaskId(item.id)
							}
						}}>
						<Trash2 className="w-4 h-4" />
						<span className="text-xs">{prettyBytes(item.size)}</span>
					</Button>
					{deleteTaskId && (
						<DeleteTaskDialog
							taskId={deleteTaskId}
							onOpenChange={(open) => !open && setDeleteTaskId(null)}
							open
						/>
					)}
				</>
			)}
		</div>
	)
}

interface ContextWindowProgressProps {
	contextWindow: number
	contextTokens: number
	maxTokens?: number
}

const EnhancedContextWindowProgress = ({
	contextWindow,
	contextTokens,
	maxTokens
}: ContextWindowProgressProps) => {
	const { t } = useTranslation()
	// Use the shared utility function to calculate all token distribution values
	const tokenDistribution = useMemo(
		() => calculateTokenDistribution(contextWindow, contextTokens, maxTokens),
		[contextWindow, contextTokens, maxTokens],
	)

	// Destructure the values we need
	const { currentPercent, reservedPercent, availableSize, reservedForOutput } = tokenDistribution

	// For display purposes
	const safeContextWindow = Math.max(0, contextWindow)
	const safeContextTokens = Math.max(0, contextTokens)

	return (
		<>
			<div className="flex items-center gap-1 flex-shrink-0">
				<span className="font-medium text-sm" data-testid="context-window-label">
					{t("chat:task.contextWindow")}
				</span>
			</div>
			<div className="flex items-center gap-2 flex-1 whitespace-nowrap px-2">
				<div className="text-sm" data-testid="context-tokens-count">{formatLargeNumber(safeContextTokens)}</div>
				<div className="flex-1 relative">
					{/* Main progress bar container */}
					<div className="flex items-center h-2 rounded-full overflow-hidden w-full bg-[#1F948F15]">
						{/* Current tokens container */}
						<motion.div
							className="relative h-full bg-[#1F948F]"
							initial={{ width: 0 }}
							animate={{ width: `${currentPercent}%` }}
							transition={{ duration: 0.5, ease: "easeOut" }}
							title={t("chat:tokenProgress.tokensUsed", {
								used: formatLargeNumber(safeContextTokens),
								total: formatLargeNumber(safeContextWindow),
							})}
							data-testid="context-tokens-used"
						/>

						{/* Container for reserved tokens */}
						<motion.div
							className="relative h-full bg-[#1F948F40]"
							initial={{ width: 0 }}
							animate={{ width: `${reservedPercent}%` }}
							transition={{ duration: 0.5, ease: "easeOut", delay: 0.1 }}
							title={t("chat:tokenProgress.reservedForResponse", {
								amount: formatLargeNumber(reservedForOutput),
							})}
							data-testid="context-reserved-tokens"
						/>
					</div>

					{/* Hover tooltip area */}
					<div
						className="absolute w-full cursor-pointer"
						style={{
							height: "16px",
							top: "-7px",
							zIndex: 5,
						}}
						title={t("chat:tokenProgress.availableSpace", { amount: formatLargeNumber(availableSize) })}
						data-testid="context-available-space"
					/>
				</div>
				<div className="text-sm" data-testid="context-window-size">{formatLargeNumber(safeContextWindow)}</div>
			</div>
		</>
	)
}

export default memo(TaskHeader)
