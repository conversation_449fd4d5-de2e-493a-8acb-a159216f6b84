import React, { memo, useEffect, useMemo, useRef, useState } from "react"
import { useWindowSize } from "react-use"
import { motion, AnimatePresence } from "framer-motion"
import {
  ChevronRight,
  X,
  ArrowUpRight,
  ArrowDownRight,
  Database,
  Zap,
  Download,
  Trash2,
  MessageSquare,
  Clock,
  Cpu,
  DollarSign
} from "lucide-react"
import prettyBytes from "pretty-bytes"
import { useTranslation } from "react-i18next"

import { vscode } from "@/utils/vscode"
import { formatLargeNumber } from "@/utils/format"
import { calculateTokenDistribution, getMaxTokensForModel } from "@/utils/model-utils"
import { Button } from "@/components/ui"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

import { ClineMessage } from "../../../../src/shared/ExtensionMessage"
import { mentionRegexGlobal } from "../../../../src/shared/context-mentions"
import { HistoryItem } from "../../../../src/shared/HistoryItem"

import { useExtensionState } from "../../context/ExtensionStateContext"
import Thumbnails from "../common/Thumbnails"
import { normalizeApiConfiguration } from "../settings/ApiOptions"
import { DeleteTaskDialog } from "../history/DeleteTaskDialog"

interface BracketTaskInfoProps {
  task: ClineMessage
  tokensIn: number
  tokensOut: number
  doesModelSupportPromptCache: boolean
  cacheWrites?: number
  cacheReads?: number
  totalCost: number
  contextTokens: number
  onClose: () => void
}

const BracketTaskInfo: React.FC<BracketTaskInfoProps> = ({
  task,
  tokensIn,
  tokensOut,
  doesModelSupportPromptCache,
  cacheWrites,
  cacheReads,
  totalCost,
  contextTokens,
  onClose,
}) => {
  const { t } = useTranslation()
  const { apiConfiguration, currentTaskItem } = useExtensionState()
  const { selectedModelInfo } = useMemo(() => normalizeApiConfiguration(apiConfiguration), [apiConfiguration])
  const [isTaskExpanded, setIsTaskExpanded] = useState(true)
  const [isTextExpanded, setIsTextExpanded] = useState(false)
  const [showSeeMore, setShowSeeMore] = useState(false)
  const textContainerRef = useRef<HTMLDivElement>(null)
  const textRef = useRef<HTMLDivElement>(null)
  const contextWindow = selectedModelInfo?.contextWindow || 1

  const { height: windowHeight, width: windowWidth } = useWindowSize()

  useEffect(() => {
    if (isTextExpanded && textContainerRef.current) {
      const maxHeight = windowHeight * (1 / 2)
      textContainerRef.current.style.maxHeight = `${maxHeight}px`
    }
  }, [isTextExpanded, windowHeight])

  useEffect(() => {
    if (textRef.current && textContainerRef.current) {
      let textContainerHeight = textContainerRef.current.clientHeight
      if (!textContainerHeight) {
        textContainerHeight = textContainerRef.current.getBoundingClientRect().height
      }
      const isOverflowing = textRef.current.scrollHeight > textContainerHeight
      if (!isOverflowing) {
        setIsTextExpanded(false)
      }
      setShowSeeMore(isOverflowing)
    }
  }, [task.text, windowWidth])

  const isCostAvailable = useMemo(() => {
    return (
      apiConfiguration?.apiProvider !== "openai" &&
      apiConfiguration?.apiProvider !== "ollama" &&
      apiConfiguration?.apiProvider !== "lmstudio" &&
      apiConfiguration?.apiProvider !== "gemini"
    )
  }, [apiConfiguration?.apiProvider])

  const shouldShowPromptCacheInfo = doesModelSupportPromptCache && apiConfiguration?.apiProvider !== "openrouter"

  // Calculate token usage percentage
  const tokenUsagePercent = useMemo(() => {
    if (contextWindow <= 0) return 0
    return Math.min(100, (contextTokens / contextWindow) * 100)
  }, [contextTokens, contextWindow])

  // Calculate total tokens
  const totalTokens = tokensIn + tokensOut

  return (
    <div className="p-2">
      <div className="rounded-md border border-[var(--vscode-panel-border)] bg-gradient-to-r from-[#1F948F08] to-[#23948B05] shadow-sm overflow-hidden">
        {/* Header */}
        <div
          className={cn(
            "flex items-center justify-between px-3 py-2 cursor-pointer border-b border-[var(--vscode-panel-border)]",
            isTaskExpanded ? "bg-gradient-to-r from-[#1F948F20] to-[#23948B10]" : ""
          )}
          onClick={() => setIsTaskExpanded(!isTaskExpanded)}
        >
          <div className="flex items-center gap-2 flex-grow min-w-0 select-none">
            <motion.div
              initial={{ rotate: 0 }}
              animate={{ rotate: isTaskExpanded ? 90 : 0 }}
              transition={{ duration: 0.2 }}
              className="flex-shrink-0"
            >
              <ChevronRight className="w-4 h-4 text-[var(--vscode-foreground)]" />
            </motion.div>
            <div className="flex-grow min-w-0 overflow-hidden">
              <div className="flex items-center gap-2">
                <MessageSquare className="w-4 h-4 text-[#1F948F]" />
                <span className="font-medium text-base">
                  {t("chat:task.title")}
                </span>
              </div>
              {!isTaskExpanded && (
                <span className="text-[var(--vscode-descriptionForeground)] overflow-hidden text-ellipsis whitespace-nowrap block mt-0.5 text-[14px]">
                  {highlightMentions(task.text, false)}
                </span>
              )}
            </div>
          </div>

          {!isTaskExpanded && (
            <div className="flex items-center gap-2">
              {isCostAvailable && (
                <Badge
                  variant="outline"
                  className="bg-[#1F948F30] text-[var(--vscode-editor-foreground)] border-none flex items-center gap-1"
                >
                  <DollarSign className="w-3 h-3" />
                  {totalCost?.toFixed(4)}
                </Badge>
              )}
              <Badge
                variant="outline"
                className="bg-[#1F948F30] text-[var(--vscode-editor-foreground)] border-none flex items-center gap-1"
              >
                <Cpu className="w-3 h-3" />
                {formatLargeNumber(totalTokens)}
              </Badge>
            </div>
          )}

          <Button
            variant="ghost"
            size="icon"
            className="ml-2 flex-shrink-0 text-[var(--vscode-foreground)] hover:text-[#1F948F]"
            onClick={(e) => {
              e.stopPropagation();
              onClose();
            }}
            title={t("chat:task.closeAndStart")}
          >
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Content */}
        <AnimatePresence>
          {isTaskExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="px-4 py-3"
            >
              {/* Task Text */}
              <div
                ref={textContainerRef}
                className="relative overflow-hidden word-break-word mb-2 p-1"
                style={{
                  overflowY: isTextExpanded ? "auto" : "hidden",
                }}
              >
                <div
                  ref={textRef}
                  className={cn(
                    "whitespace-pre-wrap word-break-word text-[16px] font-medium",
                    !isTextExpanded && "line-clamp-3"
                  )}
                >
                  {highlightMentions(task.text, false)}
                </div>

                {!isTextExpanded && showSeeMore && (
                  <div className="absolute right-0 bottom-0 flex items-center">
                    <div className="w-8 h-[1.2em] bg-gradient-to-r from-transparent to-[#1F948F10]" />
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-xs text-[#1F948F] hover:text-[#1F948F] hover:bg-[#1F948F15] p-0.5 font-medium"
                      onClick={(e) => {
                        e.stopPropagation();
                        setIsTextExpanded(!isTextExpanded);
                      }}
                    >
                      {t("chat:task.seeMore")}
                    </Button>
                  </div>
                )}
              </div>

              {isTextExpanded && showSeeMore && (
                <div className="flex justify-end">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-xs text-[#1F948F] hover:text-[#1F948F] hover:bg-[#1F948F15] p-0.5 font-medium -mt-1"
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsTextExpanded(!isTextExpanded);
                    }}
                  >
                    {t("chat:task.seeLess")}
                  </Button>
                </div>
              )}

              {/* Images */}
              {task.images && task.images.length > 0 && (
                <div className="mt-2">
                  <Thumbnails images={task.images} />
                </div>
              )}

              {/* Stats in a more compact layout */}
              <div className="mt-2 flex flex-wrap gap-2">
                {/* Token Usage Card */}
                <div className="flex-1 min-w-[180px] rounded-md border border-[var(--vscode-panel-border)] p-2 bg-[#1F948F08]">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-1.5">
                      <Cpu className="w-4 h-4 text-[#1F948F]" />
                      <span className="font-medium text-sm">{t("chat:task.tokens")}</span>
                    </div>
                    <div className="text-xs text-[var(--vscode-descriptionForeground)]">
                      {formatLargeNumber(totalTokens)} total
                    </div>
                  </div>

                  <div className="flex flex-col gap-1 mt-1">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-1 text-xs">
                        <ArrowUpRight className="w-3 h-3 text-[#1F948F]" />
                        <span>Input</span>
                      </div>
                      <span className="text-xs font-medium">{formatLargeNumber(tokensIn || 0)}</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-1 text-xs">
                        <ArrowDownRight className="w-3 h-3 text-[#1F948F]" />
                        <span>Output</span>
                      </div>
                      <span className="text-xs font-medium">{formatLargeNumber(tokensOut || 0)}</span>
                    </div>

                    {shouldShowPromptCacheInfo && (cacheReads !== undefined || cacheWrites !== undefined) && (
                      <>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-1 text-xs">
                            <Database className="w-3 h-3 text-[#1F948F]" />
                            <span>Cache Writes</span>
                          </div>
                          <span className="text-xs font-medium">+{formatLargeNumber(cacheWrites || 0)}</span>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-1 text-xs">
                            <Zap className="w-3 h-3 text-[#1F948F]" />
                            <span>Cache Reads</span>
                          </div>
                          <span className="text-xs font-medium">{formatLargeNumber(cacheReads || 0)}</span>
                        </div>
                      </>
                    )}
                  </div>
                </div>

                {/* Context Window Card */}
                {contextWindow > 0 && (
                  <div className="flex-1 min-w-[180px] rounded-md border border-[var(--vscode-panel-border)] p-2 bg-[#1F948F08]">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-1.5">
                        <Clock className="w-4 h-4 text-[#1F948F]" />
                        <span className="font-medium text-sm">{t("chat:task.contextWindow")}</span>
                      </div>
                      <div className="text-xs text-[var(--vscode-descriptionForeground)]">
                        {formatLargeNumber(contextWindow)} max
                      </div>
                    </div>

                    <div className="flex flex-col gap-1 mt-1">
                      <div className="flex items-center justify-between">
                        <span className="text-xs">Used</span>
                        <span className="text-xs font-medium">{formatLargeNumber(contextTokens || 0)}</span>
                      </div>

                      <div className="w-full h-2 bg-[#1F948F15] rounded-full overflow-hidden">
                        <motion.div
                          className="h-full bg-[#1F948F]"
                          initial={{ width: 0 }}
                          animate={{ width: `${tokenUsagePercent}%` }}
                          transition={{ duration: 0.5, ease: "easeOut" }}
                        />
                      </div>

                      <div className="flex items-center justify-between text-xs text-[var(--vscode-descriptionForeground)]">
                        <span>{tokenUsagePercent.toFixed(1)}% used</span>
                        <span>{(100 - tokenUsagePercent).toFixed(1)}% available</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* API Cost and Actions in a single row */}
              <div className="mt-2 flex justify-between items-center">
                {isCostAvailable ? (
                  <div className="flex items-center gap-1.5">
                    <DollarSign className="w-4 h-4 text-[#1F948F]" />
                    <span className="font-medium text-sm">{t("chat:task.apiCost")}:</span>
                    <Badge
                      variant="outline"
                      className="bg-[#1F948F30] text-[var(--vscode-editor-foreground)] border-none"
                    >
                      ${totalCost?.toFixed(4)}
                    </Badge>
                  </div>
                ) : (
                  <div></div> /* Empty div to maintain flex layout */
                )}

                <TaskActions item={currentTaskItem} />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}

export const highlightMentions = (text?: string, withShadow = true) => {
  if (!text) return text
  const parts = text.split(mentionRegexGlobal)
  return parts.map((part, index) => {
    if (index % 2 === 0) {
      // This is regular text
      return part
    } else {
      // This is a mention
      return (
        <span
          key={index}
          className={withShadow ? "mention-context-highlight-with-shadow" : "mention-context-highlight"}
          style={{ cursor: "pointer" }}
          onClick={() => vscode.postMessage({ type: "openMention", text: part })}>
          @{part}
        </span>
      )
    }
  })
}

const TaskActions = ({ item }: { item: HistoryItem | undefined }) => {
  const [deleteTaskId, setDeleteTaskId] = useState<string | null>(null)
  const { t } = useTranslation()

  return (
    <div className="flex gap-2">
      <Button
        variant="ghost"
        size="sm"
        className="text-[var(--vscode-foreground)] hover:text-[#1F948F] hover:bg-[#1F948F15]"
        title={t("chat:task.export")}
        onClick={() => vscode.postMessage({ type: "exportCurrentTask" })}>
        <Download className="w-4 h-4" />
      </Button>
      {!!item?.size && item.size > 0 && (
        <>
          <Button
            variant="ghost"
            size="sm"
            className="text-[var(--vscode-foreground)] hover:text-[#1F948F] hover:bg-[#1F948F15] flex items-center gap-1"
            title={t("chat:task.delete")}
            onClick={(e) => {
              e.stopPropagation()

              if (e.shiftKey) {
                vscode.postMessage({ type: "deleteTaskWithId", text: item.id })
              } else {
                setDeleteTaskId(item.id)
              }
            }}>
            <Trash2 className="w-4 h-4" />
            <span className="text-xs">{prettyBytes(item.size)}</span>
          </Button>
          {deleteTaskId && (
            <DeleteTaskDialog
              taskId={deleteTaskId}
              onOpenChange={(open) => !open && setDeleteTaskId(null)}
              open
            />
          )}
        </>
      )}
    </div>
  )
}

export default memo(BracketTaskInfo)
