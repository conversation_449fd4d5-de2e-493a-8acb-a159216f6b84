import React, { useState, useEffect } from 'react';
import { useContextEngine } from '../../context/ContextEngineContext';
import ChatContextEngineBlock from './ChatContextEngineBlock';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

// Get the images base URI from the window object
const getImagesBaseUri = () => {
  const w = window as any;
  return w.IMAGES_BASE_URI || '';
};

/**
 * A component that displays all context engine blocks inline in the chat
 */
const InlineContextEngineBlocks: React.FC = () => {
  const { contextEngineBlocks, isReasoningCollapsed, setIsReasoningCollapsed } = useContextEngine();
  const [showCollapseButton, setShowCollapseButton] = useState(false);
  // Get the images base URI once when the component mounts
  const [imagesBaseUri] = useState(getImagesBaseUri);

  // Show the collapse button when there are blocks
  useEffect(() => {
    if (contextEngineBlocks && contextEngineBlocks.length > 0) {
      setShowCollapseButton(true);
    } else {
      setShowCollapseButton(false);
    }
  }, [contextEngineBlocks]);

  // Listen for the bracketStarting message to collapse the reasoning blocks
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      const message = event.data;
      if (message.type === 'action' && message.action === 'rooCodeStarting') {
        // Collapse the reasoning blocks when Bracket starts
        console.log('InlineContextEngineBlocks: Received rooCodeStarting message, collapsing reasoning blocks');
        setIsReasoningCollapsed(true);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [setIsReasoningCollapsed]);

  if (!contextEngineBlocks || contextEngineBlocks.length === 0) {
    return null;
  }

  return (
    <div className="mb-4">
      {/* Header with collapse button */}
      {showCollapseButton && (
        <div
          className={`flex items-center justify-between p-2 cursor-pointer bg-[#1F948F0A] ${isReasoningCollapsed ? 'rounded-md' : 'rounded-t-md'} border border-[var(--vscode-editorWidget-border)] ${isReasoningCollapsed ? '' : 'border-b-0'}`}
          onClick={() => setIsReasoningCollapsed(!isReasoningCollapsed)}
        >
          <div className="flex items-center gap-2">
            <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center overflow-hidden">
              <img
                src={`${imagesBaseUri}/bracket_logo.svg`}
                alt="Bracket Logo"
                className="w-4 h-4 object-contain object-center"
                onError={(e) => {
                  // Fallback to a simple colored div if the image fails to load
                  const target = e.target as HTMLImageElement;
                  const parent = target.parentElement;
                  if (parent) {
                    // Replace the img with a colored div
                    const div = document.createElement('div');
                    div.className = 'w-4 h-4 bg-teal-500 rounded-sm';
                    parent.replaceChild(div, target);
                  }
                }}
              />
            </div>
            <span className="font-medium text-[var(--vscode-titleBar-activeForeground)] text-sm">Bracket Context Engine Analysis</span>
          </div>
          <div>
            <motion.div
              initial={{ rotate: 0 }}
              animate={{ rotate: isReasoningCollapsed ? 0 : 180 }}
              transition={{ duration: 0.2 }}
            >
              <ChevronDown className="w-4 h-4 text-[var(--vscode-titleBar-activeForeground)]" />
            </motion.div>
          </div>
        </div>
      )}

      {/* Collapsible content */}
      <AnimatePresence>
        {!isReasoningCollapsed && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className="space-y-2 max-h-[400px] overflow-y-auto pr-2 custom-scrollbar bg-[#1F948F05] rounded-b-md border border-[var(--vscode-editorWidget-border)] border-t-0 p-2"
          >
            <div className="animate-fade-in">
              {contextEngineBlocks.map((block, index) => (
                <ChatContextEngineBlock
                  key={block.id || `block-${index}-${Date.now()}`}
                  block={block}
                  className={`transition-all duration-200 ease-in-out ${index === contextEngineBlocks.length - 1 ? 'animate-slide-in' : ''}`}
                />
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default InlineContextEngineBlocks;
