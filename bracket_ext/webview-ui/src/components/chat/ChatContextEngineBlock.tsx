import React, { useEffect, useState } from 'react';
import { Check, Database } from 'lucide-react';
import { Markdown } from '../ui/markdown/Markdown';
import { cn } from '@/lib/utils';
import { ContextEngineBlock } from '../../../../src/shared/WebviewMessage';

interface ChatContextEngineBlockProps {
  block: ContextEngineBlock;
  className?: string;
}

/**
 * A component that displays a context engine block in the chat
 */
const ChatContextEngineBlock: React.FC<ChatContextEngineBlockProps> = ({
  block,
  className
}) => {
  // Track when the block content changes to add animation
  const [prevContent, setPrevContent] = useState<string>(block.content);
  const [isUpdating, setIsUpdating] = useState<boolean>(false);

  useEffect(() => {
    // If content has changed, trigger the animation
    if (prevContent !== block.content) {
      setIsUpdating(true);
      setPrevContent(block.content);

      // Reset the animation state after animation completes
      const timer = setTimeout(() => {
        setIsUpdating(false);
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [block.content, prevContent]);

  return (
    <div className={cn(
      "flex items-start gap-2 p-3 rounded-md border border-[var(--vscode-editorWidget-border)] mb-2",
      "bg-[#1F948F05]", /* Lighter background color */
      block.isComplete ? "border-l-[#1F948F] border-l-2" : "border-l-[#1F948F] border-l-2",
      isUpdating ? "animate-fade-in" : "",
      className
    )}>
      <div className="flex-shrink-0 mt-1">
        {block.isComplete ? (
          <div className="w-5 h-5 rounded-full bg-[#1F948F] flex items-center justify-center">
            <Check className="w-3 h-3 text-white" />
          </div>
        ) : (
          <Database className="w-5 h-5 text-[#1F948F]" />
        )}
      </div>
      <div className="flex-grow min-w-0 overflow-hidden">
        <div className="flex items-center gap-2 mb-1">
          <span className="text-xs font-medium text-[var(--vscode-titleBar-activeForeground)] text-opacity-80">Bracket Context Engine</span>
          {block.isLoading && (
            <div className="flex space-x-1 ml-1">
              <div className="w-1 h-1 bg-[#1F948F] rounded-full animate-pulse"></div>
              <div className="w-1 h-1 bg-[#1F948F] rounded-full animate-pulse delay-150"></div>
              <div className="w-1 h-1 bg-[#1F948F] rounded-full animate-pulse delay-300"></div>
            </div>
          )}
        </div>
        <div className={cn(
          "prose prose-xs max-w-none augment-style overflow-auto text-[var(--vscode-editor-foreground)] text-xs", /* Reduced font size from text-sm to text-xs */
          isUpdating ? "animate-fade-in" : ""
        )}>
          <Markdown content={block.content} />
        </div>
      </div>
    </div>
  );
};

export default ChatContextEngineBlock;
