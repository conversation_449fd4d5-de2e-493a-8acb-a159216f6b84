import React from "react"
import BracketTaskInfo from "./BracketTaskInfo"
import { ClineMessage } from "../../../../src/shared/ExtensionMessage"

interface TaskInfoSwitcherProps {
  task: ClineMessage
  tokensIn: number
  tokensOut: number
  doesModelSupportPromptCache: boolean
  cacheWrites?: number
  cacheReads?: number
  totalCost: number
  contextTokens: number
  onClose: () => void
}

const TaskInfoSwitcher: React.FC<TaskInfoSwitcherProps> = (props) => {
  return <BracketTaskInfo {...props} />
}

export default TaskInfoSwitcher
