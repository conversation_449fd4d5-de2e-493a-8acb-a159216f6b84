# Mermaid Block Demo

This is a demonstration of the auto-focus feature in Mermaid diagrams.

## Basic Diagram

```mermaid
%% focus: Process Data
graph TD
    A[Start] --> B[Load Data]
    B --> C[Process Data]
    C --> D{Is Valid?}
    D -->|Yes| E[Save Results]
    D -->|No| F[Log Error]
    E --> G[End]
    F --> G
```

## Complex Diagram

```mermaid
%% focus: Validate Input
graph TD
    Start[Start Application] --> Init[Initialize System]
    Init --> LoadConfig[Load Configuration]
    LoadConfig --> ConnectDB[Connect to Database]
    ConnectDB --> CheckAuth[Check Authentication]
    CheckAuth --> GetInput[Get User Input]
    GetInput --> ValidateInput[Validate Input]
    ValidateInput --> ProcessData[Process Data]
    ProcessData --> SaveResults[Save Results]
    SaveResults --> GenerateReport[Generate Report]
    GenerateReport --> DisplayResults[Display Results]
    DisplayResults --> End[End Application]
    
    ValidateInput -->|Invalid| ShowError[Show Error Message]
    ShowError --> GetInput
    
    ProcessData -->|Error| LogError[Log Error]
    LogError --> NotifyAdmin[Notify Administrator]
    NotifyAdmin --> End
```

## Class Diagram

```mermaid
%% focus: DataProcessor
classDiagram
    class Application {
        +start()
        +stop()
        +configure()
    }
    
    class DataLoader {
        -connection
        +loadData()
        -validateSource()
    }
    
    class DataProcessor {
        -algorithms
        +processData()
        +analyzeResults()
        -applyAlgorithm()
    }
    
    class ReportGenerator {
        +generateReport()
        -formatOutput()
    }
    
    Application --> DataLoader
    Application --> DataProcessor
    Application --> ReportGenerator
    DataLoader --> DataProcessor
    DataProcessor --> ReportGenerator
```
