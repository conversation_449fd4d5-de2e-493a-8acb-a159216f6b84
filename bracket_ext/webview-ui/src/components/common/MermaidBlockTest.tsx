import React, { useState } from "react"
import MermaidBlock from "./MermaidBlock"
import styled from "styled-components"

const testMermaidCode = `
graph TD
    A[Start] --> B{Is it working?}
    B -->|Yes| C[Great!]
    B -->|No| D[Debug]
    C --> E[Continue]
    D --> F[Fix]
    F --> B
`

export default function MermaidBlockTest() {
    const [functionName, setFunctionName] = useState<string>("Great!")
    // Using testMermaidCode directly instead of state since we don't modify it
    const mermaidCode = testMermaidCode

    return (
        <TestContainer>
            <h2>Mermaid Block Test</h2>

            <ControlPanel>
                <div>
                    <label>Function to focus on:</label>
                    <input
                        type="text"
                        value={functionName}
                        onChange={(e) => setFunctionName(e.target.value)}
                    />
                </div>
                <button onClick={() => setFunctionName("")}>Clear Focus</button>
            </ControlPanel>

            <MermaidBlock code={mermaidCode} functionName={functionName} />
        </TestContainer>
    )
}

const TestContainer = styled.div`
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
`

const ControlPanel = styled.div`
    margin-bottom: 20px;
    padding: 10px;
    background-color: var(--vscode-editor-background);
    border: 1px solid var(--vscode-panel-border);
    border-radius: 4px;

    display: flex;
    flex-direction: column;
    gap: 10px;

    label {
        margin-right: 10px;
    }

    input {
        background-color: var(--vscode-input-background);
        color: var(--vscode-input-foreground);
        border: 1px solid var(--vscode-input-border);
        padding: 4px 8px;
        border-radius: 2px;
    }

    button {
        background-color: var(--vscode-button-background);
        color: var(--vscode-button-foreground);
        border: none;
        padding: 4px 8px;
        border-radius: 2px;
        cursor: pointer;
        align-self: flex-start;

        &:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
    }
`
