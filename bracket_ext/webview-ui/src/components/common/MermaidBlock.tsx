import { useEffect, useRef, useState } from "react"
import mermaid from "mermaid"
import { useDebounceEffect } from "../../utils/useDebounceEffect"
import styled from "styled-components"
import { vscode } from "../../utils/vscode"

const MERMAID_THEME = {
	background: "#1e1e1e", // VS Code dark theme background
	textColor: "#ffffff", // Main text color
	mainBkg: "#2d2d2d", // Background for nodes
	nodeBorder: "#888888", // Border color for nodes
	lineColor: "#4a9eff", // Lines connecting nodes - bright blue
	primaryColor: "#3c3c3c", // Primary color for highlights
	primaryTextColor: "#ffffff", // Text in primary colored elements
	primaryBorderColor: "#888888",
	secondaryColor: "#2d2d2d", // Secondary color for alternate elements
	tertiaryColor: "#454545", // Third color for special elements
	edgeColor: "#4a9eff", // Edge color - bright blue
	edgeLabelBackground: "#2d2d2d", // Edge label background

	// Class diagram specific
	classText: "#000000",

	// State diagram specific
	labelColor: "#000000",

	// Sequence diagram specific
	actorLineColor: "#cccccc",
	actorBkg: "#2d2d2d",
	actorBorder: "#888888",
	actorTextColor: "#000000",

	// Flow diagram specific
	fillType0: "#2d2d2d",
	fillType1: "#3c3c3c",
	fillType2: "#454545",
	padding: 20,
}

mermaid.initialize({
	startOnLoad: false,
	securityLevel: "loose",
	theme: "dark",
	flowchart: {
		diagramPadding: 15,
		nodeSpacing: 50,
		rankSpacing: 70,
		htmlLabels: true,
		curve: 'basis',
		useMaxWidth: false
	},
	themeVariables: {
		...MERMAID_THEME,
		fontSize: "16px",
		fontFamily: "var(--vscode-font-family, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif)",

		// Additional styling
		noteTextColor: "#ffffff",
		noteBkgColor: "#454545",
		noteBorderColor: "#888888",

		// Improve contrast for special elements
		critBorderColor: "#ff9580",
		critBkgColor: "#803d36",

		// Task diagram specific
		taskTextColor: "#ffffff",
		taskTextOutsideColor: "#ffffff",
		taskTextLightColor: "#ffffff",

		// Numbers/sections
		sectionBkgColor: "#2d2d2d",
		sectionBkgColor2: "#3c3c3c",

		// Alt sections in sequence diagrams
		altBackground: "#2d2d2d",

		// Links
		linkColor: "#6cb6ff",

		// Edge styling
		edgeColor: "#4a9eff",
		edgeLabelBackground: "rgba(45, 45, 45, 0.7)",
		edgeLabelColor: "#ffffff",

		// Borders and lines
		compositeBackground: "#2d2d2d",
		compositeBorder: "#888888",
		titleColor: "#ffffff",
		padding: 40,
	},
})

interface MermaidBlockProps {
	code: string
	functionName?: string
}

export default function MermaidBlock({ code, functionName }: MermaidBlockProps) {
	const containerRef = useRef<HTMLDivElement>(null)
	const [isLoading, setIsLoading] = useState(false)

	// 1) Whenever `code` changes, mark that we need to re-render a new chart
	useEffect(() => {
		setIsLoading(true)
	}, [code])

	// Helper function to find and focus on a function in the diagram
	const focusOnFunction = (svgElement: SVGElement, functionName: string) => {
		if (!functionName || !svgElement) return

		console.log(`Focusing on function: ${functionName}`)

		// Find all text elements in the SVG
		const textElements = svgElement.querySelectorAll('text')
		let targetNode: Element | null = null
		let bestMatchScore = 0
		let bestMatchNode: Element | null = null

		// Try to find the best match using a scoring system
		Array.from(textElements).forEach(textEl => {
			const text = textEl.textContent?.trim() || ''
			let score = 0

			// Exact match gets highest score
			if (text === functionName) {
				score = 100
			}
			// Case insensitive exact match
			else if (text.toLowerCase() === functionName.toLowerCase()) {
				score = 90
			}
			// Contains match with word boundaries
			else if (new RegExp(`\\b${functionName}\\b`, 'i').test(text)) {
				score = 80
			}
			// Contains match
			else if (text.toLowerCase().includes(functionName.toLowerCase())) {
				score = 70
			}
			// Partial match (function name contains the text)
			else if (functionName.toLowerCase().includes(text.toLowerCase()) && text.length > 3) {
				score = 60
			}

			// If we have a match and it's better than our previous best
			if (score > bestMatchScore) {
				bestMatchScore = score
				bestMatchNode = textEl.closest('.node') || textEl.parentElement
			}
		})

		// Use the best match if found
		if (bestMatchNode && bestMatchScore > 0) {
			targetNode = bestMatchNode
			console.log(`Found match with score: ${bestMatchScore}`)
		}

		// If we found a target node, highlight and focus on it
		if (targetNode) {
			highlightAndFocusNode(svgElement, targetNode)
		} else {
			console.log(`No node found for function: ${functionName}`)
		}
	}

	// Helper function to highlight and focus on a node
	const highlightAndFocusNode = (svgElement: SVGElement, node: Element) => {
		// Get the bounding box of the node
		const nodeBBox = (node as SVGGraphicsElement).getBBox()

		// Get the SVG dimensions
		const svgWidth = (svgElement as SVGSVGElement).width.baseVal.value
		const svgHeight = (svgElement as SVGSVGElement).height.baseVal.value

		// Calculate the center of the node
		const nodeX = nodeBBox.x + nodeBBox.width / 2
		const nodeY = nodeBBox.y + nodeBBox.height / 2

		// Calculate the container dimensions (the parent div)
		const containerWidth = svgElement.parentElement?.clientWidth || svgWidth
		const containerHeight = svgElement.parentElement?.clientHeight || svgHeight

		// Calculate the scale to focus on the node (zoom in)
		// We want the node to be clearly visible and take up a good portion of the view
		// Higher scale values = more zoom
		const desiredNodeWidthPercentage = 0.4 // Node should take up 40% of the container width
		const scaleForWidth = (containerWidth * desiredNodeWidthPercentage) / nodeBBox.width

		// Ensure we have a reasonable minimum and maximum scale
		const minScale = 2.0 // Minimum zoom level
		const maxScale = 6.0 // Maximum zoom level
		const targetScale = Math.max(minScale, Math.min(scaleForWidth, maxScale))

		// Apply transform to the SVG to focus on the node
		// Center the node in the visible area
		const translateX = (containerWidth / 2) - (nodeX * targetScale)
		const translateY = (containerHeight / 2) - (nodeY * targetScale)

		console.log(`Focusing on node at (${nodeX}, ${nodeY}) with scale ${targetScale}`)
		console.log(`Container size: ${containerWidth}x${containerHeight}`)
		console.log(`Node size: ${nodeBBox.width}x${nodeBBox.height}`)
		console.log(`Desired scale: ${scaleForWidth}, Min: ${minScale}, Max: ${maxScale}, Final: ${targetScale}`)
		console.log(`Applying transform: translate(${translateX}px, ${translateY}px) scale(${targetScale})`)

		// Apply the transform with a smooth transition
		svgElement.style.transition = 'transform 0.5s ease-in-out'
		svgElement.style.transform = `translate(${translateX}px, ${translateY}px) scale(${targetScale})`
		svgElement.style.transformOrigin = 'top left'

		// Add a highlight effect to the node
		highlightNode(node)
	}

	// Helper function to highlight a node
	const highlightNode = (node: Element) => {
		// Find the shape element (rect, circle, etc.)
		const shape = node.querySelector('rect, circle, polygon, path')
		if (!shape) return

		// We could store original attributes to restore later if needed
		// but for now we'll just apply our highlight styles

		// Add a highlight effect
		shape.setAttribute('stroke', '#ff4d4d')
		shape.setAttribute('stroke-width', '3')

		// Add a subtle glow effect using filter
		const svg = node.closest('svg')
		if (svg) {
			// Check if we already have a filter defined
			let filterId = 'highlight-glow'
			let filter = svg.querySelector(`#${filterId}`)

			// Create the filter if it doesn't exist
			if (!filter) {
				const defs = svg.querySelector('defs') || svg.insertBefore(document.createElementNS('http://www.w3.org/2000/svg', 'defs'), svg.firstChild)

				filter = document.createElementNS('http://www.w3.org/2000/svg', 'filter')
				filter.setAttribute('id', filterId)
				filter.setAttribute('x', '-30%')
				filter.setAttribute('y', '-30%')
				filter.setAttribute('width', '160%')
				filter.setAttribute('height', '160%')

				const feGaussianBlur = document.createElementNS('http://www.w3.org/2000/svg', 'feGaussianBlur')
				feGaussianBlur.setAttribute('stdDeviation', '3')
				feGaussianBlur.setAttribute('result', 'blur')

				const feColorMatrix = document.createElementNS('http://www.w3.org/2000/svg', 'feColorMatrix')
				feColorMatrix.setAttribute('in', 'blur')
				feColorMatrix.setAttribute('type', 'matrix')
				feColorMatrix.setAttribute('values', '1 0 0 0 0  0 0 0 0 0  0 0 0 0 0  0 0 0 18 -7')
				feColorMatrix.setAttribute('result', 'glow')

				const feBlend = document.createElementNS('http://www.w3.org/2000/svg', 'feBlend')
				feBlend.setAttribute('in', 'SourceGraphic')
				feBlend.setAttribute('in2', 'glow')
				feBlend.setAttribute('mode', 'normal')

				filter.appendChild(feGaussianBlur)
				filter.appendChild(feColorMatrix)
				filter.appendChild(feBlend)
				defs.appendChild(filter)
			}

			// Apply the filter
			shape.setAttribute('filter', `url(#${filterId})`)
		}

		// Also highlight the text
		const textElement = node.querySelector('text')
		if (textElement) {
			textElement.setAttribute('fill', '#ff4d4d')
			textElement.setAttribute('font-weight', 'bold')
		}

		// Pulse animation effect
		let growing = true
		let pulseCount = 0
		const maxPulses = 3
		const pulseInterval = setInterval(() => {
			if (growing) {
				shape.setAttribute('stroke-width', '5')
			} else {
				shape.setAttribute('stroke-width', '3')
				pulseCount++
			}

			growing = !growing

			if (pulseCount >= maxPulses) {
				clearInterval(pulseInterval)
			}
		}, 300)
	}

	// 2) Debounce the actual parse/render
	useDebounceEffect(
		() => {
			if (containerRef.current) {
				containerRef.current.innerHTML = ""
			}
			// Ensure the code doesn't start with "mermaid" keyword
			let processedCode = code
			if (processedCode.trim().startsWith("mermaid")) {
				const lines = processedCode.trim().split('\n')
				if (lines.length > 1 && lines[0].trim() === "mermaid") {
					processedCode = lines.slice(1).join('\n').trim()
					console.log("Removed 'mermaid' prefix from diagram code")
				}
			}

			// Log the code being parsed for debugging
			console.log("Parsing mermaid code:", processedCode.substring(0, 100) + "...")

			mermaid
				.parse(processedCode, { suppressErrors: true })
				.then((isValid) => {
					if (!isValid) {
						console.error("Invalid mermaid code:", processedCode)
						throw new Error("Invalid or incomplete Mermaid code")
					}
					const id = `mermaid-${Math.random().toString(36).substring(2)}`
					return mermaid.render(id, processedCode)
				})
				.then(({ svg }) => {
					if (containerRef.current) {
						containerRef.current.innerHTML = svg

						// After rendering, focus on the function if provided
						const svgElement = containerRef.current.querySelector("svg") as SVGElement
						if (svgElement) {
							// Set a reasonable default scale to prevent tiny diagrams
							const defaultScale = 1.0
							svgElement.style.transform = `scale(${defaultScale})`
							svgElement.style.transformOrigin = 'top left'

							// Apply additional styling to ensure text has proper spacing
							const style = document.createElement('style')
							style.textContent = `
								.node rect, .node circle, .node polygon, .node path {
									padding: 15px !important;
								}
								.node text {
									padding: 8px !important;
									text-overflow: visible !important;
									overflow: visible !important;
									white-space: normal !important;
									word-wrap: break-word !important;
									text-anchor: middle !important;
								}
								.edgeLabel {
									padding: 6px 8px !important;
									background-color: rgba(45, 45, 45, 0.8) !important;
									color: #ffffff !important;
									border-radius: 4px !important;
									font-weight: 400 !important;
									box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
								}
								.edgeLabel .edgeLabel-text {
									color: #ffffff !important;
								}
								.edgePath path.path {
									stroke: #4a9eff !important;
									stroke-width: 2px !important;
								}
								.edgePath marker {
									fill: #4a9eff !important;
								}
								/* Ensure all text elements are visible */
								text {
									overflow: visible !important;
									white-space: normal !important;
									word-wrap: break-word !important;
								}
								.nodeLabel text, .cluster-label text, .titleText text {
									font-weight: 500 !important;
									overflow: visible !important;
									white-space: normal !important;
									word-wrap: break-word !important;
									text-anchor: middle !important;
								}
							`
							svgElement.appendChild(style)

							// If we have a function name, try to focus on it
							if (functionName) {
								// Give a small delay to ensure the SVG is fully rendered
								setTimeout(() => {
									focusOnFunction(svgElement, functionName)
								}, 100)
							}
						}
					}
				})
				.catch((err) => {
					console.warn("Mermaid parse/render failed:", err)

					// Create a more user-friendly error display
					if (containerRef.current) {
						const errorDiv = document.createElement('div')
						errorDiv.className = 'mermaid-error'
						errorDiv.style.color = '#ff6b6b'
						errorDiv.style.padding = '10px'
						errorDiv.style.border = '1px solid #ff6b6b'
						errorDiv.style.borderRadius = '4px'
						errorDiv.style.margin = '10px 0'
						errorDiv.style.backgroundColor = 'rgba(255, 107, 107, 0.1)'

						// Create error heading
						const errorHeading = document.createElement('div')
						errorHeading.style.fontWeight = 'bold'
						errorHeading.style.marginBottom = '8px'
						errorHeading.textContent = 'Failed to render diagram:'
						errorDiv.appendChild(errorHeading)

						// Create error message
						const errorMessage = document.createElement('div')
						errorMessage.style.marginBottom = '8px'
						errorMessage.textContent = err.message || 'Unknown error'
						errorDiv.appendChild(errorMessage)

						// Create code preview
						const codePreview = document.createElement('pre')
						codePreview.style.backgroundColor = 'rgba(0, 0, 0, 0.2)'
						codePreview.style.padding = '8px'
						codePreview.style.borderRadius = '4px'
						codePreview.style.overflow = 'auto'
						codePreview.style.maxHeight = '200px'
						codePreview.style.fontSize = '12px'
						codePreview.style.color = '#cccccc'
						codePreview.textContent = processedCode.substring(0, 200) + (processedCode.length > 200 ? '...' : '')
						errorDiv.appendChild(codePreview)

						// Add a hint
						const hint = document.createElement('div')
						hint.style.marginTop = '8px'
						hint.style.fontSize = '12px'
						hint.style.fontStyle = 'italic'
						hint.textContent = 'Hint: Mermaid diagrams should start with a diagram type like "flowchart", "sequenceDiagram", etc.'
						errorDiv.appendChild(hint)

						containerRef.current.innerHTML = ''
						containerRef.current.appendChild(errorDiv)
					}
				})
				.finally(() => {
					setIsLoading(false)
				})
		},
		500, // Delay 500ms
		[code, functionName], // Dependencies for scheduling
	)

	/**
	 * Called when user clicks the rendered diagram.
	 * Converts the <svg> to a PNG and sends it to the extension.
	 */
	const handleClick = async () => {
		if (!containerRef.current) return
		const svgEl = containerRef.current.querySelector("svg")
		if (!svgEl) return

		try {
			const pngDataUrl = await svgToPng(svgEl)
			vscode.postMessage({
				type: "openImage",
				text: pngDataUrl,
			})
		} catch (err) {
			console.error("Error converting SVG to PNG:", err)
		}
	}

	return (
		<MermaidBlockContainer>
			{isLoading && <LoadingMessage>Generating mermaid diagram...</LoadingMessage>}

			{/* The container for the final <svg> or raw code. */}
			<SvgContainer onClick={handleClick} ref={containerRef} $isLoading={isLoading} />
		</MermaidBlockContainer>
	)
}

async function svgToPng(svgEl: SVGElement): Promise<string> {
	// Clone the SVG to avoid modifying the original
	const svgClone = svgEl.cloneNode(true) as SVGElement

	// Get the original viewBox
	const viewBox = svgClone.getAttribute("viewBox")?.split(" ").map(Number) || []
	const originalWidth = viewBox[2] || svgClone.clientWidth
	const originalHeight = viewBox[3] || svgClone.clientHeight

	// Preserve any transform that was applied to the original SVG (for focusing on a function)
	const originalTransform = svgEl.style.transform

	// Apply the original transform to the clone if it exists
	if (originalTransform) {
		// Extract the scale from the transform if possible
		const scaleMatch = originalTransform.match(/scale\((\d+\.?\d*)\)/)
		const translateMatch = originalTransform.match(/translate\(([\-\d\.]+)px,\s*([\-\d\.]+)px\)/)

		const currentScale = scaleMatch ? parseFloat(scaleMatch[1]) : 1
		const translateX = translateMatch ? parseFloat(translateMatch[1]) : 0
		const translateY = translateMatch ? parseFloat(translateMatch[2]) : 0

		console.log(`PNG Export - Scale: ${currentScale}, Translate: (${translateX}, ${translateY})`)

		// Set dimensions based on the current scale
		const scaledWidth = originalWidth * currentScale
		const scaledHeight = originalHeight * currentScale

		// Create a new SVG with the correct dimensions that includes the visible portion
		const newSvg = document.createElementNS('http://www.w3.org/2000/svg', 'svg')
		newSvg.setAttribute('width', `${scaledWidth}`)
		newSvg.setAttribute('height', `${scaledHeight}`)
		newSvg.setAttribute('viewBox', `${-translateX/currentScale} ${-translateY/currentScale} ${scaledWidth/currentScale} ${scaledHeight/currentScale}`)

		// Copy all child nodes from the original SVG
		Array.from(svgClone.childNodes).forEach(node => {
			newSvg.appendChild(node.cloneNode(true))
		})

		return renderSvgToPng(newSvg, scaledWidth, scaledHeight)
	}

	// If no transform was applied, use the original SVG without scaling
	svgClone.setAttribute("width", `${originalWidth}`)
	svgClone.setAttribute("height", `${originalHeight}`)

	return renderSvgToPng(svgClone, originalWidth, originalHeight)
}

// Helper function to render SVG to PNG
async function renderSvgToPng(svgClone: SVGElement, width: number, height: number): Promise<string> {

	const serializer = new XMLSerializer()
	const svgString = serializer.serializeToString(svgClone)
	const svgDataUrl = "data:image/svg+xml;base64," + btoa(decodeURIComponent(encodeURIComponent(svgString)))

	return new Promise((resolve, reject) => {
		const img = new Image()
		img.onload = () => {
			const canvas = document.createElement("canvas")
			canvas.width = width
			canvas.height = height

			const ctx = canvas.getContext("2d")
			if (!ctx) return reject("Canvas context not available")

			// Fill background with Mermaid's dark theme background color
			ctx.fillStyle = MERMAID_THEME.background
			ctx.fillRect(0, 0, canvas.width, canvas.height)

			ctx.imageSmoothingEnabled = true
			ctx.imageSmoothingQuality = "high"

			ctx.drawImage(img, 0, 0, width, height)
			resolve(canvas.toDataURL("image/png", 1.0))
		}
		img.onerror = reject
		img.src = svgDataUrl
	})
}

const MermaidBlockContainer = styled.div`
	position: relative;
	margin: 16px 0;
	padding: 10px;
`

const LoadingMessage = styled.div`
	padding: 8px 0;
	color: var(--vscode-descriptionForeground);
	font-style: italic;
	font-size: 0.9em;
`

interface SvgContainerProps {
	$isLoading: boolean
}

const SvgContainer = styled.div<SvgContainerProps>`
	opacity: ${(props) => (props.$isLoading ? 0.3 : 1)};
	min-height: 25px;
	transition: opacity 0.2s ease;
	cursor: pointer;
	display: flex;
	justify-content: center;
	padding: 10px;
	overflow: auto;

	/* Ensure SVG has proper spacing */
	& svg {
		margin: 10px;
	}

	/* Ensure text in nodes has proper spacing */
	& svg .node text {
		text-transform: none;
		font-weight: 300;
		overflow: visible;
		white-space: normal;
		text-overflow: visible;
		word-wrap: break-word;
		text-anchor: middle;
		padding: 18px;
	}

	/* Style for edge labels */
	& svg .edgeLabel {
		padding: 6px 8px;
		background-color: rgba(45, 45, 45, 0.8);
		color: #ffffff;
		border-radius: 4px;
		font-weight: 400;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
	}

	/* Style for edges/lines */
	& svg .edgePath path.path {
		stroke: #4a9eff;
		stroke-width: 2px;
	}

	/* Style for arrowheads */
	& svg .edgePath marker {
		fill: #4a9eff;
	}

	/* Style for node shapes */
	& svg .node rect, & svg .node circle, & svg .node polygon, & svg .node path {
		padding: 10px;
		fill: #3b7ea6;
		stroke: #2c5d7c;
		stroke-width: 1px;
		rx: 5px;
		ry: 5px;
		filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.4));
	}

	/* Style for node text */
	& svg .node text {
		font-weight: 400;
		padding: 18px;
	}
`
