import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { ContextEngineResult } from '../components/context-engine/BracketContextEngine';
import { ContextFunction } from '../components/context-engine/InjectedContextShowcase';
import { ContextEngineBlock } from '../../../src/shared/WebviewMessage';

interface ContextEngineContextType {
  contextEngineResult: ContextEngineResult | undefined;
  setContextEngineResult: (result: ContextEngineResult | undefined) => void;
  isContextEngineEnabled: boolean;
  setIsContextEngineEnabled: (enabled: boolean) => void;
  callForEveryMessage: boolean;
  setCallForEveryMessage: (enabled: boolean) => void;
  contextFunctions: ContextFunction[];
  setContextFunctions: (functions: ContextFunction[]) => void;
  contextEngineBlocks: ContextEngineBlock[];
  addContextEngineBlock: (block: ContextEngineBlock) => void;
  updateContextEngineBlock: (block: ContextEngineBlock) => void;
  clearContextEngineBlocks: () => void;
  isReasoningCollapsed: boolean;
  setIsReasoningCollapsed: (collapsed: boolean) => void;
  collapseReasoningBlocks: () => void;
}

const ContextEngineContext = createContext<ContextEngineContextType | undefined>(undefined);

export const useContextEngine = () => {
  const context = useContext(ContextEngineContext);
  if (context === undefined) {
    throw new Error('useContextEngine must be used within a ContextEngineProvider');
  }
  return context;
};

interface ContextEngineProviderProps {
  children: ReactNode;
}

// Create a singleton instance to persist state across component remounts
let persistentState = {
  contextEngineResult: undefined as ContextEngineResult | undefined,
  isContextEngineEnabled: true,
  callForEveryMessage: false,
  contextFunctions: [] as ContextFunction[],
  contextEngineBlocks: [] as ContextEngineBlock[],
  isReasoningCollapsed: false,
};

export const ContextEngineProvider: React.FC<ContextEngineProviderProps> = ({ children }) => {
  // Initialize state from the persistent singleton
  const [contextEngineResult, setContextEngineResultState] = useState<ContextEngineResult | undefined>(persistentState.contextEngineResult);
  const [isContextEngineEnabled, setIsContextEngineEnabledState] = useState<boolean>(persistentState.isContextEngineEnabled);
  const [callForEveryMessage, setCallForEveryMessageState] = useState<boolean>(persistentState.callForEveryMessage);
  const [contextFunctions, setContextFunctionsState] = useState<ContextFunction[]>(persistentState.contextFunctions);
  const [contextEngineBlocks, setContextEngineBlocksState] = useState<ContextEngineBlock[]>(persistentState.contextEngineBlocks);
  const [isReasoningCollapsed, setIsReasoningCollapsedState] = useState<boolean>(persistentState.isReasoningCollapsed);

  // Wrapper functions to update both state and persistent singleton
  const setContextEngineResult = (result: ContextEngineResult | undefined) => {
    persistentState.contextEngineResult = result;
    setContextEngineResultState(result);
  };

  const setIsContextEngineEnabled = (enabled: boolean) => {
    persistentState.isContextEngineEnabled = enabled;
    setIsContextEngineEnabledState(enabled);
  };

  const setCallForEveryMessage = (enabled: boolean) => {
    persistentState.callForEveryMessage = enabled;
    setCallForEveryMessageState(enabled);
  };

  const setContextFunctions = (functions: ContextFunction[]) => {
    persistentState.contextFunctions = functions;
    setContextFunctionsState(functions);
  };

  const addContextEngineBlock = (block: ContextEngineBlock) => {
    // Ensure the block has an ID
    const blockWithId = block.id ? block : {
      ...block,
      id: `block-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
    };

    // Add meaningful initial content if empty
    if (!blockWithId.content || blockWithId.content.trim() === '') {
      blockWithId.content = 'Analyzing codebase...';
    }

    const updatedBlocks = [...persistentState.contextEngineBlocks, blockWithId];
    persistentState.contextEngineBlocks = updatedBlocks;
    setContextEngineBlocksState(updatedBlocks);
  };

  const updateContextEngineBlock = (block: ContextEngineBlock) => {
    if (!block.id) {
      console.warn('Attempted to update a block without an ID');
      return;
    }

    // Check if the block exists
    const blockExists = persistentState.contextEngineBlocks.some(existingBlock => existingBlock.id === block.id);

    if (blockExists) {
      // Find the block with the matching ID and update it
      const updatedBlocks = persistentState.contextEngineBlocks.map(existingBlock => {
        if (existingBlock.id === block.id) {
          return { ...existingBlock, ...block };
        }
        return existingBlock;
      });

      persistentState.contextEngineBlocks = updatedBlocks;
      setContextEngineBlocksState(updatedBlocks);
    } else {
      // If block doesn't exist (which shouldn't happen), add it
      console.warn(`Tried to update non-existent block with ID ${block.id}`);
      addContextEngineBlock(block);
    }
  };

  const clearContextEngineBlocks = () => {
    persistentState.contextEngineBlocks = [];
    setContextEngineBlocksState([]);
    // Reset the collapsed state when clearing blocks
    persistentState.isReasoningCollapsed = false;
    setIsReasoningCollapsedState(false);
  };

  const setIsReasoningCollapsed = (collapsed: boolean) => {
    persistentState.isReasoningCollapsed = collapsed;
    setIsReasoningCollapsedState(collapsed);
  };

  const collapseReasoningBlocks = () => {
    console.log('ContextEngineContext: Collapsing reasoning blocks');
    setIsReasoningCollapsed(true);
  };

  // Log when context functions change (for debugging)
  useEffect(() => {
    if (contextFunctions.length > 0) {
      console.log(`Context Engine has ${contextFunctions.length} functions available`);
    }
  }, [contextFunctions.length]);

  // Listen for the rooCodeStarting message to collapse the reasoning blocks
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      const message = event.data;
      if (message.type === 'action' && message.action === 'rooCodeStarting') {
        // Collapse the reasoning blocks when RooCode starts
        collapseReasoningBlocks();
      }
    };

    window.addEventListener('message', handleMessage);
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [collapseReasoningBlocks]); // Added collapseReasoningBlocks as a dependency

  return (
    <ContextEngineContext.Provider
      value={{
        contextEngineResult,
        setContextEngineResult,
        isContextEngineEnabled,
        setIsContextEngineEnabled,
        callForEveryMessage,
        setCallForEveryMessage,
        contextFunctions,
        setContextFunctions,
        contextEngineBlocks,
        addContextEngineBlock,
        updateContextEngineBlock,
        clearContextEngineBlocks,
        isReasoningCollapsed,
        setIsReasoningCollapsed,
        collapseReasoningBlocks,
      }}
    >
      {children}
    </ContextEngineContext.Provider>
  );
};
