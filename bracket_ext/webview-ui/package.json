{"name": "webview-ui", "version": "0.1.0", "private": true, "type": "module", "scripts": {"lint": "eslint src --ext ts,tsx", "lint-fix": "eslint src --ext ts,tsx --fix", "check-types": "tsc", "test": "jest", "dev": "vite", "tsc": "tsc -b", "vite-build": "vite build", "build": "npm-run-all -p tsc vite-build", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "clean": "<PERSON><PERSON><PERSON> build"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@tailwindcss/vite": "^4.0.0", "@tanstack/react-query": "^5.68.0", "@vscode/webview-ui-toolkit": "^1.4.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "debounce": "^2.1.1", "fast-deep-equal": "^3.1.3", "framer-motion": "^12.7.4", "fzf": "^0.5.2", "i18next": "^24.2.2", "i18next-http-backend": "^3.0.2", "knuth-shuffle-seeded": "^1.0.6", "lucide-react": "^0.475.0", "mermaid": "^11.4.1", "posthog-js": "^1.227.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.4.1", "react-markdown": "^9.0.3", "react-remark": "^2.1.0", "react-textarea-autosize": "^8.5.3", "react-use": "^17.5.1", "react-virtuoso": "^4.7.13", "rehype-highlight": "^7.0.0", "remark-gfm": "^4.0.1", "remove-markdown": "^0.6.0", "shell-quote": "^1.8.2", "styled-components": "^6.1.13", "tailwind-merge": "^2.6.0", "tailwindcss": "^4.0.0", "tailwindcss-animate": "^1.0.7", "vscrui": "^0.2.2", "zod": "^3.24.2"}, "devDependencies": {"@storybook/addon-essentials": "^8.5.6", "@storybook/blocks": "^8.5.6", "@storybook/react": "^8.5.6", "@storybook/react-vite": "^8.5.6", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^27.5.2", "@types/node": "^18.0.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/shell-quote": "^1.7.5", "@types/testing-library__jest-dom": "^5.14.5", "@types/vscode-webview": "^1.57.5", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.3.4", "eslint": "^8.57.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-storybook": "^0.11.2", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-simple-dot-reporter": "^1.0.5", "shiki": "^2.3.2", "storybook": "^8.5.6", "storybook-dark-mode": "^4.0.2", "ts-jest": "^29.2.5", "typescript": "^5.4.5", "vite": "6.0.11"}}