name: Publish Extension
on:
  pull_request:
    types: [closed]
  workflow_dispatch:

env:
  GIT_REF: ${{ github.event_name == 'pull_request' && github.event.pull_request.head.sha || 'main' }}
  NODE_VERSION: 20.18.1

jobs:
  publish-extension:
    runs-on: ubuntu-latest
    permissions:
      contents: write # Required for pushing tags.
    if: >
        ( github.event_name == 'pull_request' &&
        github.event.pull_request.base.ref == 'main' &&
        contains(github.event.pull_request.title, 'Changeset version bump') ) ||
        github.event_name == 'workflow_dispatch'
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ env.GIT_REF }}
      - uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
      - run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
      - name: Install Dependencies
        run: |
          npm install -g vsce ovsx
          npm run install:all
      - name: Create .env file
        run: echo "POSTHOG_API_KEY=${{ secrets.POSTHOG_API_KEY }}" >> .env
      - name: Package Extension
        run: |
          current_package_version=$(node -p "require('./package.json').version")
          npm run vsix
          package=$(unzip -l bin/roo-cline-${current_package_version}.vsix)
          echo "$package"
          echo "$package" | grep -q "dist/extension.js" || exit 1
          echo "$package" | grep -q "extension/webview-ui/build/assets/index.js" || exit 1
          echo "$package" | grep -q "extension/node_modules/@vscode/codicons/dist/codicon.ttf" || exit 1
          echo "$package" | grep -q ".env" || exit 1
      - name: Create and Push Git Tag
        run: |
          current_package_version=$(node -p "require('./package.json').version")
          git tag -a "v${current_package_version}" -m "Release v${current_package_version}"
          git push origin "v${current_package_version}"
          echo "Successfully created and pushed git tag v${current_package_version}"
      - name: Publish Extension
        env:
          VSCE_PAT: ${{ secrets.VSCE_PAT }}
          OVSX_PAT: ${{ secrets.OVSX_PAT }}
        run: |
          current_package_version=$(node -p "require('./package.json').version")
          npm run publish:marketplace
          echo "Successfully published version $current_package_version to VS Code Marketplace"
      - name: Create GitHub Release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          current_package_version=$(node -p "require('./package.json').version")

          # Extract changelog for current version
          echo "Extracting changelog for version ${current_package_version}"
          changelog_content=$(sed -n "/## \\[${current_package_version}\\]/,/## \\[/p" CHANGELOG.md | sed '$d')

          # If changelog extraction failed, use a default message
          if [ -z "$changelog_content" ]; then
            echo "Warning: No changelog section found for version ${current_package_version}"
            changelog_content="Release v${current_package_version}"
          else
            echo "Found changelog section for version ${current_package_version}"
          fi

          # Create release with changelog content
          gh release create "v${current_package_version}" \
            --title "Release v${current_package_version}" \
            --notes "$changelog_content" \
            --target ${{ env.GIT_REF }} \
            bin/roo-cline-${current_package_version}.vsix
          echo "Successfully created GitHub Release v${current_package_version}"
