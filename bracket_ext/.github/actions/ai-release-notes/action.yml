name: AI Release Notes
description: Generate AI release notes using git and openai, outputs 'RELEASE_NOTES' and 'OPENAI_PROMPT'

inputs:
  OPENAI_API_KEY:
    required: true
    type: string
  GHA_PAT:
    required: true
    type: string
  model_name:
    required: false
    type: string
    default: gpt-4o-mini
  repo_path:
    required: false
    type: string
  custom_prompt:
    required: false
    default: ''
    type: string
  git_ref:
    required: true
    type: string
  head_ref:
    required: true
    type: string
  base_ref:
    required: true
    type: string

outputs:
  RELEASE_NOTES:
    description: "AI generated release notes"
    value: ${{ steps.ai_release_notes.outputs.RELEASE_NOTES }}
  OPENAI_PROMPT:
    description: "Prompt used to generate release notes"
    value: ${{ steps.ai_prompt.outputs.OPENAI_PROMPT }}

env:
  GITHUB_REF: ${{ inputs.git_ref }}
  BASE_REF: ${{ inputs.base_ref }}
  HEAD_REF: ${{ inputs.head_ref }}

runs:
  using: "composite"
  steps:
    - uses: actions/checkout@v4
      with:
        repository: ${{ inputs.repo_path }}
        token: ${{ inputs.GHA_PAT }}
        ref: ${{ env.GITHUB_REF }}
        fetch-depth: 0

    - name: Set Workspace
      shell: bash
      run: |
        pip install tiktoken
        pip install pytz

    # Github outputs: 'OPENAI_PROMPT'
    - name: Add Git Info to base prompt
      id: ai_prompt
      shell: bash
      env:
        BASE_REF: ${{ env.BASE_REF }}
        HEAD_SHA: ${{ env.HEAD_SHA }}
        PR_TITLE: ${{ github.event.pull_request.title }}
        PR_BODY: ${{ github.event.pull_request.body }}
        MODEL_NAME: ${{ inputs.model_name }}
        CUSTOM_PROMPT: ${{ inputs.custom_prompt }} # Default: ''
      run: python .github/scripts/release-notes-prompt.py

    # Github outputs: 'RELEASE_NOTES'
    - name: Generate AI release notes
      id: ai_release_notes
      shell: bash
      env:
        OPENAI_API_KEY: ${{ inputs.OPENAI_API_KEY }}
        CUSTOM_PROMPT: ${{ steps.ai_prompt.outputs.OPENAI_PROMPT }}
        MODEL_NAME: ${{ inputs.model_name }}
      run: python .github/scripts/ai-release-notes.py

