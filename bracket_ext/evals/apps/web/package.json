{"name": "@evals/web", "private": true, "scripts": {"lint": "next lint", "check-types": "tsc -b", "dev": "dotenvx run -f ../../.env -- next dev --turbopack", "format": "prettier --write src", "build": "next build", "start": "next start"}, "dependencies": {"@evals/db": "workspace:^", "@evals/ipc": "workspace:^", "@evals/types": "workspace:^", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-query": "^5.69.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.0", "fuzzysort": "^3.1.0", "lucide-react": "^0.479.0", "next": "15.2.2", "next-themes": "^0.4.6", "p-map": "^7.0.3", "ps-tree": "^1.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-use": "^17.6.0", "sonner": "^2.0.2", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.24.2"}, "devDependencies": {"@evals/eslint-config": "workspace:^", "@evals/typescript-config": "workspace:^", "@tailwindcss/postcss": "^4", "@types/ps-tree": "^1.1.6", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4"}}