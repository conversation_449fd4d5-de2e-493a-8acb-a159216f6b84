{"name": "@evals/ipc", "private": true, "type": "module", "exports": "./src/index.ts", "scripts": {"lint": "eslint src --ext ts --max-warnings=0", "check-types": "tsc --noEmit", "format": "prettier --write src"}, "dependencies": {"@evals/types": "workspace:^", "node-ipc": "^12.0.0", "zod": "^3.24.2"}, "devDependencies": {"@evals/eslint-config": "workspace:^", "@evals/typescript-config": "workspace:^", "@types/node-ipc": "^9.2.3"}}