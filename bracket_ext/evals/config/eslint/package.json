{"name": "@evals/eslint-config", "private": true, "type": "module", "exports": {"./base": "./base.js", "./next-js": "./next.js"}, "devDependencies": {"@eslint/js": "^9.22.0", "@next/eslint-plugin-next": "^15.2.1", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-turbo": "^2.4.4", "globals": "^16.0.0", "typescript": "^5", "typescript-eslint": "^8.26.0"}}