import * as vscode from "vscode"
import delay from "delay"

import { ClineProvider } from "../core/webview/ClineProvider"

/**
 * Helper to get the visible ClineProvider instance or log if not found.
 */
export function getVisibleProviderOrLog(outputChannel: vscode.OutputChannel): ClineProvider | undefined {
	const visibleProvider = ClineProvider.getVisibleInstance()
	if (!visibleProvider) {
		outputChannel.appendLine("Cannot find any visible Cline instances.")
		return undefined
	}
	return visibleProvider
}

import { registerHumanRelayCallback, unregisterHumanRelayCallback, handleHumanRelayResponse } from "./humanRelay"
import { handleNewTask } from "./handleTask"

// Store panel references in both modes
let sidebarPanel: vscode.WebviewView | undefined = undefined
let tabPanel: vscode.WebviewPanel | undefined = undefined

/**
 * Get the currently active panel
 * @returns WebviewPanel或WebviewView
 */
export function getPanel(): vscode.WebviewPanel | vscode.WebviewView | undefined {
	return tabPanel || sidebarPanel
}

/**
 * Set panel references
 */
export function setPanel(
	newPanel: vscode.WebviewPanel | vscode.WebviewView | undefined,
	type: "sidebar" | "tab",
): void {
	if (type === "sidebar") {
		sidebarPanel = newPanel as vscode.WebviewView
		tabPanel = undefined
	} else {
		tabPanel = newPanel as vscode.WebviewPanel
		sidebarPanel = undefined
	}
}

export type RegisterCommandOptions = {
	context: vscode.ExtensionContext
	outputChannel: vscode.OutputChannel
	provider: ClineProvider
}

export const registerCommands = (options: RegisterCommandOptions) => {
	const { context, outputChannel } = options

	for (const [command, callback] of Object.entries(getCommandsMap(options))) {
		context.subscriptions.push(vscode.commands.registerCommand(command, callback))
	}
}

const getCommandsMap = ({ context, outputChannel, provider }: RegisterCommandOptions) => {
	return {
		"roo-cline.activationCompleted": () => {},
		"roo-cline.plusButtonClicked": async () => {
			const visibleProvider = getVisibleProviderOrLog(outputChannel)
			if (!visibleProvider) return
			await visibleProvider.removeClineFromStack()
			await visibleProvider.postStateToWebview()
			await visibleProvider.postMessageToWebview({ type: "action", action: "chatButtonClicked" })
		},
		"roo-cline.mcpButtonClicked": () => {
			const visibleProvider = getVisibleProviderOrLog(outputChannel)
			if (!visibleProvider) return
			visibleProvider.postMessageToWebview({ type: "action", action: "mcpButtonClicked" })
		},
		"roo-cline.promptsButtonClicked": () => {
			const visibleProvider = getVisibleProviderOrLog(outputChannel)
			if (!visibleProvider) return
			visibleProvider.postMessageToWebview({ type: "action", action: "promptsButtonClicked" })
		},
		"roo-cline.popoutButtonClicked": () => openClineInNewTab({ context, outputChannel }),
		"roo-cline.openInNewTab": () => openClineInNewTab({ context, outputChannel }),
		"roo-cline.settingsButtonClicked": () => {
			const visibleProvider = getVisibleProviderOrLog(outputChannel)
			if (!visibleProvider) return
			visibleProvider.postMessageToWebview({ type: "action", action: "settingsButtonClicked" })
		},
		"roo-cline.historyButtonClicked": () => {
			const visibleProvider = getVisibleProviderOrLog(outputChannel)
			if (!visibleProvider) return
			visibleProvider.postMessageToWebview({ type: "action", action: "historyButtonClicked" })
		},
		"roo-cline.helpButtonClicked": () => {
			vscode.env.openExternal(vscode.Uri.parse("https://docs.roocode.com"))
		},
		"roo-cline.showHumanRelayDialog": (params: { requestId: string; promptText: string }) => {
			const panel = getPanel()

			if (panel) {
				panel?.webview.postMessage({
					type: "showHumanRelayDialog",
					requestId: params.requestId,
					promptText: params.promptText,
				})
			}
		},
		"roo-cline.registerHumanRelayCallback": registerHumanRelayCallback,
		"roo-cline.unregisterHumanRelayCallback": unregisterHumanRelayCallback,
		"roo-cline.handleHumanRelayResponse": handleHumanRelayResponse,
		"roo-cline.newTask": handleNewTask,
		"roo-cline.setCustomStoragePath": async () => {
			const { promptForCustomStoragePath } = await import("../shared/storagePathManager")
			await promptForCustomStoragePath()
		},
		"roo-cline.focusInput": () => {
			provider.postMessageToWebview({ type: "action", action: "focusInput" })
		},
		"bracket-context-engine.clearContextFunctions": async () => {
			// Import the ContextEngineService and ContextEngineProcessor and clear all context functions
			const { ContextEngineService } = await import("../services/ContextEngineService")
			const { ContextEngineProcessor } = await import("../services/ContextEngineProcessor")

			// Clear context functions in both services
			const contextEngineService = ContextEngineService.getInstance()
			contextEngineService.clearContextFunctions()

			const contextEngineProcessor = ContextEngineProcessor.getInstance()
			contextEngineProcessor.clearContextFunctions()

			outputChannel.appendLine("Cleared context engine functions")
		},
		"roo-cline.runCodebaseAnalysis": async () => {
			const visibleProvider = getVisibleProviderOrLog(outputChannel)
			if (!visibleProvider) return

			// Instead of loading hardcoded content, run the dynamic LLM-driven analysis
			// by calling the generateCodebaseOverview and generateSuggestedQuestions commands
			vscode.commands.executeCommand("roo-cline.generateCodebaseOverview", { stream: true })
			// Comment out the suggested questions generation command
			// vscode.commands.executeCommand("roo-cline.generateSuggestedQuestions")

			// Initialize the context engine
			try {
				const { ContextEngineService } = await import("../services/ContextEngineService")
				const contextEngineService = ContextEngineService.getInstance()
				const panel = visibleProvider.getPanel()
				if (panel) {
					contextEngineService.setPanel(panel)
				}
				contextEngineService.updateContextEngine({
					content: "## BRACKET Context Engine Ready\n\nThe Context Engine is ready to analyze your queries and provide relevant context from the codebase.",
					isSuccess: true
				})
				outputChannel.appendLine("Initialized context engine")
			} catch (error) {
				outputChannel.appendLine(`Error initializing context engine: ${error}`)
			}
		},
		"roo-cline.generateCodebaseOverview": async (options: { stream: boolean }) => {
			const visibleProvider = getVisibleProviderOrLog(outputChannel)
			if (!visibleProvider) return

			try {
				// Inform the user that generation has started
				// visibleProvider.postMessageToWebview({
				// 	type: "streamCodebaseOverview",
				// 	text: ""
				// })

				// Check if workspace exists (just for validation)
				const workspaceFolders = vscode.workspace.workspaceFolders
				if (!workspaceFolders || workspaceFolders.length === 0) {
					throw new Error("No workspace folder found")
				}

				// Create a temporary output directory
				const fs = require('fs')
				const path = require('path')
				const os = require('os')
				const { spawn } = require('child_process')

				const tempDir = path.join(os.tmpdir(), `bracket-codebase-overview-${Date.now()}`)
				if (!fs.existsSync(tempDir)) {
					fs.mkdirSync(tempDir, { recursive: true })
				}

				// Get the domain taxonomy path from the environment or use a default path
				const taxonomyPath = process.env.BRACKET_DOMAIN_TAXONOMY_PATH || path.join(context.extensionPath, '..', 'experiments', 'gitlab', 'domain_taxonomy_final.json')

				// Verify that the file exists
				if (!fs.existsSync(taxonomyPath)) {
					throw new Error(`Domain taxonomy file not found at the specified path: ${taxonomyPath}`)
				}

				outputChannel.appendLine(`Using domain taxonomy file: ${taxonomyPath}`)

				// Run the Python script to generate the codebase explanation
				outputChannel.appendLine(`Generating codebase overview using ${taxonomyPath}`)

				// Use the Python script from the scripts directory
				const pythonScript = path.join(context.extensionPath, '..', 'scripts', 'generate_codebase_explanation.py')

				// Verify that the script exists
				if (!fs.existsSync(pythonScript)) {
					throw new Error(`Python script not found at the specified path: ${pythonScript}`)
				}

				outputChannel.appendLine(`Using Python script: ${pythonScript}`)
				const outputPath = tempDir

				// Try to determine the Python executable to use
				let pythonCommand = 'python'

				// Check if our virtual environment exists
				const virtualEnvPath = '/tmp/bracket_venv/bin/python'
				if (fs.existsSync(virtualEnvPath)) {
					pythonCommand = virtualEnvPath
					outputChannel.appendLine('Using virtual environment Python')
				} else {
					try {
						// Check if python3 is available
						const { execSync } = require('child_process')
						execSync('python3 --version', { stdio: 'ignore' })
						pythonCommand = 'python3'
					} catch (e) {
						// Fallback to python
						outputChannel.appendLine('python3 not found, falling back to python')
					}
				}

				// Build command arguments with streaming enabled if requested
				const pythonArgs = [
					pythonScript,
					'--taxonomy', taxonomyPath,
					'--output', outputPath,
					'--model', 'o3-mini',
					'--max-tokens', '4000'
				]

				// Add streaming flag if streaming is enabled
				if (options.stream) {
					pythonArgs.push('--stream')
					outputChannel.appendLine('Streaming mode enabled')
				}

				const pythonProcess = spawn(pythonCommand, pythonArgs)

				// Stream the output to the user
				pythonProcess.stdout.on('data', (data: Buffer) => {
					const output = data.toString()

					// Only log to output channel if it's not streaming content
					// This prevents flooding the output channel with large amounts of streamed content
					if (!options.stream || output.includes('INFO:') || output.includes('WARNING:') || output.includes('ERROR:')) {
						outputChannel.appendLine(output)
					}

					// Send progress updates to the webview
					if (options.stream) {
						// Only send non-log lines to the webview
						if (!output.includes('INFO:') && !output.includes('WARNING:') && !output.includes('ERROR:')) {
							visibleProvider.postMessageToWebview({
								type: "streamCodebaseOverview",
								text: output
							})
						}
					}
				})

				pythonProcess.stderr.on('data', (data: Buffer) => {
					const error = data.toString()
					outputChannel.appendLine(`Error: ${error}`)
				})

				// Wait for the process to complete
				const exitCode = await new Promise<number>((resolve) => {
					pythonProcess.on('close', resolve)
				})

				if (exitCode !== 0) {
					throw new Error(`Python script exited with code ${exitCode}`)
				}

				// Read the explanation from the user-specified path
				const explanationPath = '/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/outputs/datadog_agent/global_artifacts/global_overview_short2.md'
				if (!fs.existsSync(explanationPath)) {
					outputChannel.appendLine(`Explanation file not found at ${explanationPath}. Falling back to sample explanation.`)
					throw new Error(`Explanation file not found at ${explanationPath}`)
				}

				const content = fs.readFileSync(explanationPath, 'utf-8')

				// Send the full content
				visibleProvider.postMessageToWebview({
					type: "codebaseExplanation",
					text: content
				})

				outputChannel.appendLine("Generated codebase overview successfully")
			} catch (error) {
				outputChannel.appendLine(`Error generating codebase overview: ${error}`)
				vscode.window.showErrorMessage(`Failed to generate codebase overview: ${error}`)

				// Inform the user about the error
				visibleProvider.postMessageToWebview({
					type: "streamCodebaseOverview",
					text: `\n\nError generating codebase overview: ${error}\n\nFalling back to sample codebase explanation...`
				})

				// Fallback to sample codebase explanation
				try {
					const fs = require('fs')
					const path = require('path')
					const samplePath = path.join(context.extensionPath, 'assets', 'codebase_explanation.md')

					if (fs.existsSync(samplePath)) {
						const content = fs.readFileSync(samplePath, 'utf-8')
						visibleProvider.postMessageToWebview({
							type: "codebaseExplanation",
							text: content
						})
						outputChannel.appendLine("Loaded sample codebase explanation as fallback")
					} else {
						outputChannel.appendLine("Sample codebase explanation not found")
						visibleProvider.postMessageToWebview({
							type: "codebaseExplanation",
							text: "# Codebase Overview\n\nUnable to generate codebase overview. Please check the output channel for more details."
						})
					}
				} catch (fallbackError) {
					outputChannel.appendLine(`Error loading fallback codebase explanation: ${fallbackError}`)
					visibleProvider.postMessageToWebview({
						type: "codebaseExplanation",
						text: "# Codebase Overview\n\nUnable to generate codebase overview. Please check the output channel for more details."
					})
				}
			}
		},
		// Commented out suggested questions generation - using hardcoded questions in SuggestedQuestionsPanel.tsx instead
		"roo-cline.generateSuggestedQuestions": async () => {
			// No-op - using hardcoded questions in SuggestedQuestionsPanel.tsx
			const visibleProvider = getVisibleProviderOrLog(outputChannel)
			if (!visibleProvider) return

			outputChannel.appendLine("Using hardcoded suggested questions instead of generating them")
		},
	}
}

export const openClineInNewTab = async ({ context, outputChannel }: Omit<RegisterCommandOptions, "provider">) => {
	// (This example uses webviewProvider activation event which is necessary to
	// deserialize cached webview, but since we use retainContextWhenHidden, we
	// don't need to use that event).
	// https://github.com/microsoft/vscode-extension-samples/blob/main/webview-sample/src/extension.ts
	const tabProvider = new ClineProvider(context, outputChannel, "editor")
	const lastCol = Math.max(...vscode.window.visibleTextEditors.map((editor) => editor.viewColumn || 0))

	// Check if there are any visible text editors, otherwise open a new group
	// to the right.
	const hasVisibleEditors = vscode.window.visibleTextEditors.length > 0

	if (!hasVisibleEditors) {
		await vscode.commands.executeCommand("workbench.action.newGroupRight")
	}

	const targetCol = hasVisibleEditors ? Math.max(lastCol + 1, 1) : vscode.ViewColumn.Two

	const newPanel = vscode.window.createWebviewPanel(ClineProvider.tabPanelId, "Roo Code", targetCol, {
		enableScripts: true,
		retainContextWhenHidden: true,
		localResourceRoots: [context.extensionUri],
	})

	// Save as tab type panel.
	setPanel(newPanel, "tab")

	// TODO: Use better svg icon with light and dark variants (see
	// https://stackoverflow.com/questions/58365687/vscode-extension-iconpath).
	newPanel.iconPath = {
		light: vscode.Uri.joinPath(context.extensionUri, "assets", "icons", "panel_light.png"),
		dark: vscode.Uri.joinPath(context.extensionUri, "assets", "icons", "panel_dark.png"),
	}

	await tabProvider.resolveWebviewView(newPanel)

	// Handle panel closing events.
	newPanel.onDidDispose(() => {
		setPanel(undefined, "tab")
	})

	// Lock the editor group so clicking on files doesn't open them over the panel.
	await delay(100)
	await vscode.commands.executeCommand("workbench.action.lockEditorGroup")

	return tabProvider
}
