import * as vscode from "vscode"
import { ContextEngineService } from "../services/ContextEngineService"
import { getVisibleProviderOrLog } from "./registerCommands"

export const registerContextEngineCommands = (context: vscode.ExtensionContext, outputChannel: vscode.OutputChannel) => {
    // Register the context engine commands
    context.subscriptions.push(
        vscode.commands.registerCommand("bracket-context-engine.processQuery", async (query?: string) => {
            if (!query) {
                query = await vscode.window.showInputBox({
                    prompt: "Enter your query to analyze the codebase",
                    placeHolder: "E.g. How does the context engine work?",
                })
            }

            if (!query) return

            const contextEngineService = ContextEngineService.getInstance()
            await contextEngineService.processQuery(query)
        }),

        vscode.commands.registerCommand("bracket-context-engine.processQueryWithCallback", async (query: string, _callbackId: string) => {
            if (!query) return

            const visibleProvider = getVisibleProviderOrLog(outputChannel)
            if (!visibleProvider) return

            // Store the query and callback ID for later use
            const contextEngineService = ContextEngineService.getInstance()

            // Process the query and when complete, trigger the callback
            await contextEngineService.processQuery(query, () => {
                // When processing is complete, notify the webview
                visibleProvider.postMessageToWebview({
                    type: "action",
                    action: "contextEngineComplete",
                    // Add a flag to indicate that the input should be re-enabled
                    shouldEnableInput: true
                })
            })
        }),

        vscode.commands.registerCommand("bracket-context-engine.pauseRooCline", async () => {
            const visibleProvider = getVisibleProviderOrLog(outputChannel)
            if (!visibleProvider) return

            // Pause the current Cline instance
            const currentCline = visibleProvider.getCurrentCline()
            if (currentCline) {
                currentCline.pauseForContextEngine()
            }
        }),

        vscode.commands.registerCommand("bracket-context-engine.resumeRooCline", async () => {
            const visibleProvider = getVisibleProviderOrLog(outputChannel)
            if (!visibleProvider) return

            // Get the context engine service
            const contextEngineService = ContextEngineService.getInstance()

            // Get the relevant functions and files
            const relevantCodeMap: Map<string, string> = contextEngineService.getRelevantFunctions()

            // Log detailed information about what we're passing to RooCline
            console.log(`[registerContextEngineCommands] Got ${relevantCodeMap.size} relevant items from context engine`)

            // Log the first few keys to help with debugging
            const keys = Array.from(relevantCodeMap.keys()).slice(0, 5);
            console.log(`[registerContextEngineCommands] First few keys: ${keys.join(', ')}`)

            // Check if we have any content
            if (relevantCodeMap.size === 0) {
                console.warn(`[registerContextEngineCommands] No relevant content to pass to RooCline!`)
            }

            // Resume the current Cline instance with the relevant functions and files
            const currentCline = visibleProvider.getCurrentCline()
            if (currentCline) {
                currentCline.resumeFromContextEngine(relevantCodeMap)
            }
        }),

        vscode.commands.registerCommand("bracket-context-engine.toggleContextEngine", () => {
            const visibleProvider = getVisibleProviderOrLog(outputChannel)
            if (!visibleProvider) return

            // Toggle the context engine visibility
            visibleProvider.postMessageToWebview({
                type: "action",
                action: "toggleContextEngine"
            })
        }),

        vscode.commands.registerCommand("bracket-context-engine.getRelevantFunctions", () => {
            // Get the context engine service
            const contextEngineService = ContextEngineService.getInstance()

            // Get the relevant functions
            const relevantFunctions = contextEngineService.getRelevantFunctions()
            console.log(`[bracket-context-engine.getRelevantFunctions] Returning ${relevantFunctions.size} relevant functions`)

            // Return the functions so they can be used by other components
            return relevantFunctions
        }),

        vscode.commands.registerCommand("bracket-context-engine.setContextEngineFunctions", async (functions: string[]) => {
            const visibleProvider = getVisibleProviderOrLog(outputChannel)
            if (!visibleProvider) return

            // Get the current Cline instance
            const currentCline = visibleProvider.getCurrentCline()
            if (currentCline) {
                // Set the context engine functions directly
                currentCline.setContextEngineFunctions(functions)
                console.log(`[bracket-context-engine.setContextEngineFunctions] Set ${functions.length} functions on current Cline instance`)
                return true
            }

            console.log(`[bracket-context-engine.setContextEngineFunctions] No current Cline instance found`)
            return false
        }),

        vscode.commands.registerCommand("bracket-context-engine.toggleCallForEveryMessage", async () => {
            const contextEngineService = ContextEngineService.getInstance()
            const currentValue = contextEngineService.getCallForEveryMessage()

            // Toggle the value
            contextEngineService.setCallForEveryMessage(!currentValue)

            // Notify the user
            const message = !currentValue
                ? "Context Engine will now be called for every message"
                : "Context Engine will now be called only once per conversation"

            vscode.window.showInformationMessage(message)

            // Notify the webview
            const visibleProvider = getVisibleProviderOrLog(outputChannel)
            if (visibleProvider) {
                visibleProvider.postMessageToWebview({
                    type: "action",
                    action: "updateContextEngineSettings",
                    callForEveryMessage: !currentValue
                })
            }

            return !currentValue
        })
    )
}
