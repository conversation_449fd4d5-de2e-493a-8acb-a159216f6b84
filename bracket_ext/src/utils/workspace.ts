import * as vscode from "vscode"

/**
 * Get the root path of the current workspace
 * @returns The workspace root path or undefined if no workspace is open
 */
export function getWorkspaceRoot(): string | undefined {
    const workspaceFolders = vscode.workspace.workspaceFolders
    if (!workspaceFolders || workspaceFolders.length === 0) {
        return undefined
    }
    return workspaceFolders[0].uri.fsPath
}

/**
 * Get the relative path from the workspace root
 * @param absolutePath The absolute path
 * @returns The relative path from the workspace root
 */
export function getRelativePath(absolutePath: string): string | undefined {
    const workspaceRoot = getWorkspaceRoot()
    if (!workspaceRoot || !absolutePath.startsWith(workspaceRoot)) {
        return absolutePath
    }
    return absolutePath.substring(workspaceRoot.length + 1)
}

/**
 * Get the absolute path from a relative path
 * @param relativePath The relative path
 * @returns The absolute path
 */
export function getAbsolutePath(relativePath: string): string | undefined {
    const workspaceRoot = getWorkspaceRoot()
    if (!workspaceRoot) {
        return undefined
    }
    return vscode.Uri.joinPath(vscode.Uri.file(workspaceRoot), relativePath).fsPath
}
