#!/usr/bin/env python3
import sys
import json
import os
import time

# Check for required dependencies
try:
    import pandas as pd
except ImportError:
    print(json.dumps({"error": "pandas module not found. Please install it using 'pip install pandas pyarrow'"}))
    sys.exit(1)

try:
    import pyarrow
except ImportError:
    print(json.dumps({"error": "pyarrow module not found. Please install it using 'pip install pandas pyarrow'"}))
    sys.exit(1)

PARQUET_FILE_PATH = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/semantic_documented_functions.parquet"

# Check if the parquet file exists
if not os.path.exists(PARQUET_FILE_PATH):
    print(json.dumps({"error": f"Parquet file not found at {PARQUET_FILE_PATH}"}))
    sys.exit(1)

def normalize_function_ref(func_ref):
    """Normalize function reference to match node_id format in parquet file."""
    # Handle path:class.function format
    if ":" in func_ref:
        file_path, class_func = func_ref.split(":", 1)
        # Extract just the filename without the full path
        file_path = os.path.basename(file_path)
        return f"{file_path}:{class_func}"

    # Handle class.function format (no file path)
    return func_ref

def find_best_match(df, func_ref, normalized_ref=None):
    """Find the best matching row for a function reference."""
    # Try exact match first
    exact_match = df[df['node_id'] == func_ref]
    if not exact_match.empty:
        return exact_match.iloc[0]

    # Use provided normalized reference or compute it
    if normalized_ref is None:
        normalized_ref = normalize_function_ref(func_ref)

    # Check if the normalized reference is in any node_id
    partial_matches = df[df['node_id'].str.contains(normalized_ref, regex=False)]
    if not partial_matches.empty:
        return partial_matches.iloc[0]

    # Try matching just the class and function name
    if "." in func_ref:
        class_func = func_ref.split(":")[-1] if ":" in func_ref else func_ref
        class_func_matches = df[df['node_id'].str.contains(class_func, regex=False)]
        if not class_func_matches.empty:
            return class_func_matches.iloc[0]

    # Try matching just the function name
    if "." in func_ref:
        func_name = func_ref.split(".")[-1]
        func_name_matches = df[df['name'] == func_name]
        if not func_name_matches.empty:
            return func_name_matches.iloc[0]

    return None

def find_function_content(function_refs):
    """
    Find function content for a list of function references.

    Args:
        function_refs: List of function reference strings

    Returns:
        Dictionary mapping function references to their content
    """
    try:
        start_time = time.time()
        print(f"Processing {len(function_refs)} function references", file=sys.stderr)

        # Read the parquet file once for all function references
        parquet_start = time.time()
        df = pd.read_parquet(PARQUET_FILE_PATH)
        parquet_time = time.time() - parquet_start
        print(f"Reading parquet file took {parquet_time:.3f} seconds", file=sys.stderr)

        # Create a dictionary to store results
        results = {}

        # Pre-compute normalized references for all function references
        norm_start = time.time()
        normalized_refs = {func_ref: normalize_function_ref(func_ref) for func_ref in function_refs}
        norm_time = time.time() - norm_start
        print(f"Normalizing references took {norm_time:.3f} seconds", file=sys.stderr)

        # Process each function reference
        match_start = time.time()
        match_count = 0
        for func_ref in function_refs:
            # Find the best match for this function reference using pre-computed normalized reference
            match = find_best_match(df, func_ref, normalized_refs[func_ref])

            if match is not None:
                match_count += 1
                results[func_ref] = {
                    'found': True,
                    'file_path': match['file_path'],
                    'start_line': int(match['start_line']),
                    'end_line': int(match['end_line']),
                    'text': match['text'],
                    'node_id': match['node_id']  # Include the actual node_id for debugging
                }
            else:
                # If we get here, we couldn't find a match
                results[func_ref] = {
                    'found': False,
                    'error': f"Could not find function {func_ref} in parquet file"
                }

        match_time = time.time() - match_start
        print(f"Finding matches took {match_time:.3f} seconds, found {match_count}/{len(function_refs)}", file=sys.stderr)

        total_time = time.time() - start_time
        print(f"Total processing time: {total_time:.3f} seconds", file=sys.stderr)

        return results

    except Exception as e:
        return {'error': str(e)}

if __name__ == "__main__":
    # Read function references from stdin (one per line)
    if len(sys.argv) > 1:
        # Function references passed as arguments
        function_refs = sys.argv[1:]
    else:
        # Read from stdin
        function_refs = [line.strip() for line in sys.stdin.readlines()]

    # Find function content
    results = find_function_content(function_refs)

    # Output results as JSON
    print(json.dumps(results))
