import * as vscode from "vscode"
import * as path from "path"
import * as fs from "fs"
import { promisify } from "util"
import { getWorkspacePath } from "./path"
import { exec } from "child_process"

// Promisify fs functions
const readFile = promisify(fs.readFile)
const access = promisify(fs.access)

/**
 * Interface for file information
 */
interface FileInfo {
    filePath: string;
}

/**
 * Parse a file reference into its components
 * @param fileRef The file reference string
 * @returns An object containing the file path
 */
export function parseFileReference(fileRef: string): FileInfo | null {
    try {
        // Normalize the file path
        const normalizedPath = fileRef.replace(/\\/g, "/").trim();
        return { filePath: normalizedPath };
    } catch (error) {
        console.error(`Error parsing file reference "${fileRef}":`, error);
        return null;
    }
}

/**
 * Find a file in the workspace
 * @param info The file information
 * @param cwd The current working directory
 * @returns The absolute path to the file, or null if not found
 */
export async function findFile(info: FileInfo, cwd: string): Promise<string | null> {
    try {
        // If we already have a file path, use it
        if (info.filePath) {
            const absolutePath = path.isAbsolute(info.filePath)
                ? info.filePath
                : path.resolve(cwd, info.filePath);

            // Check if the file exists
            try {
                await access(absolutePath);
                return absolutePath;
            } catch {
                // Try to find the file in the workspace
                const files = await vscode.workspace.findFiles(`**/${path.basename(info.filePath)}`);
                if (files.length > 0) {
                    return files[0].fsPath;
                }
            }
        }

        return null;
    } catch (error) {
        console.error("Error finding file:", error);
        return null;
    }
}

/**
 * Extract the content of a file
 * @param filePath The path to the file
 * @returns The file content, or null if not found
 */
export async function extractFileContent(filePath: string): Promise<string | null> {
    try {
        // Read the file content
        const content = await readFile(filePath, "utf8");
        return content;
    } catch (error) {
        console.error("Error extracting file content:", error);
        return null;
    }
}

/**
 * Get the content of a file from a file reference
 * @param fileRef The file reference string
 * @returns The file content, or an error message if not found
 */
export async function getFileContent(fileRef: string): Promise<string> {
    try {
        // Parse the file reference
        const info = parseFileReference(fileRef);
        if (!info) {
            return `Error: Could not parse file reference "${fileRef}"`;
        }

        // Get the workspace path
        const cwd = getWorkspacePath() || "";
        if (!cwd) {
            return `Error: Could not determine workspace path`;
        }

        // Find the file
        const filePath = await findFile(info, cwd);
        if (!filePath) {
            return `Error: Could not find file "${fileRef}"`;
        }

        // Extract the file content
        const content = await extractFileContent(filePath);
        if (!content) {
            return `Error: Could not extract content for file "${filePath}"`;
        }

        return `File: ${filePath}\nContent: \n${content}`;
    } catch (error) {
        console.error("Error getting file content:", error);
        return `Error getting file content for "${fileRef}": ${error.message}`;
    }
}

// Promisify exec for cleaner async/await usage
const execAsync = promisify(exec);

// Cache for file content results
const fileContentCache: Record<string, string> = {};

/**
 * Clear the file content cache
 * @param filePaths Optional array of file paths to clear from the cache. If not provided, clears the entire cache.
 */
export function clearFileContentCache(filePaths?: string[]): void {
    if (!filePaths) {
        // Clear the entire cache
        console.log('Clearing entire file content cache');
        Object.keys(fileContentCache).forEach(key => delete fileContentCache[key]);
    } else {
        // Clear only the specified file paths
        console.log(`Clearing ${filePaths.length} files from cache`);
        filePaths.forEach(filePath => delete fileContentCache[filePath]);
    }
}

/**
 * Get statistics about the file content cache
 * @returns Object with cache statistics
 */
export function getFileContentCacheStats(): { size: number, keys: string[] } {
    return {
        size: Object.keys(fileContentCache).length,
        keys: Object.keys(fileContentCache)
    };
}

/**
 * Process a batch of file paths
 * @param filePaths Array of file paths
 * @returns Object mapping file paths to their content
 */
export async function processFileBatch(filePaths: string[]): Promise<Record<string, string>> {
    console.log(`Processing batch of ${filePaths.length} files`);

    // Check which files are already in the cache
    const cachedFiles: Record<string, string> = {};
    const uncachedFilePaths: string[] = [];

    for (const filePath of filePaths) {
        if (fileContentCache[filePath]) {
            cachedFiles[filePath] = fileContentCache[filePath];
        } else {
            uncachedFilePaths.push(filePath);
        }
    }

    console.log(`Cache hits: ${Object.keys(cachedFiles).length}, cache misses: ${uncachedFilePaths.length}`);

    // If all files are in the cache, return immediately
    if (uncachedFilePaths.length === 0) {
        console.log('All files found in cache, skipping file system lookup');
        return cachedFiles;
    }

    // Process uncached files
    const uncachedResults: Record<string, string> = {};
    const startTime = Date.now();

    for (const filePath of uncachedFilePaths) {
        try {
            const content = await getFileContent(filePath);
            uncachedResults[filePath] = content;
            fileContentCache[filePath] = content;
        } catch (error) {
            console.error(`Error processing file ${filePath}:`, error);
            uncachedResults[filePath] = `Error: Could not read file "${filePath}": ${error.message}`;
        }
    }

    console.log(`Processed ${uncachedFilePaths.length} uncached files in ${Date.now() - startTime}ms`);

    // Combine cached and uncached results
    return { ...cachedFiles, ...uncachedResults };
}
