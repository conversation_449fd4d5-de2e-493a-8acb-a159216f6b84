import * as vscode from "vscode"
import { ApiConfiguration } from "../shared/api"

/**
 * Get the OpenAI API key from the extension configuration
 * @returns The OpenAI API key or null if not found
 */
export async function getOpenAiApiKey(): Promise<string | null> {
    try {
        // Try to get the API key from the extension configuration
        const config = vscode.workspace.getConfiguration("bracket")
        const apiConfigurations = config.get<Record<string, ApiConfiguration>>("apiConfigurations")
        const currentApiConfigName = config.get<string>("currentApiConfigName")

        if (!apiConfigurations || !currentApiConfigName) {
            // Try to get from environment variable
            const env = process.env.OPENAI_API_KEY
            if (env) {
                return env
            }
            return null
        }

        const currentConfig = apiConfigurations[currentApiConfigName]
        if (!currentConfig) {
            return null
        }

        // Check for OpenAI API key in different possible locations
        if (currentConfig.openAiApiKey) {
            return currentConfig.openAiApiKey
        } else if (currentConfig.apiKey && currentConfig.apiProvider === "openai") {
            return currentConfig.apiKey
        } else if (currentConfig.apiProvider === "requesty" && currentConfig.requestyApiKey) {
            // Requesty can also be used with OpenAI models
            return currentConfig.requestyApiKey
        }

        // Try to get from environment variable as fallback
        const env = process.env.OPENAI_API_KEY
        if (env) {
            return env
        }

        return null
    } catch (error) {
        console.error("Error getting OpenAI API key:", error)
        return null
    }
}

/**
 * Get the Anthropic API key from the extension configuration
 * @returns The Anthropic API key or null if not found
 */
export async function getAnthropicApiKey(): Promise<string | null> {
    try {
        // Try to get the API key from the extension configuration
        const config = vscode.workspace.getConfiguration("bracket")
        const apiConfigurations = config.get<Record<string, ApiConfiguration>>("apiConfigurations")
        const currentApiConfigName = config.get<string>("currentApiConfigName")

        if (!apiConfigurations || !currentApiConfigName) {
            // Try to get from environment variable
            const env = process.env.ANTHROPIC_API_KEY
            if (env) {
                return env
            }
            return null
        }

        const currentConfig = apiConfigurations[currentApiConfigName]
        if (!currentConfig) {
            return null
        }

        // Check for Anthropic API key
        if (currentConfig.apiKey && currentConfig.apiProvider === "anthropic") {
            return currentConfig.apiKey
        }

        // Try to get from environment variable as fallback
        const env = process.env.ANTHROPIC_API_KEY
        if (env) {
            return env
        }

        return null
    } catch (error) {
        console.error("Error getting Anthropic API key:", error)
        return null
    }
}

/**
 * Get the OpenRouter API key from the extension configuration
 * @returns The OpenRouter API key or null if not found
 */
export async function getOpenRouterApiKey(): Promise<string | null> {
    try {
        // Try to get the API key from the extension configuration
        const config = vscode.workspace.getConfiguration("bracket")
        const apiConfigurations = config.get<Record<string, ApiConfiguration>>("apiConfigurations")
        const currentApiConfigName = config.get<string>("currentApiConfigName")

        if (!apiConfigurations || !currentApiConfigName) {
            // Try to get from environment variable
            const env = process.env.OPENROUTER_API_KEY
            if (env) {
                return env
            }
            return null
        }

        const currentConfig = apiConfigurations[currentApiConfigName]
        if (!currentConfig) {
            return null
        }

        // Check for OpenRouter API key
        if (currentConfig.openRouterApiKey) {
            return currentConfig.openRouterApiKey
        }

        // Try to get from environment variable as fallback
        const env = process.env.OPENROUTER_API_KEY
        if (env) {
            return env
        }

        // Default OpenRouter API key as last resort
        return "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"
    } catch (error) {
        console.error("Error getting OpenRouter API key:", error)
        return null
    }
}
