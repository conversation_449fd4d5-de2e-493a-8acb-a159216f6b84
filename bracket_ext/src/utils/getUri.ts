import * as vscode from 'vscode';
import * as path from 'path';

/**
 * Gets the webview URI for a given file path
 * @param webview The webview to get the URI for
 * @param extensionUri The URI of the extension
 * @param pathList The path segments to the file
 * @returns A URI that can be used in the webview
 */
export function getUri(webview: vscode.Webview, extensionUri: vscode.Uri, pathList: string[]): vscode.Uri {
    return webview.asWebviewUri(vscode.Uri.joinPath(extensionUri, ...pathList));
}
