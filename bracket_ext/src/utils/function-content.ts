import * as vscode from "vscode"
import * as path from "path"
import * as fs from "fs/promises"
import { getWorkspacePath } from "./path"
import { parseSourceCodeDefinitionsForFile } from "../services/tree-sitter"
import { readLines } from "../integrations/misc/read-lines"
import { addLineNumbers } from "../integrations/misc/extract-text"
import { exec } from "child_process"
import { promisify } from "util"

/**
 * Different formats of function references:
 * 1. Standalone function name: "TimelineEventsFinder.execute"
 * 2. Class and function name: "CreateService.relate_issuables"
 * 3. Path with class and function: "app/finders/todos_finder.rb:TodosFinder.execute"
 */

interface FunctionInfo {
    filePath: string;
    className?: string;
    functionName: string;
}

/**
 * Parse a function reference into its components
 * @param functionRef The function reference string
 * @returns An object containing the file path, class name, and function name
 */
export function parseFunctionReference(functionRef: string): FunctionInfo | null {
    try {
        // Check if the function reference contains a file path
        if (functionRef.includes(":")) {
            // Format: "path/to/file.ext:Class.function"
            const [filePath, classAndFunction] = functionRef.split(":")

            // Parse class and function
            if (classAndFunction.includes(".")) {
                const [className, functionName] = classAndFunction.split(".")
                return { filePath, className, functionName }
            } else {
                // Just a function name without a class
                return { filePath, functionName: classAndFunction }
            }
        } else if (functionRef.includes(".")) {
            // Format: "Class.function" (no file path)
            const [className, functionName] = functionRef.split(".")
            return { filePath: "", className, functionName }
        } else {
            // Just a function name
            return { filePath: "", functionName: functionRef }
        }
    } catch (error) {
        console.error(`Error parsing function reference "${functionRef}":`, error)
        return null
    }
}

/**
 * Find a file that contains the specified class or function
 * @param info The function information
 * @param cwd The current working directory
 * @returns The absolute path to the file, or null if not found
 */
export async function findFileForFunction(info: FunctionInfo, cwd: string): Promise<string | null> {
    try {
        // If we already have a file path, use it
        if (info.filePath) {
            const absolutePath = path.isAbsolute(info.filePath)
                ? info.filePath
                : path.resolve(cwd, info.filePath)

            // Check if the file exists
            try {
                await fs.access(absolutePath)
                return absolutePath
            } catch {
                // Try to find the file in the workspace
                const files = await vscode.workspace.findFiles(`**/${path.basename(info.filePath)}`)
                if (files.length > 0) {
                    return files[0].fsPath
                }
            }
        }

        // If we don't have a file path, try to find the file by class or function name
        const searchPattern = info.className || info.functionName
        const files = await vscode.workspace.findFiles(`**/*.{js,ts,jsx,tsx,py,rb,go,java,c,cpp,h,hpp}`)

        // Search for the class or function in each file
        for (const file of files) {
            const content = await fs.readFile(file.fsPath, "utf8")

            // Simple pattern matching - this could be improved with tree-sitter or regex
            if (content.includes(searchPattern)) {
                return file.fsPath
            }
        }

        return null
    } catch (error) {
        console.error("Error finding file for function:", error)
        return null
    }
}

/**
 * Extract the function content from a file
 * @param filePath The path to the file
 * @param info The function information
 * @returns The function content, or null if not found
 */
export async function extractFunctionContent(filePath: string, info: FunctionInfo): Promise<string | null> {
    try {
        // Read the file content
        const content = await fs.readFile(filePath, "utf8")
        const lines = content.split("\n")

        // Different patterns for different languages
        const patterns: Record<string, RegExp> = {
            ".js": new RegExp(`(function\\s+${info.functionName}|${info.functionName}\\s*=\\s*function|${info.className}\\.${info.functionName}\\s*=\\s*function|${info.className}\\.prototype\\.${info.functionName}\\s*=\\s*function)\\s*\\(`),
            ".ts": new RegExp(`(function\\s+${info.functionName}|${info.functionName}\\s*=\\s*function|${info.className}\\.${info.functionName}\\s*=\\s*function|${info.className}\\.prototype\\.${info.functionName}\\s*=\\s*function|${info.functionName}\\s*\\(|${info.className}\\.${info.functionName}\\s*\\()\\s*`),
            ".py": new RegExp(`(def\\s+${info.functionName}\\s*\\(|class\\s+${info.className})`),
            ".rb": new RegExp(`(def\\s+${info.functionName}\\s*|class\\s+${info.className})`),
            ".go": new RegExp(`(func\\s+${info.functionName}\\s*\\(|func\\s*\\([^)]*\\)\\s*${info.functionName}\\s*\\(|type\\s+${info.className}\\s+struct)`),
            ".java": new RegExp(`(public|private|protected)\\s+(static\\s+)?\\w+\\s+${info.functionName}\\s*\\(`),
            ".c": new RegExp(`\\w+\\s+${info.functionName}\\s*\\(`),
            ".cpp": new RegExp(`\\w+\\s+${info.functionName}\\s*\\(`),
        }

        // Get the file extension
        const ext = path.extname(filePath)
        const pattern = patterns[ext] || new RegExp(`${info.functionName}\\s*\\(`)

        // Find the function in the file
        let startLine = -1
        let endLine = -1
        let braceCount = 0
        let inFunction = false

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i]

            if (!inFunction) {
                // Check if this line contains the function definition
                if (pattern.test(line)) {
                    startLine = i
                    inFunction = true
                    braceCount += (line.match(/{/g) || []).length
                    braceCount -= (line.match(/}/g) || []).length

                    // If the function is defined and closed on the same line
                    if (braceCount === 0 && line.includes("{") && line.includes("}")) {
                        endLine = i
                        break
                    }
                }
            } else {
                braceCount += (line.match(/{/g) || []).length
                braceCount -= (line.match(/}/g) || []).length

                // If we've closed all braces, we've reached the end of the function
                if (braceCount === 0 && line.includes("}")) {
                    endLine = i
                    break
                }
            }
        }

        // If we found the function, return its content
        if (startLine !== -1) {
            if (endLine === -1) {
                endLine = lines.length - 1
            }

            // Extract the function content
            const functionLines = lines.slice(startLine, endLine + 1)
            return addLineNumbers(functionLines.join("\n"), startLine + 1)
        }

        // If we couldn't find the function with regex, try using tree-sitter
        const definitions = await parseSourceCodeDefinitionsForFile(filePath)
        if (definitions) {
            // Look for the function in the definitions
            const functionPattern = info.className
                ? new RegExp(`${info.className}.*${info.functionName}`)
                : new RegExp(`${info.functionName}`)

            const definitionLines = definitions.split("\n")
            for (let i = 0; i < definitionLines.length; i++) {
                const line = definitionLines[i]
                if (functionPattern.test(line)) {
                    // Found the function definition, now get its content
                    const match = line.match(/\((\d+)-(\d+)\)/)
                    if (match) {
                        const startLine = parseInt(match[1])
                        const endLine = parseInt(match[2])
                        return addLineNumbers(await readLines(filePath, endLine, startLine - 1), startLine)
                    }
                }
            }
        }

        return null
    } catch (error) {
        console.error("Error extracting function content:", error)
        return null
    }
}

/**
 * Get the content of a function from a function reference
 * @param functionRef The function reference string
 * @returns The function content, or an error message if not found
 */
export async function getFunctionContent(functionRef: string): Promise<string> {
    try {
        // Parse the function reference
        const info = parseFunctionReference(functionRef)
        if (!info) {
            return `Error: Could not parse function reference "${functionRef}"`
        }

        // Get the workspace path
        const cwd = getWorkspacePath() || ""
        if (!cwd) {
            return `Error: Could not determine workspace path`
        }

        // Find the file that contains the function
        const filePath = await findFileForFunction(info, cwd)
        if (!filePath) {
            return `Error: Could not find file for function "${functionRef}"`
        }

        // Extract the function content
        const content = await extractFunctionContent(filePath, info)
        if (!content) {
            return `Error: Could not extract content for function "${functionRef}" in file "${filePath}"`
        }

        return `Function: ${functionRef}\nFile: ${filePath}\nContent: \n${content}`
    } catch (error) {
        console.error("Error getting function content:", error)
        return `Error getting function content for "${functionRef}": ${error.message}`
    }
}

// Promisify exec for cleaner async/await usage
const execAsync = promisify(exec)

/**
 * Get the content of a function from a function reference using the parquet file
 * This is a hacky solution that reads the parquet file directly
 * @param functionRef The function reference string
 * @returns The function content, or an error message if not found
 */
export async function getFunctionContentHacky(functionRef: string): Promise<string> {
    // Check if the function content is already in the cache
    if (functionContentCache[functionRef]) {
        console.log(`Cache hit for function: ${functionRef}`)
        return functionContentCache[functionRef]
    }

    // Call the batch version with a single function reference
    const results = await getFunctionContentsHacky([functionRef])
    const result = results[functionRef] || `Error: Could not find function "${functionRef}" in parquet file`

    // Store the result in the cache
    functionContentCache[functionRef] = result

    return result
}

/**
 * Process a batch of function references for the context engine
 * This is optimized for the context engine which needs to process many functions at once
 * @param functionRefs Array of function reference strings
 * @returns Object mapping function references to their content
 */
export async function processFunctionBatch(functionRefs: string[]): Promise<Record<string, string>> {
    console.log(`Processing batch of ${functionRefs.length} functions`)

    // Check which functions are already in the cache
    const cachedFunctions: Record<string, string> = {}
    const uncachedFunctionRefs: string[] = []

    for (const funcRef of functionRefs) {
        if (functionContentCache[funcRef]) {
            cachedFunctions[funcRef] = functionContentCache[funcRef]
        } else {
            uncachedFunctionRefs.push(funcRef)
        }
    }

    console.log(`Cache hits: ${Object.keys(cachedFunctions).length}, cache misses: ${uncachedFunctionRefs.length}`)

    // If all functions are in the cache, return immediately
    if (uncachedFunctionRefs.length === 0) {
        console.log('All functions found in cache, skipping parquet lookup')
        return cachedFunctions
    }

    // Get content for uncached functions
    const uncachedResults = await getFunctionContentsHacky(uncachedFunctionRefs)

    // Store results in cache
    for (const funcRef of uncachedFunctionRefs) {
        if (uncachedResults[funcRef]) {
            functionContentCache[funcRef] = uncachedResults[funcRef]
        }
    }

    // Combine cached and uncached results
    return { ...cachedFunctions, ...uncachedResults }
}

// Flag to track if dependencies have been checked
let pythonDependenciesChecked = false;

// Cache for function content results
const functionContentCache: Record<string, string> = {};

/**
 * Clear the function content cache
 * @param functionRefs Optional array of function references to clear from the cache. If not provided, clears the entire cache.
 */
export function clearFunctionContentCache(functionRefs?: string[]): void {
    if (!functionRefs) {
        // Clear the entire cache
        console.log('Clearing entire function content cache')
        Object.keys(functionContentCache).forEach(key => delete functionContentCache[key])
    } else {
        // Clear only the specified function references
        console.log(`Clearing ${functionRefs.length} functions from cache`)
        functionRefs.forEach(funcRef => delete functionContentCache[funcRef])
    }
}

/**
 * Get statistics about the function content cache
 * @returns Object with cache statistics
 */
export function getFunctionContentCacheStats(): { size: number, keys: string[] } {
    return {
        size: Object.keys(functionContentCache).length,
        keys: Object.keys(functionContentCache)
    }
}

/**
 * Check if Python dependencies are installed and install them if needed
 */
async function ensurePythonDependencies(): Promise<boolean> {
    // If we've already checked dependencies, return true
    if (pythonDependenciesChecked) {
        return true;
    }

    try {
        // Try running a simple Python script that imports pandas and pyarrow
        await execAsync('python3 -c "import pandas, pyarrow; print(\'ok\')"')
        pythonDependenciesChecked = true;
        return true;
    } catch (error) {
        console.log("Python dependencies not installed, attempting to install...")
        try {
            // Show a notification to the user
            vscode.window.showInformationMessage("Installing required Python dependencies (pandas, pyarrow)...")

            // Install the dependencies
            const result = await execAsync('pip install pandas pyarrow')
            console.log("Dependency installation result:", result.stdout)

            vscode.window.showInformationMessage("Python dependencies installed successfully!")
            pythonDependenciesChecked = true;
            return true;
        } catch (installError) {
            console.error("Failed to install Python dependencies:", installError)
            vscode.window.showErrorMessage(`Failed to install Python dependencies: ${installError.message}`)
            return false;
        }
    }
}

/**
 * Get the content of multiple functions from function references using the parquet file
 * This is a hacky solution that reads the parquet file directly
 * @param functionRefs Array of function reference strings
 * @returns Object mapping function references to their content
 */
export async function getFunctionContentsHacky(functionRefs: string[]): Promise<Record<string, string>> {
    try {
        const startTime = Date.now();
        console.log(`Starting batch processing of ${functionRefs.length} function references`);

        // If no function references, return empty object
        if (!functionRefs || functionRefs.length === 0) {
            return {}
        }

        // Ensure Python dependencies are installed
        const dependenciesStartTime = Date.now();
        const dependenciesInstalled = await ensurePythonDependencies()
        console.log(`Dependencies check took ${Date.now() - dependenciesStartTime}ms`);

        if (!dependenciesInstalled) {
            return { error: "Failed to install required Python dependencies" }
        }

        const pythonScript = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_ext/src/utils/function_content_reader.py"

        // Escape function references for command line
        const escapedRefs = functionRefs.map(ref => `"${ref.replace(/"/g, '\\"')}"`).join(' ')

        // Execute the Python script with all function references as arguments
        console.log(`Executing Python script with ${functionRefs.length} function references`);
        const pythonStartTime = Date.now();
        const { stdout, stderr } = await execAsync(`python3 "${pythonScript}" ${escapedRefs}`)
        console.log(`Python script execution took ${Date.now() - pythonStartTime}ms`);

        if (stderr) {
            console.error(`Python script error: ${stderr}`)
        }

        // Parse the JSON output from the Python script
        const parseStartTime = Date.now();
        const results = JSON.parse(stdout)
        console.log(`JSON parsing took ${Date.now() - parseStartTime}ms`);

        // Check if there was an error
        if (results.error) {
            return { error: results.error }
        }

        // Format the output for each function reference
        const formatStartTime = Date.now();
        const formattedResults: Record<string, string> = {}

        for (const funcRef of functionRefs) {
            const result = results[funcRef]

            if (!result || !result.found) {
                formattedResults[funcRef] = `Error: Could not find function "${funcRef}" in parquet file`
            } else {
                const content = addLineNumbers(result.text, result.start_line)
                formattedResults[funcRef] = `Function: ${funcRef}\nFile: ${result.file_path}\nContent: \n${content}`
            }
        }
        console.log(`Formatting results took ${Date.now() - formatStartTime}ms`);
        console.log(`Total batch processing time: ${Date.now() - startTime}ms for ${functionRefs.length} functions`);

        return formattedResults
    } catch (error) {
        console.error("Error in getFunctionContentsHacky:", error)
        return { error: `Error getting function contents: ${error.message}` }
    }
}
