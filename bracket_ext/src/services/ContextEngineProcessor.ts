import * as vscode from "vscode"
import * as path from "path"
import * as fs from "fs"
import { spawn } from "child_process"
import { ContextEngineResult, ContextFunction } from "../shared/WebviewMessage"
import { ContextEngineService } from "./ContextEngineService"
import { getWorkspacePath } from "../utils/path"
import { getFunctionContentHacky, processFunctionBatch, clearFunctionContentCache, getFunctionContentCacheStats } from '../utils/function-content';
import { processFileBatch, clearFileContentCache, getFileContentCacheStats } from '../utils/file-content';
import { ReasoningShowcaseService } from "./ReasoningShowcaseService"
import { Tiktoken } from "js-tiktoken/lite"
import o200kBase from "js-tiktoken/ranks/o200k_base"

/**
 * Interface for basic file details
 */
interface BasicFileDetails {
    path: string;
    domain: string;
    relevance: number;
}

/**
 * Interface for enhanced file details with content information
 */
interface EnhancedFileDetails extends BasicFileDetails {
    content?: string;
}

/**
 * Interface for basic function details (kept for backward compatibility)
 */
interface BasicFunctionDetails {
    name: string;
    domain: string;
    relevance: number;
}

/**
 * Interface for enhanced function details with file path and line information (kept for backward compatibility)
 */
interface EnhancedFunctionDetails extends BasicFunctionDetails {
    file_path?: string;
    start_line?: number;
    end_line?: number;
    function_text?: string;
    is_file?: boolean; // Flag to indicate if this is a file rather than a function
}

/**
 * Service for processing context engine queries using the global_localisation.py script
 */
export class ContextEngineProcessor {
    private static instance: ContextEngineProcessor
    private contextEngineService: ContextEngineService
    private reasoningShowcaseService: ReasoningShowcaseService
    private isProcessing: boolean = false

    private constructor() {
        this.contextEngineService = ContextEngineService.getInstance()
        this.reasoningShowcaseService = ReasoningShowcaseService.getInstance()

        // Initialize the reasoning showcase service
        this.reasoningShowcaseService.initialize().catch(error => {
            console.error("Error initializing reasoning showcase service:", error)
        })
    }

    /**
     * Get the singleton instance of the ContextEngineProcessor
     */
    public static getInstance(): ContextEngineProcessor {
        if (!ContextEngineProcessor.instance) {
            ContextEngineProcessor.instance = new ContextEngineProcessor()
        }
        return ContextEngineProcessor.instance
    }

    /**
     * Clear all context functions and reset the processor state
     */
    public clearContextFunctions(): void {
        // Clear context functions in the ContextEngineService
        this.contextEngineService.clearContextFunctions()

        // Reset processing state
        this.isProcessing = false
    }

    /**
     * Process a query using the global_localisation.py script
     * @param query The user's query
     * @param clearCache Whether to clear the function content cache before processing the query
     */
    public async processQuery(query: string, clearCache: boolean = false): Promise<void> {
        if (this.isProcessing) {
            this.contextEngineService.updateContextEngine({
                content: "A query is already being processed. Please wait for it to complete.",
                isSuccess: false
            })
            return
        }

        this.isProcessing = true

        // Clear the cache if requested
        if (clearCache) {
            console.log('Clearing function content cache before processing query')
            this.clearFunctionCache()

            // Update the UI to show that the cache was cleared
            this.contextEngineService.updateContextEngine({
                content: "Cleared function content cache. Starting Bracket Context Engine...",
                isLoading: true
            })
        }

        try {
            // Show initial loading state immediately
            this.contextEngineService.updateContextEngine({
                content: "Starting Bracket Context Engine...",
                isLoading: true
            })

            // We don't need to start the processing visualization anymore
            // as the ContextEngineService will handle the reasoning showcase
            // this.startProcessingVisualization(query)

            // Get workspace root - this is fast and needed for script execution
            let workspaceRoot = getWorkspacePath()

            // If that fails, try to get the parent directory of the current file
            if (!workspaceRoot) {
                // Try to find the bracket_prod directory by going up from the current file
                const currentDir = __dirname
                const parts = currentDir.split(path.sep)
                const bracketProdIndex = parts.findIndex(part => part === 'bracket_prod')

                if (bracketProdIndex >= 0) {
                    // Reconstruct the path up to bracket_prod
                    workspaceRoot = parts.slice(0, bracketProdIndex + 1).join(path.sep)
                    console.log(`Using detected bracket_prod directory: ${workspaceRoot}`)
                }
            }

            if (!workspaceRoot) {
                this.contextEngineService.updateContextEngine({
                    content: "No workspace root found. Please open a folder or workspace.",
                    isSuccess: false
                })
                this.isProcessing = false
                return
            }

            // Check if global_localization.py exists - use hardcoded paths for speed
            const localizationPath = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/localisation/global_localisation.py"
            const alternativePath = path.join(workspaceRoot, "bracket_core", "localization", "global_localization.py")
            // const taxonomyPath = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/domain_taxonomy.json"
            // const taxonomyPath = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/outputs/django/domain_file_taxonomy/taxonomy.json"
            // const taxonomyPath = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/mermaid_outputs_json/gitlab/taxonomy1.json"
            const taxonomyPath = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/outputs/datadog_agent/taxonomy/taxonomy.json"

            const scriptPath = fs.existsSync(localizationPath) ? localizationPath :
                               fs.existsSync(alternativePath) ? alternativePath : null

            console.log(`Selected script path: ${scriptPath}`)

            // Immediately run the script if found, otherwise use mock implementation
            if (scriptPath && fs.existsSync(taxonomyPath)) {
                // Call the global_localization.py script immediately
                await this.runGlobalLocalization(query, scriptPath, taxonomyPath, workspaceRoot)
            } else {
                console.log(scriptPath ? "Taxonomy file not found" : "Script not found", "using mock implementation")

                // Generate mock results (don't wait for simulation to complete)
                // Note: We don't use the mock result directly, it's just for logging
                this.generateMockResult(query)

                // Update the context engine with the mock results
                setTimeout(async () => {
                    const mockResult = await getFunctionContentHacky(query);

                    // Create mock context functions
                    const mockContextFunctions: ContextFunction[] = [
                        {
                            name: "processQuery",
                            file_path: "src/services/ContextEngineProcessor.ts",
                            start_line: 65,
                            end_line: 169,
                            function_text: "public async processQuery(query: string, clearCache: boolean = false): Promise<void> { ... }",
                            relevance: 9
                        },
                        {
                            name: "updateContextEngine",
                            file_path: "src/services/ContextEngineService.ts",
                            start_line: 41,
                            end_line: 54,
                            function_text: "public updateContextEngine(data: ContextEngineResult): void { ... }",
                            relevance: 8
                        },
                        {
                            name: "setContextFunctions",
                            file_path: "src/services/ContextEngineService.ts",
                            start_line: 142,
                            end_line: 145,
                            function_text: "public setContextFunctions(functions: ContextFunction[]): void { ... }",
                            relevance: 7
                        }
                    ];

                    // Create mock context files
                    const mockContextFiles = [
                        {
                            path: "src/services/ContextEngineProcessor.ts",
                            content: "// Mock content for ContextEngineProcessor.ts",
                            domain: "Context Engine",
                            relevance: 9
                        },
                        {
                            path: "src/services/ContextEngineService.ts",
                            content: "// Mock content for ContextEngineService.ts",
                            domain: "Context Engine",
                            relevance: 8
                        },
                        {
                            path: "src/shared/WebviewMessage.ts",
                            content: "// Mock content for WebviewMessage.ts",
                            domain: "Shared Types",
                            relevance: 7
                        }
                    ];

                    // Set the context functions and files
                    this.contextEngineService.setContextFunctions(mockContextFunctions);
                    this.contextEngineService.setContextFiles(mockContextFiles);

                    this.contextEngineService.updateContextEngine({
                        content: mockResult,
                        isSuccess: true,
                        isLoading: false,
                        contextFunctions: mockContextFunctions,
                        contextFiles: mockContextFiles
                    })

                    console.log("Context engine processing complete, notifying services")
                    // Notify that processing is complete
                    this.contextEngineService.notifyProcessingComplete()

                    // Also notify the reasoning showcase service
                    this.reasoningShowcaseService.notifyProcessingComplete()

                    this.isProcessing = false
                }, 3000) // Short delay to make it look like processing happened
            }
        } catch (error) {
            console.error("Error processing query:", error)
            this.contextEngineService.updateContextEngine({
                content: `Error processing query: ${error instanceof Error ? error.message : String(error)}`,
                isSuccess: false
            })
            this.isProcessing = false
        }
    }

    // Removed unused startProcessingVisualization method

    /**
     * Run the global_localization.py script
     * @param query The user's query
     * @param scriptPath Path to the global_localization.py script
     * @param taxonomyPath Path to the domain_taxonomy.json file
     * @param workspaceRoot Path to the workspace root
     */
    private async runGlobalLocalization(query: string, scriptPath: string, taxonomyPath: string, workspaceRoot: string): Promise<void> {
        // Prepare the command to run the script - use cached value for speed
        const pythonExecutable = "python3" // Fast default instead of calling getPythonExecutable()
        console.log(`Using Python executable: ${pythonExecutable}`)

        // Create a temporary file for the query
        const queryFilePath = path.join(workspaceRoot, "temp_query.txt")
        fs.writeFileSync(queryFilePath, query)
        console.log(`Created temporary query file at: ${queryFilePath}`)

        // Prepare the arguments for the script
        // const outputDir = path.join(workspaceRoot, "bracket_core", "localisation", "qa_data")
        const outputDir = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/localisation/qa_data"
        const args = [
            scriptPath,
            "--query", query,
            "--taxonomy", taxonomyPath,
            "--use-two-pass",
            "--output-dir", outputDir
        ]

        console.log(`Running script with arguments:`);
        console.log(`- Script path: ${scriptPath}`);
        console.log(`- Query: ${query}`);
        console.log(`- Taxonomy path: ${taxonomyPath}`);
        console.log(`- Output directory: ${outputDir}`);

        // Update UI to show we're running the script (but don't wait for this to complete)
        // We don't need to update the UI here as the ContextEngineService will handle the reasoning showcase

        // Run the script
        const process = spawn(pythonExecutable, args, {
            cwd: workspaceRoot
        })

        let stdout = ""
        let stderr = ""

        process.stdout.on("data", (data) => {
            const chunk = data.toString();
            console.log(`Received stdout chunk: ${chunk.substring(0, 200)}${chunk.length > 200 ? '...' : ''}`);
            stdout += chunk;

            // Update with intermediate results if available
            this.processIntermediateOutput(stdout);

            // Pass the stdout data to the reasoning showcase service
            this.reasoningShowcaseService.processStdoutData(chunk).catch(error => {
                console.error("Error processing stdout in reasoning showcase:", error);
            });
        })

        process.stderr.on("data", (data) => {
            const chunk = data.toString();
            console.log(`Received stderr chunk: ${chunk}`);
            stderr += chunk;
        })

        process.on("close", async (code) => {
            console.log(`Process closed with code: ${code}`);

            // Clean up the temporary file
            if (fs.existsSync(queryFilePath)) {
                fs.unlinkSync(queryFilePath);
                console.log(`Removed temporary query file: ${queryFilePath}`);
            }

            // Save stdout and stderr to files for debugging
            const timestamp = new Date().toISOString().replace(/[:.]/g, '');
            const stdoutPath = path.join(
                "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/localisation/qa_data",
                `stdout_${timestamp}.txt`
            );
            const stderrPath = path.join(
                "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/localisation/qa_data",
                `stderr_${timestamp}.txt`
            );

            fs.writeFileSync(stdoutPath, stdout);
            fs.writeFileSync(stderrPath, stderr);
            console.log(`Saved stdout to ${stdoutPath}`);
            console.log(`Saved stderr to ${stderrPath}`);

            if (code === 0) {
                // Process was successful
                console.log(`Global localization process completed successfully`);
                await this.processSuccessfulResult(stdout, query);
            } else {
                // Process failed
                console.error(`Global localization process exited with code ${code}`);
                console.error(`stderr: ${stderr}`);

                this.contextEngineService.updateContextEngine({
                    content: `## Error Running Context Analysis\n\n` +
                             `The context analysis process encountered an error.\n\n` +
                             `Error details:\n\`\`\`\n${stderr}\n\`\`\`\n\n` +
                             `Please try again with a more specific query.`,
                    isSuccess: false
                })

                console.log("Context engine processing error, notifying services")
                // Notify that processing is complete even in case of error
                this.contextEngineService.notifyProcessingComplete()

                // Also notify the reasoning showcase service
                this.reasoningShowcaseService.notifyProcessingComplete()
            }

            this.isProcessing = false
        })
    }

    /**
     * Process intermediate output from the global_localization.py script
     * @param output The output from the script
     */
    private processIntermediateOutput(output: string): void {
        // Look for specific markers in the output to provide updates
        if (output.includes("Time taken for step 1: domain relevance")) {
            this.contextEngineService.updateContextEngine({
                content: `## Context Analysis in Progress\n\n` +
                         `✅ **Phase 1 Complete**: Domain relevance analysis finished\n\n` +
                         `🔄 **Phase 2 In Progress**: Analyzing function implementations...\n\n` +
                         `The context engine has identified relevant domains in the codebase and is now analyzing specific functions.`,
                isLoading: true
            })
        } else if (output.includes("Time taken for step 2: functions relevance")) {
            this.contextEngineService.updateContextEngine({
                content: `## Context Analysis in Progress\n\n` +
                         `✅ **Phase 1 Complete**: Domain relevance analysis finished\n\n` +
                         `✅ **Phase 2 Complete**: Function relevance analysis finished\n\n` +
                         `🔄 **Phase 3 In Progress**: Filtering and ranking results...\n\n` +
                         `The context engine has identified relevant functions and is now filtering and ranking the results.`,
                isLoading: true
            })
        }
    }

    /**
     * Process the successful result from the global_localization.py script
     * @param output The output from the script
     * @param query The user's query
     */
    private async processSuccessfulResult(output: string, query: string): Promise<void> {
        try {
            // Save the raw output to a file for debugging
            const timestamp = new Date().toISOString().replace(/[:.]/g, '');
            const outputPath = path.join(
                "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/localisation/qa_data",
                `output_${timestamp}.txt`
            );
            fs.writeFileSync(outputPath, output);
            console.log(`Saved raw output to ${outputPath}`);

            // Log the output for debugging
            console.log("Raw output from global_localisation.py:");
            console.log(output);

            // Try to extract the JSON output from the Python script
            const jsonMatch = output.match(/JSON_OUTPUT_START\n([\s\S]*?)\nJSON_OUTPUT_END/);

            if (jsonMatch && jsonMatch[1]) {
                try {
                    // Parse the JSON output
                    const jsonOutput = JSON.parse(jsonMatch[1]);
                    console.log("Successfully parsed JSON output from Python script");

                    // Extract the relevant files (primary data structure with file-level granularity)
                    const relevantFiles = jsonOutput.relevant_files || [];
                    const numRelevantFiles = relevantFiles.length;
                    console.log(`Found ${numRelevantFiles} relevant files from JSON output`);

                    // Extract functions for backward compatibility
                    const relevantFunctions = jsonOutput.relevant_functions || [];
                    const numRelevantFunctions = relevantFunctions.length;
                    console.log(`Extracted ${numRelevantFunctions} relevant functions from JSON output`);

                    // Process files first (primary data structure)
                    let enhancedFileDetails: any[] = [];

                    if (numRelevantFiles > 0) {
                        // Create basic file details for processing
                        const fileDetails: BasicFileDetails[] = relevantFiles.map((file: any) => ({
                            path: file.file_path,
                            domain: file.domain_trace,
                            relevance: file.relevance_score
                        }));

                        // Log the number of files before filtering
                        console.log(`Processing ${fileDetails.length} files from global localization`);

                        // Extract file content - this will filter by relevance score and apply token limits
                        const fileContents = await this.extractRelevantFilesContent(fileDetails);

                        // Log the number of files after filtering
                        console.log(`After filtering by relevance and token limits: ${Object.keys(fileContents.contentMap).length} files`);

                        // Store the relevant files in the context engine service
                        this.contextEngineService.setRelevantFilesContentMap(fileContents.contentMap);

                        // Create enhanced file details for the UI - only for files that passed the filtering
                        enhancedFileDetails = [];
                        for (const file of relevantFiles) {
                            // Only include files that passed the filtering
                            if (fileContents.contentMap[file.file_path]) {
                                enhancedFileDetails.push({
                                    path: file.file_path,
                                    domain: file.domain_trace,
                                    relevance: file.relevance_score,
                                    content: fileContents.contentMap[file.file_path]
                                });
                            }
                        }

                        // Store the context files with metadata
                        this.contextEngineService.setContextFiles(enhancedFileDetails);
                        console.log(`Set ${enhancedFileDetails.length} context files with metadata`);
                    }

                    // Process functions for backward compatibility
                    // Create enhanced function details for direct use
                    const enhancedFunctionDetails: EnhancedFunctionDetails[] = relevantFunctions.map((func: any) => ({
                        name: func.function_name,
                        domain: func.domain_trace,
                        relevance: func.relevance_score,
                        file_path: func.file_path,
                        start_line: func.start_line,
                        end_line: func.end_line,
                        function_text: func.function_text
                    }));

                    // Create a content map directly from the enhanced function details
                    const functionContentMap: Record<string, string> = {};
                    for (const func of enhancedFunctionDetails) {
                        const formattedContent = `Function: ${func.name}\nFile: ${func.file_path}\nContent: \n${func.function_text}`;
                        functionContentMap[func.name] = formattedContent;
                    }

                    // Store the relevant functions in the context engine service
                    this.contextEngineService.setRelevantFnsNameCodeMap(functionContentMap);

                    // Convert file details to function-compatible format for the UI
                    // This is necessary because the InjectedContextShowcase component expects ContextFunction objects
                    // Only include files that passed the relevance and token limit filtering
                    const fileAsFunctions: EnhancedFunctionDetails[] = enhancedFileDetails.map((file: any) => ({
                        name: file.path, // Use file path as function name
                        domain: file.domain,
                        relevance: file.relevance,
                        file_path: file.path,
                        start_line: 1,
                        end_line: file.content ? file.content.split('\n').length : 100,
                        function_text: file.content || `Content for ${file.path} not available`,
                        is_file: true // Mark as file for differentiation in UI
                    }));

                    // Combine file-as-functions with actual functions, prioritizing files
                    const combinedContextItems = [...fileAsFunctions, ...enhancedFunctionDetails];

                    // Store the combined items as context functions with metadata
                    this.contextEngineService.setContextFunctions(combinedContextItems);

                    // Extract reasoning if available
                    let reasoning = "";
                    const reasoningMatch = output.match(/Reasoning for selection:([\s\S]*?)(?=\n\nFull third pass results|$)/);
                    if (reasoningMatch) {
                        reasoning = reasoningMatch[1].trim();
                        console.log(`Extracted reasoning: ${reasoning.substring(0, 100)}...`);
                    }

                    // Generate a summary of the results
                    const summary = this.generateResultSummary(query, numRelevantFunctions, combinedContextItems, reasoning);

                    // Update the context engine with the results
                    this.contextEngineService.updateContextEngine({
                        content: summary,
                        isSuccess: true,
                        isLoading: false,
                        contextFunctions: combinedContextItems,
                        contextFiles: this.contextEngineService.getContextFiles()
                    });

                    console.log("Context engine processing complete, notifying services");
                    // Notify that processing is complete
                    this.contextEngineService.notifyProcessingComplete();

                    // Also notify the reasoning showcase service
                    this.reasoningShowcaseService.notifyProcessingComplete();
                    return;
                } catch (jsonError) {
                    console.error("Error parsing JSON output:", jsonError);
                    // Fall through to the legacy parsing method
                }
            } else {
                console.log("No JSON output found, falling back to regex parsing");
            }

            // Ideally we should never reach here and should return from up there.
            // Legacy parsing method using regex
            // Extract the relevant functions from the output
            let relevantFunctionsMatch = output.match(/Found (\d+) relevant functions:/);
            if (!relevantFunctionsMatch) {
                relevantFunctionsMatch = output.match(/Identified (\d+) relevant functions/);
            }
            if (!relevantFunctionsMatch) {
                relevantFunctionsMatch = output.match(/Selected (\d+) functions/);
            }

            const numRelevantFunctions = relevantFunctionsMatch ? parseInt(relevantFunctionsMatch[1]) : 0;
            console.log(`Extracted numRelevantFunctions: ${numRelevantFunctions}`);

            // Extract the function details
            const functionDetails: BasicFunctionDetails[] = [];

            // Try different regex patterns to match function details
            const functionRegexPatterns = [
                /(\d+)\. ([^\n]+)\s+Domain: ([^\n]+)\s+Relevance: (\d+)\/10/g,
                /(\d+)\. ([^\n]+)\s+\(Domain: ([^\n]+)\)\s+Relevance: (\d+)\/10/g,
                /(\d+)\. ([^\n]+) - Domain: ([^\n]+), Relevance: (\d+)\/10/g
            ];

            let anyMatches = false;

            for (const regex of functionRegexPatterns) {
                let regexMatch;
                while ((regexMatch = regex.exec(output)) !== null) {
                    const func = {
                        name: regexMatch[2].trim(),
                        domain: regexMatch[3].trim(),
                        relevance: parseInt(regexMatch[4])
                    };
                    functionDetails.push(func);
                    console.log(`Extracted function: ${JSON.stringify(func)}`);
                    anyMatches = true;
                }
            }

            if (!anyMatches) {
                console.log("No function details matched with any of the regex patterns");
            }

            // Extract reasoning if available
            let reasoning = "";
            const reasoningMatch = output.match(/Reasoning for selection:([\s\S]*?)(?=\n\nFull third pass results|$)/);
            if (reasoningMatch) {
                reasoning = reasoningMatch[1].trim();
                console.log(`Extracted reasoning: ${reasoning.substring(0, 100)}...`);
            } else {
                console.log("No reasoning found in output");
            }

            // Extract the relevant function code
            // This is where we extract the actual code for the functions
            // The function has been optimized with caching to improve performance
            const relevantFunctions = await this.extractRelevantFunctionsCode(functionDetails);

            // Store the relevant functions in the context engine service
            this.contextEngineService.setRelevantFnsNameCodeMap(relevantFunctions.contentMap);

            // Create enhanced function details for the legacy parsing method
            // Since we don't have file paths and line numbers from the regex parsing,
            // we'll create enhanced function details with just the basic information
            const enhancedDetails: EnhancedFunctionDetails[] = functionDetails.map(func => ({
                ...func,
                file_path: undefined,
                start_line: undefined,
                end_line: undefined,
                function_text: undefined
            }));

            // Store the context functions with metadata
            this.contextEngineService.setContextFunctions(enhancedDetails);



            // Generate a summary of the results
            const summary = this.generateResultSummary(query, numRelevantFunctions, enhancedDetails, reasoning);

            // TODO -> instead of generating a summary and sending that we need to do better here: Look into Cursor
            // Update the context engine with the results
            this.contextEngineService.updateContextEngine({
                content: summary,
                isSuccess: true,
                isLoading: false,
                contextFunctions: enhancedDetails,
                contextFiles: this.contextEngineService.getContextFiles()
            });

            console.log("Context engine processing complete, notifying services")
            // Notify that processing is complete
            this.contextEngineService.notifyProcessingComplete();

            // Also notify the reasoning showcase service
            this.reasoningShowcaseService.notifyProcessingComplete();
        } catch (error) {
            console.error("Error processing successful result:", error);

            // Fallback to a simpler result
            this.contextEngineService.updateContextEngine({
                content: `## Context Analysis Complete\n\n` +
                        `The context engine has analyzed your query and found relevant code.\n\n` +
                        `Raw output:\n\`\`\`\n${output}\n\`\`\``,
                isSuccess: true,
                isLoading: false,
                contextFiles: this.contextEngineService.getContextFiles(),
                contextFunctions: this.contextEngineService.getContextFunctions()
            });

            console.log("Context engine processing complete (fallback), notifying services")
            // Notify that processing is complete
            this.contextEngineService.notifyProcessingComplete();

            // Also notify the reasoning showcase service
            this.reasoningShowcaseService.notifyProcessingComplete();
        }
    }



    /**
     * Generate a summary of the results
     * @param query The user's query
     * @param numRelevantFunctions The number of relevant functions
     * @param functionDetails The details of the relevant functions
     * @param reasoning The reasoning for the selection
     */
    private generateResultSummary(
        query: string,
        _numRelevantFunctions: number, // Unused parameter, kept for backward compatibility
        functionDetails: EnhancedFunctionDetails[],
        reasoning: string
    ): string {
        // We don't need to get context files separately anymore as we're using the combined items

        // Group functions by domain
        const domainGroups: { [domain: string]: { name: string, relevance: number, isFile?: boolean }[] } = {};

        // Process all items (files and functions)
        functionDetails.forEach(item => {
            const domain = item.domain || 'Unknown Domain';
            if (!domainGroups[domain]) {
                domainGroups[domain] = [];
            }

            // Check if this is a file or a function
            const isFile = item.is_file === true;

            domainGroups[domain].push({
                name: item.name,
                relevance: item.relevance || 0,
                isFile: isFile
            });
        });

        // Generate the summary
        let summary = `## Context Analysis for: "${query}"\n\n`;

        // Count files and functions in the combined items
        const fileCount = functionDetails.filter(item => item.is_file === true).length;
        const functionCount = functionDetails.filter(item => item.is_file !== true).length;

        // Add a brief explanation of what the context engine did
        if (fileCount > 0 && functionCount > 0) {
            summary += `The Bracket Context Engine analyzed your query across the codebase's domain structure and identified ${fileCount} relevant files and ${functionCount} relevant functions.\n\n`;
        } else if (fileCount > 0) {
            summary += `The Bracket Context Engine analyzed your query across the codebase's domain structure and identified ${fileCount} relevant files.\n\n`;
        } else if (functionCount > 0) {
            summary += `The Bracket Context Engine analyzed your query across the codebase's domain structure and identified ${functionCount} relevant functions.\n\n`;
        } else {
            summary += `The Bracket Context Engine analyzed your query across the codebase's domain structure.\n\n`;
        }

        // Add the reasoning if available
        if (reasoning) {
            summary += `### Analysis Reasoning\n\n${reasoning}\n\n`;
        }

        // Add the domain groups
        summary += `### Relevant Code Paths\n\n`;

        Object.entries(domainGroups).forEach(([domain, items]) => {
            summary += `#### ${domain}\n\n`;

            items.forEach(item => {
                if (item.isFile) {
                    // This is a file
                    summary += `- File: \`${item.name}\` (Relevance: ${(item.relevance/10).toFixed(1)})\n`;
                } else {
                    // This is a function (for backward compatibility)
                    // Find the enhanced details for this function
                    const enhancedFunc = functionDetails.find(f => f.name === item.name);
                    if (enhancedFunc && enhancedFunc.file_path) {
                        summary += `- Function: \`${item.name}\` (Relevance: ${(item.relevance/10).toFixed(1)}) - ${enhancedFunc.file_path}:${enhancedFunc.start_line}-${enhancedFunc.end_line}\n`;
                    } else {
                        summary += `- Function: \`${item.name}\` (Relevance: ${(item.relevance/10).toFixed(1)})\n`;
                    }
                }
            });

            summary += `\n`;
        });

        // Add a conclusion
        summary += `### Next Steps\n\n`;
        if (fileCount > 0 && functionCount > 0) {
            summary += `- Explore the identified files and functions to understand the implementation details\n`;
        } else if (fileCount > 0) {
            summary += `- Explore the identified files to understand the implementation details\n`;
        } else {
            summary += `- Explore the identified functions to understand the implementation details\n`;
        }
        summary += `- Consider how these components interact within their domains\n`;
        summary += `- Ask follow-up questions about specific files, functions, or domains\n`;

        return summary;
    }

    /**
     * Generate mock results for when the script is not available
     * @param query The user's query
     */
    private async generateMockResult(query: string): Promise<string> {
        // Create some mock domains and functions based on the query
        const domains = [
            "UI Components",
            "Data Processing",
            "API Integration",
            "Authentication",
            "Database"
        ];

        // Select 2-3 random domains that would be relevant
        const selectedDomains = domains
            .sort(() => 0.5 - Math.random())
            .slice(0, Math.floor(Math.random() * 2) + 2);

        // Create mock functions for each domain
        const domainGroups: { [domain: string]: { name: string, relevance: number }[] } = {};

        selectedDomains.forEach(domain => {
            domainGroups[domain] = [];

            // Generate 2-4 functions per domain
            const numFunctions = Math.floor(Math.random() * 3) + 2;

            for (let i = 0; i < numFunctions; i++) {
                let functionName = "";

                // Generate function names based on domain
                switch (domain) {
                    case "UI Components":
                        functionName = ["render", "update", "create", "handle"][i % 4] +
                                     ["Component", "View", "Element", "Widget"][Math.floor(Math.random() * 4)];
                        break;
                    case "Data Processing":
                        functionName = ["process", "transform", "filter", "analyze"][i % 4] +
                                     ["Data", "Results", "Input", "Output"][Math.floor(Math.random() * 4)];
                        break;
                    case "API Integration":
                        functionName = ["fetch", "post", "update", "delete"][i % 4] +
                                     ["Resource", "Entity", "Item", "Record"][Math.floor(Math.random() * 4)];
                        break;
                    case "Authentication":
                        functionName = ["validate", "authenticate", "authorize", "verify"][i % 4] +
                                     ["User", "Token", "Credentials", "Session"][Math.floor(Math.random() * 4)];
                        break;
                    case "Database":
                        functionName = ["query", "insert", "update", "delete"][i % 4] +
                                     ["Record", "Entity", "Document", "Row"][Math.floor(Math.random() * 4)];
                        break;
                }

                domainGroups[domain].push({
                    name: functionName,
                    relevance: Math.floor(Math.random() * 3) + 7 // Relevance between 7-10
                });
            }
        });

        // Create an array of function details for extracting code
        const functionDetails: BasicFunctionDetails[] = [];
        const enhancedDetails: EnhancedFunctionDetails[] = [];
        Object.entries(domainGroups).forEach(([domain, functions]) => {
            functions.forEach(func => {
                const basicDetails = {
                    name: func.name,
                    domain: domain,
                    relevance: func.relevance
                };
                functionDetails.push(basicDetails);

                // Create mock enhanced details with fake file paths and line numbers
                enhancedDetails.push({
                    ...basicDetails,
                    file_path: `src/${domain.toLowerCase().replace(/\s+/g, '_')}/${func.name.toLowerCase()}.ts`,
                    start_line: Math.floor(Math.random() * 100) + 1,
                    end_line: Math.floor(Math.random() * 100) + 101,
                    function_text: `function ${func.name}() { /* Mock function */ }`
                });
            });
        });

        // Extract the relevant function code
        const relevantFunctions = await this.extractRelevantFunctionsCode(functionDetails);

        // Store the relevant functions in the context engine service
        this.contextEngineService.setRelevantFnsNameCodeMap(relevantFunctions.contentMap);

        // Store the context functions with metadata
        this.contextEngineService.setContextFunctions(enhancedDetails);

        // Generate the summary
        let summary = `## Context Analysis for: "${query}"

`;

        // Add a brief explanation of what the context engine did
        const totalFunctions = Object.values(domainGroups).reduce((sum, funcs) => sum + funcs.length, 0);
        summary += `The Bracket Context Engine analyzed your query across the codebase's domain structure and identified ${totalFunctions} relevant functions.

`;

        // Add mock reasoning
        summary += `### Analysis Reasoning

`;
        summary += `Based on the query "${query}", the context engine identified key concepts and mapped them to relevant domains in the codebase. `;
        summary += `The most significant matches were found in the ${selectedDomains.join(", ")} domains, `;
        summary += `which contain implementations that are likely to be relevant to your question.

`;

        // Add the domain groups
        summary += `### Relevant Code Paths

`;

        Object.entries(domainGroups).forEach(([domain, functions]) => {
            summary += `#### ${domain}

`;

            functions.forEach(func => {
                // Find the enhanced details for this function
                const enhancedFunc = enhancedDetails.find(f => f.name === func.name);
                if (enhancedFunc && enhancedFunc.file_path) {
                    summary += `- \`${func.name}\` (Relevance: ${func.relevance}/10) - ${enhancedFunc.file_path}:${enhancedFunc.start_line}-${enhancedFunc.end_line}
`;
                } else {
                    summary += `- \`${func.name}\` (Relevance: ${func.relevance}/10)
`;
                }
            });

            summary += `
`;
        });

        // Add a conclusion
        summary += `### Next Steps

`;
        summary += `- Explore the identified functions to understand the implementation details
`;
        summary += `- Consider how these components interact within their domains
`;
        summary += `- Ask follow-up questions about specific functions or domains
`;

        return summary;
    }

    /**
     * Extract relevant function code from function details
     * @param functionDetails Array of function details with name, domain, and relevance
     * @returns Array of function code strings
     */
    /**
     * Clear the function content cache
     * @param functionRefs Optional array of function references to clear from the cache. If not provided, clears the entire cache.
     */
    public clearFunctionCache(functionRefs?: string[]): void {
        clearFunctionContentCache(functionRefs);
    }

    /**
     * Clear the file content cache
     * @param filePaths Optional array of file paths to clear from the cache. If not provided, clears the entire cache.
     */
    public clearFileCache(filePaths?: string[]): void {
        clearFileContentCache(filePaths);
    }

    /**
     * Get statistics about the function content cache
     * @returns Object with cache statistics
     */
    public getFunctionCacheStats(): { size: number, keys: string[] } {
        return getFunctionContentCacheStats();
    }

    /**
     * Get statistics about the file content cache
     * @returns Object with cache statistics
     */
    public getFileCacheStats(): { size: number, keys: string[] } {
        return getFileContentCacheStats();
    }

    /**
     * Trim content to fit within token limit
     * @param contentMap Map of file paths to file content
     * @param maxTokens Maximum number of tokens to allow (default: 100K)
     * @returns Trimmed content map
     */
    private trimContentToTokenLimit(contentMap: Record<string, string>, maxTokens: number = 100000): Record<string, string> {
        const startTime = Date.now();
        console.log(`Trimming content to fit within ${maxTokens} tokens`);

        // Create a copy of the content map to avoid modifying the original
        const trimmedContentMap: Record<string, string> = {};

        // Create an array of [path, content, relevance] tuples for sorting
        const contentItems: [string, string, number][] = [];

        // Extract relevance scores from file paths (assuming they're in the content)
        for (const [path, content] of Object.entries(contentMap)) {
            // Try to extract relevance score from content
            // Format is typically "File: path\n\nContent..."
            let relevance = 0;

            // Look for relevance in the first few lines
            const lines = content.split('\n', 10);
            for (const line of lines) {
                const relevanceMatch = line.match(/Relevance: (\d+(\.\d+)?)/);
                if (relevanceMatch) {
                    relevance = parseFloat(relevanceMatch[1]);
                    break;
                }
            }

            // If no relevance found, assume it's from the original map and use a default high value
            if (relevance === 0) {
                relevance = 9.0; // Default high relevance
            }

            contentItems.push([path, content, relevance]);
        }

        // Sort by relevance score in descending order
        contentItems.sort((a, b) => b[2] - a[2]);

        // Initialize the tiktoken encoder
        const encoder = new Tiktoken(o200kBase);

        // Track total tokens
        let totalTokens = 0;

        // Add content items until we reach the token limit
        for (const [path, content, relevance] of contentItems) {
            // Count tokens in this content
            const tokens = encoder.encode(content);
            const tokenCount = tokens.length;

            // If adding this content would exceed the token limit, skip it
            if (totalTokens + tokenCount > maxTokens) {
                console.log(`Skipping ${path} (${tokenCount} tokens) as it would exceed the token limit`);
                continue;
            }

            // Add this content to the trimmed map
            trimmedContentMap[path] = content;
            totalTokens += tokenCount;

            console.log(`Added ${path} (${tokenCount} tokens, relevance: ${relevance}), total: ${totalTokens}/${maxTokens}`);

            // If we've reached the token limit, stop adding content
            if (totalTokens >= maxTokens) {
                console.log(`Reached token limit of ${maxTokens}, stopping`);
                break;
            }
        }

        console.log(`Trimmed content from ${Object.keys(contentMap).length} to ${Object.keys(trimmedContentMap).length} files`);
        console.log(`Total tokens: ${totalTokens}/${maxTokens}`);
        console.log(`Trimming took ${Date.now() - startTime}ms`);

        return trimmedContentMap;
    }

    private async extractRelevantFunctionsCode(functionDetails: BasicFunctionDetails[]): Promise<{ contentList: string[], contentMap: Record<string, string> }> {
        const startTime = Date.now();
        console.log(`Extracting code for ${functionDetails.length} functions`);

        const functionNodeIds = functionDetails.map(func => func.name);
        const contentMap: Record<string, string> = {};

        try {
            // Process all function references in a single batch
            console.log(`Processing batch of ${functionNodeIds.length} functions`);
            const batchStartTime = Date.now();
            const batchResults = await processFunctionBatch(functionNodeIds);
            console.log(`Batch processing took ${Date.now() - batchStartTime}ms`);

            // Convert the batch results to the expected format
            const formatStartTime = Date.now();
            const functionContents = functionNodeIds.map(functionRef => {
                const content = batchResults[functionRef] ||
                    `Error: Could not find function "${functionRef}" in parquet file`;
                contentMap[functionRef] = content;
                return content;
            });
            console.log(`Formatting results took ${Date.now() - formatStartTime}ms`);

            // Log cache statistics after processing
            const cacheStats = getFunctionContentCacheStats();
            console.log(`Function cache size: ${cacheStats.size} entries`);

            console.log(`Extracted ${functionContents.length} function contents in ${Date.now() - startTime}ms total`);
            return {
                contentList: functionContents,
                contentMap: contentMap
            };
        } catch (error) {
            console.error("Error fetching function contents:", error);
            const errorResults = functionNodeIds.map(id => `Error: Could not extract content for ${id}`);
            functionNodeIds.forEach((id, index) => {
                contentMap[id] = errorResults[index];
            });
            return {
                contentList: errorResults,
                contentMap: contentMap
            };
        }
    }

    /**
     * Extract relevant file content from file details
     * @param fileDetails Array of file details with path, domain, and relevance
     * @returns Object with content list and content map
     */
    private async extractRelevantFilesContent(fileDetails: BasicFileDetails[]): Promise<{ contentList: string[], contentMap: Record<string, string> }> {
        const startTime = Date.now();
        console.log(`Extracting content for ${fileDetails.length} files`);

        // Filter files with relevance score >= 0.9 (9/10)
        const RELEVANCE_THRESHOLD = 0.9;
        const filteredFileDetails = fileDetails.filter(file => file.relevance / 10 >= RELEVANCE_THRESHOLD);
        console.log(`Filtered to ${filteredFileDetails.length} files with relevance >= ${RELEVANCE_THRESHOLD}`);

        // Sort by relevance score in descending order to prioritize most relevant files
        filteredFileDetails.sort((a, b) => b.relevance - a.relevance);

        const filePaths = filteredFileDetails.map(file => file.path);
        const contentMap: Record<string, string> = {};

        try {
            // Process all file paths in a single batch
            console.log(`Processing batch of ${filePaths.length} files`);
            const batchStartTime = Date.now();
            const batchResults = await processFileBatch(filePaths);
            console.log(`Batch processing took ${Date.now() - batchStartTime}ms`);

            // Convert the batch results to the expected format
            const formatStartTime = Date.now();
            const fileContents = filePaths.map(filePath => {
                let content = batchResults[filePath] ||
                    `Error: Could not read file "${filePath}"`;

                // Format the content with file path information for better context
                content = `File: ${filePath}\n\n${content}`;

                contentMap[filePath] = content;
                return content;
            });
            console.log(`Formatting results took ${Date.now() - formatStartTime}ms`);

            // Log cache statistics after processing
            const cacheStats = getFileContentCacheStats();
            console.log(`File cache size: ${cacheStats.size} entries`);

            console.log(`Extracted ${fileContents.length} file contents in ${Date.now() - startTime}ms total`);

            // Count tokens and trim if necessary
            const trimmedContentMap = this.trimContentToTokenLimit(contentMap);

            return {
                contentList: Object.values(trimmedContentMap),
                contentMap: trimmedContentMap
            };
        } catch (error) {
            console.error("Error fetching file contents:", error);
            const errorResults = filePaths.map(path => `Error: Could not extract content for ${path}`);
            filePaths.forEach((path, index) => {
                contentMap[path] = errorResults[index];
            });
            return {
                contentList: errorResults,
                contentMap: contentMap
            };
        }
    }
}
