import * as vscode from "vscode"
import * as path from "path"
import * as fs from "fs"
import { MermaidMappingService } from "./MermaidMappingService"
import { MermaidCompanionProvider } from "../../core/webview/MermaidCompanionProvider"
import { getWorkspacePath } from "../../utils/path"

/**
 * Service for managing the Mermaid Companion feature
 */
export class MermaidCompanionService {
    private static instance: MermaidCompanionService
    private mermaidMappingService: MermaidMappingService
    private outputChannel: vscode.OutputChannel
    private activeEditor: vscode.TextEditor | undefined
    private taxonomyWatcher: vscode.FileSystemWatcher | undefined
    private isEnabled: boolean = true
    private disposables: vscode.Disposable[] = []

    private constructor(outputChannel: vscode.OutputChannel) {
        this.mermaidMappingService = MermaidMappingService.getInstance()
        this.outputChannel = outputChannel
        this.activeEditor = vscode.window.activeTextEditor
    }

    /**
     * Get the singleton instance of the MermaidCompanionService
     */
    public static getInstance(outputChannel?: vscode.OutputChannel): MermaidCompanionService {
        if (!MermaidCompanionService.instance) {
            if (!outputChannel) {
                throw new Error("OutputChannel is required for first initialization")
            }
            MermaidCompanionService.instance = new MermaidCompanionService(outputChannel)
        }
        return MermaidCompanionService.instance
    }

    /**
     * Initialize the service
     */
    public async initialize(context: vscode.ExtensionContext): Promise<void> {
        try {
            // Initialize the mermaid mapping service
            await this.mermaidMappingService.initialize()

            // Register commands
            this.registerCommands(context)

            // Register event listeners
            this.registerEventListeners(context)

            // Watch for changes to the domain taxonomy file
            this.watchTaxonomyFile()

            this.outputChannel.appendLine("Mermaid Companion Service initialized")
        } catch (error) {
            this.outputChannel.appendLine(`Error initializing Mermaid Companion Service: ${error}`)
            throw error
        }
    }

    /**
     * Register commands
     */
    private registerCommands(context: vscode.ExtensionContext): void {
        // Command to show the mermaid diagram for the current function
        const showMermaidCommand = vscode.commands.registerCommand(
            "bracket-mermaid-companion.showMermaidForCurrentFunction",
            this.showMermaidForCurrentFunction.bind(this)
        )

        // Command to toggle the mermaid companion
        const toggleMermaidCommand = vscode.commands.registerCommand(
            "bracket-mermaid-companion.toggleMermaidCompanion",
            this.toggleMermaidCompanion.bind(this)
        )

        // Command to refresh the mermaid mapping
        const refreshMermaidCommand = vscode.commands.registerCommand(
            "bracket-mermaid-companion.refreshMermaidMapping",
            this.refreshMermaidMapping.bind(this)
        )

        // Command to show mermaid diagram for a specific function
        const showMermaidForFunctionCommand = vscode.commands.registerCommand(
            "bracket-mermaid-companion.showMermaidForFunction",
            this.showMermaidForFunction.bind(this)
        )

        context.subscriptions.push(showMermaidCommand, toggleMermaidCommand, refreshMermaidCommand, showMermaidForFunctionCommand)
        this.disposables.push(showMermaidCommand, toggleMermaidCommand, refreshMermaidCommand, showMermaidForFunctionCommand)
    }

    /**
     * Register event listeners
     */
    private registerEventListeners(context: vscode.ExtensionContext): void {
        // Listen for active editor changes
        const editorChangeDisposable = vscode.window.onDidChangeActiveTextEditor(
            this.handleActiveEditorChanged.bind(this)
        )

        // Listen for cursor position changes
        const selectionChangeDisposable = vscode.window.onDidChangeTextEditorSelection(
            this.handleSelectionChanged.bind(this)
        )

        context.subscriptions.push(editorChangeDisposable, selectionChangeDisposable)
        this.disposables.push(editorChangeDisposable, selectionChangeDisposable)
    }

    /**
     * Watch for changes to the domain taxonomy file
     */
    private watchTaxonomyFile(): void {
        const taxonomyPath = this.mermaidMappingService.getTaxonomyPath()
        if (!taxonomyPath) {
            return
        }

        // Create a file system watcher for the taxonomy file
        this.taxonomyWatcher = vscode.workspace.createFileSystemWatcher(taxonomyPath)

        // When the file changes, refresh the mapping
        this.taxonomyWatcher.onDidChange(async () => {
            this.outputChannel.appendLine("Domain taxonomy file changed, refreshing mapping")
            await this.refreshMermaidMapping()
        })

        this.disposables.push(this.taxonomyWatcher)
    }

    /**
     * Handle active editor changed event
     */
    private async handleActiveEditorChanged(editor: vscode.TextEditor | undefined): Promise<void> {
        if (!this.isEnabled || !editor) {
            return
        }

        this.activeEditor = editor
        await this.updateMermaidForCurrentPosition()
    }

    /**
     * Handle selection changed event
     */
    private async handleSelectionChanged(event: vscode.TextEditorSelectionChangeEvent): Promise<void> {
        if (!this.isEnabled || !event.textEditor) {
            return
        }

        // Debounce the update to avoid too many updates
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer)
        }

        this.debounceTimer = setTimeout(async () => {
            await this.updateMermaidForCurrentPosition()
        }, 300)
    }

    private debounceTimer: NodeJS.Timeout | undefined

    /**
     * Update the mermaid diagram for the current cursor position
     */
    private async updateMermaidForCurrentPosition(): Promise<void> {
        if (!this.isEnabled || !this.activeEditor) {
            return
        }

        try {
            const document = this.activeEditor.document
            const filePath = document.uri.fsPath

            // Only process certain file types for now
            const supportedExtensions = ['.py', '.js', '.ts', '.rb', '.java', '.go', '.c', '.cpp', '.h', '.hpp', '.cs', '.php']
            const fileExtension = path.extname(filePath)

            if (!supportedExtensions.includes(fileExtension)) {
                return
            }

            // Get the current position
            const position = this.activeEditor.selection.active

            // Log the current position for debugging
            this.outputChannel.appendLine(`Current position: ${position.line}:${position.character} in ${filePath}`)

            // Get the mermaid companion provider
            const provider = MermaidCompanionProvider.getInstance()
            if (!provider) {
                this.outputChannel.appendLine(`MermaidCompanionProvider not found`)
                return
            }

            // Primary approach: Use file-level granularity
            this.outputChannel.appendLine(`Updating diagram for file: ${filePath}`)
            const fileDiagramResult = await provider.updateDiagramForFile(filePath)

            // If no file-level diagram found, fall back to function-level for backward compatibility
            if (!fileDiagramResult) {
                // Try to find the function at the current position
                const functionPath = await this.findFunctionAtPosition(document, position)

                if (functionPath) {
                    this.outputChannel.appendLine(`No file-level diagram found. Falling back to function: ${functionPath}`)
                    await provider.updateDiagramForFunction(functionPath)
                }
            }
        } catch (error) {
            this.outputChannel.appendLine(`Error updating mermaid for current position: ${error}`)
        }
    }

    /**
     * Find the function at the given position
     */
    private async findFunctionAtPosition(
        document: vscode.TextDocument,
        position: vscode.Position
    ): Promise<string | undefined> {
        try {
            // Get the file path
            const filePath = document.uri.fsPath
            const fileBaseName = path.basename(filePath)

            // Check if there's a selection (multiple lines selected)
            const selection = this.activeEditor?.selection
            if (selection && !selection.isEmpty && selection.start.line !== selection.end.line) {
                // This is a multi-line selection, check if it's a function
                this.outputChannel.appendLine(`Multi-line selection detected: ${selection.start.line}-${selection.end.line}`)

                // Check the first few lines of the selection for function definition
                const maxLinesToCheck = Math.min(5, selection.end.line - selection.start.line + 1)
                for (let i = 0; i < maxLinesToCheck; i++) {
                    const lineIndex = selection.start.line + i
                    const lineText = document.lineAt(lineIndex).text
                    const functionMatch = this.getFunctionDefinitionFromLine(lineText, fileBaseName)
                    if (functionMatch) {
                        this.outputChannel.appendLine(`Found function in selection: ${functionMatch}`)
                        return functionMatch
                    }
                }
            }

            // Get the line text at cursor position
            const lineText = document.lineAt(position.line).text

            // Check if we're in a function definition
            const functionMatch = this.getFunctionDefinitionFromLine(lineText, fileBaseName)
            if (functionMatch) {
                return functionMatch
            }

            // If not, try to find the nearest function definition above
            for (let i = position.line - 1; i >= 0; i--) {
                const lineText = document.lineAt(i).text
                const functionMatch = this.getFunctionDefinitionFromLine(lineText, fileBaseName)
                if (functionMatch) {
                    return functionMatch
                }
            }

            return undefined
        } catch (error) {
            this.outputChannel.appendLine(`Error finding function at position: ${error}`)
            return undefined
        }
    }

    /**
     * Get the function definition from a line of code
     */
    private getFunctionDefinitionFromLine(line: string, fileName: string): string | undefined {
        this.outputChannel.appendLine(`Analyzing line: ${line}`)

        // Python function definition
        const pythonMatch = line.match(/^\s*def\s+([a-zA-Z0-9_]+)\s*\(/)
        if (pythonMatch) {
            const result = `${fileName}:${pythonMatch[1]}`
            this.outputChannel.appendLine(`Found Python function: ${result}`)
            return result
        }

        // JavaScript/TypeScript function definition
        const jsMatch = line.match(/^\s*(function|async function)\s+([a-zA-Z0-9_$]+)\s*\(/)
        if (jsMatch) {
            const result = `${fileName}:${jsMatch[2]}`
            this.outputChannel.appendLine(`Found JS/TS function: ${result}`)
            return result
        }

        // JavaScript/TypeScript method definition
        const jsMethodMatch = line.match(/^\s*([a-zA-Z0-9_$]+)\s*\(/)
        if (jsMethodMatch) {
            const result = `${fileName}:${jsMethodMatch[1]}`
            this.outputChannel.appendLine(`Found JS/TS method: ${result}`)
            return result
        }

        // JavaScript/TypeScript arrow function
        const arrowMatch = line.match(/^\s*const\s+([a-zA-Z0-9_$]+)\s*=\s*(\(|async\s*\()/)
        if (arrowMatch) {
            const result = `${fileName}:${arrowMatch[1]}`
            this.outputChannel.appendLine(`Found JS/TS arrow function: ${result}`)
            return result
        }

        // Ruby method definition
        const rubyMatch = line.match(/^\s*def\s+([a-zA-Z0-9_?!]+)/)
        if (rubyMatch) {
            const result = `${fileName}:${rubyMatch[1]}`
            this.outputChannel.appendLine(`Found Ruby method: ${result}`)
            return result
        }

        // Ruby class method definition (def self.method_name)
        const rubyClassMethodMatch = line.match(/^\s*def\s+self\.([a-zA-Z0-9_?!]+)/)
        if (rubyClassMethodMatch) {
            const result = `${fileName}:${rubyClassMethodMatch[1]}`
            this.outputChannel.appendLine(`Found Ruby class method: ${result}`)
            return result
        }

        // Class definition (Python, JS, TS, Ruby)
        const classMatch = line.match(/^\s*(class)\s+([a-zA-Z0-9_$]+)/)
        if (classMatch) {
            const result = `${fileName}:${classMatch[2]}`
            this.outputChannel.appendLine(`Found class definition: ${result}`)
            return result
        }

        return undefined
    }

    /**
     * Show the mermaid diagram for the current function
     */
    public async showMermaidForCurrentFunction(): Promise<void> {
        if (!this.activeEditor) {
            vscode.window.showInformationMessage("No active editor")
            return
        }

        await this.updateMermaidForCurrentPosition()
    }

    /**
     * Show the mermaid diagram for a specific function
     * This is called when a function is clicked in the injected context
     */
    public async showMermaidForFunction(params: { filePath: string, functionName: string }): Promise<void> {
        try {
            const { filePath, functionName } = params
            this.outputChannel.appendLine(`[MERMAID-DEBUG] Showing mermaid for function: ${functionName} in ${filePath}`)

            // Extract the file name from the path
            const fileName = path.basename(filePath)

            // Parse the function name to handle different formats
            let parsedFunctionName = functionName

            // If the function name contains a colon (e.g., "file.rb:Class.method"), extract the part after the colon
            if (functionName.includes(':')) {
                const parts = functionName.split(':')
                parsedFunctionName = parts[parts.length - 1]
                this.outputChannel.appendLine(`[MERMAID-DEBUG] Extracted function name after colon: ${parsedFunctionName}`)
            }

            // Create a function path in the format expected by the mermaid service
            // Format: fileName:functionName
            const functionPath = `${fileName}:${parsedFunctionName}`
            this.outputChannel.appendLine(`[MERMAID-DEBUG] Constructed function path: ${functionPath}`)

            // Get the mermaid companion provider
            const provider = MermaidCompanionProvider.getInstance()
            if (!provider) {
                this.outputChannel.appendLine(`[MERMAID-DEBUG] MermaidCompanionProvider not found`)
                return
            }

            // Update the diagram for the function
            this.outputChannel.appendLine(`[MERMAID-DEBUG] Calling updateDiagramForFunction with: ${functionPath}`)
            await provider.updateDiagramForFunction(functionPath)
            this.outputChannel.appendLine(`[MERMAID-DEBUG] Successfully updated diagram for function: ${functionPath}`)
        } catch (error) {
            this.outputChannel.appendLine(`[MERMAID-DEBUG] Error showing mermaid for function: ${error}`)
            console.error(`[MERMAID-DEBUG] Error showing mermaid for function:`, error)
        }
    }

    /**
     * Toggle the mermaid companion
     */
    public toggleMermaidCompanion(): void {
        this.isEnabled = !this.isEnabled
        vscode.window.showInformationMessage(
            `Mermaid Companion ${this.isEnabled ? "enabled" : "disabled"}`
        )
    }

    /**
     * Refresh the mermaid mapping
     */
    public async refreshMermaidMapping(): Promise<void> {
        try {
            // Re-initialize the mermaid mapping service
            await this.mermaidMappingService.initialize()
            vscode.window.showInformationMessage("Mermaid mapping refreshed")
        } catch (error) {
            this.outputChannel.appendLine(`Error refreshing mermaid mapping: ${error}`)
            vscode.window.showErrorMessage(`Error refreshing mermaid mapping: ${error}`)
        }
    }

    /**
     * Dispose of resources
     */
    public dispose(): void {
        this.disposables.forEach(d => d.dispose())
        this.disposables = []
    }
}
