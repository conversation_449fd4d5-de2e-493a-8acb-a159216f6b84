import * as fs from "fs"
import * as path from "path"
import { MermaidMappingService } from "../MermaidMappingService"

// Mock fs
jest.mock("fs", () => ({
    promises: {
        readFile: jest.fn(),
    },
    existsSync: jest.fn(),
    mkdirSync: jest.fn(),
    writeFileSync: jest.fn(),
}))

// Mock path
jest.mock("path", () => ({
    join: jest.fn((...args) => args.join("/")),
    resolve: jest.fn((...args) => args.join("/")),
}))

// Mock getWorkspacePath
jest.mock("../../../utils/path", () => ({
    getWorkspacePath: jest.fn(() => "/workspace"),
}))

// Mock data for tests
const mockTaxonomyData1 = {
    name: "Root",
    full_path: null,
    children: [
        {
            name: "Domain1",
            full_path: "Domain1",
            functions: ["file.py:function1"],
            diagram: "```mermaid\ngraph TD\nA-->B\n```",
        },
    ],
}

const mockTaxonomyData2 = {
    name: "Root",
    full_path: null,
    children: [
        {
            name: "Domain1",
            full_path: "Domain1",
            functions: ["file.py:function1"],
            diagram: "```mermaid\ngraph TD\nA-->B\n```",
            diagram_path: "/path/to/diagram.md",
        },
    ],
}

describe("MermaidMappingService", () => {
    let service: MermaidMappingService

    beforeEach(() => {
        jest.clearAllMocks()
        service = MermaidMappingService.getInstance()
    })

    describe("initialize", () => {
        it("should initialize the service with the provided taxonomy path", async () => {
            // Mock fs.existsSync to return true
            (fs.existsSync as jest.Mock).mockReturnValue(true)

            // Mock the readFile function to return the mock data
            (fs.promises.readFile as jest.Mock).mockResolvedValue(JSON.stringify(mockTaxonomyData1))

            // Initialize the service
            await service.initialize("/path/to/taxonomy.json")

            // Verify that readFile was called with the correct path
            expect(fs.promises.readFile).toHaveBeenCalledWith("/path/to/taxonomy.json", "utf-8")

            // Verify that the service is initialized
            expect(service.isInitializedStatus()).toBe(true)
        })

        it("should throw an error if the taxonomy file is not found", async () => {
            // Mock fs.existsSync to return false
            (fs.existsSync as jest.Mock).mockReturnValue(false)

            // Initialize the service
            await expect(service.initialize()).rejects.toThrow("Domain taxonomy file not found")
        })
    })

    describe("findMermaidDiagramsForFunction", () => {
        it("should find mermaid diagrams for a function", async () => {
            // Mock fs.existsSync to return true
            (fs.existsSync as jest.Mock).mockReturnValue(true)

            // Mock the readFile function to return the mock data
            (fs.promises.readFile as jest.Mock).mockResolvedValue(JSON.stringify(mockTaxonomyData2))

            // Initialize the service
            await service.initialize("/path/to/taxonomy.json")

            // Find mermaid diagrams for a function
            const diagrams = await service.findMermaidDiagramsForFunction("file.py:function1")

            // Verify that the diagrams were found
            expect(diagrams).toHaveLength(1)
            expect(diagrams[0].diagram).toBe("```mermaid\ngraph TD\nA-->B\n```")
            expect(diagrams[0].path).toBe("/path/to/diagram.md")
            expect(diagrams[0].domainName).toBe("Domain1")
            expect(diagrams[0].fullPath).toBe("Domain1")
        })

        it("should return an empty array if no diagrams are found", async () => {
            // Mock fs.existsSync to return true
            (fs.existsSync as jest.Mock).mockReturnValue(true)

            // Mock the readFile function to return the mock data
            (fs.promises.readFile as jest.Mock).mockResolvedValue(JSON.stringify(mockTaxonomyData1))

            // Initialize the service
            await service.initialize("/path/to/taxonomy.json")

            // Find mermaid diagrams for a function that doesn't exist
            const diagrams = await service.findMermaidDiagramsForFunction("file.py:function2")

            // Verify that no diagrams were found
            expect(diagrams).toHaveLength(0)
        })
    })

    describe("findMermaidDiagramsForFile", () => {
        it("should find mermaid diagrams for a file", async () => {
            // Mock fs.existsSync to return true
            (fs.existsSync as jest.Mock).mockReturnValue(true)

            // Mock the readFile function to return the mock data
            (fs.promises.readFile as jest.Mock).mockResolvedValue(JSON.stringify(mockTaxonomyData2))

            // Initialize the service
            await service.initialize("/path/to/taxonomy.json")

            // Find mermaid diagrams for a file
            const diagrams = await service.findMermaidDiagramsForFile("file.py")

            // Verify that the diagrams were found
            expect(diagrams).toHaveLength(1)
            expect(diagrams[0].diagram).toBe("```mermaid\ngraph TD\nA-->B\n```")
            expect(diagrams[0].path).toBe("/path/to/diagram.md")
            expect(diagrams[0].domainName).toBe("Domain1")
            expect(diagrams[0].fullPath).toBe("Domain1")
        })

        it("should return an empty array if no diagrams are found", async () => {
            // Mock fs.existsSync to return true
            (fs.existsSync as jest.Mock).mockReturnValue(true)

            // Mock the readFile function to return the mock data
            (fs.promises.readFile as jest.Mock).mockResolvedValue(JSON.stringify(mockTaxonomyData1))

            // Initialize the service
            await service.initialize("/path/to/taxonomy.json")

            // Find mermaid diagrams for a file that doesn't exist
            const diagrams = await service.findMermaidDiagramsForFile("other_file.py")

            // Verify that no diagrams were found
            expect(diagrams).toHaveLength(0)
        })
    })
})
