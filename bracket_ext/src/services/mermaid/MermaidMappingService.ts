import * as vscode from "vscode"
import * as fs from "fs"
import * as path from "path"
import { getWorkspacePath } from "../../utils/path"

/**
 * Interface representing a node in the domain taxonomy
 */
interface TaxonomyNode {
    name: string
    full_path: string | null
    files?: string[]
    functions?: string[] // Kept for backward compatibility
    diagram?: string
    diagram_path?: string
    diagram_name?: string
    children?: TaxonomyNode[]
    combined_diagram?: string
    combined_diagram_path?: string
    combined_diagram_name?: string
}

/**
 * Interface representing a hierarchical diagram result
 */
export interface HierarchicalDiagramResult {
    diagrams: MermaidDiagramResult[]
    leafDiagram: MermaidDiagramResult
}

/**
 * Interface representing the result of finding a mermaid diagram
 */
export interface MermaidDiagramResult {
    diagram: string
    path: string
    domainName: string
    fullPath: string
}

/**
 * Service for mapping functions to their associated mermaid diagrams
 */
export class MermaidMappingService {
    private static instance: MermaidMappingService
    private taxonomyData: TaxonomyNode | null = null
    private taxonomyPath: string | null = null
    private fileToNodeMap: Map<string, TaxonomyNode[]> = new Map()
    private functionToNodeMap: Map<string, TaxonomyNode[]> = new Map() // Kept for backward compatibility
    private isInitialized = false
    private initializationPromise: Promise<void> | null = null

    private constructor() {}

    /**
     * Get the singleton instance of the MermaidMappingService
     */
    public static getInstance(): MermaidMappingService {
        if (!MermaidMappingService.instance) {
            MermaidMappingService.instance = new MermaidMappingService()
        }
        return MermaidMappingService.instance
    }

    /**
     * Initialize the service by loading the domain taxonomy
     */
    public async initialize(taxonomyPath?: string): Promise<void> {
        if (this.isInitialized) {
            return
        }

        if (this.initializationPromise) {
            return this.initializationPromise
        }

        this.initializationPromise = this.doInitialize(taxonomyPath)
        return this.initializationPromise
    }

    private async doInitialize(taxonomyPath?: string): Promise<void> {
        try {
            const workspacePath = getWorkspacePath()
            if (!workspacePath) {
                throw new Error("No workspace path found")
            }

            // If taxonomyPath is not provided, look for it in the default locations
            if (!taxonomyPath) {
                // Hardcode the absolute path to the domain taxonomy file
                // const hardcodedPath = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/domain_taxonomy_final.json"
                // const hardcodedPath = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/outputs/django/domain_file_taxonomy/taxonomy.json"
                // const hardcodedPath = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/exp_repomap/exp_results/mermaid_outputs_json/gitlab/taxonomy1.json"
                const hardcodedPath = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/bracket_core/bracket_irl/outputs/datadog_agent/taxonomy/taxonomy.json"
                
                console.log(`Looking for domain taxonomy at hardcoded path: ${hardcodedPath}`)

                if (fs.existsSync(hardcodedPath)) {
                    taxonomyPath = hardcodedPath
                    console.log(`Found domain taxonomy at: ${taxonomyPath}`)
                } else {
                    console.warn(`Hardcoded domain taxonomy file not found at: ${hardcodedPath}`)
                }

                if (!taxonomyPath) {
                    console.warn("Domain taxonomy file not found. Using empty taxonomy.")
                    // Create an empty taxonomy structure instead of throwing an error
                    this.taxonomyData = {
                        name: "Root",
                        full_path: null,
                        children: []
                    }
                    this.isInitialized = true
                    return
                }
            }

            this.taxonomyPath = taxonomyPath
            console.log(`Loading domain taxonomy from: ${taxonomyPath}`)

            const taxonomyContent = await fs.promises.readFile(taxonomyPath, "utf-8")
            this.taxonomyData = JSON.parse(taxonomyContent)

            console.log(`Successfully loaded domain taxonomy with ${this.taxonomyData?.children?.length || 0} top-level domains`)

            // Build the function-to-node and file-to-node maps
            this.buildNodeMaps(this.taxonomyData)

            console.log(`Built function-to-node map with ${this.functionToNodeMap.size} functions`)
            console.log(`Built file-to-node map with ${this.fileToNodeMap.size} files`)

            this.isInitialized = true
        } catch (error) {
            console.error("Failed to initialize MermaidMappingService:", error)
            // Create an empty taxonomy structure instead of throwing an error
            this.taxonomyData = {
                name: "Root",
                full_path: null,
                children: []
            }
            this.isInitialized = true
        } finally {
            this.initializationPromise = null
        }
    }

    /**
     * Build maps of function paths and file paths to their containing taxonomy nodes
     */
    private buildNodeMaps(node: TaxonomyNode | null, parentPath: string = "", depth: number = 0): void {
        if (!node) return

        const currentPath = node.full_path || parentPath
        const indent = "  ".repeat(depth)

        // Log the current node for debugging
        if (depth === 0) {
            console.log(`Building node maps from root node`)
        } else if (depth === 1) {
            console.log(`${indent}Processing top-level domain: ${node.name}`)
        } else if (depth === 2) {
            if (node.files && node.files.length > 0) {
                console.log(`${indent}Processing domain with ${node.files.length} files: ${node.name}`)
            } else if (node.functions && node.functions.length > 0) {
                console.log(`${indent}Processing domain with ${node.functions.length} functions: ${node.name}`)
            }
        }

        // If this node has files, add them to the file-to-node map
        if (node.files && node.files.length > 0) {
            // Log a sample of files for debugging
            if (node.files.length > 0 && depth <= 2) {
                const sampleSize = Math.min(3, node.files.length)
                const sampleFiles = node.files.slice(0, sampleSize)
                console.log(`${indent}Sample files in ${node.name}: ${sampleFiles.join(", ")}${node.files.length > sampleSize ? ` (and ${node.files.length - sampleSize} more)` : ""}`)
            }

            for (const filePath of node.files) {
                const normalizedFilePath = this.normalizeFilePath(filePath)

                if (!this.fileToNodeMap.has(normalizedFilePath)) {
                    this.fileToNodeMap.set(normalizedFilePath, [])
                }

                this.fileToNodeMap.get(normalizedFilePath)!.push(node)
            }
        }

        // For backward compatibility, also process functions if present
        if (node.functions && node.functions.length > 0) {
            // Log a sample of functions for debugging
            if (node.functions.length > 0 && depth <= 2) {
                const sampleSize = Math.min(3, node.functions.length)
                const sampleFunctions = node.functions.slice(0, sampleSize)
                console.log(`${indent}Sample functions in ${node.name}: ${sampleFunctions.join(", ")}${node.functions.length > sampleSize ? ` (and ${node.functions.length - sampleSize} more)` : ""}`)
            }

            for (const functionPath of node.functions) {
                const normalizedFunctionPath = this.normalizeFunctionPath(functionPath)

                if (!this.functionToNodeMap.has(normalizedFunctionPath)) {
                    this.functionToNodeMap.set(normalizedFunctionPath, [])
                }

                this.functionToNodeMap.get(normalizedFunctionPath)!.push(node)
            }
        }

        // Recursively process children
        if (node.children) {
            for (const child of node.children) {
                this.buildNodeMaps(child, currentPath, depth + 1)
            }
        }
    }

    /**
     * Normalize a function path for consistent matching
     */
    private normalizeFunctionPath(functionPath: string): string {
        // Log the original function path for debugging
        console.log(`Normalizing function path: ${functionPath}`)

        // Extract just the filename from paths like "app/services/admin/abuse_reports/moderate_user_service.rb"
        let normalized = functionPath

        // First, handle full paths by extracting the filename
        if (functionPath.includes("/")) {
            const parts = functionPath.split("/")
            const lastPart = parts[parts.length - 1]
            if (lastPart.includes(":")) {
                normalized = lastPart // Already has filename:function format
            } else {
                normalized = functionPath // Keep the original if no function part
            }
        }

        // Remove any language tags or prefixes
        normalized = normalized
            .replace(/^```\w+\s*/, "")  // Remove language tag
            .replace(/^def\s+/, "")     // Remove 'def' prefix
            .replace(/\([^)]*\)$/, "")  // Remove parameter list
            .replace(/^function\s+/, "") // Remove 'function' prefix
            .replace(/^async\s+/, "")    // Remove 'async' prefix
            .replace(/^class\s+/, "")    // Remove 'class' prefix
            .trim()

        // Log the normalized function path for debugging
        console.log(`Normalized to: ${normalized}`)

        return normalized
    }

    /**
     * Normalize a file path for consistent matching
     */
    private normalizeFilePath(filePath: string): string {
        // Log the original file path for debugging
        console.log(`Normalizing file path: ${filePath}`)

        // Normalize path separators
        let normalized = filePath.replace(/\\/g, "/")

        // Remove any language tags or code block markers
        normalized = normalized
            .replace(/^```\w+\s*/, "")  // Remove language tag
            .replace(/```\s*$/, "")     // Remove closing code block
            .trim()

        // Log the normalized file path for debugging
        console.log(`Normalized to: ${normalized}`)

        return normalized
    }

    /**
     * Find the mermaid diagram(s) associated with a function path
     */
    public async findMermaidDiagramsForFunction(functionPath: string): Promise<MermaidDiagramResult[]> {
        try {
            if (!this.isInitialized) {
                await this.initialize()
            }
        } catch (error) {
            console.error("Error initializing MermaidMappingService:", error)
            // Return empty results if initialization fails
            return []
        }

        const normalizedFunctionPath = this.normalizeFunctionPath(functionPath)
        console.log(`Looking for diagrams for normalized function path: ${normalizedFunctionPath}`)

        // First try exact match
        let nodes = this.functionToNodeMap.get(normalizedFunctionPath) || []

        // If no exact match, try partial matching
        if (nodes.length === 0) {
            console.log(`No exact match found, trying partial matching`)
            nodes = this.findNodesWithPartialFunctionMatch(normalizedFunctionPath)
        }

        const results: MermaidDiagramResult[] = []

        for (const node of nodes) {
            if (node.diagram) {
                console.log(`Found diagram in domain: ${node.name}`)
                results.push({
                    diagram: node.diagram,
                    path: node.diagram_path || "",
                    domainName: node.name,
                    fullPath: node.full_path || ""
                })

                // Get all parent diagrams for this node
                const parentDiagrams = this.getParentDiagrams(node)
                results.push(...parentDiagrams)
            }
        }

        return results
    }

    /**
     * Get hierarchical diagrams for a function path
     * This returns both the leaf diagram and all parent diagrams in the hierarchy
     */
    public async getHierarchicalDiagramsForFunction(functionPath: string): Promise<HierarchicalDiagramResult | null> {
        const diagrams = await this.findMermaidDiagramsForFunction(functionPath)

        if (diagrams.length === 0) {
            return null
        }

        // Sort diagrams by domain depth (number of "->" in the path)
        // The diagram with the most "->" is the deepest and most specific
        diagrams.sort((a, b) => {
            const aDepth = (a.fullPath.match(/->/g) || []).length
            const bDepth = (b.fullPath.match(/->/g) || []).length
            return bDepth - aDepth // Sort in descending order (deepest first)
        })

        // Log the sorted diagrams for debugging
        console.log(`Sorted diagrams by depth:`)
        diagrams.forEach((diagram, index) => {
            const depth = (diagram.fullPath.match(/->/g) || []).length
            console.log(`  ${index}: ${diagram.domainName} (depth: ${depth})`)
        })

        // The first diagram after sorting is the deepest (most specific)
        const leafDiagram = diagrams[0]
        console.log(`Selected deepest diagram: ${leafDiagram.domainName}`)

        return {
            diagrams: diagrams,
            leafDiagram: leafDiagram
        }
    }

    /**
     * Get all parent diagrams for a node
     * This traverses up the hierarchy to find all parent nodes with diagrams
     */
    private getParentDiagrams(node: TaxonomyNode): MermaidDiagramResult[] {
        const results: MermaidDiagramResult[] = []

        // If the node doesn't have a full path, we can't find its parents
        if (!node.full_path) {
            return results
        }

        // Parse the full path to get the hierarchy
        const pathParts = node.full_path.split(" -> ")

        // Start from the root and traverse down to find each parent
        const rootNode = this.taxonomyData
        let currentPath = ""

        if (!rootNode || !rootNode.children) {
            return results
        }

        let currentNode: TaxonomyNode = rootNode

        for (let i = 0; i < pathParts.length - 1; i++) { // Skip the last part (current node)
            const part = pathParts[i]

            // Update the current path
            currentPath = currentPath ? `${currentPath} -> ${part}` : part

            // Find the child node that matches this part
            if (!currentNode.children) {
                break
            }

            const childNode = currentNode.children.find(child => child.name === part)

            if (!childNode) {
                break
            }

            currentNode = childNode

            // If this node has a diagram, add it to the results
            if (currentNode.diagram) {
                console.log(`Found parent diagram in domain: ${currentNode.name}`)
                results.push({
                    diagram: currentNode.diagram,
                    path: currentNode.diagram_path || "",
                    domainName: currentNode.name,
                    fullPath: currentNode.full_path || ""
                })
            }

            // If this node has a combined diagram, add it to the results
            if (currentNode.combined_diagram) {
                console.log(`Found combined diagram in domain: ${currentNode.name}`)
                results.push({
                    diagram: currentNode.combined_diagram,
                    path: currentNode.combined_diagram_path || "",
                    domainName: `${currentNode.name} (Combined)`,
                    fullPath: currentNode.full_path ? `${currentNode.full_path} (Combined)` : ""
                })
            }
        }

        return results
    }

    /**
     * Find nodes with partial function path matches
     */
    private findNodesWithPartialFunctionMatch(partialFunctionPath: string): TaxonomyNode[] {
        const results: TaxonomyNode[] = []
        const seenDiagrams = new Set<string>()

        // Parse the partial function path to get file and function parts
        const parts = partialFunctionPath.split(":")
        if (parts.length !== 2) {
            console.log(`Invalid partial function path format: ${partialFunctionPath}`)
            return results
        }

        const fileNamePart = parts[0].trim()
        const functionNamePart = parts[1].trim()

        console.log(`Searching for file part: "${fileNamePart}" and function part: "${functionNamePart}"`)

        // Iterate through all function paths in the map
        for (const [fullFunctionPath, nodes] of this.functionToNodeMap.entries()) {
            // Parse the full function path
            const fullParts = fullFunctionPath.split(":")
            if (fullParts.length < 2) continue

            const fullFilePart = fullParts[0].trim()
            const fullFunctionPart = fullParts.slice(1).join(":").trim()

            // Check if both file and function parts match partially
            const fileMatches = fullFilePart.includes(fileNamePart) || fileNamePart.includes(fullFilePart)

            // For function matching, handle class methods specially
            let functionMatches = false

            // Check if the function part is a direct match
            if (fullFunctionPart.includes(functionNamePart) || functionNamePart.includes(fullFunctionPart)) {
                functionMatches = true
            }
            // Check if it's a class method (ClassName.method_name)
            else if (fullFunctionPart.includes(".")) {
                const methodName = fullFunctionPart.split(".").pop() || ""
                if (methodName === functionNamePart || methodName.includes(functionNamePart) || functionNamePart.includes(methodName)) {
                    functionMatches = true
                    console.log(`Matched class method: ${methodName} with ${functionNamePart}`)
                }
            }

            if (fileMatches && functionMatches) {
                console.log(`Partial match found: ${fullFunctionPath}`)

                // Add nodes with unique diagrams
                for (const node of nodes) {
                    if (node.diagram && !seenDiagrams.has(node.diagram)) {
                        results.push(node)
                        seenDiagrams.add(node.diagram)
                    }
                }
            }
        }

        console.log(`Found ${results.length} nodes with partial matches`)
        return results
    }

    /**
     * Find the mermaid diagram associated with a file path
     * This uses the fileToNodeMap to find diagrams directly associated with files
     */
    public async findMermaidDiagramsForFile(filePath: string): Promise<MermaidDiagramResult[]> {
        try {
            if (!this.isInitialized) {
                await this.initialize()
            }
        } catch (error) {
            console.error("Error initializing MermaidMappingService:", error)
            // Return empty results if initialization fails
            return []
        }

        const normalizedFilePath = this.normalizeFilePath(filePath)
        console.log(`Finding mermaid diagrams for file: ${normalizedFilePath}`)

        const results: MermaidDiagramResult[] = []
        const fileBaseName = path.basename(normalizedFilePath)
        console.log(`File base name: ${fileBaseName}`)

        // First try: Direct lookup in the fileToNodeMap
        let nodes = this.fileToNodeMap.get(normalizedFilePath) || []
        console.log(`Direct file lookup found ${nodes.length} nodes for ${normalizedFilePath}`)

        // If no direct match, try with just the filename
        if (nodes.length === 0) {
            console.log(`No direct match found, trying with just the filename: ${fileBaseName}`)
            nodes = this.fileToNodeMap.get(fileBaseName) || []
            console.log(`Filename-only lookup found ${nodes.length} nodes`)
        }

        // If still no match, try partial matching
        if (nodes.length === 0) {
            console.log(`No exact matches found, trying partial matching`)
            // Try to find files that contain the normalized path or basename
            for (const [mappedFilePath, mappedNodes] of this.fileToNodeMap.entries()) {
                if (mappedFilePath.includes(normalizedFilePath) ||
                    normalizedFilePath.includes(mappedFilePath) ||
                    mappedFilePath.includes(fileBaseName) ||
                    fileBaseName.includes(mappedFilePath)) {
                    console.log(`Found partial match: ${mappedFilePath}`)
                    nodes = [...nodes, ...mappedNodes]
                }
            }
            console.log(`Partial matching found ${nodes.length} nodes`)
        }

        // If still no match and we have a function-to-node map, fall back to the old method
        if (nodes.length === 0 && this.functionToNodeMap.size > 0) {
            console.log(`No file matches found, falling back to function-based lookup for backward compatibility`)
            // Look for functions that belong to this file
            for (const [functionPath, functionNodes] of this.functionToNodeMap.entries()) {
                const functionFilePart = functionPath.split(":")[0]

                // Check if the function belongs to this file using various matching strategies
                const isMatch =
                    functionPath.includes(normalizedFilePath) ||
                    normalizedFilePath.includes(functionFilePart) ||
                    functionFilePart === fileBaseName ||
                    functionPath.startsWith(fileBaseName + ":")

                if (isMatch) {
                    console.log(`Found matching function: ${functionPath}`)
                    nodes = [...nodes, ...functionNodes]
                }
            }
            console.log(`Function-based fallback found ${nodes.length} nodes`)
        }

        // Process all found nodes
        const seenDiagrams = new Set<string>()
        for (const node of nodes) {
            if (node.diagram && !seenDiagrams.has(node.diagram)) {
                console.log(`Adding diagram from domain: ${node.name}`)
                seenDiagrams.add(node.diagram)
                results.push({
                    diagram: node.diagram,
                    path: node.diagram_path || "",
                    domainName: node.name,
                    fullPath: node.full_path || ""
                })

                // Get all parent diagrams for this node
                const parentDiagrams = this.getParentDiagrams(node)
                for (const parentDiagram of parentDiagrams) {
                    if (!seenDiagrams.has(parentDiagram.diagram)) {
                        seenDiagrams.add(parentDiagram.diagram)
                        results.push(parentDiagram)
                    }
                }
            }
        }

        console.log(`Found ${results.length} diagrams for file: ${normalizedFilePath}`)
        return results
    }

    /**
     * Get hierarchical diagrams for a file path
     * This returns both the leaf diagram and all parent diagrams in the hierarchy
     */
    public async getHierarchicalDiagramsForFile(filePath: string): Promise<HierarchicalDiagramResult | null> {
        const diagrams = await this.findMermaidDiagramsForFile(filePath)

        if (diagrams.length === 0) {
            return null
        }

        // Sort diagrams by domain depth (number of "->" in the path)
        // The diagram with the most "->" is the deepest and most specific
        diagrams.sort((a, b) => {
            const aDepth = (a.fullPath.match(/->/g) || []).length
            const bDepth = (b.fullPath.match(/->/g) || []).length
            return bDepth - aDepth // Sort in descending order (deepest first)
        })

        // Log the sorted diagrams for debugging
        console.log(`Sorted file diagrams by depth:`)
        diagrams.forEach((diagram, index) => {
            const depth = (diagram.fullPath.match(/->/g) || []).length
            console.log(`  ${index}: ${diagram.domainName} (depth: ${depth})`)
        })

        // The first diagram after sorting is the deepest (most specific)
        const leafDiagram = diagrams[0]
        console.log(`Selected deepest file diagram: ${leafDiagram.domainName}`)

        return {
            diagrams: diagrams,
            leafDiagram: leafDiagram
        }
    }

    /**
     * Get the path to the domain taxonomy file
     */
    public getTaxonomyPath(): string | null {
        return this.taxonomyPath
    }

    /**
     * Check if the service is initialized
     */
    public isInitializedStatus(): boolean {
        return this.isInitialized
    }
}
