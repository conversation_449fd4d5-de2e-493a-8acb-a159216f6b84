import * as vscode from "vscode"
import { ContextEngineResult, ContextFunction, ContextFile, ContextEngineBlock } from "../shared/WebviewMessage"
import { ExtensionMessage } from "../shared/ExtensionMessage"
import { getPanel } from "../activate/registerCommands"
import { ReasoningShowcaseService } from "./ReasoningShowcaseService"
import { getWorkspaceRoot } from "../utils/workspace"

/**
 * Service for handling context engine functionality
 */
export class ContextEngineService {
	private static instance: ContextEngineService
	private panel: vscode.WebviewPanel | vscode.WebviewView | undefined
	// Store relevant functions from global localization
	private relevantFunctions: Map<string, string> = new Map()
	// Store relevant files from global localization
	private relevantFiles: Map<string, string> = new Map()
	// Store context functions with metadata
	private contextFunctions: ContextFunction[] = []
	// Store context files with metadata
	private contextFiles: ContextFile[] = []
	// Store context engine blocks for inline display
	private contextEngineBlocks: ContextEngineBlock[] = []
	// Reasoning showcase service
	private reasoningShowcaseService: ReasoningShowcaseService
	// Codebase overview content
	private codebaseOverview: string = ""
	// Flag to control whether the Context Engine should be called for every message
	private callForEveryMessage: boolean = false

	private constructor() {
		// Private constructor to enforce singleton pattern
		this.reasoningShowcaseService = ReasoningShowcaseService.getInstance()
		// Initialize the reasoning showcase service
		this.reasoningShowcaseService.initialize().catch(error => {
			console.error("Error initializing reasoning showcase service:", error)
		})

		// Try to load the codebase overview
		this.loadCodebaseOverview().catch(error => {
			console.error("Error loading codebase overview:", error)
		})
	}

	/**
	 * Get the singleton instance of the ContextEngineService
	 */
	public static getInstance(): ContextEngineService {
		if (!ContextEngineService.instance) {
			ContextEngineService.instance = new ContextEngineService()
		}
		return ContextEngineService.instance
	}

	/**
	 * Set the panel to use for sending messages
	 * @param panel The webview panel or view
	 */
	public setPanel(panel: vscode.WebviewPanel | vscode.WebviewView): void {
		this.panel = panel
	}

	/**
	 * Update the context engine with new data
	 * @param data The context engine result data
	 */
	public updateContextEngine(data: ContextEngineResult): void {
		if (!this.panel) {
			this.panel = getPanel()
		}

		if (this.panel) {
			const message: ExtensionMessage = {
				type: "action",
				action: "updateContextEngine",
				contextEngineData: data,
				contextFunctions: this.contextFunctions,
				contextFiles: this.contextFiles
			}
			this.panel.webview.postMessage(message)
		}
	}

	/**
	 * Send a context engine block to be displayed inline in the chat
	 * @param block The context engine block to send
	 */
	public sendInlineContextEngineBlock(block: ContextEngineBlock): void {
		if (!this.panel) {
			this.panel = getPanel()
		}

		// Check if we already have a similar block to avoid duplicates
		const isDuplicate = this.contextEngineBlocks.some(existingBlock =>
			existingBlock.content === block.content ||
			// Also check for similar content (first 50 chars)
			(existingBlock.content.substring(0, 50) === block.content.substring(0, 50) &&
			 existingBlock.content.length > 50 && block.content.length > 50)
		);

		if (!isDuplicate) {
			// Add the block to our collection
			this.contextEngineBlocks.push(block)

			if (this.panel) {
				const message: ExtensionMessage = {
					type: "inlineContextEngine",
					contextEngineBlocks: [block]
				}
				this.panel.webview.postMessage(message)
			}
		}
	}

	// Callback function type for when context processing is complete
	private onProcessingCompleteCallback: (() => void) | null = null;

	/**
	 * Process a user query to find relevant context
	 * @param query The user's query
	 * @param onComplete Optional callback to execute when processing is complete
	 */
	public async processQuery(query: string, onComplete?: () => void): Promise<void> {
		// Store the callback
		this.onProcessingCompleteCallback = onComplete || null;

		// Show loading state
		this.updateContextEngine({
			content: "Initializing Bracket Context Engine...",
			isLoading: true,
		})

		try {
			// Import the ContextEngineProcessor dynamically to avoid circular dependencies
			const { ContextEngineProcessor } = await import("./ContextEngineProcessor")
			const processor = ContextEngineProcessor.getInstance()

			// Start the reasoning showcase in parallel with the actual processing
			this.startReasoningShowcase(query)

			// Process the query using the processor
			await processor.processQuery(query)
		} catch (error) {
			console.error("Error processing query:", error)
			this.updateContextEngine({
				content: `Error processing query: ${error instanceof Error ? error.message : String(error)}`,
				isSuccess: false,
			})

			// Call the callback even if there's an error
			this.executeCallback();
		}
	}

	/**
	 * Notify that context processing is complete
	 */
	public notifyProcessingComplete(): void {
		// Stop the reasoning showcase
		this.reasoningShowcaseService.stopGeneration()
		this.executeCallback();
	}

	/**
	 * Execute the callback if it exists
	 */
	private executeCallback(): void {
		if (this.onProcessingCompleteCallback) {
			console.log("Executing context engine completion callback")

			// Send a message to the webview to indicate that the Context Engine has completed
			if (!this.panel) {
				this.panel = getPanel()
			}

			if (this.panel) {
				this.panel.webview.postMessage({
					type: "action",
					action: "contextEngineComplete"
				});
			}

			// Execute the callback
			this.onProcessingCompleteCallback();
			this.onProcessingCompleteCallback = null; // Clear the callback
		}
	}

	/**
	 * Set the relevant functions from global localization
	 * @param functions Array of function code strings
	 */
	public setRelevantFnsNameCodeMap(functions: Record<string, string>): void {
		console.log(`Setting ${Object.keys(functions).length} relevant functions from global localization`)
		this.relevantFunctions = new Map(Object.entries(functions));
	}

	/**
	 * Set the relevant files from global localization
	 * @param files Array of file content strings
	 */
	public setRelevantFilesContentMap(files: Record<string, string>): void {
		console.log(`Setting ${Object.keys(files).length} relevant files from global localization`)
		this.relevantFiles = new Map(Object.entries(files));
	}

	/**
	 * Set the context functions with metadata
	 * @param functions Array of context functions with metadata
	 */
	public setContextFunctions(functions: ContextFunction[]): void {
		console.log(`Setting ${functions.length} context functions with metadata`)
		this.contextFunctions = functions;
	}

	/**
	 * Set the context files with metadata
	 * @param files Array of context files with metadata
	 */
	public setContextFiles(files: ContextFile[]): void {
		console.log(`Setting ${files.length} context files with metadata`)
		this.contextFiles = files;
	}

	/**
	 * Clear all context data and reset the context engine state
	 */
	public clearContextData(): void {
		console.log('Clearing all context data')
		this.contextFunctions = [];
		this.contextFiles = [];
		this.relevantFunctions = new Map();
		this.relevantFiles = new Map();
		this.contextEngineBlocks = [];

		// Update the UI to reflect the cleared state
		if (!this.panel) {
			this.panel = getPanel()
		}

		if (this.panel) {
			// Clear any existing context engine blocks
			this.panel.webview.postMessage({
				type: "action",
				action: "clearContextEngineBlocks"
			});

			// Update the context data in the UI
			this.panel.webview.postMessage({
				type: "action",
				action: "updateContextEngine",
				contextEngineData: {
					content: "",
					isSuccess: true,
					isLoading: false
				},
				contextFunctions: [],
				contextFiles: []
			});
		}
	}

	/**
	 * Clear all context functions and reset the context engine state
	 * @deprecated Use clearContextData instead
	 */
	public clearContextFunctions(): void {
		console.log('Clearing all context functions (deprecated method)')
		this.clearContextData();
	}

	/**
	 * Get the context functions with metadata
	 * @returns Array of context functions with metadata
	 */
	public getContextFunctions(): ContextFunction[] {
		return this.contextFunctions;
	}

	/**
	 * Get the context files with metadata
	 * @returns Array of context files with metadata
	 */
	public getContextFiles(): ContextFile[] {
		return this.contextFiles;
	}

	/**
	 * Get the relevant functions and files from global localization
	 * @returns Map of function/file names to code/content strings
	 */
	public getRelevantFunctions(): Map<string, string> {
		// Create a combined map of both functions and files
		const combinedMap = new Map<string, string>();

		// Add all functions to the combined map
		this.relevantFunctions.forEach((content, name) => {
			combinedMap.set(name, content);
		});

		// Add all files to the combined map
		// Note: The filtering by relevance score is now done in ContextEngineProcessor.extractRelevantFilesContent
		// This method just returns what's already been filtered
		this.relevantFiles.forEach((content, path) => {
			// Format file content with a header to identify it as a file
			const formattedContent = `File: ${path}\n\n${content}`;
			combinedMap.set(path, formattedContent);
		});

		console.log(`Returning combined map with ${combinedMap.size} items (${this.relevantFunctions.size} functions and ${this.relevantFiles.size} files)`);
		return combinedMap;
	}

	/**
	 * Get the relevant files from global localization
	 * @returns Map of file paths to file content strings
	 */
	public getRelevantFiles(): Map<string, string> {
		return this.relevantFiles;
	}

	/**
	 * Set whether the Context Engine should be called for every message
	 * @param value True to call for every message, false to call only once
	 */
	public setCallForEveryMessage(value: boolean): void {
		console.log(`Setting Context Engine to ${value ? 'call for every message' : 'call only once'}`);
		this.callForEveryMessage = value;
	}

	/**
	 * Get whether the Context Engine should be called for every message
	 * @returns True if the Context Engine should be called for every message, false otherwise
	 */
	public getCallForEveryMessage(): boolean {
		return this.callForEveryMessage;
	}

	/**
	 * Start the reasoning showcase process
	 * @param query The user query
	 */
	private async startReasoningShowcase(query: string): Promise<void> {
		// If we don't have the codebase overview yet, try to load it
		if (!this.codebaseOverview) {
			await this.loadCodebaseOverview()
		}

		// Start generating reasoning steps - don't use the callback as the service handles blocks directly
		this.reasoningShowcaseService.generateReasoningSteps(query, this.codebaseOverview)
	}

	/**
	 * Load the codebase overview from the file system
	 */
	private async loadCodebaseOverview(): Promise<void> {
		try {
			const workspaceRoot = getWorkspaceRoot()
			if (!workspaceRoot) {
				console.error("No workspace root found")
				this.codebaseOverview = "No codebase overview available"
				return
			}

			// Try to read the codebase explanation file
			const fs = require('fs')
			const path = require('path')
			const codebaseExplanationPath = path.join(workspaceRoot, 'bracket_ext/assets/codebase_explanation.md')

			if (fs.existsSync(codebaseExplanationPath)) {
				this.codebaseOverview = fs.readFileSync(codebaseExplanationPath, 'utf8')
				console.log("Loaded codebase overview")
			} else {
				console.warn(`Codebase explanation file not found at ${codebaseExplanationPath}`)
				this.codebaseOverview = "This codebase contains multiple domains and components that work together to process and analyze code."
			}
		} catch (error) {
			console.error("Error loading codebase overview:", error)
			this.codebaseOverview = "Error loading codebase overview"
		}
	}
}
