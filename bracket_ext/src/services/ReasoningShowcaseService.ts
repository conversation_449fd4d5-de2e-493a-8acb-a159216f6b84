import * as vscode from "vscode"
import axios from "axios"
import { getOpenAiApiKey } from "../utils/apiKeys"
import { getPanel } from "../activate/registerCommands"
import { ContextEngineBlock } from "../shared/WebviewMessage"

/**
 * Service for generating reasoning showcase steps for the Bracket Context Engine
 */
export class ReasoningShowcaseService {
    private static instance: ReasoningShowcaseService
    private isGenerating: boolean = false
    private abortController: AbortController | null = null
    private openaiApiKey: string | null = null
    private model: string = "gpt-4o-mini"
    private codebaseOverviewPath: string = "experiments/gitlab/global_overview.md"
    private codebaseOverview: string = ""
    private processingSteps: string[] = []
    private currentStepIndex: number = 0
    private stdoutBuffer: string = ""
    private lastProcessedLine: number = 0
    private processingComplete: boolean = false
    private activeQuery: string = ""
    private _lastStepTime: number | null = null
    private _pendingOutput: string | null = null
    private _stepCheckTimer: NodeJS.Timeout | null = null
    private _periodicStepTimer: NodeJS.Timeout | null = null
    private _processedChunks: Set<string> = new Set()
    private _processedContents: Set<string> = new Set()

    private constructor() {
        // Private constructor to enforce singleton pattern
        // Load the codebase overview file
        this.loadCodebaseOverview()
        // Initialize notification flags
        this._hasNotifiedCompletion = false
        this._hasNotifiedRooCodeStart = false
    }

    /**
     * Get the singleton instance of the ReasoningShowcaseService
     */
    public static getInstance(): ReasoningShowcaseService {
        if (!ReasoningShowcaseService.instance) {
            ReasoningShowcaseService.instance = new ReasoningShowcaseService()
        }
        return ReasoningShowcaseService.instance
    }

    /**
     * Initialize the service with necessary API keys
     */
    public async initialize(): Promise<boolean> {
        try {
            this.openaiApiKey = await getOpenAiApiKey()

            // Ensure codebase overview is loaded
            if (!this.codebaseOverview) {
                await this.loadCodebaseOverview()
            }

            return !!this.openaiApiKey
        } catch (error) {
            console.error("Error initializing ReasoningShowcaseService:", error)
            return false
        }
    }

    /**
     * Load the codebase overview file
     */
    private async loadCodebaseOverview(): Promise<void> {
        try {
            // Read the file using VSCode API
            const workspaceFolders = vscode.workspace.workspaceFolders
            if (!workspaceFolders) {
                throw new Error("No workspace folder open")
            }
            const rootPath = workspaceFolders[0].uri.fsPath
            const uri = vscode.Uri.file(`${rootPath}/${this.codebaseOverviewPath}`)
            const fileContent = await vscode.workspace.fs.readFile(uri)
            this.codebaseOverview = Buffer.from(fileContent).toString('utf8')
            console.log("Loaded codebase overview file, length:", this.codebaseOverview.length)
        } catch (error) {
            console.error("Error loading codebase overview:", error)
            // Set a default value if file can't be loaded
            this.codebaseOverview = "This is a complex codebase with multiple interconnected domains."
        }
    }

    /**
     * Generate reasoning showcase steps for a query
     * @param query The user query
     * @param codebaseOverview Optional codebase overview (will use loaded overview if not provided)
     * @param onStep Callback function to handle each reasoning step
     */
    public async generateReasoningSteps(
        query: string,
        codebaseOverview?: string,
        onStep?: (step: string, stepNumber: number, isLast: boolean) => void
    ): Promise<void> {
        // Use the loaded codebase overview if none is provided
        const overviewText = codebaseOverview || this.codebaseOverview

        // Clear any existing context engine blocks when starting a new query
        const panel = getPanel()
        if (panel) {
            panel.webview.postMessage({
                type: "action",
                action: "clearContextEngineBlocks"
            })
        }

        if (this.isGenerating) {
            this.stopGeneration()
        }

        if (!this.openaiApiKey) {
            const initialized = await this.initialize()
            if (!initialized) {
                console.error("Failed to initialize ReasoningShowcaseService")
                return
            }
        }

        // Reset state for new query
        this.isGenerating = true
        this.abortController = new AbortController()
        this.processingSteps = []
        this.currentStepIndex = 0
        this.stdoutBuffer = ""
        this.lastProcessedLine = 0
        this.processingComplete = false
        this.activeQuery = query
        this._processedChunks.clear() // Clear the set of processed chunks
        this._hasNotifiedCompletion = false // Reset the completion notification flag
        this._hasNotifiedRooCodeStart = false // Reset the RooCode start notification flag

        try {
            // Create a single initial block to show we're starting the analysis
            const initialBlockId = `reasoning-block-initial-${Date.now()}`
            const initialBlock = {
                id: initialBlockId,
                content: `Analyzing query: "${query}"...`,
                isComplete: false,
                isLoading: true
            }
            this.sendInlineContextEngineBlock(initialBlock)

            // Initial delay to show we're thinking
            await this.delay(1200)

            // Generate initial steps with a single API call for efficiency
            // These will be shown while waiting for real data from the context engine
            const initialStepsPrompt = this.createInitialStepsPrompt(query, overviewText)
            const initialStepsResponse = await this.callOpenAI(initialStepsPrompt)

            // Parse the response into individual steps
            let initialSteps = this.parseStepsFromResponse(initialStepsResponse)

            // Ensure we have unique steps (no duplicates)
            initialSteps = [...new Set(initialSteps)]

            // If we have fewer than 2 steps, add generic ones
            const genericSteps = [
                `Scanning codebase architecture for components related to ${query}. Looking for key domains and services based on system design patterns.`,
                `Identifying relevant domain models and business logic. Examining core entities that might handle the requested functionality.`
            ]

            while (initialSteps.length < 2) {
                initialSteps.push(genericSteps[initialSteps.length % genericSteps.length])
            }

            // Limit to 2 steps maximum
            initialSteps = initialSteps.slice(0, 2)

            // Store these steps for display
            this.processingSteps = initialSteps

            // Remove the initial block once we start showing actual steps
            panel?.webview.postMessage({
                type: "action",
                action: "clearContextEngineBlocks"
            })

            // Start displaying steps in a non-blocking way
            // Use setTimeout to ensure this doesn't block the main thread
            setTimeout(() => {
                this.startDisplayingSteps(query, onStep)

                // Set up a timer to generate additional steps periodically
                this.setupPeriodicStepGeneration(query, overviewText)
            }, 0)

        } catch (error) {
            console.error("Error generating reasoning steps:", error)
            this.isGenerating = false
            this.abortController = null
        }
    }

    /**
     * Set up periodic step generation to ensure we keep showing steps during long-running processes
     * @param query The user query
     * @param overviewText The codebase overview text
     */
    private setupPeriodicStepGeneration(query: string, overviewText: string): void {
        // Clear any existing timer
        if (this._periodicStepTimer) {
            clearTimeout(this._periodicStepTimer)
            this._periodicStepTimer = null
        }

        // Set up a timer to generate additional steps periodically
        this._periodicStepTimer = setInterval(async () => {
            // Only generate new steps if we're still processing and don't have too many steps already
            if (this.isGenerating && !this.processingComplete && this.processingSteps.length < 10) {
                console.log("Periodic step generation triggered")
                try {
                    // Generate a new step based on the query and what we've processed so far
                    const periodicStepPrompt = this.createPeriodicStepPrompt(query, overviewText, this.processingSteps)
                    const stepResponse = await this.callOpenAI(periodicStepPrompt)

                    // Clean up the response
                    const newStep = stepResponse.trim()
                        .replace(/^"/,'') // Remove leading quote if present
                        .replace(/"$/,'') // Remove trailing quote if present
                        .replace(/^```.*\n/,'') // Remove markdown code block start
                        .replace(/\n```$/,'') // Remove markdown code block end
                        .trim()

                    console.log(`Generated periodic step: ${newStep}`)

                    if (newStep && newStep.length > 20) {
                        // Check if this step is too similar to existing steps
                        const isDuplicate = this.processingSteps.some(step => {
                            const similarity = this.calculateSimilarity(step, newStep)
                            return similarity > 0.7 // If more than 70% similar, consider it a duplicate
                        })

                        if (!isDuplicate) {
                            // Add the new step to our list but don't display it directly
                            // It will be displayed through the startDisplayingSteps method
                            this.processingSteps.push(newStep)
                            console.log(`Added new periodic step: ${newStep}`)

                            // If we're not currently displaying steps, start displaying them
                            if (this.currentStepIndex >= this.processingSteps.length - 1) {
                                this.startDisplayingSteps(this.activeQuery)
                            }
                        } else {
                            console.log(`Skipping duplicate periodic step: ${newStep}`)
                        }
                    }
                } catch (error) {
                    console.error("Error generating periodic step:", error)
                }
            } else if (!this.isGenerating || this.processingComplete) {
                // If we're done processing, clear the timer
                console.log("Clearing periodic step generation timer")
                clearInterval(this._periodicStepTimer!)
                this._periodicStepTimer = null
            }
        }, 15000) // Generate a new step every 15 seconds
    }

    /**
     * Create a prompt for generating a periodic step
     * @param query The user query
     * @param overviewText The codebase overview text
     * @param existingSteps The existing steps
     * @returns The prompt for generating a periodic step
     */
    private createPeriodicStepPrompt(query: string, overviewText: string, existingSteps: string[]): string {
        return `You are the Bracket Context Engine, a sophisticated code analysis system that thinks like a Staff Software Engineer with deep domain expertise. You're analyzing a user query to find relevant code in a large, complex codebase.

USER QUERY: "${query}"

CODEBASE OVERVIEW:
${overviewText.substring(0, 3000)}...

EXISTING STEPS:
${existingSteps.join('\n')}

TASK:
Generate ONE new, concise, action-oriented reasoning step that would logically follow the existing steps. This should reflect what a context engine would be doing at this point in the analysis process. Your step should be different from the existing steps and show progression in the analysis.

Your reasoning step should:
1. Be action-oriented ("Scanning for...", "Identifying...", "Narrowing down...", "Examining...")
2. Reflect the actual mental process a senior engineer would use to locate code
3. Be concise and focused (1-2 sentences)
4. Show deeper analysis than the previous steps
5. Be specific about what's being analyzed

IMPORTANT:
- Your step must be different from the existing steps
- Focus on the reasoning process, not descriptive explanations
- Keep it concise - avoid verbose explanations

Your response should be a single string with the reasoning step, nothing else.`
    }

    /**
     * Simulate streaming text by gradually revealing content
     * @param blockId The ID of the block to update
     * @param fullText The complete text to reveal
     * @param charsPerUpdate Number of characters to reveal per update
     */
    private async simulateStreamingText(blockId: string, fullText: string, charsPerUpdate: number = 20): Promise<void> {
        return new Promise<void>((resolve) => {
            // Start with an empty string to avoid showing the query for a second
            let currentPosition = 0

            // Function to update the text incrementally
            const updateText = () => {
                if (!this.isGenerating) {
                    resolve(); // Resolve the promise if generation has stopped
                    return;
                }

                // Update the block with the current partial text
                const partialBlock = {
                    id: blockId,
                    content: fullText.substring(0, currentPosition),
                    isComplete: false,
                    isLoading: true
                }

                this.updateInlineContextEngineBlock(partialBlock)

                // Find the next word boundary for more natural breaks
                const nextSpace = fullText.indexOf(' ', currentPosition + 1)
                const nextPosition = nextSpace > 0 ?
                    Math.min(nextSpace, currentPosition + charsPerUpdate) :
                    Math.min(currentPosition + charsPerUpdate, fullText.length)

                // Advance the position for the next update
                currentPosition = nextPosition

                // Variable delay between updates to simulate natural thinking and typing
                // Slow down at punctuation for more natural pauses
                const lastChar = fullText.charAt(currentPosition - 1)
                const isPunctuation = ['.', ',', ':', ';', '?', '!'].includes(lastChar)

                const baseDelay = isPunctuation ? 150 : 50 // Longer pause after punctuation
                const delay = Math.random() * baseDelay + 30 // Variable delay

                // If we've reached the end of the text, show the complete block and resolve
                if (currentPosition >= fullText.length) {
                    // Ensure the full text is shown at the end
                    const completeBlock = {
                        id: blockId,
                        content: fullText,
                        isComplete: false,
                        isLoading: true
                    }

                    this.updateInlineContextEngineBlock(completeBlock)

                    // Final pause to let the user read the completed step, then resolve
                    setTimeout(resolve, 300);
                } else {
                    // Continue updating with the next chunk after a delay
                    setTimeout(updateText, delay);
                }
            }

            // Initial delay to simulate thinking before starting
            setTimeout(updateText, Math.random() * 300 + 200); // 200-500ms initial thinking delay
        });
    }

    /**
     * Stop the generation process
     */
    public stopGeneration(): void {
        if (this.abortController) {
            this.abortController.abort()
        }
        this.isGenerating = false
    }

    /**
     * Create a prompt that generates initial reasoning steps with sophisticated engineering reasoning
     */
    private createInitialStepsPrompt(query: string, codebaseOverview: string): string {
        return `You are the Bracket Context Engine, a sophisticated code analysis system that thinks like a Staff Software Engineer with deep domain expertise. You're analyzing a user query to find relevant code in a large, complex codebase.

USER QUERY: "${query}"

CODEBASE OVERVIEW:
${codebaseOverview.substring(0, 5000)}...

TASK:
Generate 2 highly informative, action-oriented reasoning steps that reflect the initial thought process of a Staff SWE analyzing this query to locate relevant code. These steps should be detailed and specific, not generic. Format your response as a JSON array of strings.

Your reasoning steps should:
1. Be action-oriented ("Scanning for...", "Identifying...", "Narrowing down...", "Examining...")
2. Reflect the actual mental process a senior engineer would use to locate code
3. Be detailed and specific to this query (1-2 sentences per step)
4. Focus on the early stages of code localization (domain analysis, architectural understanding)
5. Include specific technical details relevant to the query

IMPORTANT:
- Generate EXACTLY 2 steps
- Each step must reflect an action being taken to understand the query and locate relevant code
- Make the steps highly informative and specific to the query, not generic
- Focus on the reasoning process, not descriptive explanations
- Do not duplicate content between steps
- Each step should provide meaningful insight into the code localization process

Example format (but with content specific to this codebase):
[
  "Scanning domain architecture for messaging-related components, focusing on WebSocket handlers and real-time communication protocols used in chat applications. Identifying key bounded contexts based on the event-driven architecture pattern.",
  "Examining the message persistence layer to understand how chat history is stored and retrieved. Looking for database schemas and caching mechanisms that support real-time updates and message synchronization across devices."
]

Your response should ONLY contain the JSON array with action-oriented reasoning steps, nothing else.`
    }

    /**
     * Create a prompt that generates a reasoning step based on context engine output
     */
    private createStepFromOutputPrompt(query: string, contextEngineOutput: string): string {
        return `You are the Bracket Context Engine, a sophisticated code analysis system that thinks like a Staff Software Engineer with deep domain expertise. You're analyzing a user query to find relevant code in a large, complex codebase.

USER QUERY: "${query}"

CONTEXT ENGINE OUTPUT:
${contextEngineOutput}

TASK:
Based on the above context engine output, generate ONE concise, action-oriented reasoning step that reflects what the context engine is currently doing. This should be from the perspective of a Staff SWE analyzing the codebase. Your response should be a single string, not JSON.

Your reasoning step should:
1. Be action-oriented ("Scanning for...", "Identifying...", "Narrowing down...", "Examining...")
2. Reflect the actual processing happening in the context engine output
3. Be concise and focused (1-2 sentences)
4. Provide specific details about what's being analyzed

IMPORTANT:
- Focus on the specific processing shown in the context engine output
- Be specific about domains, functions, or patterns being analyzed
- Keep it concise - avoid verbose explanations

Example good responses:
"Analyzing domain relevance scores to identify the most promising code areas. Focusing on Authentication and User Management domains with scores above 0.8."
"Extracting function implementations from the Pipeline domain. Examining execution flows in CreatePipelineService and BuildService classes."
"Evaluating function relevance based on semantic similarity to the query. Prioritizing functions with direct parameter matches and similar business logic."

Your response should be a single string with the reasoning step, nothing else.`
    }

    /**
     * Parse the steps from the API response
     */
    private parseStepsFromResponse(response: string): string[] {
        try {
            // Try to parse as JSON array with better markdown code block handling
            const cleanedResponse = response.trim()
                .replace(/^```json\s*/g, '') // Remove markdown json code block start
                .replace(/^```\s*/g, '')     // Remove any other code block start
                .replace(/\s*```$/g, '')     // Remove markdown code block end
                .trim()

            const parsedSteps = JSON.parse(cleanedResponse)

            if (Array.isArray(parsedSteps)) {
                // Ensure each step is a non-empty string with proper content
                const validSteps = parsedSteps
                    .filter(step => typeof step === 'string' && step.trim().length > 20)
                    .map(step => step.trim())

                // Remove any duplicates
                return [...new Set(validSteps)]
            }

            // Fallback if not an array - create action-oriented default steps
            return [
                "Scanning codebase architecture for relevant domains and components. Identifying key services and patterns that align with the query requirements.",
                "Examining specific implementation files in the identified domains. Looking for service classes and utility functions that handle the core functionality.",
                "Analyzing integration points between components. Searching for methods that implement the requested behavior and their dependencies."
            ]
        } catch (error) {
            console.error("Error parsing steps from response:", error)

            // Try regex extraction for arrays of strings
            try {
                const stringRegex = /"((?:\\.|[^"])*?)"/g
                const steps: string[] = []
                let stringMatch

                while ((stringMatch = stringRegex.exec(response)) !== null) {
                    const step = stringMatch[1].trim()
                    if (step.length > 20) {
                        steps.push(step)
                    }
                }

                if (steps.length > 0) {
                    // Remove any duplicates
                    return [...new Set(steps)].slice(0, 3)
                }
            } catch (e) {
                console.log("Regex extraction failed", e)
            }

            // Last resort: split by newlines and filter for meaningful content
            const lines = response.split('\n')
                .map(line => line.trim())
                .filter(line =>
                    line.length > 20 && // Minimum meaningful length
                    !line.startsWith('[') &&
                    !line.startsWith(']') &&
                    !line.includes('```')
                )
                .slice(0, 3) // Limit to 3 steps

            // If we have at least one meaningful line, use those
            if (lines.length > 0) {
                return lines
            }

            // If all else fails, return action-oriented default steps
            return [
                "Scanning codebase architecture for relevant domains and components. Identifying key services and patterns that align with the query requirements.",
                "Examining specific implementation files in the identified domains. Looking for service classes and utility functions that handle the core functionality.",
                "Analyzing integration points between components. Searching for methods that implement the requested behavior and their dependencies."
            ]
        }
    }

    /**
     * Call the OpenAI API to generate a reasoning step
     */
    private async callOpenAI(prompt: string): Promise<string> {
        try {
            if (!this.openaiApiKey) {
                throw new Error("OpenAI API key not available")
            }

            const response = await axios.post(
                "https://api.openai.com/v1/chat/completions",
                {
                    model: this.model,
                    messages: [{ role: "user", content: prompt }],
                    temperature: 0.7,
                    max_tokens: 500, // Increased token limit for more detailed responses
                    top_p: 0.95,     // Slightly more focused sampling
                    presence_penalty: 0.1 // Slight penalty for repetition
                },
                {
                    headers: {
                        "Content-Type": "application/json",
                        "Authorization": `Bearer ${this.openaiApiKey}`
                    },
                    signal: this.abortController?.signal
                }
            )

            const content = response.data.choices[0]?.message?.content || ""
            if (!content) {
                console.warn("Empty response from OpenAI API")
                return JSON.stringify([
                    "Scanning domain architecture for components that match query intent. Identifying key bounded contexts and services based on domain-driven design principles in the codebase.",
                    "Narrowing focus to specific implementation files in the identified domains. Looking for service classes that implement core business logic using Command or Mediator patterns.",
                    "Examining integration points and cross-cutting concerns. Searching for specific methods that handle the requested functionality while considering authentication and error handling patterns."
                ])
            }

            return content
        } catch (error) {
            if (axios.isCancel(error)) {
                console.log("Request canceled")
                return "Generation canceled"
            }
            console.error("Error calling OpenAI API:", error)

            // Return a fallback JSON array with meaningful steps
            return JSON.stringify([
                "Scanning domain architecture for components that match query intent. Identifying key bounded contexts and services based on domain-driven design principles in the codebase.",
                "Narrowing focus to specific implementation files in the identified domains. Looking for service classes that implement core business logic using Command or Mediator patterns.",
                "Examining integration points and cross-cutting concerns. Searching for specific methods that handle the requested functionality while considering authentication and error handling patterns."
            ])
        }
    }



    /**
     * Send an inline context engine block to the webview
     * @param block The context engine block to send
     */
    private sendInlineContextEngineBlock(block: any): void {
        const panel = getPanel()
        if (panel) {
            panel.webview.postMessage({
                type: "inlineContextEngine",
                contextEngineBlocks: [block]
            })
        }
    }

    /**
     * Update an existing inline context engine block in the webview
     * @param block The updated context engine block
     */
    private updateInlineContextEngineBlock(block: any): void {
        const panel = getPanel()
        if (panel) {
            panel.webview.postMessage({
                type: "updateContextEngineBlock",
                block: block
            })
        }
    }

    /**
     * Utility function to create a delay
     */
    private async delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms))
    }

    /**
     * Start displaying reasoning steps
     * @param query The user query
     * @param onStep Optional callback for each step
     */
    private async startDisplayingSteps(
        query: string,
        onStep?: (step: string, stepNumber: number, isLast: boolean) => void
    ): Promise<void> {
        console.log(`Starting to display ${this.processingSteps.length} reasoning steps`)

        // Process each step with streaming effect
        // Calculate total time to spread across all steps (aim for ~15 seconds total for initial steps)
        const totalTimeMs = 15000 // 15 seconds total
        const timePerStepMs = totalTimeMs / this.processingSteps.length

        // Start from where we left off
        const startIndex = this.currentStepIndex
        console.log(`Starting from step index ${startIndex}`)

        // If we've already sent the completion notification, don't send it again
        const shouldSendCompletionMessage = this.processingComplete && !this._hasNotifiedCompletion

        // Process steps one at a time with non-blocking delays between them
        this.processNextStep(startIndex, query, totalTimeMs, timePerStepMs, shouldSendCompletionMessage, onStep)
    }

    /**
     * Process the next step in a non-blocking way
     * @param index The index of the step to process
     * @param query The user query
     * @param totalTimeMs Total time to spread across all steps
     * @param timePerStepMs Time per step
     * @param shouldSendCompletionMessage Whether to send the completion message
     * @param onStep Optional callback for each step
     */
    private processNextStep(
        index: number,
        query: string,
        totalTimeMs: number,
        timePerStepMs: number,
        shouldSendCompletionMessage: boolean,
        onStep?: (step: string, stepNumber: number, isLast: boolean) => void
    ): void {
        // If we've reached the end of the steps or generation has stopped, we're done
        if (index >= this.processingSteps.length || !this.isGenerating) {
            // If we've completed all steps and processing is complete, send the completion message
            if (shouldSendCompletionMessage) {
                console.log("All steps displayed and processing complete, sending completion message")
                setTimeout(async () => {
                    const panel = getPanel()
                    if (panel) {
                        panel.webview.postMessage({
                            type: "action",
                            action: "contextEngineStepComplete"
                        })
                        this._hasNotifiedCompletion = true
                    }
                }, 500)
            } else if (!this.processingComplete) {
                console.log("All current steps displayed, but processing not yet complete")
                // Set up a timer to periodically check for and display new steps
                this.setupStepCheckTimer(query, onStep)
            }
            return
        }

        const isLast = index === this.processingSteps.length - 1 && this.processingComplete
        const stepNumber = index + 1
        const stepContent = this.processingSteps[index]

        console.log(`Displaying step ${stepNumber}: ${stepContent.substring(0, 50)}...`)

        // Create a unique ID for this step
        const blockId = `reasoning-block-${stepNumber}-${Date.now()}`

        // Create initial block with empty content
        const initialBlock = {
            id: blockId,
            content: "",
            isComplete: false,
            isLoading: true
        }

        // Send the initial block
        this.sendInlineContextEngineBlock(initialBlock)

        // Thinking delay before starting to stream content
        // Shorter thinking time for later steps to show progress faster
        const thinkingTime = Math.max(300, 500 - (index * 50)) // Start with 500ms, decrease for later steps

        // Use setTimeout for non-blocking delays
        setTimeout(() => {
            if (!this.isGenerating) return

            // Calculate streaming speed to fit within our time budget for this step
            // We want each step to take approximately timePerStepMs minus the thinking time
            const streamingTimeMs = Math.min(3000, timePerStepMs - thinkingTime) // Cap at 3 seconds max
            const charsPerUpdate = Math.max(5, Math.floor(stepContent.length / (streamingTimeMs / 100))) // Assuming ~100ms per update

            // Simulate streaming by gradually revealing the content
            this.simulateStreamingText(blockId, stepContent, charsPerUpdate).then(() => {
                if (!this.isGenerating) return

                // Mark as complete when done streaming
                const finalBlock = {
                    id: blockId,
                    content: stepContent,
                    isComplete: isLast,
                    isLoading: !isLast
                }

                // Update the block in the webview
                this.updateInlineContextEngineBlock(finalBlock)

                // Notify the callback if provided
                if (onStep) {
                    onStep(stepContent, stepNumber, isLast)
                }

                // Update the current step index
                this.currentStepIndex = index + 1
                console.log(`Updated current step index to ${this.currentStepIndex}`)

                // Wait before starting the next step
                const pauseTime = !isLast ? Math.max(300, 800 - (index * 50)) : 0 // Start with 800ms, decrease for later steps

                // Process the next step after a delay
                setTimeout(() => {
                    this.processNextStep(index + 1, query, totalTimeMs, timePerStepMs, shouldSendCompletionMessage, onStep)
                }, pauseTime)
            }).catch(error => {
                console.error("Error simulating streaming text:", error)
                // Try to continue with the next step
                setTimeout(() => {
                    this.processNextStep(index + 1, query, totalTimeMs, timePerStepMs, shouldSendCompletionMessage, onStep)
                }, 500)
            })
        }, thinkingTime)
    }

    /**
     * Set up a timer to periodically check for and display new steps
     * @param query The user query
     * @param onStep Optional callback for each step
     */
    private setupStepCheckTimer(query: string, onStep?: (step: string, stepNumber: number, isLast: boolean) => void): void {
        // Clear any existing timer
        if (this._stepCheckTimer) {
            clearTimeout(this._stepCheckTimer)
            this._stepCheckTimer = null
        }

        // If we've already sent the completion notification, don't set up a new timer
        if (this._hasNotifiedCompletion) {
            console.log("Completion already notified, not setting up step check timer")
            return
        }

        // Set up a new timer
        this._stepCheckTimer = setTimeout(() => {
            // Check if we have new steps to display
            if (this.isGenerating && this.currentStepIndex < this.processingSteps.length) {
                console.log(`Timer detected new steps: ${this.processingSteps.length - this.currentStepIndex} new steps`)
                // Display the new steps in a non-blocking way
                setTimeout(() => {
                    this.startDisplayingSteps(query, onStep)
                }, 0)
            } else if (this.isGenerating && !this.processingComplete && !this._hasNotifiedCompletion) {
                // No new steps yet, but processing is still ongoing, so set up another timer
                console.log("No new steps yet, checking again later")
                this.setupStepCheckTimer(query, onStep)
            }
        }, 10000) // Check every 10 seconds
    }



    /**
     * Process stdout data from the context engine
     * @param stdout The stdout data from the context engine
     */
    public async processStdoutData(stdout: string): Promise<void> {
        if (!this.isGenerating) return

        // Log the received stdout data for debugging
        console.log(`ReasoningShowcaseService received stdout: ${stdout.substring(0, 100)}${stdout.length > 100 ? '...' : ''}`)

        // Create a hash of the stdout content to detect exact duplicates
        const contentHash = this.hashString(stdout)

        // Check if we've already processed this exact chunk
        if (this._processedChunks.has(contentHash)) {
            console.log(`Skipping exact duplicate stdout chunk with hash ${contentHash}`)
            return
        }

        // Add this chunk to our processed set
        this._processedChunks.add(contentHash)

        // Append the new data to the buffer
        this.stdoutBuffer += stdout

        // Split the buffer into lines
        const lines = this.stdoutBuffer.split('\n')

        // Process new lines
        const newLines = lines.slice(this.lastProcessedLine)
        if (newLines.length === 0) return

        // Update the last processed line
        this.lastProcessedLine = lines.length

        // Check if we have enough new content to generate a step
        // We'll be more aggressive in generating steps by using a lower threshold
        const newContent = newLines.join('\n')
        const hasSubstantialContent = newContent.length > 50 // Lower threshold

        // Look for meaningful patterns in the output
        const significantOutput = this.extractSignificantOutput(newLines)

        // If we don't have significant output but have substantial content, use the content directly
        const outputToUse = significantOutput || (hasSubstantialContent ? newContent.substring(0, 500) : null)

        // Only proceed if we have output to use
        if (!outputToUse) return

        // Check if we've already processed very similar content
        // This helps prevent duplicate messages that come in immediately one after another
        if (this._processedContents.size > 0) {
            // Check if this content is too similar to recently processed content
            for (const existingContent of this._processedContents) {
                const similarity = this.calculateSimilarity(existingContent, outputToUse)
                if (similarity > 0.7) { // If more than 70% similar, consider it a duplicate
                    console.log(`Skipping similar content with similarity ${similarity.toFixed(2)}`)
                    return
                }
            }
        }

        // Add this content to our processed set (keep only the last 5 items)
        this._processedContents.add(outputToUse)
        if (this._processedContents.size > 5) {
            // Remove the oldest item (first item in the set)
            const firstItem = this._processedContents.values().next().value
            if (firstItem) {
                this._processedContents.delete(firstItem)
            }
        }

        // If processing is already complete, don't generate new steps
        // This prevents new steps from being generated after we've notified completion
        if (this._hasNotifiedCompletion) {
            console.log("Processing already complete, not generating new steps")
            return
        }

        // Don't generate steps too frequently - check if enough time has passed since the last step
        const now = Date.now()
        if (!this._lastStepTime) {
            this._lastStepTime = now
        } else if (now - this._lastStepTime < 5000) { // At least 5 seconds between steps
            // Not enough time has passed, but store the output for later use
            this._pendingOutput = outputToUse
            return
        }
        this._lastStepTime = now

        try {
            // Generate a new reasoning step based on the output
            const stepPrompt = this.createStepFromOutputPrompt(this.activeQuery, outputToUse)
            const stepResponse = await this.callOpenAI(stepPrompt)

            // Clean up the response (it should be a single string, not JSON)
            const newStep = stepResponse.trim()
                .replace(/^"/, '') // Remove leading quote if present
                .replace(/"$/, '') // Remove trailing quote if present
                .replace(/^```.*\n/, '') // Remove markdown code block start
                .replace(/\n```$/, '') // Remove markdown code block end
                .trim()

            console.log(`Generated new reasoning step from stdout: ${newStep}`)

            if (newStep && newStep.length > 20) {
                // Check if this step is too similar to existing steps
                const isDuplicate = this.processingSteps.some(step => {
                    // Calculate similarity (simple check for now)
                    const similarity = this.calculateSimilarity(step, newStep)
                    return similarity > 0.7 // If more than 70% similar, consider it a duplicate
                })

                if (!isDuplicate) {
                    // Add the new step to our list but don't display it directly
                    // It will be displayed through the startDisplayingSteps method
                    this.processingSteps.push(newStep)
                    console.log(`Added new step from stdout: ${newStep}`)

                    // If we're not currently displaying steps, start displaying them
                    if (this.currentStepIndex >= this.processingSteps.length - 1) {
                        this.startDisplayingSteps(this.activeQuery)
                    }
                } else {
                    console.log(`Skipping duplicate step: ${newStep}`)
                }
            }

            // Clear any pending output since we've processed it
            this._pendingOutput = null
        } catch (error) {
            console.error("Error generating reasoning step from stdout:", error)
        }
    }

    /**
     * Create a simple hash of a string
     * @param str The string to hash
     * @returns A hash string
     */
    private hashString(str: string): string {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32bit integer
        }
        return hash.toString(16);
    }

    // hashString method removed as it's no longer needed

    /**
     * Extract significant output from the context engine stdout
     * @param lines The lines of stdout data
     * @returns A string containing significant output, or null if none found
     */
    private extractSignificantOutput(lines: string[]): string | null {
        // Join the lines for easier pattern matching
        const output = lines.join('\n')

        // Log what we're analyzing
        console.log(`Analyzing ${lines.length} lines for significant output`)

        // Look for specific patterns that indicate meaningful processing
        if (output.includes("Time taken for step 1:") || output.includes("domain relevance")) {
            return "Completed domain relevance analysis. The context engine has identified the most relevant domains in the codebase based on the query."
        }

        if (output.includes("Time taken for step 2:") || output.includes("functions relevance")) {
            return "Completed function relevance analysis. The context engine has identified and ranked functions within the relevant domains."
        }

        // Look for any mentions of functions
        if (output.match(/[Ff]ound|[Ii]dentified|[Ss]elected|[Aa]nalyz(ed|ing)|[Ee]valuat(ed|ing)|[Rr]etriev(ed|ing)|[Pp]rocess(ed|ing)|[Ee]xtract(ed|ing)/) &&
            output.match(/function|method|class|component|module|service/)) {
            // Try to extract a number if present
            const match = output.match(/(\d+)\s+(?:relevant\s+)?(?:function|method|class|component|module|service)/i)
            if (match) {
                return `Identified ${match[1]} relevant code elements across the codebase. These have been ranked by relevance to the query.`
            } else {
                return "Analyzing relevant code elements in the codebase. Identifying functions and methods that match the query intent."
            }
        }

        // Look for domain relevance information
        if (output.match(/domain|subdomain|component|module|service/) &&
            output.match(/relevance|score|match|similarity|analysis|evaluation/)) {
            return "Analyzing domain relevance scores to identify the most promising code areas. Focusing on domains with the highest relevance scores."
        }

        // Look for function relevance information
        if (output.match(/function|method|class/) &&
            output.match(/relevance|score|match|similarity|analysis|evaluation/)) {
            return "Evaluating function relevance based on semantic similarity to the query. Prioritizing functions with direct parameter matches and similar business logic."
        }

        // Look for third pass filtering or reasoning
        if (output.match(/third pass|filtering|selection|reasoning|rationale|explanation|justification/i)) {
            return "Performing final analysis to select the most relevant functions. Applying semantic filtering to ensure the selected functions directly address the query."
        }

        // Look for JSON output which indicates completion
        if (output.includes("JSON_OUTPUT_START") || output.match(/output|result|complete|finish|done/i)) {
            return "Finalizing context analysis. Preparing the complete set of relevant functions with their metadata and content."
        }

        // Look for any processing indicators
        if (output.match(/process|analyz|evaluat|search|scan|examin|identif|extract/i)) {
            return "Processing codebase data to locate relevant code elements. Analyzing code structure and semantics to match the query intent."
        }

        // If we don't find any specific patterns but have substantial output, use it anyway
        if (output.length > 50) { // Lower threshold to be more aggressive
            // Extract a meaningful portion of the output
            const cleanedOutput = output
                .replace(/\[\d+m/g, '') // Remove ANSI color codes
                .replace(/\s+/g, ' ')   // Normalize whitespace
                .trim()
                .substring(0, 500)      // Limit length

            return cleanedOutput
        }

        return null
    }

    /**
     * Calculate similarity between two strings
     * @param str1 First string
     * @param str2 Second string
     * @returns A similarity score between 0 and 1
     */
    private calculateSimilarity(str1: string, str2: string): number {
        // Convert to lowercase for case-insensitive comparison
        const s1 = str1.toLowerCase()
        const s2 = str2.toLowerCase()

        // If either string is empty, return 0
        if (s1.length === 0 || s2.length === 0) return 0

        // If the strings are identical, return 1
        if (s1 === s2) return 1

        // Calculate Jaccard similarity using word sets
        const words1 = new Set(s1.split(/\s+/).filter(w => w.length > 3)) // Only consider words longer than 3 chars
        const words2 = new Set(s2.split(/\s+/).filter(w => w.length > 3))

        // Count common words
        let intersection = 0
        for (const word of words1) {
            if (words2.has(word)) intersection++
        }

        // Calculate Jaccard similarity: intersection size / union size
        const union = words1.size + words2.size - intersection
        return union > 0 ? intersection / union : 0
    }

    private _hasNotifiedCompletion: boolean = false;
    private _hasNotifiedRooCodeStart: boolean = false;

    /**
     * Notify that RooCode is starting
     * This will trigger the UI to collapse the reasoning blocks and context showcase
     */
    public notifyRooCodeStarting(): void {
        if (!this._hasNotifiedRooCodeStart) {
            console.log("ReasoningShowcaseService: Notifying RooCode starting")
            const panel = getPanel()
            if (panel) {
                // Send the message to collapse both the reasoning blocks and context showcase
                panel.webview.postMessage({
                    type: "action",
                    action: "rooCodeStarting"
                })
                console.log("ReasoningShowcaseService: Sent rooCodeStarting message to webview")
            } else {
                console.log("ReasoningShowcaseService: No panel available to send rooCodeStarting message")
            }
            this._hasNotifiedRooCodeStart = true;
        } else {
            console.log("ReasoningShowcaseService: Already notified RooCode starting, skipping")
        }
    }

    /**
     * Notify that context engine processing is complete
     */
    public notifyProcessingComplete(): void {
        console.log("ReasoningShowcaseService: Processing complete notification received")

        // Only send the completion notification once
        if (!this._hasNotifiedCompletion) {
            // Immediately notify that processing is complete to allow RooCline to continue
            const panel = getPanel()
            if (panel) {
                panel.webview.postMessage({
                    type: "action",
                    action: "contextEngineStepComplete"
                })
            }
            this._hasNotifiedCompletion = true;
            console.log("ReasoningShowcaseService: Sent contextEngineStepComplete notification")

            // Immediately notify that RooCode is starting
            // This will trigger the UI to collapse both the reasoning blocks and context showcase
            this.notifyRooCodeStarting();
        }

        // Mark as complete but continue processing in the background
        this.processingComplete = true

        // Process any pending output asynchronously without blocking
        if (this._pendingOutput) {
            console.log("Processing pending output in background")
            // Use setTimeout to ensure this doesn't block the main thread
            setTimeout(() => {
                this.processStdoutData(this._pendingOutput as string).catch(error => {
                    console.error("Error processing pending output:", error)
                })
            }, 0)
        }

        // Add a final step if we don't have many - do this asynchronously
        setTimeout(() => {
            if (this.processingSteps.length < 5) {
                const finalStep = "Finalizing context analysis. The context engine has completed processing and identified the most relevant code paths for the query."
                console.log(`Adding final step: ${finalStep}`)
                this.processingSteps.push(finalStep)
            }

            // Continue displaying steps in the background if we haven't shown all of them yet
            if (this.currentStepIndex < this.processingSteps.length - 1) {
                console.log(`Continuing to display steps in background: ${this.currentStepIndex} of ${this.processingSteps.length}`)
                this.startDisplayingSteps(this.activeQuery)
            }
        }, 0)

        // Clean up
        this.isGenerating = false
        this.abortController = null
        this._lastStepTime = null
        this._pendingOutput = null
        this._processedChunks.clear() // Clear the set of processed chunks
        this._processedContents.clear() // Clear the set of processed contents

        // Clear any periodic step generation timer
        if (this._periodicStepTimer) {
            console.log("Clearing periodic step generation timer")
            clearInterval(this._periodicStepTimer)
            this._periodicStepTimer = null
        }

        console.log("ReasoningShowcaseService: Processing complete cleanup finished")
    }
}
