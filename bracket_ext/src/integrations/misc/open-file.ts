import * as path from "path"
import * as os from "os"
import * as vscode from "vscode"
import { arePathsEqual, getWorkspacePath } from "../../utils/path"

export async function openImage(dataUri: string) {
	const matches = dataUri.match(/^data:image\/([a-zA-Z]+);base64,(.+)$/)
	if (!matches) {
		vscode.window.showErrorMessage("Invalid data URI format")
		return
	}
	const [, format, base64Data] = matches
	const imageBuffer = Buffer.from(base64Data, "base64")
	const tempFilePath = path.join(os.tmpdir(), `temp_image_${Date.now()}.${format}`)
	try {
		await vscode.workspace.fs.writeFile(vscode.Uri.file(tempFilePath), imageBuffer)
		await vscode.commands.executeCommand("vscode.open", vscode.Uri.file(tempFilePath))
	} catch (error) {
		vscode.window.showErrorMessage(`Error opening image: ${error}`)
	}
}

interface OpenFileOptions {
	create?: boolean
	content?: string
}

export async function openFile(filePath: string, options: OpenFileOptions = {}) {
	try {
		console.log(`[CONTEXT-SHOWCASE-DEBUG] openFile called with path: ${filePath}`);

		// Get workspace root
		const workspaceRoot = getWorkspacePath()
		if (!workspaceRoot) {
			console.error(`[CONTEXT-SHOWCASE-DEBUG] No workspace root found`);
			throw new Error("No workspace root found")
		}
		console.log(`[CONTEXT-SHOWCASE-DEBUG] Workspace root: ${workspaceRoot}`);

		// If path starts with ./ or /, resolve it relative to workspace root
		let fullPath = filePath;
		if (filePath.startsWith("./")) {
			fullPath = path.join(workspaceRoot, filePath.slice(2));
		} else if (filePath.startsWith("/")) {
			// Remove the leading slash and join with workspace root
			fullPath = path.join(workspaceRoot, filePath.slice(1));
		}
		console.log(`[CONTEXT-SHOWCASE-DEBUG] Resolved full path: ${fullPath}`);

		const uri = vscode.Uri.file(fullPath)
		console.log(`[CONTEXT-SHOWCASE-DEBUG] Created URI: ${uri.toString()}`);

		// Check if file exists
		try {
			await vscode.workspace.fs.stat(uri)
			console.log(`[CONTEXT-SHOWCASE-DEBUG] File exists: ${fullPath}`);
		} catch (statError) {
			// File doesn't exist
			console.log(`[CONTEXT-SHOWCASE-DEBUG] File does not exist: ${fullPath}`);
			if (!options.create) {
				console.error(`[CONTEXT-SHOWCASE-DEBUG] File does not exist and create option not set`);
				throw new Error("File does not exist")
			}

			// Create with provided content or empty string
			const content = options.content || ""
			console.log(`[CONTEXT-SHOWCASE-DEBUG] Creating new file: ${fullPath}`);
			await vscode.workspace.fs.writeFile(uri, Buffer.from(content, "utf8"))
			console.log(`[CONTEXT-SHOWCASE-DEBUG] File created successfully: ${fullPath}`);
		}

		// Check if the document is already open in a tab group that's not in the active editor's column
		try {
			let existingTabFound = false;
			for (const group of vscode.window.tabGroups.all) {
				const existingTab = group.tabs.find(
					(tab) =>
						tab.input instanceof vscode.TabInputText && arePathsEqual(tab.input.uri.fsPath, uri.fsPath),
				)
				if (existingTab) {
					existingTabFound = true;
					console.log(`[CONTEXT-SHOWCASE-DEBUG] File already open in a tab`);
					const activeColumn = vscode.window.activeTextEditor?.viewColumn
					const tabColumn = vscode.window.tabGroups.all.find((group) =>
						group.tabs.includes(existingTab),
					)?.viewColumn
					if (activeColumn && activeColumn !== tabColumn && !existingTab.isDirty) {
						console.log(`[CONTEXT-SHOWCASE-DEBUG] Closing existing tab in different column`);
						await vscode.window.tabGroups.close(existingTab)
					}
					break
				}
			}
			if (!existingTabFound) {
				console.log(`[CONTEXT-SHOWCASE-DEBUG] File not currently open in any tab`);
			}
		} catch (tabError) {
			console.log(`[CONTEXT-SHOWCASE-DEBUG] Error checking tabs: ${tabError instanceof Error ? tabError.message : String(tabError)}`);
		} // not essential, sometimes tab operations fail

		console.log(`[CONTEXT-SHOWCASE-DEBUG] Opening text document: ${uri.toString()}`);
		const document = await vscode.workspace.openTextDocument(uri)
		console.log(`[CONTEXT-SHOWCASE-DEBUG] Document opened, showing in editor`);
		await vscode.window.showTextDocument(document, { preview: false })
		console.log(`[CONTEXT-SHOWCASE-DEBUG] Document successfully shown in editor`);
		return true; // Indicate success
	} catch (error) {
		console.error(`[CONTEXT-SHOWCASE-DEBUG] Error in openFile: ${error instanceof Error ? error.message : String(error)}`);
		if (error instanceof Error) {
			vscode.window.showErrorMessage(`Could not open file: ${error.message}`)
		} else {
			vscode.window.showErrorMessage(`Could not open file!`)
		}
		throw error; // Re-throw to allow caller to catch
	}
}
