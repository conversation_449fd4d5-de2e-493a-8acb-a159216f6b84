import * as vscode from "vscode"
import * as path from "path"
import * as fs from "fs"
import { MermaidMappingService, MermaidDiagramResult, HierarchicalDiagramResult } from "../../services/mermaid/MermaidMappingService"
import { getWorkspacePath } from "../../utils/path"

/**
 * Provider for the Mermaid Companion webview
 */
export class MermaidCompanionProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = "bracket-mermaid-companion"
    private static instance: MermaidCompanionProvider | undefined

    private _view?: vscode.WebviewView
    private _extensionUri: vscode.Uri
    private _currentDiagram: MermaidDiagramResult | null = null
    private _currentFunctionPath: string | null = null
    private _currentFilePath: string | null = null
    private _hierarchicalDiagrams: MermaidDiagramResult[] = []
    private _mermaidService: MermaidMappingService
    private _outputChannel: vscode.OutputChannel

    constructor(extensionUri: vscode.Uri, outputChannel: vscode.OutputChannel) {
        this._extensionUri = extensionUri
        this._mermaidService = MermaidMappingService.getInstance()
        this._outputChannel = outputChannel
        MermaidCompanionProvider.instance = this
    }

    /**
     * Get the singleton instance of the MermaidCompanionProvider
     */
    public static getInstance(): MermaidCompanionProvider | undefined {
        return MermaidCompanionProvider.instance
    }

    /**
     * Resolve the webview view
     */
    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken
    ) {
        this._view = webviewView

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this._extensionUri]
        }

        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview)

        // Handle messages from the webview
        webviewView.webview.onDidReceiveMessage(message => {
            switch (message.type) {
                case "zoomIn":
                    this._handleZoomIn()
                    break
                case "zoomOut":
                    this._handleZoomOut()
                    break
                case "resetZoom":
                    this._handleResetZoom()
                    break
                case "exportAsPng":
                    this._handleExportAsPng(message.data)
                    break
                case "refreshDiagram":
                    this._handleRefreshDiagram(message.functionPath)
                    break
                case "toggleCompanion":
                    this._handleToggleCompanion()
                    break
                case "ready":
                    // If we have a current diagram, send it to the webview
                    if (this._currentDiagram) {
                        this._updateWebviewContent(this._currentDiagram)
                    } else {
                        this._showWelcomeMessage()
                    }
                    break
            }
        })

        // If we have a current diagram, update the webview
        if (this._currentDiagram) {
            this._updateWebviewContent(this._currentDiagram)
        } else {
            this._showWelcomeMessage()
        }
    }

    /**
     * Update the webview with a new mermaid diagram
     * @returns true if a diagram was found and displayed, false otherwise
     */
    public async updateDiagramForFunction(functionPath: string): Promise<boolean> {
        try {
            this._outputChannel.appendLine(`Looking for mermaid diagrams for function: ${functionPath}`)
            this._currentFunctionPath = functionPath
            this._currentFilePath = null

            // Get hierarchical diagrams
            const hierarchicalResult = await this._mermaidService.getHierarchicalDiagramsForFunction(functionPath)

            if (hierarchicalResult) {
                this._hierarchicalDiagrams = hierarchicalResult.diagrams
                this._currentDiagram = hierarchicalResult.leafDiagram

                this._outputChannel.appendLine(`Found ${this._hierarchicalDiagrams.length} hierarchical diagrams for function: ${functionPath}`)
                this._outputChannel.appendLine(`Using leaf diagram from domain: ${this._currentDiagram.domainName}`)

                if (this._view) {
                    this._updateWebviewWithHierarchicalDiagrams()
                } else {
                    this._outputChannel.appendLine(`Webview not available, cannot update content`)
                }
                return true;
            } else {
                this._currentDiagram = null
                this._hierarchicalDiagrams = []
                this._outputChannel.appendLine(`No mermaid diagrams found for function: ${functionPath}`)
                this._showNoMermaidMessage(functionPath)
                return false;
            }
        } catch (error) {
            this._outputChannel.appendLine(`Error updating diagram for function ${functionPath}: ${error}`)
            this._showErrorMessage(String(error))
            return false;
        }
    }

    /**
     * Update the webview with a new mermaid diagram for a file
     * @returns true if a diagram was found and displayed, false otherwise
     */
    public async updateDiagramForFile(filePath: string): Promise<boolean> {
        try {
            this._outputChannel.appendLine(`Looking for mermaid diagrams for file: ${filePath}`)
            this._currentFunctionPath = null
            this._currentFilePath = filePath

            // Get hierarchical diagrams
            const hierarchicalResult = await this._mermaidService.getHierarchicalDiagramsForFile(filePath)

            if (hierarchicalResult) {
                this._hierarchicalDiagrams = hierarchicalResult.diagrams
                this._currentDiagram = hierarchicalResult.leafDiagram

                this._outputChannel.appendLine(`Found ${this._hierarchicalDiagrams.length} hierarchical diagrams for file: ${filePath}`)
                this._outputChannel.appendLine(`Using leaf diagram from domain: ${this._currentDiagram.domainName}`)

                if (this._view) {
                    this._updateWebviewWithHierarchicalDiagrams()
                } else {
                    this._outputChannel.appendLine(`Webview not available, cannot update content`)
                }
                return true;
            } else {
                this._currentDiagram = null
                this._hierarchicalDiagrams = []
                this._outputChannel.appendLine(`No mermaid diagrams found for file: ${filePath}`)
                this._showNoMermaidMessage(filePath)
                return false;
            }
        } catch (error) {
            this._outputChannel.appendLine(`Error updating diagram for file ${filePath}: ${error}`)
            this._showErrorMessage(String(error))
            return false;
        }
    }

    /**
     * Get the HTML for the webview
     */
    private _getHtmlForWebview(webview: vscode.Webview): string {
        // Get the local path to main script run in the webview
        const scriptUri = webview.asWebviewUri(
            vscode.Uri.joinPath(this._extensionUri, "media", "mermaid-companion", "main.js")
        )

        // Get the local path to css styles
        const styleMainUri = webview.asWebviewUri(
            vscode.Uri.joinPath(this._extensionUri, "media", "mermaid-companion", "main.css")
        )

        // Get the local path to codicons CSS
        const codiconsUri = webview.asWebviewUri(
            vscode.Uri.joinPath(this._extensionUri, "node_modules", "@vscode", "codicons", "dist", "codicon.css")
        )

        // Get the local path to codicons font file
        const codiconsFontUri = webview.asWebviewUri(
            vscode.Uri.joinPath(this._extensionUri, "node_modules", "@vscode", "codicons", "dist", "codicon.ttf")
        )

        // Get the local path to mermaid
        const mermaidUri = webview.asWebviewUri(
            vscode.Uri.joinPath(this._extensionUri, "webview-ui", "node_modules", "mermaid", "dist", "mermaid.min.js")
        )

        // Use a nonce to only allow specific scripts to be run
        const nonce = this._getNonce()

        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; font-src ${webview.cspSource}; script-src 'nonce-${nonce}'; img-src data: ${webview.cspSource} https:;">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <link href="${styleMainUri}" rel="stylesheet">
            <link href="${webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, "media", "mermaid-companion", "breadcrumb-enhancements.css"))}" rel="stylesheet">
            <link href="${codiconsUri}" rel="stylesheet">
            <style>
                @font-face {
                    font-family: 'codicon';
                    font-display: block;
                    src: url("${codiconsFontUri}?38dcd33a732ebca5a557e04831e9e235") format("truetype");
                }
                .codicon {
                    font: normal normal normal 16px/1 codicon;
                    display: inline-block;
                    text-decoration: none;
                    text-rendering: auto;
                    text-align: center;
                    -webkit-font-smoothing: antialiased;
                    -moz-osx-font-smoothing: grayscale;
                    user-select: none;
                    -webkit-user-select: none;
                    -ms-user-select: none;
                }
            </style>
            <title>Architecture Lens</title>
        </head>
        <body>
            <div class="container">
                <header class="header">
                    <div class="title-bar">
                        <div class="title">Architecture Lens</div>
                        <div class="actions">
                        </div>
                    </div>
                </header>

                <div class="info-panel">
                    <div class="domain-section">
                        <div class="label">Domain</div>
                        <div id="domain-name" class="value"></div>
                    </div>
                    <div class="function-section">
                        <div class="label">Function</div>
                        <div id="function-path" class="value"></div>
                    </div>
                </div>

                <div class="toolbar">
                    <div class="toolbar-group" id="hierarchy-controls" style="flex-grow: 1; margin: 0;">
                        <div class="breadcrumb-container" id="breadcrumb-container" style="width: 100%;">
                            <!-- Breadcrumbs will be populated dynamically -->
                        </div>
                    </div>
                </div>

                <div class="main-content">
                    <div class="diagram-container">
                        <div id="diagram"></div>
                        <div class="loading">
                            <div class="loading-spinner"></div>
                            <div>Loading diagram...</div>
                        </div>
                        <div class="diagram-controls">
                            <div class="zoom-level">100%</div>
                        </div>
                    </div>

                    <div id="minimap" class="minimap">
                        <div class="minimap-viewport"></div>
                    </div>
                </div>

                <div id="message" class="message"></div>

                <div class="status-bar">
                    <div class="status-item" id="diagram-status">Ready</div>
                    <div class="status-item" id="diagram-size"></div>
                </div>
            </div>
            <script nonce="${nonce}" src="${mermaidUri}"></script>
            <script nonce="${nonce}" src="${scriptUri}"></script>
        </body>
        </html>`
    }

    /**
     * Generate a nonce for the webview
     */
    private _getNonce(): string {
        let text = ""
        const possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
        for (let i = 0; i < 32; i++) {
            text += possible.charAt(Math.floor(Math.random() * possible.length))
        }
        return text
    }

    /**
     * Update the webview content with a new mermaid diagram
     */
    private _updateWebviewContent(diagram: MermaidDiagramResult): void {
        if (!this._view) {
            return
        }

        // Extract the mermaid code from the markdown
        const mermaidCode = this._extractMermaidCode(diagram.diagram)

        // Extract just the function name without path or class
        let functionName = "";
        let displayPath = "";

        if (this._currentFunctionPath) {
            // Function-level granularity
            displayPath = this._currentFunctionPath;

            // First try to get the part after the last colon (for class methods)
            const colonParts = this._currentFunctionPath.split(":")
            if (colonParts.length > 1) {
                const lastPart = colonParts[colonParts.length - 1]

                // Check if it's a class method (contains a dot)
                if (lastPart.includes(".")) {
                    // Get the part after the last dot
                    const dotParts = lastPart.split(".")
                    functionName = dotParts[dotParts.length - 1]
                } else {
                    functionName = lastPart
                }
            } else {
                // If no colon, just use the last part of the path
                const pathParts = this._currentFunctionPath.split("/")
                functionName = pathParts[pathParts.length - 1]
            }

            // Remove any parameters
            functionName = functionName.replace(/\([^)]*\)$/, "").trim()
            this._outputChannel.appendLine(`Extracted function name for focus: ${functionName}`)
        } else if (this._currentFilePath) {
            // File-level granularity
            displayPath = this._currentFilePath;

            // Extract just the filename from the path
            const pathParts = this._currentFilePath.split(/[\/\\]/)
            functionName = pathParts[pathParts.length - 1]
            this._outputChannel.appendLine(`Using file name for focus: ${functionName}`)
        }

        this._view.webview.postMessage({
            type: "updateDiagram",
            mermaidCode,
            domainName: diagram.domainName,
            fullPath: diagram.fullPath,
            functionPath: displayPath,
            functionName: functionName, // Pass the extracted function name
            isHierarchical: false,
            hierarchyLevel: 0,
            totalLevels: 1
        })

        // Make the view visible
        this._view.show?.(true)
    }

    /**
     * Update the webview with hierarchical diagrams
     */
    private _updateWebviewWithHierarchicalDiagrams(): void {
        if (!this._view || this._hierarchicalDiagrams.length === 0) {
            return
        }

        // Extract just the function name without path or class
        let functionName = "";
        let displayPath = "";

        if (this._currentFunctionPath) {
            // Function-level granularity
            displayPath = this._currentFunctionPath;

            // First try to get the part after the last colon (for class methods)
            const colonParts = this._currentFunctionPath.split(":")
            if (colonParts.length > 1) {
                const lastPart = colonParts[colonParts.length - 1]

                // Check if it's a class method (contains a dot)
                if (lastPart.includes(".")) {
                    // Get the part after the last dot
                    const dotParts = lastPart.split(".")
                    functionName = dotParts[dotParts.length - 1]
                } else {
                    functionName = lastPart
                }
            } else {
                // If no colon, just use the last part of the path
                const pathParts = this._currentFunctionPath.split("/")
                functionName = pathParts[pathParts.length - 1]
            }

            // Remove any parameters
            functionName = functionName.replace(/\([^)]*\)$/, "").trim()
        } else if (this._currentFilePath) {
            // File-level granularity
            displayPath = this._currentFilePath;

            // Extract just the filename from the path
            const pathParts = this._currentFilePath.split(/[\/\\]/)
            functionName = pathParts[pathParts.length - 1]
        }

        // Start with the leaf diagram (most specific)
        const leafDiagram = this._hierarchicalDiagrams[0]
        const mermaidCode = this._extractMermaidCode(leafDiagram.diagram)

        // Send the hierarchical diagrams to the webview
        this._view.webview.postMessage({
            type: "updateHierarchicalDiagrams",
            diagrams: this._hierarchicalDiagrams.map((diagram, index) => ({
                mermaidCode: this._extractMermaidCode(diagram.diagram),
                domainName: diagram.domainName,
                fullPath: diagram.fullPath,
                level: index
            })),
            currentDiagram: {
                mermaidCode,
                domainName: leafDiagram.domainName,
                fullPath: leafDiagram.fullPath,
                functionPath: displayPath,
                functionName: functionName
            },
            totalLevels: this._hierarchicalDiagrams.length,
            // Make sure the full path is available for breadcrumb navigation
            hierarchyPath: leafDiagram.fullPath || ""
        })

        // Make the view visible
        this._view.show?.(true)
    }

    /**
     * Extract the mermaid code from a markdown code block
     */
    private _extractMermaidCode(markdown: string): string {
        // First try to match the standard ```mermaid pattern
        const mermaidPattern = /```mermaid\s*([\s\S]*?)\s*```/
        const match = markdown.match(mermaidPattern)

        if (match) {
            // Found standard ```mermaid pattern
            let code = match[1].trim()

            // Check if the extracted code starts with "mermaid" on its own line
            // This happens in some diagrams where the format is:
            // ```
            // mermaid
            // flowchart TD
            // ...
            // ```
            if (code.startsWith("mermaid")) {
                // Remove the "mermaid" line and any whitespace after it
                code = code.replace(/^mermaid\s*\n/, '')
                this._outputChannel.appendLine(`Removed 'mermaid' prefix from diagram code`)
            }

            return code
        }

        // If no match with the standard pattern, try to match the format:
        // ```
        // mermaid
        // flowchart TD
        // ...
        // ```
        const altPattern = /```\s*\n\s*mermaid\s*\n([\s\S]*?)\s*```/
        const altMatch = markdown.match(altPattern)

        if (altMatch) {
            this._outputChannel.appendLine(`Matched alternative mermaid format`)
            return altMatch[1].trim()
        }

        // If still no match, just return the original markdown
        // But first check if it starts with "mermaid" and remove it if needed
        if (markdown.trim().startsWith("mermaid")) {
            const lines = markdown.trim().split('\n')
            if (lines.length > 1 && lines[0].trim() === "mermaid") {
                this._outputChannel.appendLine(`Removed 'mermaid' prefix from raw diagram code`)
                return lines.slice(1).join('\n').trim()
            }
        }

        return markdown
    }

    /**
     * Show a welcome message in the webview
     */
    private _showWelcomeMessage(): void {
        if (!this._view) {
            return
        }

        this._view.webview.postMessage({
            type: "showMessage",
            message: "Navigate to a file to see its associated mermaid diagram"
        })
    }

    /**
     * Show a message when no mermaid diagram is found
     */
    private _showNoMermaidMessage(path: string): void {
        if (!this._view) {
            return
        }

        this._view.webview.postMessage({
            type: "showMessage",
            message: `No mermaid diagram found for ${path}`
        })
    }

    /**
     * Show an error message in the webview
     */
    private _showErrorMessage(error: string): void {
        if (!this._view) {
            return
        }

        this._view.webview.postMessage({
            type: "showError",
            message: `Error: ${error}`
        })
    }

    /**
     * Handle zoom in action
     */
    private _handleZoomIn(): void {
        if (!this._view) {
            return
        }

        this._view.webview.postMessage({
            type: "zoomIn"
        })
    }

    /**
     * Handle zoom out action
     */
    private _handleZoomOut(): void {
        if (!this._view) {
            return
        }

        this._view.webview.postMessage({
            type: "zoomOut"
        })
    }

    /**
     * Handle reset zoom action
     */
    private _handleResetZoom(): void {
        if (!this._view) {
            return
        }

        this._view.webview.postMessage({
            type: "resetZoom"
        })
    }

    /**
     * Handle export as PNG action
     */
    private _handleExportAsPng(dataUrl: string): void {
        if (!dataUrl) {
            return
        }

        // Create a temporary file and show it
        const workspacePath = getWorkspacePath()
        if (!workspacePath) {
            vscode.window.showErrorMessage("No workspace path found")
            return
        }

        const tempDir = path.join(workspacePath, ".bracket", "temp")
        fs.mkdirSync(tempDir, { recursive: true })

        const fileName = `mermaid-${Date.now()}.png`
        const filePath = path.join(tempDir, fileName)

        // Convert data URL to buffer
        const base64Data = dataUrl.replace(/^data:image\/png;base64,/, "")
        const buffer = Buffer.from(base64Data, "base64")

        fs.writeFileSync(filePath, buffer)

        // Open the file
        vscode.commands.executeCommand("vscode.open", vscode.Uri.file(filePath))
    }

    /**
     * Handle refresh diagram action
     */
    private _handleRefreshDiagram(functionPath: string): void {
        if (!functionPath) {
            return
        }

        this._outputChannel.appendLine(`Refreshing diagram for function: ${functionPath}`)
        this.updateDiagramForFunction(functionPath)
    }

    /**
     * Handle toggle companion action
     */
    private _handleToggleCompanion(): void {
        this._outputChannel.appendLine("Toggling Mermaid Companion visibility")
        vscode.commands.executeCommand("bracket-mermaid-companion.toggle")
    }
}
