import { LANGUAGES, isLanguage } from "../../../shared/language"

// Create browser-compatible stubs for Node.js functions
// These will be replaced with actual implementations in the extension context
// but allow the code to be bundled for the browser
const isExtensionContext = typeof window === 'undefined';

/**
 * Safely read a file and return its trimmed content
 * This is a stub for browser compatibility
 */
async function safeReadFile(filePath: string): Promise<string> {
	// In browser context, return empty string
	if (!isExtensionContext) {
		return "";
	}

	// This code will only run in the extension context
	try {
		// Use dynamic import to avoid bundling Node.js modules
		const fs = await import('fs');
		const util = await import('util');
		const readFile = util.promisify(fs.readFile);

		const content = await readFile(filePath, "utf-8");
		return content.trim();
	} catch (err: any) {
		const errorCode = err.code;
		if (!errorCode || !["ENOENT", "EISDIR"].includes(errorCode)) {
			throw err;
		}
		return "";
	}
}

/**
 * Check if a directory exists
 * This is a stub for browser compatibility
 */
async function directoryExists(dirPath: string): Promise<boolean> {
	// In browser context, return false
	if (!isExtensionContext) {
		return false;
	}

	// This code will only run in the extension context
	try {
		// Use dynamic import to avoid bundling Node.js modules
		const fs = await import('fs');
		const util = await import('util');
		const stat = util.promisify(fs.stat);

		const stats = await stat(dirPath);
		return stats.isDirectory();
	} catch (err) {
		return false;
	}
}

/**
 * Read all text files from a directory in alphabetical order
 * This is a stub for browser compatibility
 */
async function readTextFilesFromDirectory(dirPath: string): Promise<Array<{ filename: string; content: string }>> {
	// In browser context, return empty array
	if (!isExtensionContext) {
		return [];
	}

	// This code will only run in the extension context
	try {
		// Use dynamic import to avoid bundling Node.js modules
		const fs = await import('fs');
		const path = await import('path');
		const util = await import('util');

		const readdir = util.promisify(fs.readdir);
		const stat = util.promisify(fs.stat);
		const readlink = util.promisify(fs.readlink);

		const entries = await readdir(dirPath, { withFileTypes: true, recursive: true });

		// Process all entries - regular files and symlinks that might point to files
		const filePaths: string[] = [];

		for (const entry of entries) {
			const fullPath = path.resolve(entry.parentPath || dirPath, entry.name);
			if (entry.isFile()) {
				// Regular file
				filePaths.push(fullPath);
			} else if (entry.isSymbolicLink()) {
				try {
					// Get the symlink target
					const linkTarget = await readlink(fullPath);
					// Resolve the target path (relative to the symlink location)
					const resolvedTarget = path.resolve(path.dirname(fullPath), linkTarget);

					// Check if the target is a file
					const stats = await stat(resolvedTarget);
					if (stats.isFile()) {
						filePaths.push(resolvedTarget);
					}
				} catch (err) {
					// Skip invalid symlinks
				}
			}
		}

		const fileContents = await Promise.all(
			filePaths.map(async (file) => {
				try {
					// Check if it's a file (not a directory)
					const stats = await stat(file);
					if (stats.isFile()) {
						const content = await safeReadFile(file);
						return { filename: file, content };
					}
					return null;
				} catch (err) {
					return null;
				}
			}),
		);

		// Filter out null values (directories or failed reads)
		return fileContents.filter((item): item is { filename: string; content: string } => item !== null);
	} catch (err) {
		return [];
	}
}

/**
 * Format content from multiple files with filenames as headers
 */
function formatDirectoryContent(dirPath: string, files: Array<{ filename: string; content: string }>): string {
	if (files.length === 0) return "";

	return (
		"\n\n" +
		files
			.map((file) => {
				return `# Rules from ${file.filename}:\n${file.content}`;
			})
			.join("\n\n")
	);
}

/**
 * Load rule files from the specified directory
 * This is a stub for browser compatibility
 */
export async function loadRuleFiles(cwd: string): Promise<string> {
	// In browser context, return empty string
	if (!isExtensionContext) {
		return "";
	}

	// This code will only run in the extension context
	try {
		// Use dynamic import to avoid bundling Node.js modules
		const path = await import('path');

		// Check for .roo/rules/ directory
		const rooRulesDir = path.join(cwd, ".roo", "rules");
		if (await directoryExists(rooRulesDir)) {
			const files = await readTextFilesFromDirectory(rooRulesDir);
			if (files.length > 0) {
				return formatDirectoryContent(rooRulesDir, files);
			}
		}

		// Fall back to existing behavior
		const ruleFiles = [".roorules", ".clinerules"];

		for (const file of ruleFiles) {
			const content = await safeReadFile(path.join(cwd, file));
			if (content) {
				return `\n# Rules from ${file}:\n${content}\n`;
			}
		}
	} catch (err) {
		console.error("Error loading rule files:", err);
	}

	return "";
}

export async function addCustomInstructions(
	modeCustomInstructions: string,
	globalCustomInstructions: string,
	cwd: string,
	mode: string,
	options: { language?: string; rooIgnoreInstructions?: string } = {},
): Promise<string> {
	const sections = [];

	// Load mode-specific rules if mode is provided
	let modeRuleContent = "";
	let usedRuleFile = "";

	if (isExtensionContext && mode) {
		try {
			// Use dynamic import to avoid bundling Node.js modules
			const path = await import('path');

			// Check for .roo/rules-${mode}/ directory
			const modeRulesDir = path.join(cwd, ".roo", `rules-${mode}`);
			if (await directoryExists(modeRulesDir)) {
				const files = await readTextFilesFromDirectory(modeRulesDir);
				if (files.length > 0) {
					modeRuleContent = formatDirectoryContent(modeRulesDir, files);
					usedRuleFile = modeRulesDir;
				}
			}

			// If no directory exists, fall back to existing behavior
			if (!modeRuleContent) {
				const rooModeRuleFile = `.roorules-${mode}`;
				modeRuleContent = await safeReadFile(path.join(cwd, rooModeRuleFile));
				if (modeRuleContent) {
					usedRuleFile = rooModeRuleFile;
				} else {
					const clineModeRuleFile = `.clinerules-${mode}`;
					modeRuleContent = await safeReadFile(path.join(cwd, clineModeRuleFile));
					if (modeRuleContent) {
						usedRuleFile = clineModeRuleFile;
					}
				}
			}
		} catch (err) {
			console.error("Error loading mode-specific rules:", err);
		}
	}

	// Add language preference if provided
	if (options.language) {
		const languageName = isLanguage(options.language) ? LANGUAGES[options.language] : options.language;
		sections.push(
			`Language Preference:\nYou should always speak and think in the "${languageName}" (${options.language}) language unless the user gives you instructions below to do otherwise.`,
		);
	}

	// Add global instructions first
	if (typeof globalCustomInstructions === "string" && globalCustomInstructions.trim()) {
		sections.push(`Global Instructions:\n${globalCustomInstructions.trim()}`);
	}

	// Add mode-specific instructions after
	if (typeof modeCustomInstructions === "string" && modeCustomInstructions.trim()) {
		sections.push(`Mode-specific Instructions:\n${modeCustomInstructions.trim()}`);
	}

	// Add rules - include both mode-specific and generic rules if they exist
	const rules = [];

	// Add mode-specific rules first if they exist
	if (modeRuleContent && modeRuleContent.trim()) {
		if (isExtensionContext) {
			try {
				const path = await import('path');
				if (usedRuleFile.includes(path.join(".roo", `rules-${mode}`))) {
					rules.push(modeRuleContent.trim());
				} else {
					rules.push(`# Rules from ${usedRuleFile}:\n${modeRuleContent}`);
				}
			} catch (err) {
				console.error("Error processing mode rule content:", err);
				rules.push(modeRuleContent.trim());
			}
		} else {
			rules.push(modeRuleContent.trim());
		}
	}

	if (options.rooIgnoreInstructions) {
		rules.push(options.rooIgnoreInstructions);
	}

	// Add generic rules
	const genericRuleContent = await loadRuleFiles(cwd);
	if (genericRuleContent && genericRuleContent.trim()) {
		rules.push(genericRuleContent.trim());
	}

	if (rules.length > 0) {
		sections.push(`Rules:\n\n${rules.join("\n\n")}`);
	}

	const joinedSections = sections.join("\n\n");

	return joinedSections
		? `
====

USER'S CUSTOM INSTRUCTIONS

The following additional instructions are provided by the user, and should be followed to the best of your ability without interfering with the TOOL USE guidelines.

${joinedSections}`
		: "";
}
