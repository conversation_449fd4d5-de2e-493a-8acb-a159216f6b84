# chmod +x scripts/reload-extension.sh
#!/bin/bash

# Get the version from package.json
VERSION=$(node -p "require('./package.json').version")

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Building extension...${NC}"
npm run build:esbuild

echo -e "${YELLOW}Creating VSIX package...${NC}"
npm run vsix

echo -e "${YELLOW}Installing extension...${NC}"
code --install-extension bin/roo-cline-$VERSION.vsix

echo -e "${GREEN}Extension rebuilt and installed!${NC}"
echo -e "${YELLOW}Please reload VS Code window with:${NC}"
echo -e "${GREEN}Ctrl+Shift+P${NC} (or ${GREEN}Cmd+Shift+P${NC} on Mac) → 'Developer: Reload Window'"