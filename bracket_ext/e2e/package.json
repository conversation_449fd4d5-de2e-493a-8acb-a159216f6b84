{"name": "e2e", "version": "0.1.0", "private": true, "scripts": {"lint": "eslint src --ext ts", "check-types": "tsc --noEmit", "test": "npm run build && npx dotenvx run -f .env.local -- node ./out/runTest.js", "ci": "npm run vscode-test && npm run test", "build": "rimraf out && tsc -p tsconfig.json", "vscode-test": "cd .. && npm run vscode-test"}, "dependencies": {}, "devDependencies": {"@types/mocha": "^10.0.10", "@vscode/test-cli": "^0.0.9", "@vscode/test-electron": "^2.4.0", "mocha": "^11.1.0", "typescript": "^5.4.5"}}