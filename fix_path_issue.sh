#!/bin/bash
# Script to fix the path issue in the orchestrator service

# Stop the services
cd ../../
./stop.sh

# Create a patch for the orchestrator service
cat > orchestrator_path_fix.patch << EOF
--- src/services/orchestrator.py.orig
+++ src/services/orchestrator.py
@@ -300,7 +300,7 @@
         # Create domain analysis request
         domain_analysis_request = request.domain_analysis_config or DomainAnalysisRequest(
-            repomap_path=repomap_path,
+            repomap_path=f"artifacts/{job_id}/repomap.json",  # Use relative path instead of absolute
             **default_config
         )
 
EOF

# Apply the patch to the orchestrator service
cd orchestrator-service
patch -p0 < ../orchestrator_path_fix.patch

# Rebuild and restart the services
cd ..
./start.sh

# Run the orchestrator E2E test
cd tests/e2e
./run_orchestrator_e2e_test.sh
