#!/usr/bin/env ruby

# A simple Ruby class to test the knowledge graph generator
class Person
  attr_accessor :name, :age
  
  def initialize(name, age)
    @name = name
    @age = age
  end
  
  def greet
    puts "Hello, my name is #{@name} and I am #{@age} years old."
  end
  
  def self.create_from_hash(hash)
    new(hash[:name], hash[:age])
  end
end

# A module with some utility functions
module Utils
  def self.format_person(person)
    "#{person.name} (#{person.age})"
  end
  
  def self.create_people(count)
    people = []
    count.times do |i|
      people << Person.new("Person #{i}", 20 + i)
    end
    people
  end
end

# Main program
def main
  person = Person.new("<PERSON>", 30)
  person.greet
  
  people = Utils.create_people(3)
  people.each do |p|
    puts Utils.format_person(p)
  end
end

# Call the main function if this file is executed directly
if __FILE__ == $0
  main
end
