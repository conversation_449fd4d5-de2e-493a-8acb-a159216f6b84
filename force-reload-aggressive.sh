#!/bin/bash
set -e

echo "🧹 Aggressively cleaning VS Code extension cache..."
# On macOS, clean all VS Code caches
if [[ "$OSTYPE" == "darwin"* ]]; then
  echo "Cleaning macOS VS Code caches..."
  rm -rf ~/Library/Application\ Support/Code/CachedExtensionVSIXs/*
  rm -rf ~/Library/Application\ Support/Code/Cache/*
  rm -rf ~/Library/Application\ Support/Code/CachedData/*
  rm -rf ~/Library/Application\ Support/Code/User/workspaceStorage/*
  rm -rf ~/Library/Caches/com.microsoft.VSCode*
  rm -rf ~/Library/Saved\ Application\ State/com.microsoft.VSCode*
fi

# On Linux, clean all VS Code caches
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
  echo "Cleaning Linux VS Code caches..."
  rm -rf ~/.config/Code/CachedExtensionVSIXs/*
  rm -rf ~/.config/Code/Cache/*
  rm -rf ~/.config/Code/CachedData/*
  rm -rf ~/.config/Code/User/workspaceStorage/*
  rm -rf ~/.cache/code-*
fi

# On Windows, clean all VS Code caches
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" ]]; then
  echo "Cleaning Windows VS Code caches..."
  rm -rf "$USERPROFILE/.vscode/extensions/CachedExtensionVSIXs/*"
  rm -rf "$USERPROFILE/AppData/Roaming/Code/Cache/*"
  rm -rf "$USERPROFILE/AppData/Roaming/Code/CachedData/*"
  rm -rf "$USERPROFILE/AppData/Roaming/Code/User/workspaceStorage/*"
  rm -rf "$USERPROFILE/AppData/Local/Microsoft/VSCode/*"
fi

echo "🧹 Cleaning extension build artifacts..."
rm -rf bracket_ext/dist
rm -rf bracket_ext/out
rm -rf bracket_ext/webview-ui/build
rm -rf bracket_ext/webview-ui/node_modules/.vite
rm -rf bracket_ext/webview-ui/node_modules/.cache
rm -rf bracket_ext/node_modules/.cache
rm -rf bracket_ext/webview-ui/dist

echo "🧹 Cleaning node_modules (optional, uncomment if needed)..."
# Uncomment these lines if you want to completely reinstall node modules
# rm -rf bracket_ext/node_modules
# rm -rf bracket_ext/webview-ui/node_modules

echo "📦 Reinstalling dependencies..."
cd bracket_ext
npm ci
cd webview-ui
npm ci
cd ../..

echo "📦 Rebuilding extension from scratch..."
cd bracket_ext/webview-ui
npm run clean
NODE_ENV=development npm run build
cd ../..

cd bracket_ext
npm run build
cd ..

echo "🚀 Launching VS Code with extension..."
# Kill any running VS Code instances
pkill -f "Visual Studio Code" || true
pkill -f "Code" || true
sleep 2

# Launch VS Code with a completely clean environment
code --extensionDevelopmentPath="$(pwd)/bracket_ext" --disable-extensions --new-window --disable-gpu-sandbox --disable-workspace-trust
