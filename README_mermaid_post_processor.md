# Mermaid Diagram Post-Processor

This set of tools allows you to post-process Mermaid diagrams to fix common syntax issues without using LLM beautification. The post-processing applies the following fixes:

- Removes parentheses from node text (which can cause rendering errors)
- Removes problematic syntax like "Direction TD", "notes", and "type label"
- Applies other syntax fixes to ensure proper rendering

## Available Tools

1. **post_process_mermaid.py** - Process a single directory of Mermaid diagrams
2. **batch_post_process_mermaid.py** - Process multiple subdirectories of Mermaid diagrams
3. **mermaid_post_processor.py** - Python module that can be imported and used in your code

## Usage

### Single Directory Processing

```bash
python post_process_mermaid.py --input_dir /path/to/mermaid/diagrams --output_dir /path/to/output
```

Options:
- `--input_dir` (required): Directory containing Mermaid diagram files
- `--output_dir` (optional): Directory to save processed diagrams (if not specified, overwrites original files)
- `--dry_run` (optional): Don't actually save changes, just log what would be done

### Batch Processing

```bash
python batch_post_process_mermaid.py --input_dir /path/to/parent/dir --output_dir /path/to/output
```

Options:
- `--input_dir` (required): Parent directory containing subdirectories with Mermaid diagrams
- `--output_dir` (required): Directory to save processed diagrams
- `--dry_run` (optional): Don't actually save changes, just log what would be done

### Python Module

You can import and use the functions directly in your Python code:

```python
from mermaid_post_processor import post_process_mermaid_directory_sync, post_process_mermaid_text

# Process a directory
stats = post_process_mermaid_directory_sync(
    input_dir="./mermaid_diagrams",
    output_dir="./processed_diagrams"
)
print(f"Processed {stats['processed_files']} files")

# Process a single diagram
diagram = """
flowchart TD
    A[Start (Process)] --> B(Do Something)
    B --> C{Decision}
    C -->|Yes| D[End (Process)]
    C -->|No| B
"""

processed = post_process_mermaid_text(diagram)
print(processed)
```

## Examples

### Example 1: Process a single directory

```bash
python post_process_mermaid.py --input_dir ./diagrams --output_dir ./processed_diagrams
```

### Example 2: Process multiple subdirectories

```bash
python batch_post_process_mermaid.py --input_dir ./project_diagrams --output_dir ./processed_project_diagrams
```

### Example 3: Dry run to see what would be changed

```bash
python post_process_mermaid.py --input_dir ./diagrams --output_dir ./processed_diagrams --dry_run
```

## How It Works

The post-processor:

1. Searches for files with `.md` or `.mermaid` extensions in the input directory
2. Extracts Mermaid diagrams from these files (looking for ```mermaid blocks)
3. Applies syntax fixes to each diagram
4. Saves the processed diagrams to the output directory (or overwrites the original files)

## Requirements

- Python 3.7+
- The `mermaid_beautifier` package (included in the repository)

## Notes

- The post-processor only applies syntax fixes and does not use LLM beautification
- It preserves all nodes and connections from the original diagram
- It only modifies the syntax to ensure proper rendering
