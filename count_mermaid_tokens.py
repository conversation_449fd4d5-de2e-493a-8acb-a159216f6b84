#!/usr/bin/env python
"""
Count Mermaid Tokens for Each Trace

This script counts the mermaid diagram tokens for each trace in a domain taxonomy
and stores the results in a CSV file.
"""

import os
import json
import pandas as pd
import time
from typing import Dict, List, Any

# Import token counting utility
try:
    from bracket_core.llm.tokens import num_tokens_from_string
except ImportError:
    # Fallback to tiktoken if bracket_core.llm.tokens is not available
    import tiktoken
    
    def num_tokens_from_string(string: str, model_name: str = "gpt-4o-mini") -> int:
        """Returns the number of tokens in a text string."""
        encoding = tiktoken.encoding_for_model(model_name)
        return len(encoding.encode(string))

def count_mermaid_tokens(taxonomy_json_path, output_csv_path=None, model_name="gpt-4o-mini"):
    """
    Count mermaid diagram tokens for each trace in a domain taxonomy.
    
    Args:
        taxonomy_json_path: Path to the domain taxonomy JSON file
        output_csv_path: Path to save the CSV output (optional)
        model_name: Model name to use for token counting
    
    Returns:
        Path to the generated CSV file
    """
    # Set default output path if not provided
    if not output_csv_path:
        output_csv_path = os.path.join(
            os.path.dirname(taxonomy_json_path),
            f"mermaid_tokens_{time.strftime('%Y%m%d-%H%M%S')}.csv"
        )
    
    print(f"Reading domain taxonomy from: {taxonomy_json_path}")
    
    # Read the taxonomy JSON
    with open(taxonomy_json_path, 'r') as f:
        taxonomy_data = json.load(f)
    
    # List to store results
    results = []
    
    # Function to recursively process nodes
    def process_node(node, path=None, level=0):
        if path is None:
            path = []
        
        name = node.get('name', 'Unknown')
        current_path = path + [name]
        full_path = " -> ".join(current_path)
        
        # Get diagram and functions
        diagram = node.get('diagram', '')
        functions = node.get('functions', [])
        
        # Calculate tokens for diagram
        diagram_tokens = 0
        if diagram:
            diagram_tokens = num_tokens_from_string(diagram)
        
        # Check if this is a leaf node
        is_leaf = 'children' not in node or not node['children']
        
        # Add to results
        results.append({
            'trace': full_path,
            'level': level,
            'is_leaf': is_leaf,
            'function_count': len(functions),
            'mermaid_tokens': diagram_tokens
        })
        
        # Process children recursively
        if not is_leaf and 'children' in node:
            for child in node['children']:
                process_node(child, current_path, level + 1)
    
    # Start processing from the root
    process_node(taxonomy_data)
    
    print(f"Analyzed {len(results)} traces")
    
    # Convert to DataFrame and save to CSV
    df = pd.DataFrame(results)
    df.to_csv(output_csv_path, index=False)
    
    print(f"Results saved to: {output_csv_path}")
    
    # Print a simple summary
    print("\nSummary:")
    print(f"Total traces: {len(df)}")
    print(f"Leaf nodes: {df['is_leaf'].sum()}")
    print(f"Total mermaid tokens: {df['mermaid_tokens'].sum():,}")
    print(f"Average mermaid tokens per trace: {df['mermaid_tokens'].mean():.2f}")
    
    return output_csv_path

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Count mermaid diagram tokens for each trace")
    # parser.add_argument("--taxonomy-json", required=True, help="Path to the domain taxonomy JSON file")
    # parser.add_argument("--output", help="Path to save the CSV output")
    parser.add_argument("--model", default="gpt-4o-mini", help="Model name to use for token counting")
    
    args = parser.parse_args()

    taxonomy_json = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/domain_taxonomy.json"
    output_dir = os.path.join(os.path.dirname(taxonomy_json), "token_analysis")
    output_csv_path = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/token_analysis/"
    
    count_mermaid_tokens(
        taxonomy_json_path=taxonomy_json,
        output_csv_path=output_dir,
        model_name=args.model
    )
