#!/usr/bin/env python
"""
Run Domain Taxonomy Token Analysis

This script runs comprehensive token analysis on a domain taxonomy JSON file:
1. Leaf component analysis - analyzes token usage in leaf components only
2. Trace analysis - analyzes token usage in all traces (paths from root to any node)

It provides detailed breakdowns of token usage for functions and diagrams.
"""

import os
import argparse
import time
from bracket_core.domain_taxonomy_token_analyzer import DomainTaxonomyTokenAnalyzer
from bracket_core.domain_trace_token_analyzer import DomainTraceTokenAnalyzer

def run_leaf_analysis(taxonomy_json, output_dir, model_name):
    """Run analysis on leaf components only."""
    print("\n=== LEAF COMPONENT ANALYSIS ===")
    print(f"Analyzing leaf components in the taxonomy...")

    # Create analyzer
    analyzer = DomainTaxonomyTokenAnalyzer(
        taxonomy_json_path=taxonomy_json,
        output_dir=output_dir,
        model_name=model_name
    )

    # Run analysis
    start_time = time.time()
    result, csv_path, summary_path = analyzer.run_analysis()
    end_time = time.time()

    print(f"\nLeaf analysis complete in {end_time - start_time:.2f} seconds!")
    print(f"Summary saved to: {summary_path}")
    print(f"Detailed results saved to: {csv_path}")

    # Print key statistics
    print(f"\nLeaf Component Statistics:")
    print(f"  Total leaf components: {result.total_leaf_components}")
    print(f"  Total tokens: {result.total_tokens:,}")
    print(f"  Average tokens per component: {result.average_tokens_per_component:.2f}")
    print(f"  Maximum tokens: {result.max_tokens:,} (Component: {result.max_tokens_component})")
    print(f"  Components with diagrams: {result.components_with_diagrams}")
    print(f"  Components without diagrams: {result.components_without_diagrams}")

    return result

def run_trace_analysis(taxonomy_json, output_dir, model_name):
    """Run analysis on all traces in the taxonomy."""
    print("\n=== TRACE ANALYSIS ===")
    print(f"Analyzing all traces in the taxonomy...")

    # Create analyzer
    analyzer = DomainTraceTokenAnalyzer(
        taxonomy_json_path=taxonomy_json,
        output_dir=output_dir,
        model_name=model_name
    )

    # Run analysis
    start_time = time.time()
    result, csv_path, summary_path = analyzer.run_analysis()
    end_time = time.time()

    print(f"\nTrace analysis complete in {end_time - start_time:.2f} seconds!")
    print(f"Summary saved to: {summary_path}")
    print(f"Detailed results saved to: {csv_path}")

    # Print key statistics
    print(f"\nTrace Statistics:")
    print(f"  Total traces: {result.total_traces}")
    print(f"  Leaf traces: {result.leaf_traces}")
    print(f"  Intermediate traces: {result.intermediate_traces}")
    print(f"  Total tokens: {result.total_tokens:,}")
    print(f"  Total diagram tokens: {result.total_diagram_tokens:,} ({result.total_diagram_tokens/result.total_tokens*100:.1f}%)")
    print(f"  Total function tokens: {result.total_function_tokens:,} ({result.total_function_tokens/result.total_tokens*100:.1f}%)")
    print(f"  Average tokens per trace: {result.average_tokens_per_trace:.2f}")
    print(f"  Maximum tokens: {result.max_tokens:,} (Trace: {result.max_tokens_trace})")

    return result

def main():
    parser = argparse.ArgumentParser(description="Run comprehensive domain taxonomy token analysis")
    parser.add_argument("--taxonomy-json", help="Path to the domain taxonomy JSON file")
    parser.add_argument("--output-dir", help="Directory to save analysis results")
    parser.add_argument("--model", default="gpt-4o-mini", help="Model name to use for token counting")
    parser.add_argument("--leaf-only", action="store_true", help="Run only leaf component analysis")
    parser.add_argument("--trace-only", action="store_true", help="Run only trace analysis")

    args = parser.parse_args()

    # Default paths if not provided
    taxonomy_json = args.taxonomy_json or "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/gitlab/domain_taxonomy.json"
    output_dir = args.output_dir or os.path.join(os.path.dirname(taxonomy_json), "token_analysis")

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    print(f"Running domain taxonomy token analysis with:")
    print(f"  Taxonomy JSON: {taxonomy_json}")
    print(f"  Output directory: {output_dir}")
    print(f"  Model: {args.model}")

    # Run the appropriate analysis based on flags
    if args.leaf_only and not args.trace_only:
        run_leaf_analysis(taxonomy_json, output_dir, args.model)
    elif args.trace_only and not args.leaf_only:
        run_trace_analysis(taxonomy_json, output_dir, args.model)
    else:
        # Run both analyses by default
        leaf_result = run_leaf_analysis(taxonomy_json, output_dir, args.model)
        trace_result = run_trace_analysis(taxonomy_json, output_dir, args.model)

        # Print combined summary
        print("\n=== COMBINED ANALYSIS SUMMARY ===")
        print(f"Total leaf components: {leaf_result.total_leaf_components}")
        print(f"Total traces (including intermediate nodes): {trace_result.total_traces}")
        print(f"Total tokens in leaf components: {leaf_result.total_tokens:,}")
        print(f"Total tokens in all traces: {trace_result.total_tokens:,}")
        print(f"Diagram tokens: {trace_result.total_diagram_tokens:,} ({trace_result.total_diagram_tokens/trace_result.total_tokens*100:.1f}%)")
        print(f"Function tokens: {trace_result.total_function_tokens:,} ({trace_result.total_function_tokens/trace_result.total_tokens*100:.1f}%)")

    return 0

if __name__ == "__main__":
    exit(main())
