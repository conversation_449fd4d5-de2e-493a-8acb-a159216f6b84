#!/usr/bin/env python3
"""
Complete PR Broadcast Test Script

This script tests the complete PR Broadcast workflow with dummy PR data.
"""

import os
import sys
import json
import logging
import asyncio
import argparse
from typing import Dict, List, Any, Tuple, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def main():
    """Main entry point for the complete PR Broadcast test script."""
    parser = argparse.ArgumentParser(description="Test Complete PR Broadcast with dummy PR data")
    
    # Required arguments
    # parser.add_argument("--taxonomy-json", required=True, help="Path to domain taxonomy JSON file")
    # parser.add_argument("--domain-traces-yaml", required=True, help="Path to domain traces YAML file")
    # parser.add_argument("--dummy-data", required=True, help="Path to dummy PR data JSON file")
    # parser.add_argument("--output-dir", required=True, help="Directory to save generated diagrams and reports")
    
    # Optional arguments
    parser.add_argument("--llm-api-key", help="API key for the LLM service")
    parser.add_argument("--open-browser", action="store_true", help="Open HTML report in browser")
    
    args = parser.parse_args()
    

    args.taxonomy_json = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/beauty_of_life/data/bracket/domain_taxonomy.json"
    args.domain_traces_yaml = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/experiments/beauty_of_life/data/bracket/domain_traces.yaml"
    args.output_dir = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/pr_broadcast_logger_output/bracket/"
    args.dummy_data = "/Users/<USER>/work/startup/godzilla/bracket/bracket_prod/sample_logger_diffs.json"

    try:
        # Step 1: Load the dummy data
        with open(args.dummy_data, 'r') as f:
            dummy_data = json.load(f)
        
        mr_details = dummy_data["mr_details"]
        diffs = dummy_data["diffs"]
        
        logger.info(f"Loaded dummy PR data with {len(diffs)} diffs")
        
        # Step 2: Analyze the diffs to identify function changes
        from pr_broadcast.enhanced_function_diff_analyzer import EnhancedFunctionDiffAnalyzer
        
        analyzer = EnhancedFunctionDiffAnalyzer()
        function_changes = []
        
        for diff in diffs:
            file_changes = analyzer.analyze_diff(diff)
            function_changes.extend(file_changes)
        
        logger.info(f"Identified {len(function_changes)} function changes")
        
        # Step 3: Map function changes to domains
        from pr_broadcast.domain_mapper import DomainMapper
        
        mapper = DomainMapper(
            taxonomy_json_path=args.taxonomy_json,
            domain_traces_yaml_path=args.domain_traces_yaml
        )
        
        domain_changes = mapper.map_functions_to_domains(function_changes)
        
        logger.info(f"Mapped changes to {len(domain_changes)} domains")
        
        # Step 4: Generate impact diagrams for leaf domains
        from pr_broadcast.llm_diagram_generator import LLMDiagramGenerator
        
        diagram_generator = LLMDiagramGenerator(
            taxonomy_json_path=args.taxonomy_json,
            llm_api_key=args.llm_api_key
        )
        
        # Load the taxonomy to get domain diagrams
        with open(args.taxonomy_json, 'r') as f:
            taxonomy = json.load(f)
        
        # Function to find a domain node in the taxonomy
        def find_domain_node(domain_path):
            if not domain_path:
                return taxonomy
                
            parts = domain_path.split(" -> ")
            node = taxonomy
            
            for part in parts:
                if "children" not in node:
                    return None
                    
                found = False
                for child in node["children"]:
                    if child["name"] == part:
                        node = child
                        found = True
                        break
                        
                if not found:
                    return None
                    
            return node
        
        # Function to check if a domain is a leaf domain
        def is_leaf_domain(domain_path):
            node = find_domain_node(domain_path)
            return node and ("children" not in node or not node["children"])
        
        # Generate diagrams for leaf domains
        leaf_diagrams = {}
        
        for domain, changes in domain_changes.items():
            if is_leaf_domain(domain):
                logger.info(f"Generating diagram for leaf domain: {domain}")
                
                # Get the domain node
                domain_node = find_domain_node(domain)
                
                if domain_node and "diagram" in domain_node:
                    original_diagram = domain_node["diagram"]
                    
                    # Generate a new diagram showing the impact of changes
                    impact_diagram = await diagram_generator.generate_leaf_domain_diagram(
                        domain, 
                        original_diagram, 
                        changes["functions"]
                    )
                    
                    leaf_diagrams[domain] = {
                        "original_diagram": original_diagram,
                        "impact_diagram": impact_diagram,
                        "changes": changes
                    }
                    
                    logger.info(f"Generated impact diagram for leaf domain: {domain}")
        
        # Step 5: Propagate changes upward through the domain hierarchy
        parent_diagrams = {}
        
        # Function to get the parent domain
        def get_parent_domain(domain_path):
            if " -> " not in domain_path:
                return None
                
            parts = domain_path.split(" -> ")
            return " -> ".join(parts[:-1])
        
        # Function to check if a domain is a child of another domain
        def is_child_of(child, parent):
            return child.startswith(parent + " -> ")
        
        # Get all domains with changes
        affected_domains = set(domain_changes.keys())
        
        # Process each domain with changes
        for domain in affected_domains:
            # Get the parent domain
            parent = get_parent_domain(domain)
            
            # Skip if no parent or parent is root
            if not parent or parent == "Root":
                continue
                
            # Skip if already processed
            if parent in parent_diagrams:
                continue
                
            # Get the parent's diagram
            parent_node = find_domain_node(parent)
            
            if parent_node and "diagram" in parent_node:
                parent_diagram = parent_node["diagram"]
                
                # Get all child domains with changes
                child_domains = [d for d in affected_domains if is_child_of(d, parent)]
                
                # Collect all child diagrams
                child_diagrams = {}
                for child in child_domains:
                    if child in leaf_diagrams:
                        child_diagrams[child] = leaf_diagrams[child]
                
                if child_diagrams:
                    logger.info(f"Generating diagram for parent domain: {parent}")
                    
                    # Generate a new diagram showing the impact of changes
                    impact_diagram = await diagram_generator.generate_parent_domain_diagram(
                        parent,
                        parent_diagram,
                        child_diagrams
                    )
                    
                    parent_diagrams[parent] = {
                        "original_diagram": parent_diagram,
                        "impact_diagram": impact_diagram,
                        "child_domains": child_domains
                    }
                    
                    logger.info(f"Generated impact diagram for parent domain: {parent}")
        
        # Step 6: Save artifacts and generate summary
        from pr_broadcast.output_manager import OutputManager
        
        output_manager = OutputManager(args.output_dir)
        
        # Combine leaf and parent diagrams
        all_diagrams = {**leaf_diagrams, **parent_diagrams}
        
        # Save diagrams
        artifacts = output_manager.save_diagrams(all_diagrams, mr_details["iid"])
        
        # Generate summary
        summary = {
            "title": mr_details["title"],
            "author": mr_details["author"]["name"],
            "total_changes": len(function_changes),
            "change_counts": {
                "added": len([fc for fc in function_changes if fc.change_type == "ADDED"]),
                "modified": len([fc for fc in function_changes if fc.change_type == "MODIFIED"]),
                "deleted": len([fc for fc in function_changes if fc.change_type == "DELETED"])
            },
            "total_impacted_domains": len(domain_changes),
            "domain_levels": {},
            "most_impacted_domains": []
        }
        
        # Count domains by level
        for domain in domain_changes.keys():
            # Count the number of "->" to determine the level
            level = domain.count("->") if "->" in domain else 0
            
            if level not in summary["domain_levels"]:
                summary["domain_levels"][level] = 0
            
            summary["domain_levels"][level] += 1
        
        # Get most impacted domains
        sorted_domains = sorted(
            domain_changes.items(),
            key=lambda x: len(x[1]["functions"]),
            reverse=True
        )
        
        # Take top 5 domains
        for domain, changes in sorted_domains[:5]:
            summary["most_impacted_domains"].append({
                "name": domain,
                "changes": changes
            })
        
        # Save summary
        summary_path = output_manager.save_summary(summary, mr_details["iid"])
        
        logger.info(f"Saved summary to {summary_path}")
        
        # Print summary
        print("\n" + "=" * 80)
        print(f"COMPLETE PR BROADCAST SUMMARY FOR MR !{mr_details['iid']}")
        print("=" * 80)
        
        print(f"\nTitle: {summary['title']}")
        print(f"Author: {summary['author']}")
        
        print("\nCHANGE SUMMARY:")
        print(f"- Total Changed Functions: {summary['total_changes']}")
        print(f"  - Added: {summary['change_counts']['added']}")
        print(f"  - Modified: {summary['change_counts']['modified']}")
        print(f"  - Deleted: {summary['change_counts']['deleted']}")
        
        print("\nDOMAIN IMPACT SUMMARY:")
        print(f"- Total Impacted Domains: {summary['total_impacted_domains']}")
        
        if summary.get("domain_levels"):
            print("- Domain Levels:")
            for level, count in summary["domain_levels"].items():
                print(f"  - Level {level}: {count} domains")
        
        print("\nMOST IMPACTED DOMAINS:")
        for domain in summary["most_impacted_domains"]:
            name = domain["name"]
            changes = domain["changes"]
            print(f"- {name}")
            print(f"  - Changed Functions: {len(changes['functions'])}")
            print("  - Functions:")
            for fc in changes["functions"][:3]:  # Show only first 3 functions
                print(f"    - {fc['function_name']} ({fc['change_type']})")
            if len(changes["functions"]) > 3:
                print(f"    - ... and {len(changes['functions']) - 3} more")
        
        print("\nGENERATED ARTIFACTS:")
        if artifacts:
            if "leaf_domains" in artifacts:
                print(f"- Leaf Domain Diagrams: {len(artifacts['leaf_domains'])}")
                for domain, path in artifacts["leaf_domains"].items():
                    print(f"  - {domain}: {path}")
            if "parent_domains" in artifacts:
                print(f"- Parent Domain Diagrams: {len(artifacts['parent_domains'])}")
                for domain, path in artifacts["parent_domains"].items():
                    print(f"  - {domain}: {path}")
        
        print(f"\nSummary Report: {summary_path}")
        
        # Open HTML report in browser if requested
        if args.open_browser:
            html_dir = os.path.join(args.output_dir, "html")
            html_file = f"pr_impact_report_{mr_details['iid']}.html"
            html_path = os.path.join(html_dir, html_file)
            
            if os.path.exists(html_path):
                import webbrowser
                webbrowser.open(f"file://{os.path.abspath(html_path)}")
                print(f"Opened HTML report in browser: {html_path}")
            else:
                print(f"HTML report not found: {html_path}")
        
        print("\n" + "=" * 80)
        print("Complete PR Broadcast analysis complete!")
        print("=" * 80 + "\n")
        
        return 0
    
    except Exception as e:
        logger.error(f"Error in Complete PR Broadcast test: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
