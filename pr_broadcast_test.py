#!/usr/bin/env python3
"""
PR Broadcast Test Script

This script generates dummy PR data for the current codebase and processes it through
the PR Broadcast service to visualize the impact of changes.
"""

import os
import sys
import json
import logging
import asyncio
import argparse
from typing import Dict, List, Any, Tuple, Optional

# Import PR Broadcast components
from pr_broadcast.pr_broadcast_service import PRBroadcastService
from pr_broadcast.diff_analysis.gitlab_diff_fetcher import GitlabDiffFetcher
from pr_broadcast.diff_analysis.function_diff_analyzer import FunctionChange, ChangeType

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MockGitlabDiffFetcher(GitlabDiffFetcher):
    """
    Mock implementation of GitlabDiffFetcher that returns dummy PR data.
    """
    
    def __init__(self, gitlab_url: str, gitlab_token: str, dummy_data_path: Optional[str] = None):
        """
        Initialize the mock GitLab diff fetcher.
        
        Args:
            gitlab_url: URL of the GitLab instance (not used)
            gitlab_token: GitLab API token (not used)
            dummy_data_path: Path to dummy PR data JSON file (optional)
        """
        super().__init__(gitlab_url, gitlab_token)
        self.dummy_data_path = dummy_data_path
        self.dummy_data = None
        
    async def fetch_merge_request(
        self,
        project_id: str,
        mr_iid: int
    ) -> Tuple[Dict[str, Any], List[Dict[str, Any]]]:
        """
        Return dummy merge request data instead of fetching from GitLab.
        
        Args:
            project_id: GitLab project ID (not used)
            mr_iid: Merge request IID (not used)
            
        Returns:
            Tuple containing dummy merge request details and diffs
        """
        # Load dummy data if provided
        if self.dummy_data_path and os.path.exists(self.dummy_data_path):
            with open(self.dummy_data_path, 'r') as f:
                self.dummy_data = json.load(f)
            
            logger.info(f"Loaded dummy PR data from {self.dummy_data_path}")
            return self.dummy_data["mr_details"], self.dummy_data["diffs"]
        
        # Generate dummy data if not provided
        mr_details, diffs = self._generate_dummy_data()
        
        # Save generated data for future use
        if self.dummy_data_path:
            os.makedirs(os.path.dirname(self.dummy_data_path), exist_ok=True)
            with open(self.dummy_data_path, 'w') as f:
                json.dump({"mr_details": mr_details, "diffs": diffs}, f, indent=2)
            
            logger.info(f"Saved generated dummy PR data to {self.dummy_data_path}")
        
        return mr_details, diffs
    
    def _generate_dummy_data(self) -> Tuple[Dict[str, Any], List[Dict[str, Any]]]:
        """
        Generate dummy PR data for the current codebase.
        
        Returns:
            Tuple containing dummy merge request details and diffs
        """
        logger.info("Generating dummy PR data for the current codebase")
        
        # Generate dummy merge request details
        mr_details = {
            "id": 12345,
            "iid": 678,
            "project_id": 42,
            "title": "Implement PR Broadcast Feature",
            "description": "This PR adds the PR Broadcast feature to visualize the impact of code changes on domain architecture.",
            "state": "opened",
            "created_at": "2023-06-15T10:30:45.000Z",
            "updated_at": "2023-06-16T14:20:30.000Z",
            "target_branch": "main",
            "source_branch": "feature/pr-broadcast",
            "author": {
                "id": 101,
                "name": "Test User",
                "username": "testuser",
                "avatar_url": "https://secure.gravatar.com/avatar/abcdef1234567890"
            }
        }
        
        # Generate dummy diffs for selected files
        diffs = self._generate_dummy_diffs()
        
        return mr_details, diffs
    
    def _generate_dummy_diffs(self) -> List[Dict[str, Any]]:
        """
        Generate dummy diffs for selected files in the current codebase.
        
        Returns:
            List of dictionaries containing diff information
        """
        # Files to include in the dummy PR
        files_to_modify = [
            "pr_broadcast/pr_broadcast_service.py",
            "pr_broadcast/cli.py",
            "pr_broadcast/diff_analysis/function_diff_analyzer.py",
            "pr_broadcast/domain_impact/impact_analyzer.py"
        ]
        
        diffs = []
        
        # Generate diffs for modified files
        for file_path in files_to_modify:
            if os.path.exists(file_path):
                diff = self._generate_file_diff(file_path)
                diffs.append(diff)
        
        return diffs
    
    def _generate_file_diff(self, file_path: str) -> Dict[str, Any]:
        """
        Generate a diff for a modified file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Dictionary containing diff information
        """
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            # Generate a simple diff by modifying some lines and adding new ones
            lines = content.split('\n')
            diff_lines = []
            
            # Add diff header
            diff_lines.append(f"--- a/{file_path}")
            diff_lines.append(f"+++ b/{file_path}")
            
            # Find a function to modify
            function_start = None
            for i, line in enumerate(lines):
                if line.strip().startswith("def ") or line.strip().startswith("class "):
                    function_start = i
                    break
            
            if function_start is not None:
                # Add a diff chunk for this function
                diff_lines.append(f"@@ -{function_start+1},{min(10, len(lines)-function_start)} +{function_start+1},{min(12, len(lines)-function_start)+2} @@")
                
                # Add some context lines
                for i in range(function_start, min(function_start + 3, len(lines))):
                    diff_lines.append(" " + lines[i])
                
                # Modify a line
                if function_start + 3 < len(lines):
                    diff_lines.append("-" + lines[function_start + 3])
                    diff_lines.append("+" + lines[function_start + 3] + "  # Modified for better performance")
                
                # Add some new lines
                diff_lines.append("+")
                diff_lines.append("+        # Added comment for clarity")
                diff_lines.append("+        logger.debug(f\"Processing {file_path}\")")
                
                # Add more context lines
                for i in range(function_start + 4, min(function_start + 10, len(lines))):
                    diff_lines.append(" " + lines[i])
            
            return {
                "old_path": file_path,
                "new_path": file_path,
                "new_file": False,
                "deleted_file": False,
                "diff": "\n".join(diff_lines)
            }
        
        except Exception as e:
            logger.error(f"Error generating diff for {file_path}: {e}")
            return {
                "old_path": file_path,
                "new_path": file_path,
                "new_file": False,
                "deleted_file": False,
                "diff": ""
            }

class MockPRBroadcastService(PRBroadcastService):
    """
    Mock implementation of PRBroadcastService that uses the MockGitlabDiffFetcher.
    """
    
    def __init__(
        self,
        gitlab_url: str,
        gitlab_token: str,
        taxonomy_json_path: str,
        domain_traces_yaml_path: str,
        output_dir: str,
        dummy_data_path: Optional[str] = None
    ):
        """
        Initialize the mock PR Broadcast Service.
        
        Args:
            gitlab_url: URL of the GitLab instance
            gitlab_token: GitLab API token
            taxonomy_json_path: Path to the domain taxonomy JSON file
            domain_traces_yaml_path: Path to the domain traces YAML file
            output_dir: Directory to save generated diagrams and reports
            dummy_data_path: Path to dummy PR data JSON file (optional)
        """
        super().__init__(
            gitlab_url=gitlab_url,
            gitlab_token=gitlab_token,
            taxonomy_json_path=taxonomy_json_path,
            domain_traces_yaml_path=domain_traces_yaml_path,
            output_dir=output_dir
        )
        
        # Replace the diff fetcher with our mock implementation
        self.diff_fetcher = MockGitlabDiffFetcher(
            gitlab_url=gitlab_url,
            gitlab_token=gitlab_token,
            dummy_data_path=dummy_data_path
        )
    
    async def analyze_merge_request(
        self,
        project_id: str,
        mr_iid: int,
        include_indirect_impact: bool = True,
        generate_html: bool = True
    ) -> Dict[str, Any]:
        """
        Analyze a mock merge request and generate impact diagrams.
        
        Args:
            project_id: GitLab project ID
            mr_iid: Merge request IID (internal ID)
            include_indirect_impact: Whether to include indirect impact analysis
            generate_html: Whether to generate an HTML report
            
        Returns:
            Dictionary containing analysis results and paths to generated diagrams
        """
        # Call the parent method to analyze the merge request
        results = await super().analyze_merge_request(
            project_id=project_id,
            mr_iid=mr_iid,
            include_indirect_impact=include_indirect_impact
        )
        
        # Generate HTML report if requested
        if generate_html:
            html_path = os.path.join(self.output_dir, f"pr_impact_report_{mr_iid}.html")
            
            # Import the visualization helper if it exists
            try:
                from pr_broadcast.utils.visualization_helper import generate_html_report
                html_report_path = generate_html_report(results["diagrams"], html_path)
                results["html_report"] = html_report_path
            except ImportError:
                # Create a simple HTML report
                html_report_path = self._generate_simple_html_report(results["diagrams"], html_path)
                results["html_report"] = html_report_path
        
        return results
    
    def _generate_simple_html_report(self, diagrams: Dict[str, Any], output_path: str) -> str:
        """
        Generate a simple HTML report from the generated diagrams.
        
        Args:
            diagrams: Dictionary mapping diagram types to file paths
            output_path: Path to save the HTML report
            
        Returns:
            Path to the generated HTML report
        """
        html_content = f"""<!DOCTYPE html>
<html>
<head>
    <title>PR Impact Analysis Report</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {{
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }}
        h1, h2, h3 {{
            color: #2c3e50;
        }}
        .diagram-list {{
            margin: 20px 0;
        }}
        .diagram-item {{
            margin-bottom: 10px;
        }}
    </style>
</head>
<body>
    <h1>PR Impact Analysis Report</h1>
    
    <div class="diagram-list">
        <h2>Generated Diagrams</h2>
        
        <div class="diagram-item">
            <h3>Summary Diagram</h3>
            <a href="{os.path.basename(diagrams.get('summary', ''))}" target="_blank">View Summary Diagram</a>
        </div>
        
        <div class="diagram-item">
            <h3>Hierarchy Impact Diagram</h3>
            <a href="{os.path.basename(diagrams.get('hierarchy', ''))}" target="_blank">View Hierarchy Impact Diagram</a>
        </div>
        
        <div class="diagram-item">
            <h3>Domain Impact Diagrams</h3>
            <ul>
"""
        
        # Add links to domain diagrams
        for domain, path in diagrams.get("domains", {}).items():
            html_content += f'                <li><a href="{os.path.basename(path)}" target="_blank">{domain}</a></li>\n'
        
        html_content += """            </ul>
        </div>
        
        <div class="diagram-item">
            <h3>Function Change Diagrams</h3>
            <ul>
"""
        
        # Add links to function diagrams
        for func, path in diagrams.get("functions", {}).items():
            html_content += f'                <li><a href="{os.path.basename(path)}" target="_blank">{func}</a></li>\n'
        
        html_content += """            </ul>
        </div>
    </div>
</body>
</html>
"""
        
        # Save the HTML report
        with open(output_path, 'w') as f:
            f.write(html_content)
        
        logger.info(f"Generated simple HTML report at {output_path}")
        return output_path

def print_summary(results: Dict[str, Any]) -> None:
    """
    Print a summary of the analysis results.
    
    Args:
        results: Analysis results
    """
    summary = results["summary"]
    
    print("\n" + "=" * 80)
    print(f"PR BROADCAST SUMMARY FOR MR !{results['mr_details']['iid']}")
    print("=" * 80)
    
    print(f"\nTitle: {summary['title']}")
    print(f"Author: {summary['author']}")
    
    print("\nCHANGE SUMMARY:")
    print(f"- Total Changed Functions: {summary['total_changes']}")
    print(f"  - Added: {summary['change_counts']['added']}")
    print(f"  - Modified: {summary['change_counts']['modified']}")
    print(f"  - Deleted: {summary['change_counts']['deleted']}")
    
    print("\nIMPACT SUMMARY:")
    print(f"- Total Impacted Domains: {summary['total_impacted_domains']}")
    print(f"  - High Impact: {summary['impact_counts']['high']}")
    print(f"  - Medium Impact: {summary['impact_counts']['medium']}")
    print(f"  - Low Impact: {summary['impact_counts']['low']}")
    
    print("\nMOST IMPACTED DOMAINS:")
    for domain in summary["most_impacted_domains"]:
        name = domain["name"]
        impact = domain["impact"]
        print(f"- {name}")
        print(f"  - Impact Score: {impact['impact_score']:.2f}")
        print(f"  - Severity: {impact['severity']}")
        print(f"  - Direct Impact: {'Yes' if impact['direct_impact'] else 'No'}")
        print(f"  - Changed Functions: {len(impact['changes'])}")
    
    print("\nGENERATED DIAGRAMS:")
    print(f"- Summary: {results['diagrams']['summary']}")
    print(f"- Hierarchy: {results['diagrams']['hierarchy']}")
    print(f"- Domain Diagrams: {len(results['diagrams']['domains'])} diagrams")
    print(f"- Function Diagrams: {len(results['diagrams']['functions'])} diagrams")
    
    if "html_report" in results:
        print(f"\nHTML REPORT: {results['html_report']}")
    
    print("\n" + "=" * 80)
    print(f"View the summary diagram at: {results['diagrams']['summary']}")
    print("=" * 80 + "\n")

async def main():
    """Main entry point for the PR Broadcast test script."""
    parser = argparse.ArgumentParser(description="Test PR Broadcast with dummy PR data")
    
    # Required arguments
    parser.add_argument("--taxonomy-json", required=True, help="Path to domain taxonomy JSON file")
    parser.add_argument("--domain-traces-yaml", required=True, help="Path to domain traces YAML file")
    
    # Optional arguments
    parser.add_argument("--output-dir", default="pr_broadcast_test_output", help="Directory to save generated diagrams and reports")
    parser.add_argument("--dummy-data", help="Path to save/load dummy PR data JSON file")
    parser.add_argument("--include-indirect-impact", action="store_true", help="Include indirect impact analysis")
    parser.add_argument("--generate-html", action="store_true", help="Generate HTML report")
    parser.add_argument("--open-browser", action="store_true", help="Open HTML report in browser")
    
    args = parser.parse_args()
    
    try:
        # Initialize the mock PR Broadcast service
        service = MockPRBroadcastService(
            gitlab_url="https://gitlab.example.com",
            gitlab_token="dummy_token",
            taxonomy_json_path=args.taxonomy_json,
            domain_traces_yaml_path=args.domain_traces_yaml,
            output_dir=args.output_dir,
            dummy_data_path=args.dummy_data
        )
        
        # Analyze the mock merge request
        results = await service.analyze_merge_request(
            project_id="12345",
            mr_iid=678,
            include_indirect_impact=args.include_indirect_impact,
            generate_html=args.generate_html
        )
        
        # Print summary
        print_summary(results)
        
        # Open HTML report in browser if requested
        if args.open_browser and "html_report" in results:
            import webbrowser
            webbrowser.open(f"file://{os.path.abspath(results['html_report'])}")
        
        return 0
    
    except Exception as e:
        logger.error(f"Error testing PR Broadcast: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
