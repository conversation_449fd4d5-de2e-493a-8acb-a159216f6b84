# Bracket SBOM Generation Process

## Overview

This document explains how the Software Bill of Materials (SBOM) for the Bracket project was generated, validated, and signed. The SBOM follows the CycloneDX specification and includes comprehensive information about all dependencies across Python, JavaScript/TypeScript, and Docker components.

## Generation Process

### Tools Used

- **CycloneDX Python Module**: For generating Python dependency SBOMs
- **CycloneDX npm Module**: For generating JavaScript/TypeScript dependency SBOMs
- **Syft**: For analyzing Docker container images
- **Cosign**: For signing the final SBOM (part of Sigstore)

### Step 1: Environment Setup

The process begins by installing all necessary tools:

```bash
./install_sbom_tools.sh
```

This script installs CycloneDX tools for both Python and JavaScript ecosystems, as well as Syft for container analysis.

### Step 2: SBOM Generation

The main script (`generate_sbom.py`) performs the following operations:

1. **Python Dependencies**: Analyzes Python dependencies using Poetry or pip, generating a comprehensive SBOM with all direct and transitive dependencies.

2. **JavaScript/TypeScript Dependencies**: Analyzes the `bracket_ext` directory using the CycloneDX npm tool, capturing all npm dependencies including their versions, licenses, and relationships.

3. **Docker Images**: Analyzes Dockerfiles in the microservices directory using Syft, capturing base images and installed packages.

4. **Combination**: Merges all individual SBOMs into a single comprehensive SBOM file that represents the entire project.

```bash
./generate_sbom.py --output sbom_output
```

### Step 3: SBOM Enhancement

After generation, additional scripts enhance the SBOM with more detailed information:

1. **License Analysis**: The `update_licenses.py` script enriches the SBOM with detailed license information.

2. **Vulnerability Scanning**: The `scan_vulnerabilities.py` script checks for known vulnerabilities in the dependencies.

3. **Analysis Report**: The `analyze_sbom.py` script generates a detailed report of the SBOM contents.

```bash
./update_licenses.py --sbom sbom_output/combined-sbom.json --output sbom_output/enhanced-sbom.json
./scan_vulnerabilities.py --sbom sbom_output/enhanced-sbom.json --output sbom_output/enhanced-vulnerability-report.md
./analyze_sbom.py --sbom sbom_output/enhanced-sbom.json --output sbom_output/enhanced-analysis-report.md
```

### Step 4: SBOM Validation and Signing

The final SBOM is validated against the CycloneDX schema and signed using Sigstore's cosign tool:

```bash
# Validate the SBOM
./validate_sbom.py sbom_output/enhanced-sbom.json

# Sign the SBOM
cosign sign-blob --key cosign.key sbom_output/enhanced-sbom.json
```

## SBOM Contents

The generated SBOM includes:

- **Metadata**: Project information, generation timestamp, and tool details
- **Components**: All dependencies with versions, licenses, and package URLs (purls)
- **Dependencies**: Relationship graph showing which components depend on others
- **Licenses**: Comprehensive license information for all components
- **Vulnerabilities**: Known security issues in the dependencies (if any)

## Usage

The SBOM can be used for:

1. **M&A Due Diligence**: Provides a complete inventory of all software components
2. **Security Analysis**: Identifies potential vulnerabilities in dependencies
3. **License Compliance**: Ensures all components have compatible licenses
4. **Supply Chain Security**: Enables verification of software components

## Troubleshooting

Common issues encountered during SBOM generation:

1. **JavaScript SBOM Generation**: Issues with the jszip dependency were resolved by providing a proper implementation.
2. **Docker Analysis**: Some Dockerfiles may require additional configuration for proper analysis.
3. **Signing Process**: Cosign requires proper key management for non-interactive signing.

For any issues, refer to the detailed logs in the output directory or run the generation script with more verbose output.
